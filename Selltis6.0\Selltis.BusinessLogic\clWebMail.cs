﻿using System;

//OWNER RH


using MailBee.AddressCheck;
using MailBee.ImapMail;
using MailBee.Mime;
using System.IO;
using MailBee;
using System.Web;
using System.Data;
using System.Collections;





namespace Selltis.BusinessLogic
{
	public class clWebMail
	{

		private bool instanceDataInitialized = false;

		public clWebMail()
		{
			if (!instanceDataInitialized)
			{
				InitializeInstanceData();
				instanceDataInitialized = true;
			}
		}

			private void InitializeInstanceData()
			{
				oIMAP = new Imap(sMailBeeLicense);
				oSMTP = new MailBee.SmtpMail.Smtp(sMailBeeLicense);
			}

		private string sMailBeeLicense = "MN110-D21AE5AB1BB11AAA1A6F90390211-94CA"; //'"MN800-****************************-FF98"

		private Imap oIMAP;
		private MailBee.SmtpMail.Smtp oSMTP;

		public bool bAccountOrLoginError = false;
		public string sAccountOrLoginErrorMessage = "";
		public string sSentFolder = "";
		public string sDeletedFolder = "";

		private clData goData;
		private clError goErr;



		public enum convTo
		{
			KB = 1,
			MB = 2,
			GB = 3,
			TB = 4
		}

		public string GetSetting(string sSetting)
		{

			//AUTHOR: RH
			//
			//PURPOSE:
			//		Returns the value of POP settings used by class
			//PARAMETERS:
			//		sSetting: name of the setting to return from POP
			//RETURNS:
			//		setting value


			string sReturn = "";
			sSetting = sSetting.ToLower();

			clMetaData oMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			clProject goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];

			switch (sSetting)
			{


				case "webmail_from_name":
					sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "WEBMAIL_FROM_NAME", "");
					break;
				case "webmail_from_address":
					sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "WEBMAIL_FROM_ADDRESS", "");
					break;
				case "smtp_server":
					sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SMTP_SERVER", "");
					break;
				case "smtp_server_port":
					sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SMTP_SERVER_PORT", "587");
					break;
				case "smtp_user":
					sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SMTP_USER", "");
					break;
				case "smtp_password":
					sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SMTP_PASSWORD", "");
					clEncrypt2 oCrypt = new clEncrypt2();
					sReturn = oCrypt.Decrypt(sReturn);
					break;
				case "imap_server":
					sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "IMAP_SERVER", "");
					break;
				case "imap_server_port":
					sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "IMAP_SERVER_PORT", "143");
					break;
				case "imap_user":
					sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "IMAP_USER", "");
					break;
				case "imap_password":
					sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "IMAP_PASSWORD", "");
					clEncrypt2 oCrypt2 = new clEncrypt2();
					sReturn = oCrypt2.Decrypt(sReturn);
					break;
				case "smtp_server_requires_authentication":
					sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SMTP_Server_Requires_Authentication", "1");
					break;
			}

			return sReturn;

		}

		public string ValidateAddresses(string sAddresses)
		{


			//AUTHOR: RH
			//
			//PURPOSE:
			//		Tests validity of email addresses
			//		
			//PARAMETERS:
			//		sAddresses: comma or semicolon delimited string of email addresses to validate
			//RETURNS:
			//		Returns message string and invalid addresses


			EmailAddressValidator valid = new EmailAddressValidator(sMailBeeLicense);

			// To perform DNS MX lookup queries, we need some DNS servers for that.
			valid.DnsServers.Autodetect();

			sAddresses = sAddresses.Replace(";", ",");

			string[] aAddresses = sAddresses.Split(',');

			AddressValidationLevel result = 0;
			string sDefaultMessage = "One or more addresses are invalid:" + "\r\n";

			string sReturnMessage = "";

			valid.ValidationLevel = AddressValidationLevel.RegexCheck;

			foreach (var addr in aAddresses)
			{

				result = valid.Verify(addr.Trim(' '));

				switch (result)
				{

					case AddressValidationLevel.OK:
					break;

						//do nothing

					case AddressValidationLevel.RegexCheck:

						sReturnMessage = sReturnMessage + "\r\n" + addr;
						break;

				}

			}

			if (sReturnMessage != "")
			{
				return sDefaultMessage + sReturnMessage;
			}
			else
			{
				return "";

			}


		}

		public double BytesTO(double lBytes, convTo convertto)
		{
			return lBytes / (Math.Pow(1024,Convert.ToDouble(convertto)));
		}

		public DataTable GetMail(string sFolder, int iFirstItem, int iNumberOfItems, string sSearch = "")
		{


			Imap imp = new Imap();
			DataTable dt = new DataTable();
			bAccountOrLoginError = false;
			sAccountOrLoginErrorMessage = "";

			try
			{
				if (imp.Connect(GetSetting("imap_server"), Convert.ToInt32(GetSetting("imap_server_port"))) == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" + GetSetting("imap_server") + "). No further details are available.";
					imp.Disconnect();
					return dt;
				}
				if (imp.Login(GetSetting("imap_user"), GetSetting("imap_password")) == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" + GetSetting("imap_server") + "). No further details are available.";
					imp.Disconnect();
					return dt;
				}

			}
			catch (Exception ex)
			{
				bAccountOrLoginError = true;
				sAccountOrLoginErrorMessage = ex.Message;
				return dt;
			}

			//Try

			EnvelopeCollection msgs = new EnvelopeCollection(); //MailMessageCollection
				UidCollection uids = null;

				dt.Columns.Add("HasAttachment", System.Type.GetType("System.Boolean"));
				dt.Columns.Add("AttachmentList");
				dt.Columns.Add("From");
				dt.Columns.Add("To");
				dt.Columns.Add("CC");
				dt.Columns.Add("BCC");
				dt.Columns.Add("Subject");
				dt.Columns.Add("DateReceived", System.Type.GetType("System.DateTime"));
				dt.Columns.Add("IsRead", System.Type.GetType("System.Boolean"));
				dt.Columns.Add("Size");
				dt.Columns.Add("UID");
				dt.Columns.Add("MID");

				DataRow dr = null;

				try
				{
					if (imp.SelectFolder(sFolder) != true)
					{
						imp.Disconnect();
						return dt;
					}
				}
				catch (Exception ex)
				{
					return dt;
				}

				int intFrom = (imp.MessageCount + 1) - iFirstItem;
				int intTo = (intFrom - iNumberOfItems) + 1;
				if (intTo < 1)
				{
					intTo = 1;
				}

				if (imp.MessageCount == 0)
				{
					return dt;
				}
				else
				{
					if (sSearch == "")
					{
						msgs = imp.DownloadEnvelopes(intTo + ":" + intFrom, false, EnvelopeParts.MailBeeEnvelope | EnvelopeParts.BodyStructure, 80, null, null);
					}
					else
					{

						try
						{
							uids = (UidCollection)imp.Search(true, sSearch, null);
							msgs = imp.DownloadEnvelopes(uids.ToString(), true, EnvelopeParts.MailBeeEnvelope | EnvelopeParts.BodyStructure, 80, null, null);
						}
						catch (Exception ex)
						{
							return dt;
						}
					}


				}
				msgs.Reverse();

// INSTANT C# NOTE: Commented this declaration since looping variables in 'foreach' loops are declared in the 'foreach' header in C#:
//				Dim env As MailBee.ImapMail.Envelope
				bool bSeen = false;

				foreach (MailBee.ImapMail.Envelope env in msgs)
				{

					dr = dt.NewRow();

					Microsoft.VisualBasic.Collection cCol = ConvertAddressStringToCollection(env.To.ToString());
					string sString = ConvertAddressCollectionToString(cCol);
					MailBee.ImapMail.Envelope temp = env;
					dr["HasAttachment"] = TestForAttachments(ref temp, true);
					dr["AttachmentList"] = TestForAttachments(ref temp, false);
					dr["From"] = HttpUtility.HtmlEncode(temp.From.ToString());
					dr["To"] = HttpUtility.HtmlEncode(temp.To.ToString());
					dr["CC"] = HttpUtility.HtmlEncode(temp.Cc.ToString());
					dr["BCC"] = HttpUtility.HtmlEncode(temp.Bcc.ToString());
					dr["Subject"] = temp.Subject.ToString();
					dr["DateReceived"] = temp.DateReceived;

					if (temp.Flags.ToString().ToLower().IndexOf("\\seen") + 1 > 0)
					{
						bSeen = true;
					}
					else
					{
						bSeen = false;
					}
					dr["IsRead"] = bSeen;
					dr["Size"] = Math.Round(BytesTO(temp.Size, convTo.KB), 1);
					dr["UID"] = temp.Uid.ToString();
					dr["MID"] = getSHA1Hash(temp.MessageID);

					dt.Rows.Add(dr);

				}

				//Catch ex As Exception
				//    'xxx raise error
				//End Try

				imp.Disconnect();

			return dt;


		}
		public int GetCount(string sFolder)
		{

			Imap imp = new Imap();
			int iCount = 0;
			bAccountOrLoginError = false;
			sAccountOrLoginErrorMessage = "";

			try
			{
				if (imp.Connect(GetSetting("imap_server"), Convert.ToInt32(GetSetting("imap_server_port"))) == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" + GetSetting("imap_server") + "). No further details are available.";
					imp.Disconnect();
					return iCount;
				}
				if (imp.Login(GetSetting("imap_user"), GetSetting("imap_password")) == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" + GetSetting("imap_server") + "). No further details are available.";
					imp.Disconnect();
					return iCount;
				}

			}
			catch (Exception ex)
			{
				bAccountOrLoginError = true;
				sAccountOrLoginErrorMessage = ex.Message;
				return iCount;
			}

			try
			{
				if (imp.SelectFolder(sFolder) != true)
				{
					imp.Disconnect();
					return iCount;
				}
			}
			catch (Exception ex)
			{
				return iCount;
			}
			iCount = imp.MessageCount;
			imp.Disconnect();

			return iCount;

		}

		public int GetUnReadCount()
		{




			Imap imp = new Imap();
			UidCollection uids = null;
			bAccountOrLoginError = false;
			sAccountOrLoginErrorMessage = "";

			try
			{
				if (imp.Connect(GetSetting("imap_server"), Convert.ToInt32(GetSetting("imap_server_port"))) == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" + GetSetting("imap_server") + "). No further details are available.";
					imp.Disconnect();
					return 0;
				}
				if (imp.Login(GetSetting("imap_user"), GetSetting("imap_password")) == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" + GetSetting("imap_server") + "). No further details are available.";
					imp.Disconnect();
					return 0;
				}

			}
			catch (Exception ex)
			{
				bAccountOrLoginError = true;
				sAccountOrLoginErrorMessage = ex.Message;
				return 0;
			}

			try
			{
				if (imp.SelectFolder("INBOX") != true)
				{
					imp.Disconnect();
					return 0;
				}
			}
			catch (Exception ex)
			{
				return 0;
			}



			uids = (UidCollection)imp.Search(true, "UNSEEN", null);

			return uids.Count;



		}


		public bool SetSeenFlag(string sFolder, string sUIDs)
		{

			//sUIDs=comma delimited string of UIDs to mark as seen


			Imap imp = new Imap();
			imp.Connect(GetSetting("imap_server"), Convert.ToInt32(GetSetting("imap_server_port")));
			imp.Login(GetSetting("imap_user"), GetSetting("imap_password"));
			try
			{
				if (imp.SelectFolder(sFolder) != true)
				{
					return false;
				}
			}
			catch (Exception ex)
			{
				return false;
			}
			bool bResult = imp.SetMessageFlags(sUIDs, true, SystemMessageFlags.Seen, MessageFlagAction.Add);
			imp.Disconnect();

			return bResult;






		}

		public bool SendMail(SellMailItem oMailItem)
		{



			try
			{

				string sTestString = "";

				if (ConvertAddressCollectionToString(oMailItem.c_To) != "")
				{
					sTestString = ConvertAddressCollectionToString(oMailItem.c_To);
				}
				if (ConvertAddressCollectionToString(oMailItem.c_CC) != "")
				{
					sTestString = sTestString + "," + ConvertAddressCollectionToString(oMailItem.c_CC);
				}
				if (ConvertAddressCollectionToString(oMailItem.c_BCC) != "")
				{
					sTestString = sTestString + "," + ConvertAddressCollectionToString(oMailItem.c_BCC);
				}

				string sValidation = ValidateAddresses(sTestString);

				if (sValidation != "")
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = sValidation;
					return false;
				}

				if (GetSetting("smtp_server") == "")
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "You do not have an SMTP server setting.  Please configure Webmail Settings in your Personal Options.";
					return false;
				}

				if (GetSetting("smtp_server_requires_authentication") == "1")
				{

					if (GetSetting("smtp_user") == "")
					{
						bAccountOrLoginError = true;
						sAccountOrLoginErrorMessage = "You do not have an SMTP user setting.  Please configure Webmail Settings in your Personal Options.";
						return false;
					}

					if (GetSetting("smtp_password") == "")
					{
						bAccountOrLoginError = true;
						sAccountOrLoginErrorMessage = "You do not have an SMTP password setting.  Please configure Webmail Settings in your Personal Options.";
						return false;
					}
				}

				MailBee.Mime.MailMessage oMessage = new MailBee.Mime.MailMessage();
				oMessage.BodyHtmlText = oMailItem.s_HTMLBody;

				oMessage.Builder.RelatedFilesFolder = System.Web.HttpContext.Current.Server.MapPath("");
				oMessage.ImportRelatedFiles(ImportRelatedFilesOptions.None);

				MailBee.SmtpMail.Smtp mailer = new MailBee.SmtpMail.Smtp();

				mailer.SmtpServers.Add(GetSetting("smtp_server"), GetSetting("smtp_user"), GetSetting("smtp_password"));
				mailer.SmtpServers[0].Port = Convert.ToInt32(GetSetting("smtp_server_port"));
				if (mailer.Connect() == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to connect to your SMTP server (" + GetSetting("smtp_server") + "). No further details are available.";
					return false;
				}
				mailer.Hello();
				if (GetSetting("smtp_server_requires_authentication") == "1")
				{
					mailer.Login();
				}
				MailBee.Mime.EmailAddress oFrom = new MailBee.Mime.EmailAddress();
				oFrom.Email = GetSetting("webmail_from_address");
				oFrom.DisplayName = GetSetting("webmail_from_name");
				oMessage.From = oFrom;
				oMessage.To.AsString = ConvertAddressCollectionToString(oMailItem.c_To);
				oMessage.Cc.AsString = ConvertAddressCollectionToString(oMailItem.c_CC);
				oMessage.Bcc.AsString = ConvertAddressCollectionToString(oMailItem.c_BCC);
				oMessage.Subject = oMailItem.s_Subject;
				mailer.Message = oMessage;
				foreach (string Attch in oMailItem.c_Attachments)
				{
					mailer.AddAttachment(Attch);
				}
				mailer.Send();


				//****************************************************
				//PUT COPY IN SENT FOLDER

				Imap imp = new Imap();

				bAccountOrLoginError = false;
				sAccountOrLoginErrorMessage = "";

				try
				{
					if (imp.Connect(GetSetting("imap_server"), Convert.ToInt32(GetSetting("imap_server_port"))) == false)
					{
						bAccountOrLoginError = true;
						sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" + GetSetting("imap_server") + "). No further details are available.";
						return false;
					}
					if (imp.Login(GetSetting("imap_user"), GetSetting("imap_password")) == false)
					{
						bAccountOrLoginError = true;
						sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" + GetSetting("imap_server") + "). No further details are available.";
						return false;
					}

				}
				catch (Exception ex)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = ex.Message;
					return false;
				}

				GetFolders();
				imp.UploadMessage(mailer.Message, sSentFolder, SystemMessageFlags.Seen | SystemMessageFlags.Answered);

				imp.Disconnect();

				//****************************************************

				mailer.Disconnect();

				return true;


			}
			catch (Exception ex)
			{
				bAccountOrLoginError = true;
				sAccountOrLoginErrorMessage = ex.Message + "\r\n" + "\r\n" + "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator.";
				return false;
			}

		}

		public bool DeleteMessages(string sFolder, string sUIDs)
		{

			//sUIDs=comma delimited string of UIDs to mark as seen
			Imap imp = new Imap();

			if (sDeletedFolder == "")
			{
				GetFolders();
			}

			bAccountOrLoginError = false;
			sAccountOrLoginErrorMessage = "";

			try
			{
				if (imp.Connect(GetSetting("imap_server"), Convert.ToInt32(GetSetting("imap_server_port"))) == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" + GetSetting("imap_server") + "). No further details are available." + "\r\n" + "\r\n" + "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator.";
					imp.Disconnect();
					return false;
				}
				if (imp.Login(GetSetting("imap_user"), GetSetting("imap_password")) == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" + GetSetting("imap_server") + "). No further details are available." + "\r\n" + "\r\n" + "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator.";
					imp.Disconnect();
					return false;
				}

			}
			catch (Exception ex)
			{
				bAccountOrLoginError = true;
				sAccountOrLoginErrorMessage = ex.Message;
				imp.Disconnect();
				return false;
			}

			try
			{
				if (imp.SelectFolder(sFolder) != true)
				{
					return false;
				}
			}
			catch (Exception ex)
			{
				return false;
			}

			bool bResult = false;

			if (sDeletedFolder == "" || sFolder == sDeletedFolder)
			{
				bResult = imp.DeleteMessages(sUIDs, true);
				imp.Expunge();
			}
			else
			{
				bResult = imp.MoveMessages(sUIDs, true, sDeletedFolder);
			}

			imp.Disconnect();

			return bResult;


		}

		public bool MoveMessages(string sSourceFolder, string sTargetFolder, string sUIDs)
		{

			Imap imp = new Imap();

			bAccountOrLoginError = false;
			sAccountOrLoginErrorMessage = "";

			try
			{
				if (imp.Connect(GetSetting("imap_server"), Convert.ToInt32(GetSetting("imap_server_port"))) == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" + GetSetting("imap_server") + "). No further details are available." + "\r\n" + "\r\n" + "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator.";
					imp.Disconnect();
					return false;
				}
				if (imp.Login(GetSetting("imap_user"), GetSetting("imap_password")) == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" + GetSetting("imap_server") + "). No further details are available." + "\r\n" + "\r\n" + "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator.";
					imp.Disconnect();
					return false;
				}

			}
			catch (Exception ex)
			{
				bAccountOrLoginError = true;
				sAccountOrLoginErrorMessage = ex.Message;
				imp.Disconnect();
				return false;
			}

			try
			{
				if (imp.SelectFolder(sSourceFolder) != true)
				{
					imp.Disconnect();
					return false;
				}
			}
			catch (Exception ex)
			{
				return false;
			}

			bool bResult;

			bResult = imp.MoveMessages(sUIDs, true, sTargetFolder);
			imp.Disconnect();
			return bResult;


		}

		public DataTable GetFolders()
		{

			Imap imp = new Imap();
			FolderCollection fc = null;
			DataTable dt = new DataTable();

			bAccountOrLoginError = false;
			sAccountOrLoginErrorMessage = "";

			imp.UseXList = true;

			if (GetSetting("imap_server") == "")
			{
				bAccountOrLoginError = true;
				sAccountOrLoginErrorMessage = "You do not have an IMAP server setting.  Please configure Webmail Settings in your Personal Options.";
				return dt;
			}

			if (GetSetting("imap_user") == "")
			{
				bAccountOrLoginError = true;
				sAccountOrLoginErrorMessage = "You do not have an IMAP user setting.  Please configure Webmail Settings in your Personal Options.";
				return dt;
			}

			if (GetSetting("imap_password") == "")
			{
				bAccountOrLoginError = true;
				sAccountOrLoginErrorMessage = "You do not have an IMAP user password setting.  Please configure Webmail Settings in your Personal Options.";
				return dt;
			}

			try
			{
				if (imp.Connect(GetSetting("imap_server"), Convert.ToInt32(GetSetting("imap_server_port"))) == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" + GetSetting("imap_server") + "). No further details are available." + "\r\n" + "\r\n" + "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator.";
					return dt;
				}
				if (imp.Login(GetSetting("imap_user"), GetSetting("imap_password")) == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" + GetSetting("imap_server") + "). No further details are available." + "\r\n" + "\r\n" + "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator.";
					return dt;
				}

			}
			catch (Exception ex)
			{
				bAccountOrLoginError = true;
				sAccountOrLoginErrorMessage = ex.Message + "\r\n" + "\r\n" + "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator.";
				return dt;
			}



			try
			{
				fc = imp.DownloadFolders(false, null, null);
			}
			catch (Exception ex)
			{
				try
				{
					imp.UseXList = false;
					fc = imp.DownloadFolders(false, null, null);
				}
				catch (Exception ex2)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = ex2.Message + "\r\n" + "\r\n" + "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator.";
					return dt;

				}


			}

			if (fc.Count == 0)
			{


				imp.UseXList = false;

				//Try
				fc = imp.DownloadFolders(false, null, null);
				//Catch ex As Exception

				//End Try


			}



			dt.Columns.Add("name");
			dt.Columns.Add("short");
			dt.Columns.Add("level");
			dt.Columns.Add("delim");
			dt.DefaultView.Sort = "level";

			foreach (MailBee.ImapMail.Folder fold in fc)
			{

				DataRow r = dt.NewRow();
				r["name"] = fold.Name;
				r["short"] = fold.ShortName;
				r["level"] = fold.NestingLevel;
				r["delim"] = fold.Delimiter;
				dt.Rows.Add(r);

				if (fold.Flags == FolderFlags.Sent)
				{
					sSentFolder = fold.Name;
				}

				if (fold.Flags == FolderFlags.Trash)
				{
					sDeletedFolder = fold.Name;
				}


			}

			DataView dataView = new DataView(dt);
			dataView.Sort = "level asc";
			DataTable dataTable = dataView.ToTable();

			imp.Disconnect();

			if (sSentFolder == "")
			{
				foreach (System.Data.DataRow row in dataTable.Rows)
				{
					if (Convert.ToString(row["name"]).ToLower().IndexOf("sent") + 1 > 0)
					{
						sSentFolder = Convert.ToString(row["name"]);
						break;
					}
				}

			}

			if (sDeletedFolder == "")
			{
				foreach (System.Data.DataRow row in dataTable.Rows)
				{
					if (Convert.ToString(row["name"]).ToLower().IndexOf("delete") + 1 > 0)
					{
						sDeletedFolder = Convert.ToString(row["name"]);
						break;
					}
				}
			}

			if (sDeletedFolder == "")
			{
				foreach (System.Data.DataRow row in dataTable.Rows)
				{
					if (Convert.ToString(row["name"]).ToLower().IndexOf("trash") + 1 > 0)
					{
						sDeletedFolder = Convert.ToString(row["name"]);
						break;
					}
				}
			}

			return dataTable;

		}

		public string GetSearchString(string sTo, string sFrom, string sSubject, string sBody)
		{

			ArrayList sParams = new ArrayList();
			string sReturn = "";


			if (sTo != "")
			{
				sParams.Add("TO " + ImapUtils.ToQuotedString(sTo));
			}
			if (sFrom != "")
			{
				sParams.Add("FROM " + ImapUtils.ToQuotedString(sFrom));
			}
			if (sSubject != "")
			{
				sParams.Add("SUBJECT " + ImapUtils.ToQuotedString(sSubject));
			}
			if (sBody != "")
			{
				sParams.Add("BODY " + ImapUtils.ToQuotedString(sBody));
			}

			sReturn = ImapUtils.AllOf("TO " + ImapUtils.ToQuotedString(sTo), "FROM " + ImapUtils.ToQuotedString(sFrom), "SUBJECT " + ImapUtils.ToQuotedString(sSubject), "BODY " + ImapUtils.ToQuotedString(sBody));
			sReturn = sReturn.Replace("TO " + (char)34 + (char)34, "");
			sReturn = sReturn.Replace("FROM " + (char)34 + (char)34, "");
			sReturn = sReturn.Replace("SUBJECT " + (char)34 + (char)34, "");
			sReturn = sReturn.Replace("BODY " + (char)34 + (char)34, "");
			sReturn = sReturn.Replace("(", "");
			sReturn = sReturn.Replace(")", "");
			sReturn = sReturn.Trim(' ');
			sReturn = "(" + sReturn + ")";

			return sReturn;



		}

		public SellMailItem DownloadMsgViaImap(string sUID, string sFolder, string sMID = "")
		{


			string uid = sUID;
			string partID = "2";
			string ofolder = sFolder;
			string url = null;

			MailBee.ImapMail.Imap imp = new MailBee.ImapMail.Imap();
			MailBee.Mime.MailMessage msg = new MailBee.Mime.MailMessage();
			bool bAlreadyDownloaded = false;

			if (sMID != "")
			{

				string sEMLFile = System.Web.HttpContext.Current.Server.MapPath("../Temp/Webmail/") + sMID + "\\msg.eml";

				if (System.IO.File.Exists(sEMLFile))
				{
					byte[] msgBytes = null;

					BinaryReader br = null;
					try
					{
						br = new BinaryReader(File.OpenRead(sEMLFile));
						FileInfo fi = new FileInfo(sEMLFile);
						msgBytes = br.ReadBytes((int)fi.Length);
					}
					finally
					{
						if (br != null)
						{
							br.Close();
						}
					}


					msg.LoadMessage(msgBytes);
					bAlreadyDownloaded = true;
				}

			}

			//xxx ERROR HANDLE SECTION
			if (bAlreadyDownloaded == false)
			{
				if (GetSetting("imap_server_port") == "0")
				{
					imp.Connect(GetSetting("imap_server"));
				}
				else
				{
					imp.Connect(GetSetting("imap_server"), Convert.ToInt32(GetSetting("imap_server_port")));
				}
				imp.Login(GetSetting("imap_user"), GetSetting("imap_password"));
				imp.SelectFolder(ofolder);
				msg = imp.DownloadEntireMessage(Convert.ToInt64(uid), true);
				imp.Disconnect();

			}


			string sPrivateDirectory = "";
			string htmlBody = "";

			if (msg != null)
			{

				msg.Parser.PlainToHtmlMode = PlainToHtmlAutoConvert.IfNoHtml;
				msg.Parser.WorkingFolder = System.Web.HttpContext.Current.Server.MapPath("../Temp/Webmail");

				sPrivateDirectory = System.Web.HttpContext.Current.Server.MapPath("../Temp/Webmail/") + getSHA1Hash(msg.MessageID).ToUpper();


				if (bAlreadyDownloaded == false)
				{
					htmlBody = msg.GetHtmlAndSaveRelatedFiles("..", VirtualMappingType.Static, MessageFolderBehavior.CreateOnly);

					string s = "<base target=\"_blank\" />";

					string sBody = htmlBody;
					int iBody = sBody.IndexOf("<head");

					if (iBody < 0)
					{
						sBody = s + sBody;
					}
					else
					{
						int iBodyEnd = sBody.IndexOf(">", iBody);
						sBody = sBody.Insert(iBodyEnd + 1, s);
					}

					htmlBody = sBody;



					System.IO.Directory.CreateDirectory(sPrivateDirectory);
					Microsoft.VisualBasic.FileIO.FileSystem.WriteAllText(sPrivateDirectory + "\\message.html", htmlBody, false);
					msg.Attachments.SaveAll(sPrivateDirectory, true);
				}
				else
				{
					htmlBody = System.IO.File.ReadAllText(sPrivateDirectory + "\\message.html");
				}

				url = "../Temp/Webmail/" + getSHA1Hash(msg.MessageID).ToUpper() + "/message.html";

			}
			else
			{
				url = "Inbox is empty or the requested e-mail is no longer on the server (may happen with Gmail/POP3)";
			}

			if (bAlreadyDownloaded == false)
			{
				msg.SaveMessage(System.Web.HttpContext.Current.Server.MapPath("../Temp/Webmail/") + getSHA1Hash(msg.MessageID).ToUpper() + "/msg.eml");
			}

			SellMailItem oSellMailItem = new SellMailItem();

			string sTOs = "";
			string sCCs = "";
			string sBCCs = "";

			foreach (MailBee.Mime.EmailAddress sTO in msg.To)
			{
				sTOs = sTOs + sTO.Email + ",";
			}

			if (sTOs.Length > 0)
			{
				sTOs = sTOs.Substring(0, sTOs.Length - 1);
			}

			foreach (MailBee.Mime.EmailAddress sCC in msg.Cc)
			{
				sCCs = sCCs + sCC.Email + ",";
			}

			if (sCCs.Length > 0)
			{
				sCCs = sCCs.Substring(0, sCCs.Length - 1);
			}

			foreach (MailBee.Mime.EmailAddress sBCC in msg.Bcc)
			{
				sBCCs = sBCCs + sBCC.Email + ",";
			}

			if (sBCCs.Length > 0)
			{
				sBCCs = sBCCs.Substring(0, sBCCs.Length - 1);
			}



			oSellMailItem.c_To = ConvertAddressStringToCollection(sTOs);
			oSellMailItem.c_CC = ConvertAddressStringToCollection(sCCs);
			oSellMailItem.c_BCC = ConvertAddressStringToCollection(sBCCs);
			oSellMailItem.s_From = msg.From.Email;
			oSellMailItem.s_Subject = msg.Subject;
			if (htmlBody != "")
			{
				oSellMailItem.s_HTMLBody = htmlBody;
			}
			else
			{
				oSellMailItem.s_HTMLBody = msg.BodyPlainText;
			}

			oSellMailItem.s_TextBody = msg.BodyPlainText;

			oSellMailItem.c_Attachments = ConvertAddressStringToCollection(GetAttachmentNamesFromMsg(msg.Attachments));
			oSellMailItem.s_UID = Convert.ToString(msg.UidOnServer);
			oSellMailItem.s_MID = getSHA1Hash(msg.MessageID);
			oSellMailItem.s_URL = url;
			if (msg.DateReceived == DateTime.MinValue)
			{
				oSellMailItem.dt_DateReceived = msg.Date;
			}
			else
			{
				oSellMailItem.dt_DateReceived = msg.DateReceived;
			}

			if (msg.DateSent == DateTime.MinValue)
			{
				oSellMailItem.dt_DateSent = msg.Date;
			}
			else
			{
				oSellMailItem.dt_DateSent = msg.DateSent;
			}


			//SetSeenFlag(sFolder, sUID)


			return oSellMailItem;

		}

		private bool LogMessage(ref SellMailItem oMessage, bool bIsSent)
		{


			DataTable table = new DataTable();

			DataColumn column = new DataColumn("ID", typeof(string));
			table.Columns.Add(column);
			column = new DataColumn("Type", typeof(string));
			table.Columns.Add(column);
			column = new DataColumn("From", typeof(string));
			table.Columns.Add(column);
			column = new DataColumn("To", typeof(string));
			table.Columns.Add(column);
			column = new DataColumn("CC", typeof(string));
			table.Columns.Add(column);
			column = new DataColumn("BCC", typeof(string));
			table.Columns.Add(column);
			column = new DataColumn("Subject", typeof(string));
			table.Columns.Add(column);
			column = new DataColumn("Body", typeof(string));
			table.Columns.Add(column);
			column = new DataColumn("Attachments", typeof(string));
			table.Columns.Add(column);
			column = new DataColumn("Date", typeof(string));
			table.Columns.Add(column);
			column = new DataColumn("Time", typeof(string));
			table.Columns.Add(column);
			column = new DataColumn("Importance", typeof(string));
			table.Columns.Add(column);
			DataSet ds = new DataSet();
			ds.Tables.Add(table);

			DataRow row = table.NewRow();
			if (bIsSent == true)
			{
				row["Type"] = "Sent";
			}
			else
			{
				row["Type"] = "Received";
			}

			if (oMessage.s_MID != "")
			{
				row["ID"] = oMessage.s_MID;
			}
			else
			{
				row["ID"] = oMessage.s_UID;
			}

			row["From"] = oMessage.s_From;
			row["To"] = ConvertAddressCollectionToString(oMessage.c_To);
			row["CC"] = ConvertAddressCollectionToString(oMessage.c_CC);
			row["BCC"] = ConvertAddressCollectionToString(oMessage.c_BCC);
			row["Subject"] = oMessage.s_Subject;
			row["Body"] = oMessage.s_TextBody;
			row["Importance"] = "";
			row["Attachments"] = ConvertAddressCollectionToString(oMessage.c_Attachments);
			if (bIsSent)
			{
				row["Date"] = ConvertToSellDate(Convert.ToDateTime(oMessage.dt_DateSent), "Date");
				row["Time"] = ConvertToSellDate(Convert.ToDateTime(oMessage.dt_DateSent), "Time");
			}
			else
			{
				row["Date"] = ConvertToSellDate(Convert.ToDateTime(oMessage.dt_DateReceived), "Date");
				row["Time"] = ConvertToSellDate(Convert.ToDateTime(oMessage.dt_DateReceived), "Time");
			}

			table.TableName = "email";
			table.Rows.Add(row);

			clEmail oMail = new clEmail();
			if (oMail.LogMessage(table) == "1")
			{
				return true;
			}
			else
			{
				return false;
			}


		}

		public string GetUnloggedIDs(string sFolder, string sIDs)
		{
			//sFolder = Folder name
			//sIDs =  Comma delimited list of IDs or "ALL" to log all

			Microsoft.VisualBasic.Collection colIDsToLog = new Microsoft.VisualBasic.Collection();

			GetFolders();

			if (sIDs == "ALL")
			{

				DataTable dt = new DataTable();
				dt = GetMail(sFolder, 1, 10000);

// INSTANT C# NOTE: Commented this declaration since looping variables in 'foreach' loops are declared in the 'foreach' header in C#:
//				Dim row As DataRow
				foreach (DataRow row in dt.Rows)
				{

					if (TestIfLogged(Convert.ToString(row["UID"])) == false)
					{
						colIDsToLog.Add(row["UID"]);
					}

				}

			}

			if (sIDs != "ALL")
			{

				string[] aIDs;
				aIDs = sIDs.Split(',');

				int i = 0;
				for (i = aIDs.GetLowerBound(0); i <= aIDs.GetUpperBound(0); i++)
				{
					if (TestIfLogged(aIDs[i]) == false)
					{
						colIDsToLog.Add(aIDs[i]);
					}
				}
			}

			return ConvertAddressCollectionToString(colIDsToLog);

		}

		public bool LogMessageByID(string sFolder, string mailID, bool bIsSent)
		{

			Selltis.BusinessLogic.SellMailItem tempVar = DownloadMsgViaImap(mailID, sFolder);
			if (LogMessage(ref tempVar, bIsSent) == true)
			{
				AddLoggedID(mailID);
				return true;
			}
			else
			{
				return false;
			}


		}

		public bool LogMessages(string sFolder, string sIDs, string sStatusMDPage)
		{

			//sFolder = Folder name
			//sIDs =  Comma delimited list of IDs or "ALL" to log all
			//sStatusMDPage = page of md for status.. 
			//       two properties: 
			//        status=initiated,evaluating, logging, completed, or error
			//        message= message to be displayed.  
			//       md is initially created by calling process with status set to initiated

			Microsoft.VisualBasic.Collection colIDsToLog = new Microsoft.VisualBasic.Collection();

			GetFolders();

			clProject goP = null;
			clMetaData goMeta = null;

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];

			//xxx SetLoggingStatus evaluating
			goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "STATUS", "evaluating");
			goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "MESSAGE", "Identifying unlogged email.");

			if (sIDs == "ALL")
			{

				DataTable dt = new DataTable();
				dt = GetMail(sFolder, 1, 10000);

// INSTANT C# NOTE: Commented this declaration since looping variables in 'foreach' loops are declared in the 'foreach' header in C#:
//				Dim row As DataRow
				foreach (DataRow row in dt.Rows)
				{

					if (TestIfLogged(Convert.ToString(row["UID"])) == false)
					{
						colIDsToLog.Add(row["UID"]);
					}

				}

			}

			if (sIDs != "ALL")
			{

				string[] aIDs;
				aIDs = sIDs.Split(',');

				int i = 0;
				for (i = aIDs.GetLowerBound(0); i <= aIDs.GetUpperBound(0); i++)
				{
					if (TestIfLogged(aIDs[i]) == false)
					{
						colIDsToLog.Add(aIDs[i]);
					}
				}
			}

			goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "STATUS", "evaluating");
			goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "MESSAGE", colIDsToLog.Count + " emails to log.");


			int x = 1;

			foreach (string mailID in colIDsToLog)
			{

				goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "STATUS", "logging");
				goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "MESSAGE", "Logging " + x + " of " + colIDsToLog.Count + " emails.");

				if (sFolder == sSentFolder)
				{
					Selltis.BusinessLogic.SellMailItem tempVar = DownloadMsgViaImap(mailID, sFolder);
					LogMessage(ref tempVar, true);
				}
				else
				{
					Selltis.BusinessLogic.SellMailItem tempVar2 = DownloadMsgViaImap(mailID, sFolder);
					LogMessage(ref tempVar2, false);
				}

				AddLoggedID(mailID);

			}

			goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "STATUS", "completed");
			if (colIDsToLog.Count == 0)
			{
				goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "MESSAGE", "All mail items were previously logged.");
			}
			else
			{
				goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "MESSAGE", colIDsToLog.Count + " emails logged");
			}



// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		private string ConvertToSellDate(DateTime dtDate, string sDorT)
		{
			string str = null;
			try
			{
				string str2 = "";
				str2 = Convert.ToDateTime(dtDate).ToString("yyyy-MM-dd HH:mm:ss.fff");
				if (sDorT == "Date")
				{
					str2 = this.FromTo(str2, 1L, 10L);
				}
				else
				{
					str2 = this.FromTo(str2, 12L, -1L);
				}
				str = str2;
			}
			catch (System.Exception exception1)
			{
				str = "";
				return str;
			}

			return str;
		}

		private string FromTo(string par_sString, long par_lStart, long par_lEnd = -1L)
		{
			string str = par_sString;
			long num3 = par_lStart;
			long num = par_lEnd;
			if (num3 < 1L)
			{
				num3 = 1L;
			}
			if (num == -1L)
			{
				return str.Substring(((int)num3) - 1);
			}
			if (num < num3)
			{
				return "";
			}
			if (num == num3)
			{
				return str.Substring(((int)num3) - 1, 1);
			}
			long num2 = num - num3;
			return str.Substring(((int)num3) - 1, (int)(num2 + 1L));
		}

		public long GetFileSize(string sFile)
		{

			try
			{
				FileInfo info2 = new FileInfo(sFile);
				long length2 = info2.Length;
				return Convert.ToInt64(Math.Round(BytesTO(length2, convTo.KB), 1));
			}
			catch (Exception ex)
			{
				return 0;
			}




		}

		public bool TestIfLogged(string sID)
		{
			//MI 3/3/07 Added call to GenSQLForAddingNNLinks.

			//PURPOSE:
			//		Checks whether an email ID has already been logged
			//PARAMETERS:
			//		sID: UID of email        
			//RETURNS:
			//		True if successful, false or error if not.
			//AUTHOR: RH


			string sProc = "clWebMail::TestIfLogged";
			clProject goP = null;
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];

			//Try
			System.Data.SqlClient.SqlConnection sqlConnection1;

				sqlConnection1 = goData.GetConnection();

				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();

				cmd.CommandText = "SELECT * FROM EmailLogged WHERE TXT_EmailID='" + sID + "' AND GID_UserID='" + goP.GetUserTID() + "'";

				cmd.CommandType = System.Data.CommandType.Text;
				cmd.Connection = sqlConnection1;
				cmd.CommandTimeout = 300;

				if (Convert.ToInt32(cmd.ExecuteScalar()) != 0)
				{
					sqlConnection1.Close();
					return true;
				}
				else
				{
					sqlConnection1.Close();
					return false;
				}


			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If

			//End Try
		}

		public bool AddLoggedID(string sUID)
		{

			string sProc = "clWebMail::TestIfLogged";
			clProject goP = null;
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];


			System.Data.SqlClient.SqlConnection sqlConnection1;

			sqlConnection1 = goData.GetConnection();

			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();

			//Try
			cmd.CommandText = "pEmailLoggedAdd";
				cmd.CommandType = System.Data.CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uUserID", SqlDbType.UniqueIdentifier);
				uSection.Value = StringToGuid(goP.GetUserTID());
				cmd.Parameters.Add(uSection);

				System.Data.SqlClient.SqlParameter strUID = new System.Data.SqlClient.SqlParameter("@par_sEmailID", SqlDbType.NVarChar);
				strUID.Value = sUID;
				cmd.Parameters.Add(strUID);
				if (cmd.ExecuteNonQuery() == 1)
				{
					return true;
				}

			//Catch ex1 As Exception

			//    If Not ex1.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex1, 45100, sProc)
			//    End If
			//End Try



// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		private System.Guid StringToGuid(string sValue)
		{
			System.Guid guidValue = Guid.TryParse(sValue, out guidValue) ? guidValue : Guid.Empty;
			return guidValue;
		}

		public string GetAttachmentNamesFromMsg(MailBee.Mime.AttachmentCollection ac)
		{

			string sReturn = "";

			foreach (Attachment attach in ac)
			{
				sReturn = sReturn + attach.Name + ", ";
			}

			if (sReturn.Length > 2)
			{
				sReturn = sReturn.Substring(0, sReturn.Length - 2);
			}
			else
			{
				sReturn = "";
			}

			return sReturn;

		}

		public string getSHA1Hash(string strToHash)
		{

			System.Security.Cryptography.SHA1CryptoServiceProvider sha1Obj = new System.Security.Cryptography.SHA1CryptoServiceProvider();
			byte[] bytesToHash = System.Text.Encoding.ASCII.GetBytes(strToHash);

			bytesToHash = sha1Obj.ComputeHash(bytesToHash);

			string strResult = "";

			foreach (byte b in bytesToHash)
			{
				strResult += b.ToString("x2");
			}

			return strResult;

		}

		public string TestForAttachments(ref MailBee.ImapMail.Envelope env, bool AsBoolean)
		{

			if (env.IsValid)
			{

				System.Text.StringBuilder strBuffer = new System.Text.StringBuilder();
				ImapBodyStructureCollection parts = env.BodyStructure.GetAllParts();

// INSTANT C# NOTE: There is no C# equivalent to VB's implicit 'once only' variable initialization within loops, so the following variable declaration has been placed prior to the loop:
				string filename = null;
				foreach (ImapBodyStructure part in parts)
				{
					// Detect if this part is attachment.
					if ((part.Disposition != null && part.Disposition.ToLower() == "attachment") || (part.Filename != null && part.Filename != string.Empty) || (part.ContentType != null && part.ContentType.ToLower() == "message/rfc822"))
					{

	//					Dim filename As String

						if (part.Filename != null)
						{
							filename = part.Filename;
						}
						else
						{
							filename = "untitled";
						}

						strBuffer.Append("[" + filename + "]");
					}
				}

				if (strBuffer.Length > 0)
				{
					if (AsBoolean)
					{
						return "True";
					}
					else
					{
						return strBuffer.ToString();
					}
				}
				else
				{
					if (AsBoolean)
					{
						return "False";
					}
					else
					{
						return "";
					}

				}
			}

			if (AsBoolean == true)
			{
				return "False";
			}
			else
			{
				return "";
			}


		}

		public string ConvertAddressCollectionToString(Microsoft.VisualBasic.Collection cAddresses)
		{

			string sReturn = "";
			if (cAddresses == null)
			{
				return "";
			}

			foreach (Microsoft.VisualBasic.Collection Addr in cAddresses)
			{
				sReturn = sReturn + Addr + ", ";
			}

			if (sReturn.Length > 2)
			{
				return sReturn.Substring(0, sReturn.Length - 2);
			}
			else
			{
				return "";
			}

		}

		public bool TestIMAPSettings(string sServer, string sPort, string sUser, string sPassword)
		{


			//Test IMAP settings
			//Returns True or False
			//If False error message is in clWebmail::sAccountOrLoginErrorMessage
			try
			{

				clEncrypt2 oCrypt = new clEncrypt2();

				Imap imp = new Imap();

				if (sServer == "")
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "You do not have an IMAP server setting.";
					return false;
				}

				if (sUser == "")
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "You do not have an IMAP user setting.";
					return false;
				}

				if (sPassword != "")
				{
					sPassword = oCrypt.Decrypt(sPassword);
				}

				if (sPassword == "")
				{

					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "You do not have an IMAP user password setting.";
					return false;
				}


				if (imp.Connect(sServer, Convert.ToInt32(sPort)) == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" + sServer + "). No further details are available.";
					return false;
				}
				if (imp.Login(sUser, sPassword) == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" + sServer + "). No further details are available.";
					return false;
				}

			}
			catch (Exception ex)
			{
				bAccountOrLoginError = true;
				sAccountOrLoginErrorMessage = ex.Message;
				return false;
			}

			return true;


		}

		public bool TestSMTPSettings(string sServer, string sPort, string sUser, string sPassword, string sAuthentication, bool bAuthenticationRequired = true)
		{

			//Test IMAP settings
			//Returns True or False
			//If False error message is in clWebmail::sAccountOrLoginErrorMessage

			if (sServer == "")
			{
				bAccountOrLoginError = true;
				sAccountOrLoginErrorMessage = "You must first provide a server setting to test.";
				return false;
			}

			if (bAuthenticationRequired == true)
			{

				if (sUser == "")
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "You must first provide a user setting to test.";
					return false;
				}

				if (sPassword == "")
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "You must first provide a password setting to test.";
					return false;
				}

				clEncrypt2 oCrypt = new clEncrypt2();
				sPassword = oCrypt.Decrypt(sPassword);

			}





			try
			{
				MailBee.SmtpMail.Smtp mailer = new MailBee.SmtpMail.Smtp();

				mailer.SmtpServers.Add(sServer, sUser, sPassword);
				mailer.SmtpServers[0].Port = Convert.ToInt32(sPort);

				if (mailer.Connect() == false)
				{
					bAccountOrLoginError = true;
					sAccountOrLoginErrorMessage = "Unable to connect to your SMTP server (" + sServer + "). No further details are available.";
					return false;
				}

				mailer.Hello();

				if (GetSetting("smtp_server_requires_authentication") == "1")
				{
					if (mailer.Login() == false)
					{
						bAccountOrLoginError = true;
						sAccountOrLoginErrorMessage = "Unable to connect to your SMTP server (" + sServer + "). No further details are available.";
						return false;
					}
				}


			}
			catch (Exception ex)
			{

				bAccountOrLoginError = true;
				sAccountOrLoginErrorMessage = ex.Message;
				return false;

			}

			return true;


		}

		public Microsoft.VisualBasic.Collection ConvertAddressStringToCollection(string sAddresses)
		{

			if (sAddresses == "")
			{
				return new Microsoft.VisualBasic.Collection();
			}

			sAddresses = sAddresses.Replace(";", ",");
			sAddresses = sAddresses.Replace(", ", ",");
			sAddresses = sAddresses.Replace(", ", ",");
			sAddresses = sAddresses.Replace(", ", ",");

			Microsoft.VisualBasic.Collection cReturn = new Microsoft.VisualBasic.Collection();
			string[] aAddresses = sAddresses.Split(',');

			foreach (string Addr in aAddresses)
			{
				cReturn.Add(Addr);
			}

			return cReturn;

		}

		public bool CleanOldWebmailItems(int iNumberOfDays = 2)
		{


			string sWebmailFolder = System.Web.HttpContext.Current.Server.MapPath("../Temp/Webmail/");

			DirectoryInfo source = new DirectoryInfo(sWebmailFolder);

			//Determine whether the source directory exists. 
			if (source.Exists)
			{

				//Directories under /Temp/Webmail
				//For Each Directory In New IO.DirectoryInfo(sWebmailFolder).GetDirectories
				foreach (var Directory in source.GetDirectories())
				{
					if (Directory.LastWriteTime < DateTime.Today.AddDays(-iNumberOfDays))
					{
						//Try
						Directory.Delete(true);
						//Catch ex As Exception

						//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
						//        goErr.SetError(ex, 45100, "clWebmail::CleanOldWebmailItems")
						//    End If

						//End Try

					}
				}
			}



			return true;
		}

		public bool RewriteMailHTMLToFile(SellMailItem oSellMailItem, string strNewFileHTML)
		{

			//Try
			string sPrivateDirectory = System.Web.HttpContext.Current.Server.MapPath("../Temp/Webmail/") + oSellMailItem.s_MID.ToUpper();
				Microsoft.VisualBasic.FileIO.FileSystem.WriteAllText(sPrivateDirectory + "\\message.html", strNewFileHTML, false);
			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, "clWebMail::RewriteMailHTMLToFile")
			//    End If

			//End Try



// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

	}

	public class SellMailItem
	{
		public Microsoft.VisualBasic.Collection c_To;
		public Microsoft.VisualBasic.Collection c_CC;
		public Microsoft.VisualBasic.Collection c_BCC;
		public string s_From;
		public string s_Subject;
		public string s_UID;
		public string s_MID;
		public string s_URL;
		public string s_HTMLBody;
		public string s_TextBody;
		public Microsoft.VisualBasic.Collection c_Attachments;
		public DateTime dt_DateReceived;
		public DateTime dt_DateSent;



		private MailBee.Mime.MailMessage msg; //= GetNewMsg()

		private MailBee.Mime.MailMessage GetNewMsg()
		{

			MailBee.Mime.MailMessage oRetMsg = new MailBee.Mime.MailMessage();
			return oRetMsg;

		}

		public SellMailItem()
		{

		}
	}






}
