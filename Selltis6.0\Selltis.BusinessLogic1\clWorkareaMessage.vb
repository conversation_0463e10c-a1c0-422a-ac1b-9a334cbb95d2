Imports Microsoft.VisualBasic
Imports System.Web

Public Class clWorkareaMessage

#Region "Declarations"

    Private goErr As clError

    Private gsMBFormMode As String = "NORMAL"
    Private gsMBScriptInitiatedSave As String = ""
    Private gbMBDisplay As Boolean = False
    Private gsMBMessage As String = ""
    Private giMBStyle As Integer = clC.SELL_MB_OK
    Private gsMBTitle As String = ""
    Private gsMBButton1Label As String = ""
    Private gsMBButton2Label As String = ""
    Private gsMBButton3Label As String = ""
    Private gsMBInputDefaultValue As String = ""
    Private gsMBButton1Script As String = ""
    Private gsMBButton2Script As String = ""
    Private gsMBButton3Script As String = ""
    Private gsMBPar1 As String = ""
    Private gsMBPar2 As String = ""
    Private gsMBPar3 As String = ""
    Private gsMBPar4 As String = ""
    Private gsMBPar5 As String = ""
    Private goCallingObject As Object

#End Region

#Region "Public Properties"

    Public ReadOnly Property MessageBoxButton1Label() As String
        Get
            Return gsMBButton1Label
        End Get
    End Property

    Public ReadOnly Property MessageBoxButton1Script() As String
        Get
            Return gsMBButton1Script
        End Get
    End Property

    Public ReadOnly Property MessageBoxButton2Label() As String
        Get
            Return gsMBButton2Label
        End Get
    End Property

    Public ReadOnly Property MessageBoxButton2Script() As String
        Get
            Return gsMBButton2Script
        End Get
    End Property

    Public ReadOnly Property MessageBoxButton3Label() As String
        Get
            Return gsMBButton3Label
        End Get
    End Property

    Public ReadOnly Property MessageBoxButton3Script() As String
        Get
            Return gsMBButton3Script
        End Get
    End Property

    Public ReadOnly Property MessageBoxDisplay() As Boolean
        Get
            Return gbMBDisplay
        End Get
    End Property

    Public ReadOnly Property MessageBoxFormMode() As String
        Get
            Return gsMBFormMode
        End Get
    End Property

    Public ReadOnly Property MessageBoxInputDefaultValue() As String
        Get
            Return gsMBInputDefaultValue
        End Get
    End Property

    Public ReadOnly Property MessageBoxMessage() As String
        Get
            Return gsMBMessage
        End Get
    End Property

    Public ReadOnly Property MessageBoxPar1() As String
        Get
            Return gsMBPar1
        End Get
    End Property

    Public ReadOnly Property MessageBoxPar2() As String
        Get
            Return gsMBPar2
        End Get
    End Property

    Public ReadOnly Property MessageBoxPar3() As String
        Get
            Return gsMBPar3
        End Get
    End Property

    Public ReadOnly Property MessageBoxPar4() As String
        Get
            Return gsMBPar4
        End Get
    End Property

    Public ReadOnly Property MessageBoxPar5() As String
        Get
            Return gsMBPar5
        End Get
    End Property

    Public ReadOnly Property MessageBoxScriptInitiatedSave() As String
        Get
            Return gsMBScriptInitiatedSave
        End Get
    End Property

    Public ReadOnly Property MessageBoxStyle() As Integer
        Get
            Return giMBStyle
        End Get
    End Property

    Public ReadOnly Property MessageBoxTitle() As String
        Get
            Return gsMBTitle
        End Get
    End Property

    Public ReadOnly Property MessageBoxCallingObject() As Object
        Get
            Return goCallingObject
        End Get
    End Property

#End Region

#Region "Private Methods"

    Private Sub Initialize()
        Dim sProc As String = "clWorkareaMessage:Initialize"
        'Try
        goErr = HttpContext.Current.Session("goErr")
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

#End Region

#Region "Public Methods"

    Public Sub New(ByVal par_sMessage As String, _
                  Optional ByVal par_iStyle As Integer = clC.SELL_MB_OK, _
                  Optional ByVal par_sTitle As String = "Selltis", _
                  Optional ByVal par_sButton1Label As String = "", _
                  Optional ByVal par_sButton2Label As String = "", _
                  Optional ByVal par_sButton3Label As String = "", _
                  Optional ByVal par_sInputDefaultValue As String = "", _
                  Optional ByVal par_sButton1Script As String = "", _
                  Optional ByVal par_sButton2Script As String = "", _
                  Optional ByVal par_sButton3Script As String = "", _
                  Optional ByRef par_doCallingObject As Object = Nothing, _
                  Optional ByVal par_doArray As clArray = Nothing, _
                  Optional ByVal par_s1 As String = "", _
                  Optional ByVal par_s2 As String = "", _
                  Optional ByVal par_s3 As String = "", _
                  Optional ByVal par_s4 As String = "", _
                  Optional ByVal par_s5 As String = "")

        'par_doCallingObject needs default value of "doForm" per Mario, which I guess is a refrence to ME...

        '- par_iStyle is not Microsoft.VisualBasic.MsgBoxStyle data type to allow supporting
        'InputBox as a style and not to have to support all standard MsgBox styles. Initially,
        'the following styles should be supported:
        '        SELL_MB_OK (default)
        '        SELL_MB_YESNO()
        '        SELL_MB_YESNOCANCEL()
        '        SELL_MB_INPUTBOX (OK button only)
        '        SELL_MB_ABORTRETRYIGNORE = &H2
        '        SELL_MB_OKCANCEL = &H1
        '        SELL_MB_RETRYCANCEL = &H5
        '        SELL_MB_DEFBUTTON1()
        '        SELL_MB_DEFBUTTON2()
        '        SELL_MB_DEFBUTTON3()
        'Icons are not supported to keep the spec tight.
        'Styles can be added (for ex clc.SELL_MB_YESNOCANCEL + clc.SELL_MB_DEFBUTTON2).

        '- par_sButton<1-3>Label allows applying custom labels to the buttons. Leaving these parameters
        'blank causes default button labels to be used. In the case of SELL_MB_INPUTBOX,
        'par_sButton1Label relabels the OK button.

        '- par_sInputDefaultValue is the value to enter into the input field by default.

        '- par_sButton<1-3>Script allows defining script procedure names that will be executed when each button
        'is clicked. If a script is not defined, the button click causes Return False to be executed.

        '- Last 7 parameters are passed to the called script(s) unchanged. In the form context, par_doCallingObject 
        'should always be doForm (set by default).

        Dim sProc As String = "clWorkareaMessage::MessageBox"
        'Try

        Initialize()

            gbMBDisplay = True
            gsMBMessage = par_sMessage
            giMBStyle = par_iStyle
            gsMBTitle = par_sTitle
            gsMBButton1Label = par_sButton1Label
            gsMBButton2Label = par_sButton2Label
            gsMBButton3Label = par_sButton3Label
            gsMBInputDefaultValue = par_sInputDefaultValue
            gsMBButton1Script = par_sButton1Script
            gsMBButton2Script = par_sButton2Script
            gsMBButton3Script = par_sButton3Script
            gsMBPar1 = par_s1
            gsMBPar2 = par_s2
            gsMBPar3 = par_s3
            gsMBPar4 = par_s4
            gsMBPar5 = par_s5

        'redirect - open form
        'goUI.SetVar(GUID, Me)
        'HttpContext.Current.Response.Redirect(goUI.Navigate("FORMOBJECT", GUID))

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Public Function GetButtonLabel(ByVal sDefault As String, ByVal sSetting As String) As String
        Dim sProc As String = "clWorkareaMessage::GetButtonLabel"
        Try
            Select Case sSetting
                Case ""
                    Return sDefault
                Case Else
                    Return sSetting
            End Select
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return ""
        End Try
    End Function

#End Region

End Class
