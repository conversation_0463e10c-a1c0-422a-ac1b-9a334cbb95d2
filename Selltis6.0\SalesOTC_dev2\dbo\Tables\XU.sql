﻿CREATE TABLE [dbo].[XU] (
    [GID_ID]                         UNIQUEIDENTIFIER CONSTRAINT [DF_XU_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'XU',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                         BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_Name]                       NVARCHAR (80)    NULL,
    [DTT_CreationTime]               DATETIME         CONSTRAINT [DF_XU_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [TXT_LogonName]                  NVARCHAR (20)    NOT NULL,
    [TXT_Password]                   NVARCHAR (20)    NOT NULL,
    [GID_UserID]                     UNIQUEIDENTIFIER NULL,
    [TXT_ModBy]                      VARCHAR (4)      NULL,
    [DTT_ModTime]                    DATETIME         CONSTRAINT [DF_XU_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [SI__ShareState]                 TINYINT          CONSTRAINT [DF_XU_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]               UNIQUEIDENTIFIER NULL,
    [DTT_AgrAccepted]                DATETIME         NULL,
    [DTT_LastLogin]                  DATETIME         NULL,
    [CHK_LoginGroup]                 BIT              CONSTRAINT [DF_XU_CHK_LoginGroup] DEFAULT ((0)) NULL,
    [GID_InLoginGroup]               UNIQUEIDENTIFIER NULL,
    [SI__BillingType]                TINYINT          CONSTRAINT [DF_XU_SI__Billing] DEFAULT ((0)) NOT NULL,
    [CHK_Enabled]                    BIT              DEFAULT ('1') NULL,
    [CHK_PasswordIsTemporary]        BIT              DEFAULT ('1') NULL,
    [DTT_TempPasswordExpirationTime] DATETIME         DEFAULT (dateadd(day,(7),getutcdate())) NULL,
    [DTT_TempPasswordGeneratedTime]  DATETIME         NULL,
    [TXT_TempPasswordDeliveryMethod] VARCHAR (8)      NULL,
    [TXT_TempPasswordGeneratedBy]    VARCHAR (4)      NULL,
    [TXT_TempPasswordSentTo]         NVARCHAR (80)    NULL,
    [TXT_Message]                    NVARCHAR (100)   NULL,
    [CHK_LogoffNow]                  BIT              NULL,
    [CHK_PasswordDoesntExpire]       BIT              NULL,
    [TXT_PasswordChangedBy]          VARCHAR (4)      NULL,
    [DTT_PasswordChangedTime]        DATETIME         NULL,
    [CHK_Bill]                       BIT              DEFAULT ('1') NULL,
    [TXT_FCMToken]                   NVARCHAR (MAX)   NULL,
    [TXT_FCMTokenDevice]             VARCHAR (50)     NULL,
    CONSTRAINT [PK_XU] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC)
);


GO
CREATE UNIQUE CLUSTERED INDEX [IX_XU_LogonName]
    ON [dbo].[XU]([TXT_LogonName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XU_CreationTime]
    ON [dbo].[XU]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XU_ModTime]
    ON [dbo].[XU]([DTT_ModTime] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'0=Sales 1=Lite 2=Partner 255=Do not bill', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'XU', @level2type = N'COLUMN', @level2name = N'SI__BillingType';

