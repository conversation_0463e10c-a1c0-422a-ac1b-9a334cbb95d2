﻿CREATE TABLE [dbo].[MS_Related_GR] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_MS_Related_GR_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_MS] UNIQUEIDENTIFIER NOT NULL,
    [GID_GR] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MS_Related_GR] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_GR_Related_MS] FOREIGN KEY ([GID_MS]) REFERENCES [dbo].[MS] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_MS_Related_GR] FOREIGN KEY ([GID_GR]) REFERENCES [dbo].[GR] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MS_Related_GR] NOCHECK CONSTRAINT [LNK_GR_Related_MS];


GO
ALTER TABLE [dbo].[MS_Related_GR] NOCHECK CONSTRAINT [LNK_MS_Related_GR];


GO
CREATE NONCLUSTERED INDEX [IX_MS_Related_GR]
    ON [dbo].[MS_Related_GR]([GID_MS] ASC, [GID_GR] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_GR_Related_MS]
    ON [dbo].[MS_Related_GR]([GID_GR] ASC, [GID_MS] ASC);

