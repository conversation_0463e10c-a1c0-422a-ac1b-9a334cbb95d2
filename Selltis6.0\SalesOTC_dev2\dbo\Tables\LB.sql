﻿CREATE TABLE [dbo].[LB] (
    [GID_ID]                 UNIQUEIDENTIFIER CONSTRAINT [DF_LB_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'LB',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                 BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]               NVARCHAR (80)    NULL,
    [DTT_CreationTime]       DATETIME         CONSTRAINT [DF_LB_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]           TINYINT          NULL,
    [TXT_ModBy]              VARCHAR (4)      NULL,
    [DTT_ModTime]            DATETIME         CONSTRAINT [DF_LB_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_LineofBusinessName] NVARCHAR (50)    NULL,
    [MMO_ImportData]         NTEXT            NULL,
    [SI__ShareState]         TINYINT          CONSTRAINT [DF_LB_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]       UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]           VARCHAR (50)     NULL,
    [TXT_ExternalID]         NVARCHAR (80)    NULL,
    [TXT_ExternalSource]     VARCHAR (10)     NULL,
    [TXT_ImpJobID]           VARCHAR (20)     NULL,
    CONSTRAINT [PK_LB] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_LB_LineofBusinessName]
    ON [dbo].[LB]([TXT_LineofBusinessName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_LB_CreatedBy_US]
    ON [dbo].[LB]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_LB_ModDateTime]
    ON [dbo].[LB]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_LB_Name]
    ON [dbo].[LB]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_LB_CreationTime]
    ON [dbo].[LB]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_LB_BI__ID]
    ON [dbo].[LB]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_LB_TXT_ImportID]
    ON [dbo].[LB]([TXT_ImportID] ASC);

