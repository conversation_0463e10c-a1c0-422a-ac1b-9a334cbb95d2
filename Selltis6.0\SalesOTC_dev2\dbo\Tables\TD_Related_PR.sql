﻿CREATE TABLE [dbo].[TD_Related_PR] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_ToDo_Related_Project_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_PR] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_Related_Project] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PR_Connected_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_Related_PR] FOREIGN KEY ([GID_PR]) REFERENCES [dbo].[PR] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_Related_PR] NOCHECK CONSTRAINT [LNK_PR_Connected_TD];


GO
ALTER TABLE [dbo].[TD_Related_PR] NOCHECK CONSTRAINT [LNK_TD_Related_PR];


GO
CREATE CLUSTERED INDEX [IX_PR_Connected_TD]
    ON [dbo].[TD_Related_PR]([GID_TD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_Related_PR]
    ON [dbo].[TD_Related_PR]([GID_PR] ASC);

