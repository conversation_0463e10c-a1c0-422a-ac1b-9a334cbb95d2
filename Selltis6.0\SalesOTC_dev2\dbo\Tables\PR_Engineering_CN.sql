﻿CREATE TABLE [dbo].[PR_Engineering_CN] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_PR_Engineering_CN_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PR] UNIQUEIDENTIFIER NOT NULL,
    [GID_CN] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PR_Engineering_CN] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CN_EngineerFor_PR] FOREIGN KEY ([GID_PR]) REFERENCES [dbo].[PR] ([GID_ID]),
    CONSTRAINT [LNK_PR_Engineering_CN] FOREIGN KEY ([GID_CN]) REFERENCES [dbo].[CN] ([GID_ID])
);


GO
ALTER TABLE [dbo].[PR_Engineering_CN] NOCHECK CONSTRAINT [LNK_CN_EngineerFor_PR];


GO
ALTER TABLE [dbo].[PR_Engineering_CN] NOCHECK CONSTRAINT [LNK_PR_Engineering_CN];

