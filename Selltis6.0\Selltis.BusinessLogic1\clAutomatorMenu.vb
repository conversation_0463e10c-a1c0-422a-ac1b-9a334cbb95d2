'Author: WT

Imports Microsoft.VisualBasic
Imports System.Web

Public Class clAutomatorMenu

    Private goP As clProject
    Private goErr As clError
    Private goData As clData
    Private goTR As clTransform

    Private Sub Initialize()
        Dim sProc As String = "clAutomatorMenu:Initialize"
        ' Try
        goP = HttpContext.Current.Session("goP")
            goErr = HttpContext.Current.Session("goErr")
            goData = HttpContext.Current.Session("goData")
            goTR = HttpContext.Current.Session("goTr")
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Public Sub New()
        Dim sProc As String = "clAutomatorMenu::New"
        ' Try
        Initialize()
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    'Public Function GetMenu() As MenuItem

    '    Dim sProc As String = "clAutomatorMenu::GetMenu"
    '    Try

    '        Dim oMenuItem As MenuItem
    '        Dim oSubMenuItem As MenuItem
    '        Dim iResult As Integer = 0

    '        '*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
    '        Dim oConnection As SqlClient.SqlConnection = goData.GetConnection
    '        Dim oCommand As New SqlClient.SqlCommand
    '        Dim oReader As SqlClient.SqlDataReader

    '        oMenuItem = New MenuItem("Tasks")
    '        'oMenuItem.Selectable = False
    '        oMenuItem.NavigateUrl = "javascript:returnFalse();"
    '        oMenuItem.SeparatorImageUrl = "../Images/Blank16.gif"

    '        oCommand.CommandText = "pGetActiveMenuAutomators"
    '        oCommand.CommandType = CommandType.StoredProcedure
    '        oCommand.Connection = oConnection

    '        'return parameter
    '        Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
    '        retValParam.Direction = ParameterDirection.ReturnValue
    '        oCommand.Parameters.Add(retValParam)

    '        'execute            
    '        oReader = oCommand.ExecuteReader()

    '        'read returned value
    '        If oReader.HasRows Then
    '            Do While oReader.Read()
    '                Dim sSection As String = Trim(oReader("Section").ToString)
    '                Dim sPage As String = Trim(oReader("Page").ToString)
    '                Dim sName As String = Trim(oReader("Name").ToString)
    '                Dim sIcon As String = Trim(oReader("Icon").ToString)
    '                Dim sTooltip As String = Trim(oReader("Tooltip").ToString)
    '                Dim sType As String = Trim(oReader("A_01_TYPE").ToString)
    '                Dim sExecute As String = Trim(oReader("A_01_EXECUTE").ToString)

    '                If sIcon = "" Then sIcon = "blank16.gif"

    '                Select Case sType
    '                    Case clC.SELL_AGE_RUNSCRIPT
    '                        oSubMenuItem = New MenuItem()
    '                        'oSubMenuItem.ImageUrl = "../Images/" & sIcon
    '                        oSubMenuItem.Text = sTooltip
    '                        oSubMenuItem.NavigateUrl = "javascript:RefreshContent('SiteRunScript','RunScript.aspx?scr=" & sExecute & "');"
    '                        oMenuItem.ChildItems.Add(oSubMenuItem)

    '                    Case clC.SELL_AGE_OPENRECORD
    '                        Dim sArgs As String = "FORM||" & goTR.GetFileFromSUID(sExecute) & "||" & sExecute & "|"
    '                        oSubMenuItem = New MenuItem()
    '                        'oSubMenuItem.ImageUrl = "../Images/" & sIcon
    '                        oSubMenuItem.Text = sTooltip
    '                        oSubMenuItem.NavigateUrl = "javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"
    '                        oMenuItem.ChildItems.Add(oSubMenuItem)

    '                    Case clC.SELL_AGE_OPENDESKTOP
    '                        Dim sArgs As String = "DESKTOP||" & goP.GetUserTID & "|" & sExecute
    '                        oSubMenuItem = New MenuItem()
    '                        'oSubMenuItem.ImageUrl = "../Images/" & sIcon
    '                        oSubMenuItem.Text = sTooltip
    '                        oSubMenuItem.NavigateUrl = "javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"
    '                        oMenuItem.ChildItems.Add(oSubMenuItem)

    '                    Case clC.SELL_AGE_OPENURL
    '                        Dim sArgs As String = "URL|" & sExecute
    '                        oSubMenuItem = New MenuItem()
    '                        'oSubMenuItem.ImageUrl = "../Images/" & sIcon
    '                        oSubMenuItem.Text = sTooltip
    '                        oSubMenuItem.NavigateUrl = "javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"
    '                        oMenuItem.ChildItems.Add(oSubMenuItem)

    '                    Case clC.SELL_AGE_OPENURLEXTERNAL
    '                        Dim sArgs As String = "URL|" & sExecute
    '                        oSubMenuItem = New MenuItem()
    '                        'oSubMenuItem.ImageUrl = "../Images/" & sIcon
    '                        oSubMenuItem.Text = sTooltip
    '                        oSubMenuItem.NavigateUrl = "javascript:window.open('" & sExecute & "','SelltisURL', '');"
    '                        oMenuItem.ChildItems.Add(oSubMenuItem)

    '                    Case clC.SELL_AGE_OPENNDBFORM
    '                        Dim sArgs As String = "NDBFORM|" & sExecute
    '                        oSubMenuItem = New MenuItem()
    '                        oSubMenuItem.Text = sTooltip
    '                        oSubMenuItem.NavigateUrl = "javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"
    '                        oMenuItem.ChildItems.Add(oSubMenuItem)

    '                    Case clC.SELL_AGE_OPENCVS
    '                        Dim sArgs As String = "CVS|" & sExecute
    '                        oSubMenuItem = New MenuItem()
    '                        oSubMenuItem.Text = sTooltip
    '                        oSubMenuItem.NavigateUrl = "javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"
    '                        oMenuItem.ChildItems.Add(oSubMenuItem)


    '                    Case clC.SELL_AGE_OPENFRF
    '                        Dim sArgs As String = "FRF|" & sExecute
    '                        oSubMenuItem = New MenuItem()
    '                        oSubMenuItem.Text = sTooltip
    '                        oSubMenuItem.NavigateUrl = "javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"
    '                        oMenuItem.ChildItems.Add(oSubMenuItem)


    '                    Case Else
    '                        oSubMenuItem = New MenuItem()
    '                        'oSubMenuItem.ImageUrl = "../Images/" & sIcon
    '                        oSubMenuItem.Text = sTooltip
    '                        oSubMenuItem.NavigateUrl = "javascript:return false;"
    '                        oMenuItem.ChildItems.Add(oSubMenuItem)

    '                End Select

    '            Loop

    '        Else
    '            oMenuItem.Enabled = False
    '        End If

    '        oReader.Close()
    '        oConnection.Close()

    '        If iResult = 0 Then
    '            Return oMenuItem
    '        Else
    '            '==> error
    '            Return New MenuItem
    '        End If

    '    Catch ex As Exception
    '        If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
    '            goErr.SetError(ex, 45105, sProc)
    '        End If
    '        Return New MenuItem
    '    End Try

    'End Function

    Public Function GetCSSMenu() As String 'MenuItem

        Dim sProc As String = "clAutomatorMenu::GetCSSMenu"
        Try

            '*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
            Dim oConnection As SqlClient.SqlConnection = goData.GetConnection
            Dim oCommand As New SqlClient.SqlCommand
            Dim oReader As SqlClient.SqlDataReader

            oCommand.CommandText = "pGetActiveMenuAutomators"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            Dim sHTML As String = ""

            'return parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            'execute            
            oReader = oCommand.ExecuteReader()

            If oReader.HasRows Then

                'sHTML = sHTML & "<li>"
                'sHTML = sHTML & "<a class=""parent"">Tasks</a>"
                sHTML = sHTML & "<ul>"
                'sHTML = sHTML & "<li class=""arrow""></li>"

                Do While oReader.Read()
                    Dim sSection As String = Trim(oReader("Section").ToString)
                    Dim sPage As String = Trim(oReader("Page").ToString)
                    Dim sName As String = Trim(oReader("Name").ToString)
                    Dim sIcon As String = Trim(oReader("Icon").ToString)
                    Dim sTooltip As String = Trim(oReader("Tooltip").ToString)
                    Dim sType As String = Trim(oReader("A_01_TYPE").ToString)
                    Dim sExecute As String = Trim(oReader("A_01_EXECUTE").ToString)
                    Dim sLabel As String = ""
                    If (sTooltip.Length > 60) Then
                        sLabel = Strings.Left(sTooltip, 60) & "..."
                    Else
                        sLabel = sTooltip
                    End If

                    If sIcon = "" Then sIcon = "blank16.gif"

                    Select Case sType
                        Case clC.SELL_AGE_RUNSCRIPT
                            sHTML = sHTML & "<li style=""cursor:pointer;"" title='" & sTooltip & "' onclick=""javascript:TasksContent('" + sExecute + "','" + sType + "');"">" & sLabel & "</li>"

                            'sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:TasksContent('" + sExecute + "','" + sType + "');"">" & sLabel & "</a></li>"
                            'sHTML = sHTML & "<li><a onclick=""javascript:RefreshContent('SiteRunScript','RunScript.aspx?scr=" & sExecute & "');"">" & sTooltip & "</a></li>"

                        Case clC.SELL_AGE_OPENRECORD
                            Dim sArgs As String = "FORM|" & goTR.GetFileFromSUID(sExecute) & "|" & sExecute
                            sHTML = sHTML & "<li style=""cursor:pointer;"" title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</li>"

                            'sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</a></li>"
                            'sHTML = sHTML & "<li><a onclick=""javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"">" & sTooltip & "</a></li>"

                        Case clC.SELL_AGE_OPENDESKTOP
                            Dim sArgs As String = "DESKTOP|" & goP.GetUserTID & "|" & sExecute
                            sHTML = sHTML & "<li style=""cursor:pointer;"" title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</li>"
                            'sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</a></li>"
                            'sHTML = sHTML & "<li><a onclick=""javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"">" & sTooltip & "</a></li>"

                        Case clC.SELL_AGE_OPENURL
                            Dim sArgs As String = "URL|" & sExecute
                            sHTML = sHTML & "<li style=""cursor:pointer;"" title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</li>"
                            'sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</a></li>"
                            'sHTML = sHTML & "<li><a onclick=""javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"">" & sTooltip & "</a></li>"

                        Case clC.SELL_AGE_OPENURLEXTERNAL
                            Dim sArgs As String = "URL|" & sExecute
                            sHTML = sHTML & "<li style=""cursor:pointer;"" title='" & sTooltip & "' onclick=""javascript:window.open('" & sExecute & "','SelltisURL', '');"">" & sLabel & "</li>"
                            'sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:window.open('" & sExecute & "','SelltisURL', '');"">" & sLabel & "</a></li>"

                        Case clC.SELL_AGE_OPENNDBFORM
                            Dim sArgs As String = "NDBFORM|" & sExecute
                            sHTML = sHTML & "<li style=""cursor:pointer;"" title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</li>"
                            'sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</a></li>"
                            'sHTML = sHTML & "<li><a onclick=""javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"">" & sTooltip & "</a></li>"

                        Case clC.SELL_AGE_OPENCVS
                            Dim sArgs As String = "CVS|" & sExecute
                            sHTML = sHTML & "<li style=""cursor:pointer;"" title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</li>"
                            'sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</a></li>"
                            'sHTML = sHTML & "<li><a onclick=""javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"">" & sTooltip & "</a></li>"

                        Case clC.SELL_AGE_OPENFRF
                            Dim sArgs As String = "FRF|" & sExecute
                            sHTML = sHTML & "<li style=""cursor:pointer;"" title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</li>"
                            'sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</a></li>"
                            'sHTML = sHTML & "<li><a onclick=""javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"">" & sTooltip & "</a></li>"

                        Case Else
                            sHTML = sHTML & "<li style=""cursor:pointer;"" title='" & sTooltip & "' onclick=""javascript:return false;"">" & sLabel & "</li>"
                            'sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:return false;"">" & sLabel & "</a></li>"

                    End Select

                Loop

                ''sHTML = sHTML & "</ul></li>"
                sHTML = sHTML & "</ul>"

            Else
                ''sHTML = sHTML & "<li><a class=""disabled"">Tasks</a></li>"
                sHTML = sHTML & "</ul>"

            End If

            oReader.Close()
            oConnection.Close()

            Return sHTML

        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return ""
        End Try

    End Function

End Class
