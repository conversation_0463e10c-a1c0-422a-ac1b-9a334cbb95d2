﻿CREATE TABLE [dbo].[RS_Related_GR] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Resource_Related_Group_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_RS] UNIQUEIDENTIFIER NOT NULL,
    [GID_GR] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_RS_Related_GR] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_GR_Connected_RS] FOREIGN KEY ([GID_RS]) REFERENCES [dbo].[RS] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_RS_Related_GR] FOREIGN KEY ([GID_GR]) REFERENCES [dbo].[GR] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[RS_Related_GR] NOCHECK CONSTRAINT [LNK_GR_Connected_RS];


GO
ALTER TABLE [dbo].[RS_Related_GR] NOCHECK CONSTRAINT [LNK_RS_Related_GR];


GO
CREATE CLUSTERED INDEX [IX_GR_Connected_RS]
    ON [dbo].[RS_Related_GR]([GID_RS] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RS_Related_GR]
    ON [dbo].[RS_Related_GR]([GID_GR] ASC);

