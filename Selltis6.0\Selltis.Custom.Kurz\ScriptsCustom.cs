﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using System.Xml;
using System.Diagnostics;
using System.Net;


namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;
        private clLog goLog;
        private clDefaults goDef;
        //private clScrMng goScr;
        ScriptManager scriptManager = new ScriptManager();
        private ClUI goUI;
        private clPerm goPerm;
        private clHistory goHist;
        public string sError;

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";

        System.Data.SqlClient.SqlConnection par_oConnection = null;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();
            Util.SetSessionValue("SkipQLSpecificLogic", "Y");
        }
        public ScriptsCustom()
        {
            Initialize();
        }


        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}


            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }

        public bool CalcAllTemp(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string myQTID;

            clRowSet doRS;

            doRS = new clRowSet("QT", 1, "CUR_QTLISTTOTAL = 0 OR sr__discount = 0", null, "GID_ID");
            if (doRS.GetFirst() == 1)
            {
                do
                {
                    myQTID = Convert.ToString(doRS.GetFieldVal("GID_ID"));
                    scriptManager.RunScript("CalcQuoteTotal",ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections,null,"","","","","");
                    if (doRS.GetNext() == 0)
                        break;
                }
                while (true);
            }
            return true;
        }

        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doform = (Form)par_doCallingObject;

            scriptManager.RunScript("Activity_FillUnderSignature", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");

            return true;
        }

        public bool Activity_FillUnderSignature(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            // Fills the Rep Co's info in the MMO_Undersignature field on the AC form

            Form doForm = (Form)par_doCallingObject;
            int iType;
            clRowSet doRSRepCo;
            string sCoName = "";
            string sCoPhone = "";
            string sCoWeb = "";
            string sUnderSig = "";
            string sUnderSigNew = "";

            // Abort if not new record
            if (doForm.doRS.iRSType != 2)
                return true;

            if (doForm.doRS.GetLinkCount("LNK_RELATED_CO") != 1)
                return true;

            // TLD 8/27/2010 ----------Changed to Case statement
            iType = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_TYPE", 2));
            switch (iType)
            {
                case 2:
                case 4:
                case 6:
                    {
                        // Get related company's Rep Company
                        doRSRepCo = new clRowSet("CO", 3, "LNK_REPFOR_CO='" + doForm.doRS.GetFieldVal("LNK_RELATED_CO") + "'", goData.GetDefaultSort("CO"), "GID_ID, TXT_COMPANYNAME, TEL_PHONENO, URL_WEBPAGE");
                        if (doRSRepCo.Count() == 0)
                            return true;

                        sCoName = Convert.ToString(doRSRepCo.GetFieldVal("TXT_COMPANYNAME"));
                        sCoPhone = Convert.ToString(doRSRepCo.GetFieldVal("TEL_PHONENO"));
                        sCoWeb = Convert.ToString(doRSRepCo.GetFieldVal("URL_WEBPAGE"));

                        sUnderSig = Convert.ToString(doForm.doRS.GetFieldVal("MMO_UNDERSIGNATURE"));

                        if (sCoName != "")
                        {
                            sUnderSigNew = "Your local Kurz representative is" + Constants.vbCrLf + sCoName;
                        }
                        if (sCoPhone != "")
                        {
                            sUnderSigNew += Constants.vbCrLf + sCoPhone;
                        }
                        if (sCoWeb != "")
                        {
                            sUnderSigNew += Constants.vbCrLf + sCoWeb;
                        }

                        if (sUnderSigNew != "")
                        {
                            sUnderSigNew = sUnderSig + Constants.vbCrLf + Constants.vbCrLf + Constants.vbCrLf + sUnderSigNew;
                        }

                        doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", sUnderSigNew);
                        break;
                    }
            }
            // TLD 8/27/2010 ----------Changed to Case statement

            // TLD 8/27/2010 Changed to Case statements
            // If iType = 2 Or iType = 4 Or iType = 6 Then
            // Get related company's Rep Company
            // doRSRepCo = New clRowSet("CO", 3, "LNK_REPFOR_CO='" & doForm.doRS.GetFieldVal("LNK_RELATED_CO") & "'", goData.GetDefaultSort("CO"), "GID_ID, TXT_COMPANYNAME, TEL_PHONENO, URL_WEBPAGE")
            // If doRSRepCo.Count = 0 Then
            // Return True
            // End If

            // sCoName = doRSRepCo.GetFieldVal("TXT_COMPANYNAME")
            // sCoPhone = doRSRepCo.GetFieldVal("TEL_PHONENO")
            // sCoWeb = doRSRepCo.GetFieldVal("URL_WEBPAGE")

            // sUnderSig = doForm.doRS.GetFieldVal("MMO_UNDERSIGNATURE")

            // If sCoName <> "" Then
            // sUnderSigNew = "Your local Kurz representative is" & vbCrLf & sCoName
            // End If
            // If sCoPhone <> "" Then
            // sUnderSigNew += vbCrLf & sCoPhone
            // End If
            // If sCoWeb <> "" Then
            // sUnderSigNew += vbCrLf & sCoWeb
            // End If

            // If sUnderSigNew <> "" Then
            // sUnderSigNew = sUnderSig & vbCrLf & vbCrLf & vbCrLf & sUnderSigNew
            // End If

            // doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", sUnderSigNew)

            // Else
            // Return True
            // End If
            par_doCallingObject = doForm;
            return true;
        }

        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 4/3/2009 Prevent main from running
            // Added custom totals here
            par_bRunNext = false;

            // MI 2/23/07 Mods
            // PURPOSE:
            // Calculate totals of a Quote from Quote Lines independent of the form context.
            // RETURNS:
            // True if successful, False if not with SetError. Returns calculation results
            // via gop:SetVar() in the following variables:
            // CUR_SUBTOTAL
            // CUR_SUBTOTALT
            // CUR_SALESTAX
            // CUR_TOTAL	

            clRowSet doLines = default(clRowSet);   // 
            int lI;
            decimal cWork = default(decimal);        // Non-taxable subtotals
            decimal cWorkT = default(decimal);        // Taxable subtotals only
            double rTaxPercent;
            decimal cTaxAmount= default(decimal);
            decimal cOtherCharge;
            decimal cTotalAmount;
            decimal cShipAmount;
            // Dim s1 As String = par_s1   'Quote GID_ID
            // Dim s2 As String = par_s2   'Quote Line GID_ID 
            // Dim s3 As String = par_s3
            // Dim s4 As String = par_s4
            // Dim s5 As String = par_s5
            clRowSet doRS = (clRowSet)par_doCallingObject;      // Quote rowset
            bool bCommit = false;
            bool bQLTaxable = false; ;
            decimal cQLSubtotal = default(decimal);
            bool bQLFound = false;
            string sFiles = ""; // FIL_INCLUSIONS from Quote Line Models
            bool bUpdInclusions = false;
            decimal cQTListTotal = 0; // running total of QL list
            decimal cSubTotal = 0; // subtotal for calc QT Discount
            decimal cQLList = 0;

            // Vars are:
            // s1 = QT GID_ID
            // s2 = QL GID_ID
            // s3 = QL CHK_TAXABLE
            // s4 = QL CUR_Subtotal

            // Vars used to be:
            // s1 = goP.GetVar("QUOTE_GID_ID")
            // s2 = goP.GetVar("QUOTE_CHK_INCLUDETAXCHARGES")
            // s3 = goP.GetVar("QUOTE_SR__SALESTAXPERCENT")
            // s4 = goP.GetVar("QUOTE_CUR_OTHERCHARGE")
            // s5 = goP.GetVar("QUOTE_CUR_SHIPPING")


            // ------------- Validate parameters ----------------
            if (!goData.IsFileValid(goTR.GetFileFromSUID(par_s1.ToString())))
            {
                goErr.SetError(10100, sProc, null/* Conversion error: Set to default value for this argument */, goTR.GetFileFromSUID(par_s1), "File extracted from SUID in par_s1: '" + par_s1 + "'. Be sure to pass the GID_ID value of the Quote to recalculate.");
                // 10100: Invalid file name '[1]'. [2]
                return false;
            }
            if (par_s2.ToString() == "")
            {
                // Override QL ID not provided - ignore the rest of QL parameters
                par_s3 = "";
                par_s4 = "";
            }
            else
            {
                // Quote Line GID_ID was passed
                // QL's CHK_Taxable value
                if (par_s3.ToString() == "")
                {
                    goErr.SetError(35000, sProc, "par_s3 is blank. QL's CHK_Taxable value must be passed through this parameter when QL's GID_ID is passed via par_s2.");
                    return false;
                }
                else
                    bQLTaxable = (goTR.StringToCheckbox(par_s3,false,ref par_iValid));
                // QL's CUR_Subtotal value
                if (par_s4.ToString() == "")
                {
                    goErr.SetError(35000, sProc, "par_s4 is blank. QL's CUR_Subtotal value must be passed through this parameter when QL's GID_ID is passed via par_s2.");
                    return false;
                }
                else
                {
                    cQLSubtotal = Convert.ToDecimal(par_s4);
                    // TLD 7/6/2012 Need this here, passed from
                    // QL Record on save when called from there
                    // otherwise adding, new QLs won't total
                    // correctly in Quote List Total and Quote Discount
                    cQLList = Convert.ToDecimal(goP.GetVar("QL_List"));
                }
            }


            // -------------- Read Lines and calculate their amounts ------------
            // CS 12/2/08: Check if MO.FIL_INCLUSIONS exists. If so need to get it in the QL RS below
            if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
            {
                doLines = new clRowSet("QL", 3, "LNK_IN_QT='" + par_s1.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, LNK_FOR_MO%%FIL_INCLUSIONS, SR__QTY, CUR_PRICEUNIT, CHK_INCLUDE");

            }
            else
            {
                doLines = new clRowSet("QL", 3, "LNK_IN_QT='" + par_s1.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL, CHK_INCLUDE, SR__QTY, CUR_PRICEUNIT");

            }
            // Browse through the rowset
            lI = 1;
            if (doLines.GetFirst()==1)
            {
                do
                {
                    // Add up Quote Lines. Skip the one for which GID_ID is passed via par_s2
                    if (par_s2.ToString() == "" | Strings.UCase(par_s2) != Strings.UCase(doLines.GetFieldVal("GID_ID", 1).ToString()))
                    {
                        // TLD 4/14/2009 Modified to ONLY tally if INCLUDE is checked
                        // Also only wants the Quote Value and Total fields totaled if Include is checked
                        // TLD 4/3/2009 Tallies QL (Qty * List)
                        if (Convert.ToInt32(doLines.GetFieldVal("CHK_INCLUDE", 2)) == 1)
                        {
                            cQTListTotal = cQTListTotal + (Convert.ToDecimal(doLines.GetFieldVal("SR__QTY", clC.SELL_SYSTEM)) * (Convert.ToDecimal(doLines.GetFieldVal("CUR_PRICEUNIT", clC.SELL_SYSTEM))));
                            if (Convert.ToInt32(doLines.GetFieldVal("CHK_TAXABLE", 2)) == 1)
                            {
                                cWorkT += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));
                            }
                            else
                            {
                                cWork += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));
                            }
                        }
                        // If doLines.GetFieldVal("CHK_TAXABLE", 2) = 1 Then
                        // cWorkT += doLines.GetFieldVal("CUR_SUBTOTAL", 2)
                        // Else
                        // cWork += doLines.GetFieldVal("CUR_SUBTOTAL", 2)
                        // End If
                        // 'TLD 4/14/2009 Modified to ONLY tally if Report is checked
                        // 'TLD 4/3/2009 Tallies QL (Qty * List)
                        // If doLines.GetFieldVal("CHK_INCLUDE", 2) = 1 Then
                        // cQTListTotal = cQTListTotal + (doLines.GetFieldVal("SR__QTY", clC.SELL_SYSTEM) * (doLines.GetFieldVal("CUR_PRICEUNIT", clC.SELL_SYSTEM)))
                        // End If
                        // CS 12/2/08: Get value from QL%%MO FIL_INCLUSIONS field
                        if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
                        {
                            // Check if the file has already been added
                            if (goTR.Position(sFiles, Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"))) == 0)
                            {
                                // If this is the first file don't add a vbcrlf in front of it
                                if (sFiles == "")
                                {
                                    sFiles = Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"));
                                }
                                else
                                {
                                    sFiles = sFiles + Constants.vbCrLf + doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS");
                                }
                            }
                        }
                    }

                    if (doLines.GetNext() == 0)
                        break;
                    lI += 1;
                }
                while (true);
            }
            // delete(doLines)
            doLines = null;

            // Add the Quote Line passed via parameters
            if (par_s2 != "")
            {
                // CS 12/31/08: Get Fil_Inclusions of QL passed via parameter
                // This code needs to be run if you open a QL directly from
                // a QL view and it has file inclusions
                // CS 1/8/09: Check if FIL_INCLUSIONS exist in db first.
                if (goData.IsFieldValid("MO", "FIL_INCLUSIONS"))
                {
                    doLines = new clRowSet("QL", 3, "GID_ID='" + par_s2.ToString() + "'", goData.GetDefaultSort("QL"), "LNK_FOR_MO%%FIL_INCLUSIONS");
                    if (doLines.GetFirst()==0)
                    {
                        // If goData.IsFieldValid("MO", "FIL_INCLUSIONS") Then
                        // Check if the file has already been added
                        if (goTR.Position(sFiles, Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"))) == 0)
                        {
                            // If this is the first file don't add a vbcrlf in front of it
                            if (sFiles == "")
                            {
                                sFiles = Convert.ToString(doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS"));
                            }
                            else
                            {
                                sFiles = sFiles + Constants.vbCrLf + doLines.GetFieldVal("LNK_FOR_MO%%FIL_INCLUSIONS");
                            }
                        }
                    }
                }

                if (bQLTaxable == true)
                {
                    cWorkT += cQLSubtotal;
                }
                else
                {
                    cWork += cQLSubtotal;
                }

                // TLD 7/6/2012 Need this here, passed from
                // QL Record on save when called from there
                // otherwise adding, new QLs won't total
                // correctly in Quote List Total and Quote Discount
                cQTListTotal += cQLList;
            }

            // Subtotal = cWork + cWorkT
            // SubtotalT = cWorkT

            // ---------- Pull up the Quote -----------
            if (doRS == null)
            {
                // Get the quote from disk
                bCommit = true;
                // doRS = New clRowSet("QT", 1, _
                // "GID_ID='" & par_s1 & "'", _
                // "DTT_TIME ASC", _
                // "CUR_SUBTOTAL, CUR_SUBTOTALT, CUR_SALESTAX, CUR_TOTAL")
                // CS 7/26/07: Currently if you open a QT that has a required field missing
                // such as NA date, edit a QL and save the QL you get an error. Trying to avoid
                // that by bypassing validation.

                // TLD 8/27/2010 Changed Edit RS to Optimize
                doRS = new clRowSet("QT", 1, "GID_ID='" + par_s1 + "'", "DTT_TIME ASC", "*",-1,"","","","","",false,false,false,false,-1,"",false,false,1800);
            }
            else
                // Validate the passed Rowset object
                if (Strings.UCase(doRS.GetFileName()) != "QT")
            {
                goErr.SetError(35000, sProc, "The file of the rowset in par_doCallingObject parameter is not QT (Quote). Either pass a Quote rowset or only pass the Quote GID_ID in par_s1 parameter.");
                return false;
            }

            // ----------- Read Quote data and calculate -------------
            if (doRS.GetFirst()==1)
            {
                // CS 12/2/08: Get the value of the 'Do not update inclusions' on QT
                // If checked, do not update FIL_INCLUSIONS field on QT
                if (goData.IsFieldValid("QT", "CHK_NOUPDINCLUSIONS"))
                {
                    if (Convert.ToInt32(doRS.GetFieldVal("CHK_NOUPDINCLUSIONS", 2)) == 0)
                    {
                        bUpdInclusions = true;
                    }
                }
                rTaxPercent = Convert.ToDouble(doRS.GetFieldVal("SR__SALESTAXPERCENT", clC.SELL_SYSTEM));    // s3
                cTaxAmount = (cWorkT * Convert.ToDecimal(rTaxPercent)) / 100;     // cTaxAmount goes into CUR_SALESTAX
                                                                                  // If the 'Include Tax/Charges' check-box is not checked, do not add tax,
                                                                                  // other charge and shipping to Total. 
                if (Convert.ToInt32(doRS.GetFieldVal("CHK_INCLUDETAXCHARGES", clC.SELL_SYSTEM)) == 1)
                {
                    cOtherCharge = Convert.ToInt32(doRS.GetFieldVal("CUR_OTHERCHARGE", clC.SELL_SYSTEM));   // s4
                    cShipAmount = Convert.ToInt32(doRS.GetFieldVal("CUR_SHIPPING", clC.SELL_SYSTEM));   // s5
                                                                                       // cTotalAmount goes to CUR_TOTAL
                    cTotalAmount = cWork + cWorkT + cTaxAmount + cOtherCharge + cShipAmount;
                }
                else
                // cTotalAmount goes to CUR_TOTAL
                {
                    cTotalAmount = cWork + cWorkT;
                }
            }
            else
            {
                // goP.TraceLine("doRS GetFirst not found", "", sProc)
                doRS = null/* TODO Change to default(_) if this is not a reference type */;
                goErr.SetError(30032, sProc, "", "Quote");
                // The linked [1] can't be updated because it can't be found. 
                // goP.TraceLine("Return False", "", sProc)
                return false;
            }

            // --------------- Update calculated fields ----------------
            doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cWork + cWorkT), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_SUBTOTALT", goTR.RoundCurr(cWorkT), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_SALESTAX", goTR.RoundCurr(cTaxAmount), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_TOTAL", goTR.RoundCurr(cTotalAmount), clC.SELL_SYSTEM);
            // TLD 4/8/2009 Modified to allow for negative numbers
            // TLD 4/3/2009 Added for new fields
            doRS.SetFieldVal("CUR_QTLISTTOTAL", goTR.RoundCurr(cQTListTotal), clC.SELL_SYSTEM);
            if (cQTListTotal == 0)
            // If cQTListTotal - (goTR.RoundCurr(cWork + cWorkT)) <= 0 Then
            {
                doRS.SetFieldVal("SR__DISCOUNT", 0);
            }
            else
            {
                doRS.SetFieldVal("SR__DISCOUNT", ((cQTListTotal - (goTR.RoundCurr(cWork + cWorkT))) / (decimal)cQTListTotal) * 100, clC.SELL_SYSTEM);
            }

            // --------------Update FIL_Inclusions
            if (bUpdInclusions == true)
            {
                doRS.SetFieldVal("FIL_INCLUSIONS", sFiles);
            }

            // --------------- Save the Quote ---------------
            if (bCommit)
            {
                goP.SetVar("bDoNotUpdateQuoteLines", "1");
                // TLD 7/14/2010 Copied update from main
                // CS 11/5/09: Setting a variable here to let me know NOT to try to update the Qt total again in QT_RecOnSave. Issue was that if
                // you open a QT, add a Quote Line and then cancel out of the QT, the QT total did not reflect the actual total. This
                // was because CalcQuotetotal was being called here and then again in QT_RecordOnSave. We do NOT want it to be called in QT
                // RecOnSave if it was called here.
                goP.SetVar("bDoNotRecalcQuoteTotal", "1");
                // Save to disk
                if (doRS.Commit() != 1)
                {
                    // goP.TraceLine("Commit failed, raising error", "", sProc)
                    goP.DeleteVar("bDoNotUpdateQuoteLines");
                    // TLD 7/14/2010 Copied from main CS 11/6/09: Set in Ql_RecordOnSave
                    goP.DeleteVar("bDoNotRecalcQuoteTotal");
                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                    goErr.SetError(35000, sProc, "Error updating the Quote '" + par_s1 + "'."); // CS
                                                                                                // goP.TraceLine("Return False", "", sProc)
                    return false;
                }
                else
                {
                    goP.DeleteVar("bDoNotUpdateQuoteLines");
                    goP.DeleteVar("bDoNotRecalcQuoteTotal"); // TLD 7/14/2010 Copied from main CS 11/6/09
                    if (!(doRS == null))
                        doRS = null/* TODO Change to default(_) if this is not a reference type */;
                }
            }

            goP.SetVar("QL_List", "0");

            return true;
        }

        public bool CalculateCommission(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5:  Unused
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            try
            {

                // TLD 10/3/2008 Calculates Commission Dollars
                // Called from QT_FormControlOnChange_BTN_RECALCCOMM, QT_FormOnSave
                double iCommPerc;
                decimal iQuoteTotal = Convert.ToInt32(doForm.doRS.GetFieldVal("CUR_TOTAL", 2,-1,true,-1,"CR","A_a"));
                decimal iCommDollars;

                // TLD 8/27/2010 Changed to do all calcs here instead of
                // calling 4 times from functions
                // listed above

                // Calc SR__CommPerc (Rep Company)
                iCommPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__CommPerc", 2));
                iCommDollars = Convert.ToDecimal(iCommPerc / 100) * iQuoteTotal;
                doForm.doRS.SetFieldVal("CUR_CommDollars", iCommDollars, 2);

                // Calc SR__AddShipCommPerc (Rep CO 2)
                iCommPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__AddShipCommPerc", 2));
                iCommDollars = Convert.ToDecimal(iCommPerc / 100) * iQuoteTotal;
                doForm.doRS.SetFieldVal("CUR_AddShipCommDollars", iCommDollars, 2);

                // Calc SR__AddBillCommPerc (Rep Company)
                iCommPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__AddBillCommPerc", 2));
                iCommDollars = Convert.ToDecimal(iCommPerc / 100) * iQuoteTotal;
                doForm.doRS.SetFieldVal("CUR_AddBillCommDollars", iCommDollars, 2);

                // Calc SR__EngCommPerc (Rep Company)
                iCommPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__EngCommPerc", 2));
                iCommDollars = Convert.ToDecimal(iCommPerc / 100) * iQuoteTotal;
                doForm.doRS.SetFieldVal("CUR_EngCommDollars", iCommDollars, 2);
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                    goErr.SetError(ex, 45105, sProc);
                    return false;
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FilterFields_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5:  Unused
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm =(Form)par_doCallingObject;

            try
            {
                string sFilter = "";
                // Applications
                sFilter = "ACTIVE=1";
                sFilter = sFilter + Constants.vbCrLf + "C1CONDITION==";
                sFilter = sFilter + Constants.vbCrLf + "C1FIELDNAME=<%MLS_TYPE%>";
                sFilter = sFilter + Constants.vbCrLf + "CCOUNT=1";
                sFilter = sFilter + Constants.vbCrLf + "C1VALUE=0";
                sFilter = sFilter + Constants.vbCrLf + "CONDITION=MLS_TYPE=0";
                sFilter = sFilter + Constants.vbCrLf + "DIRECTION=1";
                sFilter = sFilter + Constants.vbCrLf + "FILE=BI";
                sFilter = sFilter + Constants.vbCrLf + "US_NAME=Linkbox selector filter";
                sFilter = sFilter + Constants.vbCrLf + "SORT=SYS_NAME ASC";
                sFilter = sFilter + Constants.vbCrLf + "SORT1=SYS_NAME";

                doForm.SetFilterINI("LNK_CONNECTED_BI", sFilter);

                // Fluid
                sFilter = "";
                sFilter = "ACTIVE=1";
                sFilter = sFilter + Constants.vbCrLf + "C1CONDITION==";
                sFilter = sFilter + Constants.vbCrLf + "C1FIELDNAME=<%MLS_TYPE%>";
                sFilter = sFilter + Constants.vbCrLf + "CCOUNT=1";
                sFilter = sFilter + Constants.vbCrLf + "C1VALUE=1";
                sFilter = sFilter + Constants.vbCrLf + "CONDITION=MLS_TYPE=1";
                sFilter = sFilter + Constants.vbCrLf + "DIRECTION=1";
                sFilter = sFilter + Constants.vbCrLf + "FILE=BI";
                sFilter = sFilter + Constants.vbCrLf + "US_NAME=Linkbox selector filter";
                sFilter = sFilter + Constants.vbCrLf + "SORT=SYS_NAME ASC";
                sFilter = sFilter + Constants.vbCrLf + "SORT1=SYS_NAME";

                doForm.SetFilterINI("LNK_CONNECTEDF_BI", sFilter);

                // Equipment
                sFilter = "";
                sFilter = "ACTIVE=1";
                sFilter = sFilter + Constants.vbCrLf + "C1CONDITION==";
                sFilter = sFilter + Constants.vbCrLf + "C1FIELDNAME=<%MLS_TYPE%>";
                sFilter = sFilter + Constants.vbCrLf + "CCOUNT=1";
                sFilter = sFilter + Constants.vbCrLf + "C1VALUE=2";
                sFilter = sFilter + Constants.vbCrLf + "CONDITION=MLS_TYPE=2";
                sFilter = sFilter + Constants.vbCrLf + "DIRECTION=1";
                sFilter = sFilter + Constants.vbCrLf + "FILE=BI";
                sFilter = sFilter + Constants.vbCrLf + "US_NAME=Linkbox selector filter";
                sFilter = sFilter + Constants.vbCrLf + "SORT=SYS_NAME ASC";
                sFilter = sFilter + Constants.vbCrLf + "SORT1=SYS_NAME";

                doForm.SetFilterINI("LNK_CONNECTEDE_BI", sFilter);
            }

            catch (Exception ex)
            {
                // do nothing?
                return true;
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormControlOnChange_LNK_REPCONTACT_CN(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections , clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/19/2008 Calls ConnectCompanies to fill LNK_REPIS_CO with LNK_RELATED_CO from LNK_REPCONTACT_CN
            scriptManager.RunScript("ConnectCompanies",ref par_doCallingObject,ref par_oReturn,ref par_bRunNext,ref par_sSections,null,"","","","","");
            par_doCallingObject = doForm;
            return true;
        }

        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 3/28/2013 Moved stuff to CO_FilterFields
            scriptManager.RunScript("CO_FilterFields", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");

            // TLD 6/7/2013 Added same for SR__Multiplier
            // TLD 3/28/2013 Enable/Disable SR__StdDiscPerc
            // based on user perm
            clRowSet doUSRS = new clRowSet("US", 6, "CHK_COStdDiscPercPerm=1 AND GID_ID='" + goP.GetMe("ID") + "'");
            if (Convert.ToInt32(doUSRS.GetFieldVal("BI__Count",0,-1,true,-1,"CR","A_a")) == 1)
            {
                // Enable
                doForm.SetControlState("SR__StdDiscPerc", 0);
                doForm.SetControlState("SR__Multiplier", 0);
            }
            else
            {
                // Disable
                doForm.SetControlState("SR__StdDiscPerc", 4);
                doForm.SetControlState("SR__Multiplier", 4);
            }

            doForm.MoveToTab(12);
            par_doCallingObject = doForm;
            return true;
        }

        public bool MO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm =(Form)par_doCallingObject;

            // TLD 9/22/2010 Check for duplicate name
            // Excluse this one
            var sThisModelName = doForm.doRS.GetFieldVal("TXT_MODELNAME");
            clRowSet doMORS = new clRowSet("MO", 3, "TXT_MODELNAME='" + sThisModelName + "' AND GID_ID<>'" + doForm.doRS.GetCurrentRecID() + "'", null/* Conversion error: Set to default value for this argument */, "TXT_MODELNAME");
            if (doMORS.GetFirst() > 0)
            {
                // Give user message that duplicates exist
                doForm.MessageBox("You can't save this because model " + sThisModelName + " already exists.",0,"Selltis","","","","","","","",null,null,"","","","","","",false);
                return false;
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QL_FormControlOnChange_BTN_CalcDiscTotal(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 10/15/2008 Custom Disc Percent Calculation
            // Calculates SR__DISCPERCENT from unit price and unit price after discount

            decimal cPriceUnit= default(decimal);
            double rQtyFld;
            decimal cPriceUnitAfterDisc = default(decimal);
            decimal cSubtotal = default(decimal);
            decimal cCostVal = default(decimal);
            decimal cWork= default(decimal);
            decimal cDiscPerc = default(decimal);
            decimal cDiscAddAmt = default(decimal);

            cPriceUnit = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_PRICEUNIT", 2));
            cPriceUnitAfterDisc = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_PRICEUNITAFTERDISC", 2));
            rQtyFld = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY", 2));


            // Calculate Disc %
            cDiscPerc = (cPriceUnit * Convert.ToDecimal(rQtyFld) - (cPriceUnitAfterDisc * Convert.ToDecimal(rQtyFld) / (cPriceUnit * Convert.ToDecimal(rQtyFld)))) * 100;
            doForm.doRS.SetFieldVal("SR__DISCPERCENT", cDiscPerc);

            // Calculate Subtotal
            cSubtotal = (cPriceUnit * Convert.ToDecimal(rQtyFld) - (cPriceUnit * Convert.ToDecimal(rQtyFld) * cDiscPerc / 100)) + cDiscAddAmt;
            doForm.doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cSubtotal));

            // Recalculate Gross Profit
            cCostVal = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_COST", 2));
            cWork = cSubtotal - (cCostVal * Convert.ToDecimal(rQtyFld)); // CS 6/13/07: Added rQtyField per DF
            doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));
            par_doCallingObject = doForm;
            return true;
        }

        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.SetControlState("LNK_FOR_PD", 0);
            par_doCallingObject = doForm;
            return true;
        }

        public bool QL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // TLD 7/6/2012
            goP.SetVar("QL_List", Convert.ToInt32(doRS.GetFieldVal("SR__QTY", clC.SELL_SYSTEM)) * Convert.ToInt32(doRS.GetFieldVal("CUR_PRICEUNIT", clC.SELL_SYSTEM)));
            par_doCallingObject = doRS;
            return true;
        }

        public bool QL_FormControlOnChange_LNK_FOR_PD(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sPD;
            sPD = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD", 1));

            string sFilter = "";
            sFilter = "ACTIVE=1";
            sFilter = sFilter + Constants.vbCrLf + "C1CONDITION=1";
            sFilter = sFilter + Constants.vbCrLf + "C1FIELDNAME=<%LNK_FOR_PD%>";
            sFilter = sFilter + Constants.vbCrLf + "CCOUNT=1";
            sFilter = sFilter + Constants.vbCrLf + "CONDITION=LNK_OF_PD=" + sPD;
            sFilter = sFilter + Constants.vbCrLf + "DIRECTION=1";
            sFilter = sFilter + Constants.vbCrLf + "FILE=OP";
            sFilter = sFilter + Constants.vbCrLf + "US_NAME=Linkbox selector filter";
            sFilter = sFilter + Constants.vbCrLf + "SORT=SYS_NAME ASC";
            sFilter = sFilter + Constants.vbCrLf + "SORT1=SYS_NAME";

            doForm.SetFilterINI("LNK_FOR_MO", sFilter);
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FillLinks_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5:  Unused
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            try
            {

                // TLD 6/7/2013 Added SR__Multiplier
                // TLD 4/5/2013 Fill links
                // Called from:
                // QT_FormControlOnChange_BTN_LNKCompany, TLD 9/13/2010 Added to fill territory
                // QT_FormControlOnChange_LNK_To_CO
                // QT_FormOnLoadRecord_Post
                // QT_FormControlOnChange_LNK_OriginatedBy_CN

                clArray doLink;
                doLink = new clArray();
                oTable = null;
                doLink = doForm.doRS.GetLinkVal("LNK_TO_CO",ref doLink,true,0,-1,"A_a",ref oTable);
                clRowSet doCORS = new clRowSet("CO", 3, "GID_ID='" + doLink.GetItem(1) + "'", null/* Conversion error: Set to default value for this argument */, "LNK_REPIS_CO, LNK_IN_TE, LNK_RelatedTo_IU, LNK_TeamLeader_US, SR__StdDiscPerc, SR__Multiplier", 1);
                if (doCORS.GetFirst() == 1)
                {
                    doForm.doRS.SetFieldVal("LNK_RELATED_TE", doCORS.GetFieldVal("LNK_IN_TE", 2), 2);
                    doForm.doRS.SetFieldVal("LNK_REPCOMPANY_CO", doCORS.GetFieldVal("LNK_REPIS_CO", 2), 2);
                    doForm.doRS.SetFieldVal("LNK_Related_IU", doCORS.GetFieldVal("LNK_RelatedTo_IU", 2), 2);
                    doForm.doRS.SetFieldVal("LNK_CreditedTo_US", doCORS.GetFieldVal("LNK_TeamLeader_US", 2), 2);
                    doForm.doRS.SetFieldVal("SR__StdDiscPerc", doCORS.GetFieldVal("SR__StdDiscPerc", 2), 2);
                    doForm.doRS.SetFieldVal("SR__Multiplier", doCORS.GetFieldVal("SR__Multiplier", 2), 2);
                    doCORS = null/* TODO Change to default(_) if this is not a reference type */;
                }
                else
                {
                    doForm.doRS.ClearLinkAll("LNK_Related_TE");
                    doForm.doRS.ClearLinkAll("LNK_REPCOMPANY_CO");
                    doForm.doRS.ClearLinkAll("LNK_Related_IU");
                    // doForm.doRS.ClearLinkAll("LNK_CreditedTo_US") 'don't clear in case filled otherwise?
                    doForm.doRS.SetFieldVal("SR__StdDiscPerc", 0, 2);
                    doForm.doRS.SetFieldVal("SR__Multiplier", 0, 2);
                }
            }

            catch (Exception ex)
            {
            }

            return true;
        }

        public bool QT_FormControlOnChange_LNK_ORIGINATEDBY_CN_Post(ref object par_doCallingObject, ref object par_oReturn , ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 4/5/2013 Moved to QT_FillLinks, added _Post to function
            scriptManager.RunScript("QT_FillLinks", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            // 'TLD 12/13/2011 Fill territory & industry from CN's CO
            // Dim doLink As clArray

            // doLink = New clArray
            // doLink = doForm.doRS.GetLinkVal("LNK_OriginatedBy_CN%%LNK_Related_CO", doLink, , , 1)
            // Dim doCORS As New clRowSet("CO", 3, "GID_ID='" & doLink.GetItem(1) & "'", , "LNK_REPIS_CO, LNK_IN_TE, LNK_RelatedTo_IU", 1)
            // If doCORS.GetFirst() = 1 Then
            // doForm.doRS.SetFieldVal("LNK_RELATED_TE", doCORS.GetFieldVal("LNK_IN_TE", 2), 2)
            // doForm.doRS.SetFieldVal("LNK_Related_IU", doCORS.GetFieldVal("LNK_RelatedTo_IU", 2), 2)
            // doCORS = Nothing
            // Else
            // doForm.doRS.ClearLinkAll("LNK_Related_TE")
            // doForm.doRS.ClearLinkAll("LNK_Related_IU")
            // End If
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_LINKCOMPANY_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 4/5/2013 Moved to QT_FillLinks
            scriptManager.RunScript("QT_FillLinks", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");

            // 'TLD 9/13/2010 Added to fill territory
            // Dim doLink As clArray
            // doLink = New clArray
            // doLink = doForm.doRS.GetLinkVal("LNK_TO_CO", doLink, , , 1)
            // Dim doCORS As New clRowSet("CO", 3, "GID_ID='" & doLink.GetItem(1) & "'", , "LNK_REPIS_CO, LNK_IN_TE, LNK_RelatedTo_IU", 1)
            // If doCORS.GetFirst() = 1 Then
            // doForm.doRS.SetFieldVal("LNK_RELATED_TE", doCORS.GetFieldVal("LNK_IN_TE", 2), 2)
            // doForm.doRS.SetFieldVal("LNK_REPCOMPANY_CO", doCORS.GetFieldVal("LNK_REPIS_CO", 2), 2)
            // 'TLD 12/13/2011 Added to fill LNK_Related_IU
            // doForm.doRS.SetFieldVal("LNK_Related_IU", doCORS.GetFieldVal("LNK_RelatedTo_IU", 2), 2)
            // doCORS = Nothing
            // Else
            // doForm.doRS.ClearLinkAll("LNK_Related_TE")
            // doForm.doRS.ClearLinkAll("LNK_REPCOMPANY_CO")
            // doForm.doRS.ClearLinkAll("LNK_Related_IU")
            // End If
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_RECALCCOMM(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/27/2010 Changed to do all calcs in function
            // TLD 10/7/2008 Calculates Commission Dollars on Order Admin tab
            scriptManager.RunScript("CalculateCommission", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");

            // goScr.RunScript("CalculateCommission", doForm, , , , "SR__AddBillCommPerc", "CUR_TOTAL", "CUR_AddBillCommDollars")

            // goScr.RunScript("CalculateCommission", doForm, , , , "SR__AddShipCommPerc", "CUR_TOTAL", "CUR_AddShipCommDollars")

            // goScr.RunScript("CalculateCommission", doForm, , , , "SR__CommPerc", "CUR_TOTAL", "CUR_CommDollars")

            // goScr.RunScript("CalculateCommission", doForm, , , , "SR__EngCommPerc", "CUR_TOTAL", "CUR_EngCommDollars")
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_LNK_REPCN2_CN(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/19/2008 Calls ConnectCompanies to fill LNK_REPCO2_CO with LNK_RELATED_CO from LNK_REPCN2_CN
            if (doForm.doRS.GetLinkCount("LNK_REPCN2_CN") != 0)
            {
                scriptManager.RunScript("ConnectCompanies", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            }
            else
            {
                doForm.doRS.ClearLinkAll("LNK_REPCO2_CO");
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_LNK_REPCN3_CN(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/19/2008 Calls ConnectCompanies to fill LNK_REPCO3_CO with LNK_RELATED_CO from LNK_REPCN3_CN
            if (doForm.doRS.GetLinkCount("LNK_REPCN3_CN") != 0)
            {
                scriptManager.RunScript("ConnectCompanies", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            }
            else
            {
                doForm.doRS.ClearLinkAll("LNK_REPCO3_CO");
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_LNK_REPCN4_CN(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/19/2008 Calls ConnectCompanies to fill LNK_REPCO4_CO with LNK_RELATED_CO from LNK_REPCN4_CN
            if (doForm.doRS.GetLinkCount("LNK_REPCN4_CN") != 0)
            {
                scriptManager.RunScript("ConnectCompanies", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            }
            else
            {
                doForm.doRS.ClearLinkAll("LNK_REPCO4_CO");
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_LNK_REPCONTACT_CN(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/19/2008 Calls ConnectCompanies to fill LNK_REPCOMPANY_CO with LNK_RELATED_CO from LNK_REPCONTACT_CN
            if (doForm.doRS.GetLinkCount("LNK_REPCONTACT_CN") != 0)
            {
                scriptManager.RunScript("ConnectCompanies", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            }
            else
            {
                doForm.doRS.ClearLinkAll("LNK_REPCOMPANY_CO");
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_LNK_TO_CO_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // Dim doLink As clArray

            // TLD 4/5/2013 Moved to QT_FillLinks
            scriptManager.RunScript("QT_FillLinks", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            // 'TLD 3/28/2013 Added SR__StdDiscPerc
            // 'TLD 3/22/2012 Added to fill Credited to User
            // 'TLD 9/13/2010 Added to fill industry
            // doLink = New clArray
            // doLink = doForm.doRS.GetLinkVal("LNK_TO_CO", doLink, , , 1)
            // 'Dim doCORS As New clRowSet("CO", 3, "GID_ID='" & doLink.GetItem(1) & "'", , "LNK_REPIS_CO, LNK_IN_TE, LNK_RelatedTo_IU", 1)
            // 'Dim doCORS As New clRowSet("CO", 3, "GID_ID='" & doLink.GetItem(1) & "'", , _
            // '    "LNK_REPIS_CO, LNK_IN_TE, LNK_RelatedTo_IU, LNK_TeamLeader_US", 1)
            // Dim doCORS As New clRowSet("CO", 3, "GID_ID='" & doLink.GetItem(1) & "'", , _
            // "LNK_REPIS_CO, LNK_IN_TE, LNK_RelatedTo_IU, LNK_TeamLeader_US, SR__StdDiscPerc", 1)

            // If doCORS.GetFirst() = 1 Then
            // doForm.doRS.SetFieldVal("LNK_RELATED_TE", doCORS.GetFieldVal("LNK_IN_TE", 2), 2)
            // doForm.doRS.SetFieldVal("LNK_REPCOMPANY_CO", doCORS.GetFieldVal("LNK_REPIS_CO", 2), 2)
            // 'TLD 12/13/2011 Added to fill LNK_Related_IU
            // doForm.doRS.SetFieldVal("LNK_Related_IU", doCORS.GetFieldVal("LNK_RelatedTo_IU", 2), 2)
            // 'TLD 3/22/2012 Added to fill LNK_CreditedTo_US
            // doForm.doRS.SetFieldVal("LNK_CreditedTo_US", doCORS.GetFieldVal("LNK_TeamLeader_US", 2), 2)
            // 'TLD 3/28/2013 Added SR__StdDiscPerc
            // doForm.doRS.SetFieldVal("SR__StdDiscPerc", doCORS.GetFieldVal("SR__StdDiscPerc", 2), 2)
            // doCORS = Nothing
            // Else
            // doForm.doRS.ClearLinkAll("LNK_Related_TE")
            // doForm.doRS.ClearLinkAll("LNK_REPCOMPANY_CO")
            // doForm.doRS.ClearLinkAll("LNK_Related_IU")
            // 'TLD 3/22/2012 Added to clear Credited to User if CO is blank
            // doForm.doRS.ClearLinkAll("LNK_CreditedTo_US")
            // End If
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }
            // -----------------Control states
            // TLD 10/6/2008 Set Commission Dollar fields on Admin tab to gray.
            doForm.SetControlState("CUR_CommDollars", 4);
            doForm.SetControlState("CUR_AddShipCommDollars", 4);
            doForm.SetControlState("CUR_AddBillCommDollars", 4);
            doForm.SetControlState("CUR_EngCommDollars", 4);

            // TLD 4/8/2009 Set Quote List Total and Quote Discoutn fields to locked
            doForm.SetControlState("SR__Discount", 1); // edit locked
            doForm.SetControlState("CUR_QTLISTTOTAL", 1); // edit locked

            // TLD 3/28/2013 Nobody edits Standard Disc %
            doForm.SetControlState("SR__StdDiscPerc", 4);
            // TLD 6/7/2013 Nobody edits Multiplier
            doForm.SetControlState("SR__Multiplier", 4);
            // -----------------End Control states

            // TLD 4/5/2013 Moved to QT_FillLinks
            if (doForm.GetMode() == "CREATION")
                scriptManager.RunScript("QT_FillLinks", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            // 'TLD 12/13/2011 Fill Industry and Territory from company if New
            // If doForm.GetMode() = "CREATION" Then
            // Dim doLink As clArray

            // doLink = New clArray
            // doLink = doForm.doRS.GetLinkVal("LNK_To_CO", doLink, , , 1)
            // Dim doCORS As New clRowSet("CO", 3, "GID_ID='" & doLink.GetItem(1) & "'", , "LNK_REPIS_CO, LNK_IN_TE, LNK_RelatedTo_IU", 1)
            // If doCORS.GetFirst() = 1 Then
            // doForm.doRS.SetFieldVal("LNK_RELATED_TE", doCORS.GetFieldVal("LNK_IN_TE", 2), 2)
            // doForm.doRS.SetFieldVal("LNK_Related_IU", doCORS.GetFieldVal("LNK_RelatedTo_IU", 2), 2)
            // doCORS = Nothing
            // Else
            // doForm.doRS.ClearLinkAll("LNK_Related_TE")
            // doForm.doRS.ClearLinkAll("LNK_Related_IU")
            // End If
            // End If
            par_doCallingObject = doForm;
            return true;
        }
        public bool Quote_FillAddress_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //goP.TraceLine("", "", sProc)

            //PURPOSE:
            //		Fill the address field, first checking whether it is empty.
            //RETURNS:
            //		True

            string sContactName = "";
            string sMailingAddr = null;
            string sFirstName = null;
            string sLastName = null;
            //Dim sCompName as string
            string sAddrMail = null;
            string sCityMail = null;
            string sStateMail = null;
            string sZipMail = null;
            string sCountryMail = null;
            string scompany = null;

            //VS 03262018 TKT#2151 : Refill data whenever Contact has changed even if not empty.
            //if (!string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_ADDRESSMAILING").ToString()))
            //    return true;
            if (doForm.doRS.GetLinkCount("LNK_ORIGINATEDBY_CN") < 1)
                return true;

            //CS 6/22/09: Create CN rowset to get CN fields
            clRowSet doRSContact = new clRowSet("CN", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN") + "'", "", "LNK_RELATED_CO,TXT_NAMEFIRST,TXT_NAMELAST,TXT_ADDRBUSINESS,TXT_CITYBUSINESS,TXT_STATEBUSINESS,TXT_ZIPBUSINESS,TXT_COUNTRYBUSINESS");
            if (doRSContact.GetFirst() == 1)
            {
                scompany = Convert.ToString(doRSContact.GetFieldVal("LNK_RELATED_CO%%TXT_COMPANYNAME"));
                sFirstName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMEFIRST"));
                sLastName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMELAST"));


                if (!string.IsNullOrEmpty(sFirstName))
                {
                    sContactName = sFirstName + " ";
                }
                sContactName += sLastName;

                sAddrMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ADDRBUSINESS"));
                sCityMail = Convert.ToString(doRSContact.GetFieldVal("TXT_CITYBUSINESS"));
                sStateMail = Convert.ToString(doRSContact.GetFieldVal("TXT_STATEBUSINESS"));
                sZipMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ZIPBUSINESS"));
                sCountryMail = Convert.ToString(doRSContact.GetFieldVal("TXT_COUNTRYBUSINESS"));

                //Start building the mailing address
                //sMailingAddr = scompany;
                //if (!string.IsNullOrEmpty(scompany))
                //{
                //    sMailingAddr = sMailingAddr + Environment.NewLine + scompany;
                //}
                //sMailingAddr = scompany;
                sMailingAddr = sContactName;
                if (!sAddrMail.Contains(scompany))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + scompany;
                }
                if (!string.IsNullOrEmpty(sAddrMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sAddrMail;
                }
                if (!string.IsNullOrEmpty(sCityMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sCityMail;
                }
                if (!string.IsNullOrEmpty(sStateMail))
                {
                    sMailingAddr = sMailingAddr + ", " + sStateMail;
                }
                if (!string.IsNullOrEmpty(sZipMail))
                {
                    sMailingAddr = sMailingAddr + " " + sZipMail;
                }
                if (!string.IsNullOrEmpty(sCountryMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sCountryMail;
                }
                doForm.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailingAddr);
                doForm.doRS.SetFieldVal("MMO_ADDRMAILING", sMailingAddr);
            }


            par_bRunNext = false;
            par_doCallingObject = doForm;
            return true;

        }

        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 8/27/2010 Changed to do all calcs in called function
            // TLD 10/7/2008 Calculates Commission Dollars on Order Admin tab in the event commissions were changed
            // but recalc was not clicked
            scriptManager.RunScript("CalculateCommission", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");

            // goScr.RunScript("CalculateCommission", doForm, , , , "SR__AddBillCommPerc", "CUR_TOTAL", "CUR_AddBillCommDollars")

            // goScr.RunScript("CalculateCommission", doForm, , , , "SR__AddShipCommPerc", "CUR_TOTAL", "CUR_AddShipCommDollars")

            // goScr.RunScript("CalculateCommission", doForm, , , , "SR__CommPerc", "CUR_TOTAL", "CUR_CommDollars")

            // goScr.RunScript("CalculateCommission", doForm, , , , "SR__EngCommPerc", "CUR_TOTAL", "CUR_EngCommDollars")
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // TLD 3/28/2013 Just in case not filled elsewhere?
            if (doRS.IsLinkEmpty("LNK_To_CO") != true)
            {
                // TLD 6/7/2013 Added RS
                clRowSet doCORS = new clRowSet("CO", 3, "GID_ID='" + doRS.GetFieldVal("LNK_To_CO") + "'", null/* Conversion error: Set to default value for this argument */, "SR__StdDiscPerc, SR__Multiplier", 1);
                if (doCORS.GetFirst()==1)
                {
                    // doRS.SetFieldVal("SR__StdDiscPerc", doRS.GetFieldVal("LNK_To_CO%%SR__StdDiscPerc"))
                    doRS.SetFieldVal("SR__StdDiscPerc", doRS.GetFieldVal("SR__StdDiscPerc"));
                    doRS.SetFieldVal("SR__StdDiscPerc", doRS.GetFieldVal("SR__Multiplier"));
                    doCORS = null/* TODO Change to default(_) if this is not a reference type */;
                }
            }
            par_doCallingObject = doRS;
            return true;
        }

        public bool XR_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sPOP = goMeta.PageRead(goP.GetMe("ID"), "POP_PERSONAL_OPTIONS","",false,"",false);

            // TLD 3/22/2012 for default Quote Type Option
            if (goTR.StrRead(sPOP, "QUOTE_CUSTYPE") != "")
            {
                // Read values from MD and set in the form fields
                // Label is written to MD, so need to find value for NDB field
                clList goList = new clList();
                // Get label written to pop
                string sLabel = goTR.StrRead(sPOP, "QUOTE_CUSTYPE");
                // Check to see if the POP Type field still has a value for that label
                string sType = goList.LReadSeek("XR:TYPE", "VALUE", sLabel);
                // Set NDB Field to POP MD if no error
                if (sType != "" & goErr.GetLastError("NUMBER") != "E30035")
                {
                    doForm.SetControlVal("NDB_MLS_Type", sType);
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool XR_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 3/22/2012 Prevent main from running
            par_bRunNext = false;
          
            Form doForm = (Form)par_doCallingObject;
            string sPOP = goMeta.PageRead(goP.GetMe("ID"), "POP_PERSONAL_OPTIONS","",false,"",false);

            // Write values to MD
            // We use this to set Type on QT to POP, but can't write label of
            // NDB field to POP and retrieve to set it correctly
            // So, need to grab number from ndb field, then get label
            // to write to POP
            clList goList = new clList();
            // Get selected type as string value
            string sType = doForm.GetControlVal("NDB_MLS_Type");
            // Get label for that type to save to MD
            string sLabel = goList.LReadSeek("XR:TYPE", "KEY", sType);
            // If no error, then save that label to MD
            if (sLabel != "" & goErr.GetLastError("NUMBER") != "E30035")
                goTR.StrWrite(ref sPOP, "QUOTE_CUSTYPE", sLabel);

            // Write to Product XX
            goMeta.PageWrite(goP.GetMe("ID"), "POP_PERSONAL_OPTIONS", sPOP, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "XX");

            doForm.CloseOnReturn = true;
            doForm.CancelSave();
            par_doCallingObject = doForm;
            return true;
        }

        public bool GenerateSysName_Post(ref object par_doCallingObject, ref string par_oReturn , bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "" )
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sFileName = doRS.GetFileName();
            string sResult = "";
            // Dim doLink As clRowSet
            // Dim iLen As Integer

            // We assume that sFileName is valid. If this is a problem, test it here and SetError.

            switch (Strings.UCase(sFileName))
            {
                case "BI":
                    {
                        if (!doRS.IsLoaded("TXT_BIOGRAPHYNAME"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".TXT_BIOGRAPHYNAME");
                        }
                            sResult = Convert.ToString(doRS.GetFieldVal("TXT_BIOGRAPHYNAME"));
                            sResult = Strings.Replace(sResult, Constants.vbCrLf, " ");
                            sResult = Strings.Replace(sResult, Constants.vbTab, " ");
                            par_oReturn = sResult;
                            break;
                        
                    }
            }
            par_doCallingObject = doRS;
            return true;
        }

        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext , ref string par_sSections , clArray par_doArray = null, string par_sFileName = "", string par_sReverseDirection = "0", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here.
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }

        public bool RHTESTSCRIPT(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sDate = "", string par_stime = "", string par_sInterval = "", string par_s4 = "", string par_s5 = "" )
        {

            // rh test script for toolbar automater and alerts
            ClUI goUI = new ClUI();
            //goUI.Initialize();

            goUI.AddAlert("Process billing reports", clC.SELL_ALT_OPENDESKTOP, "DSK_2005042614562492255MAR 00002XX", clC.SELL_USER_ALL);
            goUI.AddAlert("New Message from 'Rick'", clC.SELL_ALT_OPENRECORD, "c0023d42-55b3-473d-4d53-9752008b6c04", clC.SELL_USER_ALL);

            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_MoveQuoteLine_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            par_bRunNext = false;

            string sDirection = "";
            string sLtGt = "";
            string sFirstLast = "";
            string sMoveSortDir = "";

            string sRecID = "";
            string sQuoteID = "";
            string sSelLineNO = "";
            string sMoveLineNO = "";
            double dSelLineNO = 0;
            double dMoveLineNO = 0;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot edit Quote Lines.");
                return true;
            }

            if (par_s3.ToUpper() == "UP")
            {
                sDirection = "up";
                sLtGt = "<";
                sFirstLast = "first";
                sMoveSortDir = "desc";
            }
            else if (par_s3.ToUpper() == "DOWN")
            {
                sDirection = "down";
                sLtGt = ">";
                sFirstLast = "last";
                sMoveSortDir = "asc";
            }
            else
            {
                return false;
            }

            sQuoteID = doForm.doRS.GetFieldVal("GID_ID").ToString();
            sRecID = doForm.GetLinkSelection("LNK_Connected_QL");

            if (string.IsNullOrEmpty(sRecID))
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }

            clRowSet doQLSelRS = new clRowSet("QL", clC.SELL_EDIT, "GID_ID=" + sRecID, "", "*,**", 1, par_bBypassValidation: true);
            if (doQLSelRS.GetFirst() == 0)
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }
            sSelLineNO = doQLSelRS.GetFieldVal("SR__LineNo").ToString();
            dSelLineNO = Convert.ToDouble(doQLSelRS.GetFieldVal("SR__LineNo"));

            clRowSet doQLMoveRS = new clRowSet("QL", clC.SELL_EDIT, ("LNK_IN_QT=" + sQuoteID + " AND SR__LineNo" + sLtGt + sSelLineNO), "SR__LineNo " + sMoveSortDir, "*,**", 1, par_bBypassValidation: true);
            if (doQLMoveRS.GetFirst() == 0)
            {
                doForm.MessageBox("This is the " + sFirstLast + " Qoute Line. Please select another Qoute Line to move " + sDirection + ".");
                par_doCallingObject = doForm;
                return false;
            }
            sMoveLineNO = doQLMoveRS.GetFieldVal("SR__LineNo").ToString();
            dMoveLineNO = Convert.ToDouble(doQLMoveRS.GetFieldVal("SR__LineNo"));

            doQLSelRS.SetFieldVal("SR__LineNo", dMoveLineNO, 2);
            doQLMoveRS.SetFieldVal("SR__LineNo", dSelLineNO, 2);

            if (doQLSelRS.Commit() == 0 || doQLMoveRS.Commit() == 0)
            {
                doForm.MessageBox("Could not save the Quote Lines. Please reopen the quote and try again.");
                par_doCallingObject = doForm;
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");
            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_READONLY, "LNK_IN_QT='" + sGidId + "' And LNK_FOR_MO%%BI__ID<1 ", "LNK_IN_QT", "LNK_FOR_MO");

            if ((rsQL.GetFirst() == 1))
            {
                goErr.SetWarning(35000, sProc, "Please fill Model before saving the Quote  ");
                doForm.FieldInFocus = "LNK_FOR_MO";
                par_doCallingObject = doForm;
                return false;
            }

            return true;
        }
        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote_standard_draft.docx";
                }
                else if (sQTTemplate == "Service Word")
                {
                    return "cus_corr_ms word_quote_draft.docx";
                }
            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote_standard.docx";
                }
                else if (sQTTemplate == "Service Word")
                {
                    return "cus_corr_ms word_quote.docx";
                }
            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }

            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);
                    }

                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));
            string sModelText = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%MMO_DESCRIPTION"));


            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            // rsQL.SetFieldVal("CUR_Cost", curCost);
            rsQL.SetFieldVal("TXT_MODEL", sModelText);

            rsQL.SetFieldVal("SR__Qty", dQty);
            rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);

            //rsQL.SetFieldVal("TXT_Model", sModelText);      
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }
        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
            }

        }

        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }

        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "LNK_IN_OP", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if (rsOL1.GetFirst() == 1)
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

            }
            //convert to quote button
            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {

                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm = (Form)par_doCallingObject;
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "LNK_IN_OP", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if (rsOL1.GetFirst() == 1)
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

            }
            doForm.doRS.SetFieldVal("DTT_OppDate", "Today|Now");
            ClearLineFields(doForm);
            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;


            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "LNK_IN_OP", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if (rsOL1.GetFirst() == 1)
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                doRS.SetFieldVal("CUR_VALUE", curValue);
                doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

            }
            par_doCallingObject = doRS;
            par_bRunNext= false;    
            return true;
        }

        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string Mo_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));


            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_RELATED_PD,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);
            rsOL.SetFieldVal("LNK_FOR_MO", Mo_Gid);

            iLineCount = iLineCount + 1;

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "LNK_IN_OP", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            
            if (rsOL1.GetFirst() == 1)
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);

            //This will generate line no's in mobile.
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }

            par_doCallingObject = doRS;

            return true;
        }

        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
       {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLinesName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }
            par_doCallingObject = doForm;
            par_bRunNext = false;
            return true;
        }
        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            
            return true;

        }

        public bool OL_RecordBeforeDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.oVar.SetVar("OppID", doRS.GetFieldVal("TXT_OpportunityLinesName"));

            par_doCallingObject = doRS;

            return true;
        }

        public bool OL_RecordAfterDelete_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_s2 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sID = doRS.oVar.GetVar("OppID").ToString();

            if (!string.IsNullOrEmpty(sID))
            {
                clRowSet doOPLines = default(clRowSet);

                //Create a rowset of quote lines linked to the deleted quote.
                doOPLines = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo,Gid_id", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    StringBuilder sbQuery = new StringBuilder();
                    for (int i = 1; i <= doOPLines.Count(); i++)
                    {
                        double iLineno = Convert.ToDouble(doOPLines.GetFieldVal("SR__LineNo"));
                        string sGid_id = Convert.ToString(doOPLines.GetFieldVal("Gid_ID"));
                        string sTemp = "#" + goTR.Pad(i.ToString(), 6, " ", "L", true, "R");
                        sbQuery.AppendLine("Update OL Set SR__LineNo='" + i.ToString() + "',sys_name='" + sTemp + "' where Gid_id='" + sGid_id + "' AND SR__LineNo = '" + iLineno.ToString() + "'");

                        if (doOPLines.GetNext() == 0)
                        {
                            break;
                        }

                    }
                    if (!string.IsNullOrEmpty(sbQuery.ToString()))
                    {
                        bool bretval = goData.RunSQLQuery(sbQuery.ToString());
                    }

                }

                doOPLines = null;

            }

            par_doCallingObject = doRS;

            return true;
        }

        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }



            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));
            doFormQT.doRS.SetFieldVal("LNK_FOR_MO", rsOP.GetFieldVal("LNK_FORLINE_MO"));
            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_ENDUSER_CO", rsOP.GetFieldVal("LNK_ENGINEERING_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_DISTREP_CO", rsOP.GetFieldVal("LNK_DISTRIBUTOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECOMPLETED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
                rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("Cur_UnitPrice", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }
        public bool OP_FormControlOnChange_BTN_CALCPROBABILITY_1_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            Refresh_OPPTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            par_doCallingObject = doForm;
            return true;

        }
        private static void Refresh_OPPTotal(clRowSet doForm)
        {
            string sGidId = Convert.ToString(doForm.GetFieldVal("Gid_id"));

            clRowSet rsOL = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + sGidId + "' ", "LNK_IN_OP", "CUR_VALUE|SUM");

            double curTotalAmt = 0.0;

            if ((rsOL.GetFirst() == 1))
            {

                curTotalAmt = Convert.ToDouble(rsOL.GetFieldVal("CUR_VALUE|SUM", 2));


                doForm.SetFieldVal("CUR_VALUE", curTotalAmt, 2);
                //doForm.SetFieldVal("CUR_TOTAL", curTotalAmt, 2);

            }
            else
            {
                doForm.SetFieldVal("CUR_VALUE", 0.0);
                //doForm.SetFieldVal("CUR_TOTAL", 0.0);

            }


        }

        public bool QT_FormControlOnChange_BTN_RECALC_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

             Refresh_QouteTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            par_doCallingObject = doForm;
            return true;

        }
        private static void Refresh_QouteTotal(clRowSet doQuote)
        {
            string sGidId = Convert.ToString(doQuote.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_GROUPBY, "LNK_IN_QT='" + sGidId + "' ", "LNK_IN_QT", "CUR_SUBTOTAL|SUM");

            double curTotalAmt = 0.0;

            if ((rsQL.GetFirst() == 1))
            {

                curTotalAmt = Convert.ToDouble(rsQL.GetFieldVal("CUR_SUBTOTAL|SUM", 2));


                doQuote.SetFieldVal("CUR_SUBTOTAL", curTotalAmt, 2);
                doQuote.SetFieldVal("CUR_TOTAL", curTotalAmt, 2);

            }
            else
            {
                doQuote.SetFieldVal("CUR_SUBTOTAL", 0.0);
                doQuote.SetFieldVal("CUR_TOTAL", 0.0);

            }


        }
    }
}
