﻿using Selltis.Core;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace Selltis.MVC.Controllers
{
    public class WhatsNewController : Controller
    {
        // GET: WhatsNew
        public ActionResult WhatsNew()
        {
            if (Util.GetSessionValue("DesktopId") != null)
            {
                if (Util.GetSessionValue("DesktopId").ToString() != string.Empty)
                {
                    ViewBag.SelectedDesktopID = Util.GetSessionValue("DesktopId");
                    ViewBag.FolderId = Util.GetSessionValue("FolderID");
                    ViewBag.HistoryKey = Util.GetSessionValue("LastDesktopHistoryKey");
                }
            }

            if (Util.GetSessionValue("SiteSettings") == null)
            {
                return RedirectToAction("LoginSubmit", "Login");
            }

            if (Util.GetSessionValue("DesktopId") != null)
            {
                if (Util.GetSessionValue("DesktopId").ToString() != string.Empty)
                {
                    ViewBag.SelectedDesktopID = Util.GetSessionValue("DesktopId");
                }
                else
                {
                    RedirectToAction("Logout", "Login");
                }
            }
            else
            {
                RedirectToAction("Logout", "Login");
            }

            ViewBag.SiteId = ((DataTable)Util.GetSessionValue("SiteSettings")).Rows[0]["SiteId"].ToString();

            return View("~/Views/WhatsNew/_WhatsNew.cshtml");
        }

        public ActionResult ShowInProcess()
        {
            if (Util.GetSessionValue("DesktopId") != null)
            {
                if (Util.GetSessionValue("DesktopId").ToString() != string.Empty)
                {
                    ViewBag.SelectedDesktopID = Util.GetSessionValue("DesktopId");
                    ViewBag.FolderId = Util.GetSessionValue("FolderID");
                }
            }
            return View("~/Views/WhatsNew/_ShowInProcess.cshtml");
        }

        public ActionResult Cus_WhatsNewCustom()
        {
            if (Util.GetSessionValue("DesktopId") != null)
            {
                if (Util.GetSessionValue("DesktopId").ToString() != string.Empty)
                {
                    ViewBag.SelectedDesktopID = Util.GetSessionValue("DesktopId");
                    ViewBag.FolderId = Util.GetSessionValue("FolderID");
                }
            }

            return View("~/Views/WhatsNew/_cus_WhatsNewCustom.cshtml");
        }

        public ActionResult WhatsNewArchive()
        {
            if (Util.GetSessionValue("DesktopId") != null)
            {
                if (Util.GetSessionValue("DesktopId").ToString() != string.Empty)
                {
                    ViewBag.SelectedDesktopID = Util.GetSessionValue("DesktopId");
                    ViewBag.FolderId = Util.GetSessionValue("FolderID");
                }
            }

            return View("~/Views/WhatsNew/WhatsNew_Archive.cshtml");
        }

        public ActionResult WhatsNew_2007()
        {
            if (Util.GetSessionValue("DesktopId") != null)
            {
                if (Util.GetSessionValue("DesktopId").ToString() != string.Empty)
                {
                    ViewBag.SelectedDesktopID = Util.GetSessionValue("DesktopId");
                    ViewBag.FolderId = Util.GetSessionValue("FolderID");
                }
            }

            return View("~/Views/WhatsNew/WhatsNew_2007.cshtml");
        }


        public ActionResult LoadLagacyWhatsNew()
        {
            if (Util.GetSessionValue("DesktopId") != null)
            {
                if (Util.GetSessionValue("DesktopId").ToString() != string.Empty)
                {
                    ViewBag.SelectedDesktopID = Util.GetSessionValue("DesktopId");
                    ViewBag.FolderId = Util.GetSessionValue("FolderID");
                    ViewBag.HistoryKey = Util.GetSessionValue("LastDesktopHistoryKey");
                }
            }

            return View("~/Views/WhatsNew/LoadLagacyWhatsNew.cshtml");
        }
    }
}