﻿'OWNER RH


Imports Microsoft.VisualBasic
Imports MailBee.AddressCheck
Imports MailBee.ImapMail
Imports MailBee.Mime
Imports System.Data
Imports System.IO
Imports MailBee
Imports System
Imports System.Web





Public Class clWebMail

    Dim sMailBeeLicense As String = "MN110-D21AE5AB1BB11AAA1A6F90390211-94CA" ''"MN800-****************************-FF98"

    Dim oIMAP As New Imap(sMailBeeLicense)
    Dim oSMTP As New MailBee.SmtpMail.Smtp(sMailBeeLicense)

    Public bAccountOrLoginError As Boolean = False
    Public sAccountOrLoginErrorMessage As String = ""
    Public sSentFolder As String = ""
    Public sDeletedFolder As String = ""

    Dim goData As clData
    Dim goErr As clError



    Public Enum convTo
        KB = 1
        MB = 2
        GB = 3
        TB = 4
    End Enum

    Public Function GetSetting(ByVal sSetting As String) As String

        'AUTHOR: RH
        '
        'PURPOSE:
        '		Returns the value of POP settings used by class
        'PARAMETERS:
        '		sSetting: name of the setting to return from POP
        'RETURNS:
        '		setting value


        Dim sReturn As String = ""
        sSetting = LCase(sSetting)

        Dim oMeta As clMetaData = HttpContext.Current.Session("goMeta")
        Dim goP As clProject = HttpContext.Current.Session("goP")

        Select Case sSetting


            Case "webmail_from_name"
                sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "WEBMAIL_FROM_NAME", "")
            Case "webmail_from_address"
                sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "WEBMAIL_FROM_ADDRESS", "")
            Case "smtp_server"
                sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SMTP_SERVER", "")
            Case "smtp_server_port"
                sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SMTP_SERVER_PORT", "587")
            Case "smtp_user"
                sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SMTP_USER", "")
            Case "smtp_password"
                sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SMTP_PASSWORD", "")
                Dim oCrypt As New clEncrypt2
                sReturn = oCrypt.Decrypt(sReturn)
            Case "imap_server"
                sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "IMAP_SERVER", "")
            Case "imap_server_port"
                sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "IMAP_SERVER_PORT", "143")
            Case "imap_user"
                sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "IMAP_USER", "")
            Case "imap_password"
                sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "IMAP_PASSWORD", "")
                Dim oCrypt2 As New clEncrypt2
                sReturn = oCrypt2.Decrypt(sReturn)
            Case "smtp_server_requires_authentication"
                sReturn = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SMTP_Server_Requires_Authentication", "1")
        End Select

        Return sReturn

    End Function

    Function ValidateAddresses(ByVal sAddresses As String) As String


        'AUTHOR: RH
        '
        'PURPOSE:
        '		Tests validity of email addresses
        '		
        'PARAMETERS:
        '		sAddresses: comma or semicolon delimited string of email addresses to validate
        'RETURNS:
        '		Returns message string and invalid addresses


        Dim valid As New EmailAddressValidator(sMailBeeLicense)

        ' To perform DNS MX lookup queries, we need some DNS servers for that.
        valid.DnsServers.Autodetect()

        sAddresses = Replace(sAddresses, ";", ",")

        Dim aAddresses() As String = Split(sAddresses, ",")

        Dim result As AddressValidationLevel
        Dim sDefaultMessage As String = "One or more addresses are invalid:" & vbCrLf

        Dim sReturnMessage As String = ""

        valid.ValidationLevel = AddressValidationLevel.RegexCheck

        For Each addr In aAddresses

            result = valid.Verify(Trim(addr))

            Select Case result

                Case AddressValidationLevel.OK

                    'do nothing

                Case AddressValidationLevel.RegexCheck

                    sReturnMessage = sReturnMessage & vbCrLf & addr

            End Select

        Next

        If sReturnMessage <> "" Then
            Return sDefaultMessage & sReturnMessage
        Else
            Return ""

        End If


    End Function

    Public Function BytesTO(ByVal lBytes As Double, ByVal convertto As convTo) As Double
        BytesTO = lBytes / (1024 ^ convertto)
    End Function

    Function GetMail(ByVal sFolder As String, ByVal iFirstItem As Integer, ByVal iNumberOfItems As Integer, Optional ByVal sSearch As String = "") As DataTable


        Dim imp As New Imap()
        Dim dt As New DataTable
        bAccountOrLoginError = False
        sAccountOrLoginErrorMessage = ""

        Try
            If imp.Connect(GetSetting("imap_server"), GetSetting("imap_server_port")) = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" & GetSetting("imap_server") & "). No further details are available."
                imp.Disconnect()
                Return dt
            End If
            If imp.Login(GetSetting("imap_user"), GetSetting("imap_password")) = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" & GetSetting("imap_server") & "). No further details are available."
                imp.Disconnect()
                Return dt
            End If

        Catch ex As Exception
            bAccountOrLoginError = True
            sAccountOrLoginErrorMessage = ex.Message
            Return dt
        End Try

        'Try

        Dim msgs As New EnvelopeCollection 'MailMessageCollection
            Dim uids As UidCollection = Nothing

            dt.Columns.Add("HasAttachment", System.Type.GetType("System.Boolean"))
            dt.Columns.Add("AttachmentList")
            dt.Columns.Add("From")
            dt.Columns.Add("To")
            dt.Columns.Add("CC")
            dt.Columns.Add("BCC")
            dt.Columns.Add("Subject")
            dt.Columns.Add("DateReceived", System.Type.GetType("System.DateTime"))
            dt.Columns.Add("IsRead", System.Type.GetType("System.Boolean"))
            dt.Columns.Add("Size")
            dt.Columns.Add("UID")
            dt.Columns.Add("MID")

            Dim dr As DataRow

            Try
                If imp.SelectFolder(sFolder) <> True Then
                    imp.Disconnect()
                    Return dt
                End If
            Catch ex As Exception
                Return dt
            End Try

            Dim intFrom As Integer = (imp.MessageCount + 1) - iFirstItem
            Dim intTo As Integer = (intFrom - iNumberOfItems) + 1
            If intTo < 1 Then intTo = 1

            If imp.MessageCount = 0 Then
                Return dt
            Else
                If sSearch = "" Then
                    msgs = imp.DownloadEnvelopes(intTo & ":" & intFrom, False, EnvelopeParts.MailBeeEnvelope Or EnvelopeParts.BodyStructure, 80, Nothing, Nothing)
                Else

                    Try
                        uids = CType(imp.Search(True, sSearch, Nothing), UidCollection)
                        msgs = imp.DownloadEnvelopes(uids.ToString, True, EnvelopeParts.MailBeeEnvelope Or EnvelopeParts.BodyStructure, 80, Nothing, Nothing)
                    Catch ex As Exception
                        Return dt
                    End Try
                End If


            End If
            msgs.Reverse()

            Dim env As MailBee.ImapMail.Envelope
            Dim bSeen As Boolean

            For Each env In msgs

                dr = dt.NewRow

                Dim cCol As Collection = ConvertAddressStringToCollection(env.To.ToString())
                Dim sString As String = ConvertAddressCollectionToString(cCol)

                dr("HasAttachment") = TestForAttachments(env, True)
                dr("AttachmentList") = TestForAttachments(env, False)
                dr("From") = HttpUtility.HtmlEncode(env.From.ToString())
                dr("To") = HttpUtility.HtmlEncode(env.To.ToString())
                dr("CC") = HttpUtility.HtmlEncode(env.Cc.ToString())
                dr("BCC") = HttpUtility.HtmlEncode(env.Bcc.ToString())
                dr("Subject") = env.Subject.ToString()
                dr("DateReceived") = env.DateReceived

                If InStr(LCase(env.Flags.ToString), "\seen") > 0 Then bSeen = True Else bSeen = False
                dr("IsRead") = bSeen
                dr("Size") = Math.Round(BytesTO(env.Size, convTo.KB), 1)
                dr("UID") = env.Uid.ToString()
                dr("MID") = getSHA1Hash(env.MessageID)

                dt.Rows.Add(dr)

            Next env

            'Catch ex As Exception
            '    'xxx raise error
            'End Try

            imp.Disconnect()

        Return dt


    End Function
    Function GetCount(ByVal sFolder As String) As Integer

        Dim imp As New Imap()
        Dim iCount As Integer = 0
        bAccountOrLoginError = False
        sAccountOrLoginErrorMessage = ""

        Try
            If imp.Connect(GetSetting("imap_server"), GetSetting("imap_server_port")) = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" & GetSetting("imap_server") & "). No further details are available."
                imp.Disconnect()
                Return iCount
            End If
            If imp.Login(GetSetting("imap_user"), GetSetting("imap_password")) = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" & GetSetting("imap_server") & "). No further details are available."
                imp.Disconnect()
                Return iCount
            End If

        Catch ex As Exception
            bAccountOrLoginError = True
            sAccountOrLoginErrorMessage = ex.Message
            Return iCount
        End Try

        Try
            If imp.SelectFolder(sFolder) <> True Then
                imp.Disconnect()
                Return iCount
            End If
        Catch ex As Exception
            Return iCount
        End Try
        iCount = imp.MessageCount
        imp.Disconnect()

        Return iCount

    End Function

    Public Function GetUnReadCount() As Integer




        Dim imp As New Imap()
        Dim uids As UidCollection = Nothing
        bAccountOrLoginError = False
        sAccountOrLoginErrorMessage = ""

        Try
            If imp.Connect(GetSetting("imap_server"), GetSetting("imap_server_port")) = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" & GetSetting("imap_server") & "). No further details are available."
                imp.Disconnect()
                Return 0
            End If
            If imp.Login(GetSetting("imap_user"), GetSetting("imap_password")) = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" & GetSetting("imap_server") & "). No further details are available."
                imp.Disconnect()
                Return 0
            End If

        Catch ex As Exception
            bAccountOrLoginError = True
            sAccountOrLoginErrorMessage = ex.Message
            Return 0
        End Try

        Try
            If imp.SelectFolder("INBOX") <> True Then
                imp.Disconnect()
                Return 0
            End If
        Catch ex As Exception
            Return 0
        End Try



        uids = CType(imp.Search(True, "UNSEEN", Nothing), UidCollection)

        Return uids.Count



    End Function


    Public Function SetSeenFlag(ByVal sFolder As String, ByVal sUIDs As String) As Boolean

        'sUIDs=comma delimited string of UIDs to mark as seen


        Dim imp As New Imap()
        imp.Connect(GetSetting("imap_server"), GetSetting("imap_server_port"))
        imp.Login(GetSetting("imap_user"), GetSetting("imap_password"))
        Try
            If imp.SelectFolder(sFolder) <> True Then
                Return False
            End If
        Catch ex As Exception
            Return False
        End Try
        Dim bResult As Boolean = imp.SetMessageFlags(sUIDs, True, SystemMessageFlags.Seen, MessageFlagAction.Add)
        imp.Disconnect()

        Return bResult






    End Function

    Public Function SendMail(ByVal oMailItem As SellMailItem) As Boolean



        Try

            Dim sTestString As String = ""

            If ConvertAddressCollectionToString(oMailItem.c_To) <> "" Then
                sTestString = ConvertAddressCollectionToString(oMailItem.c_To)
            End If
            If ConvertAddressCollectionToString(oMailItem.c_CC) <> "" Then
                sTestString = sTestString & "," & ConvertAddressCollectionToString(oMailItem.c_CC)
            End If
            If ConvertAddressCollectionToString(oMailItem.c_BCC) <> "" Then
                sTestString = sTestString & "," & ConvertAddressCollectionToString(oMailItem.c_BCC)
            End If

            Dim sValidation As String = ValidateAddresses(sTestString)

            If sValidation <> "" Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = sValidation
                Return False
            End If

            If GetSetting("smtp_server") = "" Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "You do not have an SMTP server setting.  Please configure Webmail Settings in your Personal Options."
                Return False
            End If

            If GetSetting("smtp_server_requires_authentication") = 1 Then

                If GetSetting("smtp_user") = "" Then
                    bAccountOrLoginError = True
                    sAccountOrLoginErrorMessage = "You do not have an SMTP user setting.  Please configure Webmail Settings in your Personal Options."
                    Return False
                End If

                If GetSetting("smtp_password") = "" Then
                    bAccountOrLoginError = True
                    sAccountOrLoginErrorMessage = "You do not have an SMTP password setting.  Please configure Webmail Settings in your Personal Options."
                    Return False
                End If
            End If

            Dim oMessage As New MailBee.Mime.MailMessage
            oMessage.BodyHtmlText = oMailItem.s_HTMLBody

            oMessage.Builder.RelatedFilesFolder = System.Web.HttpContext.Current.Server.MapPath("")
            oMessage.ImportRelatedFiles(ImportRelatedFilesOptions.None)

            Dim mailer As MailBee.SmtpMail.Smtp = New MailBee.SmtpMail.Smtp

            mailer.SmtpServers.Add(GetSetting("smtp_server"), GetSetting("smtp_user"), GetSetting("smtp_password"))
            mailer.SmtpServers.Item(0).Port = GetSetting("smtp_server_port")
            If mailer.Connect() = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to connect to your SMTP server (" & GetSetting("smtp_server") & "). No further details are available."
                Return False
            End If
            mailer.Hello()
            If GetSetting("smtp_server_requires_authentication") = 1 Then
                mailer.Login()
            End If
            Dim oFrom As New MailBee.Mime.EmailAddress
            oFrom.Email = GetSetting("webmail_from_address")
            oFrom.DisplayName = GetSetting("webmail_from_name")
            oMessage.From = oFrom
            oMessage.To.AsString = ConvertAddressCollectionToString(oMailItem.c_To)
            oMessage.Cc.AsString = ConvertAddressCollectionToString(oMailItem.c_CC)
            oMessage.Bcc.AsString = ConvertAddressCollectionToString(oMailItem.c_BCC)
            oMessage.Subject = oMailItem.s_Subject
            mailer.Message = oMessage
            For Each Attch In oMailItem.c_Attachments
                mailer.AddAttachment(Attch)
            Next
            mailer.Send()


            '****************************************************
            'PUT COPY IN SENT FOLDER

            Dim imp As New Imap()

            bAccountOrLoginError = False
            sAccountOrLoginErrorMessage = ""

            Try
                If imp.Connect(GetSetting("imap_server"), GetSetting("imap_server_port")) = False Then
                    bAccountOrLoginError = True
                    sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" & GetSetting("imap_server") & "). No further details are available."
                    Return False
                End If
                If imp.Login(GetSetting("imap_user"), GetSetting("imap_password")) = False Then
                    bAccountOrLoginError = True
                    sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" & GetSetting("imap_server") & "). No further details are available."
                    Return False
                End If

            Catch ex As Exception
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = ex.Message
                Return False
            End Try

            GetFolders()
            imp.UploadMessage(mailer.Message, sSentFolder, SystemMessageFlags.Seen Or SystemMessageFlags.Answered)

            imp.Disconnect()

            '****************************************************

            mailer.Disconnect()

            Return True


        Catch ex As Exception
            bAccountOrLoginError = True
            sAccountOrLoginErrorMessage = ex.Message & vbCrLf & vbCrLf & "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator."
            Return False
        End Try

    End Function

    Public Function DeleteMessages(ByVal sFolder As String, ByVal sUIDs As String) As Boolean

        'sUIDs=comma delimited string of UIDs to mark as seen
        Dim imp As New Imap()

        If sDeletedFolder = "" Then
            GetFolders()
        End If

        bAccountOrLoginError = False
        sAccountOrLoginErrorMessage = ""

        Try
            If imp.Connect(GetSetting("imap_server"), GetSetting("imap_server_port")) = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" & GetSetting("imap_server") & "). No further details are available." & vbCrLf & vbCrLf & "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator."
                imp.Disconnect()
                Return False
            End If
            If imp.Login(GetSetting("imap_user"), GetSetting("imap_password")) = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" & GetSetting("imap_server") & "). No further details are available." & vbCrLf & vbCrLf & "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator."
                imp.Disconnect()
                Return False
            End If

        Catch ex As Exception
            bAccountOrLoginError = True
            sAccountOrLoginErrorMessage = ex.Message
            imp.Disconnect()
            Return False
        End Try

        Try
            If imp.SelectFolder(sFolder) <> True Then
                Return False
            End If
        Catch ex As Exception
            Return False
        End Try

        Dim bResult As Boolean

        If sDeletedFolder = "" Or sFolder = sDeletedFolder Then
            bResult = imp.DeleteMessages(sUIDs, True)
            imp.Expunge()
        Else
            bResult = imp.MoveMessages(sUIDs, True, sDeletedFolder)
        End If

        imp.Disconnect()

        Return bResult


    End Function

    Public Function MoveMessages(ByVal sSourceFolder As String, ByVal sTargetFolder As String, ByVal sUIDs As String) As Boolean

        Dim imp As New Imap()

        bAccountOrLoginError = False
        sAccountOrLoginErrorMessage = ""

        Try
            If imp.Connect(GetSetting("imap_server"), GetSetting("imap_server_port")) = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" & GetSetting("imap_server") & "). No further details are available." & vbCrLf & vbCrLf & "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator."
                imp.Disconnect()
                Return False
            End If
            If imp.Login(GetSetting("imap_user"), GetSetting("imap_password")) = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" & GetSetting("imap_server") & "). No further details are available." & vbCrLf & vbCrLf & "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator."
                imp.Disconnect()
                Return False
            End If

        Catch ex As Exception
            bAccountOrLoginError = True
            sAccountOrLoginErrorMessage = ex.Message
            imp.Disconnect()
            Return False
        End Try

        Try
            If imp.SelectFolder(sSourceFolder) <> True Then
                imp.Disconnect()
                Return False
            End If
        Catch ex As Exception
            Return False
        End Try

        Dim bResult As Boolean

        bResult = imp.MoveMessages(sUIDs, True, sTargetFolder)
        imp.Disconnect()
        Return bResult


    End Function

    Public Function GetFolders() As DataTable

        Dim imp As New Imap()
        Dim fc As FolderCollection
        Dim dt As New DataTable

        bAccountOrLoginError = False
        sAccountOrLoginErrorMessage = ""

        imp.UseXList = True

        If GetSetting("imap_server") = "" Then
            bAccountOrLoginError = True
            sAccountOrLoginErrorMessage = "You do not have an IMAP server setting.  Please configure Webmail Settings in your Personal Options."
            Return dt
        End If

        If GetSetting("imap_user") = "" Then
            bAccountOrLoginError = True
            sAccountOrLoginErrorMessage = "You do not have an IMAP user setting.  Please configure Webmail Settings in your Personal Options."
            Return dt
        End If

        If GetSetting("imap_password") = "" Then
            bAccountOrLoginError = True
            sAccountOrLoginErrorMessage = "You do not have an IMAP user password setting.  Please configure Webmail Settings in your Personal Options."
            Return dt
        End If

        Try
            If imp.Connect(GetSetting("imap_server"), GetSetting("imap_server_port")) = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" & GetSetting("imap_server") & "). No further details are available." & vbCrLf & vbCrLf & "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator."
                Return dt
            End If
            If imp.Login(GetSetting("imap_user"), GetSetting("imap_password")) = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" & GetSetting("imap_server") & "). No further details are available." & vbCrLf & vbCrLf & "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator."
                Return dt
            End If

        Catch ex As Exception
            bAccountOrLoginError = True
            sAccountOrLoginErrorMessage = ex.Message & vbCrLf & vbCrLf & "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator."
            Return dt
        End Try



        Try
            fc = imp.DownloadFolders(False, Nothing, Nothing)
        Catch ex As Exception
            Try
                imp.UseXList = False
                fc = imp.DownloadFolders(False, Nothing, Nothing)
            Catch ex2 As Exception
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = ex2.Message & vbCrLf & vbCrLf & "You may configure and test your Webmail Settings in Personal Options. If you continue to experience errors, contact your Selltis Administrator."
                Return dt

            End Try


        End Try

        If fc.Count = 0 Then


            imp.UseXList = False

            'Try
            fc = imp.DownloadFolders(False, Nothing, Nothing)
            'Catch ex As Exception

            'End Try


        End If



        dt.Columns.Add("name")
        dt.Columns.Add("short")
        dt.Columns.Add("level")
        dt.Columns.Add("delim")
        dt.DefaultView.Sort = "level"

        For Each fold As MailBee.ImapMail.Folder In fc

            Dim r As DataRow = dt.NewRow
            r("name") = fold.Name
            r("short") = fold.ShortName
            r("level") = fold.NestingLevel
            r("delim") = fold.Delimiter
            dt.Rows.Add(r)

            If fold.Flags = FolderFlags.Sent Then
                sSentFolder = fold.Name
            End If

            If fold.Flags = FolderFlags.Trash Then
                sDeletedFolder = fold.Name
            End If


        Next

        Dim dataView As New DataView(dt)
        dataView.Sort = "level asc"
        Dim dataTable As DataTable = dataView.ToTable()

        imp.Disconnect()

        If sSentFolder = "" Then
            For Each row In dataTable.Rows
                If InStr(LCase(row("name")), "sent") > 0 Then
                    sSentFolder = row("name")
                    Exit For
                End If
            Next

        End If

        If sDeletedFolder = "" Then
            For Each row In dataTable.Rows
                If InStr(LCase(row("name")), "delete") > 0 Then
                    sDeletedFolder = row("name")
                    Exit For
                End If
            Next
        End If

        If sDeletedFolder = "" Then
            For Each row In dataTable.Rows
                If InStr(LCase(row("name")), "trash") > 0 Then
                    sDeletedFolder = row("name")
                    Exit For
                End If
            Next
        End If

        Return dataTable

    End Function

    Public Function GetSearchString(ByVal sTo As String, ByVal sFrom As String, ByVal sSubject As String, ByVal sBody As String) As String

        Dim sParams As New ArrayList
        Dim sReturn As String = ""


        If sTo <> "" Then
            sParams.Add("TO " + ImapUtils.ToQuotedString(sTo))
        End If
        If sFrom <> "" Then
            sParams.Add("FROM " + ImapUtils.ToQuotedString(sFrom))
        End If
        If sSubject <> "" Then
            sParams.Add("SUBJECT " + ImapUtils.ToQuotedString(sSubject))
        End If
        If sBody <> "" Then
            sParams.Add("BODY " + ImapUtils.ToQuotedString(sBody))
        End If

        sReturn = ImapUtils.AllOf("TO " + ImapUtils.ToQuotedString(sTo), "FROM " + ImapUtils.ToQuotedString(sFrom), "SUBJECT " + ImapUtils.ToQuotedString(sSubject), "BODY " + ImapUtils.ToQuotedString(sBody))
        sReturn = Replace(sReturn, "TO " & Chr(34) & Chr(34), "")
        sReturn = Replace(sReturn, "FROM " & Chr(34) & Chr(34), "")
        sReturn = Replace(sReturn, "SUBJECT " & Chr(34) & Chr(34), "")
        sReturn = Replace(sReturn, "BODY " & Chr(34) & Chr(34), "")
        sReturn = Replace(sReturn, "(", "")
        sReturn = Replace(sReturn, ")", "")
        sReturn = Trim(sReturn)
        sReturn = "(" & sReturn & ")"

        Return sReturn



    End Function

    Public Function DownloadMsgViaImap(ByVal sUID As String, ByVal sFolder As String, Optional ByVal sMID As String = "") As SellMailItem


        Dim uid As String = sUID
        Dim partID As String = "2"
        Dim ofolder As String = sFolder
        Dim url As String

        Dim imp As New MailBee.ImapMail.Imap()
        Dim msg As New MailBee.Mime.MailMessage
        Dim bAlreadyDownloaded As Boolean = False

        If sMID <> "" Then

            Dim sEMLFile As String = System.Web.HttpContext.Current.Server.MapPath("../Temp/Webmail/") & sMID & "\msg.eml"

            If My.Computer.FileSystem.FileExists(sEMLFile) Then
                Dim msgBytes As Byte() = Nothing

                Dim br As BinaryReader
                Try
                    br = New BinaryReader(File.OpenRead(sEMLFile))
                    Dim fi As New FileInfo(sEMLFile)
                    msgBytes = br.ReadBytes(fi.Length)
                Finally
                    If Not br Is Nothing Then
                        br.Close()
                    End If
                End Try


                msg.LoadMessage(msgBytes)
                bAlreadyDownloaded = True
            End If

        End If

        'xxx ERROR HANDLE SECTION
        If bAlreadyDownloaded = False Then
            If GetSetting("imap_server_port") = "0" Then
                imp.Connect(GetSetting("imap_server"))
            Else
                imp.Connect(GetSetting("imap_server"), GetSetting("imap_server_port"))
            End If
            imp.Login(GetSetting("imap_user"), GetSetting("imap_password"))
            imp.SelectFolder(ofolder)
            msg = imp.DownloadEntireMessage(uid, True)
            imp.Disconnect()

        End If


        Dim sPrivateDirectory As String = ""
        Dim htmlBody As String = ""

        If msg IsNot Nothing Then

            msg.Parser.PlainToHtmlMode = PlainToHtmlAutoConvert.IfNoHtml
            msg.Parser.WorkingFolder = System.Web.HttpContext.Current.Server.MapPath("../Temp/Webmail")

            sPrivateDirectory = System.Web.HttpContext.Current.Server.MapPath("../Temp/Webmail/") & UCase(getSHA1Hash(msg.MessageID))


            If bAlreadyDownloaded = False Then
                htmlBody = msg.GetHtmlAndSaveRelatedFiles("..", VirtualMappingType.[Static], MessageFolderBehavior.CreateOnly)

                Dim s As String = "<base target=""_blank"" />"

                Dim sBody As String = htmlBody
                Dim iBody As Integer = sBody.IndexOf("<head")

                If iBody < 0 Then
                    sBody = s & sBody
                Else
                    Dim iBodyEnd As Integer = sBody.IndexOf(">", iBody)
                    sBody = sBody.Insert(iBodyEnd + 1, s)
                End If

                htmlBody = sBody



                My.Computer.FileSystem.CreateDirectory(sPrivateDirectory)
                My.Computer.FileSystem.WriteAllText(sPrivateDirectory & "\message.html", htmlBody, False)
                msg.Attachments.SaveAll(sPrivateDirectory, True)
            Else
                htmlBody = My.Computer.FileSystem.ReadAllText(sPrivateDirectory & "\message.html")
            End If

            url = "../Temp/Webmail/" & UCase(getSHA1Hash(msg.MessageID)) & "/message.html"

        Else
            url = "Inbox is empty or the requested e-mail is no longer on the server (may happen with Gmail/POP3)"
        End If

        If bAlreadyDownloaded = False Then
            msg.SaveMessage(System.Web.HttpContext.Current.Server.MapPath("../Temp/Webmail/") & UCase(getSHA1Hash(msg.MessageID)) & "/msg.eml")
        End If

        Dim oSellMailItem As New SellMailItem

        Dim sTOs As String = ""
        Dim sCCs As String = ""
        Dim sBCCs As String = ""

        For Each sTO In msg.To
            sTOs = sTOs & sTO.Email & ","
        Next

        If Len(sTOs) > 0 Then
            sTOs = Left(sTOs, Len(sTOs) - 1)
        End If

        For Each sCC In msg.Cc
            sCCs = sCCs & sCC.Email & ","
        Next

        If Len(sCCs) > 0 Then
            sCCs = Left(sCCs, Len(sCCs) - 1)
        End If

        For Each sBCC In msg.Bcc
            sBCCs = sBCCs & sBCC.Email & ","
        Next

        If Len(sBCCs) > 0 Then
            sBCCs = Left(sBCCs, Len(sBCCs) - 1)
        End If



        oSellMailItem.c_To = ConvertAddressStringToCollection(sTOs)
        oSellMailItem.c_CC = ConvertAddressStringToCollection(sCCs)
        oSellMailItem.c_BCC = ConvertAddressStringToCollection(sBCCs)
        oSellMailItem.s_From = msg.From.Email
        oSellMailItem.s_Subject = msg.Subject
        If htmlBody <> "" Then
            oSellMailItem.s_HTMLBody = htmlBody
        Else
            oSellMailItem.s_HTMLBody = msg.BodyPlainText
        End If

        oSellMailItem.s_TextBody = msg.BodyPlainText

        oSellMailItem.c_Attachments = ConvertAddressStringToCollection(GetAttachmentNamesFromMsg(msg.Attachments))
        oSellMailItem.s_UID = msg.UidOnServer
        oSellMailItem.s_MID = getSHA1Hash(msg.MessageID)
        oSellMailItem.s_URL = url
        If msg.DateReceived = DateTime.MinValue Then
            oSellMailItem.dt_DateReceived = msg.Date
        Else
            oSellMailItem.dt_DateReceived = msg.DateReceived
        End If

        If msg.DateSent = DateTime.MinValue Then
            oSellMailItem.dt_DateSent = msg.Date
        Else
            oSellMailItem.dt_DateSent = msg.DateSent
        End If


        'SetSeenFlag(sFolder, sUID)


        Return oSellMailItem

    End Function

    Private Function LogMessage(ByRef oMessage As SellMailItem, ByVal bIsSent As Boolean) As Boolean


        Dim table As New DataTable()

        Dim column As New DataColumn("ID", GetType(String))
        table.Columns.Add(column)
        column = New DataColumn("Type", GetType(String))
        table.Columns.Add(column)
        column = New DataColumn("From", GetType(String))
        table.Columns.Add(column)
        column = New DataColumn("To", GetType(String))
        table.Columns.Add(column)
        column = New DataColumn("CC", GetType(String))
        table.Columns.Add(column)
        column = New DataColumn("BCC", GetType(String))
        table.Columns.Add(column)
        column = New DataColumn("Subject", GetType(String))
        table.Columns.Add(column)
        column = New DataColumn("Body", GetType(String))
        table.Columns.Add(column)
        column = New DataColumn("Attachments", GetType(String))
        table.Columns.Add(column)
        column = New DataColumn("Date", GetType(String))
        table.Columns.Add(column)
        column = New DataColumn("Time", GetType(String))
        table.Columns.Add(column)
        column = New DataColumn("Importance", GetType(String))
        table.Columns.Add(column)
        Dim ds As New DataSet()
        ds.Tables.Add(table)

        Dim row As DataRow = table.NewRow()
        If bIsSent = True Then
            row("Type") = "Sent"
        Else
            row("Type") = "Received"
        End If

        If oMessage.s_MID <> "" Then
            row("ID") = oMessage.s_MID
        Else
            row("ID") = oMessage.s_UID
        End If

        row("From") = oMessage.s_From
        row("To") = ConvertAddressCollectionToString(oMessage.c_To)
        row("CC") = ConvertAddressCollectionToString(oMessage.c_CC)
        row("BCC") = ConvertAddressCollectionToString(oMessage.c_BCC)
        row("Subject") = oMessage.s_Subject
        row("Body") = oMessage.s_TextBody
        row("Importance") = ""
        row("Attachments") = ConvertAddressCollectionToString(oMessage.c_Attachments)
        If bIsSent Then
            row("Date") = ConvertToSellDate(CDate(oMessage.dt_DateSent), "Date")
            row("Time") = ConvertToSellDate(CDate(oMessage.dt_DateSent), "Time")
        Else
            row("Date") = ConvertToSellDate(CDate(oMessage.dt_DateReceived), "Date")
            row("Time") = ConvertToSellDate(CDate(oMessage.dt_DateReceived), "Time")
        End If

        table.TableName = "email"
        table.Rows.Add(row)

        Dim oMail As New clEmail
        If oMail.LogMessage(table) = "1" Then Return True Else Return False


    End Function

    Public Function GetUnloggedIDs(ByVal sFolder As String, ByVal sIDs As String) As String
        'sFolder = Folder name
        'sIDs =  Comma delimited list of IDs or "ALL" to log all

        Dim colIDsToLog As New Collection

        GetFolders()

        If sIDs = "ALL" Then

            Dim dt As New DataTable
            dt = GetMail(sFolder, 1, 10000)

            Dim row As DataRow
            For Each row In dt.Rows

                If TestIfLogged(row("UID")) = False Then
                    colIDsToLog.Add(row("UID"))
                End If

            Next

        End If

        If sIDs <> "ALL" Then

            Dim aIDs() As String
            aIDs = Split(sIDs, ",")

            Dim i As Integer
            For i = aIDs.GetLowerBound(0) To aIDs.GetUpperBound(0)
                If TestIfLogged(aIDs(i)) = False Then
                    colIDsToLog.Add(aIDs(i))
                End If
            Next
        End If

        Return ConvertAddressCollectionToString(colIDsToLog)

    End Function

    Public Function LogMessageByID(ByVal sFolder As String, ByVal mailID As String, ByVal bIsSent As Boolean) As Boolean

        If LogMessage(DownloadMsgViaImap(mailID, sFolder), bIsSent) = True Then
            AddLoggedID(mailID)
            Return True
        Else
            Return False
        End If


    End Function

    Public Function LogMessages(ByVal sFolder As String, ByVal sIDs As String, ByVal sStatusMDPage As String) As Boolean

        'sFolder = Folder name
        'sIDs =  Comma delimited list of IDs or "ALL" to log all
        'sStatusMDPage = page of md for status.. 
        '       two properties: 
        '        status=initiated,evaluating, logging, completed, or error
        '        message= message to be displayed.  
        '       md is initially created by calling process with status set to initiated

        Dim colIDsToLog As New Collection

        GetFolders()

        Dim goP As clProject
        Dim goMeta As clMetaData

        goP = HttpContext.Current.Session("goP")
        goMeta = HttpContext.Current.Session("goMeta")

        'xxx SetLoggingStatus evaluating
        goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "STATUS", "evaluating")
        goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "MESSAGE", "Identifying unlogged email.")

        If sIDs = "ALL" Then

            Dim dt As New DataTable
            dt = GetMail(sFolder, 1, 10000)

            Dim row As DataRow
            For Each row In dt.Rows

                If TestIfLogged(row("UID")) = False Then
                    colIDsToLog.Add(row("UID"))
                End If

            Next

        End If

        If sIDs <> "ALL" Then

            Dim aIDs() As String
            aIDs = Split(sIDs, ",")

            Dim i As Integer
            For i = aIDs.GetLowerBound(0) To aIDs.GetUpperBound(0)
                If TestIfLogged(aIDs(i)) = False Then
                    colIDsToLog.Add(aIDs(i))
                End If
            Next
        End If

        goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "STATUS", "evaluating")
        goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "MESSAGE", colIDsToLog.Count & " emails to log.")


        Dim x As Integer = 1

        For Each mailID As String In colIDsToLog

            goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "STATUS", "logging")
            goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "MESSAGE", "Logging " & x & " of " & colIDsToLog.Count & " emails.")

            If sFolder = sSentFolder Then
                LogMessage(DownloadMsgViaImap(mailID, sFolder), True)
            Else
                LogMessage(DownloadMsgViaImap(mailID, sFolder), False)
            End If

            AddLoggedID(mailID)

        Next

        goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "STATUS", "completed")
        If colIDsToLog.Count = 0 Then
            goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "MESSAGE", "All mail items were previously logged.")
        Else
            goMeta.LineWrite(goP.GetUserTID(), sStatusMDPage, "MESSAGE", colIDsToLog.Count & " emails logged")
        End If



    End Function

    Private Function ConvertToSellDate(ByVal dtDate As DateTime, ByVal sDorT As String) As String
        Dim str As String
        Try
            Dim str2 As String = ""
            str2 = Strings.Format(Convert.ToDateTime(dtDate), "yyyy-MM-dd HH:mm:ss.fff")
            If sDorT = "Date" Then
                str2 = Me.FromTo(str2, 1L, 10L)
            Else
                str2 = Me.FromTo(str2, 12L, -1L)
            End If
            str = str2
        Catch exception1 As System.Exception
            str = ""
            Return str
        End Try

        Return str
    End Function

    Private Function FromTo(ByVal par_sString As String, ByVal par_lStart As Long, Optional ByVal par_lEnd As Long = -1L) As String
        Dim str As String = par_sString
        Dim num3 As Long = par_lStart
        Dim num As Long = par_lEnd
        If num3 < 1L Then
            num3 = 1L
        End If
        If num = -1L Then
            Return Strings.Mid(str, CInt(num3))
        End If
        If num < num3 Then
            Return ""
        End If
        If num = num3 Then
            Return Strings.Mid(str, CInt(num3), 1)
        End If
        Dim num2 As Long = num - num3
        Return Strings.Mid(str, CInt(num3), CInt(num2 + 1L))
    End Function

    Public Function GetFileSize(ByVal sFile As String) As Long

        Try
            Dim info2 As New FileInfo(sFile)
            Dim length2 As Long = info2.Length
            Return Math.Round(BytesTO(length2, convTo.KB), 1)
        Catch ex As Exception
            Return 0
        End Try




    End Function

    Function TestIfLogged(ByVal sID As String) As Boolean
        'MI 3/3/07 Added call to GenSQLForAddingNNLinks.

        'PURPOSE:
        '		Checks whether an email ID has already been logged
        'PARAMETERS:
        '		sID: UID of email        
        'RETURNS:
        '		True if successful, false or error if not.
        'AUTHOR: RH


        Dim sProc As String = "clWebMail::TestIfLogged"
        Dim goP As clProject
        goData = HttpContext.Current.Session("goData")
        goErr = HttpContext.Current.Session("goErr")
        goP = HttpContext.Current.Session("goP")

        'Try
        Dim sqlConnection1 As SqlClient.SqlConnection

            sqlConnection1 = goData.GetConnection

            Dim cmd As New System.Data.SqlClient.SqlCommand

            cmd.CommandText = "SELECT * FROM EmailLogged WHERE TXT_EmailID='" & sID & "' AND GID_UserID='" & goP.GetUserTID & "'"

            cmd.CommandType = System.Data.CommandType.Text
            cmd.Connection = sqlConnection1
            cmd.CommandTimeout = 300

            If cmd.ExecuteScalar() <> 0 Then
                sqlConnection1.Close()
                Return True
            Else
                sqlConnection1.Close()
                Return False
            End If


        'Catch ex As Exception

        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45100, sProc)
        '    End If

        'End Try
    End Function

    Function AddLoggedID(ByVal sUID As String) As Boolean

        Dim sProc As String = "clWebMail::TestIfLogged"
        Dim goP As clProject
        goData = HttpContext.Current.Session("goData")
        goErr = HttpContext.Current.Session("goErr")
        goP = HttpContext.Current.Session("goP")


        Dim sqlConnection1 As SqlClient.SqlConnection

        sqlConnection1 = goData.GetConnection

        Dim cmd As New System.Data.SqlClient.SqlCommand

        'Try
        cmd.CommandText = "pEmailLoggedAdd"
            cmd.CommandType = System.Data.CommandType.StoredProcedure
            cmd.Connection = sqlConnection1

            Dim uSection As New System.Data.SqlClient.SqlParameter("@par_uUserID", SqlDbType.UniqueIdentifier)
            uSection.Value = StringToGuid(goP.GetUserTID())
            cmd.Parameters.Add(uSection)

            Dim strUID As New System.Data.SqlClient.SqlParameter("@par_sEmailID", SqlDbType.NVarChar)
            strUID.Value = sUID
            cmd.Parameters.Add(strUID)
            If cmd.ExecuteNonQuery() = 1 Then
                Return True
            End If

        'Catch ex1 As Exception

        '    If Not ex1.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex1, 45100, sProc)
        '    End If
        'End Try



    End Function

    Private Function StringToGuid(ByVal sValue As String) As System.Guid
        Dim guidValue As System.Guid = CType(System.ComponentModel.TypeDescriptor.GetConverter(guidValue).ConvertFrom(sValue), System.Guid)
        Return guidValue
    End Function

    Function GetAttachmentNamesFromMsg(ByVal ac As MailBee.Mime.AttachmentCollection) As String

        Dim sReturn As String = ""

        For Each attach As Attachment In ac
            sReturn = sReturn & attach.Name & ", "
        Next

        If Len(sReturn) > 2 Then
            sReturn = Left(sReturn, Len(sReturn) - 2)
        Else
            sReturn = ""
        End If

        Return sReturn

    End Function

    Function getSHA1Hash(ByVal strToHash As String) As String

        Dim sha1Obj As New System.Security.Cryptography.SHA1CryptoServiceProvider
        Dim bytesToHash() As Byte = System.Text.Encoding.ASCII.GetBytes(strToHash)

        bytesToHash = sha1Obj.ComputeHash(bytesToHash)

        Dim strResult As String = ""

        For Each b As Byte In bytesToHash
            strResult += b.ToString("x2")
        Next

        Return strResult

    End Function

    Function TestForAttachments(ByRef env As MailBee.ImapMail.Envelope, ByVal AsBoolean As Boolean) As String

        If env.IsValid Then

            Dim strBuffer As New System.Text.StringBuilder
            Dim parts As ImapBodyStructureCollection = env.BodyStructure.GetAllParts()

            For Each part As ImapBodyStructure In parts
                ' Detect if this part is attachment.
                If (Not part.Disposition Is Nothing AndAlso part.Disposition.ToLower() = "attachment") OrElse _
                    (Not part.Filename Is Nothing AndAlso part.Filename <> String.Empty) OrElse _
                    (Not part.ContentType Is Nothing AndAlso part.ContentType.ToLower() = "message/rfc822") Then

                    Dim filename As String

                    If (Not part.Filename Is Nothing) Then
                        filename = part.Filename
                    Else
                        filename = "untitled"
                    End If

                    strBuffer.Append("[" & filename & "]")
                End If
            Next

            If strBuffer.Length > 0 Then
                If AsBoolean Then
                    Return "True"
                Else
                    Return strBuffer.ToString
                End If
            Else
                If AsBoolean Then
                    Return "False"
                Else
                    Return ""
                End If

            End If
        End If

        If AsBoolean = True Then
            Return "False"
        Else
            Return ""
        End If


    End Function

    Public Function ConvertAddressCollectionToString(ByVal cAddresses As Collection) As String

        Dim sReturn As String = ""
        If cAddresses Is Nothing Then Return ""

        For Each Addr In cAddresses
            sReturn = sReturn & Addr & ", "
        Next

        If Len(sReturn) > 2 Then
            Return Left(sReturn, Len(sReturn) - 2)
        Else
            Return ""
        End If

    End Function

    Public Function TestIMAPSettings(ByVal sServer As String, ByVal sPort As String, ByVal sUser As String, ByVal sPassword As String) As Boolean


        'Test IMAP settings
        'Returns True or False
        'If False error message is in clWebmail::sAccountOrLoginErrorMessage
        Try

            Dim oCrypt As New clEncrypt2

            Dim imp As New Imap()

            If sServer = "" Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "You do not have an IMAP server setting."
                Return False
            End If

            If sUser = "" Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "You do not have an IMAP user setting."
                Return False
            End If

            If sPassword <> "" Then
                sPassword = oCrypt.Decrypt(sPassword)
            End If

            If sPassword = "" Then

                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "You do not have an IMAP user password setting."
                Return False
            End If


            If imp.Connect(sServer, sPort) = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to connect to your IMAP server (" & sServer & "). No further details are available."
                Return False
            End If
            If imp.Login(sUser, sPassword) = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to login to your IMAP server (" & sServer & "). No further details are available."
                Return False
            End If

        Catch ex As Exception
            bAccountOrLoginError = True
            sAccountOrLoginErrorMessage = ex.Message
            Return False
        End Try

        Return True


    End Function

    Public Function TestSMTPSettings(ByVal sServer As String, ByVal sPort As String, ByVal sUser As String, ByVal sPassword As String, ByVal sAuthentication As String, Optional ByVal bAuthenticationRequired As Boolean = True) As Boolean

        'Test IMAP settings
        'Returns True or False
        'If False error message is in clWebmail::sAccountOrLoginErrorMessage

        If sServer = "" Then
            bAccountOrLoginError = True
            sAccountOrLoginErrorMessage = "You must first provide a server setting to test."
            Return False
        End If

        If bAuthenticationRequired = True Then

            If sUser = "" Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "You must first provide a user setting to test."
                Return False
            End If

            If sPassword = "" Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "You must first provide a password setting to test."
                Return False
            End If

            Dim oCrypt As New clEncrypt2
            sPassword = oCrypt.Decrypt(sPassword)

        End If





        Try
            Dim mailer As MailBee.SmtpMail.Smtp = New MailBee.SmtpMail.Smtp

            mailer.SmtpServers.Add(sServer, sUser, sPassword)
            mailer.SmtpServers.Item(0).Port = sPort

            If mailer.Connect() = False Then
                bAccountOrLoginError = True
                sAccountOrLoginErrorMessage = "Unable to connect to your SMTP server (" & sServer & "). No further details are available."
                Return False
            End If

            mailer.Hello()

            If GetSetting("smtp_server_requires_authentication") = 1 Then
                If mailer.Login() = False Then
                    bAccountOrLoginError = True
                    sAccountOrLoginErrorMessage = "Unable to connect to your SMTP server (" & sServer & "). No further details are available."
                    Return False
                End If
            End If


        Catch ex As Exception

            bAccountOrLoginError = True
            sAccountOrLoginErrorMessage = ex.Message
            Return False

        End Try

        Return True


    End Function

    Public Function ConvertAddressStringToCollection(ByVal sAddresses As String) As Collection

        If sAddresses = "" Then Return New Collection

        sAddresses = Replace(sAddresses, ";", ",")
        sAddresses = Replace(sAddresses, ", ", ",")
        sAddresses = Replace(sAddresses, ", ", ",")
        sAddresses = Replace(sAddresses, ", ", ",")

        Dim cReturn As New Collection
        Dim aAddresses() As String = Split(sAddresses, ",")

        For Each Addr As String In aAddresses
            cReturn.Add(Addr)
        Next

        Return cReturn

    End Function

    Public Function CleanOldWebmailItems(Optional ByVal iNumberOfDays As Integer = 2) As Boolean


        Dim sWebmailFolder As String = System.Web.HttpContext.Current.Server.MapPath("../Temp/Webmail/")

        Dim source As DirectoryInfo = New DirectoryInfo(sWebmailFolder)

        'Determine whether the source directory exists. 
        If source.Exists Then

            'Directories under /Temp/Webmail
            'For Each Directory In New IO.DirectoryInfo(sWebmailFolder).GetDirectories
            For Each Directory In source.GetDirectories
                If Directory.LastWriteTime < Today.AddDays(-iNumberOfDays) Then
                    'Try
                    Directory.Delete(True)
                    'Catch ex As Exception

                    '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
                    '        goErr.SetError(ex, 45100, "clWebmail::CleanOldWebmailItems")
                    '    End If

                    'End Try

                End If
            Next
        End If



        Return True
    End Function

    Public Function RewriteMailHTMLToFile(ByVal oSellMailItem As SellMailItem, ByVal strNewFileHTML As String) As Boolean

        'Try
        Dim sPrivateDirectory As String = System.Web.HttpContext.Current.Server.MapPath("../Temp/Webmail/") & UCase(oSellMailItem.s_MID)
            My.Computer.FileSystem.WriteAllText(sPrivateDirectory & "\message.html", strNewFileHTML, False)
        'Catch ex As Exception

        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45100, "clWebMail::RewriteMailHTMLToFile")
        '    End If

        'End Try



    End Function

End Class

Public Class SellMailItem
    Public c_To As Collection
    Public c_CC As Collection
    Public c_BCC As Collection
    Public s_From As String
    Public s_Subject As String
    Public s_UID As String
    Public s_MID As String
    Public s_URL As String
    Public s_HTMLBody As String
    Public s_TextBody As String
    Public c_Attachments As Collection
    Public dt_DateReceived As DateTime
    Public dt_DateSent As DateTime



    Private msg As MailBee.Mime.MailMessage '= GetNewMsg()

    Private Function GetNewMsg() As MailBee.Mime.MailMessage

        Dim oRetMsg As New MailBee.Mime.MailMessage
        Return oRetMsg

    End Function

    Public Sub New()

    End Sub
End Class





