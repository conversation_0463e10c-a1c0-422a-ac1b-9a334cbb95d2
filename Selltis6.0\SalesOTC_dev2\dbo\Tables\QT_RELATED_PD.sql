﻿CREATE TABLE [dbo].[QT_RELATED_PD] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_QT_RELATED_PD_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_QT] UNIQUEIDENTIFIER NOT NULL,
    [GID_PD] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_QT_RELATED_PD] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PD_Connected_QT] FOREIGN KEY ([GID_QT]) REFERENCES [dbo].[QT] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_RELATED_PD] FOREIGN KEY ([GID_PD]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[QT_RELATED_PD] NOCHECK CONSTRAINT [LNK_PD_Connected_QT];


GO
ALTER TABLE [dbo].[QT_RELATED_PD] NOCHECK CONSTRAINT [LNK_QT_RELATED_PD];


GO
CREATE NONCLUSTERED INDEX [IX_QT_RELATED_PD]
    ON [dbo].[QT_RELATED_PD]([GID_QT] ASC, [GID_PD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PD_Connected_QT]
    ON [dbo].[QT_RELATED_PD]([GID_PD] ASC, [GID_QT] ASC);

