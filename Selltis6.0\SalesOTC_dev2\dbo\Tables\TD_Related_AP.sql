﻿CREATE TABLE [dbo].[TD_Related_AP] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_ToDo_Related_App_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_AP] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_Related_AP] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_AP_Related_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_Related_AP] FOREIGN KEY ([GID_AP]) REFERENCES [dbo].[AP] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_Related_AP] NOCHECK CONSTRAINT [LNK_AP_Related_TD];


GO
ALTER TABLE [dbo].[TD_Related_AP] NOCHECK CONSTRAINT [LNK_TD_Related_AP];


GO
CREATE CLUSTERED INDEX [IX_TD_Related_AP]
    ON [dbo].[TD_Related_AP]([GID_AP] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_AP_Related_TD]
    ON [dbo].[TD_Related_AP]([GID_TD] ASC);

