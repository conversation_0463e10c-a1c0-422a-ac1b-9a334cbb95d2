﻿CREATE TABLE [dbo].[PD_Related_US] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Product_Related_User_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PD] UNIQUEIDENTIFIER NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PD_Related_US] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PD_Related_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_Connected_PD] FOREIGN KEY ([GID_PD]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PD_Related_US] NOCHECK CONSTRAINT [LNK_PD_Related_US];


GO
ALTER TABLE [dbo].[PD_Related_US] NOCHECK CONSTRAINT [LNK_US_Connected_PD];


GO
CREATE CLUSTERED INDEX [IX_US_Connected_PD]
    ON [dbo].[PD_Related_US]([GID_PD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PD_Related_US]
    ON [dbo].[PD_Related_US]([GID_US] ASC);

