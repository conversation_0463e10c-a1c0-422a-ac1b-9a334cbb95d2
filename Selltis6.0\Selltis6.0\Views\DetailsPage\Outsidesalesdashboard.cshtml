﻿@model Selltis.MVC.Models.OutsideSales

@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<head>


    <link href="~/Content/assets/js/jquery-ui/css/no-theme/jquery-ui-1.10.3.custom.css" rel="stylesheet" />
    <link rel="stylesheet" href="~/Content/assets/css/font-icons/entypo/css/entypo.css">

    <link rel="stylesheet" href="~/Content/assets/js/select2/select2-bootstrap.css">
    <link rel="stylesheet" href="~/Content/assets/js/select2/select2.css">
    <link rel="stylesheet" href="~/Content/assets/js/datatables/datatables.css">
    <link rel="stylesheet" href="~/Content/assets/js/zurb-responsive-tables/responsive-tables.css">
    <link rel="stylesheet" href="~/Content/assets/js/vertical-timeline/css/component.css">

    <link rel="stylesheet" href="~/Content/assets/css/custom.css">

    <script src="~/Content/assets/js/gsap/TweenMax.min.js"></script>


</head>
<style>

    .mt-20 {
        color: black;
    }

    .table-bordered > thead > tr > th, .table-bordered > tbody > tr > th, .table-bordered > tfoot > tr > th, .table-bordered > thead > tr > td, .table-bordered > tbody > tr > td, .table-bordered > tfoot > tr > td {
        border: 0px solid #ebebeb !important
    }

    .table-bordered > thead > tr > th {
        font-weight: 400
    }

    .table-bordered > tfoot > tr > td, .table-bordered > tfoot > tr > th, .table-bordered > thead > tr > td, .table-bordered > thead > tr > th {
        background-color: #ecf1f7;
        color: #a6a7aa;
    }

    .page-content-container {
        padding: 5px 15px 15px 15px;
        border-radius: 3px;
        background-color: white
    }

    h4 {
        font-size: 16px;
        color: #00366c;
        font-family: inherit;
        font-weight: 500;
    }

    table {
        border-collapse: collapse;
        border-spacing: 0
    }

    a {
        color: #373e4a;
    }

    .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
        position: relative;
        min-height: 1px;
        padding-left: 15px;
        padding-right: 15px
    }

    .table thead th, .table tbody td {
        text-align: right;
    }

        .table thead th:first-child, .table tbody td:first-child {
            text-align: left;
        }

    .mb-10 {
        margin-bottom: 10px !important;
        margin-top: 10px !important;
    }

    .table > thead > tr > th {
        padding: 8px;
        line-height: 1.********;
    }

    .text-left-i-quotes div, .text-left-i-contacts div, .text-left-i-opps div, .text-left-i-Accounts div, .text-left-i-activities div, .text-left-i-activitiesSV div, .text-left-i-Tasks div {
        width: auto;
        overflow: hidden;
        white-space: normal;
        text-overflow: ellipsis;
    }

    .Main-name {
        font-size: 12px !important;
        color: #373E4A;
    }

    .Sub-name {
        font-family: 'Open Sans', Helvetica, Arial, sans-serif !important;
        font-size: 10px;
        color: #949494;
    }

    .hide-row {
        display: none;
    }

    .table-link {
        color: #a6a7aa;
        font-size: 10px !important;
        margin-right: 15px;
        display: inline-flex;
        align-items: center;
    }


        .table-link i {
            font-size: 8px;
            margin-left: 5px;
        }

    .icon {
        font-size: 60px;
        font-weight: bold;
        line-height: 100%;
        color: #ECF1F7;
        font-style: normal;
    }

    .heading {
        color: white;
    }



    /*Page content css*/
    .social-icons {
        font-size: 4rem;
    }

        .social-icons li {
            display: inline;
            float: left;
        }

            .social-icons li i:before {
                margin: 0px;
            }

    .profile-name aside.user-thumb img {
        max-width: 70px;
    }

    .contacts-all-btn-icon {
        font-size: 4rem;
    }




    .text-decoration-none {
        text-decoration: none !important;
    }

    .profile-name {
        color: #fff;
    }

    .tile-stats {
        position: relative;
        display: block;
        background: #303641;
        padding: 20px;
        margin-bottom: 12px;
        overflow: hidden;
        -webkit-border-radius: 5px;
        -webkit-background-clip: padding-box;
        -moz-border-radius: 5px;
        -moz-background-clip: padding;
        border-radius: 5px;
        background-clip: padding-box;
        -webkit-transition: all 300ms ease-in-out;
        -moz-transition: all 300ms ease-in-out;
        -o-transition: all 300ms ease-in-out;
        transition: all 300ms ease-in-out;
    }

        .tile-stats:hover {
            background: #252a32;
        }

        .tile-stats .icon {
            color: rgba(0, 0, 0, 0.1);
            position: absolute;
            right: 5px;
            bottom: 5px;
            z-index: 1;
        }

            .tile-stats .icon i {
                font-size: 100px;
                line-height: 0;
                margin: 0;
                padding: 0;
                vertical-align: bottom;
            }

                .tile-stats .icon i:before {
                    margin: 0;
                    padding: 0;
                    line-height: 0;
                }

        .tile-stats .num,
        .tile-stats h3,
        .tile-stats p {
            position: relative;
            color: #fff;
            z-index: 5;
            margin: 0;
            padding: 0;
        }

        .tile-stats .num {
            font-size: 38px;
            font-weight: bold;
        }

        .tile-stats h3 {
            font-size: 18px;
            margin-top: 5px;
        }

        .tile-stats p {
            font-size: 11px;
            margin-top: 5px;
        }

        .tile-stats.tile-red {
            background: #f56954;
        }

            .tile-stats.tile-red:hover {
                background: #f4543c;
            }

            .tile-stats.tile-red .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-red .num,
            .tile-stats.tile-red h3,
            .tile-stats.tile-red p {
                color: #fff;
            }

        .tile-stats.tile-green {
            background: #00a65a;
        }

            .tile-stats.tile-green:hover {
                background: #008d4c;
            }

            .tile-stats.tile-green .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-green .num,
            .tile-stats.tile-green h3,
            .tile-stats.tile-green p {
                color: #fff;
            }

        .tile-stats.tile-blue {
            background: #0073b7;
        }

            .tile-stats.tile-blue:hover {
                background: #00639e;
            }

            .tile-stats.tile-blue .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-blue .num,
            .tile-stats.tile-blue h3,
            .tile-stats.tile-blue p {
                color: #fff;
            }

        .tile-stats.tile-aqua {
            background: #00c0ef;
        }

            .tile-stats.tile-aqua:hover {
                background: #00acd6;
            }

            .tile-stats.tile-aqua .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-aqua .num,
            .tile-stats.tile-aqua h3,
            .tile-stats.tile-aqua p {
                color: #fff;
            }

        .tile-stats.tile-cyan {
            background: #00b29e;
        }

            .tile-stats.tile-cyan:hover {
                background: #009987;
            }

            .tile-stats.tile-cyan .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-cyan .num,
            .tile-stats.tile-cyan h3,
            .tile-stats.tile-cyan p {
                color: #fff;
            }

        .tile-stats.tile-purple {
            background: #ba79cb;
        }

            .tile-stats.tile-purple:hover {
                background: #b167c4;
            }

            .tile-stats.tile-purple .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-purple .num,
            .tile-stats.tile-purple h3,
            .tile-stats.tile-purple p {
                color: #fff;
            }

        .tile-stats.tile-pink {
            background: #ec3b83;
        }

            .tile-stats.tile-pink:hover {
                background: #ea2474;
            }

            .tile-stats.tile-pink .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-pink .num,
            .tile-stats.tile-pink h3,
            .tile-stats.tile-pink p {
                color: #fff;
            }

        .tile-stats.tile-orange {
            background: #ffa812;
        }

            .tile-stats.tile-orange:hover {
                background: #f89d00;
            }

            .tile-stats.tile-orange .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-orange .num,
            .tile-stats.tile-orange h3,
            .tile-stats.tile-orange p {
                color: #fff;
            }

        .tile-stats.tile-brown {
            background: #6c541e;
        }

            .tile-stats.tile-brown:hover {
                background: #584418;
            }

            .tile-stats.tile-brown .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-brown .num,
            .tile-stats.tile-brown h3,
            .tile-stats.tile-brown p {
                color: #fff;
            }

        .tile-stats.tile-plum {
            background: #701c1c;
        }

            .tile-stats.tile-plum:hover {
                background: #5c1717;
            }

            .tile-stats.tile-plum .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-plum .num,
            .tile-stats.tile-plum h3,
            .tile-stats.tile-plum p {
                color: #fff;
            }

        .tile-stats.tile-gray {
            background: #f5f5f5;
        }

            .tile-stats.tile-gray:hover {
                background: #e8e8e8;
            }

            .tile-stats.tile-gray .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-gray .num,
            .tile-stats.tile-gray h3,
            .tile-stats.tile-gray p {
                color: #8f8f8f;
            }

        .tile-stats.tile-white {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white .num,
            .tile-stats.tile-white h3,
            .tile-stats.tile-white p {
                color: #303641;
            }

            .tile-stats.tile-white:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-red {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-red:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-red .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-red .num,
            .tile-stats.tile-white-red h3,
            .tile-stats.tile-white-red p {
                color: #f56954;
            }

            .tile-stats.tile-white-red:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-green {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-green:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-green .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-green .num,
            .tile-stats.tile-white-green h3,
            .tile-stats.tile-white-green p {
                color: #00a65a;
            }

            .tile-stats.tile-white-green:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-blue {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-blue:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-blue .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-blue .num,
            .tile-stats.tile-white-blue h3,
            .tile-stats.tile-white-blue p {
                color: #0073b7;
            }

            .tile-stats.tile-white-blue:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-aqua {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-aqua:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-aqua .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-aqua .num,
            .tile-stats.tile-white-aqua h3,
            .tile-stats.tile-white-aqua p {
                color: #00c0ef;
            }

            .tile-stats.tile-white-aqua:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-cyan {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-cyan:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-cyan .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-cyan .num,
            .tile-stats.tile-white-cyan h3,
            .tile-stats.tile-white-cyan p {
                color: #00b29e;
            }

            .tile-stats.tile-white-cyan:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-purple {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-purple:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-purple .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-purple .num,
            .tile-stats.tile-white-purple h3,
            .tile-stats.tile-white-purple p {
                color: #ba79cb;
            }

            .tile-stats.tile-white-purple:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-pink {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-pink:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-pink .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-pink .num,
            .tile-stats.tile-white-pink h3,
            .tile-stats.tile-white-pink p {
                color: #ec3b83;
            }

            .tile-stats.tile-white-pink:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-orange {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-orange:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-orange .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-orange .num,
            .tile-stats.tile-white-orange h3,
            .tile-stats.tile-white-orange p {
                color: #ffa812;
            }

            .tile-stats.tile-white-orange:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-brown {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-brown:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-brown .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-brown .num,
            .tile-stats.tile-white-brown h3,
            .tile-stats.tile-white-brown p {
                color: #6c541e;
            }

            .tile-stats.tile-white-brown:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-plum {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-plum:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-plum .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-plum .num,
            .tile-stats.tile-white-plum h3,
            .tile-stats.tile-white-plum p {
                color: #701c1c;
            }

            .tile-stats.tile-white-plum:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-gray {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-gray:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-gray .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-gray .num,
            .tile-stats.tile-white-gray h3,
            .tile-stats.tile-white-gray p {
                color: #8f8f8f;
            }

            .tile-stats.tile-white-gray:hover {
                background-color: #fafafa;
            }

        .tile-stats.stat-tile {
            padding: 0px;
            height: 155px;
            border: none !important;
            box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
        }

        .tile-stats.tile-neon-red {
            background: #ff4e50;
        }

        .tile-stats.stat-tile h3 {
            padding: 20px 20px 0px 20px;
        }

        .tile-stats.stat-tile p {
            padding: 0px 20px 20px 20px;
            margin-bottom: 20px;
        }

    .tile-reg-users {
        background: #8a6edc !important;
    }

    .tile-stats {
        min-height: 149px;
    }

    .tile-oppstatus {
        background: #373E4A;
    }

    .tile-quicklinks {
        background: #ffffff;
        border: 1px solid #EBEBEB;
    }



    .company-details label, .company-details p {
        font-size: 12px;
    }

    .company-details label {
        color: #00366c;
        margin-bottom: 0px;
    }

    .tile-stats .icon i {
        position: relative;
        top: -20px
    }

    .tile-stats {
        min-height: 149px
    }

        .tile-stats .num {
            font-size: 60px;
            font-weight: bold;
            line-height: 100%
        }

        .tile-stats p {
            font-size: 14px
        }

            .tile-stats p a {
                text-decoration: underline
            }

    .tile-quicklinks {
        background: #ffffff;
        border: 1px solid #EBEBEB
    }

        .tile-quicklinks div.num {
            color: #373E4A
        }

        .tile-quicklinks h3 {
            color: #373E4A
        }

        .tile-quicklinks p {
            color: #373E4A
        }

        .tile-quicklinks:hover {
            background: #ffffff
        }

    .tile-oppstatus {
        background: #da58a4 !important
    }

    .tile-oppstatus1 {
        background: #40587C !important
    }

    .tile-oppstatus2 {
        background: #30D5C8 !important
    }

    .tile-aopotentials {
        background: #5bce29 !important;
    }

    .tile-aopotentials1 {
        background: #f17b00 !important;
    }

    .name-title-info-list {
        padding: 5px 15px
    }

        .name-title-info-list .name-title-info-item {
            padding-top: 5px !important;
            padding-bottom: 5px !important
        }

        .name-title-info-list .name-title {
            font-size: 14px;
            color: #373E4A
        }

        .name-title-info-list .sub-phone-email {
            font-size: 10px;
            color: #949494
        }

    .timeline {
        border: 1px solid #f5f5f6;
        border-radius: 3px;
        padding: 15px
    }

    ul.cbp_tmtimeline > li .cbp_tmlabel, ul.cbp_tmtimeline > li .cbp_tmlabel.empty {
        margin-bottom: 15px !important
    }

    .cbp_tmtimeline > li .cbp_tmlabel {
        background: #F5F5F7;
        padding: 1.2rem 1.6rem
    }

    .company-details label {
        color: #00366c;
        margin-bottom: 0px
    }

    .company-details label, .company-details p {
        font-size: 12px
    }

    .border-light-gray {
        border: 1px solid #f5f5f6;
    }

    .txt-light-gray {
        color: #949494;
    }

    .edit-btn {
        font-size: 12px;
    }

    .ml-10 {
        margin-left: 10px;
    }

    .company-details label, .company-details p {
        font-size: 12px;
    }

    .company-details label {
        color: #00366c;
        margin-bottom: 0px;
    }

    label {
        display: inline-block;
        max-width: 100%;
        margin-bottom: 5px;
        font-weight: bold;
    }

    .cbp_tmtimeline:before {
        background: #f5f5f6;
        width: 5px;
        margin-left: -6px;
    }


    #content.table-layout > div, #content.table-layout > section {
        vertical-align: top;
        padding: 0px 0px 0px !important;
    }
    .tile-stats {
        cursor: pointer;
    }
    th a:hover {
        color: #3498db !important;
    }

    .responsive-container {
        overflow-x: auto;
        display: block;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        margin: 0 auto;
    }

    .table {
        width: 100%;
        table-layout: auto;
        border-collapse: collapse;
    }

        .table th, .table td {
            white-space: nowrap;
            text-align: left;
            vertical-align: middle;
        }

            .table th a, .table td a {
                display: inline-block;
            }


    .responsive-container {
        padding: 0 10px;
    }

    .header-row .custom-header-buttons {
        display: flex;
        flex-direction: column;
    }

    .d-flex {
        display: flex;
    }

    .flex-wrap {
        flex-wrap: wrap;
    }

    .justify-content-between {
        justify-content: space-between;
    }

    .align-items-center {
        align-items: center;
    }

    .gap-1 a {
        margin-right: 10px;
        text-decoration: none;
    }

        .gap-1 a:last-child {
            margin-right: 0;
        }
    footer.main {
        display: none;
    }

    @@media (min-width: 768px) {
        .header-row .custom-header-buttons {
            flex-direction: row;
            align-items: center;
        }

        .gap-1 {
            margin-left: auto;
        }
    }

    .custom-header-buttons .d-flex a {
        text-decoration: none;
        color: inherit;
        font-weight: 500;
    }

        .custom-header-buttons .d-flex a:hover {
            text-decoration: underline;
            color: #007bff;
        }

    @@media (max-width: 768px) {
        .custom-header-buttons .d-flex {
            flex-direction: column;
        }
    }
</style>

<!-- TOP FIXED NAVIGATION -->
<section id="content_wrapper" style="height:calc(100%)">
    <!-- Begin: Content -->
    <section id="content" style="height:100%" class=" table-layout animated fadein">

        <div class="page-container">




            <div class="main-content" style="background-color:#003665;padding:12px;height:90%">
                <div class="d-flex justify-content-between align-items-center" style="position: relative; width: 100%; color: white;">
                    <!-- Left Section: Title -->
                    <div class="company-title ml-10 mb-10">
                        <span style="font-size: 18px;">My Priorities Outside Sales</span>
                    </div>
                    <!-- Right Section: Last Refreshed Date Label -->
                    @*<div style="position: absolute; right: 0; top: 10px; color: white; display: flex; align-items: center;">
                        <span>Last Refreshed:</span>
                        <span id="lastRefreshedDate" style="margin-left: 5px;"></span>
                        <span class="fa fa-refresh" onclick="RefreshData()" title="Refresh" style="margin-left: 5px; cursor: pointer;"></span>
                    </div>*@
                </div>

                <div class="page-content-container">
                    <!--tile's-->
                    <div class="row my-4">
                        <div class="col-lg-4 col-md-6 col-xs-12">
                            <div class="tile-stats tile-oppstatus d-flex justify-content-start align-items-center" onclick="gotoDesktop('DSK_0D259E66-1AD3-4569-5858-AFB10103AEAD')">
                                <div>
                                    <div class="icon"><i class="fa fa-hashtag"></i></div>
                                    <div class="num" id="overDueprimaryAccountsQtytile"></div>
                                    <h3>Overdue Primary Accounts</h3>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 col-xs-12">
                            <div class="tile-stats tile-aopotentials" onclick="gotoDesktop('DSK_A835E3E4-E895-4B58-5858-AE71007667AC')">
                                <div class="icon"><i class="fa fa-dollar"></i></div>
                                <div class="num" id="myQuotePipeline"></div>
                                <h3>Quote pipeline</h3>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 col-xs-12 ">
                            <div class="tile-stats tile-oppstatus1 d-flex justify-content-start align-items-center" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319')">
                                <div>
                                    <div class="icon"><i class="fa fa-hashtag"></i></div>
                                    <div class="num" id="MyLeadsTile"></div>
                                    <h3>Overdue Leads</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row my-4">
                        <div class="col-lg-4 col-md-6 col-xs-12">
                            <div class="tile-stats tile-oppstatus2" onclick="gotoDesktop('DSK_3629E899-CB49-42F5-5858-B16400243CFC')">
                                <div class="icon"><i class="fa fa-hashtag"></i></div>
                                <div class="num" id="openStrategicQuotesQtytile"></div>
                                <h3>Overdue Strategic Quotes</h3>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 col-xs-12">
                            <div class="tile-stats tile-reg-users" onclick="gotoDesktop('DSK_B348EECC-B451-4ABE-5858-B1FB01004FC6')">
                                <div class="icon"><i class="fa fa-hashtag"></i></div>
                                <div class="num" id="MyTaskstile"></div>
                                <h3>Overdue Tasks</h3>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 col-xs-12">
                            <div class="tile-stats tile-aopotentials1" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793')">
                                <div class="icon"><i class="fa fa-hashtag"></i></div>
                                <div class="num" id="MyOpenOppsqtytile"></div>
                                <h3>Overdue Opportunities</h3>
                            </div>
                        </div>

                    </div>
                    <!--Body-->
                    <div class="row mt-20">
                        <div class="col-lg-4 col-xs-12">
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0">My Primary Accounts</h4>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_0D259E66-1AD3-4569-5858-AFB10103AEAD','','','')" class="primary-account-link">My Primary Accounts</a>
                                                    <span class="d-flex gap-1">
                                                        <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_2004063009585560154C_S 00016XX','','','')"><i class="entypo-eye"></i> View All</a>
                                                        <a href="#" style="color: #a6a7aa; margin-right: 5px;" onclick="gotoDesktop('DSK_0B9E62DB-3FE2-4E50-5858-AC4F0085282F','','','')"><i class="entypo-eye"></i> View Recent</a>
                                                        <a href="#" style="color: #a6a7aa;" onclick="PrepareFormLink('CO','CRU_CO')"><i class="entypo-plus"></i> Add</a>
                                                    </span>
                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_9070887A-CDD5-40CF-5858-B1FB00E60D33','','','')">New Accounts</a></td>
                                            <td>  <a href="#" onclick="gotoDesktop('DSK_9070887A-CDD5-40CF-5858-B1FB00E60D33','','','')"><p style="font-size:105%" id="accountQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_5E3C9341-1A0A-4BEF-5858-B1FB00EF319F','','','')">Team Sell Accounts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_5E3C9341-1A0A-4BEF-5858-B1FB00EF319F','','','')"><p style="font-size: 105% " id="teamSellAccountsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_EC897A4F-DEB9-4BCD-5858-AF850114AE25','','','')">Overserved Accounts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_EC897A4F-DEB9-4BCD-5858-AF850114AE25','','','')"><p style="font-size: 105% " id="overservedAccountsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_EC897A4F-DEB9-4BCD-5858-AF850114AE25','','','')">Underserved Accounts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_EC897A4F-DEB9-4BCD-5858-AF850114AE25','','','')"><p style="font-size: 105% " id="underservedAccountsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_0D259E66-1AD3-4569-5858-AFB10103AEAD','','','')">Overdue Primary Accounts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_0D259E66-1AD3-4569-5858-AFB10103AEAD','','','')"><p style="font-size: 105% " id="overDueprimaryAccountsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_E101F0C7-60D0-4CC7-5858-B27A01208899','','','')">Primary Accounts Without Contacts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_E101F0C7-60D0-4CC7-5858-B27A01208899','','','')"><p style="font-size: 105% " id="AccountsWithoutContacts"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_96398864-E2DA-4D07-5858-B27A0120B340','','','')">Primary Accounts Without Activities</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_96398864-E2DA-4D07-5858-B27A0120B340','','','')"><p style="font-size: 105% " id="AccountsWithoutActivities"></p></a></td>
                                         </tr>
                                        <tr>
                                            <td class="text-left-i-Accounts" colspan="2" style="text-align: left">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0 ">My Contacts</h4>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_2004110817164953089MAR 00014XX','','','')" class="primary-account-link">My Contacts</a>
                                                    <span class="d-flex gap-1">
                                                        <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_2004051408135030253C_S 00016XX','','','')"><i class="entypo-eye"></i> View All</a>
                                                        <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_2004110817164953089MAR 00014XX','','','')"><i class="entypo-eye"></i> View All My</a>
                                                        <a href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_F38C50BB-CB7D-4A45-5858-AC4F00A2F11A','','','')"><i class="entypo-eye"></i> View Recent</a>
                                                    </span>
                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_2004110817164953089MAR 00014XX','','','')">New Contacts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_2004110817164953089MAR 00014XX','','','')"><p style="font-size: 105% " id="newContactsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_2004110817164953089MAR 00014XX','','','')">Key Contacts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_2004110817164953089MAR 00014XX','','','')"><p style="font-size: 105% " id="keyContactsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_2004110817164953089MAR 00014XX','','','')">Overdue Contacts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_2004110817164953089MAR 00014XX','','','')"><p style="font-size: 105% " id="overdueContactsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td class="text-left-i-contacts" colspan="2" style="text-align: left">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0 ">My Tasks</h4>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_04E70BB9-C8F5-4385-5858-AD6C010DB15E','','','')" class="primary-account-link">My Tasks</a>
                                                    <span class="d-flex gap-1">
                                                        <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_93121443-FA60-431F-5858-AD6B010D3FFC','','','')"><i class="entypo-eye"></i> View All</a>
                                                        <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_04E70BB9-C8F5-4385-5858-AD6C010DB15E','','','')"><i class="entypo-eye"></i> View All My</a>
                                                        <a href="#" style="color: #a6a7aa;" onclick=PrepareFormLink('TD','CRU_TD')><i class="entypo-plus"></i> Add</a>
                                                    </span>
                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_B348EECC-B451-4ABE-5858-B1FB01004FC6','','','')">Due Next 10 Days</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_B348EECC-B451-4ABE-5858-B1FB01004FC6','','','')"><p style="font-size: 105% " id="overdueTasks"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_B348EECC-B451-4ABE-5858-B1FB01004FC6','','','')">Overdue Tasks</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_B348EECC-B451-4ABE-5858-B1FB01004FC6','','','')"><p style="font-size: 105% " id="dueNext10Days"></p></a></td>
                                        </tr>

                                        <tr>
                                            <td class="text-left-i-Tasks" colspan="2" style="text-align: left">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0 ">My Activities</h4>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_2004052710225915073C_S 00016XX','','','')" class="primary-account-link">My Activities</a>
                                                    <span class="d-flex gap-1">
                                                        <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_2003100817330625007MAR 00014XX','','','')"><i class="entypo-eye"></i> View All</a>
                                                        <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_2004052710225915073C_S 00016XX','','','')"><i class="entypo-eye"></i> View All My</a>

                                                        <a href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_E7128441-F2B9-429B-2020-98CF0118E2B5','','','')"><i class="entypo-eye"></i> View Recent</a>
                                                    </span>
                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_EA0F819A-102D-4EC0-5858-B22501787ED8','','','')">Teamsell Activities</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_EA0F819A-102D-4EC0-5858-B22501787ED8','','','')"><p style="font-size: 105%" id="myAcTeamSell"></p></a></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_768E7403-2332-4D6C-5858-B225017B1FD7','','','')" class="primary-account-link">My Sales Visits</a>
                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_768E7403-2332-4D6C-5858-B225017B1FD7','','','')">This Month</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_768E7403-2332-4D6C-5858-B225017B1FD7','','','')"><p style="font-size: 105%" id="mySalesThisMonth"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_53B4A600-9696-49DE-5858-B29E00619361','','','')">Last Month</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_53B4A600-9696-49DE-5858-B29E00619361','','','')"><p style="font-size: 105%" id="mySalesLastonth"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td class="text-left-i-activitiesSV" colspan="2" style="text-align: left">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a class="primary-account-link">Other's Activities</a>
                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_D5E140E0-06A4-4799-5858-B2260006EC16','','','')">This Month</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_D5E140E0-06A4-4799-5858-B2260006EC16','','','')"><p style="font-size: 105%" id="othersAcThisMonth"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_1C7B1615-8771-491A-5858-B29E006224A3','','','')">Last Month</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_1C7B1615-8771-491A-5858-B29E006224A3','','','')"><p style="font-size: 105%" id="othersAcLastMonth"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td class="text-left-i-activities" colspan="2" style="text-align: left">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>


                        <div class="col-lg-8 col-xs-12">
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0 ">My Opportunities</h4>

                                </div>
                            </div>
                            <table class="table table-bordered responsive first-col-left">
                                <thead>
                                    <tr>
                                        <th width="33%" class="custom-header-buttons">
                                            Open Leads
                                            <span class="d-flex gap-1">
                                                <a class="align-items-center justify-content-center" href="#" onclick="gotoDesktop('DSK_5FA273F0-0ED8-4BF2-5858-A7440109EA66','','','')">
                                                    <i class="entypo-eye"></i>
                                                    View All
                                                </a>
                                                <a class="align-items-center justify-content-center" href="#" onclick="gotoDesktop('DSK_2004052710071079098C_S 00016XX','','','')">
                                                    <i class="entypo-eye"></i>
                                                    View All My
                                                </a>
                                                <a class="align-items-center justify-content-center" href="#" onclick="gotoDesktop('DSK_2004052710071079098C_S 00016XX','','','')">
                                                    <i class="entypo-eye"></i>
                                                    View Recent
                                                </a>
                                            </span>
                                        </th>
                                        <th>Qty</th>
                                        <th>Expected Value</th>
                                        <th>Weighted Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')">My Leads</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')"><p style="font-size: 105%" id="MyLeadQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')"><p style="font-size: 105%" id="MyLeadExpVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')"><p style="font-size: 105%" id="MyLeadWeightedVal"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')">Team Sell Leads</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')"><p style="font-size: 105%" id="teamSellLeadQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')"><p style="font-size: 105%" id="teamSellLeadExpVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')"><p style="font-size: 105%" id="teamSellLeadWeightedVal"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')">New Leads</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')"><p style="font-size: 105%" id="newLeadsQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')"><p style="font-size: 105%" id="newLeadsExpVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')"><p style="font-size: 105%" id="newLeadWeightedVal"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')">Overdue Leads</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')"><p style="font-size: 105%" id="overDueLeadsQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')"><p style="font-size: 105%" id="overDueLeadsExpVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_C54A84F0-6B4B-4480-5858-B226000A7319','','','')"><p style="font-size: 105%" id="overDueLeadsWeightedVal"></p></a></td>
                                    </tr>
                                    <tr style="background-color: #ecf1f7; height: 30px; ">
                                        <td  width="33%" class="custom-header-buttons">
                                            <a href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')">Open Opportunities</a>

                                            <span class="d-flex gap-1">
                                                <a class="align-items-center justify-content-center" href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_2003012009491863136MAI 30119XX','','','')">
                                                    <i class="entypo-eye"></i>
                                                    &nbsp; View All
                                                </a>
                                                <a class="align-items-center justify-content-center" style="color:#a6a7aa;" href="#" onclick="gotoDesktop('DSK_8A77F884-81D3-4041-5858-AED10164277D','','','')">
                                                    <i class="entypo-eye"></i>
                                                    View All My
                                                </a>
                                                <a class="align-items-center justify-content-center" style="color:#a6a7aa;" href="#" onclick="gotoDesktop('DSK_8A77F884-81D3-4041-5858-AED10164277D','','','')">
                                                    <i class="entypo-eye"></i>
                                                    View Recent
                                                </a>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')"><span id="openOppQty"></span> </a>
                                        </td>
                                        <td>
                                            <a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')"><span id="openOppExpVal"></span></a>
                                        </td>
                                        <td>
                                            <a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')"><span id="openOppWeightedVal"></span></a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')">Team Sell</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')"><p style="font-size: 105%" id="openOppTeamSellQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')"><p style="font-size: 105%" id="openOppTeamSellExpVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')"><p style="font-size: 105%" id="openOppTeamSellWeightedVal"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')">Due Next 30 days</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')"><p style="font-size: 105%" id="openOppDueNext30DaysQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')"><p style="font-size: 105%" id="openOppDueNext30DaysExpVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')"><p style="font-size: 105%" id="openOppDueNext30DaysWeightedVal"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')">Overdue Opportunities</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')"><p style="font-size: 105%" id="openOppOverDueQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')"><p style="font-size: 105%" id="openOppOverDueExpVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9C864C0E-FDA5-4A94-5858-AE5A0092C793','','','')"><p style="font-size: 105%" id="openOppOverDueWeightedVal"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td class="text-left-i-opps" colspan="4">
                                            @*<td class="" colspan="2" style="text-align: left">*@
                                        </td>
                                    </tr>
                                    <tr style="background-color: #ecf1f7; height: 30px; ">
                                        <td style="width: 35%;color: #a6a7aa; text-align: left;">Last 90 Days</td>
                                        <td style="width: 33%; color: #a6a7aa;">Qty</td>
                                        <td style="width: 20%; color: #a6a7aa;">Total</td>
                                        <td style="width: 23%; color: #a6a7aa;">Rate(%)</td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_6D50EC51-43C4-4D54-5858-B2260108E6AB','','','')">Won</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_6D50EC51-43C4-4D54-5858-B2260108E6AB','','','')"><p style="font-size: 105%" id="wonQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_6D50EC51-43C4-4D54-5858-B2260108E6AB','','','')"><p style="font-size: 105%" id="wonTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_6D50EC51-43C4-4D54-5858-B2260108E6AB','','','')"><p style="font-size: 105%" id="wonRate"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_6D50EC51-43C4-4D54-5858-B2260108E6AB','','','')">Lost</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_6D50EC51-43C4-4D54-5858-B2260108E6AB','','','')"><p style="font-size: 105%" id="lostQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_6D50EC51-43C4-4D54-5858-B2260108E6AB','','','')"><p style="font-size: 105%" id="lostTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_6D50EC51-43C4-4D54-5858-B2260108E6AB','','','')"><p style="font-size: 105%" id="lostRate"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_6D50EC51-43C4-4D54-5858-B2260108E6AB','','','')">Cancelled</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_6D50EC51-43C4-4D54-5858-B2260108E6AB','','','')"><p style="font-size: 105%" id="cancelledQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_6D50EC51-43C4-4D54-5858-B2260108E6AB','','','')"><p style="font-size: 105%" id="cancelledTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_6D50EC51-43C4-4D54-5858-B2260108E6AB','','','')"><p style="font-size: 105%" id="cancelledRate"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td colspan="4" style="height: 20px;"></td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <h4 class="m-0 " style="text-align:left">My Quotes</h4>
                                        </td>
                                    </tr>
                                    <tr style="background-color: #ecf1f7; height: 30px;">
                                        <th width="33%" class="custom-header-buttons">


                                            <span class="d-flex gap-1">
                                                <a class="align-items-center justify-content-center" style="color:#a6a7aa;" href="#" onclick="gotoDesktop('DSK_2004052610211387019C_S 00016XX','','','')">
                                                    <i class="entypo-eye"></i>
                                                    View All
                                                </a>
                                                <a class="align-items-center justify-content-center" style="color:#a6a7aa;" href="#" onclick="gotoDesktop('DSK_5D8505F5-A8E9-4EA7-5858-AECF0153D82A','','','')">
                                                    <i class="entypo-eye"></i>
                                                    View All My
                                                </a>
                                                <a class="align-items-center justify-content-center" style="color:#a6a7aa;" href="#" onclick="gotoDesktop('DSK_5D8505F5-A8E9-4EA7-5858-AECF0153D82A','','','')">
                                                    <i class="entypo-eye"></i>
                                                    View Recent
                                                </a>
                                            </span>
                                        </th>
                                        <td style="width: 33%; color: #a6a7aa;">Qty</td>
                                        <td style="width: 20%; color: #a6a7aa;">Total</td>
                                        <td style="width: 23%; color: #a6a7aa;">Margin(%)</td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E7EE34F8-C152-422A-5858-B225003672ED','','','')">Open RFQ's</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E7EE34F8-C152-422A-5858-B225003672ED','','','')"><p style="font-size: 105%" id="openRFQsQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E7EE34F8-C152-422A-5858-B225003672ED','','','')"><p style="font-size: 105%" id="openRFQstotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E7EE34F8-C152-422A-5858-B225003672ED','','','')"><p style="font-size: 105%" id="openRFQsmargin"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3629E899-CB49-42F5-5858-B16400243CFC','','','')">Open Strategic Quotes</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3629E899-CB49-42F5-5858-B16400243CFC','','','')"><p style="font-size: 105%" id="openStrategicQuotesQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3629E899-CB49-42F5-5858-B16400243CFC','','','')"><p style="font-size: 105%" id="openStrategicQuotesTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3629E899-CB49-42F5-5858-B16400243CFC','','','')"><p style="font-size: 105%" id="openStrategicQuotesMargin"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3629E899-CB49-42F5-5858-B16400243CFC','','','')">New Strategic Quotes</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3629E899-CB49-42F5-5858-B16400243CFC','','','')"><p style="font-size: 105%" id="newStrategicQuotesQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3629E899-CB49-42F5-5858-B16400243CFC','','','')"><p style="font-size: 105%" id="newStrategicQuotesTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3629E899-CB49-42F5-5858-B16400243CFC','','','')"><p style="font-size: 105%" id="newStrategicQuotesMargin"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3629E899-CB49-42F5-5858-B16400243CFC','','','')">Overdue Strategic Quotes</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3629E899-CB49-42F5-5858-B16400243CFC','','','')"><p style="font-size: 105%" id="overdueStrategicQuotesQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3629E899-CB49-42F5-5858-B16400243CFC','','','')"><p style="font-size: 105%" id="overdueStrategicQuotesTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3629E899-CB49-42F5-5858-B16400243CFC','','','')"><p style="font-size: 105%" id="overdueStrategicQuotesMargin"></p></a></td>

                                    </tr>
                                    <tr>
                                        <td class="text-left-i-quotes" colspan="4">
                                        </td>
                                    </tr>

                                    <tr style="background-color: #ecf1f7; height: 30px; ">
                                        <td style="color: #a6a7aa; text-align: left;">Last 90 Days</td>
                                        <td style="color: #a6a7aa;">Qty</td>
                                        <td style="color: #a6a7aa;">Total</td>
                                        <td style="color: #a6a7aa;">Rate(%)</td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FB588DB0-EA42-447E-5858-B22500390F3A','','','')">Strategic Quotes - Won</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FB588DB0-EA42-447E-5858-B22500390F3A','','','')"><p style="font-size: 105%" id="strategicQuotesWonQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FB588DB0-EA42-447E-5858-B22500390F3A','','','')"><p style="font-size: 105%" id="strategicQuotesWonTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FB588DB0-EA42-447E-5858-B22500390F3A','','','')"><p style="font-size: 105%" id="strategicQuotesWonRate"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FB588DB0-EA42-447E-5858-B22500390F3A','','','')">Strategic Quotes - Lost</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FB588DB0-EA42-447E-5858-B22500390F3A','','','')"><p style="font-size: 105%" id="strategicQuotesLostQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FB588DB0-EA42-447E-5858-B22500390F3A','','','')"><p style="font-size: 105%" id="strategicQuotesLostTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FB588DB0-EA42-447E-5858-B22500390F3A','','','')"><p style="font-size: 105%" id="strategicQuotesLostRate"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FB588DB0-EA42-447E-5858-B22500390F3A','','','')">Strategic Quotes - Cancelled</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FB588DB0-EA42-447E-5858-B22500390F3A','','','')"><p style="font-size: 105%" id="strategicQuotesCancelledQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FB588DB0-EA42-447E-5858-B22500390F3A','','','')"><p style="font-size: 105%" id="strategicQuotesCancelledTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FB588DB0-EA42-447E-5858-B22500390F3A','','','')"><p style="font-size: 105%" id="strategicQuotesCancelledRate"></p></a></td>
                                    </tr>
                                    <tr>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="row mt-20">
                        <div class="mb-20">
                            <div class="Contact-input-trend"></div>
                            <div class="SalesVisit-Input-Trends"></div>
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                </div>
                            </div>
                        </div>
                        <div class="mb-20">
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-20">
                        <div class="Opportunity-Input-Trends"></div>
                        <div class="Strategic-Quote-Input-Trend"></div>
                        <div class="col-lg-6 col-xs-12">
                            <div class="mb-20">
                                <div class="d-flex justify-content-between align-items-center mb-10">
                                    <div class="d-flex justify-content-start align-items-center">

                                    </div>
                                </div>
                            </div>
                        </div>                       
                        <div class="col-lg-6 col-xs-12">
                            <div class="mb-20">
                                <div class="d-flex justify-content-between align-items-center mb-10">
                                    <div class="d-flex justify-content-start align-items-center">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Footer -->
                <footer class="main mt-0 pt-20 pb-20 pl-20">
                    &copy; 2020 <strong>Selltis</strong>
                </footer>
            </div>
        </div>
    </section>
</section>

<script type="text/javascript">
    $(document).ready(function () {

        showProgress();
        GetOSR_Dashboard_Data();

    });
    function GetOSR_Dashboard_Data() {
            $.ajax({
url: '@Url.Action("GetOutsidesalesdashboard", "DetailsPage")',
type: 'GET',
dataType: 'json',
success: function (data) {
    console.log(data[0]);  // Check the returned data structure

    // Assuming `data` is an array, access the first item
    if (data && data.length > 0) {
        var item = data[0];  // Access the first item
        // Update fields with data from the first item
        $('#accountQty').text(item.NewAccountsQty || '0');
        $('#teamSellAccountsQty').text(item.TeamSellAccountsQty || '0');
        $('#overservedAccountsQty').text(item.OverservedAccountsQty || '0');
        $('#underservedAccountsQty').text(item.UnderservedAccountsQty || '0');
        $('#overDueprimaryAccountsQtytile').text(item.OverDueprimaryAccountsQty || '0');
        $('#overDueprimaryAccountsQty').text(item.OverDueprimaryAccountsQty || '0');
        $('#newContactsQty').text(item.NewContactsQty || '0');
        $('#keyContactsQty').text(item.KeyContactsQty || '0');
        $('#overdueContactsQty').text(item.OverdueContactsQty || '0');
        $('#AccountsWithoutContacts').text(item.AccountsWithoutContacts || '0');
        $('#AccountsWithoutActivities').text(item.AccountsWithoutActivities || '0');

        $('#teamSellLeadQty').text(item.MyLeadTeamSellLeadQty || '0');
        $('#teamSellLeadExpVal').text((item.MyTeamSellLeadsExptedVal || '0'));
        $('#teamSellLeadWeightedVal').text((item.MyTeamSellLeadsWeightedVal || '0'));
        $('#newLeadsQty').text(item.MyLeadNewLeadsQty || '0');
        $('#newLeadsExpVal').text((item.MyNewLeadsExptedVal || '0'));
        $('#newLeadWeightedVal').text((item.MyNewLeadsWeightedVal || '0'));
        $('#overDueLeadsQty').text(item.MyLeadOverDueLeadsQty || '0');
        $('#overDueLeadsExpVal').text((item.MyOverdueLeadsExptedVal || '0'));
        $('#overDueLeadsWeightedVal').text((item.MyOverdueLeadsWeightedVal || '0'));

        $('#MyLeadQty').text(item.Leads || '0');
        $('#MyLeadsTile').text(item.MyLeadOverDueLeadsQty || '0');
        $('#MyLeadExpVal').text((item.LeadsExpVal || '0'));
        $('#MyLeadWeightedVal').text((item.LeadWeightedVal || '0'));

        $('#MyOpenOppsqtytile').text(item.OpenOppOverDueQty || '0');
        $('#openOppQty').text(item.MyOpenOppsqty || '0');
        $('#openOppExpVal').text((item.MyOpenOppsExptedVal || '0'));
        $('#openOppWeightedVal').text((item.MyOpenOppsWeightedVal || '0'));

        $('#openOppTeamSellQty').text(item.OpenOppTeamSellQty || '0');
        $('#openOppDueNext30DaysQty').text(item.OpenOppDueNext30DaysQty || '0');
        $('#openOppOverDueQty').text(item.OpenOppOverDueQty || '0');

        $('#openOppTeamSellExpVal').text((item.OpenOppTeamSellExpVal || '0'));
        $('#openOppDueNext30DaysExpVal').text((item.OpenOppDueNext30DaysExpVal || '0'));
        $('#openOppOverDueExpVal').text((item.OpenOppOverDueExpVal || '0'));

        $('#openOppTeamSellWeightedVal').text((item.OpenOppTeamSellWeightedVal || '0'));
        $('#openOppDueNext30DaysWeightedVal').text((item.OpenOppDueNext30DaysWeightedVal || '0'));
        $('#openOppOverDueWeightedVal').text((item.OpenOppOverDueWeightedVal || '0'));

        $('#wonQty').text(item.WonQty || '0');
        $('#lostQty').text(item.LostQty || '0');
        $('#cancelledQty').text(item.CancelledQty || '0');
        $('#wonTotal').text((item.WonTotal || '0'));
        $('#lostTotal').text((item.LostTotal || '0'));
        $('#cancelledTotal').text((item.CancelledTotal || '0'));

        $('#wonRate').text((item.WonRate || 0));
        $('#lostRate').text((item.LostRate || 0));
        $('#cancelledRate').text((item.CancelledRate || 0));


        $('#dueNext10Days').text(item.DueNext10Days || '0');
        $('#overdueTasks').text(item.OverdueTasks || '0');
        $('#openOrdersTotal').text((item.OpenOrdersTotal || 0));
        $('#openOrdersMargin').text((item.OpenOrdersMargin || 0));
        $('#ordersThisMonthQty').text(item.OrdersThisMonthQty || '0');
        $('#ordersThisMonthTotal').text((item.OrdersThisMonthTotal || 0));
        $('#ordersThisMonthMargin').text((item.OrdersThisMonthMargin || 0));
        $('#ordersLastMonthQty').text(item.OrdersLastMonthQty || '0');
        $('#ordersLastMonthTotal').text((item.OrdersLastMonthTotal || 0));
        $('#ordersLastMonthMargin').text((item.OrdersLastMonthMargin || 0));
        $('#openOrdersQty').text(item.OpenOrdersQty || '0');
        $('#myActivities').text(item.MyActivities || '0');
        $('#myAcTeamSell').text(item.MyAcTeamSell || '0');
        $('#mySalesThisMonth').text(item.MySalesThisMonth || '0');
        $('#mySalesLastonth').text(item.MySalesLastonth || '0');
        $('#othersAcThisMonth').text(item.OthersAcThisMonth || '0');
        $('#othersAcLastMonth').text(item.OthersAcLastMonth || '0');

        $('#openStrategicQuotesQty').text(item.OpenStrategicQuotesQty || '0');
        $('#openStrategicQuotesQtytile').text(item.OverdueStrategicQuotesQty || '0');
        $('#newStrategicQuotesQty').text(item.NewStrategicQuotesQty || '0');
        $('#overdueStrategicQuotesQty').text(item.OverdueStrategicQuotesQty || '0');
        $('#openStrategicQuotesTotal').text((item.OpenStrategicQuotesTotal || 0));
        $('#newStrategicQuotesTotal').text((item.NewStrategicQuotesTotal || 0));
        $('#overdueStrategicQuotesTotal').text((item.OverdueStrategicQuotesTotal || 0));
        $('#openStrategicQuotesMargin').text((item.OpenStrategicQuotesMargin || 0));
        $('#newStrategicQuotesMargin').text((item.NewStrategicQuotesMargin || 0));
        $('#overdueStrategicQuotesMargin').text((item.OverdueStrategicQuotesMargin || 0));

        $('#openRFQsQty').text(item.OpenRFQsQty || '0');
        $('#openRFQstotal').text((item.OpenRFQstotal||'0'));
        $('#openRFQsmargin').text((item.OpenRFQsmargin || 0));

        $('#strategicQuotesWonQty').text(item.StrategicQuotesWonQty || '0');
        $('#strategicQuotesLostQty').text(item.StrategicQuotesLostQty || '0');
        $('#strategicQuotesCancelledQty').text(item.StrategicQuotesCancelledQty || '0');
        $('#strategicQuotesWonTotal').text((item.StrategicQuotesWonTotal || 0));
        $('#strategicQuotesLostTotal').text((item.StrategicQuotesLostTotal || 0));
        $('#strategicQuotesCancelledTotal').text((item.StrategicQuotesCancelledTotal || 0));
        $('#strategicQuotesWonRate').text((item.StrategicQuotesWonRate || 0));
        $('#strategicQuotesLostRate').text((item.StrategicQuotesLostRate || 0));
        $('#strategicQuotesCancelledRate').text((item.StrategicQuotesCancelledRate || 0));

        $('#salesThisMonthQty').text(item.SalesThisMonthQty || '0');
        $('#salesLastMonthQty').text(item.SalesLastMonthQty || '0');
        $('#salesThisMonthTotal').text((item.SalesThisMonthTotal || 0));
        $('#salesThisMonthMargin').text((item.SalesThisMonthMargin || 0));
        $('#salesLastMonthTotal').text((item.SalesLastMonthTotal || 0));
        $('#salesLastMonthMargin').text((item.SalesLastMonthMargin || 0));
        $('#lastRefreshedDate').text((item.LastRefreshedDate || 0));

        $('#myQuotePipeline').text((item.MyQuotePipeline === "$0.00" ? "0" : item.MyQuotePipeline || 0));

        $('#MyTaskstile').text((item.DueNext10Days || 0));

        $('.Contact-input-trend').append(item.ContactInputTrend);
        $('.SalesVisit-Input-Trends').append(item.SalesVisitInputTrends);
        $('.Opportunity-Input-Trends').append(item.OpportunityInputTrends);
        $('.Strategic-Quote-Input-Trend').append(item.StrategicQuoteInputTrend);
        $('.Bookings-Input-Trend').hide().append(item.BookingsInputTrend);
        $('.Sales-Input-Trend').hide().append(item.SalesInputTrend);

        // Clear and populate overdue account list
        $('.text-left-i-Accounts').empty();
        $.each(item.OverdueAccountList.slice(0, 3) || [], function (index, account) {
            var accountDiv = `
         <div class="name-title-info-item" style= "padding-top: 6px !important;padding-bottom: 6px !important">
        <a href="#" onclick="GoDetailsPage('CO', '${account.GID_ID}')">
            <div class="Main-name">${account.TXT_CompanyName}</div>
            <div class="Sub-name">${account.Txt_AddrMailing}| ${account.Txt_CityMailing}| ${account.Txt_StateMailing}| ${account.txt_ZipMailing}</div>
        </a>
        </div>`;
            $('.text-left-i-Accounts').append(accountDiv);
        });
        // Clear and populate overdue contacts list
        $('.text-left-i-contacts').empty();
        $.each(item.OverdueContactsList || [], function (index, contact) {
            // var contactDiv = `<div><a href="#" onclick="GoDetailsPage('CN', '${contact.GID_ID}')">${contact.SYS_NAME}</a></div>`;
            var contactDiv = `
                     <div class="name-title-info-item" style= "padding-top: 6px !important;padding-bottom: 6px !important">
                     <a href="#" onclick="GoDetailsPage('CN', '${contact.GID_ID}')">
                     <div class="Main-name">${contact.Txt_NameFirst}|${contact.Txt_NameLast}|${contact.TXT_COMPANYNAMETEXT}</div>
                     <div class="Sub-name">${contact.Txt_AddrMailing}|${contact.Txt_MailingCity}|${contact.Txt_MailingState}|${contact.txt_MailingZip}</div>
                     </a>
                     </div>`;
            $('.text-left-i-contacts').append(contactDiv);
        });

        // Clear and populate overdue tasks list
        $('.text-left-i-Tasks').empty();
        $.each(item.OverdueTasksList || [], function (index, task) {
            var taskDiv = `
           <div class="name-title-info-item" style= "padding-top: 6px !important;padding-bottom: 6px !important">
            <a href="#" onclick="openRecord('TD', '${task.GID_ID}')">
           <div class="Main-name">${task.TXT_DESCRIPTION}</div>
           <div class="Sub-name">${task.TXT_FULLNAME}|${task.DTT_STARTDATE}|${task.DTT_DUETIME}|${task.MLS_TYPE}</div>
            </a>
            </div>`;
            $('.text-left-i-Tasks').append(taskDiv);
        });

        // Overdue Activities (Sales Visit)
        $('.text-left-i-activitiesSV').empty();
        $.each(item.OverdueActivitiesSalesVisitList || [], function (index, acsalesvisit) {
            //var acsalesvisitDiv = `<div><a href="#" onclick="openRecord('AC', '${acsalesvisit.GID_ID}')">${acsalesvisit.SYS_NAME}</a></div>`;
            var acsalesvisitDiv = `
          <div class="name-title-info-item" style= "padding-top: 6px !important;padding-bottom: 6px !important">
           <a href="#" onclick="openRecord('AC', '${acsalesvisit.GID_ID}')">
            <div class="Main-name">${acsalesvisit.MLS_TYPE}|${acsalesvisit.DTT_STARTTIME}|${acsalesvisit.TXT_FULLNAME}</div>
             <div class="Sub-name">${acsalesvisit.MMO_History}</div>
          </a>
          </div>`;
            $('.text-left-i-activitiesSV').append(acsalesvisitDiv);
        });

        //// Overdue Activities
        $('.text-left-i-activities').empty();
        $.each(item.OverdueActivitiesList || [], function (index, activities) {
            //var activitiesDiv = `<div><a href="#" onclick="openRecord('AC', '${activities.GID_ID}')">${activities.SYS_NAME}</a></div>`;
            var activitiesDiv = `
          <div class="name-title-info-item" style= "padding-top: 6px !important;padding-bottom: 6px !important">
           <a href="#" onclick="openRecord('AC', '${activities.GID_ID}')">
            <div class="Main-name">${activities.MLS_TYPE}|${activities.DTT_STARTTIME}|${activities.TXT_FULLNAME}|${activities.TXT_COMPANYNAME}</div>
             <div class="Sub-name">${activities.MMO_History}</div>
           </a>
            </div>`;
            $('.text-left-i-activities').append(activitiesDiv);
        });

        // Overdue Opportunities
        $('.text-left-i-opps').empty();
        $.each(item.OverdueOppsList || [], function (index, opps) {
            //var oppsDiv = `<div><a href="#" onclick="GoDetailsPage('OP', '${opps.GID_ID}')">${opps.SYS_NAME}</a></div>`;
            var oppsDiv = `
            <div class="name-title-info-item" style= "padding-top: 6px !important;padding-bottom: 6px !important">
         <a href="#" onclick="GoDetailsPage('OP', '${opps.GID_ID}')">
           <div class="Main-name">${opps.TXT_FULLNAME}|${opps.TXT_COMPANYNAME}|${opps.TXT_OPPORTUNITYNAME}</div>
            <div class="Sub-name">${opps.MLS_OPPORTUNITYTYPE}| ${opps.MLS_SALESPROCESSSTAGE}| ${opps.DTT_CREATIONTIME}| ${opps.DTT_EXPCLOSEDATE}|${opps.CUR_OPPLINEVALUE}</div>
          </a>
         </div>`;
            $('.text-left-i-opps').append(oppsDiv);
        });

        // Overdue Quotes
        $('.text-left-i-quotes').empty();
        $.each(item.OverdueQuotesList || [], function (index, quote) {
            //var quoteDiv = `<div><a href="#" onclick="openRecord('QT', '${quote.GID_ID}')">${quote.SYS_NAME}</a></div>`;
            var quoteDiv = `
              <div class="name-title-info-item" style= "padding-top: 6px !important;padding-bottom: 6px !important">
           <a href="#" onclick="openRecord('QT', '${quote.GID_ID}')">
            <div class="Main-name">${quote.TXT_QUOTENO}|${quote.TXT_DESCRIPTION}|${quote.TXT_FULLNAME}|${quote.TXT_COMPANYNAME}</div>
           <div class="Sub-name">${quote.MLS_STATUS}| ${quote.CUR_TOTALAMOUNT}| ${quote.DTT_EXPCLOSEDATE}| ${quote.DTT_VALIDUNTILDATE}</div>
         </a>
       </div>`;
            $('.text-left-i-quotes').append(quoteDiv);
        });
        $(window).on('load', function () {
            hideProgress();
        });

        $(window).on('pageshow', function (event) {
            if (event.originalEvent.persisted) {
                hideProgress();
            }
        });
    }
    else {
        console.log("No data returned.");
    }
    hideProgress();
        },

error: function (xhr, status, error) {
    console.log("Error: ", error);
    $('#accountQty').text('Error');
    $('#teamSellAccountsQty').text('Error');
    $('#overservedAccountsQty').text('Error');
    $('#newContactsQty').text('Error');
}
    });
    }
    function GoDetailsPage(file, gid) {
        if (file == 'CO') {
            // window.location = "/DetailsPage/CompanyDetails/?sCompanyId=" + gid;

            window.location = "/DetailsPage/PRF?sPRPId=PRF_CO&sRecId=" + gid
        }
        else if (file == 'CN') {
            //window.location = "/DetailsPage/ContactDetails/?sContactId=" + gid;
            window.location = "/DetailsPage/PRF?sPRPId=PRF_CN&sRecId=" + gid
        }
        else if (file == 'OP') {
            //window.location = "/DetailsPage/OPDetails/?sOPId=" + gid;
            window.location = "/DetailsPage/PRF?sPRPId=PRF_OP&sRecId=" + gid
        }
    }
    function openRecord(tablename, gid_id) {

        window.location = "/CreateForm/CreateForm/" + tablename + "/" + gid_id + "/TYPE/false/false/KEY/false/MASTERID/FORMKEY/FIELD/"

    }
    function RefreshData() {
        showProgress();
        $.ajax({
            url: '/DetailsPage/RefreshOutsideSalesDashboardData',
            type: 'POST',
            dataType: 'json',
            success: function (response) {
                if (response.success) {

                    window.location = "/DetailsPage/Outsidesalesdashboard"
                    //gotoDesktop('DSK_3E971649-F608-49AD-5858-B20C00C8201E', '', '', '')
                } else {

                }
            },

        });

    }
     function PrepareFormLink(tableName, type) {
    ////debugger;

    if (navigator.onLine == false) {
        hideProgress();
        alert('Check your Internet Connection');
        return false;
    }

    showProgress();

    window.location = "/CreateForm/CreateForm/" + tableName + "/ID/" + type + "/false/false/KEY/false/MASTERID/FORMKEY/FIELD/";

    }

</script>
<script>
    var spinnerVisible = false;
    function showProgress() {
        if (!spinnerVisible) {
            $("div#spinnertool").fadeIn("fast");
            spinnerVisible = true;
        }
    };
    function hideProgress() {
        if (spinnerVisible) {
            var spinner = $("div#spinnertool");
            spinner.stop();
            spinner.fadeOut("fast");
            spinnerVisible = false;
        }
    };
</script>
