﻿CREATE TABLE [dbo].[VE_RELATED_BU] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_VE_RELATED_BU_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_VE] UNIQUEIDENTIFIER NOT NULL,
    [GID_BU] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_VE_RELATED_BU] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_BU_CONNECTED_VE] FOREIGN KEY ([GID_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_VE_RELATED_BU] FOREIGN KEY ([GID_BU]) REFERENCES [dbo].[BU] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[VE_RELATED_BU] NOCHECK CONSTRAINT [LNK_BU_CONNECTED_VE];


GO
ALTER TABLE [dbo].[VE_RELATED_BU] NOCHECK CONSTRAINT [LNK_VE_RELATED_BU];


GO
CREATE NONCLUSTERED INDEX [IX_VE_RELATED_BU]
    ON [dbo].[VE_RELATED_BU]([GID_VE] ASC, [GID_BU] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_BU_CONNECTED_VE]
    ON [dbo].[VE_RELATED_BU]([GID_BU] ASC, [GID_VE] ASC);

