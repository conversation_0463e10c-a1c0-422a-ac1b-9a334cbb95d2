﻿CREATE TABLE [dbo].[GL] (
    [GID_ID]               UNIQUEIDENTIFIER CONSTRAINT [DF_GL_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),(0))+CONVERT([binary](2),'GL',(0)))+CONVERT([binary](6),getutcdate(),(0)),(0))) ROWGUIDCOL NOT NULL,
    [BI__ID]               BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]             NVARCHAR (80)    NULL,
    [DTT_CreationTime]     DATETIME         CONSTRAINT [DF_GL_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]         TINYINT          NULL,
    [TXT_ModBy]            VARCHAR (4)      NULL,
    [DTT_ModTime]          DATETIME         CONSTRAINT [DF_GL_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_GoalName]         NVARCHAR (50)    NULL,
    [MMO_ImportData]       NTEXT            NULL,
    [SI__ShareState]       TINYINT          CONSTRAINT [DF_GL_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]     UNIQUEIDENTIFIER NULL,
    [GID_For_LO]           UNIQUEIDENTIFIER NULL,
    [GID_For_CO]           UNIQUEIDENTIFIER NULL,
    [GID_For_DV]           UNIQUEIDENTIFIER NULL,
    [GID_For_GR]           UNIQUEIDENTIFIER NULL,
    [GID_For_PD]           UNIQUEIDENTIFIER NULL,
    [GID_For_TE]           UNIQUEIDENTIFIER NULL,
    [GID_For_US]           UNIQUEIDENTIFIER NULL,
    [GID_For_VE]           UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]         VARCHAR (50)     NULL,
    [DTT_LastAnalysis]     DATETIME         NULL,
    [MMO_LastResult]       NTEXT            NULL,
    [TXT_ModelText]        VARCHAR (30)     NULL,
    [MLS_ActivityType]     SMALLINT         NULL,
    [MLS_ApptType]         SMALLINT         NULL,
    [LI__GoalAC]           INT              NULL,
    [LI__GoalLE]           INT              NULL,
    [LI__GoalAP]           INT              NULL,
    [LI__GoalOP]           INT              NULL,
    [CUR_GoalOP]           MONEY            NULL,
    [LI__GoalQT]           INT              NULL,
    [CUR_GoalQL]           MONEY            NULL,
    [LI__GoalOH]           INT              NULL,
    [CUR_GoalOH]           MONEY            NULL,
    [CHK_MyAccounts]       TINYINT          NULL,
    [CHK_MyTargetAccounts] TINYINT          NULL,
    [TXT_ExternalID]       NVARCHAR (120)   NULL,
    [TXT_ExternalSource]   VARCHAR (10)     NULL,
    [TXT_ImpJobID]         VARCHAR (20)     NULL,
    CONSTRAINT [PK_GL] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);

