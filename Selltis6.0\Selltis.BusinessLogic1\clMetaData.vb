Imports System.Web

'Owner: MI since 5/8/06. Was WT.

Public Class clMetaData

    Dim goP As clProject
    Dim goMeta As clMetaData
    Dim goTR As clTransform
    Dim goData As clData
    Dim goErr As clError
    Dim goLog As clLog
    Dim goDef As clDefaults
    ' using local variable. deprecated oMetaConnection member variable - MN 1/22/2018
    ' Public oMetaConnection As SqlClient.SqlConnection = Nothing





    Public Function DeleteMetaObject(ByVal par_sSectionID As String, _
                    ByVal par_sPageID As String, _
                    Optional ByVal par_bSilent As Boolean = False, _
                    Optional ByVal par_sProduct As String = "") As Integer
        'MI 1/16/09 Added par_sProduct.
        'MI 5/7/08 Added test for PERMSHAREDDESKTOPS
        'MI 12/5/06 Ported from NGP.

        'PURPOSE:
        '   Delete the _META record for the metadata object if WG permissions
        '   allow the user to do so. 
        '   First, the parameters are validated, then user permissions are
        '   checked. In NGP, if the user had permissions to delete the object,
        '   we asked the user whether to delete unless par_b<PERSON>ile<PERSON> was True.
        '   in SellSQL, the deletion occurs without user interaction.
        '   If everything passes we delete the object.
        'PARAMETERS:
        '   par_sSectionID: Metadata "section" ID of the object. Supported:
        '       36-character GID_ID of the user who created the object or
        '       "GLOBAL" or "" to delete a shared object.
        '   par_sObjectID: Page ID of the metadata object, for example
        '       "DSK_Xxxxxxxxx" or "POP_PERSONL_OPTIONS".
        '   par_bSilent: *** NOT SUPPORTED *** Always False.
        '       In NGP, this parameter allowed this method to interact
        '       with the user.
        '   par_sProduct: optional: product code. If nothing or "",
        '       the current session product will be used.
        'RETURNS:
        '   Integer:
        '       1 if the object was deleted or wasn't found;
        '       0 if the object was not found or deletion erred;
        '       -1 if the user didn't have permission to delete the object;
        '       -2 if deleting dependent objects failed.
        '       -1 can be returned when the object doesn't exist, for example
        '       when the user is a partial author. In this case the Creator ID
        '       of the object is "" and that doesn't match the User's ID.
        '       If the user is not an author, deleting any
        '       GLOBAL page will fail and -1 will be returned. Likewise,
        '       a partial author (goP.IsUserAuthor returning 1) can't delete
        '       pages (s)he didn't create.
        '       The result of the deletion of related objects is not
        '       reported.

        Dim sProc As String = "clUI::DeleteMetaObject"

        '==> Not finished, must add considering user permissions

        'Delete _META record under GLOBAL section if shared,
        'under username section if not shared.
        Dim sPageID As String = Trim(par_sPageID.ToString)
        Dim sSectionID As String = Trim(par_sSectionID.ToString)
        Dim sMetaType As String
        Dim sVals As String
        Dim sUserID As String
        Dim iResult As Integer = 1
        Dim sProduct As String

        Dim iViewCount As Integer
        Dim i As Integer
        Dim sViewID As String
        Dim sTemplFile As String

        par_bSilent = True

        sMetaType = goDef.GetMetaTypeFromPrefix(Left(sPageID, 4))
        sUserID = goP.GetUserTID()

        If par_sProduct Is Nothing Then
            sProduct = ""
        Else
            sProduct = UCase(par_sProduct)
        End If
        If sProduct = "" Then
            sProduct = goP.GetProduct()
        End If
        If sProduct = "" Then
            goErr.SetError(35000, sProc, "Product is not initialized. goP.GetProduct returned ''.")
        End If

        '=============== CHECK PERMISSIONS, ASK USER ================
        If sSectionID = "GLOBAL" Or sSectionID = "" Then       'Object is shared
            If goP.GetMe("PERMSHAREDDESKTOPS") = "1" Then
                'Like full author
                'If Not par_bSilent Then
                '    Select Case (Left(sPageID, 4))
                '        Case "DBB_"
                '            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
                '            If Not YesNo(goTR.MessComplete(MessTranslate(5010), sMetaType, sName)) Then
                '                '5010:Are you sure you want to delete the shared [1] '[2]' and all of its Shortcuts?
                '                '
                '                'This will affect all workgroup users.
                '                Return (False)
                '            End If
                '        Case "DBS_"
                '            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
                '            If Not YesNo(goTR.MessComplete(MessTranslate(5011), sMetaType, sName)) Then
                '                '5011:Are you sure you want to remove the shared [1] '[2]'?
                '                '
                '                'This will affect all workgroup users.
                '                Return (False)
                '            End If
                '        Case "SND_"
                '            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
                '            If Not YesNo(goTR.MessComplete(MessTraduit(5072), sMetaType, sName)) Then
                '                '5072:Are you sure you want to delete the shared [1] '[2]'?
                '                '
                '                'The template file will be deleted as well.
                '                '
                '                'This will affect all workgroup users.
                '                Return (False)
                '            End If
                '        Case Else
                '            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
                '            If Not YesNo(goTR.MessComplete(MessTranslate(5003), sMetaType, sName)) Then
                '                '5003:Are you sure you want to delete the shared [1] '[2]'?
                '                '
                '                'This will affect all workgroup users.
                '                Return (False)
                '            End If
                '    End Select
                'End If
            Else
                Select Case goP.IsUserAuthor()
                    Case 1      'Partial author
                        '   IF goTr:StrRead(sVals,"CREATORID","",False) = sUserID THEN
                        If UCase(goMeta.GetCreatorID(par_sSectionID, par_sPageID, sProduct)) = UCase(sUserID) Then
                            'User is creator of this object, allow deleting
                            'If Not par_bSilent Then
                            '    Select Case (Left(sPageID, 4))
                            '        Case "DBB_"     'Dock Bar Button
                            '            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
                            '            If Not YesNo(goTR.MessComplete(MessTranslate(5010), sMetaType, sName)) Then
                            '                '5010:Are you sure you want to delete the shared [1] '[2]' and all of its shortcuts?
                            '                '
                            '                'This will affect all workgroup users.
                            '                Return (False)
                            '            End If
                            '        Case "DBS_"     'Dock Bar Shortcut
                            '            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
                            '            If Not YesNo(goTR.MessComplete(MessTranslate(5011), sMetaType, sName)) Then
                            '                '5011:Are you sure you want to remove the shared [1] '[2]'?
                            '                '
                            '                'This will affect all workgroup users.
                            '                Return (False)
                            '            End If
                            '        Case "SND_"
                            '            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
                            '            If Not YesNo(goTR.MessComplete(MessTraduit(5072), sMetaType, sName)) Then
                            '                '5072:Are you sure you want to delete the shared [1] '[2]'?
                            '                '
                            '                'The template file will be deleted as well.
                            '                '
                            '                'This will affect all workgroup users.
                            '                Return (False)
                            '            End If
                            '        Case Else
                            '            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
                            '            If Not YesNo(goTR.MessComplete(MessTranslate(5003), sMetaType, sName)) Then
                            '                '5003:Are you sure you want to delete the shared [1] '[2]'?
                            '                '
                            '                'This will affect all workgroup users.
                            '                Return (False)
                            '            End If
                            '    End Select
                            'End If
                        Else
                            'User not creator, not allowed to delete.
                            'If Not par_bSilent Then
                            '    NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
                            '    Info(goTR.MessComplete(MessTranslate(5005), sMetaType))
                            '    '5005:You are not allowed to delete this shared [1] because you didn't create it.
                            'End If
                            Return -1
                        End If

                    Case 2      'Full author
                        'If Not par_bSilent Then
                        '    Select Case (Left(sPageID, 4))
                        '        Case "DBB_"
                        '            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
                        '            If Not YesNo(goTR.MessComplete(MessTranslate(5010), sMetaType, sName)) Then
                        '                '5010:Are you sure you want to delete the shared [1] '[2]' and all of its Shortcuts?
                        '                '
                        '                'This will affect all workgroup users.
                        '                Return (False)
                        '            End If
                        '        Case "DBS_"
                        '            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
                        '            If Not YesNo(goTR.MessComplete(MessTranslate(5011), sMetaType, sName)) Then
                        '                '5011:Are you sure you want to remove the shared [1] '[2]'?
                        '                '
                        '                'This will affect all workgroup users.
                        '                Return (False)
                        '            End If
                        '        Case "SND_"
                        '            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
                        '            If Not YesNo(goTR.MessComplete(MessTraduit(5072), sMetaType, sName)) Then
                        '                '5072:Are you sure you want to delete the shared [1] '[2]'?
                        '                '
                        '                'The template file will be deleted as well.
                        '                '
                        '                'This will affect all workgroup users.
                        '                Return (False)
                        '            End If
                        '        Case Else
                        '            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
                        '            If Not YesNo(goTR.MessComplete(MessTranslate(5003), sMetaType, sName)) Then
                        '                '5003:Are you sure you want to delete the shared [1] '[2]'?
                        '                '
                        '                'This will affect all workgroup users.
                        '                Return (False)
                        '            End If
                        '    End Select
                        'End If
                    Case Else   '0: not author or unsupported value
                        'If Not par_bSilent Then
                        '    NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
                        '    Info(goTR.MessComplete(MessTranslate(5002), sMetaType))
                        '    '5002:You are not allowed to delete this shared [1].
                        'End If
                        Return -1
                End Select                         'User is author
            End If
        Else                            'Object is local
            'The 'authorship' of local objects goes by their SECTION, not Creator User ID so that
            'MD can be created for another .
            If UCase(sSectionID) = UCase(sUserID) Then
                'Allowed to delete
                'If Not par_bSilent Then
                '    Select Case (Left(sPageID, 4))
                '        Case "DBB_"
                '            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
                '            If Not YesNo(goTR.MessComplete(MessTranslate(5009), sMetaType, sName)) Then
                '                '5009:Are you sure you want to delete the [1] '[2]' and all of its shortcuts?
                '                Return (False)
                '            End If
                '        Case "DBS_"
                '            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
                '            If Not YesNo(goTR.MessComplete(MessTranslate(5012), sMetaType, sName)) Then
                '                '5012:Are you sure you want to remove the [1] '[2]'?
                '                Return (False)
                '            End If
                '        Case "SND_"
                '            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
                '            If Not YesNo(goTR.MessComplete(MessTraduit(5076), sMetaType, sName)) Then
                '                '5076:Are you sure you want to delete the [1] '[2]'?
                '                '
                '                'The template file will be deleted as well.
                '                Return (False)
                '            End If
                '        Case Else
                '            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
                '            If Not YesNo(goTR.MessComplete(MessTranslate(5004), sMetaType, sName)) Then
                '                '5004:Are you sure you want to delete the [1] '[2]'?
                '                Return (False)
                '            End If
                '    End Select
                'End If
            Else
                'Not allowed to delete
                Return -1
            End If
        End If


        '=================== DELETE DEPENDENT RECORDS OR OBJECTS ===============
        'Delete dependent records, if any.
        Select Case (Left(sPageID, 4))

            Case "DSK_"     'Desktop: delete all Desktop's Views
                sVals = goMeta.PageRead(sSectionID, sPageID, "", True, sProduct)
                'Mini Views
                iViewCount = goTR.StrRead(sVals, "VIEWCOUNT", "0", False)        'No of mini views
                i = 1
                For i = 1 To iViewCount
                    sViewID = goTR.StrRead(sVals, "VIEW" & i & "ID", "", False)
                    If Not goMeta.PageDelete(sSectionID, sViewID, sProduct) Then
                        Return -2
                    End If
                Next i
                'Tab Views
                iViewCount = goTR.StrRead(sVals, "TABCOUNT", "0", False)     'No of mini views
                i = 1
                For i = 1 To iViewCount
                    sViewID = goTR.StrRead(sVals, "TAB" & i & "VIEWID", "", False)
                    If Not goMeta.PageDelete(sSectionID, sViewID, sProduct) Then
                        Return -2
                    End If
                Next i

                'Case "DBB_"     'Dock Bar Button: delete all Dock Bar Shortcuts under the button
                '   '*** DBBs are not supported anymore. Now we use WCBs and no shortcuts. 
                '   '*** DSK_ metadata directly references DBBs via the WEBCLIENTBUTTONID parameter.
                '   '*** This parameter could be cleaned up in the future.
                '    sPage = "DBS_"
                '    '---------- SHARED DOCK BAR SHORTCUTS -----------
                '    sSection = "GLOBAL"
                '    sSearchStringFrom = goTR.Complete(sSection, UBound(_META.TXT_Section)) & _
                '    sPage(+RepeatString(Chr(0), (UBound(_META.TXT_Page) - Len(sPage))))
                '    sSearchStringTo = goTR.Complete(sSection, UBound(_META.TXT_Section)) & _
                '    sPage(+RepeatString(Chr(255), (UBound(_META.TXT_Page) - Len(sPage))))
                '    lFilter = goData.HFilter("_META", "UnikKey", sSearchStringFrom, sSearchStringTo)
                '    If goData.HReadFirst(lFilter) Then
                '        Do
                '            sPageMemo = goTR.FieldToString("_META.MMO_MemoValue")
                '            If goTR.StrRead(sPageMemo, "BUTTONID", "", False) = sPageID Then
                '                If Not goData.HDelete(lFilter) Then
                '                    Info(goTR.MessComplete(MessTraduit(5067), goLog.GetLastError(), _
                '                    MessTraduit(5069), _
                '                    goLog.GetLastError(clC.SELL_ERROR_MESSAGE), _
                '                    sMetaType))
                '                    '5069:a Dock Bar Shortcut
                '                    '5067:Error [1] occurred while deleting [2]:
                '                    '
                '                    '[3]
                '                    '
                '                    'The [4] wasn't deleted.
                '                    goLog.SetError()
                '                    goData.HDeactivateFilter(lFilter)
                '                    Return (False)
                '                End If
                '            End If
                '            If Not goData.HReadNext(lFilter) Then Exit Do
                '        Loop
                '    End If
                '    goData.HDeactivateFilter(lFilter)
                '    '---------- LOCAL DOCK BAR SHORTCUTS -----------
                '    sSection = sUserID
                '    sSearchStringFrom = goTR.Complete(sSection, UBound(_META.TXT_Section)) & _
                '                  sPage(+RepeatString(Chr(0), (UBound(_META.TXT_Page) - Len(sPage))))
                '    sSearchStringTo = goTR.Complete(sSection, UBound(_META.TXT_Section)) & _
                '                  sPage(+RepeatString(Chr(255), (UBound(_META.TXT_Page) - Len(sPage))))
                '    lFilter = goData.HFilter("_META", "UnikKey", sSearchStringFrom, sSearchStringTo)
                '    If goData.HReadFirst(lFilter) Then
                '        Do
                '            sPageMemo = goTR.FieldToString("_META.MMO_MemoValue")
                '            If goTR.StrRead(sPageMemo, "BUTTONID", "", False) = sPageID Then
                '                If Not goData.HDelete(lFilter) Then
                '                    Info(goTR.MessComplete(MessTraduit(5067), goLog.GetLastError(), _
                '                     MessTraduit(5069), _
                '                     goLog.GetLastError(clC.SELL_ERROR_MESSAGE), _
                '                     sMetaType))
                '                    '5069:a Dock Bar Shortcut
                '                    '5067:Error [1] occurred while deleting [2]:
                '                    '
                '                    '[3]
                '                    '
                '                    'The [4] wasn't deleted.
                '                    goLog.SetError()
                '                    goData.HDeactivateFilter(lFilter)
                '                    Return (False)
                '                End If
                '            End If
                '            If Not goData.HReadNext(lFilter) Then Exit Do
                '        Loop
                '    End If
                '    goData.HDeactivateFilter(lFilter)

            Case "SND_"
                sTemplFile = goMeta.LineRead(sSectionID, sPageID, "TEMPLFILE", "", True, sProduct)
                '--------- Delete the template file ----------
                '==> Implement deleting the file itself and queuing it for syncing as appropriate

                'If goDF.fDelete(goTR.AddFinalSlash(goData.GetDBPath()) & "SendTmpl\" & sTemplFile) = -1 Then
                '    'Deletion of the template file failed
                '    Info(goTR.MessComplete(MessTraduit(5074), goTR.AddFinalSlash(goData.GetDBPath()) & "SendTmpl\" & sTemplFile))    '*** MI 5/25/05
                '    '5074:Send Template file '[1]' couldn't be deleted.
                '    '
                '    'If the file exists, make sure it is not open or locked, then delete it manually.
                'End If
                ''---------- Sync deletion of the template file -----------
                'If sSectionID = "GLOBAL" Then       'Object is shared
                '    sRecordID = goMeta.GetRecordID(sSectionID, sPageID)
                '    goLog.SetError()
                '    If sTemplFile <> "" Then
                '        If Not goData.WriteFileToSync(sRecordID, "FIL_", "<%DBSendTmplPath%>", sTemplFile, clC.SELL_FILE_DELETE) Then
                '            goLog.DisplayLastError()
                '            goLog.SetError()
                '        End If
                '    End If
                'End If
        End Select


        '=================== DELETE THE RECORD ======================
        'Delete the record. Make sure the code above this exits the method
        'by returning Return False if the user doesn't have adequate
        'permissions.

        If Not goMeta.PageDelete(sSectionID, sPageID, sProduct) Then
            Return 0
        End If

        Return iResult


    End Function

    Public Function DeleteOldAlerts(Optional ByVal par_sUserID As String = "", Optional ByVal par_iDaysOld As Integer = 8) As Boolean
        'MI 2/5/07 Created.
        'AUTHOR: MI 2/5/07
        'PURPOSE:
        '		Delete ALT_ metadata older than 30 days for a particular user or for
        '       all users (and GLOBAL, if any). This calls a SS sproc. 
        'PARAMETERS:
        '		par_sUserID: optional: User's TID as string or "" to delete all odl ALT_ MD.
        '       par_iDaysOld: optional: Number of days an alert has to have been created prior to
        '           now in order to be deleted. Default: 8.
        'RETURNS:
        '		True if successful, False if not.

        Dim sProc As String = "clMetaData::DeleteOldAlerts"
        Dim sUserID As String = Trim(UCase(par_sUserID))
        Dim iResult As Integer = 0

        Try

            Dim oConnection As SqlClient.SqlConnection = goData.GetConnection
            Dim oCommand As New SqlClient.SqlCommand
            Dim oReader As SqlClient.SqlDataReader

            oCommand.CommandText = "pMetaDeleteAlerts"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter
            Dim uSection As New SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier)
            If sUserID = "GLOBAL" Or sUserID = "" Then
                uSection.Value = System.DBNull.Value
            Else
                uSection.Value = StringToGuid(sUserID)
            End If
            oCommand.Parameters.Add(uSection)

            'parameter
            Dim strPage As New SqlClient.SqlParameter("@par_iDays", SqlDbType.Int)
            strPage.Value = par_iDaysOld
            oCommand.Parameters.Add(strPage)

            'return parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            'execute

            oReader = oCommand.ExecuteReader()

            'Now you can grab the output parameter's value...
            iResult = Convert.ToInt16(retValParam.Value)

            oReader.Close()
            oConnection.Close()

            If iResult = 0 Then
                Return True
            Else
                Return False 'no error checking in sproc yet
            End If

        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return False
        End Try

    End Function


    Public Function GetCreatorID(ByVal par_sSection As String, _
                    ByVal par_sPageID As String, _
                    Optional ByVal par_sProduct As String = "") As String
        'MI 1/21/09 Added par_sProduct.
        'MI 5/16/07 Added dealing with nothing sent in Section and Page ID
        'MI 4/1/07 Added Try Catch
        'MI 12/5/06 Created
        'PURPOSE:
        '       Return the ID of the user who created this metadata page.
        'PARAMETERS:
        '       par_sSection: Section ID of the page such as user's GID_ID
        '           or "GLOBAL"
        '       par_sPageID: page ID of the page such as "DSK_Xxxxxx" or
        '           'WOP_WORKGROUP_OPTIONS"
        '       par_sProduct: product code like 'SA', 'MB', 'WP'. 'XX' is a
        '           code for pages that apply to all products. If not defined,
        '           product is taken from the session. If not defined in the
        '           session, 'SA' will be used.
        'RETURNS:
        '       GID_CreatedBy_US value as string or "" if the page is not found
        '       or the Creator ID is for some reason blank. No error raised.

        Dim sProc As String = "clMetaData::GetCreatorID"
        Dim sSection As String
        Dim sPageIDVal As String
        Dim sText As String = ""

        'Try
        If par_sSection Is Nothing Then
                sSection = ""
            Else
                sSection = par_sSection.ToString
            End If
            If par_sPageID Is Nothing Then
                sPageIDVal = ""
            Else
                sPageIDVal = par_sPageID.ToString
            End If
            Dim sqlConnection1 As SqlClient.SqlConnection = goData.GetConnection
            Dim cmd As New System.Data.SqlClient.SqlCommand
            Dim reader As SqlClient.SqlDataReader
            Dim mColl As New Collection

            cmd.CommandText = "pMetaGetCreatorID"
            'Returns column GID_CreatedBy_US with one record
            cmd.CommandType = CommandType.StoredProcedure
            cmd.Connection = sqlConnection1

            'parameter
            Dim uSec As New System.Data.SqlClient.SqlParameter("@par_uSection", System.Data.SqlDbType.UniqueIdentifier)
            If UCase(sSection) = "GLOBAL" Or sSection = "" Then
                uSec.Value = System.DBNull.Value
            Else
                uSec.Value = goTR.StringToGuid(sSection)
            End If
            cmd.Parameters.Add(uSec)

            'parameter
            Dim sPageID As New System.Data.SqlClient.SqlParameter("@par_sPageID", System.Data.SqlDbType.VarChar)
            sPageID.Value = sPageIDVal
            cmd.Parameters.Add(sPageID)

            Dim sProduct As New System.Data.SqlClient.SqlParameter("@par_sProduct", System.Data.SqlDbType.VarChar)
            sProduct.Value = par_sProduct
            cmd.Parameters.Add(sProduct)

            reader = cmd.ExecuteReader()
            If reader.Read() Then
                sText = reader(0).ToString
            End If

            reader.Close()
            sqlConnection1.Close()
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return sText

    End Function


    Public Function GetModificationInfo(ByVal par_sSection As String, _
                ByVal par_sPageID As String, _
                Optional ByVal par_sProduct As String = "") As String
        'MI 6/16/09 Created.
        'PURPOSE:
        '       Return the User code and datetime of the last modification
        '       of the metadata page. The information is read from the 
        '       most recently modified line. 
        'PARAMETERS:
        '       par_sSection: Section ID of the page such as user's GID_ID
        '           or "GLOBAL".
        '       par_sPageID: page ID of the page such as "DSK_Xxxxxx" or
        '           'WOP_WORKGROUP_OPTIONS".
        '       par_sProduct: product code like 'SA', 'MB', 'WP'. 'XX' is a
        '           code for pages that apply to all products. If not defined,
        '           product is taken from the session. If not defined in the
        '           session, 'SA' will be used.
        'RETURNS:
        '       US.TXT_Code value of the User who last modified the page & " " &
        '       datetime of modification (in yyyy-mm-dd hh:mm:ss.mmm format) as string
        '       or "" if the page is not found. No error raised.

        Dim sProc As String = "clMetaData::GetModificationInfo"
        Dim sSection As String
        Dim sPageIDVal As String
        Dim sText As String = ""

        '  Try
        If par_sSection Is Nothing Then
                sSection = ""
            Else
                sSection = par_sSection.ToString
            End If
            If par_sPageID Is Nothing Then
                sPageIDVal = ""
            Else
                sPageIDVal = par_sPageID.ToString
            End If
            Dim sqlConnection1 As SqlClient.SqlConnection = goData.GetConnection
            Dim cmd As New System.Data.SqlClient.SqlCommand
            Dim reader As SqlClient.SqlDataReader
            Dim mColl As New Collection

            cmd.CommandText = "pMetaGetModificationInfo"
            'Returns column GID_CreatedBy_US with one record
            cmd.CommandType = CommandType.StoredProcedure
            cmd.Connection = sqlConnection1

            'parameter
            Dim uSec As New System.Data.SqlClient.SqlParameter("@par_uSection", System.Data.SqlDbType.UniqueIdentifier)
            If UCase(sSection) = "GLOBAL" Or sSection = "" Then
                uSec.Value = System.DBNull.Value
            Else
                uSec.Value = goTR.StringToGuid(sSection)
            End If
            cmd.Parameters.Add(uSec)

            'parameter
            Dim sPageID As New System.Data.SqlClient.SqlParameter("@par_sPageID", System.Data.SqlDbType.VarChar)
            sPageID.Value = sPageIDVal
            cmd.Parameters.Add(sPageID)

            Dim sProduct As New System.Data.SqlClient.SqlParameter("@par_sProduct", System.Data.SqlDbType.VarChar)
            sProduct.Value = par_sProduct
            cmd.Parameters.Add(sProduct)

            reader = cmd.ExecuteReader()
            If reader.Read() Then
                sText = reader(0).ToString
            End If

            reader.Close()
            sqlConnection1.Close()
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return sText

    End Function

    Public Function IsPrefixForProductXX(ByVal par_sPageID As String) As Boolean
        'MI 11/12/09 Added OTH_GOALS.
        'MI 10/20/09 Added FRM.
        'MI 10/19/09 Added LBF.
        'MI 10/15/09 Created.
        'PURPOSE:
        '       Returns whether a page or page prefix (DSK_, VIE_, POP_, OTH_LINKS, LBF_, ...)
        '       should be created only under product XX or under individual 
        '       products (SA or MB). This is called from diaMetaDetails to warn the user
        '       when creating a page under a "wrong" product.
        '       If the page ID doesn't contain _ as 4th character or is otherwise invalid,
        '       False will be returned.
        'PARAMETERS:
        '       par_sPageID: complete metadata page ID or just the prefix (4 chars or more).
        'RETURNS:
        '       Boolean: True when the page should be created only under product XX,
        '           False otherwise.

        Dim sProc As String = "clMetaData::IsPrefixForProductXX"
        Dim sPageID As String

        '  Try
        sPageID = UCase(par_sPageID)
            If goTR.FromTo(sPageID, 4, 4) = "_" Then
                'Page ID has a valid syntax prefix
                Select Case Left(sPageID, 3)
                    Case "ALT", "CRL", "CRU", "CUR", "EXP", "FII", "FLD", "FRM", "IMP", "LNK", "LBF", "LST", "PGL", "POP", "PTR", "SCR", "SEC", "SPC", "WOP"
                        Return True
                End Select
            End If

            If sPageID = "OTH_FIRSTNAMESUBSTITUTES" Then Return True
            If sPageID = "OTH_LINKS" Then Return True
            If sPageID = "OTH_FILES" Then Return True
            If sPageID = "OTH_GOALS" Then Return True

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return False

    End Function

    Public Function IntlStrRead(ByVal par_sMeta As String, ByVal par_sParamName As String) As String
        'MI 1/19/07 Replaced Chr(4) with clc.EOT

        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Returns a string containing all international values for the different supported languages
        'PARAMETERS:
        '		par_sMeta:		String of metadata in which we want to look for the information
        '		par_sParamName:	Name of the paramter we are looking for, with or without prefix (automatically 
        '						removed if present)
        'RETURNS:
        '		A string in the following format:
        '		US=USValue
        '		FR=FRValue... in the order given by goP:GetSupportedLangs
        '		if the IN parameter are wrong, will return at least US=+RC+FR=+RC
        'HOW IT WORKS:
        '		to be used when you need all translations of a given parameter
        'EXAMPLE:
        '		goMeta.IntlStrRead(sString, "NAME") returns: US=Companies+RC+FR=Compagnies+RC

        Dim sProc As String = "clMetaData::IntlStrRead"
        Dim sMeta As String = par_sMeta
        Dim sParamName As String = Trim(par_sParamName)
        Dim sSuppLangs As String = goP.GetSupportedLangs()
        Dim sResult As String = ""
        Dim sLang As String
        Dim iI As Integer = 1

        ' Try
        'remove the language part of the parameter if there
        If Mid(sParamName, 3, 1) = "_" Then sParamName = Mid(sParamName, 4)

            'write the return string
            Do
                sLang = goTR.ExtractString(sSuppLangs, iI)
                If sLang = clC.EOT Then Exit Do
                goTR.StrWrite(sResult, sLang, goTR.StrRead(sMeta, sLang & "_" & sParamName, "", False))
                iI = iI + 1
            Loop

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try


        Return sResult

    End Function

    Public Function IntlStrWrite(ByRef par_sMeta As String, ByVal par_sParamName As String, ByVal par_sIntlString As String) As Boolean

        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Write in a meta string all the international lines for a parameter
        '		starting with a string in the format returned by IntlStrRead
        'PARAMETERS:
        '		par_sMeta:		String of metadata in which we want to write the information
        '		par_sParamName:	Name of the paramter we want to write, with or ithout prefix (automatically 
        '						removed if present). We need this name as the intl string contains only US=... & FR=...
        '		par_sIntlString:The intl content, in the format returned by the IntlStrRead:
        '						US=USValue+RC+FR=FRValue+RC+...
        'RETURNS:
        '		True if OK, false if not
        'HOW IT WORKS:
        '		to be used when you need to write all translations of a given parameter
        'EXAMPLE:
        '		goMeta.IntlStrWrite(sString, "NAME", sIntl)

        Dim sProc As String = "clMetaData::IntlStrWrite"
        Dim sParamName As String = Trim(par_sParamName)
        Dim sIntl As String = par_sIntlString
        Dim sOrder As String = goP.GetSupportedLangs()
        Dim sResult As String = ""
        Dim sLang As String
        Dim iI As Integer = 1

        ' Try
        'remove the language part of the parameter if there
        If Mid(sParamName, 3, 1) = "_" Then sParamName = Mid(sParamName, 4)

            'write the return string, returned via par_sMeta
            Do
                sLang = goTR.ExtractString(sOrder, iI)
                If sLang = Chr(4) Then Exit Do
                If Not goTR.StrWrite(par_sMeta, sLang & "_" & sParamName, goTR.StrRead(sIntl, sLang, "", False)) Then
                    Return False
                End If
                iI = iI + 1
            Loop

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return True

    End Function

    Public Function IsNameDuplicate(ByVal par_sPagePrefix As String, _
                        ByVal par_sPageID As String, _
                        ByVal par_sNameTrans As String, _
                        Optional ByVal par_sOneLangToSearch As String = "", _
                        Optional ByVal par_sProduct As String = "") As String
        'MI 1/16/09 Added par_sProduct
        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Find the first duplicate Name of a _META page for a particular
        '       product in all languages or just the specified language.
        '		The search is case-insensitive so "Leads" and "LEADS" are 
        '		considered to be duplicates.
        '		If a duplicate is found, return its language, section, page, product.
        '		Duplicates are searched in all shared metadata of the same page prefix
        '       and same product code, and only for the logged-on user's local metadata.
        'PARMETERS:
        '		par_sPagePrefix: prefix with trailing underscore that describes the 
        '			type of metadata to search. Ex: DSK_, VIE_, VTM_, FLT_.
        '		par_sPageID: Page ID of the metadata object for which to find a duplicate.
        '		par_sNameTrans: Translated Name values in ini format:
        '			"US=value"+CR+"FR=value"+CR+...
        '		par_sOneLangToSearch (opt.): language code for which to search for a duplicate.
        '			If blank, search duplicate for all languages. This method will return
        '			only information on the first duplicate found, NOT on all duplicates!
        '       par_sProduct: product code of the page for which to look for duplicates.
        'RETURNS:
        '		If no duplicate found: ""
        '		If duplicate found: string in the following format:
        '			LANGUAGE=<XX> & vbCrLf
        '			SECTION=<TXT_Section> & vbCrLf
        '			PAGE=<TXT_Page> & vbCrLf
        '           PRODUCT=<TXT_Product> & vbCrLf
        '				where XX is a 2-character language code of the language in which
        '				the duplicate was found.
        'EXAMPLE:
        '		In Desktop designer window WDESKPRO Save button, call the following
        '		as part of the validation process:
        '       Dim s as string
        '       'Write the desktop name to s
        '       goTr.StrWrite(s, "US", TXT_Name.Text)
        '		IF goMeta.IsNameDuplicate("DSK_", gw.sDesktopID, s, , gop.GetProduct()) <> "" THEN
        '			'Duplicate found
        '		END

        Dim sProc As String = "clMetaData::IsNameDuplicate"
        Dim sPagePrefix As String = par_sPagePrefix
        Dim sPageID As String = par_sPageID
        Dim sOneLangToSearch As String = UCase(par_sOneLangToSearch)
        Dim sNameTrans As String = par_sNameTrans
        Dim sNameToSearch As String
        Dim iI As Integer = 1
        Dim sReturn As String = ""

        'Try
        Do

                'setup sql connection
                '*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
                Dim oConnection As SqlClient.SqlConnection = goData.GetConnection
                Dim oCommand As New SqlClient.SqlCommand
                Dim oReader As SqlClient.SqlDataReader

                sNameToSearch = goTR.ExtractString(sNameTrans, iI, vbCrLf)
                If sNameToSearch = Chr(4) Then Exit Do
                sNameToSearch = goTR.ExtractString(sNameToSearch, 2, "=")

                'sql command
                oCommand.CommandText = "pMetaGetDuplNames"
                oCommand.CommandType = CommandType.StoredProcedure
                oCommand.Connection = oConnection

                'parameter 
                Dim sPagePrefixParam As New SqlClient.SqlParameter("@par_sPagePrefix", SqlDbType.VarChar)
                sPagePrefixParam.Value = sPagePrefix
                oCommand.Parameters.Add(sPagePrefixParam)

                'parameter 
                Dim sPageParam As New SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar)
                sPageParam.Value = sPageID
                oCommand.Parameters.Add(sPageParam)

                'parameter 
                Dim sNameParam As New SqlClient.SqlParameter("@par_sName", SqlDbType.NVarChar)
                sNameParam.Value = sNameToSearch
                oCommand.Parameters.Add(sNameParam)

                'parameter 
                Dim sLanguageParam As New SqlClient.SqlParameter("@par_sLanguage", SqlDbType.VarChar)
                sLanguageParam.Value = sOneLangToSearch
                oCommand.Parameters.Add(sLanguageParam)

                'parameter 
                Dim sProduct As New SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar)
                sProduct.Value = par_sProduct
                oCommand.Parameters.Add(sProduct)

                'execute sproc

                oReader = oCommand.ExecuteReader

                If sOneLangToSearch <> "" Then

                    'read returned value
                    If oReader.HasRows Then
                        Do While oReader.Read()
                            sReturn = sReturn & "LANGUAGE=" & oReader("TXT_Language").ToString & vbCrLf
                            sReturn = sReturn & "SECTION=" & oReader("GID_Section").ToString & vbCrLf
                            sReturn = sReturn & "PAGE=" & oReader("TXT_Page").ToString & vbCrLf
                            sReturn = sReturn & "PRODUCT=" & oReader("TXT_PRODUCT").ToString & vbCrLf
                            Return sReturn
                        Loop
                    Else
                        sReturn = ""
                    End If

                Else

                    Dim sLanguage As String
                    Dim sValue As String

                    'read returned value
                    If oReader.HasRows Then
                        Do While oReader.Read()
                            sLanguage = UCase(oReader("TXT_Language").ToString)
                            sValue = UCase(oReader("TXT_Value").ToString)
                            If UCase(goTR.StrRead(sNameTrans, sLanguage)) = sValue Then
                                sReturn = sReturn & "LANGUAGE=" & oReader("TXT_Language").ToString & vbCrLf
                                sReturn = sReturn & "SECTION=" & oReader("GID_Section").ToString & vbCrLf
                                sReturn = sReturn & "PAGE=" & oReader("TXT_Page").ToString & vbCrLf
                                sReturn = sReturn & "PRODUCT=" & oReader("TXT_PRODUCT").ToString & vbCrLf
                                Return sReturn
                            End If
                        Loop
                    Else
                        sReturn = ""
                    End If

                End If

                oReader.Close()
                oConnection.Close()

                iI = iI + 1

            Loop

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return sReturn

    End Function

    Public Function IsObjectShared(ByVal par_sPageID As String, Optional ByVal par_sProduct As String = "") As Boolean
        'MI 2/3/09 Added par_sProduct.
        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '       Determines whether a metadata page is shared ("GLOBAL" section)
        'PARAMETERS:
        '		par_sPageID: ID of the metadata page
        '       par_sProduct: Product code (SA, MB, XX for 'all' products). If omitted,
        '           the product will be read from the current session. If still NULL,
        '           it will be set to 'SA'.
        'RESULT:
        '		Boolean

        Dim sProc As String = "clMetaData::IsObjectShared"
        '*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
        Dim oConnection As SqlClient.SqlConnection = goData.GetConnection
        Dim oCommand As New SqlClient.SqlCommand
        Dim oReader As SqlClient.SqlDataReader
        Dim bReturn As Boolean = False
        Dim sPage As String = par_sPageID

        'Try

        'sql command
        oCommand.CommandText = "fnMetaIsPageShared"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter 
            Dim sPageParam As New SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar)
            sPageParam.Value = sPage
            oCommand.Parameters.Add(sPageParam)

            'parameter 
            Dim sProductParam As New SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar)
            sProductParam.Value = par_sProduct
            oCommand.Parameters.Add(sProductParam)

            'return parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Bit)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            'execute sproc

            oReader = oCommand.ExecuteReader

            'Now you can grab the output parameter's value...
            bReturn = Convert.ToBoolean(retValParam.Value)

            oReader.Close()
            oConnection.Close()

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return bReturn

    End Function

    Private Function IsSupportedLanguage(ByVal sLang As String) As Boolean

        'AUTHOR: 
        '       
        'PURPOSE:
        '		Converts a string value to a System.Guid type
        'PARAMETERS:
        '		par_sValue			= String to convert
        'RETURNS:
        '		System.Guid representation of string sent as parameter

        Dim sProc As String = "clMetaData::IsSupportedLanguage"
        Dim sSuppLangs As String = goP.GetSupportedLangs()
        Dim sSuppLang As String = ""
        Dim iI As Integer = 1
        Dim bResult As Boolean = False

        'Try

        Do
                sSuppLang = goTR.ExtractString(sSuppLangs, iI)
                If sSuppLang = Chr(4) Then Exit Do
                If sLang = sSuppLang Then
                    bResult = True
                    Exit Do
                End If
                iI = iI + 1
            Loop

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return bResult

    End Function

    Public Function LineDelete(ByVal par_sSection As String, _
                        ByVal par_sPage As String, _
                        ByVal par_sLine As String, _
                        Optional ByVal par_sProduct As String = "", _
                        Optional ByVal par_bAllProducts As Boolean = False) As Boolean
        'MI 1/16/09 Added par_sProduct.
        'MI 8/17/06 Added treating blank par_sSection treated as global.
        '7/14/06 MI Returning False if sProc returns <> 0.

        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Delete a line in a metadata page for a particular product.
        'PARAMETERS:
        '		par_sSection	=	Section in which the line must be deleted
        '		par_sPage		=	Page in which the line must be deleted
        '		par_sLine		=	Line/property to delete
        '       par_sProduct    =   Product code like 'SA', 'MB', 'WP', 'XX'.
        '                           Nothing and "" are interpreted as 'SA'.
        '                           XX is a code for pages that apply to 'all products'.
        '       par_bAllProducts=   When checked (default is unchecked), the line is
        '                           deleted for all products.
        'RETURNS:
        '		True if found and Deleted, false if not found (also OK, just nothing to delete)
        'HOW IT WORKS:
        '		Calls sProc pMetaLineDelete and gets return value
        'EXAMPLE:
        '       'Delete the 'Name' line in a global Contact form for product 'Mobile'
        '		goMeta.LineDelete("GLOBAL", "FRM_CN", "NAME", "MB")

        '==> Add error checking in sproc

        Dim sProc As String = "clMetaData::LineDelete"
        Dim sSection As String = UCase(Trim(par_sSection))
        Dim sPage As String = UCase(Trim(par_sPage))
        Dim sLine As String = UCase(Trim(par_sLine))
        Dim sLang As String = ""
        Dim iResult As Integer

        Try

            'determine language settings, if the line provides language
            If Mid(sLine, 3, 1) = "_" Then
                If IsSupportedLanguage(Mid(sLine, 1, 2)) Then
                    sLang = Mid(sLine, 1, 2)
                    sLine = Mid(sLine, 4)
                End If
            End If

            '*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
            Dim oConnection As SqlClient.SqlConnection = goData.GetConnection
            Dim oCommand As New SqlClient.SqlCommand
            Dim oReader As SqlClient.SqlDataReader

            oCommand.CommandText = "pMetaLineDelete"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter
            Dim uSection As New SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier)
            If sSection = "GLOBAL" Or sSection = "" Then
                uSection.Value = System.DBNull.Value
            Else
                uSection.Value = StringToGuid(sSection)
            End If
            oCommand.Parameters.Add(uSection)

            'parameter
            Dim strPage As New SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar)
            strPage.Value = sPage
            oCommand.Parameters.Add(strPage)

            'parameter
            Dim strProperty As New SqlClient.SqlParameter("@par_sProperty", SqlDbType.VarChar)
            strProperty.Value = sLine
            oCommand.Parameters.Add(strProperty)

            'parameter
            Dim strLanguage As New SqlClient.SqlParameter("@par_sLang", SqlDbType.Char)
            strLanguage.Value = sLang
            oCommand.Parameters.Add(strLanguage)

            'parameter
            Dim strProduct As New SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar)
            strProduct.Value = par_sProduct
            oCommand.Parameters.Add(strProduct)

            'parameter
            Dim strAllProducts As New SqlClient.SqlParameter("@par_bAllProducts", SqlDbType.Bit)
            strAllProducts.Value = par_bAllProducts
            oCommand.Parameters.Add(strAllProducts)

            'return parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            'execute

            oReader = oCommand.ExecuteReader()

            'Now you can grab the output parameter's value...
            iResult = Convert.ToInt16(retValParam.Value)

            oReader.Close()
            oConnection.Close()

            If iResult = 0 Then
                Return True
            Else
                Return False 'but no error checking in sproc yet, so this should be never returned
            End If

        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return False
        End Try

    End Function

    Public Function LineRead(ByVal par_sSection As String, _
                    ByVal par_sPage As String, _
                    ByVal par_sLine As String, _
                    Optional ByVal par_sDefaultValue As String = "", _
                    Optional ByVal par_bUserOnly As Boolean = False, _
                    Optional ByVal par_sProduct As String = "") As String
        'MI 10/29/09 Removed redundant vars that were set to parameter values.
        'MI 2/3/09 Fixed product parameter not being passed correctly to the sproc.
        'MI 1/16/09 Added par_sProduct.
        'MI 3/22/07 Changed Replace to goTr.Replace because it was returning Nothing!
        'MI 1/19/07 Added replacing Chr(1) & Chr(1) with vbcrlf
        'MI 8/17/06 Added treating blank par_sSection treated as global.
        'MI 5/26/06 Modified comments.
        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Read a low level parameter LINE, giving back the adequate value: Default/global/or user 
        'PARAMETERS:
        '		par_sSection		=	Section of the metadata page from which to read a property's value.
        '                               Can be 'GLOBAL' or user's GID as string. If not 'GLOBAL' and 
        '                               par_bUserOnly is False, the GLOBAL value is returned if the property
        '                               (par_sLine) is not found.
        '		par_sPage			=	Page in which the reading must be done.
        '		par_sLine			=	TXT_Property for which to read TXT_Value.
        '		par_sDefaultValue	= 	Default value to be returned if neither Section or GLOBAL line was found.
        '		par_bUserOnly		=	if TRUE, does not read the global values. Default is False.
        '       par_sProduct        =   TXT_Product for which to read TXT_Value. If empty or "", the code in current
        '                               SQL session will be used. If the session is not initialized, 'SA' will be used.
        '                               If the line doesn't exist for the specified product and it exists for product
        '                               'XX' (which applies to all products), that line will be returned. If this is not
        '                               desirable, use PageRead with par_bThisProductOnly=True, then read the line
        '                               with goTr.StrRead().
        'RETURNS:
        '		The value of TXT_Value for the given section/page/line/product.
        'HOW IT WORKS:
        '		calls udf fnMetaLineRead with adequate parameters (which performs the merging) and returns the result.
        'EXAMPLE:
        '		goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "COMPANYNAME") wil return the name of 
        '		the Company from workgroup options for the current product (this page happens to be stored
        '       under product 'XX' only, so the product substitution will occur.

        Dim sProc As String = "clMetaData::LineRead"
        'Dim sSection As String = UCase(Trim(par_sSection))     'MI 10/29/09 Commented
        'Dim sPage As String = UCase(Trim(par_sPage))     'MI 10/29/09 Commented
        'Dim sLine As String = UCase(Trim(par_sLine))     'MI 10/29/09 Commented
        'Dim sDefaultValue As String = par_sDefaultValue     'MI 10/29/09 Commented
        'Dim bUserOnly As Boolean = par_bUserOnly     'MI 10/29/09 Commented
        Dim sLang As String = ""

        par_sSection = UCase(Trim(par_sSection))
        par_sPage = UCase(Trim(par_sPage))
        par_sLine = UCase(Trim(par_sLine))

        'determine language settings, if the line provides language
        If Mid(par_sLine, 3, 1) = "_" Then
            If IsSupportedLanguage(Mid(par_sLine, 1, 2)) Then
                sLang = Mid(par_sLine, 1, 2)
                par_sLine = Mid(par_sLine, 4)
            End If
        End If

        Try

            '*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection

            ' using local variable. deprecated oMetaConnection member variable - MN 1/22/2018
            Using oConnection As SqlClient.SqlConnection = goData.GetConnection
                Dim oCommand As New SqlClient.SqlCommand
                ' Dim oReader As SqlClient.SqlDataReader
                Dim sResult As String = ""

                oCommand.CommandText = "fnMetaLineRead"
                oCommand.CommandType = CommandType.StoredProcedure
                oCommand.Connection = oConnection

                'parameter
                Dim uSection As New SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier)
                If par_sSection = "GLOBAL" Or par_sSection = "" Then
                    uSection.Value = System.DBNull.Value
                Else
                    uSection.Value = StringToGuid(par_sSection)
                End If
                oCommand.Parameters.Add(uSection)

                'parameter
                Dim strPage As New SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar)
                strPage.Value = par_sPage
                oCommand.Parameters.Add(strPage)

                'parameter
                Dim strProperty As New SqlClient.SqlParameter("@par_sProperty", SqlDbType.VarChar)
                strProperty.Value = par_sLine
                oCommand.Parameters.Add(strProperty)

                'parameter
                Dim strDefaultValue As New SqlClient.SqlParameter("@par_sDefaultValue", SqlDbType.NVarChar)
                strDefaultValue.Value = par_sDefaultValue
                oCommand.Parameters.Add(strDefaultValue)

                'parameter
                Dim blnUserOnly As New SqlClient.SqlParameter("@par_bUserOnly", SqlDbType.Bit)
                blnUserOnly.Value = par_bUserOnly
                oCommand.Parameters.Add(blnUserOnly)

                'parameter
                Dim strLanguage As New SqlClient.SqlParameter("@par_sLang", SqlDbType.VarChar)
                strLanguage.Value = sLang
                oCommand.Parameters.Add(strLanguage)

                'parameter
                Dim strProduct As New SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar)
                strProduct.Value = par_sProduct
                oCommand.Parameters.Add(strProduct)

                'return parameter
                Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.NVarChar)
                retValParam.Direction = ParameterDirection.ReturnValue
                oCommand.Parameters.Add(retValParam)

                'execute

                Using oReader As SqlClient.SqlDataReader = oCommand.ExecuteReader()

                    'Now you can grab the output parameter's value...
                    sResult = Convert.ToString(retValParam.Value)
                    sResult = goTR.Replace(sResult, (Chr(1) & Chr(1)).ToString, vbCrLf.ToString)

                    'oReader.Close()

                End Using
                'oConnection.Close()

                Return sResult
            End Using

        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If

            Return ""

        End Try

    End Function

    Public Function LinesToTSQL(ByVal par_sSection As String, _
                                ByVal par_sPage As String, _
                                ByVal par_sMemo As String, _
                                Optional ByVal par_bIncludePageDeletion As Boolean = True, _
                                Optional ByVal par_sProduct As String = "") As String
        'MI 3/23/09 Changed product in login section from par_sProduct to 'SA'.
        'MI 1/16/09 Added par_sProduct.
        'MI 5/23/07 Added par_bIncludePageDeletion parameter, added creating a section that allows deleting the page
        'MI 5/14/07 Created.
        'PURPOSE:
        '       Generate TSQL code that creates or edits a partial page of metadata from
        '       Metadata Details dialog page or a programmatically concatenated metadata "page".
        '       This is called from PageToTSQL.
        '       This doesn't check the validity of the section, page, or metadata string (par_sMemo).
        '       The Section can be blank, 'GLOBAL' or a valid User ID. If you pass an invalid ID
        '       the TSQL statement will generate a SQL error.
        '       Use this as a "metadata transfer out" facility for updating other databases
        '       with new or modified metadata. 

        Dim sProc As String = "clMetaData::LinesToTSQL"
        Dim sPage As String
        Dim sTemp As String
        Dim sResult As String = ""
        Dim sProperty As String
        Dim sValue As String
        Dim i As Integer
        Dim iPos As Integer
        Dim sLang As String = ""
        Dim sProduct As String = par_sProduct

        If sProduct = "" Then sProduct = "SA"

        sPage = par_sMemo
        If Trim(sPage) = "" Then Return ""

        'Build the static portion of the script
        'We create a login session under product SA regardless of the product under which
        'the lines should be created because the product could be 'XX', which login
        'doesn support, and because the 'transfer in' should be done under SA.
        sResult = "-------------------------------------------------------------------------" & vbCrLf & _
            "-- TSQL script: create/modify metadata page '" & par_sPage & "' in section '" & par_sSection & "' for product '" & par_sProduct & "'." & vbCrLf & _
            vbCrLf & _
            "-- First initialize a user session in SS" & vbCrLf & _
            "DECLARE @uUserID uniqueidentifier" & vbCrLf & _
            "DECLARE @sUserCode varchar(4)" & vbCrLf & _
            "DECLARE @uSection uniqueidentifier" & vbCrLf & _
            "DECLARE @iResult int" & vbCrLf & _
            "-- Optional: to assign a specific creator to the metadata, modify the user ID and code below." & vbCrLf & _
            "-- This is mandatory only for shared pages so that a specific user with partial author permission" & vbCrLf & _
            "-- can edit the page." & vbCrLf & _
            "SELECT @uUserID = CAST('F0272467-3EC5-48F7-5553-987900B57A11' as uniqueidentifier)" & vbCrLf & _
            "SELECT @sUserCode = 'SYST'" & vbCrLf & _
            "EXEC pInitSession @par_uUserID = @uUserID, @par_sUserCode = @sUserCode, @par_sProduct = 'SA' " & vbCrLf & _
            vbCrLf

        If par_sSection = "GLOBAL" Or par_sSection = "" Then
            sResult &= "SET @uSection = NULL" & vbCrLf & vbCrLf
        Else
            sResult &= "SET @uSection = CAST('" & par_sSection & "' as uniqueidentifier)" & vbCrLf & vbCrLf
        End If

        'Build the page deletion portion
        If par_bIncludePageDeletion Then
            sResult &= "-- Delete the page" & vbCrLf & _
            "-- *** Comment out this section to update the page instead of replacing it. ***" & vbCrLf & _
            "PRINT 'Deleting page ''" & par_sPage & "'' for product ''" & par_sProduct & "'' in section ''' + CAST(@uSection as varchar(40)) + ''''" & vbCrLf & _
            "EXEC @iResult = pMetaPageDelete @par_uSection = @uSection, @par_sPage = '" & par_sPage & "', @par_sProduct = '" & sProduct & "'" & vbCrLf & _
            "IF @iResult <> 0 PRINT '@iResult: ''' + CAST(@iResult AS varchar(20)) + ''''" & vbCrLf & vbCrLf
        Else
            sResult &= "-- Deleting the page is not an option because this script may be" & vbCrLf & _
            "-- creating only a partial page. To create a complete page, click Generate SQL Script in " & vbCrLf & _
            "-- Author>Browse Metadata menu." & vbCrLf & vbCrLf
        End If

        'Build the line-writing script
        sResult &= "-- Modify the page" & vbCrLf
        i = 1
        Do
            sTemp = goTR.ExtractString(sPage, i, vbCrLf)
            'No more lines, exit the loop
            If sTemp = clC.EOT Then Exit Do
            'Skip blank rows
            If sTemp = "" Then GoTo ProcessNext
            'Extract property
            iPos = InStr(sTemp, "=")
            If iPos < 1 Then GoTo ProcessNext 'Invalid line (no '='), skip it
            sProperty = goTR.FromTo(sTemp, 1, iPos - 1)
            'Extract language out of property if appropriate
            If goTR.FromTo(sProperty, 3, 3) = "_" Then
                If IsSupportedLanguage(goTR.FromTo(sProperty, 1, 2)) Then
                    sLang = goTR.FromTo(sProperty, 1, 2)
                    sProperty = goTR.FromTo(sProperty, 4, -1)
                Else
                    sLang = ""
                End If
            Else
                sLang = ""
            End If
            sValue = goTR.PrepareForSQL(goTR.FromTo(sTemp, iPos + 1, -1))
            par_sPage = goTR.PrepareForSQL(par_sPage)
            sProperty = goTR.PrepareForSQL(sProperty)
            sLang = goTR.PrepareForSQL(sLang)
            sResult &= "EXEC @iResult = pMetaLineWrite @par_uSection = @uSection, @par_sPage = '" & par_sPage & "', @par_sProperty = '" & sProperty & "', @par_sValue = '" & sValue & "', @par_sLanguage = '" & sLang & "', @par_sProduct = '" & sProduct & "'" & vbCrLf & _
            "IF @iResult <> 0 PRINT 'Error ' + CAST(@iResult AS varchar(100))" & vbCrLf
ProcessNext:
            i = i + 1
        Loop

        sResult &= "GO" & vbCrLf

        Return sResult
    End Function


    Public Function LineWrite(ByVal par_sSection As String, _
                ByVal par_sPage As String, _
                ByVal par_sLine As String, _
                ByVal par_vValue As Object, _
                Optional ByRef par_oConnection As SqlClient.SqlConnection = Nothing, _
                Optional ByVal par_sCreatorID As String = "", _
                Optional ByVal par_sProduct As String = "") As Boolean
        'MI 10/29/09 Removed redundant vars that were set to parameter values.
        'MI 1/30/09 added testing oConnection before closing it.
        'MI 1/16/09 Added par_sProduct.
        'MI 8/9/07 Added par_sCreatorID
        'MI 1/19/07 Disabled because it was causing errors: Added replacing CRs with Chr(1) & Chr(1)
        'MI 11/20/06 Removed trimming par_vValue
        'MI 8/17/06 Added treating blank section as GLOBAL.
        'MI 5/10/06 Trimming parameters
        'MI 5/5/06
        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Writes a line in a metadata page.
        '       ************* CAREFUL, READ WARNING BELOW! ************
        '       WARNING: Be careful which product you write metadata under. If you are using
        '       this method in the normal 'Sales' product context and you are writing a line that
        '       belongs to an application-defining page such as DSK, VIE, SND, etc, you needn't worry about the
        '       product parameter - all metadata is written under the SA product. However, if you are
        '       writing a line for a page that applies to all products (where TXT_Product in the MD
        '       table is 'XX' - for example OTH_LINKS, FLD_, CRL_, CRU, POP, WOP, and other metadata that
        '       is common to all products), you ***MUST*** specify 'XX' or you will create an 'SA' 
        '       product version of the page (with just the line you wrote). This will cause PageRead 
        '       to return just your new line under that product and in the case of OTH_LINKS, or FLD_ pages, 
        '       this may cause errors that can prevent the site from loading.
        '       ********************************************************
        'PARAMETERS:
        '		par_sSection	=	Section in which the line must be deleted
        '		par_sPage		=	Page in which the line must be deleted
        '		par_sLine		=	Line/property to delete
        '       par_sValue      =   Value to write for this line
        '       par_oConnection =   Optional SQL connection passed by PageWrite
        '       par_sCreatorID  =   GID_ID of the 'creator' User. This is needed for goMeta.PageWrite(),
        '                           which currently deletes the whole page od MD first, then
        '                           writes line by line. If this is a page edit, the creator must
        '                           remain the same. The creator is considered for limited author
        '                           permissions on GLOBAL metadata.
        '       par_sProduct    =   Product code under which to write the line. If Nothing or "",
        '                           the product in the current session will be used. If undefined
        '                           in the session, 'SA' (stored in the DB as NULL) will be used.
        'RETURNS:
        '		True if successful write, false if not.
        'HOW IT WORKS:
        '		Calls sProc pMetaLineWrite and gets return value.
        'EXAMPLE:
        '		goMeta.LineWrite("GLOBAL", "FRM_CN", "NAME", "Contact Form")

        Dim sProc As String = "clMetaData::LineWrite"
        'Dim sSection As String = Trim(UCase(par_sSection))      'MI 10/29/09 Commented
        'Dim sPage As String = Trim(par_sPage)      'MI 10/29/09 Commented
        'Dim sLine As String = Trim(par_sLine)      'MI 10/29/09 Commented
        Dim sValue As String = par_vValue.ToString  'Trim(par_vValue.ToString)
        Dim sLang As String = ""
        Dim iResult As Integer = 0

        par_sSection = Trim(UCase(par_sSection))
        par_sPage = Trim(par_sPage)
        par_sLine = Trim(par_sLine)

        'Replace CRs with two characters 1
        'MI 3/22/07 Replaced Replace(), which can return Nothing with goTr.Replace(), which returns ""
        'sValue = goTr.Replace(sValue, vbCrLf, Chr(1) & Chr(1))

        'determine language settings, if the line provides language
        If Mid(par_sLine, 3, 1) = "_" Then
            If IsSupportedLanguage(Mid(par_sLine, 1, 2)) Then
                sLang = Mid(par_sLine, 1, 2)
                par_sLine = Mid(par_sLine, 4)
            End If
        End If

        'Try

        Dim oConnection As SqlClient.SqlConnection
            If par_oConnection Is Nothing Then
                oConnection = goData.GetConnection
            Else
                oConnection = par_oConnection
            End If

            Dim oCommand As New SqlClient.SqlCommand
            Dim oReader As SqlClient.SqlDataReader

            oCommand.CommandText = "pMetaLineWrite"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter
            Dim uSection As New SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier)
            If par_sSection = "GLOBAL" Or par_sSection = "" Then
                uSection.Value = System.DBNull.Value
            Else
                uSection.Value = StringToGuid(par_sSection)
            End If
            oCommand.Parameters.Add(uSection)

            'parameter
            Dim strPage As New SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar)
            strPage.Value = par_sPage
            oCommand.Parameters.Add(strPage)

            'parameter
            'DEBUG
            'If par_sLine = Nothing Then Stop
            'If par_sLine = "" Then Stop
            'END DEBUG
            Dim strProperty As New SqlClient.SqlParameter("@par_sProperty", SqlDbType.VarChar)
            strProperty.Value = par_sLine
            oCommand.Parameters.Add(strProperty)

            'parameter
            Dim strValue As New SqlClient.SqlParameter("@par_sValue", SqlDbType.NVarChar)
            strValue.Value = sValue
            oCommand.Parameters.Add(strValue)

            'parameter
            Dim strLanguage As New SqlClient.SqlParameter("@par_sLanguage", SqlDbType.Char)
            If sLang <> "" Then
                strLanguage.Value = sLang
            Else
                strLanguage.Value = System.DBNull.Value
            End If
            oCommand.Parameters.Add(strLanguage)

            'parameter
            Dim uCreatorID As New SqlClient.SqlParameter("@par_uCreatorID", SqlDbType.UniqueIdentifier)
            If par_sCreatorID Is Nothing Or par_sCreatorID = "" Then
                uCreatorID.Value = System.DBNull.Value
            Else
                uCreatorID.Value = StringToGuid(par_sCreatorID)
            End If
            oCommand.Parameters.Add(uCreatorID)

            'parameter
            Dim strProduct As New SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar)
            strProduct.Value = par_sProduct
            oCommand.Parameters.Add(strProduct)

            'return parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            'execute
            oReader = oCommand.ExecuteReader()

            'Now you can grab the output parameter's value...
            iResult = Convert.ToInt16(retValParam.Value)

            oReader.Close()
            If par_oConnection Is Nothing Then
                If Not oConnection Is Nothing Then  'MI 1/30/09 added testing oConnection
                    oConnection.Close()
                End If
            End If

            If iResult = 0 Then
                Return True
            Else
                Select Case iResult
                    Case -1 'when fnGetMe returns '' User ID
                        goErr.SetWarning(35503, sProc)
                        'MessTranslate 35503: Metadata line couldn't be written because fnGetMe returned an invalid user ID.
                    Case -2 'when @par_sUserCode is ''
                        goErr.SetWarning(35504, sProc)
                        'MessTranslate 35504: Metadata line couldn't be written because fnGetMe returned an invalid user code.
                    Case -3 'when @par_sUserCode is ''
                        goErr.SetWarning(35000, sProc, "Error writing metadata line: product '" & par_sProduct.ToString & "' is invalid or, if the product is blank, the product written in the SQL session via pInitSession is invalid.")
                        'MessTranslate
                End Select
                Return False
            End If

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

    End Function

    Public Function PageDelete(ByVal par_sSection As String, _
                        ByVal par_sPageID As String, _
                        Optional ByVal par_sProduct As String = "") As Boolean
        'MI 1/16/09 Added par_sProduct.
        'MI 8/17/06 Added treating blank par_sSection as global.
        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Delete the metadata record (page). If multiple pages exist with the same
        '		section and page ID, this method deletes all such pages.
        'PARAMETERS:
        '		par_sSection:   "GLOBAL" or User's TID
        '		par_sPageID:    page ID of the page, typically in the <PREFIX>_<TID> format,
        '						for example: DSK_xxxxxxxx (desktop) or SCR_xxxxxxx (script)
        '       par_sProduct:   Product code like 'SA', 'MB', 'WP' for which the page will
        '                       be deleted. If Nothing or "", product will be taken from
        '                       the current session. If still undefined, 'SA' will be assumed.
        '                       'XX' is a code that stands for 'all products'.
        'RETURNS:
        '		*** Always true. *** In NGP it was True/False:
        '			False: if the page couldn't be found or deletion failed.
        '			True: page was found and the deletion succeeded.
        'EXAMPLE:
        '		sSection = goP.GetUserTID()
        '		sPageID = "SCR_xxxxxxxxxxxxxxxxxxxxxx"
        '       'Pop a message box and code the following in BTN_MsgBox1_Click:
        '		goMeta.PageDelete(sSection, sPageID, sProduct)

        Dim sProc As String = "clMetaData::PageDelete"
        Dim sSection As String = Trim(UCase(par_sSection))
        Dim sPage As String = Trim(UCase(par_sPageID))
        Dim iResult As Integer = 0

        Try

            '*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
            Dim oConnection As SqlClient.SqlConnection = goData.GetConnection
            Dim oCommand As New SqlClient.SqlCommand
            Dim oReader As SqlClient.SqlDataReader

            oCommand.CommandText = "pMetaPageDelete "
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter
            Dim uSection As New SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier)
            If sSection = "GLOBAL" Or sSection = "" Then
                uSection.Value = System.DBNull.Value
            Else
                uSection.Value = StringToGuid(sSection)
            End If
            oCommand.Parameters.Add(uSection)

            'parameter
            Dim strPage As New SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar)
            strPage.Value = sPage
            oCommand.Parameters.Add(strPage)

            'parameter
            Dim strProduct As New SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar)
            strProduct.Value = par_sProduct
            oCommand.Parameters.Add(strProduct)

            'return parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            'execute

            oReader = oCommand.ExecuteReader()

            'Now you can grab the output parameter's value...
            iResult = Convert.ToInt16(retValParam.Value)

            oReader.Close()
            oConnection.Close()

            If iResult = 0 Then
                Return True
            Else
                Return False 'no error checking in sproc yet
            End If

        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return False
        End Try

    End Function

    Public Function PageRead(ByVal par_sSection As String, _
                ByVal par_sPage As String, _
                Optional ByVal par_sDefaultPage As String = "", _
                Optional ByVal par_bUserOnly As Boolean = False, _
                Optional ByVal par_sProduct As String = "", _
                Optional ByVal par_bThisProductOnly As Boolean = False) As String
        'MI 10/29/09 Commented Dim sDefaultPage As String = par_sDefaultPage.
        'MI 2/6/09 Removed Trim() from oReader("TXT_Value").ToString - this was corrupting
        '           values that start with spaces. For example, a NAME '   aaaa' was returned
        '           as 'aaaa', causing the desktop to be duplicated or transferred out
        '           with a different name.
        'MI 1/15/09 Added par_sProduct.
        'MI 12/4/08 Improved comments.
        'MI 3/27/07 Added default Page evaluation
        'MI 5/5/06  Added allowing a blank par_sSection (treated as GLOBAL)

        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Read a PAGE of metadata, merging automatically DEFAULT, GLOBAL and USER information (if asked for).
        'PARAMETERS:
        '		par_sSection	= 	Section value (MetaData is organized in Sections, Pages and Lines).
        '           Supported values are user GID_IDs and "GLOBAL". If section is "", it is treated as "GLOBAL".
        '		par_sPage		= 	Page name (generally composed by a prefix (DSK_) and a SUID.
        '           Some page names for which there is no possibility of duplicates generated on multiple
        '           replicated servers are manually assigned, for ex OTH_LINKS, FLD_AC or FRM_CN.
        '           These pages are typically system-defining and are not user-editable.
        '           For most user-created pages, generate an ID by using a support prefix (see clDefaults.
        '           GetMetaTypeFromPrefix) plus "_" plus a valid SUID generated with goData.GenSUID("XX").
        '		par_sDefaultPage=	Default page. This whole page will be returned if nothing is found
        '           in the page we are seeking. If some lines are found, their values will override values
        '           in the default page.
        '           Remember that if par_bUserOnly is False (default), and par_sSection is not GLOBAL,
        '           the user page will be merged with the GLOBAL page first. In case of duplicate values,
        '           the lines of the user page will win. The engine will then perform the default page
        '           merging. To make sure that user/GLOBAL merging doesn't occur, set par_bUserOnly to True.
        '		par_bUserOnly	=	If TRUE, read only the page for par_sSection, and do not merge it
        '           with the GLOBAL page. If par_sDefaultPage is not blank, the merging with the default page
        '           will still occur.
        '       par_sProduct: Product code like 'SA', 'MB', 'WP'. XX is a code for 'all products' pages.
        '           If Nothing or "", product will be taken from the session. If still undefined, 'SA'
        '           will be used. 
        '       par_bThisProductOnly: When set to 1 (0 is default), merging with XX product page is suppressed.
        'RETURNS:
        '		A string containing the PAGE of metadata in a quasi-ini format (sections are not supported).
        'HOW IT WORKS:
        '		Reads in user and global section and merge the information together, then merge with the default.
        '		If you don't want to merge with Global, set par_bUserOnly to TRUE
        '		if you don't want to merge with default, set par_sDefaultPage to ""
        'EXAMPLE:
        '		goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS") 'returns workgroup options for the product
        '                                                           'of the current logon session.

        '==> Support par_sProduct!

        Dim sProc As String = "clMetaData:PageRead"
        Dim sSection As String = UCase(Trim(par_sSection))   'Transform the possibly numerical value into a string
        Dim sPage As String = UCase(Trim(par_sPage))       'Transform the possibly numerical value into a string
        Dim lRec As Long = 0
        'MI 10/29/09 Commenting because doesn't appear to be needed
        'Dim sDefaultPage As String = par_sDefaultPage
        Dim bUserOnly As Boolean = par_bUserOnly

        'A blank section is 'global'
        If sSection = "" Then
            sSection = "GLOBAL"
        End If

        'Blank page - return empty string
        If sPage = "" Then Return ""

        'V_T 4/8/2015 
        ' **** Caching *****      

        Dim iscachable As Boolean = clCache.IsCachable(sSection, sPage)
        If iscachable Then
            Dim cachedData As String
            cachedData = clCache.GetItemFromCache(clCache.MakeCacheKey(sSection, sPage))
            If Not cachedData Is Nothing Then
                Return cachedData
            End If
        End If

        ' **** Caching *****

        'setup sql connection
        ' using local variable. deprecated oMetaConnection member variable - MN 1/22/2018
        Using oConnection As SqlClient.SqlConnection = goData.GetConnection
            Dim oCommand As New SqlClient.SqlCommand
            'Dim oReader As SqlClient.SqlDataReader
            Dim sReturn As String = ""

            Try

                oCommand.CommandText = "pMetaPageRead"
                oCommand.CommandType = CommandType.StoredProcedure
                oCommand.Connection = oConnection

                'parameter 
                Dim uSection As New SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier)
                If sSection = "GLOBAL" Then
                    uSection.Value = System.DBNull.Value
                Else
                    uSection.Value = StringToGuid(sSection)
                End If
                oCommand.Parameters.Add(uSection)

                'parameter 
                Dim strPage As New SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar)
                strPage.Value = sPage
                oCommand.Parameters.Add(strPage)

                'parameter 
                Dim iUserOnly As New SqlClient.SqlParameter("@par_bUserOnly", SqlDbType.Bit)
                iUserOnly.Value = Convert.ToInt16(bUserOnly)
                oCommand.Parameters.Add(iUserOnly)

                'parameter 
                Dim strProduct As New SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar)
                strProduct.Value = par_sProduct
                oCommand.Parameters.Add(strProduct)

                'parameter 
                Dim strThisProductOnly As New SqlClient.SqlParameter("@par_bThisProductOnly", SqlDbType.Bit)
                strThisProductOnly.Value = Convert.ToInt16(par_bThisProductOnly)
                oCommand.Parameters.Add(strThisProductOnly)

                'execute sproc
                Using oReader As SqlClient.SqlDataReader = oCommand.ExecuteReader()


                    'read returned value
                    If oReader.HasRows Then
                        Do While oReader.Read()
                            Dim sKey As String = Trim(oReader("TXT_Property").ToString)     '0
                            Dim sLang As String = Trim(oReader("TXT_Language").ToString)    '2
                            'Dim sProd As String = Trim(oReader("TXT_Product").ToString)     '1
                            Dim sVal As String = oReader("TXT_Value").ToString              '3  'MI 2/6/09 Removed Trim(), was corrupting values that start with space
                            If sLang <> "" Then sKey = sLang & "_" & sKey
                            sReturn = sReturn & sKey & "=" & sVal & vbCrLf
                        Loop
                    Else
                        sReturn = ""
                    End If
                    sReturn = goTR.MergeIniStrings(sReturn, par_sDefaultPage)

                    'V_T 4/8/2015
                    If iscachable Then
                        clCache.AddtoCache(clCache.MakeCacheKey(sSection, sPage), sReturn)
                    End If


                End Using
                'oConnection.Close()

            Catch ex As Exception

                'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
                '    goErr.SetError(ex, 45105, sProc)
                'End If

                sReturn = ""

            End Try

            Return sReturn
        End Using

    End Function

    Public Function PageToTSQL(ByVal par_sSection As String, ByVal par_sPage As String, Optional ByVal par_sProduct As String = "") As String
        'MI 1/15/09 Added par_sProduct.
        'MI 5/14/07 modified to call LinesToTSQL.
        'MI 5/1/07 Created.
        'PURPOSE:
        '       Generate TSQL code that creates or edits a page of metadata.
        '       Use this as a "metadata transfer out" facility for updating other databases
        '       with new or modified metadata. 

        Dim sProc As String = "clMetaData::PageToTSQL"
        Dim sPage As String

        sPage = goMeta.PageRead(par_sSection, par_sPage, "", True, par_sProduct)
        If Trim(sPage) = "" Then Return ""

        Return Me.LinesToTSQL(par_sSection, par_sPage, sPage, , par_sProduct)

    End Function
    Public Function PageWrite(ByVal par_sSection As String, _
                ByVal par_sPage As String, _
                ByVal par_sVal As String, _
                Optional ByVal par_sFriendlyName As String = "", _
                Optional ByVal par_sCreatorID As String = "", _
                Optional ByVal par_sProduct As String = "") As Boolean
        'MI 4/20/12 changed "NAME" to "US_NAME" and conditionalized writing friendly name so it's not written twice.
        'MI 10/29/09 Removed redundant vars that are set to parameter values.
        'MI 1/19/09 Added par_sProduct.
        'MI 11/16/07 Removed par_sTextValue parameter. Watch it!
        'MI 8/9/07 Added sending creator ID to LineWrite
        'MI 1/10/07 Reversed change below.
        'MI 1/10/07 Changed par_sFriendlyName to be written as US_NAME, not NAME.
        'MI 8/17/06 Added treating blank par_sSection treated as global.
        'MI 7/17/06 Fixing bug.
        '7/14/06 MI Added raising errors
        'MI 5/10/06 Re-wrote the loop to accommodate blank lines. Added checking for invalid lines (no '=').
        'MI 5/5/06 Added PageDelete, debugging

        '==> par_bLocal is currently ignored until (if) replication becomes supported.

        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Write a metadata Page identified by a section and Page
        'PARAMETERS:
        '		par_sSection		= Metadata can be global, affecting all users, or local, affecting a single user.
        '                           Supported values: "GLOBAL" for global metadata or user's GID_ID for local metadata.
        '                           In PageRead, if a property is not found in a local page, by default it is sought
        '                           in the GLOBAL page.
        '		par_sPage			= Unique identifier of a 'page' of metadata properties. Ex:
        '                               DSK_1988EE04-2DCA-4E0F-5858-98C000DEEF31
        '                               POP_PERSONAL_OPTIONS
        '                               OTH_DATETIMEADJUSTMENT
        '                               OTH_FILES
        '		par_sVal			= Ini string consisting of property=value pairs. To be valid, a property must
        '                           be case-insensitive (will be returned by PageRead or LineRead as all-caps),
        '                           not include any spaces and not have an underscore in position 3 (unless 
        '                           prepended by a lanaguage code). To define a property for a particular language,
        '                           prepend it with a valid language code and underscore. Ex: US_PROPERTY= or 
        '                           FR_PROPERTY=. *** Language substitutions are not supported yet! ***
        '		par_sFriendlyName	= Friendly Name of the page that is stored in NAME= property.
        '       par_sCreatorID      = GID_ID of the 'creator' User. Use this parameter to give a limited author
        '                           permission to edit GLOBAL MD pages. The original creator, if also a limited author,
        '                           will not be able to edit the page anymore. Creator ID is not used for allowing
        '                           non-authors to see and edit non-GLOBAL pages - the section is.
        '		par_bLocal			= *** REMOVED ***  In NGP, if true (false was the default), the page was saved as local
        '                           (SI__ShareState=0) instead of Protected (SI__ShareState=1, which is how all
        '                           non-shared records get saved in Sales 5.0) when the section was the user ID
        '                           (i.e., not shared). It was still saved as SI__ShareState=2 (shared) when 
        '                           the section was GLOBAL.
        '		par_sTextValue		= *** REMOVED *** in NGP was the value to store in TXT_TextValue 
        '                           (used normally only from the wfMeta window). Optional parameter.
        '       par_sProduct        = Product code under which to write the page. If not defined, product will be
        '                           evaluated from the session. If not defined in the session, it will be set to 'SA'.
        'RETURNS:
        '		TRUE or FALSE depending of the result of the writing
        'HOW IT WORKS:
        '		First try to read the record identified by Section+Page, then make a hAdd or 
        '		hModify depending if it has been previously found or not
        'EXAMPLE:
        '		goMeta.PageWrite("GLOBAL","IMP_2002022711241928000MAI 00001XX",sTemplate,"Contacts from OLD Selltis")

        Dim sProc As String = "clMetaData::PageWrite"
        Dim bLocal As Boolean = False   'was = par_bLocal in NGP
        'Dim sSection As String = UCase(Trim(par_sSection))     'MI 10/29/09 commented
        'Dim sPage As String = UCase(Trim(par_sPage))     'MI 10/29/09 commented
        'Dim sTextValue As String = par_sTextValue
        'Dim sVal As String = par_sVal     'MI 10/29/09 commented
        Dim sFriendlyName As String = par_sFriendlyName
        Dim iCRPos As Integer = 0
        Dim sLine As String
        Dim sProp As String
        Dim sValue As String
        Dim iCount As Integer = 1
        Dim iEqualPos As Integer
        Dim bResult As Boolean = True
        Dim sCreatorID As String = ""
        Dim bUSNameWritten As Boolean = False

        par_sPage = UCase(Trim(par_sPage))           'MI 10/29/09 added
        par_sSection = UCase(Trim(par_sSection))             'MI 10/29/09 added

        If par_sPage = "" Then
            goErr.SetError(35506, sProc, , par_sSection, par_sPage, par_sFriendlyName, bLocal, par_sVal, "Value:      '" & par_sProduct & "'")
            '35506: PageWrite was called with a blank page parameter. Metadata page can't be written.
            ' 
            'Section:    '[1]'
            'Page:       '[2]'
            'Friendly(Name) '[3]'
            'Local:      '[4]'
            'Value:      '[5]'
            '[6]
            Return False
        End If

        'Add the final CR if missing
        If Right(par_sVal, 2) <> vbCrLf Then
            par_sVal &= vbCrLf
        End If

        'Get the creator ID
        sCreatorID = Me.GetCreatorID(par_sSection, par_sPage, par_sProduct)

        '---------- TEST WRITING TO SS ---------
        'Test whether we can write a test line without errors.
        'This is needed because the whole page will be deleted next, before lines are
        'written. If the writing of the lines fails, the page will be lost or incomplete.
        If Not LineWrite("GLOBAL", "OTH_TEST", "clMetaData.PageWrite", "Test", , sCreatorID, par_sProduct) Then
            goErr.SetError(35505, sProc, , par_sSection, par_sPage & " product: '" & par_sProduct & "'")
            '35505: Metadata page couldn't be written. Make sure that the server is running and that the ASP user has adequate permissions on it.
            ' 
            'Section:    '[1]'
            'Page:       '[2]'
            Return False
        End If

        '------------ DELETE PAGE -----------
        'Run pMetaPageDelete to start with a clean slate, otherwise we
        'have to go PageRead first and delete the properties one by one
        If Not PageDelete(par_sSection, par_sPage, par_sProduct) Then
            goErr.SetError(35500, sProc, , par_sSection, par_sPage, "Product: '" & par_sProduct & "'")
            '35500: Deleting a metadata page failed. Section: '[1]', page: '[2]'. [3]
            Return False
        End If

        '==> Replace this later with reading the whole page and comparing lines, then
        'updating the changed lines and deleting the deleted lines.
        'This will be faster and will preserve the ModTime of unchanged lines, which
        'are all being wiped out now. OR MAYBE THEY SHOULD ALL BE UPDATED?

        Try

            Dim oConnection As SqlClient.SqlConnection = goData.GetConnection
            iCount = 1
            iEqualPos = 0
            Do
                'Get the line
                sLine = goTR.ExtractString(par_sVal, iCount, vbCrLf)
                'Skip blank line
                If sLine = "" Then GoTo BottomOfLoop
                'clC.EOT indicates end of loop
                If sLine = clC.EOT Then Exit Do

                iEqualPos = InStr(sLine, "=")
                If iEqualPos < 1 Then
                    'Line doesn't have an =. It may be a comment?
                    goErr.SetWarning(35501, sProc, , sLine)
                    '35501: Invalid metadata line (no '='): '[1]'. [2]
                    GoTo BottomOfLoop
                End If

                sProp = goTR.FromTo(sLine, 1, iEqualPos - 1)
                sValue = goTR.FromTo(sLine, iEqualPos + 1, -1)

                'Skip blank property
                If Trim(sProp) = "" Then GoTo BottomOfLoop

                If Not LineWrite(par_sSection, par_sPage, sProp, sValue, oConnection, sCreatorID, par_sProduct) Then
                    bResult = False
                    'MI 4/20/12 Added, writing US_NAME failed, write it below if friendly name parameter <> ""
                    If sProp.ToUpper = "US_NAME" Then bUSNameWritten = False
                    goErr.SetWarning(35502, sProc, , par_sSection, par_sPage, sProp, sValue, "Product: '" & par_sProduct & "'")
                    '35502: Metadata line failed to be written.
                    'Section:            '[1]'
                    'Page:               '[2]'
                    'Property: '[3]'
                    'Value: '[4]'
                    '[5]
                Else
                    'MI 4/20/12 Added
                    If sProp.ToUpper = "US_NAME" Then bUSNameWritten = True
                End If

BottomOfLoop:
                iCount = iCount + 1
            Loop

            'Write the 'friendly' name if not already written
            If sFriendlyName <> "" Then
                'MI 4/20/12 Added test to avoid writing US_NAME twice
                If Not bUSNameWritten Then
                    'MI 4/20/12 changed "NAME" to "US_NAME" to 
                    If Not LineWrite(par_sSection, par_sPage, "US_NAME", sFriendlyName, oConnection, sCreatorID, par_sProduct) Then
                        bResult = False
                        goErr.SetWarning(35502, sProc, , par_sSection, par_sPage, "US_NAME", sFriendlyName, "Product: '" & par_sProduct & "'")
                        '35502: Metadata line failed to be written.
                        'Section:            '[1]'
                        'Page:               '[2]'
                        'Property: '[3]'
                        'Value: '[4]'
                        '[5]
                    End If
                End If
            End If

            oConnection.Close()

            Dim iscachable As Boolean = clCache.IsCachable(par_sSection, par_sPage)
            If iscachable Then
                clCache.ClearCache()
            End If

            If bResult Then
                Return True
            Else
                Return False
            End If

        Catch ex As Exception

            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If

            Return False

        End Try

    End Function

    Private Function StringToGuid(ByVal sValue As String) As System.Guid

        'AUTHOR: 
        '       
        'PURPOSE:
        '		Converts a string value to a System.Guid type
        'PARAMETERS:
        '		par_sValue			= String to convert
        'RETURNS:
        '		System.Guid representation of string sent as parameter

        Dim guidValue As System.Guid = CType(System.ComponentModel.TypeDescriptor.GetConverter(guidValue).ConvertFrom(sValue), System.Guid)
        Return guidValue

    End Function

    Public Function TransferIn(ByVal par_sMetadata As String, _
                                Optional ByVal par_sPageID As String = "", _
                                Optional ByVal par_bAsNew As Boolean = 1, _
                                Optional ByVal par_bShared As Boolean = False, _
                                Optional ByVal par_sName As String = "", _
                                Optional ByRef par_sMessage As String = "", _
                                Optional ByRef par_sCreatedOrEditedPageID As String = "", _
                                Optional ByVal par_sProduct As String = "") As Boolean
        'MI 1/25/10 Changing CHART view type to LIST for MB product.
        'MI 2/4/09 For DSK metadata transferred in to Mobile product, added goTR.MoveDesktopViewsToCol1(sPart(i)).
        'MI 1/27/09 WARNING: I commented MessageBox below Try/Catch section. This may cause
        '       a bug where there is no desktop definition to resurface. If the desktop
        '       is not defined, the user may think that the tfr in succeeded when nothing
        '       happened.
        'MI 1/26/09 Added par_sProduct.
        'MI 12/22/08 Fix: sOrigDesktopViewID wasn't tested if is nothing and was causing NullReferenceException error.
        'MI 8/18/08 Fix: index out of bounds error when the desktop definition is not properly delimited.
        'MI 11/13/07
        'PURPOSE:
        '       Transfer into metadata one or more pages from a string generated by
        '       Transfer Out.
        'CAUTION:
        '       Creating new pages should not be supported for bulk metadata
        '       transfers because each dependent page (such as VIE_) will be assigned
        '       a new page ID and the references to VIE_ IDs in a DSK_ page (VIEW3ID=,
        '       for example) will not be changed, corrupting the desktop.
        'PARAMETERS:
        '       par_sMetadata: Metadata string created with Transfer Out.
        '       par_sPageID: If only one page (and dependent pages) is to be
        '           transferred in, this is the page ID or the prefix of the page to transfer in.
        '           Example: 'DSK_' will cause the first DSK_ page definition found
        '           and its dependent VIE_ pages to be transferred in. 'SND_" will cause
        '           the first Send Template page to be transferred in. If there are additional
        '           pages, they will be ignored.
        '           If a complete page ID is passed, the first page with a matching 
        '           page ID will be transferred in.
        '           A valid prefix has 3 characters followed by "_". 
        '           The definition needn't be the first object in the transfer out 
        '           string, but all of its dependent objects must be found or False 
        '           will be returned.
        '           To transfer in multiple unrelated pages, leave this parameter blank.
        '       par_bAsNew: If True (default), the page(s) will be transferred in as
        '           new metadata objects, with new page IDs. Be careful when transferring
        '           in metadata pages that don't use a TID, for example 'POP_PERSONAL_OPTIONS'
        '           because they will be assigned a page ID like 
        '           'OTH_301c2e2f-6f4b-4a25-5858-99e50101a302'. If False, par_bShared is
        '           ignored.
        '       par_bShared: Ignored if par_bAsNew if False.
        '           If True (False is default), the pages will be created as GLOBAL
        '           if par_bAsNew is true or a GLOBAL existing page will be replaced if it
        '           already exists. If False, the page will be created as local or a local
        '           page will be replaced. If the user doesn't have adequate permission,
        '           False will be returned with par_sMessage. 
        '       par_sName: If par_sPageID is not empty and par_bAsNew is True, 
        '           you can provide a US language name of the main object to transfer in via
        '           this parameter. In the future this parameter may be used to provide
        '           the name in multiple languages in an ini format of:
        '               US=US Name
        '               FR=French Name
        '               GR=German Name
        '           If the name is provided in the US language only, the string must not
        '           have hard returns. If there are dependent objects, their names will
        '           remain the same. This is because dependent objects typically are not
        '           exposed on their own (for example views are always featured in 
        '           a desktop context).
        '       par_sMessage: Return parameter for validation messages.
        '       par_sCreatedOrEditedPageID: A new Page ID for a new metadata object
        '           or the actual page ID edited. par_sPageID may contain just "DSK_"
        '           because the full page ID of the desktop is not known when this method
        '           is called. This parameter returns the actual desktop page ID, whether
        '           generated here if par_bAsNew is true, or read from par_sMetadata.
        '       par_sProduct: Product code (SA, MB, WP, XX for 'all') under which to transfer
        '           in the page. If Nothing or "", the current session product will be used.
        '           Note that the ###PRODUCT line in transfer out metadata is ignored.
        'RETURNS:
        '       Boolean: True if successful, False if not. if a validation failed,
        '       a message about it is returned via par_sMessage ByRef parameter.
        '       If all validation passed, but the transfer in was not successful,
        '       a warning is raised. Test goErr.GetLastError() for warnings.

        Dim sProc As String = "clMetadata::TransferIn"
        'Dim bResult As Boolean = True      'MI 8/18/08 commented out
        Dim bResult As Boolean = False      'MI 8/18/08 added
        Dim sPart() As String
        Dim sPartSection() As String = Nothing
        Dim sPartPageID() As String = Nothing
        Dim sPartProduct() As String = Nothing
        Dim sMiniView() As String = Nothing
        Dim sMiniViewPageID() As String = Nothing
        Dim sTabView() As String = Nothing
        Dim sTabViewPageID() As String = Nothing
        Dim sTemp As String
        Dim sPage As String
        Dim iPos As Integer
        Dim sMDType As String
        Dim i As Integer
        Dim j As Integer
        Dim k As Integer
        Dim iLength As Integer
        Dim sNewDesktopID As String = ""
        Dim sOrigDesktopPage As String = ""
        Dim sOrigDesktopViewID() As String = Nothing
        Dim sProduct As String = par_sProduct
        Dim sViewName As String

        If sProduct = "" Then
            sProduct = goP.GetProduct()
        End If

        'Try
        '----------- This code generates transfer out metadata in clMetaData.TransferOutMarkup --------------
        'This is here for reference only - do not uncomment!
        'goTR.StrWrite(sPageInfo, "###TYPE", goDef.GetMetaTypeFromPrefix(par_sPageID))
        'sTemp = goTR.StrRead(par_sString, "US_NAME", , False)
        'If sTemp = "" Then sTemp = goTR.StrRead(par_sString, "NAME")
        'goTR.StrWrite(sPageInfo, "###NAME", sTemp)
        'goTR.StrWrite(sPageInfo, "###SECTION", par_sSectionID)
        'goTR.StrWrite(sPageInfo, "###PAGE", par_sPageID)
        'goTR.StrWrite(sPageInfo, "###PRODUCT", par_sProduct)
        'If Right(sPageInfo, 2) <> vbCrLf Then sPageInfo &= vbCrLf
        's = "---***^^^BEGIN PAGE^^^***---" & vbCrLf
        's &= sPageInfo
        's &= par_sString & vbCrLf
        's &= "---***^^^END PAGE^^^***---" & vbCrLf & vbCrLf

        '----------- Check user's permission ----------------
        If Not goP.GetMe("PERMTRANSFERIN") = "1" Then
                par_sMessage = "You don't have permission to transfer in application objects."
                Return False
            End If
            If par_bShared Then
                If goP.IsUserAuthor() < 1 Then
                    'Use is neither full nor partial author
                    par_sMessage = "You don't have permission to share the object(s) you are transferring in."
                    Return False
                End If
            End If

            'Get the name of the metadata object being transferred in
            If par_sPageID Is Nothing Then par_sPageID = ""
            par_sPageID = UCase(par_sPageID)
            If Len(par_sPageID) = 3 Then par_sPageID &= "_"
            If par_sPageID = "" Then
                sMDType = "object"
            Else
                sMDType = goDef.GetMetaTypeFromPrefix(par_sPageID)
            End If


            '---------------- VALIDATE --------------
            'Name
            If par_bAsNew Then
                If par_sName = "" Then
                    par_sMessage = goTR.MessComplete("Please enter a descriptive name for the [1] you are transferring in.", sMDType)
                    Return False
                End If
            End If

            'Page definition valid?
            sPart = Split(par_sMetadata, "---***^^^BEGIN PAGE^^^***---")
            If sPart.GetLength(0) < 2 Then
                'No valid object
                par_sMessage = goTR.MessComplete("Metadata does not contain a single valid [1] definition. Please paste valid transfer out metadata.", sMDType)
                Return False
            End If

            '-------------- EXTRACT PAGES --------------
            'Go through definitions in the metadata and extract individual pages
            iLength = sPart.Length() - 1
            For i = 1 To iLength
                'End Page delimiter in place?
                sTemp = sPart(i)      '0-based index: 0 element contains text before the first BEGIN PAGE delimiter line
                iPos = InStr(sTemp, "---***^^^END PAGE^^^***---")
                If iPos < 1 Then
                    par_sMessage = "The end of definition " & i.ToString & " is not properly delimited. Please use valid transfer out metadata."
                    Return False
                End If

                'Chop off the vbcrlf after BEGIN PAGE and the END PAGE delimiting strings
                sPage = goTR.FromTo(sTemp, 3, iPos - 1)
                ReDim Preserve sPartPageID(i)
                sPartPageID(i) = UCase(goTR.StrRead(sPage, "###PAGE", "", False))
                ReDim Preserve sPartSection(i)
                sPartSection(i) = UCase(goTR.StrRead(sPage, "###SECTION", "", False))
                ReDim Preserve sPartProduct(i)
                sPartProduct(i) = UCase(goTR.StrRead(sPage, "###PRODUCT", "", False))
                'Remove markup lines
                goTR.StrDelete(sPage, "###TYPE")
                goTR.StrDelete(sPage, "###NAME")
                goTR.StrDelete(sPage, "###SECTION")
                goTR.StrDelete(sPage, "###PAGE")
                goTR.StrDelete(sPage, "###PRODUCT")
                'Now we have clean MD pages in sPart(i)
                sPart(i) = sPage
                'Is the section valid?
                If sPartSection(i) <> "" And sPartSection(i) <> "GLOBAL" Then
                    If Not goTR.IsTID(sPartSection(i)) Then
                        par_sMessage = "Definition " & i.ToString & " doesn't have a valid section defined. Please use valid transfer out metadata."
                        Return False
                    End If
                End If
                'Does the page at least have a prefix like 'DSK_' or 'VIE_'?
                If goTR.FromTo(sPartPageID(i), 4, 4) <> "_" Then
                    par_sMessage = "Definition " & i.ToString & " doesn't have a valid page ID defined. Please use valid transfer out metadata."
                    Return False
                End If
            Next

            '-------------- PROCESS PAGE(S) -----------
            If par_sPageID <> "" Then
                'We are running in the 'single page' mode.
                'Find the first definition of the page of the given prefix
                'Transfer in only one page (and dependent pages)
                'For i = 1 To sPartPageID.Length()      'MI 8/18/08 commented
                For i = 0 To sPartPageID.Length() - 1      'MI 8/18/08 added
                    If Left(sPartPageID(i), Len(par_sPageID)) = par_sPageID Then
                        'Found the definition - it's in sPart(i)
                        Select Case Left(par_sPageID, 4)
                            Case "DSK_"
                                '-------- Desktop and referenced views ----------
                                'Desktop has dependent views so we process it differently than other types of pages
                                If par_bAsNew Then
                                    'Generate a new desktop ID
                                    sNewDesktopID = "DSK_" & goData.GenerateID("XX")
                                    par_sCreatedOrEditedPageID = sNewDesktopID
                                Else
                                    par_sPageID = sPartPageID(i)
                                    par_sCreatedOrEditedPageID = par_sPageID
                                    sOrigDesktopPage = goMeta.PageRead(sPartSection(i), sPartPageID(i), , True, sPartProduct(i), True)
                                    'Load IDs of the original desktop's views
                                    For j = 1 To goTR.StringToNum(goTR.StrRead(sOrigDesktopPage, "VIEWCOUNT", "0", False), "0")
                                        ReDim Preserve sOrigDesktopViewID(j)
                                        sOrigDesktopViewID(j) = UCase(goTR.StrRead(sOrigDesktopPage, "VIEW" & goTR.NumToString(j, "0") & "ID", , False))
                                    Next
                                    k = j - 1
                                    For j = 1 To goTR.StringToNum(goTR.StrRead(sOrigDesktopPage, "TABCOUNT", "0", False), "0")
                                        ReDim Preserve sOrigDesktopViewID(j + k)
                                        sOrigDesktopViewID(j + k) = UCase(goTR.StrRead(sOrigDesktopPage, "TAB" & goTR.NumToString(j, "0") & "VIEWID", , False))
                                    Next
                                End If
                                '------- Views --------
                                'Read all the desktop's views
                                ReDim Preserve sMiniViewPageID(0)
                                ReDim Preserve sMiniView(0)
                                ReDim Preserve sTabViewPageID(0)
                                ReDim Preserve sTabView(0)
                                For j = 1 To goTR.StringToNum(goTR.StrRead(sPart(i), "VIEWCOUNT", "0", False), "0")
                                    'View page ID
                                    ReDim Preserve sMiniViewPageID(j)
                                    ReDim Preserve sMiniView(j)
                                    sMiniViewPageID(j) = UCase(goTR.StrRead(sPart(i), "VIEW" & goTR.NumToString(j, "0") & "ID", , False))
                                    'Find this view's metadata in sPart array
                                    For k = 1 To sPartPageID.GetUpperBound(0)
                                        If sPartPageID(k) = sMiniViewPageID(j) Then
                                            sMiniView(j) = sPart(k)
                                            Exit For
                                        End If
                                    Next
                                    If sMiniView(j) = "" Then
                                        'View page not found
                                        par_sMessage = "There is no definition for mini view " & j.ToString & " in desktop '" & goTR.StrRead(sPart(i), "NAME") & "' (defined in slot " & i.ToString & "). Please use valid transfer out metadata."
                                        Return False
                                    End If
                                    If par_bAsNew Then
                                        'Modify some properties
                                        goTR.StrWrite(sMiniView(j), "DESKTOPID", sNewDesktopID)
                                        goTR.StrWrite(sMiniView(j), "CREATORID", goP.GetUserTID())
                                        'MI 1/25/10 Turn CHART views to LIST under MB product. This is the best compromise for now
                                        'that prevents errors when opening such views in MB. If charts get supported in MB, 
                                        'this should be removed.
                                        If sProduct <> "SA" Then
                                            If goTR.StrRead(sMiniView(j), "TYPE", "", False) = "CHART" Then
                                                goTR.StrWrite(sMiniView(j), "TYPE", "LIST")
                                                'Append '(chart not supported)' to the view name
                                                sViewName = goTR.StrRead(sMiniView(j), "US_NAME", "", False)
                                                If sViewName = "" Then sViewName = goTR.StrRead(sMiniView(j), "NAME", "", False)
                                                If sViewName = "" Then
                                                    goTR.StrWrite(sMiniView(j), "US_NAME", "(chart not supported)")
                                                Else
                                                    goTR.StrWrite(sMiniView(j), "US_NAME", sViewName & " (chart not supported)")
                                                End If
                                            End If
                                        End If
                                        'Generate a new view page ID
                                        sMiniViewPageID(j) = "VIE_" & goData.GenerateID("XX")
                                    Else
                                        'Remove the view from sOrigDesktopViewID, which we will use later to delete views
                                        'that are not in the transferred in metadata
                                        If Not sOrigDesktopViewID Is Nothing Then
                                            For k = 1 To sOrigDesktopViewID.GetUpperBound(0)
                                                If sOrigDesktopViewID(k) = sMiniViewPageID(j) Then
                                                    sOrigDesktopViewID(k) = ""
                                                End If
                                            Next
                                        End If
                                    End If
                                Next
                                For j = 1 To goTR.StringToNum(goTR.StrRead(sPart(i), "TABCOUNT", "0", False), "0")
                                    'Tab view page ID
                                    ReDim Preserve sTabViewPageID(j)
                                    ReDim Preserve sTabView(j)
                                    sTabViewPageID(j) = UCase(goTR.StrRead(sPart(i), "TAB" & goTR.NumToString(j, "0") & "VIEWID", , False))
                                    'Find this tab view's metadata in sPart array
                                    For k = 1 To sPartPageID.GetUpperBound(0)
                                        If sPartPageID(k) = sTabViewPageID(j) Then
                                            sTabView(j) = sPart(k)
                                            Exit For
                                        End If
                                    Next
                                    If sTabView(j) = "" Then
                                        'View page not found
                                        par_sMessage = "There is no definition for tab view " & j.ToString & " in desktop '" & goTR.StrRead(sPart(i), "NAME") & "' (defined in slot " & i.ToString & "). Please use valid transfer out metadata."
                                        Return False
                                    End If
                                    If par_bAsNew Then
                                        'Modify some properties
                                        goTR.StrWrite(sTabView(j), "DESKTOPID", sNewDesktopID)
                                        goTR.StrWrite(sTabView(j), "CREATORID", goP.GetUserTID())
                                        'MI 1/25/10 Turn CHART views to LIST under MB product. This is the best compromise for now
                                        'that prevents errors when opening such views in MB. If charts get supported in MB, 
                                        'this should be removed.
                                        If sProduct <> "SA" Then
                                            If goTR.StrRead(sTabView(j), "TYPE", "", False) = "CHART" Then
                                                goTR.StrWrite(sTabView(j), "TYPE", "LIST")
                                                'Append '(chart not supported)' to the view name
                                                sViewName = goTR.StrRead(sTabView(j), "US_NAME", "", False)
                                                If sViewName = "" Then sViewName = goTR.StrRead(sTabView(j), "NAME", "", False)
                                                If sViewName = "" Then
                                                    goTR.StrWrite(sTabView(j), "US_NAME", "(chart not supported)")
                                                Else
                                                    goTR.StrWrite(sTabView(j), "US_NAME", sViewName & " (chart not supported)")
                                                End If
                                            End If
                                        End If
                                        'Generate a new view page ID
                                        sTabViewPageID(j) = "VIE_" & goData.GenerateID("XX")
                                    Else
                                        'Remove the view from sOrigDesktopViewID, which we will use later to delete views
                                        'that are not in the transferred in metadata
                                        If Not sOrigDesktopViewID Is Nothing Then
                                            For k = 1 To sOrigDesktopViewID.GetUpperBound(0)
                                                If sOrigDesktopViewID(k) = sTabViewPageID(j) Then
                                                    sOrigDesktopViewID(k) = ""
                                                End If
                                            Next
                                        End If
                                    End If
                                Next
                                '--------- Modify view IDs in the desktop page ---------------
                                'Modify VIEWnID= and TABnVIEWID= properties in the DSK_ page 
                                'to reference new VIE_ page IDs.
                                If par_bAsNew Then
                                    For j = 1 To sMiniViewPageID.GetUpperBound(0)
                                        goTR.StrWrite(sPart(i), "VIEW" & j.ToString & "ID", sMiniViewPageID(j))
                                    Next
                                    For j = 1 To sTabViewPageID.GetUpperBound(0)
                                        goTR.StrWrite(sPart(i), "TAB" & j.ToString & "VIEWID", sTabViewPageID(j))
                                    Next
                                End If
                                '--------- Create/edit the desktop ----------
                                'Move all views to column 1 for Mobile
                                If sProduct = "MB" Then sPart(i) = goTR.MoveDesktopViewsToCol1(sPart(i)) 'MI 2/4/09 added
                                If par_bAsNew Then
                                    If Not TransferInOnePage(sPart(i), sNewDesktopID, par_bAsNew, par_bShared, par_sName, sPartSection(i), par_sMessage, sProduct) Then
                                        Return False
                                    End If
                                Else
                                    If Not TransferInOnePage(sPart(i), par_sPageID, par_bAsNew, par_bShared, par_sName, sPartSection(i), par_sMessage, sProduct) Then
                                        Return False
                                    End If
                                End If
                                '---------- Create/edit the views -----------
                                iLength = sMiniView.GetUpperBound(0)
                                For j = 1 To iLength
                                    If Not TransferInOnePage(sMiniView(j), sMiniViewPageID(j), par_bAsNew, par_bShared, goTR.StrRead(sMiniView(j), "NAME"), sPartSection(i), par_sMessage, sProduct) Then
                                        Return False
                                    End If
                                Next
                                iLength = sTabView.GetUpperBound(0)
                                For j = 1 To iLength
                                    If Not TransferInOnePage(sTabView(j), sTabViewPageID(j), par_bAsNew, par_bShared, goTR.StrRead(sTabView(j), "NAME"), sPartSection(i), par_sMessage, sProduct) Then
                                        Return False
                                    End If
                                Next
                                '----------- Delete the views not in the transfer in metadata ------------
                                If Not par_bAsNew Then
                                    If Not sOrigDesktopViewID Is Nothing Then
                                        For j = 1 To sOrigDesktopViewID.GetUpperBound(0)
                                            If sOrigDesktopViewID(j) <> "" Then
                                                goMeta.PageDelete(sPartSection(i), sOrigDesktopViewID(j), sPartProduct(i))
                                            End If
                                        Next
                                    End If
                                End If
                            Case Else
                                'Non-desktop page - process without worrying about dependent pages
                                'Create/edit the page
                                If par_bAsNew Then
                                    If Not TransferInOnePage(sPart(i), goTR.GetPrefix(par_sPageID), par_bAsNew, par_bShared, par_sName, sPartSection(i), par_sMessage, sProduct) Then
                                        Return False
                                    End If
                                Else
                                    If Not TransferInOnePage(sPart(i), par_sPageID, par_bAsNew, par_bShared, par_sName, sPartSection(i), par_sMessage, sProduct) Then
                                        Return False
                                    End If
                                End If
                        End Select
                        'We are done - we created/edited the page we were looking for
                        'MI 1/27/09 Commented out the following because it doesn't make any sense
                        ''If we didn't get here because we didn't find the main definition      'MI 8/18/08 added
                        ''or any definitions, bResult will be false.      'MI 8/18/08 added
                        'bResult = True      'MI 8/18/08 added 
                        Exit For
                    End If
                Next
            Else
                'Transfer in all pages - this is bulk transfer. See 'CAUTION' in comments above.
                For i = 1 To sPartPageID.GetUpperBound(0)
                    'Create/edit the page
                    If par_bAsNew Then
                        If Not TransferInOnePage(sPart(i), goTR.GetPrefix(sPartPageID(i)), par_bAsNew, par_bShared, par_sName, sPartSection(i), par_sMessage, sProduct) Then
                            Return False
                        End If
                    Else
                        If Not TransferInOnePage(sPart(i), sPartPageID(i), par_bAsNew, par_bShared, par_sName, sPartSection(i), par_sMessage, sProduct) Then
                            Return False
                        End If
                    End If
                Next
            End If
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        'MI 1/27/09 Commented out the following because it doesn't make any sense
        'We return False in case of any failed calls to TransferInOnePage. That means that if we get here
        'we have processed everything successfully. It may be that a case where there is no DSK definition
        'at all is not caught within the For loop earlier, but in that case tests have to be done there.
        ''bResult is False if no definitions got processed.      'MI 8/18/08 added
        'par_sMessage = "There is no valid desktop definition. Please use valid transfer out metadata."      'MI 8/18/08 added
        Return True
    End Function

    Private Function TransferInOnePage(ByVal par_sMetadata As String, _
                                Optional ByVal par_sPageID As String = "", _
                                Optional ByVal par_bAsNew As Boolean = 1, _
                                Optional ByVal par_bShared As Boolean = False, _
                                Optional ByVal par_sName As String = "", _
                                Optional ByVal par_sSection As String = "", _
                                Optional ByRef par_sMessage As String = "", _
                                Optional ByVal par_sProduct As String = "") As Boolean
        'MI 1/26/09 Added par_sProduct.
        'MI 10/9/08 Added allowing full authors to transfer in local pages regardless of who's the author
        'PURPOSE:
        '       Called from TransferIn to write a single metadata page.
        '       CAUTION: Creating new pages should not be supported for bulk metadata
        '       transfers because each dependent page (such as VIE_) will be assigned
        '       a new page ID and the references to VIE_ IDs in a DSK_ page (VIEW3ID=,
        '       for example) will not be changed, corrupting the desktop.
        'PARAMETERS:
        '       par_sMetadata: Page metadata.
        '       par_sPageID As String = Page ID or page prefix only (4 chars like 'DSK_'
        '           or 'SND_' if par_bAsNew is true. CAREFUL: If the prefix is <= 4 chars, the ID
        '           will be generated in this method, otherwise the ID that is passed via
        '           this parameter will be used for the _new_ page. 
        '       par_bAsNew As Boolean = Create a new page. If unchecked, replace existing page.
        '       par_bShared As Boolean = Create a shared page. Ignored if par_bAsNew is False.
        '       par_sName As String = Create new page with this US_Name value.
        '       par_sSection As String = Page Section. If blank, user's ID is used
        '           as the section.
        '       par_sMessage = return parameter containing a message in case of failure.
        '       par_sProduct: Product code (SA, MB, WP, XX for 'all'). If nothing or "",
        '           the current session product will be used.
        'RETURNS:
        '       True when successful, marioimotherwise False either with par_sMessage set or
        '       goTr.SetWarning() raised.

        Dim sProc As String = "clMetadata::TransferInOnePage"
        Dim sProduct As String = par_sProduct
        If sProduct Is Nothing Or sProduct = "" Then
            sProduct = goP.GetProduct()
        End If
        ' Try
        '------------- Permissions -------------------
        If goP.GetMe("PERMTRANSFERIN") <> "1" Then
                par_sMessage = "You don't have permission to transfer in application objects."
                Return False
            End If

            If par_sSection = "" Then
                par_sSection = goP.GetUserTID()
            End If
            par_sSection = UCase(par_sSection)

            '------------ Write the page --------------
            If par_bAsNew Then
                'Create a new page
                'Edit name, tab label and creator ID in the metadata
                If par_sName <> "" Then
                    goTR.StrWrite(par_sMetadata, "US_NAME", par_sName)
                    If InStr(par_sMetadata, "US_WCLABEL=") > 0 Then
                        'For Desktops, write the Name in web client label property, if in the metadata
                        goTR.StrWrite(par_sMetadata, "US_WCLABEL ", par_sName)
                    End If
                End If
                goTR.StrWrite(par_sMetadata, "CREATORID", goP.GetUserTID())
                '----- Generate Page ID if not passed to this method -----
                If Len(par_sPageID) <= 4 Then   '4 is the length of a valid page prefix (e.g. "VIE_" or "DSK_")
                    If Len(par_sPageID) < 4 Then
                        'Invalid prefix
                        par_sMessage = "Error: metadata page prefix is invalid or missing: '" & par_sPageID & "'. Writing page '" & par_sPageID & "' failed."
                        Return False
                    Else
                        'A prefix was passed, generate a new ID
                        par_sPageID = par_sPageID & goData.GenerateID("XX")
                    End If
                Else
                    'Since the page ID is > 4 characters, we can assume that a valid page ID was passed in par_sPageID
                End If
                If par_bShared Then
                    'Shared - only authors are allowed to create
                    If InStr(par_sMetadata, "SHARED=") > 0 Then
                        'Write Shared only if already in metadata
                        goTR.StrWrite(par_sMetadata, "SHARED", "1")
                    End If
                    'Is the user author?
                    If goP.IsUserAuthor() < 1 Then
                        par_sMessage = "You don't have permission to create new shared pages. Writing page '" & par_sPageID & "' failed."
                        Return False
                    Else
                        If Not goMeta.PageWrite("GLOBAL", par_sPageID, par_sMetadata, , , sProduct) Then
                            par_sMessage = "An error occurred writing global page '" & par_sPageID & "' for product '" & sProduct & "'. If the problem persists, please contact Selltis support."
                            Return False
                        End If
                    End If
                Else
                    'Unshared - all users are allowed to create
                    If InStr(par_sMetadata, "SHARED=") > 0 Then
                        goTR.StrWrite(par_sMetadata, "SHARED", "0")
                    End If
                    If Not goMeta.PageWrite(goP.GetUserTID(), par_sPageID, par_sMetadata, , , sProduct) Then
                        par_sMessage = "An error occurred writing page '" & par_sPageID & "' (section '" & par_sSection & "', product '" & sProduct & "'). If the problem persists, please contact Selltis support."
                        Return False
                    End If
                End If
            Else
                'Edit an existing page if found, or create a new one
                If par_sSection = "" Or par_sSection = "GLOBAL" Then
                    'GLOBAL page, only full authors and partial authors who are creators can edit the page
                    Select Case goP.IsUserAuthor()
                        Case 2
                            'Full author - always OK
                        Case 1
                            'Limited author
                            If goMeta.GetCreatorID(par_sSection, par_sPageID, sProduct) <> goP.GetUserTID() Then
                                par_sMessage = "You are not allowed to overwrite shared metadata that you didn't create because you only have a limited author permission. Section: '" & par_sSection & "' page: '" & par_sPageID & "' product: '" & sProduct & "'."
                                Return False
                            End If
                        Case Else
                            'Not an author
                            par_sMessage = "You are not allowed to overwrite shared metadata because you don't have an author permission. Section: '" & par_sSection & "' page: '" & par_sPageID & "' product: '" & sProduct & "'."
                            Return False
                    End Select
                Else
                    'Local page, only full author or the 'creator' user can edit it
                    'MI 10/9/08 Added allowing full authors to transfer in local pages regardless of who's the author
                    Select Case goP.IsUserAuthor()
                        Case 2
                            'Full author - always OK
                        Case Else       '1 limited author or 0 not an author
                            'Not an author
                            'In this case (unshared page), permission is based on the section, not creator
                            'The section is editable by authors whereas the creator isn't. The creator gets
                            'lost during transfer. The section typically is the ID of the creator, so this is
                            'a moot point, but important in this context.
                            If par_sSection <> goP.GetUserTID() Then
                                par_sMessage = "You are not allowed to overwrite non-shared metadata of other users. Section: '" & par_sSection & "' page: '" & par_sPageID & "' product: '" & sProduct & "'."
                                Return False
                            End If
                    End Select
                End If

                If Not goMeta.PageWrite(par_sSection, par_sPageID, par_sMetadata, , , sProduct) Then
                    par_sMessage = "An error occurred writing page '" & par_sPageID & "' (section '" & par_sSection & "', product '" & sProduct & "'). If the problem persists, please contact Selltis support."
                    Return False
                End If
            End If

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
        Return True
    End Function

    Public Function TransferOut(ByVal par_sSectionID As String, _
                        ByVal par_sPageID As String, _
                        Optional ByVal par_sProduct As String = "") As String
        'MI 1/26/09 added par_sProduct.
        'MI 11/9/07
        'PURPOSE:
        '       Create a string with the ini-format information about a
        '       metadata object and dependent objects, if any, ready for
        '       transferring to another database or for creating a new
        '       MD object out of it.
        'PARAMETERS:
        '       par_sSectionID: section ID for user's objects or 
        '          'GLOBAL' (or '') if the object is GLOBAL.
        '       par_sPageID: SUID of the MD page to transfer out.
        '       par_sProduct: Product code (SA, WP, MB, XX for 'all'). If Nothing or
        '           "", current session product will be used.
        'RETURNS:
        '       String with each page marked up for the transfer in
        '       facility to be able to parse each page.

        Dim sproc As String = "clMetadata::TransferOut"
        goLog.Log(sproc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)
        Dim sResult As String = ""
        Dim sTemp As String = ""
        Dim sTemp2 As String = ""
        Dim sTemp3 As String = ""
        Dim i As Integer
        Dim sProduct As String = par_sProduct
        If sProduct = "" Then
            sProduct = goP.GetProduct()
        End If
        ' Try
        'Check user's permission
        If Not goP.GetMe("PERMTRANSFEROUT") = "1" Then
                Return "You don't have permission to transfer out application objects."
            End If
            Select Case UCase(goTR.GetPrefix(par_sPageID))
                Case "DSK_"
                    'DSK and VIE objects
                    sTemp = goMeta.PageRead(par_sSectionID, par_sPageID, , True, sProduct, True)
                    'Put out the desktop
                    sResult = goMeta.TransferOutMarkup(par_sSectionID, par_sPageID, sTemp, sProduct)
                    'Views
                    For i = 1 To goTR.StringToNum(goTR.StrRead(sTemp, "VIEWCOUNT", "0", False), "0")
                        'View page ID
                        sTemp2 = goTR.StrRead(sTemp, "VIEW" & goTR.NumToString(i, "0") & "ID", , False)
                        'View metadata
                        sTemp3 = goMeta.PageRead(par_sSectionID, sTemp2, , True, sProduct, True)
                        If sTemp3 <> "" Then
                            sResult &= goMeta.TransferOutMarkup(par_sSectionID, sTemp2, sTemp3, sProduct)
                        End If
                    Next
                    For i = 1 To goTR.StringToNum(goTR.StrRead(sTemp, "TABCOUNT", "0", False), "0")
                        'View page ID
                        sTemp2 = goTR.StrRead(sTemp, "TAB" & goTR.NumToString(i, "0") & "VIEWID", , False)
                        'View metadata
                        sTemp3 = goMeta.PageRead(par_sSectionID, sTemp2, , True, sProduct, True)
                        If sTemp3 <> "" Then
                            sResult &= goMeta.TransferOutMarkup(par_sSectionID, sTemp2, sTemp3, sProduct)
                        End If
                    Next
                Case Else
                    'Single MD page
                    sResult = goMeta.TransferOutMarkup(par_sSectionID, par_sPageID, goMeta.PageRead(par_sSectionID, par_sPageID, , True, sProduct, True), sProduct)
            End Select
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sproc)
        '    End If
        'End Try
        Return sResult
    End Function


    Private Function TransferOutMarkup(ByVal par_sSectionID As String, _
                        ByVal par_sPageID As String, _
                        ByVal par_sString As String, _
                        Optional ByVal par_sProduct As String = "") As String
        'MI 1/26/09 added par_sProduct.
        'MI 11/9/07
        'PURPOSE:
        '       Wrap a single metadata page to be transferred out with
        '       markup that transfer in functionality will use to parse
        '       individual pages.
        'PARAMETERS:
        '       par_sSectionID
        '       par_sPageID
        '       par_sString: Page of metadata (ini-format string).
        '       par_sProduct: Product code (SA, MB, WP, XX for 'all'). If Nothing or "",
        '           the current session product will be used.
        'RETURNS:
        '       Marked-up string ready for inclusion in the transfer out
        '       string.

        Dim sProc As String = "clMetadata:TransferOutMarkup"
        Dim s As String = par_sString
        Dim sTemp As String
        Dim sPageInfo As String = ""
        Dim sProduct As String = par_sProduct
        If par_sSectionID Is Nothing Then par_sSectionID = "GLOBAL"
        If par_sSectionID = "" Then par_sSectionID = "GLOBAL"
        If sProduct = "" Then
            sProduct = goP.GetProduct()
        End If
        '  Try
        goTR.StrWrite(sPageInfo, "###TYPE", goDef.GetMetaTypeFromPrefix(par_sPageID))
            sTemp = goTR.StrRead(par_sString, "US_NAME", , False)
            If sTemp = "" Then sTemp = goTR.StrRead(par_sString, "NAME")
            goTR.StrWrite(sPageInfo, "###NAME", sTemp)
            goTR.StrWrite(sPageInfo, "###SECTION", par_sSectionID)
            goTR.StrWrite(sPageInfo, "###PAGE", par_sPageID)
            goTR.StrWrite(sPageInfo, "###PRODUCT", sProduct)
            If Right(sPageInfo, 2) <> vbCrLf Then sPageInfo &= vbCrLf
            s = "---***^^^BEGIN PAGE^^^***---" & vbCrLf
            s &= sPageInfo
            s &= par_sString & vbCrLf
            s &= "---***^^^END PAGE^^^***---" & vbCrLf & vbCrLf

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return s
    End Function

    Public Sub Initialize()
        goP = HttpContext.Current.Session("goP")
        goTR = HttpContext.Current.Session("goTr")
        goMeta = HttpContext.Current.Session("goMeta")
        goData = HttpContext.Current.Session("goData")
        goErr = HttpContext.Current.Session("goErr")
        goLog = HttpContext.Current.Session("goLog")
        goDef = HttpContext.Current.Session("goDef")
    End Sub

    Public Sub New()

    End Sub

End Class