﻿CREATE TABLE [dbo].[TD_RELATED_QO] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_TD_RELATED_QO_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [G<PERSON>_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_QO] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_RELATED_QO] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_QO_CONNECTED_TD] FOREIGN KEY ([G<PERSON>_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_RELATED_QO] FOREIGN KEY ([GID_QO]) REFERENCES [dbo].[QO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_RELATED_QO] NOCHECK CONSTRAINT [LNK_QO_CONNECTED_TD];


GO
ALTER TABLE [dbo].[TD_RELATED_QO] NOCHECK CONSTRAINT [LNK_TD_RELATED_QO];


GO
CREATE NONCLUSTERED INDEX [IX_TD_RELATED_QO]
    ON [dbo].[TD_RELATED_QO]([GID_TD] ASC, [GID_QO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QO_CONNECTED_TD]
    ON [dbo].[TD_RELATED_QO]([GID_QO] ASC, [GID_TD] ASC);

