﻿CREATE TABLE [dbo].[consolidated_QTT] (
    [GID_CREDITEDTO_US]     UNIQUEIDENTIFIER NULL,
    [US_GID_ID]             UNIQUEIDENTIFIER NULL,
    [BU_GID_ID]             UNIQUEIDENTIFIER NULL,
    [QT_GID_RELATED_BU]     UNIQUE<PERSON>ENTIFIER NULL,
    [GID_IN_QT]             UNIQUEIDENTIFIER NULL,
    [QT_GID_ID]             UNIQUEIDENTIFIER NOT NULL,
    [PF_GID_ID]             UNIQUEIDENTIFIER NULL,
    [QL_GID_RELATED_PF]     UNIQUEIDENTIFIER NULL,
    [PC_GID_ID]             UNIQUEIDENTIFIER NULL,
    [PF_GID_RELATED_PC]     UNIQUEIDENTIFIER NULL,
    [VE_GID_ID]             UNIQUEIDENTIFIER NULL,
    [QL_GID_FOR_VE]         UNIQUEIDENTIFIER NULL,
    [BC_GID_ID]             UNIQUEIDENTIFIER NULL,
    [QT_GID_RELATED_BC]     UNIQUEIDENTIFIER NULL,
    [QT_CUR_TOTALAMOUNT]    MONEY            NULL,
    [QT_SI__GROSSMARGIN]    TINYINT          NULL,
    [QT_DTT_CREATIONTIME]   DATETIME         NULL,
    [QT_CHK_BUDGET]         TINYINT          NULL,
    [QT_MLS_STATUS]         SMALLINT         NULL,
    [QT_CUR_LINETOTALOPEN]  DECIMAL (18)     NULL,
    [QT_MLS_TYPE]           SMALLINT         NULL,
    [QT_DTT_EXPCLOSEDATE]   DATETIME         NULL,
    [QT_DTT_NEXTACTIONDATE] DATETIME         NULL,
    [QT_DTT_DATECLOSED]     DATETIME         NULL
);


GO
CREATE NONCLUSTERED INDEX [Index4]
    ON [dbo].[consolidated_QTT]([QT_GID_RELATED_BU] ASC, [QT_DTT_CREATIONTIME] ASC, [QT_CHK_BUDGET] ASC, [QT_MLS_STATUS] ASC)
    INCLUDE([QT_GID_ID], [QL_GID_RELATED_PF], [PF_GID_RELATED_PC], [VE_GID_ID], [QT_CUR_TOTALAMOUNT], [QT_SI__GROSSMARGIN], [QT_CUR_LINETOTALOPEN], [QT_MLS_TYPE]);


GO
CREATE NONCLUSTERED INDEX [Index5]
    ON [dbo].[consolidated_QTT]([QT_GID_RELATED_BU] ASC, [QT_CHK_BUDGET] ASC, [QT_MLS_STATUS] ASC)
    INCLUDE([QT_GID_ID], [QL_GID_RELATED_PF], [PF_GID_RELATED_PC], [QL_GID_FOR_VE], [QT_CUR_TOTALAMOUNT], [QT_SI__GROSSMARGIN], [QT_CUR_LINETOTALOPEN], [QT_MLS_TYPE]);


GO
CREATE NONCLUSTERED INDEX [Index6]
    ON [dbo].[consolidated_QTT]([BU_GID_ID] ASC, [QT_DTT_CREATIONTIME] ASC, [QT_CHK_BUDGET] ASC, [QT_MLS_STATUS] ASC)
    INCLUDE([QT_GID_ID], [QL_GID_RELATED_PF], [PF_GID_RELATED_PC], [QL_GID_FOR_VE], [QT_CUR_TOTALAMOUNT], [QT_SI__GROSSMARGIN], [QT_CUR_LINETOTALOPEN], [QT_MLS_TYPE], [QT_DTT_EXPCLOSEDATE], [QT_DTT_NEXTACTIONDATE]);


GO
CREATE NONCLUSTERED INDEX [Index7]
    ON [dbo].[consolidated_QTT]([BU_GID_ID] ASC, [QT_MLS_STATUS] ASC, [QT_DTT_DATECLOSED] ASC)
    INCLUDE([QT_GID_ID], [QL_GID_RELATED_PF], [PF_GID_RELATED_PC], [QL_GID_FOR_VE], [QT_CUR_TOTALAMOUNT], [QT_CUR_LINETOTALOPEN], [QT_MLS_TYPE]);

