﻿CREATE TABLE [dbo].[MC_RELATED_CN] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_MC_RELATED_CN_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [<PERSON><PERSON>_<PERSON>] UNIQUEIDENTIFIER NOT NULL,
    [GID_CN] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MC_RELATED_CN] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CN_CONNECTED_MC] FOREIGN KEY ([G<PERSON>_<PERSON>]) REFERENCES [dbo].[MC] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_MC_RELATED_CN] FOREIGN KEY ([GID_CN]) REFERENCES [dbo].[CN] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MC_RELATED_CN] NOCHECK CONSTRAINT [LNK_CN_CONNECTED_MC];


GO
ALTER TABLE [dbo].[MC_RELATED_CN] NOCHECK CONSTRAINT [LNK_MC_RELATED_CN];


GO
CREATE NONCLUSTERED INDEX [IX_MC_RELATED_CN]
    ON [dbo].[MC_RELATED_CN]([GID_MC] ASC, [GID_CN] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_CN_CONNECTED_MC]
    ON [dbo].[MC_RELATED_CN]([GID_CN] ASC, [GID_MC] ASC);

