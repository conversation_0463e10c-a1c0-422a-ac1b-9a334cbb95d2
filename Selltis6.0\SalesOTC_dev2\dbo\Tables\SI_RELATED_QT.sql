﻿CREATE TABLE [dbo].[SI_RELATED_QT] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_SI_RELATED_QT_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_SI] UNIQUEIDENTIFIER NOT NULL,
    [GID_QT] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_SI_RELATED_QT] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_QT_LINKED_SI] FOREIGN KEY ([GID_SI]) REFERENCES [dbo].[SI] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_SI_RELATED_QT] FOREIGN KEY ([GID_QT]) REFERENCES [dbo].[QT] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[SI_RELATED_QT] NOCHECK CONSTRAINT [LNK_QT_LINKED_SI];


GO
ALTER TABLE [dbo].[SI_RELATED_QT] NOCHECK CONSTRAINT [LNK_SI_RELATED_QT];


GO
CREATE NONCLUSTERED INDEX [IX_SI_RELATED_QT]
    ON [dbo].[SI_RELATED_QT]([GID_SI] ASC, [GID_QT] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_LINKED_SI]
    ON [dbo].[SI_RELATED_QT]([GID_QT] ASC, [GID_SI] ASC);

