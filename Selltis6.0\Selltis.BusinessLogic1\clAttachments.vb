﻿'V_T 6/8/2015
'Manage all attachments functions
Imports Microsoft.VisualBasic
Imports System.Collections.Generic
Imports System.IO
Imports System.Web
Imports System.Text
Imports Microsoft.WindowsAzure.Storage.File

Public Class clAttachments

    Private goMeta As clMetaData
    Private goP As clProject
    Private goErr As clError

    Public Sub New()
        goMeta = HttpContext.Current.Session("goMeta")
        goP = HttpContext.Current.Session("goP")
        goErr = HttpContext.Current.Session("goErr")
    End Sub

    Public Function GetAttachmentsSource() As String
        Dim sAttachmentSource As String = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "ATTACHMENTS_SOURCE", "", True)
        Return sAttachmentSource
    End Function

    Public Function GetAllowedFileExtensions() As String()
        Dim sExtensions As String = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SELLTIS_SERVER_ALLOWED_FILE_EXTENSIONS", ".txt,.doc,.docx,.xls,.xlsx,.csv,.pdf,.jpg,.jpeg,.png,.bmp,.gif,.ppt,.pptx,.log,.html", True)
        Return sExtensions.Split(",")
    End Function

    Public Function GetRootPath() As String
        ' Return System.Configuration.ConfigurationManager.AppSettings("AttachmentsPath").ToString()
        Return DirectCast(HttpContext.Current.Session(clSettings.GetHostName() + "_SiteSettings"), DataTable).Rows(0)("AttachmentsPath")
    End Function

    Public Function GetRootTempPath() As String
        ' Return System.Configuration.ConfigurationManager.AppSettings("AttachmentsTempPath").ToString()
        Return DirectCast(HttpContext.Current.Session(clSettings.GetHostName() + "_SiteSettings"), DataTable).Rows(0)("AttachmentsTempPath")
    End Function

    Public Function GetCurrentAttachmentsFolderSize(ViewName As String) As Long
        Dim rs = New clRowSet("AD", clC.SELL_GROUPBY, "TXT_FILENAME='" & ViewName & "'", "TXT_FILENAME", "DR__ATTACHMENT|SUM", , , , , , , True)
        rs.ToTable()

        Dim dt As DataTable = rs.dtTransTable
        Dim CurrentFolderSize As Long = 0
        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            Long.TryParse(dt.Rows(0)(0).ToString(), CurrentFolderSize)
        End If
        Return CurrentFolderSize
    End Function

    Public Function ADR_to_Link(ByVal par_ADR_Value As String, ByVal par_File_GID_ID As String, ByVal par_sField_Name As String, ByVal par_sViewName As String) As String
        'V_T Created 5/6
        'Returns the friendly value of the ADR fields

        Dim sProc As String = "clAttachments::ADR_to_Link"
        Try
            If String.IsNullOrEmpty(par_File_GID_ID) Then
                Return String.Empty
            End If

            Dim ADRString As String = String.Empty

            Dim dt As DataTable = GetAttachments(par_sField_Name, par_File_GID_ID)

            If dt IsNot Nothing And dt.Rows.Count > 0 Then

                For index = 0 To dt.Rows.Count - 1
                    'TXT_Source,TXT_ObjectId

                    Dim sAttachmentSource As String = "Selltis"
                    Dim sGidId As String = String.Empty
                    Dim sAttachmentName As String = String.Empty

                    If Not IsDBNull(dt.Rows(index)("TXT_Source")) Then
                        sAttachmentSource = dt.Rows(index)("TXT_Source").ToString()
                    End If
                    If Not IsDBNull(dt.Rows(index)("TXT_AttachmentName")) Then
                        sAttachmentName = dt.Rows(index)("TXT_AttachmentName").ToString()
                    End If
                    If Not IsDBNull(dt.Rows(index)("GID_ID")) Then
                        sGidId = dt.Rows(index)("GID_ID").ToString()
                    End If

                    If sAttachmentSource = "EFileCabinet" Then
                        'need ObjectId to download the file from efile cabinet
                        If Not IsDBNull(dt.Rows(index)("TXT_ATTACHMENTDIRECTORYPATH")) Then
                            sGidId = dt.Rows(index)("TXT_ATTACHMENTDIRECTORYPATH").ToString()
                        End If
                    End If

                    Dim AttachmentsStorageType As String = System.Configuration.ConfigurationManager.AppSettings("AttachmentsStorageType").ToString()

                    If AttachmentsStorageType = "Server" Then
                        If String.IsNullOrEmpty(ADRString) Then
                            ADRString = GetFileDownloadURL("dType=1&sGidid=" & sGidId & "&sSource=" & sAttachmentSource & "&sFileName=" & sAttachmentName & "", sAttachmentName)
                        Else
                            ADRString = ADRString & "<BR>" & GetFileDownloadURL("dType=1&sGidid=" & sGidId & "&sSource=" & sAttachmentSource & "&sFileName=" & sAttachmentName & "", sAttachmentName)
                        End If
                    Else
                        If String.IsNullOrEmpty(ADRString) Then
                            ADRString = GetFileDownloadURLAzure("sFileName=" & sAttachmentName & "&sViewName=" & par_sViewName & "&sGidId=" & par_File_GID_ID & "&sFieldName=" & par_sField_Name & "  ", sAttachmentName)
                        Else
                            ADRString = ADRString & "<BR>" & GetFileDownloadURLAzure("sFileName=" & sAttachmentName & "&sViewName=" & par_sViewName & "&sGidId=" & par_File_GID_ID & "&sFieldName=" & par_sField_Name & "  ", sAttachmentName)
                        End If
                    End If

                    'If sAttachmentSource = "Selltis" Then
                    '    If String.IsNullOrEmpty(ADRString) Then
                    '        ADRString = GetFileDownloadURL("Gidid=" & dt.Rows(index)("GID_ID").ToString() & "&sSource=Selltis", dt.Rows(index)("TXT_AttachmentName").ToString())
                    '    Else
                    '        ADRString = ADRString & "<BR>" & GetFileDownloadURL("Gidid=" & dt.Rows(index)("GID_ID").ToString() & "&sSource=Selltis", dt.Rows(index)("TXT_AttachmentName").ToString())
                    '    End If
                    'ElseIf sAttachmentSource = "EFileCabinet" Then
                    '    If String.IsNullOrEmpty(ADRString) Then
                    '        ADRString = GetFileDownloadURL("sfilename=" & dt.Rows(index)("TXT_AttachmentName").ToString() & "&objectid=" & dt.Rows(index)("TXT_ObjectId").ToString() & "&sSource=EFileCabinet", dt.Rows(index)("TXT_AttachmentName").ToString())
                    '    Else
                    '        ADRString = ADRString & "<BR>" & GetFileDownloadURL("sfilename=" & dt.Rows(index)("TXT_AttachmentName").ToString() & "&objectid=" & dt.Rows(index)("TXT_ObjectId").ToString() & "&sSource=EFileCabinet", dt.Rows(index)("TXT_AttachmentName").ToString())
                    '    End If
                    'End If

                Next

            End If

            Return ADRString

        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, , sProc)
            'End If
            Return ""
        End Try

    End Function
    Public Function DownloadFileToStream(sViewName As String, sGidId As String, sFieldName As String, sTempId As String, isTempPath As Boolean, FileName As String) As System.IO.MemoryStream
        Dim FinalFolder = Nothing
        If isTempPath Then
            FinalFolder = clAzureFileStorage.GetFinalFolder("", "", "", sTempId, True)
        Else
            FinalFolder = clAzureFileStorage.GetFinalFolder(sViewName, sGidId, sFieldName, "", False)
        End If
        Dim sFile As CloudFile = FinalFolder.GetFileReference(FileName)
        Dim tStream As System.IO.MemoryStream = New System.IO.MemoryStream()
        sFile.DownloadToStream(tStream)
        Return tStream

    End Function
    Public Function GetFile(Gid_Id As String, sSource As String, Optional sFilePath As String = "") As Byte()

        Dim NewGuid As Guid

        If Not String.IsNullOrEmpty(Gid_Id) AndAlso Guid.TryParse(Gid_Id, NewGuid) Then


            'Dim dt As DataTable = GetAD_DataTable("GID_ID='" + Gid_Id + "' ", "TXT_ATTACHMENTDIRECTORYPATH,TXT_Source")
            Dim AttachmentsStorageType As String = System.Configuration.ConfigurationManager.AppSettings("AttachmentsStorageType").ToString()
            Dim dt As DataTable = Nothing

            If AttachmentsStorageType = "Server" Then
                dt = GetAD_DataTable("GID_ID='" + Gid_Id + "' ", "TXT_ATTACHMENTDIRECTORYPATH,TXT_Source")
            Else
                dt = GetAD_DataTable("GID_ID='" + Gid_Id + "' ", "TXT_AttachmentName,TXT_FILENAME,TXT_FILEGIDID,TXT_FieldName,TXT_ATTACHMENTDIRECTORYPATH,TXT_Source")
            End If


            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then

                'Dim sSource As String = "Selltis"
                If Not IsDBNull(dt.Rows(0)("TXT_Source")) Then
                    sSource = dt.Rows(0)("TXT_Source").ToString()
                End If

                If sSource = "Selltis" Then

                    If AttachmentsStorageType = "Server" Then
                        Dim filename As String = dt.Rows(0)("TXT_ATTACHMENTDIRECTORYPATH").ToString() 'GetFullFilePathByGID_ID(Gid_Id)
                        Dim rootpath As String = GetRootPath() 'System.Configuration.ConfigurationManager.AppSettings("AttachmentsPath").ToString()
                        filename = rootpath & filename
                        If File.Exists(filename) Then
                            Return GetServerFile(filename)
                        Else
                            Return Nothing
                        End If
                    Else
                        Dim filename As String = dt.Rows(0)("TXT_AttachmentName").ToString()
                        Dim viewname As String = dt.Rows(0)("TXT_FILENAME").ToString()
                        Dim gidid As String = dt.Rows(0)("TXT_FILEGIDID").ToString()
                        Dim fieldname As String = dt.Rows(0)("TXT_FieldName").ToString()

                        If filename <> Nothing And filename <> "" Then
                            Return clAzureFileStorage.GetFile(viewname, gidid, fieldname, filename)
                        Else
                            Return Nothing
                        End If
                    End If

                ElseIf sSource = "EFileCabinet" Then 'EFC

                    Dim iEfcObjectId As Integer
                    Integer.TryParse(dt.Rows(0)("TXT_ATTACHMENTDIRECTORYPATH").ToString(), iEfcObjectId)
                    If iEfcObjectId > 0 Then
                        Return GetEFCFile(iEfcObjectId)
                    End If

                End If

            End If

            dt = Nothing

        ElseIf sSource = "EFileCabinet" Then
            Dim iEfcObjectId As Integer
            If Not String.IsNullOrEmpty(Gid_Id) Then
                Integer.TryParse(Gid_Id, iEfcObjectId)
            Else
                Integer.TryParse(sFilePath, iEfcObjectId)
            End If

            If iEfcObjectId > 0 Then
                Return GetEFCFile(iEfcObjectId)
            End If
        ElseIf sSource = "Selltis" Then
            Dim AttachmentsStorageType As String = System.Configuration.ConfigurationManager.AppSettings("AttachmentsStorageType").ToString()
            If AttachmentsStorageType = "Server" Then
                Return GetServerFile(sFilePath)
            Else
                Return Nothing
            End If
        End If

        Return Nothing

    End Function

    Private Function GetEFCFile(iEfcObjectId As Integer) As Byte()
        Dim objclEFCAttachments As New clEFCAttachments()
        Dim storeStream = objclEFCAttachments.DownloadFile(iEfcObjectId)
        Dim bytes = storeStream.ToArray()
        Return bytes
    End Function

    Private Function GetServerFile(filename As String) As Byte()
        Dim extension As String = System.IO.Path.GetExtension(filename.ToString())
        Dim mimetype As String = GetMIMEType(extension)

        If Not String.IsNullOrEmpty(mimetype) Then
            Dim bytes = System.IO.File.ReadAllBytes(filename.ToString())
            Return bytes
        Else
            Return Nothing
        End If
    End Function

    Public Function GetFileDownloadURL(sQuerystring As String, sLinkName As String) As String

        Return "<a " & "href='" & "/CreateForm/Download?" & sQuerystring & "'>" + sLinkName + "</a>"
        '  Return "<a href='#' onclick=""window.open('../Pages/FileDownloader.aspx?" & sQuerystring & "', 'new', '', false);"">" & sLinkName & "</a>"
    End Function

    Public Function GetFileDownloadURLAzure(sQuerystring As String, sLinkName As String) As String

        Return "<a " & "href='" & "/CreateForm/DownloadAzure?" & sQuerystring & "'>" + sLinkName + "</a>"

        ''DownloadAzure(string sFileName, string sViewName, string sGidId, string sFieldName, string sTempid)

    End Function

    Public Function DeleteAttachment(Gid_Id As String) As String

        ' Try
        Dim strFilepath As String = GetFullFilePathByGID_ID(Gid_Id)
        Dim oRs = New clRowSet("AD", clC.SELL_EDIT, "GID_ID='" & Gid_Id & "'", , "**")

        Dim sFieldName As String
        Dim sFileName As String
        Dim sAttachmentName As String
        Dim sKey As String = clSettings.GetHostName() & "_"

        If oRs.GetFirst Then
            sFieldName = oRs.GetFieldVal("TXT_FIELDNAME")
            sFileName = oRs.GetFieldVal("TXT_FILENAME")
            sAttachmentName = oRs.GetFieldVal("SYS_NAME")
            Dim sAttachmentFileGIDID As String = oRs.GetFieldVal("TXT_FILEGIDID")

            oRs.DeleteRecord()
            oRs = Nothing

        'Delete Files from Table
        Dim rs = New clRowSet(sFileName, clC.SELL_EDIT, "GID_ID = '" & sAttachmentFileGIDID & "'", "", sFieldName, 1, "", "", "", "", "", True, True)
        Dim AllAttachemnts As String = rs.GetFieldVal(sFieldName)
        AllAttachemnts = AllAttachemnts.Replace(sAttachmentName, "")
        rs.SetFieldVal(sFieldName, AllAttachemnts)
        Dim result As Integer = rs.Commit()

            'Delete Files
            Dim AttachmentsStorageType As String = System.Configuration.ConfigurationManager.AppSettings("AttachmentsStorageType").ToString()
            If AttachmentsStorageType = "Server" Then
                If Not String.IsNullOrEmpty(strFilepath) Then
                    strFilepath = GetRootPath() & strFilepath
                    If File.Exists(strFilepath) Then
                        File.Delete(strFilepath)
                        Return "Success"
                    End If
                Else
                    Return "File not existed"
                End If
            Else
                'Delete file from cloud
                Dim finalDir = clAzureFileStorage.GetFinalFolder(sFileName, sAttachmentFileGIDID, sFieldName, "", False)
                Dim retval As Boolean = clAzureFileStorage.DeleteFile(finalDir, sAttachmentName)

                If retval Then
                    Return "Success"
                Else
                    Return "File not existed error in file storage"
                End If
            End If
        Else
            Return "File not existed"
        End If



        ''delete from folder if exists (for selltis file only)
        ''
        'Dim AttachmentsStorageType As String = System.Configuration.ConfigurationManager.AppSettings("AttachmentsStorageType").ToString()
        '    If AttachmentsStorageType = "Server" Then
        '        If Not String.IsNullOrEmpty(strFilepath) Then
        '            strFilepath = GetRootPath() & strFilepath
        '            If File.Exists(strFilepath) Then
        '                File.Delete(strFilepath)
        '                Return "Success"
        '            End If
        '        Else
        '            Return "File not existed"
        '        End If
        '    Else
        '        ''AC/63373361-6332-3339-4143-31312f332f32/ADR_Attachments/2.PNG

        '        Dim str() = strFilepath.Split("/")

        '        If str.Length = 4 Then
        '            Dim viewname As String = str.GetValue(0).ToString()
        '            Dim gidid As String = str.GetValue(1).ToString()
        '            Dim fieldname As String = str.GetValue(2).ToString()
        '            Dim filename As String = str.GetValue(3).ToString()

        '            Dim finalDir = clAzureFileStorage.GetFinalFolder(viewname, gidid, fieldname, "", False)
        '            Dim retval As Boolean = clAzureFileStorage.DeleteFile(finalDir, filename)

        '            If retval Then
        '                Return "Success"
        '            Else
        '                Return "File not existed"
        '            End If
        '        Else
        '            Return "File not existed"
        '        End If
        '    End If

        Return "File not existed"

        'Catch ex As Exception
        '    Return ex.ToString()
        'End Try

    End Function

    Public Function DeleteAttachment(ViewName As String, ViewGID As String, FieldName As String) As String

        ' Try
        Dim rs = New clRowSet(ViewName, clC.SELL_EDIT, "GID_ID = '" & ViewGID & "'", "", FieldName, 1, "", "", "", "", "", True, True)
            rs.SetFieldVal(FieldName, "")
            Dim retval As Integer = rs.Commit()

            Dim strFilepath As String = "" 'GetFullFilePathByGID_ID(Gid_Id)
            Dim oRs = New clRowSet("AD", clC.SELL_EDIT, "TXT_FILEGIDID='" & ViewGID & "'", , "**")
            If oRs.GetFirst Then
                Do
                    strFilepath = oRs.GetFieldVal("TXT_ATTACHMENTDIRECTORYPATH")
                    If Not String.IsNullOrEmpty(strFilepath) Then
                        strFilepath = GetRootPath() & strFilepath
                        If File.Exists(strFilepath) Then
                            File.Delete(strFilepath)
                        End If
                    End If
                    oRs.DeleteRecord()

                    If oRs.GetNext = 0 Then Exit Do
                Loop
            End If
            oRs = Nothing
            rs = Nothing

            'delete from folder if exists (for selltis file only)
            '
            'If Not String.IsNullOrEmpty(strFilepath) Then
            '    strFilepath = GetRootPath() & strFilepath
            '    If File.Exists(strFilepath) Then
            '        File.Delete(strFilepath)
            '    End If
            'Else
            '    Return "File not existed"
            'End If

            Return "Success"
        'Catch ex As Exception
        '    Return ex.ToString()
        'End Try

    End Function

    Public Sub DeleteAllAttachmentsByFileGidId(sFileGidId As String, sViewName As String)

        Dim oRs = New clRowSet("AD", clC.SELL_EDIT, "TXT_FILEGIDID='" & sFileGidId & "'", , "**")
        If oRs.GetFirst Then
            Dim retval As Integer = oRs.DeleteRecord()
            If retval = 1 Then
                Dim sFolderPath As String = Path.Combine(GetRootPath(), sViewName & "\" & sFileGidId)
                If Directory.Exists(sFolderPath) Then
                    DeleteFolder(sFolderPath)
                End If
            End If
        End If
        oRs = Nothing

    End Sub

    Public Function CopyAttachments(ByVal Source_File_Gid_Id As String, ByVal Destination_File_Gid_Id As String, Optional sViewName As String = "") As Boolean
        Dim sProc As String = "clTransform::CopyAttachments"
        Try
            Dim sFields As String = "TXT_FIELDNAME,TXT_ATTACHMENTNAME,TXT_FILENAME,TXT_ATTACHMENTEXTENSION,DR__ATTACHMENT,TXT_ATTACHMENTDIRECTORYPATH,TXT_SOURCE"

            Dim dtFiles As DataTable = GetAD_DataTable("TXT_FileGIDID='" & Source_File_Gid_Id & "' ", sFields)



            If dtFiles IsNot Nothing AndAlso dtFiles.Rows.Count > 0 Then
                Dim sHostName As String = clSettings.GetHostName()
                'Dim RootPath As String = System.Configuration.ConfigurationManager.AppSettings("AttachmentsPath").ToString()
                Dim RootPath As String = DirectCast(HttpContext.Current.Session(sHostName + "_SiteSettings"), DataTable).Rows(0)("AttachmentsPath")
                For index = 0 To dtFiles.Rows.Count - 1

                    Dim ViewName As String = String.Empty
                    Dim FileName As String = String.Empty
                    Dim FileExtension As String = String.Empty
                    Dim SourceFilePath As String = String.Empty
                    Dim FileSize As String = String.Empty
                    Dim FieldName As String = String.Empty
                    Dim NewFilePath As String = String.Empty
                    Dim sSource As String = String.Empty
                    'Dim sSourceObjectId As String = String.Empty

                    ViewName = IIf(String.IsNullOrEmpty(sViewName), dtFiles.Rows(index)("TXT_FILENAME").ToString(), sViewName)
                    FileName = dtFiles.Rows(index)("TXT_AttachmentName").ToString()
                    FileExtension = dtFiles.Rows(index)("TXT_ATTACHMENTEXTENSION").ToString()
                    SourceFilePath = RootPath & dtFiles.Rows(index)("TXT_ATTACHMENTDIRECTORYPATH").ToString()
                    FileSize = dtFiles.Rows(index)("DR__ATTACHMENT").ToString()
                    FieldName = dtFiles.Rows(index)("TXT_FIELDNAME").ToString() 'dtFiles.Rows(index)("TXT_ATTACHMENTDIRECTORYPATH").ToString().Split("/").GetValue(2).ToString() '
                    sSource = IIf(IsDBNull(dtFiles.Rows(index)("TXT_SOURCE")), "Selltis", dtFiles.Rows(index)("TXT_SOURCE").ToString())
                    'sSourceObjectId = IIf(IsDBNull(dtFiles.Rows(index)("TXT_OBJECTID")), "", dtFiles.Rows(index)("TXT_OBJECTID").ToString())
                    NewFilePath = ViewName & "\" & Destination_File_Gid_Id & "\" & FieldName '& "\" & FileName

                    If System.IO.File.Exists(SourceFilePath) Then

                        Dim retval As Integer = SaveAttachment(ViewName, Destination_File_Gid_Id, FileExtension, FileSize, FileName, NewFilePath & "\" & FileName, FieldName, sSource)
                        If retval = 1 Then

                            Dim AttachmentsStorageType As String = System.Configuration.ConfigurationManager.AppSettings("AttachmentsStorageType").ToString()
                            If AttachmentsStorageType = "Server" Then
                                NewFilePath = RootPath & NewFilePath
                                If Not System.IO.Directory.Exists(NewFilePath) Then
                                    System.IO.Directory.CreateDirectory(NewFilePath)
                                End If
                                If Not System.IO.File.Exists(NewFilePath & "\" & FileName) Then
                                    System.IO.File.Copy(SourceFilePath, NewFilePath & "\" & FileName)
                                End If
                            Else
                                Dim sourceFileFolder = clAzureFileStorage.GetFinalFolder(ViewName, Source_File_Gid_Id, FieldName, "", False)
                                Dim destinationFileFolder = clAzureFileStorage.GetFinalFolder(ViewName, Destination_File_Gid_Id, FieldName, "", False)

                                If Not clAzureFileStorage.IsFileExists(destinationFileFolder, FileName) Then
                                    clAzureFileStorage.CopyFile(sourceFileFolder, destinationFileFolder, FileName)
                                End If
                            End If

                        End If
                        ' rsnew = Nothing
                    End If
                Next
                'rs = Nothing
            Else
                Return False
            End If

            Return True
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, , sProc)
            'End If
            Return False
        End Try
    End Function

    Public Function SaveAttachment(sViewName As String, sGID As String, sFileExtension As String, filesize As String, sFilename As String, sFileFullpath As String, sFieldName As String, sSource As String) As Integer
        Dim sProc As String = "clTransform::SaveAttachment"
        Try

            If Not String.IsNullOrEmpty(sFilename) Then
                Dim rs = New clRowSet("AD", clC.SELL_ADD, , , , , , , , , , True)
                rs.SetFieldVal("TXT_FILENAME", sViewName, 1)
                rs.SetFieldVal("TXT_FILEGIDID", sGID, 1)
                rs.SetFieldVal("TXT_ATTACHMENTEXTENSION", sFileExtension, 1)
                rs.SetFieldVal("DR__ATTACHMENT", filesize, 1)
                rs.SetFieldVal("TXT_AttachmentName", sFilename, 1)
                rs.SetFieldVal("TXT_ATTACHMENTDIRECTORYPATH", sFileFullpath, 1)
                rs.SetFieldVal("TXT_FIELDNAME", sFieldName, 1)
                rs.SetFieldVal("TXT_SOURCE", sSource, 1)
                'rs.SetFieldVal("txt_ObjectId", sSourceObjectId, 1)
                Return rs.Commit()
            Else
                Return 0
            End If
            
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, , sProc)
            'End If
            Return 0
        End Try
    End Function

    Public Function GetAttachments(par_sField_Name As String, par_File_GID_ID As String) As DataTable

        par_sField_Name = par_sField_Name.Replace("ADV", "ADR")

        Dim dt As DataTable
        If String.IsNullOrEmpty(par_sField_Name) Then
            dt = GetAD_DataTable("TXT_FILEGIDID='" & par_File_GID_ID & "' ", "TXT_AttachmentName,GID_ID,TXT_Source,TXT_ATTACHMENTDIRECTORYPATH")
        Else
            dt = GetAD_DataTable("TXT_FILEGIDID='" & par_File_GID_ID & "' AND TXT_FIELDNAME='" & par_sField_Name & "' ", "TXT_AttachmentName,GID_ID,TXT_Source,TXT_ATTACHMENTDIRECTORYPATH")
        End If

        Return dt

    End Function

    Public Function GetAttachments_ADR(par_sField_Name As String, par_File_GID_ID As String, par_Source As String) As String
        Dim dtAttachments As DataTable = GetAttachments(par_sField_Name, par_File_GID_ID)
        Dim dv As DataView = New DataView(dtAttachments, "TXT_Source='" & par_Source & "'", "", DataViewRowState.CurrentRows)
        Dim sAdrAttachments As New StringBuilder()

        If dv IsNot Nothing AndAlso dv.ToTable() IsNot Nothing Then
            Dim dtret As DataTable = dv.ToTable()
            For index = 0 To dtret.Rows().Count - 1
                sAdrAttachments.Append(dtret.Rows(index)("TXT_AttachmentName").ToString() & "~" & dtret.Rows(index)("TXT_ATTACHMENTDIRECTORYPATH").ToString() & "|")
            Next
            If sAdrAttachments.Length > 0 Then
                Return sAdrAttachments.ToString().Remove(sAdrAttachments.Length - 1, 1)
            End If
            Return ""
        End If

    End Function

    Public Function DeleteFiles(ByVal sFieldName As String, ByVal sViewName As String, ByVal sGID As String, ByVal AttachmentsPath As String) As Integer

        Dim sDeletedFileNames As String = String.Empty
        Dim sKey As String = clSettings.GetHostName() & "_"
        If System.Web.HttpContext.Current.Session(sKey & sFieldName & "_DeletedFiles") IsNot Nothing Then

            sDeletedFileNames = System.Web.HttpContext.Current.Session(sKey & sFieldName & "_DeletedFiles").ToString()

            If Not String.IsNullOrEmpty(sDeletedFileNames) Then

                Dim strDeleteFiles() As String
                strDeleteFiles = sDeletedFileNames.Split("|")

                For Each sdelfile As String In strDeleteFiles

                    Dim sDeleteFilePath As String = AttachmentsPath & sViewName & "/" & sGID & "/" & sFieldName & "/" & sdelfile

                    'delete from db
                    Dim oRs = New clRowSet("AD", clC.SELL_EDIT, "TXT_FileGIDID='" & sGID & "' AND TXT_AttachmentName='" & sdelfile & "'", , "**")
                    If oRs.GetFirst Then
                        oRs.DeleteRecord()
                    End If
                    oRs = Nothing

                    If System.IO.File.Exists(sDeleteFilePath) Then
                        'Try
                        'delete from folder
                        If File.Exists(sDeleteFilePath) Then
                                System.IO.File.Delete(sDeleteFilePath)
                            End If
                        'Catch ex As Exception
                        'End Try
                    End If
                Next
                'clear the session value
                System.Web.HttpContext.Current.Session(sKey & sFieldName & "_DeletedFiles") = ""
            End If
        End If
    End Function

    Public Function DeleteFilesAzure(ByVal sFieldName As String, ByVal sViewName As String, ByVal sGID As String, ByVal AttachmentsPath As String) As Integer

        Dim sDeletedFileNames As String = String.Empty
        Dim sKey As String = clSettings.GetHostName() & "_"
        If System.Web.HttpContext.Current.Session(sKey & sFieldName & "_DeletedFiles") IsNot Nothing Then

            sDeletedFileNames = System.Web.HttpContext.Current.Session(sKey & sFieldName & "_DeletedFiles").ToString()

            If Not String.IsNullOrEmpty(sDeletedFileNames) Then

                Dim strDeleteFiles() As String
                strDeleteFiles = sDeletedFileNames.Split("|")

                For Each sdelfile As String In strDeleteFiles

                    ''Dim sDeleteFilePath As String = AttachmentsPath & sViewName & "/" & sGID & "/" & sFieldName & "/" & sdelfile

                    'delete from db
                    Dim oRs = New clRowSet("AD", clC.SELL_EDIT, "TXT_FileGIDID='" & sGID & "' AND TXT_AttachmentName='" & sdelfile & "'", , "**")
                    If oRs.GetFirst Then
                        oRs.DeleteRecord()
                    End If
                    oRs = Nothing

                    'If System.IO.File.Exists(sDeleteFilePath) Then
                    '    Try
                    '        ''delete from folder
                    '        If File.Exists(sDeleteFilePath) Then
                    '            System.IO.File.Delete(sDeleteFilePath)
                    '        End If

                    '    Catch ex As Exception
                    '    End Try
                    'End If

                    If sdelfile <> Nothing And sdelfile <> "" Then
                        Dim FinalDir = clAzureFileStorage.GetFinalFolder(sViewName, sGID, sFieldName, "", False)
                        clAzureFileStorage.DeleteFile(FinalDir, sdelfile)
                    End If

                Next
                'clear the session value
                System.Web.HttpContext.Current.Session(sKey & sFieldName & "_DeletedFiles") = ""
            End If
        End If
    End Function

    Public Function GetFullFilePathByGID_ID(gidid As String) As String
        Dim sFilepath As String = String.Empty
        Dim dt As DataTable = GetAD_DataTable("GID_ID='" + gidid.ToString() + "' ", "TXT_ATTACHMENTDIRECTORYPATH")
        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            sFilepath = dt.Rows(0)("TXT_ATTACHMENTDIRECTORYPATH").ToString()
        End If
        Return sFilepath
    End Function

    Private Function GetMIMEType(extension As String) As String
        Select Case extension.ToLower()
            Case ".3dm"
                Return "x-world/x-3dmf"
            Case ".3dmf"
                Return "x-world/x-3dmf"
            Case ".a"
                Return "application/octet-stream"
            Case ".aab"
                Return "application/x-authorware-bin"
            Case ".aam"
                Return "application/x-authorware-map"
            Case ".aas"
                Return "application/x-authorware-seg"
            Case ".abc"
                Return "text/vnd.abc"
            Case ".acgi"
                Return "text/html"
            Case ".afl"
                Return "video/animaflex"
            Case ".ai"
                Return "application/postscript"
            Case ".aif"
                Return "audio/aiff"
            Case ".aifc"
                Return "audio/aiff"
            Case ".aiff"
                Return "audio/aiff"
            Case ".aim"
                Return "application/x-aim"
            Case ".aip"
                Return "text/x-audiosoft-intra"
            Case ".ani"
                Return "application/x-navi-animation"
            Case ".aos"
                Return "application/x-nokia-9000-communicator-add-on-software"
            Case ".aps"
                Return "application/mime"
            Case ".arc"
                Return "application/octet-stream"
            Case ".arj"
                Return "application/arj"
            Case ".art"
                Return "image/x-jg"
            Case ".asf"
                Return "video/x-ms-asf"
            Case ".asm"
                Return "text/x-asm"
            Case ".asp"
                Return "text/asp"
            Case ".asx"
                Return "application/x-mplayer2"
            Case ".au"
                Return "audio/basic"
            Case ".avi"
                Return "application/x-troff-msvideo"
            Case ".avs"
                Return "video/avs-video"
            Case ".bcpio"
                Return "application/x-bcpio"
            Case ".bin"
                Return "application/mac-binary"
            Case ".bm"
                Return "image/bmp"
            Case ".bmp"
                Return "image/bmp"
            Case ".boo"
                Return "application/book"
            Case ".book"
                Return "application/book"
            Case ".boz"
                Return "application/x-bzip2"
            Case ".bsh"
                Return "application/x-bsh"
            Case ".bz"
                Return "application/x-bzip"
            Case ".bz2"
                Return "application/x-bzip2"
            Case ".c"
                Return "text/plain"
            Case ".c++"
                Return "text/plain"
            Case ".cat"
                Return "application/vnd.ms-pki.seccat"
            Case ".cc"
                Return "text/plain"
            Case ".ccad"
                Return "application/clariscad"
            Case ".cco"
                Return "application/x-cocoa"
            Case ".cdf"
                Return "application/cdf"
            Case ".cer"
                Return "application/pkix-cert"
            Case ".cha"
                Return "application/x-chat"
            Case ".chat"
                Return "application/x-chat"
            Case ".class"
                Return "application/java"
            Case ".com"
                Return "application/octet-stream"
            Case ".conf"
                Return "text/plain"
            Case ".cpio"
                Return "application/x-cpio"
            Case ".cpp"
                Return "text/x-c"
            Case ".cpt"
                Return "application/mac-compactpro"
            Case ".crl"
                Return "application/pkcs-crl"
            Case ".crt"
                Return "application/pkix-cert"
            Case ".csh"
                Return "application/x-csh"
            Case ".css"
                Return "application/x-pointplus"
            Case ".cxx"
                Return "text/plain"
            Case ".dcr"
                Return "application/x-director"
            Case ".deepv"
                Return "application/x-deepv"
            Case ".def"
                Return "text/plain"
            Case ".der"
                Return "application/x-x509-ca-cert"
            Case ".dif"
                Return "video/x-dv"
            Case ".dir"
                Return "application/x-director"
            Case ".dl"
                Return "video/dl"
            Case ".doc"
                Return "application/msword"
            Case ".docx"
                Return "application/msword"
            Case ".dot"
                Return "application/msword"
            Case ".dp"
                Return "application/commonground"
            Case ".drw"
                Return "application/drafting"
            Case ".dump"
                Return "application/octet-stream"
            Case ".dv"
                Return "video/x-dv"
            Case ".dvi"
                Return "application/x-dvi"
            Case ".dwf"
                Return "drawing/x-dwf (old)"
            Case ".dwg"
                Return "application/acad"
            Case ".dxf"
                Return "application/dxf"
            Case ".dxr"
                Return "application/x-director"
            Case ".el"
                Return "text/x-script.elisp"
            Case ".elc"
                Return "application/x-bytecode.elisp (compiled elisp)"
            Case ".env"
                Return "application/x-envoy"
            Case ".eps"
                Return "application/postscript"
            Case ".es"
                Return "application/x-esrehber"
            Case ".etx"
                Return "text/x-setext"
            Case ".evy"
                Return "application/envoy"
            Case ".exe"
                Return "application/octet-stream"
            Case ".f"
                Return "text/plain"
            Case ".f77"
                Return "text/x-fortran"
            Case ".f90"
                Return "text/plain"
            Case ".fdf"
                Return "application/vnd.fdf"
            Case ".fif"
                Return "application/fractals"
            Case ".fli"
                Return "video/fli"
            Case ".flo"
                Return "image/florian"
            Case ".flx"
                Return "text/vnd.fmi.flexstor"
            Case ".fmf"
                Return "video/x-atomic3d-feature"
            Case ".for"
                Return "text/plain"
            Case ".fpx"
                Return "image/vnd.fpx"
            Case ".frl"
                Return "application/freeloader"
            Case ".funk"
                Return "audio/make"
            Case ".g"
                Return "text/plain"
            Case ".g3"
                Return "image/g3fax"
            Case ".gif"
                Return "image/gif"
            Case ".gl"
                Return "video/gl"
            Case ".gsd"
                Return "audio/x-gsm"
            Case ".gsm"
                Return "audio/x-gsm"
            Case ".gsp"
                Return "application/x-gsp"
            Case ".gss"
                Return "application/x-gss"
            Case ".gtar"
                Return "application/x-gtar"
            Case ".gz"
                Return "application/x-compressed"
            Case ".gzip"
                Return "application/x-gzip"
            Case ".h"
                Return "text/plain"
            Case ".hdf"
                Return "application/x-hdf"
            Case ".help"
                Return "application/x-helpfile"
            Case ".hgl"
                Return "application/vnd.hp-hpgl"
            Case ".hh"
                Return "text/plain"
            Case ".hlb"
                Return "text/x-script"
            Case ".hlp"
                Return "application/hlp"
            Case ".hpg"
                Return "application/vnd.hp-hpgl"
            Case ".hpgl"
                Return "application/vnd.hp-hpgl"
            Case ".hqx"
                Return "application/binhex"
            Case ".hta"
                Return "application/hta"
            Case ".htc"
                Return "text/x-component"
            Case ".htm"
                Return "text/html"
            Case ".html"
                Return "text/html"
            Case ".htmls"
                Return "text/html"
            Case ".htt"
                Return "text/webviewhtml"
            Case ".htx"
                Return "text/html"
            Case ".ice"
                Return "x-conference/x-cooltalk"
            Case ".ico"
                Return "image/x-icon"
            Case ".idc"
                Return "text/plain"
            Case ".ief"
                Return "image/ief"
            Case ".iefs"
                Return "image/ief"
            Case ".iges"
                Return "application/iges"
            Case ".igs"
                Return "application/iges"
            Case ".ima"
                Return "application/x-ima"
            Case ".imap"
                Return "application/x-httpd-imap"
            Case ".inf"
                Return "application/inf"
            Case ".ins"
                Return "application/x-internett-signup"
            Case ".ip"
                Return "application/x-ip2"
            Case ".isu"
                Return "video/x-isvideo"
            Case ".it"
                Return "audio/it"
            Case ".iv"
                Return "application/x-inventor"
            Case ".ivr"
                Return "i-world/i-vrml"
            Case ".ivy"
                Return "application/x-livescreen"
            Case ".jam"
                Return "audio/x-jam"
            Case ".jav"
                Return "text/plain"
            Case ".java"
                Return "text/plain"
            Case ".jcm"
                Return "application/x-java-commerce"
            Case ".jfif"
                Return "image/jpeg"
            Case ".jfif-tbnl"
                Return "image/jpeg"
            Case ".jpe"
                Return "image/jpeg"
            Case ".jpeg"
                Return "image/jpeg"
            Case ".jpg"
                Return "image/jpeg"
            Case ".jps"
                Return "image/x-jps"
            Case ".js"
                Return "application/x-javascript"
            Case ".jut"
                Return "image/jutvision"
            Case ".kar"
                Return "audio/midi"
            Case ".ksh"
                Return "application/x-ksh"
            Case ".la"
                Return "audio/nspaudio"
            Case ".lam"
                Return "audio/x-liveaudio"
            Case ".latex"
                Return "application/x-latex"
            Case ".lha"
                Return "application/lha"
            Case ".lhx"
                Return "application/octet-stream"
            Case ".list"
                Return "text/plain"
            Case ".lma"
                Return "audio/nspaudio"
            Case ".log"
                Return "text/plain"
            Case ".lsp"
                Return "application/x-lisp"
            Case ".lst"
                Return "text/plain"
            Case ".lsx"
                Return "text/x-la-asf"
            Case ".ltx"
                Return "application/x-latex"
            Case ".lzh"
                Return "application/octet-stream"
            Case ".lzx"
                Return "application/lzx"
            Case ".m"
                Return "text/plain"
            Case ".m1v"
                Return "video/mpeg"
            Case ".m2a"
                Return "audio/mpeg"
            Case ".m2v"
                Return "video/mpeg"
            Case ".m3u"
                Return "audio/x-mpequrl"
            Case ".man"
                Return "application/x-troff-man"
            Case ".map"
                Return "application/x-navimap"
            Case ".mar"
                Return "text/plain"
            Case ".mbd"
                Return "application/mbedlet"
            Case ".mc$"
                Return "application/x-magic-cap-package-1.0"
            Case ".mcd"
                Return "application/mcad"
            Case ".mcf"
                Return "image/vasa"
            Case ".mcp"
                Return "application/netmc"
            Case ".me"
                Return "application/x-troff-me"
            Case ".mht"
                Return "message/rfc822"
            Case ".mhtml"
                Return "message/rfc822"
            Case ".mid"
                Return "application/x-midi"
            Case ".midi"
                Return "application/x-midi"
            Case ".mif"
                Return "application/x-frame"
            Case ".mime"
                Return "message/rfc822"
            Case ".mjf"
                Return "audio/x-vnd.audioexplosion.mjuicemediafile"
            Case ".mjpg"
                Return "video/x-motion-jpeg"
            Case ".mm"
                Return "application/base64"
            Case ".mme"
                Return "application/base64"
            Case ".mod"
                Return "audio/mod"
            Case ".moov"
                Return "video/quicktime"
            Case ".mov"
                Return "video/quicktime"
            Case ".movie"
                Return "video/x-sgi-movie"
            Case ".mp2"
                Return "audio/mpeg"
            Case ".mp3"
                Return "audio/mpeg3"
            Case ".mpa"
                Return "audio/mpeg"
            Case ".mpc"
                Return "application/x-project"
            Case ".mpe"
                Return "video/mpeg"
            Case ".mpeg"
                Return "video/mpeg"
            Case ".mpg"
                Return "audio/mpeg"
            Case ".mpga"
                Return "audio/mpeg"
            Case ".mpp"
                Return "application/vnd.ms-project"
            Case ".mpt"
                Return "application/x-project"
            Case ".mpv"
                Return "application/x-project"
            Case ".mpx"
                Return "application/x-project"
            Case ".mrc"
                Return "application/marc"
            Case ".ms"
                Return "application/x-troff-ms"
            Case ".mv"
                Return "video/x-sgi-movie"
            Case ".my"
                Return "audio/make"
            Case ".mzz"
                Return "application/x-vnd.audioexplosion.mzz"
            Case ".nap"
                Return "image/naplps"
            Case ".naplps"
                Return "image/naplps"
            Case ".nc"
                Return "application/x-netcdf"
            Case ".ncm"
                Return "application/vnd.nokia.configuration-message"
            Case ".nif"
                Return "image/x-niff"
            Case ".niff"
                Return "image/x-niff"
            Case ".nix"
                Return "application/x-mix-transfer"
            Case ".nsc"
                Return "application/x-conference"
            Case ".nvd"
                Return "application/x-navidoc"
            Case ".o"
                Return "application/octet-stream"
            Case ".oda"
                Return "application/oda"
            Case ".omc"
                Return "application/x-omc"
            Case ".omcd"
                Return "application/x-omcdatamaker"
            Case ".omcr"
                Return "application/x-omcregerator"
            Case ".p"
                Return "text/x-pascal"
            Case ".p10"
                Return "application/pkcs10"
            Case ".p12"
                Return "application/pkcs-12"
            Case ".p7a"
                Return "application/x-pkcs7-signature"
            Case ".p7c"
                Return "application/pkcs7-mime"
            Case ".p7m"
                Return "application/pkcs7-mime"
            Case ".p7r"
                Return "application/x-pkcs7-certreqresp"
            Case ".p7s"
                Return "application/pkcs7-signature"
            Case ".part"
                Return "application/pro_eng"
            Case ".pas"
                Return "text/pascal"
            Case ".pbm"
                Return "image/x-portable-bitmap"
            Case ".pcl"
                Return "application/vnd.hp-pcl"
            Case ".pct"
                Return "image/x-pict"
            Case ".pcx"
                Return "image/x-pcx"
            Case ".pdb"
                Return "chemical/x-pdb"
            Case ".pdf"
                Return "application/pdf"
            Case ".pfunk"
                Return "audio/make"
            Case ".pgm"
                Return "image/x-portable-graymap"
            Case ".pic"
                Return "image/pict"
            Case ".pict"
                Return "image/pict"
            Case ".pkg"
                Return "application/x-newton-compatible-pkg"
            Case ".pko"
                Return "application/vnd.ms-pki.pko"
            Case ".pl"
                Return "text/plain"
            Case ".plx"
                Return "application/x-pixclscript"
            Case ".pm"
                Return "image/x-xpixmap"
            Case ".pm4"
                Return "application/x-pagemaker"
            Case ".pm5"
                Return "application/x-pagemaker"
            Case ".png"
                Return "image/png"
            Case ".pnm"
                Return "application/x-portable-anymap"
            Case ".pot"
                Return "application/mspowerpoint"
            Case ".pov"
                Return "model/x-pov"
            Case ".ppa"
                Return "application/vnd.ms-powerpoint"
            Case ".ppm"
                Return "image/x-portable-pixmap"
            Case ".pps"
                Return "application/mspowerpoint"
            Case ".ppt"
                Return "application/mspowerpoint"
            Case ".ppz"
                Return "application/mspowerpoint"
            Case ".pre"
                Return "application/x-freelance"
            Case ".prt"
                Return "application/pro_eng"
            Case ".ps"
                Return "application/postscript"
            Case ".psd"
                Return "application/octet-stream"
            Case ".pvu"
                Return "paleovu/x-pv"
            Case ".pwz"
                Return "application/vnd.ms-powerpoint"
            Case ".py"
                Return "text/x-script.phyton"
            Case ".pyc"
                Return "application/x-bytecode.python"
            Case ".qcp"
                Return "audio/vnd.qcelp"
            Case ".qd3"
                Return "x-world/x-3dmf"
            Case ".qd3d"
                Return "x-world/x-3dmf"
            Case ".qif"
                Return "image/x-quicktime"
            Case ".qt"
                Return "video/quicktime"
            Case ".qtc"
                Return "video/x-qtc"
            Case ".qti"
                Return "image/x-quicktime"
            Case ".qtif"
                Return "image/x-quicktime"
            Case ".ra"
                Return "audio/x-pn-realaudio"
            Case ".ram"
                Return "audio/x-pn-realaudio"
            Case ".ras"
                Return "application/x-cmu-raster"
            Case ".rast"
                Return "image/cmu-raster"
            Case ".rexx"
                Return "text/x-script.rexx"
            Case ".rf"
                Return "image/vnd.rn-realflash"
            Case ".rgb"
                Return "image/x-rgb"
            Case ".rm"
                Return "application/vnd.rn-realmedia"
            Case ".rmi"
                Return "audio/mid"
            Case ".rmm"
                Return "audio/x-pn-realaudio"
            Case ".rmp"
                Return "audio/x-pn-realaudio"
            Case ".rng"
                Return "application/ringing-tones"
            Case ".rnx"
                Return "application/vnd.rn-realplayer"
            Case ".roff"
                Return "application/x-troff"
            Case ".rp"
                Return "image/vnd.rn-realpix"
            Case ".rpm"
                Return "audio/x-pn-realaudio-plugin"
            Case ".rt"
                Return "text/richtext"
            Case ".rtf"
                Return "application/rtf"
            Case ".rtx"
                Return "application/rtf"
            Case ".rv"
                Return "video/vnd.rn-realvideo"
            Case ".s"
                Return "text/x-asm"
            Case ".s3m"
                Return "audio/s3m"
            Case ".saveme"
                Return "application/octet-stream"
            Case ".sbk"
                Return "application/x-tbook"
            Case ".scm"
                Return "application/x-lotusscreencam"
            Case ".sdml"
                Return "text/plain"
            Case ".sdp"
                Return "application/sdp"
            Case ".sdr"
                Return "application/sounder"
            Case ".sea"
                Return "application/sea"
            Case ".set"
                Return "application/set"
            Case ".sgm"
                Return "text/sgml"
            Case ".sgml"
                Return "text/sgml"
            Case ".sh"
                Return "application/x-bsh"
            Case ".shar"
                Return "application/x-bsh"
            Case ".shtml"
                Return "text/html"
            Case ".sid"
                Return "audio/x-psid"
            Case ".sit"
                Return "application/x-sit"
            Case ".skd"
                Return "application/x-koan"
            Case ".skm"
                Return "application/x-koan"
            Case ".skp"
                Return "application/x-koan"
            Case ".skt"
                Return "application/x-koan"
            Case ".sl"
                Return "application/x-seelogo"
            Case ".smi"
                Return "application/smil"
            Case ".smil"
                Return "application/smil"
            Case ".snd"
                Return "audio/basic"
            Case ".sol"
                Return "application/solids"
            Case ".spc"
                Return "application/x-pkcs7-certificates"
            Case ".spl"
                Return "application/futuresplash"
            Case ".spr"
                Return "application/x-sprite"
            Case ".sprite"
                Return "application/x-sprite"
            Case ".src"
                Return "application/x-wais-source"
            Case ".ssi"
                Return "text/x-server-parsed-html"
            Case ".ssm"
                Return "application/streamingmedia"
            Case ".sst"
                Return "application/vnd.ms-pki.certstore"
            Case ".step"
                Return "application/step"
            Case ".stl"
                Return "application/sla"
            Case ".stp"
                Return "application/step"
            Case ".sv4cpio"
                Return "application/x-sv4cpio"
            Case ".sv4crc"
                Return "application/x-sv4crc"
            Case ".svf"
                Return "image/vnd.dwg"
            Case ".svr"
                Return "application/x-world"
            Case ".swf"
                Return "application/x-shockwave-flash"
            Case ".t"
                Return "application/x-troff"
            Case ".talk"
                Return "text/x-speech"
            Case ".tar"
                Return "application/x-tar"
            Case ".tbk"
                Return "application/toolbook"
            Case ".tcl"
                Return "application/x-tcl"
            Case ".tcsh"
                Return "text/x-script.tcsh"
            Case ".tex"
                Return "application/x-tex"
            Case ".texi"
                Return "application/x-texinfo"
            Case ".texinfo"
                Return "application/x-texinfo"
            Case ".text"
                Return "application/plain"
            Case ".tgz"
                Return "application/gnutar"
            Case ".tif"
                Return "image/tiff"
            Case ".tiff"
                Return "image/tiff"
            Case ".tr"
                Return "application/x-troff"
            Case ".tsi"
                Return "audio/tsp-audio"
            Case ".tsp"
                Return "application/dsptype"
            Case ".tsv"
                Return "text/tab-separated-values"
            Case ".turbot"
                Return "image/florian"
            Case ".txt"
                Return "text/plain"
            Case ".uil"
                Return "text/x-uil"
            Case ".uni"
                Return "text/uri-list"
            Case ".unis"
                Return "text/uri-list"
            Case ".unv"
                Return "application/i-deas"
            Case ".uri"
                Return "text/uri-list"
            Case ".uris"
                Return "text/uri-list"
            Case ".ustar"
                Return "application/x-ustar"
            Case ".uu"
                Return "application/octet-stream"
            Case ".uue"
                Return "text/x-uuencode"
            Case ".vcd"
                Return "application/x-cdlink"
            Case ".vcs"
                Return "text/x-vcalendar"
            Case ".vda"
                Return "application/vda"
            Case ".vdo"
                Return "video/vdo"
            Case ".vew"
                Return "application/groupwise"
            Case ".viv"
                Return "video/vivo"
            Case ".vivo"
                Return "video/vivo"
            Case ".vmd"
                Return "application/vocaltec-media-desc"
            Case ".vmf"
                Return "application/vocaltec-media-file"
            Case ".voc"
                Return "audio/voc"
            Case ".vos"
                Return "video/vosaic"
            Case ".vox"
                Return "audio/voxware"
            Case ".vqe"
                Return "audio/x-twinvq-plugin"
            Case ".vqf"
                Return "audio/x-twinvq"
            Case ".vql"
                Return "audio/x-twinvq-plugin"
            Case ".vrml"
                Return "application/x-vrml"
            Case ".vrt"
                Return "x-world/x-vrt"
            Case ".vsd"
                Return "application/x-visio"
            Case ".vst"
                Return "application/x-visio"
            Case ".vsw"
                Return "application/x-visio"
            Case ".w60"
                Return "application/wordperfect6.0"
            Case ".w61"
                Return "application/wordperfect6.1"
            Case ".w6w"
                Return "application/msword"
            Case ".wav"
                Return "audio/wav"
            Case ".wb1"
                Return "application/x-qpro"
            Case ".wbmp"
                Return "image/vnd.wap.wbmp"
            Case ".web"
                Return "application/vnd.xara"
            Case ".wiz"
                Return "application/msword"
            Case ".wk1"
                Return "application/x-123"
            Case ".wmf"
                Return "windows/metafile"
            Case ".wml"
                Return "text/vnd.wap.wml"
            Case ".wmlc"
                Return "application/vnd.wap.wmlc"
            Case ".wmls"
                Return "text/vnd.wap.wmlscript"
            Case ".wmlsc"
                Return "application/vnd.wap.wmlscriptc"
            Case ".word"
                Return "application/msword"
            Case ".wp"
                Return "application/wordperfect"
            Case ".wp5"
                Return "application/wordperfect"
            Case ".wp6"
                Return "application/wordperfect"
            Case ".wpd"
                Return "application/wordperfect"
            Case ".wq1"
                Return "application/x-lotus"
            Case ".wri"
                Return "application/mswrite"
            Case ".wrl"
                Return "application/x-world"
            Case ".wrz"
                Return "model/vrml"
            Case ".wsc"
                Return "text/scriplet"
            Case ".wsrc"
                Return "application/x-wais-source"
            Case ".wtk"
                Return "application/x-wintalk"
            Case ".xbm"
                Return "image/x-xbitmap"
            Case ".xdr"
                Return "video/x-amt-demorun"
            Case ".xgz"
                Return "xgl/drawing"
            Case ".xif"
                Return "image/vnd.xiff"
            Case ".xl"
                Return "application/excel"
            Case ".xla"
                Return "application/excel"
            Case ".xls"
                Return "application/x-excel"
            Case ".xlsx"
                Return "application/x-msexcel"
            Case ".xlb"
                Return "application/excel"
            Case ".xlc"
                Return "application/excel"
            Case ".xld"
                Return "application/excel"
            Case ".xlk"
                Return "application/excel"
            Case ".xll"
                Return "application/excel"
            Case ".xlm"
                Return "application/excel"
            Case ".xlt"
                Return "application/excel"
            Case ".xlv"
                Return "application/excel"
            Case ".xlw"
                Return "application/excel"
            Case ".xm"
                Return "audio/xm"
            Case ".xml"
                Return "application/xml"
            Case ".xmz"
                Return "xgl/movie"
            Case ".xpix"
                Return "application/x-vnd.ls-xpix"
            Case ".xpm"
                Return "image/x-xpixmap"
            Case ".x-png"
                Return "image/png"
            Case ".xsr"
                Return "video/x-amt-showrun"
            Case ".xwd"
                Return "image/x-xwd"
            Case ".xyz"
                Return "chemical/x-pdb"
            Case ".z"
                Return "application/x-compress"
            Case ".zip"
                Return "application/x-compressed"
            Case ".zoo"
                Return "application/octet-stream"
            Case ".zsh"
                Return "text/x-script.zsh"
            Case Else
                Return "application/octet-stream"
        End Select

    End Function

    Public Function GetFileNamesFromADRRawText(sAttachedFileNames As String) As String

        Dim sFileNamesOnly As String = String.Empty
        Dim srawfiles() As String = sAttachedFileNames.Split("=")
        Dim strfiles() As String = srawfiles.GetValue(0).ToString().Split("|")
        Dim tempid As String = String.Empty

        If srawfiles.Length > 1 Then
            tempid = srawfiles.GetValue(1)
        End If

        For Each sname As String In strfiles
            If String.IsNullOrEmpty(sFileNamesOnly) Then
                sFileNamesOnly = sname.Split("~").GetValue(0).ToString()
            Else
                sFileNamesOnly = sFileNamesOnly & "|" & sname.Split("~").GetValue(0).ToString()
            End If
        Next

        Return sFileNamesOnly

    End Function

    '6/19

    'Private Function DeleteFiles(ByVal sFieldName As String, ByVal sViewName As String, ByVal sGID As String, ByVal AttachmentsPath As String) As Integer
    '    Dim objclAttachments As New clAttachments()
    '    objclAttachments.DeleteFiles(sFieldName, sViewName, sGID, AttachmentsPath)
    'End Function

    Public Sub SaveAttachments(sFieldName As String, sAttachedFileNames As String, sGID As String, sViewName As String, Optional bClearSessionFiles As Boolean = True)
        'Dim sFieldName As String = String.Empty
        'Dim sAttachedFileNames As String = String.Empty
        Dim srawfiles() As String
        Dim strfiles() As String
        Dim sFileNamesOnly As String = String.Empty
        Dim tempid As String = String.Empty

        Dim AttachmentsPath As String = GetRootPath()
        Dim AttachmentsTempPath As String = GetRootTempPath()
        'Dim sViewName As String = goRowset.GetFileName

        'sFieldName = ADRFields(index)
        'sAttachedFileNames = ADRFieldsValues(index)
        srawfiles = sAttachedFileNames.Split("=")
        strfiles = srawfiles.GetValue(0).ToString().Split("|")

        'If srawfiles.Length > 1 Then
        '    tempid = srawfiles.GetValue(1)
        'End If

        Dim skey As String = clSettings.GetHostName() & "_"

        If HttpContext.Current.Session(skey & sFieldName & "_TempId") IsNot Nothing Then
            tempid = HttpContext.Current.Session(skey & sFieldName & "_TempId").ToString()
        End If

        ''Delete existing files  
        ''DeleteFiles(sFieldName, sViewName, sGID, AttachmentsPath)

        Dim AttachmentsStorageType As String = System.Configuration.ConfigurationManager.AppSettings("AttachmentsStorageType").ToString()
        If AttachmentsStorageType = "Server" Then
            DeleteFiles(sFieldName, sViewName, sGID, AttachmentsPath)
        Else
            DeleteFilesAzure(sFieldName, sViewName, sGID, AttachmentsPath)
        End If

        ''save new local files
        If Not String.IsNullOrEmpty(tempid) Then ''new selltis files attached  

            For Each sfile As String In strfiles

                If sfile.Contains("~^") Then 'not a server file
                    Dim sfiles() As String = sfile.Split(New String() {"~^"}, StringSplitOptions.None)
                    Dim strfilename As String = sfiles.GetValue(0).ToString()
                    Dim strObjectId As String = sfiles.GetValue(1).ToString()
                    If AttachmentsStorageType = "Server" Then
                        SaveAttachmentToDB(strObjectId, sFieldName, sGID, sViewName, strfilename, "", "", "EFileCabinet")
                    Else
                        SaveAttachmentToDB_Azure(strObjectId, sFieldName, sGID, sViewName, strfilename, "", "", "EFileCabinet")
                    End If
                Else
                    If AttachmentsStorageType = "Server" Then
                        SaveAttachmentToDB(tempid, sFieldName, sGID, sViewName, sfile, AttachmentsTempPath, AttachmentsPath, "Selltis")
                    Else
                        SaveAttachmentToDB_Azure(tempid, sFieldName, sGID, sViewName, sfile, AttachmentsTempPath, AttachmentsPath, "Selltis")
                    End If
                End If

            Next

            If bClearSessionFiles Then
                'deleting the temp folder
                ' Try
                If AttachmentsStorageType = "Server" Then
                        If System.IO.Directory.Exists(AttachmentsTempPath & tempid) Then
                            For Each _file As String In Directory.GetFiles(AttachmentsTempPath & tempid)
                                File.Delete(_file)
                            Next
                            System.IO.Directory.Delete(AttachmentsTempPath & tempid)
                            System.Threading.Thread.Sleep(1000)
                        End If
                    Else
                        Dim tempDir = clAzureFileStorage.GetFinalFolder("", "", "", tempid, True)

                        For Each item As IListFileItem In tempDir.ListFilesAndDirectories()
                            If item.[GetType]() = GetType(CloudFile) Then

                                Dim file As CloudFile = DirectCast(item, CloudFile)
                                file.DeleteIfExists()
                                'ElseIf item.[GetType]() = GetType(CloudFileDirectory) Then
                                '    ' Do whatever
                                '    Dim dir As CloudFileDirectory = DirectCast(item, CloudFileDirectory)
                            End If
                        Next

                        tempDir.DeleteIfExists()
                    End If
                'Catch ex As Exception
                'End Try
                System.Web.HttpContext.Current.Session(skey & sFieldName & "_Files") = ""
                System.Web.HttpContext.Current.Session(skey & sFieldName & "_TempId") = ""
            End If

        End If

    End Sub

    Private Sub SaveAttachmentToDB(ByVal tempid As String, sFieldName As String, sGID As String, sViewName As String,
                               sfile As String, AttachmentsTempPath As String, AttachmentsPath As String, Source As String)

        Dim sfileinfo() As String
        Dim temppath As String = String.Empty
        Dim rootpath As String = String.Empty
        Dim sFileExtension As String = String.Empty
        Dim sFilename As String = String.Empty
        Dim filesize As String
        Dim sFileFullpath As String = String.Empty
        Dim objclAttachments As New clAttachments()

        sfileinfo = sfile.Split("~")

        sFilename = sfileinfo.GetValue(0).ToString()
        temppath = AttachmentsTempPath & tempid & "\" & sFilename 'System.Web.HttpContext.Current.Server.MapPath(AttachmentsPath) & tempid & "\" & sFilename 
        rootpath = AttachmentsPath & sViewName & "\" & sGID & "\" & sFieldName & "\" 'System.Web.HttpContext.Current.Server.MapPath(AttachmentsPath) & sViewName & "\" & sGID & "\" & sFieldName & "\"

        If Source = "Selltis" Then

            If System.IO.File.Exists(temppath) Then

                sFileExtension = System.IO.Path.GetExtension(temppath)
                'filesize = sfileinfo.GetValue(1).ToString()
                sFileFullpath = sViewName & "/" & sGID & "/" & sFieldName & "/" & sFilename 'AttachmentsPath & sViewName & "/" & sGID & "/" & sFieldName & "/" & sFilename

                If System.IO.File.Exists(temppath) Then
                    Dim info As New FileInfo(temppath)
                    filesize = info.Length.ToString()
                End If

                Dim retval As Integer = SaveAttachment(sViewName, sGID, sFileExtension, filesize, sFilename, sFileFullpath, sFieldName, Source)

                If retval = 1 Then 'copy the files to root directory

                    If Not System.IO.Directory.Exists(rootpath) Then
                        System.IO.Directory.CreateDirectory(rootpath)
                    End If

                    'move the file from temp folder to root folder
                    '  Try
                    If System.IO.File.Exists(temppath) Then
                            If System.IO.File.Exists(rootpath & sFilename) Then
                                System.IO.File.Delete(rootpath & sFilename)
                                System.Threading.Thread.Sleep(500)
                            End If
                            System.IO.File.Copy(temppath, rootpath & sFilename)
                            System.Threading.Thread.Sleep(1000)
                        End If
                    'Catch ex As Exception
                    'End Try

                End If


            End If

        ElseIf Source = "EFileCabinet" Then  ''other than selltis
            'tempid is the objectId
            SaveAttachment(sViewName, sGID, sFileExtension, filesize, sFilename, tempid, sFieldName, Source)
        End If

    End Sub

    Private Function GetAD_DataTable(filter As String, fields As String, Optional sortfiled As String = "") As DataTable
        Dim rs As New clRowSet("AD", clC.SELL_EDIT, filter, sortfiled, fields, -1, "", "", "", "", "", False, True)
        rs.ToTable()
        Dim dt As DataTable = rs.dtTransTable
        rs = Nothing
        Return dt
    End Function

    Public Function AttachExtrenalSourceFile(sSource As String) As String

        If sSource = "EFileCabinet" Then

            If HttpContext.Current.Request.Cookies("EFCSelFile") IsNot Nothing Then

                Dim sCookie = HttpContext.Current.Request.Cookies("EFCSelFile").Value
                Dim sEFCFiles() As String = sCookie.Split("|")

                Dim sField As String = sEFCFiles.GetValue(0).ToString()
                Dim sFileName As String = sEFCFiles.GetValue(1).ToString() 'sEFCFiles.GetValue(1).ToString() & "~^" & sEFCFiles.GetValue(2).ToString()
                Dim sKey As String = clSettings.GetHostName() & "_"
                Dim sDeletedFileNames As String = String.Empty
                If HttpContext.Current.Session(sKey & sField & "_DeletedFiles") IsNot Nothing Then
                    sDeletedFileNames = HttpContext.Current.Session(sKey & sField & "_DeletedFiles").ToString()
                End If

                If sDeletedFileNames.Contains(sFileName) Then
                    HttpContext.Current.Response.Cookies("EFCSelFile").Expires = DateTime.Now.AddDays(-1)
                    Return "This file is deleted recently so please save the record first and then upload the same file again"
                End If

                If HttpContext.Current.Session(sKey & sField & "_Files") IsNot Nothing Then

                    Dim strattachments As String = String.Empty
                    strattachments = HttpContext.Current.Session(sKey & sField & "_Files").ToString()
                    If strattachments.Contains(sFileName) Then
                        HttpContext.Current.Response.Cookies("EFCSelFile").Expires = DateTime.Now.AddDays(-1)
                        Return "File with the same name is already uploaded"
                    End If
                    sFileName = sEFCFiles.GetValue(1).ToString() & "~^" & sEFCFiles.GetValue(2).ToString()
                    HttpContext.Current.Session(sKey & sField & "_Files") = HttpContext.Current.Session(sKey & sField & "_Files") & "|" & sFileName

                Else
                    HttpContext.Current.Session(sKey & sField & "_Files") = sFileName
                End If

                HttpContext.Current.Response.Cookies("EFCSelFile").Expires = DateTime.Now.AddDays(-1)

                Return ""

            End If

        End If



    End Function

    Public Sub CopyTempAttachments(sFieldName As String, sCopyFromId As String, sFromValue As String)
        'Try
        Dim sKey As String = clSettings.GetHostName() & "_"
            Dim sTempid As String = Guid.NewGuid().ToString()
            HttpContext.Current.Session(sKey & sFieldName & "_Files") = sFromValue
            HttpContext.Current.Session(sKey & sFieldName & "_TempId") = sTempid
            Dim dtCreatefromFiles As DataTable = GetAttachments(sFieldName, sCopyFromId)
            Dim srootpath As String = GetRootPath()
            Dim sTempPath As String = GetRootTempPath()
            For index = 0 To dtCreatefromFiles.Rows.Count - 1
                If dtCreatefromFiles.Rows(index)("TXT_Source").ToString() = "Selltis" Then
                    Dim sfilepath As String = Path.Combine(srootpath, dtCreatefromFiles.Rows(index)("TXT_ATTACHMENTDIRECTORYPATH").ToString())
                    Dim sfilename As String = dtCreatefromFiles.Rows(index)("TXT_AttachmentName").ToString()
                    Dim sTempfilepath As String = Path.Combine(sTempPath, sTempid)
                    If File.Exists(sfilepath) Then
                        If Not Directory.Exists(sTempfilepath) Then
                            Directory.CreateDirectory(sTempfilepath)
                        End If
                        File.Copy(sfilepath, sTempfilepath & "\" & sfilename)
                    End If
                End If
            Next
        'Catch ex As Exception
        'End Try
    End Sub

    Private Sub DeleteFolder(sFolderPath As String)
        Try
            If Directory.Exists(sFolderPath) Then
                For Each _directory As String In Directory.GetDirectories(sFolderPath)
                    DeleteFilesInTheFolder(_directory)
                    DeleteFolder(_directory)
                    If Directory.Exists(_directory) Then
                        Directory.Delete(_directory)
                    End If
                Next
                If Directory.Exists(sFolderPath) Then
                    Directory.Delete(sFolderPath)
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub DeleteFilesInTheFolder(sFolderPath As String)
        Try
            If Directory.Exists(sFolderPath) Then
                For Each _file As String In Directory.GetFiles(sFolderPath)
                    File.Delete(_file)
                Next
            End If
        Catch ex As Exception
        End Try
    End Sub

    'Public Function UploadAttachment(pViewName As String, pFile_Gid_Id As String, pFieldName As String, Optional pFileFullName As String = "", _
    '                                 Optional pFileName As String = "", Optional pFileData() As Byte = Nothing, Optional pUploadFrom As String = "") As Boolean
    '    Try

    '        Dim Source As String = "Selltis"
    '        Dim FileName As String = ""
    '        Dim FilePath As String = ""
    '        Dim sFilePath As String = ""

    '        If pUploadFrom.ToLower() = "mobile" Then
    '            FileName = pFileName
    '        Else
    '            FileName = Path.GetFileName(pFileFullName)
    '        End If

    '        FilePath = pViewName & "/" & pFile_Gid_Id & "/" & pFieldName '& "/" & FileName
    '        sFilePath = FilePath
    '        FilePath = FilePath & "/" & FileName

    '        ' ''Update the ADR_ field value
    '        'Dim rs = New clRowSet(pViewName, clC.SELL_EDIT, "GID_ID = '" & pFile_Gid_Id & "'", "", pFieldName, 1, "", "")
    '        Dim rs = New clRowSet(pViewName, clC.SELL_EDIT, "GID_ID = '" & pFile_Gid_Id & "'", "", pFieldName, 1, "", "", "", "", "", True, True)
    '        rs.ToTable()

    '        Dim FieldValueFromDB As String = rs.GetFieldVal(pFieldName)
    '        Dim NewValue As String = IIf(String.IsNullOrEmpty(FieldValueFromDB), FileName, FieldValueFromDB & "|" & FileName)

    '        rs.SetFieldVal(pFieldName, NewValue)

    '        Dim retval As Integer = rs.Commit()

    '        If retval = 1 Then

    '            Dim sFileExtension As String = ""
    '            Dim filesize As String = ""

    '            If pUploadFrom.ToLower() = "mobile" Then
    '                sFileExtension = System.IO.Path.GetExtension(pFileName)
    '                filesize = pFileData.Length.ToString()
    '            Else
    '                sFileExtension = System.IO.Path.GetExtension(pFileFullName)
    '                If System.IO.File.Exists(pFileFullName) Then
    '                    Dim info As New FileInfo(pFileFullName)
    '                    filesize = info.Length.ToString()
    '                End If
    '            End If

    '            retval = SaveAttachment(pViewName, pFile_Gid_Id, sFileExtension, filesize, FileName, FilePath, pFieldName, Source)

    '            If retval = 1 Then

    '                FilePath = GetRootPath() & FilePath
    '                sFilePath = GetRootPath() & sFilePath

    '                If Not Directory.Exists(sFilePath) Then
    '                    Directory.CreateDirectory(sFilePath)
    '                End If

    '                If File.Exists(FilePath) Then
    '                    File.Delete(FilePath)
    '                End If

    '                Dim data() As Byte

    '                If pUploadFrom.ToLower() = "mobile" Then
    '                    data = pFileData
    '                Else
    '                    data = File.ReadAllBytes(pFileFullName)
    '                End If

    '                File.WriteAllBytes(FilePath, data)

    '                Return True
    '            End If

    '        End If

    '        Return False

    '    Catch ex As Exception
    '        Throw ex
    '    End Try

    'End Function

    Public Function UploadAttachment(pViewName As String, pFile_Gid_Id As String, pFieldName As String, pFiles As ArrayList) As Boolean

        'Try

        Dim Source As String = "Selltis"
        Dim sSourceFileName As String = ""
        Dim sSourceFilePath As String = ""
        Dim sDestinationFilePath As String = ""
        Dim sDestinationFileName As String = ""

        sDestinationFilePath = pViewName & "/" & pFile_Gid_Id & "/" & pFieldName & "/"


        For index = 0 To pFiles.Count - 1
            sSourceFileName = IIf(String.IsNullOrEmpty(sSourceFileName), Path.GetFileName(pFiles(index).ToString()), sSourceFileName & "|" & Path.GetFileName(pFiles(index).ToString()))
        Next

        ' ''Update the ADR_ field value
        Dim rs = New clRowSet(pViewName, clC.SELL_EDIT, "GID_ID = '" & pFile_Gid_Id & "'", "", pFieldName, 1, "", "", "", "", "", True, True)
        rs.ToTable()

        Dim FieldValueFromDB As String = rs.GetFieldVal(pFieldName)
        Dim NewValue As String = IIf(String.IsNullOrEmpty(FieldValueFromDB), sSourceFileName, FieldValueFromDB & "|" & sSourceFileName)

        rs.SetFieldVal(pFieldName, NewValue)

        Dim retval As Integer = rs.Commit()

        If retval = 1 Then

            Dim sFileExtension As String = ""
            Dim filesize As String = ""

            Dim sDesFolderPath As String = GetRootPath() & sDestinationFilePath

            If Not Directory.Exists(sDesFolderPath) Then
                Directory.CreateDirectory(sDesFolderPath)
            End If

            For index = 0 To pFiles.Count - 1

                sSourceFileName = ""
                sSourceFilePath = ""
                sDestinationFileName = ""

                sSourceFilePath = pFiles(index).ToString()
                sSourceFileName = Path.GetFileName(sSourceFilePath)
                sFileExtension = Path.GetExtension(sSourceFilePath)
                If System.IO.File.Exists(sSourceFilePath) Then
                    Dim info As New FileInfo(sSourceFilePath)
                    filesize = info.Length.ToString()
                End If

                sDestinationFileName = sDestinationFilePath & sSourceFileName

                retval = SaveAttachment(pViewName, pFile_Gid_Id, sFileExtension, filesize, sSourceFileName, sDestinationFileName, pFieldName, Source)

                If retval = 1 Then

                    sDestinationFileName = GetRootPath() & sDestinationFileName

                    If File.Exists(sDestinationFileName) Then
                        File.Delete(sDestinationFileName)
                    End If

                    Dim data() As Byte

                    data = File.ReadAllBytes(sSourceFilePath)

                    File.WriteAllBytes(sDestinationFileName, data)

                End If

            Next

            Return True

        End If

        Return False

        'Catch ex As Exception
        '    Throw ex
        'End Try

    End Function

    Public Function UploadAttachment(pViewName As String, pFile_Gid_Id As String, pFieldName As String, pFileName As String, pSFileData As String, Optional ByVal hostName As String = "") As Boolean

        '   Try

        Dim Source As String = "Selltis"
        Dim FileName As String = ""
        Dim FilePath As String = ""
        Dim sFilePath As String = ""
        Dim pFileData() As Byte = Convert.FromBase64String(pSFileData)

        FileName = pFileName

        FilePath = pViewName & "/" & pFile_Gid_Id & "/" & pFieldName '& "/" & FileName
        sFilePath = FilePath
        FilePath = FilePath & "/" & FileName

        ' ''Update the ADR_ field value
        Dim rs = New clRowSet(pViewName, clC.SELL_EDIT, "GID_ID = '" & pFile_Gid_Id & "'", "", pFieldName, 1, "", "", "", "", "", True, True)
        rs.ToTable()

        Dim FieldValueFromDB As String = rs.GetFieldVal(pFieldName)
        Dim NewValue As String = IIf(String.IsNullOrEmpty(FieldValueFromDB), FileName, FieldValueFromDB & "|" & FileName)

        rs.SetFieldVal(pFieldName, NewValue)

        Dim retval As Integer = rs.Commit()

        If retval = 1 Then

            Dim sFileExtension As String = ""
            Dim filesize As String = ""

            sFileExtension = System.IO.Path.GetExtension(pFileName)
            filesize = pFileData.Length.ToString()

            'no need the file path here for azure file storage for mobile attachments..J
            FilePath = String.Empty

            retval = SaveAttachment(pViewName, pFile_Gid_Id, sFileExtension, filesize, FileName, FilePath, pFieldName, Source)

            If retval = 1 Then

                Dim AttachmentsStorageType As String = System.Configuration.ConfigurationManager.AppSettings("AttachmentsStorageType").ToString()
                If AttachmentsStorageType = "Server" Then
                    FilePath = GetRootPath() & FilePath
                    sFilePath = GetRootPath() & sFilePath

                    If Not Directory.Exists(sFilePath) Then
                        Directory.CreateDirectory(sFilePath)
                    End If

                    If File.Exists(FilePath) Then
                        File.Delete(FilePath)
                    End If

                    Dim data() As Byte

                    data = pFileData

                    File.WriteAllBytes(FilePath, data)
                Else
                    Dim finalDir = clAzureFileStorage.GetFinalFolder(pViewName, pFile_Gid_Id, pFieldName, "", False, hostName)
                    clAzureFileStorage.DeleteFile(finalDir, FileName)
                    Dim stream As New MemoryStream(pFileData)
                    clAzureFileStorage.UploadFromStream(finalDir, stream, FileName)
                End If

                Return True

            End If

        End If

        Return False

        'Catch ex As Exception
        '    Throw ex
        'End Try

    End Function


    'azure file storage related code
    Private Sub SaveAttachmentToDB_Azure(ByVal tempid As String, sFieldName As String, sGID As String, sViewName As String,
                              sfile As String, AttachmentsTempPath As String, AttachmentsPath As String, Source As String)

        Dim sfileinfo() As String
        Dim temppath As String = String.Empty
        Dim rootpath As String = String.Empty
        Dim sFileExtension As String = String.Empty
        Dim sFilename As String = String.Empty
        Dim filesize As String = "0"
        Dim sFileFullpath As String = String.Empty
        Dim objclAttachments As New clAttachments()

        Dim goTR As clTransform
        goTR = HttpContext.Current.Session("goTr")

        sfileinfo = sfile.Split("~")

        sFilename = sfileinfo.GetValue(0).ToString()
        'temppath = AttachmentsTempPath & tempid & "\" & sFilename 'System.Web.HttpContext.Current.Server.MapPath(AttachmentsPath) & tempid & "\" & sFilename 
        'rootpath = AttachmentsPath & sViewName & "\" & sGID & "\" & sFieldName & "\" 'System.Web.HttpContext.Current.Server.MapPath(AttachmentsPath) & sViewName & "\" & sGID & "\" & sFieldName & "\"

        If Source = "Selltis" Then

            'If System.IO.File.Exists(temppath) Then

            'sFileExtension = System.IO.Path.GetExtension(temppath)
            'filesize = sfileinfo.GetValue(1).ToString()
            'sFileFullpath = sViewName & "/" & sGID & "/" & sFieldName & "/" & sFilename 'AttachmentsPath & sViewName & "/" & sGID & "/" & sFieldName & "/" & sFilename

            sFileExtension = System.IO.Path.GetExtension(sFilename)
            Dim TempFolder = clAzureFileStorage.GetFinalFolder("", "", "", tempid, True)

            If String.IsNullOrEmpty(sFilename) = False Then
                filesize = clAzureFileStorage.GetFileSize(TempFolder, sFilename).ToString()
            End If

            'If System.IO.File.Exists(temppath) Then
            '    Dim info As New FileInfo(temppath)
            '    filesize = info.Length.ToString()
            'End If

            Dim retval As Integer = 0


            'To skip duplicate saving..J
            Dim checkexistrs = New clRowSet("AD", 3, "TXT_FILENAME='" + sViewName + "' AND TXT_FILEGIDID='" + sGID + "' AND TXT_ATTACHMENTEXTENSION='" + sFileExtension + "' AND TXT_AttachmentName='" + goTR.PrepareForSQL(sFilename) + "' AND TXT_ATTACHMENTDIRECTORYPATH='" + sFileFullpath + "' AND TXT_FIELDNAME='" + sFieldName + "' AND TXT_SOURCE='" + Source + "' ")
            If checkexistrs.GetFirst = 1 Then
                retval = 1
            Else
                retval = SaveAttachment(sViewName, sGID, sFileExtension, filesize, sFilename, sFileFullpath, sFieldName, Source)
            End If

            If retval = 1 Then 'copy the files to root directory

                Dim FieldNameDir = clAzureFileStorage.GetFinalFolder(sViewName, sGID, sFieldName, "", False)

                If String.IsNullOrEmpty(sFilename) = False AndAlso clAzureFileStorage.IsFileExists(TempFolder, sFilename) Then
                    ''copy file from temp folder to the field name directory
                    clAzureFileStorage.CopyFile(TempFolder, FieldNameDir, sFilename)
                End If

                'If Not System.IO.Directory.Exists(rootpath) Then
                '    System.IO.Directory.CreateDirectory(rootpath)
                'End If

                ''move the file from temp folder to root folder
                'Try
                '    If System.IO.File.Exists(temppath) Then
                '        If System.IO.File.Exists(rootpath & sFilename) Then
                '            System.IO.File.Delete(rootpath & sFilename)
                '            System.Threading.Thread.Sleep(500)
                '        End If
                '        System.IO.File.Copy(temppath, rootpath & sFilename)
                '        System.Threading.Thread.Sleep(1000)
                '    End If
                'Catch ex As Exception
                'End Try

            End If

            'End If

        ElseIf Source = "EFileCabinet" Then  ''other than selltis
            'tempid is the objectId
            SaveAttachment(sViewName, sGID, sFileExtension, filesize, sFilename, tempid, sFieldName, Source)
        End If

    End Sub

End Class

Public Class clEFCAttachments

    Public Function LogIntoEFC() As Boolean

        Try
            Dim goMeta As clMetaData
            Dim goP As clProject
            goMeta = HttpContext.Current.Session("goMeta")
            goP = HttpContext.Current.Session("goP")

            Dim sUserName As String = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "EFILECABINET_USERNAME", "", True)
            Dim sPassword As String = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "EFILECABINET_PASSWORD", "", True)
            Dim sAppId As String = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "EFILECABINET_APPLICATIONID", "", True)


            Dim o As New clEncrypt2
            sPassword = o.Decrypt(sPassword)

            EfcOnlineApi.ApplicationId = sAppId

            Dim authenticated As Boolean = False
            AuthenticateToEfcOnlineAPI(sUserName, sPassword)

            If Not String.IsNullOrEmpty(EfcOnlineApi.AuthToken) AndAlso EfcOnlineApi.AuthToken.Length <= 37 Then
                Return True
            Else
                Return False
            End If
        Catch ex As Exception
            Return False
        End Try

    End Function

    Public Sub AuthenticateToEfcOnlineAPI(username As String, password As String)
        EfcOnlineApi.GenerateUserAuthToken(username, password)
    End Sub

    Public Function GetEFCHierarchy(ByRef sMessage As String) As List(Of EfcObject)

        Dim goMeta As clMetaData
        Dim goP As clProject
        goMeta = HttpContext.Current.Session("goMeta")
        goP = HttpContext.Current.Session("goP")

        Dim fulltree As New List(Of EfcObject)()
        Dim cabinets As New List(Of EfcObject)()
        Dim drawers As New List(Of EfcObject)()
        Dim foldersAndFiles As New List(Of EfcObject)()
        Dim Filesnew As New List(Of EfcObject)()
        Dim Filesnewa As New List(Of EfcObject)()
        Dim FilePath As String = ""
        Dim Files As New List(Of EfcObject)()
        sMessage = String.Empty

        If Not LogIntoEFC() Then
            sMessage = "EFileCabinet authentication failed!"
            Return Nothing
        End If

        cabinets = EfcOnlineApi.GetCabinets()

        For Each cab As EfcObject In cabinets
            If cab.ParentEfcObjectId = -1 Then
                cab.ParentEfcObjectId = 0
            End If
        Next

        fulltree.AddRange(cabinets)

        If cabinets.Count = 0 Then
            sMessage = "No cabinets found"
        End If

        Return fulltree

        'If cabinets.Count > 0 Then

        '    For index As Integer = 0 To 0 'cabinets.Count - 1

        '        'cabinets.Count - 1
        '        drawers = New List(Of EfcObject)()
        '        drawers = EfcOnlineApi.GetChildren(cabinets(index).EfcObjectId)
        '        fulltree.AddRange(drawers)

        '        If drawers.Count > 0 Then

        '            For indexd As Integer = 0 To drawers.Count - 1

        '                foldersAndFiles = New List(Of EfcObject)()
        '                foldersAndFiles = EfcOnlineApi.GetChildren(drawers(indexd).EfcObjectId)
        '                fulltree.AddRange(foldersAndFiles)

        '                If foldersAndFiles.Count > 0 Then
        '                    For indexf As Integer = 0 To foldersAndFiles.Count - 1
        '                        Files = New List(Of EfcObject)()
        '                        Files = EfcOnlineApi.GetChildren(foldersAndFiles(indexf).EfcObjectId)
        '                        fulltree.AddRange(Files)
        '                    Next
        '                End If

        '            Next
        '        End If

        '    Next

        'End If

        'Return fulltree

    End Function

    Public Function GetFiles(iEfcObjectId As Integer) As List(Of EfcObject)
        Return EfcOnlineApi.GetChildren(iEfcObjectId)
    End Function

    Public Function DownloadFile(iEfcObjectId As Integer) As MemoryStream

        If Not IsEFCAuthenticated() Then
            LogIntoEFC()
        End If

        Dim storeStream As New MemoryStream()
        storeStream = EfcOnlineApi.DownloadFileStream(iEfcObjectId, 1024)
        Return storeStream

    End Function



    Public Function IsEFCAuthenticated() As Boolean
        If Not String.IsNullOrEmpty(EfcOnlineApi.AuthToken) AndAlso EfcOnlineApi.AuthToken.Length <= 37 Then
            Return True
        Else
            Return False
        End If
    End Function

End Class
