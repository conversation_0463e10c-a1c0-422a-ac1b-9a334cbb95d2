﻿using System;
using System.Data;


//Owner: MI

using System.Web;
using System.Web.UI.WebControls;

namespace Selltis.BusinessLogic
{
	public class clTable
	{
		//MI 5/24/07 Added bReturnStringType public property, when set to true GetVal returns a string
		//MI 2/7/07
		//MI 10/4/06 bug fix in Insert.
		//PURPOSE:
		//       Manage data in DataTable and a UI control bound to it. The purpose is to facilitate working
		//       with DataTables and make it similar to NGP Table functions. 
		//       Currently only the ListBox control can be bound to the datatable via BindListBox() method..
		//       Row index numbers are 1-based here.

		//Unused global objects are commented out
		//Dim goP As clProject
		//Dim goTR As clTransform
		//Dim goMeta As clMetaData
		//Dim goData As clData
		private clError goErr;
		//Dim goLog As clLog
		//Dim goDef As clDefaults

		//Private sViewDefaults As String
		public DataTable dt;
		//Public ds As DataSet
		public ListBox Par_oListBox;
		public string sValueColName; //bound to listbox 'value': primary key column for the datatable
		public string sTextColName; //bound to listbox as 'text'
		public long lSelectedRow = 0; //Index (1-based) that corresponds to the DataTable's 'selected' row (0-based index). Corresponds to listbox.SelectedIndex + 1 (MI 11/24/09 Is + 1 true?).
		public bool bReturnStringType = false; //When true, GetVal() returns .ToString value regardless of the data type in a column.

		private DataColumn column;
		private DataRow row;
		private string sEmptyValue = "~!@#$%^&*()_+_)(*&^%$#@!~";


		public void Initialize()
		{
			string sProc = "clTable::Initialize";

			//goP = HttpContext.Current.Session("goP")
			//goTR = HttpContext.Current.Session("goTr")
			//goMeta = HttpContext.Current.Session("goMeta")
			//goData = HttpContext.Current.Session("goData")
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			//goLog = HttpContext.Current.Session("goLog")
			//goDef = HttpContext.Current.Session("goDef")

		}

		public clTable()
		{
			//Overloaded to be able to declare a global object within a page before 
			//go objects become available.

		}

		public clTable(ref DataTable par_dtDataTable, string par_sValueColName = "Value", string par_sTextColName = "Text", bool par_bReturnStringType = false)
		{
			//PURPOSE:
			//       Initializes the clTable object and creates the following columns:
			//           'Index' (long)
			//           par_sValueColName ('Value' by default) (object)
			//           par_sTextColName ('Text' by default) (string)
			//       Index column is used to keep the IndexOf value in the table so it can
			//       be used for sorting in datarow.Select(). If you want to bind the datatable to
			//       a listbox, run BindListbox after running clTable methods. This will populate
			//       the listbox and select the appropriate item in it.
			//       If you need to, you can set the datatable to be part of a dataset like this:
			//           Dim dsDataSet As DataSet
			//           dsDataSet = New DataSet()
			//           dsDataSet.Tables.Add(dtThisDataTable)
			//PARAMETERS:
			//       par_dtDataTable: DataTable object. Instantiate this object like this:
			//               Dim dtDataTable As New DataTable("ListOfFields")
			//               'Create a new clTable object
			//               Dim t as New clTable(dtDataTable)
			//           Note that you can declare a global clTable object without providing
			//           any parameters.
			//       par_sValueColName: Name of the 'Value' column. Default is 'Value'. 
			//           This is the 'key' value by which each row is identified in the listbox,
			//           if it is bound. The value must be unique.
			//           This becomes the PrimaryKeyColumn for the datatable.
			//           If you provide this parameter, the column 'Value' is created as the
			//           first column. Don't attempt to create it with AddColumn().
			//       par_sTextColName: Name of the 'text' column. Default is 'Text'.
			//           This column is bound to the listbox's .Text property. This is what the
			//           user will see in the 
			//           listbox. If you provide this parameter, the column 'Text' is created 
			//           automatically as the second column (or first column if par_sValueColName
			//           is blank. Don't attempt to create it with AddColumn().
			//       par_bReturnStringType: False by default. If true, GetVal returns all values
			//           as string. DBNulls get returned as "".

			string sProc = "clTable::New";
			sValueColName = par_sValueColName;
			sTextColName = par_sTextColName;

			Initialize();

			if (par_dtDataTable == null)
			{
				goErr.SetError(30012, sProc, "", "par_dtDataTable");
				//30012: Object '[1]' is not assigned.
			}

			bReturnStringType = par_bReturnStringType;

			dt = par_dtDataTable;

			//Enforce
			//Value column
			if (par_sValueColName == "")
			{
				goErr.SetError(35000, sProc, "par_sValueColName must not be blank.");
			}
			//Text column is required
			if (par_sTextColName == "")
			{
				goErr.SetError(35000, sProc, "par_sTextColName must not be blank.");
			}

			//Create default columns
			this.AddColumn("Index", System.Type.GetType("System.Int64"), true);
			this.AddColumn(par_sValueColName, null, true);
			if (par_sTextColName != "")
			{
				this.AddColumn(par_sTextColName);
			}

			//Set primary key column on the index
			DataColumn[] PrimaryKeyColumns = new DataColumn[1];
			PrimaryKeyColumns[0] = dt.Columns[sValueColName];
			//PrimaryKeyColumns(0) = dt.Columns("Index")
			//Set the column to primary key makes the column unique
			dt.PrimaryKey = PrimaryKeyColumns;

		}

		public bool Add(string par_sTableName = "", System.Data.DataRow par_drDataRow = null)
		{
			//PURPOSE:
			//       Appends a row to the end of the table. Does not change the selected row.
			//       In NGP was used to create a table or add a whole row
			//       of tab-delimited values.
			//PARAMETERS:
			//       par_sTableName: ignored.
			//       par_drDataRow: Optional: DataRow class that has the values of a whole datatable
			//           row. This is useful when copying a row. The structure must be
			//           the same. If this parameter is omitted, an empty row is added
			//           for you to fill it with tTable.SetVal().
			//           IMPORTANT: If one of the columns has the Unique property set,
			//           only one blank row can be created. Adding a second blank row will
			//           raise an error.
			//RETURNS:
			//       Boolean: True if a row added, False otherwise.

			string sProc = "clTable::Add";

			bool bResult = false;
			long lEmptyRow = 0;

			//Try
			if (par_drDataRow == null)
			{
					lEmptyRow = Seek(this.sValueColName, sEmptyValue);
					if (lEmptyRow > 0)
					{
						//Delete empty row
						Delete("", lEmptyRow);
					}
					DataRow drNewRow = dt.NewRow();
					drNewRow["Index"] = dt.Rows.Count;
					//Set Value to blank - NULL value is not allowed
					drNewRow[this.sValueColName] = sEmptyValue;
					dt.Rows.Add(drNewRow);
				}
				else
				{
					dt.Rows.Add(par_drDataRow);
				}

				//Leave the selected row unchanged
				//Me.lSelectedRow = dt.Rows.Count()

				RegenerateIndexCol();

				bResult = true;
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return bResult;

		}

		public bool AddColumn(string par_sName, System.Type par_tDataType = null, bool par_bUnique = false)
		{
			//MI 7/10/07
			//PURPOSE:
			//       Adds a column to the .Columns collection to the right of other columns
			//       Once added, columns can't be removed. Keep in mind that New()
			//       adds 'Index', 'Value' and, optionally, 'Text' columns.
			//PARAMETERS:
			//       par_sName: Name of the column.
			//       par_tDataType: pass a value such as System.Type.GetType("System.Int32") value.
			//           "System.String" is the default. The GetType string is case sensitive.
			//       par_bUnique: Enforce uniqueness of values in this column. If True,
			//           NULL values (Nothing) can't be set in this column.

			string sProc = "clTable::AddColumn";

			bool bResult = true;

			//Try
			//Create columns
			column = new DataColumn();
				if (par_tDataType == null)
				{
					column.DataType = System.Type.GetType("System.String");
				}
				else
				{
					column.DataType = par_tDataType;
				}
				column.Unique = par_bUnique;
				column.ColumnName = par_sName;
				dt.Columns.Add(column);
				//Catch ex As Exception
				//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//        goErr.SetError(ex, 45105, sProc)
				//    End If
				//End Try

				return bResult;

		}

		public bool BindListbox(ref dynamic par_oListBox) //par_oListBox as ListBox
		{
			//MI 12/1/09 Fixed error 'Selected row is greater than the combobox count' when combo is empty.
			//MI 11/24/09 added dealing with dropdownlist: now selecting element 1 if selection not defined.
			//PURPOSE:
			//       Bind the datatable to a listbox control and select an item in it.
			//PARAMETERS:
			//       par_oListBox: listbox control to bind. For dropdownlist control, use BindCombobox.
			//       par_bCombo: DEPRECATED Now dropdownlist vs listbox is determined from the
			//           object passed in par_oListBox. 
			//           Original NGP notes: Set to true if the control is a dropdownlist (combo).
			//           When set to true, if the selection is undefined t.Selected() is -1,
			//           the selected item will be set to the first element (index 0) 
			//           because dropdownlist doesn't allow not having an element selected.
			//RETURNS:
			//       True.

			string sProc = "clTable::BindListbox";
			string sType = null;
			bool bCombo = false;

			//Try
			sType = par_oListBox.GetType().ToString();
				if (sType.ToUpper() == "SYSTEM.WEB.UI.WEBCONTROLS.DROPDOWNLIST")
				{
					bCombo = true;
				}


				//dt.AcceptChanges()
				//DEBUG
				string sSelectedvalue = par_oListBox.SelectedValue;
				//END DEBUG

				par_oListBox.items.Clear();
				par_oListBox.DataSource = dt;
				par_oListBox.DataValueField = sValueColName;
				par_oListBox.DataTextField = sTextColName;
				par_oListBox.SelectedIndex = -1; //MI 11/24/09 commenting. -1 doesn't work in combos and is redundant in lists after CLear()
				par_oListBox.DataBind();
				if (bCombo)
				{
					//dropdownlist
					//Select item in the listbox
					if (lSelectedRow > par_oListBox.Items.Count)
					{
						goErr.SetError(35000, sProc, "Selected row is greater than the combobox count.");
						//MessTranslate()
					}
					if (par_oListBox.Items.Count > 0)
					{
						//Select first item if nothing is to be selected
						if (lSelectedRow < 1)
						{
							lSelectedRow = 1;
						}
						//Select an item
						par_oListBox.SelectedIndex = lSelectedRow - 1;
					}
				}
				else
				{
					//listbox
					//Select item in the listbox
					if (lSelectedRow < 1)
					{
						lSelectedRow = -1;
					}
					if (lSelectedRow > par_oListBox.Items.Count)
					{
						goErr.SetError(35000, sProc, "Selected row is greater than the ListBox count.");
						//MessTranslate()
					}
					if (par_oListBox.Items.Count > 0)
					{
						if (lSelectedRow == -1)
						{
							par_oListBox.SelectedIndex = lSelectedRow;
						}
						else
						{
							//A row is selected
							par_oListBox.SelectedIndex = lSelectedRow - 1;
						}
					}
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return true;

		}
		public long Count(string par_sTableName = "", int par_iType = 1)
		{
			//PURPOSE:
			//       Count the no of rows or columns in the table.
			//PARAMETERS:
			//       par_sTableName: can be left blank - for backward compatibility.
			//       par_iType: 1: number of rows; 2: number of columns.
			//RETURNS:
			//       Long: no of rows or columns in the table.

			string sProc = "clTable::Count";

			// Try
			if (par_iType == 1)
			{
					return dt.Rows.Count;
				}
				else
				{
					return dt.Columns.Count;
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

		}
		public bool Delete(string par_sTable, long par_lRow = -1)
		{
			//PURPOSE:
			//       Deletes a row from the table based on its index (1-based).
			//       Adjusts selected row as needed. If row is not provided,
			//       the selected row is deleted. 
			//PARAMETERS:
			//       par_sTable: for backward compatibility - can be "".
			//       par_lRow: Optional: Index of the row to delete (1-based).
			//           If < 1, the currently selected row is deleted. If the
			//           selected row is invalid, no row is deleted and 
			//           result returned is False.
			//RETURNS:
			//       Boolean: true if successful, False if the row is invalid
			//       (> Count or < 1 - error is not raised).

			string sProc = "clTable::Delete";
			bool bResult = true;

			//Try
			if (par_lRow < 1)
			{
					par_lRow = this.lSelectedRow;
				}
				if (par_lRow > Count() || par_lRow < 1)
				{
					//Invalid row
					bResult = false;
				}
				else
				{
					if (dt.Rows.Count < 1)
					{
						//Nothing to delete
						bResult = false;
					}
					else
					{
						//Delete
						dt.Rows.RemoveAt((int)(par_lRow - 1));
						dt.AcceptChanges();
						//Adjust selected row
						if (lSelectedRow > par_lRow)
						{
							lSelectedRow = lSelectedRow - 1;
						}
						if (lSelectedRow > dt.Rows.Count)
						{
							lSelectedRow = dt.Rows.Count;
						}
						if (lSelectedRow < 1)
						{
							lSelectedRow = -1;
						}
						this.RegenerateIndexCol();
					}
				}
				//Catch ex As Exception
				//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//        goErr.SetError(ex, 45105, sProc)
				//    End If
				//End Try

				return bResult;

		}
		public bool DeleteAll(string par_sTableName = "")
		{
			//PURPOSE:
			//       Delete all rows in the datatable.
			//PARAMETERS:
			//       par_sTableName: ignored.
			//RETURNS:
			//       True or raises an error.

			string sProc = "clTable::DeleteAll";

			//Try
			dt.Rows.Clear();
				dt.AcceptChanges();
				lSelectedRow = -1;
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return true;
		}
		public object GetSelectedValue()
		{
			//PURPOSE:
			//       Returns the 'Value' column value for the currently selected row.
			//RETURNS:
			//       Object: value in datatype of the column.

			string sProc = "clTable::GetSelectedValue";
			object oResult = null;
			string sResult = "";

			//Try
			if (lSelectedRow > dt.Rows.Count)
			{
					goErr.SetError(35000, sProc, "lSelectedRow exceeds row count of the DataTable.");
					if (bReturnStringType)
					{
						sResult = "";
						return sResult;
					}
					else
					{
						return null;
					}
				}

				if (lSelectedRow < 1)
				{
					if (bReturnStringType)
					{
						sResult = "";
						return sResult;
					}
					else
					{
						return null;
					}
					//Select Case dt.Columns(sValueColName).DataType.ToString
					//    Case "System.String", "System.Char", "System.Byte"
					//        Return ""
					//    Case "System.DateTime"
					//        Return ""
					//    Case Else   'Numeric
					//        Return 0
					//End Select
				}
				oResult = dt.Rows[(int)(lSelectedRow - 1)][this.sValueColName];
				if (bReturnStringType)
				{
					if (oResult == System.DBNull.Value)
					{
						sResult = "";
						return sResult;
					}
					else
					{
						if (oResult == null)
						{
							sResult = "";
							return sResult;
						}
						else
						{
							return oResult.ToString();
						}
					}
				}

				//Catch ex As Exception
				//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//        goErr.SetError(ex, 45105, sProc)
				//    End If
				//End Try

				return oResult;

		}

		public object GetVal(string par_sColumnName, long par_lRow = -1)
		{
			//PURPOSE:
			//       Retrieves a value from a particular cell (row/column intersection). In NGP,
			//       the equivalent syntax is <value> = COL_COLUMNNAME_TABLENAME[index].
			//PARAMETERS:
			//       par_sColumnName: Name of the column
			//       par_lRow: index of the row from which to retrieve the value (1-based index).
			//           If out of range, an error is raised. If < 1 the currently selected row
			//           is used. If no row is selected, Nothing will be returned.
			//RETURNS:
			//       Value in data type of the column or, if bReturnStringType is True, the value
			//       as string. If the value is DBNull or Nothing, "" is returned.
			//       If lRow is out of range, an error is raised.
			//       WARNING: if you don't set par_bReturnStringType parameter in New. to True,
			//       keep in mind that the value CAN be system.DBNull.value!
			//       Use t.GetVal().ToString to turn it into "".

			string sProc = "clTable::GetVal";

			object oResult = null;
			string sResult = "";
			long lRow = par_lRow;

			//Try
			if (lRow < 1)
			{
					//Use selected row
					lRow = this.lSelectedRow;
					if (lRow < 1)
					{
						if (bReturnStringType)
						{
							sResult = "";
							return sResult;
						}
						else
						{
							return System.DBNull.Value;
						}
					}
				}
				if (lRow > dt.Rows.Count)
				{
					goErr.SetError(35000, sProc, "lRow exceeds row count of the DataTable.");
					if (bReturnStringType)
					{
						sResult = "";
						return sResult;
					}
					else
					{
						return System.DBNull.Value;
					}
				}
				if (dt.Rows.Count < 1)
				{
					if (bReturnStringType)
					{
						sResult = "";
						return sResult;
					}
					else
					{
						return System.DBNull.Value;
					}
				}
				else
				{
					oResult = dt.Rows[(int)(lRow - 1)][par_sColumnName];
					if (bReturnStringType)
					{
						if (oResult == System.DBNull.Value)
						{
							sResult = "";
							return sResult;
						}
						else
						{
							if (oResult == null)
							{
								sResult = "";
								return sResult;
							}
							else
							{
								sResult = oResult.ToString();
								return sResult;
							}
						}
					}
				}

				//Catch ex As Exception
				//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//        goErr.SetError(ex, 45105, sProc)
				//    End If
				//End Try

				return oResult;

		}
		public bool Insert(string par_sTableName = "", System.Data.DataRow par_drDataRow = null, long par_lRowNo = 0)
		{
			//PURPOSE:
			//       Inserts a row at an index position (1-based). To append a row to 
			//       the bottom of the datatable, use Add(). Does not make the inserted
			//       row the selected row. 
			//PARAMETERS:
			//       par_sTableName: ignored.
			//       par_drDataRow: DataRow class that contains the values of a whole
			//           data row from a DataTable. If omitted, a blank row is inserted
			//           unless par_lRowno is 0 or greater than Count, in which case
			//           this works like Add().
			//       par_lRowNo: The number of the row where to insert the new row.
			//           Rows below get moved one position down. If this parameter is
			//           not specified, the row is inserted before the currently
			//           selected row (lSelectedRow). If lSelectedRow < 1 or > Count(),
			//           the method calls Add().
			//EXAMPLE:
			//       In NGP:
			//           'Insert a copy of the selected row
			//           TableInsert("TBL_FIELDS",TBL_FIELDS[TBL_FIELDS],TBL_FIELDS)
			//           'Insert a blank row in position 1
			//           TableInsert("TBL_AVAIL","",1)

			string sProc = "clTable::Insert";
			DataRow drNewRow = null;
			long lRowNo = par_lRowNo;
			long lEmptyRow = 0;
			long lDuplicateValueRow = 0;

			//Try
			if (lRowNo < 0 || lRowNo > dt.Rows.Count)
			{
					//Unsupported row no
					return Add(par_sTableName, par_drDataRow);
				}
				else if (lRowNo == 0)
				{
					lRowNo = this.lSelectedRow;
					//Selected row is 0 or out of whack
					if (this.lSelectedRow < 1 || this.lSelectedRow > dt.Rows.Count)
					{
						return Add(par_sTableName, par_drDataRow);
					}
				}

				if (par_drDataRow == null)
				{
					//If an empty row exists due to an unsuccessful Insert or Add, remove it
					lEmptyRow = Seek(this.sValueColName, sEmptyValue);
					if (lEmptyRow > 0)
					{
						//Delete empty row
						Delete("", lEmptyRow);
					}
					//Create a new row
					drNewRow = dt.NewRow();
					drNewRow["Index"] = dt.Rows.Count;
					drNewRow[this.sValueColName] = sEmptyValue; //Null not allowed System.DBNull.Value
					dt.Rows.InsertAt(drNewRow, (int)(lRowNo - 1));
				}
				else
				{
					//Does a duplicate 'Value' exist?
					lDuplicateValueRow = Seek(sValueColName, par_drDataRow[dt.Columns[this.sValueColName].Ordinal]);
					if (lDuplicateValueRow > 0)
					{
						goErr.SetWarning(35000, sProc, "Duplicate value exists in row '" + lDuplicateValueRow.ToString() + "': '" + par_drDataRow[dt.Columns[this.sValueColName].Ordinal].ToString() + "'.");
						//MessTranslate
						return false;
					}
					dt.Rows.InsertAt(par_drDataRow, (int)(lRowNo - 1));
				}

				RegenerateIndexCol();

				//Let the selected row remain unchanged
				//lSelectedRow = lRowNo

				//Catch ex As Exception
				//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//        goErr.SetError(ex, 45105, sProc)
				//    End If
				//End Try

				return true;

		}
		public bool MoveDown()
		{
			//MI 10/4/06 Ported from NGP.
			//OWNER: MI		3/13/2002

			//PURPOSE:
			//		Move the selected row in a cltable object one position down
			//       and select that row.
			//PARAMETERS:
			//RETURNS:
			//		Boolean - indicates whether the selected row can be moved down
			//			This can be useful if you need to gray out or disable a button
			//			with which the user is moving through the table
			//			True: row can be moved down at least one more position
			//			False: row is on the lowest position, can't be moved farther
			//EXAMPLE:
			//		gw.tFields.MoveDown()

			string sProc = "clTable::MoveDown";

			string sTable = "";
			int iRow = 0;
			int iRowCount = 0;
			DataRow dr = null;
			string sColName = null;
			object oValue = null;
			object oValueColValue = "";
			int i = 0;
			bool bOrigReturnStringType = bReturnStringType;

			//Don't alter data type returned by GetVal to string
			bReturnStringType = false;

			iRowCount = (int)Count();
			iRow = (int)Selected();
			//Prevent out of range index exception in dt below
			if (iRow < 0)
			{
				iRow = 1;
			}
			if (iRow < iRowCount)
			{
				//Get the current row's DataRow object
				dr = this.dt.Rows[iRow - 1];
				if (dr == null)
				{
					//Return the GetVal data type to original setting
					bReturnStringType = bOrigReturnStringType;
					goErr.SetError(35000, sProc, "Datatable row is null (0-based index):  '" + (iRow - 1).ToString() + "'.");
				}
				//We can't just copy the row (say using dt.ImportRow) because the 
				//value and index columns must not have duplicates.
				//That's why we do this the long way: create a blank row, populate it,
				//delete the existing row, then modify the new row.
				if (!Insert(sTable, null, iRow + 2))
				{
					//Return the GetVal data type to original setting
					bReturnStringType = bOrigReturnStringType;
					goErr.SetError(35000, sProc, "Inserting a blank row failed at row (1-based index) '" + (iRow - 1).ToString() + "'.");
				}
				//Populate values in the new row
				for (i = 0; i < dt.Columns.Count; i++)
				{
					sColName = dt.Columns[i].ColumnName;
					if (sColName == "Index")
					{
						//Count should be unique if RegenerateIndexCol is run whenever modifying rows
						oValue = dt.Rows.Count;
					}
					else if (sColName == this.sValueColName)
					{
						oValueColValue = GetVal(sColName, iRow);
						oValue = this.sEmptyValue;
					}
					else
					{
						oValue = GetVal(sColName, iRow);
					}
					SetVal(sColName, iRow + 2, oValue);
				}

				SelectPlus(sTable, iRow + 2);
				Delete(sTable, iRow);
				SetVal(this.sValueColName, iRow + 1, oValueColValue);
			}

			this.RegenerateIndexCol();

			//Return the GetVal data type to original setting
			bReturnStringType = bOrigReturnStringType;

			if (Selected(sTable) > iRowCount - 1)
			{
				return false;
			}
			else
			{
				return true;
			}

		}
		public bool MoveUp()
		{
			//MI 10/4/06 Ported from NGP.
			//OWNER: MI		3/13/2002

			//PURPOSE:
			//		Move the selected row in a table one position upward
			//       and select that row.
			//PARAMETERS:
			//		par_sWindowName: <window name>
			//		par_sTableName: <name of the table>
			//RETURNS:
			//		Boolean - indicates whether the selected row can be moved up
			//			This can be useful if you need to gray out or disable a button
			//			with which the user is moving through the table
			//			True: row can be moved up at least one more position
			//			False: row is on position 1, can't be moved farther
			//EXAMPLE:
			//		tTable.MoveUp()

			string sProc = "clTable::MoveUp";

			string sTable = "";
			int iRow = 0;
			DataRow dr = null;
			int i = 0;
			string sColName = null;
			object oValue = null;
			object oValueColValue = "";
			bool bOrigReturnStringType = bReturnStringType;

			//Don't alter data type returned by GetVal to string
			bReturnStringType = false;

			iRow = (int)Selected();
			if (iRow > 1)
			{
				//Get the current row's DataRow object
				dr = this.dt.Rows[iRow - 1];
				if (dr == null)
				{
					//Return the GetVal data type to original setting
					bReturnStringType = bOrigReturnStringType;
					goErr.SetError(35000, sProc, "Datatable row is null (0-based index): '" + (iRow - 1).ToString() + "'.");
				}
				//We can't just copy the row (say using dt.ImportRow) because the 
				//value and index columns must not have duplicates.
				//That's why we do this the long way: create a blank row, populate it,
				//delete the existing row, then modify the new row.
				//Insert a blank row
				if (!Insert(sTable, null, iRow - 1))
				{
					//Return the GetVal data type to original setting
					bReturnStringType = bOrigReturnStringType;
					goErr.SetError(35000, sProc, "Inserting a blank row failed at row (1-based index) '" + (iRow - 1).ToString() + "'.");
				}
				//Populate values in the new row
				for (i = 0; i < dt.Columns.Count; i++)
				{
					sColName = dt.Columns[i].ColumnName;
					if (sColName == "Index")
					{
						//Count should be unique if RegenerateIndexCol is run whenever modifying rows
						oValue = dt.Rows.Count;
					}
					else if (sColName == this.sValueColName)
					{
						oValueColValue = GetVal(sColName, iRow + 1);
						oValue = this.sEmptyValue;
					}
					else
					{
						oValue = GetVal(sColName, iRow + 1);
					}
					SetVal(sColName, iRow - 1, oValue);
				}
				SelectPlus(sTable, iRow - 1);
				Delete(sTable, iRow + 1);
				SetVal(this.sValueColName, iRow - 1, oValueColValue);
			}

			this.RegenerateIndexCol();

			//Return the GetVal data type to original setting
			bReturnStringType = bOrigReturnStringType;

			if (Selected(sTable) < 2)
			{
				return false;
			}
			else
			{
				return true;
			}

		}

		public bool MoveToTable(ref clTable par_oToTable, ref ListBox par_oFromListBox, ref ListBox par_oToListBox, long par_lTable1Row, long par_lTable2Row, string par_sMode)
		{
			return MoveToTable(ref par_oToTable, ref par_oFromListBox, ref par_oToListBox, par_lTable1Row, par_lTable2Row, par_sMode, false);
		}

		public bool MoveToTable(ref clTable par_oToTable, ref ListBox par_oFromListBox, ref ListBox par_oToListBox, long par_lTable1Row, long par_lTable2Row)
		{
			return MoveToTable(ref par_oToTable, ref par_oFromListBox, ref par_oToListBox, par_lTable1Row, par_lTable2Row, "Insert", false);
		}

		public bool MoveToTable(ref clTable par_oToTable, ref ListBox par_oFromListBox, ref ListBox par_oToListBox, long par_lTable1Row)
		{
			return MoveToTable(ref par_oToTable, ref par_oFromListBox, ref par_oToListBox, par_lTable1Row, 0, "Insert", false);
		}

		public bool MoveToTable(ref clTable par_oToTable, ref ListBox par_oFromListBox, ref ListBox par_oToListBox)
		{
			return MoveToTable(ref par_oToTable, ref par_oFromListBox, ref par_oToListBox, 0, 0, "Insert", false);
		}

		public bool MoveToTable(ref clTable par_oToTable, ref ListBox par_oFromListBox)
		{
			ListBox tempVar = null;
			return MoveToTable(ref par_oToTable, ref par_oFromListBox, ref tempVar, 0, 0, "Insert", false);
		}

		public bool MoveToTable(ref clTable par_oToTable)
		{
			ListBox tempVar = null;
			ListBox tempVar2 = null;
			return MoveToTable(ref par_oToTable, ref tempVar, ref tempVar2, 0, 0, "Insert", false);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Function MoveToTable(ByRef par_oToTable As clTable, Optional ByRef par_oFromListBox As ListBox = Nothing, Optional ByRef par_oToListBox As ListBox = Nothing, Optional ByVal par_lTable1Row As Long = 0, Optional ByVal par_lTable2Row As Long = 0, Optional ByVal par_sMode As String = "Insert", Optional ByVal par_bCopyOnly As Boolean = False) As Boolean
		public bool MoveToTable(ref clTable par_oToTable, ref ListBox par_oFromListBox, ref ListBox par_oToListBox, long par_lTable1Row, long par_lTable2Row, string par_sMode, bool par_bCopyOnly)
		{
			//MI 10/24/06	Added checking for duplicate in the value column
			//MI 10/5/06 Ported from MoveFromTableToTable in NGP.

			//AUTHOR: MI 3/2/2003
			//PURPOSE:
			//		Move or copy a row of data from one table to another and make that
			//       the selected row in the target table. The columns of the two tables
			//       must match. If par_sMode is "Insert",
			//       the new row is inserted above the selected line in table 2
			//		If "Append", the new row is added on the bottom of table 2. If par_bCopyOnly
			//       is True, the row is not removed from the 'from' table.
			//       If listbox parameters are passed, from and to listboxes are bound to the
			//       tables.
			//		Both tables MUST have exactly the same columns including masks and widths!
			//       Moving/copying will fail and False will be returned if me.sValueColName column
			//       in the target table already contains the value being moved/copied.
			//PARAMETERS:
			//		par_oToTable: clTable into which to insert the row.
			//       par_oFromListBox: Optional: listbox bound to the 'from' table.
			//		par_oToListBox: Optional: Listbox bound to the target table.
			//		par_lTable1Row: Optional: row of table 1. If < 1, move or copy the 
			//           currently selected row.
			//		par_lTable2Row: Optional: row of table 2. If < 1, insert above currently
			//           selected row. Ignored if par_sMode is not "Insert".
			//		par_sMode: "Insert" or "Append".
			//		par_bCopyOnly: If true, don't remove the row from table 1.
			//RETURNS:
			//		Boolean: True if successful or False if the Value in the target table
			//           would be duplicated. In that case the row is not moved/copied.
			//HOW IT WORKS:
			//		Takes row from table 1 and inserts it above the selected row of table 2,
			//		then deletes the row from table 1. Data binds the listboxes.
			//EXAMPLES (NGP):
			//		oTable:MoveFromTableToTable(FenEnExecution()+".TBL_1",FenEnExecution()+".TBL_2")
			//			'inserts selected row from table 1 above selected row of table 2
			//		oTable:MoveFromTableToTable(FenEnExecution()+".TBL_1",FenEnExecution()+".TBL_2",3,2)
			//			'Inserts row 3 from table 1 above row 2 of table 2


			string sProc = "clTable::MoveToTable";
			bool bResult = true;
			string sColName = "";
			object oValue = null;
			object oValueColValue = null;
			int i = 0;
			long lDuplicateValueRow = 0;
			string sToColumnName = "";
			bool bOrigReturnStringType = bReturnStringType;

			//Try
			int iRow1 = 0;
				int iRow2 = 0;

				if (par_oToTable == null)
				{
					goErr.SetError(30012, sProc, "", "par_oToTable");
					//30012: Object '[1]' is not assigned.
				}

				if (par_lTable1Row < 1)
				{
					iRow1 = (int)Selected();
				}
				else
				{
					iRow1 = (int)par_lTable1Row;
				}

				if (Count() < 1)
				{
					//No rows in the 'from' table
					return false;
				}

				//Set getVal return type to non-string so the values don't get changed
				bReturnStringType = false;

				if (par_sMode == "Insert")
				{
					if (par_lTable2Row < 1)
					{
						iRow2 = (int)par_oToTable.Selected();
					}
					else
					{
						iRow2 = (int)par_lTable2Row;
					}
					//Does a duplicate 'Value' exist in the target table?
					lDuplicateValueRow = par_oToTable.Seek(par_oToTable.sValueColName, GetVal(this.sValueColName, iRow1));
					if (lDuplicateValueRow > 0)
					{
						//Return the GetVal data type to original setting
						bReturnStringType = bOrigReturnStringType;
						goErr.SetWarning(35000, sProc, "Duplicate value exists in the target table in row '" + lDuplicateValueRow.ToString() + "': '" + GetVal(this.sValueColName, iRow1).ToString() + "'.");
						//MessTranslate
						//Exit without moving the row
						return false;
					}
					//Insert a row
					par_oToTable.Insert("", null, iRow2);
					//If the 'to' table was blank, now we are dealing with row 1
					if (iRow2 < 1)
					{
						iRow2 = 1;
					}
					//Populate cell values column by column
					for (i = 0; i < dt.Columns.Count; i++)
					{
						sColName = dt.Columns[i].ColumnName;
						sToColumnName = par_oToTable.dt.Columns[i].ColumnName;
						//Not comparing column names because we are allowing them to be different.
						//However, the purpose, count, and type of columns must match.
						if (sColName == "Index")
						{
							//Count should be unique if RegenerateIndexCol is run whenever modifying rows
							oValue = par_oToTable.Count();
						}
						else if (sColName == this.sValueColName)
						{
							oValueColValue = GetVal(sColName, iRow1);
							//oValue = Me.sEmptyValue
							oValue = oValueColValue;
						}
						else
						{
							oValue = GetVal(sColName, iRow1);
						}
						par_oToTable.SetVal(sToColumnName, iRow2, oValue);
					}
					//Select the inserted row
					par_oToTable.SelectPlus("", iRow2);
					//'Set the original value in the 'Value' column
					//par_oToTable.SetVal(par_oToTable.sValueColName, iRow2, oValueColValue)
				}
				else
				{
					//Append
					//Does a duplicate 'Value' exist in the target table?
					lDuplicateValueRow = par_oToTable.Seek(par_oToTable.sValueColName, GetVal(this.sValueColName, iRow1));
					if (lDuplicateValueRow > 0)
					{
						//Return the GetVal data type to original setting
						bReturnStringType = bOrigReturnStringType;
						goErr.SetWarning(35000, sProc, "Duplicate value exists in the target table in row '" + lDuplicateValueRow.ToString() + "': '" + GetVal(this.sValueColName, iRow1).ToString() + "'.");
						//MessTranslate
						//Exit without moving the row
						return false;
					}
					par_oToTable.Add();
					iRow2 = (int)par_oToTable.Count();
					//Populate cell values column by column
					for (i = 0; i < dt.Columns.Count; i++)
					{
						sColName = dt.Columns[i].ColumnName;
						//Not comparing column names because we are allowing them to be different.
						//However, the purpose, count, and type of columns must match.
						if (sColName == "Index")
						{
							//Count should be unique if RegenerateIndexCol is run whenever modifying rows
							oValue = iRow2;
						}
						else if (sColName == this.sValueColName)
						{
							oValueColValue = GetVal(sColName, iRow1);
							//oValue = Me.sEmptyValue
							oValue = oValueColValue;
						}
						else
						{
							oValue = GetVal(sColName, iRow1);
						}
						par_oToTable.SetVal(par_oToTable.dt.Columns[i].ColumnName, iRow2, oValue);
					}
					//Select the added row
					par_oToTable.SelectPlus("", iRow2);
					//'Set the original value in the 'Value' column
					//par_oToTable.SetVal(par_oToTable.sValueColName, iRow2, oValueColValue)
				}

				if (!par_bCopyOnly)
				{
					Delete("", iRow1);
					RegenerateIndexCol();
				}

				par_oToTable.RegenerateIndexCol();

				if (par_oFromListBox != null)
				{
					object temp_par_oFromListBox = par_oFromListBox;
					this.BindListbox(ref temp_par_oFromListBox);
						par_oFromListBox = (ListBox)temp_par_oFromListBox;
				}
				if (par_oToListBox != null)
				{
					object temp_par_oToListBox = par_oToListBox;
					par_oToTable.BindListbox(ref temp_par_oToListBox);
						par_oToListBox = (ListBox)temp_par_oToListBox;
				}

				//Return the GetVal data type to original setting
				bReturnStringType = bOrigReturnStringType;

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return bResult;

		}
		public long Position(string par_sTableName = "")
		{
			//MI 12/16/11 Removed Dim lResult As Long = 0.
			//PURPOSE:
			//       Calls Selected, which returns the selected row in the datatable (1-based index).
			//       Else, returns 0.
			//       In NGP this was returning the index of the top-most displayed row.

			string sProc = "clTable::Position";

			return Selected(par_sTableName);

		}
		public void RegenerateIndexCol()
		{
			//PURPOSE:
			//       Reestablish values in the index column in the order
			//       of rows. Values in the index column should reflect the
			//       the actual row index values of the underlying
			//       datatable (0-based index).

			string sProc = "clTable::RegenerateIndexCol";

			//Try
			long l = 0;

				//First pass puts very large nos to avoid duplicate value errors
				for (l = 0; l < dt.Rows.Count; l++)
				{
					dt.Rows[(int)l]["Index"] = l + 2000000000;
				}

				//Second pass populates "right" numbers
				for (l = 0; l < dt.Rows.Count; l++)
				{
					dt.Rows[(int)l]["Index"] = l;
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

		}
		public long Seek(string par_sColumnName, object par_oValue, bool par_bExact = true, long par_lStartRow = 1)
		{
			//PURPOSE:
			//       Returns an index (1-based) of the row which starts with 
			//       a value in a specified column. If not found, -1 is returned.
			//PARAMETERS:
			//       par_sColumnName: Name of the column in which to find a value.
			//       par_oValue: Value to find. Data type must match or convert
			//           to the column's data type.
			//       par_bExact: 'False' observed only if the column is of System.String
			//           type. When true (default), the whole value must match,
			//           else the value must begin with par_oValue. In case of all
			//           other data types, the value must match. String comparisons
			//           are always case insensitive.
			//       par_lStartRow: row at which to start the search. Default is 1.
			//RETURNS:
			//       Long: Index of the found row (1-based) or -1 if not found.

			string sProc = "clTable::Seek";
			long lResult = -1;
			//Dim dr As DataRow
			DataRow[] dr1 = null;
			long l = 0;
			long lTemp = 0;

			//Try
			//Abandoned technique - not flexible enough
			//If UCase(par_sColumnName) = UCase(Me.sValueColName) Then
			//    dr = dt.Rows.Find(par_oValue)
			//    If dr Is Nothing Then
			//        lResult = -1
			//    Else
			//        lResult = dt.Rows.IndexOf(dr) + 1
			//    End If
			//Else

			if (par_bExact)
			{
					switch (dt.Columns[par_sColumnName].DataType.ToString())
					{
						case "System.String":
						case "System.Char":
						case "System.Byte":
						case "System.DateTime":
						case "System.Guid":
							dr1 = dt.Select(par_sColumnName + " = '" + par_oValue.ToString() + "'", "Index ASC");
							break;
						default: //Numeric
							dr1 = dt.Select(par_sColumnName + " = " + par_oValue.ToString() + "", "Index ASC");
							break;
					}
				}
				else
				{
					switch (dt.Columns[par_sColumnName].DataType.ToString())
					{
						case "System.String":
							dr1 = dt.Select(par_sColumnName + " LIKE '" + par_oValue.ToString() + "%'", "Index ASC");
							break;
						case "System.Char":
						case "System.Byte":
						case "System.Guid":
							dr1 = dt.Select(par_sColumnName + " = '" + par_oValue.ToString() + "'", "Index ASC");
							break;
						case "System.DateTime":
							dr1 = dt.Select(par_sColumnName + " = '" + par_oValue.ToString() + "'", "Index ASC");
							break;
						default: //Numeric
							dr1 = dt.Select(par_sColumnName + " = " + par_oValue.ToString() + "", "Index ASC");
							break;
					}
				}
				if (dr1 == null)
				{
					lResult = -1;
				}
				else
				{
					//GetUpperBound returns -1 when dr length=0.
					if (dr1.GetUpperBound(0) < 0)
					{
						lResult = -1;
					}
					else
					{
						if (par_lStartRow <= 1)
						{
							lResult = dt.Rows.IndexOf(dr1[0]) + 1;
						}
						else
						{
							//Find a match after par_lStartRow
							lResult = -1;
							for (l = 0; l <= dr1.GetUpperBound(0); l++)
							{
								lTemp = dt.Rows.IndexOf(dr1[l]);
								if (lTemp >= par_lStartRow - 1)
								{
									lResult = lTemp + 1;
									break;
								}
							}
						}
					}
				}
			//End If

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return lResult;

		}
		public long Selected(string par_sTableName = "")
		{
			//PURPOSE:
			//       Returns the index of the 'selected' row in the datatable (1-based index).

			string sProc = "clTable::Selected";
			long lResult = 0;

			//Try
			lResult = lSelectedRow;
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return lResult;

		}
		public bool SelectPlus(string par_sTableName, long par_lRow)
		{
			//PURPOSE:
			//       Set the selected row in the datatable (1-based index).
			//       If par_lRow is greater than no of rows in datatable,
			//       the selected row remains unchanged. The name of this method
			//       is kept for backward compatibility with NGP. 
			//PARAMETERS:
			//       par_sTable: Ignored.
			//       par_lRow: Row to select (1-based index). If negative,
			//           no row is selected (lSelectedRow is -1).
			//RETURNS:
			//       Boolean: If par_lRow is > datatable count (adjusted for
			//           0-based index), False, else True. If par_lRow
			//           is < 1, it is set to True.

			string sProc = "clTable::SelectPlus";
			bool bResult = true;
			long lRow = par_lRow;

			//Try
			if (lRow > dt.Rows.Count)
			{
					bResult = false;
					//Selection remains unchanged
				}
				else
				{
					//If negative, make row -1 selected (no row is selected)
					if (lRow < 1)
					{
						lRow = -1;
						bResult = false;
					}
					//Change the selected row
					lSelectedRow = lRow;
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return bResult;
		}
		public bool SetVal(string par_sColumnName, long par_lRow, object par_oValue)
		{
			//PURPOSE:
			//       Sets a value to a particular cell (row/column intersection) without
			//       making that row 'selected'. In NGP,
			//       the equivalent syntax is COL_COLUMNNAME_TABLENAME[index] = <value>.
			//       If setting a value to the 'Value' column, you must make sure it is
			//       unique, otherwise an exception will be raised. If a duplicate value
			//       error gets raised, the Value column for sSelectedRow will be blank.
			//       This will cause an error when performing .Add or .Insert. 
			//       Do not try to set a value to the "Index" column because it will be
			//       replaced when the column is automatically regenerated.
			//RETURNS:
			//       Boolean: True when successful, False if not.
			//PARAMETERS:
			//       par_sColumnName: Name of the column
			//       par_lRow: index of the row in which to set the value (1-based index).
			//           If < 1, the value is set in the currently selected row. If no
			//           row is selected, the value is not set and False is returned.
			//       par_oValue: Value to set. Must match or convert to the column's data type.

			string sProc = "clTable::SetVal";

			bool bResult = true;
			long lRow = par_lRow;

			//Try
			if (lRow < 1)
			{
					//Use selected row
					lRow = this.lSelectedRow;
					if (lRow < 1)
					{
						return false;
					}
				}
				if (lRow > dt.Rows.Count)
				{
					goErr.SetError(35000, sProc, "lRow exceeds Rows.Count of the DataTable.");
					bResult = false;
				}
				if (dt.Rows.Count < 1)
				{
					//The table is empty
					return false;
				}
				dt.Rows[(int)(lRow - 1)][par_sColumnName] = par_oValue;
				//Catch ex As Exception
				//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//        goErr.SetError(ex, 45105, sProc)
				//    End If
				//End Try

				return bResult;

		}
		public bool Sort(string par_sColumnList)
		{
			//PURPOSE:
			//       Sort on one or more columns, asc or desc per column, not case sensitive.
			//       Selects the row that was selected prior to sorting.
			//PARAMETERS:
			//       par_sColumnList: SQL-syntax ORDER BY list of columns. Example:
			//               Text ASC, Type DESC, Value ASC
			//           IMPORTANT: This is different than in NGP, where the syntax was: 
			//               [+|-]Column1[ & vbTab & [+|-]Column2 ...]
			//           where '+' means 'ASC' and '-' means 'DESC'.
			//           "" value is allowed. 

			string sProc = "clTable::Sort";

			//Try
			object oKeyValue = GetSelectedValue();

				//DataSet management is not supported
				//If Not ds Is Nothing Then
				//    ds = New DataSet()
				//    ds.Tables.Add(dt)
				//End If

				//Sort using defaultview       
				DataTable dt2 = new DataTable("Sorted");
				//'Example of how to filter DefaultView
				//'Careful: this permanently replaces the global datatable with the filtered set
				//gw.dtAvail.DefaultView.RowFilter = "Field LIKE '<[%]CHK_%'"
				dt.DefaultView.Sort = par_sColumnList;
				dt = dt.DefaultView.ToTable();

				//Set primary key
				DataColumn[] PrimaryKeyColumns = new DataColumn[1];
				PrimaryKeyColumns[0] = dt.Columns[sValueColName];
				dt.PrimaryKey = PrimaryKeyColumns;

				//'Dataset, if used, requires that the table be added after the sort.
				//DataSet = New DataSet()
				//If DataSet.Tables.IndexOf(gw.dtAvail) < 0 Then
				//    DataSet.Tables.Add(gw.dtAvail)
				//Else
				//    If DataSet.Tables.CanRemove(gw.dtAvail) Then
				//        DataSet.Tables.Remove(gw.dtAvail)
				//        DataSet.Tables.Add(gw.dtAvail)
				//    Else
				//        'Can't remove table from dataset, raise error?
				//        DataSet.Tables.Add(gw.dtAvail)
				//    End If
				//End If

				//Select the previously selected row
				if (oKeyValue != null)
				{
					this.lSelectedRow = Seek(sValueColName, oKeyValue);
				}
				else
				{
					//Select first row
					if (this.Count() > 0)
					{
						this.lSelectedRow = 1;
					}
				}

				//'To databind to a grid control
				//DataGrid1.SetDataBinding(DataSet, "Avail")

				//Catch ex As Exception
				//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//        goErr.SetError(ex, 45105, sProc)
				//    End If
				//End Try

				return true;

		}

		public bool zz_Adjust(string par_s)
		{
			//Deprecated: in NGP allowed to define columns run-time.
			//Use AddColumn instead.
			//RETURNS:
			//       False.

			string sProc = "clTable::Adjust";

			return false;

		}

		public bool zz_Modify(string par_sColumnName, object par_oValue)
		{
			//PURPOSE:
			//       NOT SUPPORTED currently. Use SetVal() instead.
			//       Modify value of whole row or one cell (by column name).

			string sProc = "clTable::Modify";

			//Try

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return false;

		}

	}
}
