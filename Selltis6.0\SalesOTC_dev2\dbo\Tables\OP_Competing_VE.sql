﻿CREATE TABLE [dbo].[OP_Competing_VE] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Opp_Competing_Vendor_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    [GID_VE] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_OP_Competing_VE] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_OP_Competing_VE] FOREIGN KEY ([GID_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_VE_CompetitorIn_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[OP_Competing_VE] NOCHECK CONSTRAINT [LNK_OP_Competing_VE];


GO
ALTER TABLE [dbo].[OP_Competing_VE] NOCHECK CONSTRAINT [LNK_VE_CompetitorIn_OP];


GO
CREATE CLUSTERED INDEX [IX_VE_CompetitorIn_OP]
    ON [dbo].[OP_Competing_VE]([GID_OP] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_Competing_VE]
    ON [dbo].[OP_Competing_VE]([GID_VE] ASC);

