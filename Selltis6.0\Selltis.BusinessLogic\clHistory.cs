﻿using System;
using System.Web;

namespace Selltis.BusinessLogic
{
	public class clHistory
	{

		private Microsoft.VisualBasic.Collection gcHistory = new Microsoft.VisualBasic.Collection();
		private Microsoft.VisualBasic.Collection gcHistoryStack = new Microsoft.VisualBasic.Collection();

		private string gsCurrentGUID;
		private string gsCurrentType;
		private string gsCurrentSeqNo;
		private string gsLastVisitedSeqNo;

		private int giHistoryMax;
		private int glSeqNo;

		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		//Private goUI As clUI

		public Microsoft.VisualBasic.Collection Stack
		{
			get
			{
				return gcHistoryStack;
			}
		}

		public string CurrentObjGUID
		{
			get
			{
				return gsCurrentGUID;
			}
			set
			{
				gsCurrentGUID = value;
			}
		}

		public string CurrentObjType
		{
			get
			{
				return gsCurrentType;
			}
			set
			{
				gsCurrentType = value;
			}
		}

		public string CurrentObjSeqNo
		{
			get
			{
				return gsLastVisitedSeqNo;
			}
			set
			{
				gsLastVisitedSeqNo = value;
			}
		}

		public string CurrentSeqNo
		{
			get
			{
				return gsCurrentSeqNo;
			}
			set
			{
				gsCurrentSeqNo = value;
			}
		}

		public void Initialize()
		{
			string sProc = "clHistory::Initialize";
			// Try
			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
				goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
				goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
				goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
				goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			//goUI = HttpContext.Current.Session("goUI")
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public clHistory()
		{
			string sProc = "clHistory::New";
			// Try
			Initialize();
				glSeqNo = 0;
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public int GetNextSeqNo()
		{
			string sProc = "clHistory::GetNextSeqNo";
			// Try
			glSeqNo = glSeqNo + 1;
				return glSeqNo;
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public bool Add(clHistoryItem oItem)
		{
			string sProc = "clHistory::Add";
			try
			{

				//first, check if single reference requirement
				//if so, look for object already in history
				//if found, grab it, make it current, open it
				//if not found, add as per usual
				if (oItem.SingleReferenceKey != "")
				{
					//this is a single reference item, should only appear once in history
					//lets check each one, see if it is already there
					foreach (clHistoryItem oHistoryItem in gcHistoryStack)
					{
						if (oHistoryItem.SingleReferenceKey == oItem.SingleReferenceKey)
						{
							//found it, select it exit
							gsCurrentGUID = oItem.GUID;
							gsCurrentType = oItem.Type;
							gsCurrentSeqNo = oItem.SeqNo;
							gsLastVisitedSeqNo = oItem.SeqNo;
							return true;
						}
					}
				}

				//if i'm here, the single reference item is not already in history OR this is not a single reference item....

				int i = 0;
				clHistoryItem oHI = null;
				string sGUIDs = "";
				int iObjCount = 0;
				bool bFoundDesktop = false;
				int iDeletedCounter = 0;

				oItem.SeqNo = DuplicateAndInvertForwardStack(oItem);

				gcHistoryStack.Add(oItem);

				gcHistory.Add(oItem);
				gsCurrentGUID = oItem.GUID;
				gsCurrentType = oItem.Type;
				gsCurrentSeqNo = oItem.SeqNo;
				gsLastVisitedSeqNo = oItem.SeqNo;

				//only if desktop, attempt removal
				if (gsCurrentType == "DESKTOP")
				{

					//first check to see if max limit is met, if so, remove oldest which is in the first position
					iObjCount = CountUniqueObjects();
					giHistoryMax = Convert.ToInt32(goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "MAXHISTORYITEMS", "20"));

					if (iObjCount > giHistoryMax)
					{

						int iNumberToDelete = iObjCount - giHistoryMax;
						int j = 0;

						for (i = 1; i <= gcHistory.Count; i++)
						{
							oHI = (clHistoryItem)gcHistory[i];
							//remove only the first open desktop, if all are forms then treat like the form max was exceeded
							if (oHI.Type.ToUpper() == "DESKTOP")
							{
								Remove(oHI.SeqNo, true);
								bFoundDesktop = true;
								j = j + 1;
								if (j == iNumberToDelete)
								{
									break;
								}
							}
						}

						if (bFoundDesktop == false)
						{
							//treat this like the form max was exceeded (all items in history are forms)
							OfferFormManagement();
						}

					}

				}

				return true;
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return false;
			}
		}

		public bool AddTemp(string par_sSpecialType = "", string par_sSpecialGUID = "")
		{
			string sProc = "clHistory::AddTemp";
			try
			{
				RemoveTemp();
				clHistoryItem oItem = new clHistoryItem("TEMP", "TEMP", "", this.GetNextSeqNo().ToString(), par_sSpecialType, par_sSpecialGUID);
				gcHistory.Add(oItem);
				gsCurrentGUID = "TEMP";
				gsCurrentType = "TEMP";
				gsCurrentSeqNo = oItem.SeqNo;
				return true;
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return false;
			}
		}

		private int CountUniqueObjects()
		{
			string sProc = "clHistory::CountUniqueObjects";
			try
			{
				//for now, just count desktop objects
				int i = 0;
				clHistoryItem oHI = null;
				int iCount = 0;
				for (i = 1; i <= gcHistoryStack.Count; i++)
				{
					oHI = (clHistoryItem)gcHistory[i];
					if (oHI.Type.ToUpper() == "DESKTOP")
					{
						iCount = iCount + 1;
					}
				}
				return iCount;
				//Return gcHistoryStack.Count
				//Return gcHistory.Count
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return 0;
			}
		}

		public bool Remove(string sObjSeqNo, bool bKeepSelector = false)
		{
			string sProc = "clHistory::Remove";
			try
			{

				//goLog.Log(sProc, "Start", 0, True, True)

				int i = 0;
				clHistoryItem oHI = null;
				int iCount = gcHistory.Count;
				//Dim iIndex As Integer = 0
				string sGUIDToRemove = "";

				for (i = 1; i <= iCount; i++)
				{
					oHI = (clHistoryItem)gcHistory[i];
					if (oHI.SeqNo == sObjSeqNo)
					{
						sGUIDToRemove = oHI.GUID;
						gcHistory.Remove(i);
						if (!bKeepSelector)
						{
							if (iCount > 1)
							{
// INSTANT C# NOTE: The following VB 'Select Case' included either a non-ordinal switch expression or non-ordinal, range-type, or non-constant 'Case' expressions and was converted to C# 'if-else' logic:
//								Select Case i
// ORIGINAL LINE: Case 1
								if (i == 1)
								{
										clHistoryItem oNew = (clHistoryItem)gcHistory[1];
										gsCurrentGUID = oNew.GUID;
										gsCurrentType = oNew.Type;
										gsCurrentSeqNo = oNew.SeqNo;
										gsLastVisitedSeqNo = oNew.SeqNo;
								}
// ORIGINAL LINE: Case Is > 1
								else if (i > 1)
								{
// INSTANT C# NOTE: The following VB 'Select Case' included either a non-ordinal switch expression or non-ordinal, range-type, or non-constant 'Case' expressions and was converted to C# 'if-else' logic:
//										Select Case i
// ORIGINAL LINE: Case Is > gcHistory.Count
										if (i > gcHistory.Count)
										{
												clHistoryItem oNew = (clHistoryItem)gcHistory[gcHistory.Count];
												gsCurrentGUID = oNew.GUID;
												gsCurrentType = oNew.Type;
												gsCurrentSeqNo = oNew.SeqNo;
												gsLastVisitedSeqNo = oNew.SeqNo;
										}
// ORIGINAL LINE: Case Else
										else
										{
												clHistoryItem oNew = (clHistoryItem)gcHistory[i - 1];
												gsCurrentGUID = oNew.GUID;
												gsCurrentType = oNew.Type;
												gsCurrentSeqNo = oNew.SeqNo;
												gsLastVisitedSeqNo = oNew.SeqNo;
										}
								}
							}
							else
							{
								gsCurrentGUID = "";
								gsCurrentType = "";
								gsCurrentSeqNo = "";
								gsLastVisitedSeqNo = "";
							}
						}
						break;
					}
				}

				//remove all pointers with this guid
				if (sGUIDToRemove != "")
				{
					bool bFound = false;

					while (true)
					{
						bFound = false;
						iCount = gcHistory.Count;
						for (i = 1; i <= iCount; i++)
						{
							oHI = (clHistoryItem)gcHistory[i];
							if (oHI.GUID == sGUIDToRemove)
							{
								gcHistory.Remove(i);
								bFound = true;

								if (!bKeepSelector)
								{

									if (iCount > 1)
									{
// INSTANT C# NOTE: The following VB 'Select Case' included either a non-ordinal switch expression or non-ordinal, range-type, or non-constant 'Case' expressions and was converted to C# 'if-else' logic:
//										Select Case i
// ORIGINAL LINE: Case 1
										if (i == 1)
										{
												gsCurrentGUID = ((clHistoryItem)gcHistory[1]).GUID;
												gsCurrentType = ((clHistoryItem)gcHistory[1]).Type;
												gsCurrentSeqNo = ((clHistoryItem)gcHistory[1]).SeqNo;
												gsLastVisitedSeqNo = ((clHistoryItem)gcHistory[1]).SeqNo;
										}
// ORIGINAL LINE: Case Is > 1
										else if (i > 1)
										{
// INSTANT C# NOTE: The following VB 'Select Case' included either a non-ordinal switch expression or non-ordinal, range-type, or non-constant 'Case' expressions and was converted to C# 'if-else' logic:
//												Select Case i
// ORIGINAL LINE: Case Is > gcHistory.Count
												if (i > gcHistory.Count)
												{
														gsCurrentGUID = ((clHistoryItem)gcHistory[gcHistory.Count]).GUID;
														gsCurrentType = ((clHistoryItem)gcHistory[gcHistory.Count]).Type;
														gsCurrentSeqNo = ((clHistoryItem)gcHistory[gcHistory.Count]).SeqNo;
														gsLastVisitedSeqNo = ((clHistoryItem)gcHistory[gcHistory.Count]).SeqNo;
												}
// ORIGINAL LINE: Case Else
												else
												{
														gsCurrentGUID = ((clHistoryItem)gcHistory[i - 1]).GUID;
														gsCurrentType = ((clHistoryItem)gcHistory[i - 1]).Type;
														gsCurrentSeqNo = ((clHistoryItem)gcHistory[i - 1]).SeqNo;
														gsLastVisitedSeqNo = ((clHistoryItem)gcHistory[i - 1]).SeqNo;
												}
										}
									}
									else
									{
										gsCurrentGUID = "";
										gsCurrentType = "";
										gsCurrentSeqNo = "";
										gsLastVisitedSeqNo = "";
									}

								}

								break;
							}
						}

						if (!bFound)
						{

							//goLog.Log(sProc, "Exit", 0, True, True)
							break;

						}

					}

					bFound = false;
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of gcHistoryStack.Count for every iteration:
					int tempVar = gcHistoryStack.Count;
					for (i = 1; i <= tempVar; i++)
					{
						oHI = (clHistoryItem)gcHistoryStack[i];
						if (oHI.GUID == sGUIDToRemove)
						{
							gcHistoryStack.Remove(i);
							bFound = true;
							break;
						}
					}

				}

				//remove the actual ui object
				if (sGUIDToRemove != "")
				{
					// goUI.RemoveUIObject(sGUIDToRemove)
				}

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return false;
			}
// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public void RemoveAll()
		{
			string sProc = "clHistory::RemoveAll";
			// Try
			gcHistory.Clear();
				gcHistoryStack.Clear();
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public void RemoveTemp()
		{
			string sProc = "clHistory::RemoveTemp";
			// Try
			int i = 0;
				clHistoryItem oHI = null;
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of gcHistory.Count for every iteration:
				int tempVar = gcHistory.Count;
				for (i = 1; i <= tempVar; i++)
				{
					oHI = (clHistoryItem)gcHistory[i];
					if (oHI.GUID == "TEMP")
					{
						gcHistory.Remove(i);
						break;
					}
				}
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of gcHistoryStack.Count for every iteration:
				int tempVar2 = gcHistoryStack.Count;
				for (i = 1; i <= tempVar2; i++)
				{
					oHI = (clHistoryItem)gcHistoryStack[i];
					if (oHI.GUID == "TEMP")
					{
						gcHistoryStack.Remove(i);
						break;
					}
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public bool MoveBack()
		{
			string sProc = "clHistory::MoveBack";
			try
			{
				int i = 0;
				clHistoryItem oHI = null;
				switch (gsCurrentType)
				{
					case "TEMP":
						for (i = 1; i <= gcHistory.Count; i++)
						{
							oHI = (clHistoryItem)gcHistory[i];
							if (oHI.SeqNo == this.gsLastVisitedSeqNo)
							{
								gsCurrentGUID = ((clHistoryItem)gcHistory[i]).GUID;
								gsCurrentType = ((clHistoryItem)gcHistory[i]).Type;
								gsCurrentSeqNo = ((clHistoryItem)gcHistory[i]).SeqNo;
								gsLastVisitedSeqNo = ((clHistoryItem)gcHistory[i]).SeqNo;
								break;
							}
						}
						break;
					default:
						for (i = 1; i <= gcHistory.Count; i++)
						{
							oHI = (clHistoryItem)gcHistory[i];
							//If oHI.SeqNo = Me.gsCurrentSeqNo Then
							if (oHI.SeqNo == this.gsLastVisitedSeqNo)
							{
								if (i != 1)
								{
									gsCurrentGUID = ((clHistoryItem)gcHistory[i - 1]).GUID;
									gsCurrentType = ((clHistoryItem)gcHistory[i - 1]).Type;
									gsCurrentSeqNo = ((clHistoryItem)gcHistory[i - 1]).SeqNo;
									gsLastVisitedSeqNo = ((clHistoryItem)gcHistory[i - 1]).SeqNo;
								}
								else
								{
									//don't change current information. on the first one
								}
								break;
							}
						}
						break;
				}
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return false;
			}
// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public bool MoveForward()
		{
			string sProc = "clHistory::MoveForward";
			try
			{
				int i = 0;
				clHistoryItem oHI = null;
				switch (gsCurrentType)
				{
					case "TEMP":
						for (i = 1; i <= gcHistory.Count; i++)
						{
							oHI = (clHistoryItem)gcHistory[i];
							if (oHI.SeqNo == this.gsLastVisitedSeqNo)
							{
								gsCurrentGUID = ((clHistoryItem)gcHistory[i + 1]).GUID;
								gsCurrentType = ((clHistoryItem)gcHistory[i + 1]).Type;
								gsCurrentSeqNo = ((clHistoryItem)gcHistory[i + 1]).SeqNo;
								gsLastVisitedSeqNo = ((clHistoryItem)gcHistory[i + 1]).SeqNo;
								break;
							}
						}
						break;
					default:
						for (i = 1; i <= gcHistory.Count; i++)
						{
							oHI = (clHistoryItem)gcHistory[i];
							//If oHI.SeqNo = Me.gsCurrentSeqNo Then
							if (oHI.SeqNo == this.gsLastVisitedSeqNo)
							{
								if (i != gcHistory.Count)
								{
									gsCurrentGUID = ((clHistoryItem)gcHistory[i + 1]).GUID;
									gsCurrentType = ((clHistoryItem)gcHistory[i + 1]).Type;
									gsCurrentSeqNo = ((clHistoryItem)gcHistory[i + 1]).SeqNo;
									gsLastVisitedSeqNo = ((clHistoryItem)gcHistory[i + 1]).SeqNo;
								}
								else
								{
									//don't change the current information, on the last one
								}
								break;
							}
						}
						break;
				}
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return false;
			}
// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public string GetLastOpenObjectGUID()
		{
			string sProc = "clHistory::GetLastOpenObjectGUID";
			try
			{
				int i = 0;
				clHistoryItem oHI = null;
				string sGUID = "";
				for (i = gcHistory.Count; i >= 1; i--)
				{
					oHI = (clHistoryItem)gcHistory[i];
					if (oHI.Type != "TEMP")
					{
						if (gsLastVisitedSeqNo == oHI.SeqNo)
						{
							return oHI.GUID;
						}
					}
				}
				return "";
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return "";
			}
		}

		public string GetLastOpenDesktopGUID()
		{
			string sProc = "clHistory::GetLastOpenDesktopGUID";
			try
			{
				int i = 0;
				clHistoryItem oHI = null;
				string sGUID = "";
				bool bStartLooking = false;
				for (i = gcHistory.Count; i >= 1; i--)
				{
					oHI = (clHistoryItem)gcHistory[i];
					if (gsLastVisitedSeqNo == oHI.SeqNo)
					{
						bStartLooking = true;
					}
					if (oHI.Type.ToUpper() == "DESKTOP" && bStartLooking)
					{
						return oHI.GUID;
					}
				}
				return "";
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return "";
			}
		}

		public string GetLastOpenFormGUID()
		{
			string sProc = "clHistory::GetLastOpenFormGUID";
			try
			{
				int i = 0;
				clHistoryItem oHI = null;
				string sGUID = "";
				bool bStartLooking = false;
				for (i = gcHistory.Count; i >= 1; i--)
				{
					oHI = (clHistoryItem)gcHistory[i];

					//for now, this used for linkbox selector and NDB forms...
					if (oHI.NDBType.ToUpper() == "FORM")
					{
						return oHI.NDBGUID;
					}

					if (gsLastVisitedSeqNo == oHI.SeqNo)
					{
						bStartLooking = true;
					}
					if (oHI.Type.ToUpper() == "FORM" && bStartLooking)
					{
						return oHI.GUID;
					}
					//If gsLastVisitedSeqNo = oHI.SeqNo Then bStartLooking = True

				}
				return "";
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return "";
			}
		}

		public string GetParentFormGUID()
		{
			string sProc = "clHistory::GetParentFormGUID";
			try
			{
				int i = 0;
				clHistoryItem oHI = null;
				string sGUID = "";
				bool bStartLooking = false;
				for (i = gcHistory.Count; i >= 1; i--)
				{
					oHI = (clHistoryItem)gcHistory[i];

					//for now, this used for linkbox selector and NDB forms...
					if (oHI.NDBType.ToUpper() == "FORM")
					{
						return oHI.NDBGUID;
					}

					//If gsLastVisitedSeqNo = oHI.SeqNo Then bStartLooking = True
					if (oHI.Type.ToUpper() == "FORM" && bStartLooking)
					{
						return oHI.GUID;
					}
					if (gsLastVisitedSeqNo == oHI.SeqNo)
					{
						bStartLooking = true;
					}

				}
				return "";
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return "";
			}
		}

		private string DuplicateAndInvertForwardStack(clHistoryItem oItem)
		{
			string sProc = "clHistory::DuplicateAndInvertForwardStack";

			int i = 0;
			int iForwardStackStart = 0;
			clHistoryItem oHI = null;
			clHistoryItem oHICopy = null;
			var iCurrentSeqNo = int.Parse(oItem.SeqNo);

			try
			{

				//if there is nothing in the stack, no need to do anything
				if (gcHistory.Count == 0)
				{
					return iCurrentSeqNo.ToString();
				}

				for (i = 1; i <= gcHistory.Count; i++)
				{
					oHI = (clHistoryItem)gcHistory[i];
					if (oHI.SeqNo == this.gsLastVisitedSeqNo)
					{
						//this is the current item, start dupl/inv after this record
						iForwardStackStart = i;
						break;
					}
				}

				int iCount = gcHistory.Count;
				int iNo = 0;
				bool bDebug = false;

				for (i = iCount - 1; i >= iForwardStackStart; i--)
				{

					//added 2009-05-07
					if (i == 0)
					{
						break;
					}

					bDebug = true;
					if (i == iCount - 1)
					{
						iNo = iCurrentSeqNo;
					}
					else
					{
						iNo = GetNextSeqNo();
					}
					oHI = (clHistoryItem)gcHistory[i];
					oHICopy = new clHistoryItem(oHI.Type, oHI.GUID, oHI.Title, iNo.ToString());
					gcHistory.Add(oHICopy);
				}

				if (bDebug)
				{
					iCurrentSeqNo = GetNextSeqNo();
				}
				//iCurrentSeqNo = GetNextSeqNo()

				return iCurrentSeqNo.ToString();

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return iCurrentSeqNo.ToString();
			}
		}

		private void OfferFormManagement()
		{

		}

		~clHistory()
		{
// INSTANT C# NOTE: The base class Finalize method is automatically called from the destructor:
			//base.Finalize();
		}

		public void RenameCurrentObj(string sName)
		{
			string sProc = "clHistory::RenameCurrentObj";
			// Try
			int i = 0;
				clHistoryItem oHI = null;
				string sGUID = "";
				for (i = gcHistory.Count; i >= 1; i--)
				{
					oHI = (clHistoryItem)gcHistory[i];
					if (oHI.Type != "TEMP")
					{
						if (gsLastVisitedSeqNo == oHI.SeqNo)
						{
							sGUID = oHI.GUID;
							oHI.Title = sName;
							break;
						}
					}
				}
				if (sGUID != "")
				{
					//let's modify the visible list now
					for (i = 1; i <= gcHistoryStack.Count; i++)
					{
						oHI = (clHistoryItem)gcHistoryStack[i];
						if (oHI.GUID == sGUID)
						{
							oHI.Title = sName;
							break;
						}
					}
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public bool ContainsObject(string par_sGUID)
		{
			string sProc = "clHistory::ContainsObject";
			try
			{
				int i = 0;
				clHistoryItem oHI = null;
				string sGUID = "";
				for (i = gcHistory.Count; i >= 1; i--)
				{
					oHI = (clHistoryItem)gcHistory[i];
					if (par_sGUID == oHI.SeqNo)
					{
						return true;
					}
				}
				return false;
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return false;
			}
		}

	}
}
