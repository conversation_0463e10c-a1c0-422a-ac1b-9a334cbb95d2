﻿using System;
using System.Data;


//Owner: MI


using System.Web;

namespace Selltis.BusinessLogic
{
	public class clDefaults
	{
		//2/14/14 MI InitConduits. Added GOOGLECAL and GOOGLECONTACT.
		//MI 12/6/13 InitConduits: added OUTLOOKTODO.
		//WT 11/13/08 Added GetDateRangeLabelByKey
		//MI 10/15/09 Added FRM_.
		//MI 10/19/09 Added LBF prefix definitions to pertinent methods.
		//MI 2/7/08 InitPersonalOptions: changed default MAXHISTORYITEMS from 100 to 20.
		//MI 2/4/07 initViewDefaults: Added filling CAL_ALARMDEF.
		//MI 10/6/06 Replaced + with & throughout the class.
		//MI 9/8/06  Added support for BI_ field type

		private clProject goP;
		private clTransform goTR;
		private clMetaData goMeta;
		private clData goData;
		private clError goErr;
		private clLog goLog;
		private clDefaults goDef;

		private string sViewDefaults;
		private string sMetaObjects;
		private string sPersonalOptions;
		private string sConduits;
		private string sConduitMaps;

		private string sOutlookCalFieldList;
		private string sOutlookContactFieldList;
		private string sPalmAddressBookFieldList;
		private string sPalmDateBookFieldList;
		private string sPalmMemoFieldList;
		private string sPalmToDoFieldList;

		private string sOutlookCalMap;
		private string sOutlookContactMap;
		private string sPalmAddressBookMap;
		private string sPalmDateBookMap;
		private string sPalmMemoMap;
		private string sPalmToDoMap;

		private string sPageDefinitions;
		private string sLabelDefaults;
		private string sRecDetailDefs;
		public string AddSendData()
		{
			//MI 10/1/07
			//MI 2/21/05
			//AUTHOR: MI Moved from clUI to clDef 2/21/05
			//PURPOSE:
			//		Return the line to put in the MMO_SENDDATA in Activity and Quote
			//		forms' Send buttons. This standardizes the way the line is generated.
			//		This info may be needed for SellSend or one of WT's utilities.

			string sProc = "clDefaults::AddSendData";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sResult;

			//sResult = DateSys() & TAB & TimeSys() & TAB & goP : GetUserTID()
			sResult = goTR.NowUTC().ToString("yyyy-MM-dd HH:mm:ss.fff") + "\t" + goP.GetUserTID(); //*** MI 10/1/07
			//2007-03-31 18:34:59.683'

			return sResult;
		}
		public string CleanUpViewMeta(string par_sPage, string par_sDefaults = "")
		{
			//MI 8/2/07
			//MI 9/5/06

			//PURPOSE:
			//		Remove View metadata properties that are specified in View defaults
			//		and are therefore redundant. When View metadata is loaded, for 
			//		example by calling clData:PageRead(goP:GetUserTID(),sViewPageID...),
			//		the third parameter, default page, should be filled with View
			//		defaults, which can be read by calling goDef:GetViewDefaults().
			//		PageRead merges the default metadata with the metadata that is
			//		explicitly written in the View page. This means that all groups of
			//		properties that are the same as defaults needn't be written in the
			//		View page.
			//PARAMETERS:
			//		par_sPage: View properties page in ini format. This string _must_
			//			contain a valid file in the property FILE=, otherwise this method
			//			with raise an error and return the original string.
			//		par_sDefaults: View defaults in ini format. If blank, defaults
			//			are read from :GetViewDefaults(), otherwise defaults are taken
			//			from this parameter.
			//RETURNS:
			//		String with unnecessary properties removed.
			//ERRORS:
			//		This method sets an error, which the calling procedure must reset
			//		with goLog:SetError()!

			//For debugging the order of calls.
			string sProc = "clDefaults::CleanUpViewMeta";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sFileName = null;
			string sDefs = null;
			string sPage = par_sPage;

			sFileName = goTR.StrRead(sPage, "FILE", "", false);
			if (!goData.IsFileValid(sFileName))
			{
				goErr.SetError(10100, sProc, "", sFileName);
				// Internal error: incorrect file name '[1]'. 
				return par_sPage;
			}

			if (par_sDefaults.ToUpper() == "")
			{
				sDefs = GetViewDefaults(sFileName);
			}
			else
			{
				sDefs = par_sDefaults;
			}

			//====================== GENERAL =========================


			//====================== FORMAT ==========================
			sPage = CleanUpViewMetaFormt(sPage, sDefs);

			//====================== FIELDS ==========================
			sPage = CleanUpViewMetaFlds(sPage, sDefs);

			//====================== COLORS ==========================
			sPage = CleanUpViewMetaClrs(sPage, sDefs);

			//====================== FONTS ===========================
			sPage = CleanUpViewMetaFonts(sPage, sDefs);

			//====================== PRINT/SEND ======================
			sPage = CleanUpViewMetaPrn(sPage, sDefs);

			return sPage;

		}
		public string CleanUpViewMetaClrs(string par_sPage, string par_sDefs)
		{
			//CS ported 9/8/06
			//CS PORTED as placeholder. Return par_sPage.
			//==> Port

			//Called from :CleanUpViewMeta, this cleans up Colors metadata in Views
			//PARAMETERS:
			//		par_sPage: metadata page
			//		par_sDefs: Defaults
			//RETURNS:
			//		Cleaned up string

			//For debugging the order of calls.
			string sProc = "clDefaults::CleanUpViewMetaClrs";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)


			string sPage = par_sPage;
			bool bDiff = false;
			//CS Dim iCount As Integer
			string sTemp = null;
			//CS Dim i As Integer
			string sDefs = par_sDefs;

			//------------- LIST -------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "LIST_CellBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_CellBGColor", "", false))
				{
					bDiff = true;
					goto ProcessListColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_CellTxtColor", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_CellTxtColor", ""))
				{
					bDiff = true;
					goto ProcessListColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_SectionHeadingTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_SectionHeadingTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessListColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_SectionSummaryTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_SectionSummaryTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessListColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_HeadingBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_HeadingBGColor", "", false))
				{
					bDiff = true;
					goto ProcessListColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_HeadingTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_HeadingTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessListColors;
				}
			}
	ProcessListColors:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "LIST_CellBGColor");
				goTR.StrDelete(ref sPage, "LIST_CellTxtColor");
				goTR.StrDelete(ref sPage, "LIST_SectionHeadingTxtColor");
				goTR.StrDelete(ref sPage, "LIST_SectionSummaryTxtColor");
				goTR.StrDelete(ref sPage, "LIST_HeadingBGColor");
				goTR.StrDelete(ref sPage, "LIST_HeadingTxtColor");
			}

			//------------- CALDAY -------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "CALDAY_SelectedBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_SelectedBGColor", "", false))
				{
					bDiff = true;
					goto ProcessDayCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_SelectedTxtColor", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_SelectedTxtColor", ""))
				{
					bDiff = true;
					goto ProcessDayCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_NonTimedBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_NonTimedBGColor", "", false))
				{
					bDiff = true;
					goto ProcessDayCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_NonTimedTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_NonTimedTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessDayCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_HeadingBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_HeadingBGColor", "", false))
				{
					bDiff = true;
					goto ProcessDayCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_HeadingTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_HeadingTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessDayCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_NonPrimeBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_NonPrimeBGColor", "", false))
				{
					bDiff = true;
					goto ProcessDayCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_NonPrimeTxtColor", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_NonPrimeTxtColor", ""))
				{
					bDiff = true;
					goto ProcessDayCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_PrimeBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_PrimeBGColor", "", false))
				{
					bDiff = true;
					goto ProcessDayCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_PrimeTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_PrimeTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessDayCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_TimeBarBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_TimeBarBGColor", "", false))
				{
					bDiff = true;
					goto ProcessDayCalColors;
				}
			}
	ProcessDayCalColors:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "CALDAY_SelectedBGColor");
				goTR.StrDelete(ref sPage, "CALDAY_SelectedTxtColor");
				goTR.StrDelete(ref sPage, "CALDAY_NonTimedBGColor");
				goTR.StrDelete(ref sPage, "CALDAY_NonTimedTxtColor");
				goTR.StrDelete(ref sPage, "CALDAY_HeadingBGColor");
				goTR.StrDelete(ref sPage, "CALDAY_HeadingTxtColor");
				goTR.StrDelete(ref sPage, "CALDAY_NonPrimeBGColor");
				goTR.StrDelete(ref sPage, "CALDAY_NonPrimeTxtColor");
				goTR.StrDelete(ref sPage, "CALDAY_PrimeBGColor");
				goTR.StrDelete(ref sPage, "CALDAY_PrimeTxtColor");
				goTR.StrDelete(ref sPage, "CALDAY_TimeBarBGColor");
			}

			//------------- CALWEEK -------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "CALWEEK_CellTodayBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_CellTodayBGColor", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_CellTodayTxtColor", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_CellTodayTxtColor", ""))
				{
					bDiff = true;
					goto ProcessWeekCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_CellWeekendBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_CellWeekendBGColor", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_CellWeekendTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_CellWeekendTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_CellWorkDayBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_CellWorkDayBGColor", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_CellWorkDayTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_CellWorkDayTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_SelDayBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_SelDayBGColor", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_SelDayTxtColor", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_SelDayTxtColor", ""))
				{
					bDiff = true;
					goto ProcessWeekCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_TimeBarBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_TimeBarBGColor", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalColors;
				}
			}
	ProcessWeekCalColors:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "CALWEEK_CellTodayBGColor");
				goTR.StrDelete(ref sPage, "CALWEEK_CellTodayTxtColor");
				goTR.StrDelete(ref sPage, "CALWEEK_CellWeekendBGColor");
				goTR.StrDelete(ref sPage, "CALWEEK_CellWeekendTxtColor");
				goTR.StrDelete(ref sPage, "CALWEEK_CellWorkDayBGColor");
				goTR.StrDelete(ref sPage, "CALWEEK_CellWorkDayTxtColor");
				goTR.StrDelete(ref sPage, "CALWEEK_SelDayBGColor");
				goTR.StrDelete(ref sPage, "CALWEEK_SelDayTxtColor");
				goTR.StrDelete(ref sPage, "CALWEEK_TimeBarBGColor");
			}


			//------------- CALMONTH -------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "CALMONTH_CellTodayBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_CellTodayBGColor", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_CellTodayTxtColor", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_CellTodayTxtColor", ""))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_CellInactiveBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_CellInactiveBGColor", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_CellWeekendBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_CellWeekendBGColor", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_CellWeekendTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_CellWeekendTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_CellWorkDayBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_CellWorkDayBGColor", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_CellWorkDayTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_CellWorkDayTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_HeadingWeekendBGColor", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_HeadingWeekendBGColor", ""))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_HeadingWeekendTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_HeadingWeekendTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_HeadingWorkDayBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_HeadingWorkDayBGColor", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_HeadingWorkDayTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_HeadingWorkDayTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_DateNumberTxtColor", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_DateNumberTxtColor", ""))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_SelDayBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_SelDayBGColor", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_SelDayTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_SelDayTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_TimeBarBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_TimeBarBGColor", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalColors;
				}
			}
	ProcessMonthCalColors:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "CALMONTH_CellTodayBGColor");
				goTR.StrDelete(ref sPage, "CALMONTH_CellTodayTxtColor");
				goTR.StrDelete(ref sPage, "CALMONTH_CellInactiveBGColor");
				goTR.StrDelete(ref sPage, "CALMONTH_CellWeekendBGColor");
				goTR.StrDelete(ref sPage, "CALMONTH_CellWeekendTxtColor");
				goTR.StrDelete(ref sPage, "CALMONTH_CellWorkDayBGColor");
				goTR.StrDelete(ref sPage, "CALMONTH_CellWorkDayTxtColor");
				goTR.StrDelete(ref sPage, "CALMONTH_HeadingWeekendBGColor");
				goTR.StrDelete(ref sPage, "CALMONTH_HeadingWeekendTxtColor");
				goTR.StrDelete(ref sPage, "CALMONTH_HeadingWorkDayBGColor");
				goTR.StrDelete(ref sPage, "CALMONTH_HeadingWorkDayTxtColor");
				goTR.StrDelete(ref sPage, "CALMONTH_DateNumberTxtColor");
				goTR.StrDelete(ref sPage, "CALMONTH_SelDayBGColor");
				goTR.StrDelete(ref sPage, "CALMONTH_SelDayTxtColor");
				goTR.StrDelete(ref sPage, "CALMONTH_TimeBarBGColor");
			}

			//------------- CALYEAR -------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "CALYEAR_NonEmptyBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALYEAR_NonEmptyBGColor", "", false))
				{
					bDiff = true;
					goto ProcessYearCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALYEAR_NonEmptyTxtColor", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALYEAR_NonEmptyTxtColor", ""))
				{
					bDiff = true;
					goto ProcessYearCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALYEAR_BGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALYEAR_BGColor", "", false))
				{
					bDiff = true;
					goto ProcessYearCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALYEAR_TxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALYEAR_TxtColor", "", false))
				{
					bDiff = true;
					goto ProcessYearCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALYEAR_SelDateBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALYEAR_SelDateBGColor", "", false))
				{
					bDiff = true;
					goto ProcessYearCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALYEAR_SelDateBorderColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALYEAR_SelDateBorderColor", "", false))
				{
					bDiff = true;
					goto ProcessYearCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALYEAR_SelDateTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALYEAR_SelDateTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessYearCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALYEAR_TodayBGColor", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALYEAR_TodayBGColor", ""))
				{
					bDiff = true;
					goto ProcessYearCalColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALYEAR_TodayTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALYEAR_TodayTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessYearCalColors;
				}
			}
	ProcessYearCalColors:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "CALYEAR_NonEmptyBGColor");
				goTR.StrDelete(ref sPage, "CALYEAR_NonEmptyTxtColor");
				goTR.StrDelete(ref sPage, "CALYEAR_BGColor");
				goTR.StrDelete(ref sPage, "CALYEAR_TxtColor");
				goTR.StrDelete(ref sPage, "CALYEAR_SelDateBGColor");
				goTR.StrDelete(ref sPage, "CALYEAR_SelDateBorderColor");
				goTR.StrDelete(ref sPage, "CALYEAR_SelDateTxtColor");
				goTR.StrDelete(ref sPage, "CALYEAR_TodayBGColor");
				goTR.StrDelete(ref sPage, "CALYEAR_TodayTxtColor");
			}


			//------------- MFIELD -------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "MFIELD_CellBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "MFIELD_CellBGColor", "", false))
				{
					bDiff = true;
					goto ProcessMFieldColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "MFIELD_CellTxtColor", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "MFIELD_CellTxtColor", ""))
				{
					bDiff = true;
					goto ProcessMFieldColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "MFIELD_HeadingBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "MFIELD_HeadingBGColor", "", false))
				{
					bDiff = true;
					goto ProcessMFieldColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "MFIELD_HeadingTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "MFIELD_HeadingTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessMFieldColors;
				}
			}
	ProcessMFieldColors:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "MFIELD_CellBGColor");
				goTR.StrDelete(ref sPage, "MFIELD_CellTxtColor");
				goTR.StrDelete(ref sPage, "MFIELD_HeadingBGColor");
				goTR.StrDelete(ref sPage, "MFIELD_HeadingTxtColor");
			}

			//------------- RECORD -------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "RECORD_CellBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "RECORD_CellBGColor", "", false))
				{
					bDiff = true;
					goto ProcessRecordColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "RECORD_CellTxtColor", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "RECORD_CellTxtColor", ""))
				{
					bDiff = true;
					goto ProcessRecordColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "RECORD_HeadingBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "RECORD_HeadingBGColor", "", false))
				{
					bDiff = true;
					goto ProcessRecordColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "RECORD_HeadingTxtColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "RECORD_HeadingTxtColor", "", false))
				{
					bDiff = true;
					goto ProcessRecordColors;
				}
			}
	ProcessRecordColors:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "RECORD_CellBGColor");
				goTR.StrDelete(ref sPage, "RECORD_CellTxtColor");
				goTR.StrDelete(ref sPage, "RECORD_HeadingBGColor");
				goTR.StrDelete(ref sPage, "RECORD_HeadingTxtColor");
			}

			//------------- SFIELD -------------
			goTR.StrRead(sPage, "SFIELD_CellBGColor", "255,255,255");
			goTR.StrRead(sPage, "SFIELD_CellTxtColor", "0,0,0");

			bDiff = false;
			sTemp = goTR.StrRead(sPage, "SFIELD_CellBGColor", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "SFIELD_CellBGColor", "", false))
				{
					bDiff = true;
					goto ProcessSFieldColors;
				}
			}
			sTemp = goTR.StrRead(sPage, "SFIELD_CellTxtColor", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "SFIELD_CellTxtColor", ""))
				{
					bDiff = true;
					goto ProcessSFieldColors;
				}
			}
	ProcessSFieldColors:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "SFIELD_CellBGColor");
				goTR.StrDelete(ref sPage, "SFIELD_CellTxtColor");
			}

			return (sPage);
		}
		public string CleanUpViewMetaFlds(string par_sPage, string par_sDefs)
		{
			//CS 9/8/06: Translated from MField to end
			//MI 9/5/06
			//==> Finish the commented sections below

			//Called from :CleanUpViewMeta, this cleans up Fields metadata in Views
			//PARAMETERS:
			//		par_sPage: metadata page
			//		par_sDefs: Defaults
			//RETURNS:
			//		Cleaned up string

			string sProc = "clDefaults::CleanUpViewMetaFlds";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sPage = par_sPage;
			bool bDiff = false;
			int iCount = 0;
			string sTemp = null;
			int i = 0;
			string sDefs = par_sDefs;

			// ------------- LIST ------------- 
			bDiff = false;
			iCount = 0;
			sTemp = goTR.StrRead(sPage, "COLCOUNT", "", false);
			if (sTemp == "")
			{
				//Count wasn't written - delete all field definitions
				goto ProcessListFields;
			}
			else
			{
				iCount = Convert.ToInt32(NumericHelper.Val(sTemp));
				if (iCount.ToString() != goTR.StrRead(sDefs, "COLCOUNT", "1", false))
				{
					//Count is different - don't delete any field definitions
					bDiff = true;
					goto ProcessListFields;
				}
			}
			//Not removing any COL definitions because the view is not aware of defaults
			bDiff = true;

			//For i = 1 To iCount
			//    sTemp = UCase(goTR.StrRead(sPage, "COL" & i.ToString & "FIELD", "", False))
			//    If sTemp <> "" Then
			//        If sTemp <> UCase(goTR.StrRead(sDefs, "COL" & i.ToString & "FIELD", "", False)) Then
			//            bDiff = True
			//            GoTo ProcessListFields
			//        End If
			//    End If
			//    sTemp = UCase(goTR.StrRead(sPage, "COL" & i.ToString & "LABEL", ""))
			//    If sTemp <> "" Then
			//        If sTemp <> UCase(goTR.StrRead(sDefs, "COL" & i.ToString & "LABEL", "")) Then
			//            bDiff = True
			//            GoTo ProcessListFields
			//        End If
			//    End If
			//    sTemp = goTR.StrRead(sPage, "COL" & i.ToString & "WIDTH", "", False)
			//    If sTemp <> "" Then
			//        If sTemp <> goTR.StrRead(sDefs, "COL" & i.ToString & "WIDTH", "", False) Then
			//            bDiff = True
			//            GoTo ProcessListFields
			//        End If
			//    End If
			//    sTemp = goTR.StrRead(sPage, "COL" & i.ToString & "ALIGNMENT", "", False)
			//    If sTemp <> "" Then
			//        If sTemp <> goTR.StrRead(sDefs, "COL" & i.ToString & "ALIGNMENT", "", False) Then
			//            bDiff = True
			//            GoTo ProcessListFields
			//        End If
			//    End If
			//    sTemp = goTR.StrRead(sPage, "COL" & i.ToString & "TOTAL", "", False)
			//    If sTemp <> "" Then
			//        If sTemp <> goTR.StrRead(sDefs, "COL" & i.ToString & "TOTAL", "", False) Then
			//            bDiff = True
			//            GoTo ProcessListFields
			//        End If
			//    End If
			//    sTemp = goTR.StrRead(sPage, "COL" & i.ToString & "AVERAGE", "")
			//    If sTemp <> "" Then
			//        If sTemp <> goTR.StrRead(sDefs, "COL" & i.ToString & "AVERAGE", "") Then
			//            bDiff = True
			//            GoTo ProcessListFields
			//        End If
			//    End If
			//    sTemp = goTR.StrRead(sPage, "COL" & i.ToString & "MINIMUM", "", False)
			//    If sTemp <> "" Then
			//        If sTemp <> goTR.StrRead(sDefs, "COL" & i.ToString & "MINIMUM", "", False) Then
			//            bDiff = True
			//            GoTo ProcessListFields
			//        End If
			//    End If
			//    sTemp = goTR.StrRead(sPage, "COL" & i.ToString & "MAXIMUM", "", False)
			//    If sTemp <> "" Then
			//        If sTemp <> goTR.StrRead(sDefs, "COL" & i.ToString & "MAXIMUM", "", False) Then
			//            bDiff = True
			//            GoTo ProcessListFields
			//        End If
			//    End If
			//    sTemp = goTR.StrRead(sPage, "COL" & i.ToString & "DISPLAYASICON", "", False)
			//    If sTemp <> "" Then
			//        If sTemp <> goTR.StrRead(sDefs, "COL" & i.ToString & "DISPLAYASICON", "", False) Then
			//            bDiff = True
			//            GoTo ProcessListFields
			//        End If
			//    End If
			//Next i
	ProcessListFields:
			if (!bDiff)
			{
				//Delete all list field definitions
				i = 1;
				for (i = 1; i <= iCount; i++)
				{
					goTR.StrDelete(ref sPage, "COL" + i.ToString() + "FIELD");
					goTR.StrDelete(ref sPage, "COL" + i.ToString() + "LABEL");
					goTR.StrDelete(ref sPage, "COL" + i.ToString() + "WIDTH");
					goTR.StrDelete(ref sPage, "COL" + i.ToString() + "ALIGNMENT");
					goTR.StrDelete(ref sPage, "COL" + i.ToString() + "TOTAL");
					goTR.StrDelete(ref sPage, "COL" + i.ToString() + "AVERAGE");
					goTR.StrDelete(ref sPage, "COL" + i.ToString() + "MINIMUM");
					goTR.StrDelete(ref sPage, "COL" + i.ToString() + "MAXIMUM");
					goTR.StrDelete(ref sPage, "COL" + i.ToString() + "DISPLAYASICON");
				}
				goTR.StrDelete(ref sPage, "COLCOUNT");
			}

			// ------------- MFIELD -------------
			bDiff = false;
			iCount = 0;
			sTemp = goTR.StrRead(sPage, "MFIELD_FIELDCOUNT", "", false);
			if (sTemp == "")
			{
				//Count wasn't written - delete all field definitions
				goto ProcessMfieldFields;
			}
			else
			{
				iCount = Convert.ToInt32(NumericHelper.Val(sTemp));
				if (iCount.ToString() != goTR.StrRead(sDefs, "MFIELD_FIELDCOUNT", "1", false))
				{
					//Count is different - don't delete any field definitions
					bDiff = true;
					goto ProcessMfieldFields;
				}
			}

			//Not removing any field definitions because the view is not aware of the defaults
			bDiff = true;
			//For i = 1 To iCount
			//    sTemp = UCase(goTR.StrRead(sPage, "MFIELD_FIELD" & i.ToString & "NAME", "", False))
			//    If sTemp <> "" Then
			//        If sTemp <> UCase(goTR.StrRead(sDefs, "MFIELD_FIELD" & i.ToString & "NAME", "", False)) Then
			//            bDiff = True
			//            GoTo ProcessMfieldFields
			//        End If
			//    End If
			//    sTemp = UCase(goTR.StrRead(sPage, "MFIELD_FIELD" & i.ToString & "LABEL", ""))
			//    If sTemp <> "" Then
			//        If sTemp <> UCase(goTR.StrRead(sDefs, "MFIELD_FIELD" & i.ToString & "LABEL", "")) Then
			//            bDiff = True
			//            GoTo ProcessMfieldFields
			//        End If
			//    End If
			//Next i
	ProcessMfieldFields:
			if (!bDiff)
			{
				//Delete all list field definitions
				i = 1;
				for (i = 1; i <= iCount; i++)
				{
					goTR.StrDelete(ref sPage, "MFIELD_FIELD" + i.ToString() + "NAME");
					goTR.StrDelete(ref sPage, "MFIELD_FIELD" + i.ToString() + "LABEL");
				}
				goTR.StrDelete(ref sPage, "MFIELD_FIELDCOUNT");
			}

			// ------------- RECORD -------------
			bDiff = false;
			iCount = 0;
			sTemp = goTR.StrRead(sPage, "RECORD_FIELDCOUNT", "", false);
			if (sTemp == "")
			{
				//Count wasn't written - delete all field definitions
				goto ProcessRecordFields;
			}
			else
			{
				iCount = Convert.ToInt32(NumericHelper.Val(sTemp));
				if (iCount.ToString() != goTR.StrRead(sDefs, "RECORD_FIELDCOUNT", "1", false))
				{
					//Count is different - don't delete any field definitions
					bDiff = true;
					goto ProcessRecordFields;
				}
			}

			//Not removing any field definitions because the view is not aware of the defaults
			bDiff = true;
			//For i = 1 To iCount
			//    sTemp = UCase(goTR.StrRead(sPage, "RECORD_FIELD" & i.ToString & "NAME", "", False))
			//    If sTemp <> "" Then
			//        If sTemp <> UCase(goTR.StrRead(sDefs, "RECORD_FIELD" & i.ToString & "NAME", "", False)) Then
			//            bDiff = True
			//            GoTo ProcessRecordFields
			//        End If
			//    End If
			//    sTemp = UCase(goTR.StrRead(sPage, "RECORD_FIELD" & i.ToString & "LABEL", ""))
			//    If sTemp <> "" Then
			//        If sTemp <> UCase(goTR.StrRead(sDefs, "RECORD_FIELD" & i.ToString & "LABEL", "")) Then
			//            bDiff = True
			//            GoTo ProcessRecordFields
			//        End If
			//    End If
			//Next i
	ProcessRecordFields:
			if (!bDiff)
			{
				//Delete all list field definitions
				i = 1;
				for (i = 1; i <= iCount; i++)
				{
					goTR.StrDelete(ref sPage, "RECORD_FIELD" + i.ToString() + "NAME");
					goTR.StrDelete(ref sPage, "RECORD_FIELD" + i.ToString() + "LABEL");
				}
				goTR.StrDelete(ref sPage, "RECORD_FIELDCOUNT");
			}

			// ------------ SFIELD -------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "SFIELD_FIELDNAME", "", false).ToUpper();
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "SFIELD_FIELDNAME", "", false).ToUpper())
				{
					bDiff = true;
					goto ProcessSfieldFields;
				}
			}
			sTemp = goTR.StrRead(sPage, "SFIELD_FIELDLABEL", "").ToUpper();
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "SFIELD_FIELDLABEL", "").ToUpper())
				{
					bDiff = true;
					goto ProcessSfieldFields;
				}
			}

			//Not removing any field definitions because the view is not aware of the defaults
			bDiff = true;

	ProcessSfieldFields:
			if (!bDiff)
			{
				//Delete all field definitions
				goTR.StrDelete(ref sPage, "SFIELD_FIELDNAME");
				goTR.StrDelete(ref sPage, "SFIELD_FIELDLABEL");
			}


			//----------------- CALENDAR FIELDS ------------
			//Not removing any field definitions because the view is not aware of the defaults
			bDiff = true;

			//sTemp = UCase(goTR.StrRead(sPage, "CALDAY_LINE", ""))
			//If sTemp <> "" Then
			//    If sTemp = UCase(goTR.StrRead(sDefs, "CALDAY_LINE", "")) Then
			//        goTR.StrDelete(sPage, "CALDAY_LINE")
			//    End If
			//End If
			//sTemp = UCase(goTR.StrRead(sPage, "CALWEEK_LINE", ""))
			//If sTemp <> "" Then
			//    If sTemp = UCase(goTR.StrRead(sDefs, "CALWEEK_LINE", "")) Then
			//        goTR.StrDelete(sPage, "CALWEEK_LINE")
			//    End If
			//End If
			//sTemp = UCase(goTR.StrRead(sPage, "CALMONTH_LINE", ""))
			//If sTemp <> "" Then
			//    If sTemp = UCase(goTR.StrRead(sDefs, "CALMONTH_LINE", "")) Then
			//        goTR.StrDelete(sPage, "CALMONTH_LINE")
			//    End If
			//End If


			//---------- FIELD DEFINITIONS FOR CALENDAR TYPES --------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "CAL_DATEDEF", "", false).ToUpper();
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CAL_DATEDEF", "", false).ToUpper())
				{
					bDiff = true;
					goto ProcessCalDefinitions;
				}
			}
			sTemp = goTR.StrRead(sPage, "CAL_STARTTIMEDEF", "").ToUpper();
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CAL_STARTTIMEDEF", "").ToUpper())
				{
					bDiff = true;
					goto ProcessCalDefinitions;
				}
			}
			sTemp = goTR.StrRead(sPage, "CAL_ENDTIMEDEF", "", false).ToUpper();
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CAL_ENDTIMEDEF", "", false).ToUpper())
				{
					bDiff = true;
					goto ProcessCalDefinitions;
				}
			}
			sTemp = goTR.StrRead(sPage, "CAL_ALARMDEF", "", false).ToUpper();
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CAL_ALARMDEF", "", false).ToUpper())
				{
					bDiff = true;
					goto ProcessCalDefinitions;
				}
			}
	ProcessCalDefinitions:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "CAL_DATEDEF");
				goTR.StrDelete(ref sPage, "CAL_STARTTIMEDEF");
				goTR.StrDelete(ref sPage, "CAL_ENDTIMEDEF");
				goTR.StrDelete(ref sPage, "CAL_ALARMDEF");
			}

			return sPage;
		}
		public string CleanUpViewMetaFonts(string par_sPage, string par_sDefs)
		{
			//CS 9/8/06
			//CS PORTED as placeholder. Return par_sPage.
			//==> Port

			//Called from :CleanUpViewMeta, this cleans up Fonts metadata in Views
			//PARAMETERS:
			//		par_sPage: metadata page
			//		par_sDefs: Defaults
			//RETURNS:
			//		Cleaned up string

			//For debugging the order of calls.
			string sProc = "clDefaults::CleanUpViewMetaFonts";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)



			string sPage = par_sPage;
			bool bDiff = false;
			//CS Dim iCount As Integer
			string sTemp = null;
			//CS Dim i As Integer
			string sDefs = par_sDefs;

			//-------------- LIST ----------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "LIST_HTMLCellFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_HTMLCellFont", "", false))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_HTMLHeadingFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_HTMLHeadingFont", ""))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_HTMLSectionHeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_HTMLSectionHeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_HTMLSectionSummaryFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_HTMLSectionSummaryFont", "", false))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_PrintTextFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_PrintTextFont", "", false))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_PrintHeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_PrintHeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_PrintSectionHeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_PrintSectionHeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_PrintSectionSummaryFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_PrintSectionSummaryFont", ""))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_HTMLTitleFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_HTMLTitleFont", "", false))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_PrintHeaderFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_PrintHeaderFont", "", false))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_PrintTitleFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_PrintTitleFont", "", false))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_PrintFooterFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_PrintFooterFont", ""))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_TextFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_TextFont", "", false))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_HeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_HeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_SectionHeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_SectionHeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "LIST_SectionSummaryFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "LIST_SectionSummaryFont", "", false))
				{
					bDiff = true;
					goto ProcessListFonts;
				}
			}
	ProcessListFonts:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "LIST_HTMLCellFont");
				goTR.StrDelete(ref sPage, "LIST_HTMLHeadingFont");
				goTR.StrDelete(ref sPage, "LIST_HTMLSectionHeadingFont");
				goTR.StrDelete(ref sPage, "LIST_HTMLSectionSummaryFont");
				goTR.StrDelete(ref sPage, "LIST_PrintTextFont");
				goTR.StrDelete(ref sPage, "LIST_PrintHeadingFont");
				goTR.StrDelete(ref sPage, "LIST_PrintSectionHeadingFont");
				goTR.StrDelete(ref sPage, "LIST_PrintSectionSummaryFont");
				goTR.StrDelete(ref sPage, "LIST_HTMLTitleFont");
				goTR.StrDelete(ref sPage, "LIST_PrintHeaderFont");
				goTR.StrDelete(ref sPage, "LIST_PrintTitleFont");
				goTR.StrDelete(ref sPage, "LIST_PrintFooterFont");
				goTR.StrDelete(ref sPage, "LIST_TextFont");
				goTR.StrDelete(ref sPage, "LIST_HeadingFont");
				goTR.StrDelete(ref sPage, "LIST_SectionHeadingFont");
				goTR.StrDelete(ref sPage, "LIST_SectionSummaryFont");
			}


			//----------------- CALDAY ---------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "CALDAY_HTMLCellFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_HTMLCellFont", "", false))
				{
					bDiff = true;
					goto ProcessDayCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_HTMLHeadingFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_HTMLHeadingFont", ""))
				{
					bDiff = true;
					goto ProcessDayCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_HTMLTimeFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_HTMLTimeFont", "", false))
				{
					bDiff = true;
					goto ProcessDayCalFonts;
				}
			}
			//	sTemp=goTr:StrRead(sPage, "CALDAY_HeadingFont", "",False)
			//	IF sTemp<>"" THEN
			//		IF sTemp<>goTr:StrRead(sDefs, "CALDAY_HeadingFont", "",False) THEN
			//			bDiff=True
			//			GOTO ProcessDayCalFonts
			//		END
			//	END
			//sTemp=goTr:StrRead(sPage, "CALDAY_TimeBarFont", "",False)
			//IF sTemp<>"" THEN
			//	IF sTemp<>goTr:StrRead(sDefs, "CALDAY_TimeBarFont", "",False) THEN
			//		bDiff=True
			//		GOTO ProcessDayCalFonts
			//	END
			//END
			sTemp = goTR.StrRead(sPage, "CALDAY_MinutesFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_MinutesFont", "", false))
				{
					bDiff = true;
					goto ProcessDayCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_HoursFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_HoursFont", "", false))
				{
					bDiff = true;
					goto ProcessDayCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_TextFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_TextFont", ""))
				{
					bDiff = true;
					goto ProcessDayCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_PrintHeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_PrintHeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessDayCalFonts;
				}
			}
			//	sTemp=goTr:StrRead(sPage, "CALDAY_PrintMinutesFont", "",False)
			//	IF sTemp<>"" THEN
			//		IF sTemp<>goTr:StrRead(sDefs, "CALDAY_PrintMinutesFont", "",False) THEN
			//			bDiff=True
			//			GOTO ProcessDayCalFonts
			//		END
			//	END
			//	sTemp=goTr:StrRead(sPage, "CALDAY_PrintHoursFont", "",False)
			//	IF sTemp<>"" THEN
			//		IF sTemp<>goTr:StrRead(sDefs, "CALDAY_PrintHoursFont", "",False) THEN
			//			bDiff=True
			//			GOTO ProcessDayCalFonts
			//		END
			//	END
			sTemp = goTR.StrRead(sPage, "CALDAY_PrintTimeFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_PrintTimeFont", "", false))
				{
					bDiff = true;
					goto ProcessDayCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_PrintTextFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_PrintTextFont", ""))
				{
					bDiff = true;
					goto ProcessDayCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_HTMLTitleFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_HTMLTitleFont", "", false))
				{
					bDiff = true;
					goto ProcessDayCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_PrintHeaderFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_PrintHeaderFont", "", false))
				{
					bDiff = true;
					goto ProcessDayCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_PrintTitleFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_PrintTitleFont", "", false))
				{
					bDiff = true;
					goto ProcessDayCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_PrintFooterFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_PrintFooterFont", "", false))
				{
					bDiff = true;
					goto ProcessDayCalFonts;
				}
			}
	ProcessDayCalFonts:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "CALDAY_HTMLCellFont");
				goTR.StrDelete(ref sPage, "CALDAY_HTMLHeadingFont");
				goTR.StrDelete(ref sPage, "CALDAY_HTMLTimeFont");
				//	goTr:StrDelete(sPage,"CALDAY_HeadingFont")
				//	goTr:StrDelete(sPage,"CALDAY_TimeBarFont")
				goTR.StrDelete(ref sPage, "CALDAY_MinutesFont");
				goTR.StrDelete(ref sPage, "CALDAY_HoursFont");
				goTR.StrDelete(ref sPage, "CALDAY_TextFont");
				goTR.StrDelete(ref sPage, "CALDAY_PrintHeadingFont");
				//	goTr:StrDelete(sPage,"CALDAY_PrintMinutesFont")
				//	goTr:StrDelete(sPage,"CALDAY_PrintHoursFont")
				goTR.StrDelete(ref sPage, "CALDAY_PrintTimeFont");
				goTR.StrDelete(ref sPage, "CALDAY_PrintTextFont");
				goTR.StrDelete(ref sPage, "CALDAY_HTMLTitleFont");
				goTR.StrDelete(ref sPage, "CALDAY_PrintHeaderFont");
				goTR.StrDelete(ref sPage, "CALDAY_PrintTitleFont");
				goTR.StrDelete(ref sPage, "CALDAY_PrintFooterFont");
			}


			//----------------- CALWEEK -----------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "CALWEEK_HTMLHeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_HTMLHeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_HTMLCellFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_HTMLCellFont", ""))
				{
					bDiff = true;
					goto ProcessWeekCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_HeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_HeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_TextFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_TextFont", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_PrintHeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_PrintHeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_PrintTextFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_PrintTextFont", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_HTMLTitleFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_HTMLTitleFont", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_PrintHeaderFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_PrintHeaderFont", ""))
				{
					bDiff = true;
					goto ProcessWeekCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_PrintTitleFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_PrintTitleFont", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALWEEK_PrintFooterFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALWEEK_PrintFooterFont", "", false))
				{
					bDiff = true;
					goto ProcessWeekCalFonts;
				}
			}
	ProcessWeekCalFonts:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "CALWEEK_HTMLHeadingFont");
				goTR.StrDelete(ref sPage, "CALWEEK_HTMLCellFont");
				goTR.StrDelete(ref sPage, "CALWEEK_HeadingFont");
				goTR.StrDelete(ref sPage, "CALWEEK_TextFont");
				goTR.StrDelete(ref sPage, "CALWEEK_PrintHeadingFont");
				goTR.StrDelete(ref sPage, "CALWEEK_PrintTextFont");
				goTR.StrDelete(ref sPage, "CALWEEK_HTMLTitleFont");
				goTR.StrDelete(ref sPage, "CALWEEK_PrintHeaderFont");
				goTR.StrDelete(ref sPage, "CALWEEK_PrintTitleFont");
				goTR.StrDelete(ref sPage, "CALWEEK_PrintFooterFont");
			}


			//----------------- CALMONTH ----------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "CALMONTH_HTMLHeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_HTMLHeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_HTMLDateNumberFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_HTMLDateNumberFont", ""))
				{
					bDiff = true;
					goto ProcessMonthCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_HTMLCellFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_HTMLCellFont", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_HeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_HeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_DateNumberFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_DateNumberFont", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_TextFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_TextFont", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_PrintHeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_PrintHeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_PrintDateNumberFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_PrintDateNumberFont", ""))
				{
					bDiff = true;
					goto ProcessMonthCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_PrintTextFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_PrintTextFont", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_HTMLTitleFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_HTMLTitleFont", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_PrintHeaderFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_PrintHeaderFont", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_PrintTitleFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_PrintTitleFont", ""))
				{
					bDiff = true;
					goto ProcessMonthCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALMONTH_PrintFooterFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALMONTH_PrintFooterFont", "", false))
				{
					bDiff = true;
					goto ProcessMonthCalFonts;
				}
			}
	ProcessMonthCalFonts:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "CALMONTH_HTMLHeadingFont");
				goTR.StrDelete(ref sPage, "CALMONTH_HTMLDateNumberFont");
				goTR.StrDelete(ref sPage, "CALMONTH_HTMLCellFont");
				goTR.StrDelete(ref sPage, "CALMONTH_HeadingFont");
				goTR.StrDelete(ref sPage, "CALMONTH_DateNumberFont");
				goTR.StrDelete(ref sPage, "CALMONTH_TextFont");
				goTR.StrDelete(ref sPage, "CALMONTH_PrintHeadingFont");
				goTR.StrDelete(ref sPage, "CALMONTH_PrintDateNumberFont");
				goTR.StrDelete(ref sPage, "CALMONTH_PrintTextFont");
				goTR.StrDelete(ref sPage, "CALMONTH_HTMLTitleFont");
				goTR.StrDelete(ref sPage, "CALMONTH_PrintHeaderFont");
				goTR.StrDelete(ref sPage, "CALMONTH_PrintTitleFont");
				goTR.StrDelete(ref sPage, "CALMONTH_PrintFooterFont");
			}


			//------------------ CALYEAR ------------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "CALYEAR_HeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALYEAR_HeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessYearCalFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALYEAR_TextFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALYEAR_TextFont", ""))
				{
					bDiff = true;
					goto ProcessYearCalFonts;
				}
			}
	ProcessYearCalFonts:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "CALYEAR_HeadingFont");
				goTR.StrDelete(ref sPage, "CALYEAR_TextFont");
			}

			//------------------ MFIELD --------------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "MFIELD_HTMLCellFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "MFIELD_HTMLCellFont", "", false))
				{
					bDiff = true;
					goto ProcessMFieldFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "MFIELD_HTMLHeadingFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "MFIELD_HTMLHeadingFont", ""))
				{
					bDiff = true;
					goto ProcessMFieldFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "MFIELD_PrintTextFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "MFIELD_PrintTextFont", "", false))
				{
					bDiff = true;
					goto ProcessMFieldFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "MFIELD_PrintHeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "MFIELD_PrintHeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessMFieldFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "MFIELD_HTMLTitleFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "MFIELD_HTMLTitleFont", "", false))
				{
					bDiff = true;
					goto ProcessMFieldFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "MFIELD_PrintHeaderFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "MFIELD_PrintHeaderFont", "", false))
				{
					bDiff = true;
					goto ProcessMFieldFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "MFIELD_PrintTitleFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "MFIELD_PrintTitleFont", "", false))
				{
					bDiff = true;
					goto ProcessMFieldFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "MFIELD_PrintFooterFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "MFIELD_PrintFooterFont", ""))
				{
					bDiff = true;
					goto ProcessMFieldFonts;
				}
			}
	ProcessMFieldFonts:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "MFIELD_HTMLCellFont");
				goTR.StrDelete(ref sPage, "MFIELD_HTMLHeadingFont");
				goTR.StrDelete(ref sPage, "MFIELD_PrintTextFont");
				goTR.StrDelete(ref sPage, "MFIELD_PrintHeadingFont");
				goTR.StrDelete(ref sPage, "MFIELD_HTMLTitleFont");
				goTR.StrDelete(ref sPage, "MFIELD_PrintHeaderFont");
				goTR.StrDelete(ref sPage, "MFIELD_PrintTitleFont");
				goTR.StrDelete(ref sPage, "MFIELD_PrintFooterFont");
			}

			//------------------ RECORD --------------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "RECORD_HTMLCellFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "RECORD_HTMLCellFont", "", false))
				{
					bDiff = true;
					goto ProcessRecordFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "RECORD_HTMLHeadingFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "RECORD_HTMLHeadingFont", ""))
				{
					bDiff = true;
					goto ProcessRecordFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "RECORD_PrintTextFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "RECORD_PrintTextFont", "", false))
				{
					bDiff = true;
					goto ProcessRecordFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "RECORD_PrintHeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "RECORD_PrintHeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessRecordFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "RECORD_HTMLTitleFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "RECORD_HTMLTitleFont", "", false))
				{
					bDiff = true;
					goto ProcessRecordFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "RECORD_PrintHeaderFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "RECORD_PrintHeaderFont", "", false))
				{
					bDiff = true;
					goto ProcessRecordFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "RECORD_PrintTitleFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "RECORD_PrintTitleFont", "", false))
				{
					bDiff = true;
					goto ProcessRecordFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "RECORD_PrintFooterFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "RECORD_PrintFooterFont", ""))
				{
					bDiff = true;
					goto ProcessRecordFonts;
				}
			}
	ProcessRecordFonts:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "RECORD_HTMLCellFont");
				goTR.StrDelete(ref sPage, "RECORD_HTMLHeadingFont");
				goTR.StrDelete(ref sPage, "RECORD_PrintTextFont");
				goTR.StrDelete(ref sPage, "RECORD_PrintHeadingFont");
				goTR.StrDelete(ref sPage, "RECORD_HTMLTitleFont");
				goTR.StrDelete(ref sPage, "RECORD_PrintHeaderFont");
				goTR.StrDelete(ref sPage, "RECORD_PrintTitleFont");
				goTR.StrDelete(ref sPage, "RECORD_PrintFooterFont");
			}

			//----------------- SFIELD ------------------
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "SFIELD_HTMLCellFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "SFIELD_HTMLCellFont", "", false))
				{
					bDiff = true;
					goto ProcessSFieldFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "SFIELD_HTMLHeadingFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "SFIELD_HTMLHeadingFont", ""))
				{
					bDiff = true;
					goto ProcessSFieldFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "SFIELD_PrintTextFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "SFIELD_PrintTextFont", "", false))
				{
					bDiff = true;
					goto ProcessSFieldFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "SFIELD_PrintHeadingFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "SFIELD_PrintHeadingFont", "", false))
				{
					bDiff = true;
					goto ProcessSFieldFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "SFIELD_HTMLTitleFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "SFIELD_HTMLTitleFont", "", false))
				{
					bDiff = true;
					goto ProcessSFieldFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "SFIELD_PrintHeaderFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "SFIELD_PrintHeaderFont", "", false))
				{
					bDiff = true;
					goto ProcessSFieldFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "SFIELD_PrintTitleFont", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "SFIELD_PrintTitleFont", "", false))
				{
					bDiff = true;
					goto ProcessSFieldFonts;
				}
			}
			sTemp = goTR.StrRead(sPage, "SFIELD_PrintFooterFont", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "SFIELD_PrintFooterFont", ""))
				{
					bDiff = true;
					goto ProcessSFieldFonts;
				}
			}
	ProcessSFieldFonts:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "SFIELD_HTMLCellFont");
				goTR.StrDelete(ref sPage, "SFIELD_HTMLHeadingFont");
				goTR.StrDelete(ref sPage, "SFIELD_PrintTextFont");
				goTR.StrDelete(ref sPage, "SFIELD_PrintHeadingFont");
				goTR.StrDelete(ref sPage, "SFIELD_HTMLTitleFont");
				goTR.StrDelete(ref sPage, "SFIELD_PrintHeaderFont");
				goTR.StrDelete(ref sPage, "SFIELD_PrintTitleFont");
				goTR.StrDelete(ref sPage, "SFIELD_PrintFooterFont");
			}

			return (sPage);
		}
		public string CleanUpViewMetaFormt(string par_sPage, string par_sDefs)
		{
			//MI 8/2/07

			//Called from :CleanUpViewMeta, this cleans up Format metadata in Views
			//PARAMETERS:
			//		par_sPage: metadata page
			//		par_sDefs: Defaults
			//RETURNS:
			//		Cleaned up string

			//For debugging the order of calls.
			string sProc = "clDefaults::CleanUpViewMetaFormt";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sPage = par_sPage;
			bool bDiff = false;
			string sTemp = null;
			string sDefs = par_sDefs;

			//LIST
			//These settings can be deleted independent of one another
			if (goTR.StrRead(sPage, "LIST_USEHEADINGS", "", false) == goTR.StrRead(sDefs, "LIST_USEHEADINGS", "", false))
			{
				goTR.StrDelete(ref sPage, "LIST_USEHEADINGS");
			}
			if (goTR.StrRead(sPage, "LIST_ONELINEPERRECORD", "", false) == goTR.StrRead(sDefs, "LIST_ONELINEPERRECORD", "", false))
			{
				goTR.StrDelete(ref sPage, "LIST_ONELINEPERRECORD");
			}

			//CALDAY
			//These settings are deleted only if all are the same as defaults
			bDiff = false;
			sTemp = goTR.StrRead(sPage, "CALDAY_COMPRESS", "");
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_COMPRESS", ""))
				{
					bDiff = true;
					goto ProcessCalFormat;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_TIMEUNIT", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_TIMEUNIT", "", false))
				{
					bDiff = true;
					goto ProcessCalFormat;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_STARTTIME", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_STARTTIME", "", false))
				{
					bDiff = true;
					goto ProcessCalFormat;
				}
			}
			sTemp = goTR.StrRead(sPage, "CALDAY_DISPLAYTIMEBAR", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALDAY_DISPLAYTIMEBAR", "", false))
				{
					bDiff = true;
					goto ProcessCalFormat;
				}
			}
			//CALWEEK
			//CALMONTH
			//CALYEAR
			sTemp = goTR.StrRead(sPage, "CALYEAR_DISPLAYTYPE", "", false);
			if (sTemp != "")
			{
				if (sTemp != goTR.StrRead(sDefs, "CALYEAR_DISPLAYTYPE", "", false))
				{
					bDiff = true;
					goto ProcessCalFormat;
				}
			}
	ProcessCalFormat:
			if (!bDiff)
			{
				goTR.StrDelete(ref sPage, "CALDAY_COMPRESS");
				goTR.StrDelete(ref sPage, "CALDAY_TIMEUNIT");
				goTR.StrDelete(ref sPage, "CALDAY_STARTTIME");
				goTR.StrDelete(ref sPage, "CALDAY_DISPLAYTIMEBAR");
				goTR.StrDelete(ref sPage, "CALYEAR_DISPLAYTYPE");
			}

			return sPage;
		}
		public string CleanUpViewMetaPrn(string par_sPage, string par_sDefs)
		{
			//MI 9/5/06

			//Called from :CleanUpViewMeta, this cleans up Print/send metadata in Views
			//PARAMETERS:
			//		par_sPage: metadata page
			//		par_sDefs: Defaults
			//RETURNS:
			//		Cleaned up string

			string sProc = "clDefaults::CleanUpViewMetaPrn";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sPage = par_sPage;
			bool bDiff = false;
			string sTemp = null;
			int i = 0;
			string sDefs = par_sDefs;
			string sType = "";

			i = 1;
			for (i = 1; i <= 8; i++) //Number of supported View types
			{
				switch (i)
				{
					case 1:
						sType = "LIST_";
						break;
					case 2:
						sType = "CALDAY_";
						break;
					case 3:
						sType = "CALWEEK_";
						break;
					case 4:
						sType = "CALMONTH_";
						break;
					case 5:
						sType = "CALYEAR_";
						break;
					case 6:
						sType = "MFIELD_";
						break;
					case 7:
						sType = "RECORD_";
						break;
					case 8:
						sType = "SFIELD_";
						break;
				}
				bDiff = false;
				sTemp = goTR.StrRead(sPage, sType + "HEADERL", "", false);
				if (sTemp != "")
				{
					if (sTemp != goTR.StrRead(sDefs, sType + "HEADERL", "", false))
					{
						bDiff = true;
						goto ProcessPrintOptions;
					}
				}
				sTemp = goTR.StrRead(sPage, sType + "HEADERC", "");
				if (sTemp != "")
				{
					if (sTemp != goTR.StrRead(sDefs, sType + "HEADERC", ""))
					{
						bDiff = true;
						goto ProcessPrintOptions;
					}
				}
				sTemp = goTR.StrRead(sPage, sType + "HEADERR", "", false);
				if (sTemp != "")
				{
					if (sTemp != goTR.StrRead(sDefs, sType + "HEADERR", "", false))
					{
						bDiff = true;
						goto ProcessPrintOptions;
					}
				}
				sTemp = goTR.StrRead(sPage, sType + "TITLE", "", false);
				if (sTemp != "")
				{
					if (sTemp != goTR.StrRead(sDefs, sType + "TITLE", "", false))
					{
						bDiff = true;
						goto ProcessPrintOptions;
					}
				}
				sTemp = goTR.StrRead(sPage, sType + "FOOTERL", "", false);
				if (sTemp != "")
				{
					if (sTemp != goTR.StrRead(sDefs, sType + "FOOTERL", "", false))
					{
						bDiff = true;
						goto ProcessPrintOptions;
					}
				}
				sTemp = goTR.StrRead(sPage, sType + "FOOTERC", "", false);
				if (sTemp != "")
				{
					if (sTemp != goTR.StrRead(sDefs, sType + "FOOTERC", "", false))
					{
						bDiff = true;
						goto ProcessPrintOptions;
					}
				}
				sTemp = goTR.StrRead(sPage, sType + "FOOTERR", "", false);
				if (sTemp != "")
				{
					if (sTemp != goTR.StrRead(sDefs, sType + "FOOTERR", "", false))
					{
						bDiff = true;
						goto ProcessPrintOptions;
					}
				}
				sTemp = goTR.StrRead(sPage, sType + "TITLESEND", "");
				if (sTemp != "")
				{
					if (sTemp != goTR.StrRead(sDefs, sType + "TITLESEND", ""))
					{
						bDiff = true;
						goto ProcessPrintOptions;
					}
				}
				sTemp = goTR.StrRead(sPage, sType + "PAGELAYOUT", "", false);
				if (sTemp != "")
				{
					if (sTemp != goTR.StrRead(sDefs, sType + "PAGELAYOUT", "", false))
					{
						bDiff = true;
						goto ProcessPrintOptions;
					}
				}
				sTemp = goTR.StrRead(sPage, sType + "SENDTEMPLHTML", "");
				if (sTemp != "")
				{
					if (sTemp != goTR.StrRead(sDefs, sType + "SENDTEMPLHTML", ""))
					{
						bDiff = true;
						goto ProcessPrintOptions;
					}
				}
	ProcessPrintOptions:
				if (!bDiff)
				{
					goTR.StrDelete(ref sPage, sType + "HEADERL");
					goTR.StrDelete(ref sPage, sType + "HEADERC");
					goTR.StrDelete(ref sPage, sType + "HEADERR");
					goTR.StrDelete(ref sPage, sType + "TITLE");
					goTR.StrDelete(ref sPage, sType + "FOOTERL");
					goTR.StrDelete(ref sPage, sType + "FOOTERC");
					goTR.StrDelete(ref sPage, sType + "FOOTERR");
					goTR.StrDelete(ref sPage, sType + "TITLESEND");
					goTR.StrDelete(ref sPage, sType + "PAGELAYOUT");
					goTR.StrDelete(ref sPage, sType + "SENDTEMPLHTML");
				}
			}

			return sPage;

		}
		public string GenerateProcName(string par_sName)
		{
			//CS OK

			//PURPOSE:
			//		Generate the name of the svbcrlfipt that appears on the PROCEDURE line
			//		and in PROCNAME property in metadata.
			//PARAMETERS:
			//		par_sName: name of the procedure

			//Dim sProc As String = FenEnExecution() & ".clDefaults::GenerateProcName"
			//IF gbWriteLog THEN oLog is clLogObj(sProc, "Start" , SELL_LOGLEVEL_DEBUG)

			string sName = goTR.StripIllegalChars(par_sName);

			return (sName);
		}
		public string GetAutActionLabel(string par_sString, int par_iActionNo)
		{
			//MI 3/18/09 Commented goErr.SetError().
			//CS Need to test

			//---------
			//SET ERROR
			//---------
			//PURPOSE:
			//		Get a label of a particular action from a complete automator metadata string.
			//PARAMETERS:
			//		par_sString: Metadata string containing the action.
			//		par_iActionNo: action number that's unique within an automator.
			//RETURNS:
			//		String.

			//For debugging the order of calls.
			string sProc = "clDefaults::GetAutActionLabel";
			//Dim oLog As New clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//goLog.SetError()       'MI 3/18/09 commented

			string sWork = null;
			string sNum = par_iActionNo.ToString();
			string sString = par_sString;
			string sResult = null;

			string sType = goTR.StrRead(sString, "A_" + sNum + "_TYPE", "", false);
			switch (sType)
			{
				case "ALERT":
					sResult = GetAutActionTypeLbl(sType);
					break;
				case "CREATERECORD":
					sWork = goTR.StrRead(sString, "A_" + sNum + "_FILE", "", false);
					if (sWork == "")
					{
						//sResult = goTR : MessComplete(MessTraduit(5206), MessTraduit(5205)) 'MessTranslate
						sResult = goTR.MessComplete("Create new [1]", "record");
						//5206:vbcrlfeate new [1]
						//5205:record
					}
					else
					{
						//sResult = goTr:MessComplete(MessTraduit(5206),goData:GetFileLabelFromName(sWork)) 'MessTranslate
						sResult = goTR.MessComplete("Create new [1]", goData.GetFileLabelFromName(sWork));
						//5206:vbcrlfeate new [1]
					}
					break;
				case "EDITRECORD":
					//sResult = goTr:MessComplete(MessTraduit(5207),goTr:StrRead(sString,"A_"&sNum&"_FILE","",False)) 'MessTranslate
					sResult = goTR.MessComplete("Edit [1]", goTR.StrRead(sString, "A_" + sNum + "_FILE", "", false));
					break;
					//5207:Edit [1]
				case "MESSAGE":
					sResult = GetAutActionTypeLbl(sType);
					break;
				case "DOCUMENT":
					//sResult = goTr:MessComplete(MessTraduit(5208),goTr:StrRead(sString,"A_"&sNum&"_EXECUTE","",False)) 'MessTranslate
					sResult = goTR.MessComplete("Open '[1]'", goTR.StrRead(sString, "A_" + sNum + "_EXECUTE", "", false));
					break;
					//5208:Open '[1]'
				case "AUTOMATOR":
					sWork = goTR.StrRead(sString, "A_" + sNum + "_EXECUTE", "", false);
					sWork = goMeta.PageRead(goP.GetUserTID(), sWork);
					sWork = goTR.StrRead(sWork, "NAME");
					//sResult = goTr:MessComplete(MessTraduit(5209),:GetAutActionTypeLbl(sType),sWork) 'MessTranslate
					sResult = goTR.MessComplete("[1] '[2]'", GetAutActionTypeLbl(sType), sWork);
					break;
					//5209:[1] '[2]'
				case "PROGRAM": //7
					//sResult = goTr:MessComplete(MessTraduit(5209),:GetAutActionTypeLbl(sType),goTr:StrRead(sString,"A_"&sNum&"_EXECUTE","",False)) 'MessTranslate
					sResult = goTR.MessComplete("[1] '[2]'", GetAutActionTypeLbl(sType), goTR.StrRead(sString, "A_" + sNum + "_EXECUTE", "", false));
					break;
					//5209:[1] '[2]'
				case "SCRIPT": //9
					sWork = goTR.StrRead(sString, "A_" + sNum + "_EXECUTE", "", false);
					sWork = goMeta.PageRead(goP.GetUserTID(), sWork);
					sWork = goTR.StrRead(sWork, "NAME");
					//sResult = goTr:MessComplete(MessTraduit(5209),:GetAutActionTypeLbl(sType),sWork) 'MessTranslate
					sResult = goTR.MessComplete("[1] '[2]'", GetAutActionTypeLbl(sType), sWork);
					break;
					//5209:[1] '[2]'
				case "DELETERECORD":
					//sResult = goTR : MessComplete(MessTraduit(5211), "") 'MessTranslate
					sResult = goTR.MessComplete("Delete '[1]' record", "");
					break;
					//5211:Delete '[1]' record
				case "OPENDESKTOP":
					//sResult = goTr:MessComplete(MessTraduit(5209),:GetAutActionTypeLbl(sType),"") 'MessTranslate
					sResult = goTR.MessComplete("[1] '[2]'", GetAutActionTypeLbl(sType), "");
					break;
					//5209:[1] '[2]'
				case "SENDDDE":
					//sResult = goTr:MessComplete(MessTraduit(5209),:GetAutActionTypeLbl(sType),"") 'MessTranslate
					sResult = goTR.MessComplete("[1] '[2]'", GetAutActionTypeLbl(sType), "");
					break;
					//5209:[1] '[2]'
				case "BACKUP":
					//sResult = goTr:MessComplete(MessTraduit(5210),:GetAutActionTypeLbl(sType),"")'MessTranslate
					sResult = goTR.MessComplete("[1] to '[2]'", GetAutActionTypeLbl(sType), "");
					break;
					//5210:[1] to '[2]'
				case "IMPORT":
					//sResult = goTr:MessComplete(MessTraduit(5209),:GetAutActionTypeLbl(sType),"") 'MessTranslate
					sResult = goTR.MessComplete("[1] '[2]'", GetAutActionTypeLbl(sType), "");
					break;
					//5209:[1] '[2]'
				case "EXPORT":
					//sResult = goTr:MessComplete(MessTraduit(5210),:GetAutActionTypeLbl(sType),"") 'MessTranslate
					sResult = goTR.MessComplete("[1] to '[2]'", GetAutActionTypeLbl(sType), "");
					break;
					//5210:[1] to '[2]'
				case "TRANSFERIN": //16
					//sResult = goTr:MessComplete(MessTraduit(5209),:GetAutActionTypeLbl(sType),"") 'MessTranslate
					sResult = goTR.MessComplete("[1] '[2]'", GetAutActionTypeLbl(sType), "");
					break;
					//5209:[1] '[2]'
				case "TRANSFEROUT": //17
					//sResult = goTr:MessComplete(MessTraduit(5210),:GetAutActionTypeLbl(sType),"") 'MessTranslate
					sResult = goTR.MessComplete("[1] to '[2]'", GetAutActionTypeLbl(sType), "");
					break;
					//5210:[1] to '[2]'
				default:
					//Unsupported action type
					sResult = "";
					goLog.SetError(10103, sProc, "", "sType]", sProc, sType);
					break;
					// Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
					//
					//Please contact Selltis support.
			}

			return (sResult);
		}
		public string GetAutActionProps(string par_sType)
		{
			//OK

			//PURPOSE:
			//		Get automator action property words, either for all types of actions
			//		or for a particular action type. A "property word" is the last part of
			//		each property, after the language code and action number. For example,
			//		for the property "A_03_MSGTYPE", this method returns "MSGTYPE". The purpose
			//		of this method is to allow deleting or duplicating all properties of a
			//		particular action or all actions in a generic way.
			//PARAMETERS:
			//		par_sType: string: Action type keyword, all uppercase.
			//RETURNS:
			//		String that contains a list of property words delimited with "|".
			//		A trailing "|" is returned at the end of the list.
			//		Words that can be translated are prefixed with "XX_". To delete
			//		all such words, replace "XX_" with each language code returned by
			//		goP:GetSupportedLangs() followed by "_".

			//For debugging the order of calls.
			string sProc = "clDefaults::GetAutActionTypeLbl";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sResult = "";
			int i = 0;

			switch (par_sType)
			{
				case "ALERT":
				break;

				case "CREATERECORD":
				break;

				case "EDITRECORD":
				break;

				case "MESSAGE":
					sResult += "MESSAGE|";
					sResult += "MSGTYPE|";
					sResult += "XX_YESLABEL|";
					sResult += "XX_NOLABEL|";
					sResult += "XX_OKLABEL|";
					sResult += "BEEP|";
					sResult += "ORDER|";
					sResult += "TYPE|";
					sResult += "XX_ACTIONLABEL|";
					sResult += "OBJSHARED|";
					break;
				case "DOCUMENT":
					sResult += "EXECUTE|";
					break;
				case "AUTOMATOR":
					for (i = 1; i <= 10; i++)
					{
						sResult += "PARAM_" + i.ToString() + "|";
					}
					sResult += "EXECUTE|";
					sResult += "OBJSHARED|";
					break;
				case "PROGRAM": //7
					sResult += "EXECUTE|";
					break;
				case "SCRIPT": //9
					sResult += "EXECUTE|";
					sResult += "OBJSHARED|";
					break;
				case "DELETERECORD":
				break;

				case "OPENDESKTOP":
				break;

				case "SENDDDE":
				break;

				case "BACKUP":
				break;

				case "IMPORT":
				break;

				case "EXPORT":
				break;

				case "TRANSFERIN": //16
				break;

				case "TRANSFEROUT": //17
				break;

				default:
				break;
					//Return all properties
					//==> Finish!

			}

			return sResult;
		}
		public string GetAutActionTypeLbl(string par_sType)
		{
			//CS Need to test

			//PURPOSE:
			//		Return a friendly label of an automator action type
			//PARAMETERS:
			//		par_sType: string: type keyword. For a list of supported
			//			keywords, see WAGEACT window.
			//RETURNS:
			//		String containing the label in the current language.

			//For debugging the order of calls.
			string sProc = "clDefaults::GetAutActionTypeLbl";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sResult = null;

			switch (par_sType)
			{
				case "ALERT":
					sResult = "Alert"; //MessTranslate MessTraduit(5180) '5180:Alert
					break;
				case "CREATERECORD":
					sResult = "vCreate New Record"; //MessTranslate MessTraduit(5181) '5181:vbcrlfeate New Record
					break;
				case "EDITRECORD":
					sResult = "Edit Record(s)"; //MessTranslate MessTraduit(5182) '5182:Edit Record(s)
					break;
				case "MESSAGE":
					sResult = "Display Message"; //MessTranslate MessTraduit(5183) '5183:Display Message
					break;
				case "DOCUMENT":
					sResult = "Open Document or URL"; //MessTranslate MessTraduit(5184) '5184:Open Document or URL
					break;
				case "AUTOMATOR":
					sResult = "Run Automator"; //MessTranslate MessTraduit(5185) '5185:Run Automator
					break;
				case "PROGRAM":
					sResult = "Run Program"; //MessTranslateMessTraduit(5186) '5186:Run Program
					break;
				case "SCRIPT":
					sResult = "Run Script"; //MessTranslate MessTraduit(5187) '5187:Run Svbcrlfipt
					break;
				case "DELETERECORD":
					sResult = "Delete Record"; //MessTranslate MessTraduit(5188) '5188:Delete Record
					break;
				case "OPENDESKTOP":
					sResult = "Open Desktop"; //MessTranslate MessTraduit(5189) '5189:Open Desktop
					break;
				case "SENDDDE":
					sResult = "Send DDE"; //MessTranslate MessTraduit(5190) '5190:Send DDE
					break;
				case "BACKUP":
					sResult = "Backup"; //MessTranslate '5191:Backup
					break;
				case "IMPORT":
					sResult = "Import"; //MessTranslate MessTraduit(5192) '5192:Import
					break;
				case "EXPORT":
					sResult = "Export"; //MessTranslate MessTraduit(5193) '5193:Export
					break;
				case "TRANSFERIN":
					sResult = " Transfer In"; //MessTranslate MessTraduit(5194) '5194:Transfer In
					break;
				case "TRANSFEROUT":
					sResult = "Transfer Out"; //MessTranslate: MessTraduit(5195) '5195:Transfer Out
					break;
				default:
					//Unsupported action type
					sResult = "";
					break;
			}

			return sResult;
		}
		public string GetAutEventTypeLbl(string par_sType)
		{
			//CS OK

			//PURPOSE:
			//		Return a friendly label of an automator event type
			//PARAMETERS:
			//		par_sType: string: type keyword. For a list of supported
			//			keywords, see WAGEPRO window.
			//RETURNS:
			//		String containing the label in the current language.

			//For debugging the order of calls.
			string sProc = "clDefaults::GetAutEventTypeLbl";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sResult = null;

			switch (par_sType)
			{
				case "ALARM":
					sResult = "Alarm"; //messtranslate MessTraduit(5196) '5196:Alarm
					break;
				case "AUTOMATOR":
					sResult = "Call from Automator"; //messtranslate MessTraduit(5197) '5197:Call from Automator
					break;
				case "BUTTON":
					sResult = "Button Click"; //messtranslate MessTraduit(5198) '5198:Button Click
					break;
				case "DDE":
					sResult = "DDE Received"; //messtranslate MessTraduit(5199) '5199:DDE Received
					break;
				case "DESKTOPOPEN":
					sResult = "Desktop Opened"; //messtranslate MessTraduit(5200) '5200:Desktop Opened
					break;
				case "FORMOPEN":
					sResult = "Form Opened"; //messtranslate MessTraduit(5201) '5201:Form Opened
					break;
				case "RECORDDELETE":
					sResult = "Record Deleted"; //messtranslate MessTraduit(5202) '5202:Record Deleted
					break;
				case "RECORDSAVE":
					sResult = "Record Saved"; //messtranslate MessTraduit(5203) '5203:Record Saved
					break;
				case "SCRIPT":
					sResult = "Call from Script"; //messtranslate MessTraduit(5233) '5233:Call from Svbcrlfipt
					break;
				case "TIMER":
					sResult = "Timer"; //Messtranslate MessTraduit(5204) '5204:Timer
					break;
				default:
					//Unsupported action type
					sResult = "";
					break;
			}

			return sResult;
		}

		public string GetConditionLabel(string par_sFieldType, string par_sSymbol)
		{
			//MI 11/15/12 Added ~ and <~> (Is Like and Is Not Like, for wildcard searches)
			//MI 9/22/09 Relabeled LNK cases 3 and 13 to Is not Blank/Is Blank.
			//MI 9/1/09 Added support for Link conditions 3 and 13.
			//MI 9/13/07 Added link cases 10 and 11  
			//MI 8/2/07 Added support for link Condition "2"
			//MI 11/24/06 Added support for MEMO
			//MI 10/27/06
			//MI 10/18/06

			//PURPOSE:
			//       Return the label for the supplied filter condition symbol depending on the 
			//       field type.
			//PARAMETERS:
			//		par_sFieldType: high-level type of field used in a single filter condition.
			//			Supported values:
			//				NUMBER
			//               MEMO    '*** MI 11/24/06
			//				TEXT
			//               DATETIME
			//				DATE
			//				TIME
			//				TELEPHONE [not supported currently]
			//				CHECKBOX
			//				LIST
			//				LINK
			//		par_sSymbol: condition symbol. For a complete list of supported
			//			symbols, see comments on CONDITION parameter in the window
			//			opening code of window -> WFILTCND <- (press F2 over WFILTCND).
			//IMPORTANT:
			//		Some MessTraduit messages are shared among multiple cases. Be careful
			//		when editing them!

			string sProc = "clDefaults::GetConditionLabel";

			string sResult = ""; //"???"

			switch (par_sFieldType)
			{
				case "NUMBER":
					switch (par_sSymbol)
					{
						case "=":
							sResult = "Is Equal To"; //messtranslate MessTraduit(5077) '5077:Is Equal To
							break;
						case "<>":
							sResult = "Is Not Equal To"; //messtranslate MessTraduit(5078) '5078:Is Not Equal To
							break;
						case ">":
							sResult = "Is Greater Than"; //messtranslate MessTraduit(5079) '5079:Is Greater Than
							break;
						case ">=":
							sResult = "Is Greater Than Or Equal To"; //messtranslate MessTraduit(5087) '5087:Is Greater Than Or Equal To
							break;
						case "<":
							sResult = "Is Less Than"; //messtranslate MessTraduit(5088) '5088:Is Less Than
							break;
						case "<=":
							sResult = "Is Less Than Or Equal To"; //messtranslate MessTraduit(5089) '5089:Is Less Than Or Equal To
							break;
						case "][":
							sResult = "Is Between"; //messtranslate MessTraduit(5097) '5097:Is Between
							break;
					}
					break;

				case "TEXT":
				case "TELEPHONE":
				case "MEMO":
					switch (par_sSymbol)
					{
						case "[=":
							sResult = "Starts With"; //messtranslate MessTraduit(5098) '5098:Starts With
							break;
						case "[<>":
							sResult = "Does Not Start With"; //messtranslate MessTraduit(5099) '5099:Does Not Start With
							break;
						case "[":
							sResult = "Contains"; //messtranslate MessTraduit(5100) '5100:Contains
							break;
						case "<[>":
							sResult = "Does Not Contain"; //messtranslate MessTraduit(5101) '5101:Does Not Contain
							break;
						case "~":
							sResult = "Is Like";
							break;
						case "<~>":
							sResult = "Is Not Like";
							break;
						case "=":
							sResult = "Is Equal To"; //messtranlsate MessTraduit(5102) '5102:Is Equal To
							break;
						case "<>":
							sResult = "Is Not Equal To"; //messtranslate MessTraduit(5103) '5103:Is Not Equal To
							break;
						case ">":
							sResult = "Is Greater Than"; //messtranslate MessTraduit(5079) '5079:Is Greater Than
							break;
						case ">=":
							sResult = "Is Greater Than Or Equal To"; //messtranslate MessTraduit(5087) '5087:Is Greater Than Or Equal To
							break;
						case "<":
							sResult = "Is Less Than"; //messtranslate MessTraduit(5088) '5088:Is Less Than
							break;
						case "<=":
							sResult = "Is Less Than Or Equal To"; //messtranslate MessTraduit(5089) '5089:Is Less Than Or Equal To
							break;
						case "][":
							sResult = "Is Between"; //messtranslate MessTraduit(5104) '5104:Is Between
							break;
						case "=]":
							sResult = "Ends With"; //messtranslate MessTraduit(5116) '5116:Ends With
							break;
						case "<>]":
							sResult = "Does Not End With"; //messtranslate MessTraduit(5117) '5117:Does Not End With
							break;
						case "[]":
							sResult = "Is Blank"; //messtranslate MessTraduit(5105) '5105:Is Blank
							break;
						case "<[]>":
							sResult = "Is Not Blank"; //messtranslate MessTraduit(5106) '5106:Is Not Blank
							break;
					}
					break;

				case "DATETIME":
					switch (par_sSymbol)
					{
						case ">":
							sResult = "Is After"; //messtranslate MessTraduit(5107) '5107:Is After
							break;
						case ">=":
							sResult = "Is After Or At"; //messtranslate MessTraduit(5165) '5165:Is After Or On
							break;
						case "<":
							sResult = "Is Before"; //messtranslate MessTraduit(5108) '5108:Is Before
							break;
						case "<=":
							sResult = "Is Before Or At"; //messtranslate MessTraduit(5166) '5166:Is Before Or On
							break;
						case "][":
							sResult = "Is Between"; //messtranslate MessTraduit(5109) '5109:Is Between
							break;
						case "=":
							sResult = "Is At"; //messtranslate MessTraduit(5110) '5110:Is On
							break;
						case "<>":
							sResult = "Is Not At"; //messtranslate MessTraduit(5111) '5111:Is Not On
							break;
						case "[]":
							sResult = "Is Blank"; //messtranslate MessTraduit(5112) '5112:Is Blank
							break;
						case "<[]>":
							sResult = "Is Not Blank"; //messtranslate MessTraduit(5113) '5113:Is Not Blank
							break;
					}
					break;


				case "DATE":
					switch (par_sSymbol)
					{
						case ">":
							sResult = "Is After"; //messtranslate MessTraduit(5107) '5107:Is After
							break;
						case ">=":
							sResult = "Is After Or On"; //messtranslate MessTraduit(5165) '5165:Is After Or On
							break;
						case "<":
							sResult = "Is Before"; //messtranslate MessTraduit(5108) '5108:Is Before
							break;
						case "<=":
							sResult = "Is Before Or On"; //messtranslate MessTraduit(5166) '5166:Is Before Or On
							break;
						case "][":
							sResult = "Is Between"; //messtranslate MessTraduit(5109) '5109:Is Between
							break;
						case "=":
							sResult = "Is On"; //messtranslate MessTraduit(5110) '5110:Is On
							break;
						case "<>":
							sResult = "Is Not On"; //messtranslate MessTraduit(5111) '5111:Is Not On
							break;
						case "[]":
							sResult = "Is Blank"; //messtranslate MessTraduit(5112) '5112:Is Blank
							break;
						case "<[]>":
							sResult = "Is Not Blank"; //messtranslate MessTraduit(5113) '5113:Is Not Blank
							break;
					}
					break;

				case "TIME":
					//MessTraduit messages shared with CASE "DATE"!
					switch (par_sSymbol)
					{
						case ">":
							sResult = "Is After"; //messtranslate MessTraduit(5107) '5107:Is After
							break;
						case ">=":
							sResult = "Is After Or At"; //messtranslate MessTraduit(5167) '5167:Is After Or At
							break;
						case "<":
							sResult = "Is Before"; //messtranslate MessTraduit(5108) '5108:Is Before
							break;
						case "<=":
							sResult = "Is Before Or At"; //messtranslate MessTraduit(5168) '5168:Is Before Or At
							break;
						case "][":
							sResult = "Is Between"; //messtranslate MessTraduit(5109) '5109:Is Between
							break;
						case "=":
							sResult = "Is At"; //messtranslate MessTraduit(5114) '5114:Is At
							break;
						case "<>":
							sResult = "Is Not At"; //messtranslate MessTraduit(5115) '5115:Is Not At
							break;
						case "[]":
							sResult = "Is Blank"; //Messtranslate MessTraduit(5112) '5112:Is Blank
							break;
						case "<[]>":
							sResult = "Is Not Blank"; //messtranslate MessTraduit(5113) '5113:Is Not Blank
							break;
					}
					break;

				case "CHECKBOX":
					switch (par_sSymbol)
					{
						case "1":
							sResult = "Is Checked"; //messtranslate MessTraduit(5000) '5000:Is Checked
							break;
						case "0":
							sResult = "Is Not Checked"; //messtranslate MessTraduit(5120) '5120:Is Not Checked
							break;
					}
					break;
				case "LIST":
					switch (par_sSymbol)
					{
						case "=":
							sResult = "Is"; //messtranslate MessTraduit(5118) '5118:Is
							break;
						case "<>":
							sResult = "Is Not"; //messtranslate MessTraduit(5119) '5119:Is Not
							break;
							//Should we support this? Makes sense for power users who understand
							//how list index values work, but not for ordinary users who will think
							//that < and > pertain to alphabetical order of item labels they see.
							//Case ">"
							//    sResult = "Is Greater Than" 'messtranslate MessTraduit(5079)   '5079:Is Greater Than
							//Case ">="
							//    sResult = "Is Greater Than Or Equal To" 'messtranslate MessTraduit(5087)   '5087:Is Greater Than Or Equal To
							//Case "<"
							//    sResult = "Is Less Than" 'messtranslate MessTraduit(5088)   '5088:Is Less Than
							//Case "<="
							//    sResult = "Is Less Than Or Equal To" 'messtranslate MessTraduit(5089)   '5089:Is Less Than Or Equal To
							//Case "]["
							//    sResult = "Is Between" 'messtranslate MessTraduit(5097)   '5097:Is Between
					}
					break;

				case "LINK":
					switch (par_sSymbol)
					{
						case "1":
							sResult = "Is Selected Record in a Linked File's View"; //messtranslate MessTraduit(5121) '5121:Selected Record in a Linked File's View
							break;
						case "11":
							sResult = "Is not Selected Record in a Linked File's View"; //messtranslate
							break;
						case "2":
							sResult = "Is Selected Record in a Linked File's View in Focus"; //messtranslate
							break;
						case "3":
							sResult = "Is not Blank"; //messtranslate
							break;
						case "13":
							sResult = "Is Blank"; //messtranslate
							break;
						case "0":
							sResult = "Is"; //messtranslate MessTraduit(5122) '5122:Is
							break;
						case "10":
							sResult = "Is not"; //messtranslate
							break;
					}
					break;

					//Case "ALL"
					//    sResult = ""

				default:
					sResult = ""; //"???"
					break;
			}

			if (sResult == "???")
			{
				goErr.SetError(35000, sProc, "Field type '" + par_sFieldType + "' doesn't support operator '" + par_sSymbol + "'.");
			}

			return sResult;
		}
		public string GetConditionList(string par_sFieldType, bool par_bLinkField = false)
		{
			//MI 11/15/12 Added ~ and <~> (is like and is not like)
			//MI 9/1/09 Disabled Is Blank for link fields because this creates the IS NULL test, which causes all recs to be returned due to LEFT JOINs.
			//MI 6/5/07 Removed extra vbcrlfs at ends of lists.
			//MI 1/17/07 Added par_bLinkField parameter.
			//MI 11/24/06 Added case MEMO for text and ntext fields like URL, MMO, FIL, etc.
			//MI 10/27/06

			//PURPOSE:
			//       Return a vbcrlf-delimited list of condition symbols appropriate to
			//       use in a filter condition for the field type in par_sFieldType.
			//PARAMETERS:
			//		par_sFieldType: high-level type of field used in a single filter condition.
			//			Supported values:
			//				NUMBER
			//               MEMO    '*** MI 11/24/06
			//				TEXT
			//               DATETIME
			//				DATE
			//				TIME
			//				TELEPHONE [not supported currently]
			//       par_bLinkField: When true, exclusion conditions are not included. Default: false.
			//RETURNS:
			//		vbcrlf_delimited list of condition symbols in the order in which
			//			they should be presented to the user (e.g. in WFILTCND window,
			//			CMB_CONDITION combo). There is no final vbcrlf after the last value.
			//			Use ExtractString(sString,<n>,vbcrlf) to process the list in a loop.

			//For debugging the order of calls.
			string sProc = "clDefaults::GetConditionList";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string s = "";

			switch (par_sFieldType)
			{
				case "NUMBER":
					if (par_bLinkField)
					{
						s += "=" + "\r\n"; //2025:Is Equal To
						s += ">" + "\r\n"; //2027:Is Greater Than
						s += ">=" + "\r\n"; //2028:Is Greater Than Or Equal To
						s += "<" + "\r\n"; //2029:Is Less Than
						s += "<=" + "\r\n"; //2030:Is Less Than Or Equal To
						s += "]["; //2031:Is Between
					}
					else
					{
						s += "=" + "\r\n"; //2025:Is Equal To
						s += "<>" + "\r\n"; //2026:Is Not Equal To
						s += ">" + "\r\n"; //2027:Is Greater Than
						s += ">=" + "\r\n"; //2028:Is Greater Than Or Equal To
						s += "<" + "\r\n"; //2029:Is Less Than
						s += "<=" + "\r\n"; //2030:Is Less Than Or Equal To
						s += "]["; //2031:Is Between
					}
					break;

				case "TEXT":
					if (par_bLinkField)
					{
						s += "[=" + "\r\n"; //2032:Starts With
						s += "[" + "\r\n"; //2034:Contains
						s += "~" + "\r\n"; //Is Like
						s += "=" + "\r\n"; //2025:Is Equal To
						s += "][" + "\r\n"; //2031:Is Between
						s += "=]"; //20474:Ends With
						//s &= "[]" & vbCrLf   '2036:Is Blank     'MI 9/1/09 disabling because IS NULL causes all recs to be returned due to LEFT JOIN.
					}
					else
					{
						s += "[=" + "\r\n"; //2032:Starts With
						s += "[<>" + "\r\n"; //2033:Does Not Start With
						s += "[" + "\r\n"; //2034:Contains
						s += "<[>" + "\r\n"; //2035:Does Not Contain
						s += "~" + "\r\n"; //Is Like
						s += "<~>" + "\r\n"; //Is Not Like
						s += "=" + "\r\n"; //2025:Is Equal To
						s += "<>" + "\r\n"; //2026:Is Not Equal To
						s += "][" + "\r\n"; //2031:Is Between
						s += "=]" + "\r\n"; //20474:Ends With
						s += "<>]" + "\r\n"; //2048:Does Not End With
						s += "[]" + "\r\n"; //2036:Is Blank
						s += "<[]>"; //2037:Is Not Blank
					}
					break;

				case "MEMO":
					if (par_bLinkField)
					{
						s += "[=" + "\r\n"; //2032:Starts With
						s += "[" + "\r\n"; //2034:Contains
						s += "~" + "\r\n"; //Is Like
						s += "=" + "\r\n"; //2025:Is Equal To
						s += "=]"; //20474:Ends With
						//s &= "[]" & vbCrLf   '2036:Is Blank     'MI 9/1/09 disabling because IS NULL causes all recs to be returned due to LEFT JOIN.
					}
					else
					{
						s += "[=" + "\r\n"; //2032:Starts With
						s += "[<>" + "\r\n"; //2033:Does Not Start With
						s += "[" + "\r\n"; //2034:Contains
						s += "<[>" + "\r\n"; //2035:Does Not Contain
						s += "~" + "\r\n"; //Is Like
						s += "<~>" + "\r\n"; //Is Not Like
						s += "=" + "\r\n"; //2025:Is Equal To
						s += "<>" + "\r\n"; //2026:Is Not Equal To
						s += "=]" + "\r\n"; //20474:Ends With
						s += "<>]" + "\r\n"; //2048:Does Not End With
						s += "[]" + "\r\n"; //2036:Is Blank
						s += "<[]>"; //2037:Is Not Blank
					}
					break;

				case "DATETIME":
					if (par_bLinkField)
					{
						s += ">" + "\r\n"; //2038:Is After
						s += ">=" + "\r\n"; //Is After Or On
						s += "<" + "\r\n"; //2039:Is Before
						s += "<=" + "\r\n"; //Is Before Or On
						s += "][" + "\r\n"; //2031:Is Between
						s += "=" + "\r\n"; //2040:Is
						//s &= "[]"   '2036:Is Blank     'MI 9/1/09 disabling because IS NULL causes all recs to be returned due to LEFT JOIN.
					}
					else
					{
						s += ">" + "\r\n"; //2038:Is After
						s += ">=" + "\r\n"; //Is After Or On
						s += "<" + "\r\n"; //2039:Is Before
						s += "<=" + "\r\n"; //Is Before Or On
						s += "][" + "\r\n"; //2031:Is Between
						s += "=" + "\r\n"; //2040:Is
						s += "<>" + "\r\n"; //2041:Is Not
						s += "[]" + "\r\n"; //2036:Is Blank
						s += "<[]>"; //2037:Is Not Blank
					}
					break;

				case "DATE":
					if (par_bLinkField)
					{
						s += ">" + "\r\n"; //2038:Is After
						s += ">=" + "\r\n"; //Is After Or On
						s += "<" + "\r\n"; //2039:Is Before
						s += "<=" + "\r\n"; //Is Before Or On
						s += "][" + "\r\n"; //2031:Is Between
						s += "=" + "\r\n"; //2040:Is
						//s &= "[]"   '2036:Is Blank     'MI 9/1/09 disabling because IS NULL causes all recs to be returned due to LEFT JOIN.
					}
					else
					{
						s += ">" + "\r\n"; //2038:Is After
						s += ">=" + "\r\n"; //Is After Or On
						s += "<" + "\r\n"; //2039:Is Before
						s += "<=" + "\r\n"; //Is Before Or On
						s += "][" + "\r\n"; //2031:Is Between
						s += "=" + "\r\n"; //2040:Is
						s += "<>" + "\r\n"; //2041:Is Not
						s += "[]" + "\r\n"; //2036:Is Blank
						s += "<[]>"; //2037:Is Not Blank
					}
					break;

				case "TIME":
					if (par_bLinkField)
					{
						s += ">" + "\r\n"; //2038:Is After
						s += ">=" + "\r\n"; //Is After Or On
						s += "<" + "\r\n"; //2039:Is Before
						s += "<=" + "\r\n"; //Is Before Or On
						s += "][" + "\r\n"; //2031:Is Between
						s += "=" + "\r\n"; //2042:Is [At]
						//s &= "[]"   '2036:Is Blank     'MI 9/1/09 disabling because IS NULL causes all recs to be returned due to LEFT JOIN.
					}
					else
					{
						s += ">" + "\r\n"; //2038:Is After
						s += ">=" + "\r\n"; //Is After Or On
						s += "<" + "\r\n"; //2039:Is Before
						s += "<=" + "\r\n"; //Is Before Or On
						s += "][" + "\r\n"; //2031:Is Between
						s += "=" + "\r\n"; //2042:Is [At]
						s += "<>" + "\r\n"; //2043:Is Not [At]
						s += "[]" + "\r\n"; //2036:Is Blank
						s += "<[]>"; //2037:Is Not Blank
					}
					break;

				case "TELEPHONE":
					//Currently not supported, but may be in the future
					if (par_bLinkField)
					{
						s += "[=" + "\r\n"; //2032:Starts With
						s += "[" + "\r\n"; //2034:Contains
						s += "~" + "\r\n"; //Is Like
						s += "=" + "\r\n"; //2025:Is Equal To
						s += "][" + "\r\n"; //2031:Is Between
						s += "=]" + "\r\n"; //20474:Ends With
						//s &= "[]" & vbCrLf   '2036:Is Blank     'MI 9/1/09 disabling because IS NULL causes all recs to be returned due to LEFT JOIN.
						s += "~"; //Is Like
					}
					else
					{
						s += "[=" + "\r\n"; //2032:Starts With
						s += "[<>" + "\r\n"; //2033:Does Not Start With
						s += "[" + "\r\n"; //2034:Contains
						s += "<[>" + "\r\n"; //2035:Does Not Contain
						s += "~" + "\r\n"; //Is Like
						s += "<~>" + "\r\n"; //Is Not Like
						s += "=" + "\r\n"; //2025:Is Equal To
						s += "<>" + "\r\n"; //2026:Is Not Equal To
						s += "][" + "\r\n"; //2031:Is Between
						s += "=]" + "\r\n"; //20474:Ends With
						s += "<>]" + "\r\n"; //2048:Does Not End With
						s += "[]" + "\r\n"; //2036:Is Blank
						s += "<[]>"; //2037:Is Not Blank
					}
					break;

			}

			return s;
		}
		public string GetConduitFieldList(string par_sConduit)
		{
			//CS 9/8/06
			//CS PORTED as placeholder. Return "".

			//Return a list of fields for the conduit in par_sConduit
			//
			//PARAMETERS:
			//		par_sConduit: conduit for which to return the list
			//			Example: OUTLOOKCAL, PALMTODO

			//Debugging the order of calls.
			string sProc = "clDefaults::GetConduitFieldList";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			switch (par_sConduit.Trim(' ').ToUpper())
			{
				case "OUTLOOKCAL":
				case "GOOGLECAL":
					InitOutlookCalFieldList();
					return sOutlookCalFieldList;
				case "OUTLOOKCONTACT":
				case "GOOGLECONTACT":
					InitOutlookContactFldList();
					return sOutlookContactFieldList;
				case "PALMADDRESSBOOK":
					InitPalmAddrBookFldList();
					return sPalmAddressBookFieldList;
				case "PALMDATEBOOK":
					InitPalmDateBookFldList();
					return sPalmDateBookFieldList;
				case "PALMMEMO":
					InitPalmMemoFieldList();
					return sPalmMemoFieldList;
				case "PALMTODO":
					InitPalmToDoFieldList();
					return sPalmToDoFieldList;
			}

			return "";
		}
		public string GetConduitFile(string par_sConduitCode)
		{
			//CS 9/8/06
			//CS PORTED as placeholder. REturn "".

			//Return the file name of a conduit.
			//
			//PARAMETERS:
			//		par_sConduitCode: code of the conduit
			//			Example:OUTLOOKCAL, PALMTODO

			//Added to aid in debugging of the order of calls in the stack.
			string sProc = "clDefaults::GetConduitFile";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)


			string sLine = null;
			string sCode = null;
			int i = 0;
			string sResult = "";

			InitConduits();

			i = 1;
			do
			{
				sLine = goTR.ExtractString(sConduits, i, "\r\n");
				if (sLine[0] == clC.EOT)
				{
					break;
				}
				sCode = goTR.ExtractString(sLine, 1).ToUpper();
				if (sCode == par_sConduitCode.ToUpper())
				{
					sResult = goTR.ExtractString(sLine, 3);
					break;
				}
				i = i + 1;
			} while (true);

			return (sResult);
		}
		public string GetConduitLink(string par_sConduitCode)
		{
			//CS 9/8/06
			//CS PORTED as placeholder. Return "".

			//AUTHOR: FH 10/10/2002

			//Return the link of a conduit.
			//
			//PARAMETERS:
			//		par_sConduitCode: code of the conduit
			//			Example:OUTLOOKCAL, PALMTODO

			//Debugging the order of calls.
			string sProc = "clDefaults::GetConduitLink";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)


			string sLine = null;
			string sCode = null;
			int i = 0;
			string sResult = "";

			InitConduits();

			i = 1;
			do
			{
				sLine = goTR.ExtractString(sConduits, i, "\r\n");
				if (sLine[0] == clC.EOT)
				{
					break;
				}
				sCode = goTR.ExtractString(sLine, 1).ToUpper();
				if (sCode == par_sConduitCode.ToUpper())
				{
					sResult = goTR.ExtractString(sLine, 4);
					break;
				}
				i = i + 1;
			} while (true);

			return (sResult);
		}
		public string GetConduitMap(string par_sConduit)
		{
			//MI 2/20/14 Deprecate?
			//CS 9/8/06
			//CS PORTED as placeholder. Return "".

			//Return a list of field maps for the conduit in par_sConduit
			//Conduit maps are initialized in :InitConduitMaps()
			//
			//PARAMETERS:
			//		par_sConduit: conduit for which to return the list
			//			Example: OUTLOOKCAL, PALMTODO

			//Debugging the order of calls.
			string sProc = "clDefaults::GetConduitMap";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)


			switch (par_sConduit.Trim(' ').ToUpper())
			{
				case "OUTLOOKCAL":
					InitOutlookCalMap();
					return (sOutlookCalMap);
				case "OUTLOOKCONTACT":
					InitOutlookContactMap();
					return (sOutlookContactMap);
				case "PALMADDRESSBOOK":
					InitPalmAddressBookMap();
					return (sPalmAddressBookMap);
				case "PALMDATEBOOK":
					InitPalmDateBookMap();
					return (sPalmDateBookMap);
				case "PALMMEMO":
					InitPalmMemoMap();
					return (sPalmMemoMap);
				case "PALMTODO":
					InitPalmToDoMap();
					return (sPalmToDoMap);
			}

			return "";
		}
		public string GetConduitMaps(string par_sConduit = "")
		{
			//CS 9/8/06
			//CS PORTED as placeholder. Return "".

			//Return a list of supported Conduit field maps for all conduits
			//Conduit maps are initialized in :InitConduitMaps()
			//
			//PARAMETERS:
			//		par_sConduit: code of the conduit for which to return the list
			//			If blank, return field maps for all conduits

			//Debugging the order of calls.
			string sProc = "clDefaults::GetConduitMaps";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)


			string s = "";
			int i = 0;
			string sLine = null;

			//CS InitConduitMaps()

			if (par_sConduit == "")
			{
				return sConduitMaps;
			}
			else
			{
				i = 1;
				do
				{
					sLine = goTR.ExtractString(sConduitMaps, i, "\r\n");
					if (sLine[0] == clC.EOT)
					{
						break;
					}
					if (goTR.ExtractString(sLine, 1, "_").ToUpper() == par_sConduit.Trim(' ').ToUpper())
					{
						s += sLine + "\r\n";
					}
					i = i + 1;
				} while (true);
				//Remove trailing CR
				if (goTR.FromTo(s, s.Length - 1) == "\r\n")
				{
					s = goTR.FromTo(s, 1, s.Length - 2);
				}
				return (s);
			}
		}
		public string GetConduitName(string par_sConduitCode, string par_sNameType = "FRIENDLY")
		{
			//CS 9/8/06
			//CS PORTED as placeholder. Return "".

			//Return the friendly name of a conduit in the current language.
			//
			//PARAMETERS:
			//		par_sConduitCode: code of the conduit
			//			Example:OUTLOOKCAL, PALMTODO
			//		par_sNameType: what to return. Supported:
			//			FRIENDLY		'default - friendly full name of the conduit
			//			DEVICE			'name of the conduit "device", e.g. "MS Outlook" or "Palm Pilot"

			//Debugging the order of calls.
			string sProc = "clDefaults::GetConduitName";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)


			string sLine = null;
			string sCode = null;
			int i = 0;
			string sResult = "";

			InitConduits();

			i = 1;
			do
			{
				sLine = goTR.ExtractString(sConduits, i, "\r\n");
				if (sLine[0] == clC.EOT)
				{
					break;
				}
				sCode = goTR.ExtractString(sLine, 1).ToUpper();
				if (sCode == par_sConduitCode.ToUpper())
				{
					switch (par_sNameType)
					{
						case "FRIENDLY":
							sResult = goTR.ExtractString(sLine, 2);
							break;
						case "DEVICE":
							sResult = goTR.ExtractString(sLine, 6);
							break;
						default:
							sResult = "";
							break;
					}
					break;
				}
				i = i + 1;
			} while (true);

			return (sResult);
		}
		public string GetConduits()
		{
			//CS 9/8/06
			//CS PORTED as placeholder. Return "".

			//Return a list of supported Conduits (App links)
			//		The list is in the format:
			//			CONDUITCODE&TAB&FriendlyConduitName&TAB&SelltisFileName&CR&...
			//		The list is initiated in :InitConduits()

			//Debugging the order of calls.
			string sProc = "clDefaults::GetConduits";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			InitConduits();
			return sConduits;
		}
		public string GetCorrSendDefs()
		{
			//MI 1/5/07 modified IDs of default templates.

			//Get Correspondence Sending defaults

			//Debugging the order of calls.
			string sProc = "clDefaults::GetCorrSendDefs";
			//oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string s = "";

			goTR.StrWrite(ref s, "CORRSENDTEMPLEMAIL", "SND_DEFCORRSENDTEMPLEMAIL"); //was SND_2003010609514317053MAI 09732XX
			goTR.StrWrite(ref s, "CORRSENDTEMPLFAX", "SND_DEFCORRSENDTEMPLFAX"); //was SND_2004121311050415114RAH 00007XX
			goTR.StrWrite(ref s, "CORRSENDTEMPLLTRHTML", "SND_DEFCORRSENDTEMPLLTRHTML"); //was SND_2004121311043112250RAH 00007XX
			goTR.StrWrite(ref s, "CORRSENDTEMPLLTRWORD", "SND_DEFCORRSENDTEMPLLTRWORD"); //was SND_2004121311035071218RAH 00007XX
			goTR.StrWrite(ref s, "CORRSENDTEMPLQTEEMAIL", "SND_DEFCORRSENDTEMPLQTELTRWORD"); //was SND_2004121311063073193RAH 00007XX
			goTR.StrWrite(ref s, "CORRSENDTEMPLQTEFAX", "SND_DEFCORRSENDTEMPLQTEFAX"); //was SND_2004121311070904188RAH 00007XX
			goTR.StrWrite(ref s, "CORRSENDTEMPLQTELTRHTML", "SND_DEFCORRSENDTEMPLQTELTRHTML"); //was SND_2004121311055885237RAH 00007XX
			goTR.StrWrite(ref s, "CORRSENDTEMPLQTELTRWORD", "SND_DEFCORRSENDTEMPLQTELTRWORD"); //was SND_2004121311063073193RAH 00007XX

			return s;
		}
		public string GetDataAccssPermDefs()
		{
			//CS Ported, but commented out b/c the following are called but were not ported:
			//goData.FileFirstX, goData.FileNextX, godata.GetCurrentFileName, goData.GetCurrentFilePrefix()
			//PORT. Change file names to new 2-char names. To get the new names use SS UDF fnGetShortTableName.
			//Remove CASEs for tables that we don't support anymore.

			//PURPOSE:
			//		Returns data access defaults for regular users as an ini string
			//		ready to be written into the security file with goPerm:SetPerm,
			//		or into the Data Access permission profile.
			//		This is called from WADMPERM window.
			//RETURNS:
			//		Ini string with default user permissions.

			string sProc = "clDefaults::GetDataAccssPermDefs";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sResult = "";
			//CS: Commented here to end
			//Dim sVal As String

			//'Then files 
			//Dim lInd As Long = goData.FileFirstX("ALL")
			//'Loop on files
			//Do
			//    If Not goData.FileNextX(lInd, "ALL") Then Exit Do
			//    Select Case UCase(goData.GetCurrentFileName())
			//        'CASE "_IMPLOG"
			//        '          sVal = clc.SELL_FULL.ToString & clc.SELL_FULL.ToString & clc.SELL_FULL.ToString
			//        'CASE "_LINK"
			//        '          sVal = clc.SELL_FULL.ToString & clc.SELL_FULL.ToString & clc.SELL_FULL.ToString
			//        Case "MD"
			//            sVal = clc.SELL_FULL.ToString & clc.SELL_FULL.ToString & clc.SELL_FULL.ToString
			//            'Case "_SESSION"
			//            '    sVal = clc.SELL_FULL.ToString & clc.SELL_FULL.ToString & clc.SELL_FULL.ToString
			//            'Case "_SYNCLOG"
			//            '    sVal = clc.SELL_FULL.ToString & clc.SELL_FULL.ToString & clc.SELL_FULL.ToString
			//        Case "TN"
			//            sVal = clc.SELL_FULL.ToString & clc.SELL_FULL.ToString & clc.SELL_FULL.ToString
			//        Case "AC"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString
			//        Case "AP"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString
			//        Case "CO"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_NO.ToString
			//        Case "CR"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "CN"
			//            sVal = clc.SELL_FULL.ToString & clc.SELL_FULL.ToString & clC.SELL_NO.ToString
			//        Case "DM"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "DV"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "DO"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString
			//        Case "EL"
			//            sVal = clc.SELL_FULL.ToString & clc.SELL_FULL.ToString & clc.SELL_FULL.ToString
			//        Case "EA"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "EC"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "EX"
			//            sVal = clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString
			//        Case "FI"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "GR"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString
			//        Case "IU"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "JF"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "LO"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "MS"
			//            sVal = clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString
			//        Case "MO"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "OP"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString
			//        Case "PD"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "PR"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString
			//        Case "PT"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "QT"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString
			//        Case "QL"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString
			//        Case "RL"     'Customer Relationship
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "RE"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "RS"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString
			//        Case "RO"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "ST"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "SO"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString
			//        Case "TR"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "TE"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "TD"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_SELECTIVE.ToString
			//        Case "US"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_NO.ToString & clC.SELL_NO.ToString
			//        Case "VE"
			//            sVal = clc.SELL_FULL.ToString & clC.SELL_SELECTIVE.ToString & clC.SELL_NO.ToString
			//        Case Else
			//            'Custom file
			//            sVal = clc.SELL_FULL.ToString & clc.SELL_FULL.ToString & clc.SELL_FULL.ToString
			//    End Select
			//    goTR.StrWrite(sResult, goData.GetCurrentFilePrefix(), sVal)

			//Loop
			return sResult;
		}
		public string GetDayNames(string par_sAbbrevLevel)
		{
			//CS PORTED as placeholder. Translate later if needed.

			//Return a tab-delimited list of day names or abbreviations
			//starting with the first day of week selected (and saved) in
			//Personal Options>Calendar (POP_PERSONAL_OPTIONS, CMB_FIRSTDAYOFWEEK
			//property) 
			//
			//PARAMETERS:
			//		par_sAbbrevLevel: level of abbreviation. Supported values:
			//			NONE		'no abbreviation, return full day names
			//			3CHARS		'abbreviate to 3 characters
			//			1CHAR		'abbreviate to the first character only
			//
			//RETURNS:
			//		tab-delimited string of 7 day names. First day is 
			//			whatever is selected in CMB_FIRSTDAYOFWEEK in personal
			//			options.

			//Debugging the order of calls.
			string sProc = "clDefaults::GetDayNames";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)
			return "";

			//iFirstDay is int
			//sResult is string

			//iFirstDay = goMeta:LineRead(goP:GetUserTID(),"POP_PERSONAL_OPTIONS","CMB_FIRSTDAYOFWEEK",1,True)

			//IF iFirstDay < 1 THEN iFirstDay = 1
			//IF iFirstDay > 7 THEN iFirstDay = 1

			//sResult = ""
			//SWITCH iFirstDay
			// 	CASE 1
			//		SWITCH par_sAbbrevLevel
			//			CASE "NONE"
			//				sResult &= MessTraduit(5070)&TAB   '5070:Sunday
			//				sResult &= MessTraduit(5071)&TAB   '5070:Monday
			//				sResult &= MessTraduit(5072)&TAB   '5070:Tuesday
			//				sResult &= MessTraduit(5073)&TAB   '5070:Wednesday
			//				sResult &= MessTraduit(5074)&TAB   '5070:Thursday
			//				sResult &= MessTraduit(5075)&TAB   '5070:Friday
			//				sResult &= MessTraduit(5076)   '5070:Saturday
			//			CASE "3CHARS"
			//				sResult &= MessTraduit(5080)&TAB   '5071:Sun
			//				sResult &= MessTraduit(5081)&TAB   '5071:Mon
			//				sResult &= MessTraduit(5082)&TAB   '5071:Tue
			//				sResult &= MessTraduit(5083)&TAB   '5071:Wed
			//				sResult &= MessTraduit(5084)&TAB   '5071:Thu
			//				sResult &= MessTraduit(5085)&TAB   '5071:Fri
			//				sResult &= MessTraduit(5086)   '5071:Sat
			//			CASE "1CHAR"
			//				sResult &= MessTraduit(5090)&TAB   '5072:S
			//				sResult &= MessTraduit(5091)&TAB   '5072:M
			//				sResult &= MessTraduit(5092)&TAB   '5072:T
			//				sResult &= MessTraduit(5093)&TAB   '5072:W
			//				sResult &= MessTraduit(5094)&TAB   '5072:T
			//				sResult &= MessTraduit(5095)&TAB   '5072:F
			//				sResult &= MessTraduit(5096)   '5072:S
			//			OTHER CASE
			//				sResult &= MessTraduit(5070)&TAB   '5070:Sunday
			//				sResult &= MessTraduit(5071)&TAB   '5070:Monday
			//				sResult &= MessTraduit(5072)&TAB   '5070:Tuesday
			//				sResult &= MessTraduit(5073)&TAB   '5070:Wednesday
			//				sResult &= MessTraduit(5074)&TAB   '5070:Thursday
			//				sResult &= MessTraduit(5075)&TAB   '5070:Friday
			//				sResult &= MessTraduit(5076)   '5070:Saturday
			//		END
			// 	CASE 2
			//		SWITCH par_sAbbre&Level
			//			CASE "NONE"
			//				sResult &= MessTraduit(5071)&TAB   '5070:Monday
			//				sResult &= MessTraduit(5072)&TAB   '5070:Tuesday
			//				sResult &= MessTraduit(5073)&TAB   '5070:Wednesday
			//				sResult &= MessTraduit(5074)&TAB   '5070:Thursday
			//				sResult &= MessTraduit(5075)&TAB   '5070:Friday
			//				sResult &= MessTraduit(5076)&TAB   '5070:Saturday
			//				sResult &= MessTraduit(5070)   '5070:Sunday
			//			CASE "3CHARS"
			//				sResult &= MessTraduit(5081)&TAB   '5071:Mon
			//				sResult &= MessTraduit(5082)&TAB   '5071:Tue
			//				sResult &= MessTraduit(5083)&TAB   '5071:Wed
			//				sResult &= MessTraduit(5084)&TAB   '5071:Thu
			//				sResult &= MessTraduit(5085)&TAB   '5071:Fri
			//				sResult &= MessTraduit(5086)&TAB   '5071:Sat
			//				sResult &= MessTraduit(5080)   '5071:Sun
			//			CASE "1CHAR"
			//				sResult &= MessTraduit(5091)&TAB   '5072:M
			//				sResult &= MessTraduit(5092)&TAB   '5072:T
			//				sResult &= MessTraduit(5093)&TAB   '5072:W
			//				sResult &= MessTraduit(5094)&TAB   '5072:T
			//				sResult &= MessTraduit(5095)&TAB   '5072:F
			//				sResult &= MessTraduit(5096)&TAB   '5072:S
			//				sResult &= MessTraduit(5090)   '5072:S
			//			OTHER CASE
			//				sResult &= MessTraduit(5071)&TAB   '5070:Monday
			//				sResult &= MessTraduit(5072)&TAB   '5070:Tuesday
			//				sResult &= MessTraduit(5073)&TAB   '5070:Wednesday
			//				sResult &= MessTraduit(5074)&TAB   '5070:Thursday
			//				sResult &= MessTraduit(5075)&TAB   '5070:Friday
			//				sResult &= MessTraduit(5076)&TAB   '5070:Saturday
			//				sResult &= MessTraduit(5070)   '5070:Sunday
			//		END
			// 	CASE 3
			//		SWITCH par_sAbbrevLevel
			//			CASE "NONE"
			//				sResult &= MessTraduit(5072)&TAB   '5070:Tuesday
			//				sResult &= MessTraduit(5073)&TAB   '5070:Wednesday
			//				sResult &= MessTraduit(5074)&TAB   '5070:Thursday
			//				sResult &= MessTraduit(5075)&TAB   '5070:Friday
			//				sResult &= MessTraduit(5076)&TAB   '5070:Saturday
			//				sResult &= MessTraduit(5070)&TAB   '5070:Sunday
			//				sResult &= MessTraduit(5071)   '5070:Monday
			//			CASE "3CHARS"
			//				sResult &= MessTraduit(5082)&TAB   '5071:Tue
			//				sResult &= MessTraduit(5083)&TAB   '5071:Wed
			//				sResult &= MessTraduit(5084)&TAB   '5071:Thu
			//				sResult &= MessTraduit(5085)&TAB   '5071:Fri
			//				sResult &= MessTraduit(5086)&TAB   '5071:Sat
			//				sResult &= MessTraduit(5080)&TAB   '5071:Sun
			//				sResult &= MessTraduit(5081)   '5071:Mon
			//			CASE "1CHAR"
			//				sResult &= MessTraduit(5092)&TAB   '5072:T
			//				sResult &= MessTraduit(5093)&TAB   '5072:W
			//				sResult &= MessTraduit(5094)&TAB   '5072:T
			//				sResult &= MessTraduit(5095)&TAB   '5072:F
			//				sResult &= MessTraduit(5096)&TAB   '5072:S
			//				sResult &= MessTraduit(5090)&TAB   '5072:S
			//				sResult &= MessTraduit(5091)   '5072:M
			//			OTHER CASE
			//				sResult &= MessTraduit(5072)&TAB   '5070:Tuesday
			//				sResult &= MessTraduit(5073)&TAB   '5070:Wednesday
			//				sResult &= MessTraduit(5074)&TAB   '5070:Thursday
			//				sResult &= MessTraduit(5075)&TAB   '5070:Friday
			//				sResult &= MessTraduit(5076)&TAB   '5070:Saturday
			//				sResult &= MessTraduit(5070)&TAB   '5070:Sunday
			//				sResult &= MessTraduit(5071)   '5070:Monday
			//		END
			// 	CASE 4
			//		SWITCH par_sAbbrevLevel
			//			CASE "NONE"
			//				sResult &= MessTraduit(5073)&TAB   '5070:Wednesday
			//				sResult &= MessTraduit(5074)&TAB   '5070:Thursday
			//				sResult &= MessTraduit(5075)&TAB   '5070:Friday
			//				sResult &= MessTraduit(5076)&TAB   '5070:Saturday
			//				sResult &= MessTraduit(5070)&TAB   '5070:Sunday
			//				sResult &= MessTraduit(5071)&TAB   '5070:Monday
			//				sResult &= MessTraduit(5072)   '5070:Tuesday
			//			CASE "3CHARS"
			//				sResult &= MessTraduit(5083)&TAB   '5071:Wed
			//				sResult &= MessTraduit(5084)&TAB   '5071:Thu
			//				sResult &= MessTraduit(5085)&TAB   '5071:Fri
			//				sResult &= MessTraduit(5086)&TAB   '5071:Sat
			//				sResult &= MessTraduit(5080)&TAB   '5071:Sun
			//				sResult &= MessTraduit(5081)&TAB   '5071:Mon
			//				sResult &= MessTraduit(5082)   '5071:Tue
			//			CASE "1CHAR"
			//				sResult &= MessTraduit(5093)&TAB   '5072:W
			//				sResult &= MessTraduit(5094)&TAB   '5072:T
			//				sResult &= MessTraduit(5095)&TAB   '5072:F
			//				sResult &= MessTraduit(5096)&TAB   '5072:S
			//				sResult &= MessTraduit(5090)&TAB   '5072:S
			//				sResult &= MessTraduit(5091)&TAB   '5072:M
			//				sResult &= MessTraduit(5092)   '5072:T
			//			OTHER CASE
			//				sResult &= MessTraduit(5073)&TAB   '5070:Wednesday
			//				sResult &= MessTraduit(5074)&TAB   '5070:Thursday
			//				sResult &= MessTraduit(5075)&TAB   '5070:Friday
			//				sResult &= MessTraduit(5076)&TAB   '5070:Saturday
			//				sResult &= MessTraduit(5070)&TAB   '5070:Sunday
			//				sResult &= MessTraduit(5071)&TAB   '5070:Monday
			//				sResult &= MessTraduit(5072)   '5070:Tuesday
			//		END
			// 	CASE 5
			//		SWITCH par_sAbbrevLevel
			//			CASE "NONE"
			//				sResult &= MessTraduit(5074)&TAB   '5070:Thursday
			//				sResult &= MessTraduit(5075)&TAB   '5070:Friday
			//				sResult &= MessTraduit(5076)&TAB   '5070:Saturday
			//				sResult &= MessTraduit(5070)&TAB   '5070:Sunday
			//				sResult &= MessTraduit(5071)&TAB   '5070:Monday
			//				sResult &= MessTraduit(5072)&TAB   '5070:Tuesday
			//				sResult &= MessTraduit(5073)   '5070:Wednesday
			//			CASE "3CHARS"
			//				sResult &= MessTraduit(5084)&TAB   '5071:Thu
			//				sResult &= MessTraduit(5085)&TAB   '5071:Fri
			//				sResult &= MessTraduit(5086)&TAB   '5071:Sat
			//				sResult &= MessTraduit(5080)&TAB   '5071:Sun
			//				sResult &= MessTraduit(5081)&TAB   '5071:Mon
			//				sResult &= MessTraduit(5082)&TAB   '5071:Tue
			//				sResult &= MessTraduit(5083)   '5071:Wed
			//			CASE "1CHAR"
			//				sResult &= MessTraduit(5094)&TAB   '5072:T
			//				sResult &= MessTraduit(5095)&TAB   '5072:F
			//				sResult &= MessTraduit(5096)&TAB   '5072:S
			//				sResult &= MessTraduit(5090)&TAB   '5072:S
			//				sResult &= MessTraduit(5091)&TAB   '5072:M
			//				sResult &= MessTraduit(5092)&TAB   '5072:T
			//				sResult &= MessTraduit(5093)   '5072:W
			//			OTHER CASE
			//				sResult &= MessTraduit(5074)&TAB   '5070:Thursday
			//				sResult &= MessTraduit(5075)&TAB   '5070:Friday
			//				sResult &= MessTraduit(5076)&TAB   '5070:Saturday
			//				sResult &= MessTraduit(5070)&TAB   '5070:Sunday
			//				sResult &= MessTraduit(5071)&TAB   '5070:Monday
			//				sResult &= MessTraduit(5072)&TAB   '5070:Tuesday
			//				sResult &= MessTraduit(5073)   '5070:Wednesday
			//		END
			// 	CASE 6
			//		SWITCH par_sAbbrevLevel
			//			CASE "NONE"
			//				sResult &= MessTraduit(5075)&TAB   '5070:Friday
			//				sResult &= MessTraduit(5076)&TAB   '5070:Saturday
			//				sResult &= MessTraduit(5070)&TAB   '5070:Sunday
			//				sResult &= MessTraduit(5071)&TAB   '5070:Monday
			//				sResult &= MessTraduit(5072)&TAB   '5070:Tuesday
			//				sResult &= MessTraduit(5073)&TAB   '5070:Wednesday
			//				sResult &= MessTraduit(5074)   '5070:Thursday
			//			CASE "3CHARS"
			//				sResult &= MessTraduit(5085)&TAB   '5071:Fri
			//				sResult &= MessTraduit(5086)&TAB   '5071:Sat
			//				sResult &= MessTraduit(5080)&TAB   '5071:Sun
			//				sResult &= MessTraduit(5081)&TAB   '5071:Mon
			//				sResult &= MessTraduit(5082)&TAB   '5071:Tue
			//				sResult &= MessTraduit(5083)&TAB   '5071:Wed
			//				sResult &= MessTraduit(5084)   '5071:Thu
			//			CASE "1CHAR"
			//				sResult &= MessTraduit(5095)&TAB   '5072:F
			//				sResult &= MessTraduit(5096)&TAB   '5072:S
			//				sResult &= MessTraduit(5090)&TAB   '5072:S
			//				sResult &= MessTraduit(5091)&TAB   '5072:M
			//				sResult &= MessTraduit(5092)&TAB   '5072:T
			//				sResult &= MessTraduit(5093)&TAB   '5072:W
			//				sResult &= MessTraduit(5094)   '5072:T
			//			OTHER CASE
			//				sResult &= MessTraduit(5075)&TAB   '5070:Friday
			//				sResult &= MessTraduit(5076)&TAB   '5070:Saturday
			//				sResult &= MessTraduit(5070)&TAB   '5070:Sunday
			//				sResult &= MessTraduit(5071)&TAB   '5070:Monday
			//				sResult &= MessTraduit(5072)&TAB   '5070:Tuesday
			//				sResult &= MessTraduit(5073)&TAB   '5070:Wednesday
			//				sResult &= MessTraduit(5074)   '5070:Thursday
			//		END
			// 	CASE 7
			//		SWITCH par_sAbbrevLevel
			//			CASE "NONE"
			//				sResult &= MessTraduit(5076)&TAB   '5070:Saturday
			//				sResult &= MessTraduit(5070)&TAB   '5070:Sunday
			//				sResult &= MessTraduit(5071)&TAB   '5070:Monday
			//				sResult &= MessTraduit(5072)&TAB   '5070:Tuesday
			//				sResult &= MessTraduit(5073)&TAB   '5070:Wednesday
			//				sResult &= MessTraduit(5074)&TAB   '5070:Thursday
			//				sResult &= MessTraduit(5075)   '5070:Friday
			//			CASE "3CHARS"
			//				sResult &= MessTraduit(5086)&TAB   '5071:Sat
			//				sResult &= MessTraduit(5080)&TAB   '5071:Sun
			//				sResult &= MessTraduit(5081)&TAB   '5071:Mon
			//				sResult &= MessTraduit(5082)&TAB   '5071:Tue
			//				sResult &= MessTraduit(5083)&TAB   '5071:Wed
			//				sResult &= MessTraduit(5084)&TAB   '5071:Thu
			//				sResult &= MessTraduit(5085)   '5071:Fri
			//			CASE "1CHAR"
			//				sResult &= MessTraduit(5096)&TAB   '5072:S
			//				sResult &= MessTraduit(5090)&TAB   '5072:S
			//				sResult &= MessTraduit(5091)&TAB   '5072:M
			//				sResult &= MessTraduit(5092)&TAB   '5072:T
			//				sResult &= MessTraduit(5093)&TAB   '5072:W
			//				sResult &= MessTraduit(5094)&TAB   '5072:T
			//				sResult &= MessTraduit(5095)   '5072:F
			//			OTHER CASE
			//				sResult &= MessTraduit(5076)&TAB   '5070:Saturday
			//				sResult &= MessTraduit(5070)&TAB   '5070:Sunday
			//				sResult &= MessTraduit(5071)&TAB   '5070:Monday
			//				sResult &= MessTraduit(5072)&TAB   '5070:Tuesday
			//				sResult &= MessTraduit(5073)&TAB   '5070:Wednesday
			//				sResult &= MessTraduit(5074)&TAB   '5070:Thursday
			//				sResult &= MessTraduit(5075)   '5070:Friday
			//		END
			//END

			//RESULT sResult
		}
		public string GetFeaturePermDefs()
		{
			//MI 5/18/11 Added DOCLIBPUBLxxxxx, CUSTADMINxxxxxxxx cases.
			//MI 5/13/11 Added CUSTADMINREPORTS.
			//MI 11/26/07 Added WORKGROUPOPTIONS.
			//Waiting on goPerm.GetFeaturePermList()

			//PURPOSE:
			//		Returns user feature permissions defaults for regular users as an ini string
			//		ready to be written into the security file (goPerm:SetPerm). Before writing it
			//		with goPerm:SetPerm, concatenate this string with goDef:GetDataAccssPermDefs() and
			//		add to it TYPE= and ID= parameters with gotr.strwrite().
			//		This is called from WADMPERM window.
			//RETURNS:
			//		Ini string with default user permissions.
			//PERMISSIONS CONSTANTS
			//		SELL_AUTHOR_NONE		=	0
			//		SELL_AUTHOR_PARTIAL		=	1
			//		SELL_AUTHOR_FULL		=	2
			//		SELL_ADMIN				=	3
			//		SELL_CREATENEWDB		=	4
			//		SELL_BACKUPSERVER		=	5		'NOT SUPPORTED!
			//		SELL_BACKUP				=	6
			//		SELL_IMPORT				=	7
			//		SELL_EXPORT				=	8
			//		SELL_TRANSFERIN			=	9
			//		SELL_TRANSFEROUT		=	10
			//		SELL_PERIODICBILLING	=	11
			//		SELL_REIMBURSE			=	12
			//		SELL_APPROVEREIMBURSE	=	13
			//		SELL_PRINT				=	14
			//		SELL_PRINTRECORD		=	15		'NOT SUPPORTED!
			//		SELL_PRINTOWNCORR		=	16		'NOT SUPPORTED!
			//		SELL_PRINTALLCORR		=	17		'NOT SUPPORTED!
			//		SELL_SEND				=	18
			//		SELL_SENDRECORD			=	19		'NOT SUPPORTED!
			//		SELL_SENDOWNCORR		=	20		'NOT SUPPORTED!
			//		SELL_SENDALLCORR		=	21		'NOT SUPPORTED!


			string sProc = "clDefaults::GetFeaturePermDefs";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			int iPos = 0;
			string sWork = null;
			string sSlot = "";
			goTR.StrWrite(ref sSlot, "ADMIN", 0);
			goTR.StrWrite(ref sSlot, "AUTHOR", clC.SELL_AUTHOR_NONE.ToString()); //0
			do
			{
				//CS: need to uncomment after goPerm.GetFeaturepermList ready
				//sWork = gotr.ExtractString(goPerm.GetFeaturePermList(), iPos, vbtab)
				sWork = ""; //CS remove
				if (sWork[0] == clC.EOT)
				{
					break;
				}
				switch (sWork)
				{
					case "":
					break;
						//Skip
					case "REIMBURSE":
					case "APPROVEREIMBURSE":
					case "PERIODICBILLING":
					case "WORKGROUPOPTIONS":
					case "CUSTADMINREPORTS":
					case "CUSTADMINMANAGELOGINS":
					case "CUSTADMINMANAGEFEATURES":
					case "CUSTADMINMANAGEDATAACCESS":
					case "CUSTADMINREASSIGNRECORDS":
						//Disallowed
						goTR.StrWrite(ref sSlot, sWork, "0");
						break;
					case "DOCLIBPUBLICCREATEFOLDERS":
					case "DOCLIBPUBLICDELETEFOLDERS":
					case "DOCLIBPUBLICCREATEFILES":
					case "DOCLIBPUBLICDELETEFILES":
						//Disallowed
						goTR.StrWrite(ref sSlot, sWork, "0");
						break;
					default:
						//Allowed
						goTR.StrWrite(ref sSlot, sWork, "1");
						break;
				}
				iPos = iPos + 1;
			} while (true);

			return sSlot;
		}
		public string GetFieldPrefixes()
		{
			//MI 3/28/14 Added MMR_.
			//MI 8/21/10 Added comment to PURPOSE.
			//MI 12/2/09 Added support for DTY, DTQ, DTM, DTD.
			//MI 11/27/06 Added GID.
			//MI 6/17/06 Commented out blank case, which is unsupported in SellSQL
			//CS OK

			//PURPOSE:
			//		Return a vbcrlf_delimited list of supported field prefixes.
			//		There is no vbcrlf added at the end of the list. If modifying
			//		this list make sure that:
			//			- No vbcrlf is added at the end of the list
			//			- No blank lines are added anywhere other than for the 
			//				CLEUNIK field
			//           - Prefixes are uppercase only and 3 chars each
			//		IMPORTANT: Keep in sync with :InitMetaObjects()!
			//NOTE:
			//		Use ExtractString(goDef:GetFieldPrefixes(),<n>,vbcrlf) in a loop
			//		to extract all list elements.
			//		To get default field properties for each prefix, run
			//		goDef:GetFieldPropertiesByPrfx(sPrefix).

			//Debugging the order of calls.
			string sProc = "clDefaults::GetFieldPrefixes";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string s = "";

			//s &= "" & vbCrLf    'Counter (CLEUNIK) - not supported in SellSQL
			s += "BI_" + "\r\n"; //Big integer
			s += "BIN" + "\r\n"; //Binary and other memos
			s += "CHK" + "\r\n"; //Checkbox (internally short int)
			//s &= "CHR" & vbcrlf 'Checkbox reverse
			s += "CMB" + "\r\n"; //Combo box
			s += "CUR" + "\r\n"; //Currency
			//s &= "CRR" & vbCrLf 'Currency Reverse
			s += "DR_" + "\r\n"; //Double Real
			//s &= "DRR" & vbCrLf 'Double Real Reverse
			s += "DTT" + "\r\n"; //Date/time (datetime)
			s += "DTE" + "\r\n"; //Date (datetime, virtual)
			//s &= "DTR" & vbCrLf 'Date Reverse (Long int with value stored as 'max val-date val')
			s += "DTD" + "\r\n"; //Day Date (datetime, read-only)
			s += "DTM" + "\r\n"; //Month Date (datetime, read-only)
			s += "DTQ" + "\r\n"; //Quarter Date (datetime, read-only)
			s += "DTY" + "\r\n"; //Year Date (datetime, read-only)
			s += "EML" + "\r\n"; //E-mail
			//s &= "EUP" & vbCrLf 'E-mail uppercase, 40 characters only
			//s &= "EUR" & vbCrLf 'E-mail uppercase reverse, 40 characters only
			s += "FIL" + "\r\n"; //File
			s += "GID" + "\r\n"; //Globally unique ID
			s += "INT" + "\r\n"; //Integer
			//s &= "INR" & vbCrLf 'Integer Reverse
			s += "LI_" + "\r\n"; //Long integer
			//s &= "LIR" & vbCrLf 'Long integer Reverse
			s += "LNK" + "\r\n"; //Link
			s += "LST" + "\r\n"; //Listbox
			s += "MLS" + "\r\n"; //Metadata list (combobox- or listbox-type field automatically filled from a list in _META)
			//s &= "MLR" & vbCrLf 'Metadata list Reverse
			s += "MMO" + "\r\n"; //Memo
			s += "MMR" + "\r\n"; //Memo
			//s &= "MUP" & vbCrLf 'Uppercase-only, no-accent version of a MMO_ field of same name, 40 chars only
			//s &= "MUR" & vbCrLf 'Memo uppercase reverse, 40 chars only
			s += "SEL" + "\r\n"; //Selector (radio buttons)
			s += "SI_" + "\r\n"; //Short integer
			//s &= "SIR" & vbCrLf 'Short integer Reverse
			s += "SR_" + "\r\n"; //Single Real
			//s &= "SRR" & vbCrLf 'Single Real Reverse
			s += "SYS" + "\r\n"; //System-generated (virtual), such as 'Name'
			s += "TEL" + "\r\n"; //Telephone
			//s &= "TEO" & vbCrLf 'Telephone opposite direction
			s += "TME" + "\r\n"; //Time (Long int)
			s += "TML" + "\r\n"; //Time long (Long int)
			//s &= "TMR" & vbCrLf 'Time reverse order (long int with value stored as 'max val-date val')
			s += "TXT" + "\r\n"; //Text (non-memo)
			//s &= "TUP" & vbCrLf 'Uppercase-only, no-accent version of a TXT_ field of same name, up to 40 chars
			//s &= "TUR" & vbCrLf 'Text uppercase-only, reverse, up to 40 chars
			s += "URL"; //Web URL

			return (s);
		}

		public string GetFieldPropertiesByPrfx(string par_sPrefix)
		{
			//MI 11/2/09 Added support for DTY, DTQ, DTM, DTD.
			//MI 11/27/06 Added GID.
			//MI 10/27/06 Added DTT, cleaned up.

			//PURPOSE:
			//       Returns default field alignment, size, type as a tab-delimited string.
			//PARAMETERS
			//		par_sPrefix: field prefix, for ex: "CMB", "LNK", "TXT", "_MUP"
			//			or complete field name, ex: "DTE_Time", "SYS_Name", etc.
			//			Prefix can be appended or prepended with "_", ex: "CMB_",
			//			"TXT_", "_CHR", "_SYS_", etc.
			//			For CLEUNIK fields, pass "" to this parameter.
			//RETURNS
			//		sAlignment & vbTAB & sWidth & vbTAB & sType where:
			//			sAlignment can be:
			//				L	'left
			//				C	'center
			//				R	'right
			//			sWidth is an integer representing width in characters
			//			sType is a string with proper-cased words describing the type
			//EXAMPLE:
			//		sResult is string
			//		sAlignment is string
			//		iWidth is int
			//		sType is string
			//		sResult = goDef:GetFieldPropertiesByPrefix("TXT")
			//		sAlignment = ExtractString(sResult,1)
			//		iWidth = Val(ExtractString(sResult,2))
			//		sType = ExtractString(sResult,3)

			//Debugging the order of calls.
			string sProc = "clDefaults::GetFieldPropertiesByPrefix";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sPrefix = par_sPrefix.ToUpper();
			string sAlignment = null;
			int iWidth = 0;
			string sType = null;

			//Remove starting '_' if any
			if (sPrefix.Substring(0, 1) == "_")
			{
				sPrefix = sPrefix.Substring(1);
			}

			//Deal with CLEUNIK
			if (sPrefix.Substring(2).ToUpper() == "CLEUNIK")
			{
				sPrefix = "";
			}

			//Append '_' to first 3 characters if needed
			sPrefix = sPrefix.Substring(0, 3) + "_";

			switch (sPrefix)
			{
				case "_": //Counter (reserved for CLEUNIK fields)
					sAlignment = "R";
					iWidth = 10;
					sType = "Counter - long int"; //messtranslate MessTraduit(5158) '5158:Counter - long int
					break;
				case "BI__": //Big integer (bigint in SQL Server)
					sAlignment = "R";
					iWidth = 12;
					sType = "Number - big int"; //messtranslate MessTraduit(????)
					break;
				case "BIN_": //Binary and other memos
					sAlignment = "L";
					iWidth = 30;
					sType = "Binary"; //messtranslate MessTraduit(5123) '5123:Binary
					break;
				case "CHK_": //Checkbox (internally short int)
					sAlignment = "C";
					iWidth = 6;
					sType = "Checkbox"; //messtranslate MessTraduit(5124) '5124:Checkbox
					break;
				case "CMB_": //Combo box
					sAlignment = "L";
					iWidth = 16;
					sType = "Combobox"; //Messtranslate MessTraduit(5126) '5126:Combobox
					break;
				case "CUR_": //Currency
					sAlignment = "R";
					iWidth = 14;
					sType = "Currency"; //messtranslate MessTraduit(5127) '5127:Currency
					break;
				case "DR__": //Double Real
					sAlignment = "R";
					iWidth = 12;
					sType = "Number - double real"; //messtranslate MessTraduit(5128) '5128:Number - double real
					break;
				case "DTE_": //Date (datetime)
					sAlignment = "L";
					iWidth = 12;
					sType = "Date"; //messtranslate MessTraduit(5129) '5129:Date
					break;
				case "DTT_": //Date (datetime)
					sAlignment = "L";
					iWidth = 20;
					sType = "Date/time"; //messtranslate
					break;
				case "DTD_": //Day Date (datetime)
					sAlignment = "L";
					iWidth = 12;
					sType = "Day"; //messtranslate
					break;
				case "DTM_": //month Date (datetime)
					sAlignment = "L";
					iWidth = 10;
					sType = "Month"; //messtranslate
					break;
				case "DTQ_": //Quarter Date (datetime)
					sAlignment = "L";
					iWidth = 10;
					sType = "Quarter"; //messtranslate
					break;
				case "DTY_": //Year Date (datetime)
					sAlignment = "L";
					iWidth = 6;
					sType = "Year"; //messtranslate
					break;
				case "EML_": //E-mail
					sAlignment = "L";
					iWidth = 25;
					sType = "E-mail"; //messtranslate MessTraduit(5234) '5234:E-mail
					break;
				case "FIL_":
					sAlignment = "L";
					iWidth = 30;
					sType = "File"; //messtranslate MessTraduit(5156) '5156:File
					break;
				case "GID_":
					sAlignment = "L";
					iWidth = 30;
					sType = "Globally unique ID";
					break;
				case "INT_": //Integer
					sAlignment = "R";
					iWidth = 6;
					sType = "Number - int"; //messtranslate MessTraduit(5131) '5131:Number - int
					break;
				case "LI__": //Long integer
					sAlignment = "R";
					iWidth = 12;
					sType = "Number - long int"; //messtranslate MessTraduit(5132) '5132:Number - long int
					break;
				case "LNK_": //Link
					sAlignment = "L";
					iWidth = 20;
					sType = "Link"; //messtranslate MessTraduit(5133) '5133:Link
					break;
				case "LST_": //Listbox
					sAlignment = "L";
					iWidth = 16;
					sType = "Listbox"; //messtranslate MessTraduit(5134) '5134:Listbox
					break;
				case "MLS_": //Metadata list (combobox- or listbox-type field automatically filled from a list in _META)
					sAlignment = "L";
					iWidth = 16;
					sType = "List"; //messtranslate MessTraduit(5135) '5135:List
					break;
				case "MMO_": //Memo
					sAlignment = "L";
					iWidth = 40;
					sType = "Memo"; //messtranslate MessTraduit(5136) '5136:Memo
					break;
				case "MMR_": //Memo
					sAlignment = "L";
					iWidth = 40;
					sType = "Rich memo";
					break;
				case "SEL_": //Selector (radio buttons)
					sAlignment = "L";
					iWidth = 14;
					sType = "Selector"; //messtranslate MessTraduit(5139) '5139:Selector
					break;
				case "SI__": //Short integer
					sAlignment = "R";
					iWidth = 6;
					sType = "Number - short int"; //messtranslate MessTraduit(5140) '5140:Number - short int
					break;
				case "SR__": //Single Real
					sAlignment = "R";
					iWidth = 10;
					sType = "Number - single real"; //messtranslate MessTraduit(5141) '5141:Number - single real
					break;
				case "SYS_": //System-generated (virtual), such as 'Name'
					sAlignment = "L";
					iWidth = 24;
					sType = "System"; //messtranslate MessTraduit(5142) '5142:System
					break;
				case "TEL_": //Telephone
					sAlignment = "L";
					iWidth = 20;
					sType = "Telephone"; //messtranslate MessTraduit(5159) '5159:Telephone
					break;
				case "TME_": //Time (Long int)
					sAlignment = "L";
					iWidth = 12;
					sType = "Time"; //messtranslate MessTraduit(5144) '5144:Time
					break;
				case "TML_": //Time long (Long int)
					sAlignment = "L";
					iWidth = 12;
					sType = "Time (long display)"; //messtranslate MessTraduit(5145) '5145:Time (long display)
					break;
				case "TXT_": //Text (non-memo)
					sAlignment = "L";
					iWidth = 20;
					sType = "Text"; //messtranslate MessTraduit(5147) '5147:Text
					break;
				case "URL_":
					sAlignment = "L";
					iWidth = 30;
					sType = "URL"; //messtranslate MessTraduit(5157) '5157:URL
					break;
				default:
					//Catches "SYS_" and other field types
					sAlignment = "L";
					iWidth = 20;
					sType = "Unsupported"; //messtranslate MessTraduit(5150) '5150:Unsupported
					break;
			}

			return (sAlignment + "\t" + iWidth + "\t" + sType);
		}
		public int GetFirstFreeActionNo(string par_sActionsList)
		{
			//CS OK

			//PURPOSE:
			//		This is called from WAGEPRO, WAGEACT, and WAGEMSG windows
			//		to get the first available number for a new action.
			//		Return the first free Action number in par_sActionsList.
			//		In this context, the 'action number' is its ID
			//		that is unique within a single automator. 
			//		All actions including the 'No' actions in events and 
			//		message box actions draw from this pool of numbers.
			//		Actions start with a number 1 (which is the default value).
			//		Non-contiguous values are allowed. 
			//PARAMETERS:
			//		par_sActionsList: string: comma-delimited list of
			//			action numbers that are in use. The list must have a
			//			trailing comma, for ex:
			//				2,3,6,9,14,
			//RETURNS:
			//		Integer: first free action number. If there is no 
			//			available number, returns 0.

			string sProc = "clDefaults::GetFirstFreeActionNo";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			int i = 0;
			int iResult = -1;
			//Dim iCount As Integer

			for (i = 1; i <= clC.SELL_MAX_AUTOMATOR_ACTIONS; i++)
			{
				if (par_sActionsList.IndexOf(i.ToString() + ",") + 1 < 1)
				{
					iResult = i;
					break;
				}
			}

			if (iResult == -1)
			{
				iResult = clC.SELL_MAX_AUTOMATOR_ACTIONS + 1;
			}

			if (iResult > clC.SELL_MAX_AUTOMATOR_ACTIONS)
			{
				iResult = 0;
			}

			return iResult;
		}

		public string GetFormDefaults(string par_sFileName)
		{
			//MI 6/5/07 Created.
			//PURPOSE:
			//       Returns the basic FRM_ metadata that defines a blank form.
			//       The validity of par_sFileName is not checked to allow
			//       defining forms for files that haven't been added to the schema yet.
			//EXAMPLE OF EMPTY FORM WITH Links and Properties tabs:
			//001001=[blank cell]
			//011001=LNK_CreatedBy_US,Created By,,1,2
			//021001=sys_name,Name,,,3
			//021002=DTE_CreationTime,Created
			//021003=DTE_ModTime,Modified
			//021004=GID_ID,Record ID,,,3
			//022001=[colspan placeholder]
			//022002=TME_CreationTime
			//022003=TME_ModTime
			//022004=[ColSpan Placeholder]
			//023001=[colspan placeholder]
			//023002=LNK_CreatedBy_US,,,,2
			//023003=TXT_ModBy
			//023004=[ColSpan Placeholder]
			//024001=[blank cell]
			//024002=[ColSpan Placeholder]
			//024003=[Blank Cell]
			//024004=[Blank Cell]
			//COL11LBLWIDTH = 100
			//COL11ROWCOUNT = 1
			//COL11WIDTH = 200
			//COL1LBLWIDTH = 100
			//COL1ROWCOUNT = 1
			//COL1WIDTH = 200
			//COL21LBLWIDTH = 100
			//COL21ROWCOUNT = 4
			//COL21WIDTH = 200
			//COL22LBLWIDTH = 0
			//COL22ROWCOUNT = 4
			//COL22WIDTH = 140
			//COL23LBLWIDTH = 0
			//COL23ROWCOUNT = 4
			//COL23WIDTH = 140
			//COL24LBLWIDTH = 100
			//COL24ROWCOUNT = 4
			//COL24WIDTH = 200
			//FILE=goData.GetLabelFromFileName(par_sFileName)
			//FILEID=par_sFileName
			//US_NAME=goData.GetLabelFromFileName(par_sFileName) & "Form"
			//TAB1LABEL = Links
			//TAB1POSITION = 1
			//TAB2LABEL = Properties
			//TAB2POSITION = 2
			//TABCOUNT = 2

			string sProc = "clDefaults::GetLabelDefaults";

			string sResult = "";
			string sFileLabel = "";

			goTR.StrWrite(ref sResult, "001001", "[blank cell]");
			goTR.StrWrite(ref sResult, "011001", "LNK_CreatedBy_US,Created By,,1,2");
			goTR.StrWrite(ref sResult, "021001", "sys_name,Name,,,3");
			goTR.StrWrite(ref sResult, "021002", "DTE_CreationTime,Created");
			goTR.StrWrite(ref sResult, "021003", "DTE_ModTime,Modified");
			goTR.StrWrite(ref sResult, "021004", "GID_ID,Record ID,,,3");
			goTR.StrWrite(ref sResult, "022001", "[colspan placeholder]");
			goTR.StrWrite(ref sResult, "022002", "TME_CreationTime");
			goTR.StrWrite(ref sResult, "022003", "TME_ModTime");
			goTR.StrWrite(ref sResult, "022004", "[ColSpan Placeholder]");
			goTR.StrWrite(ref sResult, "023001", "[colspan placeholder]");
			goTR.StrWrite(ref sResult, "023002", "LNK_CreatedBy_US,,,,2");
			goTR.StrWrite(ref sResult, "023003", "TXT_ModBy");
			goTR.StrWrite(ref sResult, "023004", "[ColSpan Placeholder]");
			goTR.StrWrite(ref sResult, "024001", "[blank cell]");
			goTR.StrWrite(ref sResult, "024002", "[ColSpan Placeholder]");
			goTR.StrWrite(ref sResult, "024003", "[Blank Cell]");
			goTR.StrWrite(ref sResult, "024004", "[Blank Cell]");
			goTR.StrWrite(ref sResult, "COL11LBLWIDTH", "100");
			goTR.StrWrite(ref sResult, "COL11ROWCOUNT", "1");
			goTR.StrWrite(ref sResult, "COL11WIDTH", "200");
			goTR.StrWrite(ref sResult, "COL1LBLWIDTH", "100");
			goTR.StrWrite(ref sResult, "COL1ROWCOUNT", "1");
			goTR.StrWrite(ref sResult, "COL1WIDTH", "200");
			goTR.StrWrite(ref sResult, "COL21LBLWIDTH", "100");
			goTR.StrWrite(ref sResult, "COL21ROWCOUNT", "4");
			goTR.StrWrite(ref sResult, "COL21WIDTH", "200");
			goTR.StrWrite(ref sResult, "COL22LBLWIDTH", "0");
			goTR.StrWrite(ref sResult, "COL22ROWCOUNT", "4");
			goTR.StrWrite(ref sResult, "COL22WIDTH", "140");
			goTR.StrWrite(ref sResult, "COL23LBLWIDTH", "0");
			goTR.StrWrite(ref sResult, "COL23ROWCOUNT", "4");
			goTR.StrWrite(ref sResult, "COL23WIDTH", "140");
			goTR.StrWrite(ref sResult, "COL24LBLWIDTH", "100");
			goTR.StrWrite(ref sResult, "COL24ROWCOUNT", "4");
			goTR.StrWrite(ref sResult, "COL24WIDTH", "200");
			sFileLabel = goData.GetFileLabelFromName(par_sFileName);
			goTR.StrWrite(ref sResult, "FILEID", par_sFileName);
			if (sFileLabel.Trim(' ') == "")
			{
				goTR.StrWrite(ref sResult, "FILE", par_sFileName);
				goTR.StrWrite(ref sResult, "US_NAME", par_sFileName + " Form");
			}
			else
			{
				goTR.StrWrite(ref sResult, "FILE", sFileLabel);
				goTR.StrWrite(ref sResult, "US_NAME", sFileLabel + " Form");
			}
			goTR.StrWrite(ref sResult, "TAB1LABEL", "Links");
			goTR.StrWrite(ref sResult, "TAB1POSITION", "1");
			goTR.StrWrite(ref sResult, "TAB2LABEL", "Properties");
			goTR.StrWrite(ref sResult, "TAB2POSITION", "2");
			goTR.StrWrite(ref sResult, "TABCOUNT", "2");

			return sResult;
		}

		public string GetLabelDefaults(string par_sFileName)
		{
			//CS OK

			//Return default layout for label printing
			//
			//PARAMETERS:
			//		par_sFileName: name of the file for which to generate label defaults

			//Debugging the order of calls.
			string sProc = "clDefaults::GetLabelDefaults";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string s = null;
			string sFileName = par_sFileName.Trim(' ').ToUpper();

			switch (sFileName)
			{
				case "AC":
					s = "<%MMO_Address%>";
					break;
				case "AP":
					s = "<%DTE_StartTime%> <%TME_StartTime%>" + "\r\n";
					s += "<%MMO_Description%>" + "\r\n";
					s += "Coordinator: <%LNK_CoordinateBy_User%>";
					break;
				case "CO":
					s = "<%TXT_CompanyName%>" + "\r\n";
					s += "<%MMO_AddrMailing%>" + "\r\n";
					s += "<%TXT_CityMailing%>, <%TXT_StateMailing%> <%TXT_ZipMailing%>" + "\r\n";
					s += "<%TXT_CountryMailing%>";
					break;
				case "CN":
					s = "<%TXT_NameFirst%> <%TXT_NameLast%>" + "\r\n";
					s += "<%MMO_AddrBusiness%>" + "\r\n";
					s += "<%TXT_CityBusiness%>, <%TXT_StateBusiness%> <%TXT_ZipBusiness%>" + "\r\n";
					s += "<%TXT_CountryBusiness%>";
					break;
				case "MO":
					s = "<%TXT_ModelName%>" + "\r\n";
					s += "<%CUR_Price%>";
					break;
				case "PD":
					s = "<%TXT_ProductName%>";
					break;
				case "US":
					//		s = "<%TXT_NameFirst%> <%TXT_NameLast%>"&CR
					//		s&= "<%TXT_LogonName%>"&CR
					//		s&= "<%LNK_At_Location%>"
					s = "<%TXT_NameFirst%> <%TXT_NameLast%>" + "\r\n";
					s += "<%MMO_AddrBusiness%>" + "\r\n";
					s += "<%TXT_CityBusiness%>, <%TXT_StateBusiness%> <%TXT_ZipBusiness%>" + "\r\n";
					s += "<%TXT_CountryBusiness%>";
					break;
				default:
					s = "<%SYS_Name%>";
					break;
			}

			return s;
		}
		public string GetLinkBoxDefinitions(string par_sContent, object par_vParam1 = null)
		{
			//CS PORTED as placeholder.

			//---------
			//SET ERROR
			//---------
			//AUTHOR: FH - 11/5/2002 
			//MODIFIED by MI 9/16/05
			//PURPOSE:
			//		Get the default values for the aspect or behavior of a linkbox
			//		specified in the parameter par_sContent. This parameter can be:
			//			SELL_LBOX_FILTER (we want the filter metadata used in the linkbox)
			//			SELL_LBOX_COLFORM (a list of default columns to use in the linkbox form)
			//			SELL_LBOX_COLSEL (a list of default columns to use in the linkbox selector)
			//PARAMETERS:
			//		par_sContent:	Desired content. Can be SELL_LBOX_FILTER, SELL_LBOX_COLFORM, SELL_LBOX_COLSEL
			//		par_vParam1:	Depends on par_sContent:
			//						For SELL_LBOX_FILTER, par_vParam1 must be a file Name
			//						For SELL_LBOX_COLUMNS, par_vParam1 must be a file Name...
			//RETURNS:
			//		Data depending of sContent:
			//						For SELL_LBOX_FILTER, returns a filter metadata content (not pageID)
			//						For SELL_LBOX_COLUMNS, returns ???
			//		if empty, also set an error
			//HOW IT WORKS:
			//		To get the default value for all needs regarding the linkbox selector
			//EXAMPLE:
			//		goDefaults:GetLinkBoxDefinitions(SELL_LBOX_FILTER, "COMPANY")

			string sProc = "clDefaults::GetLinkBoxDefinitions";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "& par_sContent&" "&par_vParam1, SELL_LOGLEVEL_DETAILS)
			return ""; //CS remove

			//goLog:SetError()

			//sFileName is string	=	""
			//sResult is string	=	""
			//switch par_sContent

			//'------------------------------------------------------------------------------------------------------------------------------
			//case SELL_LBOX_FILTER	'Asking for the filter content
			//'------------------------------------------------------------------------------------------------------------------------------
			//	'Depend of the file
			//	sFileName=par_vParam1
			//	sFileName=nospace(upper(sFileName))
			//	if goData:GetFileIndex(sFileName)<1 then
			//		goLog:SetError(10100, sProc, "", par_vParam1)
			//		' Internal error: Incorrect file name. Please contact your system administrator and give him the file name causing the problem: '[1]'
			//		result ""		
			//	END
			//	'We could have a switch on the file here, but as it's the same for all FILES, for now:
			//	gotr.strwrite(sResult, "FILE", sFileName)
			//	gotr.strwrite(sResult, "KEYNAME", goData:GetDefaultKey(sFileName))
			//	if upper(goData:GetDefaultOrder(sFileName))="DESCENDING" then gotr.strwrite(sResult, "REVERSE", "1")
			//	gotr.strwrite(sResult, "CONDITION", "")		'Condition empty means ALL
			//	gotr.strwrite(sResult, "US_NAME", goData:GetFileLabelFromName(sFileName)&" - "&MessTraduit(5063))
			//'																					----- Anglais/English (3) -----
			//'																					All
			//'																					----- Francais/French (5) -----
			//'																					Tous
			//	result sResult

			//'------------------------------------------------------------------------------------------------------------------------------
			//case SELL_LBOX_COMBOLIKE, SELL_LBOX_TABLE, SELL_LBOX_LISTBOX, ...
			//	SELL_LBOX_ONEPANE, SELL_LBOX_TWOPANES, SELL_LBOX_TWOPANESALL 'Definitions for the table like display: titles visibles, multiple columns, horizontal and 
			//								'vertical scrollbar visible, columns resiezable, no horizontal or vertical separators visibles
			//'------------------------------------------------------------------------------------------------------------------------------
			//	'Depend of the file
			//	sFileName=par_vParam1
			//	sFileName=nospace(upper(sFileName))
			//	if goData:GetFileIndex(sFileName)<1 then
			//		goLog:SetError(10100, sProc, "", par_vParam1)
			//		' Internal error: Incorrect file name. Please contact your system administrator and give him the file name causing the problem: '[1]'
			//		result ""		
			//	END
			//	SWITCH sFileName
			//		CASE "ACTIVITY"
			//			'List type fields
			//			gotr.strwrite(sResult, "COLCOUNT", 6)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%DTE_StartTime%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"DTE_StartTime"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "10")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%TME_StartTime%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"TME_StartTime"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "9")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")
			//			gotr.strwrite(sResult, "COL3FIELD", "<%MLS_Type%>")
			//			gotr.strwrite(sResult, "COL3LABEL", goData:GetFieldLabelFromName(sFileName,"MLS_Type"))
			//			gotr.strwrite(sResult, "COL3WIDTH", "12")
			//			gotr.strwrite(sResult, "COL3ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL3TYPE", "T")
			//			gotr.strwrite(sResult, "COL4FIELD", "<%MMO_Notes%>")
			//			gotr.strwrite(sResult, "COL4LABEL", goData:GetFieldLabelFromName(sFileName,"MMO_Notes"))
			//			gotr.strwrite(sResult, "COL4WIDTH", "45")
			//			gotr.strwrite(sResult, "COL4ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL4TYPE", "T")
			//			gotr.strwrite(sResult, "COL5FIELD", "<%LNK_RELATED_CONTACT%>")
			//			gotr.strwrite(sResult, "COL5LABEL", goData:GetFieldLabelFromName(sFileName,"LNK_RELATED_CONTACT"))
			//			gotr.strwrite(sResult, "COL5WIDTH", "18")
			//			gotr.strwrite(sResult, "COL5ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL5TYPE", "T")
			//			gotr.strwrite(sResult, "COL6FIELD", "<%LNK_RELATED_COMPANY%>")
			//			gotr.strwrite(sResult, "COL6LABEL", goData:GetFieldLabelFromName(sFileName,"LNK_RELATED_COMPANY"))
			//			gotr.strwrite(sResult, "COL6WIDTH", "24")
			//			gotr.strwrite(sResult, "COL6ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL6TYPE", "T")

			//		CASE "APP"
			//			gotr.strwrite(sResult, "COLCOUNT", 5)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%DTE_StartTime%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"DTE_StartTime"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "10")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%TME_StartTime%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"TME_StartTime"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "9")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")
			//			gotr.strwrite(sResult, "COL3FIELD", "<%LNK_CoordinatedBy_User%>")
			//			gotr.strwrite(sResult, "COL3LABEL", goData:GetFieldLabelFromName(sFileName,"LNK_CoordinatedBy_User"))
			//			gotr.strwrite(sResult, "COL3WIDTH", "14")
			//			gotr.strwrite(sResult, "COL3ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL3TYPE", "T")
			//			gotr.strwrite(sResult, "COL4FIELD", "<%MMO_Description%>")
			//			gotr.strwrite(sResult, "COL4LABEL", goData:GetFieldLabelFromName(sFileName,"MMO_Description"))
			//			gotr.strwrite(sResult, "COL4WIDTH", "50")
			//			gotr.strwrite(sResult, "COL4ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL4TYPE", "T")
			//			gotr.strwrite(sResult, "COL5FIELD", "<%MLS_Type%>")
			//			gotr.strwrite(sResult, "COL5LABEL", goData:GetFieldLabelFromName(sFileName,"MLS_Type"))
			//			gotr.strwrite(sResult, "COL5WIDTH", "16")
			//			gotr.strwrite(sResult, "COL5ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL5TYPE", "T")

			//		CASE "COMPANY"
			//			gotr.strwrite(sResult, "COLCOUNT", 4)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%TXT_CompanyName%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_CompanyName"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "24")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%TXT_CityMailing%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_CityMailing"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "14")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")
			//			gotr.strwrite(sResult, "COL3FIELD", "<%TXT_StateMailing%>")
			//			gotr.strwrite(sResult, "COL3LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_StateMailing"))
			//			gotr.strwrite(sResult, "COL3WIDTH", "5")
			//			gotr.strwrite(sResult, "COL3ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL3TYPE", "T")
			//			gotr.strwrite(sResult, "COL4FIELD", "<%MMO_Note%>")
			//			gotr.strwrite(sResult, "COL4LABEL", goData:GetFieldLabelFromName(sFileName,"MMO_Note"))
			//			gotr.strwrite(sResult, "COL4WIDTH", "60")
			//			gotr.strwrite(sResult, "COL4ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL4TYPE", "T")

			//		CASE "CONTACT"
			//			gotr.strwrite(sResult, "COLCOUNT", 7)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%TXT_NameLast%>, <%TXT_NameFirst%> <%TXT_Nickname%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"SYS_Name"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "20")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%TEL_BusPhone%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"TEL_BusPhone"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "18")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")
			//			gotr.strwrite(sResult, "COL3FIELD", "<%TXT_TitleText%>")
			//			gotr.strwrite(sResult, "COL3LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_TitleText"))
			//			gotr.strwrite(sResult, "COL3WIDTH", "18")
			//			gotr.strwrite(sResult, "COL3ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL3TYPE", "T")
			//			gotr.strwrite(sResult, "COL4FIELD", "<%TXT_CityBusiness%>")
			//			gotr.strwrite(sResult, "COL4LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_City"))
			//			gotr.strwrite(sResult, "COL44WIDTH", "14")
			//			gotr.strwrite(sResult, "COL4ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL4TYPE", "T")
			//			gotr.strwrite(sResult, "COL5FIELD", "<%TXT_StateBusiness%>")
			//			gotr.strwrite(sResult, "COL5LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_State"))
			//			gotr.strwrite(sResult, "COL54WIDTH", "5")
			//			gotr.strwrite(sResult, "COL5ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL5TYPE", "T")
			//			gotr.strwrite(sResult, "COL6FIELD", "<%TXT_CountryBusiness%>")
			//			gotr.strwrite(sResult, "COL6LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_Country"))
			//			gotr.strwrite(sResult, "COL64WIDTH", "16")
			//			gotr.strwrite(sResult, "COL6ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL6TYPE", "T")
			//			gotr.strwrite(sResult, "COL7FIELD", "<%MMO_Note%>")
			//			gotr.strwrite(sResult, "COL7LABEL", goData:GetFieldLabelFromName(sFileName,"MMO_Note"))
			//			gotr.strwrite(sResult, "COL74WIDTH", "30")
			//			gotr.strwrite(sResult, "COL7ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL7TYPE", "T")

			//		case "CONFROOM"					'	_TUP_ConfRoomName === 
			//			gotr.strwrite(sResult, "COLCOUNT", 1)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%TXT_ConfRoomName%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_ConfRoomName"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "60")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")

			//		case "DELIVERY"					'	_TUP_DelMethName ==== 
			//			gotr.strwrite(sResult, "COLCOUNT", 1)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%TXT_DelMethName%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_DelMethName"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "60")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")

			//		case "DIVISION"					'	 _TUP_DivisionName === 
			//			gotr.strwrite(sResult, "COLCOUNT", 1)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%TXT_DivisionName%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_DivisionName"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "60")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")

			//		case "DOCUMENT"					'	DTE_Time, TME_Time === 
			//			gotr.strwrite(sResult, "COLCOUNT", 2)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%TXT_DocumentName%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_DocumentName"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "40")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%LNK_CreatedBy_User%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"LNK_CreatedBy_User"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "16")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")

			//		case "EXPENSE"					'	 === 
			//			gotr.strwrite(sResult, "COLCOUNT", 7)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%DTE_TIME%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"DTE_TIME"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "12")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%LNK_FOR_USER%%TXT_CODE%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"LNK_FOR_USER"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "9")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")
			//			gotr.strwrite(sResult, "COL3FIELD", "<%LNK_TO_VENDOR%>")
			//			gotr.strwrite(sResult, "COL3LABEL", goData:GetFieldLabelFromName(sFileName,"LNK_TO_VENDOR"))
			//			gotr.strwrite(sResult, "COL3WIDTH", "24")
			//			gotr.strwrite(sResult, "COL3ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL3TYPE", "T")
			//			gotr.strwrite(sResult, "COL4FIELD", "<%CUR_AMOUNT%>")
			//			gotr.strwrite(sResult, "COL4LABEL", goData:GetFieldLabelFromName(sFileName,"CUR_AMOUNT"))
			//			gotr.strwrite(sResult, "COL4WIDTH", "14")
			//			gotr.strwrite(sResult, "COL4ALIGNMENT", "R")
			//			gotr.strwrite(sResult, "COL4TYPE", "T")
			//			gotr.strwrite(sResult, "COL5FIELD", "<%MLS_TYPE%>")
			//			gotr.strwrite(sResult, "COL5LABEL", goData:GetFieldLabelFromName(sFileName,"MLS_TYPE"))
			//			gotr.strwrite(sResult, "COL5WIDTH", "22")
			//			gotr.strwrite(sResult, "COL5ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL5TYPE", "T")
			//			gotr.strwrite(sResult, "COL6FIELD", "<%CHK_REIMBURSABLE%>")
			//			gotr.strwrite(sResult, "COL6LABEL", goData:GetFieldLabelFromName(sFileName,"CHK_REIMBURSABLE"))
			//			gotr.strwrite(sResult, "COL6WIDTH", "6")
			//			gotr.strwrite(sResult, "COL6ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL6TYPE", "T")
			//			gotr.strwrite(sResult, "COL7FIELD", "<%CHK_BILLABLE%>")
			//			gotr.strwrite(sResult, "COL7LABEL", goData:GetFieldLabelFromName(sFileName,"CHK_BILLABLE"))
			//			gotr.strwrite(sResult, "COL7WIDTH", "6")
			//			gotr.strwrite(sResult, "COL7ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL7TYPE", "T")


			//		case "FILELIST"					'	TXT_FileName, TXT_Description === 
			//			gotr.strwrite(sResult, "COLCOUNT", 2)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%TXT_FileName%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_FileName"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "14")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%TXT_Description%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_Description"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "50")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")

			//		case "GROUP"
			//			gotr.strwrite(sResult, "COLCOUNT", 3)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%TXT_GroupName%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_GroupName"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "40")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%MMO_Description%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"MMO_Description"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "45")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")
			//			gotr.strwrite(sResult, "COL3FIELD", "<%LNK_RELATED_USER%>")
			//			gotr.strwrite(sResult, "COL3LABEL", goData:GetFieldLabelFromName(sFileName,"LNK_RELATED_USER"))
			//			gotr.strwrite(sResult, "COL3WIDTH", "20")
			//			gotr.strwrite(sResult, "COL3ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL3TYPE", "T")

			//		CASE "INDUSTRY"
			//			'TXT_IndustryCode
			//			gotr.strwrite(sResult, "COLCOUNT", 2)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%TXT_IndustryName%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_IndustryName"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "40")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%TXT_IndustryCode%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_IndustryCode"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "10")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")

			//		CASE "MODEL"
			//			gotr.strwrite(sResult, "COLCOUNT", 3)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%TXT_MODELNAME%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_MODELNAME"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "16")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%MMO_DESCRIPTION%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"MMO_DESCRIPTION"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "36")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")
			//			gotr.strwrite(sResult, "COL3FIELD", "<%CUR_PRICE%>")
			//			gotr.strwrite(sResult, "COL3LABEL", goData:GetFieldLabelFromName(sFileName,"CUR_PRICE"))
			//			gotr.strwrite(sResult, "COL3WIDTH", "14")
			//			gotr.strwrite(sResult, "COL3ALIGNMENT", "R")
			//			gotr.strwrite(sResult, "COL3TYPE", "T")

			//		CASE "OPP"
			//			gotr.strwrite(sResult, "COLCOUNT", 8)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%DTE_TIME%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"DTE_TIME"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "12")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%DTE_EXPCLOSEDATE%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"DTE_EXPCLOSEDATE"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "12")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")
			//			gotr.strwrite(sResult, "COL3FIELD", "<%LNK_CREDITEDTO_USER%%TXT_CODE%>")
			//			gotr.strwrite(sResult, "COL3LABEL", goData:GetFieldLabelFromName(sFileName,"LNK_CREDITEDTO_USER%%TXT_CODE"))
			//			gotr.strwrite(sResult, "COL3WIDTH", "6")
			//			gotr.strwrite(sResult, "COL3ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL3TYPE", "T")
			//			gotr.strwrite(sResult, "COL4FIELD", "<%LNK_FOR_COMPANY%>")
			//			gotr.strwrite(sResult, "COL4LABEL", goData:GetFieldLabelFromName(sFileName,"LNK_FOR_COMPANY"))
			//			gotr.strwrite(sResult, "COL4WIDTH", "18")
			//			gotr.strwrite(sResult, "COL4ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL4TYPE", "T")
			//			gotr.strwrite(sResult, "COL5FIELD", "<%LNK_FOR_PRODUCT%>")
			//			gotr.strwrite(sResult, "COL5LABEL", goData:GetFieldLabelFromName(sFileName,"LNK_FOR_PRODUCT"))
			//			gotr.strwrite(sResult, "COL5WIDTH", "24")
			//			gotr.strwrite(sResult, "COL5ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL5TYPE", "T")
			//			gotr.strwrite(sResult, "COL6FIELD", "<%CUR_VALUEINDEX%>")
			//			gotr.strwrite(sResult, "COL6LABEL", goData:GetFieldLabelFromName(sFileName,"CUR_VALUEINDEX"))
			//			gotr.strwrite(sResult, "COL6WIDTH", "15")
			//			gotr.strwrite(sResult, "COL6ALIGNMENT", "R")
			//			gotr.strwrite(sResult, "COL6TYPE", "T")
			//			gotr.strwrite(sResult, "COL7FIELD", "<%CUR_VALUE%>")
			//			gotr.strwrite(sResult, "COL7LABEL", goData:GetFieldLabelFromName(sFileName,"CUR_VALUE"))
			//			gotr.strwrite(sResult, "COL7WIDTH", "15")
			//			gotr.strwrite(sResult, "COL7ALIGNMENT", "R")
			//			gotr.strwrite(sResult, "COL7TYPE", "T")
			//			gotr.strwrite(sResult, "COL8FIELD", "<%TXT_DESCRIPTION%>")
			//			gotr.strwrite(sResult, "COL8LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_DESCRIPTION"))
			//			gotr.strwrite(sResult, "COL8WIDTH", "30")
			//			gotr.strwrite(sResult, "COL8ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL8TYPE", "T")

			//		CASE "PROJECT"
			//			gotr.strwrite(sResult, "COLCOUNT", 5)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%DTE_DATE%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"DTE_DATE"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "12")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%MLS_STATUS%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"MLS_STATUS"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "12")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")
			//			gotr.strwrite(sResult, "COL3FIELD", "<%LNK_ORIGINATEDBY_USER%%TXT_CODE%>")
			//			gotr.strwrite(sResult, "COL3LABEL", goData:GetFieldLabelFromName(sFileName,"LNK_ORIGINATEDBY_USER%%TXT_CODE"))
			//			gotr.strwrite(sResult, "COL3WIDTH", "6")
			//			gotr.strwrite(sResult, "COL3ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL3TYPE", "T")
			//			gotr.strwrite(sResult, "COL4FIELD", "<%TXT_PROJECTNAME%>")
			//			gotr.strwrite(sResult, "COL4LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_PROJECTNAME"))
			//			gotr.strwrite(sResult, "COL4WIDTH", "30")
			//			gotr.strwrite(sResult, "COL4ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL4TYPE", "T")
			//			gotr.strwrite(sResult, "COL5FIELD", "<%MLS_TYPE%>")
			//			gotr.strwrite(sResult, "COL5LABEL", goData:GetFieldLabelFromName(sFileName,"MLS_STATUS"))
			//			gotr.strwrite(sResult, "COL5WIDTH", "15")
			//			gotr.strwrite(sResult, "COL5ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL5TYPE", "T")

			//		CASE "QUOTE"
			//			gotr.strwrite(sResult, "COLCOUNT", 8)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%DTE_TIME%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"DTE_TIME"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "12")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%DTE_EXPCLOSEDATE%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"DTE_EXPCLOSEDATE"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "12")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")
			//			gotr.strwrite(sResult, "COL3FIELD", "<%MLS_STATUS%>")
			//			gotr.strwrite(sResult, "COL3LABEL", goData:GetFieldLabelFromName(sFileName,"MLS_STATUS"))
			//			gotr.strwrite(sResult, "COL3WIDTH", "12")
			//			gotr.strwrite(sResult, "COL3ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL3TYPE", "T")
			//			gotr.strwrite(sResult, "COL4FIELD", "<%LNK_CREDITEDTO_USER%%TXT_CODE%>")
			//			gotr.strwrite(sResult, "COL4LABEL", goData:GetFieldLabelFromName(sFileName,"LNK_CREDITEDTO_USER%%TXT_CODE"))
			//			gotr.strwrite(sResult, "COL4WIDTH", "6")
			//			gotr.strwrite(sResult, "COL4ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL4TYPE", "T")
			//			gotr.strwrite(sResult, "COL5FIELD", "<%LNK_PEER_USER%%TXT_CODE%>")
			//			gotr.strwrite(sResult, "COL5LABEL", goData:GetFieldLabelFromName(sFileName,"LNK_PEER_USER%%TXT_CODE"))
			//			gotr.strwrite(sResult, "COL5WIDTH", "6")
			//			gotr.strwrite(sResult, "COL5ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL5TYPE", "T")
			//			gotr.strwrite(sResult, "COL6FIELD", "<%CUR_TOTAL%>")
			//			gotr.strwrite(sResult, "COL6LABEL", goData:GetFieldLabelFromName(sFileName,"CUR_TOTAL"))
			//			gotr.strwrite(sResult, "COL6WIDTH", "15")
			//			gotr.strwrite(sResult, "COL6ALIGNMENT", "R")
			//			gotr.strwrite(sResult, "COL6TYPE", "T")
			//			gotr.strwrite(sResult, "COL7FIELD", "<%LNK_TO_COMPANY%>")
			//			gotr.strwrite(sResult, "COL7LABEL", goData:GetFieldLabelFromName(sFileName,"LNK_TO_COMPANY"))
			//			gotr.strwrite(sResult, "COL7WIDTH", "20")
			//			gotr.strwrite(sResult, "COL7ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL7TYPE", "T")
			//			gotr.strwrite(sResult, "COL8FIELD", "<%MMO_DESCRIPTION%>")
			//			gotr.strwrite(sResult, "COL8LABEL", goData:GetFieldLabelFromName(sFileName,"MMO_DESCRIPTION"))
			//			gotr.strwrite(sResult, "COL8WIDTH", "40")
			//			gotr.strwrite(sResult, "COL8ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL8TYPE", "T")

			//		CASE "QUOTLINE"
			//			'List type fields
			//			gotr.strwrite(sResult, "COLCOUNT", 7)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%CHK_Include%>")
			//			gotr.strwrite(sResult, "COL1LABEL", MessTraduit(5226))	'goData:GetFieldLabelFromName(sFileName,"CHK_Include"))
			//			'5226:Incl
			//			gotr.strwrite(sResult, "COL1WIDTH", "6")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%SR__LINENO%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"SR__LINENO"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "8")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "R")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")
			//			gotr.strwrite(sResult, "COL3FIELD", "<%TXT_MODEL%>")
			//			gotr.strwrite(sResult, "COL3LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_MODEL"))
			//			gotr.strwrite(sResult, "COL3WIDTH", "36")
			//			gotr.strwrite(sResult, "COL3ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL3TYPE", "T")
			//			gotr.strwrite(sResult, "COL4FIELD", "<%SR__QTY%>")
			//			gotr.strwrite(sResult, "COL4LABEL", goData:GetFieldLabelFromName(sFileName,"SR__QTY"))
			//			gotr.strwrite(sResult, "COL4WIDTH", "8")
			//			gotr.strwrite(sResult, "COL4ALIGNMENT", "R")
			//			gotr.strwrite(sResult, "COL4TYPE", "T")
			//			gotr.strwrite(sResult, "COL5FIELD", "<%TXT_UNIT%>")
			//			gotr.strwrite(sResult, "COL5LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_UNIT"))
			//			gotr.strwrite(sResult, "COL5WIDTH", "10")
			//			gotr.strwrite(sResult, "COL5ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL5TYPE", "T")
			//			gotr.strwrite(sResult, "COL6FIELD", "<%SR__DISCPERCENT%>")
			//			gotr.strwrite(sResult, "COL6LABEL", MessTraduit(5227))	'goData:GetFieldLabelFromName(sFileName,"SR__DISCPERCENT"))
			//			'5227:Disc %
			//			gotr.strwrite(sResult, "COL6WIDTH", "6")
			//			gotr.strwrite(sResult, "COL6ALIGNMENT", "R")
			//			gotr.strwrite(sResult, "COL6TYPE", "T")
			//			gotr.strwrite(sResult, "COL7FIELD", "<%CUR_SUBTOTAL%>")
			//			gotr.strwrite(sResult, "COL7LABEL", goData:GetFieldLabelFromName(sFileName,"CUR_SUBTOTAL"))
			//			gotr.strwrite(sResult, "COL7WIDTH", "16")
			//			gotr.strwrite(sResult, "COL7ALIGNMENT", "R")
			//			gotr.strwrite(sResult, "COL7TYPE", "T")

			//		case "TODO"
			//			gotr.strwrite(sResult, "COLCOUNT", 7)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%DTE_DUETIME%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"DTE_DUETIME"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "12")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%MLS_STATUS%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"MLS_STATUS"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "12")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")
			//			gotr.strwrite(sResult, "COL3FIELD", "<%LNK_CREATEDBY_USER%%TXT_CODE%>")
			//			gotr.strwrite(sResult, "COL3LABEL", MessTraduit(5245))	'goData:GetFieldLabelFromName(sFileName,"LNK_CREATEDBY_USER%%TXT_CODE"))
			//			'5245:From
			//			gotr.strwrite(sResult, "COL3WIDTH", "6")
			//			gotr.strwrite(sResult, "COL3ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL3TYPE", "T")
			//			gotr.strwrite(sResult, "COL4FIELD", "<%LNK_ASSIGNEDTO_USER%%TXT_CODE%>")
			//			gotr.strwrite(sResult, "COL4LABEL", MessTraduit(5246))	'goData:GetFieldLabelFromName(sFileName,"LNK_ASSIGNEDTO_USER%%TXT_CODE"))
			//			'5246:To
			//			gotr.strwrite(sResult, "COL4WIDTH", "6")
			//			gotr.strwrite(sResult, "COL4ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL4TYPE", "T")
			//			gotr.strwrite(sResult, "COL5FIELD", "<%MMO_DESCRIPTION%>")
			//			gotr.strwrite(sResult, "COL5LABEL", goData:GetFieldLabelFromName(sFileName,"MMO_DESCRIPTION"))
			//			gotr.strwrite(sResult, "COL5WIDTH", "40")
			//			gotr.strwrite(sResult, "COL5ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL5TYPE", "T")
			//			gotr.strwrite(sResult, "COL6FIELD", "<%MLS_PRIORITY%>")
			//			gotr.strwrite(sResult, "COL6LABEL", goData:GetFieldLabelFromName(sFileName,"MLS_PRIORITY"))
			//			gotr.strwrite(sResult, "COL6WIDTH", "8")
			//			gotr.strwrite(sResult, "COL6ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL6TYPE", "T")
			//			gotr.strwrite(sResult, "COL7FIELD", "<%MLS_TYPE%>")
			//			gotr.strwrite(sResult, "COL7LABEL", goData:GetFieldLabelFromName(sFileName,"MLS_TYPE"))
			//			gotr.strwrite(sResult, "COL7WIDTH", "14")
			//			gotr.strwrite(sResult, "COL7ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL7TYPE", "T")

			//		case "USER"
			//			gotr.strwrite(sResult, "COLCOUNT", 6)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%SYS_Name%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"SYS_Name"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "24")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL1TYPE", "T")
			//			gotr.strwrite(sResult, "COL2FIELD", "<%TXT_Code%>")
			//			gotr.strwrite(sResult, "COL2LABEL", goData:GetFieldLabelFromName(sFileName,"TXT_Code"))
			//			gotr.strwrite(sResult, "COL2WIDTH", "6")
			//			gotr.strwrite(sResult, "COL2ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL2TYPE", "T")
			//			gotr.strwrite(sResult, "COL3FIELD", "<%TEL_BUSPHONE%> <%TXT_BUSPHONEEXT%>")
			//			gotr.strwrite(sResult, "COL3LABEL", goData:GetFieldLabelFromName(sFileName,"TEL_BUSPHONE"))
			//			gotr.strwrite(sResult, "COL3WIDTH", "20")
			//			gotr.strwrite(sResult, "COL3ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL3TYPE", "T")
			//			gotr.strwrite(sResult, "COL4FIELD", "<%TEL_CELLPHONE%>")
			//			gotr.strwrite(sResult, "COL4LABEL", goData:GetFieldLabelFromName(sFileName,"TEL_CELLPHONE"))
			//			gotr.strwrite(sResult, "COL4WIDTH", "17")
			//			gotr.strwrite(sResult, "COL4ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL4TYPE", "T")
			//			gotr.strwrite(sResult, "COL5FIELD", "<%TEL_HOMEPHONE%>")
			//			gotr.strwrite(sResult, "COL5LABEL", goData:GetFieldLabelFromName(sFileName,"TEL_HOMEPHONE"))
			//			gotr.strwrite(sResult, "COL5WIDTH", "17")
			//			gotr.strwrite(sResult, "COL5ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL5TYPE", "T")
			//			gotr.strwrite(sResult, "COL6FIELD", "<%MMO_Note%>")
			//			gotr.strwrite(sResult, "COL6LABEL", goData:GetFieldLabelFromName(sFileName,"MMO_Note"))
			//			gotr.strwrite(sResult, "COL6WIDTH", "60")
			//			gotr.strwrite(sResult, "COL6ALIGNMENT", "L")
			//			gotr.strwrite(sResult, "COL6TYPE", "T")

			//		OTHER CASE
			//			gotr.strwrite(sResult, "COLCOUNT", 1)
			//			gotr.strwrite(sResult, "COL1FIELD", "<%SYS_Name%>")
			//			gotr.strwrite(sResult, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"SYS_Name"))
			//			gotr.strwrite(sResult, "COL1WIDTH", "60")
			//			gotr.strwrite(sResult, "COL1ALIGNMENT", "L")
			//	END
			//	result sResult


			//other case
			//	goLog:SetError(10103, sProc, "", "par_sContent", sProc, par_sContent)
			//	' Internal error: unsupported parameter value. The parameter '[1]' received in '[2]' is equal to '[3]'. This value is not supported.
			//	'
			//	'Please contact your system administrator.
			//	result ""	
			//END
		}

		public string GetDateRangeLabelByKey(string par_sKey)
		{
			string sProc = "clDefaults::GetDateRangeLabelByKey";
			try
			{

				DataTable oTable = GetDateRanges();
				DataRow[] oRows = oTable.Select("KEY='" + par_sKey + "'");
// INSTANT C# NOTE: Commented this declaration since looping variables in 'foreach' loops are declared in the 'foreach' header in C#:
//				Dim oRow As DataRow
				string sReturn = "";
				if (oRows.Length == 1)
				{
					foreach (DataRow oRow in oRows)
					{
						sReturn = oRow["VALUE"].ToString();
					}
				}

				return sReturn;

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, , sProc)
				//End If
				return "";
			}
		}

		public DataTable GetDateRangeGroups(string par_sFor)
		{
			DataTable tempVar = null;
			return GetDateRangeGroups(par_sFor, ref tempVar);
		}

		public DataTable GetDateRangeGroups()
		{
			DataTable tempVar = null;
			return GetDateRangeGroups("DRS", ref tempVar);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Public Function GetDateRangeGroups(Optional ByVal par_sFor As String = "DRS", Optional ByRef par_oDataTable As DataTable = Nothing) As DataTable
		public DataTable GetDateRangeGroups(string par_sFor, ref DataTable par_oDataTable)
		{
			string sProc = "clDefaults::GetDateRangeGroups";
			//MI 7/30/12 Created.
			//PURPOSE:
			//       Generates a new datatable that contains date range groups and labels
			//       ready for binding to a control. The groups can either execute directly
			//       (SUBMENUOPTIONS colum is blank) or pop out a submenu of choices
			//       (SUBMENUOPTIONS=ITEM1|ITEM2|ITEM3...). The choices are read from GetDateRanges.
			//PARAMETERS:
			//       par_sFor: Purpose for the list. Supported values:
			//           DRS         'date range selector (default)
			//           FILTER      'filter condition for DTT fields
			//       par_oDataTable: DataTable in which rows will be appended.
			//           If not provided, a new datatable is created and returned,
			//           else the rows are appended to this datatable. In this case
			//           both this parameter and the result contain the appended rows.
			//RETURNS:
			//       DataTable containing system date range names as KEY and
			//           and date range labels as VALUE.
			try
			{
				//*** KEEP THIS IN SYNC WITH GetDateRanges and clTransform.GetDateRangeTime! ***
				//Date Range combo with selections like this/previous/next week/month/quarter/year, etc.
				DataTable oDT = null;
				if (par_oDataTable == null)
				{
					//Create new datatable
					oDT = new DataTable();
				}
				else
				{
					//Use the datatable passed via parameter
					oDT = par_oDataTable;
				}
				oDT.Columns.Add("KEY", System.Type.GetType("System.String"));
				oDT.Columns.Add("VALUE", System.Type.GetType("System.String"));
				oDT.Columns.Add("SUBMENUOPTIONS", System.Type.GetType("System.String"));

				DataRow oRow;
				oRow = oDT.NewRow();
				oRow["KEY"] = "MAKESELECTION";
				oRow["VALUE"] = "<Make selection>";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "TODAY";
				oRow["VALUE"] = "Today";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "YESTERDAY";
				oRow["VALUE"] = "Yesterday";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "TOMORROW";
				oRow["VALUE"] = "Tomorrow";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "THIS";
				oRow["VALUE"] = "This";
				oRow["SUBMENUOPTIONS"] = "THISWEEK|THIS2WEEKS|THISMONTH|THISQUARTER|HALFYEAR|THISYEAR";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "LAST";
				oRow["VALUE"] = "Last";
				oRow["SUBMENUOPTIONS"] = "LASTWEEK|LAST2WEEKS|LASTMONTH|LASTQUARTER|LASTYEAR|LAST30DAYS|LAST90DAYS|LAST180DAYS|LAST12MONTHS";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "NEXT";
				oRow["VALUE"] = "Next";
				oRow["SUBMENUOPTIONS"] = "NEXTWEEK|NEXT2WEEKS|NEXTMONTH|NEXTQUARTER|NEXTYEAR|NEXT30DAYS|NEXT90DAYS|NEXT180DAYS|NEXT12MONTHS";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "TODATE";
				oRow["VALUE"] = "To date";
				oRow["SUBMENUOPTIONS"] = "WEEKTODATE|MONTHTODATE|QUARTERTODATE|YEARTODATE";
				oDT.Rows.Add(oRow);

				if (par_sFor == "DRS")
				{
					oRow = oDT.NewRow();
					oRow["KEY"] = "ALL";
					oRow["VALUE"] = "All dates";
					oDT.Rows.Add(oRow);
				}

				if (par_sFor == "FILTER")
				{
					oRow = oDT.NewRow();
					oRow["KEY"] = "DRSDATES";
					oRow["VALUE"] = "Between date range selector dates";
					oDT.Rows.Add(oRow);
				}

				oRow = oDT.NewRow();
				oRow["KEY"] = "CUSTOM";
				oRow["VALUE"] = "Custom...";
				oDT.Rows.Add(oRow);

				return oDT;
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, , sProc)
				//End If
				return new DataTable();
			}
		}

		public DataTable GetDateRanges(string par_sFor)
		{
			DataTable tempVar = null;
			return GetDateRanges(par_sFor, ref tempVar);
		}

		public DataTable GetDateRanges()
		{
			DataTable tempVar = null;
			return GetDateRanges("DRS", ref tempVar);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Public Function GetDateRanges(Optional ByVal par_sFor As String = "DRS", Optional ByRef par_oDataTable As DataTable = Nothing) As DataTable
		public DataTable GetDateRanges(string par_sFor, ref DataTable par_oDataTable)
		{
			string sProc = "clDefaults::GetDateRanges";
			//MI 7/30/12 Added SHORTLABEL column.
			//MI 7/25/12 Added HALFYEAR, THIS2WEEKS, LAST2WEEKS, NEXT2WEEKS.
			//MI 7/19/12 Added NEXT180DAYS, LAST180DAYS.
			//MI 8/20/10 Added NEXT30DAYS, NEXT90DAYS, LAST30DAYS, LAST90DAYS.
			//MI 3/9/10 Added NEXT12MONTHS.
			//MI 11/26/08 Restructured to use Do Loop instead of For Next.
			//MI 11/26/08 Added par_sFor parameter
			//PURPOSE:
			//       Generates a new datatable that contains date ranges and labels
			//       ready for binding to a listbox or dropdownlist control.
			//PARAMETERS:
			//       par_sFor: Purpose for the list. Supported values:
			//           DRS         'date range selector (default)
			//           FILTER      'filter condition for DTT fields
			//       par_oDataTable: DataTable in which rows will be appended.
			//           If not provided, a new datatable is created and returned,
			//           else the rows are appended to this datatable. In this case
			//           both this parameter and the result contain the appended rows.
			//RETURNS:
			//       DataTable containing system date range names as KEY and
			//           and date range labels as VALUE.
			try
			{
				//*** KEEP THIS IN SYNC WITH clDef.GetDateRangeGroups and clTransform.GetDateRangeTime! ***
				//Date Range combo with selections like this/previous/next week/month/quarter/year, etc.
				DataTable oDT = null;
				if (par_oDataTable == null)
				{
					//Create new datatable
					oDT = new DataTable();
				}
				else
				{
					//Use the datatable passed via parameter
					oDT = par_oDataTable;
				}
				oDT.Columns.Add("KEY", System.Type.GetType("System.String"));
				oDT.Columns.Add("VALUE", System.Type.GetType("System.String"));
				oDT.Columns.Add("SHORTLABEL", System.Type.GetType("System.String"));

				DataRow oRow;
				oRow = oDT.NewRow();
				oRow["KEY"] = "MAKESELECTION";
				oRow["VALUE"] = "<Make selection>";
				oRow["SHORTLABEL"] = "<Make selection>";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "TODAY";
				oRow["VALUE"] = "Today";
				oRow["SHORTLABEL"] = "Today";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "THISWEEK";
				oRow["VALUE"] = "This week";
				oRow["SHORTLABEL"] = "Week";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "THIS2WEEKS";
				oRow["VALUE"] = "This 2 weeks";
				oRow["SHORTLABEL"] = "2 weeks";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "THISMONTH";
				oRow["VALUE"] = "This month";
				oRow["SHORTLABEL"] = "Month";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "THISQUARTER";
				oRow["VALUE"] = "This quarter";
				oRow["SHORTLABEL"] = "Quarter";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "HALFYEAR";
				oRow["VALUE"] = "This half year";
				oRow["SHORTLABEL"] = "Half year";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "THISYEAR";
				oRow["VALUE"] = "This year";
				oRow["SHORTLABEL"] = "Year";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "YESTERDAY";
				oRow["VALUE"] = "Yesterday";
				oRow["SHORTLABEL"] = "Yesterday";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "LASTWEEK";
				oRow["VALUE"] = "Last week";
				oRow["SHORTLABEL"] = "Week";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "LAST2WEEKS";
				oRow["VALUE"] = "Last 2 weeks";
				oRow["SHORTLABEL"] = "2 weeks";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "LASTMONTH";
				oRow["VALUE"] = "Last month";
				oRow["SHORTLABEL"] = "Month";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "LASTQUARTER";
				oRow["VALUE"] = "Last quarter";
				oRow["SHORTLABEL"] = "Quarter";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "LASTYEAR";
				oRow["VALUE"] = "Last year";
				oRow["SHORTLABEL"] = "Year";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "LAST30DAYS";
				oRow["VALUE"] = "Last 30 days";
				oRow["SHORTLABEL"] = "30 days";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "LAST90DAYS";
				oRow["VALUE"] = "Last 90 days";
				oRow["SHORTLABEL"] = "90 days";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "LAST180DAYS";
				oRow["VALUE"] = "Last 180 days";
				oRow["SHORTLABEL"] = "180 days";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "LAST12MONTHS";
				oRow["VALUE"] = "Last 12 months";
				oRow["SHORTLABEL"] = "12 months";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "TOMORROW";
				oRow["VALUE"] = "Tomorrow";
				oRow["SHORTLABEL"] = "Tomorrow";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "NEXTWEEK";
				oRow["VALUE"] = "Next week";
				oRow["SHORTLABEL"] = "Week";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "NEXT2WEEKS";
				oRow["VALUE"] = "Next 2 weeks";
				oRow["SHORTLABEL"] = "2 weeks";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "NEXTMONTH";
				oRow["VALUE"] = "Next month";
				oRow["SHORTLABEL"] = "Month";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "NEXTQUARTER";
				oRow["VALUE"] = "Next quarter";
				oRow["SHORTLABEL"] = "Quarter";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "NEXTYEAR";
				oRow["VALUE"] = "Next year";
				oRow["SHORTLABEL"] = "Year";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "NEXT30DAYS";
				oRow["VALUE"] = "Next 30 days";
				oRow["SHORTLABEL"] = "30 days";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "NEXT90DAYS";
				oRow["VALUE"] = "Next 90 days";
				oRow["SHORTLABEL"] = "90 days";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "NEXT180DAYS";
				oRow["VALUE"] = "Next 180 days";
				oRow["SHORTLABEL"] = "180 days";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "NEXT12MONTHS";
				oRow["VALUE"] = "Next 12 months";
				oRow["SHORTLABEL"] = "12 months";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "WEEKTODATE";
				oRow["VALUE"] = "Week to date";
				oRow["SHORTLABEL"] = "Week";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "MONTHTODATE";
				oRow["VALUE"] = "Month to date";
				oRow["SHORTLABEL"] = "Month";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "QUARTERTODATE";
				oRow["VALUE"] = "Quarter to date";
				oRow["SHORTLABEL"] = "Quarter";
				oDT.Rows.Add(oRow);

				oRow = oDT.NewRow();
				oRow["KEY"] = "YEARTODATE";
				oRow["VALUE"] = "Year to date";
				oRow["SHORTLABEL"] = "Year";
				oDT.Rows.Add(oRow);

				if (par_sFor == "DRS")
				{
					oRow = oDT.NewRow();
					oRow["KEY"] = "ALL";
					oRow["VALUE"] = "All dates";
					oRow["SHORTLABEL"] = "All dates";
					oDT.Rows.Add(oRow);
				}

				if (par_sFor == "FILTER")
				{
					oRow = oDT.NewRow();
					oRow["KEY"] = "DRSDATES";
					oRow["VALUE"] = "Between date range selector dates";
					oRow["SHORTLABEL"] = "Between date range selector dates";
					oDT.Rows.Add(oRow);
				}

				oRow = oDT.NewRow();
				oRow["KEY"] = "CUSTOM";
				oRow["VALUE"] = "Custom...";
				oRow["SHORTLABEL"] = "Custom...";
				oDT.Rows.Add(oRow);

				return oDT;
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, , sProc)
				//End If
				return new DataTable();
			}
		}


		public string GetMasterCurrencyInfo(string par_sParameter)
		{
			//CS OK

			//Return name, code, and symbol of the default master currency (U.S. Dollar)
			//
			//PARAMETERS:
			//		par_sParameter: type of information to return
			//			NAME
			//			CODE
			//			SYMBOL
			//			DECIMALS

			//Debugging the order of calls.
			string sProc = "clDefaults::GetMasterCurrencyInfo";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sParam = par_sParameter.ToUpper();

			switch (sParam)
			{
				case "NAME":
					return "U.S. Dollar"; //messtranslate (MessTraduit(5067)) '5067:U.S. Dollar
				case "US_NAME":
					return ("U.S. Dollar");
				case "CODE":
					return ("USD");
				case "SYMBOL":
					return ("$");
				case "DECIMALS":
					return ("2");
				default:
					//Info(goTr:MessComplete(MessTraduit(5068),sParam))
					//Microsoft.VisualBasic.Interaction.MsgBox(goTR.MessComplete("Unsupported parameter 'par_sParameter' passed to clDefaults::GetMasterCurrency(): [1]", sParam), 6, "Selltis");
					//5068:Unsupported parameter 'par_sParameter' passed to clDefaults::GetMasterCurrency(): [1]
					//
					//Please contact Selltis support.
					return ("");
			}
		}
		public string GetMetaLinkDefs(string par_sFileName, string par_sLinkName = "")
		{
			//CS PORTED as placeholder.

			//COMMENET FOR SELLSQL:
			//		Let's not support default values other than those defined in the 
			//		FLD metadata (as read on line 114). This will remove the confusion
			//		about where default values are coming from.
			//PURPOSE:
			//		Return either a list of links in which to set default values
			//		or a list of TIDs to link in the linkbox par_sLinkName.
			//		Be sure to code valid link names here, otherwise the calling
			//		method such as clForm::InitForm() will cause a crash.  We don't
			//		check for validity of each field here to speed up the processing.
			//PARAMETERS:
			//		par_sFileName: name of the file
			//		par_sLinkName: name of the link for which to return the default
			//			value. If blank, the method returns a tab-delimited list of
			//			links that have default values defined.
			//RETURNS:
			//		 - When par_sLinkName is blank, a tab-delimited list of links
			//		that have a default value defined; 
			//      - else a list of TIDs to link from par_sLinkName. The list of TIDs is also TAB delimited.
			//		- If the file name is invalid, returns a blank string. 
			//		- If the link name is provided, but invalid, returns a blank string. 
			//		- If there are no defined links with default values, returns a blank string.

			//Debugging the order of calls.
			string sProc = "clDefaults::GetMetaLinkDefs";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			return ""; //cs remove

			//sFileName is string = Upper(par_sFileName)
			//sLinkName is string = Upper(par_sLinkName)
			//sResult is string = ""
			//sMeLinks is string
			//sMeID is string

			//'-------------- SET SYSTEM-LEVEL DEFAULTS -------------
			//SWITCH par_sFileName
			//	CASE "ACTIVITY"
			//		SWITCH sLinkName
			//			CASE ""
			//				sResult = "LNK_CreditedTo_User"
			//			CASE "LNK_CREDITEDTO_USER"
			//				sResult = goTr:GetLineValue("<%MEID%>")		'goTr:FieldToString("<%ME%>")
			//		END
			//	CASE "APP"
			//		SWITCH sLinkName
			//			CASE ""
			//				sResult = "LNK_CoordinatedBy_User"&TAB&"LNK_Involves_User"
			//			CASE "LNK_COORDINATEDBY_USER", "LNK_INVOLVES_USER"
			//				sResult = goTr:GetLineValue("<%MEID%>")
			//		END
			//	CASE "COMPANY"
			//		SWITCH sLinkName
			//			CASE ""
			//				sResult = "LNK_TeamLeader_User"&TAB&"LNK_Involves_User"
			//			CASE "LNK_TEAMLEADER_USER", "LNK_INVOLVES_USER"
			//				sResult = goTr:GetLineValue("<%MEID%>")
			//		END
			//	CASE "CONTACT"
			//		SWITCH sLinkName
			//			CASE ""
			//				sResult = "LNK_Related_User"
			//			CASE "LNK_RELATED_USER"
			//				sResult = goTr:GetLineValue("<%MEID%>")
			//		END
			//	CASE "EXPENSE"
			//		SWITCH sLinkName
			//			CASE ""
			//				sResult = "LNK_For_User"
			//			CASE "LNK_FOR_USER"
			//				sResult = goTr:GetLineValue("<%MEID%>")
			//		END
			//	CASE "MESSAGE"
			//		SWITCH sLinkName
			//			CASE ""
			//				sResult = "LNK_CreatedBy_User"
			//			CASE "LNK_CREATEDBY_USER"
			//				sResult = goTr:GetLineValue("<%MEID%>")
			//		END
			//	CASE "OPP"
			//		SWITCH sLinkName
			//			CASE ""
			//				sResult = "LNK_CreditedTo_User"
			//			CASE "LNK_CREDITEDTO_USER"
			//				sResult = goTr:GetLineValue("<%MEID%>")
			//		END
			//	CASE "PROJECT"
			//		SWITCH sLinkName
			//			CASE ""
			//				sResult = "LNK_OriginatedBy_User"&TAB&"LNK_TeamLeader_User"&TAB&"LNK_Involves_User"
			//			CASE "LNK_ORIGINATEDBY_USER", "LNK_TEAMLEADER_USER", "LNK_INVOLVES_USER"
			//				sResult = goTr:GetLineValue("<%MEID%>")
			//		END
			//	CASE "QUOTE"
			//		SWITCH sLinkName
			//			CASE ""
			//				sResult = "LNK_CreditedTo_User"
			//			CASE "LNK_CREDITEDTO_USER"
			//				sResult = goTr:GetLineValue("<%MEID%>")
			//		END
			//'	CASE "QUOTLINE"
			//'		SWITCH sLinkName
			//'			CASE ""
			//'				sResult = "LNK_CreditedTo_User"
			//'			CASE "LNK_CREDITEDTO_USER"
			//'				sResult = goTr:GetLineValue("<%MEID%>")
			//'		END
			//	OTHER CASE
			//		'No default fields defined
			//		sResult = ""
			//END

			//'-------------- ADD LINKS DEFINED IN FLD_ METADATA -------------
			//sMeLinks=goMeta:LineRead("GLOBAL","FLD_"&sFileName,"LINKMELINKS","",True)
			//sMeLinks = Replace(sMeLinks,"|",TAB)
			//IF Right(sMeLinks,1)=TAB THEN
			//	sMeLinks = sMeLinks[[ to Length(sMeLinks)-1]]
			//END

			//SWITCH sLinkName
			//	CASE ""
			//		IF NoSpace(sMeLinks) <> "" THEN
			//			sResult &= TAB&sMeLinks
			//		END
			//	OTHER CASE
			//		IF Position(Upper(sMeLinks),Upper(sLinkName)) > 0 THEN
			//			sMeID = goTr:GetLineValue("<%MEID%>")
			//			IF Position(sResult,sMeID) < 1 THEN
			//				'Add Me ID only if it is not already in the result
			//				IF sResult = "" THEN
			//					sResult = sMeID
			//				ELSE
			//					'Append it to the result from above
			//					sResult &= TAB&sMeID
			//				END
			//			END
			//		END
			//END

			//RESULT sResult
		}
		public string GetMetaObjects()
		{
			//CS OK

			//Return a list of metadata objects supported in Transfer In/Out

			//For debugging of the order of calls.
			string sProc = "clDefaults::GetMetaObjects";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			InitMetaObjects();
			return sMetaObjects;
		}
		public string GetMetaTypeFromPrefix(string par_sPrefix, bool par_bInPlural = false)
		{
			//MI 1/14/09 Added ERR (commented)
			//MI 11/14/07 Added supporting full page IDs, not only 4-character prefixes.
			//           Added case VIE_
			//Get a friendly name of metadata type in the current language from its prefix.
			//
			//PARAMETERS:
			//		par_sPrefix: prefix with or without the trailing '_'
			//			Examples: "DSK", "DSK_", "VIE_", "VTM", "IMP_"...
			//		par_bInPlural: return names in plural (boolean)

			//For debugging of the order of calls.
			string sProc = "clDefaults::GetMetaTypeFromPrefix";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sPrefix = null;
			string sResult = null;

			if (par_sPrefix == null)
			{
				par_sPrefix = "";
			}
			sPrefix = par_sPrefix.Trim(' ').ToUpper();
			if (sPrefix.Length == 3)
			{
				sPrefix += "_";
			}
			sPrefix = goTR.GetPrefix(sPrefix);

			if (!par_bInPlural)
			{
				//Singular
				switch (sPrefix)
				{
					case "AGE_":
						sResult = "Automator"; //messtranslate MessTranslate(5025) '5005:Automator
						break;
					case "ALT_":
						sResult = "Alert"; //messtranslate MessTranslate(5180) '5180:Alert
						break;
					case "CND_":
						sResult = "Application Link Conduit"; //messtranslate MessTranslate(5061) '5061:Application Link Conduit
						break;
					case "CRL_":
						sResult = "Create Linked Definition"; //messtranslate MessTranslate(5173) '5173:Create Linked Definition
						break;
					case "CRU_":
						sResult = "Create Unlinked Definition"; //messtranslate MessTranslate(5215) '5215:Create Unlinked Definition
						break;
					case "CUR_":
						sResult = "Currency"; //messtranslate MessTranslate(5066) '5066:Currency
						break;
					case "DBI_":
						sResult = "Database Information"; //messtranslate MessTraduit(5228) '5228:Database Information
						break;
					case "DBB_":
						sResult = "Dock Bar Button"; //messtranslate MessTranslate(5045) '5045:Dock Bar Button
						break;
					case "DBS_":
						sResult = "Dock Bar Shortcut"; //messtranslate MessTranslate(5046) '5046:Dock Bar Shortcut
						break;
						//		CASE "DEV_"
						//			sResult=MessTraduit(5163)   '5163:External Device Properties
					case "DS__":
						sResult = "Data Source"; //messtranslate MessTranslate(5031) '5031:Data Source
						break;
					case "DSK_":
						sResult = "Desktop"; //messtranslate MessTranslate(5026) '5006:Desktop
						break;
						//Case "ERR_"
						//    sResult = "Error" 'messtranslate MessTranslate()
					case "EXP_":
						sResult = "Export Template"; //messtranslate MessTranslate(5027) '5007:Export Template
						break;
					case "FII_":
						sResult = "Field Icon Map"; //messtranslate MessTranslate(5151) '5151:Field Icon Map
						break;
					case "FLD_":
						sResult = "Field Label"; //messtranslate MessTranslate(5028) '5008:Field Label
						break;
						//		CASE "FLT_"
						//			sResult=MessTranslate(5029)   '5009:Filter
						//		CASE "FRM_"
						//			sResult=MessTranslate(5030)   '5010:Form Customization
					case "FRM_":
						sResult = "Form"; //messtranslate
						break;
					case "IMP_":
						sResult = "Import Template"; //messtranslate MessTranslate(5032) '5012:Import Template
						break;
						//		CASE "LBX_"
						//			sResult=MessTranslate(5178)   '5178:Linkbox Definition
					case "LBF_":
						sResult = "Linkbox Filter"; //messtranslate
						break;
					case "LNK_":
						sResult = "Custom Link Definition"; //messtranslate MessTranslate(5232) '5232:Custom Link Definition
						break;
					case "LST_":
						sResult = "List"; //messtranslate MessTranslate(5033) '5013:List
						break;
					case "OTH_":
						sResult = "Other"; //messtranslate MessTranslate(5014) '5014:Other
						break;
					case "PGL_":
						sResult = "Page Layout"; //messtranslate MessTranslate(5053) '5053:Page Layout
						break;
					case "POP_":
						sResult = "Personal Options"; //messtranslate MessTranslate(5015) '5015:Personal Options
						break;
					case "PTA_":
						sResult = "Automator Pointers"; //messtranslate MessTranslate(5212) '5212:Automator Pointers
						break;
					case "PTR_":
						sResult = "Synchronization Pointers"; //messtranslate MessTranslate(5213) '5213:Synchronization Pointers
						break;
					case "REC_":
						sResult = "Record Detail Options"; //messtranslate MessTranslate(5160) '5160:Record Detail Options
						break;
					case "SCR_":
						sResult = "Script"; //messtranslate MessTranslate(5036) '5016:Script
						break;
					case "SEC_":
						sResult = "Event Script Cursor Information"; //messtranslate MessTranslate(5217) '5217:Event Script Cursor Information
						break;
					case "SPC_":
						sResult = "Procedure Script Cursor Information"; //MessTranslate(5218) 5218:Procedure Script Cursor Information"
						break;
					case "SND_":
						sResult = "Send Template"; //MessTranslate(5047) '5047:Send Template
						break;
					case "SNQ_":
						sResult = "Send Queue Item";
						break;
					case "TMP_":
						sResult = "Temporary"; //MessTraduit(5161) '5161:Temporary
						break;
						//		CASE "VTM_"
						//			sResult=MessTranslate(5037)   '5017:View Template
					case "VIE_":
						sResult = "View";
						break;
					case "WCB_":
						sResult = "Web Client Button"; //MessTranslate(5241) '5241:Web Client Button
						break;
					case "WOP_":
						sResult = "Workgroup Options"; //MessTranslate(5018) '5018:Workgroup Options
						break;
					default:
						sResult = "Unsupported Object"; //MessTranslate(5019) '5019:unsupported object
						break;
				}
			}
			else
			{
				//Plural
				switch (sPrefix)
				{
					case "ALT_":
						sResult = "Alerts"; //MessTranslate(5244) '5244:Alerts
						break;
					case "AGE_":
						sResult = "Automators"; //MessTranslate(5021) '5021:Automators
						break;
					case "CND_":
						sResult = "Application Link Conduits"; //MessTranslate(5062) '5062:Application Link Conduits
						break;
					case "CRL_":
						sResult = "Create Linked Definitions"; //MessTranslate(5174) '5174:Create Linked Definitions
						break;
					case "CRU_":
						sResult = "Create Unlinked Definitions"; //MessTranslate(5216) '5216:Create Unlinked Definitions
						break;
					case "CUR_":
						sResult = "Currencies"; //MessTranslate(5065) '5065:Currencies
						break;
					case "DBI_":
						sResult = "Database Information"; //MessTraduit(5229) '5229:Database Information
						break;
					case "DBB_":
						sResult = "Dock Bar Buttons"; //messtranslate(5043) '5043:Dock Bar Buttons
						break;
					case "DBS_":
						sResult = "Dock Bar Shortcuts"; //MessTranslate(5044) '5044:Dock Bar Shortcuts
						break;
						//		CASE "DEV_"
						//			sResult=MessTraduit(5164)   '5164:External Device Properties
					case "DS__":
						sResult = "Data Sources"; //MessTranslate(5038) '5038:Data Sources
						break;
					case "DSK_":
						sResult = "Desktops"; //MessTranslate(5022) '5022:Desktops
						break;
					case "EXP_":
						sResult = "Export Templates"; //MessTranslate(5023) '5023:Export Templates
						break;
					case "FII_":
						sResult = "Field Icon Maps"; //MessTranslate(5152) '5152:Field Icon Maps
						break;
					case "FLD_":
						sResult = "Field Labels"; //MessTranslate(5024) '5024:Field Labels
						break;
						//		CASE "FLT_"
						//			sResult=MessTranslate(5034)   '5034:Filters
						//		CASE "FRM_"
						//			sResult=MessTranslate(5035)   '5035:Form Customizations
					case "FRM_":
						sResult = "Forms"; //messtranslate
						break;
					case "IMP_":
						sResult = "Import Templates"; //MessTranslate(5039) '5039:Import Templates
						break;
						//		CASE "LBX_"
						//			sResult=MessTranslate(5179)   '5179:Linkbox Definitions
					case "LBF_":
						sResult = "Linkbox Filters"; //MessTranslate
						break;
					case "LNK_":
						sResult = "Custom Link Definitions"; //MessTranslate(5069) '5069:Custom Link Definitions
						break;
					case "LST_":
						sResult = "Lists"; //MessTranslate(5040) '5040:Lists
						break;
					case "OTH_":
						sResult = "Other"; // MessTranslate(5014) '5014:Other
						break;
					case "PGL_":
						sResult = "Page Layouts"; //MessTranslate(5054) '5054:Page Layouts
						break;
					case "POP_":
						sResult = "Personal Options"; //MessTranslate(5015) '5015:Personal Options
						break;
					case "PTA_":
						sResult = "Automator Pointers"; //MessTranslate(5212) '5212:Automator Pointers
						break;
					case "PTR_":
						sResult = "Synchronization Pointers"; //MessTranslate(5213) '5213:Synchronization Pointers
						break;
					case "REC_":
						sResult = "Record Detail Options"; //MessTranslate(5160) '5160:Record Detail Options
						break;
					case "SCR_":
						sResult = "Scripts"; //MessTranslate(5041) '5041:Scripts
						break;
					case "SEC_":
						sResult = "Event Script Cursor Information"; //MessTranslate(5217) '5217:Event Script Cursor Information
						break;
					case "SPC_":
						sResult = "Procedure Script Cursor Information"; //MessTranslate(5218) '5218:Procedure Script Cursor Information
						break;
					case "SND_":
						sResult = "Send Templates"; //MessTranslate(5048) '5048:Send Templates
						break;
					case "SNQ_":
						sResult = "Send Queue Items";
						break;
					case "TMP_":
						sResult = "Temporary"; //MessTraduit(5162) '5162:Temporary
						break;
						//		CASE "VTM_"
						//			sResult=MessTranslate(5042)   '5042:View Templates
					case "VIE_":
						sResult = "Views";
						break;
					case "WCB_":
						sResult = "Web Client Buttons"; //MessTranslate(5242) '5242:Web Client Buttons
						break;
					case "WOP_":
						sResult = "Workgroup Options 'MessTranslate(5018)   '5018:Workgroup Options";
						break;
					default:
						sResult = "Unsupported Objects"; //MessTranslate(5019) '5019:unsupported object
						break;
				}
			}

			return (sResult);
		}
		public string GetPageDefinitions()
		{
			//CS OK

			//Return page size/dimension definitions for Page Layouts.

			//For debugging of the order of calls.
			string sProc = "clDefaults::GetPageDefinitions";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			InitPageDefinitions();
			return sPageDefinitions;
		}
		public int GetPageDimension(string par_sName, string par_sDimension, string par_sOrientation = "PORTRAIT", string par_sUnit = "INCHES")
		{
			//CS 9/8/06
			//CS PORTED as placeholder. Return 0.

			//Return the requested dimension of the page
			//
			//PARAMETERS:
			//		par_sName: name of the page size for which to return the dimension
			//			Example: "A4", "LETTER", "LEGAL", etc.
			//		par_sDimension: "W" or "H" - width or height
			//		par_sOrientation: Orientation of the page. Supported values:
			//			PORTRAIT (default)
			//			LANDSCAPE
			//		par_sUnit: unit of measure. Supported values:
			//			INCHES (default)
			//			MILLIMETERS
			//
			//RETURNS:
			//		Real containing the requested dimension or 0 if the value is invalid or 
			//		an error occurred.
			//
			//:sPageDefinitions format:
			//		PAGECODE&TAB&Page Description&TAB&WInInches&TAB&HInInches&TAB&WInmm&TAB&HInmm&CR

			//For debugging of the order of calls.
			string sProc = "clDefaults::GetPageDimension";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)



			int iPos = 0;
			int iCRPos = 0;
			string sLine = null;
			string sResult = null;

			InitPageDefinitions();

			if (par_sName == "-1")
			{
				return(0);
			}

			iPos = sPageDefinitions.IndexOf(par_sName.Trim(' ').ToUpper()) + 1;
			if (iPos == 0)
			{
				return(0);
			}

			iCRPos = sPageDefinitions.IndexOf("\r\n", iPos) + 1;
			if (iCRPos == 0)
			{
				sLine = goTR.FromTo(sPageDefinitions, iPos);
			}
			else
			{
				sLine = goTR.FromTo(sPageDefinitions, iPos, iCRPos - 1);
			}

			if (par_sUnit.Trim(' ').ToUpper() == "INCHES") //Inches
			{
				if (par_sOrientation == "PORTRAIT") //Portrait
				{
					if (par_sDimension == "H") //Height
					{
						sResult = goTR.ExtractString(sLine, 4);
					}
					else //width
					{
						sResult = goTR.ExtractString(sLine, 3);
					}
				}
				else //Landscape
				{
					if (par_sDimension == "H") //Height
					{
						sResult = goTR.ExtractString(sLine, 3);
					}
					else //width
					{
						sResult = goTR.ExtractString(sLine, 4);
					}
				}
			}
			else //Millimeters
			{
				if (par_sOrientation == "PORTRAIT") //Portrait
				{
					if (par_sDimension == "H") //Height
					{
						sResult = goTR.ExtractString(sLine, 6);
					}
					else //width
					{
						sResult = goTR.ExtractString(sLine, 5);
					}
				}
				else //Landscape
				{
					if (par_sDimension == "H") //Height
					{
						sResult = goTR.ExtractString(sLine, 5);
					}
					else //width
					{
						sResult = goTR.ExtractString(sLine, 6);
					}
				}
			}

			if (sResult[0] == clC.EOT)
			{
				return (0);
			}
			else
			{
				return Convert.ToInt32((NumericHelper.Val(sResult)));
			}
		}
		public string GetPersonalOptionDefs()
		{
			//CS OK

			//Retrieve personal options default properties

			//For debugging of the order of calls.
			string sProc = "clDefaults::GetPersonalOptionDefaults";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//Initialize defaults
			InitPersonalOptions();

			return sPersonalOptions;
		}
		public string GetPrnTemplTypeName(string par_sCode)
		{
			//CS OK
			//PURPOSE:
			//		Return the friendly name of the Print Template type ("available for")
			//		based on the code provided in par_sCode.
			//
			//PARAMETERS:
			//		par_sCode: Code of the Print Template Type
			//
			//RETURNS:
			//		Friendly name of the Print Template type or "" if the code is
			//		not supported.

			//For debugging of the order of calls.
			string sProc = "clDefaults::GetPrnTemplTypeNameFromCode";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sCode = par_sCode.Trim(' ').ToUpper();

			switch (sCode)
			{
				case "CORR":
					return "Correspondence"; // MessTranslate(5049) '5049:Correspondence
				case "QUOTES":
					return "Price Quotes"; // MessTranslate(5050) '5050:Price Quotes
				case "VIEWS":
					return "View"; // MessTranslate(5051) '5051:Views
				case "FORMS":
					return "Record Details"; //MessTranslate(5052) '5052:Record Details
				case "LABELS":
					return "Labels"; //messtranslate MessTraduit(5064) '5064:Labels
				default:
					return "";
			}
		}
		public string GetRecDetailDefs(string par_sFileName)
		{
			//CS OK

			//Get defaults for REC_ metadata. This is in the same format as
			//MFIELD View. See ::GetViewDefaults() and ::InitViewDefaults().

			//PARAMETERS:
			//		par_sFileName: name of the file
			//			This is needed for file-dependent defaults such as fields.

			//Debugging the order of calls.
			string sProc = "clDefaults::GetRecDetailDefs";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//Initialize View defaults for the View's File
			InitRecDetailDefs(par_sFileName);

			return sRecDetailDefs;
		}
		public string GetRegionalOptions()
		{
			//CS OK

			//PURPOSE:
			//		Get an ini-style string that contains all default regional options
			//		needed to populate the WREGION window. To get a one-line format
			//		string from this metadata, use goTr:IniToFormatString().
			//		To display sample formats, use goTr:GetSampleFormat().
			//		This method is called from :InitPersonalOptions().
			//		This method does not write the Name parameter so that the string can
			//		be merged with a complete Personal Options string.
			//RETURNS:
			//		Ini-style string with regional options.

			//Debugging the order of calls.
			string sProc = "clDefaults::GetRegionalOptions";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sVals = "";

			//=================== REGIONAL SETTINGS ==========================
			//----------- Numeric -------------
			goTR.StrWrite(ref sVals, "EDT_DECIMALSYMBOL", ".");
			goTR.StrWrite(ref sVals, "EDT_THOUSANDSEP", ",");
			goTR.StrWrite(ref sVals, "EDT_NEGATIVEBEFORE", "-");
			goTR.StrWrite(ref sVals, "EDT_NEGATIVEAFTER", "");
			goTR.StrWrite(ref sVals, "CHK_DISPLAYZERO", 0);
			//------------ Currency -------------
			goTR.StrWrite(ref sVals, "EDT_CURRENCYSYMBOL", "$");
			goTR.StrWrite(ref sVals, "EDT_CURRDECIMALSYMBOL", ".");
			goTR.StrWrite(ref sVals, "EDT_CURRDECIMALDIGITS", 2);
			goTR.StrWrite(ref sVals, "EDT_CURRTHOUSANDSEP", ",");
			goTR.StrWrite(ref sVals, "EDT_CURRNEGATIVEBEFORE", "(");
			goTR.StrWrite(ref sVals, "EDT_CURRNEGATIVEAFTER", ")");
			goTR.StrWrite(ref sVals, "SEL_CURRSYMBOLPLACEMENT", 1);
			goTR.StrWrite(ref sVals, "CHK_CURRDISPLAYZERO", 1);
			//----------------Date---------------------
			goTR.StrWrite(ref sVals, "CMB_DATESHORTDAY", 1);
			goTR.StrWrite(ref sVals, "CMB_DATESHORTMONTH", 1);
			goTR.StrWrite(ref sVals, "CMB_DATESHORTYEAR", 1);
			goTR.StrWrite(ref sVals, "EDT_DATESHORTSEPARATOR", "/"); //"/"
			goTR.StrWrite(ref sVals, "CMB_DATESHORTORDER", "2");
			goTR.StrWrite(ref sVals, "CMB_DATELONGDAY", 1);
			goTR.StrWrite(ref sVals, "EDT_DATELONGDAYSEPARATOR", ", "); //"," & Chr(160)
			goTR.StrWrite(ref sVals, "CMB_DATELONGMONTH", 3); //3
			goTR.StrWrite(ref sVals, "EDT_DATELONGMONTHSEP", " "); //Chr(160)
			goTR.StrWrite(ref sVals, "CMB_DATELONGYEAR", 1);
			goTR.StrWrite(ref sVals, "EDT_DATELONGYEARSEPARATOR", " "); //Chr(160)
			goTR.StrWrite(ref sVals, "CMB_DATELONGORDER", 2);
			goTR.StrWrite(ref sVals, "EDT_2DIGITYEAREND", "2029"); //"2029"
			//---------------Time ---------------------
			goTR.StrWrite(ref sVals, "CMB_TIMEHOUR", 1);
			goTR.StrWrite(ref sVals, "EDT_TIMESEPARATOR", ":"); //":"
			goTR.StrWrite(ref sVals, "CHK_TIMEDISPLAYSECONDS", 0); //0
			goTR.StrWrite(ref sVals, "SEL_TIME2412HOUR", 2); //2
			goTR.StrWrite(ref sVals, "EDT_TIMEAMSYMBOL", Microsoft.VisualBasic.Strings.Chr(160) + "AM"); //" AM"
			goTR.StrWrite(ref sVals, "EDT_TIMEPMSYMBOL", Microsoft.VisualBasic.Strings.Chr(160) + "PM"); //" PM"
			return sVals;
		}
		public string GetScriptHeader(string par_sName = "Script", string par_sType = "PROCEDURE", bool par_bFullScript = true)
		{
			//CS PORTED as placeholder.

			//PURPOSE:
			//		Return the default script header. If par_sType is not 'Procedure',
			//		the header returned is appropriate for the given script event type.
			//PARAMETERS:
			//		par_sName:	Name of the script (for the 'Procedure' statement). If the name contains
			//			'illegal' characters, they will be replaced with "_" characters.
			//		par_sType: Type of script, all uppercase. Supported values:
			//			<Any supported script event>	'ControlOnLeave, FormOnLoad, RecordOnSave, etc.
			//			PROCEDURE
			//		par_bFullScript: If true (default), the whole default script is returned, otherwise
			//			only the header.
			//RETURNS:
			//		String: The default script header.
			//EXAMPLE:
			//		goDef:GetScriptHeader("ACTIVITY_FormOnOpen","FormOnOpen")

			string sProc = "clDefaults::GetScriptHeader";
			//oLog is clLogObj(sProc, "Start" , SELL_LOGLEVEL_DEBUG)


			return "";
			//sType is string = Upper(par_sType)
			//sString is string
			//sName is string = :GenerateProcName(par_sName)

			//        '				"'Do not delete parameters for future compatibility."&CR&...
			//        '				"'Do not delete object parameters in the script."&CR&CR

			//        SWITCH(sType)
			//	CASE "DATABASEONOPEN", "DATABASEONCLOSE"
			//		sString="Procedure "&sName&"(par_doCallingObject=Null, par_doArray=Null, par_s1="""", par_s2="""", par_s3="""", par_s4="""", par_s5="""")"&CR&...
			//				"'par_doCallingObject: Unused."&CR&...
			//				"'par_doArray: Unused."&CR&...
			//				"'par_s1 to par_s5: Unused."&CR&CR
			//	CASE "FORMONLOADRECORD", "FORMONOPEN", "FORMONCANCEL", "FORMONSAVE", "FORMONSEND"
			//		sString="Procedure "&sName&"(par_doCallingObject=Null, par_doArray=Null, par_sFormAlias="""", par_s2="""", par_s3="""", par_s4="""", par_s5="""")"&CR&...
			//				"'par_doCallingObject: Form object calling this script. Do not delete in script!"&CR&...
			//				"'par_doArray: Unused."&CR&...
			//				"'par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'."&CR&...
			//				"'par_s2 to par_s5: Unused."&CR&CR
			//	CASE "FORMONBROWSE"
			//		sString="Procedure "&sName&"(par_doCallingObject=Null, par_doArray=Null, par_sFormAlias="""", par_sBrowse="""", par_s3="""", par_s4="""", par_s5="""")"&CR&...
			//				"'par_doCallingObject: Form object calling this script. Do not delete in script!"&CR&...
			//				"'par_doArray: Unused."&CR&...
			//				"'par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'."&CR&...
			//				"'par_sBrowse: 'FIRST', 'PREVIOUS', 'NEXT', 'LAST'."&CR&...
			//				"'par_s3 to par_s5: Unused."&CR&CR
			//	CASE "TABONCLICK"
			//		sString="Procedure "&sName&"(par_doCallingObject=Null, par_doArray=Null, par_sFormAlias="""", par_s2="""", par_s3="""", par_s4="""", par_s5="""")"&CR&...
			//				"'par_doCallingObject: Form object calling this script. Do not delete in script!"&CR&...
			//				"'par_doArray: Unused."&CR&...
			//				"'par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'."&CR&...
			//				"'par_s2 to par_s5: Unused."&CR&CR
			//	CASE "CONTROLONCHANGE"
			//		sString="Procedure "&sName&"(par_doCallingObject=Null, par_doArray=Null, par_sFormAlias="""", par_sFieldName="""", par_s3="""", par_s4="""", par_s5="""")"&CR&...
			//				"'par_doCallingObject: Form object calling this script. Do not delete in script!"&CR&...
			//				"'par_doArray: Unused."&CR&...
			//				"'par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'."&CR&...
			//				"'par_sFieldName: Name of the control, e.g. 'MMO_Notes'."&CR&...
			//				"'par_s3 to par_s5: Unused."&CR&CR
			//	CASE "CONTROLONENTER"
			//		sString="Procedure "&sName&"(par_doCallingObject=Null, par_doArray=Null, par_sFormAlias="""", par_sFieldName="""", par_s3="""", par_s4="""", par_s5="""")"&CR&...
			//				"'par_doCallingObject: Form object calling this script. Do not delete in script!"&CR&...
			//				"'par_doArray: Unused."&CR&...
			//				"'par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'."&CR&...
			//				"'par_sFieldName: Name of the control, e.g. 'MMO_Notes'."&CR&...
			//				"'par_s3 to par_s5: Unused."&CR&CR
			//	CASE "CONTROLONLEAVE"
			//		sString="Procedure "&sName&"(par_doCallingObject=Null, par_doArray=Null, par_sFormAlias="""", par_sFieldName="""", par_s3="""", par_s4="""", par_s5="""")"&CR&...
			//				"'par_doCallingObject: Form object calling this script. Do not delete in script!"&CR&...
			//				"'par_doArray: Unused."&CR&...
			//				"'par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'."&CR&...
			//				"'par_sFieldName: Name of the control, e.g. 'MMO_Notes'."&CR&...
			//				"'par_s3 to par_s5: Unused."&CR&CR
			//	CASE "RECORDONSAVE"
			//		sString="Procedure "&sName&"(par_doCallingObject=Null, par_doArray=Null, par_sMode="""", par_s2="""", par_s3="""", par_s4="""", par_s5="""")"&CR&...
			//				"'par_doCallingObject: Rowset object containing the record to be saved."&CR&...
			//				"'par_doArray: Unused."&CR&...
			//				"'par_sMode: 'CREATION' or 'MODIF'."&CR&...
			//				"'par_s2 to par_s5: Unused."&CR&CR
			//	CASE "RECORDONDELETE"
			//		sString="Procedure "&sName&"(par_doCallingObject=Null, par_doArray=Null, par_s1="""", par_s2="""", par_s3="""", par_s4="""", par_s5="""")"&CR&...
			//				"'par_doCallingObject: Rowset object containing the record to be saved."&CR&...
			//				"'par_doArray: Unused."&CR&...
			//				"'par_s1 to par_s5: Unused."&CR&CR
			//	CASE "PROCEDURE"
			//		sString="Procedure "&sName&"(par_doCallingObject=Null, par_doArray=Null, par_s1="""", par_s2="""", par_s3="""", par_s4="""", par_s5="""")"&Cr&...
			//				"'par_doCallingObject: Unused."&CR&...
			//				"'par_doArray: Unused."&CR&...
			//				"'par_s1: "&CR&...
			//				"'par_s2: "&CR&... 
			//				"'par_s3: "&CR&... 
			//				"'par_s4: "&CR&... 
			//				"'par_s5: "&CR&CR 
			//	OTHER CASE
			//        sString = ""
			//        End

			//sString&=	"sProc is string = ""Script::"&sName&""""&CR&...
			//			"IF gbWriteLog THEN oLog is clLogObj(sProc, ""Start"", 3)"&CR&...
			//			CR&...
			//			"'--- HISTORY ---"&CR&...
			//			goTr:GenerateHistoryLine(sName,"Created.")&CR

			//        If sType <> "RECORDONSAVE" And sType <> "RECORDONDELETE" Then
			//            sString &= "doForm is object dynamic = par_doCallingObject" & CR & CR
			//            End

			//            'sString&=	"'------------------- END OF STANDARD HEADER --------------------"&CR&CR

			//            If par_bFullScript Then
			//                sString &= "Result True" & rc
			//                End

			//                result(sString)
		}
		public string GetScriptWords(string par_sType = "ALL")
		{
			//MI 7/25/06 Added keywords for GetLinePart, GetLineValue.

			//8/19/04	CREATED 2/13/04 MI
			//PURPOSE:
			//		Get a list of "words" supported in the Selltis scripting language.
			//		This list is used to start context-sensitive help from the
			//		script editor. It _must_ be kept in sync with the supported
			//		context topics in the scripting documentation.
			//		Also get help topic numbers and objects under which the words
			//		belong.
			//		The list must have a trailing TAB for easier parsing of line elements.
			//PARAMETERS:
			//		par_sType: String: Type of words to return. Supported values:
			//			ALL				'All words
			//			METHODS			'Methods only
			//RETURNS:
			//		String: Tab-delimited list of supported words. Each word
			//		starts with: <help topic number>&"_"&<object name>&":"&<method name>.
			//		Example:
			//		1500_Array:Add&TAB&1501_Array:GetDimension&TAB&1502_Array:Get&TAB&...
			//IMPORTANT:
			//		Do not change the format of <help number>&"_"&<object>&":"&keyword
			//		because the format is relied on to find a topic number for a keyword.

			string sProc = "clDefaults::GetScriptWords";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string s = "";

			switch (par_sType)
			{
				case "METHODS":
					s += "1500_Array:Add" + "\t";
					s += "1501_Array:GetDimension" + "\t";
					s += "1502_Array:Get" + "\t";
					s += "1503_Array:Modify" + "\t";
					s += "1504_Array:Seek" + "\t";
					s += "1505_Form:ClearLink" + "\t";
					s += "1506_Form:ClearLinkAll" + "\t";
					s += "1507_Form:DeleteVar" + "\t";
					s += "1508_Form:Execute" + "\t";
					s += "1509_Form:GetControlState" + "\t";
					s += "1510_Form:GetDataFormat" + "\t";
					s += "1511_Form:GetFieldName" + "\t";
					s += "1512_Form:GetFieldVal" + "\t";
					s += "1513_Form:GetFileName" + "\t";
					//		s&="1514_Form:GetLinkColumns"&TAB
					s += "1515_Form:GetLinkFilter" + "\t";
					s += "1516_Form:GetLinkFilterState" + "\t";
					s += "1517_Form:GetLinkSelection" + "\t";
					s += "1518_Form:GetLinkVal" + "\t";
					s += "1519_Form:GetMode" + "\t";
					s += "1520_Form:GetRecordID" + "\t";
					s += "1521_Form:GetTab" + "\t";
					s += "1522_Form:GetVar" + "\t";
					s += "1523_Form:IsDirty" + "\t";
					s += "1524_Form:IsLinkEmpty" + "\t";
					s += "1525_Form:SetControlState" + "\t";
					s += "1526_Form:SetDataFormat" + "\t";
					s += "1527_Form:SetFieldVal" + "\t";
					s += "1528_Form:SetLinkColumns" + "\t";
					s += "1529_Form:SetLinkFilter" + "\t";
					s += "1530_Form:SetLinkFilterState" + "\t";
					s += "1531_Form:SetLinkVal" + "\t";
					s += "1532_Form:SetVar" + "\t";
					s += "1533_goErr:DisplayLastError" + "\t";
					s += "1534_goErr:DisplayLastErrorFriendly" + "\t";
					s += "1535_goErr:GetErrorMessage" + "\t";
					s += "1536_goErr:GetLastError" + "\t";
					s += "1537_goErr:SetError" + "\t";
					s += "1538_goProject:DeleteVar" + "\t";
					s += "1539_goProject:GetInfo" + "\t";
					s += "1540_goProject:GetVar" + "\t";
					s += "1541_goProject:SetVar" + "\t";
					s += "1542_Rowset:ClearLink" + "\t";
					s += "1543_Rowset:ClearLinkAll" + "\t";
					s += "1544_Rowset:Commit" + "\t";
					s += "1545_Rowset:DeleteRecord" + "\t";
					s += "1546_Rowset:DeleteVar" + "\t";
					//1547 can be reused
					s += "1548_Rowset:GetDataFormat" + "\t";
					s += "1549_Rowset:GetFieldVal" + "\t";
					s += "1550_Rowset:GetFirst" + "\t";
					s += "1551_Rowset:GetLast" + "\t";
					s += "1552_Rowset:GetLinkVal" + "\t";
					s += "1553_Rowset:GetMode" + "\t";
					s += "1554_Rowset:GetNext" + "\t";
					s += "1555_Rowset:GetPrevious" + "\t";
					s += "1556_Rowset:GetVar" + "\t";
					s += "1557_Rowset:IsLinkEmpty" + "\t";
					s += "1558_Rowset:SeekRecordByID" + "\t";
					s += "1559_Rowset:SetDataFormat" + "\t";
					s += "1560_Rowset:SetFieldVal" + "\t";
					s += "1561_Rowset:SetLinkVal" + "\t";
					s += "1562_Rowset:SetVar" + "\t";
					s += "1563_Form:SetFieldProperties" + "\t";
					s += "1564_Form:GetOpeningMode" + "\t";
					//1565 committed
					s += "1566_Form:CreateForm" + "\t";
					s += "1567_Form:CancelForm" + "\t";
					s += "1568_Form:DeleteForm" + "\t";
					s += "1569_Form:SaveForm" + "\t";
					s += "1570_Array:Clone" + "\t";
					//1571 committed
					s += "1572_Form:MoveToField" + "\t";
					s += "1573_Form:MoveToTab" + "\t";
					s += "1574_Form:GetLinkCountEx" + "\t";
					//1575 to 1610 committed
					s += "1611_Form:SetLinkByVal" + "\t";
					s += "1612_goTr:CheckboxToString" + "\t";
					s += "1613_goTr:CurrToString" + "\t";
					s += "1614_goTr:DateToString" + "\t";
					s += "1615_goTr:NumToString" + "\t";
					s += "1616_goTr:TimeToString" + "\t";
					s += "1617_goTr:StringToCheckbox" + "\t";
					s += "1618_goTr:StringToCurr" + "\t";
					s += "1619_goTr:StringToDate" + "\t";
					s += "1620_goTr:StringToNum" + "\t";
					s += "1621_goTr:StringToTime" + "\t";
					s += "1622_goTr:GetDay" + "\t";
					s += "1623_goTr:GetDayOfWeek" + "\t";
					s += "1624_goTr:GetMonth" + "\t";
					s += "1625_goTr:GetMonthAlpha" + "\t";
					s += "1626_goTr:GetYear" + "\t";
					s += "1627_Form:SetLinkProperty" + "\t";
					//1628-1629 committed
					s += "1630_Form:SetFieldLabelColor" + "\t";
					//1631-1633 committed
					s += "1634_OpenForm:DeleteVar" + "\t";
					s += "1635_OpenForm:GetVar" + "\t";
					s += "1636_OpenForm:SetFieldVal" + "\t";
					s += "1637_OpenForm:SetLinkVal" + "\t";
					s += "1638_OpenForm:SetVar" + "\t";
					s += "1639_Form:SetFieldProperty" + "\t";
					s += "1640_OpenForm:SetLinkByVal" + "\t";
					//1641-1643
					s += "1644_Form:CopyRowsetTo" + "\t";
					s += "1645_Rowset:CloneTo" + "\t";
					//1646, 1647
					s += "1648_goTr:UpperTxt" + "\t";
					s += "1649_Rowset:GetLinkCountEx" + "\t";
					s += "1650_Rowset:Count" + "\t";
					//1651
					s += "1652_Rowset:GetFileName" + "\t";
					//1653-1664
					s += "1665_Import:GetAscFieldValue" + "\t";
					s += "1666_Import:GetFieldVal" + "\t";
					s += "1667_Import:GetHFName" + "\t";
					s += "1668_Import:GetLinkVal" + "\t";
					s += "1669_Import:SetFieldVal" + "\t";
					s += "1670_Import:SetLinkVal" + "\t";
					s += "1671_Import:WriteIDToTrans" + "\t";
					//1672-1674
					s += "1675_goTr:DateToInteger" + "\t";
					s += "1676_goTr:TimeToInteger" + "\t";
					s += "1677_goTr:IntegerToDate" + "\t";
					s += "1678_goTr:IntegerToTime" + "\t";
					//1679-1681
					s += "1682_goTr:AddMinutes" + "\t";
					s += "1683_OpenForm:SetOpeningMode" + "\t";
					s += "1684_goTr:GetLinePart" + "\t";
					s += "1685_goTr:GetLineValue" + "\t";
					break;
				default:
					s += "1500_Array:Add" + "\t";
					s += "1501_Array:GetDimension" + "\t";
					s += "1502_Array:Get" + "\t";
					s += "1503_Array:Modify" + "\t";
					s += "1504_Array:Seek" + "\t";
					s += "1505_Form:ClearLink" + "\t";
					s += "1506_Form:ClearLinkAll" + "\t";
					s += "1507_Form:DeleteVar" + "\t";
					s += "1508_Form:Execute" + "\t";
					s += "1509_Form:GetControlState" + "\t";
					s += "1510_Form:GetDataFormat" + "\t";
					s += "1511_Form:GetFieldName" + "\t";
					s += "1512_Form:GetFieldVal" + "\t";
					s += "1513_Form:GetFileName" + "\t";
					//		s&="1514_Form:GetLinkColumns"&TAB
					s += "1515_Form:GetLinkFilter" + "\t";
					s += "1516_Form:GetLinkFilterState" + "\t";
					s += "1517_Form:GetLinkSelection" + "\t";
					s += "1518_Form:GetLinkVal" + "\t";
					s += "1519_Form:GetMode" + "\t";
					s += "1520_Form:GetRecordID" + "\t";
					s += "1521_Form:GetTab" + "\t";
					s += "1522_Form:GetVar" + "\t";
					s += "1523_Form:IsDirty" + "\t";
					s += "1524_Form:IsLinkEmpty" + "\t";
					s += "1525_Form:SetControlState" + "\t";
					s += "1526_Form:SetDataFormat" + "\t";
					s += "1527_Form:SetFieldVal" + "\t";
					s += "1528_Form:SetLinkColumns" + "\t";
					s += "1529_Form:SetLinkFilter" + "\t";
					s += "1530_Form:SetLinkFilterState" + "\t";
					s += "1531_Form:SetLinkVal" + "\t";
					s += "1532_Form:SetVar" + "\t";
					s += "1533_goErr:DisplayLastError" + "\t";
					s += "1534_goErr:DisplayLastErrorFriendly" + "\t";
					s += "1535_goErr:GetErrorMessage" + "\t";
					s += "1536_goErr:GetLastError" + "\t";
					s += "1537_goErr:SetError" + "\t";
					s += "1538_goProject:DeleteVar" + "\t";
					s += "1539_goProject:GetInfo" + "\t";
					s += "1540_goProject:GetVar" + "\t";
					s += "1541_goProject:SetVar" + "\t";
					s += "1542_Rowset:ClearLink" + "\t";
					s += "1543_Rowset:ClearLinkAll" + "\t";
					s += "1544_Rowset:Commit" + "\t";
					s += "1545_Rowset:DeleteRecord" + "\t";
					s += "1546_Rowset:DeleteVar" + "\t";
					//1547 can be reused
					s += "1548_Rowset:GetDataFormat" + "\t";
					s += "1549_Rowset:GetFieldVal" + "\t";
					s += "1550_Rowset:GetFirst" + "\t";
					s += "1551_Rowset:GetLast" + "\t";
					s += "1552_Rowset:GetLinkVal" + "\t";
					s += "1553_Rowset:GetMode" + "\t";
					s += "1554_Rowset:GetNext" + "\t";
					s += "1555_Rowset:GetPrevious" + "\t";
					s += "1556_Rowset:GetVar" + "\t";
					s += "1557_Rowset:IsLinkEmpty" + "\t";
					s += "1558_Rowset:SeekRecordByID" + "\t";
					s += "1559_Rowset:SetDataFormat" + "\t";
					s += "1560_Rowset:SetFieldVal" + "\t";
					s += "1561_Rowset:SetLinkVal" + "\t";
					s += "1562_Rowset:SetVar" + "\t";
					s += "1563_Form:SetFieldProperties" + "\t";
					s += "1564_Form:GetOpeningMode" + "\t";
					s += "1565_RunScript" + "\t";
					s += "1566_Form:CreateForm" + "\t";
					s += "1567_Form:CancelForm" + "\t";
					s += "1568_Form:DeleteForm" + "\t";
					s += "1569_Form:SaveForm" + "\t";
					s += "1570_Array:Clone" + "\t";
					s += "1571_WriteLogLine" + "\t";
					s += "1572_Form:MoveToField" + "\t";
					s += "1573_Form:MoveToTab" + "\t";
					s += "1574_Form:GetLinkCountEx" + "\t";
					s += "1575_MsgBox" + "\t";
					s += "1576_IsDate" + "\t";
					s += "1577_IsTime" + "\t";
					s += "1578_DateTimeDiff" + "\t";
					s += "1579_IsNumeric" + "\t";
					s += "1580_TraceLine" + "\t";
					s += "1581_GetMe" + "\t";
					s += "1582_Debug" + "\t";
					s += "1583_[[" + "\t";
					s += "1584_Left" + "\t";
					s += "1585_Position" + "\t";
					s += "1586_Right" + "\t";
					s += "1587_NoSpace" + "\t";
					s += "1588_ToClipboard" + "\t";
					s += "1589_Clipboard" + "\t";
					s += "1590_Upper" + "\t";
					s += "1591_Lower" + "\t";
					s += "1592_HourGlass" + "\t";
					s += "1593_Middle" + "\t";
					s += "1594_Asc" + "\t";
					s += "1595_Length" + "\t";
					s += "1596_Dimension" + "\t";
					s += "1597_Round" + "\t";
					s += "1598_Replace" + "\t";
					s += "1599_Charact" + "\t";
					s += "1600_PositionRev" + "\t";
					s += "1601_StrRead" + "\t";
					s += "1602_StrWrite" + "\t";
					s += "1603_StripIllegalChars" + "\t";
					s += "1604_ExtractString" + "\t";
					s += "1605_NumToString" + "\t";
					s += "1606_Val" + "\t";
					s += "1607_Complete" + "\t";
					s += "1608_IsObjectAssigned" + "\t";
					s += "1609_DateSys" + "\t";
					s += "1610_TimeSys" + "\t";
					s += "1611_Form:SetLinkByVal" + "\t";
					s += "1612_goTr:CheckboxToString" + "\t";
					s += "1613_goTr:CurrToString" + "\t";
					s += "1614_goTr:DateToString" + "\t";
					s += "1615_goTr:NumToString" + "\t";
					s += "1616_goTr:TimeToString" + "\t";
					s += "1617_goTr:StringToCheckbox" + "\t";
					s += "1618_goTr:StringToCurr" + "\t";
					s += "1619_goTr:StringToDate" + "\t";
					s += "1620_goTr:StringToNum" + "\t";
					s += "1621_goTr:StringToTime" + "\t";
					s += "1622_goTr:GetDay" + "\t";
					s += "1623_goTr:GetDayOfWeek" + "\t";
					s += "1624_goTr:GetMonth" + "\t";
					s += "1625_goTr:GetMonthAlpha" + "\t";
					s += "1626_goTr:GetYear" + "\t";
					s += "1627_Form:SetLinkProperty" + "\t";
					s += "1628_GetMapURL" + "\t";
					s += "1629_ShowMap" + "\t";
					s += "1630_Form:SetFieldLabelColor" + "\t";
					s += "1631_InputBox" + "\t";
					s += "1632_GetRunMode" + "\t";
					s += "1633_ShellExec" + "\t";
					s += "1634_OpenForm:DeleteVar" + "\t";
					s += "1635_OpenForm:GetVar" + "\t";
					s += "1636_OpenForm:SetFieldVal" + "\t";
					s += "1637_OpenForm:SetLinkVal" + "\t";
					s += "1638_OpenForm:SetVar" + "\t";
					s += "1639_Form:SetFieldProperty" + "\t";
					s += "1640_OpenForm:SetLinkByVal" + "\t";
					s += "1641_GetSelectedRecordID" + "\t";
					s += "1642_GetDuplicates" + "\t";
					s += "1643_IsDuplicate" + "\t";
					s += "1644_Form:CopyRowsetTo" + "\t";
					s += "1645_Rowset:CloneTo" + "\t";
					//==> Reuse 1646, 1647!
					s += "1648_goTr:UpperTxt" + "\t";
					s += "1649_Rowset:GetLinkCountEx" + "\t";
					s += "1650_Rowset:Count" + "\t";
					s += "1651_GetFieldLabel" + "\t";
					s += "1652_Rowset:GetFileName" + "\t";
					s += "1653_RunAction" + "\t";
					s += "1654_GetActionObject" + "\t";
					s += "1655_PageRead" + "\t";
					s += "1656_PageWrite" + "\t";
					s += "1657_GetUserNameFromCode" + "\t";
					s += "1658_GetFileFromID" + "\t";
					s += "1659_GetFileLabel" + "\t";
					s += "1660_GetDefaultKey" + "\t";
					s += "1661_GetDefaultOrder" + "\t";
					s += "1662_SetStatusBarText" + "\t";
					s += "1663_IsValid" + "\t";
					s += "1664_AddAlert" + "\t";
					s += "1665_Import:GetAscFieldValue" + "\t";
					s += "1666_Import:GetFieldVal" + "\t";
					s += "1667_Import:GetHFName" + "\t";
					s += "1668_Import:GetLinkVal" + "\t";
					s += "1669_Import:SetFieldVal" + "\t";
					s += "1670_Import:SetLinkVal" + "\t";
					s += "1671_Import:WriteIDToTrans" + "\t";
					s += "1672_GetAlertPointer" + "\t";
					s += "1673_SetAlertPointer" + "\t";
					s += "1674_Pause" + "\t";
					s += "1675_goTr:DateToInteger" + "\t";
					s += "1676_goTr:TimeToInteger" + "\t";
					s += "1677_goTr:IntegerToDate" + "\t";
					s += "1678_goTr:IntegerToTime" + "\t";
					s += "1679_IntegerPart" + "\t";
					s += "1680_DecimalPart" + "\t";
					s += "1681_Abs" + "\t";
					s += "1682_goTr:AddMinutes" + "\t";
					s += "1683_OpenForm:SetOpeningMode" + "\t";
					s += "1684_goTr:GetLinePart" + "\t";
					s += "1685_goTr:GetLineValue" + "\t";
					break;
			}

			return s;
		}
		public string GetSendFormatLabel(string par_sSendType, bool par_bUCase = true)
		{
			//MI 1/10/07
			//PURPOSE:
			//       Returns a friendly label as imperative verb in user's current language (not supported currently)
			//       for a sending type code.
			//PARAMETERS:
			//       par_sSendType: sendtype URL parameter in diaSend.aspx. Supported values:
			//           EMAIL
			//           FAX
			//           LETTER
			//           WPRESPONSE
			//       par_bUCase: If True (default), the result is uppercased.

			string sProc = "clDefaults::GetSendFormatLabel";

			string s = null;

			switch (par_sSendType.ToUpper())
			{
				case "EMAIL":
					if (par_bUCase)
					{
						s = "E-mail";
					}
					else
					{
						s = "e-mail";
					}
					break;
				case "FAX":
					if (par_bUCase)
					{
						s = "Fax";
					}
					else
					{
						s = "fax";
					}
					break;
				case "LETTER":
					if (par_bUCase)
					{
						s = "Letter";
					}
					else
					{
						s = "letter";
					}
					break;
				case "WPRESPONSE":
					if (par_bUCase)
					{
						s = "WebPertner Response";
					}
					else
					{
						s = "WebPartner response";
					}
					break;
				default:
					return "";
			}

			return s;
		}

		public string GetSendTemplateIcon(string par_sAvailableFor = "", string par_sViewType = "")
		{
			//MI 12/2/09 Added CHART case.
			//MI 2/21/05
			//MI moved from clUI to clDefaults 2/21/05. Renamed from GetPrintTemplateIcon to GetSendTemplateIcon.
			//PURPOSE:
			//		Get the icon filename of the icon appropriate for the given Send Template
			//		The icon can be used in listboxes using gImage(). See example.
			//PARAMETERS:
			//		sAvailableFor: objects for which the template is available. Supported:
			//			CORR
			//			QUOTES
			//			VIEWS		'specify type of View in par_sViewType
			//			FORMS
			//			LABELS
			//		par_sViewType: type of View. Supported values:
			//			LIST		'list
			//			CAL			'generic calendar
			//			CALDAY		'day calendar
			//			CALWEEK		'week calendar
			//			CALMONTH	'month calendar
			//			MFIELD		'multi-field
			//			SFIELD		'single-field
			//           CHART       'chart
			//EXAMPLE:
			//		ListAdd("LST_LISTNAME",gImage(goDef:GetSendTemplateIcon())&sTemplateLabel&gLink(sTemplateID))

			string sProc = "clDefaults::GetSendTemplateIcon";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sAvailableFor = par_sAvailableFor.Trim(' ').ToUpper();
			string sViewType = par_sViewType.Trim(' ').ToUpper();

			switch (sAvailableFor)
			{
				case "CORR":
					return ("PrintTemplCorrespondence.ico");
				case "QUOTES":
					return ("PrintTemplQuote.ico");
				case "VIEWS":
					switch (sViewType)
					{
						case "LIST":
							return ("PrintTemplViewList.ico");
						case "CAL":
							return ("PrintTemplViewCalendar.ico");
						case "CALDAY":
							return ("PrintTemplViewCalDay.ico");
						case "CALWEEK":
							return ("PrintTemplViewCalWeek.ico");
						case "CALMONTH":
							return ("PrintTemplViewCalMonth.ico");
						case "MFIELD":
							return ("PrintTemplViewMultiField.ico");
						case "SFIELD":
							return ("PrintTemplViewSingleField.ico");
						case "CHART":
							return ("PrintTemplViewChart.ico");
						default:
							return ("");
					}
					break;
				case "FORMS":
					return ("PrintTemplRecordDetails.ico");
				case "LABELS":
					return ("PrintTemplLabels.ico");
				default:
					return ("");
			}
		}
		public string GetSignatureFonts()
		{
			//CS OK

			//Return an ini string with default font definitions for signatures
			//in correspondence and quotes.

			//For debugging of the order of calls.
			string sProc = "clDefaults::GetSignatureFonts";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string s = "WORDFACE=Brush Script MT" + "\r\n" + "WORDSIZE=20" + "\r\n" + "WORDCOLOR=Blue" + "\r\n" + "WORDBOLD=No" + "\r\n" + "WORDITALICS=No" + "\r\n" + "WORDUNDERLINED=No" + "\r\n" + "HTMLFACE=Brush Script MT, Brush Script MT Italic, Script MT Bold, Script MT, Forte, Script" + "\r\n" + "HTMLSIZE=6" + "\r\n" + "HTMLCOLOR=Navy" + "\r\n" + "HTMLBOLD=Yes" + "\r\n" + "HTMLITALICS=No" + "\r\n" + "HTMLUNDERLINED=No";

			return (s);
		}

		public string GetViewCalAlarmDef(string par_sFileName)
		{
			//MI 12/19/08 Created
			//Returns a CAL_ALARMDEF view property for the VIE_ metadata.

			string sProc = "clData::GetViewCalAlarmDef";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);
			string s = "";

			// Try
			switch (par_sFileName.ToUpper())
			{
					case "AP":
						s = "<%CHK_Alarm%>";
						break;
					default:
						s = "";
						break;
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return s;

		}

		public string GetViewCalLine(string par_sFileName, string par_sCalViewType)
		{
			//MI 12/18/08 Created
			//PURPOSE:
			//       Returns the 'line' definition of fields and/or static text
			//       to display in calendar views.
			//       Called from InitViewDefaults to set CALDAY_LINE,
			//       CALWEEK_LINE and CALMONTH_LINE properties.
			//PARAMETERS:
			//       par_sFileName: name of the file, case insensitive.
			//       par_sCalViewType: CALDAY, CALWEEK, or CALMONTH.
			//RETURNS:
			//       String: value of the 'line' to display in cal views.

			string sProc = "clData::GetViewCalLine";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string s = "";

			// Try
			switch (par_sFileName.ToUpper())
			{
					case "AP":
						//Appointment file
						//Supported: DTE_StartTime, TME_StartTime, TME_EndTime, CHK_Alarm
						switch (par_sCalViewType.ToUpper())
						{
							case "CALDAY":
								s = "<%TME_StartTime%>-<%TME_EndTime%> <%LNK_CoordinatedBy_US%%TXT_Code%> <%TXT_Description%>";
								break;
							case "CALWEEK":
								s = "<%TME_StartTime%>-<%TME_EndTime%> <%LNK_CoordinatedBy_US%%TXT_Code%> <%TXT_Description%>";
								break;
							case "CALMONTH":
								s = "<%TME_StartTime%> <%LNK_CoordinatedBy_US%%TXT_Code%> <%TXT_Description%>";
								break;
							default:
								goErr.SetError(10103, sProc, "", "par_sCalViewType", "sProc", par_sCalViewType);
								break;
								//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
						}
						break;
					default:
						//All non-Appointment fields display the Name field only
						s = "<%SYS_Name%>";
						break;
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return s;

		}


		public string GetViewDefaults(string par_sFileName, string par_sViewType = "LIST")
		{
			//MI 1/29/10 Added par_sViewType to support CHART sort and related defaults.
			//CS OK

			//PURPOSE:
			//		Retrieve all default properties for a View of a provided file name.
			//		View defaults are filled with :InitViewDefaults()
			//		To retrieve record detail defaults, call :GetRecDetailDefs().
			//PARAMETERS:
			//		par_sFileName: name of the file
			//			This is needed for file-dependent defaults such as fields.
			//       par_sViewType: 'LIST' by default. 'CHART' has different sort defaults.

			//Debugging the order of calls.
			string sProc = "clDefaults::GetViewDefaults";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//Initialize View defaults for the View's File
			InitViewDefaults(par_sFileName, par_sViewType);

			return sViewDefaults;
		}
		public string GetViewTypeCode(string par_sViewLabel)
		{
			//MI 12/2/09 Enabled CHART view type.
			//MI 2/21/05
			//MI moved from clUI to clDefaults 2/21/05
			//PURPOSE:
			//		Return numeric View Type Code from View Type label
			//PARAMETERS:
			//		par_sViewLabel: View Type label
			//RETURNS:
			//		Numeric View Type Code as string. If the View Label is
			//			unsupported, returns "".
			//SUPPORTED VIEW TYPES:
			//		NOTVIEW		00 Window is not a view
			//		FORMNOTVIEW	01 Form (not a view)
			//		LIST		10 List view
			//		CALDAY		21 Day Calendar view
			//		CALMDAY		22 Multi-day Calendar view		'Unsupported
			//		CALWEEK		23 Week Calendar view
			//		CALMONTH	24 Month Calendar view
			//		CALYEAR		25 Year Calendar view
			//		CALMCOL		26 Multi-column Calendar view	'Unsupported
			//		CALGRAPH	27 Graphical Calendar view		'Unsupported
			//		MFIELD		30 Multi-field view
			//		SFIELD		31 Single-field view
			//		WEB			40 Web browser view				'Unsupported
			//		FORM		50 Form view					'Unsupported
			//		CHART		60 Chart view
			//		GANTT		70 Gantt (scheduling) view		'Unsupported

			//For debugging of the order of calls.
			string sProc = "clDefaults::GetViewTypeCode";
			//oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			switch (par_sViewLabel.ToUpper().Trim(' '))
			{
				case "NOTVIEW":
					return ("00");
				case "FORMNOTVIEW":
					return ("01");
				case "LIST":
					return ("10");
				case "CALDAY":
					return ("21");
				case "CALMDAY": //Multi-day Calendar view 'Unsupported
					return ("22");
				case "CALWEEK":
					return ("23");
				case "CALMONTH":
					return ("24");
				case "CALYEAR":
					return ("25");
				case "CALMCOL": //Multi-column Calendar view 'Unsupported
					return ("26");
				case "CALGRAPH": //Multi-column Calendar view 'Unsupported
					return ("27");
				case "MFIELD": //Multi-field
					return ("30");
				case "SFIELD": //Single-field view
					return ("31");
				case "WEB": //Web browser view 'Unsupported
					return ("40");
				case "FORM": //Form view 'Unsupported
					return ("50");
				case "CHART": //Chart view
					return ("60");
				case "GANTT": //Gantt (scheduling) view 'Unsupported
					return ("70");
				default:
					return ("");
			}
		}
		public void InitConduits()
		{
			//2/14/14 MI InitConduits. Added GOOGLECAL and GOOGLECONTACT.
			//PURPOSE:
			//       Initialize a list of supported App Link conduits.
			//       The list is in the format:
			//		    CONDUITCODE&TAB&...
			//   		sFriendlyConduitName&TAB&...
			//	    	sSelltisFileName&TAB&...
			//		    sDefaultMeLink&TAB&...
			//   		bShowInUI&TAB&...
			//	    	sDeviceName&CR...
			//       There must not be a CR at the end of the list.

			string sProc = "clDefaults::InitConduits";

			//sConduits = "OUTLOOKCAL" & vbtab & MessTranslate(5055) & TAB & "APP" & TAB & "<%LNK_Involves_User%>" & TAB & "1" & TAB & MessTraduit(5175) & CR
			sConduits = "OUTLOOKCAL" + "\t" + "MS Outlook Calendar (Appointments)" + "\t" + "APP" + "\t" + "<%LNK_Involves_User%>" + "\t" + "1" + "\t" + "MS Outlook" + "\r\n";
			//5055:MS Outlook Calendar (Appointments)
			//5175:MS Outlook
			//: sConduits &= "OUTLOOKCONTACT" & TAB & MessTranslate(5056) & TAB & "CONTACT" & TAB & "<%LNK_Related_User%>" & TAB & "1" & TAB & MessTraduit(5175) & CR
			sConduits += "OUTLOOKCONTACT" + "\t" + "MS Outlook Contacts (Contacts)" + "\t" + "CONTACT" + "\t" + "<%LNK_Related_User%>" + "\t" + "1" + "\t" + "MS Outlook" + "\r\n";
			//5056:MS Outlook Contacts (Contacts)
			sConduits += "OUTLOOKTODO" + "\t" + "MS Outlook Tasks (To Dos)" + "\t" + "TODO" + "\t" + "<%LNK_AssignedTo_User%>" + "\t" + "1" + "\t" + "MS Outlook" + "\r\n";
			//5056:MS Outlook Contacts (Contacts)

			sConduits += "GOOGLECAL" + "\t" + "Google Calendar (Appointments)" + "\t" + "APP" + "\t" + "<%LNK_Involves_User%>" + "\t" + "1" + "\t" + "Google" + "\r\n";
			sConduits += "GOOGLECONTACT" + "\t" + "Google Contacts (Contacts)" + "\t" + "CONTACT" + "\t" + "<%LNK_Related_User%>" + "\t" + "1" + "\t" + "Google" + "\r\n";

			//: sConduits &= "PALMADDRESSBOOK" & TAB & MessTranslate(5057) & TAB & "CONTACT" & TAB & "<%LNK_Related_User%>" & TAB & "1" & TAB & MessTraduit(5176) & CR

			//sConduits &= "PALMADDRESSBOOK" & vbTab & "Palm Pilot Address Book (Contacts)" & vbTab & "CONTACT" & vbTab & "<%LNK_Related_User%>" & vbTab & "1" & vbTab & "Palm Pilot" & vbCrLf
			//'5057:Palm Pilot Address Book (Contacts)
			//'5176:Palm Pilot
			//': sConduits &= "PALMDATEBOOK" & TAB & MessTranslate(5058) & TAB & "APP" & TAB & "<%LNK_Involves_User%>" & TAB & "1" & TAB & MessTraduit(5176) & CR
			//sConduits &= "PALMDATEBOOK" & vbTab & "Palm Pilot Date Book (Appointments)" & vbTab & "APP" & vbTab & "<%LNK_Involves_User%>" & vbTab & "1" & vbTab & "Palm Pilot" & vbCrLf
			//'5058:Palm Pilot Date Book (Appointments)
			//': sConduits &= "PALMMEMO" & TAB() & MessTranslate(5059) & TAB() & "ACTIVITY" & TAB() & "<%LNK_CreditedTo_User%>" & TAB() & "1" & TAB() & MessTraduit(5176) & CR
			//sConduits &= "PALMMEMO" & vbTab & "Palm Pilot Memo Pad (Activities)" & vbTab & "ACTIVITY" & vbTab & "<%LNK_CreditedTo_User%>" & vbTab & "1" & vbTab & "Palm Pilot" & vbCrLf
			//'5059:Palm Pilot Memo Pad (Activities)
			//': sConduits &= "PALMTODO" & TAB & MessTranslate(5060) & TAB & "TODO" & TAB & "<%LNK_AssignedTo_User%>" & TAB & "1" & TAB & MessTraduit(5176) & CR
			//sConduits &= "PALMTODO" & vbTab & "Palm Pilot To Do List (To Dos)" & vbTab & "TODO" & vbTab & "<%LNK_AssignedTo_User%>" & vbTab & "1" & vbTab & "Palm Pilot" & vbCrLf
			//'5060:Palm Pilot To Do List (To Dos)
			//'        : sConduits &= "OUTLOOKCALWKGP" & TAB & MessTranslate(5153) & TAB & "APP" & TAB & "<%LNK_Involves_User%>" & TAB & "0" & TAB & MessTraduit(5177) & CR
			//sConduits &= "OUTLOOKCALWKGP" & vbTab & "MS Exchange Calendar (Appointments)" & vbTab & "APP" & vbTab & "<%LNK_Involves_User%>" & vbTab & "0" & vbTab & "MS Exchange" & vbCrLf
			//'								----- Anglais/English (3) -----
			//'								MS Exchange Calendar (Appointments)
			//'								----- Francais/French (5) -----

			//'5177:MS Exchange
			//'        : sConduits &= "OUTLOOKCONTACTWKGP" & TAB & MessTranslate(5154) & TAB & "CONTACT" & TAB & "<%LNK_Related_User%>" & TAB & "0" & TAB & MessTraduit(5177) & CR
			//sConduits &= "OUTLOOKCONTACTWKGP" & vbTab & "MS Exchange Contacts (Contacts)" & vbTab & "CONTACT" & vbTab & "<%LNK_Related_User%>" & vbTab & "0" & vbTab & "MS Exchange" & vbCrLf
			//'								----- Anglais/English (3) -----
			//'								MS Exchange Contacts (Contacts)
			//'								----- Francais/French (5) -----

			//'        : sConduits &= "OUTLOOKPUBLICWKGP" & TAB() & MessTranslate(5155) & TAB() & "CONTACT" & TAB() & "<%LNK_Related_User%>" & TAB() & "0" & TAB() & MessTraduit(5177)
			//sConduits &= "OUTLOOKPUBLICWKGP" & vbTab & "MS Exchange Contacts (Contacts public folder)" & vbTab & "CONTACT" & vbTab & "<%LNK_Related_User%>" & vbTab & "0" & vbTab & "MS Exchange"
			//'								----- Anglais/English (3) -----
			//'								MS Exchange Contacts (Contacts public folder)
			//'								----- Francais/French (5) -----
		}


		public void Initialize()
		{

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			goDef = (Selltis.BusinessLogic.clDefaults)HttpContext.Current.Session["goDef"];
		}
		public void InitMetaObjects()
		{
			//MI 1/14/09 Added ERR (commented)
			//12/16/08 Fixed "AGE_", added & before = "ALT_"
			//MI 1/3/07 Added SNQ_.
			//CS OK

			//PURPOSE:
			//		Initialize a list of metadata objects supported in Transfer Out, In.
			//		IMPORTANT: Keep in sync with :GetMetaTypeFromPrefix()!
			//FORMAT:
			//		:sMetaObjects&=Prefix (string)& vbtab &Label (string)& vbcrlf&...

			//For debugging of the order of calls.
			string sProc = "clDefaults::InitMetaObjects";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			sMetaObjects = "AGE_" + "\t" + "Automators" + "\r\n"; //5005:Automators
			sMetaObjects += "ALT_" + "\t" + "Alerts" + "\r\n"; //5244:Alerts
			sMetaObjects += "CND_" + "\t" + "Application Link Conduits" + "\r\n"; //5062:Application Link Conduits
			sMetaObjects += "CRL_" + "\t" + "Create Linked Definitions" + "\r\n"; //5174:Create Linked Definitions
			sMetaObjects += "CRU_" + "\t" + "Create Unlinked Definitions" + "\r\n"; //5216:Create Unlinked Definitions
			sMetaObjects += "CUR_" + "\t" + "Currencies" + "\r\n"; //5065:Currencies
			sMetaObjects += "DBI_" + "\t" + "Database Information" + "\r\n"; //5229:Database Information
			sMetaObjects += "DBB_" + "\t" + "Dock Bar Buttons" + "\r\n"; //5043:Dock Bar Buttons
			sMetaObjects += "DBS_" + "\t" + "Dock Bar Shortcuts" + "\r\n"; //5044:Dock Bar Shortcuts
			//:sMetaObjects&="DEV_"& vbtab &MessTranslate(5164)& vbcrlf   '5164:External Device Properties
			sMetaObjects += "DS__" + "\t" + "Data Sources" + "\r\n"; //5011:Data Sources
			sMetaObjects += "DSK_" + "\t" + "Desktops" + "\r\n"; //5006:Desktops
			//sMetaObjects &= "ERR_" & vbTab & "Errors" & vbCrLf   '
			sMetaObjects += "EXP_" + "\t" + "Export Templates" + "\r\n"; //5007:Export Templates
			sMetaObjects += "FII_" + "\t" + "Field Icon Maps" + "\r\n"; //5152:Field Icon Maps
			sMetaObjects += "FLD_" + "\t" + "Field Labels" + "\r\n"; //5008:Field Labels
			//:sMetaObjects&="FLT_"& vbtab &MessTranslate(5009)& vbcrlf   '5009:Filters
			sMetaObjects += "FRM_" + "\t" + "Forms" + "\r\n"; //5010:Form Customizations
			sMetaObjects += "IMP_" + "\t" + "Import Templates" + "\r\n"; //5012:Import Templates
			//:sMetaObjects&="LBX_"& vbtab &MessTranslate(5179)& vbcrlf   '5179:Linkbox Definitions
			sMetaObjects += "LBF_" + "\t" + "Linkbox Filters" + "\r\n"; //messtranslate
			sMetaObjects += "LNK_" + "\t" + "Custom Link Definitions" + "\r\n"; //5179:Custom Link Definitions
			sMetaObjects += "LST_" + "\t" + "Lists" + "\r\n"; //5013:Lists
			sMetaObjects += "OTH_" + "\t" + "Other" + "\r\n"; //5014:Other
			sMetaObjects += "PGL_" + "\t" + "Page Layouts" + "\r\n"; //5054:Page Layouts
			sMetaObjects += "POP_" + "\t" + "Personal Options" + "\r\n"; //5015:Personal Options
			sMetaObjects += "PTA_" + "\t" + "Automator Pointers" + "\r\n"; //5212:Automator Pointers
			sMetaObjects += "PTR_" + "\t" + "Sync Pointers" + "\r\n"; //5213:Sync Pointers
			sMetaObjects += "REC_" + "\t" + "Record Detail Options" + "\r\n"; //5160:Record Detail Options
			sMetaObjects += "SCR_" + "\t" + "Scripts" + "\r\n"; //5016:Scripts
			sMetaObjects += "SEC_" + "\t" + "Event Script Cursor Information" + "\r\n"; //5217:Event Script Cursor Information
			sMetaObjects += "SND_" + "\t" + "Send Templates" + "\r\n"; //5048:Send Templates
			sMetaObjects += "SNQ_" + "\t" + "Send Queue Items" + "\r\n"; //5048:Send Templates
			sMetaObjects += "SPC_" + "\t" + "Procedure Script Cursor Information" + "\r\n"; //5218:Procedure Script Cursor Information
			sMetaObjects += "TMP_" + "\t" + "Temporary" + "\r\n"; //5162:Temporary
			//:sMetaObjects&="VTM_"& vbtab &MessTranslate(5017)& vbcrlf   '5017:View Templates
			sMetaObjects += "WCB_" + "\t" + "Web Client Buttons" + "\r\n"; //5242:Web Client Buttons
			sMetaObjects += "WOP_" + "\t" + "Workgroup Options"; //5018:Workgroup Options
		}
		public void InitOutlookCalFieldList()
		{
			//CS PORTED as placeholder

			//Initialize field map

			//Debugging the order of calls.
			string sProc = "clDefaults::InitOutlookCalFieldList";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//        : sOutlookCalFieldList = ""

			//        '"OUTLOOKCAL_"
			//        : sOutlookCalFieldList &= "All Day Event" & CR
			//        : sOutlookCalFieldList &= "Billing Information" & CR
			//        : sOutlookCalFieldList &= "Body" & CR
			//        : sOutlookCalFieldList &= "Busy Status" & CR
			//        : sOutlookCalFieldList &= "Categories" & CR
			//        : sOutlookCalFieldList &= "End Date" & CR
			//        : sOutlookCalFieldList &= "End Time" & CR
			//        : sOutlookCalFieldList &= "Importance" & CR
			//        : sOutlookCalFieldList &= "Location" & CR
			//        : sOutlookCalFieldList &= "Optional Attendees" & CR
			//        : sOutlookCalFieldList &= "Organizer" & CR
			//        : sOutlookCalFieldList &= "Private" & CR
			//        : sOutlookCalFieldList &= "Reminder Minutes" & CR
			//        : sOutlookCalFieldList &= "Reminder Set" & CR
			//        : sOutlookCalFieldList &= "Reminder Sound" & CR
			//        : sOutlookCalFieldList &= "Reminder Sound File" & CR
			//        : sOutlookCalFieldList &= "Required Attendees" & CR
			//        : sOutlookCalFieldList &= "Resources" & CR
			//        : sOutlookCalFieldList &= "Sensitivity" & CR
			//        : sOutlookCalFieldList &= "Start Date" & CR
			//        : sOutlookCalFieldList &= "Start Time" & CR
			//        : sOutlookCalFieldList &= "Subject"
		}
		public void InitOutlookCalMap()
		{
			//CS PORTED as placeholder

			//Debugging the order of calls.
			string sProc = "clDefaults::InitOutlookCalMap";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//: sOutlookCalMap = ""

			//'"OUTLOOKCAL_"
			//: sOutlookCalMap &= "<%MMO_Notes%>=Body" & CR
			//: sOutlookCalMap &= "<%DTE_StartTime%>=End Date" & CR
			//: sOutlookCalMap &= "<%TME_EndTime%>=End Time" & CR
			//: sOutlookCalMap &= "<%CHK_PrivateApp%>=Private" & CR
			//: sOutlookCalMap &= "<%CHK_Alarm%>=Reminder Set" & CR
			//: sOutlookCalMap &= "<%DTE_StartTime%>=Start Date" & CR
			//: sOutlookCalMap &= "<%MMO_Description%>=Subject" & CR
			//: sOutlookCalMap &= "<%TME_StartTime%>=Start Time" & CR
		}
		public void InitOutlookContactFldList()
		{
			//CS PORTED as placeholder

			//Debugging the order of calls.
			string sProc = "clDefaults::InitOutlookContactFieldList";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//:sOutlookContactFieldList=""

			//'"OUTLOOKCONTACT"
			//:sOutlookContactFieldList&="Account"&CR
			//:sOutlookContactFieldList&="Assistant Name"&CR
			//:sOutlookContactFieldList&="Assistant Phone"&CR
			//:sOutlookContactFieldList&="Billing Information"&CR
			//:sOutlookContactFieldList&="Body"&CR
			//:sOutlookContactFieldList&="Business Phone2"&CR
			//:sOutlookContactFieldList&="Business City"&CR
			//:sOutlookContactFieldList&="Business Country"&CR
			//:sOutlookContactFieldList&="Business ZIP"&CR
			//:sOutlookContactFieldList&="Business PO Box"&CR
			//:sOutlookContactFieldList&="Business State"&CR
			//:sOutlookContactFieldList&="Business Street"&CR
			//:sOutlookContactFieldList&="Business Fax"&CR
			//:sOutlookContactFieldList&="Business Homepage"&CR
			//:sOutlookContactFieldList&="Business Phone"&CR
			//:sOutlookContactFieldList&="Callback Phone"&CR
			//:sOutlookContactFieldList&="Car Phone"&CR
			//:sOutlookContactFieldList&="Categories"&CR
			//:sOutlookContactFieldList&="Company Phone"&CR
			//:sOutlookContactFieldList&="Company Name"&CR
			//:sOutlookContactFieldList&="Customer ID"&CR
			//:sOutlookContactFieldList&="Department"&CR
			//:sOutlookContactFieldList&="Email1"&CR
			//:sOutlookContactFieldList&="Email1 Type"&CR
			//:sOutlookContactFieldList&="Email2"&CR
			//:sOutlookContactFieldList&="Email2 Type"&CR
			//:sOutlookContactFieldList&="Email3"&CR
			//:sOutlookContactFieldList&="Email3 Type"&CR
			//:sOutlookContactFieldList&="File As"&CR
			//:sOutlookContactFieldList&="First Name"&CR
			//:sOutlookContactFieldList&="FTP Site"&CR
			//:sOutlookContactFieldList&="Government ID"&CR
			//:sOutlookContactFieldList&="Home Phone2"&CR
			//:sOutlookContactFieldList&="Home City"&CR
			//:sOutlookContactFieldList&="Home Country"&CR
			//:sOutlookContactFieldList&="Home ZIP"&CR
			//:sOutlookContactFieldList&="Home PO Box"&CR
			//:sOutlookContactFieldList&="Home State"&CR
			//:sOutlookContactFieldList&="Home Street"&CR
			//:sOutlookContactFieldList&="Home FAX"&CR
			//:sOutlookContactFieldList&="Home Phone"&CR
			//:sOutlookContactFieldList&="Homepage"&CR
			//:sOutlookContactFieldList&="Importance"&CR
			//:sOutlookContactFieldList&="Initials"&CR
			//:sOutlookContactFieldList&="Job Title"&CR
			//:sOutlookContactFieldList&="Last Name"&CR
			//:sOutlookContactFieldList&="Manager"&CR
			//:sOutlookContactFieldList&="Middle Name"&CR
			//:sOutlookContactFieldList&="Mobile Phone"&CR
			//:sOutlookContactFieldList&="Nickname"&CR
			//:sOutlookContactFieldList&="Location"&CR
			//:sOutlookContactFieldList&="Organizational ID"&CR
			//:sOutlookContactFieldList&="Other City"&CR
			//:sOutlookContactFieldList&="Other Country"&CR
			//:sOutlookContactFieldList&="Other ZIP"&CR
			//:sOutlookContactFieldList&="Other PO Box"&CR
			//:sOutlookContactFieldList&="Other State"&CR
			//:sOutlookContactFieldList&="Other Street"&CR
			//:sOutlookContactFieldList&="Other Fax"&CR
			//:sOutlookContactFieldList&="Other Phone"&CR
			//:sOutlookContactFieldList&="Pager Phone"&CR
			//:sOutlookContactFieldList&="Personal Homepage"&CR
			//:sOutlookContactFieldList&="Primary Phone"&CR
			//:sOutlookContactFieldList&="Private"&CR
			//:sOutlookContactFieldList&="Profession"&CR
			//:sOutlookContactFieldList&="Referred By"&CR
			//:sOutlookContactFieldList&="Primary Email"&CR
			//:sOutlookContactFieldList&="Sensitivity"&CR
			//:sOutlookContactFieldList&="Spouse"&CR
			//:sOutlookContactFieldList&="Subject"&CR
			//:sOutlookContactFieldList&="Suffix"&CR
			//:sOutlookContactFieldList&="Title"
		}
		public void InitOutlookContactMap()
		{
			//CS PORTED as placeholder

			//Debugging the order of calls.
			string sProc = "clDefaults::InitOutlookContactMap";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//:sOutlookContactMap=""

			//'"OUTLOOKCONTACT"
			//:sOutlookContactMap&="<%TXT_AssistantName%>=Assistant Name"&CR
			//:sOutlookContactMap&="<%TEL_AssistantPhone%>=Assistant Phone"&CR
			//:sOutlookContactMap&="<%MMO_Note%>=Body"&CR
			//:sOutlookContactMap&="<%TXT_CityBusiness%>=Business City"&CR
			//:sOutlookContactMap&="<%TXT_CountryBusiness%>=Business Country"&CR
			//:sOutlookContactMap&="<%TXT_ZipBusiness%>=Business ZIP"&CR
			//:sOutlookContactMap&="<%TXT_StateBusiness%>=Business State"&CR
			//:sOutlookContactMap&="<%MMO_AddrBusiness%>=Business Street"&CR
			//:sOutlookContactMap&="<%TEL_Fax%>=Business Fax"&CR
			//:sOutlookContactMap&="<%MMO_Web%>=Business Homepage"&CR
			//:sOutlookContactMap&="<%TEL_BusPhone%>=Business Phone"&CR
			//:sOutlookContactMap&="<%TXT_CompanyNameText%>=Company Name"&CR
			//:sOutlookContactMap&="<%EML_EMAIL%>=Email1"&CR
			//:sOutlookContactMap&="<%TXT_NameFirst%>=First Name"&CR
			//:sOutlookContactMap&="<%TEL_HomePhone%>=Home Phone"&CR
			//:sOutlookContactMap&="<%TXT_TitleText%>=Job Title"&CR
			//:sOutlookContactMap&="<%TXT_NameLast%>=Last Name"&CR
			//:sOutlookContactMap&="<%TEL_CellPhone%>=Mobile Phone"&CR
			//:sOutlookContactMap&="<%TXT_Nickname%>=Nickname"&CR
			//:sOutlookContactMap&="<%TEL_OtherPhone%>=Other Phone"&CR
			//:sOutlookContactMap&="<%TEL_Pager%>=Pager Phone"&CR
			//:sOutlookContactMap&="<%TEL_MainPhone%>=Primary Phone"&CR
			//:sOutlookContactMap&="<%EML_EMAIL%>=Primary Email"&CR
			//:sOutlookContactMap&="<%MLS_Priority%>=Sensitivity"&CR
			//:sOutlookContactMap&="<%TXT_TitleText%>=Title"&CR
		}
		public void InitPageDefinitions()
		{
			//CS OK

			//Initialize page size/dimension definitions used in Page Layouts
			//FORMAT:
			//		PAGECODE&TAB&Page Description&TAB&WInInches&TAB&HInInches&TAB&WInmm&TAB&HInmm&CR

			//Debugging the order of calls.
			string sProc = "clDefaults::InitPageDefinitions";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			sPageDefinitions = "";

			sPageDefinitions += "A4" + "\t" + "A4 (210 x 297 mm)" + "\t" + 8.268 + "\t" + 11.693 + "\t" + 210 + "\t" + 297 + "\r\n";
			sPageDefinitions += "A5" + "\t" + "A5 (148 x 210 mm)" + "\t" + 5.827 + "\t" + 8.268 + "\t" + 148 + "\t" + 210 + "\r\n";
			sPageDefinitions += "B4" + "\t" + "B4 (250 x 354 mm)" + "\t" + 9.843 + "\t" + 13.937 + "\t" + 250 + "\t" + 354 + "\r\n";
			sPageDefinitions += "B5" + "\t" + "B5 (182 x 257 mm)" + "\t" + 7.165 + "\t" + 10.118 + "\t" + 182 + "\t" + 257 + "\r\n";
			sPageDefinitions += "CUSTOM" + "\t" + "Custom" + "\t" + 8.5 + "\t" + 11 + "\t" + 215.9 + "\t" + 279.4 + "\r\n";
			//messtranslate 5020:Custom
			sPageDefinitions += "ENVELOPE10" + "\t" + "#10 Envelope (4.125 x 9.5 in)" + "\t" + 4.125 + "\t" + 9.5 + "\t" + 104.775 + "\t" + 241.3 + "\r\n";
			sPageDefinitions += "FOLIO" + "\t" + "Folio (8.5 x 13 in)" + "\t" + 8.5 + "\t" + 13 + "\t" + 215.9 + "\t" + 330.2 + "\r\n";
			sPageDefinitions += "LEGAL" + "\t" + "Legal (8.5 x 14 in)" + "\t" + 8.5 + "\t" + 14 + "\t" + 215.9 + "\t" + 355.6 + "\r\n";
			sPageDefinitions += "LETTER" + "\t" + "Letter (8.5 x 11 in)" + "\t" + 8.5 + "\t" + 11 + "\t" + 215.9 + "\t" + 279.4 + "\r\n";
			sPageDefinitions += "TABLOID" + "\t" + "Tabloid (11 x 17 in)" + "\t" + 11 + "\t" + 17 + "\t" + 279.4 + "\t" + 431.8 + "\r\n";
		}
		public void InitPalmAddrBookFldList()
		{
			//CS PORTED as placeholder

			//Debugging the order of calls.
			string sProc = "clDefaults::InitPalmAddressBookFldList";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//: sPalmAddressBookFieldList = ""

			//'"PALMADDRESSBOOK"
			//: sPalmAddressBookFieldList &= "Address" & CR
			//: sPalmAddressBookFieldList &= "City" & CR
			//: sPalmAddressBookFieldList &= "Company" & CR
			//: sPalmAddressBookFieldList &= "Country" & CR
			//: sPalmAddressBookFieldList &= "Custom Field 1" & CR
			//: sPalmAddressBookFieldList &= "Custom Field 2" & CR
			//: sPalmAddressBookFieldList &= "Custom Field 3" & CR
			//: sPalmAddressBookFieldList &= "Custom Field 4" & CR
			//: sPalmAddressBookFieldList &= "E-Mail Address" & CR
			//: sPalmAddressBookFieldList &= "First Name" & CR
			//: sPalmAddressBookFieldList &= "Fax Telephone" & CR
			//: sPalmAddressBookFieldList &= "Home Telephone" & CR
			//: sPalmAddressBookFieldList &= "Last Name" & CR
			//: sPalmAddressBookFieldList &= "Main Telephone" & CR
			//: sPalmAddressBookFieldList &= "Mobile Telephone" & CR
			//: sPalmAddressBookFieldList &= "Note" & CR
			//: sPalmAddressBookFieldList &= "Other Telephone" & CR
			//: sPalmAddressBookFieldList &= "Pager" & CR
			//: sPalmAddressBookFieldList &= "State" & CR
			//: sPalmAddressBookFieldList &= "Title" & CR
			//: sPalmAddressBookFieldList &= "Work Telephone" & CR
			//: sPalmAddressBookFieldList &= "Zip Code"
		}
		public void InitPalmAddressBookMap()
		{
			//CS PORTED as placeholder

			//Debugging the order of calls.
			string sProc = "clDefaults::InitPalmAddressBookMap";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//        : sPalmAddressBookMap = ""

			//        '"PALMADDRESSBOOK_"
			//        : sPalmAddressBookMap &= "<%TXT_NameLast%>=Last Name" & CR
			//        : sPalmAddressBookMap &= "<%TXT_NameFirst%>=First Name" & CR
			//        : sPalmAddressBookMap &= "<%TXT_CompanyNameText%>=Company" & CR
			//        : sPalmAddressBookMap &= "<%TXT_TitleText%>=Title" & CR
			//        : sPalmAddressBookMap &= "<%MMO_AddrBusiness%>=Address" & CR
			//        : sPalmAddressBookMap &= "<%TXT_CityBusiness%>=City" & CR
			//        : sPalmAddressBookMap &= "<%TXT_StateBusiness%>=State" & CR
			//        : sPalmAddressBookMap &= "<%TXT_ZipBusiness%>=Zip Code" & CR
			//        : sPalmAddressBookMap &= "<%TXT_CountryBusiness%>=Country" & CR
			//        : sPalmAddressBookMap &= "<%EML_EMAIL%>=E-Mail Address" & CR
			//        : sPalmAddressBookMap &= "<%TEL_BusPhone%>=Work Telephone" & CR
			//        : sPalmAddressBookMap &= "<%TEL_HomePhone%>=Home Telephone" & CR
			//        : sPalmAddressBookMap &= "<%TEL_Fax%>=Fax Telephone" & CR
			//        : sPalmAddressBookMap &= "<%TEL_OtherPhone%>=Other Telephone" & CR
			//        : sPalmAddressBookMap &= "<%TEL_MainPhone%>=Main Telephone" & CR
			//        : sPalmAddressBookMap &= "<%TEL_Pager%>=Pager" & CR
			//        : sPalmAddressBookMap &= "<%TEL_CellPhone%>=Mobile Telephone" & CR
			//        : sPalmAddressBookMap &= "<%MMO_Note%>=Note" & CR
		}
		public void InitPalmDateBookFldList()
		{
			//CS PORTED as placeholder

			//Debugging the order of calls.
			string sProc = "clDefaults::InitPalmDateBookFldList";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//: sPalmDateBookFieldList = ""

			//'"PALMDATEBOOK_"
			//: sPalmDateBookFieldList &= "Description" & CR
			//: sPalmDateBookFieldList &= "Date" & CR
			//: sPalmDateBookFieldList &= "Start Time" & CR
			//: sPalmDateBookFieldList &= "End Time" & CR
			//: sPalmDateBookFieldList &= "Alarm" & CR
			//: sPalmDateBookFieldList &= "Alarm Interval" & CR
			//: sPalmDateBookFieldList &= "Alarm Units" & CR
			//: sPalmDateBookFieldList &= "Note"
		}
		public void InitPalmDateBookMap()
		{
			//CS PORTED as placeholder

			//Debugging the order of calls.
			string sProc = "clDefaults::InitPalmDateBookMap";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//: sPalmDateBookMap = ""

			//'"PALMDATEBOOK"
			//: sPalmDateBookMap &= "<%MMO_Description%>=Description" & CR
			//: sPalmDateBookMap &= "<%DTE_StartTime%>=Date" & CR
			//: sPalmDateBookMap &= "<%TME_StartTime%>=Start Time" & CR
			//: sPalmDateBookMap &= "<%TME_EndTime%>=End Time" & CR
			//: sPalmDateBookMap &= "<%CHK_Alarm%>=Alarm" & CR
			//: sPalmDateBookMap &= "<%LI__AlarmInterval%>=Alarm Interval" & CR
			//: sPalmDateBookMap &= "<%MLS_AlarmUnits%>=Alarm Units" & CR
			//: sPalmDateBookMap &= "<%MMO_Notes%>=Note" & CR
		}
		public void InitPalmMemoFieldList()
		{
			//CS PORTED as placeholder

			//For debugging of the order of calls.
			string sProc = "clDefaults::InitPalmMemoFieldList";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//        : sPalmMemoFieldList = ""

			//'"PALMMEMO"
			//        : sPalmMemoFieldList &= "Description" & CR
			//        : sPalmMemoFieldList &= "Memo Text"
		}
		public void InitPalmMemoMap()
		{
			//CS PORTED as placeholder

			//Debugging the order of calls.
			string sProc = "clDefaults::InitPalmMemoMap";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//        : sPalmMemoMap = ""

			//        '"PALMMEMO_"
			//        'Enable the following line if TXT_Description field is added to the Analysis
			//        ':sPalmMemoMap&="<TXT_Description%>=Title"&CR

			//        : sPalmMemoMap &= "<%MMO_Notes%>=Memo Text" & CR
		}
		public void InitPalmToDoFieldList()
		{
			//CS PORTED as placeholder

			//Debugging the order of calls.
			string sProc = "clDefaults::InitPalmToDoFieldList";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//        : sPalmToDoFieldList = ""

			//        '"PALMTODO"
			//        : sPalmToDoFieldList &= "Description" & CR
			//        : sPalmToDoFieldList &= "Due Date" & CR
			//        : sPalmToDoFieldList &= "Completed" & CR
			//        : sPalmToDoFieldList &= "Priority" & CR
			//        : sPalmToDoFieldList &= "Note"
		}
		public void InitPalmToDoMap()
		{
			//CS PORTED as placeholder

			//Debugging the order of calls.
			string sProc = "clDefaults::InitPalmToDoMap";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//        : sPalmToDoMap = ""

			//        '"PALMTODO_"
			//        : sPalmToDoMap &= "<%MMO_Description%>=Description" & CR
			//        : sPalmToDoMap &= "<%DTE_DueDate%>=Due Date" & CR
			//        : sPalmToDoMap &= "<%CHK_Completed%>=Completed" & CR
			//        : sPalmToDoMap &= "<%MLS_Priority%>=Priority" & CR
			//        : sPalmToDoMap &= "<%MMO_Notes%>=Note" & CR
		}
		public void InitPersonalOptions()
		{
			//MI 2/20/14 Added GOOGLECAL, GOOGLECONTACT options.
			//MI 2/7/08 Changed default MAXHISTORYITEMS from 100 to 20.

			//Debugging the order of calls.
			string sProc = "clDefaults::InitPersonalOptions";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			sPersonalOptions = "";

			//=============== METADATA NAME ===================
			goTR.StrWrite(ref sPersonalOptions, "US_NAME", "Personal Options");


			//=============== DATA ENTRY ===================
			goTR.StrWrite(ref sPersonalOptions, "TODOFILLDUEDATE", 0);
			goTR.StrWrite(ref sPersonalOptions, "TODOSDUEINDAYS", 0);
			goTR.StrWrite(ref sPersonalOptions, "TODOSASSIGNTOME", 0);
			goTR.StrWrite(ref sPersonalOptions, "PUBLISHACTIVITIES", 1);


			//=============== DATABASE =====================
			goTR.StrWrite(ref sPersonalOptions, "MAXHISTORYITEMS", 20);


			//============== APPLICATION LINKS (CONDUITS) =======================
			goTR.StrWrite(ref sPersonalOptions, "OUTLOOKCAL_SYNCDIRECTION", 3);
			goTR.StrWrite(ref sPersonalOptions, "OUTLOOKCAL_SYNCOVERWRITE", 1);
			goTR.StrWrite(ref sPersonalOptions, "OUTLOOKCAL_FILTER_FILE", "AP");
			//goTR.StrWrite(sPersonalOptions, "OUTLOOKCAL_FILTER_KEYNAME", "APCLEUNIK")
			goTR.StrWrite(ref sPersonalOptions, "OUTLOOKCAL_FILTER_CONDITION", "LNK_INVOLVES_US='<%MEID%>'");
			goTR.StrWrite(ref sPersonalOptions, "OUTLOOKCAL_SYNCCONDOPTION", "MYRECORDS");
			//goTR.StrWrite(sPersonalOptions, "OUTLOOKCAL_SYNCCONDGROUP", "<%NONE%>")

			goTR.StrWrite(ref sPersonalOptions, "OUTLOOKCONTACT_SYNCDIRECTION", 3);
			goTR.StrWrite(ref sPersonalOptions, "OUTLOOKCONTACT_SYNCOVERWRITE", 1);
			goTR.StrWrite(ref sPersonalOptions, "OUTLOOKCONTACT_FILTER_FILE", "CN");
			//goTR.StrWrite(sPersonalOptions, "OUTLOOKCONTACT_FILTER_KEYNAME", "CNCLEUNIK")
			goTR.StrWrite(ref sPersonalOptions, "OUTLOOKCONTACT_FILTER_CONDITION", "LNK_RELATED_US='<%MEID%>'");
			goTR.StrWrite(ref sPersonalOptions, "OUTLOOKCONTACT_SYNCCONDOPTION", "MYRECORDS");
			//goTR.StrWrite(sPersonalOptions, "OUTLOOKCONTACT_SYNCCONDGROUP", "<%NONE%>")

			goTR.StrWrite(ref sPersonalOptions, "GOOGLECAL_SYNCDIRECTION", 3);
			goTR.StrWrite(ref sPersonalOptions, "GOOGLECAL_SYNCOVERWRITE", 1);
			goTR.StrWrite(ref sPersonalOptions, "GOOGLECAL_FILTER_FILE", "AP");
			goTR.StrWrite(ref sPersonalOptions, "GOOGLECAL_FILTER_CONDITION", "LNK_INVOLVES_US='<%MEID%>'");
			goTR.StrWrite(ref sPersonalOptions, "GOOGLECAL_SYNCCONDOPTION", "MYRECORDS");

			goTR.StrWrite(ref sPersonalOptions, "GOOGLECONTACT_SYNCDIRECTION", 3);
			goTR.StrWrite(ref sPersonalOptions, "GOOGLECONTACT_SYNCOVERWRITE", 1);
			goTR.StrWrite(ref sPersonalOptions, "GOOGLECONTACT_FILTER_FILE", "CN");
			goTR.StrWrite(ref sPersonalOptions, "GOOGLECONTACT_FILTER_CONDITION", "LNK_RELATED_US='<%MEID%>'");
			goTR.StrWrite(ref sPersonalOptions, "GOOGLECONTACT_SYNCCONDOPTION", "MYRECORDS");

			//Palm links are not supported currently
			//goTR.StrWrite(sPersonalOptions, "PALMADDRESSBOOK_SYNCDIRECTION", 3)
			//goTR.StrWrite(sPersonalOptions, "PALMADDRESSBOOK_SYNCOVERWRITE", 1)
			//goTR.StrWrite(sPersonalOptions, "PALMADDRESSBOOK_FILTER_FILE", "CN")
			//'goTR.StrWrite(sPersonalOptions, "PALMADDRESSBOOK_FILTER_KEYNAME", "CNCLEUNIK")
			//goTR.StrWrite(sPersonalOptions, "PALMADDRESSBOOK_FILTER_CONDITION", "LNK_RELATED_US='<%MEID%>'")
			//goTR.StrWrite(sPersonalOptions, "PALMADDRESSBOOK_SYNCCONDOPTION", "MYRECORDS")
			//'goTR.StrWrite(sPersonalOptions, "PALMADDRESSBOOK_SYNCCONDGROUP", "<%NONE%>")

			//goTR.StrWrite(sPersonalOptions, "PALMDATEBOOK_SYNCDIRECTION", 3)
			//goTR.StrWrite(sPersonalOptions, "PALMDATEBOOK_SYNCOVERWRITE", 1)
			//goTR.StrWrite(sPersonalOptions, "PALMDATEBOOK_FILTER_FILE", "AP")
			//'goTR.StrWrite(sPersonalOptions, "PALMDATEBOOK_FILTER_KEYNAME", "APCLEUNIK")
			//goTR.StrWrite(sPersonalOptions, "PALMDATEBOOK_FILTER_CONDITION", "LNK_INVOLVES_US='<%MEID%>'")
			//goTR.StrWrite(sPersonalOptions, "PALMDATEBOOK_SYNCCONDOPTION", "MYRECORDS")
			//'goTR.StrWrite(sPersonalOptions, "PALMDATEBOOK_SYNCCONDGROUP", "<%NONE%>")

			//goTR.StrWrite(sPersonalOptions, "PALMMEMO_SYNCDIRECTION", 3)
			//goTR.StrWrite(sPersonalOptions, "PALMMEMO_SYNCOVERWRITE", 1)
			//goTR.StrWrite(sPersonalOptions, "PALMMEMO_FILTER_FILE", "AC")
			//'goTR.StrWrite(sPersonalOptions, "PALMMEMO_FILTER_KEYNAME", "ACCLEUNIK")
			//goTR.StrWrite(sPersonalOptions, "PALMMEMO_FILTER_CONDITION", "LNK_CREDITEDTO_US='<%MEID%>' AND MLS_TYPE=19")     'Type 19='Note'
			//goTR.StrWrite(sPersonalOptions, "PALMMEMO_SYNCCONDOPTION", "MYRECORDS")
			//'goTR.StrWrite(sPersonalOptions, "PALMMEMO_SYNCCONDGROUP", "<%NONE%>")

			//goTR.StrWrite(sPersonalOptions, "PALMTODO_SYNCDIRECTION", 3)
			//goTR.StrWrite(sPersonalOptions, "PALMTODO_SYNCOVERWRITE", 1)
			//goTR.StrWrite(sPersonalOptions, "PALMTODO_FILTER_FILE", "TD")
			//'goTR.StrWrite(sPersonalOptions, "PALMTODO_FILTER_KEYNAME", "OpenDueDateTimeRev")
			//goTR.StrWrite(sPersonalOptions, "PALMTODO_FILTER_CONDITION", "CHK_OPEN=1 AND LNK_AssignedTo_US='<%MEID%>'")
			//goTR.StrWrite(sPersonalOptions, "PALMTODO_SYNCCONDOPTION", "MYRECORDS")
			//'goTR.StrWrite(sPersonalOptions, "PALMTODO_SYNCCONDGROUP", "<%NONE%>")


			//====================== CALENDAR ==============================
			//First day of week is no longer based on language selection, but POP/WOP.
			//Select Case goP.GetLangCode()
			//    Case "US"   'English
			//        goTR.StrWrite(sPersonalOptions, "FIRSTDAYOFWEEK", "0")    'Sunday
			//    Case "FR", "GR", "SP"
			//        goTR.StrWrite(sPersonalOptions, "FIRSTDAYOFWEEK", "1")    'Monday
			//    Case Else
			//        'Unsupported language
			//        goTR.StrWrite(sPersonalOptions, "CMB_FIRSTDAYOFWEEK", "1")
			//End Select
			//goTR.StrWrite(sPersonalOptions, "CALDAY_PRIMETIMESTART", "0800")
			//goTR.StrWrite(sPersonalOptions, "CALDAY_PRIMETIMEEND", "1700")
			//goTR.StrWrite(sPersonalOptions, "CAL_WEEKENDDAYS", "6,7")
			//goTR.StrWrite(sPersonalOptions, "TWODIGITSTARTYEAR", "1950")



			//=================== REGIONAL SETTINGS ==========================
			sPersonalOptions = goTR.MergeIniStrings(sPersonalOptions, GetRegionalOptions());

			//'----------- Numeric -------------
			//gotr.strwrite(:sPersonalOptions, "EDT_DECIMALSYMBOL", ".")
			//gotr.strwrite(:sPersonalOptions, "EDT_THOUSANDSEP", ",")
			//gotr.strwrite(:sPersonalOptions, "EDT_NEGATIVEBEFORE", "-")
			//gotr.strwrite(:sPersonalOptions, "EDT_NEGATIVEAFTER", "")
			//gotr.strwrite(:sPersonalOptions, "CHK_DISPLAYZERO", 0)
			//'------------ Currency -------------
			//gotr.strwrite(:sPersonalOptions, "EDT_CURRENCYSYMBOL", "$")
			//gotr.strwrite(:sPersonalOptions, "EDT_CURRDECIMALSYMBOL", ".")
			//gotr.strwrite(:sPersonalOptions, "EDT_CURRDECIMALDIGITS", 2)
			//gotr.strwrite(:sPersonalOptions, "EDT_CURRTHOUSANDSEP", ",")
			//gotr.strwrite(:sPersonalOptions, "EDT_CURRNEGATIVEBEFORE", "(")
			//gotr.strwrite(:sPersonalOptions, "EDT_CURRNEGATIVEAFTER", ")")
			//gotr.strwrite(:sPersonalOptions, "SEL_CURRSYMBOLPLACEMENT", 1)
			//gotr.strwrite(:sPersonalOptions, "CHK_CURRDISPLAYZERO", 1)
			//'----------------Date---------------------
			//gotr.strwrite(:sPersonalOptions, "CMB_DATESHORTDAY", 1)
			//gotr.strwrite(:sPersonalOptions, "CMB_DATESHORTMONTH", 1)
			//gotr.strwrite(:sPersonalOptions, "CMB_DATESHORTYEAR", 1)
			//gotr.strwrite(:sPersonalOptions, "EDT_DATESHORTSEPARATOR", "/")			'"/"
			//gotr.strwrite(:sPersonalOptions, "CMB_DATESHORTORDER", "2")
			//gotr.strwrite(:sPersonalOptions, "CMB_DATELONGDAY", 1)
			//gotr.strwrite(:sPersonalOptions, "EDT_DATELONGDAYSEPARATOR", ","&Charact(160))		'", "
			//gotr.strwrite(:sPersonalOptions, "CMB_DATELONGMONTH", 3)	'3
			//gotr.strwrite(:sPersonalOptions, "EDT_DATELONGMONTHSEP", Charact(160))				'" "
			//gotr.strwrite(:sPersonalOptions, "CMB_DATELONGYEAR", 1)
			//gotr.strwrite(:sPersonalOptions, "EDT_DATELONGYEARSEPARATOR", Charact(160))		'" "
			//gotr.strwrite(:sPersonalOptions, "CMB_DATELONGORDER", 2)
			//gotr.strwrite(:sPersonalOptions, "EDT_2DIGITYEAREND", "2029")					'"2029"
			//'---------------Time ---------------------
			//gotr.strwrite(:sPersonalOptions, "CMB_TIMEHOUR", 1)
			//gotr.strwrite(:sPersonalOptions, "EDT_TIMESEPARATOR", ":")			'":"
			//gotr.strwrite(:sPersonalOptions, "CHK_TIMEDISPLAYSECONDS", 0)	'0
			//gotr.strwrite(:sPersonalOptions, "SEL_TIME2412HOUR", 2)				'2
			//gotr.strwrite(:sPersonalOptions, "EDT_TIMEAMSYMBOL", Charact(160)&"AM")			'" AM"
			//gotr.strwrite(:sPersonalOptions, "EDT_TIMEPMSYMBOL", Charact(160)&"PM")			'" PM"


			//================= CORRESPONDENCE =================
			//To get Send template defaults, call :GetCorrSendDefs()
			//gotr.strwrite(:sPersonalOptions,"CORRSENDTEMPLEMAIL","SND_2003010609514317053MAI 09732XX")
			//gotr.strwrite(:sPersonalOptions,"CORRSENDTEMPLFAX","SND_2002103016511512135FH__24779XX")
			//gotr.strwrite(:sPersonalOptions,"CORRSENDTEMPLLTRHTML","SND_2003022108501694144MAR 06704XX")
			//gotr.strwrite(:sPersonalOptions,"CORRSENDTEMPLLTRWORD","SND_2002103016502161067FH__24779XX")
			goTR.StrWrite(ref sPersonalOptions, "CORRLTRACTIVITYDEF", "1");
			//gotr.strwrite(:sPersonalOptions,"CORRSENDTEMPLQTEEMAIL","SND_2003022108512613117MAR 06704XX")
			//gotr.strwrite(:sPersonalOptions,"CORRSENDTEMPLQTEFAX","SND_2002111616080828177MAI 27618XX")
			//gotr.strwrite(:sPersonalOptions,"CORRSENDTEMPLQTELTRHTML","SND_2003022108512613117MAR 06704XX")
			//gotr.strwrite(:sPersonalOptions,"CORRSENDTEMPLQTELTRWORD","SND_2002103016505583015FH__24779XX")
			goTR.StrWrite(ref sPersonalOptions, "CORRLTRQTEDEF", "1");
			goTR.StrWrite(ref sPersonalOptions, "CORRPREVIEWEMAIL", "1");
			goTR.StrWrite(ref sPersonalOptions, "CORRINDIVIDUALIZED", "0");
			goTR.StrWrite(ref sPersonalOptions, "CORRPREVIEWFAX", "1");

			goTR.StrWrite(ref sPersonalOptions, goP.GetLangCode() + "_CORRABOVESIGNATURE", "Best regards,"); //messtranslateMessTraduit(5230))
			//5230:Best regards,
			goTR.StrWrite(ref sPersonalOptions, goP.GetLangCode() + "_CORRSIGNATURE", "");
			goTR.StrWrite(ref sPersonalOptions, goP.GetLangCode() + "_CORRBELOWSIGNATURE", "Me, Title" + "\r\n" + "Company Name" + "\r\n" + "E-mail:" + "\r\n" + "Tel:" + "\r\n" + "Fax:");
			//messtranslate MessTraduit(5231))
			//5231:Me, Title
			//Company Name
			//E-mail: 
			//Tel: 
			//Fax: 
			clDefaults oDefault = new clDefaults();
			goTR.StrWrite(ref sPersonalOptions, "CORRSIGNATUREFONTS", oDefault.GetSignatureFonts());
		}
		public void InitRecDetailDefs(string par_sFileName)
		{
			//CS OK

			//PURPOSE:
			//		Initialize record detail metadata (REC_) defaults.
			//		Load default parameters for record details on file par_sFileName 
			//		into the member :sRecDetailDefs. To retrieve View defaults,
			//		use :GetRecDetailDefs().
			//
			//PARAMETERS:
			//		par_sFileName: name of the File for which to initialize defaults
			//			Files are considered for default fields, for example.

			//Debugging the order of calls.
			string sProc = "clDefaults::InitViewDefaults";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sFileName = par_sFileName.ToUpper();
			string sType = null;

			sRecDetailDefs = "";

			//====================== GENERAL =========================
			goTR.StrWrite(ref sRecDetailDefs, "FILE", sFileName);

			//====================== FORMAT ==========================


			//====================== FIELDS ==========================
			//'Column' in this context means 'field'. We are filling
			//values of fields in rows of the table TBL_FIELDS.
			switch (sFileName)
			{
				case "AC":
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELDCOUNT", 3);
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD1NAME", "<%DTE_StartTime%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD1LABEL", goData.GetFieldLabelFromName(sFileName, "DTE_StartTime"));
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD2NAME", "<%TME_StartTime%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD2LABEL", goData.GetFieldLabelFromName(sFileName, "TME_StartTime"));
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD3NAME", "<%MMO_Notes%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD3LABEL", goData.GetFieldLabelFromName(sFileName, "MMO_Notes"));
					break;
				case "AP":
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELDCOUNT", 3);
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD1NAME", "<%DTE_StartTime%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD1LABEL", goData.GetFieldLabelFromName(sFileName, "DTE_StartTime"));
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD2NAME", "<%TME_StartTime%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD2LABEL", goData.GetFieldLabelFromName(sFileName, "TME_StartTime"));
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD3NAME", "<%MMO_Description%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD3LABEL", goData.GetFieldLabelFromName(sFileName, "MMO_Description"));
					break;
				case "CO":
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELDCOUNT", 3);
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD1NAME", "<%TXT_CompanyName%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD1LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_CompanyName"));
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD2NAME", "<%TXT_CityMailing%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD2LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_CityMailing"));
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD3NAME", "<%TXT_StateMailing%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD3LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_StateMailing"));
					break;
				case "CN":
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELDCOUNT", 4);
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD1NAME", "<%TXT_NameLast%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD1LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_NameLast"));
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD2NAME", "<%TXT_NameFirst%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD2LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_NameFirst"));
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD3NAME", "<%TXT_TitleText%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD3LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_TitleText"));
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD4NAME", "<%TEL_BusPhone%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD4LABEL", goData.GetFieldLabelFromName(sFileName, "TEL_BusPhone"));
					break;
				case "FI":
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELDCOUNT", 3);
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD1NAME", "<%TXT_FileName%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD1LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_FileName"));
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD2NAME", "<%TXT_Description%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD2LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_Description"));
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD3NAME", "<%MMO_Note%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD3LABEL", goData.GetFieldLabelFromName(sFileName, "MMO_Note"));
					break;
				default:
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELDCOUNT", 1);
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD1NAME", "<%SYS_Name%>");
					goTR.StrWrite(ref sRecDetailDefs, "RECORD_FIELD1LABEL", goData.GetFieldLabelFromName(sFileName, "SYS_Name"));
					break;
			}



			//====================== COLORS ==========================
			goTR.StrWrite(ref sRecDetailDefs, "RECORD_CellBGColor", "255,255,255");
			goTR.StrWrite(ref sRecDetailDefs, "RECORD_CellTxtColor", "0,0,0");
			//Heading is column on left with field labels
			goTR.StrWrite(ref sRecDetailDefs, "RECORD_HeadingBGColor", "255,255,255");
			goTR.StrWrite(ref sRecDetailDefs, "RECORD_HeadingTxtColor", "0,0,0");


			//====================== FONTS ===========================
			goTR.StrWrite(ref sRecDetailDefs, "RECORD_HTMLCellFont", "Arial,1,0,0,0");
			goTR.StrWrite(ref sRecDetailDefs, "RECORD_HTMLHeadingFont", "Arial,1,1,0,0");
			goTR.StrWrite(ref sRecDetailDefs, "RECORD_PrintTextFont", "Arial,8,0,0,0"); //ADDED
			goTR.StrWrite(ref sRecDetailDefs, "RECORD_PrintHeadingFont", "Arial,8,1,0,0"); //ADDED
			//gotr.strwrite(:sRecDetailDefs,"RECORD_HTMLHeaderFont", "Arial,1,0,0,0")		'ADDED
			goTR.StrWrite(ref sRecDetailDefs, "RECORD_HTMLTitleFont", "Arial,3,1,0,0"); //ADDED
			//gotr.strwrite(:sRecDetailDefs,"RECORD_HTMLFooterFont", "Arial,1,0,0,0")		'ADDED
			goTR.StrWrite(ref sRecDetailDefs, "RECORD_PrintHeaderFont", "Arial,8,0,0,0"); //ADDED
			goTR.StrWrite(ref sRecDetailDefs, "RECORD_PrintTitleFont", "Arial,12,1,0,0"); //ADDED
			goTR.StrWrite(ref sRecDetailDefs, "RECORD_PrintFooterFont", "Arial,8,0,0,0"); //ADDED



			//====================== PRINT/SEND ======================
			//-------------------- Header, title, footer --------------------
			sType = "RECORD_";
			goTR.StrWrite(ref sRecDetailDefs, sType + "HEADERL", "");
			goTR.StrWrite(ref sRecDetailDefs, sType + "HEADERC", "");
			goTR.StrWrite(ref sRecDetailDefs, sType + "HEADERR", "Page <%Page%>"); //MessTranslate(5001)) '5001:Page <%Page%>
			goTR.StrWrite(ref sRecDetailDefs, sType + "TITLE", "<%FileLabel%> Record Details"); //MessTranslate(5169)) '5169:<%FileLabel%> Record Details
			goTR.StrWrite(ref sRecDetailDefs, sType + "FOOTERL", "<%DateLong%> <%Time%>"); //MessTranslate(5003)) '5003:<%DateLong%> <%Time%>
			goTR.StrWrite(ref sRecDetailDefs, sType + "FOOTERC", "");
			goTR.StrWrite(ref sRecDetailDefs, sType + "FOOTERR", "<%Me%>"); //MessTranslate(5004)) '5004:<%Me%>
			goTR.StrWrite(ref sRecDetailDefs, sType + "TITLESEND", "<%FileLabel%> Record Details"); //MessTranslate(5169)) '5169:<%FileLabel%> Record Details

			//---------------- Send templates -----------------------
			goTR.StrWrite(ref sRecDetailDefs, "RECORD_PAGELAYOUT", "PGL_2002103016542097217FH__24779XX");
			goTR.StrWrite(ref sRecDetailDefs, "RECORD_SENDTEMPLHTML", "SND_2002103016521984045FH__24779XX");
		}
		public void InitViewDefaults(string par_sFileName, string par_sViewType = "LIST")
		{
			//MI 9/1/10 Added LIST_USEBANDEDHEADINGS.
			//MI 1/29/10 Added par_sViewType to support CHART sort and related defaults.
			//MI 3/13/09 Modified TN field defs.
			//MI 12/1/08 Added DISPLAYASLINK to some selections.
			//MI 10/6/08 Added AUTOCOUNT.
			//MI 8/25/06 Cleanup.

			//PURPOSE:
			//		Load default parameters for a View on file par_sFileName 
			//		into the member sViewDefaults. To retrieve View defaults,
			//		use GetViewDefaults().
			//		To retrieve record detail defaults, call GetRecDetailDefs().
			//TIP:
			//		Create a view and copy its metadata into the code below, then 
			//		create StrWrite statements to write the same metadata to sViewDefaults.
			//PARAMETERS:
			//		par_sFileName: name of the File for which to initialize defaults
			//			Files are considered for default fields, for example.
			//       par_sViewType: 'LIST' by default. 'CHART' has different sort defaults.

			//Establish all default values in gwsDefaults

			//Debugging the order of calls.
			string sProc = "clDefaults::InitViewDefaults";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sFileName = par_sFileName.ToUpper();
			string s = null;
			string sType = "";
			string sSort = "";
			string[] sSortArray = null;

			//MI 12/15/08 Reset view defaults!
			sViewDefaults = "";

			//====================== GENERAL =========================
			goTR.StrWrite(ref sViewDefaults, "AUTOLOADVIEWDATA", 1);
			goTR.StrWrite(ref sViewDefaults, "AUTOCOUNT", 0);

			//====================== FORMAT ==========================
			//LIST
			goTR.StrWrite(ref sViewDefaults, "LIST_USEHEADINGS", "1"); //ADDED (column headings on or off)
			//goTR.StrWrite(sViewDefaults, "LIST_USEBANDEDHEADINGS", "0")    '0 is the default, we don't want to write it needlessly
			goTR.StrWrite(ref sViewDefaults, "LIST_ONELINEPERRECORD", "1");

			//CALDAY
			//goTR.StrWrite(sViewDefaults, "CALDAY_COMPRESS", "0")   '8/2/07
			//goTR.StrWrite(sViewDefaults, "CALDAY_TIMEUNIT", "30")   '8/2/07
			//goTR.StrWrite(sViewDefaults, "CALDAY_STARTTIME", "0800")   '8/2/07
			//gotr.strwrite(sViewDefaults,"CALDAY_HEADINGALIGNMENT","C")
			//goTR.StrWrite(sViewDefaults, "CALDAY_DISPLAYTIMEBAR", "1")   '8/2/07
			//CALWEEK
			//gotr.strwrite(sViewDefaults,"CALWEEK_HEADINGALIGNMENT","L")
			//CALMONTH
			//gotr.strwrite(sViewDefaults,"CALMONTH_SELDAYBORDER","1")
			//gotr.strwrite(sViewDefaults,"CALMONTH_HEADINGALIGNMENT","C")
			//gotr.strwrite(sViewDefaults,"CALMONTH_USEHEADINGS","1")		'ADDED (this is the only calendar UseHeadings property)
			//CALYEAR
			//goTR.StrWrite(sViewDefaults, "CALYEAR_DISPLAYTYPE", "4X3")        '<x>x<y> example: 4x3   '8/2/07
			//gotr.strwrite(sViewDefaults,"CALYEAR_HEADINGALIGNMENT","C")
			//gotr.strwrite(sViewDefaults,"CALYEAR_DISPLAYNONEMPTY","1")

			//================== FILTER/SORT =========================
			//KEYNAME not supported/needed in SellSQL
			//Select Case (sFileName)
			//    'Case "_LINK"
			//    '    goTR.StrWrite(sViewDefaults, "KEYNAME", "_IDKey1")
			//    Case "TN"
			//        goTR.StrWrite(sViewDefaults, "KEYNAME", "FileExtID")
			//    Case Else
			//        'Default sort is automatically used by the view
			//End Select

			//====================== FIELDS ==========================
			//---------- LIST, MFIELD, SFIELD types ----------
			//'Column' in this context means 'field'. We are filling
			//values of fields in rows of the table TBL_FIELDS.
			//Add the following properties to list view field defaults as appropriate:
			//		gotr.strwrite(sViewDefaults, "COL1TOTAL","0")
			//		gotr.strwrite(sViewDefaults, "COL1AVERAGE","0")
			//		gotr.strwrite(sViewDefaults, "COL1MINIMUM","0")
			//		gotr.strwrite(sViewDefaults, "COL1MAXIMUM","0")
			//		gotr.strwrite(sViewDefaults, "COL1DISPLAYASICON","0")

			switch (sFileName)
			{

				case "TN":
					//List type fields
					goTR.StrWrite(ref sViewDefaults, "COLCOUNT", 4);
					goTR.StrWrite(ref sViewDefaults, "COL1FIELD", "<%TXT_File%>");
					goTR.StrWrite(ref sViewDefaults, "COL1LABEL", goData.GetFieldLabelFromName(sFileName, "File"));
					goTR.StrWrite(ref sViewDefaults, "COL1WIDTH", "8");
					goTR.StrWrite(ref sViewDefaults, "COL1ALIGNMENT", "L");
					goTR.StrWrite(ref sViewDefaults, "COL2FIELD", "<%GID_InternalID%>");
					goTR.StrWrite(ref sViewDefaults, "COL2LABEL", goData.GetFieldLabelFromName(sFileName, "Internal ID"));
					goTR.StrWrite(ref sViewDefaults, "COL2WIDTH", "42");
					goTR.StrWrite(ref sViewDefaults, "COL2ALIGNMENT", "L");
					goTR.StrWrite(ref sViewDefaults, "COL3FIELD", "<%TXT_ExternalSource%>");
					goTR.StrWrite(ref sViewDefaults, "COL3LABEL", goData.GetFieldLabelFromName(sFileName, "External Source"));
					goTR.StrWrite(ref sViewDefaults, "COL3WIDTH", "14");
					goTR.StrWrite(ref sViewDefaults, "COL3ALIGNMENT", "L");
					goTR.StrWrite(ref sViewDefaults, "COL4FIELD", "<%TXT_ExternalID%>");
					goTR.StrWrite(ref sViewDefaults, "COL4LABEL", goData.GetFieldLabelFromName(sFileName, "External ID"));
					goTR.StrWrite(ref sViewDefaults, "COL4WIDTH", "60");
					goTR.StrWrite(ref sViewDefaults, "COL4ALIGNMENT", "L");
					break;
					//'MFIELD
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELDCOUNT", 4)
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1NAME", "<%TXT_File%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1LABEL", goData.GetFieldLabelFromName(sFileName, "File"))
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD2NAME", "<%GID_InternalID%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD2LABEL", goData.GetFieldLabelFromName(sFileName, "Internal ID"))
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD3NAME", "<%TXT_ExternalSource%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD3LABEL", goData.GetFieldLabelFromName(sFileName, "External Source"))
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD4NAME", "<%TXT_ExternalID%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD4LABEL", goData.GetFieldLabelFromName(sFileName, "External ID"))
					//'SFIELD
					//goTR.StrWrite(sViewDefaults, "SFIELD_FIELDNAME", "<%SYS_Name%>")
					//goTR.StrWrite(sViewDefaults, "SFIELD_FIELDLABEL", goData.GetFieldLabelFromName(sFileName, "SYS_Name"))
				case "AC":
					//List type fields
					goTR.StrWrite(ref sViewDefaults, "COLCOUNT", 3);
					goTR.StrWrite(ref sViewDefaults, "COL1FIELD", "<%DTE_StartTime%>");
					goTR.StrWrite(ref sViewDefaults, "COL1LABEL", goData.GetFieldLabelFromName(sFileName, "DTE_StartTime"));
					goTR.StrWrite(ref sViewDefaults, "COL1WIDTH", "12");
					goTR.StrWrite(ref sViewDefaults, "COL1ALIGNMENT", "L");
					//		gotr.strwrite(sViewDefaults, "COL1TOTAL","0")
					//		gotr.strwrite(sViewDefaults, "COL1AVERAGE","0")
					//		gotr.strwrite(sViewDefaults, "COL1MINIMUM","0")
					//		gotr.strwrite(sViewDefaults, "COL1MAXIMUM","0")
					//		gotr.strwrite(sViewDefaults, "COL1DISPLAYASICON","0")
					goTR.StrWrite(ref sViewDefaults, "COL2FIELD", "<%TME_StartTime%>");
					goTR.StrWrite(ref sViewDefaults, "COL2LABEL", goData.GetFieldLabelFromName(sFileName, "TME_StartTime"));
					goTR.StrWrite(ref sViewDefaults, "COL2WIDTH", "12");
					goTR.StrWrite(ref sViewDefaults, "COL2ALIGNMENT", "L");
					//		gotr.strwrite(sViewDefaults, "COL2TOTAL","0")
					//		gotr.strwrite(sViewDefaults, "COL2AVERAGE","0")
					//		gotr.strwrite(sViewDefaults, "COL2MINIMUM","0")
					//		gotr.strwrite(sViewDefaults, "COL2MAXIMUM","0")
					//		gotr.strwrite(sViewDefaults, "COL2DISPLAYASICON","0")
					goTR.StrWrite(ref sViewDefaults, "COL3FIELD", "<%MMO_Notes%>");
					goTR.StrWrite(ref sViewDefaults, "COL3LABEL", goData.GetFieldLabelFromName(sFileName, "MMO_Notes"));
					goTR.StrWrite(ref sViewDefaults, "COL3WIDTH", "50");
					goTR.StrWrite(ref sViewDefaults, "COL3ALIGNMENT", "L");
					break;
					//		gotr.strwrite(sViewDefaults, "COL3TOTAL","0")
					//		gotr.strwrite(sViewDefaults, "COL3AVERAGE","0")
					//		gotr.strwrite(sViewDefaults, "COL3MINIMUM","0")
					//		gotr.strwrite(sViewDefaults, "COL3MAXIMUM","0")
					//		gotr.strwrite(sViewDefaults, "COL3DISPLAYASICON","0")
					//'MFIELD
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELDCOUNT", 3)
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1NAME", "<%DTE_StartTime%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1LABEL", goData.GetFieldLabelFromName(sFileName, "DTE_StartTime"))
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD2NAME", "<%TME_StartTime%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD2LABEL", goData.GetFieldLabelFromName(sFileName, "TME_StartTime"))
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD3NAME", "<%MMO_Notes%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD3LABEL", goData.GetFieldLabelFromName(sFileName, "MMO_Notes"))
					//'SFIELD
					//goTR.StrWrite(sViewDefaults, "SFIELD_FIELDNAME", "<%MMO_Notes%>")
					//goTR.StrWrite(sViewDefaults, "SFIELD_FIELDLABEL", goData.GetFieldLabelFromName(sFileName, "MMO_Notes"))
				case "AP":
					goTR.StrWrite(ref sViewDefaults, "COLCOUNT", 3);
					goTR.StrWrite(ref sViewDefaults, "COL1FIELD", "<%DTE_StartTime%>");
					goTR.StrWrite(ref sViewDefaults, "COL1LABEL", goData.GetFieldLabelFromName(sFileName, "DTE_StartTime"));
					goTR.StrWrite(ref sViewDefaults, "COL1WIDTH", "12");
					goTR.StrWrite(ref sViewDefaults, "COL1ALIGNMENT", "L");
					goTR.StrWrite(ref sViewDefaults, "COL2FIELD", "<%TME_StartTime%>");
					goTR.StrWrite(ref sViewDefaults, "COL2LABEL", goData.GetFieldLabelFromName(sFileName, "TME_StartTime"));
					goTR.StrWrite(ref sViewDefaults, "COL2WIDTH", "12");
					goTR.StrWrite(ref sViewDefaults, "COL2ALIGNMENT", "L");
					goTR.StrWrite(ref sViewDefaults, "COL3FIELD", "<%TXT_Description%>");
					goTR.StrWrite(ref sViewDefaults, "COL3LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_Description"));
					goTR.StrWrite(ref sViewDefaults, "COL3WIDTH", "50");
					goTR.StrWrite(ref sViewDefaults, "COL3ALIGNMENT", "L");
					break;
					// ''MFIELD
					//'goTR.StrWrite(sViewDefaults, "MFIELD_FIELDCOUNT", 3)
					//'goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1NAME", "<%DTE_StartTime%>")
					//'goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1LABEL", goData.GetFieldLabelFromName(sFileName, "DTE_StartTime"))
					//'goTR.StrWrite(sViewDefaults, "MFIELD_FIELD2NAME", "<%TME_StartTime%>")
					//'goTR.StrWrite(sViewDefaults, "MFIELD_FIELD2LABEL", goData.GetFieldLabelFromName(sFileName, "TME_StartTime"))
					//'goTR.StrWrite(sViewDefaults, "MFIELD_FIELD3NAME", "<%TXT_Description%>")
					//'goTR.StrWrite(sViewDefaults, "MFIELD_FIELD3LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_Description"))
					// ''SFIELD
					//'goTR.StrWrite(sViewDefaults, "SFIELD_FIELDNAME", "<%MMO_Notes%>")
					//'goTR.StrWrite(sViewDefaults, "SFIELD_FIELDLABEL", goData.GetFieldLabelFromName(sFileName, "MMO_Notes"))
				case "CO":
					goTR.StrWrite(ref sViewDefaults, "COLCOUNT", 3);
					goTR.StrWrite(ref sViewDefaults, "COL1FIELD", "<%TXT_CompanyName%>");
					goTR.StrWrite(ref sViewDefaults, "COL1LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_CompanyName"));
					goTR.StrWrite(ref sViewDefaults, "COL1WIDTH", "28");
					goTR.StrWrite(ref sViewDefaults, "COL1ALIGNMENT", "L");
					goTR.StrWrite(ref sViewDefaults, "COL2FIELD", "<%TXT_CityMailing%>");
					goTR.StrWrite(ref sViewDefaults, "COL2LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_CityMailing"));
					goTR.StrWrite(ref sViewDefaults, "COL2WIDTH", "16");
					goTR.StrWrite(ref sViewDefaults, "COL2ALIGNMENT", "L");
					goTR.StrWrite(ref sViewDefaults, "COL3FIELD", "<%TXT_StateMailing%>");
					goTR.StrWrite(ref sViewDefaults, "COL3LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_StateMailing"));
					goTR.StrWrite(ref sViewDefaults, "COL3WIDTH", "6");
					goTR.StrWrite(ref sViewDefaults, "COL3ALIGNMENT", "L");
					break;
					//'MFIELD
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELDCOUNT", 3)
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1NAME", "<%TXT_CompanyName%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_CompanyName"))
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD2NAME", "<%TXT_CityMailing%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD2LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_CityMailing"))
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD3NAME", "<%TXT_StateMailing%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD3LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_StateMailing"))
					//'SFIELD
					//goTR.StrWrite(sViewDefaults, "SFIELD_FIELDNAME", "<%MMO_Note%>")
					//goTR.StrWrite(sViewDefaults, "SFIELD_FIELDLABEL", goData.GetFieldLabelFromName(sFileName, "MMO_Note"))
				case "CN":
					goTR.StrWrite(ref sViewDefaults, "COLCOUNT", 4);
					goTR.StrWrite(ref sViewDefaults, "COL1FIELD", "<%TXT_NameLast%>");
					goTR.StrWrite(ref sViewDefaults, "COL1LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_NameLast"));
					goTR.StrWrite(ref sViewDefaults, "COL1WIDTH", "16");
					goTR.StrWrite(ref sViewDefaults, "COL1ALIGNMENT", "L");
					goTR.StrWrite(ref sViewDefaults, "COL2FIELD", "<%TXT_NameFirst%>");
					goTR.StrWrite(ref sViewDefaults, "COL2LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_NameFirst"));
					goTR.StrWrite(ref sViewDefaults, "COL2WIDTH", "16");
					goTR.StrWrite(ref sViewDefaults, "COL2ALIGNMENT", "L");
					goTR.StrWrite(ref sViewDefaults, "COL3FIELD", "<%TXT_TitleText%>");
					goTR.StrWrite(ref sViewDefaults, "COL3LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_TitleText"));
					goTR.StrWrite(ref sViewDefaults, "COL3WIDTH", "16");
					goTR.StrWrite(ref sViewDefaults, "COL3ALIGNMENT", "L");
					goTR.StrWrite(ref sViewDefaults, "COL4FIELD", "<%TEL_BusPhone%>");
					goTR.StrWrite(ref sViewDefaults, "COL4LABEL", goData.GetFieldLabelFromName(sFileName, "TEL_BusPhone"));
					goTR.StrWrite(ref sViewDefaults, "COL4WIDTH", "20");
					goTR.StrWrite(ref sViewDefaults, "COL4ALIGNMENT", "L");
					goTR.StrWrite(ref sViewDefaults, "COL4DISPLAYASLINK", "1");
					break;
					//'MFIELD
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELDCOUNT", 4)
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1NAME", "<%TXT_NameLast%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_NameLast"))
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD2NAME", "<%TXT_NameFirst%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD2LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_NameFirst"))
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD3NAME", "<%TXT_TitleText%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD3LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_TitleText"))
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD4NAME", "<%TEL_BusPhone%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD4LABEL", goData.GetFieldLabelFromName(sFileName, "TEL_BusPhone"))
					//'SFIELD
					//goTR.StrWrite(sViewDefaults, "SFIELD_FIELDNAME", "<%MMO_Note%>")
					//goTR.StrWrite(sViewDefaults, "SFIELD_FIELDLABEL", goData.GetFieldLabelFromName(sFileName, "MMO_Note"))
				case "FI":
					goTR.StrWrite(ref sViewDefaults, "COLCOUNT", 2);
					goTR.StrWrite(ref sViewDefaults, "COL1FIELD", "<%TXT_FileName%>");
					goTR.StrWrite(ref sViewDefaults, "COL1LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_FileName"));
					goTR.StrWrite(ref sViewDefaults, "COL1WIDTH", "12");
					goTR.StrWrite(ref sViewDefaults, "COL1ALIGNMENT", "L");
					goTR.StrWrite(ref sViewDefaults, "COL2FIELD", "<%TXT_Description%>");
					goTR.StrWrite(ref sViewDefaults, "COL2LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_Description"));
					goTR.StrWrite(ref sViewDefaults, "COL2WIDTH", "50");
					goTR.StrWrite(ref sViewDefaults, "COL2ALIGNMENT", "L");
					break;
					//'MFIELD
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELDCOUNT", 3)
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1NAME", "<%TXT_FileName%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_FileName"))
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD2NAME", "<%TXT_Description%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD2LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_Description"))
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD3NAME", "<%MMO_Note%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD3LABEL", goData.GetFieldLabelFromName(sFileName, "MMO_Note"))
					//'SFIELD
					//goTR.StrWrite(sViewDefaults, "SFIELD_FIELDNAME", "<%TXT_FileName%>")
					//goTR.StrWrite(sViewDefaults, "SFIELD_FIELDLABEL", goData.GetFieldLabelFromName(sFileName, "TXT_FileName"))
				case "IU":
					goTR.StrWrite(ref sViewDefaults, "COLCOUNT", 3);
					goTR.StrWrite(ref sViewDefaults, "COL1FIELD", "<%TXT_INDUSTRYNAME%>");
					goTR.StrWrite(ref sViewDefaults, "COL1LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_INDUSTRYNAME"));
					goTR.StrWrite(ref sViewDefaults, "COL1WIDTH", "30");
					goTR.StrWrite(ref sViewDefaults, "COL1ALIGNMENT", "L");
					goTR.StrWrite(ref sViewDefaults, "COL2FIELD", "<%TXT_INDUSTRYCODE%>");
					goTR.StrWrite(ref sViewDefaults, "COL2LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_INDUSTRYCODE"));
					goTR.StrWrite(ref sViewDefaults, "COL2WIDTH", "10");
					goTR.StrWrite(ref sViewDefaults, "COL2ALIGNMENT", "L");
					goTR.StrWrite(ref sViewDefaults, "COL3FIELD", "<%MMO_DESCRIPTION%>");
					goTR.StrWrite(ref sViewDefaults, "COL3LABEL", goData.GetFieldLabelFromName(sFileName, "MMO_DESCRIPTION"));
					goTR.StrWrite(ref sViewDefaults, "COL3WIDTH", "50");
					goTR.StrWrite(ref sViewDefaults, "COL3ALIGNMENT", "L");
					break;
					//'MFIELD
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELDCOUNT", 3)
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1NAME", "<%TXT_INDUSTRYNAME%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_INDUSTRYNAME"))
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD2NAME", "<%TXT_INDUSTRYCODE%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD2LABEL", goData.GetFieldLabelFromName(sFileName, "TXT_INDUSTRYCODE"))
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD3NAME", "<%MMO_DESCRIPTION%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD3LABEL", goData.GetFieldLabelFromName(sFileName, "MMO_DESCRIPTION"))
					//'SFIELD
					//goTR.StrWrite(sViewDefaults, "SFIELD_FIELDNAME", "<%MMO_Description%>")
					//goTR.StrWrite(sViewDefaults, "SFIELD_FIELDLABEL", goData.GetFieldLabelFromName(sFileName, "MMO_Description"))
					//'	CASE "MESSAGE"
					//'		'Example of setting column 1 to display as icon. Replace '...' with the field name.
					//'		'LIST
					//'		gotr.strwrite(sViewDefaults, "COLCOUNT", 1)
					//'		gotr.strwrite(sViewDefaults, "COL1FIELD", "...")
					//'		gotr.strwrite(sViewDefaults, "COL1LABEL", goData:GetFieldLabelFromName(sFileName,"..."))
					//'		gotr.strwrite(sViewDefaults, "COL1WIDTH", "4")
					//'		gotr.strwrite(sViewDefaults, "COL1ALIGNMENT", "L")
					//'		gotr.strwrite(sViewDefaults, "COL1DISPLAYASICON", "1")
					//'		'MFIELD
					//'		'SFIELD
				default:
					goTR.StrWrite(ref sViewDefaults, "COLCOUNT", 1);
					goTR.StrWrite(ref sViewDefaults, "COL1FIELD", "<%SYS_Name%>");
					goTR.StrWrite(ref sViewDefaults, "COL1LABEL", goData.GetFieldLabelFromName(sFileName, "SYS_Name"));
					goTR.StrWrite(ref sViewDefaults, "COL1WIDTH", "50");
					goTR.StrWrite(ref sViewDefaults, "COL1ALIGNMENT", "L");
					break;
					//'MFIELD
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELDCOUNT", 1)
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1NAME", "<%SYS_Name%>")
					//goTR.StrWrite(sViewDefaults, "MFIELD_FIELD1LABEL", goData.GetFieldLabelFromName(sFileName, "SYS_Name"))
					//'SFIELD
					//goTR.StrWrite(sViewDefaults, "SFIELD_FIELDNAME", "<%SYS_Name%>")
					//goTR.StrWrite(sViewDefaults, "SFIELD_FIELDLABEL", goData.GetFieldLabelFromName(sFileName, "SYS_Name"))
			}


			//----------------- CALENDAR FIELDS ------------
			//Fill definitions of calendar "lines"
			goTR.StrWrite(ref sViewDefaults, "CALDAY_LINE", GetViewCalLine(sFileName, "CALDAY"));
			goTR.StrWrite(ref sViewDefaults, "CALWEEK_LINE", GetViewCalLine(sFileName, "CALWEEK"));
			goTR.StrWrite(ref sViewDefaults, "CALMONTH_LINE", GetViewCalLine(sFileName, "CALMONTH"));


			//---------- FIELD DEFINITIONS FOR CALENDAR TYPES --------------
			//Fill calendar field definitions
			//CAL_DATETIMEKEYDEF removed
			goTR.StrWrite(ref sViewDefaults, "CAL_DATETIMEFIELDSDEF", goData.GetDefaultCalDateTimeFields(sFileName));

			//Fill Alarm default 'CAL_ALARMDEF'
			s = GetViewCalAlarmDef(sFileName);
			if (s == "")
			{
				goTR.StrDelete(ref sViewDefaults, "CAL_ALARMDEF");
			}
			else
			{
				goTR.StrWrite(ref sViewDefaults, "CAL_ALARMDEF", s);
			}


			//===================== CHART ============================
			//Chart properties are written for all view types to facilitate type switching
			sSort = goData.GetDefaultSort(sFileName, false, par_sViewType);
			sSortArray = sSort.Split(',');
			goTR.StrWrite(ref sViewDefaults, "GRAPHXSORTFIELDS", (sSortArray.GetUpperBound(0) + 1).ToString());

			switch (sFileName)
			{
				case "AC":
				case "AP":
				case "MS":
					//Column, record count
					goTR.StrWrite(ref sViewDefaults, "GRAPHTYPE", "COLUMN");
					goTR.StrWrite(ref sViewDefaults, "GRAPHYSHOW", "COUNT"); //Record count
					break;
					//goTR.StrWrite(sViewDefaults, "GRAPHYFIELD1", "")   'Redundant for record count
				case "TD":
					//Pie, record count
					goTR.StrWrite(ref sViewDefaults, "GRAPHTYPE", "PIE");
					goTR.StrWrite(ref sViewDefaults, "GRAPHYSHOW", "COUNT"); //Record count
					break;
					//goTR.StrWrite(sViewDefaults, "GRAPHYFIELD1", "")   'Redundant for record count
				case "EX":
					//Column, Amount
					goTR.StrWrite(ref sViewDefaults, "GRAPHTYPE", "COLUMN");
					//goTR.StrWrite(sViewDefaults, "GRAPHYSHOW", "TOTAL")    'Total is redundant - that's the default
					goTR.StrWrite(ref sViewDefaults, "GRAPHYFIELD1", "CUR_AMOUNT");
					break;
				case "OP":
					//Column, Value
					goTR.StrWrite(ref sViewDefaults, "GRAPHTYPE", "COLUMN");
					//goTR.StrWrite(sViewDefaults, "GRAPHYSHOW", "TOTAL")    'Total is redundant - that's the default
					goTR.StrWrite(ref sViewDefaults, "GRAPHYFIELD1", "CUR_VALUE");
					break;
				case "QT":
					//Column, Value
					goTR.StrWrite(ref sViewDefaults, "GRAPHTYPE", "COLUMN");
					//goTR.StrWrite(sViewDefaults, "GRAPHYSHOW", "TOTAL")    'Total is redundant - that's the default
					goTR.StrWrite(ref sViewDefaults, "GRAPHYFIELD1", "CUR_TOTAL");
					break;
				case "QL":
					//Column, Value
					goTR.StrWrite(ref sViewDefaults, "GRAPHTYPE", "COLUMN");
					//goTR.StrWrite(sViewDefaults, "GRAPHYSHOW", "TOTAL")    'Total is redundant - that's the default
					goTR.StrWrite(ref sViewDefaults, "GRAPHYFIELD1", "CUR_SUBTOTAL");
					break;
				default:
					//List-type files
					//Column, record count
					goTR.StrWrite(ref sViewDefaults, "GRAPHTYPE", "COLUMN");
					goTR.StrWrite(ref sViewDefaults, "GRAPHYSHOW", "COUNT"); //Record count
					break;
					//goTR.StrWrite(sViewDefaults, "GRAPHYFIELD1", "")   'Redundant for record count
			}


			//====================== COLORS ==========================
			//goTR.StrWrite(sViewDefaults, "LIST_CellBGColor", "255,255,255")
			//goTR.StrWrite(sViewDefaults, "LIST_CellTxtColor", "0,0,0")
			//goTR.StrWrite(sViewDefaults, "LIST_SectionHeadingTxtColor", "128,0,0")        '==> New for grid report View
			//goTR.StrWrite(sViewDefaults, "LIST_SectionSummaryTxtColor", "128,0,0")        '==> New for grid report View
			//goTR.StrWrite(sViewDefaults, "LIST_HeadingBGColor", "255,255,255")        'not supported in display, only in HTML and Print
			//goTR.StrWrite(sViewDefaults, "LIST_HeadingTxtColor", "0,0,0")         'not supported in display, only in HTML and Print

			//goTR.StrWrite(sViewDefaults, "CALDAY_SelectedBGColor", "192,255,255")         'ADDED
			//goTR.StrWrite(sViewDefaults, "CALDAY_SelectedTxtColor", "0,0,0")          'ADDED
			//goTR.StrWrite(sViewDefaults, "CALDAY_NonTimedBGColor", "255,255,255")         'ADDED
			//goTR.StrWrite(sViewDefaults, "CALDAY_NonTimedTxtColor", "0,0,0")          'ADDED
			//'Headings are the top row of the day display
			//goTR.StrWrite(sViewDefaults, "CALDAY_HeadingBGColor", "255,255,255")
			//goTR.StrWrite(sViewDefaults, "CALDAY_HeadingTxtColor", "0,0,0")
			//goTR.StrWrite(sViewDefaults, "CALDAY_NonPrimeBGColor", "192,192,0")       'Renamed from CALDAY_NonPrimeBackColor Ex: Before 8am, after 5pm
			//goTR.StrWrite(sViewDefaults, "CALDAY_NonPrimeTxtColor", "0,0,0")      'ADDED
			//goTR.StrWrite(sViewDefaults, "CALDAY_PrimeBGColor", "255,255,128")        'Renamed from CALDAY_PrimeBackColor 8am-5pm (work hours)
			//goTR.StrWrite(sViewDefaults, "CALDAY_PrimeTxtColor", "0,0,0")     'ADDED
			//goTR.StrWrite(sViewDefaults, "CALDAY_TimeBarBGColor", "255,255,255")      'RENAMED from CALDAY_TimeAreaBackColor

			//goTR.StrWrite(sViewDefaults, "CALWEEK_CellTodayBGColor", "255,255,160")
			//goTR.StrWrite(sViewDefaults, "CALWEEK_CellTodayTxtColor", "0,0,0")
			//goTR.StrWrite(sViewDefaults, "CALWEEK_CellWeekendBGColor", "224,224,224")
			//goTR.StrWrite(sViewDefaults, "CALWEEK_CellWeekendTxtColor", "0,0,0")
			//goTR.StrWrite(sViewDefaults, "CALWEEK_CellWorkDayBGColor", "255,255,255")
			//goTR.StrWrite(sViewDefaults, "CALWEEK_CellWorkDayTxtColor", "0,0,0")
			//'Headings are the first row of each day cell
			//'gotr.strwrite(sViewDefaults,"CALWEEK_HeadingTodayBGColor","255,255,192")		'heading: Day name, date above each day
			//'gotr.strwrite(sViewDefaults,"CALWEEK_HeadingTodayTxtColor","0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALWEEK_HeadingWeekendBGColor","224,224,224")
			//'gotr.strwrite(sViewDefaults,"CALWEEK_HeadingWeekendTxtColor","0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALWEEK_HeadingWorkDayBGColor","255,255,255")
			//'gotr.strwrite(sViewDefaults,"CALWEEK_HeadingWorkDayTxtColor","0,0,0")
			//goTR.StrWrite(sViewDefaults, "CALWEEK_SelDayBGColor", "192,255,255")      'Renamed from CALWEEK_SelDayBackColor
			//goTR.StrWrite(sViewDefaults, "CALWEEK_SelDayTxtColor", "0,0,0")       'ADDED
			//goTR.StrWrite(sViewDefaults, "CALWEEK_TimeBarBGColor", "255,255,255")     'ADDED

			//goTR.StrWrite(sViewDefaults, "CALMONTH_CellTodayBGColor", "255,255,160")
			//goTR.StrWrite(sViewDefaults, "CALMONTH_CellTodayTxtColor", "0,0,0")
			//goTR.StrWrite(sViewDefaults, "CALMONTH_CellInactiveBGColor", "128,128,128")
			//goTR.StrWrite(sViewDefaults, "CALMONTH_CellWeekendBGColor", "224,224,224")
			//goTR.StrWrite(sViewDefaults, "CALMONTH_CellWeekendTxtColor", "0,0,0")
			//goTR.StrWrite(sViewDefaults, "CALMONTH_CellWorkDayBGColor", "255,255,255")
			//goTR.StrWrite(sViewDefaults, "CALMONTH_CellWorkDayTxtColor", "0,0,0")
			//'Headings are day names on top of the whole month calendar
			//goTR.StrWrite(sViewDefaults, "CALMONTH_HeadingWeekendBGColor", "224,224,224")
			//goTR.StrWrite(sViewDefaults, "CALMONTH_HeadingWeekendTxtColor", "0,0,0")
			//goTR.StrWrite(sViewDefaults, "CALMONTH_HeadingWorkDayBGColor", "255,255,255")
			//goTR.StrWrite(sViewDefaults, "CALMONTH_HeadingWorkDayTxtColor", "0,0,0")
			//goTR.StrWrite(sViewDefaults, "CALMONTH_DateNumberTxtColor", "0,0,0")      'ADDED
			//goTR.StrWrite(sViewDefaults, "CALMONTH_SelDayBGColor", "192,255,255")     'Renamed from CALMONTH_SelDateBackColor
			//'gotr.strwrite(sViewDefaults,"CALMONTH_SelDayBorderColor","0,0,128")		'REMOVED
			//goTR.StrWrite(sViewDefaults, "CALMONTH_SelDayTxtColor", "0,0,0")      'Renamed from CALMONTH_SelDateTextColor
			//goTR.StrWrite(sViewDefaults, "CALMONTH_TimeBarBGColor", "255,255,255")        'ADDED

			//goTR.StrWrite(sViewDefaults, "CALYEAR_NonEmptyBGColor", "255,128,128")        'Renamed from CALYEAR_BackBusyColor
			//goTR.StrWrite(sViewDefaults, "CALYEAR_NonEmptyTxtColor", "0,0,0")     'Renamed from CALYEAR_BackBusyColor
			//goTR.StrWrite(sViewDefaults, "CALYEAR_BGColor", "255,255,255")        'Renamed from CALYEAR_BackgroundColor
			//goTR.StrWrite(sViewDefaults, "CALYEAR_TxtColor", "0,0,0")     'Renamed from CALYEAR_TextColor
			//goTR.StrWrite(sViewDefaults, "CALYEAR_SelDateBGColor", "192,255,255")     'Renamed from CALYEAR_SelDateBackColor
			//goTR.StrWrite(sViewDefaults, "CALYEAR_SelDateBorderColor", "0,0,128")     'ADDED?
			//goTR.StrWrite(sViewDefaults, "CALYEAR_SelDateTxtColor", "0,0,0")      'Renamed from CALYEAR_SelDateTextColor
			//goTR.StrWrite(sViewDefaults, "CALYEAR_TodayBGColor", "255,255,160")       'Renamed from CALYEAR_TodayBackColor
			//goTR.StrWrite(sViewDefaults, "CALYEAR_TodayTxtColor", "0,0,0")        'Renamed from CALYEAR_TodayTextColor

			//goTR.StrWrite(sViewDefaults, "MFIELD_CellBGColor", "255,255,255")
			//goTR.StrWrite(sViewDefaults, "MFIELD_CellTxtColor", "0,0,0")
			//'Heading is column on left with field labels
			//goTR.StrWrite(sViewDefaults, "MFIELD_HeadingBGColor", "255,255,255")
			//goTR.StrWrite(sViewDefaults, "MFIELD_HeadingTxtColor", "0,0,0")

			//goTR.StrWrite(sViewDefaults, "SFIELD_CellBGColor", "255,255,255")
			//goTR.StrWrite(sViewDefaults, "SFIELD_CellTxtColor", "0,0,0")


			//====================== FONTS ===========================
			//'-------------- LIST ----------------
			//goTR.StrWrite(sViewDefaults, "LIST_HTMLCellFont", "Arial,1,0,0,0")
			//goTR.StrWrite(sViewDefaults, "LIST_HTMLHeadingFont", "Arial,1,1,0,0")
			//goTR.StrWrite(sViewDefaults, "LIST_HTMLSectionHeadingFont", "Arial,1,1,0,0") 'New for sending grid reports
			//goTR.StrWrite(sViewDefaults, "LIST_HTMLSectionSummaryFont", "Arial,1,1,0,0") 'New for sending grid reports
			//goTR.StrWrite(sViewDefaults, "LIST_PrintTextFont", "Arial,7,0,0,0")      'ADDED
			//goTR.StrWrite(sViewDefaults, "LIST_PrintHeadingFont", "Arial,7,1,0,0")       'ADDED
			//goTR.StrWrite(sViewDefaults, "LIST_PrintSectionHeadingFont", "Arial,7,1,0,0")        'New for printing grid reports
			//goTR.StrWrite(sViewDefaults, "LIST_PrintSectionSummaryFont", "Arial,7,1,0,0")        'New for printing grid reports
			//'gotr.strwrite(sViewDefaults,"LIST_HTMLHeaderFont", "Arial,1,0,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "LIST_HTMLTitleFont", "Arial,3,1,0,0")      'ADDED
			//'gotr.strwrite(sViewDefaults,"LIST_HTMLFooterFont", "Arial,1,0,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "LIST_PrintHeaderFont", "Arial,7,0,0,0")        'ADDED
			//goTR.StrWrite(sViewDefaults, "LIST_PrintTitleFont", "Arial,12,1,0,0")
			//goTR.StrWrite(sViewDefaults, "LIST_PrintFooterFont", "Arial,7,0,0,0")        'ADDED
			//goTR.StrWrite(sViewDefaults, "LIST_TextFont", "MS Sans Serif,8,0,0,0")       '==> New for grid report View
			//goTR.StrWrite(sViewDefaults, "LIST_HeadingFont", "MS Sans Serif,8,0,0,0")        '==> Column heading New for grid report View
			//goTR.StrWrite(sViewDefaults, "LIST_SectionHeadingFont", "MS Sans Serif,8,1,0,1")     '==> Section heading New for grid report View
			//goTR.StrWrite(sViewDefaults, "LIST_SectionSummaryFont", "MS Sans Serif,8,1,0,0")     '==> Column heading New for grid report View



			//'----------------- CALDAY ---------------
			//'gotr.strwrite(sViewDefaults,"CALDAY_ApptTextFont","MS Sans Serif,8,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALDAY_HoursFont","MS Sans Serif,8,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALDAY_PrintTextFont","Arial,8,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALDAY_PrintTimeFont","Arial,8,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALDAY_PrintTimeBarFont","Arial,8,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALDAY_TimesFont","MS Sans Serif,8,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALDAY_TimeBarFont","MS Sans Serif,8,0,0,0")
			//goTR.StrWrite(sViewDefaults, "CALDAY_HTMLCellFont", "Arial,2,0,0,0")
			//goTR.StrWrite(sViewDefaults, "CALDAY_HTMLHeadingFont", "Arial,2,1,0,0")
			//'Time bar not supported in HTML
			//'gotr.strwrite(sViewDefaults,"CALDAY_HTMLTimeBarFont", "Arial,1,0,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "CALDAY_HTMLTimeFont", "Arial,2,0,0,0")     'ADDED
			//'gotr.strwrite(sViewDefaults,"CALDAY_HTMLMinutesFont", "Arial,2,0,0,0")		'ADDED
			//'gotr.strwrite(sViewDefaults,"CALDAY_HTMLHoursFont", "Arial,2,0,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "CALDAY_HeadingFont", "Times New Roman,10,1,0,0")       'ADDED for display only
			//'gotr.strwrite(sViewDefaults,"CALDAY_TimeBarFont", "Small Fonts,7,0,0,0")		'ADDED for display only
			//goTR.StrWrite(sViewDefaults, "CALDAY_MinutesFont", "MS Sans Serif,8,0,0,0")      'ADDED for display only
			//goTR.StrWrite(sViewDefaults, "CALDAY_HoursFont", "MS Sans Serif,8,0,0,0")        'ADDED for display only
			//goTR.StrWrite(sViewDefaults, "CALDAY_TextFont", "MS Sans Serif,8,0,0,0")     'ADDED for display only
			//goTR.StrWrite(sViewDefaults, "CALDAY_PrintHeadingFont", "Times New Roman,12,1,0,0")      'ADDED
			//'Time bar not supported in print
			//'gotr.strwrite(sViewDefaults,"CALDAY_PrintTimeBarFont", "Arial,7,1,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "CALDAY_PrintMinutesFont", "Arial,7,0,0,0")     'ADDED
			//goTR.StrWrite(sViewDefaults, "CALDAY_PrintHoursFont", "Arial,7,0,0,0")       'ADDED
			//goTR.StrWrite(sViewDefaults, "CALDAY_PrintTextFont", "Arial,7,0,0,0")        'ADDED
			//'gotr.strwrite(sViewDefaults,"CALDAY_HTMLHeaderFont", "Arial,1,0,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "CALDAY_HTMLTitleFont", "Arial,3,1,0,0")        'ADDED
			//'gotr.strwrite(sViewDefaults,"CALDAY_HTMLFooterFont", "Arial,1,0,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "CALDAY_PrintHeaderFont", "Arial,7,0,0,0")      'ADDED
			//goTR.StrWrite(sViewDefaults, "CALDAY_PrintTitleFont", "Arial,12,1,0,0")      'ADDED
			//goTR.StrWrite(sViewDefaults, "CALDAY_PrintFooterFont", "Arial,7,0,0,0")      'ADDED


			//'----------------- CALWEEK -----------------
			//'gotr.strwrite(sViewDefaults,"CALWEEK_FontSizeFont","MS Sans Serif,8,0,0,0")
			//goTR.StrWrite(sViewDefaults, "CALWEEK_HTMLHeadingFont", "Arial,1,1,0,0")
			//goTR.StrWrite(sViewDefaults, "CALWEEK_HTMLCellFont", "Arial,1,0,0,0")
			//goTR.StrWrite(sViewDefaults, "CALWEEK_HeadingFont", "MS Sans Serif,8,1,0,0")     'ADDED for display only
			//goTR.StrWrite(sViewDefaults, "CALWEEK_TextFont", "MS Sans Serif,8,0,0,0")        'ADDED for display only
			//goTR.StrWrite(sViewDefaults, "CALWEEK_PrintHeadingFont", "Arial,7,1,0,0")        'ADDED
			//goTR.StrWrite(sViewDefaults, "CALWEEK_PrintTextFont", "Arial,7,0,0,0")       'ADDED
			//'gotr.strwrite(sViewDefaults,"CALWEEK_HTMLHeaderFont", "Arial,1,0,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "CALWEEK_HTMLTitleFont", "Arial,3,1,0,0")       'ADDED
			//'gotr.strwrite(sViewDefaults,"CALWEEK_HTMLFooterFont", "Arial,1,0,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "CALWEEK_PrintHeaderFont", "Arial,7,0,0,0")     'ADDED
			//goTR.StrWrite(sViewDefaults, "CALWEEK_PrintTitleFont", "Arial,12,1,0,0")     'ADDED
			//goTR.StrWrite(sViewDefaults, "CALWEEK_PrintFooterFont", "Arial,7,0,0,0")     'ADDED


			//'----------------- CALMONTH ----------------
			//'gotr.strwrite(sViewDefaults,"CALMONTH_FontFont","Small Fonts,7,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALMONTH_PrintFont","Arial,7,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALMONTH_PrintNumFont","Arial,7,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALMONTH_TitleFont","Small Fonts,7,0,0,0")
			//goTR.StrWrite(sViewDefaults, "CALMONTH_HTMLHeadingFont", "Arial,1,1,0,0")        'ADDED
			//goTR.StrWrite(sViewDefaults, "CALMONTH_HTMLDateNumberFont", "Arial,1,1,0,0")     'ADDED?
			//goTR.StrWrite(sViewDefaults, "CALMONTH_HTMLCellFont", "Arial,1,0,0,0")
			//goTR.StrWrite(sViewDefaults, "CALMONTH_HeadingFont", "MS Sans Serif,8,1,0,0")        'ADDED for display only
			//goTR.StrWrite(sViewDefaults, "CALMONTH_DateNumberFont", "Small Fonts,7,1,0,0")       'ADDED
			//goTR.StrWrite(sViewDefaults, "CALMONTH_TextFont", "Small Fonts,7,0,0,0")     'ADDED for display only
			//goTR.StrWrite(sViewDefaults, "CALMONTH_PrintHeadingFont", "Arial,7,1,0,0")       'ADDED
			//goTR.StrWrite(sViewDefaults, "CALMONTH_PrintDateNumberFont", "Arial,7,1,0,0")        'ADDED
			//goTR.StrWrite(sViewDefaults, "CALMONTH_PrintTextFont", "Arial,7,0,0,0")      'ADDED
			//'gotr.strwrite(sViewDefaults,"CALMONTH_HTMLHeaderFont", "Arial,1,0,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "CALMONTH_HTMLTitleFont", "Arial,3,1,0,0")      'ADDED
			//'gotr.strwrite(sViewDefaults,"CALMONTH_HTMLFooterFont", "Arial,1,0,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "CALMONTH_PrintHeaderFont", "Arial,7,0,0,0")        'ADDED
			//goTR.StrWrite(sViewDefaults, "CALMONTH_PrintTitleFont", "Arial,12,1,0,0")        'ADDED
			//goTR.StrWrite(sViewDefaults, "CALMONTH_PrintFooterFont", "Arial,7,0,0,0")        'ADDED


			//'------------------ CALYEAR ------------------
			//'gotr.strwrite(sViewDefaults,"CALYEAR_DayFont1","Small Fonts,7,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALYEAR_DayFont2","Small Fonts,7,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALYEAR_Font","Small Fonts,7,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALYEAR_TitleFont","Small Fonts,7,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALYEAR_PrintDayFont1","Arial,8,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALYEAR_PrintDayFont2","Arial,8,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALYEAR_PrintFont","Arial,8,0,0,0")
			//'gotr.strwrite(sViewDefaults,"CALYEAR_PrintTitleFont","Arial,12,1,0,0")
			//'Headings in year cals are month names
			//goTR.StrWrite(sViewDefaults, "CALYEAR_HeadingFont", "MS Sans Serif,8,1,0,0")     'ADDED for display only
			//goTR.StrWrite(sViewDefaults, "CALYEAR_TextFont", "MS Sans Serif,8,0,0,0")        'ADDED for display only
			//'Year calendar printing or sending to HTML is not supported


			//'------------------ MFIELD --------------------
			//goTR.StrWrite(sViewDefaults, "MFIELD_HTMLCellFont", "Arial,1,0,0,0")
			//goTR.StrWrite(sViewDefaults, "MFIELD_HTMLHeadingFont", "Arial,1,1,0,0")
			//goTR.StrWrite(sViewDefaults, "MFIELD_PrintTextFont", "Arial,7,0,0,0")        'ADDED
			//goTR.StrWrite(sViewDefaults, "MFIELD_PrintHeadingFont", "Arial,7,1,0,0")     'ADDED
			//'gotr.strwrite(sViewDefaults,"MFIELD_HTMLHeaderFont", "Arial,1,0,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "MFIELD_HTMLTitleFont", "Arial,3,1,0,0")        'ADDED
			//'gotr.strwrite(sViewDefaults,"MFIELD_HTMLFooterFont", "Arial,1,0,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "MFIELD_PrintHeaderFont", "Arial,7,0,0,0")      'ADDED
			//goTR.StrWrite(sViewDefaults, "MFIELD_PrintTitleFont", "Arial,12,1,0,0")      'ADDED
			//goTR.StrWrite(sViewDefaults, "MFIELD_PrintFooterFont", "Arial,7,0,0,0")      'ADDED


			//'----------------- SFIELD ------------------
			//goTR.StrWrite(sViewDefaults, "SFIELD_HTMLCellFont", "Arial,2,0,0,0")
			//goTR.StrWrite(sViewDefaults, "SFIELD_HTMLHeadingFont", "Arial,2,1,0,0")
			//goTR.StrWrite(sViewDefaults, "SFIELD_PrintTextFont", "Arial,8,0,0,0")
			//goTR.StrWrite(sViewDefaults, "SFIELD_PrintHeadingFont", "Arial,8,1,0,0")
			//'gotr.strwrite(sViewDefaults,"SFIELD_HTMLHeaderFont", "Arial,1,0,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "SFIELD_HTMLTitleFont", "Arial,3,1,0,0")        'ADDED
			//'gotr.strwrite(sViewDefaults,"SFIELD_HTMLFooterFont", "Arial,1,0,0,0")		'ADDED
			//goTR.StrWrite(sViewDefaults, "SFIELD_PrintHeaderFont", "Arial,7,0,0,0")      'ADDED
			//goTR.StrWrite(sViewDefaults, "SFIELD_PrintTitleFont", "Arial,12,1,0,0")      'ADDED
			//goTR.StrWrite(sViewDefaults, "SFIELD_PrintFooterFont", "Arial,7,0,0,0")      'ADDED



			//====================== PRINT/SEND ======================
			//-------------------- Header, title, footer --------------------
			//i = 1
			//For i = 1 To 7      'Number of supported View types
			//    Select Case (i)
			//        Case 1
			//            sType = "LIST_"
			//            goTR.StrWrite(sViewDefaults, sType & "HEADERL", "")
			//        Case 2
			//            sType = "CALDAY_"
			//            goTR.StrWrite(sViewDefaults, sType & "HEADERL", "")
			//        Case 3
			//            sType = "CALWEEK_"
			//            goTR.StrWrite(sViewDefaults, sType & "HEADERL", "")
			//        Case 4
			//            sType = "CALMONTH_"
			//            goTR.StrWrite(sViewDefaults, sType & "HEADERL", "")
			//        Case 5
			//            sType = "CALYEAR_"
			//            goTR.StrWrite(sViewDefaults, sType & "HEADERL", "")
			//        Case 6
			//            sType = "MFIELD_"
			//            goTR.StrWrite(sViewDefaults, sType & "HEADERL", "<%SelectedRecordName%>") 'messtranslate MessTraduit(5214))
			//            '5214:<%SelectedRecordName%>
			//        Case 7
			//            sType = "SFIELD_"
			//            goTR.StrWrite(sViewDefaults, sType & "HEADERL", "<%SelectedRecordName%>") 'messtranslate MessTraduit(5214))
			//            '5214:<%SelectedRecordName%>
			//    End Select
			//    goTR.StrWrite(sViewDefaults, sType & "HEADERC", "")
			//    goTR.StrWrite(sViewDefaults, sType & "HEADERR", "Page <%Page%>") 'messtranslate MessTranslate(5001))    '5001:Page <%Page%>
			//    goTR.StrWrite(sViewDefaults, sType & "TITLE", "Page <%Page%>") 'MessTranslate(5002))  '5002:<%ViewName%>
			//    goTR.StrWrite(sViewDefaults, sType & "FOOTERL", "<%DateLong%> <%Time%>") 'MessTranslate(5003))    '5003:<%DateLong%> <%Time%>
			//    goTR.StrWrite(sViewDefaults, sType & "FOOTERC", "")
			//    goTR.StrWrite(sViewDefaults, sType & "FOOTERR", "<%Me%>") 'MessTranslate(5004))    '5004:<%Me%>
			//    goTR.StrWrite(sViewDefaults, sType & "TITLESEND", "<%ViewName%>") 'MessTranslate(5002))  '5002:<%ViewName%>

			//Next i

			//---------------- Send templates -----------------------
			//    'View - List
			//    'Portrait: PGL_2002103016542097217FH__24779XX
			//    'Landscape: PGL_2003121214211710166C_S 00016XX
			//    goTR.StrWrite(sViewDefaults, "LIST_PAGELAYOUT", "PGL_2003121214211710166C_S 00016XX")
			//    goTR.StrWrite(sViewDefaults, "LIST_SENDTEMPLHTML", "SND_2002103016483712250FH__24779XX")

			//    'View - Day Calendar
			//    goTR.StrWrite(sViewDefaults, "CALDAY_PAGELAYOUT", "PGL_2002103016542097217FH__24779XX")
			//    goTR.StrWrite(sViewDefaults, "CALDAY_SENDTEMPLHTML", "SND_2002103016485980210FH__24779XX")

			//    'View - Week Calendar
			//    goTR.StrWrite(sViewDefaults, "CALWEEK_PAGELAYOUT", "PGL_2002103016542097217FH__24779XX")
			//    goTR.StrWrite(sViewDefaults, "CALWEEK_SENDTEMPLHTML", "SND_2002103016494147120FH__24779XX")

			//    'View - Month Calendar
			//    goTR.StrWrite(sViewDefaults, "CALMONTH_PAGELAYOUT", "PGL_2002103016542097217FH__24779XX")
			//    goTR.StrWrite(sViewDefaults, "CALMONTH_SENDTEMPLHTML", "SND_2002103016495712044FH__24779XX")

			//    'Year calendar printing and sending not supported
			//    'gotr.strwrite(sViewDefaults,"CALYEAR_PRNTEMPLHTML","")

			//    'View - Multi field
			//    goTR.StrWrite(sViewDefaults, "MFIELD_PAGELAYOUT", "PGL_2002103016542097217FH__24779XX")
			//    goTR.StrWrite(sViewDefaults, "MFIELD_SENDTEMPLHTML", "SND_2002120116312369113MAI 06556XX")

			//    'View - Single field
			//    goTR.StrWrite(sViewDefaults, "SFIELD_PAGELAYOUT", "PGL_2002103016542097217FH__24779XX")
			//    goTR.StrWrite(sViewDefaults, "SFIELD_SENDTEMPLHTML", "SND_2002120116474960097MAI 06556XX")

		}

		public bool IsFieldAutoFilled(string par_sField, string par_sFile = "")
		{
			//MI 11/2/09 added support for DTY, DTQ, DTM, DTD. Deprecated NGP-only fields, prefixes, and CLEUNIK.
			//PURPOSE:
			//		Find out whether a field is auto-filled or not. Auto-filled
			//		fields are those fields that are automatically given a value,
			//		such as all SYS_ fields, ID fields, etc. A value can be assigned
			//		automatically by SellSQL, by clData, by clForm and clAPI, or through
			//		the clDef:Enforce() method.
			//PARAMETERS:
			//		par_sField: name of the field
			//		par_sFile: optional - name of the field's file. If blank,
			//			the field is considered in the context of all files.
			//			Some fields within certain files can be auto-filled in
			//			clDef:Enforce() method. Be sure to pass the file parameter
			//			if you want to be certain that a field is not auto-filled.
			//RETURNS:
			//		True/False

			string sProc = "clDefaults::IsFieldAutoFilled";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "& par_sField, SELL_LOGLEVEL_DEBUG)

			par_sField = par_sField.ToUpper();
			par_sFile = par_sFile.ToUpper();
			bool bResult = false;
			string sPrefix = null;

			switch (par_sField)
			{
				case "MMO_HISTORY":
				case "LNK_CREATEDBY_USER":
				case "TXT_MODBY":
				case "DTE_MODDATE":
				case "TME_MODTIME":
					return true;
			}
			//Deprecated NGP fields
			//Select Case par_sField
			//    Case "TXT_IDCREATEDBY", "DTE_IDDATE", "TML_IDTIME", "SI__IDCOUNTER", "INT_IDUAID", _
			//     "_LI__SRCSESSION", _
			//     "_TXT_SRCIDCREATEDBYXX", "_DTE_SRCIDDATEXX", "_TML_SRCIDTIMEXX", "_SI__SRCIDCOUNTERXX", "_INT_SRCIDUAIDXX", _
			//     "_TXT_SCFRMIDCREATEDBYXX", "_DTE_SCFRMIDDATEXX", "_TML_SCFRMIDTIMEXX", "_SI__SCFRMIDCOUNTERXX", "_INT_SCFRMIDUAIDXX", _
			//     "DTE_CREATORIDDATEUS", "INT_CREATORIDUAIDUS", "SI__CREATORIDCOUNTERUS", "TML_CREATORIDTIMEUS", "TXT_CREATORIDCREATEDBYUS"
			//        Return True
			//End Select
			//Deprecated NGP field
			//If goTR.FromTo(par_sField, 3) = "CLEUNIK" Then
			//    bResult = 1
			//End If
			sPrefix = goTR.ExtractFieldPrefix(par_sField);
			switch (sPrefix)
			{
				case "SYS":
				case "DTY":
				case "DTQ":
				case "DTM":
				case "DTD":
					return true;
			}
			//..R' fields are deprecated, were used for 'reverse' values in NGP
			//Select Case sPrefix
			//    Case "CHR", "DTR", "MUP", "MUR", "EUP", "EUR", "TEO", "TMR", "TUP", "TUR", _
			//        "CRR", "SRR", "DRR", "INR", "LIR", "SIR", "MLR"
			//        Return True
			//End Select

			switch (par_sFile)
			{
				case "AP":
					switch (par_sField)
					{
						case "DTE_ENDTIME":
							return true;
					}
					break;
			}

			return bResult;
		}
		public bool IsFieldGlobal(string par_sFieldName)
		{
			//CS OK

			//PURPOSE:
			//		Return true or false indicating whether the field is managed globally
			//		and not in the context of each file. This method is used by WCUSTDB and 
			//		WFLDPRO windows when determining whether to allow the user to edit
			//		an MLS_ field or not. An example of a "global" MLS field is MLS_Currency,
			//		which is managed as an MLS field definition for all files in LST_:CURRENCY
			//		metadata page (LST metadata pages define MLS fields). When this field is
			//		encountered, the user can't modify its elements or icon mapping, which is
			//		modifiable (if) via some other UI. In the case of currencies, the list is
			//		managed in the Workgroup Options UI.
			//
			//PARAMETERS:
			//		par_sFieldName: name of the field without filename, but with prefix.
			//			Example: MLS_Currency.
			//
			//RETURNS:
			//		True/false

			string sProc = "clDefaults::IsFieldGlobal";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "& par_sFieldName, SELL_LOGLEVEL_DEBUG)

			string sFieldName = par_sFieldName.Trim(' ').ToUpper();

			switch (sFieldName)
			{
				case "MLS_CURRENCY":
					return (true);
				default:
					return (false);
			}
		}
		public bool IsValidConduitName(string par_sName)
		{
			//CS PORTED as placeholder.

			//AUTHOR: FH 10/9/2002

			//PURPOSE:
			//		Test if the string matches a valid conduit name
			//PARAMETERS:
			//		par_sName:	String to test
			//RETURNS:
			//		True or false
			//HOW IT WORKS:
			//		Look in clDefault GetConduits result to find a match
			//EXAMPLE:
			//		if goDef:IsValidConduitName(sConduit) then

			string sProc = "clDefaults::IsValidConduitName";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "& par_sName, SELL_LOGLEVEL_DETAILS)
			return true; //CS remove

			//sString is string= :GetConduits()
			//sName is string=par_sName
			//sWork is string
			//iI is int=1
			//loop
			//        sWork = extractstring(sString, iI, rc)
			//        If sWork = eot Then break()
			//        If extractstring(sWork, 1, tab) = sName Then result(True)
			//	iI++
			//        End

			//        result(False)
		}
	}

}
