using System;
using System.Web;
using System.Data;
namespace Selltis.BusinessLogic
{
    public class clArray : clInfoMessage
    {
        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;

        public clArray()
        {
            //goP = (clProject)HttpContext.Current.Session["goP"];
            //goTR = (clTransform)HttpContext.Current.Session["goTr"];
            //goMeta = (clMetaData)HttpContext.Current.Session["goMeta"];
            //goData = (clData)HttpContext.Current.Session["goData"];
            //goErr = (clError)HttpContext.Current.Session["goErr"];
        }

        public void Add(string par_sString)
        {
            AddInfo(par_sString);
        }

        public bool Clone(ref clArray par_doArray)
        {
            try
            {
                for (int i = 1; i <= cArrayCollection.Count; i++)
                {
                    par_doArray.AddInfo(cArrayCollection[i]);
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public string GetItem(long par_lItem)
        {
            if (par_lItem < 1 || par_lItem > cArrayCollection.Count)
            {
                return "";
            }
            //return cArrayCollection[Convert.ToInt32(par_lItem)];
            return (par_lItem >= 0 && par_lItem < cArrayCollection.Count) ? cArrayCollection[Convert.ToInt32(par_lItem)] : null;

        }

        public bool Modify(int par_lItem, string par_sNewValue)
        {
            return ModInfo(par_lItem, par_sNewValue);
        }

        public long Seek(string par_sInfo, bool par_bMode = false)
        {
            return SeekInfo(par_sInfo, par_bMode);
        }

        public bool StringToArray(string par_sString)
        {
            string[] sVals = new string[1];
            int i = 0;

            if (par_sString.Contains("\r\n"))
            {
                sVals = par_sString.Split(new[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries);
                foreach (string sVal in sVals)
                {
                    this.AddInfo(sVal);
                }
            }
            else
            {
                this.AddInfo(par_sString);
            }

            return true;
        }
    }
}
