﻿CREATE TABLE [dbo].[PD_RELATED_BC] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_PD_RELATED_BC_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PD] UNIQUEIDENTIFIER NOT NULL,
    [<PERSON><PERSON>_<PERSON>] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PD_RELATED_BC] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_BC_CONNECTED_PD] FOREIGN KEY ([GID_PD]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PD_RELATED_BC] FOREIGN KEY ([GID_BC]) REFERENCES [dbo].[BC] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PD_RELATED_BC] NOCHECK CONSTRAINT [LNK_BC_CONNECTED_PD];


GO
ALTER TABLE [dbo].[PD_RELATED_BC] NOCHECK CONSTRAINT [LNK_PD_RELATED_BC];


GO
CREATE NONCLUSTERED INDEX [IX_PD_RELATED_BC]
    ON [dbo].[PD_RELATED_BC]([GID_PD] ASC, [GID_BC] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_BC_CONNECTED_PD]
    ON [dbo].[PD_RELATED_BC]([GID_BC] ASC, [GID_PD] ASC);

