﻿using System;

//Owner: RH

//Error number ranges:
//
//WT 5000 - 9999 (was <PERSON>'s range)
//Nobody for now 15000 - 19999 (was FH)
//CS 25000 - 29999 (was JL)
//MI 35000 - 39999
//RH 45000 - 49000
//SQL Server 50000 - 59999
//
//This numbering preserves NGP messages which, within each 10000 numbers
//range per developer, do not exceed <start number>+4000.
//
//SS sProcs pass errors as positive resultcodes.
//
//In SS 2000, unhandled SS errors are numbered 1 to 19999. The overlap
//with SellSQL numbers is not a problem because SS messages are not
//displayed or logged within SellSQL. SS errors should not be displayed
//to the user to prevent hackers from gaining information needed to
//execute an exploit such as SQL injection.

using System.Web;


namespace Selltis.BusinessLogic
{
	public class clError
	{

		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clLog goLog;

		public Exception exLastError;



		public void Initialize()
		{

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
		}

		public string GetErrorMessage(long par_lError)
		{

			string sProc = "clError::GetErrorMessage";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			return goLog.GetErrorMessage(par_lError);
		}
		public string GetLastError(string par_sParam = "NUMBER")
		{

			string sProc = "clError::GetLastError";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			return goLog.GetLastError(par_sParam);


		}
		public bool SetError(long par_lErrorNumber = 0, string par_sProcedure = "", string par_sLocalMessage = "", string par_vPar1 = "", string par_vPar2 = "", string par_vPar3 = "", string par_vPar4 = "", string par_vPar5 = "", string par_vPar6 = "", string par_vPar7 = "", string par_vPar8 = "", string par_vPar9 = "", string par_vPar10 = "", Exception par_EX = null)
		{


			//If par_lErrorNumber is 0 the error object is reset without an error being raised.


			string sProc = "clError::SetError";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)


			if (par_EX == null)
			{
				exLastError = null;
			}
			else
			{
				exLastError = par_EX;
			}


			goLog.SetError(par_lErrorNumber, par_sProcedure, par_sLocalMessage, par_vPar1, par_vPar2, par_vPar3, par_vPar4, par_vPar5, par_vPar6, par_vPar7, par_vPar8, par_vPar9, par_vPar10);


// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public bool SetError(Exception par_EX, long par_lErrorNumber = 0, string par_sProcedure = "", string par_sLocalMessage = "", string par_vPar1 = "", string par_vPar2 = "", string par_vPar3 = "", string par_vPar4 = "", string par_vPar5 = "", string par_vPar6 = "", string par_vPar7 = "", string par_vPar8 = "", string par_vPar9 = "", string par_vPar10 = "")
		{

			//Overloaded to allow passing exception.  Error number not required when called this way.
			//If par_lErrorNumber is 0 it is set as 45105, the generic error for unhandled exceptions.

			string sProc = "clError::SetError";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			if (par_lErrorNumber == 0)
			{
				par_lErrorNumber = 45105;
			}

			if (par_EX == null)
			{
				exLastError = null;
			}
			else
			{
				exLastError = par_EX;
			}


			goLog.SetError(par_lErrorNumber, par_sProcedure, par_sLocalMessage, par_vPar1, par_vPar2, par_vPar3, par_vPar4, par_vPar5, par_vPar6, par_vPar7, par_vPar8, par_vPar9, par_vPar10, false);


// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public bool SetWarning(long par_lErrorNumber = 0, string par_sProcedure = "", string par_sLocalMessage = "", string par_vPar1 = "", string par_vPar2 = "", string par_vPar3 = "", string par_vPar4 = "", string par_vPar5 = "", string par_vPar6 = "", string par_vPar7 = "", string par_vPar8 = "", string par_vPar9 = "", string par_vPar10 = "")
		{


			string sProc = "clError::SetError";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)




			goLog.SetError(par_lErrorNumber, par_sProcedure, par_sLocalMessage, par_vPar1, par_vPar2, par_vPar3, par_vPar4, par_vPar5, par_vPar6, par_vPar7, par_vPar8, par_vPar9, par_vPar10, true);


// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}



		public clError()
		{

		}
	}

}
