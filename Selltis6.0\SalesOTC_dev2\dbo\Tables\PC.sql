﻿CREATE TABLE [dbo].[PC] (
    [GID_ID]               UNIQUEIDENTIFIER CONSTRAINT [DF_PC_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),(0))+CONVERT([binary](2),'PC',(0)))+CONVERT([binary](6),getutcdate(),(0)),(0))) ROWGUIDCOL NOT NULL,
    [BI__ID]               BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]             NVARCHAR (80)    NULL,
    [DTT_CreationTime]     DATETIME         CONSTRAINT [DF_PC_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]         TINYINT          NULL,
    [TXT_ModBy]            VARCHAR (4)      NULL,
    [DTT_ModTime]          DATETIME         CONSTRAINT [DF_PC_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_ProductClassName] NVARCHAR (50)    NULL,
    [MMO_ImportData]       NTEXT            NULL,
    [SI__ShareState]       TINYINT          CONSTRAINT [DF_PC_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]     UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]         VARCHAR (50)     NULL,
    [TXT_ExternalID]       NVARCHAR (80)    NULL,
    [TXT_ExternalSource]   VARCHAR (10)     NULL,
    [TXT_ImpJobID]         VARCHAR (20)     NULL,
    [MLS_LEGACYDATASOURCE] SMALLINT         NULL,
    CONSTRAINT [PK_PC] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_PC_ProductClassName]
    ON [dbo].[PC]([TXT_ProductClassName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PC_TXT_ImportID]
    ON [dbo].[PC]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PC_Name]
    ON [dbo].[PC]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PC_ModDateTime]
    ON [dbo].[PC]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PC_CreatedBy_US]
    ON [dbo].[PC]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PC_CreationTime]
    ON [dbo].[PC]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_PC_BI__ID]
    ON [dbo].[PC]([BI__ID] ASC);


GO

CREATE TRIGGER [dbo].[trPCUpdateTN]
ON [dbo].[PC]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in PC table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(120)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'PC'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable
					and tn2.GID_InternalID = inserted.GID_ID)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [PC]
			SET [PC].TXT_ExternalSource = '', [PC].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [PC].GID_ID = in1.GID_ID
				and ISNULL([PC].TXT_ExternalSource, '') <> ''
				and ISNULL([PC].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trPCUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trPCUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trPCUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!
