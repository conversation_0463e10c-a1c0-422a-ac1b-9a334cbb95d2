﻿CREATE TABLE [dbo].[TD_Related_PD] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_ToDo_Related_Product_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_PD] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_Related_PD] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PD_Connected_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_Related_PD] FOREIGN KEY ([GID_PD]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_Related_PD] NOCHECK CONSTRAINT [LNK_PD_Connected_TD];


GO
ALTER TABLE [dbo].[TD_Related_PD] NOCHECK CONSTRAINT [LNK_TD_Related_PD];


GO
CREATE CLUSTERED INDEX [IX_PD_Connected_TD]
    ON [dbo].[TD_Related_PD]([GID_TD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_Related_PD]
    ON [dbo].[TD_Related_PD]([GID_PD] ASC);

