'Owner: RH

'Error number ranges:
'
'WT 5000 - 9999 (was <PERSON>'s range)
'Nobody for now 15000 - 19999 (was FH)
'CS 25000 - 29999 (was J<PERSON>)
'MI 35000 - 39999
'RH 45000 - 49000
'SQL Server 50000 - 59999
'
'This numbering preserves NGP messages which, within each 10000 numbers
'range per developer, do not exceed <start number>+4000.
'
'SS sProcs pass errors as positive resultcodes.
'
'In SS 2000, unhandled SS errors are numbered 1 to 19999. The overlap
'with SellSQL numbers is not a problem because SS messages are not
'displayed or logged within SellSQL. SS errors should not be displayed
'to the user to prevent hackers from gaining information needed to
'execute an exploit such as SQL injection.

Imports Microsoft.VisualBasic
Imports System
Imports System.Web
Imports System.Data


Public Class clError

    Dim goP As clProject
    Dim goMeta As clMetaData
    Dim goTR As clTransform
    Dim goData As clData
    Dim goErr As clError
    Dim goLog As clLog

    Public exLastError As Exception



    Public Sub Initialize()

        goP = HttpContext.Current.Session("goP")
        goTR = HttpContext.Current.Session("goTr")
        goMeta = HttpContext.Current.Session("goMeta")
        goData = HttpContext.Current.Session("goData")
        goErr = HttpContext.Current.Session("goErr")
        goLog = HttpContext.Current.Session("goLog")
    End Sub

    Function GetErrorMessage(ByVal par_lError As Long) As String

        Dim sProc As String = "clError::GetErrorMessage"
        'if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

        Return goLog.GetErrorMessage(par_lError)
    End Function
    Function GetLastError(Optional ByVal par_sParam As String = "NUMBER") As String

        Dim sProc As String = "clError::GetLastError"
        'if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

        Return goLog.GetLastError(par_sParam)


    End Function
    Function SetError(Optional ByVal par_lErrorNumber As Long = 0, Optional ByVal par_sProcedure As String = "", Optional ByVal par_sLocalMessage As String = "", _
          Optional ByVal par_vPar1 As String = "", Optional ByVal par_vPar2 As String = "", Optional ByVal par_vPar3 As String = "", _
          Optional ByVal par_vPar4 As String = "", Optional ByVal par_vPar5 As String = "", Optional ByVal par_vPar6 As String = "", Optional ByVal par_vPar7 As String = "", _
          Optional ByVal par_vPar8 As String = "", Optional ByVal par_vPar9 As String = "", Optional ByVal par_vPar10 As String = "", Optional ByVal par_EX As Exception = Nothing) As Boolean


        'If par_lErrorNumber is 0 the error object is reset without an error being raised.


        Dim sProc As String = "clError::SetError"
        'if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)


        If par_EX Is Nothing Then
            exLastError = Nothing
        Else
            exLastError = par_EX
        End If


        goLog.SetError(par_lErrorNumber, par_sProcedure, par_sLocalMessage, _
                  par_vPar1, par_vPar2, par_vPar3, _
                  par_vPar4, par_vPar5, par_vPar6, par_vPar7, _
                  par_vPar8, par_vPar9, par_vPar10)


    End Function

    Function SetError(ByVal par_EX As Exception, Optional ByVal par_lErrorNumber As Long = 0, Optional ByVal par_sProcedure As String = "", Optional ByVal par_sLocalMessage As String = "", _
      Optional ByVal par_vPar1 As String = "", Optional ByVal par_vPar2 As String = "", Optional ByVal par_vPar3 As String = "", _
      Optional ByVal par_vPar4 As String = "", Optional ByVal par_vPar5 As String = "", Optional ByVal par_vPar6 As String = "", Optional ByVal par_vPar7 As String = "", _
      Optional ByVal par_vPar8 As String = "", Optional ByVal par_vPar9 As String = "", Optional ByVal par_vPar10 As String = "") As Boolean

        'Overloaded to allow passing exception.  Error number not required when called this way.
        'If par_lErrorNumber is 0 it is set as 45105, the generic error for unhandled exceptions.

        Dim sProc As String = "clError::SetError"
        'if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

        If par_lErrorNumber = 0 Then par_lErrorNumber = 45105

        If par_EX Is Nothing Then
            exLastError = Nothing
        Else
            exLastError = par_EX
        End If


        goLog.SetError(par_lErrorNumber, par_sProcedure, par_sLocalMessage, _
                  par_vPar1, par_vPar2, par_vPar3, _
                  par_vPar4, par_vPar5, par_vPar6, par_vPar7, _
                  par_vPar8, par_vPar9, par_vPar10, False)


    End Function

    Function SetWarning(Optional ByVal par_lErrorNumber As Long = 0, Optional ByVal par_sProcedure As String = "", Optional ByVal par_sLocalMessage As String = "", _
      Optional ByVal par_vPar1 As String = "", Optional ByVal par_vPar2 As String = "", Optional ByVal par_vPar3 As String = "", _
      Optional ByVal par_vPar4 As String = "", Optional ByVal par_vPar5 As String = "", Optional ByVal par_vPar6 As String = "", _
      Optional ByVal par_vPar7 As String = "", Optional ByVal par_vPar8 As String = "", Optional ByVal par_vPar9 As String = "", _
      Optional ByVal par_vPar10 As String = "") As Boolean


        Dim sProc As String = "clError::SetError"
        'if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)




        goLog.SetError(par_lErrorNumber, par_sProcedure, par_sLocalMessage, _
                  par_vPar1, par_vPar2, par_vPar3, _
                  par_vPar4, par_vPar5, par_vPar6, par_vPar7, _
                  par_vPar8, par_vPar9, par_vPar10, True)


    End Function

    

    Public Sub New()

    End Sub
End Class
