﻿CREATE TABLE [dbo].[OL] (
    [GID_ID]                  UNIQUEIDENTIFIER CONSTRAINT [DF_OL_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'OL',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                  BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                NVARCHAR (80)    NULL,
    [DTT_CreationTime]        DATETIME         CONSTRAINT [DF_OL_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]            TINYINT          NULL,
    [TXT_ModBy]               VARCHAR (4)      NULL,
    [DTT_ModTime]             DATETIME         CONSTRAINT [DF_OL_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_OpportunityLineName] NVARCHAR (50)    NULL,
    [MMO_ImportData]          NTEXT            NULL,
    [SI__ShareState]          TINYINT          CONSTRAINT [DF_OL_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]        UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]            VARCHAR (50)     NULL,
    [TXT_ExternalID]          NVARCHAR (80)    NULL,
    [TXT_ExternalSource]      VARCHAR (10)     NULL,
    [TXT_ImpJobID]            VARCHAR (20)     NULL,
    [GID_RELATED_PD]          UNIQUEIDENTIFIER NULL,
    [TXT_ItemNumber]          VARCHAR (500)    NULL,
    [TXT_LineDescription]     VARCHAR (500)    NULL,
    [SR__Quantity]            REAL             NULL,
    [CUR_SalePrice]           MONEY            NULL,
    [CUR_ListPrice]           MONEY            NULL,
    [CUR_DailyRentalRate]     MONEY            NULL,
    [LI__MinRentalDays]       INT              NULL,
    [CUR_ExtendedPrice]       MONEY            NULL,
    [CUR_RentalRevenue]       MONEY            NULL,
    [TXT_SFOppLineID]         NVARCHAR (50)    NULL,
    [SI__LineNo]              TINYINT          NULL,
    [MMO_DESCRIPTION]         NTEXT            NULL,
    [GID_RELATED_PG]          UNIQUEIDENTIFIER NULL,
    [GID_RELATED_VE]          UNIQUEIDENTIFIER NULL,
    [GID_RELATED_MO]          UNIQUEIDENTIFIER NULL,
    [SR__GO]                  REAL             NULL,
    [SR__Profit]              REAL             NULL,
    [CUR_TotalValue]          MONEY            NULL,
    [CUR_WeightedValue]       MONEY            NULL,
    [CUR_TotalProfit]         MONEY            NULL,
    [CUR_WeightedProfit]      MONEY            NULL,
    [CUR_UnitPrice]           MONEY            NULL,
    [SR__GET]                 REAL             NULL,
    [CHK_Report]              TINYINT          NULL,
    [CUR_COST]                MONEY            NULL,
    [TXT_OpportunityName]     NVARCHAR (1000)  NULL,
    [GID_RELATED_PF]          UNIQUEIDENTIFIER NULL,
    [GID_ORIGINATEDBY_CN]     UNIQUEIDENTIFIER NULL,
    [GID_FOR_CO]              UNIQUEIDENTIFIER NULL,
    [GID_CREDITEDTO_US]       UNIQUEIDENTIFIER NULL,
    [DTT_EXPCLOSEDATE]        DATETIME         NULL,
    [CHK_DIRECTQUOTE]         TINYINT          NULL,
    [Gid_TempOP]              UNIQUEIDENTIFIER NULL,
    [CHK_ASSEMBLYLINEITEM]    TINYINT          NULL,
    [GID_ParentLineId]        UNIQUEIDENTIFIER NULL,
    [SR__LineNo]              REAL             NULL,
    [MMO_NOTES]               NTEXT            NULL,
    [GID_RELATED_BC]          UNIQUEIDENTIFIER NULL,
    [GID_RELATED_BR]          UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_OL] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_OL_OpportunityLineName]
    ON [dbo].[OL]([TXT_OpportunityLineName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OL_CreatedBy_US]
    ON [dbo].[OL]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OL_ModDateTime]
    ON [dbo].[OL]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OL_Name]
    ON [dbo].[OL]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OL_CreationTime]
    ON [dbo].[OL]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_OL_BI__ID]
    ON [dbo].[OL]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OL_TXT_ImportID]
    ON [dbo].[OL]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OL_RELATED_PF]
    ON [dbo].[OL]([GID_RELATED_PF] ASC)
    INCLUDE([DTT_EXPCLOSEDATE]);

