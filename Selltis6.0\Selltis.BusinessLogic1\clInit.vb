'OWNER: RH
Imports Microsoft.VisualBasic
Imports System.Web
Imports System.Configuration
Imports System.IO

Public Class clInit

    'MI 3/13/08 Added goTr.InitClass()

    Dim goP As clProject
    Dim goMeta As clMetaData
    Dim goTR As clTransform
    Dim goData As clData
    Dim goErr As clError
    Dim goLog As clLog
    Dim goDef As clDefaults
    Dim goScr As clScrMngRowSet
    Dim goNumMask As clNumMask
    ' Dim goUI As clUI
    Dim goHist As clHistory
    Dim goPerm As clPerm


    Public Sub New()
        Try

            If HttpContext.Current.Session("USERID").ToString = "" Then

            End If
        Catch ex As Exception
            HttpContext.Current.Response.Redirect("../pages/redirect.aspx", True)
        End Try


        InitializeSession()


    End Sub

    Private Function InitializeSession() As Boolean
        'MI 11/1/07 Added testing session("Connection") is NULL - was raising exception if NULL

        Try
            Dim sHostName As String = clSettings.GetHostName()
            If HttpContext.Current.Session(sHostName + "_SiteSettings") Is Nothing Then
                clSettings.LoadSiteSettings()
            End If

            If HttpContext.Current.Session("Connection") Is Nothing Then
                Return Initialize()
            Else
                If HttpContext.Current.Session("Connection").ToString = "" Then
                    Return Initialize()
                Else
                    Return True
                End If
            End If
        Catch ex As Exception
            Return Initialize()
        End Try

    End Function

    Private Function Initialize() As Boolean
        'MI 11/9/09 Moved NumMask, goTr and time zone initialization above loading lists.
        'MI 1/14/09 Added goP.LoadProductList()
        Dim sProc As String = "clInit::Initialize"


        Try
            HttpContext.Current.Session("goP") = New clProject
            HttpContext.Current.Session("goTr") = New clTransform
            HttpContext.Current.Session("goMeta") = New clMetaData
            HttpContext.Current.Session("goData") = New clData
            HttpContext.Current.Session("goErr") = New clError
            HttpContext.Current.Session("goLog") = New clLog
            HttpContext.Current.Session("goDef") = New clDefaults
            HttpContext.Current.Session("goScr") = New clScrMngRowSet
            HttpContext.Current.Session("goNumMask") = New clNumMask
            'HttpContext.Current.Session("goUI") = New clUI
            HttpContext.Current.Session("goHist") = New clHistory
            HttpContext.Current.Session("goPerm") = New clPerm

            'HttpContext.Current.Session("Connection") = ConfigurationManager.ConnectionStrings(sConnectionString).ConnectionString ''ConfigurationManager.ConnectionStrings("SelltisConnectionString").ConnectionString

            HttpContext.Current.Session("Connection") = DirectCast(HttpContext.Current.Session(clSettings.GetHostName() + "_SiteSettings"), DataTable).Rows(0)("ConnectionString")


            goP = HttpContext.Current.Session("goP")
            goTR = HttpContext.Current.Session("goTr")
            goMeta = HttpContext.Current.Session("goMeta")
            goData = HttpContext.Current.Session("goData")
            goErr = HttpContext.Current.Session("goErr")
            goLog = HttpContext.Current.Session("goLog")
            goDef = HttpContext.Current.Session("goDef")
            goScr = HttpContext.Current.Session("goScr")
            goNumMask = HttpContext.Current.Session("goNumMask")
            'goUI = HttpContext.Current.Session("goUI")
            goHist = HttpContext.Current.Session("goHist")
            goPerm = HttpContext.Current.Session("goPerm")

            goP.Initialize()
            goTR.Initialize()
            goMeta.Initialize()
            goData.Initialize()
            goErr.Initialize()
            goLog.Initialize()
            goDef.Initialize()
            goScr.Initialize()
            goNumMask.Initialize()
            'goUI.Initialize()
            goHist.Initialize()
            goPerm.Initialize()

            goP.InitClass()

            goLog.SetLogLevel(goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "LOGLEVEL", "0", True))
            goP.SellTraceMode(goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "TRACELEVEL", clC.SELLTRACE_USER_AND_EMPTY, True))

            'MI 11/9/09 Moved here from below the list loading block
            '------- Initialization code from W_MAIN in NGP -------
            goNumMask.InitClass()
            goTR.InitClass()            '*** MI added 3/13/08
            goTR.InitDateTime()
            goTR.UTC_InitTimeZones()    '*** MI added 9/11/07
            'Initialize time zone
            goP.InitProject2()          '*** MI added 9/13/07

            '------------- Load lists -------------
            'Load product list
            goP.LoadProductList()
            'Load schema datatables
            goData.LoadSchemaData()

            goP.VerifyDatabaseID()

            Initialize = True
            'HttpContext.Current.Session.Timeout = 30



        Catch ex As Exception
            'Error initializing the project.
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If

            Initialize = False
        End Try

    End Function

    Public Function ReInitialize() As Boolean

        Return Initialize()




    End Function


End Class
