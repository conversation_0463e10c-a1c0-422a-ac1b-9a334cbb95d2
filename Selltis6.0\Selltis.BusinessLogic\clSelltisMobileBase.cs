﻿using System;
using System.IO;
using System.Web;
using System.Runtime.Serialization;
using System.Collections.Generic;

namespace Selltis.BusinessLogic
{
	public class clSelltisMobileBase
	{
		protected clRowSet m_RS;
		protected cljsondata m_clData;
		protected ListFields m_clListFields;
		protected clTransform m_goTR;
		protected clError m_goErr;
		protected clData m_goData;
		protected clProject m_goP;
		protected clLog m_goLog;
		protected clMetaData m_goMeta;




		public clSelltisMobileBase(cljsondata data)
		{
			m_clData = data;
			m_clListFields = new ListFields(m_clData.lstData);

			m_goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			m_goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			m_goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			m_goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			m_goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			m_goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];

		}

		public void WriteLog(string proc, string data)
		{
			m_goLog.Log(proc, data, 0, true);
		}

		public virtual void SaveOpen()
		{


			//get the new clrowset
			if (m_clData.IsNew == false)
			{
				m_RS = new clRowSet(m_clData.filename, clC.SELL_EDIT, m_clData.filter, "", "*", 1, "", ""); //Update
			}
			else
			{
				m_RS = new clRowSet(m_clData.filename, clC.SELL_ADD, "", "", "", -1, "", "", m_clData.createType, m_clData.createFrom, "", false); //Insert
			}

			m_RS.ToTable();

			//V_T create the rowset fields collection to manage the dirty fields collection
			//'m_RS.PrepareDirtyFieldsCollection()

		}

		public virtual void UpdateRowSet()
		{

			//WriteLog("Base", "UpdateRowset")

			//clear all links 
			if (m_clData.IsNew == false)
			{
				foreach (var item in m_clData.lstData)
				{
					if (item.FieldName.Contains("LNK"))
					{
						m_RS.ClearLinkAll(item.FieldName);
					}
				}
			}

			//set field values
			foreach (var item in m_clData.lstData)
			{
				if (item.FieldName.StartsWith("CUR_"))
				{
					m_RS.SetFieldVal(item.FieldName, item.FieldValue.Replace("$", ""), 1);
				}
				else
				{
					m_RS.SetFieldVal(item.FieldName, item.FieldValue, 1);
				}
			}
		}

		public virtual bool OnPreSaveData(string SectionsToDisable)
		{
			//CS passing a string variable from the cus file that lists all sections that should not run in the base

			//WriteLog("Base", "OnSavePreData")
			//WriteLog("OnPreSaveSectionsToDisable", SectionsToDisable)

			//CS 05192015 This runs before the record has been committed to the rowset and before all the fields have been set to the rowset.
			//To customize this function, add code to the top of the same named function in cus_ Mobile file
			//and then call this function from it.
			//If you do not want to call this function remove the call from the _OnPreSave function in the cus file.

			//AC core functions; pass variable from cus Pre script telling which scripting functionality to ignore in the base
			if (ProcessAC_Pre(SectionsToDisable) == false)
			{
				return false;
			}

			if (ProcessOP_Pre(SectionsToDisable) == false)
			{
				return false;
			}



			return true;


		}

		public virtual bool OnPostSaveData(string SectionsToDisable)
		{

			//WriteLog("Base", "OnSavePostData")

			//CS 05192015 This runs after the record has been committed to the rowset.
			//To customize this function, add code to the top of the same named function in cus_ Mobile file
			//and then call this function from it.
			//If you do not want to call this function remove the call from the _OnPostSave function in the cus file.

			//AC core post functions; pass variable from cus Post script telling which scripting functionality to ignore in the base
			if (ProcessAC_Post(SectionsToDisable) == false)
			{
				return false;
			}

			//OP core post functions; pass variable from cus Post script telling which scripting functionality to ignore in the base
			if (ProcessOP_Post(SectionsToDisable) == false)
			{
				return false;
			}


			return true;

		}

		public string SaveType
		{

			get
			{
				if (m_clData.IsNew == false)
				{
					return "Update";
				}
				else
				{
					return "Save";
				}
			}

		}

		public clServiceOutput GetReturnErrorMessage()
		{
			clServiceOutput objclServiceOutput = new clServiceOutput();

			string sMsg;

			sMsg = m_goErr.GetLastError("MESSAGE");
			WriteLog("SaveMsg", sMsg);

			if (m_goErr.GetLastError("NUMBER") == "E47260") //validation error
			{
				WriteLog("SaveField", m_goTR.ExtractString(m_goErr.GetLastError("PARAMS"), 1));
				string sField = m_goTR.ExtractString(m_goErr.GetLastError("PARAMS"), 1); //DB field name
				var sFieldLabel = m_goData.GetFieldLabelFromName(m_clData.filename, sField); //Field label

				sMsg = "The field " + sFieldLabel + " must contain a value.";

				objclServiceOutput.Status = "Fail";
				objclServiceOutput.Data = SaveType + " Failed - " + sMsg;
			}
			else
			{
				objclServiceOutput.Status = "Fail";
				objclServiceOutput.Data = SaveType + " Failed - " + sMsg;

			}

			return objclServiceOutput;
		}
		public virtual clServiceOutput Save()
		{

			clServiceOutput objclServiceOutput = new clServiceOutput();


			SaveOpen();

			//Custom or base function failed due to missing rquired field, etc
			bool bReturn;
			bReturn = OnPreSaveData("");

			if (bReturn == false)
			{
				//WriteLog("Save", "Returned False")
				return GetReturnErrorMessage();
			}

			UpdateRowSet();

			//commit transaction
			int retval = m_RS.Commit();

			if (retval == 1)
			{

				OnPostSaveData(""); //This runs after the record has been committed to the rowset.



				objclServiceOutput.Status = "Success";
				//objclServiceOutput.Data = sType & "d Successfully"

				//Get GID_ID & SYS_NAME from the rs
				//V_T 4/20/2015
				string sGID_ID = m_RS.oDataSet.Tables[0].Rows[0]["GID_ID"].ToString();
				string sSYS_NAME = m_RS.SYS_NAME_LastCommit;
				objclServiceOutput.Data = SaveType + "d Successfully" + "|" + sGID_ID + "|" + sSYS_NAME;

			}
			else
			{
				//MS 5/12 commented by Manmeet, added fail message details

				return GetReturnErrorMessage();

			}

			return objclServiceOutput;
		}
		public bool ProcessAC_Pre(string SectionsToDisable)
		{
			//WriteLog("Base", "ProcessAC")
			//WriteLog("ProcessACSectionsToDisable", SectionsToDisable)

			//NOTE: Every section of code should be pre-pended to check if it was disabled in the custom pre script

			if (m_clData.filename.ToUpper() != "AC")
			{
				return true;
			}

			if (m_goTR.StrRead(SectionsToDisable, "FILLACENDDATEANDTIME") == "") //Only call base function if custom pre script didn't disable it
			{
				//WriteLog("BaseProcessAC", "Run")
				if (m_clData.IsNew)
				{
					m_clListFields.CopyField("DTE_ENDTIME", "DTE_STARTTIME", "Today");
					m_clListFields.CopyField("TME_ENDTIME", "TME_STARTTIME", "Now");
				}
			}
			else
			{
				//disabled in cus script
				//WriteLog("Base", "Don't Fill End Date")
			}

			//If m_goTR.StrRead(SectionsToDisable, "EnforceProductIfPurposeIsInquiry") = "" Then
			//    'WriteLog("ProcessAC:purpose", m_clListFields.GetField("MLS_PURPOSE"))
			//    If m_clListFields.GetField("MLS_PURPOSE") = "Inquiry" And m_clListFields.GetField("LNK_RELATED_PD") = "" Then
			//        'Dim sFieldLabel As String = m_goData.GetFieldLabelFromName(m_clData.filename, "LNK_RELATED_PD")
			//        m_goErr.SetWarning(47260, "ProcessAC_Pre", "", "LNK_RELATED_PD", "", "", "", "", "", "", "", "", "LNK_RELATED_PD")
			//        Return False
			//    End If
			//End If

			//CS don't think we can do this b/c Journal field is not available on creation of a new AC?
			//If m_goTR.StrRead(SectionsToDisable, "CopyNotesToJournalIfNewLead") = "" Then
			//    If m_clData.IsNew Then
			//        If m_clListFields.GetField("MLS_PURPOSE") = "Lead" And m_clListFields.GetField("MMO_NOTES") <> "" Then
			//            'If journal is blank
			//            If m_clListFields.GetField("MMO_JOURNAL") = "" Then
			//                'Dim sDateCode As String = m_goTR.NowLocal '& " " & m_goP.GetMe("CODE")
			//                Dim dtNow As DateTime = m_goTR.NowLocal()
			//                Dim sDateStamp As String = Left(m_goTR.DateTimeToSysString(dtNow, , " "), 16)
			//                WriteLog("DateandCode", sDateStamp)
			//                m_clListFields.SetField("MMO_JOURNAL", sDateStamp & " " & m_clListFields.GetField("MMO_NOTES"))
			//            Else
			//                'If journal is not blank        '               
			//                m_clListFields.SetField("MMO_JOURNAL", m_clListFields.GetField("MMO_JOURNAL") & " " & m_clListFields.GetField("MMO_NOTES"))
			//            End If
			//        End If
			//    End If
			//End If     

			//If editing an AC, capture value of Journal being added. This will be used in Post Save script to create the linked Journal AC
			if (m_goTR.StrRead(SectionsToDisable, "CreateJournalActivity") == "")
			{
				if (m_clListFields.GetField("MMO_JOURNAL") != "") //MMO_JOUrnal only passed when adding a journal entry
				{
					string sRecordID = Convert.ToString(m_RS.GetFieldVal("GID_ID"));
					string sOriginalJournal = Convert.ToString(m_RS.GetFieldVal("MMO_JOURNAL"));
					string sJournalAdded = m_clListFields.GetField("MMO_JOURNAL");
					sJournalAdded = m_goTR.Replace(sJournalAdded, sOriginalJournal, "");
					m_goP.SetVar("NewJournal", sJournalAdded);
				}
			}


			return true;
		}
		public bool ProcessAC_Post(string SectionsToDisable)
		{

			if (m_clData.filename.ToUpper() != "AC")
			{
				return true;
			}

			if (m_goTR.StrRead(SectionsToDisable, "CreateJournalActivity") == "")
			{
				//Only in edit mode
				if (m_clData.IsNew == false)
				{
					//Check if have WOP enabled to create journal activity when journal field is updated.
					if (m_goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "ONSAVEAC_CREATE_AC", "0") == "1")
					{
						//Check if there is a value in MMO_JOURNAl; when adding a journal to a record we only have MMO_JOURNAL field in m_clListFields
						//WriteLog("ProcessAC_Post", "WOP on")
						if (Convert.ToString(m_goP.GetVar("NewJournal")) != "")
						{
							string sRecordID = Convert.ToString(m_RS.GetFieldVal("GID_ID"));
							CreateJournalAC(sRecordID, Convert.ToString(m_goP.GetVar("NewJournal")));
							m_goP.SetVar("NewJournal", "");
						}

					}
				}
			}

			return true;
		}
		public bool CreateJournalAC(string RecordID, string JournalAdded)
		{

			string sRecordID = RecordID;
			string sJournalAdded = JournalAdded;

			if (sRecordID != "" && sJournalAdded != "")
			{

				//Dim sNotes As String = sJournalAdded & vbCrLf & vbCrLf & "== Created from Activity '" & m_RS.GetFieldVal("SYS_NAME") & "'"
				string sNotes = sJournalAdded + "\r\n" + "\r\n" + "== Created from " + m_clData.filename + " '" + m_RS.GetFieldVal("SYS_NAME").ToString() + "'";

				clRowSet doNew = new clRowSet("AC", 2, "", "", "", -1, "", "", "CRL_AC", sRecordID, "", true);
				doNew.SetFieldVal("MMO_NOTES", sNotes);
				doNew.SetFieldVal("MLS_TYPE", 31, 2);
				doNew.SetFieldVal("MLS_Status", 1, 2); //Completed
				doNew.SetFieldVal("DTT_StartTime", "Today|Now");
				doNew.SetFieldVal("DTT_EndTime", "Today|Now");
				doNew.SetFieldVal("LNK_CreditedTo_US", m_goP.GetMe("ID"));
				doNew.SetFieldVal("MMO_HISTORY", m_goTR.WriteLogLine("", "Created."));
				doNew.Commit();

			}


			return true;
		}

		public bool ProcessOP_Pre(string SectionsToDisable)
		{

			//NOTE: Every section of code should be pre-pended to check if it was disabled in the custom pre script

			if (m_clData.filename.ToUpper() != "OP")
			{
				return true;
			}

			//If m_goTR.StrRead(SectionsToDisable, "EnforceNextActionDate") = "" Then
			//    If (m_clListFields.GetField("MLS_STATUS") = "Open" Or m_clListFields.GetField("MLS_STATUS") = "On Hold") And m_clListFields.GetField("DTE_NEXTACTIONDATE") = "" Then
			//        m_goErr.SetWarning(47260, "ProcessOP_Pre", "", "DTE_NextActionDate", "", "", "", "", "", "", "", "", "DTE_NextActionDate")
			//        Return False
			//    End If
			//End If

			//If editing an OP, capture value of Journal being added. This will be used in Post Save script to create the linked Journal AC
			if (m_goTR.StrRead(SectionsToDisable, "CreateJournalActivity") == "")
			{
				if (m_clListFields.GetField("MMO_JOURNAL") != "") //MMO_JOUrnal only passed when adding a journal entry
				{
					string sRecordID = Convert.ToString(m_RS.GetFieldVal("GID_ID"));
					string sOriginalJournal = Convert.ToString(m_RS.GetFieldVal("MMO_JOURNAL"));
					string sJournalAdded = m_clListFields.GetField("MMO_JOURNAL");
					sJournalAdded = m_goTR.Replace(sJournalAdded, sOriginalJournal, "");
					m_goP.SetVar("NewJournal", sJournalAdded);
				}
			}


			return true;
		}

		public bool ProcessOP_Post(string SectionsToDisable)
		{

			if (m_clData.filename.ToUpper() != "OP")
			{
				return true;
			}

			if (m_goTR.StrRead(SectionsToDisable, "CreateJournalActivity") == "")
			{
				//Only in edit mode
				if (m_clData.IsNew == false)
				{
					//Check if have WOP enabled to create journal activity when journal field is updated.
					if (m_goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "ONSAVEOPP_CREATE_AC", "0") == "1")
					{
						//Check if there is a value in MMO_JOURNAl; when adding a journal to a record we only have MMO_JOURNAL field in m_clListFields
						if (Convert.ToString(m_goP.GetVar("NewJournal")) != "")
						{
							string sRecordID = Convert.ToString(m_RS.GetFieldVal("GID_ID"));
							CreateJournalAC(sRecordID, Convert.ToString(m_goP.GetVar("NewJournal")));
							m_goP.SetVar("NewJournal", "");
						}
					}
				}
			}

			return true;
		}

	}

	[Serializable()]
	[DataContract()]
	public class clServiceOutput
	{
		[DataMember()]
		public string Status;
		[DataMember()]
		public string Data;
		[DataMember()]
		public string TurnAroundTime;
		[DataMember()]
		public byte[] FileData;
	}
	[Serializable()]
	[DataContract()]
	public class clversion
	{
		[DataMember()]
		public string VersionCode;
	}

	[Serializable()]
	[DataContract()]
	public class clServiceOutputForLogin
	{
		[DataMember()]
		public string UserId;
		[DataMember()]
		public string Name;
		[DataMember()]
		public string Code;
		[DataMember()]
		public string UserName;
		[DataMember()]
		public string Curr_MD_Version;
		[DataMember()]
		public string UserFCMTokenID;
		[DataMember()]
		public string FCMTokenDeviceName;
		[DataMember()]
		public bool EnabledTwoFactorAuth;
		[DataMember()]
		public string TwoFactorAuthMessage;
		[DataMember()]
		public string AutoCRLACSave;
		[DataMember()]
		public string Status;
		[DataMember()]
		public string Data;
		[DataMember()]
		public string AppVersion;
		[DataMember()]
		public string CustomerLogo;
		[DataMember()]
		public bool IsUserAdmin;

	}

	[Serializable()]
	[DataContract()]
	public class clSiteSettings
	{
		[DataMember()]
		public string EnableSSO;
		[DataMember()]
		public string ClientId;
		[DataMember()]
		public string TenantId;
	}

	[Serializable()]
	[DataContract]
	public class clFields
	{
		[DataMember()]
		public string FieldName;
		[DataMember()]
		public string FieldValue;
	}

	[Serializable()]
	[DataContract]
	public class MobileProfilePage
	{
		[DataMember()]
		public string PageId {get; set;}
		[DataMember()]
		public string Title {get; set;}
		[DataMember()]
		public int ViewCount {get; set;}
		[DataMember()]
		public List<MobileProfileView> ProfileViews {get; set;}
		[DataMember()]
		public string Status {get; set;}
		[DataMember()]
		public string ErrorMessage {get; set;}
	}
	[Serializable()]
	[DataContract]
	public class MobileProfileView
	{
		[DataMember()]
		public string ViewTitle {get; set;}
		[DataMember()]
		public string ViewType {get; set;}
		[DataMember()]
		public string ViewId {get; set;}
		[DataMember()]
		public string BgColor {get; set;}
		[DataMember()]
		public string Icon {get; set;}
		[DataMember()]
		public string NavigateToPageId {get; set;}
		[DataMember()]
		public int AddNewLink {get; set;}
		[DataMember()]
		public string GroupName {get; set;}
		[DataMember()]
		public string GroupValue {get; set;}
		[DataMember()]
		public string FileName {get; set;}
		[DataMember()]
		public string Condition {get; set;}
		[DataMember()]
		public string Sort {get; set;}
		[DataMember()]
		public List<RowData> RowsData {get; set;}
	}

	[Serializable()]
	[DataContract]
	public class RowData
	{
		[DataMember()]
		public string Row1Data {get; set;}
		[DataMember()]
		public string Row2Data {get; set;}
		[DataMember()]
		public string GidId {get; set;}
	}

	[Serializable()]
	[DataContract]
	public class CustomOutput
	{
		[DataMember()]
		public string RequiredFields {get; set;}
		[DataMember()]
		public string ShowFields {get; set;}
		[DataMember()]
		public int HideFields {get; set;}
	}

	public class ListFields
	{

		private List<clFields> lstData;

		public ListFields(List<clFields> data)
		{
			lstData = data;
		}


		public bool FieldExists(string fieldName)
		{

			foreach (var item in lstData)
			{
				if (item.FieldName.ToUpper() == fieldName.ToUpper())
				{
					return true;
				}
			}

			return false;
		}

		public string GetField(string fieldName)
		{

			foreach (var item in lstData)
			{
				if (item.FieldName.ToUpper() == fieldName.ToUpper())
				{
					return item.FieldValue;
				}
			}

			return null;
		}

		public bool CopyField(string toField, string fromField, string defaultValue)
		{

			string toValue = null;

			if (string.IsNullOrEmpty(GetField(toField)))
			{
				// not exists
				string fromValue = GetField(fromField);

				if (string.IsNullOrEmpty(fromValue))
				{
					toValue = defaultValue;
				}
				else
				{
					toValue = fromValue;
				}

				AddField(toField, toValue, true);
			}

			return true;
		}
		public bool AddField(string fieldName, string fieldValue, bool bUpdateIfExists)
		{

			if (FieldExists(fieldName))
			{
				if (bUpdateIfExists == true)
				{
					SetField(fieldName, fieldValue);
					return true;
				}
				return false;
			}

			clFields c1 = new clFields();
			c1.FieldName = fieldName;
			c1.FieldValue = fieldValue;
			lstData.Add(c1);

			return true;

		}

		public void SetField(string fieldName, string fieldValue)
		{

			foreach (var item in lstData)
			{
				if (item.FieldName.ToUpper() == fieldName.ToUpper())
				{
					item.FieldValue = fieldValue;
				}
			}

		}
	}

	[Serializable()]
	[DataContract()]
	public class cljsondata
	{

		[DataMember()]
		public string username;
		[DataMember()]
		public string password;
		[DataMember()]
		public string filename;
		[DataMember()]
		public string filter;
		[DataMember()]
		public List<clFields> lstData = new List<clFields>();
		[DataMember()]
		public bool IsNew;
		[DataMember()]
		public string createType;
		[DataMember()]
		public string createFrom;
		[DataMember()]
		public string App_Version;
		[DataMember()]
		public string App_DeviceName;
		[DataMember()]
		public string MD_Version;
		//<DataMember()>
		//Public FileData() As Byte

	}

	public class UserMailSettings
	{
		public string UserName;
		public string Password;
		public string ServerName;
		public int PortNumber;
		public string SmtpServerName;
		public int SmtpPortnumber;
		public string SmtpUserName;
		public string SmtpPassword;
	}

	public class clFCMUpdate
	{

		[DataMember()]
		public string UserName;
		[DataMember()]
		public string Password;
		[DataMember()]
		public string FCMTokenId;
		[DataMember()]
		public string FCMTokenDevice;
		[DataMember()]
		public string App_Version;
		[DataMember()]
		public string App_DeviceName;
		[DataMember()]
		public string MD_Version;

	}

	[Serializable()]
	[DataContract()]
	public class WebmailFolders
	{
		[DataMember()]
		public string FolderName;
		[DataMember()]
		public IList<WebmailFolders> ChildFolder;
	}

	[Serializable()]
	[DataContract()]
	public class clMP
	{

		[DataMember()]
		public string GID_ID
		{
			get
			{
				return (string.IsNullOrEmpty(m_GID_ID) ? "" : m_GID_ID);
			}
			set
			{
				m_GID_ID = value;
			}
		}
		private string m_GID_ID;

		[DataMember()]
		public string TXT_VIEW_NAME
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_VIEW_NAME) ? "" : m_TXT_VIEW_NAME);
			}
			set
			{
				m_TXT_VIEW_NAME = value;
			}
		}
		private string m_TXT_VIEW_NAME;

		[DataMember()]
		public string TXT_PAGE_TYPE
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_PAGE_TYPE) ? "" : m_TXT_PAGE_TYPE);
			}
			set
			{
				m_TXT_PAGE_TYPE = value;
			}
		}
		private string m_TXT_PAGE_TYPE;

		[DataMember()]
		public string TXT_PAGE_TITLE
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_PAGE_TITLE) ? "" : m_TXT_PAGE_TITLE);
			}
			set
			{
				m_TXT_PAGE_TITLE = value;
			}
		}
		private string m_TXT_PAGE_TITLE;

		[DataMember()]
		public string TXT_KEY
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_KEY) ? "" : m_TXT_KEY);
			}
			set
			{
				m_TXT_KEY = value;
			}
		}
		private string m_TXT_KEY;

		[DataMember()]
		public string SI__NAVBAR_HOME
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__NAVBAR_HOME) ? "" : m_SI__NAVBAR_HOME);
			}
			set
			{
				m_SI__NAVBAR_HOME = value;
			}
		}
		private string m_SI__NAVBAR_HOME;

		[DataMember()]
		public string SI__NAVBAR_SCAN
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__NAVBAR_SCAN) ? "" : m_SI__NAVBAR_SCAN);
			}
			set
			{
				m_SI__NAVBAR_SCAN = value;
			}
		}
		private string m_SI__NAVBAR_SCAN;

		[DataMember()]
		public string SI__SAVE_CREATE_ANOTHER
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__SAVE_CREATE_ANOTHER) ? "" : m_SI__SAVE_CREATE_ANOTHER);
			}
			set
			{
				m_SI__SAVE_CREATE_ANOTHER = value;
			}
		}
		private string m_SI__SAVE_CREATE_ANOTHER;

		[DataMember()]
		public string SI__NAVBAR_JOURNAL
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__NAVBAR_JOURNAL) ? "" : m_SI__NAVBAR_JOURNAL);
			}
			set
			{
				m_SI__NAVBAR_JOURNAL = value;
			}
		}
		private string m_SI__NAVBAR_JOURNAL;

		[DataMember()]
		public string SI__NAVBAR_CREATELINKED
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__NAVBAR_CREATELINKED) ? "" : m_SI__NAVBAR_CREATELINKED);
			}
			set
			{
				m_SI__NAVBAR_CREATELINKED = value;
			}
		}
		private string m_SI__NAVBAR_CREATELINKED;

		[DataMember()]
		public string TXT_CREATELINKED_TYPE
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_CREATELINKED_TYPE) ? "" : m_TXT_CREATELINKED_TYPE);
			}
			set
			{
				m_TXT_CREATELINKED_TYPE = value;
			}
		}
		private string m_TXT_CREATELINKED_TYPE;

		[DataMember()]
		public string SI__NAVBAR_EDIT
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__NAVBAR_EDIT) ? "" : m_SI__NAVBAR_EDIT);
			}
			set
			{
				m_SI__NAVBAR_EDIT = value;
			}
		}
		private string m_SI__NAVBAR_EDIT;

		[DataMember()]
		public string TXT_EDIT_PAGE_ID
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_EDIT_PAGE_ID) ? "" : m_TXT_EDIT_PAGE_ID);
			}
			set
			{
				m_TXT_EDIT_PAGE_ID = value;
			}
		}
		private string m_TXT_EDIT_PAGE_ID;

		[DataMember()]
		public string SI__NAVBAR_MARKREVIEWED
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__NAVBAR_MARKREVIEWED) ? "" : m_SI__NAVBAR_MARKREVIEWED);
			}
			set
			{
				m_SI__NAVBAR_MARKREVIEWED = value;
			}
		}
		private string m_SI__NAVBAR_MARKREVIEWED;

		[DataMember()]
		public string DTT_CREATIONTIME
		{
			get
			{
				return (string.IsNullOrEmpty(m_DTT_CREATIONTIME) ? "" : m_DTT_CREATIONTIME);
			}
			set
			{
				m_DTT_CREATIONTIME = value;
			}
		}
		private string m_DTT_CREATIONTIME;

		[DataMember()]
		public string SYS_Name
		{
			get
			{
				return (string.IsNullOrEmpty(m_SYS_Name) ? "" : m_SYS_Name);
			}
			set
			{
				m_SYS_Name = value;
			}
		}
		private string m_SYS_Name;

		[DataMember()]
		public string SI__ShareState
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__ShareState) ? "" : m_SI__ShareState);
			}
			set
			{
				m_SI__ShareState = value;
			}
		}
		private string m_SI__ShareState;

		[DataMember()]
		public string GID_CreatedBy_US
		{
			get
			{
				return (string.IsNullOrEmpty(m_GID_CreatedBy_US) ? "" : m_GID_CreatedBy_US);
			}
			set
			{
				m_GID_CreatedBy_US = value;
			}
		}
		private string m_GID_CreatedBy_US;

		[DataMember()]
		public string TXT_DETAILS_PAGE_ID
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_DETAILS_PAGE_ID) ? "" : m_TXT_DETAILS_PAGE_ID);
			}
			set
			{
				m_TXT_DETAILS_PAGE_ID = value;
			}
		}
		private string m_TXT_DETAILS_PAGE_ID;

		[DataMember()]
		public string SI__NAVBAR_ADD
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__NAVBAR_ADD) ? "" : m_SI__NAVBAR_ADD);
			}
			set
			{
				m_SI__NAVBAR_ADD = value;
			}
		}
		private string m_SI__NAVBAR_ADD;

		[DataMember()]
		public string TXT_ADD_TYPE
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_ADD_TYPE) ? "" : m_TXT_ADD_TYPE);
			}
			set
			{
				m_TXT_ADD_TYPE = value;
			}
		}
		private string m_TXT_ADD_TYPE;

		[DataMember()]
		public string TXT_SCANFIELD
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_SCANFIELD) ? "" : m_TXT_SCANFIELD);
			}
			set
			{
				m_TXT_SCANFIELD = value;
			}
		}
		private string m_TXT_SCANFIELD;

		[DataMember()]
		public string TXT_ICON_NAME
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_ICON_NAME) ? "" : m_TXT_ICON_NAME);
			}
			set
			{
				m_TXT_ICON_NAME = value;
			}
		}
		private string m_TXT_ICON_NAME;

		[DataMember()]
		public string SI__PAGE_SIZE
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__PAGE_SIZE) ? "" : m_SI__PAGE_SIZE);
			}
			set
			{
				m_SI__PAGE_SIZE = value;
			}
		}
		private string m_SI__PAGE_SIZE;

		[DataMember()]
		public string SI__LINKS_TOP_COUNT
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__LINKS_TOP_COUNT) ? "" : m_SI__LINKS_TOP_COUNT);
			}
			set
			{
				m_SI__LINKS_TOP_COUNT = value;
			}
		}
		private string m_SI__LINKS_TOP_COUNT;

		[DataMember()]
		public string TXT_SWIPE1FIELD
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_SWIPE1FIELD) ? "" : m_TXT_SWIPE1FIELD);
			}
			set
			{
				m_TXT_SWIPE1FIELD = value;
			}
		}
		private string m_TXT_SWIPE1FIELD;

		[DataMember()]
		public string TXT_SWIPE2FIELD
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_SWIPE2FIELD) ? "" : m_TXT_SWIPE2FIELD);
			}
			set
			{
				m_TXT_SWIPE2FIELD = value;
			}
		}
		private string m_TXT_SWIPE2FIELD;

		[DataMember()]
		public string TXT_SWIPE1EDITPAGEID
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_SWIPE1EDITPAGEID) ? "" : m_TXT_SWIPE1EDITPAGEID);
			}
			set
			{
				m_TXT_SWIPE1EDITPAGEID = value;
			}
		}
		private string m_TXT_SWIPE1EDITPAGEID;

		[DataMember()]
		public string TXT_SWIPE2EDITPAGEID
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_SWIPE2EDITPAGEID) ? "" : m_TXT_SWIPE2EDITPAGEID);
			}
			set
			{
				m_TXT_SWIPE2EDITPAGEID = value;
			}
		}
		private string m_TXT_SWIPE2EDITPAGEID;

		[DataMember()]
		public string SI__SEARCH
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__SEARCH) ? "" : m_SI__SEARCH);
			}
			set
			{
				m_SI__SEARCH = value;
			}
		}
		private string m_SI__SEARCH;

		[DataMember()]
		public string TXT_SEARCH
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_SEARCH) ? "" : m_TXT_SEARCH);
			}
			set
			{
				m_TXT_SEARCH = value;
			}
		}
		private string m_TXT_SEARCH;

		[DataMember()]
		public string SI__ROLLODEX
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__ROLLODEX) ? "" : m_SI__ROLLODEX);
			}
			set
			{
				m_SI__ROLLODEX = value;
			}
		}
		private string m_SI__ROLLODEX;

		[DataMember()]
		public string TXT_ROLLODEX
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_ROLLODEX) ? "" : m_TXT_ROLLODEX);
			}
			set
			{
				m_TXT_ROLLODEX = value;
			}
		}
		private string m_TXT_ROLLODEX;

		[DataMember()]
		public string TXT_SWIPE3FIELD
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_SWIPE3FIELD) ? "" : m_TXT_SWIPE3FIELD);
			}
			set
			{
				m_TXT_SWIPE3FIELD = value;
			}
		}
		private string m_TXT_SWIPE3FIELD;

		[DataMember()]
		public string TXT_SWIPE3EDITPAGEID
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_SWIPE3EDITPAGEID) ? "" : m_TXT_SWIPE3EDITPAGEID);
			}
			set
			{
				m_TXT_SWIPE3EDITPAGEID = value;
			}
		}
		private string m_TXT_SWIPE3EDITPAGEID;

		[DataMember()]
		public string TXT_SWIPE1CLPAGEIDS
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_SWIPE1CLPAGEIDS) ? "" : m_TXT_SWIPE1CLPAGEIDS);
			}
			set
			{
				m_TXT_SWIPE1CLPAGEIDS = value;
			}
		}
		private string m_TXT_SWIPE1CLPAGEIDS;

		[DataMember()]
		public string TXT_SWIPE2CLPAGEIDS
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_SWIPE2CLPAGEIDS) ? "" : m_TXT_SWIPE2CLPAGEIDS);
			}
			set
			{
				m_TXT_SWIPE2CLPAGEIDS = value;
			}
		}
		private string m_TXT_SWIPE2CLPAGEIDS;

		[DataMember()]
		public string TXT_SWIPE3CLPAGEIDS
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_SWIPE3CLPAGEIDS) ? "" : m_TXT_SWIPE3CLPAGEIDS);
			}
			set
			{
				m_TXT_SWIPE3CLPAGEIDS = value;
			}
		}
		private string m_TXT_SWIPE3CLPAGEIDS;

		[DataMember()]
		public string SI__CACHEDATA
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__CACHEDATA) ? "" : m_SI__CACHEDATA);
			}
			set
			{
				m_SI__CACHEDATA = value;
			}
		}
		private string m_SI__CACHEDATA;
		[DataMember()]
		public string SI__SHOWASBTNINHOMEPAGE
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__SHOWASBTNINHOMEPAGE) ? "" : m_SI__SHOWASBTNINHOMEPAGE);
			}
			set
			{
				m_SI__SHOWASBTNINHOMEPAGE = value;
			}
		}
		private string m_SI__SHOWASBTNINHOMEPAGE;

		[DataMember()]
		public string SI__NEARME
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__NEARME) ? "" : m_SI__NEARME);
			}
			set
			{
				m_SI__NEARME = value;
			}
		}
		private string m_SI__NEARME;

		[DataMember()]
		public string TXT_NEARME_PAGEID
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_NEARME_PAGEID) ? "" : m_TXT_NEARME_PAGEID);
			}
			set
			{
				m_TXT_NEARME_PAGEID = value;
			}
		}
		private string m_TXT_NEARME_PAGEID;

		[DataMember()]
		public string TXT_MAINTITLE
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_MAINTITLE) ? "" : m_TXT_MAINTITLE);
			}
			set
			{
				m_TXT_MAINTITLE = value;
			}
		}
		private string m_TXT_MAINTITLE;

	}

	[Serializable()]
	[DataContract()]
	public class clMF
	{

		[DataMember()]
		public string BI__ID
		{
			get
			{
				return (string.IsNullOrEmpty(m_BI__ID) ? "" : m_BI__ID);
			}
			set
			{
				m_BI__ID = value;
			}
		}
		private string m_BI__ID;


		[DataMember()]
		public string GID_ID
		{
			get
			{
				return (string.IsNullOrEmpty(m_GID_ID) ? "" : m_GID_ID);
			}
			set
			{
				m_GID_ID = value;
			}
		}
		private string m_GID_ID;


		[DataMember()]
		public string TXT_MP_GID_ID
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_MP_GID_ID) ? "" : m_TXT_MP_GID_ID);
			}
			set
			{
				m_TXT_MP_GID_ID = value;
			}
		}
		private string m_TXT_MP_GID_ID;


		[DataMember()]
		public string TXT_CATEGORY
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_CATEGORY) ? "" : m_TXT_CATEGORY);
			}
			set
			{
				m_TXT_CATEGORY = value;
			}
		}
		private string m_TXT_CATEGORY;


		[DataMember()]
		public string TXT_FIELDTYPE
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_FIELDTYPE) ? "" : m_TXT_FIELDTYPE);
			}
			set
			{
				m_TXT_FIELDTYPE = value;
			}
		}
		private string m_TXT_FIELDTYPE;


		[DataMember()]
		public string TXT_FIELDNAMES
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_FIELDNAMES) ? "" : m_TXT_FIELDNAMES);
			}
			set
			{
				m_TXT_FIELDNAMES = value;
			}
		}
		private string m_TXT_FIELDNAMES;


		[DataMember()]
		public string TXT_LABELNAME
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_LABELNAME) ? "" : m_TXT_LABELNAME);
			}
			set
			{
				m_TXT_LABELNAME = value;
			}
		}
		private string m_TXT_LABELNAME;


		[DataMember()]
		public string TXT_GROUP_NAME
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_GROUP_NAME) ? "" : m_TXT_GROUP_NAME);
			}
			set
			{
				m_TXT_GROUP_NAME = value;
			}
		}
		private string m_TXT_GROUP_NAME;


		[DataMember()]
		public string TXT_TITLE
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_TITLE) ? "" : m_TXT_TITLE);
			}
			set
			{
				m_TXT_TITLE = value;
			}
		}
		private string m_TXT_TITLE;


		[DataMember()]
		public string TXT_WIDGETTYPE
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_WIDGETTYPE) ? "" : m_TXT_WIDGETTYPE);
			}
			set
			{
				m_TXT_WIDGETTYPE = value;
			}
		}
		private string m_TXT_WIDGETTYPE;


		[DataMember()]
		public string TXT_WIDGETVALUE
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_WIDGETVALUE) ? "" : m_TXT_WIDGETVALUE);
			}
			set
			{
				m_TXT_WIDGETVALUE = value;
			}
		}
		private string m_TXT_WIDGETVALUE;


		[DataMember()]
		public string TXT_FIELDVALUE
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_FIELDVALUE) ? "" : m_TXT_FIELDVALUE);
			}
			set
			{
				m_TXT_FIELDVALUE = value;
			}
		}
		private string m_TXT_FIELDVALUE;


		[DataMember()]
		public string SI__IS_REQUIRED
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__IS_REQUIRED) ? "" : m_SI__IS_REQUIRED);
			}
			set
			{
				m_SI__IS_REQUIRED = value;
			}
		}
		private string m_SI__IS_REQUIRED;


		[DataMember()]
		public string TXT_REQUIRED_MESSAGE
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_REQUIRED_MESSAGE) ? "" : m_TXT_REQUIRED_MESSAGE);
			}
			set
			{
				m_TXT_REQUIRED_MESSAGE = value;
			}
		}
		private string m_TXT_REQUIRED_MESSAGE;


		[DataMember()]
		public string SR__SORTORDER
		{
			get
			{
				return (string.IsNullOrEmpty(m_SR__SORTORDER) ? "" : m_SR__SORTORDER);
			}
			set
			{
				m_SR__SORTORDER = value;
			}
		}
		private string m_SR__SORTORDER;


		[DataMember()]
		public string SYS_Name
		{
			get
			{
				return (string.IsNullOrEmpty(m_SYS_Name) ? "" : m_SYS_Name);
			}
			set
			{
				m_SYS_Name = value;
			}
		}
		private string m_SYS_Name;


		[DataMember()]
		public string SI__ShareState
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__ShareState) ? "" : m_SI__ShareState);
			}
			set
			{
				m_SI__ShareState = value;
			}
		}
		private string m_SI__ShareState;


		[DataMember()]
		public string GID_CreatedBy_US
		{
			get
			{
				return (string.IsNullOrEmpty(m_GID_CreatedBy_US) ? "" : m_GID_CreatedBy_US);
			}
			set
			{
				m_GID_CreatedBy_US = value;
			}
		}
		private string m_GID_CreatedBy_US;


		[DataMember()]
		public string DTT_CreationTime
		{
			get
			{
				return (string.IsNullOrEmpty(m_DTT_CreationTime) ? "" : m_DTT_CreationTime);
			}
			set
			{
				m_DTT_CreationTime = value;
			}
		}
		private string m_DTT_CreationTime;


		[DataMember()]
		public string TXT_ICON_NAME
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_ICON_NAME) ? "" : m_TXT_ICON_NAME);
			}
			set
			{
				m_TXT_ICON_NAME = value;
			}
		}
		private string m_TXT_ICON_NAME;


		[DataMember()]
		public string TXT_FIELD_LOCATION
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_FIELD_LOCATION) ? "" : m_TXT_FIELD_LOCATION);
			}
			set
			{
				m_TXT_FIELD_LOCATION = value;
			}
		}
		private string m_TXT_FIELD_LOCATION;


		[DataMember()]
		public string SI__FILTER_DEFAULT
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__FILTER_DEFAULT) ? "" : m_SI__FILTER_DEFAULT);
			}
			set
			{
				m_SI__FILTER_DEFAULT = value;
			}
		}
		private string m_SI__FILTER_DEFAULT;


		[DataMember()]
		public string TXT_FILTER_SORT
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_FILTER_SORT) ? "" : m_TXT_FILTER_SORT);
			}
			set
			{
				m_TXT_FILTER_SORT = value;
			}
		}
		private string m_TXT_FILTER_SORT;

		[DataMember()]
		public string DT_DataValue
		{
			get
			{
				return (string.IsNullOrEmpty(m_DT_DataValue) ? "" : m_DT_DataValue);
			}
			set
			{
				m_DT_DataValue = value;
			}
		}
		private string m_DT_DataValue;

		[DataMember()]
		public string SI__FIELD_VISIBLE
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__FIELD_VISIBLE) ? "" : m_SI__FIELD_VISIBLE);
			}
			set
			{
				m_SI__FIELD_VISIBLE = value;
			}
		}
		private string m_SI__FIELD_VISIBLE;

		[DataMember()]
		public string INT_ROWNUMBER
		{
			get
			{
				return (string.IsNullOrEmpty(m_INT_ROWNUMBER) ? "" : m_INT_ROWNUMBER);
			}
			set
			{
				m_INT_ROWNUMBER = value;
			}
		}
		private string m_INT_ROWNUMBER;

		[DataMember()]
		public string TXT_CRL_TYPE
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_CRL_TYPE) ? "" : m_TXT_CRL_TYPE);
			}
			set
			{
				m_TXT_CRL_TYPE = value;
			}
		}
		private string m_TXT_CRL_TYPE;

		[DataMember()]
		public string SI__CRL_VISIBLE
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__CRL_VISIBLE) ? "" : m_SI__CRL_VISIBLE);
			}
			set
			{
				m_SI__CRL_VISIBLE = value;
			}
		}
		private string m_SI__CRL_VISIBLE;

		[DataMember()]
		public string TXT_CRL_PAGEID
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_CRL_PAGEID) ? "" : m_TXT_CRL_PAGEID);
			}
			set
			{
				m_TXT_CRL_PAGEID = value;
			}
		}
		private string m_TXT_CRL_PAGEID;

		[DataMember()]
		public string TXT_CRL
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_CRL) ? "" : m_TXT_CRL);
			}
			set
			{
				m_TXT_CRL = value;
			}
		}
		private string m_TXT_CRL;

		[DataMember()]
		public string TXT_CRL_DEFAULT
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_CRL_DEFAULT) ? "" : m_TXT_CRL_DEFAULT);
			}
			set
			{
				m_TXT_CRL_DEFAULT = value;
			}
		}
		private string m_TXT_CRL_DEFAULT;

		[DataMember()]
		public string SR__SORTORDERNEW
		{
			get
			{
				return (string.IsNullOrEmpty(m_SR__SORTORDERNEW) ? "" : m_SR__SORTORDERNEW);
			}
			set
			{
				m_SR__SORTORDERNEW = value;
			}
		}
		private string m_SR__SORTORDERNEW;

		[DataMember()]
		public string TXT_MAP_LINKBOX
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_MAP_LINKBOX) ? "" : m_TXT_MAP_LINKBOX);
			}
			set
			{
				m_TXT_MAP_LINKBOX = value;
			}
		}
		private string m_TXT_MAP_LINKBOX;

		[DataMember()]
		public string SI__SHOWLABEL
		{
			get
			{
				return (string.IsNullOrEmpty(m_SI__SHOWLABEL) ? "" : m_SI__SHOWLABEL);
			}
			set
			{
				m_SI__SHOWLABEL = value;
			}
		}
		private string m_SI__SHOWLABEL;

		[DataMember()]
		public string TXT_SEARCHFILTERKEY
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_SEARCHFILTERKEY) ? "" : m_TXT_SEARCHFILTERKEY);
			}
			set
			{
				m_TXT_SEARCHFILTERKEY = value;
			}
		}
		private string m_TXT_SEARCHFILTERKEY;

		[DataMember()]
		public string TXT_SEARCHHINTTEXT
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_SEARCHHINTTEXT) ? "" : m_TXT_SEARCHHINTTEXT);
			}
			set
			{
				m_TXT_SEARCHHINTTEXT = value;
			}
		}
		private string m_TXT_SEARCHHINTTEXT;

		[DataMember()]
		public string TXT_LNKSEARCHFILTERKEY
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_LNKSEARCHFILTERKEY) ? "" : m_TXT_LNKSEARCHFILTERKEY);
			}
			set
			{
				m_TXT_LNKSEARCHFILTERKEY = value;
			}
		}
		private string m_TXT_LNKSEARCHFILTERKEY;

		[DataMember()]
		public string TXT_LNKSEARCHHINTTEXT
		{
			get
			{
				return (string.IsNullOrEmpty(m_TXT_LNKSEARCHHINTTEXT) ? "" : m_TXT_LNKSEARCHHINTTEXT);
			}
			set
			{
				m_TXT_LNKSEARCHHINTTEXT = value;
			}
		}
		private string m_TXT_LNKSEARCHHINTTEXT;

	}

	[Serializable()]
	[DataContract()]
	public class clStatus
	{

		[DataMember()]
		public string status;

		[DataMember()]
		public string Errormessage;

	}

	[Serializable()]
	[DataContract()]
	public class clOutput
	{

		[DataMember()]
		public List<clMP> lstMP;

		[DataMember()]
		public List<clMF> lstMF;

		[DataMember()]
		public string Data;

		[DataMember()]
		public List<Dictionary<string, string>> Datarows = new List<Dictionary<string, string>>();

		[DataMember()]
		public clStatus objStatus = new clStatus();

		[DataMember()]
		public string CurrentMDVersion;

		[DataMember()]
		public MobileProfilePage ProfilePage = new MobileProfilePage();

		[DataMember()]
		public CustomOutput CusOutput = new CustomOutput();

	}

}
