﻿CREATE TABLE [dbo].[NP] (
    [GID_ID]                 UNIQUEIDENTIFIER CONSTRAINT [DF_NP_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'NP',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                 BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]               NVARCHAR (80)    NULL,
    [DTT_CreationTime]       DATETIME         CONSTRAINT [DF_NP_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]           TINYINT          NULL,
    [TXT_ModBy]              VARCHAR (4)      NULL,
    [DTT_ModTime]            DATETIME         CONSTRAINT [DF_NP_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_NinjaProgramName]   NVARCHAR (50)    NULL,
    [MMO_ImportData]         NTEXT            NULL,
    [SI__ShareState]         TINYINT          CONSTRAINT [DF_NP_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]       UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]           VARCHAR (50)     NULL,
    [TXT_ExternalID]         NVARCHAR (80)    NULL,
    [TXT_ExternalSource]     VARCHAR (10)     NULL,
    [TXT_ImpJobID]           VARCHAR (20)     NULL,
    [TXT_PROGRAMID]          NVARCHAR (20)    NULL,
    [TXT_PROGRAMNAME]        NVARCHAR (50)    NULL,
    [CHK_ACTIVE]             TINYINT          NULL,
    [MLS_ProgramType]        SMALLINT         NULL,
    [MMO_ProgramDescription] NTEXT            NULL,
    [CUR_VALUE]              MONEY            NULL,
    [DTT_STARTDATE]          DATETIME         NULL,
    [DTT_ENDDATE]            DATETIME         NULL,
    CONSTRAINT [PK_NP] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_NP_NinjaProgramName]
    ON [dbo].[NP]([TXT_NinjaProgramName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NP_CreatedBy_US]
    ON [dbo].[NP]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NP_ModDateTime]
    ON [dbo].[NP]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NP_Name]
    ON [dbo].[NP]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NP_CreationTime]
    ON [dbo].[NP]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_NP_BI__ID]
    ON [dbo].[NP]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NP_TXT_ImportID]
    ON [dbo].[NP]([TXT_ImportID] ASC);

