Imports Microsoft.VisualBasic
Imports System.Web

Public Class clSend

    'OWNER: RH as of 2/8/2012
    'MI 10/29/10 Added support for Above Sig, Under Signature.

    Dim goP As clProject
    Dim goTR As clTransform
    Dim goMeta As clMetaData
    Dim goData As clData
    Dim goErr As clError
    Dim goLog As clLog
    Dim goDef As clDefaults
    Dim goScr As clScrMngRowSet
    Dim INI As SDI


    Public Structure MailObject

        Dim sTo As String
        Dim sCC As String
        Dim sBCC As String
        Dim sSubject As String
        Dim sBody As String
        Dim sFiles As String
        Dim sAboveSig As String
        Dim sUnderSig As String

    End Structure
    Public Structure SDI
        Public Address As String
        Public BroadcastCount As Integer
        Public BroadcastLink As String
        Public BroadcastIDs As String
        Public DB As String
        Public File As String
        Public ID As String
        Public Individualized As Boolean
        Public InstanceCode As String
        Public IsSingle As Boolean
        Public Mode As String
        Public ModeExt As String
        Public Password As String
        Public Preview As Boolean
        Public PrintLetterHead As Boolean
        Public TempDir As String
        Public Template As String
        Public TemplType As String
        Public User As String
        Public SendLink As String
        Public ObjectType As String
        Public Share As String
        Public ViewName As String
        Public DesktopName As String
        Public SubjectField As String
        Public QuoteNumber As String

    End Structure

    '*******************************************************************************
    'INITIALIZE
    Public Sub New()
        Initialize()
    End Sub
    Sub Initialize()

        goP = HttpContext.Current.Session("goP")
        goTR = HttpContext.Current.Session("goTr")
        goMeta = HttpContext.Current.Session("goMeta")
        goData = HttpContext.Current.Session("goData")
        goErr = HttpContext.Current.Session("goErr")
        goLog = HttpContext.Current.Session("goLog")
        goDef = HttpContext.Current.Session("goDef")
        goScr = HttpContext.Current.Session("goScr")

    End Sub
    '*******************************************************************************
    'SMTP EMail
    Public Function SMTPTrigger(ByVal par_sINI As String) As Boolean
        'PURPOSE:
        '       Send a correspondence email via our SMTP server. This method reads
        '       data from an AC or QT record. To send a generic e-mail via the server,
        '       use clEmail.SendSMTPEmail or SendSMTPEmailHTMLWithEmbeddedImages.

        'MI 2/1/12 Added note on purpose above.
        'MI 5/14/09 Changed SYS_Name to TXT_NameFirst & " " & TXT_NameLast
        'MI 5/13/09 SMTPTrigger: added redirecting to Mobile_diaSendPreview.aspx if product is Mobile.

        Dim sProc As String = "clSend::SMTPTrigger"
        Dim sReplyName As String
        Dim sReplyAddress As String
        Dim sBody As String

        'Try

        Dim oMailItem As New MailObject
            Dim sMIID As String = goTR.DateToInteger(goTR.NowUTC()).ToString    '*** MI 10/1/07


            If goTR.StrRead(par_sINI, "OBJECTTOSEND") = "CORR" Then
                'Correspondence sending

                Dim oCorrRS As New clRowSet(goTR.StrRead(par_sINI, "FILE"), clC.SELL_EDIT, "GID_ID='" & goTR.StrRead(par_sINI, "ID") & "'")
                If oCorrRS.Count() < 1 Then
                    goErr.SetWarning(35000, "The record doesn't exist and can't be e-mailed via SMTP server. It may have been deleted by another user. If the problem persists, please contact your Selltis administrator. GID_ID: '" & goTR.StrRead(par_sINI, "ID") & "', file: '" & goTR.StrRead(par_sINI, "FILE") & "'.")
                    Return False
                End If

                If goTR.StrRead(par_sINI, "FILE") = "AC" Then
                    oMailItem.sSubject = oCorrRS.GetFieldVal("TXT_SUBJ", clC.SELL_FRIENDLY)
                    oMailItem.sBody = oCorrRS.GetFieldVal("MMO_LETTER", clC.SELL_FRIENDLY)
                    oMailItem.sAboveSig = oCorrRS.GetFieldVal("TXT_ABOVESIGNATURE", clC.SELL_FRIENDLY)
                    oMailItem.sUnderSig = oCorrRS.GetFieldVal("MMO_UNDERSIGNATURE", clC.SELL_FRIENDLY)
                ElseIf goTR.StrRead(par_sINI, "FILE") = "QT" Then
                    oMailItem.sSubject = oCorrRS.GetFieldVal("TXT_DESCRIPTION", clC.SELL_FRIENDLY)
                    oMailItem.sBody = oCorrRS.GetFieldVal("MMO_COVERLETTER", clC.SELL_FRIENDLY)
                End If

                oMailItem.sTo = GetTO(oCorrRS)
                oMailItem.sCC = GetCC(oCorrRS)
                oMailItem.sBCC = GetBCC(oCorrRS)
                oMailItem.sFiles = "(none)"

            Else
                'Record details sending, view sending?

            End If

            If CBool(goTR.StrRead(par_sINI, "PREVIEW")) = True Then
                goP.SetVar(sMIID, oMailItem)
                goP.SetVar(sMIID & "_sSendINI", par_sINI)
                'Open the preview page
                If goP.GetProduct() = "MB" Then
                    HttpContext.Current.Response.Redirect("Mobile_DiaEmailPreview.aspx?" & sMIID)
                Else
                    'SA
                    HttpContext.Current.Response.Redirect("diaEmailPreview.aspx?" & sMIID)
                End If

            Else

                'Send
                Dim oEMail As New clEmail
                'MI 5/14/09 Changed SYS_Name to TXT_NameLast,TXT_NameFirst
                Dim rs As New clRowSet("US", 3, "GID_ID='" & goP.GetUserTID & "'", , "TXT_NameLast,TXT_NameFirst,EML_email")
                If rs.Count() < 1 Then
                    'Record may have been deleted - do not raise error so the calling page can display the following 'friendly' error
                    goErr.SetWarning(35000, sProc, "Sending via SMTP failed because current user's User record can't be found. GID_ID: '" & goP.GetUserTID & "'.")
                    Return False
                End If
                'MI 5/14/09 Changed SYS_Name to TXT_NameFirst & " " & TXT_NameLast
                sReplyName = rs.GetFieldVal("TXT_NameFirst") & " " & rs.GetFieldVal("TXT_NameLast")
                sReplyAddress = rs.GetFieldVal("EML_EMail")

                'MI 10/29/10 Added support for Above/Under Sig
                sBody = oMailItem.sBody
                If Not oMailItem.sAboveSig.ToString = "" Then
                    sBody &= vbCrLf & vbCrLf & oMailItem.sAboveSig.ToString
                End If
                If Not oMailItem.sUnderSig.ToString = "" Then
                    sBody &= vbCrLf & vbCrLf & oMailItem.sUnderSig.ToString
                End If

                If oEMail.SendSMTPEmailNew(oMailItem.sSubject, _
                                         sBody, _
                                         oMailItem.sTo, _
                                         oMailItem.sCC, _
                                         oMailItem.sBCC, _
                                         "", _
                                         sReplyName, _
                                         sReplyAddress, _
                                         "") Then
                    'Mark the record as sent
                    MarkRecordAsSent(goTR.StrRead(par_sINI, "ID", , False), goTR.StrRead(par_sINI, "OBJECTTOSEND", , False))
                    Return True

                Else
                    'Warning is set by SendSMTPEmail
                    Return False

                End If

            End If

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

    End Function
    Function GetTO(ByRef oRS As clRowSet) As String

        Dim sTempStr As String = ""

        If oRS.GetFileName = "AC" Then
            If oRS.GetFieldVal("EML_EMAIL", clC.SELL_FRIENDLY) = "" Then
                sTempStr = oRS.GetFieldVal("LNK_RELATED_CN%%EML_EMAIL", clC.SELL_FRIENDLY)
            Else
                sTempStr = oRS.GetFieldVal("EML_EMAIL", clC.SELL_FRIENDLY)
            End If

        ElseIf oRS.GetFileName = "QT" Then
            If oRS.GetFieldVal("EML_EMAIL", clC.SELL_FRIENDLY) = "" Then
                sTempStr = oRS.GetFieldVal("LNK_TO_CN%%EML_EMAIL", clC.SELL_FRIENDLY)
            Else
                sTempStr = oRS.GetFieldVal("EML_EMAIL", clC.SELL_FRIENDLY)
            End If

        End If

        sTempStr = goTR.Replace(sTempStr, vbCrLf, "; ")

        Return sTempStr

    End Function
    Function GetCC(ByRef oRS As clRowSet) As String
        Dim sproc As String = "clSend::GetCC"

        Dim sTempStr As String = ""

        sTempStr = oRS.GetFieldVal("LNK_CC_CN%%EML_EMAIL", clC.SELL_FRIENDLY)
        sTempStr = goTR.Replace(sTempStr, vbCrLf, "; ")

        Return sTempStr

    End Function
    Function GetBCC(ByRef oRS As clRowSet) As String
        Dim sproc As String = "clSend::GetBCC"

        Dim sTempStr As String = ""

        sTempStr = oRS.GetFieldVal("LNK_BC_CN%%EML_EMAIL", clC.SELL_FRIENDLY)
        sTempStr = goTR.Replace(sTempStr, vbCrLf, "; ")

        Return sTempStr

    End Function
    '*******************************************************************************
    'TEMPLATE PROCESSING
    Sub InitializeProcesses(ByVal sINI As String)

        Dim sTemplateName As String = ""

        '[Load INI data]
        INI.User = goTR.StrRead(sINI, "USER", "")
        INI.File = goTR.StrRead(sINI, "FILE", "")
        INI.Share = goTR.StrRead(sINI, "SHARE", "")
        INI.ViewName = goTR.StrRead(sINI, "ViewName", "Selltis View")
        INI.DesktopName = goTR.StrRead(sINI, "DesktopName", "Selltis Desktop")
        INI.TempDir = IO.Path.GetDirectoryName(Reflection.Assembly.GetExecutingAssembly().Location()) & "\Temp\"
        'xxx validate
        INI.ID = goTR.StrRead(sINI, "ID", "")
        'xxx validate
        INI.Template = goTR.StrRead(sINI, "TEMPLATEPATH", "")
        sTemplateName = INI.Template
        INI.Template = IO.Path.GetDirectoryName(Reflection.Assembly.GetExecutingAssembly().Location()) & "\SendTemp\" & INI.Template

        'xxx validate
        INI.Individualized = CBool(goTR.StrRead(sINI, "INDIVIDUALIZEDEMAIL", ""))
        INI.Preview = CBool(goTR.StrRead(sINI, "PREVIEW", ""))
        INI.Address = goTR.StrRead(sINI, "ADDRESS", "")
        INI.ObjectType = goTR.StrRead(sINI, "OBJECTTOSEND", "CORR")
        INI.TemplType = InitTemplType()
        INI.InstanceCode = GetTimeString()


        'xxx validate
        INI.Mode = goTR.StrRead(sINI, "MODE", "")
        INI.ModeExt = goTR.StrRead(sINI, "MODEEXE", "")


        If UCase(INI.File) = "AC" Then
            INI.BroadcastLink = "LNK_RELATED_CN"
            INI.SendLink = "LNK_RELATED_CN"
        ElseIf UCase(INI.File) = "QT" Then
            INI.BroadcastLink = "LNK_TO_CN"
            INI.SendLink = "LNK_TO_CN"
        End If


        'xxx add loading of ids from text files



    End Sub

    Function InitTemplType() As String

        If Len(INI.Template) < 4 Then INI.Template = "unk"


        Select Case LCase(Right(INI.Template, 3))
            Case "txt"

                InitTemplType = "Text"

                If INI.Individualized = True Then
                    INI.IsSingle = True
                Else
                    INI.IsSingle = False
                End If

            Case "htm", "tml"

                InitTemplType = "HTML"

                If INI.BroadcastCount > 1 Then
                    INI.IsSingle = False
                Else
                    INI.IsSingle = True
                End If

            Case "doc", "dot"

                InitTemplType = "Word"

                If INI.Mode = "EMAIL" Then
                    If INI.Individualized = True Then
                        INI.IsSingle = False
                    Else
                        INI.IsSingle = True
                    End If
                Else
                    If INI.BroadcastCount > 1 Then
                        INI.IsSingle = False
                    Else
                        INI.IsSingle = True
                    End If
                End If

            Case Else

                InitTemplType = "Unk"

                If INI.Individualized = True Then
                    INI.IsSingle = True
                Else
                    INI.IsSingle = False
                End If



        End Select



    End Function
    Public Function GetTimeString() As String

        Dim sTime As DateTime = goTR.NowUTC()   '*** MI 10/1/07
        'GetLocalTime(sTime)

        GetTimeString = sTime.Year & PadLeadingZeros(sTime.Month, 2) & PadLeadingZeros(sTime.Day, 2) & PadLeadingZeros(sTime.Hour, 2) & PadLeadingZeros(sTime.Minute, 2) & PadLeadingZeros(sTime.Second, 2) & Left(PadLeadingZeros(sTime.Millisecond, 3), 2)

    End Function

    Public Function PadLeadingZeros(ByVal strForm As String, ByVal intLen As Integer) As String

        If Len(strForm) < intLen Then
            Do While Len(strForm) <> intLen
                strForm = "0" & strForm
            Loop
            PadLeadingZeros = strForm
        Else
            PadLeadingZeros = strForm
        End If

    End Function


    '*******************************************************************************
    'PC LINK INTERFACE
    Public Function GetSendJobs() As DataTable

        Dim sproc As String = "clSend::GetSendJobs"

        Dim dtSend As New DataTable
        'Dim i As Integer
        Dim sSendPage As String = ""
        Dim sLog As String = ""

        Dim sqlConnection1 As SqlClient.SqlConnection = goData.GetConnection

        Dim cmd As New SqlClient.SqlCommand

        Dim reader As SqlClient.SqlDataReader
        Dim mColl As New Collection
        'Dim dtNow As DateTime = goTr.NowServer()    '*** MI 10/1/07
        'Dim bFired As Boolean

        'dtNow = dtNow.AddMilliseconds(-dtNow.Millisecond)    '*** MI 10/1/07
        'dtNow = dtNow.AddSeconds(-dtNow.Second)    '*** MI 10/1/07

        'Try

        cmd.CommandText = "pGetSendJobs"
            cmd.CommandType = CommandType.StoredProcedure
            cmd.Connection = sqlConnection1

            Dim uResult As New SqlClient.SqlParameter("@par_bDetails", SqlDbType.Bit)
            uResult.Value = 1
            cmd.Parameters.Add(uResult)

            'uResult = New SqlClient.SqlParameter("@par_sPageID", SqlDbType.NVarChar, 40)
            'uResult.Value = goP.GetUserTID
            'cmd.Parameters.Add(uResult)

            reader = cmd.ExecuteReader()

            If reader.HasRows Then
                dtSend.Load(reader)
            Else
                '==> raise error
            End If

            reader.Close()
            sqlConnection1.Close()

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sproc)
        '    End If
        'End Try

        'If dtSend.Rows.Count > 0 Then

        'sLog = "Server time: " & dtNow.ToString & "[cr]"
        'For i = 0 To dtAge.Rows.Count - 1
        '    sAgePage = goMeta.PageRead("GLOBAL", dtAge.Rows(i).Item("Page").ToString)

        '    If goTR.StrRead(sAgePage, "ACTIVE") = "1" Then
        '        If TestTimerTrigger(dtAge.Rows(i).Item("Page").ToString, sAgePage, dtNow) = True Then
        '            '==> Execute automator
        '            goMeta.LineWrite("GLOBAL", "PTA_" & goTR.FromTo(dtAge.Rows(i).Item("Page").ToString, 5), "LASTDATE", goTR.DateTimeToString(dtNow))
        '            '==> Compile and write log

        '            If goScr.RunScript(goTR.StrRead(sAgePage, "A_01_EXECUTE")) = False Then
        '                sLog = sLog & "[cr]" & goTR.StrRead(sAgePage, "US_NAME") & "  [False]"
        '            Else
        '                sLog = sLog & "[cr]" & goTR.StrRead(sAgePage, "US_NAME") & "  [True]"
        '            End If

        '            bFired = True

        '        End If
        '    End If
        'Next
        'If bFired = True Then
        '    Return sLog
        'Else
        '    Return "None triggered"
        'End If

        'End If

        Return dtSend



    End Function
    Public Function GetSendJobCount() As Integer







        Dim sproc As String = "clSend::GetSendJobCount"
        Dim sSendPage As String = ""
        Dim sLog As String = ""
        Dim iVal As Integer

        Dim sqlConnection1 As SqlClient.SqlConnection = goData.GetConnection

        Dim cmd As New SqlClient.SqlCommand
        Dim mColl As New Collection

        'Try

        cmd.CommandText = "pGetSendJobCount"
            cmd.CommandType = CommandType.StoredProcedure
            cmd.Connection = sqlConnection1

            iVal = CInt(cmd.ExecuteScalar())

            sqlConnection1.Close()
            Return iVal

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sproc)
        '    End If
        'End Try

        Return 0

    End Function

    Public Function GetSendJobCountVer(ByVal sVerLocal As String) As Integer

        Dim bOldPCLink As Boolean = False
        Try
            Dim ia1, ia2, ia3 As Integer

            ia1 = CInt(goTR.ExtractString(sVerLocal, 1, "."))
            ia2 = CInt(goTR.ExtractString(sVerLocal, 2, "."))
            ia3 = CInt(goTR.ExtractString(sVerLocal, 3, "."))

            If ia1 = 2 And ia2 = 0 And ia3 <= 21 Then
                bOldPCLink = True
            Else
                bOldPCLink = False
            End If

        Catch ex As Exception
            bOldPCLink = False
        End Try

        Dim sproc As String = "clSend::GetSendJobCount"
        Dim sSendPage As String = ""
        Dim sLog As String = ""
        Dim iVal As Integer


        If bOldPCLink = False And goP.TestVersion(goP.GetVersion("PCLink"), sVerLocal) = "-" Then
            Return -1
        End If


        Dim sqlConnection1 As SqlClient.SqlConnection = goData.GetConnection

        Dim cmd As New SqlClient.SqlCommand
        Dim mColl As New Collection

        'Try

        cmd.CommandText = "pGetSendJobCount"
            cmd.CommandType = CommandType.StoredProcedure
            cmd.Connection = sqlConnection1

            iVal = CInt(cmd.ExecuteScalar())

            sqlConnection1.Close()
            Return iVal

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sproc)
        '    End If
        'End Try

        Return 0

    End Function

    Public Function GetFileVersionDate(ByVal FName As String, ByVal sPath As String) As Date


        Dim lpath As String = ""

        Try

            If LCase(sPath) = "temp" Then
                lpath = goP.sPath & "\Temp\"
            ElseIf LCase(sPath) = "templates" Then
                lpath = goP.sPath & "\Templates\"
            ElseIf LCase(sPath) = "downloads" Then
                lpath = goP.sPath & "\Downloads\"
            Else
                lpath = sPath
            End If
            lpath = goTR.Replace(lpath, "\\", "\")

            Dim fi As System.IO.FileInfo = My.Computer.FileSystem.GetFileInfo(lpath & FName)

            Return fi.LastWriteTimeUtc

        Catch ex As Exception

            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, lpath, , lpath)
            'End If

            Return goTR.NowServer()

        End Try

    End Function
    Public Function AddSendJob(ByVal par_sSendName As String, _
                               ByVal par_sIDOfObjectToSend As String, _
                               ByVal par_sTemplatePath As String, _
                               Optional ByVal par_sObjectToSend As String = "CORR", _
                               Optional ByVal par_sMode As String = "EMAIL", _
                               Optional ByVal par_sFile As String = "", _
                               Optional ByVal par_bIndividualizedEmail As Boolean = False, _
                               Optional ByVal par_bPreview As Boolean = True, _
                               Optional ByVal par_sTo As String = "", _
                               Optional ByVal par_sCc As String = "", _
                               Optional ByVal par_sBc As String = "") As String
        'MI 1/3/08 Added case "FILE" to par_sMode.
        'MI 1/16/07 Created.
        'PURPOSE:
        '       Create SNQ_ (send queue) metadata page for processing by
        '       a client sending utility.
        'PARAMETERS:
        '       par_sSendName: Descriptive 'Name' of the send job in default language.
        '           This name will be displayed in the client utility that allows managing
        '           queued jobs. Suggested format is:
        '           Case "CORR"
        '                sSendName = goDef.GetSendFormatLabel(sSendType) & " " & goData.GetFileLabelFromName(sFileName) & " '" & sSendName & "'"
        '           Case "RECORD"
        '             sSendName = "Record details for " & goData.GetFileLabelFromName(sFileName) & " '" & sSendName & "'"
        '           Case "VIEW"
        '                sSendName = "View '" & sSendName & "'"
        '       par_sIDOfObjectToSend: ID as string of the object to send. This can be
        '           a record ID of the record or ID of the view.
        '       par_sTemplatePath: filename of the send template (defined in SND_ metadata). In some
        '           cases may be optional. See clSend for details.
        '       par_sObjectToSend: "CORR" for correspondence (AC and QT), "VIEW", or "RECORD" for record details.
        '       par_sMode: how to send: "EMAIL", "FAX", "LETTER", "FILE". Use "FILE" when sending from
        '           non-correspondence files. "LETTER" may not be supported even for CORR object?
        '       par_sFile: Filename of the view to send. To retrieve a filename from VIE_ MD,
        '           use goTr.StrRead(sViewMDPage, "FILE", ""). Optional for record and correspondence
        '           sending.
        '       par_bIndividualizedEmail: if True, a correspondence template will be processed in the
        '           "individualized" mode: an e-mail will be generated for each recipient, a letter
        '           will be generated with a section for each recipient, etc. Default: false.
        '       par_bPreview: If True, the e-mail, fax, or letter will be displayed before it is sent.
        '           Default: True
        '       par_sTo: 'ADDRESS' property: the 'To' e-mails, address, or fax numbers. If provided, this information
        '           overrides information from the correspondence record. In the case of view or record
        '           details sending, this information is mandatory unless the user is expected to enter
        '           this info in the preview window.
        '       par_sCc: 'CC' property (not supported yet).
        '       par_sBc: 'BC' property (not supported yet).
        'RETURNS: Page ID of the SNQ_ page created or "" if an error occurred.

        Dim sProc As String = "clSend::AddSendJob"
        Dim sPageID As String
        Dim sFile As String = par_sFile
        Dim sVals As String = ""

        'Validate or retrieve filename
        If sFile = "" Then
            sFile = goTR.GetFileFromSUID(par_sIDOfObjectToSend)
        End If
        If Not goData.IsFileValid(sFile) Then
            goErr.SetError(10100, sProc, , sFile)
            '10100: Invalid file name '[1]'. [2]
            Return ""
        End If

        'validate object to send
        Select Case UCase(par_sObjectToSend)
            Case "CORR", "VIEW", "RECORD", "REQUEST"
                'Pass
            Case Else
                goErr.SetError(10103, sProc, , "par_sObjectToSend", sProc, par_sObjectToSend)
                '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
        End Select

        'Validate sending mode
        Select Case UCase(par_sMode)
            Case "EMAIL", "FAX", "LETTER", "FILE", "S3UPLOAD", "S3DOWNLOAD", "EXCELUPLOAD", "MEETINGREQUEST"
                'Pass
            Case Else
                goErr.SetError(10103, sProc, , "par_sMode", sProc, par_sMode)
                '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
        End Select

        'Concat the string
        goTR.StrWrite(sVals, "US_NAME", goDef.GetMetaTypeFromPrefix("SNQ_"))
        goTR.StrWrite(sVals, "MODE", par_sMode)
        goTR.StrWrite(sVals, "FILE", sFile)
        goTR.StrWrite(sVals, "ID", par_sIDOfObjectToSend)
        goTR.StrWrite(sVals, "TEMPLATEPATH", par_sTemplatePath)
        goTR.StrWrite(sVals, "INDIVIDUALIZEDEMAIL", goTR.CheckBoxToText(par_bIndividualizedEmail, True))
        goTR.StrWrite(sVals, "PREVIEW", goTR.CheckBoxToText(par_bPreview, True))
        If par_sTo <> "" Then goTR.StrWrite(sVals, "ADDRESS", par_sTo) 'To be chaged to "TO"
        If par_sCc <> "" Then goTR.StrWrite(sVals, "CC", par_sCc)
        If par_sBc <> "" Then goTR.StrWrite(sVals, "BC", par_sBc)
        goTR.StrWrite(sVals, "OBJECTTOSEND", par_sObjectToSend)   'VIEW, RECORD, or CORR
        goTR.StrWrite(sVals, "SENDNAME", par_sSendName)

        sPageID = "SNQ_" & goData.GenSUID("XX")
        If Not goMeta.PageWrite(goP.GetUserTID(), sPageID, sVals) Then
            sPageID = ""
        End If

        Return sPageID

    End Function
    '*******************************************************************************
    'Public Function StartEmailLogging() As Boolean
    '    'AUTHOR: MI 3/6/07
    '    'PURPOSE:
    '    '       Create a send job (SNQ_ MD page) to start logging e-mails
    '    '       This is for initiating logging emails from the browser.
    '    'RETURNS:
    '    '       True or False if unsuccessful.

    '    Dim sProc As String = "clSend::StartEmailLogging"
    '    Dim sPageID As String
    '    Dim sIni As String = ""

    '    goTR.StrWrite(sIni, "MODE", "LOGEMAIL")
    '    goTR.StrWrite(sIni, "US_NAME", "LOGEMAIL")
    '    goTR.StrWrite(sIni, "SENDNAME", "Start logging e-mail")

    '    sPageID = "SNQ_" & goData.GenSUID("XX")
    '    If Not goMeta.PageWrite(goP.GetUserTID(), sPageID, sIni) Then
    '        goErr.SetError(35000, sProc, "Error writing SNQ_ metadata to initiate logging e-mail.")
    '        Return False
    '    End If

    '    Return True

    'End Function

    Public Function CreateOutlookMeetingRequest(ByVal sSubject As String, ByVal sBody As String, ByVal dtStart As DateTime, ByVal dtEnd As DateTime, _
                    ByVal sRequiredAttendees As String, ByVal sOptionalAttendees As String, Optional ByVal bReminderSet As Boolean = False, _
                    Optional ByVal iReminderMinutesBeforeStart As Integer = 5, Optional ByVal sLocation As String = "", _
                    Optional ByVal bIsOnlineMeeting As Boolean = False, Optional ByVal bAllDayEvent As Boolean = False, Optional ByVal sAttachmentFile As String = "") As Boolean

        'AUTHOR: RH 2/9/2012
        'PURPOSE:
        '       Create a TMP md page to hold details of Outlook Meeting Request
        '       Create a send job (SNQ_ MD page) to create Outlook Meeting Request
        'PARAMETERS:
        '       sSubject    Subject
        '       sBody       Body
        '       dtStart     Date/Time of meeting start
        '       dtEnd       Date/Time of meeting end
        '       sRequiredAttendees      Comma delimited list of email addresses 
        '       sOptionalAttendees      Comma delimited list of email addresses 
        '       bReminderSet    Boolean for whether to set reminder
        '       iReminderMinutesBeforeStart Number of minutes before meeting for reminder
        '       sLocation   Meeting Location
        '       bIsOnlineMeeting    Boolean whether online
        '       bAllDayEvent        Boolean whether all day event
        '       sAttachmentFile     Name of file that exists in the temp folder to attach to the request


        'RETURNS:
        '       True or False if unsuccessful.

        Dim sProc As String = "clSend::CreateOutlookMeetingRequest"
        Dim sTMPPageID As String = "TMP_" & goData.GenSUID("XX")
        Dim sIni As String = ""

        goTR.StrWrite(sIni, "SUBJECT", sSubject)
        goTR.StrWrite(sIni, "BODY", sBody)
        goTR.StrWrite(sIni, "DTSTART", dtStart.ToString)
        goTR.StrWrite(sIni, "DTEND", dtEnd.ToString)
        goTR.StrWrite(sIni, "REQUIREDATTENDEES", sRequiredAttendees)
        goTR.StrWrite(sIni, "OPTIONALATTENDEES", sOptionalAttendees)
        goTR.StrWrite(sIni, "REMINDERSET", bReminderSet.ToString)
        goTR.StrWrite(sIni, "REMINDERMINUTES", iReminderMinutesBeforeStart.ToString)
        goTR.StrWrite(sIni, "LOCATION", sLocation)
        goTR.StrWrite(sIni, "ISMEETINGONLINE", bIsOnlineMeeting)
        goTR.StrWrite(sIni, "ALLDAYEVENT", bAllDayEvent)
        goTR.StrWrite(sIni, "ATTACHMENT", sAttachmentFile)

        If Not goMeta.PageWrite("GLOBAL", sTMPPageID, sIni) Then
            goErr.SetError(35000, sProc, "Error writing TMP_ metadata to initiate an Outlook Meeting Request.")
            Return False
        End If
        sIni = ""

        AddSendJob("Create Meeting Request", sTMPPageID, "", "REQUEST", "MEETINGREQUEST", "AC")

        Return True


    End Function
    Public Function MarkRecordAsSent(ByVal par_sID As String, ByVal par_sObjectToSend As String) As Boolean
        'PURPOSE:
        '       Set in correspondence or record being sent field CHK_Sent to checked and
        '       update MMO_History.
        'PARAMETERS:
        '       par_sID: GID_ID of the record sent.
        '       par_sObjectToSend: "RECORD" or "CORR". If another value is passed, False is returned.
        'RETURNS:
        '       True on success, False otherwise. Sets error if file not valid. If Commit fails,
        '       sets warning and returns False.

        Dim sProc As String = "clSend::MarkRecordAsSent"

        Dim sID As String
        Dim sFile As String
        Dim doRS As clRowSet

        'Try

        If UCase(par_sObjectToSend) <> "RECORD" And UCase(par_sObjectToSend) <> "CORR" Then
                Return False
            End If

            sID = par_sID
            sFile = goTR.GetFileFromSUID(sID)
            If Not goData.IsFileValid(sFile) Then
                goErr.SetError(10100, sProc, , sFile, "File retrieved from ID '" & sID & "'.")
                '10100: Invalid file name '[1]'. [2]
            End If
            doRS = New clRowSet(sFile, clC.SELL_EDIT, "GID_ID=" & sID, , "CHK_Sent, MMO_History", 1)
            'If we can't read the record, it got deleted by someone else. Do not raise error.
            If doRS.GetFirst() = 1 Then
                doRS.SetFieldVal("CHK_Sent", 1, clC.SELL_SYSTEM)
                Select Case UCase(par_sObjectToSend)
                    Case "RECORD"
                        doRS.SetFieldVal("MMO_History", goTR.AddLineToRecHistory(doRS.GetFieldVal("MMO_History"), "Record Sent", sFile))
                    Case Else       'CORR
                        doRS.SetFieldVal("MMO_History", goTR.AddLineToRecHistory(doRS.GetFieldVal("MMO_History"), "Sent", sFile))
                End Select
                If doRS.Commit() <> 1 Then
                    goErr.SetWarning(35000, sProc, "Updating CHK_Sent failed in record '" & sFile & "." & sID & "'.")
                    Return False
                End If
            End If

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return True

    End Function
    '*******************************************************************************



End Class
