﻿CREATE TABLE [dbo].[US_RELATED_LB] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_US_RELATED_LB_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    [GID_LB] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_US_RELATED_LB] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_LB_CONNECTED_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_RELATED_LB] FOREIGN KEY ([GID_LB]) REFERENCES [dbo].[LB] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[US_RELATED_LB] NOCHECK CONSTRAINT [LNK_LB_CONNECTED_US];


GO
ALTER TABLE [dbo].[US_RELATED_LB] NOCHECK CONSTRAINT [LNK_US_RELATED_LB];


GO
CREATE NONCLUSTERED INDEX [IX_US_RELATED_LB]
    ON [dbo].[US_RELATED_LB]([GID_US] ASC, [GID_LB] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_LB_CONNECTED_US]
    ON [dbo].[US_RELATED_LB]([GID_LB] ASC, [GID_US] ASC);

