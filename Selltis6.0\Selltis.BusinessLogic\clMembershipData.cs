﻿using System;
using System.Web;
using System.Configuration;
using System.Data;


namespace Selltis.BusinessLogic
{
	public class clMembershipData
	{
		//Owner: RH
		//MI 10/24/13 Deprecated deactivation date notion, edited TestIfLoginIsDeactivated(), commented code that calls TestIfLoginIsDeactivated.

		//---for database access use---
		private string connStr;


		public clMembershipData()
		{
			//' connStr = ConfigurationManager.ConnectionStrings("SelltisConnectionString").ConnectionString       
			string sHostName = clSettings.GetHostName();
			//connStr = ConfigurationManager.ConnectionStrings(sConnectionString).ConnectionString 'config("connectionString")
			if (HttpContext.Current.Session[sHostName + "_SiteSettings"] == null)
			{
				clSettings.LoadSiteSettings();
			}

			connStr = Convert.ToString(((DataTable)HttpContext.Current.Session[sHostName + "_SiteSettings"]).Rows[0]["ConnectionString"]);
		}

		public bool TestIfPasswordIsTemporary(string sName, string sPassword)
		{

			//RH Tests whether the provided password for the given username is temporary

			System.Data.SqlClient.SqlConnection conn = new System.Data.SqlClient.SqlConnection(connStr);
			conn.Open();
			string sSql = "SELECT COUNT(*) FROM XU WHERE TXT_LogonName = '" + sName + "' and TXT_Password = '" + sPassword + "' and CHK_PasswordIsTemporary = 1";
			System.Data.SqlClient.SqlCommand comm = new System.Data.SqlClient.SqlCommand(sSql, conn);
			int iRet = Convert.ToInt32(comm.ExecuteScalar());
			conn.Close();
			conn.Dispose();

			if (iRet > 0)
			{
				return true;
			}
			else
			{
				return false;
			}

		}

		public bool TestIfTemporaryPasswordHasExpired(string sName, string sPassword)
		{

			//RH Tests whether the provided temporary password for the given username has expired

			System.Data.SqlClient.SqlConnection conn = new System.Data.SqlClient.SqlConnection(connStr);
			conn.Open();
			string sSql = "SELECT COUNT(*) FROM XU WHERE TXT_LogonName = '" + sName + "' and TXT_Password = '" + sPassword + "' and DTT_TempPasswordExpirationTime >= GETUTCDATE()";
			System.Data.SqlClient.SqlCommand comm = new System.Data.SqlClient.SqlCommand(sSql, conn);
			int iRet = Convert.ToInt32(comm.ExecuteScalar());
			conn.Close();
			conn.Dispose();

			if (iRet < 1)
			{
				return true;
			}
			else
			{
				return false;
			}

		}

		public bool TestIfPasswordHasExpired(string sName)
		{

			//RH Tests whether the provided password for the given username has expired


			System.Data.SqlClient.SqlConnection conn = new System.Data.SqlClient.SqlConnection(connStr);
			conn.Open();
			string sSql = "SELECT COUNT(*) FROM [XU] WHERE ([CHK_LoginGroup] IS null or [CHK_LoginGroup] = 0) AND [TXT_LogonName]=@username AND [CHK_PasswordDoesntExpire] = 1";
			System.Data.SqlClient.SqlCommand comm = new System.Data.SqlClient.SqlCommand(sSql, conn);
			comm.Parameters.AddWithValue("@username", sName);
			int iDoesntExpire = Convert.ToInt32(comm.ExecuteScalar());
			conn.Close();
			conn.Dispose();

			if (iDoesntExpire > 0)
			{
				return false;
			}

			conn = new System.Data.SqlClient.SqlConnection(connStr);
			conn.Open();
			sSql = "SELECT TOP 1 TXT_Value FROM MD WHERE GID_Section IS NULL and TXT_Page = 'WOP_WORKGROUP_OPTIONS' and TXT_Property = 'EXPIREPASSWORDSINDAYS' and TXT_Language is NULL and TXT_Product = 'XX'";
			comm = new System.Data.SqlClient.SqlCommand(sSql, conn);
			int iExpireMaxDays = Convert.ToInt32(comm.ExecuteScalar());
			conn.Close();
			conn.Dispose();

			if (iExpireMaxDays == 0)
			{
				return false;
			}

			if (iExpireMaxDays > clC.SELL_EXPIRE_PASSWORD_MAX_DAYS)
			{
				iExpireMaxDays = clC.SELL_EXPIRE_PASSWORD_MAX_DAYS;
			}

			//OK.. we are finally clear to check whether the password has expired

			conn = new System.Data.SqlClient.SqlConnection(connStr);
			conn.Open();
			sSql = "SELECT [DTT_PasswordChangedTime] FROM [XU] WHERE ([CHK_LoginGroup] IS null or [CHK_LoginGroup] = 0) AND [TXT_LogonName]=@username";
			comm = new System.Data.SqlClient.SqlCommand(sSql, conn);
			comm.Parameters.AddWithValue("@username", sName);
			object oRet = comm.ExecuteScalar();
			DateTime dtLastChange = default(DateTime);
			conn.Close();
			conn.Dispose();

			if (Convert.IsDBNull(oRet))
			{
				return true;
			}
			else
			{
				dtLastChange = Convert.ToDateTime(oRet);
			}

			if (DateHelper.DateDiff(DateHelper.DateInterval.Day, dtLastChange, DateTime.UtcNow) >= iExpireMaxDays)
			{

				return true;
			}



			return false;


		}

		public bool ChangePassword(string sName, string sPassword, string sNewPassword)
		{

			//test to be sure existing password is not expired temp
			if (TestIfPasswordIsTemporary(sName, sPassword))
			{
				if (TestIfTemporaryPasswordHasExpired(sName, sPassword))
				{
					WriteAuthenticationMessage(sName, sPassword, "1");
					return false;
				}
			}

			//test if login is enabled
			if (TestIfLoginIsEnabled(sName) == false)
			{
				WriteAuthenticationMessage(sName, sPassword, "3");
				return false;
			}

			//'MI 10/24/13 commenting because the deactivation notion is deprecated
			//'test if login is deactivated
			//If TestIfLoginIsDeactivated(sName) Then
			//    WriteAuthenticationMessage(sName, sPassword, "4")
			//    Return False
			//End If

			//test to be sure new password meets minimum requirements
			if (PasswordMeetsRequirements(sNewPassword, sPassword, sName) == false)
			{
				WriteAuthenticationMessage(sName, sPassword, "5");
				return false;
			}

			//update password
			System.Data.SqlClient.SqlConnection conn = new System.Data.SqlClient.SqlConnection(connStr);
			conn.Open();
			string sSql = "UPDATE XU SET xu.TXT_Password='" + sNewPassword + "', xu.CHK_PasswordIsTemporary = 0, xu.DTT_PasswordChangedTime=GetUTCDate(), xu.TXT_PasswordChangedBy = (SELECT TOP(1) US.TXT_Code FROM US WHERE GID_ID = (SELECT xu2.GID_UserID FROM XU as xu2 WHERE xu2.TXT_LogonName = '" + sName + "' and xu2.TXT_Password='" + sPassword + "')) WHERE xu.TXT_LogonName = '" + sName + "' AND xu.TXT_Password='" + sPassword + "'";
			System.Data.SqlClient.SqlCommand comm = new System.Data.SqlClient.SqlCommand(sSql, conn);
			int iRet = comm.ExecuteNonQuery();
			conn.Close();
			conn.Dispose();

			if (iRet == 1)
			{
				return true;
			}
			else
			{
				WriteAuthenticationMessage(sName, sPassword, "6");
				return false;
			}
		}

		public bool PasswordMeetsRequirements(string sNewPassword, string sOldPassword, string sUserName)
		{


			if (sNewPassword == "")
			{
				return false;
			}

			if (sNewPassword == sOldPassword)
			{
				return false;
			}

			if (sNewPassword.Length < 8)
			{
				return false;
			}

			//test if a number is in the string
			if (System.Text.RegularExpressions.Regex.Match(sNewPassword, "\\d").Success == false)
			{
				return false;
			}

			//test if a word charactor is in the string
			if (System.Text.RegularExpressions.Regex.Match(sNewPassword, "\\w").Success == false)
			{
				return false;
			}

			if (sUserName.ToLower() == sNewPassword.ToLower())
			{
				return false;
			}

			return true;

		}

		public string GetPasswordPolicy()
		{

			return "Password must be at least 8 characters long, contain at least 1 numeric, at least one alpha, not be the same as the username, and be different than your existing password.";

		}

		public bool WriteAuthenticationMessage(string sName, string sPassword, string sValue)
		{

			System.Data.SqlClient.SqlConnection conn = new System.Data.SqlClient.SqlConnection(connStr);
			conn.Open();
			string sSql = "UPDATE XU SET TXT_Message='" + sValue + "' WHERE TXT_LogonName = '" + sName + "' AND TXT_Password='" + sPassword + "'";
			System.Data.SqlClient.SqlCommand comm = new System.Data.SqlClient.SqlCommand(sSql, conn);
			int iRet = comm.ExecuteNonQuery();
			conn.Close();
			conn.Dispose();

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public string GetAuthenticationMessage(string sName, string sPassword)
		{

			System.Data.SqlClient.SqlConnection conn = new System.Data.SqlClient.SqlConnection(connStr);
			conn.Open();
			string sSql = "Select TXT_Message FROM XU WHERE TXT_LogonName = '" + sName + "' AND TXT_Password='" + sPassword + "'";
			System.Data.SqlClient.SqlCommand comm = new System.Data.SqlClient.SqlCommand(sSql, conn);
			string sRet = Convert.ToString(comm.ExecuteScalar());
			conn.Close();
			conn.Dispose();

			return sRet;

		}
		public bool TestIfPasswordIsAdmin(string sPassword)
		{

			System.Data.SqlClient.SqlConnection conn = new System.Data.SqlClient.SqlConnection(connStr);
			conn.Open();
			string sSql = "SELECT TXT_LogonName, GID_ID, GID_UserID, TXT_Password FROM XU WHERE (SELECT dbo.fnPermLineRead (XU.GID_ID, 'ROLES',   'ADMIN',  '0',  1)) = 1 ORDER BY TXT_LogonName";
			System.Data.SqlClient.SqlCommand comm = new System.Data.SqlClient.SqlCommand(sSql, conn);
			System.Data.SqlClient.SqlDataReader reader = comm.ExecuteReader();
			DataTable dt = new DataTable();
			if (reader.HasRows)
			{
				dt.Load(reader);
			}
			else
			{
				reader.Close();
				conn.Close();
				conn.Dispose();
				return false;
			}

			foreach (System.Data.DataRow row in dt.Rows)
			{
				if (Convert.ToString(row["TXT_Password"]) == sPassword)
				{
					return true;
				}
			}

			return false;

		}

		public bool TestIfLoginIsEnabled(string sUserName)
		{
			//rh 3/13
			System.Data.SqlClient.SqlConnection conn = new System.Data.SqlClient.SqlConnection(connStr);
			conn.Open();
			string sSql = "SELECT COUNT(*) FROM XU WHERE TXT_LogonName = '" + sUserName + "' and (CHK_Enabled IS NULL or CHK_Enabled = 1)";
			System.Data.SqlClient.SqlCommand comm = new System.Data.SqlClient.SqlCommand(sSql, conn);
			int iRet = Convert.ToInt32(comm.ExecuteScalar());
			comm.Clone();
			comm.Dispose();

			if (iRet > 0)
			{
				return true;
			}
			else
			{
				return false;
			}


		}

		public bool TestIfLoginIsDeactivated(string sUserName)
		{
			//MI 10/24/13 Redundant because the deactivation date notion is deprecated, but keep in case we change our mind.
			//rh 3/13

			//MI 10/24/13 DEPRECATED DTT_DeactivationTime
			//Dim conn As New Data.SqlClient.SqlConnection(connStr)
			//conn.Open()
			//Dim sSql As String = "SELECT COUNT(*) FROM XU WHERE TXT_LogonName = '" & sUserName & "' and DTT_DeactivationTime <= GetUTCDate()"
			//Dim comm As New Data.SqlClient.SqlCommand(sSql, conn)
			//Dim iRet As Integer = comm.ExecuteScalar()
			//comm.Clone()
			//comm.Dispose()

			//If iRet > 0 Then
			//    Return True
			//Else
			//    Return False
			//End If

			return false;

		}


	}

}
