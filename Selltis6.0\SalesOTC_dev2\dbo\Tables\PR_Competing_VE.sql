﻿CREATE TABLE [dbo].[PR_Competing_VE] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Project_Competing_Vendor_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PR] UNIQUEIDENTIFIER NOT NULL,
    [GID_VE] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PR_Competing_VE] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PR_Competing_VE] FOREIGN KEY ([GID_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_VE_CompetitorIn_PR] FOREIGN KEY ([GID_PR]) REFERENCES [dbo].[PR] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PR_Competing_VE] NOCHECK CONSTRAINT [LNK_PR_Competing_VE];


GO
ALTER TABLE [dbo].[PR_Competing_VE] NOCHECK CONSTRAINT [LNK_VE_CompetitorIn_PR];


GO
CREATE CLUSTERED INDEX [IX_PR_Competing_VE]
    ON [dbo].[PR_Competing_VE]([GID_VE] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_VE_CompetitorIn_PR]
    ON [dbo].[PR_Competing_VE]([GID_PR] ASC);

