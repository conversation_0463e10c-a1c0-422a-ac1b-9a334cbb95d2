﻿using System;
using System.IO;
using System.Web;


namespace Selltis.BusinessLogic
{
	public class cus_clSelltisMobileCustomTest
	{

	}
	public class cus_clSelltisMobileCustom : clSelltisMobileBase
	{
		public cus_clSelltisMobileCustom(cljsondata data) : base(data)
		{

			//call the base to initialize
		}

		public override bool OnPreSaveData(string sVar)
		{
			clTransform goTR;
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			//WriteLog("CusScripts", "OnSavePreData")

			string sSectionsToDisable = "";

			//CS 05192015 This runs before the record has been committed to the rowset and before all the fields have been set to the rowset.
			//Add custom code here.



			//Disable base sections of main script here
			//EXAMPlE: 
			//goTR.StrWrite(sSectionsToDisable, "FILLACENDDATEANDTIME", "1")
			//Base function checks for every code section before running it to see if disabled in cus.

			//WriteLog("CusScripts", sSectionsToDisable)


			//call the base script
			//CS 05192015 Call the base OnPreSave function. If you do not want to call the base function, comment out this line.
			if (base.OnPreSaveData(sSectionsToDisable) == false)
			{
				return false;
			}


			return true;

		}

		public override bool OnPostSaveData(string SectionsToDisable)
		{

			//WriteLog("CusScripts", "OnSavePostData")

			//CS 05192015 This runs after the record has been committed to the rowset.
			//Add custom code here.

			//call the base to initialize
			//CS 05192015 Call the base OnPostSave function. If you do not want to call the base function, comment out this line.
			return base.OnPostSaveData(SectionsToDisable);




		}

	}

}
