﻿CREATE TABLE [dbo].[SE] (
    [GID_ID]             UNIQUEIDENTIFIER CONSTRAINT [DF_SE_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),(0))+CONVERT([binary](2),'SE',(0)))+CONVERT([binary](6),getutcdate(),(0)),(0))) ROWGUIDCOL NOT NULL,
    [BI__ID]             BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]           NVARCHAR (80)    NULL,
    [DTT_CreationTime]   DATETIME         CONSTRAINT [DF_SE_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]       TINYINT          NULL,
    [TXT_ModBy]          VARCHAR (4)      NULL,
    [DTT_ModTime]        DATETIME         CONSTRAINT [DF_SE_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_StateName]      NVARCHAR (50)    NULL,
    [MMO_ImportData]     NTEXT            NULL,
    [SI__ShareState]     TINYINT          CONSTRAINT [DF_SE_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]   UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]       VARCHAR (50)     NULL,
    [TXT_ExternalID]     NVARCHAR (80)    NULL,
    [TXT_ExternalSource] VARCHAR (10)     NULL,
    [TXT_ImpJobID]       VARCHAR (20)     NULL,
    CONSTRAINT [PK_SE] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_SE_CreationTime]
    ON [dbo].[SE]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SE_StateName]
    ON [dbo].[SE]([TXT_StateName] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_SE_BI__ID]
    ON [dbo].[SE]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SE_ModDateTime]
    ON [dbo].[SE]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SE_TXT_ImportID]
    ON [dbo].[SE]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SE_CreatedBy_US]
    ON [dbo].[SE]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SE_Name]
    ON [dbo].[SE]([SYS_NAME] ASC);

