﻿CREATE TABLE [dbo].[NW] (
    [GID_ID]               UNIQUEIDENTIFIER CONSTRAINT [DF_NW_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'NW',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]               BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]             NVARCHAR (80)    NULL,
    [DTT_CreationTime]     DATETIME         CONSTRAINT [DF_NW_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]         TINYINT          NULL,
    [TXT_ModBy]            VARCHAR (4)      NULL,
    [DTT_ModTime]          DATETIME         CONSTRAINT [DF_NW_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_NinjaWebLeadName] NVARCHAR (50)    NULL,
    [MMO_ImportData]       NTEXT            NULL,
    [SI__ShareState]       TINYINT          CONSTRAINT [DF_NW_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]     UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]         VARCHAR (50)     NULL,
    [TXT_ExternalID]       NVARCHAR (80)    NULL,
    [TXT_ExternalSource]   VARCHAR (10)     NULL,
    [TXT_ImpJobID]         VARCHAR (20)     NULL,
    [TXT_WEBLEADNUMBER]    NVARCHAR (20)    NULL,
    [TXT_STUDENTFIRSTNAME] NVARCHAR (50)    NULL,
    [TXT_STUDENTLASTNAME]  NVARCHAR (50)    NULL,
    [TXT_PARENTFIRSTNAME]  NVARCHAR (50)    NULL,
    [TXT_PARENTLASTNAME]   NVARCHAR (50)    NULL,
    [EML_EMAIL]            NVARCHAR (1000)  NULL,
    [TEL_PHONENUMBER]      VARCHAR (39)     NULL,
    [TXT_ZIPCODE]          VARCHAR (10)     NULL,
    [GID_ASSIGNED_US]      UNIQUEIDENTIFIER NULL,
    [MLS_STAGE]            SMALLINT         NULL,
    [GID_RELATED_CN]       UNIQUEIDENTIFIER NULL,
    [MLS_Program]          SMALLINT         NULL,
    [MMO_Comments]         NTEXT            NULL,
    CONSTRAINT [PK_NW] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_NW_NinjaWebLeadName]
    ON [dbo].[NW]([TXT_NinjaWebLeadName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NW_CreatedBy_US]
    ON [dbo].[NW]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NW_ModDateTime]
    ON [dbo].[NW]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NW_Name]
    ON [dbo].[NW]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NW_CreationTime]
    ON [dbo].[NW]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_NW_BI__ID]
    ON [dbo].[NW]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NW_TXT_ImportID]
    ON [dbo].[NW]([TXT_ImportID] ASC);


GO



CREATE TRIGGER [dbo].[trNW_AfterInsert]
ON [dbo].[NW]
AFTER INSERT 
AS
BEGIN

    Declare @NewWLNo nvarchar(50)=''  

     Select @NewWLNo = 'SENW-' + Isnull(try_Cast(Max(try_Cast(Replace(Isnull(TXT_WEBLEADNUMBER,'SENW-9999'),'SENW-','') as int)) + 1 as nvarchar(20)),'10000') 
     from NW 
	 WHERE TXT_WEBLEADNUMBER like 'SENW-%'
        
   Update l 
   Set 
   l.TXT_WEBLEADNUMBER=@NewWLNo
   , l.GID_RELATED_CN= c.GID_ID
    
   FROM inserted i
   JOIN NW l on l.Gid_id=i.GID_ID
   LEFT JOIN CN c on cast(i.EML_EMAIL  as nvarchar(500))=Cast(c.EML_Email as nvarchar(500))
    
    
END

   
