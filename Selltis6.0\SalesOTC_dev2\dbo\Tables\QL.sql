﻿CREATE TABLE [dbo].[QL] (
    [GID_ID]                 UNIQUEIDENTIFIER CONSTRAINT [DF_QL_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'QL',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                 BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]               NVARCHAR (4000)  NULL,
    [FIL_Attachments]        NTEXT            NULL,
    [CHK_Open]               TINYINT          CONSTRAINT [DF_QuotLine_CHK_Open] DEFAULT ((1)) NULL,
    [MLS_CompetingPosition]  SMALLINT         NULL,
    [MMO_CompetitionNotes]   NTEXT            NULL,
    [CUR_Cost]               MONEY            NULL,
    [DTT_CreationTime]       DATETIME         CONSTRAINT [DF_QL_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [MLS_Currency]           SMALLINT         NULL,
    [CHK_DemoData]           TINYINT          NULL,
    [CUR_DiscAddlAmt]        MONEY            NULL,
    [SR__DiscPercent]        REAL             NULL,
    [SR__ExchRate]           REAL             NULL,
    [DTT_ExpCloseDate]       DATETIME         NULL,
    [CUR_GrossProfit]        MONEY            NULL,
    [SR__LineNo]             REAL             NULL,
    [MMO_Details]            NTEXT            NULL,
    [TXT_Model]              NVARCHAR (4000)  NULL,
    [SI__Month]              TINYINT          NULL,
    [SI__MonthClose]         TINYINT          NULL,
    [MMO_Notes]              NTEXT            NULL,
    [CUR_PriceUnit]          MONEY            NULL,
    [CUR_PriceUnitAfterDisc] MONEY            NULL,
    [CHK_Include]            TINYINT          CONSTRAINT [DF_QuotLine_CHK_Include] DEFAULT ((1)) NULL,
    [DTT_QteTime]            DATETIME         NULL,
    [SR__Qty]                REAL             NULL,
    [MLS_ReasonWonLost]      SMALLINT         NULL,
    [CHK_Report]             TINYINT          CONSTRAINT [DF_QuotLine_CHK_Report] DEFAULT ((1)) NULL,
    [CUR_SalesTax]           MONEY            NULL,
    [SR__SalesTaxPercent]    REAL             NULL,
    [MLS_Status]             SMALLINT         NULL,
    [CUR_Subtotal]           MONEY            NULL,
    [CHK_Taxable]            TINYINT          NULL,
    [DTT_Time]               DATETIME         NULL,
    [DTT_TimeCompleted]      DATETIME         NULL,
    [TXT_Unit]               NVARCHAR (10)    NULL,
    [URL_URLs]               NTEXT            NULL,
    [TXT_Year]               CHAR (4)         NULL,
    [TXT_YearClose]          CHAR (4)         NULL,
    [TXT_ModBy]              VARCHAR (4)      NULL,
    [DTT_ModTime]            DATETIME         CONSTRAINT [DF_QL_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [MMO_ImportData]         NTEXT            NULL,
    [SI__ShareState]         TINYINT          CONSTRAINT [DF_QL_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]       UNIQUEIDENTIFIER NULL,
    [GID_CreditedTo_US]      UNIQUEIDENTIFIER NULL,
    [GID_To_CO]              UNIQUEIDENTIFIER NULL,
    [GID_For_PD]             UNIQUEIDENTIFIER NULL,
    [GID_For_MO]             UNIQUEIDENTIFIER NULL,
    [GID_In_QT]              UNIQUEIDENTIFIER NULL,
    [GID_LostTo_VE]          UNIQUEIDENTIFIER NULL,
    [GID_Peer_US]            UNIQUEIDENTIFIER NULL,
    [GID_Related_DV]         UNIQUEIDENTIFIER NULL,
    [GID_Related_OP]         UNIQUEIDENTIFIER NULL,
    [GID_Related_PR]         UNIQUEIDENTIFIER NULL,
    [GID_Related_TE]         UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]           VARCHAR (50)     NULL,
    [SI__Day]                TINYINT          NULL,
    [SI__DayClose]           TINYINT          NULL,
    [TXT_ExternalID]         NVARCHAR (120)   NULL,
    [TXT_ExternalSource]     VARCHAR (10)     NULL,
    [TXT_ImpJobID]           VARCHAR (20)     NULL,
    [GID_TakenAt_LO]         UNIQUEIDENTIFIER NULL,
    [ADR_Attachments]        NTEXT            NULL,
    [CUR_DailyRentalRate]    MONEY            NULL,
    [CUR_SALEPRICE]          MONEY            NULL,
    [CUR_LISTPRICE]          MONEY            NULL,
    [CUR_EXTENDEDPRICE]      MONEY            NULL,
    [LI__MINRENTALDAYS]      INT              NULL,
    [CUR_RENTALREVENUE]      MONEY            NULL,
    [TXT_LINEDESCRIPTION]    NVARCHAR (1000)  NULL,
    [Txt_PriceUnit]          AS               (case [cur_saleprice] when (0) then '' else ([dbo].[GetCurrencyCode]([MLS_Currency])+' ')+replace(CONVERT([nvarchar](20),format([cur_saleprice],'C2'),(0)),'$','') end),
    [Txt_DAILYRENTALRATE]    AS               (case [CUR_DAILYRENTALRATE] when (0) then '' else ([dbo].[GetCurrencyCode]([MLS_Currency])+' ')+replace(CONVERT([nvarchar](20),format([Cur_DailyRentalRate],'C2'),(0)),'$','') end),
    [Txt_SUBTOTAL]           AS               (case [CUR_EXTENDEDPRICE] when (0) then '' else ([dbo].[GetCurrencyCode]([MLS_Currency])+' ')+replace(CONVERT([nvarchar](20),format([CUR_EXTENDEDPRICE],'C2'),(0)),'$','') end),
    [GID_RELATED_PG]         UNIQUEIDENTIFIER NULL,
    [GID_RELATED_PC]         UNIQUEIDENTIFIER NULL,
    [CUR_UnitPrice]          MONEY            NULL,
    [GID_RELATED_VE]         UNIQUEIDENTIFIER NULL,
    [CUR_LineTotal]          MONEY            NULL,
    [GID_FOR_VE]             UNIQUEIDENTIFIER NULL,
    [SI__GROSSMARGIN]        FLOAT (53)       NULL,
    [CUR_UNITGM]             MONEY            NULL,
    [SI__SELLMULTI]          TINYINT          NULL,
    [SI__COSTMULTI]          TINYINT          NULL,
    [GID_RELATED_PF]         UNIQUEIDENTIFIER NULL,
    [GID_ORIGINATEDBY_CN]    UNIQUEIDENTIFIER NULL,
    [TXT_ModelPrefix]        AS               (case when [Gid_For_MO] IS NULL then '' else 'Model #' end),
    [TXT_FORMODELNAME]       NVARCHAR (500)   NULL,
    [GID_JCIID]              UNIQUEIDENTIFIER NULL,
    [CUR_POCOST]             MONEY            NULL,
    [CHK_ASSEMBLYLINEITEM]   TINYINT          NULL,
    [GID_ParentLineId]       UNIQUEIDENTIFIER NULL,
    [MMO_AssemblyLinesNotes] NTEXT            NULL,
    [TXT_SXLINENO]           NVARCHAR (250)   NULL,
    [TXT_SXSTATUS]           NVARCHAR (250)   NULL,
    [GID_RELATED_BC]         UNIQUEIDENTIFIER NULL,
    [CUR_FDFLINEVALUE]       MONEY            NULL,
    [SR__SKU]                REAL             NULL,
    [CUR_LineCost]           AS               ([SR__QTY]*[CUR_COST]),
    [TXT_ISMVendorID]        NVARCHAR (250)   NULL,
    [SR__HP]                 REAL             NULL,
    [SR__GET]                REAL             NULL,
    [CUR_WEIGHTEDPROFIT]     MONEY            NULL,
    [CUR_WEIGHTEDVALUE]      MONEY            NULL,
    [GID_RELATED_BR]         UNIQUEIDENTIFIER NULL,
    [TXT_LOSTREASONCODE]     NVARCHAR (250)   NULL,
    [TXT_PRICEORIGINCODE]    NVARCHAR (250)   NULL,
    [GID_RELATED_BU]         UNIQUEIDENTIFIER NULL,
    [CHK_NOTINCLUDEINTOTAL]  TINYINT          NULL,
    CONSTRAINT [PK_QL] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_QL_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QL_CreditedTo_US] FOREIGN KEY ([GID_CreditedTo_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QL_For_MO] FOREIGN KEY ([GID_For_MO]) REFERENCES [dbo].[MO] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QL_For_PD] FOREIGN KEY ([GID_For_PD]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QL_In_QT] FOREIGN KEY ([GID_In_QT]) REFERENCES [dbo].[QT] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QL_LostTo_VE] FOREIGN KEY ([GID_LostTo_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QL_Peer_US] FOREIGN KEY ([GID_Peer_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QL_Related_DV] FOREIGN KEY ([GID_Related_DV]) REFERENCES [dbo].[DV] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QL_Related_OP] FOREIGN KEY ([GID_Related_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QL_Related_PR] FOREIGN KEY ([GID_Related_PR]) REFERENCES [dbo].[PR] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QL_Related_TE] FOREIGN KEY ([GID_Related_TE]) REFERENCES [dbo].[TE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QL_To_CO] FOREIGN KEY ([GID_To_CO]) REFERENCES [dbo].[CO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[QL] NOCHECK CONSTRAINT [LNK_QL_CreatedBy_US];


GO
ALTER TABLE [dbo].[QL] NOCHECK CONSTRAINT [LNK_QL_CreditedTo_US];


GO
ALTER TABLE [dbo].[QL] NOCHECK CONSTRAINT [LNK_QL_For_MO];


GO
ALTER TABLE [dbo].[QL] NOCHECK CONSTRAINT [LNK_QL_For_PD];


GO
ALTER TABLE [dbo].[QL] NOCHECK CONSTRAINT [LNK_QL_In_QT];


GO
ALTER TABLE [dbo].[QL] NOCHECK CONSTRAINT [LNK_QL_LostTo_VE];


GO
ALTER TABLE [dbo].[QL] NOCHECK CONSTRAINT [LNK_QL_Peer_US];


GO
ALTER TABLE [dbo].[QL] NOCHECK CONSTRAINT [LNK_QL_Related_DV];


GO
ALTER TABLE [dbo].[QL] NOCHECK CONSTRAINT [LNK_QL_Related_OP];


GO
ALTER TABLE [dbo].[QL] NOCHECK CONSTRAINT [LNK_QL_Related_PR];


GO
ALTER TABLE [dbo].[QL] NOCHECK CONSTRAINT [LNK_QL_Related_TE];


GO
ALTER TABLE [dbo].[QL] NOCHECK CONSTRAINT [LNK_QL_To_CO];


GO
CREATE NONCLUSTERED INDEX [IX_QL_Related_TE]
    ON [dbo].[QL]([GID_Related_TE] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_LostTo_VE]
    ON [dbo].[QL]([GID_LostTo_VE] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_CreationTime]
    ON [dbo].[QL]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_StatusSubtotalRev]
    ON [dbo].[QL]([MLS_Status] ASC, [CUR_Subtotal] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_ModDateTime]
    ON [dbo].[QL]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_TakenAt_LO]
    ON [dbo].[QL]([GID_TakenAt_LO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_CreditedTo_US]
    ON [dbo].[QL]([GID_CreditedTo_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_StatusQteDTRevLine]
    ON [dbo].[QL]([MLS_Status] ASC, [DTT_QteTime] DESC, [SR__LineNo] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_To_CO]
    ON [dbo].[QL]([GID_To_CO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_Name]
    ON [dbo].[QL]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_QteDateTimeRevLine]
    ON [dbo].[QL]([DTT_QteTime] DESC, [SR__LineNo] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_For_MD]
    ON [dbo].[QL]([GID_For_MO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_TXT_ImportID]
    ON [dbo].[QL]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_Peer_US]
    ON [dbo].[QL]([GID_Peer_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_Related_DV]
    ON [dbo].[QL]([GID_Related_DV] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_For_PD]
    ON [dbo].[QL]([GID_For_PD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_StatusExpClDteSTRevs]
    ON [dbo].[QL]([MLS_Status] ASC, [DTT_ExpCloseDate] ASC, [CUR_Subtotal] DESC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_QL_BI__ID]
    ON [dbo].[QL]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_Related_OP]
    ON [dbo].[QL]([GID_Related_OP] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_In_QT]
    ON [dbo].[QL]([GID_In_QT] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_Related_PR]
    ON [dbo].[QL]([GID_Related_PR] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_CreatedBy_US]
    ON [dbo].[QL]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_FOR_PF]
    ON [dbo].[QL]([GID_RELATED_PF] ASC)
    INCLUDE([GID_In_QT]);


GO
CREATE NONCLUSTERED INDEX [IX_QL_RELATED_PF]
    ON [dbo].[QL]([DTT_Time] ASC, [GID_RELATED_PF] ASC)
    INCLUDE([GID_RELATED_BC]);


GO
CREATE NONCLUSTERED INDEX [IX_QL_RELATED_BC]
    ON [dbo].[QL]([GID_RELATED_BC] ASC, [DTT_Time] ASC, [GID_RELATED_PF] ASC);


GO
CREATE TRIGGER trQLUpdateTN
ON [QL]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in QL table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(120)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'QL'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable
					and tn2.GID_InternalID = inserted.GID_ID)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [QL]
			SET [QL].TXT_ExternalSource = '', [QL].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [QL].GID_ID = in1.GID_ID
				and ISNULL([QL].TXT_ExternalSource, '') <> ''
				and ISNULL([QL].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trQLUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trQLUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trQLUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!