Imports System.Web

'author: wt

Public Class clHistoryItem

    Private goErr As clError

    Private gsType As String
    Private gsGuid As String
    Private gsTitle As String
    Private gsSeqNo As String
    Private gsNDBType As String
    Private gsNDBGUID As String
    Private gsSingleReferenceKey As String

    Public Property Type() As String
        Get
            Return gsType
        End Get
        Set(ByVal value As String)
            gsType = value
        End Set
    End Property

    Public Property GUID() As String
        Get
            Return gsGuid
        End Get
        Set(ByVal value As String)
            gsGuid = value
        End Set
    End Property

    Public Property Title() As String
        Get
            Return gsTitle
        End Get
        Set(ByVal value As String)
            gsTitle = value
        End Set
    End Property

    Public Property SeqNo() As String
        Get
            Return gsSeqNo
        End Get
        Set(ByVal value As String)
            gsSeqNo = value
        End Set
    End Property

    Public Property NDBType() As String
        Get
            Return gsNDBType
        End Get
        Set(ByVal value As String)
            gsNDBType = value
        End Set
    End Property

    Public Property NDBGUID() As String
        Get
            Return gsNDBGUID
        End Get
        Set(ByVal value As String)
            gsNDBGUID = value
        End Set
    End Property

    Public Property SingleReferenceKey() As String
        Get
            Return gsSingleReferenceKey
        End Get
        Set(ByVal value As String)
            gsSingleReferenceKey = value
        End Set
    End Property

    Private Sub Initialize()
        Dim sProc As String = "Default.aspx::Initialize"
        ' Try
        goErr = HttpContext.Current.Session("goErr")
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Public Sub New(ByVal sType As String, ByVal sGUID As String, ByVal sTitle As String, ByVal sSeqNo As String, Optional ByVal sSpecialType As String = "", Optional ByVal sSpecialGUID As String = "", Optional ByVal sSingleReferenceKey As String = "")
        Dim sProc As String = "clHistoryItem::New"
        ' Try
        Initialize()
            gsType = sType
            gsGuid = sGUID
            gsTitle = sTitle

            'if there is a hard return here, then the history panel will error
            gsTitle = gsTitle.Replace(Chr(13), "")
            gsTitle = gsTitle.Replace(Chr(10), "")

            gsSeqNo = sSeqNo
            gsNDBType = sSpecialType
            gsNDBGUID = sSpecialGUID

            gsSingleReferenceKey = sSingleReferenceKey

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

End Class