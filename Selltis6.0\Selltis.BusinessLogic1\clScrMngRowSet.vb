﻿Imports Microsoft.VisualBasic
Imports System
Imports System.Web
Imports System.Data
Imports System.Reflection
Imports System.Configuration

Public Class clScrMngRowSet

    Dim goP As clProject
    Dim goMeta As clMetaData
    Dim goTR As clTransform
    Dim goData As clData
    Dim goErr As clError
    Dim goLog As clLog
    Dim goDef As clDefaults
    Dim goScr As clScrMngRowSet
    Dim oScripts As New clBaseScripts
    Dim oScriptsCustom As New clScriptsRowSetCustom

    Public Sub Initialize()

        goP = HttpContext.Current.Session("goP")
        goTR = HttpContext.Current.Session("goTr")
        goMeta = HttpContext.Current.Session("goMeta")
        goData = HttpContext.Current.Session("goData")
        goErr = HttpContext.Current.Session("goErr")
        goLog = HttpContext.Current.Session("goLog")
        goDef = HttpContext.Current.Session("goDef")
        goScr = HttpContext.Current.Session("goScr")

        oScripts.Initialize()
        oScriptsCustom.Initialize()


    End Sub

    Function RunScript(ByVal par_sScriptName As String, Optional ByRef par_doCallingObject As Object = Nothing, Optional ByVal par_doArray As Object = Nothing, Optional ByVal par_s1 As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "", Optional ByRef par_oReturn As Object = Nothing, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
        'Function RunScript(ByVal par_sScriptName As String, Optional ByRef par_doCallingObject As Object = Nothing, Optional ByVal par_doArray As Object = Nothing, Optional ByVal par_s1 As Object = "", Optional ByVal par_s2 As Object = "", Optional ByVal par_s3 As Object = "", Optional ByVal par_s4 As Object = "", Optional ByVal par_s5 As Object = "", Optional ByRef par_oReturn As Object = Nothing, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
        'MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.
        '       Also resetting errors when returning True to prevent casual warnings from being
        '       interpreted by the calling code as errors to display to the user.
        'MI 10/25/07 Added par_sSections.

        'RE WARNINGS: We want warnings to survive only 'upstream' when a script sets Return False or par_bRunNext=False.
        'Warnings are reset (goErr.SetError()) before running the Pre, main, and Post scripts. This ensures that a warning set in a 
        'previously run script won't be picked up within the script we are invoking and erroneously interpreted as
        'a warning. If the script doesn't test warnings, all of this is irrelevant, but if testing code is added later,
        'the coder will be able to rely that the slate was cleaned when the script was invoked.

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start: '" & par_sScriptName & "'.", clC.SELL_LOGLEVEL_DEBUG, True)

        'Return oScripts._RunScriptManager(par_sScriptName, _
        '                                    par_doCallingObject, _
        '                                    par_doArray, _
        '                                    par_s1, _
        '                                    par_s2, _
        '                                    par_s3, _
        '                                    par_s4, _
        '                                    par_s5, _
        '                                    par_oReturn, _
        '                                    par_bRunNext, _
        '                                    par_sSections)

        Dim bResult As Boolean = False
        Dim mType As System.Type = GetType(clBaseScripts)
        Dim mInfo As System.Reflection.MethodInfo = mType.GetMethod(par_sScriptName, Reflection.BindingFlags.Public Or Reflection.BindingFlags.NonPublic Or Reflection.BindingFlags.Instance Or Reflection.BindingFlags.Static Or Reflection.BindingFlags.IgnoreCase)

        If mInfo Is Nothing Then
            Dim dll = Assembly.LoadFile(HttpContext.Current.Request.MapPath("/") & "//bin" & "//Selltis.Core.dll")
            mType = dll.GetType("Selltis.Core.Scripts")
            mInfo = mType.GetMethod(par_sScriptName, Reflection.BindingFlags.Public Or Reflection.BindingFlags.NonPublic Or Reflection.BindingFlags.Instance Or Reflection.BindingFlags.Static Or Reflection.BindingFlags.IgnoreCase)

        End If

        'TO-DO
        'bResult = goScr.RunCustomScript(par_sScriptName & "_Pre", par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)

        'If Not mInfo Is Nothing Then
        '    Dim oParamArray() As Object = {par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections}
        '    If par_bRunNext And bResult Then
        '        goErr.SetError()   'MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.
        '        Dim oClass = Activator.CreateInstance(mType)
        '        'Dim objScrt = TryCast(oClass, clBaseScripts)
        '        oClass.Initialize()
        '        bResult = mInfo.Invoke(oClass, oParamArray)
        '        par_bRunNext = oParamArray(8)     'bRunNext
        '    End If
        'Else
        '    If par_bRunNext And bResult Then bResult = goScr.RunCustomScript(par_sScriptName, par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)

        'End If

        'If par_bRunNext And bResult Then bResult = goScr.RunCustomScript(par_sScriptName & "_Post", par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)

        If Not mInfo Is Nothing Then
            'Method found in clScripts
            'Pre script: if not found returns True and bRunNext remains True
            bResult = goScr.RunCustomScript(par_sScriptName & "_Pre", par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)
            'Main script - runs only if the pre script isn't overriding it (bRunNext = True) and didn't return false
            'Dim oParamArray() As Object = {par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections}
            Dim oParamArray() As Object = {par_doCallingObject, par_oReturn, par_bRunNext, par_sSections, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5}
            If par_bRunNext And bResult Then
                goErr.SetError()   'MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.
                Dim oClass = Activator.CreateInstance(mType)
                'Dim objScrt = TryCast(oClass, clBaseScripts)
                oClass.Initialize()
                bResult = mInfo.Invoke(oClass, oParamArray)
                'par_bRunNext = oParamArray(8)     'bRunNext
                par_bRunNext = oParamArray(2)
            End If
            'Post script - run only if the main script didn't return false and the _Pre script didn't set bRunNext to false
            If par_bRunNext And bResult Then
                'bResult = goScr.RunCustomScript(par_sScriptName & "_Post", oParamArray(0), oParamArray(1), oParamArray(2), oParamArray(3), oParamArray(4), oParamArray(5), oParamArray(6), oParamArray(7), oParamArray(8), oParamArray(9))
                bResult = goScr.RunCustomScript(par_sScriptName & "_Post", oParamArray(0), oParamArray(4), oParamArray(5), oParamArray(6), oParamArray(7), oParamArray(8), oParamArray(9), oParamArray(1), oParamArray(2), oParamArray(3))
                'Set all ByRef variables in case the called custom script modified them
                par_doCallingObject = oParamArray(0)
                par_oReturn = oParamArray(1)
                par_bRunNext = oParamArray(2)
                par_sSections = oParamArray(3)
            End If
        Else
            ''NEW CODE 02242016 RUNSCRIPT USING DLL NAME/PATH
            'bResult = RunScriptLoadDllCSHARP(par_sScriptName, par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)

            'Method not found in clScripts, check whether we have a custom '_PRE', main, or '_POST' script
            bResult = goScr.RunCustomScript(par_sScriptName & "_Pre", par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)
            If par_bRunNext And bResult Then bResult = goScr.RunCustomScript(par_sScriptName, par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)
            If par_bRunNext And bResult Then bResult = goScr.RunCustomScript(par_sScriptName & "_Post", par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)
        End If

        'MI 3/31/09 Added. If result is true, we reset warnings so that they are not erroneously
        'picked up in the calling code. Warnings that are not followed with Return false are considered
        'informational and are not meant to be handled by the calling code. There can be multiple
        'warnings raised within a code module or modules it calls. In this case, only the last 
        'warning would be picked up and possibly reported by the calling code. Such cases must be
        'dealt with explicitly, capturing and storing each warning as it occurs, then returned together
        'as a single warning followed by Return false.
        'For example: a form is calling FormOnSave script, which calls GetFieldVal, which raises a warning. 
        'If FormOnSave returns False, the form will get the warning and display it in a warning panel. 
        'If FormOnSave returns True, the form won't look for warnings anyway, but assuming that it does
        'it will not get the warning because we reset it here. 
        If bResult Then goErr.SetError()

        Return bResult


        '---------------- OLD NGP CODE -------------------
        ''CS In progress, RH or MI finish porting
        ''PORT. This is the method that gets called from scripts and inside WD code
        ''to run a particular script.

        ''---------
        ''SET ERROR
        ''---------
        ''LAST MOD: MI 1/19/05		AUTHOR: FH 4/5/04
        ''PURPOSE:
        ''		Front end for starting a script (from a script or from WinDev code).
        ''		Originally this was a procedure in W_PROC.
        ''PARAMETERS:
        ''		par_sScriptPageIDOrName:	Page ID of the script or Name of the script
        ''			All other parameter depends of the calling code:
        ''		par_doCallingObject: 
        ''		par_doArray: 
        ''		par_s1: 
        ''		par_s2: 
        ''		par_s3: 
        ''		par_s4: 
        ''		par_s5: 
        ''RETURNS:
        ''		True of false, with a possible SetError. If script not found, unloadable, etc, returns -1
        ''HOW IT WORKS:
        ''		Try to find the script meta record, based on it's pageID or it's name, then try to 
        ''		load and execute the script
        ''EXAMPLE:
        ''		if not goScr:RunScript(sScriptName) then

        'Dim sProc As String = "clScrMng::RunScript"
        ''if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_sScriptPageIDOrName, SELL_LOGLEVEL_DEBUG)

        'goLog.SetError()
        'Dim lErr As Long = 0

        'Dim sPageID As String
        ''Dim sName As String
        'Dim sMeta As String
        'Dim iResult As Integer
        ''First try to find the script
        'If goTR.IsPageID(par_sScriptPageIDOrName) Then
        '    sPageID = par_sScriptPageIDOrName
        '    'Look if we find the record
        '    sMeta = Trim(goMeta.PageRead(goP.GetUserTID(), sPageID))
        '    If sMeta = "" Then
        '        goLog.SetError(10506, sProc, "", sPageID)
        '        ' The requested script '[1]' was not found. 
        '        '
        '        'Please contact Sellis support.
        '        Return (-1)
        '    End If
        '    'Else  'CS: Commented out Else b/c of hReadSeek
        '    '    sName = UCase(par_sScriptPageIDOrName)
        '    '    If Not goData.hReadSeek("_META", "MetaTypeSort1", goTR.Complete("SCR", dimension(_META.TXT_MetaType)) & goTR.Complete(sName, dimension(_META.TXT_SortValue1))) Then
        '    '        goLog.SetError(10506, sProc, "", sName)
        '    '        ' The requested script '[1]' was not found. 
        '    '        '
        '    '        'Please contact Sellis support.
        '    '        Return (-1)
        '    '    End If
        '    '    'Here, we found it, we can take its pageID
        '    '    sPageID = _META.TXT_Page
        'End If

        ''And then try to load it
        ''doScript is object dynamic=new clscript()

        ''CS Commented out here to end until clScript available
        ''Dim doScript As New clScript
        ''iResult = doScript.Init(sPageID)
        ''If iResult Then
        ''    iResult = doScript : Execute(par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5)
        ''    End

        ''    Select Case goLog.GetLastError()
        ''        Case "E00000"
        ''            'No error was set
        ''        Case "E10505", "E10901"
        ''            '10505: Recursivity forbidden. 10901: exception during execution
        ''            'Display these errors immediately since they are critical.
        ''            goLog.DisplayLastError()
        ''        Case Else
        ''            'Pass the error to the calling code without displaying it
        ''            'The calling code decides whether to "know" about the error or not.
        ''            lErr = goLog.SaveLastError()
        ''    End Select

        ''    delete(doScript)

        ''    If lErr > 0 Then
        ''        goLog.RestoreLastError(lErr)
        ''        goLog.DeleteSavedLastErrorNumber(lErr)
        ''    End If

        'Return (iResult)
    End Function

    Function IsSectionEnabled(ByVal par_sProc As String, _
                    ByVal par_sSections As String, _
                    ByVal par_sSectionName As String, _
                    Optional ByVal par_bRunByDefault As Boolean = True) As Boolean
        'MI 10/25/07 Created.
        'PURPOSE:
        '       To be used within script methods in clScripts or cus_clScriptsCustom
        '       to test whether a code "section" should be executed or not.
        '       This allows _Pre scripts or code calling goScr.RunScript() to suppress 
        '       or force portions of the main script to run. It can also suppress/force
        '       sections of _Post scripts. For more info, see comments about the 
        '       par_sSections parameter in clScripts._RunScriptManager.
        'FUTURE PLANS:
        '       A UI facility will allow defining which script sections to enable or
        '       disable. This method will read that definition from MD and merge it
        '       with par_sSectionsIni using gotr.MergeIniStrings(). par_sSectionsIni
        '       will have precedence so that the MD always can be overriden programmatically.
        'PARAMETERS:
        '       par_sProc: The sProc parameter that defines the class and method name
        '           in every script. If the script doesn't have a variable sProc,
        '           create it as "Script::ScriptName" both in main and custom scripts.
        '       par_sSections: Ini string that contains the definitions of which
        '           section to suppress and which to force run. This is passed to
        '           scripts via goScr.RunScript() through par_sSections parameter. 
        '           Supported format is:
        '               SectionName1=1
        '               SectionName2=0
        '           where 1 means 'run' and 0 means 'do not run'.
        '       par_sSectionName: Name of the section.
        '       par_bRunByDefault: Optional: If true (default), the section will run 
        '           if an override is not found
        'RETURNS:
        '       True if the section is enabled, False otherwise

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start: '" & par_sProc & "' section '" & par_sSectionName & "'.", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim sDefault As String
        Dim bResult As Boolean

        'Try
        If par_bRunByDefault Then
                sDefault = "1"
            Else
                sDefault = "0"
            End If

            If goTR.StrRead(par_sSections, par_sSectionName, sDefault, False) = "1" Then
                bResult = True
            Else
                bResult = False
            End If
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return bResult

    End Function

    Dim domain As AppDomain = Nothing

    Function RunCustomScript(ByVal par_sScriptName As String, Optional ByRef par_doCallingObject As Object = Nothing, Optional ByVal par_doArray As Object = Nothing, Optional ByVal par_s1 As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "", Optional ByRef par_oReturn As Object = Nothing, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
        'Function RunCustomScript(ByVal par_sScriptName As String, Optional ByRef par_doCallingObject As Object = Nothing, Optional ByVal par_doArray As Object = Nothing, Optional ByVal par_s1 As Object = "", Optional ByVal par_s2 As Object = "", Optional ByVal par_s3 As Object = "", Optional ByVal par_s4 As Object = "", Optional ByVal par_s5 As Object = "", Optional ByRef par_oReturn As Object = Nothing, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
        'MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.
        'MI 10/25/07 Added par_sSections.

        Try



            Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
            goLog.Log(sProc, "Start: '" & par_sScriptName & "'", clC.SELL_LOGLEVEL_DEBUG, True)

            'Return oScriptsCustom._RunScriptManager(par_sScriptName, _
            '                                    par_doCallingObject, _
            '                                    par_doArray, _
            '                                    par_s1, _
            '                                    par_s2, _
            '                                    par_s3, _
            '                                    par_s4, _
            '                                    par_s5, _
            '                                    par_oReturn, _
            '                                    par_bRunNext, _
            '                                    par_sSections)


            Dim bResult As Boolean = False
            'Dim bRunNext As Boolean = True

            'Dim dll = Assembly.LoadFile(HttpContext.Current.Request.MapPath("/") & "//bin" & "//Selltis.Core.dll")
            'Dim mType As System.Type = dll.GetType("Selltis.Core.ScriptsCustom")

            'Dim sHostName As String = ""
            'Dim sCustomDLLPath As String = ""

            '#If DEBUG Then
            '        sCustomDLLPath = HttpContext.Current.Request.MapPath("/") + "bin" + "//Selltis.Custom.dll"
            '#End If

            'read the "HostingEnvironment" from web config and if test then get the port number
            'Dim sHostingEnvironment As String = ConfigurationManager.AppSettings("HostingEnvironment").ToString().ToLower()
            'Dim sCustomDLLFolder As String = ""
            'If sHostingEnvironment = "debugging" Then
            '    sCustomDLLFolder = HttpContext.Current.Request.MapPath("/") + "bin"
            '    sCustomDLLPath = sCustomDLLFolder + "//Selltis.Custom.dll"
            'End If

            'If String.IsNullOrEmpty(sCustomDLLPath) Then
            '    If sHostingEnvironment = "staging" Then
            '        sHostName = HttpContext.Current.Request.Url.Port.ToString()
            '    Else
            '        sHostName = HttpContext.Current.Request.Url.Host 'dev.selltis.com
            '        sHostName = sHostName.Replace(".selltis.com", "") 'dev
            '    End If
            '    'sCustomDLLPath = (Convert.ToString(HttpContext.Current.Request.MapPath("/") + "bin\CustomFiles\") & sHostName) + "\Selltis.Custom.dll"
            '    sCustomDLLFolder = (Convert.ToString(HttpContext.Current.Request.MapPath("/") + "bin\CustomFiles\") & sHostName)
            '    sCustomDLLPath = sCustomDLLFolder + "\Selltis.Custom.dll"
            'End If

            ' Dim sCustomDLLName As String = DirectCast(HttpContext.Current.Session("SiteSettings"), DataTable).Rows(0)("CustomDLLName")
            'Dim dll = Assembly.LoadFile(HttpContext.Current.Request.MapPath("/") + "bin\" + sCustomDLLName)
            'Dim mType As System.Type = dll.GetType("Selltis.Custom.ScriptsCustom")

            If Not HttpContext.Current.Session("CUSTOMDLLEXISTED") = Nothing Then
                If HttpContext.Current.Session("CUSTOMDLLEXISTED") = False Then
                    Return True
                End If
            End If


            Dim sHostingEnvironment As String = ConfigurationManager.AppSettings("HostingEnvironment").ToString().ToLower()
            Dim sCusFilesPath As String = ConfigurationManager.AppSettings("CustomFilesPath").ToString()
            Dim sHostName As String = clSettings.GetHostName()

            Dim substring As String = sCusFilesPath.Substring(1, 2)
            If substring = ":\" Then
                sCusFilesPath = System.Configuration.ConfigurationManager.AppSettings("CustomFilesPath").ToString()
            Else
                sCusFilesPath = HttpContext.Current.Server.MapPath(System.Configuration.ConfigurationManager.AppSettings("CustomFilesPath").ToString())
            End If

            Dim sPortNumber As String = HttpContext.Current.Request.Url.Port.ToString()

            If sHostingEnvironment = "staging" Then
                sHostName = sHostName & "_" & sPortNumber
            ElseIf sHostingEnvironment = "debugging" Then
                sHostName = "default"
            End If

            Dim sCustomDLLName As String = DirectCast(HttpContext.Current.Session(sHostName + "_SiteSettings"), DataTable).Rows(0)("CustomDLLName")
            Dim sCustomDLLFilePath As String = sCusFilesPath & sHostName & "\\" & sCustomDLLName

            Dim cusDLLFilePath As String = clSettings.GetCustomDLL_FilePath()

            Dim dll As System.Reflection.Assembly

            If sHostingEnvironment = "debugging" Then
                dll = Assembly.LoadFrom(cusDLLFilePath)
            Else
                dll = Assembly.LoadFile(cusDLLFilePath)
            End If

            Dim mType As System.Type = dll.GetType("Selltis.Custom.ScriptsCustom")

            'Dim domaininfo As New AppDomainSetup()
            'domaininfo.ApplicationBase = HttpContext.Current.Request.MapPath("/") + "bin\"
            'Dim adevidence As System.Security.Policy.Evidence = AppDomain.CurrentDomain.Evidence
            'domain = AppDomain.CreateDomain("MyDomain", adevidence, domaininfo)

            'Dim type As Type = GetType(Proxy)
            'Dim value = DirectCast(domain.CreateInstanceAndUnwrap(type.Assembly.FullName, type.FullName), Proxy)

            'Dim dll = value.GetAssembly(HttpContext.Current.Request.MapPath("/") + "bin\" + sCustomDLLName)
            'Dim mType = dll.GetType("Selltis.Custom.ScriptsCustom")

            Dim mInfo As System.Reflection.MethodInfo = mType.GetMethod(par_sScriptName, Reflection.BindingFlags.Public Or Reflection.BindingFlags.NonPublic Or Reflection.BindingFlags.Instance Or Reflection.BindingFlags.Static Or Reflection.BindingFlags.IgnoreCase)

            If Not mInfo Is Nothing Then
                Dim oParamArray() As Object = {par_doCallingObject, par_oReturn, par_bRunNext, par_sSections, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5}
                If par_bRunNext Then
                    goErr.SetError()      'MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.
                    Dim oClass = Activator.CreateInstance(mType)
                    'Dim objScrt = TryCast(oClass, clBaseScripts)
                    oClass.Initialize()
                    bResult = mInfo.Invoke(oClass, oParamArray)
                    'bResult = mInfo.Invoke(oScriptsCustom, oParamArray)
                    'Set all ByRef variables in case the called custom script modified them
                    par_doCallingObject = oParamArray(0)
                    par_oReturn = oParamArray(1)
                    par_bRunNext = oParamArray(2)
                    par_sSections = oParamArray(3)
                End If
            Else
                'Undefined script is same as blank script = success = Return True
                'par_bRunNext = True
                bResult = True
            End If

            Return bResult

        Catch ex As Exception
            If domain IsNot Nothing Then
                AppDomain.Unload(domain)
            End If
            Return False
        Finally
            If domain IsNot Nothing Then
                AppDomain.Unload(domain)
            End If
        End Try

    End Function


    Function RunScriptLoadDllCSHARP(ByVal par_sScriptName As String, Optional ByRef par_doCallingObject As Object = Nothing, Optional ByVal par_doArray As Object = Nothing, Optional ByVal par_s1 As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "", Optional ByRef par_oReturn As Object = Nothing, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
        'Function RunScript(ByVal par_sScriptName As String, Optional ByRef par_doCallingObject As Object = Nothing, Optional ByVal par_doArray As Object = Nothing, Optional ByVal par_s1 As Object = "", Optional ByVal par_s2 As Object = "", Optional ByVal par_s3 As Object = "", Optional ByVal par_s4 As Object = "", Optional ByVal par_s5 As Object = "", Optional ByRef par_oReturn As Object = Nothing, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
        'MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.
        '       Also resetting errors when returning True to prevent casual warnings from being
        '       interpreted by the calling code as errors to display to the user.
        'MI 10/25/07 Added par_sSections.

        'RE WARNINGS: We want warnings to survive only 'upstream' when a script sets Return False or par_bRunNext=False.
        'Warnings are reset (goErr.SetError()) before running the Pre, main, and Post scripts. This ensures that a warning set in a 
        'previously run script won't be picked up within the script we are invoking and erroneously interpreted as
        'a warning. If the script doesn't test warnings, all of this is irrelevant, but if testing code is added later,
        'the coder will be able to rely that the slate was cleaned when the script was invoked.

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start: '" & par_sScriptName & "'.", clC.SELL_LOGLEVEL_DEBUG, True)

        'Return oScripts._RunScriptManager(par_sScriptName, _
        '                                    par_doCallingObject, _
        '                                    par_doArray, _
        '                                    par_s1, _
        '                                    par_s2, _
        '                                    par_s3, _
        '                                    par_s4, _
        '                                    par_s5, _
        '                                    par_oReturn, _
        '                                    par_bRunNext, _
        '                                    par_sSections)

        Dim bResult As Boolean = False
        Dim dll = Assembly.LoadFile(HttpContext.Current.Request.MapPath("/") & "//bin" & "//Selltis.Core.dll")
        'Dim mType As System.Type = Type.GetType("Selltis.BusinessLogic.clBaseScripts")
        Dim mType As System.Type = dll.GetType("Selltis.Core.Scripts")

        Dim mInfo As System.Reflection.MethodInfo = mType.GetMethod(par_sScriptName, Reflection.BindingFlags.Public Or Reflection.BindingFlags.NonPublic Or Reflection.BindingFlags.Instance Or Reflection.BindingFlags.Static Or Reflection.BindingFlags.IgnoreCase)
        If Not mInfo Is Nothing Then
            'Method found in clScripts
            'Pre script: if not found returns True and bRunNext remains True
            bResult = goScr.RunCustomScript(par_sScriptName & "_Pre", par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)
            'Main script - runs only if the pre script isn't overriding it (bRunNext = True) and didn't return false
            '///////////////SCRIPT PART//////////////
            Dim oParamArray() As Object = {par_doCallingObject, par_oReturn, par_bRunNext, par_sSections, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5}
            If par_bRunNext And bResult Then
                goErr.SetError()   'MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.

                'OLD CODE 02242016
                'Dim oClass = Activator.CreateInstance(mType)
                'Dim objScrt = TryCast(oClass, clBaseScripts)
                'objScrt.Initialize()
                'bResult = mInfo.Invoke(objScrt, oParamArray)
                'NEW CODE 02242016
                Dim cClass = Activator.CreateInstance(mType)
                cClass.Initialize()
                bResult = mInfo.Invoke(cClass, oParamArray)
                par_bRunNext = oParamArray(2)     'bRunNext
            End If

            'Post script - run only if the main script didn't return false and the _Pre script didn't set bRunNext to false
            If par_bRunNext And bResult Then
                bResult = goScr.RunCustomScript(par_sScriptName & "_Post", oParamArray(0), oParamArray(4), oParamArray(5), oParamArray(6), oParamArray(7), oParamArray(8), oParamArray(9), oParamArray(1), oParamArray(2), oParamArray(3))
                'Set all ByRef variables in case the called custom script modified them
                par_doCallingObject = oParamArray(0)
                par_oReturn = oParamArray(1)
                par_bRunNext = oParamArray(2)
                par_sSections = oParamArray(3)
            End If
        Else
            'Method not found in clScripts, check whether we have a custom '_PRE', main, or '_POST' script
            bResult = goScr.RunCustomScript(par_sScriptName & "_Pre", par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)
            If par_bRunNext And bResult Then bResult = goScr.RunCustomScript(par_sScriptName, par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)
            If par_bRunNext And bResult Then bResult = goScr.RunCustomScript(par_sScriptName & "_Post", par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)
        End If

        'MI 3/31/09 Added. If result is true, we reset warnings so that they are not erroneously
        'picked up in the calling code. Warnings that are not followed with Return false are considered
        'informational and are not meant to be handled by the calling code. There can be multiple
        'warnings raised within a code module or modules it calls. In this case, only the last 
        'warning would be picked up and possibly reported by the calling code. Such cases must be
        'dealt with explicitly, capturing and storing each warning as it occurs, then returned together
        'as a single warning followed by Return false.
        'For example: a form is calling FormOnSave script, which calls GetFieldVal, which raises a warning. 
        'If FormOnSave returns False, the form will get the warning and display it in a warning panel. 
        'If FormOnSave returns True, the form won't look for warnings anyway, but assuming that it does
        'it will not get the warning because we reset it here. 
        If bResult Then goErr.SetError()

        Return bResult
    End Function
End Class

Public Class Proxy
    Inherits MarshalByRefObject
    Public Function GetAssembly(assemblyPath As String) As Assembly
        Try
            Return Assembly.LoadFile(assemblyPath)
        Catch generatedExceptionName As Exception
            ' throw new InvalidOperationException(ex);
            Return Nothing
        End Try
    End Function
End Class
