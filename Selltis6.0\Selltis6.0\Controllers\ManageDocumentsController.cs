﻿using Selltis.BusinessLogic;
using Selltis.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;

namespace Selltis.MVC.Controllers
{
    public class ManageDocumentsController : Controller
    {
        // GET: ManageDocuments

        private clMetaData goMeta;

        private clTransform goTr;
        public ActionResult Documents()
        {
            return View();
        }
        public ActionResult GetDetails(string BuId, string fileName)
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTr = (clTransform)Util.GetInstance("tr");
            
            StringBuilder menu = new StringBuilder();

            //  string sHideList = Convert.ToString(goMeta.LineRead("GLOBAL", "OTH_QUOTE_TEMPLATE_CONFIGURATION", sCOBUID + "_HIDELIST", "0"));
            //int lists = Convert.ToInt32(goMeta.LineRead("GLOBAL", "OTH_QUOTE_TEMPLATE_CONFIGURATION", BuId + "_TEMPLATES", "0"));
            string lists = Convert.ToString(goMeta.LineRead("GLOBAL", "OTH_QUOTE_TEMPLATE_CONFIGURATION", BuId + "_TEMPLATES", "0"));

            string[] words = lists.Split(',');
           
            for (int i = 0; i < words.Length; i++)
            {

                string sTemplateFileName = Convert.ToString(goMeta.LineRead("GLOBAL", "OTH_QUOTE_TEMPLATE_CONFIGURATION", "TEMPLATE_" + words[i] + "_FILE", ""));
                string sTemplate = Convert.ToString(goMeta.LineRead("GLOBAL", "LST_QT:QTTEMPLATE", "US_" + words[i], ""));
                sTemplateFileName = sTemplateFileName.Replace("_draft", "").Replace("_Draft", "");


                //string sTemplate = sTemplateFileName.Replace("cus_", "").Replace("Cus_", "").Replace(".docx","").Replace(".Docx", "").Replace("_", "");
                //sTemplate = Regex.Replace(sTemplate, @"\B([A-Z])", " $1");

                string sPage = goMeta.PageRead("GLOBAL", "LST_ QT:QTTEMPLATE", "US_" + sTemplateFileName, true);

                menu.Append("<div class=\"list-group-item\">");
                menu.Append("<div class=\"pull-left\" style=\"padding-right: 10px; \">");
                menu.Append(" <span style=\"border-radius:64px; \" class=\"btn btn-circle-sm btn-info\"><i class=\"fa fa-file-word-o\"></i></span></div>");
                menu.Append("<div class=\"media-body\">");
                menu.Append("<div class=\"maintitle\">");
                menu.Append("<b><a class=\"anchorlink\"  href=\"https://otctest.selltis.com/PublicFiles/5f783588-49dc-47b3-820e-10d633cace6a/Templates/" + sTemplateFileName + "\"" + "title=\"" + sTemplate + "\">" + sTemplate + "</a></b></div>");
                menu.Append("<ul class=\"lgi-attrs\">");
                menu.Append(" <li class=\"btn-infoL2\">Business Unit :" + fileName + "</li>");
                menu.Append("<li class=\"btn-infoL1\">Document Type: Quote Template</li>");
                menu.Append(" <li class=\"btn-infoL4\">Modified By: System System</li>");
                menu.Append("</ul>");
                menu.Append("</div></div>");


            }
            string files= menu.ToString();



            return Content(files);
        }




    }
}