﻿using System;
using System.Web.Security;
using System.Web;
using System.Data;
using System.Diagnostics;

namespace Selltis.BusinessLogic
{
	public class clImport
	{

		private DataTable dtSource;
		private DataTable dtSourceUnique;
		private bool bAllowSourceDupes = false;
		private DataTable dtMap;
		private string sFileName;
		private string sImpJobID;
		private string sKeySource;
		private string sKeyDest;
		private DataTable dtKeyDest;
		private DataTable dtKeySource;
		private int iHighestBIID;

		//From MI clDiaDeskMan example
		public bool bBackFromSubdialog;
		public string sMessageBoxPurpose;

		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clLog goLog;
		private clDefaults goDef;

#region Public Properties
		public DataTable SourceDataTable
		{
			//Raw source data read by sending UI
			get
			{
				return dtSource;
			}
			set
			{
				dtSource = value;
			}
		}
		public DataTable SourceDataTableUnique
		{
			//Raw source data after dupes merged - dupes identified by source key field(s)
			get
			{
				return dtSourceUnique;
			}
			set
			{
				dtSourceUnique = value;
			}
		}
		public DataTable MapTable
		{
			//Datatable of mapped fileds and properties
			get
			{
				return dtMap;
			}
			set
			{
				dtMap = value;
			}
		}
		public string FileName
		{
			//Name of the file to import to
			get
			{
				return sFileName;
			}
			set
			{
				sFileName = value;
			}
		}
		public string SourceKey
		{
			//String representing the source field(s) and properties that make up the source key
			get
			{
				return sKeySource;
			}
			set
			{
				sKeySource = value;
			}
		}
		public string SelltisKey
		{
			//String representing the Selltis field(s) and properties that make up the Selltis key
			get
			{
				return sKeyDest;
			}
			set
			{
				sKeyDest = value;
			}
		}
		public string ImportJobID
		{
			//Id of this import job
			get
			{
				return sImpJobID;
			}
			set
			{
				sImpJobID = value;
			}
		}
		public int HighestBIID
		{
			//BI__ID value needed for start value of record when processing sys_names
			get
			{
				return iHighestBIID;
			}
			set
			{
				iHighestBIID = value;
			}
		}
		public bool AllowSourceDupes
		{
			//Set to True to import all source records regardless of duplicate keys
			get
			{
				return bAllowSourceDupes;
			}
			set
			{
				bAllowSourceDupes = value;
			}
		}
#endregion

		//Owner: PJ
		//MI 8/13/09 Resolved VS2008 warnings per PJ

		public clImport()
		{

		}

		public bool Logon(string sUser, string sPassword)
		{

			//PURPOSE:
			//		Validate logon and establish session
			//PARAMETERS:
			//		sUser
			//       sPassword
			//RETURNS:
			//		True/False
			//AUTHOR: RH

			//Dim sb As Boolean
			//sb = Membership.ValidateUser(sUser, sPassword)
			clSelltisMembershipProviderNew _memberShip = new clSelltisMembershipProviderNew();
			bool sb = _memberShip.ValidateUser(sUser, sPassword);

			if (sb)
			{
				clInit Init = new clInit();
				string sID = HttpContext.Current.Session.LCID.ToString();
			}

			return sb;

		}
		public bool ValidateSession()
		{

			try
			{
				clProject oP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
				if (oP.gsUserID == "")
				{

					HttpContext.Current.Session.Abandon();

					return false;
				}
				else
				{
					return true;
				}
			}
			catch (Exception ex)
			{
				HttpContext.Current.Session.Abandon();
				return false;
			}

		}

		public bool Initialize(string sImpJobID = "")
		{

			//PURPOSE:
			//		Initialize SQL import-related objects
			//PARAMETERS:
			//		none
			//RETURNS:
			//		True/False
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			goDef = (Selltis.BusinessLogic.clDefaults)HttpContext.Current.Session["goDef"];

			DataTable dtLog = new DataTable();

			try
			{
				//Create Log table. This will be returned to client
				dtLog.Columns.Add("Time");
				dtLog.Columns.Add("Level");
				dtLog.Columns.Add("Process");
				dtLog.Columns.Add("Status");

				goP.SetVar("ImportSQL_dtLog", dtLog);

				return true;

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
				return false;

			}

		}

		private DataTable CreateKeyDatatable(string sKey)
		{

			//PURPOSE:		Create datatable of key params to be stored in session and used throughout import process
			//               Import utility has text fields for source and selltis keys. Values are strings that can include
			//               field codes for more than one field.

			//PARAMETERS:    sKey: String containing key params. Supports
			//                       FLD=    Name of the field in the data table
			//                       LEN=    Length of the field value
			//                       NOR=    Normalized (True/False)
			//                       PAD=    -- not supported. multiple params --

			//CALLED FROM:   InitializeLocalVars()   
			//		
			//RETURNS:       DataTable of field(s) and parameters
			//		
			//AUTHOR: PJ
			//USED By: Import Utility 2.0

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			DataTable dtKey = new DataTable();
			DataColumn dc = null;
			DataRow dr = null;
			string sCol = "FLD,LEN,NOR";
			string[] aCol = sCol.Split(',');
			int iStart = 1;
			int iEnd = 0;
			string sFieldCode = "";
			string[] aFieldCode = null;
			string sFieldCodeName = null;
			string sLength = null;
			string sNormalize = null;

			try
			{
				for (int i = 0; i <= aCol.GetUpperBound(0); i++)
				{
					dc = new DataColumn();
					dc.ColumnName = aCol[i];
					dtKey.Columns.Add(dc);
				}

				while (iStart > 0)
				{
					dr = dtKey.NewRow();
					iStart = sKey.IndexOf("(%") + 1;
					if (iStart > 0)
					{
						iEnd = sKey.IndexOf("%)") + 1 + 1;
						if (iEnd > 0)
						{
							sFieldCode = sKey.Substring(iStart - 1, iEnd - iStart + 1);
							aFieldCode = sFieldCode.Split('|');
							if (aFieldCode[0].ToUpper().Contains("FLD="))
							{
								sFieldCodeName = aFieldCode[0].Replace("FLD=", "");
							}
							else
							{
								sFieldCodeName = sFieldCode; //This for backward compat. NO! for other values (%Zip%)
								//Exit For
							}

							sFieldCodeName = RemoveFieldCodeTags(sFieldCodeName);
							dr["FLD"] = sFieldCodeName;

							if (aFieldCode.GetUpperBound(0) > 0)
							{
								if (aFieldCode[1].ToUpper().Contains("LEN="))
								{
									sLength = aFieldCode[1].Replace("LEN=", "");
									sLength = RemoveFieldCodeTags(sLength);
									dr["LEN"] = sLength;
								}
								if (aFieldCode.GetUpperBound(0) > 1)
								{
									if (aFieldCode[2].ToUpper().Contains("NOR="))
									{
										sNormalize = aFieldCode[2].Replace("NOR=", "");
										sNormalize = RemoveFieldCodeTags(sNormalize);
										dr["NOR"] = sNormalize;
									}
								}
							}

							dtKey.Rows.Add(dr);
						}
					}
					sKey = sKey.Replace(sFieldCode, ""); //Remove this field code from key string in order to find next field code
				}

				return dtKey;

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
				return null;

			}

		}

		public bool Cleanup(string sIMPJobID)
		{

			//PURPOSE:
			//		Clear session variables
			//PARAMETERS:
			//		sIMPJobID: not used. No need to make session var's job specific since user can only run one job at a time(?)
			//RETURNS:
			//		
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			try
			{
				clProject goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
				goP.SetVar("ImportSQL_dtSource", null);
				goP.SetVar("ImportSQL_dtSourceDupes", null);
				goP.SetVar("ImportSQL_dtMap", null);
				goP.SetVar("ImportSQL_dtLST", null);
				goP.SetVar("ImportSQL_dtTransformed", null);
				goP.SetVar("ImportSQL_dtTransformedNew", null);
				goP.SetVar("ImportSQL_dtTransformedUpd", null);

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
				return false;

			}

			return true;

		}

		public DataTable GetLog(string sIMPJobID)
		{

			//PURPOSE:
			//		Return log file to user in a datatable for local display
			//       Called by consumer as needed
			//PARAMETERS:
			//		none
			//RETURNS:
			//		Datatable, instantiated in Initialize above
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			try
			{
				clProject goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];

				DataTable dtLog = (DataTable)goP.GetVar("ImportSQL_dtLog");

				dtLog.TableName = "dtLog";

				return dtLog;

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
				return null;

			}


		}

		public DataTable GetTables()
		{

			//PURPOSE:
			//		Get list of tables for this database.
			//PARAMETERS:
			//		nihil
			//RETURNS:
			//		delimited string
			//AUTHOR: PJ
			//USED By: Import Utilty 2.0

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			DataTable dt = new DataTable();
			string settings = Convert.ToString(HttpContext.Current.Session["Connection"]);
			System.Data.SqlClient.SqlConnection sqlConnection1 = new System.Data.SqlClient.SqlConnection(settings);
			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader reader = null;

			try
			{

				sqlConnection1.Open();

				cmd.CommandText = "pGetTables";
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				reader = cmd.ExecuteReader();

				if (reader.HasRows)
				{

					dt.Load(reader);

				}
				else
				{
					//==> raise error
				}

				reader.Close();
				sqlConnection1.Close();
				//LoadTableData = True

				dt.TableName = "Tables";

				return dt;

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
				return null;

			}


		}

		public DataTable GetFields()
		{

			//PURPOSE:
			//		Get list of fields for this database.
			//PARAMETERS:
			//		nihil
			//RETURNS:
			//		delimited string
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			DataTable dt = new DataTable();
			string settings = Convert.ToString(HttpContext.Current.Session["Connection"]);
			System.Data.SqlClient.SqlConnection sqlConnection1 = new System.Data.SqlClient.SqlConnection(settings);
			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader reader = null;

			try
			{

				sqlConnection1.Open();

				cmd.CommandText = "pGetFields";
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				reader = cmd.ExecuteReader();

				if (reader.HasRows)
				{

					dt.Load(reader);

				}
				else
				{
					//==> raise error
				}

				reader.Close();
				sqlConnection1.Close();
				//LoadTableData = True

				dt.TableName = "Fields";

				return dt;

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
				return null;

			}


		}

		public bool DedupeSourceData(DataTable par_dtSource, string par_sFile, DataTable par_dtMap, bool par_bUseNormalizedKey, string par_sIMPJobID, string par_sSourceKey = "")
		{

			//PURPOSE:
			//		Merge duplicates in the soruce datatable. Duplicates are identified by a key field 
			//       or combinatioin of fields.
			//       Only non-NULL values are merged
			//PARAMETERS:
			//		dtSource:           This is the datatable created by the consumer containing the raw source data
			//                           that has not been transformed yet.
			//       sFile:              SQL table name
			//       dtMap:              Field mapping, with column for 'Key' fields (True/False/Null)
			//       bUsenormalizedKey:  Normalized key values use 'NormalizeValue' function
			//       sIMPJobIDP:         Id of the import job, used for filtering field TXT_ImpJobID. Created by import utils
			//       sSourceKey:         String value of source key containing one or more fields with LEN, NOR, ..
			//RETURNS:
			//		True/False. De-duped datatable is stored in session variable, replaceing par_dtSource
			//AUTHOR: PJ

			//PJ: Add param to pass back the de-duped datatable. May want to take results back to source.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			string sID = HttpContext.Current.Session.LCID.ToString();

			clProject goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			clTransform goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTR"];

			DataTable dtSourceDupes = new DataTable();
			DataColumn dcPrimaryKey = null;
			int iKeyCount = -1;
			string sKeyExpression = "";
			DataRow dr = null;
			DataView dvMergeTo = null;
			int iMergeCount = 0;
			string sExpression = "";
			string sFilter = "";
			string sFilterPart = "";
			string sColName = "";
			DataColumn[] aKeys = new DataColumn[1];
			string sKeyValue = "";

			try
			{

				//--- Create table for source keys with columns for FLD, LEN, NOR
				//Compat with Import applet: Check if dtMap has 'Key' column and if value is 1 or True
				//   Key|SQLFieldName|Expression
				if (sKeySource == "")
				{
					if (par_dtMap != null)
					{
						if (par_dtMap.Columns.Contains("Key"))
						{
							foreach (DataRow row in par_dtMap.Rows)
							{
								if (!(row["Key"].ToString() == "System.DBNull"))
								{
									if (row["Key"].ToString() == "True" || row["Key"].ToString() == "1")
									{
										sKeySource = Convert.ToString(row["Expression"]);
									}
								}
							}
						}
					}
				}

				if (sKeySource != "")
				{
					dtKeySource = CreateKeyDatatable(sKeySource);
					if (dtKeySource.Rows.Count > 0)
					{
						iKeyCount = dtKeySource.Rows.Count;
					}
				}
				else
				{
					LogLine("0", "Dedupe Source Data: Key not set.", "Warning");
					return true;
				}

				//--- Add UniqueKey column to dtSource
				if (!par_dtSource.Columns.Contains("UniqueKey"))
				{
					par_dtSource.Columns.Add("UniqueKey");
				}

				//--- Populate UniqueKey value in each row of dtSource
				for (int i = 0; i < par_dtSource.Rows.Count; i++)
				{
					dr = par_dtSource.Rows[i];
					if (sKeySource != "")
					{
						sKeyValue = GetFieldValFromDataTable(ref par_dtSource, sKeySource, i);
					}
					dr["UniqueKey"] = sKeyValue;
				}

				//--- Create dtSourceUnique and set UniqueKey as key column
				dtSourceUnique = par_dtSource.Clone();
				if (!bAllowSourceDupes)
				{
					if (iKeyCount > 0)
					{
						//NEW way
						dcPrimaryKey = dtSourceUnique.Columns["UniqueKey"];
						aKeys = new DataColumn[1];
						aKeys[0] = dcPrimaryKey;
						dtSourceUnique.PrimaryKey = aKeys;
					}
				}

				//--- Copy rows from dtSource to dtSourceUnique. Catch errors which are likely due to dupe key
				for (int i = 0; i < par_dtSource.Rows.Count; i++)
				{
					try
					{
						dr = par_dtSource.Rows[i];
						//Replace nulls in primary keys with blank string, "". Otherwise ImportRow will fail since key col's can't be null
						for (int z = 0; z < par_dtSource.Columns.Count; z++)
						{
							sColName = par_dtSource.Columns[z].ColumnName;
							//If oHashTable.Contains(sColName) Then
							if (sColName == "UniqueKey")
							{
								//For this primary key row:
								if (Convert.IsDBNull(dr[sColName]))
								{
									dr[sColName] = "";
								}
								//If normalizing this key, copy normalized value to TXT_IDNormalized
								if (par_bUseNormalizedKey)
								{
									dr["TXT_IDNormalized"] = dr["TXT_IDNormalized"] + NormalizeValue(dr[sColName].ToString(), "");
								}
							}
						}
						dtSourceUnique.ImportRow(dr);

					}
					catch (Exception ex)
					{
						//Row could not be imported into par_dtSourceUnique, probably due to key violation. Try updating
						//Find row in par_dtSourceUnique
						//2/10: Update this to allow multiple key fields, using aKeys
						//Try
						if (iKeyCount > -1)
						{
								sFilter = "";
								sFilterPart = "";
								//Keys exist, so create filter string to be used in RowFilter
								for (int j = 0; j <= aKeys.GetUpperBound(0); j++)
								{
									if (Convert.IsDBNull(dr[aKeys[j].ColumnName])) //this is the key's value
									{
										//value is null
										string sTestName = aKeys[j].ColumnName.ToString();
										string stype = par_dtSource.Columns[sTestName].DataType.ToString();
										dr[aKeys[j].ColumnName] = "";
									}

									sFilterPart = aKeys[j].ColumnName + " = '" + goTR.PrepareForSQL(dr[aKeys[j].ColumnName].ToString()) + "'";
									if (sFilter == "")
									{
										sFilter = sFilterPart;
									}
									else
									{
										sFilter += " AND " + sFilterPart;
									}
								}

								dtSourceUnique.DefaultView.RowFilter = sFilter;
								dvMergeTo = dtSourceUnique.DefaultView; //dvMerge to contains the record to merge TO

								if (dvMergeTo.Count > 0)
								{
									//update this row in the datatable, column by column. Look for non-blank/null values only
									for (int j = 0; j < par_dtSource.Columns.Count; j++)
									{
										//This is the merge: Last one wins, overwriting values in 0th row record, with non-blank/null values in ith row record
										if (!Convert.IsDBNull(par_dtSource.Rows[i][j]))
										{
											//Get datatype of column and check for null, blank

											string sType = par_dtSource.Columns[j].DataType.ToString();

											if (par_dtSource.Rows[i][j].ToString() != "")
											{
												dvMergeTo[0][j] = par_dtSource.Rows[i][j].ToString();
											}
										}
									}
									//Log
									iMergeCount += 1;
									LogLine("1", "Merged '" + sFilter, "OK");
								}

							}

						//Catch ex1 As Exception
						//    'Merge failed. Return error message to the log. 
						//    LogLine(0, "Error adding/merging record " & i, "Failed")
						//End Try

					}

				} //par_dtSource.Row

				//for easy inspection, sort by key fld
				//par_dtSourceUnique.DefaultView.Sort = aKeys.GetValue(0).ToString

				//STEP 4: Save datatables in session var's
				goP.SetVar("ImportSQL_par_dtSource", dtSourceUnique);
				goP.SetVar("ImportSQL_par_dtSourceDupes", dtSourceDupes); //Not used in later process, but may want to return to user
				goP.SetVar("ImportSQL_par_dtMap", par_dtMap);

				LogLine("0", "Source Data: " + iMergeCount + " records merged", "OK");

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
				return false;

			}

			return true;

		}

		public DataTable GetSourceDupes(string sIMPJobID)
		{

			//PURPOSE:
			//		Return datatable of dupes to consumer
			//       Called by consumer as needed
			//PARAMETERS:
			//		sIMPJobID:  Id of the import job, used for filtering field TXT_ImpJobID. Created by import utils
			//RETURNS:
			//		Datatable of duplicate records
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			try
			{
				clProject goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];

				DataTable dtSourceDupes = (DataTable)goP.GetVar("ImportSQL_dtSourceDupes" + sIMPJobID);

				dtSourceDupes.TableName = "dtSourceDupes";

				return dtSourceDupes;

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
				return null;

			}


		}

		public bool TransformTable(string par_sSQLTableName, DataTable par_dtSourceUnique, DataTable par_dtMap, bool par_bUseNormalizedKey, bool par_bIgnoreTransformErrors, string sIMPJobID, int par_iOffsetMinutes = 0, bool par_bCreateMissingLinkedRecords = false, bool par_bCreateMissingLSTValues = false)
		{

			//PURPOSE:
			//		Validation: source datatable is inserted cell by cell into dtTransformed. dtTransformed column are sql data typed
			//       Validation fails in the following ways:
			//           CHK, ,"CUR", "DR_", "INT", "LI_", "BI_", "SI_", "SR_": Replaced with 0
			//           MLS: Lookup used with LST_ metadata. If not found, set to 0
			//           DTE: Replaced with Selltis empty date '1753-01-02 23:59:59.000'
			//           TXT, MMO: Length truncated
			//       Concatenation: Values from multiple source columns can be concatenated into one dtTransformed column
			//       Supported Functions: Specific VB-like functions are supported. Currenty: Propercase, FormatPhone
			//PARAMETERS:
			//       par_sSQLTableName:      2-char SQL table name
			//       par_dtSourceUnique:     Source datatable. Set to 'Nothing' if the datatable is created by DedupeSourceData() above
			//                                   Param is available here for calling this method first
			//       par_dtMap:              Map datatable. Columns are:
			//                                   Col1
			//                                   Col2...
			//       par_bUseNormalizedKey:  True if normalizing CO/CN
			//       par_bIgnoreTransformErrors: True to ignore transform errors and not log them. Increases performance
			//       sIMPJobID:              Id of the import job, used for filtering field TXT_ImpJobID. Created by import utils
			//       par_iOffsetMinutes:     Time offset for datetime fields. Timeless dates import as 00:00:00 GMT
			//       par_bCreateMissingLinkedRecords:    True to create linked records for selection type links
			//       par_bCreateMissingLSTValues:        True to create LST metadata

			//RETURNS:
			//		True/False. Transformed datatable is stored in session variable
			//AUTHOR: PJ
			//USED BY: Integrator APP

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];

			string sExpression = "";
			string sExpressionValue = "";
			DataTable dtDestinationSchema = new DataTable();
			DataTable dtTransformed = new DataTable();
			DataTable dtTransformedUpd = new DataTable();
			DataTable dtTransformedNew = new DataTable();
			string sDestFieldName = "";
			Microsoft.VisualBasic.Collection colDestFieldExpression = new Microsoft.VisualBasic.Collection();
			DataColumn dcFromSchema = null;
			DataColumn dcTransTable = null;
			DataColumn dcTransTableNew = null;
			int iError = 0;
			string sError = "";
			clTransform oTr = new clTransform();
			DataTable dtLST = new DataTable();
			bool bKeysAdded = false;

			try
			{

				//Get LST_ metadata and put in session var
				dtLST = GetLSTMetadata(par_sSQLTableName);
				goP.SetVar("ImportSQL_dtLST", dtLST);

				//If source data deduped first
				if (par_dtSourceUnique == null)
				{
					par_dtSourceUnique = (DataTable)goP.GetVar("ImportSQL_par_dtSource");
				}
				if (par_dtSourceUnique == null)
				{
					LogLine("0", sProc + ": Unable to read source data", "Failed");
					return false;
				}

				if (par_dtMap == null)
				{
					par_dtMap = (DataTable)goP.GetVar("ImportSQL_par_dtMap");
				}
				dtMap = par_dtMap;
				if (par_dtMap == null)
				{
					LogLine("0", sProc + ": Unable to read map data", "Failed");
					return false;
				}

				//Get schema from destination table for copying column objects REDO this METHOD - gets top 1 record
				dtDestinationSchema = GetTableSchemaDatatable(par_sSQLTableName);
				if (dtDestinationSchema.Columns.Count == 0)
				{
					//Get schema failed to return datatable. Record in log
					LogLine("0", sProc + ": Get SQL Table Schema", "Failed");
					return false;
				}
				else
				{
					//LogLine(0, "Get SQL Table Schema", "OK")

				}

				// ----------------- Create dtTransformed columns ------------------------
				//Read destination mapping from map table and load those columns from schema table into trans tables
				try
				{
					for (int k = 0; k < par_dtMap.Rows.Count; k++)
					{
						if (!Convert.IsDBNull(par_dtMap.Rows[k]["Expression"]))
						{
							sExpression = Convert.ToString(par_dtMap.Rows[k]["Expression"]);
							if (sExpression != "")
							{
								//Field is mapped. add schema column to trans table
								sDestFieldName = Convert.ToString(par_dtMap.Rows[k]["SQLFieldName"]);
								//V2: If sDestFieldName contains (%..%) then this is the key row in the map and should be ignored
								if (!sDestFieldName.Contains("(%"))
								{
									//V2: If this is a link, par_dtMap value is 'LNK_TeamLeader_US', but dtDestinationSchema value is
									//   'TXT_LNK_TeamLeader_US'. Need to get link type here and substitute temp field
									if (sDestFieldName.Substring(0, 4) == "LNK_")
									{
										//Add temp link fields to SQL table and dtDestinationSchema
										clSchema oSchema = new clSchema();
										clArray aLinks = new clArray();
										aLinks.Add(sDestFieldName);
										if (!oSchema.SchemaAddFieldsToTable(par_sSQLTableName, "", aLinks, true))
										{
											LogLine("0", sProc + ": Invalid Destination field name, '" + sDestFieldName + "'. Import terminated.", "Failed");
										}

										string sLinkType = Microsoft.VisualBasic.Strings.Split(goData.LK_GetType(par_sSQLTableName, sDestFieldName), "\t")[4];
										switch (sLinkType)
										{
											case "N1":
												sDestFieldName = "TXT_" + sDestFieldName;
												break;
											case "NN":
												sDestFieldName = "MMO_" + sDestFieldName;
												break;
											default:
											break;
										}
										//If temp link field, Reload schema table (top 1 rec)
										dtDestinationSchema = GetTableSchemaDatatable(par_sSQLTableName);
									}
									//Add dest field name and expression to collection for use below
									colDestFieldExpression.Add(sExpression, sDestFieldName);
									//Get column schema
									dcFromSchema = dtDestinationSchema.Columns[sDestFieldName];
									if (dcFromSchema == null)
									{
										//MsgBox("Invalid Destination field name, '" & sDestFieldName & "'")  'Put in status window
										LogLine("0", sProc + "\r\n" + "Invalid Destination field name, '" + sDestFieldName + "'. Import terminated.", "Failed");
										return false;
									}
									//Fix: use clone
									dcTransTable = new DataColumn();
									dcTransTable.ColumnName = dcFromSchema.ColumnName;
									dcTransTable.DataType = dcFromSchema.DataType;
									dcTransTable.MaxLength = dcFromSchema.MaxLength;
									dcTransTable.AllowDBNull = true;
									dtTransformed.Columns.Add(dcTransTable);

								}
							}
						}
					}

					//Add UniqueKey column to dtTransformed. This column has to be added because it is not
					//   part of the map and does not get created in the above loop
					dcTransTableNew = new DataColumn();
					dcTransTableNew.ColumnName = "UniqueKey";
					dcTransTableNew.DataType = System.Type.GetType("System.String");
					dcTransTableNew.MaxLength = 200;
					dcTransTableNew.AllowDBNull = true;
					dtTransformed.Columns.Add(dcTransTableNew);

				}
				catch (Exception ex)
				{
					//LogLine(0, sProc & ": Create transform table columns", "Failed")
					return false;

				}
				//-------------------------------------------------------------------------------

				//If using Normalized keys, add TXT_IDNormalized to each table
				if (par_bUseNormalizedKey)
				{
					dtTransformed.Columns.Add("TXT_IDNormalized");
				}

				//Add ImportJobID column. If this is mapped, cannot add here because it's in the table already
				if (!dtTransformed.Columns.Contains("TXT_ImpJobID"))
				{
					dtTransformed.Columns.Add("TXT_ImpJobID");
				}
				//== Empty transform tables are now created with all mapped columns ==

				//Add rows to transform table
				for (int iRow = 0; iRow < par_dtSourceUnique.Rows.Count; iRow++)
				{
					dtTransformed.Rows.Add();
				}

				//--------------- For each column in transfer table, fill row by row ----------------------
				for (int iCol = 0; iCol < dtTransformed.Columns.Count; iCol++)
				{

					//4/18: Get dt of linked file for LNK_, with field values that are link key values.
					string sFieldName = dtTransformed.Columns[iCol].ColumnName;
					string sLinkField = "";
					string sLinkedFile = "";
					string sLinkKeyField = ""; //get from map!
					DataTable dtLinkedKeys = null;
					DataTable dtLinkedKeysAdd = null;

					if (sFieldName.Contains("LNK_"))
					{
						//What is link name (not temp field name)
						if (sFieldName.StartsWith("TXT_LNK_") | sFieldName.StartsWith("MMO_LNK_"))
						{
							sLinkField = goTR.FromTo(sFieldName, 5);
						}
						//Get link file
						sLinkedFile = sLinkField.Substring(sLinkField.Length - 2);
						//Get link key field
						dtMap.DefaultView.RowFilter = "SQLFieldName='" + sLinkField + "'";
						if (dtMap.DefaultView.Count > 0)
						{
							if (!Convert.IsDBNull(dtMap.DefaultView[0]["LinkKeyField"]))
							{
								sLinkKeyField = Convert.ToString(dtMap.DefaultView[0]["LinkKeyField"]);
							}
							else
							{
								sLinkKeyField = "GID_ID";
							}
							//Get dataset of existing link key values
							string sSQL = "SELECT " + sLinkKeyField + ", SYS_NAME FROM " + sLinkedFile;
							dtLinkedKeys = RunSQLSelect(sSQL);
							//Create Add table for linked key values
							//   need to have dt with column name and type same as dtLinkedKeys
							if (dtLinkedKeys != null)
							{
								dtLinkedKeysAdd = dtLinkedKeys.Clone();
							}
						}
					}

					iError = 0;
					sError = "";
					for (int iRow = 0; iRow < par_dtSourceUnique.Rows.Count; iRow++)
					{
						sExpression = "";
						sExpressionValue = "";

						try
						{
							//If using Normalized keys
							if (dtTransformed.Columns[iCol].ColumnName == "TXT_IDNormalized")
							{
								sExpressionValue = Convert.ToString(par_dtSourceUnique.Rows[iRow]["TXT_IDNormalized"]);
								dtTransformed.Rows[iRow][dtTransformed.Columns[iCol].ColumnName] = sExpressionValue;
							}
							else
							{

								//For this column, iCol, what is sExpression??
								//If field is ImportJobID then fill with constant value
								if (dtTransformed.Columns[iCol].ColumnName == "TXT_ImpJobID")
								{
									sExpressionValue = sIMPJobID;
									dtTransformed.Rows[iRow][dtTransformed.Columns[iCol].ColumnName] = sExpressionValue;

								}
								else
								{
									//Special case for UniqueKey column (not part of source or dest schema)
									if (dtTransformed.Columns[iCol].ColumnName == "UniqueKey")
									{
										sExpression = "(%UniqueKey%)";
									}
									else
									{
										sExpression = colDestFieldExpression[dtTransformed.Columns[iCol].ColumnName].ToString();
									}

									//Translate sExpression into string
									//debug
									if (dtTransformed.Columns[iCol].ColumnName.Contains("UniqueKey"))
									{
										string s = "";
									}

									sExpressionValue = GetFieldValFromDataTable(ref par_dtSourceUnique, sExpression, iRow, par_sSQLTableName, dtTransformed.Columns[iCol].ColumnName, 480, "", par_bCreateMissingLSTValues);

									//Handle nulls for some data types
									if (sExpressionValue == "" || sExpressionValue == null)
									{
										if (dtTransformed.Columns[iCol].ColumnName.Substring(0, 3) != "TXT") //debug, not needed
										{
											switch (dtTransformed.Columns[iCol].ColumnName.Substring(0, 3))
											{
												case "CHK":
												case "CMB":
												case "CUR":
												case "DR_":
												case "INT":
												case "LI_":
												case "BI_":
												case "MLS":
												case "SEL":
												case "SI_":
												case "SR_":
													sExpressionValue = "0";
													break;
												case "DTE":
												case "DTT":
												case "TME":
													sExpressionValue = "1753-01-02 23:59:59.000";
													break;
											}
										}
									}
									else
									{
										if (sExpressionValue.Length > 4)
										{
											if (sExpressionValue.Substring(0, 5) == "Error")
											{
												iError += 1;
												if (iError < 20)
												{
													//PJ ok to remove this? 1/28/09
													//LogLine(1, sProc & vbCrLf & "EvalExpression returned '" & sExpressionValue & "'", "Warning")
												}
											}
										}

										sExpressionValue = goTR.PrepareForSQL(sExpressionValue);
										dtTransformed.Rows[iRow][dtTransformed.Columns[iCol].ColumnName] = sExpressionValue; //EvalExpression(sExpression, iRow)

										if (dtTransformed.Columns[iCol].ColumnName.Contains("_LNK_"))
										{
											//LNK_: Does record exist in linked table? If not, create it
											//   filter dtLinkedKeys
											if (par_bCreateMissingLinkedRecords)
											{
												//Parse sExpression into array
												string[] aExpressionValue = Microsoft.VisualBasic.Strings.Split(sExpressionValue, "\r\n");
												for (int i = 0; i <= aExpressionValue.GetUpperBound(0); i++)
												{
													dtLinkedKeys.DefaultView.RowFilter = sLinkKeyField + "='" + aExpressionValue[i] + "'";
													if (dtLinkedKeys.DefaultView.Count == 0)
													{
														//Add value to dtLinkedKeysAdd if it doesn't exist in add table
														dtLinkedKeysAdd.DefaultView.RowFilter = sLinkKeyField + "='" + aExpressionValue[i] + "'";
														if (dtLinkedKeysAdd.DefaultView.Count == 0)
														{
															DataRow row = dtLinkedKeysAdd.NewRow();
															row[sLinkKeyField] = aExpressionValue[i];
															row["SYS_NAME"] = aExpressionValue[i];
															dtLinkedKeysAdd.Rows.Add(row);
															bKeysAdded = true;
															LogLine("1", "Added " + sLinkKeyField + "='" + aExpressionValue[i] + "' to table " + sLinkedFile, "OK");
														}
													}
												}
											}
										}
									}
								}
							}


						}
						catch (Exception ex)
						{
							//Check if 'Ignore Errors' is checked. If so, clear the value or truncate if txt or mmo
							if (par_bIgnoreTransformErrors)
							{
								switch (dtTransformed.Columns[iCol].ColumnName.Substring(0, 3))
								{
									case "CHK":
									case "CMB":
									case "CUR":
									case "DR_":
									case "INT":
									case "LI_":
									case "BI_":
									case "LST":
									case "SEL":
									case "SI_":
									case "SR_":
									case "MLS":
										sExpressionValue = "0";
										break;
									case "DTE":
									case "DTT":
									case "TME":
										//sExpressionValue = ""
										sExpressionValue = "1753-01-02 23:59:59.000";
										break;
									case "TXT":
									case "MMO":
									case "MMR":
									case "SYS":
									case "TEL":
										if (sExpressionValue.Length > dtTransformed.Columns[iCol].MaxLength)
										{
											LogLine("1", sProc + "VALIDATE__Truncate " + dtTransformed.Columns[iCol].ColumnName + ", Value='" + sExpressionValue + "'", "Warning");
											sExpressionValue = sExpressionValue.Substring(0, dtTransformed.Columns[iCol].MaxLength);
											//'5/16/11 Truncate - was getting error with maxlength on UniqueKey field
											//If Len(sExpressionValue) > dtTransformed.Columns(iCol).MaxLength Then
											//    sExpressionValue = Left(sExpressionValue, dtTransformed.Columns(iCol).MaxLength)
											//End If
										}
										break;
								}
								dtTransformed.Rows[iRow][dtTransformed.Columns[iCol].ColumnName] = sExpressionValue; //EvalExpression(sExpression, iRow)
							}
							else
							{
								LogLine("1", sProc + "VALIDATE__" + dtTransformed.Columns[iCol].ColumnName + ", Value='" + sExpressionValue + "'", "Warning");
								//Return Nothing
							}
						}
					} //Row

					//Upload dtLinkedKeysAdd

					if (bKeysAdded)
					{
						SQLBulkAdd(sLinkedFile, dtLinkedKeysAdd, sIMPJobID);
						bKeysAdded = false;
					}
				} //Column

				dtTransformedNew = dtTransformed.Clone();
				dtTransformedUpd = dtTransformed.Clone();

				//Save dtTransform to session
				goP.SetVar("ImportSQL_dtTransformed", dtTransformed);
				goP.SetVar("ImportSQL_dtTransformedNew", dtTransformedNew);
				goP.SetVar("ImportSQL_dtTransformedUpd", dtTransformedUpd);

				LogLine("0", "Source data validation complete", "OK");

				return true;

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
				return false;

			}

		}

		public bool TransformTableSplit(string par_sSQLTableName, bool par_bUseNormalizedKey, string par_iKeyType, string par_sIMPJobID, bool bDupeCheck = true, string par_sSourceKey = "", string par_sDestKey = "")
		{

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			//^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
			//At this point, dtTransformed is created.
			//Now put update records into dtTransformedUpd and delete from dtTransformed
			//dtTrnasformedUpd will be inserted row by row into SQL db

			//This part modified to handle duplicate IDs in the source data
			//Source IDs need to be added to the dtKey table as duplicates are being evaluated
			// A dupe source ID should result in the first instance being added to dtTransformedNew
			//and duplicate instances being addded to dtTransformedUpd. << Still accurate??

			//PARAMS:
			//   par_sSQLTableName:  Selltis File/Table name
			//   par_bUseNormalizedKey:  True if using normalized key. For back compat
			//   iKeyType:   0 = Source field mapped to field in SQL table
			//               1 = Source ID is in TN table in SQL
			//               2 = Dynamic keys provided in par_sSourceKey and par_sDestKey (new functionality V2010)
			//               (This param for back compat.)
			//   par_sIMPJobID:  Import ID
			//   par_sSourceKey: String containing source key field(s) and properties
			//   par_sDestKey: String containing Selltis key field(s) and properties

			clTransform goTr = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			clProject goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];

			DataTable dtMap = null;
			DataTable dtTransformed = (DataTable)goP.GetVar("ImportSQL_dtTransformed");
			DataTable dtTransformedUpd = (DataTable)goP.GetVar("ImportSQL_dtTransformedUpd");
			DataTable dtTransformedNew = (DataTable)goP.GetVar("ImportSQL_dtTransformedNew");
			DataTable dtKeyDest = null;
			//Dim sDestKey As String = ""
			string sDestKeyColumnName = "";
			string sSourceKeyColumnName = "";
			string sKeyField = "";
			DataView dvKey = null;
			DataView dvUpd = null;
			DataRow dr = null;
			int iCount = 0;
			string sFilter = "";
			string sFilterPart = "";
			string sNormValue = "";
			int iUseExtID = 0;
			bool bKeysExist = false;
			int iKeyCount = -1;


			try
			{

				//no dupechecking
				if (!bDupeCheck)
				{
					dtTransformedNew = dtTransformed;
				}
				else
				{

					dtMap = MapTable;

					//Check tables stored in session
					if (dtTransformed == null || dtTransformedNew == null || dtTransformedUpd == null)
					{
						LogLine("0", sProc + ": Unable to read source data", "Failed");
						return false;
					}
					else
					{
						//Add UniqueKey column to each table
						if (!dtTransformed.Columns.Contains("UniqueKey"))
						{
							dtTransformed.Columns.Add("UniqueKey");
						}
						if (!dtTransformedNew.Columns.Contains("UniqueKey"))
						{
							dtTransformedNew.Columns.Add("UniqueKey");
						}
						if (!dtTransformedUpd.Columns.Contains("UniqueKey"))
						{
							dtTransformedUpd.Columns.Add("UniqueKey");
						}
					}
					if (dtMap == null)
					{
						LogLine("0", sProc + ": Unable to read map data", "Failed");
						return false;
					}

					switch (Convert.ToInt32(par_iKeyType))
					{

						case 0:
						{
							//-- Duplicate records identified by specific field(s) in SQL table
							//-- This is iKeyType = 0
							//-- Get Primary key columns from SQL. Key fields are indicated by 'Key' column in dtMap (true or null)
							//-- Supports: multiple, mapped fields. Single source field per destination field. No functions
							//-- V2: support for multiple keys  using one row in dtMap with field code expression, '(%TXT_NameFirst%) (%TXT_NameLast%)'

							string[] aKeys = null;

							for (int i = 0; i < dtMap.Rows.Count; i++)
							{
								if (!Convert.IsDBNull(dtMap.Rows[i]["Key"]))
								{
									if (Convert.ToBoolean(dtMap.Rows[i]["Key"]) == true)
									{
										sDestKeyColumnName = Convert.ToString(dtMap.Rows[i]["SQLFieldName"]);
										//V2: Add source key:
										sSourceKeyColumnName = Convert.ToString(dtMap.Rows[i]["Expression"]);


										iKeyCount += 1;
										Array.Resize(ref aKeys, iKeyCount + 1);
										aKeys[iKeyCount] = sDestKeyColumnName;
										bKeysExist = true;
										if (sDestKeyColumnName == "TXT_ExternalID" || sDestKeyColumnName == "TXT_ExternalID")
										{
											iUseExtID += 1; //If this = 2 then we know to use TN table with external IDs
										}
										//Temporary while multiple key system is worked out
										//V2: sDestKeycolumnName is an expression which may contain multiple field codes
										sKeyField = sDestKeyColumnName;
									}
								}
							}

							//Now have SQL key column names in array aKeys

							//If sKeyField <> "" Then
							if (bKeysExist)
							{

								//Get table of key values if sKeyField is passed
								//New - handle TN table
								if (iUseExtID != 2)
								{
									dtKeyDest = LoadRecordIDs(par_sSQLTableName, 0, aKeys);
								}
								else
								{
									//iUseExtID = 2, which means key fields are TXT_ExternalID and TXT_ExternalSource
									dtKeyDest = LoadRecordIDs(par_sSQLTableName, 1, aKeys);
								}

								if (dtKeyDest == null)
								{
									LogLine("0", "Duplicate checking: Unable to load key table.", "Failed");
									return false;
								}

								//V2: Add UniqueID column to dtKey and fill based on SQL Key expression (%TXT_NameFirst%) (%TXT_NameLast%)
								if (dtKeyDest.Columns.Contains("UniqueKey"))
								{
									dtKeyDest.Columns.Remove("UniqueKey");
								}
								else
								{

									dtKeyDest.Columns.Add("UniqueKey");
									//key expression is sKeyField
									foreach (DataRow drKey in dtKeyDest.Rows)
									{
										string sKeyExpression = sKeyField;
										int iStart = 1;
										int iEnd = 1;
										string sFieldCode = "";
										string sFieldName = "";
										string sFieldVal = "";

										//Compat with applet, single field may not have (% and %):
										if (!sKeyExpression.Contains("(%") && !sKeyExpression.Contains("%)"))
										{
											sKeyExpression = "(%" + sKeyExpression + "%)";
										}

										//-------
										while (iStart > 0)
										{
											iStart = sKeyExpression.IndexOf("(%") + 1;
											if (iStart > 0)
											{
												iEnd = sKeyExpression.IndexOf("%)") + 1 + 1;
												if (iEnd > 0)
												{
													sFieldCode = sKeyExpression.Substring(iStart - 1, iEnd - iStart + 1);
													//Replace
													//sFieldCode is the column in dtKey
													sFieldName = sFieldCode.Replace("(%", "");
													sFieldName = sFieldName.Replace("%)", "");
													sFieldVal = Convert.ToString(drKey[sFieldName]);
													sKeyExpression = sKeyExpression.Replace(sFieldCode, sFieldVal);
												}
											}
										}
										drKey["UniqueKey"] = sKeyExpression;
									}
								}

								//2/6 STOP HERE
								//Next: if using TN, dttransupd needs GID_ID of target record, which is GID_InternalID
								//so, rename GID_InternalID to GID_ID??
								//then check if this ID actually exists in target table

								//Replace dbNull with empty string in dtKey (SQL data). Needed for string comparison?
								foreach (DataRow row in dtKeyDest.Rows)
								{
									foreach (DataColumn col in dtKeyDest.Columns)
									{
										if (Convert.IsDBNull(row[col]))
										{
											row[col] = "";
										}
									}
								}

								//Add IDNormalized column to SQL key table and set aKeys. Create normalized values
								if (par_bUseNormalizedKey)
								{
									dtKeyDest.Columns.Add("TXT_IDNormalized");
									aKeys = new string[1];
									aKeys[0] = "TXT_IDNormalized";
									//create TXT_IDNormalized value by looping thru key table rows
									foreach (DataRow row in dtKeyDest.Rows)
									{
										sNormValue = "";
										foreach (DataColumn col in dtKeyDest.Columns)
										{
											if (col.ColumnName != "TXT_IDNormalized" && col.ColumnName != "GID_ID")
											{
												sNormValue += NormalizeValue(row[col].ToString(), par_sSQLTableName);
											}
										}
										row["TXT_IDNormalized"] = sNormValue;
									}
								}

								//set uniqueID in source 
								EvalUniqueSourceID(ref dtTransformed, ref dtMap);

								//Loop thru dtTransformed and move rows to either Add or Edit dt's
								while (dtTransformed.Rows.Count > 0)
								{
									//Always working with row 0
									// Get key for dtTransformed row
									dr = dtTransformed.Rows[0];
									sFilter = Convert.ToString(dr["UniqueKey"]);
									sFilter = "UniqueKey='" + sFilter + "'";

									//Does this key exist in destination keys?
									dtKeyDest.DefaultView.RowFilter = sFilter;
									if (dtKeyDest.DefaultView.Count > 0)
									{
										//This is an edit/update
										//If dvKey.Count > 0 Then
										//Move this row to dtTransformedUpd, checking first if key exists in dtTransformedUpd
										//dtTransformedUpd.DefaultView.RowFilter = sKeyField & " = '" & goTr.PrepareForSQL(sDestKey) & "'"
										dtTransformedUpd.DefaultView.RowFilter = sFilter;
										dvUpd = dtTransformedUpd.DefaultView;
										if (dvUpd.Count == 0)
										{
											//key doesn't exist
											//Change key value to GID_ID
											dr["UniqueKey"] = dtKeyDest.DefaultView[0]["GID_ID"];
											//Import
											dtTransformedUpd.ImportRow(dr);
										}
										else
										{
											//do nothing. don't want to add duplicate
										}

									}
									else
									{
										dtTransformedNew.ImportRow(dr);
										//Add key to dtKey. No need to check if value already exists in dtKey b/c it will be caught in viewfilter above(?)
										//DO NOT want to do this now. >>>Source table has been deduped!!
										//USE GID_ID in dtKey now and update may be faster in SQLEdit
										//drKey = dtKey.NewRow
										//drKey.Item(0) = dr.Item(sKeyField)    'first col in dtKey is the key value
										//dtKey.Rows.Add(drKey)

									}
									dtTransformed.Rows.Remove(dr);

									//Else       'Old way
									//    'key value in source data is null, so do not import
									//    dtTransformed.Rows.Remove(dr)
									//End If
									//Next
								}

								//Write dt's to session var's
							}
							else
							{
								dtTransformedNew = dtTransformed;
							}
							break;

						}
						case 1:
						{

							//-- Duplicate records identified by external ID and source in TN table
							//-- This is iKeyType = 1
							//-- Get filtered TN records from SQL.
							//LogLine(0, "Begin duplicate checking", "")

							dtKeyDest = LoadRecordIDs(par_sSQLTableName, 1, null);
							if (dtKeyDest == null)
							{
								LogLine("0", "Duplicate checking: Unable to load key table.", "Failed");
								return false;
							}

							//Iterate through source data rows
							while (dtTransformed.Rows.Count > 0)
							{
								dr = dtTransformed.Rows[iCount]; //iCount is always 0 b/c always working with first row. we delete all rows

								//aKeys contains TXT_ExternalSource and TXT_ExternalID
								string[] aKeys = new string[2];
								aKeys[0] = "TXT_ExternalSource";

								sFilter = "";
								sFilterPart = "";
								for (int i = 0; i <= aKeys.GetUpperBound(0); i++)
								{
									//aSourceKeyVal(i) = dr.Item(aKeys(i))   'needed?
									if (Convert.IsDBNull(dr[aKeys[i]]))
									{
										dr[aKeys[i]] = "";
									}
									sFilterPart = aKeys[i] + " = '" + goTr.PrepareForSQL(dr[aKeys[i]].ToString()) + "'";
									if (sFilter == "")
									{
										sFilter = sFilterPart;
									}
									else
									{
										sFilter += " AND " + sFilterPart;
									}
								}
								//sDestKey = dr.Item(sKeyField)  'Old way with single field

								//If Not IsDBNull(dr.Item(sKeyField)) Then   'Old way

								//dtKey.DefaultView.RowFilter = sKeyField & " = '" & goTr.PrepareForSQL(sDestKey) & "'"
								dtKeyDest.DefaultView.RowFilter = sFilter;
								dvKey = dtKeyDest.DefaultView;

							}

							if (dvKey != null)
							{
								if (dvKey.Count > 0)
								{
									//Move this row to dtTransformedUpd, checking first if key exists in dtTransformedUpd
									//dtTransformedUpd.DefaultView.RowFilter = sKeyField & " = '" & goTr.PrepareForSQL(sDestKey) & "'"
									dtTransformedUpd.DefaultView.RowFilter = sFilter;
									dvUpd = dtTransformedUpd.DefaultView;
									if (dvUpd.Count == 0)
									{
										//key doesn't exist
										dtTransformedUpd.ImportRow(dr);
									}
									else
									{
										//do nothing. don't want to add duplicate
									}

								}
								else
								{
									dtTransformedNew.ImportRow(dr);

								}
							}

							dtTransformed.Rows.Remove(dr);
							break;

						}
						case 2:
						{
							//-- Duplicate records identified by Key text fields in new import utility
							//-- Key fields can contain field codes and properties in style of GetLinePart
							DataTable dtKeys = null;
							string sKeys = "";
							string[] aKeys = null;
							string sResult = "";
							int iStart = 1;
							string sFieldCode = "";
							string sFieldCodeName = "";
							//Dim iFieldCode As Integer = 0

							if (sKeySource != "" && sKeyDest != "")
							{
								//LogLine(0, "Begin duplicate checking", "")
								//Parse dest key string for Selltis field names
								dtKeys = CreateKeyDatatable(sKeyDest);
								foreach (DataRow row in dtKeys.Rows)
								{
									sKeys += Convert.ToString(row["FLD"].ToString() + ",");
								}
								if (sKeys.EndsWith(","))
								{
									sKeys.TrimEnd(',');
								}
								aKeys = sKeys.Split(',');

								//Get table of key values
								dtKeyDest = LoadRecordIDs(par_sSQLTableName, 2, aKeys);
								if (dtKeyDest == null)
								{
									LogLine("0", "Duplicate checking: Unable to load key table.", "Failed");
									return false;
								}

								//Add UniqueID column to dtKey and fill based on SQL Key expression (%TXT_NameFirst%) (%TXT_NameLast%)
								if (!dtKeyDest.Columns.Contains("UniqueKey"))
								{
									dtKeyDest.Columns.Add("UniqueKey");
								}

								//2/6 STOP HERE
								//Next: if using TN, dttransupd needs GID_ID of target record, which is GID_InternalID
								//so, rename GID_InternalID to GID_ID??
								//then check if this ID actually exists in target table

								//Set UniqueKey in dtKeyDest
								for (int i = 0; i < dtKeyDest.Rows.Count; i++)
								{
									dtKeyDest.Rows[i]["UniqueKey"] = GetFieldValFromDataTable(ref dtKeyDest, sKeyDest, i);
								}

								//Replace dbNull with empty string in dtKey (SQL data). Needed for string comparison?
								foreach (DataRow row in dtKeyDest.Rows)
								{
									foreach (DataColumn col in dtKeyDest.Columns)
									{
										if (Convert.IsDBNull(row[col]))
										{
											row[col] = "";
										}
									}
								}

								//set uniqueID in source *****   HERE 4/7  LOOK AT FUNCTION BELOW:::   *****
								//Is the unique key value filled in dtTransformed (source data)?
								//EvalUniqueSourceID(dtTransformed, dtMap)



								//Loop thru dtTransformed and move rows to either Add or Edit dt's
								while (dtTransformed.Rows.Count > 0)
								{
									//dtKey.DefaultView.RowFilter = sKeyField & " = '" & goTr.PrepareForSQL(sDestKey) & "'"
									//dtKeyDest.DefaultView.RowFilter = sFilter
									//dvKey = dtKeyDest.DefaultView

									//Always working with row 0
									// Get key for dtTransformed row
									dr = dtTransformed.Rows[0];
									if (Convert.IsDBNull(dr["UniqueKey"]))
									{
										dr["UniqueKey"] = "";
									}

									sFilter = goTr.PrepareForSQL(Convert.ToString(dr["UniqueKey"]));
									sFilter = "UniqueKey='" + sFilter + "'";

									//Does this key exist in destination keys?
									dtKeyDest.DefaultView.RowFilter = sFilter;
									if (dtKeyDest.DefaultView.Count > 0)
									{
										//This is an edit/update
										//If dvKey.Count > 0 Then
										//Move this row to dtTransformedUpd, checking first if key exists in dtTransformedUpd
										//dtTransformedUpd.DefaultView.RowFilter = sKeyField & " = '" & goTr.PrepareForSQL(sDestKey) & "'"
										dtTransformedUpd.DefaultView.RowFilter = sFilter;
										dvUpd = dtTransformedUpd.DefaultView;
										if (dvUpd.Count == 0)
										{
											//key doesn't exist
											//Change key value to GID_ID
											dr["UniqueKey"] = dtKeyDest.DefaultView[0]["GID_ID"];
											//Import
											dtTransformedUpd.ImportRow(dr);
										}
										else
										{
											//do nothing. don't want to add duplicate
										}

									}
									else
									{
										dtTransformedNew.ImportRow(dr);
										//Add key to dtKey. No need to check if value already exists in dtKey b/c it will be caught in viewfilter above(?)
										//DO NOT want to do this now. >>>Source table has been deduped!!
										//USE GID_ID in dtKey now and update may be faster in SQLEdit
										//drKey = dtKey.NewRow
										//drKey.Item(0) = dr.Item(sKeyField)    'first col in dtKey is the key value
										//dtKey.Rows.Add(drKey)

									}
									dtTransformed.Rows.Remove(dr);

									//Else       'Old way
									//    'key value in source data is null, so do not import
									//    dtTransformed.Rows.Remove(dr)
									//End If
									//Next
								}

							}
							break;

							//Write dt's to session var's
							//Else
							//dtTransformedNew = dtTransformed
							//End If


						}
						default:
						{
						break;

						}
					}

				}

				//Prepare tables for import
				//dtTransformedNew: remove UniqueKey column
				if (dtTransformedNew.Columns.Contains("UniqueKey"))
				{
					dtTransformedNew.Columns.Remove("UniqueKey");
				}
				//dtTransformedUpd: rename UniqueKey to GID_ID
				if (!dtTransformedUpd.Columns.Contains("GID_ID"))
				{
					if (dtTransformedUpd.Columns.Contains("UniqueKey"))
					{
						dtTransformedUpd.Columns["UniqueKey"].ColumnName = "GID_ID";
					}
				}

				//Update session dt's
				goP.SetVar("ImportSQL_dtTransformed", dtTransformed);
				goP.SetVar("ImportSQL_dtTransformedNew", dtTransformedNew);
				goP.SetVar("ImportSQL_dtTransformedUpd", dtTransformedUpd);

				LogLine("0", "Duplicate check resulted in " + dtTransformedNew.Rows.Count + " adds, and " + dtTransformedUpd.Rows.Count + " updates", "OK");

				return true;

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
				return false;

			}

		}

		private string GetFieldValFromDataTable(ref DataTable par_dt, string par_sExpression, int par_iRow, string par_sSelltisFile = "", string par_sDestFieldName = "", int par_iOffsetMinutes = 0, string par_sNormalizeCOorCN = "", bool par_bCreateMissingLSTValues = false)
		{

			//PURPOSE:
			//		Evaluate field codes. Like a GetFieldVal for source and destination data tables
			//       Values are read from the fields in iRow, and transformed into Selltis friendly data
			//CALLED FROM:
			//       DedupeSourceData
			//PARAMETERS:
			//       par_dt:                 Datatable containing the value to search
			//       par_sExpression:        String expression to evaluate. Usually contains field codes
			//       par_iRow:               Row to read from dt
			//       par_sSelltisFile:       Selltis File name
			//       par_sDestFieldName:     Selltis field name
			//       par_iOffsetMinutes:     Number of minutes to offset datetime values
			//       par_sNormalizeCOorCN:   'CO' or 'CN' if key is to be normalized for CO or CN data
			//       par_bCreateMissingLSTValues:    True to create LST metadata lines for values that do not exist. No undo
			//RETURNS:
			//		String
			//AUTHOR: PJ

			//PJ: Some known issues with '%' appearing in source data. Will cause evaluation of one value to fail, but not critical

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			string sExpression = par_sExpression;
			int iStart = 1;
			int iEnd = 1;
			string sFieldCode = "";
			string sFieldVal = "";
			string[] aFieldCode = null;
			string sFieldCodeName = null;
			string sLength = "";
			string sNormalize = "";
			string sReturnValue = null;

			int iResult = -1;
			string sPrefix = par_sDestFieldName.Substring(0, 4);
			string sSourceVal = "";
			string sResult = "";

			try
			{

				//Get instances of (% followed by %)
				while (iStart > 0)
				{
					iStart = sExpression.IndexOf("(%") + 1;
					if (iStart > 0)
					{
						iEnd = sExpression.IndexOf("%)") + 1 + 1;
						if (iEnd > 0)
						{
							sFieldCode = sExpression.Substring(iStart - 1, iEnd - iStart + 1);
							//sFieldCode = Replace(sFieldCode, "(%", "")
							//sFieldCode = Replace(sFieldCode, "%)", "")
							//V2: Field codes have properties FLD_, LEN_, ??
							aFieldCode = sFieldCode.Split('|');
							//For i As Integer = 0 To aFieldCode.GetUpperBound(0)
							if (aFieldCode[0].ToUpper().Contains("FLD="))
							{
								sFieldCodeName = aFieldCode[0].Replace("FLD=", "");
							}
							else
							{
								sFieldCodeName = sFieldCode; //This for backward compat. NO! for other values (%Zip%)
								//Exit For
							}
							sFieldCodeName = RemoveFieldCodeTags(sFieldCodeName);
							if (aFieldCode.GetUpperBound(0) > 0)
							{
								if (aFieldCode[1].ToUpper().Contains("LEN="))
								{
									sLength = aFieldCode[1].Replace("LEN=", "");
									sLength = RemoveFieldCodeTags(sLength);
								}
								if (aFieldCode.GetUpperBound(0) > 1)
								{
									if (aFieldCode[2].ToUpper().Contains("NOR="))
									{
										sNormalize = aFieldCode[2].Replace("NOR=", "");
										sNormalize = RemoveFieldCodeTags(sNormalize);
									}
								}
							}

							//Next

							if (!Convert.IsDBNull(par_dt.Rows[par_iRow][sFieldCodeName]))
							{
								sFieldVal = Convert.ToString(par_dt.Rows[par_iRow][sFieldCodeName]);
							}
							else
							{
								sFieldVal = "";
							}

							//debug
							if (sFieldVal.Contains("OLIN CHEMICAL"))
							{
								Debug.WriteLine("Here");
							}
							//

							if (sNormalize.ToUpper() == "1")
							{
								sFieldVal = NormalizeValue(sFieldVal, par_sNormalizeCOorCN);
							}

							if (NumericHelper.IsNumeric(sLength))
							{
								sFieldVal = goTR.TruncateText(sFieldVal, Convert.ToInt32(goTR.StringToNum(sLength)), false);
							}

							//remove leading % from value, so as to not confuse when Propercase(%Olin Equip)
							while (sFieldVal.IndexOf("%") + 1 == 1)
							{
								sFieldVal = sFieldVal.Substring(1);
							}

							//sExpression = Replace(sExpression, sFieldCode, GetSourceValue(sSQLTableName, dtSource, sFieldCode, iRow, sDestFieldName, iOffsetMinutes))    '<<enuf to keep rows aligned properly??
							sExpression = sExpression.Replace(sFieldCode, sFieldVal);
							//
						}
					}
				}

				//!!Does this take care of source and dest values?
				//sExpression = Replace(sExpression, vbCrLf, " ") 'replace VbCrLf
				sExpression = sExpression.Trim(' '); //remove trailing spaces
				if (sExpression.EndsWith("\r\n"))
				{
					sExpression = sExpression.Substring(0, sExpression.Length - 2);
				}

				sReturnValue = sExpression;

				//debug
				if (sReturnValue.Contains("OLIN CHEMICAL"))
				{
					Debug.WriteLine("Here");
				}

				//--- Selltis-ize the data for Sellts field types ---
				switch (sPrefix)
				{
					case "MLS_":
						//Lookup: All lst values for this table are in dtLSTMetadata
						//iResult = GetLSTValue(sFieldCode, dtSource.Rows(iRow).Item(sFieldCode))
						iResult = GetLSTValue(par_sSelltisFile, par_sDestFieldName, sReturnValue, par_bCreateMissingLSTValues);
						if (iResult == -1)
						{
							return "Error: Cannot find value for " + par_sExpression + ": " + Convert.ToString(par_dt.Rows[par_iRow][sFieldCode]);
						}
						else
						{
							sResult = iResult.ToString();
						}
						break;

					case "CHK_":
						switch (sReturnValue.ToUpper())
						{
							case "TRUE":
							case "YES":
							case "CHECKED":
							case "1":
								sResult = "1";
								break;
							case "FALSE":
							case "NO":
							case "UNCHECKED":
							case "0":
								sResult = "0";
								break;
						}
						break;
					case "CUR_":
						sReturnValue = sReturnValue.Replace("$", "");
						sReturnValue = sReturnValue.Replace(",", "");
						//PJ 10/11/12: Euro symbol replacement
						sReturnValue = sReturnValue.Replace(Microsoft.VisualBasic.Strings.Chr(128).ToString(), "");
						sResult = sReturnValue;
						break;

					case "DTT_":
						if (sReturnValue == "")
						{
							sResult = "1753-01-02 23:59:59.000";
						}
						else
						{
							if (sReturnValue.IndexOf("-") + 1 == 0 && (sReturnValue.IndexOf("/") + 1) == 0)
							{
								if (sReturnValue.Length == 8)
								{
									//Add hyphen between yyyy-mm-dd
									sResult = sReturnValue.Substring(0, 4) + "-" + sReturnValue.Substring(4, 2) + "-" + sReturnValue.Substring(6, 2);
								}
							}
							else
							{
								sResult = sReturnValue;
							}
							//GMT offset
							if (par_iOffsetMinutes != 0)
							{
								DateTime myDate = DateTime.Parse(sReturnValue);
								string myTime = myDate.Hour.ToString();
								sResult = myDate.AddMinutes(par_iOffsetMinutes).ToString();

							}
						}
						break;

					default:
						sResult = sReturnValue;
						break;
				}

				//Need to wrap leading and trailing % with ''. Othewise could result in 
				//a parsing error if the returned value is part of a function, such as 
				//Propercase(%ABC CHEMICAL). Code will see this as an invalid field code.

				if (sResult != "")
				{
					while (sResult.Substring(0, 1) == "%")
					{
						sResult = "'" + sResult;
					}
					while (sResult.Substring(sResult.Length - 1) == "%")
					{
						sResult = sResult + "'";
					}
				}


				//--- Run Supported Functions ---
				int iFunctionStart = 0; //InStr(sExpression, "Propercase(")
				string sFunction = ""; //portion of sExpression to replace
				bool bFuncEndFound = false;
				string sParam = "";
				string sParamProper = "";
				int iParamStart = 0;
				int iParamEnd = 0;
				bool bParamEndFound = false;
				int iOpenParenCt = 0;
				int iCloseParenCt = 0;
				int iCharPos = 0;
				string sChar = null;

				//Functions - Propercase
				do
				{
					iFunctionStart = sResult.IndexOf("Propercase(") + 1;
					if (iFunctionStart == 0)
					{
						break;
					}
					//If iFunctionStart > 0 Then      'ifunctionStart is first char of string to replace with returned value
					//remove everything to the left of function
					sFunction = sResult.Substring(iFunctionStart - 1); //, Len(sResult) - iFunctionStart + 1) 'everything to right

					//On what char does sfunction end - closing paren
					//iOpenParenCt = 1
					iCharPos = 0;
					iOpenParenCt = 0;
					iCloseParenCt = 0;
					bFuncEndFound = false;
					while (bFuncEndFound == false)
					{
						iCharPos += 1; //increment char position
						sChar = sFunction.Substring(iCharPos - 1, 1); //get next char
						if (sChar == ")")
						{
							iCloseParenCt += 1;
							if (iCloseParenCt == iOpenParenCt)
							{
								bFuncEndFound = true;
								iParamEnd = iCharPos;
							}
						}
						else if (sChar == "(")
						{
							iOpenParenCt += 1;
							if (iOpenParenCt == 1)
							{
								iParamStart = iCharPos;
							}
						}
						if (iCharPos == sFunction.Length)
						{
							break;
						}
					}

					if (iParamEnd > iParamStart)
					{
						sFunction = sFunction.Substring(0, iParamEnd);
						sParam = sFunction.Substring(iParamStart, iParamEnd - iParamStart - 1);
						sParamProper = Microsoft.VisualBasic.Strings.StrConv(sParam, Microsoft.VisualBasic.VbStrConv.ProperCase);

						sResult = sResult.Replace(sFunction, sParamProper);
					}
					else
					{
						//can't parse, possibly because field value contains unpaired '(' or ')'
						sResult = sResult.Replace("Propercase(", "");
					}

				} while (true);

				//Functions - Format PHone
				string sNumericPortion = "";
				string sExtension = "";
				string sPhoneFormatted = "";

				do
				{
					iFunctionStart = sResult.IndexOf("FormatPhone(") + 1;
					if (iFunctionStart == 0)
					{
						break;
					}
					//If iFunctionStart > 0 Then      'ifunctionStart is first char of string to replace with returned value
					//remove everything to the left of function
					sFunction = sResult.Substring(iFunctionStart - 1); //, Len(sResult) - iFunctionStart + 1) 'everything to right

					//On what char does sfunction end - closing paren
					//iOpenParenCt = 1
					iCharPos = 0;
					iOpenParenCt = 0;
					iCloseParenCt = 0;
					bFuncEndFound = false;

					//Do While bFuncEndFound = False
					do
					{
						iCharPos += 1; //increment char position
						sChar = sFunction.Substring(iCharPos - 1, 1); //get next char
						if (sChar == ")")
						{
							iCloseParenCt += 1;
							if (iCloseParenCt == iOpenParenCt)
							{
								bFuncEndFound = true;
								iParamEnd = iCharPos;
							}
						}
						else if (sChar == "(")
						{
							iOpenParenCt += 1;
							if (iOpenParenCt == 1)
							{
								iParamStart = iCharPos;
							}
						}
						if (iCharPos == sFunction.Length)
						{
							break;
						}
					} while (true);

					if (iParamEnd > iParamStart)
					{
						sFunction = sFunction.Substring(0, iParamEnd);
						sParam = sFunction.Substring(iParamStart, iParamEnd - iParamStart - 1);
						//sParamProper = StrConv(sParam, VbStrConv.ProperCase)
						//format phone which is sParam
						//Strip ( ) - . , 
						sParam = sParam.Replace("(", "");
						sParam = sParam.Replace(")", "");
						sParam = sParam.Replace("-", "");
						sParam = sParam.Replace(".", "");
						sParam = sParam.Replace(",", "");
						sParam = sParam.Replace(" ", "");

						//If non-numeric found (x, ext, ????) then take number to left as phone #, leave non-numeric intact
						sNumericPortion = sParam;
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of Len(sParam) for every iteration:
						int tempVar = sParam.Length;
						for (int i = 1; i <= tempVar; i++)
						{
							if (!NumericHelper.IsNumeric(sParam.Substring(i - 1, 1)))
							{
								//text found (ext?)
								sNumericPortion = sParam.Substring(0, i - 1);
								sExtension = sParam.Substring(i - 1, sParam.Length - i);
								break;
							}
						}

						//Select case on len and insert -
						switch (sNumericPortion.Length)
						{
							case 7:
								sPhoneFormatted = sNumericPortion.Substring(0, 3) + "-" + sNumericPortion.Substring(3, 4);
								break;
							case 8:
								sPhoneFormatted = sNumericPortion.Substring(0, 1) + "-" + sNumericPortion.Substring(1, 3) + "-" + sNumericPortion.Substring(4, 4);
								break;
							case 10:
								sPhoneFormatted = sNumericPortion.Substring(0, 3) + "-" + sNumericPortion.Substring(3, 3) + "-" + sNumericPortion.Substring(6, 4);
								break;
							case 11:
								sPhoneFormatted = sNumericPortion.Substring(0, 1) + "-" + sNumericPortion.Substring(1, 3) + "-" + sNumericPortion.Substring(4, 3) + "-" + sNumericPortion.Substring(7, 4);
								break;
							default:
								sPhoneFormatted = sNumericPortion;
								break;
						}

						if (sExtension != "")
						{
							sPhoneFormatted = sPhoneFormatted + " " + sExtension;
						}

						sResult = sResult.Replace(sFunction, sPhoneFormatted);
					}
					else
					{
						//can't parse, possibly because field value contains unpaired '(' or ')'
						sResult = sResult.Replace("FormatPhone(", "");
					}

				} while (true);

				//Remove leading/trailing vbcrlf
				while (sResult.Substring(0, 2) == "\r\n")
				{
					sResult = sResult.Substring(2);
				}
				while (sResult.Substring(sResult.Length - 2) == "\r\n")
				{
					sResult = sResult.Substring(0, sResult.Length - 2);
				}

				sResult = sResult.Trim(' ');

				return sResult;

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & ": " & ex.Message, "Failed")
				return null;

			}

		}

		private string EvalExpression(string sSQLTableName, ref DataTable dtSource, string sExpression, int iRow, string sDestFieldName, int iOffsetMinutes = 0)
		{

			//PURPOSE:
			//		Evaluate source field codes. Like a GetFieldVal for source data.   4/6/11: Replace top portion with GetFieldValFromDatatable??
			//       Execute custom functions after field codes are evaluated
			//CALLED FROM:
			//       TransformTable
			//PARAMETERS:
			//		sSQLTableName: This is the name of the destination SQL table
			//       dtSource: Source datatable (has been deduped)
			//       sExpression: expression to be evaluated, usually taken from the map table. 
			//           example: 'Propercase((%TXT_CompanyName%))'
			//       iRow:       row of dtSource to read
			//       sDestFieldName:     Selltis field name this expression is mapped to
			//       iOffsetMinutes:     offset minutes for datetime fields
			//RETURNS:
			//		String
			//AUTHOR: PJ

			//PJ: Some known issues with '%' appearing in source data. Will cause evaluation of one value to fail, but not critical

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			int iStart = 1;
			int iEnd = 1;
			string sFieldCode = "";
			string sFieldVal = "";
			string[] aFieldCode = null;
			string sFieldCodeName = "";

			try
			{

				//Replace source field values with field values from source data
				//Get instances of (% followed by %)
				while (iStart > 0)
				{
					iStart = sExpression.IndexOf("(%") + 1;
					if (iStart > 0)
					{
						iEnd = sExpression.IndexOf("%)") + 1 + 1;
						if (iEnd > 0)
						{
							sFieldCode = sExpression.Substring(iStart - 1, iEnd - iStart + 1);
							//V2: Field codes have properties FLD_, LEN_, ??
							aFieldCode = sFieldCode.Split('|');
							for (int i = 0; i <= aFieldCode.GetUpperBound(0); i++)
							{
								if (aFieldCode[i].ToUpper().Contains("FLD="))
								{
									sFieldCodeName = aFieldCode[i].Replace("FLD=", "");
								}
								else
								{
									sFieldCodeName = sFieldCode;
									//Exit For
								}
							}
							//Replace
							sFieldVal = GetSourceValue(sSQLTableName, ref dtSource, sFieldCodeName, iRow, sDestFieldName, iOffsetMinutes);
							if (sFieldVal.Substring(0, 5) != "Error")
							{
								//sExpression = Replace(sExpression, sFieldCode, GetSourceValue(sSQLTableName, dtSource, sFieldCode, iRow, sDestFieldName, iOffsetMinutes))    '<<enuf to keep rows aligned properly??
								sExpression = sExpression.Replace(sFieldCode, sFieldVal);
							}
							else
							{
								sExpression = sFieldVal; //this is the error
							}
						}
					}
				}

				//Replace keywords in (- -)


				//Replace 

				int iFunctionStart = 0; //InStr(sExpression, "Propercase(")
				string sFunction = ""; //portion of sExpression to replace
				bool bFuncEndFound = false;
				string sParam = "";
				string sParamProper = "";
				int iParamStart = 0;
				int iParamEnd = 0;
				bool bParamEndFound = false;
				int iOpenParenCt = 0;
				int iCloseParenCt = 0;
				int iCharPos = 0;
				string sChar = null;

				//Functions - Propercase
				do
				{
					iFunctionStart = sExpression.IndexOf("Propercase(") + 1;
					if (iFunctionStart == 0)
					{
						break;
					}
					//If iFunctionStart > 0 Then      'ifunctionStart is first char of string to replace with returned value
					//remove everything to the left of function
					sFunction = sExpression.Substring(iFunctionStart - 1); //, Len(sExpression) - iFunctionStart + 1) 'everything to right

					//On what char does sfunction end - closing paren
					//iOpenParenCt = 1
					iCharPos = 0;
					iOpenParenCt = 0;
					iCloseParenCt = 0;
					bFuncEndFound = false;
					while (bFuncEndFound == false)
					{
						iCharPos += 1; //increment char position
						sChar = sFunction.Substring(iCharPos - 1, 1); //get next char
						if (sChar == ")")
						{
							iCloseParenCt += 1;
							if (iCloseParenCt == iOpenParenCt)
							{
								bFuncEndFound = true;
								iParamEnd = iCharPos;
							}
						}
						else if (sChar == "(")
						{
							iOpenParenCt += 1;
							if (iOpenParenCt == 1)
							{
								iParamStart = iCharPos;
							}
						}
						if (iCharPos == sFunction.Length)
						{
							break;
						}
					}

					if (iParamEnd > iParamStart)
					{
						sFunction = sFunction.Substring(0, iParamEnd);
						sParam = sFunction.Substring(iParamStart, iParamEnd - iParamStart - 1);
						sParamProper = Microsoft.VisualBasic.Strings.StrConv(sParam, Microsoft.VisualBasic.VbStrConv.ProperCase);

						sExpression = sExpression.Replace(sFunction, sParamProper);
					}
					else
					{
						//can't parse, possibly because field value contains unpaired '(' or ')'
						sExpression = sExpression.Replace("Propercase(", "");
					}

				} while (true);

				//Functions - Format PHone
				string sNumericPortion = "";
				string sExtension = "";
				string sPhoneFormatted = "";

				do
				{
					iFunctionStart = sExpression.IndexOf("FormatPhone(") + 1;
					if (iFunctionStart == 0)
					{
						break;
					}
					//If iFunctionStart > 0 Then      'ifunctionStart is first char of string to replace with returned value
					//remove everything to the left of function
					sFunction = sExpression.Substring(iFunctionStart - 1); //, Len(sExpression) - iFunctionStart + 1) 'everything to right

					//On what char does sfunction end - closing paren
					//iOpenParenCt = 1
					iCharPos = 0;
					iOpenParenCt = 0;
					iCloseParenCt = 0;
					bFuncEndFound = false;

					//Do While bFuncEndFound = False
					do
					{
						iCharPos += 1; //increment char position
						sChar = sFunction.Substring(iCharPos - 1, 1); //get next char
						if (sChar == ")")
						{
							iCloseParenCt += 1;
							if (iCloseParenCt == iOpenParenCt)
							{
								bFuncEndFound = true;
								iParamEnd = iCharPos;
							}
						}
						else if (sChar == "(")
						{
							iOpenParenCt += 1;
							if (iOpenParenCt == 1)
							{
								iParamStart = iCharPos;
							}
						}
						if (iCharPos == sFunction.Length)
						{
							break;
						}
					} while (true);

					if (iParamEnd > iParamStart)
					{
						sFunction = sFunction.Substring(0, iParamEnd);
						sParam = sFunction.Substring(iParamStart, iParamEnd - iParamStart - 1);
						//sParamProper = StrConv(sParam, VbStrConv.ProperCase)
						//format phone which is sParam
						//Strip ( ) - . , 
						sParam = sParam.Replace("(", "");
						sParam = sParam.Replace(")", "");
						sParam = sParam.Replace("-", "");
						sParam = sParam.Replace(".", "");
						sParam = sParam.Replace(",", "");

						//If non-numeric found (x, ext, ????) then take number to left as phone #, leave non-numeric intact
						sNumericPortion = sParam;
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of Len(sParam) for every iteration:
						int tempVar = sParam.Length;
						for (int i = 1; i <= tempVar; i++)
						{
							if (!NumericHelper.IsNumeric(sParam.Substring(i - 1, 1)))
							{
								//text found (ext?)
								sNumericPortion = sParam.Substring(0, i - 1);
								sExtension = sParam.Substring(i - 1, sParam.Length - i);
								break;
							}
						}

						//Select case on len and insert -
						switch (sNumericPortion.Length)
						{
							case 7:
								sPhoneFormatted = sNumericPortion.Substring(0, 3) + "-" + sNumericPortion.Substring(3, 4);
								break;
							case 8:
								sPhoneFormatted = sNumericPortion.Substring(0, 1) + "-" + sNumericPortion.Substring(1, 3) + "-" + sNumericPortion.Substring(4, 4);
								break;
							case 10:
								sPhoneFormatted = sNumericPortion.Substring(0, 3) + "-" + sNumericPortion.Substring(3, 3) + "-" + sNumericPortion.Substring(6, 4);
								break;
							case 11:
								sPhoneFormatted = sNumericPortion.Substring(0, 1) + "-" + sNumericPortion.Substring(1, 3) + "-" + sNumericPortion.Substring(4, 3) + "-" + sNumericPortion.Substring(7, 4);
								break;
						}

						if (sExtension != "")
						{
							sPhoneFormatted = sPhoneFormatted + " " + sExtension;
						}

						sExpression = sExpression.Replace(sFunction, sPhoneFormatted);
					}
					else
					{
						//can't parse, possibly because field value contains unpaired '(' or ')'
						sExpression = sExpression.Replace("FormatPhone(", "");
					}

				} while (true);

				//Remove leading/trailing vbcrlf
				while (sExpression.Substring(0, 2) == "\r\n")
				{
					sExpression = sExpression.Substring(2);
				}
				while (sExpression.Substring(sExpression.Length - 2) == "\r\n")
				{
					sExpression = sExpression.Substring(0, sExpression.Length - 2);
				}


				return sExpression;

			}
			catch (Exception ex)
			{

				//LogLine(1, sProc & ": " & ex.Message & "; Could not evaluate expression " & sExpression, "Failed")
				return "";

			}


		}

		private string GetSourceValue(string sSQLTableName, ref DataTable dtSource, string sFieldCode, int iRow, string sDestFieldName, int iOffsetMinutes = 0)
		{

			//pj 12/6/2011 Why is this commented out?

			//PURPOSE:
			//		Gets value from Source datatable
			//CALLED FROM:
			//       EvalExpression
			//PARAMETERS:
			//		sSQLTableName: This is the name of the destination SQL table
			//       dtSource: Source datatable (has been deduped)
			//       sFieldCode: field to be evaluated 
			//       iRow: row in dtSource being evaluated
			//       sDestFieldName: SQL field name being edited
			//       iOffsetMinutes:     offset minutes for datetime fields
			//RETURNS:
			//		String
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			int iResult = -1;
			string sPrefix = "";
			string sSourceVal = "";
			string sResult = "";

			try
			{
				sFieldCode = sFieldCode.Replace("(%", "");
				sFieldCode = sFieldCode.Replace("%)", "");

				//Check if is null - other field types??

				//If Not IsDBNull(dtSource.Rows(iRow).Item(sFieldCode)) Then  'want destination field here
				//    sPrefix = Mid(sDestFieldName, 1, 4)
				//    Select Case sPrefix
				//        Case "MLS_"
				//            'Lookup: All lst values for this table are in dtLSTMetadata
				//            'iResult = GetLSTValue(sFieldCode, dtSource.Rows(iRow).Item(sFieldCode))
				//            iResult = GetLSTValue(sSQLTableName, sDestFieldName, dtSource.Rows(iRow).Item(sFieldCode))
				//            If iResult = -1 Then
				//                Return "Error: Cannot find value for " & sFieldCode & ": " & dtSource.Rows(iRow).Item(sFieldCode)
				//            Else
				//                sResult = iResult.ToString
				//            End If

				//        Case "CHK_"
				//            sSourceVal = dtSource.Rows(iRow).Item(sFieldCode)
				//            Select Case UCase(sSourceVal)
				//                Case "TRUE", "YES", "CHECKED"
				//                    sResult = 1
				//                Case "FALSE", "NO", "UNCHECKED"
				//                    sResult = 0
				//            End Select
				//        Case "CUR_"
				//            sSourceVal = dtSource.Rows(iRow).Item(sFieldCode)
				//            sSourceVal = Replace(sSourceVal, "$", "")
				//            sSourceVal = Replace(sSourceVal, ",", "")
				//            sResult = sSourceVal

				//        Case "DTT_"
				//            sSourceVal = dtSource.Rows(iRow).Item(sFieldCode)
				//            If InStr(sSourceVal, "-") = 0 And InStr(sSourceVal, "/") = 0 Then
				//                If Len(sSourceVal) = 8 Then
				//                    'Add hyphen between yyyy-mm-dd
				//                    sResult = Left(sSourceVal, 4) & "-" & Mid(sSourceVal, 5, 2) & "-" & Mid(sSourceVal, 7, 2)
				//                End If
				//            Else
				//                sResult = sSourceVal
				//            End If
				//            'GMT offset
				//            If iOffsetMinutes <> 0 Then
				//                Dim myDate As DateTime = CType(sResult, DateTime)
				//                Dim myTime As String = DatePart(DateInterval.Hour, myDate)
				//                sResult = DateAdd(DateInterval.Minute, iOffsetMinutes, myDate)

				//            End If


				//        Case Else
				//            sResult = dtSource.Rows(iRow).Item(sFieldCode)
				//    End Select
				//End If

				//'Need to wrap leading and trailing % with ''. Othewise could result in 
				//'a parsing error if the returned value is part of a function, such as 
				//'Propercase(%ABC CHEMICAL). Code will see this as an invalid field code.

				//If sResult <> "" Then
				//    Do While Left(sResult, 1) = "%"
				//        sResult = "'" & sResult
				//    Loop
				//    Do While Right(sResult, 1) = "%"
				//        sResult = sResult & "'"
				//    Loop
				//End If

				return sResult;

			}
			catch (Exception ex)
			{

				//LogLine(1, sProc & ": " & ex.Message & "; Could not get source field value " & sFieldCode, "Failed")
				return "";

			}

		}

		private bool EvalUniqueSourceID(ref DataTable dtTransformed, ref DataTable dtMap)
		{

			//PURPOSE: 
			//      Fill UniqueID in source data
			//CALLED FROM:
			//       EvalExpression
			//PARAMETERS:
			//		dtTransformed:  Datatable with transformed/validated source data
			//       dtmap:          Map table
			//RETURNS:
			//		True/False
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			try
			{
				//dtTransformed is source data, with Selltis field names and UniqueID column
				//dtMap has one row with 'Key' field = True
				if (!dtTransformed.Columns.Contains("UniqueKey"))
				{
					dtTransformed.Columns.Add("UniqueKey");
				}

				//What is key source expression? Comes from dtMap, 'Key' row, field 'Expression'
				string sSourceKeyExpression = "";
				string sSourceKey = "";
				foreach (DataRow drMap in dtMap.Rows)
				{
					if (!Convert.IsDBNull(drMap["Key"]))
					{
						if (Convert.ToString(drMap["Key"]) == "True")
						{
							sSourceKey = Convert.ToString(drMap["Expression"]);
						}
					}
				}

				//Translate sSourceKeyExpression into Selltis field names. Fields must be mapped!
				int iStart = 1;
				int iEnd = 1;
				string sFieldCodeSource = "";
				string sFieldCodeDest = "";
				string sFieldName = "";
				string sFieldVal = "";

				foreach (DataRow drTrans in dtTransformed.Rows)
				{
					sSourceKeyExpression = sSourceKey;
					do //While iStart > 0
					{
						iStart = sSourceKeyExpression.IndexOf("(%") + 1;
						if (iStart > 0)
						{
							iEnd = sSourceKeyExpression.IndexOf("%)") + 1 + 1;
							if (iEnd > 0)
							{
								//Pick out the field code from string of field codes
								sFieldCodeSource = sSourceKeyExpression.Substring(iStart - 1, iEnd - iStart + 1);
								//Consult dtMap for name of Selltis field
								try
								{
									sFieldCodeDest = "";
									sFieldVal = "";
									foreach (DataRow drMap in dtMap.Rows)
									{
										if (drMap["Expression"].ToString() != "")
										{
											if (Convert.ToString(drMap["Expression"]) == sFieldCodeSource)
											{
												//the field is mapped
												sFieldCodeDest = Convert.ToString(drMap["SQLFieldName"]);
												if (sFieldCodeDest.Contains("(%"))
												{
													//This is the key row in dtMap - not the mapped row
													sFieldCodeDest = "";
												}
												else
												{
													//This is the mapped row, exit map
													break;
												}
											}
										}
									}
									//Now we have sFieldCode = (%City%) and sFieldCodeDest = TXT_CityMailing
									if (sFieldCodeDest != "")
									{
										//Get source field value from source datatable
										if (!Convert.IsDBNull(drTrans[sFieldCodeDest]))
										{
											sFieldVal = Convert.ToString(drTrans[sFieldCodeDest]);
										}
										else
										{
											sFieldVal = "";
										}
									}
									else
									{
										sFieldVal = "";
									}
									sSourceKeyExpression = sSourceKeyExpression.Replace(sFieldCodeSource, sFieldVal);

								}
								catch (Exception ex2)
								{
									Debug.WriteLine(ex2.Message);
								}
							}
						}
						else
						{
							break;
						}
					} while (true);
					drTrans["UniqueKey"] = sSourceKeyExpression;
				}

				return true;

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
				return false;

			}

		}

		private int GetLSTValue(string sSQLTableName, string sFieldCode, string sValue, bool par_bCreateMissingLSTValues = false)
		{

			//PURPOSE:
			//		Lookup for MLS_ fields
			//CALLED FROM:
			//       GetSourceValue
			//PARAMETERS:
			//		sSQLTableName: This is the name of the destination SQL table
			//       sFieldCode: SQL field name
			//       sValue: value to lookup in LST metadata. Ex, 'Open', 'Customer' (not integer values)
			//       par_bCreateMissingLSTValues:    True to create LST_ metadata values
			//RETURNS:
			//		Integer equal to LST_ TXT_Property
			//       -1 if value not found
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			try
			{

				clTransform oTR = new clTransform();
				string sFilter = "";
				string sFieldNameNoPrefix = "";
				DataTable dtLST = new DataTable();
				DataView dvLST = new DataView();
				clProject goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];

				oTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTR"];
				dtLST = (DataTable)goP.GetVar("ImportSQL_dtLST");

				//Ouch! not here!
				//dtLST = GetLSTMetadata(sSQLTableName)

				sFieldNameNoPrefix = sFieldCode.Replace("MLS_", "");
				//Need to handle ' in filter, as for case of "Don't know"
				sValue = oTR.PrepareForSQL(sValue);

				sFilter = "TXT_Page = 'LST_" + sSQLTableName + ":" + sFieldNameNoPrefix + "' AND TXT_Value = '" + goTR.PrepareForSQL(sValue) + "' AND TXT_Property <> 'Default'";

				dtLST.DefaultView.RowFilter = sFilter;
				dvLST = dtLST.DefaultView;

				//If dtLST.Rows.Count = 1 Then
				if (dvLST.Count > 0)
				{
					//Return dtLST.Rows(0).Item("TXT_Property")   'to int
					return Convert.ToInt32(dvLST[0]["TXT_Property"]);
				}
				else
				{


					//For MLS_ fields, lookup values in LST metadata and add if missing
					//   From top of function, we have dtLST which contains TXT_Page, TXT_Property, TXT_Value, so need to
					//   filter the dt for TXT_Page=LST_[File]:[Field]  (Field name without prefix)
					if (par_bCreateMissingLSTValues)
					{
						//If sFieldName.StartsWith("MLS_") Then
						//Add LST_ line to MD
						//   Get highest index
						int iHighestIndex = 0;
						string sProperty = "";
						string sPage = "";
						foreach (DataRow row in dtLST.Rows)
						{
							sPage = "LST_" + FileName + ":" + goTR.FromTo(sFieldCode, 5);
							if (Convert.ToString(row["TXT_Page"]).ToUpper() == sPage.ToUpper())
							{
								sProperty = row["TXT_Property"].ToString();
								//If sProperty.Contains("_") Then
								//get integer to right of _
								if (NumericHelper.IsNumeric(sProperty))
								{
									if (int.Parse(sProperty) > iHighestIndex && sProperty != "99")
									{
										iHighestIndex = int.Parse(sProperty);
									}
								}
								//End If
							}
						}
						//Add LST value
						System.Data.SqlClient.SqlConnection tempVar = null;
						goMeta.LineWrite("", sPage, "US_" + (iHighestIndex + 1).ToString(), sValue, ref tempVar, "", "XX");
						LogLine("1", "Added item '" + sValue + "' for list " + sFieldCode, "OK");

						//Get updated MD of list
						dtLST = GetLSTMetadata(FileName);
						goP.SetVar("ImportSQL_dtLST", dtLST);
						return iHighestIndex + 1;

					}
					else
					{

						return -1;
					}
				}


			}
			catch (Exception ex)
			{

				//LogLine(1, sProc & ": " & ex.Message & "; Error evaluating MLS field, " & sFieldCode, "Failed")
				return -1;

			}

		}

		public DataTable GetLSTMetadata(string sSQLTableName)
		{

			//PURPOSE:
			//		Load LST_ metadata for MLS_ lookup. Loaded once at beginning of import session
			//CALLED FROM:
			//       Initialize
			//PARAMETERS:
			//		sSQLTableName: This is the name of the destination SQL table
			//RETURNS:
			//		Datatable
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			DataTable dt = new DataTable();
			string settings = Convert.ToString(HttpContext.Current.Session["Connection"]);
			System.Data.SqlClient.SqlConnection sqlConnection1 = new System.Data.SqlClient.SqlConnection(settings);

			try
			{

				sqlConnection1.Open();

				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader reader = null;
				Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();
				string sSQL = "";
				string sTable = sSQLTableName;
				//testing
				//Dim sTable As String = "CO"


				//sSQL = "SELECT * FROM" & modGlobal.sDestinationTable
				sSQL = "SELECT TXT_Page, TXT_Property, TXT_Value FROM MD WHERE TXT_Page LIKE 'LST_" + sSQLTableName + "%'";

				//Try
				cmd.CommandText = sSQL;
				cmd.CommandType = CommandType.Text;
				cmd.Connection = sqlConnection1;

				reader = cmd.ExecuteReader();

				if (reader.HasRows)
				{

					dt.Load(reader);

				}
				else
				{
					//==> raise error
				}

				reader.Close();
				sqlConnection1.Close();

				dt.TableName = "LSTMetadata";

				return dt;
			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & ": " & ex.Message, "Failed")
				return null;

			}

		}

		public bool LogLine(string iLogLevel, string sProcess, string sStatus)
		{

			//PURPOSE:
			//		Write lines to log datatable
			//CALLED FROM:
			//       everywhere
			//PARAMETERS:
			//		iLogLevel:  0: Process start and end
			//                   1: warning or failure of individual data methods, non-critical
			//RETURNS:
			//		True/False
			//AUTHOR: PJ

			//This would be edited in each project depending on how we need to log.
			//Perhaps all logging is done to the status table????

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			try
			{

				clProject oP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
				string sID = HttpContext.Current.Session.LCID.ToString();
				string sIMPJobID = Convert.ToString(oP.GetVar("ImportSQL_sIMPJobID"));

				//Testing:
				string sdtLog = oP.GetVar("ImportSQL_dtLog").ToString();
				//------
				DataTable dtLog = (DataTable)oP.GetVar("ImportSQL_dtLog");

				object[] aLine = new object[4];

				aLine[0] = DateTime.Now.ToString();
				aLine[1] = iLogLevel;
				aLine[2] = sProcess;
				aLine[3] = sStatus;

				dtLog.Rows.Add(aLine);

				oP.SetVar("ImportSQL_dtLog", dtLog);

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
				return false;

			}

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public bool SQLBulkAdd(string par_sSQLTableName, DataTable par_dtAdd, string sIMPJobID)
		{

			//PURPOSE:
			//		Use SQLBulkCopy method to add NEW records to destination SQL table
			//CALLED FROM:
			//       Public: web service consumer
			//PARAMETERS:
			//		par_sSQLTableName: name of destination SQL table
			//       par_dtAdd:  Datatable to be bulk added
			//       sImpJobID:  Import job ID
			//RETURNS:
			//		True/False
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			//LogLine(0, "SQLBulkCopy", "Started")
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];

			if (goP == null)
			{
				LogLine("0", sProc + ": Unable to retreive session objects", "Failed");
				return false;
			}

			DataTable dtAdd = null;
			if (par_dtAdd == null)
			{
				dtAdd = (DataTable)goP.GetVar("ImportSQL_dtTransformedNew");
			}
			else
			{
				dtAdd = par_dtAdd;
			}

			//'Dim dtAdd As DataTable = goP.GetVar("ImportSQL_dtTransformedNew")
			if (dtAdd == null)
			{
				LogLine("0", sProc + ": Add table does not exist", "Failed");
				return false;
			}
			else
			{
				if (dtAdd.Rows.Count == 0)
				{
					LogLine("0", sProc + ": Add table does not contain any rows", "OK");
					return true;
				}
			}

			try
			{

				//Get highest BI__ID in this file, for sys_name processing. HOW WORK FOR MULTIPLE PASSES OF SAME FILE OR MULT FILES?
				clRowSet oRS;
				oRS = new clRowSet(par_sSQLTableName, 3, "", "BI__ID Desc", "BI__ID", 1);
				if (oRS.GetFirst() == 1)
				{
					iHighestBIID = Convert.ToInt32(goTR.StringToNum(oRS.GetFieldVal("BI__ID")));
				}
				else
				{
					iHighestBIID = 0;
				}


				string settings = Convert.ToString(HttpContext.Current.Session["Connection"]);
				//sConnectionString = "Data Source=PSSQL001\SQL2005;Initial Catalog=PJTest;User ID=sa;Password=********;connect timeout=60"
				System.Data.SqlClient.SqlConnection sqlConnection1 = new System.Data.SqlClient.SqlConnection(settings);
				sqlConnection1.Open();

				System.Data.SqlClient.SqlBulkCopy oSBC = new System.Data.SqlClient.SqlBulkCopy(settings, System.Data.SqlClient.SqlBulkCopyOptions.FireTriggers);

				oSBC.DestinationTableName = "[" + par_sSQLTableName + "]";
				oSBC.BulkCopyTimeout = 300;

				//dtTransformed col names are SQL field names so source and dest mapping is the same (dtTrans colname)
				System.Data.SqlClient.SqlBulkCopyColumnMapping cm = null;
				for (int i = 0; i < dtAdd.Columns.Count; i++)
				{
					cm = oSBC.ColumnMappings.Add(dtAdd.Columns[i].ColumnName, dtAdd.Columns[i].ColumnName);
				}

				//Break this into 1,000 record increments and update status table
				//AddHandler oSBC.SqlRowsCopied, AddressOf OnSBCRowsImported     '<<< Will need to be done the ASP way
				oSBC.NotifyAfter = 1000;

				try
				{
					oSBC.WriteToServer(dtAdd);

					LogLine("0", "Added " + dtAdd.Rows.Count + " new records in table " + par_sSQLTableName, "OK");

				}
				catch (Exception ex1)
				{

					//LogLine(0, "Bulk copy: Error writing dtAdd to SQL table for table " & par_sSQLTableName & ". " & ex1.Message, "Failed")
					return false;

				}

				//LogLine(0, "SQL Bulk Copy: Imported " & dtAdd.Rows.Count & " records", "OK")

				//Destroy datatable
				dtAdd = null;
				goP.SetVar("ImportSQL_dtTransformedNew", null);

				sqlConnection1.Close();

				return true;

			}
			catch (Exception ex)
			{

				//LogLine(0, "SQL Bulk Copy: " & ex.Message, "Failed")
				return false;

			}


		}

		public bool SQLEdit(string sSQLTableName, string sSQLTempTableName, DataTable dtMap, string sIMPJobID)
		{

			//PURPOSE:
			//		Use SQLBulkCopy method to UPDATE records in destination SQL table.
			//       This is done by creating a temp table in SQL db, bulk copying datatable to this temp table
			//       and then executing an UPDATE statement, then dropping the temp table
			//CALLED FROM:
			//       Public: web service consumer
			//PARAMETERS:
			//		sSQLTableName: name of destination SQL table
			//       sSQLTempTableName: name of the temporary SQL table. will be dropped after UPDATE
			//       dtMap: map table
			//       sIMPJobID:  Import job ID
			//RETURNS:
			//		True/False
			//AUTHOR: PJ

			//LogLine(0, "SQLBulkEdit", "Started")

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			DataTable dtEdit = null;
			string sFields = "";
			string sValues = "";
			string sSQL = "";
			string sSet = "";
			int iErrorCount = 0;

			string[] aKeys = null;
			string sDestKeyColumnName = "";
			bool bKeysExist = false;
			int iKeyCount = -1;
			int iUseExtID = 0;
			string sKeyField = "";
			string sWherePart = "";

			clProject goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			clTransform goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTR"];

			//Try
			if (goP == null)
			{
					LogLine("0", sProc + ": Unable to retreive session objects", "Failed");
					return false;
				}

				dtEdit = (DataTable)goP.GetVar("ImportSQL_dtTransformedUpd");

				if (dtEdit == null)
				{
					LogLine("0", sProc + ": Edit table does not exist", "Failed");
					return false;
				}
				else
				{
					if (dtEdit.Rows.Count == 0)
					{
						LogLine("0", sProc + ": Edit table does not contain any rows", "OK");
						return true;
					}
				}

				if (dtMap == null)
				{
					dtMap = (DataTable)goP.GetVar("ImportSQL_dtMap");
				}
				if (dtMap == null)
				{
					LogLine("0", sProc + ": Unable to read map data", "Failed");
					return false;
				}

				//NEW WAY
				//key field is always GID_ID
				sKeyField = "GID_ID";

			//'OLD WAY
			//'aKeys contians key field(s) used in WHERE statement for SQL UPDATE below
			//'Separate function ---
			//For i As Integer = 0 To dtMap.Rows.Count - 1
			//    If Not IsDBNull(dtMap.Rows(i).Item("Key")) Then
			//        If dtMap.Rows(i).Item("Key") = True Then
			//            sDestKeyColumnName = dtMap.Rows(i).Item("SQLFieldName")
			//            iKeyCount += 1
			//            ReDim Preserve aKeys(iKeyCount)
			//            aKeys(iKeyCount) = sDestKeyColumnName
			//            bKeysExist = True
			//            If sDestKeyColumnName = "TXT_ExternalID" Or sDestKeyColumnName = "TXT_ExternalID" Then
			//                iUseExtID += 1  'If this = 2 then we know to use TN table with external IDs
			//            End If
			//            'Temporary while multiple key system is worked out
			//            sKeyField = sDestKeyColumnName
			//        End If
			//    End If
			//Next
			//------------

			//If aKeys.Length = 0 Then
			//    LogLine(0, sProc & ": Key field(s) not defined. Update stopped", "Failed")
			//    Return False
			//End If
			//Catch ex As Exception

			//    LogLine(0, sProc & ": " & ex.Message, "Failed")

			//End Try

			string settings = Convert.ToString(HttpContext.Current.Session["Connection"]);
			System.Data.SqlClient.SqlConnection sqlConnection1 = new System.Data.SqlClient.SqlConnection(settings);
			sqlConnection1.Open();

			//Drop table if already exists
			//Try
			System.Data.SqlClient.SqlCommand cmd1 = new System.Data.SqlClient.SqlCommand();
				sSQL = "DROP TABLE " + sSQLTempTableName;
				cmd1.CommandText = sSQL;
				cmd1.CommandType = System.Data.CommandType.Text;
				cmd1.Connection = sqlConnection1;
				if (cmd1.ExecuteNonQuery() == 1)
				{

				}

			//Catch ex As Exception
			//    'Do nothing. Table may not exist

			//End Try

			//Create temp table
			try
			{
				System.Data.SqlClient.SqlCommand cmd2 = new System.Data.SqlClient.SqlCommand();
				sSQL = "SELECT TOP 0 * INTO " + sSQLTempTableName + " FROM " + sSQLTableName + "\r\n";
				//V2: Don't drop fields, where clause depends on GID_ID now
				//sSQL += "ALTER TABLE " & sSQLTempTableName & vbCrLf
				//sSQL += "DROP COLUMN GID_ID" & vbCrLf
				//sSQL += "ALTER TABLE " & sSQLTempTableName & vbCrLf
				//sSQL += "DROP COLUMN BI__ID"
				//V2: Need to allow nulls in GID_ID and BI__ID
				sSQL += "ALTER TABLE " + sSQLTempTableName + "\r\n";
				sSQL += "ALTER COLUMN GID_ID varchar(50) NULL";

				cmd2.CommandText = sSQL;
				cmd2.CommandType = System.Data.CommandType.Text;
				cmd2.Connection = sqlConnection1;
				if (cmd2.ExecuteNonQuery() == 1)
				{

				}
				//LogLine(0, "Creating SQL edit table", "OK")
			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & ": Error creating SQL edit table", "Failed")
				return false;

			}

			//Upload the datatable to temp table
			try
			{
				System.Data.SqlClient.SqlBulkCopy oSBC = new System.Data.SqlClient.SqlBulkCopy(settings, System.Data.SqlClient.SqlBulkCopyOptions.FireTriggers);

				oSBC.DestinationTableName = sSQLTempTableName;
				oSBC.BulkCopyTimeout = 300;

				//dtTransformed col names are SQL field names so source and dest mapping is the same (dtTrans colname)
				oSBC.ColumnMappings.Clear();
				for (int i = 0; i < dtEdit.Columns.Count; i++)
				{
					//Dim sTest As String = dtEdit.Columns(i).ColumnName
					//V2: using GID_ID as key, do not map
					//If dtEdit.Columns(i).ColumnName <> "GID_ID" Then
					oSBC.ColumnMappings.Add(dtEdit.Columns[i].ColumnName, dtEdit.Columns[i].ColumnName);
					//End If
				}

				//Break this into 1,000 record increments and update status table
				//AddHandler oSBC.SqlRowsCopied, AddressOf OnSBCRowsImported     '<<< Will need to be done the ASP way
				oSBC.NotifyAfter = 1000;

				oSBC.WriteToServer(dtEdit); //DEGUG this

				//LogLine(0, "Uploading edit datatable to SQL", "OK")

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & ": Error uploading edit datatable to SQL", "Failed")
				return false;

			}

			//Run update

			//String of fields to update
			//= ISNULL(  will only update non-null values from source data
			//May want to have option to clear all fields and import new values only. In this case, drop the '= ISNULL('
			for (int i = 0; i < dtEdit.Columns.Count; i++)
			{
				string sColumnName = dtEdit.Columns[i].ColumnName;
				//V2: GID_ID not mapped
				if (sColumnName != "GID_ID")
				{
					if (sSet != "")
					{
						sSet += ", " + "\r\n";
					}
					sSet += sSQLTableName + "." + sColumnName + " = ISNULL(" + sSQLTempTableName + "." + sColumnName + ", " + sSQLTableName + "." + sColumnName + ")";
				}
			}
			sSet += "\r\n";

			try
			{
				System.Data.SqlClient.SqlCommand cmd3 = new System.Data.SqlClient.SqlCommand();
				sSQL = "UPDATE " + sSQLTableName + "\r\n";
				sSQL += "SET " + "\r\n";
				sSQL += sSet;
				sSQL += "FROM " + sSQLTableName + ", " + sSQLTempTableName + "\r\n";

				//2/16: this needs to allow for multiple key fields:
				//sSQL += "WHERE " & sSQLTableName & "." & sKeyField & " = " & sSQLTempTableName & "." & sKeyField

				sSQL += "WHERE ";

				//OLD WAY
				//For i As Integer = 0 To aKeys.GetUpperBound(0)
				//    If sWherePart = "" Then
				//        sWherePart = sSQLTableName & "." & aKeys(i) & " = " & sSQLTempTableName & "." & aKeys(i)
				//    Else
				//        sWherePart += " AND " & sSQLTableName & "." & aKeys(i) & " = " & sSQLTempTableName & "." & aKeys(i)
				//    End If
				//Next

				//NEW WAY
				sWherePart = "CAST (" + sSQLTableName + ".GID_ID AS VARCHAR(50))" + " = " + sSQLTempTableName + ".GID_ID";

				sSQL += sWherePart;

				//------------
				cmd3.CommandText = sSQL;
				cmd3.CommandType = System.Data.CommandType.Text;
				cmd3.Connection = sqlConnection1;
				if (cmd3.ExecuteNonQuery() == 1)
				{

				}

				LogLine("0", "Edited " + dtEdit.Rows.Count.ToString() + " records", "OK");

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & ": Error updating SQL target table", "Failed")
				return false;

			}

			//Drop table
			try
			{
				System.Data.SqlClient.SqlCommand cmd2 = new System.Data.SqlClient.SqlCommand();
				sSQL = "DROP TABLE " + sSQLTempTableName;
				cmd2.CommandText = sSQL;
				cmd2.CommandType = System.Data.CommandType.Text;
				cmd2.Connection = sqlConnection1;
				if (cmd2.ExecuteNonQuery() == 1)
				{

				}

				//LogLine(0, "Dropping SQL edit table", "OK")

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & ": Error dropping temp SQL table", "Failed")
				return false;

			}

			//Destroy datatable
			dtEdit = null;
			goP.SetVar("ImportSQL_dtTransformedUpd", null);

			sqlConnection1.Close();
			return true;


		}

		public bool SQLBulkDelete(string sSQLTableName, string sCondition)
		{

			//PURPOSE:
			//		Run sql delete command for table
			//CALLED FROM:
			//       
			//PARAMETERS:
			//		sSQLTableName: name of SQL table
			//       sCondition: this is the WHERE clause
			//RETURNS:
			//		True/False
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			LogLine("0", "SQLBulkDelete", "Started");

			clProject goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			if (goP == null)
			{
				LogLine("0", sProc + ": Unable to retreive session objects", "Failed");
				return false;
			}

			DataTable dt = new DataTable();
			string settings = Convert.ToString(HttpContext.Current.Session["Connection"]);
			System.Data.SqlClient.SqlConnection sqlConnection1 = new System.Data.SqlClient.SqlConnection(settings);

			try
			{

				sqlConnection1.Open();

				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
				Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();
				string sSQL = "";
				string sTable = sSQLTableName;

				sSQL = "DELETE " + sSQLTableName;
				if (sCondition != "")
				{
					sSQL += " WHERE " + sCondition;
				}

				cmd.CommandText = sSQL;
				cmd.CommandType = CommandType.Text;
				cmd.Connection = sqlConnection1;

				//If cmd.ExecuteNonQuery() = 1 Then
				//    sqlConnection1.Close()
				//    Return True
				//Else
				//    sqlConnection1.Close()
				//    Return False
				//End If

				//Above return value is # of records deleted, so just call and then need to check results in calling code
				cmd.ExecuteNonQuery();

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & ": " & ex.Message, "Failed")
				return false;

			}

			return true;

		}

		public bool CreateTempFields(string sFileName)
		{

			//This should be replaced by individual temp fields, not all fields

			clSchema oSchema = new clSchema();
			bool bResult;

			bResult = oSchema.SchemaAddFields(sFileName + "|" + sFileName);

			return bResult;

		}
		public bool DeleteTempFields(string sFileName)
		{

			clSchema oSchema = new clSchema();
			bool bResult;

			bResult = oSchema.SchemaRemoveFields(sFileName + "|" + sFileName);

			return bResult;

		}
		public bool PostProcessImportData(string sFileName, string sImportJobID)
		{

			clSchema oSchema = new clSchema();
			bool bResult;

			bResult = oSchema.ImportProcessFields(sFileName + "|" + sFileName, 20, -1, -1, true, true, sImportJobID);

			return bResult;

		}

		public bool TestCreateSessionVar(string sValue)
		{

			clProject oP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];

			oP.SetVar("ImportSQL_Test", sValue);
			string s = Convert.ToString(oP.GetVar("ImportSQL_Test"));

			return true;

		}

		public string TestGetSessionVar()
		{

			clProject oP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];

			string s = Convert.ToString(oP.GetVar("ImportSQL_Test"));
			return Convert.ToString(oP.GetVar("ImportSQL_Test"));

		}

		private DataTable GetTableSchemaDatatable(string sSQLTableName) //As Boolean
		{

			//PURPOSE:
			//		Get destination SQL table schema as empty datatable
			//PARAMETERS:
			//		sSQLTableName: This is the name of the destination SQL table
			//RETURNS:
			//		0-row datatable
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			DataTable dtTableSchema = new DataTable();
			string settings = Convert.ToString(HttpContext.Current.Session["Connection"]);
			System.Data.SqlClient.SqlConnection sqlConnection1 = new System.Data.SqlClient.SqlConnection(settings);

			//LogLine(0, sProc & "Getting SQL table schema", "Started")
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];

			try
			{
				//reload schema
				goData.LoadSchemaData();

				sqlConnection1.Open();

				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader reader = null;
				Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();
				string sSQL = "";
				string sSQL2 = "";
				string sTable = sSQLTableName;

				sSQL = "SELECT TOP 1 * FROM [" + sTable + "]";

				try
				{
					cmd.CommandText = sSQL;
					cmd.CommandType = CommandType.Text;
					cmd.Connection = sqlConnection1;

					reader = cmd.ExecuteReader();

					if (reader.HasRows == false)
					{
						//close reader
						reader.Close();
						//SQL table has 0 rows, need to add 1 row to get columns returned
						sSQL2 = "INSERT INTO [" + sTable + "] (SYS_NAME)" + "\r\n" + "VALUES ('zTest')";
						cmd.CommandText = sSQL2;
						cmd.CommandType = CommandType.Text;
						cmd.Connection = sqlConnection1;
						cmd.ExecuteNonQuery();

						cmd.CommandText = sSQL;
						cmd.CommandType = CommandType.Text;
						cmd.Connection = sqlConnection1;
						reader = cmd.ExecuteReader();

					}
				}
				catch (Exception ex1)
				{

					//LogLine("0", sProc & ": " & ex1.Message, "Failed")
					return null;

					//SQL table has 0 rows, need to add 1 row to get columns returned
					//sSQL2 = "INSERT INTO " & sTable & " (SYS_NAME)" & vbCrLf & _
					//    "VALUES (zTest)"
					//cmd.CommandText = sSQL2
					//cmd.CommandType = CommandType.Text
					//cmd.Connection = sqlConnection1
					//cmd.ExecuteNonQuery()


				}

				//Remove zTest row
				//?
				if (reader.HasRows)
				{

					dtTableSchema.Load(reader);

					//if Selltis structure, 
					//Try
					dtTableSchema.Columns["GID_ID"].AllowDBNull = true;
						dtTableSchema.Columns["BI__ID"].AllowDBNull = true;
					//Catch
					//    'Ignore: table doesn't have these fields
					//End Try

				}
				else
				{
					//==> raise error
				}

				reader.Close();
				sqlConnection1.Close();

				//LogLine(0, sProc & "Getting SQL table schema", "OK")

				return dtTableSchema;
			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
				return null;

			}

		}

		private DataTable LoadRecordIDs(string sSQLTableName, int iKeyType, string[] aFields) //As Boolean
		{

			//PURPOSE:
			//		Get record ID (GID_ID) and key columns from SQL destination table
			//CALLED FROM:
			//       TransformTableSplit
			//PARAMETERS:
			//		sSQLTableName: This is the name of the destination SQL table
			//       sFields: comma delimited string of fields to return. These are the fields used to determine key value
			//RETURNS:
			//		datatable of GID_IDs and key column(s)
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			DataTable dtTableSchema = new DataTable();
			string settings = Convert.ToString(HttpContext.Current.Session["Connection"]);
			System.Data.SqlClient.SqlConnection sqlConnection1 = new System.Data.SqlClient.SqlConnection(settings);

			//LogLine(0, sProc & "Getting SQL record IDs", "Started")

			try
			{

				sqlConnection1.Open();

				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader reader = null;
				Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();
				string sSQL = "";
				string sTable = sSQLTableName;
				string sFields = "";

				//testing
				//Dim sTable As String = "CO"

				switch (iKeyType)
				{
					case 0:
					case 2:
						//Split string array of SQL fields
						for (int z = 0; z <= aFields.GetUpperBound(0); z++)
						{
							if (aFields[z] != "")
							{
								if (sFields == "")
								{
									sFields = aFields[z];
								}
								else
								{
									sFields += ", " + aFields[z];
								}
							}
						}

						if (sFields == "")
						{
							return null;
						}

						//'V2: sFields may contain multiple field codes (%TXT_NameFirst%) (%TXT_NameLast%)
						//'   so need to parse sFields and strip (%%) chars
						//Dim iStart As Integer = 1
						//Dim iEnd As Integer = 1
						//Dim sFieldCode As String = ""
						//Dim sFieldName As String = ""
						//Dim aFieldName As New clArray

						//Replace source field values with field values from source data
						//Get instances of (% followed by %)
						//5/27/11: aFields has field names already parsed
						//Do While iStart > 0
						//    iStart = InStr(sFields, "(%")
						//    If iStart > 0 Then
						//        iEnd = InStr(sFields, "%)") + 1
						//        If iEnd > 0 Then
						//            sFieldCode = Mid(sFields, iStart, iEnd - iStart + 1)
						//            'Replace
						//            'sFieldVal = GetSourceValue(sSQLTableName, dtSource, sFieldCode, iRow, sDestFieldName, iOffsetMinutes)
						//            sFieldName = Replace(sFieldCode, "(%", "")
						//            sFieldName = Replace(sFieldName, "%)", "")
						//            aFieldName.Add(sFieldName)
						//            sFields = Replace(sFields, sFieldCode, "")
						//            'If Mid(sFieldVal, 1, 5) <> "Error" Then
						//            'sFields = Replace(sFields, sFieldCode, GetSourceValue(sSQLTableName, dtSource, sFieldCode, iRow, sDestFieldName, iOffsetMinutes))    '<<enuf to keep rows aligned properly??
						//            'sFields = Replace(sFields, sFieldCode, sFieldVal)
						//            'Else
						//            'sFields = sFieldVal 'this is the error
						//            'End If
						//        End If
						//    End If
						//Loop
						//Put sFieldName  into sFields string
						//'sFields = ""
						//'For i As Integer = 0 To aFields.GetUpperBound(0)
						//'    If aFields(i) <> "" Then
						//'        If sFields = "" Then
						//'            sFields = aFields(i)
						//'        Else
						//'            sFields += "," & aFields(i)
						//'        End If
						//'    End If
						//'Next
						//V2: END
						//---------------

						//don't want GID_ID in table, because can't have it null when adding new ID's
						//Bah Humbug. We do want it. Not adding keys to dtKey anymore since dtSource is deduped
						sFields = sFields + ", GID_ID";

						//sSQL = "SELECT * FROM" & modGlobal.sDestinationTable
						sSQL = "SELECT " + sFields + " FROM " + sTable;
						break;

					case 1:
						//Use TN table
						sSQL = "SELECT TXT_ExternalID, TXT_ExternalSource, GID_InternalID FROM TN" + " WHERE TXT_File = '" + sSQLTableName + "'";
						break;

					default:
					break;

				}
				//Try
				if (sSQL == "")
				{
					return null;
				}

				cmd.CommandText = sSQL;
				cmd.CommandType = CommandType.Text;
				cmd.Connection = sqlConnection1;

				reader = cmd.ExecuteReader();

				if (reader.HasRows)
				{

					dtTableSchema.Load(reader);

				}
				else
				{
					//==> raise error
				}

				reader.Close();
				sqlConnection1.Close();
				//LoadFieldData = True

			}
			catch (System.Data.SqlTypes.SqlTypeException ex)
			{

				//LogLine(0, "LoadRecordIDs: " & ex.Message, "Failed")
				return null;

			}

			return dtTableSchema;

		}

		private string NormalizeValue(string sText, string par_s1)
		{

			//Company name only for now
			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			string sReturn = sText;
			int iPosStart = 0;
			int iPosEnd = 0;
			string sSuffix = "";
			string sName = "";

			//Temporary
			//par_s1 = "CO"
			//Try

			switch (par_s1.ToUpper())
			{

					case "CO":
						//Get suffix. Suffix defined as characters following the last space char
						iPosStart = sText.LastIndexOf(" ") + 1;
						if (iPosStart > 0)
						{
							//there is a space. what's to the right?
							sSuffix = sText.Substring(sText.Length - (sText.Length - iPosStart + 1));
							sName = sText.Substring(0, iPosStart - 1);
							switch (sSuffix.ToUpper())
							{
								case " LLC":
								case " L.L.C.":
								case " INC":
								case " INC.":
								case " INCORPORATED":
								case " CORPORATION":
								case " CO":
								case " CO.":
								case " COMP":
								case " COMP.":
								case " COMPANY":
								case " CORP":
								case " CORP.":
								case " LTD":
								case " LTD.":
									sReturn = sName;
									break;
								default:
									sReturn = sText;
									break;
							}
						}

						//Remove any chars inside parens (). Like 'ChemoPharm Labratories (CP Industries)'
						iPosStart = 0;
						iPosEnd = 0;
						iPosStart = sReturn.IndexOf("(") + 1;
						if (iPosStart > 0)
						{
							iPosEnd = sReturn.IndexOf(")") + 1;
							if (iPosEnd > iPosStart)
							{
								//there is a pair of parens
								sReturn = sReturn.Substring(0, iPosStart - 1) + sReturn.Substring(iPosEnd);
							}
						}
						break;

					case "CN":
					break;


					default:

						sReturn = sText;
						break;

				}

				//Remove punctuation and non-alphanumerics
				sReturn = sReturn.Replace("!", "");
				sReturn = sReturn.Replace("@", "");
				sReturn = sReturn.Replace("#", "");
				sReturn = sReturn.Replace("$", "");
				sReturn = sReturn.Replace("%", "");
				sReturn = sReturn.Replace("^", "");
				sReturn = sReturn.Replace("&", "");
				sReturn = sReturn.Replace("*", "");
				sReturn = sReturn.Replace("(", "");
				sReturn = sReturn.Replace(")", "");
				sReturn = sReturn.Replace("-", "");
				sReturn = sReturn.Replace("+", "");
				sReturn = sReturn.Replace("_", "");
				sReturn = sReturn.Replace("=", "");
				sReturn = sReturn.Replace("\\", "");
				sReturn = sReturn.Replace("|", "");
				sReturn = sReturn.Replace("'", "");
				sReturn = sReturn.Replace(((char)34).ToString(), "");
				sReturn = sReturn.Replace(",", "");
				sReturn = sReturn.Replace(".", "");
				sReturn = sReturn.Replace("?", "");
				sReturn = sReturn.Replace("/", "");
				sReturn = sReturn.Replace("~", "");
				sReturn = sReturn.Replace(">", "");
				sReturn = sReturn.Replace("<", "");

				//Remove spaces
				sReturn = sReturn.Replace(" ", "");

			//Catch ex As Exception

			//    LogLine(0, sProc & ": " & ex.Message, "Failed")

			//End Try

			return sReturn;

		}

		public bool LoadDatatable(DataTable dt, string sInstructions, ref object par_oReturn)
		{

			//PURPOSE:
			//		Upload datatable of source data (in memory), processed by script (not mapped directly to a File)
			//CALLED FROM:
			//       ws consumer
			//PARAMETERS:
			//		dt: source datatable
			//       sInstructions: string including the function to run and parameters
			//RETURNS:
			//		True/False
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			string sReturn = "";

			try
			{

				//Call function to process data
				clScrMngRowSet goScr = (Selltis.BusinessLogic.clScrMngRowSet)HttpContext.Current.Session["goScr"];
				object temp_dt = dt;
				object temp_sReturn = sReturn;
				goScr.RunScript("Import_ManageImports", ref temp_dt, null, sInstructions, "", "", "", "", ref temp_sReturn);
					sReturn = Convert.ToString(temp_sReturn);
					dt = (DataTable)temp_dt;

				par_oReturn = sReturn;

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & ": Error uploading datatable to Selltis server. " & ex.Message, "Failed")
				return false;

			}

			return true;

		}

		public bool PostProcessLinkData()
		{

			//PURPOSE:
			//		Create link data for mapped links
			//CALLED FROM:
			//       calling UI
			//PARAMETERS:

			//RETURNS:
			//		True/False
			//AUTHOR: PJ

			clSchema goSchema = new clSchema();
			//Dim goSchemaPJ As New clSchemaPJ
			bool bResult = false;
			string sSQL = null;
			string sLinkName = null;
			string sLinkKeyField = null;
			string sTempField = null;

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			try
			{

				//Read Map to find mapped links and link key field
				foreach (DataRow row in dtMap.Rows)
				{
					if (row["Expression"].ToString() != "")
					{
						sLinkName = Convert.ToString(row["SQLFieldName"]);
						if (sLinkName.StartsWith("LNK_"))
						{
							if (Convert.IsDBNull(row["LinkKeyField"]))
							{
								//log error
								LogLine("0", "Process links: Key field for " + sLinkName + " is not set", "Failed");
							}
							else
							{
								sLinkKeyField = Convert.ToString(row["LinkKeyField"]);

								//Prefix link name with TXT_ or MMO_
								if (goTR.FromTo(goData.LKGetType(sFileName, sLinkName), 2, 2) == "1")
								{
									//1 link
									sTempField = "TXT_" + sLinkName;
								}
								else
								{
									//N link
									sTempField = "MMO_" + sLinkName;
								}
								sSQL = goSchema.GenerateLinkCreationSQL(sFileName, sTempField, -1, -1, true, sImpJobID, clC.SELL_LinkProcessMode_ImportID, "", sLinkKeyField);
								if (sSQL != "")
								{
									//execute query
									bResult = goData.RunSQLQuery(sSQL);
									if (bResult)
									{
										LogLine("0", "Process links: " + sLinkName, "OK");
									}
									else
									{
										LogLine("0", "Process links: " + sLinkName, "Failed");
									}
								}
							}
						}
					}
				}

				//bResult = goSchema.ImportProcessFields(sFileName, , , , True, True, sImpJobID)
				//If bResult Then
				//    LogLine(0, "Process Links", "OK")
				//Else
				//    LogLine(0, "Process Links", "Failed")
				//End If

				return true;

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
				return false;

			}

		}

		public bool PostProcessSysNames()
		{

			//PURPOSE:
			//		Run sys_name creation
			//CALLED FROM:
			//       calling UI
			//PARAMETERS:

			//RETURNS:
			//		True/False
			//AUTHOR: PJ

			clSchema goSchema = new clSchema();
			bool bResult;

			bResult = goSchema.ImportUpdateSysNames(sFileName + "|" + sFileName, iHighestBIID, -1, 50, "", "", false);

			if (bResult)
			{
				LogLine("0", "Generate SysNames", "OK");
			}
			else
			{
				LogLine("0", "Generate SysNames", "Failed");
			}

			return true;

		}

		public bool ConvertDateTimes(string sSQLTableName, string sFieldName, int par_iMinutes, string sImpJobID = "")
		{
			//CS added 2/9/10. HSI site update continually failed because this function was missing.
			//PURPOSE:
			//		Adjust times for GMT offset
			//CALLED FROM:
			//       
			//PARAMETERS:
			//		sSQLTableName: name of SQL table
			//       iTimeOffsetMinutes: minutes to adjust the time (6 hrs = 480 min)
			//       sImpJobID: value of TXT_ImpJobID used for filtering records to update
			//RETURNS:
			//		True/False
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			try
			{

				//LogLine(0, "ConvertDateTimes", "Started")

				clProject goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
				if (goP == null)
				{
					LogLine("0", sProc + ": Unable to retreive session objects", "Failed");
					return false;
				}

				DataTable dt = new DataTable();
				string settings = Convert.ToString(HttpContext.Current.Session["Connection"]);
				System.Data.SqlClient.SqlConnection sqlConnection1 = new System.Data.SqlClient.SqlConnection(settings);

				sqlConnection1.Open();

				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
				string sSQL = "";

				sSQL = "UPDATE [" + sSQLTableName + "]" + "\r" + "\n";
				sSQL = sSQL + "SET [" + sFieldName + "] = DateAdd(mi, " + par_iMinutes + ", [" + sFieldName + "])" + "\r" + "\n";
				sSQL = sSQL + "WHERE " + "\r" + "\n";
				sSQL = sSQL + "[" + sFieldName + "] is not null" + "\r" + "\n";
				sSQL = sSQL + " and [" + sFieldName + "] <> '1753-01-02 23:59:59.000'" + "\r" + "\n";
				if (sImpJobID != "")
				{
					sSQL = sSQL + " and [TXT_ImpJobID = '" + sImpJobID + "'";
				}

				cmd.CommandText = sSQL;
				cmd.CommandType = CommandType.Text;
				cmd.Connection = sqlConnection1;

				if (cmd.ExecuteNonQuery() > 0)
				{
					sqlConnection1.Close();
					return true;
				}
				else
				{
					sqlConnection1.Close();
					return false;
				}

			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & ": " & ex.Message, "Failed")
				return false;

			}

		}

		public DataTable RunSQLSelect(string sSQLStatement, string sDataTableName = "")
		{

			//PURPOSE:
			//		Generic RunSQLSELECT
			//CALLED FROM:
			//       Initialize
			//PARAMETERS:
			//		sSQLStatement
			//RETURNS:
			//		Datatable
			//AUTHOR: PJ

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

			DataTable dt = new DataTable();
			string settings = Convert.ToString(HttpContext.Current.Session["Connection"]);
			System.Data.SqlClient.SqlConnection sqlConnection1 = new System.Data.SqlClient.SqlConnection(settings);

			try
			{

				sqlConnection1.Open();

				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader reader = null;
				Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();

				//Try
				cmd.CommandText = sSQLStatement;
				cmd.CommandType = CommandType.Text;
				cmd.Connection = sqlConnection1;

				reader = cmd.ExecuteReader();

				if (reader.HasRows)
				{

					dt.Load(reader);

				}
				else
				{
					//==> raise error
				}

				reader.Close();
				sqlConnection1.Close();

				dt.TableName = sDataTableName;

				return dt;
			}
			catch (Exception ex)
			{

				//LogLine(0, sProc & ": " & ex.Message, "Failed")
				return null;

			}

		}

		private string RemoveFieldCodeTags(string par_sValue)
		{

			string sValue = par_sValue;
			sValue = sValue.Replace("(%", "");
			sValue = sValue.Replace("%)", "");

			return sValue;

		}



	}

}
