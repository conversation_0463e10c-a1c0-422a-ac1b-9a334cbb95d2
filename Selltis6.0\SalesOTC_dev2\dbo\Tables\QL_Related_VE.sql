﻿CREATE TABLE [dbo].[QL_Related_VE] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_QuotLine_Related_Vendor_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_QL] UNIQUEIDENTIFIER NOT NULL,
    [GID_VE] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_QL_Related_VE] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_QL_Related_VE] FOREIGN KEY ([GID_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_VE_Connected_QL] FOREIGN KEY ([GID_QL]) REFERENCES [dbo].[QL] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[QL_Related_VE] NOCHECK CONSTRAINT [LNK_QL_Related_VE];


GO
ALTER TABLE [dbo].[QL_Related_VE] NOCHECK CONSTRAINT [LNK_VE_Connected_QL];


GO
CREATE CLUSTERED INDEX [IX_VE_Connected_QL]
    ON [dbo].[QL_Related_VE]([GID_QL] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_Related_VE]
    ON [dbo].[QL_Related_VE]([GID_VE] ASC);

