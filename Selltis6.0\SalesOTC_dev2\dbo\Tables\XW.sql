﻿CREATE TABLE [dbo].[XW] (
    [GID_ID]                         UNIQUEIDENTIFIER CONSTRAINT [DF_XW_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'XW',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                         BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                       NVARCHAR (80)    NULL,
    [DTT_CreationTime]               DATETIME         CONSTRAINT [DF_XW_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]                   TINYINT          NULL,
    [TXT_ModBy]                      VARCHAR (4)      NULL,
    [DTT_ModTime]                    DATETIME         CONSTRAINT [DF_XW_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_CustomWorkgroupOptionsName] NVARCHAR (50)    NULL,
    [MMO_ImportData]                 NTEXT            NULL,
    [SI__ShareState]                 TINYINT          CONSTRAINT [DF_XW_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]               UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]                   VARCHAR (50)     NULL,
    CONSTRAINT [PK_XW] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_XW_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[XW] NOCHECK CONSTRAINT [LNK_XW_CreatedBy_US];


GO
CREATE NONCLUSTERED INDEX [IX_XW_TXT_ImportID]
    ON [dbo].[XW]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XW_ModDateTime]
    ON [dbo].[XW]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XW_CreationTime]
    ON [dbo].[XW]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_XW_BI__ID]
    ON [dbo].[XW]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XW_Name]
    ON [dbo].[XW]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XW_CustomWorkgroupOptionsName]
    ON [dbo].[XW]([TXT_CustomWorkgroupOptionsName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XW_CreatedBy_US]
    ON [dbo].[XW]([GID_CreatedBy_US] ASC);

