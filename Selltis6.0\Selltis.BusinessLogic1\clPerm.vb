Imports System.Web

'Owner: MI

Public Class clPerm
    'MI 10/24/13 LoginAdd, LoginEdit, LoginEditIgnorePassword: Deprecated par_dtBillingStartTime, par_dtDeactivationTime; added par_bBill.
    'MI 5/10/13 LoginAdd: Changed the duplicate login message to: 'The login record can't be added because a user login or login group with the same name exists. Please assign a unique name.'
    'MI 4/24/12 LoginAdd: removed test connection-closing code that was inadvertently left in.
    'MI 12/16/11 Added GenerateTemporaryLoginPassword, commented redundant sProc lines.
    'MI 3/13/07 Created for managing permissions access to/from XP table.
    'PURPOSE:
    '       Manage user permissions. Permissions are stored in the XP table,
    '       which is similar to the MD (metadata) table. Permissions data
    '       is organized in sections (GLOBAL for workgroup-level permissions,
    '       user ID for user-specific permissions) and pages (groups of property/
    '       value pairs that can be read with a single method). Individual properties
    '       (Admin=, AUTHOR=, AC_READ_FILTER=, etc.) can be assigned values.
    '       Pages can be read as ini strings using PageRead, and each property 
    '       can be read from the resulting string with goTr.StrRead.
    '       If a local (user's) permission isn't defined, a global value for the
    '       same property is sought and returned if found. If neither value
    '       is found, the default value is returned. If a default is not defined
    '       in the reading method, "" is returned.
    '       Values are stored as strings. Remember to convert them to the proper
    '       type, for example by using goTr.StringToCheckbox() when you need to
    '       convert a "1" or "0" into True/False.
    '       Unlike metadata, permissions do not support the language notion.
    '       Do not prefix properties with langauge codes (ex: US_NAME)

    Dim goP As clProject
    'Dim goMeta As clMetaData
    Dim goTR As clTransform
    Dim goData As clData
    Dim goErr As clError
    Dim goLog As clLog
    'Dim goDef As clDefaults
    Dim goPerm As clPerm
    Public oMetaConnection As SqlClient.SqlConnection = Nothing

    Public Function GenerateTemporaryLoginPassword() As String
        'MI 12/15/11 Added. Used in diaAdmPerm.aspx.

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Return Left(goData.GenGuid(), 12)

        'Alternative method with more control over how the password looks. From RH:

        'Dim charset As String = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890-+=\|/()[]{}*&^%$#@!"
        'Dim pass As String = ""
        'Dim r As New Random

        'For i As Integer = 0 To lenPass - 1
        '    pass += charset(r.Next(0, charset.Length))
        'Next

        'Return pass

    End Function

    Public Function GetTemporaryPasswordExpirationTime(Optional ByVal par_sTZPerspective As String = "LOCAL") As DateTime
        'MI 3/6/12 Changed from 60 to 7.
        'MI 2/8/12 Made the parameter optional.
        'MI 1/9/12 Returning the time in local time zone.
        'MI 12/16/11 Added
        'PURPOSE:
        '       This is when the temporary password expires.
        '       Generate the temp password expiration time in local, server, or UTC time. 
        '       Can be tweaked here to be based on metadata, customized, etc. For now, it is 60 days.
        'RETURNS:
        '       Datetime: expiration datetime.

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Select Case par_sTZPerspective
            Case "LOCAL"
                Return goTR.UTC_ServerToLocal(DateAdd(DateInterval.Day, 7, Now))
            Case "SERVER"
                Return DateAdd(DateInterval.Day, 7, Now)
            Case Else
                'UTC
                Return goTR.UTC_ServerToUTC(DateAdd(DateInterval.Day, 7, Now))
        End Select

    End Function

    Public Function ResetAndSendPassword(ByVal par_sCase As Integer, _
                                         ByVal par_sLoginName As String, _
                                         ByVal par_sSendToAddress As String, _
                                         Optional ByVal par_sSendMethod As String = "EMAIL", _
                                         Optional ByRef par_sTempPassword As String = "", _
                                         Optional ByRef par_dtTempPasswordExpirationTime As DateTime = clC.SELL_BLANK_DTDATETIME, _
                                         Optional ByRef par_sMessage As String = "", _
                                         Optional ByRef par_sErrorCode As String = "", _
                                         Optional ByRef par_sErrorMessage As String = "") As Boolean
        'MI 2/2/12 Created.
        'PURPOSE:
        '       Generate a new password and send an email to the user. If par_sCase='SELFSIGNUP',
        '       also write the new password and other data to the XU record.
        '       To generate the password only, run clPerm.GenerateTemporaryLoginPassword().
        'PARAMETERS:
        '       par_sCase: the following values are currently supported: 
        '           ADMINRESET      Admin resetting the password in diaAdmPerm; XU record not edited
        '           ADMINWELCOME    Admin welcoming a new user in diaAdmPerm; XU record not edited
        '           SELFSIGNUP      Self-registered eval user signing up; XU record edited
        '       par_sLoginName: The value of XU.TXT_LogonName. There is a unique constraint on that column in 
        '           the database. Currently only used in SELFSIGNUP case. Pass "" otherwise.
        '       par_sSendToAddress: e-mail address, e.g. '<EMAIL>'.
        '       par_sSendMethod: Optional: 'EMAIL'. May support other delivery method we may support in the future. 
        '           Keep the supported values the same as in TEMPPASSWORDDELIVERYMETHOD (see diaAdmPerm).
        '           Anything other than 'EMAIL' (e.g. 'VERBAL') will raise an error.
        '       par_sTempPassword: optional ByRef, returns the generated password, if of interest in the calling code.
        '       par_dtTempPasswordExpirationTime: optional ByRef, returns the datetime when the temporary password expires.
        '       par_sMessage: optional ByRef, returns the message to display in a messagebox to the user (admin).
        '       par_sErrorCode: optional ByRef, returns the error code if an error occurred.
        '       par_sErrorMessage: optional ByRef, returns the error message if an error occurred.
        'RETURNS:
        '       True or false: success or failure. In case of failure, error code and
        '       message can be retrieved from par_sErrorCode and par_sErrorMessage params.
        '       
        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim sPassword As String
        Dim sBody As String
        Dim doRS As clRowSet = Nothing
        Dim dtTempPasswordGeneratedTime As DateTime

        'Try
        'Add cases for all supported methods
        Select Case par_sSendMethod
                Case "EMAIL"
                    'Supported
                Case Else
                    goErr.SetError(10103, sProc, , "par_sSendMethod", sProc, par_sSendMethod)
                    '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
            End Select

            Select Case par_sCase
                Case "SELFSIGNUP"
                    'We edit the XU record below
                    doRS = New clRowSet("XU", clC.SELL_EDIT, "TXT_LogonName='" & par_sLoginName & "'", , "CHK_PasswordIsTemporary, DTT_TempPasswordExpirationTime, DTT_TempPasswordGeneratedTime, TXT_TempPasswordDeliveryMethod, TXT_TempPasswordGeneratedBy, TXT_TempPasswordSentTo, DTT_PasswordChangedTime")
                    If doRS.Count < 1 Then
                        par_sErrorCode = ""
                        par_sErrorMessage = "Login '" & par_sLoginName & "' not found."
                        par_sMessage = par_sErrorMessage & " The login may have been deleted since you opened it. The password was not reset."
                        Return False
                    Else
                        'The record was found, we'll edit it below
                    End If
                Case Else
                    'XU record won't be edited here, be sure the calling code edits it!
            End Select

            'Generate the temporary password
            sPassword = goPerm.GenerateTemporaryLoginPassword()
            dtTempPasswordGeneratedTime = goTR.UTC_ServerToUTC(Now)
            par_dtTempPasswordExpirationTime = GetTemporaryPasswordExpirationTime("UTC")

            sBody = "Your Selltis password has been reset. You have been assigned the following temporary password:" & vbCrLf & vbCrLf
            sBody &= sPassword & vbCrLf & vbCrLf
            sBody &= "It will expire on " & goTR.DateToString(par_dtTempPasswordExpirationTime) & " UTC. " & vbCrLf & vbCrLf
            sBody &= "After logging in with your temporary password, you will be prompted to enter a new password." & vbCrLf & vbCrLf
            sBody &= "If you need assistance, e-mail our support <NAME_EMAIL> or call ************ Monday - Friday, 8am - 5pm CST." & vbCrLf & vbCrLf
            sBody &= "Thank you for using Selltis." & vbCrLf & vbCrLf
            sBody &= "The Selltis Support Team" & vbCrLf & vbCrLf & "Selltis, LLC" & vbCrLf & "http://www.selltis.com" & vbCrLf
            sBody &= "Tel: (************* (support)" & vbCrLf & "Tel: (************* (main)" & vbCrLf & "3500 Hwy 190, Ste 200" & vbCrLf & "Mandeville, LA 70471" & vbCrLf & "USA"

            '--> Add other send method Cases if appropriate
            Select Case par_sSendMethod.ToUpper
                Case "EMAIL"
                    Dim oSend As New clEmail
                    If Not oSend.SendSMTPEmailNew("Password reset", sBody, par_sSendToAddress, "", "", "", "Selltis Support", "<EMAIL>", , , , , , False) Then
                        'Undo gw.tLogins.SetVals set above?
                        par_sErrorCode = goErr.GetLastError("NUMBER")
                        par_sErrorMessage = goErr.GetLastError("MESSAGE")
                        par_sMessage = goErr.GetLastError("MESSAGE") & " (" & goErr.GetLastError() & "). View, copy, and e-mail the temporary password manually."
                        Return False
                    Else
                        par_sMessage = "Password change notification sent to '" & par_sSendToAddress & "'."
                        'Edit the XU record
                        Select Case par_sCase
                            Case "SELFSIGNUP"
                                doRS.SetFieldVal("CHK_PasswordIsTemporary", "1")
                                doRS.SetFieldVal("DTT_TempPasswordExpirationTime", par_dtTempPasswordExpirationTime, clC.SELL_SYSTEM)
                                doRS.SetFieldVal("DTT_TempPasswordGeneratedTime", dtTempPasswordGeneratedTime, clC.SELL_SYSTEM)
                                doRS.SetFieldVal("TXT_TempPasswordDeliveryMethod", "")
                                doRS.SetFieldVal("TXT_TempPasswordGeneratedBy", "")
                                doRS.SetFieldVal("TXT_TempPasswordSentTo", "")
                                doRS.SetFieldVal("DTT_PasswordChangedTime", "")
                        End Select
                    End If
                Case Else
                    goErr.SetError(10103, sProc, , "par_sSendMethod", sProc, par_sSendMethod)
                    '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
            End Select

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Function

    Public Function GetDataAccessPermLetter(ByVal par_sPermType As String, ByVal par_iPermLevel As Integer, Optional ByVal par_bDefault As Boolean = False) As String
        'MI 3/27/07 Created.
        'PUPROSE:
        '       Return a letter to display in the caption for each file's selective access permissions
        '       for each permission type (Read, add, edit, delete). Note that the permissions level of
        '       -1 (default) is not allowed in this context, only 0-2 (none-full)
        'PARAMETERS:
        '       par_sPermType: A, E, D, or R.
        '       par_iPermLevel: 0 (none), 1 (selective), 2 (full). -1 will raise an error.
        '       par_bDefault: when true [default: False], the letter is returned enclosed in parens, ex: (A) or (r)
        'RETURNS:
        '       String up to 3 characters (parens around the letter when level is default.

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        'goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim sResult As String = ""

        Select Case UCase(par_sPermType)
            Case "R"        'Read
                Select Case par_iPermLevel
                    Case 0    'None
                        If par_bDefault Then
                            sResult = " "   '"( )"
                        Else
                            sResult = " *"
                        End If
                    Case 1    'Selective
                        If par_bDefault Then
                            sResult = "r"
                        Else
                            sResult = "r*"
                        End If
                    Case 2    'Full
                        If par_bDefault Then
                            sResult = "R"
                        Else
                            sResult = "R*"
                        End If
                    Case Else
                        goErr.SetError(10103, sProc, , "par_sPermType", sProc, par_sPermType)
                        '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
                        sResult = ""
                End Select
            Case "A"        'Add
                Select Case par_iPermLevel
                    Case 0    'None
                        If par_bDefault Then
                            sResult = " "
                        Else
                            sResult = " *"
                        End If
                    Case 1    'Selective--NOT SUPPORTED, but not removing to avoid introducing issues (MI 10/30/13)
                        If par_bDefault Then
                            sResult = "a"
                        Else
                            sResult = "a*"
                        End If
                    Case 2    'Full
                        If par_bDefault Then
                            sResult = "A"
                        Else
                            sResult = "A*"
                        End If
                    Case Else
                        goErr.SetError(10103, sProc, , "par_sPermType", sProc, par_sPermType)
                        '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
                        sResult = ""
                End Select
            Case "E"        'Edit
                Select Case par_iPermLevel
                    Case 0    'None
                        If par_bDefault Then
                            sResult = " "
                        Else
                            sResult = " *"
                        End If
                    Case 1    'Selective
                        If par_bDefault Then
                            sResult = "e"
                        Else
                            sResult = "e*"
                        End If
                    Case 2    'Full
                        If par_bDefault Then
                            sResult = "E"
                        Else
                            sResult = "E*"
                        End If
                    Case Else
                        goErr.SetError(10103, sProc, , "par_sPermType", sProc, par_sPermType)
                        '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
                        sResult = ""
                End Select
            Case "D"        'Delete
                Select Case par_iPermLevel
                    Case 0    'None
                        If par_bDefault Then
                            sResult = " "
                        Else
                            sResult = " *"
                        End If
                    Case 1    'Selective
                        If par_bDefault Then
                            sResult = "d"
                        Else
                            sResult = "d*"
                        End If
                    Case 2    'Full
                        If par_bDefault Then
                            sResult = "D"
                        Else
                            sResult = "D*"
                        End If
                    Case Else
                        goErr.SetError(10103, sProc, , "par_sPermType", sProc, par_sPermType)
                        '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
                        sResult = ""
                End Select
            Case Else
                goErr.SetError(10103, sProc, , "par_sPermType", sProc, par_sPermType)
                '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
                sResult = ""
        End Select

        Return sResult
    End Function


    Public Function GetCreatorID(ByVal par_sSection As String, _
                    ByVal par_sPageID As String) As String
        'MI 12/5/06 Created
        'PURPOSE:
        '       Return the ID of the user who created this permissions page.
        'PARAMETERS:
        '       par_sSection: Section ID of the page such as user's GID_ID
        '           or "GLOBAL"
        '       par_sPageID: page ID of the page such as "DSK_Xxxxxx" or
        '           'WOP_WORKGROUP_OPTIONS"
        'RETURNS:
        '       GID_CreatedBy_US value as string.

        'Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        'goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim sSection As String = par_sSection.ToString
        Dim sText As String = ""

        Dim sqlConnection1 As SqlClient.SqlConnection = goData.GetConnection
        Dim cmd As New System.Data.SqlClient.SqlCommand
        Dim reader As SqlClient.SqlDataReader
        Dim mColl As New Collection

        cmd.CommandText = "pPermGetCreatorID"
        'Returns column GID_CreatedBy_US with one record
        cmd.CommandType = CommandType.StoredProcedure
        cmd.Connection = sqlConnection1

        'parameter
        Dim uSec As New System.Data.SqlClient.SqlParameter("@par_uSection", System.Data.SqlDbType.UniqueIdentifier)
        If UCase(sSection) = "GLOBAL" Or sSection = "" Then
            uSec.Value = System.DBNull.Value
        Else
            uSec.Value = goTR.StringToGuid(sSection)
        End If
        cmd.Parameters.Add(uSec)

        'parameter
        Dim sPageID As New System.Data.SqlClient.SqlParameter("@par_sPageID", System.Data.SqlDbType.VarChar)
        sPageID.Value = par_sPageID
        cmd.Parameters.Add(sPageID)

        reader = cmd.ExecuteReader()
        If reader.Read() Then
            sText = reader(0).ToString
        End If

        reader.Close()
        sqlConnection1.Close()

        Return sText

    End Function


    Public Function GetDefaultFeaturePermissions() As String
        'MI 2/20/14 Added GOOGLECAL, GOOGLECONTACT.
        'MI 12/6/13 Added LINK_OUTLOOKTODO.
        'MI 10/29/13 Removed customer admin permission, which are now under Roles. Reordered, commented lines.
        'MI 5/18/11 Added DOCLIBxxx, CUSTADMINxxx.
        'MI 5/13/11 Added CUSTADMINREPORTS
        'MI 5/14/09 added FORMS.
        'MI 3/27/09 Added VIEWS.
        'MI 3/5/09 Added MOBILEREMEMBERME, MOBILEEXPIRELOGININDAYS, MOBILEREMEMBERUSERNAME permissions.
        'MI 2/5/09 Added MOBILE.
        'MI 5/6/08 Added SHAREDDESKTOPS.
        'MI 5/5/08 Added LINK_OUTLOOKCAL and LINK_OUTLOOKCONTACT
        'PURPOSE:
        '       Returns an ini string with feature permission defaults
        '       Permissions that are not explicitly defined here will
        '       have a default of 0.
        '       The only other method where individual permissions are hard-coded
        '       is clProject.GetMe(). Maintain these two methods in sync.

        'Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        'goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim s As String = ""

        goTR.StrWrite(s, "PRINT", "1")
        goTR.StrWrite(s, "TOEXCEL", "0")
        goTR.StrWrite(s, "IMPORT", "0")
        goTR.StrWrite(s, "EXPORT", "0")
        goTR.StrWrite(s, "TRANSFERIN", "0")
        goTR.StrWrite(s, "TRANSFEROUT", "0")
        goTR.StrWrite(s, "LINK_OUTLOOKCAL", "1")
        goTR.StrWrite(s, "LINK_OUTLOOKCONTACT", "1")
        goTR.StrWrite(s, "LINK_OUTLOOKTODO", "1")
        goTR.StrWrite(s, "LINK_GOOGLECAL", "1")
        goTR.StrWrite(s, "LINK_GOOGLECONTACT", "1")
        goTR.StrWrite(s, "MOBILE", "0")
        goTR.StrWrite(s, "MOBILEREMEMBERME", "1")
        goTR.StrWrite(s, "MOBILEEXPIRELOGININDAYS", clC.SELL_MOBILEEXPIRELOGININDAYS.ToString)
        goTR.StrWrite(s, "MOBILEREMEMBERUSERNAME", "1")
        goTR.StrWrite(s, "PERIODICBILLING", "0")
        goTR.StrWrite(s, "APPROVEREIMBURSE", "0")
        goTR.StrWrite(s, "REIMBURSE", "0")
        goTR.StrWrite(s, "SHAREDDESKTOPS", "0")
        goTR.StrWrite(s, "VIEWS", "1")
        goTR.StrWrite(s, "FORMS", "0")
        'MI 10/29/13 Removed customer admin permissions, which are now written under 'roles'
        'goTR.StrWrite(s, "CUSTADMINMANAGELOGINS", "0")
        'goTR.StrWrite(s, "CUSTADMINMANAGEFEATURES", "0")
        'goTR.StrWrite(s, "CUSTADMINMANAGEDATAACCESS", "0")
        'goTR.StrWrite(s, "WORKGROUPOPTIONS", "0")
        'goTR.StrWrite(s, "CUSTADMINREPORTS", "0")
        'goTR.StrWrite(s, "CUSTADMINREASSIGNRECORDS", "0")

        'MI 5/18/11
        goTR.StrWrite(s, "DOCLIBPUBLICCREATEFOLDERS", "0")
        goTR.StrWrite(s, "DOCLIBPUBLICDELETEFOLDERS", "0")
        goTR.StrWrite(s, "DOCLIBPUBLICCREATEFILES", "0")
        goTR.StrWrite(s, "DOCLIBPUBLICDELETEFILES", "0")
        goTR.StrWrite(s, "DOCLIBPERSONALCREATEFOLDERS", "1")
        goTR.StrWrite(s, "DOCLIBPERSONALDELETEFOLDERS", "1")
        goTR.StrWrite(s, "DOCLIBPERSONALCREATEFILES", "1")
        goTR.StrWrite(s, "DOCLIBPERSONALDELETEFILES", "1")
        goTR.StrWrite(s, "DOCRECORDLEVELCREATEFILES", "1")
        goTR.StrWrite(s, "DOCRECORDLEVELDELETEFILES", "1")


        Return s

    End Function

    Public Function GetDataAccessDefaultFilter(ByVal par_sFile As String, Optional ByVal par_sResultType As String = "FILTER") As String
        'MI 9/6/12 Removed CONDSUMMARY. The summary can be fetched dynamically with goTr.GetFilterConditionSummary.
        'PURPOSE:
        '   Get the default filter (Created by me) [default] or condition used in most data access permission.
        'PARAMETERS:
        '   par_sFile: File name, e.g. 'CO'.
        '   par_sResultType: optional: 'FILTER' [default] or 'CONDITION' (value of CONDITION= line in filter ini-style string).

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim sIniString As String = ""

        ' Try

        Select Case par_sResultType
                Case "FILTER"
                    goTR.StrWrite(sIniString, "FILE", par_sFile)
                    goTR.StrWrite(sIniString, "CONDITION", "LNK_CreatedBy_US='<%MEID%>'")
                    goTR.StrWrite(sIniString, "CCOUNT", "1")
                    goTR.StrWrite(sIniString, "C1FIELDNAME", "<%LNK_CREATEDBY_US%>")
                    goTR.StrWrite(sIniString, "C1CONDITION", "0")
                    goTR.StrWrite(sIniString, "C1VALUE1", "<%MEID%>")
                    'MI 9/6/12 Removed CONDSUMMARY. The summary can be fetched dynamically with goTr.GetFilterConditionSummary.
                    'goTR.StrWrite(sIniString, "CONDSUMMARY", "Created by User Is '<Me>'")
                Case "CONDITION"
                    sIniString = "LNK_CREATEDBY_US='<%MEID%>'"
                Case Else
                    goErr.SetError(10103, sProc, , "par_sResultType", sProc, par_sResultType)
            End Select

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return sIniString

    End Function


    Public Function GetSelectiveFilter(ByVal par_sFile As String, Optional ByVal par_sType As String = "R") As String
        'MI 9/19/08 Edited comment.
        'MI 8/8/08 Changed case "R" to "LNK_CreatedBy_US='<%MEID%>'" from "".
        'MI 3/26/07 Changed MD format.
        'MI 3/20/07 Implemented goData.IsFileSystem.
        'MI 3/15/07 Returning "" for system files.
        'PURPOSE:
        '       Return current user's selective permission filter condition for Read, Edit, or Delete
        '       access to a particular file.
        'PARAMETERS:
        '       par_sFile: System file name
        '       par_sType: optional: type of permission. Supported values: 'R', 'E', 'D'.
        '           note that filter conditions are not supported for 'A' (add) access.
        'RETURNS:
        '       String: filter condition to use as CONDITION= property in a
        '           standard SellSQL filter/sort statement. If the condition is not defined
        '           either for the user or in GLOBAL permissions, this method
        '           returns a "default" string, which is:
        '               LNK_CreatedBy_US='<%MEID%>'

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim sResult As String
        Dim sDefault As String = ""

        If goData.IsFileSystem(par_sFile) Then
            'Selective permissions are not allowed for these 'system' files.
            Return ""
        End If

        'Validate parameters
        Select Case UCase(par_sType)
            Case "R"
                sDefault = "LNK_CreatedBy_US='<%MEID%>'"    'MI 8/8/08 Changed from ""
            Case "E", "D"
                sDefault = "LNK_CreatedBy_US='<%MEID%>'"
            Case Else
                goErr.SetError(10103, sProc, , "par_sType", sProc, par_sType)
                '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
        End Select

        sResult = goPerm.LineRead(goP.GetUserLogonID(), "ACCESS", par_sFile & "_" & par_sType & "COND", sDefault)

        Return sResult

    End Function

    Public Function GetSelectivePermission(ByVal par_sFile As String, Optional ByVal par_sType As String = "R") As Integer
        'MI 3/16/09 Changed perm for system files (MD, TN, XL) from 2 for all users
        '       (which was a security threat) to 2 only for admins and full authors
        '       and R:0, A:2, E:0, D:0 (no read, full add, no edit, no delete) for
        '       other users. These permissions are hard-coded.
        'MI 3/26/07 Changed MD format.
        'MI 3/20/07 Implemented goData.IsFileSystem.
        'MI 3/15/07 Added allowing full edit and delete to system files.

        'PURPOSE:
        '       Return current user's permission level for Read, Add, Edit, or Delete access to a particular file.
        'PARAMETERS:
        '       par_sFile: System file name
        '       par_sType: optional: type of permission. Supported values: 'R', 'A', 'E', 'D'.
        'RETURNS:
        '       Integer: permission level: 0 (none), 1 (selective), 2 (full).
        '           For system files, 2 is returned for admins and full authors;
        '           other users get R:0, A:0, E:0, D:0. For all other files,
        '           if a permission is not defined for the user, the GLOBAL default
        '           permission is returned. If the GLOBAL default isn't defined or is -1,
        '           then 2 or 1 is returned depending on the access type:
        '               Read, Add: 2 (full)
        '               Edit, Delete: 1 (selective)
        'NOTE MI 3/16/09 
        '       System files like MD, TN and XL are off limit to regular users.
        '       Admins and full authors have full access to them.
        '       IMPORTANT: scripts that access these files under a non-admin/author user login
        '       will fail. You will have to code around this limitation. You can use one of
        '       these techniques:
        '          1. Set par_bSysFileFullPermission parameter in clRowset.New to True.
        '              The rowset will read, add, edit, and delete data with full permissions.
        '          2. Pass all parameters to the rowset in the ini parameter with
        '              ACCESSTYPE= property set to the letter that represents the 
        '              permission which the rowset should use. For example, assuming that the
        '              user has a full add permission, but no read, edit, or delete permission
        '              ( - A - - ), set ACCESSTYPE=A and the rowset's reading, writing and deleting
        '              will run under the 'add' permission for the logged on user.
        '          3. Put your code in clScripts.AutoAlertEveryDay or AutoAlertEveryNSecs, which
        '              run hourly and every minute, respectively. This is not feasible when an 
        '              immediate UI response is required, but will work well for batch updates
        '              and other delayed updates that should be performed under SYSTEM login.

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim sResult As String
        Dim iResult As Integer
        Dim sDefault As String = "2"

        If goData.IsFileSystem(par_sFile) Then
            If goP.IsUserAdmin() Or goP.IsUserAuthor() = 2 Then
                'Admins and full authors have no restriction on system files
                Return 2  'full in all cases, R, A, E, and D
            Else
                Select Case UCase(par_sType)
                    Case "R"
                        Return 0
                    Case "A"
                        Return 0
                    Case "E"
                        Return 0
                    Case "D"
                        Return 0
                    Case Else
                        goErr.SetError(10103, sProc, , "par_sType", sProc, par_sType)
                        '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
                End Select
            End If
        End If

        'Validate parameters
        Select Case UCase(par_sType)
            Case "R", "A"
                sDefault = "2"  'full
            Case "E", "D"
                sDefault = "1"  'selective
            Case Else
                goErr.SetError(10103, sProc, , "par_sType", sProc, par_sType)
                '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
        End Select

        sResult = goPerm.LineRead(goP.GetUserLogonID(), "ACCESS", par_sFile & "_" & par_sType, sDefault)
        'Replace 'default' (-1) with an actual permission
        If sResult = "-1" Then sResult = sDefault
        iResult = goTR.StringToNum(sResult)
        If iResult > 2 Or iResult < 0 Then
            goErr.SetError(35000, sProc, "Unsupported value returned by goPerm.LineRead for login '" & goP.GetUserLogon() & "', login ID '" & goP.GetUserLogonID() & "', user: '" & goP.GetUserTID() & " (" & goP.GetMe("NAME") & "), par_sFile: '" & par_sFile & "', par_sType: '" & par_sType & "'. Value: '" & sResult & "'.")
        End If

        Return iResult

    End Function

    Public Function GetUserPermission(ByVal par_sUserID As String, ByVal par_sType As String) As String
        'MI 9/23/08 Added support for @par_sType parameter in the call to pGetUserLogon.
        'MI 4/23/07 Created.
        'PURPOSE:
        '       Returns roles and feature permissions for any user. For data access permissions,
        '       use goPerm.GetSelectivePermission().
        'PARAMETERS:
        '       par_sUserID: User record GID_ID.
        '       par_sType: Permission type to return:
        '           LOGONNAME: name with which the user logs into the system
        '           LOGONID: ID of the XU (user login) record by which permissions are referenced in XP file.
        '           AUTHOR: Author permissions, can be 0: none, 1: partial; 2: full
        '           ADMIN: Admin permission, can be 0: none, 1: true
        '           <any feature permission such as IMPORT, EXPORT, TRANSFERIN, PERIODICBILLING, REIMBURSE, etc.>
        '               "0" is returned if the feature is not defined
        'RETURNS:
        '       String: requested permission.

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim sType As String = UCase(par_sType)
        Dim sRoles As String
        Dim sPermissions As String
        Dim sLogonID As String      'GID_ID of the XU record (User logon)
        Dim sLogonName As String

        sRoles = goPerm.PageRead(goP.GetUserLogonID(), "ROLES")
        sPermissions = goPerm.PageRead(goP.GetUserLogonID(), "FEATURES")

        Dim sqlConnection1 As SqlClient.SqlConnection = goData.GetConnection
        Dim cmd As New System.Data.SqlClient.SqlCommand
        Dim reader As SqlClient.SqlDataReader
        Dim mColl As New Collection

        cmd.CommandText = "pGetUserLogon"
        'Returns column GID_CreatedBy_US with one record
        cmd.CommandType = CommandType.StoredProcedure
        cmd.Connection = sqlConnection1

        'parameter
        Dim uSec As New System.Data.SqlClient.SqlParameter("@par_uGID", System.Data.SqlDbType.UniqueIdentifier)
        uSec.Value = goTR.StringToGuid(par_sUserID)
        cmd.Parameters.Add(uSec)

        'parameter
        Dim sMode As New System.Data.SqlClient.SqlParameter("@par_sType", System.Data.SqlDbType.VarChar)
        sMode.Value = "LOGIN"
        cmd.Parameters.Add(sMode)

        reader = cmd.ExecuteReader()
        If reader.Read() Then
            sLogonID = reader(0).ToString
            sLogonName = reader(1).ToString
        Else
            goErr.SetWarning(35000, sProc, "Permissions cannot be evaluated because user login was not found for user '" & par_sUserID & "'.")
            Return ""
        End If

        reader.Close()
        sqlConnection1.Close()

        sRoles = PageRead(sLogonID, "ROLES")
        sPermissions = PageRead(sLogonID, "FEATURES")

        Select Case sType
            Case "LOGONNAME"
                Return sLogonName
            Case "LOGONID"
                Return sLogonID
            Case "AUTHOR"
                Return goTR.StrRead(sRoles, "AUTHOR", "0")
            Case "ADMIN"
                Return goTR.StrRead(sRoles, "ADMIN", "0")
            Case Else
                Return goTR.StrRead(sPermissions, par_sType, "0", False)
        End Select

    End Function

    Public Function GetUserLogin(ByVal par_sUserID As String, Optional ByVal par_sType As String = "ID") As String
        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)
        'MI 3/17/14 Created.
        'PURPOSE:
        '       Return A Login ID or Login Name for the provided User's GID_ID.
        'PARAMETERS:
        '       par_sUserID: User record GID_ID
        '       par_sType: What to return: 
        '           ID: Login ID (GID_ID of the record in the XU file) [default]
        '           NAME: TXT_LogonName of the login
        'RETURNS:
        '       String: Login GID_ID or TXT_LogonName depending on par_sType for
        '           User with GID_ID = par_sUserID

        Dim sReturn As String = ""

        'Try
        Dim sqlConnection1 As SqlClient.SqlConnection = goData.GetConnection
            Dim cmd As New System.Data.SqlClient.SqlCommand
            Dim reader As SqlClient.SqlDataReader
            Dim mColl As New Collection

            cmd.CommandText = "pGetUserLogon"
            'Returns column GID_CreatedBy_US with one record
            cmd.CommandType = CommandType.StoredProcedure
            cmd.Connection = sqlConnection1

            'parameter
            Dim uSec As New System.Data.SqlClient.SqlParameter("@par_uGID", System.Data.SqlDbType.UniqueIdentifier)
            uSec.Value = goTR.StringToGuid(par_sUserID)
            cmd.Parameters.Add(uSec)

            'parameter
            Dim sMode As New System.Data.SqlClient.SqlParameter("@par_sType", System.Data.SqlDbType.VarChar)
            sMode.Value = "LOGIN"
            cmd.Parameters.Add(sMode)

            reader = cmd.ExecuteReader()
            If reader.Read() Then
                If par_sType.ToUpper = "ID" Then
                    sReturn = reader(0).ToString
                Else
                    sReturn = reader(1).ToString
                End If
            Else
                goErr.SetError(35000, sProc, "User Login cannot be returned because there is no User Login for User '" & par_sUserID & "'.")
                sReturn = ""
            End If

            reader.Close()
            sqlConnection1.Close()

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return sReturn

    End Function

    Public Function IsObjectShared(ByVal par_sPageID As String) As Boolean

        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '       Determines whether a permissions page is shared ("GLOBAL" section)
        'PARAMETERS:
        '		par_sPageID: ID of the permission page
        'RESULT:
        '		Boolean

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        '*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
        Dim oConnection As SqlClient.SqlConnection = goData.GetConnection
        Dim oCommand As New SqlClient.SqlCommand
        Dim oReader As SqlClient.SqlDataReader
        Dim bReturn As Boolean = False
        Dim sPage As String = par_sPageID

        '  Try

        'sql command
        oCommand.CommandText = "fnPermIsPageShared"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter 
            Dim sPageParam As New SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar)
            sPageParam.Value = sPage
            oCommand.Parameters.Add(sPageParam)

            'return parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Bit)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            'execute sproc

            oReader = oCommand.ExecuteReader

            'Now you can grab the output parameter's value...
            bReturn = Convert.ToBoolean(retValParam.Value)

            oReader.Close()
            oConnection.Close()

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return bReturn

    End Function

    Public Function IsGroup(ByVal par_sLoginID As String) As Boolean
        'MI 9/8/09 Created.
        'PURPOSE:
        '       Test whether a Login is a Login or Login Group
        'PARAMETERS:
        '       par_sLoginID: GID_ID of the Login (XU) record to test.

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        'Try
        'Find out whether the Login to copy is a Login ot Login Group
        Dim oConnection As SqlClient.SqlConnection = goData.GetConnection
            Dim oCommand As New System.Data.SqlClient.SqlCommand
            Dim oReader As System.Data.SqlClient.SqlDataReader
            oCommand.CommandText = "SELECT TOP 1 [CHK_LoginGroup] FROM [XU] WHERE [GID_ID] = '" & par_sLoginID & "'"
            oCommand.CommandType = System.Data.CommandType.Text
            oCommand.Connection = oConnection
            'execute
            oReader = oCommand.ExecuteReader()
            'read returned value
            If oReader.HasRows Then
                'iCount = 1
                Do While oReader.Read()
                    If oReader("CHK_LoginGroup").ToString = "True" Then
                        Return True
                    End If
                Loop
            End If
            oReader.Close()
            oConnection.Close()
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
        Return False
    End Function


    Public Function LineDelete(ByVal par_sSection As String, ByVal par_sPage As String, ByVal par_sLine As String) As Boolean
        'MI 8/17/06 Added treating blank par_sSection treated as global.
        '7/14/06 MI Returning False if sProc returns <> 0.

        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Delete a line in a permission page
        'PARAMETERS:
        '		par_sSection	=	Section in which the line must be deleted
        '		par_sPage		=	Page in which the line must be deleted
        '		par_sLine		=	Line/property to delete
        'RETURNS:
        '		True if found and Deleted, false if not found (also OK, just nothing to delete)
        'HOW IT WORKS:
        '		Calls sProc pPermLineDelete and gets return value
        'EXAMPLE:
        '		goPerm.LineDelete(goP.GetUserTID(), "FEATURES", "PRINT")

        '==> Add error checking in sproc

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        'goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim sSection As String = UCase(Trim(par_sSection))
        Dim sPage As String = UCase(Trim(par_sPage))
        Dim sLine As String = UCase(Trim(par_sLine))
        Dim iResult As Integer

        Try

            '*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
            Dim oConnection As SqlClient.SqlConnection = goData.GetConnection
            Dim oCommand As New SqlClient.SqlCommand
            Dim oReader As SqlClient.SqlDataReader

            oCommand.CommandText = "pPermLineDelete"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter
            Dim uSection As New SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier)
            If sSection = "GLOBAL" Or sSection = "" Then
                uSection.Value = System.DBNull.Value
            Else
                uSection.Value = goTR.StringToGuid(sSection)
            End If
            oCommand.Parameters.Add(uSection)

            'parameter
            Dim strPage As New SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar)
            strPage.Value = sPage
            oCommand.Parameters.Add(strPage)

            'parameter
            Dim strProperty As New SqlClient.SqlParameter("@par_sProperty", SqlDbType.VarChar)
            strProperty.Value = sLine
            oCommand.Parameters.Add(strProperty)

            'return parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            'execute

            oReader = oCommand.ExecuteReader()

            'Now you can grab the output parameter's value...
            iResult = Convert.ToInt16(retValParam.Value)

            oReader.Close()
            oConnection.Close()

            If iResult = 0 Then
                Return True
            Else
                Return False 'but no error checking in sproc yet, so this should be never returned
            End If

        Catch ex As Exception

            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If

            Return False

        End Try

    End Function

    Public Function LineRead(ByVal par_sSection As String, _
                    ByVal par_sPage As String, _
                    ByVal par_sLine As String, _
                    Optional ByVal par_sDefaultValue As String = "", _
                    Optional ByVal par_bUserOnly As Boolean = False) As String
        'MI 1/19/07 Added replacing Chr(1) & Chr(1) with vbcrlf
        'MI 8/17/06 Added treating blank par_sSection treated as global.
        'MI 5/26/06 Modified comments.
        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Read a permission line. If par_bUserOnly is False (default), if the user's permission
        '       is not defined (left on default), the Login Group's value is returned. If not defined,
        '       the master default (User Login '<%DEFAULT%>') value is returned. If not defined,
        '       par_sDefaultValue is returned.
        'PARAMETERS:
        '		par_sSection		=	Section of the permission page from which to read a property's value.
        '                               Can be 'GLOBAL' or User's GID as string. If not 'GLOBAL' and 
        '                               par_bUserOnly is False, the Login Group's or GLOBAL value is returned 
        '                               if the property (par_sLine) is not found.
        '		par_sPage			=	Permission 'Page' such as "ACCESS", "FEATURES", or "ROLES".
        '		par_sLine			=	TXT_Property for which to read TXT_Value. Ex: AC_R for Activity read
        '                               permission or TRANSFERIN for a transfer in feature permission.
        '		par_sDefaultValue	= 	Default value to be returned if Section, Login Group or GLOBAL line was found.
        '		par_bUserOnly		=	if TRUE, does not read the Login Group or GLOBAL values. Default is False.
        'RETURNS:
        '		The content found or "".
        'HOW IT WORKS:
        '		calls udf fnPermLineRead with adequate parameters (already doing the merging) and returns the result.
        'EXAMPLE:
        '       Dim bAdmin as boolean
        '		bAdmin = goTr.StringToCheckbox(goPerm.LineRead(goP.GetUserTID(), "ROLES ", "Admin", "0"))

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        'goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim sSection As String = UCase(Trim(par_sSection))
        Dim sPage As String = UCase(Trim(par_sPage))
        Dim sLine As String = UCase(Trim(par_sLine))
        Dim sDefaultValue As String = par_sDefaultValue
        Dim bUserOnly As Boolean = par_bUserOnly
        Dim sLang As String = ""

        Try

            If oMetaConnection Is Nothing Then
                oMetaConnection = goData.GetConnection
            End If
            Dim oConnection As SqlClient.SqlConnection = oMetaConnection
            Dim oCommand As New SqlClient.SqlCommand
            Dim oReader As SqlClient.SqlDataReader
            Dim sResult As String = ""

            oCommand.CommandText = "fnPermLineRead"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter
            Dim uSection As New SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier)
            If sSection = "GLOBAL" Or sSection = "" Then
                uSection.Value = System.DBNull.Value
            Else
                uSection.Value = goTR.StringToGuid(sSection)
            End If
            oCommand.Parameters.Add(uSection)

            'parameter
            Dim strPage As New SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar)
            strPage.Value = sPage
            oCommand.Parameters.Add(strPage)

            'parameter
            Dim strProperty As New SqlClient.SqlParameter("@par_sProperty", SqlDbType.VarChar)
            strProperty.Value = sLine
            oCommand.Parameters.Add(strProperty)

            'parameter
            Dim strDefaultValue As New SqlClient.SqlParameter("@par_sDefaultValue", SqlDbType.NVarChar)
            strDefaultValue.Value = sDefaultValue
            oCommand.Parameters.Add(strDefaultValue)

            'parameter
            Dim blnUserOnly As New SqlClient.SqlParameter("@par_bUserOnly", SqlDbType.Bit)
            blnUserOnly.Value = bUserOnly
            oCommand.Parameters.Add(blnUserOnly)

            'return parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.NVarChar)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            'execute

            oReader = oCommand.ExecuteReader()

            'Now you can grab the output parameter's value...
            sResult = Convert.ToString(retValParam.Value)
            sResult = goTR.Replace(sResult, Chr(1) & Chr(1), vbCrLf)

            oReader.Close()
            'oConnection.Close()

            Return sResult

        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If

            Return ""

        End Try

    End Function

    Public Function LineWrite(ByVal par_sSection As String, _
                ByVal par_sPage As String, _
                ByVal par_sLine As String, _
                ByVal par_vValue As Object, _
                Optional ByRef par_oConnection As SqlClient.SqlConnection = Nothing) As Boolean
        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Writes a line in a permission page
        'PARAMETERS:
        '		par_sSection	=	Section in which the line must be deleted
        '		par_sPage		=	Page in which the line must be deleted
        '		par_sLine		=	Line/property to delete
        '       par_sValue      =   Value to write for this line
        '       par_oConnection =   Optional SQL connection passed by PageWrite
        'RETURNS:
        '		True if successful write, false if not
        'HOW IT WORKS:
        '		Calls sProc pPermLineWrite and gets return value
        'EXAMPLE:
        '		goPerm.LineWrite("GLOBAL", "ACCESS", "AUTHOR", "2")

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        'goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim sSection As String = Trim(UCase(par_sSection))
        Dim sPage As String = Trim(par_sPage)
        Dim sLine As String = Trim(par_sLine)
        Dim sValue As String = par_vValue.ToString  'Trim(par_vValue.ToString)
        Dim sLang As String = ""
        Dim iResult As Integer = 0

        'Replace CRs with two characters 1
        sValue = goTR.Replace(sValue, vbCrLf, Chr(1) & Chr(1))

        'Try

        Dim oConnection As SqlClient.SqlConnection
            If par_oConnection Is Nothing Then
                oConnection = goData.GetConnection
            Else
                oConnection = par_oConnection
            End If

            Dim oCommand As New SqlClient.SqlCommand
            Dim oReader As SqlClient.SqlDataReader

            oCommand.CommandText = "pPermLineWrite"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter
            Dim uSection As New SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier)
            If sSection = "GLOBAL" Or sSection = "" Then
                uSection.Value = System.DBNull.Value
            Else
                uSection.Value = goTR.StringToGuid(sSection)
            End If
            oCommand.Parameters.Add(uSection)

            'parameter
            Dim strPage As New SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar)
            strPage.Value = sPage
            oCommand.Parameters.Add(strPage)

            'parameter
            Dim strProperty As New SqlClient.SqlParameter("@par_sProperty", SqlDbType.VarChar)
            strProperty.Value = sLine
            oCommand.Parameters.Add(strProperty)

            'parameter
            Dim strValue As New SqlClient.SqlParameter("@par_sValue", SqlDbType.NVarChar)
            strValue.Value = sValue
            oCommand.Parameters.Add(strValue)

            'return parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            'execute

            oReader = oCommand.ExecuteReader()

            'Now you can grab the output parameter's value...
            iResult = Convert.ToInt16(retValParam.Value)

            oReader.Close()
            If par_oConnection Is Nothing Then
                oConnection.Close()
            End If


            If iResult = 0 Then
                Return True
            Else
                Select Case iResult
                    Case -1 'when fnGetMe returns '' User ID
                        goErr.SetWarning(35503, sProc)
                        '35503: Metadata or permission line couldn't be written because fnGetMe returned an invalid user ID.
                    Case -2 'when @par_sUserCode is ''
                        goErr.SetWarning(35504, sProc)
                        '35504: Metadata or permission line couldn't be written because fnGetMe returned an invalid user code.
                End Select
                Return False
            End If

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

    End Function

    Public Function LoginAdd(ByVal par_sLogonName As String, _
                    ByVal par_sPassword As String, _
                    Optional ByVal par_sUserGIDID As String = "", _
                    Optional ByRef par_sResult As String = "", _
                    Optional ByVal par_bThisIsLoginGroup As Boolean = False, _
                    Optional ByVal par_sGroupGIDID As String = "", _
                    Optional ByVal par_sBillingType As String = "0", _
                    Optional ByVal par_bEnabled As Boolean = False, _
                    Optional ByVal par_bPasswordIsTemporary As Boolean = False, _
                    Optional ByVal par_dtTempPasswordExpirationTime As DateTime = clC.SELL_BLANK_DTDATETIME, _
                    Optional ByVal par_dtTempPasswordGeneratedTime As DateTime = clC.SELL_BLANK_DTDATETIME, _
                    Optional ByVal par_sTempPasswordDeliveryMethod As String = "", _
                    Optional ByVal par_sTempPasswordGeneratedBy As String = "", _
                    Optional ByVal par_sTempPasswordSentTo As String = "", _
                    Optional ByVal par_bPasswordDoesntExpire As Boolean = False, _
                    Optional ByVal par_sPasswordChangedBy As String = "", _
                    Optional ByVal par_dtPasswordChangedTime As DateTime = clC.SELL_BLANK_DTDATETIME, _
                    Optional ByVal par_bBill As Boolean = False) As Boolean

        'Public Function LoginAdd(ByVal par_sLogonName As String, _
        '                ByVal par_sPassword As String, _
        '                Optional ByVal par_sUserGIDID As String = "", _
        '                Optional ByRef par_sResult As String = "", _
        '                Optional ByVal par_bThisIsLoginGroup As Boolean = False, _
        '                Optional ByVal par_sGroupGIDID As String = "", _
        '                Optional ByVal par_sBillingType As String = "0", _
        '                Optional ByVal par_bEnabled As Boolean = False, _
        '                Optional ByVal par_dtBillingStartTime As DateTime = clC.SELL_BLANK_DTDATETIME, _           --> DEPRECATED
        '                Optional ByVal par_dtDeactivationTime As DateTime = clC.SELL_BLANK_DTDATETIME, _           --> DEPRECATED
        '                Optional ByVal par_bPasswordIsTemporary As Boolean = False, _
        '                Optional ByVal par_dtTempPasswordExpirationTime As DateTime = clC.SELL_BLANK_DTDATETIME, _
        '                Optional ByVal par_dtTempPasswordGeneratedTime As DateTime = clC.SELL_BLANK_DTDATETIME, _
        '                Optional ByVal par_sTempPasswordDeliveryMethod As String = "", _
        '                Optional ByVal par_sTempPasswordGeneratedBy As String = "", _
        '                Optional ByVal par_sTempPasswordSentTo As String = "", _
        '                Optional ByVal par_dtPasswordUserChangedTime As DateTime = clC.SELL_BLANK_DTDATETIME, _    --> DEPRECATED
        '                Optional ByVal par_bPasswordDoesntExpire As Boolean = False, _
        '                Optional ByVal par_sPasswordChangedBy As String = "", _
        '                Optional ByVal par_dtPasswordChangedTime As DateTime = clC.SELL_BLANK_DTDATETIME) As Boolean

        'MI 10/24/13 LoginAdd: Added par_bBill.
        'MI 10/23/13 LoginAdd: Deprecating par_dtBillingStartTime, par_dtDeactivationTime.
        'MI 10/4/13 LoginAdd: Added par_sPasswordChangedBy, par_dtPasswordChangedTime parameters.
        'MI 9/24/13 LoginAdd: Added par_bPasswordDoesntExpire parameter.
        'MI 5/10/13 LoginAdd: Changed the duplicate login message to: 'The login record can't be added because a user login or login group with the same name exists. Please assign a unique name.'
        'MI 4/24/12 LoginAdd: removed test connection-closing code that was inadvertently left in.
        'MI 1/3/12 Added support for enablement, billing, and temp password parameters.
        'MI 10/20/09 Added par_sBillingType parameter.
        'MI 8/26/09 Added par_sGroupGIDID parameter.
        'MI 9/23/08 Added par_bThisIsLoginGroup parameter.
        'PURPOSE:
        '       Create a new XU (User login) record. If an error occurs, sets a warning.
        '       Default values in optional parameters assume that a user login is being
        '       created. 
        'PARAMETERS:
        '       par_sLogonName: name under which the user will log on.
        '       par_sPassword: Logon password. If par_bThisIsLoginGroup=True, send "".
        '       par_sUserGIDID: GID_ID of the US record associated. If 
        '           par_bThisIsLoginGroup = True, omit or send "".
        '       par_sResult: Return parameter: GID_ID of the XU record created.
        '       par_bThisIsLoginGroup: When True, create the 'login' as a login group.
        '           Default is False.
        '       par_sGroupGIDID: GID_ID of the Login Group record from XU table.
        '           Ignored if par_bThisIsLoginGroup is True: omit or send ''
        '           if this is a login group.
        '       	--------- PARAMETERS BELOW APPLICABLE ONLY TO LOGINS, NOT GROUPS ----------
        '       par_siBillingType: 0=Sales (default); 1=Lite; 2=Partner; 255=Do not bill
        '       par_bEnabled bit: when 1 (default), the login is enabled, otherwise
        '       	the user can't log in
        '       par_dtBillingStartTime: DEPRECATEAD start billing on this date. The default value
        '       	in SS is GETUTCDATE()
        '       par_dtDeactivationTime: DEPRECATED deactivate the login on this datetime. 
        '       	NULL means infinity, never deactivate.
        '       par_bPasswordIsTemporary: when 1 (default), the password is temporary
        '       	and expires on datetime in @par_dtTempPasswordExpirationTime. If NULL,
        '           it(doesn) 't expire.
        '       par_dtTempPasswordExpirationTime: if the password is temporary, it expires
        '       	on this datetime.
        '       par_dtTempPasswordGeneratedTime: datetime the temp password was generated
        '       	by an admin in diaAdmPerm.
        '       par_sTempPasswordDeliveryMethod: VERBAL or EMAIL (other modes may be supported
        '       	in the future).
        '       par_sTempPasswordGeneratedBy: 4-char user code of the admin who generated
        '           the temp password.
        '       par_sTempPasswordSentTo: E-mail or other address indicating how the temp
        '           password was sent to the user.
        '       par_dtPasswordUserChangedTime: DEPRECATED datetime the user changed the password.
        '       par_bPasswordDoesntExpire: When True, the password is not subject to the 
        '           WOP property EXPIREPASSWORDSINDAYS= and never 'expires'. 
        '       par_sPasswordChangedBy: 4-char user code of the user or admin who last
        '           changed the password (temporary or permanent)
        '       par_dtPasswordChangedTime: datetime the user or admin changed the password
        '       par_bBill: when 1 (default), the login will be billed.
        'RETURNS:
        '       True on success, false otherwise. Get the error via goErr.GetLastError.

        '@par_sLogonName nvarchar(20),
        '@par_sPassword nvarchar(20),
        '@par_uUserGIDID uniqueidentifier = NULL,
        '@par_uResult uniqueidentifier OUTPUT,
        '@par_bThisIsLoginGroup bit = 0,
        '@par_uGroupGIDID uniqueidentifier = NULL

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DETAILS, True)

        Dim bResult As Boolean = True
        Dim iResult As Integer

        'Try

        '------------ Validate ------------
        If Trim(par_sLogonName) = "" Then
                goErr.SetWarning(35000, sProc, "The login record can't be added because the Login Name is blank.")
                'MessTranslate
            End If
            If Not par_bThisIsLoginGroup Then
                If Trim(par_sPassword) = "" Then
                    goErr.SetWarning(35000, sProc, "The login record can't be added because the Password is blank.")
                    'MessTranslate
                End If
            End If

            '------------- Add ------------
            Dim oConnection As New SqlClient.SqlConnection
            oConnection = goData.GetConnection

            Dim oCommand As New SqlClient.SqlCommand
            Dim oReader As SqlClient.SqlDataReader

            oCommand.CommandText = "pLoginAdd"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter
            Dim strPage As New SqlClient.SqlParameter("@par_sLogonName", SqlDbType.VarChar)
            strPage.Value = par_sLogonName
            oCommand.Parameters.Add(strPage)

            'parameter
            Dim strProperty As New SqlClient.SqlParameter("@par_sPassword", SqlDbType.VarChar)
            strProperty.Value = par_sPassword
            oCommand.Parameters.Add(strProperty)

            'parameter
            Dim uUserGIDID As New SqlClient.SqlParameter("@par_uUserGIDID", SqlDbType.UniqueIdentifier)
            If par_sUserGIDID = "" Or par_sUserGIDID = "<%NONE%>" Or par_sUserGIDID Is Nothing Then
                uUserGIDID.Value = System.DBNull.Value
            Else
                uUserGIDID.Value = goTR.StringToGuid(par_sUserGIDID)
            End If
            oCommand.Parameters.Add(uUserGIDID)

            'Return parameter
            Dim uResult As New SqlClient.SqlParameter("@par_uResult", SqlDbType.UniqueIdentifier)
            'uResult.Value = System.DBNull.Value
            uResult.Direction = ParameterDirection.Output
            oCommand.Parameters.Add(uResult)

            'parameter
            Dim bThisIsLoginGroup As New SqlClient.SqlParameter("@par_bThisIsLoginGroup", SqlDbType.Bit)
            bThisIsLoginGroup.Value = Convert.ToInt16(par_bThisIsLoginGroup)
            oCommand.Parameters.Add(bThisIsLoginGroup)

            'parameter
            Dim uGroupGIDID As New SqlClient.SqlParameter("@par_uGroupGIDID", SqlDbType.UniqueIdentifier)
            If Not par_bThisIsLoginGroup Then
                'Regular Login
                If par_sGroupGIDID = "" Or par_sGroupGIDID = "<%NONE%>" Or par_sGroupGIDID Is Nothing Then
                    uGroupGIDID.Value = System.DBNull.Value
                Else
                    uGroupGIDID.Value = goTR.StringToGuid(par_sGroupGIDID)
                End If
            Else
                'Login Group - ignore the Login Group GID_ID parameter
                uGroupGIDID.Value = System.DBNull.Value
            End If
            oCommand.Parameters.Add(uGroupGIDID)

            'parameter
            Dim parBillingType As New SqlClient.SqlParameter("@par_siBillingType", SqlDbType.TinyInt)
            parBillingType.Value = goTR.StringToNum(par_sBillingType, "0")
            oCommand.Parameters.Add(parBillingType)

            'MI 1/3/12 -------- Added the following 10 parameters ------------

            'parameter
            Dim bEnabled As New SqlClient.SqlParameter("@par_bEnabled", SqlDbType.Bit)
            bEnabled.Value = Convert.ToInt16(par_bEnabled)
            oCommand.Parameters.Add(bEnabled)

            ''parameter
            'Dim dtBillingStartTime As New SqlClient.SqlParameter("@par_dtBillingStartTime", SqlDbType.DateTime)
            'If par_dtBillingStartTime = clC.SELL_BLANK_DTDATETIME Then
            '    dtBillingStartTime.Value = System.DBNull.Value
            'Else
            '    dtBillingStartTime.Value = par_dtBillingStartTime
            'End If
            'oCommand.Parameters.Add(dtBillingStartTime)

            ''parameter
            'Dim dtDeactivationTime As New SqlClient.SqlParameter("@par_dtDeactivationTime", SqlDbType.DateTime)
            'If par_dtDeactivationTime = clC.SELL_BLANK_DTDATETIME Then
            '    dtDeactivationTime.Value = System.DBNull.Value
            'Else
            '    dtDeactivationTime.Value = par_dtDeactivationTime
            'End If
            'oCommand.Parameters.Add(dtDeactivationTime)

            'parameter
            Dim bPasswordIsTemporary As New SqlClient.SqlParameter("@par_bPasswordIsTemporary", SqlDbType.Bit)
            bPasswordIsTemporary.Value = Convert.ToInt16(par_bPasswordIsTemporary)
            oCommand.Parameters.Add(bPasswordIsTemporary)

            'parameter
            Dim dtTempPasswordExpirationTime As New SqlClient.SqlParameter("@par_dtTempPasswordExpirationTime", SqlDbType.DateTime)
            If par_dtTempPasswordExpirationTime = clC.SELL_BLANK_DTDATETIME Then
                dtTempPasswordExpirationTime.Value = System.DBNull.Value
            Else
                dtTempPasswordExpirationTime.Value = par_dtTempPasswordExpirationTime
            End If
            oCommand.Parameters.Add(dtTempPasswordExpirationTime)

            'parameter
            Dim dtTempPasswordGeneratedTime As New SqlClient.SqlParameter("@par_dtTempPasswordGeneratedTime", SqlDbType.DateTime)
            If par_dtTempPasswordGeneratedTime = clC.SELL_BLANK_DTDATETIME Then
                dtTempPasswordGeneratedTime.Value = System.DBNull.Value
            Else
                dtTempPasswordGeneratedTime.Value = par_dtTempPasswordGeneratedTime
            End If
            oCommand.Parameters.Add(dtTempPasswordGeneratedTime)

            'parameter
            Dim sTempPasswordDeliveryMethod As New SqlClient.SqlParameter("@par_sTempPasswordDeliveryMethod", SqlDbType.VarChar, 8)
            If par_sTempPasswordDeliveryMethod = "" Then
                sTempPasswordDeliveryMethod.Value = System.DBNull.Value
            Else
                sTempPasswordDeliveryMethod.Value = par_sTempPasswordDeliveryMethod
            End If
            oCommand.Parameters.Add(sTempPasswordDeliveryMethod)

            'parameter
            Dim sTempPasswordGeneratedBy As New SqlClient.SqlParameter("@par_sTempPasswordGeneratedBy", SqlDbType.VarChar, 4)
            If par_sTempPasswordGeneratedBy = "" Then
                sTempPasswordGeneratedBy.Value = System.DBNull.Value
            Else
                sTempPasswordGeneratedBy.Value = par_sTempPasswordGeneratedBy
            End If
            oCommand.Parameters.Add(sTempPasswordGeneratedBy)

            'parameter
            Dim sTempPasswordSentTo As New SqlClient.SqlParameter("@par_sTempPasswordSentTo", SqlDbType.NVarChar, 80)
            If par_sTempPasswordSentTo = "" Then
                sTempPasswordSentTo.Value = System.DBNull.Value
            Else
                sTempPasswordSentTo.Value = par_sTempPasswordSentTo
            End If
            oCommand.Parameters.Add(sTempPasswordSentTo)

            ''parameter
            'Dim dtPasswordUserChangedTime As New SqlClient.SqlParameter("@par_dtPasswordUserChangedTime", SqlDbType.DateTime)
            'If par_dtPasswordUserChangedTime = clC.SELL_BLANK_DTDATETIME Then
            '    dtPasswordUserChangedTime.Value = System.DBNull.Value
            'Else
            '    dtPasswordUserChangedTime.Value = par_dtPasswordUserChangedTime
            'End If
            'oCommand.Parameters.Add(dtPasswordUserChangedTime)

            'parameter MI 9/24/13
            Dim bPasswordDoesntExpire As New SqlClient.SqlParameter("@par_bPasswordDoesntExpire", SqlDbType.Bit)
            bPasswordDoesntExpire.Value = Convert.ToInt16(par_bPasswordDoesntExpire)
            oCommand.Parameters.Add(bPasswordDoesntExpire)

            'parameter MI 10/4/13
            Dim sPasswordChangedBy As New SqlClient.SqlParameter("@par_sPasswordChangedBy", SqlDbType.VarChar, 4)
            If par_sPasswordChangedBy = "" Then
                sPasswordChangedBy.Value = System.DBNull.Value
            Else
                sPasswordChangedBy.Value = par_sPasswordChangedBy
            End If
            oCommand.Parameters.Add(sPasswordChangedBy)

            'parameter MI 10/4/13
            Dim dtPasswordChangedTime As New SqlClient.SqlParameter("@par_dtPasswordChangedTime", SqlDbType.DateTime)
            If par_dtPasswordChangedTime = clC.SELL_BLANK_DTDATETIME Then
                dtPasswordChangedTime.Value = System.DBNull.Value
            Else
                dtPasswordChangedTime.Value = par_dtPasswordChangedTime
            End If
            oCommand.Parameters.Add(dtPasswordChangedTime)

            'parameter
            Dim bBill As New SqlClient.SqlParameter("@par_bBill", SqlDbType.Bit)
            bBill.Value = Convert.ToInt16(par_bBill)
            oCommand.Parameters.Add(bBill)


            'Result parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            Try
                'execute
                oReader = oCommand.ExecuteReader()
                'Now you can grab the output parameter's value...
                iResult = Convert.ToInt16(retValParam.Value)
                par_sResult = uResult.Value.ToString        'GID_ID of the XU record

                oReader.Close()
                oConnection.Close()

            Catch sqlex As SqlClient.SqlException
                If sqlex.ErrorCode = -2146232060 Then
                    goErr.SetWarning(35000, sProc, "The login record can't be added because a user login or login group with the same name exists. Please assign a unique name.")
                    'MessTranslate
                Else
                    goErr.SetWarning(35000, sProc, "Adding login record failed due to database error '" & sqlex.ErrorCode & "': '" & sqlex.Message & "'")
                    'MessTranslate
                End If
                Return False
            End Try

            If iResult = 0 Then
                Return True
            Else
                Select Case iResult
                    Case -1 'when fnGetMe returns '' User ID
                        goErr.SetWarning(35503, sProc)
                        '35503: Metadata or permission line couldn't be written because fnGetMe returned an invalid user ID.
                    Case -2 'when @par_sUserCode is ''
                        goErr.SetWarning(35504, sProc)
                        '35504: Metadata or permission line couldn't be written because fnGetMe returned an invalid user code.
                    Case -3 'LogonName duplicate error (SS error 2601)
                        goErr.SetWarning(35000, sProc, "The login record can't be added because a user login or login group with the same name exists. Please assign a unique name.")
                        'MessTranslate
                    Case Else
                        goErr.SetWarning(35000, sProc, "Adding login record failed: database error is '" & iResult & "'.")
                        'MessTranslate
                End Select
                Return False
            End If

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return bResult

    End Function

    Public Function LoginEdit(ByVal par_sGIDID As String, _
                    ByVal par_sLogonName As String, _
                    ByVal par_sPassword As String, _
                    ByVal par_sUserGIDID As String, _
                    ByVal par_sGroupGIDID As String, _
                    ByVal par_sBillingType As String, _
                    ByVal par_bEnabled As Boolean, _
                    ByVal par_bPasswordIsTemporary As Boolean, _
                    ByVal par_dtTempPasswordExpirationTime As DateTime, _
                    ByVal par_dtTempPasswordGeneratedTime As DateTime, _
                    ByVal par_sTempPasswordDeliveryMethod As String, _
                    ByVal par_sTempPasswordGeneratedBy As String, _
                    ByVal par_sTempPasswordSentTo As String, _
                    ByVal par_bPasswordDoesntExpire As Boolean, _
                    ByVal par_sPasswordChangedBy As String, _
                    ByVal par_dtPasswordChangedTime As DateTime, _
                    ByVal par_bBill As Boolean) As Boolean

        'Public Function LoginEdit(ByVal par_sGIDID As String, _
        '                ByVal par_sLogonName As String, _
        '                ByVal par_sPassword As String, _
        '                ByVal par_sUserGIDID As String, _
        '                ByVal par_sGroupGIDID As String, _
        '                ByVal par_sBillingType As String, _
        '                ByVal par_bEnabled As Boolean, _
        '                ByVal par_dtBillingStartTime As DateTime, _            --> DEPRECATED
        '                ByVal par_dtDeactivationTime As DateTime, _            --> DEPRECATED
        '                ByVal par_bPasswordIsTemporary As Boolean, _
        '                ByVal par_dtTempPasswordExpirationTime As DateTime, _
        '                ByVal par_dtTempPasswordGeneratedTime As DateTime, _
        '                ByVal par_sTempPasswordDeliveryMethod As String, _
        '                ByVal par_sTempPasswordGeneratedBy As String, _
        '                ByVal par_sTempPasswordSentTo As String, _
        '                ByVal par_dtPasswordUserChangedTime As DateTime, _     --> DEPRECATED
        '                ByVal par_bPasswordDoesntExpire As Boolean, _
        '                ByVal par_sPasswordChangedBy As String, _
        '                ByVal par_dtPasswordChangedTime As DateTime) As Boolean

        'MI 10/24/13 LoginEdit: Added par_bBill.
        'MI 10/23/13 LoginEdit: Deprecating par_dtBillingStartTime, par_dtDeactivationTime.
        'MI 10/11/13 Undoing skipping the password.
        'MI 10/10/13 Enabling skipping the password by sending Nothing in that parameter.
        'MI 10/4/13 LoginEdit: Added par_sPasswordChangedBy, par_dtPasswordChangedTime parameters.
        'MI 9/24/13 Added support for par_bPasswordDoesntExpire. 
        'MI 12/29/11 Added support for enablement, billing, and temp password parameters.
        'MI 8/26/09 Added support for Group GID_ID.
        'MI 9/23/08 Added support for login groups.
        'PURPOSE:
        '       Edit an XU (User login or User Login Group) record. 
        '       If an error occurs, sets a warning. Since none of the parameters is optional,
        '       all field values must be read and set explicitly even in the case of login
        '       Group to which most of the parameters don't pertain.
        '       To skip sending the password and modifying other password-related fields, 
        '       use LoginEditIgnorePassword instead.
        'WARNING:
        '       Since all fields are written explicitly, the XU writing is record-level, 
        '       not field-level. If user 1 opens the XU record (by selecting a Login in
        '       diaAdmPerm.aspx), user 2 edits and saves the same XU record, and
        '       user 1 then saves, the changes made by user 2 will be lost.
        'PARAMETERS:
        '       Note: Except for password, none of the parameters is optional to prevent inadvertently 
        '       overwriting values. Since pLoginEdit performs a single UPDATE of the XU record, none of
        '       the fields can be skipped. If pLoginEdit were rewritten to write only the
        '       non-null fields using dynamic SQL, then the parameters in this method could be
        '       made optional. Note that boolean and datetime types can't be set to Nothing.
        '       To do so, parameters would need to be of object type or mandatory parameters
        '       could be made nullable using this syntax (from WT 1/4/12):
        '          Optional ByVal par_bEnabled As Nullable(Of Boolean), _
        '
        '       par_sGIDID: GID_ID of the XU record to edit.
        '       par_sLogonName: name under which the user will log on or the 
        '           name of the login group.
        '       par_sPassword: Logon password. If this is a login group, send ''.
        '           To skip sending the password and modifying other password-
        '           related fields, use LoginEditIgnorePassword instead.
        '       par_sUserGIDID: GID_ID of the US record associated. If
        '           this is a login group, omit or send ''. 
        '       par_sGroupGIDID: GID_ID of the Login Group record from XU table.
        '           Only applicable to Logins. If this is a login group, omit or
        '           send ''. This WILL write the Group GID_ID in Login Group
        '           records (which is nonsensical), but this will be ignored by the UI.
        '       	--------- PARAMETERS BELOW APPLICABLE ONLY TO LOGINS, NOT GROUPS ----------
        '       par_siBillingType: 0=Sales (default); 1=Lite; 2=Partner; 255=Do not bill.
        '       par_bEnabled bit: when 1 (default), the login is enabled, otherwise
        '       	the user can't log in. 
        '       par_dtBillingStartTime: DEPRECATED start billing on this date. The default value
        '       	in SS is GETUTCDATE().
        '       par_dtDeactivationTime: DEPRECATED deactivate the login on this datetime. 
        '       	NULL means infinity, never deactivate. 
        '       par_bPasswordIsTemporary: when 1 (default), the password is temporary
        '       	and expires on datetime in @par_dtTempPasswordExpirationTime. If NULL,
        '           it doesn't expire. 
        '       par_dtTempPasswordExpirationTime: if the password is temporary, it expires
        '       	on this datetime.  
        '       par_dtTempPasswordGeneratedTime: datetime the temp password was generated
        '       	by an admin in diaAdmPerm.  
        '       par_sTempPasswordDeliveryMethod: VERBAL or EMAIL (other modes may be supported
        '       	in the future).  
        '       par_sTempPasswordGeneratedBy: 4-char user code of the admin who generated
        '           the temp password.  
        '       par_sTempPasswordSentTo: E-mail or other address indicating how the temp
        '           password was sent to the user.  
        '       par_dtPasswordUserChangedTime: DEPRECATED datetime the user changed the password.
        '       par_bPasswordDoesntExpire: 0=password is subject to EXPIREPASSWORDSINDAYS= in WOP
        '           (default); 1=password never expires. 
        '       par_sPasswordChangedBy: 4-char user code of the user or admin who last
        '           changed the password (temporary or permanent)
        '       par_dtPasswordChangedTime: datetime the user or admin changed the password
        '       par_bBill: when 1 (default), the login will be billed.

        'RETURNS:
        '       True on success, False otherwise. Retrieve the error via goErr.GetLastError
        'CREATE PROCEDURE pLoginEdit 
        '	@par_uGIDID uniqueidentifier,
        '	@par_sLogonName nvarchar(20),
        '	@par_sPassword nvarchar(20),
        '	@par_uUserGIDID uniqueidentifier = NULL,
        '   @par_uGroupGIDID uniqueidentifier = NULL

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DETAILS, True)

        If par_dtTempPasswordExpirationTime = #12:00:00 AM# Then

            par_dtTempPasswordExpirationTime = clC.SELL_BLANK_DTDATETIME

        End If
        If par_dtTempPasswordGeneratedTime = #12:00:00 AM# Then

            par_dtTempPasswordGeneratedTime = clC.SELL_BLANK_DTDATETIME

        End If
        If par_dtPasswordChangedTime = #12:00:00 AM# Then

            par_dtPasswordChangedTime = clC.SELL_BLANK_DTDATETIME

        End If
        Dim bResult As Boolean = True
        Dim iResult As Integer

        'Try

        '------------ Validate ------------
        If Trim(par_sGIDID) = "" Then
                goErr.SetError(10103, sProc, , "par_sGIDID", sProc, par_sGIDID)
                '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
            End If
            If Trim(par_sLogonName) = "" Then
                goErr.SetWarning(35000, sProc, "The login record can't be edited because the Login Name is blank.")
                'MessTranslate
            End If
            'If Trim(par_sPassword) = "" Then
            '    goErr.SetWarning(35000, sProc, "The login record can't be edited because the Password is blank.")
            '    'MessTranslate
            'End If

            '------------- Edit ---------------
            Dim oConnection As New SqlClient.SqlConnection
            oConnection = goData.GetConnection
            Dim oCommand As New SqlClient.SqlCommand
            Dim oReader As SqlClient.SqlDataReader

            oCommand.CommandText = "pLoginEdit"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter
            Dim uGIDID As New SqlClient.SqlParameter("@par_uGIDID", SqlDbType.UniqueIdentifier)
            If par_sGIDID = "" Then
                uGIDID.Value = System.DBNull.Value
            Else
                uGIDID.Value = goTR.StringToGuid(par_sGIDID)
            End If
            oCommand.Parameters.Add(uGIDID)

            'parameter
            Dim strPage As New SqlClient.SqlParameter("@par_sLogonName", SqlDbType.VarChar)
            strPage.Value = par_sLogonName
            oCommand.Parameters.Add(strPage)

            'parameter
            Dim strProperty As New SqlClient.SqlParameter("@par_sPassword", SqlDbType.VarChar)
            strProperty.Value = par_sPassword
            oCommand.Parameters.Add(strProperty)

            'parameter
            Dim uUserGIDID As New SqlClient.SqlParameter("@par_uUserGIDID", SqlDbType.UniqueIdentifier)
            If par_sUserGIDID = "" Or par_sUserGIDID = "<%NONE%>" Then
                uUserGIDID.Value = System.DBNull.Value
            Else
                uUserGIDID.Value = goTR.StringToGuid(par_sUserGIDID)
            End If
            oCommand.Parameters.Add(uUserGIDID)

            'parameter
            Dim uGroupGIDID As New SqlClient.SqlParameter("@par_uGroupGIDID", SqlDbType.UniqueIdentifier)
            If par_sGroupGIDID = "" Or par_sGroupGIDID = "<%NONE%>" Then
                uGroupGIDID.Value = System.DBNull.Value
            Else
                uGroupGIDID.Value = goTR.StringToGuid(par_sGroupGIDID)
            End If
            oCommand.Parameters.Add(uGroupGIDID)

            'parameter
            Dim parBillingType As New SqlClient.SqlParameter("@par_siBillingType", SqlDbType.TinyInt)
            parBillingType.Value = goTR.StringToNum(par_sBillingType, "0")
            oCommand.Parameters.Add(parBillingType)

            'MI 1/3/12 -------- Added the following 10 parameters ------------

            'parameter
            Dim bEnabled As New SqlClient.SqlParameter("@par_bEnabled", SqlDbType.Bit)
            bEnabled.Value = Convert.ToInt16(par_bEnabled)
            oCommand.Parameters.Add(bEnabled)

            ''parameter
            'Dim dtBillingStartTime As New SqlClient.SqlParameter("@par_dtBillingStartTime", SqlDbType.DateTime)
            'If par_dtBillingStartTime = clC.SELL_BLANK_DTDATETIME Then
            '    dtBillingStartTime.Value = System.DBNull.Value
            'Else
            '    dtBillingStartTime.Value = par_dtBillingStartTime
            'End If
            'oCommand.Parameters.Add(dtBillingStartTime)

            ''parameter
            'Dim dtDeactivationTime As New SqlClient.SqlParameter("@par_dtDeactivationTime", SqlDbType.DateTime)
            'If par_dtDeactivationTime = clC.SELL_BLANK_DTDATETIME Then
            '    dtDeactivationTime.Value = System.DBNull.Value
            'Else
            '    dtDeactivationTime.Value = par_dtDeactivationTime
            'End If
            'oCommand.Parameters.Add(dtDeactivationTime)

            'parameter
            Dim bPasswordIsTemporary As New SqlClient.SqlParameter("@par_bPasswordIsTemporary", SqlDbType.Bit)
            bPasswordIsTemporary.Value = Convert.ToInt16(par_bPasswordIsTemporary)
            oCommand.Parameters.Add(bPasswordIsTemporary)

            'parameter
            Dim dtTempPasswordExpirationTime As New SqlClient.SqlParameter("@par_dtTempPasswordExpirationTime", SqlDbType.DateTime)
            If par_dtTempPasswordExpirationTime = clC.SELL_BLANK_DTDATETIME Then
                dtTempPasswordExpirationTime.Value = System.DBNull.Value
            Else
                dtTempPasswordExpirationTime.Value = par_dtTempPasswordExpirationTime
            End If
            oCommand.Parameters.Add(dtTempPasswordExpirationTime)

            'parameter
            Dim dtTempPasswordGeneratedTime As New SqlClient.SqlParameter("@par_dtTempPasswordGeneratedTime", SqlDbType.DateTime)
            If par_dtTempPasswordGeneratedTime = clC.SELL_BLANK_DTDATETIME Then
                dtTempPasswordGeneratedTime.Value = System.DBNull.Value
            Else
                dtTempPasswordGeneratedTime.Value = par_dtTempPasswordGeneratedTime
            End If
            oCommand.Parameters.Add(dtTempPasswordGeneratedTime)

            'parameter
            Dim sTempPasswordDeliveryMethod As New SqlClient.SqlParameter("@par_sTempPasswordDeliveryMethod", SqlDbType.VarChar, 8)
            If par_sTempPasswordDeliveryMethod = "" Then
                sTempPasswordDeliveryMethod.Value = System.DBNull.Value
            Else
                sTempPasswordDeliveryMethod.Value = par_sTempPasswordDeliveryMethod
            End If
            oCommand.Parameters.Add(sTempPasswordDeliveryMethod)

            'parameter
            Dim sTempPasswordGeneratedBy As New SqlClient.SqlParameter("@par_sTempPasswordGeneratedBy", SqlDbType.VarChar, 4)
            If par_sTempPasswordGeneratedBy = "" Then
                sTempPasswordGeneratedBy.Value = System.DBNull.Value
            Else
                sTempPasswordGeneratedBy.Value = par_sTempPasswordGeneratedBy
            End If
            oCommand.Parameters.Add(sTempPasswordGeneratedBy)

            'parameter
            Dim sTempPasswordSentTo As New SqlClient.SqlParameter("@par_sTempPasswordSentTo", SqlDbType.NVarChar, 80)
            If par_sTempPasswordSentTo = "" Then
                sTempPasswordSentTo.Value = System.DBNull.Value
            Else
                sTempPasswordSentTo.Value = par_sTempPasswordSentTo
            End If
            oCommand.Parameters.Add(sTempPasswordSentTo)

            ''parameter
            'Dim dtPasswordUserChangedTime As New SqlClient.SqlParameter("@par_dtPasswordUserChangedTime", SqlDbType.DateTime)
            'If par_dtPasswordUserChangedTime = clC.SELL_BLANK_DTDATETIME Then
            '    dtPasswordUserChangedTime.Value = System.DBNull.Value
            'Else
            '    dtPasswordUserChangedTime.Value = par_dtPasswordUserChangedTime
            'End If
            'oCommand.Parameters.Add(dtPasswordUserChangedTime)

            'parameter MI 9/24/13
            Dim bPasswordDoesntExpire As New SqlClient.SqlParameter("@par_bPasswordDoesntExpire", SqlDbType.Bit)
            bPasswordDoesntExpire.Value = Convert.ToInt16(par_bPasswordDoesntExpire)
            oCommand.Parameters.Add(bPasswordDoesntExpire)

            'parameter MI 10/4/13
            Dim sPasswordChangedBy As New SqlClient.SqlParameter("@par_sPasswordChangedBy", SqlDbType.VarChar, 4)
            If par_sPasswordChangedBy = "" Then
                sPasswordChangedBy.Value = System.DBNull.Value
            Else
                sPasswordChangedBy.Value = par_sPasswordChangedBy
            End If
            oCommand.Parameters.Add(sPasswordChangedBy)

            'parameter MI 10/4/13
            Dim dtPasswordChangedTime As New SqlClient.SqlParameter("@par_dtPasswordChangedTime", SqlDbType.DateTime)
            If par_dtPasswordChangedTime = clC.SELL_BLANK_DTDATETIME Then
                dtPasswordChangedTime.Value = System.DBNull.Value
            Else
                dtPasswordChangedTime.Value = par_dtPasswordChangedTime
            End If
            oCommand.Parameters.Add(dtPasswordChangedTime)

            'parameter
            Dim bBill As New SqlClient.SqlParameter("@par_bBill", SqlDbType.Bit)
            bBill.Value = Convert.ToInt16(par_bBill)
            oCommand.Parameters.Add(bBill)


            'Result parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            Try
                'execute
                oReader = oCommand.ExecuteReader()
                'Now you can grab the output parameter's value...
                iResult = Convert.ToInt16(retValParam.Value)

                oReader.Close()
                oConnection.Close()

            Catch sqlex As SqlClient.SqlException
                If sqlex.ErrorCode = -2146232060 Then
                    goErr.SetWarning(35000, sProc, "The login/login group record can't be edited because a duplicate exists.")
                    'MessTranslate
                Else
                    goErr.SetWarning(35000, sProc, "Editing login or login group record failed due to database error '" & sqlex.ErrorCode & "': '" & sqlex.Message & "'")
                    'MessTranslate
                End If
                Return False
            End Try

            If iResult = 0 Then
                Return True
            Else
                Select Case iResult
                    Case -1 'when fnGetMe returns '' User ID
                        goErr.SetWarning(35503, sProc)
                        '35503: Metadata or permission line couldn't be written because fnGetMe returned an invalid user ID.
                    Case -2 'when @par_sUserCode is ''
                        goErr.SetWarning(35504, sProc)
                        '35504: Metadata or permission line couldn't be written because fnGetMe returned an invalid user code.
                    Case -3 'LogonName duplicate error (SS error 2601)
                        goErr.SetWarning(35000, sProc, "The login or login group record can't be edited because a duplicate exists.")
                        'MessTranslate
                    Case -4
                        goErr.SetWarning(35000, sProc, "The login or login group record can't be edited because it doesn't exist.")
                        'MessTranslate
                    Case Else
                        goErr.SetWarning(35000, sProc, "Editing login or login group record failed: database error is '" & iResult & "'.")
                        'MessTranslate
                End Select
                Return False
            End If

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return bResult

    End Function

    Public Function LoginEditIgnorePassword(ByVal par_sGIDID As String, _
                ByVal par_sLogonName As String, _
                ByVal par_sUserGIDID As String, _
                ByVal par_sGroupGIDID As String, _
                ByVal par_sBillingType As String, _
                ByVal par_bEnabled As Boolean, _
                ByVal par_bBill As Boolean) As Boolean

        'Public Function LoginEditIgnorePassword(ByVal par_sGIDID As String, _
        '            ByVal par_sLogonName As String, _
        '            ByVal par_sUserGIDID As String, _
        '            ByVal par_sGroupGIDID As String, _
        '            ByVal par_sBillingType As String, _
        '            ByVal par_bEnabled As Boolean, _
        '            ByVal par_dtBillingStartTime As DateTime, _
        '            ByVal par_dtDeactivationTime As DateTime) As Boolean

        'MI 10/24/13 LoginEditIgnorePassword: Added par_bBill.
        'MI 10/23/13 LoginEditIgnorePassword: Deprecating par_dtBillingStartTime, par_dtDeactivationTime.
        'MI 10/11/13 Created from LoginEdit.
        'PURPOSE:
        '       Edit an XU (User login or User Login Group) record without changing any of
        '       the password-related fields. This is intended to be called from Admin>
        '       User/Group Permissions dialog (diaAdmPerm) when saving changes to the
        '       login (when selecting another login or clicking OK). Password information
        '       is written, together with other login properties by Generate Password, 
        '       Reset Password and Set Permanent Password buttons in this dialog. As 
        '       the user of the login could change the password before the admin selects
        '       anothear login or exits the dialog, we don't want the information about 
        '       the temporary password written when saving the login information the second time. 
        '       If an error occurs, sets a warning. Since none of the parameters is optional,
        '       all field values must be read and set explicitly even in the case of login
        '       Group to which most of the parameters don't pertain.
        '       To set the password and other password-related fields, use 
        '       LoginEdit instead.
        'WARNING:
        '       Since all fields are written explicitly, the XU writing is record-level, 
        '       not field-level. If user 1 opens the XU record (by selecting a Login in
        '       diaAdmPerm.aspx), user 2 edits and saves the same XU record, and
        '       user 1 then saves, the changes made by user 2 will be lost.
        'PARAMETERS:
        '       Note: Except for password, none of the parameters is optional to prevent inadvertently 
        '       overwriting values. Since pLoginEditIgnorePassword performs a single 
        '       UPDATE of the XU record, none of the fields can be skipped. 
        '       For more, see notes under pLoginEdit.
        '
        '       par_sGIDID: GID_ID of the XU record to edit.
        '       par_sLogonName: name under which the user will log on or the 
        '           name of the login group.
        '       par_sUserGIDID: GID_ID of the US record associated. If
        '           this is a login group, omit or send ''. 
        '       par_sGroupGIDID: GID_ID of the Login Group record from XU table.
        '           Only applicable to Logins. If this is a login group, omit or
        '           send ''. This WILL write the Group GID_ID in Login Group
        '           records (which is nonsensical), but this will be ignored by the UI.
        '       	--------- PARAMETERS BELOW APPLICABLE ONLY TO LOGINS, NOT GROUPS ----------
        '       par_siBillingType: 0=Sales (default); 1=Lite; 2=Partner; 255=Do not bill.
        '       par_bEnabled bit: when 1 (default), the login is enabled, otherwise
        '       	the user can't log in. 
        '       par_dtBillingStartTime: DEPRECATED start billing on this date. The default value
        '       	in SS is GETUTCDATE().
        '       par_dtDeactivationTime: DEPRECATED deactivate the login on this datetime. 
        '       	NULL means infinity, never deactivate. 
        '       par_bBill: when 1 (default), the login will be billed.

        'RETURNS:
        '       True on success, False otherwise. Retrieve the error via goErr.GetLastError
        'CREATE PROCEDURE pLoginEdit 
        '	@par_uGIDID uniqueidentifier,
        '	@par_sLogonName nvarchar(20),
        '	@par_sPassword nvarchar(20),
        '	@par_uUserGIDID uniqueidentifier = NULL,
        '   @par_uGroupGIDID uniqueidentifier = NULL

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DETAILS, True)

        Dim bResult As Boolean = True
        Dim iResult As Integer

        'Try

        '------------ Validate ------------
        If Trim(par_sGIDID) = "" Then
                goErr.SetError(10103, sProc, , "par_sGIDID", sProc, par_sGIDID)
                '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
            End If
            If Trim(par_sLogonName) = "" Then
                goErr.SetWarning(35000, sProc, "The login record can't be edited because the Login Name is blank.")
                'MessTranslate
            End If
            'If Trim(par_sPassword) = "" Then
            '    goErr.SetWarning(35000, sProc, "The login record can't be edited because the Password is blank.")
            '    'MessTranslate
            'End If

            '------------- Edit ---------------
            Dim oConnection As New SqlClient.SqlConnection
            oConnection = goData.GetConnection
            Dim oCommand As New SqlClient.SqlCommand
            Dim oReader As SqlClient.SqlDataReader

            oCommand.CommandText = "pLoginEditIgnorePassword"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter
            Dim uGIDID As New SqlClient.SqlParameter("@par_uGIDID", SqlDbType.UniqueIdentifier)
            If par_sGIDID = "" Then
                uGIDID.Value = System.DBNull.Value
            Else
                uGIDID.Value = goTR.StringToGuid(par_sGIDID)
            End If
            oCommand.Parameters.Add(uGIDID)

            'parameter
            Dim strPage As New SqlClient.SqlParameter("@par_sLogonName", SqlDbType.VarChar)
            strPage.Value = par_sLogonName
            oCommand.Parameters.Add(strPage)

            'parameter
            Dim uUserGIDID As New SqlClient.SqlParameter("@par_uUserGIDID", SqlDbType.UniqueIdentifier)
            If par_sUserGIDID = "" Or par_sUserGIDID = "<%NONE%>" Then
                uUserGIDID.Value = System.DBNull.Value
            Else
                uUserGIDID.Value = goTR.StringToGuid(par_sUserGIDID)
            End If
            oCommand.Parameters.Add(uUserGIDID)

            'parameter
            Dim uGroupGIDID As New SqlClient.SqlParameter("@par_uGroupGIDID", SqlDbType.UniqueIdentifier)
            If par_sGroupGIDID = "" Or par_sGroupGIDID = "<%NONE%>" Then
                uGroupGIDID.Value = System.DBNull.Value
            Else
                uGroupGIDID.Value = goTR.StringToGuid(par_sGroupGIDID)
            End If
            oCommand.Parameters.Add(uGroupGIDID)

            'parameter
            Dim parBillingType As New SqlClient.SqlParameter("@par_siBillingType", SqlDbType.TinyInt)
            parBillingType.Value = goTR.StringToNum(par_sBillingType, "0")
            oCommand.Parameters.Add(parBillingType)

            'parameter
            Dim bEnabled As New SqlClient.SqlParameter("@par_bEnabled", SqlDbType.Bit)
            bEnabled.Value = Convert.ToInt16(par_bEnabled)
            oCommand.Parameters.Add(bEnabled)

            ''parameter
            'Dim dtBillingStartTime As New SqlClient.SqlParameter("@par_dtBillingStartTime", SqlDbType.DateTime)
            'If par_dtBillingStartTime = clC.SELL_BLANK_DTDATETIME Then
            '    dtBillingStartTime.Value = System.DBNull.Value
            'Else
            '    dtBillingStartTime.Value = par_dtBillingStartTime
            'End If
            'oCommand.Parameters.Add(dtBillingStartTime)

            ''parameter
            'Dim dtDeactivationTime As New SqlClient.SqlParameter("@par_dtDeactivationTime", SqlDbType.DateTime)
            'If par_dtDeactivationTime = clC.SELL_BLANK_DTDATETIME Then
            '    dtDeactivationTime.Value = System.DBNull.Value
            'Else
            '    dtDeactivationTime.Value = par_dtDeactivationTime
            'End If
            'oCommand.Parameters.Add(dtDeactivationTime)

            'parameter
            Dim bBill As New SqlClient.SqlParameter("@par_bBill", SqlDbType.Bit)
            bBill.Value = Convert.ToInt16(par_bBill)
            oCommand.Parameters.Add(bBill)


            'Result parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            Try
                'execute
                oReader = oCommand.ExecuteReader()
                'Now you can grab the output parameter's value...
                iResult = Convert.ToInt16(retValParam.Value)

                oReader.Close()
                oConnection.Close()

            Catch sqlex As SqlClient.SqlException
                If sqlex.ErrorCode = -2146232060 Then
                    goErr.SetWarning(35000, sProc, "The login/login group record can't be edited because a duplicate exists.")
                    'MessTranslate
                Else
                    goErr.SetWarning(35000, sProc, "Editing login or login group record failed due to database error '" & sqlex.ErrorCode & "': '" & sqlex.Message & "'")
                    'MessTranslate
                End If
                Return False
            End Try

            If iResult = 0 Then
                Return True
            Else
                Select Case iResult
                    Case -1 'when fnGetMe returns '' User ID
                        goErr.SetWarning(35503, sProc)
                        '35503: Metadata or permission line couldn't be written because fnGetMe returned an invalid user ID.
                    Case -2 'when @par_sUserCode is ''
                        goErr.SetWarning(35504, sProc)
                        '35504: Metadata or permission line couldn't be written because fnGetMe returned an invalid user code.
                    Case -3 'LogonName duplicate error (SS error 2601)
                        goErr.SetWarning(35000, sProc, "The login or login group record can't be edited because a duplicate exists.")
                        'MessTranslate
                    Case -4
                        goErr.SetWarning(35000, sProc, "The login or login group record can't be edited because it doesn't exist.")
                        'MessTranslate
                    Case Else
                        goErr.SetWarning(35000, sProc, "Editing login or login group record failed: database error is '" & iResult & "'.")
                        'MessTranslate
                End Select
                Return False
            End If

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return bResult

    End Function

    Public Function LoginDelete(ByVal par_sGIDID As String, Optional ByVal par_sLogonName As String = "") As Boolean
        'MI 9/23/08 Added support for login groups.
        'PURPOSE:
        '       Delete a record in XU (User login) by its GID_ID or LogonName value. XU records
        '       can be true logins or login groups when CHK_LoginGroup is set to 1.
        'PARAMETERS:
        '       par_sGIDID: GID_ID of the XU record to delete. If "", par_sLogonName is used to find the record.
        '       par_sLogonName: If par_sGIDID is "", LogonName of the XU record to delete.
        'RETURNS:
        '       True on success, false otherwise. Retrieve the error via goErr.GetLastError

        '--	@par_uGIDID uniqueidentifier
        '--	@par_sLogonName nvarchar(20)

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_NONE, True)

        Dim bResult As Boolean = True
        Dim iResult As Integer

        'Try

        Dim oConnection As New SqlClient.SqlConnection
            oConnection = goData.GetConnection
            Dim oCommand As New SqlClient.SqlCommand
            Dim oReader As SqlClient.SqlDataReader

            oCommand.CommandText = "pLoginDelete"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter
            Dim uUserGIDID As New SqlClient.SqlParameter("@par_uGIDID", SqlDbType.UniqueIdentifier)
            If par_sGIDID = "" Then
                uUserGIDID.Value = System.DBNull.Value
            Else
                uUserGIDID.Value = goTR.StringToGuid(par_sGIDID)
            End If
            oCommand.Parameters.Add(uUserGIDID)

            'parameter
            Dim strPage As New SqlClient.SqlParameter("@par_sLogonName", SqlDbType.VarChar)
            strPage.Value = par_sLogonName
            oCommand.Parameters.Add(strPage)

            'Result parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            Try
                'execute
                oReader = oCommand.ExecuteReader()
                'Now you can grab the output parameter's value...
                iResult = Convert.ToInt16(retValParam.Value)

                oReader.Close()
                oConnection.Close()

            Catch sqlex As SqlClient.SqlException
                'If sqlex.ErrorCode = -2146232060 Then
                goErr.SetWarning(35000, sProc, "Deleting login/login group record failed due to database error '" & sqlex.ErrorCode & "': '" & sqlex.Message & "'")
                'MessTranslate
                Return False
            End Try

            If iResult = 0 Then
                Return True
            Else
                Select Case iResult
                    Case -3 'Neither par_uGIDID nor par_sLogonName provided.
                        goErr.SetError(35000, sProc, "The login or login group record can't be deleted because neither par_uGIDID not par_uLogonName were provided to pLoginDelete.")
                        'MessTranslate
                    Case -4 'Record not found.
                        goErr.SetWarning(35000, sProc, "The login or login group record doesn't exist.")
                        'MessTranslate
                    Case Else
                        goErr.SetWarning(35000, sProc, "Adding login or login group record failed: database error is '" & iResult & "'.")
                        'MessTranslate
                End Select
                Return False
            End If

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return bResult

    End Function



    Public Function LoginResetAgreement(ByVal par_sGIDID As String) As Boolean
        'MI 5/17/07 Created.
        'PURPOSE:
        '       Delete the value of DTT_AgrAccepted field in the XU (User login) record.
        'PARAMETERS:
        '       par_sGIDID: GID_ID of the XU record.
        'RETURNS:
        '       True on success, False otherwise. Retrieve the error via goErr.GetLastError
        'CREATE PROCEDURE pLoginResetAgreement 
        '	@par_uGIDID uniqueidentifier

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim bResult As Boolean = True
        Dim iResult As Integer

        'Try

        '------------ Validate ------------
        If Trim(par_sGIDID) = "" Then
                goErr.SetError(10103, sProc, , "par_sGIDID", sProc, par_sGIDID)
                '10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
            End If

            '------------- Edit ---------------
            Dim oConnection As New SqlClient.SqlConnection
            oConnection = goData.GetConnection
            Dim oCommand As New SqlClient.SqlCommand
            Dim oReader As SqlClient.SqlDataReader

            oCommand.CommandText = "pLoginResetAgreement"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter
            Dim uGIDID As New SqlClient.SqlParameter("@par_uGIDID", SqlDbType.UniqueIdentifier)
            If par_sGIDID = "" Then
                uGIDID.Value = System.DBNull.Value
            Else
                uGIDID.Value = goTR.StringToGuid(par_sGIDID)
            End If
            oCommand.Parameters.Add(uGIDID)

            'Result parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            Try
                'execute
                oReader = oCommand.ExecuteReader()
                'Now you can grab the output parameter's value...
                iResult = Convert.ToInt16(retValParam.Value)

                oReader.Close()
                oConnection.Close()

            Catch sqlex As SqlClient.SqlException
                goErr.SetWarning(35000, sProc, "Editing login record failed due to database error '" & sqlex.ErrorCode & "': '" & sqlex.Message & "'")
                'MessTranslate
                Return False
            End Try

            If iResult = 0 Then
                Return True
            Else
                Select Case iResult
                    Case -1 'when fnGetMe returns '' User ID
                        goErr.SetWarning(35503, sProc)
                        '35503: Metadata or permission line couldn't be written because fnGetMe returned an invalid user ID.
                    Case -2 'when @par_sUserCode is ''
                        goErr.SetWarning(35504, sProc)
                        '35504: Metadata or permission line couldn't be written because fnGetMe returned an invalid user code.
                    Case -4
                        goErr.SetWarning(35000, sProc, "The login record can't be edited because it doesn't exist.")
                        'MessTranslate
                    Case Else
                        goErr.SetWarning(35000, sProc, "Editing login record failed: database error is '" & iResult & "'.")
                        'MessTranslate
                End Select
                Return False
            End If

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return bResult

    End Function

    Public Function PageDelete(ByVal par_sSection As String, ByVal par_sPageID As String) As Boolean
        'MI 8/17/06 Added treating blank par_sSection as global.
        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Delete the permission record (page). If multiple pages exist with the same
        '		section and page ID, this method deletes all such pages.
        'PARAMETERS:
        '		par_sSection: "GLOBAL" or User's TID
        '		par_sPageID: page ID of the page, typically in the <PREFIX>_<TID> format,
        '						for example: DSK_xxxxxxxx (desktop) or SCR_xxxxxxx (script)
        'RETURNS:
        '		*** Always true. *** In NGP it was True/False:
        '			False: if the page couldn't be found or deletion failed.
        '			True: page was found and the deletion succeeded.
        'EXAMPLE:
        '		sSection = goP.GetUserTID()
        '		sPageID = "ACCESS"
        '       IF not goPerm.PageDelete(sSection,sPageID) THEN
        '			goLog.Log(sProc, "Deletion failed.")
        '		ELSE
        '			goLog.Log(sProc, Deletion succeeded.")
        '		END

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim sSection As String = Trim(UCase(par_sSection))
        Dim sPage As String = Trim(UCase(par_sPageID))
        Dim iResult As Integer = 0

        Try

            Dim oConnection As SqlClient.SqlConnection = goData.GetConnection
            Dim oCommand As New SqlClient.SqlCommand
            Dim oReader As SqlClient.SqlDataReader

            oCommand.CommandText = "pPermPageDelete"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter
            Dim uSection As New SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier)
            If sSection = "GLOBAL" Or sSection = "" Then
                uSection.Value = System.DBNull.Value
            Else
                uSection.Value = goTR.StringToGuid(sSection)
            End If
            oCommand.Parameters.Add(uSection)

            'parameter
            Dim strPage As New SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar)
            strPage.Value = sPage
            oCommand.Parameters.Add(strPage)

            'return parameter
            Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
            retValParam.Direction = ParameterDirection.ReturnValue
            oCommand.Parameters.Add(retValParam)

            'execute

            oReader = oCommand.ExecuteReader()

            'Now you can grab the output parameter's value...
            iResult = Convert.ToInt16(retValParam.Value)

            oReader.Close()
            oConnection.Close()

            If iResult = 0 Then
                Return True
            Else
                Return False 'no error checking in sproc yet
            End If

        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return False
        End Try

    End Function

    Public Function PageRead(ByVal par_sSection As String, _
                ByVal par_sPage As String, _
                Optional ByVal par_sDefaultPage As String = "", _
                Optional ByVal par_bUserOnly As Boolean = False) As String

        'MI 3/27/07 Added default Page evaluation.
        'MI 5/5/06  Added allowing a blank par_sSection (treated as GLOBAL)

        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Read a PAGE of permissions, merging automatically DEFAULT, GLOBAL and USER information (if asked for)
        'PARAMETERS:
        '		par_sSection	= 	Section value (Permissions are organized in Sections, Pages and Lines like metadata)
        '		par_sPage		= 	Page name (generally composed by a prefix (DSK_) and a SUID
        '		par_sDefaultPage=	Default page returned if nothing found
        '		par_bUserOnly	=	If TRUE, read only the par_sSection, and not the global one
        'RETURNS:
        '		A string containing the PAGE of permission lines.
        'HOW IT WORKS:
        '		Reads in uSer and global section and merge the information together (and with the default)
        '		If you don't want to merge with Global, set par_bUserOnly to TRUE
        '		if you don't want to merge with default, set par_sDefaultPage to ""
        'EXAMPLE:
        '		goPerm("GLOBAL","ACCESS","") will return global ACCESS permissions

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim sSection As String = UCase(Trim(par_sSection))   'Transform the possibly numerical value into a string
        Dim sPage As String = UCase(Trim(par_sPage))       'Transform the possibly numerical value into a string
        Dim lRec As Long = 0
        Dim sDefaultPage As String = par_sDefaultPage
        Dim bUserOnly As Boolean = par_bUserOnly

        'A blank section is 'global'
        If sSection = "" Then
            sSection = "GLOBAL"
        End If

        'Blank page - return empty string
        If sPage = "" Then Return ""

        'setup sql connection
        If oMetaConnection Is Nothing Then
            oMetaConnection = goData.GetConnection
        End If
        Dim oConnection As SqlClient.SqlConnection = oMetaConnection
        Dim oCommand As New SqlClient.SqlCommand
        Dim oReader As SqlClient.SqlDataReader
        Dim sReturn As String = ""

        Try

            oCommand.CommandText = "pPermPageRead"
            oCommand.CommandType = CommandType.StoredProcedure
            oCommand.Connection = oConnection

            'parameter 
            Dim uSection As New SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier)
            If sSection = "GLOBAL" Then
                uSection.Value = System.DBNull.Value
            Else
                uSection.Value = goTR.StringToGuid(sSection)
            End If
            oCommand.Parameters.Add(uSection)

            'parameter 
            Dim strPage As New SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar)
            strPage.Value = sPage
            oCommand.Parameters.Add(strPage)

            'parameter 
            Dim iUserOnly As New SqlClient.SqlParameter("@par_bUserOnly", SqlDbType.Bit)
            iUserOnly.Value = Convert.ToInt16(bUserOnly)
            oCommand.Parameters.Add(iUserOnly)

            'execute sproc
            oReader = oCommand.ExecuteReader()

            'read returned value
            If oReader.HasRows Then
                Do While oReader.Read()
                    Dim sKey As String = Trim(oReader(0).ToString)
                    Dim sVal As String = Trim(oReader(1).ToString)
                    sReturn = sReturn & sKey & "=" & sVal & vbCrLf
                Loop
            Else
                sReturn = ""
            End If
            sReturn = goTR.MergeIniStrings(sReturn, par_sDefaultPage)

            oReader.Close()
            'oConnection.Close()

        Catch ex As Exception

            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If

            sReturn = ""

        End Try

        Return sReturn

    End Function

    Public Function PageWrite(ByVal par_sSection As String, _
                ByVal par_sPage As String, _
                ByVal par_sVal As String, _
                Optional ByVal par_sFriendlyName As String = "", _
                Optional ByVal par_bLocal As Boolean = False, _
                Optional ByVal par_sTextValue As String = "") As Boolean

        '==> par_bLocal is currently ignored until syncing is supported

        'AUTHOR: 
        '       WGT 3/15/2006
        'PURPOSE:
        '		Write a permissions Page identified by a section and Page
        'PARAMETERS:
        '		par_sSection		= First part of the identifier, "GLOBAL" for global permissions
        '		par_sPage			= Second part of the identifier... The identifier is supposed UNIQUE because the
        '							  Page should contain XXX_GID (GID is a ''UNIQUE'' generated ID)
        '		par_sVal			= String to write in the memo field
        '		par_sFriendlyName	= Friendly Name of the page that is stored in the NAME property
        '							  under the default (US) language.
        '		par_bLocal			= *** Ignored in SellSQL ***
        '                           If true, the page is saved as local instead of Protected for user section and shared for GLOBAL
        '		par_sTextValue		= *** Ignored in SellSQL ***
        '                           in NGP was the value to store in TXT_TextValue
        'RETURNS:
        '		TRUE or FALSE depending of the result of the writing
        'HOW IT WORKS:
        '		First try to read the record identified by Section+Page, then make a hAdd or 
        '		hModify depending if it has been previously found or not
        'EXAMPLE:
        '       Dim sPerms as string
        '       goTr.StrWrite(sPerms, "THISPERMISSION", "1")
        '       goTr.StrWrite(sPerms, "THATPERMISSION", "0")
        '		goPerm.PageWrite("GLOBAL", "ACCESS", sPerms)

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim bLocal As Boolean = par_bLocal
        Dim sSection As String = UCase(Trim(par_sSection))
        Dim sPage As String = UCase(Trim(par_sPage))
        Dim sTextValue As String = par_sTextValue
        Dim sVal As String = par_sVal
        Dim sFriendlyName As String = par_sFriendlyName
        Dim iCRPos As Integer = 0
        Dim sLine As String
        Dim sProp As String
        Dim sValue As String
        Dim iCount As Integer = 1
        Dim iEqualPos As Integer
        Dim bResult As Boolean = True

        If sPage = "" Then
            goErr.SetError(35506, sProc, , par_sSection, par_sPage, par_sFriendlyName, par_bLocal.ToString, par_sVal)
            '35506: PageWrite was called with a blank page parameter. Metadata or permission page can't be written.
            ' 
            'Section:    '[1]'
            'Page:       '[2]'
            'Friendly(Name) '[3]'
            'Local:      '[4]'
            'Value:      '[5]'
            '[6]
            Return False
        End If

        'Add the final CR if missing
        If Right(sVal, 2) <> vbCrLf Then
            sVal &= vbCrLf      '*** MI 5/5/06 Changed += to &=
        End If

        '---------- TEST WRITING TO SS ---------
        'Test whether we can write a test line without errors.
        'This is needed because the whole page will be deleted next, before lines are
        'written. If the writing of the lines fails, the page will be lost or incomplete.
        If Not LineWrite("GLOBAL", "OTH_TEST", "clPerm.PageWrite", "Test") Then
            goErr.SetError(35505, sProc, , sSection, sPage)
            '35505: Metadata or permission page couldn't be written. Make sure that the server is running and that the ASP user has adequate permissions on it.
            ' 
            'Section:    '[1]'
            'Page:       '[2]'
            Return False
        End If

        '------------ DELETE PAGE -----------
        'Run pPermPageDelete to start with a clean slate, otherwise we
        'have to go PageRead first and delete the properties one by one
        If Not PageDelete(sSection, sPage) Then
            goErr.SetError(35500, sProc, , sSection, sPage)
            '35500: Deleting a metadata or permission page failed. Section: '[1]', page: '[2]'. [3]
            Return False
        End If

        '==> Replace this later with reading the whole page and comparing lines, then
        'updating the changed lines and deleting the deleted lines.
        'This will be faster and will preserve the ModTime of unchanged lines, which
        'are all being wiped out now. OR MAYBE THEY SHOULD ALL BE UPDATED?

        Try

            Dim oConnection As SqlClient.SqlConnection = goData.GetConnection
            iCount = 1
            iEqualPos = 0
            Do
                'Get the line
                sLine = goTR.ExtractString(sVal, iCount, vbCrLf)
                'Skip blank line
                If sLine = "" Then GoTo BottomOfLoop
                'clC.EOT indicates end of loop
                If sLine = clC.EOT Then Exit Do

                iEqualPos = InStr(sLine, "=")
                If iEqualPos < 1 Then
                    'Line doesn't have an =. It may be a comment?
                    goErr.SetWarning(35501, sProc, , sLine)
                    '35501: Invalid metadata line (no '='): '[1]'. [2]
                    GoTo BottomOfLoop
                End If

                sProp = goTR.FromTo(sLine, 1, iEqualPos - 1)
                sValue = goTR.FromTo(sLine, iEqualPos + 1, -1)

                'Skip blank property
                If Trim(sProp) = "" Then GoTo BottomOfLoop

                If Not LineWrite(sSection, sPage, sProp, sValue, oConnection) Then
                    bResult = False
                    goErr.SetWarning(35502, sProc, , sSection, sPage, sProp, sValue)
                    '35502: Metadata or permission line failed to be written.
                    'Section:            '[1]'
                    'Page:               '[2]'
                    'Property: '[3]'
                    'Value: '[4]'
                    '[5]
                End If

BottomOfLoop:
                iCount = iCount + 1
            Loop

            'write the friendly value...
            If sFriendlyName <> "" Then
                If Not LineWrite(sSection, sPage, "NAME", sFriendlyName, oConnection) Then
                    bResult = False
                    goErr.SetWarning(35502, sProc, , sSection, sPage, "NAME", sFriendlyName)
                    '35502: Metadata or permission line failed to be written.
                    'Section:            '[1]'
                    'Page:               '[2]'
                    'Property: '[3]'
                    'Value: '[4]'
                    '[5]
                End If
            End If

            oConnection.Close()

            If bResult Then
                Return True
            Else
                Return False
            End If

        Catch ex As Exception

            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If

            Return False

        End Try

    End Function


    '    Public Function GetDefaultAccessPermissions(Optional ByVal par_sFile As String = "") As String
    '        'MI 10/31/13 This is coded in diaAdmPerm.FillAccess() now. Deprecating this method.
    '        'MI 9/17/09 This method currently isn't called from anywhere.
    '        'MI 3/26/07 Created.
    '        'PURPOSE:
    '        '       Retrieves an ini string with selective access permissions either for one file
    '        '       or all of them. This returns all '(none)' permissions to prevent users from gaining
    '        '       greater permissions than intended if some permission metadata is deleted inadvertently.
    '        '       Default permissions for each file should be defined as GLOBAL metadata in the XP
    '        '       table when creating a file. If permissions are not defined, they should be none.
    '        'PARAMETERS: 
    '        '       par_sFile: File for which to retrieve access permissions. If blank, permissions
    '        '           are returned for all files.
    '        'RETURNS:
    '        '       Ini format string to be read with goTr.StrRead().

    '        'Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
    '        'goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

    '        Dim aFiles As New clArray
    '        Dim i As Integer
    '        Dim sFile As String = UCase(par_sFile)
    '        Dim s As String = ""

    '        If sFile <> "" Then
    '            goTR.StrWrite(s, sFile & "_R", "2")     'originally was "0"
    '            goTR.StrWrite(s, sFile & "_A", "2")     'originally was "0"
    '            goTR.StrWrite(s, sFile & "_E", "1")     'originally was "0"
    '            goTR.StrWrite(s, sFile & "_D", "1")     'originally was "0"
    '            goTR.StrWrite(s, sFile & "_RCOND", GetDataAccessDefaultFilter(sFile, "CONDITION"))
    '            goTR.StrWrite(s, sFile & "_ECOND", GetDataAccessDefaultFilter(sFile, "CONDITION"))
    '            goTR.StrWrite(s, sFile & "_DCOND", GetDataAccessDefaultFilter(sFile, "CONDITION"))
    '            goTR.StrWrite(s, sFile & "_RFILTER", GetDataAccessDefaultFilter(sFile))
    '            goTR.StrWrite(s, sFile & "_EFILTER", GetDataAccessDefaultFilter(sFile))
    '            goTR.StrWrite(s, sFile & "_DFILTER", GetDataAccessDefaultFilter(sFile))

    '        Else
    '            aFiles = goData.GetFiles()
    '            For i = 0 To aFiles.GetDimension() - 1
    '                sFile = UCase(aFiles.GetInfo(i))
    '                'If Not goData.IsFileSystem(sFile) Then GoTo ProcessNextFile
    '                '                Select Case sFile
    '                '                    Case "MS"
    '                '                        'Message
    '                '                        goTR.StrWrite(s, sFile & "_R", "1")
    '                '                        goTR.StrWrite(s, sFile & "_A", "2")
    '                '                        goTR.StrWrite(s, sFile & "_E", "1")
    '                '                        goTR.StrWrite(s, sFile & "_D", "1")
    '                '                        goTR.StrWrite(s, sFile & "_R_COND", "LNK_To_US='<%MEID%>' or LNK_Createdby_US='<%MEID%>'")
    '                '                        goTR.StrWrite(s, sFile & "_E_COND", "LNK_To_US='<%MEID%>' or LNK_Createdby_US='<%MEID%>'")
    '                '                        goTR.StrWrite(s, sFile & "_D_COND", "LNK_CreatedBy_US='<%MEID%>'")
    '                '                    Case Else
    '                '                        goTR.StrWrite(s, sFile & "_R", "2")
    '                '                        goTR.StrWrite(s, sFile & "_A", "2")
    '                '                        goTR.StrWrite(s, sFile & "_E", "1")
    '                '                        goTR.StrWrite(s, sFile & "_D", "1")
    '                '                        goTR.StrWrite(s, sFile & "_R_COND", "LNK_CreatedBy_US='<%MEID%>'")
    '                '                        goTR.StrWrite(s, sFile & "_E_COND", "LNK_CreatedBy_US='<%MEID%>'")
    '                '                        goTR.StrWrite(s, sFile & "_D_COND", "LNK_CreatedBy_US='<%MEID%>'")
    '                '                End Select
    '                goTR.StrWrite(s, sFile & "_R", "2")     'originally was "0"
    '                goTR.StrWrite(s, sFile & "_A", "2")     'originally was "0"
    '                goTR.StrWrite(s, sFile & "_E", "1")     'originally was "0"
    '                goTR.StrWrite(s, sFile & "_D", "1")     'originally was "0"
    '                goTR.StrWrite(s, sFile & "_R_COND", GetDataAccessDefaultFilter(sFile, "CONDITION"))
    '                goTR.StrWrite(s, sFile & "_E_COND", GetDataAccessDefaultFilter(sFile, "CONDITION"))
    '                goTR.StrWrite(s, sFile & "_D_COND", GetDataAccessDefaultFilter(sFile, "CONDITION"))
    '                goTR.StrWrite(s, sFile & "_RFILTER", GetDataAccessDefaultFilter(sFile))
    '                goTR.StrWrite(s, sFile & "_EFILTER", GetDataAccessDefaultFilter(sFile))
    '                goTR.StrWrite(s, sFile & "_DFILTER", GetDataAccessDefaultFilter(sFile))
    'ProcessNextFile:
    '            Next

    '        End If

    '        Return s

    '    End Function



    'Public Function GetDataAccssPermLbl(ByVal par_sPermission As String) As String
    '    'MI 3/27/07 Moved to clPerm from clDefaults::GetDataAccssPermLbl.
    '    'PURPOSE:
    '    '		Retrieve the label for a data access permission in the current language.
    '    'PARAMETERS:
    '    '		par_sPermission: String or number: a permission constant
    '    '		defined in Selltis.wl:
    '    '			SELL_DEFAULT			=  -1
    '    '			SELL_NO					=	0
    '    '			SELL_SELECTIVE			=	1
    '    '			SELL_FULL				=	2
    '    'RETURNS:
    '    '		Label as string.

    '    'Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
    '    'goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

    '    Dim sResult As String = ""

    '    Select Case par_sPermission
    '        Case clC.SELL_DEFAULT       '-1
    '            sResult = "(Default)" 'messtranslate MessTraduit(5237)   '5237:<Default>
    '        Case clC.SELL_NO            '0
    '            sResult = "None" 'messtranslate MessTraduit(5238)   '5238:None
    '        Case clC.SELL_SELECTIVE     '1
    '            sResult = "Selective" 'messtranslate MessTraduit(5239)   '5239:Selective
    '        Case clC.SELL_FULL          '2
    '            sResult = "Full" 'messtranslate MessTraduit(5240)   '5240:Full
    '    End Select

    '    Return sResult
    'End Function



    Public Sub Initialize()
        goP = HttpContext.Current.Session("goP")
        goTR = HttpContext.Current.Session("goTr")
        'goMeta = HttpContext.Current.Session("goMeta")
        goData = HttpContext.Current.Session("goData")
        goErr = HttpContext.Current.Session("goErr")
        goLog = HttpContext.Current.Session("goLog")
        'goDef = HttpContext.Current.Session("goDef")
        goPerm = HttpContext.Current.Session("goPerm")
    End Sub

    Public Sub New()

    End Sub

End Class