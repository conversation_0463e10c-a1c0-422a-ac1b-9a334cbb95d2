﻿CREATE TABLE [dbo].[QT] (
    [GID_ID]                         UNIQUEIDENTIFIER CONSTRAINT [DF_QT_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'QT',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                         BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                       NVARCHAR (1000)  NULL,
    [TXT_AddressMailing]             NVARCHAR (300)   NULL,
    [FIL_Attachments]                NTEXT            NULL,
    [CHK_Open]                       TINYINT          NULL,
    [MMO_CoverLetter]                NTEXT            NULL,
    [DTT_CreationTime]               DATETIME         CONSTRAINT [DF_QT_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [MLS_Currency]                   SMALLINT         NULL,
    [DTT_DateClosed]                 DATETIME         NULL,
    [CHK_DemoData]                   TINYINT          NULL,
    [TXT_Description]                NVARCHAR (1000)  NULL,
    [EML_Email]                      TEXT             NULL,
    [SR__ExchRate]                   REAL             NULL,
    [MMO_UnderSignature]             NTEXT            NULL,
    [DTT_ExpCloseDate]               DATETIME         NULL,
    [TEL_Fax]                        VARCHAR (39)     NULL,
    [FIL_File]                       NTEXT            NULL,
    [TXT_FOB]                        NVARCHAR (20)    NULL,
    [DTT_NextActionDate]             DATETIME         NULL,
    [MMO_History]                    NTEXT            NULL,
    [FIL_Inclusions]                 NTEXT            NULL,
    [MMO_Journal]                    NTEXT            NULL,
    [TXT_AboveSignature]             NVARCHAR (30)    NULL,
    [MMO_Closing]                    NTEXT            NULL,
    [SI__Month]                      TINYINT          NULL,
    [MMO_NextAction]                 NTEXT            NULL,
    [MMO_Notes]                      NTEXT            NULL,
    [CHK_NoUpdInclusions]            TINYINT          NULL,
    [CUR_OtherCharge]                MONEY            NULL,
    [TXT_OtherChargeReason]          NVARCHAR (20)    NULL,
    [TXT_PONo]                       NVARCHAR (50)    NULL,
    [CHK_IncludeLetterhead]          TINYINT          NULL,
    [CHK_IncludeTaxCharges]          TINYINT          NULL,
    [CHK_IncludeTerms]               TINYINT          NULL,
    [CHK_IncludeTotals]              TINYINT          NULL,
    [MLS_Priority]                   SMALLINT         NULL,
    [TXT_ProposedShipping]           NVARCHAR (50)    NULL,
    [MMO_Questionnaire]              NTEXT            NULL,
    [TXT_QuoteNo]                    NVARCHAR (500)   NULL,
    [TXT_QuoteRefNo]                 NVARCHAR (20)    NULL,
    [TXT_QuoteTitle]                 NVARCHAR (40)    NULL,
    [MLS_ReasonWonLost]              SMALLINT         NULL,
    [TXT_RFQNo]                      NVARCHAR (20)    NULL,
    [CUR_SalesTax]                   MONEY            NULL,
    [SR__SalesTaxPercent]            REAL             NULL,
    [MMO_SendData]                   NTEXT            NULL,
    [CHK_Sent]                       TINYINT          NULL,
    [CUR_Shipping]                   MONEY            NULL,
    [TXT_ShippingTerms]              NVARCHAR (30)    NULL,
    [TXT_Signature]                  NVARCHAR (40)    NULL,
    [MLS_Status]                     SMALLINT         NULL,
    [CUR_Subtotal]                   MONEY            NULL,
    [CUR_SubtotalT]                  MONEY            NULL,
    [DTT_Time]                       DATETIME         NULL,
    [CUR_Total]                      MONEY            NULL,
    [URL_URLs]                       NTEXT            NULL,
    [CHK_UseSignature]               TINYINT          NULL,
    [DTT_ValidUntilDate]             DATETIME         NULL,
    [TXT_Year]                       CHAR (4)         NULL,
    [TXT_ModBy]                      VARCHAR (4)      NULL,
    [DTT_ModTime]                    DATETIME         CONSTRAINT [DF_QT_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [MMO_ImportData]                 NTEXT            NULL,
    [SI__ShareState]                 TINYINT          CONSTRAINT [DF_QT_SI__ShareState] DEFAULT ((2)) NULL,
    [CHK_Report]                     TINYINT          CONSTRAINT [DF_QT_CHK_Report] DEFAULT ((1)) NULL,
    [GID_To_CO]                      UNIQUEIDENTIFIER NULL,
    [GID_OriginatedBy_CN]            UNIQUEIDENTIFIER NULL,
    [GID_Related_DM]                 UNIQUEIDENTIFIER NULL,
    [GID_Template_DO]                UNIQUEIDENTIFIER NULL,
    [GID_TakenAt_LO]                 UNIQUEIDENTIFIER NULL,
    [GID_Related_PR]                 UNIQUEIDENTIFIER NULL,
    [GID_ClonedFrom_QT]              UNIQUEIDENTIFIER NULL,
    [GID_Related_ST]                 UNIQUEIDENTIFIER NULL,
    [GID_From_SO]                    UNIQUEIDENTIFIER NULL,
    [GID_Related_TR]                 UNIQUEIDENTIFIER NULL,
    [GID_CreatedBy_US]               UNIQUEIDENTIFIER NULL,
    [GID_CreditedTo_US]              UNIQUEIDENTIFIER NULL,
    [GID_Peer_US]                    UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]                   VARCHAR (50)     NULL,
    [CHK_UpdQLStatus]                TINYINT          NULL,
    [SI__Day]                        TINYINT          NULL,
    [TXT_ExternalID]                 NVARCHAR (120)   NULL,
    [TXT_ExternalSource]             VARCHAR (10)     NULL,
    [TXT_ImpJobID]                   VARCHAR (20)     NULL,
    [CHK_Budget]                     TINYINT          NULL,
    [ADR_Attachments]                NTEXT            NULL,
    [ADR_Inclusions]                 NTEXT            NULL,
    [CHK_RFQCREATED]                 TINYINT          NULL,
    [GID_RELATED_PD]                 UNIQUEIDENTIFIER NULL,
    [MLS_OPPORTUNITYTYPE]            SMALLINT         NULL,
    [TXT_LinkedOppNo]                NVARCHAR (250)   NULL,
    [MLS_TYPEOFBID]                  SMALLINT         NULL,
    [TXT_OPPORTUNITYTYPE]            NVARCHAR (250)   NULL,
    [CHK_PRIMARYQUOTE]               TINYINT          NULL,
    [TXT_OPPORTUNITYNAME]            NVARCHAR (1000)  NULL,
    [TXT_SHIPPINGADDRESS]            NVARCHAR (1000)  NULL,
    [MLS_PAYMENTTERMS]               SMALLINT         NULL,
    [TXT_SALESREP]                   NVARCHAR (80)    NULL,
    [TXT_SHIPPINGTERMSFORMULA]       NVARCHAR (100)   NULL,
    [TXT_INCLUDESHIPPINGINOUTPUT]    NVARCHAR (100)   NULL,
    [CHK_CAPEXREQUIRED]              TINYINT          NULL,
    [TXT_BILLINGADDRESS]             NVARCHAR (2000)  NULL,
    [TXT_BILLINGADDITIONALCOMMENTS]  NVARCHAR (100)   NULL,
    [TXT_BILLINGCONTACTNAME]         NVARCHAR (1000)  NULL,
    [TXT_BILLINGPHONE]               NVARCHAR (80)    NULL,
    [EML_BILLINGEMAIL]               TEXT             NULL,
    [MLS_FINALDESTINATION]           SMALLINT         NULL,
    [TXT_SHIPPINGSPECIFIEDCARRIER]   NVARCHAR (100)   NULL,
    [TXT_SHIPPINGADDITIONALCOMMENTS] NVARCHAR (2000)  NULL,
    [TXT_SHIPPINGCONTACTNAME]        NVARCHAR (100)   NULL,
    [EML_SHIPPINGEMAIL]              TEXT             NULL,
    [TEL_SHIPPINGPHONE]              VARCHAR (80)     NULL,
    [SI__RENTALRATES]                TINYINT          NULL,
    [DTT_REQUESTEDSHIPDATE]          DATETIME         NULL,
    [CHK_GROUPLINEITEMS]             TINYINT          NULL,
    [CHK_PRINTLINEITEMS]             TINYINT          NULL,
    [CHK_WATERMARKSHOWN]             TINYINT          NULL,
    [GID_FOR_CO]                     UNIQUEIDENTIFIER NULL,
    [TXT_CUSTOMERAMOUNT]             NVARCHAR (80)    NULL,
    [TXT_NETAMOUNT]                  NVARCHAR (80)    NULL,
    [TXT_OPPORTUNITYREVISION]        NVARCHAR (100)   NULL,
    [TXT_ADDITIONALDISC]             NVARCHAR (80)    NULL,
    [TXT_TOTALCUSTOMERDISCAMOUNT]    NVARCHAR (80)    NULL,
    [TXT_ADDITIONALDISCAMOUNT]       NVARCHAR (80)    NULL,
    [MLS_SHIPFROMLOCATION]           SMALLINT         NULL,
    [MMO_LEADTIME]                   NTEXT            NULL,
    [MLS_TYPEOFBILLING]              SMALLINT         NULL,
    [LI__MinRentalDays]              INT              NULL,
    [MLS_UNITTYPE]                   SMALLINT         NULL,
    [MLS_SHIPPINGAGENT]              SMALLINT         NULL,
    [MMO_RentalRates]                NTEXT            NULL,
    [MLS_PLACEOFDELIVERY]            SMALLINT         NULL,
    [MMO_Introduction]               NTEXT            NULL,
    [DTT_PRICEVALIDITY]              DATETIME         NULL,
    [MLS_PRICEBOOK]                  SMALLINT         NULL,
    [MLS_TYPE]                       SMALLINT         NULL,
    [CUR_NETAMOUNT]                  MONEY            NULL,
    [CUR_AMOUNT]                     MONEY            NULL,
    [CUR_TOTALAMOUNTUSD]             MONEY            NULL,
    [CUR_RENTALAMOUNT]               MONEY            NULL,
    [CUR_TOTALAMOUNT]                MONEY            NULL,
    [TXT_PRIMARYQUOTENO]             NVARCHAR (150)   NULL,
    [GID_QUOTEPREPAREDFOR_CN]        UNIQUEIDENTIFIER NULL,
    [MMO_NOTESFORQUOTE]              NTEXT            NULL,
    [TXT_OPPNO]                      VARCHAR (250)    NULL,
    [MLS_REGION]                     SMALLINT         NULL,
    [MLS_LOB]                        SMALLINT         NULL,
    [MLS_STAGE]                      SMALLINT         NULL,
    [MLS_UNITCONDITION]              SMALLINT         NULL,
    [TXT_REVISION]                   NVARCHAR (80)    NULL,
    [MLS_TYPEOFBUSINESS]             SMALLINT         NULL,
    [MLS_RISKLEVEL]                  SMALLINT         NULL,
    [SI__Probability]                TINYINT          NULL,
    [MLS_SCOPEFITCAPABILITIES]       SMALLINT         NULL,
    [MLS_URGENCY]                    SMALLINT         NULL,
    [MLS_TIMELINE]                   SMALLINT         NULL,
    [MLS_DECISIONMAKERENGAGEMENT]    SMALLINT         NULL,
    [TXT_STATEBILLING]               NVARCHAR (100)   NULL,
    [TXT_STATESHIPPING]              NVARCHAR (100)   NULL,
    [TXT_ZIPBILLING]                 NVARCHAR (100)   NULL,
    [TXT_ZIPSHIPPING]                NVARCHAR (100)   NULL,
    [MLS_SHIPPINGTERMS]              SMALLINT         NULL,
    [TXT_ORDERNUMBER]                NVARCHAR (80)    NULL,
    [GID_RELATED_SO]                 UNIQUEIDENTIFIER NULL,
    [CHK_ORDERED]                    TINYINT          NULL,
    [CHK_WAITINGFORACCOUNTNAVID]     TINYINT          NULL,
    [Txt_TotalAmount]                AS               (case [Cur_TotalAmount] when (0) then '' else ([dbo].[GetCurrencyCode]([MLS_Currency])+' ')+replace(CONVERT([nvarchar](20),format([Cur_TotalAmount],'C2'),(0)),'$','') end),
    [MMO_SIGNATURE]                  NTEXT            NULL,
    [INT_REVNO]                      SMALLINT         NULL,
    [MMO_FieldsAuditTrail]           NTEXT            NULL,
    [TXT_PDFTempPath]                NVARCHAR (2000)  NULL,
    [CHK_Commit]                     TINYINT          NULL,
    [GID_PRIMARYREASONCODE_RC]       UNIQUEIDENTIFIER NULL,
    [GID_SECONDARYREASONCODE_RC]     UNIQUEIDENTIFIER NULL,
    [GID_RELATED_VE]                 UNIQUEIDENTIFIER NULL,
    [GID_RELATED_PC]                 UNIQUEIDENTIFIER NULL,
    [GID_RELATED_PG]                 UNIQUEIDENTIFIER NULL,
    [GID_RELATED_MO]                 UNIQUEIDENTIFIER NULL,
    [TXT_MODEL]                      NVARCHAR (250)   NULL,
    [SR__QTY]                        REAL             NULL,
    [CUR_UnitPrice]                  MONEY            NULL,
    [CHK_DIRECTQUOTE]                TINYINT          NULL,
    [CUR_COST]                       MONEY            NULL,
    [CUR_GROSSPROFIT]                MONEY            NULL,
    [SI__GROSSMARGIN]                TINYINT          NULL,
    [SI__SELLMULTI]                  TINYINT          NULL,
    [SI__COSTMULTI]                  TINYINT          NULL,
    [TXT_JCIQTNO]                    NVARCHAR (150)   NULL,
    [GID_RELATED_PF]                 UNIQUEIDENTIFIER NULL,
    [TXT_LeadTime]                   NVARCHAR (500)   NULL,
    [TXT_FreightTerms]               NVARCHAR (500)   NULL,
    [TXT_FOBPoint]                   NVARCHAR (500)   NULL,
    [GID_JCIID]                      UNIQUEIDENTIFIER NULL,
    [MLS_QTTEMPLATE]                 SMALLINT         NULL,
    [SI__WEEKS]                      TINYINT          NULL,
    [TXT_QuoteNoOrig]                NVARCHAR (250)   NULL,
    [GID_RELATED_BC]                 UNIQUEIDENTIFIER NULL,
    [CUR_POCOST]                     MONEY            NULL,
    [MLS_QTEPUMPSTEMPLATE]           SMALLINT         NULL,
    [CHK_AssemblyLineItem]           TINYINT          NULL,
    [TXT_InternalQuoteNo]            NVARCHAR (150)   NULL,
    [TXT_SXOrderNo]                  NVARCHAR (250)   NULL,
    [MLS_SXOrderType]                SMALLINT         NULL,
    [CUR_BLORDERAMOUNT]              MONEY            NULL,
    [CHK_SXQUOTE]                    TINYINT          NULL,
    [CUR_BLQTPipelineAmount]         MONEY            NULL,
    [TXT_SXSTATUS]                   NVARCHAR (250)   NULL,
    [DTT_OrigCreationTime]           DATETIME         NULL,
    [MLS_SXSTAGE]                    SMALLINT         NULL,
    [INT_SXSTAGE]                    SMALLINT         NULL,
    [TXT_SXPACKAGEID]                NVARCHAR (250)   NULL,
    [CHK_MARKASLOST]                 TINYINT          NULL,
    [CUR_LineTotalOpen]              AS               ([dbo].[Get_QL_Totals]([GID_ID],(0),(1))),
    [CUR_LineTotalOnHold]            AS               ([dbo].[Get_QL_Totals]([GID_ID],(1),(1))),
    [CUR_LineTotalWon]               AS               ([dbo].[Get_QL_Totals]([GID_ID],(2),(1))),
    [CUR_LineTotalLost]              AS               ([dbo].[Get_QL_Totals]([GID_ID],(3),(1))),
    [CUR_LineTotalCancelled]         AS               ([dbo].[Get_QL_Totals]([GID_ID],(4),(1))),
    [CUR_LineTotalDeleted]           AS               ([dbo].[Get_QL_Totals]([GID_ID],(5),(1))),
    [CUR_LineTotalRevised]           AS               ([dbo].[Get_QL_Totals]([GID_ID],(6),(1))),
    [SI__LineGMOpen]                 AS               ([dbo].[Get_QL_Totals]([GID_ID],(0),(2))),
    [SI__LineGMOnHold]               AS               ([dbo].[Get_QL_Totals]([GID_ID],(1),(2))),
    [SI__LineGMWon]                  AS               ([dbo].[Get_QL_Totals]([GID_ID],(2),(2))),
    [SI__LineGMLost]                 AS               ([dbo].[Get_QL_Totals]([GID_ID],(3),(2))),
    [SI__LineGMCancelled]            AS               ([dbo].[Get_QL_Totals]([GID_ID],(4),(2))),
    [SI__LineGMDeleted]              AS               ([dbo].[Get_QL_Totals]([GID_ID],(5),(2))),
    [SI__LineGMRevised]              AS               ([dbo].[Get_QL_Totals]([GID_ID],(6),(2))),
    [CUR_LineGFOpen]                 AS               ([dbo].[Get_QL_Totals]([GID_ID],(0),(3))),
    [CUR_LineGFOnHold]               AS               ([dbo].[Get_QL_Totals]([GID_ID],(1),(3))),
    [CUR_LineGFWon]                  AS               ([dbo].[Get_QL_Totals]([GID_ID],(2),(3))),
    [CUR_LineGFLost]                 AS               ([dbo].[Get_QL_Totals]([GID_ID],(3),(3))),
    [CUR_LineGFCancelled]            AS               ([dbo].[Get_QL_Totals]([GID_ID],(4),(3))),
    [CUR_LineGFDeleted]              AS               ([dbo].[Get_QL_Totals]([GID_ID],(5),(3))),
    [CUR_LineGFRevised]              AS               ([dbo].[Get_QL_Totals]([GID_ID],(6),(3))),
    [CHK_RFQManpart]                 TINYINT          NULL,
    [CHK_RFQCusPart]                 TINYINT          NULL,
    [CHK_RFQDescription]             TINYINT          NULL,
    [CHK_RFQLocation]                TINYINT          NULL,
    [CHK_RFQMinStockReq]             TINYINT          NULL,
    [CHK_RFQCustomerStock]           TINYINT          NULL,
    [CHK_RFQChangeoutFreq]           TINYINT          NULL,
    [CHK_RFQChangeoutQty]            TINYINT          NULL,
    [CUR_ExpectedValue]              MONEY            NULL,
    [SR__SKU]                        REAL             NULL,
    [CUR_FDFLINEVALUE]               MONEY            NULL,
    [GID_RELATED_OP]                 UNIQUEIDENTIFIER NULL,
    [MLS_SOURCE]                     SMALLINT         NULL,
    [TXT_ExtStatCode]                NVARCHAR (1000)  NULL,
    [TXT_ExtStatus]                  NVARCHAR (1000)  NULL,
    [CUR_SXQuoteAmount]              AS               (case when [MLS_SOURCE]=(2) then [CUR_TOTALAMOUNT] else (0) end),
    [CUR_NONSXQuoteAmount]           AS               (case when isnull([MLS_SOURCE],(0))=(0) OR [MLS_SOURCE]=(1) then [CUR_TOTALAMOUNT] else (0) end),
    [CUR_CSDQuoteAmount]             AS               (case when [MLS_SOURCE]=(3) then [CUR_TOTALAMOUNT] else (0) end),
    [CUR_ISMQuoteAmount]             AS               (case when [MLS_SOURCE]=(4) then [CUR_TOTALAMOUNT] else (0) end),
    [SR__GO]                         REAL             NULL,
    [SR__HP]                         REAL             NULL,
    [GID_RELATED_BR]                 UNIQUEIDENTIFIER NULL,
    [MLS_JOURNALTYPE]                SMALLINT         NULL,
    [TXT_LOSTREASONCODE]             NVARCHAR (250)   NULL,
    [GID_RELATED_BU]                 UNIQUEIDENTIFIER NULL,
    [CHK_QL_NOTINCLUDEINTOTAL]       TINYINT          NULL,
    [TXT_ShipToName]                 NVARCHAR (1000)  NULL,
    [DTT_AppStageDate]               DATETIME         NULL,
    [DTT_POStageDate]                DATETIME         NULL,
    [DTT_RFQStageDate]               DATETIME         NULL,
    [DTT_SubmitStageDate]            DATETIME         NULL,
    [TXT_Reference]                  NVARCHAR (1000)  NULL,
    [TXT_RevisedQTNo]                NVARCHAR (250)   NULL,
    [TXT_SalesRepIn]                 NVARCHAR (500)   NULL,
    [TXT_SalesRepOut]                NVARCHAR (500)   NULL,
    CONSTRAINT [PK_QT] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_QT_ClonedFrom_QT] FOREIGN KEY ([GID_ID]) REFERENCES [dbo].[QT] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_CreditedTo_US] FOREIGN KEY ([GID_CreditedTo_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_From_SO] FOREIGN KEY ([GID_From_SO]) REFERENCES [dbo].[SO] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_OriginatedBy_CN] FOREIGN KEY ([GID_OriginatedBy_CN]) REFERENCES [dbo].[CN] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_Peer_US] FOREIGN KEY ([GID_Peer_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_Related_DM] FOREIGN KEY ([GID_Related_DM]) REFERENCES [dbo].[DM] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_Related_PR] FOREIGN KEY ([GID_Related_PR]) REFERENCES [dbo].[PR] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_Related_ST] FOREIGN KEY ([GID_Related_ST]) REFERENCES [dbo].[ST] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_Related_TR] FOREIGN KEY ([GID_Related_TR]) REFERENCES [dbo].[TR] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_TakenAt_LO] FOREIGN KEY ([GID_TakenAt_LO]) REFERENCES [dbo].[LO] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_Template_DO] FOREIGN KEY ([GID_Template_DO]) REFERENCES [dbo].[DO] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_To_CO] FOREIGN KEY ([GID_To_CO]) REFERENCES [dbo].[CO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[QT] NOCHECK CONSTRAINT [LNK_QT_ClonedFrom_QT];


GO
ALTER TABLE [dbo].[QT] NOCHECK CONSTRAINT [LNK_QT_CreatedBy_US];


GO
ALTER TABLE [dbo].[QT] NOCHECK CONSTRAINT [LNK_QT_CreditedTo_US];


GO
ALTER TABLE [dbo].[QT] NOCHECK CONSTRAINT [LNK_QT_From_SO];


GO
ALTER TABLE [dbo].[QT] NOCHECK CONSTRAINT [LNK_QT_OriginatedBy_CN];


GO
ALTER TABLE [dbo].[QT] NOCHECK CONSTRAINT [LNK_QT_Peer_US];


GO
ALTER TABLE [dbo].[QT] NOCHECK CONSTRAINT [LNK_QT_Related_DM];


GO
ALTER TABLE [dbo].[QT] NOCHECK CONSTRAINT [LNK_QT_Related_PR];


GO
ALTER TABLE [dbo].[QT] NOCHECK CONSTRAINT [LNK_QT_Related_ST];


GO
ALTER TABLE [dbo].[QT] NOCHECK CONSTRAINT [LNK_QT_Related_TR];


GO
ALTER TABLE [dbo].[QT] NOCHECK CONSTRAINT [LNK_QT_TakenAt_LO];


GO
ALTER TABLE [dbo].[QT] NOCHECK CONSTRAINT [LNK_QT_Template_DO];


GO
ALTER TABLE [dbo].[QT] NOCHECK CONSTRAINT [LNK_QT_To_CO];


GO
CREATE NONCLUSTERED INDEX [IX_QT_To_CO]
    ON [dbo].[QT]([GID_To_CO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_DateTime]
    ON [dbo].[QT]([DTT_Time] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_Related_ST]
    ON [dbo].[QT]([GID_Related_ST] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_QuoteNo]
    ON [dbo].[QT]([TXT_QuoteNo] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_Name]
    ON [dbo].[QT]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_ExpCloseDteTotalRev]
    ON [dbo].[QT]([DTT_ExpCloseDate] ASC, [CUR_Total] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_ClonedFrom_QT]
    ON [dbo].[QT]([GID_ClonedFrom_QT] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_StatusExpClDteTotRev]
    ON [dbo].[QT]([MLS_Status] ASC, [DTT_ExpCloseDate] ASC, [CUR_Total] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_StatusTotalRev]
    ON [dbo].[QT]([MLS_Status] ASC, [CUR_Total] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_StatusPriNADteTotRev]
    ON [dbo].[QT]([MLS_Status] ASC, [MLS_Priority] ASC, [DTT_NextActionDate] ASC, [CUR_Total] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_CreditedTo_US]
    ON [dbo].[QT]([GID_CreditedTo_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_Template_DO]
    ON [dbo].[QT]([GID_Template_DO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_Related_PR]
    ON [dbo].[QT]([GID_Related_PR] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_Peer_US]
    ON [dbo].[QT]([GID_Peer_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_StatusNADtePrioTotRev]
    ON [dbo].[QT]([MLS_Status] ASC, [DTT_NextActionDate] ASC, [MLS_Priority] ASC, [CUR_Total] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_ModDateTime]
    ON [dbo].[QT]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_TXT_ImportID]
    ON [dbo].[QT]([TXT_ImportID] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_QT_BI__ID]
    ON [dbo].[QT]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_CreationTime]
    ON [dbo].[QT]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_StatusNADateTotalRev]
    ON [dbo].[QT]([MLS_Status] ASC, [DTT_NextActionDate] ASC, [CUR_Total] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_Related_TR]
    ON [dbo].[QT]([GID_Related_TR] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_StatusDateTimeRev]
    ON [dbo].[QT]([MLS_Status] ASC, [DTT_Time] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_Related_DM]
    ON [dbo].[QT]([GID_Related_DM] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_OriginatedBy_CN]
    ON [dbo].[QT]([GID_OriginatedBy_CN] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_TakenAt_LO]
    ON [dbo].[QT]([GID_TakenAt_LO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_From_SO]
    ON [dbo].[QT]([GID_From_SO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_CreatedBy_US]
    ON [dbo].[QT]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [QT_MLS_Status_Stage]
    ON [dbo].[QT]([MLS_Status] ASC, [MLS_STAGE] ASC)
    INCLUDE([GID_CreditedTo_US], [GID_Peer_US], [GID_FOR_CO], [CUR_TOTALAMOUNT], [SI__GROSSMARGIN]);


GO
CREATE NONCLUSTERED INDEX [Index1]
    ON [dbo].[QT]([GID_RELATED_BU] ASC, [MLS_Status] ASC)
    INCLUDE([CUR_TOTALAMOUNT], [SI__GROSSMARGIN]);


GO
CREATE TRIGGER trQTUpdateTN
ON [QT]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in QT table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(120)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'QT'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable
					and tn2.GID_InternalID = inserted.GID_ID)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [QT]
			SET [QT].TXT_ExternalSource = '', [QT].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [QT].GID_ID = in1.GID_ID
				and ISNULL([QT].TXT_ExternalSource, '') <> ''
				and ISNULL([QT].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trQTUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trQTUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trQTUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!