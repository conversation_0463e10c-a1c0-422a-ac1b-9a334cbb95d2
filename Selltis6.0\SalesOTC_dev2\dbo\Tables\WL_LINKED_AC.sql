﻿CREATE TABLE [dbo].[WL_LINKED_AC] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_WL_LINKED_AC_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_WL] UNIQUEIDENTIFIER NOT NULL,
    [GID_AC] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_WL_LINKED_AC] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_AC_CONNECTED_WL] FOREIGN KEY ([GID_WL]) REFERENCES [dbo].[WL] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_WL_LINKED_AC] FOREIGN KEY ([GID_AC]) REFERENCES [dbo].[AC] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[WL_LINKED_AC] NOCHECK CONSTRAINT [LNK_AC_CONNECTED_WL];


GO
ALTER TABLE [dbo].[WL_LINKED_AC] NOCHECK CONSTRAINT [LNK_WL_LINKED_AC];


GO
CREATE NONCLUSTERED INDEX [IX_WL_LINKED_AC]
    ON [dbo].[WL_LINKED_AC]([GID_WL] ASC, [GID_AC] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_AC_CONNECTED_WL]
    ON [dbo].[WL_LINKED_AC]([GID_AC] ASC, [GID_WL] ASC);

