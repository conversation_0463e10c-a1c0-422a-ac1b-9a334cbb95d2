﻿CREATE TABLE [dbo].[RE_Related_GR] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Report_Related_Group_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_RE] UNIQUEIDENTIFIER NOT NULL,
    [GID_GR] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_RE_Related_GR] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_GR_Related_RE] FOREIGN KEY ([GID_RE]) REFERENCES [dbo].[RE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_RE_Related_GR] FOREIGN KEY ([GID_GR]) REFERENCES [dbo].[GR] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[RE_Related_GR] NOCHECK CONSTRAINT [LNK_GR_Related_RE];


GO
ALTER TABLE [dbo].[RE_Related_GR] NOCHECK CONSTRAINT [LNK_RE_Related_GR];


GO
CREATE CLUSTERED INDEX [IX_GR_Related_RE]
    ON [dbo].[RE_Related_GR]([GID_RE] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RE_Related_GE]
    ON [dbo].[RE_Related_GR]([GID_GR] ASC);

