﻿CREATE TABLE [dbo].[XP] (
    [GID_ID]           UNIQUEIDENTIFIER CONSTRAINT [DF_XP_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'XP',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]           BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_Name]         NVARCHAR (80)    NULL,
    [DTT_CreationTime] DATETIME         CONSTRAINT [DF_XP_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [GID_Section]      UNIQUEIDENTIFIER NULL,
    [TXT_Page]         VARCHAR (40)     NOT NULL,
    [TXT_Property]     VARCHAR (50)     NOT NULL,
    [TXT_Value]        NVARCHAR (3800)  NULL,
    [TXT_ModBy]        VARCHAR (4)      NULL,
    [DTT_ModTime]      DATETIME         CONSTRAINT [DF_XP_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [SI__ShareState]   TINYINT          CONSTRAINT [DF_XP_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US] UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_XP] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC)
);


GO
CREATE UNIQUE CLUSTERED INDEX [IX_XP_SectionPageProp]
    ON [dbo].[XP]([GID_Section] ASC, [TXT_Page] ASC, [TXT_Property] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XP_ModDateTime]
    ON [dbo].[XP]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XP_PageProp]
    ON [dbo].[XP]([TXT_Page] ASC, [TXT_Property] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_XP_BI__ID]
    ON [dbo].[XP]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XP_CreatedBy_US]
    ON [dbo].[XP]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XP_CreationTime]
    ON [dbo].[XP]([DTT_CreationTime] ASC);

