'OWNER: RH

Imports Microsoft.VisualBasic


Public Class clVar

    'MI 8/21/06 Changed Collection object to SortedList to support DeleteVarsByName.
    'MI 8/21/06 Added DeleteVarsByName

    'OWNER: MI
    'PURPOSE:
    '       Allow storing "global variables" accessible within an object of appropriate scope. For example,
    '       clProject inherits from clVar, allowing us to persist on the goP. level
    '       as many "variables" as necessary.
    'IMPLEMENTATION NOTE:
    '       "Variables" are stored as elements of a sorted list that contains key/value pairs.
    '       SortedList is a .net object that is automatically sorted by key. We chose this object
    '       to be able to find elements by index, partial key, and partial value. Collection doesn't
    '       allow finding the key by index or value.
    '       IMPORTANT: Because the sorting is performed automatically, index values change on
    '       each insertion and deletion.

    'Private cCollection As New Collection      'old object

    Private slCollection As New SortedList

    Public Sub New()

    End Sub

    Public Sub DeleteAllVar()

        'PURPOSE:
        '		Delete ALL items in the collection
        'PARAMETERS:
        '		N/A
        'RETURNS:
        '		Nothing
        'HOW IT WORKS:
        '		Clears the collection
        'EXAMPLE:
        '		oForm.DeleteAllVar()

        slCollection.Clear()

    End Sub

    Public Function DeleteVar(ByVal par_sVarName As String) As Boolean
        'PURPOSE:
        '		Delete the value and name corresponding to a var name
        'PARAMETERS:
        '		par_sVarName:	Name of the var we want to delete
        'RETURNS:
        '		True if job done, false if not (ie the var was not found)
        'EXAMPLE:
        '		if not oForm.DeleteVar("MyVar") then

        par_sVarName = par_sVarName.ToUpper

        If slCollection.Contains(par_sVarName) Then
            slCollection.Remove(par_sVarName)
            Return True
        Else
            Return False
        End If
    End Function
    Public Function DeleteVarsByName(ByVal par_sStartsWith As String) As Boolean
        'AUTHOR: MI 8/21/06
        'PURPOSE:
        '		Delete items whose var name starts with par_sStartsWith.
        '       The search is case insensitive.
        'PARAMETERS:
        '		par_sStartsWith: String with which the var name starts.
        '           CAUTION: If this is a blank string, no vars are deleted and
        '           False is returned. To delete all vars, use DeleteAllVar().
        'RETURNS:
        '		True if successful, false if no vars were found. False if
        '           par_sStartsWith is blank.
        'EXAMPLE:
        '		if not goP.DeleteVars("diaMetaBrowser") then

        Dim sStartsWith As String = UCase(par_sStartsWith)
        Dim i As Integer = 0
        Dim bResult As Boolean = False
        Dim iLen As Integer = Len(sStartsWith)

        If iLen < 1 Then
            'Search string is blank, return False and don't delete any vars
            Return False
        End If

        '0-based index
        Do
            If i > slCollection.Count - 1 Then Exit Do
            If Left(UCase(slCollection.GetKey(i).ToString), iLen) = sStartsWith Then
                slCollection.RemoveAt(i)
                i = i - 1
                bResult = True
            End If
            i = i + 1
        Loop

        Return bResult

    End Function

    Public Function GetVar(ByVal par_sVarName As String) As Object

        'PURPOSE:
        '		Get the value corresponding to a var name
        'PARAMETERS:
        '		par_sVarName:	Name of the var we want to get
        'RETURNS:
        '		The value found, or an empty string, if not found
        'HOW IT WORKS:
        '		Pulls the value out of the storage collection
        'EXAMPLE:
        '		oForm.GetVar("MyVar")

        par_sVarName = par_sVarName.ToUpper

        If slCollection.Contains(par_sVarName) Then
            Return slCollection.Item(par_sVarName)
        Else
            Return ""
        End If

    End Function
    Public Function SetVar(ByVal par_sVar As String, ByVal par_vVal As Object) As Boolean

        'PURPOSE:
        '		Add or modify a variable value in the arrays
        'PARAMETERS:
        '		par_sVar:		Name of the variable
        '		par_vVal:		Value to store
        'RETURNS:
        '		True if job done, false if the name of the variable was empty
        'HOW IT WORKS:
        '		Look in the array if the variable already exist. If true, modify the value, if false, add a line in the array or 
        '		use the first available space, if any
        'EXAMPLE:
        '		oForm:SetVar("MyVar", 23)	//- we use oForm because clVar is generally not used directly, but several classes 
        '									//inherit it
        '	

        If par_sVar = "" Then Return False

        Try
            par_sVar = par_sVar.ToUpper

            If slCollection.Contains(par_sVar) Then
                slCollection.Remove(par_sVar)
            End If

            slCollection.Add(par_sVar, par_vVal)

            Return True
        Catch ex As Exception
            Return False
        End Try

    End Function

End Class
