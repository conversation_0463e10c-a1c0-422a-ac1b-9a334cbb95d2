﻿Imports System.Web
Imports System.IO
Imports System.Configuration
Imports System.Text.RegularExpressions
Imports System.Drawing

Public NotInheritable Class clSettings

    Public Shared Function LoadSiteSettings() As String

        Dim sHostName As String = GetHostName()

        'Try

        'Dim myXMLfile As String = HttpContext.Current.Server.MapPath((Convert.ToString("~/App_Data/SiteSettings/") & sHostName) + "_SiteSettings.xml")
        Dim myXMLfile As String = System.Configuration.ConfigurationManager.AppSettings("CustomFilesPath").ToString() & sHostName & "/SiteSettings.xml"

        'To differentiate local and azure..J
        Dim substring As String = myXMLfile.Substring(1, 2)
        If substring = ":\" Then
            myXMLfile = System.Configuration.ConfigurationManager.AppSettings("CustomFilesPath").ToString() & sHostName & "/SiteSettings.xml"
        Else
            myXMLfile = HttpContext.Current.Server.MapPath(System.Configuration.ConfigurationManager.AppSettings("CustomFilesPath").ToString()) & sHostName & "\SiteSettings.xml"
        End If


        HttpContext.Current.Session(sHostName + "_SiteSettings") = Nothing

        'If Not String.IsNullOrEmpty(myXMLfile) Then

        If String.IsNullOrEmpty(myXMLfile) Then
            Return myXMLfile + "Site settings file for the site " + System.Web.HttpContext.Current.Request.Url.Host + " is not found"
        End If

        If File.Exists(myXMLfile) = False Then

            Return myXMLfile + "Site settings file for the site " + sHostName + " is not found"
        End If

        Dim ds As New DataSet()
        Dim fsReadXml = New FileStream(myXMLfile, FileMode.Open, FileAccess.Read)
        ds.ReadXml(fsReadXml)
        fsReadXml.Close()
        fsReadXml.Dispose()

        If ds IsNot Nothing AndAlso ds.Tables(0) IsNot Nothing Then

            Dim sError As String = String.Empty

            sError = ValidateConfiguration("SiteId", ds)
            If sError <> String.Empty Then
                Return sError
            End If

            sError = ValidateConfiguration("ConnectionString", ds)
            If sError <> String.Empty Then
                Return sError
            End If

            'sError = ValidateConfiguration("Logo", ds)
            'If sError <> String.Empty Then
            '    Return sError
            'End If

            sError = ValidateConfiguration("AttachmentsPath", ds)
            If sError <> String.Empty Then
                Return sError
            End If

            sError = ValidateConfiguration("AttachmentsTempPath", ds)
            If sError <> String.Empty Then
                Return sError
            End If

            sError = ValidateConfiguration("AttachmentsMaxFolderSize", ds)
            If sError <> String.Empty Then
                Return sError
            End If

            sError = ValidateConfiguration("AttachmentMaxFileSize", ds)
            If sError <> String.Empty Then
                Return sError
            End If

            sError = ValidateConfiguration("UseCache", ds)
            If sError <> String.Empty Then
                Return sError
            End If

            sError = ValidateConfiguration("CustomDLLName", ds)
            If sError <> String.Empty Then
                'Return sError
                HttpContext.Current.Session("CUSTOMDLLEXISTED") = False
            End If

            HttpContext.Current.Session(sHostName + "_SiteSettings") = ds.Tables(0)

            Return String.Empty

        Else
            Return myXMLfile + "Site settings for the site " + System.Web.HttpContext.Current.Request.Url.Host + " is not found"
        End If
        'Catch ex As Exception
        '    HttpContext.Current.Session(sHostName + "_SiteSettings") = Nothing
        '    Return ex.Message
        'End Try
        'Else
        'Return False
        'End If
    End Function

    Private Shared Function ValidateConfiguration(sProperty As String, ds As DataSet) As String

        If ds.Tables(0).Columns.Contains(sProperty) = False Then
            Return "Site settings for the site '" + System.Web.HttpContext.Current.Request.Url.Host + "' is not configured correctly; Error:'" & sProperty & "' is missed in the configuration"
        End If

        If ds.Tables(0).Rows(0)(sProperty) Is Nothing Then
            Return "Site settings for the site '" + System.Web.HttpContext.Current.Request.Url.Host + "' is not configured correctly; Error:'" & sProperty & "' is missed in the configuration"
        End If

        If String.IsNullOrEmpty(ds.Tables(0).Rows(0)(sProperty).ToString()) Then
            Return "Site settings for the site '" + System.Web.HttpContext.Current.Request.Url.Host + "' is not configured correctly; Error:'" & sProperty & "' is missed in the configuration"
        End If

        Return String.Empty

    End Function

    Public Shared Function GetHostName() As String
        Dim sHostingEnvironment As String = ConfigurationManager.AppSettings("HostingEnvironment").ToString().ToLower()
        Dim sHostName As String = ""
        If sHostingEnvironment = "debugging" Then
            sHostName = "default"
        ElseIf sHostingEnvironment = "staging" Then
            sHostName = HttpContext.Current.Request.Url.Host.ToString().ToLower() + "_" + HttpContext.Current.Request.Url.Port.ToString()
        Else

            Dim host = Nothing
            If HttpContext.Current.Session("HostName") = Nothing Then
                host = HttpContext.Current.Request.Url.Host.ToString().ToLower()
            Else
                host = HttpContext.Current.Session("HostName").ToString()
            End If


            Return host
        End If
        Return sHostName
    End Function

    Public Shared Function GetSiteId() As String
        Dim sHostingEnvironment As String = ConfigurationManager.AppSettings("HostingEnvironment").ToString().ToLower()
        Dim sSiteId = DirectCast(GetSessionValue("SiteSettings"), DataTable).Rows(0)("SiteId").ToString() '((DataTable)Util.GetSessionValue("SiteSettings")).Rows[0]["SiteId"].ToString()

        Return sSiteId
    End Function

    Public Shared Function GetUseSSO() As String
        Dim sHostingEnvironment As String = ConfigurationManager.AppSettings("HostingEnvironment").ToString().ToLower()
        Dim sUseSSO As String = ""
        Try
            sUseSSO = Convert.ToString(DirectCast(GetSessionValue("SiteSettings"), DataTable).Rows(0)("UseSSO"))
        Catch ex As Exception
            sUseSSO = "false"
        End Try
        Return sUseSSO
    End Function

    Private Shared Function GetHostPrefix() As String
        Dim HostPrefix As String = ""

        Dim HostName As String = ""
        HostName = HttpContext.Current.Request.Url.Host

        HostPrefix = HostName.Replace(".selltis.com", "")
        Return HostPrefix
    End Function

    Public Shared Function GetCustomDLL_FilePath() As String
        Try
            Dim sCustomDLLFilePath As String = ""
            Dim sCustomFilesPath As String = ConfigurationManager.AppSettings("CustomFilesPath").ToString()
            Dim sHostName As String = GetHostName()

            'To differentiate local and azure..J
            Dim substring As String = sCustomFilesPath.Substring(1, 2)
            If substring = ":\" Then
                sCustomFilesPath = System.Configuration.ConfigurationManager.AppSettings("CustomFilesPath").ToString()
            Else
                sCustomFilesPath = HttpContext.Current.Server.MapPath(System.Configuration.ConfigurationManager.AppSettings("CustomFilesPath").ToString())
            End If

            Dim sCusDLLName As String = DirectCast(GetSessionValue("SiteSettings"), DataTable).Rows(0)("CustomDLLName").ToString()

            Dim sHostingEnvironment As String = ConfigurationManager.AppSettings("HostingEnvironment").ToString().ToLower()
            If sHostingEnvironment = "debugging" Then
                Dim path = System.IO.Path.Combine(System.AppDomain.CurrentDomain.BaseDirectory, "bin")
                sCustomDLLFilePath = System.IO.Path.Combine(path, sCusDLLName)
            Else
                sCustomDLLFilePath = Convert.ToString((sCustomFilesPath & sHostName) + "\") & sCusDLLName
            End If

            Return sCustomDLLFilePath
        Catch generatedExceptionName As Exception
            Return ""
        End Try

    End Function


    Public Shared Function GetSessionValue(sKeyName As String) As Object
        sKeyName = MakeSessionKey(sKeyName)
        Return HttpContext.Current.Session(sKeyName)
    End Function

    Public Shared Function MakeSessionKey(sKey As String) As String
        Return Convert.ToString(GetHostName() + "_") & sKey
    End Function

    Public Shared Function ConvertBase64Images(ByRef strData As String, Optional UserId As String = "") As String

        Dim Ht As New ArrayList() 'keeps the physical image file names
        Dim Ht1 As New ArrayList() 'keeps the base64 Image string to check for the same images
        Dim DHT As Dictionary(Of Integer, String) = New Dictionary(Of Integer, String)

        'MMR Change -- Replace the Base64 images with local image path
        Dim regexImgSrc As String = "<img[^>]*?src\s*=\s*[""']?([^'"" >]+?)[ '""][^>]*?>"
        Dim matchesImgSrc As MatchCollection = Regex.Matches(strData, regexImgSrc, RegexOptions.IgnoreCase Or RegexOptions.Singleline)

        Dim indx As Integer = 0

        For indx = 0 To matchesImgSrc.Count - 1

            Dim imgstrs As String = matchesImgSrc.Item(indx).Value

            Dim _sPath As String = System.Web.HttpContext.Current.Server.MapPath("../Temp") 'Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)
            _sPath = _sPath & "\" & clSettings.GetHostName() & "\SignatureImages\" & UserId & "\"

            'Dim _sPath = "/PublicFiles/" + clSettings.GetSiteId() + "/OutLookImages/"

            Dim exists As Boolean = System.IO.Directory.Exists(_sPath)
            If Not exists Then
                System.IO.Directory.CreateDirectory(_sPath)
            End If

            Dim iwidth, iheight As Integer
            iwidth = 0
            iheight = 0

            'Get the Image size section start
            Dim reHeight As New Regex("height:\s\d*", RegexOptions.IgnoreCase Or RegexOptions.Singleline)
            Dim reWidth As New Regex("width:\s\d*", RegexOptions.IgnoreCase Or RegexOptions.Singleline)

            Dim mWidth As Match = reWidth.Match(imgstrs)
            If mWidth.Length > 8 And mWidth.Value.Contains(":") And mWidth.Value.Contains("width") Then
                Integer.TryParse(mWidth.Value.Split(":").GetValue(1).ToString().Trim(), iwidth)
            End If

            Dim mHeight As Match = reHeight.Match(imgstrs)
            If mHeight.Length > 8 And mHeight.Value.Contains(":") And mHeight.Value.Contains("height") Then
                Integer.TryParse(mHeight.Value.Split(":").GetValue(1).ToString().Trim(), iheight)
            End If

            'Get the images size section end
            Dim _imageName As String = "SignatureImage" & GetTimeString() & indx.ToString() & ".jpg"
            _sPath = _sPath & _imageName
            'Dim PBase64Imagestr As String = Regex.Match(imgstrs, "<img.+?src=[""'](.+?)[""'].+?>", RegexOptions.IgnoreCase).Groups(1).Value

            '29082018 tickt #2401: Images are not showed in outlook email for the fist time after AC logged from outlook..J
            Dim PBase64Imagestr As String = Regex.Match(imgstrs, "src\s*=\s*[""'](.+?)[""'].+?", RegexOptions.IgnoreCase).Groups(1).Value

            If PBase64Imagestr.Split(",").Length > 1 Then
                Dim Base64Imagestr As String = PBase64Imagestr.Split(",")(1)
                'Dim Base64ImagestrEncoded = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(Base64Imagestr)) 'Convert.FromBase64String(Base64Imagestr)
                'Dim bytes = Convert.FromBase64String(Base64ImagestrEncoded)
                Dim b64 As String = Base64Imagestr.Replace(" ", "+")
                Dim bytes = Convert.FromBase64String(b64)
                Using imageFile = New FileStream(_sPath, FileMode.Create)
                    imageFile.Write(bytes, 0, bytes.Length)
                    imageFile.Flush()
                End Using

                'Resize Image if style specified in the image tag
                If iwidth > 0 AndAlso iheight > 0 Then
                    Dim _Timg As Image = Image.FromFile(_sPath)
                    Dim myCallback As Image.GetThumbnailImageAbort = New Image.GetThumbnailImageAbort(AddressOf ThumbnailCallback)
                    Dim _ImgToSave As Image = _Timg.GetThumbnailImage(iwidth, iheight, myCallback, IntPtr.Zero)
                    _Timg.Dispose()
                    If File.Exists(_sPath) Then
                        File.Delete(_sPath)
                    End If
                    _ImgToSave.Save(_sPath, System.Drawing.Imaging.ImageFormat.Jpeg)
                End If
                'Resize done
                Dim objmmr_letterAttach As Dictionary(Of String, String) = New Dictionary(Of String, String) 'Attach mmr_Later Image
                objmmr_letterAttach.Add(_imageName, _sPath)

                'checking for the same image 
                If Ht1.Contains(imgstrs) Then
                    Dim pair As KeyValuePair(Of Integer, String)
                    Dim indval As Integer = 0
                    For Each pair In DHT
                        If pair.Value = imgstrs Then
                            indval = pair.Key
                            GoTo end_of_for
                        End If
                    Next
end_of_for:
                    Ht.Add(Ht(indval))
                Else
                    Ht.Add(_imageName)
                    Ht1.Add(imgstrs)
                    DHT.Add(indx, imgstrs)
                End If

                Dim baseURL As String = HttpContext.Current.Request.Url.Scheme + "://" + HttpContext.Current.Request.Url.Authority
                _sPath = baseURL & "/Temp/" & clSettings.GetHostName() & "/SignatureImages/" & UserId & "/" & _imageName
                _imageName = "<img src='" & _sPath & "' />"

                strData = strData.ToString().Replace(imgstrs, _imageName)
            End If
        Next

        Return ""

    End Function

    Public Shared Function GetTimeString() As String

        Dim sTime As DateTime = Now
        'GetLocalTime(sTime)

        GetTimeString = sTime.Year & PadLeadingZeros(sTime.Month, 2) & PadLeadingZeros(sTime.Day, 2) & PadLeadingZeros(sTime.Hour, 2) & PadLeadingZeros(sTime.Minute, 2) & PadLeadingZeros(sTime.Second, 2) & Left(PadLeadingZeros(sTime.Millisecond, 3), 2)

    End Function

    Public Shared Function ThumbnailCallback() As Boolean
        Return False
    End Function

    Public Shared Function PadLeadingZeros(ByVal strForm As String, ByVal intLen As Integer) As String

        If Len(strForm) < intLen Then
            Do While Len(strForm) <> intLen
                strForm = "0" & strForm
            Loop
            PadLeadingZeros = strForm
        Else
            PadLeadingZeros = strForm
        End If

    End Function
End Class
