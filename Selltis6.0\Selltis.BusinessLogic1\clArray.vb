'Owner: RH

Imports Microsoft.VisualBasic
Imports System
Imports System.Web
Imports System.Data


Public Class clArray

    Inherits clInfoMessage

    Dim goP As clProject
    Dim goMeta As clMetaData
    Dim goTR As clTransform
    Dim goData As clData
    Dim goErr As clError



    Public Sub New()

        'goP = HttpContext.Current.Session("goP")
        'goTR = HttpContext.Current.Session("goTr")
        'goMeta = HttpContext.Current.Session("goMeta")
        'goData = HttpContext.Current.Session("goData")
        'goErr = HttpContext.Current.Session("goErr")

    End Sub

    Public Sub Add(ByVal par_sString As String)

        AddInfo(par_sString)


    End Sub

    Public Function Clone(ByRef par_doArray As clArray) As Boolean
        '//PURPOSE:
        '//		Copy all elements of the internal array into another clArray object
        '//PARAMETERS:
        '//		par_doArray:	clArray object to copy into
        '//RETURNS:
        '//		True if done, false if problem (target doesn't exist), with a seterror
        '//HOW IT WORKS:
        '//		loop on internal array and add the info in the target object
        '//EXAMPLE:
        '//		if not doArray.Clone(doArray2) then

        Try

            Dim i As Long

            For i = 1 To cArrayCollection.Count
                par_doArray.AddInfo(cArrayCollection(i))
            Next

            Return True

        Catch ex As Exception

            Return False

        End Try



    End Function

    Public Function GetItem(ByVal par_lItem As Long) As String

        If par_lItem < 1 Then
            Return ""
        End If
        If par_lItem > cArrayCollection.Count Then
            Return ""
        End If
        Return cArrayCollection.Item(par_lItem)

    End Function

    Public Function Modify(ByVal par_lItem As Long, ByVal par_sNewValue As String) As Boolean

        If ModInfo(par_lItem, par_sNewValue) = True Then
            Return True
        Else
            Return False
        End If

    End Function

    Public Function Seek(ByVal par_sInfo As String, Optional ByVal par_bMode As Boolean = False) As Long


        '//PURPOSE:
        '//		Seek a string in the array and return the index if found.
        '//PARAMETERS:
        '//		par_sInfo:	String to search in the array
        '//		par_bMode:	if False, returns the index if the beginning of the string in the array=the string to test
        '//					if true, returns the index only if both string are EXACTLY the same
        '//RETURNS:
        '//		The index in the array where the string was found (method depends of par_bMode), 0 if not found
        '//HOW IT WORKS:
        '//		Loop on the array and compare
        '//EXAMPLE:
        '//		lI=goIM:SeeekInfo(sString)

        Return SeekInfo(par_sInfo, par_bMode)

    End Function

    Public Function StringToArray(ByVal par_sString As String) As Boolean


        Dim sVals(0) As String
        Dim i As Integer

        If InStr(par_sString, vbCrLf) > 0 Then
            sVals = Split(par_sString, vbCrLf)
            For i = sVals.GetLowerBound(0) To sVals.GetUpperBound(0)
                Me.AddInfo(sVals(i))
            Next
        Else
            Me.AddInfo(par_sString)
        End If

        Return True

    End Function


End Class
