﻿CREATE TABLE [dbo].[PS_Related_CN] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_PS_Related_CN_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PS] UNIQUEIDENTIFIER NOT NULL,
    [GID_CN] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PS_Related_CN] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CN_Connected_PS] FOREIGN KEY ([GID_PS]) REFERENCES [dbo].[PS] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PS_Related_CN] FOREIGN KEY ([GID_CN]) REFERENCES [dbo].[CN] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PS_Related_CN] NOCHECK CONSTRAINT [LNK_CN_Connected_PS];


GO
ALTER TABLE [dbo].[PS_Related_CN] NOCHECK CONSTRAINT [LNK_PS_Related_CN];


GO
CREATE NONCLUSTERED INDEX [IX_CN_Connected_PS]
    ON [dbo].[PS_Related_CN]([GID_CN] ASC, [GID_PS] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PS_Related_CN]
    ON [dbo].[PS_Related_CN]([GID_PS] ASC, [GID_CN] ASC);

