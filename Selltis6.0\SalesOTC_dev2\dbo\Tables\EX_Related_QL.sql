﻿CREATE TABLE [dbo].[EX_Related_QL] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Expense_Related_QuotLine_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_EX] UNIQUEIDENTIFIER NOT NULL,
    [GID_QL] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_EX_Related_QL] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_EX_Related_QL] FOREIGN KEY ([GID_QL]) REFERENCES [dbo].[QL] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QL_Connected_EX] FOREIGN KEY ([GID_EX]) REFERENCES [dbo].[EX] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[EX_Related_QL] NOCHECK CONSTRAINT [LNK_EX_Related_QL];


GO
ALTER TABLE [dbo].[EX_Related_QL] NOCHECK CONSTRAINT [LNK_QL_Connected_EX];


GO
CREATE CLUSTERED INDEX [IX_QL_Connected_EX]
    ON [dbo].[EX_Related_QL]([GID_EX] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_Related_QL]
    ON [dbo].[EX_Related_QL]([GID_QL] ASC);

