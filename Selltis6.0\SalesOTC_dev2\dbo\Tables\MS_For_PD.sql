﻿CREATE TABLE [dbo].[MS_For_PD] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Message_For_Product_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_MS] UNIQUEIDENTIFIER NOT NULL,
    [GID_PD] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MS_For_PD] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_MS_For_PD] FOREIGN KEY ([GID_PD]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PD_Connected_MS] FOREIGN KEY ([GID_MS]) REFERENCES [dbo].[MS] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MS_For_PD] NOCHECK CONSTRAINT [LNK_MS_For_PD];


GO
ALTER TABLE [dbo].[MS_For_PD] NOCHECK CONSTRAINT [LNK_PD_Connected_MS];


GO
CREATE CLUSTERED INDEX [IX_PD_Connected_MS]
    ON [dbo].[MS_For_PD]([GID_MS] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MS_For_PD]
    ON [dbo].[MS_For_PD]([GID_PD] ASC);

