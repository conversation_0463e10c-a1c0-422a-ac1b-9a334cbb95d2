﻿@model Selltis.MVC.Models.OutsideSales

@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<head>


    <link href="~/Content/assets/js/jquery-ui/css/no-theme/jquery-ui-1.10.3.custom.css" rel="stylesheet" />
    <link rel="stylesheet" href="~/Content/assets/css/font-icons/entypo/css/entypo.css">

    <link rel="stylesheet" href="~/Content/assets/js/select2/select2-bootstrap.css">
    <link rel="stylesheet" href="~/Content/assets/js/select2/select2.css">
    <link rel="stylesheet" href="~/Content/assets/js/datatables/datatables.css">
    <link rel="stylesheet" href="~/Content/assets/js/zurb-responsive-tables/responsive-tables.css">
    <link rel="stylesheet" href="~/Content/assets/js/vertical-timeline/css/component.css">

    <link rel="stylesheet" href="~/Content/assets/css/custom.css">

    <script src="~/Content/assets/js/gsap/TweenMax.min.js"></script>


</head>
<style>

    .mt-20 {
        color: black;
    }

    .table-bordered > thead > tr > th, .table-bordered > tbody > tr > th, .table-bordered > tfoot > tr > th, .table-bordered > thead > tr > td, .table-bordered > tbody > tr > td, .table-bordered > tfoot > tr > td {
        border: 0px solid #ebebeb !important
    }

    .table-bordered > thead > tr > th {
        font-weight: 400
    }

    .table-bordered > tfoot > tr > td, .table-bordered > tfoot > tr > th, .table-bordered > thead > tr > td, .table-bordered > thead > tr > th {
        background-color: #ecf1f7;
        color: #a6a7aa;
    }

    .page-content-container {
        padding: 5px 15px 15px 15px;
        border-radius: 3px;
        background-color: white
    }

    h4 {
        font-size: 16px;
        color: #00366c;
        font-family: inherit;
        font-weight: 500;
    }

    table {
        border-collapse: collapse;
        border-spacing: 0
    }

    a {
        color: #373e4a;
    }

    .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
        position: relative;
        min-height: 1px;
        padding-left: 15px;
        padding-right: 15px
    }

    .table thead th, .table tbody td {
        text-align: right;
    }

        .table thead th:first-child, .table tbody td:first-child {
            text-align: left;
        }

    .mb-10 {
        margin-bottom: 10px !important;
        margin-top: 10px !important;
    }

    .table > thead > tr > th {
        padding: 8px;
        line-height: 1.********;
    }

    .text-left-i-quotes div, .text-left-i-contacts div, .text-left-i-opps div, .text-left-i-Accounts div, .text-left-i-activities div, .text-left-i-activitiesSV div, .text-left-i-Tasks div {
        width: auto;
        overflow: hidden;
        white-space: normal;
        text-overflow: ellipsis;
    }

    .Main-name {
        font-size: 12px !important;
        color: #373E4A;
    }

    .Sub-name {
        font-family: 'Open Sans', Helvetica, Arial, sans-serif !important;
        font-size: 10px;
        color: #949494;
    }

    .hide-row {
        display: none;
    }

    .table-link {
        color: #a6a7aa;
        font-size: 10px !important;
        margin-right: 15px;
        display: inline-flex;
        align-items: center;
    }


        .table-link i {
            font-size: 8px;
            margin-left: 5px;
        }

    .icon {
        font-size: 60px;
        font-weight: bold;
        line-height: 100%;
        color: #ECF1F7;
        font-style: normal;
    }

    .heading {
        color: white;
    }



    /*Page content css*/
    .social-icons {
        font-size: 4rem;
    }

        .social-icons li {
            display: inline;
            float: left;
        }

            .social-icons li i:before {
                margin: 0px;
            }

    .profile-name aside.user-thumb img {
        max-width: 70px;
    }

    .contacts-all-btn-icon {
        font-size: 4rem;
    }




    .text-decoration-none {
        text-decoration: none !important;
    }

    .profile-name {
        color: #fff;
    }

    .tile-stats {
        position: relative;
        display: block;
        background: #303641;
        padding: 20px;
        margin-bottom: 12px;
        overflow: hidden;
        -webkit-border-radius: 5px;
        -webkit-background-clip: padding-box;
        -moz-border-radius: 5px;
        -moz-background-clip: padding;
        border-radius: 5px;
        background-clip: padding-box;
        -webkit-transition: all 300ms ease-in-out;
        -moz-transition: all 300ms ease-in-out;
        -o-transition: all 300ms ease-in-out;
        transition: all 300ms ease-in-out;
    }

    .company-details label, .company-details p {
        font-size: 12px;
    }

    .company-details label {
        color: #00366c;
        margin-bottom: 0px;
    }



    .timeline {
        border: 1px solid #f5f5f6;
        border-radius: 3px;
        padding: 15px
    }

    ul.cbp_tmtimeline > li .cbp_tmlabel, ul.cbp_tmtimeline > li .cbp_tmlabel.empty {
        margin-bottom: 15px !important
    }

    .cbp_tmtimeline > li .cbp_tmlabel {
        background: #F5F5F7;
        padding: 1.2rem 1.6rem
    }

    .company-details label {
        color: #00366c;
        margin-bottom: 0px
    }

    .company-details label, .company-details p {
        font-size: 12px
    }

    .border-light-gray {
        border: 1px solid #f5f5f6;
    }

    .txt-light-gray {
        color: #949494;
    }

    .edit-btn {
        font-size: 12px;
    }

    .ml-10 {
        margin-left: 10px;
    }

    .company-details label, .company-details p {
        font-size: 12px;
    }

    .company-details label {
        color: #00366c;
        margin-bottom: 0px;
    }

    label {
        display: inline-block;
        max-width: 100%;
        margin-bottom: 5px;
        font-weight: bold;
    }

    .cbp_tmtimeline:before {
        background: #f5f5f6;
        width: 5px;
        margin-left: -6px;
    }


    #content.table-layout > div, #content.table-layout > section {
        vertical-align: top;
        padding: 0px 0px 0px !important;
    }

    th a:hover {
        color: #3498db !important;
    }

    .responsive-container {
        overflow-x: auto;
        display: block;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        margin: 0 auto;
    }

    .table {
        width: 100%;
        table-layout: auto;
        border-collapse: collapse;
    }

        .table th, .table td {
            white-space: nowrap;
            text-align: left;
            vertical-align: middle;
        }

            .table th a, .table td a {
                display: inline-block;
            }


    .responsive-container {
        padding: 0 10px;
    }

    .header-row .custom-header-buttons {
        display: flex;
        flex-direction: column;
    }

    .d-flex {
        display: flex;
    }

    .flex-wrap {
        flex-wrap: wrap;
    }

    .justify-content-between {
        justify-content: space-between;
    }

    .align-items-center {
        align-items: center;
    }

    .gap-1 a {
        margin-right: 10px;
        text-decoration: none;
    }

        .gap-1 a:last-child {
            margin-right: 0;
        }

    footer.main {
        display: none;
    }

    @@media (min-width: 768px) {
        .header-row .custom-header-buttons {
            flex-direction: row;
            align-items: center;
        }

        .gap-1 {
            margin-left: auto;
        }
    }

    .custom-header-buttons .d-flex a {
        text-decoration: none;
        color: inherit;
        font-weight: 500;
    }

        .custom-header-buttons .d-flex a:hover {
            text-decoration: underline;
            color: #007bff;
        }

    @@media (max-width: 768px) {
        .custom-header-buttons .d-flex {
            flex-direction: column;
        }
    }

    .table-container {
        display: block; /* Block display for both tables */
        margin-top: 20px;
    }

    /* Ensure both tables take up the full width */
    .table {
        width: 100%;
        border-collapse: collapse;
        table-layout: fixed; /* Ensures equal width for each column */
    }

        .table th, .table td {
            padding: 10px;
            text-align: left;
            word-wrap: break-word; /* Prevents overflow in case of long text */
        }

        /* Apply background to header for both tables */
        .table th {
            background-color: #f1f1f1;
        }
</style>


<!-- TOP FIXED NAVIGATION -->
<section id="content_wrapper" style="height:calc(100%)">
    <!-- Begin: Content -->
    <section id="content" style="height:100%" class=" table-layout animated fadein">

        <div class="page-container">




            <div class="main-content" style="background-color:#003665;padding:12px;height:auto">
                <div class="d-flex justify-content-between align-items-center" style="position: relative; width: 100%; color: white;">
                    @*<div class="k-d-flex k-justify-content-left" style="padding-top: 4px;width:40%;padding-bottom:6px">*@
                    <div class="company-title ml-10 mb-10" style="display: flex; align-items: center;">
                        <span style="font-size: 18px; margin-right: 10px;">Segment Dashboard </span>
                        <div id="dropdownTree" style="width: 300px; margin-right: 20px;">
                            @(Html.Kendo().DropDownTree()
                             .Name("SalesPersonHierarchy")
                             .DataTextField("Name")
                             .DataValueField("id")
                             .Height("400px")
                             .Filter("startswith")
                             .MinLength(3)
                             .HtmlAttributes(new { style = "width: 90%" })
                             .DataSource(dataSource => dataSource
                                 .Read(read => read.Action("GetSegmentLeaderHierarchy", "DetailsPage"))
                             )
                             .Events(e => e.Change("onChange"))
                         )
                            @*.*@

                        </div>
                        <div id="breadcrumb" style="font-size: 16px; display: flex; align-items: center;">
                            <ul class="breadcrumb">
                            </ul>
                        </div>
                    </div>



                    <nav aria-label="breadcrumb">

                    </nav>


                    <div>
                        @*<span style="font-size: 18px;">Segment Dashboard:</span>*@
                    </div>

                </div>

                <div class="page-content-container">
                    <!--tile's-->
                    <!--Body-->
                    <div class="row mt-20">
                        <div class="col-lg-4 col-xs-12">
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0">Segment Accounts</h4>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_70E3F26B-791B-432F-5858-B2550180804A','','','')" class="primary-account-link"> Primary Accounts</a>

                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_AE585C2C-C193-441E-5858-B27E01165AD7','','','')">Overdue Primary Accounts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_AE585C2C-C193-441E-5858-B27E01165AD7','','','')"><p style="font-size:105%" id="overDueprimaryAccountsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_E4A451FC-9B8A-4466-5858-B2790143BC01','','','')">Primary Accounts Without Contacts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_E4A451FC-9B8A-4466-5858-B2790143BC01','','','')"><p style="font-size: 105% " id="accountsWithoutContacts"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_9DA8DAD7-9059-475A-5858-B27901440776','','','')">Primary Accounts Without Activities</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_9DA8DAD7-9059-475A-5858-B27901440776','','','')"><p style="font-size: 105% " id="accountsWithoutActivities"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_4243362F-7137-41EB-5858-B25600051FDB','','','')">Team Sell Accounts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_4243362F-7137-41EB-5858-B25600051FDB','','','')"><p style="font-size: 105% " id="myTeamSellAccounts"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_59211831-BB75-45F8-5858-B2560006D215','','','')">Overserve  Accounts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_59211831-BB75-45F8-5858-B2560006D215','','','')"><p style="font-size: 105% " id="myOverserveAccounts"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_59211831-BB75-45F8-5858-B2560006D215','','','')">Underserved  Accounts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_59211831-BB75-45F8-5858-B2560006D215','','','')"><p style="font-size: 105% " id="myUnderservedAccounts"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td class="text-left-i-Accounts" colspan="2" style="text-align: left">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0 ">Segment Contacts</h4>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_A16F592E-7358-4043-5858-B2550187CB25','','','')" class="primary-account-link">Contacts</a>

                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_A16F592E-7358-4043-5858-B2550187CB25','','','')">New Contacts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_A16F592E-7358-4043-5858-B2550187CB25','','','')"><p style="font-size: 105% " id="MyNewContacts"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_A16F592E-7358-4043-5858-B2550187CB25','','','')">Key Contacts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_A16F592E-7358-4043-5858-B2550187CB25','','','')"><p style="font-size: 105% " id="MyKeyContacts"></p></a></td>
                                        </tr>

                                        <tr>
                                            <td class="text-left-i-contacts" colspan="2" style="text-align: left">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0 ">Segment Tasks</h4>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_38929CE9-D64C-4BED-5858-B2560009376B','','','')" class="primary-account-link">Tasks</a>

                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_38929CE9-D64C-4BED-5858-B2560009376B','','','')">Due Soon</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_38929CE9-D64C-4BED-5858-B2560009376B','','','')"><p style="font-size: 105% " id="MyTasksDueSoon"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_38929CE9-D64C-4BED-5858-B2560009376B','','','')">Overdue Tasks</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_38929CE9-D64C-4BED-5858-B2560009376B','','','')"><p style="font-size: 105% " id="MyTasksOverdue"></p></a></td>
                                        </tr>

                                        <tr>
                                            <td class="text-left-i-Tasks" colspan="2" style="text-align: left">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0 ">Segment Activities</h4>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_C1569CE9-3069-4F72-5858-B256000B7F2F','','','')" class="primary-account-link">Activities</a>

                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_778BE602-76F1-48EA-5858-B256000C3E36','','','')">Team Sell Activities</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_778BE602-76F1-48EA-5858-B256000C3E36','','','')"><p style="font-size: 105%" id="MyAcTeamSel"></p></a></td>
                                        </tr>
                                        <tr style="background-color: #ecf1f7; height: 30px; ">
                                            <td style="color: #a6a7aa; ">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_F31F68CF-9815-4BF1-5858-B256000E1A71','','','')" class="primary-account-link">Sales Visits</a>
                                                </div>
                                            </td>
                                            <td style=" color: #a6a7aa;">Qty</td>

                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_F31F68CF-9815-4BF1-5858-B256000E1A71','','','')">This Month</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_F31F68CF-9815-4BF1-5858-B256000E1A71','','','')"><p style="font-size: 105%" id="MySalesThisMonth"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_9EFE3080-C756-481E-5858-B28800686E6C','','','')">Last Month</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_9EFE3080-C756-481E-5858-B28800686E6C','','','')"><p style="font-size: 105%" id="MySalesLastMonth"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td class="text-left-i-activitiesSV" colspan="2" style="text-align: left">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            @*<div class="d-flex justify-content-between align-items-center mb-10">
                                    <div class="d-flex justify-content-start align-items-center">
                                        <h4 class="m-0 ">Segment Sales Visits</h4>
                                    </div>
                                </div>*@
                            @*<div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr class="header-row">
                                                <th class="custom-header-buttons">
                                                    <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                        <a href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_F31F68CF-9815-4BF1-5858-B256000E1A71','','','')" class="primary-account-link">Sales Visits</a>
                                                    </div>
                                                </th>
                                                <th>Qty</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><a href="#" onclick="gotoDesktop('DSK_F31F68CF-9815-4BF1-5858-B256000E1A71','','','')">This Month</a></td>
                                                <td><a href="#" onclick="gotoDesktop('DSK_F31F68CF-9815-4BF1-5858-B256000E1A71','','','')"><p style="font-size: 105%" id="MySalesThisMonth"></p></a></td>
                                            </tr>
                                            <tr>
                                                <td><a href="#" onclick="gotoDesktop('DSK_F31F68CF-9815-4BF1-5858-B256000E1A71','','','')">Last Month</a></td>
                                                <td><a href="#" onclick="gotoDesktop('DSK_F31F68CF-9815-4BF1-5858-B256000E1A71','','','')"><p style="font-size: 105%" id="MySalesLastMonth"></p></a></td>
                                            </tr>
                                            <tr>
                                                <td class="text-left-i-activitiesSV" colspan="2" style="text-align: left">
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>*@

                        </div>
                        <div class="col-lg-8 col-xs-12">
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0 ">Segment Opportunities</h4>

                                </div>
                            </div>
                            <table class="table table-bordered responsive first-col-right">
                                <thead>
                                    <tr>
                                        <th style="width: 20%;"><a href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_07E29005-6525-45C9-5858-B25600116431','','','')">Leads</a></th>
                                        <th style="width: 20%;">Qty</th>
                                        <th style="width: 30%;">Expected Value</th>
                                        <th style="width: 30%;">Weighted Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_07E29005-6525-45C9-5858-B25600116431','','','')">Open Leads</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_07E29005-6525-45C9-5858-B25600116431','','','')"><p style="font-size: 105%" id="MyLeads"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_07E29005-6525-45C9-5858-B25600116431','','','')"><p style="font-size: 105%" id="MyLeadsExptedVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_07E29005-6525-45C9-5858-B25600116431','','','')"><p style="font-size: 105%" id="MyLeadsWeightedVal"></p></a></td>

                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_7514CC78-B68B-4553-5858-B27D00BF6C14','','','')">Team Sell Leads</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_7514CC78-B68B-4553-5858-B27D00BF6C14','','','')"><p style="font-size: 105%" id="MyTeamSellLeads"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_7514CC78-B68B-4553-5858-B27D00BF6C14','','','')"><p style="font-size: 105%" id="MyTeamSellLeadsExptedVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_7514CC78-B68B-4553-5858-B27D00BF6C14','','','')"><p style="font-size: 105%" id="MyTeamSellLeadsWeightedVal"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_16D71CB5-AC8E-40C4-5858-B27D00BFBDF6','','','')">New Leads</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_16D71CB5-AC8E-40C4-5858-B27D00BFBDF6','','','')"><p style="font-size: 105%" id="MyNewLeads"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_16D71CB5-AC8E-40C4-5858-B27D00BFBDF6','','','')"><p style="font-size: 105%" id="MyNewLeadsExptedVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_16D71CB5-AC8E-40C4-5858-B27D00BFBDF6','','','')"><p style="font-size: 105%" id="MyNewLeadsWeightedVal"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_532E49C4-9F4A-49C0-5858-B27D0182C5BA','','','')">Overdue Leads</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_532E49C4-9F4A-49C0-5858-B27D0182C5BA','','','')"><p style="font-size: 105%" id="MyOverdueLeads"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_532E49C4-9F4A-49C0-5858-B27D0182C5BA','','','')"><p style="font-size: 105%" id="MyOverdueLeadsExptedVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_532E49C4-9F4A-49C0-5858-B27D0182C5BA','','','')"><p style="font-size: 105%" id="MyOverdueLeadsWeightedVal"></p></a></td>
                                    </tr>
                                    <tr style="background-color: #ecf1f7; height: 30px; ">
                                        <td width="33%" class="custom-header-buttons">
                                            <a href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_B5AF9FFE-23E9-4C34-5858-B256000FB313','','','')"> Opportunity Pipeline </a>
                                        </td>
                                        <td>
                                            <a href="#" onclick="gotoDesktop('DSK_B5AF9FFE-23E9-4C34-5858-B256000FB313','','','')"><span style="font-size: 105%" id="MyOpenOppsqty"></span> </a>
                                        </td>
                                        <td>
                                            <a href="#" onclick="gotoDesktop('DSK_B5AF9FFE-23E9-4C34-5858-B256000FB313','','','')"><span style="font-size: 105%" id="MyOpenOppsExptedVal"></span></a>
                                        </td>
                                        <td>
                                            <a href="#" onclick="gotoDesktop('DSK_B5AF9FFE-23E9-4C34-5858-B256000FB313','','','')"><span style="font-size: 105%" id="MyOpenOppsWeightedVal"></span></a>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_A604E9DD-15AE-4780-5858-B27D0178648F','','','')">Team Sell</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_A604E9DD-15AE-4780-5858-B27D0178648F','','','')"><p style="font-size: 105%" id="MyTeamSellOpps_count"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_A604E9DD-15AE-4780-5858-B27D0178648F','','','')"><p style="font-size: 105%" id="MyTeamSellOpps_expval"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_A604E9DD-15AE-4780-5858-B27D0178648F','','','')"><p style="font-size: 105%" id="MyTeamSellOpps_wghtval"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_143C2A21-03BD-4D68-5858-B27D0178CC64','','','')">Due Next 30 days</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_143C2A21-03BD-4D68-5858-B27D0178CC64','','','')"><p style="font-size: 105%" id="MyDueNext30DaysOpps_count"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_143C2A21-03BD-4D68-5858-B27D0178CC64','','','')"><p style="font-size: 105%" id="MyDueNext30DaysOpps_expval"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_143C2A21-03BD-4D68-5858-B27D0178CC64','','','')"><p style="font-size: 105%" id="MyDueNext30DaysOpps_wghtval"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_14581191-9BEF-4AAE-5858-B27D0178A559','','','')">Overdue Opportunities</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_14581191-9BEF-4AAE-5858-B27D0178A559','','','')"><p style="font-size: 105%" id="MyOverdueOpps_count"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_14581191-9BEF-4AAE-5858-B27D0178A559','','','')"><p style="font-size: 105%" id="MyOverdueOpps_expval"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_14581191-9BEF-4AAE-5858-B27D0178A559','','','')"><p style="font-size: 105%" id="MyOverdueOpps_wghtval"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td class="text-left-i-opps" colspan="4">
                                        </td>
                                    </tr>
                                    <tr style="background-color: #ecf1f7; height: 30px; ">
                                        <td style="color: #a6a7aa; ">Closed Last 90 Days</td>
                                        <td style=" color: #a6a7aa;">Qty</td>
                                        <td style=" color: #a6a7aa;">Total</td>
                                        <td style=" color: #a6a7aa;">Rate (%)</td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_885F4B13-9CA6-4FA8-5858-B25600133609','','','')">Won</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_885F4B13-9CA6-4FA8-5858-B25600133609','','','')"><p style="font-size: 105%" id="MyOpps_won_Qty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_885F4B13-9CA6-4FA8-5858-B25600133609','','','')"><p style="font-size: 105%" id="MyOpps_won_Tot"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_885F4B13-9CA6-4FA8-5858-B25600133609','','','')"><p style="font-size: 105%" id="MyOpps_won_Rate"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_885F4B13-9CA6-4FA8-5858-B25600133609','','','')">Lost</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_885F4B13-9CA6-4FA8-5858-B25600133609','','','')"><p style="font-size: 105%" id="MyOpps_Lost_Qty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_885F4B13-9CA6-4FA8-5858-B25600133609','','','')"><p style="font-size: 105%" id="MyOpps_Lost_Tot"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_885F4B13-9CA6-4FA8-5858-B25600133609','','','')"><p style="font-size: 105%" id="MyOpps_Lost_Rate"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_885F4B13-9CA6-4FA8-5858-B25600133609','','','')">Cancelled</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_885F4B13-9CA6-4FA8-5858-B25600133609','','','')"><p style="font-size: 105%" id="MyOpps_Cancelled_Qty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_885F4B13-9CA6-4FA8-5858-B25600133609','','','')"><p style="font-size: 105%" id="MyOpps_Cancelled_Tot"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_885F4B13-9CA6-4FA8-5858-B25600133609','','','')"><p style="font-size: 105%" id="MyOpps_Cancelled_Rate"></p></a></td>
                                    </tr>
                                </tbody>

                            </table>


                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0 ">Segment Quotes</h4>

                                </div>
                            </div>
                            <table class="table table-bordered responsive first-col-right">
                                <thead>
                                    <tr>
                                        <th style="width: 20%;"> </th>
                                        <th style="width: 20%;">Qty</th>
                                        <th style="width: 30%;">Total</th>
                                        <th style="width: 30%;">Margin (%)</th>

                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FCB6CAA6-D735-46BC-5858-B2560024144F','','','')">Open RFQ's</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FCB6CAA6-D735-46BC-5858-B2560024144F','','','')"><p style="font-size: 105%" id="OpenRFQsQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FCB6CAA6-D735-46BC-5858-B2560024144F','','','')"><p style="font-size: 105%" id="OpenRFQstotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_FCB6CAA6-D735-46BC-5858-B2560024144F','','','')"><p style="font-size: 105%" id="OpenRFQsmargin"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3FD8AAE8-8DCD-45E8-5858-B23E015C4DF7','','','')"> All Open Quotes</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3FD8AAE8-8DCD-45E8-5858-B23E015C4DF7','','','')"><p style="font-size: 105%" id="MyQuotesQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3FD8AAE8-8DCD-45E8-5858-B23E015C4DF7','','','')"><p style="font-size: 105%" id="MyQuotesTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_3FD8AAE8-8DCD-45E8-5858-B23E015C4DF7','','','')"><p style="font-size: 105%" id="MyQuotesMargin"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_1995E5B7-ED02-4FA5-5858-B241010FFA83','','','')">Open Strategic Quotes</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_1995E5B7-ED02-4FA5-5858-B241010FFA83','','','')"><p style="font-size: 105%" id="OpenStrategicQuotesQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_1995E5B7-ED02-4FA5-5858-B241010FFA83','','','')"><p style="font-size: 105%" id="OpenStrategicQuotesTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_1995E5B7-ED02-4FA5-5858-B241010FFA83','','','')"><p style="font-size: 105%" id="OpenStrategicQuotesMargin"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_14191786-1215-46BD-5858-B27D0175E62C','','','')">New Strategic Quotes</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_14191786-1215-46BD-5858-B27D0175E62C','','','')"><p style="font-size: 105%" id="NewStrategicQuotesQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_14191786-1215-46BD-5858-B27D0175E62C','','','')"><p style="font-size: 105%" id="NewStrategicQuotesTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_14191786-1215-46BD-5858-B27D0175E62C','','','')"><p style="font-size: 105%" id="NewStrategicQuotesMargin"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_D6831449-EB9B-4D38-5858-B27D01762FE7','','','')">Overdue Strategic Quotes</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_D6831449-EB9B-4D38-5858-B27D01762FE7','','','')"><p style="font-size: 105%" id="OverdueStrategicQuotesQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_D6831449-EB9B-4D38-5858-B27D01762FE7','','','')"><p style="font-size: 105%" id="OverdueStrategicQuotesTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_D6831449-EB9B-4D38-5858-B27D01762FE7','','','')"><p style="font-size: 105%" id="OverdueStrategicQuotesMargin"></p></a></td>

                                    </tr>
                                    <tr>
                                        <td class="text-left-i-quotes" colspan="4">
                                        </td>
                                    </tr>

                                    <tr style="background-color: #ecf1f7; height: 30px; ">
                                        <td style="color: #a6a7aa; text-align: left;">Closed Last 90 Days</td>
                                        <td style="color: #a6a7aa;">Qty</td>
                                        <td style="color: #a6a7aa;">Total</td>
                                        <td style="color: #a6a7aa;">Rate (%)</td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B2F3D694-EC49-43FC-5858-B25600254D53','','','')">Strategic Quotes - Won</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B2F3D694-EC49-43FC-5858-B25600254D53','','','')"><p style="font-size: 105%" id="StrategicQuotesWonQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B2F3D694-EC49-43FC-5858-B25600254D53','','','')"><p style="font-size: 105%" id="StrategicQuotesWonTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B2F3D694-EC49-43FC-5858-B25600254D53','','','')"><p style="font-size: 105%" id="StrategicQuotesWonRate"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B2F3D694-EC49-43FC-5858-B25600254D53','','','')">Strategic Quotes - Lost</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B2F3D694-EC49-43FC-5858-B25600254D53','','','')"><p style="font-size: 105%" id="StrategicQuotesLostQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B2F3D694-EC49-43FC-5858-B25600254D53','','','')"><p style="font-size: 105%" id="StrategicQuotesLostTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B2F3D694-EC49-43FC-5858-B25600254D53','','','')"><p style="font-size: 105%" id="StrategicQuotesLostRate"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B2F3D694-EC49-43FC-5858-B25600254D53','','','')">Strategic Quotes - Cancelled</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B2F3D694-EC49-43FC-5858-B25600254D53','','','')"><p style="font-size: 105%" id="StrategicQuotesCancelledQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B2F3D694-EC49-43FC-5858-B25600254D53','','','')"><p style="font-size: 105%" id="StrategicQuotesCancelledTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B2F3D694-EC49-43FC-5858-B25600254D53','','','')"><p style="font-size: 105%" id="StrategicQuotesCancelledRate"></p></a></td>
                                    </tr>
                                </tbody>

                            </table>
                        </div>
                    </div>

                </div>
                <!-- Footer -->
                <footer class="main mt-0 pt-20 pb-20 pl-20">
                    &copy; 2020 <strong>Selltis</strong>
                </footer>
            </div>
        </div>
    </section>
</section>

<script type="text/javascript">
    $(document).ready(function () {

        showProgress();
        GetSegment_Dashboard_Data();
    });
    function onChange(e) {
        debugger
    var selectedNode = this.treeview.dataItem(this.treeview.select());
    if (selectedNode) {

        var selectedNodeId = selectedNode.id;
        var selectedNodeName = selectedNode.Name;
        updateBreadcrumb(selectedNode);
        //if (selectedNode) {
        //    this.treeView.expand(selectedNode); // Expand the selected node
        //}
        //debugger
        $.ajax({
            url: '@Url.Action("SetSegmentManagerUserID", "DetailsPage")',
            method: 'POST',
            data: { SelectedUser: selectedNodeId, SelectedNodeName: selectedNodeName },
            success: function (response) {
                if (response.success) {

                   window.location.href = '@Url.Action("SalesManagerDashboard", "DetailsPage")';
               } else {
                   showProgress();
                   GetSegment_Dashboard_Data();
               }
}
        });
    }
}
    function updateBreadcrumb(selectedNode) {

        var breadcrumb = $("#breadcrumb");
        breadcrumb.empty();

        if (!selectedNode) return;
        var nodePath = [];

        while (selectedNode) {
            nodePath.unshift(selectedNode);
            selectedNode = selectedNode.parentNode();
        }

        if (nodePath.length === 0) return;
        nodePath.forEach((node, index) => {
            if (index > 0) breadcrumb.append("<span>&nbsp;&nbsp;&gt;&nbsp;&nbsp;</span>");
            breadcrumb.append(
                $("<a>")
                    .text(node.Name || "Undefined")
                    .attr("href", "#")
                    .data("id", node.id)
                    .on("click", function (e) {
                        e.preventDefault();
                        ChangeNodeInDropdownTree(node.id, node.Name);
                        selectNodeInDropdownTree(node.id);
                    })
                    .css("color", "white")
            );
        });
    }
    function ChangeNodeInDropdownTree(selectedNodeId, SelectedNodeName) {
        const breadcrumb = $("#breadcrumb");
        let shouldRemove = false;

        breadcrumb.children().each(function () {
            const $child = $(this);

            if ($child.data("id") === selectedNodeId) {
                shouldRemove = true;
            } else if (shouldRemove) {
                $child.remove();
            }
        });
         const dropdown = $("#SalesPersonHierarchy").data("kendoDropDownTree");
    if (dropdown) {
        const selectedNode = dropdown.dataSource.get(selectedNodeId);
        if (selectedNode) {
            dropdown.value(selectedNodeId);
        }
    }

    $.ajax({
        url: '@Url.Action("SetSegmentManagerUserID", "DetailsPage")',
        method: 'POST',
        data: { SelectedUser: selectedNodeId, SelectedNodeName: SelectedNodeName },
        success: function (response) {
            //alert(response.success)
            if (response.success) {
                showProgress();
                GetSegment_User_Dashboard_Data();
            } else {
                window.location.href = '@Url.Action("Segmentsalesdashboard", "DetailsPage")';
            }
        },
        error: function (error) {
            console.error("Redirection failed:", error);
        }
    });
}
    function selectNodeInDropdownTree(nodeId) {
        var treeView = dropdownTree.treeview;
        var nodeToSelect = findNodeById(treeView.dataSource.view(), nodeId);

        if (nodeToSelect) {
            treeView.select(treeView.findByUid(nodeToSelect.uid));
            dropdownTree.value(nodeToSelect.id);
            treeView.expandTo(nodeToSelect.id);
            updateBreadcrumb(nodeToSelect);
        }
    }
    function findNodeById(nodes, nodeId) {
        for (var i = 0; i < nodes.length; i++) {
            if (nodes[i].id == nodeId) {
                return nodes[i];
            } else if (nodes[i].hasChildren) {
                var childResult = findNodeById(nodes[i].children.view(), nodeId);
                if (childResult) return childResult;
            }
        }
        return null;
    }
    function GetSegment_Dashboard_Data() {
         $.ajax({
            url: '@Url.Action("GetSegmentsalesdashboard", "DetailsPage")',
            type: 'GET',
            dataType: 'json',
            success: function (data) {

                try {
                    if (data && data.length > 0) {
                        var item = data[0];

                        console.log(data[0]);

                        $('#overDueprimaryAccountsQty').text(item.OverDueprimaryAccountsQty || '0');
                        $('#accountsWithoutContacts').text(item.AccountsWithoutContacts || '0');
                        $('#accountsWithoutActivities').text(item.AccountsWithoutActivities || '0');
                        $('#myTeamSellAccounts').text(item.TeamSellAccounts || '0');
                        $('#myOverserveAccounts').text(item.OverserveAccounts || '0');
                        $('#myUnderservedAccounts').text(item.UnderservedAccounts || '0');
                        $('#MyNewContacts').text(item.NewContacts || '0');
                        $('#MyKeyContacts').text(item.KeyContacts || '0');
                        $('#MyTasksOverdue').text(item.MyTasksOverdue || '0');
                        $('#MyAcTeamSel').text(item.MyAcTeamSel || '0');
                        $('#MySalesThisMonth').text(item.MySalesThisMonth || '0');

                        $('#MyLeads').text(item.MyLeads || '0');
                        $('#MyLeadsExptedVal').text(item.MyLeadsExptedVal || '0');
                        $('#MyLeadsWeightedVal').text('');
                        $('#MyNewLeads').text(item.MyNewLeads || '0');
                        $('#MyNewLeadsExptedVal').text(item.MyNewLeadsExptedVal || '0');
                        $('#MyNewLeadsWeightedVal').text('');
                        $('#MyTeamSellLeads').text(item.MyTeamSellLeads || '0');
                        $('#MyTeamSellLeadsExptedVal').text(item.MyTeamSellLeadsExptedVal || '0');
                        $('#MyTeamSellLeadsWeightedVal').text('');
                        $('#MyOverdueLeads').text(item.MyOverdueLeads || '0');
                        $('#MyOverdueLeadsExptedVal').text(item.MyOverdueLeadsExptedVal || '0');
                        $('#MyOverdueLeadsWeightedVal').text('');

                        $('#MyOpenOppsqty').text(item.MyOpenOppsqty || '0');
                        $('#MyOpenOppsExptedVal').text(item.MyOpenOppsExptedVal || '0');
                        $('#MyOpenOppsWeightedVal').text(item.MyOpenOppsWeightedVal || '0');
                        $('#MyDueNext30DaysOpps_count').text(item.MyDueNext30DaysOpps_count || '0');
                        $('#MyDueNext30DaysOpps_expval').text(item.MyDueNext30DaysOpps_expval || '0');
                        $('#MyDueNext30DaysOpps_wghtval').text(item.MyDueNext30DaysOpps_wghtval || '0');
                        $('#MyTeamSellOpps_count').text(item.MyTeamSellOpps_count || '0');
                        $('#MyTeamSellOpps_expval').text(item.MyTeamSellOpps_expval || '0');
                        $('#MyTeamSellOpps_wghtval').text(item.MyTeamSellOpps_wghtval || '0');
                        $('#MyOverdueOpps_count').text(item.MyOverdueOpps_count || '0');
                        $('#MyOverdueOpps_expval').text(item.MyOverdueOpps_expval || '0');
                        $('#MyOverdueOpps_wghtval').text(item.MyOverdueOpps_wghtval || '0');

                        $('#OpenRFQsQty').text(item.OpenRFQsQty || '0');
                        $('#OpenRFQstotal').text(item.OpenRFQstotal || '0');
                        $('#OpenRFQsmargin').text(item.OpenRFQsmargin || '0');
                        $('#MyQuotesQty').text(item.MyQuotesQty || '0');
                        $('#MyQuotesTotal').text(item.MyQuotesTotal || '0');
                        $('#MyQuotesMargin').text(item.MyQuotesMargin || '0');

                        $('#MyOpps_won_Qty').text(item.MyOpps_won_Qty || '0');
                        $('#MyOpps_won_Tot').text(item.MyOpps_won_Tot || '0');
                        $('#MyOpps_won_Rate').text(item.MyOpps_won_Rate || '0');
                        $('#MyOpps_Lost_Qty').text(item.MyOpps_Lost_Qty || '0');
                        $('#MyOpps_Lost_Tot').text(item.MyOpps_Lost_Tot || '0');
                        $('#MyOpps_Lost_Rate').text(item.MyOpps_Lost_Rate || '0');
                        $('#MyOpps_Cancelled_Qty').text(item.MyOpps_Cancelled_Qty || '0');
                        $('#MyOpps_Cancelled_Tot').text(item.MyOpps_Cancelled_Tot || '0');
                        $('#MyOpps_Cancelled_Rate').text(item.MyOpps_Cancelled_Rate || '0');

                        $('#NewStrategicQuotesQty').text(item.NewStrategicQuotesQty || '0');
                        $('#NewStrategicQuotesTotal').text(item.NewStrategicQuotesTotal || '0');
                        $('#NewStrategicQuotesMargin').text(item.NewStrategicQuotesMargin || '0');
                        $('#OpenStrategicQuotesQty').text(item.OpenStrategicQuotesQty || '0');
                        $('#OpenStrategicQuotesTotal').text(item.OpenStrategicQuotesTotal || '0');
                        $('#OpenStrategicQuotesMargin').text(item.OpenStrategicQuotesMargin || '0');
                        $('#OverdueStrategicQuotesQty').text(item.OverdueStrategicQuotesQty || '0');
                        $('#OverdueStrategicQuotesTotal').text(item.OverdueStrategicQuotesTotal || '0');
                        $('#OverdueStrategicQuotesMargin').text(item.OverdueStrategicQuotesMargin || '0');

                        $('#StrategicQuotesWonQty').text(item.StrategicQuotesWonQty || '0');
                        $('#StrategicQuotesWonTotal').text(item.StrategicQuotesWonTotal || '0');
                        $('#StrategicQuotesWonRate').text(item.StrategicQuotesWonRate || '0');
                        $('#StrategicQuotesLostQty').text(item.StrategicQuotesLostQty || '0');
                        $('#StrategicQuotesLostTotal').text(item.StrategicQuotesLostTotal || '0');
                        $('#StrategicQuotesLostRate').text(item.StrategicQuotesLostRate || '0');
                        $('#StrategicQuotesCancelledQty').text(item.StrategicQuotesCancelledQty || '0');
                        $('#StrategicQuotesCancelledTotal').text(item.StrategicQuotesCancelledTotal || '0');
                        $('#StrategicQuotesCancelledRate').text(item.StrategicQuotesCancelledRate || '0');
                        $('#MyTasksDueSoon').text(item.MyTasksDueSoon || '0');
                        $('#MySalesLastMonth').text(item.MySalesLastMonth || '0');

                        const dropdown = $("#SalesPersonHierarchy").data("kendoDropDownTree");


                        if (dropdown) {
                            dropdown.bind("dataBound", function () {
                                const selectedUserID = item.SelectedUserID;
                                const selectedNode = dropdown.dataSource.get(selectedUserID);

                                if (selectedNode) {
                                    dropdown.value(selectedUserID);
                                    expandToNode(selectedNode, dropdown);
                                    updateBreadcrumb(selectedNode);

                                }
                            });
                        }

                        $(window).on('load', function () {
                            hideProgress();
                        });

                        $(window).on('pageshow', function (event) {
                            if (event.originalEvent.persisted) {
                                hideProgress();
                            }
                        });

                    }
                } catch (e) {
                    console.log(e);
                    hideProgress();
                }

                hideProgress();
        },
            });

    }
    function expandToNode(node, dropdown) {
        const treeview = dropdown.treeview;
        const path = [];
        let currentNode = node;
        while (currentNode) {
            path.unshift(currentNode.id);
            currentNode = dropdown.dataSource.parent(currentNode);
        }
        path.forEach((nodeId) => {
            const treeNode = treeview.findByUid(dropdown.dataSource.get(nodeId).uid);
            if (treeNode.length) {
                treeview.expand(treeNode);
            }
        });

        const selectedNode = treeview.findByUid(dropdown.dataSource.get(node.id).uid);
        if (selectedNode.length) {
            treeview.expand(selectedNode);
        }
    }
    function GoDetailsPage(file, gid) {
        if (file == 'CO') {
            // window.location = "/DetailsPage/CompanyDetails/?sCompanyId=" + gid;

            window.location = "/DetailsPage/PRF?sPRPId=PRF_CO&sRecId=" + gid
        }
        else if (file == 'CN') {
            //window.location = "/DetailsPage/ContactDetails/?sContactId=" + gid;
            window.location = "/DetailsPage/PRF?sPRPId=PRF_CN&sRecId=" + gid
        }
        else if (file == 'OP') {
            //window.location = "/DetailsPage/OPDetails/?sOPId=" + gid;
            window.location = "/DetailsPage/PRF?sPRPId=PRF_OP&sRecId=" + gid
        }
    }
    function openRecord(tablename, gid_id) {

        window.location = "/CreateForm/CreateForm/" + tablename + "/" + gid_id + "/TYPE/false/false/KEY/false/MASTERID/FORMKEY/FIELD/"

    }
    function RefreshData() {
        showProgress();
        $.ajax({
            url: '/DetailsPage/RefreshOutsideSalesDashboardData',
            type: 'POST',
            dataType: 'json',
            success: function (response) {
                if (response.success) {

                    window.location = "/DetailsPage/Outsidesalesdashboard"
                    //gotoDesktop('DSK_3E971649-F608-49AD-5858-B20C00C8201E', '', '', '')
                } else {

                }
            },

        });

    }
    function PrepareFormLink(tableName, type) {
    ////debugger;

    if (navigator.onLine == false) {
       // hideProgress();
        alert('Check your Internet Connection');
        return false;
    }

   // showProgress();

    window.location = "/CreateForm/CreateForm/" + tableName + "/ID/" + type + "/false/false/KEY/false/MASTERID/FORMKEY/FIELD/";

    }

</script>
<script>
    var spinnerVisible = false;
    function showProgress() {
        if (!spinnerVisible) {
            $("div#spinnertool").fadeIn("fast");
            spinnerVisible = true;
        }
    };
    function hideProgress() {
        if (spinnerVisible) {
            var spinner = $("div#spinnertool");
            spinner.stop();
            spinner.fadeOut("fast");
            spinnerVisible = false;
        }
    };
</script>
