﻿CREATE TABLE [dbo].[XL] (
    [GID_ID]           UNIQUEIDENTIFIER CONSTRAINT [DF_XL_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'XL',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]           BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_Name]         NVARCHAR (80)    NULL,
    [DTT_CreationTime] DATETIME         CONSTRAINT [DF_XL_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [TXT_Module]       VARCHAR (80)     NULL,
    [SI__Level]        TINYINT          NULL,
    [CHK_Stack]        TINYINT          NULL,
    [CHK_Trace]        TINYINT          NULL,
    [SI__WarnErr]      TINYINT          CONSTRAINT [DF_XL_SI__WarnErr] DEFAULT ((0)) NULL,
    [TXT_Message]      VARCHAR (7600)   NULL,
    [TXT_SessionID]    VARCHAR (30)     NULL,
    [SI__ShareState]   TINYINT          CONSTRAINT [DF_XL_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US] UNIQUEIDENTIFIER NULL,
    [TXT_Product]      VARCHAR (2)      NULL,
    [SI__Purpose]      TINYINT          DEFAULT ((0)) NULL,
    [TXT_Browser]      VARCHAR (40)     NULL,
    [TXT_Version]      VARCHAR (20)     NULL,
    [LI__MajorVersion] INT              NULL,
    [DR__MinorVersion] FLOAT (53)       NULL,
    [TXT_LoginName]    VARCHAR (20)     NULL,
    [CHK_WebService]   TINYINT          NULL,
    CONSTRAINT [PK_XL] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_XL_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[XL] NOCHECK CONSTRAINT [LNK_XL_CreatedBy_US];


GO
CREATE NONCLUSTERED INDEX [IX_XL_Related_US]
    ON [dbo].[XL]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XL_BI__ID]
    ON [dbo].[XL]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XL_CreationTime]
    ON [dbo].[XL]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XL_SessionID]
    ON [dbo].[XL]([TXT_SessionID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XL_PurposeCreationTime]
    ON [dbo].[XL]([SI__Purpose] ASC, [DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XL_Product]
    ON [dbo].[XL]([TXT_Product] ASC, [BI__ID] DESC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'0=not error, 1=warning, 2=error', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'XL', @level2type = N'COLUMN', @level2name = N'SI__WarnErr';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Null, blank or SA=Sales; MB=Mobile', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'XL', @level2type = N'COLUMN', @level2name = N'TXT_Product';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'0=unassigned; 1=login success; 2=login failure; 10=UI object start', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'XL', @level2type = N'COLUMN', @level2name = N'SI__Purpose';

