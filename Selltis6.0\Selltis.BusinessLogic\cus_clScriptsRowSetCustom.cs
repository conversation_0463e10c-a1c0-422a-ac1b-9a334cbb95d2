﻿using System;
using System.Web;
using System.Net;
using System.IO;

namespace Selltis.BusinessLogic
{
	public class clScriptsRowSetCustom
	{

		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clLog goLog;
		private clDefaults goDef;
		private clScrMngRowSet goScr;
		//Dim goUI As clUI
		private clPerm goPerm;
		private clEmail goEmail;
		public string sError;

		public void Initialize()
		{

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			goDef = (Selltis.BusinessLogic.clDefaults)HttpContext.Current.Session["goDef"];
			goScr = (Selltis.BusinessLogic.clScrMngRowSet)HttpContext.Current.Session["goScr"];
			// goUI = HttpContext.Current.Session("goUI")
			goPerm = (Selltis.BusinessLogic.clPerm)HttpContext.Current.Session["goPerm"];

		}

		public bool AC_RecordOnSave_Post(ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5, ref object par_oReturn, ref bool par_bRunNext)
		{
			string tempVar = "";
			return AC_RecordOnSave_Post(ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref par_bRunNext, ref tempVar);
		}

		public bool AC_RecordOnSave_Post(ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5, ref object par_oReturn)
		{
			bool tempVar = true;
			string tempVar2 = "";
			return AC_RecordOnSave_Post(ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref tempVar, ref tempVar2);
		}

		public bool AC_RecordOnSave_Post(ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return AC_RecordOnSave_Post(ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool AC_RecordOnSave_Post(ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2, string par_s3, string par_s4)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return AC_RecordOnSave_Post(ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool AC_RecordOnSave_Post(ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2, string par_s3)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return AC_RecordOnSave_Post(ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool AC_RecordOnSave_Post(ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return AC_RecordOnSave_Post(ref par_doCallingObject, par_doArray, par_s1, par_s2, "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool AC_RecordOnSave_Post(ref object par_doCallingObject, clArray par_doArray, string par_s1)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return AC_RecordOnSave_Post(ref par_doCallingObject, par_doArray, par_s1, "", "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool AC_RecordOnSave_Post(ref object par_doCallingObject, clArray par_doArray)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return AC_RecordOnSave_Post(ref par_doCallingObject, par_doArray, "", "", "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool AC_RecordOnSave_Post(ref object par_doCallingObject)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return AC_RecordOnSave_Post(ref par_doCallingObject, null, "", "", "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool AC_RecordOnSave_Post()
		{
			object tempVar = null;
			object tempVar2 = null;
			bool tempVar3 = true;
			string tempVar4 = "";
			return AC_RecordOnSave_Post(ref tempVar, null, "", "", "", "", "", ref tempVar2, ref tempVar3, ref tempVar4);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Function AC_RecordOnSave_Post(Optional ByRef par_doCallingObject As Object = vbNull, Optional ByVal par_doArray As clArray = Nothing, Optional ByVal par_s1 As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "", Optional ByRef par_oReturn As Object = vbNull, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
		public bool AC_RecordOnSave_Post(ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections)
		{
			//par_doCallingObject: Form object calling this script. Do not delete in script!
			//par_doArray: Unused.
			//par_s1: Unused.
			//par_s2 to par_s5: Unused.
			//par_oReturn: Object passed to script ByRef.  Used to return values to calling process
			//par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
			//par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

			//For notes on how to create a custom script, see clScrMng. ***

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			clRowSet oRS = (clRowSet)par_doCallingObject;
			clEmail oSend = new clEmail();
			string sBody = null;

			if (oRS.iRSType == 2)
			{

				int iType = Convert.ToInt32(goTR.StringToNum(oRS.GetFieldVal("MLS_TYPE", clC.SELL_SYSTEM)));
				int iPurpose = Convert.ToInt32(goTR.StringToNum(oRS.GetFieldVal("MLS_PURPOSE", clC.SELL_SYSTEM)));

				if (iType == 22 && iPurpose == 56) //RUN ONLY IF WEB SUBMISSION & EVENT REGISTRATION
				{

					string sGID = Convert.ToString(oRS.GetFieldVal("GID_ID"));
					clArray oArray = new clArray();
					oArray = oRS.GetLinkVal("LNK_INVOLVES_US", ref oArray);
					string sUS = "";
					int i = 0;
					for (i = 1; i <= oArray.GetDimension(); i++)
					{
						sUS = oArray.GetItem(i);
						// If sUS <> "" Then goUI.AddAlert("Event Registration", clC.SELL_ALT_OPENRECORD, sGID, sUS)
					}
				}

				//CS 3/12/12: Per BKG, when a 'Request...' purpose AC comes in (via Webforms), email a 
				//notification to Brian.
				switch (iPurpose)
				{

					case 21:
					case 22:
					case 23:
					case 24: //21=Request Lit 22=Request Web Demo, 23=Request Call, 24=Request Quote
						//Create message body
						sBody = "A " + oRS.GetFieldVal("MLS_PURPOSE", 1).ToString() + " Activity has been received." + "\r\n" + "\r\n";
						sBody = sBody + Convert.ToString(oRS.GetFieldVal("MMO_NOTES"));
						break;
						//goLog.Log(sProc, sBody, 0, True, True)
						// If oSend.SendSMTPEmail("Web Request Received", sBody, "<EMAIL>", "", "", "", "Selltis Support", "<EMAIL>", , , , , , False) = False Then

						// End If
				}


			}

			return true;

		}

	}

}
