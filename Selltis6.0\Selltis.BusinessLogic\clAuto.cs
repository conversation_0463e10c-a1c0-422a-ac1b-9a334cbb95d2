﻿using System;
using System.Data;
using System.Web;

//OWNER: RH

namespace Selltis.BusinessLogic
{
	public class clAuto
	{
		private clProject goP;
		private clTransform goTR;
		private clMetaData goMeta;
		private clData goData;
		private clError goErr;
		private clLog goLog;
		private clDefaults goDef;
		private clScrMngRowSet goScr;

		private bool ACT_DisplayMessage()
		{


// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		private bool ACT_OpenURL()
		{


// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		private bool ACT_RunAutomator(string par_s)
		{




// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		private bool ACT_RunScript()
		{


// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public bool RunActions(string par_sAutomator)
		{

			string sScript = goTR.StrRead(par_sAutomator, "A_01_EXECUTE");

			if (goScr.RunScript(sScript) == false)
			{
				return false;
			}
			else
			{
				return true;
			}

		}

		public bool TestTimerTrigger(string par_sID, string sAutomator, DateTime dtNow)
		{

			//PURPOSE:
			//		Test whether timer automator should be launched now.
			//       If 

			//PARAMETERS:
			//		par_sID: Automator ID.

			//RETURNS:
			//		'true' if automator should be launched.


			DateTime dtLast = default(DateTime);
			DateTime dtNext = default(DateTime);

			string sUser = goP.GetUserTID();
			string sPTAPage = goMeta.PageRead("GLOBAL", "PTA_" + goTR.FromTo(par_sID, 5));

			//create pta if it does not exist
			if (sPTAPage == "")
			{
				goTR.StrWrite(ref sPTAPage, "LastDate", clC.SELL_BLANK_DATETIME);
				goTR.StrWrite(ref sPTAPage, "INPROGRESS", "0");
				goTR.StrWrite(ref sPTAPage, "Name", "Pointer for timer automator " + goTR.StrRead(sAutomator, "US_NAME"));
				goMeta.PageWrite("", "PTA_" + goTR.FromTo(par_sID, 5), sPTAPage);
			}

			string bInProgress = goTR.StrRead(sPTAPage, "INPROGRESS", "0");
			if (bInProgress == "1")
			{
				//RH Added test of LASTSTARTTIME            '
				DateTime dtLastStart = goTR.StringToDateTime(goTR.StrRead(sPTAPage, "LASTSTARTTIME"));
				if (dtLastStart.AddHours(12) < (DateTime.Now))
				{
					return true;
				}
				else
				{
					return false;
				}
			}

			dtLast = goTR.StringToDateTime(goTR.StrRead(sPTAPage, "LASTDATE"));

			//if lastdate is blank then set as now without running and exit
			if (dtLast == Convert.ToDateTime(clC.SELL_BLANK_DATETIME))
			{
				goMeta.LineWrite("GLOBAL", "PTA_" + goTR.FromTo(par_sID, 5), "LASTDATE", goTR.DateTimeToString(dtNow));
				return false;
			}

			//Dim bIfMissed As Boolean = Val(goTR.StrRead(sAutomator, "E_TM_MISSED"))    'Not supported

			string sName = goTR.StrRead(sAutomator, "NAME");
			long lInterval = Convert.ToInt64(NumericHelper.Val(goTR.StrRead(sAutomator, "E_TM_INTERVAL")));
			int iMinute = Convert.ToInt32(NumericHelper.Val(goTR.StrRead(sAutomator, "E_TM_MINUTE")));
			int iHour = Convert.ToInt32(("00" + goTR.StrRead(sAutomator, "E_TM_HOUR")).Substring(("00" + goTR.StrRead(sAutomator, "E_TM_HOUR")).Length - 2));
			int iDay = Convert.ToInt32(NumericHelper.Val(goTR.StrRead(sAutomator, "E_TM_DAY")));
			int iDate = Convert.ToInt32(NumericHelper.Val(goTR.StrRead(sAutomator, "E_TM_DATE")));
			// Dim bBetween As Boolean = Val(goTR.StrRead(sAutomator, "E_TM_ONLYBETWEEN"))    'Not supported
			long lPeriod = 0;

			//Strip seconds and milliseconds
			dtLast = dtLast.AddMilliseconds(-dtLast.Millisecond);
			dtLast = dtLast.AddSeconds(-dtLast.Second);
			dtNext = dtNext.AddMilliseconds(-dtNext.Millisecond);
			dtNext = dtNext.AddSeconds(-dtNext.Second);
			dtNow = dtNow.AddMilliseconds(-dtNow.Millisecond);
			dtNow = dtNow.AddSeconds(-dtNow.Second);



			switch (lInterval)
			{

				case clC.SELL_ALERT_EVERY_NMINUTES: // ? minutes

					//fire if next date is greater than or equal to now

					if (dtLast.AddMinutes(iMinute) <= dtNow)
					{
						return true;
					}
					else
					{
						return false;
					}
					break;

				case clC.SELL_ALERT_EVERY_HOUR: // Hour

					dtNext = dtLast;

					//derive next valid date after last date
					dtNext = dtNext.AddMinutes(1);

					while (dtNext.Minute != iMinute)
					{
						dtNext = dtNext.AddMinutes(1);
						lPeriod = lPeriod + 1;
						if (lPeriod > 1000000)
						{
							//==> raise error
						}
					}

					//fire if now date is greater than or equal to next date

					if (dtNow >= dtNext)
					{
						return true;
					}
					else
					{
						return false;
					}
					break;


				case clC.SELL_ALERT_EVERY_DAY:

					dtNext = dtLast;

					//derive next valid date after last date
					dtNext = dtNext.AddMinutes(1);

					while (dtNext.Hour != iHour || dtNext.Minute != iMinute)
					{
						dtNext = dtNext.AddMinutes(1);
						lPeriod = lPeriod + 1;
						if (lPeriod > 1000000)
						{
							//==> raise error
						}
					}

					//fire if now date is greater than or equal to next date

					if (dtNow >= dtNext)
					{
						return true;
					}
					else
					{
						return false;
					}
					break;


				case clC.SELL_ALERT_EVERY_WEEK: // Week

					dtNext = dtLast;
					iDay = iDay + 1;

					//derive next valid date after last date
					dtNext = dtNext.AddMinutes(1);

					while (dtNext.Hour != iHour || dtNext.Minute != iMinute || (int)dtNext.DayOfWeek != iDay)
					{
						dtNext = dtNext.AddMinutes(1);
						lPeriod = lPeriod + 1;
						if (lPeriod > 1000000)
						{
							//==> raise error
						}
					}

					//fire if if now date is greater than or equal to next date

					if (dtNow >= dtNext)
					{
						return true;
					}
					else
					{
						return false;
					}
					break;

				case clC.SELL_ALERT_EVERY_WEEKDAY: // WeekDay

					dtNext = dtLast;

					//derive next valid date after last date
					dtNext = dtNext.AddMinutes(1);

					while (dtNext.Hour != iHour || dtNext.Minute != iMinute)
					{
						dtNext = dtNext.AddMinutes(1);
						lPeriod = lPeriod + 1;
						if (lPeriod > 1000000)
						{
							//==> raise error
						}
					}

					//fire if  now date is greater than or equal to next date

					if (dtNow >= dtNext)
					{
						if (dtNow.DayOfWeek != DayOfWeek.Saturday && dtNow.DayOfWeek != DayOfWeek.Sunday)
						{
							return true;
						}
						return false;
					}
					else
					{
						return false;
					}
					break;

				case clC.SELL_ALERT_EVERY_MONTH: // Month

					dtNext = dtLast;

					//derive next valid date after last date
					dtNext = dtNext.AddMinutes(1);

					while (dtNext.Hour != iHour || dtNext.Minute != iMinute || dtNext.Day != iDate)
					{
						dtNext = dtNext.AddMinutes(1);
						lPeriod = lPeriod + 1;
						if (lPeriod > 1000000)
						{
							//==> raise error
						}
					}

					//fire if now date is greater than or equal to next date

					if (dtNow >= dtNext)
					{
						return true;
					}
					else
					{
						return false;
					}
					break;

			}

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}


		public void Initialize()
		{

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			goDef = (Selltis.BusinessLogic.clDefaults)HttpContext.Current.Session["goDef"];
			goScr = (Selltis.BusinessLogic.clScrMngRowSet)HttpContext.Current.Session["goScr"];

		}

		public string TestAndRunTimers()
		{
			//MI 3/4/09 Removed .net warning by moving Return below End Try.

			// Try

			string sSelect = "Select DISTINCT" + "\r\n" + "GID_Section as 'Section'," + "\r\n" + "TXT_Page as 'Page'," + "\r\n" + "TXT_Property as 'Property'," + "\r\n" + "TXT_Language as 'Language'," + "\r\n" + "TXT_Value as 'Value'" + "\r\n" + "FROM MD" + "\r\n" + "WHERE" + "\r\n" + "TXT_Page LIKE 'AGE_%'" + "\r\n" + "and TXT_Property = 'Event'" + "\r\n" + "and TXT_Value = 'Timer'" + "\r\n" + "ORDER BY GID_Section, TXT_Page, TXT_Property, TXT_Language, TXT_Value";

				DataTable dtAge = new DataTable();
				int i = 0;
				string sAgePage = "";
				string sLog = "";



				System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader reader = null;
				Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();
				DateTime dtNow = goTR.NowServer(); //*** MI 10/1/07
				bool bFired = false;

				dtNow = dtNow.AddMilliseconds(-dtNow.Millisecond);
				dtNow = dtNow.AddSeconds(-dtNow.Second);


			//   Try
			cmd.CommandText = sSelect;
					cmd.CommandType = CommandType.Text;
					cmd.Connection = sqlConnection1;

					reader = cmd.ExecuteReader();

					if (reader.HasRows)
					{
						dtAge.Load(reader);
					}
					else
					{
						//==> raise error
					}

					reader.Close();
					sqlConnection1.Close();

			//Catch ex As Exception

			//    '==> error handle
			//End Try

			if (dtAge.Rows.Count > 0)
			{


					sLog = "Server time: " + dtNow.ToString() + "[cr]";
					for (i = 0; i < dtAge.Rows.Count; i++)
					{
						sAgePage = goMeta.PageRead("GLOBAL", dtAge.Rows[i]["Page"].ToString());

						if (goTR.StrRead(sAgePage, "ACTIVE") == "1")
						{
							if (TestTimerTrigger(dtAge.Rows[i]["Page"].ToString(), sAgePage, dtNow) == true)
							{
								//==> Execute automator
								goMeta.LineWrite("GLOBAL", "PTA_" + goTR.FromTo(dtAge.Rows[i]["Page"].ToString(), 5), "INPROGRESS", 1);
								goMeta.LineWrite("GLOBAL", "PTA_" + goTR.FromTo(dtAge.Rows[i]["Page"].ToString(), 5), "LASTSTARTTIME", goTR.DateTimeToString(dtNow));
								object tempVar = null;
								if (goScr.RunScript(goTR.StrRead(sAgePage, "A_01_EXECUTE"), ref tempVar, null, goTR.StrRead(sAgePage, "PARAMS", "")) == false)
								{
									sLog = sLog + "[cr]" + goTR.StrRead(sAgePage, "US_NAME") + "  [False]";
									goLog.Log("clAuto::TestAndRunTimers", "Timer Automator script returned FALSE: " + goTR.StrRead(sAgePage, "A_01_EXECUTE"), 0, false, false, 0, 20);
								}
								else
								{
									goMeta.LineWrite("GLOBAL", "PTA_" + goTR.FromTo(dtAge.Rows[i]["Page"].ToString(), 5), "LASTDATE", goTR.DateTimeToString(dtNow));
									//==> Compile and write log
									sLog = sLog + "[cr]" + goTR.StrRead(sAgePage, "US_NAME") + "  [True]";
								}
								//goMeta.LineWrite("GLOBAL", "PTA_" & goTR.FromTo(dtAge.Rows(i).Item("Page").ToString, 5), "LASTSTARTTIME", "")
								goMeta.LineWrite("GLOBAL", "PTA_" + goTR.FromTo(dtAge.Rows[i]["Page"].ToString(), 5), "INPROGRESS", 0);
								bFired = true;
							}
						}
					}
					if (bFired == true)
					{
						return sLog;
					}
					else
					{
						return "None triggered";
					}

				}

			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, "clAuto::TestAndRunTimers")
			//    End If

			//End Try

			return "None triggered";


		}




		public clAuto()
		{
			Initialize();
		}
	}

}
