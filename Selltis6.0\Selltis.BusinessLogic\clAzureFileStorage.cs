﻿using System;
using Microsoft.WindowsAzure.Storage.File;
using Microsoft.WindowsAzure.Storage;
using Microsoft.Azure;
using Microsoft.WindowsAzure.StorageClient;
using System.Web;
using System.IO;


namespace Selltis.BusinessLogic
{
	public sealed class clAzureFileStorage
	{

		//Dim goErr As clError

		//Public Sub New()

		//    'goP = HttpContext.Current.Session("goP")
		//    'goTR = HttpContext.Current.Session("goTr")
		//    'goMeta = HttpContext.Current.Session("goMeta")
		//    'goData = HttpContext.Current.Session("goData")
		//    goErr = HttpContext.Current.Session("goErr")
		//    'goLog = HttpContext.Current.Session("goLog")
		//    'goScr = HttpContext.Current.Session("goScr")

		//End Sub

		public static CloudFileDirectory GetRootDirectory()
		{

			//  Try
			CloudStorageAccount storageAccount = CloudStorageAccount.Parse(CloudConfigurationManager.GetSetting("StorageConnectionString"));
				CloudFileClient fileClient = storageAccount.CreateCloudFileClient();
				// Get a reference to the file share we created previously.
				CloudFileShare share = fileClient.GetShareReference("attachments");

				//' WriteLog("Getting root directory")

				// Ensure that the share exists.
				if (share.Exists())
				{

					//Dim policyName As String = "sampleSharePolicy" + DateTime.UtcNow.Ticks.ToString()

					//' Create a new shared access policy and define its constraints.
					//Dim sharedPolicy As New SharedAccessFilePolicy()
					//sharedPolicy.SharedAccessExpiryTime = DateTime.UtcNow.AddHours(24)
					//sharedPolicy.Permissions = SharedAccessFilePermissions.Read Or SharedAccessFilePermissions.Write

					//' Get existing permissions for the share.
					//Dim permissions As FileSharePermissions = share.GetPermissions()

					//' Add the shared access policy to the share's policies. Note that each policy must have a unique name.
					//permissions.SharedAccessPolicies.Add(policyName, sharedPolicy)
					//share.SetPermissions(permissions)

					//WriteLog("Shared Policy created successfully")

					// Get a reference to the root directory for the share.
					CloudFileDirectory rootDir = share.GetRootDirectoryReference();

					if (rootDir == null)
					{
						//' WriteLog("Root Directory is nothing")
					}

					return rootDir;
				}
				else
				{
					//' WriteLog("Share does not existed")
				}
			//Catch ex As Exception
			//    ' WriteLog(ex.ToString())
			//End Try

			return null;

		}

		public static CloudFileDirectory CreateDirectory(CloudFileDirectory rootDir, string FolderName, bool isCreateDirectoryIfNotExists = true)
		{

			// Try

			//WriteLog("Creating Directory --> " + FolderName)
			CloudFileDirectory sampleDir = rootDir.GetDirectoryReference(FolderName);
				if (sampleDir.Exists() == false && isCreateDirectoryIfNotExists == true)
				{
					//if not exists create the directory
					//'WriteLog("Directory Creating --> " + FolderName)

					sampleDir.Create();

					//' WriteLog("Directory Created --> " + FolderName)
					//  WriteLog("Directory Created --> " + FolderName)
				}
				return sampleDir;
			//Catch ex As Exception
			//    '' WriteLog("Error in create directory --> " + FolderName + " --> " + ex.Message)
			//End Try

		}

		//Public Shared Sub UploadFile(FolderPath As CloudFileDirectory, LocalfilePath As String)
		//    Dim fileName As String = System.IO.Path.GetFileName(LocalfilePath)
		//    Dim cloudFile As CloudFile = FolderPath.GetFileReference(fileName)
		//    cloudFile.UploadFromFile(LocalfilePath)
		//End Sub

		public static void UploadFromStream(CloudFileDirectory FolderPath, System.IO.Stream _stream, string FileName)
		{

			// Try
			CloudFile cloudFile = FolderPath.GetFileReference(FileName);
				cloudFile.UploadFromStream(_stream);
			//Catch ex As Exception
			//    '  WriteLog(ex.ToString())
			//End Try

		}

		public static void UploadFromStream(string sViewName, string sGidId, string sFieldName, string sTempId, bool isTempPath, System.IO.Stream _stream, string FileName)
		{

            // Try
            CloudFileDirectory FinalFolder = null;
				if (isTempPath)
				{
					FinalFolder = GetFinalFolder("", "", "", sTempId, true);
				}
				else
				{
					FinalFolder = GetFinalFolder(sViewName, sGidId, sFieldName, "", false);
				}
				CloudFile cloudFile = FinalFolder.GetFileReference(FileName);
				cloudFile.UploadFromStream(_stream);
			//Catch ex As Exception
			//    '  WriteLog(ex.ToString())
			//End Try

		}

		public static string CopyFile(CloudFileDirectory SourceFolderPath, CloudFileDirectory DestinationFolderPath, string FileName)
		{

			// Try
			CloudFile sourceFile = SourceFolderPath.GetFileReference(FileName);
				CloudFile destFile = DestinationFolderPath.GetFileReference(FileName);
				string sReturn = destFile.StartCopy(sourceFile);
				return sReturn;
			//Catch ex As Exception
			//    '  WriteLog(ex.ToString())
			//End Try

		}

		public static System.IO.MemoryStream DownloadFileToStream(CloudFileDirectory FolderPath, string FileName)
		{

			CloudFile sFile = FolderPath.GetFileReference(FileName);
			System.IO.MemoryStream tStream = new System.IO.MemoryStream();
			sFile.DownloadToStream(tStream);
			return tStream;

		}

		public static System.IO.MemoryStream DownloadFileToStream(string sViewName, string sGidId, string sFieldName, string sTempId, bool isTempPath, string FileName)
		{
			CloudFileDirectory FinalFolder = null;
			if (isTempPath)
			{
				FinalFolder = GetFinalFolder("", "", "", sTempId, true);
			}
			else
			{
				FinalFolder = GetFinalFolder(sViewName, sGidId, sFieldName, "", false);
			}
			CloudFile sFile = FinalFolder.GetFileReference(FileName);
			System.IO.MemoryStream tStream = new System.IO.MemoryStream();
			sFile.DownloadToStream(tStream);
			return tStream;

		}

		public static byte[] GetFile(string sViewName, string sGidId, string sFieldName, string sFileName)
		{

			var FileFolder = GetFinalFolder(sViewName, sGidId, sFieldName, "", false);
			System.IO.MemoryStream ms = DownloadFileToStream(FileFolder, sFileName);

			if (ms != null)
			{
				return ms.ToArray();
			}

			return null;

		}

		//Public Shared Function DownloadFileToByteArray(FolderPath As CloudFileDirectory, FileName As String) As Byte()

		//    Dim sFile As CloudFile = FolderPath.GetFileReference(FileName)
		//    Dim sTarget() As Byte
		//    sFile.DownloadToByteArray(sTarget, 0)
		//    Return sTarget

		//End Function

		public static bool DeleteFile(CloudFileDirectory FolderPath, string FileName)
		{

			CloudFile sFile = FolderPath.GetFileReference(FileName);
			return sFile.DeleteIfExists();

		}

		public static bool DeleteFile(string sViewName, string sGidId, string sFieldName, string sTempId, bool isTempPath, string FileName)
		{
			CloudFileDirectory FinalFolder = null;
			if (isTempPath)
			{
				FinalFolder = GetFinalFolder("", "", "", sTempId, true);
			}
			else
			{
				FinalFolder = GetFinalFolder(sViewName, sGidId, sFieldName, "", false);
			}
			CloudFile sFile = FinalFolder.GetFileReference(FileName);
			return sFile.DeleteIfExists();

		}

		//Public Shared Function DeleteFolder(FolderPath As CloudFileDirectory) As Boolean

		//    Return FolderPath.DeleteIfExists()

		//End Function

		public static CloudFileDirectory GetFinalFolder(string sViewName, string sGidId, string sFieldName, string sTempId, bool isTempPath, string hostName = "")
		{

			var rootDir = GetRootDirectory();
			object host = null;
			if (hostName == "")
			{
				host = clSettings.GetHostName();
			}
			else
			{
				host = hostName;
			}
			var HostDir = CreateDirectory(rootDir, Convert.ToString(host));

			if (HostDir != null)
			{
				if (isTempPath)
				{
					var TempFileDir = CreateDirectory(HostDir, "TempFiles");
					var TempDir = CreateDirectory(TempFileDir, sTempId); //sPath is the Temp Id
					return TempDir;
				}
				else
				{

					var viewDir = CreateDirectory(HostDir, sViewName);
					var gidIdDir = CreateDirectory(viewDir, sGidId);
					var fiedNameDir = CreateDirectory(gidIdDir, sFieldName);

					return fiedNameDir;

				}
			}

			return null;

		}

		public static bool IsFileExists(CloudFileDirectory FolderName, string FileName)
		{
			CloudFile sFile = FolderName.GetFileReference(FileName);
			return sFile.Exists();
		}

		public static bool IsFileExists(string sViewName, string sGidId, string sFieldName, string sTempId, bool isTempPath, string FileName)
		{
			CloudFileDirectory FinalFolder = null;
			if (isTempPath)
			{
				FinalFolder = GetFinalFolder("", "", "", sTempId, true);
			}
			else
			{
				FinalFolder = GetFinalFolder(sViewName, sGidId, sFieldName, "", false);
			}
			CloudFile sFile = FinalFolder.GetFileReference(FileName);
			return sFile.Exists();
		}

		public static int GetFileSize(CloudFileDirectory FolderName, string FileName)
		{
			CloudFile sFile = FolderName.GetFileReference(FileName);
			if (sFile.Exists())
			{
				return DownloadFileToStream(FolderName, FileName).ToArray().Length;
			}
			return 0;
		}


		public static void WriteLog(string data)
		{
			// Return
			//Dim goP As clProject = HttpContext.Current.Session("goP")
			//Dim sName As String = goP.GetMe("Name")
			string _DestinationPath = HttpContext.Current.Server.MapPath(HttpContext.Current.Request.ApplicationPath) + "AzureFileStorageLog.txt";
			StreamWriter str = new StreamWriter(_DestinationPath, true);
			str.WriteLine(DateTime.Now.ToString() + " : " + data);
			str.Flush();
			str.Close();
		}

		public static string Upload_File_To_Blob(System.IO.Stream fStream, string sFileName, string sContentType, string sContainerName)
		{
			CloudStorageAccount storageacc = CloudStorageAccount.Parse(CloudConfigurationManager.GetSetting("StorageConnectionString"));
			Microsoft.WindowsAzure.Storage.Blob.CloudBlobClient blobClient = storageacc.CreateCloudBlobClient();
			Microsoft.WindowsAzure.Storage.Blob.CloudBlobContainer container = blobClient.GetContainerReference(sContainerName); //'("editorimages")
			Microsoft.WindowsAzure.Storage.Blob.CloudBlockBlob blockBlob = container.GetBlockBlobReference(sFileName);
			blockBlob.Properties.ContentType = sContentType; //'"image/png"
			blockBlob.UploadFromStream(fStream);
			return blockBlob.Uri.AbsoluteUri;
		}


	}

}
