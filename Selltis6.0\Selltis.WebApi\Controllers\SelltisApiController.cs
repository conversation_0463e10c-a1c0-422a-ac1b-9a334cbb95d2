﻿using Selltis.BusinessLogic;
using Selltis.Core;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web;
using System.Web.Http;
using System.Web.Http.Cors;
using System.EnterpriseServices.CompensatingResourceManager;
using System.Xml.Linq;

namespace Selltis.WebApi.Controllers
{
    //[Authorize]
    [EnableCors(origins: "*", headers: "*", methods: "*")]
    public class SelltisApiController : ApiController
    {
        [HttpPost]
        public HttpResponseMessage GetUsers(string hostName)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;          

            if (!hostName.EndsWith(".selltis.com"))
                hostName += ".selltis.com";

            if (Logon("", hostName) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }
            HttpContext.Current.Session["HostName"] = hostName;
            clInit _clint = new clInit();
            //to-do : the field names may not be same for all the clients so it shouldn't be hardcoded. ex: Business unit
            clRowSet doRS = new clRowSet("US", clC.SELL_READONLY, "CHK_ACTIVEFIELD=1 AND CHK_ONNETWORK=1 AND EML_EMAIL<[]>''", "LNK_PRIMARY_BC,TXT_FULLNAME", "TXT_SXNO,TXT_FULLNAME,TXT_NAMEFIRST,TXT_NAMELAST,TXT_CODE,MLS_TYPE,LNK_RELATED_JF%%TXT_JobFuncName,LNK_RELATED_BU%%TXT_BusinessUnitName,LNK_PRIMARY_BC%%TXT_BUCompanyName,LNK_RELATED_BC%%TXT_BUCompanyName,EML_EMAIL", 5000, "", "", "", "", "", true);
            doRS.ToTable();
            DataTable dtData = doRS.dtTransTable;
            UpdateColumnLabels(ref dtData, "US");
            response = Request.CreateResponse(HttpStatusCode.OK, dtData); 
            return response;
        }       

        private void UpdateColumnLabels(ref DataTable dt, string sFileName)
        {
            clData _godata = (clData)Util.GetInstance("data");
            int iValid = 0;
            foreach (DataColumn _col in dt.Columns)
            {
                _col.ColumnName = _godata.GetFieldFullLabelFromName(sFileName, _col.ColumnName, ref iValid);
            }
        }    

        private bool Logon(string userName, string hostName, string pwd = "")
        {
            string sHostName = (hostName == "localhost" || string.IsNullOrEmpty(hostName)) ? "default" : hostName;
            //if (string.IsNullOrEmpty(userName))
            //    userName = "system";
            //Load Settings
            DataSet ds = new DataSet();
            string myXMLfile = System.Configuration.ConfigurationManager.AppSettings["CustomFilesPath"].ToString() + sHostName + "/SiteSettings.xml";
            FileStream fsReadXml = new FileStream(myXMLfile, FileMode.Open, FileAccess.Read);
            ds.ReadXml(fsReadXml);
            fsReadXml.Close();
            fsReadXml.Dispose();
            HttpContext.Current.Session[hostName + "_SiteSettings"] = ds.Tables[0];

            //Login Process 
            string connStr = ds.Tables[0].Rows[0]["ConnectionString"].ToString();
            HttpContext.Current.Session[sHostName + "_" + "ConnString"] = connStr;
            SqlConnection sqlConnection = new SqlConnection(connStr);
            if (sqlConnection.State == ConnectionState.Closed)
                sqlConnection.Open();
            string sql = string.Empty;

            string IsSSoEnabled = "false";
            try
            {
                if (ds.Tables[0].Columns.Contains("UseSSO"))
                {
                    IsSSoEnabled = ds.Tables[0].Rows[0]["UseSSO"].ToString();
                }
                else
                {
                    IsSSoEnabled = "false";
                }
            }
            catch
            {
            }

            if (string.IsNullOrEmpty(userName))
            {
                userName = IsSSoEnabled == "true" ? "<EMAIL>" : "system";
            }              

            if (string.IsNullOrEmpty(pwd))
            {
                if (IsSSoEnabled == "true")
                {
                    sql = "Select b.* from US a Join XU b on b.GID_UserID = a.GID_ID WHERE a.CHK_ActiveField=1 AND cast(a.EML_Email as nvarchar(250))='" + userName + "'";
                }
                else if (IsSSoEnabled == "false")
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "'";
                }
                else
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "'";
                }

            }
            else
            {
                if (IsSSoEnabled == "true")
                {
                    sql = "Select b.* from US a Join XU b on b.GID_UserID = a.GID_ID WHERE a.CHK_ActiveField=1 AND cast(a.EML_Email as nvarchar(250))='" + pwd + "'";
                }
                else if (IsSSoEnabled == "false")
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "'  and [TXT_Password]='" + pwd + "'";
                }
                else
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "' and [TXT_Password]='" + pwd + "'";
                }
            }

            SqlCommand comm = new SqlCommand(sql, sqlConnection);
            //comm.Parameters.AddWithValue("@username", userName);
            //comm.Parameters.AddWithValue("@pwd", pwd);

            SqlDataReader reader = comm.ExecuteReader();
            if (reader.HasRows)
            {
                DataTable dt = new DataTable();
                dt.Load(reader);
                if (dt.Rows.Count == 0)
                {
                    return false;
                }
                else
                {

                    HttpContext.Current.Session["USERID"] = dt.Rows[0]["GID_UserID"].ToString();
                    HttpContext.Current.Session["LOGINID"] = dt.Rows[0]["GID_ID"].ToString();
                    HttpContext.Current.Session[sHostName + "_" + "LOGINNAME"] = dt.Rows[0]["TXT_LogonName"].ToString();
                    return true;
                }
            }
            else
                return false;


        }
    }
}
