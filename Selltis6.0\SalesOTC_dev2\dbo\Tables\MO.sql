﻿CREATE TABLE [dbo].[MO] (
    [GID_ID]                  UNIQUEIDENTIFIER CONSTRAINT [DF_MO_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'MO',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                  BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                NVARCHAR (150)   NULL,
    [CHK_ActiveField]         TINYINT          NULL,
    [FIL_Attachments]         NTEXT            NULL,
    [TXT_Attribute1]          VARCHAR (4)      NULL,
    [TXT_Attribute2]          VARCHAR (4)      NULL,
    [MMO_CompetitionNotes]    NTEXT            NULL,
    [CUR_Cost]                MONEY            NULL,
    [DTT_CreationTime]        DATETIME         CONSTRAINT [DF_MO_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [MLS_Currency]            SMALLINT         NULL,
    [CHK_DemoData]            TINYINT          NULL,
    [TXT_Description]         NVARCHAR (80)    NULL,
    [SR__DiscFromRetail]      REAL             NULL,
    [SR__ExchRate]            REAL             NULL,
    [FIL_Inclusions]          NTEXT            NULL,
    [TXT_ManufCode]           NVARCHAR (20)    NULL,
    [SR__Margin]              REAL             NULL,
    [TXT_ModelName]           NVARCHAR (150)   NULL,
    [MMO_Notes]               NTEXT            NULL,
    [CHK_Obsolete]            TINYINT          NULL,
    [TXT_OrigCreatedBy]       VARCHAR (4)      NULL,
    [DTT_OrigCreatedTime]     DATETIME         NULL,
    [FIL_Picture]             NTEXT            NULL,
    [CUR_Price]               MONEY            NULL,
    [CUR_PricePurch]          MONEY            NULL,
    [CUR_PriceRetail]         MONEY            NULL,
    [SR__RetailDisc]          REAL             NULL,
    [LI__SKUNo]               INT              NULL,
    [MMO_Specifications]      NTEXT            NULL,
    [CHK_Taxable]             TINYINT          NULL,
    [TXT_UnitText]            NVARCHAR (150)   NULL,
    [URL_URLs]                NTEXT            NULL,
    [SR__Weight]              REAL             NULL,
    [TXT_ModBy]               VARCHAR (4)      NULL,
    [DTT_ModTime]             DATETIME         CONSTRAINT [DF_MO_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [MMO_ImportData]          NTEXT            NULL,
    [SI__ShareState]          TINYINT          CONSTRAINT [DF_MO_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]        UNIQUEIDENTIFIER NULL,
    [GID_Of_PD]               UNIQUEIDENTIFIER NULL,
    [GID_ComponentOf_MO]      UNIQUEIDENTIFIER NULL,
    [GID_Related_VE]          UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]            VARCHAR (50)     NULL,
    [TXT_ExternalID]          NVARCHAR (120)   NULL,
    [TXT_ExternalSource]      VARCHAR (10)     NULL,
    [TXT_ImpJobID]            VARCHAR (20)     NULL,
    [ADR_Attachments]         NTEXT            NULL,
    [ADR_Inclusions]          NTEXT            NULL,
    [MLS_Size]                SMALLINT         NULL,
    [MLS_ValveType]           SMALLINT         NULL,
    [MLS_MatofConst]          SMALLINT         NULL,
    [MLS_Valverating]         SMALLINT         NULL,
    [CHK_OTC]                 TINYINT          NULL,
    [MLS_LEGACYDATASOURCE]    SMALLINT         NULL,
    [GID_JCIID]               UNIQUEIDENTIFIER NULL,
    [TXT_VendorProductNumber] NVARCHAR (250)   NULL,
    [TXT_Descrip2]            NVARCHAR (100)   NULL,
    [TXT_Descrip3]            NVARCHAR (300)   NULL,
    [TXT_HP]                  NVARCHAR (300)   NULL,
    CONSTRAINT [PK_MO] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_MO_ComponentOf_MO] FOREIGN KEY ([GID_ComponentOf_MO]) REFERENCES [dbo].[MO] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_MO_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_MO_Of_PD] FOREIGN KEY ([GID_Of_PD]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_MO_Related_VE] FOREIGN KEY ([GID_Related_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MO] NOCHECK CONSTRAINT [LNK_MO_ComponentOf_MO];


GO
ALTER TABLE [dbo].[MO] NOCHECK CONSTRAINT [LNK_MO_CreatedBy_US];


GO
ALTER TABLE [dbo].[MO] NOCHECK CONSTRAINT [LNK_MO_Of_PD];


GO
ALTER TABLE [dbo].[MO] NOCHECK CONSTRAINT [LNK_MO_Related_VE];


GO
CREATE NONCLUSTERED INDEX [IX_MO_ModDateTime]
    ON [dbo].[MO]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MO_CreatedBy_US]
    ON [dbo].[MO]([GID_CreatedBy_US] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_MO_BI__ID]
    ON [dbo].[MO]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MO_Related_VE]
    ON [dbo].[MO]([GID_Related_VE] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MO_Name]
    ON [dbo].[MO]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MO_ModelName]
    ON [dbo].[MO]([TXT_ModelName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MO_ComponentOf_MO]
    ON [dbo].[MO]([GID_ComponentOf_MO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MO_CreationTime]
    ON [dbo].[MO]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MO_Of_PD]
    ON [dbo].[MO]([GID_Of_PD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MO_TXT_ImportID]
    ON [dbo].[MO]([TXT_ImportID] ASC);


GO

CREATE Trigger [trMOCalcModelName] ON MO
For Insert, Update
as
Begin
	
	declare @GID_ID Uniqueidentifier

	Declare @MLSSize smallint
	Declare @MLSValveType smallint
	Declare @MLSMatofConst smallint
	Declare @MLSValverating smallint
	
	Declare @TXTModel Nvarchar(60) 
	Declare @TXTSize nvarchar(3)
	Declare @TXTValveType nvarchar(2)
	Declare @TXTMatofConst nvarchar(2)
	Declare @TXTValverating nvarchar(2)

	Select	 @GID_ID = GID_ID
			,@MLSSize = MLS_Size
			,@MLSValveType = MLS_ValveType
			,@MLSMatofConst = MLS_MatofConst
			,@MLSValverating = MLS_Valverating
		From inserted

	IF (ISNULL(@MLSSize, 0) > 0)
	BEGIN
		Set @TXTSize = LEFT(dbo.fnMetaLineRead(NULL, 'LST_MO:SIZE', ISNULL(@MLSSize, 0), '', 0, 'US', 'XX'), 3)
	END

	IF (ISNULL(@MLSValveType, 0) > 0)
	BEGIN
		Set @TXTValveType = LEFT(dbo.fnMetaLineRead(NULL, 'LST_MO:VALVETYPE', ISNULL(@MLSValveType, 0), '', 0, 'US', 'XX'), 2)
	END

	IF (ISNULL(@MLSMatofConst, 0) > 0)
	BEGIN
		Set @TXTMatofConst = LEFT(dbo.fnMetaLineRead(NULL, 'LST_MO:MATOFCONST', ISNULL(@MLSMatofConst, 0), '', 0, 'US', 'XX'), 2)
	END

	IF (ISNULL(@MLSValverating, 0) > 0)
	BEGIN
		Set @TXTValverating = LEFT(dbo.fnMetaLineRead(NULL, 'LST_MO:VALVERATING', ISNULL(@MLSValverating, 0), '', 0, 'US', 'XX'), 2)
	END

	Set @TXTModel = ISNULL(@TXTSize, '') + ISNULL(@TXTValveType, '') + ISNULL(@TXTMatofConst, '') + ISNULL(@TXTValverating, '')

	If (ISNULL(@TXTModel, '') <> '')
	BEGIN
		Update MO Set TXT_ModelName = @TXTModel, SYS_NAME = @TXTModel
		Where GID_ID = @GID_ID
	END

End

GO
DISABLE TRIGGER [dbo].[trMOCalcModelName]
    ON [dbo].[MO];


GO
CREATE TRIGGER trMOUpdateTN
ON [MO]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in MO table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(120)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'MO'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable
					and tn2.GID_InternalID = inserted.GID_ID)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [MO]
			SET [MO].TXT_ExternalSource = '', [MO].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [MO].GID_ID = in1.GID_ID
				and ISNULL([MO].TXT_ExternalSource, '') <> ''
				and ISNULL([MO].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trMOUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trMOUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trMOUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!