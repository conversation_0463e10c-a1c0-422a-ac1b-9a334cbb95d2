'OWNER: WT

Imports Microsoft.VisualBasic
Imports System.IO
Imports System.Runtime.Serialization.Formatters.Binary
Imports System.Reflection
Imports System.Web

Public Class clUtil

    Private goP As clProject
    Private goErr As clError
    Private goData As clData
    Private goTr As clTransform
    Private goMeta As clMetaData

    Private Sub Initialize()
        Dim sProc As String = "clForm:Initialize"
        'Try
        goP = HttpContext.Current.Session("goP")
            goErr = HttpContext.Current.Session("goErr")
            goData = HttpContext.Current.Session("goData")
            goTr = HttpContext.Current.Session("goTR")
            goMeta = HttpContext.Current.Session("goMeta")
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Public Function CreateCustomClass(baseClass As String, derivedClass As String, args() As Object) As Object

        Dim oReturn As Object = Nothing
        Dim bDerivedClass As Boolean = False

        Dim t As Type = GetType(clSelltisMobileBase)
        Dim s As String = t.Assembly.FullName.ToUpper()
        Try
            'first preference to custom class .i.e derivedClass
            oReturn = Assembly.Load(s).CreateInstance(derivedClass, True, BindingFlags.Default, Nothing, args, Nothing, Nothing)
            bDerivedClass = True
        Catch
            'custom failed, create base now
            bDerivedClass = False
        End Try

        If oReturn Is Nothing Then
            'custom failed, create base now
            bDerivedClass = False
        End If

        If bDerivedClass = False Then
            oReturn = Assembly.Load(s).CreateInstance(baseClass, True, BindingFlags.Default, Nothing, args, Nothing, Nothing)
        End If

        Return oReturn
    End Function


    Public Function ConvertToSellDate(ByVal dtDate As DateTime, ByVal sDorT As String) As String
        Dim sProc As String = "clTransform::ConvertToSellDate"
        Try
            Dim sResult As String = ""
            sResult = Format(Convert.ToDateTime(dtDate), "yyyy-MM-dd HH:mm:ss")
            If sDorT = "Date" Then
                sResult = Mid(sResult, 1, 10)
            ElseIf sDorT = "Time" Then
                sResult = Right(sResult, 8)
            Else
                sResult = Mid(sResult, 1, 10) & "|" & Right(sResult, 8)
            End If
            Return sResult
        Catch ex As Exception
            'goLog.LogActivity(ex.Message & vbCrLf & ex.StackTrace, 1)
            Return dtDate.ToString
        End Try
    End Function

    Public Function BuildString(ByVal sReturnString As String, ByVal sString As String, ByVal sDelimiter As String) As String
        'PURPOSE:
        '		builds a delimited string
        'PARAMETERS:
        '		sReturnString:              the string to start with
        '		sString:                    the string to append
        '		sDelimiter:                 the delimiter between start string and appended string
        'RETURNS:
        '		new string
        'AUTHOR: WT
        Dim sProc As String = "clUtil::BuildString"
        Try
            Select Case sReturnString
                Case ""
                    sReturnString = sString
                Case Else
                    sReturnString = sReturnString & sDelimiter & sString
            End Select
            Return sReturnString
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return ""
        End Try
    End Function

    Public Function GetSysName(ByVal sFile As String, ByVal sID As String) As String
        'MI 11/10/09 Added sFields parameter to clRowset instantiation.
        'PURPOSE:
        '		builds a toprec string for a specific record in a specific sort
        'PARAMETERS:
        '		sTable:             file that record is in
        '		sSort:              sort to build toprec on
        '		sGID:               gid_id of record
        'RETURNS:
        '		toprec string
        'AUTHOR: WT
        Dim sProc As String = "clUtil::GetTopRec"
        Try
            Dim oRS As clRowSet
            oRS = New clRowSet(sFile, clC.SELL_READONLY, "GID_ID='" & sID & "'", "GID_ID", "SYS_Name")    'MI 11/10/09 added SYS_Name in list of fields
            If oRS.GetFirst = 1 Then
                Return oRS.GetFieldVal("SYS_NAME")
            Else
                Return ""
            End If
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return ""
        End Try
    End Function

    Public Function GetTopRec(ByVal sTable As String, ByVal sSort As String, ByVal sGID As String) As String
        'MI 7/12/10 Changed returning clC.SELL_BLANK_DATETIME in descending DTx cases to the format with | between date and time.
        'MI 11/12/09 Added calculating end of the range values for date grouping fields in desc order.
        'MI 11/10/09 Added If sSort = "" Then sSort = "SYS_Name" to ensure a field will be in sFields parameter of new clRowset.
        'PURPOSE:
        '		builds a toprec string for a specific record in a specific sort
        'PARAMETERS:
        '		sTable:             file that record is in
        '		sSort:              sort to build toprec on
        '		sGID:               gid_id of record
        'RETURNS:
        '		toprec string
        'AUTHOR: WT
        Dim sProc As String = "clUtil::GetTopRec"
        Try
            Dim oRS As clRowSet
            Dim sField As String
            Dim sTopRec As String = ""
            Dim i As Integer
            Dim cSort As Collection = goData.GetFilterSortFields("SORT=" & sSort)
            Dim sFieldByItself As String
            Dim sDirection As String
            Dim dtDate As DateTime
            Dim sDate As String
            sSort = ""
            For i = 1 To cSort.Count
                sField = goTr.ExtractString(cSort.Item(i), 1, "|")
                If goData.IsFieldValid(sTable, sField) Then
                    sSort = BuildString(sSort, sField, ", ")
                End If
            Next
            'MI 11/10/09 added
            If sSort = "" Then sSort = "SYS_Name"
            oRS = New clRowSet(sTable, clC.SELL_READONLY, "GID_ID='" & sGID & "'", , sSort)
            If oRS.GetFirst() = 1 Then
                For i = 1 To cSort.Count
                    sField = goTr.ExtractString(cSort.Item(i), 1, "|")
                    If goData.IsFieldValid(sTable, sField) Then
                        'MI 11/12/09 Added considering direction to calculate the end of the 
                        'period value for DTx (date grouping) fields
                        sDirection = goTr.ExtractString(cSort.Item(i), 2, "|")  'Returns ASC or DESC
                        If Left(UCase(sDirection), 1) = "A" Then
                            'In ascending direction, DTx field values are the starting values and no calculation is needed
                            goTr.StrWrite(sTopRec, "TOPREC_" & sField, goTr.PrepareForSQL(goTr.DelimitFilterConditionValue(sField, oRS.GetFieldVal(sField, clC.SELL_SQLSTRING))))
                        Else
                            'For descending date grouping fields, calculate the end of the date range value
                            If InStr(sField, "%%") > 0 Then
                                sFieldByItself = goTr.GetFieldPartFromLinkName(sField)
                            Else
                                sFieldByItself = sField
                            End If
                            'Calculate the end time of the year, quarter, month, day period
                            Select Case goTr.GetPrefix(sFieldByItself)
                                Case "DTY_"
                                    dtDate = oRS.GetFieldVal(sField, clC.SELL_SYSTEM)
                                    If dtDate <= clC.SELL_BLANK_DTDATETIME Then
                                        sDate = clC.SELL_BLANK_SYSDATETIME 'MI 7/12/10 Changed from clC.SELL_BLANK_DATETIME
                                    Else
                                        sDate = dtDate.Year.ToString & "-12-31|23:59:59.996"
                                    End If
                                    goTr.StrWrite(sTopRec, "TOPREC_" & sField, goTr.PrepareForSQL(goTr.DelimitFilterConditionValue(sField, sDate)))
                                Case "DTQ_"
                                    dtDate = oRS.GetFieldVal(sField, clC.SELL_SYSTEM)
                                    If dtDate <= clC.SELL_BLANK_DTDATETIME Then
                                        sDate = clC.SELL_BLANK_SYSDATETIME 'MI 7/12/10 Changed from clC.SELL_BLANK_DATETIME
                                    Else
                                        dtDate = goTr.GetQuarterDate(dtDate)
                                        dtDate = dtDate.AddMonths(3)
                                        dtDate = dtDate.AddMilliseconds(-4)
                                        sDate = goTr.DateTimeToSysString(dtDate)
                                    End If
                                    goTr.StrWrite(sTopRec, "TOPREC_" & sField, goTr.PrepareForSQL(goTr.DelimitFilterConditionValue(sField, sDate)))
                                Case "DTM_"
                                    dtDate = oRS.GetFieldVal(sField, clC.SELL_SYSTEM)
                                    If dtDate <= clC.SELL_BLANK_DTDATETIME Then
                                        sDate = clC.SELL_BLANK_SYSDATETIME 'MI 7/12/10 Changed from clC.SELL_BLANK_DATETIME
                                    Else
                                        'Get first of next month
                                        sDate = goTr.Pad((dtDate.Month + 1).ToString, 2, "0", "L")
                                        sDate = dtDate.Year.ToString & "-" & sDate & "-01"
                                        dtDate = goTr.StringToDate(sDate, clC.SELL_SYSTEMFORMAT)
                                        'Get 4 milliseconds back into the previous day
                                        dtDate = dtDate.AddMilliseconds(-4)
                                        sDate = goTr.DateTimeToSysString(dtDate)
                                    End If
                                    goTr.StrWrite(sTopRec, "TOPREC_" & sField, goTr.PrepareForSQL(goTr.DelimitFilterConditionValue(sField, sDate)))
                                Case "DTD_"
                                    dtDate = oRS.GetFieldVal(sField, clC.SELL_SYSTEM)
                                    If dtDate <= clC.SELL_BLANK_DTDATETIME Then
                                        sDate = clC.SELL_BLANK_SYSDATETIME  'MI 7/12/10 Changed from clC.SELL_BLANK_DATETIME
                                    Else
                                        sDate = goTr.DateToString(dtDate, clC.SELL_SYSTEMFORMAT)
                                        sDate &= "|23:59:59.996"
                                    End If
                                    goTr.StrWrite(sTopRec, "TOPREC_" & sField, goTr.PrepareForSQL(goTr.DelimitFilterConditionValue(sField, sDate)))
                                Case Else
                                    goTr.StrWrite(sTopRec, "TOPREC_" & sField, goTr.PrepareForSQL(goTr.DelimitFilterConditionValue(sField, oRS.GetFieldVal(sField, clC.SELL_SQLSTRING))))
                            End Select
                        End If
                    End If
                Next
            End If
            Return sTopRec
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return ""
        End Try
    End Function

    Public Sub RemoveAlert(ByVal sType As String, ByVal sID As String)
        'MI 3/17/09 clRowset.New: added true in par_bGetAllUsersUnsharedRecs, par_bSysFileFullPermissions params.
        'PURPOSE:
        '		removes an alert based on parameters by deleting the page in md.
        'PARAMETERS:
        '		sType:              type of alert (FORM OR DESKTOP)
        '		sID:                ID of record (for form), or id of desktop
        'RETURNS:
        '		Nothing
        'AUTHOR: WT
        Dim sProc As String = "clUtil::RemoveAlert"
        'Try
        Dim sCondition = "TXT_PAGE[='alt%' AND EXECUTE='" & sID & "' AND TYPE='" & sType & "' AND GID_SECTION = '" & goP.GetUserTID & "'"
            'MI 3/17/09 Added true in par_bGetAllUsersUnsharedRecs, par_bSysFileFullPermissions params 
            'to make the deletion succeed under any login (normally MD can be modified only by authors and admins).
            Dim oRS As New clRowSet("MD", 1, sCondition, "TXT_PAGE", "GID_ID", , , , , , , , , True, , , , , True)
            If oRS.GetFirst Then
                oRS.DeleteRecord()
            End If

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Public Function ReverseSort(ByVal sTable As String, ByVal sSort As String) As String
        Dim sProc As String = "clUtil::ReverseSort"
        Try
            Dim i As Integer
            Dim sField As String
            Dim sOrder As String
            Dim sNewSort As String
            Dim oUtil As New clUtil
            Dim cSort As Collection = goData.GetFilterSortFields("SORT=" & sSort, False)
            sNewSort = ""
            For i = 1 To cSort.Count
                sField = goTr.ExtractString(cSort.Item(i), 1, "|")
                sOrder = goTr.ExtractString(cSort.Item(i), 2, "|")
                If goData.IsFieldValid(sTable, sField) Then
                    Select Case sOrder
                        Case "ASC"
                            sOrder = "DESC"
                        Case "DESC"
                            sOrder = "ASC"
                    End Select
                    sNewSort = oUtil.BuildString(sNewSort, sField & " " & sOrder, ", ")
                Else
                    Return ""
                End If
            Next
            Return sNewSort

        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return ""
        End Try
    End Function

    Public Sub New()
        Initialize()
    End Sub

    Public Function SessionRestartAgainAgain() As Boolean
        Dim a As New Object
        Return False
    End Function

    Public Function IsMobile() As Boolean
        'Try

        Dim sAgent As String = HttpContext.Current.Request.UserAgent.ToLower

            ' Define some test strings.
            'Dim tests() As String = {"iphone", "blackberry", "midp", "j2me", "avant", "docomo", _
            '                            "novarra", "palmos", "palmsource", "240x320", _
            '                            "opwv", "chtml", "pda", "windows ce", _
            '                            "mmp/", "mib/", "symbian", _
            '                            "wireless", "nokia", "hand", "mobi", _
            '                            "phone", "cdm", "up.b", "audio", _
            '                            "SIE-", "SEC-", "samsung", "HTC", _
            '                            "mot-", "mitsu", "sagem", "sony", _
            '                            "alcatel", "lg", "eric", "vx", _
            '                            "NEC", "philips", "mmm", "xx", _
            '                            "panasonic", "sharp", "wap", "sch", _
            '                            "rover", "pocket", "benq", "java", _
            '                            "vox", "amoi", _
            '                            "bird", "compal", "kg", "voda", _
            '                            "sany", "kdd", "dbt", "sendo", _
            '                            "sgh", "gradi", "jb", "dddi", _
            '                            "moto"}

            Dim tests() As String = {"ipad", "iphone", "blackberry", "midp", "j2me", "avant", "docomo", _
                            "novarra", "palmos", "palmsource", "240x320", _
                            "opwv", "chtml", "windows ce", _
                            "mmp/", "mib/", "symbian", _
                            "wireless", "nokia", "hand", "mobi", _
                            "phone", "cdm", "up.b", "audio", _
                            "SIE-", "SEC-", "samsung", "HTC", _
                            "mot-", "mitsu", "sagem", "sony", _
                            "alcatel", "eric", _
                            "NEC", "philips", _
                            "panasonic", "sharp", _
                            "rover", "pocket", "benq", "java", "moto"}

            'user had the following, picked up on "pt"
            'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; GTB5; .NET CLR 2.0.50727; MSN Optimized;US; MSN Optimized;US)
            'user had the following, picked up on "pg"
            '"Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; xss--3- Mi_ATXLD5bBTFzUGAmyrl_8_IXcLakDRazRjHQ38UhApg8viTh_Uo1XcEvuKa3TLAiVykVSokyqcqg3jOgGdoA; SLCC1; .NET CLR 2.0.50727; .NET CLR 3.0.04506; .NET CLR 1.1.4322; InfoPath.2)"
            '"Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; xss--3- Mi_ATXLD5bBTFzUGAmyrl_8_IXcLakDRazRjHQ38UhApg8viTh_Uo1XcEvuKa3TLAiVykVSokyqcqg3jOgGdoA; SLCC1; .net clr 2.0.50727; .NET CLR 3.0.04506; .NET CLR 1.1.4322; InfoPath.2)"
            ' Check each test string against the regular expression.
            For Each test As String In tests
                If InStr(sAgent, test) > 0 Then
                    If test = "ipad" Then Return False
                    Return True
                Else
                    'do nothing, keep looping
                End If
            Next

            Return False

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then

        '    End If
        'End Try
    End Function

    'V_T 2/19/2015
    'Function - strips out all the HTML Tags and return the plaint text from HTML content
    'MMR Change
    Public Function StripHTML(source As String) As String
        Try

            'If source.Length > 20000 Then
            '    Return source
            'End If

            Dim result As String

            ' Remove HTML Development formatting
            ' Replace line breaks with space
            ' because browsers inserts space
            result = source.Replace(vbCr, " ")
            ' Replace line breaks with space
            ' because browsers inserts space
            result = result.Replace(vbLf, " ")
            ' Remove step-formatting
            result = result.Replace(vbTab, String.Empty)
            ' Remove repeating spaces because browsers ignore them
            result = System.Text.RegularExpressions.Regex.Replace(result, "( )+", " ")

            ' Remove the header (prepare first by clearing attributes)
            result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*head([^>])*>", "<head>", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "(<( )*(/)( )*head( )*>)", "</head>", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "(<head>).*(</head>)", String.Empty, System.Text.RegularExpressions.RegexOptions.IgnoreCase)

            ' remove all scripts (prepare first by clearing attributes)
            result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*script([^>])*>", "<script>", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "(<( )*(/)( )*script( )*>)", "</script>", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            'result = System.Text.RegularExpressions.Regex.Replace(result,

            ' string.Empty,
            ' System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            result = System.Text.RegularExpressions.Regex.Replace(result, "(<script>).*(</script>)", String.Empty, System.Text.RegularExpressions.RegexOptions.IgnoreCase)

            ' remove all styles (prepare first by clearing attributes)
            result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*style([^>])*>", "<style>", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "(<( )*(/)( )*style( )*>)", "</style>", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "(<style>).*(</style>)", String.Empty, System.Text.RegularExpressions.RegexOptions.IgnoreCase)

            ' insert tabs in spaces of <td> tags
            result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*td([^>])*>", vbTab, System.Text.RegularExpressions.RegexOptions.IgnoreCase)

            ' insert line breaks in places of <BR> and <LI> tags
            result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*br( )*>", vbCr, System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*li( )*>", vbCr, System.Text.RegularExpressions.RegexOptions.IgnoreCase)

            ' insert line paragraphs (double line breaks) in place
            ' if <P>, <DIV> and <TR> tags
            result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*div([^>])*>", vbCr & vbCr, System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*tr([^>])*>", vbCr & vbCr, System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*p([^>])*>", vbCr & vbCr, System.Text.RegularExpressions.RegexOptions.IgnoreCase)

            ' Remove remaining tags like <a>, links, images,
            ' comments etc - anything that's enclosed inside < >
            result = System.Text.RegularExpressions.Regex.Replace(result, "<[^>]*>", String.Empty, System.Text.RegularExpressions.RegexOptions.IgnoreCase)

            ' replace special characters:
            result = System.Text.RegularExpressions.Regex.Replace(result, " ", " ", System.Text.RegularExpressions.RegexOptions.IgnoreCase)

            result = System.Text.RegularExpressions.Regex.Replace(result, "&bull;", " * ", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "&lsaquo;", "<", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "&rsaquo;", ">", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "&trade;", "(tm)", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "&frasl;", "/", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "&lt;", "<", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "&gt;", ">", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "&copy;", "(c)", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "&reg;", "(r)", System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            ' Remove all others. More can be added, see
            ' http://hotwired.lycos.com/webmonkey/reference/special_characters/
            result = System.Text.RegularExpressions.Regex.Replace(result, "&(.{2,6});", String.Empty, System.Text.RegularExpressions.RegexOptions.IgnoreCase)

            ' for testing
            'System.Text.RegularExpressions.Regex.Replace(result,
            ' this.txtRegex.Text,string.Empty,
            ' System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            ' make line breaking consistent
            result = result.Replace(vbLf, vbCr)

            ' Remove extra line breaks and tabs:
            ' replace over 2 breaks with 2 and over 4 tabs with 4.
            ' Prepare first to remove any whitespaces in between
            ' the escaped characters and remove redundant tabs in between line breaks
            result = System.Text.RegularExpressions.Regex.Replace(result, "(" & vbCr & ")( )+(" & vbCr & ")", vbCr & vbCr, System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "(" & vbTab & ")( )+(" & vbTab & ")", vbTab & vbTab, System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "(" & vbTab & ")( )+(" & vbCr & ")", vbTab & vbCr, System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            result = System.Text.RegularExpressions.Regex.Replace(result, "(" & vbCr & ")( )+(" & vbTab & ")", vbCr & vbTab, System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            ' Remove redundant tabs
            result = System.Text.RegularExpressions.Regex.Replace(result, "(" & vbCr & ")(" & vbTab & ")+(" & vbCr & ")", vbCr & vbCr, System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            ' Remove multiple tabs following a line break with just one tab
            result = System.Text.RegularExpressions.Regex.Replace(result, "(" & vbCr & ")(" & vbTab & ")+", vbCr & vbTab, System.Text.RegularExpressions.RegexOptions.IgnoreCase)
            ' Initial replacement target string for line breaks
            Dim breaks As String = vbCr & vbCr & vbCr
            ' Initial replacement target string for tabs
            Dim tabs As String = vbTab & vbTab & vbTab & vbTab & vbTab
            'For index As Integer = 0 To result.Length - 1
            '    result = result.Replace(breaks, vbCr & vbCr)
            '    result = result.Replace(tabs, vbTab & vbTab & vbTab & vbTab)
            '    breaks = breaks & Convert.ToString(vbCr)
            '    tabs = tabs & Convert.ToString(vbTab)
            'Next

            ' That's it.
            Return result
        Catch
            Return source
        End Try
    End Function

End Class
