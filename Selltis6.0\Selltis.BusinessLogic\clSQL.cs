﻿using System;

//Owner: MI
//MI 3/28/14 Added support for MMR_.
//MI 5/28/13 AddSelectiveFilterToCondition: Excluding sysadmins from sharestate filtering.
//       This was motivated by the need for sysadmins to see private MD records such as alerts (ALT_),
//       but the need has existed for sysadmins to be able to inspect or clean up non-shared records in _all_ files.
//MI 1/8/10 Added support for GROUPBYWITHROLLUP mode.
//MI Created 4/3/09
//PURPOSE:
//       Generation of SQL query string, called from clRowset.
//WARNING:
//       Do not instantiate objects against this class until after 
//       the project has been initialized and all goXxx objects'
//       session variables are available.

using System.Web;
using static System.Math;
using System.Text.RegularExpressions;
using System.Data;


namespace Selltis.BusinessLogic
{
	public class clSQL
	{

		private DataTable dtColumnMap;
		private clTable tColumnMap; //Map of column names in ID table (idt) and real field names used in the main SELECT statement

		internal clProject goP;
		internal clMetaData goMeta;
		internal clTransform goTR;
		internal clData goData;
		internal clError goErr;
		internal clLog goLog;
		internal clDefaults goDef;
		internal clPerm goPerm;

		private string AddGroupByField(string sStatement, string sField, string sGenFieldCalc)
		{
			//MI 12/15/09 Created
			//PARAMETERS:
			//       sWork: SELECT or FIELDS statement string to which to add a field
			//           with the appropriate calculation (aggregation) keyword
			//           appended.
			//       sField: Field name, e.g. 'CUR_Value'.
			//       sGenFieldCalc: String that contains <FieldName>_CALC=SUM, AVG, MIN, ... definition
			//           for sField field.
			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sCalcWord = null;

			//Try
			//Process the implicit BI__GROUPBYCOUNT keyword
			if (sField.ToUpper() == "BI__GROUPBYCOUNT")
			{
				//Add the field to sWork (prepend comma if needed)
				if (sStatement != "")
				{
					sStatement += ", " + sField;
				}
				else
				{
					sStatement = sField;
				}
				goto WeAreDone;
			}

			sCalcWord = goTR.ExtractString(sField, 2, "|");
			if (sCalcWord != "" && sCalcWord[0] != clC.EOT)
			{
				//Calc word is defined in the field definition
				//Add the field to sWork (prepend comma if needed)
				if (sStatement != "")
				{
					sStatement += ", " + sField;
				}
				else
				{
					sStatement = sField;
				}
			}
			else
			{
				//Seek the calc word in sGenFieldCal
				sCalcWord = "SUM";
				if (sGenFieldCalc.IndexOf(sCalcWord) + 1 > 0)
				{
					//Add the field to sWork (prepend comma if needed)
					if (sStatement != "")
					{
						sStatement += ", " + sField + "|" + sCalcWord;
					}
					else
					{
						sStatement = sField + "|" + sCalcWord;
					}
				}
				sCalcWord = "AVG";
				if (sGenFieldCalc.IndexOf(sCalcWord) + 1 > 0)
				{
					//Add the field to sWork (prepend comma if needed)
					if (sStatement != "")
					{
						sStatement += ", " + sField + "|" + sCalcWord;
					}
					else
					{
						sStatement = sField + "|" + sCalcWord;
					}
				}
				sCalcWord = "MIN";
				if (sGenFieldCalc.IndexOf(sCalcWord) + 1 > 0)
				{
					//Add the field to sWork (prepend comma if needed)
					if (sStatement != "")
					{
						sStatement += ", " + sField + "|" + sCalcWord;
					}
					else
					{
						sStatement = sField + "|" + sCalcWord;
					}
				}
				sCalcWord = "MAX";
				if (sGenFieldCalc.IndexOf(sCalcWord) + 1 > 0)
				{
					//Add the field to sWork (prepend comma if needed)
					if (sStatement != "")
					{
						sStatement += ", " + sField + "|" + sCalcWord;
					}
					else
					{
						sStatement = sField + "|" + sCalcWord;
					}
				}
				sCalcWord = "MED";
				if (sGenFieldCalc.IndexOf(sCalcWord) + 1 > 0)
				{
					//Add the field to sWork (prepend comma if needed)
					if (sStatement != "")
					{
						sStatement += ", " + sField + "|" + sCalcWord;
					}
					else
					{
						sStatement = sField + "|" + sCalcWord;
					}
				}
				sCalcWord = "COUNT";
				if (sGenFieldCalc.IndexOf(sCalcWord) + 1 > 0)
				{
					//Add the field to sWork (prepend comma if needed)
					if (sStatement != "")
					{
						sStatement += ", " + sField + "|" + sCalcWord;
					}
					else
					{
						sStatement = sField + "|" + sCalcWord;
					}
				}

			}
	WeAreDone:

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sStatement;

		}

		private string AddSelectiveFilterToCondition(string par_sTable, string par_sCondition, string sAccessType, bool par_bSysFileFullPermissions, bool par_bGetAllUsersUnsharedRecs)
		{
			//MI 5/28/13 Excluding sysadmins from sharestate filtering.
			//       This was motivated by the need for sysadmins to see private MD records such as alerts (ALT_),
			//       but the need has existed for sysadmins to be able to inspect or clean up non-shared records in _all_ files.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sWork = null;
			string sResult = par_sCondition;

			//Try

			sWork = "";

			if (goData.IsFileSystem(par_sTable) && par_bSysFileFullPermissions) //MI 3/17/09 added testing par_bSysFileFullPermissions
			{
				//The file is system and we need to return all data - no filter to append
			}
			else
			{
				//Use normal permissions
				switch (goPerm.GetSelectivePermission(par_sTable, sAccessType))
				{
					case 2:
					break;
							//Full access
					case 1:
						//Selective access
						sWork = goPerm.GetSelectiveFilter(par_sTable, sAccessType);
						break;
					case 0:
						//No permission - this will return no data
						sWork = "BI__ID < 1"; //GID_ID <[]>
						break;
				}
			}
			if (sWork != "")
			{
				if (sResult == "")
				{
					sResult = sWork;
				}
				else
				{
					sResult = "(" + sResult + ") AND (" + sWork + ")";
				}
			}

			//=================== ADD SHARESTATE FILTER TO CONDITION ============
			//SI__ShareState allows sharing a record like this:
			//0 - private: visible only by creator, not synced (when replication is supported)
			//1 - protected: visible only by creator, but synced to server. Not synced to other clients.
			//2 - Shared: visible by everyone subject to their read selective access filter(s).
			//Currently only 1 and 2 are supported in the UI. 1 (protected) is exposed as 'private' in UI.
			//MI 4/9/08 Added checking the new parameter par_bGetAllUsersUnsharedRecs
			sWork = "";
			//MI 5/28/13 Excluding sysadmins from sharestate filtering.
			//This was motivated by allowing sysadmins to see private MD records such as alerts (ALT_),
			//but the need has existed for sysadmins to be able to inspect or clean up non-shared records in all files.
			if (goP.IsUserAdmin())
			{
				//Return all records because the user is a sysadmin
			}
			else
			{
				if (par_bGetAllUsersUnsharedRecs)
				{
					//Return all records because unshared records have been requested explicitly
				}
				else
				{
					//Filter out non-shared records that were not created by the current user
					//If Not IsFileSystem(sTable) Then   'MI 3/17/09 commented
					//    If LNK_CreatedBy_US link doesn't exist, SI__ShareState is ignored.
					//Is CreatedBy User link in the file?
					if (goData.IsFieldValid(par_sTable, "LNK_CreatedBy_US"))
					{
						sWork = "SI__ShareState = 2 OR (SI__ShareState < 2 AND LNK_CreatedBy_US='<%MEID%>')";
						if (sResult == "")
						{
							sResult = sWork;
						}
						else
						{
							sResult = "(" + sResult + ") AND (" + sWork + ")";
						}
					}
					else
					{
						//There is no link Created By User link, therefore all records are treated as shared
					}
					//End If
				}
			}


			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sResult;

		}

		private string ExtractConditionSegment(string sCondition, ref int iLastPos, ref int iLastAndOrLen, ref long lPos, ref string sParensStart, ref string sParensEnd)
		{
			//MI 4/9/09
			//PURPOSE:
			//       Extract a single field's condition segment including parens.
			//       Called from GenerateSQL.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sWork = "";
			long lWork = 0;

			//Try
			if (lPos < 1)
			{
				sWork = (goTR.FromTo(sCondition, iLastPos + iLastAndOrLen)).Trim(' ');
			}
			else
			{
				sWork = (goTR.FromTo(sCondition, iLastPos + iLastAndOrLen, lPos - 1)).Trim(' ');
			}
			//Extract starting parens
			lWork = 1;
			sParensStart = "";
			do
			{
				if (sWork.Substring((int)(lWork - 1), 1) == "(")
				{
					sParensStart += "(";
				}
				else
				{
					break;
				}
				lWork = lWork + 1;
			} while (true);
			sWork = sWork.Substring(sParensStart.Length);
			//Extract closing parens
			lWork = 1;
			sParensEnd = "";
			do
			{
				//==> When a condition line is malformed and ends with an AND or OR, sWork is "",
				//and the following line causes a SS error because the start position for Mid
				//is < 1. The solution (goTr.FromTo) addresses that, but doesn't address the
				//fact that the SQL WHERE statement will end with AND or OR.
				//If Mid(sWork, (Len(sWork) - lWork + 1), 1) = ")" Then
				if (goTR.FromTo(sWork, (sWork.Length - lWork + 1), sWork.Length - lWork + 1) == ")")
				{
					sParensEnd += ")";
				}
				else
				{
					break;
				}
				lWork = lWork + 1;
			} while (true);
			sWork = sWork.Substring(0, sWork.Length - sParensEnd.Length);

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sWork;

		}

		private void ExtractFieldOperatorAndValue(string sSingleCondString, ref string sFieldPart, ref string sOperator, ref string sValue)
		{
			//MI 7/14/10 Added ignoring par_iValid from StringToDateTime() for DTx fields. THis is to avoid errors
			//       from being raised when a calculated datetime for DTx is before the 'blank' datetime

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			bool bSingleQuotes = false;
			int iType = 0;
			DateTime dtTemp = default(DateTime);
			string sFieldType = null;
			string sFieldAlone = null;
			string sFieldRemainder = null;
			int iPos = 0;
			int iTZOffset = 0;

			//Try

			sFieldPart = goTR.ExtractFromCondition(sSingleCondString, "FIELD");
			sOperator = goTR.ExtractFromCondition(sSingleCondString, "OPERATOR");
			if (sOperator == "")
			{
				goErr.SetError(35013, sProc, "", sSingleCondString);
				//35013: Invalid operator in condition '[1]'.
				//Return "SELECT '" & sProc & " Error: Invalid operator in condition [" & sSingleCondString & "].' AS 'ERROR'"
			}
			sValue = goTR.ExtractFromCondition(sSingleCondString, "VALUE").Trim(' ');
			//==> Finish or remove ProcessConditionValue. The intent is for it to add or
			//remove single quotes around the string as appropriate and prepare sValue
			//for further processing.
			//sValue = ProcessConditionValue(sValue, sFieldPart)
			//Evaluate codes such as <%MEID%> and <%DATESYS%>
			if (goTR.FromTo(sValue, 1, 1) == "'" && sValue.Substring(sValue.Length - 1) == "'")
			{
				bSingleQuotes = true;
				sValue = goTR.RemoveSingleQuotes(sValue);
			}
			else
			{
				bSingleQuotes = false;
			}
			//sValue = goTR.PrepareForSQL(sValue)
			sValue = goTR.GetLineValue(ref sValue);

			//------- Evaluate date/time expressions -----------
			//Example: 'Tomorrow', '3 days from today', 'last quarter'
			sFieldAlone = goTR.ExtractStringFromEnd(sFieldPart, 1, "%%");
			if (sFieldAlone[0] == clC.EOT || sFieldAlone == "")
			{
				sFieldAlone = sFieldPart;
			}
			//Extract the field's prefix
			sFieldType = goTR.ExtractFieldPrefix(sFieldAlone, true);
			//MI 11/5/09 Substitute DTT for data grouping fields: DTY, DTQ, DTM, DTD
			switch (sFieldType)
			{
				case "DTY":
				case "DTQ":
				case "DTM":
				case "DTD":
					//sFieldType = "DTT"     'MI 11/13/09 commented because we need to calc the UTC value differently for DTx fields
					//Change the field to DTT_ for purposes of filtering
					//Replace prefix with DTT
					sFieldAlone = "DTT_" + goTR.GetPostPrefix(sFieldAlone);
					//Replace the field in the string that may be a link name (with %%'s)
					iPos = sFieldPart.LastIndexOf("%%") + 1;
					if (iPos < 1)
					{
						sFieldRemainder = "";
					}
					else
					{
						sFieldRemainder = goTR.FromTo(sFieldPart, 1, iPos + 1);
					}
					sFieldPart = sFieldRemainder + sFieldAlone;
					break;
			}
			//Evaluate DTT, DTE, TME
			switch (sFieldType)
			{
				case "DTT": //Datetime (datetime type)
					dtTemp = goTR.StringToDateTime(sValue, "", "", ref iType, "|");
					switch (iType)
					{
							//SELL_TYPE_KEYWORD and SELL_TYPE_ABBREV are not supported for DTT strings
							//because they can be mixed between the date and the time
						case clC.SELL_TYPE_VALID:
							dtTemp = goTR.UTC_LocalToUTC(ref dtTemp); //*** MI 10/1/07
							int tempVar = clC.SELL_TYPE_INVALID;
							string tempVar2 = " ";
							sValue = goTR.DateTimeToSysString(dtTemp, ref tempVar, ref tempVar2);
							break;
						default: //clC.SELL_TYPE_INVALID
							goErr.SetError(35014, sProc, "", sSingleCondString, sValue);
							break;
							//35014: Invalid date and/or time in condition '[1]': '[2]'.
							//Return "SELECT '" & sProc & " Error: Invalid date and/or time in condition [" & sSingleCondString & "]: [" & sValue & "].' AS 'ERROR'"
					}
					break;
				case "DTY":
				case "DTQ":
				case "DTM":
				case "DTD": //Date grouping fields (datetime type)
					dtTemp = goTR.StringToDateTime(sValue, "", "", ref iType, "|");
					//MI 7/14/10 dtTemp can have a blank datetime at this point, but iType can be INVALID because
					//the datetime is earlier than 1753-01-02 due to the date calculation (a DTM of 1753-01-02 is 1753-01-01)

					//MI 7/14/10 Ignore iType because if it's INVALID, the datetime will still be BLANK.
					//Select Case iType
					//    'SELL_TYPE_KEYWORD and SELL_TYPE_ABBREV are not supported for DTT strings
					//    'because they can be mixed between the date and the time
					//    Case clC.SELL_TYPE_VALID

					//MI 11/13/09 Converting value to UTC using the user's current TZ offset,
					//not standard methods like goTR.UTC_LocalToUTC(dtTemp). This is because
					//SS returns DTx values offset strictly by the user's current TZ offset 
					//and doesn't take the DST offset into account, which goTR.UTC_LocalToUTC()
					//does. We send the user's TZ offset to SS via pInitSession.

					//dtTemp = DateTime.SpecifyKind(dtTemp, DateTimeKind.Local)      'not needed?
					iTZOffset = Convert.ToInt32(goTR.oUserTimeZone.GetUtcOffset(goTR.NowLocal()).TotalMinutes);
					iTZOffset = iTZOffset - (iTZOffset * 2);
					dtTemp = dtTemp.AddMinutes(iTZOffset);
					int tempVar3 = clC.SELL_TYPE_INVALID;
					string tempVar4 = " ";
					sValue = goTR.DateTimeToSysString(dtTemp, ref tempVar3, ref tempVar4);
					break;

						//    Case Else   'clC.SELL_TYPE_INVALID
						//        goErr.SetError(35014, sProc, , sSingleCondString, sValue)
						//        '35014: Invalid date and/or time in condition '[1]': '[2]'.
						//        'Return "SELECT '" & sProc & " Error: Invalid date and/or time in condition [" & sSingleCondString & "]: [" & sValue & "].' AS 'ERROR'"
						//End Select

				case "DTE": //Date (datetime type)
					dtTemp = goTR.StringToDate(sValue, "", ref iType);
					switch (iType)
					{
						case clC.SELL_TYPE_KEYWORD:
						case clC.SELL_TYPE_ABBREV:
						case clC.SELL_TYPE_FIXE:
							dtTemp = goTR.UTC_LocalToUTC(ref dtTemp); //*** MI 10/1/07
							int tempVar5 = clC.SELL_TYPE_INVALID;
							string tempVar6 = " ";
							sValue = goTR.DateTimeToSysString(dtTemp, ref tempVar5, ref tempVar6);
							break;
						default:
							goErr.SetError(35014, sProc, "", sSingleCondString, sValue);
							break;
							//35014: Invalid date and/or time in condition '[1]': '[2]'.
							//Return "SELECT '" & sProc & " Error: invalid date in condition [" & sSingleCondString & "]: [" & sValue & "].' AS 'ERROR'"
					}
					break;
				case "TME":
				case "TML": //Time (Datetime type)
					dtTemp = goTR.StringToTime(sValue, "", ref iType);
					switch (iType)
					{
						case clC.SELL_TYPE_KEYWORD:
						case clC.SELL_TYPE_ABBREV:
						case clC.SELL_TYPE_FIXE:
							dtTemp = goTR.UTC_LocalToUTC(ref dtTemp); //*** MI 10/1/07
							int tempVar7 = clC.SELL_TYPE_INVALID;
							string tempVar8 = " ";
							sValue = goTR.DateTimeToSysString(dtTemp, ref tempVar7, ref tempVar8);
							break;
						default:
							goErr.SetError(35014, sProc, "", sSingleCondString, sValue);
							break;
							//35014: Invalid date and/or time in condition '[1]': '[2]'.
							//Return "SELECT '" & sProc & " Error: invalid time in condition [" & sSingleCondString & "]: [" & sValue & "].' AS 'ERROR'"
					}
					break;
			}

			//Single quotes inside the value are replaced with '' in GetSQLCondition below
			if (bSingleQuotes)
			{
				sValue = "'" + sValue + "'";
			}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		private void FindNextConditionPart(string sCondition, ref bool bBreakDo, ref long lPos, ref int iAndOrLen, ref string sAndOr, ref int iLastPos, ref int iLastAndOrLen)
		{
			//MI 3/23/10 Enhanced comments on single quote as delimiter.
			//MI 4/9/09
			//PURPOSE:
			//       Locate the next single condition part of the CONDITION statement
			//       from the filter in GenerateSQL. Condition parts are between
			//       AND or OR words, and may be within parens. This sub sets
			//       several variables via ByRef parameters, which are then used
			//       inside a Do loop to process individual conditions.
			//EXAMPLE INCLUDING TOLERATED SYNTAX DEVIATIONS (spaces, case, extra parens):
			//   (CHK_ActiveField>0 and (TXT_NameLast='Doe' and (TXT_NameFirst='John' )) OR (((TXT_NameLast = Speck) aNd (txt_namefirst= Jack) )) 
			//RULES:
			//   - Single quote (') is treated as a value delimiter. Everything between 
			//       an opening and closing single quote is treated as the value.
			//       The first single quote found after = (and any no of spaces) 
			//       is treated as a opening value delimiter. The next single quote
			//       is treated as the ending value delimiter unless it is followed 
			//       by another single quote. In order for a single quote to survive
			//       within the value, it must be doubled as passed to this method. 
			//       Run goTR.PrepareForSQL()) to do so.
			//   - AND or OR must be prepended with a space character.
			//   - Validity of the field, operator and value is not checked. A string
			//       starting with ' AND' or ' OR' will pass, but SS will raise an error.
			//   - Parens are not validated. SS will raise an error if parens are mismatched
			//       or misused.
			//MAINTAIN:
			//   lPos        position of ' AND' or ' OR'
			//   iAndOrLen   OR: 3; AND: 4
			//   sAndOr      " OR " or " AND "
			//   bBreakDo    True when no more ANDs or ORs can be found
			//CHARACTERS TO LOOK FOR:
			//   (           Skip.
			//   )           Skip.
			//   '           Value delimiter. Optional. Ignore ''. Must have a closing pair.
			//               If anything other than '(', ')', 'AND', or 'OR' follows it,
			//               raise error.
			//   AND         Check whether found outside '' delimiters. When found, exit 
			//               the inner loop and process the condition segment.
			//               Start processing the next condition segment after it.
			//   OR          Ditto.

			//Walk through the string and extract each single field's condition with parens
			//We are not trying to ensure 100% syntax compliance here - SS will do that.
			//We only have to extract condition segments between ANDs or ORs accurately to
			//process them.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sWork = null;
			string sAndOrFound = null;
			bool bFoundSingleQuotes = false;
			bool bSingleQuotesClosed = false;
			bool bReadyForAndOr = false;
			int i = 0;

			//Try

			//Reset field-level condition segment variables
			sAndOrFound = "";
			bFoundSingleQuotes = false; //At least one single quote was found
			bSingleQuotesClosed = false; //A closing single quote was found, only AND or OR allowed
			bReadyForAndOr = true; //Indicates that AND or OR are allowed regardless of single quotes
			sWork = "";
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of Len(sCondition) for every iteration:
			int tempVar = sCondition.Length;
			for (i = (iLastPos + iLastAndOrLen); i <= tempVar; i++)
			{
				sWork = goTR.FromTo(sCondition, i, i);
				switch (sWork.ToUpper())
				{
					case "'":
						//Found single quote
						if (bFoundSingleQuotes == false)
						{
							//No single quotes were found yet - this is the opening delimiter
							bFoundSingleQuotes = true;
							bSingleQuotesClosed = false;
							bReadyForAndOr = false;
							sAndOrFound = "";
						}
						else
						{
							//One or more single quotes were already processed
							//Is the next character also a single quote?
							if (bSingleQuotesClosed)
							{
								//Closing single quote was already found - only AND or OR is allowed
								goErr.SetError(35022, sProc, "", sWork, sCondition);
								//35022: A condition has an unsupported character '[1]' between condition components instead of AND or OR. Condition:
								//
								//[2]
							}
							else
							{
								//
								if (goTR.FromTo(sCondition, i + 1, i + 1) == "'")
								{
									//Second single quote found - skip it and advance past ''
									i = i + 1;
									sAndOrFound = "";
								}
								else
								{
									//Closing single quote found
									bSingleQuotesClosed = true;
									bReadyForAndOr = true;
									sAndOrFound = "";
								}
							}
						}
						break;
					case "(":
						//Allow parens at any point - SS will raise an error if mismatched
						sAndOrFound = "";
						break;
							//If bSingleQuotesClosed Then
							//    goErr.SetError(35022, sProc, , sWork, sCondition)
							//    '35022: A condition has an unsupported character '[1]' between condition components instead of AND or OR. Condition:
							//    '
							//    '[2]
							//End If
							//iOpenParenCount = iOpenParenCount + 1
					case ")":
						//Allow parens at any point - SS will raise an error if mismatched
						sAndOrFound = "";
						break;
							//iCloseParenCount = iCloseParenCount + 1
					case "A":
						if (bReadyForAndOr)
						{
							if (sAndOrFound == " ")
							{
								sAndOrFound += "A";
							}
							else
							{
								if (bSingleQuotesClosed)
								{
									//Only AND or OR are allowed after a delimited value
									goErr.SetError(35022, sProc, "", sWork, sCondition);
									//35022: A condition has an unsupported character '[1]' between condition components instead of AND or OR. Condition:
									//
									//[2]
								}
								else
								{
									sAndOrFound = "";
								}
							}
						}
						else
						{
							sAndOrFound = "";
						}
						break;
					case "N":
						if (bReadyForAndOr)
						{
							if (sAndOrFound == " A")
							{
								sAndOrFound += "N";
							}
							else
							{
								if (bSingleQuotesClosed)
								{
									//Only AND or OR are allowed after a delimited value
									goErr.SetError(35022, sProc, "", sWork, sCondition);
									//35022: A condition has an unsupported character '[1]' between condition components instead of AND or OR. Condition:
									//
									//[2]
								}
								else
								{
									sAndOrFound = "";
								}
							}
						}
						else
						{
							sAndOrFound = "";
						}
						break;
					case "D":
						if (bReadyForAndOr)
						{
							if (sAndOrFound == " AN")
							{
								//Condition done - trigger processing below
								lPos = i - 3;
								iAndOrLen = 4;
								//Reset condition segment variables
								sAndOrFound = "";
								bReadyForAndOr = false;
								bSingleQuotesClosed = false;
								bFoundSingleQuotes = false;
								bBreakDo = false;
								sAndOr = " AND ";
// INSTANT C# WARNING: Exit statements not matching the immediately enclosing block are converted using a 'goto' statement:
// ORIGINAL LINE: Exit For
								goto ExitLabel1;
							}
							else
							{
								if (bSingleQuotesClosed)
								{
									//Only AND or OR are allowed after a delimited value
									goErr.SetError(35022, sProc, "", sWork, sCondition);
									//35022: A condition has an unsupported character '[1]' between condition components instead of AND or OR. Condition:
									//
									//[2]
								}
								else
								{
									sAndOrFound = "";
								}
							}
						}
						else
						{
							sAndOrFound = "";
						}
						break;
					case "O":
						if (bReadyForAndOr)
						{
							if (sAndOrFound == " ")
							{
								sAndOrFound += "O";
							}
							else
							{
								if (bSingleQuotesClosed)
								{
									//Only AND or OR are allowed after a delimited value
									goErr.SetError(35022, sProc, "", sWork, sCondition);
									//35022: A condition has an unsupported character '[1]' between condition components instead of AND or OR. Condition:
									//
									//[2]
								}
								else
								{
									sAndOrFound = "";
								}
							}
						}
						else
						{
							sAndOrFound = "";
						}
						break;
					case "R":
						if (bReadyForAndOr)
						{
							if (sAndOrFound == " O")
							{
								//Condition done - trigger processing below
								lPos = i - 2;
								iAndOrLen = 3;
								//Reset condition segment variables
								sAndOrFound = "";
								bReadyForAndOr = false;
								bSingleQuotesClosed = false;
								bFoundSingleQuotes = false;
								bBreakDo = false;
								sAndOr = " OR ";
// INSTANT C# WARNING: Exit statements not matching the immediately enclosing block are converted using a 'goto' statement:
// ORIGINAL LINE: Exit For
								goto ExitLabel1;
							}
							else
							{
								if (bSingleQuotesClosed)
								{
									//Only AND or OR are allowed after a delimited value
									goErr.SetError(35022, sProc, "", sWork, sCondition);
									//35022: A condition has an unsupported character '[1]' between condition components instead of AND or OR. Condition:
									//
									//[2]
								}
								else
								{
									sAndOrFound = "";
								}
							}
						}
						else
						{
							sAndOrFound = "";
						}
						break;
					case " ":
						if (bReadyForAndOr)
						{
							if (sAndOrFound == "")
							{
								sAndOrFound += " ";
							}
							else if (sAndOrFound == " ")
							{
								//Skip - multiple spaces before AND or OR are tolerated
							}
							else
							{
								goErr.SetError(35022, sProc, "", sWork, sCondition);
								//35022: A condition has an unsupported character '[1]' between condition components instead of AND or OR. Condition:
								//
								//[2]
							}
						}
						else
						{
							sAndOrFound = "";
						}
						break;
					default:
						if (bSingleQuotesClosed)
						{
							//A delimited value was found, this character is disallowed (only AND or OR are allowed)
							goErr.SetError(35022, sProc, "", sWork, sCondition);
							//35022: A condition has an unsupported character '[1]' between condition components instead of AND or OR. Condition:
							//
							//[2]
						}
						else
						{
							//There was no delimited value, we allow any characters
							sAndOrFound = "";
						}
						break;
				}
			}
			ExitLabel1: ;

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

		}

		private string GetFieldExpression(string par_sTable, string par_sField, string par_sMode)
		{
			//MI 4/25/12 Fixed issue: users in GMT time zone or east of it (GMT+ values) would see a blank DTT as a 1753-01-03 DTD value.
			//       Original issue from CS 8/16/11: In GMT+1 time zone, blank dates display as dates on 1753-01-03. set yourself to gmt-5...
			//       Then in Act-All add fields Next Action Date/Time (which are mostly blank) and Next Action y-m-d....
			//       they will all say 1753-01-03.
			//MI 12/22/10 Added +60 to all dbo.fnGetSessionTZOffset() calls to prevent non-DST times from falling on previous day (and therefore month, quarter, year). 
			//MI 1/28/10 Added par_sMode parameter, added ISNULL and added cases other than DTx and BI__GROUPBYCOUNT for GROUPBY modes.
			//PURPOSE:
			//       Returns [table].[field] for inclusion in SELECT or ORDER BY lines.
			//       Date grouping fields DTY DTQ DTM DTD are wrapped with additional TSQL code.
			//       This code returns the first day (midnight) of the appropriate period (year, quarter, etc)
			//       for a local time according to the user's TZ offset. Keep in mind that DTY DTQ DTM and DTD
			//       values are based on the local time, not UTC. This local time is passed to SS in
			//       goData.GetConnection, which runs pInitSession.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sResult = "";
			//Try
			switch (par_sMode)
			{
				case "GROUPBY":
				case "GROUPBYWITHROLLUP":
					//Add ISNULL to every field reference
					switch (goTR.GetPrefix(par_sField))
					{
						case "DTT_":
							sResult = "ISNULL([" + par_sTable + "].[" + par_sField + "], '1753-01-02 23:59:59.000')";
							break;
						case "DTY_":
							//sResult = "dateadd(year, datediff(year, 0, [" & par_sTable & "].[" & "DTT_" & goTR.GetPostPrefix(par_sField) & "]), 0)"
							sResult = "dateadd(year, datediff(year, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, ISNULL([" + par_sTable + "].[" + "DTT_" + goTR.GetPostPrefix(par_sField) + "], '1753-01-02 23:59:59.000'))), 0)";
							break;
						case "DTQ_":
							//sResult = "dateadd(quarter, datediff(quarter, 0, [" & par_sTable & "].[" & "DTT_" & goTR.GetPostPrefix(par_sField) & "]), 0)"
							sResult = "dateadd(quarter, datediff(quarter, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, ISNULL([" + par_sTable + "].[" + "DTT_" + goTR.GetPostPrefix(par_sField) + "], '1753-01-02 23:59:59.000'))), 0)";
							break;
						case "DTM_":
							//sResult = "dateadd(month, datediff(month, 0, [" & par_sTable & "].[" & "DTT_" & goTR.GetPostPrefix(par_sField) & "]), 0)"
							sResult = "dateadd(month, datediff(month, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, ISNULL([" + par_sTable + "].[" + "DTT_" + goTR.GetPostPrefix(par_sField) + "], '1753-01-02 23:59:59.000'))), 0)";
							break;
						case "DTD_":
							//sResult = "dateadd(day, datediff(day, 0, [" & par_sTable & "].[" & "DTT_" & goTR.GetPostPrefix(par_sField) & "]), 0)"
							//MI 4/25/12 Replacing this code with code that catches 1753-01-03, which the users in time zones to the east of GMT
							//get as a 'day' value for blank dates (1753-01-02 23:59:59.000). Note that DTM and DTY are unaffected.
							//sResult = "dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, ISNULL([" & par_sTable & "].[" & "DTT_" & goTR.GetPostPrefix(par_sField) & "], '1753-01-02 23:59:59.000'))), 0)"

							//MI 4/25/12 Fix for the above issue. New code ensures that blank DTTs return a blank DTD.
							//CASE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [AC].[DTT_NextActionDate])), 0)
							//   WHEN '1753-01-03' THEN '1753-01-01'
							//   ELSE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [AC].[DTT_NextActionDate])), 0)
							//END AS 'DTD_Fixed',
							sResult = "CASE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, ISNULL([" + par_sTable + "].[" + "DTT_" + goTR.GetPostPrefix(par_sField) + "], '1753-01-02 23:59:59.000'))), 0) WHEN '1753-01-03' THEN '1753-01-01' ELSE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, ISNULL([" + par_sTable + "].[" + "DTT_" + goTR.GetPostPrefix(par_sField) + "], '1753-01-02 23:59:59.000'))), 0) END";
							break;

						case "GID_":
							sResult = "[" + par_sTable + "].[" + par_sField + "]";
							break;
						case "BI__":
							if (par_sField.ToUpper() == "BI__GROUPBYCOUNT")
							{
								sResult = "COUNT_BIG(*)";
							}
							else
							{
								sResult = "ISNULL([" + par_sTable + "].[" + par_sField + "], 0)";
							}
							break;
						case "CHK_":
						case "CMB_":
						case "CUR_":
						case "DR__":
						case "INT_":
						case "LI__":
						case "LST_":
						case "SEL_":
						case "SI__":
						case "SR__":
							sResult = "ISNULL([" + par_sTable + "].[" + par_sField + "], 0)";
							break;
						case "MLS_":
							sResult = "ISNULL([" + par_sTable + "].[" + par_sField + "], 0)";
							break;
							//This code is in clRowset, but in SS we don't have GetDefaultIndex function...
							//If iRSType = clC.SELL_GROUPBY Or iRSType = clC.SELL_GROUPBYWITHROLLUP Then  'MI 12/23/09 added
							//    Return 0
							//Else
							//    Return oList.GetDefaultIndex(sRSFile, goTR.RemovePrefix(sTempFieldName))    'MI 9/26/08 Added
							//End If
						default:
							sResult = "ISNULL([" + par_sTable + "].[" + par_sField + "], '')"; //string type is assumed
							break;
					}
					break;
				default:
					//ISNULL references are not necessary because the rowset deals with NULLs and ISNULL would slow down SQL
					switch (goTR.GetPrefix(par_sField))
					{
						case "DTY_":
							//sResult = "dateadd(year, datediff(year, 0, [" & par_sTable & "].[" & "DTT_" & goTR.GetPostPrefix(par_sField) & "]), 0)"
							sResult = "dateadd(year, datediff(year, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [" + par_sTable + "].[" + "DTT_" + goTR.GetPostPrefix(par_sField) + "])), 0)";
							break;
						case "DTQ_":
							//sResult = "dateadd(quarter, datediff(quarter, 0, [" & par_sTable & "].[" & "DTT_" & goTR.GetPostPrefix(par_sField) & "]), 0)"
							sResult = "dateadd(quarter, datediff(quarter, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [" + par_sTable + "].[" + "DTT_" + goTR.GetPostPrefix(par_sField) + "])), 0)";
							break;
						case "DTM_":
							//sResult = "dateadd(month, datediff(month, 0, [" & par_sTable & "].[" & "DTT_" & goTR.GetPostPrefix(par_sField) & "]), 0)"
							sResult = "dateadd(month, datediff(month, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [" + par_sTable + "].[" + "DTT_" + goTR.GetPostPrefix(par_sField) + "])), 0)";
							break;
						case "DTD_":
							//sResult = "dateadd(day, datediff(day, 0, [" & par_sTable & "].[" & "DTT_" & goTR.GetPostPrefix(par_sField) & "]), 0)"

							//MI 4/25/12 Replacing this code with code that catches 1753-01-03, which the users in time zones to the east of GMT
							//           get as a 'day' value for blank dates (1753-01-02 23:59:59.000). Note that DTM and DTY are unaffected.
							//sResult = "dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [" & par_sTable & "].[" & "DTT_" & goTR.GetPostPrefix(par_sField) & "])), 0)"

							//MI 4/25/12 Fix for the above issue. New code ensures that blank DTTs return a blank DTD.
							//CASE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [AC].[DTT_NextActionDate])), 0)
							//   WHEN '1753-01-03' THEN '1753-01-01'
							//   ELSE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [AC].[DTT_NextActionDate])), 0)
							//END AS 'DTD_Fixed',
							sResult = "CASE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [" + par_sTable + "].[" + "DTT_" + goTR.GetPostPrefix(par_sField) + "])), 0) WHEN '1753-01-03' THEN '1753-01-01' ELSE dateadd(day, datediff(day, 0, dateadd(minute, dbo.fnGetSessionTZOffset()+60, [" + par_sTable + "].[" + "DTT_" + goTR.GetPostPrefix(par_sField) + "])), 0) END";
							break;

						case "BI__":
							if (par_sField.ToUpper() == "BI__GROUPBYCOUNT")
							{
								sResult = "COUNT_BIG(*)";
							}
							else
							{
								sResult = "[" + par_sTable + "].[" + par_sField + "]";
							}
							break;
						default:
							sResult = "[" + par_sTable + "].[" + par_sField + "]";
							break;
					}
					break;
			}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
			return sResult;
		}

		private string GenerateDeclarations(string sTableVarInsertStr, string sMode)
		{
			//MI 4/9/09
			//PURPOSE:
			//       This creates the declaration section. Two table variables are declared:
			//           - tManyLinkList: returned as the second recordset that contains the list of n-n links.
			//               Each n-n link is returned in a separate recordset after that. The order of links in
			//               the second recordset reflects the order of subsequent link recordsets.
			//           - tIDTable: Table if IDs and other field values that we use for the
			//               filtering of the main recordset. A SELECT statement with all fields is JOINed
			//               against IDs in this table. This gives us the flexibility of working
			//               with binary fields such as ntext.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sDeclaration = "";
			long lCount = 0;
			string sWork = null;
			string sWork2 = null;
			string sSQLType = null;

			//Try

			//Enable the following block when you want to benchmark SS performance with clean caches
			//sDeclaration &= "-- Debug" & vbCrLf
			//sDeclaration &= "DBCC DROPCLEANBUFFERS" & vbCrLf
			//sDeclaration &= "DBCC FREEPROCCACHE" & vbCrLf
			//sDeclaration &= "SET STATISTICS IO ON" & vbCrLf
			//sDeclaration &= "-- End Debug" & vbCrLf & vbCrLf

			switch (sMode)
			{
				case "COUNT":
				break;
					//Don't need N link tables
				default:
					sDeclaration += "DECLARE @tManyLinkList TABLE(LinkName nvarchar(100))" + "\r\n";
					break;
					//DECLARE @tIDTable TABLE(	GID_ID UNIQUEIDENTIFIER, 
					//			TXT_NameLast nvarchar(22), --22
					//			TXT_NameFirst nvarchar(300), --15
					//			DTT_LastContactDate datetime,
					//			CHK_ActiveField tinyint,
					// 			MMO_Note ntext)
			}

			//sDeclaration += "DECLARE @tIDTable TABLE([GID_ID] uniqueidentifier"
			//'SYS_Name is here for debugging convenience in n-n link recordsets below
			//sDeclaration += ", " + CR + TAB + "[" + sTable + "_SYS_Name] nvarchar(80)"
			sDeclaration += "DECLARE @tIDTable TABLE(";
			//Add all columns from the ORDER BY statement in the query that
			//loads the @tIDTable variable with data
			lCount = 1;
			sWork = "";
			sWork2 = "";
			do
			{
				sWork = goTR.ExtractString(sTableVarInsertStr, (int)lCount, ",").Trim(' ');
				if (sWork[0] == clC.EOT || sWork == "")
				{
					break;
				}
				if (sDeclaration.ToUpper().IndexOf(sWork.ToUpper()) + 1 < 1)
				{
					if (sDeclaration.Substring(sDeclaration.Length - 1) != "(")
					{
						sDeclaration += ", ";
					}
					//sWork can be [CO_TXT_NameLast] or [CO00003_TXT_NameLast]
					sSQLType = goTR.GetSQLTypeByPrefix(goTR.ExtractString(sWork, 2, "_")); //goTR.GetSQLTypeByPrefix(goTR.FromTo(sWork, 5, 8))
					if (sSQLType == "")
					{
						goErr.SetError(35012, sProc, "", goTR.ExtractString(sWork, 2, "_"), sWork);
						//35012: Invalid type returned by goTR.GetSQLTypeByPrefix for '[1]' from '[2]'.
						//Return "SELECT '" & sProc & " Error: Invalid type returned by goTR.GetSQLTypeByPrefix for [" & goTR.ExtractString(sWork, 2, "_") & "] from [" & sWork & "].' AS 'ERROR'"
					}
					sDeclaration += "\r\n" + "\t" + sWork + " " + sSQLType;
				}
	ProcessNextColumn:
				lCount = lCount + 1;
			} while (true);
			sDeclaration += ")" + "\r\n" + "\r\n";

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sDeclaration;

		}

		private void GenerateIDTableColumns(ref string sSortColListTemp, ref string sTable, ref clArray aAliases, ref string sTableVarInsertStr, ref string sInsertSelectString, string par_sMode = "SELECT", bool bUseLinkPermissions = true)
		{
			//MI 4/9/09
			//PURPOSE:
			//       Generate a list of columns for INSERT INTO @tIDTable statement
			//       in GenerateSQL. Parameters must be ByRef!

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sWork = "";
			string sWork2 = "";
			string sWork3 = "";
			long lCount = 1;
			long lWork = 0;
			string sLinkTable = null;
			string sLinkType = null;
			string sLink = null;
			string sField = null;
			string sAliasTable = null;

			//Try
			//MI 9/28/12 Reenabling
			//MI 9/20/12 Forcing link permissions to true
			//bUseLinkPermissions = True

			//Reset the column map. This assumes that this method is called only once
			//per generated query. If this is run multiple times, move the following
			//to the calling code before this runs the first time.
			tColumnMap.DeleteAll();

			par_sMode = par_sMode.ToUpper();

			//Add GID_ID and SYS_Name to sort columns to have a complete
			//list of columns for the INSERT INTO @tIDTable and its SELECT
			switch (par_sMode)
			{
				case "COUNT":
				case "GROUPBY":
				case "GROUPBYWITHROLLUP":
				break;
					//Don't add SYS_Name, it's redundant
				default:
					if (sSortColListTemp.ToUpper().IndexOf(", SYS_NAME") + 1 < 1)
					{
						if (sSortColListTemp.ToUpper().IndexOf("SYS_NAME") + 1 != 1)
						{
							sSortColListTemp = "SYS_NAME, " + sSortColListTemp;
						}
					}
					break;
			}

			if (sSortColListTemp.ToUpper().IndexOf(", GID_ID") + 1 < 1)
			{
				if (sSortColListTemp.ToUpper().IndexOf("GID_ID") + 1 != 1)
				{
					sSortColListTemp = "GID_ID, " + sSortColListTemp;
				}
			}
			do
			{
				sWork = goTR.ExtractString(sSortColListTemp, (int)lCount, ",").Trim(' ');
				if (sWork[0] == clC.EOT || sWork == "")
				{
					break;
				}
				if (par_sMode == "COUNT")
				{
					//Don't need any columns other than GID_ID in COUNT mode
					if (!(sWork.ToUpper() == "GID_ID"))
					{
						goto ProcessNextColumn;
					}
				}
				if (goTR.GetPrefix(sWork) == "LNK_")
				{
					//Link
					if (sWork.IndexOf("%%") + 1 > 0) //InStr()
					{
						sLink = goTR.ExtractString(sWork, 1, "%%");
						sField = goTR.ExtractString(sWork, 2, "%%");
					}
					else
					{
						sLink = sWork;
						sField = "GID_ID";
						sWork = sLink + "%%" + sField;
					}
					if (goTR.GetPrefix(sField) == "LNK_")
					{
						goErr.SetError(35010, sProc, "", sWork);
						//35010: Multi-level link not allowed in @tIDTable: '[1]'.
						//Return "SELECT '" & sProc & " Error: Multi-level link not allowed in @tIDTable: [" & sWork & "].' AS 'ERROR'"
					}
					sLinkTable = goTR.GetFileFromLinkName(sLink);
					//Determine the link type (1 or many) to compose the JOIN statement(s)
					sLinkType = goData.LKGetType(sTable, sLink);
					if (sLinkType == "")
					{
						goErr.SetError(10108, sProc, "", sTable + "." + sLink);
						//10108: Internal error: invalid link name '[1]'.
						//Return "SELECT '" & sProc & " Error: invalid link [" & sTable & "." & sLink & "].' AS 'ERROR'"
					}
					if (!goData.IsFieldValid(sLinkTable, sField))
					{
						goErr.SetError(10107, sProc, "", sTable + "." + sWork);
						//10107: Internal error: invalid field name '[1]'.
						//Return "SELECT '" & sProc & " Invalid field: [" & sTable & "." & sWork & "].' AS 'ERROR'"
					}
					//Add only the N1 links to @tIDTable columns. 
					if (sLinkType.Substring(sLinkType.Length - 1) == "1")
					{
						//Link is n to 1
						//Get or generate a table alias
						object temp_aAliases = aAliases;
						sAliasTable = goData.LKGetTableAlias(sTable, sLink, ref temp_aAliases);
							aAliases = (Selltis.BusinessLogic.clArray)temp_aAliases;
						if (sAliasTable == "")
						{
							goErr.SetError(35011, sProc, "", sTable + "." + sLink);
							//35011: goData.LKGetTableAlias failed on TableAlias.Link: '[1]'.
							//Return "SELECT '" & sProc & " Error: goData.LKGetTableAlias failed on TableAlias.Link: [" & sTable & "." & sLink & "].' AS 'ERROR'"
						}
						sWork2 = "[" + sAliasTable + "].[" + sField + "]";
						sWork3 = "[" + sAliasTable + "_" + sField + "]";
						//Add the link only if it hasn't been added already
						if (sInsertSelectString.ToUpper().IndexOf(sWork2.ToUpper()) + 1 < 1)
						{
							lWork = lWork + 1;
							//				If lWork > 1 Then
							if (sInsertSelectString.Substring(sInsertSelectString.Length - 1) == "]")
							{
								sTableVarInsertStr += ", ";
								sInsertSelectString += ", ";
							}
							sTableVarInsertStr += sWork3;
							sInsertSelectString += sWork2;
							//MI 1/13/10 added to enabled GROUPBY mode on N1 links with link perms on
							switch (par_sMode)
							{
								case "GROUPBY":
								case "GROUPBYWITHROLLUP":
									if (bUseLinkPermissions)
									{
										//Value: 'LNK_CREATEDBY_US%%TXT_CODE', Text: '[US00001_TXT_CODE]' (includes [ and ])
										tColumnMap.Add();
										tColumnMap.SetVal("Value", tColumnMap.Count(), sLink + "%%" + sField.ToUpper());
										tColumnMap.SetVal("Text", tColumnMap.Count(), sWork3.ToUpper());
									}
									break;
							}
						}
					}
				}
				else
				{
					//Direct field
					if (!goData.IsFieldValid(sTable, sWork))
					{
						goErr.SetError(10107, sProc, "", sTable + "." + sWork);
						//10107: Internal error: invalid field name '[1]'.
						//Return "SELECT '" & sProc & " Invalid field: [" & sTable & "." & sWork & "].' AS 'ERROR'"
					}
					sWork2 = "[" + sTable + "].[" + sWork + "]";
					sWork3 = "[" + sTable + "_" + sWork + "]";
					//Add the column only if it hasn't been added already
					if (sInsertSelectString.ToUpper().IndexOf(sWork2.ToUpper()) + 1 < 1)
					{
						lWork = lWork + 1;
                        //			If lWork > 1 Then
                        if (!string.IsNullOrEmpty(sInsertSelectString) &&sInsertSelectString.Substring(sInsertSelectString.Length - 1) == "]")
                        {
							sTableVarInsertStr += ", ";
							sInsertSelectString += ", ";
						}
						sTableVarInsertStr += sWork3;
						sInsertSelectString += sWork2;
						//'MI 1/13/10 added for consistency but commented. 
						//Only need to add N1 links to tColumnMap and only in GROUPBY mode when link perms are on (in WOP).
						//Select Case par_sMode
						//    Case "GROUPBY", "GROUPBYWITHROLLUP"
						//        If bUseLinkPermissions Then
						//            'key: 'DTT_TIME', value: '[OP_DTT_TIME]' (includes [ and ])
						//            tColumnMap.Add()
						//            tColumnMap.SetVal("Value", tColumnMap.Count, UCase(sWork))
						//            tColumnMap.SetVal("Text", tColumnMap.Count, UCase(sWork3))
						//        End If
						//End Select
					}
				}
	ProcessNextColumn:
				lCount = lCount + 1;
			} while (true);

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

		}

		private string GenerateMainSelectStatement(ref string sJoin, ref string sFields, ref string sSort, string sTable, ref clArray aAliases, string sMode, ref string sNLinks, ref string sSortString, long lTop = -1, bool bUseLinkPermissions = true, bool bUseInnerJoins = false)
		{
			//MI 1/8/09 Fixed bug: with link perms on, LNK_CREATEDBY_US%%SYS_NAME column was added twice. Changed sLinkTable to sLink.
			//MI 11/3/09 Added support for date grouping fields (DTY DTQ DTM DTD).
			//PURPOSE:
			//       This is the 'main' SELECT statement after the initial SELECT statement
			//       runs in the INSERT into @tIDTable context. The code exits prior
			//       to getting here in 'SIMPLESELECT' and 'JOINANDWHEREONLY' modes, so
			//       there is no need to test for them here. This is always part of the
			//       standard SELECT or COUNT mode statement.
			//       lTop parameter is used to prevent complicated subselects for N1 links
			//       when TOP is 0. The main SELECT statement never has TOP <n> defined
			//       because it is JOINed to @tIDTable, and that table variable is filled
			//       with a SELECT statement that does have TOP if needed.
			//RETURNS:
			//       Full SELECT statement that is JOINed to @tIDTable.
			//EXAMPLE:
			//SELECT CN.TXT_NameLast,
			//	CN.TXT_NameFirst, 
			//	CN.TXT_CompanyNameText AS 'Company Text',
			//	CO00002.TXT_CompanyName AS 'Related Company',
			//	US00001.TXT_NameLast, 
			//	US00001.TXT_NameFirst, 
			//	CN.GID_ID 
			//FROM CN 
			//JOIN @tIDTable idt ON idt.GID_ID = CN.GID_ID
			//-- JOINS are for SELECT and ORDER BY columns only.
			//-- There is no WHERE statement in this query
			//-- Only n-1 columns are processed, n-n are ignored.
			//LEFT JOIN US US00001 ON US00001.GID_ID = CN.GID_CreatedBy_US 
			//LEFT JOIN CO CO00002 ON CO00002.GID_ID = CN.GID_Related_CO
			//ORDER BY CN.TXT_NameLast, CN.TXT_NameFirst	--, CO00002.TXT_CompanyName 

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sSortColumnsTemp = null;
			string sWork = null;
			string sWork2 = null;
			string sWork3 = null;
			long lCount = 0;
			long lWork = 0;
			string sResult = "";
			string sLink = null;
			string sField = null;
			string sLinkTable = null;
			string sLinkType = null;
			string sAliasTable = null;
			string sToAdd = null;
			string sJoinWord = null;
			bool bAddField = false;
			int iPos = 0;
			string sGroupByString = null;
			string sFieldPart = null;
			string sCalc = null; //SUM, AVG, MIN, MAX or MED are supported
			string sFieldsTemp = sFields;
			clArray aSortFields = new clArray();
			long lRow = 0;
			string sSortStringInMainStatement = null; //sSortString, main sort string, possibly altered (in GROUPBY modes when link perms are on, for example)
			string sIDTColumn = "";

			//Try

			//MI 9/28/12 Reenabling
			//MI 9/20/12 Forcing link permission to true
			//bUseLinkPermissions = True

			sSortColumnsTemp = "";
			sSortStringInMainStatement = sSortString;

			sMode = sMode.ToUpper();

			if (bUseInnerJoins)
			{
				sJoinWord = "INNER ";
			}
			else
			{
				sJoinWord = "LEFT ";
			}


			// ------------- SELECT ----------------
			//DISTINCT is not needed because it is used in the query that
			//loads the table variable above and because the only JOINs
			//we can have in this query are on n-1 links and those don't
			//create duplicates.
			switch (sMode)
			{
				case "COUNT":
					sResult += "SELECT COUNT_BIG(*) as 'BI__COUNT'";
					//MI 2/14/10 Added FROM @tIDTable. This is faster than the old methodology:
					//--SELECT COUNT_BIG(*) as 'BI__COUNT'  
					//--FROM [AC]
					//--JOIN @tIDTable idt ON idt.[AC_GID_ID] = [AC].[GID_ID]
					sResult += "\r\n" + "FROM @tIDTable";
					goto FinishInCountMode;
				case "GROUPBY":
				case "GROUPBYWITHROLLUP": //MI 12/13/09 added
					//MI 1/14/10 Added supporting TOP in GROUPBY.
					if (lTop > -1)
					{
						sResult += "SELECT TOP " + lTop.ToString() + " ";
					}
					else
					{
						sResult += "SELECT ";
					}
					break;
				default:
					if (lTop == 0)
					{
						sResult += "SELECT TOP 0 ";
					}
					else
					{
						sResult += "SELECT ";
					}
					break;
			}


			// ---------------- Columns ---------------- 
			//First add the JOIN to @tIDTable
			//Example: JOIN @tIDTable idt ON idt.GID_ID = CN.GID_ID
			if (lTop == 0)
			{
				sJoin = "";
			}
			else
			{
				sJoin = "\r\n" + "JOIN @tIDTable idt ON idt.[" + sTable + "_GID_ID] = [" + sTable + "].[GID_ID]" + sJoin;
			}

			//Process fields
			sWork = "";
			sWork2 = "";
			lCount = 1; //used for loop iterations
			lWork = 0; //used to count columns that were actually added
			//Since DISTINCT is not used here, no need to add Sort columns here

			//Always return GID_ID except in GROUPBY mode
			switch (sMode)
			{
				case "GROUPBY":
				case "GROUPBYWITHROLLUP":
				break;
				default:
					if (sFields.ToUpper().IndexOf("GID_ID") + 1 != 1 && (sFields.ToUpper().IndexOf(", GID_ID") + 1) < 1)
					{
						sFields += ", GID_ID";
					}
					break;
			}

			//Add comma to end of sort string if needed to facilitate searching
			sFieldsTemp = sFields.Trim(' ');
			if (sFieldsTemp.Substring(sFieldsTemp.Length - 1) != ",")
			{
				sFieldsTemp += ",";
			}

			aSortFields.ClearArray();

			//Add all sort fields to fields
			do
			{
				//Read the sort field and sort direction
				sWork = goTR.ExtractString(sSort, (int)lCount, ",").Trim(' ');
				if (sWork[0] == clC.EOT)
				{
					break;
				}
				if (sWork == "")
				{
					goto NextSortField;
				}
				//Chop off direction: ASC or DESC
				sWork = goTR.ExtractString(sWork, 1, " ");
				if (sWork[0] == clC.EOT || sWork == "")
				{
					goto NextSortField;
				}
				//Normalizing link by adding %%GID_ID was done earlier.
				//Look for the field plus ',' not to 'find' the field if it has |SUM, |AVG, etc appended
				if (sFieldsTemp.ToUpper().IndexOf(sWork.ToUpper() + ",") + 1 == 1)
				{
					//Field found in pos 1 - skip it
				}
				else
				{
					//Field not in pos 1
					if (sFieldsTemp.ToUpper().IndexOf(", " + sWork.ToUpper() + ",") + 1 < 1)
					{
						//Field not found, add it
						sFields += ", " + sWork;
						sFieldsTemp += " " + sWork + ",";
						aSortFields.AddInfo(sWork);
					}
				}
	NextSortField:
				lCount = lCount + 1;
			} while (true);

			//MI 4/17/09 Added testing COUNT on this level. 
			//Process fields unless the query is for COUNT
			if (!(sMode == "COUNT"))
			{
				//Process fields
				lCount = 1;
				do
				{
					sWork = goTR.ExtractString(sFields, (int)lCount, ",").Trim(' ');
					//DEBUG
					//If InStr(UCase(sWork), UCase("LNK_CreatedBy_US")) > 0 Then Stop
					if (sWork[0] == clC.EOT || sWork == "")
					{
						break;
					}
					if (goTR.GetPrefix(sWork) == "LNK_")
					{
						//The field is a link
						if (sWork.IndexOf("%%") + 1 > 0) //InStr()
						{
							sLink = goTR.ExtractString(sWork, 1, "%%");
							sField = goTR.ExtractString(sWork, 2, "%%");
						}
						else
						{
							sLink = sWork;
							sField = "GID_ID";
							sWork = sLink + "%%" + sField;
						}
						if (goTR.GetPrefix(sField) == "LNK_")
						{
							goErr.SetError(35015, sProc, "", sWork);
							//35015: Multi-level link not allowed in FIELDS: '[1]'.
							//Return "SELECT '" & sProc & " Error: multi-level link not allowed in FIELDS: [" & sWork & "].' AS 'ERROR'"
						}
						sLinkTable = goTR.GetFileFromLinkName(sLink);
						//Determine the link type (1 or many) to compose the JOIN statement(s)
						sLinkType = goData.LKGetType(sTable, sLink);
						if (sLinkType == "")
						{
							goErr.SetError(10108, sProc, "", sTable + "." + sLink);
							//10108: Internal error: invalid link name '[1]'.
							//Return "SELECT '" & sProc & " Error: invalid link [" & sTable & "." & sLink & "].' AS 'ERROR'"
						}
						if (!goData.IsFieldValid(sLinkTable, sField))
						{
							goErr.SetError(10107, sProc, "", sTable + "." + sWork);
							//10107: Internal error: invalid field name '[1]'.
							//Return "SELECT '" & sProc & " Invalid field: [" & sTable & "." & sWork & "].' AS 'ERROR'"
						}
						if (sLinkType.Substring(sLinkType.Length - 1) == "1") //N1 type
						{
							//Link is n to 1

							if (bUseLinkPermissions)
							{
								//TOP is not 0, return SUBSELECT(s)

								if (sMode == "GROUPBY" || sMode == "GROUPBYWITHROLLUP")
								{
									//Return the column as:
									//   idt.[US00001_TXT_Code] as 'lnk_createdby_us%%TXT_Code',
									//instead of a subselect because GROUPBY requires a real column, which we
									//pull from the idt table in this case (GROUPBY or GROUPBYWITHROLLUP).
									if (sSortColumnsTemp.ToUpper().IndexOf("[" + sWork.ToUpper() + "]") + 1 < 1)
									{
										//Add the field - a column was not added for it already
										lWork = lWork + 1;
										lRow = tColumnMap.Seek("Value", sWork.ToUpper());
										if (lRow > 0)
										{
											sIDTColumn = Convert.ToString(tColumnMap.GetVal("Text", lRow));
											//MI 3/2/10 Added ISNULL to return '(empty link)' when the link is empty, as opposed to the field value being blank
											//THis is not good. First, the _GROUPING column has '(Empty)' instead of NULL and this is not
											//consistent with the behavior with link perms off where NULLs and blanks are lumped together.
											//sWork2 = "ISNULL(idt." & sIDTColumn & ", '(Empty)') AS '" & sWork & "'"
											sWork2 = "idt." + sIDTColumn + " AS '" + sWork + "'";
										}
										else
										{
											goErr.SetError(35000, sProc, "The column for '" + sWork + "' could not be found in idt table (sTable: '" + sTable + "' sFields: '" + sFields + "' sSort: '" + sSort + "')");
										}
										if (lWork > 1)
										{
											sResult += ", " + "\r\n";
											sSortColumnsTemp += ", ";
										}
										sResult += "\t" + sWork2;
										if (sMode == "GROUPBYWITHROLLUP")
										{
											//Add the GROUPING column for sort field columns
											if (aSortFields.SeekInfo(sWork) > 0)
											{
												//Sort column, add GROUPING
												sResult += ", " + "\r\n" + "\t" + "GROUPING(" + "idt." + sIDTColumn + ") AS '" + "INT_" + sWork + "_GROUPING'";
											}
										}
										sSortColumnsTemp += sWork2;
										sSortColumnsTemp += "[" + sWork + "]";
										//Replace [lnk_createdby_us%%TXT_Code] in sSortStringInMainStatement with idt.[US00001_TXT_Code]
										sSortStringInMainStatement = goTR.Replace(sSortStringInMainStatement, "[" + sWork + "]", "idt." + sIDTColumn, false);
									}
								}
								else
								{
									//------------ NEW CODE OBSERVES LINKED FILE'S READ PERM ----------
									//Generate a subselect for the link field
									//MI 4/29/09 Added aliasing link table to distinguish the subselect table from the main table in same-file links.
									//In this case, the alias is always table name & "00000". 
									//Has a subselect for the same link fields been added already?
									//Links in sSortColumnsTemp are in [LNK_Related_CO%%TXT_CompanyName] format

									//DEBUG
									//If UCase(sWork) = "LNK_CREATEDBY_US%%SYS_NAME" Then Stop
									//END DEBUG

									if (sSortColumnsTemp.ToUpper().IndexOf("[" + sWork.ToUpper() + "]") + 1 < 1)
									{
										//Add the field - a subselect was not added for it already
										lWork = lWork + 1;
										//First put together par_sAdditionalIni string with the statement to prepend to WHERE
										sWork3 = "";
										goTR.StrWrite(ref sWork3, "WHEREPREPEND", "[" + sLinkTable + "00000].[GID_ID] = [" + sTable + "].[GID_" + goTR.GetLinkPartFromLinkName(sLink) + "_" + sLinkTable + "]");
										sWork2 = "(";
										if (lTop == 0)
										{
											sWork2 += goData.GenerateSQL("", sLinkTable, sField, "", "", 0, false, "", "SIMPLESELECT", "", true, "R", false, false, false, sWork3);
										}
										else
										{
											sWork2 += goData.GenerateSQL("", sLinkTable, sField, "", "", 1, false, "", "SIMPLESELECT", "", true, "R", false, false, false, sWork3);
										}
										sWork2 += ") AS '" + sWork + "'";
										if (lWork > 1)
										{
											sResult += ", " + "\r\n";
											sSortColumnsTemp += ", ";
										}
										sResult += sWork2;
										sSortColumnsTemp += "[" + sWork + "]";
									}

									//Generate a subselect for the link's SYS_Name if the requested field is GID_ID. 
									//SYS_Name is needed only when the field is GID_ID because in that case 
									//the link is editable and must be sorted by SYS_Name. SYS_Name is also needed 
									//to retrieve a 'friendly' value of the link.
									if (sField.ToUpper() == "GID_ID")
									{
										//Add a subselect for the SYS_Name field, unless added already
										if (sSortColumnsTemp.ToUpper().IndexOf("[" + sLink.ToUpper() + "%%SYS_NAME]") + 1 < 1)
										{
											lWork = lWork + 1;
											//First put together par_sAdditionalIni string with the statement to prepend to WHERE
											sWork3 = "";
											goTR.StrWrite(ref sWork3, "WHEREPREPEND", "[" + sLinkTable + "00000].[GID_ID] = [" + sTable + "].[GID_" + goTR.GetLinkPartFromLinkName(sLink) + "_" + sLinkTable + "]");
											sWork2 = "(";
											if (lTop == 0)
											{
												sWork2 += goData.GenerateSQL("", sLinkTable, "SYS_Name", "", "", 0, false, "", "SIMPLESELECT", "", true, "R", false, false, false, sWork3);
											}
											else
											{
												sWork2 += goData.GenerateSQL("", sLinkTable, "SYS_Name", "", "", 1, false, "", "SIMPLESELECT", "", true, "R", false, false, false, sWork3);
											}
											sWork2 += ") AS '" + sLink + "%%SYS_Name'";
											if (lWork > 1)
											{
												sResult += ", " + "\r\n";
												sSortColumnsTemp += ", ";
											}
											sResult += sWork2;
											sSortColumnsTemp += "[" + sLink + "%%SYS_Name]"; //MI 1/8/10 Fixed bug: changed sLinkTable to sLink
										}
									}
									//------------- END NEW CODE --------------------

								}

							}
							else
							{

								//--------- ORIGINAL CODE IGNORES LINK FILE'S PERMISSIONS ----------
								//Get or generate a table alias
								object temp_aAliases = aAliases;
								sAliasTable = goData.LKGetTableAlias(sTable, sLink, ref temp_aAliases);
									aAliases = (Selltis.BusinessLogic.clArray)temp_aAliases;
								if (sAliasTable == "")
								{
									goErr.SetError(35011, sProc, "", sTable + "." + sLink);
									//35011: goData.LKGetTableAlias failed on TableAlias.Link: '[1]'.
									//Return "SELECT '" & sProc & " Error: goData.LKGetTableAlias failed on TableAlias.Link: [" & sTable & "." & sLink & "].' AS 'ERROR'"
								}
								//MI 11/3/09 Commented
								//sWork2 = "[" & sAliasTable & "].[" & sField & "]"
								//MI 11/3/09 New code
								sWork2 = GetFieldExpression(sAliasTable, sField, sMode);
								//Has the same link been added already?
								iPos = 0;
								do
								{
									iPos = sSortColumnsTemp.ToUpper().IndexOf(sWork2.ToUpper(), iPos) + 1;
									if (iPos > 0)
									{
										if (goTR.FromTo(sSortColumnsTemp.ToUpper(), iPos + sWork2.Length, iPos + sWork2.Length) == ")")
										{
											//No match, go to next
											bAddField = true;
										}
										else
										{
											bAddField = false;
											break;
										}
									}
									else
									{
										bAddField = true;
										break;
									}
								} while (true);
								if (bAddField)
								{
									lWork = lWork + 1;
									//Add JOIN statement
									//Example with CN LNK_Related_CO
									//JOIN CO ON CO.[GID_ID] = CN.[GID_Related_CO]				
									sToAdd = sJoinWord + "JOIN [" + sLinkTable + "] [" + sAliasTable + "] ON " + "[" + sAliasTable + "].[GID_ID] = " + "[" + sTable + "].[GID_" + goTR.GetLinkPartFromLinkName(sLink) + "_" + sLinkTable + "]";
									if (sJoin.ToUpper().IndexOf(sToAdd.ToUpper()) + 1 < 1)
									{
										sJoin += "\r\n" + sToAdd;
									}
									//MI 4/17/09 Commenting select Case because If not sMode = "COUNT" test is added around this whole section
									//Add the link field to columns
									//Select Case sMode
									//    Case "COUNT"
									//        'Don't need fields
									//    Case Else
									if (lWork > 1)
									{
										sResult += ", " + "\r\n" + "\t";
										sSortColumnsTemp += ", ";
									}
									//Requested column
									sResult += sWork2 + " AS '" + sWork + "'";
									if (sMode == "GROUPBYWITHROLLUP")
									{
										//Add the GROUPING column for sort field columns
										if (aSortFields.SeekInfo(sWork) > 0)
										{
											//Sort column, add GROUPING
											sResult += ", " + "\r\n" + "\t" + "GROUPING(" + sWork2 + ") AS '" + "INT_" + sWork + "_GROUPING'";
										}
									}
									sSortColumnsTemp += sWork2;
									if (sMode != "GROUPBY" && sMode != "GROUPBYWITHROLLUP")
									{
										//Add the link's SYS_Name if the requested field is GID_ID. SYS_Name is needed only
										//when the field is GID_ID because in that case the link is editable and
										//must be sorted by SYS_Name. SYS_Name is also needed to retrieve a 'friendly'
										//value of the link. SYS_Name must not be added in GROUPBY modes or it will cause
										//SQL error 8120: 'us.SYS_NAME' is invalid in the select list because it is not 
										//contained in either an aggregate function or the GROUP BY clause.
										if (sField.ToUpper() == "GID_ID")
										{
											if (sSortColumnsTemp.ToUpper().IndexOf("[" + sAliasTable.ToUpper() + "].[SYS_Name]") + 1 < 1)
											{
												sResult += ", " + "\r\n" + "\t" + "[" + sAliasTable + "].[SYS_Name] AS '" + sLink + "%%SYS_Name'";
												sSortColumnsTemp += ", [" + sAliasTable + "].[SYS_Name]";
											}
										}
									}
									//End Select
								}

							}

						}
						else
						{
							//Link is NN or 1N
							//Skip link in this recordset and create a separate recordset
							//for it below. The following is the old code, here for reference.
							//				'Example with CN.LNK_InvolvedWith_CO
							//				'JOIN CN_InvolvedWith_CO
							//				'        ON CN.GID_ID = CN_InvolvedWith_CO.GID_CN
							//				'JOIN CO
							//				'        ON CO.GID_ID = CN_InvolvedWith_CO.GID_CO
							//				sLinkPart = goTr:GetLinkPartFromLinkName(sLink)
							//				sJoin += CR+"JOIN "+sTable+"_"+sLinkPart+"_"+sLinkTable+" ON "+...
							//							sTable+".[GID_ID] = "+sTable+"_"+sLinkPart+"_"+sLinkTable+".[GID_"+sTable+"]"
							//				sJoin += CR+"JOIN "+sLinkTable+" ON "+...
							//							sLinkTable+".[GID_ID] = "+sTable+"_"+sLinkPart+"_"+sLinkTable+".[GID_"+sLinkTable+"]"
							//Add link to the list of N links unless already in the list
							if (sNLinks.ToUpper().IndexOf(sWork.ToUpper() + ", ") + 1 == 0)
							{
								sNLinks += sWork + ", ";
							}
						}
					}
					else
					{
						//Direct field
						switch (sMode)
						{
							case "COUNT":
							break;
								//don't need fields
							default:
								if (sMode == "GROUPBY" || sMode == "GROUPBYWITHROLLUP")
								{
									//'Don't add fields that may be added automatically
									//If sWork = "GID_ID" Then GoTo ProcessNextColumn
									//If sWork = "SYS_NAME" Then GoTo ProcessNextColumn
								}
								sFieldPart = goTR.ExtractString(sWork, 1, "|");
								sCalc = (goTR.ExtractString(sWork, 2, "|")).ToUpper();
								//Test field validity. Skip BI__GROUPBYCOUNT, which is used to get the COUNT_BIG(*) column from SS.
								if (!goData.IsFieldValid(sTable, sFieldPart))
								{
									goErr.SetError(10107, sProc, "", sTable + "." + sFieldPart);
									//10107: Internal error: invalid field name '[1]'.
									//Return "SELECT '" & sProc & " Invalid field: [" & sTable & "." & sWork & "].' AS 'ERROR'"
								}
								//MI 11/3/09 Commented
								//sWork2 = "[" & sTable & "].[" & sWork & "]"
								//MI 11/3/09 New code
								sWork2 = GetFieldExpression(sTable, sFieldPart, sMode);
								if (sCalc != "" && sCalc[0] != clC.EOT)
								{
									//We can assume GROUPBY or GROUPBYWITHROLLUP mode because a SUM, AVG, or other calculation is defined
									switch (sCalc)
									{
										case "SUM":
										case "AVG":
										case "MIN":
										case "MAX":
										case "COUNT":
											sWork2 = sCalc + "(" + sWork2 + ")";
											break;
										default:
											//MED   (MIN + ((MAX-MIN)/2))
											sWork2 = "MIN(" + sWork2 + ") + ((MAX(" + sWork2 + ") - MIN(" + sWork2 + ")) / 2)";
											break;
									}
								}

								//Add the field expression only if it hasn't been added yet
								iPos = 0;
								do
								{
									iPos = sSortColumnsTemp.ToUpper().IndexOf(sWork2.ToUpper(), iPos) + 1;
									if (iPos > 0)
									{
										if (goTR.FromTo(sSortColumnsTemp.ToUpper(), iPos + sWork2.Length, iPos + sWork2.Length) == ")")
										{
											//No match, go to next
											bAddField = true;
										}
										else
										{
											bAddField = false;
											break;
										}
									}
									else
									{
										bAddField = true;
										break;
									}
								} while (true);
								if (bAddField)
								{
									lWork = lWork + 1;
									if (lWork > 1)
									{
										sResult += ", " + "\r\n" + "\t";
										sSortColumnsTemp += ", ";
									}
									sResult += sWork2 + " AS '" + sWork + "'";
									if (sMode == "GROUPBYWITHROLLUP")
									{
										//Add the GROUPING column for sort field columns
										if (aSortFields.SeekInfo(sWork) > 0)
										{
											//Sort column, add GROUPING
											sResult += ", " + "\r\n" + "\t" + "GROUPING(" + sWork2 + ") AS '" + "INT_" + sWork + "_GROUPING'";
										}
									}
									sSortColumnsTemp += sWork2;
								}
								break;
						}
					}
	ProcessNextColumn:
					lCount = lCount + 1;
				} while (true);
			}

            //Clean up trailing comma from list of N links
            //if (sNLinks.Substring(sNLinks.Length - 2) == ", ")
            //         if (sNLinks.Length >= 2 && sNLinks.Substring(sNLinks.Length - 2) == ", ")
            //         {

            //	sNLinks = sNLinks.Substring(0, sNLinks.Length - 2);
            //}

            if (!string.IsNullOrEmpty(sNLinks) && sNLinks.Length >= 2 && sNLinks.Substring(sNLinks.Length - 2) == ", ")
            {
                sNLinks = sNLinks.Substring(0, sNLinks.Length - 2);
            }



            // ---------------- FROM ---------------- 
            sResult += " " + "\r\n" + "FROM [" + sTable + "] ";

			// ------------ WHERE and JOINs ---------------- 
			//WHERE and WHERE-related JOINs are not needed in this query
			//because they were done in the query above that loads IDs
			//into @tIDTable
			//Add previously generated JOINS (for SELECT and ORDER BY columns)
			//to the result string
			sResult += sJoin;

			// ---------------- ORDER BY ---------------- 
			if (lTop != 0)
			{
				//TOP is not 0, add ORDER BY statement
				switch (sMode)
				{
					case "COUNT":
					break;
							//Don't need ORDER BY for COUNT(*)
					case "GROUPBY": //MI 12/13/09
						if (sSortStringInMainStatement.Trim(' ') == "")
						{
							sSortString = "DTY_CreationTime ASC";
							sSortStringInMainStatement = sSortString;
						}
						//Remove ASC and DESC from sort string for GROUP BY string
						sGroupByString = sSortStringInMainStatement;
						sGroupByString = goTR.Replace(sGroupByString, " ASC", "", false);
						sGroupByString = goTR.Replace(sGroupByString, " DESC", "", false);
						sResult += "\r\n" + "GROUP BY " + sGroupByString;
						sResult += "\r\n" + "ORDER BY " + sSortStringInMainStatement;
						break;
					case "GROUPBYWITHROLLUP": //MI 1/8/09
						if (sSortStringInMainStatement.Trim(' ') == "")
						{
							sSortString = "DTY_CreationTime ASC";
							sSortStringInMainStatement = sSortString;
						}
						//Remove ASC and DESC from sort string for GROUP BY string
						sGroupByString = sSortStringInMainStatement;
						sGroupByString = goTR.Replace(sGroupByString, " ASC", "", false);
						sGroupByString = goTR.Replace(sGroupByString, " DESC", "", false);
						sResult += "\r\n" + "GROUP BY " + sGroupByString + " WITH ROLLUP";
						sResult += "\r\n" + "ORDER BY " + sSortStringInMainStatement;
						break;
					default:
						//If sSort <> "" Then    '*** MI 4/17/06
						if (sSortStringInMainStatement.Trim(' ') != "") //*** MI 4/17/06
						{
							sResult += "\r\n" + "ORDER BY " + sSortStringInMainStatement;
						} //*** MI 4/17/06
						break;
						//Else    '*** MI 4/17/06
						//    sResult &= vbCrLf & "ORDER BY " & sTable & ".[SYS_Name]"    '*** MI 4/17/06
						//End If    '*** MI 4/17/06
				}
			}

	FinishInCountMode:

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sResult;

		}

		private string GenerateSortColumns(string par_sSort, string par_sTable, bool par_bAddSeqIDToSort, bool par_bReverseSort, ref clArray aAliases, ref string sSortString, ref string sSortColumnsTemp, ref string sSortColListTemp, ref string sSortNormalized, bool bUseLinkPermissions = true, string par_sMode = "SELECT")
		{
			//MI 1/8/10 Added support for GROUPBYWITHROLLUP mode.
			//MI 12/19/09 Added par_sMode, added deling with GROUPBY mode.
			//MI 11/10/09 Fixed error when sort was on a link field and link permissions were on.
			//MI 11/3/09 Added support for date grouping fields (DTY DTQ DTM DTD).
			//MI 6/9/09 Conditionalized code based on bUseLinkPermissions
			//PARAMETERS:
			//       ...
			//       par_bAddSeqIDToSort: add sequence ID to sort. Ignored if par_sMode is GROUPBY.
			//       ...
			//       par_sMode: Rowset mode (SELECT, COUNT, GROUPBY, SIMPLESELECT, etc).

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sSort = par_sSort;
			string sWork = null;
			string sWork2 = null;
			string sWork3 = null;
			long lCount = 0;
			long lWork = 0;
			string sDirection = null;
			long lSpace = 0;
			string sLink = null;
			string sField = null;
			string sLinkTable = null;
			string sLinkType = null;
			string sAliasTable = null;
			bool bAddField = false;
			int iPos = 0;

			//Try
			//MI 9/28/12 Reenabling
			//MI 9/20/12 Disabling the link permissions feature
			//bUseLinkPermissions = True

			//All ORDER BY columns must be included in the SELECT columns
			//because DISTINCT is used.
			if (sSort != "")
			{
				sWork = "";
				sWork2 = "";
				sWork3 = "";
				lCount = 1;
				sSortString = "";
				sDirection = "";
				sSortColListTemp = "";
				sSortNormalized = "";
				lWork = 0;
				//Add SS identity field BI__ID (sequential ID) as the last sort field
				//Code below will remove it if it appears earlier in the string

				//MI 12/19/09 Add BI__ID field if necessary
				switch (par_sMode.ToUpper())
				{
					case "GROUPBY":
					case "GROUPBYWITHROLLUP":
					case "COUNT":
					break;
						//Nothing to add. In GROUPBY, ORDER BY must be explicit and it defines the GROUPBY clause; in COUNT there is no ORDER BY at all.
					default:
						if (par_bAddSeqIDToSort)
						{
							if (goData.IsFieldValid(par_sTable, "BI__ID"))
							{
								if (par_bReverseSort)
								{
									//The 'primary' direction is backwards
									sSort += ", [BI__ID] DESC";
								}
								else
								{
									//Normal forward direction
									sSort += ", [BI__ID] ASC";
								}
							}
						}
						break;
				}

				do
				{
					sWork = goTR.ExtractString(sSort, (int)lCount, ",").Trim(' ');
                    //if (sWork[0] == clC.EOT || sWork == "")
                    if (!string.IsNullOrEmpty(sWork) && sWork[0] == clC.EOT || sWork == "")
                    {
                        break;
					}
					//Check for 'ASC' or 'DESC'
					lSpace = sWork.IndexOf(" ") + 1;
					if (lSpace > 0)
					{
						sDirection = ((sWork.Substring((int)lSpace)).Trim(' ')).ToUpper();
						//If the direction is anything other than ASC, A, D, or DESC, remove the string
						//to prevent SQL injection
						switch (sDirection)
						{
							case "ASC":
							case "A":
								sDirection = " ASC";
								break;
							case "DESC":
							case "D":
								sDirection = " DESC";
								break;
							default:
								sDirection = " ASC";
								break;
						}
						sWork = sWork.Substring(0, (int)(lSpace - 1));
						//MI 5/13/08 Added
						sWork = goTR.RemoveOuterBrackets(sWork);
					}
					else
					{
						sDirection = " ASC";
					}
					//Disallow binary fields in sort order
					switch (goTR.GetPrefix(sWork))
					{
						case "BIN_":
						case "EML_":
						case "FIL_":
						case "MMO_":
						case "MMR_":
						case "URL_": //MI 3/28/14 Added MMR
							goErr.SetError(35006, sProc, "", sWork);
							break;
							//Return "SELECT '" & sProc & " Error: Memo and binary fields are not allowed in SORT: [" & sWork & "].' AS 'ERROR'"
					}
					if (goTR.GetPrefix(sWork) == "LNK_")
					{
						//Link
						//The sort is limited to a single hop N1 link only
						if (sWork.IndexOf("%%") + 1 > 0) //InStr()
						{
							sLink = goTR.ExtractString(sWork, 1, "%%");
							sField = goTR.ExtractString(sWork, 2, "%%");
						}
						else
						{
							sLink = sWork;
							sField = "GID_ID";
							sWork = sLink + "%%" + sField;
						}
						if (goTR.GetPrefix(sField) == "LNK_")
						{
							goErr.SetError(35007, sProc, "", sWork);
							//Return "SELECT '" & sProc & " Error: Multi-level link not allowed in SORT: [" & sWork & "].' AS 'ERROR'"
						}
						//Disallow binary fields in sort order
						switch (goTR.GetPrefix(sField))
						{
							case "BIN_":
							case "EML_":
							case "FIL_":
							case "MMO_":
							case "MMR_":
							case "URL_": //MI 3/28/14 Added MMR
								goErr.SetError(35006, sProc, "", sWork);
								break;
								//Return "SELECT '" & sProc & " Error: memo and binary fields are not allowed in SORT: [" & sWork & "].' AS 'ERROR'"
						}
						sLinkTable = goTR.GetFileFromLinkName(sLink);
						//Determine the link type (1 or many) to compose the JOIN statement(s)
						sLinkType = goData.LKGetType(par_sTable, sLink);
						if (sLinkType == "")
						{
							goErr.SetError(10107, sProc, "", par_sTable + "." + sLink);
							//10108: Internal error: invalid link name '[1]'.
							//Return "SELECT '" & sProc & " Error: invalid link [" & par_sTable & "." & sLink & "].' AS 'ERROR'"
						}
						if (!goData.IsFieldValid(sLinkTable, sField))
						{
							goErr.SetError(10107, sProc, "", par_sTable + "." + sWork);
							//10107: Internal error: invalid field name '[1]'.
							//Return "SELECT '" & sProc & " Invalid field: [" & par_sTable & "." & sWork & "].' AS 'ERROR'"
						}
						//Add only N1 links to ORDER BY line. NN and 1N links are
						//not supported for sorting for now. They are 
						//not sorted alphabetically through JOINs and can't be
						//used for sorting here. The sorting must be implemented
						//in the middle tier.
						if (sLinkType.Substring(sLinkType.Length - 1) == "1")
						{
							//Link is n to 1
							if (bUseLinkPermissions)
							{
								//---------- NEW WAY --------
								//MI 5/4/09 Added this
								//Since N1 links are now returned via subselects, creating a JOIN above is unnecessary
								//The subselects return columns with 'AS [LNK_LinkName_File%%PREFIX_FieldName]', and 
								//ORDER BY uses this column alias.
								sWork2 = "[" + sWork + "]";
								sWork3 = "[" + sWork + "]";
								//-------- END NEW WAY ------
							}
							else
							{
								//--------- OLD WAY --------
								//MI 6/9/09 Uncommented this
								//Get or generate a table alias
								object temp_aAliases = aAliases;
								sAliasTable = goData.LKGetTableAlias(par_sTable, sLink, ref temp_aAliases);
									aAliases = (Selltis.BusinessLogic.clArray)temp_aAliases;
								if (sAliasTable == "")
								{
									goErr.SetError(35008, sProc, "", par_sTable + "." + sLink);
									//35008: goData.LKGetTableAlias failed on TableAlias.Link
									//Return "SELECT '" & sProc & " Error: goData.LKGetTableAlias failed on TableAlias.Link: [" & par_sTable & "." & sLink & "].' AS 'ERROR'"
								}
								sWork2 = "[" + sAliasTable + "].[" + sField + "]"; //goTR.GetFileFromLinkName(sLink) & ".[" & sField & "]"
								//MI 11/3/09 New code for sSortString
								sWork3 = GetFieldExpression(sAliasTable, sField, par_sMode);
							}

							//Add the link only if it hasn't been added already
							iPos = 0;
							do
							{
								iPos = sSortColumnsTemp.ToUpper().IndexOf(sWork2.ToUpper(), iPos) + 1;
								if (iPos > 0)
								{
									if (goTR.FromTo(sSortColumnsTemp.ToUpper(), iPos + sWork2.Length, iPos + sWork2.Length) == ")")
									{
										//No match, go to next
										bAddField = true;
									}
									else
									{
										bAddField = false;
										break;
									}
								}
								else
								{
									bAddField = true;
									break;
								}
							} while (true);

							if (bAddField)
							{
								//Add the link to the main ORDER BY string and temporary sort strings
								lWork = lWork + 1;
								if (lWork > 1)
								{
									sSortString += ", ";
									sSortColumnsTemp += ", ";
									sSortColListTemp += ", ";
									sSortNormalized += ", ";
								}
								sSortString += sWork3 + sDirection;
								sSortColumnsTemp += sWork2;
								sSortColListTemp += sWork;
								sSortNormalized += sWork + sDirection;
							}
						}
						else
						{
							goErr.SetError(35009, sProc, "", par_sTable + "." + sWork);
							//Return "SELECT '" & sProc & " Error: SORT doesn't support 'multi' (NN and 1N) links. Link: [" & par_sTable & "." & sWork & "].' AS 'ERROR'"
						}
					}
					else
					{
						//Direct field
						if (!goData.IsFieldValid(par_sTable, sWork))
						{
							goErr.SetError(10107, sProc, "", par_sTable + "." + sWork);
							//10107: Internal error: invalid field name '[1]'.
							//Return "SELECT '" & sProc & " Invalid field: [" & par_sTable & "." & sWork & "].' AS 'ERROR'"
						}
						sWork2 = "[" + par_sTable + "].[" + sWork + "]";
						//MI 11/3/09 New code for sSortString
						sWork3 = GetFieldExpression(par_sTable, sWork, par_sMode);

						//Add the column only if it hasn't been added already
						iPos = 0;
						do
						{
							iPos = sSortColumnsTemp.ToUpper().IndexOf(sWork2.ToUpper(), iPos) + 1;
							if (iPos > 0)
							{
								if (goTR.FromTo(sSortColumnsTemp.ToUpper(), iPos + sWork2.Length, iPos + sWork2.Length) == ")")
								{
									//No match, go to next
									bAddField = true;
								}
								else
								{
									bAddField = false;
									break;
								}
							}
							else
							{
								bAddField = true;
								break;
							}
						} while (true);

						if (bAddField)
						{
							lWork = lWork + 1;
							if (lWork > 1)
							{
								sSortString += ", ";
								sSortColumnsTemp += ", ";
								sSortColListTemp += ", ";
								sSortNormalized += ", ";
							}
							sSortString += sWork3 + sDirection;
							sSortColumnsTemp += sWork2;
							sSortColListTemp += sWork;
							sSortNormalized += sWork + sDirection;
						}
					}
					lCount = lCount + 1;
				} while (true);
			}
			else
			{
				switch (par_sMode.ToUpper())
				{
					case "GROUPBY":
					case "GROUPBYWITHROLLUP":
						goErr.SetError(35000, sProc, "Sort is not defined in " + par_sMode.ToUpper() + " mode. Define one sort field such as DTM_CreationTime or MLS_Status. This field will be used for grouping records to be calculated.");
						break;
						//MessTranslate
					default:
						//Use SS identity field BI__ID when sort is not specified
						if (par_bAddSeqIDToSort)
						{
							if (goData.IsFieldValid(par_sTable, "BI__ID"))
							{
								//MI 5/13/08 Fixed what would've resulted in sSortString having "[xx].[BI__ID ASC] ASC"
								sSort = "BI__ID";
								sSortColumnsTemp = "[" + par_sTable + "].[" + sSort + "]";
								sSortColListTemp = "BI__ID";
								if (par_bReverseSort) //MI 10/10/08 added
								{
									//Sort direction is backwards
									sSortString = "[" + par_sTable + "].[" + sSort + "] DESC";
									sSortNormalized = sSort + " DESC";
								}
								else
								{
									//Normal, forward direction
									sSortString = "[" + par_sTable + "].[" + sSort + "] ASC";
									sSortNormalized = sSort + " ASC";
								}
							}
						}
						break;
				}
			}
			//sSort has normalized links from this point down
			sSort = sSortNormalized;

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sSort;

		}

		private bool IsFieldInvalid(string par_sFieldName, string par_sTable)
		{
			//MI 8/25/10 Created.
			//PURPOSE:
			//       Checks whether the middle tier thinks the field is not valid. When the rowset
			//       gets a SQL error due to a missing field or link, it adds that field or link
			//       to a session var InvalidFields_<fileName> (comma-delimited). This method 
			//       checks whether the field exists in the main file's InvalidFields var. 
			//       In case of a link, it also looks in InvalidFields for the linked file. 
			//       If the field is found in either var, this method returns True (invalid).
			//PARAMETERS:
			//       par_sFieldName: System name of the field or link or link%%field to test
			//       par_sTable: File name of the field's or link's file.
			//RETURNS:
			//       True if the field is found in the InvalidFields session var (is invalid),
			//       otherwise False.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			bool bIsInvalid = false;
			string sLinkToTest = null;
			string sInvalidFields = null;
			string sInvalidFieldsTemp = null;

			par_sFieldName = par_sFieldName.ToUpper();

			//sInvalidFields can have:
			//       TXTFieds, LNK_Related_CN (exclude all fields of that link, i.e. LNK_Related_CN%%TXT_NameLast)
			//       TXT_field,LNK_Related_CN%%TXT_Field1,LNK_Related_CN%%Field2 (exclude only the defined fields)
			//       TXT_Field,LNK_Related_CN%%GID_ID (exclude only GID_ID in Related CN link, include all other Related CN fields)
			sInvalidFields = Convert.ToString(goP.GetVar("InvalidFields_" + par_sTable).ToString() + ",").ToUpper();

			//Try

			//MI 8/24/10 Is the field marked as invalid in SQL schema? If so, skip it.
			bIsInvalid = false;
			if (goTR.GetPrefix(par_sFieldName) == "LNK_")
			{
				//Link
				if (par_sFieldName.IndexOf("%%") + 1 < 1)
				{
					sLinkToTest = par_sFieldName;
					par_sFieldName += "%%GID_ID";
				}
				else
				{
					sLinkToTest = goTR.ExtractString(par_sFieldName, 1, "%%");
				}
				//Test the link, skip if invalid
				if (sInvalidFields.IndexOf(sLinkToTest) + 1 > 0)
				{
					return true;
				}
				//Test the link field against InvalidFields of this file 
				if (sInvalidFields.IndexOf(par_sFieldName + ",") + 1 > 0)
				{
					return true;
				}
				//Test the link field against InvalidFields of the linked file 
				sInvalidFieldsTemp = Convert.ToString(goP.GetVar("InvalidFields_" + goData.LK_GetFile(par_sTable, par_sFieldName)).ToString() + ",").ToUpper();
				if (sInvalidFieldsTemp.IndexOf(goTR.ExtractString(par_sFieldName, 2, "%%") + ",") + 1 > 0)
				{
					return true;
				}
			}
			else
			{
				//Field
				if (sInvalidFields.IndexOf(par_sFieldName + ",") + 1 > 0)
				{
					return true;
				}
			}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return bIsInvalid;

		}

		public string GenerateSQL(string par_sFilterSort = "", string par_sTable = "", string par_sFields = "", string par_sCondition = "", string par_sSort = "", long par_lTop = -1, bool par_bReadOnly = false, string par_sGenFieldDefs = "", string par_sMode = "", string par_sTopRecord = "", bool par_bAddSeqIDToSort = true, string par_sAccessType = "R", bool par_bGetAllUsersUnsharedRecs = false, bool par_bReverseSort = false, bool par_bSysFileFullPermissions = false, string par_sAdditionalIni = "")
		{
			//MI 9/28/12 Reenabling USELINKPERMISSIONS to revert world coming to an end.
			//MI 9/20/12 Disabled USELINKPERMISSIONS feature (now it's always on) to address an error due to malformed query
			//   when TOP=0 and the filter involves a link (QT to QL).
			//MI 3/23/12 Added 4th parameter value to calls to goTR.GetSQLCondition(). Ensures that no records
			//   are returned in the primary table if they have no links in the link field in the condition.
			//MI 6/9/10 Added GetFieldExpression calls to N link sections to fix bug where LINK%%DTx field causes an invalid column error.
			//MI 2/24/10 Replaced sLinkingTableAlias with sLinkingTable to fix a big where the file name was blank
			//MI 2/17/10 In columns mode test, changed from If par_sMode to If sMode per CS.
			//MI 1/8/09 Added GROUPBYWITHROLLUP mode.
			//MI 12/10/09 Implementing GROUPBY, COUNT modes.
			//MI 11/11/09 Documented GROUPBY mode.
			//MI 11/3/09 Added support for date grouping fields (DTY DTQ DTM DTD).
			//MI 11/3/09 Changing how empty sFields is interpreted. Until now it meant 'all fields',
			//from now it means ID and Name only.
			//MI 10/15/09 Added support for SHOWTOP and FILE keywords to support direct VIE_ MD syntax.
			//MI 9/1/09 Started adding support for DTE field in SORT order, but stopped. DTE can't be treated as DTT
			//           because its time component would make subsequent sort fields unused.
			//MI 6/4/09 Adding observing WOP property USELINKPERMISSIONS. By default is on. When off, links are evaluted the "old" way.
			//MI 5/27/09 Wrapping NN link queries at the bottom of this method with temp tables to allow DISTINCT _and_ support memos (ntext).
			//MI 4/14/09 Added par_sAdditionalIni.
			//MI 4/3/09 MOved from clData to clSQL.
			//MI 3/17/09 Added par_bSysFileFullPermissions.
			//MI 12/10/08 Removed Trace logging when redundant conditions are removed.
			//MI 11/11/08 Removed par_iLinksTop and par_sLinksTop parameters.
			//MI 11/4/08 Disabled limiting NN links because that was limiting linked recs for all based table records.
			//MI 10/22/08 Edited notes.
			//MI 10/22/08 Implemented limiting NN link TOP records according to par_iLinksTop and par_sLinksTop.
			//MI 10/10/08 Added par_bReverseSort, par_iLinksTop, par_sLinksTop.
			//MI 4/9/08 Added par_bGetAllUsersUnsharedRecs.
			//MI 3/24/08 In WHERE condition section for same-file NN links moved sLinkTableNo from second to first LEFT JOIN - 
			//       conditions on these links were returning nothing due to the wrong field evaluation in the link file.
			//MI 10/1/07 Added converting filter condition datetimes to UTC
			//MI 8/14/07 Fixed bug: same-file NN links were always returning the base record.
			//MI 8/10/07 Fixed bug: DTE link field as column was causing an error.
			//MI 5/18/07 Fixed unspecified sort direction being misinterpreted as previous sort field's direction
			//MI 5/7/07 Added sValue = goTR.PrepareForSQL(sValue)
			//MI 3/14/07 Added par_sAccessType, implemented selective permissions.
			//MI 10/20/06 Fixed bug with list of fields being different in @tIDTable and
			//           the SELECT statement that loads it.
			//MI 9/20/06 Raising error when a condition segment is blank (repeated ANDs or ORs).
			//MI 9/19/06 Finished redone AND/OR and single quote parsing in CONDITIONs
			//MI 9/19/06 Fixed issue with no %%GID_ID in TOPREC definition causing an error.
			//MI 9/18/06 Fixed not putting single quotes around numeric values in conditions.
			//MI 9/15/06 Replaced TOPRECSORTFLDVAL_ with TOPREC_.
			//MI 9/14/06 Fixed bug: If the main select statement had same-named fields on different
			//           tables in ORDER BY and 1 or more of those fields wasn't in SELECT columns,
			//           SS was returning an error.
			//MI 9/11/06 Added par_sTopRecord.
			//MI 9/7/06  Added par_bAddSeqIDToSort.
			//MI 8/30/06 Fixed bug: if no field codes defined in GEN fields, SELECT had no fields.
			//           Now adding GID_ID if sFields = "".
			//MI 8/9/06  Fixed issue with datetime value not being valid due to | delimiter.
			//MI 8/7/06  Fixed duplicate N link tables being returned.
			//MI 7/27/06 Finished implementing COUNT mode.
			//MI 7/26/06 Fixed bug where in * and ** mode QL's 'CUR_PriceUnit' wasn't included in
			//           FIELDS because that string was found in 'CUR_PriceUnitAfterDisc'.
			//MI 7/25/06 Started adding par_sMode parameter and support for COUNT(*).
			//MI 7/12/06 Raising real errors.
			//MI 7/10/06 Added converting ' to '' in called methods.
			//MI 6/30/06 Fixing bug where conditions on DTT fields couldn't have 
			//           expressions with > 1 word. Added about DTT conditions to comments.
			//MI 6/17/06 Fixed time being stripped from DTT values in WHERE clauses
			//MI 6/17/06 Fixed single quotes erroneously added around WHERE values
			//MI 6/6/06  Fixed error when GEN_ field definitions contained non-field codes like <%MEID%>
			//MI 5/19/06 Fixed issue with condition on reverse NN links not evaluating correct link table
			//MI 5/16/06 Added support for me and datetime keywords (<%DATE%>) and expressions ('Tomorrow')
			//           in conditions by calling goTr.GetLineValue

			//AUTHOR: MI 2/20/06

			//==> Use goTr:GetFieldPartFromCode to get field from multi-hop links.
			//==> Harden code against dangling AND or OR on WHERE line (if the intermediate statement
			//       is malformed) and other invalid statements.
			//==> Enable support for filtering NULL links explicitly. Notes: What makes this not trivial is that not all
			//tests are supported for NULLs and for those that are (= and <> come to mind), the operators must be changed
			//(from <> to 'IS NOT' and from = to 'IS').

			//PURPOSE:
			//		Generate a SQL SELECT statement from an internal filter/sort string
			//       or SELECT COUNT(*) statement if par_sMode is "COUNT". In the "SELECT" mode
			//		multiple statements are returned as a single string if n-n links are
			//		requested either as columns or in the condition or in sort order.
			//		These multiple statements return the following recordsets (which become
			//       datatables in clRowset)
			//			----------------------------------------------------------------------
			//			MAIN recordset includes the requested main table columns and
			//               n-1 link columns, if any are requested in FIELDS= (par_sFields).
			//				It always includes the following columns:
			//					GID_ID
			//					A column for each sort field if DISTINCT keyword is required
			//					(currently, that's always the case)
			//				For each n-1 link, two columns are returned if par_bReadOnly
			//				is False (default):
			//					GID_<LinkName>_ID
			//					LNK_<LinkName>%%SYS_Name
			//				Otherwise, only the requested column is returned.
			//			-----------------------------------------------------------------------
			//			N-N LINK LIST recordset has a list of link names in the order in which
			//				the subsequent recordsets return values from n-n links.
			//				The name of the only column returned is 'LinkName'.
			//			-----------------------------------------------------------------------
			//			LINK[n] recordset returns the data from an NN or 1N link.
			//				The columns returned depend on what was requested:
			//				If the link only was requested (LNK_CreatedBy_US or LNK_CreatedBy_US%%GID_ID),
			//				the columns are:
			//						MainRecID	GID_ID	SYS_Name
			//				The reason for returning both the ID and Name is that the Name is 
			//				required for sorting link values by the middle tier. The Name is also
			//				required for the presentation and 'friendly' identification of the link.
			//				in rowsets (doRS:GetFieldVal("LNK_xxxx_xx",1) returns a CR-delimited
			//				list of Names of linked records).
			//				If a field from the link was requested (LNK_CreatedBy_US%%SYS_Name or
			//				LNK_CreatedBy_US%%TXT_NameLast or LNK_CreatedBy_US%%LNK_Is_Contact),
			//				the columns are:
			//						MainRecID	SYS_Name	<FieldRequested>
			//				For example:
			//						MainRecID	GID_ID		SYS_Name
			//						MainRecID	SYS_Name	TXT_NameLast
			//               The number of NN links returned can be controlled with par_iLinksTop and
			//               par_sLinksTop parameters (see below).
			//			-----------------------------------------------------------------------
			//SYNTAX (incomplete, see 'PARAMETERS' below for a complete list):
			//		sResult=GenerateSQL([par_sFilterSort][,par_sTable=""[,par_sFields=""[,par_sCondition=""[,_
			//                           par_sSort=""[,par_lTop=-1[,par_bReadOnly=False[,par_sGenFieldDefs=""[, _
			//                           par_sMode="SELECT"]]]]]]]]...)
			//PARAMETERS:
			//		par_sFilterSort: Optional: complete ini-format statement that contains all 
			//           property=value lines in one statement. Properties defined in this ini 
			//           string override values passed via their equivalent parameters, for ex.
			//           TABLENAME= overrides par_sTable, FIELDS= overrides par_sFields, 
			//           CONDITION= overrides par_sCondition, etc.
			//		    If par_sFilterSort is not blank, all other parameters are ignored.
			//			If par_sFilterSort is blank, the other parameters must be provided
			//			as indicated. 
			//           Supported properties (for more notes about each property see comments
			//           under their equivalent parameters below):
			//				TABLENAME= Same as par_sTable parameter.
			//               FILE= If TABLENAME= is not defined, FILE= is substituted.
			//				FIELDS= Same as par_sFields parameter.
			//				CONDITION= Same as par_sCondition parameter.
			//				SORT= Same as par_sSort parameter.
			//				TOP= Number of records to return. Not applicable in 
			//                   GROUPBY, GROUPBYWITHROLLUP and COUNT modes.
			//               SHOWTOP= If TOP is undefined, SHOWTOP is substituted.
			//               GEN_GeneratedFieldName<n>= definition of a generated field (see 
			//                   par_sGenFieldDefs for more details on GEN_ field definitions).
			//               <FieldName>_CALC=SUM, AVG, MIN, MAX, MED is required for each field
			//                   in FIELDS= if MODE is GROUPBY or GROUPBYWITHROLLUP. 
			//                   See par_sGenFieldDefs parameter.
			//               MODE= Same as par_sMode parameter.
			//               TOPREC_<SortFieldName>= value of a sort field used for starting the
			//                   recordset at a particular record. See par_sTopRecord for details.
			//               ADDSEQIDTOSORT= Same as par_bAddSeqIDToSort parameter.
			//               ACCESSTYPE= Same as par_sAccessType parameter.
			//               GETALLUSERSUNSHAREDRECS= Same as par_bGetAllUsersUnsharedRecs parameter.
			//               REVERSESORT= Same as par_bReverseSort parameter.
			//               LINKSTOP= Same as par_iLinksTop parameter.
			//               LINKSTOPLIST= Same as par_sLinksTop parameter.
			//               SYSFILEFULLPERMISSIONS= Same as par_bSysFileFullPermissions parameter.
			//		par_sTable:	The FROM part of the SELECT statement.
			//				Required if par_sFilterSort is blank.
			//		par_sFields: Comma-delimited list of columns or link values to query.
			//               This parameter is ignored in the rowset in edit and add modes and is 
			//               treated as **.
			//				If blank or *, all direct fields will be included from par_sTable table.
			//               If **, all direct fields and all links' GID_ID and SYS_Name
			//               values will be included.
			//               * or ** can appear anywhere in the string, but must be properly
			//               delimited with a comma (,) from other fields. If other fields or 
			//               links are found, they will be added unless * or ** adds them first.
			//               If more than two stars appear (*** or more), they are treated as **.
			//				Fields or links that are stated multiple times or that are duplicates
			//               of fields or links returned by * or ** will be used only once.
			//               Multiple commas with no values between them are skipped. Although
			//               tolerated, they should be avoided to prevent confusion.
			//               LINKS are supported up to the fields of the first hop only
			//               (LNK_XXXXX_YY%%FieldName). Links to the right of %% are not supported.
			//				n-1 links will cause JOIN statements to be added to the query.
			//				n-n links are returned in separate recordsets.
			//				For a link to return the ID, use the syntax 'LNK_Related_Contact'.
			//				For a link to return the Name of the linked record, use the syntax
			//				'LNK_Related_Contact%%SYS_Name'.
			//				For a link to return a field value, use the syntax
			//				'LNK_Related_Contact%%TXT_NameLast'.
			//				Any direct field from the linked record's table can be specified.
			//				Currently, 2nd hop links (links in linked records) are not supported.
			//               ***CALCULATED FIELDS IN GROUPBY/GROUPBYWITHROLLUP MODE*** If par_sMode 
			//               is GROUPBY or GROUPBYWITHROLLUP, a calculation such as 
			//               SUM, AVG, MIN, MAX or MED must be defined for each field. Fields
			//               without calculations will be skipped. Calculation definitions should be 
			//               appended to each field like this:
			//                   FIELDS=CUR_Value|SUM, CUR_Value|AVG, SI__Qty|MIN, SI__Qty|MAX
			//               Only direct fields and BI__GROUPBYCOUNT are supported in GROUPBY modes.
			//               ***GENERATED FIELDS*** (field definitions that have field codes like <%TXT_Text%>)
			//               are supported directly in this parameter or are supported by listing
			//               fields with GEN_ prefix in this parameter and providing the definitions
			//               in the par_sGenFieldDefs parameter. The latter technique is required
			//               for the rowset to properly process generated field definitions.
			//               Regardless whether a definition is provided directly in this parameter
			//               of via par_sGenFieldDefs, this method ignores all static text and returns
			//               each field defined by a field code (<%...%>) as a separate column. 
			//               Formatting within field codes also is ignored (<%xxx LENGTH=yy%>)
			//               Example of a generated field:
			//                   Started on <%DTT_StartTime%> by <%LNK_CreditedTo_US%%SYS_Name%> at <%LNK_At_LO%%TXT_LocationName%>
			//               If a generated field contains a comma, it must be delimited by double-
			//               quotes, ex: "<%TXT_Description%>, started on <%DTT_StartTime%>".
			//               Using double-quotes for one generated field doesn't require using them
			//               for all other fields. This, for example, is supported (CN file):
			//                   TXT_NameLast, TXT_NameFirst, "Created on '<%DTT_OrigCreatedTime%>', last modified on '<%DTT_ModTime%>'", TXT_CityBusiness
			//		par_sCondition:	One or more filter conditions combined with 'AND' or 'OR' keywords.
			//				Data returned are subject to the par_lTop parameter, selective access permissions, 
			//				whether link permissions are on in WOP, security settings, and module considerations.
			//				This procedure converts these conditions to the WHERE and, optionally,
			//				LEFT JOIN or INNER JOIN statements. Parentheses are left intact. For a list of
			//               supported condition symbols and their meaning, see 'ABOUT FILTER CONDITION
			//               METADATA' below.
			//               A condition value for DTE and DTT fields can be any valid date expression such as
			//               DTT_StartTime<'TODAY - 6' or DTT_StartTime>='This year'. If you need to use
			//               an explicit date in a DTT field condition, date and time portions must be delimited
			//               with the pipe '|', i.e. DTT_StartTime>'2006-10-12|13:35:56.000' is valid.
			//               Multihop links (containing %%) are supported without limit. Any number of
			//               hops is supported. Values of conditions on DTT fields are converted to UTC
			//               values as DTT fields are stored in UTC in the database.
			//               As of 4/27/06, testing inequality on NN and 1N links doesn't yield correct results.
			//               For example, this won't return the correct data:
			//                 CONDITION=LNK_InvolvedWith_CO%%LNK_Related_RL%%TXT_RelationshipName<>'Supplier'
			//		par_sSort:	Comma-delimited list of fields and link fields used for sorting in the ORDER BY statement.
			//				If this value is blank, ORDER BY SYS_Name is created. If 'NONE' or '<%NONE%>' is
			//               the value of this parameter, ORDER BY is omitted. This allows unsorted filters,
			//               which have performance advantages in some cases and can be used for 
			//               troubleshooting and maintenance.
			//				If a field is repeated, duplicates are removed.
			//				Each field can be followed by a space and 'ASC', 'A', 'DESC', or 'D'
			//				to specifiy the sort direction (ascending or descending).
			//				A sort 'field' can be any direct field or any N1 link field.
			//               Links in or beyond the first hop are not supported (LNK_xxx_yy%%LNK_aaa_bb).
			//				NN links will be omitted from the ORDER BY statement because their values
			//				are not returned alphabetically by JOINs. Sorting on NN links is 
			//               not supported. If a link field is not defined, GID_ID is substituted, i.e.
			//               LNK_For_PD is interpreted as LNK_For_PD%%GID_ID.
			//				Memo, file, and other large binary fields are not supported neither in SS
			//               nor here).
			//		par_lTop: Number of records to return. 0 returns no records, -1 all records.
			//               Default is -1. Ignored if par_sMode is COUNT. In GROUPBY and GROUPBYWITHROLLUP
			//               modes, TOP limits the data returned, but not the underlying data that is loaded 
			//               into the temp table. This first query is not limited and may involve millions of
			//               records. Since limiting the returned data will cut off grouped/calculated values
			//               and their summaries (in GROUPBYWITHROLLUP), we suggest to test whether there is
			//               more data and, if so, not return any data to the user. For example, if you don't
			//               want the rowset to receive more than 10,000 rows, set par_lTop to 10001 and test:
			//                   If oRS.Count > 10000 Then 'refuse to display the data.
			//		par_bReadOnly: CURRENTLY DOESN'T DO ANYTHING!
			//				Original idea: the SQL statement is being generated for a read-only rowset,
			//				in which case IDs are not returned when link fields or 2nd, 3rd, or 4th hop
			//				links or their fields are requested. The default value is False.
			//       par_sGenFieldDefs: ini-format string that contains definitions of all generated (GEN_)
			//               fields and calculation definitions (only in GROUPBY or GROUPBYWITHROLLUP mode) 
			//               for all fields in par_sFields (FIELDS=). For example, if par_sFields is:
			//                   GEN_CreatedOnAtBy, GEN_001
			//               then this parameter must be written as follows:
			//                   goTr.StrWrite(s, "GEN_CreatedOnAtBy", "Created On <%DTE_StartTime%> at <%TME_StartTime%> by <%MECODE%>")
			//                   goTr.StrWrite(s, "GEN_001", "Hello world")
			//               The result (s) is:
			//                   GEN_CreatedOnAtBy=Created On <%DTE_StartTime%> at <%TME_StartTime%> by <%MECODE%>
			//                   GEN_001=Hello world
			//               If a definition is missing, an error is raised. Note that additional GEN_ data
			//               may be required in different par_sModes. 
			//       par_sMode: Query mode. Supported values:
			//               ""          'Default: evaluated to SELECT. See below.
			//               SELECT      'Standard SELECT statement for clRowset reading data from SS. 
			//                           'More columns may be returned than requested via par_sFields or FIELDS=. 
			//                           'GID_ID and SYS_Name are always returned, which is necessary for the rowset.
			//               COUNT       'SELECT COUNT_BIG(*) statement that returns no of records in the base table.
			//                           'All N links are ignored in this mode.
			//               GROUPBY     'Aggregates-only mode: only SUM, AVG, MIN, MAX, MED (median) for direct fields or BI__GROUPBYCOUNT
			//                           '(COUNT_BIG(*) in SQL query) can be requested as columns in this mode via par_sFields
			//                           '(FIELDS=). Returned columns are all the fields requested in FIELDS= plus all fields and 
			//                           'N1 links in SORT=.
			//                           'This mode requires defining calculations for all FIELDS= by appending them to
			//                           'each field name in FIELDS=, e.g.:
			//                           '   FIELDS=CUR_Value|SUM, CUR_Value|AVG, SI__Quantity|AVG, SI__Quantity|MIN, SI__Quantity|MAX
			//                           'NO LONGER SUPPORTED ALTERNATIVE METHOD FOR DEFINING CALCS: via par_sGenFieldDefs or
			//                           'directly in par_sFilterSort via <FieldName>_CALC= lines. Example for FIELDS=CUR_Value, SI__Quantity:
			//                           '   CUR_VALUE_CALC=SUM, AVG
			//                           '   SI__QUANTITY_CALC=AVG, MIN, MAX
			//                           'Since multiple calculations can be defined for each field, the above example
			//                           'will cause 5 columns to be returned:
			//                           '   SUM(CUR_VALUE), AVG(CUR_VALUE), AVG(SI__QUANTITY), MIN(SI__QUANTITY), MAX(SI__QUANTITY) 
			//                           'The columns will be returned to the rowset with the following names:
			//                           '   CUR_Value|SUM, CUR_Value|AVG, SI__QUANTITY|AVG, SI__QUANTITY|MIN, SI__QUANTITY|MAX 
			//                           'Use the above names in clRowset.GetFieldVal or GetLinkVal, for example:
			//                           '   doRS.GetFieldName("CUR_Value|SUM")
			//                           'or:
			//                           '   doRS.GetFieldName("CUR_Value|SUM", clc.SELL_SYSTEM)
			//               GROUPBYWITHROLLUP   'Same as GROUPBY mode (see above), but with rollup. The GROUP BY
			//                           'statement in the SQL query gets ' WITH ROLLUP' at the end and additional
			//                           'GROUPING columns are returned for each sort field (or N1 link) column. These columns
			//                           'are in the format INT_<SortFieldName>_GROUPING and contain either a 1 or 0,
			//                           'indicating whether the row contains grouping calculations (1) or regular
			//                           'calculations like SUM, AVG, etc (0). 
			//                           'Example: if sort is:
			//                           '   SORT=DTY_Time DESC, LNK_For_PD%%SYS_Name
			//                           'the following columns will be returned in addition to the FIELDS= columns:
			//                           '   DTY_Time   INT_DTY_TIME_GROUPING    LNK_FOR_PD%%SYS_NAME    INT_LNK_FOR_PD%%SYS_NAME_GROUPING
			//               SIMPLESELECT    'A complete SELECT statement without @tIDTable, without ORDER BY,
			//                           'and with only the columns that are requested (see SELECT above). 
			//                           'This is used to generate a subselect statement 
			//                           'for ea N1 column in the main query in order to observe 
			//                           'read permissions for the link's table. Optional data such as additional
			//                           'WHERE conditions to be prepended or appended can be sent via par_sAdditionalIni.
			//               JOINANDWHEREONLY   'JOIN and WHERE lines of a simple SELECT statement with only the 
			//                           'columns that are requested (see SELECT above). This is used
			//                           'in NN and 1N recordset queries (returned as recordsets 3 and greater)
			//                           'to observe read permissions in link file(s). Optional data such as additional
			//                           'WHERE conditions can be sent via par_sAdditionalIni.
			//               JOINANDWHEREONLYFORNNLINKS  'JOIN and WHERE lines for NN link queries that are
			//                           coded on the bottom of this method.
			//       par_sTopRecord: Optional: Ini string that identifies the top record at which the recordset
			//               should start. The ini string must contain a value of each par_sSort field in the
			//               following format:
			//                   TOPREC_CHK_ACTIVE=1
			//                   TOPREC_TXT_NameLast=Doe
			//                   TOPREC_TXT_NameLast=John
			//                   TOPREC_BI__ID=4367
			//               Values must be in the "SQL string" format, i.e. ready to pass to SS as literal
			//               strings. For example, datetimes must be expressed as '2007-09-24 13:54:23.456',
			//               MLS_ field values as numeric (not text), and numbers without separators and currency
			//               symbols and with a decimal point. Toprec string is prepared in clUtil.GetTopRec().
			//               To parse sort fields into a collection, call goData.GetFilterSortFields().
			//               Note that the value of the field BI__ID (SS identity column) must be provided even
			//               when this field is not defined in the sort order. It is added automatically by
			//               goData.GetFilterSortFields() and here (unless par_bAddSeqIDToSort is set
			//               to False).
			//               For DTx fields (DTY, DTQ, DTM, DTD), the TOPREC value should be the beginning of the 
			//               period taking the sort direction into consideration. For example, for DTY ASC, the 
			//               value may be '2011-01-01 00:00:00.000' whereas for DTQ DESC the value may be
			//               '2013-06-30 23:59:59.996' (end of Q2 2013).
			//       par_bAddSeqIDToSort: Add sequence ID (BI__ID) to sort. True by default. When True, causes
			//               the field BI__ID (identity column in SS) to be added to the sort string unless it's
			//               in the par_sSort string or it is not a valid field.
			//               Why is this necessary? This is done to make views sorted on distinctive values,
			//               which can be used for programmatically selecting a record using quick select. When
			//               restoring the view state, the last selected record must be selected. If a sort order
			//               causes multiple records to have the same values in all sort fields combined (for
			//               example when sorting on a checkbox or an MLS value), quick select can't filter the
			//               TOP n records that start with a particular record by appending its condition to
			//               the CONDITION statement (ex: AND FIELD>='<QuickSelectValue>'). If quick select were
			//               to do that with a non-distinct sort, the first record returned by TOP n would be
			//               arbitrary. Since BI__ID is guaranteed to be unique, adding this field to the sort
			//               order ensures distinct sort results, thus allowing the TOP n condition, which quick
			//               select adds to the filter, to return a recordset that starts with a particular record.
			//       par_sAccessType: Access type for which to take the selective access filter definition:
			//               R, A, E, D (read, add, edit, delete). Note that you can set full permissions for
			//               'system' files with par_bSysFileFullPermissions parameter.
			//               Normally, GenerateSQL is executed to generate a read query statement. This will
			//               append a filter statement for 'read' access for the current user. When the rowset
			//               needs to edit or delete a record, it checks whether it can read it first by generating
			//               a query with this method. In those cases, the rowset sets par_sAccessType to 'E'
			//               (edit) or 'D' (delete), which makes this method append an edit or delete selective
			//               access filter statement. By using this parameter you can force the rowset to use
			//               Edit or Delete permission for reading data. Note that 'A' (add) is not supported 
			//               because the record is not saved yet, therefore it cannot be read.
			//       par_bGetAllUsersUnsharedRecs: When True all records that are not shared (private records)
			//               will be returned, else only the current user's unshared records
			//               will be returned (False is the default).
			//               WARNING: Set this parameter to True only in code that needs to read data for all users.
			//               For all normal data access leave the parameter at default (false) so that the user sees 
			//               only his/her unshared records.
			//       par_bReverseSort: True indicates that the "primary" direction of the sort is backwards,
			//               for example when the view is browsing 'pages' of data backward, i.e.
			//               the user clicked the |< or < buttons.
			//               This reverses the sort of BI__ID which is appended to ORDER BY in GenerateSQL 
			//               to make sorts on non-discriminating fields predictable for page browsing, 
			//               selecting the right record after postback, etc. (False is the default).
			//       par_bSysFileFullPermissions: When true, the rowset on system files (as determined by goData.IsFileSystem)
			//               is executed with full Read, Add, Edit, and Delete permission, allowing 
			//               programmatic access to those files under regular user logins. Logins with admin and 
			//               full author permissions automatically have R-A-E-D permissions on system files
			//               regardless of this parameter.
			//       par_sAdditionalIni: Ini string that provides additional data for different modes.
			//               Currently, this is supported:
			//               - All modes:
			//                   EXCLUDEFIELDS=  Comma-delimited list of fields to exclude even when passed via par_sFields.
			//                                   The rowset uses this when the physical schema doesn't have some of the
			//                                   fields or links that the rowset has. This can occur when fields are 
			//                                   removed during users' sessions.
			//               - JOINANDWHEREONLY/JOINANDWHEREONLYFORNNLINKS mode:
			//                   EXCLUDEJOINS=   This JOIN will not be duplicated. This is needed for NN link statements
			//                                   that may have selective perms that use the same main join.
			//               - SIMPLESELECT/JOINANDWHEREONLY/JOINANDWHEREONLYFORNNLINKS modes:
			//                   WHEREPREPEND=   TSQL statement that is prepended to the generated WHERE statement.
			//                                   Currently, extra JOINs are not supported.
			//                                   Example:
			//                                       [CO].[GID_ID] = [CN].[GID_Related_CO]
			//                                   Parens and the 'and' keyword are added as appropriate.
			//                   WHEREAPPEND=    TSQL statement that is appended to the generated WHERE statement.
			//               - SIMPLESELECT mode only:
			//                   DISTINCT=       1 or 0 (default). When 1, SELECT DISTINCT is created, else just SELECT.
			//                                   Note that DISTINCT keyword is not supported if there are any ntext or text
			//                                   columns. Mixing the two will cause this error from SQL:
			//                                   'The ntext data type cannot be selected as DISTINCT because it is not comparable.'
			//RETURNS:
			//		String: Select statement.
			//EXAMPLE 1:
			//		'Sending a single statement
			//		Dim sStatement as string
			//		sStatement = "TABLENAME=CN"
			//		sStatement &= vbCrLf & "TOP=20"
			//		sStatement &= vbCrLf & "FIELDS=TXT_NameLast,TXT_NameFirst,LNK_InvolvedWith_CO%%TXT_CompanyName,GID_ID"
			//		sStatement &= vbCrLf & "CONDITION=LNK_InvolvedWith_CO%%TXT_COMPANYNAME[='Selltis' AND LNK_Related_US%%TXT_NAMELAST='Igrec' 
			//		sStatement &= vbCrLf & "SORT=TXT_NameLast DESC, TXT_NameFirst DESC"
			//		MsgBox(goData.GenerateSQL(sStatement))
			//EXAMPLE 2:
			//		'Sending individual parameters
			//		MsgBox(goData.GenerateSQL("", _
			//						"CN", _
			//						"TXT_NameLast, TXT_NameFirst, LNK_InvolvedWith_CO%%TXT_CompanyName, GID_ID", _
			//						"CONDITION=LNK_INVOLVEDWITH_CO%%TXT_COMPANYNAME[='Selltis' AND LNK_Related_US%%TXT_NameLast='Igrec'", _
			//						"TXT_NameLast DESC, TXT_NameFirst DESC", _
			//						20))
			//Both examples return the following statement (last updated 4/24/06):
			//
			//DECLARE @tManyLinkList TABLE(LinkName nvarchar(100))
			//DECLARE @tIDTable TABLE(
			// [CN_GID_ID] uniqueidentifier, 
			// [CN_SYS_Name] nvarchar(300), 
			// [CN_TXT_NameLast] nvarchar(300), 
			// [CN_TXT_NameFirst] nvarchar(300))
			//
			//INSERT INTO @tIDTable([CN_GID_ID], [CN_SYS_Name], [CN_TXT_NameLast], [CN_TXT_NameFirst])
			//SELECT DISTINCT TOP 20 CN.[GID_ID], CN.[SYS_Name], CN.[TXT_NameLast], CN.[TXT_NameFirst]
			//FROM CN 
			//LEFT JOIN CN_InvolvedWith_CO ON CN.[GID_ID] = CN_InvolvedWith_CO.[GID_CN]
			//LEFT JOIN CO CO00001 ON CO00001.[GID_ID] = CN_InvolvedWith_CO.[GID_CO]
			//LEFT JOIN CN_Related_US ON CN.[GID_ID] = CN_Related_US.[GID_CN]
			//LEFT JOIN US US00002 ON US00002.[GID_ID] = CN_Related_US.[GID_US]
			//WHERE CO00001.[TXT_COMPANYNAME] LIKE 'Selltis%' AND US00002.[TXT_NAMELAST] = 'Igrec' 
			//ORDER BY CN.[TXT_NameLast] DESC, CN.[TXT_NameFirst] DESC
			//
			//SELECT CN.[TXT_NameLast] AS 'TXT_NameLast', 
			// CN.[TXT_NameFirst] AS 'TXT_NameFirst', 
			// CN.[GID_ID] AS 'GID_ID' 
			//FROM CN 
			//JOIN @tIDTable idt ON idt.CN_GID_ID = CN.[GID_ID]
			//ORDER BY CN.[TXT_NameLast] DESC, CN.[TXT_NameFirst] DESC
			//
			//INSERT @tManyLinkList 
			//VALUES('LNK_InvolvedWith_CO%%TXT_CompanyName') 
			//SELECT * FROM @tManyLinkList
			//
			//SELECT idt.[CN_GID_ID] AS 'BASE%%GID_ID', 
			// CN_InvolvedWith_CO.[GID_ID] AS 'GID_ID', 
			// CO.[TXT_CompanyName] AS 'LNK_InvolvedWith_CO%%TXT_CompanyName', 
			// CO.[SYS_Name] AS 'LNK_InvolvedWith_CO%%SYS_Name' 
			//FROM CO
			//JOIN CN_InvolvedWith_CO ON CO.[GID_ID] = CN_InvolvedWith_CO.[GID_CO]
			//JOIN @tIDTable idt ON idt.[CN_GID_ID] = CN_InvolvedWith_CO.[GID_CN]
			//ORDER BY idt.[CN_GID_ID], CO.[SYS_Name]
			// ------------- PROPOSAL FOR FINAL SPEC -------------
			//Here is the original proposal for how the SELECT generator should work:
			//
			//-- It will compose multiple SELECT statements as a single
			//-- string like this:
			//-- 
			//-- 1. Load the IDs of records in the main table into a 
			//-- table variable to make the IDs available for joins in
			//-- subsequent steps.
			//-- Since this list of IDs represents the "main query," it will
			//-- include all filter conditions, selective access conditions,
			//-- and the sort order.
			//--
			//-- 2. Return the main table recordset with all the requested columns.
			//-- The table is returned based on a join with the IDs retrieved
			//-- in 1.
			//--
			//-- 3. For each link return the linked records of all the records in the
			//-- main table, again via a join with the IDs retrieved in 1. 
			//-- Each link's records are returned as a separate recordset.
			//--
			//-- For the calling code to know the links that the additional
			//-- recordsets are from, the generator shall return
			//-- the name of the main query's table name and a list of links
			//-- in the order in which the link recordsets are returned.
			//-- All this information together with possible error codes shall
			//-- be returned as an object (TBD).
			//----------- ABOUT FILTER CONDITION METADATA --------------
			//The following are comments from NGP's WFILTCND (filter condition) window
			//about the properties of an ini-format string it returns. Of particular
			//value in this context are lists of supported values for the CONDITION
			//property.
			//RETURNS:
			//		string in ini format that contains the following parameters:
			//			FIELDNAME			'Field name, e.g. 'DTE_Date' or 'LNK_In_Territory'.
			//			FIELDLABEL			'Field label, e.g. 'Date'. If the field is a link,
			//								'this property contains the field from the linked
			//								'file separate with ' - ', e.g.:
			//								'Coordinated By User - Code.
			//								'If a link is referenced in the linked files, then 
			//								'all links are separated with ' - '. E.g.:
			//								'Originated By Contact - Related Company - In Territory - Territory Name.
			//								'This is what WFILTPRO displays.
			//			CONDITION			'System expression of the CMB_CONDITION value. Some of these are used only
			//								'in the UI, not in the actual conditions, e.g. ']['. Since SS supports
			//                               'the BETWEEN keyword, we may support this expression in the future.
			//                               'Supported values are (if a line is blank in a column, the expression is
			//                               'not supported):
			//								-----------------------------		-------------------------	---------------------	----------------	---------------
			//								Number (numeric/currency)			Text, Telephone, Memo		Date					Time				List
			//								-----------------------------		-------------------------	---------------------	----------------	---------------
			//				=				'2025:Is Equal To					'2025:Is Equal To			'2040:Is On			    '2042:Is At		    'Equals
			//				<>				'2026:Is Not Equal To				'2026:Is Not Equal To		'2041:Is Not On		    '2043:Is Not At	    'Does Not Equal
			//				>				'2027:Is Greater Than				'2027:Is Greater Than		'2038:Is After			'2038:Is After
			//				>=				'2028:Is Greater Than Or Equal To	'2028:Is Greater Than Or Equal To
			//				<				'2029:Is Less Than					'2029:Is Less Than			'2039:Is Before		    '2039:Is Before
			//				<=				'2030:Is Less Than Or Equal To		'2030:Is Less Than Or Equal To
			//				][				'2031:Is Between					'2031:Is Between			'2031:Is Between		'2031:Is Between
			//				[=  (ex ']=')										'2032:Starts With
			//				[<> (ex ']<>')										'2033:Does Not Start With
			//				[   (ex ']')										'2034:Contains
			//				<[>													'2035:Does Not Contain
			//				[]													'2036:Is Blank				'2036:Is Blank			'2036:Is Blank
			//				<[]>												'2037:Is Not Blank			'2037:Is Not Blank		'2037:Is Not Blank
			//				=]													'Ends With
			//				<>]													'Does Not End With
			//               ~                                                   'Is Like
			//               <~>                                                 'Is Not Like
			//                 				-----------------------------		-------------------------
			//								Checkbox							Link
			//								-----------------------------		-------------------------
			//				1				'Is Checked							'Selected record in a linked file's View (value is blank)
			//				0				'Is Not Checked						'Specific Record (value contains rec TID)
			//
			//			VALUE1				'System value (date as '20030304', linked record as record TID, etc).
			//								'This property can contain multiple values, e.g. between x and y.
			//								'Spaces are replaced with Charact(160)!
			//			VALUE2				'Second value in Is Between condition
			//								'Spaces are replaced with Charact(160)!
			//			VALUEDATE1			'For time fields only, the value for the date field that matches VALUE1
			//			VALUEDATE2			'For time fields only, the value for the date field that matches VALUE2
			//			MATCHCASE			'Match case checkbox: 1=checked; 0=unchecked
			//			TIMESEL				'Time zone selection (integer): 1=Local time; 2=GMT
			//			PARENBEFORE			'Value of field EDT_PARENBEFORE
			//			PARENAFTER			'Value of field EDT_PARENAFTER
			//			KEYWORD				'AND or OR that follows the condition
			//------------------ NOTES FOR COUNT MODE ------------------------
			//Here is the part of the statement generated in the SELECT mode
			//that needs to be generated in the COUNT mode. Unfortunately, fields
			//must be read in the SELECT statement for DISTINCT to be able to
			//remove the duplicates that otherwise would be returned.
			// 
			//DECLARE @tManyLinkList TABLE(LinkName nvarchar(100))
			//DECLARE @tIDTable TABLE(
			//	[CN_GID_ID] uniqueidentifier, 
			//	[CN_SYS_Name] nvarchar(300), 
			//	[CN_TXT_NameLast] nvarchar(300), 
			//	[CN_TXT_NameFirst] nvarchar(300))
			// 
			//INSERT INTO @tIDTable([CN_GID_ID], [CN_SYS_Name], [CN_TXT_NameLast], [CN_TXT_NameFirst])
			//SELECT DISTINCT TOP 20 CN.[GID_ID], CN.[SYS_Name], CN.[TXT_NameLast], CN.[TXT_NameFirst]
			//FROM CN 
			//LEFT JOIN CN_INVOLVEDWITH_CO ON CN.[GID_ID] = CN_INVOLVEDWITH_CO.[GID_CN]
			//LEFT JOIN CO CO00001 ON CO00001.[GID_ID] = CN_INVOLVEDWITH_CO.[GID_CO]
			//LEFT JOIN CN_Related_US ON CN.[GID_ID] = CN_Related_US.[GID_CN]
			//LEFT JOIN US US00002 ON US00002.[GID_ID] = CN_Related_US.[GID_US]
			//LEFT JOIN EL EL00003 ON EL00003.[GID_Related_CN] = CN.[GID_ID]
			//WHERE (CO00001.[TXT_COMPANYNAME] LIKE 'Selltis%' AND US00002.[TXT_NameLast] = 'Igrec' AND EL00003.[TXT_EmailAddress] LIKE 'b%') 
			//--ORDER BY CN.[TXT_NameLast] ASC, CN.[TXT_NameFirst] ASC
			// 
			//SELECT Count(*)--CN.[TXT_NameLast] AS 'TXT_NameLast', 
			//--	CN.[TXT_NameFirst] AS 'TXT_NameFirst', 
			//--	CN.[GID_ID] AS 'GID_ID' 
			//FROM CN 
			//JOIN @tIDTable idt ON idt.CN_GID_ID = CN.[GID_ID]
			//--ORDER BY CN.[TXT_NameLast] ASC, CN.[TXT_NameFirst] ASC


			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sResult = "";

			//Try

			string sTable = par_sTable.Trim(' ');
			string sFields = par_sFields.Trim(' ');
			string sCondition = par_sCondition.Trim(' ');
			string sSort = par_sSort.Trim(' '); //TXT_NameLast DESC, TXT_NameFirst DESC
			//                                       'An NN link or a multi-hop link raises an error
			long lTop = par_lTop;
			string sGenFieldDefs = par_sGenFieldDefs.Trim(' ');
			string sMode = ""; //par_sMode is considered after trying to read it from par_sFilterSort
			string sTopRecord = par_sTopRecord.Trim(' ');
			string sAccessType = par_sAccessType.ToUpper().Trim(' ');
			bool bGetAllUsersUnsharedRecs = par_bGetAllUsersUnsharedRecs;
			string sWork = null;
			string sWork2 = null;
			string sWork3 = null;
			string sWork4 = "";
			//Dim sWork5 As String
			long lCount = 0;
			//Dim lCount2 As Long
			//Dim lCount3 As Long
			long lPos = 0;
			int iLastPos = 0;
			int iAndOrLen = 0;
			string sLink = null;
			string sField = null;
			string sJoin = "";
			string sWhereJoin = ""; //JOINs needed for the WHERE statement
			string sWhere = "";
			string sJoinWhere = "";
			string sOperator = "";
			string sValue = "";
			string sFieldPart = "";
			string sLinkTable = null;
			string sLinkTableNo = "";
			bool bBreakDo = false;
			string sParensStart = "";
			string sParensEnd = "";
			string sSingleCondString = null; //Single condition string without parens
			long lWork = 0;
			//Dim lWork2 As Long
			string sAndOr = ""; //Contains ' AND ' or ' OR '
			int iLastAndOrLen = 0;
			//Dim sDirection As String
			//Dim lSpace As Long
			string sSQLCond = "";
			string sLinkType = null;
			string sLinkPart = null;
			//Dim sDeclaration As String = ""         'Variable declarations
			string sNLinks = ""; //List of n-n link fields for which to return separate recordsets
			string sToAdd = null; //Temporary string to add to another string
			string sSortString = ""; //[CO].[GID_ID], [CN].[TXT_NameLast] DESC, [CN].[TXT_NameFirst] DESC
			//                                       The original sSort string converted to the SQL syntax.
			//                                       The second Sort field is ignored (see sSort). [ ] brackets are used.
			string sSortColumnsTemp = ""; //[CO].[GID_ID], [CN].[TXT_NameLast], [CN].[TXT_NameFirst]
			//                                       Comma-delimited list of ORDER BY columns. [ ] brackets are used.
			//                                       The list is reset and refilled several times.
			string sSortColListTemp = ""; //GID_ID, SYS_Name, LNK_Related_CO%%GID_ID, TXT_NameLast, TXT_NameFirst
			//                                       Original list of sort columns without ASC or DESC, but with normalized
			//                                       syntax: simple link names become LNK_Link_ToTable%%GID_ID. No [ ] brackets
			//                                       are used.
			//                                       The list is reset and refilled several times.
			string sSortNormalized = ""; //GID_ID ASC, TXT_NameLast DESC, TXT_NameFirst DESC
			//                                       Original list of sort columns with all links normalized to contain %%<LinkField>.
			//                                       This variable's value is set to sSort after sort fields are processed.
			//                                       [ ] brackets are not used.
			string sTableVarInsertStr = ""; //[CN_GID_ID], [CN_SYS_Name], [CO00001_GID_ID], [CN_TXT_NameLast], [CN_TXT_NameFirst]
			//                                       List of columns for the INSERT INTO @tIDTable line. This is
			//                                       like sSortColListTemp (GID_ID, SYS_Name, <the sort string>) but with
			//                                       table names in col names, e.g.[CO_SYS_Name] instead of CO.[SYS_Name]
			string sInsertSelectString = ""; //CN.[GID_ID], CN.[SYS_Name], CO.[GID_ID], CN.[TXT_NameLast], CN.[TXT_NameFirst]
			//                                       List of columns for the SELECT query under the INSERT line of the
			//                                       table variable-loading query.
			string sRevLinkName = null; //Reverse link name (CN.LNK_Related_CO -> CO.LNK_Connected_CN)
			string sRevLinkPart = null;
			string sRevLinkFile = null;
			//Dim sStars As String = ""               'Filled with '*' or '**' depending on the contents of sFields
			string sAliasTable = ""; //Table alias being worked with. This value changes dynamically.
			clArray aAliases = new clArray(); //Array of links to which table aliases have been assigned.
			//                                       'Each new alias is assigned a number aAliases.GetDimension() + 1
			//                                       'converted to a string prepadded with 0's.
			//                                       'Example:
			//                                       AC.LNK_Related_CN=00001
			//                                       AC.LNK_Involves_CN=00002
			//                                       AC.LNK_Involves_US=00003
			//                                       AC.LNK_CreatedBy_US=00004
			//                                       AC.LNK_CreditedTo_US=00005
			//                                       'Except for the base table, for which we don't use an alias, whenever a 
			//                                       'table would be referenced normally, an alias must be used. 
			//                                       'The table is prepended to links to support multihop links. In SQL,
			//                                       'a multihop link is treated as multiple JOINs. The starting table of
			//                                       'each hop must be defined.
			//Dim sSQLType As String                  'SQL Server field type
			string sFromTable = null; //in multi-hop links, table from which a link originates
			string sFromAlias = null; //In multi-hop links, table alias of a link origination table
			int iLinkHop = 0; //Loop counter for link hops
			string sHop = null; //Field or link within a multi-hop link definition delimited by %%
			bool bLastHop = false; //While looping through a multi-hop link this indicates that
			//                                       'the field can be used for composing (part of) a WHERE statement.
			//Dim bQuotesAreOpen As Boolean           'Indicates that a field definition started with a double-quote
			//                                       'and that a closing double-quote hasn't been encountered yet.
			//                                       'Double-quotes are required when a combined field contains commas.
			//Dim sFieldType As String                'Field prefix
			//Dim bSingleQuotes As Boolean            'True when single quotes delimit a condition value 
			//Dim dtTemp As DateTime                  'Temporary storage for datetime type
			//Dim iType As Integer                    'Holds SELL_TYPE_XXXXXX values returned by goTr.StringToXxxxx.
			//Dim i As Integer
			string sSortField = ""; //used in toprec processing
			string sSortFieldVal = ""; //used in toprec processing
			string sSortFieldDirection = ""; //used in toprec processing
			int iOpenParenCount = 0;
			int iCloseParenCount = 0;
			string sAndOrFound = "";
			bool bFoundSingleQuotes = false; //At least one single quote was found
			bool bSingleQuotesClosed = false; //A closing single quote was found, only AND or OR allowed
			//                                           'This var is False until the closing quote is found. Since
			//                                           'single quotes may not be used at all (value is numeric,
			//                                           'for ex), this may be False to the end. Use this only to
			//                                           'disallow characters other than AND or OR after a delimited
			//                                           value was found.
			bool bReadyForAndOr = true; //Indicates that AND or OR are allowed regardless of single quotes
			//                                           'Unlike bSingleQuotesClosed, this var is True until (if)
			//                                           'a single quote value delimiter is found. Use this to 
			//                                           'suppress the parsing of AND or OR as condition segment
			//                                           'delimiters.
			string sAltCond = null; //Alternative condition statement for NN link exclusion
			string sTopStatement = "";
			string sDistinct = "";
			string sLinkingTable = ""; //Ex: CN_InvolvedWith_CO for link CN.LNK_InvolvedWith_CO
			string sLinkingTableAlias = ""; //Ex: CN_InvolvedWith_CO00002 for the second reference to link CN.LNK_InvolvedWith_CO
			clArray aLinkingTableAliases = new clArray(); //Array of aliases assigned to linking tables.
			string sTableVarName = null; //Name of the table variable used for NN link statements
			bool bUseLinkPermissions = false; //WOP property USELINKPERMISSIONS
			bool bUseInnerJoins = false; //WOP property USEINNERJOINS
			string sJoinWord = "LEFT "; //"LEFT " or "INNER ". Note that the space must follow the word.
			bool bSkipCondition = false;

			if (par_sFilterSort != "")
			{
				//The filter is provided as an ini string
				sTable = goTR.StrRead(par_sFilterSort, "TABLENAME", "", false).Trim(' ');
				if (sTable == "")
				{
					//MI 10/15/09 FILE is used in VIE MD syntax. If TABLENAME is not defined,
					//read TABLENAME instead.
					sTable = goTR.StrRead(par_sFilterSort, "FILE", "", false).Trim(' '); //MI 10/15/09 Added. FILE= is the View MD syntax
					if (sTable != "")
					{
						goTR.StrWrite(ref par_sFilterSort, "TABLENAME", sTable);
					}
				}
				sFields = goTR.StrRead(par_sFilterSort, "FIELDS", "", false).Trim(' ');
				 sCondition = goTR.StrRead(par_sFilterSort, "CONDITION", "", false).Trim(' ');
				sSort = goTR.StrRead(par_sFilterSort, "SORT", "", false).Trim(' ');
				sMode = goTR.StrRead(par_sFilterSort, "MODE", "", false); //MI 12/10/09 added reading sMode from par_sFilterSort if needed
				//Pass the whole filter string - the code below will extract
				//each GEN field's definition as needed
				sGenFieldDefs = par_sFilterSort;
				sTopRecord = goTR.StrReadAll(par_sFilterSort, "TOPREC_");
				sAccessType = goTR.StrRead(par_sFilterSort, "ACCESSTYPE", "R", false);
                //bGetAllUsersUnsharedRecs = Convert.ToBoolean(goTR.StrRead(par_sFilterSort, "GETALLUSERSUNSHAREDRECS", "0", false));
                string strValue = goTR.StrRead(par_sFilterSort, "GETALLUSERSUNSHAREDRECS", "0", false);

                bGetAllUsersUnsharedRecs = strValue == "1" || strValue.Equals("true", StringComparison.OrdinalIgnoreCase);


                string value = goTR.StrRead(par_sFilterSort, "GETALLUSERSUNSHAREDRECS", "0", false);
                Console.WriteLine($"Retrieved value: {value}"); // Logs the value to check its contents

                par_bReverseSort = goTR.StringToCheckbox(goTR.StrRead(par_sFilterSort, "REVERSESORT", "0", false));
				par_bSysFileFullPermissions = goTR.StringToCheckbox(goTR.StrRead(par_sFilterSort, "SYSFILEFULLPERMISSIONS", "0", false));
			}

			//Mode still undefined? Try the par_sMode parameter.
			if (sMode == "")
			{
				sMode = par_sMode;
			}
			//Mode still undefined? Set it to SELECT
			if (sMode == "")
			{
				sMode = "SELECT";
			}

			//Read TOP
			switch (sMode)
			{
				case "COUNT":
					//TOP is ignored
					lTop = -1;
					break;
				default:
					sWork = goTR.StrRead(par_sFilterSort, "TOP", "-1", false);
					if (sWork == "-1")
					{
						//MI 10/15/09 SHOWTOP is used in VIE MD syntax. If TOP is not defined,
						//try to read SHOWTOP instead.
						sWork = goTR.StrRead(par_sFilterSort, "SHOWTOP", "-1", false);
						if (sWork != "-1")
						{
							goTR.StrWrite(ref par_sFilterSort, "TOP", sWork);
						}
					}
					if (!NumericHelper.IsNumeric(sWork))
					{
						goErr.SetError(35001, sProc, "", sWork);
						//Return "SELECT '" & sProc & " Error: invalid TOP value: [" & sWork & "].' AS 'ERROR'"
					}
					//Try
					if (lTop <= NumericHelper.Val(sWork))
					{
						lTop = Convert.ToInt64(NumericHelper.Val(sWork));
					}
					break;
					//Catch ex As Exception
					//    goErr.SetError(35002, sProc, , sWork)
					//    'Return "SELECT '" & sProc & " Error: TOP value exceeds the size of long integer: [" & sWork & "].' AS 'ERROR'"
					//End Try
			}



			sWork = "";

			//Validate file (table)
			if (!goData.IsFileValid(sTable))
			{
				goErr.SetError(35003, sProc, "", sTable);
				//Return "SELECT '" & sProc & " Error: invalid TABLENAME.' AS 'ERROR'"
			}

			//Blank sort is substituted with 'SYS_Name'
			switch (sMode)
			{
				case "SIMPLESELECT":
				case "JOINANDWHEREONLY":
				case "JOINANDWHEREONLYFORNNLINKS":
				case "COUNT":
					//These modes ignore sort and don't include ORDER BY
					sSort = "";
					break;
				case "GROUPBY":
				case "GROUPBYWITHROLLUP": //MI 12/10/09 added
					//In GroupBy mode, if sort is not defined we could group say by the year of the creation date, but
					//we raise an error instead to make sure the sort is explicitly defined.
					//Code in clRowset.ToTable depends on this - do not change it.
					if (sSort == "")
					{
						goErr.SetError(35000, sProc, "SORT is not defined and is required in " + sMode + " mode because it defines how records will be grouped for calculations. Define at least one field like DTM_CreationTime or MLS_Status.");
					}
					break;
				default:
					if (sSort == "")
					{
						sSort = "SYS_Name";
					}
					//NONE makes the ORDER BY not appear at all
					if (sSort.ToUpper() == "NONE" || sSort.ToUpper() == "<%NONE%>")
					{
						sSort = "";
					}
					break;
			}

			//'The following is wrong to do here because GID_ID and SYS_Name are
			//'added to sSortColListTemp before sTableVarInsertStr and sInsertSelectString
			//'are built from what's in that variable.
			//sTableVarInsertStr = "[" & sTable & "_GID_ID]"
			//'SYS_Name is here for debugging convenience in n-n link recordsets below
			//sTableVarInsertStr &= ", [" & sTable & "_SYS_Name]"
			//sInsertSelectString = sTable & ".[GID_ID]"
			//'SYS_Name is here for debugging convenience in n-n link recordsets below
			//sInsertSelectString &= ", " & sTable & ".[SYS_Name]"

			//MI 9/28/12 Reenabling
			//MI 9/20/12 Disabling the link perm feature. There is a TOP=0 error with links in the filter.
			bUseLinkPermissions = goTR.StringToCheckbox(goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "USELINKPERMISSIONS", "1", true));

			bUseLinkPermissions = false;


			//MI 8/31/09 Inner Joins test: here we are reading a WOP property that determines whether LEFT JOINs are used (default) or INNER JOINs.
			bUseInnerJoins = goTR.StringToCheckbox(goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "USEINNERJOINS", "0", true));
			if (bUseInnerJoins)
			{
				sJoinWord = "INNER ";
			}
			else
			{
				sJoinWord = "LEFT ";
			}


			//============== REMOVE REDUNDANT CONDITIONS ==================
			//Exclude the condition if its value is a 'fake' SUID. Fake SUIDs are used when it is known 
			//that the condition should not return any records, for example in the case of <%SelectedViewRecordID%>
			//when the desktop knows that a view is not selected. 
			//clDesktop.GetViewCondition(), for example, puts the fake SUID as a value in all
			//<%SelectedViewRecordID FILE=xx%> conditions except the one that points to a currently selected view.
			//Excluding fake conditions is a performance optimization technique - not creating the joins and WHERE
			//conditions that are unneeded greatly improves query performance and reduces the needed resources.

			if (sCondition.IndexOf("11111111-1111-1111-") + 1 > 0)
			{
				goLog.Log(sProc, "Redundant condition found, running RemoveRedundantConditions()", (short)clC.SELL_LOGLEVEL_DEBUG);
				//==> Turn these two log calls to SELL_LOGLEVEL_DEBUG after through with testing
				goLog.Log(sProc, "Orig: '" + sCondition + "'", (short)clC.SELL_LOGLEVEL_DEBUG);
				sCondition = RemoveRedundantConditions(sCondition);
				goLog.Log(sProc, "New:  '" + sCondition + "'", (short)clC.SELL_LOGLEVEL_DEBUG);
			}


			//============== ADD SELECTIVE AND SHARESTATE FILTER TO CONDITION ===============
			//DEBUG
			sWork = par_sFilterSort;
			//==> Put a stop here to evaluate the filter
			//STOP
			//END DEBUG
			if (lTop != 0)
			{
				//TOP is not 0, add condition(s) for selective perms and and sharing state
				sCondition = AddSelectiveFilterToCondition(sTable, sCondition, sAccessType, par_bSysFileFullPermissions, bGetAllUsersUnsharedRecs);
			}



			//============== STANDARDIZE "", * AND ** IN FIELDS ================
			//Process "", and stars (*) and (**) in FIELDS= definition
			switch (sMode)
			{
				case "JOINANDWHEREONLY":
				case "JOINANDWHEREONLYFORNNLINKS":
				case "COUNT": //MI 2/14/10 added COUNT
					sFields = "GID_ID";
					break;
				case "GROUPBY":
				case "GROUPBYWITHROLLUP": //MI 2/14/10 removed COUNT
				break;
					//Stars are not supported
				default:
					sFields = ProcessStarsInFields(sFields, sTable);
					break;
			}


			//============== PROCESS GENERATED FIELDS ================
			//Process generated fields and remove fields that are in the Selltis schema,
			//but are not in SQL (because they were deleted during the user session).
			//We modify sFields if necessary by removing static text, <% and %>,
			//and field code formatting <%xxxx FORMAT=yyyy%>.
			//sFields are a "plain" comma-delimited list of fields after this.
			//GROUPBY and GROUPBYWITHROLLUP calculated field names get |SUM, |AVG, |MIN, |MAX, or |MED appended,
			//e.g. FIELDS=CUR_Value|SUM, CUR_Value|AVG, INT_Qty|MIN, INT_Qty|MAX
			switch (sMode)
			{
				case "COUNT":
				break;
					//No sFields
				default:
					sFields = ProcessGeneratedFields(sFields, sTable, sGenFieldDefs, sMode);
					break;
			}


			// =========== TABLE VARIABLE: IDs FOR MAIN RECORDSET ===============
			//Fill the table variable with IDs that will be used for JOINs
			//from the main recordset and all n-n link recordsets
			//We don't use DISTINCT in this query because this would require
			//returning all the columns from the ORDER BY line, and that would
			//require adding those columns to the table variable. Adding columns
			//would require finding their SQL Server types from the middle tier.
			//Example:
			//INSERT INTO @tIDTable(GID_ID)
			//SELECT CN.GID_ID 
			//FROM CN 
			//JOIN CN_InvolvedWith_CO ON CN.GID_ID = CN_InvolvedWith_CO.GID_CN 
			//JOIN CO00001 ON CO00001.GID_ID = CN_InvolvedWith_CO.GID_CO 
			//JOIN CN_Related_US ON CN.GID_ID = CN_Related_US.GID_CN 
			//JOIN US00002 ON US00002.GID_ID = CN_Related_US.GID_US 
			//WHERE (CO00001.TXT_CompanyName LIKE 'S%' OR US00002.TXT_NameLast = 'Igrec') 
			//--Leaving out ORDER BY can yield better performance
			//--ORDER BY CN.TXT_NameLast, CN.TXT_NameFirst	--, CO00001.TXT_CompanyName 

			switch (sMode)
			{
				case "SIMPLESELECT":
				case "JOINANDWHEREONLY":
				case "JOINANDWHEREONLYFORNNLINKS": //, "COUNT" 'MI 2/14/10 commented COUNT
				break;
					//Skip sort, @tIDTable, DECLARATIONS, etc.

				default:

					// -------------- Sort Columns -------------
					//MI 9/1/09 Replace DTE_ with DTT_. This allows to sort report views by DTE_ fields. 
					//NOT GOOD because the time component of DTT values would kill the sort on subsequent fields
					//sSort = goTR.Replace(sSort, "DTE_", "DTT_")
					sSort = GenerateSortColumns(sSort, sTable, par_bAddSeqIDToSort, par_bReverseSort, ref aAliases, ref sSortString, ref sSortColumnsTemp, ref sSortColListTemp, ref sSortNormalized, bUseLinkPermissions, sMode);


					// ----------- @tIDTable COLUMNS ------------
					GenerateIDTableColumns(ref sSortColListTemp, ref sTable, ref aAliases, ref sTableVarInsertStr, ref sInsertSelectString, sMode, bUseLinkPermissions);


					// -------------- DECLARATIONS --------------
					sResult += GenerateDeclarations(sTableVarInsertStr, sMode);


					// ---------- SELECT (FOR @tIDTable) ----------
					//DISTINCT is needed to avoid repeated entries, which appear when 
					//JOINS are included for filtering on n-n links. 
					//If there are no JOINS, DISTINCT is not necessary.
					//All ORDER BY columns must be included in SELECT columns
					//because DISTINCT is used.
					//Example:
					//INSERT INTO @tIDTable(GID_ID, TXT_NameLast, TXT_NameFirst)
					//SELECT DISTINCT TOP 1000 CN.GID_ID, CN.TXT_NameLast, CN.TXT_NameFirst
					//FROM CN 
					//-- JOINS are for SELECT, WHERE, and ORDER BY statements on links.
					//-- n-n links in ORDER BY will be ignored. n-1 links will be processed.
					//LEFT JOIN CN_InvolvedWith_CO ON CN.GID_ID = CN_InvolvedWith_CO.GID_CN 
					//LEFT JOIN CO CO00001 ON CO00001.GID_ID = CN_InvolvedWith_CO.GID_CO 
					//LEFT JOIN CN_Related_US ON CN.GID_ID = CN_Related_US.GID_CN 
					//LEFT JOIN US US00002 ON US00002.GID_ID = CN_Related_US.GID_US 
					//WHERE (CO00001.TXT_CompanyName LIKE 's%' or US00002.TXT_NameLast = 'Igrec') 
					//ORDER BY CN.TXT_NameLast, CN.TXT_NameFirst	--, CO00001.TXT_CompanyName 

					sResult += "INSERT INTO @tIDTable(";
					lCount = 1;
					sWork = "";
					do
					{
						sWork = goTR.ExtractString(sTableVarInsertStr, (int)lCount, ",").Trim(' ');
						if (sWork[0] == clC.EOT || sWork == "")
						{
							break;
						}
						if (sResult.Substring(sResult.Length - 1) != "(")
						{
							sResult += ", ";
						}
						sResult += sWork;
						lCount = lCount + 1;
					} while (true);
					sResult += ")";
					break;

			}

			//Validate TOP parameter. TOP=0 returns a 0-record recordset with all the columns.
			if (lTop < -1)
			{
				goErr.SetError(35001, sProc, "", lTop.ToString());
				//35001: Invalid TOP value: '[1]'.
				//Return "SELECT '" & sProc & " Error: invalid value in TOP parameter: [" & lTop & "].' AS 'ERROR'"
			}
			else
			{
				switch (sMode)
				{
					case "SIMPLESELECT":
					case "JOINANDWHEREONLY":
					case "JOINANDWHEREONLYFORNNLINKS": //, "COUNT" 'MI 2/14/10 commented COUNT
					break;
						//Don't add hard return
					default:
						//Add hard return
						sResult += "\r\n";
						break;
				}
				//Omit SELECT in JOINANDWHEREONLY mode
				sDistinct = "";
				switch (sMode)
				{
					case "SIMPLESELECT":
						if (goTR.StrRead(par_sAdditionalIni, "DISTINCT", "0", false) == "1")
						{
							sDistinct = "DISTINCT ";
						}
						//Distinct can't be used for ntext columns.
						if (lTop == -1)
						{
							//lTop not defined, return all records
							sResult += "SELECT " + sDistinct;
						}
						else
						{
							sResult += "SELECT " + sDistinct + "TOP " + lTop.ToString() + " ";
						}
						break;
					case "JOINANDWHEREONLY":
					case "JOINANDWHEREONLYFORNNLINKS":
					break;
							//Skipping SELECT
					case "COUNT":
						//sResult &= "SELECT "   'MI 2/14/10 commented SELECT without DISTINCT
						sResult += "SELECT DISTINCT "; //MI 2/14/10 Added DISTINCT to SELECT
						break;
					case "GROUPBY":
					case "GROUPBYWITHROLLUP":
						//TOP is added to the main SELECT statement only. If added here,
						//grouping calculations would be incorrect.
						sResult += "SELECT DISTINCT ";
						break;
					default:
						if (lTop == -1)
						{
							//lTop not defined, return all records
							sResult += "SELECT DISTINCT ";
						}
						else
						{
							sResult += "SELECT DISTINCT TOP " + lTop.ToString() + " ";
						}
						break;
				}
			}

			// ---------------- Columns ---------------- 
			switch (sMode)
			{
				case "JOINANDWHEREONLY":
				case "JOINANDWHEREONLYFORNNLINKS":
				break;
					//Nothing
					//Case "COUNT"        'MI 2/14/10 commented
					//    sResult &= "COUNT_BIG(*) as 'BI__COUNT'"        'MI 2/14/10 commented
				default:
					sSortColumnsTemp = "";
					sWork = "";
					lCount = 1; //used for loop iterations
					lWork = 0; //used to count columns that were actually added
					do
					{
						switch (sMode)
						{
							case "SIMPLESELECT":
								sWork = goTR.ExtractString(sFields, (int)lCount, ",").Trim(' ');
								break;
							default:
								//JOINANDWHEREONLY and JOINANDWHEREONLYFORNNLINKS are excluded in If statement above
								sWork = goTR.ExtractString(sSortColListTemp, (int)lCount, ",").Trim(' ');
								break;
						}
						if (sWork[0] == clC.EOT || sWork == "")
						{
							break;
						}
						if (sMode == "COUNT") //MI 2/17/10 Changed from If par_sMode to If sMode per CS.
						{
							//Add only GID_ID in COUNT mode
							if (!(sWork.ToUpper() == "GID_ID"))
							{
								goto ProcessNextColumn;
							}
						}
						if (goTR.GetPrefix(sWork) == "LNK_")
						{
							//The field is a link
							if (sWork.IndexOf("%%") + 1 > 0) //InStr()
							{
								sLink = goTR.ExtractString(sWork, 1, "%%");
								sField = goTR.ExtractString(sWork, 2, "%%");
							}
							else
							{
								sLink = sWork;
								sField = "GID_ID";
								sWork = sLink + "%%" + sField;
							}
							if (goTR.GetPrefix(sField) == "LNK_")
							{
								goErr.SetError(35010, sProc, "", sWork);
								//35010: Multi-level link not allowed in @tIDTable: '[1]'.
								//Return "SELECT '" & sProc & " Error: multi-level link not allowed in columns of @tIDTable: [" & sWork & "].' AS 'ERROR'"
							}
							sLinkTable = goTR.GetFileFromLinkName(sLink);
							//Determine the link type (1 or many) to compose the JOIN statement(s)
							sLinkType = goData.LKGetType(sTable, sLink);
							if (sLinkType == "")
							{
								goErr.SetError(10108, sProc, "", sTable + "." + sLink);
								//10108: Internal error: invalid link name '[1]'.
								//Return "SELECT '" & sProc & " Error: invalid link [" & sTable & "." & sLink & "].' AS 'ERROR'"
							}
							if (!goData.IsFieldValid(sLinkTable, sField))
							{
								goErr.SetError(10107, sProc, "", sTable + "." + sWork);
								//10107: Internal error: invalid field name '[1]'.
								//Return "SELECT '" & sProc & " Invalid field: [" & sTable & "." & sWork & "].' AS 'ERROR'"
							}
							if (sLinkType.Substring(sLinkType.Length - 1) == "1")
							{
								//Link is n to 1
								if (bUseLinkPermissions)
								{
									lWork = lWork + 1;
									//TOP is not 0, generate a subselect for the link field
									//First put together par_sAdditionalIni string with the statement to prepend to WHERE
									sWork3 = "";
									//MI 4/29/09 Added aliasing link table to distinguish the subselect table from main table in same-file links
									//In this case, the alias is always table name & "00000". Since the alias is for the table of the subselect
									//statement, it is not drawn from the pool of, not added to, a list of table aliases in this method.
									goTR.StrWrite(ref sWork3, "WHEREPREPEND", "[" + sLinkTable + "00000].[GID_ID] = [" + sTable + "].[GID_" + goTR.GetLinkPartFromLinkName(sLink) + "_" + sLinkTable + "]");
									sWork2 = "(";
									if (lTop == 0)
									{
										sWork2 += goData.GenerateSQL("", sLinkTable, sField, "", "", 0, false, "", "SIMPLESELECT", "", true, "R", false, false, false, sWork3);
									}
									else
									{
										sWork2 += goData.GenerateSQL("", sLinkTable, sField, "", "", 1, false, "", "SIMPLESELECT", "", true, "R", false, false, false, sWork3);
									}
									sWork2 += ") AS '" + sWork + "'";
									if (lWork > 1)
									{
										sResult += ", " + "\r\n";
									}
									sResult += sWork2;
								}
								else
								{
									//--------- OLD CODE IGNORES R PERMISSION ON LINKED FILE --------
									//Get or generate a table alias
									object temp_aAliases = aAliases;
									sAliasTable = goData.LKGetTableAlias(sTable, sLink, ref temp_aAliases);
										aAliases = (Selltis.BusinessLogic.clArray)temp_aAliases;
									if (sAliasTable == "")
									{
										goErr.SetError(35008, sProc, "", sTable + "." + sLink);
										//35008: goData.LKGetTableAlias failed on TableAlias.Link: '[1]'.
										//Return "SELECT '" & sProc & " Error: goData.LKGetTableAlias failed on TableAlias.Link: [" & sTable & "." & sLink & "].' AS 'ERROR'"
									}
									//MI 11/3/09 Commented
									//sWork2 = "[" & sAliasTable & "].[" & sField & "]"   'MI 5/13/08
									//MI 11/3/09 New code
									sWork2 = GetFieldExpression(sAliasTable, sField, sMode);
									//We must add all columns that the table variable contains
									//That's why we are not testing whether the same link has been added already
									//				If Position(sSortColumnsTemp, sWork2) < 1 Then
									lWork = lWork + 1;
									//Add JOIN statement
									//Example with CN LNK_Related_CO
									//LEFT JOIN CO ON CO.[GID_ID] = CN.[GID_Related_CO]			
									//MI 5/13/08
									sToAdd = sJoinWord + "JOIN [" + sLinkTable + "] [" + sAliasTable + "] ON " + "[" + sAliasTable + "].[GID_ID] = " + "[" + sTable + "].[GID_" + goTR.GetLinkPartFromLinkName(sLink) + "_" + sLinkTable + "]";
									if (sJoin.ToUpper().IndexOf(sToAdd.ToUpper()) + 1 < 1)
									{
										sJoin += "\r\n" + sToAdd;
									}
									//Add to columns
									if (lWork > 1) //MI 4/15/09 added & vbcrlf & vbTab
									{
										sResult += ", " + "\r\n" + "\t";
									}
									sResult += sWork2;
									//				END
								}

							}
							else
							{
								//Link is NN or 1N
								//Skip link in this recordset and create a separate recordset for it below.
								//An n-n link can end up here because it is used as an ORDER BY column.
								//Since we can't meaningfully sort by n links, we skip such links. Sorting
								//on n-n links has to be implemented in the middle tier.
								//The following is the old code, here for reference.
								//				'Example with CN.LNK_InvolvedWith_CO
								//				'LEFT JOIN CN_InvolvedWith_CO
								//				'        ON CN.GID_ID = CN_InvolvedWith_CO.GID_CN
								//				'LEFT JOIN CO
								//				'        ON CO.GID_ID = CN_InvolvedWith_CO.GID_CO
								//				sLinkPart = goTr:GetLinkPartFromLinkName(sLink)
								//				sJoin += CR+"LEFT JOIN "+sTable+"_"+sLinkPart+"_"+sLinkTable+" ON "+...
								//							sTable+".[GID_ID] = "+sTable+"_"+sLinkPart+"_"+sLinkTable+".[GID_"+sTable+"]"
								//				sJoin += CR+"LEFT JOIN "+sLinkTable+" ON "+...
								//							sLinkTable+".[GID_ID] = "+sTable+"_"+sLinkPart+"_"+sLinkTable+".[GID_"+sLinkTable+"]"
							}
						}
						else
						{
							//Direct field
							if (!goData.IsFieldValid(sTable, sWork))
							{
								goErr.SetError(10107, sProc, "", sTable + "." + sWork);
								//10107: Internal error: invalid field name '[1]'.
								//Return "SELECT '" & sProc & " Invalid field: [" & sTable & "." & sWork & "].' AS 'ERROR'"
							}
							switch (sMode)
							{
								case "SIMPLESELECT":
									//MI 11/3/09 Commented
									//sWork2 = "[" & sTable & "00000].[" & sWork & "]"
									//MI 11/3/09 New code 
									sWork2 = GetFieldExpression(sTable + "00000", sWork, sMode);
									break;
								case "GROUPBY":
								case "GROUPBYWITHROLLUP":
									//'Skip SYS_Name in GROUP mode
									//If UCase(sWork) = "SYS_NAME" Then GoTo ProcessNextColumn
									sWork2 = GetFieldExpression(sTable, sWork, sMode);
									break;
									//Case "COUNT" is redundant because only GID_ID is added in that mode (see above the Do loop code)
								default:
									//This whole section doesn't run in JOINANDWHEREONLY and JOINANDWHEREONLYFORNNLINKS case
									//MI 11/3/09 Commented
									//sWork2 = "[" & sTable & "].[" & sWork & "]"
									//MI 11/3/09 New code
									sWork2 = GetFieldExpression(sTable, sWork, sMode);
									break;
							}
							//We must add all fields that the table variable contains
							//That's why we are not testing whether the field has been added already
							//			If Position(sSortColumnsTemp, sWork2) < 1 Then 
							lWork = lWork + 1;
							if (lWork > 1) //MI 4/15/09 added & vbcrlf & vbTab
							{
								sResult += ", " + "\r\n" + "\t";
							}
							sResult += sWork2;
							//			END
						}
	ProcessNextColumn:
						lCount = lCount + 1;
					} while (true);
					break;
			}


			// ---------------- FROM ---------------- 
			switch (sMode)
			{
				case "SIMPLESELECT":
					sResult += "\r\n" + "FROM [" + sTable + "] " + sTable + "00000";
					break;
				case "JOINANDWHEREONLY":
				case "JOINANDWHEREONLYFORNNLINKS":
				break;
					//Nothing to add
				default:
					sResult += "\r\n" + "FROM [" + sTable + "] ";
					break;
			}


			// ------------ WHERE and JOINs ---------------- 
			if (lTop != 0)
			{
				//TOP is not 0, generate JOINS and WHERE
				//First generate and append TopRec quickselect condition(s) to the filter condition
				sWork = GenerateTopRecConditions(sTopRecord, sSort, par_bReverseSort);
				if (sWork != "")
				{
					if (sCondition == "")
					{
						sCondition = sWork;
					}
					else
					{
						sCondition = "(" + sCondition + ") AND (" + sWork + ")";
					}
				}

				//Loop through individual conditions parsed between ANDs and ORs and parens
				iLastAndOrLen = 0;
				if (sCondition != "")
				{
					sWhere = "";
					sWork = "";
					sWork2 = "";
					lCount = 1;
					iLastPos = 0;
					lPos = 0;
					iAndOrLen = 0;
					bBreakDo = false;

					do
					{
						//Parse condition for each field or link and add to WHERE and JOIN statements 
						bBreakDo = true;
						lPos = 0;
						iAndOrLen = 0;
						sAndOr = "";

						//Locate the next condition segment and set positioning variables via ByRef parameters
						FindNextConditionPart(sCondition, ref bBreakDo, ref lPos, ref iAndOrLen, ref sAndOr, ref iLastPos, ref iLastAndOrLen);

						//Extract the condition segment and remove parens
						sSingleCondString = ExtractConditionSegment(sCondition, ref iLastPos, ref iLastAndOrLen, ref lPos, ref sParensStart, ref sParensEnd);

						//Validate condition segment
						if (sSingleCondString == "")
						{
							goErr.SetError(35023, sProc, "", (iLastPos + iLastAndOrLen).ToString(), sCondition);
							//35023: A condition segment starting at character '[1]' is blank. Make sure that AND or OR words are not repeated. Condition:
							//
							//[2]
						}

						//Extract field, operator and value; evaluate the value with GetLineValue(); process date/time expressions
						ExtractFieldOperatorAndValue(sSingleCondString, ref sFieldPart, ref sOperator, ref sValue);

						//MI 3/25/10 Testing '(Any)' GID_ID and skipping the condition in that case
						sWork = goTR.RemoveSingleQuotes(sValue);
						bSkipCondition = false;
                        //if (sOperator == "=" && sWork.Substring(0, clC.SELL_SUIDFakePreFile.Length) == clC.SELL_SUIDFakePreFile && sWork.Substring(sWork.Length - clC.SELL_SUIDAnyPostFile.Length) == clC.SELL_SUIDAnyPostFile)
                        if (sOperator == "=" && sWork.Length >= clC.SELL_SUIDFakePreFile.Length && sWork.Length >= clC.SELL_SUIDAnyPostFile.Length && sWork.Substring(0, clC.SELL_SUIDFakePreFile.Length) == clC.SELL_SUIDFakePreFile && sWork.Substring(sWork.Length - clC.SELL_SUIDAnyPostFile.Length) == clC.SELL_SUIDAnyPostFile)
                        {
							//We may have an '(Any)' GID_ID. Check whether the field is GID_ID.
							if (sFieldPart == "GID_ID")
							{
								bSkipCondition = true;
							}
							else
							{
								if (goTR.GetPrefix(sFieldPart) == "LNK_")
								{
									//The field is a link - what's the field in the last hop (after last %%)?
									sField = goTR.ExtractStringFromEnd(sFieldPart, 1, "%%");
									if (sField[0] == clC.EOT)
									{
										//No field defined? Treat as GID_ID. This should never happen because we have LNK_...
										bSkipCondition = true;
									}
									else
									{
										//Field is defined
										if (sField == "GID_ID")
										{
											//Field is GID_ID
											bSkipCondition = true;
										}
										else
										{
											//Not GID_ID
											if (goTR.GetPrefix(sField) == "LNK_")
											{
												//THe last hop is a link, GID_ID is implied (code below will treat it that way)
												bSkipCondition = true;
											}
										}
									}
								}
							}
						}


						//---------------------------
						if (!bSkipCondition) //MI 3/25/10 Added
						{
							if (goTR.GetPrefix(sFieldPart) == "LNK_") //IF Left(sFieldPart,4) = "LNK_" THEN
							{
								//******************************************************************
								//This section supports multi-hop links
								//MI 9/13/07: started adding support for sOperator '<>'
								//Process all hops
								iLinkHop = 1;
								sFromTable = sTable;
								switch (sMode)
								{
									case "SIMPLESELECT": //, "JOINANDWHEREONLY"
										//Base table is aliased in subselect queries as CO00000, for ex, to distinguish it from 
										//aliases on the same table in case of same-file links (CO.LNK_Parent_CO).
										sFromAlias = sTable + "00000";
										break;
									default:
										sFromAlias = sTable;
										break;
								}
								sAliasTable = "";
								bLastHop = false;
								sAltCond = "";
								do
								{
									//We already processed the last hop, exit the loop
									if (bLastHop)
									{
										break;
									}
									sHop = goTR.ExtractString(sFieldPart, iLinkHop, "%%").Trim(' ');
									//No field defined - we should never get here
									if (sHop == "" || sHop[0] == clC.EOT)
									{
										break;
									}
									//We found a non-link field which we processed during the last loop, exit
									if (goTR.GetPrefix(sHop) != "LNK_")
									{
										break;
									}

									sLink = sHop;
									//Read the next hop - is it a field or a link?   
									sField = (goTR.ExtractString(sFieldPart, iLinkHop + 1, "%%")).Trim(' ');
									if (sField == "" || sField[0] == clC.EOT)
									{
										//Next hop doesn't exist   
										sField = "GID_ID";
										bLastHop = true;
									}
									else
									{
										//Next hop exists   
										if (goTR.GetPrefix(sField) == "LNK_")
										{
											//Next hop is a link   
											sField = "GID_ID";
											bLastHop = false;
										}
										else
										{
											//Next hop is a field
											bLastHop = true;
										}
									}
									//***************************************************
									sLinkTable = goTR.GetFileFromLinkName(sLink);
									//*** MI 8/14/07
									if (sLinkTable == sFromTable)
									{
										sLinkTableNo = "2";
									}
									else
									{
										sLinkTableNo = "";
									}
									sLinkPart = goTR.GetLinkPartFromLinkName(sLink);

									//Determine the link type (1 or many) to compose the JOIN statement(s)
									//FromTo gives us the table without the alias numeric extension
									sLinkType = goData.LKGetType(sFromTable, sLink);
									if (sLinkType == "")
									{
										goErr.SetError(10108, sProc, "", sFromTable + "." + sLink);
										//10108: Internal error: invalid link name '[1]'.
										//Return "SELECT '" & sProc & " Error: invalid link [" & sFromTable & "." & sLink & "].' AS 'ERROR'"
									}
									sWork2 = "";
									//Get or generate a table alias
									if (bUseLinkPermissions)
									{
										//MI 5/6/09 Added True in par_bForceUnique to force unique link table aliases.
										//This  prevents SQL error 'The correlation name 'US00001' is specified multiple times in a FROM clause.'
										object temp_aAliases = aAliases;
										sAliasTable = goData.LKGetTableAlias(sFromAlias, sLink, ref temp_aAliases, true);
											aAliases = (Selltis.BusinessLogic.clArray)temp_aAliases;
									}
									else
									{
										//Using non-unique table aliases the 'old way'
										object temp_aAliases = aAliases;
										sAliasTable = goData.LKGetTableAlias(sFromAlias, sLink, ref temp_aAliases, false);
											aAliases = (Selltis.BusinessLogic.clArray)temp_aAliases;
									}
									if (sAliasTable == "")
									{
										goErr.SetError(35011, sProc, "", sFromAlias + "." + sLink);
										//35011: goData.LKGetTableAlias failed on TableAlias.Link: '[1]'.
										//Return "SELECT '" & sProc & " Error: goData.LKGetTableAlias failed on TableAlias.Link: [" & sFromAlias & "." & sLink & "].' AS 'ERROR'"
									}
									if (!goData.IsFieldValid(sLinkTable, sField))
									{
										goErr.SetError(10107, sProc, "", sTable + "." + sFieldPart);
										//10107: Internal error: invalid field name '[1]'.
										//Return "SELECT '" & sProc & " Invalid field: [" & sTable & "." & sFieldPart & "].' AS 'ERROR'"
									}
									switch (sLinkType)
									{
										case "N1":
											//Link is N to 1
											//Example: CN.LNK_Related_CO
											//LEFT JOIN CO CO00001 ON CO00001.[GID_ID] = CN.[GID_Related_CO]
											sLinkPart = goTR.GetLinkPartFromLinkName(sLink);
											sWork2 = sJoinWord + "JOIN [" + sLinkTable + "] [" + sAliasTable + "] ON " + "[" + sAliasTable + "].[GID_ID] = [" + sFromAlias + "].[GID_" + sLinkPart + "_" + sLinkTable + "]";
											if (sJoin.ToUpper().IndexOf(sWork2.ToUpper()) + 1 < 1 && (sWhereJoin.ToUpper().IndexOf(sWork2.ToUpper()) + 1) < 1)
											{
												switch (sMode)
												{
													case "JOINANDWHEREONLY":
													case "JOINANDWHEREONLYFORNNLINKS":
														//Don't add LEFT JOIN if the same JOIN (INNER JOIN) is defined in par_sAdditionalIni
														sWork3 = goTR.StrRead(par_sAdditionalIni, "EXCLUDEJOINS", "", false).Trim(' ');
														if (sWork3.ToUpper().Substring(0, 5) != sJoinWord)
														{
															sWork3 = sJoinWord + sWork3;
														}
														if (sWork3.IndexOf(sWork2) + 1 < 1)
														{
															sWhereJoin += "\r\n" + sWork2;
														}
														break;
													default:
														sWhereJoin += "\r\n" + sWork2;
														break;
												}
											}
											break;
										case "1N":
											//Link is 1 to N
											//Example: CN.LNK_Connected_EL
											//LEFT JOIN EL EL00002 ON EL00002.[GID_Related_CN] = CN.[GID_ID]
											sRevLinkName = goData.LKGetInverseName(sFromTable, sLink);
											//sResult &= vbCrLf & "JOIN @tIDTable idt ON " & _
											//    "idt.[" & sFromTable & "_GID_ID] = " & sLinkTable & _
											//    ".[GID_" & goTR.GetLinkPartFromLinkName(sRevLinkName) & "_" & goTR.GetFileFromLinkName(sRevLinkName) & "]"

											sLinkPart = goTR.GetLinkPartFromLinkName(sLink);
											sWork2 = sJoinWord + "JOIN [" + sLinkTable + "] [" + sAliasTable + "] ON " + "[" + sAliasTable + "].[GID_" + goTR.GetLinkPartFromLinkName(sRevLinkName) + "_" + goTR.GetFileFromLinkName(sRevLinkName) + "] = " + "[" + sFromAlias + "].[GID_ID]";
											if (sJoin.ToUpper().IndexOf(sWork2.ToUpper()) + 1 < 1 && (sWhereJoin.ToUpper().IndexOf(sWork2.ToUpper()) + 1) < 1)
											{
												switch (sMode)
												{
													case "JOINANDWHEREONLY":
													case "JOINANDWHEREONLYFORNNLINKS":
														//Don't add LEFT JOIN if the same JOIN (INNER JOIN) is defined in EXCLUDEJOINS in par_sAdditionalIni
														sWork3 = goTR.StrRead(par_sAdditionalIni, "EXCLUDEJOINS", "", false).Trim(' ');
														if (sWork3.ToUpper().Substring(0, 5) != sJoinWord)
														{
															sWork3 = sJoinWord + sWork3;
														}
														if (sWork3.IndexOf(sWork2) + 1 < 1)
														{
															sWhereJoin += "\r\n" + sWork2;
														}
														break;
													default:
														sWhereJoin += "\r\n" + sWork2;
														break;
												}
											}
											break;
										default:
											//Link is N to N
											//Example: CN.LNK_InvolvedWith_CO
											//LEFT JOIN CN_InvolvedWith_CO
											//        ON CN.GID_ID = CN_InvolvedWith_CO.GID_CN
											//LEFT JOIN CO CO00003
											//        ON CO00003.GID_ID = CN_InvolvedWith_CO.GID_CO
											if (goData.LKGetDirection(sFromTable, sLink) == 1)
											{
												sLinkPart = goTR.GetLinkPartFromLinkName(sLink);
												sLinkingTable = sFromTable + "_" + sLinkPart + "_" + sLinkTable;
												//MI 3/24/08 Moved sLinkTableNo from first to second LEFT JOIN - this was wrong in same-file links
												if (bUseLinkPermissions)
												{
													//Link permissions observed
													//This will update the alias number in aLinkingTableAliases - run it only when necessary
													object temp_aLinkingTableAliases = aLinkingTableAliases;
													sLinkingTableAlias = goData.LKGetLinkingTableAlias(sLinkingTable, ref temp_aLinkingTableAliases);
														aLinkingTableAliases = (Selltis.BusinessLogic.clArray)temp_aLinkingTableAliases;
													sWork2 = sJoinWord + "JOIN [" + sLinkingTable + "] [" + sLinkingTableAlias + "] ON " + "[" + sFromAlias + "].[GID_ID] = [" + sLinkingTableAlias + "].[GID_" + sFromTable + "]";
												}
												else
												{
													//Link permissions not observed
													sWork2 = sJoinWord + "JOIN [" + sFromTable + "_" + sLinkPart + "_" + sLinkTable + "] ON " + "[" + sFromAlias + "].[GID_ID] = [" + sFromTable + "_" + sLinkPart + "_" + sLinkTable + "].[GID_" + sFromTable + "]";
												}
												if (sJoin.ToUpper().IndexOf(sWork2.ToUpper()) + 1 < 1 && (sWhereJoin.ToUpper().IndexOf(sWork2.ToUpper()) + 1) < 1)
												{
													switch (sMode)
													{
														case "JOINANDWHEREONLY":
														case "JOINANDWHEREONLYFORNNLINKS":
															//Don't add LEFT JOIN if the same JOIN (INNER JOIN) is defined in EXCLUDEJOINS in par_sAdditionalIni
															sWork3 = goTR.StrRead(par_sAdditionalIni, "EXCLUDEJOINS", "", false).Trim(' ');
															if (sWork3.ToUpper().Substring(0, 5) != sJoinWord)
															{
																sWork3 = sJoinWord + sWork3;
															}
															if (sWork3.IndexOf(sWork2) + 1 < 1)
															{
																sWhereJoin += "\r\n" + sWork2;
															}
															break;
														default:
															sWhereJoin += "\r\n" + sWork2;
															break;
													}
												}
												if (bUseLinkPermissions)
												{
													//Link permissions observed
													sWork2 = sJoinWord + "JOIN [" + sLinkTable + "] [" + sAliasTable + "] ON " + "[" + sAliasTable + "].[GID_ID] = [" + sLinkingTableAlias + "].[GID_" + sLinkTable + sLinkTableNo + "]";
												}
												else
												{
													//Link permissions not observed
													sWork2 = sJoinWord + "JOIN [" + sLinkTable + "] [" + sAliasTable + "] ON " + "[" + sAliasTable + "].[GID_ID] = [" + sFromTable + "_" + sLinkPart + "_" + sLinkTable + "].[GID_" + sLinkTable + sLinkTableNo + "]";
												}
												if (sJoin.ToUpper().IndexOf(sWork2.ToUpper()) + 1 < 1 && (sWhereJoin.ToUpper().IndexOf(sWork2.ToUpper()) + 1) < 1)
												{
													switch (sMode)
													{
														case "JOINANDWHEREONLY":
														case "JOINANDWHEREONLYFORNNLINKS":
															//Don't add LEFT JOIN if the same JOIN (INNER JOIN) is defined in EXCLUDEJOINS in par_sAdditionalIni
															sWork3 = goTR.StrRead(par_sAdditionalIni, "EXCLUDEJOINS", "", false).Trim(' ');
															if (sWork3.ToUpper().Substring(0, 5) != sJoinWord)
															{
																sWork3 = sJoinWord + sWork3;
															}
															if (sWork3.IndexOf(sWork2) + 1 < 1)
															{
																sWhereJoin += "\r\n" + sWork2;
															}
															break;
														default:
															sWhereJoin += "\r\n" + sWork2;
															break;
													}
												}
												if (sOperator == "<>")
												{
													if (sAltCond != "")
													{
														sAltCond += " AND ";
													}
													//EXAMPLE:
													//not exists (SELECT * FROM cn_related_gr WHERE cn_related_gr.GID_CN = CN.GID_ID and cn_related_gr.GID_GR = '3A4AA4C4-0293-47BC-4752-99A40108FB43')
													sAltCond += "not exists (SELECT * FROM [" + sLinkingTable + "] WHERE " + "[" + sLinkingTable + "].[GID_" + sFromTable + "] = " + "[" + sFromTable + "].[GID_ID] and " + goTR.GetSQLCondition("[" + sLinkingTable + "].[GID_" + sLinkTable + "]", "=", sValue, true) + ")";
													//"[" & sFromTable & "].[GID_ID] and [" & sLinkingTable & "].[GID_" & sLinkTable & "] = " & sValue & ")"
												}
											}
											else
											{
												//Reverse direction  '*** MI 5/19/06
												sLinkPart = goTR.GetLinkPartFromLinkName(sLink);
												sRevLinkName = goData.LKGetInverseName(sFromTable, sLink);
												sRevLinkPart = goTR.GetLinkPartFromLinkName(sRevLinkName);
												sRevLinkFile = goTR.GetFileFromLinkName(sLink);
												sLinkingTable = sRevLinkFile + "_" + sRevLinkPart + "_" + sFromTable;
												//MI 3/24/08 Moved sLinkTableNo from second to first LEFT JOIN - this was wrong in same-file links
												if (bUseLinkPermissions)
												{
													//Link permissions observed
													//This will update the alias number in aLinkingTableAliases - run it only when necessary
													object temp_aLinkingTableAliases = aLinkingTableAliases;
													sLinkingTableAlias = goData.LKGetLinkingTableAlias(sLinkingTable, ref temp_aLinkingTableAliases);
														aLinkingTableAliases = (Selltis.BusinessLogic.clArray)temp_aLinkingTableAliases;
													sWork2 = sJoinWord + "JOIN [" + sLinkingTable + "] [" + sLinkingTableAlias + "] ON " + "[" + sFromAlias + "].[GID_ID] = [" + sLinkingTableAlias + "].[GID_" + sFromTable + sLinkTableNo + "]";
												}
												else
												{
													//Link permissions not observed
													sWork2 = sJoinWord + "JOIN [" + sRevLinkFile + "_" + sRevLinkPart + "_" + sFromTable + "] ON " + "[" + sFromAlias + "].[GID_ID] = [" + sRevLinkFile + "_" + sRevLinkPart + "_" + sFromTable + "].[GID_" + sFromTable + sLinkTableNo + "]";
												}

												if (sJoin.ToUpper().IndexOf(sWork2.ToUpper()) + 1 < 1 && (sWhereJoin.ToUpper().IndexOf(sWork2.ToUpper()) + 1) < 1)
												{
													switch (sMode)
													{
														case "JOINANDWHEREONLY":
														case "JOINANDWHEREONLYFORNNLINKS":
															//Don't add LEFT JOIN if the same JOIN (INNER JOIN) is defined in EXCLUDEJOINS in par_sAdditionalIni
															sWork3 = goTR.StrRead(par_sAdditionalIni, "EXCLUDEJOINS", "", false).Trim(' ');
															if (sWork3.ToUpper().Substring(0, 5) != sJoinWord)
															{
																sWork3 = sJoinWord + sWork3;
															}
															if (sWork3.IndexOf(sWork2) + 1 < 1)
															{
																sWhereJoin += "\r\n" + sWork2;
															}
															break;
														default:
															sWhereJoin += "\r\n" + sWork2;
															break;
													}
												}
												if (bUseLinkPermissions)
												{
													//Link permissions observed
													sWork2 = sJoinWord + "JOIN [" + sLinkTable + "] [" + sAliasTable + "] ON " + "[" + sAliasTable + "].[GID_ID] = [" + sLinkingTableAlias + "].[GID_" + sLinkTable + "]";
												}
												else
												{
													//Link permissions not observed
													sWork2 = sJoinWord + "JOIN [" + sLinkTable + "] [" + sAliasTable + "] ON " + "[" + sAliasTable + "].[GID_ID] = [" + sRevLinkFile + "_" + sRevLinkPart + "_" + sFromTable + "].[GID_" + sLinkTable + "]";
												}
												if (sJoin.ToUpper().IndexOf(sWork2.ToUpper()) + 1 < 1 && (sWhereJoin.ToUpper().IndexOf(sWork2.ToUpper()) + 1) < 1)
												{
													switch (sMode)
													{
														case "JOINANDWHEREONLY":
														case "JOINANDWHEREONLYFORNNLINKS":
															//Don't add LEFT JOIN if the same JOIN (INNER JOIN) is defined in EXCLUDEJOINS in par_sAdditionalIni
															sWork3 = goTR.StrRead(par_sAdditionalIni, "EXCLUDEJOINS", "", false).Trim(' ');
															if (sWork3.ToUpper().Substring(0, 5) != sJoinWord)
															{
																sWork3 = sJoinWord + sWork3;
															}
															if (sWork3.IndexOf(sWork2) + 1 < 1)
															{
																sWhereJoin += "\r\n" + sWork2;
															}
															break;
														default:
															sWhereJoin += "\r\n" + sWork2;
															break;
													}
												}
												if (sOperator == "<>")
												{
													if (sAltCond != "")
													{
														sAltCond += " AND ";
													}
													//EXAMPLE:
													//not exists (SELECT * FROM cn_related_gr WHERE cn_related_gr.GID_CN = CN.GID_ID and cn_related_gr.GID_GR = '3A4AA4C4-0293-47BC-4752-99A40108FB43')
													//MI 2/24/10 Replaced sLinkingTableAlias with sLinkingTable to fix a big where the file name was blank
													sAltCond += "not exists (SELECT * FROM [" + sLinkingTable + "] WHERE " + "[" + sLinkingTable + "].[GID_" + sFromTable + "] = " + "[" + sFromTable + "].[GID_ID] and " + goTR.GetSQLCondition("[" + sLinkingTable + "].[GID_" + sLinkTable + "]", "=", sValue, true) + ")";
													//"[" & sFromTable & "].[GID_ID] and [" & sLinkingTableAlias & "].[GID_" & sLinkTable & "] = " & sValue & ")"
												}
											}
											break;
									}

									//Compose the single condition for inclusion in the WHERE statement
									if (bLastHop)
									{
										//MI 3/25/10 Skipping the condition if GID_ID = '(Any)' (meaning don't filter linked views by selected 
										//record in a view that has the (Any) line selected)
										if (sField == "GID_ID" && sValue == goData.GenSUIDAnyFake(sLinkTable))
										{

										}
										if (sAltCond != "")
										{
											sSQLCond = sAltCond;
										}
										else
										  {
											sSQLCond = goTR.GetSQLCondition("[" + sAliasTable + "].[" + sField + "]", sOperator, sValue, true);
										}
									}
									//******************************
									//Multi-hop link support
									sFromTable = sLinkTable; //***
									sFromAlias = sAliasTable; //***
									iLinkHop = iLinkHop + 1; //***
								} while (true);
								//***************************
							}
							else
							{
								//Direct fields require no joins and use a direct table name (not an alias)
								//except in SIMPLESELECT and JOINANDWHEREONLY modes
								if (!goData.IsFieldValid(sTable, sFieldPart))
								{
									goErr.SetError(10107, sProc, "", sTable + "." + sFieldPart);
									//10107: Internal error: invalid field name '[1]'.
									//Return "SELECT '" & sProc & " Invalid field: [" & sTable & "." & sFieldPart & "].' AS 'ERROR'"
								}
								switch (sMode)
								{
									case "SIMPLESELECT":
									case "JOINANDWHEREONLY":
										sAliasTable = sTable + "00000";
										sSQLCond = goTR.GetSQLCondition("[" + sAliasTable + "].[" + sFieldPart + "]", sOperator, sValue, false);
										break;
									default:
										//Includes "JOINANDWHEREONLYFORNNLINKS" mode
										sSQLCond = goTR.GetSQLCondition("[" + sTable + "].[" + sFieldPart + "]", sOperator, sValue, false);
										break;
								}
							}
						}
						if (bSkipCondition) //MI 3/25/10 added
						{
							//Skip the condition by replacing it with "1=1". This way we keep the AND/OR keywords and parens intact.
							sWhere += sParensStart + "1=1" + sParensEnd + sAndOr;
						}
						else
						{
							sWhere += sParensStart + sSQLCond + sParensEnd + sAndOr;
						}
						iLastAndOrLen = iAndOrLen;
						if (bBreakDo)
						{
							break;
						}
						lCount = lCount + 1;
						iLastPos = (int)lPos;
					} while (true);

					//'Add JOIN and WHERE to the statement    '*** MI 4/17/06 Moved below
					//If sJoin & sWhereJoin <> "" Then
					//    sResult &= sJoin & sWhereJoin
					//End If
					//If sWhere <> "" Then
					//    sResult &= vbCrLf & "WHERE " & sWhere
					//End If
				}

				//Add JOIN and WHERE to the statement    /*** MI 4/17/06 Moved from inside If ... End If above
				//--- JOIN ---
				if (sJoin + sWhereJoin != "")
				{
					sResult += sJoin + sWhereJoin;
				}
				//--- WHERE ---
				//MI 4/21/09 added prepending/appending additional WHERE statement(s)
				switch (sMode)
				{
					case "SIMPLESELECT":
					case "JOINANDWHEREONLY":
						//Prepend and append to WHERE statement if necessary
						sWork2 = goTR.StrRead(par_sAdditionalIni, "WHEREPREPEND", "", false);
						if (sWork2.Trim(' ') != "")
						{
							if (sWhere.Trim(' ') == "")
							{
								sWhere = sWork2;
							}
							else
							{
								sWhere = "(" + sWork2 + ") and (" + sWhere + ")";
							}
						}
						sWork2 = goTR.StrRead(par_sAdditionalIni, "WHEREAPPEND", "", false);
						if (sWork2.Trim(' ') != "")
						{
							if (sWhere.Trim(' ') == "")
							{
								sWhere = sWork2;
							}
							else
							{
								sWhere = "(" + sWhere + ") and (" + sWork2 + ")";
							}
						}
						break;
				}
				if (sWhere != "")
				{
					sResult += "\r\n" + "WHERE " + sWhere;
				}

			}


			// ---------------- ORDER BY ---------------- 
			if (lTop != 0)
			{
				//TOP is not 0, add ORDER BY
				switch (sMode)
				{
					case "COUNT":
					case "SIMPLESELECT":
					case "JOINANDWHEREONLY":
					case "JOINANDWHEREONLYFORNNLINKS":
					break;
						//Don't need sort
					default:
						//If sSort <> "" Then    '*** MI 4/17/06
						if (sSortString.Trim(' ') != "") //*** MI 4/17/06
						{
							sResult += "\r\n" + "ORDER BY " + sSortString;
						} //*** MI 4/17/06
						break;
						//Else    '*** MI 4/17/06
						//    sResult &= vbCrLf & "ORDER BY " & sTable & ".[SYS_Name]"    '*** MI 4/17/06
						//End If    '*** MI 4/17/06
				}
			}

			switch (sMode)
			{
				case "SIMPLESELECT":
					goto ReturnTheResult;
				case "JOINANDWHEREONLY":
				case "JOINANDWHEREONLYFORNNLINKS": //, "COUNT" 'MI 2/14/10 commented COUNT
					//Remove the starting hard return
					if (goTR.FromTo(sResult, 1, 2) == "\r\n")
					{
						sResult = goTR.FromTo(sResult, 3, -1);
					}
					goto ReturnTheResult;
			}




			// ================ FIRST RECORDSET: MAIN TABLE ===================
			//Main SELECT statement
			//SELECT CN.TXT_NameLast,
			//	CN.TXT_NameFirst, 
			//	CN.TXT_CompanyNameText AS 'Company Text',
			//	CO00002.TXT_CompanyName AS 'Related Company',
			//	US00001.TXT_NameLast, 
			//	US00001.TXT_NameFirst, 
			//	CN.GID_ID 
			//FROM CN 
			//JOIN @tIDTable idt ON idt.GID_ID = CN.GID_ID
			//-- JOINS are for SELECT and ORDER BY columns only.
			//-- There is no WHERE statement in this query
			//-- Only n-1 columns are processed, n-n are ignored.
			//LEFT JOIN US US00001 ON US00001.GID_ID = CN.GID_CreatedBy_US 
			//LEFT JOIN CO CO00002 ON CO00002.GID_ID = CN.GID_Related_CO
			//ORDER BY CN.TXT_NameLast, CN.TXT_NameFirst	--, CO00002.TXT_CompanyName 

			sResult += "\r\n" + "\r\n" + GenerateMainSelectStatement(ref sJoin, ref sFields, ref sSort, sTable, ref aAliases, sMode, ref sNLinks, ref sSortString, lTop, bUseLinkPermissions, bUseInnerJoins);



			//---------- We are finished in the COUNT mode -----------
			if (sMode == "COUNT") //Return sResult
			{
				goto ReturnTheResult;
			}
			//--------------------------------------------------------



			// ============ 2ND RECORDSET: LIST OF N-N LINKS ==================
			//Create a table of N-N link names
			sResult += "\r\n";
			lCount = 1;
			do
			{
				//At this point sNLinks should not have any duplicates
				sWork = goTR.ExtractString(sNLinks, (int)lCount, ",").Trim(' ');
                //if (sWork[0] == clC.EOT || sWork == "")
                if (string.IsNullOrEmpty(sWork) || sWork[0] == clC.EOT)
                {
                    break;
				}
				sResult += "\r\n" + "INSERT @tManyLinkList " + "\r\n" + "VALUES('" + sWork + "') ";
				//A more verbose form of the above:
				//sResult &= vbCrLf & "INSERT INTO @tManyLinkList " & vbCrLf & "VALUES('" & sWork & "') "
				lCount = lCount + 1;
			} while (true);

            //Exit if there are no n-n link recordsets to return
            //if (lCount == 1 && (sWork[0] == clC.EOT || sWork == ""))
            if (lCount == 1 && (sWork == "" || sWork[0] == clC.EOT))

            {
                goto ReturnTheResult;
				//Return sResult
			}

			sResult += "\r\n" + "SELECT * FROM @tManyLinkList";



			// ================= N-N LINK RECORDSETS ==========================
			//Example of N-N link:
			//-- CN.LNK_InvolvedWith_CO%%TXT_CompanyName
			//SELECT DISTINCT idt.[CN_GID_ID] AS 'BASE%%GID_ID', 
			//	CN_InvolvedWith_CO.[GID_ID] AS 'GID_ID', 
			//	CO.[TXT_CompanyName] AS 'LNK_InvolvedWith_CO%%TXT_CompanyName', 
			//	CO.[SYS_Name] AS 'LNK_InvolvedWith_CO%%SYS_Name' 
			//FROM CO
			//JOIN CN_InvolvedWith_CO ON CO.[GID_ID] = CN_InvolvedWith_CO.[GID_CO]
			//JOIN @tIDTable idt ON idt.[CN_GID_ID] = CN_InvolvedWith_CO.[GID_CN]
			//ORDER BY idt.[CN_GID_ID], CO.[SYS_Name]
			//-------------------
			//Example of 1-N link:
			//-- CN.LNK_Connected_EL
			//SELECT DISTINCT idt.[CN_GID_ID] AS 'BASE%%GID_ID', 
			//    EL.[GID_ID] AS 'GID_ID', 
			//    EL.[GID_ID] AS 'LNK_Connected_EL%%GID_ID', 
			//    EL.[SYS_Name] AS 'LNK_Connected_EL%%SYS_Name' 
			//FROM EL
			//JOIN @tIDTable idt ON idt.[CN_GID_ID] = EL.[GID_Related_CN]
			//ORDER BY idt.[CN_GID_ID], EL.[SYS_Name]

			//MI 11/11/08 deprecating LinksTop
			//'The number of links returned is controlled by:
			//'par_iLinksTop (-1 means all)
			//'par_sLinksTop
			//'Turn pipe-delimited string into an ini string
			//sLinksTop = goTR.Replace(par_sLinksTop, "|", vbCrLf)

			lCount = 1;
			do
			{
				sWork = goTR.ExtractString(sNLinks, (int)lCount, ",").Trim(' ');
                //if (sWork[0] == clC.EOT || sWork == "")
                if (string.IsNullOrEmpty(sWork) || sWork[0] == clC.EOT)
                {
                    break;
				}
				sTableVarName = "@tIDTable" + lCount.ToString();
				if (sWork.IndexOf("%%") + 1 > 0) //InStr()
				{
					sLink = goTR.ExtractString(sWork, 1, "%%");
					sField = goTR.ExtractString(sWork, 2, "%%");
				}
				else
				{
					sLink = sWork;
					sField = "GID_ID";
					sWork = sLink + "%%" + sField;
				}
				if (goTR.GetPrefix(sField) == "LNK_")
				{
					goErr.SetError(35016, sProc, "", sWork);
					//35016: Multi-level link not allowed in N link recordsets: '[1]'.
					//Return "SELECT '" & sProc & " Error: Multi-level link not allowed in N link recordsets: [" & sWork & "].' AS 'ERROR'"
				}
				sLinkTable = goTR.GetFileFromLinkName(sLink);


				//'DEBUG
				//If UCase(sLink) = "LNK_INVOLVEDIN_TD" Then Stop
				//'END DEBUG


				//*** MI 8/14/07
				if (sLinkTable == sTable)
				{
					sLinkTableNo = "2";
				}
				else
				{
					sLinkTableNo = "";
				}
				sLinkPart = goTR.GetLinkPartFromLinkName(sLink);

				//MI 11/11/08 ------------- Deprecating LinksTop ------------------
				//'MI 10/20/08 Added the following block to support limiting no of NN links
				//'Determine how many links to return
				//'Start with the par_iLinksTop value
				//iTop = par_iLinksTop
				//If iTop < -1 Then iTop = -1
				//'See whether there is a definition for this particular link 
				//sTopString = goTR.StrRead(sLinksTop, sWork, "NOT DEFINED", False)
				//If sTopString = "NOT DEFINED" Then
				//    'No definition found for the link field
				//    If sField = "GID_ID" Then
				//        sTopString = goTR.StrRead(sLinksTop, goTR.ExtractString(sWork, 1, "%%"), "NOT DEFINED", False)
				//        If sTopString <> "NOT DEFINED" Then
				//            'Definition found for the link (GID_ID)
				//            'Link override exists, use it if numeric
				//            iTop = goTR.StringToNum(sTopString, "0", iValid)
				//            If iValid <> clC.SELL_TYPE_VALID Then
				//                goErr.SetError(35000, sProc, "Invalid value '" & sTopString & "' found in par_sLinksTop. The value must be -1, 0, or a positive integer. par_sLinksTop: '" & par_sLinksTop & "'.")
				//            End If
				//        End If
				//    End If
				//Else
				//    'Link field 'top' value defined
				//    iTop = goTR.StringToNum(sTopString, "0", iValid)
				//    If iValid <> clC.SELL_TYPE_VALID Then
				//        goErr.SetError(35000, sProc, "Invalid value '" & sTopString & "' found in par_sLinksTop. The value must be -1, 0, or a positive integer. par_sLinksTop: '" & par_sLinksTop & "'.")
				//    End If
				//End If

				//sTopStatement = ""              'MI 11/4/08 added
				//'MI 11/4/08 commented the following block to disable limiting the no of links
				//'This doesn't work because it limits the no of links returned for ALL records
				//'in the base table. That's why the feature is being deprecated.
				//'If iTop >= 0 Then
				//'    sTopStatement = "TOP " & iTop.ToString & " "
				//'Else
				//'    sTopStatement = ""
				//'End If

				//MI 5/29/09
				//Use TOP 0 in the statement if the main recordset is TOP 0.
				if (lTop == 0)
				{
					sTopStatement = "TOP 0 ";
				}

				//---------- SELECT ----------
				//DEBUG
				//If UCase(sLink) = "LNK_INVOLVEDWITH_CO" Then Stop
				//END DEBUG

				sLinkType = goData.LKGetType(sTable, sLink);
				if (sLinkType == "")
				{
					goErr.SetError(10108, sProc, "", sTable + "." + sLink);
					//10108: Internal error: invalid link name '[1]'.
					//Return "SELECT '" & sProc & " Error: invalid link [" & sTable & "." & sLink & "].' AS 'ERROR'"
				}
				if (!goData.IsFieldValid(sLinkTable, sField))
				{
					goErr.SetError(10107, sProc, "", sTable + "." + sWork);
					//10107: Internal error: invalid field name '[1]'.
					//Return "SELECT '" & sProc & " Invalid field: [" & sTable & "." & sWork & "].' AS 'ERROR'"
				}

				if (sLinkType == "1N")
				{
					//1N' type

					if (bUseLinkPermissions)
					{
						//******************************* NEW CODE ********************************
						sRevLinkName = goData.LKGetInverseName(sTable, sLink);
						sRevLinkPart = goTR.GetLinkPartFromLinkName(sRevLinkName);
						sRevLinkFile = sTable; //goTR.GetFileFromLinkName(sLink)
						//Examples show CN.LNK_Connected_EL link
						//---------- DECLARE ---------
						//DECLARE @tIDTable1 TABLE(
						//    [CN_GID_ID] uniqueidentifier, 
						//    [EL_GID_ID] uniqueidentifier,
						//    [EL_SYS_Name] nvarchar(300))
						sResult += "\r\n" + "\r\n" + "DECLARE " + sTableVarName + " TABLE(" + "\r\n";
						sResult += "\t" + "[BASE_GID_ID] uniqueidentifier, " + "\r\n";
						sResult += "\t" + "[" + sLinkTable + "_GID_ID] uniqueidentifier, " + "\r\n";
						sResult += "\t" + "[" + sLinkTable + "_SYS_NAME] nvarchar(300)) " + "\r\n";
						//------ INSERT INTO @tIDTable<n> --------
						//INSERT INTO @tNNIDTable([CN_GID_ID], [EL_GID_ID], [EL_SYS_Name])
						sResult += "INSERT INTO " + sTableVarName + "([BASE_GID_ID], [" + sLinkTable + "_GID_ID], [" + sLinkTable + "_SYS_NAME])" + "\r\n";
						//------ FIRST SELECT --------
						//SELECT DISTINCT idt.[CN_GID_ID] AS 'BASE%%GID_ID', 
						//    EL.[GID_ID] AS 'GID_ID', 
						//    EL.[GID_ID] AS 'LNK_Connected_EL%%GID_ID', 
						//    EL.[SYS_Name] AS 'LNK_Connected_EL%%SYS_Name' 
						//FROM EL
						//*** MI 4/5/06 Removed SELECT DISTINCT - disallowed memo (ntext) fields in the query ***
						//*** MI 5/27/09 Added SELECT DISTINCT to support multiple unique aliases on same tables, which cause
						//duplicate records to be returned. We can do DISTINCT now because memos (ntext) are never in this
						//SELECT - they are in the SELECT below, which is JOINed to the table variable which this SELECT fills
						//with IDs and SYS_Names.
						sResult += "SELECT DISTINCT " + sTopStatement + "idt.[" + sTable + "_GID_ID], " + "\r\n";
						sResult += "\t" + "[" + sLinkTable + "].[GID_ID], " + "\r\n";
						//SYS_Name is here for sorting. If sorting is not important in the table variable, SYS_Name could be removed here.
						sResult += "\t" + "[" + sLinkTable + "].[SYS_Name] " + "\r\n";
						sResult += "FROM [" + sLinkTable + "]" + "\r\n";
						//---------- STATIC JOINs -----------
						//JOIN @tIDTable idt ON idt.[CN_GID_ID] = EL.[GID_Related_CN]
						sResult += "JOIN @tIDTable idt ON " + "idt.[" + sTable + "_GID_ID] = [" + sLinkTable + "].[GID_" + sRevLinkPart + "_" + sRevLinkFile + "]";
						if (lTop != 0)
						{
							//---------- JOIN and WHERE -----------
							//Generate JOIN and WHERE to observe R perm in linked file
							sWork2 = goData.GenerateSQL("", sLinkTable, "", "", "", -1, false, "", "JOINANDWHEREONLYFORNNLINKS");
							sResult += "\r\n" + sWork2;
							//-------- ORDER BY ---------
							//ORDER BY idt.[CN_GID_ID], EL.[SYS_Name]
							sResult += "\r\n" + "ORDER BY idt.[" + sTable + "_GID_ID], [" + sLinkTable + "].[SYS_Name]";
						}

						//--------- SECOND SELECT --------
						//SELECT 
						//	nnidt1.[CN_GID_ID] AS 'BASE%%GID_ID',
						//	nnidt1.[EL_GID_ID] AS 'GID_ID', 
						//	[EL].[GID_ID] AS 'LNK_Connected_EL%%GID_ID', 
						//	[EL].[SYS_Name] AS 'LNK_Connected_EL%%SYS_Name'
						//FROM [EL] 
						//JOIN @tIDTable1 nnidt1 ON nnidt1.[EL_GID_ID] = [EL].[GID_ID]
						sResult += "\r\n" + "SELECT ";
						sResult += "\r\n" + "\t" + "nnidt" + lCount.ToString() + ".[BASE_GID_ID] AS 'BASE%%GID_ID', ";
						sResult += "\r\n" + "\t" + "nnidt" + lCount.ToString() + ".[" + sLinkTable + "_GID_ID] AS 'GID_ID', ";
						//sResult &= vbCrLf & vbTab & "[" & sLinkTable & "].[" & sField & "] AS '" & sWork & "'"  'MI 6/9/10 Commented
						sResult += "\r\n" + "\t" + GetFieldExpression(sLinkTable, sField, sMode) + " AS '" + sWork + "'"; //MI 6/9/10 Added to fix bug where LINK%%DTx field causes an invalid column error.

						//Do not add GID - GID was added above if requested. If another field
						//was requested, the link is non-editable, therefore GID_ID is not needed.
						//SYS_Name must be returned so that the link can be sorted by it in the middle tier
						//Link values are always sorted by SYS_Name regardless of which field is requested.
						//Add SYS_Name if needed
						if (sField.ToUpper() != "SYS_NAME")
						{
							sResult += ", " + "\r\n" + "\t" + "[" + sLinkTable + "].[SYS_Name] AS '" + sLink + "%%SYS_Name' ";
						}
						sResult += "\r\n" + "FROM [" + sLinkTable + "] " + "\r\n";
						sResult += "JOIN @tIDTable" + lCount.ToString() + " nnidt" + lCount.ToString() + " ON nnidt" + lCount.ToString() + ".[" + sLinkTable + "_GID_ID] = [" + sLinkTable + "].[GID_ID] ";
						//-------- ORDER BY ----------
						if (lTop != 0)
						{
							//ORDER BY nnidt1.[CN_GID_ID], [EL].[SYS_Name]
							sResult += "\r\n" + "ORDER BY nnidt" + lCount.ToString() + ".[BASE_GID_ID], [" + sLinkTable + "].[SYS_NAME]";
						}

					}
					else
					{

						//---------------- ORIGINAL CODE THAT IGNORES LINK FILE PERMS -----------------
						//SELECT DISTINCT idt.[CN_GID_ID] AS 'BASE%%GID_ID', 
						//    EL.[GID_ID] AS 'GID_ID', 
						//    EL.[GID_ID] AS 'LNK_Connected_EL%%GID_ID', 
						//    EL.[SYS_Name] AS 'LNK_Connected_EL%%SYS_Name' 
						//FROM EL
						//*** MI 4/5/06 Removed SELECT DISTINCT - it disallowed memo (ntext) fields in the query ***
						sResult += "\r\n" + "\r\n" + "SELECT " + sTopStatement + "idt.[" + sTable + "_GID_ID] AS 'BASE%%GID_ID', ";
						sResult += "\r\n" + "\t" + "[" + sLinkTable + "].[GID_ID] AS 'GID_ID', ";
						//sResult &= vbCrLf & vbTab & "[" & sLinkTable & "].[" & sField & "] AS '" & sWork & "'"     'MI 6/9/10 Commented
						sResult += "\r\n" + "\t" + GetFieldExpression(sLinkTable, sField, sMode) + " AS '" + sWork + "'"; //MI 6/9/10 Added to fix bug where LINK%%DTx field causes an invalid column error.

						//Do not add GID - GID was added above if requested. If another field
						//was requested, the link is non-editable, therefore GID_ID is not returned.
						//	If sField <> "GID_ID" Then
						//		sResult += CR + TAB + sLinkTable + ".[GID_ID] AS '" + sLink + "%%GID_ID', "
						//	END
						//SYS_Name must be returned so that the link can be sorted by it in the middle tier
						//Link values are always sorted by SYS_Name regardless of which field is requested.
						//Return Name only once.
						if (sField.ToUpper() != "SYS_NAME")
						{
							sResult += ", " + "\r\n" + "\t" + "[" + sLinkTable + "].[SYS_Name] AS '" + sLink + "%%SYS_Name' ";
						}
						sResult += "\r\n" + "FROM [" + sLinkTable + "]";
						//---------- JOINs -----------
						//JOIN @tIDTable idt ON idt.[CN_GID_ID] = EL.[GID_Related_CN]
						sRevLinkName = goData.LKGetInverseName(sTable, sLink);
						sRevLinkPart = goTR.GetLinkPartFromLinkName(sRevLinkName);
						sRevLinkFile = goTR.GetFileFromLinkName(sRevLinkName);
						sResult += "\r\n" + "JOIN @tIDTable idt ON " + "idt.[" + sTable + "_GID_ID] = [" + sLinkTable + "].[GID_" + sRevLinkPart + "_" + sRevLinkFile + "]";
						//-------- ORDER BY ----------
						//ORDER BY idt.[CN_GID_ID], EL.[SYS_Name]
						sResult += "\r\n" + "ORDER BY idt.[" + sTable + "_GID_ID], [" + sLinkTable + "].[SYS_Name]";
						//---------------- END ORIGINAL CODE THAT IGNORES LINK FILE PERMS -----------------
					}


				}
				else
				{

					//NN type' can be assumed because 'N1' links were added as columns in main recordset

					if (bUseLinkPermissions)
					{
						//********************************* NEW CODE *******************************
						//New code that observes link file permissions

						if (goData.LKGetDirection(sTable, sLink) == 1)
						{
							//Primary direction link
							sLinkingTable = sTable + "_" + sLinkPart + "_" + sLinkTable;
							//---------- DECLARE ---------
							//DECLARE @tIDTable1 TABLE(
							//    [CN_GID_ID] uniqueidentifier, 
							//    [CN_INVOLVEDWITH_CO_GID_ID] uniqueidentifier,
							//    [CO_GID_ID] uniqueidentifier,
							//    [CO_SYS_Name] nvarchar(300))
							sResult += "\r\n" + "\r\n" + "DECLARE " + sTableVarName + " TABLE(" + "\r\n";
							sResult += "\t" + "[BASE_GID_ID] uniqueidentifier, " + "\r\n";
							sResult += "\t" + "[" + sLinkingTable + "_GID_ID] uniqueidentifier, " + "\r\n";
							sResult += "\t" + "[" + sLinkTable + "_GID_ID] uniqueidentifier, " + "\r\n";
							sResult += "\t" + "[" + sLinkTable + "_SYS_NAME] nvarchar(300)) " + "\r\n";
							//------ INSERT INTO @tIDTable<n> --------
							//INSERT INTO @tNNIDTable([CN_GID_ID], [CN_INVOLVEDWITH_CO_GID_ID], [CO_GID_ID], [CO_SYS_Name])
							sResult += "INSERT INTO " + sTableVarName + "([BASE_GID_ID], [" + sLinkingTable + "_GID_ID], [" + sLinkTable + "_GID_ID], [" + sLinkTable + "_SYS_NAME])" + "\r\n";
							//------ FIRST SELECT --------
							//SELECT DISTINCT TOP 20 idt.[CN_GID_ID], 
							//	[CN_INVOLVEDWITH_CO].[GID_ID], 
							//	[CO].[GID_ID], 
							//	[CO].[SYS_Name]
							//FROM [CO]
							//*** MI 4/5/06 Removed SELECT DISTINCT - disallowed memo (ntext) fields in the query ***
							//*** MI 5/27/09 Added SELECT DISTINCT to support multiple unique aliases on same tables, which cause
							//duplicate records to be returned. We can do DISTINCT now because memos (ntext) are never in this
							//SELECT - they are in the SELECT below, which is JOINed to the table variable which this SELECT fills
							//with IDs and SYS_Names.
							sResult += "SELECT DISTINCT " + sTopStatement + "idt.[" + sTable + "_GID_ID], " + "\r\n";
							sResult += "\t" + "[" + sLinkingTable + "].[GID_ID], " + "\r\n";
							sResult += "\t" + "[" + sLinkTable + "].[GID_ID], " + "\r\n";
							//SYS_Name is here for sorting. If sorting is not important in the table variable, SYS_Name could be removed here.
							sResult += "\t" + "[" + sLinkTable + "].[SYS_Name] " + "\r\n";
							sResult += "FROM [" + sLinkTable + "]" + "\r\n";
							//---------- STATIC JOINs -----------
							//JOIN [CN_INVOLVEDWITH_CO] ON [CO].[GID_ID] = [CN_INVOLVEDWITH_CO].[GID_CO]
							//JOIN @tIDTable idt ON idt.[CN_GID_ID] = [CN_INVOLVEDWITH_CO].[GID_CN]
							sWork3 = "JOIN [" + sLinkingTable + "] ON " + "[" + sLinkTable + "].[GID_ID] = [" + sLinkingTable + "].[GID_" + sLinkTable + sLinkTableNo + "]";
							sResult += sWork3 + "\r\n";
							sResult += "JOIN @tIDTable idt ON " + "idt.[" + sTable + "_GID_ID] = [" + sLinkingTable + "].[GID_" + sTable + "]";
							if (lTop != 0)
							{
								//---------- JOIN and WHERE -----------
								//Generate JOIN and WHERE to observe R perm in linked file
								//LEFT JOIN [CO_RELATED_RL] [CO_RELATED_RL00001] ON [CO].[GID_ID] = [CO_RELATED_RL00001].[GID_CO]
								//LEFT JOIN [RL] [RL00001] ON [RL00001].[GID_ID] = [CO_RELATED_RL00001].[GID_RL]
								//LEFT JOIN [CO_RELATED_RL] [CO_RELATED_RL00002] ON [CO].[GID_ID] = [CO_RELATED_RL00002].[GID_CO]
								//LEFT JOIN [RL] [RL00002] ON [RL00002].[GID_ID] = [CO_RELATED_RL00002].[GID_RL]
								//LEFT JOIN [US] [US00003] ON [US00003].[GID_ID] = [CO].[GID_CreatedBy_US]
								//WHERE 
								//   ([RL00001].[GID_ID] = 'd2a1e8d3-ef07-438d-524c-98dd0141ff71' OR [RL00001].[GID_ID] = '09e2c26e-27dd-4fb8-524c-98dd0141ff71') 
								//   AND 
								//   ([CO].[SI__ShareState] = 2 OR (([CO].[SI__ShareState] < 2 OR [CO].[SI__ShareState] IS NULL) AND [US00003].[GID_ID] = '95e9184e-bcd9-4d00-5553-98dd014446fb'))
								goTR.StrWrite(ref sWork4, "EXCLUDEJOINS", sWork3);
								sWork2 = goData.GenerateSQL("", sLinkTable, "", "", "", -1, false, "", "JOINANDWHEREONLYFORNNLINKS", "", true, "R", false, false, false, sWork4);
								sResult += "\r\n" + sWork2;
								//-------- ORDER BY ---------
								//ORDER BY idt.[CN_GID_ID], [CO].[SYS_Name]
								sResult += "\r\n" + "ORDER BY idt.[" + sTable + "_GID_ID], [" + sLinkTable + "].[SYS_Name]";
							}

							//--------- SECOND SELECT --------
							//SELECT 
							//	nnidt1.[CN_GID_ID] AS 'BASE%%GID_ID',
							//	nnidt1.[CN_INVOLVEDWITH_CO_GID_ID] AS 'GID_ID', 
							//	[CO].[MMO_Note] AS 'LNK_INVOLVEDWITH_CO%%MMO_Note', 
							//	[CO].[SYS_Name] AS 'LNK_INVOLVEDWITH_CO%%SYS_Name'
							//FROM [CO] 
							//JOIN @tIDTable1 nnidt1 ON nnidt1.[CO_GID_ID] = [CO].[GID_ID]
							sResult += "\r\n" + "SELECT ";
							sResult += "\r\n" + "\t" + "nnidt" + lCount.ToString() + ".[BASE_GID_ID] AS 'BASE%%GID_ID', ";
							sResult += "\r\n" + "\t" + "nnidt" + lCount.ToString() + ".[" + sLinkingTable + "_GID_ID] AS 'GID_ID', ";
							//sResult &= vbCrLf & vbTab & "[" & sLinkTable & "].[" & sField & "] AS '" & sWork & "'"     'MI 6/9/10 Commented
							sResult += "\r\n" + "\t" + GetFieldExpression(sLinkTable, sField, sMode) + " AS '" + sWork + "'"; //MI 6/9/10 Added to fix bug where LINK%%DTx field causes an invalid column error.

							//Do not add GID - GID was added above if requested. If another field
							//was requested, the link is non-editable, therefore GID_ID is not needed.
							//SYS_Name must be returned so that the link can be sorted by it in the middle tier
							//Link values are always sorted by SYS_Name regardless of which field is requested.
							//Add SYS_Name if needed
							if (sField.ToUpper() != "SYS_NAME")
							{
								sResult += ", " + "\r\n" + "\t" + "[" + sLinkTable + "].[SYS_Name] AS '" + sLink + "%%SYS_Name' ";
							}
							sResult += "\r\n" + "FROM [" + sLinkTable + "] " + "\r\n";
							sResult += "JOIN @tIDTable" + lCount.ToString() + " nnidt" + lCount.ToString() + " ON nnidt" + lCount.ToString() + ".[" + sLinkTable + "_GID_ID] = [" + sLinkTable + "].[GID_ID] ";
							if (lTop != 0)
							{
								//-------- ORDER BY ----------
								//ORDER BY nnidt1.[CN_GID_ID], [CO].[SYS_Name]
								sResult += "\r\n" + "ORDER BY nnidt" + lCount.ToString() + ".[BASE_GID_ID], [" + sLinkTable + "].[SYS_NAME]";
							}

						}
						else
						{

							//Reverse direction link: CO.LNK_Involves_CN
							sRevLinkName = goData.LKGetInverseName(sTable, sLink);
							sRevLinkPart = goTR.GetLinkPartFromLinkName(sRevLinkName);
							sRevLinkFile = goTR.GetFileFromLinkName(sLink);
							sLinkingTable = sRevLinkFile + "_" + sRevLinkPart + "_" + sTable;

							//---------- DECLARE ---------
							//DECLARE @tIDTable1 TABLE(
							//    [CN_GID_ID] uniqueidentifier, 
							//    [CN_INVOLVEDWITH_CO_GID_ID] uniqueidentifier,
							//    [CO_GID_ID] uniqueidentifier,
							//    [CO_SYS_Name] nvarchar(300))
							sResult += "\r\n" + "\r\n" + "DECLARE " + sTableVarName + " TABLE(" + "\r\n";
							sResult += "\t" + "[BASE_GID_ID] uniqueidentifier, " + "\r\n";
							sResult += "\t" + "[" + sLinkingTable + "_GID_ID] uniqueidentifier, " + "\r\n";
							sResult += "\t" + "[" + sLinkTable + "_GID_ID] uniqueidentifier, " + "\r\n";
							sResult += "\t" + "[" + sLinkTable + "_SYS_NAME] nvarchar(300)) " + "\r\n";
							//------ INSERT INTO @tIDTable<n> --------
							//INSERT INTO @tNNIDTable([CN_GID_ID], [CN_INVOLVEDWITH_CO_GID_ID], [CO_GID_ID], [CO_SYS_Name])
							sResult += "INSERT INTO " + sTableVarName + "([BASE_GID_ID], [" + sLinkingTable + "_GID_ID], [" + sLinkTable + "_GID_ID], [" + sLinkTable + "_SYS_NAME])" + "\r\n";
							//------ FIRST SELECT --------
							//SELECT DISTINCT TOP 20 idt.[CN_GID_ID], 
							//	[CN_INVOLVEDWITH_CO].[GID_ID], 
							//	[CO].[GID_ID], 
							//	[CO].[SYS_Name]
							//FROM [CO]
							//*** MI 4/5/06 Removed SELECT DISTINCT - disallowed memo (ntext) fields in the query ***
							//*** MI 5/27/09 Added SELECT DISTINCT to support multiple unique aliases on same tables, which cause
							//duplicate records to be returned. We can do DISTINCT now because memos (ntext) are never in this
							//SELECT - they are in the SELECT below, which is JOINed to the table variable which this SELECT fills
							//with IDs and SYS_Names.
							sResult += "SELECT DISTINCT " + sTopStatement + "idt.[" + sTable + "_GID_ID], " + "\r\n";
							sResult += "\t" + "[" + sLinkingTable + "].[GID_ID], " + "\r\n";
							sResult += "\t" + "[" + sLinkTable + "].[GID_ID], " + "\r\n";
							//SYS_Name is here for sorting. If sorting is not important in the table variable, SYS_Name could be removed here.
							sResult += "\t" + "[" + sLinkTable + "].[SYS_Name] " + "\r\n";
							sResult += "FROM [" + sLinkTable + "]" + "\r\n";
							//---------- STATIC JOINs -----------
							//JOIN CN_InvolvedWith_CO ON CO.[GID_ID] = CN_InvolvedWith_CO.[GID_CO]
							//JOIN @tIDTable idt ON idt.[CN_GID_ID] = CN_InvolvedWith_CO.[GID_CN]
							sWork3 = "JOIN [" + sLinkingTable + "] ON " + "[" + sLinkTable + "].[GID_ID] = [" + sLinkingTable + "].[GID_" + sLinkTable + "]";
							sResult += sWork3 + "\r\n";
							sResult += "JOIN @tIDTable idt ON " + "idt.[" + sTable + "_GID_ID] = [" + sLinkingTable + "].[GID_" + sTable + sLinkTableNo + "]";
							if (lTop != 0)
							{
								//---------- JOIN and WHERE -----------
								//Generate JOIN and WHERE to observe R perm in linked file
								goTR.StrWrite(ref sWork4, "EXCLUDEJOINS", sWork3);
								sWork2 = goData.GenerateSQL("", sLinkTable, "", "", "", -1, false, "", "JOINANDWHEREONLYFORNNLINKS", "", true, "R", false, false, false, sWork4);
								sResult += "\r\n" + sWork2;
								//-------- ORDER BY ---------
								//ORDER BY idt.[CN_GID_ID], [CO].[SYS_Name]
								sResult += "\r\n" + "ORDER BY idt.[" + sTable + "_GID_ID], [" + sLinkTable + "].[SYS_Name]";
							}

							//--------- SECOND SELECT --------
							//SELECT 
							//	nnidt1.[CN_GID_ID] AS 'BASE%%GID_ID',
							//	nnidt1.[CN_INVOLVEDWITH_CO_GID_ID] AS 'GID_ID', 
							//	[CO].[MMO_Note] AS 'LNK_INVOLVEDWITH_CO%%MMO_Note', 
							//	[CO].[SYS_Name] AS 'LNK_INVOLVEDWITH_CO%%SYS_Name'
							//FROM [CO] 
							//JOIN @tIDTable1 nnidt1 ON nnidt1.[CO_GID_ID] = [CO].[GID_ID]
							sResult += "\r\n" + "SELECT ";
							sResult += "\r\n" + "\t" + "nnidt" + lCount.ToString() + ".[BASE_GID_ID] AS 'BASE%%GID_ID', ";
							sResult += "\r\n" + "\t" + "nnidt" + lCount.ToString() + ".[" + sLinkingTable + "_GID_ID] AS 'GID_ID', ";
							//sResult &= vbCrLf & vbTab & "[" & sLinkTable & "].[" & sField & "] AS '" & sWork & "'"     'MI 6/9/10 Commented
							sResult += "\r\n" + "\t" + GetFieldExpression(sLinkTable, sField, sMode) + " AS '" + sWork + "'"; //MI 6/9/10 Added to fix bug where LINK%%DTx field causes an invalid column error.

							//Do not add GID - GID was added above if requested. If another field
							//was requested, the link is non-editable, therefore GID_ID is not needed.
							//SYS_Name must be returned so that the link can be sorted by it in the middle tier
							//Link values are always sorted by SYS_Name regardless of which field is requested.
							//Add SYS_Name if needed
							if (sField.ToUpper() != "SYS_NAME")
							{
								sResult += ", " + "\r\n" + "\t" + "[" + sLinkTable + "].[SYS_Name] AS '" + sLink + "%%SYS_Name' ";
							}
							sResult += "\r\n" + "FROM [" + sLinkTable + "] " + "\r\n";
							sResult += "JOIN @tIDTable" + lCount.ToString() + " nnidt" + lCount.ToString() + " ON nnidt" + lCount.ToString() + ".[" + sLinkTable + "_GID_ID] = [" + sLinkTable + "].[GID_ID] ";
							if (lTop != 0)
							{
								//-------- ORDER BY ----------
								//ORDER BY nnidt1.[CN_GID_ID], [CO].[SYS_Name]
								sResult += "\r\n" + "ORDER BY nnidt" + lCount.ToString() + ".[BASE_GID_ID], [" + sLinkTable + "].[SYS_NAME]";
							}

						}
						//**************************************************************************

					}
					else
					{
						//-------- ORIGINAL CODE IGNORES LINK FILE'S PERMISSIONS --------------

						if (goData.LKGetDirection(sTable, sLink) == 1)
						{
							//Primary direction link

							//SELECT DISTINCT idt.[CN_GID_ID] AS 'BASE%%GID_ID', 
							//	CN_InvolvedWith_CO.[GID_ID] AS 'GID_ID', 
							//	CO.[TXT_CompanyName] AS 'LNK_InvolvedWith_CO%%TXT_CompanyName', 
							//	CO.[SYS_Name] AS 'LNK_InvolvedWith_CO%%SYS_Name' 
							//FROM CO

							//Primary direction link
							//*** MI 4/5/06 Removed SELECT DISTINCT - disallowed memo (ntext) fields in the query ***
							sResult += "\r\n" + "\r\n" + "SELECT " + sTopStatement + "idt.[" + sTable + "_GID_ID] AS 'BASE%%GID_ID', ";
							//	'This line can be removed - it's for debug so that we can see the name of the record
							//	sResult += CR + TAB + "idt.[" + sTable + "_SYS_Name] AS 'SYS_Name', "
							sResult += "\r\n" + "\t" + "[" + sTable + "_" + sLinkPart + "_" + sLinkTable + "].[GID_ID] AS 'GID_ID', ";
							//sResult &= vbCrLf & vbTab & "[" & sLinkTable & "].[" & sField & "] AS '" & sWork & "'"      'MI 6/9/10 Commented
							sResult += "\r\n" + "\t" + GetFieldExpression(sLinkTable, sField, sMode) + " AS '" + sWork + "'"; //MI 6/9/10 Added to fix bug where LINK%%DTx field causes an invalid column error.

							//Do not add GID - GID was added above if requested. If another field
							//was requested, the link is non-editable, therefore GID_ID is not returned.
							//	If sField <> "GID_ID" Then
							//		sResult += CR + TAB + sLinkTable + ".[GID_ID] AS '" + sLink + "%%GID_ID', "
							//	END
							//SYS_Name must be returned so that the link can be sorted by it in the middle tier
							//Link values are always sorted by SYS_Name regardless of which field is requested.
							//Return Name only once.
							if (sField.ToUpper() != "SYS_NAME")
							{
								sResult += ", " + "\r\n" + "\t" + "[" + sLinkTable + "].[SYS_Name] AS '" + sLink + "%%SYS_Name' ";
							}
							sResult += "\r\n" + "FROM [" + sLinkTable + "]";
							//---------- JOINs -----------
							//JOIN CN_InvolvedWith_CO ON CO.[GID_ID] = CN_InvolvedWith_CO.[GID_CO]
							//JOIN @tIDTable idt ON idt.[CN_GID_ID] = CN_InvolvedWith_CO.[GID_CN]
							sResult += "\r\n" + "JOIN [" + sTable + "_" + sLinkPart + "_" + sLinkTable + "] ON " + "[" + sLinkTable + "].[GID_ID] = [" + sTable + "_" + sLinkPart + "_" + sLinkTable + "].[GID_" + sLinkTable + sLinkTableNo + "]";
							sResult += "\r\n" + "JOIN @tIDTable idt ON " + "idt.[" + sTable + "_GID_ID] = [" + sTable + "_" + sLinkPart + "_" + sLinkTable + "].[GID_" + sTable + "]";

						}
						else
						{

							//Reverse direction link: CO.LNK_Involves_CN
							sRevLinkName = goData.LKGetInverseName(sTable, sLink);
							sRevLinkPart = goTR.GetLinkPartFromLinkName(sRevLinkName);
							sRevLinkFile = goTR.GetFileFromLinkName(sLink);
							sLinkingTable = sRevLinkFile + "_" + sRevLinkPart + "_" + sTable;

							//SELECT DISTINCT idt.[CN_GID_ID] AS 'BASE%%GID_ID', 
							//	CN_InvolvedWith_CO.[GID_ID] AS 'GID_ID', 
							//	CN.[TXT_NameLast] AS 'LNK_Involves_CN%%TXT_NameLast', 
							//	CN.[SYS_Name] AS 'LNK_Involves_CN%%SYS_Name' 
							//FROM CO

							//*** MI 4/5/06 Removed SELECT DISTINCT - disallowed memo (ntext) fields in the query ***
							sResult += "\r\n" + "\r\n" + "SELECT " + sTopStatement + "idt.[" + sTable + "_GID_ID] AS 'BASE%%GID_ID', ";
							//	'This line can be removed - it's for debug so that we can see the name of the record
							//	sResult += CR + TAB + "idt.[" + sTable + "_SYS_Name] AS 'SYS_Name', "
							sResult += "\r\n" + "\t" + "[" + sLinkingTable + "].[GID_ID] AS 'GID_ID', ";
							//sResult &= vbCrLf & vbTab & "[" & sLinkTable & "].[" & sField & "] AS '" & sWork & "'"      'MI 6/9/10 Commented
							sResult += "\r\n" + "\t" + GetFieldExpression(sLinkTable, sField, sMode) + " AS '" + sWork + "'"; //MI 6/9/10 Added to fix bug where LINK%%DTx field causes an invalid column error.
							//Do not add GID - GID was added above if requested. If another field
							//was requested, the link is non-editable, therefore GID_ID is not returned.
							//	If sField <> "GID_ID" Then
							//		sResult += CR + TAB + sLinkTable + ".[GID_ID] AS '" + sLink + "%%GID_ID', "
							//	END
							//SYS_Name must be returned so that the link can be sorted by it in the middle tier
							//Link values are always sorted by SYS_Name regardless of which field is requested.
							//Return Name only once.
							if (sField.ToUpper() != "SYS_NAME")
							{
								sResult += ", " + "\r\n" + "\t" + "[" + sLinkTable + "].[SYS_Name] AS '" + sLink + "%%SYS_Name' ";
							}
							sResult += "\r\n" + "FROM [" + sLinkTable + "]";
							//---------- JOINs -----------
							//JOIN CN_InvolvedWith_CO ON CO.[GID_ID] = CN_InvolvedWith_CO.[GID_CO]
							//JOIN @tIDTable idt ON idt.[CN_GID_ID] = CN_InvolvedWith_CO.[GID_CN]
							sWork3 = "JOIN [" + sLinkingTable + "] ON " + "[" + sLinkTable + "].[GID_ID] = [" + sLinkingTable + "].[GID_" + sLinkTable + "]";
							sResult += "\r\n" + sWork3;
							sResult += "\r\n" + "JOIN @tIDTable idt ON " + "idt.[" + sTable + "_GID_ID] = [" + sLinkingTable + "].[GID_" + sTable + sLinkTableNo + "]";
							//-------- ORDER BY ----------
							//ORDER BY nnidt1.[CN_GID_ID], [CO].[SYS_Name]
							sResult += "\r\n" + "ORDER BY idt.[" + sTable + "_GID_ID], [" + sLinkTable + "].[SYS_NAME]";

						}

						//---------------- END ORIGINAL CODE THAT IGNORES LINK FILE PERMS -----------------
					}

				}

				lCount = lCount + 1;

			} while (true);

	ReturnTheResult:

			//DEBUG - remove!
			switch (sMode)
			{
				case "SIMPLESELECT":
				case "JOINANDWHEREONLY":
				case "JOINANDWHEREONLYFORNNLINKS":
				break;
				default:
					sResult = sResult;
					break;
			}
			//END DEBUG

			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If

			//End Try


			return sResult;

		}

		private string GenerateTopRecConditions(string sTopRecord, string sSort, bool par_bReverseSort)
		{
			//MI 2/26/10 Added dealing with DTx fields. Since a DTx value is derived and doesn't exist in the database,
			//datetime ranges have to be tested explicitly. For ex for a DTY (year) field, we have to test 
			//>=2010-01-01 and <=2010-12-31|23:59:59.999 instead of simply DTT=value.
			//MI 4/9/09
			//PURPOSE:
			//       Generate TopRec quickselect condition(s) so they can be appended
			//       to the filter condition in GenerateSQL. GID_ID is always appended as the
			//       last field to ensure uniqueness if there are duplicates on all other
			//       sort fields.
			//EXAMPLE of sort on 3 fields (where GID_ID is 21365): CHK_ActiveField, TXT_NameLast, TXT_NameFirst:
			//       "((CN.[CHK_ActiveField] = 1) and (CN.TXT_NameLast = 'Igrec') and (CN.TXT_NameFirst = 'Mario') and (CN.GID_ID >= 21365))" & _
			//       " or " & _
			//       "((CN.[CHK_ActiveField] = 1) and (CN.TXT_NameLast = 'Igrec') and (CN.TXT_NameFirst > 'Mario'))" & _
			//       " or " & _
			//       "((CN.[CHK_ActiveField] = 1) and (CN.TXT_NameLast > 'Igrec'))" & _
			//       " or " & _
			//       "((CN.[CHK_ActiveField] > 1))"
			//       Sort on DTY_StartTime DESC illustrates the need to test the whole year 2010 explicitly:
			//       (([AC].[DTT_STARTTIME] <= '2010-12-31 23:59:59.996' AND [AC].[DTT_STARTTIME] >= '2010-01-01 00:00:00.000' AND [AC].[BI__ID] >= 236138) 
			//       OR 
			//       (([AC].[DTT_STARTTIME] <= '2009-12-31 23:59:59.996' OR [AC].[DTT_STARTTIME] IS NULL)))


			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sWork = "";
			string sWork2 = null;
			long lCount = 0;
			long lPos = 0;
			string sSortField = null;
			string sSortFieldDirection = null;
			string sSortFieldVal = null;
			int i = 0;
			string sWork3 = null;
			DateTime dtDate = default(DateTime);
			string sTemp = null;

			//Try
			if (sTopRecord != "")
			{
				sWork = "";
				sWork2 = "";
				lCount = 0;
				Microsoft.VisualBasic.Collection colSort;
				colSort = goData.GetFilterSortFields("SORT=" + sSort, true, par_bReverseSort);

				//Process sort fields right to left
				//lPos is maintaining which field we are processing
				lPos = colSort.Count;
				for (i = colSort.Count; i >= 1; i--)
				{
					//Retrieve the field name
					sSortField = goTR.ExtractString(colSort[i].ToString(), 1, "|");
					//Retrieve sort direction
					sSortFieldDirection = goTR.ExtractString(colSort[i].ToString(), 2, "|");
					//For once we are not going to be paranoid about what ExtractString
					//returns and will NOT test for clc.EOT or "". Gees.
					//Add conditions for fields from left to right up to the processed field
					sWork += "(";
					for (lCount = 1; lCount <= lPos; lCount++)
					{
						//sWork &= "("
						sSortField = goTR.ExtractString(colSort[lCount].ToString(), 1, "|");
						sSortFieldDirection = goTR.ExtractString(colSort[lCount].ToString(), 2, "|");
						//Remove %%GID_ID from link name to test TOPREC_ property both with and without %%GID_ID
						if (goTR.GetPrefix(sSortField) == "LNK_")
						{
							sWork2 = goTR.GetFieldPartFromLinkName(sSortField).ToUpper();
							if (sWork2 == "GID_ID" || sWork2 == "")
							{
								sWork2 = goTR.ExtractString(sSortField, 1, "%%");
							}
						}
						else
						{
							sWork2 = "";
						}
						sSortFieldVal = goTR.StrRead(sTopRecord, "TOPREC_" + sSortField, clC.EOT);
						if (sSortFieldVal[0] == clC.EOT)
						{
							sSortFieldVal = goTR.StrRead(sTopRecord, "TOPREC_" + sWork2, clC.EOT);
							if (sSortFieldVal[0] == clC.EOT)
							{
								goErr.SetError(35019, sProc, "", sSortField);
								//35019:SQL query cannot be generated because sTopRecord is defined (not blank), but it doesn't contain a TOPREC_FieldName=Value definition for sort field '[1]'.
							}
						}
						sSortFieldVal = goTR.RemoveSingleQuotes(sSortFieldVal);
						//Single quotes can be used for int, bigint, but not for money type
						if (goTR.GetPrefix(sSortField) == "LNK_")
						{
							sWork2 = goTR.GetFieldPartFromLinkName(sSortField);
							if (sWork2 == "")
							{
								sWork2 = "GID_ID";
							}
						}
						else
						{
							sWork2 = sSortField;
						}
						//sWork3 = ""    'MI 2/25/10 commented because this is redundant considering the Select Case below
						//Set the delimiter - single quote or blank, depending on the field type
						switch (goTR.GetFieldHiLevelType(sWork2))
						{
							case "NUMBER": //"BI_", "CUR", "DR_", "INT", "LI_", "SI_", "SR_" 'Numeric or currency
								sWork3 = "";
								break;
							case "CHECKBOX": //"CHK"
								sWork3 = "";
								break;
							case "LIST": //"MLS", "CMB", "LST", "SEL"
								sWork3 = "";
								break;
							default:
								sWork3 = "'";
								break;
						}
						//Write the condition statement
						if (lCount == colSort.Count)
						{
							//On the last field, use >= or <= operator
							if (lCount == lPos)
							{
								//Last field, use >= or <= operator
								//MI 2/26/10 The last field will be BI__ID almost always. If it is not and the last field is
								//DTx, TOPREC will be ambiguous and a wrong record may be evaluated as the top record.
								//That's why we don't worry about evaluating DTx datetime ranges here as we do below.
								if (sSortFieldDirection == "DESC")
								{
									//<=
									sWork += sSortField + "<=" + sWork3 + sSortFieldVal + sWork3;
								}
								else
								{
									//>=
									sWork += sSortField + ">=" + sWork3 + sSortFieldVal + sWork3;
								}
							}
							else
							{
								//Not last field, typically use = operator 
								//MI 2/26/10 added dealing with DTx fields, which require that the period (year, quarter, month, day)
								//be filtered explicitly. This is because DTT fields do not hold the DTx value that is being filtered on.
								switch (goTR.GetPrefix(sSortField))
								{
									case "DTY_":
										if (sSortFieldDirection == "DESC")
										{
											//DTT_STARTTIME<='2010-12-31|23:59:59.996' AND DTT_STARTTIME>='2010-01-01'
											sWork += sSortField + "<=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											sWork += sSortField + ">=" + sWork3 + sSortFieldVal.Substring(0, 4) + "-01-01" + sWork3;
										}
										else
										{
											//DTT_STARTTIME>='2010-01-01' AND DTT_STARTTIME<='2010-12-31|23:59:59.996'
											sWork += sSortField + ">=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											sWork += sSortField + "<=" + sWork3 + sSortFieldVal.Substring(0, 4) + "-12-31|23:59:59.996" + sWork3;
										}
										break;
									case "DTQ_":
										if (sSortFieldDirection == "DESC")
										{
											//DTT_STARTTIME<='2010-06-30|23:59:59.996' AND DTT_STARTTIME>='2010-04-01|00:00:00.000'
											sWork += sSortField + "<=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											dtDate = goTR.StringToDateTime(sSortFieldVal, "SYSTEM", "SYSTEM");
											dtDate = goTR.GetQuarterDate(dtDate);
											sWork += sSortField + ">=" + sWork3 + goTR.DateTimeToSysString(dtDate) + sWork3;
										}
										else
										{
											//DTT_STARTTIME>='2010-04-01' AND DTT_STARTTIME<='2010-06-30|23:59:59.996'
											sWork += sSortField + ">=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											dtDate = goTR.StringToDateTime(sSortFieldVal, "SYSTEM", "SYSTEM");
											dtDate = goTR.GetQuarterEndDateTime(dtDate);
											sWork += sSortField + "<=" + sWork3 + goTR.DateTimeToSysString(dtDate) + sWork3;
										}
										break;
									case "DTM_":
										if (sSortFieldDirection == "DESC")
										{
											//DTT_STARTTIME<='2010-04-30|23:59:59.996' AND DTT_STARTTIME>='2010-04-01'
											sWork += sSortField + "<=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											sWork += sSortField + ">=" + sWork3 + sSortFieldVal.Substring(0, 7) + "-01" + sWork3;
										}
										else
										{
											//DTT_STARTTIME>='2010-04-01' AND DTT_STARTTIME<='2010-04-30|23:59:59.996'
											sWork += sSortField + ">=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											//Get end of the month
											sTemp = sSortFieldVal.Substring(0, 7) + "-01";
											dtDate = goTR.StringToDateTime(sTemp, "SYSTEM", "SYSTEM");
											dtDate = dtDate.AddMonths(1);
											dtDate = dtDate.AddMilliseconds(-4);
											sWork += sSortField + "<=" + sWork3 + goTR.DateTimeToSysString(dtDate) + sWork3;
										}
										break;
									case "DTD_":
										if (sSortFieldDirection == "DESC")
										{
											//DTT_STARTTIME<='2010-06-13|23:59:59.996' AND DTT_STARTTIME>='2010-06-13'
											sWork += sSortField + "<=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											sWork += sSortField + ">=" + sWork3 + sSortFieldVal.Substring(0, 10) + sWork3;
										}
										else
										{
											//DTT_STARTTIME>='2010-06-13' AND DTT_STARTTIME<='2010-06-13|23:59:59.996'
											sWork += sSortField + ">=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											//Get end time of the day
											sTemp = sSortFieldVal.Substring(0, 10) + "|23:59:59.996";
											sWork += sSortField + "<=" + sWork3 + sTemp + sWork3;
										}
										break;
									default:
										sWork += sSortField + "=" + sWork3 + sSortFieldVal + sWork3;
										break;
								}
							}
						}
						else
						{
							//Not on the last field, use > or < operator. For DTx fields, define the datetime range
							//explicitly.
							if (lCount == lPos)
							{
								//Inner loop "last" field
								if (sSortFieldDirection == "DESC")
								{
									//<
									switch (goTR.GetPrefix(sSortField))
									{
										case "DTY_":
											//Calc year ago
											dtDate = goTR.StringToDateTime(sSortFieldVal, "SYSTEM", "SYSTEM");
											dtDate = goTR.AddYear(dtDate, -1);
											sWork += sSortField + "<=" + sWork3 + goTR.DateTimeToSysString(dtDate) + sWork3;
											break;
										case "DTQ_":
											dtDate = goTR.StringToDateTime(sSortFieldVal, "SYSTEM", "SYSTEM");
											dtDate = goTR.AddMonth(ref dtDate, -3);
											sWork += sSortField + "<=" + sWork3 + goTR.DateTimeToSysString(dtDate) + sWork3;
											break;
										case "DTM_":
											dtDate = goTR.StringToDateTime(sSortFieldVal, "SYSTEM", "SYSTEM");
											dtDate = goTR.AddMonth(ref dtDate, -1);
											sWork += sSortField + "<=" + sWork3 + goTR.DateTimeToSysString(dtDate) + sWork3;
											break;
										case "DTD_":
											dtDate = goTR.StringToDateTime(sSortFieldVal, "SYSTEM", "SYSTEM");
											dtDate = goTR.AddDay(dtDate, -1);
											sWork += sSortField + "<=" + sWork3 + goTR.DateTimeToSysString(dtDate) + sWork3;
											break;
										default:
											sWork += sSortField + "<" + sWork3 + sSortFieldVal + sWork3;
											break;
									}
								}
								else //ASC
								{
									//>
									switch (goTR.GetPrefix(sSortField))
									{
										case "DTY_":
											//Calc year ahead
											dtDate = goTR.StringToDateTime(sSortFieldVal, "SYSTEM", "SYSTEM");
											dtDate = goTR.AddYear(dtDate, 1);
											sWork += sSortField + ">=" + sWork3 + goTR.DateTimeToSysString(dtDate) + sWork3;
											break;
										case "DTQ_":
											dtDate = goTR.StringToDateTime(sSortFieldVal, "SYSTEM", "SYSTEM");
											dtDate = goTR.AddMonth(ref dtDate, 3);
											sWork += sSortField + ">=" + sWork3 + goTR.DateTimeToSysString(dtDate) + sWork3;
											break;
										case "DTM_":
											dtDate = goTR.StringToDateTime(sSortFieldVal, "SYSTEM", "SYSTEM");
											dtDate = goTR.AddMonth(ref dtDate, 1);
											sWork += sSortField + ">=" + sWork3 + goTR.DateTimeToSysString(dtDate) + sWork3;
											break;
										case "DTD_":
											dtDate = goTR.StringToDateTime(sSortFieldVal, "SYSTEM", "SYSTEM");
											dtDate = goTR.AddDay(dtDate, 1);
											sWork += sSortField + ">=" + sWork3 + goTR.DateTimeToSysString(dtDate) + sWork3;
											break;
										default:
											sWork += sSortField + ">" + sWork3 + sSortFieldVal + sWork3;
											break;
									}
								}
							}
							else
							{
								//Not last field, typically use = operator 'MI 2/26/10 added dealing with DTx fields
								//MI 2/26/10 added dealing with DTx fields, which require that the period (year, quarter, month, day)
								//be filtered explicitly. This is because DTT fields do not hold the DTx value that is being filtered on.
								switch (goTR.GetPrefix(sSortField))
								{
									case "DTY_":
										if (sSortFieldDirection == "DESC")
										{
											//DTT_STARTTIME<='2010-12-31|23:59:59.996' AND DTT_STARTTIME>='2010-01-01'
											sWork += sSortField + "<=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											sWork += sSortField + ">=" + sWork3 + sSortFieldVal.Substring(0, 4) + "-01-01" + sWork3;
										}
										else
										{
											//DTT_STARTTIME>='2010-01-01' AND DTT_STARTTIME<='2010-12-31|23:59:59.996'
											sWork += sSortField + ">=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											sWork += sSortField + "<=" + sWork3 + sSortFieldVal.Substring(0, 4) + "-12-31|23:59:59.996" + sWork3;
										}
										break;
									case "DTQ_":
										if (sSortFieldDirection == "DESC")
										{
											//DTT_STARTTIME<='2010-06-30|23:59:59.996' AND DTT_STARTTIME>='2010-04-01|00:00:00.000'
											sWork += sSortField + "<=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											dtDate = goTR.StringToDateTime(sSortFieldVal, "SYSTEM", "SYSTEM");
											dtDate = goTR.GetQuarterDate(dtDate);
											sWork += sSortField + ">=" + sWork3 + goTR.DateTimeToSysString(dtDate) + sWork3;
										}
										else
										{
											//DTT_STARTTIME>='2010-04-01' AND DTT_STARTTIME<='2010-06-30|23:59:59.996'
											sWork += sSortField + ">=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											dtDate = goTR.StringToDateTime(sSortFieldVal, "SYSTEM", "SYSTEM");
											dtDate = goTR.GetQuarterEndDateTime(dtDate);
											sWork += sSortField + "<=" + sWork3 + goTR.DateTimeToSysString(dtDate) + sWork3;
										}
										break;
									case "DTM_":
										if (sSortFieldDirection == "DESC")
										{
											//DTT_STARTTIME<='2010-04-30|23:59:59.996' AND DTT_STARTTIME>='2010-04-01'
											sWork += sSortField + "<=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											sWork += sSortField + ">=" + sWork3 + sSortFieldVal.Substring(0, 7) + "-01" + sWork3;
										}
										else
										{
											//DTT_STARTTIME>='2010-04-01' AND DTT_STARTTIME<='2010-04-30|23:59:59.996'
											sWork += sSortField + ">=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											//Get end of the month
											sTemp = sSortFieldVal.Substring(0, 7) + "-01";
											dtDate = goTR.StringToDateTime(sTemp, "SYSTEM", "SYSTEM");
											dtDate = dtDate.AddMonths(1);
											dtDate = dtDate.AddMilliseconds(-4);
											sWork += sSortField + "<=" + sWork3 + goTR.DateTimeToSysString(dtDate) + sWork3;
										}
										break;
									case "DTD_":
										if (sSortFieldDirection == "DESC")
										{
											//DTT_STARTTIME<='2010-06-13|23:59:59.996' AND DTT_STARTTIME>='2010-06-13'
											sWork += sSortField + "<=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											sWork += sSortField + ">=" + sWork3 + sSortFieldVal.Substring(0, 10) + sWork3;
										}
										else
										{
											//DTT_STARTTIME>='2010-06-13' AND DTT_STARTTIME<='2010-06-13|23:59:59.996'
											sWork += sSortField + ">=" + sWork3 + sSortFieldVal + sWork3 + " AND ";
											//Get end time of the day
											sTemp = sSortFieldVal.Substring(0, 10) + "|23:59:59.996";
											sWork += sSortField + "<=" + sWork3 + sTemp + sWork3;
										}
										break;
									default:
										sWork += sSortField + "=" + sWork3 + sSortFieldVal + sWork3;
										break;
								}
							}
						}
						//sWork &= ")"
						if (lCount < lPos)
						{
							sWork += " AND ";
						}
					}
					sWork += ")";
					//sSortField
					//sSortFieldVal
					lPos = lPos - 1;
					if (lPos > 0)
					{
						sWork += " OR ";
					}
				}

			}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sWork;

		}

		public string RemoveRedundantConditions(string par_sCondition)
		{
			//PURPOSE:
			//       Exclude the condition if its value is a 'fake' SUID. Fake SUIDs are used when it is known 
			//       that the condition should not return any records, for example in the case of <%SelectedViewRecordID%>
			//       when the desktop knows that a view is not selected. 
			//       clDesktop.GetViewCondition(), for example, puts the fake SUID as a value in all
			//       <%SelectedViewRecordID FILE=xx%> conditions except the one that points to a currently selected view.
			//       Excluding fake conditions is a performance optimization technique - not creating the joins and WHERE
			//       conditions that are unneeded greatly improves query performance and reduces the needed resources.
			//PARAMETERS:
			//       par_sCondition: sCondition value from GenerateSQL
			//RETURNS:
			//       CONDITION= line without the redundant conditions. Parentheses and OR or AND words are
			//       removed as needed.
			//EXAMPLE:
			//       This is a filter that will make the desktop substitute <%SelectedViewRecordID%> with a fake SUID
			//       in 3 out of the 4 conditions.
			//           FILE=CN
			//           CONDITION=LNK_RELATED_CO=<%SelectedViewRecordID FILE=CO%> OR 
			//               LNK_ATTENDS_AP=<%SelectedViewRecordID FILE=AP%> OR 
			//               LNK_CONNECTED_AC=<%SelectedViewRecordID FILE=AC%> OR 
			//               LNK_INVOLVEDIN_OP=<%SelectedViewRecordID FILE=OP%>
			//       As evaluated by clDesktop and sent to this method as par_sCondition by GenerateSQL:
			//           CONDITION=LNK_RELATED_CO=ab86ee2f-ab5f-4cac-434f-990e00844668 OR 
			//               LNK_ATTENDS_AP=11111111-1111-1111-4150-111111111111 OR 
			//               LNK_CONNECTED_AC=11111111-1111-1111-4143-111111111111 OR 
			//               LNK_INVOLVEDIN_OP=11111111-1111-1111-4F50-111111111111
			//       We return:
			//           LNK_RELATED_CO=ab86ee2f-ab5f-4cac-434f-990e00844668
			//KEY TO REGEX STATEMENTS:
			//       [\s]: space
			//       *: any number of times
			//       ?: non-greedy search, search ends on first match
			//       (OR|or|AND|and): upper or lowercase 'OR' or 'AND'
			//       LNK_[a-zA-Z0-9]*?: 'LNK_' followed by any number of characters or digits. This excludes spaces.
			//       [=]: single equal character
			//       [1]{8}: '1' 8 times
			//       [\-]: dash character '-'
			//       [0-9a-fA-F]{4}: four digits or characters a-f that represent hexadecimal values, for ex: '434F'

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sResult = par_sCondition;
			Regex rgx = null;
			string sPattern;

			//<redundnat> means "LNK_*_*=11111111-1111-1111-????-111111111111"

			//Try
			sPattern = "LNK_[a-zA-Z0-9]*?_[a-zA-Z0-9]{2}?[=][1]{8}[\\-][1]{4}[\\-][1]{4}[\\-][0-9a-fA-F]{4}[\\-][1]{12}";
			rgx = new Regex(sPattern);
			sResult = rgx.Replace(sResult, "BI__ID<0");

			// OLD CODE
			//'This code removes multiple spaces around OR or AND keywords and between empty parens
			//'---------- Remove ' OR|AND <redundant>' ---------
			//sPattern = "[\s]*?(OR|or|AND|and)[\s]*?LNK_[a-zA-Z0-9]*?_[a-zA-Z0-9]{2}?[=][1]{8}[\-][1]{4}[\-][1]{4}[\-][0-9a-fA-F]{4}[\-][1]{12}"
			//rgx = New Regex(sPattern)
			//sResult = rgx.Replace(sResult, "")

			//'---------- Remove '<redundant> OR|AND ' ---------
			//sPattern = "LNK_[a-zA-Z0-9]*?_[a-zA-Z0-9]{2}?[=][1]{8}[\-][1]{4}[\-][1]{4}[\-][0-9a-fA-F]{4}[\-][1]{12}[\s]*?(OR|or|AND|and)[\s]*?"
			//rgx = New Regex(sPattern)
			//sResult = rgx.Replace(sResult, "")

			//'---------- Remove '<redundant>' ---------
			//sPattern = "LNK_[a-zA-Z0-9]*?_[a-zA-Z0-9]{2}?[=][1]{8}[\-][1]{4}[\-][1]{4}[\-][0-9a-fA-F]{4}[\-][1]{12}"
			//rgx = New Regex(sPattern)
			//sResult = rgx.Replace(sResult, "")

			//'---------- Remove ' OR|AND ( )' ---------
			//sPattern = "[\s]*?(OR|or|AND|and)[\s]*?[\(][\s]*?[\)]"
			//rgx = New Regex(sPattern)
			//sResult = rgx.Replace(sResult, "")

			//'---------- Remove '( ) OR|AND ' ---------
			//sPattern = "[\(][\s]*?[\)][\s]*?(OR|or|AND|and)[\s]*?"
			//rgx = New Regex(sPattern)
			//sResult = rgx.Replace(sResult, "")

			//'---------- Remove '( )' ---------
			//sPattern = "[\(][\s]*?[\)]"
			//rgx = New Regex(sPattern)
			//sResult = rgx.Replace(sResult, "")
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sResult;
		}

		private string SingleQuotes(string par_sValue, bool par_bAdd = true)
		{

			//AUTHOR: MI 5/9/06
			//PURPOSE:
			//       Add or remove single quotes around a string. This is called from ProcessConditionValue.
			//       If the string starts with a single quote but doesn't have a closing single quote,
			//       the starting one is not removed. Trailing spaces are NOT removed. Single quotes are
			//       stripped only if they are the first and last characters of the string.
			//PARAMETERS:
			//       par_sValue: Condition value.
			//       par_bAdd: When True, add single quotes, else strip them.
			//RETURNS:
			//       Value wrapped in single quotes or with outer single quotes removed, if there.

			string sValue = par_sValue;

			if (par_bAdd)
			{
				//Add
				if (goTR.FromTo(sValue, 1, 1) == "'")
				{
					if (sValue.Substring(sValue.Length - 1) == "'")
					{
						//Single quotes are already there
					}
					else
					{
						sValue = "'" + sValue + "'";
					}
				}
				else
				{
					sValue = "'" + sValue + "'";
				}
			}
			else
			{
				//Remove
				if (goTR.FromTo(sValue, 1, 1) == "'")
				{
					if (sValue.Substring(sValue.Length - 1) == "'")
					{
						sValue = goTR.FromTo(sValue, 2, sValue.Length - 1);
					}
					else
					{
						//Closing ' not found - leave as is
					}
				}
				else
				{
					//Starting ' not found - leave as is
				}
			}

			return sValue;

		}

		private string ProcessConditionValue(string par_sValue, string par_sFieldPrefix)
		{
			//==> NOT FINISHED
			//AUTHOR: MI 5/9/06
			//PURPOSE:
			//       For GenerateSQL conditions, evaluate keyword expressions such as 'Today',
			//       '3 days from tomorrow', or <%MEID%> and enclose the value with ' '
			//       or strip ' ' depending on the field type and whether the value is a
			//       field code or not.
			//PARAMETERS:
			//       par_sValue: Condition value. Examples:
			//           1
			//           2007-04-03
			//           Today
			//           <%MEID%>
			//           <%LNK_CreditedTo_US%%SYS_Name%>
			//       par_sFieldPrefix: Prefix of a field or a complete field name. Examples:
			//           CHK_
			//           DTT
			//           MLS_Type

			string sValue = par_sValue;
			string sResult = sValue;
			int iValid = clC.SELL_TYPE_INVALID;

			switch ((par_sFieldPrefix.Substring(0, 3) + "_").ToUpper())
			{
				case "DTE_":
					if (goTR.IsDate(sValue))
					{
						sResult = SingleQuotes(sValue);
					}
					else
					{
						sValue = goTR.StringToDate(sValue, "", ref iValid).ToString();
						if (iValid == clC.SELL_TYPE_INVALID)
						{
							sResult = "";
							//==> Continue here
							//goErr.SetError(
						}
						sResult = SingleQuotes(sValue);
					}
					break;
				case "LNK_": //Link
				break;
				case "CHK_":
				case "CMB_":
				case "CUR_":
				case "DR__":
				case "INT_":
				case "LI__":
				case "BI__":
				case "LST_":
				case "MLS_":
				case "SEL_":
				case "SI__":
				case "SR__": //Numeric
				break;
				case "TME_":
				case "TML_": //Time (Long int)
				break;
				default: //"BIN_", "EML_", "FIL_", "MMO_", "MMR_", "SYS_", "TXT_", "TEL_", "URL_" 'System-generated, such as 'SYS_Name' and text, telephone
				break;
			}

			return sResult;

		}

		private string ProcessGeneratedFields(string par_sFields, string par_sTable, string par_sGenFieldDefs, string par_sMode = "SELECT")
		{
			//MI 8/24/10 Added consulting the session variable "InvalidFields_" & par_sTable to
			//exclude fields and links that the rowset determined previously are not valid in the SQL schema.
			//SQL-invalid (deleted, renamed) fields are kept as valid in the Selltis schema throughout
			//the session not to disrupt objects that count on them.
			//MI 12/14/09 Added par_sMode parameter, enabling method for GROUPBY calculated field names.
			//PURPOSE:
			//       Process generated fields and remove fields that are in the Selltis schema,
			//       but are not in SQL (because they were deleted during the user session).
			//       We modify sFields if necessary by removing static text, <% and %>,
			//       and field code formatting <%xxxx FORMAT=yyyy%>.
			//       sFields are a "plain" comma-delimited list of fields after this.
			//       GROUPBY calculated field namess get |SUM, |AVG, |MIN, |MAX, or |MED appended, e.g.:
			//           FIELDS=CUR_Value|SUM, CUR_Value|AVG, INT_Qty|MIN, INT_Qty|MAX, ...
			//       We are not analyzing or modifying LNK fields or the number of hops here.
			//       We are not testing the validity of fields or links - that's done later.
			//EXAMPLE:
			//       TXT_Fld1, "str4, <%TXT_Fld4%>, str5", str <%TXT_Fld2%> str2 <%TXT_Fld3 LENGTH=20 ELLIPSIS=1%> str3, "a,b,c"

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sFields = par_sFields;
			bool bQuotesAreOpen = false;
			string sWork = null;
			string sWork2 = null;
			string sWork3 = null;
			string sWork4 = null;
			string sWork5 = null;
			long lPos = 0;
			long lWork = 0;
			long lCount = 0;
			long lCount2 = 0;
			long lCount3 = 0;
			string sGenFieldCalc = "";

			//Try
			par_sMode = par_sMode.ToUpper();
			bQuotesAreOpen = false;
			sWork = ""; //Contains the string between commas (,)
			sWork3 = ""; //Contains all fields after processing (later becomes sFields)
			lPos = 0; //Position of
			lWork = 0; //Position of
			lCount = 1; //Loop counter
			//Remove outer double-quotes (") and all commas (,) and spaces around them 
			//from generated field definitions. This will corrupt the static text, but 
			//is not a problem because we are discarding all static text. Individual
			//fields are extracted from generated field definitions in a second loop below.
			//MI 12/15/09 In GROUPBY mode all fields support <FieldName>_CALC= definitions
			//that list SUM, AVG, MIN, MAX, or MED. For each such definition, the field
			//is appended with "|<calc>", e.g. "CUR_Value|SUM".
			do
			{
				sWork = goTR.ExtractString(sFields, (int)lCount, ",").Trim(' ');
				if (sWork == "")
				{
					goto EndOfLoop;
				}
				if (sWork[0] == clC.EOT)
				{
					break;
				}
				if (bQuotesAreOpen)
				{
					//We are inside double-quotes
					if (sWork.Substring(sWork.Length - 1) == ((char)34).ToString())
					{
						//Closing quote found
						sWork = goTR.FromTo(sWork, 1, sWork.Length - 1);
						sWork3 += sWork + ", ";
						bQuotesAreOpen = false;
					}
					else
					{
						//Closing quote not found
						sWork3 += sWork;
					}
				}
				else
				{
					//Not inside double-quotes
					if (sWork.Substring(0, 1) == ((char)34).ToString())
					{
						//String starts with double-quote character "
						//Remove the starting double-quote
						sWork = goTR.FromTo(sWork, 2, -1);
						if (sWork.Substring(sWork.Length - 1) != ((char)34).ToString())
						{
							//Closing double-quote not found
							sWork3 += sWork;
							bQuotesAreOpen = true;
						}
						else
						{
							//Closing double-quote found - remove it
							sWork = goTR.FromTo(sWork, 1, sWork.Length - 1);
							sWork3 += sWork + ", ";
						}
					}
					else
					{
						//String doesn't start with double-quote "
						sWork3 += sWork + ", ";
					}
				}
	EndOfLoop:
				lCount = lCount + 1;
			} while (true);
			//Remove the trailing ", " we may have added above
			if (sWork3.Substring(sWork3.Length - 2) == ", ")
			{
				sWork3 = sWork3.Substring(0, sWork3.Length - 2);
			}
			sFields = sWork3;

			//Get rid of static text - leave individual fields. 
			//Substitute DTT_ for DTE_ and TME_ fields. We don't care whether this will cause duplicate
			//DTT fields because duplicates are removed later.
			//MI 8/24/10 Remove field if found in goP.GetVar("InvalidFields_" & par_sTable),
			//This session var is populate by the rowset when a field is not in the database. 
			//All fields in this variable are omitted from the query generated by GenerateSQL
			//to avoid SQL errors.
			//Replace GEN_ fields with fields they reference
			//EXAMPLE OF GEN VALUE AT THIS POINT:
			//   TXT_Fld1, str4 <%TXT_Fld4%> str5, str <%TXT_Fld2%> str2 <%TXT_Fld3 LENGTH=20 ELLIPSIS=1%> str3, abc
			//EXAMPLE AFTER THIS LOOP:
			//   TXT_Fld1, TXT_Fld4, TXT_Fld2, TXT_Fld3, abc
			sWork = ""; //Contains the string between commas (,)
			sWork2 = ""; //Individual field definitions and static text within a generate field
			sWork3 = ""; //Contains all fields after processing (becomes sFields)
			sWork4 = ""; //Contains GEN_ field definition (from par_GenFieldDefs)
			sWork5 = ""; //Contains one field code extracted from one line in par_GenFieldDefs
			lPos = 0; //Position of
			lWork = 0; //Position of
			lCount = 1; //Loop counter

			//Loop on comma (,)
			do
			{
				sWork = goTR.ExtractString(sFields, (int)lCount, ",").Trim(' ');
				//Skip blanks
				if (sWork == "")
				{
					goto EndOfOuterLoop;
				}
				if (sWork[0] == clC.EOT)
				{
					break;
				}
				sWork2 = "";
				if (sWork.IndexOf("<%") + 1 < 1)
				{
					//No field codes, assume this is a field definition
					if (par_sMode == "GROUPBY" || par_sMode == "GROUPBYWITHROLLUP")
					{
						//Read field calc definition
						sGenFieldCalc = goTR.StrRead(par_sGenFieldDefs, sWork + "_CALC", "", false);
						//If sGenFieldCalc = "" Then
						//    goErr.SetError(35000, sProc, "A calculation definition is missing for a generated field '" & sWork & "'. Add a line like '" & sWork & "_CALC=SUM, AVG, MIN, MAX, MED' to have those calculations (aggregates) returned for " & sWork & ".")
						//    'MessTranslate
						//End If
					}
					//Replace GEN_ field with the fields it references
					if (goTR.GetPrefix(sWork) == "GEN_")
					{
						sWork4 = goTR.StrRead(par_sGenFieldDefs, sWork);
						if (sWork4 == "")
						{
							goErr.SetError(35004, sProc, "", sWork);
							//Return "SELECT '" & sProc & " Error: Definition is missing for a generated field: [" & sWork & "]. Add a line like " & sWork & "=Static text <%FieldCode1%> more text <%FieldCode2%>... to your filter statement.'" & " AS 'ERROR'"
						}
						else
						{
							//Replace sWork with a list of fields
							lCount3 = 1;
							sWork = "";
							do
							{
								//Extract each field code from the GEN_ field definition
								sWork5 = goTR.GetLineElement(sWork4, clC.SELL_EXTRACT_FIELD, (int)lCount3);
								sWork5 = goTR.RemoveDelim(sWork5);
								sWork5 = goTR.ExtractString(sWork5, 1, " ");
								if (sWork5 == "" || sWork5[0] == clC.EOT)
								{
									break;
								}
								//Insert into list of fields only a valid field
								if (goData.IsFieldValid(par_sTable, sWork5))
								{
									//Substitute DTT for DTE, TME, and TML
									if (sWork5.ToUpper().Substring(0, 4) == "DTE_" || sWork5.ToUpper().Substring(0, 4) == "TME_" || sWork5.ToUpper().Substring(0, 4) == "TML_")
									{
										sWork5 = "DTT_" + goTR.FromTo(sWork5, 5, -1);
									}
									//Replace DTE, TME, or TML prefix in link fields with DTT
									sWork5 = goTR.Replace(sWork5, "%%DTE_", "%%DTT_");
									sWork5 = goTR.Replace(sWork5, "%%TME_", "%%DTT_");
									sWork5 = goTR.Replace(sWork5, "%%TML_", "%%DTT_");
									//----------------------

									//wgt 102814
									//Substitute MMR for MMP
									if (sWork5.ToUpper().Substring(0, 4) == "MMP_")
									{
										sWork5 = "MMR_" + goTR.FromTo(sWork5, 5, -1);
									}
									//Replace MMP prefix in link fields with MMR
									sWork5 = goTR.Replace(sWork5, "%%MMP_", "%%MMR_");
									//----------------------

									//V_T 5/14/2015
									//Substitute ADR for ADV
									if (sWork5.ToUpper().Substring(0, 4) == "ADV_")
									{
										sWork5 = "ADR_" + goTR.FromTo(sWork5, 5, -1);
									}
									//Replace MMP prefix in link fields with MMR
									sWork5 = goTR.Replace(sWork5, "%%ADV_", "%%ADR_");

									//MI 8/24/10 Is the field marked as invalid? If so, skip it.
									if (!IsFieldInvalid(sWork5, par_sTable))
									{
										if (par_sMode == "GROUPBY" || par_sMode == "GROUPBYWITHROLLUP")
										{
											//Add a field for each of its defined calculations
											sWork3 = AddGroupByField(sWork3, sWork5, sGenFieldCalc); //MI 1/5/10 Replaced sWork with sWork3
										}
										else
										{
											//Add the field to sWork (prepend comma if needed)
											if (sWork3 != "") //MI 1/5/10 Replaced sWork with sWork3
											{
												sWork3 += ", " + sWork5; //MI 1/5/10 Replaced sWork with sWork3
											}
											else
											{
												sWork3 = sWork5; //MI 1/5/10 Replaced sWork with sWork3
											}
										}
									}
									//-----------------------
								}
								lCount3 = lCount3 + 1;
							} while (true);
						}
					}
					else
					{
						//Not a GEN_ field
						//Substitute DTT for DTE, TME, and TML
						if (sWork.ToUpper().Substring(0, 4) == "DTE_" || sWork.ToUpper().Substring(0, 4) == "TME_" || sWork.ToUpper().Substring(0, 4) == "TML_")
						{
							sWork = "DTT_" + goTR.FromTo(sWork, 5, -1);
						}
						//Replace DTE, TME, or TML prefix in link fields with DTT
						sWork = goTR.Replace(sWork, "%%DTE_", "%%DTT_");
						sWork = goTR.Replace(sWork, "%%TME_", "%%DTT_");
						sWork = goTR.Replace(sWork, "%%TML_", "%%DTT_");
						//----------------------

						//wgt 102814
						//Substitute MMR for MMP
						if (sWork.ToUpper().Substring(0, 4) == "MMP_")
						{
							sWork = "MMR_" + goTR.FromTo(sWork, 5, -1);
						}
						//Replace MMP prefix in link fields with MMR
						sWork = goTR.Replace(sWork, "%%MMP_", "%%MMR_");
                        //----------------------

                        //V_T 5/14/2015
                        //Substitute ADR for ADV
                        if (!string.IsNullOrEmpty(sWork5) && sWork5.ToUpper().StartsWith("ADV_"))
                        {
							sWork5 = "ADR_" + goTR.FromTo(sWork5, 5, -1);
						}
						//Replace MMP prefix in link fields with MMR
						sWork5 = goTR.Replace(sWork5, "%%ADV_", "%%ADR_");

						//MI 8/24/10 Is the field marked as invalid? If so, skip it.
						if (!IsFieldInvalid(sWork, par_sTable))
						{
							//MI 1/5/10 Added supporting CALC definitions for non-GEN fields
							if (par_sMode == "GROUPBY" || par_sMode == "GROUPBYWITHROLLUP")
							{
								//Add a field for each of its defined calculations
								sWork3 = AddGroupByField(sWork3, sWork, sGenFieldCalc);
							}
							else
							{
								//Add the field to sWork (prepend comma if needed)
								if (sWork3 != "")
								{
									sWork3 += ", " + sWork;
								}
								else
								{
									sWork3 = sWork;
								}
							}
						}
						//-------------------------
					}
				}
				else
				{
					//One or more field codes (<%...%>) were found, parse fields from sWork.
					//This is a case that isn't used any longer because multiple field codes are now
					//always passed as a GEN_ definition. We still support this, however.
					//WARNING: Seems that GROUPBY, GROUPBYWITHROLLUP considerations were not implemented
					//in this case.
					//GEN_ fields are not supported mixed with static text and field codes.
					//We let "GEN_" pass as static text here. If a field code includes "GEN_",
					//an invalid field error will be returned further down.
					//Loop on field code delimiter (<%)
					lCount2 = 2; //we are interested in the string after <%
					do
					{
						sWork2 = (goTR.ExtractString(sWork, (int)lCount2, "<%")).Trim(' ');
						//Skip blanks
						if (sWork2 == "")
						{
							goto EndOfInnerLoop;
						}
						if (sWork2[0] == clC.EOT)
						{
							break;
						}
						//Extract the field part including formatting codes. Ex: "TXT_Fld3 LENGTH=20 ELLIPSIS=1"
						lPos = sWork2.IndexOf("%>") + 1;
						if (lPos > 0)
						{
							sWork2 = goTR.FromTo(sWork2, 1, lPos - 1);
							//Remove any formatting codes. Ex: "TXT_Fld3"
							lPos = sWork2.IndexOf(" ") + 1;
							if (lPos > 0)
							{
								sWork2 = goTR.FromTo(sWork2, 1, lPos - 1);
							}
							if (sWork2.IndexOf("=") + 1 > 0)
							{
								//Malformed field code has formatting ahead of the field definition. Ex: LENGTH=20
								sWork2 = "";
							}
						}
						else
						{
							//Field code is not properly terminated - raise error
							goErr.SetError(35005, sProc, "", sWork2);
							//Return "SELECT '" & sProc & " Error: Field code in FIELDS definition is not properly terminated (with %>): [" & sWork2 & "].' AS 'ERROR'"
						}
						if (sWork2 != "")
						{
							//Substitute DTT for DTE, TME, and TML
							//MI 8/10/07 Replaced sWork with sWork2 in the following block. sWork can't be right because it can contain multiple field codes here.
							//==> Test this case!
							if (sWork2.ToUpper().Substring(0, 4) == "DTE_" || sWork2.ToUpper().Substring(0, 4) == "TME_" || sWork2.ToUpper().Substring(0, 4) == "TML_")
							{
								sWork2 = "DTT_" + goTR.FromTo(sWork2, 5, -1);
							}
							//Replace DTE, TME, or TML prefix in link fields with DTT
							sWork2 = goTR.Replace(sWork2, "%%DTE_", "%%DTT_");
							sWork2 = goTR.Replace(sWork2, "%%TME_", "%%DTT_");
							sWork2 = goTR.Replace(sWork2, "%%TML_", "%%DTT_");
							//----------------------

							//wgt 102814
							//Substitute MMR for MMP
							if (sWork2.ToUpper().Substring(0, 4) == "MMP_")
							{
								sWork2 = "MMR_" + goTR.FromTo(sWork2, 5, -1);
							}
							//Replace MMP prefix in link fields with MMR
							sWork2 = goTR.Replace(sWork2, "%%MMP_", "%%MMR_");
							//----------------------

							//V_T 5/14/2015
							//Substitute ADR for ADV
							if (sWork5.ToUpper().Substring(0, 4) == "ADV_")
							{
								sWork5 = "ADR_" + goTR.FromTo(sWork5, 5, -1);
							}
							//Replace MMP prefix in link fields with MMR
							sWork5 = goTR.Replace(sWork5, "%%ADV_", "%%ADR_");

							//MI 8/24/10 Is the field marked as invalid? If so, skip it.
							if (!IsFieldInvalid(sWork2, par_sTable))
							{
								sWork3 += sWork2 + ", ";
							}
							//-----------------------
						}
	EndOfInnerLoop:
						lCount2 = lCount2 + 1;
					} while (true);
				}
	EndOfOuterLoop:
				lCount = lCount + 1;
			} while (true);
			//Remove the trailing ", " we may have added above
			if (sWork3.Substring(sWork3.Length - 2) == ", ")
			{
				sWork3 = sWork3.Substring(0, sWork3.Length - 2);
			}

			//Clean list of fields without codes, formatting, etc
			//Example: TXT_Fld1, TXT_Fld4, TXT_Fld2, TXT_Fld3, abc
			sFields = sWork3;

			if (sFields == "")
			{
				sFields = "GID_ID";
			}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sFields;

		}


		private string ProcessStarsInFields(string par_sFields, string par_sTable)
		{
			//MI 11/3/09 Changed how empty par_sFields is interpreted.
			//MI 11/3/09 Commented Dim sFields As String = par_sFields
			//PURPOSE:
			//       Standardize multiple stars, no fields defined, etc in the FIELDS= part of the filter.
			//       This doesn't run in the following modes:
			//       "JOINANDWHEREONLY", "JOINANDWHEREONLYFORNNLINKS", "COUNT", "GROUPBY", "GROUPBYWITHROLLUP"
			//EXAMPLE:
			//       Test filter that illustrates FIELDS line that is supported/tolerated
			//       (multiple * or **, > 2 stars, extra commas, no spaces/multiple spaces around commas)
			//           TABLENAME = CO
			//           TOP = 20
			//           FIELDS=TXT_CompanyName, ***, , ,TXT_AddrBilling, *, LNK_Related_RL%%txt_RelationshipName, LNK_Related_RL,**,LNK_Involves_CN, **,,    ,
			//           SORT = TXT_CompanyName

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sWork = null;
			//MI 11/3/09 Commented because it's redundant
			//Dim sFields As String = par_sFields
			string sStars = "";
			int lWork = 0;
			int lWork2 = 0;

			//Try
			//MI 11/3/09 Changing how empty par_sFields is interpreted. Until now it meant 'all fields',
			//from now it means ID and Name only.
			//If par_sFields = "" Then sStars = "*" 'Return all direct fields
			if (par_sFields == "")
			{
				par_sFields = "GID_ID, SYS_NAME";
			}

			//Clean up **
			lWork = par_sFields.IndexOf("**") + 1;
			if (lWork > 0)
			{
				sStars = "**"; //Return all direct fields and links
				while (lWork > 0)
				{
					lWork2 = par_sFields.IndexOf(",", (Convert.ToInt32(lWork) + 1) - 1) + 1;
					//Remove the comma after **
					if (lWork2 > 0)
					{
						par_sFields = goTR.FromTo(par_sFields, 1, lWork2 - 1) + goTR.FromTo(par_sFields, lWork2 + 1, -1);
					}
					//Remove ** itself
					par_sFields = goTR.FromTo(par_sFields, 1, lWork - 1) + goTR.FromTo(par_sFields, lWork + 2, -1);
					lWork = par_sFields.IndexOf("**", (Convert.ToInt32(lWork)) - 1) + 1;
				}
			}

			//Clean up *
			lWork = par_sFields.IndexOf("*") + 1;
			if (lWork > 0)
			{
				if (sStars != "**") //Return direct fields
				{
					sStars = "*";
				}
				//Remove all stars (*) and following commas (,) from the string
				while (lWork > 0)
				{
					lWork2 = par_sFields.IndexOf(",", (Convert.ToInt32(lWork) + 1) - 1) + 1;
					//Remove the comma after the *
					if (lWork2 > 0)
					{
						par_sFields = goTR.FromTo(par_sFields, 1, lWork2 - 1) + goTR.FromTo(par_sFields, lWork2 + 1, -1);
					}
					//Remove the * itself
					par_sFields = goTR.FromTo(par_sFields, 1, lWork - 1) + goTR.FromTo(par_sFields, lWork + 1, -1);
					lWork = par_sFields.IndexOf("*", (Convert.ToInt32(lWork)) - 1) + 1;
				}
			}

            //Add all direct fields (*) to par_sFields
            //if (sStars.Substring(0, 1) == "*")
            if (sStars != null && sStars.Length > 0 && sStars.Substring(0, 1) == "*")


            {
                //Adding fields individually to par_sFields because * returns
                //every column of all joined tables
                //par_sFields = ""       'Must not be reset!
                //All links to the list of fields
                clArray doFields = new clArray();
				doFields = goData.GetFields(par_sTable);
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of doFields.GetDimension() for every iteration:
				long tempVar = doFields.GetDimension();
				for (lWork = 1; lWork <= tempVar; lWork++)
				{
					sWork = doFields.GetInfo(lWork);
					if (sWork != "")
					{
						//'DEBUG
						//If Left(UCase(sWork), Len("CUR_PRICEUNIT")) = "CUR_PRICEUNIT" Then Stop
						//'END DEBUG

						//'Check whether the field is already in the list
						//'A bad test below using InStr indiscriminately was erroneously skipping
						//'fields like QL.CUR_PriceUnit because
						//'it would find that string in QL.CUR_PriceUnitAfterDisc. May be fixed below.
						//'Not tested.
						//If Right(UCase(par_sFields), Len(sWork)) = UCase(sWork) Or _
						//    InStr(UCase(par_sFields), UCase(sWork) & ",") > 0 Or _
						//    InStr(UCase(par_sFields), UCase(sWork) & " ") > 0 Then
						//    'Don't add the field
						//Else
						//    If par_sFields <> "" Then par_sFields &= ", "
						//    par_sFields &= sWork
						//End If
						if (sWork.Substring(0, 4) == "MMP_")
						{
							goto SkipField;
						}
						if (sWork.Substring(0, 4) == "ADV_")
						{
							goto SkipField;
						}
						if (par_sFields != "")
						{
							par_sFields += ", ";
						}
						par_sFields += sWork;
	SkipField: ;

					}
				}
				doFields = null;
			}

			//Add all links (**) to par_sFields
			if (sStars == "**")
			{
				//par_sFields were filled with direct fields above
				//All links to the list of fields
				clArray doLinks = new clArray();
				doLinks = goData.GetLinks(par_sTable);
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of doLinks.GetDimension() for every iteration:
				long tempVar2 = doLinks.GetDimension();
				for (lWork = 1; lWork <= tempVar2; lWork++)
				{
					sWork = doLinks.GetInfo(lWork);
					if (sWork != "")
					{
						//'Check whether the link is already in the list of fields
						//'We are looking only for the link in the 1st hop, i.e.
						//'not following '%%'
						//If InStr(UCase(par_sFields), UCase(sWork)) = 1 Or _
						//    InStr(UCase(par_sFields), "," & UCase(sWork)) > 0 Or _
						//    InStr(UCase(par_sFields), " " & UCase(sWork)) > 0 Then
						//    'Don't add the link
						//Else
						//    'Link not found - add it
						//    par_sFields &= ", " & sWork
						//End If
						par_sFields += ", " + sWork;
					}
				}
				doLinks = null;
			}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return par_sFields;

		}


		public void InitializeClSQL()
		{
			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			goDef = (Selltis.BusinessLogic.clDefaults)HttpContext.Current.Session["goDef"];
			goPerm = (Selltis.BusinessLogic.clPerm)HttpContext.Current.Session["goPerm"];
		}

		~clSQL()
		{
// INSTANT C# NOTE: The base class Finalize method is automatically called from the destructor:
			//base.Finalize();
		}

		public clSQL()
		{
			dtColumnMap = new DataTable("COLUMNMAP");
			tColumnMap = new clTable(ref dtColumnMap, "Value", "Text", true);

		}
	}


}
