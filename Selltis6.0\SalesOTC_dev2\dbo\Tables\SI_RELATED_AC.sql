﻿CREATE TABLE [dbo].[SI_RELATED_AC] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_SI_RELATED_AC_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_SI] UNIQUEIDENTIFIER NOT NULL,
    [GID_AC] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_SI_RELATED_AC] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_AC_LINKED_SI] FOREIGN KEY ([GID_SI]) REFERENCES [dbo].[SI] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_SI_RELATED_AC] FOREIGN KEY ([GID_AC]) REFERENCES [dbo].[AC] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[SI_RELATED_AC] NOCHECK CONSTRAINT [LNK_AC_LINKED_SI];


GO
ALTER TABLE [dbo].[SI_RELATED_AC] NOCHECK CONSTRAINT [LNK_SI_RELATED_AC];


GO
CREATE NONCLUSTERED INDEX [IX_SI_RELATED_AC]
    ON [dbo].[SI_RELATED_AC]([GID_SI] ASC, [GID_AC] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_AC_LINKED_SI]
    ON [dbo].[SI_RELATED_AC]([GID_AC] ASC, [GID_SI] ASC);

