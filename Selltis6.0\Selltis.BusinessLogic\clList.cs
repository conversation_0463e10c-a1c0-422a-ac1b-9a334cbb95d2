﻿using System;
using System.Data;


//OWNER MI as of 11/19/07. Was RH.

using System.Web;


namespace Selltis.BusinessLogic
{
	public class clList
	{
		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clLog goLog;


		public clList()
		{
			Initialize();
		}
		public void Initialize()
		{

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];

		}


		public string LReadSeek(string par_sListName, string par_sValueType, string par_sValue, string par_sLang = "")
		{
				string tempLReadSeek = null;
			//MI 6/29/08 Added support for LST_XX:YYYY definitions, which apply to all files (ex: LST_XX:CURRENCY).
			//MI 10/28/06 Edited comments.

			//PURPOSE:
			//		Send back KEY or VALUE, if found. If par_sValue
			//       is numeric (par_sValueType="KEY"), then VALUE will be returned, and vice versa.
			//PARAMETERS:
			//		par_sListName:	the name of the list (format: FILENAME:FIELDNAMEwithoutPREFIX)
			//		par_sValueType:	Type of data sent via par_sValue. Supported values:
			//						"KEY": return the label for the index provided in par_sValue. If
			//                       KEY is invalid, a blank string is returned.
			//						"VALUE": return the index ("key") of the item whose label is provided
			//                       in par_sValue. If the label is not found, the index of the default item is returned
			//                       (according to DEFAULT= property in LST_ MD page defining the MLS field)
			//                       or "0".
			//		par_sValue:	The value searched (if par_sValueType is "KEY") or key (index) searched
			//                       (if par_sValueType is "VALUE") as string.
			//		par_sLang:		If the search is on the VALUE (label), and if this parameter is "", 
			//						the search is done on current language. If not empty, search is done on the 
			//						specified language (US, FR...).
			//						If the search is on the Key, this parameter determines the language 
			//                       in which the label will be returned.
			//RETURNS:
			//		String: If par_sValueType is 'KEY', returns the VALUE (label) or "" with warning raised
			//           if the key is not found, otherwise returns the key (index) as string or the default key 
			//           or "0" (warning raised) if the label ("value") is not found. 
			//           In NGP, could also return "NO LST RECORD" when data was not found. Not done in SellSQL.
			//EXAMPLE:
			//       Dim sText as string
			//		sText = goData.goList.LReadSeek(sFileName & ":" & gotr.RemovePrefix(sFieldName), "KEY", par_vValue)
			//       'sText contains the label of a list item

			string sProc = "clList::LReadSeek";
			tempLReadSeek = "";

			DataRow [] aRowArray = null;
			DataRow dRow = null;
			string s = null;
			string sSelect = null;

			//  Try
			if (par_sListName == null)
			{
				par_sListName = "";
			}
				if (par_sValueType == null)
				{
					par_sValueType = "";
				}
			if (par_sValue == null)
			{
				par_sValue = "";
			}

			string temp_parSValue = par_sValue.ToString();

			par_sListName = par_sListName.ToUpper();

				goData.TestSchema();

				if (par_sValueType.ToUpper() == "KEY")
				{
				aRowArray = goData.dtLists.Select("TableName='" + goTR.ExtractString(par_sListName, 1, ":") + "' AND ListName='" + goTR.ExtractString(par_sListName, 2, ":") + "' AND IndexVal='" + temp_parSValue.ToString() + "'"); //*** 11/19/07 MI added .ToString to par_sValue.
				if (aRowArray.GetLength(0) < 1)
				{
					//MI 6/29/08 Added section below to support LST_XX:YYYY ------------------
					//Not found, look for LST_XX:YYYY list (that applies to all all files)
					aRowArray = goData.dtLists.Select("TableName='XX' AND ListName='" + goTR.ExtractString(par_sListName, 2, ":") + "' AND IndexVal='" + temp_parSValue.ToString() + "'");
					if (aRowArray.GetLength(0) < 1)
					{
						//Not found, return blank label
						goErr.SetWarning(30034, sProc, "", temp_parSValue.ToString(), par_sListName, "");
						//30034: List index '[1]' was not found in field '[2]'. Label returned is '[3]'.
						return "";
						}
						else
						{
							dRow = aRowArray[0];
							return dRow["Label"].ToString();
						}
						//MI 6/29/08 Added section above to support LST_XX:YYYY ------------------
					}
					else
					{
						dRow = aRowArray[0];
						return dRow["Label"].ToString();
					}
				}

				if (par_sValueType.ToUpper() == "VALUE")
				{
				//Prepare single quotes for the query against the datatable
				temp_parSValue = goTR.Replace(temp_parSValue, "'", "''");
				sSelect = "TableName='" + goTR.ExtractString(par_sListName, 1, ":") + "' AND ListName='" + goTR.ExtractString(par_sListName, 2, ":") + "' AND Label='" + temp_parSValue.ToString() + "'";
				aRowArray = goData.dtLists.Select(sSelect);
					if (aRowArray.GetLength(0) < 1)
					{
					//MI 6/29/08 Added section below to support LST_XX:YYYY ------------------
					//Not found, look for LST_XX:YYYY
					sSelect = "TableName='XX' AND ListName='" + goTR.ExtractString(par_sListName, 2, ":") + "' AND Label='" + temp_parSValue.ToString() + "'";
					aRowArray = goData.dtLists.Select(sSelect);
						if (aRowArray.GetLength(0) < 1)
						{
							//Not found, return default index
							s = goData.goList.GetDefaultIndex(goTR.ExtractString(par_sListName, 1, ":"), goTR.ExtractString(par_sListName, 2, ":")).ToString();
						goErr.SetWarning(30035, sProc, "", temp_parSValue.ToString(), par_sListName, s);
						//30035: List label '[1]' was not found in field '[2]'. Index returned is '[3]'.
						return s;
						}
						else
						{
							dRow = aRowArray[0];
							return dRow["IndexVal"].ToString();
						}
						//MI 6/29/08 Added section above to support LST_XX:YYYY ------------------
					}
					else
					{
						dRow = aRowArray[0];
						return dRow["IndexVal"].ToString();
					}
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return "";

		}


		public bool ReloadListTables()
		{
			string sProc = "clList::ReloadListTables";
			//Try
			goData.LoadListData();
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
			return true;
		}


		public int GetDefaultIndex(string par_sFileName, string par_sListName)
		{
			//MI 6/30/08 Added support for LST_XX:YYYY (definition for all files).
			//MI 11/19/07
			//PARAMETERS:
			//       par_sFileName: Ex: 'AC'
			//       par_sListname: MLS_ field name without the prefix, e.g. 'Purpose'.
			//RETURNS:
			//       Integer: default key (index) of an MLS field.

			string sproc = "clList::GetDefaultIndex";
			DataRow [] aRowArray = null;
			DataRow dRow = null;
			int iReturn = 0;

			// Try
			aRowArray = goData.dtListsExtendedInfo.Select("TableName='" + par_sFileName + "' AND ListName='" + par_sListName + "' AND Property='DEFAULT'");
				if (aRowArray.GetLength(0) < 1)
				{
					//List not defined for par_sFileName, look for LST_XX:yyyy defined for all files
					aRowArray = goData.dtListsExtendedInfo.Select("TableName='XX' AND ListName='" + par_sListName + "' AND Property='DEFAULT'");
					if (aRowArray.GetLength(0) < 1)
					{
						iReturn = 0;
					}
					else
					{
						dRow = aRowArray[0];
						iReturn = Convert.ToInt32(goTR.StringToNum(dRow["Value"].ToString()));
					}
				}
				else
				{
					dRow = aRowArray[0];
					iReturn = Convert.ToInt32(goTR.StringToNum(dRow["Value"].ToString()));
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sproc)
			//    End If
			//End Try

			return iReturn;

		}

		public int GetFailValidationIndex(string par_sFileName, string par_sListName)
		{
			//MI 9/11/08 Created.
			//PARAMETERS:
			//       par_sFileName: Ex: 'AC'
			//       par_sListname: MLS_ field name without the prefix, e.g. 'Purpose'.
			//RETURNS:
			//       Integer: index of an MLS field element that causes record validation to fail if
			//           the field is listed in FIELDSREQUIRED= in FLD_ metadata. The MLS element
			//           index is defined in LST_ MD under FAILVALIDATION= property.
			//           If FAILVALIDATION is not defined, 0 index is returned.

			string sproc = "clList::GetFailValidationIndex";
			DataRow[] aRowArray = null;
			DataRow dRow = null;
			int iReturn = 0;

			// Try
			aRowArray = goData.dtListsExtendedInfo.Select("TableName='" + par_sFileName + "' AND ListName='" + par_sListName + "' AND Property='FAILVALIDATION'");
				if (aRowArray.GetLength(0) < 1)
				{
					//List not defined for par_sFileName, look for LST_XX:yyyy defined for all files
					aRowArray = goData.dtListsExtendedInfo.Select("TableName='XX' AND ListName='" + par_sListName + "' AND Property='FAILVALIDATION'");
					if (aRowArray.GetLength(0) < 1)
					{
						iReturn = 0;
					}
					else
					{
						dRow = aRowArray[0];
						iReturn = Convert.ToInt32(goTR.StringToNum(dRow["Value"].ToString()));
					}
				}
				else
				{
					dRow = aRowArray[0];
					iReturn = Convert.ToInt32(goTR.StringToNum(dRow["Value"].ToString()));
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sproc)
			//    End If
			//End Try

			return iReturn;

		}

		public clArray GetList(string par_sFileName, string par_sFieldName, string par_sFlag = "LABEL")
		{
			//MI 7/1/08 Added support for LST_XX global list definitions (read if a list is not defined for a par_sFileName).
			//MI 11/8/07 Enabling FIXED order.

			//PURPOSE:
			//		Returns a clArray containing all values or labels for the given MLS list
			//PARAMETERS:
			//		par_sFileName:	                Selltis file
			//       par_sFieldName:                 Name of the MLS field
			//       par_sFlag:                      Optional flag to determine whether to return labels or values.
			//                                       LABEL or VALUE

			//RETURNS:
			//		clArray of strings
			//AUTHOR: RH

			string sProc = "clList::GetList";

			clArray cArr = new clArray();

			string sSort = "";
			string sOrder = "";
			string sSortString = "";
			int i = 0;
			string sField = "";

			DataRow[] aRowArray = null;
			DataRow dRow = null;
			string sReturn = "";

			//   Try
			par_sFieldName = par_sFieldName.ToUpper();

				par_sFieldName = goTR.Replace(par_sFieldName, "MLS_", "");

				if (par_sFlag == "LABEL")
				{
					sField = "Label";

				}
				else if (par_sFlag == "VALUE")
				{
					sField = "IndexVal";
				}

				aRowArray = goData.dtListsExtendedInfo.Select("TableName='" + par_sFileName + "' AND ListName='" + par_sFieldName + "' AND Property='SORT'");
				//MI 7/1/08 Added checking array length and reading LST_XX:yyyy if LST_<fileName> def not found
				if (aRowArray.GetLength(0) < 1)
				{
					//List not defined for par_sFileName, look for LST_XX:yyyy defined for all files
					aRowArray = goData.dtListsExtendedInfo.Select("TableName='XX' AND ListName='" + par_sFieldName + "' AND Property='SORT'");
					if (aRowArray.GetLength(0) < 1)
					{
						//List definition not found
						sSort = "NUMERIC";
						goErr.SetWarning(35000, sProc, "List SORT order not defined. 'NUMERIC' is returned. File: '" + par_sFileName + "' List: '" + par_sFieldName + "'.");
					}
					else
					{
						dRow = aRowArray[0];
						sSort = dRow["Value"].ToString();
					}
				}
				else
				{
					dRow = aRowArray[0];
					sSort = dRow["Value"].ToString();
				}


				if (sSort == "FIXED")
				{

					aRowArray = goData.dtListsExtendedInfo.Select("TableName='" + par_sFileName + "' AND ListName='" + par_sFieldName + "' AND Property='ORDER'");
					//MI 7/1/08 Added checking array length and reading LST_XX:yyyy if LST_<fileName> def not found
					if (aRowArray.GetLength(0) < 1)
					{
						//List not defined for par_sFileName, look for LST_XX:yyyy defined for all files
						aRowArray = goData.dtListsExtendedInfo.Select("TableName='XX' AND ListName='" + par_sFieldName + "' AND Property='ORDER'");
						if (aRowArray.GetLength(0) < 1)
						{
							//List definition not found
							//sOrder = "0"
							//For i = 1 To 99
							//    sOrder &= "," & i.ToString
							//Next
							//Raise error - order must be defined for FIXED sort
							goErr.SetError(35000, sProc, "List ORDER not defined for a FIXED order list. Standard order is used. File: '" + par_sFileName + "' List: '" + par_sFieldName + "'.");
						}
						else
						{
							dRow = aRowArray[0];
							sOrder = dRow["Value"].ToString();
						}
					}
					else
					{
						dRow = aRowArray[0];
						sOrder = dRow["Value"].ToString();
					}

				}

				if (sSort != "FIXED")
				{

					if (sSort == "ALPHA")
					{
						sSortString = "Label ASC";
					}
					else if (sSort == "NUMERIC")
					{
						sSortString = "IndexVal ASC";
					}

					goData.TestSchema();

					aRowArray = goData.dtLists.Select("TableName='" + par_sFileName + "' AND ListName='" + par_sFieldName + "'", sSortString);
					//MI 7/1/08 Added checking array length and reading LST_XX:yyyy if LST_<fileName> def not found
					if (aRowArray.GetLength(0) < 1)
					{
						//List not defined for par_sFileName, look for LST_XX:yyyy defined for all files
						aRowArray = goData.dtLists.Select("TableName='XX' AND ListName='" + par_sFieldName + "'", sSortString);
						if (aRowArray.GetLength(0) < 1)
						{
							//List definition not found
							goErr.SetWarning(35000, sProc, "List not defined: blank array returned. File: '" + par_sFileName + "' List: '" + par_sFieldName + "' Sort: '" + sSortString + "'.");
						}
					}

					for (i = 0; i <= aRowArray.GetUpperBound(0); i++)
					{
						dRow = aRowArray[i];
						cArr.Add(dRow[sField].ToString());
					}

				}
				else
				{

					//FIXED order
					string[] sOrd;
					//sOrd = Split(sOrder, "'")  '*** MI 11/8/07
					sOrd = sOrder.Split(','); //*** MI 11/8/07
					for (i = 0; i <= sOrd.GetUpperBound(0); i++)
					{
						aRowArray = goData.dtLists.Select("TableName='" + par_sFileName + "' AND ListName='" + par_sFieldName + "' AND IndexVal='" + sOrd[i].Trim(' ') + "'");
						//MI 7/1/08 Added checking array length and reading LST_XX:yyyy if LST_<fileName> def not found
						if (aRowArray.GetLength(0) < 1)
						{
							//List not defined for par_sFileName, look for LST_XX:yyyy defined for all files
							aRowArray = goData.dtLists.Select("TableName='XX' AND ListName='" + par_sFieldName + "' AND IndexVal='" + sOrd[i].Trim(' ') + "'");
							if (aRowArray.GetLength(0) < 1)
							{
								//List element not found
								goErr.SetWarning(35000, sProc, "List element index '" + sOrd[i].Trim(' ') + "' defined in ORDER= in position '" + i.ToString() + "' for a FIXED list is not found. File: '" + par_sFileName + "' List: '" + par_sFieldName + "'.");
							}
							else
							{
								dRow = aRowArray[0];
								cArr.Add(dRow[sField].ToString());
							}
						}
						else
						{
							dRow = aRowArray[0];
							cArr.Add(dRow[sField].ToString());
						}
					}

				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try


			return cArr;

		}



		public void zRemoveItem(int par_iLine)
		{
			//CS PORTED as placeholder.

			//Remove an Item from the dynamic array
			string sProc = "clList::RemoveItem";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//Dim iI As Integer
			//Dim iJ As Integer
			//Dim iK As Integer
			int iCurrentLine = par_iLine;

			//CS: Commented from here to end.

			//        'First verify that it's possible
			//if :daitems<>NULL then
			//	if Dimension(:daitems)/:lColInArray>=iCurrentLine then

			//                'Free the dynamic structure
			//                '		delete :daitems[iCurrentLine]

			//                'move all following Fields one position up
			//                iJ = iCurrentLine + 1
			//		while iJ<=dimension(:daitems)/:lColInArray
			//			for iK=1 to :lColInArray
			//				:daitems[iJ-1, iK]=:daitems[iJ, iK]
			//                        End
			//			iJ++
			//                        End

			//                        'Dimension the array one line less
			//		if Dimension(:daitems)/:lColInArray>1 then 
			//			Dimension(:daitems,Dimension(:daitems)/:lColInArray-1,:lColInArray)
			//                        Else
			//delete:                     daitems()
			//                            : daitems = NUll
			//                            End
			//                            End
			//                            End
		}
		public void zDestructor()
		{
			//CS OK

			string sProc = "clList::Destructor";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//Delete the dynamic array
			zDeleteAll();
		}
		public void zFillCombo(string par_sFile, string par_sField, string par_sCombo, string par_sLang = "")
		{
			//CS PORTED as placeholder.

			//PURPOSE:
			//		Fill Combo or list on a form/window with the content of a list...
			//		The combo/List is not graphical as we are storing only the text of the list
			//		We'll retrieve the value if needed by a LReadSeek
			//PARAMETERS:
			//		par_sFile:	File concerned
			//		par_sField:	Name of the field (must be a MLS)
			//		par_sCombo: Name of the Combo/List used to display (Window.Name format)
			//		par_sLang:	If empty, labels are returned in current language. Can also contain a specific language CODE ("US", "FR"...)
			//RETURNS:
			//		Nothing
			//HOW IT WORKS:
			//		Used by clForm to display all MLS fields
			//		The list is in the array sort order (ie the one choosen in the metadata record and used to load the array)
			//EXAMPLE:
			//		goList:FillCombo("COMPANY", "MSL_TYPE", "wFrmCOWA.MLS_TYPE")

			string sProc = "clList::FillCombo";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_sFile+" "+par_sField+" "+par_sCombo, SELL_LOGLEVEL_DEbug)

			//CS: Commented from here to end
			//sFileName is string=nospace(upper(par_sFile))
			//if goData:GetFileIndex(sFileName)<1 then
			//goLog:      SetWarning(10100, sProc, "", par_sFile)
			//            ' Internal error: Incorrect file name. Please contact your system administrator and give him the file name causing the problem: '[1]'
			//            Return
			//            End

			//sFieldName is string=nospace(upper(par_sField))
			//if goTr:ExtractFieldPrefix(sFieldName)<>"MLS" or goData:IsFieldValid(sFileName, sFieldName)<1 then
			//goLog:          SetWarning(10107, sProc, "", par_sField)
			//                ' Internal error: Incorrect field name. Please contact your system administrator and give him the field name causing the problem: '[1]'
			//                Return
			//                End

			//sComboName is string=par_sCombo
			//if not goUI:DoesFieldExist(extractstring(sComboName,1,"."),extractstring(sComboName,2,".")) then
			//goLog:              SetWarning(10107, sProc, "", par_sCombo)
			//                    ' Internal error: Incorrect field name. Please contact your system administrator and give him the field name causing the problem: '[1]'
			//                    Return
			//                    End
			//                    listDeleteAll(sComboName)

			//                    'Here, we build the name of the list (FILENAME:FIELDNAMEwithoutPrefix)
			//sListName is string	=sFileName+":"+right(sFieldName,length(sFieldName)-4)


			//iI is int=1
			//if :daItems=Null then 
			//goLog:                  SetWarning(10105, sProc, "", MessTraduit(5000))
			//                        '										----- Anglais/English (3) -----
			//                        '										Lists not initialized.
			//                        '										----- Francais/French (5) -----
			//                        '										Listes non initialis�es.	
			//                        ' An information is missing to perform the work: '[1]'.
			//                        '
			//                        'Please contact your system administrator.
			//                        Return
			//                        End

			//                        'Now we can work
			//while iI<=dimension(:daItems)/:lColInArray
			//	if nospace(:daItems[iI,SELL_LISTNAME])=sListName then
			//		ListAdd(sComboName, :GetValue(iI, par_sLang))

			//                                End
			//	iI++
			//                                End

			//                                If listcount(sComboName) < 1 Then
			//                                    'We found nothing with this listName, we now try to see if it's a GLOBAL LST (currency by example)
			//                                    'Here, we build the name of the list without the FileName(:FIELDNAMEwithoutPrefix)
			//                                    sListName = ":" + right(sFieldName, length(sFieldName) - 4)

			//                                    iI = 1
			//                                    'Now we can work
			//	while iI<=dimension(:daItems)/:lColInArray
			//		if nospace(:daItems[iI,SELL_LISTNAME])=sListName then
			//			ListAdd(sComboName, :GetValue(iI, par_sLang))

			//                                            End
			//		iI++
			//                                            End
			//                                            End

			//                                            ''Now search the default value for the list
			//                                            'if listCount(sComboName)>0 then
			//                                            '	sMeta is string=goMeta:PageRead("GLOBAL","LST_"+sListName)
			//                                            '	iDef is int=val(goTr:StrRead(sMeta,"DEFAULT","-1"))
			//                                            '	if iDef<>-1 then
			//                                            '		listSelectPlus(sComboName, ListSeek(sComboName,goTr:StrRead(sMeta,numtostring(iDef),"")))
			//                                            '	END
			//                                            'END
		}
		public bool zFillListofLists(string par_sControlName)
		{
			//CS PORTED as placeholder.

			//PURPOSE:
			//		Load a List/Combo with the list of lists
			//PARAMETERS:
			//		par_sControlName:	Name of the combo/list to fill 
			//							always WindowName.ControlName format
			//RETURNS:
			//		True if operation succeded, false if not
			//HOW IT WORKS:
			//		Filter metadata file and load the LST_ pages
			//EXAMPLE:
			//		goList:FillListOfLists("wList.CMB_Lists")
			//		
			string sProc = "clList::FillListofLists";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)


			string sControlName = par_sControlName.ToUpper().Trim(' ');

			//CS: Commented from here to end. Always return true.
			//        'If no DB opened, return false (nothing to read)
			//if not goData:IsDBOpen() or sControlName="" or goTr:Position(sControlName,".")<1 then result false


			//            'First delete the list, if already loaded
			//            listDeleteAll(sControlName)

			//sString is string
			//sLine is string
			//iI is int

			//goP:        ObjectInfo(sControlName, 43, vrai)

			//            'Filter to select all lists
			//goMeta:     PageFilter("GLOBAL", "LST_")
			//if goMeta:PageReadFirst("GLOBAL","LST_") then 
			//	Loop		' on all list records

			//            'Process of a record
			//		ListAdd(sControlName,goTr:StrRead(_META.MMO_MemoValue,"NAME","")+glien(goData:IDToString("_Meta")))
			//		if not goMeta:PageReadNext("GLOBAL","LST_") then break
			//                End
			//                End
			//goMeta:         PageDeactivateFilter()


			return true;
		}
		public bool zFillTableOfItems(string par_sControlName, string par_sTID)
		{
			//CS PORTED as placeholder. Always returns true

			//AUTHOR: FH   MODIFIED BY MI 7/14/03.
			//PURPOSE:
			//		Load a table with all items (elements) of a metadata list.
			//		The table must have the following structure:
			//			COL_KEY_<TableName>		'Key 0-99 integer
			//			COL_E_<TableName>		'English label string 30
			//			COL_F_<TableName>		'French label string 30 [this column is hidden]
			//		<TableName> is the name of the table control minus the TBL_ prefix. 
			//		For example, if the table is called TBL_ITEMS, the first column must be COL_KEY_ITEMS.
			//		This method is called from WCUSTDB, Fields tab when a field is MLS_.
			//PARAMETERS:
			//		par_sControlName:	Name of the table to fill in WindowName.TableName format.
			//		par_sTID:			TID of the list to process.
			//RETURNS:
			//		True if operation succeded, false if not.
			//HOW IT WORKS:
			//		Filter metadata file and load the content of ONE LST_ pages.
			//EXAMPLE:
			//		sMetaRecordID = goMeta:GetRecordID("GLOBAL","LST_"+CMB_FILES[CMB_FILES]+":"+goTr:ExtractFieldName(sFieldNameWithPrefix))
			//		goList:FillTableOfItems("WCUSTDB.TBL_ITEMS", sMetaRecordID)

			//--------- LOG -----------
			//7/14/03	MI	clList:FillTableOfItems			Added support for DEFAULT property.
			//5/7/03	MI	clList:FillTableOfItems			Added support for dynamically named columns in the table
			//												depending on the name of the table (column extensions are the same
			//												as the table name.
			//------------------------
			string sProc = "clList::FillTableOfItems";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sControlName = par_sControlName.ToUpper().Trim(' ');
			string sTID = par_sTID.Trim(' ');
			//Dim iDefVal As Integer
			//Dim iRow As Integer

			//CS: Commented from here to end.
			//        'If no DB opened, return false (nothing to read)
			//if not goData:IsDBOpen() or sControlName="" or goTr:Position(sControlName,".")<1 or sTID="" then result false

			//if not goData:ReadByID(sTID) then result false

			//                'First delete the table, if already loaded
			//                TableDeleteAll(sControlName)

			//sString is string
			//sLine is string
			//sKey is string
			//sItem is string
			//sWindowName is string=extractstring(sControlName,1,".")
			//sTableName is string=ExtractString(sControlName,2,".")
			//                sTableName = goTR : ExtractFieldName(sTableName)
			//iI is int
			//sSort is string
			//sSortOrder is string
			//sPrefNation is string="US_"


			//                'Process the record
			//                sString = goTR : FieldToString("_META.MMO_MemoValue")
			//                memcreate("clList")
			//sSort=nospace(upper(goTr:StrRead(sString, "SORT", "NUMERIC", False)))
			//sSortOrder=nospace(goTr:StrRead(sString, "ORDER", "", false))
			//                'Loop on all lines of the Page
			//                For iI = 0 To 99
			//                    sLine = goTR : StrRead(sString, sPrefNation + nospace(numtostring(iI)), "", False)
			//                    If sLine <> "" Then
			//                        Switch(sSort)
			//		case "ALPHA"				
			//                        memAdd("clList", upper(complete(sLine, 30)) + TAB() + nospace(numtostring(iI)), complete(sLine, 30) + TAB() + nospace(numtostring(iI)))
			//		case "FIXED"


			//			memAdd("clList", numtostring(:FindPosition(sSortOrder,nospace(numtostring(iI))),"03d")+tab+ ...
			//							 nospace(numtostring(iI,"03d"))+tab+sLine,...
			//							 numtostring(:FindPosition(sSortOrder,nospace(numtostring(iI))),"03d")+tab+ ...
			//							 nospace(numtostring(iI))+tab+sLine)
			//		other case	' mainly the default case: "NUMERIC"
			//                        memAdd("clList", nospace(numtostring(iI, "03d")) + TAB() + sLine, nospace(numtostring(iI)) + TAB() + sLine)
			//                        End
			//                        End
			//                        End

			//                        'Sort the memory zone
			//                        Memsort("clList")

			//                        'Then loop on all the lines of the memory zone
			//                        For iI = 1 To memcount("clList")
			//                            Switch(sSort)
			//	case "ALPHA"				
			//                            tableAdd(sControlName)
			//		{sWindowName+".COL_KEY_"+sTableName}[tableCount(sControlName)]	=extractstring(memrecupere("clList",iI),2)
			//		{sWindowName+".COL_E_"+sTableName}[tableCount(sControlName)]	=extractstring(memrecupere("clList",iI),1)
			//	case "FIXED"
			//                            tableAdd(sControlName)
			//		{sWindowName+".COL_KEY_"+sTableName}[tableCount(sControlName)]	=extractstring(memrecupere("clList",iI),2)
			//		{sWindowName+".COL_E_"+sTableName}[tableCount(sControlName)]	=extractstring(memrecupere("clList",iI),3)

			//	other case 	' mainly the default case: "NUMERIC"
			//                            tableAdd(sControlName)
			//		{sWindowName+".COL_KEY_"+sTableName}[tableCount(sControlName)]	=extractstring(memrecupere("clList",iI),1)
			//		{sWindowName+".COL_E_"+sTableName}[tableCount(sControlName)]	=extractstring(memrecupere("clList",iI),2)

			//                            End
			//                            End

			//                            memdeleteall("clList")

			//                            'And finally process other languages
			//                            'French for now
			//                            sPrefNation = "FR_"
			//                            For iI = 0 To 99
			//                                sLine = goTR : StrRead(sString, sPrefNation + nospace(numtostring(iI)), "")
			//                                If sLine <> "" Then
			//                                    'Search if the line is already in the table
			//                                    If tableseek(sWindowName + ".COL_KEY_" + sTableName, numtostring(iI)) > 0 Then
			//			{sWindowName+".COL_F_"+sTableName}[tableseek(sWindowName+".COL_KEY_"+sTableName, numtostring(iI))]=sLine
			//                                    Else
			//                                        'Not found, we must add th line
			//                                        tableadd(sControlName)
			//			{sWindowName+".COL_KEY_"+sTableName}[tableCount(sControlName)]	=iI
			//			{sWindowName+".COL_F_"+sTableName}[tableCount(sControlName)]	=sLine
			//                                        End
			//                                        End
			//                                        End

			//                                        'Select Default
			//                                        If TableCount(sWindowName + ".TBL_" + sTableName) > 0 Then
			//	iDefVal = Val(goTr:StrRead(sString, "DEFAULT",0,False))
			//                                            '	IF iDefVal < 0 THEN iDefVal = 0
			//                                            '	IF iDefVal >= SELL_MAX_LIST_ITEMS THEN iDefVal = 0
			//                                            iRow = tableseek(sWindowName + ".COL_KEY_" + sTableName, numtostring(iDefVal))
			//                                            If iRow > 0 Then
			//		{sWindowName+".COL_DEFAULT_"+sTableName}[iRow] = 1
			//                                            Else
			//		{sWindowName+".COL_DEFAULT_"+sTableName}[1] = 1
			//                                                End
			//                                                End

			return true;
		}
		public bool zInitArray()
		{
			//CS PORTED as placeholder.

			//PURPOSE:
			//		Load the dynamic array with the content of all lists
			//PARAMETERS:
			//		N/A
			//RETURNS:
			//		True if operation succeded, false if not
			//HOW IT WORKS:
			//		Filter metadata file and load the LST_ pages
			//EXAMPLE:
			//		Automatically called from the goData:OpenDB method
			//		

			string sProc = "clList::InitArray";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start ", SELL_LOGLEVEL_DEBUG)

			//CS: Commented from here to end.
			//if not goData:IsDBOpen() then 
			//            result(False)
			//            End
			//            'First delete the array, if already loaded
			//            : DeleteAll()

			//            'Filter to select all lists
			//lFilter is long int=goData:hfilter("_META","UnikKey",Complete("GLOBAL",dimension(_META.TXT_SECTION))+...
			//									  		"LST_"+repete(" ",30),...
			//											  Complete("GLOBAL",dimension(_META.TXT_SECTION))+...
			//											  "LST_"+repete("z",30))
			//if goData:hreadFirst(lFilter) then		'"_META","UnikKey")

			//	sString is string
			//	sSort is string
			//	sSortOrder is string
			//	sLine is string

			//	iI is int
			//	iJ is int
			//	sFullLine is string
			//	iTot is int=goP:GetNumberOfSupportedLangs()
			//	sSupLang is string=goP:GetSupportedLangs()
			//                'Loop on all list records
			//	loop
			//            'Process of a record
			//            sString = goTR : FieldToString("_META.MMO_MemoValue")
			//            memcreate("clList")
			//		sSort=nospace(upper(goTr:StrRead(sString, "SORT", "NUMERIC", false)))
			//		sSortOrder=nospace(goTr:StrRead(sString, "ORDER", "", false))
			//            'Loop on all lines of the Page
			//            For iI = 0 To 99
			//                sLine = goTR : StrRead(sString, nospace(numtostring(iI)), "")
			//                sFullLine = ""
			//                For iJ = 1 To iTot
			//				sFullLine+=goTr:StrRead(sString, extractstring(sSupLang,iJ)+"_"+nospace(numtostring(iI)),"",false)+"|"
			//                    End
			//                    sFullLine = Left(sFullLine, Length(sFullLine) - 1)       'To remove the ending "|"

			//                    If sLine <> "" Then
			//                        Switch(sSort)
			//				case "ALPHA"				
			//                        memAdd("clList", upper(complete(sLine, 30)) + TAB() + nospace(numtostring(iI)), nospace(numtostring(iI) + TAB() + sFullLine))
			//				case "FIXED"
			//					memAdd("clList", numtostring(:FindPosition(sSortOrder,nospace(numtostring(iI))),"03d")+tab+ ...
			//									 nospace(numtostring(iI,"03d"))+tab+sLine,...
			//                        '									 numtostring(:FindPosition(sSortOrder,nospace(numtostring(iI))),"03d")+tab+ ...
			//									 nospace(numtostring(iI))+tab+sFullLine)
			//				other case	' mainly the default case: "NUMERIC"
			//                        memAdd("clList", nospace(numtostring(iI, "03d")) + TAB() + sLine, nospace(numtostring(iI)) + TAB() + sFullLine)
			//                        End
			//                        End
			//                        End

			//                        'Sort the memory zone
			//                        Memsort("clList")

			//                        'Then loop on all the lines of the memory zone
			//                        For iI = 1 To memcount("clList")
			//                            Switch(sSort)
			//			case "ALPHA"				
			//				:AddItem(goTr:RemovePrefix(_META.TXT_PAGE), extractstring(memrecupere("clList",iI),1), extractstring(memrecupere("clList",iI),2))
			//			case "FIXED"
			//				:AddItem(goTr:RemovePrefix(_META.TXT_PAGE), extractstring(memrecupere("clList",iI),1), extractstring(memrecupere("clList",iI),2))
			//			other case 	' mainly the default case: "NUMERIC"
			//				:AddItem(goTr:RemovePrefix(_META.TXT_PAGE), extractstring(memrecupere("clList",iI),1), extractstring(memrecupere("clList",iI),2))
			//                            End
			//                            End

			//                            memdeleteall("clList")
			//		if not goData:hReadNext(lFilter) then break		'"_META","UnikKey")
			//                                End
			//                                End
			//goData:                         hdeactivatefilter(lFilter)           '"_META")

			return true;
		}
		public void zAddItem(string par_sListName, string par_sKey, string par_sValue)
		{
			//CS PORTED as placeholder. :daItems code will be changed.

			//Add item in the dynamic array
			string sProc = "clList::AddItem";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//CS: Commented out from here to end
			//Increse the size of the dynamic array by one 'line'
			//if :daItems=Null then
			//	:daItems=new array of 1 by :lColInArray string 'dynamic
			//        Else
			//	Dimension(:daItems,(Dimension(:daItems)/:lColInArray)+1, :lColInArray)
			//            End

			//            ''Create the dynamic structure which is going to be stored in the array
			//            ':daItems[Dimension(:daItems)/lColInArray,1]=new stItems


			//            'And put values in int
			//:daitems[Dimension(:daItems)/:lColInArray,SELL_LISTNAME]	=par_sListName  
			//:daitems[Dimension(:daItems)/:lColInArray,SELL_ITEMKEY]		=par_sKey
			//lIndex is long int=1
			//sWork is string
			//loop
			//        sWork = extractstring(par_sValue, lIndex, "|")
			//        If sWork = eot Then break()
			//	:daitems[Dimension(:daItems)/:lColInArray, SELL_ITEMVALUE+lIndex-1]	=sWork
			//	lIndex++
			//        End


			//        'trace(par_sListName+" - "+par_sKey+" - "+ par_sValue)
		}
		public bool zDeleteAll()
		{
			//CS PORTED as a placeholder. Always returns true

			//PURPOSE:
			//		Delet all items in Dynamic array (for closing or reload)
			//PARAMETERS:
			//		N/A
			//RETURNS:
			//		True if something deleted, false if already empty
			//HOW IT WORKS:
			//		--
			//EXAMPLE:
			//		:DeleteAll()
			//		Internal function, should not be called from outside the class

			string sProc = "clList::DeleteAll";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//CS: Commented from here to end.
			//if :daitems<>Null then 
			//	iI is int=Dimension(:daitems)/:lColInArray
			//            While iI > 0
			//                : RemoveItem(iI)
			//		iI--
			//                End
			//                Result(True)
			//else
			//                result(False)
			//                End
			return true;
		}
		public int zFindPosition(string par_sString, string par_sValue)
		{
			//CS OK

			//PURPOSE:
			//		Find the position of par_sValue in par_sString (result is a position in 
			//		an enumeration, not a character position)
			//PARAMETERS:
			//		par_sString:	String in which we have to search
			//						(format 6,11,5,3...)
			//		par_sValue:		Value to search for
			//RETURNS:
			//		The position in the enumeration (5 has position 3 by example) or 
			//		500 if not found (to be at the end, max value being 255)
			//HOW IT WORKS:
			//		This is used by the list sort order management for a fixed order choosen by user
			//EXAMPLE:
			//		
			string sProc = "clList::FindPosition";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sString = par_sString;
			string sValue = par_sValue;

			int iI = 1;
			string sFound = null;
			do
			{
				sFound = goTR.ExtractString(sString, iI, ",");
				if (sFound == sValue)
				{
					return iI;
				}
				else
				{
					if (sFound[0] == clC.EOT)
					{
						return 500;
					}
					else
					{
						iI = iI + 1;
					}
				}
			} while (true);
// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return 0;
		}
		public string zGetListAsString(string par_sFile, string par_sField, string par_sLang = "")
		{
			//PORT as placeholder only (code commented). Always return ""

			//PURPOSE:
			//		Return a complete list as a INI like string (KEY=Label+RC+...) for a specific MLS field
			//PARAMETERS:
			//		par_sFile:	File concerned
			//		par_sField:	Name of the field (must be a MLS)
			//		par_sLang:	if empty, returns the label in current language. If not, in the specified one (US, FR...)
			//RETURNS:
			//		The string with the result or an empty string and a seterror
			//HOW IT WORKS:
			//		Used by the API (by example) to retrieve a whole list to display
			//		The list is in the array sort order (ie the one choosen in the metadata record and used to load the array)
			//EXAMPLE:

			//		sString=goList:GetListAsString(sFileName, sFieldName)

			string sProc = "clList::GetListAsString";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_sFile+" "+par_sField+" "+par_sLang, SELL_LOGLEVEL_DETAILS)

			//CS: Commented from here to end.
			//goLog:  SetError()

			//sFileName is string=nospace(upper(par_sFile))
			//if goData:GetFileIndex(sFileName)<1 then
			//goLog:      SetError(10100, sProc, "", par_sFile)
			//            ' Internal error: Incorrect file name. Please contact your system administrator and give him the file name causing the problem: '[1]'
			//            result("")
			//            End

			//sFieldName is string=nospace(upper(par_sField))
			//if goTr:ExtractFieldPrefix(sFieldName)<>"MLS" or goData:IsFieldValid(sFileName, sFieldName)<1 then
			//goLog:          SetError(10107, sProc, "", par_sField)
			//                ' Internal error: Incorrect field name. Please contact your system administrator and give him the field name causing the problem: '[1]'
			//                result("")
			//                End

			//                'Here, we build the name of the list (FILENAME:FIELDNAMEwithoutPrefix)
			//sListName is string	=sFileName+":"+right(sFieldName,length(sFieldName)-4)

			//sResult is string	=""

			//iI is int=1
			//if :daItems=Null then 
			//goLog:              SetError(10105, sProc, "", MessTraduit(5000))
			//                    '										----- Anglais/English (3) -----
			//                    '										Lists not initialized.
			//                    '										----- Francais/French (5) -----
			//                    '										Listes non initialis�es.	
			//                    ' An information is missing to perform the work: '[1]'.
			//                    '
			//                    'Please contact your system administrator.
			//                    result("")
			//                    End

			//                    'Now we can work
			//while iI<=dimension(:daItems)/:lColInArray
			//                        'trace(:daItems[iI]:sListName+ " - "+sListName )
			//	if nospace(:daItems[iI, SELL_LISTNAME])=sListName then
			//		goTr:StrWrite(sResult,:daItems[iI,SELL_ITEMKEY],:GetValue(iI, par_sLang))		':daItems[iI, SELL_ITEMVALUE])
			//                            End
			//	iI++
			//                            End

			//                            If sResult = "" Then
			//                                'We found nothing with this listName, we now try to see if it's a GLOBAL LST (currency by example)
			//                                'Here, we build the name of the list without the FileName(:FIELDNAMEwithoutPrefix)
			//                                sListName = ":" + right(sFieldName, length(sFieldName) - 4)

			//                                iI = 1
			//                                'Now we can work
			//	while iI<=dimension(:daItems)/:lColInArray
			//		if nospace(:daItems[iI,SELL_LISTNAME])=sListName then
			//			goTr:StrWrite(sResult,:daItems[iI,SELL_ITEMKEY],:GetValue(iI, par_sLang))		':daItems[iI, SELL_ITEMVALUE])
			//                                        End
			//		iI++
			//                                        End

			//                                        End


			//                                        If sResult = "" Then
			//goLog:                                      SetError(10104, sProc, "", "FIELD", sProc, sFileName + "." + sFieldName)
			//                                            ' Not found: The parameter '[1]' received in '[2]' is equal to '[3]'. This value was not found in the files during the verification process.
			//                                            '
			//                                            '
			//                                            result("")
			//                                            End

			//                                            result(sResult)
			//CS: Remove below
			return "";
		}
		public string zGetListOfElements(string par_sFileName, string par_sFieldName, string par_sLangCode = "US")
		{
			//CS Ported all except Mem stuff.
			//PORT. Replace Memxxxx stuff with SortedList. See me.

			//AUTHOR: MI 5/6/03
			//PURPOSE:
			//		==============================================================================================================
			//		From FH:
			//		This is doing the same thing (the format of the string returned is slightly different) than the
			//		GetListAsString method... 
			//		The only difference is that it's loading the information from metadata, even if all these pieces of information 
			//		are already available in memory...
			//		If there is not a good reason to do that, the use of this method will only SLOW DOWN the process, as the whole
			//		loading and sorting done here should be unnecessary... If possible, use GetListAsString instead.
			//		===============================================================================================================
			//		Return a string with a list of elements of an MLS list (LST_ field)
			//		sorted according to the SORT= property in the LST_ metadata. Use
			//		the returned list for filling a combo, list, table, or manipulating
			//		in memory.
			//		Each line in the list represents one elements.
			//		Each line consists of two, TAB-delimited values: Key+TAB+Text
			//		There is no trailing CR.
			//		Use ExtractString() to parse lines and values within each line.
			//
			//PARAMETERS:
			//		par_sFileName: name of the file
			//		par_sFieldName: name of the field
			//		par_sLangCode: code of the language for which to return the list
			//			Example: "US", "FR", or any other supported language code.
			//
			//RETURNS:
			//		String: CR_delimited list of elements.

			//For debugging the order of calls.
			string sProc = "clList::GetListOfElements";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sResult = "";
			string sString = null;
			string sFileName = par_sFileName.ToUpper();
			string sFieldName = par_sFieldName.ToUpper();
			string sLine = "";
			string sSort = "";
			string sSortOrder = "";
			string sLangCode = par_sLangCode.Substring(0, 2).ToUpper() + "_";
			int iI = 0;
			string sListName;

			//Build the Page ID of the list (FILENAME:FIELDNAMEwithoutPrefix)
			sListName = "LST_" + sFileName + ":" + sFieldName.Substring(4);
			sString = goMeta.PageRead("GLOBAL", sListName, "", true);
			if (sString == "")
			{
				return "";
			}

			//if not goData:ReadByID(sTID) then result ""
			//sString=goTr:FieldToString("_META.MMO_MemoValue")

			//The following line returns the list in format:
			//"1=Value of 1"+CR+...
			//...
			//"10=Value of 10"+CR
			//sString = goList:GetListAsString(sFileName,sFieldName)


			//CS: Commenting from here down b/c it is dealing with memory
			//zones. Per MI, this needs to be changed to use SortedList class.
			//        memcreate("ListElements")
			//sSort=nospace(upper(goTr:StrRead(sString, "SORT", "NUMERIC", False)))
			//sSortOrder=nospace(goTr:StrRead(sString, "ORDER", "", false))
			//        'Loop on all lines of the Page
			//        For iI = 0 To 99
			//            sLine = goTR : StrRead(sString, sLangCode + nospace(numtostring(iI)), "", False)
			//            If sLine <> "" Then
			//                Switch(sSort)
			//			case "ALPHA"				
			//				memAdd("ListElements",upper(complete(sLine,30))+tab+...
			//						nospace(numtostring(iI)),complete(sLine,30)+tab+...
			//						nospace(numtostring(iI)))
			//			case "FIXED"
			//				memAdd("ListElements", numtostring(:FindPosition(sSortOrder,nospace(numtostring(iI))),"03d")+tab+ ...
			//								 nospace(numtostring(iI,"03d"))+tab+sLine,...
			//								 numtostring(:FindPosition(sSortOrder,nospace(numtostring(iI))),"03d")+tab+ ...
			//								 nospace(numtostring(iI))+tab+sLine)
			//			other case	' mainly the default case: "NUMERIC"
			//                memAdd("ListElements", nospace(numtostring(iI, "03d")) + TAB() + sLine, nospace(numtostring(iI)) + TAB() + sLine)
			//                End
			//                End
			//                End

			//                'Sort the memory zone
			//                Memsort("ListElements")

			//                'Then loop on all the lines of the memory zone
			//                For iI = 1 To memcount("ListElements")
			//                    Switch(sSort)
			//	case "ALPHA"
			//                    If sResult <> "" Then sResult += CR
			//		sResult+=	extractstring(memrecupere("ListElements",iI),2)+TAB+...
			//                    extractstring(memrecupere("ListElements", iI), 1)
			//	case "FIXED"
			//                    If sResult <> "" Then sResult += CR
			//		sResult+=	extractstring(memrecupere("ListElements",iI),2)+TAB+...
			//                    extractstring(memrecupere("ListElements", iI), 3)

			//	other case 	' mainly the default case: "NUMERIC"
			//                    If sResult <> "" Then sResult += CR
			//		sResult+=	extractstring(memrecupere("ListElements",iI),1)+TAB+...
			//                    extractstring(memrecupere("ListElements", iI), 2)

			//                    End
			//                    End

			//                    memdeleteall("ListElements")

			return sResult;
		}
		public string zGetValue(string par_iIndex, string par_sLang = "")
		{
			//CS In progress, WT review array code (:daItems) and change
			//Need goP.GetLangCode, goP.GetSupportedLangs,goP.GetDefaultLang

			//PURPOSE:
			//		Returns the value (label) for the index inthe array in the current or in a specific language
			//PARAMETERS:
			//		par_iIndex:	Index to retrieve
			//		par_sLang:	lang of the label we want to retrieve... If empty, current language is used
			//RETURNS:
			//		A label, in the required language
			//HOW IT WORKS:
			//		Find the column corresponding to the desired language an returns the corresponding label
			//EXAMPLE:
			//		:GetValue(iI)

			string sProc = "clList::GetValue";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_iIndex+" "+par_sLang , SELL_LOGLEVEL_DEBUG)

			int iI = Convert.ToInt32(par_iIndex);
			string sLang = par_sLang;


			//CS: Ported all code but am commenting it out b/c of missing
			//functions and also b/c array stuff will be changed.
			//Retrieve the current language code if necessary

			//        If Trim(sLang) = "" Then
			//            sLang = goP.GetLangCode(goP.Nation())
			//            goLog.SetError()    'In case nation is wrong, GetLangCode set an error 10103
			//        End If

			//        'Then retrieve the corersponding position in the list of languages
			//        Dim sSupLang As String = goP.GetSupportedLangs()
			//        Dim sWork As String
			//LoopOnLangs:
			//        Dim iJ As Integer = 1
			//        Do
			//            sWork = goTR.ExtractString(sSupLang, iJ)
			//            If sWork = clC.EOT Then Return ""
			//            If sWork = sLang Then
			//		if trim(:daItems[iI,SELL_ITEMVALUE+iJ-1])<>"" then 
			//			result :daItems[iI,SELL_ITEMVALUE+iJ-1]
			//                Else
			//                    If sLang <> goP.GetDefaultLang() Then
			//                        sLang = goP.GetDefaultLang()
			//                        GoTo LoopOnLangs
			//                    Else
			//                        Return ""
			//                    End If
			//                End If
			//            End If
			//            iJ = iJ + 1
			//        Loop

			//CS: Remove later
			return "";
		}
	}

}
