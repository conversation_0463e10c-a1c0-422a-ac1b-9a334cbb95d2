﻿CREATE TABLE [dbo].[GR_EditableBy_US] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_GR_EditableBy_US_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_GR] UNIQUEIDENTIFIER NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_GR_EditableBy_US] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_GR_EditableBy_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_Edits_GR] FOREIGN KEY ([GID_GR]) REFERENCES [dbo].[GR] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[GR_EditableBy_US] NOCHECK CONSTRAINT [LNK_GR_EditableBy_US];


GO
ALTER TABLE [dbo].[GR_EditableBy_US] NOCHECK CONSTRAINT [LNK_US_Edits_GR];


GO
CREATE NONCLUSTERED INDEX [IX_US_Edits_GR]
    ON [dbo].[GR_EditableBy_US]([GID_US] ASC, [GID_GR] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_GR_EditableBy_US]
    ON [dbo].[GR_EditableBy_US]([GID_GR] ASC, [GID_US] ASC);

