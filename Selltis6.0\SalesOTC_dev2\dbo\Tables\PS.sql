﻿CREATE TABLE [dbo].[PS] (
    [GID_ID]               UNIQUEIDENTIFIER CONSTRAINT [DF_PS_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),(0))+CONVERT([binary](2),'PS',(0)))+CONVERT([binary](6),getutcdate(),(0)),(0))) ROWGUIDCOL NOT NULL,
    [BI__ID]               BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]             NVARCHAR (80)    NULL,
    [DTT_CreationTime]     DATETIME         CONSTRAINT [DF_PS_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]         TINYINT          NULL,
    [CHK_DoNotAutoconnect] TINYINT          NULL,
    [TXT_ModBy]            VARCHAR (4)      NULL,
    [DTT_ModTime]          DATETIME         CONSTRAINT [DF_PS_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TEL_Phone]            NVARCHAR (39)    NULL,
    [MMO_ImportData]       NTEXT            NULL,
    [SI__ShareState]       TINYINT          CONSTRAINT [DF_PS_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]     UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]         VARCHAR (50)     NULL,
    [TXT_ExternalID]       NVARCHAR (80)    NULL,
    [TXT_ExternalSource]   VARCHAR (10)     NULL,
    [TXT_ImpJobID]         VARCHAR (20)     NULL,
    CONSTRAINT [PK_PS] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_PS_TXT_ImportID]
    ON [dbo].[PS]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PS_Phone]
    ON [dbo].[PS]([TEL_Phone] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PS_Name]
    ON [dbo].[PS]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PS_ModDateTime]
    ON [dbo].[PS]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PS_CreationTime]
    ON [dbo].[PS]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PS_CreatedBy_US]
    ON [dbo].[PS]([GID_CreatedBy_US] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_PS_BI__ID]
    ON [dbo].[PS]([BI__ID] ASC);

