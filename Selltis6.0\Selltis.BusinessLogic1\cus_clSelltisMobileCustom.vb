﻿Imports Microsoft.VisualBasic
Imports System.IO
Imports System.Web


Public Class cus_clSelltisMobileCustomTest

End Class
Public Class cus_clSelltisMobileCustom
    Inherits clSelltisMobileBase

    Public Sub New(data As cljsondata)

        'call the base to initialize
        MyBase.New(data)
    End Sub

    Public Overrides Function OnPreSaveData(sVar As String) As Boolean
        Dim goTR As clTransform
        goTR = HttpContext.Current.Session("goTr")
        'WriteLog("CusScripts", "OnSavePreData")

        Dim sSectionsToDisable As String = ""

        'CS 05192015 This runs before the record has been committed to the rowset and before all the fields have been set to the rowset.
        'Add custom code here.



        'Disable base sections of main script here
        'EXAMPlE: 
        'goTR.StrWrite(sSectionsToDisable, "FILLACENDDATEANDTIME", "1")
        'Base function checks for every code section before running it to see if disabled in cus.

        'WriteLog("CusScripts", sSectionsToDisable)


        'call the base script
        'CS 05192015 Call the base OnPreSave function. If you do not want to call the base function, comment out this line.
        If MyBase.OnPreSaveData(sSectionsToDisable) = False Then
            Return False
        End If


        Return True

    End Function

    Public Overrides Function OnPostSaveData(SectionsToDisable As String) As Boolean

        'WriteLog("CusScripts", "OnSavePostData")

        'CS 05192015 This runs after the record has been committed to the rowset.
        'Add custom code here.

        'call the base to initialize
        'CS 05192015 Call the base OnPostSave function. If you do not want to call the base function, comment out this line.
        Return MyBase.OnPostSaveData(SectionsToDisable)




    End Function

End Class
