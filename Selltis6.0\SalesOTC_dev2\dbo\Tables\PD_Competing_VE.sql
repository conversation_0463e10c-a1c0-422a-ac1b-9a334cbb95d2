﻿CREATE TABLE [dbo].[PD_Competing_VE] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Product_Competing_Vendor_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PD] UNIQUEIDENTIFIER NOT NULL,
    [GID_VE] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PD_Competing_VE] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PD_Competing_VE] FOREIGN KEY ([GID_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_VE_CompetitorTo_PD] FOREIGN KEY ([GID_PD]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PD_Competing_VE] NOCHECK CONSTRAINT [LNK_PD_Competing_VE];


GO
ALTER TABLE [dbo].[PD_Competing_VE] NOCHECK CONSTRAINT [LNK_VE_CompetitorTo_PD];


GO
CREATE CLUSTERED INDEX [IX_VE_CompetitorTo_PD]
    ON [dbo].[PD_Competing_VE]([GID_PD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PD_Competing_VE]
    ON [dbo].[PD_Competing_VE]([GID_VE] ASC);

