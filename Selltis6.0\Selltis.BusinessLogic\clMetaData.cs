﻿using System;
using System.Data;
using System.Web;

//Owner: MI since 5/8/06. Was WT.

namespace Selltis.BusinessLogic
{
	public class clMetaData
	{

		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clLog goLog;
		private clDefaults goDef;
		// using local variable. deprecated oMetaConnection member variable - MN 1/22/2018
		// Public oMetaConnection As SqlClient.SqlConnection = Nothing





		public int DeleteMetaObject(string par_sSectionID, string par_sPageID, bool par_bSilent = false, string par_sProduct = "")
		{
			//MI 1/16/09 Added par_sProduct.
			//MI 5/7/08 Added test for PERMSHAREDDESKTOPS
			//MI 12/5/06 Ported from NGP.

			//PURPOSE:
			//   Delete the _META record for the metadata object if WG permissions
			//   allow the user to do so. 
			//   First, the parameters are validated, then user permissions are
			//   checked. In NGP, if the user had permissions to delete the object,
			//   we asked the user whether to delete unless par_bSilent was True.
			//   in SellSQL, the deletion occurs without user interaction.
			//   If everything passes we delete the object.
			//PARAMETERS:
			//   par_sSectionID: Metadata "section" ID of the object. Supported:
			//       36-character GID_ID of the user who created the object or
			//       "GLOBAL" or "" to delete a shared object.
			//   par_sObjectID: Page ID of the metadata object, for example
			//       "DSK_Xxxxxxxxx" or "POP_PERSONL_OPTIONS".
			//   par_bSilent: *** NOT SUPPORTED *** Always False.
			//       In NGP, this parameter allowed this method to interact
			//       with the user.
			//   par_sProduct: optional: product code. If nothing or "",
			//       the current session product will be used.
			//RETURNS:
			//   Integer:
			//       1 if the object was deleted or wasn't found;
			//       0 if the object was not found or deletion erred;
			//       -1 if the user didn't have permission to delete the object;
			//       -2 if deleting dependent objects failed.
			//       -1 can be returned when the object doesn't exist, for example
			//       when the user is a partial author. In this case the Creator ID
			//       of the object is "" and that doesn't match the User's ID.
			//       If the user is not an author, deleting any
			//       GLOBAL page will fail and -1 will be returned. Likewise,
			//       a partial author (goP.IsUserAuthor returning 1) can't delete
			//       pages (s)he didn't create.
			//       The result of the deletion of related objects is not
			//       reported.

			string sProc = "clUI::DeleteMetaObject";

			//==> Not finished, must add considering user permissions

			//Delete _META record under GLOBAL section if shared,
			//under username section if not shared.
			string sPageID = par_sPageID.ToString().Trim(' ');
			string sSectionID = par_sSectionID.ToString().Trim(' ');
			string sMetaType = null;
			string sVals = null;
			string sUserID = null;
			int iResult = 1;
			string sProduct = null;

			int iViewCount = 0;
			int i = 0;
			string sViewID = null;
			string sTemplFile = null;

			par_bSilent = true;

			sMetaType = goDef.GetMetaTypeFromPrefix(sPageID.Substring(0, 4));
			sUserID = goP.GetUserTID();

			if (par_sProduct == null)
			{
				sProduct = "";
			}
			else
			{
				sProduct = par_sProduct.ToUpper();
			}
			if (sProduct == "")
			{
				sProduct = goP.GetProduct();
			}
			if (sProduct == "")
			{
				goErr.SetError(35000, sProc, "Product is not initialized. goP.GetProduct returned ''.");
			}

			//=============== CHECK PERMISSIONS, ASK USER ================
			if (sSectionID == "GLOBAL" || sSectionID == "") //Object is shared
			{
				if (goP.GetMe("PERMSHAREDDESKTOPS") == "1")
				{
					//Like full author
					//If Not par_bSilent Then
					//    Select Case (Left(sPageID, 4))
					//        Case "DBB_"
					//            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
					//            If Not YesNo(goTR.MessComplete(MessTranslate(5010), sMetaType, sName)) Then
					//                '5010:Are you sure you want to delete the shared [1] '[2]' and all of its Shortcuts?
					//                '
					//                'This will affect all workgroup users.
					//                Return (False)
					//            End If
					//        Case "DBS_"
					//            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
					//            If Not YesNo(goTR.MessComplete(MessTranslate(5011), sMetaType, sName)) Then
					//                '5011:Are you sure you want to remove the shared [1] '[2]'?
					//                '
					//                'This will affect all workgroup users.
					//                Return (False)
					//            End If
					//        Case "SND_"
					//            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
					//            If Not YesNo(goTR.MessComplete(MessTraduit(5072), sMetaType, sName)) Then
					//                '5072:Are you sure you want to delete the shared [1] '[2]'?
					//                '
					//                'The template file will be deleted as well.
					//                '
					//                'This will affect all workgroup users.
					//                Return (False)
					//            End If
					//        Case Else
					//            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
					//            If Not YesNo(goTR.MessComplete(MessTranslate(5003), sMetaType, sName)) Then
					//                '5003:Are you sure you want to delete the shared [1] '[2]'?
					//                '
					//                'This will affect all workgroup users.
					//                Return (False)
					//            End If
					//    End Select
					//End If
				}
				else
				{
					switch (goP.IsUserAuthor())
					{
						case 1: //Partial author
							//   IF goTr:StrRead(sVals,"CREATORID","",False) = sUserID THEN
							if (goMeta.GetCreatorID(par_sSectionID, par_sPageID, sProduct).ToUpper() == sUserID.ToUpper())
							{
								//User is creator of this object, allow deleting
								//If Not par_bSilent Then
								//    Select Case (Left(sPageID, 4))
								//        Case "DBB_"     'Dock Bar Button
								//            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
								//            If Not YesNo(goTR.MessComplete(MessTranslate(5010), sMetaType, sName)) Then
								//                '5010:Are you sure you want to delete the shared [1] '[2]' and all of its shortcuts?
								//                '
								//                'This will affect all workgroup users.
								//                Return (False)
								//            End If
								//        Case "DBS_"     'Dock Bar Shortcut
								//            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
								//            If Not YesNo(goTR.MessComplete(MessTranslate(5011), sMetaType, sName)) Then
								//                '5011:Are you sure you want to remove the shared [1] '[2]'?
								//                '
								//                'This will affect all workgroup users.
								//                Return (False)
								//            End If
								//        Case "SND_"
								//            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
								//            If Not YesNo(goTR.MessComplete(MessTraduit(5072), sMetaType, sName)) Then
								//                '5072:Are you sure you want to delete the shared [1] '[2]'?
								//                '
								//                'The template file will be deleted as well.
								//                '
								//                'This will affect all workgroup users.
								//                Return (False)
								//            End If
								//        Case Else
								//            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
								//            If Not YesNo(goTR.MessComplete(MessTranslate(5003), sMetaType, sName)) Then
								//                '5003:Are you sure you want to delete the shared [1] '[2]'?
								//                '
								//                'This will affect all workgroup users.
								//                Return (False)
								//            End If
								//    End Select
								//End If
							}
							else
							{
								//User not creator, not allowed to delete.
								//If Not par_bSilent Then
								//    NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
								//    Info(goTR.MessComplete(MessTranslate(5005), sMetaType))
								//    '5005:You are not allowed to delete this shared [1] because you didn't create it.
								//End If
								return -1;
							}
							break;

						case 2: //Full author
						break;
							//If Not par_bSilent Then
							//    Select Case (Left(sPageID, 4))
							//        Case "DBB_"
							//            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
							//            If Not YesNo(goTR.MessComplete(MessTranslate(5010), sMetaType, sName)) Then
							//                '5010:Are you sure you want to delete the shared [1] '[2]' and all of its Shortcuts?
							//                '
							//                'This will affect all workgroup users.
							//                Return (False)
							//            End If
							//        Case "DBS_"
							//            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
							//            If Not YesNo(goTR.MessComplete(MessTranslate(5011), sMetaType, sName)) Then
							//                '5011:Are you sure you want to remove the shared [1] '[2]'?
							//                '
							//                'This will affect all workgroup users.
							//                Return (False)
							//            End If
							//        Case "SND_"
							//            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
							//            If Not YesNo(goTR.MessComplete(MessTraduit(5072), sMetaType, sName)) Then
							//                '5072:Are you sure you want to delete the shared [1] '[2]'?
							//                '
							//                'The template file will be deleted as well.
							//                '
							//                'This will affect all workgroup users.
							//                Return (False)
							//            End If
							//        Case Else
							//            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
							//            If Not YesNo(goTR.MessComplete(MessTranslate(5003), sMetaType, sName)) Then
							//                '5003:Are you sure you want to delete the shared [1] '[2]'?
							//                '
							//                'This will affect all workgroup users.
							//                Return (False)
							//            End If
							//    End Select
							//End If
						default: //0: not author or unsupported value
							//If Not par_bSilent Then
							//    NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
							//    Info(goTR.MessComplete(MessTranslate(5002), sMetaType))
							//    '5002:You are not allowed to delete this shared [1].
							//End If
							return -1;
					} //User is author
				}
			}
			else //Object is local
			{
				//The 'authorship' of local objects goes by their SECTION, not Creator User ID so that
				//MD can be created for another .
				if (sSectionID.ToUpper() == sUserID.ToUpper())
				{
					//Allowed to delete
					//If Not par_bSilent Then
					//    Select Case (Left(sPageID, 4))
					//        Case "DBB_"
					//            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
					//            If Not YesNo(goTR.MessComplete(MessTranslate(5009), sMetaType, sName)) Then
					//                '5009:Are you sure you want to delete the [1] '[2]' and all of its shortcuts?
					//                Return (False)
					//            End If
					//        Case "DBS_"
					//            NextTitle("Dock Bar") '(MessTraduit(5014))   '5014:Dock Bar
					//            If Not YesNo(goTR.MessComplete(MessTranslate(5012), sMetaType, sName)) Then
					//                '5012:Are you sure you want to remove the [1] '[2]'?
					//                Return (False)
					//            End If
					//        Case "SND_"
					//            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
					//            If Not YesNo(goTR.MessComplete(MessTraduit(5076), sMetaType, sName)) Then
					//                '5076:Are you sure you want to delete the [1] '[2]'?
					//                '
					//                'The template file will be deleted as well.
					//                Return (False)
					//            End If
					//        Case Else
					//            NextTitle("Delete") '(MessTraduit(5015))   '5015:Delete
					//            If Not YesNo(goTR.MessComplete(MessTranslate(5004), sMetaType, sName)) Then
					//                '5004:Are you sure you want to delete the [1] '[2]'?
					//                Return (False)
					//            End If
					//    End Select
					//End If
				}
				else
				{
					//Not allowed to delete
					return -1;
				}
			}


			//=================== DELETE DEPENDENT RECORDS OR OBJECTS ===============
			//Delete dependent records, if any.
			switch (sPageID.Substring(0, 4))
			{

				case "DSK_": //Desktop: delete all Desktop's Views
					sVals = goMeta.PageRead(sSectionID, sPageID, "", true, sProduct);
					//Mini Views
					iViewCount = Convert.ToInt32(goTR.StrRead(sVals, "VIEWCOUNT", "0", false)); //No of mini views
					i = 1;
					for (i = 1; i <= iViewCount; i++)
					{
						sViewID = goTR.StrRead(sVals, "VIEW" + i + "ID", "", false);
						if (!goMeta.PageDelete(sSectionID, sViewID, sProduct))
						{
							return -2;
						}
					}
					//Tab Views
					iViewCount = Convert.ToInt32(goTR.StrRead(sVals, "TABCOUNT", "0", false)); //No of mini views
					i = 1;
					for (i = 1; i <= iViewCount; i++)
					{
						sViewID = goTR.StrRead(sVals, "TAB" + i + "VIEWID", "", false);
						if (!goMeta.PageDelete(sSectionID, sViewID, sProduct))
						{
							return -2;
						}
					}
					break;

					//Case "DBB_"     'Dock Bar Button: delete all Dock Bar Shortcuts under the button
					//   '*** DBBs are not supported anymore. Now we use WCBs and no shortcuts. 
					//   '*** DSK_ metadata directly references DBBs via the WEBCLIENTBUTTONID parameter.
					//   '*** This parameter could be cleaned up in the future.
					//    sPage = "DBS_"
					//    '---------- SHARED DOCK BAR SHORTCUTS -----------
					//    sSection = "GLOBAL"
					//    sSearchStringFrom = goTR.Complete(sSection, UBound(_META.TXT_Section)) & _
					//    sPage(+RepeatString(Chr(0), (UBound(_META.TXT_Page) - Len(sPage))))
					//    sSearchStringTo = goTR.Complete(sSection, UBound(_META.TXT_Section)) & _
					//    sPage(+RepeatString(Chr(255), (UBound(_META.TXT_Page) - Len(sPage))))
					//    lFilter = goData.HFilter("_META", "UnikKey", sSearchStringFrom, sSearchStringTo)
					//    If goData.HReadFirst(lFilter) Then
					//        Do
					//            sPageMemo = goTR.FieldToString("_META.MMO_MemoValue")
					//            If goTR.StrRead(sPageMemo, "BUTTONID", "", False) = sPageID Then
					//                If Not goData.HDelete(lFilter) Then
					//                    Info(goTR.MessComplete(MessTraduit(5067), goLog.GetLastError(), _
					//                    MessTraduit(5069), _
					//                    goLog.GetLastError(clC.SELL_ERROR_MESSAGE), _
					//                    sMetaType))
					//                    '5069:a Dock Bar Shortcut
					//                    '5067:Error [1] occurred while deleting [2]:
					//                    '
					//                    '[3]
					//                    '
					//                    'The [4] wasn't deleted.
					//                    goLog.SetError()
					//                    goData.HDeactivateFilter(lFilter)
					//                    Return (False)
					//                End If
					//            End If
					//            If Not goData.HReadNext(lFilter) Then Exit Do
					//        Loop
					//    End If
					//    goData.HDeactivateFilter(lFilter)
					//    '---------- LOCAL DOCK BAR SHORTCUTS -----------
					//    sSection = sUserID
					//    sSearchStringFrom = goTR.Complete(sSection, UBound(_META.TXT_Section)) & _
					//                  sPage(+RepeatString(Chr(0), (UBound(_META.TXT_Page) - Len(sPage))))
					//    sSearchStringTo = goTR.Complete(sSection, UBound(_META.TXT_Section)) & _
					//                  sPage(+RepeatString(Chr(255), (UBound(_META.TXT_Page) - Len(sPage))))
					//    lFilter = goData.HFilter("_META", "UnikKey", sSearchStringFrom, sSearchStringTo)
					//    If goData.HReadFirst(lFilter) Then
					//        Do
					//            sPageMemo = goTR.FieldToString("_META.MMO_MemoValue")
					//            If goTR.StrRead(sPageMemo, "BUTTONID", "", False) = sPageID Then
					//                If Not goData.HDelete(lFilter) Then
					//                    Info(goTR.MessComplete(MessTraduit(5067), goLog.GetLastError(), _
					//                     MessTraduit(5069), _
					//                     goLog.GetLastError(clC.SELL_ERROR_MESSAGE), _
					//                     sMetaType))
					//                    '5069:a Dock Bar Shortcut
					//                    '5067:Error [1] occurred while deleting [2]:
					//                    '
					//                    '[3]
					//                    '
					//                    'The [4] wasn't deleted.
					//                    goLog.SetError()
					//                    goData.HDeactivateFilter(lFilter)
					//                    Return (False)
					//                End If
					//            End If
					//            If Not goData.HReadNext(lFilter) Then Exit Do
					//        Loop
					//    End If
					//    goData.HDeactivateFilter(lFilter)

				case "SND_":
					sTemplFile = goMeta.LineRead(sSectionID, sPageID, "TEMPLFILE", "", true, sProduct);
					break;
					//--------- Delete the template file ----------
					//==> Implement deleting the file itself and queuing it for syncing as appropriate

					//If goDF.fDelete(goTR.AddFinalSlash(goData.GetDBPath()) & "SendTmpl\" & sTemplFile) = -1 Then
					//    'Deletion of the template file failed
					//    Info(goTR.MessComplete(MessTraduit(5074), goTR.AddFinalSlash(goData.GetDBPath()) & "SendTmpl\" & sTemplFile))    '*** MI 5/25/05
					//    '5074:Send Template file '[1]' couldn't be deleted.
					//    '
					//    'If the file exists, make sure it is not open or locked, then delete it manually.
					//End If
					//'---------- Sync deletion of the template file -----------
					//If sSectionID = "GLOBAL" Then       'Object is shared
					//    sRecordID = goMeta.GetRecordID(sSectionID, sPageID)
					//    goLog.SetError()
					//    If sTemplFile <> "" Then
					//        If Not goData.WriteFileToSync(sRecordID, "FIL_", "<%DBSendTmplPath%>", sTemplFile, clC.SELL_FILE_DELETE) Then
					//            goLog.DisplayLastError()
					//            goLog.SetError()
					//        End If
					//    End If
					//End If
			}


			//=================== DELETE THE RECORD ======================
			//Delete the record. Make sure the code above this exits the method
			//by returning Return False if the user doesn't have adequate
			//permissions.

			if (!goMeta.PageDelete(sSectionID, sPageID, sProduct))
			{
				return 0;
			}

			return iResult;


		}

		public bool DeleteOldAlerts(string par_sUserID = "", int par_iDaysOld = 8)
		{
			//MI 2/5/07 Created.
			//AUTHOR: MI 2/5/07
			//PURPOSE:
			//		Delete ALT_ metadata older than 30 days for a particular user or for
			//       all users (and GLOBAL, if any). This calls a SS sproc. 
			//PARAMETERS:
			//		par_sUserID: optional: User's TID as string or "" to delete all odl ALT_ MD.
			//       par_iDaysOld: optional: Number of days an alert has to have been created prior to
			//           now in order to be deleted. Default: 8.
			//RETURNS:
			//		True if successful, False if not.

			string sProc = "clMetaData::DeleteOldAlerts";
			string sUserID = par_sUserID.ToUpper().Trim(' ');
			int iResult = 0;

			try
			{

				System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;

				oCommand.CommandText = "pMetaDeleteAlerts";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter
				System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier);
				if (sUserID == "GLOBAL" || sUserID == "")
				{
					uSection.Value = System.DBNull.Value;
				}
				else
				{
					uSection.Value = StringToGuid(sUserID);
				}
				oCommand.Parameters.Add(uSection);

				//parameter
				System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_iDays", SqlDbType.Int);
				strPage.Value = par_iDaysOld;
				oCommand.Parameters.Add(strPage);

				//return parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				//execute

				oReader = oCommand.ExecuteReader();

				//Now you can grab the output parameter's value...
				iResult = Convert.ToInt16(retValParam.Value);

				oReader.Close();
				oConnection.Close();

				if (iResult == 0)
				{
					return true;
				}
				else
				{
					return false; //no error checking in sproc yet
				}

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return false;
			}

		}


		public string GetCreatorID(string par_sSection, string par_sPageID, string par_sProduct = "")
		{
			//MI 1/21/09 Added par_sProduct.
			//MI 5/16/07 Added dealing with nothing sent in Section and Page ID
			//MI 4/1/07 Added Try Catch
			//MI 12/5/06 Created
			//PURPOSE:
			//       Return the ID of the user who created this metadata page.
			//PARAMETERS:
			//       par_sSection: Section ID of the page such as user's GID_ID
			//           or "GLOBAL"
			//       par_sPageID: page ID of the page such as "DSK_Xxxxxx" or
			//           'WOP_WORKGROUP_OPTIONS"
			//       par_sProduct: product code like 'SA', 'MB', 'WP'. 'XX' is a
			//           code for pages that apply to all products. If not defined,
			//           product is taken from the session. If not defined in the
			//           session, 'SA' will be used.
			//RETURNS:
			//       GID_CreatedBy_US value as string or "" if the page is not found
			//       or the Creator ID is for some reason blank. No error raised.

			string sProc = "clMetaData::GetCreatorID";
			string sSection = null;
			string sPageIDVal = null;
			string sText = "";

			//Try
			if (par_sSection == null)
			{
					sSection = "";
				}
				else
				{
					sSection = par_sSection.ToString();
				}
				if (par_sPageID == null)
				{
					sPageIDVal = "";
				}
				else
				{
					sPageIDVal = par_sPageID.ToString();
				}
				System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader reader = null;
				Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();

				cmd.CommandText = "pMetaGetCreatorID";
				//Returns column GID_CreatedBy_US with one record
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				//parameter
				System.Data.SqlClient.SqlParameter uSec = new System.Data.SqlClient.SqlParameter("@par_uSection", System.Data.SqlDbType.UniqueIdentifier);
				if (sSection.ToUpper() == "GLOBAL" || sSection == "")
				{
					uSec.Value = System.DBNull.Value;
				}
				else
				{
					uSec.Value = goTR.StringToGuid(sSection);
				}
				cmd.Parameters.Add(uSec);

				//parameter
				System.Data.SqlClient.SqlParameter sPageID = new System.Data.SqlClient.SqlParameter("@par_sPageID", System.Data.SqlDbType.VarChar);
				sPageID.Value = sPageIDVal;
				cmd.Parameters.Add(sPageID);

				System.Data.SqlClient.SqlParameter sProduct = new System.Data.SqlClient.SqlParameter("@par_sProduct", System.Data.SqlDbType.VarChar);
				sProduct.Value = par_sProduct;
				cmd.Parameters.Add(sProduct);

				reader = cmd.ExecuteReader();
				if (reader.Read())
				{
					sText = reader[0].ToString();
				}

				reader.Close();
				sqlConnection1.Close();
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sText;

		}


		public string GetModificationInfo(string par_sSection, string par_sPageID, string par_sProduct = "")
		{
			//MI 6/16/09 Created.
			//PURPOSE:
			//       Return the User code and datetime of the last modification
			//       of the metadata page. The information is read from the 
			//       most recently modified line. 
			//PARAMETERS:
			//       par_sSection: Section ID of the page such as user's GID_ID
			//           or "GLOBAL".
			//       par_sPageID: page ID of the page such as "DSK_Xxxxxx" or
			//           'WOP_WORKGROUP_OPTIONS".
			//       par_sProduct: product code like 'SA', 'MB', 'WP'. 'XX' is a
			//           code for pages that apply to all products. If not defined,
			//           product is taken from the session. If not defined in the
			//           session, 'SA' will be used.
			//RETURNS:
			//       US.TXT_Code value of the User who last modified the page & " " &
			//       datetime of modification (in yyyy-mm-dd hh:mm:ss.mmm format) as string
			//       or "" if the page is not found. No error raised.

			string sProc = "clMetaData::GetModificationInfo";
			string sSection = null;
			string sPageIDVal = null;
			string sText = "";

			//  Try
			if (par_sSection == null)
			{
					sSection = "";
				}
				else
				{
					sSection = par_sSection.ToString();
				}
				if (par_sPageID == null)
				{
					sPageIDVal = "";
				}
				else
				{
					sPageIDVal = par_sPageID.ToString();
				}
				System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader reader = null;
				Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();

				cmd.CommandText = "pMetaGetModificationInfo";
				//Returns column GID_CreatedBy_US with one record
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				//parameter
				System.Data.SqlClient.SqlParameter uSec = new System.Data.SqlClient.SqlParameter("@par_uSection", System.Data.SqlDbType.UniqueIdentifier);
				if (sSection.ToUpper() == "GLOBAL" || sSection == "")
				{
					uSec.Value = System.DBNull.Value;
				}
				else
				{
					uSec.Value = goTR.StringToGuid(sSection);
				}
				cmd.Parameters.Add(uSec);

				//parameter
				System.Data.SqlClient.SqlParameter sPageID = new System.Data.SqlClient.SqlParameter("@par_sPageID", System.Data.SqlDbType.VarChar);
				sPageID.Value = sPageIDVal;
				cmd.Parameters.Add(sPageID);

				System.Data.SqlClient.SqlParameter sProduct = new System.Data.SqlClient.SqlParameter("@par_sProduct", System.Data.SqlDbType.VarChar);
				sProduct.Value = par_sProduct;
				cmd.Parameters.Add(sProduct);

				reader = cmd.ExecuteReader();
				if (reader.Read())
				{
					sText = reader[0].ToString();
				}

				reader.Close();
				sqlConnection1.Close();
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sText;

		}

		public bool IsPrefixForProductXX(string par_sPageID)
		{
			//MI 11/12/09 Added OTH_GOALS.
			//MI 10/20/09 Added FRM.
			//MI 10/19/09 Added LBF.
			//MI 10/15/09 Created.
			//PURPOSE:
			//       Returns whether a page or page prefix (DSK_, VIE_, POP_, OTH_LINKS, LBF_, ...)
			//       should be created only under product XX or under individual 
			//       products (SA or MB). This is called from diaMetaDetails to warn the user
			//       when creating a page under a "wrong" product.
			//       If the page ID doesn't contain _ as 4th character or is otherwise invalid,
			//       False will be returned.
			//PARAMETERS:
			//       par_sPageID: complete metadata page ID or just the prefix (4 chars or more).
			//RETURNS:
			//       Boolean: True when the page should be created only under product XX,
			//           False otherwise.

			string sProc = "clMetaData::IsPrefixForProductXX";
			string sPageID;

			//  Try
			sPageID = par_sPageID.ToUpper();
				if (goTR.FromTo(sPageID, 4, 4) == "_")
				{
					//Page ID has a valid syntax prefix
					switch (sPageID.Substring(0, 3))
					{
						case "ALT":
						case "CRL":
						case "CRU":
						case "CUR":
						case "EXP":
						case "FII":
						case "FLD":
						case "FRM":
						case "IMP":
						case "LNK":
						case "LBF":
						case "LST":
						case "PGL":
						case "POP":
						case "PTR":
						case "SCR":
						case "SEC":
						case "SPC":
						case "WOP":
							return true;
					}
				}

				if (sPageID == "OTH_FIRSTNAMESUBSTITUTES")
				{
					return true;
				}
				if (sPageID == "OTH_LINKS")
				{
					return true;
				}
				if (sPageID == "OTH_FILES")
				{
					return true;
				}
				if (sPageID == "OTH_GOALS")
				{
					return true;
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return false;

		}

		public string IntlStrRead(string par_sMeta, string par_sParamName)
		{
			//MI 1/19/07 Replaced Chr(4) with clc.EOT

			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Returns a string containing all international values for the different supported languages
			//PARAMETERS:
			//		par_sMeta:		String of metadata in which we want to look for the information
			//		par_sParamName:	Name of the paramter we are looking for, with or without prefix (automatically 
			//						removed if present)
			//RETURNS:
			//		A string in the following format:
			//		US=USValue
			//		FR=FRValue... in the order given by goP:GetSupportedLangs
			//		if the IN parameter are wrong, will return at least US=+RC+FR=+RC
			//HOW IT WORKS:
			//		to be used when you need all translations of a given parameter
			//EXAMPLE:
			//		goMeta.IntlStrRead(sString, "NAME") returns: US=Companies+RC+FR=Compagnies+RC

			string sProc = "clMetaData::IntlStrRead";
			string sMeta = par_sMeta;
			string sParamName = par_sParamName.Trim(' ');
			string sSuppLangs = goP.GetSupportedLangs();
			string sResult = "";
			string sLang = null;
			int iI = 1;

			// Try
			//remove the language part of the parameter if there
			if (sParamName.Substring(2, 1) == "_")
			{
				sParamName = sParamName.Substring(3);
			}

				//write the return string
				do
				{
					sLang = goTR.ExtractString(sSuppLangs, iI);
					if (sLang[0] == clC.EOT)
					{
						break;
					}
					goTR.StrWrite(ref sResult, sLang, goTR.StrRead(sMeta, sLang + "_" + sParamName, "", false));
					iI = iI + 1;
				} while (true);

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try


			return sResult;

		}

		public bool IntlStrWrite(ref string par_sMeta, string par_sParamName, string par_sIntlString)
		{

			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Write in a meta string all the international lines for a parameter
			//		starting with a string in the format returned by IntlStrRead
			//PARAMETERS:
			//		par_sMeta:		String of metadata in which we want to write the information
			//		par_sParamName:	Name of the paramter we want to write, with or ithout prefix (automatically 
			//						removed if present). We need this name as the intl string contains only US=... & FR=...
			//		par_sIntlString:The intl content, in the format returned by the IntlStrRead:
			//						US=USValue+RC+FR=FRValue+RC+...
			//RETURNS:
			//		True if OK, false if not
			//HOW IT WORKS:
			//		to be used when you need to write all translations of a given parameter
			//EXAMPLE:
			//		goMeta.IntlStrWrite(sString, "NAME", sIntl)

			string sProc = "clMetaData::IntlStrWrite";
			string sParamName = par_sParamName.Trim(' ');
			string sIntl = par_sIntlString;
			string sOrder = goP.GetSupportedLangs();
			string sResult = "";
			string sLang = null;
			int iI = 1;

			// Try
			//remove the language part of the parameter if there
			if (sParamName.Substring(2, 1) == "_")
			{
				sParamName = sParamName.Substring(3);
			}

				//write the return string, returned via par_sMeta
				do
				{
					sLang = goTR.ExtractString(sOrder, iI);
					if (sLang == ((char)4).ToString())
					{
						break;
					}
					if (!goTR.StrWrite(ref par_sMeta, sLang + "_" + sParamName, goTR.StrRead(sIntl, sLang, "", false)))
					{
						return false;
					}
					iI = iI + 1;
				} while (true);

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return true;

		}

		public string IsNameDuplicate(string par_sPagePrefix, string par_sPageID, string par_sNameTrans, string par_sOneLangToSearch = "", string par_sProduct = "")
		{
			//MI 1/16/09 Added par_sProduct
			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Find the first duplicate Name of a _META page for a particular
			//       product in all languages or just the specified language.
			//		The search is case-insensitive so "Leads" and "LEADS" are 
			//		considered to be duplicates.
			//		If a duplicate is found, return its language, section, page, product.
			//		Duplicates are searched in all shared metadata of the same page prefix
			//       and same product code, and only for the logged-on user's local metadata.
			//PARMETERS:
			//		par_sPagePrefix: prefix with trailing underscore that describes the 
			//			type of metadata to search. Ex: DSK_, VIE_, VTM_, FLT_.
			//		par_sPageID: Page ID of the metadata object for which to find a duplicate.
			//		par_sNameTrans: Translated Name values in ini format:
			//			"US=value"+CR+"FR=value"+CR+...
			//		par_sOneLangToSearch (opt.): language code for which to search for a duplicate.
			//			If blank, search duplicate for all languages. This method will return
			//			only information on the first duplicate found, NOT on all duplicates!
			//       par_sProduct: product code of the page for which to look for duplicates.
			//RETURNS:
			//		If no duplicate found: ""
			//		If duplicate found: string in the following format:
			//			LANGUAGE=<XX> & vbCrLf
			//			SECTION=<TXT_Section> & vbCrLf
			//			PAGE=<TXT_Page> & vbCrLf
			//           PRODUCT=<TXT_Product> & vbCrLf
			//				where XX is a 2-character language code of the language in which
			//				the duplicate was found.
			//EXAMPLE:
			//		In Desktop designer window WDESKPRO Save button, call the following
			//		as part of the validation process:
			//       Dim s as string
			//       'Write the desktop name to s
			//       goTr.StrWrite(s, "US", TXT_Name.Text)
			//		IF goMeta.IsNameDuplicate("DSK_", gw.sDesktopID, s, , gop.GetProduct()) <> "" THEN
			//			'Duplicate found
			//		END

			string sProc = "clMetaData::IsNameDuplicate";
			string sPagePrefix = par_sPagePrefix;
			string sPageID = par_sPageID;
			string sOneLangToSearch = par_sOneLangToSearch.ToUpper();
			string sNameTrans = par_sNameTrans;
			string sNameToSearch = null;
			int iI = 1;
			string sReturn = "";

			//Try
			do
			{

					//setup sql connection
					//*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
					System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
					System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
					System.Data.SqlClient.SqlDataReader oReader = null;

					sNameToSearch = goTR.ExtractString(sNameTrans, iI, "\r\n");
					if (sNameToSearch == ((char)4).ToString())
					{
						break;
					}
					sNameToSearch = goTR.ExtractString(sNameToSearch, 2, "=");

					//sql command
					oCommand.CommandText = "pMetaGetDuplNames";
					oCommand.CommandType = CommandType.StoredProcedure;
					oCommand.Connection = oConnection;

					//parameter 
					System.Data.SqlClient.SqlParameter sPagePrefixParam = new System.Data.SqlClient.SqlParameter("@par_sPagePrefix", SqlDbType.VarChar);
					sPagePrefixParam.Value = sPagePrefix;
					oCommand.Parameters.Add(sPagePrefixParam);

					//parameter 
					System.Data.SqlClient.SqlParameter sPageParam = new System.Data.SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar);
					sPageParam.Value = sPageID;
					oCommand.Parameters.Add(sPageParam);

					//parameter 
					System.Data.SqlClient.SqlParameter sNameParam = new System.Data.SqlClient.SqlParameter("@par_sName", SqlDbType.NVarChar);
					sNameParam.Value = sNameToSearch;
					oCommand.Parameters.Add(sNameParam);

					//parameter 
					System.Data.SqlClient.SqlParameter sLanguageParam = new System.Data.SqlClient.SqlParameter("@par_sLanguage", SqlDbType.VarChar);
					sLanguageParam.Value = sOneLangToSearch;
					oCommand.Parameters.Add(sLanguageParam);

					//parameter 
					System.Data.SqlClient.SqlParameter sProduct = new System.Data.SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar);
					sProduct.Value = par_sProduct;
					oCommand.Parameters.Add(sProduct);

					//execute sproc

					oReader = oCommand.ExecuteReader();

					if (sOneLangToSearch != "")
					{

						//read returned value
						if (oReader.HasRows)
						{
							while (oReader.Read())
							{
								sReturn = sReturn + "LANGUAGE=" + oReader["TXT_Language"].ToString() + "\r\n";
								sReturn = sReturn + "SECTION=" + oReader["GID_Section"].ToString() + "\r\n";
								sReturn = sReturn + "PAGE=" + oReader["TXT_Page"].ToString() + "\r\n";
								sReturn = sReturn + "PRODUCT=" + oReader["TXT_PRODUCT"].ToString() + "\r\n";
								return sReturn;
							}
						}
						else
						{
							sReturn = "";
						}

					}
					else
					{

						string sLanguage = null;
						string sValue = null;

						//read returned value
						if (oReader.HasRows)
						{
							while (oReader.Read())
							{
								sLanguage = oReader["TXT_Language"].ToString().ToUpper();
								sValue = oReader["TXT_Value"].ToString().ToUpper();
								if (goTR.StrRead(sNameTrans, sLanguage).ToUpper() == sValue)
								{
									sReturn = sReturn + "LANGUAGE=" + oReader["TXT_Language"].ToString() + "\r\n";
									sReturn = sReturn + "SECTION=" + oReader["GID_Section"].ToString() + "\r\n";
									sReturn = sReturn + "PAGE=" + oReader["TXT_Page"].ToString() + "\r\n";
									sReturn = sReturn + "PRODUCT=" + oReader["TXT_PRODUCT"].ToString() + "\r\n";
									return sReturn;
								}
							}
						}
						else
						{
							sReturn = "";
						}

					}

					oReader.Close();
					oConnection.Close();

					iI = iI + 1;

				} while (true);

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sReturn;

		}

		public bool IsObjectShared(string par_sPageID, string par_sProduct = "")
		{
			//MI 2/3/09 Added par_sProduct.
			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//       Determines whether a metadata page is shared ("GLOBAL" section)
			//PARAMETERS:
			//		par_sPageID: ID of the metadata page
			//       par_sProduct: Product code (SA, MB, XX for 'all' products). If omitted,
			//           the product will be read from the current session. If still NULL,
			//           it will be set to 'SA'.
			//RESULT:
			//		Boolean

			string sProc = "clMetaData::IsObjectShared";
			//*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
			System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
			System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader oReader = null;
			bool bReturn = false;
			string sPage = par_sPageID;

			//Try

			//sql command
			oCommand.CommandText = "fnMetaIsPageShared";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter 
				System.Data.SqlClient.SqlParameter sPageParam = new System.Data.SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar);
				sPageParam.Value = sPage;
				oCommand.Parameters.Add(sPageParam);

				//parameter 
				System.Data.SqlClient.SqlParameter sProductParam = new System.Data.SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar);
				sProductParam.Value = par_sProduct;
				oCommand.Parameters.Add(sProductParam);

				//return parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Bit);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				//execute sproc

				oReader = oCommand.ExecuteReader();

				//Now you can grab the output parameter's value...
				bReturn = Convert.ToBoolean(retValParam.Value);

				oReader.Close();
				oConnection.Close();

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return bReturn;

		}

		private bool IsSupportedLanguage(string sLang)
		{

			//AUTHOR: 
			//       
			//PURPOSE:
			//		Converts a string value to a System.Guid type
			//PARAMETERS:
			//		par_sValue			= String to convert
			//RETURNS:
			//		System.Guid representation of string sent as parameter

			string sProc = "clMetaData::IsSupportedLanguage";
			string sSuppLangs = goP.GetSupportedLangs();
			string sSuppLang = "";
			int iI = 1;
			bool bResult = false;

			//Try

			do
			{
					sSuppLang = goTR.ExtractString(sSuppLangs, iI);
					if (sSuppLang == ((char)4).ToString())
					{
						break;
					}
					if (sLang == sSuppLang)
					{
						bResult = true;
						break;
					}
					iI = iI + 1;
				} while (true);

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return bResult;

		}

		public bool LineDelete(string par_sSection, string par_sPage, string par_sLine, string par_sProduct = "", bool par_bAllProducts = false)
		{
			//MI 1/16/09 Added par_sProduct.
			//MI 8/17/06 Added treating blank par_sSection treated as global.
			//7/14/06 MI Returning False if sProc returns <> 0.

			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Delete a line in a metadata page for a particular product.
			//PARAMETERS:
			//		par_sSection	=	Section in which the line must be deleted
			//		par_sPage		=	Page in which the line must be deleted
			//		par_sLine		=	Line/property to delete
			//       par_sProduct    =   Product code like 'SA', 'MB', 'WP', 'XX'.
			//                           Nothing and "" are interpreted as 'SA'.
			//                           XX is a code for pages that apply to 'all products'.
			//       par_bAllProducts=   When checked (default is unchecked), the line is
			//                           deleted for all products.
			//RETURNS:
			//		True if found and Deleted, false if not found (also OK, just nothing to delete)
			//HOW IT WORKS:
			//		Calls sProc pMetaLineDelete and gets return value
			//EXAMPLE:
			//       'Delete the 'Name' line in a global Contact form for product 'Mobile'
			//		goMeta.LineDelete("GLOBAL", "FRM_CN", "NAME", "MB")

			//==> Add error checking in sproc

			string sProc = "clMetaData::LineDelete";
			string sSection = par_sSection.Trim(' ').ToUpper();
			string sPage = par_sPage.Trim(' ').ToUpper();
			string sLine = par_sLine.Trim(' ').ToUpper();
			string sLang = "";
			int iResult = 0;

			try
			{

				//determine language settings, if the line provides language
				if (sLine.Substring(2, 1) == "_")
				{
					if (IsSupportedLanguage(sLine.Substring(0, 2)))
					{
						sLang = sLine.Substring(0, 2);
						sLine = sLine.Substring(3);
					}
				}

				//*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
				System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;

				oCommand.CommandText = "pMetaLineDelete";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter
				System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier);
				if (sSection == "GLOBAL" || sSection == "")
				{
					uSection.Value = System.DBNull.Value;
				}
				else
				{
					uSection.Value = StringToGuid(sSection);
				}
				oCommand.Parameters.Add(uSection);

				//parameter
				System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar);
				strPage.Value = sPage;
				oCommand.Parameters.Add(strPage);

				//parameter
				System.Data.SqlClient.SqlParameter strProperty = new System.Data.SqlClient.SqlParameter("@par_sProperty", SqlDbType.VarChar);
				strProperty.Value = sLine;
				oCommand.Parameters.Add(strProperty);

				//parameter
				System.Data.SqlClient.SqlParameter strLanguage = new System.Data.SqlClient.SqlParameter("@par_sLang", SqlDbType.Char);
				strLanguage.Value = sLang;
				oCommand.Parameters.Add(strLanguage);

				//parameter
				System.Data.SqlClient.SqlParameter strProduct = new System.Data.SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar);
				strProduct.Value = par_sProduct;
				oCommand.Parameters.Add(strProduct);

				//parameter
				System.Data.SqlClient.SqlParameter strAllProducts = new System.Data.SqlClient.SqlParameter("@par_bAllProducts", SqlDbType.Bit);
				strAllProducts.Value = par_bAllProducts;
				oCommand.Parameters.Add(strAllProducts);

				//return parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				//execute

				oReader = oCommand.ExecuteReader();

				//Now you can grab the output parameter's value...
				iResult = Convert.ToInt16(retValParam.Value);

				oReader.Close();
				oConnection.Close();

				if (iResult == 0)
				{
					return true;
				}
				else
				{
					return false; //but no error checking in sproc yet, so this should be never returned
				}

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return false;
			}

		}

		public string LineRead(string par_sSection, string par_sPage, string par_sLine, string par_sDefaultValue = "", bool par_bUserOnly = false, string par_sProduct = "")
		{
			//MI 10/29/09 Removed redundant vars that were set to parameter values.
			//MI 2/3/09 Fixed product parameter not being passed correctly to the sproc.
			//MI 1/16/09 Added par_sProduct.
			//MI 3/22/07 Changed Replace to goTr.Replace because it was returning Nothing!
			//MI 1/19/07 Added replacing Chr(1) & Chr(1) with vbcrlf
			//MI 8/17/06 Added treating blank par_sSection treated as global.
			//MI 5/26/06 Modified comments.
			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Read a low level parameter LINE, giving back the adequate value: Default/global/or user 
			//PARAMETERS:
			//		par_sSection		=	Section of the metadata page from which to read a property's value.
			//                               Can be 'GLOBAL' or user's GID as string. If not 'GLOBAL' and 
			//                               par_bUserOnly is False, the GLOBAL value is returned if the property
			//                               (par_sLine) is not found.
			//		par_sPage			=	Page in which the reading must be done.
			//		par_sLine			=	TXT_Property for which to read TXT_Value.
			//		par_sDefaultValue	= 	Default value to be returned if neither Section or GLOBAL line was found.
			//		par_bUserOnly		=	if TRUE, does not read the global values. Default is False.
			//       par_sProduct        =   TXT_Product for which to read TXT_Value. If empty or "", the code in current
			//                               SQL session will be used. If the session is not initialized, 'SA' will be used.
			//                               If the line doesn't exist for the specified product and it exists for product
			//                               'XX' (which applies to all products), that line will be returned. If this is not
			//                               desirable, use PageRead with par_bThisProductOnly=True, then read the line
			//                               with goTr.StrRead().
			//RETURNS:
			//		The value of TXT_Value for the given section/page/line/product.
			//HOW IT WORKS:
			//		calls udf fnMetaLineRead with adequate parameters (which performs the merging) and returns the result.
			//EXAMPLE:
			//		goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "COMPANYNAME") wil return the name of 
			//		the Company from workgroup options for the current product (this page happens to be stored
			//       under product 'XX' only, so the product substitution will occur.

			string sProc = "clMetaData::LineRead";
			//Dim sSection As String = UCase(Trim(par_sSection))     'MI 10/29/09 Commented
			//Dim sPage As String = UCase(Trim(par_sPage))     'MI 10/29/09 Commented
			//Dim sLine As String = UCase(Trim(par_sLine))     'MI 10/29/09 Commented
			//Dim sDefaultValue As String = par_sDefaultValue     'MI 10/29/09 Commented
			//Dim bUserOnly As Boolean = par_bUserOnly     'MI 10/29/09 Commented
			string sLang = "";

			par_sSection = par_sSection.Trim(' ').ToUpper();
			par_sPage = par_sPage.Trim(' ').ToUpper();
			par_sLine = par_sLine.Trim(' ').ToUpper();

			//determine language settings, if the line provides language
			if (par_sLine.Substring(2, 1) == "_")
			{
				if (IsSupportedLanguage(par_sLine.Substring(0, 2)))
				{
					sLang = par_sLine.Substring(0, 2);
					par_sLine = par_sLine.Substring(3);
				}
			}

			try
			{

				//*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection

				// using local variable. deprecated oMetaConnection member variable - MN 1/22/2018
				using (System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection())
				{
					System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
					// Dim oReader As SqlClient.SqlDataReader
					string sResult = "";

					 oCommand.CommandText = "fnMetaLineRead";
					oCommand.CommandType = CommandType.StoredProcedure;
					oCommand.Connection = oConnection;

					//parameter
					System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier);
					if (par_sSection == "GLOBAL" || par_sSection == "")
					{
						uSection.Value = System.DBNull.Value;
					}
					else
					{
						uSection.Value = StringToGuid(par_sSection);
					}
					oCommand.Parameters.Add(uSection);

					//parameter
					System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar);
					strPage.Value = par_sPage;
					oCommand.Parameters.Add(strPage);

					//parameter
					System.Data.SqlClient.SqlParameter strProperty = new System.Data.SqlClient.SqlParameter("@par_sProperty", SqlDbType.VarChar);
					strProperty.Value = par_sLine;
					oCommand.Parameters.Add(strProperty);

					//parameter
					System.Data.SqlClient.SqlParameter strDefaultValue = new System.Data.SqlClient.SqlParameter("@par_sDefaultValue", SqlDbType.NVarChar);
					strDefaultValue.Value = par_sDefaultValue;
					oCommand.Parameters.Add(strDefaultValue);

					//parameter
					System.Data.SqlClient.SqlParameter blnUserOnly = new System.Data.SqlClient.SqlParameter("@par_bUserOnly", SqlDbType.Bit);
					blnUserOnly.Value = par_bUserOnly;
					oCommand.Parameters.Add(blnUserOnly);

					//parameter
					System.Data.SqlClient.SqlParameter strLanguage = new System.Data.SqlClient.SqlParameter("@par_sLang", SqlDbType.VarChar);
					strLanguage.Value = sLang;
					oCommand.Parameters.Add(strLanguage);

					//parameter
					System.Data.SqlClient.SqlParameter strProduct = new System.Data.SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar);
					strProduct.Value = par_sProduct;
					oCommand.Parameters.Add(strProduct);

					//return parameter
					System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.NVarChar);
					retValParam.Direction = ParameterDirection.ReturnValue;
					oCommand.Parameters.Add(retValParam);

					//execute

					using (System.Data.SqlClient.SqlDataReader oReader = oCommand.ExecuteReader())
					{

						//Now you can grab the output parameter's value...
						sResult = Convert.ToString(retValParam.Value);
						sResult = goTR.Replace(sResult, (((char)1).ToString() + ((char)1).ToString()).ToString(), "\r\n".ToString());

						//oReader.Close()

					}
					//oConnection.Close()

					return sResult;
				}

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If

				return "";

			}

		}

		public string LinesToTSQL(string par_sSection, string par_sPage, string par_sMemo, bool par_bIncludePageDeletion = true, string par_sProduct = "")
		{
			//MI 3/23/09 Changed product in login section from par_sProduct to 'SA'.
			//MI 1/16/09 Added par_sProduct.
			//MI 5/23/07 Added par_bIncludePageDeletion parameter, added creating a section that allows deleting the page
			//MI 5/14/07 Created.
			//PURPOSE:
			//       Generate TSQL code that creates or edits a partial page of metadata from
			//       Metadata Details dialog page or a programmatically concatenated metadata "page".
			//       This is called from PageToTSQL.
			//       This doesn't check the validity of the section, page, or metadata string (par_sMemo).
			//       The Section can be blank, 'GLOBAL' or a valid User ID. If you pass an invalid ID
			//       the TSQL statement will generate a SQL error.
			//       Use this as a "metadata transfer out" facility for updating other databases
			//       with new or modified metadata. 

			string sProc = "clMetaData::LinesToTSQL";
			string sPage = null;
			string sTemp = null;
			string sResult = "";
			string sProperty = null;
			string sValue = null;
			int i = 0;
			int iPos = 0;
			string sLang = "";
			string sProduct = par_sProduct;

			if (sProduct == "")
			{
				sProduct = "SA";
			}

			sPage = par_sMemo;
			if (sPage.Trim(' ') == "")
			{
				return "";
			}

			//Build the static portion of the script
			//We create a login session under product SA regardless of the product under which
			//the lines should be created because the product could be 'XX', which login
			//doesn support, and because the 'transfer in' should be done under SA.
			sResult = "-------------------------------------------------------------------------" + "\r\n" + "-- TSQL script: create/modify metadata page '" + par_sPage + "' in section '" + par_sSection + "' for product '" + par_sProduct + "'." + "\r\n" + "\r\n" + "-- First initialize a user session in SS" + "\r\n" + "DECLARE @uUserID uniqueidentifier" + "\r\n" + "DECLARE @sUserCode varchar(4)" + "\r\n" + "DECLARE @uSection uniqueidentifier" + "\r\n" + "DECLARE @iResult int" + "\r\n" + "-- Optional: to assign a specific creator to the metadata, modify the user ID and code below." + "\r\n" + "-- This is mandatory only for shared pages so that a specific user with partial author permission" + "\r\n" + "-- can edit the page." + "\r\n" + "SELECT @uUserID = CAST('********-3EC5-48F7-5553-987900B57A11' as uniqueidentifier)" + "\r\n" + "SELECT @sUserCode = 'SYST'" + "\r\n" + "EXEC pInitSession @par_uUserID = @uUserID, @par_sUserCode = @sUserCode, @par_sProduct = 'SA' " + "\r\n" + "\r\n";

			if (par_sSection == "GLOBAL" || par_sSection == "")
			{
				sResult += "SET @uSection = NULL" + "\r\n" + "\r\n";
			}
			else
			{
				sResult += "SET @uSection = CAST('" + par_sSection + "' as uniqueidentifier)" + "\r\n" + "\r\n";
			}

			//Build the page deletion portion
			if (par_bIncludePageDeletion)
			{
				sResult += "-- Delete the page" + "\r\n" + "-- *** Comment out this section to update the page instead of replacing it. ***" + "\r\n" + "PRINT 'Deleting page ''" + par_sPage + "'' for product ''" + par_sProduct + "'' in section ''' + CAST(@uSection as varchar(40)) + ''''" + "\r\n" + "EXEC @iResult = pMetaPageDelete @par_uSection = @uSection, @par_sPage = '" + par_sPage + "', @par_sProduct = '" + sProduct + "'" + "\r\n" + "IF @iResult <> 0 PRINT '@iResult: ''' + CAST(@iResult AS varchar(20)) + ''''" + "\r\n" + "\r\n";
			}
			else
			{
				sResult += "-- Deleting the page is not an option because this script may be" + "\r\n" + "-- creating only a partial page. To create a complete page, click Generate SQL Script in " + "\r\n" + "-- Author>Browse Metadata menu." + "\r\n" + "\r\n";
			}

			//Build the line-writing script
			sResult += "-- Modify the page" + "\r\n";
			i = 1;
			do
			{
				sTemp = goTR.ExtractString(sPage, i, "\r\n");
				//No more lines, exit the loop
				if (sTemp[0] == clC.EOT)
				{
					break;
				}
				//Skip blank rows
				if (sTemp == "")
				{
					goto ProcessNext;
				}
				//Extract property
				iPos = sTemp.IndexOf("=") + 1;
				if (iPos < 1) //Invalid line (no '='), skip it
				{
					goto ProcessNext;
				}
				sProperty = goTR.FromTo(sTemp, 1, iPos - 1);
				//Extract language out of property if appropriate
				if (goTR.FromTo(sProperty, 3, 3) == "_")
				{
					if (IsSupportedLanguage(goTR.FromTo(sProperty, 1, 2)))
					{
						sLang = goTR.FromTo(sProperty, 1, 2);
						sProperty = goTR.FromTo(sProperty, 4, -1);
					}
					else
					{
						sLang = "";
					}
				}
				else
				{
					sLang = "";
				}
				sValue = goTR.PrepareForSQL(goTR.FromTo(sTemp, iPos + 1, -1));
				par_sPage = goTR.PrepareForSQL(par_sPage);
				sProperty = goTR.PrepareForSQL(sProperty);
				sLang = goTR.PrepareForSQL(sLang);
				sResult += "EXEC @iResult = pMetaLineWrite @par_uSection = @uSection, @par_sPage = '" + par_sPage + "', @par_sProperty = '" + sProperty + "', @par_sValue = '" + sValue + "', @par_sLanguage = '" + sLang + "', @par_sProduct = '" + sProduct + "'" + "\r\n" + "IF @iResult <> 0 PRINT 'Error ' + CAST(@iResult AS varchar(100))" + "\r\n";
	ProcessNext:
				i = i + 1;
			} while (true);

			sResult += "GO" + "\r\n";

			return sResult;
		}


		public bool LineWrite(string par_sSection, string par_sPage, string par_sLine, object par_vValue, ref System.Data.SqlClient.SqlConnection par_oConnection, string par_sCreatorID)
		{
			return LineWrite(par_sSection, par_sPage, par_sLine, par_vValue, ref par_oConnection, par_sCreatorID, "");
		}

		public bool LineWrite(string par_sSection, string par_sPage, string par_sLine, object par_vValue, ref System.Data.SqlClient.SqlConnection par_oConnection)
		{
			return LineWrite(par_sSection, par_sPage, par_sLine, par_vValue, ref par_oConnection, "", "");
		}

		public bool LineWrite(string par_sSection, string par_sPage, string par_sLine, object par_vValue)
		{
			System.Data.SqlClient.SqlConnection tempVar = null;
			return LineWrite(par_sSection, par_sPage, par_sLine, par_vValue, ref tempVar, "", "");
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Public Function LineWrite(ByVal par_sSection As String, ByVal par_sPage As String, ByVal par_sLine As String, ByVal par_vValue As Object, Optional ByRef par_oConnection As SqlClient.SqlConnection = Nothing, Optional ByVal par_sCreatorID As String = "", Optional ByVal par_sProduct As String = "") As Boolean
		public bool LineWrite(string par_sSection, string par_sPage, string par_sLine, object par_vValue, ref System.Data.SqlClient.SqlConnection par_oConnection, string par_sCreatorID, string par_sProduct)
		{
			//MI 10/29/09 Removed redundant vars that were set to parameter values.
			//MI 1/30/09 added testing oConnection before closing it.
			//MI 1/16/09 Added par_sProduct.
			//MI 8/9/07 Added par_sCreatorID
			//MI 1/19/07 Disabled because it was causing errors: Added replacing CRs with Chr(1) & Chr(1)
			//MI 11/20/06 Removed trimming par_vValue
			//MI 8/17/06 Added treating blank section as GLOBAL.
			//MI 5/10/06 Trimming parameters
			//MI 5/5/06
			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Writes a line in a metadata page.
			//       ************* CAREFUL, READ WARNING BELOW! ************
			//       WARNING: Be careful which product you write metadata under. If you are using
			//       this method in the normal 'Sales' product context and you are writing a line that
			//       belongs to an application-defining page such as DSK, VIE, SND, etc, you needn't worry about the
			//       product parameter - all metadata is written under the SA product. However, if you are
			//       writing a line for a page that applies to all products (where TXT_Product in the MD
			//       table is 'XX' - for example OTH_LINKS, FLD_, CRL_, CRU, POP, WOP, and other metadata that
			//       is common to all products), you ***MUST*** specify 'XX' or you will create an 'SA' 
			//       product version of the page (with just the line you wrote). This will cause PageRead 
			//       to return just your new line under that product and in the case of OTH_LINKS, or FLD_ pages, 
			//       this may cause errors that can prevent the site from loading.
			//       ********************************************************
			//PARAMETERS:
			//		par_sSection	=	Section in which the line must be deleted
			//		par_sPage		=	Page in which the line must be deleted
			//		par_sLine		=	Line/property to delete
			//       par_sValue      =   Value to write for this line
			//       par_oConnection =   Optional SQL connection passed by PageWrite
			//       par_sCreatorID  =   GID_ID of the 'creator' User. This is needed for goMeta.PageWrite(),
			//                           which currently deletes the whole page od MD first, then
			//                           writes line by line. If this is a page edit, the creator must
			//                           remain the same. The creator is considered for limited author
			//                           permissions on GLOBAL metadata.
			//       par_sProduct    =   Product code under which to write the line. If Nothing or "",
			//                           the product in the current session will be used. If undefined
			//                           in the session, 'SA' (stored in the DB as NULL) will be used.
			//RETURNS:
			//		True if successful write, false if not.
			//HOW IT WORKS:
			//		Calls sProc pMetaLineWrite and gets return value.
			//EXAMPLE:
			//		goMeta.LineWrite("GLOBAL", "FRM_CN", "NAME", "Contact Form")

			string sProc = "clMetaData::LineWrite";
			//Dim sSection As String = Trim(UCase(par_sSection))      'MI 10/29/09 Commented
			//Dim sPage As String = Trim(par_sPage)      'MI 10/29/09 Commented
			//Dim sLine As String = Trim(par_sLine)      'MI 10/29/09 Commented
			string sValue = par_vValue.ToString(); //Trim(par_vValue.ToString)
			string sLang = "";
			int iResult = 0;

			par_sSection = par_sSection.ToUpper().Trim(' ');
			par_sPage = par_sPage.Trim(' ');
			par_sLine = par_sLine.Trim(' ');

			//Replace CRs with two characters 1
			//MI 3/22/07 Replaced Replace(), which can return Nothing with goTr.Replace(), which returns ""
			//sValue = goTr.Replace(sValue, vbCrLf, Chr(1) & Chr(1))

			//determine language settings, if the line provides language
			if (par_sLine.Substring(2, 1) == "_")
			{
				if (IsSupportedLanguage(par_sLine.Substring(0, 2)))
				{
					sLang = par_sLine.Substring(0, 2);
					par_sLine = par_sLine.Substring(3);
				}
			}

			//Try

			System.Data.SqlClient.SqlConnection oConnection = null;
				if (par_oConnection == null)
				{
					oConnection = goData.GetConnection();
				}
				else
				{
					oConnection = par_oConnection;
				}

				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;

				oCommand.CommandText = "pMetaLineWrite";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter
				System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier);
				if (par_sSection == "GLOBAL" || par_sSection == "")
				{
					uSection.Value = System.DBNull.Value;
				}
				else
				{
					uSection.Value = StringToGuid(par_sSection);
				}
				oCommand.Parameters.Add(uSection);

				//parameter
				System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar);
				strPage.Value = par_sPage;
				oCommand.Parameters.Add(strPage);

				//parameter
				//DEBUG
				//If par_sLine = Nothing Then Stop
				//If par_sLine = "" Then Stop
				//END DEBUG
				System.Data.SqlClient.SqlParameter strProperty = new System.Data.SqlClient.SqlParameter("@par_sProperty", SqlDbType.VarChar);
				strProperty.Value = par_sLine;
				oCommand.Parameters.Add(strProperty);

				//parameter
				System.Data.SqlClient.SqlParameter strValue = new System.Data.SqlClient.SqlParameter("@par_sValue", SqlDbType.NVarChar);
				strValue.Value = sValue;
				oCommand.Parameters.Add(strValue);

				//parameter
				System.Data.SqlClient.SqlParameter strLanguage = new System.Data.SqlClient.SqlParameter("@par_sLanguage", SqlDbType.Char);
				if (sLang != "")
				{
					strLanguage.Value = sLang;
				}
				else
				{
					strLanguage.Value = System.DBNull.Value;
				}
				oCommand.Parameters.Add(strLanguage);

				//parameter
				System.Data.SqlClient.SqlParameter uCreatorID = new System.Data.SqlClient.SqlParameter("@par_uCreatorID", SqlDbType.UniqueIdentifier);
				if (par_sCreatorID == null || par_sCreatorID == "")
				{
					uCreatorID.Value = System.DBNull.Value;
				}
				else
				{
					uCreatorID.Value = StringToGuid(par_sCreatorID);
				}
				oCommand.Parameters.Add(uCreatorID);

				//parameter
				System.Data.SqlClient.SqlParameter strProduct = new System.Data.SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar);
				strProduct.Value = par_sProduct;
				oCommand.Parameters.Add(strProduct);

				//return parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				//execute
				oReader = oCommand.ExecuteReader();

				//Now you can grab the output parameter's value...
				iResult = Convert.ToInt16(retValParam.Value);

				oReader.Close();
				if (par_oConnection == null)
				{
					if (oConnection != null) //MI 1/30/09 added testing oConnection
					{
						oConnection.Close();
					}
				}

				if (iResult == 0)
				{
					return true;
				}
				else
				{
					switch (iResult)
					{
						case -1: //when fnGetMe returns '' User ID
							goErr.SetWarning(35503, sProc);
							break;
							//MessTranslate 35503: Metadata line couldn't be written because fnGetMe returned an invalid user ID.
						case -2: //when @par_sUserCode is ''
							goErr.SetWarning(35504, sProc);
							break;
							//MessTranslate 35504: Metadata line couldn't be written because fnGetMe returned an invalid user code.
						case -3: //when @par_sUserCode is ''
							goErr.SetWarning(35000, sProc, "Error writing metadata line: product '" + par_sProduct.ToString() + "' is invalid or, if the product is blank, the product written in the SQL session via pInitSession is invalid.");
							break;
							//MessTranslate
					}
					return false;
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

		}

		public bool PageDelete(string par_sSection, string par_sPageID, string par_sProduct = "")
		{
			//MI 1/16/09 Added par_sProduct.
			//MI 8/17/06 Added treating blank par_sSection as global.
			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Delete the metadata record (page). If multiple pages exist with the same
			//		section and page ID, this method deletes all such pages.
			//PARAMETERS:
			//		par_sSection:   "GLOBAL" or User's TID
			//		par_sPageID:    page ID of the page, typically in the <PREFIX>_<TID> format,
			//						for example: DSK_xxxxxxxx (desktop) or SCR_xxxxxxx (script)
			//       par_sProduct:   Product code like 'SA', 'MB', 'WP' for which the page will
			//                       be deleted. If Nothing or "", product will be taken from
			//                       the current session. If still undefined, 'SA' will be assumed.
			//                       'XX' is a code that stands for 'all products'.
			//RETURNS:
			//		*** Always true. *** In NGP it was True/False:
			//			False: if the page couldn't be found or deletion failed.
			//			True: page was found and the deletion succeeded.
			//EXAMPLE:
			//		sSection = goP.GetUserTID()
			//		sPageID = "SCR_xxxxxxxxxxxxxxxxxxxxxx"
			//       'Pop a message box and code the following in BTN_MsgBox1_Click:
			//		goMeta.PageDelete(sSection, sPageID, sProduct)

			string sProc = "clMetaData::PageDelete";
			string sSection = par_sSection.ToUpper().Trim(' ');
			string sPage = par_sPageID.ToUpper().Trim(' ');
			int iResult = 0;

			try
			{

				//*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
				System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;

				oCommand.CommandText = "pMetaPageDelete ";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter
				System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier);
				if (sSection == "GLOBAL" || sSection == "")
				{
					uSection.Value = System.DBNull.Value;
				}
				else
				{
					uSection.Value = StringToGuid(sSection);
				}
				oCommand.Parameters.Add(uSection);

				//parameter
				System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar);
				strPage.Value = sPage;
				oCommand.Parameters.Add(strPage);

				//parameter
				System.Data.SqlClient.SqlParameter strProduct = new System.Data.SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar);
				strProduct.Value = par_sProduct;
				oCommand.Parameters.Add(strProduct);

				//return parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				//execute

				oReader = oCommand.ExecuteReader();

				//Now you can grab the output parameter's value...
				iResult = Convert.ToInt16(retValParam.Value);

				oReader.Close();
				oConnection.Close();

				if (iResult == 0)
				{
					return true;
				}
				else
				{
					return false; //no error checking in sproc yet
				}

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return false;
			}

		}

		public string PageRead(string par_sSection, string par_sPage, string par_sDefaultPage = "", bool par_bUserOnly = false, string par_sProduct = "", bool par_bThisProductOnly = false)
		{
			//MI 10/29/09 Commented Dim sDefaultPage As String = par_sDefaultPage.
			//MI 2/6/09 Removed Trim() from oReader("TXT_Value").ToString - this was corrupting
			//           values that start with spaces. For example, a NAME '   aaaa' was returned
			//           as 'aaaa', causing the desktop to be duplicated or transferred out
			//           with a different name.
			//MI 1/15/09 Added par_sProduct.
			//MI 12/4/08 Improved comments.
			//MI 3/27/07 Added default Page evaluation
			//MI 5/5/06  Added allowing a blank par_sSection (treated as GLOBAL)

			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Read a PAGE of metadata, merging automatically DEFAULT, GLOBAL and USER information (if asked for).
			//PARAMETERS:
			//		par_sSection	= 	Section value (MetaData is organized in Sections, Pages and Lines).
			//           Supported values are user GID_IDs and "GLOBAL". If section is "", it is treated as "GLOBAL".
			//		par_sPage		= 	Page name (generally composed by a prefix (DSK_) and a SUID.
			//           Some page names for which there is no possibility of duplicates generated on multiple
			//           replicated servers are manually assigned, for ex OTH_LINKS, FLD_AC or FRM_CN.
			//           These pages are typically system-defining and are not user-editable.
			//           For most user-created pages, generate an ID by using a support prefix (see clDefaults.
			//           GetMetaTypeFromPrefix) plus "_" plus a valid SUID generated with goData.GenSUID("XX").
			//		par_sDefaultPage=	Default page. This whole page will be returned if nothing is found
			//           in the page we are seeking. If some lines are found, their values will override values
			//           in the default page.
			//           Remember that if par_bUserOnly is False (default), and par_sSection is not GLOBAL,
			//           the user page will be merged with the GLOBAL page first. In case of duplicate values,
			//           the lines of the user page will win. The engine will then perform the default page
			//           merging. To make sure that user/GLOBAL merging doesn't occur, set par_bUserOnly to True.
			//		par_bUserOnly	=	If TRUE, read only the page for par_sSection, and do not merge it
			//           with the GLOBAL page. If par_sDefaultPage is not blank, the merging with the default page
			//           will still occur.
			//       par_sProduct: Product code like 'SA', 'MB', 'WP'. XX is a code for 'all products' pages.
			//           If Nothing or "", product will be taken from the session. If still undefined, 'SA'
			//           will be used. 
			//       par_bThisProductOnly: When set to 1 (0 is default), merging with XX product page is suppressed.
			//RETURNS:
			//		A string containing the PAGE of metadata in a quasi-ini format (sections are not supported).
			//HOW IT WORKS:
			//		Reads in user and global section and merge the information together, then merge with the default.
			//		If you don't want to merge with Global, set par_bUserOnly to TRUE
			//		if you don't want to merge with default, set par_sDefaultPage to ""
			//EXAMPLE:
			//		goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS") 'returns workgroup options for the product
			//                                                           'of the current logon session.

			//==> Support par_sProduct!

			string sProc = "clMetaData:PageRead";
			string sSection = par_sSection.Trim(' ').ToUpper(); //Transform the possibly numerical value into a string
			string sPage = par_sPage.Trim(' ').ToUpper(); //Transform the possibly numerical value into a string
			long lRec = 0;
			//MI 10/29/09 Commenting because doesn't appear to be needed
			//Dim sDefaultPage As String = par_sDefaultPage
			bool bUserOnly = par_bUserOnly;

			//A blank section is 'global'
			if (sSection == "")
			{
				sSection = "GLOBAL";
			}

			//Blank page - return empty string
			if (sPage == "")
			{
				return "";
			}

			//V_T 4/8/2015 
			// **** Caching *****      

			bool iscachable = clCache.IsCachable(sSection, sPage);
			if (iscachable)
			{
				string cachedData;
				cachedData = Convert.ToString(clCache.GetItemFromCache(clCache.MakeCacheKey(sSection, sPage)));
				if (cachedData != null)
				{
					return cachedData;
				}
			}

			// **** Caching *****

			//setup sql connection
			// using local variable. deprecated oMetaConnection member variable - MN 1/22/2018
			using (System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection())
			{
				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				//Dim oReader As SqlClient.SqlDataReader
				string sReturn = "";

				try
				{

					oCommand.CommandText = "pMetaPageRead";
					oCommand.CommandType = CommandType.StoredProcedure;
					oCommand.Connection = oConnection;

					//parameter 
					System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier);
					if (sSection == "GLOBAL")
					{
						uSection.Value = System.DBNull.Value;
					}
					else
					{
						uSection.Value = StringToGuid(sSection);
					}
					oCommand.Parameters.Add(uSection);

					//parameter 
					System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar);
					strPage.Value = sPage;
					oCommand.Parameters.Add(strPage);

					//parameter 
					System.Data.SqlClient.SqlParameter iUserOnly = new System.Data.SqlClient.SqlParameter("@par_bUserOnly", SqlDbType.Bit);
					iUserOnly.Value = Convert.ToInt16(bUserOnly);
					oCommand.Parameters.Add(iUserOnly);

					//parameter 
					System.Data.SqlClient.SqlParameter strProduct = new System.Data.SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar);
					strProduct.Value = par_sProduct;
					oCommand.Parameters.Add(strProduct);

					//parameter 
					System.Data.SqlClient.SqlParameter strThisProductOnly = new System.Data.SqlClient.SqlParameter("@par_bThisProductOnly", SqlDbType.Bit);
					strThisProductOnly.Value = Convert.ToInt16(par_bThisProductOnly);
					oCommand.Parameters.Add(strThisProductOnly);

					//execute sproc
					using (System.Data.SqlClient.SqlDataReader oReader = oCommand.ExecuteReader())
					{


						//read returned value
						if (oReader.HasRows)
						{
							while (oReader.Read())
							{
								string sKey = oReader["TXT_Property"].ToString().Trim(' '); //0
								string sLang = oReader["TXT_Language"].ToString().Trim(' '); //2
								//Dim sProd As String = Trim(oReader("TXT_Product").ToString)     '1
								string sVal = oReader["TXT_Value"].ToString(); //3 'MI 2/6/09 Removed Trim(), was corrupting values that start with space
								if (sLang != "")
								{
									sKey = sLang + "_" + sKey;
								}
								sReturn = sReturn + sKey + "=" + sVal + "\r\n";
							}
						}
						else
						{
							sReturn = "";
						}
						sReturn = goTR.MergeIniStrings(sReturn, par_sDefaultPage);

						//V_T 4/8/2015
						if (iscachable)
						{
							clCache.AddtoCache(clCache.MakeCacheKey(sSection, sPage), sReturn);
						}


					}
					//oConnection.Close()

				}
				catch (Exception ex)
				{

					//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
					//    goErr.SetError(ex, 45105, sProc)
					//End If

					sReturn = "";

				}

				return sReturn;
			}

		}

		public string PageToTSQL(string par_sSection, string par_sPage, string par_sProduct = "")
		{
			//MI 1/15/09 Added par_sProduct.
			//MI 5/14/07 modified to call LinesToTSQL.
			//MI 5/1/07 Created.
			//PURPOSE:
			//       Generate TSQL code that creates or edits a page of metadata.
			//       Use this as a "metadata transfer out" facility for updating other databases
			//       with new or modified metadata. 

			string sProc = "clMetaData::PageToTSQL";
			string sPage;

			sPage = goMeta.PageRead(par_sSection, par_sPage, "", true, par_sProduct);
			if (sPage.Trim(' ') == "")
			{
				return "";
			}

			return this.LinesToTSQL(par_sSection, par_sPage, sPage, true, par_sProduct);

		}
		public bool PageWrite(string par_sSection, string par_sPage, string par_sVal, string par_sFriendlyName = "", string par_sCreatorID = "", string par_sProduct = "")
		{
			//MI 4/20/12 changed "NAME" to "US_NAME" and conditionalized writing friendly name so it's not written twice.
			//MI 10/29/09 Removed redundant vars that are set to parameter values.
			//MI 1/19/09 Added par_sProduct.
			//MI 11/16/07 Removed par_sTextValue parameter. Watch it!
			//MI 8/9/07 Added sending creator ID to LineWrite
			//MI 1/10/07 Reversed change below.
			//MI 1/10/07 Changed par_sFriendlyName to be written as US_NAME, not NAME.
			//MI 8/17/06 Added treating blank par_sSection treated as global.
			//MI 7/17/06 Fixing bug.
			//7/14/06 MI Added raising errors
			//MI 5/10/06 Re-wrote the loop to accommodate blank lines. Added checking for invalid lines (no '=').
			//MI 5/5/06 Added PageDelete, debugging

			//==> par_bLocal is currently ignored until (if) replication becomes supported.

			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Write a metadata Page identified by a section and Page
			//PARAMETERS:
			//		par_sSection		= Metadata can be global, affecting all users, or local, affecting a single user.
			//                           Supported values: "GLOBAL" for global metadata or user's GID_ID for local metadata.
			//                           In PageRead, if a property is not found in a local page, by default it is sought
			//                           in the GLOBAL page.
			//		par_sPage			= Unique identifier of a 'page' of metadata properties. Ex:
			//                               DSK_1988EE04-2DCA-4E0F-5858-98C000DEEF31
			//                               POP_PERSONAL_OPTIONS
			//                               OTH_DATETIMEADJUSTMENT
			//                               OTH_FILES
			//		par_sVal			= Ini string consisting of property=value pairs. To be valid, a property must
			//                           be case-insensitive (will be returned by PageRead or LineRead as all-caps),
			//                           not include any spaces and not have an underscore in position 3 (unless 
			//                           prepended by a lanaguage code). To define a property for a particular language,
			//                           prepend it with a valid language code and underscore. Ex: US_PROPERTY= or 
			//                           FR_PROPERTY=. *** Language substitutions are not supported yet! ***
			//		par_sFriendlyName	= Friendly Name of the page that is stored in NAME= property.
			//       par_sCreatorID      = GID_ID of the 'creator' User. Use this parameter to give a limited author
			//                           permission to edit GLOBAL MD pages. The original creator, if also a limited author,
			//                           will not be able to edit the page anymore. Creator ID is not used for allowing
			//                           non-authors to see and edit non-GLOBAL pages - the section is.
			//		par_bLocal			= *** REMOVED ***  In NGP, if true (false was the default), the page was saved as local
			//                           (SI__ShareState=0) instead of Protected (SI__ShareState=1, which is how all
			//                           non-shared records get saved in Sales 5.0) when the section was the user ID
			//                           (i.e., not shared). It was still saved as SI__ShareState=2 (shared) when 
			//                           the section was GLOBAL.
			//		par_sTextValue		= *** REMOVED *** in NGP was the value to store in TXT_TextValue 
			//                           (used normally only from the wfMeta window). Optional parameter.
			//       par_sProduct        = Product code under which to write the page. If not defined, product will be
			//                           evaluated from the session. If not defined in the session, it will be set to 'SA'.
			//RETURNS:
			//		TRUE or FALSE depending of the result of the writing
			//HOW IT WORKS:
			//		First try to read the record identified by Section+Page, then make a hAdd or 
			//		hModify depending if it has been previously found or not
			//EXAMPLE:
			//		goMeta.PageWrite("GLOBAL","IMP_2002022711241928000MAI 00001XX",sTemplate,"Contacts from OLD Selltis")

			string sProc = "clMetaData::PageWrite";
			bool bLocal = false; //was = par_bLocal in NGP
			//Dim sSection As String = UCase(Trim(par_sSection))     'MI 10/29/09 commented
			//Dim sPage As String = UCase(Trim(par_sPage))     'MI 10/29/09 commented
			//Dim sTextValue As String = par_sTextValue
			//Dim sVal As String = par_sVal     'MI 10/29/09 commented
			string sFriendlyName = par_sFriendlyName;
			int iCRPos = 0;
			string sLine = null;
			string sProp = null;
			string sValue = null;
			int iCount = 1;
			int iEqualPos = 0;
			bool bResult = true;
			string sCreatorID = "";
			bool bUSNameWritten = false;

			par_sPage = par_sPage.Trim(' ').ToUpper(); //MI 10/29/09 added
			par_sSection = par_sSection.Trim(' ').ToUpper(); //MI 10/29/09 added

			if (par_sPage == "")
			{
				goErr.SetError(35506, sProc,"", par_sSection, par_sPage, par_sFriendlyName, bLocal.ToString(), par_sVal, "Value:      '" + par_sProduct + "'");
				//35506: PageWrite was called with a blank page parameter. Metadata page can't be written.
				// 
				//Section:    '[1]'
				//Page:       '[2]'
				//Friendly(Name) '[3]'
				//Local:      '[4]'
				//Value:      '[5]'
				//[6]
				return false;
			}

			//Add the final CR if missing
			if (par_sVal.Substring(par_sVal.Length - 2) != "\r\n")
			{
				par_sVal += "\r\n";
			}

			//Get the creator ID
			sCreatorID = this.GetCreatorID(par_sSection, par_sPage, par_sProduct);

			//---------- TEST WRITING TO SS ---------
			//Test whether we can write a test line without errors.
			//This is needed because the whole page will be deleted next, before lines are
			//written. If the writing of the lines fails, the page will be lost or incomplete.
			System.Data.SqlClient.SqlConnection tempVar = null;
			if (!LineWrite("GLOBAL", "OTH_TEST", "clMetaData.PageWrite", "Test", ref tempVar, sCreatorID, par_sProduct))
			{
				goErr.SetError(35505, sProc, "", par_sSection, par_sPage + " product: '" + par_sProduct + "'");
				//35505: Metadata page couldn't be written. Make sure that the server is running and that the ASP user has adequate permissions on it.
				// 
				//Section:    '[1]'
				//Page:       '[2]'
				return false;
			}

			//------------ DELETE PAGE -----------
			//Run pMetaPageDelete to start with a clean slate, otherwise we
			//have to go PageRead first and delete the properties one by one
			if (!PageDelete(par_sSection, par_sPage, par_sProduct))
			{
				goErr.SetError(35500, sProc, "", par_sSection, par_sPage, "Product: '" + par_sProduct + "'");
				//35500: Deleting a metadata page failed. Section: '[1]', page: '[2]'. [3]
				return false;
			}

			//==> Replace this later with reading the whole page and comparing lines, then
			//updating the changed lines and deleting the deleted lines.
			//This will be faster and will preserve the ModTime of unchanged lines, which
			//are all being wiped out now. OR MAYBE THEY SHOULD ALL BE UPDATED?

			try
			{

				System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
				iCount = 1;
				iEqualPos = 0;
				do
				{
					//Get the line
					sLine = goTR.ExtractString(par_sVal, iCount, "\r\n");
					//Skip blank line
					if (sLine == "")
					{
						goto BottomOfLoop;
					}
					//clC.EOT indicates end of loop
					if (sLine[0] == clC.EOT)
					{
						break;
					}

					iEqualPos = sLine.IndexOf("=") + 1;
					if (iEqualPos < 1)
					{
						//Line doesn't have an =. It may be a comment?
						goErr.SetWarning(35501, sProc, "", sLine);
						//35501: Invalid metadata line (no '='): '[1]'. [2]
						goto BottomOfLoop;
					}

					sProp = goTR.FromTo(sLine, 1, iEqualPos - 1);
					sValue = goTR.FromTo(sLine, iEqualPos + 1, -1);

					//Skip blank property
					if (sProp.Trim(' ') == "")
					{
						goto BottomOfLoop;
					}

					if (!LineWrite(par_sSection, par_sPage, sProp, sValue, ref oConnection, sCreatorID, par_sProduct))
					{
						bResult = false;
						//MI 4/20/12 Added, writing US_NAME failed, write it below if friendly name parameter <> ""
						if (sProp.ToUpper() == "US_NAME")
						{
							bUSNameWritten = false;
						}
						goErr.SetWarning(35502, sProc, "", par_sSection, par_sPage, sProp, sValue, "Product: '" + par_sProduct + "'");
						//35502: Metadata line failed to be written.
						//Section:            '[1]'
						//Page:               '[2]'
						//Property: '[3]'
						//Value: '[4]'
						//[5]
					}
					else
					{
						//MI 4/20/12 Added
						if (sProp.ToUpper() == "US_NAME")
						{
							bUSNameWritten = true;
						}
					}

	BottomOfLoop:
					iCount = iCount + 1;
				} while (true);

				//Write the 'friendly' name if not already written
				if (sFriendlyName != "")
				{
					//MI 4/20/12 Added test to avoid writing US_NAME twice
					if (!bUSNameWritten)
					{
						//MI 4/20/12 changed "NAME" to "US_NAME" to 
						if (!LineWrite(par_sSection, par_sPage, "US_NAME", sFriendlyName, ref oConnection, sCreatorID, par_sProduct))
						{
							bResult = false;
							goErr.SetWarning(35502, sProc, "", par_sSection, par_sPage, "US_NAME", sFriendlyName, "Product: '" + par_sProduct + "'");
							//35502: Metadata line failed to be written.
							//Section:            '[1]'
							//Page:               '[2]'
							//Property: '[3]'
							//Value: '[4]'
							//[5]
						}
					}
				}

				oConnection.Close();

				bool iscachable = clCache.IsCachable(par_sSection, par_sPage);
				if (iscachable)
				{
					clCache.ClearCache();
				}

				if (bResult)
				{
					return true;
				}
				else
				{
					return false;
				}

			}
			catch (Exception ex)
			{

				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If

				return false;

			}

		}

		private System.Guid StringToGuid(string sValue)
		{

			//AUTHOR: 
			//       
			//PURPOSE:
			//		Converts a string value to a System.Guid type
			//PARAMETERS:
			//		par_sValue			= String to convert
			//RETURNS:
			//		System.Guid representation of string sent as parameter

			System.Guid guidValue = Guid.TryParse(sValue, out guidValue) ? guidValue : Guid.Empty;
            return guidValue;

		}

		public bool TransferIn(string par_sMetadata, string par_sPageID, bool par_bAsNew, bool par_bShared, string par_sName, ref string par_sMessage, ref string par_sCreatedOrEditedPageID)
		{
			return TransferIn(par_sMetadata, par_sPageID, par_bAsNew, par_bShared, par_sName, ref par_sMessage, ref par_sCreatedOrEditedPageID, "");
		}

		public bool TransferIn(string par_sMetadata, string par_sPageID, bool par_bAsNew, bool par_bShared, string par_sName, ref string par_sMessage)
		{
			string tempVar = "";
			return TransferIn(par_sMetadata, par_sPageID, par_bAsNew, par_bShared, par_sName, ref par_sMessage, ref tempVar, "");
		}

		public bool TransferIn(string par_sMetadata, string par_sPageID, bool par_bAsNew, bool par_bShared, string par_sName)
		{
			string tempVar = "";
			string tempVar2 = "";
			return TransferIn(par_sMetadata, par_sPageID, par_bAsNew, par_bShared, par_sName, ref tempVar, ref tempVar2, "");
		}

		public bool TransferIn(string par_sMetadata, string par_sPageID, bool par_bAsNew, bool par_bShared)
		{
			string tempVar = "";
			string tempVar2 = "";
			return TransferIn(par_sMetadata, par_sPageID, par_bAsNew, par_bShared, "", ref tempVar, ref tempVar2, "");
		}

		public bool TransferIn(string par_sMetadata, string par_sPageID, bool par_bAsNew)
		{
			string tempVar = "";
			string tempVar2 = "";
			return TransferIn(par_sMetadata, par_sPageID, par_bAsNew, false, "", ref tempVar, ref tempVar2, "");
		}

		public bool TransferIn(string par_sMetadata, string par_sPageID)
		{
			string tempVar = "";
			string tempVar2 = "";
			return TransferIn(par_sMetadata, par_sPageID, true, false, "", ref tempVar, ref tempVar2, "");
		}

		public bool TransferIn(string par_sMetadata)
		{
			string tempVar = "";
			string tempVar2 = "";
			return TransferIn(par_sMetadata, "", true, false, "", ref tempVar, ref tempVar2, "");
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Public Function TransferIn(ByVal par_sMetadata As String, Optional ByVal par_sPageID As String = "", Optional ByVal par_bAsNew As Boolean = 1, Optional ByVal par_bShared As Boolean = False, Optional ByVal par_sName As String = "", Optional ByRef par_sMessage As String = "", Optional ByRef par_sCreatedOrEditedPageID As String = "", Optional ByVal par_sProduct As String = "") As Boolean
		public bool TransferIn(string par_sMetadata, string par_sPageID, bool par_bAsNew, bool par_bShared, string par_sName, ref string par_sMessage, ref string par_sCreatedOrEditedPageID, string par_sProduct)
		{
			//MI 1/25/10 Changing CHART view type to LIST for MB product.
			//MI 2/4/09 For DSK metadata transferred in to Mobile product, added goTR.MoveDesktopViewsToCol1(sPart(i)).
			//MI 1/27/09 WARNING: I commented MessageBox below Try/Catch section. This may cause
			//       a bug where there is no desktop definition to resurface. If the desktop
			//       is not defined, the user may think that the tfr in succeeded when nothing
			//       happened.
			//MI 1/26/09 Added par_sProduct.
			//MI 12/22/08 Fix: sOrigDesktopViewID wasn't tested if is nothing and was causing NullReferenceException error.
			//MI 8/18/08 Fix: index out of bounds error when the desktop definition is not properly delimited.
			//MI 11/13/07
			//PURPOSE:
			//       Transfer into metadata one or more pages from a string generated by
			//       Transfer Out.
			//CAUTION:
			//       Creating new pages should not be supported for bulk metadata
			//       transfers because each dependent page (such as VIE_) will be assigned
			//       a new page ID and the references to VIE_ IDs in a DSK_ page (VIEW3ID=,
			//       for example) will not be changed, corrupting the desktop.
			//PARAMETERS:
			//       par_sMetadata: Metadata string created with Transfer Out.
			//       par_sPageID: If only one page (and dependent pages) is to be
			//           transferred in, this is the page ID or the prefix of the page to transfer in.
			//           Example: 'DSK_' will cause the first DSK_ page definition found
			//           and its dependent VIE_ pages to be transferred in. 'SND_" will cause
			//           the first Send Template page to be transferred in. If there are additional
			//           pages, they will be ignored.
			//           If a complete page ID is passed, the first page with a matching 
			//           page ID will be transferred in.
			//           A valid prefix has 3 characters followed by "_". 
			//           The definition needn't be the first object in the transfer out 
			//           string, but all of its dependent objects must be found or False 
			//           will be returned.
			//           To transfer in multiple unrelated pages, leave this parameter blank.
			//       par_bAsNew: If True (default), the page(s) will be transferred in as
			//           new metadata objects, with new page IDs. Be careful when transferring
			//           in metadata pages that don't use a TID, for example 'POP_PERSONAL_OPTIONS'
			//           because they will be assigned a page ID like 
			//           'OTH_301c2e2f-6f4b-4a25-5858-99e50101a302'. If False, par_bShared is
			//           ignored.
			//       par_bShared: Ignored if par_bAsNew if False.
			//           If True (False is default), the pages will be created as GLOBAL
			//           if par_bAsNew is true or a GLOBAL existing page will be replaced if it
			//           already exists. If False, the page will be created as local or a local
			//           page will be replaced. If the user doesn't have adequate permission,
			//           False will be returned with par_sMessage. 
			//       par_sName: If par_sPageID is not empty and par_bAsNew is True, 
			//           you can provide a US language name of the main object to transfer in via
			//           this parameter. In the future this parameter may be used to provide
			//           the name in multiple languages in an ini format of:
			//               US=US Name
			//               FR=French Name
			//               GR=German Name
			//           If the name is provided in the US language only, the string must not
			//           have hard returns. If there are dependent objects, their names will
			//           remain the same. This is because dependent objects typically are not
			//           exposed on their own (for example views are always featured in 
			//           a desktop context).
			//       par_sMessage: Return parameter for validation messages.
			//       par_sCreatedOrEditedPageID: A new Page ID for a new metadata object
			//           or the actual page ID edited. par_sPageID may contain just "DSK_"
			//           because the full page ID of the desktop is not known when this method
			//           is called. This parameter returns the actual desktop page ID, whether
			//           generated here if par_bAsNew is true, or read from par_sMetadata.
			//       par_sProduct: Product code (SA, MB, WP, XX for 'all') under which to transfer
			//           in the page. If Nothing or "", the current session product will be used.
			//           Note that the ###PRODUCT line in transfer out metadata is ignored.
			//RETURNS:
			//       Boolean: True if successful, False if not. if a validation failed,
			//       a message about it is returned via par_sMessage ByRef parameter.
			//       If all validation passed, but the transfer in was not successful,
			//       a warning is raised. Test goErr.GetLastError() for warnings.

			string sProc = "clMetadata::TransferIn";
			//Dim bResult As Boolean = True      'MI 8/18/08 commented out
			bool bResult = false; //MI 8/18/08 added
			string[] sPart = null;
			string[] sPartSection = null;
			string[] sPartPageID = null;
			string[] sPartProduct = null;
			string[] sMiniView = null;
			string[] sMiniViewPageID = null;
			string[] sTabView = null;
			string[] sTabViewPageID = null;
			string sTemp = null;
			string sPage = null;
			int iPos = 0;
			string sMDType = null;
			int i = 0;
			int j = 0;
			int k = 0;
			int iLength = 0;
			string sNewDesktopID = "";
			string sOrigDesktopPage = "";
			string[] sOrigDesktopViewID = null;
			string sProduct = par_sProduct;
			string sViewName = null;

			if (sProduct == "")
			{
				sProduct = goP.GetProduct();
			}

			//Try
			//----------- This code generates transfer out metadata in clMetaData.TransferOutMarkup --------------
			//This is here for reference only - do not uncomment!
			//goTR.StrWrite(sPageInfo, "###TYPE", goDef.GetMetaTypeFromPrefix(par_sPageID))
			//sTemp = goTR.StrRead(par_sString, "US_NAME", , False)
			//If sTemp = "" Then sTemp = goTR.StrRead(par_sString, "NAME")
			//goTR.StrWrite(sPageInfo, "###NAME", sTemp)
			//goTR.StrWrite(sPageInfo, "###SECTION", par_sSectionID)
			//goTR.StrWrite(sPageInfo, "###PAGE", par_sPageID)
			//goTR.StrWrite(sPageInfo, "###PRODUCT", par_sProduct)
			//If Right(sPageInfo, 2) <> vbCrLf Then sPageInfo &= vbCrLf
			//s = "---***^^^BEGIN PAGE^^^***---" & vbCrLf
			//s &= sPageInfo
			//s &= par_sString & vbCrLf
			//s &= "---***^^^END PAGE^^^***---" & vbCrLf & vbCrLf

			//----------- Check user's permission ----------------
			if (!(goP.GetMe("PERMTRANSFERIN") == "1"))
			{
					par_sMessage = "You don't have permission to transfer in application objects.";
					return false;
				}
				if (par_bShared)
				{
					if (goP.IsUserAuthor() < 1)
					{
						//Use is neither full nor partial author
						par_sMessage = "You don't have permission to share the object(s) you are transferring in.";
						return false;
					}
				}

				//Get the name of the metadata object being transferred in
				if (par_sPageID == null)
				{
					par_sPageID = "";
				}
				par_sPageID = par_sPageID.ToUpper();
				if (par_sPageID.Length == 3)
				{
					par_sPageID += "_";
				}
				if (par_sPageID == "")
				{
					sMDType = "object";
				}
				else
				{
					sMDType = goDef.GetMetaTypeFromPrefix(par_sPageID);
				}


				//---------------- VALIDATE --------------
				//Name
				if (par_bAsNew)
				{
					if (par_sName == "")
					{
						par_sMessage = goTR.MessComplete("Please enter a descriptive name for the [1] you are transferring in.", sMDType);
						return false;
					}
				}

				//Page definition valid?
				sPart = Microsoft.VisualBasic.Strings.Split(par_sMetadata, "---***^^^BEGIN PAGE^^^***---");
				if (sPart.GetLength(0) < 2)
				{
					//No valid object
					par_sMessage = goTR.MessComplete("Metadata does not contain a single valid [1] definition. Please paste valid transfer out metadata.", sMDType);
					return false;
				}

				//-------------- EXTRACT PAGES --------------
				//Go through definitions in the metadata and extract individual pages
				iLength = sPart.Length - 1;
				for (i = 1; i <= iLength; i++)
				{
					//End Page delimiter in place?
					sTemp = sPart[i]; //0-based index: 0 element contains text before the first BEGIN PAGE delimiter line
					iPos = sTemp.IndexOf("---***^^^END PAGE^^^***---") + 1;
					if (iPos < 1)
					{
						par_sMessage = "The end of definition " + i.ToString() + " is not properly delimited. Please use valid transfer out metadata.";
						return false;
					}

					//Chop off the vbcrlf after BEGIN PAGE and the END PAGE delimiting strings
					sPage = goTR.FromTo(sTemp, 3, iPos - 1);
					Array.Resize(ref sPartPageID, i + 1);
					sPartPageID[i] = goTR.StrRead(sPage, "###PAGE", "", false).ToUpper();
					Array.Resize(ref sPartSection, i + 1);
					sPartSection[i] = goTR.StrRead(sPage, "###SECTION", "", false).ToUpper();
					Array.Resize(ref sPartProduct, i + 1);
					sPartProduct[i] = goTR.StrRead(sPage, "###PRODUCT", "", false).ToUpper();
					//Remove markup lines
					goTR.StrDelete(ref sPage, "###TYPE");
					goTR.StrDelete(ref sPage, "###NAME");
					goTR.StrDelete(ref sPage, "###SECTION");
					goTR.StrDelete(ref sPage, "###PAGE");
					goTR.StrDelete(ref sPage, "###PRODUCT");
					//Now we have clean MD pages in sPart(i)
					sPart[i] = sPage;
					//Is the section valid?
					if (sPartSection[i] != "" && sPartSection[i] != "GLOBAL")
					{
						if (!goTR.IsTID(sPartSection[i]))
						{
							par_sMessage = "Definition " + i.ToString() + " doesn't have a valid section defined. Please use valid transfer out metadata.";
							return false;
						}
					}
					//Does the page at least have a prefix like 'DSK_' or 'VIE_'?
					if (goTR.FromTo(sPartPageID[i], 4, 4) != "_")
					{
						par_sMessage = "Definition " + i.ToString() + " doesn't have a valid page ID defined. Please use valid transfer out metadata.";
						return false;
					}
				}

				//-------------- PROCESS PAGE(S) -----------
				if (par_sPageID != "")
				{
					//We are running in the 'single page' mode.
					//Find the first definition of the page of the given prefix
					//Transfer in only one page (and dependent pages)
					//For i = 1 To sPartPageID.Length()      'MI 8/18/08 commented
					for (i = 0; i < sPartPageID.Length; i++) //MI 8/18/08 added
					{
						if (sPartPageID[i].Substring(0, par_sPageID.Length) == par_sPageID)
						{
							//Found the definition - it's in sPart(i)
							switch (par_sPageID.Substring(0, 4))
							{
								case "DSK_":
									//-------- Desktop and referenced views ----------
									//Desktop has dependent views so we process it differently than other types of pages
									if (par_bAsNew)
									{
										//Generate a new desktop ID
										sNewDesktopID = "DSK_" + goData.GenerateID("XX");
										par_sCreatedOrEditedPageID = sNewDesktopID;
									}
									else
									{
										par_sPageID = sPartPageID[i];
										par_sCreatedOrEditedPageID = par_sPageID;
										sOrigDesktopPage = goMeta.PageRead(sPartSection[i], sPartPageID[i], "", true, sPartProduct[i], true);
										//Load IDs of the original desktop's views
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of goTR.StringToNum(goTR.StrRead(sOrigDesktopPage, "VIEWCOUNT", "0", false), "0") for every iteration:
										int tempVar3 = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sOrigDesktopPage, "VIEWCOUNT", "0", false), "0"));
										for (j = 1; j <= tempVar3; j++)
										{
											Array.Resize(ref sOrigDesktopViewID, j + 1);
											sOrigDesktopViewID[j] = (goTR.StrRead(sOrigDesktopPage, "VIEW" + goTR.NumToString(j, "0") + "ID", null, false)).ToUpper();
										}
										k = j - 1;
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of goTR.StringToNum(goTR.StrRead(sOrigDesktopPage, "TABCOUNT", "0", false), "0") for every iteration:
										int tempVar4 = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sOrigDesktopPage, "TABCOUNT", "0", false), "0"));
										for (j = 1; j <= tempVar4; j++)
										{
											Array.Resize(ref sOrigDesktopViewID, (j + k) + 1);
											sOrigDesktopViewID[j + k] = (goTR.StrRead(sOrigDesktopPage, "TAB" + goTR.NumToString(j, "0") + "VIEWID", null, false)).ToUpper();
										}
									}
									//------- Views --------
									//Read all the desktop's views
									Array.Resize(ref sMiniViewPageID, 1);
									Array.Resize(ref sMiniView, 1);
									Array.Resize(ref sTabViewPageID, 1);
									Array.Resize(ref sTabView, 1);
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of goTR.StringToNum(goTR.StrRead(sPart(i), "VIEWCOUNT", "0", false), "0") for every iteration:
									int tempVar5 = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sPart[i], "VIEWCOUNT", "0", false), "0"));
									for (j = 1; j <= tempVar5; j++)
									{
										//View page ID
										Array.Resize(ref sMiniViewPageID, j + 1);
										Array.Resize(ref sMiniView, j + 1);
										sMiniViewPageID[j] = (goTR.StrRead(sPart[i], "VIEW" + goTR.NumToString(j, "0") + "ID", null, false)).ToUpper();
										//Find this view's metadata in sPart array
										for (k = 1; k <= sPartPageID.GetUpperBound(0); k++)
										{
											if (sPartPageID[k] == sMiniViewPageID[j])
											{
												sMiniView[j] = sPart[k];
												break;
											}
										}
										if (sMiniView[j] == "")
										{
											//View page not found
											par_sMessage = "There is no definition for mini view " + j.ToString() + " in desktop '" + goTR.StrRead(sPart[i], "NAME") + "' (defined in slot " + i.ToString() + "). Please use valid transfer out metadata.";
											return false;
										}
										if (par_bAsNew)
										{
											//Modify some properties
											goTR.StrWrite(ref sMiniView[j], "DESKTOPID", sNewDesktopID);
											goTR.StrWrite(ref sMiniView[j], "CREATORID", goP.GetUserTID());
											//MI 1/25/10 Turn CHART views to LIST under MB product. This is the best compromise for now
											//that prevents errors when opening such views in MB. If charts get supported in MB, 
											//this should be removed.
											if (sProduct != "SA")
											{
												if (goTR.StrRead(sMiniView[j], "TYPE", "", false) == "CHART")
												{
													goTR.StrWrite(ref sMiniView[j], "TYPE", "LIST");
													//Append '(chart not supported)' to the view name
													sViewName = goTR.StrRead(sMiniView[j], "US_NAME", "", false);
													if (sViewName == "")
													{
														sViewName = goTR.StrRead(sMiniView[j], "NAME", "", false);
													}
													if (sViewName == "")
													{
														goTR.StrWrite(ref sMiniView[j], "US_NAME", "(chart not supported)");
													}
													else
													{
														goTR.StrWrite(ref sMiniView[j], "US_NAME", sViewName + " (chart not supported)");
													}
												}
											}
											//Generate a new view page ID
											sMiniViewPageID[j] = "VIE_" + goData.GenerateID("XX");
										}
										else
										{
											//Remove the view from sOrigDesktopViewID, which we will use later to delete views
											//that are not in the transferred in metadata
											if (sOrigDesktopViewID != null)
											{
												for (k = 1; k <= sOrigDesktopViewID.GetUpperBound(0); k++)
												{
													if (sOrigDesktopViewID[k] == sMiniViewPageID[j])
													{
														sOrigDesktopViewID[k] = "";
													}
												}
											}
										}
									}
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of goTR.StringToNum(goTR.StrRead(sPart(i), "TABCOUNT", "0", false), "0") for every iteration:
									int tempVar6 = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sPart[i], "TABCOUNT", "0", false), "0"));
									for (j = 1; j <= tempVar6; j++)
									{
										//Tab view page ID
										Array.Resize(ref sTabViewPageID, j + 1);
										Array.Resize(ref sTabView, j + 1);
										sTabViewPageID[j] = (goTR.StrRead(sPart[i], "TAB" + goTR.NumToString(j, "0") + "VIEWID", null, false)).ToUpper();
										//Find this tab view's metadata in sPart array
										for (k = 1; k <= sPartPageID.GetUpperBound(0); k++)
										{
											if (sPartPageID[k] == sTabViewPageID[j])
											{
												sTabView[j] = sPart[k];
												break;
											}
										}
										if (sTabView[j] == "")
										{
											//View page not found
											par_sMessage = "There is no definition for tab view " + j.ToString() + " in desktop '" + goTR.StrRead(sPart[i], "NAME") + "' (defined in slot " + i.ToString() + "). Please use valid transfer out metadata.";
											return false;
										}
										if (par_bAsNew)
										{
											//Modify some properties
											goTR.StrWrite(ref sTabView[j], "DESKTOPID", sNewDesktopID);
											goTR.StrWrite(ref sTabView[j], "CREATORID", goP.GetUserTID());
											//MI 1/25/10 Turn CHART views to LIST under MB product. This is the best compromise for now
											//that prevents errors when opening such views in MB. If charts get supported in MB, 
											//this should be removed.
											if (sProduct != "SA")
											{
												if (goTR.StrRead(sTabView[j], "TYPE", "", false) == "CHART")
												{
													goTR.StrWrite(ref sTabView[j], "TYPE", "LIST");
													//Append '(chart not supported)' to the view name
													sViewName = goTR.StrRead(sTabView[j], "US_NAME", "", false);
													if (sViewName == "")
													{
														sViewName = goTR.StrRead(sTabView[j], "NAME", "", false);
													}
													if (sViewName == "")
													{
														goTR.StrWrite(ref sTabView[j], "US_NAME", "(chart not supported)");
													}
													else
													{
														goTR.StrWrite(ref sTabView[j], "US_NAME", sViewName + " (chart not supported)");
													}
												}
											}
											//Generate a new view page ID
											sTabViewPageID[j] = "VIE_" + goData.GenerateID("XX");
										}
										else
										{
											//Remove the view from sOrigDesktopViewID, which we will use later to delete views
											//that are not in the transferred in metadata
											if (sOrigDesktopViewID != null)
											{
												for (k = 1; k <= sOrigDesktopViewID.GetUpperBound(0); k++)
												{
													if (sOrigDesktopViewID[k] == sTabViewPageID[j])
													{
														sOrigDesktopViewID[k] = "";
													}
												}
											}
										}
									}
									//--------- Modify view IDs in the desktop page ---------------
									//Modify VIEWnID= and TABnVIEWID= properties in the DSK_ page 
									//to reference new VIE_ page IDs.
									if (par_bAsNew)
									{
										for (j = 1; j <= sMiniViewPageID.GetUpperBound(0); j++)
										{
											goTR.StrWrite(ref sPart[i], "VIEW" + j.ToString() + "ID", sMiniViewPageID[j]);
										}
										for (j = 1; j <= sTabViewPageID.GetUpperBound(0); j++)
										{
											goTR.StrWrite(ref sPart[i], "TAB" + j.ToString() + "VIEWID", sTabViewPageID[j]);
										}
									}
									//--------- Create/edit the desktop ----------
									//Move all views to column 1 for Mobile
									if (sProduct == "MB") //MI 2/4/09 added
									{
										sPart[i] = goTR.MoveDesktopViewsToCol1(sPart[i]);
									}
									if (par_bAsNew)
									{
										if (!TransferInOnePage(sPart[i], sNewDesktopID, par_bAsNew, par_bShared, par_sName, sPartSection[i], ref par_sMessage, sProduct))
										{
											return false;
										}
									}
									else
									{
										if (!TransferInOnePage(sPart[i], par_sPageID, par_bAsNew, par_bShared, par_sName, sPartSection[i], ref par_sMessage, sProduct))
										{
											return false;
										}
									}
									//---------- Create/edit the views -----------
									iLength = sMiniView.GetUpperBound(0);
									for (j = 1; j <= iLength; j++)
									{
										if (!TransferInOnePage(sMiniView[j], sMiniViewPageID[j], par_bAsNew, par_bShared, goTR.StrRead(sMiniView[j], "NAME"), sPartSection[i], ref par_sMessage, sProduct))
										{
											return false;
										}
									}
									iLength = sTabView.GetUpperBound(0);
									for (j = 1; j <= iLength; j++)
									{
										if (!TransferInOnePage(sTabView[j], sTabViewPageID[j], par_bAsNew, par_bShared, goTR.StrRead(sTabView[j], "NAME"), sPartSection[i], ref par_sMessage, sProduct))
										{
											return false;
										}
									}
									//----------- Delete the views not in the transfer in metadata ------------
									if (!par_bAsNew)
									{
										if (sOrigDesktopViewID != null)
										{
											for (j = 1; j <= sOrigDesktopViewID.GetUpperBound(0); j++)
											{
												if (sOrigDesktopViewID[j] != "")
												{
													goMeta.PageDelete(sPartSection[i], sOrigDesktopViewID[j], sPartProduct[i]);
												}
											}
										}
									}
									break;
								default:
									//Non-desktop page - process without worrying about dependent pages
									//Create/edit the page
									if (par_bAsNew)
									{
										if (!TransferInOnePage(sPart[i], goTR.GetPrefix(par_sPageID), par_bAsNew, par_bShared, par_sName, sPartSection[i], ref par_sMessage, sProduct))
										{
											return false;
										}
									}
									else
									{
										if (!TransferInOnePage(sPart[i], par_sPageID, par_bAsNew, par_bShared, par_sName, sPartSection[i], ref par_sMessage, sProduct))
										{
											return false;
										}
									}
									break;
							}
							//We are done - we created/edited the page we were looking for
							//MI 1/27/09 Commented out the following because it doesn't make any sense
							//'If we didn't get here because we didn't find the main definition      'MI 8/18/08 added
							//'or any definitions, bResult will be false.      'MI 8/18/08 added
							//bResult = True      'MI 8/18/08 added 
							break;
						}
					}
				}
				else
				{
					//Transfer in all pages - this is bulk transfer. See 'CAUTION' in comments above.
					for (i = 1; i <= sPartPageID.GetUpperBound(0); i++)
					{
						//Create/edit the page
						if (par_bAsNew)
						{
							if (!TransferInOnePage(sPart[i], goTR.GetPrefix(sPartPageID[i]), par_bAsNew, par_bShared, par_sName, sPartSection[i], ref par_sMessage, sProduct))
							{
								return false;
							}
						}
						else
						{
							if (!TransferInOnePage(sPart[i], sPartPageID[i], par_bAsNew, par_bShared, par_sName, sPartSection[i], ref par_sMessage, sProduct))
							{
								return false;
							}
						}
					}
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			//MI 1/27/09 Commented out the following because it doesn't make any sense
			//We return False in case of any failed calls to TransferInOnePage. That means that if we get here
			//we have processed everything successfully. It may be that a case where there is no DSK definition
			//at all is not caught within the For loop earlier, but in that case tests have to be done there.
			//'bResult is False if no definitions got processed.      'MI 8/18/08 added
			//par_sMessage = "There is no valid desktop definition. Please use valid transfer out metadata."      'MI 8/18/08 added
			return true;
		}

		private bool TransferInOnePage(string par_sMetadata, string par_sPageID, bool par_bAsNew, bool par_bShared, string par_sName, string par_sSection, ref string par_sMessage)
		{
			return TransferInOnePage(par_sMetadata, par_sPageID, par_bAsNew, par_bShared, par_sName, par_sSection, ref par_sMessage, "");
		}

		private bool TransferInOnePage(string par_sMetadata, string par_sPageID, bool par_bAsNew, bool par_bShared, string par_sName, string par_sSection)
		{
			string tempVar = "";
			return TransferInOnePage(par_sMetadata, par_sPageID, par_bAsNew, par_bShared, par_sName, par_sSection, ref tempVar, "");
		}

		private bool TransferInOnePage(string par_sMetadata, string par_sPageID, bool par_bAsNew, bool par_bShared, string par_sName)
		{
			string tempVar = "";
			return TransferInOnePage(par_sMetadata, par_sPageID, par_bAsNew, par_bShared, par_sName, "", ref tempVar, "");
		}

		private bool TransferInOnePage(string par_sMetadata, string par_sPageID, bool par_bAsNew, bool par_bShared)
		{
			string tempVar = "";
			return TransferInOnePage(par_sMetadata, par_sPageID, par_bAsNew, par_bShared, "", "", ref tempVar, "");
		}

		private bool TransferInOnePage(string par_sMetadata, string par_sPageID, bool par_bAsNew)
		{
			string tempVar = "";
			return TransferInOnePage(par_sMetadata, par_sPageID, par_bAsNew, false, "", "", ref tempVar, "");
		}

		private bool TransferInOnePage(string par_sMetadata, string par_sPageID)
		{
			string tempVar = "";
			return TransferInOnePage(par_sMetadata, par_sPageID, true, false, "", "", ref tempVar, "");
		}

		private bool TransferInOnePage(string par_sMetadata)
		{
			string tempVar = "";
			return TransferInOnePage(par_sMetadata, "", true, false, "", "", ref tempVar, "");
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Private Function TransferInOnePage(ByVal par_sMetadata As String, Optional ByVal par_sPageID As String = "", Optional ByVal par_bAsNew As Boolean = 1, Optional ByVal par_bShared As Boolean = False, Optional ByVal par_sName As String = "", Optional ByVal par_sSection As String = "", Optional ByRef par_sMessage As String = "", Optional ByVal par_sProduct As String = "") As Boolean
		private bool TransferInOnePage(string par_sMetadata, string par_sPageID, bool par_bAsNew, bool par_bShared, string par_sName, string par_sSection, ref string par_sMessage, string par_sProduct)
		{
			//MI 1/26/09 Added par_sProduct.
			//MI 10/9/08 Added allowing full authors to transfer in local pages regardless of who's the author
			//PURPOSE:
			//       Called from TransferIn to write a single metadata page.
			//       CAUTION: Creating new pages should not be supported for bulk metadata
			//       transfers because each dependent page (such as VIE_) will be assigned
			//       a new page ID and the references to VIE_ IDs in a DSK_ page (VIEW3ID=,
			//       for example) will not be changed, corrupting the desktop.
			//PARAMETERS:
			//       par_sMetadata: Page metadata.
			//       par_sPageID As String = Page ID or page prefix only (4 chars like 'DSK_'
			//           or 'SND_' if par_bAsNew is true. CAREFUL: If the prefix is <= 4 chars, the ID
			//           will be generated in this method, otherwise the ID that is passed via
			//           this parameter will be used for the _new_ page. 
			//       par_bAsNew As Boolean = Create a new page. If unchecked, replace existing page.
			//       par_bShared As Boolean = Create a shared page. Ignored if par_bAsNew is False.
			//       par_sName As String = Create new page with this US_Name value.
			//       par_sSection As String = Page Section. If blank, user's ID is used
			//           as the section.
			//       par_sMessage = return parameter containing a message in case of failure.
			//       par_sProduct: Product code (SA, MB, WP, XX for 'all'). If nothing or "",
			//           the current session product will be used.
			//RETURNS:
			//       True when successful, marioimotherwise False either with par_sMessage set or
			//       goTr.SetWarning() raised.

			string sProc = "clMetadata::TransferInOnePage";
			string sProduct = par_sProduct;
			if (sProduct == null || sProduct == "")
			{
				sProduct = goP.GetProduct();
			}
			// Try
			//------------- Permissions -------------------
			if (goP.GetMe("PERMTRANSFERIN") != "1")
			{
					par_sMessage = "You don't have permission to transfer in application objects.";
					return false;
				}

				if (par_sSection == "")
				{
					par_sSection = goP.GetUserTID();
				}
				par_sSection = par_sSection.ToUpper();

				//------------ Write the page --------------
				if (par_bAsNew)
				{
					//Create a new page
					//Edit name, tab label and creator ID in the metadata
					if (par_sName != "")
					{
						goTR.StrWrite(ref par_sMetadata, "US_NAME", par_sName);
						if (par_sMetadata.IndexOf("US_WCLABEL=") + 1 > 0)
						{
							//For Desktops, write the Name in web client label property, if in the metadata
							goTR.StrWrite(ref par_sMetadata, "US_WCLABEL ", par_sName);
						}
					}
					goTR.StrWrite(ref par_sMetadata, "CREATORID", goP.GetUserTID());
					//----- Generate Page ID if not passed to this method -----
					if (par_sPageID.Length <= 4) //4 is the length of a valid page prefix (e.g. "VIE_" or "DSK_")
					{
						if (par_sPageID.Length < 4)
						{
							//Invalid prefix
							par_sMessage = "Error: metadata page prefix is invalid or missing: '" + par_sPageID + "'. Writing page '" + par_sPageID + "' failed.";
							return false;
						}
						else
						{
							//A prefix was passed, generate a new ID
							par_sPageID = par_sPageID + goData.GenerateID("XX");
						}
					}
					else
					{
						//Since the page ID is > 4 characters, we can assume that a valid page ID was passed in par_sPageID
					}
					if (par_bShared)
					{
						//Shared - only authors are allowed to create
						if (par_sMetadata.IndexOf("SHARED=") + 1 > 0)
						{
							//Write Shared only if already in metadata
							goTR.StrWrite(ref par_sMetadata, "SHARED", "1");
						}
						//Is the user author?
						if (goP.IsUserAuthor() < 1)
						{
							par_sMessage = "You don't have permission to create new shared pages. Writing page '" + par_sPageID + "' failed.";
							return false;
						}
						else
						{
							if (!goMeta.PageWrite("GLOBAL", par_sPageID, par_sMetadata, "", "", sProduct))
							{
								par_sMessage = "An error occurred writing global page '" + par_sPageID + "' for product '" + sProduct + "'. If the problem persists, please contact Selltis support.";
								return false;
							}
						}
					}
					else
					{
						//Unshared - all users are allowed to create
						if (par_sMetadata.IndexOf("SHARED=") + 1 > 0)
						{
							goTR.StrWrite(ref par_sMetadata, "SHARED", "0");
						}
						if (!goMeta.PageWrite(goP.GetUserTID(), par_sPageID, par_sMetadata, "", "", sProduct))
						{
							par_sMessage = "An error occurred writing page '" + par_sPageID + "' (section '" + par_sSection + "', product '" + sProduct + "'). If the problem persists, please contact Selltis support.";
							return false;
						}
					}
				}
				else
				{
					//Edit an existing page if found, or create a new one
					if (par_sSection == "" || par_sSection == "GLOBAL")
					{
						//GLOBAL page, only full authors and partial authors who are creators can edit the page
						switch (goP.IsUserAuthor())
						{
							case 2:
							break;
								//Full author - always OK
							case 1:
								//Limited author
								if (goMeta.GetCreatorID(par_sSection, par_sPageID, sProduct) != goP.GetUserTID())
								{
									par_sMessage = "You are not allowed to overwrite shared metadata that you didn't create because you only have a limited author permission. Section: '" + par_sSection + "' page: '" + par_sPageID + "' product: '" + sProduct + "'.";
									return false;
								}
								break;
							default:
								//Not an author
								par_sMessage = "You are not allowed to overwrite shared metadata because you don't have an author permission. Section: '" + par_sSection + "' page: '" + par_sPageID + "' product: '" + sProduct + "'.";
								return false;
						}
					}
					else
					{
						//Local page, only full author or the 'creator' user can edit it
						//MI 10/9/08 Added allowing full authors to transfer in local pages regardless of who's the author
						switch (goP.IsUserAuthor())
						{
							case 2:
							break;
								//Full author - always OK
							default: //1 limited author or 0 not an author
								//Not an author
								//In this case (unshared page), permission is based on the section, not creator
								//The section is editable by authors whereas the creator isn't. The creator gets
								//lost during transfer. The section typically is the ID of the creator, so this is
								//a moot point, but important in this context.
								if (par_sSection != goP.GetUserTID())
								{
									par_sMessage = "You are not allowed to overwrite non-shared metadata of other users. Section: '" + par_sSection + "' page: '" + par_sPageID + "' product: '" + sProduct + "'.";
									return false;
								}
								break;
						}
					}

					if (!goMeta.PageWrite(par_sSection, par_sPageID, par_sMetadata, "", "", sProduct))
					{
						par_sMessage = "An error occurred writing page '" + par_sPageID + "' (section '" + par_sSection + "', product '" + sProduct + "'). If the problem persists, please contact Selltis support.";
						return false;
					}
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
			return true;
		}

		public string TransferOut(string par_sSectionID, string par_sPageID, string par_sProduct = "")
		{
			//MI 1/26/09 added par_sProduct.
			//MI 11/9/07
			//PURPOSE:
			//       Create a string with the ini-format information about a
			//       metadata object and dependent objects, if any, ready for
			//       transferring to another database or for creating a new
			//       MD object out of it.
			//PARAMETERS:
			//       par_sSectionID: section ID for user's objects or 
			//          'GLOBAL' (or '') if the object is GLOBAL.
			//       par_sPageID: SUID of the MD page to transfer out.
			//       par_sProduct: Product code (SA, WP, MB, XX for 'all'). If Nothing or
			//           "", current session product will be used.
			//RETURNS:
			//       String with each page marked up for the transfer in
			//       facility to be able to parse each page.

			string sproc = "clMetadata::TransferOut";
			goLog.Log(sproc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);
			string sResult = "";
			string sTemp = "";
			string sTemp2 = "";
			string sTemp3 = "";
			int i = 0;
			string sProduct = par_sProduct;
			if (sProduct == "")
			{
				sProduct = goP.GetProduct();
			}
			// Try
			//Check user's permission
			if (!(goP.GetMe("PERMTRANSFEROUT") == "1"))
			{
					return "You don't have permission to transfer out application objects.";
				}
				switch (goTR.GetPrefix(par_sPageID).ToUpper())
				{
					case "DSK_":
						//DSK and VIE objects
						sTemp = goMeta.PageRead(par_sSectionID, par_sPageID, "", true, sProduct, true);
						//Put out the desktop
						sResult = goMeta.TransferOutMarkup(par_sSectionID, par_sPageID, sTemp, sProduct);
						//Views
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of goTR.StringToNum(goTR.StrRead(sTemp, "VIEWCOUNT", "0", false), "0") for every iteration:
						int tempVar = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sTemp, "VIEWCOUNT", "0", false), "0"));
						for (i = 1; i <= tempVar; i++)
						{
							//View page ID
							sTemp2 = goTR.StrRead(sTemp, "VIEW" + goTR.NumToString(i, "0") + "ID", null, false);
							//View metadata
							sTemp3 = goMeta.PageRead(par_sSectionID, sTemp2, "", true, sProduct, true);
							if (sTemp3 != "")
							{
								sResult += goMeta.TransferOutMarkup(par_sSectionID, sTemp2, sTemp3, sProduct);
							}
						}
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of goTR.StringToNum(goTR.StrRead(sTemp, "TABCOUNT", "0", false), "0") for every iteration:
						int tempVar2 = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sTemp, "TABCOUNT", "0", false), "0"));
						for (i = 1; i <= tempVar2; i++)
						{
							//View page ID
							sTemp2 = goTR.StrRead(sTemp, "TAB" + goTR.NumToString(i, "0") + "VIEWID", null, false);
							//View metadata
							sTemp3 = goMeta.PageRead(par_sSectionID, sTemp2, "", true, sProduct, true);
							if (sTemp3 != "")
							{
								sResult += goMeta.TransferOutMarkup(par_sSectionID, sTemp2, sTemp3, sProduct);
							}
						}
						break;
					default:
						//Single MD page
						sResult = goMeta.TransferOutMarkup(par_sSectionID, par_sPageID, goMeta.PageRead(par_sSectionID, par_sPageID, "", true, sProduct, true), sProduct);
						break;
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sproc)
			//    End If
			//End Try
			return sResult;
		}


		private string TransferOutMarkup(string par_sSectionID, string par_sPageID, string par_sString, string par_sProduct = "")
		{
			//MI 1/26/09 added par_sProduct.
			//MI 11/9/07
			//PURPOSE:
			//       Wrap a single metadata page to be transferred out with
			//       markup that transfer in functionality will use to parse
			//       individual pages.
			//PARAMETERS:
			//       par_sSectionID
			//       par_sPageID
			//       par_sString: Page of metadata (ini-format string).
			//       par_sProduct: Product code (SA, MB, WP, XX for 'all'). If Nothing or "",
			//           the current session product will be used.
			//RETURNS:
			//       Marked-up string ready for inclusion in the transfer out
			//       string.

			string sProc = "clMetadata:TransferOutMarkup";
			string s = par_sString;
			string sTemp = null;
			string sPageInfo = "";
			string sProduct = par_sProduct;
			if (par_sSectionID == null)
			{
				par_sSectionID = "GLOBAL";
			}
			if (par_sSectionID == "")
			{
				par_sSectionID = "GLOBAL";
			}
			if (sProduct == "")
			{
				sProduct = goP.GetProduct();
			}
			//  Try
			goTR.StrWrite(ref sPageInfo, "###TYPE", goDef.GetMetaTypeFromPrefix(par_sPageID));
				sTemp = goTR.StrRead(par_sString, "US_NAME", null, false);
				if (sTemp == "")
				{
					sTemp = goTR.StrRead(par_sString, "NAME");
				}
				goTR.StrWrite(ref sPageInfo, "###NAME", sTemp);
				goTR.StrWrite(ref sPageInfo, "###SECTION", par_sSectionID);
				goTR.StrWrite(ref sPageInfo, "###PAGE", par_sPageID);
				goTR.StrWrite(ref sPageInfo, "###PRODUCT", sProduct);
				if (sPageInfo.Substring(sPageInfo.Length - 2) != "\r\n")
				{
					sPageInfo += "\r\n";
				}
				s = "---***^^^BEGIN PAGE^^^***---" + "\r\n";
				s += sPageInfo;
				s += par_sString + "\r\n";
				s += "---***^^^END PAGE^^^***---" + "\r\n" + "\r\n";

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return s;
		}

		public void Initialize()
		{
			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			goDef = (Selltis.BusinessLogic.clDefaults)HttpContext.Current.Session["goDef"];
		}

		public clMetaData()
		{

		}

	}
}
