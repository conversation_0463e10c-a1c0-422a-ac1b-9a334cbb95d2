'OWNER: MI

Public Class clImportTasks
    'Inherits clVar

    'MI 11/2/06 Created.

    'Enable global objects only if needed
    'Private goP As clProject
    'Private goMeta As clMetaData
    'Private goTR As clTransform
    'Private goData As clData
    'Private goErr As clError
    'Private goLog As clLog
    'Private goUI As clUI
    'Private goHist As clHistory

    'Public par_sMode As String     'Page opening mode
    Public sReturnURL As String = ""
    Public bBackFromSubdialog As Boolean
    Public sMessageBoxPurpose As String

    Public sTitle As String = "Import Tasks"
    '==> Edit
    Public fenenexecution As String = "ImportTasks.aspx"

    Public sFileName As String = ""     'Main file



    Public Sub Initialize()
        Dim sProc As String = "clDiaFiltCnd::Initialize"
        'Try
        '   'Enable only if needed
        '   goP = HttpContext.Current.Session("goP")
        '   goTR = HttpContext.Current.Session("goTr")
        '   goMeta = HttpContext.Current.Session("goMeta")
        '   goData = HttpContext.Current.Session("goData")
        '   goErr = HttpContext.Current.Session("goErr")
        '   goLog = HttpContext.Current.Session("goLog")
        '   goUI = HttpContext.Current.Session("goUI")
        '   goHist = HttpContext.Current.Session("goHist")
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Public Sub New()

    End Sub

    Protected Overrides Sub Finalize()
        MyBase.Finalize()
    End Sub
End Class
