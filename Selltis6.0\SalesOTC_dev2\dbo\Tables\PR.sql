﻿CREATE TABLE [dbo].[PR] (
    [GID_ID]                    UNIQUEIDENTIFIER CONSTRAINT [DF_PR_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'PR',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                    BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                  NVARCHAR (80)    NULL,
    [MMO_ActionPlan]            NTEXT            NULL,
    [FIL_Attachments]           NTEXT            NULL,
    [DTT_CreationTime]          DATETIME         CONSTRAINT [DF_PR_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [DTT_Q10]                   DATETIME         NULL,
    [DTT_Q20]                   DATETIME         NULL,
    [DTT_Q30]                   DATETIME         NULL,
    [DTT_Q40]                   DATETIME         NULL,
    [DTT_Q50]                   DATETIME         NULL,
    [DTT_Q60]                   DATETIME         NULL,
    [DTT_Q70]                   DATETIME         NULL,
    [DTT_Q80]                   DATETIME         NULL,
    [DTT_Q90]                   DATETIME         NULL,
    [CHK_Open]                  TINYINT          CONSTRAINT [DF_PR_CHK_Open] DEFAULT ((0)) NULL,
    [DTT_ExpCloseDate]          DATETIME         NULL,
    [MLS_CompetingPosition]     SMALLINT         NULL,
    [MMO_CompetitionNotes]      NTEXT            NULL,
    [MLS_ConfidenceLevel]       SMALLINT         NULL,
    [MLS_Currency]              SMALLINT         NULL,
    [DTT_Time]                  DATETIME         NULL,
    [DTT_DateClosed]            DATETIME         NULL,
    [CHK_DemoData]              TINYINT          NULL,
    [MMO_Description]           NTEXT            NULL,
    [SR__ExchRate]              REAL             NULL,
    [MMO_Influences]            NTEXT            NULL,
    [MMO_Journal]               NTEXT            NULL,
    [MMO_NextAction]            NTEXT            NULL,
    [DTT_NextActionDate]        DATETIME         NULL,
    [MMO_Note]                  NTEXT            NULL,
    [TXT_OrigCreatedBy]         VARCHAR (4)      NULL,
    [DTT_OrigCreatedTime]       DATETIME         NULL,
    [TXT_ProjectID]             NVARCHAR (14)    NULL,
    [TXT_ProjectName]           NVARCHAR (50)    NULL,
    [TXT_ProjectRef]            NVARCHAR (30)    NULL,
    [MLS_Priority]              SMALLINT         NULL,
    [MMO_Profile]               NTEXT            NULL,
    [CHK_Q10]                   TINYINT          CONSTRAINT [DF_PR_CHK_Q10] DEFAULT ((0)) NULL,
    [CHK_Q20]                   TINYINT          CONSTRAINT [DF_PR_CHK_Q20] DEFAULT ((0)) NULL,
    [CHK_Q30]                   TINYINT          CONSTRAINT [DF_PR_CHK_Q30] DEFAULT ((0)) NULL,
    [CHK_Q40]                   TINYINT          CONSTRAINT [DF_PR_CHK_Q40] DEFAULT ((0)) NULL,
    [CHK_Q50]                   TINYINT          CONSTRAINT [DF_PR_CHK_Q50] DEFAULT ((0)) NULL,
    [CHK_Q60]                   TINYINT          CONSTRAINT [DF_PR_CHK_Q60] DEFAULT ((0)) NULL,
    [CHK_Q70]                   TINYINT          CONSTRAINT [DF_PR_CHK_Q70] DEFAULT ((0)) NULL,
    [CHK_Q80]                   TINYINT          CONSTRAINT [DF_PR_CHK_Q80] DEFAULT ((0)) NULL,
    [CHK_Q90]                   TINYINT          CONSTRAINT [DF_PR_CHK_Q90] DEFAULT ((0)) NULL,
    [MLS_ReasonWonLost]         SMALLINT         NULL,
    [CHK_Report]                TINYINT          CONSTRAINT [DF_PR_CHK_Report] DEFAULT ((1)) NULL,
    [CUR_Revenue]               MONEY            NULL,
    [MMO_ScopeDefinition]       NTEXT            NULL,
    [MLS_Stage]                 SMALLINT         NULL,
    [MLS_Status]                SMALLINT         NULL,
    [MMO_Strategy]              NTEXT            NULL,
    [MLS_Type]                  SMALLINT         NULL,
    [URL_URLs]                  NTEXT            NULL,
    [CUR_Value]                 MONEY            NULL,
    [TXT_ModBy]                 VARCHAR (4)      NULL,
    [DTT_ModTime]               DATETIME         CONSTRAINT [DF_PR_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [MMO_ImportData]            NTEXT            NULL,
    [SI__ShareState]            TINYINT          CONSTRAINT [DF_PR_SI__ShareState] DEFAULT ((2)) NULL,
    [DR__Custom1]               FLOAT (53)       NULL,
    [DR__Custom2]               FLOAT (53)       NULL,
    [GID_CreatedBy_US]          UNIQUEIDENTIFIER NULL,
    [GID_For_CO]                UNIQUEIDENTIFIER NULL,
    [GID_OriginatedBy_CN]       UNIQUEIDENTIFIER NULL,
    [GID_OriginatedBy_US]       UNIQUEIDENTIFIER NULL,
    [GID_Related_SO]            UNIQUEIDENTIFIER NULL,
    [GID_Related_TE]            UNIQUEIDENTIFIER NULL,
    [GID_TeamLeader_US]         UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]              VARCHAR (50)     NULL,
    [TXT_Year]                  CHAR (4)         NULL,
    [SI__Month]                 TINYINT          NULL,
    [SI__Day]                   TINYINT          NULL,
    [TXT_ExternalID]            NVARCHAR (120)   NULL,
    [TXT_ExternalSource]        VARCHAR (10)     NULL,
    [TXT_ImpJobID]              VARCHAR (20)     NULL,
    [ADR_Attachments]           NTEXT            NULL,
    [TXT_PROJECTLOCATION]       NVARCHAR (150)   NULL,
    [CUR_EXPECTEDAMOUNT]        MONEY            NULL,
    [MLS_MARKET]                SMALLINT         NULL,
    [CHK_MULTIBID]              TINYINT          NULL,
    [TXT_UnitName]              NVARCHAR (150)   NULL,
    [DTT_NEXTSCHEDULEDSERVICE]  DATETIME         NULL,
    [MLS_UNITTYPE]              SMALLINT         NULL,
    [DTT_LASTTIMESERVICED]      DATETIME         NULL,
    [MLS_LOCATIONTYPE]          SMALLINT         NULL,
    [TXT_PECReportURL]          NVARCHAR (150)   NULL,
    [GID_ENDUSER_US]            UNIQUEIDENTIFIER NULL,
    [MLS_RecordType]            SMALLINT         NULL,
    [MLS_COUNTRIES]             SMALLINT         NULL,
    [CHK_Custom1]               TINYINT          NULL,
    [CHK_Custom2]               TINYINT          NULL,
    [CHK_Custom3]               TINYINT          NULL,
    [CHK_Custom4]               TINYINT          NULL,
    [CHK_Custom5]               TINYINT          NULL,
    [CHK_Custom6]               TINYINT          NULL,
    [CHK_Custom7]               TINYINT          NULL,
    [CHK_Custom8]               TINYINT          NULL,
    [CHK_Custom9]               TINYINT          NULL,
    [CHK_Custom10]              TINYINT          NULL,
    [CHK_Custom11]              TINYINT          NULL,
    [CHK_Custom12]              TINYINT          NULL,
    [CHK_Custom13]              TINYINT          NULL,
    [CHK_Custom14]              TINYINT          NULL,
    [CHK_Custom15]              TINYINT          NULL,
    [CHK_Custom16]              TINYINT          NULL,
    [DTT_Custom1]               DATETIME         NULL,
    [DTT_Custom2]               DATETIME         NULL,
    [DTT_Custom3]               DATETIME         NULL,
    [DTT_Custom4]               DATETIME         NULL,
    [DTT_Custom5]               DATETIME         NULL,
    [DTT_Custom6]               DATETIME         NULL,
    [DTT_Custom7]               DATETIME         NULL,
    [DTT_Custom8]               DATETIME         NULL,
    [DTT_Custom9]               DATETIME         NULL,
    [DTT_Custom10]              DATETIME         NULL,
    [DTT_Custom11]              DATETIME         NULL,
    [DTT_Custom12]              DATETIME         NULL,
    [DTT_Custom13]              DATETIME         NULL,
    [DTT_Custom14]              DATETIME         NULL,
    [DTT_Custom15]              DATETIME         NULL,
    [DTT_Custom16]              DATETIME         NULL,
    [MLS_Custom1]               SMALLINT         NULL,
    [MLS_Custom2]               SMALLINT         NULL,
    [MLS_Custom3]               SMALLINT         NULL,
    [TXT_CHK_Q50]               VARCHAR (5)      NULL,
    [TXT_DTE_Q50]               VARCHAR (12)     NULL,
    [TXT_TME_Q50]               VARCHAR (12)     NULL,
    [TXT_MLS_CompetingPosition] VARCHAR (60)     NULL,
    [TXT_CHK_Q10]               VARCHAR (5)      NULL,
    [TXT_DTE_Q10]               VARCHAR (12)     NULL,
    [TXT_TME_Q10]               VARCHAR (12)     NULL,
    [TXT_MLS_ConfidenceLevel]   VARCHAR (60)     NULL,
    [TXT_DTE_CreationTime]      VARCHAR (12)     NULL,
    [TXT_TME_CreationTime]      VARCHAR (12)     NULL,
    [TXT_MLS_Currency]          VARCHAR (60)     NULL,
    [TXT_DTE_Time]              VARCHAR (12)     NULL,
    [TXT_TME_Time]              VARCHAR (12)     NULL,
    [TXT_DTE_DateClosed]        VARCHAR (12)     NULL,
    [TXT_TME_DateClosed]        VARCHAR (12)     NULL,
    [TXT_CHK_DemoData]          VARCHAR (5)      NULL,
    [TXT_CHK_Q40]               VARCHAR (5)      NULL,
    [TXT_DTE_Q40]               VARCHAR (12)     NULL,
    [TXT_TME_Q40]               VARCHAR (12)     NULL,
    [TXT_DR__Custom1]           VARCHAR (30)     NULL,
    [TXT_SR__ExchRate]          VARCHAR (18)     NULL,
    [TXT_DTE_ExpCloseDate]      VARCHAR (12)     NULL,
    [TXT_TME_ExpCloseDate]      VARCHAR (12)     NULL,
    [TXT_CHK_Q20]               VARCHAR (5)      NULL,
    [TXT_DTE_Q20]               VARCHAR (12)     NULL,
    [TXT_TME_Q20]               VARCHAR (12)     NULL,
    [TXT_CHK_Custom1]           VARCHAR (5)      NULL,
    [TXT_DTE_Custom1]           VARCHAR (12)     NULL,
    [TXT_TME_Custom1]           VARCHAR (12)     NULL,
    [TXT_CHK_Custom3]           VARCHAR (5)      NULL,
    [TXT_DTE_Custom5]           VARCHAR (12)     NULL,
    [TXT_TME_Custom5]           VARCHAR (12)     NULL,
    [TXT_DTE_Custom2]           VARCHAR (12)     NULL,
    [TXT_TME_Custom2]           VARCHAR (12)     NULL,
    [TXT_CHK_Custom10]          VARCHAR (5)      NULL,
    [TXT_CHK_Custom11]          VARCHAR (5)      NULL,
    [TXT_DTE_Custom4]           VARCHAR (12)     NULL,
    [TXT_TME_Custom4]           VARCHAR (12)     NULL,
    [TXT_CHK_Custom4]           VARCHAR (5)      NULL,
    [TXT_DTE_Custom7]           VARCHAR (12)     NULL,
    [TXT_TME_Custom7]           VARCHAR (12)     NULL,
    [TXT_DTE_Custom12]          VARCHAR (12)     NULL,
    [TXT_TME_Custom12]          VARCHAR (12)     NULL,
    [TXT_CHK_Custom12]          VARCHAR (5)      NULL,
    [TXT_DTE_Custom8]           VARCHAR (12)     NULL,
    [TXT_TME_Custom8]           VARCHAR (12)     NULL,
    [TXT_CHK_Custom13]          VARCHAR (5)      NULL,
    [TXT_CHK_Custom14]          VARCHAR (5)      NULL,
    [TXT_DTE_Custom15]          VARCHAR (12)     NULL,
    [TXT_TME_Custom15]          VARCHAR (12)     NULL,
    [TXT_DTE_Custom6]           VARCHAR (12)     NULL,
    [TXT_TME_Custom6]           VARCHAR (12)     NULL,
    [TXT_CHK_Custom2]           VARCHAR (5)      NULL,
    [TXT_DTE_Custom9]           VARCHAR (12)     NULL,
    [TXT_TME_Custom9]           VARCHAR (12)     NULL,
    [TXT_CHK_Custom5]           VARCHAR (5)      NULL,
    [TXT_DTE_Custom3]           VARCHAR (12)     NULL,
    [TXT_TME_Custom3]           VARCHAR (12)     NULL,
    [TXT_CHK_Custom15]          VARCHAR (5)      NULL,
    [TXT_DTE_Custom10]          VARCHAR (12)     NULL,
    [TXT_TME_Custom10]          VARCHAR (12)     NULL,
    [TXT_CHK_Custom6]           VARCHAR (5)      NULL,
    [TXT_DTE_Custom11]          VARCHAR (12)     NULL,
    [TXT_TME_Custom11]          VARCHAR (12)     NULL,
    [TXT_CHK_Custom7]           VARCHAR (5)      NULL,
    [TXT_CHK_Custom8]           VARCHAR (5)      NULL,
    [TXT_DTE_Custom13]          VARCHAR (12)     NULL,
    [TXT_TME_Custom13]          VARCHAR (12)     NULL,
    [TXT_CHK_Custom16]          VARCHAR (5)      NULL,
    [TXT_DTE_Custom16]          VARCHAR (12)     NULL,
    [TXT_TME_Custom16]          VARCHAR (12)     NULL,
    [TXT_MLS_Custom1]           VARCHAR (60)     NULL,
    [TXT_MLS_Custom2]           VARCHAR (60)     NULL,
    [TXT_DTE_ModTime]           VARCHAR (12)     NULL,
    [TXT_TME_ModTime]           VARCHAR (12)     NULL,
    [TXT_DTE_NextActionDate]    VARCHAR (12)     NULL,
    [TXT_TME_NextActionDate]    VARCHAR (12)     NULL,
    [TXT_CHK_Open]              VARCHAR (5)      NULL,
    [TXT_DTE_OrigCreatedTime]   VARCHAR (12)     NULL,
    [TXT_TME_OrigCreatedTime]   VARCHAR (12)     NULL,
    [TXT_CHK_Q30]               VARCHAR (5)      NULL,
    [TXT_DTE_Q30]               VARCHAR (12)     NULL,
    [TXT_TME_Q30]               VARCHAR (12)     NULL,
    [TXT_MLS_Priority]          VARCHAR (60)     NULL,
    [TXT_CHK_Q60]               VARCHAR (5)      NULL,
    [TXT_DTE_Q60]               VARCHAR (12)     NULL,
    [TXT_TME_Q60]               VARCHAR (12)     NULL,
    [TXT_CHK_Q70]               VARCHAR (5)      NULL,
    [TXT_DTE_Q70]               VARCHAR (12)     NULL,
    [TXT_TME_Q70]               VARCHAR (12)     NULL,
    [TXT_CHK_Q80]               VARCHAR (5)      NULL,
    [TXT_DTE_Q80]               VARCHAR (12)     NULL,
    [TXT_TME_Q80]               VARCHAR (12)     NULL,
    [TXT_CHK_Q90]               VARCHAR (5)      NULL,
    [TXT_DTE_Q90]               VARCHAR (12)     NULL,
    [TXT_TME_Q90]               VARCHAR (12)     NULL,
    [TXT_MLS_ReasonWonLost]     VARCHAR (60)     NULL,
    [TXT_CHK_Report]            VARCHAR (5)      NULL,
    [TXT_CUR_Revenue]           VARCHAR (30)     NULL,
    [TXT_MLS_Stage]             VARCHAR (60)     NULL,
    [TXT_MLS_Status]            VARCHAR (60)     NULL,
    [TXT_CHK_Custom9]           VARCHAR (5)      NULL,
    [TXT_DTE_Custom14]          VARCHAR (12)     NULL,
    [TXT_TME_Custom14]          VARCHAR (12)     NULL,
    [TXT_MLS_Custom3]           VARCHAR (60)     NULL,
    [TXT_MLS_Type]              VARCHAR (60)     NULL,
    [TXT_DR__Custom2]           VARCHAR (30)     NULL,
    [TXT_CUR_Value]             VARCHAR (30)     NULL,
    [MMO_LNK_ASSIGNEDTO_US]     TEXT             NULL,
    [MMO_LNK_Attached_AF]       TEXT             NULL,
    [MMO_LNK_COMPETING_VE]      TEXT             NULL,
    [MMO_LNK_Connected_AC]      TEXT             NULL,
    [MMO_LNK_Connected_AP]      TEXT             NULL,
    [MMO_LNK_Connected_EX]      TEXT             NULL,
    [MMO_LNK_Connected_MS]      TEXT             NULL,
    [MMO_LNK_Connected_OP]      TEXT             NULL,
    [MMO_LNK_Connected_QL]      TEXT             NULL,
    [MMO_LNK_Connected_QT]      TEXT             NULL,
    [MMO_LNK_Connected_TD]      TEXT             NULL,
    [TXT_LNK_CREATEDBY_US]      VARCHAR (50)     NULL,
    [TXT_LNK_FOR_CO]            VARCHAR (50)     NULL,
    [MMO_LNK_INVOLVED_CN]       TEXT             NULL,
    [MMO_LNK_INVOLVED_CO]       TEXT             NULL,
    [MMO_LNK_INVOLVES_US]       TEXT             NULL,
    [TXT_LNK_ORIGINATEDBY_CN]   VARCHAR (50)     NULL,
    [TXT_LNK_ORIGINATEDBY_US]   VARCHAR (50)     NULL,
    [MMO_LNK_RELATED_GR]        TEXT             NULL,
    [MMO_LNK_RELATED_IU]        TEXT             NULL,
    [MMO_LNK_RELATED_PD]        TEXT             NULL,
    [TXT_LNK_RELATED_SO]        VARCHAR (50)     NULL,
    [TXT_LNK_RELATED_TE]        VARCHAR (50)     NULL,
    [MMO_LNK_RELATED_VE]        TEXT             NULL,
    [TXT_LNK_TEAMLEADER_US]     VARCHAR (50)     NULL,
    [MMO_LNK_CONTRACTOR_CN]     TEXT             NULL,
    [MMO_SALESJOURNAL]          NTEXT            NULL,
    [MMO_LNK_ENGINEERING_CN]    TEXT             NULL,
    [MMO_LNK_ENGINEERING_CO]    TEXT             NULL,
    [MMO_LNK_CONTRACTOR_CO]     TEXT             NULL,
    [TXT_CustNo]                NVARCHAR (16)    NULL,
    [INT_ReviewInterval]        SMALLINT         NULL,
    [TXT_INT_ReviewInterval]    VARCHAR (30)     NULL,
    [CHK_ReviewOverdue]         TINYINT          NULL,
    [TXT_CHK_ReviewOverdue]     VARCHAR (5)      NULL,
    [TXT_SI__Day]               VARCHAR (8)      NULL,
    [TXT_MLS_MARKET]            VARCHAR (60)     NULL,
    [TXT_SI__Month]             VARCHAR (8)      NULL,
    [MMO_LNK_AboutFor_AC]       TEXT             NULL,
    [MMO_LNK_FROM_SO]           TEXT             NULL,
    [MMO_LNK_RELATED_EV]        TEXT             NULL,
    [MMO_LNK_RELATED_MO]        TEXT             NULL,
    [SI__PROBABILITY]           TINYINT          NULL,
    [DTT_PREBID]                DATETIME         NULL,
    [GID_ENGINEERING_CO]        UNIQUEIDENTIFIER NULL,
    [GID_WINCON_CO]             UNIQUEIDENTIFIER NULL,
    [CUR_TOTVALUE]              MONEY            NULL,
    [CHK_MGRREVIEW]             TINYINT          NULL,
    [GID_Has_PD]                UNIQUEIDENTIFIER NULL,
    [SR__Qty]                   REAL             NULL,
    [CUR_UnitPrice]             MONEY            NULL,
    [CUR_OppValue]              MONEY            NULL,
    [SR__OppPercProfit]         REAL             NULL,
    [DTT_OppExpClose]           DATETIME         NULL,
    [DTT_OppNextAction]         DATETIME         NULL,
    [DTT_BidDateFirm]           DATETIME         NULL,
    [GID_BidDate_AP]            UNIQUEIDENTIFIER NULL,
    [CHK_AddBidAP]              TINYINT          NULL,
    [CUR_ProfitProbability]     MONEY            NULL,
    [CUR_ProfitProbIndex]       MONEY            NULL,
    [CUR_BUYRESALE]             MONEY            NULL,
    [CUR_BUYRESALEGP]           MONEY            NULL,
    [CUR_REPORDER]              MONEY            NULL,
    [CUR_REPORDERGP]            MONEY            NULL,
    [CUR_TOTALPROJECT]          MONEY            NULL,
    [CUR_TOTALPROJECTGP]        MONEY            NULL,
    [MLS_OPPTYPE]               SMALLINT         NULL,
    [MLS_PROJECTPHASE]          SMALLINT         NULL,
    [CUR_OPPPROFIT]             MONEY            NULL,
    [MLS_JOURNALTYPE]           SMALLINT         NULL,
    [DTT_EXPECTEDBIDDATE]       DATETIME         NULL,
    [DTT_EXPBOOKINGDATE]        DATETIME         NULL,
    [CUR_TOTALPRGPVALUEINDEX]   MONEY            NULL,
    [CUR_TOTALPRVALUEINDEX]     MONEY            NULL,
    [DTT_EXPECTEDINVOICEDATE]   DATETIME         NULL,
    [CUR_TOTALWON]              MONEY            NULL,
    [CUR_TotalWonGP]            MONEY            NULL,
    [GID_HAS_VE]                UNIQUEIDENTIFIER NULL,
    [GID_HAS_PF]                UNIQUEIDENTIFIER NULL,
    [GID_HAS_PG]                UNIQUEIDENTIFIER NULL,
    [GID_JCIID]                 UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_PR] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PR_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]),
    CONSTRAINT [LNK_PR_For_CO] FOREIGN KEY ([GID_For_CO]) REFERENCES [dbo].[CO] ([GID_ID]),
    CONSTRAINT [LNK_PR_OriginatedBy_CN] FOREIGN KEY ([GID_OriginatedBy_CN]) REFERENCES [dbo].[CN] ([GID_ID]),
    CONSTRAINT [LNK_PR_OriginatedBy_US] FOREIGN KEY ([GID_OriginatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]),
    CONSTRAINT [LNK_PR_Related_SO] FOREIGN KEY ([GID_Related_SO]) REFERENCES [dbo].[SO] ([GID_ID]),
    CONSTRAINT [LNK_PR_Related_TE] FOREIGN KEY ([GID_Related_TE]) REFERENCES [dbo].[TE] ([GID_ID]),
    CONSTRAINT [LNK_PR_TeamLeader_US] FOREIGN KEY ([GID_TeamLeader_US]) REFERENCES [dbo].[US] ([GID_ID])
);


GO
ALTER TABLE [dbo].[PR] NOCHECK CONSTRAINT [LNK_PR_CreatedBy_US];


GO
ALTER TABLE [dbo].[PR] NOCHECK CONSTRAINT [LNK_PR_For_CO];


GO
ALTER TABLE [dbo].[PR] NOCHECK CONSTRAINT [LNK_PR_OriginatedBy_CN];


GO
ALTER TABLE [dbo].[PR] NOCHECK CONSTRAINT [LNK_PR_OriginatedBy_US];


GO
ALTER TABLE [dbo].[PR] NOCHECK CONSTRAINT [LNK_PR_Related_SO];


GO
ALTER TABLE [dbo].[PR] NOCHECK CONSTRAINT [LNK_PR_Related_TE];


GO
ALTER TABLE [dbo].[PR] NOCHECK CONSTRAINT [LNK_PR_TeamLeader_US];


GO
CREATE NONCLUSTERED INDEX [IX_PR_TypeStatusExpCloseDate]
    ON [dbo].[PR]([MLS_Type] ASC, [MLS_Status] ASC, [DTT_ExpCloseDate] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_DateTime]
    ON [dbo].[PR]([DTT_Time] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_StatusDate]
    ON [dbo].[PR]([MLS_Status] ASC, [DTT_Time] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_OriginatedBy_CN]
    ON [dbo].[PR]([GID_OriginatedBy_CN] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_PR_BI__ID]
    ON [dbo].[PR]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_StatusValueRev]
    ON [dbo].[PR]([MLS_Status] ASC, [CUR_Value] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_Related_TE]
    ON [dbo].[PR]([GID_Related_TE] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_For_CO]
    ON [dbo].[PR]([GID_For_CO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_CreatedBy_US]
    ON [dbo].[PR]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_TeamLeader_US]
    ON [dbo].[PR]([GID_TeamLeader_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_StatusNextAction]
    ON [dbo].[PR]([MLS_Status] ASC, [DTT_NextActionDate] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_OriginatedBy_US]
    ON [dbo].[PR]([GID_OriginatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_StatusExpCloseDate]
    ON [dbo].[PR]([MLS_Status] ASC, [DTT_ExpCloseDate] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_ModDateTime]
    ON [dbo].[PR]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_TXT_ImportID]
    ON [dbo].[PR]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_CreationTime]
    ON [dbo].[PR]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_ProjectName]
    ON [dbo].[PR]([TXT_ProjectName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_StatusDateRev]
    ON [dbo].[PR]([MLS_Status] ASC, [DTT_Time] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_TypeStatusDate]
    ON [dbo].[PR]([MLS_Type] ASC, [MLS_Status] ASC, [DTT_Time] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_StatusNADateValueRev]
    ON [dbo].[PR]([MLS_Status] ASC, [DTT_NextActionDate] ASC, [CUR_Value] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_TypeStatusDateRev]
    ON [dbo].[PR]([MLS_Type] ASC, [MLS_Status] ASC, [DTT_Time] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_Name]
    ON [dbo].[PR]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_Related_SO]
    ON [dbo].[PR]([GID_Related_SO] ASC);


GO
CREATE TRIGGER trPRUpdateTN
ON [PR]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in PR table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(120)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'PR'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable
					and tn2.GID_InternalID = inserted.GID_ID)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [PR]
			SET [PR].TXT_ExternalSource = '', [PR].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [PR].GID_ID = in1.GID_ID
				and ISNULL([PR].TXT_ExternalSource, '') <> ''
				and ISNULL([PR].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trPRUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trPRUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trPRUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!