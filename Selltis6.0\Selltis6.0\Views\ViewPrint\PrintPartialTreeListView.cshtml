﻿@model Selltis.Core.Report
@using Kendo.Mvc.UI;

@*<script src="https://kendo.cdn.telerik.com/2015.3.930/js/kendo.all.min.js"></script>

    <script src="https://kendo.cdn.telerik.com/2015.3.930/js/kendo.web.min.js" type="text/javascript"></script>
    <script src="https://kendo.cdn.telerik.com/2015.3.930/js/kendo.aspnetmvc.min.js" type="text/javascript"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/2.4.0/jszip.js"></script>*@

<!--Kendo UI version Upgraded to 2017-->
@*<script src="~/Content/themes/Selltis/scripts/2017.1.223/kendo.all.min.js" type="text/javascript"></script>
<script src="~/Content/themes/Selltis/scripts/2017.1.223/kendo.aspnetmvc.min.js" type="text/javascript"></script>*@

<script src="~/Content/themes/Selltis/scripts/2017.3.1026/kendo.all.min.js" type="text/javascript"></script>
<script src="~/Content/themes/Selltis/scripts/2017.3.1026/kendo.aspnetmvc.min.js" type="text/javascript"></script>

<script src="~/Content/themes/Selltis/scripts/kendo.web.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/2.4.0/jszip.js"></script>

<script id="photo-template" type="text/x-kendo-template">
    <div>Total: #: Value #</div>
</script>

<style>

    .k-icon.k-i-none {
        background-image: none !important;
    }
    .k-grid-content{
        min-height:200px;
        height:auto !important;
        /*width:auto !important;
        min-width:100% !important;*/
    }
    .k-treelist.k-grid.k-widget.k-display-block{
        /*min-width:100% !important;
        width:auto !important;
        overflow-x:auto !important;*/
    }
    .k-grid-content {
    position: relative;
     /*overflow: inherit !important; 
     overflow-x: inherit !important; 
     overflow-y: inherit !important;*/
    zoom: 1;
}
</style>
@(Html.Hidden("AggregateColumns" + Model.ViewId.Replace(" ", ""), Model.AggregateColumns))
@(Html.Kendo().TreeList<dynamic>()
               .Name("grid" + Model.ViewId.Replace(" ", ""))
                     .Columns(columns =>
                     {
                         //if (Model.ViewRecordOpen != "1")
                         //{
                         //    columns.Add().Template("<a id=\"OpendLinkPO\" style=\"cursor: pointer;\" class=\"AnchorOpen\" onclick=\"OpenReportClick(this,'" + Model.TableName + "','" + Model.ViewId.Replace(" ", "") + "')\">Open</a>").Width(38);
                         //}
                         foreach (Selltis.Core.GridColumn column in Model.Columns)
                         {
                             if (column.Title != "GID_ID")
                             {
                                 var c = columns.Add().Field(column.Name.Replace(",", "").Replace("<%", "").Replace("%>", "").Replace(" ", "")).Title(column.Title);
                                 //.Width(column.Width);
                                 if (column.Name.ToUpper().Contains("MMO") || column.Name.ToUpper().Contains("MMR") || column.Name.ToUpper().Contains("JOURNAL") || column.Name.ToUpper().Contains("NOTES") || column.Name.ToUpper().Contains("DESCRIPTION"))
                                 {
                                     c.Width(500);
                                 }
                                 //style=\"vertical-align: text-top;\"
                                 c.HtmlAttributes(new { @style = "vertical-align: text-top;" })
                                  .Encoded(false);

                                 if (column.Alignment == "C" || column.Alignment.ToUpper() == "CENTER")
                                     c.HtmlAttributes(new { @style = "text-align:center;vertical-align:text-top;" });
                                 else if (column.Alignment == "R" || column.Alignment.ToUpper() == "RIGHT")
                                     c.HtmlAttributes(new { @style = "text-align:right;vertical-align:text-top;" });

                                 if (column.Name.Contains("CUR_") || column.Name.Contains("SI__"))
                                 {
                                     c.Template("#=FormatNegativeValue(data." + column.Name + ",\'" + column.Name.ToUpper() + "\')#");
                                 }

                                 if (!column.Name.Equals("id") && !column.Name.Equals("parentId"))
                                 {
                                     if (column.Name.Equals("Value"))
                                     {
                                         c.Template("<div>Total: #: Value #</div>");
                                     }
                                     else if (column.Name.Equals("GID_ID"))
                                     {
                                         c.Hidden(true);
                                     }

                                     else if (column.IsIcon == true)
                                     {
                                         c.Template("#=TreeListTemplateData(data." + column.Name + ",\'" + column.Name.ToUpper() + "\')#");

                                         //c.ClientTemplate("#if(data." + Column.Name.ToUpper() + ".indexOf('.')>=0) {#<img src='/Content/Images/" + "#= " + Column.Name + " #' alt='#= " + Column.Name + " # ' />#} else{#<span>#= " + Column.Name + " #</span>#}#");
                                         //c.Template("#if(data." + column.Name + "==null){}else if(data." + column.Name.ToUpper() + ".indexOf('(') == -1 && data." + column.Name + ".indexOf(')') == -1) {#<img src='/Content/Images/" + "#: data." + column.Name + " #' alt='#:data." + column.Name + "#' />#}else{if(data." + column.Name.ToUpper() + ".indexOf('&lt;') >= 0 && data." + column.Name.ToUpper() + ".indexOf('&gt;') >= 0){#<span>#: data." + column.Name + ".replace('&lt;','<').replace('&gt;','>')  #</span>#}else{#<span>#: data." + column.Name + "#</span>#}}#");
                                     }
                                     //else if (column.IsIcon == true)
                                     //{
                                     //    c.Template("#if(data." + column.Name + "==null){}else if(data." + column.Name.ToUpper() + ".indexOf('(') == -1 && data." + column.Name + ".indexOf(')') == -1) {#<img src='/Content/Images/" + "#: data." + column.Name + " #' alt='#:data." + column.Name + "#' />#}else{#<span>#: data." + column.Name + "#</span>#}#");
                                     //}

                                     else if (column.IsLink == true)
                                     {
                                         c.Sortable(false);
                                         if (column.Name.ToUpper().Contains("EML_"))
                                         {
                                             string colValue = "#:data." + column.Name + "#";
                                             c.Template("#if(data." + column.Name + "==null){}else{#<a href='mailto:" + colValue + "?Subject='>" + colValue + "</a>#}#");
                                             //c.Template(column.Name);
                                         }
                                         else if (column.Name.ToUpper().Contains("LNK_"))
                                         {
                                             c.Template("#=getTreeLinks(data." + column.Name + ",\'" + column.Name.ToUpper() + "\')#");
                                             //c.Template(column.Name);
                                         }
                                     }
                                 }
                                 //if (column.IsSortable == false)
                                 //{
                                 c.Sortable(false).HeaderAttributes(new { @title = "This column is not sortable" });
                                 //}
                                 //else
                                 //{
                                 //    c.Sortable(true).HeaderAttributes(new { @title = "Click to sort by this column" });
                                 //}
                             }
                         }
                     })
                .Filterable(false)
                .Sortable()
                .Resizable(true)
                .Events(e => e.DataBound("TreeDataBound"))
                .DataSource(dataSource => dataSource
                    //.Aggregates(aggr =>
                    //{
                    //    aggr.Add("Value", typeof(int)).Sum();
                    //    aggr.Add("ForCompany", typeof(string)).Count();
                    //})
                    .Model(m =>
                    {
                        //m.Id(Model.Columns[0].ColumnName);
                        //m.ParentId("parentId1");
                    })
                        //.Read(read => read.Action("ReadData1", "Report", new { ViewKey = Model.ViewKey, IsActive = true, MasterSelID = Model.MasterSelID, RowClick = Model.RowClick }))
                        //.Read(read => read.Action("ReadData1_V2", "Report", new { ViewKey = Model.ViewKey, firstload = true, Key = Model.Key }))
                        .Read(read => read.Action("ReadData_SendJob", "Report", new { ViewKey = Model.ViewKey, firstload = true, _ViewType = "REPORT", Key = Model.Key }))
               .ServerOperation(false)
                )
)
<script>


    $(document).ready(function () {
        @*debugger;
        var _viewId = '@Model.ViewId.Replace(" ", "")';
        var treelist = $("#grid" + _viewId).data("kendoTreeList");
        var rows = $("tr.k-treelist-group", treelist.tbody);

        //if ($("#ExpandCollapse" + _viewId).hasClass('fa-arrows')) {
            $.each(rows, function (idx, row) {
                treelist.expand(row);
                row.firstChild.innerHTML = row.firstChild.innerHTML.replace('k-icon k-i-expand', 'k-icon k-i-collapse');
            });
        //}*@
    })


    function TreeDataBound(e) {
        //alert("ss");

        //debugger;
        var _viewId = e.sender.element[0].id;
        var treelist = $("#" + _viewId).data("kendoTreeList");
        var rows = $("tr.k-treelist-group", treelist.tbody);

        //if ($("#ExpandCollapse" + _viewId).hasClass('fa-arrows')) {
        $.each(rows, function (idx, row) {
            treelist.expand(row);
            row.firstChild.innerHTML = row.firstChild.innerHTML.replace('k-icon k-i-expand', 'k-icon k-i-collapse');
        });
        //}


        var PrintActionMode = sessionStorage.getItem('PrintActionMode');
        if (PrintActionMode.toLowerCase() == "view") {
            //RunTreeSpecifiedAction("view", 0);
            setTimeout(RunTreeSpecifiedAction, 2000, "view", 0);
        }
        else if (PrintActionMode.toLowerCase() == "desktop") {
            var viewCount = sessionStorage.getItem('desktopViewCount');
            viewCount = (viewCount == null || viewCount == undefined) ? 1 : viewCount;
            if (viewCount == '@Selltis.Core.Util.GetSessionValue("_desktopViewCount")') {
                //RunTreeSpecifiedAction("desktop", viewCount);
                setTimeout(RunTreeSpecifiedAction, 2000, 'desktop', viewCount);
                sessionStorage.setItem('desktopViewCount', 0);
            }
            else {
                viewCount = (viewCount == null || viewCount == undefined) ? 0 : viewCount;
                sessionStorage.setItem('desktopViewCount', parseInt(viewCount) + 1);
            }
        }
    }

    @*function RunTreeSpecifiedAction(_Mode, viewCount) {
        var grid = $("#grid" + '@Model.ViewId.Replace(" ", "")').data("kendoTreeList");

        var TXT_ToText = sessionStorage.getItem("TXT_To");
        var TXT_CcText = sessionStorage.getItem("TXT_Cc");
        var TXT_SubjectText = sessionStorage.getItem("TXT_Subject");
        var Target = sessionStorage.getItem("Target");
        var ViewId = sessionStorage.getItem("ViewId");
        var EDT_FIRSTNRECORDSText = sessionStorage.getItem("EDT_FIRSTNRECORDSText");
        var SEL_VIEWOPTIONSSelectedIndex = sessionStorage.getItem("SEL_VIEWOPTIONSSelectedIndex");

        if (SEL_VIEWOPTIONSSelectedIndex == null || SEL_VIEWOPTIONSSelectedIndex == "") {
            SEL_VIEWOPTIONSSelectedIndex = 0;
        }

        //Expanding rows Headers to display all rows..S1
        var rows = $("tr.k-treelist-group", grid.tbody);
        $.each(rows, function (idx, row) {
            grid.expand(row);
            row.firstChild.innerHTML = row.firstChild.innerHTML.replace('k-icon k-i-expand', 'k-icon k-i-collapse');
        });

        if (Target == "EXCEL") {
            var EmailGridHtml = grid.table.context.innerHTML.toString();
            var EmailGridHtml = encodeURI(EmailGridHtml);
            $.ajax({
                url: '/ViewPrint/btn_OK_Click',
                dataType: 'json',
                type: 'POST',
                data: { EmailGridHtml: EmailGridHtml, Target: Target, ViewKey: '@Model.ViewKey', EDT_FIRSTNRECORDSText: EDT_FIRSTNRECORDSText, SEL_VIEWOPTIONSSelectedIndex: SEL_VIEWOPTIONSSelectedIndex, TXT_ToText: TXT_ToText, TXT_CcText: TXT_CcText, TXT_SubjectText: TXT_SubjectText, _viewCount: viewCount, Key: '@Model.Key', View_Mode: _Mode },
                success: function (data) {
                    //debugger;
                    if (data == "success") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }
                    else if (data == "desktop") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }

                },
                error: function (data) {
                    //debugger;
                }
            });
        }

        else if (Target == "HTMLEMAIL") {
            //debugger;
            var EmailGridHtml = grid.table.context.innerHTML.toString();
            EmailGridHtml = encodeURI(EmailGridHtml);
            $.ajax({
                url: '/ViewPrint/btn_OK_Click',
                dataType: 'json',
                type: 'POST',
                async: false,
                data: { EmailGridHtml: EmailGridHtml, Target: Target, ViewKey: '@Model.ViewKey', EDT_FIRSTNRECORDSText: EDT_FIRSTNRECORDSText, SEL_VIEWOPTIONSSelectedIndex: SEL_VIEWOPTIONSSelectedIndex, TXT_ToText: TXT_ToText, TXT_CcText: TXT_CcText, TXT_SubjectText: TXT_SubjectText, _viewCount: viewCount, Key: '@Model.Key', View_Mode: _Mode },
                success: function (data) {
                    //debugger;
                    if (data.PNL_MessageBoxVisible == true) {
                        DisplayMessageBox(data);
                        $("#myModalPrint").modal('show');
                    }
                    else if (_Mode == "view") {
                        window.close();
                    }
                    else if (data == "desktop") {
                        window.close();
                    }

                },
                error: function (data) {
                    //debugger;
                }
            })
        }
        else if (Target == "PDF") {
            //debugger;
            var EmailGridHtml = grid.table.context.innerHTML.toString();
            var EmailGridHtml = encodeURI(EmailGridHtml);
            $.ajax({
                url: '/ViewPrint/btn_OK_Click',
                dataType: 'json',
                type: 'POST',
                async: false,
                data: { EmailGridHtml: EmailGridHtml, Target: Target, ViewKey: '@Model.ViewKey', EDT_FIRSTNRECORDSText: EDT_FIRSTNRECORDSText, SEL_VIEWOPTIONSSelectedIndex: SEL_VIEWOPTIONSSelectedIndex, TXT_ToText: TXT_ToText, TXT_CcText: TXT_CcText, TXT_SubjectText: TXT_SubjectText, _viewCount: viewCount, Key: '@Model.Key', View_Mode: _Mode },
                success: function (data) {
                    //debugger;
                    if (data == "success") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }
                    else if (data == "desktop") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }

                },
                error: function (data) {
                    //debugger;
                }
            })
        }
        else if (Target == "print") {
            //window.print();
        }
    }*@

    function DisplayMessageBox(data) {
        $("#PNL_MessageBox").show();
        $("#LBL_MsgBoxTitle").text(data.LBL_MsgBoxTitleText);
        var _LBL_MsgBoxMessage = data.LBL_MsgBoxMessageText.replace("/n/n", "</br></br>").replace("/n", "</br>");
        $("#LBL_MsgBoxMessage").html(_LBL_MsgBoxMessage);

        if (data.BTN_MsgBox1Visible == true) {
            $("#BTN_MsgBox1").show();

            $("#BTN_MsgBox1").attr('value', data.BTN_MsgBox1Text);
            $("#BTN_MsgBox1").prop('value', data.BTN_MsgBox1Text);
            $("#BTN_MsgBox1").html(data.BTN_MsgBox1Text);
        }
        else {
            $("#BTN_MsgBox1").hide();
        }

        if (data.BTN_MsgBox2Visible == true) {
            $("#BTN_MsgBox2").show();

            $("#BTN_MsgBox2").attr('value', data.BTN_MsgBox2Text);
            $("#BTN_MsgBox2").prop('value', data.BTN_MsgBox2Text);
            $("#BTN_MsgBox2").html(data.BTN_MsgBox2Text);
        }
        else {
            $("#BTN_MsgBox2").hide();
        }

        if (data.BTN_MsgBox3Visible == true) {
            $("#BTN_MsgBox3").show();

            $("#BTN_MsgBox3").attr('value', data.BTN_MsgBox3Text);
            $("#BTN_MsgBox3").prop('value', data.BTN_MsgBox3Text);
            $("#BTN_MsgBox3").html(data.BTN_MsgBox3Text);
        }
        else {
            $("#BTN_MsgBox3").hide();
        }
    }

    function onBTN_MsgBox1Click() {
        window.close();
    }
    function onBTN_MsgBox2Click() {
        window.close();
    }
    function onBTN_MsgBox3Click() {
        window.close();
    }


    function ReportPerms() {
        //debugger;
        return {
            ViewKey: '@Model.ViewKey',
            MasterSelID: sessionStorage.getItem("RecordId")
        };
    }
    function TreeListTemplateData(data, ColumnName) {

        var htmlContent = "";
        if (data == null) {

        }
        else if (data.indexOf('(') == -1 && data.indexOf(')') == -1) {
            if (data.indexOf(".") >= 0) {
                if (data.toLowerCase().indexOf("cus_") >= 0) {
                    htmlContent = "<img src='/PublicFiles/" + '@Model.SiteId' + "/Images/" + data + "' alt='" + data + "' />";
                }
                else {
                    htmlContent = "<img src='/Content/Images/" + data + "' alt='" + data + "' />";
                }
            }
            else {
                htmlContent = "<span>" + data + "</span>";
            }
        }
        else if (data.indexOf('<img src=') > -1) {
            htmlContent = unescape(data);
        }
        else {
            //if(data.indexOf('&lt;') >= 0 && data.indexOf('&gt;') >= 0)
            //{
            //    htmlContent = "<span>" + data.replace('&lt;','<').replace('&gt;','>') + "  </span>";
            //}
            //else
            //{
            htmlContent = "<span>" + data + "</span>";
            //}
        }
        return htmlContent;
    }
    function FormatNegativeValue(data, ColumnName) {

        if (data == null)
            return "";

        var GridContent = data;
        var IsRed = '@Model.DisplayNegativeValuesInRed';

        if (IsRed == "1") {
            if (data.indexOf("-") >= 0) {
                GridContent = "<b style='color:red'>" + data + "</b>";
            }
            else {
                GridContent = data;
            }
        }
        return GridContent;
    }


    function RunTreeSpecifiedAction(_Mode, viewCount) {
        //debugger;
        sessionStorage.setItem("RunSpecifiedRun", "true");
        var grid = $("#grid" + '@Model.ViewId.Replace(" ", "")').data("kendoTreeList");

        var TXT_ToText = sessionStorage.getItem("TXT_To");
        var TXT_CcText = sessionStorage.getItem("TXT_Cc");
        var TXT_SubjectText = sessionStorage.getItem("TXT_Subject");
        var Target = sessionStorage.getItem("Target");
        var ViewId = sessionStorage.getItem("ViewId");
        var EDT_FIRSTNRECORDSText = sessionStorage.getItem("EDT_FIRSTNRECORDSText");
        var SEL_VIEWOPTIONSSelectedIndex = sessionStorage.getItem("SEL_VIEWOPTIONSSelectedIndex");

        if (SEL_VIEWOPTIONSSelectedIndex == null || SEL_VIEWOPTIONSSelectedIndex == "") {
            SEL_VIEWOPTIONSSelectedIndex = 0;
        }

        //Expanding rows Headers to display all rows..S1
        var rows = $("tr.k-treelist-group", grid.tbody);
        $.each(rows, function (idx, row) {
            grid.expand(row);
            row.firstChild.innerHTML = row.firstChild.innerHTML.replace('k-icon k-i-expand', 'k-icon k-i-collapse');
        });

        if (Target == "EXCEL") {
            var _Page = encodeURI(document.documentElement.innerHTML);
            var siteN = location.protocol + '//' + location.host;

            $.ajax({
                url: '/ViewPrint/SendJob',
                dataType: 'json',
                type: 'POST',
                data: { Page: _Page, Target: Target, ViewKey: '@Model.ViewKey', EDT_FIRSTNRECORDSText: EDT_FIRSTNRECORDSText, SEL_VIEWOPTIONSSelectedIndex: SEL_VIEWOPTIONSSelectedIndex, TXT_ToText: TXT_ToText, TXT_CcText: TXT_CcText, TXT_SubjectText: TXT_SubjectText, _viewCount: viewCount, Key: '@Model.Key', View_Mode: _Mode, SiteName: siteN },
                success: function (data) {
                    //debugger;
                    if (data == "success") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }
                    else if (data == "desktop") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }

                },
                error: function (data) {
                    //debugger;
                }
            });
        }

        else if (Target == "HTMLEMAIL") {
            //debugger;
            var _Page = encodeURI(document.documentElement.innerHTML);
            var siteN = location.protocol + '//' + location.host;
            $.ajax({
                url: '/ViewPrint/SendJob',
                dataType: 'json',
                type: 'POST',
                async: false,
                data: { Page: _Page, Target: Target, ViewKey: '@Model.ViewKey', EDT_FIRSTNRECORDSText: EDT_FIRSTNRECORDSText, SEL_VIEWOPTIONSSelectedIndex: SEL_VIEWOPTIONSSelectedIndex, TXT_ToText: TXT_ToText, TXT_CcText: TXT_CcText, TXT_SubjectText: TXT_SubjectText, _viewCount: viewCount, Key: '@Model.Key', View_Mode: _Mode, SiteName: siteN },
                success: function (data) {
                    //debugger;
                    //if (data.PNL_MessageBoxVisible == true) {
                    //    $("#AlertModel").modal('show');
                    //    $('#AlertModel').html(modelPrint);
                    //    $("#myModalPrint").modal('show');

                    //    DisplayMessageBox(data);
                    //}
                    //else if (_Mode == "view") {
                    //    window.close();
                    //}
                    //else if (data == "desktop") {
                    //    window.close();
                    //}

                    window.close();

                },
                error: function (data) {
                    //debugger;
                }
            })
        }
        else if (Target == "PDF") {
            //debugger;
            var _Page = encodeURI(document.documentElement.innerHTML);
            var siteN = location.protocol + '//' + location.host;
            $.ajax({
                url: '/ViewPrint/SendJob',
                dataType: 'json',
                type: 'POST',
                async: false,
                data: { Page: _Page, Target: Target, ViewKey: '@Model.ViewKey', EDT_FIRSTNRECORDSText: EDT_FIRSTNRECORDSText, SEL_VIEWOPTIONSSelectedIndex: SEL_VIEWOPTIONSSelectedIndex, TXT_ToText: TXT_ToText, TXT_CcText: TXT_CcText, TXT_SubjectText: TXT_SubjectText, _viewCount: viewCount, Key: '@Model.Key', View_Mode: _Mode, SiteName: siteN },
                success: function (data) {
                    //debugger;
                    if (data == "success") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }
                    else if (data == "desktop") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }

                },
                error: function (data) {
                    //debugger;
                }
            })
        }
        else if (Target == "print") {
            //window.print();
        }
    }


    function getTreeLinks(entireValue, linkName) {
        //debugger;
        var htmlContent = "";
        if (entireValue != null) {
            if (entireValue.substring(entireValue.length - 1) == ')') {
                if (entireValue != null) {
                    if (entireValue.indexOf("#$%^&*") > -1) {
                        entireValue = entireValue.substring(0, entireValue.indexOf("#$%^&*")) + ' ' + entireValue.substring(entireValue.lastIndexOf('('));      //List group data  with group count.. SB
                    }
                }
                return entireValue;
            }
            else if (entireValue.indexOf("#$%^&*") > -1) {
                var values = entireValue.split("#$%^&*");
                var names = values[0].split("\n");
                var gid_ids = values[1].split("\n");

                if (names.length > 0) {
                    for (var i = 0; i < names.length; i++) {
                        var gid_id_Val = gid_ids[i];
                        if (gid_id_Val != undefined && gid_id_Val != null) {
                            gid_id_Val = gid_ids[i].replace("\r", "")
                        }
                        else {
                            gid_id_Val = '';
                        }

                        if (htmlContent) {
                            htmlContent = htmlContent + "&nbsp;" + "<a id='" + gid_id_Val + "' href=\'#\' onclick=LinkCall(\'" + linkName + "\',\'" + gid_id_Val + "\')>" + names[i] + "</a>";
                        }
                        else {
                            htmlContent = "<a id='" + gid_id_Val + "' href=\'#\' onclick=LinkCall(\'" + linkName + "\',\'" + gid_id_Val + "\')>" + names[i] + "</a>";
                        }
                    }
                }
            }
            else {
                return entireValue;
            }
        }
        return htmlContent;
    }
</script>