﻿CREATE TABLE [dbo].[OP_RELATED_OL] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_OP_RELATED_OL_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    [GID_OL] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_OP_RELATED_OL] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_OL_CONNECTED_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_RELATED_OL] FOREIGN KEY ([GID_OL]) REFERENCES [dbo].[OL] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[OP_RELATED_OL] NOCHECK CONSTRAINT [LNK_OL_CONNECTED_OP];


GO
ALTER TABLE [dbo].[OP_RELATED_OL] NOCHECK CONSTRAINT [LNK_OP_RELATED_OL];


GO
CREATE NONCLUSTERED INDEX [IX_OP_RELATED_OL]
    ON [dbo].[OP_RELATED_OL]([GID_OP] ASC, [GID_OL] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OL_CONNECTED_OP]
    ON [dbo].[OP_RELATED_OL]([GID_OL] ASC, [GID_OP] ASC);

