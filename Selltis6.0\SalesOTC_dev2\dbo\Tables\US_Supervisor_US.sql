﻿CREATE TABLE [dbo].[US_Supervisor_US] (
    [GID_ID]  UNIQUEIDENTIFIER CONSTRAINT [DF_User_Supervisor_User_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_US]  UNIQUEIDENTIFIER NOT NULL,
    [GID_US2] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_US_Supervisor_US] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_US_Subordinate_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_Supervisor_US] FOREIGN KEY ([GID_US2]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[US_Supervisor_US] NOCHECK CONSTRAINT [LNK_US_Subordinate_US];


GO
ALTER TABLE [dbo].[US_Supervisor_US] NOCHECK CONSTRAINT [LNK_US_Supervisor_US];


GO
CREATE CLUSTERED INDEX [IX_US_Subordinate_US]
    ON [dbo].[US_Supervisor_US]([GID_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_Supervisor_US]
    ON [dbo].[US_Supervisor_US]([GID_US2] ASC);

