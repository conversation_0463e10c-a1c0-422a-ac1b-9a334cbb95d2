﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text;
using System.Drawing;
using System.Data;
using System.Data.SqlClient;
using Microsoft.VisualBasic;
using System.Collections;
using System.IO;
using System.Net;
using Newtonsoft.Json;
using System.Xml;
using System.Text.RegularExpressions;
using System.Diagnostics;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Threading;
using System.Security.Policy;

namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clProject goP;
        private clLog goLog;
        private clError goErr;
        private clPerm goPerm;
        private ClUI goUI;
        ScriptManager scriptManager = new ScriptManager();

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";
        SqlConnection par_oConnection = null;

        public string sError;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();
            Util.SetSessionValue("SkipQLSpecificLogic", "Y");
        }
        public ScriptsCustom()
        {
            Initialize();
        }
        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
            }
            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool AC_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sFilterIni = "";

            // SKO 12/24/2015  Ticket#858 Set the link box Filter condition to show only the last 6 months quote records
            // Set a filter in the LNK_RELATED_QT linkbox.
            goTR.StrWrite(ref sFilterIni, "FILE", "QT");
            goTR.StrWrite(ref sFilterIni, "CONDITION", "(DTT_CREATIONTIME>='6 months ago + 1' AND DTT_CREATIONTIME<='today|23:59:59')");
            goTR.StrWrite(ref sFilterIni, "C1CONDITION", "][");
            goTR.StrWrite(ref sFilterIni, "C1DATERANGE", "LAST180DAYS");
            goTR.StrWrite(ref sFilterIni, "C1FIELDNAME", "<%DTT_CREATIONTIME%>");
            goTR.StrWrite(ref sFilterIni, "C1PARENBEFORE", "");
            goTR.StrWrite(ref sFilterIni, "C1PARENAFTER", "");
            goTR.StrWrite(ref sFilterIni, "C1VALUE1", "6 months ago + 1");
            goTR.StrWrite(ref sFilterIni, "C1VALUE2", "today|23:59:59");
            goTR.StrWrite(ref sFilterIni, "C1KEYWORD", "");
            goTR.StrWrite(ref sFilterIni, "ACTIVE", "1");
            goTR.StrWrite(ref sFilterIni, "CCOUNT", "1");
            goTR.StrWrite(ref sFilterIni, "SORT", "SYS_NAME ASC");
            goTR.StrWrite(ref sFilterIni, "SORT1", "SYS_NAME");
            // goTR.StrWrite(sFilterIni, "FILE", "QT")
            goTR.StrWrite(ref sFilterIni, "TABLENAME", "QT");
            goTR.StrWrite(ref sFilterIni, "DIRECTION1", "1");
            goTR.StrWrite(ref sFilterIni, "SHOWTOP", "10");
            goTR.StrWrite(ref sFilterIni, "TOP", "10");
            goTR.StrWrite(ref sFilterIni, "US_NAME", "Linkbox selector filter");
            doForm.SetFilterINI("LNK_RELATED_QT", sFilterIni);
            // End If

            // VS 08192016 TKT#1215: Copy Territory from CO
            doForm.doRS.SetFieldVal("LNK_RELATED_TE", doForm.doRS.GetFieldVal("LNK_RELATED_CO%%LNK_IN_TE", 2), 2);

            par_doCallingObject = doForm;
            return true;
        }
        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // VS 08192016 TKT#1215: Copy Territory from CO
            doForm.doRS.SetFieldVal("LNK_RELATED_TE", doForm.doRS.GetFieldVal("LNK_RELATED_CO%%LNK_IN_TE", 2), 2);

            par_doCallingObject = doForm;
            return true;
        }
        public bool AC_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Activity_CreateActLog_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // goP.TraceLine("In " & sProc, "", sProc)

            // TLD 6/29/2009 Prevents main from running
            // Fills required Purpose field
            par_bRunNext = false;

            // PURPOSE:
            // PJ 5/30/02 Adds Act Log with new notes of Lead Activity.
            // Run from enforce.
            // RETURNS:
            // 1 if the Activity is created or didn't need to be created; 0 if not created or the user canceled.

            // 2004/12/08 10:03:25 MAR Considering changes to Journal instead of Notes.

            Form doForm = (Form)par_doCallingObject;
            string sNotes;
            string sWork;
            int lWork;
            string sMessage;

            // If answered Yes on MB to create a journal, then go directly to script that creates journal.
            // If UCase(par_s1) = "YES" Then
            // GoTo CREATEACTLOG
            // End If

            if (Strings.Len(Convert.ToString(doForm.doRS.GetFieldVal("MMO_JOURNAL"))) <= Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))
            {
                par_doCallingObject = doForm;
                return true;
            }
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Activity is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages")?.ToString() + sMessage + Constants.vbCrLf);
                doForm.oVar.SetVar("Ac_CreateActLog_Ran", "1");
                // Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                par_doCallingObject = doForm;
                return true;
            }
            // If doForm.GetMode() = "CREATION" Then Return True		'The record must be in edit mode so we have its ID for links to it.
            doForm.oVar.SetVar("Ac_CreateActLog_Ran", "1");

            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, null, null, null, -1, null, null, null, null, null, doForm.doRS.bBypassValidation);

            // ==> Use goData:CopyAllLinks() method instead of copying links one by one?

            sWork = doForm.doRS.GetFieldVal("MMO_JOURNAL")?.ToString();
            // goP.TraceLine("Length of sWork: '" & Len(sWork) & "'", "", sProc)
            // goP.TraceLine("Val of GetVar of lLenJournal: '" & Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")) & "'", "", sProc)
            // goP.TraceLine("Length - Val = '" & (Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"))) & "'", "", sProc)
            // goP.TraceLine("lWork (Left of sWork for Length - Val): '" & Left(sWork, (Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))) & "'", "", sProc)
            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, lWork);
            // goP.TraceLine("sNotes (Left(sWork, lWork)): '" & sNotes & "'", "", sProc)
            sNotes = sNotes + "== Created from Activity '" + doForm.doRS.GetFieldVal("SYS_Name")?.ToString() + "'";
            // goP.TraceLine("Full sNotes: '" & sNotes & "'", "", sProc)
            // goP.TraceLine("Setting field MMO_NOTES '" & sNotes & "'", "", sProc)
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            // CS goP.TraceLine("Setting field LNK_Involves_US '" & goP.GetMe("ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            // goP.TraceLine("Setting field MLS_Status '" & 1 & "'", "", sProc)
            doNew.SetFieldVal("MLS_Status", 1, 2);     // Completed
                                                       // goP.TraceLine("Setting field TME_STARTTIME '" & "Now" & "'", "", sProc)
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            // goP.TraceLine("Setting field TME_ENDTIME '" & "Now" & "'", "", sProc)
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            // goP.TraceLine("Setting field DTE_STARTTIME '" & "Today" & "'", "", sProc)
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            // goP.TraceLine("Setting field DTE_ENDTIME '" & "Today" & "'", "", sProc)
            doNew.SetFieldVal("DTE_ENDTIME", "Today");
            // goP.TraceLine("Setting field LNK_CreditedTo_US '" & goP.GetMe("ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_Related_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_CN", doLink);
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_Related_PD", ref doLink, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_PD", doLink);
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_Related_GR", ref doLink, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_GR", doLink);
            // goP.TraceLine("Setting field EML_EMAIL '" & doForm.doRS.GetFieldVal("EML_EMAIL") & "'", "", sProc)
            doNew.SetFieldVal("EML_EMAIL", doForm.doRS.GetFieldVal("EML_EMAIL"));
            // goP.TraceLine("Setting field TEL_FAX '" & doForm.doRS.GetFieldVal("TEL_FAX") & "'", "", sProc)
            doNew.SetFieldVal("TEL_FAX", doForm.doRS.GetFieldVal("TEL_FAX"));
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_Related_CO", ref doLink, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_CO", doLink);
            // goP.TraceLine("Setting field MLS_TYPE '" & 31 & "'", "", sProc)
            doNew.SetFieldVal("MLS_TYPE", 31, 2);      // Journal
                                                       // goP.TraceLine("Setting field MMO_HISTORY '" & goTR.WriteLogLine(doForm.GetFieldVal("MMO_HISTORY"), "Created.") & "'", "", sProc)
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine("", "Created."));
            // TLD 6/29/2009 Fills required Purpose field
            doNew.SetFieldVal("MLS_Purpose", 99, 2);     // Other

            // goP.TraceLine("Setting field LNK_Connected_AC '" & doForm.GetFieldVal("GID_ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_Related_AC", doForm.doRS.GetCurrentRecID());
            // goP.TraceLine("About to commit.", "", sProc)

            if (doNew.Commit() != 1)
            {
                // delete(doNew)
                // delete(doLink)
                doNew = null;
                doLink = null;

                string sError = goErr.GetLastError();
                // Dim sMessage As String
                sMessage = goErr.GetLastError("MESSAGE");
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages")?.ToString() + sMessage + Constants.vbCrLf);

                goLog.Log("Activity_CreateActLog", doForm.oVar.GetVar("ScriptMessages")?.ToString(), 1, false, true);

                par_doCallingObject = doForm;
                return false;
            }

            doNew = null;
            doLink = null;

            par_doCallingObject = doForm;
            return true;
        }
        public bool AutoAlertEveryDay_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter in all rowsets to get all users' private recs
            // MI 10/19/07 started converting the script to be aware of the start of the day depending on the user's time zone
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DETAILS, true);

            Form doForm = (Form)par_doCallingObject;
            // *** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
            // It is unlikely that there would be non-shared User records, but just in case...
            var rsUsers = new clRowSet("US", clC.SELL_READONLY, "CHK_ACTIVEFIELD=1", "SYS_Name", "GID_ID", -1, null, null, null, null, null, false, false, true);

            // No active users
            if (rsUsers.Count() == 0)
            {
                par_doCallingObject = doForm;
                return true;
            }

            // PURPOSE:
            // Every day display alerts about things that require review or followup,
            // QT Follow-up Sent and QT Still Active Sent alerts.
            // The alerts are typically set up to open an appropriate desktop.
            // This replaces 'alarm' agents in Selltis 1.0. The name of the agent 
            // is on the top of each section of code.
            // MI 10/19/07: Making the script aware of ea user's time zone so that
            // the alerts, auto-updates, rescheduling, etc. occurs after midnight
            // user's time and not server's time. See additional notes in the code.
            // HOW THIS WORKS:
            // This script runs hourly by an automator AGE_2005072616363748192MAR 00002XX
            // that has the following definition:
            // A_01_EXECUTE = AutoAlertEveryDay
            // A_01_OBJSHARED = 1
            // A_01_TYPE = RUNSCRIPT
            // A_ORDER=1,
            // ACTIONS=1,
            // ACTIVE = 1
            // E_TM_HOUR = 0
            // E_TM_INTERVAL = 2
            // E_TM_MINUTE = 1
            // E_TM_MISSED = 1
            // EVENT=TIMER
            // US_NAME=AutoAlertEveryDay
            // US_PURPOSE=Add alerts every morning for followup of leads, contacts, to dos, etc.
            // SORTVALUE1=TIMER_ACTIVE
            // Make sure the timer automator engine is running on this site's web server.
            // WARNING:
            // Tread carefully - this is a very sensitive area.

            clRowSet doRS;
            string sResult;
            // Dim sStartDate As String
            // Dim sDueDate As String
            bool bMore = true;
            string sCurrentUser = "";
            // Dim dtDate As Date
            // Dim lDays As Long
            string sPointers;
            string sPointerDateTime;
            DateTime dtPointerDate;
            PublicDomain.TzTimeZone zone;
            DateTime dtUsersNow;       // User time zone 'now'
            DateTime dtUsersToday;     // User time zone Today (now.Date())
            string sDateTime;
            DateTime dtDateTime;
            // Dim sStartTime As String
            // Dim sEndTime As String

            // Get a page with date stamps of last dates when daily timer script was processed for ea user. Ex:
            // USER1GID_ID=2008-03-25
            // USER2GID_ID=2008-03-22
            // ...
            // where USERnGID_ID is a Selltis global ID (SUID).
            sPointers = goMeta.PageRead("GLOBAL", "OTH_DAILY_SCRIPT_PROCESSED");


            // --------------------- Non-user-dependent updates -----------------------

            // ---------------------- Per-user processing loop ------------------
            while (bMore == true)
            {

                // The timer automator fires this script every hour.
                // Here we make sure the actual update is made only once in a day for each user.
                // This is very important because we are modifying data (changing Appointment 
                // date field values, for example) for the whole day. This should not be done 
                // piece meal or it will be confusing to users.

                // A 'day' is seen from the perspective of the currently processed user's
                // current time zone. If the user switches to a different time zone within 
                // the same day, the processing should not occur again until the next day 
                // starts in that time zone. As a consequence, for users traveling west 
                // this script may not fire for up to an extra day. Users traveling east 
                // will have the processing occur in less than 24 hours from the last time it ran.

                // Read user's 'last processed' date (recorded in user's time zone at the time)
                sCurrentUser = rsUsers.GetFieldVal("GID_ID", clC.SELL_FRIENDLY)?.ToString();

                // DEBUG 
                // goLog.Log(sProc, "  Processing user '" & goData.GetRecordNameByID(sCurrentUser) & "' [" & sCurrentUser & "]", clC.SELL_LOGLEVEL_DETAILS)
                // END DEBUG

                // Get user's time zone
                zone = goTR.UTC_GetUserTimeZone(sCurrentUser);
                // Get user's 'now' and 'today at midnight' in the user's current time zone
                dtUsersNow = zone.ToLocalTime(goTR.NowUTC());   // User's current datetime in his/her time zone
                dtUsersToday = dtUsersNow.Date;                  // Midnight on user's current date in his/her time zone
                                                                 // Get the 'last processed' date (no time) as a local datetime
                                                                 // Pointers are written as local datetimes in user's last login time zone.
                sPointerDateTime = Strings.Left(goTR.StrRead(sPointers, sCurrentUser, "", false), 10);
                par_iValid = 3;
                if (sPointerDateTime == "")
                    // Leaving one whole day ahead of 'blank' datetime: '1753-01-02 23:59:59.000'.
                    dtPointerDate = goTR.StringToDate("1753-01-04", "", ref par_iValid);
                else
                    dtPointerDate = goTR.StringToDate(sPointerDateTime, clC.SELL_FORMAT_DATEDEF, ref par_iValid).Date;
                dtPointerDate = DateTime.SpecifyKind(dtPointerDate, DateTimeKind.Local);

                // *** MI 10/23/07 Replaced >= with just = to avoid not processing for days or months in case 
                // the server clock was accidentally set ahead and the processing occurred... In that case, 
                // pointer datetimes set far in the future would preclude running this.
                // If dtPointerDate >= dtUsersToday Then GoTo ProcessNextUser

                // DEBUG
                // goLog.Log(sProc, "    Pointer date: '" & goTR.DateTimeToSysString(dtPointerDate) & "' User's date: '" & goTR.DateTimeToSysString(dtUsersToday) & "'", clC.SELL_LOGLEVEL_DETAILS)
                // END DEBUG

                if (dtPointerDate == dtUsersToday)
                    goto ProcessNextUser;
                // --------- Set 'today' -------------
                // sDateTime replaces the Selltis keyword 'Today' with the datetime value that corresponds to midnight
                // in the currently processed user's time zone
                dtDateTime = goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday));
                // TLD 7/2/2009 Sets to 15 days before today
                // to compare to follow-up sent and still active sent dates
                dtDateTime = goTR.AddDay(dtDateTime, -15);
                par_iValid = 3;
                string par_sDelim = "|";
                sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_sDelim);

                // TLD 7/2/2009 Desktop alert for Quote Followups to Review
                // ---------- Quote FollowUps to Review ----------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertFollowUpQuotes", true))
                {
                    // Quote:DailyFollowUpAlert
                    // sDateTime: tomorrow
                    // *** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                    doRS = new clRowSet("QT", 3, "MLS_STATUS=0 AND CHK_STILLACTIVE=0 AND DTT_FOLLOWSENT<'" + sDateTime + "' AND LNK_FOLLOWUP_US='" + sCurrentUser + "'", null, "GID_ID", 1, null, null, null, null, null, false, false, true);
                    // goLog.Log("autoalerteveryday:quotes", doRS.Count, , , True)
                    if (doRS.GetFirst() == 1)
                        sResult = goUI.AddAlert("Follow-Up Quotes", "OPENDESKTOP", "DSK_B8DEF088-623B-4939-5858-9C39012D6D3B", sCurrentUser, "QUOTE16.gif").ToString();
                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                }

                // TLD 7/2/2009 Desktop alert for Quotes Still Active to Review
                // ---------- Quote Still Active to Review ----------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AlertStillActiveQuotes", true))
                {
                    // Quote:DailyStillActiveAlert
                    // sDateTime: tomorrow
                    // *** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                    doRS = new clRowSet("QT", 3, "MLS_STATUS=0 AND DTT_STILLACTSENT<'" + sDateTime + "' AND LNK_STILLACTIVE_US='" + sCurrentUser + "'", null, "GID_ID", 1, null, null, null, null, null, false, false, true);
                    // goLog.Log("autoalerteveryday:quotes", doRS.Count, , , True)
                    if (doRS.GetFirst() == 1)
                        sResult = goUI.AddAlert("Still Active Quotes", "OPENDESKTOP", "DSK_A09A5B44-422E-47DA-5858-9C39013AB2E4", sCurrentUser, "QUOTE16.gif").ToString();
                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                }

                // 'TLD 8/24/2009 Not necessary to update here -- Main gets it
                // 'Update the daily processing pointer with current datetime in the processed user's time zone
                // goMeta.LineWrite("GLOBAL", "OTH_DAILY_SCRIPT_PROCESSED", sCurrentUser, goTR.DateTimeToSysString(dtUsersNow))

                ProcessNextUser:
                ;
                if (rsUsers.GetNext() == 0)
                    bMore = false;
            }

            rsUsers = null/* TODO Change to default(_) if this is not a reference type */;

            // DEBUG: 
            // goLog.Log(sProc, "End: Return True", clC.SELL_LOGLEVEL_DETAILS, True)
            // END DEBUG
            par_doCallingObject = doForm;
            return true;
        }
        public bool AutoQuoteCreateRevision_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 1/24/08 Changed from 1 to 0 to make the first rev to be Rev 1 not 2.
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 7/20/2009 Prevents main from running
            // Customizes for custom Quote Numbers
            par_bRunNext = false;

            string sID = "";
            clRowSet doRowset;
            string sFileName;
            Form doF;
            clRowSet doAllQTs;
            string sQuoteNo;
            string sQuoteOnlyNo;      // Quote number without the revision
            int iRevNo;
            int iRevNoTemp;
            int iLen;
            int i;
            int iPos;
            string sQuoteTitleOnly;

            // Check selected record
            //sID = goUI.GetLastSelected("SELECTEDRECORDID");
            string Key = "";
            if (Util.GetSessionValue("LastDesktopHistoryKey") != null)
            {
                Key = Util.GetSessionValue("LastDesktopHistoryKey").ToString();
            }

            if (Util.GetSessionValue(Key + "_" + "SelectedRecordID") != null)
                sID = Util.GetSessionValue(Key + "_" + "SelectedRecordID").ToString();
            // goP.TraceLine("sID: " & sID & "'", "", sProc)
            sFileName = goTR.GetFileFromSUID(sID).ToUpper();
            if (sFileName != "QT")
            {
                goUI.NewWorkareaMessage("Please select a Quote first.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            // Check if have permissions
            if (goData.GetAddPermission("QT") == false)
            {
                goUI.NewWorkareaMessage("You cannot create a revision of the selected Quote because you don't have permissions to create Quotes.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            // Copy the selected record
            // Get doRowset of current record
            doRowset = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + sID + "'", null, "**", 1);
            if (doRowset.Count() < 1)
            {
                goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user. Select a different record and start again.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }


            // ------------------- Update revision no in Quote No -------------------
            sQuoteNo = Convert.ToString(doRowset.GetFieldVal("TXT_QuoteNo", 2)).Trim();
            if (sQuoteNo == "")
            {
                //goScr.RunScript("Quote_GenerateQuoteNo", null, null, null, null, null, null, null, sQuoteNo)
                par_doCallingObject = null;
                if (!scriptManager.RunScript("Quote_GenerateQuoteNo", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, sQuoteNo))
                    return false;
            }
            if (sQuoteNo == "")
            {
                // Should never happen
                goErr.SetError(35000, sProc, "Quote number is blank.");
                return false;
            }

            // Get the quote no without the revision number
            sQuoteOnlyNo = sQuoteNo;

            // If Quote contains "R." or "-Rev." then revision
            if (goTR.CountOccurrences(sQuoteOnlyNo, "-R.") == 1)
            {
                // Is a revision
                // Need to trim it to get original quote #
                iPos = Strings.InStr(sQuoteOnlyNo, "-R.");
                sQuoteOnlyNo = Strings.Left(sQuoteOnlyNo, iPos - 1);
            }

            if (goTR.CountOccurrences(sQuoteOnlyNo, "-Rev.") == 1)
            {
                // Is a revision
                // Need to trim it to get original quote #
                iPos = Strings.InStr(sQuoteOnlyNo, "-Rev.");
                sQuoteOnlyNo = Strings.Left(sQuoteOnlyNo, iPos - 1);
            }

            // 'Not necessary, disables Quote No for user entry
            // 'Limit the length of the quote no just in case someone mangled it by hand
            // iLen = Len(sQuoteOnlyNo)
            // If iLen > 14 Then
            // iLen = 14
            // sQuoteOnlyNo = Left(sQuoteOnlyNo, 14)
            // End If

            // Determine the highest revision no
            doAllQTs = new clRowSet(sFileName, clC.SELL_READONLY, "TXT_QuoteNo[='" + goTR.PrepareForSQL(sQuoteOnlyNo) + "'", "DTT_Time DESC", "TXT_QuoteNo");    // MI 11/6/08 Added goTr.PrepareForSQL
            iRevNo = 0;  // *** MI 1/24/08 Changed from 1 to 0 to make the first rev to be Rev 1 not 2.
            for (i = 1; i <= doAllQTs.Count(); i++)
            {
                sQuoteNo = Convert.ToString(doAllQTs.GetFieldVal("TXT_QuoteNO"));
                iPos = Strings.InStr(sQuoteNo, "-");

                if (goTR.CountOccurrences(sQuoteNo, "-Rev.") == 1)
                {
                    // The number contains a revision no
                    iPos = Strings.InStr(sQuoteNo, ".");
                    iLen = Strings.Len(sQuoteNo);
                    par_iValid = 4;
                    iRevNoTemp = Convert.ToInt32(goTR.StringToNum(Strings.Right(sQuoteNo, iLen - iPos), "", ref par_iValid));
                    if (iRevNoTemp > iRevNo)
                        iRevNo = iRevNoTemp;
                }

                if (goTR.CountOccurrences(sQuoteNo, "-R.") == 1)
                {
                    par_iValid = 4;
                    // The number contains a revision no
                    iPos = Strings.InStr(sQuoteNo, ".");
                    iLen = Strings.Len(sQuoteNo);
                    iRevNoTemp = Convert.ToInt32(goTR.StringToNum(Strings.Right(sQuoteNo, iLen - iPos), "", ref par_iValid));
                    if (iRevNoTemp > iRevNo)
                        iRevNo = iRevNoTemp;
                }
                if (doAllQTs.GetNext() != 1)
                    break;
            }

            // Advance the revision number by 1, but not if revision is 1.
            iRevNo = iRevNo + 1;
            if (iRevNo > 999)
                iRevNo = 999;
            // Remove all spaces - there can be a space after the user code if shorter than 4 chars
            sQuoteNo = goTR.Replace(sQuoteOnlyNo, " ", "_");
            // 'Ensure fixed length - commented because it makes numbers ugly with "_"
            // sQuoteNo = goTR.Pad(sQuoteNo, 14, "_")
            // TLD 7/23/2009 Modified to Ensure "Rev. " appears in Quote #
            sQuoteNo = sQuoteOnlyNo + "-" + "Rev. " + iRevNo;
            if (doAllQTs != null)
                doAllQTs = null/* TODO Change to default(_) if this is not a reference type */;

            // Create the new form
            doF = new Form(sFileName, "", "CRU_" + sFileName);

            // Copy this quote to the new form's rowset
            clRowSet doRTemp = doF.doRS;
            if (!goData.CopyRecord(ref doRowset, ref doRTemp))
            {
                doF.doRS = doRTemp;
                goErr.SetError(35000, sProc, "Copying the selected Quote '" + sID + "' failed.");
                return false;
            }

            // doF.doRS.SetFieldVal("GID_ID", goData.GenerateID(sFileName))
            doF.doRS.SetFieldVal("LNK_CreatedBy_US", goP.GetUserTID());
            doF.doRS.SetFieldVal("DTT_Time", "Today|Now");
            // CS 8/3/07: Reason and Status of revised QT should be same as original QT
            // doF.doRS.SetFieldVal("MLS_Status", 0, clC.SELL_SYSTEM)    'Open
            // doF.doRS.SetFieldVal("MLS_ReasonWonLost", 0, clC.SELL_SYSTEM)    '<Make selection>
            doF.doRS.SetFieldVal("DTT_DateClosed", "");
            // Set History tab & Cloned From Qt
            doF.doRS.SetFieldVal("MMO_History", "Revision of Quote" + doRowset.GetFieldVal("SYS_Name")?.ToString());
            doF.doRS.SetLinkVal("LNK_ClonedFrom_QT", sID);
            sQuoteTitleOnly = doRowset.GetFieldVal("TXT_QuoteTitle")?.ToString();
            iPos = Strings.InStr(sQuoteTitleOnly, " REV ");
            if (iPos > 0)
                sQuoteTitleOnly = Strings.Left(sQuoteTitleOnly, iPos - 1);
            doF.doRS.SetFieldVal("TXT_QuoteTitle", sQuoteTitleOnly + " REV " + iRevNo.ToString());
            doF.doRS.SetFieldVal("TXT_QuoteNo", sQuoteNo);
            doF.doRS.ClearLinkAll("LNK_Connected_QL");
            doF.SetControlVal("NDB_MMO_Lines", doRowset.GetFieldVal("LNK_Connected_QL%%SYS_Name")?.ToString());      // Lines will display as a memo
            doF.oVar.SetVar("QuoteOpeningMode", "Revision");
            doF.oVar.SetVar("QuoteOrinalQuoteID", doRowset.GetFieldVal("GID_ID"));
            // TLD 7/20/2009 Clear Follow-Up Sent and Still Active fields
            doF.doRS.SetFieldVal("CHK_FOLLOWUP", 0, 2);
            doF.doRS.SetFieldVal("CHK_STILLACTIVE", 0, 2);
            doF.doRS.ClearLinkAll("LNK_FOLLOWUP_US");
            doF.doRS.ClearLinkAll("LNK_STILLACTIVE_US");
            doF.doRS.SetFieldVal("DTE_FOLLOWSENT", "");
            doF.doRS.SetFieldVal("DTE_STILLACTSENT", "");
            // SKO 12/24/2015  Ticket#858
            doF.doRS.SetFieldVal("MLS_Status", 1, 2);     // US_1=80 Open

            doF.MessagePanel("This is a revision of the Quote '" + doRowset.GetFieldVal("SYS_Name")?.ToString() + "'." + Constants.vbCrLf + "Check the Title and Quote Number and click Save or click Modify Lines to add, edit or remove them.", null, null, "Info.gif");


            // 'SKO 12/24/2015  Ticket#858 Create Revision of Selected Quote” auto change the selected Quote “MLS_Status” to “60 Revised” 
            // If doRowset.Count > 0 Then
            // doRowset.SetFieldVal("MLS_Status", 6, clC.SELL_SYSTEM)  'US_6=60 Revised
            // If doRowset.Commit() <> 1 Then
            // End If
            // End If


            goUI.Queue("FORM", doF);

            // Clean up objects
            doRowset = null/* TODO Change to default(_) if this is not a reference type */;

            return true;
        }
        // Function QT_FormAfterSave_Post(Optional ByRef par_doCallingObject As Object = vbNull, Optional ByVal par_doArray As clArray = Nothing, Optional ByVal par_sFormAlias As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "", Optional ByRef par_oReturn As Object = vbNull, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
        // 'par_doCallingObject: Form object calling this script. Do not delete in script!
        // 'par_doArray: Unused.
        // 'par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
        // 'par_s2 to par_s5: Unused.
        // 'par_oReturn: Object passed to script ByRef.  Used to return values to calling process
        // 'par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
        // 'par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

        // Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        // goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        // 'MI 4/26/07

        // Dim doForm As Form = par_doCallingObject
        // Return True
        // End Function
        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CalcQuoteTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sCityMaining = Convert.ToString(doForm.doRS.GetFieldVal("TXT_CITYMAILING", 2)).ToUpper();

            // TLD 7/13/2009 Tracks URL_WEBPAGE contents
            // Checks this in formonsave, asks user if they want to 
            // update CN URL_WEB
            goP.SetVar("CO_WEBPAGE", doForm.doRS.GetFieldVal("URL_WEBPAGE"));

            // SKO 12/03/2015  Ticket#831 Auto fill territory(LNK_IN_TE) on CO main form with the territory value linked in the connected states field(LNK_CONNECTED_SE%%LNK_RELATEDTERRITORY_TE)
            doForm.doRS.SetFieldVal("LNK_IN_TE", doForm.doRS.GetFieldVal("LNK_CONNECTED_SE%%LNK_RELATEDTERRITORY_TE", 2), 2);

            // SKO 12/07/2015  Ticket#831
            if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_CONNECTED_SE")).ToUpper() == "34303538-3761-3332-5345-31322F322F32")
            {
                if (sCityMaining == "HOUSTON" | sCityMaining == "AUSTIN" | sCityMaining == "SAN ANTONIO" | sCityMaining == "BEAUMONT" | sCityMaining == "PORT AUTHOR")
                    doForm.doRS.SetFieldVal("LNK_IN_TE", "3F778534-9E0F-458A-5445-98DD014161CF"); // Houston (Houston)
                else
                    doForm.doRS.SetFieldVal("LNK_IN_TE", "724D0F49-3CD4-4CD9-5445-98DD014161CF");// West Coast  (West Coast )
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_FormControlOnChange_LNK_CONNECTED_SE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sCityMaining = Convert.ToString(doForm.doRS.GetFieldVal("TXT_CITYMAILING")).ToUpper();

            // SKO 12/03/2015  Ticket#831 Auto fill territory(LNK_IN_TE) on CO main form with the territory value linked in the connected states field(LNK_CONNECTED_SE%%LNK_RELATEDTERRITORY_TE)
            doForm.doRS.SetFieldVal("LNK_IN_TE", doForm.doRS.GetFieldVal("LNK_CONNECTED_SE%%LNK_RELATEDTERRITORY_TE", 2), 2);

            // SKO 12/09/2015  Ticket#836  fill in TXT_STATEMAILING on CO form with LNK_CONNECTED_SE%%TXT_STATE when Connected state is selected. 
            // If doForm.GetMode() = "CREATION" Then
            doForm.doRS.SetFieldVal("TXT_STATEMAILING", doForm.doRS.GetFieldVal("LNK_CONNECTED_SE%%TXT_STATE"));
            // End If

            // SKO 12/07/2015  Ticket#831
            if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_CONNECTED_SE")).ToUpper() == "34303538-3761-3332-5345-31322F322F32")
            {
                if (sCityMaining == "HOUSTON" | sCityMaining == "AUSTIN" | sCityMaining == "SAN ANTONIO" | sCityMaining == "BEAUMONT" | sCityMaining == "PORT AUTHOR")
                    doForm.doRS.SetFieldVal("LNK_IN_TE", "3F778534-9E0F-458A-5445-98DD014161CF"); // Houston (Houston)
                else
                    doForm.doRS.SetFieldVal("LNK_IN_TE", "724D0F49-3CD4-4CD9-5445-98DD014161CF");// West Coast  (West Coast )
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            var sOldWebPage = goP.GetVar("CO_WEBPAGE");
            var sNewWebPage = doForm.doRS.GetFieldVal("URL_WEBPAGE");
            string sCityMaining = Convert.ToString(doForm.doRS.GetFieldVal("TXT_CITYMAILING")).ToUpper();

            // TLD 7/13/2009 Checks for change in URL_WEBPAGE
            // if changed asks user if they want to update
            // Contact of User records with new URL_WEBPAGE
            if (Convert.ToString(doForm.oVar.GetVar("CO_FormOnSave_UpdateWebAddress")) != "1")
            {
                if (doForm.GetMode() != "CREATION")
                {
                    if (sOldWebPage != sNewWebPage)
                    {
                        doForm.oVar.SetVar("CO_FormOnSave_UpdateWebAddress", "1");
                        doForm.MessageBox("You changed the Company web page address.  Do you want to replace the web page of the contacts?" + Constants.vbCrLf + Constants.vbCrLf + "This may take a few minutes.", clC.SELL_MB_YESNO, "Update Contact Web Page", "Yes", "No", null, null, "MessageBoxEvent", "MessageBoxEvent", null, doForm, null, "Yes", "No", null, Convert.ToString(sNewWebPage), "CO_FormOnSave_UpdateWebAddress");
                        par_doCallingObject = doForm;
                        return true;
                    }
                }
            }

            // SKO 12/03/2015  Ticket#831 Auto fill territory(LNK_IN_TE) on CO main form with the territory value linked in the connected states field(LNK_CONNECTED_SE%%LNK_RELATEDTERRITORY_TE)
            doForm.doRS.SetFieldVal("LNK_IN_TE", doForm.doRS.GetFieldVal("LNK_CONNECTED_SE%%LNK_RELATEDTERRITORY_TE", 2), 2);

            // SKO 12/03/2015  Ticket#831
            // if the value in "connected states" field on the CO form is empty then link the territory(LNK_IN_TE) value to "International Territory" on save.
            if (doForm.doRS.GetLinkCount("LNK_CONNECTED_SE") == 0)
                doForm.doRS.SetFieldVal("LNK_IN_TE", "E0E6F2D8-F488-4097-5445-98DD014161CF");// International (International)

            // SKO 12/03/2015  Ticket#836  Fill in TXT_STATEMAILING on CO form with LNK_CONNECTED_SE%%TXT_STATE for all the new Companies
            if (doForm.GetMode() == "CREATION")
                doForm.doRS.SetFieldVal("TXT_STATEMAILING", doForm.doRS.GetFieldVal("LNK_CONNECTED_SE%%TXT_STATE"));

            // SKO 12/07/2015  Ticket#831
            if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_CONNECTED_SE")).ToUpper() == "34303538-3761-3332-5345-31322F322F32")
            {
                if (sCityMaining == "HOUSTON" | sCityMaining == "AUSTIN" | sCityMaining == "SAN ANTONIO" | sCityMaining == "BEAUMONT" | sCityMaining == "PORT AUTHOR")
                    doForm.doRS.SetFieldVal("LNK_IN_TE", "3F778534-9E0F-458A-5445-98DD014161CF"); // Houston (Houston)
                else
                    doForm.doRS.SetFieldVal("LNK_IN_TE", "724D0F49-3CD4-4CD9-5445-98DD014161CF");// West Coast  (West Coast )
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sCurVol = "";
            string sPotVol = "";
            int iCurCount = Convert.ToInt32(doRS.GetLinkCount("LNK_Current_PD"));
            int iPotCount = Convert.ToInt32(doRS.GetLinkCount("LNK_Potential_PD"));

            // SKO 12/15/2015  Ticket#848 
            // TLD 3/28/2013 ---------Target Account Matrix Profiling 
            // Copy LNK_Current_PD to LNK_Potential_PD
            doRS.SetFieldVal("LNK_Potential_PD", doRS.GetFieldVal("LNK_Current_PD"));

            // Record total selections from LNK_Current_PD and LNK_Potential_PD
            doRS.SetFieldVal("INT_CURCOUNT", iCurCount, 2);
            doRS.SetFieldVal("INT_POTCOUNT", iPotCount, 2);

            // Calculate Potential Percentage & Potential Portfolio
            // Get number of all products
            clRowSet doPDRS = new clRowSet("PD", 6, null, null, "GID_ID");
            int iPDCount = Convert.ToInt32(doPDRS.GetFieldVal("BI__COUNT"));
            if (iPDCount != 0)
            {
                if (iPotCount != 0)
                    doRS.SetFieldVal("SR__POTENTIALPERC", (iCurCount / (double)iPotCount) * 100, 2);
                doRS.SetFieldVal("SR__POTENTIALPORTFOLIO", (iPotCount / (double)iPDCount) * 100, 2);

                doRS.SetFieldVal("SR__ProdLinePot", (iPotCount - iCurCount), 2);
                sCurVol = Strings.Left(Convert.ToString(doRS.GetFieldVal("MLS_CURVOLUME")), 1);
                sPotVol = Strings.Left(Convert.ToString(doRS.GetFieldVal("MLS_POTVOLUME")), 1);
                if (sCurVol == "<")
                    sCurVol = "Z";
                if (sPotVol == "<")
                    sPotVol = "Z";

                // set field to cur & pot
                doRS.SetFieldVal("TXT_CURRANDPOT", sCurVol + sPotVol);
            }

            // Set Product Potential Quadrant
            double rTotalPortfolio = Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPORTFOLIO", 2));
            double rPotentialProduct = Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPERC", 2));

            if (rTotalPortfolio >= 51 & rTotalPortfolio <= 100)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                    // Set to 1
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "1");

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                    // Set to 3
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "3");
            }

            if (rTotalPortfolio >= 0 & rTotalPortfolio <= 50)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                    // Set to 2
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "2");

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                    // Set to 4
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "4");
            }
            // TLD 3/28/2013 Because COs are updated nightly to set custom
            // date fields, need to write to custom mod time and mod by fields
            // AutoCOUpdate does NOT run recordonsave
            doRS.SetFieldVal("TXT_CusModBy", goP.GetMe("CODE"));
            doRS.SetFieldVal("DTT_CusModTime", "Today|Now");
            // TLD 3/28/2013 ---------Target Account Matrix Profiling
            par_doCallingObject = doRS;
            return true;
        }
        public bool COProfileMatrixTabUpdate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            // SKO 01082016 TKT#883
            // Automator Runs Daily Script - AGE_87e240dc-fbfc-4128-5858-a58700b1469b
            par_bRunNext = false;
            string sQuery;
            string sQuery1;
            string sQuery2;

            try
            {
                goMeta.LineWrite("GLOBAL", "OTH_CUSTOM_DAILY_SCRIPT_PROCESSED", "COProfileMatrixTabUpdateStart", DateTime.UtcNow.ToString(), ref par_oConnection);

                // CUR_LMQUOTEINPUT = (Sum of all the "Total‘s" on the Quote‘s that are linked to the company (LNK_INVOLVEDIN_QT) AND are created during last month) 
                sQuery = " UPDATE CO SET CO.CUR_LMQUOTEINPUT = TBL.CUR_LMQUOTEINPUT " + Constants.vbCrLf + " FROM CO  " + Constants.vbCrLf + " JOIN (SELECT CO.GID_ID,SUM(QT.CUR_SUBTOTAL) CUR_LMQUOTEINPUT " + Constants.vbCrLf + "       FROM CO " + Constants.vbCrLf + "       JOIN QT_INVOLVES_CO B ON CO.GID_ID = B.GID_CO " + Constants.vbCrLf + "       JOIN QT ON QT.GID_ID = B.GID_QT " + Constants.vbCrLf + "       WHERE QT.DTT_CREATIONTIME >= DateAdd(Month, DateDiff(Month, 0, GETUTCDATE()) - 1, 0) " + Constants.vbCrLf + "       AND QT.DTT_CREATIONTIME < DATEADD(MONTH, DATEDIFF(MONTH, 0, GETUTCDATE()), 0)	" + Constants.vbCrLf + "       GROUP BY CO.GID_ID" + Constants.vbCrLf + "     ) TBL ON CO.GID_ID = TBL.GID_ID";

                // CUR_TOTALOPENQT = (Sum of all the "Total‘s" on the Quote‘s that are linked to the company (LNK_INVOLVEDIN_QT) AND with "status= Open") 
                sQuery1 = " UPDATE CO SET CO.CUR_TOTALOPENQT = TBL.CUR_TOTALOPENQT " + Constants.vbCrLf + " FROM CO " + Constants.vbCrLf + "JOIN (SELECT CO.GID_ID,sum(QT.CUR_SUBTOTAL) CUR_TOTALOPENQT " + Constants.vbCrLf + "       FROM CO " + Constants.vbCrLf + "       join QT_Involves_CO b ON CO.GID_ID = b.GID_CO " + Constants.vbCrLf + "       JOIN QT on QT.GID_ID = b.GID_QT " + Constants.vbCrLf + "       WHERE QT.MLS_Status = 1 " + Constants.vbCrLf + "       GROUP by CO.GID_ID	 " + Constants.vbCrLf + "     ) TBL ON CO.GID_ID = TBL.GID_ID";

                // CUR_QUOTEFORECAST90 ((Forcast - next 90days)= (Sum of all the totals on the Quote‘s that are linked to the company (LNK_INVOLVEDIN_QT) AND with "DTE_ExpCloseDate" is anytime in the next 90 days) 
                sQuery2 = "UPDATE CO SET CO.CUR_QUOTEFORECAST90 = TBL.CUR_QUOTEFORECAST90 " + Constants.vbCrLf + "FROM CO " + Constants.vbCrLf + "JOIN (SELECT CO.GID_ID,sum(QT.CUR_SUBTOTAL) CUR_QUOTEFORECAST90 " + Constants.vbCrLf + "      FROM CO " + Constants.vbCrLf + "      join QT_Involves_CO b ON CO.GID_ID = b.GID_CO " + Constants.vbCrLf + "      JOIN QT on QT.GID_ID = b.GID_QT " + Constants.vbCrLf + "      WHERE DTT_ExpCloseDate >= DateAdd(Day, DateDiff(Day, 0, GETUTCDATE()) + 1, 0) " + Constants.vbCrLf + "      and DTT_ExpCloseDate < DATEADD(day, DATEDIFF(day, 0, GETUTCDATE())+91, 0) " + Constants.vbCrLf + "      GROUP by CO.GID_ID	 " + Constants.vbCrLf + "    ) TBL ON CO.GID_ID = TBL.GID_ID";

                goData.RunSQLQuery(sQuery);

                goData.RunSQLQuery(sQuery1);

                goData.RunSQLQuery(sQuery2);

                goMeta.LineWrite("GLOBAL", "OTH_CUSTOM_DAILY_SCRIPT_PROCESSED", "COProfileMatrixTabUpdateEnd", DateTime.UtcNow.ToString(), ref par_oConnection);
            }
            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                {
                    goErr.SetError(ex, 45105, sProc);
                    return false;
                }
            }

            par_oReturn = "Success";

            return true;
        }
        public bool FillSignature_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // TLD 3/4/2010
            // PURPOSE:
            // Fill custom signature fields on Quote. Signature contains the first and last name of the user.
            // RETURNS:
            // True

            // Dim sEmplNameFirst As String
            // Dim sEmplNameLast As String
            // Dim sEmplInfo As String

            // If doRS.GetFieldVal("CHK_USESIGNATURE", 2) = 1 Then
            if (Convert.ToString(doRS.GetFieldVal("TXT_Signature2", 2)) == "")
                // CS 6/27/07: For some reason signature was being filled from Credited To User instead of from personal options.
                // It uses POP in Activity_FillSignature. Per DF, need to do the same way here.
                // sEmplNameFirst = doRS.GetFieldVal("LNK_CreditedTo_US%%TXT_NameFirst")
                // sEmplNameLast = doRS.GetFieldVal("LNK_CreditedTo_US%%TXT_NameLast")
                // doRS.SetFieldVal("TXT_Signature", sEmplNameFirst & " " & sEmplNameLast)
                doRS.SetFieldVal("TXT_Signature2", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_SIG2"));
            // End If
            // goP.TraceLine("GetFieldVal of MMO_UnderSignature (should be ''): '" & doForm.GetFieldVal("MMO_UNDERSIGNATURE") & "'")
            if (Convert.ToString(doRS.GetFieldVal("TXT_ABOVESIGNATURE2", 2)) == "")
                doRS.SetFieldVal("TXT_ABOVESIGNATURE2", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_ABOVESIG2"));
            if (Convert.ToString(doRS.GetFieldVal("MMO_UNDERSIGNATURE2", 2)) == "")
                doRS.SetFieldVal("MMO_UNDERSIGNATURE2", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_UNDERSIG2"));
            par_doCallingObject = doRS;
            return true;
        }
        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null/* TODO Change to default(_) if this is not a reference type */, string par_sFileName = "", string par_sReverseDirection = "0", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here. To not override the default sort, par_oReturn must be "".
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }
        public bool GenerateSysName_Pre(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            // MI 3/12/10 Added CASE "DF", modified Case Else to generate Name based on TXT_<FileName>Name field if exists.
            // CS 6/2/09 Modified QL case
            // CS 8/25/08 Case US: Added Title text
            // CS 8/20/08 Case CN: added Title Text
            // MI 11/14/07 Case CN: added TXT_ContactCode.
            // MI 10/15/07 Appended ' UTC' to all datetimes
            // MI 6/22/07 Changes to QT, OP, PR, PD, MO
            // MI 4/17/07 Added Phone Co to CN; Phone to CO ; removed padding from and to to 4 in MS; Status to OP, PR, QT, TD(?)
            // MI 3/9/07 Removed ellipsis from Co name in AC name.
            // MI 3/1/07 Added Contact, Company to AC Name.
            // MI 2/1/07 Added Date, Originator to Project name
            // MI 9/15/06 Updated QL, WT SYS_Name formats.
            // MI 7/25/06 Added raising error when field is not in the rowset.
            // MI 7/24/06 Mods. 
            // MI 7/20/06 Created in clScripts.
            // MI 7/20/06 Finished, tested, moved from clData to clScripts and renamed from GetCurrentRecordName to GetSysName.
            // MI 7/17/06 Started making this work in SellSQL.

            // AUTHOR: MI
            // PURPOSE:
            // Send back via the par_oReturn parameter the 'User Friendly' Name of the current record in par_oRowset.
            // This is to be called from each RecordOnSave script, but can be called
            // from any other code to generate a SYS_Name value. Do NOT set the name in 
            // the par_doCallingObject rowset or the script won't be usable simply for evaluating the returned
            // string.
            // IMPORTANT: Keep this "in sync" with clScripts.GetDefaultSort(), which is called from
            // clData.GetDefaultSort.
            // PARAMETERS:
            // par_doCallingObject: rowset object (for example, 'doRS'). The rowset must contain
            // all links and fields being referenced in the code below or error 45163 will be
            // raised. This can be achieved by putting '**' in the FIELDS parameter of the rowset, but 
            // avoid this when possible for performance reasons. The object, declared ByRef to conserve
            // resources by avoiding duplicating the object in memory, should not be altered directly
            // by this method (the purpose of the method is to return the name, not set it), but check
            // the code below to be sure.
            // par_doArray: not used
            // par_s1 - 5: not used
            // par_oReturn: String containing the generated SysName.
            // RETURNS:
            // True as a result. Returns friendly name or an empty string if the
            // filename is invalid via par_oReturn parameter.
            // EXAMPLE:
            // 'From a RecordOnSave script (not tested):
            // Dim sName as string = goScr.RunScript("GenerateSysName", doRS)
            // NOTES:
            // When a "Name" that is built with this method
            // is displayed in a View or linkbox and the same Name field is used
            // to sort the View or linkbox, at least the first field should match
            // the first field defined in clData::LKGetSortValue(). Otherwise what's
            // displayed will appear to be sorted arbitrarily.
            // NOTE 2:  
            // Links will not be tested because they are loaded automatically, but 
            // currently there is a bug in clRowset. RH working on this.

            string sProc = "clScripts:GenerateSysName";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sTemp4 = "";
            string sFileName = doRS.GetFileName();
            string sResult = "";
            clRowSet doLink;
            // Dim doLink As clRowSet
            // Dim iLen As Integer

            // We assume that sFileName is valid. If this is a problem, test it here and SetError.
            // SKO 12/03/2015  Ticket#831 Make SYS_NAME on SE form as "State Code - State" (TXT_STATE - TXT_STATEELEMENTNAME)
            switch (Strings.UCase(sFileName))
            {
                case "SE":
                    {
                        if (!doRS.IsLoaded("TXT_STATE"))
                            goErr.SetError(35103, sProc, null, sFileName + ".TXT_STATE");
                        if (!doRS.IsLoaded("TXT_STATEELEMENTNAME"))
                            goErr.SetError(35103, sProc, null, sFileName + ".TXT_STATEELEMENTNAME");
                        sResult = Convert.ToString(doRS.GetFieldVal("TXT_STATE")) + " - " + Convert.ToString(doRS.GetFieldVal("TXT_STATEELEMENTNAME"));
                        par_bRunNext = false;
                        par_oReturn = sResult;
                        break;
                    }

                case "QT":
                    {
                        // SKO 12/03/2015  Ticket#858
                        // Script a condition to fill the SYS_NAME on the Quote form to the format below.
                        // Format: Company name (25) – Contact last name(13), Description(15), Quote number QTS #(13), Total $(10)   
                        // LNK_TO_CO%%TXT_COMPANYNAME – LNK_ORIGINATEDBY_CN%%TXT_NAMELAST, TXT_DESCRIPTION, TXT_QUOTEREFNO, CUR_TOTAL

                        if (!doRS.IsLoaded("LNK_To_CO"))
                            goErr.SetError(35103, sProc, null, sFileName + ".LNK_To_CO");

                        if (!doRS.IsLoaded("LNK_ORIGINATEDBY_CN"))
                            goErr.SetError(35103, sProc, null, sFileName + ".LNK_ORIGINATEDBY_CN");

                        if (!doRS.IsLoaded("TXT_DESCRIPTION"))
                            goErr.SetError(35103, sProc, null, sFileName + ".TXT_DESCRIPTION");

                        if (!doRS.IsLoaded("TXT_QUOTEREFNO"))
                            goErr.SetError(35103, sProc, null, sFileName + ".TXT_QUOTEREFNO");

                        if (!doRS.IsLoaded("CUR_Total"))
                            goErr.SetError(35103, sProc, null, sFileName + ".CUR_Total");

                        // LNK_To_CO%%TXT_CompanyName
                        sTemp = doRS.GetFieldVal("LNK_To_CO", 0, -1, false, 1).ToString();
                        if (sTemp == "")
                            // No records linked
                            sTemp = "?";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("CO", 3, "GID_ID='" + sTemp + "'", null, "TXT_CompanyName", 1);
                            if (doLink.Count() > 0)
                                sTemp = doLink.GetFieldVal("TXT_CompanyName").ToString();
                            else
                                sTemp = "?";
                        }

                        // LNK_ORIGINATEDBY_CN%%TXT_NAMELAST
                        sTemp2 = doRS.GetFieldVal("LNK_ORIGINATEDBY_CN", 0, -1, false, 1)?.ToString();
                        if (sTemp2 == "")
                            // No records linked
                            sTemp2 = "?";
                        else
                        {
                            // Find the field value in the linked record
                            doLink = new clRowSet("CN", 3, "GID_ID='" + sTemp2 + "'", null, "TXT_NAMELAST", 1);
                            if (doLink.Count() > 0)
                                sTemp2 = doLink.GetFieldVal("TXT_NAMELAST")?.ToString();
                            else
                                sTemp2 = "?";
                        }
                        // Format: Company name (25) – Contact last name(13), Description(15), Quote number QTS #(13), Total $(10)   
                        // LNK_TO_CO%%TXT_COMPANYNAME – LNK_ORIGINATEDBY_CN%%TXT_NAMELAST, TXT_DESCRIPTION, TXT_QUOTEREFNO, CUR_TOTAL
                        // goTR.Pad(doRS.GetFieldVal("CUR_Total"), 10, , "L", True) & ""
                        sResult = goTR.Pad(sTemp, 25, null, null, true) + "-" + goTR.Pad(sTemp2, 13, null, null, true) + "," + goTR.Pad(doRS.GetFieldVal("TXT_DESCRIPTION")?.ToString(), 15, null, null, true) + "," + goTR.Pad(doRS.GetFieldVal("TXT_QUOTEREFNO")?.ToString(), 13, null, null, true) + "," + goTR.Pad(doRS.GetFieldVal("CUR_Total")?.ToString(), 10, null, "L", true);

                        par_bRunNext = false;
                        sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
                        sResult = goTR.Replace(sResult, Strings.Chr(10).ToString(), " ");
                        sResult = goTR.Replace(sResult, Strings.Chr(13).ToString(), " ");
                        sResult = goTR.Replace(sResult, Constants.vbTab, " ");
                        par_oReturn = sResult;
                        break;
                    }
            }


            // par_oReturn = sResult

            return true;
        }
        public bool MessageBoxEvent_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // Par_s5 will always be the name of the script that called doform.MessageBox
            // Par_s1 will be whatever button the user clicked.
            // Par_s2-Par_s4 can be whatever else you want to pass.
            // In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 4/24/2009 
            // When Complete All checked on Corr In Box - My
            // par_callingObject is clDesktop, so throws an error
            // so changed per Christy
            // Dim doForm As Form = par_doCallingObject
            Form doForm = (Form)par_doCallingObject;

            // TLD 7/13/2009 Added for updating CN web page from CO
            switch (Strings.UCase(par_s5))
            {
                case "CO_FORMONSAVE_UPDATEWEBADDRESS":
                    {
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // TLD 10/6/2010 Mod RS to optimize
                                    // Dim doCNRS As New clRowSet("CN", 1, "LNK_Related_CO='" & doForm.doRS.GetFieldVal("GID_ID") & "'", , , , , , , , , True, True)
                                    clRowSet doCNRS = new clRowSet("CN", 1, "LNK_Related_CO='" + doForm.doRS.GetFieldVal("GID_ID")?.ToString() + "'", "", "*", -1, "", "", "", "", "", true, true);
                                    if (doCNRS.Count() > 0)
                                    {
                                        do
                                        {
                                            // Clear contact web page
                                            doCNRS.SetFieldVal("URL_WEB", "");
                                            // Set the new webpage
                                            doCNRS.SetFieldVal("URL_WEB", par_s4);
                                            // Try to save the change. if the user doesn't have selective edit perm on the Contact, Commit will fail.
                                            if (doCNRS.Commit() != 1)
                                                goErr.SetWarning(35000, sProc, "Contact web page could not updated for '" + doCNRS.GetFieldVal("SYS_Name")?.ToString() + "'.");
                                            if (doCNRS.GetNext() != 1)
                                                break;
                                        }
                                        while (true);
                                        doCNRS = null/* TODO Change to default(_) if this is not a reference type */;
                                    }

                                    break;
                                }

                            case "NO":
                                {
                                    break;
                                }
                        }

                        break;
                    }
            }
            par_doCallingObject = doForm;
            return true;
        }
        //public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        //{
        //    // par_doCallingObject: Form object calling this script. Do not delete in script!
        //    // par_doArray: Unused.
        //    // par_s1: Unused.
        //    // par_s2 to par_s5: Unused.
        //    // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
        //    // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
        //    // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

        //    string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

        //    return true;
        //}
        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // VS 08192016 TKT#1215: Copy Territory from CO
            doForm.doRS.SetFieldVal("LNK_RELATED_TE", doForm.doRS.GetFieldVal("LNK_FOR_CO%%LNK_IN_TE", 2), 2);
            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // VS 08192016 TKT#1215: Copy Territory from CO
            doForm.doRS.SetFieldVal("LNK_RELATED_TE", doForm.doRS.GetFieldVal("LNK_FOR_CO%%LNK_IN_TE", 2), 2);
            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        //public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        //{
        //    // par_doCallingObject: Form object calling this script. Do not delete in script!
        //    // par_doArray: Unused.
        //    // par_s1: Unused.
        //    // par_s2 to par_s5: Unused.
        //    // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
        //    // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
        //    // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

        //    string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

        //    return true;
        //}
        public bool Opp_CreateActLog_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null/* TODO Change to default(_) if this is not a reference type */, string par_s1 = "0", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // 12/22/2004 RAH: turned over to MI
            // 2004/12/22 10:29:34 MAR Edited. SetLinkVals cause an error on line 37 of SetLinkVal: incorrect type.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/29/2009 Prevents main from running
            // Purpose required, so added to set purpose to "Other"
            par_bRunNext = false;

            // PURPOSE:
            // PJ 5/30/02 Adds Act Log with new notes of Opportunity.
            // Run from enforce only in non-CREATION (MODIF) mode.
            // If it is run in CREATION mode, the new Activity will not be linked to the original Opp.
            // RETURNS:
            // 1 if the Activity is created or didn't need to be created; 0 if not created or the user canceled.

            Form doForm = (Form)par_doCallingObject;
            string sNotes = "";
            string sWork = "";
            long lWork = 0;
            string sMessage;

            // 'If answered Yes on MB to create a journal, then go directly to script that creates journal.
            // If UCase(par_s1) = "YES" Then
            // GoTo CREATEACTLOG
            // End If

            if (Strings.Len(Convert.ToString(doForm.doRS.GetFieldVal("MMO_JOURNAL"))) <= Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"))) {
                par_doCallingObject = doForm;
                return true;
            }
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Opportunity is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages")?.ToString() + sMessage + Constants.vbCrLf);
                doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");
                // Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                par_doCallingObject = doForm;
                return true;
            }

            doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");

            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, null, null, null, -1, null, null, null, null, null, doForm.doRS.bBypassValidation);

            sWork = doForm.doRS.GetFieldVal("MMO_Journal")?.ToString();

            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, Convert.ToInt32(lWork));
            sNotes = sNotes + "== Created from Opportunity '" + doForm.doRS.GetFieldVal("SYS_Name")?.ToString() + "'";
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            doNew.SetFieldVal("MLS_Status", 1, 2);     // Completed
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            // CS doNew.SetFieldVal("TME_ENDTIME", doForm.doRS.GetFieldVal("TME_Time"))
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            doNew.SetFieldVal("DTE_ENDTIME", "Today");
            // TLD 6/29/2009 Sets required Purpose field
            doNew.SetFieldVal("MLS_Purpose", 99, 2);     // Other

            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));

            // In the code below Paul added IsObjectAssigned tests because SetLinkVals weren't working in some cases.
            // ==> Remove the IsObjectAssigned tests.
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_ORIGINATEDBY_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (10).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            doNew.SetLinkVal("LNK_Related_CN", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (11).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_FOR_PD", ref doLink, true, 0, -1, "A_a", ref oTable);    // PJ Changed link name from 'LNK_RELATED_PRODUCT' to 'LNK_FOR_PRODUCT'
                                                                                                          // If Not goP.IsObjectAssigned(doLink) Then
                                                                                                          // goP.TraceLine("doLink (LNK_Related_PD) is not assigned (12).", "", sProc)
                                                                                                          // '	goErr.DisplayLastError()
                                                                                                          // End If
            doNew.SetLinkVal("LNK_Related_PD", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_PD) is not assigned (13).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_Related_GR", ref doLink, true, 0, -1, "A_a", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_GR) is not assigned.", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            doNew.SetLinkVal("LNK_Related_GR", doLink);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_Related_GROUP) is not assigned (2).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_FOR_CO", ref doLink, true, 0, -1, "A_a", ref oTable);
            // If Not goP.IsObjectAssigned(doLink) Then
            // goP.TraceLine("doLink (LNK_FOR_CO) is not assigned (3).", "", sProc)
            // '	goErr.DisplayLastError()
            // End If
            doNew.SetLinkVal("LNK_RELATED_CO", doLink);
            // if goErr.GetLastError()<>"E00000" then
            // goErr.DisplayLastError()
            // End If

            doNew.SetFieldVal("MLS_TYPE", 31, 2);      // Journal
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine(Convert.ToString(doNew.GetFieldVal("MMO_HISTORY")), "Created."));
            doNew.SetFieldVal("LNK_RELATED_OP", doForm.GetRecordID());

            if (doNew.Commit() != 1)
            {
                doNew = null;
                doLink = null;
                string sError = goErr.GetLastError();
                sMessage = goErr.GetLastError("MESSAGE");

                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages")?.ToString() + sMessage + Constants.vbCrLf);

                goLog.Log("MessageBoxEvent", doForm.oVar.GetVar("ScriptMessages")?.ToString(), 1, false, true);
                par_doCallingObject = doForm;
                return false;
            }

            doNew = null;
            doLink = null;
            par_doCallingObject = doForm;
            return true;
        }
        public bool PR_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Project_CreateActLog_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/29/2009 Prevents main from running
            // Modified to add fill of Purpose -- required
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sWork;
            long lWork;
            string sNotes;
            string sMessage;

            // goP.TraceLine("", "", sProc)

            // PURPOSE:
            // Offer to create an Activity when the Journal in the Project has been edited.
            // RETURNS:
            // True if Activity was created or creation wasn't needed; False if the user declined creating an Activity.

            // CS: Per MI, no prompting
            // 'If answered Yes on MB to create a journal, then go directly to script that creates journal.
            // If UCase(par_s1) = "YES" Then
            // GoTo CREATEACTLOG
            // End If

            if (Strings.Len(Convert.ToString(doForm.doRS.GetFieldVal("MMO_JOURNAL"))) <= Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))
            {
                par_doCallingObject = doForm;
                return true;
            }
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Project is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages")?.ToString() + sMessage + Constants.vbCrLf);
                doForm.oVar.SetVar("Project_CreateActLog_Ran", "1");
                // Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                par_doCallingObject = doForm;
                return true;
            } // If doForm.GetMode() = "CREATION" Then Return True					'The record must be in edit mode so we have its ID for links to it.

            doForm.oVar.SetVar("Project_CreateActLog_Ran", "1");

            // CREATEACTLOG:
            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, null, null, null, -1, null, null, null, null, null, doForm.doRS.bBypassValidation);

            sWork = doForm.doRS.GetFieldVal("MMO_Journal")?.ToString();
            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, Convert.ToInt32(lWork));
            sNotes = sNotes + "== Created from Project '" + doForm.doRS.GetFieldVal("SYS_Name")?.ToString() + "'";
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            // CS goP.TraceLine("Setting field LNK_Involves_US '" & goP.GetMe("ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            // goP.TraceLine("Setting field MLS_Status '" & 1 & "'", "", sProc)
            doNew.SetFieldVal("MLS_Status", 1, 2);     // Completed
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            doNew.SetFieldVal("DTE_ENDTIME", "Today");
            // goP.TraceLine("Setting field TME_STARTTIME '" & "<%Now%>" & "'", "", sProc)
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            // goP.TraceLine("Setting field TME_ENDTIME '" & "<%Now%>" & "'", "", sProc)
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            // goP.TraceLine("Setting field DTE_STARTTIME '" & "<%Today%>" & "'", "", sProc)
            // doNew.SetFieldVal("DTE_STARTTIME", "Today")
            // goP.TraceLine("Setting field DTE_ENDTIME '" & "<%Today%>" & "'", "", sProc)
            // doNew.SetFieldVal("DTE_ENDTIME", doForm.GetFieldVal("DTE_StartTime"))
            // goP.TraceLine("Setting field LNK_CreditedTo_US '" & goP.GetMe("ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));
            doNew.SetFieldVal("LNK_RELATED_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN", 2), 2);
            doNew.SetFieldVal("LNK_RELATED_CO", doForm.doRS.GetFieldVal("LNK_FOR_CO", 2), 2);
            // goP.TraceLine("Setting field MLS_TYPE '" & 31 & "'", "", sProc)
            doNew.SetFieldVal("MLS_TYPE", 31, 2);      // Journal
                                                       // goP.TraceLine("Setting field MMO_HISTORY '" & goTR.WriteLogLine(doNew.GetFieldVal("MMO_HISTORY"), "Created.") & "'", "", sProc)
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine(Convert.ToString(doNew.GetFieldVal("MMO_HISTORY")), "Created."));
            // goP.TraceLine("Setting field LNK_RELATED_QT '" & doForm.GetRecordID() & "'", "", sProc)
            doNew.SetFieldVal("LNK_RELATED_PR", doForm.GetRecordID());
            // TLD 6/29/2009 Sets required field Purpose
            doNew.SetFieldVal("MLS_Purpose", 99, 2);     // Other

            // goP.TraceLine("About to commit.", "", sProc)
            if (doNew.Commit() != 1)
            {
                doNew = null;
                doLink = null;
                string sError = goErr.GetLastError();
                sMessage = goErr.GetLastError("MESSAGE");

                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages")?.ToString() + sMessage + Constants.vbCrLf);

                goLog.Log("Project_CreateActLog", doForm.oVar.GetVar("ScriptMessages")?.ToString(), 1, false, true);
                par_doCallingObject = doForm;
                return false;
            }

            doNew = null;
            doLink = null;
            par_doCallingObject = doForm;
            return true;
        }
        public bool QL_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // --------- TLD 4/27/2010 Overrides main AUTO-FILLED FIELDS ---------
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "SetQuoteOpenAndQLTimeCompleted"))
            {
                // Fill CHK_OPEN when Status is Open or On Hold
                switch (Convert.ToInt32(doRS.GetFieldVal("MLS_STATUS", 2)))
                {
                    case 0:
                    case 1:
                    case 7:     // Incomplete, On Hold, Complete (was using 0 and 20)

                        {
                            doRS.SetFieldVal("CHK_OPEN", 1, 2);
                            break;
                        }

                    case 2:
                    case 3: // Won, Lost

                        {
                            doRS.SetFieldVal("CHK_OPEN", 0, 2);
                            // CS 1/11/10 Fill QL.DTE_TimeCompleted with QT.DTE_DateClosed if blank
                            if (Convert.ToString(doRS.GetFieldVal("DTT_TimeCompleted", 1)) == "")
                            {
                                // If don't manage quote line status independent of qt set to same as QT; otherwise, set to now.
                                if (Convert.ToString(doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_STATUSMGMT")) == "0")
                                    doRS.SetFieldVal("DTT_TimeCompleted", doRS.GetFieldVal("LNK_IN_QT%%DTT_DATECLOSED"));
                                else
                                    // QL status mgmt is on. 
                                    doRS.SetFieldVal("DTT_TimeCompleted", goTR.NowLocal());
                            }

                            break;
                        }

                    default:
                        {
                            doRS.SetFieldVal("CHK_OPEN", 0, 2);
                            break;
                        }
                }
            }
            par_doCallingObject = doRS;
            return true;
        }
        public bool QT_FormControlOnChange_BTN_ACTIVE_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = goP.GetMe("GID_ID");

            // Sets date and user field
            doForm.doRS.SetFieldVal("LNK_STILLACTIVE_US", goP.GetMe("GID_ID"));
            doForm.doRS.SetFieldVal("DTE_STILLACTSENT", "Today");

            // TLD 7/1/2009 Calls script to send Quote Follow up Email
            //goScr.RunScript("QT_SENDEMAIL", doForm, null, "CHK_STILLACTIVE", "41", " Status Update", "QUOTE_STILLACTIVE");
            par_doCallingObject = doForm;
            scriptManager.RunScript("QT_SENDEMAIL", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "CHK_STILLACTIVE", "41", " Status Update", "QUOTE_STILLACTIVE");
            doForm = (Form)par_doCallingObject;
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_BTN_FOLLOWUP_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = goP.GetMe("GID_ID");

            // Sets date and user field
            doForm.doRS.SetFieldVal("LNK_FOLLOWUP_US", goP.GetMe("GID_ID"));
            doForm.doRS.SetFieldVal("DTE_FOLLOWSENT", "Today");

            // TLD 7/1/2009 Calls script to send Quote Follow up Email
            //goScr.RunScript("QT_SENDEMAIL", doForm, null, "CHK_FOLLOWUP", "2", " Follow-Up", "QUOTE_FOLLOWUP");

            par_doCallingObject = doForm;
            scriptManager.RunScript("QT_SENDEMAIL", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "CHK_FOLLOWUP", "2", " Follow-Up", "QUOTE_FOLLOWUP");
            doForm = (Form)par_doCallingObject;

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_CHK_SENDFROMVIZE_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 3/22/2010 If "Send from Vize" is checked,
            // call QT_SwapSignature script to copy main POP sig fields
            // to secondary, custom sig fields; copy secondary, custom
            // sig fields to main sig fields
            //goScr.RunScript("QT_SwapSignature", doForm.doRS);

            par_doCallingObject = doForm.doRS;
            scriptManager.RunScript("QT_SwapSignature", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            doForm.doRS = (clRowSet)par_doCallingObject;
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_LNK_ORIGINATEDBY_CN_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN")) != "")
            {
                doForm.doRS.ClearLinkAll("LNK_TO_CN");
                clArray doLink = new clArray();
                oTable = null;
                doLink = doForm.doRS.GetLinkVal("LNK_ORIGINATEDBY_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
                doForm.doRS.SetLinkVal("LNK_TO_CN", doLink);
            }

            par_doCallingObject = doForm;

            return true;

        }
        public bool QT_FormControlOnChange_LNK_FORLINE_MO_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO")) != "")
            {
                string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));

                clRowSet doRSModel = default(clRowSet);

                doRSModel = new clRowSet("MO", 3, "GID_ID='" + MO_Gid + "'", "", "cur_price,TXT_UNITTEXT");
                //cur_cost,CUR_PRICERETAIL

                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", doRSModel.GetFieldVal("CUR_PRICE", 1).ToString());
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", doRSModel.GetFieldVal("TXT_UNITTEXT",1).ToString());
            }
           
            par_doCallingObject = doForm;

            return true;

        }
        public bool QT_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Util.SetSessionValue("SkipQLSpecificLogic", "Y");
            // SGR TKT#:828 
            Form doForm = (Form)par_doCallingObject;
            // 0=10 Quote In Process
            // 6=60 Revised
            // 1=80 Complete Inquiry
            // If ((doForm.doRS.GetFieldVal("MLS_STATUS", 2) = "0" Or doForm.doRS.GetFieldVal("MLS_STATUS", 2) = "6" Or doForm.doRS.GetFieldVal("MLS_STATUS", 2) = "1") And (doForm.doRS.GetFieldVal("DTT_CreationTime", 2) < Convert.ToDateTime(goTR.NowLocal()).AddMonths(-18))) Then
            // doForm.doRS.SetFieldVal("MLS_STATUS", 4, 2)        '4=70 Cancelled
            // End If

            string sColor = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", sColor);

            doForm.doRS.SetFieldVal("CHK_LineInclude", "1", 2);
            

            doForm.doRS.SetFieldVal("LNK_RELATED_TE", doForm.doRS.GetFieldVal("LNK_TO_CO%%LNK_IN_TE", 2), 2);
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sColor = "#000000";
            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", sColor);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }
            // TLD 7/9/2010 Text Ship Date field replaced
            // with drop-down field, so this one not mandatory
            doForm.SetFieldProperty("TXT_ProposedShipping", "LABELCOLOR", sColor);

            // TLD 7/13/2009
            // Set Quote # field to disabled b/c it is auto-populated. It is only enabled if the quote # cannot
            // be generated OR the user is prompted if there is a dup
            doForm.SetControlState("TXT_QUOTENO", 4);

            // TLD 6/30/2009 Disables follow-up and still active fields for user entry
            // Checked by system based on user action
            doForm.SetControlState("CHK_STILLACTIVE", 4); // Gray
            doForm.SetControlState("CHK_FOLLOWUP", 4); // Gray
            doForm.SetControlState("DTE_FOLLOWSENT", 4); // Gray
            doForm.SetControlState("DTE_STILLACTSENT", 4); // Gray
            doForm.SetControlState("LNK_FOLLOWUP_US", 4); // Gray
            doForm.SetControlState("LNK_STILLACTIVE_US", 4); // Gray
                                                             // Hide if new -- shouldn't be sending followup or still active emails
            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_ACTIVE", 4); // Gray
                doForm.SetControlState("BTN_FOLLOWUP", 4); // Gray
            }
            // TLD 4/27/2010 Disable custom date field
            doForm.SetControlState("DTE_DATECOMPLETED", 4);

            // TLD 7/1/2009 Only hide buttons if new, so commented that
            // TLD 6/30/2009 Hids/Displays buttons and Set tooltips for new send buttons
            // If CHK_STILLACTIVE NOT checked, then email not sent, so button visible
            // If doForm.doRS.GetFieldVal("CHK_STILLACTIVE", 2) = 0 Then 'unchecked
            // Hide if new -- shouldn't be sending followup or still active emails
            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_ACTIVE", 2); // Hide
                doForm.SetControlState("BTN_FOLLOWUP", 2); // Hide
            }
            else
            {
                doForm.SetControlState("BTN_ACTIVE", 0); // Show
                doForm.SetControlState("BTN_FOLLOWUP", 0); // Show
            }
            doForm.SetFieldProperties("BTN_ACTIVE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Send Still Active Email");
            // doForm.SetControlState("BTN_ACTIVE", 0)
            // Else
            // doForm.SetControlState("BTN_ACTIVE", 2)
            // End If

            // IF CHK_FOLLOWUP NOT checked, then email not sent, so button visible=
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_FOLLOWUP", 2)) == 0)
            {
                doForm.SetFieldProperties("BTN_FOLLOWUP", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Send Follow-Up Email");
                doForm.SetControlState("BTN_FOLLOWUP", 0);
            }
            else
                doForm.SetControlState("BTN_FOLLOWUP", 2);

            // TLD 6/17/2010 Added to disable commission rate for user entry
            doForm.SetControlState("SR__COMMRATE", 4); // Gray
            doForm.doRS.SetFieldVal("CHK_LineInclude", "1", 2);

            Refresh_QouteTotal(doForm.doRS);

           

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_READONLY, "LNK_IN_QT='" + sGidId + "' And LNK_FOR_MO%%BI__ID<1 ", "LNK_IN_QT", "LNK_FOR_MO");

            if ((rsQL.GetFirst() == 1))
            {
                goErr.SetWarning(35000, sProc, "Please fill Model before saving the Quote  ");
                doForm.FieldInFocus = "LNK_FOR_MO";
                par_doCallingObject = doForm;
                return false;
            }

            return true;
        }
        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            int iQuoteSeqID;

            // Make sure the Quote # does not already exist.
            if (Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) != "")
            {
                clRowSet doRSQuote = new clRowSet("QT", 3, "TXT_QUOTENO='" + doForm.doRS.GetFieldVal("TXT_QUOTENO")?.ToString() + "' AND GID_ID <> " + doForm.doRS.GetFieldVal("GID_ID")?.ToString() + "", null, "GID_ID", -1, null, null, null, null, "1");
                if (doRSQuote.GetFirst() == 1)
                {
                    // Enable the Quote # field
                    doForm.SetControlState("TXT_QUOTENO", 0);
                    doForm.MessageBox("This Quote Number already exists. Please make the Quote Number unique by appending an A, B, or C to the number.");
                    par_doCallingObject = doForm;
                    return false;
                }
            }

            // Update OTH MD for next QT # if we are saving a new QT that has a quote # and is not a revision
            if (doForm.GetMode() == "CREATION" & Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) != "" & Convert.ToString(doForm.oVar.GetVar("QuoteOpeningMode")) != "Revision")
            {
                // Update OTH MD for next Quote #
                // update Quote Seq ID
                // TLD 8/24/2009 Updated to Read and Write for Product XX
                // iQuoteSeqID = goMeta.LineRead(goP.GetUserTID, "OTH_HIGHESTQUOTEPOINTERS", "QUOTESEQID") 'this QT's seq ID
                iQuoteSeqID = Convert.ToInt32(goMeta.LineRead(goP.GetUserTID(), "OTH_HIGHESTQUOTEPOINTERS", "QUOTESEQID", "1", false, "XX")); // this QT's seq ID
                                                                                                                                               // update Quote Seq ID
                                                                                                                                               // goMeta.LineWrite(goP.GetUserTID, "OTH_HIGHESTQUOTEPOINTERS", "QUOTESEQID", iQuoteSeqID + 1) 'write next seq Id
                goMeta.LineWrite(goP.GetUserTID(), "OTH_HIGHESTQUOTEPOINTERS", "QUOTESEQID", iQuoteSeqID + 1, ref par_oConnection, null, "XX"); // write next seq Id
            }

            // SGR TKT#:828 

            // 0=10 Quote In Process
            // 6=60 Revised
            // 1=80 Complete Inquiry
            if (((Convert.ToString(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == "0" | Convert.ToString(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == "6" | Convert.ToString(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == "1") & (Convert.ToDateTime(doForm.doRS.GetFieldVal("DTT_CreationTime", 2)) < Convert.ToDateTime(goTR.NowLocal()).AddMonths(-18))))
                doForm.doRS.SetFieldVal("MLS_STATUS", 4, 2);// 4=70 Cancelled

            doForm.doRS.SetFieldVal("LNK_RELATED_TE", doForm.doRS.GetFieldVal("LNK_TO_CO%%LNK_IN_TE", 2), 2);
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            int iStatus = Convert.ToInt32(doRS.GetFieldVal("MLS_STATUS", 2));
            int iAppDatasheet = Convert.ToInt32(doRS.GetFieldVal("CHK_APPDATASHEET", 2));
            int iQuotingAgainst = Convert.ToInt32(doRS.GetFieldVal("CHK_QUOTINGAGAINST", 2));
            int iInstallBase = Convert.ToInt32(doRS.GetFieldVal("CHK_INSTALLBASE", 2));
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", null, false, "XX");

            // ------------ TLD 4/27/2010 Overrides main AUTO-FILLED FIELDS ------------
            // Fill CHK_Closed when Status is Open or On Hold
            switch (iStatus)
            {
                case 0:
                case 1:
                case 7:       // Incomplete, On Hold, Complete
                    {
                        doRS.SetFieldVal("CHK_OPEN", 1, 2);
                        break;
                    }

                default:
                    {
                        doRS.SetFieldVal("CHK_OPEN", 0, 2);
                        break;
                    }
            }

            if (iStatus == 7)
            {
                par_iValid = 3;
                // AND if date completed has not already been set -- is blank
                if (Convert.ToDateTime(doRS.GetFieldVal("DTT_DATECOMPLETED", 2)) == goTR.StringToDateTime(clC.SELL_BLANK_DATETIME, null, null, ref par_iValid, " "))
                    // Set date/time completed to now
                    doRS.SetFieldVal("DTT_DATECOMPLETED", "Today|Now");
            }

            // TLD 10/6/2010 Converted to Case statements
            // TLD 6/17/2010 ----------------Sets SR__COMMPERC based on 3 checkboxes on QT Form
            // If all checked, set to WOP SR__A
            switch (iAppDatasheet)
            {
                case 0:
                    {
                        switch (iQuotingAgainst)
                        {
                            case 0:
                                {
                                    if (iInstallBase == 0)
                                        doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_F"));// None Checked

                                    if (iInstallBase == 1)
                                        doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_E"));// Install Base Checked
                                    break;
                                }

                            case 1:
                                {
                                    if (iInstallBase == 0)
                                        doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_H"));// Against Checked

                                    if (iInstallBase == 1)
                                        doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_G"));// App Data Sheet NOT Checked
                                    break;
                                }
                        }

                        break;
                    }

                case 1:
                    {
                        switch (iQuotingAgainst)
                        {
                            case 0:
                                {
                                    if (iInstallBase == 0)
                                        doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_C"));// App Data Sheet Checked

                                    if (iInstallBase == 1)
                                        doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_B"));// Against NOT Checked
                                    break;
                                }

                            case 1:
                                {
                                    if (iInstallBase == 0)
                                        doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_D"));// Install Base NOT Checked

                                    if (iInstallBase == 1)
                                        doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_A"));// All Checked
                                    break;
                                }
                        }

                        break;
                    }
            }
            // If iAppDatasheet = 1 And iQuotingAgainst = 1 And iInstallBase = 1 Then
            // doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_A")) 'All Checked
            // End If

            // Against NOT checked, set to WOP SR__B
            // If iAppDatasheet = 1 And iQuotingAgainst = 0 And iInstallBase = 1 Then
            // doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_B")) 'Against NOT Checked
            // End If

            // 'App Data Sheet checked, set to WOP SR__C
            // If iAppDatasheet = 1 And iQuotingAgainst = 0 And iInstallBase = 0 Then
            // doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_C")) 'App Data Sheet Checked
            // End If

            // Install Base NOT checked, set to WOP SR__D
            // If iAppDatasheet = 1 And iQuotingAgainst = 1 And iInstallBase = 0 Then
            // doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_D")) 'Install Base NOT Checked
            // End If

            // Install Base  checked, set to WOP SR__E
            // If iAppDatasheet = 0 And iQuotingAgainst = 0 And iInstallBase = 1 Then
            // doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_E")) 'Install Base Checked
            // End If

            // None checked, set to WOP SR__F
            // If iAppDatasheet = 0 And iQuotingAgainst = 0 And iInstallBase = 0 Then
            // doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_F")) 'None Checked
            // End If

            // App Data Sheet NOT checked, set to WOP SR__G
            // If iAppDatasheet = 0 And iQuotingAgainst = 1 And iInstallBase = 1 Then
            // doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_G")) 'App Data Sheet NOT Checked
            // End If

            // Against checked, set to WOP SR__H
            // If iAppDatasheet = 0 And iQuotingAgainst = 1 And iInstallBase = 0 Then
            // doRS.SetFieldVal("SR__COMMRATE", goTR.StrRead(sWOP, "QUOTE_H")) 'Against Checked
            // End If
            // TLD 6/17/2010 ----------------End Sets SR__COMMPERC based on 3 checkboxes on QT Form
            par_doCallingObject = doRS;
            return true;
        }
        public bool QT_SENDEMAIL_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 7/9/2009 Modified to also attach QT
            // TLD 7/1/2009 Modified to Create AC of type Email sent and
            // copy default text from Custom WOP instead of sending as attachment.
            // TLD 6/30/2009 Sends Follow-Up email with custom print template
            // cus_ms word_quote_followup.doc, leaves quote open

            Form doForm = (Form)par_doCallingObject;
            // Dim sEmail As String = doForm.doRS.GetFieldVal("EML_EMAIL")
            string sLetter = "";
            string sObjectPassed = par_doCallingObject.GetType().ToString();
            string sID = "";
            clSend oMySend = new clSend();
            // Dim oSend As Object
            bool bNoAddPerm = false;
            string sSubject = par_s3;
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS");
            clRowSet doACRS; // new AC of type email sent
            string sField = par_s1;
            int iPurpose = Convert.ToInt32(par_s2);
            string sWOPField = par_s4;
            string sSysName = Convert.ToString(doForm.doRS.GetFieldVal("SYS_NAME")).Trim();
            string sURL;
            string sName;
            string sMEID = goP.GetMe("ID");

            // 'Checks to see if Follow-up Sent already checked
            // If doForm.doRS.GetFieldVal("CHK_FOLLOWUP", 2) = 1 Then
            // Exit Function
            // End If

            // oSend.AddSendJob("Send Followup " & doForm.doRS.GetFieldVal("SYS_NAME") & " Email", doForm.doRS.GetFieldVal("GID_ID"), "cus_ms word_quote_followup.doc", "CORR", "EMAIL", "QT", True, True, sEmail)
            // goUI.ExecuteSendPopup = True

            // Build text for Letter field
            sLetter = goTR.StrRead(sWOP, sWOPField);

            sLetter = sLetter.Replace("(%LNK_TO_CN%%TXT_NAMEFIRST%)", Convert.ToString(doForm.doRS.GetFieldVal("LNK_TO_CN%%TXT_NAMEFIRST")));
            sLetter = sLetter.Replace("(%LNK_TO_CN%%TXT_NAMELAST%)", Convert.ToString(doForm.doRS.GetFieldVal("LNK_TO_CN%%TXT_NAMELAST")));
            sLetter = sLetter.Replace("(%TXT_QUOTENO%)", Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")));


            string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_TO_CN%%EML_EMAIL"));
            string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
            // TLD 7/13/2009 Fill form fields
            // Checks appropriate checkbox
            doForm.doRS.SetFieldVal(sField, 1, 2);
            doForm.doRS.SetFieldVal("TXT_ABOVESIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRABOVESIGNATURE"));
            doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
            // TLD 7/15/2009 Added to fill signature field with
            doForm.doRS.SetFieldVal("TXT_SIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));

            // TLD 7/13/2009 Added if followup to customized
            // for current user email
            if (Strings.InStr(sSubject, "Follow-Up") > 0)
            {
                clRowSet doMERS = new clRowSet("US", 3, "GID_ID='" + sMEID + "'", null, "EML_EMAIL");
                if (doMERS.GetFirst() == 1)
                    // TLD 7/13/2009 Sets E-mail field to User, NOT Created by
                    sLetter = sLetter + "Your business is important to us!  Please email us at " + doMERS.GetFieldVal("EML_EMAIL")?.ToString() + " or fax us back at 281-240-2440." + Constants.vbCrLf + Constants.vbCrLf + "Thank you!!";
            }
            // Sets Quote Cover Letter to custom text
            doForm.doRS.SetFieldVal("MMO_COVERLETTER", sLetter);

            // Check for add permissions
            if (goData.GetAddPermission("AC") == false)
                bNoAddPerm = true;

            if (sObjectPassed == "Form")
                sID = doForm.doRS.GetFieldVal("GID_ID").ToString();

            // Make sure POP set with email client
            if (goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "EMAILPROG", "<%NONE%>", true) == "<%NONE%>")
            {
                doForm.MessageBox("You must select your e-mail program in Personal Options before you can send.");
                par_doCallingObject = doForm;
                return true;
            }

            // 'Saves QT before sending AC email
            // If doForm.Save(2, , System.Reflection.MethodInfo.GetCurrentMethod().Name) = 0 Then
            // 'doForm.MessageBox(goErr.GetLastError("MESSAGE"))
            // Return False
            // End If

            // TLD 5/15/2009 Sets subject field
            sSubject = "Quote: " + doForm.doRS.GetFieldVal("TXT_QUOTENO") + sSubject;

            // Creates new AC
            doACRS = new clRowSet("AC", 2, null, null, null, -1, null, null, "CRL_AC:EMAILSENT", sID, null, true);

            // Set values in linked AC
            doACRS.SetFieldVal("MLS_PURPOSE", iPurpose, 2);
            doACRS.SetFieldVal("MMO_LETTER", sLetter);
            doACRS.SetFieldVal("TXT_SUBJ", sSubject);
            // TLD 7/13/2009 Added to fix with user signature, not createdby
            doACRS.SetFieldVal("TXT_ABOVESIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRABOVESIGNATURE"));
            doACRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
            doACRS.SetFieldVal("TXT_SIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));

            // Saves new AC record
            if (doACRS.Commit() != 1)
            {
                doForm.MessageBox("Cannot create an email Activity.");
                par_doCallingObject = doForm;
                return true;
            }

            // 'Sends AC to PC Link
            // If oMySend.AddSendJob("Email Activity from Quote: " & sSysName, doACRS.GetFieldVal("GID_ID"), "cus_Text Email.txt", "CORR", "EMAIL", "AC", False, True) <> "" Then
            // oSend = New clSend
            // oSend.MarkRecordAsSent(doACRS.GetFieldVal("GID_ID"), "CORR")
            // goUI.ExecuteSendPopup = True
            // Else
            // doForm.MessageBox("Cannot create send job.")
            // Return True
            // End If

            // 'Saves/Closes QT after sending AC email
            // If doForm.Save(1, , System.Reflection.MethodInfo.GetCurrentMethod().Name) = 0 Then
            // 'doForm.MessageBox(goErr.GetLastError("MESSAGE"))
            // Return False
            // End If

            //if (goP.GetProduct() == "MB")
            //    sURL = "../Pages/Mobile_DiaSend.aspx";
            //else
            //    sURL = "../Pages/diaSend.aspx";
            //// objectotsend: VIEW, RECORD, CORR
            //goTR.URLWrite(ref sURL, "objecttosend", "CORR");

            //// We do know how the user wants to send a Quote 
            //// Send type: EMAIL, FAX, LETTER, or WPRESPONSE
            //goTR.URLWrite(ref sURL, "sendtype", "EMAIL");

            //goTR.URLWrite(ref sURL, "objectid", doForm.doRS.GetFieldVal("GID_ID").ToString());
            sName = doForm.doRS.GetFieldVal("SYS_Name").ToString();
            // CS: Always update sys name in case one of the affected fields changed.
            // If sName = "" Then
            //goScr.RunScript("GenerateSysName", doForm.doRS, null, null, null, null, null, null, sName);
            par_doCallingObject = doForm.doRS;
            scriptManager.RunScript("GenerateSysName", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, sName);
            doForm.doRS = (clRowSet)par_doCallingObject;
            // End If
            //goTR.URLWrite(ref sURL, "objectname", sName);
            // goTR.URLWrite(sURL, "returnto", "MI_Test.aspx")

            // If doForm.Save(1, , System.Reflection.MethodInfo.GetCurrentMethod().Name) = 0 Then
            // Return False
            // End If

            // CS 6/28/07:Changing to doForm.save(2) so that the _post script will be called if there is any. When
            // set to doform.save(1) the code never returned here after save so the function never
            // returned true and the post script would never be called. So now the post script can be called and I
            // just set a boolean that tells the form to close so that the Send dialog displays.
            if (doForm.Save(2, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
            {
                par_doCallingObject = doForm;
                return false;
            }
            Util.SetSessionValue("SendFileData",  "||||" + sTo + "|" + sSubject + "|" + sLetter + "|" + sFrom);
          
            // CS 9/13/08 Added
            //HttpContext.Current.Response.Redirect(goUI.Navigate("DIALOG", sURL));
            par_doCallingObject = doForm;
            return true;
        }
        
        public bool QT_SwapSignature_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // TLD 3/22/2010
            // PURPOSE:
            // Swap custom signature fields on Quote if "Send from Vize" is checked
            // Called from QT_FormControlOnChange_CHk_SENDFROMVIZE
            // QTs should ONLY be sent to one or the other but allow for a user unchecking
            // RETURNS:
            // True

            // Dim sEmplNameFirst As String
            // Dim sEmplNameLast As String
            // Dim sEmplInfo As String

            if (Convert.ToInt32(doRS.GetFieldVal("CHK_SENDFROMVIZE", 2)) == 1)
            {
                // Set Secondary Signature fields to main POP fields
                doRS.SetFieldVal("TXT_Signature2", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                doRS.SetFieldVal("TXT_ABOVESIGNATURE2", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRABOVESIGNATURE"));
                doRS.SetFieldVal("MMO_UNDERSIGNATURE2", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));

                // Set main POP fields to secondary POP fields
                doRS.SetFieldVal("TXT_Signature", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_SIG2"));
                doRS.SetFieldVal("TXT_ABOVESIGNATURE", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_ABOVESIG2"));
                doRS.SetFieldVal("MMO_UNDERSIGNATURE", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_UNDERSIG2"));
            }
            else
            {
                // Set main Signature fields to main POP fields
                doRS.SetFieldVal("TXT_Signature", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                doRS.SetFieldVal("TXT_ABOVESIGNATURE", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRABOVESIGNATURE"));
                doRS.SetFieldVal("MMO_UNDERSIGNATURE", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));

                // Set secondary POP fields to secondary POP fields
                doRS.SetFieldVal("TXT_Signature2", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_SIG2"));
                doRS.SetFieldVal("TXT_ABOVESIGNATURE2", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_ABOVESIG2"));
                doRS.SetFieldVal("MMO_UNDERSIGNATURE2", doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_UNDERSIG2"));
            }

            par_doCallingObject = doRS;
            return true;
        }
        public bool Quote_CreateActLog_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 6/29/2009 Prevents main from running
            // Modified to fill required Purpose
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sWork;
            long lWork;
            string sNotes;
            string sMessage;

            // goP.TraceLine("", "", sProc)

            // PURPOSE:
            // Offer to create an Activity when the Journal in the Quote have been edited.
            // Originally by PJ 5/30/02 Adds Act Log with new Journal of Qte.
            // RETURNS:
            // True if Activity was created or creation wasn't needed; False if the user declined creating an Activity.


            // CS: per MI, no prompts
            // 'If answered Yes on MB to create a journal, then go directly to script that creates journal.
            // If UCase(par_s1) = "YES" Then
            // GoTo CREATEACTLOG
            // End If

            if (Strings.Len(Convert.ToString(doForm.doRS.GetFieldVal("MMO_JOURNAL"))) <= Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))
            {
                par_doCallingObject = doForm;
                return true;
            }

            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Quote is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages")?.ToString() + sMessage + Constants.vbCrLf);
                doForm.oVar.SetVar("Quote_CreateActLog_Ran", "1");
                // Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                par_doCallingObject = doForm;
                return true;
            } // If doForm.GetMode() = "CREATION" Then Return True					'The record must be in edit mode so we have its ID for links to it.

            doForm.oVar.SetVar("Quote_CreateActLog_Ran", "1");

            // CREATEACTLOG:

            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, null, null, null, -1, null, null, null, null, null, doForm.doRS.bBypassValidation);

            sWork = doForm.doRS.GetFieldVal("MMO_Journal")?.ToString();
            lWork = Convert.ToInt64(sWork.Length) - ((doForm.oVar.GetVar("lLenJournal") == null) ? 0 : Convert.ToInt64(doForm.oVar.GetVar("lLenJournal")));
            sNotes = Strings.Left(sWork, Convert.ToInt32(lWork));
            sNotes = sNotes + "== Created from Quote '" + doForm.doRS.GetFieldVal("SYS_Name")?.ToString() + "'";
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            // CS goP.TraceLine("Setting field LNK_Involves_US '" & goP.GetMe("ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            // goP.TraceLine("Setting field MLS_Status '" & 1 & "'", "", sProc)
            doNew.SetFieldVal("MLS_Status", 1, 2);     // Completed
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            doNew.SetFieldVal("DTE_ENDTIME", "Today");
            // goP.TraceLine("Setting field TME_STARTTIME '" & "<%Now%>" & "'", "", sProc)
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            // goP.TraceLine("Setting field TME_ENDTIME '" & "<%Now%>" & "'", "", sProc)
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            // goP.TraceLine("Setting field DTE_STARTTIME '" & "<%Today%>" & "'", "", sProc)
            // doNew.SetFieldVal("DTE_STARTTIME", "Today")
            // goP.TraceLine("Setting field DTE_ENDTIME '" & "<%Today%>" & "'", "", sProc)
            // CS doNew.SetFieldVal("DTE_ENDTIME", doForm.doRS.GetFieldVal("DTE_Time"))
            // doNew.SetFieldVal("DTE_ENDTIME", "Today")
            // goP.TraceLine("Setting field LNK_CreditedTo_US '" & goP.GetMe("ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));
            doNew.SetFieldVal("LNK_RELATED_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN", 2), 2);
            doNew.SetFieldVal("LNK_RELATED_CO", doForm.doRS.GetFieldVal("LNK_TO_CO", 2), 2);
            // goP.TraceLine("Setting field MLS_TYPE '" & 31 & "'", "", sProc)
            doNew.SetFieldVal("MLS_TYPE", 31, 2);      // Journal
                                                       // goP.TraceLine("Setting field MMO_HISTORY '" & goTR.WriteLogLine(doForm.dors.GetFieldVal("MMO_HISTORY"), "Created.") & "'", "", sProc)
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine(Convert.ToString(doNew.GetFieldVal("MMO_HISTORY")), "Created."));
            // goP.TraceLine("Setting field LNK_RELATED_QT '" & doForm.GetRecordID() & "'", "", sProc)
            doNew.SetFieldVal("LNK_RELATED_QT", doForm.GetRecordID());
            // TLD 6/29/2009 Fills required Purpose field
            doNew.SetFieldVal("MLS_Purpose", 99, 2);     // Other

            // goP.TraceLine("About to commit.", "", sProc)
            if (doNew.Commit() != 1)
            {
                doNew = null;
                doLink = null;

                string sError = goErr.GetLastError();
                sMessage = goErr.GetLastError("MESSAGE");

                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages")?.ToString() + sMessage + Constants.vbCrLf);

                goLog.Log("Quote_CreateActLog", doForm.oVar.GetVar("ScriptMessages")?.ToString(), 1, false, true);
                par_doCallingObject = doForm;
                return false;
            }


            doNew = null;
            doLink = null;
            par_doCallingObject = doForm;
            return true;
        }
        public bool Quote_GenerateQuoteNo_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //par_bRunNext = false;

            string s = "";

            Form doForm = (Form)par_doCallingObject;

            try
            {

                goData = (clData)Util.GetInstance("data");
                System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
                System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
                System.Data.SqlClient.SqlDataReader reader = null;
                cmd.CommandText = "proc_GenerateSeqNo";
                cmd.CommandType = System.Data.CommandType.StoredProcedure;
                cmd.Connection = sqlConnection1;

                //parameter
                System.Data.SqlClient.SqlParameter Gid_iD = new System.Data.SqlClient.SqlParameter("@UserId", System.Data.SqlDbType.VarChar);
                Gid_iD.Value = goP.GetUserTID();
                cmd.Parameters.Add(Gid_iD);
                System.Data.SqlClient.SqlParameter Usercode = new System.Data.SqlClient.SqlParameter("@UserCode", System.Data.SqlDbType.VarChar);
                Usercode.Value = goP.GetUserCode();
                cmd.Parameters.Add(Usercode);
                System.Data.SqlClient.SqlParameter File = new System.Data.SqlClient.SqlParameter("@Type", System.Data.SqlDbType.VarChar);
                File.Value = "QT";
                cmd.Parameters.Add(File);

                reader = cmd.ExecuteReader();

                DataTable dt = new DataTable();
                dt.Load(reader);

                if (dt != null && dt.Rows.Count > 0)
                {
                    s = Convert.ToString(dt.Rows[0]["QTNo"]);
                }
            }
            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                {
                    goErr.SetError(ex, 45105, sProc);
                    par_doCallingObject = doForm;
                    return false;
                }
            }
            par_oReturn = s;
            par_doCallingObject = doForm;

            return true;
        }
        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Quotline_CalcTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        private static void Refresh_QouteTotal(clRowSet doQuote)
        {
            string sGidId = Convert.ToString(doQuote.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_GROUPBY, "LNK_IN_QT='" + sGidId + "' ", "LNK_IN_QT", "CUR_SUBTOTAL|SUM");

            double curTotalAmt = 0.0;

            if ((rsQL.GetFirst() == 1))
            {
                
                curTotalAmt = Convert.ToDouble(rsQL.GetFieldVal("CUR_SUBTOTAL|SUM", 2));
                

                doQuote.SetFieldVal("CUR_SUBTOTAL", curTotalAmt, 2);
                doQuote.SetFieldVal("CUR_TOTAL", curTotalAmt,2);
               
            }
            else
            {
                doQuote.SetFieldVal("CUR_SUBTOTAL", 0.0);
                doQuote.SetFieldVal("CUR_TOTAL", 0.0);
              
            }

           
        }

        public bool TD_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool XR_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = "Script::XW_FormOnLoadREcord_Pre";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 3/4/2010 Added for custom POP signature fields

            Form doForm = (Form)par_doCallingObject;
            string sPOP = goMeta.PageRead(goP.GetMe("ID"), "POP_PERSONAL_OPTIONS");

            // Read values from MD and set in the form fields
            doForm.doRS.SetFieldVal("TXT_ABOVESIGNATURE2", goTR.StrRead(sPOP, "QUOTE_ABOVESIG2"));
            doForm.doRS.SetFieldVal("TXT_SIGNATURE2", goTR.StrRead(sPOP, "QUOTE_SIG2"));
            doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE2", goTR.StrRead(sPOP, "QUOTE_UNDERSIG2"));
            par_doCallingObject = doForm;
            return true;
        }
        public bool XR_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = "Script::XW_FormOnSave_PRE";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sPOP = goMeta.PageRead(goP.GetMe("ID"), "POP_PERSONAL_OPTIONS");

            // TLD 3/4/2010 Added for custom POP signature fields

            // Write values to MD
            goTR.StrWrite(ref sPOP, "QUOTE_ABOVESIG2", doForm.doRS.GetFieldVal("TXT_ABOVESIGNATURE2"));
            goTR.StrWrite(ref sPOP, "QUOTE_SIG2", doForm.doRS.GetFieldVal("TXT_SIGNATURE2"));
            goTR.StrWrite(ref sPOP, "QUOTE_UNDERSIG2", doForm.doRS.GetFieldVal("MMO_UNDERSIGNATURE2"));

            // Write to Product XX
            goMeta.PageWrite(goP.GetMe("ID"), "POP_PERSONAL_OPTIONS", sPOP, null, null, "XX");

            doForm.CloseOnReturn = true;
            doForm.CancelSave();
            par_doCallingObject = doForm;
            return true;
        }
        public bool XW_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = "Script::XW_FormOnLoadREcord_Pre";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            par_bRunNext = false;

            // TLD 7/1/2009 Added for custom text fields/functionality
            // for sending AC Emails from QT

            Form doForm = (Form)par_doCallingObject;
            // TLD 8/24/2009 Updated to read Product XX
            // Reading here because should ONLY be 1 set of text regardless of Product
            // Dim sWOP As String = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS")
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", null, false, "XX");

            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                par_doCallingObject = doForm;
                return false;
            }

            // Read values from MD and set in the form fields
            doForm.doRS.SetFieldVal("MMO_FOLLOWUP", goTR.StrRead(sWOP, "QUOTE_FOLLOWUP"));
            doForm.doRS.SetFieldVal("MMO_STILLACTIVE", goTR.StrRead(sWOP, "QUOTE_STILLACTIVE"));
            // TLD 6/17/2010 Added for custom QT Commission Rate Fields
            doForm.doRS.SetFieldVal("SR__A", goTR.StrRead(sWOP, "QUOTE_A")); // All Checked
            doForm.doRS.SetFieldVal("SR__B", goTR.StrRead(sWOP, "QUOTE_B")); // Quoting Against NOT Checked
            doForm.doRS.SetFieldVal("SR__C", goTR.StrRead(sWOP, "QUOTE_C")); // Application Data Sheet Checked
            doForm.doRS.SetFieldVal("SR__D", goTR.StrRead(sWOP, "QUOTE_D")); // Install Base NOT Checked
            doForm.doRS.SetFieldVal("SR__E", goTR.StrRead(sWOP, "QUOTE_E")); // Install Base Checked
            doForm.doRS.SetFieldVal("SR__F", goTR.StrRead(sWOP, "QUOTE_F")); // None Checked
            doForm.doRS.SetFieldVal("SR__G", goTR.StrRead(sWOP, "QUOTE_G")); // Application Data Sheet NOT Checked
            doForm.doRS.SetFieldVal("SR__H", goTR.StrRead(sWOP, "QUOTE_H")); // Quoting Against Checked
            par_doCallingObject = doForm;
            return true;
        }
        public bool XW_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = "Script::XW_FormOnSave_PRE";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // TLD 8/24/2009 Updated to read Product XX
            // Reading here because should ONLY be 1 set of text regardless of Product
            // Dim sWOP As String = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS")
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", null, false, "XX");

            par_bRunNext = false;

            // TLD 7/1/2009 Added for custom text fields/functionality
            // for sending AC Emails from QT

            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                par_doCallingObject = doForm;
                return false;
            }

            // Write values to MD
            goTR.StrWrite(ref sWOP, "QUOTE_FOLLOWUP", doForm.doRS.GetFieldVal("MMO_FOLLOWUP"));
            goTR.StrWrite(ref sWOP, "QUOTE_STILLACTIVE", doForm.doRS.GetFieldVal("MMO_STILLACTIVE"));
            // TLD 6/17/2010 Added for custom QT Commission Rate Fields
            goTR.StrWrite(ref sWOP, "QUOTE_A", doForm.doRS.GetFieldVal("SR__A")); // All Checked
            goTR.StrWrite(ref sWOP, "QUOTE_B", doForm.doRS.GetFieldVal("SR__B")); // Quoting Against Not Checked
            goTR.StrWrite(ref sWOP, "QUOTE_C", doForm.doRS.GetFieldVal("SR__C")); // Application Data Sheet Checked
            goTR.StrWrite(ref sWOP, "QUOTE_D", doForm.doRS.GetFieldVal("SR__D")); // Install Base NOT Checked
            goTR.StrWrite(ref sWOP, "QUOTE_E", doForm.doRS.GetFieldVal("SR__E")); // Install Base Checked
            goTR.StrWrite(ref sWOP, "QUOTE_F", doForm.doRS.GetFieldVal("SR__F")); // None Checked
            goTR.StrWrite(ref sWOP, "QUOTE_G", doForm.doRS.GetFieldVal("SR__G")); // Application Data Sheet NOT Checked
            goTR.StrWrite(ref sWOP, "QUOTE_H", doForm.doRS.GetFieldVal("SR__H")); // Quoting Against Checked

            goMeta.PageWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", sWOP, null, null, "XX");
            // --Write MD page setting Product parameter to "XX"

            doForm.CloseOnReturn = true;
            doForm.CancelSave();
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote_Draft.docx";
                }
                else if (sQTTemplate == "Letterhead Quote Template QT")
                {
                    return "Cus_LetterheadQuoteTemplateQT_Draft.docx";
                }
                else if (sQTTemplate == "Quote - Followup or Still Active Template")
                {
                    return "cus_Followup_StillActive_Quote_Draft.docx";
                }
                else if (sQTTemplate == "Quote Template")
                {
                    return "Cus_QuoteTemplateQT_Draft.docx";
                }
            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote.docx";
                }
                else if (sQTTemplate == "Letterhead Quote Template QT")
                {
                    return "Cus_LetterheadQuoteTemplateQT.docx";
                }
                else if (sQTTemplate == "Quote - Followup or Still Active Template")
                {
                    return "cus_Followup_StillActive_Quote.docx";
                }
                else if (sQTTemplate == "Quote Template")
                {
                    return "Cus_QuoteTemplateQT.docx";
                }
            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }

            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);
                       


                        //if (!string.IsNullOrEmpty(sFrom))
                        //{
                        //    Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom);

                        //}
                        //else
                        //{
                        //    Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom);

                        //    //doForm.MessageBox("Please configure valid E-mail for " + goP.GetMe("NAMEUS") +" to send the Quote. ");
                        //}
                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }
  
            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));
            string sModelText = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_MODELNAME"));
            string sModelDesc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%MMO_SPECIFICATIONS"));



            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,SR__QTY,TXT_UNIT,LNK_ORIGINATEDBY_CN,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            // rsQL.SetFieldVal("CUR_Cost", curCost);
            rsQL.SetFieldVal("TXT_MODEL", sModelText);
            rsQL.SetFieldVal("MMO_DETAILS", sModelDesc);


            rsQL.SetFieldVal("SR__Qty", dQty);
            rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);

            //rsQL.SetFieldVal("TXT_Model", sModelText);      
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);
            rsQL.SetFieldVal("TXT_MODEL", sModelText);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}

            Refresh_QouteTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }
        public bool QT_FormControlOnChange_BTN_RECALC_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            Refresh_QouteTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            par_doCallingObject = doForm;
            return true;

        }

        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
            }

        }

        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "") 
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);

            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {

                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }

            par_doCallingObject = doForm;
            return true;
        }


        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_FOR_MO,LNK_RELATED_PD,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("LNK_FOR_MO", MO_Gid);
            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);

            //This will generate line no# when add lines from mobile app
            //double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            //if (rLineNo <= 0)
            //{
            //    long iLineCount = doRS.GetLinkCount("LNK_CONNECTED_OP%%LNK_RELATED_OL");
            //    iLineCount = iLineCount + 1;
            //    doRS.SetFieldVal("SR__LINENO", iLineCount);
            //}
            //mobile Ol line number
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    iLineCount = doOPLines.Count();
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }
            par_doCallingObject = doRS;

            return true;
        }

        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }

        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
       {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = null;
            clRowSet doRS1 = null;


            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS1.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doRS1.SetFieldVal("CUR_VALUE", curValue);
                    doRS1.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doRS1;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                    doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doForm;
            }

            return true;

        }

        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }



            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));
            
            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_ENDUSER_CO", rsOP.GetFieldVal("LNK_ENGINEERING_CO"));
            doFormQT.doRS.SetFieldVal("LNK_DISTREP_CO", rsOP.GetFieldVal("LNK_DISTRIBUTOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECOMPLETED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
                rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("Cur_UnitPrice", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }

        public bool FD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sTitle = "";
            string sUserGid = "";

            sTitle = Convert.ToString(doRS.GetFieldVal("TXT_TITLE"));
            sUserGid = Convert.ToString(doRS.GetFieldVal("LNK_TO_US"));

            goUI.AddAlert(sTitle + "  file is ready to download", "OPENDESKTOP", "DSK_4E8207CE-28DE-4F55-5858-B0B300B6D2B2", sUserGid, "").ToString();

            par_doCallingObject = doRS;
            return true;
        }
    }
}

