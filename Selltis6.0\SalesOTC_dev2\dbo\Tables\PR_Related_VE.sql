﻿CREATE TABLE [dbo].[PR_Related_VE] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Project_Related_Vendor_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PR] UNIQUEIDENTIFIER NOT NULL,
    [GID_VE] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PR_Related_VE] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PR_Related_VE] FOREIGN KEY ([GID_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_VE_Connected_PR] FOREIGN KEY ([GID_PR]) REFERENCES [dbo].[PR] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PR_Related_VE] NOCHECK CONSTRAINT [LNK_PR_Related_VE];


GO
ALTER TABLE [dbo].[PR_Related_VE] NOCHECK CONSTRAINT [LNK_VE_Connected_PR];


GO
CREATE CLUSTERED INDEX [IX_VE_Connected_PR]
    ON [dbo].[PR_Related_VE]([GID_PR] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_Related_VE]
    ON [dbo].[PR_Related_VE]([GID_VE] ASC);

