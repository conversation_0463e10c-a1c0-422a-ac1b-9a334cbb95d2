﻿function FormatNegativeValue(data, ColumnName, GridViewKey) {
    if (data == null)
        return "";

    var Key = $("#hidKey_" + GridViewKey).val();

    try {
        var GridContent = data;

        var IsRed = $("#hidDisplayNegativeValuesInRed_" + GridViewKey).val();

        if (IsRed == "1") {
            if (data.indexOf("-") >= 0) {
                GridContent = "<b style='color:red'>" + data + "</b>";
            }
            else {
                GridContent = data;
            }
        }
        return GridContent;
    } catch (e) {
        LogScriptError(e.stack, "FormatNegativeValue in Partial Report", Key, GridViewKey, data);
    }
}

function GoDetailsPage(file, gid) {  
    window.location = "/DetailsPage/PRF?sPRPId=PRF_" + file + "&sRecId=" + gid + "";
}

function getLinks(entireValue, linkName) {
    var htmlContent = "";
    if (entireValue != null) {
        if (entireValue.substring(entireValue.length - 1) == ')') {
            if (entireValue.indexOf("#$%^&*") > -1) {
                entireValue = entireValue.substring(0, entireValue.indexOf("#$%^&*")) + ' ' + entireValue.substring(entireValue.lastIndexOf('('));      //List group data  with group count.. SB
            }
            return entireValue;
        }
        else if (entireValue.indexOf("#$%^&*") > -1) {
            var values = entireValue.split("#$%^&*");
            var names = values[0].split("\n");
            var gid_ids = values[1].split("\n");

            if (names.length > 0) {
                for (var i = 0; i < names.length; i++) {
                    var gid_id_Val = gid_ids[i];
                    if (gid_id_Val != undefined && gid_id_Val != null) {
                        gid_id_Val = gid_ids[i].replace("\r", "")
                    }
                    else {
                        gid_id_Val = '';
                    }

                    if (htmlContent) {
                        htmlContent = htmlContent + "&nbsp;" + "<a id='" + gid_id_Val + "' href=\'#\' onclick=LinkCall(\'" + linkName + "\',\'" + gid_id_Val + "\')>" + names[i] + "</a>";
                    }
                    else {
                        htmlContent = "<a id='" + gid_id_Val + "' href=\'#\' onclick=LinkCall(\'" + linkName + "\',\'" + gid_id_Val + "\')>" + names[i] + "</a>";
                    }
                }
            }
        }
        else {
            return entireValue;
        }
    }
    return htmlContent;
}

function getColumnHeader(columnTitle) {
    return columnTitle;
}

function LinkCall(linkName, gid) {
    var forwardid = gid.replace("\n", "");

    var GridViewKey = $("#hidPartialGridViewKey").val();
    var Key = $("#hidKey_" + GridViewKey).val();

    showProgress();
    $.ajax({
        url: '/Common/GetFileFromLinkName',
        type: "GET",
        async: false,
        data: { LinkName: linkName },
        success: function (data) {
            window.location = "/CreateForm/CreateForm/" + data + "/" + forwardid + "/TYPE/false/false/KEY/true/MASTERID/FORMKEY/FIELD/" + Key;
        },
        error: function (request, status, error) {
            hideProgress();
            if (request.responseText.length > 0 || request.responseText != null || request.responseText != undefined) {
            }
        }
    });
    hideProgress();
}

//set template data in order to get rid out of html ecryption issues need to call as a seperate method..J
function TreeListTemplateData(data, ColumnName, GridViewKey) {

    var ModelSiteId = $("#hidSiteId_" + GridViewKey).val();

    var htmlContent = "";
    if (data == null) {
    }
    else if (data.indexOf('(') == -1 && data.indexOf(')') == -1) {
        if (data.indexOf(".") >= 0) {
            if (data.toLowerCase().indexOf("cus_") >= 0) {
                htmlContent = "<img src='/PublicFiles/" + ModelSiteId + "/Images/" + data + "' alt='" + data + "' />";
            }
            else {
                htmlContent = "<img src='/Content/Images/" + data + "' alt='" + data + "' />";
            }
        }
        else {
            htmlContent = "<span>" + data + "</span>";
        }
    }
    else if (data.indexOf('<img src=') > -1) {
        htmlContent = unescape(data);
    }
    else {
        htmlContent = "<span>" + data + "</span>";
    }
    return htmlContent;
}

function ReportPerms() {
    return {
        ViewKey: $("#hidPartialGridViewKey").val(),
        MasterSelID: sessionStorage.getItem($("#hidKey_" + $("#hidPartialGridViewKey").val()).val() + "_" + "RecordId")
    };
}

function OpenReportClick(obj, file, viewid) {
    try {
        sessionStorage.setItem('OpenReportClick', true);
        var gid_id = $(obj).parent().parent().find('td:last').html();

        showProgress();
        DependencyViewsLoad(gid_id, viewid, file);

        var ModelIsMasterView = $("#hidIsMasterView_" + viewid.replace(" ", "")).val();
        var ModelIsMaster_And_Client_View = $("#hidIsMaster_And_Client_View_" + viewid.replace(" ", "")).val();
        var Key = $("#hidKey_" + viewid.replace(" ", "")).val();

        if (ModelIsMasterView == 'True' || ModelIsMaster_And_Client_View == 'True') {
            window.location = "/CreateForm/CreateForm/" + file + "/" + gid_id + "/TYPE/false/false/" + viewid + "/false/MASTERID/FORMKEY/FIELD/" + Key;
        }
        else {
            window.location = "/CreateForm/CreateForm/" + file + "/" + gid_id + "/TYPE/false/false/" + viewid + "/false/" + sessionStorage.getItem(Key + "_" + 'RecordId') + "/FORMKEY/FIELD/" + Key;
        }
    } catch (e) {
        LogScriptError(e.stack, "OpenReportClick in Partial Report", Key, viewid, $(obj).parent().parent().find('td:last').html());
    }
}

var ClicksDELAY = 700, Noclicks = 0, clickstimer = null;
function OpenLinkClick(gid_id, file, viewid) {

    try {
        //To select current row in the selected report..J
        ToSelectRowInReport = $("#grid" + viewid).data("kendoTreeList");
        ToSelectRowInReport.tbody.find("tr.k-selected").removeClass("k-selected");
        ToSelectRowInReport.tbody.css({ "color": "", "background-color": "", "border-color": "" });
        if (ToSelectRowInReport) {
            var pageData = ToSelectRowInReport.dataSource.view();
            for (var i = 0; i < pageData.length; i++) {
                if (gid_id == pageData[i].GID_ID) {
                    ToSelectRowInReport.tbody.find(">tr[data-uid='" + pageData[i].uid + "']").addClass('k-selected');
                    break;
                }
            }
        }

        //Prevent black form when double click on 'Openlink'
        Noclicks++;
        if (Noclicks === 1) {
            clickstimer = setTimeout(function () {
                sessionStorage.setItem('OpenReportClick', true);

                var ModelIsMasterView = $("#hidIsMasterView_" + viewid.replace(" ", "")).val();
                var ModelIsMaster_And_Client_View = $("#hidIsMaster_And_Client_View_" + viewid.replace(" ", "")).val();
                var Key = $("#hidKey_" + viewid.replace(" ", "")).val();

                if (ModelIsMasterView == 'True' || ModelIsMaster_And_Client_View == 'True') {
                    showProgress();
                    window.location = "/CreateForm/CreateForm/" + file + "/" + gid_id + "/TYPE/false/false/" + viewid + "/false/MASTERID/FORMKEY/FIELD/" + Key;
                }
                else {
                    showProgress();
                    window.location = "/CreateForm/CreateForm/" + file + "/" + gid_id + "/TYPE/false/false/" + viewid + "/false/" + sessionStorage.getItem(Key + "_" + 'RecordId') + "/FORMKEY/FIELD/" + Key;
                }
            }, ClicksDELAY);
        }
        else {
            clearTimeout(clickstimer);
            Noclicks = 0;
        }
    } catch (e) {
        LogScriptError(e.stack, "OpenLinkClick in Partial Report", $("#hidKey_" + viewid.replace(" ", "")).val(), viewid, gid_id);
    }
}

function ReportDocumentReady(gridId, gridKey) {
    var GridViewId = gridId; //$("#hidPartialGridViewId").val();
    var GridViewKey = gridKey; //$("#hidPartialGridViewKey").val();
    var Key = $("#hidKey_" + GridViewKey).val();
    var ModelReportTooLong = $("#hidReportTooLong_" + GridViewKey).val();
    var ModelOneLinePerRecord = $("#hidOneLinePerRecord_" + GridViewKey).val();
    var ModelAutoCount = $("#hidAutoCount_" + GridViewKey).val();
    var ModelIsTabView = $("#hidIsTabView_" + GridViewKey).val();
    var ModelIsActive = $("#hidIsActive_" + GridViewKey).val();
    var ModelIsMasterView = $("#hidIsMasterView_" + GridViewKey).val();
    var ModelIsMaster_And_Client_View = $("#hidIsMaster_And_Client_View_" + GridViewKey).val();
    var ModelIndex = $("#hidIndex_" + GridViewKey).val();
    var ModelDependencyViewIds = $("#hidDependencyViewIds_" + GridViewKey).val();
    var ModelDependencyViewIdsOnFocus = $("#hidDependencyViewIdsOnFocus_" + GridViewKey).val();
    var ModelTableName = $("#hidTableName_" + GridViewKey).val();
    var ModelViewRecordOpen = $("#hidViewRecordOpen_" + GridViewKey).val();
    var ModelOrderIndex = $("#hidOrderIndex_" + GridViewKey).val();
    var ModelIsSelectSpecificRecord = $("#hidIsSelectSpecificRecord_" + GridViewKey).val();
    var ModelSelectSpecificRecordId = $("#hidSelectSpecificRecordId_" + GridViewKey).val();
    var ModelIsParent = $("#hidIsParent_" + GridViewKey).val();
    var ModelAutoLoad = $("#hidAutoLoad_" + GridViewKey).val();
    var ModelFirstTdPossibleWidth = $("#hidFirstTdPossibleWidth_" + GridViewKey).val();
    var DesktopID = $("#hidDesktopId_" + GridViewKey).val();
    var ModelUseHeadings = $("#hidUseHeadings_" + GridViewKey).val();
    var ModelViewMultiSelect = $("#hidViewMultiSelect_" + GridViewKey).val();

    "use strict";
    if (ModelReportTooLong == true || ModelReportTooLong == 'True') {
        if (ModelAutoLoad == "1") {
            $('#' + GridViewId.replace("V", "divReportTooLong").replace(" ", "")).show();
            $('#grid' + GridViewKey).hide();
        }
        else {
            $('#' + GridViewId.replace("V", "divReportTooLong").replace(" ", "")).hide();
            $('#grid' + GridViewKey).show();
        }
    }
    else {
        $('#' + GridViewId.replace("V", "divReportTooLong").replace(" ", "")).hide();
        $('#grid' + GridViewKey).show();
    }

    if (ModelOneLinePerRecord == "1") {
        $('#grid' + GridViewKey + ' .k-grid-content table').removeClass("wraptext");
        $('#grid' + GridViewKey + ' .k-grid-content table').addClass("nowraptext");
    }
    else {
        $('#grid' + GridViewKey + ' .k-grid-content table').removeClass("nowraptext");
        $('#grid' + GridViewKey + ' .k-grid-content table').addClass("wraptext");
    }

    if (ModelAutoCount == '1') {
        $("#viewDataCountSpn" + GridViewKey).show();
    }
    else {
        $("#viewDataCountSpn" + GridViewKey).hide();
    }

    if (ModelIsTabView == 'True') {
        if (ModelIsActive == 'True') {
            $('#hdnActive').val(GridViewKey);
            $('#hdnActiveTab').val($('#hdnActiveTab').val() + "\n" + GridViewKey);
        }
    }

    //RN #1873 Multiselect functionality added.
    $("#grid" + GridViewKey).on('click', '.chkbx' + GridViewKey + '', function () {
        var ViewId = GridViewKey;
        var checked = $(this).is(':checked');
        var grid = $("#grid" + ViewId).data().kendoGrid;
        var treelist = $('#grid' + ViewId).data("kendoTreeList");

        if (treelist != null && treelist != undefined) {
            var dataItem = treelist.dataItem($(this).closest('tr'));
            if (dataItem != null && dataItem != undefined) {
                var gid_id = dataItem.GID_ID;
                var bCheck = checked;
                var historyKey = Key;
                var _uid = dataItem.uid;
                var row = treelist.table.find("[data-uid=" + _uid + "]");

                treelist.select(row);

                ManageMultipleSelectedRecords(gid_id, bCheck, ViewId, historyKey);

                showProgress();
                $.ajax({
                    url: '/Common/UpdateMultiSelectValues',
                    cache: false,
                    data: { RecordId: gid_id, IsChecked: bCheck, ViewKey: ViewId, Key: historyKey },
                    success: function (data) {
                        hideProgress();
                    },
                    error: function (data) {
                        hideProgress();
                    }
                })
            }
        }
    })

    if (ModelIsMasterView == 'True' || ModelIsMaster_And_Client_View == 'True' || ModelIsMasterView == 'False') {

        sessionStorage.setItem(Key + "_ViewId", GridViewId);
        sessionStorage.setItem("Index", ModelIndex);
        var DELAY = 700, clicks = 0, timer = null;

        //Master report row single click event..J
        $("#grid" + GridViewKey).delegate("tbody>tr", "click", function (el) {

            var treelist = $("#grid" + GridViewKey).data("kendoTreeList");
            var currentDataItem = treelist.dataItem(treelist.select());
            var gid_id = currentDataItem.GID_ID;
            var view_id = GridViewKey;

            try {
                clicks++;
                var thisobj = this;
                if (this.className.indexOf('k-treelist-group') >= 0) {
                    clicks = 0; //after action performed, reset counter
                    //return false;           //On Expandable row selection..S1
                }
                if (clicks === 1) {
                    timer = setTimeout(function () {

                        currentDataItem = treelist.dataItem(treelist.select());
                        gid_id = currentDataItem.GID_ID;

                        var lastViewID = sessionStorage.getItem(Key + "_" + "LastGridViewId");
                        var lastGID_ID = sessionStorage.getItem(Key + "_" + "LastSelectedRow");
                        if (gid_id != lastGID_ID || lastViewID != view_id) {
                            $('.panel-heading.active').parent().removeClass('z-depthstyle');
                            $('#view' + view_id).addClass('z-depthstyle');

                            if ($(thisobj)[0].className.indexOf('k-treelist-group') == -1) {
                            }

                            $(".panel-heading.active").removeClass('ViewHeaderOn');
                            $(".panel-heading.active").addClass('ViewHeaderOff');

                            ReportRowColorPersistance(gid_id);
                            sessionStorage.setItem(Key + "_" + "LastSelectedRow", gid_id);
                            sessionStorage.setItem(Key + "_" + "LastGridViewId", view_id);
                            sessionStorage.setItem(Key + "_" + "CurrentViewId", view_id);
                            var isExist = false;
                            $("#grid" + GridViewKey).find('tbody').find('tr').filter(function () {
                                isExist = $(this).css('background-color') === 'rgb(226, 226, 226)';
                                if ($(this).hasClass('defaultrowselectionGrey')) {
                                    $(this).removeClass('defaultrowselectionGrey');
                                }
                                if (isExist)
                                    $(this).removeAttr("style");
                            });

                            var dataRows = $("#grid" + view_id).data("kendoTreeList").items();
                            var SelectedRowIndex = dataRows.index($("#grid" + view_id).data("kendoTreeList").select());

                            var viewkey = view_id;
                            $.ajax({
                                url: '/Desktop/SaveSelectedRecordID',
                                type: 'GET',
                                async: false,
                                cache: false,
                                data: { RecordId: gid_id, SelectedRowIndex: SelectedRowIndex, ViewKey: viewkey, Key: Key },
                                success: function (data) {
                                },
                                error: function (data) {
                                }
                            })

                            selectedDiv(el, viewkey);
                            $('#DefaultViewId').val(viewkey);

                            sessionStorage.setItem(Key + "_" + "RecordId", gid_id);
                            sessionStorage.setItem(Key + "_" + "CRL_RecordId", gid_id);

                            if (sessionStorage.getItem('OpenReportClick') == null) {
                                if (gid_id != "") {
                                    var senderid = viewkey;
                                    IsMasterViewRowSelected = true;
                                    if ((ModelDependencyViewIds != "" && ModelDependencyViewIds != null) || (ModelDependencyViewIdsOnFocus != "" && ModelDependencyViewIdsOnFocus != null)) {
                                        OnLoadDependecies(view_id, gid_id, ModelTableName, true);
                                    }
                                }
                            }
                            else {
                                sessionStorage.removeItem('OpenReportClick');
                            }
                        }
                        else {
                            // K_M : 11/05/2018 11/05/2018 Ticket 2524 selltis70 Fixed  selected row Persistency for Report
                            var dataRows = $("#grid" + view_id).data("kendoTreeList").items();
                            var SelectedRowIndex = dataRows.index($("#grid" + view_id).data("kendoTreeList").select());

                            var viewkey = view_id;
                            $.ajax({
                                url: '/Desktop/SaveSelectedRecordID',
                                type: 'GET',
                                async: false,
                                cache: false,
                                data: { RecordId: gid_id, SelectedRowIndex: SelectedRowIndex, ViewKey: viewkey, Key: '@Model.Key' },
                                success: function (data) {
                                },
                                error: function (data) {
                                }
                            })

                            if (sessionStorage.getItem('OpenReportClick') == null) {
                                if (gid_id != "") {
                                    var senderid = viewkey;
                                    IsMasterViewRowSelected = true;
                                    if ((ModelDependencyViewIds != "" && ModelDependencyViewIds != null) || (ModelDependencyViewIdsOnFocus != "" && ModelDependencyViewIdsOnFocus != null)) {
                                        if (treelist.dataSource.total() != 0) {
                                            OnLoadDependecies(view_id, gid_id, ModelTableName, true, true);
                                            // load all dependency views, make those dependency views on focus are empty.
                                            MakeChildDependencyViewsOnFocus_AsEmpty(view_id);
                                        }
                                    }
                                }
                            }
                            else {
                                sessionStorage.removeItem('OpenReportClick');
                            }
                        }
                        clicks = 0; //after action performed, reset counter
                    }, DELAY);
                } else {
                    clearTimeout(timer); //prevent single-click action

                    clicks = 0; //after action performed, reset counter
                }
                ReportGetSortedFieldControl(ModelTableName, GridViewKey);

                //tckt #2136 Set view properties list buttons display hide/show when load view data enabled or not..J
                $("#btnCreateLinkedList").prop("disabled", false);
                $("#liVwPrPrint").removeClass('disabledLiElements');
                $("#liVwPrSendByEmail").removeClass('disabledLiElements');
                $("#liVwPrSendToExcel").removeClass('disabledLiElements');
                $("#liVwPrSendToPdf").removeClass('disabledLiElements');
                $("#liVwPrDeleteRecord").removeClass('disabledLiElements');
                $("#liVwPrDeleteAllRecords").removeClass('disabledLiElements');
            } catch (e) {
                LogScriptError(e.stack, "Master report row single click event in Partial Report", Key, view_id, gid_id);
            }


        })

        //Master report row double click event..J
        $("#grid" + GridViewKey).delegate("tbody>tr", "dblclick", function (el) {

            var viewkey = GridViewKey;
            var currentDataItem = $("#grid" + viewkey).data("kendoTreeList").dataItem($(this).select());
            var gid_id = currentDataItem.GID_ID;

            try {

                //tckt #2136 Set view properties list buttons display hide/show when load view data enabled or not..J
                $("#btnCreateLinkedList").prop("disabled", false);
                $("#liVwPrPrint").removeClass('disabledLiElements');
                $("#liVwPrSendByEmail").removeClass('disabledLiElements');
                $("#liVwPrSendToExcel").removeClass('disabledLiElements');
                $("#liVwPrSendToPdf").removeClass('disabledLiElements');
                $("#liVwPrDeleteRecord").removeClass('disabledLiElements');
                $("#liVwPrDeleteAllRecords").removeClass('disabledLiElements');

                if (ModelViewRecordOpen != '3') {

                    var dataRows = $("#grid" + viewkey).data("kendoTreeList").items();
                    var SelectedRowIndex = dataRows.index($("#grid" + viewkey).data("kendoTreeList").select());
                    showProgress();

                    $.ajax({
                        url: '/Desktop/SaveSelectedRecordID',
                        type: 'GET',
                        async: true,
                        cache: false,
                        data: { RecordId: gid_id, SelectedRowIndex: SelectedRowIndex, ViewKey: viewkey, Key: Key },
                        success: function (data) {
                        },
                        error: function (data) {
                        }
                    })

                    selectedDiv(el, viewkey);
                    if (gid_id != "" && $(this)[0].className.indexOf("k-treelist-group") == -1) {
                        $.ajax({
                            url: '/CreateForm/ClearExistingFormSession',
                            cache: false,
                            data: { File: ModelTableName, RecordId: gid_id, SelectedIndex: SelectedRowIndex, ViewKey: viewkey, Key: Key },
                            success: function (data) {
                                window.location = "/CreateForm/CreateForm/" + ModelTableName + "/" + gid_id + "/TYPE/false/false/" + viewkey + "/false/MASTERID/FORMKEY/FIELD/" + Key;
                            },
                            error: function (data) {
                            }
                        });
                    }
                }
                ReportGetSortedFieldControl(ModelTableName, GridViewKey);

            } catch (e) {
                LogScriptError(e.stack, "Master report row double click event in Partial Report", Key, GridViewKey, gid_id);
            }
        });
    }
    else {

        //Child report row signle click event..J
        $("#grid" + GridViewKey).delegate("tbody>tr", "click", function (el) {

            var currentDataItem = $("#grid" + GridViewKey).data("kendoTreeList").dataItem($(this).select());
            var gid_id = currentDataItem.GID_ID;
            var view_id = GridViewKey;

            try {

                //tckt #2136 Set view properties list buttons display hide/show when load view data enabled or not..J
                $("#btnCreateLinkedList").prop("disabled", false);
                $("#liVwPrPrint").removeClass('disabledLiElements');
                $("#liVwPrSendByEmail").removeClass('disabledLiElements');
                $("#liVwPrSendToExcel").removeClass('disabledLiElements');
                $("#liVwPrSendToPdf").removeClass('disabledLiElements');
                $("#liVwPrDeleteRecord").removeClass('disabledLiElements');
                $("#liVwPrDeleteAllRecords").removeClass('disabledLiElements');

                sessionStorage.setItem(Key + "_ViewId", GridViewId);
                sessionStorage.setItem("Index", ModelIndex);

                var lastViewID = sessionStorage.getItem(Key + "_" + "LastGridViewId");
                var lastGID_ID = sessionStorage.getItem(Key + "_" + "LastSelectedRow");
                if (gid_id != lastGID_ID || lastViewID != view_id) {
                    if ($(this)[0].className.indexOf('k-treelist-group') == -1) {
                        //To select a row on row click event
                        $(this).parent().find("tr.k-selected").removeClass("k-selected");
                        $(this).parent().css({ "color": "", "background-color": "", "border-color": "" });
                        $(this).css({ "color": "", "background-color": "", "border-color": "" });
                        $(this).removeClass("k-alt");
                        $(this).addClass('k-selected');
                    }

                    $('.panel-heading.active').parent().removeClass('z-depthstyle');
                    $('#view' + view_id).addClass('z-depthstyle');
                    ReportRowColorPersistance(gid_id);
                    sessionStorage.setItem(Key + "_" + "LastSelectedRow", gid_id);
                    sessionStorage.setItem(Key + "_" + "LastGridViewId", view_id);
                    var isExist = false;
                    $("#grid" + view_id).find('tbody').find('tr').filter(function () {
                        isExist = $(this).css('background-color') === 'rgb(226, 226, 226)';
                        if ($(this).hasClass('defaultrowselectionGrey')) {
                            $(this).removeClass('defaultrowselectionGrey');
                        }
                        if (isExist)
                            $(this).removeAttr("style");
                    });

                    var viewkey = view_id;

                    var dataRows = $("#grid" + view_id).data("kendoTreeList").items();
                    var SelectedRowIndex = dataRows.index($("#grid" + view_id).data("kendoTreeList").select());

                    $.ajax({
                        url: '/Desktop/SaveSelectedRecordID',
                        type: 'GET',
                        async: false,
                        cache: false,
                        data: { RecordId: gid_id, SelectedRowIndex: SelectedRowIndex, ViewKey: viewkey, Key: Key },
                        success: function (data) {
                        },
                        error: function (data) {
                        }
                    })

                    selectedDiv(el, viewkey);
                    $('#DefaultViewId').val(viewkey);

                    sessionStorage.setItem(Key + "_" + "CRL_RecordId", gid_id);
                }
                ReportGetSortedFieldControl(ModelTableName, GridViewKey);
            } catch (e) {
                LogScriptError(e.stack, "Child grid row signle click event in Partial Grid", Key, GridViewKey, gid_id);
            }
        })

        //child report row double click event..J
        $("#grid" + GridViewKey).delegate("tbody>tr", "dblclick", function (el) {

            var currentDataItem = $("#grid" + GridViewKey).data("kendoTreeList").dataItem($(this).select());
            var gid_id = currentDataItem.GID_ID;
            var viewkey = GridViewKey;

            try {
                //tckt #2136 Set view properties list buttons display hide/show when load view data enabled or not..J
                $("#btnCreateLinkedList").prop("disabled", false);
                $("#liVwPrPrint").removeClass('disabledLiElements');
                $("#liVwPrSendByEmail").removeClass('disabledLiElements');
                $("#liVwPrSendToExcel").removeClass('disabledLiElements');
                $("#liVwPrSendToPdf").removeClass('disabledLiElements');
                $("#liVwPrDeleteRecord").removeClass('disabledLiElements');
                $("#liVwPrDeleteAllRecords").removeClass('disabledLiElements');

                if (ModelViewRecordOpen != '3') {
                    showProgress();
                    selectedDiv(el, viewkey);
                    if (gid_id != "" && $(this)[0].className.indexOf("k-treelist-group") == -1) {
                        window.location = "/CreateForm/CreateForm/" + ModelTableName + "/" + gid_id + "/TYPE/false/false/" + GridViewId + "/false/" + sessionStorage.getItem(Key + "_" + 'RecordId') + "/FORMKEY/FIELD/" + Key;
                    }
                }
                ReportGetSortedFieldControl(ModelTableName, GridViewKey);
            } catch (e) {
                LogScriptError(e.stack, "Child report row double click event in Partial Report", Key, GridViewKey, gid_id);
            }
        });
    }

    if (isLoadFromSession == true || isLoadFromSession == 'True') {
        var lastSelViewId = sessionStorage.getItem(Key + "_lastViewid");
        var _lastSelViewId = sessionStorage.getItem(Key + "_LastGridViewId");
        if (lastSelViewId != null && lastSelViewId != "" && lastSelViewId != undefined) {
            if (GridViewKey == lastSelViewId) {
                $(".panel-heading.active").removeClass('ViewHeaderOn');
                $(".panel-heading.active").parent().removeClass('z-depthstyle');
                $("#" + ModelTableName + GridViewKey).removeClass('ViewHeaderOff').addClass('ViewHeaderOn');
                sessionStorage.setItem("type", ModelTableName);
                $("#" + 'view' + GridViewKey).addClass('z-depthstyle');
                sessionStorage.removeItem(Key + "_lastViewid");
                //05022019 tckt #2641: When open *01A Companies dsk, and select an opp, open it, then close and when you get back to the dsk, both the companies view and the opps view are blue in focus.
                sessionStorage.setItem(Key + "_" + "lastViewid", GridViewKey); //to find issue with session viewid in metadatasave and prop link
                sessionStorage.setItem(Key + "_" + "ViewId", GridViewKey);
            }
            else {
                $("#" + ModelTableName + GridViewKey).addClass('ViewHeaderOff');
            }
        }
        else {
            if (ModelOrderIndex == 1) {
                $(".panel-heading.active:first").removeClass('ViewHeaderOff');
                $(".panel-heading.active:first").addClass('ViewHeaderOn');
                $('.panel-heading.active:first').parent().addClass('z-depthstyle');
                sessionStorage.setItem(Key + "_lastViewid", GridViewKey);
                sessionStorage.setItem(Key + "_LastGridViewId", GridViewKey);
                sessionStorage.setItem(Key + "_CurrentViewId", GridViewKey);
            }
        }
    }
    else if (ModelIsTabView == 'False') {
        if (ModelOrderIndex == 1) {
            $(".panel-heading.active:first").removeClass('ViewHeaderOff');
            $(".panel-heading.active:first").addClass('ViewHeaderOn');
            $('.panel-heading.active:first').parent().addClass('z-depthstyle');
            sessionStorage.setItem(Key + "_lastViewid", GridViewKey);
            sessionStorage.setItem(Key + "_LastGridViewId", GridViewKey);
            sessionStorage.setItem(Key + "_CurrentViewId", GridViewKey);
        }
    }

    function ReportGetSortedFieldControl(TableName, ViewKey) {
        try {
            showProgress();
            $.ajax({
                url: '/Desktop/GetSortedFieldControl',
                type: "GET",
                async: false,
                cache: false,
                data: { datafield: "", ViewKey: ViewKey.replace(" ", ""), Key: Key, ViewMultiSelect: ModelViewMultiSelect },
                success: function (data) {
                    if (data.FilterText != null) {

                        $("#QuickFilterBar").html("");
                        $("#QuickFilterBar").html(data.FilterText);

                        SetDocumentHeight();

                        var myval = ViewKey.replace(" ", "");
                        var _tooltip = data.ToolTip;
                        $("#" + data.TableName + myval).attr("title", _tooltip);
                    }
                    hideProgress();
                },
                error: function (data) {
                    hideProgress();
                }
            })
        } catch (e) {
            LogScriptError(e.stack, "ReportGetSortedFieldControl in Partial Report", Key, ViewKey, "");
        }
    }

    $('#view' + GridViewKey).click(function (e) {

        $('#DefaultViewId').val(GridViewKey);
        sessionStorage.setItem(Key + "_ViewId", GridViewId);
        sessionStorage.setItem("Index", ModelIndex);
        sessionStorage.setItem("type", ModelTableName);

        //remove k-alt class for all alternative rows when click on expand and collapse symbols..J
        var report = $("#grid" + GridViewKey).data("kendoTreeList");

        //condition added -- or else it will through an script error when change from map/chart to report..J
        if (report != undefined && report != null) {
            report.tbody.find('tr').removeClass('k-alt');
        }
    });

    function GetColumnIndexFromName(strName) {
        var index = -1;
        var strName = strName;

        var grid = $('#grid' + GridViewKey).data("kendoTreeList");
        if (grid != null) {
            var colCount = grid.columns.length;
            var columns = grid.options.columns;

            if (columns.length > 0) {
                for (var i = 0; i < columns.length; i++) {
                    if (columns[i].title == strName) {
                        index = i;
                    }
                }
            }
        }
        return index;
    }

    function jsSaveSelectedRecordIDInReport(gid_id, SelectedRowIndex, viewkey, _key) {
        try {
            $.ajax({
                url: '/Desktop/SaveSelectedRecordID',
                type: 'GET',
                async: false,
                cache: false,
                data: { RecordId: gid_id, SelectedRowIndex: SelectedRowIndex, ViewKey: viewkey, Key: _key },
                success: function (data) {
                },
                error: function (data) {
                }
            })
        } catch (e) {
            LogScriptError(e.stack, "jsSaveSelectedRecordIDInReport in Partial report", _key, viewkey, gid_id);
        }
    }

    //Report View DataBound Event
    $("#grid" + GridViewKey).data("kendoTreeList").bind('dataBound', function (e) {

        try {
            //debugger

            //To select Previous opened record in edit form...
            var treeview = $('#grid' + GridViewKey).data("kendoTreeList");
            var pageData = treeview.dataSource.view();
            var visibleRecordCount = 0;
            var _gridid = GridViewKey;
            var _tablename = ModelTableName;

            var divItem = $('#' + _tablename + _gridid);

            this.element.find('tr.k-alt').removeClass("k-alt");
            this.element.find('tr.k-treelist-group').css({ "color": "black", "background-color": "#DFDFDF", "border-color": "#428bca" });


            var toprec = '';
            var treelist = e.sender;

            if (treelist.dataSource._data.length == 0) {
                treelist.thead.addClass('displaynone');
            }
            else {
                treelist.thead.removeClass('displaynone');
                toprec = treelist.dataSource._data[0].GID_ID;
            }
            var _thisId = e.sender.wrapper[0].id;

            _thisId = _thisId.replace('grid', '');

            //set the top rec to hidden field
            var hidfieldid = "#hidSelRecId_" + _thisId;
            $(hidfieldid).val(toprec);

            var valcolumns = $('#AggregateColumns' + _thisId).val();
            var expandcolumns = $('#DISPLAYSECTIONS' + _thisId).val();
            var _CURSymbolscsv = $('#CURSymbols' + _thisId).val();
            var _CURSymbolscsvarr = _CURSymbolscsv.split(',');
            var valcurrency = "";
            var _Perarrytotal = [];

            var expandedcolcount = 0;

            if ($(this).length > 0) {
                var trdata = $(this)[0].table.find("tr.k-treelist-group");
                var val = $(this)[0].table[0].innerHTML;

                //TOTAL|AVG|MIN|MAX|MID
                $(this)[0].table.find("tr.k-treelist-group").each(function (trindex, trvalue) {             //LOOP 1
                    if (expandcolumns == 1) {
                        if (expandedcolcount <= 4) { //exapand only first 5 groups by default
                            treelist.expand(trvalue);
                            expandedcolcount++;
                        }
                    }
                    else if (expandcolumns == 3) {
                        if (trindex == 0) {
                            treelist.expand(trvalue);
                        }
                    }

                    var Val_Html = $(trvalue).find('td:first')[0].innerHTML;
                    if (Val_Html.substring(Val_Html.length - 1) == ')') {
                        if (Val_Html.indexOf("#$%^&amp;*") > -1) {
                            Val_Html = Val_Html.substring(0, Val_Html.indexOf("#$%^&amp;*")) + ' ' + Val_Html.substring(Val_Html.lastIndexOf('('));      //List group data  with group count.. SB
                            $(trvalue).find('td:first')[0].innerHTML = Val_Html;
                        }
                    }

                    //Hide open link for group items..J
                    if ($(trvalue).find('td:first')[0].innerHTML.indexOf('OpendLinkPO') > -1) {

                        if (ModelViewRecordOpen == '2' || ModelViewRecordOpen == '3') {
                            $(trvalue).find('td:nth-child(2)')[0].innerHTML = $(trvalue).find('td:first')[0].innerHTML.replace("style=\"cursor: pointer;\"", "style=\"cursor: pointer;display:none;\"") + $(trvalue).find('td:nth-child(2)')[0].innerHTML;
                            $(trvalue).find('td:first')[0].innerHTML = "";
                        }
                        else {
                            $(trvalue).find('td:first')[0].innerHTML = $(trvalue).find('td:first')[0].innerHTML.replace("style=\"cursor: pointer;\"", "style=\"cursor: pointer;display:none;\"");
                        }
                    }

                    var val = trvalue.innerHTML;
                    val = val.replace(/&lt;br&gt;/g, "<br>");
                    trvalue.innerHTML = val;

                    var poslength = ModelFirstTdPossibleWidth;
                    poslength = poslength / 6;

                    var txt = trvalue.cells[0].innerText;
                    txt = txt.replace("Avg:", "");
                    txt = txt.replace("Min:", "");
                    txt = txt.replace("Max:", "");
                    txt = txt.trim();
                    var textlength = txt.length;

                    if (textlength < poslength) {
                        $(trvalue).find('td:first').css('overflow', 'visible');
                    }
                    else {
                        $(trvalue).find('td:first').css('overflow', 'hidden');
                    }
                    $(trvalue).find('td:first').css('text-overflow', 'ellipse');
                    $(trvalue).find('td:first').css('white-space', 'nowrap');
                    $(trvalue).find('td:first').css('text-align', 'left');
                    //SB 11-01-2017 Tckt#1901 Report Grouping Issues
                    var inHtml = $(trvalue.cells[0].innerHTML).text(); //Removes html tags and returns only text.. SB
                    $(trvalue).find('td:first').attr('title', inHtml);

                    trvalue.style.backgroundColor = "#D3D3D3 !important";
                    trvalue.style.color = "#000000 !important";
                    trvalue.style.verticalAlign = "middle";
                    trvalue.style.fontWeight = "bold";
                });

                $(this)[0].table.find("tr.k-alt").each(function (trindex, trvalue) {                    //LOOP 2
                    $(this).removeClass('k-alt');
                    if (greyGID_ID != "") {
                        var divItem = $('#' + ModelTableName + GridViewKey);

                        if (divItem.hasClass('ViewHeaderOn') == false) {
                            treeview.tbody.find(">tr[data-uid='" + greyGID_ID + "']").removeClass("defaultrowselectionGrey");
                            treeview.tbody.find(">tr[data-uid='" + greyGID_ID + "']").addClass("defaultrowselectionGrey");
                        }
                        greyGID_ID = "";
                    }

                });
            }


            if (ModelUseHeadings == '0') {
                $('#grid' + GridViewKey + ' .k-grid-header').hide();
            }
            else {
                $('#grid' + GridViewKey + ' .k-grid-header').show();
            }

            var newSelection = [];
            var selected = false;

            var LastSelectedRowIndex = 0;
            var LastSelectedRowValue = 0;
            LastSelectedRowIndex = $("#hdn_LastSelectedVal_" + GridViewKey).val();
            LastSelectedRowValue = $("#hdn_LastSelectedValue_" + GridViewKey).val();
            if (IsMasterViewRowSelected) {
                LastSelectedRowIndex = 0;
            }

            //debugger
            var greyGID_ID = "";
            var TabClickedReport = sessionStorage.getItem(Key + "_" + "TabClicked");
            if (pageData.length > 0) {

                $.ajax({
                    cache: false,
                    async: false,
                    url: '/Common/GetSessionData',
                    dataType: "json",
                    type: "GET",
                    contentType: 'application/json',
                    data: {
                        ViewKey: _gridid, Key: Key
                    },
                    success: function (data) {

                        LastSelectedRowIndex = data.LastSelectedRowIndex;
                        LastSelectedRowValue = data.LastSelectedRowGID_ID;
                        visibleRecordCount = data.VisibleRecordCount;                                               

                        //if performs delete in edit form..
                        if (LastSelectedRowIndex >= pageData.length) {
                            LastSelectedRowIndex = LastSelectedRowIndex - pageData.length;
                        }

                        var LastSelectedRowGID_ID = pageData[LastSelectedRowIndex].GID_ID;

                        if (LastSelectedRowValue != null && LastSelectedRowValue != "" && LastSelectedRowValue != undefined) {
                            if (LastSelectedRowValue != LastSelectedRowGID_ID) {    //If the selected record were deleted/change the data (not according to the filter in the view) in form then get that based on index(it may be the next record)..J
                                LastSelectedRowValue = LastSelectedRowGID_ID;
                                jsSaveSelectedRecordIDInReport(LastSelectedRowValue, LastSelectedRowIndex, _gridid.replace(" ", ""), Key);
                                if ((ModelDependencyViewIds != "" && ModelDependencyViewIds != null) || (ModelDependencyViewIdsOnFocus != "" && ModelDependencyViewIdsOnFocus != null)) {
                                    OnLoadDependecies(_gridid, LastSelectedRowValue, ModelTableName, true); //Now call row single click for this record to load child views..J
                                }
                            }
                        }
                    },
                    error: function (xhr) {
                    }
                });               

                $("#viewdatacount" + _gridid).text(kendo.toString(visibleRecordCount, "n0"));

                //Select specific record starts
                var IsSelectSpecificRecord = ModelIsSelectSpecificRecord;

                if (IsSelectSpecificRecord == "True") {
                    var SelectSpecificRecordId = ModelSelectSpecificRecordId;

                    for (var k = 0; k < pageData.length; k++) {                         //Loop to get selected record index
                        if (pageData[k].GID_ID == SelectSpecificRecordId) {
                            LastSelectedRowValue = SelectSpecificRecordId;
                            LastSelectedRowIndex = k;
                            break;
                        }
                    }
                }
                //Select specific record ends                              

                if (LastSelectedRowIndex >= 0) {

                    if (pageData[LastSelectedRowIndex] != null && pageData[LastSelectedRowIndex] != undefined) {

                        var LastSelectedRowGID_ID = pageData[LastSelectedRowIndex].GID_ID;
                        if (LastSelectedRowGID_ID == undefined || LastSelectedRowGID_ID == null) {
                            for (i = LastSelectedRowIndex; i < pageData.length; i++) {                      //LOOP 3
                                if (pageData[i].hasChildren == false) {
                                    LastSelectedRowGID_ID = pageData[i].GID_ID;
                                    LastSelectedRowIndex = i;
                                    break;
                                }
                            }
                        }

                        if (LastSelectedRowValue != null && LastSelectedRowValue != "" && LastSelectedRowValue != undefined) {
                            if (LastSelectedRowValue != LastSelectedRowGID_ID) {
                                for (var k = LastSelectedRowIndex; k < pageData.length; k++) {              //LOOP 5
                                    if (pageData[k].hasChildren == false) {
                                        if (pageData[k].GID_ID == LastSelectedRowValue) {
                                            LastSelectedRowIndex = k;
                                            LastSelectedRowGID_ID = LastSelectedRowValue;
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        LastSelectedRowValue = LastSelectedRowGID_ID;                        
                    }
                }

                SetSelectedRowColor_In_Report(divItem, treeview, pageData, _gridid, LastSelectedRowValue, LastSelectedRowIndex);

                //RN #1873 Multiselect functionality added.
                ManageMultiSelectPersistancy(pageData, GridViewKey, Key);
            }
            else {
                //Set visible count and total count to "0" in view header when no data..J
                $("#viewdatacount" + GridViewKey).text("0");
                $("#viewpagesize" + GridViewKey).text("0");

                if (TabClickedReport) {
                    RowColorPersistance();
                    sessionStorage.removeItem(Key + "_" + "TabClicked");
                }
            }

            //var myval = GridViewKey;

            if (ModelAutoLoad == "0") {
                $($('#grid' + _gridid.replace(" ", "")).data("kendoTreeList").wrapper).find('.k-status').css("display", "none");
            }            

            //Removing unwanted spaces in checkbox Columns..RN
            //RN #1873 Multiselect functionality added.
            for (var j = 1; j < treeview.tbody[0].children.length; j++) {                                   //LOOP 7
                //Removing unwanted spaces in image Columns..S1
                if (treeview.tbody[0].children[j].innerHTML.indexOf('<span class="k-icon k-i-none"></span><span class="k-icon k-i-none"></span><img') >= 0) {
                    treeview.tbody[0].children[j].innerHTML = treeview.tbody[0].children[j].innerHTML.replace('<span class="k-icon k-i-none"></span><span class="k-icon k-i-none"></span><img', "<span class=\"k-icon k-i-none\" style=\"display: none;\"></span><span class=\"k-icon k-i-none\" style=\"display: none;\"></span><img");
                }
                //debugger
                if (treeview.tbody[0].children[j].innerHTML.indexOf('<span class="k-icon k-i-none"></span><span class="k-icon k-i-none"></span><span class="k-icon k-i-none"></span><span class="k-icon k-i-none"></span><input') >= 0) {
                    treeview.tbody[0].children[j].innerHTML = treeview.tbody[0].children[j].innerHTML.replace(/<span class=\"k-icon k-i-none\"><\/span><span class=\"k-icon k-i-none\"><\/span><span class="k-icon k-i-none"><\/span><span class="k-icon k-i-none"><\/span><input/g, "<span class=\"k-icon k-i-none\" style=\"display: none;\"></span><span class=\"k-icon k-i-none\" style=\"display: none;\"></span><span class=\"k-icon k-i-none\" style=\"display: none;\"></span><span class=\"k-icon k-i-none\" style=\"display: none;\"></span><input");
                }
                if (treeview.tbody[0].children[j].innerHTML.indexOf('<span class="k-icon k-i-none"></span><span class="k-icon k-i-none"></span><span class="k-icon k-i-none"></span><input') >= 0) {
                    treeview.tbody[0].children[j].innerHTML = treeview.tbody[0].children[j].innerHTML.replace(/<span class="k-icon k-i-none"><\/span><span class="k-icon k-i-none"><\/span><span class="k-icon k-i-none"><\/span><input/g, "<span class=\"k-icon k-i-none\" style=\"display: none;\"></span><span class=\"k-icon k-i-none\" style=\"display: none;\"></span><span class=\"k-icon k-i-none\" style=\"display: none;\"></span><input");
                }
                if (treeview.tbody[0].children[j].innerHTML.indexOf('<span class="k-icon k-i-none"></span><span class="k-icon k-i-none"></span><input') >= 0) {
                    treeview.tbody[0].children[j].innerHTML = treeview.tbody[0].children[j].innerHTML.replace(/<span class="k-icon k-i-none"><\/span><span class="k-icon k-i-none"><\/span><input/g, "<span class=\"k-icon k-i-none\" style=\"display: none;\"></span><span class=\"k-icon k-i-none\" style=\"display: none;\"></span><input");
                }
                if (treeview.tbody[0].children[j].innerHTML.indexOf('<span class="k-icon k-i-none"></span><input') >= 0) {
                    treeview.tbody[0].children[j].innerHTML = treeview.tbody[0].children[j].innerHTML.replace(/<span class="k-icon k-i-none"><\/span><input/g, "<span class=\"k-icon k-i-none\" style=\"display: none;\"></span><input");
                }
            }
            
        } catch (e) {
            LogScriptError(e.stack, "ReportDatabound", Key, _gridid, LastSelectedRowValue);
        }
    })

    function SetSelectedRowColor_In_Report(divItem, treeview, pageData, viewid, LastSelectedRow, LastSelectedRowIndex) {

        try {
            if (divItem.hasClass('ViewHeaderOn')) {
                treeview.table.focus(); // set focus to report when load from session is true
                ReportGetSortedFieldControl(ModelTableName, viewid);
                treeview.tbody.find(">tr[data-uid='" + pageData[LastSelectedRowIndex].uid + "']").removeClass("k-alt");
                treeview.tbody.find(">tr[data-uid='" + pageData[LastSelectedRowIndex].uid + "']").addClass("k-selected");

                sessionStorage.setItem(Key + "_" + "RecordId", treeview.tbody.find(">tr[data-uid='" + pageData[LastSelectedRowIndex].uid + "'] td:last").html());
                sessionStorage.setItem(Key + "_" + "LastSelectedRow", treeview.tbody.find(">tr[data-uid='" + pageData[LastSelectedRowIndex].uid + "'] td:last").html());
                sessionStorage.setItem(Key + "_" + "CRL_RecordId", treeview.tbody.find(">tr[data-uid='" + pageData[LastSelectedRowIndex].uid + "'] td:last").html());
                sessionStorage.setItem(Key + "_" + "LastGridViewId", viewid);

            }
            else {
                //greyGID_ID = pageData[LastSelectedRowIndex].uid;
                //EmptyDependencyViewsOnFocus_Report(GridViewKey);
                treeview.tbody.find(">tr[data-uid='" + pageData[LastSelectedRowIndex].uid + "']").addClass('defaultrowselectionGrey');
            }

            //setting grid scroll on selected row on post back from FORM ..S1
            if (isLoadFromSession == 'True' || ModelIsSelectSpecificRecord == "True") {
                var childCount = treeview.tbody[0].children.length;
                for (var curChild = 0; curChild < childCount; curChild++) {
                    if (treeview.tbody[0].children[curChild].className == 'k-selected' || treeview.tbody[0].children[curChild].className == 'defaultrowselectionGrey') {

                        try {
                            var eleTop = treeview.tbody[0].children[curChild].offsetTop;
                            var treeTop = treeview.element.find('.k-grid-content').position().top;
                            var treeScrollTop = treeview.element.find('.k-grid-content').scrollTop();
                            treeview.element.find('.k-grid-content').animate({
                                scrollTop: (treeScrollTop + eleTop) - treeTop
                            });
                        } catch (e) {
                            alert(e.stack);
                        }

                        
                        return;
                    }
                }
            }

        } catch (e) {
            LogScriptError(e.stack, "GridRowPersistancy in Partial Grid", Key, viewid, LastSelectedRow);
        }
    }

    $("#grid" + GridViewKey).on("keydown", function (e) {
        if (e.which != 13 && e.which != 38 && e.which != 40) {
            if (document.getElementById("QuickSelectCONDITION") != null && $('#QuickSelectCONDITION').length > 0)   {
                document.getElementById("QuickSelectCONDITION").focus();
            }            
        }

        // when click on enter key
        if (e.which == 13) {

            var gid_id = sessionStorage.getItem(Key + "_" + "LastSelectedRow");
            var LastGridViewId = sessionStorage.getItem(Key + "_" + "LastGridViewId");

            try {
                if (ModelIsMasterView == 'True' || ModelIsMaster_And_Client_View == 'True') {
                    showProgress();
                    window.location = "/CreateForm/CreateForm/" + ModelFile + "/" + gid_id + "/TYPE/false/false/" + GridViewKey + "/false/MASTERID/FORMKEY/FIELD/" + Key;
                }
                else {
                    showProgress();
                    window.location = "/CreateForm/CreateForm/" + ModelFile + "/" + gid_id + "/TYPE/false/false/" + LastGridViewId + "/false/" + sessionStorage.getItem(Key + "_" + 'RecordId') + "/FORMKEY/FIELD/" + Key;
                }
            } catch (e) {
                LogScriptError(e.stack, "Enter click to open record in Partial Report", Key, LastGridViewId, gid_id);
            }
        }
            //up key
        else if (e.which == 38) {

            try {
                if (e.ctrlKey) {
                    $("#grid" + GridViewKey).data("kendoTreeList").table.focus();
                }
                else {
                    if ($("#grid" + GridViewKey + " .k-grid-content table.k-selectable tr.k-selected").prev().hasClass("k-treelist-group")) {
                        //check for 2nd group level if the previous one of selected record is a group or not..J
                        if ($("#grid" + GridViewKey + " .k-grid-content table.k-selectable tr.k-selected").prev().prev().hasClass("k-treelist-group")) {
                            //check for another level of grouping, if its there then add selection to the previous record in the above group..J
                            if ($("#grid" + GridViewKey + " .k-grid-content table.k-selectable tr.k-selected").prev().prev().prev().prev().prev().hasClass("k-treelist-group")) {
                                $("#grid" + GridViewKey + " .k-grid-content table.k-selectable tr.k-selected").removeClass("k-selected").prev().prev().prev().addClass("k-selected");
                            }
                            else {
                                //return if the selected record is the first in first group..J
                                return;
                            }
                        }
                        else {
                            //check for first record if the selected one is first record and also has no group at its top then return..J
                            if ($("#grid" + GridViewKey).data("kendoTreeList").tbody.find("tr:first").next()[0].innerText == $("#grid" + GridViewKey + " .k-grid-content table.k-selectable tr.k-selected")[0].innerText) {
                                return;
                            }
                            $("#grid" + GridViewKey + " .k-grid-content table.k-selectable tr.k-selected").removeClass("k-selected").prev().prev().addClass("k-selected");
                        }
                    }
                    else {
                        $("#grid" + GridViewKey + " .k-grid-content table.k-selectable tr.k-selected").removeClass("k-selected").prev().addClass("k-selected");
                    }

                    var report = $("#grid" + GridViewKey).data("kendoTreeList");
                    var selectedItem = report.dataItem(report.select());

                    if (selectedItem != null) {
                        $("#grid" + GridViewKey).find("tbody > tr").eq(report.select().index()).trigger('click');
                        $('#view' + GridViewKey).trigger('click');
                        $("#grid" + GridViewKey).data("kendoTreeList").table.focus();
                    }
                }
            } catch (e) {
                LogScriptError(e.stack, "Report selected row key up in Partial Report", Key, GridViewKey, "");
            }
        }
            //down key
        else if (e.which == 40) {

            try {
                if (e.ctrlKey) {
                    $("#grid" + GridViewKey).data("kendoTreeList").table.focus();
                }
                else {
                    if ($("#grid" + GridViewKey + " .k-grid-content table.k-selectable tr.k-selected").next().hasClass("k-treelist-group")) {
                        //check for 2nd group level if the previous one of selected record is a group or not..J
                        if ($("#grid" + GridViewKey + " .k-grid-content table.k-selectable tr.k-selected").next().next().hasClass("k-treelist-group")) {
                            //check for another level of grouping, if its there then add selection to the previous record in the above group..J
                            if ($("#grid" + GridViewKey + " .k-grid-content table.k-selectable tr.k-selected").next().next().next().next().hasClass("k-treelist-group")) {
                                $("#grid" + GridViewKey + " .k-grid-content table.k-selectable tr.k-selected").removeClass("k-selected").next().next().next().addClass("k-selected");
                            }
                            else {
                                return;
                            }
                        }
                        else {
                            $("#grid" + GridViewKey + " .k-grid-content table.k-selectable tr.k-selected").removeClass("k-selected").next().next().addClass("k-selected");
                        }
                    }
                    else {
                        //check for first record if the selected one is last record and also has no group at its below then return..J
                        if ($("#grid" + GridViewKey).data("kendoTreeList").tbody.find("tr:last")[0].innerText == $("#grid" + GridViewKey + " .k-grid-content table.k-selectable tr.k-selected")[0].innerText) {
                            return;
                        }
                        $("#grid" + GridViewKey + " .k-grid-content table.k-selectable tr.k-selected").removeClass("k-selected").next().addClass("k-selected");
                    }

                    var report = $("#grid" + GridViewKey).data("kendoTreeList");
                    var selectedItem = report.dataItem(report.select());

                    if (selectedItem != null) {
                        $("#grid" + GridViewKey).find("tbody > tr").eq(report.select().index()).trigger('click');
                        $('#view' + GridViewKey).trigger('click');
                        $("#grid" + GridViewKey).data("kendoTreeList").table.focus();
                    }
                }
            } catch (e) {
                LogScriptError(e.stack, "Report selected row key down in Partial Report", Key, GridViewKey, "");
            }
        }
    });

    //tckt #2136 Set view properties list buttons display hide/show when load view data enabled or not..J
    var divItem = $('#' + ModelTableName + GridViewKey);
    if (divItem.hasClass('ViewHeaderOn')) {
        if (ModelAutoLoad == "1") {
            $("#btnCreateLinkedList").prop("disabled", false);
            $("#liVwPrPrint").removeClass('disabledLiElements');
            $("#liVwPrSendByEmail").removeClass('disabledLiElements');
            $("#liVwPrSendToExcel").removeClass('disabledLiElements');
            $("#liVwPrSendToPdf").removeClass('disabledLiElements');
            $("#liVwPrDeleteRecord").removeClass('disabledLiElements');
            $("#liVwPrDeleteAllRecords").removeClass('disabledLiElements');
        }
        else {
            $("#btnCreateLinkedList").prop("disabled", true);
            $("#liVwPrPrint").addClass('disabledLiElements');
            $("#liVwPrSendByEmail").addClass('disabledLiElements');
            $("#liVwPrSendToExcel").addClass('disabledLiElements');
            $("#liVwPrSendToPdf").addClass('disabledLiElements');
            $("#liVwPrDeleteRecord").addClass('disabledLiElements');
            $("#liVwPrDeleteAllRecords").addClass('disabledLiElements');
        }
    }

    if (ModelAutoLoad == '1') {
        if ($("#divAutoLoadView" + GridViewKey).length > 0) {
            if ($("#divAutoLoadView" + GridViewKey)[0].innerText.indexOf("Click here to load view.") > -1 || $("#divAutoLoadView" + GridViewKey)[0].innerText.indexOf("Click here to not load view.") > -1) {
                $("#divAutoLoadView" + GridViewKey).show();
            }
            else {
                $("#divAutoLoadView" + GridViewKey).hide();
            }
        }
        else {
            $("#divAutoLoadView" + GridViewKey).hide();
        }
    }
    else {
        if ($("#divAutoLoadView" + GridViewKey).length > 0) {
            if ($("#divAutoLoadView" + GridViewKey)[0].innerText.indexOf("Click here to load view.") > -1 || $("#divAutoLoadView" + GridViewKey)[0].innerText.indexOf("Click here to not load view.") > -1) {
                $("#divAutoLoadView" + GridViewKey).show();
            }
            else {
                $("#divAutoLoadView" + GridViewKey).hide();
            }
        }
        else {
            $("#divAutoLoadView" + GridViewKey).show();
        }
    }
}

function stripHTML(html) {
    var tmp = document.createElement("DIV");
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || "";
}

function ExpandAllGroups(_viewId) {

    try {
        var treelist = $('#grid' + _viewId).data("kendoTreeList");
        var rows = $("tr.k-treelist-group", treelist.tbody);

        showProgress();

        if ($("#ExpandCollapse" + _viewId).hasClass('fa-arrows')) {
            $.each(rows, function (idx, row) {
                treelist.expand(row);
                row.firstChild.innerHTML = row.firstChild.innerHTML.replace('k-icon k-i-expand', 'k-icon k-i-collapse');
            });
        }

        hideProgress();
    } catch (e) {
        LogScriptError(e.stack, "ExpandAllGroups in Partial Grid", Key, _viewId, "");
    }


}

function SetLastSelected_Info_IntoSession(viewid, lastselectedrowgid_id) {
    var lastId = sessionStorage.getItem(Key + "_" + "LastSelectedRow");
    if (lastselectedrowgid_id != lastId) {
        RowColorPersistance(viewid);
    }
    sessionStorage.setItem(Key + "_" + "LastGridViewId", viewid);
    sessionStorage.setItem(Key + "_" + "LastSelectedRow", lastselectedrowgid_id);
    sessionStorage.setItem(Key + "_" + "CRL_RecordId", lastselectedrowgid_id);
}

function TabClickedChanges(treeview, lastselectedrowindex, viewid, lastselectedrowgid_id, pagedata) {
    var Key = $("#hidKey_" + viewid.replace(" ", "")).val();

    treeview.tbody.find(">tr[data-uid='" + pagedata[lastselectedrowindex].uid + "']").removeClass("k-alt");
    treeview.tbody.find(">tr[data-uid='" + pagedata[lastselectedrowindex].uid + "']").addClass("k-selected");

    SetLastSelected_Info_IntoSession(viewid, lastselectedrowgid_id);
    sessionStorage.removeItem(Key + "_" + "TabClicked");
}

function ReportRowColorPersistance(gid_id) {
    RowColorPersistance();
}

function RemoveSelectedRowColor(gid_id) {

    var Key = $("#hidKey_" + viewid.replace(" ", "")).val();
    var LastGridViewId = sessionStorage.getItem(Key + "_LastGridViewId");

    try {
        if (gid_id != null && gid_id != undefined) {
            var LastSelectedGrid = $("#grid" + LastGridViewId).data("kendoGrid");
            if (LastSelectedGrid) {
                if (LastSelectedGrid.tbody.find('tr').hasClass('defaultrowselectionGrey')) {
                    LastSelectedGrid.tbody.find('tr').removeClass('defaultrowselectionGrey');
                }
                var pageData = LastSelectedGrid.dataSource.view();
                for (var i = 0; i < pageData.length; i++) {
                    if (gid_id == pageData[i].GID_ID) {
                        LastSelectedGrid.tbody.find(">tr[data-uid='" + pageData[i].uid + "']").addClass('defaultrowselectionGrey');
                        break;
                    }
                }
            }
        }
    } catch (e) {
        LogScriptError(e.stack, "RemoveSelectedRowColor in Partial Grid", Key, LastGridViewId, gid_id);
    }
}

function EmptyDependencyViewsOnFocus_Report(viewid) {

    try {
        var dependenceviewids = $("#hidDepViewsOnFocus_" + viewid).val();;

        var lsViewIds = dependenceviewids.split(",");

        for (var i = 0; i < lsViewIds.length; i++) {

            var viewId = lsViewIds[i].replace(" ", "");

            var viewtype = $("#hidViewType_" + viewId.replace(" ", "")).val();

            if (viewtype == null || viewtype == undefined || viewtype == "undefined") {
                var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();
                var ids = viewIds_and_viewtypes.split("|");

                for (var j = 0; j < ids.length; j++) {
                    var types = ids[j].split(",");
                    if (types[0].replace(" ", "") == viewId.replace(" ", "")) {
                        viewtype = types[1];
                        break;
                    }
                }
            }

            var grid_report_object = null;

            if (viewtype == "list") {
                grid_report_object = $("#grid" + viewId.replace(" ", "")).data("kendoGrid");
            }
            else if (viewtype == "report") {
                grid_report_object = $("#grid" + viewId.replace(" ", "")).data("kendoTreeList");
            }
            else if (viewtype == "calendar") {
                grid_report_object = $("#calender" + viewId.replace(" ", "")).data("kendoScheduler");
            }

            if (grid_report_object != null) {
                grid_report_object.dataSource.data([]);
            }
        }
    } catch (e) {
        LogScriptError(e.stack, "EmptyDependencyViewsOnFocus_Report in Partial Grid", Key, viewid, "");
    }
}

function AvoidReportDragAndDropInMobileLAyout(GridViewKey) {

    try {
        var ModelTableName = $("#hidTableName_" + GridViewKey).val();

        var grid = $("#grid" + GridViewKey).data("kendoTreeList"),
            gridRowOffset = grid.tbody.find("tr:first").offset();

        var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();

        //avoid drag and rop for mobile and tablet..J
        var isMobile = {
            Android: function () {
                return navigator.userAgent.match(/Android/i);
            },
            BlackBerry: function () {
                return navigator.userAgent.match(/BlackBerry/i);
            },
            iOS: function () {
                return navigator.userAgent.match(/iPhone|iPad|iPod/i);
            },
            Opera: function () {
                return navigator.userAgent.match(/Opera Mini/i);
            },
            Windows: function () {
                return navigator.userAgent.match(/IEMobile/i);
            },
            any: function () {
                return (isMobile.Android() || isMobile.BlackBerry() || isMobile.iOS() || isMobile.Opera() || isMobile.Windows());
            }
        };

        if (isMobile.any()) return;
        if (isMobile.iOS()) return;

        if (viewIds_and_viewtypes.indexOf('calendar') > -1) {
            grid.table.kendoDraggable({
                filter: "tbody > tr",
                dragstart: function (e) {
                    //add margin to position correctly the tooltip under the pointer
                    $("#dragTooltip").css("margin-left", e.clientX - gridRowOffset.left - 50);
                },
                hint: function (row) {
                    //remove old selection
                    row.parent().find(".k-selected").each(function () {
                        $(this).removeClass("k-selected")
                    })

                    //add selected class to the current row
                    row.addClass("k-selected");
                    //return if the selected row is treelist group..J
                    if (row.hasClass("k-treelist-group k-selected")) {
                        return;
                    }
                    //var dataItem = grid.dataItem(row);
                    dataItem = grid.dataItem(row);

                    var tooltipText = row[0].innerText;
                    if (tooltipText != undefined && tooltipText != null) {
                        if (tooltipText.indexOf('Open') > -1) {
                            tooltipText = tooltipText.replace("Open", "");
                        }
                        if (tooltipText.indexOf(dataItem.GID_ID) > -1) {
                            tooltipText = tooltipText.replace(dataItem.GID_ID, "");
                        }
                    }

                    sessionStorage.setItem("DraggedObject", dataItem.GID_ID + "|" + ModelTableName);

                    var tooltipHtml = "<div class='k-event' id='dragTooltip'>" +
                                "<div class='k-event-template'>" + tooltipText +
                                "</div></div>";

                    return $(tooltipHtml).css("width", 100);
                }
            });
        }
    } catch (e) {
        LogScriptError(e.stack, "Avoid drag and rop for mobile and tablet in Partial Report", Key, GridViewKey, "");
    }
}


function GetMLSControlR(_tablename, _fieldname, _defselval, _width, _ViewId, _key, _title) {
    //debugger;
    var _data = "";
    PageSizeChanged = "true";

    if (_defselval != null) {
        _defselval = _defselval.replace("<", "").replace(">", "");

        //get the MLS control from local session
        var cookieName = _ViewId.replace(" ", "") + "_" + _tablename + "_" + _fieldname + "_" + _defselval;
        _data = localStorage.getItem(cookieName);
        if (_data != null) {
            return _data;
        }

        return GetMLS_Control_As_String(_tablename, _fieldname, _defselval, _width, _ViewId, _key, _title, cookieName);

        //$.ajax({
        //    url: '/Common/GetMLS_Control_As_StringR',
        //    type: "GET",
        //    async: false,
        //    data: { TableName: _tablename, FieldName: _fieldname, selectedValue: _defselval, width: _width, ViewId: _ViewId, Key: _key, Title: _title },
        //    success: function (data) {
        //        _data = data;
        //        localStorage.setItem(cookieName, _data);
        //    },
        //    error: function (request, status, error) {
        //        return "";
        //    }
        //});        

        //return _data;
    }
    else {
        return _data;
    }
}



//function GetSelectedValR(a, viewId, tablename, fieldname, enablebulksave, title) {
//    //debugger
//    var x = (a.value || a.options[a.selectedIndex].value);

//    //crossbrowser solution =)
//    var id = $(a).parent().parent().find('td:last').html();
//    var rowIndex = $(a).parent().parent().index();
//    $(a).parent().parent().parent().parent().find("tbody tr.k-selected").removeClass("k-selected")
//    $(a).parent().parent().addClass('k-selected');
//    $(a).parent().prepend("<span id=" + fieldname + " class='k-dirty'></span>");

//    var ModelEnableBulkRecordsSaveOnView = $("#hidEnableBulkRecordsSaveOnView_" + viewId.replace(" ","")).val();
//    if (ModelEnableBulkRecordsSaveOnView == "1") {
//        enablebulksave = 'true';
//    }
//    else{
//        enablebulksave = 'false';
//    }

//    if (enablebulksave == 'true' || enablebulksave == "true") {
//        localStorage.setItem('IsDirty_' + viewId.replace(" ", ""), true);
//        localStorage.setItem('IsDirtyReportEdit', true);

//        var _grid = $("#grid" + viewId).data("kendoTreeList");

//        gid_id = $(a).parent().parent().find('td:last').html();

//        var divItem = $('#' + tablename + viewId.replace(" ", ""));
//        divItem.removeClass('ViewHeaderOn');
//        ViewHeaderPanelOnClick($("#" + tablename + viewId.replace(" ", "")), viewId.replace(" ", ""), tablename);  //Apply view header style and save session details for selected record and view id..J

//        $.ajax({
//            type: 'POST',
//            url: '/Desktop/Push_Changed_Field_Value_Into_Collection',
//            data: { GIDID: gid_id, TableName: tablename, FieldName: fieldname, ViewId: viewId.replace(" ", ""), FieldValue: x, SelectedRowIndex: rowIndex },
//            datatype: 'json',
//            success: function (result) {

//            },
//            error: function (result) {

//            }
//        });
//    }
//    else if (enablebulksave == 'false' || enablebulksave == "false") {
//        if (confirm('Are you sure you want to change the ' + title + '?')) {

//            var _grid = $("#grid" + viewId).data("kendoGrid");

//            gid_id = $(a).parent().parent().find('td:last').html();

//            ViewHeaderPanelOnClick($("#" + tablename + viewId.replace(" ", "")), viewId.replace(" ", ""), tablename);  //Apply view header style and save session details for selected record and view id..J

//            $.ajax({
//                type: 'POST',
//                url: '/Desktop/Push_Changed_Field_Value_Into_Collection',
//                data: { GIDID: gid_id, TableName: tablename, FieldName: fieldname, ViewId: viewId.replace(" ", ""), FieldValue: x, SelectedRowIndex: rowIndex },
//                datatype: 'json',
//                success: function (result) {
//                    SaveReportRows(viewId, "");
//                },
//                error: function (result) {

//                }
//            });
//        }
//        else{
//            refreshviewV2(viewId, viewId.replace(" ", ""), "report", false);
//        }
//    }
//}

//function SaveReportRows(id, filename) {
//    showProgress();
//    $.ajax({
//        type: 'POST',
//        url: '/Desktop/SaveChangedFieldValues',
//        async: true,
//        cache: false,
//        data: { ViewId: id.replace(" ", ""), FileName: filename },
//        //datatype: 'json',
//        success: function (response) {

//            localStorage.setItem('IsDirty_' + id.replace(" ", ""), false);
//            localStorage.setItem('IsDirtyReportEdit', false);
//            alert(response.ResultMessage);

//            var _grid = $("#grid" + id.replace(" ", "")).data("kendoTreeList");
//            var pageData = _grid.dataSource.view();   

//            if (response.ListOfTotalRecords != null) {
//                for (var i = 0; i < response.ListOfTotalRecords.length; i++) {
//                    if (response.ListOfTotalRecords[i].GIDID == pageData[response.ListOfTotalRecords[i].RowIndex].GID_ID) {
//                        _grid.tbody.find(">tr[data-uid='" + pageData[response.ListOfTotalRecords[i].RowIndex].uid + "'] >td> span#" + response.ListOfTotalRecords[i].FieldName).removeClass("k-dirty");
//                    }
//                }
//            }

//            if (response.ListOfUnSavedRecords != null) {
//                for (var i = 0; i < response.ListOfUnSavedRecords.length; i++) {
//                    if (response.ListOfUnSavedRecords[i].GIDID == pageData[response.ListOfUnSavedRecords[i].RowIndex].GID_ID) {
//                        _grid.tbody.find(">tr[data-uid='" + pageData[response.ListOfUnSavedRecords[i].RowIndex].uid + "'] >td> span#" + response.ListOfUnSavedRecords[i].FieldName).addClass("k-dirty");
//                    }
//                }
//            }

//            hideProgress();           
//        },
//        error: function (response) {

//        }
//    });
//}
