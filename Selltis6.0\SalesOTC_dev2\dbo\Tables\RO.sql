﻿CREATE TABLE [dbo].[RO] (
    [GID_ID]              UNIQUEIDENTIFIER CONSTRAINT [DF_RO_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'RO',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]              BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]            NVARCHAR (80)    NULL,
    [DTT_CreationTime]    DATETIME         CONSTRAINT [DF_RO_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [TXT_RoutingName]     NVARCHAR (80)    NULL,
    [MMO_Notes]           NTEXT            NULL,
    [TXT_OrigCreatedBy]   VARCHAR (4)      NULL,
    [DTT_OrigCreatedTime] DATETIME         NULL,
    [CHK_DemoData]        TINYINT          NULL,
    [TXT_ModBy]           VARCHAR (4)      NULL,
    [DTT_ModTime]         DATETIME         CONSTRAINT [DF_RO_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [MMO_ImportData]      NTEXT            NULL,
    [SI__ShareState]      TINYINT          CONSTRAINT [DF_RO_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]    UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]        VARCHAR (50)     NULL,
    [TXT_ExternalID]      NVARCHAR (120)   NULL,
    [TXT_ExternalSource]  VARCHAR (10)     NULL,
    [TXT_ImpJobID]        VARCHAR (20)     NULL,
    [MLS_RecordType]      SMALLINT         NULL,
    [TXT_DesktopId]       VARCHAR (250)    NULL,
    [TXT_ProductType]     NVARCHAR (250)   NULL,
    [MMO_EmailTemplate]   NTEXT            NULL,
    [TXT_TYPEOFREQUEST]   NVARCHAR (500)   NULL,
    [CHK_SendCsv]         TINYINT          NULL,
    [MLS_AutomatorType]   SMALLINT         NULL,
    CONSTRAINT [PK_RO] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_RO_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[RO] NOCHECK CONSTRAINT [LNK_RO_CreatedBy_US];


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_RO_BI__ID]
    ON [dbo].[RO]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RO_CreatedBy_US]
    ON [dbo].[RO]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RO_CreationTime]
    ON [dbo].[RO]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RO_ModDateTime]
    ON [dbo].[RO]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RO_Name]
    ON [dbo].[RO]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RO_RoutingName]
    ON [dbo].[RO]([TXT_RoutingName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RO_TXT_ImportID]
    ON [dbo].[RO]([TXT_ImportID] ASC);


GO
CREATE TRIGGER trROUpdateTN
ON [RO]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in RO table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(120)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'RO'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable
					and tn2.GID_InternalID = inserted.GID_ID)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [RO]
			SET [RO].TXT_ExternalSource = '', [RO].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [RO].GID_ID = in1.GID_ID
				and ISNULL([RO].TXT_ExternalSource, '') <> ''
				and ISNULL([RO].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trROUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trROUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trROUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!