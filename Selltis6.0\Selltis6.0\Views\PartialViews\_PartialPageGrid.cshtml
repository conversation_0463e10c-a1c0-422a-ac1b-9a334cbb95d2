﻿@model Selltis.Core.Grid
@using Kendo.Mvc.UI;
@using Selltis.Core;

<link href="~/Content/themes/Selltis/css/Custom/PartialPageGridStyles.css?v=1.0" rel="stylesheet" />

@(Html.Kendo().Grid<dynamic>()
      .Name("grid" + Model.ViewId.Replace(" ", ""))
      .ToolBar(tools => { tools.Excel(); tools.Pdf(); }) //worked with with mvc 5 and kendo ui 2015
      .Excel(excel => excel
        .FileName(Model.ViewTitle.ToString().Replace(" ", "_") + "X.xlsx")
        .Filterable(true)
        .AllPages(true)
        .ProxyURL(Url.Action("Excel_Export_Save", "Common"))
      )
      .Pdf(pdf => pdf
        .FileName(Model.ViewTitle.ToString().Replace(" ", "_") + "P.pdf")
        .ProxyURL(Url.Action("Export_Save", "Common"))
      )
      .Events(e => e.ExcelExport("onExcelExport"))
      .Columns(columns =>
      {
          if (Model != null)
          {
              //RN #1873 Multiselect functionality added.
              if (Model.ViewMultiSelect == "1" && Model.MergeMultiSelect == "1" || Model.ViewMultiSelect == "1")
              {
                  columns.Template(@<text></text>).ClientTemplate("<input type='checkbox' id='chk#=data.GID_ID#' class='chkbx" + Model.ViewId.Replace(" ", "") + "' />").Width(20);
              }

              //to differentiate tablet/web device -- J
             string _ViewRecordOpen = Model.ViewRecordOpen;
             if (Request.Browser.IsMobileDevice)
             {
                  _ViewRecordOpen = "2";
             }
            // Open anchor column
             if (_ViewRecordOpen != "1")
             {
                 @*columns.Template(@<text></text>).ClientTemplate("<a style=\"cursor: pointer;\" class=\"AnchorOpen\" onclick=\"OpenGridClick(this,'" + Model.File + "','" + Model.ViewId.Replace(" ", "") + "')\"><i class=\"fa fa-edit\" title=\"Edit Record\"></i></a>").Width(40).HtmlAttributes(new { @style = "text-align:center" });*@

                 columns.Template(@<text></text>).ClientTemplate(" <a style=\"cursor: pointer;\" class=\"AnchorOpen\" onclick=\"OpenGridClick(this,'" + Model.File + "','" + Model.ViewId.Replace(" ", "") + "')\"><div><i class=\"fa fa-edit\" title=\"Edit Record\"></i></div></a>").Width(40).HtmlAttributes(new { @style = "text-align:center" });

              }
              if (Model.ShowProfileIcon)
              {
                  string _gidid = "#:data.GID_ID#";
                  columns.Template(@<text></text>).ClientTemplate("<a style=\"cursor: pointer;\" class=\"AnchorOpen\" onclick=\"GoDetailsPage('" + Model.File + "','" + _gidid + "')\"><div><i class=\"fa fa-eye\" title=\"Show 360 View\"></i></div></a>").Width(40).HtmlAttributes(new { @style = "text-align:center" });
              }
              foreach (var Column in Model.Columns)
              {
                  var c = columns.Bound(Column.Name.Replace(",", "").Replace("<%", "").Replace("%>", "").Replace(" ", "")).Title(Column.Title).Width(Column.Width);

                  c.Width(Column.Width);

                  if (Column.Alignment == "C" || Column.Alignment.ToUpper() == "CENTER")
                      c.HtmlAttributes(new { @style = "text-align:center" });
                  else if (Column.Alignment == "R" || Column.Alignment.ToUpper() == "RIGHT")
                      c.HtmlAttributes(new { @style = "text-align:right" });

                  bool _IsLicon = false;

                  if (Column.Name.Contains("CHK_") && Column.Name.Split('_').Length > 0)
                  {
                      if (Column.Name.Split('_')[0].Equals("CHK"))
                      {
                          _IsLicon = true;
                      }
                      if (Column.AllowEdit == true)
                      {
                          _IsLicon = false;
                          string dateValue1 = "#:data." + Column.Name + "#";
                          string _id = "CHK_#:data.GID_ID#";
                          c.ClientTemplate("<input type=\"checkbox\" style = \"height:auto !important;\" id=" + _id + "  name=" + _id + "  " + dateValue1 + "  onchange=\"GetSelectedVal(this," + "'" + Model.ViewId.Replace(" ", "") + "'," + "'" + Model.TableName + "'," + "'" + Column.Name.ToUpper() + "'," + "'" + Model.EnableBulkRecordsSaveOnView.ToLower() + "'," + "'" + Column.Title + "'" + "," + "'" + Model.ViewType + "'" + ")\">");
                      }
                  }

                  if (Column.Name.Contains("CUR_") || Column.Name.Contains("SI__"))
                  {
                      c.ClientTemplate("#=FormatNegativeValue(data." + Column.Name + ",\'" + Column.Name.ToUpper() + "\')#");
                  }

                  if (Column.IsIcon == true || _IsLicon)
                  {
                      c.ClientTemplate("#=GridTemplateData(data." + Column.Name + ",\'" + Column.Name.ToUpper() + "\')#");
                  }

                  //To set bustton style for MLS columns..J
                  if (Column.IsButton == true)
                  {
                      c.Encoded(false);
                  }

                  //To set bustton style for MLS columns..S1
                  if (Column.IsIconButton == true)
                  {
                      c.Encoded(false);
                  }

                  if (Column.IsLink == true)
                  {
                      if (Column.Name.ToUpper().Contains("EML_"))
                      {
                          string colValue = "#:data." + Column.Name + "#";
                          c.ClientTemplate("<a href='mailto:" + colValue + "?Subject='>" + colValue + "</a>");
                      }
                      else if (Column.Name.ToUpper().Contains("LNK_"))
                      {
                          c.ClientTemplate("#=getLinks(data." + Column.Name + ",\'" + Column.Name.ToUpper() + "\')#");
                      }
                  }
                  if (Column.Name.ToUpper().Contains("URL_"))
                  {
                      string colValue = "#:data." + Column.Name + "#";
                        c.ClientTemplate("<a href='javascript:void(0);' onclick=\"window.open('" + colValue + "', '');\"><i class=\"glyphicon glyphicon-download-alt\"></i></a>");
                     
                  }
                  if (Column.Name.Contains("ADV_"))
                  {
                      c.ClientTemplate("#= data." + Column.Name + "#");
                  }
                  //if (Column.Name.ToUpper().Contains("TXT_"))
                  //{
                  //    string coltxtValue = "#:data." + Column.Name + "#";
                  //    c.ClientTemplate("<input class='k-textbox'  value= " + coltxtValue + " >");
                  //}
                  if (Column.Name.ToUpper().Contains("MLS_") && Column.AllowEdit == true)
                  {
                      string coltxtValue = "#:data." + Column.Name + "#";

                      // c.ClientTemplate(Util.GetMLSCOntrol_As_String(Column.Name, coltxtValue, Column.Width, Model.ViewId.Replace(" ", "")));
                      c.ClientTemplate(string.Format("#=GetMLSControl(\'" + Model.TableName + "\',\'" + Column.Name.ToUpper() + "\',data." + Column.Name + ",\'" + Column.Width + "\',\'" + Model.ViewId + "\',\'" + Model.Key + "\'" + ",\'" + Column.Title + "\')#"));
                  }

                  if (Column.Name.ToUpper().Contains("DTE_") && Column.AllowEdit == true)
                  {
                      string dateValue1 = "#:data." + Column.Name + "#";
                      string _id = "DT_#:data.GID_ID#";
                      c.ClientTemplate("<input style = \"height:auto !important;\" id=" + _id + "  onmouseover=\"DTClicked(this);\"  name=" + _id + "  value=" + dateValue1 + "  onchange=\"GetSelectedVal(this," + "'" + Model.ViewId.Replace(" ", "") + "'," + "'" + Model.TableName + "'," + "'" + Column.Name.ToUpper() + "'," + "'" + Model.EnableBulkRecordsSaveOnView.ToLower() + "'," + "'" + Column.Title + "'" + "," + "'" + Model.ViewType + "'" + ")\">");
                  }

                  if (Column.IsVisible == false)
                  {
                      c.Hidden(true);
                  }

                  if (Column.IsSortable == false)
                  {
                      c.Sortable(false).HeaderHtmlAttributes(new { @title = "This column is not sortable" });
                  }
                  else
                  {
                      //set sort icon for default sort field
                      if (Column.Name == Model.DefaultSortField)
                      {
                          if (Model.DefaultSortDirection == "DESC")
                          {
                              c.Sortable(false).HeaderTemplate("<span title = 'This column is sortable' name = 'SortableField' class='k-link' onclick=\"onGridColumnSortClick('" + Column.Name + "', '" + Model.ViewId.Replace(" ", "") + "', '" + Model.TableName + "')\" > " + Column.Title + "   <span id='" + Model.Key + "_" + Model.ViewId.Replace(" ", "") + "_" + Column.Name.Replace(" ", "") + "_SortableFieldIcon' class='fa fa-long-arrow-down' >");
                          }
                          else
                          {
                              c.Sortable(false).HeaderTemplate("<span title = 'This column is sortable' name = 'SortableField' class='k-link' onclick=\"onGridColumnSortClick('" + Column.Name + "', '" + Model.ViewId.Replace(" ", "") + "', '" + Model.TableName + "')\" > " + Column.Title + "   <span id='" + Model.Key + "_" + Model.ViewId.Replace(" ", "") + "_" + Column.Name.Replace(" ", "") + "_SortableFieldIcon' class='fa fa-long-arrow-up' >");
                          }
                      }
                      else
                      {
                          c.Sortable(false).HeaderTemplate("<span title = 'This column is sortable' name = 'SortableField' class='k-link' onclick=\"onGridColumnSortClick('" + Column.Name + "', '" + Model.ViewId.Replace(" ", "") + "', '" + Model.TableName + "')\" > " + Column.Title + "   <span id='" + Model.Key + "_" + Model.ViewId.Replace(" ", "") + "_" + Column.Name.Replace(" ", "") + "_SortableFieldIcon' class='fa fa-long-arrow-up' style='display: none;' >");
                      }
                  }

                  //if(Model.File == "CO" && Column.Name == "TXT_COMPANYNAME")
                  //{
                  //    string dateValue2 = "#:data." + Column.Name + "#";
                  //    string _gidid = "#:data.GID_ID#";
                  //    c.ClientTemplate("<a style=\"cursor: pointer;\" class=\"AnchorOpen\" onclick=\"GoDetailsPage('" + Model.File + "','" + _gidid + "')\">"+ dateValue2 + "</a>");
                  //}
                  //else if (Model.File == "CN" && (Column.Name == "SYS_NAME" || Column.Name == "TXT_NAMELAST" || Column.Name == "TXT_NAMEFIRST"))
                  //{
                  //    string dateValue2 = "#:data." + Column.Name + "#";
                  //    string _gidid = "#:data.GID_ID#";
                  //    c.ClientTemplate("<a style=\"cursor: pointer;\" class=\"AnchorOpen\" onclick=\"GoDetailsPage('" + Model.File + "','" + _gidid + "')\">" + dateValue2 + "</a>");
                  //}
                  //else if (Model.File == "OP" && Column.Name == "TXT_OPPORTUNITYNAME")
                  //{
                  //    string dateValue2 = "#:data." + Column.Name + "#";
                  //    string _gidid = "#:data.GID_ID#";
                  //    c.ClientTemplate("<a style=\"cursor: pointer;\" class=\"AnchorOpen\" onclick=\"GoDetailsPage('" + Model.File + "','" + _gidid + "')\">" + dateValue2 + "</a>");
                  //}
                  //else if (Model.File == "WL" && Column.Name == "TXT_WLNO")
                  //{
                  //    string dateValue2 = "#:data." + Column.Name + "#";
                  //    string _gidid = "#:data.GID_ID#";
                  //    c.ClientTemplate("<a style=\"cursor: pointer;\" class=\"AnchorOpen\" onclick=\"GoDetailsPage('" + Model.File + "','" + _gidid + "')\">" + dateValue2 + "</a>");
                  //}

              }
          }
      })
      .Selectable(s => { s.Mode(GridSelectionMode.Single); })
      .Navigatable()
      .HtmlAttributes(new { @style = "width:99.8%;height:100%!important" })
      .Scrollable()
      .Sortable(s => { s.AllowUnsort(false); })
      .Reorderable(reorderable => reorderable.Columns(true))
      .Resizable(resize => resize.Columns(true))
      .DataSource(dataSource => dataSource
        .Ajax()
        .PageSize(Model.PageSize)
        .Read(read => read.Action("ReadData_V2", "LoadView", new { ViewKey = Model.ViewKey, firstload = Model.FirstLoad, Key = Model.Key }))
      )
)

<input type="hidden" id="<EMAIL>(" ", "")" value="@Model.DependencyViewIds" />
<input type="hidden" id="<EMAIL>(" ", "")" value="@Model.DependencyViewIdsOnFocus" />
<input type="hidden" id="<EMAIL>(" ", "")" value="@Model.ParentViewId" />
<input type="hidden" id="<EMAIL>(" ", "")" value="" />
<input type="hidden" id="<EMAIL>(" ", "")" value="list" />

<!--Version for code minification starts here..J-->

@Html.Hidden("hidPartialGridViewId", Model.ViewId)
@Html.Hidden("hidPartialGridViewKey", Model.ViewId.Replace(" ", ""))
@Html.Hidden("hidKey_" + Model.ViewId.Replace(" ", ""), Model.Key)
@Html.Hidden("hidSiteId_" + Model.ViewId.Replace(" ", ""), Model.SiteId)
@Html.Hidden("hidDisplayNegativeValuesInRed_" + Model.ViewId.Replace(" ", ""), Model.DisplayNegativeValuesInRed)
@Html.Hidden("hidOneLinePerRecord_" + Model.ViewId.Replace(" ", ""), Model.OneLinePerRecord)
@Html.Hidden("hidPageSize_" + Model.ViewId.Replace(" ", ""), Model.PageSize)
@Html.Hidden("hidIsInvalid_" + Model.ViewId.Replace(" ", ""), Model.IsInvalid)
@Html.Hidden("hidIsTabView_" + Model.ViewId.Replace(" ", ""), Model.IsTabView)
@Html.Hidden("hidIsActive_" + Model.ViewId.Replace(" ", ""), Model.IsActive)
@Html.Hidden("hidIsMasterView_" + Model.ViewId.Replace(" ", ""), Model.IsMasterView)
@Html.Hidden("hidIsMaster_And_Client_View_" + Model.ViewId.Replace(" ", ""), Model.IsMaster_And_Client_View)
@Html.Hidden("hidDependencyViewIds_" + Model.ViewId.Replace(" ", ""), Model.DependencyViewIds)
@Html.Hidden("hidDependencyViewIdsOnFocus_" + Model.ViewId.Replace(" ", ""), Model.DependencyViewIdsOnFocus)
@Html.Hidden("hidTableName_" + Model.ViewId.Replace(" ", ""), Model.TableName)
@Html.Hidden("hidViewRecordOpen_" + Model.ViewId.Replace(" ", ""), Model.ViewRecordOpen)
@Html.Hidden("hidIndex_" + Model.ViewId.Replace(" ", ""), Model.Index)
@Html.Hidden("hidSelectSpecificRecordId_" + Model.ViewId.Replace(" ", ""), Model.SelectSpecificRecordId)
@Html.Hidden("hidIsParent_" + Model.ViewId.Replace(" ", ""), Model.IsParent)
@Html.Hidden("hidAutoLoad_" + Model.ViewId.Replace(" ", ""), Model.AutoLoad)
@Html.Hidden("hidEnableBulkRecordsSaveOnView_" + Model.ViewId.Replace(" ", ""), Model.EnableBulkRecordsSaveOnView)
@Html.Hidden("hidFile_" + Model.ViewId.Replace(" ", ""), Model.File)
@Html.Hidden("hidOrderIndex_" + Model.ViewId.Replace(" ", ""), Model.OrderIndex)
@Html.Hidden("hidIsSelectSpecificRecord_" + Model.ViewId.Replace(" ", ""), Model.IsSelectSpecificRecord)
@Html.Hidden("hidUseHeadings_" + Model.ViewId.Replace(" ", ""), Model.UseHeadings)
@Html.Hidden("hidAutoCount_" + Model.ViewId.Replace(" ", ""), Model.AutoCount)
@Html.Hidden("hidViewMultiSelect_" + Model.ViewId.Replace(" ", ""), Model.ViewMultiSelect)
@Html.Hidden("hidMergeMultiSelect_" + Model.ViewId.Replace(" ", ""), Model.MergeMultiSelect)

@Html.Hidden("hidDesktopId_" + Model.ViewId.Replace(" ", ""), Selltis.Core.Util.GetSessionValue("DesktopId"))

<!--Version for code minification ends here..J-->


<div id="@Model.ViewId.Replace("V","divCorrupt").Replace(" ","")" style="display: none; background-color: #FFFFB0;">
    <label style="margin-left:3px;">The view cannot be displayed due to an invalid field in the view, sort or filter. Please select View>Properties to try to repair it.</label>
</div>

<script src="~/Content/themes/Selltis/scripts/Custom/PartialPageGridScripts.js?v1=4.2" type="text/javascript"></script>

<script>
    $(document).ready(function () {
        DocumentReady('@Model.ViewId', '@Model.ViewId.Replace(" ", "")');
    });
   
</script>