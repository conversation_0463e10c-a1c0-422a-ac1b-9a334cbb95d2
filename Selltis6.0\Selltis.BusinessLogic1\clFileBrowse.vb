﻿Imports Microsoft.VisualBasic
Imports System.Web


Public Class clFileBrowse

    'owner wt
    'returns a datatable...
    '...with the following fields: sys_name, gid_id, along with any additional fields requested by the caller
    '...with the file's default sort unless defined by caller
    '...with a filter of "all records" unless defined by the caller

    'reference project session information
    Private goP As clProject
    Private goMeta As clMetaData
    Private goTR As clTransform
    Private goData As clData
    Private goErr As clError

    Public TopRecIDForPrevious As String = ""
    Public TopRecIDForNext As String = ""
    Public PreviousEnabled As Boolean = False
    Public NextEnabled As Boolean = False
    Public RecordCount As Long = 0

    Public Sub New()
        Initialize()
    End Sub

    Private Sub Initialize()
        Dim sProc As String = "clView:Initialize"
        ' Try
        goP = HttpContext.Current.Session("goP")
            goTR = HttpContext.Current.Session("goTr")
            goMeta = HttpContext.Current.Session("goMeta")
            goData = HttpContext.Current.Session("goData")
            goErr = HttpContext.Current.Session("goErr")
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Public Function QuickSelect(ByVal par_sFile As String, _
                                Optional ByVal par_sQuickSelectValue As String = "", _
                                Optional ByVal par_iTop As Integer = -1, _
                                Optional ByVal par_sFilter As String = "", _
                                Optional ByVal par_sSort As String = "", _
                                Optional ByVal par_sTopRecordGID As String = "", _
                                Optional ByVal par_sAdditionalFields As String = "" _
                                ) As DataTable
        Try


            If par_sQuickSelectValue <> "" Then

                'filter
                Dim sFilter As String = "" 'all records
                If par_sFilter <> "" Then sFilter = par_sFilter

                Dim sOper As String = ""
                Dim sSortOn As String = ""
                Dim bDescending As Boolean = False

                If par_sSort = "" Then par_sSort = goData.GetDefaultSort(par_sFile)
                sSortOn = GetFirstSortField(par_sSort, sOper)
                If sOper = "<=" Then bDescending = True

                If sFilter = "" Then
                    sFilter = sSortOn & sOper & goTR.ConvertStringForQS(goTR.PrepareForSQL(par_sQuickSelectValue), sSortOn.Replace(">=", "").Replace("<=", ""), par_sFile, True, bDescending)
                Else
                    sFilter = "(" & sFilter & ") AND (" & sSortOn & sOper & goTR.ConvertStringForQS(goTR.PrepareForSQL(par_sQuickSelectValue), sSortOn.Replace(">=", "").Replace("<=", ""), par_sFile, True, bDescending) & ")"
                End If

                Dim oTable As DataTable = GetData(par_sFile, par_iTop + 1, sFilter, par_sSort, par_sTopRecordGID, par_sAdditionalFields)
                FinalizeData(oTable, par_iTop, "QUICKSELECT")
                Return oTable

            Else

                Dim oTable As DataTable = GetFirst(par_sFile, par_iTop, par_sFilter, par_sSort, par_sTopRecordGID, par_sAdditionalFields)
                Return oTable

            End If


        Catch ex As Exception

            Return New DataTable

        End Try

    End Function

    Public Function GetFirst(ByVal par_sFile As String, _
                        Optional ByVal par_iTop As Integer = -1, _
                        Optional ByVal par_sFilter As String = "", _
                        Optional ByVal par_sSort As String = "", _
                        Optional ByVal par_sTopRecordGID As String = "", _
                        Optional ByVal par_sAdditionalFields As String = "" _
                        ) As DataTable

        Try


            If goData.IsFileValid(par_sFile) Then

                Dim oTable As DataTable = GetData(par_sFile, par_iTop + 1, par_sFilter, par_sSort, par_sTopRecordGID, par_sAdditionalFields)
                FinalizeData(oTable, par_iTop, "FIRST")
                Return oTable

            Else

                Return New DataTable

            End If

        Catch ex As Exception

            Return New DataTable

        End Try

    End Function

    Public Function GetNext(ByVal par_sFile As String, _
                            Optional ByVal par_iTop As Integer = -1, _
                            Optional ByVal par_sFilter As String = "", _
                            Optional ByVal par_sSort As String = "", _
                            Optional ByVal par_sTopRecordGID As String = "", _
                            Optional ByVal par_sAdditionalFields As String = "" _
                            ) As DataTable

        Try


            If goData.IsFileValid(par_sFile) Then

                Dim sTopRec As String = TopRecIDForNext
                If par_sTopRecordGID <> "" Then sTopRec = par_sTopRecordGID

                Dim oTable As DataTable = GetData(par_sFile, par_iTop + 1, par_sFilter, par_sSort, sTopRec, par_sAdditionalFields)
                FinalizeData(oTable, par_iTop, "NEXT")

                Return oTable

            Else

                Return New DataTable

            End If

        Catch ex As Exception

            Return New DataTable

        End Try

    End Function

    Public Function GetPrevious(ByVal par_sFile As String, _
                            Optional ByVal par_iTop As Integer = -1, _
                            Optional ByVal par_sFilter As String = "", _
                            Optional ByVal par_sSort As String = "", _
                            Optional ByVal par_sTopRecordGID As String = "", _
                            Optional ByVal par_sAdditionalFields As String = "" _
                            ) As DataTable

        Try

            If goData.IsFileValid(par_sFile) Then

                If par_sSort = "" Then par_sSort = goData.GetDefaultSort(par_sFile)

                Dim oUtil As New clUtil
                Dim sSort As String = oUtil.ReverseSort(par_sFile, par_sSort)

                Dim sTopRec As String = TopRecIDForPrevious
                If par_sTopRecordGID <> "" Then sTopRec = par_sTopRecordGID

                Dim oTable As DataTable = GetData(par_sFile, par_iTop + 2, par_sFilter, sSort, sTopRec, par_sAdditionalFields)
                FinalizeData(oTable, par_iTop, "PREVIOUS", par_sFile, par_sFilter, par_sSort, par_sAdditionalFields)

                Return oTable

            Else

                Return New DataTable

            End If

        Catch ex As Exception

            Return New DataTable

        End Try

    End Function

    Public Function GetLast(ByVal par_sFile As String, _
                        Optional ByVal par_iTop As Integer = -1, _
                        Optional ByVal par_sFilter As String = "", _
                        Optional ByVal par_sSort As String = "", _
                        Optional ByVal par_sTopRecordGID As String = "", _
                        Optional ByVal par_sAdditionalFields As String = "" _
                        ) As DataTable

        Try


            If goData.IsFileValid(par_sFile) Then

                If par_sSort = "" Then par_sSort = goData.GetDefaultSort(par_sFile)

                Dim oUtil As New clUtil
                Dim sSort As String = oUtil.ReverseSort(par_sFile, par_sSort)

                Dim oTable As DataTable = GetData(par_sFile, par_iTop + 1, par_sFilter, sSort, , par_sAdditionalFields)
                FinalizeData(oTable, par_iTop, "LAST")

                Return oTable

            Else

                Return New DataTable

            End If

        Catch ex As Exception

            Return New DataTable

        End Try

    End Function

    Private Function GetData(ByVal par_sFile As String, _
                            Optional ByVal par_iTop As Integer = -1, _
                            Optional ByVal par_sFilter As String = "", _
                            Optional ByVal par_sSort As String = "", _
                            Optional ByVal par_sTopRecordGID As String = "", _
                            Optional ByVal par_sAdditionalFields As String = "" _
                            ) As DataTable
        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        Try

            'field list
            Dim sFieldList As String = "SYS_NAME,GID_ID" ',BI__ID"
            If par_sAdditionalFields <> "" Then sFieldList = sFieldList & "," & par_sAdditionalFields

            'filter
            Dim sFilter As String = "" 'all records
            If par_sFilter <> "" Then sFilter = par_sFilter

            'sort
            Dim sSort As String = goData.GetDefaultSort(par_sFile) 'default sort for given file
            If par_sSort <> "" Then sSort = par_sSort

            'top rec
            Dim sTopRec As String = ""
            If par_sTopRecordGID <> "" Then
                If goTR.IsTID(par_sTopRecordGID) Then
                    Dim oUtil As New clUtil
                    sTopRec = oUtil.GetTopRec(par_sFile, sSort, par_sTopRecordGID)
                End If
            End If
            Dim oRS As New clRowSet(par_sFile, clC.SELL_READONLY, sFilter, sSort, sFieldList, par_iTop, , , , , sTopRec)
            oRS.ToTable()
            Dim oTable As DataTable = oRS.dtTransTable

            RecordCount = goData.GetCount(par_sFile, sFieldList, sFilter)

            Return oTable

        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return New DataTable
        End Try

    End Function

    Private Sub FinalizeData(ByRef par_oTable As DataTable, _
                             ByVal par_iTop As Integer, _
                             ByVal sBrowseInstruct As String, _
                             Optional ByVal par_sFile As String = "", _
                             Optional ByVal par_sFilter As String = "", _
                             Optional ByVal par_sSort As String = "", _
                             Optional ByVal par_sAdditionalFields As String = "" _
                             )
        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        ' Try
        Select Case par_oTable.Rows.Count
                Case Is <= par_iTop
                    Select Case sBrowseInstruct
                        Case "FIRST"
                            PreviousEnabled = False
                            NextEnabled = False
                            TopRecIDForNext = ""
                            TopRecIDForPrevious = ""
                        Case "PREVIOUS"
                            par_oTable = GetFirst(par_sFile, par_iTop, par_sFilter, par_sSort, , par_sAdditionalFields)
                        Case "NEXT"
                            PreviousEnabled = (RecordCount > par_iTop)
                            NextEnabled = False
                            TopRecIDForNext = ""
                            TopRecIDForPrevious = par_oTable.Rows(0)("GID_ID").ToString
                        Case "LAST"
                            PreviousEnabled = (RecordCount > par_iTop)
                            NextEnabled = False
                            par_oTable = ReverseData(par_oTable)
                            TopRecIDForNext = ""
                            TopRecIDForPrevious = ""
                        Case "QUICKSELECT"
                            PreviousEnabled = True
                            NextEnabled = False
                            TopRecIDForNext = ""
                            TopRecIDForPrevious = par_oTable.Rows(0)("GID_ID").ToString
                        Case Else
                            PreviousEnabled = False
                            NextEnabled = False
                            TopRecIDForNext = ""
                            TopRecIDForPrevious = ""
                    End Select
                Case Is > par_iTop
                    Select Case sBrowseInstruct
                        Case "FIRST"
                            PreviousEnabled = False
                            NextEnabled = True
                            TopRecIDForNext = par_oTable.Rows(par_oTable.Rows.Count - 1)("GID_ID").ToString
                            TopRecIDForPrevious = ""
                            par_oTable.Rows.RemoveAt(par_oTable.Rows.Count - 1)
                        Case "PREVIOUS"
                            If par_oTable.Rows.Count = par_iTop + 2 Then
                                PreviousEnabled = True
                                NextEnabled = True
                                TopRecIDForNext = par_oTable.Rows(0)("GID_ID").ToString
                                TopRecIDForPrevious = par_oTable.Rows(par_oTable.Rows.Count - 2)("GID_ID").ToString
                                par_oTable.Rows.RemoveAt(par_oTable.Rows.Count - 1)
                                par_oTable.Rows.RemoveAt(0)
                                par_oTable = ReverseData(par_oTable)
                            Else
                                'PreviousEnabled = False
                                par_oTable = GetFirst(par_sFile, par_iTop, par_sFilter, par_sSort, , par_sAdditionalFields)
                            End If
                        Case "NEXT"
                            PreviousEnabled = True
                            NextEnabled = True
                            TopRecIDForNext = par_oTable.Rows(par_oTable.Rows.Count - 1)("GID_ID").ToString
                            TopRecIDForPrevious = par_oTable.Rows(0)("GID_ID").ToString
                            par_oTable.Rows.RemoveAt(par_oTable.Rows.Count - 1)
                        Case "LAST"
                            PreviousEnabled = True
                            NextEnabled = False
                            TopRecIDForNext = ""
                            TopRecIDForPrevious = par_oTable.Rows(par_oTable.Rows.Count - 2)("GID_ID").ToString
                            par_oTable.Rows.RemoveAt(par_oTable.Rows.Count - 1)
                            par_oTable = ReverseData(par_oTable)
                        Case "QUICKSELECT"
                            PreviousEnabled = True
                            NextEnabled = True
                            TopRecIDForNext = par_oTable.Rows(par_oTable.Rows.Count - 1)("GID_ID").ToString
                            TopRecIDForPrevious = par_oTable.Rows(0)("GID_ID").ToString
                            par_oTable.Rows.RemoveAt(par_oTable.Rows.Count - 1)
                        Case Else
                            PreviousEnabled = False
                            NextEnabled = False
                            TopRecIDForNext = ""
                            TopRecIDForPrevious = ""
                    End Select
            End Select
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Private Function ReverseData(ByVal par_oTable As DataTable) As DataTable
        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        Try
            Dim oNewDT As DataTable = par_oTable.Clone
            Dim i As Integer
            For i = par_oTable.Rows.Count - 1 To 0 Step -1
                Dim oRow As DataRow
                oRow = par_oTable.Rows.Item(i)
                oNewDT.ImportRow(oRow)
            Next
            Return oNewDT
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return par_oTable
        End Try
    End Function

    Public Function GetFirstSortField(ByVal sOrder As String, ByRef sOper As String) As String
        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        Try

            Dim aOrder As Array
            Dim iPos As Integer
            aOrder = Split(sOrder, ", ")
            sOrder = aOrder(0)
            sOper = ">="
            iPos = InStr(UCase(sOrder), " A")
            If iPos > 0 Then
                sOrder = goTR.FromTo(sOrder, 1, iPos - 1)
                sOper = ">="
            End If
            iPos = InStr(UCase(sOrder), " D")
            If iPos > 0 Then
                sOrder = goTR.FromTo(sOrder, 1, iPos - 1)
                sOper = "<="
            End If
            Return sOrder
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return ""
        End Try
    End Function

End Class
