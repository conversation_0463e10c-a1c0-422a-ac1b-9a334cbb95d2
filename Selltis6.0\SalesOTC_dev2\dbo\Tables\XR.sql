﻿CREATE TABLE [dbo].[XR] (
    [GID_ID]                        UNIQUEIDENTIFIER CONSTRAINT [DF_XR_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'XR',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                        BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                      NVARCHAR (80)    NULL,
    [DTT_CreationTime]              DATETIME         CONSTRAINT [DF_XR_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]                  TINYINT          NULL,
    [TXT_ModBy]                     VARCHAR (4)      NULL,
    [DTT_ModTime]                   DATETIME         CONSTRAINT [DF_XR_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_CustomPersonalOptionsName] NVARCHAR (50)    NULL,
    [MMO_ImportData]                NTEXT            NULL,
    [SI__ShareState]                TINYINT          CONSTRAINT [DF_XR_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]              UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]                  VARCHAR (50)     NULL,
    CONSTRAINT [PK_XR] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_XR_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[XR] NOCHECK CONSTRAINT [LNK_XR_CreatedBy_US];


GO
CREATE NONCLUSTERED INDEX [IX_XR_CreatedBy_US]
    ON [dbo].[XR]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XR_Name]
    ON [dbo].[XR]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XR_CreationTime]
    ON [dbo].[XR]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XR_TXT_ImportID]
    ON [dbo].[XR]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XR_CustomPersonalOptionsName]
    ON [dbo].[XR]([TXT_CustomPersonalOptionsName] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_XR_BI__ID]
    ON [dbo].[XR]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XR_ModDateTime]
    ON [dbo].[XR]([DTT_ModTime] ASC);

