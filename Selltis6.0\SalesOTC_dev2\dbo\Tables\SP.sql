﻿CREATE TABLE [dbo].[SP] (
    [GID_ID]                UNIQUEIDENTIFIER CONSTRAINT [DF_SP_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'SP',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]              NVARCHAR (80)    NULL,
    [DTT_CreationTime]      DATETIME         CONSTRAINT [DF_SP_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]          TINYINT          NULL,
    [TXT_ModBy]             VARCHAR (4)      NULL,
    [DTT_ModTime]           DATETIME         CONSTRAINT [DF_SP_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_SP360DownloadName] NVARCHAR (50)    NULL,
    [MMO_ImportData]        NTEXT            NULL,
    [SI__ShareState]        TINYINT          CONSTRAINT [DF_SP_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]      UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]          VARCHAR (50)     NULL,
    [TXT_ExternalID]        NVARCHAR (80)    NULL,
    [TXT_ExternalSource]    VARCHAR (10)     NULL,
    [TXT_ImpJobID]          VARCHAR (20)     NULL,
    CONSTRAINT [PK_SP] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_SP_SP360DownloadName]
    ON [dbo].[SP]([TXT_SP360DownloadName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SP_CreatedBy_US]
    ON [dbo].[SP]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SP_ModDateTime]
    ON [dbo].[SP]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SP_Name]
    ON [dbo].[SP]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SP_CreationTime]
    ON [dbo].[SP]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_SP_BI__ID]
    ON [dbo].[SP]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SP_TXT_ImportID]
    ON [dbo].[SP]([TXT_ImportID] ASC);

