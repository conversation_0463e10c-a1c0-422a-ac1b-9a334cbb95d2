﻿using System;

//OWNER: MI

namespace Selltis.BusinessLogic
{
	public class clImportTasks
	{
		//Inherits clVar

		//MI 11/2/06 Created.

		//Enable global objects only if needed
		//Private goP As clProject
		//Private goMeta As clMetaData
		//Private goTR As clTransform
		//Private goData As clData
		//Private goErr As clError
		//Private goLog As clLog
		//Private goUI As clUI
		//Private goHist As clHistory

		//Public par_sMode As String     'Page opening mode
		public string sReturnURL = "";
		public bool bBackFromSubdialog;
		public string sMessageBoxPurpose;

		public string sTitle = "Import Tasks";
		//==> Edit
		public string fenenexecution = "ImportTasks.aspx";

		public string sFileName = ""; //Main file



		public void Initialize()
		{
			string sProc = "clDiaFiltCnd::Initialize";
			//Try
			//   'Enable only if needed
			//   goP = HttpContext.Current.Session("goP")
			//   goTR = HttpContext.Current.Session("goTr")
			//   goMeta = HttpContext.Current.Session("goMeta")
			//   goData = HttpContext.Current.Session("goData")
			//   goErr = HttpContext.Current.Session("goErr")
			//   goLog = HttpContext.Current.Session("goLog")
			//   goUI = HttpContext.Current.Session("goUI")
			//   goHist = HttpContext.Current.Session("goHist")
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public clImportTasks()
		{

		}

		~clImportTasks()
		{
// INSTANT C# NOTE: The base class Finalize method is automatically called from the destructor:
			//base.Finalize();
		}
	}

}
