﻿Imports Microsoft.VisualBasic
Imports System
Imports System.Web
Imports System.Data
Imports System.Net
Imports System.IO

Public Class clScriptsRowSetCustom

    Dim goP As clProject
    Dim goMeta As clMetaData
    Dim goTR As clTransform
    Dim goData As clData
    Dim goErr As clError
    Dim goLog As clLog
    Dim goDef As clDefaults
    Dim goScr As clScrMngRowSet
    'Dim goUI As clUI
    Dim goPerm As clPerm
    Dim goEmail As clEmail
    Public sError As String

    Public Sub Initialize()

        goP = HttpContext.Current.Session("goP")
        goTR = HttpContext.Current.Session("goTr")
        goMeta = HttpContext.Current.Session("goMeta")
        goData = HttpContext.Current.Session("goData")
        goErr = HttpContext.Current.Session("goErr")
        goLog = HttpContext.Current.Session("goLog")
        goDef = HttpContext.Current.Session("goDef")
        goScr = HttpContext.Current.Session("goScr")
        ' goUI = HttpContext.Current.Session("goUI")
        goPerm = HttpContext.Current.Session("goPerm")

    End Sub

    Function AC_RecordOnSave_Post(Optional ByRef par_doCallingObject As Object = vbNull, Optional ByVal par_doArray As clArray = Nothing, Optional ByVal par_s1 As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "", Optional ByRef par_oReturn As Object = vbNull, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
        'par_doCallingObject: Form object calling this script. Do not delete in script!
        'par_doArray: Unused.
        'par_s1: Unused.
        'par_s2 to par_s5: Unused.
        'par_oReturn: Object passed to script ByRef.  Used to return values to calling process
        'par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
        'par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

        'For notes on how to create a custom script, see clScrMng. ***

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Dim oRS As clRowSet = CType(par_doCallingObject, clRowSet)
        Dim oSend As New clEmail
        Dim sBody As String

        If oRS.iRSType = 2 Then

            Dim iType As Integer = goTR.StringToNum(oRS.GetFieldVal("MLS_TYPE", clC.SELL_SYSTEM))
            Dim iPurpose As Integer = goTR.StringToNum(oRS.GetFieldVal("MLS_PURPOSE", clC.SELL_SYSTEM))

            If iType = 22 And iPurpose = 56 Then 'RUN ONLY IF WEB SUBMISSION & EVENT REGISTRATION

                Dim sGID As String = oRS.GetFieldVal("GID_ID")
                Dim oArray As New clArray
                oArray = oRS.GetLinkVal("LNK_INVOLVES_US", oArray)
                Dim sUS As String = ""
                Dim i As Integer
                For i = 1 To oArray.GetDimension
                    sUS = oArray.GetItem(i)
                    ' If sUS <> "" Then goUI.AddAlert("Event Registration", clC.SELL_ALT_OPENRECORD, sGID, sUS)
                Next i
            End If

            'CS 3/12/12: Per BKG, when a 'Request...' purpose AC comes in (via Webforms), email a 
            'notification to Brian.
            Select Case iPurpose

                Case 21, 22, 23, 24   '21=Request Lit 22=Request Web Demo, 23=Request Call, 24=Request Quote
                    'Create message body
                    sBody = "A " & oRS.GetFieldVal("MLS_PURPOSE", 1) & " Activity has been received." & vbCrLf & vbCrLf
                    sBody = sBody & oRS.GetFieldVal("MMO_NOTES")
                    'goLog.Log(sProc, sBody, 0, True, True)
                    ' If oSend.SendSMTPEmail("Web Request Received", sBody, "<EMAIL>", "", "", "", "Selltis Support", "<EMAIL>", , , , , , False) = False Then

                    ' End If
            End Select


        End If

        Return True

    End Function

End Class
