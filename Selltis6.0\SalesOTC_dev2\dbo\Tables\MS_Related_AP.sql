﻿CREATE TABLE [dbo].[MS_Related_AP] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Message_Related_App_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_MS] UNIQUEIDENTIFIER NOT NULL,
    [GID_AP] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MS_Related_AP] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_AP_Connected_MS] FOREIGN KEY ([GID_MS]) REFERENCES [dbo].[MS] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_MS_Related_AP] FOREIGN KEY ([GID_AP]) REFERENCES [dbo].[AP] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MS_Related_AP] NOCHECK CONSTRAINT [LNK_AP_Connected_MS];


GO
ALTER TABLE [dbo].[MS_Related_AP] NOCHECK CONSTRAINT [LNK_MS_Related_AP];


GO
CREATE CLUSTERED INDEX [IX_AP_Connected_MS]
    ON [dbo].[MS_Related_AP]([GID_MS] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MS_Related_AP]
    ON [dbo].[MS_Related_AP]([GID_AP] ASC);

