﻿CREATE TABLE [dbo].[<PERSON>] (
    [GID_ID]           UNIQUEIDENTIFIER CONSTRAINT [DF_MD_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'MD',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]           BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_Name]         NVARCHAR (80)    NULL,
    [DTT_CreationTime] DATETIME         CONSTRAINT [DF_MD_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [GID_Section]      UNIQUEIDENTIFIER NULL,
    [TXT_Page]         VARCHAR (40)     NOT NULL,
    [TXT_Language]     CHAR (2)         NULL,
    [TXT_Property]     VARCHAR (50)     NOT NULL,
    [TXT_Value]        NVARCHAR (3800)  NULL,
    [TXT_ModBy]        VARCHAR (4)      NULL,
    [DTT_ModTime]      DATETIME         CONSTRAINT [DF_MD_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [SI__ShareState]   TINYINT          CONSTRAINT [DF_MD_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US] UNIQUEIDENTIFIER NULL,
    [TXT_Product]      VARCHAR (2)      NULL,
    CONSTRAINT [PK_MD] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_MD_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MD] NOCHECK CONSTRAINT [LNK_MD_CreatedBy_US];


GO
CREATE UNIQUE CLUSTERED INDEX [IX_MD_SectionPageProp]
    ON [dbo].[MD]([GID_Section] ASC, [TXT_Page] ASC, [TXT_Property] ASC, [TXT_Product] ASC, [TXT_Language] ASC) WITH (FILLFACTOR = 90);


GO
CREATE NONCLUSTERED INDEX [IX_MD_PageProp]
    ON [dbo].[MD]([TXT_Page] ASC, [TXT_Property] ASC, [TXT_Language] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MD_CreationTime]
    ON [dbo].[MD]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MD_CreatedBy_US]
    ON [dbo].[MD]([GID_CreatedBy_US] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_MD_BI__ID]
    ON [dbo].[MD]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MD_ModDateTime]
    ON [dbo].[MD]([DTT_ModTime] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Owner: ''GLOBAL'' cast to binary or creator''s SUID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'MD', @level2type = N'COLUMN', @level2name = N'GID_Section';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'SUID as text or unique description of properties for a single object, .e.g. view or desktop', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'MD', @level2type = N'COLUMN', @level2name = N'TXT_Page';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'2-character language code (EN, SP, IT, GR, etc)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'MD', @level2type = N'COLUMN', @level2name = N'TXT_Language';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Individual property name, i.e. NAME or CREATEDBY', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'MD', @level2type = N'COLUMN', @level2name = N'TXT_Property';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Property value', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'MD', @level2type = N'COLUMN', @level2name = N'TXT_Value';

