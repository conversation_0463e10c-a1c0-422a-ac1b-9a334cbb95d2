'OWNER: MI

Public Class clDiaAdmPerm
    'Inherits clVar

    'MI 3/12/13 Added sLogonPass.
    'MI 8/25/09 Enabled login groups.
    'MI 3/15/07 Created.

    'Enable global objects only if needed
    'Private goP As clProject
    'Private goMeta As clMetaData
    'Private goTR As clTransform
    'Private goData As clData
    'Private goErr As clError
    'Private goLog As clLog
    'Private goUI As clUI
    'Private goHist As clHistory

    Public par_sMode As String     'Page opening mode
    Public sReturnURL As String = ""
    Public bBackFromSubdialog As Boolean
    Public sMessageBoxPurpose As String

    'Parent dialog object
    '==> Replace or remove
    'Public oView As cldiaViewPro
    Public sTitle As String = "User/Group Permissions"
    Public fenenexecution As String = "diaAdmPerm.aspx"

    Public sVals As String = ""         'Main work string
    Public iCurrentPlane As Integer     'number of the current plane (Multiview view)
    Public sMode As String = ""         'Window opening mode in uppercase
    Public sResult As String = ""       'Result string returned by the window
    Public sFileName As String = ""     'Main file
    Public sRoles As String = ""        'Roles permission page
    Public sFeatures As String = ""     'Feature permission page
    Public sAccess As String = ""       'Selective access permission page
    Public sGlobalAccess As String = "" 'GLOBAL (default) selective access permissions
    'Public sGroupAccess As String = ""  'Access permissions of the selected Login's Login Group
    Public sGlobalFeatures As String = "" 'GLOBAL (default) features
    Public sLastLogin As String = ""    'Last selected user login
    Public sLastGroup As String = ""    'Last selected user login group
    Public sAccessFilterTypeEdited As String    'R', 'E', or 'D'
    Public sAccessFilter As String
    Public sAccessCondSummary As String
    Public sDefaultCond As String = ""  'Default condition statement
    Public sResultFromDiaFiltPro As String
    Public sNeverLabel As String        'Label for '(never)'

    'Table declarations
    Public dtLogins As New DataTable
    Public tLogins As New clTable
    Public dtAccess As New DataTable
    Public tAccess As New clTable


    Public Sub Initialize()
        Dim sProc As String = "clDiaAdmPerm::Initialize"
        'Try
        '   'Enable only if needed
        '   goP = HttpContext.Current.Session("goP")
        '   goTR = HttpContext.Current.Session("goTr")
        '   goMeta = HttpContext.Current.Session("goMeta")
        '   goData = HttpContext.Current.Session("goData")
        '   goErr = HttpContext.Current.Session("goErr")
        '   goLog = HttpContext.Current.Session("goLog")
        '   goUI = HttpContext.Current.Session("goUI")
        '   goHist = HttpContext.Current.Session("goHist")
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Public Sub New()

    End Sub

    Protected Overrides Sub Finalize()
        MyBase.Finalize()
    End Sub
End Class
