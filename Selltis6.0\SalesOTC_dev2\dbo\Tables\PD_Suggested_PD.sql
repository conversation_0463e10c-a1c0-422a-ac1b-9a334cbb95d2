﻿CREATE TABLE [dbo].[PD_Suggested_PD] (
    [GID_ID]  UNIQUEIDENTIFIER CONSTRAINT [DF_Product_Suggested_Product_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PD]  UNIQUEIDENTIFIER NOT NULL,
    [GID_PD2] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PD_Suggested_PD] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PD_Connected_PD] FOREIGN KEY ([GID_PD]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PD_Suggested_PD] FOREIGN KEY ([GID_PD2]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PD_Suggested_PD] NOCHECK CONSTRAINT [LNK_PD_Connected_PD];


GO
ALTER TABLE [dbo].[PD_Suggested_PD] NOCHECK CONSTRAINT [LNK_PD_Suggested_PD];


GO
CREATE CLUSTERED INDEX [IX_PD_Suggested_PD]
    ON [dbo].[PD_Suggested_PD]([GID_PD2] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PD_Connected_PD]
    ON [dbo].[PD_Suggested_PD]([GID_PD] ASC);

