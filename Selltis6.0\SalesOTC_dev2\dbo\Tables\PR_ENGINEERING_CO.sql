﻿CREATE TABLE [dbo].[PR_ENGINEERING_CO] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_PR_ENGINEERING_CO_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PR] UNIQUEIDENTIFIER NOT NULL,
    [G<PERSON>_<PERSON>] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PR_ENGINEERING_CO] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CO_EngineerFor_PR] FOREIGN KEY ([GID_PR]) REFERENCES [dbo].[PR] ([GID_ID]),
    CONSTRAINT [LNK_PR_ENGINEERING_CO] FOREIGN KEY ([GID_CO]) REFERENCES [dbo].[CO] ([GID_ID])
);


GO
ALTER TABLE [dbo].[PR_ENGINEERING_CO] NOCHECK CONSTRAINT [LNK_CO_EngineerFor_PR];


GO
ALTER TABLE [dbo].[PR_ENGINEERING_CO] NOCHECK CONSTRAINT [LNK_PR_ENGINEERING_CO];

