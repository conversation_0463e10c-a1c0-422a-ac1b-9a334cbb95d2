﻿CREATE TABLE [dbo].[QD] (
    [GID_ID]                 UNIQUEIDENTIFIER CONSTRAINT [DF_QD_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),(0))+CONVERT([binary](2),'QD',(0)))+CONVERT([binary](6),getutcdate(),(0)),(0))) NOT NULL,
    [BI__ID]                 BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]               NVARCHAR (80)    NULL,
    [DTT_CreationTime]       DATETIME         CONSTRAINT [DF_QD_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]           TINYINT          NULL,
    [TXT_ModBy]              VARCHAR (4)      NULL,
    [DTT_ModTime]            DATETIME         CONSTRAINT [DF_QD_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_QuoteOrderLineName] NVARCHAR (50)    NULL,
    [MMO_ImportData]         NTEXT            NULL,
    [SI__ShareState]         TINYINT          CONSTRAINT [DF_QD_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]       UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]           VARCHAR (50)     NULL,
    [TXT_ExternalID]         NVARCHAR (80)    NULL,
    [TXT_ExternalSource]     VARCHAR (10)     NULL,
    [TXT_ImpJobID]           VARCHAR (20)     NULL,
    [GID_FOR_VE]             UNIQUEIDENTIFIER NULL,
    [GID_RELATED_PF]         UNIQUEIDENTIFIER NULL,
    [GID_RELATED_PG]         UNIQUEIDENTIFIER NULL,
    [GID_FOR_PD]             UNIQUEIDENTIFIER NULL,
    [GID_FOR_MO]             UNIQUEIDENTIFIER NULL,
    [TXT_Model]              NVARCHAR (4000)  NULL,
    [TME_Time]               TIME (7)         NULL,
    [MLS_Status]             SMALLINT         NULL,
    [MLS_ReasonWonLost]      SMALLINT         NULL,
    [SR__LineNo]             REAL             NULL,
    [SR__Qty]                REAL             NULL,
    [TXT_Unit]               NVARCHAR (100)   NULL,
    [CUR_UnitPrice]          MONEY            NULL,
    [CUR_LineTotal]          MONEY            NULL,
    [CUR_Cost]               MONEY            NULL,
    [SI__SELLMULTI]          TINYINT          NULL,
    [SI__COSTMULTI]          TINYINT          NULL,
    [CUR_POCOST]             MONEY            NULL,
    [CUR_GrossProfit]        MONEY            NULL,
    [SR__GROSSMARGIN]        REAL             NULL,
    [GID_IN_QO]              UNIQUEIDENTIFIER NULL,
    [DTT_Time]               DATETIME         NULL,
    [TXT_SXLINENO]           NVARCHAR (250)   NULL,
    [TXT_SXSTATUS]           NVARCHAR (100)   NULL,
    [GID_RELATED_BC]         UNIQUEIDENTIFIER NULL,
    [SR__SXRELEASEDQTY]      REAL             NULL,
    [ADR_ATTACHMENTS]        NTEXT            NULL,
    [CUR_ReleasedLineTotal]  AS               ([SR__SXRELEASEDQTY]*[CUR_UnitPrice]),
    [GID_FOR_CO]             UNIQUEIDENTIFIER NULL,
    [GID_PEER_US]            UNIQUEIDENTIFIER NULL,
    [GID_CREDITEDTO_US]      UNIQUEIDENTIFIER NULL,
    [GID_RELATED_BR]         UNIQUEIDENTIFIER NULL,
    [DTT_LINEENTEREDDATE]    DATETIME         NULL,
    [CUR_ReleasedLineCost]   AS               ([SR__SXRELEASEDQTY]*[CUR_Cost]),
    [CUR_ReleasedLineProfit] AS               ([SR__SXRELEASEDQTY]*[CUR_GrossProfit]),
    [TXT_Description]        NVARCHAR (1000)  NULL,
    [DTT_PROMISEDATE]        DATETIME         NULL,
    [TXT_LOSTREASONCODE]     NVARCHAR (250)   NULL,
    [GID_RELATED_BU]         UNIQUEIDENTIFIER NULL,
    [TXT_BOTYPE]             NVARCHAR (250)   NULL,
    [SR__BONO]               REAL             NULL,
    [TXT_LineWarehouse]      NVARCHAR (250)   NULL,
    [TXT_WHSE]               NVARCHAR (500)   NULL,
    CONSTRAINT [PK_QD] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_QD_FOR_VE]
    ON [dbo].[QD]([GID_FOR_VE] ASC)
    INCLUDE([GID_IN_QO]);


GO
CREATE NONCLUSTERED INDEX [IX_QD_RELATED_PF]
    ON [dbo].[QD]([GID_RELATED_PF] ASC)
    INCLUDE([GID_IN_QO]);


GO
CREATE NONCLUSTERED INDEX [IX_QD_FOR_PD]
    ON [dbo].[QD]([GID_FOR_PD] ASC, [GID_IN_QO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QD_IN_QO]
    ON [dbo].[QD]([GID_IN_QO] ASC)
    INCLUDE([MLS_Status]);


GO
CREATE NONCLUSTERED INDEX [IX_QD_RELATED_BC]
    ON [dbo].[QD]([GID_RELATED_PF] ASC, [DTT_Time] ASC)
    INCLUDE([SYS_NAME], [DTT_CreationTime], [GID_RELATED_BC]);


GO



CREATE TRIGGER [dbo].[trQDUpdateTN]
ON [dbo].[QD]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in QD table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(120)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'QD'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable
					and tn2.GID_InternalID = inserted.GID_ID)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [QD]
			SET [QD].TXT_ExternalSource = '', [QD].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [QD].GID_ID = in1.GID_ID
				and ISNULL([QD].TXT_ExternalSource, '') <> ''
				and ISNULL([QD].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trQTUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trQTUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trQTUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!
