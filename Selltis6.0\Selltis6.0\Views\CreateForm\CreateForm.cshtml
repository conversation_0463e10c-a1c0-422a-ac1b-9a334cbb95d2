﻿@model Selltis.MVC.Models.clCreateForm
@using Kendo.Mvc.UI;
@*@using SelltisBI;*@
@using Microsoft.VisualBasic;
@using Selltis.MVC.Models;
@using System.Web.Script.Serialization;
@using Selltis.BusinessLogic;
@using Selltis.Core;
@{
    ViewBag.Title = "Create " + Model.FormTitle;
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<link href="~/Content/FormCSS.css?v=2.2" rel="stylesheet" />
<link href="~/Content/MaterialTabStrip.css?v=1.0" rel="stylesheet" />
<link href="~/Content/TicketStatusBreadcrum.css" rel="stylesheet" />
<style>

    h3, h4 {
        margin-top: 2px !important;
        margin-bottom: 2px !important;
    }

    #BTN_DEF, #BTN_CLOSE, #BTN_GENORDER, #BTN_PRINT, #BTN_PRINTSEND {
        font-size: 12px !important;
        font-family: arial !important;
        font-weight: bold !important;
        color: #ffffff;
        background-color: #0084d4 !important;
    }

    #BTN_SQ, #BTN_RQ, #BTN_PRQ, #BTN_DA, #BTN_ADDPROPERTY, #BTN_STANDARDQT {
        font-size: 12px !important;
        font-family: arial !important;
        font-weight: bold !important;
        color: #ffffff;
        background-color: #003665 !important;
    }

    #BTN_CREATEAC, #BTN_SP, #BTN_MULTIBIDOP, #BTN_DISQUALIFY, #BTN_CREATEJOB, #BTN_REOPEN {
        font-size: 13px !important;
        font-family: arial !important;
        font-weight: bold !important;
        color: #ffffff;
        background-color: #003665 !important;
    }

    #BTN_CREATEREVISION, #BTN_VALIDATECN {
        font-size: 13px !important;
        font-family: arial !important;
        font-weight: bold !important;
        color: #123568;
        background-color: #ECF1F7 !important;
    }

    #BTN_CONVERTTOQT {
        font-size: 13px !important;
        font-family: arial !important;
        font-weight: bold !important;
        color: #ffffff;
        background-color: #0084d4 !important;
    }

    div.k-multiselect div.btn-group {
        display: none;
    }

    .btn-group.more, .btn-group.links {
        display: inline-block;
    }

    .row {
        margin-top: 5px;
    }

    .CountLabel {
        font-size: 11px;
    }

    .demo-section {
        width: 100%;
    }

    .panel-title {
        line-height: 34px !important;
    }

    .anchoronClick {
        border: 2px solid #5B97CB;
        border-radius: 2px;
    }

    #LNKanchor2 a:hover {
        background-color: #D8D8D8 !important;
    }

    #LNKanchor1 a:hover {
        background-color: #D8D8D8 !important;
    }

    .k-editor-toolbar li {
        display: inline-block !important;
    }

    label {
        padding-top: 5px !important;
        color: #666666;
    }

        label.date {
            padding-top: 5px !important;
        }

    #page-loader {
        width: 100%;
        height: 100%;
        position: fixed;
        z-index: 10000000;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
    }

    .input-group-addon {
        color: #bbbbbb !important;
    }

    input[type="text"] {
        padding: 1px 0px 0px 0px !important;
        height: 24px !important;
    }

    .input-group[class*="col-"] {
        padding-right: 0px !important;
    }

    .k-dropdown-wrap .k-input {
        text-indent: 0px !important; /* to remove the white space in front of kendo combobox, date&time pickers*/
    }

    .k-picker-wrap .k-input {
        text-indent: 0px !important; /* to remove the white space in front of kendo combobox, date&time pickers*/
    }

    #divCreatLinked ul li {
        cursor: pointer;
    }

        #divCreatLinked ul li ul {
            position: absolute;
            display: none;
        }

        #divCreatLinked ul li:hover ul {
            display: block;
            position: inherit;
        }

    .k-editor .k-editable-area {
        border-style: none;
    }

    /*Mullti List Box setting Option to left and top*/
    .ListBoxURL {
        padding: 0px 0px !important;
    }
    /*83B9E2*/
    .btn.btn-sm.btn-info {
        background-color: #5bafda !important;
    }

    .ADRControlCss .k-widget.k-dropdown.k-header {
        width: 135px !important;
        height: 23px !important;
        margin-top: 2px !important;
    }

        .ADRControlCss .k-widget.k-dropdown.k-header .k-dropdown-wrap.k-state-default {
            height: 100% !important;
        }

            .ADRControlCss .k-widget.k-dropdown.k-header .k-dropdown-wrap.k-state-default .k-input {
                line-height: normal !important;
            }

            .ADRControlCss .k-widget.k-dropdown.k-header .k-dropdown-wrap.k-state-default .k-select {
                line-height: normal !important;
            }

    .k-icon.k-clear-value.k-i-close {
        display: none;
    }

    .k-list-scroller .k-list.k-reset {
        overflow-y: auto;
    }

    /*Css changes crmp specific.. S1*/
    /*Changed to crmp's font-family*/
    .k-input, .k-list-container, .k-widget.k-grid, .input-group[class*="col-"], a, #divfrmtitle {
        font-family: sans-serif !important;
    }

    /*Disabled Date/Time picker styles.. S1*/
    .k-picker-wrap.k-state-disabled input[disabled] {
        background-color: #d1d1d1 !important;
        padding: .167em 0 !important;
        height: 26px !important;
        cursor: not-allowed;
    }
    /*Kendo dropdown disabled*/
    .k-dropdown-wrap.k-state-disabled input[disabled] {
        background-color: #d1d1d1 !important;
        cursor: not-allowed;
    }

    /*input disabled*/
    .form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
        background-color: #E1E1E1 !important;
        cursor: not-allowed;
    }

    /*Form background Color to FAFAFA*/
    .panel, .input-group-addon, textarea, .k-widget.k-grid, k-widget.k-editor.k-header.k-editor-widget,
    .k-widget.k-editor.k-header.k-editor-widget, .k-widget.k-editor.k-header, .k-combobox .k-select, .k-multiselect-wrap,
    .k-timepicker .k-select, .k-datepicker .k-select, .k-grid-content, .k-dropdown-wrap.k-state-default,
    input[type="text"] {
        background-color: #FAFAFA !important;
    }
    /*Kendo toolbar*/
    .k-editor .k-content {
        background: #FAFAFA !important;
    }

    .headercontrolclass input[type="text"],
    .headercontrolclass .k-combobox .k-select,
    .headercontrolclass .input-group-addon,
    .headercontrolclass .k-widget.k-grid,
    .headercontrolclass .k-datepicker .k-select,
    .headercontrolclass .k-datepicker .k-picker-wrap.k-state-default,
    .headercontrolclass .k-widget.k-editor.k-header.k-editor-widget,
    .headercontrolclass .k-widget.k-editor.k-header,
    .headercontrolclass .k-widget.k-editor.k-editor-widget .k-editable-area,
    .headercontrolclass .k-multiselect-wrap,
    .headercontrolclass .k-multiselect-wrap.k-floatwrap ul li {
        background-color: #eee !important;
    }

    /*Kendo multi select disabled*/
    .k-widget.k-multiselect.k-header.k-state-disabled div, .k-widget.k-multiselect.k-header.k-state-disabled div ul li, .k-widget.k-multiselect.k-header.k-state-disabled div input, .k-widget.k-multiselect.k-header.k-state-disabled div ul li span span {
        background-color: #d1d1d1 !important;
        cursor: not-allowed !important;
    }

    .k-widget.k-multiselect.k-header.k-state-disabled {
        border: none !important;
    }

        .k-widget.k-multiselect.k-header.k-state-disabled div input {
            display: none;
        }
    /*End 'Css changes crmp specific'.. S1*/


    /*kendo editor maximize*/
    .kendoeditormaximize {
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        position: fixed;
        z-index: 9999;
        background-color: #fff;
        padding: 0px 5px 0px 5px;
    }

    .editorcontentheight {
        height: 100% !important;
    }

    .hidescrollinfullscreen {
        overflow: hidden;
    }

    /*.stylein_enabledstate {
        margin-top: -12px !important;
    }*/

    .editorFontSize {
        font-size: 15px;
    }

    .paddingforicon {
        padding: 5px !important;
    }

    .paddingforlabel {
        font-size: 16px !important;
        padding: 0px 0px 0px 5px !important;
        margin: 0px !important;
        left: 0 !important;
    }
    /*kendo editor maximize end*/
    .k-multiselect-wrap.k-floatwrap ul.k-reset {
        padding-top: 2.5px !important;
    }

    .k-edit-buttons.k-state-default .k-button.k-primary {
        color: #333 !important;
    }

    /*Form Header to be shown always*/
    #VisibleContentId {
        position: fixed;
        z-index: 600;
        width: 85.9%;
    }
    /*Model dialogue*/
    .modal-open .modal {
        z-index: 999999 !important;
    }

    .customToolbarClass1 {
        margin-right: 4%;
    }

    .customToolbarClass2 {
        margin-right: 1%;
    }

    .disabled {
        pointer-events: none;
        opacity: 0.6;
    }

    .AddMarginClass {
        margin: 3px 0px;
    }

    /*SB 10-31-2017 Tckt#1896 Fileds are overlapping*/
    .DateTimePicker_cls {
        display: block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        /*width: 100%;*/
    }

    table, th, td {
        border: 1px solid coral;
        border-collapse: collapse;
        padding-left: 5px;
    }

    .k-grid tr.k-alt {
        background-color: transparent !important;
    }

    .k-grid {
        font-size: 13px !important;
    }
</style>
<style>
    @@-moz-document url-prefix() {
        /*#tabMain > .row, #tabMain > .row > .row, #tabMain > .row > .col-md-1 > .row, #tabMain > .row > .col-md-2 > .row,
        #tabMain > .row > .col-md-3 > .row, #tabMain > .row > .col-md-7 > .row, #tabMain > .row > .col-md-11 > .row,
        #tabMain > .row > .col-md-4 > .row, #tabMain > .row > .col-md-8 > .row, #tabMain > .row > .col-md-12 > .row #tabMain > .row > .col-md-5 > .row, #tabMain > .row > .col-md-9 > .row, #tabMain > .row > .col-md-6 > .row,
        #tabMain > .row > .col-md-10 > .row,
        #tabMain > .row > .col-md-6 > .row > .col-md-1, #tabMain > .row > .col-md-6 > .row > .col-md-2, #tabMain > .row > .col-md-6 > .row > .col-md-3,
        #tabMain > .row > .col-md-6 > .row > .col-md-4, #tabMain > .row > .col-md-6 > .row > .col-md-5, #tabMain > .row > .col-md-6 > .row > .col-md-6,
        #tabMain > .row > .col-md-6 > .row > .col-md-7, #tabMain > .row > .col-md-6 > .row > .col-md-8, #tabMain > .row > .col-md-6 > .row > .col-md-9,
        #tabMain > .row > .col-md-6 > .row > .col-md-10, #tabMain > .row > .col-md-6 > .row > .col-md-11, #tabMain > .row > .col-md-6 > .row > .col-md-12 {
            margin-top: 15px !important;
        }*/
        /*.tab-pane > .row{
            margin-top: 15px !important;
        }*/
        .tab-pane > .row > .row {
            margin-top: 15px !important;
        }

            .tab-pane > .row > .row > .col-md-1 {
                margin-top: 10px !important;
            }

        .tab-pane > .row > .col-md-1 > .row, .tab-pane > .row > .col-md-2 > .row,
        .tab-pane > .row > .col-md-3 > .row, .tab-pane > .row > .col-md-7 > .row, .tab-pane > .row > .col-md-11 > .row,
        .tab-pane > .row > .col-md-4 > .row, .tab-pane > .row > .col-md-8 > .row, .tab-pane > .row > .col-md-12 > .row,
        .tab-pane > .row > .col-md-5 > .row, .tab-pane > .row > .col-md-9 > .row, .tab-pane > .row > .col-md-6 > .row,
        .tab-pane > .row > .col-md-10 > .row {
            margin-top: 10px !important;
        }

            .tab-pane > .row > .col-md-6 > .row > .col-md-1, .tab-pane > .row > .col-md-6 > .row > .col-md-2, .tab-pane > .row > .col-md-6 > .row > .col-md-3,
            .tab-pane > .row > .col-md-6 > .row > .col-md-4, .tab-pane > .row > .col-md-6 > .row > .col-md-5, .tab-pane > .row > .col-md-6 > .row > .col-md-6,
            .tab-pane > .row > .col-md-6 > .row > .col-md-7, .tab-pane > .row > .col-md-6 > .row > .col-md-8, .tab-pane > .row > .col-md-6 > .row > .col-md-9,
            .tab-pane > .row > .col-md-6 > .row > .col-md-10, .tab-pane > .row > .col-md-6 > .row > .col-md-11, .tab-pane > .row > .col-md-6 > .row > .col-md-12 {
                margin-top: 15px !important;
            }
    }
</style>

<!--LNK control styles-->
<style>
    .k-grid-pager {
        height: 0px;
    }

    .k-grid table {
        table-layout: fixed;
    }

    .k-grid td {
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .k-grid tr {
        background-color: transparent;
    }

    .k-grid .k-selected {
        background: #428bca !important;
        color: #fff !important;
    }

    .k-grid tbody tr {
        height: 8px;
    }

    .k-grid tr td {
        border-width: 0px 0 0 0px;
    }

    .k-widget {
        border-color: #ccc;
    }

    .k-grid tbody tr td {
        cursor: pointer;
        line-height: 21px;
    }

    /*enabledClass injecting css when disabled/enabled*/
    .enabledClass .k-grid tr {
        background-color: #e1e1e1 !important;
    }

    .enabledClass .k-grid .k-selected {
        background: #e1e1e1 !important;
        color: #666 !important;
    }

    .enabledClass .k-grid tr.k-selected:hover td {
        background: #e1e1e1 !important;
        color: #666 !important;
    }

    .enabledClass .k-grid tr:hover td, .enabledClass .k-grid table {
        cursor: not-allowed;
    }

    .enabledClass .k-grid {
        border: none;
    }

    .k-input-solid {
        border-color: #ccc;
        color: #424242;
        border-style: solid;
        background-color: #fff;
        border-top: none;
        border-left: none;
        border-right: none;
    }

    .k-input-md .k-input-inner, .k-picker-md .k-input-inner {
        padding: 2px 2px !important;
        height: inherit !important;
    }


    .k-draghandle.k-selected:hover, .k-ghost-splitbar-horizontal, .k-ghost-splitbar-vertical, .k-list > .k-state-highlight, .k-list > .k-selected, .k-marquee-color, .k-panel > .k-selected, .k-scheduler .k-scheduler-toolbar .k-selected, .k-scheduler .k-today.k-selected, .k-selected, .k-selected:link, .k-selected:visited {
        background-color: #428bca !important;
    }

    .k-button-solid-primary {
        background-color: #ff6358 !important;
    }
</style>

<link href="~/Content/themes/Selltis/css/AjaxCss/animate.min.css" rel="stylesheet" />
<link href="~/Content/themes/Selltis/css/AjaxCss/style.min.css" rel="stylesheet" />

<section id="content_wrapper">


    <div id="content" class="animated fadeIn">
        <div class="VisibleContent" id="VisibleContentId" style="background-color: #EEEEEE;">
            <div class="row headrow">
                <div class="col-md-12">
                    <div class="panel panel-visible" id="spy2" name="Fpopin">
                        <div class="topbar-left">
                            <div class="panel-heading panelhead">

                                <!--overflow record title in title bar or else shows breaking when resize the window..J-->
                                <div class="col-md-6 col-sm-4" style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;" title="@Model.FormTitle">

                                    @if (Model.RecordSelectorDisplay && Model.Mode == "MODIF")
                                    {
                                        <div id="recordSelector" class="panel-title col-md-9">
                                            <span id="divfrmtitle" class="panel-title"><i class="glyphicon glyphicon-th-large"></i>&nbsp;&nbsp;@Model.TableLabel (</span>

                                            @(Html.Kendo().ComboBox()
                                                .Name(Model.Table + "_RecordSelector")
                                                .DataTextField("NAME")
                                                .DataValueField("ID")
                                                .HtmlAttributes(new { @id = "cmbRecordSelector", @style = "display:block; width: 100%; background-color: #E9E9E9; ", @class = "RecordSelector", @title = "Please select a record" })
                                                .ValuePrimitive(true)
                                                .Value(Model.RecordId)
                                                .Text(Model.RecordSelectorInitialText)
                                                .Filter(FilterType.StartsWith)
                                                .FooterTemplate("Total #: instance.dataSource.total() # loaded")
                                                //.HeaderTemplate("<div class=\"dropdown-header k-widget k-header\"><span>Photo</span><span>Contact info</span></div>")
                                                .Template(Model.RecordSelectorTemplateHTML)
                                                .AutoBind(false)
                                                .DataSource(source =>
                                                {
                                                    source.Custom()
                                                        .ServerFiltering(true)
                                                        //.ServerPaging(false)
                                                        //.PageSize(100)
                                                        .Type("aspnetmvc-ajax") //Set this type if you want to use DataSourceRequest and ToDataSourceResult instances
                                                        .Transport(transport =>
                                                        {
                                                            transport.Read("LoadRecordSelector", "CreateForm", new { Model.FormKey, Model.RecordId, Model.ViewKey, bAppendRecords = false, LastRecordID = "", @Model.Key, @Model.FormHistoryKey });
                                                        });
                                                })
                                                .Events(events => events.Change("RecordSelected").Close("RecordSelectedListClosed"))
                                            //.Virtual(v => v.ItemHeight(50).ValueMapper("valueMapper"))
                                            )
                                            <input hidden="hidden" id="hdnRecordSelectorSelectedRecordID" type="text" value="" />
                                            <input hidden="hidden" id="hdnRecordSelectorCurrentRecordID" type="text" value="" />
                                            <input hidden="hidden" id="hdnRecordSelectorCurrentRecordText" type="text" value="@Model.RecordSelectorInitialText" />

                                            <script>
                                                $(document).ready(function () {
                                                    //debugger;
                                                    var cmb1 = $("#cmbRecordSelector").data("kendoComboBox").dataSource;
                                                    $("#hdnRecordSelectorCurrentRecordID").val($("#cmbRecordSelector").val());
                                                    //Add scroll event to Record Selector to sappend items when scroll end reached
                                                    $('.k-list-scroller').scroll(function () {
                                                        if ($(this).scrollTop() + $(this).innerHeight() >= ($(this)[0].scrollHeight)) {
                                                            //debugger;
                                                            if (cmb1.data().length == 0) {
                                                                return;
                                                            }
                                                            var FilterText = "";
                                                            if (cmb1._filter.filters.length > 0) {
                                                                FilterText = cmb1._filter.filters[0].value;
                                                            }
                                                            showProgress();
                                                            $.ajax({
                                                                url: "/CreateForm/LoadRecordSelector",
                                                                type: "POST",
                                                                dataType: "json",
                                                                data: { FormKey: '@Model.FormKey', SelectedRecordID: "", ViewKey: '@Model.ViewKey', bAppendRecords: true, LastRecordID: cmb1.data()[cmb1.data().length - 1]["ID"], Key: '@Model.Key', FormHistoryKey: '@Model.FormHistoryKey', ScrollFilterText: FilterText },
                                                                //async: false,
                                                                success: function (data) {
                                                                    //debugger;
                                                                    if (data == "[]" || data == "") {
                                                                        hideProgress();
                                                                        return;
                                                                    }
                                                                    var cmb = $("#cmbRecordSelector").data("kendoComboBox").dataSource;
                                                                    var datasource = jQuery.parseJSON(JSON.stringify(cmb.data()));
                                                                    var FullData = cmb.data();
                                                                    var CurrentData = datasource.concat(data);
                                                                    cmb.data(CurrentData);
                                                                    $('#hdnLastRecord').val = data[data.length - 1]["GID_ID"];
                                                                    hideProgress();
                                                                },
                                                                error: function (data) {
                                                                    //debugger;
                                                                    hideProgress();
                                                                    //alert(data.responseText);
                                                                }
                                                            })
                                                        } else {

                                                        }
                                                    });

                                                })

                                                function RecordSelected(e) {
                                                    //debugger;
                                                    //var item = e.item;
                                                    //var text = item.text();
                                                    var table = '@Model.Table';
                                                    var RecordID = this.value();
                                                    if ($("#hdnRecordSelectorCurrentRecordID").val() == RecordID) {
                                                        return;
                                                    }
                                                    $("#hdnRecordSelectorSelectedRecordID").val(RecordID);
                                                    var viewid = '@Model.ViewKey';
                                                    if (RecordID == "")
                                                        return;
                                                    //Check if selected item or random text entered
                                                    if (isGuid(RecordID) == false) {
                                                        return;
                                                    }
                                                    //alert(text);
                                                    //window.location = "/CreateForm/CreateForm/" + Model.Table + "/" + item.value() + "/TYPE/false/false/" + viewkey + "/false/MASTERID/FORMKEY/FIELD";
                                                    $.ajax({
                                                        url: '/CreateForm/ClearExistingFormSession',
                                                        async: false,
                                                        cache: false,
                                                        data: { File: table, RecordId: this.value(), SelectedIndex: 1 },
                                                        success: function (data) {
                                                            if (data == "success") {
                                                                //debugger;
                                                                FormNavigation('', '@Model.Table', '@Model.RecordId', '@Model.ViewKey');
                                                            }
                                                        },
                                                        error: function (data) {
                                                            alert(data.responseText);
                                                        }
                                                    })
                                                }

                                                function RecordSelectedListClosed() {
                                                    //debugger;
                                                    var cmb1 = $("#cmbRecordSelector").data("kendoComboBox");
                                                    //cmb1.value(cmb1.value());
                                                    cmb1.text($("#hdnRecordSelectorCurrentRecordText").val());
                                                    cmb1.value($("#hdnRecordSelectorCurrentRecordID").val());
                                                }

                                                function isGuid(stringToTest) {
                                                    if (stringToTest[0] === "{") {
                                                        stringToTest = stringToTest.substring(1, stringToTest.length - 1);
                                                    }
                                                    var regexGuid = /^(\{){0,1}[0-9a-fA-F]{8}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{4}\-[0-9a-fA-F]{12}(\}){0,1}$/gi;
                                                    return regexGuid.test(stringToTest);
                                                }
                                            </script>
                                            <style>
                                                .RecordSelector {
                                                    font-size: 15px !important;
                                                }
                                            </style>
                                            <span id="divfrmtitleend" class="panel-title" style="color:#999999; font-size:15px; font-weight:bold;">)</span>

                                        </div>

                                    }
                                    else
                                    {
                                        <span id="divfrmtitle" class="panel-title"><i class="glyphicon glyphicon-th-large"></i>&nbsp;&nbsp;@Model.FormTitle</span>
                                    }
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="panel-heading-btn" style="float: right; margin-top: 3px;">

                                        <button type="button" class="btn btn-sm btn-primary" onclick="BackToDesignClick('@Model.gsForm.TableName')" title="Back to design" style="display:none;" id="btnBackToDesign"><i class="fa fa-reply" aria-hidden="true"></i></button>
                                        <button type="button" class="btn btn-sm btn-info" title="Show 360 View" id="btngotopreview" onclick="GoDetailsPage('@Model.gsForm.TableName','@Model.RecordId')"><i class="glyphicon glyphicon-eye-open"></i></button>
                                        <button type="button" class="btn btn-sm btn-success" title="Save and close" id="btnSaveandClose"><i class="glyphicon glyphicon-floppy-disk"></i></button>
                                        <button type="button" class="btn btn-sm btn-primary" title="Save and leave open" id="btnSaveandleave"><i class="fa fa-folder-open"></i></button>
                                        <button type="button" class="btn btn-sm btn-danger" title="Cancel" onclick="onCancel()" id="btnCancel"><i class="fa fa-times"></i></button>
                                        <button type="button" class="btn btn-sm btn-primary" title="Delete" id="btnDelete" onclick="onDelete()"><i class="fa fa-trash-o" aria-hidden="true"></i></button>
                                        <div class="btn-group hidden-sm hidden-xs" title="Create Linked" id="divCreatLinked">
                                            <button type="button" id="btnCreateLinked" class="btn btn-sm btn-success dropdown-toggle" data-toggle="dropdown">
                                                <i class="fa fa-link"></i><span class="caret"></span>
                                            </button>
                                            <ul id="createlinkedmenu" class="dropdown-menu pull-right" role="menu">
                                                @Html.Raw(Util.GetCreateLinkData())
                                            </ul>
                                        </div>
                                        <div class="btn-group hidden-sm hidden-xs" id="divSharingStatus">
                                            <button type="button" id="btnSharingStatus" title="Options" class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown">
                                                <i class="fa fa-ellipsis-v"></i>
                                            </button>



                                            @Html.Raw(Util.GetSharedStatusData(Model.Metadata, Model.gsForm.RecordShareState, Model.gsForm.Table, Model.gsForm.GetMode(), Model.gsForm.doRS, Model.gsForm, Model.FormKey.Replace(" ", "")))

                                        </div>

                                        @*<button type="button" id="btnToolbarHelp" onclick="window.open('http://help.selltis.com/sales/index.html', '_blank'); return false;" class="btn btn-sm btn-primary hidden-sm hidden-xs" title="Help"><i class="fa fa-question"></i></button>*@

                                        <button type="button" class="ArrowOperations btn btn-sm btn-default hidden-sm hidden-xs" id="First" title="First" onclick="FormNavigation('First','@Model.Table','@Model.RecordId','@Model.ViewKey')"><i class="fa fa-step-backward"></i></button>
                                        <button type="button" class="ArrowOperations btn btn-sm btn-default hidden-sm hidden-xs" id="Previous" title="Previous" onclick="FormNavigation('Previous','@Model.Table','@Model.RecordId','@Model.ViewKey')"><i class="fa fa-arrow-left"></i></button>
                                        <button type="button" class="ArrowOperations btn btn-sm btn-default hidden-sm hidden-xs" id="Next" title="Next" onclick="FormNavigation('Next','@Model.Table','@Model.RecordId','@Model.ViewKey')"><i class="fa fa-arrow-right"></i></button>
                                        <button type="button" class="ArrowOperations btn btn-sm btn-default hidden-sm hidden-xs" id="Last" title="Last" onclick="FormNavigation('Last','@Model.Table','@Model.RecordId','@Model.ViewKey')"><i class="fa fa-step-forward"></i></button>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8" id="HeaderControlsRow" style="margin: 3px 0px;padding-left:1%;">
                    <div class="col-md-12" id="HeaderPart">

                    </div>
                </div>
                @if (Model.CustomToolbarControls.Count > 0)
                {
                    <div class="col-md-4" id="secondtoolbar">
                        <div class="pull-right customToolbarClass1">
                            @for (int i = 0; i < Model.CustomToolbarControls.Count; i++)
                            {
                                var _visibility = string.Empty;
                                var _disabled = string.Empty;
                                if (Model.CustomToolbarControls[i].isVisible == true)
                                {
                                    _visibility = "inline";
                                }
                                else
                                {
                                    _visibility = "none";
                                }
                                if (Model.CustomToolbarControls[i].IsDisabled == true)
                                {
                                    _disabled = "disabled";
                                }
                                if (Model.CustomToolbarControls[i].ControlType == "BTN_")
                                {

                                    string sScript = "";
                                    var cScripts = Selltis.Core.Util.GetScripts(Model.CustomToolbarControls[i].FieldName, Model.Table);
                                    if (cScripts.Count > 0)
                                    {
                                        sScript = Selltis.Core.Util.GetEventHandler(cScripts, "FORMCONTROLONCHANGE", Model.CustomToolbarControls[i].FieldName, Model.Table);
                                    }
                                    if (!string.IsNullOrEmpty(sScript))
                                    {
                                        <button style="display:@_visibility" type="button" id="@Model.CustomToolbarControls[i].FieldName" onclick="RunScript('@sScript',true)" class="btn btn-xs btn-default" title="@Model.CustomToolbarControls[i].LabelName">
                                            @if (!Model.CustomToolbarControls[i].ImageUrl.Equals(clC.EOT.ToString()) && !Model.CustomToolbarControls[i].ImageUrl.Equals(string.Empty))
                                            {
                                                <img src="~/Content/themes/Selltis/images/@Model.CustomToolbarControls[i].ImageUrl" />
                                            }
                                            &nbsp; @Model.CustomToolbarControls[i].LabelName &nbsp;
                                        </button>
                                    }
                                    else
                                    {
                                        <button style="display:@_visibility" type="button" id="@Model.CustomToolbarControls[i].FieldName" class="btn btn-xs btn-default" title="@Model.CustomToolbarControls[i].LabelName">
                                            @if (!Model.CustomToolbarControls[i].ImageUrl.Equals(clC.EOT.ToString()) && !Model.CustomToolbarControls[i].ImageUrl.Equals(string.Empty))
                                            {
                                                <img src="~/Content/themes/Selltis/images/@Model.CustomToolbarControls[i].ImageUrl" />
                                            }
                                            &nbsp; @Model.CustomToolbarControls[i].LabelName &nbsp;
                                        </button>
                                    }

                                }
                                else if (Model.CustomToolbarControls[i].ControlType == "CHK_")
                                {
                                    <input style="display:@_visibility" type="checkbox" id="@Model.CustomToolbarControls[i].FieldName" title="@Model.CustomToolbarControls[i].LabelName" />
                                    <label style="display:@_visibility" title="@Model.CustomToolbarControls[i].LabelName" for="@Model.CustomToolbarControls[i].FieldName">@Model.CustomToolbarControls[i].LabelName</label>

                                }
                                else if (Model.CustomToolbarControls[i].ControlType == "LBL_")
                                {
                                    <label style="display:@_visibility" id="@Model.CustomToolbarControls[i].FieldName">@Model.CustomToolbarControls[i].LabelName &nbsp;</label>

                                }
                            }
                        </div>
                    </div>
                }


            </div>
            <div class="row headrow" style="margin-left: 1%;margin-right: 1.3%;margin-top:5px !important;">
                <div class="col-md-12 tabcontentpadding">
                    @*<div style="line-height: 34px!important;" class="panel-heading">*@
                    <div class="panel-heading tabstrip-css">

                        <div class="panel-title" style="padding:0px;">
                            <input type="hidden" value="btnMain" id="hdPreTab" />
                            <input type="hidden" value="" id="hdRunScriptControl" />
                            <button type="button" onmouseout="onMouseOut(this)" onmouseover="onHover(this)" class="btnBootstrap btn btn-default" id="btnMain"><i class="fa fa-info-circle"></i>&nbsp;&nbsp;Main</button>
                            @{
                                string selectedtab;

                                int k = Model.ShowDefaultNoofTabs;
                                int y;

                                for (y = 0; y < Model.lstTabsorderlist.Count; y++)
                                {
                                    var _visibility = "inline";
                                    var _disabled = "";
                                    if (y < k)
                                    {
                                        Selltis.Core.ControlState _ControlState = new Selltis.Core.ControlState();
                                        //_ControlState = Selltis.Core.Util.GetControlsStateForField("TAB_FORM[" + (y + 1) + "]", Model.gsForm);
                                        _ControlState = Selltis.Core.Util.GetControlsStateForField("TAB_FORM[" + Model.lstTabsorderlist[y].TabIndex + "]", Model.gsForm);
                                        if (_ControlState != null && _ControlState.FieldPropertiy != null && _ControlState.FieldPropertiy.State != null)
                                        {
                                            if (_ControlState.FieldPropertiy.State == 2)
                                            {
                                                k++;
                                                Model.ShowDefaultNoofTabs++;
                                                _visibility = "none";
                                            }
                                            else if (_ControlState.FieldPropertiy.State == 1 || _ControlState.FieldPropertiy.State == 4)
                                            {
                                                _disabled = "disabled";
                                            }
                                        }
                                        <button onmouseout="onMouseOut(this)" onmouseover="onHover(this)" type="button" @_disabled class="btnBootstrap btn btn-default" id="@("btn" + Model.lstTabsorderlist[y].TabName.Replace(" ", ""))" style="display:@_visibility;"><i class="@Model.lstTabsorderlist[y].TabIcon"></i>&nbsp;&nbsp;@Model.lstTabsorderlist[y].TabName</button>
                                    }
                                    else //if (y == 4)
                                    {
                                        break;
                                    }
                                }
                                // if (y == 4)
                                // {
                                if (Model.lstTabsorderlist.Count > k)
                                {
                                    <div id="divMoreDropdown" class="btn-group more">
                                        <!--Added new tab to show selected tab from 'more' tabs list..S1-->
                                        <button type="button" id="btnShowSelectedTab" class="btnBootstrap btn btn-default" style="min-width: 90px;display:none;"></button>
                                        <button type="button" class="btnBootstrap btn btn-default dropdown-toggle" data-toggle="dropdown" style="min-width: 90px;">
                                            <i class="fa fa-ellipsis-h"></i>&nbsp;&nbsp;More <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu" role="menu">
                                            @for (y = k; y < Model.lstTabsorderlist.Count; y++)
                                            {
                                                var _visibility = "inline";
                                                var _disabled = "";
                                                var _displayCss = "";
                                                // if (y > 3)
                                                // {
                                                Selltis.Core.ControlState _ControlState = new Selltis.Core.ControlState();
                                                //_ControlState = Selltis.Core.Util.GetControlsStateForField("TAB_FORM[" + (y + 1) + "]", Model.gsForm);
                                                _ControlState = Selltis.Core.Util.GetControlsStateForField("TAB_FORM[" + Model.lstTabsorderlist[y].TabIndex + "]", Model.gsForm);
                                                if (_ControlState != null && _ControlState.FieldPropertiy != null && _ControlState.FieldPropertiy.State != null)
                                                {
                                                    if (_ControlState.FieldPropertiy.State == 2)
                                                    {
                                                        _visibility = "none";
                                                    }
                                                    else if (_ControlState.FieldPropertiy.State == 1 || _ControlState.FieldPropertiy.State == 4)
                                                    {
                                                        _disabled = "disabled";
                                                    }
                                                }
                                                if (_visibility == "none")
                                                {
                                                    _displayCss = "display:none;";
                                                }
                                                <li style="@_displayCss">
                                                    <button onmouseout="onMouseOut(this)" onmouseover="onHover(this)" type="button" @_disabled class="btnBootstrap btn btn-default" id="@("btn" + Model.lstTabsorderlist[y].TabName.Replace(" ", ""))" style="display:@_visibility;"><i class="@Model.lstTabsorderlist[y].TabIcon"></i>&nbsp;&nbsp;@Model.lstTabsorderlist[y].TabName</button>
                                                </li>
                                                // }
                                            }
                                        </ul>
                                    </div>
                                }
                                // }
                            }

                            <div class="btn-group links" style="display:none">
                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" style="border: none!important; background-color: transparent!important;">
                                    <i class="fa fa-link"></i>&nbsp;&nbsp;Links <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu" role="menu">
                                    <li id="linked-ac"><a href="#">Activities</a></li>
                                    <li><a href="#">Appointments</a></li>
                                    <li><a href="#">Expenses</a></li>
                                    <li><a href="#">Messages</a></li>
                                    <li><a href="#">Opportunities</a></li>
                                    <li><a href="#">Projects</a></li>
                                    <li><a href="#">Quotes</a></li>
                                    <li><a href="#">To Dos</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="DummyDivForFormContent" style="height:114px;"></div>
        <div class="row card" style="background-color:#FAFAFA !important;">
            <div class="col-md-12">
                <div id="panelform">

                    <div class="row" id="pnlError" style="display: none; background-color: #FFFFD0; border-color: Gray; border-width: 1px; border-style: Solid; min-height: 22px; width: 100%; border-radius: 5px; padding: 2px 3px; ">
                        <div class="col-md-12">

                            <div class="col-md-12">
                                <span><i class="fa fa-exclamation-triangle" aria-hidden="true"></i></span>
                                <span id="lblError" style="color:Black;font-family:Verdana,Arial,Helvetica;font-size:8pt;">
                                </span>
                            </div>
                        </div>
                    </div>

                    @*Message panel added -- RN*@
                    <div class="row" id="pnlMessagePanel" style="display: none; background-color: #FFFFD0; border-color: Gray; border-width: 1px; border-style: Solid; min-height: 22px; width: 100%; border-radius: 5px; padding: 2px 3px; ">
                        <div class="col-md-12">

                            <div class="col-md-12">
                                <span><i class="fa fa-exclamation-triangle" aria-hidden="true"></i></span>
                                <span id="lblMessagePanel" style="color:Black;font-family:Verdana,Arial,Helvetica;font-size:8pt;">
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="PNL_MessageBox" style="display: none;">
                        <div class="col-md-2">

                        </div>
                        <div class="col-md-8" style="margin-top: -5px;">
                            <div class="row" style="background-color: #5b97cb;border-radius: 10px;height: 28px;">
                                @*<div style="width: 100%">
                                    <div class="col-md-11">*@
                                <label style="margin-left: 10px;color: white;font-weight:bold;margin-top: -2px;" id="LBL_MsgBoxTitle">LBL_MsgBoxTitle</label>
                                @*</div>
                                    <div class="col-md-1" style="text-align:right;">*@
                                @*<img id="IMG_MsgBoxClose" src="~/Content/Images/CloseWhiteNoBorder.gif" style="cursor: pointer;margin-top: 5px;margin-right: 5px;float:right;" />*@
                                <label id="IMG_MsgBoxClose" style="cursor: pointer;margin-top: -2px;margin-right: 10px;float:right;color: white;"><b>X</b></label>
                                @*</div>
                                    </div>*@
                            </div>
                            <div class="row" style="margin-top: -5px;background-color: #FFFFB0;border-radius: 0px 0px 10px 10px;">
                                <div class="row" style="margin-left:10px;margin-right:10px;font-size: 12px;">
                                    <label id="LBL_MsgBoxMessage" style="margin-top: -5px;">LBL_MsgBoxMessage</label>
                                </div>
                                <div class="row" style="margin-left:10px;margin-right:10px;margin-top: -5px;">
                                    <div class="col-md-12">
                                        <textarea id="TXT_MsgBoxInput" name="TXT_MsgBoxInput" style="width: 100%; height: 50px; display: none ;font-size :13px;">TXT_MsgBoxInput</textarea>
                                    </div>
                                    <div id="divTXT_MsgBoxInputRich" style="display:none">
                                        @(Html.Kendo().Editor().Name("TXT_MsgBoxInputRich")
                                            .HtmlAttributes(new { @class = "form-control", title = "", style = "width:100%;height:50px;font-size :13px;" })
                                            .Value("")
                                        )
                                    </div>
                                </div>
                                <div class="row" style="margin-left:10px;margin-right:10px;margin-top: 0px;margin-bottom: 5px;">
                                    <br />
                                    <div align="center">
                                        <input type="button" class="btn btn-xs btn-primary" name="BTN_MsgBox1" id="BTN_MsgBox1" value="Yes" style="display:none" />
                                        <input type="button" class="btn btn-xs btn-primary" name="BTN_MsgBox2" id="BTN_MsgBox2" value="No" style="display:none" />
                                        <input type="button" class="btn btn-xs btn-primary" name="BTN_MsgBox3" id="BTN_MsgBox3" value="Cancel" style="display:none" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-2">

                        </div>
                    </div>
                    <div class="row headrow">
                        <div class="col-md-12 tabcontentpadding">
                            <div class="panel panel-bottom">
                                <div class="panel-heading hidden">
                                    <ul class="nav panel-tabs-border panel-tabs panel-tabs-left">

                                        @{
                                            <li class="active">
                                                <a href="#tabMain" data-toggle="tab" aria-expanded="false">Main</a>
                                            </li>
                                            <script>
                                                $(document).ready(function () {

                                                    var selectedtab = "Main";
                                                    $("#tabname").text(selectedtab);

                                                    $("#btnMain").click(function () {
                                                        //Hiding selected tab from 'more' tab list if any shown..S1
                                                        $('#btnShowSelectedTab').hide();
                                                        sessionStorage.removeItem('renderedrows');
                                                        $('#btnMain').addClass('btn btn-default active');
                                                        if ($("#hdPreTab").val() != 'btnMain') {

                                                            $('#' + $("#hdPreTab").val()).removeClass('btn btn-default active');
                                                            $('#' + $("#hdPreTab").val()).addClass('btn btn-default');
                                                            $("#hdPreTab").val('btnMain');
                                                            var formkey = '@Model.FormKey';
                                                            sessionStorage.setItem("CurrentTab" + '@Model.Table' + formkey, 'btnMain');

                                                            var svals = $("#hdtabinfocus").val().split(",");

                                                            showProgress();
                                                            var fielditems = [];
                                                            fielditems = GetAllFieldValues(true);
                                                            var objfields = JSON.stringify(fielditems);

                                                            if (svals && svals.indexOf("0") == -1) {
                                                                objfields = encodeURIComponent(objfields);
                                                                $.ajax({
                                                                    url: "/CreateForm/CreateFormHTML",
                                                                    data: {
                                                                        tabname: "Main", tabId: 0, FormKey: formkey, Fields: objfields, IsHeaderControlsLoad: false, FormHistoryKey : '@Model.FormHistoryKey'
                                                                    },
                                                                    type: 'post',
                                                                    cache: false,
                                                                    success: function (data) {
                                                                        $(".tab-pane").hide();

                                                                        hideProgress();
                                                                        $(".tab-content").append(data.TabData);

                                                                        $('.panel-tabs a[href="#tabMain"]').tab('show');
                                                                        $("#hdtabinfocus").val($("#hdtabinfocus").val() + "," + "0")
                                                                        selectedtab = $("li.active").text().replace(/ /g, '');
                                                                        selectedtab = selectedtab.trim();
                                                                        $("#tabname").text(selectedtab);
                                                                        $("#tab" + selectedtab).show();

                                                                        //set rowspan count for current tab..if it is greater than 20 then allow to load rest of rows..J
                                                                        sessionStorage.setItem("RowSpansCount" + '@Model.FormKey', data.RowSpanCount);

                                                                        CheckScrollBar(data.IsRowCountGreaterThanTopCount);

                                                                        //V_T 7/15 --set the scrool to top position
                                                                        $(window).scrollTop(0);

                                                                        if ('@Model.Table.ToUpper()' == 'FIND') {
                                                                            var tabName = $.trim(selectedtab);
                                                                            if ($("#tab" + tabName + " input:first").length > 0)
                                                                            {
                                                                                $("#tab" + tabName + " input:first").focus();
                                                                            }

                                                                        }

                                                                    },
                                                                    error: function (data) {

                                                                        hideProgress();
                                                                        if (data.responseText.length > 0 || data.responseText != undefined || data.responseText != null) {
                                                                            //alert(data);
                                                                        }
                                                                    }
                                                                })
                                                            }
                                                            else {
                                                                UpdateTabPositionInSession('0', 'Main', objfields);
                                                                $(".tab-pane").hide();
                                                                $('.panel-tabs a[href="#tabMain"]').tab('show');
                                                                selectedtab = $("li.active").text().replace(/ /g, '');
                                                                selectedtab = selectedtab.trim();
                                                                $("#tabname").text(selectedtab);
                                                                $("#tab" + selectedtab).show();

                                                                if ('@Model.Table.ToUpper()' == 'FIND') {
                                                                    var tabName = $.trim(selectedtab);
                                                                    if ($("#tab" + tabName + " input:first").length > 0) {
                                                                        $("#tab" + tabName + " input:first").focus();
                                                                    }
                                                                }

                                                                hideProgress();

                                                                //V_T 7/15 --set the scrool to top position
                                                                $(window).scrollTop(0);
                                                            }
                                                        }
                                                    });
                                                });
                                            </script>

                                            for (int _count = 0; _count < Model.lstTabsorderlist.Count; _count++)
                                            {

                                                <li class="">
                                                    <a href="@("#tab" + Model.lstTabsorderlist[_count].TabName.Replace(" ", ""))" data-toggle="tab" aria-expanded="false">@Model.lstTabsorderlist[_count].TabName</a>
                                                </li>
                                                <script>
                                                    $(document).ready(function () {
                                                        $("@("#btn" + Model.lstTabsorderlist[_count].TabName.Replace(" ", ""))").click(function () {

                                                            showProgress();

                                                            sessionStorage.removeItem('renderedrows');
                                                            var tabposition = "@Model.lstTabsorderlist[_count].TabPosition";
                                                            var tabname = "@Model.lstTabsorderlist[_count].TabName";
                                                            var formkey = '@Model.FormKey';

                                                            var MoreTabIndex = "@Model.ShowDefaultNoofTabs";
                                                            //Added new tab to show selected tab from 'more' tabs list..S1

                                                            //tabposition > 4

                                                            if (Number(tabposition) > Number(MoreTabIndex)) {
                                                                var iconName = (tabname.toLowerCase() == 'properties') ? 'fa-user' : 'fa-pencil';
                                                                $('#btnShowSelectedTab').show();
                                                                $('#btnShowSelectedTab').html("<i class='fa " + iconName + "'></i>&nbsp;&nbsp;" + tabname);
                                                                $('#btnShowSelectedTab').addClass('active');
                                                            }
                                                            else {
                                                                $('#btnShowSelectedTab').hide();
                                                            }

                                                            $("#btn" + tabname.replace(/ /g, '')).addClass('btn btn-default active');
                                                            if ($("#hdPreTab").val() != "btn" + tabname.replace(/ /g, '')) {
                                                                $('#' + $("#hdPreTab").val()).removeClass('btn btn-default active');
                                                                $('#' + $("#hdPreTab").val()).addClass('btn btn-default');
                                                                $("#hdPreTab").val('btn' + tabname.replace(/ /g, ''));
                                                                sessionStorage.setItem("CurrentTab" + '@Model.Table' + formkey, $("#hdPreTab").val());
                                                                var svals = $("#hdtabinfocus").val().split(",");

                                                                var fielditems = [];
                                                                fielditems = GetAllFieldValues(true);
                                                                var objfields = JSON.stringify(fielditems);

                                                                if (svals && svals.indexOf(tabposition) == -1) {
                                                                    objfields = encodeURIComponent(objfields);
                                                                    $.ajax({
                                                                        url: "/CreateForm/CreateFormHTML",
                                                                        data: {
                                                                            tabId: -1,
                                                                            tabname: tabname,
                                                                            FormKey: formkey,
                                                                            Fields: objfields,
                                                                            IsHeaderControlsLoad: false,
                                                                            FormHistoryKey : '@Model.FormHistoryKey'
                                                                        },
                                                                        cache: false,
                                                                        type: 'post',
                                                                        success: function (data) {
                                                                            $(".tab-pane").hide();
                                                                            $(".tab-content").append(data.TabData);

                                                                            $('.panel-tabs a[href="@("#tab" + Model.lstTabsorderlist[_count].TabName.Replace(" ", ""))"]').tab('show');
                                                                            $("#hdtabinfocus").val($("#hdtabinfocus").val() + "," + tabposition)
                                                                            selectedtab = $("li.active").text().replace(/ /g, '');
                                                                            selectedtab = selectedtab.trim();
                                                                            $("#tabname").text(selectedtab);
                                                                            hideProgress();

                                                                            //V_T 7/15 --set the scrool to top position
                                                                            $(window).scrollTop(0);

                                                                            $("#tab" + selectedtab).show();

                                                                            if ($("#MMO_NOTES").data("kendoEditor")) {
                                                                                $("#MMO_NOTES").data("kendoEditor").focus();
                                                                            }
                                                                            else if ($("#MMO_NOTE").data("kendoEditor")) {
                                                                                $("#MMO_NOTE").data("kendoEditor").focus();
                                                                            }

                                                                            //set rowspan count for current tab..if it is greater than 20 then allow to load rest of rows..J
                                                                            sessionStorage.setItem("RowSpansCount" + '@Model.FormKey', data.RowSpanCount);

                                                                            CheckScrollBar(data.IsRowCountGreaterThanTopCount);

                                                                            if ('@Model.Table.ToUpper()' == 'FIND') {
                                                                                var tabName = $.trim(selectedtab);
                                                                                if ($("#tab" + tabName + " input:first").length > 0) {
                                                                                    $("#tab" + tabName + " input:first").focus();
                                                                                }
                                                                            }
                                                                        },
                                                                        error: function (request, status, error) {
                                                                            // alert('error');
                                                                            if (request.responseText.length > 0 || request.responseText != undefined || request.responseText != null) {
                                                                                // alert(data.responseText);
                                                                            }
                                                                        }
                                                                    })
                                                                }
                                                                else {
                                                                    $(".tab-pane").hide();
                                                                    $('.panel-tabs a[href="@("#tab" + Model.lstTabsorderlist[_count].TabName.Replace(" ", ""))"]').tab('show');
                                                                    UpdateTabPositionInSession(tabposition, '@Model.lstTabsorderlist[_count].TabName.Replace(" ", "")', objfields);


                                                                    selectedtab = $("li.active").text().replace(/ /g, '');
                                                                    selectedtab = selectedtab.trim();
                                                                    $("#tabname").text(selectedtab);
                                                                    hideProgress();

                                                                    //V_T 7/15 --set the scrool to top position
                                                                    $(window).scrollTop(0);


                                                                    $("#tab" + selectedtab).show();
                                                                    if ($("#MMO_NOTES").data("kendoEditor")) {
                                                                        $("#MMO_NOTES").data("kendoEditor").focus();
                                                                    }
                                                                    else if ($("#MMO_NOTE").data("kendoEditor")) {
                                                                        $("#MMO_NOTE").data("kendoEditor").focus();
                                                                    }

                                                                    if ('@Model.Table.ToUpper()' == 'FIND') {
                                                                        var tabName = $.trim(selectedtab);
                                                                        if ($("#tab" + tabName + " input:first").length > 0) {
                                                                            $("#tab" + tabName + " input:first").focus();
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                            else {
                                                                hideProgress();
                                                            }
                                                        });
                                                    });
                                                </script>
                                            }

                                        }
                                    </ul>
                                </div>

                                <div class="panel-body" style="border:none;">
                                    <div class="tab-content pn br-n">

                                    </div>
                                </div>
                                <input type="hidden" id="hdtabinfocus" value="" />
                            </div>


                            <div class="row hidden-sm hidden-md hidden-lg">
                                <div class="col-md-12">
                                    <div class="panel panel-visible" id="spy2" style="margin-bottom: 5px!important; background-color: #eeeeee;display:none;">

                                        <button type="button" class="btn btn-sm btn-primary" title="New Company" onclick="location.href = 'form.html';"><i class="fa fa-plus"></i></button>
                                        <div class="btn-group" title="Create Linked">
                                            <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-toggle="dropdown">
                                                <i class="fa fa-link"></i><span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu pull-right" role="menu">
                                                <li>
                                                    <a href="#"><i class="fa fa-pencil-square-o"></i>&nbsp;&nbsp;Activity</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-pencil-square-o"></i>&nbsp;&nbsp;Lead</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-calendar"></i>&nbsp;&nbsp;Appointment</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-list-ul"></i>&nbsp;&nbsp;To Do</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-lightbulb-o"></i>&nbsp;&nbsp;Opportunity</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-sitemap"></i>&nbsp;&nbsp;Project</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-quote-left"></i>&nbsp;&nbsp;Quote</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="btn-group" title="View">
                                            <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-toggle="dropdown">
                                                <i class="glyphicon glyphicon-list"></i><span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu pull-right" role="menu">
                                                <li>
                                                    <a href="#"><i class="fa fa-print"></i>&nbsp;&nbsp;Print</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-envelope-o"></i>&nbsp;&nbsp;Send by Email...</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-file-excel-o"></i>&nbsp;&nbsp;Send to Excel...</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-file-pdf-o"></i>&nbsp;&nbsp;Send to PDF...</a>
                                                </li>
                                                <li class="divider"></li>
                                                <li>
                                                    <a href="#"><i class="fa fa-cog"></i>&nbsp;&nbsp;Properties</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-filter"></i>&nbsp;&nbsp;Filter</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-sort"></i>&nbsp;&nbsp;Sort</a>
                                                </li>
                                                <li class="divider"></li>
                                                <li>
                                                    <a href="#"><i class="fa fa-filter"></i>&nbsp;&nbsp;Reset Filter</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-sort"></i>&nbsp;&nbsp;Reset Sort</a>
                                                </li>
                                                <li class="divider"></li>
                                                <li id="liMeta" class="metaHide">
                                                    <a href="#"><i class="fa fa-wrench"></i>&nbsp;&nbsp;Metadata</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="btn-group hidden" title="Desktop">
                                            <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-toggle="dropdown">
                                                <i class="glyphicon glyphicon-th-large"></i><span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu pull-right" role="menu">
                                                <li>
                                                    <a href="#"><i class="fa fa-print"></i>&nbsp;&nbsp;Print</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-envelope-o"></i>&nbsp;&nbsp;Send by Email...</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-file-excel-o"></i>&nbsp;&nbsp;Send to Excel...</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-file-pdf-o"></i>&nbsp;&nbsp;Send to PDF...</a>
                                                </li>
                                                <li class="divider"></li>
                                                <li class="metaHide">
                                                    <a href="#"><i class="fa fa-wrench"></i>&nbsp;&nbsp;Metadata</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="btn-group" title="Selected">
                                            <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">
                                                <i class="fa fa-check-square-o"></i><span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu pull-right" role="menu">
                                                <li>
                                                    <a href="#"><i class="fa fa-check-square"></i>&nbsp;&nbsp;Mark Reviewed...</a>
                                                </li>
                                                <li>
                                                    <a href="#"><i class="fa fa-check-square"></i>&nbsp;&nbsp;Assign to Group...</a>
                                                </li>
                                            </ul>
                                        </div>

                                        <button type="button" class="btn btn-sm btn-default hidden-xs" title="Maximize"><i class="fa fa-external-link"></i></button>
                                        <button type="button" class="btn btn-sm btn-primary hidden-xs" title="Help"><i class="fa fa-question"></i></button>

                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
    <!-- End: Content -->
    <!-- Begin: popout -->
    <!-- Modal -->
    @*<div id="AP_Recurrence_Model" class="modal fade" role="dialog" style="left: 20%; right: 20%; padding-top:7%; padding-right:0px !important;" aria-hidden="true" data-backdrop="static" data-keyboard="false">*@
    @*<div class="modal-dialog" style="margin: 0px !important;width:100%">*@
    <div id="AP_Recurrence_Model" class="modal fade" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog" style="margin-left: 20% !important;width:60%;margin-top:7%;">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header" style="padding: 0px;margin-left: 15px;">
                    <label style="font-size:18px;color:#666666;"><b>Appointment Recurrence</b></label>
                    @*<button type="button" id="AP_Model_Close" class="close" data-dismiss="modal" style="margin-right: 10px;margin-top: 5px;">&times;</button>*@
                </div>
                <div id="viewContentRec" class="modal-body" style="max-height:650px;padding-top: 1px;">
                    @*@Html.Partial("~/Views/CreateForm/APRecurrencePartial.cshtml", Model)*@
                </div>
            </div>
        </div>
    </div>

    @*Quote Send Mail Modal*@
    <div class="modal fade" id="SendMailModal" role="dialog">
        <div class="modal-dialog" style="margin-left:18%;width:80%;">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <label style="font-size:17px;font-weight:500">Send Mail</label>
                </div>
                <div class="modal-body" style="overflow-x: scroll; overflow-y: scroll; height:80%;max-height:650px;">
                    <fieldset class="row" style="margin-top:0px;">
                        <div class="row" style="margin-top:0px;">
                            <div class="col-md-12" style="float:right">
                                <button type="button" style="margin:3px 0px 3px 0px;" class="btn btn-sm btn-success" title="Send" id="btnSend" onclick="SendMail()"><i class="fa fa-share"></i> Send</button>
                                <button type="button" style="margin:3px 0px 3px 0px;" class="btn btn-sm btn-danger" data-dismiss="modal" id="btnCancel" title="Cancel"><i class="fa fa-times"> Cancel</i></button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-2">
                                <label>From:</label>
                            </div>
                            <div class="col-md-10">
                                <input type="text" id="SND_From" class="form-control input-sm" width="500" />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-2"><label>To:</label></div>
                            <div class="col-md-10"><input type="text" class="form-control input-sm" id="SND_To" width="500" /></div>
                        </div>
                        <div class="row">
                            <div class="col-md-2"><label>Cc:</label></div>
                            <div class="col-md-3"><input type="text" class="form-control input-sm" id="SND_Cc" width="250" /></div>
                            <div class="col-md-2"><label>Bcc:</label></div>
                            <div class="col-md-3"><input type="text" class="form-control input-sm" id="SND_Bcc" width="250" /></div>
                        </div>
                        <div class="row">
                            <div class="col-md-2"><label>Subject:</label></div>
                            <div class="col-md-10"><input type="text" class="form-control input-sm" id="SND_Subject" width="250" /></div>
                        </div>
                        <div class="row">
                            <div class="col-md-2"><label>Body:</label></div>
                            <div class="col-md-10">
                                @(Html.Kendo().Editor()
                                     .ImageBrowser(imageBrowser => imageBrowser
                                     .Image(Selltis.Core.Util.GetPathForImageBrowser() + "{0}")
                                     .Read("Read", "ImageBrowser")
                                     .Create("Create", "ImageBrowser")
                                     .Destroy("Destroy", "ImageBrowser")
                                     .Upload("Upload", "ImageBrowser")
                                     .Thumbnail("Thumbnail", "ImageBrowser"))
                                     .Name("SND_Body")
                                     .Tools(tools => tools.Clear()      //remove all tools
                                     .Bold().Italic().Underline().Strikethrough()
                                     .JustifyLeft().JustifyCenter().JustifyRight().JustifyFull()
                                     .InsertUnorderedList().InsertOrderedList()
                                     .Outdent().Indent()
                                     .SubScript()
                                     .SuperScript()
                                     .Formatting()
                                     .CleanFormatting()
                                     .FontName()
                                     .FontSize()
                                     .FontColor().BackColor()
                                 )
                                      .HtmlAttributes(new { @readonly = "readonly", @class = "form-control", title = "Body" })
                                      .Encode(false)
                              )
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-2"><label>Attachments:</label></div>
                            <div class="col-md-10" id="SND_Files"></div>
                        </div>
                        <div>
                            <input hidden="hidden" id="hdnViewName" type="text" value="" />
                            <input hidden="hidden" id="hdnFileName" type="text" value="" />
                            <input hidden="hidden" id="hdnFieldName" type="text" value="" />
                            <input hidden="hidden" id="hdnGidId" type="text" value="" />
                        </div>
                    </fieldset>
                </div>
                <div class="modal-footer">

                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="myModal" role="dialog">
        <div class="modal-dialog" style="margin-left:18%;width:80%;">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body" style="overflow-x: scroll; overflow-y: scroll; height:100%;max-height:650px;">
                    <p>Some text in the modal.</p>
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="LNKRelatedModal" role="dialog" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog" style="margin-left:28%;width:45%;margin-top:8%;">
            <!-- Modal content-->
            <div class="modal-content" style="min-height: 445px;box-shadow:none">
                <div class="modal-header" style="padding:2px;padding-left:10px;">
                    <label style="font-size:17px;font-weight:500" id="lnkRelatedHeader"></label>
                    <button type="button" class="close" onclick="clearSession(this)" style="padding-top:8px;padding-right:5px;">&times;</button>
                </div>
                <div class="modal-body" style="height:100%;max-height:650px;padding:0px;">
                    <div id="modalToolBar" class="row" style="min-height: 75px; margin-top: 0px; margin-right: 0px;">

                        <div class="topbar-left row" style="margin-top: 0px; margin-right: 0px;">
                            <div class="col-md-12" style="padding-right:0px;">
                                <div class="row" style="margin:0px;">
                                    <div class="col-md-12" style="padding-right:0px;">
                                        <div class="panel-heading" style="background: #e9e9e9 !important; line-height: 0px; padding-bottom: 3px;">
                                            <div class="row" style="padding-right:0px;">
                                                <div class="panel-title" style="margin-bottom:3px;padding-left:0px;line-height:0px !important;">
                                                    <div class="col-md-12">

                                                        <button type="button" style="margin-bottom:3px;" class="btn btn-sm btn-success" onclick="btn_SaveAndExit(this)" title="Save and exit" id="btnSave"><i class="glyphicon glyphicon-floppy-disk"></i></button>
                                                        <button type="button" style="margin-bottom:3px;" class="btn btn-sm btn-danger" onclick="clearSession(this)" id="btnCancel" title="Exit without saving"><i class="fa fa-times"></i></button>
                                                        <button type="button" style="margin-bottom:3px;" title="Create new" class="btn btn-sm btn-primary" onclick="CreateNew(this)"><i class="fa fa-plus"></i></button>

                                                        <button type="button" style="margin-bottom:3px;" class=" btn btn-sm btn-primary" id="btnSelectAll" title="Select all" onclick="btn_SelectAll(this)"><i class="fa fa-check-square-o"></i></button>
                                                        <button type="button" style="margin-bottom:3px;" class="btn btn-sm btn-primary" id="btnDeselectAll" title="Deselect all" onclick="btn_DeselectAll(this)"><i class="glyphicon glyphicon-unchecked"></i></button>
                                                        <button type="button" style="margin-bottom:3px;" class="btn btn-sm btn-primary" id="btnSelectedOnly" onclick="btn_SelectedOnly(this)" title="Selected records only"><i class="fa fa-list-ul"></i></button>
                                                        <button type="button" style="margin-bottom:3px;" class="btn btn-sm btn-primary" id="btnAllRecords" onclick="btn_AllRecords(this)" style="display:none" title="Show all records"><i class="glyphicon glyphicon-list-alt"></i></button>
                                                        <input type="checkbox" id="btn_Filter" title="Filter" onclick="btn_FilterChange(this)" />&nbsp;
                                                        <i class="fa fa-filter" style="min-width:0px;" title="Filter"></i>
                                                        <button type="button" style="margin-bottom:3px;" id="btn_Edit" onclick="btn_LNKFilter(this)" class="btn btn-sm btn-primary"><i class="fa fa-pencil-square-o"></i></button>   @*<i class="fa fa-pencil-square-o"></i>*@
                                                        <button type="button" style="margin-bottom:3px;" title="Count total number of recs in the linkbox selector" onclick="getCount(this)" class="btn btn-sm btn-primary">Count</button>
                                                        &nbsp;
                                                        <label id="lblLNKGridCount" style="color: #666666;display:inline !important" class="CountLabel"></label>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" style="margin-top: 3px; margin-right: 0px;">
                                    <div class="col-md-12" style="padding-right: 0px;">
                                        <div style="margin-top:0px;">
                                            <div class="panel-title" style="margin-bottom:3px;padding-right:5px;line-height:0px !important;">

                                                @*<div class="col-md-6" style="margin-top:3px;margin-bottom:3px;">*@
                                                @*<div class="col-md-12" style="padding-right: 0px;">*@
                                                <div class="col-md-4 col-sm-4 col-xs-4">
                                                    <input id="txtSelectFilter" style="margin-bottom:2px;width:100% !important;padding-left:5px !important;" type="text" width="50" autofocus />
                                                </div>

                                                <button id="btn_Select" style="margin-bottom:3px;" class="btn btn-xs btn-primary" onclick="btnSelectFilter(this)">Search</button>

                                                @*</div>*@
                                                @*</div>
                                                    <div class="col-md-6" style="float:right;">*@
                                                @*<div class="col-md-4 col-sm-4 col-xs-4">*@
                                                <button type="button" style="margin-bottom:3px;" class="btn btn-sm btn-primary" disabled id="GridNavFirst" title="First" onclick="Navigation('first',this)"><i class="fa fa-angle-double-left"></i></button>
                                                <button type="button" style="margin-bottom:3px;" class="btn btn-sm btn-primary" disabled id="GridNavPrevious" title="Previous" onclick="Navigation('previous', this)"><i class="fa fa-angle-left"></i></button>
                                                <button type="button" style="margin-bottom:3px;" class="btn btn-sm btn-primary" id="GridNavNext" title="Next" onclick="Navigation('next', this)"><i class=" fa fa-angle-right"></i></button>
                                                <button type="button" style="margin-bottom:3px;" class="btn btn-sm btn-primary" id="GridNavLast" title="Last" onclick="Navigation('last', this)"><i class=" fa fa-angle-double-right"></i></button>
                                                @*</div>*@
                                                @*</div>*@
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @*</div>*@
                    </div>
                    @*<hr style="border-top: 1px solid #fff;"/>*@
                    <div class="grid-content" style="margin: 5px;overflow-y:scroll;overflow-x:hidden;height:350px"></div>
                </div>
                <div class="modal-footer" style="display:none">
                </div>
            </div>
        </div>
    </div>
    <!-- End: popout -->
    <!--Quick Edit Pop up starts here-->
    <div class="modal fade" id="QuickAddEditModelDialog" role="dialog" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog" style="width: 30%;height: 75%;top: 8%;position: relative;">
            <!-- Modal content-->
            <div class="modal-content" style="min-height: 445px;box-shadow:none">
                <div id="modalToolBar" class="row" style="margin-top: 0px; margin-right: 0px;">

                    <div class="topbar-left row" style="margin-top: 0px; margin-right: 0px;">
                        <div class="col-md-12" style="padding-right:0px;">
                            <div class="row" style="margin:0px;">
                                <div class="col-md-12" style="padding-right:0px;">
                                    <div class="panel-heading" style="background: #e9e9e9 !important; line-height: 9px; padding-bottom: 3px;">
                                        <div class="row" style="padding-right:0px;">
                                            <div class="panel-title" style="margin-top: 3px;margin-bottom:3px;padding-left:0px;line-height:9px !important;">
                                                <div class="col-md-12">
                                                    <div class="col-md-8 col-sm-4" style="text-overflow: ellipsis;white-space: nowrap;margin-top: 6px;font-size: 15px;overflow: hidden;" title="Quick Add / Edit">
                                                        <i class="glyphicon glyphicon-th-large"></i>&nbsp;&nbsp;<label style="font-size:15px;font-weight:600" id="lnkTitle"> Quick Add / Edit</label>
                                                    </div>
                                                    <div class="col-md-4 col-sm-6">
                                                        <div class="panel-heading-btn" style="float: right;">
                                                            <button type="button" class="btn btn-sm btn-success" onclick="btn_SaveAndExitFromQuickAddEdit(this)" title="Save and exit" id="btnSave"><i class="glyphicon glyphicon-floppy-disk"></i></button>
                                                            <button type="button" class="btn btn-sm btn-danger" onclick="CancelQuickAddEditPopUp(this)" id="btnCancel" title="Exit without saving"><i class="fa fa-times"></i></button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 3px; margin-right: 0px;">
                                <div class="col-md-12" style="padding-right: 0px;">
                                    <div style="margin-top:0px;">

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-body" style="height:100%;max-height:650px;padding:0px;overflow-y: auto;">

                    @Html.Hidden("hdnLNK_FieldName")

                    <div class="row" id="pnlErrorForQuickEdit" style="display: none; background-color: #FFFFD0; border-color: Gray; border-width: 1px; border-style: Solid; min-height: 22px; width: 100%; border-radius: 5px; padding: 2px 3px; ">
                        <div class="col-md-12">

                            <div class="col-md-12">
                                <span><i class="fa fa-exclamation-triangle" aria-hidden="true"></i></span>
                                <span id="lblErrorForQuickEdit" style="color:Black;font-family:Verdana,Arial,Helvetica;font-size:8pt;">
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="pnlMessagePanelForQuickEdit" style="display: none; background-color: #FFFFD0; border-color: Gray; border-width: 1px; border-style: Solid; min-height: 22px; width: 100%; border-radius: 5px; padding: 2px 3px; ">
                        <div class="col-md-12">

                            <div class="col-md-12">
                                <span><i class="fa fa-exclamation-triangle" aria-hidden="true"></i></span>
                                <span id="lblMessagePanelForQuickEdit" style="color:Black;font-family:Verdana,Arial,Helvetica;font-size:8pt;">
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="PNL_MessageBoxForQuickEdit" style="display: none;">
                        <div class="col-md-2">

                        </div>
                        <div class="col-md-8" style="margin-top: -5px;">
                            <div class="row" style="background-color: #5b97cb;border-radius: 10px;height: 28px;">
                                <label style="margin-left: 10px;color: white;font-weight:bold;margin-top: -2px;" id="LBL_MsgBoxTitleForQuickEdit">LBL_MsgBoxTitle</label>
                                <label id="IMG_MsgBoxCloseForQuickEdit" style="cursor: pointer;margin-top: -2px;margin-right: 10px;float:right;color: white;"><b>X</b></label>
                            </div>
                            <div class="row" style="margin-top: -5px;background-color: #FFFFB0;border-radius: 0px 0px 10px 10px;">
                                <div class="row" style="margin-left:10px;margin-right:10px;font-size: 12px;">
                                    <label id="LBL_MsgBoxMessageForQuickEdit" style="margin-top: -5px;">LBL_MsgBoxMessageForQuickEdit</label>
                                </div>
                                <div class="row" style="margin-left:10px;margin-right:10px;margin-top: -5px;">
                                    <div class="col-md-12">
                                        <textarea id="TXT_MsgBoxInputForQuickEdit" name="TXT_MsgBoxInputForQuickEdit" hidden="hidden" style="width: 100%; height: 50px; font-size: 13px;">TXT_MsgBoxInputForQuickEdit</textarea>
                                    </div>
                                    <div id="divTXT_MsgBoxInputRichForQuickEdit" style="display:none">
                                        @(Html.Kendo().Editor().Name("TXT_MsgBoxInputRichForQuickEdit")
                                            .HtmlAttributes(new { @class = "form-control", title = "", style = "width:100%;height:50px;font-size :13px;" })
                                            .Value("")
                                        )
                                    </div>
                                </div>
                                <div class="row" style="margin-left:10px;margin-right:10px;margin-top: 0px;margin-bottom: 5px;">
                                    <br />
                                    <div align="center">
                                        <input type="button" class="btn btn-xs btn-primary" name="BTN_MsgBox1" id="BTN_MsgBox1" value="Yes" hidden="hidden" />
                                        <input type="button" class="btn btn-xs btn-primary" name="BTN_MsgBox2" id="BTN_MsgBox2" value="No" hidden="hidden" />
                                        <input type="button" class="btn btn-xs btn-primary" name="BTN_MsgBox3" id="BTN_MsgBox3" value="Cancel" hidden="hidden" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-2">

                        </div>
                    </div>

                    <div id="QuickAddEditPopUpContent" style="margin: 5px; ">

                    </div>
                </div>
                <div class="modal-footer" style="display:none">
                </div>
            </div>
        </div>
    </div>
    <!--Quick Edit Pop up ends here-->
</section>

<!-- Form Metadata popup window when click on Show metadata..J-->
<div id="ModalFormMetaData" class="modal fade" role="dialog">
    <div class="modal-dialog" style="margin-left: 30%; width: 48%; ">
        <!-- Modal content-->
        <div class="modal-content" style="margin-top:70px;">
            <div class="modal-header">
                <label style="font-size:15px;" id="LBL_Title">Metadata Details</label>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body" style="overflow-x: scroll; overflow-y: scroll; height:100%;max-height:650px;">

                <!-- Message Box For MetaData Display starts here-->
                <div class="row" id="PNL_MessageBox_ForFormMeta" style="display: none;padding-top:10px;">

                    <div class="col-md-12" style="background-color:#FFFFB0;">

                        <table cellpadding="0" cellspacing="0" style="border-right: silver 0px;border-radius:5px; border-top: silver 0px; overflow: hidden; border-left: silver 0px; border-bottom: silver 0px; height: 8px;width:100%">
                            <tr style="background-color: #5b97cb">
                                <td style="height: 21px; width: 98%;">
                                    <table cellpadding="0" cellspacing="0" style="width: 100%">
                                        <tr>
                                            <td style="width: 90%; height: 17px;">
                                                <label id="LBL_MsgBoxTitle_ForMeta"
                                                       style="background-color:#5B97CB;border-color:#5B97CB;border-style:Solid;border-width:2px;margin:0px;
                                                        font-weight:bold;font-family:Open Sans, Helvetica, Arial, sans-serif;font-size:13px;color:white;width:100%;">
                                                    LBL_MsgBoxTitle_ForMeta
                                                </label>
                                            </td>
                                            <td align="right" style="width: 10%; height: 17px; vertical-align: top;" valign="top">
                                                <img id="IMG_MsgBoxCloseForMeta" src="~/Content/Images/CloseWhiteNoBorder.gif" style="cursor:pointer; border-width:0px;" />
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" valign="top" style="width: 98%; vertical-align: top; padding: 4px 4px 4px 4px;">
                                    <label id="LBL_MsgBoxMessage_ForMeta"
                                           style="font-family:Open Sans, Helvetica, Arial, sans-serif;font-size:12px;">
                                        LBL_MsgBoxMessage_ForMeta
                                    </label>
                                </td>
                            </tr>

                            <tr>
                                <td align="center" style="height: 30px; width: 98%; vertical-align: middle; padding: 4px 4px 4px 4px;" valign="middle">
                                    <input type="button" name="BTN_MsgBox1" id="BTN_MsgBox1_ForMeta" value="Yes" hidden="hidden" style="border-radius:5px;" onclick="BTN_MsgBox1_ForMeta_Click()" />
                                    <input type="button" name="BTN_MsgBox2" id="BTN_MsgBox2_ForMeta" value="No" hidden="hidden" style="border-radius:5px;" onclick="BTN_MsgBox2_ForMeta_Click()" />
                                    <input type="button" name="BTN_MsgBox3" id="BTN_MsgBox3_ForMeta" value="Cancel" hidden="hidden" style="border-radius:5px;" onclick="BTN_MsgBox3_ForMeta_Click()" />
                                </td>
                            </tr>
                        </table>

                    </div>
                    <div class="col-md-2">

                    </div>
                </div>
                <br />


                <div class="row " style="padding-bottom:5px;">
                    <div class="col-md-1">
                        <label>Section</label>
                    </div>
                    <div class="col-md-6">
                        <input id="txtSection" name="Section" style="color:Black;background-color:White;font-family:Verdana,Arial,Helvetica;font-size:8pt;width:300px;" type="text" value="">
                    </div>
                </div>

                <div class="row" style="padding-bottom:8px;">
                    <div class="col-md-1">
                        <label>Page</label>
                    </div>
                    <div id="viewmetaid" class="col-md-5">
                        <input id="txtPage" name="Page" style="color:Black;background-color:White;font-family:Verdana,Arial,Helvetica;font-size:8pt;width:300px;" type="text" value="">
                    </div>
                    <div class="col-md-2">
                        <button class=" btn btn-xs btn-primary m-l-5" id="BTN_GenerateSUID" name="BTN_GenerateSUID" onclick="onBTN_GenerateSUIDClick();" type="button"> Insert Unique ID </button>
                    </div>
                </div>

                <div class="row" style="padding-bottom:4px;">
                    <img id="imgInfo2" src="~/Content/Images/info.gif" style="border-width:0px;">
                    <span id="lblTip" style="font-family:Verdana,Arial,Helvetica;font-size:8pt;">Changing Section or Page will create a new page (or edit that Section/Page if it already exists) and leave the existing page as is.</span>
                </div>

                <div class="row" style="padding-bottom:5px;">
                    <div id="ViewMetatext" class="col-md-12">
                        @Html.TextArea("txtFormMetadata", new { id = "txtFormMetadata", @class = "col-md-12", @style = "color:Black;background-color:White;font-family:Verdana,Arial,Helvetica;font-size:8pt;height:350px;width:634px;" })
                    </div>
                </div>
                <div class="row" style="margin-left:5px !important;margin-right:5px !important">
                    <div class="col-md-6">
                        <img id="CLK_INFO" src="~/Content/Images/info.gif" style="border-width: 0px;">
                        <span id="STA_MESSAGE" style="display:inline-block;font-family:Verdana,Arial,Helvetica;font-size:8pt;">Changes to this page will affect all workgroup users.</span>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-sm btn-primary" title="Cancel" id="btn__form_metadata_Cancel" style="float: right; margin-left: 10px;" data-dismiss="modal">Cancel</button>
                        <button class="btn btn-sm btn-primary" title="Save" id="btn_form_metadata_Save" style="float: right; margin-left: 10px;">Save</button>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<!-- Form Metadata popup window when click on Show metadata..J-->

<script type="text/javascript">

    $(document).ready(function () {

        $("#hdtabinfocus").val('');
        $("#hdPreTab").val('');

        var NavValue = "";
        var NavTableName = "";
        var NavRecordId = "";
        var NavViewKey = "";
        var NavMasterViewRecordId = "";
        var NavField = "";

        $.ajax({
            url: '/CreateForm/ClearFRM_formControlsSession',
            async: false,
            cache: false,
            success: function (data) {

            },
            error: function (data) {

            }
        })
        showProgress();
        var ready = true; //Assign the flag here

        $(window).scroll(function () {
            //VT 11/10 -- this function call is skippped to show all the rows by default in the page load
            return;
            //debugger
            //Check the flag here. Check it first, it's better performance wise.
            if (ready && Math.round($(window).scrollTop() + $(window).height()) >= $(document).height()) {

                var tabname = $("li.active").text().replace(/ /g, '').replace(/\n/g, '');

                // debugger
                //set rowspan count for current tab..if it is greater than 20 then allow to load rest of rows..J
                //var rowSpanCount = $('#tab' + tabname + '> div ').length;
                var rowSpanCount = sessionStorage.getItem("RowSpansCount" + '@Model.FormKey');
                var renderedrows = sessionStorage.getItem('renderedrows');
                if (rowSpanCount != null || rowSpanCount != undefined) {
                    if (rowSpanCount <= 20) {
                        return;
                    }

                }

                ready = false; //Set the flag here
                //$("#page-loader").show();
                showProgress();
                //var renderedrows = sessionStorage.getItem('renderedrows');
                if (renderedrows == null || renderedrows == undefined) {
                    renderedrows = 30;
                    sessionStorage.setItem('renderedrows', '30');
                }
                else {
                    renderedrows = parseInt(renderedrows) + 10;
                    sessionStorage.setItem('renderedrows', renderedrows);
                }

                $.ajax({
                    url: "/CreateForm/CreateFormHTML",
                    data: {
                        tabname: tabname,
                        tabId: -1,
                        FormKey: '@Model.FormKey',
                        itop: renderedrows,
                        IsHeaderControlsLoad: false,
                        FormHistoryKey: '@Model.FormHistoryKey'
                    },
                    type: 'post',
                    cache: false,
                    success: function (data) {
                        $(".tab-content").append(data.TabData);
                        ready = true;
                        //$("#page-loader").hide();
                        hideProgress();
                    },
                    error: function (data) {
                        //debugger
                        //$("#page-loader").hide();
                        hideProgress();
                        if (data.responseText.length > 0) {
                            //alert(data);
                        }
                    }
                })

            }
        });

        @*$(window).scroll(function () {
            //if ($(this).scrollTop() > 100) {

                var tabname = $("li.active").text().replace(/ /g, '').replace('\n', '');

                var renderedrows = sessionStorage.getItem('renderedrows');
                if (renderedrows == null || renderedrows == undefined)
                {
                    renderedrows = 30;
                    sessionStorage.setItem('renderedrows', '30');
                }
                else
                {
                    renderedrows = parseInt(renderedrows) + 10;
                    sessionStorage.setItem('renderedrows', renderedrows);
                }

                $.ajax({
                    url: "/CreateForm/CreateFormHTML",
                    data: {
                        tabname: tabname,
                        tabId: -1,
                        FormKey:'@Model.FormKey',
                        itop: renderedrows
                    },
                    type: 'post',
                    success: function (data) {
                        $(".tab-content").append(data);

                    },
                    error: function (data) {
                        //debugger
                        if (data.responseText.length > 0) {
                            //alert(data);
                        }
                    }
                })

           // }
        });*@

        //hide or show meta data
        var isMetaVisible = '@Util.GetSessionValue("MetaVisible")';
        if (isMetaVisible == "true") {
            $('.metaHide').show();
        }
        else {
            $('.metaHide').hide();
        }


        //When comes from manage form to get preview..J
        if (sessionStorage.getItem("IsFromPreview") != null && sessionStorage.getItem("IsFromPreview") == "true") {
            sessionStorage.setItem("IsFromPreview", null);

            $("#properties").hide();
            $("#btnSaveandClose").hide();
            $("#btngotopreview").hide();
            $("#btnCancel").hide();
            $("#btnSaveandleave").hide();
            $("#btnDelete").hide();
            $("#btnPrint").hide();
            $("#popout").hide();
            $("#btnToolbarHelp").hide();
            $("#divSharingStatus").hide();
            $("#divCreatLinked").hide();
            $("#First").hide();
            $("#Previous").hide();
            $("#Next").hide();
            $("#Last").hide();
            //$("#AutomatorToolbar").hide();
            sessionStorage.setItem("FromPreview", true);
            $("#btnBackToDesign").show();

        }
        else {
            sessionStorage.removeItem("FromPreview");
        }

        if ('@Model.gsForm.TableName' == null || '@Model.gsForm.TableName' == "") {
            $("#properties").hide();
            $("#btnSaveandClose").hide();
            $("#btngotopreview").hide();
            $("#btnCancel").show();
            $("#btnSaveandleave").hide();
            $("#btnDelete").hide();
            $("#btnPrint").hide();
            $("#popout").hide();
            $("#btnToolbarHelp").hide();
            $("#divSharingStatus").hide();
            $("#divCreatLinked").hide();
            $("#First").hide();
            $("#Previous").hide();
            $("#Next").hide();
            $("#Last").hide();
            //$("#AutomatorToolbar").show();
            $("#btnBackToDesign").hide();
            sessionStorage.removeItem("LastGridViewId");
        }
        if (('@Model.Mode' == 'MODIF') && ('@Model.gsForm.TableName' == 'CO' || '@Model.gsForm.TableName' == 'CN' || '@Model.gsForm.TableName' == 'OP')) {
            $("#btngotopreview").show();
        }
        else {
            $("#btngotopreview").hide();
        }
    });

    function formprint() {
        var formkey = '@Model.FormKey';
        window.open("/CreateForm/FormPrint?formkey=" + formkey + "&FormHistoryKey=" + '@Model.FormHistoryKey', "_blank", "top=100,left=100,width=1200, height=550,resizable=1,scrollbars=1");
    }

    function onpopout(el) {
        //  debugger;
        var attr = $("#spy2").attr('name');
        if (attr == "Fpopin") {
            $("#toggle_sidemenu_l").click();
            $("#spy2").removeAttr("name");
            $("#spy2").attr('name', 'Fpopout');
            $("#frmpopoutin").removeClass('fa-expand').addClass('fa-compress');
            $("#popout").attr('title', "Minimize");
        }

        else {
            $("#toggle_sidemenu_l").click();
            $("#spy2").removeAttr("name");
            $("#spy2").attr('name', 'Fpopin');
            $("#frmpopoutin").addClass('fa-expand').removeClass('fa-compress');
            $("#popout").attr('title', "Maximize");

        }
    }
    function UpdateTabPositionInSession(tabposition, tabname, objfields) {
        objfields = encodeURIComponent(objfields);
        $.ajax({
            url: '/CreateForm/UpdateTabPositionIn_FormSession',
            async: false,
            cache: false,
            type: 'post',
            data: { TabPosition: tabposition, TabName: tabname, FormKey: '@Model.FormKey', Fields: objfields, FormHistoryKey: '@Model.FormHistoryKey' },
            success: function (data) {
                if (data != "") {
                    //set rowspan count for current tab..if it is greater than 20 then allow to load rest of rows..J
                    sessionStorage.setItem("RowSpansCount" + '@Model.FormKey', parseInt(data));
                }


            },
            error: function (data) {
                alert(data.responseText);
            }
        })
    }

    $(document).ready(function () {
        "use strict";

        if ('@Model.Mode' == "NEW") {
            $(".ArrowOperations").prop('disabled', true);
            $("#btnCreateLinked").prop('disabled', true);
            $('#btnDelete').prop('disabled', true);
        }
        if ('@Model.gsForm.MessageBoxDisplay' == 'True') {
            DisplayMessageBox(@(Html.Raw(new JavaScriptSerializer().Serialize(Model.gsForm.FormMessageBox))));
        }

        if ('@Model.IsSaveButtonDisabled' == 'True') {
            $('#btnSaveandClose').prop('disabled', true);
            $('#btnSaveandleave').prop('disabled', true);
        }
        else {
            $('#btnSaveandClose').prop('disabled', false);
            $('#btnSaveandleave').prop('disabled', false);
        }
        sessionStorage.removeItem('renderedrows');

        $(document).keypress(function (e) {
            if (e.keyCode == 13) {
                if ('@Model.Table.ToUpper()' == 'FIND') {
                    //debugger;
                    var selectedTAB = $("li.active").text().replace(/ /g, '');
                    selectedTAB = selectedTAB.trim();
                    switch (selectedTAB.toLowerCase()) {
                        case 'activity':
                            $('#BTN_ACSEARCH').click();
                            break;
                        case 'appointment':
                            $('#BTN_APSEARCH').click();
                            break;
                        case 'company':
                            $('#BTN_COSEARCH').click();
                            break;
                        case 'contact':
                            $('#BTN_CNSEARCH').click();
                            break;
                        case 'machine':
                            $('#BTN_MASEARCH').click();
                            break;
                        case 'message':
                            $('#BTN_MSSEARCH').click();
                            break;
                        case 'model':
                            $('#BTN_MOSEARCH').click();
                            break;
                        case 'opportunity':
                            $('#BTN_OPSEARCH').click();
                            break;
                        case 'project':
                            $('#BTN_PRSEARCH').click();
                            break;
                        case 'quote':
                            $('#BTN_QTSEARCH').click();
                            break;
                        case 'todo':
                            $('#BTN_TDSEARCH').click();
                            break;
                        case 'ppractivity':
                            $('#BTN_PPRSEARCH').click();        //LabConco
                            break;
                        case 'quoteline':
                            $('#BTN_QLSEARCH').click();         //FlowMaster
                            break;
                        default:
                            return false;
                            break;
                    }

                }
            }
        });
    });
</script>
<script>

    $(document).ready(function () {
        var height = 600;
        if ($('#secondtoolbar').length > 0) {
            height = height - 30;
        }
        $("#pnlError").hide();

        var selectedtab = "";
        var tabposition = "";
        var formkey = '@Model.FormKey';
        var IsLnkPopup = sessionStorage.getItem("IsLnkPopup" + formkey);
        var fromnew = sessionStorage.getItem("FromLNK_PopUp_CreateNew" + formkey);
        var FromLNKGridDblClick = sessionStorage.getItem("FromLNKGridDblClick" + formkey);
        var IsFromLNKFilter = sessionStorage.getItem("IsFromLNKFilter");
        if (IsLnkPopup == 'YES' || FromLNKGridDblClick == 'YES' || fromnew == 'YES' || IsFromLNKFilter == 'YES') {
            sessionStorage.removeItem("FromLNKGridDblClick" + formkey);
            var currentTab = sessionStorage.getItem("CurrentTab" + '@Model.Table' + formkey);
            if (currentTab != '' && currentTab != null) {

                showProgress();
                if (currentTab != 'btnMain') {
                    $('#btnMain').removeClass('btn btn-default active');
                    $('#btnMain').addClass('btn btn-default');
                }
                if (currentTab == 'btnMain') {

                    var LNKVal1 = sessionStorage.getItem("LNKItem1" + formkey);
                    if (LNKVal1 == "Yes") {
                        svals = "[]";
                    }
                    var formkey = '@Model.FormKey';
                    //Showing Main on load when btnMain selected.. S1
                    $("#hdPreTab").val('btnMain');
                    $('#' + $("#hdPreTab").val()).removeClass('btn btn-default');
                    $('#' + $("#hdPreTab").val()).addClass('btn btn-default active');

                    $.ajax({
                        url: "/CreateForm/CreateFormHTML",
                        data: {
                            tabname: "Main",
                            tabId: 0,
                            FormKey: formkey,
                            IsHeaderControlsLoad: true,
                            FormHistoryKey: '@Model.FormHistoryKey'
                        },
                        cache: false,
                        type: 'post',
                        success: function (data) {
                            $(".tab-content").append(data.TabData);

                            if (data.HeaderControlsCount > 0) {
                                $("#HeaderControlsRow").show();

                                $("#HeaderPart").append(data.HeaderHtml);

                                ManageRowStyle(true);
                            }
                            else {
                                $("#HeaderControlsRow").hide();

                                ManageRowStyle(false);

                            }

                            $('.panel-tabs a[href="#tabMain"]').tab('show');
                            $("#hdtabinfocus").val($("#hdtabinfocus").val() + "," + "0")
                            selectedtab = $("li.active").text().replace(/ /g, '');
                            selectedtab.trim();
                            $("#tabname").text(selectedtab);

                            hideProgress();
                            //set rowspan count for current tab..if it is greater than 20 then allow to load rest of rows..J
                            sessionStorage.setItem("RowSpansCount" + '@Model.FormKey', data.RowSpanCount);

                            CheckScrollBar(data.IsRowCountGreaterThanTopCount);

                        },
                        error: function (data) {
                            //debugger
                            if (data.responseText.length > 0) {
                                //alert(data);
                            }
                        }
                    })
                    sessionStorage.setItem("LNKItem1" + formkey, "");
                }
                else {
                    $("#" + currentTab).addClass('btn btn-default active');
                    var tab = currentTab.replace("btn", '');
                    $("#hdPreTab").val('btn' + tab);
                    var formkey = '@Model.FormKey.Replace(" ","")';
                    //$("#page-loader").show();
                    showProgress();
                    $.ajax({
                        url: "/CreateForm/CreateFormHTML",
                        data: {
                            tabId: -1,
                            tabname: tab,
                            FormKey: formkey,
                            IsHeaderControlsLoad: true,
                            FormHistoryKey: '@Model.FormHistoryKey'
                        },
                        type: 'post',
                        cache: false,
                        success: function (data) {
                            $(".tab-content").append(data.TabData);

                            if (data.HeaderControlsCount > 0) {
                                $("#HeaderControlsRow").show();

                                $("#HeaderPart").append(data.HeaderHtml);

                                ManageRowStyle(true);
                            }
                            else {
                                $("#HeaderControlsRow").hide();

                                ManageRowStyle(false);
                            }

                            //$(".tab-content").append(data.HtmlValue);
                            $('.panel-tabs a[href="#tab' + tab + '"]').tab('show');
                            $("#hdtabinfocus").val($("#hdtabinfocus").val() + "," + '@Model.PrevTabPosition')
                            selectedtab = $("li.active").text().replace(/ /g, '');
                            selectedtab.trim();

                            //Showing More list tab when its selected, on save/edit..S1
                            if (parseInt('@Model.PrevTabPosition') >= 5) {
                                var tabName = $.trim(selectedtab);
                                var iconName = (tabName.toLowerCase() == 'properties') ? 'fa-user' : 'fa-pencil';
                                $('#btnShowSelectedTab').show();
                                $('#btnShowSelectedTab').html("<i class='fa " + iconName + "'></i>&nbsp;&nbsp;" + selectedtab);
                                $('#btnShowSelectedTab').addClass('active');
                            }

                            $("#tabname").text(selectedtab);
                            //$("#page-loader").hide();
                            hideProgress();
                            //set rowspan count for current tab..if it is greater than 20 then allow to load rest of rows..J
                            sessionStorage.setItem("RowSpansCount" + '@Model.FormKey', data.RowSpanCount);

                            CheckScrollBar(data.IsRowCountGreaterThanTopCount);
                        },
                        error: function (request, status, error) {
                            //debugger
                            if (request.responseText.length > 0 || request.responseText != undefined || request.responseText != null) {
                                // alert(request.responseText);
                            }
                            //alert(error);
                        }
                    })
                }
            }
            else {
                BtnMainClick()
            }
        }
        else if ('@Model.TabNameInFocus' != null && '@Model.TabNameInFocus.Replace(" ","")' != '') {
            if ('@Model.TabNameInFocus.Trim()' != '' && '@Model.TabNameInFocus.Trim()' != 'Main') {
                // $("#page-loader").show();
                showProgress();
                var tabname = '@Model.TabNameInFocus.Replace(" ","")';
                var formkey = '@Model.FormKey';
                @*var tabindex = "@Model.gsForm.GetTabIDByPos(Model.lstTabsorderlist[_count].TabPosition)";*@
                $("#btn" + '@Model.TabNameInFocus.Replace(" ","")').addClass('btn btn-default active');
                $('#' + $("#hdPreTab").val()).removeClass('btn btn-default active');
                $('#' + $("#hdPreTab").val()).addClass('btn btn-default');
                $("#hdPreTab").val('btn' + '@Model.TabNameInFocus.Replace(" ","")');
                sessionStorage.setItem("CurrentTab" + '@Model.Table' + formkey, $("#hdPreTab").val());
                $.ajax({
                    url: "/CreateForm/CreateFormHTML",
                    data: {
                        tabId: -1,
                        tabname: tabname,
                        FormKey: formkey,
                        IsHeaderControlsLoad: true,
                        FormHistoryKey: '@Model.FormHistoryKey'
                    },
                    cache: false,
                    type: 'post',
                    success: function (data) {
                        $(".tab-content").append(data.TabData);

                        if (data.HeaderControlsCount > 0) {
                            $("#HeaderControlsRow").show();

                            $("#HeaderPart").append(data.HeaderHtml);

                            ManageRowStyle(true);
                        }
                        else {
                            $("#HeaderControlsRow").hide();

                            ManageRowStyle(false);
                        }


                        //$(".tab-content").append(data.HtmlValue);
                        $('.panel-tabs a[href="@("#tab" + Model.TabNameInFocus.Replace(" ", ""))"]').tab('show');
                        $("#hdtabinfocus").val($("#hdtabinfocus").val() + "," + '@Model.PrevTabPosition')
                        selectedtab = $("li.active").text().replace(/ /g, '');
                        selectedtab.trim();
                        //Showing More list tab when its selected, on save/edit..S1
                        if (parseInt('@Model.PrevTabPosition') >= 5) {
                            var tabName = $.trim(selectedtab);
                            var iconName = (tabName.toLowerCase() == 'properties') ? 'fa-user' : 'fa-pencil';
                            $('#btnShowSelectedTab').show();
                            $('#btnShowSelectedTab').html("<i class='fa " + iconName + "'></i>&nbsp;&nbsp;" + selectedtab);
                            $('#btnShowSelectedTab').addClass('active');
                        }
                        $("#tabname").text(selectedtab);
                        //$("#page-loader").hide();
                        hideProgress();
                        //set rowspan count for current tab..if it is greater than 20 then allow to load rest of rows..J
                        sessionStorage.setItem("RowSpansCount" + '@Model.FormKey', data.RowSpanCount);

                        CheckScrollBar(data.IsRowCountGreaterThanTopCount);

                        if ('@Model.Table.ToUpper()' == 'FIND') {
                            var tabName = $.trim(selectedtab);
                            $("#tab" + tabName + " input:first").focus();
                        }
                    },
                    error: function (request, status, error) {
                        //debugger
                        if (request.responseText.length > 0 || request.responseText != undefined || request.responseText != null) {
                            //alert(request.responseText);
                        }
                    }
                })
            }
            else if ('@Model.TabNameInFocus.Trim()' == 'Main') {
                BtnMainClick();

            }
        }
        else {
            BtnMainClick()

        }

        //to error message in form load like 'user doesnot have permission to edit this record' etc..,
        var IsPannelErrorMessageDisplay = '@Model.gsForm.MessagePanelDisplay';
        var PannelErrorMessage = '@Model.gsForm.MessagePanelMessage';
        if (IsPannelErrorMessageDisplay == 'True') {
            $("#pnlError").show();
            $("#lblError").html(PannelErrorMessage);
        }
        else {
            $("#pnlError").hide();
        }

        if ('@Model.FirstOn' == 'True') {
            $('#First').prop('disabled', false);
            $('#Previous').prop('disabled', false);
        }
        else {
            $('#First').prop('disabled', true);
            $('#Previous').prop('disabled', true);
        }
        if ('@Model.LastOn' == 'True') {
            $('#Last').prop('disabled', false);
            $('#Next').prop('disabled', false);
        }
        else {
            $('#Last').prop('disabled', true);
            $('#Next').prop('disabled', true);
        }

    });

    function BtnMainClick() {

        var LNKVal1 = sessionStorage.getItem("LNKItem1" + '@Model.FormKey');
        if (LNKVal1 == "Yes") {
            svals = "[]";
        }
        var formkey = '@Model.FormKey';

        showProgress();
        $("#hdPreTab").val('btnMain');
        $('#' + $("#hdPreTab").val()).removeClass('btn btn-default');
        $('#' + $("#hdPreTab").val()).addClass('btn btn-default active');
        sessionStorage.setItem("CurrentTab" + '@Model.Table' + formkey, 'btnMain');
        $.ajax({
            url: "/CreateForm/CreateFormHTML",
            data: {
                tabname: "Main",
                tabId: 0,
                FormKey: formkey,
                IsHeaderControlsLoad: true,
                FormHistoryKey: '@Model.FormHistoryKey'
            },
            type: 'post',
            cache: false,
            success: function (data) {
                $(".tab-content").append(data.TabData);

                if (data.HeaderControlsCount > 0) {
                    $("#HeaderControlsRow").show();

                    $("#HeaderPart").append(data.HeaderHtml);

                    ManageRowStyle(true);
                }
                else {
                    $("#HeaderControlsRow").hide();

                    ManageRowStyle(false);
                }

                //$(".tab-content").append(data.HtmlValue);
                $('.panel-tabs a[href="#tabMain"]').tab('show');
                $("#hdtabinfocus").val($("#hdtabinfocus").val() + "," + "0")
                selectedtab = $("li.active").text().replace(/ /g, '');
                selectedtab.trim();
                $("#tabname").text(selectedtab);
                hideProgress();

                if ('@Model.Table' == 'AC')
                {
                    if ($("#MMO_NOTES").data("kendoEditor")) {
                        $("#MMO_NOTES").data("kendoEditor").focus();
                        //debugger
                        //$("html,body").animate({ scrollTop: $('#MMO_NOTES')[0].offsetTop - 100 }, 100);

                    }
                    else if ($("#MMO_NOTE").data("kendoEditor")) {
                        $("#MMO_NOTE").data("kendoEditor").focus();
                        //$('#MMO_NOTE').offsetTop - 100

                        //$("html,body").animate({ scrollTop: $('#MMO_NOTE')[0].offsetTop - 100 }, 100);
                    }
                }

                //set rowspan count for current tab..if it is greater than 20 then allow to load rest of rows..J
                sessionStorage.setItem("RowSpansCount" + '@Model.FormKey', data.RowSpanCount);

                CheckScrollBar(data.IsRowCountGreaterThanTopCount);
            },
            error: function (data) {
                if (data.responseText.length > 0 || data.responseText != undefined || data.responseText != null) {
                    //alert(data.responseText);
                }
            }
        })
        sessionStorage.setItem("LNKItem1" + '@Model.FormKey', "");
    }

    function FormNavigation(value, tablename, recordid, viewid) {

        viewid = viewid.replace(" ", "");
        var masterViewRecordId = sessionStorage.getItem("RecordId");

        NavValue = value;
        NavTableName = tablename;
        NavRecordId = recordid;
        NavViewKey = viewid;
        NavMasterViewRecordId = masterViewRecordId;
        NavField = '@Model.Field';

        showProgress();
        //var result = MessageBoxClick("SAVEANDNAVIGATE", "SAVE", "", true);
        var result = MessageBoxClick("SAVEANDNAVIGATE", "BROWSE", "", true);
        //debugger
        //if (result == "success") {
        @*$.ajax({
                url: "/CreateForm/GetRecordId",
                cache: false,
                async: true,
                data: { NavigateValue: value, TableName: tablename, RecordId: recordid, ViewKey: viewid, MasterViewRecordId: masterViewRecordId, Field: '@Model.Field' },
                type: 'GET',
                success: function (data) {
                    $.ajax({
                        url: '/CreateForm/SetNavigationSession',
                        cache: false,
                        async: false,
                        data: { FileName: '@Model.Table' },
                        success: function (data) {

                        },
                        error: function (data) {
                        }
                    })
                    if (data.NavigateRecordId != null && data.TableName != "") {

                        showProgress();
                        window.location = "/CreateForm/CreateForm/" + data.TableName + "/" + data.NavigateRecordId + "/TYPE/" + data.FirstOn + "/" + data.LastOn + "/" + data.ViewKey + "/false/" + data.MasterViewRecordId + "/FORMKEY/" + '@Model.Field';
                    }
                },
                error: function (request, status, error) {
                    if (request.responseText.length > 0 || request.responseText != undefined || request.responseText != null) {
                        //alert(request.responseText);
                    }
                }
            })*@
        //}
        //else
        //{
        //    hideProgress();
        //}

    }
    function onCancel() {
        //debugger
        if (navigator.onLine == false) {
            hideProgress();
            alert('Check your Internet Connection');
            return false;
        }

        var r = confirm("Are you sure you want to cancel this Form?");

        if (r == false) {
            return false;
        }

        showProgress();
        var formkey = '@Model.FormKey';
        var fromnew = sessionStorage.getItem("FromLNK_PopUp_CreateNew" + formkey);
        var LNKVal = sessionStorage.getItem("LNKItem" + formkey);
        if (fromnew == "YES") {

            //var url = '@ViewBag.PreviousUrl';
            sessionStorage.setItem("FromLNK_PopUp_CreateNew" + formkey, "");
            sessionStorage.setItem("IsLnkPopup" + formkey, "YES");
            sessionStorage.setItem("LNKItem1" + formkey, "Yes");
            sessionStorage.setItem("IsFromLNK" + formkey, "YES");
            //window.history.back();

            //window.location.href = url;
        }
        else if (LNKVal == "Yes") {
            //var url = '@ViewBag.PreviousUrl';
            sessionStorage.setItem("LNKItem" + formkey, "");
            sessionStorage.setItem("FromLNKGridDblClick" + formkey, "YES");
            sessionStorage.setItem("LNKItem1" + formkey, "Yes");
            sessionStorage.setItem("IsFromLNK" + formkey, "YES");
            //window.history.forward(-1);
            //window.history.back();

            //window.location.href = url;


            sessionStorage.removeItem("FilterGrid");
            sessionStorage.setItem("CurrentTab" + '@Model.Table' + '@Model.FormKey', "")

            @*var _value1 = '@Util.GetSessionValue("DesktopId")';
            var _value2 = '@Util.GetSessionValue("FolderID")';

            if (_value1 == null || _value1.length === 0) {
                window.location = '/Login/LoginSubmit';
        }

            if ('@Model.PreviousURL' != null && '@Model.PreviousURL' != '') {
                //debugger
                var prevUrl = '@Model.PreviousURL.ToString()';
                var UrlItems = prevUrl.split('/');
                if (UrlItems.length > 6) {
                    if (UrlItems[3] == "Desktop") {
                        UrlItems[6] = "true";
                        prevUrl = '';
                        $.each(UrlItems, function (index, value) {
                            if (value != '') {
                                if (value.indexOf('http') > -1)
                                    prevUrl = prevUrl + value + '//';
                                else
                                    prevUrl = prevUrl + value + "/";
                            }
                        })
                    }
                }

                //debugger
                window.location.href = prevUrl;
            }*@

            RemoveCurrentFormFromHistoryPanel()

        }
        else {
            $.ajax({
                url: '/CreateForm/ClearAllSessions',
                cache: false,
                async: true,
                data: { FormKey: '@Model.FormKey.Replace(" ","")', FormHistoryKey: '@Model.FormHistoryKey' },
                success: function (data) {

                    var IsLNKDblClick = sessionStorage.getItem("LNKItem" + '@Util.GetSessionValue("LastFormKey"+Model.FormHistoryKey)');
                    var IsLNKPopUp_CreateNewClick = sessionStorage.getItem("FromLNK_PopUp_CreateNew" + '@Util.GetSessionValue("LastFormKey" + Model.FormHistoryKey)');


                    if (data != null && data != "" && data != undefined && IsLNKDblClick != "Yes" && IsLNKPopUp_CreateNewClick != "YES") {
                        window.location = data;
                    }
                    else {

                        if (IsLNKDblClick == "Yes" || IsLNKPopUp_CreateNewClick == "YES") {

                            if (IsLNKDblClick == "Yes")
                                sessionStorage.removeItem("LNKItem" + '@Util.GetSessionValue("LastFormKey"+ Model.FormHistoryKey)');

                            if ('@Model.PreviousURL' != null && '@Model.PreviousURL' != '') {

                                var prevUrl = '@Model.PreviousURL.ToString()';
                                var UrlItems = prevUrl.split('/');
                                if (UrlItems.length > 6) {
                                    if (UrlItems[3] == "Desktop") {
                                        UrlItems[6] = "true";
                                        prevUrl = '';
                                        $.each(UrlItems, function (index, value) {
                                            if (value != '') {
                                                if (value.indexOf('http') > -1)
                                                    prevUrl = prevUrl + value + '//';
                                                else
                                                    prevUrl = prevUrl + value + "/";
                                            }
                                        })
                                    }
                                }

                                window.location.href = prevUrl;
                            }
                        }
                        else {

                            sessionStorage.removeItem("FilterGrid");
                            sessionStorage.setItem("CurrentTab" + '@Model.Table' + '@Model.FormKey', "")

                            var _value1 = '@Util.GetSessionValue("DesktopId")';
                            var _value2 = '@Util.GetSessionValue("FolderID")';

                            if (_value1 == null || _value1.length === 0) {
                                window.location = '/Login/LoginSubmit';
                            }

                            if ('@Model.PreviousURL' != null && '@Model.PreviousURL' != '') {
                                //debugger
                                var prevUrl = '@Model.PreviousURL.ToString()';
                                var UrlItems = prevUrl.split('/');
                                if (UrlItems.length > 6) {
                                    if (UrlItems[3] == "Desktop") {
                                        UrlItems[6] = "true";
                                        prevUrl = '';
                                        $.each(UrlItems, function (index, value) {
                                            if (value != '') {
                                                if (value.indexOf('http') > -1)
                                                    prevUrl = prevUrl + value + '//';
                                                else
                                                    prevUrl = prevUrl + value + "/";
                                            }
                                        })
                                    }
                                }

                                //debugger
                                window.location.href = prevUrl;
                            }
                        }
                    }
                },
                error: function (request, status, error) {

                }
            })
        }

        @*if ('@Model.PreviousURL' != null && '@Model.PreviousURL' != '') {
            //debugger
            var prevUrl = '@Model.PreviousURL.ToString()';
            var UrlItems = prevUrl.split('/');
            if (UrlItems.length > 6) {
                if (UrlItems[3] == "Desktop") {
                    UrlItems[6] = "true";
                    prevUrl = '';
                    $.each(UrlItems, function (index, value) {
                        if (value != '') {
                            if (value.indexOf('http') > -1)
                                prevUrl = prevUrl + value + '//';
                            else
                                prevUrl = prevUrl + value + "/";
                        }
            })
                }
            }

            //debugger
            window.location.href = prevUrl;
        }*@
    }

    function RemoveCurrentFormFromHistoryPanel() {
        $.ajax({
            url: '/CreateForm/RemoveCurrentFormFromHistoryPanel',
            cache: false,
            async: true,
            data: { FormKey: '@Model.FormKey.Replace(" ","")', FormHistoryKey: '@Model.FormHistoryKey' },
            success: function (data) {

                if (data != null && data != "" && data != undefined) {
                    window.location = data;
                }
                else {
                    sessionStorage.removeItem("FilterGrid");
                    sessionStorage.setItem("CurrentTab" + '@Model.Table' + '@Model.FormKey', "")

                    var _value1 = '@Util.GetSessionValue("DesktopId")';
                    var _value2 = '@Util.GetSessionValue("FolderID")';

                    if (_value1 == null || _value1.length === 0) {
                        window.location = '/Login/LoginSubmit';
                    }


                    if ('@Model.PreviousURL' != null && '@Model.PreviousURL' != '') {
                        //debugger
                        var prevUrl = '@Model.PreviousURL.ToString()';
                        var UrlItems = prevUrl.split('/');
                        if (UrlItems.length > 6) {
                            if (UrlItems[3] == "Desktop") {
                                UrlItems[6] = "true";
                                prevUrl = '';
                                $.each(UrlItems, function (index, value) {
                                    if (value != '') {
                                        if (value.indexOf('http') > -1)
                                            prevUrl = prevUrl + value + '//';
                                        else
                                            prevUrl = prevUrl + value + "/";
                                    }
                                })
                            }
                        }

                        window.location.href = prevUrl;
                    }
                }
            },
            error: function (request, status, error) {

            }
        })
    }

    function onDelete() {
        $.ajax({
            url: '/CreateForm/SetSelectedRecordID',
            cache: false,
            async: true,
            data: { TableName: '@Model.gsForm.Table', RecordId: '@Model.gsForm.RecordID', ViewKey: '@Model.ViewKey' },
            success: function (data) {
                if (data == "success")
                    MessageBoxClick("DELETEWARNING", "DELETEWARNING");
                @*$.ajax({
                    url: '/CreateForm/GetSessionValue',
                    cache: false,
                    async: true,
                    success: function (data)
                    {
                        if (data == 'success')
                        {
                            window.location.href = '@ViewBag.PreviousUrl';
                        }
                    }
                })*@
            },
            error: function (data) {
                //debugger
                if (data.responseText.length > 0 || data.responseText != undefined || data.responseText != null) {
                    //alert(data.responseText);
                }
            }
        })

    }


    function RunScript1(Args, Args1) {


        if (Args1.indexOf("CHK_") > -1) {
            if ($("#" + Args1)) {

                var _checked = $("#" + Args1).is(":checked");

                var _lit = Args1.substring(Args1.length - 2);
                //debugger
                if (_lit.indexOf("_") > -1) {
                    var DupItem = CheckForDuplicates(Args1);

                    if (DupItem.trim() != "False") {

                        var _arr = DupItem.split(',');

                        for (i = 0; i < _arr.length; i++) {

                            $("#" + _arr[i]).prop('checked', _checked);

                        }
                    }
                }
                else {
                    var DupItem = CheckForDuplicates(Args1);
                    if (DupItem.trim() != "False") {

                        var _arr = DupItem.split(',');

                        for (i = 0; i < _arr.length; i++) {

                            $("#" + _arr[i]).prop('checked', _checked);

                        }
                    }
                }
            }
        }
        //$('#page-loader').show();
        MessageBoxClick("RUNSCRIPT", Args, "");
        //$('#page-loader').hide();

        return false;
    }


    function RunScript(Args, IsFromCustomToolbar) {
       // alert(Args);
        var formkey = '@Model.FormKey';
        //$('#page-loader').show();
        MessageBoxClick("RUNSCRIPT", Args, "", null, null, IsFromCustomToolbar);
        //$('#page-loader').hide();
        //$.ajax({
        //    url: "/Common/RunScript",
        //    data: { Args: Args,FormKey:formkey },
        //    type: 'GET',
        //    success: function (data) {
        //        ////debugger;
        //        alert(data);
        //    },
        //    error: function (data) {
        //        ////debugger;
        //        alert(data)
        //    }
        //})
        if (Args.indexOf('FIND') >= 0 && Args.toLowerCase().indexOf('cancel') >= 0) {    //For Find on Cancel click
            $('#btnCancel').click();
        }
        return false;
    }

    function DisplayMessageBox(data) {

        if (navigator.onLine == false) {
            hideProgress();
            alert('Check your Internet Connection');
            return false;
        }
        //To toggle the Menu drop down..SB
        $('body').click();

        ////debugger;
        $("#pnlError").hide();

        if (data == null) {
            return;
        }
        if (data.MessageBoxDisplay == true) {
            //Set scroll to the top of the Body when MessageBox is displayed..J
            $("html,body").animate({ scrollTop: 0 }, 100);

            hideProgress();
            $("#PNL_MessageBox").show();
            $("#LBL_MsgBoxTitle").text(data.LBL_MsgBoxTitle);
            var _LBL_MsgBoxMessage = data.LBL_MsgBoxMessage.replace("/n/n", "</br></br>").replace("/n", "</br>");
            $("#LBL_MsgBoxMessage").html(_LBL_MsgBoxMessage);

            if (data.TXT_MsgBoxInputVisible == true) {
                $("#TXT_MsgBoxInput").show();
                $("#TXT_MsgBoxInput").val(data.TXT_MsgBoxInputText);
                $("#TXT_MsgBoxInput").focus();
                $('#TXT_MsgBoxInput').focus().val('').val(data.TXT_MsgBoxInputText);
            }
            else {
                $("#TXT_MsgBoxInput").hide();
                $("#TXT_MsgBoxInput").val("");
            }

            if (data.TXT_MsgBoxInputRichVisible == true) {
                $("#divTXT_MsgBoxInputRich").show();
                $("#TXT_MsgBoxInputRich").data("kendoEditor").value(data.TXT_MsgBoxInputRichContent);
                ////debugger;
            }
            else {
                $("#divTXT_MsgBoxInputRich").hide();
                $("#TXT_MsgBoxInputRich").data("kendoEditor").value("");
            }

            if (data.BTN_MsgBox1Visible == true) {
                $("#BTN_MsgBox1").show();

                $("#BTN_MsgBox1").attr('value', data.BTN_MsgBox1Text); //versions older than 1.6
                $("#BTN_MsgBox1").prop('value', data.BTN_MsgBox1Text); //versions newer than 1.6
                $("#BTN_MsgBox1").html(data.BTN_MsgBox1Text);
                $("#BTN_MsgBox1").attr("title", data.BTN_MsgBox1ToolTip);
                //debugger;
                $("#BTN_MsgBox1").attr('onclick', data.BTN_MsgBox1_onClick);

                //BTN_MsgBox1_onClick
            }
            else {
                $("#BTN_MsgBox1").hide();
            }

            if (data.BTN_MsgBox2Visible == true) {
                $("#BTN_MsgBox2").show();

                $("#BTN_MsgBox2").attr('value', data.BTN_MsgBox2Text); //versions older than 1.6
                $("#BTN_MsgBox2").prop('value', data.BTN_MsgBox2Text); //versions newer than 1.6
                $("#BTN_MsgBox2").html(data.BTN_MsgBox2Text);
                $("#BTN_MsgBox2").attr("title", data.BTN_MsgBox2ToolTip);
                $("#BTN_MsgBox2").attr('onclick', data.BTN_MsgBox2_onClick);
                //BTN_MsgBox1_onClick
            }
            else {
                $("#BTN_MsgBox2").hide();
            }

            if (data.BTN_MsgBox3Visible == true) {
                $("#BTN_MsgBox3").show();

                $("#BTN_MsgBox3").attr('value', data.BTN_MsgBox3Text); //versions older than 1.6
                $("#BTN_MsgBox3").prop('value', data.BTN_MsgBox3Text); //versions newer than 1.6
                $("#BTN_MsgBox3").html(data.BTN_MsgBox3Text);
                $("#BTN_MsgBox3").attr("title", data.BTN_MsgBox3ToolTip);
                $("#BTN_MsgBox3").attr('onclick', data.BTN_MsgBox3_onClick);

                //BTN_MsgBox1_onClick
            }
            else {
                $("#BTN_MsgBox3").hide();
            }

        }
        else if (data.IsError == true) {
            //Set scroll to the top of the Body when MessageBox is displayed..J
            $("html,body").animate({ scrollTop: 0 }, 100);
            hideProgress();
            $("#pnlError").show();
            $("#lblError").text(data.ErrorMessage);
        }
        //alert(data);
    }

    function DisplayMessageBoxPanel(data) {
        //Message panel added and replaced pnrError with Message panel -- RN
        $("#pnlMessagePanel").hide();
        //$("#PNL_MessageBox").hide();
        if (data.MessagePanelDisplay == true) {
            hideProgress();
            $("#pnlMessagePanel").show();
            $("#lblMessagePanel").text(data.MessagePanelMessage);
        }
    }

    function RefreshIFrame(data) {
        var iframe = $('#' + data.FieldName);
        if (iframe.length) {
            iframe.attr('src', data.FilePath);
            return false;
        }
    }

    function RefreshLinkControls(data) {

        $.each(data, function (index, value) {
            var fieldname = value.FieldName;
            if (fieldname.indexOf("LNK_") > -1) {
                if ($("#" + fieldname).data("kendoGrid")) {
                    // $("#" + value).data("kendoMultiSelect").value(value.FieldValue.split("\r\n"));
                    $("#" + fieldname).data('kendoGrid').dataSource.read();
                    $("#" + fieldname).data('kendoGrid').refresh();
                }
                else if ($("#" + fieldname).data("kendoMultiSelect") || $("#NDB_" + fieldname).data("kendoMultiSelect")) {

                    showProgress();
                    $.ajax({
                        url: '/CreateForm/LoadLNKMultiSelectData',
                        data: { sField: fieldname, FormKey: '@Model.FormKey', FormHistoryKey: '@Model.FormHistoryKey' },
                        cache: false,
                        async: false,
                        dataType: "json",
                        type: "GET",
                        contentType: 'application/json',
                        success: function (result) {
                            if ($("#" + fieldname).data("kendoMultiSelect"))
                                $("#" + fieldname).data("kendoMultiSelect").setDataSource(new kendo.data.DataSource({ data: result }));
                            else if ($("#NDB_" + fieldname).data("kendoMultiSelect"))
                                $("#NDB_" + fieldname).data("kendoMultiSelect").setDataSource(new kendo.data.DataSource({ data: result }));
                            hideProgress();
                        },
                        error: function (data) {
                            hideProgress();

                        }
                    })

                }
            }
        })
    }

    function RefreshControls(data) {


        $.each(data, function (index, value) {
            var fieldname = value.FieldName;

            if ($("#" + fieldname)) {

                if (fieldname.indexOf("MMR_") > -1 || fieldname.indexOf("MMO_") > -1) {

                    var ContentValue = value.FieldValue;    //.replace(/(\r\n|\r|\n)/g, "</br>");

                    if (ContentValue.indexOf('[%Prepend/AppendText%]')>-1)
                    {
                        var aValues = ContentValue.split('[%Prepend/AppendText%]');

                        ContentValue = aValues[0];
                        var PrependAppendText = aValues[1];

                        var editor = $("#" + fieldname).data("kendoEditor");
                        editor.exec("inserthtml", { value: PrependAppendText });

                        ContentValue = $("#" + fieldname).data("kendoEditor").value();
                    }

                    if (fieldname.indexOf("MMO_") > -1 && $("#" + fieldname).data("kendoEditor")) {
                        ContentValue = ContentValue.replace(/(\r\n|\r|\n)/g, "</br>");
                    }
                    else if (fieldname.indexOf("MMR_") > -1) {
                        ContentValue = ContentValue.replace(/(\r\n|\r|\n)/g, "");
                    }
                    //ContentValue = ContentValue.replace(/(\r\n|\r|\n)/g, "</br>");

                    var _fielddet = CheckForDuplicates(fieldname);
                    if (_fielddet.trim() != "False") {

                        var _arr = _fielddet.split(',');

                        for (i = 0; i < _arr.length; i++) {

                            //  $("#" + _arr[i]).prop('checked', _checked);

                            if ($("#" + _arr[i]).data("kendoEditor")) {

                                //$("#" + _arr[i]).data("kendoEditor").value(value.FieldValue.replace(/(\r\n|\r|\n)/g, "</br>"));
                                $("#" + _arr[i]).data("kendoEditor").value(ContentValue);

                                var editorBody = $(".k-editable-area iframe").contents().find("body");
                                editorBody.css("font-family", "Arial");
                                editorBody.css("font-size", "12px");
                            }

                        }
                    }
                    else {
                        if ($("#" + fieldname).data("kendoEditor")) {

                            //$("#" + fieldname).data("kendoEditor").value(value.FieldValue.replace(/(\r\n|\r|\n)/g, "</br>"));
                            $("#" + fieldname).data("kendoEditor").value(ContentValue);

                            var editorBody = $(".k-editable-area iframe").contents().find("body");
                            editorBody.css("font-family", "Arial");
                            editorBody.css("font-size", "12px");
                        }
                        else {
                            $("#" + fieldname).val(ContentValue);
                        }
                    }



                }
                else if (fieldname.indexOf("DTE_") > -1) {
                    if ($("#" + fieldname).data("kendoDatePicker")) {
                        $("#" + fieldname).data("kendoDatePicker").value(value.FieldValue);
                        if (value.FieldValue != '' && value.FieldValue != null) {
                            $($("#" + fieldname).parent().parent().parent().parent()).removeClass('fg-toggledDTE');
                            $($("#" + fieldname).parent().parent().parent().parent()).addClass('fg-toggledDTE');
                        }
                    }

                }
                else if (fieldname.indexOf("TME_") > -1) {
                    if ($("#" + fieldname).data("kendoTimePicker")) {
                        $("#" + fieldname).data("kendoTimePicker").value(value.FieldValue)
                        if (value.FieldValue != '' && value.FieldValue != null) {
                            $($("#" + fieldname).parent().parent().parent().parent()).removeClass('fg-toggledDTE');
                            $($("#" + fieldname).parent().parent().parent().parent()).addClass('fg-toggledDTE');
                        }
                    }
                }
                else if (fieldname.indexOf("CHK_") > -1) {
                    if (value.FieldValue.toLowerCase() == "checked") {
                        $("#" + fieldname).prop("checked", true);
                    }
                    else {
                        $("#" + fieldname).prop("checked", false);
                    }
                }
                else if (fieldname.indexOf("MLS_") > -1) {
                    $("#" + fieldname).data("kendoComboBox").value(value.FieldValue);
                }
                else if (fieldname.indexOf("SI__") > -1) {
                    $("#" + fieldname).val(value.FieldValue);
                }

                else if (fieldname.indexOf("LNK_") > -1) {
                    if ($("#" + fieldname).data("kendoGrid")) {
                        // $("#" + value).data("kendoMultiSelect").value(value.FieldValue.split("\r\n"));
                        $("#" + fieldname).data('kendoGrid').dataSource.read();
                        $("#" + fieldname).data('kendoGrid').refresh();
                    }
                    else if ($("#" + fieldname).data("kendoMultiSelect") || $("#NDB_" + fieldname).data("kendoMultiSelect")) {

                        showProgress();
                        $.ajax({
                            url: '/CreateForm/LoadLNKMultiSelectData',
                            data: { sField: fieldname, FormKey: '@Model.FormKey', FormHistoryKey: '@Model.FormHistoryKey' },
                            cache: false,
                            async: false,
                            dataType: "json",
                            type: "GET",
                            contentType: 'application/json',
                            success: function (result) {
                                if ($("#" + fieldname).data("kendoMultiSelect"))
                                    $("#" + fieldname).data("kendoMultiSelect").setDataSource(new kendo.data.DataSource({ data: result }));
                                else if ($("#NDB_" + fieldname).data("kendoMultiSelect"))
                                    $("#NDB_" + fieldname).data("kendoMultiSelect").setDataSource(new kendo.data.DataSource({ data: result }));
                                hideProgress();
                            },
                            error: function (data) {
                                hideProgress();

                            }
                        })

                    }
                    else if ($("#" + fieldname).data("kendoComboBox")) {
                        $("#" + value).data("kendoComboBox").value(value.FieldValue);
                    }
                }
                else if (fieldname.indexOf("FIL_") > -1 || fieldname.indexOf("URL_") > -1) {
                    if ($("#" + fieldname).length > 0) {
                        $("#" + fieldname).empty();
                        var _lstdata = value.FieldValue.split("\r\n");
                        $.each(_lstdata, function (ind, val) {
                            if (val != "") {
                                $("#" + fieldname).append($('<option>').text(val).val(val));
                            }
                        });
                    }
                }
                else {
                    if (value.FieldValue != null) {
                        if (value.FieldValue.indexOf('\r\n') > -1) {
                            $("#" + fieldname).val(value.FieldValue.replace(/(\r\n|\r|\n)/g, "\n"));
                        }
                        else {
                            $("#" + fieldname).val(value.FieldValue);
                        }
                    }
                    if (fieldname.indexOf("TXT_") > -1 || fieldname.indexOf("EML_") > -1 || fieldname.indexOf("TEL_") > -1) {
                        $($("#" + fieldname).parent()).removeClass('fg-toggled');
                        $($("#" + fieldname).parent()).addClass('fg-toggled');
                    }
                }
            }
        });


    }

    function RefreshControlsState(obj) {

        if (navigator.onLine == false) {
            hideProgress();
            alert('Check your Internet Connection');
            return false;
        }

        var data = obj.ControlState;

        $.each(data, function (index, prop) {

            var fieldname = prop.FieldName;

            //Set proper field name or else it skips some controls, Some of them control ids are in uppercase and some are in lower case..J
            if ($("#" + fieldname.toUpperCase()).length > 0) {
                fieldname = fieldname.toUpperCase();
            }
            else if ($("#" + fieldname.toLowerCase()).length > 0) {
                fieldname = fieldname.toLowerCase();
            }
            else {
                fieldname = prop.FieldName;
            }

            //debugger
            if (($("#" + fieldname) && $("#" + fieldname).length > 0) || fieldname.indexOf("TAB_FORM") > -1) {
                if (fieldname.indexOf("MMR_") > -1 || fieldname.indexOf("MMO_") > -1) {
                    if ($("#" + fieldname).data("kendoEditor")) {
                        switch (prop.FieldPropertiy.State) {
                            case 0://0	Active	Active
                                // $("#div_" + fieldname).show();
                                $("#lbl_" + fieldname).show();
                                $("#lbldiv_" + fieldname).hide();
                                $("#cdiv_" + fieldname).show();
                                var editor = $("#" + fieldname).data("kendoEditor");
                                $(editor.body).attr('contenteditable', true);
                                break;
                            case 1: //1	Inactive	Visible, but locked—the user can’t click the control
                            case 4:  //4	Grayed	Grayed out
                                $("#lbl_" + fieldname).show();
                                $("#lbldiv_" + fieldname).hide();
                                var editor = $("#" + fieldname).data("kendoEditor");
                                $(editor.body).attr('contenteditable', false);
                                $("#cdiv_" + fieldname).show();
                                break;
                            case 2:  //2	Invisible	Invisible
                                $("#lbl_" + fieldname).hide();
                                $("#cdiv_" + fieldname).hide();
                                $("#lbldiv_" + fieldname).show();
                                break;
                            default:
                                break;
                        }
                    }

                }
                    //else if (fieldname.indexOf('MMO_') > -1 || fieldname.indexOf('MMP_') > -1) {
                    //    if ($("#" + fieldname)) {
                    //        switch (prop.FieldPropertiy.State) {
                    //            case 0://0	Active	Active
                    //                // $("#div_" + fieldname).show();
                    //                $("#lbl_" + fieldname).show();
                    //                $("#lbldiv_" + fieldname).hide();
                    //                $("#cdiv_" + fieldname).show();
                    //                $('#' + fieldname).prop('disabled', false);
                    //                break;
                    //            case 1: //1	Inactive	Visible, but locked—the user can’t click the control
                    //            case 4:  //4	Grayed	Grayed out
                    //                $("#lbl_" + fieldname).show();
                    //                $("#lbldiv_" + fieldname).hide();
                    //                $('#' + fieldname).prop('disabled', true);
                    //                $("#cdiv_" + fieldname).show();
                    //                break;
                    //            case 2:  //2	Invisible	Invisible
                    //                $("#lbl_" + fieldname).hide();
                    //                $("#cdiv_" + fieldname).hide();
                    //                $("#lbldiv_" + fieldname).show();
                    //                break;
                    //            default:
                    //                break;
                    //        }
                    //    }
                    //}
                else if (fieldname.indexOf("DTE_") > -1) {
                    //debugger
                    if ($("#" + fieldname).data("kendoDatePicker")) {

                        if ($('#dtelbl_' + fieldname).length > 0) {
                            if (prop.FieldPropertiy.LabelColor != "" && prop.FieldPropertiy.LabelColor != null) {
                                $('#dtelbl_' + fieldname).css('color', prop.FieldPropertiy.LabelColor);
                            }
                            if (prop.FieldPropertiy.LabelText != "" && prop.FieldPropertiy.LabelText != null) {
                                $('#dtelbl_' + fieldname).text(prop.FieldPropertiy.LabelText);
                            }
                        }

                        switch (prop.FieldPropertiy.State) {
                            case 0://0	Active	Active
                                // $("#div_" + fieldname).show();
                                $("#lbl_" + fieldname).show();
                                $("#divMainDateTimePicker_" + fieldname).show();
                                $("#lbldiv_" + fieldname).hide();
                                $("#cdiv_" + fieldname).show();
                                $("#" + fieldname).data("kendoDatePicker").enable(true);
                                break;
                            case 1: //1	Inactive	Visible, but locked—the user can’t click the control
                            case 4:  //4	Grayed	Grayed out
                                $("#lbl_" + fieldname).show();
                                $("#divMainDateTimePicker_" + fieldname).show();
                                $("#lbldiv_" + fieldname).hide();
                                $("#" + fieldname).data("kendoDatePicker").enable(false);
                                $("#cdiv_" + fieldname).show();
                                break;
                            case 2:  //2	Invisible	Invisible
                                $("#lbl_" + fieldname).hide();
                                $("#divMainDateTimePicker_" + fieldname).hide();
                                $("#cdiv_" + fieldname).hide();
                                $("#lbldiv_" + fieldname).hide();
                                break;
                            default:
                                break;
                        }
                    }
                }
                else if (fieldname.indexOf("TXT_") > -1) {

                        if ($('#lbl_' + fieldname).length > 0) {
                            if (prop.FieldPropertiy.LabelColor != "" && prop.FieldPropertiy.LabelColor != null) {
                                $('#lbl_' + fieldname).css('color', prop.FieldPropertiy.LabelColor);
                            }
                            if (prop.FieldPropertiy.LabelText != "" && prop.FieldPropertiy.LabelText != null) {
                                $('#lbl_' + fieldname).text(prop.FieldPropertiy.LabelText);
                            }
                        }

                        switch (prop.FieldPropertiy.State) {

                            case 0://0	Active	Active
                                // $("#div_" + fieldname).show();
                                $("#lbl_" + fieldname).show();
                                $("#" + fieldname).show();
                                $("#icon_" + fieldname).show();
                                $("#lbldiv_" + fieldname).hide();

                                $('#' + fieldname).prop('disabled', false);
                                if ($('#ilbl_' + fieldname).length > 0) {
                                    $('#ilbl_' + fieldname).show();
                                }
                                break;
                            case 1: //1	Inactive	Visible, but locked—the user can’t click the control
                            case 4:  //4	Grayed	Grayed out
                                $("#lbl_" + fieldname).show();
                                $("#" + fieldname).show();
                                $("#icon_" + fieldname).show();
                                $("#lbldiv_" + fieldname).hide();
                                $('#' + fieldname).prop('disabled', true);
                                break;
                            case 2:  //2	Invisible	Invisible
                                $("#lbl_" + fieldname).hide();
                                $("#" + fieldname).hide();
                                $("#icon_" + fieldname).hide();
                                $("#lbldiv_" + fieldname).show();
                                break;
                            default:
                                break;
                        }

                }
                else if (fieldname.indexOf("TME_") > -1) {
                    if ($("#" + fieldname).data("kendoTimePicker")) {

                        if ($('#tmelbl_' + fieldname).length > 0) {
                            if (prop.FieldPropertiy.LabelColor != "" && prop.FieldPropertiy.LabelColor != null) {
                                $('#tmelbl_' + fieldname).css('color', prop.FieldPropertiy.LabelColor);
                            }
                            if (prop.FieldPropertiy.LabelText != "" && prop.FieldPropertiy.LabelText != null) {
                                $('#tmelbl_' + fieldname).text(prop.FieldPropertiy.LabelText);
                            }
                        }

                        switch (prop.FieldPropertiy.State) {
                            case 0://0	Active	Active
                                // $("#div_" + fieldname).show();
                                $("#lbl_" + fieldname).show();
                                $("#lbldiv_" + fieldname).hide();
                                $("#cdiv_" + fieldname).show();
                                $("#" + fieldname).data("kendoTimePicker").enable(true);
                                break;
                            case 1: //1	Inactive	Visible, but locked—the user can’t click the control
                            case 4:  //4	Grayed	Grayed out
                                $("#lbl_" + fieldname).show();
                                $("#lbldiv_" + fieldname).hide();
                                $("#" + fieldname).data("kendoTimePicker").enable(false);
                                $("#cdiv_" + fieldname).show();
                                break;
                            case 2:  //2	Invisible	Invisible
                                $("#lbl_" + fieldname).hide();
                                $("#cdiv_" + fieldname).hide();
                                $("#lbldiv_" + fieldname).show();
                                break;
                            default:
                                break;
                        }
                    }
                }
                else if (fieldname.indexOf("MLS_") > -1) {

                    if ($('#mlslbl_' + fieldname).length > 0) {
                        if (prop.FieldPropertiy.LabelColor != "" && prop.FieldPropertiy.LabelColor != null) {
                            $('#mlslbl_' + fieldname).css('color', prop.FieldPropertiy.LabelColor);
                        }
                        if (prop.FieldPropertiy.LabelText != "" && prop.FieldPropertiy.LabelText != null) {
                            $('#mlslbl_' + fieldname).text(prop.FieldPropertiy.LabelText);
                        }
                    }

                    switch (prop.FieldPropertiy.State) {
                        case 0://0	Active	Active
                            // $("#div_" + fieldname).show();
                            $("#lbl_" + fieldname).show();
                            $("#lbldiv_" + fieldname).hide();
                            $("#cdiv_" + fieldname).show();
                            $("#" + fieldname).data("kendoComboBox").enable(true);
                            break;
                        case 1: //1	Inactive	Visible, but locked—the user can’t click the control
                        case 4:  //4	Grayed	Grayed out
                            $("#lbl_" + fieldname).show();
                            $("#lbldiv_" + fieldname).hide();
                            $("#" + fieldname).data("kendoComboBox").enable(false);
                            $("#cdiv_" + fieldname).show();
                            break;
                        case 2:  //2	Invisible	Invisible
                            $("#lbl_" + fieldname).hide();
                            $("#cdiv_" + fieldname).hide();
                            $("#lbldiv_" + fieldname).show();
                            break;
                        default:
                            break;
                    }
                }
                else if (fieldname.indexOf("LNK_") > -1) {
                    // debugger;
                    var _fControl = null;
                    if ($("#" + fieldname).data("kendoGrid")) {
                        _fControl = $("#" + fieldname).data("kendoGrid");
                        // _fControl = $("#" + fieldname).data("kendoMultiSelect");
                    }
                    else if ($("#" + fieldname).data("kendoComboBox")) {
                        _fControl = $("#" + fieldname).data("kendoComboBox");
                    }
                    else if ($("#" + fieldname).data("kendoMultiSelect")) {
                        _fControl = $("#" + fieldname).data("kendoMultiSelect");
                    }
                    if (_fControl != null) {
                        switch (prop.FieldPropertiy.State) {
                            case 0://0	Active	Active
                                // $("#div_" + fieldname).show();
                                $("#lbl" + fieldname).show();

                                //Set lable color after any event has fired(Ex: MLS_TYPE change)..J
                                if ($('#lbl' + fieldname).length > 0) {
                                    if (prop.FieldPropertiy.LabelColor != "" && prop.FieldPropertiy.LabelColor != null) {
                                        $('#lbl' + fieldname).css('color', prop.FieldPropertiy.LabelColor);
                                        $('#ilbl_' + fieldname).css('color', prop.FieldPropertiy.LabelColor);
                                    }
                                    if (prop.FieldPropertiy.LabelText != "" && prop.FieldPropertiy.LabelText != null) {
                                        $('#lbl' + fieldname).text(prop.FieldPropertiy.LabelText);
                                       /* $('#ilbl_' + fieldname).text(prop.FieldPropertiy.LabelText);*/
                                    }

                                }


                                $("#lbldiv_" + fieldname).hide();
                                $("#cdiv_" + fieldname).show();
                                $("#cdiv_" + fieldname).removeClass('enabledClass');
                                // _fControl.enable(true);
                                if ($('#ilbl_' + fieldname).length > 0) {
                                    $('#ilbl_' + fieldname).show();
                                }
                                break;
                            case 1: //1	Inactive	Visible, but locked—the user can’t click the control
                            case 4:  //4	Grayed	Grayed out
                                $("#lbl_" + fieldname).show();
                                $("#lbldiv_" + fieldname).hide();
                                //  _fControl.enable(false);
                                $("#cdiv_" + fieldname).show();
                                $("#cdiv_" + fieldname).removeClass('enabledClass');
                                $("#cdiv_" + fieldname).addClass('enabledClass');
                                if ($('#ilbl_' + fieldname).length > 0) {
                                    $('#ilbl_' + fieldname).prop("disabled", true);
                                }
                                break;
                            case 2:  //2	Invisible	Invisible
                                $("#lbl_" + fieldname).hide();
                                $("#cdiv_" + fieldname).hide();
                                $("#lbldiv_" + fieldname).show();
                                if ($('#ilbl_' + fieldname).length > 0) {
                                    $('#ilbl_' + fieldname).hide();
                                }
                                $("#lbl" + fieldname).hide();
                                break;
                            default:
                                break;
                        }
                    }

                }
                else if (fieldname.indexOf("FIL_") > -1 || fieldname.indexOf("URL_") > -1) {
                    //if ($("#" + fieldname).length > 0) {
                    //    $("#" + fieldname).empty();
                    //    var _lstdata = value.FieldValue.split("\r\n");
                    //    $.each(_lstdata, function (ind, val) {
                    //        if (val != "") {
                    //            $("#" + fieldname).append($('<option>').text(val).val(val));
                    //        }
                    //    });
                    //}
                }
                else if (fieldname.indexOf("TEL_") > -1) {
                    switch (prop.FieldPropertiy.State) {

                        case 0://0	Active	Active
                            // $("#div_" + fieldname).show();
                            $("#lbl_" + fieldname).show();
                            $("#" + fieldname).show();
                            $("#icon_" + fieldname).show();
                            $("#lbldiv_" + fieldname).hide();

							$('#' + fieldname).prop('disabled', false);
							if ($('#ilbl_' + fieldname).length > 0) {
								$('#ilbl_' + fieldname).show();
							}
                            break;
                        case 1: //1	Inactive	Visible, but locked—the user can’t click the control
                        case 4:  //4	Grayed	Grayed out
                            $("#lbl_" + fieldname).show();
                            $("#" + fieldname).show();
                            $("#icon_" + fieldname).show();
                            $("#lbldiv_" + fieldname).hide();
                            $('#' + fieldname).prop('disabled', true);
                            break;
                        case 2:  //2	Invisible	Invisible
                            $("#lbl_" + fieldname).hide();
                            $("#" + fieldname).hide();
                            $("#icon_" + fieldname).hide();
                            $("#lbldiv_" + fieldname).show();
                            break;
                        default:
                            break;
                    }
                }
                else if (fieldname.indexOf("BTN_") > -1) {
                    //debugger
                    switch (prop.FieldPropertiy.State) {
                        case 0://0	Active	Active
                            // $("#div_" + fieldname).show();
                            $("#lbl_" + fieldname).show();
                            $("#" + fieldname).show();
                            $("#icon_" + fieldname).show();
                            $("#lbldiv_" + fieldname).hide();
                            //debugger;
                            if (prop.FieldPropertiy.ToolTip != null)
                                $('#' + fieldname).prop('title', prop.FieldPropertiy.ToolTip);
                            var _btntext = $("#" + fieldname)[0].innerText;
                            var _html = "";
                            if ($('#' + fieldname).length > 0 && $('#' + fieldname)[0].childElementCount > 0) {
                                _html = $("#" + fieldname)[0].children['0'].outerHTML;
                                if (prop.FieldPropertiy.Image != null) {

                                    _html = "<img src=\"/Content/themes/Selltis/images/" + prop.FieldPropertiy.Image + "\" />&nbsp; ";
                                }
                            }
                            if (prop.FieldPropertiy.LabelText != null) {

                                _btntext = "&nbsp;" + prop.FieldPropertiy.LabelText;
                                $('#' + fieldname).html(_html + _btntext);
                            }

							$('#' + fieldname).prop('disabled', false);
							if ($('#ilbl_' + fieldname).length > 0) {
								$('#ilbl_' + fieldname).show();
							}
                            break;
                        case 1: //1	Inactive	Visible, but locked—the user can’t click the control
                        case 4:  //4	Grayed	Grayed out
                            $("#lbl_" + fieldname).show();
                            $("#" + fieldname).show();
                            $("#icon_" + fieldname).show();
                            $("#lbldiv_" + fieldname).hide();
                            if (prop.FieldPropertiy.ToolTip != null)
                                $('#' + fieldname).prop('title', prop.FieldPropertiy.ToolTip);
                            var _html = "";
                            if ($('#' + fieldname).length > 0 && $('#' + fieldname)[0].childElementCount > 0) {
                                _html = $("#" + fieldname)[0].children['0'].outerHTML;
                                if (prop.FieldPropertiy.Image != null) {

                                    _html = "<img src=\"/Content/themes/Selltis/images/" + prop.FieldPropertiy.Image + "\" />&nbsp; ";
                                }
                            }
                            if (prop.FieldPropertiy.LabelText != null) {

                                _btntext = "&nbsp;" + prop.FieldPropertiy.LabelText;
                                $('#' + fieldname).html(_html + _btntext);
                            }

                            $('#' + fieldname).prop('disabled', true);
                            break;
                        case 2:  //2	Invisible	Invisible
                            $("#lbl_" + fieldname).hide();
                            $("#" + fieldname).hide();
                            $("#icon_" + fieldname).hide();
                            $("#lbldiv_" + fieldname).show();
                            break;
                        default:
                            break;
                    }
                }
                else if (fieldname.indexOf("TAB_FORM") > -1) {
                    //TAB_FORM[1] //to-do
                    var tab = fieldname.replace('TAB_FORM', '');
                    tab = tab.replace('[', '');
                    tab = tab.replace(']', '');
                    var _TabName = obj.Tabs[parseInt(tab) - 1].TabName;
                    _TabName = "btn" + _TabName;
                    if ($('#' + _TabName).length > 0) {
                        switch (prop.FieldPropertiy.State) {
                            case 0://0	Active	Active
                                $("#" + _TabName).show();
                                if (prop.FieldPropertiy.ToolTip != null)
                                    $('#' + _TabName).prop('title', prop.FieldPropertiy.ToolTip);
								$('#' + _TabName).prop('disabled', false);
								if ($('#ilbl_' + _TabName).length > 0) {
									$('#ilbl_' + _TabName).show();
								}
                                //debugger
                                var RunScriptControl = $('#hdRunScriptControl').val();
                                if (($("#hdPreTab").val() == "btnMain") && (RunScriptControl.indexOf('MLS_') > -1)) {
                                    //$("#page-loader").show();
                                    showProgress();
                                    var tabposition = tab;
                                    var tabname = _TabName.replace("btn", "");
                                    var formkey = '@Model.FormKey';

                                    $("#" + _TabName).addClass('btn btn-default active');
                                    $('#' + $("#hdPreTab").val()).removeClass('btn btn-default active');
                                    $('#' + $("#hdPreTab").val()).addClass('btn btn-default');
                                    $("#hdPreTab").val('btn' + tabname.replace(/ /g, ''));
                                    sessionStorage.setItem("CurrentTab" + '@Model.Table' + formkey, $("#hdPreTab").val());
                                    var svals = $("#hdtabinfocus").val().split(",");
                                    //debugger
                                    var fielditems = [];
                                    fielditems = GetAllFieldValues(true);
                                    var objfields = JSON.stringify(fielditems);
                                    objfields = encodeURIComponent(objfields);
                                    if (svals && svals.indexOf(tabposition) == -1) {
                                        $.ajax({
                                            url: "/CreateForm/CreateFormHTML",
                                            data: {
                                                tabId: -1,
                                                tabname: tabname,
                                                FormKey: formkey,
                                                Fields: objfields,
                                                IsHeaderControlsLoad: false,
                                                FormHistoryKey: '@Model.FormHistoryKey'
                                            },
                                            type: 'post',
                                            cache: false,
                                            success: function (data) {
                                                $(".tab-pane").hide();
                                                $(".tab-content").append(data.TabData);
                                                //$(".tab-content").append(data.HtmlValue);
                                                var value = _TabName.replace("btn", "#tab");
                                                $('.panel-tabs a[href="' + value + '"]').tab('show');
                                                $("#hdtabinfocus").val($("#hdtabinfocus").val() + "," + tabposition)
                                                selectedtab = $("li.active").text().replace(/ /g, '');
                                                selectedtab.trim();
                                                $("#tabname").text(selectedtab);
                                                $("#tab" + selectedtab).show();
                                                //$("#page-loader").hide();
                                                hideProgress();

                                                //set rowspan count for current tab..if it is greater than 20 then allow to load rest of rows..J
                                                sessionStorage.setItem("RowSpansCount" + '@Model.FormKey', data.RowSpanCount);

                                                CheckScrollBar(data.IsRowCountGreaterThanTopCount);
                                                //if (data.FieldInFocus != null && data.FieldInFocus != undefined && data.FieldInFocus != "") {
                                                //    if (data.FieldInFocus.indexOf("MMO_") >= 0) {
                                                //        if ($("#" + data.FieldInFocus).data("kendoEditor")) {
                                                //            $("#" + data.FieldInFocus).data("kendoEditor").focus();
                                                //        }

                                                //    }
                                                //    else ($("#" + data.FieldInFocus))
                                                //    {
                                                //        $("#" + data.FieldInFocus).focus();
                                                //    }
                                                //}
                                            },
                                            error: function (request, status, error) {
                                                //debugger
                                                if (request.responseText.length > 0 || request.responseText != undefined || request.responseText != null) {
                                                    //alert(request.responseText);
                                                }
                                            }
                                        })
                                    }
                                    else {
                                        var value = _TabName.replace("btn", "#tab");
                                        $(".tab-pane").hide();
                                        $('.panel-tabs a[href="' + value + '"]').tab('show');
                                        UpdateTabPositionInSession(tabposition, value, objfields);
                                        selectedtab = $("li.active").text().replace(/ /g, '');
                                        selectedtab = selectedtab.trim();
                                        $("#tabname").text(selectedtab);
                                        //$("#page-loader").hide();
                                        hideProgress();
                                        $("#tab" + selectedtab).show();
                                    }
                                }

                                break;
                            case 1: //1	Inactive	Visible, but locked—the user can’t click the control
                            case 4:  //4	Grayed	Grayed out
                                $("#" + _TabName).show();
                                if (prop.FieldPropertiy.ToolTip != null)
                                    $('#' + _TabName).prop('title', prop.FieldPropertiy.ToolTip);
                                $('#' + _TabName).prop('disabled', true);
                                break;
                            case 2:  //2	Invisible	Invisible
                                $("#" + _TabName).hide();
                                break;
                            default:
                                break;
                        }
                    }
                }
                else if (fieldname.indexOf("CHK_") > -1) {
                    var state = prop.FieldPropertiy.State;
                    if (state != 1 && state != 4) {
                        if ($("#lbl_" + fieldname).length > 0) {
                            $("#lbl_" + fieldname).removeAttr("style");
                        }
                    }
                    switch (state) {
                        case 0://0	Active	Active
                            // $("#div_" + fieldname).show();
                            $("#chkDiv_" + fieldname).show();
                            $("#chkDiv_" + fieldname).css('display', 'table');
                            $("#lbl_" + fieldname).show();
                            $("#" + fieldname).show();
                            $("#lbldiv_" + fieldname).hide();

							$('#' + fieldname).prop('disabled', false);
							if ($('#ilbl_' + fieldname).length > 0) {
								$('#ilbl_' + fieldname).show();
							}
                            break;
                        case 1: //1	Inactive	Visible, but locked—the user can’t click the control
                        case 4:  //4	Grayed	Grayed out
                            $("#lbl_" + fieldname).show();
                            $("#chkDiv_" + fieldname).show();
                            $("#chkDiv_" + fieldname).css('display', 'table');
                            $("#" + fieldname).show();
                            $("#lbldiv_" + fieldname).hide();
                            $('#' + fieldname).prop('disabled', true);
                            $("#lbl_" + fieldname).css({ backgroundColor: '#777' });
                            break;
                        case 2:  //2	Invisible	Invisible
                            $("#lbl_" + fieldname).hide();
                            $("#chkDiv_" + fieldname).hide();
                            $("#chkDiv_" + fieldname).css('display', 'none');
                            $("#" + fieldname).hide();
                            $("#lbldiv_" + fieldname).show();
                            break;
                        default:
                            break;
                    }
                }
                else {
                    //debugger;
                    switch (prop.FieldPropertiy.State) {
                        case 0://0	Active	Active
                            // $("#div_" + fieldname).show();

                            //Set lable color after any event has fired(Ex: MLS_TYPE change)..J
                            if ($('#lbl_' + fieldname).length > 0) {
                                if (prop.FieldPropertiy.LabelColor != "" && prop.FieldPropertiy.LabelColor != null) {
                                    $('#lbl_' + fieldname).css('color', prop.FieldPropertiy.LabelColor);
                                }
                            }

                            if ($("#txtDiv_" + fieldname).length > 0) {
                                $("#txtDiv_" + fieldname).show();
                            }

                            $("#lbl_" + fieldname).show();
                            $("#" + fieldname).show();
                            $("#lbldiv_" + fieldname).hide();

                            $('#' + fieldname).prop('disabled', false);

							$('#' + fieldname).prop('readonly', false);

							if ($('#ilbl_' + fieldname).length > 0) {
								$('#ilbl_' + fieldname).show();
							}
                            break;
                        case 1: //1	Inactive	Visible, but locked—the user can’t click the control
                        case 4:  //4	Grayed	Grayed out
                            $("#lbl_" + fieldname).show();
                            $("#" + fieldname).show();

                            if ($("#txtDiv_" + fieldname).length > 0) {
                                $("#txtDiv_" + fieldname).show();
                            }

                            //Set lable color after any event has fired(Ex: MLS_TYPE change)..J
                            if ($('#lbl_' + fieldname).length > 0) {
                                if (prop.FieldPropertiy.LabelColor != "" && prop.FieldPropertiy.LabelColor != null) {
                                    $('#lbl_' + fieldname).css('color', prop.FieldPropertiy.LabelColor);
                                }
                            }

                            $("#lbldiv_" + fieldname).hide();
                            $('#' + fieldname).prop('disabled', true);
                            break;
                        case 2:  //2	Invisible	Invisible
                            $("#lbl_" + fieldname).hide();
                            $("#" + fieldname).hide();
                            $("#" + fieldname).val('');
                            $("#lbldiv_" + fieldname).show();

                            if ($("#txtDiv_" + fieldname).length > 0) {
                                $("#txtDiv_" + fieldname).hide();
                            }
                            break;
                            //case 4:  //4	Grayed	Grayed out
                            //    $("#div_" + fieldname).show();
                            //    $('#' + fieldname).prop('disabled', true);
                            //    break;
                        default:
                            break;
                    }
                }
            }
        });
    }

    function GetLNKControlValue(controlId) {
        var FieldValue = '';

        if ($("#" + controlId).data("kendoGrid") != null || $("#" + controlId).data("kendoMultiSelect") != null) {

            $.ajax({
                url: "/CreateForm/GetLNKData123",
                cache: false,
                async: false,
                data: { Field: controlId, FormKey: '@Model.FormKey', FormHistoryKey: '@Model.FormHistoryKey' },
                dataType: "json",
                type: "GET",
                contentType: 'application/json',
                success: function (data) {
                    var totalNumber = data.length;
                    if (totalNumber > 0) {
                        FieldValue = "";
                        for (var i = 0; i < totalNumber; i++) {
                            var currentDataItem = data[i];
                            if (i == 0) {
                                FieldValue = currentDataItem.Value;
                            }
                            else {
                                FieldValue = FieldValue + "\r\n" + currentDataItem.Value;
                            }
                        }
                    }
                    else {
                        FieldValue = "";
                    }

                    return FieldValue;
                },
                error: function (request, status, error) {
                    //  alert(request.responseText);
                }
            })

            return FieldValue;
        }

    }

    function GetAllFieldValues(IsLNKField_dblClick) {
        var _allfields = '@Model.AllFieldsCSV';

        var _arrallfields = _allfields.split(',');
        var fields = [];

        $.each(_arrallfields, function (index, value) {

            var i = index;
            var v = value;

            if (value.indexOf("BTN_") < 0) {
                var FieldValue = "undefined";

                if (value.indexOf("TBC_") > -1) {

                    var _tbcVal = value.replace("TBC_", "");
                    if (_tbcVal.indexOf("CHK_") > -1) {

                        if ($("#" + _tbcVal).length > 0) {
                            var _checked = $("#" + _tbcVal).is(":checked");
                            if (_checked == true) {
                                FieldValue = "on"
                            }
                            else {
                                FieldValue = "off";
                            }
                        }
                    }
                }

                else if (value.indexOf("NDB_") > -1) {
                    var _ndbVal = value.replace("NDB_", "");
                    if (_ndbVal.indexOf("CHK_") > -1) {

                        if ($("#" + value).length > 0) {
                            var _checked = $("#" + value).is(":checked");
                            if (_checked == true) {
                                FieldValue = "on"
                            }
                            else {
                                FieldValue = "off";
                            }
                        }
                        else if ($("#" + _ndbVal).length > 0) {
                            var _checked = $("#" + _ndbVal).is(":checked");
                            if (_checked == true) {
                                FieldValue = "on"
                            }
                            else {
                                FieldValue = "off";
                            }
                        }

                    }
                    else if (_ndbVal.indexOf("MMR_") > -1 || _ndbVal.indexOf("MMO_") > -1) {
                        if ($("#" + value).length > 0) {
                            if ($("#" + value).data("kendoEditor")) {
                                FieldValue = $("#" + value).data("kendoEditor").value();
                            }
                        }
                    }
                    else if (_ndbVal.indexOf("MLS_") > -1) {

                        if ($("#" + value).length > 0) {
                            FieldValue = $("#" + value).val();
                        }
                    }
                    else if (_ndbVal.indexOf("SI__") > -1) {
                        if ($("#" + value).length > 0) {
                            FieldValue = $("#" + value).val();
                        }
                    }
                    else if (_ndbVal.indexOf("URL_") > -1 || _ndbVal.indexOf("FIL_") > -1) {
                        var _data = "";
                        if ($("#" + value)) {
                            if ($("#" + value).length > 0) {
                                for (var i = 0; i < $("#" + value)[0].childElementCount; i++) {

                                    if (_data == "") {
                                        _data = $("#" + value)[0][i].text;

                                    }
                                    else {
                                        _data = _data + "\r\n" + $("#" + value)[0][i].text;

                                    }
                                }
                            }
                            else {
                                _data = "undefined";
                            }
                        }
                        else {
                            _data = "undefined";
                        }
                        FieldValue = _data;
                    }
                    else if (_ndbVal.indexOf("LBL_") > -1) {
                        //ignor
                    }
                    else if (_ndbVal.indexOf("EML_") > -1) {
                        //debugger;
                        if ($("#" + value).length > 0) {
                            FieldValue = $("#" + value).val();
                        }
                    }
                    else if (_ndbVal.indexOf("LNK_") > -1) {
                        //debugger
                        if (IsLNKField_dblClick) {
                            return true;
                        }
                        else {
                            FieldValue = GetLNKControlValue(value);
                        }
                    }
                    else if (_ndbVal.indexOf("ADR_") > -1) {
                        FieldValue = GetADR_ControlValue(value);
                    }
                    else {
                        if ($("#" + value)) {
                            FieldValue = $("#" + value).val();
                        }
                    }
                }
                else if (value.indexOf("MMR_") > -1 || value.indexOf("MMO_") > -1) {
                    if ($("#" + value).length > 0) {
                        if ($("#" + value).data("kendoEditor")) {
                            FieldValue = $("#" + value).data("kendoEditor").value();
                        }
                        else {
                            FieldValue = $("#" + value).val();
                        }
                    }
                }
                else if (value.indexOf("MLS_") > -1) {
                    if ($("#" + value).length > 0) {
                        FieldValue = $("#" + value).val();
                    }
                }
                else if (value.indexOf("SI__") > -1) {
                    if ($("#" + value).length > 0) {
                        FieldValue = $("#" + value).val();
                    }
                }
                    //kendoMultiSelect  to kendoGrid
                else if (value.indexOf("LNK_") > -1) {

                    if (IsLNKField_dblClick) {
                        return true;
                    }
                    else {
                        FieldValue = GetLNKControlValue(value);
                    }
                }
                else if (value.indexOf("URL_") > -1 || value.indexOf("FIL_") > -1) {
                    var _data = "";
                    //debugger
                    if ($("#" + value).length > 0) {
                        for (var i = 0; i < $("#" + value)[0].childElementCount; i++) {
                            if (_data == "") {
                                _data = $("#" + value)[0][i].text;

                            }
                            else {
                                _data = _data + "\r\n" + $("#" + value)[0][i].text;

                            }
                        }

                    }
                    else {
                        _data = "undefined";
                    }
                    FieldValue = _data;
                }
                else if (value.indexOf("LBL_") > -1) {
                    //ignor
                }
                else if (value.indexOf("EML_") > -1) {
                    //debugger;
                    if ($("#" + value).length > 0) {
                        FieldValue = $("#" + value).val();
                    }
                }
                else if (value.indexOf("CHK_") > -1) {

                    if ($("#" + value).length > 0) {
                        var _checked = $("#" + value).is(":checked");
                        if (_checked == true) {
                            FieldValue = "on"
                        }
                        else {
                            FieldValue = "off";
                        }
                    }
                }
                else if (value.indexOf("ADR_") > -1) {
                    FieldValue = GetADR_ControlValue(value);
                }
                else {
                    if ($("#" + value).length > 0) {
                        FieldValue = $("#" + value).val();
                    }
                }
                if (FieldValue != "undefined" && FieldValue != undefined) {
                    var field = new Object();
                    field.FieldName = value;
                    field.DBFieldName = value;//to-do for same controls in form
                    field.FieldValue = encodeURI(FieldValue);
                    fields.push(field);
                }

            }
        });
        return fields;
    }

    function GetADR_ControlValue(value) {
        var FieldValue = '';
        $.ajax({
            url: "/CreateForm/GetADR_ControlValue",
            cache: false,
            async: false,
            data: { FieldName: value },
            type: "GET",
            contentType: 'application/json',
            success: function (data) {
                if (data == "data not found")
                    FieldValue = "undefined";
                else if (data != undefined && data != null)
                    FieldValue = data;
            }
        })

        return FieldValue;
    }

    function MessageBoxClick(theevent, thearg, thearg2, close, lnkRefresh, isFromCustomToolbar) {
        //debugger
        showProgress();

        if (navigator.onLine == false) {
            hideProgress();
            alert('Check your Internet Connection');
            return false;
        }

        $('#hdRunScriptControl').val(thearg);
        if (thearg2 != "" && thearg2 != undefined) {
            thearg = thearg + "|" + thearg2
        }
        $("#pnlError").hide();
        $("#PNL_MessageBox").hide();
        var _allfields = '@Model.AllFieldsCSV';
        var data = $("#frmcontent :input");

        var _arrallfields = _allfields.split(',');
        var fields = [];
        //$("#page-loader").show();
        var EventArg = theevent;

        if (theevent == "SAVEANDNAVIGATE") {
            //EventArg = "SAVE";
            EventArg = "BROWSE";
        }

        if (isFromCustomToolbar == undefined || isFromCustomToolbar == null) {
            isFromCustomToolbar = false;
        }
        fields = GetAllFieldValues();

        var objfielditems = JSON.stringify(fields);
        var formkey = '@Model.FormKey';

        $.ajax({
            url: "/CreateForm/DoEvent",
            data: { fields: objfielditems, FormKey: formkey, sEvent: EventArg, sArg: thearg, sArg2: thearg2, lnkRefresh: lnkRefresh, IsFromCustomToolbar: isFromCustomToolbar, Key: '@Model.Key', FormHistoryKey: '@Model.FormHistoryKey' },
            type: 'POST',
            success: function (data) {
                //$("#page-loader").hide();

                //if (data.Type=="") {
                //    data.Type = "TYPE";
                //}
               //debugger

                //&&(data.MessageBox == null && data.MessageBox.MessageBoxDisplay == false && data.MessageBox.IsError == false)

                if (theevent == "SAVE" && data.DetailsPageURL != null && data.DetailsPageURL != "" &&
                    (data.MessageBox == null || (data.MessageBox != null && data.MessageBox.MessageBoxDisplay == false && data.MessageBox.IsError == false))) {
                    var deturl = data.DetailsPageURL;
                    $.ajax({
                        url: '/CreateForm/ClearAllSessions',
                        cache: false,
                        async: false,
                        data: { FormKey: '@Model.FormKey.Replace(" ","")', FormHistoryKey: '@Model.FormHistoryKey' },
                        success: function (data) {
                            if (data == "&amp;") {

                                data = data.Replace("amp;", "&");
                            }
                            window.location.href = data;
                        }
                    });

                   // window.location.href = deturl;
                }

                if (theevent == "RUNSCRIPT" && data.DownloadFileData != null && data.DownloadFileData != "") {
                    var arr = data.DownloadFileData.split("|");

                    var filename = arr[0];
                    var viewname = arr[1];
                    var fieldname = arr[2];
                    var gidid = arr[3];

                    if (filename.includes("^") == true) {
                        var arrfiles = filename.split("^");
                        for (var i = 0; i < arrfiles.length; i++) {
                            var Querystring = "sFileName=" + arrfiles[i] + "&sViewName=" + viewname + "&sGidId=" + gidid + "&sFieldName=" + fieldname + "  ";
                            window.open("/CreateForm/DownloadAzureFile?" + Querystring, "_blank" + i.toString());
                        }
                    }
                    else {
                        var Querystring = "sFileName=" + filename + "&sViewName=" + viewname + "&sGidId=" + gidid + "&sFieldName=" + fieldname + "  ";
                        window.open("/CreateForm/DownloadAzureFile?" + Querystring, "_blank");
                    }

                }
                else if (theevent == "RUNSCRIPT" && data.SendFileData != null && data.SendFileData != "")
                {
                    var arr = data.SendFileData.split("|");
                    var filename = arr[0];
                    var viewname = arr[1];
                    var fieldname = arr[2];
                    var gidid = arr[3];
                    var sTo = arr[4];
                    var sSubject = arr[5];
                    var sBody = arr[6];
                    var sFrom = arr[7];
                    var sCc = arr[8];

                    //show the model with the above fields data
                    //SendMailModal
                    $('#SND_From').val(sFrom);
                    $('#SND_To').val(sTo);
                    $('#SND_Cc').val(sCc);
                    $('#SND_Subject').val(sSubject);
                    $("#SND_Body").data("kendoEditor").value(sBody);
                    $("#hdnViewName").val(viewname);
                    $("#hdnFieldName").val(fieldname);
                    $("#hdnFileName").val(filename);
                    $("#hdnGidId").val(gidid);

                    var _url = "/CreateForm/DownloadAzureFile?sFileName=" + filename + "&sViewName=" + viewname + "&sGidId=" + gidid + "&sFieldName=" + fieldname + "  ";
                    $('#SND_Files').html("<a href=\"#\" onclick=\"window.open('" + _url +"', '_blank');\">" + filename +"</a>");
                    $('#SendMailModal').modal('show');

                }
                //preview pdf
                else if (theevent == "RUNSCRIPT" && data.PreviewFileData != null && data.PreviewFileData != "") {
                    var arr = data.PreviewFileData.split("|");
                    var filename = arr[0];
                    var viewname = arr[1];
                    var fieldname = arr[2];
                    var gidid = arr[3];
                    var Querystring = "sFileName=" + filename + "&sViewName=" + viewname + "&sGidId=" + gidid + "&sFieldName=" + fieldname + "  ";
                    //window.open("/CreateForm/DownloadAzureFile?" + Querystring, "_blank");

                    var _pdfviewer = document.getElementById("SelltispdfPreview");
                    _pdfviewer.src = "/CreateForm/PreviewAzureFile?" + Querystring;

                }

                if (navigator.onLine == false) {
                    hideProgress();
                    alert('Check your Internet Connection');
                    return false;
                }

                if (data.RecordId == "") {
                    data.RecordId = "ID";
                }
                if (data.ErrorMessage != "") {
                    // alert(data.ErrorMessage);
                    var errormessage = "ScriptsError";
                    window.location.href = "/ErrorLog/LogError?sErrorLogs=" + errormessage;

                }
                if (data.ExecuteSendPopup == true) { //Open a popup in Form page S_B
                    //debugger;
                    hideProgress();
                    window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                }
                //debugger
                var isErrorRaised = false;
                DisplayMessageBox(data.MessageBox);
                if (data.MessageBox != null && (data.MessageBox.MessageBoxDisplay == true || data.MessageBox.IsError)) {
                    isErrorRaised = true;
                }

                if (data.MessageBoxPanel != null && data.MessageBoxPanel.MessagePanelDisplay == true) {
                    DisplayMessageBoxPanel(data.MessageBoxPanel);
                    //isErrorRaised = true;
                }
                //else if (data.FileName != "" && data.FileName != null && data.Type != null) {
                //    window.location.href = "/CreateForm/CreateForm/" + data.FileName + "/" + data.RecordId + "/" + data.Type + "/false/false/KEY/false/MASTERID";
                //}
                if ((data.IsNavigate == true && isErrorRaised == false)) {
                    var _navigatetype = data.NavigateType;

                    if (data.Type != null && data.Type != undefined && _navigatetype != "OPENURLEXTERNAL") {
                        if (data.Type.indexOf("CRU_QL") >= 0 && data.FileName == "QL" && _navigatetype == "FORM") {
                            var lastformkey = '@Util.GetSessionValue("LatestFormKey")';
                            if (lastformkey != null) {
                                window.location.href = "/CreateForm/CreateForm/" + data.FileName + "/ID/" + data.Type + "/false/false/KEY/false/MASTERID/" + lastformkey + "/FIELD/" + '@Model.Key';
                            }
                        }
                        else {
                            if (_navigatetype == "FORM") {
                                var lastformkey = '@Util.GetSessionValue("LatestFormKey")';
                                var isnavigatefromcustomtoolbar = '@Util.GetSessionValue(Model.FormHistoryKey + "_" + "IsNavigateFromCustomToolbar")';

                                var masterId = "MASTERID";
                                if ('@Model.Table' == "QT" && data.FileName == "QL" && '@Model.RecordId' != "" && '@Model.RecordId' != null)
                                {
                                    masterId = '@Model.RecordId';
                                }

                                if (lastformkey != null && isnavigatefromcustomtoolbar != null) {
                                    window.location.href = "/CreateForm/CreateForm/" + data.FileName + "/" + data.RecordId + "/" + data.Type + "/false/false/KEY/false/" + masterId + "/" + lastformkey + "/FIELD/" + '@Model.Key';
                                }
                                else {
                                    window.location.href = "/CreateForm/CreateForm/" + data.FileName + "/" + data.RecordId + "/" + data.Type + "/false/false/KEY/false/" + masterId + "/FORMKEY/FIELD/" + '@Model.Key';
                                }
                            }
                            else if (_navigatetype == "DESKTOP") {
                                //load desktop
                                var HistoryKey = data.HistoryKey;
                                if (HistoryKey == null || HistoryKey == "") {
                                    HistoryKey = "HISTORYKEY";
                                }
                                window.location.href = "/Desktop/LoadDesktop/" + data.RecordId + "/false/FOLDERID/" + HistoryKey;
                            }
                            else if (_navigatetype == "DIALOG") {
                                var previousUrl = data.PreviousUrlForSendOption;
                                window.location.href = "/CreateForm/LoadSendOptions?PreviousUrl=" + previousUrl;
                            }
                            else if (_navigatetype == "MAPDIALOG") {
                                var formKey = '@Model.FormKey';
                                //var previousUrl = data.PreviousUrlForSendOption;
                                window.location.href = "/CreateForm/LoadGeoMap?FormKey=" + formKey;
                            }
                            else if (_navigatetype == "RELOAD") {
                                var viewkey = "KEY";
                                if ('@Model.ViewKey' != null && '@Model.ViewKey' != "") {
                                    viewkey = '@Model.ViewKey';
                                }
                                window.location = "/CreateForm/CreateForm/" + data.FileName + "/" + data.RecordId + "/TYPE/false/false/" + viewkey + "/false/MASTERID/FORMKEY/FIELD/" + '@Model.Key' + "/" + '@Model.FormHistoryKey';
                            }
                            else if (_navigatetype == "NDBFORM") {
                                window.location.href = "/CreateForm/CreateForm/" + data.FileName + "/ID/TYPE/false/false/KEY/false/MASTERID/" + '@Model.FormKey' + "/FIELD/" + '@Model.Key';
                            }
                        }
                    }
                    else if (_navigatetype == "DESKTOP") {
                        //load desktop
                        var HistoryKey = data.HistoryKey;
                        if (HistoryKey == null || HistoryKey == "") {
                            HistoryKey = "HISTORYKEY";
                        }
                        window.location.href = "/Desktop/LoadDesktop/" + data.RecordId + "/false/FOLDERID/" + HistoryKey;
                    }
                    else if (_navigatetype == "DIALOG") {
                        var previousUrl = data.PreviousUrlForSendOption;
                        window.location.href = "/CreateForm/LoadSendOptions?PreviousUrl=" + previousUrl;
                    }
                    else if (_navigatetype == "OPENURLEXTERNAL") {
                        if (data.ExternalUrlItems != null && data.ExternalUrlItems != undefined) {
                            var sURL = data.ExternalUrlItems.sFilePath;
                            var sTitle = data.ExternalUrlItems.sTitle;
                            var sParams = data.ExternalUrlItems.sParams;

                            hideProgress();

                            if (sURL.indexOf("mailto:") == 0) {

                                window.location = sURL;
                            }
                            else {
                                var myWin = window.open(sURL, sTitle, sParams);
                                if (window.focus) {
                                    //if EML will error on focus
                                    try { myWin.focus() }
                                    catch (Error) { }
                                }
                            }

                            //window.open(data.ExternalUrlItems.sFilePath, '_blank', data.ExternalUrlItems.sParams);
                        }
                    }
                    else if (_navigatetype == "MAPDIALOG") {
                        var formKey = '@Model.FormKey';
                        //var previousUrl = data.PreviousUrlForSendOption;
                        window.location.href = "/CreateForm/LoadGeoMap?FormKey=" + formKey;
                    }
                    else if (_navigatetype == "RELOAD") {
                        var viewkey = "KEY";
                        if ('@Model.ViewKey' != null && '@Model.ViewKey' != "") {
                            viewkey = '@Model.ViewKey';
                        }
                        window.location = "/CreateForm/CreateForm/" + data.FileName + "/" + data.RecordId + "/TYPE/false/false/" + viewkey + "/false/MASTERID/FORMKEY/FIELD/" + '@Model.Key' + "/" + '@Model.FormHistoryKey';
                    }
                    else if (_navigatetype == "NDBFORM") {
                        window.location.href = "/CreateForm/CreateForm/" + data.FileName + "/ID/TYPE/false/false/KEY/false/MASTERID/" + '@Model.FormKey' + "/FIELD/" + '@Model.Key';
                    }
                    else if (_navigatetype == "SCHEDULE_RECURRENCE") {
                        //debugger;
                        if ($('#BTN_RECURRENCE').length > -1) {
                            $('#BTN_RECURRENCE').parent().show();
                            $('#BTN_RECURRENCE').show();
                            //$('#BTN_RECURRENCE').attr('title', 'Click here to edit recurring appointment settings');
                        }

                        if($('#CHK_RECURRENCE').is(':checked')){
                            $.ajax({
                                url: '/CreateForm/ScheduleRecurrencePartial',
                                type:'Get',
                                data: {FormKey:'@Model.FormKey',Key:'@Model.FormHistoryKey'},
                                cache: false,
                                async:true,
                                success: function (sch_Data) {
                                    $('#AP_Recurrence_Model').modal('show');
                                    $('#viewContentRec').html(sch_Data);
                                    hideProgress();
                                },
                                error: function (xhr) {

                                }
                            });

                        }
                        hideProgress();
                    }

                }
                else if (thearg.toUpperCase().indexOf("AC_FORMCONTROLONCHANGE_BTN_PRINTJOURNAL") >= 0) {
                    hideProgress();
                }
                else {
                    if ($('#CHK_RECURRENCE').length > -1) {
                        //debugger;
                        if (!$('#CHK_RECURRENCE').is(':checked') && $('#BTN_RECURRENCE').length > -1) {
                            $('#BTN_RECURRENCE').parent().hide();
                            $('#BTN_RECURRENCE').hide();
                            //$('#BTN_RECURRENCE').attr('title', 'Click here to edit recurring appointment settings');
                        }
                    }

                    if (data.TabInFocus != null && data.TabInFocus != "" && data.TabInFocus != undefined && thearg.toUpperCase().indexOf("FORMCONTROLONCHANGE_LNK") <= -1) {
                        if ($("#" + data.TabInFocus).length > 0) {
                            $("#" + data.TabInFocus).trigger("click");
                        }
                    }

                    var FieldinFocus = data.FieldInFocus;

                    if (FieldinFocus.trim() != "" && thearg.toUpperCase().indexOf("FORMCONTROLONCHANGE_LNK") <= -1) {
                        var n = FieldinFocus.indexOf("LNK_");
                        if (n != "-1") {
                            //$('#lbl_' + FieldinFocus).addClass("anchoronClick");
                            $("#lbl_" + FieldinFocus).addClass("anchoronClick")
                               .delay(2000)
                               .queue(function () {
                                   $(this).removeClass("anchoronClick");
                                   $(this).dequeue();
                               });
                        }
                        else if (FieldinFocus.indexOf("TXT_") > -1) {
                            var n = FieldinFocus.indexOf("TXT_");
                            if (n != "-1") {
                                if ($('#' + FieldinFocus).length > 0) {
                                    $('#' + FieldinFocus).focus();
                                }
                            }
                        }
                        else if (FieldinFocus.indexOf("MLS_") > -1) {
                            if ($('#' + FieldinFocus).length > 0) {
                                $('#' + FieldinFocus).data("kendoComboBox").input.focus();
                            }
                        }
                        else if (FieldinFocus.indexOf("DTE_") > -1) {
                            if ($('#' + FieldinFocus).length > 0) {
                                var value = $("#" + FieldinFocus).val();
                                if (value == null || value == '' || value == undefined) {
                                    $("#" + FieldinFocus).data("kendoDatePicker").element.focus();
                                    $("#" + FieldinFocus).data("kendoDatePicker").open();
                                }
                            }
                        }
                        else if (FieldinFocus.indexOf("TME_") > -1) {
                            if ($('#' + FieldinFocus).length > 0) {
                                var value = $("#" + FieldinFocus).val();
                                if (value == null || value == '' || value == undefined) {
                                    $("#" + FieldinFocus).data("kendoTimePicker").element.focus();
                                    $("#" + FieldinFocus).data("kendoTimePicker").open();
                                }
                            }
                        }
                        else {
                            $('#' + FieldinFocus).focus();
                        }
                    }
                    else {
                        if (thearg.indexOf("LNK_") > -1) {
                            var inFocus = thearg.substring(thearg.indexOf("LNK_"));
                            if ($("#ilbl_" + inFocus).length > 0) {
                                $("#ilbl_" + inFocus).focus();
                            }
                        }
                    }



                    if (data.ControlState) {
                        RefreshControlsState(data);
                    }
                    if (data.Fields) {
                        RefreshControls(data.Fields);
                        if (data._iFrameData != null) {
                            RefreshIFrame(data._iFrameData);
                        }
                    }

                    if (data.RefreshLinkFields)
                    {
                        RefreshLinkControls(data.RefreshLinkFields);
                    }
                    //debugger;
                    if (data.Title != "") {
                        if ('@Model.RecordSelectorDisplay' != 'True') {
                            $("#divfrmtitle").text(data.Title);
                        }
                    }
                    if (data.OpenURL != null) {
                        if (data.OpenURL != "") {
                            if (data.OpenURL.indexOf("mailto:") == 0) {
                                //parent.location.href= sURL;
                                window.location = data.OpenURL;
                            } else {
                                var myWin = window.open(data.OpenURL, "Selltis");
                                if (window.focus) {
                                    //if EML will error on focus
                                    try { myWin.focus() }
                                    catch (Error) { }
                                }
                            }
                        }
                    }
                    if (theevent == "SAVE" || theevent == 'MESSAGEBOXBUTTONCLICKEDASSAVE') {
                        if (data.MessageBox.IsError == true) {
                            //$("#pnlError").show();
                            //$("#lblError").text(data.MessageBox.ErrorMessage);
                        }
                        else {
                            if (close == false || data.MessageBox.MessageBoxDisplay == true) {
                            }
                            else {
                                //debugger;
                                var formkey = '@Model.FormKey';
                                var fromnew = sessionStorage.getItem("FromLNK_PopUp_CreateNew" + formkey);
                                if (fromnew == "YES") {
                                    sessionStorage.setItem("FromLNK_PopUp_CreateNew" + formkey, "");
                                    sessionStorage.setItem("IsLnkPopup" + formkey, "YES");
                                    sessionStorage.setItem("LNKItem1" + formkey, "Yes");

                                    //window.history.back();
                                }
                                $.ajax({
                                    url: '/CreateForm/ClearFormSession',
                                    cache: false,
                                    async: true,
                                    data: { FormKey: '@Model.FormKey.Replace(" ","")', FormHistoryKey: '@Model.FormHistoryKey' },
                                    success: function (data) {

                                        if (navigator.onLine == false) {
                                            hideProgress();
                                            alert('Check your Internet Connection');
                                            return false;
                                        }

                                        var currentFormkey = '@Model.FormKey';
                                        if (data == 'success') {



                                            var IsFromLNK = sessionStorage.getItem("IsFromLNK" + currentFormkey);

                                            var IsLNKDblClick = sessionStorage.getItem("LNKItem" + '@Util.GetSessionValue("LastFormKey" + Model.FormHistoryKey)');
                                            var IsLNKPopUp_CreateNewClick = sessionStorage.getItem("FromLNK_PopUp_CreateNew" + '@Util.GetSessionValue("LastFormKey" + Model.FormHistoryKey)');

                                            if (IsFromLNK == "YES") {
                                                sessionStorage.setItem("IsFromLNK" + currentFormkey, "")

                                                var _value1 = '@Util.GetSessionValue("DesktopId")';
                                                var _value2 = '@Util.GetSessionValue("FolderID")';

                                                @*var FormHistoryKey = '@Model.FormHistoryKey';
                                                var PreviousUrlFromHistoryItems = GetPreviousUrlFromHistoryItems();*@
                                                window.location.href = '@Model.PreviousURL';
                                            }

                                            else if ((IsLNKDblClick != null && IsLNKDblClick.toUpperCase() == "YES") || (IsLNKPopUp_CreateNewClick != null && IsLNKPopUp_CreateNewClick.toUpperCase() == "YES")) {

                                                if (IsLNKDblClick != null)
                                                    sessionStorage.removeItem("LNKItem" + '@Util.GetSessionValue("LastFormKey"+ Model.FormHistoryKey)');

                                                var FormHistoryKey = '@Model.FormHistoryKey';
                                                var PrevUrl = '@Model.PreviousURL';
                                                var PreviousUrlFromHistoryItems = GetPreviousUrlFromHistoryItems(FormHistoryKey, PrevUrl);

                                                window.location.href = PrevUrl;
                                            }
                                            else {
                                                var IsLnkPopup = sessionStorage.getItem("IsLnkPopup" + currentFormkey);
                                                if (IsLnkPopup == "YES") {
                                                    sessionStorage.setItem("IsFromLNK" + currentFormkey, "YES");
                                                }

                                                var FormHistoryKey = '@Model.FormHistoryKey';
                                                var PrevUrl = '@Model.PreviousURL';
                                                var PreviousUrlFromHistoryItems = GetPreviousUrlFromHistoryItems(FormHistoryKey, PrevUrl);
                                                window.location.href = PreviousUrlFromHistoryItems;


                                                @*window.location.href = '@Model.PreviousURL';*@
                                            }
                                        }
                                    },
                                    error: function (data) {

                                        if (navigator.onLine == false) {
                                            hideProgress();
                                            alert('Check your Internet Connection');
                                            return false;
                                        }

                                        //debugger
                                        hideProgress();
                                        if (data.responseText.length > 0 || data.responseText != undefined || data.responseText != null) {
                                            //alert(data.responseText);
                                        }
                                    }
                                })
                            }
                        }
                    }
                    else if (theevent == "SAVEANDLEAVEOPEN") {
                        //location.reload();
                        if (isErrorRaised == false) {
                            var viewkey = "KEY";
                            if ('@Model.ViewKey' != null && '@Model.ViewKey' != "") {
                                viewkey = '@Model.ViewKey';
                            }

                            NavgField = '@Model.Field';
                            if (NavgField == "" || NavgField == null || NavgField == undefined) {
                                NavgField = "FIELD";
                            }

                            //Clear the previous opened form from history and reopen it as new..J
                            $.ajax({
                                url: '/CreateForm/RemoveCurrentFormFromHistoryPanel',
                                cache: false,
                                async: false,
                                data: { FormKey: '@Model.FormKey.Replace(" ","")', FormHistoryKey: '@Model.FormHistoryKey' },
                                success: function (result) {

                                    if (navigator.onLine == false) {
                                        hideProgress();
                                        alert('Check your Internet Connection');
                                        return false;
                                    }

                                    window.location = "/CreateForm/CreateForm/" + data.FileName + "/" + data.RecordId + "/TYPE/false/false/" + viewkey + "/false/MASTERID/FORMKEY/" + NavgField + "/" + '@Model.Key';   @*+ "/" + '@Model.FormHistoryKey'*@
                                },
                                error: function (result) {
                                    if (navigator.onLine == false) {
                                        hideProgress();
                                        alert('Check your Internet Connection');
                                        return false;
                                    }
                                }
                            })


                        }
                    }
                    else if (theevent == "CHANGESHARESTATE") {
                        //window.location = window.location.pathname;
                        var formkey = '@Model.FormKey';

                        $.ajax({
                            url: "/CreateForm/SharedStatusChanged",
                            data: { FormKey: formkey, FormHistoryKey: '@Model.FormHistoryKey' },
                            cache: false,
                            success: function (result) {
                                $("#createSharedmenu").remove();
                                $("#divSharingStatus").append(result);
                                hideProgress();
                            },
                            error: function (result) {
                                hideProgress();
                            }
                        })
                    }

                    else if (theevent == "SAVEANDNAVIGATE") {
                        if ($("#hdnRecordSelectorSelectedRecordID").val() != undefined && $("#hdnRecordSelectorSelectedRecordID").val() != "") {

                            var RecSelectorRecID = $("#hdnRecordSelectorSelectedRecordID").val();
                            if (isErrorRaised == false) {

                                if ('@Model.ViewKey' != null) {
                                    //Set last selected Record for selection in view when opened
                                    $.ajax({
                                        url: '/CreateForm/RecordSelector_SaveSelectedRecordID',
                                        cache: false,
                                        async: false,
                                        data: { NavigateRecordId: RecSelectorRecID, ViewKey: '@Model.ViewKey', Field: NavField, Key: '@Model.Key' },
                                        success: function (data) {

                                            if (navigator.onLine == false) {
                                                hideProgress();
                                                alert('Check your Internet Connection');
                                                return false;
                                            }
                                        },
                                        error: function (data) {
                                            if (navigator.onLine == false) {
                                                hideProgress();
                                                alert('Check your Internet Connection');
                                                return false;
                                            }
                                        }
                                    })
                                }

                                //Set prev URL as prev of current rec. Helps in opening desktop on close of second record opened through Record Selector. Otherwise earlier record is opened after 2nd record close.
                                $.ajax({
                                    url: '/CreateForm/SetNavigationSession',
                                    cache: false,
                                    async: false,
                                    data: { FileName: '@Model.Table' },
                                    success: function (data) {

                                        if (navigator.onLine == false) {
                                            hideProgress();
                                            alert('Check your Internet Connection');
                                            return false;
                                        }
                                    },
                                    error: function (data) {
                                        if (navigator.onLine == false) {
                                            hideProgress();
                                            alert('Check your Internet Connection');
                                            return false;
                                        }
                                    }
                                })

                                $("#hdnRecordSelectorSelectedRecordID").val("");
                                //    window.location = "/CreateForm/CreateForm/" + table + "/" + RecordID + "/TYPE/false/false/" + viewid + "/false/MASTERID/FORMKEY/FIELD";
                                //window.location = "/CreateForm/CreateForm/" + data.TableName + "/" + $("#hdnRecordSelectorSelectedRecordID").val() + "/TYPE/" + false + "/" + data.LastOn + "/" + data.ViewKey + "/false/" + data.MasterViewRecordId + "/FORMKEY/" + NavField;
                                window.location = "/CreateForm/CreateForm/" + NavTableName + "/" + RecSelectorRecID + "/TYPE/false/false/" + NavViewKey + "/false/" + NavMasterViewRecordId + "/FORMKEY/" + NavField + "/" + '@Model.Key';
                            }
                            else {
                                var combobox = $("#cmbRecordSelector").data("kendoComboBox");
                                combobox.value($("#hdnRecordSelectorCurrentRecordID").val());
                            }
                        }
                        else {
                            if (isErrorRaised == false) {
                                $.ajax({
                                    url: "/CreateForm/GetRecordId",
                                    cache: false,
                                    async: true,
                                    data: { NavigateValue: NavValue, TableName: NavTableName, RecordId: NavRecordId, ViewKey: NavViewKey, MasterViewRecordId: NavMasterViewRecordId, Field: NavField, Key: '@Model.Key' },
                                    type: 'GET',
                                    success: function (data) {

                                        if (navigator.onLine == false) {
                                            hideProgress();
                                            alert('Check your Internet Connection');
                                            return false;
                                        }

                                        $.ajax({
                                            url: '/CreateForm/SetNavigationSession',
                                            cache: false,
                                            async: false,
                                            data: { FileName: '@Model.Table' },
                                            success: function (result) {

                                                if (navigator.onLine == false) {
                                                    hideProgress();
                                                    alert('Check your Internet Connection');
                                                    return false;
                                                }
                                            },
                                            error: function (result) {
                                                if (navigator.onLine == false) {
                                                    hideProgress();
                                                    alert('Check your Internet Connection');
                                                    return false;
                                                }
                                            }
                                        })
                                        if (data.NavigateRecordId != null && data.TableName != "") {

                                            showProgress();

                                            var viewkey = '';
                                            var recordid = '';

                                            if (data.ViewKey == '' || data.ViewKey == null)
                                                viewkey = 'KEY';
                                            else
                                                viewkey = data.ViewKey;
                                            if (data.MasterViewRecordId == '' || data.MasterViewRecordId == null)
                                                recordid = 'MASTERID';
                                            else
                                                recordid = data.MasterViewRecordId;

                                            window.location = "/CreateForm/CreateForm/" + data.TableName + "/" + data.NavigateRecordId + "/TYPE/" + data.FirstOn + "/" + data.LastOn + "/" + viewkey + "/false/" + recordid + "/FORMKEY/" + NavField + "/" + '@Model.Key';
                                        }
                                    },
                                    error: function (request, status, error) {
                                        if (navigator.onLine == false) {
                                            hideProgress();
                                            alert('Check your Internet Connection');
                                            return false;
                                        }
                                        if (request.responseText.length > 0 || request.responseText != undefined || request.responseText != null) {
                                            //alert(request.responseText);
                                        }
                                    }
                                })
                            }
                        }
                    }

                    else if ((theevent == "RUNSCRIPT" && thearg.toUpperCase().indexOf("SENDEMAIL_FORMCONTROLONCHANGE_") > -1) || (theevent == "MESSAGEBOXBUTTONCLICKED" && data.IsNavigateToPrevious == true)) {
                        if (isErrorRaised == false) {
                            window.location.href = '@Model.PreviousURL';
                        }
                    }
                    else {
                        $.ajax({
                            url: '/CreateForm/GetDeleteSessionValue',
                            cache: false,
                            async: true,
                            data: { FormKey: '@Model.FormKey.Replace(" ","")', FormHistoryKey: '@Model.FormHistoryKey' },
                            success: function (data) {
                                if (data == 'success') {
                                    //debugger
                                    if (navigator.onLine == false) {
                                        hideProgress();
                                        alert('Check your Internet Connection');
                                        return false;
                                    }
                                    window.location.href = '@Model.PreviousURL';
                                }
                                else {
                                    if (navigator.onLine == false) {
                                        hideProgress();
                                        alert('Check your Internet Connection');
                                        return false;
                                    }
                                    @*var FormHistoryKey = '@Model.FormHistoryKey';
                                    var PreviousUrlFromHistoryItems = GetPreviousUrlFromHistoryItems(FormHistoryKey);
                                    window.location.href = PreviousUrlFromHistoryItems;*@
                                    hideProgress();
                                }
                            },
                            error: function (data) {
                                if (navigator.onLine == false) {
                                    hideProgress();
                                    alert('Check your Internet Connection');
                                    return false;
                                }
                                hideProgress();
                            }
                        })
                    }
                    //var VisibleContentIdHeight = $('#VisibleContentId').height();
                    //$('#DummyDivForFormContent').height(VisibleContentIdHeight - 5);
                    SettingHeaderHeightWidth();
                    //UserReassign form cancel click issue.
                    if(data.IsNavigateToPrevious)
                    {
                        $('#btnCancel').click();
                    }
                }
            },
            error: function (request, status, error) {
                if (navigator.onLine == false) {
                    hideProgress();
                    alert('Check your Internet Connection');
                    return false;
                }
                // $("#page-loader").hide();
                hideProgress();
                if (request.responseText.length > 0 || request.responseText != undefined || request.responseText != null) {
                    //alert(request.responseText);
                }
            }
        })

    }

    function SendMail()
    {

        var _from = $('#SND_From').val();
        var _to = $('#SND_To').val();
        var _cc = $('#SND_Cc').val();
        var _bcc = $('#SND_Bcc').val();
        var _subject= $('#SND_Subject').val();
        var _body = $("#SND_Body").data("kendoEditor").value();
        var _viewname=  $("#hdnViewName").val();
        var _fieldname= $("#hdnFieldName").val();
        var _filename=$("#hdnFileName").val();
        var _gidid = $("#hdnGidId").val();

        @*var email = new RegExp('^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$');*@


        if (_from == '' || _from == "") {
            alert("Please enter 'From'");
           // $('#SND_From').removeAttr("disabled");
            return false;
        }



        //if (!_from.match(emailPattern)) {
        //    alert("Please enter valid 'From Address'");
        //    return false;
        //}

        if (_to == '' || _to == "") {
            alert("Please enter 'To'");
            return false;
        }
        //if (!_to.match(emailPattern)) {
        //    alert("Please enter valid  'To Address'");
        //    return false;
        //}

        if (_subject == '' || _subject == "") {
            alert("Please enter 'Subject'");
            return false;
        }

        showProgress();

        $.ajax({
            url: '/CreateForm/SendMail',
            cache: false,
            async: false,
            data: { sFrom: _from, sTo: _to, sCC: _cc, sBCC:_bcc, sSubject:_subject, sBody:_body, sGid_id:_gidid, sViewName:_viewname, sFileName:_filename, sFieldName:_fieldname },
            success: function (result) {
                alert("Email has been sent successfully");
                $('#SND_From').val("");
                $('#SND_To').val("");
                $('#SND_Subject').val("");
                $("#SND_Body").data("kendoEditor").value("");
                $("#hdnViewName").val("");
                $("#hdnFieldName").val("");
                $("#hdnFileName").val("");
                $("#hdnGidId").val("");
                $('#SND_Files').html("");
                $('#SendMailModal').modal('hide');
                hideProgress();
            },
            fail: function (xhr, textStatus, errorThrown) {
                hideProgress();
             },
            error: function (data) {
                hideProgress();
                alert("Error in sending email");
            }
        });
    }

    //function GetHistoryItemsCount() {

    //    var count = 0;

    //    $.ajax({
    //        url: '/Common/GetHistoryItemsCount',
    //        cache: false,
    //        async: false,
    //        success: function (result) {
    //            count = result;
    //        },
    //        error: function (request, status, error) {

    //        }
    //    })

    //    return count;

    //}

    function GetPreviousUrlFromHistoryItems(FormHistoryKey, PrevUrl) {

        var PreviousUrl = "";

        $.ajax({
            url: '/CreateForm/RemoveCurrentFormFromHistoryPanelAndGetNextUrl',
            cache: false,
            async: false,
            data: { FormHistoryKey: FormHistoryKey, PrevUrl: PrevUrl },
            success: function (result) {
                PreviousUrl = result;
            },
            error: function (request, status, error) {

            }
        })

        return PreviousUrl;
    }

    function CheckForDuplicates(val) {
        var DuplicateField = "";
        $.ajax({
            type: 'GET',
            cache: false,
            async: false,
            url: '/CreateForm/CheckForDuplicates',
            data: { FieldValue: val },
            success: function (data) {
                DuplicateField = data;
            },
            error: function (request, status, error) {
                //debugger
                if (request.responseText.length > 0 || request.responseText != undefined || request.responseText != null) {
                    //alert(request.responseText);
                }
            }
        })

        return DuplicateField;
    }

    //IMG_MsgBoxClose
    $("#IMG_MsgBoxClose").click(function () {

        $.ajax({
            url: "/CreateForm/RemoveFormMessageBox",
            cache: false,
            data: { FormKey: '@Model.FormKey' },
            success: function (data) {

            },
            error: function (data) {
            }
        })

        $("#PNL_MessageBox").hide();
    });

    //btnSaveandleave
    $("#btnSaveandleave").click(function () {
        MessageBoxClick("SAVEANDLEAVEOPEN", "SAVEANDLEAVEOPEN", "", false);
    });

    $("#btnSaveandClose").click(function () {
        MessageBoxClick("SAVE", "SAVE", "", true);
    });


    function GoDetailsPage(file, gid) {
        if (file == 'CO') {
           // window.location = "/DetailsPage/CompanyDetails/?sCompanyId=" + gid;

            window.location = "/DetailsPage/PRF?sPRPId=PRF_CO&sRecId=" + gid
        }
        else if (file == 'CN') {
            //window.location = "/DetailsPage/ContactDetails/?sContactId=" + gid;
            window.location = "/DetailsPage/PRF?sPRPId=PRF_CN&sRecId=" + gid
        }
        else if (file == 'OP') {
            //window.location = "/DetailsPage/OPDetails/?sOPId=" + gid;
            window.location = "/DetailsPage/PRF?sPRPId=PRF_OP&sRecId=" + gid
        }
    }


    function CHANGESHARESTATE(eventtype, type) {

        //alert(eventtype +" , "+type);
        MessageBoxClick(eventtype, type, "", true);
    }


    //CRL Start

    function CreateLink(tableName, type) {
        ////debugger;

        if (navigator.onLine == false) {
            hideProgress();
            alert('Check your Internet Connection');
            return false;
        }

        showProgress();
        if ('@Model.RecordId' != "" && '@Model.RecordId' != null) {
            window.location = "/CreateForm/CreateForm/" + tableName + "/" + '@Model.RecordId' + "/" + type + "/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + '@Model.Key';
        }
        else {
            window.location = "/CreateForm/CreateForm/" + tableName + "/ID/" + type + "/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + '@Model.Key';
        }
    }


    //function SearchKey() {
    //    ////debugger;
    //    return {
    //        Attachment: $("#sValue").val(),
    //        sFieldName: $("#sField").val(),
    //        GID_ID: $("#GID_ID").val(),
    //        FileName : $("#FileName").val()
    //    };
    //}





    @*function onRemoveClick(NameofFile, sViewName, sGID, FieldValue) {
        ////debugger;
        $.ajax({
            url: "/CreateForm/DeleteAttachments",
            type: "GET",
          //  url: '@Url.Action("CreateForm", "DeleteAttachments")',
            data: { NameofFile: NameofFile, sViewName: sViewName, sGID: sGID, FieldValue: FieldValue },
            success: function (data) {
                ////debugger;
                alert("success");

            },
            error: function (data) {
                ////debugger;
                alert("Fail");

            }
        })
    }*@

    //CRL End

    $('ul.dropdown-menu li').click(function (e) {
        $('#divMoreDropdown').removeClass('open');
    })
    function UpdateDoRS(IsLNKField_dblClick) {

        if (navigator.onLine == false) {
            hideProgress();
            alert('Check your Internet Connection');
            return false;
        }

        var fielditems = [];
        fielditems = GetAllFieldValues(IsLNKField_dblClick);
        var objfields = JSON.stringify(fielditems);

        $.ajax({
            url: '/CreateForm/UpdateDoRS',
            async: false,
            type: 'POST',
            data: { Fields: objfields, FormKey: '@Model.FormKey.Replace(" ","")', FormHistoryKey: '@Model.FormHistoryKey' },
            success: function (data) {
                //debugger;

                if (navigator.onLine == false) {
                    hideProgress();
                    alert('Check your Internet Connection');
                    return false;
                }

            },
            error: function (data) {
                //debugger

                if (navigator.onLine == false) {
                    hideProgress();
                    alert('Check your Internet Connection');
                    return false;
                }

                if (data.responseText.length > 0 || data.responseText != undefined || data.responseText != null) {
                    //alert(data.responseText);
                }
            }
        })
    }
    function sendMail(eml) {
        var isWebkit = 'webkitRequestAnimationFrame' in window;
        if (isWebkit) {
            myWindow = window.open("mailto:" + eml, '', 'width=50,height=50');
            myWindow.close();
        } else {
            window.location.href = "mailto:" + eml;
        }
        //window.location.href = "mailto:" + eml;
    }

    function onMouseOut(obj) {
        if (obj != null && obj != undefined) {
            $(obj).removeClass('btn-bootstrap-hover');
        }
    }

    function onHover(obj) {
        if (obj != null && obj != undefined) {
            var classname = $(obj)[0].className;
            if (classname.indexOf('active') < 0) {
                $(obj).addClass('btn-bootstrap-hover');
            }
        }
    }

	function txtBoxOnBlur(obj) {
		//debugger;
		var ShowtextBox = '@Selltis.Core.Util.GetSessionValue("showFloationtxtbox")';
        var value = $(obj).val();
        $($(obj).parent()).removeClass('fg-toggledcontrol');
        value = value.trim();
		if (value == null || value == '' || value == undefined) {
			if (ShowtextBox == "0") {
				$($(obj).parent()).removeClass('fg-toggled');
			}

            $(obj).val('');
        }

        var txtId = obj.id;
        var val = $('#' + txtId).val();
        var prefix = txtId.substring(0, 4);
        if (prefix == "TXT_") {
            $.ajax({
                url: '/CreateForm/UpdateValueInRowSet',
                async: false,
                cache: false,
                data: { Field: txtId, Value: val, FormKey: '@Model.FormKey', FormHistoryKey: '@Model.FormHistoryKey' },
                success: function (data) {
					if (data != '') {
                        var fields = data.split('|');
                        $.each(fields, function (index, value) {

                            if (val != null && val != '' && val != undefined) {
                                $($('#' + value).parent()).removeClass('fg-toggled');
                                $($('#' + value).parent()).addClass('fg-toggled');
                            }
                            $('#' + value).val(val);
                        })
                    }
                },
                error: function (data) {
                    //alert(data.responseText);
                }
            })
        }
    }

    function txtAreaOnBlur(obj) {

        var value = $(obj).val();
        $($(obj).parent()).removeClass('fg-toggledcontrol');
        value = value.trim();
        if (value == null || value == '' || value == undefined) {
            $($(obj).parent()).removeClass('fg-toggled');
            $(obj).val('');
        }


        var txtId = obj.id;
        var val = $('#' + txtId).val();
        $.ajax({
            url: '/CreateForm/UpdateValueInRowSet',
            async: false,
            cache: false,
            data: { Field: txtId, Value: val, FormKey: '@Model.FormKey', FormHistoryKey: '@Model.FormHistoryKey' },
            success: function (data) {
                if (data != '') {
                    var fields = data.split('|');
                    $.each(fields, function (index, value) {
                        if (val != null && val != '' && val != undefined) {
                            $($('#' + value).parent()).removeClass('fg-toggled');
                            $($('#' + value).parent()).addClass('fg-toggled');
                        }
                        $('#' + value).val(val);
                    })
                }
            },
            error: function (data) {
                //alert(data.responseText);
            }
        })
    }

    function MMO_txtAreaOnBlur(obj) {

        if (navigator.onLine == false) {
            hideProgress();
            alert('Check your Internet Connection');
            return false;
        }

        var value = $(obj).val();
        $($(obj).parent()).removeClass('fg-toggledcontrol');
        value = value.trim();
        if (value == null || value == '' || value == undefined) {
            $($(obj).parent()).removeClass('fg-toggled');
            $(obj).val('');
        }


        var txtId = obj.id;
        var val = $('#' + txtId).val();
        $.ajax({
            url: '/CreateForm/UpdateValueInRowSet',
            async: false,
            cache: false,
            data: { Field: txtId, Value: val, FormKey: '@Model.FormKey', FormHistoryKey: '@Model.FormHistoryKey' },
            success: function (data) {

                if (navigator.onLine == false) {
                    hideProgress();
                    alert('Check your Internet Connection');
                    return false;
                }

                if (data != '') {
                    var fields = data.split('|');
                    $.each(fields, function (index, value) {
                        if (val != null && val != '' && val != undefined) {
                            $($('#' + value).parent()).removeClass('fg-toggled');
                            $($('#' + value).parent()).addClass('fg-toggled');
                        }
                        $('#' + value).val(val);
                    })
                }
            },
            error: function (data) {

                if (navigator.onLine == false) {
                    hideProgress();
                    alert('Check your Internet Connection');
                    return false;
                }

                //alert(data.responseText);
            }
        })
    }

    function FillCorrespondentMLS_Controls(obj) {

        if (navigator.onLine == false) {
            hideProgress();
            alert('Check your Internet Connection');
            return false;
        }

        if (obj) {
            var txtId = obj.element[0].id;
            var val = $('#' + txtId).val();
            $.ajax({
                url: '/CreateForm/UpdateValueInRowSet',
                async: false,
                cache: false,
                data: { Field: txtId, Value: val, FormKey: '@Model.FormKey', FormHistoryKey: '@Model.FormHistoryKey' },
                success: function (data) {

                    if (navigator.onLine == false) {
                        hideProgress();
                        alert('Check your Internet Connection');
                        return false;
                    }

                    if (data != '') {
                        var fields = data.split('|');
                        $.each(fields, function (index, value) {
                            if ($('#' + value).length > 0) {
                                $('#' + value).data('kendoComboBox').value(val);
                            }
                        })
                    }
                },
                error: function (data) {
                    //alert(data.responseText);
                }
            })
        }
    }


    function FillCorrespondentDatePicker_Controls(obj) {

        if (navigator.onLine == false) {
            hideProgress();
            alert('Check your Internet Connection');
            return false;
        }

        if (obj) {
            var txtId = obj.element[0].id;
            var val = $('#' + txtId).val();
            $.ajax({
                url: '/CreateForm/UpdateValueInRowSet',
                async: false,
                cache: false,
                data: { Field: txtId, Value: val, FormKey: '@Model.FormKey', FormHistoryKey: '@Model.FormHistoryKey' },
                success: function (data) {

                    if (navigator.onLine == false) {
                        hideProgress();
                        alert('Check your Internet Connection');
                        return false;
                    }

                    if (data != '') {
                        var fields = data.split('|');
                        $.each(fields, function (index, value) {
                            if ($('#' + value).length > 0) {
                                $('#' + value).data('kendoDatePicker').value(val);
                            }
                        })
                    }
                },
                error: function (data) {
                    //alert(data.responseText);
                }
            })
        }
    }

    //$(".k-input").focus(function (e) {
    //    $($(this).parent().parent().parent()).addClass('fg-toggled');
    //    $($(this).parent().parent().parent()).addClass('fg-toggledcontrol');
    //});
    //$(".k-input").blur(function (e) {
    //    var value = $(this).val();
    //    $($(this).parent().parent().parent().parent()).removeClass('fg-toggledcontrol');
    //    value = value.trim();
    //    if (value == null || value == '' || value == undefined) {
    //        $($(this).parent().parent().parent().parent()).removeClass('fg-toggled');
    //        $(this).val('');
    //    }
    //});
    function FormPropertiesClick(tablename, formkey) {
        showProgress();
        window.location.href = '/ManageForms/AddOrEditForm?Mode=EDIT&Form=FRM_' + tablename + '&FormKey=' + formkey + "&FormHistoryKey=" + '@Model.FormHistoryKey';
    }

    //Related to show form metadata in options click (starts here)..J
    function FormMetadataClick(Section, Page) {

        $.ajax({
            url: '/CreateForm/ShowFormMetaData',
            data: { Section: Section, Page: Page },
            success: function (result) {
                var model = $("#ModalFormMetaData");

                $('#txtFormMetadata').val(result.ViewMetaData);
                $('#txtSection').val(result.Section);
                $("#txtPage").val(result.PageId);
                $("#txtPage").focus();
                model.modal('show');
            }
        })
    }

    function onBTN_GenerateSUIDClick() {

        var _page = $("#txtPage").val();
        if (_page != "") {

            $("#LBL_MsgBoxTitle_ForMeta").text("Selltis");
            $("#LBL_MsgBoxMessage_ForMeta").text("Delete the Page value first.");
            $("#PNL_MessageBox_ForFormMeta").show();
            $("#BTN_MsgBox1_ForMeta").val("OK");
            $("#BTN_MsgBox1_ForMeta").show();

            return;
        }

        $.ajax(
        {
            type: 'POST',
            url: "/BrowseMetadata/BTN_GenerateSUID_Click",
            dataType: 'json',
            success: function (result) {
                $("#txtPage").val(result);
            },
            error: function (result) {
            }
        })

    }

    function BTN_MsgBox1_ForMeta_Click() {
        $("#PNL_MessageBox_ForFormMeta").hide();
    }
    function BTN_MsgBox2_ForMeta_Click() {
        $("#PNL_MessageBox_ForFormMeta").hide();
    }

    function BTN_MsgBox3_ForMeta_Click() {
        $("#PNL_MessageBox_ForFormMeta").hide();
    }

    $("#IMG_MsgBoxCloseForMeta").click(function () {
        $("#PNL_MessageBox_ForFormMeta").hide();
    });

    $("#btn_form_metadata_Save").click(function () {

        var Section = $("#txtSection").val();
        var PageId = $("#txtPage").val();
        var MetaData = $("#txtFormMetadata").val();
        var Product_val = "SA";

        var PassData = { Section: Section, PageId: PageId, Product_val: Product_val, MetaData: MetaData }

        MetaData = JSON.stringify(MetaData)
        $.ajax({
            url: '/Desktop/SaveMetaData',
            data: { PassData: JSON.stringify(PassData) },
            type: 'POST',
            dataType: "json",
            success: function (data) {

                if (data == "True") {
                    $("#ModalFormMetaData").modal('hide');
                    @*window.location.href = "/Desktop/LoadDesktop?DesktopId=" + '@Util.GetSessionValue("DesktopId").ToString()';*@
                }
                if (data.PNL_MessageBoxVisible == true) {

                    $("#PNL_MessageBox_ForFormMeta").show();
                    $("#LBL_MsgBoxTitle_ForMeta").text(data.LBL_MsgBoxTitleText);
                    var _LBL_MsgBoxMessage = data.LBL_MsgBoxMessageText.replace("/n/n", "</br></br>").replace("/n", "</br>");
                    $("#LBL_MsgBoxMessage_ForMeta").html(_LBL_MsgBoxMessage);

                    if (data.BTN_MsgBox1Visible == true) {
                        $("#BTN_MsgBox1_ForMeta").show();

                        $("#BTN_MsgBox1_ForMeta").attr('value', data.BTN_MsgBox1Text);
                        $("#BTN_MsgBox1_ForMeta").prop('value', data.BTN_MsgBox1Text);
                        $("#BTN_MsgBox1_ForMeta").html(data.BTN_MsgBox1Text);
                    }
                    else {
                        $("#BTN_MsgBox1_ForMeta").hide();
                    }

                    if (data.BTN_MsgBox2Visible == true) {
                        $("#BTN_MsgBox2_ForMeta").show();

                        $("#BTN_MsgBox2_ForMeta").attr('value', data.BTN_MsgBox2Text);
                        $("#BTN_MsgBox2_ForMeta").prop('value', data.BTN_MsgBox2Text);
                        $("#BTN_MsgBox2_ForMeta").html(data.BTN_MsgBox2Text);
                    }
                    else {
                        $("#BTN_MsgBox2").hide();
                    }

                    if (data.BTN_MsgBox3Visible == true) {
                        $("#BTN_MsgBox3_ForMeta").show();

                        $("#BTN_MsgBox3_ForMeta").attr('value', data.BTN_MsgBox3Text);
                        $("#BTN_MsgBox3_ForMeta").prop('value', data.BTN_MsgBox3Text);
                        $("#BTN_MsgBox3_ForMeta").html(data.BTN_MsgBox3Text);
                    }
                    else {
                        $("#BTN_MsgBox3_ForMeta").hide();
                    }

                    $("#txtFormMetadata").focus();

                }
            }
        });

    })
    //Related to show form metadata in options click (ends here)..J

    function BackToDesignClick(tablename) {
        showProgress();
        window.location.href = '/ManageForms/AddOrEditForm?Mode=EDIT&Form=FRM_' + tablename;
    }
    $('#txtSelectFilter').keypress(function (e) {
        var key = e.which;
        if (key == 13)  // the enter key code
        {
            btnSelectFilter(e);
        }
    });
    // to set focus on LNK quick search box.
    $('#LNKRelatedModal').on('shown.bs.modal', function () {
        $('#txtSelectFilter').focus();
    })

    function AutomatorToolBarContent(workarea, sevent) {
        if (workarea == "SiteRunScript") {
            AutomatorToolBarRunScript("RunScript", sevent, undefined, true);
        }
        else if (workarea == "SiteWorkarea") {
            var eventSplit = null;
            if (sevent.indexOf('|') >= 0) {
                var eventSplit = sevent.split('|');
                if (eventSplit[0] == "NDBFORM") {
                    showProgress();
                    $.ajax({
                        url: "/CreateForm/LoadNDBScript",
                        data: { sEvent: sevent },
                        type: 'POST',
                        success: function (data) {
                            if (data != "" && data != null && data != undefined) {
                                //window.location = "/CreateForm/CreateForm/" + data + "/ID/TYPE/false/false/KEY/false/MASTERID/FORMKEY";

                                if (data == "FIND") {
                                    $.ajax({
                                        url: "/CreateForm/ClearExistingNewSession",
                                        async: true,
                                        cache: false,
                                        data: { File: 'FIND', Type: 'FIND', IsSubNode: '' },
                                        success: function (data) {

                                            if (navigator.onLine == false) {
                                                hideProgress();
                                                alert('Check your Internet Connection');
                                                return false;
                                            }

                                            if (data == "success") {
                                                window.location = "/CreateForm/CreateForm/" + data + "/ID/TYPE/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + '@Model.Key';
                                            }
                                            else {
                                                window.location.href = "/CreateForm/CreateForm/" + data + "/ID/TYPE/false/false/KEY/false/MASTERID/" + data + "/FIELD/" + '@Model.Key';
                                            }
                                        },
                                        error: function (statusCode, errorThrown) {

                                            if (navigator.onLine == false) {
                                                hideProgress();
                                                alert('Check your Internet Connection');
                                                return false;
                                            }
                                        }
                                    })
                                }
                                else {
                                    window.location = "/CreateForm/CreateForm/" + data + "/ID/TYPE/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + '@Model.Key';
                                }

                            }
                            else {
                                hideProgress();
                            }
                        }
                    })
                }
            }

        }
        else {

        }
    }
    function AutomatorToolBarRunScript(runscript, event, arg, isfromautomatortoolbar) {
        showProgress();
        if (isfromautomatortoolbar == undefined) {
            isfromautomatortoolbar = false;
        }
        $.ajax({
            url: "/Desktop/DoEvent",
            data: { sRunscript: runscript, sEvent: event, sArg: arg, IsFromAutomatorToolbar: isfromautomatortoolbar },
            type: 'POST',
            success: function (data) {

                if (data != null && data != undefined && data != "") {
                    if (data.ErrorMessage != "") {
                        var errormessage = "ScriptsError";
                        //showProgress();
                        window.location.href = "/ErrorLog/LogError?sErrorLogs=" + errormessage;
                    }
                    else if (data.IsNavigate == true) {
                        var _navigatetype = data.NavigateType;
                        //showProgress();
                        if (_navigatetype == "FORM") {
                            window.location.href = "/CreateForm/CreateForm/" + data.FileName + "/" + data.RecordId + "/" + data.Type + "/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + '@Model.Key';
                        }
                        else if (_navigatetype == "DESKTOP") {
                            window.location.href = "/Desktop/LoadDesktop/" + data.RecordId + "/false/FOLDERID/HISTORYKEY";
                        }
                    }
                    else {
                        if (data.MessageBox.MessageBoxDisplay == true) {
                            DisplayMessageBox(data.MessageBox);
                            hideProgress();
                        }
                        else {
                            var desktopid = '@Util.GetSessionValue("DesktopId")';
                            var folderid = '@Util.GetSessionValue("FolderID")';
                            var _HistoryKey = '@Util.GetSessionValue("LastDesktopHistoryKey")';

                            if (folderid == undefined || folderid == null || folderid == "")
                                folderid = "FOLDERID";

                            if (_HistoryKey == undefined || _HistoryKey == null || _HistoryKey == "")
                                _HistoryKey = "HISTORYKEY";

                            if (desktopid != null && desktopid != undefined && desktopid != "") {
                                // showProgress();
                                window.location.href = "/Desktop/LoadDesktop/" + desktopid + "/false/" + folderid + "/" + _HistoryKey;
                            }
                            else {
                                window.location.href = '/Login/LoginSubmit';
                            }

                            $("#PNL_MessageBox_Desktop").hide();
                        }

                    }
                }
            },
            error: function (data) {
                hideProgress();
                //alert(data.responseText);
            }
        })
    }

    function CheckScrollBar(IsRowCountGreaterThanTopCount) {

        if (IsRowCountGreaterThanTopCount != null && IsRowCountGreaterThanTopCount == true) {

            var htmlContent = $("html").height(); // get the height of your content
            var FormContent = $("#content").height();  // get the height of the visitor's browser window

            var tabname = $("li.active").text().replace(/ /g, '').replace('\n', '');

            if (FormContent <= (htmlContent - 60)) {

                var renderedrows = sessionStorage.getItem('renderedrows');
                if (renderedrows == null || renderedrows == undefined) {
                    renderedrows = 30;
                    sessionStorage.setItem('renderedrows', '30');
                }
                else {
                    renderedrows = parseInt(renderedrows) + 10;
                    sessionStorage.setItem('renderedrows', renderedrows);
                }

                //$("#page-loader").show();
                showProgress();
                $.ajax({
                    url: "/CreateForm/CreateFormHTML",
                    data: {
                        tabname: tabname,
                        tabId: -1,
                        FormKey: '@Model.FormKey',
                        itop: renderedrows,
                        IsHeaderControlsLoad: false,
                        FormHistoryKey: '@Model.FormHistoryKey'
                    },
                    type: 'post',
                    success: function (data) {
                        $(".tab-content").append(data.TabData);
                        //$("#page-loader").hide();
                        hideProgress();
                        CheckScrollBar(data.IsRowCountGreaterThanTopCount);
                    },
                    error: function (data) {
                        //$("#page-loader").hide();
                        hideProgress();
                        if (data.responseText.length > 0) {
                            //alert(data);
                        }
                    }
                })
            }
        }
    }



    $(document).ready(function () {
        //Setting Header & Tabstrip width.. S1
        setTimeout(SettingHeaderHeightWidth, 200);

        //Setting DummyDiv height to show tabstrip ContentDiv on scroll moves.. S1
        //var VisibleContentIdHeight = $('#VisibleContentId').height();
        //$('#DummyDivForFormContent').height(VisibleContentIdHeight - 5);


        //Kendo editor EditArea Paragraph linebreak set to false.. S1
        //var defaultTools = kendo.ui.Editor.defaultTools;

        //defaultTools["insertLineBreak"].options.shift = false;
        //defaultTools["insertParagraph"].options.shift = true;

        $(window).unload(function() {
            UpdateDoRS(true);
        });
    });

    function ManageRowStyle(IsShowHeaderRow) {
        if (IsShowHeaderRow) {
            $("#secondtoolbar").removeClass();
            $("#secondtoolbar").addClass("col-md-4 AddMarginClass");

            $("#secondtoolbar .pull-right").removeClass('customToolbarClass2');
            $("#secondtoolbar .pull-right").addClass("customToolbarClass1");
        }
        else {
            $("#secondtoolbar").removeClass();
            $("#secondtoolbar").addClass("col-md-12");

            $("#secondtoolbar .pull-right").removeClass('customToolbarClass1');
            $("#secondtoolbar .pull-right").addClass("customToolbarClass2");
        }

        //var VisibleContentIdHeight = $('#VisibleContentId').height();
        //$('#DummyDivForFormContent').height(VisibleContentIdHeight - 5);
        SettingHeaderHeightWidth();
    }

    //Setting Form Header width according window width on Collapsing/Expanding Sid\e Menu Bar..S1
    $('#toggle_sidemenu_l').click(function () {
        setTimeout(SettingHeaderHeightWidth, 200);
    });

    //Set Responsive Header Height and width..S1
    function SettingHeaderHeightWidth() {
        var SideMenuWidth = $('#sidebar_left').width();
        var winWidth = $(window).width();
        $('#VisibleContentId').width(winWidth - SideMenuWidth);

        var VisibleContentIdHeight = $('#VisibleContentId').height();
        $('#DummyDivForFormContent').height(VisibleContentIdHeight - 5);
    }


    $(window).resize(function () {
        setTimeout(SettingHeaderHeightWidth(), 200);
    });

    document.onkeydown = function (e) {

        var keyCode = e.which || e.keyCode;

        if (keyCode == 13) {
            if (e.srcElement != undefined && e.srcElement != null && e.srcElement.nodeName != null && e.srcElement.id != null && e.srcElement.className != null && e.srcElement.id.indexOf('BTN_') < 0 && e.srcElement.id.indexOf('NDB_') < 0 && e.srcElement.id != "txtSelectFilter" && e.srcElement.id.toUpperCase().indexOf('ILBL_LNK_') < 0 && e.srcElement.className.toUpperCase().indexOf('K-INPUT') < 0) {
                if (e.srcElement.nodeName.toUpperCase().indexOf("TEXTAREA") < 0) {
                    $("#btnSaveandClose").click();
                }
            }
            else if (e.originalTarget != undefined && e.originalTarget != null && e.originalTarget.nodeName != null && e.originalTarget.id != null && e.originalTarget.className != null && e.originalTarget.id.indexOf('BTN_') < 0 && e.originalTarget.id.indexOf('NDB_') < 0 && e.originalTarget.id != "txtSelectFilter" && e.originalTarget.id.toUpperCase().indexOf('ILBL_LNK_') < 0 && e.originalTarget.className.toUpperCase().indexOf('K-INPUT') < 0) {
                if (e.originalTarget.nodeName.toUpperCase().indexOf("TEXTAREA") < 0) {
                    $("#btnSaveandClose").click();
                }
            }
        }
    }

    //tickt #2215: close the current form when click on ESC button..J
    $(document).keyup(function (e) {
        if (e.keyCode == 27) {
            if (!$('#LNKRelatedModal').hasClass('in')) {
                onCancel();
            }
        }
	});

	//SB Quick Add Edit event funtionality.
	function DskRunScript(fieldName, fieldValue, fieldControl) {
		//debugger;
		var filename = sessionStorage.getItem("type");;

		if (fieldValue == '') {
			fieldValue = $('#' + fieldName).val();
		}

		$.ajax({
			url: '/Desktop/DskRunScript',
			type: 'POST',
			async: false,
			cache: false,
			data: {
				FieldName: fieldName, FieldValue: fieldValue, FieldControl: fieldControl
			},
			success: function (result) {
				//debugger;
			},
			error: function (xhr) {
				//debugger;
				//alert("error");
			}
		});
    }

    function openrecord(tablename, gid_id) {

        $.ajax({
            url: '/CreateForm/RemoveCurrentFormFromHistoryPanel',
            cache: false,
            async: true,
            data: { FormKey: '@Model.FormKey.Replace(" ","")', FormHistoryKey: '@Model.FormHistoryKey' },
            success: function (data) {

                if (data != null && data != "" && data != undefined) {
                    window.location = "/CreateForm/CreateForm/" + tablename + "/" + gid_id + "/TYPE/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + '@Model.Key';
                }
            },
            error: function (request, status, error) {

            }
        })


    }

</script>
