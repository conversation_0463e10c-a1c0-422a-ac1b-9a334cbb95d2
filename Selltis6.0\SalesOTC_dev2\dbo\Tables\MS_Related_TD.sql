﻿CREATE TABLE [dbo].[MS_Related_TD] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Message_Related_ToDo_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_MS] UNIQUEIDENTIFIER NOT NULL,
    [G<PERSON>_TD] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MS_Related_TD] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_MS_Related_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_Connected_MS] FOREIGN KEY ([GID_MS]) REFERENCES [dbo].[MS] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MS_Related_TD] NOCHECK CONSTRAINT [LNK_MS_Related_TD];


GO
ALTER TABLE [dbo].[MS_Related_TD] NOCHECK CONSTRAINT [LNK_TD_Connected_MS];


GO
CREATE CLUSTERED INDEX [IX_TD_Connected_MS]
    ON [dbo].[MS_Related_TD]([GID_MS] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MS_Related_TD]
    ON [dbo].[MS_Related_TD]([GID_TD] ASC);

