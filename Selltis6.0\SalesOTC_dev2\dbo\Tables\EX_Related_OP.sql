﻿CREATE TABLE [dbo].[EX_Related_OP] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Expense_Related_Opp_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_EX] UNIQUEIDENTIFIER NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_EX_Related_OP] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_EX_Related_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_Connected_EX] FOREIGN KEY ([GID_EX]) REFERENCES [dbo].[EX] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[EX_Related_OP] NOCHECK CONSTRAINT [LNK_EX_Related_OP];


GO
ALTER TABLE [dbo].[EX_Related_OP] NOCHECK CONSTRAINT [LNK_OP_Connected_EX];


GO
CREATE CLUSTERED INDEX [IX_OP_Connected_EX]
    ON [dbo].[EX_Related_OP]([GID_EX] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_Related_OP]
    ON [dbo].[EX_Related_OP]([GID_OP] ASC);

