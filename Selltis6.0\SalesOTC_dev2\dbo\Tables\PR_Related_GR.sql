﻿CREATE TABLE [dbo].[PR_Related_GR] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Project_Related_Group_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PR] UNIQUEIDENTIFIER NOT NULL,
    [GID_GR] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PR_Related_GR] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_GR_Related_PR] FOREIGN KEY ([GID_PR]) REFERENCES [dbo].[PR] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PR_Related_GR] FOREIGN KEY ([GID_GR]) REFERENCES [dbo].[GR] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PR_Related_GR] NOCHECK CONSTRAINT [LNK_GR_Related_PR];


GO
ALTER TABLE [dbo].[PR_Related_GR] NOCHECK CONSTRAINT [LNK_PR_Related_GR];


GO
CREATE CLUSTERED INDEX [IX_GR_Related_PR]
    ON [dbo].[PR_Related_GR]([GID_PR] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_Related_GR]
    ON [dbo].[PR_Related_GR]([GID_GR] ASC);

