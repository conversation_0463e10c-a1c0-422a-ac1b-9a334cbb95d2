﻿CREATE TABLE [dbo].[OP_Related_GR] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Opp_Related_Group_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    [GID_GR] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_OP_Related_GR] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_GR_Related_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_Related_GR] FOREIGN KEY ([GID_GR]) REFERENCES [dbo].[GR] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[OP_Related_GR] NOCHECK CONSTRAINT [LNK_GR_Related_OP];


GO
ALTER TABLE [dbo].[OP_Related_GR] NOCHECK CONSTRAINT [LNK_OP_Related_GR];


GO
CREATE CLUSTERED INDEX [IX_GR_Related_OP]
    ON [dbo].[OP_Related_GR]([GID_OP] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_Related_GR]
    ON [dbo].[OP_Related_GR]([GID_GR] ASC);

