﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using System.Xml;
using System.Diagnostics;
using System.Net;


namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;
        private clLog goLog;
        private clDefaults goDef;
        //private clScrMng goScr;
        ScriptManager scriptManager = new ScriptManager();
        private ClUI goUI;
        private clPerm goPerm;
        private clHistory goHist;
        public string sError;

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";

        System.Data.SqlClient.SqlConnection par_oConnection = null;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();

        }
        public ScriptsCustom()
        {
            Initialize();
        }


        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}


            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }

        public bool AC_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            Form doForm = (Form)par_doCallingObject;

            doForm.SetFieldProperties("BTN_INSERTPRODQS", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend Product Questions");

            par_doCallingObject = doForm;
            return true;
        }

        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            //doForm.SetControlState("TAB_FORM[1]", 0);
            par_doCallingObject = doForm;
            return true;
        }

        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.


            // MI 1/4/08 Changed declaration to Dim bMailList As Boolean = True per CS.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            par_bRunNext = false;
            Form doForm = (Form)par_doCallingObject;
            bool bResult;

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillEndTimeIfBlank"))
            {
                //Fill end time with Now if blank.
                if (string.IsNullOrEmpty(doForm.doRS.GetFieldVal("DTT_EndTime", clC.SELL_FRIENDLY).ToString()))
                {
                    int par_iValid = 4;
                    //	gop.traceline("Filling end time with start time.","",sProc)
                    DateTime goTrDatetime = Convert.ToDateTime(Microsoft.VisualBasic.Strings.Format(goTR.NowLocal(), "HH:mm"));
                    doForm.doRS.SetFieldVal("TME_ENDTIME", goTR.TimeToString(goTrDatetime, "", ref par_iValid), 1);
                    //doForm.dors.SetFieldVal("DTE_ENDTIME", goTR.DateToString(Format(goTR.NowLocal(), "yyyy-MM-dd")), 1)
                    doForm.doRS.SetFieldVal("DTE_ENDTIME", doForm.doRS.GetFieldVal("DTE_StartTime"), 1);
                }
            }

            //CS: If only end time is blank, fill with Now
            //If doForm.dors.GetFieldVal("TME_EndTime", clC.SELL_FRIENDLY) = "" Then
            //    doForm.dors.SetFieldVal("TME_ENDTIME", goTR.TimeToString(Format(goTR.NowLocal(), "HH:mm")), 1)
            //End If

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceValidEndTime"))
            {
                //If End Time is < start time AND end date is = to start date, give message to user to enter a valid end time. For now just
                //validation,later with messaging can give a better message to user.
                if (Convert.ToDateTime(doForm.doRS.GetFieldVal("TME_Endtime", clC.SELL_FRIENDLY)) < Convert.ToDateTime(doForm.doRS.GetFieldVal("TME_StartTime", clC.SELL_FRIENDLY)) & doForm.doRS.GetFieldVal("dte_endtime") == doForm.doRS.GetFieldVal("dte_starttime"))
                {
                    doForm.MoveToField("TME_EndTime");
                    // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "TME_EndTime"), "", "", "", "", "", "", "", "", "TME_EndTime")
                    goErr.SetWarning(30200, sProc, "", "The end time is before the start time.", "", "", "", "", "", "", "", "", "TME_EndTime");
                    return false;
                }
            }

            par_doCallingObject = doForm;

            return true;
        }
        public bool AC_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = default(clRowSet);
            //object
            clArray doLink = default(clArray);
            //object
            string sFileName = "AC";
            string sSysName = "";
            bool bResult = false;
            string sJournal = null;
            string sDateStamp = "";

            par_bRunNext = false;
            doRS = (clRowSet)par_doCallingObject;

            //Fill End datetime with Start datetime if blank
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillEndTimeIfBlank"))
            {
                if (string.IsNullOrEmpty(doRS.GetFieldVal("DTT_EndTime", clC.SELL_FRIENDLY).ToString()))
                {
                    //	gop.traceline("Filling end time with start time.","",sProc)
                    doRS.SetFieldVal("DTT_EndTime", doRS.GetFieldVal("DTT_StartTime", 2), 2);
                }
            }

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "AutoFillYearMonthDay"))
            {
                DateTime dtDateTime = default(DateTime);
                DateTime dttstarttime = Convert.ToDateTime(doRS.GetFieldVal("DTT_STARTTIME", 2));
                dtDateTime = goTR.UTC_LocalToUTC(ref dttstarttime);
                int par_iValid = 4;
                if (goData.IsFieldValid("AC", "TXT_StartTimeYear"))
                    doRS.SetFieldVal("TXT_StartTimeYear", goTR.GetYear(dtDateTime));
                if (goData.IsFieldValid("AC", "SI__StartTimeMonth"))
                    doRS.SetFieldVal("SI__StartTimeMonth", goTR.StringToNum(goTR.GetMonth(dtDateTime), "", ref par_iValid));
                if (goData.IsFieldValid("AC", "SI__StartTimeDay"))
                    doRS.SetFieldVal("SI__StartTimeDay", goTR.StringToNum(goTR.GetDay(dtDateTime), "", ref par_iValid));
            }

            if (goP.GetRunMode() != "Import")
            {
                //CS 2/4/09: Removing Email Alias code. This is handled in 
                //clEmail.LogMessage which calls GetEmailAliasLinks
                //if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillEmailAliasLink"))
                //{
                //    par_doCallingObject = doRS;
                //    bResult = scriptManager.RunScript("Activity_EmailAlias", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                //    doRS = (clRowSet)par_doCallingObject;
                //    if (!string.IsNullOrEmpty(doRS.oVar.GetVar("EL_ID").ToString()))
                //    {
                //        doRS.SetFieldVal("LNK_Related_EL", doRS.oVar.GetVar("EL_ID"));
                //    }
                //}




                //*** MI 11/27/07 This is done further down
                //If scriptManager.IsSectionEnabled(sProc, par_sSections, "FillUserLinks") Then
                //    scriptManager.RunScript("Activity_FillEmplConns", doRS)
                //End If

                //----- Set the Related Territory connection from Related Company's territory
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillTerritoryFromCompanyTerritory"))
                {
                    doRS.ClearLinkAll("LNK_Related_TE");
                    doLink = new clArray();
                    DataTable oTable = null;  //SB 02192019 Pass it as null, otherwise it will throw error.
                    doLink = doRS.GetLinkVal("LNK_Related_CO%%LNK_In_TE", ref doLink, true, 0, -1, "A_a", ref oTable);
                    doRS.SetLinkVal("LNK_Related_TE", doLink);
                    doLink = null;
                }


                // CS 11/19/2014 If Activity is of type Sales Visit, update linked Related Company.Last AC Sales date
                if (doRS.GetInfo("TYPE") == "2")
                {
                    if (Convert.ToInt32(doRS.GetFieldVal("MLS_TYPE", 2)) == 11)
                    {
                        if (doRS.GetLinkCount("LNK_RELATED_CO") > 0)
                        {
                            clArray doCompanies = new clArray();
                            DataTable oTable = null;
                            doCompanies = doRS.GetLinkVal("LNK_RELATED_CO", ref doCompanies, true, 0, -1, "A_a", ref oTable);
                            // Bipass reconsave and validation to speed up AC save
                            for (int i = 1; i <= doCompanies.GetDimension(); i++)
                            {
                                clRowSet doRSCompany = new clRowSet("CO", 1, "GID_ID='" + doCompanies.GetItem(i) + "'", null, "DTT_LastACSales", -1, "", "", "", "", "", true, true);
                                if (doRSCompany.GetFirst() == 1)
                                {
                                    doRSCompany.SetFieldVal("DTT_LastACSales", doRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    if (doRSCompany.Commit() == 0)
                                        goLog.Log(sProc, "CO update of last AC sales visit date field failed for CO " + doRSCompany.GetFieldVal("TXT_CompanyName") + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                }
                                doRSCompany = null/* TODO Change to default(_) if this is not a reference type */;
                            }
                        }
                    }
                    else
            // Activity is not of type sales visit
            if (doRS.GetLinkCount("LNK_RELATED_CO") > 0)
                    {
                        clArray doCompanies = new clArray();
                        DataTable oTable = null;
                        doCompanies = doRS.GetLinkVal("LNK_RELATED_CO", ref doCompanies, true, 0, -1, "A_a", ref oTable);
                        // Bipass reconsave and validation to speed up AC save
                        for (int i = 1; i <= doCompanies.GetDimension(); i++)
                        {
                            clRowSet doRSCompany = new clRowSet("CO", 1, "GID_ID='" + doCompanies.GetItem(i) + "'", null, "DTT_LastAC", -1, "", "", "", "", "", true, true);
                            if (doRSCompany.GetFirst() == 1)
                            {
                                doRSCompany.SetFieldVal("DTT_LastAC", doRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                if (doRSCompany.Commit() == 0)
                                    goLog.Log(sProc, "CO update of last AC date field failed for CO " + doRSCompany.GetFieldVal("TXT_CompanyName") + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                            }
                            doRSCompany = null/* TODO Change to default(_) if this is not a reference type */;
                        }
                    }
                }

                // CS 11/19/2014 If Activity is of type Sales Visit, update linked Related Contact.Last AC Sales Date
                if (doRS.GetInfo("TYPE") == "2")
                {
                    if (Convert.ToInt32(doRS.GetFieldVal("MLS_TYPE", 2)) == 11)

                    {
                        if (doRS.GetLinkCount("LNK_RELATED_CN") > 0)
                        {
                            clArray doContacts = new clArray();
                            DataTable oTable = null;
                            doContacts = doRS.GetLinkVal("LNK_RELATED_CN", ref doContacts, true, 0, -1, "A_a", ref oTable);
                            // Bipass reconsave and validation to speed up AC save
                            for (int i = 1; i <= doContacts.GetDimension(); i++)
                            {
                                clRowSet doRSContact = new clRowSet("CN", 1, "GID_ID='" + doContacts.GetItem(i) + "'", null, "DTT_LastACSales", -1, "", "", "", "", "", true, true);
                                if (doRSContact.GetFirst() == 1)
                                {
                                    doRSContact.SetFieldVal("DTT_LastACSales", doRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    if (doRSContact.Commit() == 0)
                                        goLog.Log(sProc, "CN update of last AC sales visit date field failed for CN " + doRSContact.GetFieldVal("TXT_NameLast") + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                }
                                doRSContact = null/* TODO Change to default(_) if this is not a reference type */;
                            }
                        }
                    }
                    else
            // Activity is not of type sales visit, so update Related Contact. Last AC date
            if (doRS.GetLinkCount("LNK_RELATED_CN") > 0)
                    {
                        clArray doContacts = new clArray();
                        DataTable oTable = null;
                        doContacts = doRS.GetLinkVal("LNK_RELATED_CN", ref doContacts, true, 0, -1, "A_a", ref oTable);
                        // Bipass reconsave and validation to speed up AC save
                        for (int i = 1; i <= doContacts.GetDimension(); i++)
                        {
                            clRowSet doRSContact = new clRowSet("CN", 1, "GID_ID='" + doContacts.GetItem(i) + "'", null, "DTT_LastAC", -1, "", "", "", "", "", true, true);
                            if (doRSContact.GetFirst() == 1)
                            {
                                doRSContact.SetFieldVal("DTT_LastAC", doRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                if (doRSContact.Commit() == 0)
                                    goLog.Log(sProc, "CN update of last AC date field failed for CN " + doRSContact.GetFieldVal("TXT_NameLast") + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                            }
                            doRSContact = null/* TODO Change to default(_) if this is not a reference type */;
                        }
                    }
                }

                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillEmailAliasLink"))
                {
                    if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillEmailAliasLink"))
                    {
                        par_doCallingObject = doRS;
                        bResult = scriptManager.RunScript("Activity_EmailAlias", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                        doRS = (clRowSet)par_doCallingObject;
                        if (!string.IsNullOrEmpty(doRS.oVar.GetVar("EL_ID").ToString()))
                        {
                            doRS.SetFieldVal("LNK_Related_EL", doRS.oVar.GetVar("EL_ID"));
                        }
                    }
                    //bResult = scriptManager.RunScript("Activity_EmailAlias", doRS);
                    //if (doRS.oVar.GetVar("EL_ID") != "")
                    //    doRS.SetFieldVal("LNK_Related_EL", doRS.oVar.GetVar("EL_ID"));
                }
                string sNotes = null;
                // CS 4/14/2015 If this is a new 'Email Sent' or 'Email Rcvd' type Activity being created from the Outlook add-in
                // copy MMO_Letter to MMO_Notes field. MMO_Letter is not currently used in GPS b/c GPS does not allow sending email;
                // this is to allow logging only.
                // Check if MMO_LETTER has data (means created from Log in Outlook)
                if (doRS.GetInfo("TYPE") == "2" & doRS.GetFieldVal("MMO_LETTER") != "")
                {
                    if (Convert.ToInt32(doRS.GetFieldVal("MLS_TYPE", 2)) == 6)
                        sNotes = "To:" + " " + doRS.GetFieldVal("EML_EMAIL");// Logging does not properly show 'To' in the MMO_LETTER field
                    if (Convert.ToInt32(doRS.GetFieldVal("MLS_TYPE", 2)) == 5 | Convert.ToInt32(doRS.GetFieldVal("MLS_TYPE", 2)) == 6)
                    {
                        if (sNotes != "")
                            sNotes = sNotes + Constants.vbCrLf + doRS.GetFieldVal("MMO_LETTER");
                        else
                            sNotes = Convert.ToString(doRS.GetFieldVal("MMO_LETTER"));
                        doRS.SetFieldVal("MMO_NOTES", sNotes);
                    }
                }

            }
            // PR_ email alerts 21/12/2020
            //SendEmailAlerts(doRS, par_sMode);

            try
            {
                //Ticket# 4008, send <NAME_EMAIL>
                string sLoginUserId = goP.GetMe("ID").ToLower();
                if (sLoginUserId != "e9e29589-e5ca-4a8a-5553-a43c015c33d9")
                {
                    string sCompany = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_CO%%TXT_COMPANYNAME"));
                    string sContact = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_CN%%TXT_NAMELAST")) + " " + Convert.ToString(doRS.GetFieldVal("LNK_RELATED_CN%%TXT_NAMEFIRST"));
                    string sNotes = Convert.ToString(doRS.GetFieldVal("MMO_NOTES"));
                    //string sType = Convert.ToString(doRS.GetFieldVal("MLS_TYPE"));
                    string sActionType = doRS.iRSType == clC.SELL_ADD ? " Created" : " Updated";
                    //string sModType = Convert.ToString(doRS.GetFieldVal("TXT_MODBY"));
                    //string sDate = Convert.ToString(doRS.GetFieldVal("DTT_MODTIME"));
                    string sTitle = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_CN%%TXT_TITLETEXT"));
                    string sCompanyAddr = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_CO%%TXT_ADDRMAILING"));
                    string sCompanyCity = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_CO%%TXT_CITYMAILING"));
                    string sCompanyState = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_CO%%TXT_STATEMAILING"));
                    sCompanyAddr = sCompanyAddr + "<br>" + sCompanyCity + ", " + sCompanyState;

                    string sTo = "<EMAIL>";
                    string sSubject = "Selltis - Activity has been" + sActionType;
                    string sEmailBody = "<div><b>The following Activity has been" + sActionType + "</b></div><br>";

                    sEmailBody = sEmailBody + "<div><div><b>Company</b></div> <div>" + sCompany + "</div></div><br>";
                    sEmailBody = sEmailBody + "<div><div><b>Address</b></div> <div>" + sCompanyAddr + "</div></div><br>";

                    sEmailBody = sEmailBody + "<div><div><b>Contact</b></div> <div>" + sContact + "</div></div><br>";
                    sEmailBody = sEmailBody + "<div><div><b>Title</b></div> <div>" + sTitle + "</div></div><br>";
                    //sEmailBody = sEmailBody + "<div><div><b>Type</b></div> <div>" + sType + "</div></div><br>";
                    //sEmailBody = sEmailBody + "<div><div><b>" + sActionType + " By</b></div> <div>" + sModType + "</div></div><br>";
                    //sEmailBody = sEmailBody + "<div><div><b>Date" + sActionType + "</b></div> <div>" + sDate + "</div></div><br>";
                    sEmailBody = sEmailBody + "<div><div><b>Notes</b></div> <div>" + sNotes + "</div></div><br>";

                    clEmail _clemail = new clEmail();
                    bool bmailsent = _clemail.SendSMTPEmailNew(sSubject, sEmailBody, sTo, "", "", "", "", "", "", true);
                }
            }
            catch (Exception ex)
            {
                goLog.Log("AC change mail send", "Error: " + ex.Message, clC.SELL_LOGLEVEL_DEBUG, false, true);
                return true;
            }

            return true;
        }
        public void SendEmailAlerts(clRowSet doRS, string par_sMode)
        {
            if (goP.GetRunMode() != "Import")
            {
                //'Email Alerts
                dynamic sEmailAlerts = goP.GetVar("EmailAlertsSave");
                if (sEmailAlerts != null && sEmailAlerts.ToString() == "ON")
                {
                    //Read the WG Options
                    string sWGOption = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", true);
                    string sWG_EMAILALERTS_ENABLE = goTR.StrRead(sWGOption, "EMAILALERTS_ENABLE", "No");

                    //Return if email alerts disabled in WG options
                    if (sWG_EMAILALERTS_ENABLE == "No")
                    {
                        goP.SetVar("EmailAlertsSave ", "OFF");
                        return;
                    }

                    clEmailAlerts _clEmailAlerts = new clEmailAlerts();
                    _clEmailAlerts.SendEmailAlerts(doRS, par_sMode);
                    goP.SetVar("EmailAlertsSave ", "OFF");

                    ////Push Notifications
                    Util.SendPushNotifications(doRS, par_sMode);
                }
            }
        }
        public bool AC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            return true;
        }

        public bool AC_FormControlOnChange_LNK_RELATED_PD_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //// VS 08212015 TKT#673 : Fill Notes with Product Questions on prod select which currently happens on button click.
            //goScr.RunScript("Activity_FillItemQuestions", doForm, null/* Conversion error: Set to default value for this argument */, "MMO_NOTES");
            //Form doForm = (Form)par_doCallingObject;

            //goP.TraceLine("BTN_INSERTPRODQS clicked.", "", sProc)
            scriptManager.RunScript("Activity_FillItemQuestions", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "MMO_NOTES");
            par_doCallingObject = doForm;
            return true;
        }
        public bool Activity_FillItemQuestions_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFieldName = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_sFieldName: Field name into which to insert product questions
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // PURPOSE:
            // Insert questions from linked Products into the par_sFieldName field and
            // position cursor in that field after the inserted questions.
            // RETURNS:
            // True if there were product questions and the user clicked Yes or No at the
            // 'do you want to insert questions?' prompt. False if the user clicks Cancel.

            // VS 12042015 TKT#823 : Do not append Product name
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;

            int m = 1;
            string sItemQuestions = "";
            string sLetterFld = "";
            // Dim sItemName As String
            string sReturn = "";

            DataTable oTable = new DataTable();
            clArray doLinkQuestions = new clArray();
            oTable = null;
            doLinkQuestions = doForm.doRS.GetLinkVal("LNK_Related_PD%%MMO_SPECQUESTIONS", ref doLinkQuestions, true, 0, -1, "A_a", ref oTable);
            // Dim doLinkNames As Object = New clArray()
            // doLinkNames = doForm.doRS.GetLinkVal("LNK_Related_PD%%TXT_PRODUCTNAME", doLinkNames)

            sLetterFld = doForm.doRS.GetFieldVal(par_sFieldName).ToString();

            for (m = 1; m <= doLinkQuestions.GetDimension(); m++)
            {
                if (doLinkQuestions.GetDimension() > 0)
                {

                    // sItemName = doLinkNames.GetItem(m)
                    // sItemQuestions = doLinkQuestions.GetItem(m)
                    // If sItemQuestions <> "" Then sReturn = sReturn & sItemName & ":" & vbCrLf & vbCrLf & sItemQuestions & vbCrLf & vbCrLf

                    sItemQuestions = doLinkQuestions.GetItem(m);
                    if (sItemQuestions != "")
                        sReturn = sReturn + sItemQuestions + Constants.vbCrLf + Constants.vbCrLf;
                }
            }

            doForm.doRS.SetFieldVal(par_sFieldName, sReturn + sLetterFld);

            doForm.MoveToField(par_sFieldName);

            doLinkQuestions = null;
            // doLinkNames = Nothing
            par_doCallingObject = doForm;
            return true;
        }

        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }

        public bool CalcQuoteTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }

        public bool CN_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string Proc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(Proc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            par_bRunNext = false;
            Form objForm = (Form)par_doCallingObject;
            bool bResult = false;
            string work = "";
            string nameLast;
            string nameFirst;
            bool checkDup;
            goErr.SetError();

            if (objForm.oVar.GetVar("CancelSave").ToString() == "1")
            {
                objForm.oVar.SetVar("Contact_CountPhoneFields", "");
                objForm.oVar.SetVar("Contact_DupCheck", "");
                objForm.oVar.SetVar("CancelSave", "");
                return false;
            }
            if (objForm.oVar.GetVar("ShareItem_Ran").ToString() != "1")
            {
                par_doCallingObject = objForm;
                scriptManager.RunScript("ShareItem", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                objForm = (Form)par_doCallingObject;
            }

            nameLast = objForm.FormRowSet.GetFieldVal("TXT_NAMELAST").ToString();
            nameFirst = objForm.FormRowSet.GetFieldVal("TXT_NAMEFIRST").ToString();

            if (scriptManager.IsSectionEnabled(Proc, par_sSections, "CheckDuplicates"))
            {
                checkDup = false;
                if (objForm.GetMode() == "CREATION")
                {
                    checkDup = true;
                }
                else
                {
                    if (nameFirst.ToUpper() != ((objForm.oVar.GetVar("ContactNameFirstOnLoad")).ToString()).ToUpper())
                    {
                        checkDup = true;
                    }
                }

                if (objForm.oVar.GetVar("Contact_DupCheck").ToString() != "1")
                {
                    if (checkDup)
                    {
                        par_doCallingObject = objForm;
                        if (scriptManager.RunScript("Contact_DupCheck", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "CN_FormOnSave"))
                        {
                            objForm = (Form)par_doCallingObject;
                            if (objForm.oVar.GetVar("Contact_DupCheck").ToString() == "1")
                            {
                                return true;
                            }
                        }
                    }
                }
                else
                {
                    clRowSet _clrwset = new clRowSet("CN", 3, "TXT_NameLast='" + goTR.PrepareForSQL(nameLast) + "' and TXT_NameFirst='" + goTR.PrepareForSQL(nameFirst) + "' and TXT_ContactCode='" + goTR.PrepareForSQL(objForm.FormRowSet.GetFieldVal("TXT_ContactCode").ToString()) + "'", "", "GID_ID", 1);
                    if (_clrwset.Count() > 0)
                    {
                        objForm.MessageBox("A Contact exists with the same First Name, Last Name, and Unique Code." + Environment.NewLine + "Please modify one of these three fields.");

                        return false;
                    }
                }

            }

            if (scriptManager.IsSectionEnabled(Proc, par_sSections, "PropertizeAddress"))
            {
                par_doCallingObject = objForm;
                scriptManager.RunScript("PropertizeAddress", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "TXT_ADDRBUSINESS");
                scriptManager.RunScript("PropertizeAddress", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "TXT_ADDRHOME");
                scriptManager.RunScript("PropertizeAddress", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "TXT_ADDROTHER");
                objForm = (Form)par_doCallingObject;
            }

            if (scriptManager.IsSectionEnabled(Proc, par_sSections, "UncheckCHK_ExtDevReview"))
            {
                if (objForm.GetMode() != "CREATION")
                {
                    if (Convert.ToInt32(objForm.FormRowSet.GetFieldVal("CHK_EXTDEVREVIEW", 2)) == 1)
                    {
                        objForm.FormRowSet.SetFieldVal("CHK_EXTDEVREVIEW", 0, 2);
                    }
                }
            }

            // CS 11/19/2014: Add core functionality that inserting a journal creates a Journal activity
            if (scriptManager.IsSectionEnabled(Proc, par_sSections, "CreateJournalActivity"))
            {
                // check if we already ran this script. If so don't run it again.
                // This only happens when a journal entry is added via the form. This is b/c it requires interaction with the journal messagebox.
                if (objForm.oVar.GetVar("Cn_CreateActLog_Ran") != "1")
                {
                    par_doCallingObject = objForm;
                    bool bRunNext = true;
                    // If doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONSAVEAC_CREATE_AC") = "1" Then
                    bResult = scriptManager.RunScript("CreateActLog", ref par_doCallingObject, ref par_oReturn, ref bRunNext, ref par_sSections, "CN");
                }
            }
            par_doCallingObject = objForm;
            return true;
        }
        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            par_bRunNext = false;

            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = default(clRowSet);
            clArray doLink = default(clArray);
            string sFileName = "CN";
            string sSysName = "";
            string sEmail = null;
            doRS = (clRowSet)par_doCallingObject;

            string s = doRS.GetInfo(clC.SELL_ROWSET_TYPE);
            //Fill Original Creation Date/Time if blank
            if (doRS.GetInfo(clC.SELL_ROWSET_TYPE) == "2")
            {
                if (string.IsNullOrEmpty(doRS.GetFieldVal("dtt_origcreatedtime", 1).ToString()))
                {
                    doRS.SetFieldVal("dtt_origcreatedtime", "Today|Now");
                }
            }

            //CS 6/19/09 Get company info
            clRowSet doRSCO = new clRowSet("CO", 3, "GID_ID='" + doRS.GetFieldVal("LNK_RELATED_CO") + "'", "", "TXT_COMPANYNAME,LNK_RELATED_IU");

            //Fill the Company Name Text if empty
            if (string.IsNullOrEmpty(doRS.GetFieldVal("TXT_COMPANYNAMETEXT").ToString()))
            {
                if (doRSCO.Count() > 0)
                {
                    doRS.SetFieldVal("TXT_COMPANYNAMETEXT", doRSCO.GetFieldVal("TXT_COMPANYNAME"));
                }
                else
                {
                    doRS.SetFieldVal("TXT_COMPANYNAMETEXT", "");
                }
            }

            //Uppercase the State fields
            doRS.SetFieldVal("TXT_STATEBUSINESS", (doRS.GetFieldVal("TXT_STATEBUSINESS").ToString()).ToUpper());
            doRS.SetFieldVal("TXT_STATEHOME", (doRS.GetFieldVal("TXT_STATEHOME").ToString()).ToUpper());
            doRS.SetFieldVal("TXT_STATEOTHER", (doRS.GetFieldVal("TXT_STATEOTHER").ToString()).ToUpper());

            //---------- AUTO-FILLED FIELDS
            if (goP.GetRunMode() != "Import")
            {
                doLink = new clArray();
                oTable = null;  //SB 02222019 Pass it as null, otherwise it will throw error.
                doLink = doRS.GetLinkVal("LNK_ORIGINATED_OP", ref doLink, false, 0, -1, "A_a", ref oTable);
                doRS.SetLinkVal("LNK_INVOLVEDIN_OP", doLink);
                doLink = null;
            }

            //PR_email alerts
            SendEmailAlerts(doRS, par_sMode);

            // doRS = (clRowSet)par_doCallingObject;
            par_doCallingObject = doRS;

            return true;
        }
        public bool CN_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }

        public bool CO_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;


            return true;
        }
        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            return true;
        }
        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string Proc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(Proc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            par_bRunNext = false;
            //string Proc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            string work = "";
            string companyName;
            bool checkDup;
            string scriptVar;
            string oldTeamLeaderID;
            string newTeamLeaderID;
            string teamLeaders;
            bool bResult = false;

            Form objForm = (Form)par_doCallingObject;
            if (scriptManager.IsSectionEnabled(Proc, par_sSections, "CancelSave"))
            {
                if ((objForm.oVar.GetVar("CancelSave")).ToString() == "1")
                {
                    objForm.oVar.SetVar("Company_DupCheck", "");
                    objForm.oVar.SetVar("CancelSave", "");
                    return false;
                }
            }

            companyName = objForm.FormRowSet.GetFieldVal("TXT_COMPANYNAME").ToString();
            par_doCallingObject = objForm;
            if (scriptManager.IsSectionEnabled(Proc, par_sSections, "CheckDuplicates"))
            {
                checkDup = false;
                if (objForm.GetMode() == "CREATION")
                {
                    checkDup = true;
                }
                else
                {
                    if (companyName.ToUpper() != ((objForm.oVar.GetVar("CompanyNameOnLoad").ToString()).ToUpper()))
                    {
                        checkDup = true;
                    }
                    else
                    {
                        if (Convert.ToString(objForm.FormRowSet.GetFieldVal("TXT_CITYMAILING")).ToUpper() != Convert.ToString(objForm.oVar.GetVar("CompanyCityOnLoad")).ToUpper())
                        {
                            checkDup = true;
                        }
                    }
                }

                if ((objForm.oVar.GetVar("Company_DupCheck")).ToString() != "1")
                {
                    if (checkDup)
                    {

                        if (scriptManager.RunScript("Company_DupCheck", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "CO_FormOnSave"))
                        {
                            objForm = (Form)par_doCallingObject;
                            if (objForm.MessageBoxFormMode == "SAVEANDKEEPOPEN")
                            {

                            }
                            else
                            {
                                if ((objForm.oVar.GetVar("Company_DupCheck")).ToString() == "1")
                                {
                                    return false;
                                }
                            }
                        }
                    }
                }
            }

            par_doCallingObject = objForm;
            if (scriptManager.IsSectionEnabled(Proc, par_sSections, "FixAddresses"))
            {
                scriptManager.RunScript("Company_FixAddresses", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                objForm = (Form)par_doCallingObject;
            }

            //customer code
            if (scriptManager.IsSectionEnabled(Proc, par_sSections, "FillCustomerCode"))
            {
                if ((objForm.FormRowSet.GetFieldVal("TXT_CUSTCODE")).ToString() == "")  //Don't overwrite a manual entry
                {
                    object par_doCallingObjectwithRowSet = objForm.FormRowSet;

                    scriptManager.RunScript("Company_FillCustCode", ref par_doCallingObjectwithRowSet, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                    objForm.FormRowSet = (clRowSet)par_doCallingObjectwithRowSet;
                }
                //     If Trim(doForm.doRS.GetFieldVal("TXT_CUSTCODE")) = "" Then

                //TKT#3978-The client is requesting that on the CO form the field customer code no longer be a required field.(12-07-2021)
                //string code = objForm.FormRowSet.GetFieldVal("TXT_CUSTCODE").ToString();
                //if (objForm.FormRowSet.GetFieldVal("TXT_CUSTCODE").ToString().Trim().Equals(""))
                //{
                //    objForm.MoveToField("TXT_CUSTCODE");
                //    goErr.SetWarning(30200, Proc, "", "The Customer Code could not be generated automatically. Please enter it manually.", "", "", "", "", "", "", "", "", "TXT_CUSTCODE");
                //    return false;
                //}
            }

            //Uncheck CHK_EXTDEVREVIEW
            if (scriptManager.IsSectionEnabled(Proc, par_sSections, "UncheckExternalDeviceReview"))
            {
                if (objForm.GetMode() != "CREATION")
                {
                    if (Convert.ToInt32(objForm.FormRowSet.GetFieldVal("CHK_EXTDEVREVIEW", 2)) == 1)
                    {
                        objForm.FormRowSet.SetFieldVal("CHK_EXTDEVREVIEW", 0, 2);
                    }
                }
            }

            if (scriptManager.IsSectionEnabled(Proc, par_sSections, "OfferToLinkTeamLeaderToLinkedContacts"))
            {
                if ((objForm.oVar.GetVar("CO_FormOnSave_LinkTeamLeaderMsgBox")).ToString() != "1")
                {
                    if (objForm.GetMode() != "CREATION")
                    {
                        oldTeamLeaderID = objForm.oVar.GetVar("TeamLeaderIDOnLoad").ToString();
                        newTeamLeaderID = objForm.FormRowSet.GetFieldVal("LNK_TeamLeader_US").ToString();
                        if (oldTeamLeaderID != newTeamLeaderID)
                        {
                            teamLeaders = newTeamLeaderID + "|" + oldTeamLeaderID;
                            clRowSet clrw = new clRowSet("US", 3, "GID_ID='" + objForm.FormRowSet.GetFieldVal("LNK_TEAMLEADER_US") + "'", "", "TXT_NAMELAST,TXT_NAMEFIRST");
                            objForm.MessageBox("You changed the Team Leader from '" + objForm.oVar.GetVar("TeamLeaderNameOnLoad") + "' to '" + clrw.GetFieldVal("TXT_NameLAst") + ", " + clrw.GetFieldVal("TXT_NameFirst") + "'. Would you like to link the new Team Leader to the Contacts of this Company?" + "/n" + "/n" + "This may take a few minutes.", clC.SELL_MB_YESNOCANCEL + clC.SELL_MB_DEFBUTTON1, "Link Team Leader to Contacts", "Yes", "Yes, and unlink old Team Leader", "No", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", objForm, null, "1", "2", "3", teamLeaders, "CO_FormOnSave_LinkTeamLeaderMsgBox", "CO_FormOnSave_LinkTeamLeaderMsgBox");
                            return true;
                        }

                        newTeamLeaderID = newTeamLeaderID;
                    }
                }
            }

            // CS 11/19/2014: Add core functionality that inserting a journal creates a Journal activity
            if (scriptManager.IsSectionEnabled(Proc, par_sSections, "CreateJournalActivity"))
            {
                // check if we already ran this script. If so don't run it again.
                // This only happens when a journal entry is added via the form. This is b/c it requires interaction with the journal messagebox.
                if (objForm.oVar.GetVar("Co_CreateActLog_Ran") != "1")
                {
                    par_doCallingObject = objForm;
                    bool bRunNext = true;
                    // If doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONSAVEAC_CREATE_AC") = "1" Then
                    bResult = scriptManager.RunScript("CreateActLog", ref par_doCallingObject, ref par_oReturn, ref bRunNext, ref par_sSections, "CO");
                }
            }

            if (scriptManager.IsSectionEnabled(Proc, par_sSections, "DisplayErrorMessages"))
            {
                scriptVar = objForm.oVar.GetVar("ScriptMessages").ToString();
                if (scriptVar != "")
                {
                    if (scriptVar.Length < 500)
                    {
                        //objForm.MessageBox(scriptVar, , , , , , , "MessageBoxEvent", , , , , "OK", , , , "CO_FormOnSave_ScriptMessages");
                    }
                    else
                    {
                        //objForm.MessageBox(String.Left(scriptVar, 497) & "...", , , , , , , "MessageBoxEvent", , , , , "OK", , , , "CO_FormOnSave_ScriptMessages");
                    }
                    return true;
                }
            }
            par_doCallingObject = objForm;
            return true;
        }
        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            return true;
        }
        public bool CO_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // par_bRunNext = false;
            // string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = default(clRowSet);
            clArray doLink = default(clArray);
            string sFileName = "CO";
            string sWork = null;
            string sSysName = "";
            //Dim sNum As String
            par_bRunNext = false;
            doRS = (clRowSet)par_doCallingObject;

            object par_doCalling = doRS;

            //CS added this.
            //If Cust code is blank try to generate it.
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "GenerateCustomerCodeIfBlank"))
            {
                string code = (doRS.GetFieldVal("TXT_CUSTCODE", 1)).ToString();
                if (string.IsNullOrEmpty((doRS.GetFieldVal("TXT_CUSTCODE", 1)).ToString()))
                {
                    scriptManager.RunScript("Company_FillCustCode", ref par_doCalling, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                }
                //Uppercase the code
                doRS.SetFieldVal("TXT_CUSTCODE", (doRS.GetFieldVal("TXT_CUSTCODE").ToString()).ToUpper());
            }

            if (doRS.bBypassValidation != true)
            {
                //Check if review fields exist in db and if so, calculate
                if (goData.IsFieldValid("CO", "CHK_REVIEW") == true)
                {
                    scriptManager.RunScript("Company_CalcReviewOverdue", ref par_doCalling, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                }

                //Duplicate Company Code
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "ValidateCompanyCode"))
                {
                    scriptManager.RunScript("Company_ValidateName", ref par_doCalling, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, doRS.GetFieldVal("TXT_CUSTCODE").ToString(), doRS.GetFieldVal("GID_ID").ToString());
                    //if (goP.GetVar("sDuplCustCodeCount").ToString() != "0")
                    //{
                    //    sWork = doRS.GetFieldVal("TXT_CUSTCODE").ToString();
                    //    //Don't give duplicate Cust Code message if the code is blank.
                    //    goErr.SetWarning(30200, sProc, "", "Company Code '" + sWork + "' is already in use. Please edit the code to make it unique.", "", "", "", "", "", "", "", "", "TXT_CUSTCODE");
                    //    return false;
                    //}
                }
            }

            //------------ AUTO-FILLED FIELDS ------------
            if (goP.GetRunMode() != "Import")
            {
                //Fill Original Creation Date/Time if blank
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillOrigCreationDateTime"))
                {
                    if (doRS.GetInfo(clC.SELL_ROWSET_TYPE) == "2")
                    {
                        if (doRS.GetFieldVal("dtt_origcreatedtime", 1).ToString() == "")
                        {
                            doRS.SetFieldVal("dtt_origcreatedtime", "Today|Now");
                        }
                    }
                }
                //--------- FROM FILL NAME FUNCTION ----------------
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "UppercaseStateFields"))
                {
                    sWork = doRS.GetFieldVal("TXT_STATEMAILING").ToString();
                    if (sWork != sWork.ToUpper())
                        doRS.SetFieldVal("TXT_STATEMAILING", sWork.ToUpper());

                    sWork = doRS.GetFieldVal("TXT_STATEBILLING").ToString();
                    if (sWork != sWork.ToUpper())
                        doRS.SetFieldVal("TXT_STATEBILLING", sWork.ToUpper());

                    sWork = doRS.GetFieldVal("TXT_STATESHIPPING").ToString();
                    if (sWork != sWork.ToUpper())
                        doRS.SetFieldVal("TXT_STATESHIPPING", sWork.ToUpper());
                }
                //LNK_INVOLVES_USER
                System.Data.DataTable oTable = new System.Data.DataTable();
                if (scriptManager.IsSectionEnabled(sProc, par_sSections, "FillInvolvesUser"))
                {
                    doLink = new clArray();
                    oTable = null;  //SB 02222019 Pass it as null, otherwise it will throw error.
                    doLink = doRS.GetLinkVal("LNK_TEAMLEADER_US", ref doLink, false, 0, -1, "A_a", ref oTable);
                    doRS.SetLinkVal("LNK_INVOLVES_US", doLink);
                    doLink = null;
                }
            }
            string sCurVol = "";
            string sPotVol = "";
            long iCurCount = doRS.GetLinkCount("LNK_Current_PD");
            long iPotCount = doRS.GetLinkCount("LNK_Potential_PD");


            // ---------TLD 7/24/2012 Target Account Matrix Profiling
            // Copy LNK_Current_PD to LNK_Potential_PD
            doRS.SetFieldVal("LNK_Potential_PD", doRS.GetFieldVal("LNK_Current_PD"));

            // Record total selections from LNK_Current_PD and LNK_Potential_PD
            doRS.SetFieldVal("INT_CURCOUNT", iCurCount, 2);
            doRS.SetFieldVal("INT_POTCOUNT", iPotCount, 2);

            // Calculate Potential Percentage & Potential Portfolio
            clRowSet doPDRS = new clRowSet("PD", 3, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "GID_ID");
            long iPDCount = doPDRS.Count();
            if (iPDCount != 0)
            {
                if (iPotCount != 0)
                    doRS.SetFieldVal("SR__POTENTIALPERC", (iCurCount / (double)iPotCount) * 100, 2);
                doRS.SetFieldVal("SR__POTENTIALPORTFOLIO", (iPotCount / (double)iPDCount) * 100, 2);

                doRS.SetFieldVal("SR__ProdLinePot", (iPotCount - iCurCount), 2);
                sCurVol = Strings.Left(Convert.ToString(doRS.GetFieldVal("MLS_CURVOLUME")), 1);
                sPotVol = Strings.Left(Convert.ToString(doRS.GetFieldVal("MLS_POTVOLUME")), 1);
                if (sCurVol == "<")
                    sCurVol = "Z";
                if (sPotVol == "<")
                    sPotVol = "Z";

                // set field to cur & pot
                doRS.SetFieldVal("TXT_CURANDPOT", sCurVol + sPotVol);
            }

            // ---------- Target Account -----------------
            // Set Product Potential Quadrant
            double rTotalPortfolio = Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPORTFOLIO", 2));
            double rPotentialProduct = Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPERC", 2));

            if (rTotalPortfolio >= 51 & rTotalPortfolio <= 100)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                    // Set to 1
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "1");

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                    // Set to 3
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "3");
            }

            if (rTotalPortfolio >= 0 & rTotalPortfolio <= 50)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                    // Set to 2
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "2");

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                    // Set to 4
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "4");
            }

            // Because COs are updated nightly to set custom
            // date fields, need to write to custom mod time and mod by fields
            // AutoCOUpdate does NOT run recordonsave
            doRS.SetFieldVal("TXT_CusModBy", goP.GetMe("CODE"));
            doRS.SetFieldVal("DTT_CusModTime", "Today|Now");
            // ---------TLD 7/24/2012 End Target Account Matrix Profiling

            // CS 4/27/2015 Check for the # of linked, Open Opportunities and update CO.LI__OpenOpCount field. 
            // Only do this if have not already done it in OP_RecordOnSave when adding a new linked Op.
            if (doRS.oVar.GetVar("UpdatedOpCount") != "1")
            {
                clRowSet doRSOp = new clRowSet("OP", 3, "LNK_FOR_CO=" + doRS.GetCurrentRecID() + " and MLS_STATUS=0");
                if (doRSOp.GetFirst() == 1)
                    doRS.SetFieldVal("LI__OpenOpCount", doRSOp.Count(), 2);
            }

            SendEmailAlerts(doRS, par_sMode);
            par_doCallingObject = doRS;


            return true;
        }

        public bool Company_ValidateName_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sValue = "", string par_sRecID = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingobject: unused.
            //par_doarray: unused.
            //par_svalue: cust code value to check duplicates against
            //par_srecid: id of the record to validate. this record is excluded from the duplicate check.
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oreturn: object passed to script byref.  used to return values to calling process
            //par_brunnext: only observed in clscriptscustom '_pre' scripts: set this to false to prevent the main and _post script from running.
            //par_ssections: ini-format list of code sections to exclude (sectionname=0) or include (sectionname=1). see _runscriptmanager for more info.

            string sproc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //golog.log(sproc, "start", clc.sell_loglevel_debug, true);

            //purpose:
            //		return the number of companies with the same cust code, up to 100.
            //		if more than 100 companies have dupl code, 100 is returned.
            //		the company which is being validated is not counted.
            //		the name of this method is awakward, but it was left the same
            //		as in selltis commence for compatibility reasons.
            //returns:
            //		true if it was successful, otherwise false.
            //		the number of companies is returned via a form-global variable 'sduplcustcodecount'.

            int lcount = 0;
            clRowSet doRS = null;

            //gop.traceline("", "", sproc)

            //---------- uppercase pass ----------
            //count duplicates that have the same code in all uppercase
            //gop.traceline("uppercase pass", "", sproc)
            doRS = new clRowSet("co", 3, "txt_custcode='" + goTR.PrepareForSQL(Strings.UCase(par_sValue)) + "'", "txt_custcode a", "gid_id");
            int li = 1;
            lcount = 0;
            if (doRS.GetFirst() == 1)
            {
                do
                {
                    //gop.traceline("'" & li & "': dors sysid: '" & ucase(dors.getfieldval("gid_id")) & "'", "", sproc)
                    //gop.traceline("par_srecid: '" & ucase(par_srecid) & "'", "", sproc)
                    if (Strings.UCase(doRS.GetFieldVal("GID_ID").ToString()) != Strings.UCase(par_sRecID))
                    {
                        lcount += 1;
                    }
                    if (doRS.GetNext() != 1)
                        break; // todo: might not be correct. was : exit do
                    li = li + 1;
                    if (li > 100)
                        break; // todo: might not be correct. was : exit do
                } while (true);
            }

            //delete the rowset object
            doRS = null;

            //----------- same case pass -----------
            //count duplicates that have the same code in the same case
            // gop.traceline("same case pass", "", sproc)
            doRS = new clRowSet("co", 3, "txt_custcode='" + goTR.PrepareForSQL(par_sValue) + "'", "txt_custcode a", "gid_id");
            li = 1;
            if (doRS.GetFirst() == 1)
            {
                do
                {
                    //gop.traceline("'" & li & "': dors sysid: '" & ucase(dors.getfieldval("gid_id")) & "'", "", sproc)
                    //gop.traceline("par_srecid: '" & ucase(par_srecid) & "'", "", sproc)
                    if (Strings.UCase(doRS.GetFieldVal("GID_ID").ToString()) != Strings.UCase(par_sRecID))
                    {
                        lcount = lcount + 1;
                    }
                    if (doRS.GetNext() != 1)
                        break; // todo: might not be correct. was : exit do
                    li = li + 1;
                    if (li > 100)
                        break; // todo: might not be correct. was : exit do
                } while (true);
            }

            //delete the rowset object
            //delete(dors)
            doRS = null;


            goP.SetVar("sduplcustcodecount", lcount);
            par_doCallingObject = doRS;
            return true;

        }

        public bool CO_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }

        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            Form doForm = (Form)par_doCallingObject;
            string sColor = goP.GetVar("sMandatoryFieldColor").ToString();

            //PORTING FROM COMMENCE IN PROGRESS (ALMOST DONE). Owner: RH. Original owner: PJ.

            //2004/12/22 10:11:51 MAR Changed MMO_NOTES to MMO_JOURNAL in SetVar.

            //Set journal field locked
            doForm.SetControlState("MMO_Journal", 1);
            doForm.SetControlState("MMR_Journal", 1);

            //CS uncomment----------- FIELD LABELS -----------
            //doForm.SetFieldProperty("CHK_Q01", "LABELTEXT", "Leverage")
            //doForm.SetFieldProperty("CHK_Q02", "LABELTEXT", "Executive Buy-In")
            //doForm.SetFieldProperty("CHK_Q03", "LABELTEXT", "CEO Contacted")
            //doForm.SetFieldProperty("CHK_Q04", "LABELTEXT", "Front End Buy-In")
            //doForm.SetFieldProperty("CHK_Q05", "LABELTEXT", "Competition ID'd")
            //doForm.SetFieldProperty("CHK_Q06", "LABELTEXT", "Champion Built")
            //doForm.SetFieldProperty("CHK_Q07", "LABELTEXT", "Influences ID'd")
            //doForm.SetFieldProperty("CHK_Q08", "LABELTEXT", "Needs Assessed")
            //doForm.SetFieldProperty("CHK_Q09", "LABELTEXT", "Approved/Funded")
            //doForm.SetFieldProperty("CHK_Q10", "LABELTEXT", "Decision Process")
            //doForm.SetFieldProperty("CHK_Q11", "LABELTEXT", "Timing Estimate")
            //doForm.SetFieldProperty("CHK_Q12", "LABELTEXT", "Key Questions")
            //doForm.SetFieldProperty("CHK_Q13", "LABELTEXT", "Present/Demo")
            //doForm.SetFieldProperty("CHK_Q14", "LABELTEXT", "Quoted")
            //doForm.SetFieldProperty("CHK_Q15", "LABELTEXT", "Marked Status")

            //----------- VARIABLES -------------
            doForm.oVar.SetVar("lLenJournal", Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")));

            //Set mandatory field color
            doForm.SetFieldProperty("DTE_Time", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("TME_Time", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_CreditedTo_US", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_For_Co", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_for_PD", "LABELCOLOR", sColor);
            //doForm.SetFieldProperty("DTE_ExpCloseDate", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("SR__Qty", "LABELCOLOR", sColor);
            //doForm.SetFieldProperty("CUR_UnitValue", "LABELCOLOR", sColor);


            //Set button field tooltips
            doForm.SetFieldProperties("BTN_CALCPROBABILITY_1", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Calculate Opportunity value and value index");
            doForm.SetFieldProperties("BTN_LINKCOMPANY", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Link Company of the linked Contact");
            doForm.SetFieldProperties("BTN_INSERTLINE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend a Journal line");
            doForm.SetFieldProperties("BTN_INSERTLINEMMR", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend a Journal line");
            doForm.SetFieldProperties("BTN_INSERTDATE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend the date and time in the Notes field");
            doForm.SetFieldProperties("BTN_INSERTPRODQS", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend questions for the linked Product in the Notes field");
            doForm.SetFieldProperties("BTN_CALCPROBABILITY", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Calculate probability %");
            doForm.SetFieldProperties("BTN_LINKCOMPANY_1", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Link Companies of the linked Contacts");
            doForm.SetFieldProperties("BTN_INSERTPRESALE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend Presale Questions defined in Workgroup options in the Questionnaire field");
            doForm.SetFieldProperties("CHK_CREATETODO", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Create a linked To Do");


            //----------- STATES --------------
            par_doCallingObject = doForm;
            bool bRunNext = true;
            scriptManager.RunScript("Opp_ManageControlState", ref par_doCallingObject, ref par_oReturn, ref bRunNext, ref par_sSections);
            doForm = (Form)par_doCallingObject;
            //CS 8/4/08
            //Check if this Opp was created as a result of a Create Linked from an AC.
            //copy AC MMO_Journal (Lead only) to OP MMO_Journal when the OP is created from the AC
            if (doForm.GetMode() == "CREATION")
            {
                string sID = doForm.GetCreateLinkedSourceRecSUID;
                //If the ID is not blank, find out if coming from an AC
                if (!string.IsNullOrEmpty(sID) & sID != null)
                {
                    if (goTR.GetFileFromSUID(sID) == "AC")
                    {
                        //Check if this AC is Lead
                        clRowSet doRSAC = new clRowSet("AC", 3, "GID_ID='" + sID + "'", "", "GID_ID,MLS_PURPOSE,MMO_JOURNAL");
                        if (doRSAC.GetFirst() == 1)
                        {
                            //Lead
                            if (doRSAC.GetFieldVal("MLS_PURPOSE", 2).ToString() == "8")
                            {
                                doForm.doRS.SetFieldVal("MMO_JOURNAL", doRSAC.GetFieldVal("MMO_JOURNAL"));
                            }
                        }
                        else
                        {
                            //Can't find Activity
                        }
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;


        }
        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            return true;
        }
        public bool OP_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;

            Form doForm = (Form)par_doCallingObject;
            string sDateStamp = "";
            string sJournal = null;


            // *******************ENFORCE*************************

            //	REVIEW: 
            //	DateTimeDiff returning 0 and null.  RAH: Changed all instances to use Val
            //	Review month and year field requirements - used for sorting views in Cmc.
            //	Runscript UpdateContactItem - this procedure is not fixed
            //	UpdateProject - have not ported this
            bool bIAmInvolved = false;
            string sMeVal = null;
            //Dim sMsgResponse As String
            long lStatusVal = 0;
            string sCreatedDate = null;
            string sCloseDate = null;
            //Dim sDateWon As String
            bool bEnforceVal = false;
            //Dim sValueFld As Decimal
            //Dim sWork As String
            //Dim iMonthCreatedNum As String
            //Dim lCreateDate As Long
            //Dim sMonthCreated As String
            //Dim lCloseDate As Long
            //Dim iMonthCloseNum As String
            bool bResult = false;
            string sScriptVar = null;

            lStatusVal = Convert.ToInt64(doForm.doRS.GetFieldVal("MLS_STATUS", 2));

            string sEnforceNextAtionDate = doForm.oVar.GetVar("EnforceNextActionDate") == null ? "" : doForm.oVar.GetVar("EnforceNextActionDate").ToString();
            //Next Action Date - check only when status is Open or On Hold
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceNextActionDate") && sEnforceNextAtionDate != "No") //
            {
                switch (lStatusVal)
                {
                    case 0:
                    case 1:
                        //Open, On Hold
                        if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1).ToString()) | string.IsNullOrEmpty(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1).ToString()))
                        {
                            doForm.MoveToTab(1);
                            doForm.MoveToField("DTE_NEXTACTIONDATE");
                            // 'goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE")
                            goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE");
                            return false;
                        }
                        else
                        {
                            //Prompt user to update Next Action date if the current value is < Today
                            //and if the user is the 'Credited To' or 'Peer' in the Opportunity.
                            sMeVal = goP.GetMe("ID");
                            bIAmInvolved = false;

                            if (doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US").ToString() == sMeVal)
                                bIAmInvolved = true;
                            if (doForm.doRS.GetFieldVal("LNK_PEER_US").ToString() == sMeVal)
                                bIAmInvolved = true;
                            //goP.TraceLine("bIAmInvolved: '" & bIAmInvolved & "'", "", sProc)
                            //goP.TraceLine("Next Action is " & doForm.GetFieldVal("DTE_NEXTACTIONDATE", 2), "", sProc)
                            //goP.TraceLine("DateSys is " & Format(goTR.NowLocal(), "yyyy-MM-dd hh:mm:ss.fff"), "", sProc)
                            if (Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1).ToString()) < Convert.ToDateTime(Strings.Format(goTR.NowLocal(), "yyyy-MM-dd")) & bIAmInvolved == true)
                            {
                                //goP.TraceLine("NextReview was found to be < than DateSys()", "", sProc)
                                //lMsgResponse - may return long int. 

                                //Check if we already asked.
                                if (doForm.oVar.GetVar("Op_FormOnSave_NA_Date") != null && doForm.oVar.GetVar("Op_FormOnSave_NA_Date").ToString() != "1")
                                {
                                    //CS 6/13/11: Now set in call to messagebox doForm.oVar.SetVar("Op_FormOnSave_NA_Date", "1")
                                    doForm.MessageBox("Would you like to update the 'Next Action' date with 2 weeks from today?" + Environment.NewLine + Environment.NewLine + "Currently it is " + doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE") + ".", clC.SELL_MB_YESNO + clC.SELL_MB_DEFBUTTON1, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "", null, null, "Yes", "No", "", "", "Op_FormOnSave_NADate", "Op_FormOnSave_NA_Date");
                                    return true;
                                }

                                //CS: Msgbox must be replaced in web context.
                                //Select Case MsgBox("Would you like to update the 'Next Action' date?" & vbCrLf & vbCrLf & "Currently, it is " & doForm.GetFieldVal("DTE_NEXTACTIONDATE") & ".", 3, "Selltis")
                                //    Case 6        'user clicked Yes 
                                //        doForm.MoveToTab(1)
                                //        doForm.MoveToField("DTE_NEXTACTIONDATE")
                                //        doForm.SetFieldVal("DTE_NEXTACTIONDATE", "2 weeks from today")
                                //        Return False
                                //    Case 2      'user clicked Cancel
                                //        Return False
                                //End Select
                            }
                        }

                        break;
                    case 6:
                        //Quoted
                        doForm.doRS.SetFieldVal("CHK_Q14", 1, 2);

                        break;
                }
            }

            //	*** StatusFunc ***
            // Status is Open
            //Make sure this is not called other than from (after) Enforce

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "EnforceAndFillStatusRelatedFields"))
            {
                bool bRunNext = false;
                par_doCallingObject = doForm;
                switch (lStatusVal)
                {
                    case 0:
                    case 6:
                        //Open, Quoted
                        //Make sure the Expected Close Date field is not earlier than Date Created
                        //First test whether either of the two fields is empty

                        if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_TIME", 1).ToString()) | string.IsNullOrEmpty(doForm.doRS.GetFieldVal("DTE_TIME", 1).ToString()))
                        {
                            doForm.doRS.SetFieldVal("DTE_TIME", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                        }
                        else
                        {
                            sCreatedDate = doForm.doRS.GetFieldVal("DTE_TIME", 2).ToString();
                        }

                        if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1).ToString()) | string.IsNullOrEmpty(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1).ToString()))
                        {
                            doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                        }
                        else
                        {
                            sCloseDate = doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2).ToString();
                        }


                        if (Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2)) < Convert.ToDateTime(doForm.doRS.GetFieldVal("DTE_TIME", 2)))
                        {
                            sCloseDate = "";
                            //doForm.SetFieldVal("DTE_EXPCLOSEDATE", sCloseDate, 2)  'RAH: I commented this out as it was a little confusing and unneccessary.  
                            //Additionally, knowing what the older date is has some value

                            //doForm.MoveToTab(2)    'RAH:There is no need to move to a tab as the field is on the top of the form.
                            doForm.MoveToField("DTE_EXPCLOSEDATE");
                            goErr.SetWarning(30200, sProc, "", "The Expected Close Date can't be earlier than the Date Created.", "", "", "", "", "", "", "", "", "DTE_EXpCloseDate");
                            return false;
                        }
                        else
                        {
                            bRunNext = true;
                            bEnforceVal = scriptManager.RunScript("Opp_EnforceValue", ref par_doCallingObject, ref par_oReturn, ref bRunNext, ref par_sSections);
                            if (bEnforceVal == false)
                            {
                                //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
                                goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
                                return false;
                            }
                        }

                        break;
                    case 2:
                    case 3:
                        //Won, Lost
                        if (doForm.doRS.GetFieldVal("MLS_REASONWONLOST", 2).ToString() == "0")
                        {
                            doForm.MoveToTab(2);
                            doForm.MoveToField("MLS_REASONWONLOST");
                            //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "MLS_REASONWONLOST"), "", "", "", "", "", "", "", "", "MLS_REASONWONLOST")
                            goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("MLS_REASONWONLOST"), "", "", "", "", "", "", "", "", "MLS_REASONWONLOST");
                            return false;
                        }

                        // Fill 'Date Won' field if empty
                        if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_DATECLOSED", 1).ToString()) | string.IsNullOrEmpty(doForm.doRS.GetFieldVal("DTE_DATECLOSED", 1).ToString()))
                        {
                            doForm.doRS.SetFieldVal("DTE_DATECLOSED", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                        }

                        // Fill Expected Close Date field if empty
                        if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 2).ToString()) | string.IsNullOrEmpty(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1).ToString()))
                        {
                            doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                        }
                        bRunNext = true;
                        bEnforceVal = scriptManager.RunScript("Opp_EnforceValue", ref par_doCallingObject, ref par_oReturn, ref bRunNext, ref par_sSections);
                        if (bEnforceVal == false)
                        {
                            //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
                            goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
                            return false;
                        }

                        break;
                    case 1:
                        //On Hold
                        bRunNext = true;
                        bEnforceVal = scriptManager.RunScript("Opp_EnforceValue", ref par_doCallingObject, ref par_oReturn, ref bRunNext, ref par_sSections);
                        if (bEnforceVal == false)
                        {
                            //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
                            goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
                            return false;
                        }

                        break;
                    case 4:
                    case 5:
                        //Cancelled, Delete
                        //Fill 'Date Won' field (if empty) with today, the date we lost or cancelled the opportunity
                        if (!goTR.IsDate(doForm.doRS.GetFieldVal("DTE_DATECLOSED", 2).ToString()) | string.IsNullOrEmpty(doForm.doRS.GetFieldVal("DTE_DATECLOSED", 1).ToString()))
                        {
                            doForm.doRS.SetFieldVal("DTE_DATECLOSED", Strings.Format(goTR.NowLocal(), "yyyy-MM-dd"), 2);
                        }
                        bRunNext = true;
                        bEnforceVal = scriptManager.RunScript("Opp_EnforceValue", ref par_doCallingObject, ref par_oReturn, ref bRunNext, ref par_sSections);
                        if (bEnforceVal == false)
                        {
                            //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
                            goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
                            return false;
                        }

                        break;
                }
            }

            //	StatusFunc	***This has been added above
            //'	CalcReviewOverdue	***No longer used. Field no longer exists on Opp
            //	CalcID				  'Calculates ID number. No longer needed

            //-----	CalcProbability	  'Calculates Value, Probability % and Value Index
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CalcProbability"))
            {
                par_doCallingObject = doForm;
                bool bRunNext = true;
                scriptManager.RunScript("Opp_CalcProbability", ref par_doCallingObject, ref par_oReturn, ref bRunNext, ref par_sSections);
            }


            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CopyNotesToJournalIfNewOpp"))
            {
                if (doForm.GetMode() == "CREATION")
                {
                    //CS 4/28/09: Make sure Notes field has a value
                    if (!string.IsNullOrEmpty(doForm.doRS.GetFieldVal("MMO_NOTES").ToString()))
                    {
                        //CS 4/20/09
                        //Check if there is a value in the Journal field already
                        sJournal = doForm.doRS.GetFieldVal("MMO_JOURNAL").ToString();
                        //5/20/09
                        if (!string.IsNullOrEmpty(sJournal))
                        {
                            doForm.doRS.SetFieldVal("MMO_JOURNAL", sJournal + " " + doForm.doRS.GetFieldVal("MMO_NOTES"));
                        }
                        else
                        {
                            par_doCallingObject = doForm; par_oReturn = sDateStamp;
                            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", "", "CODE", "USERNOOFFSETLABEL");
                            sDateStamp = par_oReturn.ToString();
                            doForm = (Form)par_doCallingObject;
                            //returns var sDateStamp
                            doForm.doRS.SetFieldVal("MMO_JOURNAL", sJournal + sDateStamp + " " + doForm.doRS.GetFieldVal("MMO_NOTES"));
                        }

                    }
                }
            }

            //-----	CreateActLog
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CreateActivity"))
            {
                //If doForm.GetMode() <> "CREATION" Then		'and doForm.GetFieldVal("MLS_STATUS",2) = 0 Then

                //check if we already ran this script. If so don't run it again.
                if (doForm.oVar.GetVar("Opp_CreateActLog_Ran").ToString() != "1")
                {
                    //Only try to create an AC log if have the option set in workgroup options to do so
                    if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONSAVEOPP_CREATE_AC").ToString() == "1")
                    {
                        par_doCallingObject = doForm;
                        bool bRunNext = true;
                        //bool bRunNext = false;
                        bResult = scriptManager.RunScript("Opp_CreateActLog", ref par_doCallingObject, ref par_oReturn, ref bRunNext, ref par_sSections);

                        //*** MI 11/28/07 Commented the following block because Return True caused FormOnSave
                        //code to exit when the Journal Activity record was created successfully. I am not 100%
                        //sure, but it appears that the ElseIf test below is redundant. I can't explain Return True
                        //in the ElseIf case any other way than this being a hasty attempt to stop some code below this
                        //from running when it should be conditionalized separately. Whether a journal activity
                        //is or isn't created in Activity_CreateActLog, we must continue AC_FormOnSave or the
                        //Activity itself will be saved (Return True!) without being processed by half of FormOnSave.

                        //If bResult = False Then
                        //    'we set a var in Opp_CreateActLog which tells us to display error at end of save
                        //    'If ran create act log and need to display message asking if want to create journal
                        //ElseIf bResult = True And doForm.oVar.GetVar("Opp_CreateActLog_Ran") = "1" And doForm.oVar.GetVar("ContinueSave") <> "1" Then
                        //    Return True
                        //End If
                    }
                }
                //End If
            }

            //	UpdateContactInterestedInItem	*** Review this procedure - not sure how to handle rowset object
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "UpdateContactInterestedInProduct"))
            {
                if (doForm.oVar.GetVar("Opp_UpdateContactItem_Ran") != null && doForm.oVar.GetVar("Opp_UpdateContactItem_Ran").ToString() != "1")
                {
                    par_doCallingObject = doForm;
                    scriptManager.RunScript("Opp_UpdateContactItem", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                }
            }

            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "DisplayErrorMessages"))
            {
                //If we had previously run this script which called updating linked CN's next action/contact
                //dates and/or creating an ACT the commit of the Contact  or AC had failed we need to display a message on the AC form to
                //to the user.
                sScriptVar = doForm.oVar.GetVar("ScriptMessages").ToString();
                if (!string.IsNullOrEmpty(sScriptVar))
                {
                    if (Strings.Len(sScriptVar) < 500)
                    {
                        doForm.MessageBox(sScriptVar, clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", null, null, "OK", "", "", "", "OP_FormOnSave_ScriptMessages");
                    }
                    else
                    {
                        doForm.MessageBox(Strings.Left(sScriptVar, 497) + "...", clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", null, null, "OK", "", "", "", "OP_FormOnSave_ScriptMessages");
                    }
                    return true;
                }
            }

            // CS 11/19/2014: Add core functionality that inserting a journal creates a Journal activity
            if (scriptManager.IsSectionEnabled(sProc, par_sSections, "CreateJournalActivity"))
            {
                // check if we already ran this script. If so don't run it again.
                // This only happens when a journal entry is added via the form. This is b/c it requires interaction with the journal messagebox.
                if (doForm.oVar.GetVar("OP_CreateActLog_Ran") != "1")
                {
                    if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONSAVEOPP_CREATE_AC") == "1")
                    {
                        par_doCallingObject = doForm;
                        bool bRunNext = true;
                        bResult = scriptManager.RunScript("CreateActLog", ref par_doCallingObject, ref par_oReturn, ref bRunNext, ref par_sSections, "OP");
                    }
                }
            }

            int iStage = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STAGE", 2));
            // CS 11/19/2014 Update stage dates if stage changed.
            if (goP.GetVar("OppStageOnLoad") != goTR.NumToString(iStage))
            {
                // Check what the stage is and update the date field with today/now
                switch (iStage)
                {
                    case 10 // Lead
                   :
                        {
                            doForm.doRS.SetFieldVal("DTT_LeadDate", "Today|Now");
                            break;
                        }

                    case 20 // Opp
             :
                        {
                            doForm.doRS.SetFieldVal("DTT_OppDate", "Today|Now");
                            break;
                        }

                    case 30 // Quote
             :
                        {
                            doForm.doRS.SetFieldVal("DTT_QuoteDate", "Today|Now");
                            break;
                        }
                }
            }



            par_doCallingObject = doForm;

            return true;
        }

        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            Form doForm = (Form)par_doCallingObject;

            //REVIEW:
            //This is a separate proc since it is called multiple times in OnSave Proc

            // decimal cValueFld = default(decimal);

            // Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            //Get system value

            // First checking whether the field value is numeric, then checking for
            // two conditions, 0 if numeric, empty if not numeric
            //No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    //if (cValueFld == 0)
            //    //{
            //    //    doForm.MoveToField("CUR_UNITVALUE");
            //    //    //cValueFld = ""
            //    //    doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //    //    //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //    //    goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //    //    return false;
            //    //}
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            par_doCallingObject = doForm;
            return true;

        }



        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        //public bool OP_RecordOnSave_pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        //{
        //    // par_doCallingObject: Rowset object containing the record to be saved.
        //    // par_doArray: Unused.
        //    // par_sMode: 'CREATION' or 'MODIF'.
        //    // par_s2 to par_s5: Unused.
        //    // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
        //    // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
        //    // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

        //    string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


        //    return true;
        //}
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            clRowSet doRS = (clRowSet)par_doCallingObject;


            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "LNK_IN_OP", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if (rsOL1.GetFirst() == 1)
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));

                doRS.SetFieldVal("CUR_VALUE", curValue);
                doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);

            }
            par_doCallingObject = doRS;
            return true;
        }
        public bool OP_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                //Ticket# 4008, send <NAME_EMAIL>
                string sLoginUserId = goP.GetMe("ID").ToLower();
                if (sLoginUserId != "e9e29589-e5ca-4a8a-5553-a43c015c33d9")
                {
                    clRowSet doRS = (clRowSet)par_doCallingObject;

                    string sCompany = Convert.ToString(doRS.GetFieldVal("LNK_FOR_CO%%TXT_COMPANYNAME"));
                    string sContact = Convert.ToString(doRS.GetFieldVal("LNK_INVOLVES_CN%%TXT_NAMELAST")) + " " + Convert.ToString(doRS.GetFieldVal("LNK_INVOLVES_CN%%TXT_NAMEFIRST"));
                    string sNotes = Convert.ToString(doRS.GetFieldVal("MMO_JOURNAL"));
                    string sActionType = doRS.iRSType == clC.SELL_ADD ? " Created" : " Updated";
                    string sTitle = Convert.ToString(doRS.GetFieldVal("LNK_INVOLVES_CN%%TXT_TITLETEXT"));
                    string sModType = goP.GetMe("NAME");
                    //string sDate = Convert.ToString(doRS.GetFieldVal("DTT_MODTIME"));
                    string sCompanyAddr = Convert.ToString(doRS.GetFieldVal("LNK_FOR_CO%%TXT_ADDRMAILING"));
                    string sCompanyCity = Convert.ToString(doRS.GetFieldVal("LNK_FOR_CO%%TXT_CITYMAILING"));
                    string sCompanyState = Convert.ToString(doRS.GetFieldVal("LNK_FOR_CO%%TXT_STATEMAILING"));
                    sCompanyAddr = sCompanyAddr + "<br>" + sCompanyCity + ", " + sCompanyState;

                    string sTo = "<EMAIL>";
                    string sSubject = "Selltis - Opportunity has been" + sActionType;
                    string sEmailBody = "<div><b>The following Opportunity has been" + sActionType + "</b></div><br>";

                    sEmailBody = sEmailBody + "<div><div><b>Company</b></div> <div>" + sCompany + "</div></div><br>";
                    sEmailBody = sEmailBody + "<div><div><b>Address</b></div> <div>" + sCompanyAddr + "</div></div><br>";
                    sEmailBody = sEmailBody + "<div><div><b>Contact</b></div> <div>" + sContact + "</div></div><br>";
                    sEmailBody = sEmailBody + "<div><div><b>Title</b></div> <div>" + sTitle + "</div></div><br>";
                    sEmailBody = sEmailBody + "<div><div><b>Journal</b></div> <div>" + sNotes + "</div></div><br>";
                    sEmailBody = sEmailBody + "<div><div><b>" + sActionType.Trim() + " By</b></div> <div>" + sModType + "</div></div><br>";
                    //sEmailBody = sEmailBody + "<div><div><b>Date" + sActionType + "</b></div> <div>" + sDate + "</div></div><br>";

                    clEmail _clemail = new clEmail();
                    bool bmailsent = _clemail.SendSMTPEmailNew(sSubject, sEmailBody, sTo, "", "", "", "", "", "", true);
                }
            }
            catch (Exception ex)
            {
                goLog.Log("OP change mail send", "Error: " + ex.Message, clC.SELL_LOGLEVEL_DEBUG, false, true);
                return true;
            }

            return true;
        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = null;
            clRowSet doRS1 = null;


            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS1.GetFieldVal("Gid_ID").ToString() + "'", "LNK_IN_OP", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doRS1.SetFieldVal("CUR_VALUE", curValue);
                    doRS1.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doRS1;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "LNK_IN_OP", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));

                    doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                    doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                    doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);

                }
                par_doCallingObject = doForm;
            }

            return true;

        }

        public bool PR_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;

            return true;
        }
        public bool PR_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }

        public bool QL_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Quotline_CalcTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }

        public bool TD_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool VE_FormControlOnChange_LNK_RELATED_CO_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // SKO Tic 409 Show city, state, phone number & email address on top of vendor form
            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.GetLinkCount("LNK_Related_CO") < 1)
            {
                doForm.MoveToField("LNK_Related_CO");
                return false;
            }

            clRowSet doCompany = new clRowSet("CO", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_RELATED_CO") + "'", null/* Conversion error: Set to default value for this argument */, "TXT_CITYMAILING,TXT_STATEMAILING,TEL_PHONENO");
            if (doCompany.GetFirst() != 1)
                return false;

            doForm.doRS.SetFieldVal("TXT_CITY", doCompany.GetFieldVal("TXT_CITYMAILING"));
            doForm.doRS.SetFieldVal("TXT_STATE", doCompany.GetFieldVal("TXT_STATEMAILING"));
            doForm.doRS.SetFieldVal("TEL_PHONENO", doCompany.GetFieldVal("TEL_PHONENO"));

            return true;
        }
        public bool US_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Rowset object containing the record to be saved.
            // par_doArray: Unused.
            // par_sMode: 'CREATION' or 'MODIF'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // --- HISTORY ---
            // 09/28/05	MAR	Script					USER_RecordOnSave: Commented filling secondary links 
            // because that slows down the saving too much.

            // goP.TraceLine("", "", sProc)

            clRowSet doRS;
            // Dim doLink As Object
            string sFileName = "US";
            string sSysName = "";

            // CS: Comment all calls to IsObjAss
            // If Not IsObjectAssigned(par_doCallingObject) Then
            // goErr.SetError(30012, sProc, "", "par_doCallingObject")
            // ' An internal error has occurred. Object '[1]' is not assigned.
            // '
            // 'Please contact Selltis support.
            // Return False
            // Else
            doRS = (clRowSet)par_doCallingObject;
            // End If

            // Enforce last name and code
            // If goP.GetRunMode() <> "Import" Then
            if (doRS.bBypassValidation != true)
            {
            }

            // Fill Original Creation Date/Time if blank
            if (doRS.GetInfo(clC.SELL_ROWSET_TYPE) == "2")
            {
                if (doRS.GetFieldVal("dtt_origcreatedtime", 1) == "")
                    doRS.SetFieldVal("dtt_origcreatedtime", "Today|Now");
            }

            // End If

            // Uppercase the logon name
            // doRS.SetFieldVal("TXT_LOGONNAME", UCase(doRS.GetFieldVal("TXT_LOGONNAME")))


            // Uppercase the User Code. This prevents an issue in clData with lowercase
            // characters in the code that, when used in record IDs, cause Edit>Delete
            // not to work and possibly other related issues.
            doRS.SetFieldVal("TXT_CODE", Strings.UCase(doRS.GetFieldVal("TXT_CODE").ToString()));

            // ----------- AUTO-FILLED FIELDS ---------
            if (goP.GetRunMode() != "Import")
            {
            }

            // VS 01092018 TKT#2033 : JF not available in GPS Site causing error on User Save. Commenting below code and set RunNext = false.
            // If doRS.GetFieldVal("TXT_TITLETEXT") = "" Then
            // doRS.SetFieldVal("TXT_TITLETEXT", doRS.GetFieldVal("LNK_RELATED_JF%%TXT_JOBFUNCNAME"))
            // End If

            par_bRunNext = false;

            par_doCallingObject = doRS;
            return true;
        }

        //    public bool OP_ViewControlOnChange_BTN_UpdateNextAction7_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        //    {
        //        // par_doCallingObject: Form object calling this script. Do not delete in script!
        //        // par_doArray: Unused.
        //        // par_s1: Unused.
        //        // par_s2 to par_s5: Unused.
        //        // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
        //        // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
        //        // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

        //        // *** For notes on how to create a custom script, see clScripts.vb ***
        //        string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
        //        par_bRunNext=false
        //        // clDesktop doDesktop = par_doCallingObject;
        //        Desktop doDesktop = (Desktop)par_doCallingObject;
        //        string sID = goUI.GetLastSelected("SELECTEDRECORDID");

        //        if (sID == "" | sID == null)
        //        {
        //            //doDesktop.MessageBox("Please select a record.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null);
        //            return true;
        //        }

        //        //try
        //        //{
        //            par_doCallingObject = doDesktop;
        //            // CS 12/3/2014 Call function, passing record ID in par_s2, number days in par_s3
        //            if (scriptManager.RunScript("UpdateNextAction", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, sID, "7") == false)
        //            {
        //            }
        //        //}

        //    //catch (Exception ex)
        //    //{
        //    //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
        //    //        goErr.SetError(ex, 45105, sProc);
        //    //}

        //       par_doCallingObject = doDesktop;

        //        return true;
        //    }

        public bool CN_FormControlOnChange_LNK_RELATED_CO_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string work;
            string customerNameL;
            string street;
            double workd;
            bool addressBlank = false;
            //..goErr.SetError();
            par_bRunNext = false;
            Form objForm = (Form)par_doCallingObject;

            if (objForm.FormRowSet.GetLinkCount("LNK_Related_CO") < 1)
            {
                objForm.MoveToField("LNK_Related_CO");
                return false;
            }

            clRowSet companyRowSet = new clRowSet("CO", 3, "GID_ID='" + objForm.FormRowSet.GetFieldVal("LNK_RELATED_CO") + "'", "", "TXT_COMPANYNAME,TEL_PHONENO,TEL_FAXNO,URL_WEBPAGE,TXT_ADDRMAILING,TXT_CITYMAILING,TXT_STATEMAILING,TXT_ZIPMAILING,TXT_COUNTRYMAILING");

            if (companyRowSet.GetFirst() != 1)
            {
                return false;
            }

            if (objForm.FormRowSet.GetFieldVal("TXT_COMPANYNAMETEXT").ToString() == "")
            {
                objForm.FormRowSet.SetFieldVal("TXT_COMPANYNAMETEXT", companyRowSet.GetFieldVal("TXT_COMPANYNAME"));
            }
            if (objForm.FormRowSet.GetFieldVal("TEL_BUSPHONE").ToString() == "")
            {
                objForm.FormRowSet.SetFieldVal("TEL_BUSPHONE", companyRowSet.GetFieldVal("TEL_PHONENO"));
            }
            if (objForm.FormRowSet.GetFieldVal("TEL_FAX").ToString() == "")
            {
                objForm.FormRowSet.SetFieldVal("TEL_FAX", companyRowSet.GetFieldVal("TEL_FAXNO"));
            }
            if (objForm.FormRowSet.GetFieldVal("URL_WEB").ToString() == "")
            {
                objForm.FormRowSet.SetFieldVal("URL_WEB", companyRowSet.GetFieldVal("URL_WEBPAGE"));
            }

            customerNameL = companyRowSet.GetFieldVal("TXT_COMPANYNAME").ToString();
            street = companyRowSet.GetFieldVal("TXT_ADDRMAILING").ToString();

            if (street != "")
            {
                workd = Convert.ToDouble(street.Contains(Environment.NewLine));
                if (workd > 0)
                {
                    work = Microsoft.VisualBasic.Strings.Mid(street, 1, Convert.ToInt32(workd) - 1);
                    if (work == customerNameL)
                    {
                        street = goTR.FromTo(street, Convert.ToInt32(workd) + 2);
                    }
                }

                if (objForm.FormRowSet.GetFieldVal("TXT_ADDRBUSINESS").ToString() == "")
                {
                    addressBlank = true;
                    objForm.FormRowSet.SetFieldVal("TXT_ADDRBUSINESS", customerNameL + Environment.NewLine + street);
                }
            }

            if (addressBlank)
            {
                objForm.FormRowSet.SetFieldVal("TXT_STATEBUSINESS", companyRowSet.GetFieldVal("TXT_STATEMAILING"));
                objForm.FormRowSet.SetFieldVal("TXT_ZIPBUSINESS", companyRowSet.GetFieldVal("TXT_ZIPMAILING"));
                objForm.FormRowSet.SetFieldVal("TXT_COUNTRYBUSINESS", companyRowSet.GetFieldVal("TXT_COUNTRYMAILING"));
                addressBlank = false;
            }

            //objForm.FormRowSet.SetFieldVal("LNK_RELATED_IU", companyRowSet.GetFieldVal("LNK_RELATED_IU"));
            par_doCallingObject = objForm;
            return true;
        }
        public bool CO_FormControlOnChange_BTN_INSERTLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            ////MI 11/8/07 Changed time stamp to local time, no label.
            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            ////par_s3 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            //Dim sWork As String
            string sParams = "";

            //goP.TraceLine("", "", sProc)
            //goP.TraceLine("par_sFieldName: '" & par_sFieldName & "'", "", sProc)
            par_doCallingObject = doForm;
            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", "sDateStamp", "CODE", "USERNOOFFSETLABEL");

            doForm = (Form)par_doCallingObject;
            //returns var sDateStamp

            //goTR.StrWrite(sParams, "TITLE", "Add Note")
            //CS:Inputbox must be replaced in web context.
            //sWork = InputBox(doForm.oVar.GetVar("sDateStamp") & " ", sParams, doForm)
            //next line wt mod for release
            string sJournalType = doForm.GetControlVal("NDB_MLS_JOURNALTYPE");
            string sValue = goData.goList.LReadSeek("XX:JOURNALTYPE", "KEY", sJournalType);

            doForm.MessageBox("Please enter your journal note.", clC.SELL_MB_INPUTBOX, "Add Journal Note", "OK", "", "", doForm.oVar.GetVar("sDateStamp") + " " + sValue + " ", "MessageBoxEvent", "", "", null, null, "OK", "", "", "", System.Reflection.MethodInfo.GetCurrentMethod().Name);

            par_bRunNext = false;
            par_doCallingObject = doForm;
            return true;



            //sWork = doForm.oVar.GetVar("sDateStamp")
            //If sWork <> "" Then
            //    sWork &= vbCrLf & vbCrLf & doForm.doRS.GetFieldVal("MMO_JOURNAL")
            //    doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork)
            //    doForm.MoveToField("MMO_JOURNAL")
            //    'doForm.oVar.SetVar("sJournalVal", sWork)
            //End If        
        }
        public bool CN_FormControlOnChange_BTN_INSERTLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            ////MI 11/8/07 Changed time stamp to local time, no label.
            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            ////par_s3 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            //Dim sWork As String
            string sParams = "";

            //goP.TraceLine("", "", sProc)
            //goP.TraceLine("par_sFieldName: '" & par_sFieldName & "'", "", sProc)
            par_doCallingObject = doForm;
            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", "sDateStamp", "CODE", "USERNOOFFSETLABEL");

            doForm = (Form)par_doCallingObject;
            //returns var sDateStamp

            //goTR.StrWrite(sParams, "TITLE", "Add Note")
            //CS:Inputbox must be replaced in web context.
            //sWork = InputBox(doForm.oVar.GetVar("sDateStamp") & " ", sParams, doForm)
            //next line wt mod for release
            string sJournalType = doForm.GetControlVal("NDB_MLS_JOURNALTYPE");
            string sValue = goData.goList.LReadSeek("XX:JOURNALTYPE", "KEY", sJournalType);

            doForm.MessageBox("Please enter your journal note.", clC.SELL_MB_INPUTBOX, "Add Journal Note", "OK", "", "", doForm.oVar.GetVar("sDateStamp") + " " + sValue + " ", "MessageBoxEvent", "", "", null, null, "OK", "", "", "", System.Reflection.MethodInfo.GetCurrentMethod().Name);

            par_bRunNext = false;
            par_doCallingObject = doForm;
            return true;



            //sWork = doForm.oVar.GetVar("sDateStamp")
            //If sWork <> "" Then
            //    sWork &= vbCrLf & vbCrLf & doForm.doRS.GetFieldVal("MMO_JOURNAL")
            //    doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork)
            //    doForm.MoveToField("MMO_JOURNAL")
            //    'doForm.oVar.SetVar("sJournalVal", sWork)
            //End If        
        }
        public bool OP_FormControlOnChange_BTN_INSERTLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            ////MI 11/8/07 Changed time stamp to local time, no label.
            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            ////par_s3 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            //Dim sWork As String
            string sParams = "";

            //goP.TraceLine("", "", sProc)
            //goP.TraceLine("par_sFieldName: '" & par_sFieldName & "'", "", sProc)
            // par_doCallingObject = doForm;
            scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", "sDateStamp", "CODE", "USERNOOFFSETLABEL");

            doForm = (Form)par_doCallingObject;
            //returns var sDateStamp

            //goTR.StrWrite(sParams, "TITLE", "Add Note")
            //CS:Inputbox must be replaced in web context.
            //sWork = InputBox(doForm.oVar.GetVar("sDateStamp") & " ", sParams, doForm)
            //next line wt mod for release
            string sJournalType = doForm.GetControlVal("NDB_MLS_JOURNALTYPE");
            string sValue = goData.goList.LReadSeek("XX:JOURNALTYPE", "KEY", sJournalType);

            doForm.MessageBox("Please enter your journal note.", clC.SELL_MB_INPUTBOX, "Add Journal Note", "OK", "", "", doForm.oVar.GetVar("sDateStamp") + " " + sValue + " ", "MessageBoxEvent", "", "", null, null, "OK", "", "", "", System.Reflection.MethodInfo.GetCurrentMethod().Name);

            par_bRunNext = false;
            par_doCallingObject = doForm;
            return true;



            //sWork = doForm.oVar.GetVar("sDateStamp")
            //If sWork <> "" Then
            //    sWork &= vbCrLf & vbCrLf & doForm.doRS.GetFieldVal("MMO_JOURNAL")
            //    doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork)
            //    doForm.MoveToField("MMO_JOURNAL")
            //    'doForm.oVar.SetVar("sJournalVal", sWork)
            //End If        
        }

        public bool MessageBoxEvent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = null;
            sProc = "Script::MessageBoxEvent";
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            Form doForm = null;
            Desktop doDesktop = null;

            if (par_s5.ToUpper().Contains("FORM"))
            {
                doForm = (Form)par_doCallingObject;
            }
            else if (par_s5.ToUpper().Contains("VIEW"))
            {
                doDesktop = (Desktop)par_doCallingObject;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
            }

            //par_doCallingObject = (Form)par_doCallingObject;
            string sWork = null;
            //input value
            string sWork2 = null;
            string sJournal = null;
            //original value in journal field
            clRowSet doRS = default(clRowSet);
            bool bUpdateFailed = false;
            bool bNoPerm = false;
            bool bError = false;
            bool bReqMissing = false;
            string s = Strings.UCase(par_s5);
            try
            {
                switch (s)
                {
                    case "OP_FORMCONTROLONCHANGE_BTN_INSERTLINE_PRE":
                        switch (par_s1.ToUpper())
                        {
                            case "OK":
                                sJournal = doForm.doRS.GetFieldVal("MMO_Journal").ToString();
                                //CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                //Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                sWork = par_s2;
                                doForm.oVar.SetVar("JournalWithHardReturns", sWork + Environment.NewLine + sJournal);
                                if (!string.IsNullOrEmpty(sWork))
                                {
                                    //CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                    //If not defined in WOP, default is 1
                                    if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS").ToString() != "0")
                                    {
                                        sWork = goTR.Replace(sWork, Environment.NewLine, Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString());
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Environment.NewLine + sJournal);
                                    }
                                    else
                                    {
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Environment.NewLine + sJournal);
                                    }
                                    doForm.MoveToField("MMO_JOURNAL");
                                    //doForm.oVar.SetVar("sJournalVal", sWork)
                                }
                                break;
                        }

                        break;
                    case "CN_FORMCONTROLONCHANGE_BTN_INSERTLINE_PRE":
                        switch (par_s1.ToUpper())
                        {
                            case "OK":
                                sJournal = doForm.doRS.GetFieldVal("MMO_Journal").ToString();
                                //CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                //Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                sWork = par_s2;
                                doForm.oVar.SetVar("JournalWithHardReturns", sWork + Environment.NewLine + sJournal);
                                if (!string.IsNullOrEmpty(sWork))
                                {
                                    //CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                    //If not defined in WOP, default is 1
                                    if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS").ToString() != "0")
                                    {
                                        sWork = goTR.Replace(sWork, Environment.NewLine, Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString());
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Environment.NewLine + sJournal);
                                    }
                                    else
                                    {
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Environment.NewLine + sJournal);
                                    }
                                    doForm.MoveToField("MMO_JOURNAL");
                                    //doForm.oVar.SetVar("sJournalVal", sWork)
                                }
                                break;
                        }

                        break;
                    case "CO_FORMCONTROLONCHANGE_BTN_INSERTLINE_PRE":
                        switch (par_s1.ToUpper())
                        {
                            case "OK":
                                sJournal = doForm.doRS.GetFieldVal("MMO_Journal").ToString();
                                //CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                //Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                sWork = par_s2;
                                doForm.oVar.SetVar("JournalWithHardReturns", sWork + Environment.NewLine + sJournal);
                                if (!string.IsNullOrEmpty(sWork))
                                {
                                    //CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                    //If not defined in WOP, default is 1
                                    if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS").ToString() != "0")
                                    {
                                        sWork = goTR.Replace(sWork, Environment.NewLine, Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString());
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Environment.NewLine + sJournal);
                                    }
                                    else
                                    {
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Environment.NewLine + sJournal);
                                    }
                                    doForm.MoveToField("MMO_JOURNAL");
                                    //doForm.oVar.SetVar("sJournalVal", sWork)
                                }
                                break;
                        }

                        break;
                    case "AC_VIEWCONTROLONCHANGE_BTN_COMPLETEALL":
                        bUpdateFailed = false;
                        doRS = new clRowSet("AC", 1, "LNK_CreditedTo_US='<%MEID%>' AND CHK_CORRRECEIVED=1 and MLS_STATUS=0", goData.GetDefaultSort("AC") + " a", "*");

                        if (doRS.GetFirst() == 1)
                        {
                            do
                            {
                                doRS.SetFieldVal("MLS_STATUS", 1, 2);
                                if (doRS.Commit() == 0)
                                {
                                    bUpdateFailed = true;
                                    if (goErr.GetLastError("NUMBER") == "E47250")
                                    {
                                        bNoPerm = true;
                                        //failed b/c of permissions
                                    }
                                    else if (goErr.GetLastError("NUMBER") == "E47260")
                                    {
                                        bReqMissing = true;
                                    }
                                    else
                                    {
                                        bError = true;
                                        //failed b/c of other errors
                                    }
                                }
                                if (doRS.GetNext() == 0)
                                    break; // TODO: might not be correct. Was : Exit Do
                            } while (true);
                            if (bUpdateFailed)
                            {
                                //par_doCallingObject=doForm;
                                //goUI.RefreshOpenDesktop();
                                if (bError == true & bNoPerm == false)
                                {
                                    doDesktop.MessageBox(ref par_doCallingObject, "Some records couldn't be marked as completed. Please try again and contact your Selltis administrator if you are unsuccessful.");
                                }
                                else if (bError == false & bNoPerm == true)
                                {
                                    doDesktop.MessageBox(ref par_doCallingObject, "Some records couldn't be marked as completed because you do not have permission to edit the record(s).");
                                }
                                else if (bError == true & bNoPerm == true)
                                {
                                    doDesktop.MessageBox(ref par_doCallingObject, "Some records couldn't be marked as completed. You either do not have permission to edit the remaining records or there are required fields missing." + Environment.NewLine + "You should open each record " + "remaining in the view, review mandatory fields and attempt to Save.");
                                }
                                else if (bReqMissing == true)
                                {
                                    doDesktop.MessageBox(ref par_doCallingObject, "Some records couldn't be marked as completed. Please open each record " + "remaining in the view, fill out all mandatory fields and Save.");
                                }
                                doRS = null;
                                return true;
                            }
                            //goUI.RefreshOpenDesktop();

                        }
                        else
                        {
                            //First not found.
                        }
                        doRS = null;

                        break;

                    case "CO_FORMONSAVE_LINKTEAMLEADERMSGBOX":
                        //The 3 buttons are an answer to the question ""You changed the Team Leader from '" & doForm.oVar.GetVar("TeamLeaderNameOnLoad") & "' to '" & doForm.doRS.GetFieldVal("LNK_TeamLeader_US%%SYS_Name") & "'. Would you like to link the new Team Leader to this Company's Contacts?"
                        //Button labels are:
                        //   YES: Yes
                        //   NO: Yes, and unlink old Team Leader
                        //   CANCEL: No
                        switch (par_s1.ToUpper())
                        {
                            case "1":
                            case "2":
                                //Yes, Yes, and unlink old Team Leader
                                //==> After rowset supports dynamically fetching missing links, put this to the sFields parameter: "NOLINKS,LNK_Related_US,LNK_Related_CO". Test this!
                                //This will speed up the Contact updates by loading only the links necessary for generateSysname and for SetFieldVals here.
                                doRS = new clRowSet("CN", clC.SELL_EDIT, "LNK_Related_CO='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "*", -1, "", "", "", "", "", true, true);
                                if (doRS.Count() > 0)
                                {
                                    sWork = goTR.ExtractString(par_s4, 1, "|");
                                    //User to link (new team leader)
                                    sWork2 = goTR.ExtractString(par_s4, 2, "|");
                                    //User to unlink (original team leader)
                                    do
                                    {
                                        if (par_s1.ToUpper() == "2")
                                        {
                                            //User clicked the second button: clear the original team leader
                                            doRS.ClearLink("LNK_Related_US", sWork2);
                                        }
                                        //Set the new team leader
                                        doRS.SetFieldVal("LNK_Related_US", sWork);
                                        //Try to save the change. if the user doesn't have selective edit perm on the Contact, Commit will fail.
                                        if (doRS.Commit() != 1)
                                        {
                                            goErr.SetWarning(35000, sProc, "User '" + sWork + "' couldn't be linked to Contact '" + doRS.GetFieldVal("SYS_Name") + "'.");
                                            //Proceed because we expect Commit to fail if no edit permission. 
                                        }
                                        if (doRS.GetNext() != 1)
                                            break; // TODO: might not be correct. Was : Exit Do
                                    } while (true);
                                }
                                break;
                            default:
                                //3 - No
                                break;
                                //Proceed
                        }

                        break;
                    case "CO_FORMONSAVE_SCRIPTMESSAGES":
                        switch (par_s1.ToUpper())
                        {
                            case "OK":
                                doForm.oVar.SetVar("ScriptMessages", "");
                                break;
                        }

                        break;
                    case "COMPANY_DUPCHECK":
                        switch (par_s1.ToUpper())
                        {
                            case "NO":
                                //doForm.oVar.SetVar("CancelSave", "1") CS 8/28/08 #2
                                doForm.oVar.SetVar("Company_DupCheck", "");
                                //CS 8/28/08
                                break;
                            case "YES":
                                //CS 8/28/08; call save
                                //Cs 8/5/09
                                if (doForm.MessageBoxFormMode == "SAVEANDKEEPOPEN")
                                {
                                    if (doForm.Save(5) == 0)
                                    {
                                        return false;
                                    }
                                }
                                else
                                {
                                    if (doForm.Save(0, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
                                    {
                                        return false;
                                    }
                                }
                                break;

                        }

                        break;
                    case "CONTACT_NOTESLENGTH":
                        switch (par_s1.ToUpper())
                        {
                            case "YES":
                                doForm.MoveToTab(2);
                                doForm.MoveToField("MMO_Note");
                                doForm.oVar.SetVar("CancelSave", "1");
                                break;
                        }
                        break;
                    case "CONTACT_COUNTPHONEFIELDS":
                        switch (par_s1.ToUpper())
                        {
                            case "NO":
                                doForm.oVar.SetVar("CancelSave", "1");
                                break;
                        }

                        break;
                    case "CONTACT_DUPCHECK":
                        switch (par_s1.ToUpper())
                        {
                            case "NO":
                                doForm.oVar.SetVar("CancelSave", "1");
                                break;
                        }

                        break;
                    case "EX_FORMONSAVE_PERSONAL":
                        switch (par_s1.ToUpper())
                        {
                            case "YES":
                                doForm.doRS.SetFieldVal("CHK_REIMBURSABLE", 1, 2);
                                break;
                            case "CANCEL":
                                doForm.oVar.SetVar("bReimbursableValue", 0);
                                doForm.MoveToField("LNK_RELATED_EA");
                                doForm.oVar.SetVar("CancelSave", "1");
                                break;
                        }

                        break;

                    //case "OP_FORMCONTROLONCHANGE_BTN_INSERTLINE":
                    //    switch (par_s1.ToUpper())
                    //    {
                    //        case "OK":
                    //            sJournal = doForm.doRS.GetFieldVal("MMO_Journal").ToString();
                    //            //CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                    //            //Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                    //            sWork = par_s2;
                    //            doForm.oVar.SetVar("JournalWithHardReturns", sWork + Environment.NewLine + sJournal);
                    //            if (!string.IsNullOrEmpty(sWork))
                    //            {
                    //                //CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                    //                //If not defined in WOP, default is 1
                    //                if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS").ToString() != "0")
                    //                {
                    //                    sWork = goTR.Replace(sWork, Environment.NewLine, Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString());
                    //                    doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Environment.NewLine + sJournal);
                    //                }
                    //                else
                    //                {
                    //                    doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Environment.NewLine + sJournal);
                    //                }
                    //                doForm.MoveToField("MMO_JOURNAL");
                    //                //doForm.oVar.SetVar("sJournalVal", sWork)
                    //            }
                    //            break;
                    //    }

                    //    break;                  
                    case "OP_FORMCONTROLONCHANGE_BTN_INSERTLINEMMR":
                        switch (par_s1.ToUpper())
                        {
                            case "OK":
                                sJournal = doForm.doRS.GetFieldVal("MMR_Journal").ToString();
                                doForm.doRS.SetFieldVal("MMR_Journal", par_s2 + "<br/>" + sJournal);
                                doForm.MoveToField("MMR_Journal");
                                break;
                        }

                        break;
                    case "OP_FORMCONTROLONCHANGE_BTN_INSERTPRESALE":
                        switch (par_s1.ToUpper())
                        {
                            case "YES":
                                scriptManager.RunScript("AddTextToField", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "MMO_QUESTIONNAIRE", doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%PRESALEQUEST").ToString(), "PREPEND");
                                break;
                        }

                        break;

                    case "OP_FORMONSAVE_NADATE":
                        switch (par_s1.ToUpper())
                        {
                            case "YES":
                                doForm.doRS.SetFieldVal("DTE_NEXTACTIONDATE", "2 weeks from today");
                                break;
                            case "NO":
                                break;
                        }

                        break;

                    case "OP_FORMONSAVE_SCRIPTMESSAGES":
                        switch (par_s1.ToUpper())
                        {
                            case "OK":
                                doForm.oVar.SetVar("ScriptMessages", "");
                                break;
                        }

                        break;


                    case "QL_FORMCONTROLONCHANGE_BTN_INSERTSPEC":
                        switch (par_s1.ToUpper())
                        {
                            case "YES":
                                scriptManager.RunScript("AddTextToField", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "MMO_DETAILS", doForm.doRS.GetFieldVal("LNK_FOR_MO%%MMO_SPECIFICATIONS").ToString(), "PREPEND");
                                break;
                        }

                        break;
                    case "QL_FORMONSAVE_SHARED":
                        switch (par_s1.ToUpper())
                        {
                            case "OK":
                                doForm.doRS.SetFieldVal("SI__SHARESTATE", 1, 2);
                                break;
                            case "CANCEL":
                                doForm.oVar.SetVar("CancelSave", "1");
                                break;
                        }

                        break;
                    case "QT_FORMCONTROLONCHANGE_BTN_INSERTLINE":
                        switch (par_s1.ToUpper())
                        {
                            case "OK":
                                sJournal = doForm.doRS.GetFieldVal("MMO_Journal").ToString();
                                //CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                //Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                sWork = par_s2;
                                doForm.oVar.SetVar("JournalWithHardReturns", sWork + Environment.NewLine + sJournal);
                                if (!string.IsNullOrEmpty(sWork))
                                {
                                    //CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                    //If not defined in WOP, default is 1
                                    if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS").ToString() != "0")
                                    {
                                        sWork = goTR.Replace(sWork, Environment.NewLine, Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString() + Microsoft.VisualBasic.Strings.Chr(32).ToString());
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Environment.NewLine + sJournal);

                                    }
                                    else
                                    {
                                        doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Environment.NewLine + sJournal);
                                        //doForm.oVar.SetVar("sJournalVal", sWork)
                                    }
                                    doForm.MoveToField("MMO_JOURNAL");
                                }

                                break;
                        }

                        break;

                    case "SHAREITEM":
                        switch (par_s1.ToUpper())
                        {
                            case "YES":
                                //Share the record
                                doForm.doRS.SetFieldVal("SI__SHARESTATE", 2, 2);
                                doForm.doRS.SetFieldVal("CHK_EXTDEVREVIEW", 0, 2);
                                break;
                        }

                        break;
                }

            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //   // goErr.SetError(ex, 45105, sProc);
                //    return false;
                //}
                return false;
            }
            // par_doCallingObject = doForm;

            if (par_s5.ToUpper().Contains("FORM"))
            {
                par_doCallingObject = doForm;
            }
            else if (par_s5.ToUpper().Contains("VIEW"))
            {
                par_doCallingObject = doDesktop;
            }
            else
            {
                par_doCallingObject = doForm;
            }

            return true;
        }
        public bool AC_FormControlOnChange_MLS_TYPE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            Form doForm = (Form)par_doCallingObject;
            par_bRunNext = false;
            //doForm.MoveToTab(1);
            //doForm.SetControlState("TAB_FORM[1]", 0);
            //doForm.MoveToTab(1);
            par_doCallingObject = doForm;

            return true;
        }


        public bool Opp_CreateActLog_pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "0", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: doForm.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //   12/22/2004 RAH: turned over to MI
            //2004/12/22 10:29:34 MAR Edited. SetLinkVals cause an error on line 37 of SetLinkVal: incorrect type.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            //PURPOSE:
            //		PJ 5/30/02 Adds Act Log with new notes of Opportunity.
            //		Run from enforce only in non-CREATION (MODIF) mode.
            //		If it is run in CREATION mode, the new Activity will not be linked to the original Opp.
            //RETURNS:
            //		1 if the Activity is created or didn't need to be created; 0 if not created or the user canceled.

            Form doForm = (Form)par_doCallingObject;
            string sNotes = "";
            string sWork = "";
            long lWork = 0;
            string sMessage = null;
            
            //'If answered Yes on MB to create a journal, then go directly to script that creates journal.
            //If UCase(par_s1) = "YES" Then
            //    GoTo CREATEACTLOG
            //End If


            if (Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL").ToString()) <= Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))
                return true;
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Opportunity is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Environment.NewLine);
                doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");
                //Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                return true;
            }

            doForm.oVar.SetVar("Opp_CreateActLog_Ran", "1");

            //sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            //CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            //hard returns in the journal field
            sWork = doForm.oVar.GetVar("JournalWithHardReturns").ToString();
            //CS 2/4/10
            if (string.IsNullOrEmpty(sWork))
                return true;
            //We didn't hit MessageBoxEvent from entering a journal note.

            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);

            //'sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            //'CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            //'hard returns in the journal field
            //sWork = doForm.oVar.GetVar("JournalWithHardReturns")

            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, Convert.ToInt32(lWork)).ToString();
            sNotes = sNotes + "== Created from Opportunity '" + doForm.doRS.GetFieldVal("SYS_Name") + "'";
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            doNew.SetFieldVal("MLS_Status", 1, 2);
            //Completed
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            //CS doNew.SetFieldVal("TME_ENDTIME", doForm.doRS.GetFieldVal("TME_Time"))
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            doNew.SetFieldVal("DTE_ENDTIME", "Today");

            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));


            //In the code below Paul added IsObjectAssigned tests because SetLinkVals weren't working in some cases.
            //==> Remove the IsObjectAssigned tests.
            DataTable oTable = new DataTable();
            oTable = null;  //SB 02222019 Pass it as null, otherwise it will throw error.
            doLink = doForm.doRS.GetLinkVal("LNK_ORIGINATEDBY_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (10).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If
            doNew.SetLinkVal("LNK_Related_CN", doLink);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_ORIGINATEDBY_CN) is not assigned (11).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If
            oTable = null;  //SB 02222019 Pass it as null, otherwise it will throw error.
            doLink = doForm.doRS.GetLinkVal("LNK_FOR_PD", ref doLink, true, 0, -1, "A_a", ref oTable);
            //PJ Changed link name from 'LNK_RELATED_PRODUCT' to 'LNK_FOR_PRODUCT'
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_Related_PD) is not assigned (12).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If
            doNew.SetLinkVal("LNK_Related_PD", doLink);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_Related_PD) is not assigned (13).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If
            //oTable = null;  //SB 02222019 Pass it as null, otherwise it will throw error.
            //doLink = doForm.doRS.GetLinkVal("LNK_Related_GR", ref doLink, true, 0, -1, "A_a", ref oTable);
            ////If Not goP.IsObjectAssigned(doLink) Then
            ////    goP.TraceLine("doLink (LNK_Related_GR) is not assigned.", "", sProc)
            ////    '	goErr.DisplayLastError()
            ////End If
            //doNew.SetLinkVal("LNK_Related_GR", doLink);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_Related_GROUP) is not assigned (2).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If
            oTable = null;  //SB 02222019 Pass it as null, otherwise it will throw error.
            doLink = doForm.doRS.GetLinkVal("LNK_FOR_CO", ref doLink, true, 0, -1, "A_a", ref oTable);
            //If Not goP.IsObjectAssigned(doLink) Then
            //    goP.TraceLine("doLink (LNK_FOR_CO) is not assigned (3).", "", sProc)
            //    '	goErr.DisplayLastError()
            //End If
            doNew.SetLinkVal("LNK_RELATED_CO", doLink);
            //if goErr.GetLastError()<>"E00000" then
            //	goErr.DisplayLastError()
            //End If

            doNew.SetFieldVal("MLS_TYPE", 31, 2);
            //Journal
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine(doNew.GetFieldVal("MMO_HISTORY").ToString(), "Created."));
            doNew.SetFieldVal("LNK_RELATED_OP", doForm.GetRecordID());

            oTable = null;  //VT 12/17/2019 TKT# 3252
            doLink = (clArray)doForm.doRS.GetLinkVal("LNK_Related_VE", ref doLink, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_VE", doLink);

            if (doNew.Commit() != 1)
            {
                doNew = null;
                doLink = null;
                string sError = goErr.GetLastError();
                sMessage = goErr.GetLastError("MESSAGE");

                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Environment.NewLine);

                ////goLog.Log("MessageBoxEvent", doForm.oVar.GetVar("ScriptMessages").ToString(),1 ,false , true);
                return false;
            }
            par_bRunNext = false;
            doNew = null;
            doLink = null;

            return true;

        }

        public bool USERREASSIGN_FormControlOnChange_BTN_START_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sFromUser = null;
            string sToUser = null;
            string sLinesToWrite = "";
            DateTime dtStarted = goTR.NowUTC();
            string sMessage = null;
            DateTime dtLocalStart = default(DateTime);
            par_bRunNext = false;

            //Make sure there is not an existing OTH_USERREASSIGNMENT page of MD for this user. If there is, refuse to proceed.
            //One job must finish before another can begin.
            if (!string.IsNullOrEmpty(goMeta.PageRead("GLOBAL", "OTH_USERREASSIGNMENT")))
            {
                //Warn user that has an outstanding user reassign job.
                //Convert Date Started to local time
                //int par_valid = 0;
                int par_iValid = 0;
                dtLocalStart = goTR.StringToDateTime(goMeta.LineRead("GLOBAL", "OTH_USERREASSIGNMENT", "CREATED_DATETIME"), "", "", ref par_iValid, "|", false);
                dtLocalStart = goTR.UTC_UTCToLocal(dtLocalStart);
                sMessage = "It appears that " + goMeta.LineRead("GLOBAL", "OTH_USERREASSIGNMENT", "CREATED_BY") + " is currently resassigning Users:" + Environment.NewLine + "Started: " + goTR.Replace(Convert.ToDateTime(dtLocalStart).ToString(), "|", " ") + Environment.NewLine + "From User: " + goMeta.LineRead("GLOBAL", "OTH_USERREASSIGNMENT", "FROM_USER_NAME") + Environment.NewLine + "To User: " + goMeta.LineRead("GLOBAL", "OTH_USERREASSIGNMENT", "TO_USER_NAME") + Environment.NewLine + " If this is not the case, please contact your Selltis administrator.";

                goErr.SetWarning(30029, sProc, sMessage);
                return false;
            }

            //Is there a From and To User selected?
            if (doForm.GetControlLinkCount("NDB_LNK_FROMUSER_US") == 0)
            {
                goErr.SetWarning(30029, sProc, "", "From User", "", "", "", "", "", "", "", "", "");
                return false;
            }

            if (doForm.GetControlLinkCount("NDB_LNK_TOUSER_US") == 0)
            {
                goErr.SetWarning(30029, sProc, "", "To User", "", "", "", "", "", "", "", "", "");
                return false;
            }

            sFromUser = doForm.GetControlVal("NDB_LNK_FROMUSER_US");
            //More than one value returned
            if (Strings.InStr(sFromUser, Environment.NewLine) != 0)
            {
                string[] aFromUserLink = Strings.Split(sFromUser, Environment.NewLine);
                sFromUser = aFromUserLink[0].ToString();
            }
            goTR.StrWrite(ref sLinesToWrite, "From_User", sFromUser);
            goTR.StrWrite(ref sLinesToWrite, "From_User_Name", doForm.GetControlVal("NDB_LNK_FROMUSER_US", "SYS_NAME"));
            //sLinesToWrite = "From_User=" & sFromUser & vbCrLf
            //sLinesToWrite = sLinesToWrite & "From_User_Name=" & doForm.GetControlVal("NDB_LNK_FROMUSER_US", "SYS_NAME") & vbCrLf

            sToUser = doForm.GetControlVal("NDB_LNK_TOUSER_US");
            //More than one value returned
            if (Strings.InStr(sToUser, Environment.NewLine) != 0)
            {
                string[] aToUserLink = Strings.Split(sToUser, Environment.NewLine);
                sToUser = aToUserLink[0];
            }
            goTR.StrWrite(ref sLinesToWrite, "To_User", sToUser);
            goTR.StrWrite(ref sLinesToWrite, "To_User_Name", doForm.GetControlVal("NDB_LNK_TOUSER_US", "SYS_NAME"));
            //sLinesToWrite = sLinesToWrite & "To_User=" & sToUser & vbCrLf
            //sLinesToWrite = sLinesToWrite & "To_User_Name=" & doForm.GetControlVal("NDB_LNK_TOUSER_US", "SYS_NAME") & vbCrLf

            //Check which files we need to reassign
            if (doForm.GetControlVal("NDB_CHK_CN") == "CHECKED")
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=YES" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Files_Contact", "YES");
            }
            else
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=NO" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Files_Contact", "NO");
            }
            if (doForm.GetControlVal("NDB_CHK_CO") == "CHECKED")
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=YES" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Files_Company", "YES");
            }
            else
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=NO" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Files_Company", "NO");
            }
            if (doForm.GetControlVal("NDB_CHK_AC") == "CHECKED")
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=YES" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Files_Activity", "YES");
            }
            else
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=NO" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Files_Activity", "NO");
            }
            if (doForm.GetControlVal("NDB_CHK_TD") == "CHECKED")
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=YES" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Files_ToDo", "YES");
            }
            else
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=NO" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Files_ToDo", "NO");
            }
            if (doForm.GetControlVal("NDB_CHK_OP") == "CHECKED")
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=YES" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Files_Opp", "YES");
            }
            else
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=NO" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Files_Opp", "NO");
            }
            if (doForm.GetControlVal("NDB_CHK_QT") == "CHECKED")
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=YES" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Files_Quote", "YES");
            }
            else
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=NO" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Files_Quote", "NO");
            }
            //CS 10/11/11
            if (doForm.GetControlVal("NDB_CHK_PR") == "CHECKED")
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=YES" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Files_Project", "YES");
            }
            else
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=NO" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Files_Project", "NO");
            }


            //Check if we need to get local records also
            if (doForm.GetControlVal("NDB_CHK_LOCAL") == "CHECKED")
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=YES" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Option_Include_Local", "YES");
            }
            else
            {
                //sLinesToWrite = sLinesToWrite & "Option_Include_Local=NO" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Option_Include_Local", "NO");
            }

            //Check if we need to deactive/activate User recs
            if (doForm.GetControlVal("NDB_CHK_RECORD") == "CHECKED")
            {
                //sLinesToWrite = sLinesToWrite & "Option_ACTIVATE_USERS=YES" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Option_Activate_Users", "YES");
            }
            else
            {
                //sLinesToWrite = sLinesToWrite & "Option_ACTIVATE_USERS=NO" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Option_Activate_Users", "NO");
            }

            if (doForm.GetControlVal("NDB_CHK_ONNETWORK") == "CHECKED")
            {
                //sLinesToWrite = sLinesToWrite & "Option_ON_Network=YES" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Option_On_Network", "YES");
            }
            else
            {
                //sLinesToWrite = sLinesToWrite & "Option_On_Network=NO" & vbCrLf
                goTR.StrWrite(ref sLinesToWrite, "Option_On_Network", "NO");
            }

            //Record user who created the job
            //sLinesToWrite = sLinesToWrite & "Created_By=" & goP.GetUserName & vbCrLf
            //sLinesToWrite = sLinesToWrite & "Created_By_ID=" & goP.GetUserTID() & vbCrLf
            goTR.StrWrite(ref sLinesToWrite, "Created_By", goP.GetUserName());
            goTR.StrWrite(ref sLinesToWrite, "Created_By_ID", goP.GetUserTID());

            //Record date/time clicked Start button
            //sLinesToWrite = sLinesToWrite & "Created_DateTime=" & goTR.DateTimeToString(dtStarted) & vbCrLf
            goTR.StrWrite(ref sLinesToWrite, "Created_DateTime", dtStarted.ToString());

            //Create OTH MD page for this user's section
            goMeta.PageWrite("GLOBAL", "OTH_USERREASSIGNMENT", sLinesToWrite);

            doForm.MessageBox("User reassignment is now in progress. You will receive an Alert in the Alerts panel when it completes.", clC.SELL_MB_OK, "Selltis", "Close", "", "", "", "MessageBoxEvent", "", "", doForm, null, "Close", "", "", "", System.Reflection.MethodInfo.GetCurrentMethod().Name);

            //doForm.CloseOnReturn = True 'close this form



            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string product = Convert.ToString(doForm.doRS.GetFieldVal("LNK_For_PD%%TXT_PRODUCTNAME"));
            string Company = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_CO%%GID_ID"));
            string Credited_us = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%GID_ID"));




            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_RELATED_PD,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX,LNK_FOR_CO,LNK_CREATEDBY_US,TXT_PRODUCTNAME", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            //rsOL.SetFieldVal("LNK_FOR_MO", MO_Gid);
            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);
            rsOL.SetFieldVal("TXT_PRODUCTNAME", product);
            rsOL.SetFieldVal("LNK_FOR_CO", Company);
            rsOL.SetFieldVal("LNK_CREATEDBY_US", Credited_us);

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));
            string product = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_PD%%TXT_PRODUCTNAME"));

            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);
            doRS.SetFieldVal("TXT_PRODUCTNAME", product);

            //mobile Ol line number
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    iLineCount = doOPLines.Count();
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }
            par_doCallingObject = doRS;

            return true;
        }
        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
            }

        }
        private static void Refresh_OPPTotal(clRowSet doForm)
        {
            string sGidId = Convert.ToString(doForm.GetFieldVal("Gid_id"));

            clRowSet rsOL = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + sGidId + "' ", "LNK_IN_OP", "CUR_VALUE|SUM");

            double curTotalAmt = 0.0;

            if ((rsOL.GetFirst() == 1))
            {

                curTotalAmt = Convert.ToDouble(rsOL.GetFieldVal("CUR_VALUE|SUM", 2));


                doForm.SetFieldVal("CUR_VALUE", curTotalAmt, 2);
                //doForm.SetFieldVal("CUR_TOTAL", curTotalAmt, 2);

            }
            else
            {
                doForm.SetFieldVal("CUR_VALUE", 0.0);
                //doForm.SetFieldVal("CUR_TOTAL", 0.0);

            }


        }
        public bool OP_FormControlOnChange_BTN_CALCPROBABILITY_1_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            Refresh_OPPTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            par_doCallingObject = doForm;
            return true;

        }
        public bool FD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sTitle = "";
            string sUserGid = "";

            sTitle = Convert.ToString(doRS.GetFieldVal("TXT_TITLE"));
            sUserGid = Convert.ToString(doRS.GetFieldVal("LNK_TO_US"));

            goUI.AddAlert(sTitle + "  file is ready to download", "OPENDESKTOP", "DSK_D7ECEB16-ABEC-4CC3-5858-B1070079852D", sUserGid, "").ToString();

            par_doCallingObject = doRS;
            return true;
        }
        public bool OL_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sColor = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_RELATED_PD", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("CUR_UNITPRICE", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("SR__QTY", "LABELCOLOR", sColor);
            doForm.SetControlState("LNK_FOR_CO", 4);
            doForm.SetControlState("LNK_CREATEDBY_US", 4);

            par_doCallingObject = doForm;

            return true;
        }
        public bool OL_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            Form doForm = (Form)par_doCallingObject;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_UNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_UNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }
            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }
            if (doForm.doRS.IsLinkEmpty("LNK_RELATED_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Product");
                doForm.FieldInFocus = "LNK_RELATED_PD";
                par_doCallingObject = doForm;
                return false;
            }
            par_doCallingObject = doForm;
            return true;
        }
    }
}
