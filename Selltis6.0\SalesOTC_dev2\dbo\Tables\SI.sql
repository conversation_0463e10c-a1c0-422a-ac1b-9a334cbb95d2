﻿CREATE TABLE [dbo].[SI] (
    [GID_ID]                      UNIQUEIDENTIFIER CONSTRAINT [DF_SI_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'SI',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                      BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                    NVARCHAR (80)    NULL,
    [DTT_CreationTime]            DATETIME         CONSTRAINT [DF_SI_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]                TINYINT          NULL,
    [TXT_ModBy]                   VARCHAR (4)      NULL,
    [DTT_ModTime]                 DATETIME         CONSTRAINT [DF_SI_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_StrategicInitiativeName] NVARCHAR (50)    NULL,
    [MMO_ImportData]              NTEXT            NULL,
    [SI__ShareState]              TINYINT          CONSTRAINT [DF_SI_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]            UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]                VARCHAR (50)     NULL,
    [TXT_ExternalID]              NVARCHAR (80)    NULL,
    [TXT_ExternalSource]          VARCHAR (10)     NULL,
    [TXT_ImpJobID]                VARCHAR (20)     NULL,
    [GID_CREATEDTO_US]            UNIQUEIDENTIFIER NULL,
    [TXT_Description]             NVARCHAR (1000)  NULL,
    CONSTRAINT [PK_SI] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_SI_StrategicInitiativeName]
    ON [dbo].[SI]([TXT_StrategicInitiativeName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SI_CreatedBy_US]
    ON [dbo].[SI]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SI_ModDateTime]
    ON [dbo].[SI]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SI_Name]
    ON [dbo].[SI]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SI_CreationTime]
    ON [dbo].[SI]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_SI_BI__ID]
    ON [dbo].[SI]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SI_TXT_ImportID]
    ON [dbo].[SI]([TXT_ImportID] ASC);

