﻿CREATE TABLE [dbo].[US] (
    [GID_ID]                     UNIQUEIDENTIFIER CONSTRAINT [DF_US_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'US',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                     BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                   NVARCHAR (80)    NULL,
    [CHK_ActiveField]            TINYINT          NULL,
    [TXT_AddrBusiness]           NVARCHAR (300)   NULL,
    [FIL_Attachments]            NTEXT            NULL,
    [TEL_BusPhone]               VARCHAR (39)     NULL,
    [TXT_BusPhoneExt]            VARCHAR (6)      NULL,
    [TEL_CellPhone]              VARCHAR (39)     NULL,
    [TXT_CityBusiness]           NVARCHAR (25)    NULL,
    [TXT_Code]                   VARCHAR (4)      NULL,
    [TXT_CountryBusiness]        NVARCHAR (20)    NULL,
    [DTT_CreationTime]           DATETIME         CONSTRAINT [DF_US_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]               TINYINT          NULL,
    [EML_Email]                  TEXT             NULL,
    [TXT_EmplRefNo]              VARCHAR (10)     NULL,
    [TEL_Fax]                    VARCHAR (39)     NULL,
    [TXT_FaxExt]                 VARCHAR (6)      NULL,
    [TEL_HomePhone]              VARCHAR (39)     NULL,
    [TXT_NameFirst]              NVARCHAR (15)    NULL,
    [TXT_NameLast]               NVARCHAR (1000)  NULL,
    [TXT_Nickname]               NVARCHAR (20)    NULL,
    [MMO_Note]                   NTEXT            NULL,
    [CHK_OnNetwork]              TINYINT          NULL,
    [TXT_OrigCreatedBy]          VARCHAR (4)      NULL,
    [DTT_OrigCreatedTime]        DATETIME         NULL,
    [TEL_Pager]                  VARCHAR (39)     NULL,
    [CHK_Partner]                TINYINT          NULL,
    [FIL_Picture]                NTEXT            NULL,
    [TXT_StateBusiness]          NVARCHAR (2)     NULL,
    [MLS_TimeZone]               SMALLINT         NULL,
    [TXT_TitleText]              NVARCHAR (50)    NULL,
    [MLS_Type]                   SMALLINT         NULL,
    [URL_URLs]                   NTEXT            NULL,
    [TXT_ZipBusiness]            VARCHAR (10)     NULL,
    [TXT_ModBy]                  VARCHAR (4)      NULL,
    [DTT_ModTime]                DATETIME         CONSTRAINT [DF_US_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [MMO_ImportData]             NTEXT            NULL,
    [SI__ShareState]             TINYINT          CONSTRAINT [DF_US_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]           UNIQUEIDENTIFIER NULL,
    [GID_Related_JF]             UNIQUEIDENTIFIER NULL,
    [GID_At_LO]                  UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]               VARCHAR (50)     NULL,
    [TXT_ExternalID]             NVARCHAR (120)   NULL,
    [TXT_ExternalSource]         VARCHAR (10)     NULL,
    [TXT_ImpJobID]               VARCHAR (20)     NULL,
    [ADR_Attachments]            NTEXT            NULL,
    [ADR_Picture]                NTEXT            NULL,
    [TXT_FCMToken]               NVARCHAR (MAX)   NULL,
    [TXT_FCMTokenDevice]         VARCHAR (50)     NULL,
    [TXT_Auth_Code]              NVARCHAR (10)    NULL,
    [DTT_Auth_Verified_Date]     DATETIME         NULL,
    [TXT_SFUserID]               NVARCHAR (50)    NULL,
    [MLS_Region]                 SMALLINT         NULL,
    [MLS_LOB]                    SMALLINT         NULL,
    [MLS_COUNTRIES]              SMALLINT         NULL,
    [MLS_ENTITY]                 SMALLINT         NULL,
    [Txt_FullName]               AS               (Trim((isnull([TXT_NameFirst],'')+' ')+isnull([TXT_NameLast],''))),
    [CHK_WLGeneral]              TINYINT          NULL,
    [CHK_WLTechnical]            TINYINT          NULL,
    [CHK_WLQuote]                TINYINT          NULL,
    [GID_RELATED_BC]             UNIQUEIDENTIFIER NULL,
    [GID_RELATED_BR]             UNIQUEIDENTIFIER NULL,
    [GID_PRIMARY_BC]             UNIQUEIDENTIFIER NULL,
    [GID_RELATED_BU]             UNIQUEIDENTIFIER NULL,
    [TXT_SXNO]                   NVARCHAR (250)   NULL,
    [GID_JCIID]                  UNIQUEIDENTIFIER NULL,
    [GID_EPUMPID]                UNIQUEIDENTIFIER NULL,
    [EML_EmailOld]               TEXT             NULL,
    [CHK_SIGNATURE]              TINYINT          NULL,
    [MMO_SIGNATURE]              NTEXT            NULL,
    [CUR_YTDSales]               MONEY            NULL,
    [CUR_LastYTDSales]           MONEY            NULL,
    [CUR_LastYearSales]          MONEY            NULL,
    [SR__PercentToPYTD]          DECIMAL (18)     NULL,
    [INT_SalesVisitsLast2wks]    INT              NULL,
    [INT_SalesVisitsLast30days]  INT              NULL,
    [INT_SalesVisitsLast90days]  INT              NULL,
    [INT_SalesVisitsYTD]         INT              NULL,
    [DTT_LastSalesVisitDateTime] AS               ([dbo].[UserLastSalesVisitDate]([GID_ID])),
    [GID_SUPERVISOR_US]          UNIQUEIDENTIFIER NULL,
    [CHK_PROXY]                  TINYINT          NULL,
    [GID_LEADERUSER_US]          UNIQUEIDENTIFIER NULL,
    [GID_RELATED_RG]             UNIQUEIDENTIFIER NULL,
    [CHK_SSOADMIN]               TINYINT          NULL,
    [TXT_SupervisorName]         NVARCHAR (1000)  NULL,
    CONSTRAINT [PK_US] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_US_At_LO] FOREIGN KEY ([GID_At_LO]) REFERENCES [dbo].[LO] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_Related_JF] FOREIGN KEY ([GID_Related_JF]) REFERENCES [dbo].[JF] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[US] NOCHECK CONSTRAINT [LNK_US_At_LO];


GO
ALTER TABLE [dbo].[US] NOCHECK CONSTRAINT [LNK_US_CreatedBy_US];


GO
ALTER TABLE [dbo].[US] NOCHECK CONSTRAINT [LNK_US_Related_JF];


GO
CREATE NONCLUSTERED INDEX [IX_US_Name]
    ON [dbo].[US]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_CreationTime]
    ON [dbo].[US]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_At_LO]
    ON [dbo].[US]([GID_At_LO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_Related_JF]
    ON [dbo].[US]([GID_Related_JF] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_US_BI__ID]
    ON [dbo].[US]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_ModDateTime]
    ON [dbo].[US]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_CreatedBy_US]
    ON [dbo].[US]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_TXT_ImportID]
    ON [dbo].[US]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_NameLastFirst]
    ON [dbo].[US]([TXT_NameLast] ASC, [TXT_NameFirst] ASC);


GO
CREATE TRIGGER trUSUpdateTN
ON [US]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in US table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(120)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'US'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable
					and tn2.GID_InternalID = inserted.GID_ID)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [US]
			SET [US].TXT_ExternalSource = '', [US].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [US].GID_ID = in1.GID_ID
				and ISNULL([US].TXT_ExternalSource, '') <> ''
				and ISNULL([US].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trUSUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trUSUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trUSUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!