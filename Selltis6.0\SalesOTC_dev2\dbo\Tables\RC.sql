﻿CREATE TABLE [dbo].[RC] (
    [GID_ID]                  UNIQUEIDENTIFIER CONSTRAINT [DF_RC_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'RC',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                  BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                NVARCHAR (80)    NULL,
    [DTT_CreationTime]        DATETIME         CONSTRAINT [DF_RC_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]            TINYINT          NULL,
    [TXT_ModBy]               VARCHAR (4)      NULL,
    [DTT_ModTime]             DATETIME         CONSTRAINT [DF_RC_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_QuoteWonLostResName] NVARCHAR (50)    NULL,
    [MMO_ImportData]          NTEXT            NULL,
    [SI__ShareState]          TINYINT          CONSTRAINT [DF_RC_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]        UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]            VARCHAR (50)     NULL,
    [TXT_ExternalID]          NVARCHAR (80)    NULL,
    [TXT_ExternalSource]      VARCHAR (10)     NULL,
    [TXT_ImpJobID]            VARCHAR (20)     NULL,
    [TXT_Description]         NVARCHAR (1000)  NULL,
    [TXT_ReasonCode]          NVARCHAR (500)   NULL,
    [MLS_TYPE]                SMALLINT         NULL,
    CONSTRAINT [PK_RC] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_RC_QuoteWonLostResName]
    ON [dbo].[RC]([TXT_QuoteWonLostResName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RC_CreatedBy_US]
    ON [dbo].[RC]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RC_ModDateTime]
    ON [dbo].[RC]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RC_Name]
    ON [dbo].[RC]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RC_CreationTime]
    ON [dbo].[RC]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_RC_BI__ID]
    ON [dbo].[RC]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RC_TXT_ImportID]
    ON [dbo].[RC]([TXT_ImportID] ASC);

