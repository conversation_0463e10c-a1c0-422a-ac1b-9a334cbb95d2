﻿Imports Microsoft.VisualBasic
Imports System.Collections.Generic
Imports System.Web
Imports System.Text.RegularExpressions
Imports System.Configuration

Public Class clEmailAlerts

    Dim goMeta = HttpContext.Current.Session("goMeta")
    Dim goP = HttpContext.Current.Session("goP")
    Dim goTR = HttpContext.Current.Session("goTR")
    Dim goDef = HttpContext.Current.Session("goDef")
    Dim goData = HttpContext.Current.Session("goData")

    Public Sub SendEmailAlerts(doRS As clRowSet, sMode As String)
        Try

            Dim sPerOptions As String = ""
            Dim sPer_EmailAlertsEnabled As String = ""
            Dim sPer_EmailAlertsFiles As String = ""
            Dim sTemplateName As String = ""
            Dim sLocalTemplateFileName As String = ""
            Dim sMessageBody As String = ""
            Dim sCurrFile As String
            Dim sWGOption As String = ""
            Dim sWG_EMAILALERTS_ENABLE As String = ""
            Dim sReply_To As String = ""

            'Read the WG Options
            sWGOption = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", True)
            sWG_EMAILALERTS_ENABLE = goTR.StrRead(sWGOption, "EMAILALERTS_ENABLE", "No")
            sReply_To = goTR.StrRead(sWGOption, "EMAILALERTS_REPLY_TO", "")

            'Return if email alerts disabled in WG options
            If sWG_EMAILALERTS_ENABLE = "No" Then
                Exit Sub
            End If

            'Read the email alerts settings from personal options   
            'sPerOptions = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", goDef.GetPersonalOptionDefs(), False)
            'sPer_EmailAlertsEnabled = goTR.StrRead(sPerOptions, "EMAILALERTS_ENABLE", sWG_EMAILALERTS_ENABLE, False)
            'sPer_EmailAlertsFiles = goTR.StrRead(sPerOptions, "EMAILALERTS_FILES ", "", False)

            ''Return if email alerts disabled in personal options
            'If sPer_EmailAlertsEnabled = "No" Then
            '    Exit Sub
            'End If

            'get the current file name
            sCurrFile = doRS.GetFileName()

            'get the template file name form work group options if email alerts are enabled for the both user and the current file
            'if no template file is available for the current file then gets the default template file


            sTemplateName = GetTemplateFileName(sWGOption, sCurrFile)

            'process the template file and prepare the message body
            If String.IsNullOrEmpty(sTemplateName) = False Then
                sMessageBody = ProcessTemplateFile(sTemplateName, doRS, sMode)
            End If

            'call the ClEmail.SendSMTPEmail() function to send the email
            If String.IsNullOrEmpty(sMessageBody) = False Then
                SendEmail(sMessageBody, sReply_To, sCurrFile)
            End If



        Catch ex As Exception

        End Try
    End Sub

    Private Sub SendEmail(sMessageBody As String, sReply_To As String, sCurrFile As String)

        Dim sSubject As String = ""
        Dim sEmailIds As String = ""
        Dim sFrom As String = ""

        sEmailIds = GetBetweenString(sMessageBody, "<span id=""sEmailids"">", "</span>")
        sSubject = GetBetweenString(sMessageBody, "<span id=""sSubject"">", "</span>")
        sFrom = GetBetweenString(sMessageBody, "<span id=""sFrom"">", "</span>")

        sEmailIds = ValidateEmailIds(sEmailIds, sCurrFile)

        If Not String.IsNullOrEmpty(sEmailIds) Then
            Dim _clEmail As New clEmail()
            'Dim bSendStatus As Boolean = _clEmail.SendSMTPEmail(sSubject, sMessageBody, sEmailIds, "", "", "", sReply_To, "", "", True, "", "", True, True)
            Dim bSendStatus As Boolean = _clEmail.SendSMTPEmailNew(sSubject, sMessageBody, sEmailIds, "", "", "", sReply_To, "", "", True, "", "", True, True)
        End If

    End Sub

    Private Function ValidateEmailIds(_sEmailIds As String, sCurrFile As String) As String

        _sEmailIds = _sEmailIds.Replace("<BR>", ";")
        Dim sEmails() As String = _sEmailIds.Split(";")
        Dim sRetVal As String = ""

        For Each _emailId As String In sEmails

            If isEmail(_emailId) AndAlso sRetVal.Contains(_emailId) = False Then

                'verify the email alerts are enabled for the user or not
                Dim rsUS As clRowSet = New clRowSet("US", clC.SELL_READONLY, "EML_EMAIL='" & _emailId & "'", "", "GID_ID", -1, "", "", "", "", "", True, True)
                rsUS.ToTable()

                Dim dt As DataTable = rsUS.dtTransTable

                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then

                    Dim sUserId As String = dt.Rows(0)("GID_ID").ToString()

                    If goTR.IsEmailAlertsEnabled(sUserId, sCurrFile) Then
                        sRetVal = sRetVal & ";" & _emailId
                    End If

                End If

                rsUS = Nothing

            End If

        Next

        If sRetVal.Length > 1 Then
            sRetVal = sRetVal.Remove(0, 1)
        End If

        Return sRetVal

    End Function

    Private Function isEmail(inputEmail As String) As Boolean
        Dim strRegex As String = "^([a-zA-Z0-9_\-\.]+)@((\[[0-9]{1,3}" + "\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\" + ".)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$"
        Dim re As New Regex(strRegex)
        If re.IsMatch(inputEmail) Then
            Return (True)
        Else
            Return (False)
        End If
    End Function

    Private Function GetBetweenString(src As String, findfrom As String, findto As String) As String
        Dim start As Integer = src.IndexOf(findfrom)
        Dim [to] As Integer = src.IndexOf(findto, start + findfrom.Length)
        If start < 0 OrElse [to] < 0 Then
            Return ""
        End If
        Dim s As String = src.Substring(start + findfrom.Length, [to] - start - findfrom.Length)
        Return s
    End Function

    Private Function GetTemplateFileName(sWGOption As String, sCurrFile As String) As String

        Dim sTemplateName As String
        '  Dim sWGOption As String = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", True)
        Dim sEMAILALERTS_FILES As String = goTR.StrRead(sWGOption, "EMAILALERTS_FILES", "")
        Dim sEMAILALERTS_TEMPLATES As String = goTR.StrRead(sWGOption, "EMAILALERTS_TEMPLATES", "")

        Dim HtFiles As New Hashtable()
        Dim sTemplates = sEMAILALERTS_TEMPLATES.Split(",")
        For Each sfile As String In sTemplates
            Dim stemps() As String = sfile.Split("~")
            HtFiles.Add(stemps.GetValue(0).ToString(), stemps.GetValue(1).ToString())
        Next

        If HtFiles.Contains(sCurrFile) Then
            sTemplateName = HtFiles(sCurrFile).ToString()
            If (sTemplateName = "All (default)") Then 'get the default template 
                sTemplateName = HtFiles("All").ToString()
            End If
        Else
            'get the default template 
            sTemplateName = HtFiles("All").ToString()
        End If

        Return sTemplateName

    End Function

    Private Function GetTemplatesPath() As String
        Dim sCusFilesPath As String = ConfigurationManager.AppSettings("CustomFilesPath").ToString()
        Dim sHostName As String = HttpContext.Current.Request.Url.Host.ToString().ToLower()

        'To differentiate local and azure..J
        Dim substring As String = sCusFilesPath.Substring(1, 2)
        If substring = ":\" Then
            sCusFilesPath = System.Configuration.ConfigurationManager.AppSettings("CustomFilesPath").ToString()
        Else
            sCusFilesPath = HttpContext.Current.Server.MapPath(System.Configuration.ConfigurationManager.AppSettings("CustomFilesPath").ToString())
        End If

        Dim sHostingEnvironment As String = ConfigurationManager.AppSettings("HostingEnvironment").ToString().ToLower()
        If sHostingEnvironment = "debugging" Then
            sHostName = "default"
        ElseIf sHostingEnvironment = "staging" Then
            sHostName = sHostName & "_" & HttpContext.Current.Request.Url.Port.ToString()
        End If
        Return sCusFilesPath & sHostName & "\\Templates\\"
    End Function

    Private Function ProcessTemplateFile(sTemplateFileName As String, doRS As clRowSet, sMode As String) As String

        Try

            Dim intStartPos
            Dim intEndPos
            Dim strCode
            Dim strValue
            Dim strField
            Dim sTemplate As String

            'sTemplate = System.IO.File.ReadAllText(goP.sPath & "\Templates\EmailAlertsTemplates\" & sTemplateFileName)
            sTemplate = System.IO.File.ReadAllText(GetTemplatesPath() & "\EmailAlertsTemplates\" & sTemplateFileName)

            Do Until InStr(sTemplate, "(%") = 0

                intStartPos = InStr(sTemplate, "(%")
                intEndPos = InStr(intStartPos, sTemplate, "%)")

                If intStartPos = 0 Then Exit Function
                If intEndPos = 0 Then Exit Function

                If intStartPos <> 0 And intEndPos <> 0 Then

                    strCode = Mid(sTemplate, intStartPos, intEndPos - intStartPos + 2)

                    Dim strTempCode As String = Mid(strCode, 3, (Len(strCode) - 4))

                    If strTempCode = "USER_NAME" Then
                        strValue = goP.GetMe("NAME")
                    ElseIf strTempCode = "RECORD_ACTION" Then
                        If doRS.iRSType = clC.SELL_ADD Then
                            strValue = "Created"
                        ElseIf doRS.iRSType = clC.SELL_EDIT Then
                            strValue = "Updated"
                        Else
                            strValue = ""
                        End If
                    ElseIf strTempCode = "FILE_NAME" Then
                        strValue = goData.GetFileLabel(doRS.GetFileName)
                    ElseIf strTempCode = "SITE_URL" Then
                        strValue = GetBaseURL()
                    ElseIf strTempCode = "DATA_MODIFIED" Then
                        strValue = GetModifiedFields(doRS)
                    Else

                        '*******
                        Dim aVal As String() = Split(strTempCode, "%%")

                        If aVal.Length > 2 Then

                            'LNK_RELATED_CO%%LNK_INVOLVES_US%%EML_EMAIL

                            Dim sID As String = aVal(0) + "%%GID_ID"
                            strValue = doRS.GetFieldVal(sID)

                            Dim aGid As String() = Split(strValue, vbCrLf)
                            Dim sField As String = aVal(1) + "%%" + aVal(2)

                            strValue = ""

                            For Each s As String In aGid

                                Dim sFile As String = goTR.GetFileFromSUID(s).ToString

                                If Not goData.IsFieldValid(sFile, sField) Then
                                    Continue For
                                End If

                                Dim oRS As New clRowSet(sFile, clC.SELL_READONLY, "GID_ID=" & s, "GID_ID", sField, 1)

                                If oRS.GetFirst Then

                                    If strValue = "" Then
                                        strValue = oRS.GetFieldVal(sField, clC.SELL_FRIENDLY)
                                    Else
                                        strValue = strValue + vbCrLf + oRS.GetFieldVal(sField, clC.SELL_FRIENDLY)
                                    End If
                                End If
                                oRS = Nothing

                            Next

                        Else
                            If goData.IsFieldValid(doRS.GetFileName(), strTempCode) Then
                                strValue = doRS.GetFieldVal(strTempCode)
                            Else
                                strValue = "***Invalid Code***"
                            End If
                        End If

                        '********

                        'If goData.IsFieldValid(doRS.GetFileName(), strTempCode) Then
                        '    strValue = doRS.GetFieldVal(strTempCode)
                        'Else
                        '    strValue = "***Invalid Code***"
                        'End If
                    End If

                    sTemplate = Replace(sTemplate, strCode, ReplaceHRTs(strValue))

                End If

            Loop

            Return sTemplate

        Catch ex As Exception

            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    Return ex.ToString()
            'End If
            Return ""
        End Try


    End Function

    Function GetBaseURL() As String
        Dim baseUrl As String = HttpContext.Current.Request.Url.Scheme + "://" + HttpContext.Current.Request.Url.Authority + HttpContext.Current.Request.ApplicationPath.TrimEnd("/"c) + "/"
        Return baseUrl
    End Function

    Function ReplaceHRTs(ByVal RawText As String, Optional bAlways As Boolean = False)
        'V_T 4/23/2015
        'If bAlways = False Then
        '    If INI.TemplType = "Text" Then
        '        ReplaceHRTs = RawText
        '        Exit Function
        '    End If
        'End If

        'HTMLText = ""
        'Replace Hard Returns
        Dim j, k, l, TabChar
        j = 1
        While j <> 0
            j = InStr(j, RawText, vbCrLf)
            If j <> 0 Then
                RawText = Mid(RawText, 1, j - 1) & "<BR>" & Mid(RawText, j + 2)
            End If
        End While
        j = 1
        While j <> 0
            j = InStr(j, RawText, vbLf)
            If j <> 0 Then
                RawText = Mid(RawText, 1, j - 1) & "<BR>" & Mid(RawText, j + 1)
            End If
        End While

        'Replace tabs with multiple non-breaking spaces
        TabChar = vbTab
        l = 1
        While l <> 0
            l = InStr(l, RawText, TabChar) 'vbTab
            If l <> 0 Then
                RawText = Mid(RawText, 1, l - 1) & "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" & Mid(RawText, l + 1)
            End If
        End While
        'Replace multi spaces with non-breaking spaces
        k = 1
        While k <> 0
            k = InStr(k, RawText, "  ")
            If k <> 0 Then
                RawText = Mid(RawText, 1, k - 1) & "&nbsp;&nbsp;" & Mid(RawText, k + 2)
            End If
        End While

        'HTMLText = RawText
        ReplaceHRTs = RawText
        RawText = ""
        'DEBUG
        'MsgBox "HTMLText is " & HTMLText
        'Now capture the HTMLText variable in the procedure from which you ran this function
    End Function

    Function GetModifiedFields(doRS As clRowSet) As String

        ' Try
        If doRS.gsDirtyFields IsNot Nothing AndAlso doRS.gsDirtyFields.Count > 0 Then

                Dim sModifiedFields As String = ""

                Dim pair As KeyValuePair(Of String, Tuple(Of Object, Object))

                For Each pair In doRS.gsDirtyFields

                    Dim sField As String = pair.Key

                    If sField = "MMO_HISTORY" Then
                        Continue For
                    End If

                    Dim sOriginalValue As Object = pair.Value.Item1
                    Dim sNewValue As Object = pair.Value.Item2

                    If sOriginalValue Is Nothing Or sNewValue Is Nothing Then
                        Continue For
                    End If

                    If sField.ToLower().Contains("cur_") Or sField.ToLower().Contains("chk_") Then
                        If sOriginalValue.ToString() = "" Then
                            sOriginalValue = "0"
                        End If
                        If sNewValue.ToString() = "" Then
                            sNewValue = "0"
                        End If
                        If sField.ToLower().Contains("chk_") Then
                            sOriginalValue = IIf(sOriginalValue = 0, "Unchecked", "Checked")
                            sNewValue = IIf(sNewValue = 0, "Unchecked", "Checked")
                        ElseIf sField.ToLower().Contains("cur_") Then
                            sOriginalValue = goTR.FormatCurrField(sOriginalValue)
                            sNewValue = goTR.FormatCurrField(sNewValue)
                        End If
                    End If

                    If sOriginalValue.ToString().Trim().Equals(sNewValue.ToString().Trim()) Then
                        Continue For
                    End If

                    Dim srow As String = ""

                    If sField.ToLower().Contains("mls_") Then
                        sOriginalValue = goTR.MLSToString(doRS.GetFileName, sField, sOriginalValue)
                        sNewValue = goTR.MLSToString(doRS.GetFileName, sField, sNewValue)
                    End If

                    srow = "<strong>" & goData.GetFieldLabelFromName(doRS.GetFileName, sField) & "</strong>"
                    srow = srow & vbTab & "<strike>" & sOriginalValue & "</strike>"
                    srow = srow & vbTab & sNewValue

                    sModifiedFields = sModifiedFields & Environment.NewLine & srow

                Next

                Return sModifiedFields

            End If

            Return ""
        'Catch ex As Exception
        '    Throw ex
        'End Try

    End Function

End Class
