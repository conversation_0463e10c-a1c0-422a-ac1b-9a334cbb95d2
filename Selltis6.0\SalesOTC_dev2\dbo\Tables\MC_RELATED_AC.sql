﻿CREATE TABLE [dbo].[MC_RELATED_AC] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_MC_RELATED_AC_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [<PERSON><PERSON>_<PERSON>] UNIQUEIDENTIFIER NOT NULL,
    [GID_AC] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MC_RELATED_AC] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_AC_RELATED_MC] FOREIGN KEY ([GID_MC]) REFERENCES [dbo].[MC] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_MC_RELATED_AC] FOREIGN KEY ([GID_AC]) REFERENCES [dbo].[AC] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MC_RELATED_AC] NOCHECK CONSTRAINT [<PERSON>NK_AC_RELATED_MC];


GO
ALTER TABLE [dbo].[MC_RELATED_AC] NOCHECK CONSTRAINT [LNK_MC_RELATED_AC];


GO
CREATE NONCLUSTERED INDEX [IX_MC_RELATED_AC]
    ON [dbo].[MC_RELATED_AC]([GID_MC] ASC, [GID_AC] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_AC_RELATED_MC]
    ON [dbo].[MC_RELATED_AC]([GID_AC] ASC, [GID_MC] ASC);

