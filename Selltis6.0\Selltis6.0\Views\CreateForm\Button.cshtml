﻿@model Selltis.MVC.Models.ControlAttributes
@using Selltis.BusinessLogic;

@{
    string sScript = "";

    string _Field = Model.field;
    string output = Model.field.Substring(Model.field.Length - 2);

    //if (output.Contains("_"))
    //{
    //    _Field = Model.field.Substring(0, Model.field.Length - 2);
    //}

    var cScripts = Selltis.Core.Util.GetScripts(_Field, Model.TableName);
    if (cScripts.Count <= 0)
    {
        if (output.Contains("_"))
        {
            _Field = Model.field.Substring(0, Model.field.Length - 2);
        }
        cScripts = Selltis.Core.Util.GetScripts(_Field, Model.TableName);
    }
    if (cScripts.Count > 0)
    {
        sScript = Selltis.Core.Util.GetEventHandler(cScripts, "FORMCONTROLONCHANGE", _Field, Model.TableName);
    }
    string btnClass = "btn btn-link";
    string btnStyle = "padding-left:0px;padding-top:0px;padding-bottom:12px;";

    if(Model.IsNDB_Form)
    {
        btnClass = "btn btn-sm btn-info";
        btnStyle = "";
    }

    if(Model.IsHeaderControl)
    {
        btnStyle = "padding-left:0px;padding-top:0px;";
    }

}
@if (Model.image.Equals(clC.EOT.ToString()) || Model.image.Equals(string.Empty))
{
    <span class="input-group-addon"></span>

    if ((Model.ControlState.FieldPropertiy.State == 1 || Model.ControlState.FieldPropertiy.State == 4))
    {
        <button type="button" tabindex="-1" style="display:@Model.display;@btnStyle" class="@btnClass" onclick="RunScript('@sScript')" id=@Model.field title='@Html.Raw(@Model.title)' disabled="disabled" src='@Model.image'>
            @Model.label
        </button>
    }
    else
    {
        <button type="button" tabindex="-1" style="display:@Model.display;@btnStyle" class="@btnClass" onclick="RunScript('@sScript')" id=@Model.field title='@Html.Raw(@Model.title)' src='@Model.image'>
            @Model.label
        </button>
    }


}
else if (Model.label.Contains("<a href"))
{
    <span class="input-group-addon"></span>
    if ((Model.ControlState.FieldPropertiy.State == 1 || Model.ControlState.FieldPropertiy.State == 4))
    {
        <input type="image" src='@Model.image' style="padding-left:0px;" id=@Model.field title='@Html.Raw(@Model.title)' disabled="disabled" tabindex="-1" /><span style="vertical-align: text-bottom;">&nbsp;@Html.Raw(Model.label)</span>
    }
    else
    {
        <input type="image" src='@Model.image' style="padding-left:0px;" id=@Model.field title='@Html.Raw(@Model.title)' tabindex="-1" /><span style="vertical-align: text-bottom;">&nbsp;@Html.Raw(Model.label)</span>
    }
}
else
{
    if (!string.IsNullOrEmpty(sScript))
    {
        <span class="input-group-addon"></span>
        if ((Model.ControlState.FieldPropertiy.State == 1 || Model.ControlState.FieldPropertiy.State == 4))
        {
            //S_B To show tooltip on Recurrence button written exclusive logic for this synario. Not a good practice
            if (sScript.Contains("BTN_RECURRENCE"))
            {
                <button type="button" style="display:@Model.display;@btnStyle" class="@btnClass" title='Click here to edit recurring appointment settings @Html.Raw(@Model.title)' onclick="RunScript('@sScript')" disabled="disabled" tabindex="-1">
                    <img style="vertical-align: text-bottom;" src='@Model.image' id=@Model.field tabindex="-1"><span style="vertical-align: text-bottom;">&nbsp;@Model.label</span>
                </button>
            }
            else
            {
                <button type="button" style="@btnStyle" class="@btnClass" onclick="RunScript('@sScript')" disabled="disabled" tabindex="-1">
                    <img style="vertical-align: text-bottom;" src='@Model.image' id=@Model.field title='@Html.Raw(@Model.title)' tabindex="-1"><span title='@Html.Raw(@Model.title)' style="vertical-align: text-bottom;">&nbsp;@Model.label</span>
                </button>
            }
        }
        else
        {
            //S_B To show tooltip on Recurrence button written exclusive logic for this synario. Not a good practice
            if (sScript.Contains("BTN_RECURRENCE"))
            {
                <button type="button" style="display:@Model.display;@btnStyle" class="@btnClass" title='Click here to edit recurring appointment settings @Html.Raw(@Model.title)' onclick="RunScript('@sScript')" tabindex="-1">
                    <img style="vertical-align: text-bottom;" src='@Model.image' id=@Model.field tabindex="-1"><span style="vertical-align: text-bottom;">&nbsp;@Model.label</span>
                </button>
            }
            else
            {
                <button type="button" style="@btnStyle" class="@btnClass" onclick="RunScript('@sScript')" tabindex="-1">
                    @*<img style="vertical-align: text-bottom;display:@Model.display;" src='@Model.image' id=@Model.field title='@Html.Raw(@Model.title)' tabindex="-1"><span title='@Html.Raw(@Model.title)' style="vertical-align: text-bottom;">&nbsp;@Model.label</span>*@
                    <img style="vertical-align: top;" src='@Model.image' id=@Model.field title='@Html.Raw(@Model.title)' tabindex="-1"><span title='@Html.Raw(@Model.title)' style="vertical-align: text-bottom;">&nbsp;@Model.label</span>
                </button>
            }
        }

    }
    else
    {
        <span class="input-group-addon"></span>
        if ((Model.ControlState.FieldPropertiy.State == 1 || Model.ControlState.FieldPropertiy.State == 4))
        {
            <button type="button" style="@btnStyle" class="@btnClass" disabled="disabled" tabindex="-1">
                <img style="vertical-align: text-bottom;" src='@Model.image' id=@Model.field title='@Html.Raw(@Model.title)' tabindex="-1"><span title='@Html.Raw(@Model.title)' style="vertical-align: text-bottom;">&nbsp;@Model.label</span>
            </button>
        }
        else
        {
            <button type="button" style="@btnStyle" class="@btnClass" tabindex="-1">
                <img style="vertical-align: text-bottom;" src='@Model.image' id=@Model.field title='@Html.Raw(@Model.title)' tabindex="-1"><span title='@Html.Raw(@Model.title)' style="vertical-align: text-bottom;">&nbsp;@Model.label</span>
            </button>
        }

    }

}

