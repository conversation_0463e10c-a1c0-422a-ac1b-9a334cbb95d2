﻿CREATE TABLE [dbo].[MS_Related_OP] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Message_Related_Opp_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_MS] UNIQUEIDENTIFIER NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MS_Related_OP] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_MS_Related_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_Connected_MS] FOREIGN KEY ([GID_MS]) REFERENCES [dbo].[MS] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MS_Related_OP] NOCHECK CONSTRAINT [LNK_MS_Related_OP];


GO
ALTER TABLE [dbo].[MS_Related_OP] NOCHECK CONSTRAINT [LNK_OP_Connected_MS];


GO
CREATE CLUSTERED INDEX [IX_OP_Connected_MS]
    ON [dbo].[MS_Related_OP]([GID_MS] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MS_Related_OP]
    ON [dbo].[MS_Related_OP]([GID_OP] ASC);

