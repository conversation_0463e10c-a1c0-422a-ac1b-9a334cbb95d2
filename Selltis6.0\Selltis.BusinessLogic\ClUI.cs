﻿using System;
using System.Web;
using static System.Math;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using System.Data;

namespace Selltis.BusinessLogic
{
	public class ClUI
	{

		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clLog goLog;
		private clDefaults goDef;
		private clScrMngRowSet goScr;
		private clNumMask goNumMask;
		// Dim goUI As clUI
		private clHistory goHist;
		private clPerm goPerm;
		private clWorkareaMessage goWAMess;

		private Microsoft.VisualBasic.Collection gcUIObjects = new Microsoft.VisualBasic.Collection();
		private Microsoft.VisualBasic.Collection gcNavigationQueue = new Microsoft.VisualBasic.Collection();
		private Microsoft.VisualBasic.Collection gcQueuedObjectsToLoad = new Microsoft.VisualBasic.Collection();
		private static bool gbExecuteSendPopup = false;
		private static List<QueueItems> QueueList = new List<QueueItems>();

		public bool AddAlert(string par_sMessage, string par_sType, string par_sExecute, string par_sUser = clC.SELL_USER_ALL, string par_sIcon = "", string par_sProduct = "XX", string par_sTooltip = "")
		{
			//MI 4/24/12 Started adding cuplicate checking code, but abandoned the approach. Would require a query for each user
			//       in the loop for the following 'line' records in MD, which constitute a duplicate:
			//       TYPE=
			//       MESSAGE=
			//       EXECUTE=
			//       INNER JOINS would be needed for those 3 MD records.
			//       This was originally requested by the dev team (RH) to prevent writing duplicate alerts in a high alert 
			//       creation environment, all in response to a customer request for Tena to write an alert creation routine, 
			//       which would create 2400 alerts/day.
			//MI 4/20/12 Writing MESSAGE and TOOLTIP only when not "".
			//WT 10/27/10 added par_sTooltip
			//MI 2/6/09 Added par_sProduct.
			//MI 10/26/07 Added logging.

			//PURPOSE:
			//  Create one or more new alert(s)(ALT_) record(s) in metadata.  Metadata is written using an add rowset. See notes within
			//  the code below. BY default alerts are written for XX product (which makes it apply to all products).
			//PARAMETERS:
			//  par_sMessage: Text to display in the ALERTS window.
			//  par_sType: Alert type. Supported values:
			//   OPENRECORD  'Record TID
			//   OPENDESKTOP  'Page ID of the desktop to open
			//   OPENNEWFORM     'type of form to open (CRU_CN, CRL_CN)
			//   RUNSCRIPT   'script name
			//   *** NOT SUPPORTED IN SELLSQL  *** PROGRAM   'Full path of the program to run (<ProgramDir> and <DBDir>
			//       'keywords supported
			//   DOCUMENT     URL
			//  par_sExecute: ID of the record, page ID of desktop, type of new form to open, script to run
			//   or the URL to run/open. See par_sType for supported alert types.
			//  par_sIcon: Optional: Icon file name without path.
			//   Evaluated from images.
			//  par_sProduct: Product code like SA, MB, or XX for 'all products. If not specified,
			//   'XX' will be used. To get the current session's product, send goP.GetProduct() in this param.
			//  par_sTooltip: tooltip shown when hovering over alert in alerts panel. if blank, actual tooltip is MESSAGE
			//  *** NOT SUPPORTED *** par_bMsgBox: Display the alert as an 'above all' message box instead of in the
			//   ALERTS window. The Alerts button in SellSQL doesn't change if this property is True.
			//  *** NOT SUPPORTED *** par_sMsgType: Message type (ignored if par_bMsgBox <> 1). Supported values:
			//   OK    'OK button only
			//   YESNO   'Yes and No buttons (default)
			//   'LIST   'Currently not supported because LIST messages allow specifying
			//       'automator actions. For now we don't want to deal with
			//       'running automator actions outside of the automator context
			//RETURNS:
			//  String: True on success or errors
			//EXAMPLE:

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			goDef = (Selltis.BusinessLogic.clDefaults)HttpContext.Current.Session["goDef"];
			goScr = (Selltis.BusinessLogic.clScrMngRowSet)HttpContext.Current.Session["goScr"];
			goNumMask = (Selltis.BusinessLogic.clNumMask)HttpContext.Current.Session["goNumMask"];
			//goUI = HttpContext.Current.Session("goUI")
			goHist = (Selltis.BusinessLogic.clHistory)HttpContext.Current.Session["goHist"];
			goPerm = (Selltis.BusinessLogic.clPerm)HttpContext.Current.Session["goPerm"];

			string sProc = "clUI::AddAlert";
			goLog.Log(sProc, "Start: '" + par_sMessage + "' Type: '" + par_sType + "' Execute: '" + par_sExecute + "' User: '" + par_sUser + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			string sString = "";
			clRowSet rs = null;
			int i = 0;
			string sStr = null;
			string sUserGID = null;

			//Try

			//Write parameters for the memo
			goTR.StrWrite(ref sString, "US_NAME", "Alert");
				if (par_sMessage != "")
				{
					goTR.StrWrite(ref sString, "MESSAGE", par_sMessage);
				}
				goTR.StrWrite(ref sString, "TYPE", par_sType);
				goTR.StrWrite(ref sString, "EXECUTE", par_sExecute);
				if (par_sTooltip != "")
				{
					goTR.StrWrite(ref sString, "TOOLTIP", par_sTooltip);
				}
				if (par_sIcon != "")
				{
					goTR.StrWrite(ref sString, "ICON", par_sIcon);
				}

				//Get rs of GID_IDs for users to add alerts for
				if (par_sUser == clC.SELL_USER_ALL)
				{
					rs = new clRowSet("US", clC.SELL_READONLY, "CHK_ActiveField='1'", "", "GID_ID");
				}
				else
				{
					rs = new clRowSet("US", clC.SELL_READONLY, "GID_ID='" + par_sUser + "' AND CHK_ActiveField='1'", "", "GID_ID");
				}

				//Add an alert for each active user
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of rs.Count for every iteration:
				int tempVar = (int)rs.Count();
				for (i = 1; i <= tempVar; i++)
				{
					sUserGID = Convert.ToString(rs.GetFieldVal("GID_ID", clC.SELL_FRIENDLY));
					sStr = "ALT_" + goData.GenGuid();
					//MI 4/24/12 ==> Evaluate duplicates for sUserGID here. 
					//A duplicate is an MD 'page' that has the same TYPE= MESSAGE= EXECUTE=.
					goMeta.PageWrite(sUserGID, sStr, sString, "Alert", "", par_sProduct);

					rs.GetNext();
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45101, sProc)
			//    End If
			//End Try

			return true;
		}

		public void Queue(string sType, object NavigationItem)
		{

			QueueItems lQueueItems = new QueueItems();

			lQueueItems.IsNavigate = true;
			lQueueItems.NavigationItem = NavigationItem;
			lQueueItems.NavigationType = sType;

			QueueList.Add(lQueueItems);

			//gcNavigationQueue.Add(lQueueItems)

			//HttpContext.Current.Session(clSettings.GetHostName() + "_IsNavigate") = True
			//HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationItem") = NavigationItem
			//HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationType") = sType 'FORM / DESKTOP
		}

		public void ReleaseQueue(int i = 0)
		{

			string sProc = "clUI::ReleaseQueue";
			//Try

			if (QueueList.Count > 0)
			{
				QueueList.RemoveAt(i); ////25042019 tckt #2793:None of the checkboxes on the top of the forms to create another record when you save an leave open are working. On JCI the from ac the create qt is not working...J
			}

			//Dim sNav As String = ""
			//Dim oQI As QueueItems
			//'Dim oHI As clHistoryItem
			//Dim i As Integer
			//'add to ui object collection
			//'add to history
			//For i = Me.gcNavigationQueue.Count To 1 Step -1
			//    oQI = Me.gcNavigationQueue.Item(i)
			//    Dim sGuid As String = oQI.NavigationItem.GUID
			//    AddUIObject(sGuid, oQI.NavigationItem)
			//    Dim sTitle = oQI.NavigationItem.Title
			//    'If oQI.NavigationItem.GetType.ToString.ToUpper = "CLFORM" Then sTitle = oQI.NavigationItem.TitleForHistory
			//    'oHI = New clHistoryItem(oQI.Type, sGuid, sTitle, goHist.GetNextSeqNo.ToString)
			//    'goHist.Add(oHI)
			//    RequiresLoad(sGuid)
			//Next
			//Me.gcNavigationQueue.Clear()
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45101, sProc)
			//    End If
			//End Try

			//HttpContext.Current.Session(clSettings.GetHostName() + "_IsNavigate") = False
			//HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationItem") = Nothing
			//HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationType") = Nothing
		}

		public bool AddUIObject(string sGUID, object oUIObject)
		{
			string sProc = "clUI::AddUIObject";
			//Try
			if (gcUIObjects.Contains(sGUID))
			{
					return false;
				}
				else
				{
					gcUIObjects.Add(oUIObject, sGUID);
					return true;
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//    Return False
			//End Try
		}

		private void RequiresLoad(string sGuid)
		{
			if (gcQueuedObjectsToLoad.Contains(sGuid))
			{
			}
			else
			{
				gcQueuedObjectsToLoad.Add(sGuid, sGuid);
			}
		}

		public bool IsQueueLoaded()
		{
			return QueueList.Count > 0;
			//If HttpContext.Current.Session(clSettings.GetHostName() + "_IsNavigate") <> False AndAlso Not String.IsNullOrEmpty(HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationType")) Then
			//    Return True
			//End If
			//Return False

		}

		public int QueueCount()
		{
			return QueueList.Count;
		}

		public string NavigateType(int i = 0)
		{
			if (QueueList.Count > 0)
			{
				return QueueList[i].NavigationType;
			}

			return "";


			//If Not String.IsNullOrEmpty(HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationType")) Then
			//    Return HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationType").ToString()
			//End If
			//Return ""
		}

		public void ReleaseAllFromQueue()
		{
			if (QueueList.Count > 0)
			{
				QueueList.Clear();
			}
		}

		public object NavigateItem(int i = 0)
		{

			if (QueueList.Count > 0)
			{
				return QueueList[i].NavigationItem;
			}

			return null;

			//If HttpContext.Current.Session("NavigationItem") <> Nothing Then
			//    Return HttpContext.Current.Session("NavigationItem")
			//End If
			//Return Nothing
			//Return HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationItem")
		}

		public object NavigateSelectedItem(int i)
		{

			if (QueueList.Count > 0)
			{
				return QueueList[i].NavigationItem;
			}

			return null;

			//If HttpContext.Current.Session("NavigationItem") <> Nothing Then
			//    Return HttpContext.Current.Session("NavigationItem")
			//End If
			//Return Nothing
			//Return HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationItem")
		}

		public void OpenURLExternal(string sUrl, string sWindowTitle = "Selltis", string sParams = "")
		{

			if (sUrl.ToLower().Contains("cus_") && HttpContext.Current.Session[clSettings.GetHostName() + "_SiteSettings"] != null)
			{

				//get the public files folder and set the path

				System.Data.DataTable dt = new DataTable();

				dt = (System.Data.DataTable)HttpContext.Current.Session[clSettings.GetHostName() + "_SiteSettings"];

				string sSiteId = (dt.Rows[0]["SiteId"] == null ? null : Convert.ToString(dt.Rows[0]["SiteId"]));

				string FileName = sUrl.Substring(sUrl.ToLower().IndexOf("cus_"));

				HttpContext.Current.Session[clSettings.GetHostName() + "_ExternalURLPath"] = "../../PublicFiles/" + sSiteId + "/WebForms/" + FileName;
			}
			else
			{
				HttpContext.Current.Session[clSettings.GetHostName() + "_ExternalURLPath"] = sUrl;
			}

			HttpContext.Current.Session[clSettings.GetHostName() + "_IsNavigate"] = true;
			HttpContext.Current.Session[clSettings.GetHostName() + "_NavigationItem"] = sUrl;
			HttpContext.Current.Session[clSettings.GetHostName() + "_ExtUrlTitle"] = sWindowTitle;
			HttpContext.Current.Session[clSettings.GetHostName() + "_NavigationType"] = "OPENURLEXTERNAL"; //FORM / DESKTOP
			HttpContext.Current.Session[clSettings.GetHostName() + "_ExtUrlParms"] = sParams;
		}

		public void AddNDBFormToQueue(string sType, object NavigationItem, object NavigationValue)
		{

			HttpContext.Current.Session[clSettings.GetHostName() + "_IsNavigate"] = true;
			HttpContext.Current.Session[clSettings.GetHostName() + "_NavigationItem"] = NavigationItem;
			HttpContext.Current.Session[clSettings.GetHostName() + "_NavigationType"] = sType; //FORM / DESKTOP
			HttpContext.Current.Session[clSettings.GetHostName() + "_NavigationValue"] = NavigationValue;
		}


		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script, ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2, string par_s3, string par_s4)
		{
			NewWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, "");
		}

		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script, ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2, string par_s3)
		{
			NewWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, "", "");
		}

		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script, ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2)
		{
			NewWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref par_doCallingObject, par_doArray, par_s1, par_s2, "", "", "");
		}

		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script, ref object par_doCallingObject, clArray par_doArray, string par_s1)
		{
			NewWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref par_doCallingObject, par_doArray, par_s1, "", "", "", "");
		}

		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script, ref object par_doCallingObject, clArray par_doArray)
		{
			NewWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref par_doCallingObject, par_doArray, "", "", "", "", "");
		}

		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script, ref object par_doCallingObject)
		{
			NewWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref par_doCallingObject, null, "", "", "", "", "");
		}

		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script)
		{
			object tempVar = null;
			NewWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref tempVar, null, "", "", "", "", "");
		}

		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script)
		{
			object tempVar = null;
			NewWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, "", ref tempVar, null, "", "", "", "", "");
		}

		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script)
		{
			object tempVar = null;
			NewWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, "", "", ref tempVar, null, "", "", "", "", "");
		}

		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue)
		{
			object tempVar = null;
			NewWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, "", "", "", ref tempVar, null, "", "", "", "", "");
		}

		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label)
		{
			object tempVar = null;
			NewWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, "", "", "", "", ref tempVar, null, "", "", "", "", "");
		}

		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label)
		{
			object tempVar = null;
			NewWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, "", "", "", "", "", ref tempVar, null, "", "", "", "", "");
		}

		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label)
		{
			object tempVar = null;
			NewWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, "", "", "", "", "", "", ref tempVar, null, "", "", "", "", "");
		}

		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle)
		{
			object tempVar = null;
			NewWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, "", "", "", "", "", "", "", ref tempVar, null, "", "", "", "", "");
		}

		public void NewWorkareaMessage(string par_sMessage, int par_iStyle)
		{
			object tempVar = null;
			NewWorkareaMessage(par_sMessage, par_iStyle, "Selltis", "", "", "", "", "", "", "", ref tempVar, null, "", "", "", "", "");
		}

		public void NewWorkareaMessage(string par_sMessage)
		{
			object tempVar = null;
			NewWorkareaMessage(par_sMessage, clC.SELL_MB_OK, "Selltis", "", "", "", "", "", "", "", ref tempVar, null, "", "", "", "", "");
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Public Sub NewWorkareaMessage(ByVal par_sMessage As String, Optional ByVal par_iStyle As Integer = clC.SELL_MB_OK, Optional ByVal par_sTitle As String = "Selltis", Optional ByVal par_sButton1Label As String = "", Optional ByVal par_sButton2Label As String = "", Optional ByVal par_sButton3Label As String = "", Optional ByVal par_sInputDefaultValue As String = "", Optional ByVal par_sButton1Script As String = "", Optional ByVal par_sButton2Script As String = "", Optional ByVal par_sButton3Script As String = "", Optional ByRef par_doCallingObject As Object = Nothing, Optional ByVal par_doArray As clArray = Nothing, Optional ByVal par_s1 As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "")
		public void NewWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script, ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5)
		{
			goWAMess = new clWorkareaMessage(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5);

			HttpContext.Current.Session[clSettings.GetHostName() + "_NewWorkareaMessage"] = goWAMess;
		}

		public string GetLastSelected(string sWhat)
		{
			switch (sWhat)
			{
				case "SELECTEDRECORDID":
					if (HttpContext.Current.Session[clSettings.GetHostName() + "_SelectedRecordID"] != null)
					{
						return HttpContext.Current.Session[clSettings.GetHostName() + "_SelectedRecordID"].ToString();
					}
					break;
				case "LASTOPENDESKTOPGUID":
					return "";
				default:
					return "";
			}

			return "";
		}

		public bool ExecuteSendPopup //S_B On Calling it will set true/false based on passing value
		{
			get
			{
				return gbExecuteSendPopup;
			}
			set
			{
				gbExecuteSendPopup = value; //S_B true for open a popup in Form page.
			}
		}

	}

	public class QueueItems
	{
		public bool IsNavigate;
		public object NavigationItem;
		public string NavigationType;
	}

}
