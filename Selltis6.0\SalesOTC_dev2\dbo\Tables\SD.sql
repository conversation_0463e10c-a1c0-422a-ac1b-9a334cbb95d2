﻿CREATE TABLE [dbo].[SD] (
    [GID_ID]                  UNIQUEIDENTIFIER CONSTRAINT [DF_SD_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'SD',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                  BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                NVARCHAR (80)    NULL,
    [DTT_CreationTime]        DATETIME         CONSTRAINT [DF_SD_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]            TINYINT          NULL,
    [TXT_ModBy]               VARCHAR (4)      NULL,
    [DTT_ModTime]             DATETIME         CONSTRAINT [DF_SD_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_SelltisDownloadName] NVARCHAR (50)    NULL,
    [MMO_ImportData]          NTEXT            NULL,
    [SI__ShareState]          TINYINT          CONSTRAINT [DF_SD_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]        UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]            VARCHAR (50)     NULL,
    [TXT_ExternalID]          NVARCHAR (80)    NULL,
    [TXT_ExternalSource]      VARCHAR (10)     NULL,
    [TXT_ImpJobID]            VARCHAR (20)     NULL,
    CONSTRAINT [PK_SD] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_SD_SelltisDownloadName]
    ON [dbo].[SD]([TXT_SelltisDownloadName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SD_CreatedBy_US]
    ON [dbo].[SD]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SD_ModDateTime]
    ON [dbo].[SD]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SD_Name]
    ON [dbo].[SD]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SD_CreationTime]
    ON [dbo].[SD]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_SD_BI__ID]
    ON [dbo].[SD]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_SD_TXT_ImportID]
    ON [dbo].[SD]([TXT_ImportID] ASC);

