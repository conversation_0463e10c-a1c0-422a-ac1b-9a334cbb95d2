﻿CREATE TABLE [dbo].[PR_Involved_CN] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Project_Involved_Contact_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PR] UNIQUEIDENTIFIER NOT NULL,
    [GID_CN] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PR_Involved_CN] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CN_InvolvedIn_PR] FOREIGN KEY ([GID_PR]) REFERENCES [dbo].[PR] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PR_Involved_CN] FOREIGN KEY ([GID_CN]) REFERENCES [dbo].[CN] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PR_Involved_CN] NOCHECK CONSTRAINT [LNK_CN_InvolvedIn_PR];


GO
ALTER TABLE [dbo].[PR_Involved_CN] NOCHECK CONSTRAINT [LNK_PR_Involved_CN];


GO
CREATE CLUSTERED INDEX [IX_PR_Involved_CN]
    ON [dbo].[PR_Involved_CN]([GID_CN] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_CN_InvolvedIn_PR]
    ON [dbo].[PR_Involved_CN]([GID_PR] ASC);

