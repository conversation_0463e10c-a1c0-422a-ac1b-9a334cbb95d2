﻿using System;

//Owner: RH

using System.Web;
using System.Web.Routing;
using System.Web.Mvc;
using System.Diagnostics;
using System.Data;

namespace Selltis.BusinessLogic
{
	public class clLog
	{


		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clLog goLog;

		//Local private variables declared in the declarations section of clLog
		//need to be public variables.
		private object oSellErrs; //for SellErrs.dll
		private bool bTrace;
		private long lFileHandle; //handle of the log file
		private long lErrFileHandle; //Handle of the error file
		private string sErrorMsg; //Error message
		private string sWindowInfo; //Informations on current window
		private string sLogFileName; //Name of the log file (with path)
		private string sErrorFileName; //Name of the error file (with path)
		private short siLogLevel = 0; //Current level of LOG
		private long liMaxLogSize; //Maximum size of Log.txt file
		private long liLogDecrementSize; //When File is > max size, we remove around liLogDecrementSize at
		//the beginning
		private bool bManagingSize;
		private string sDoNotResizeBefore; //Date & Time before we should not try again to resize, if conflict
		//between two exes...
		private bool bProgressBar;
		private long lLastError; //Last error number
		private string sLastErrorMessage; //Last error message
		private string sLastErrorProcedure; //Procedure in which the error occurred
		private string sPar1; //1rst param of the error
		private string sPar2; //1rst param of the error
		private string sPar3; //1rst param of the error
		private string sPar4; //1rst param of the error
		private string sPar5; //1rst param of the error
		private string sPar6; //1rst param of the error
		private string sPar7; //1rst param of the error
		private string sPar8; //1rst param of the error
		private string sPar9; //1rst param of the error
		private string sPar10; //1rst param of the error
		private Array dasLastErrors;

		public void Initialize()
		{

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
		}

		public string GetErrorMessage(long par_lError)
		{
			//MI 7/11/06 Replaced NumToString with padding lError with zeros to left.

			//PURPOSE:
			//		Retrieve an error mesage inthe current langage, depending on the error number given here
			//PARAMETERS:
			//		par_lError:		Number of the error
			//RETURNS:
			//		a string containing the error message, or an empty string if not found
			//HOW IT WORKS:
			//		
			//EXAMPLE:
			//		sMessage=goLog:GetErrorMessage(lError)

			string sProc = "clLog::GetErrorMessage";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_lError , SELL_LOGLEVEL_DEBUG)

			long lError = par_lError;
			string sError = "";
			string sString = "";
			string sMess = "";
			//Retrieve the error message

			sError = ("00000" + lError.ToString()).Substring(("00000" + lError.ToString()).Length - 5);

			sMess = goMeta.LineRead("GLOBAL", "ERR_" + sError, "TXT");

			return sMess;
		}
		public string GetErrorMsg()
		{
			//CS OK     

			return sErrorMsg;

		}
		public string GetLastError(string par_sParam = "NUMBER") //SELL_ERROR_NUMBER
		{
			//MI 7/11/06 Replaced NumToString with padding lError with zeros on left.

			//PURPOSE:
			//		Retrive the last error number, message and Procedure 
			//PARAMETERS:
			//		par_sParam:		if SELL_ERROR_NUMBER (or empty), send back the number of the last error
			//						if SELL_ERROR_MESSAGE, send back the message of the last error
			//						if SELL_ERROR_PROC, send back the Procedure  name of the last error
			//                       if SELL_ERROR_PARAMS, send back tab delimited string of params
			//RETURNS:
			//		a string containg various information (ses upper)
			//HOW IT WORKS:
			//		call this after a function send you a FALSE result (like hADD)
			//EXAMPLE:
			//		if not goData:hAdd(....) then info("Last error was "+goLog:GetLastError(SELL_ERROR_NUMBER))

			//sProc is string="clLog::GetLastError"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_sParam, SELL_LOGLEVEL_DETAILS)

			string sResult = "";

			//CS: Commenting select for now until have needed things
			switch (par_sParam.ToUpper().Trim(' '))
			{

				case "MESSAGE": //SELL_ERROR_MESSAGE
					sResult = sLastErrorMessage;
					break;
				case "PROCEDURE": //SELL_ERROR_PROC
					sResult = sLastErrorProcedure;
					break;
				case "PARAMS": //SELL_ERROR_PARAMS
					sResult = (sPar1 + "\t" + sPar2 + "\t" + sPar3 + "\t" + sPar4 + "\t" + sPar5 + "\t" + sPar6 + "\t" + sPar7 + "\t" + sPar8 + "\t" + sPar9 + "\t" + sPar10).Trim(' ');
					break;
					//	sResult=nospace(:sPar1+" "+:sPar2+" "+:sPar3+" "+:sPar4+" "+:sPar5+" "+:sPar6+" "+:sPar7+" "+:sPar8+" "+:sPar9+" "+:sPar10)
				default: //And mainly SELL_ERROR_NUMBER, send back the error number
					//(in case of error, at least we have the error number)
					sResult = "E" + ("00000" + lLastError.ToString()).Substring(("00000" + lLastError.ToString()).Length - 5);
					break;
			}

			return sResult;
		}

		public bool Log(string par_sProc, string par_sMessage, short par_siLevel = 1, bool par_bStack = false, bool par_bTrace = false, short par_siWarnErr = 0, short par_tiPurpose = 0)
		{
			//MI 3/12/09 Added par_tiPurpose.
			//MI 3/5/07 Added clC.SELL_LOGLINE_MAXLENGTH as length of message.

			//PURPOSE:	Write in a text file a line to log current task/message
			//
			//PARAMETERS:
			//		par_sModule: 	Module Name (the name of the module where the call is made)
			//		par_sMessage: 	The message to log 
			//		par_siLevel:	Level of the log operation
			//		Levels available:
			//		0 - None: no logging except error messages
			//		1 - Always ON, only Entry and Exit of the main modules are logged (and errors, of course)
			//		2 - Details: main functionalities of each module are also logged
			//		3 - Debug: Everything is logged
			//       par_bStack: True if method stack log
			//       par_bTrace: Used by trace system to flag as a tracg log item
			//       par_siWarnErr: 0=not error or warning; 1=warning; 2=error
			//       par_tiPurpose: 0 (default)=unassigned; 1=login success; 2=login failure; 5=useragent string; 10=UI element start; 20=Support Alert; 30=View Load Time Warning; 40=Rewset New Debug Visibility
			//RETURNS:	
			//		True if the operation was logged, False if not (level of the operation > current level)

			//EXAMPLE:	goLog.Log("clImpExp:ExecuteImport", "Going to process script after line", 3)


			string sProc = "clLog::Log";

			//MI 12/17/08 Now allowing to log explicitly on loglevel 0 (no logging).
			//If par_siLevel < 1 Or par_siLevel > 3 Then
			if (par_siLevel < 0 || par_siLevel > 3)
			{
				goErr.SetWarning(47174, sProc, "", par_siLevel.ToString());
				return false;
			}

			if (par_bTrace == true || par_siWarnErr > 0)
			{
				//trace and error entries are already qualified. 
			}
			else
			{
				if (par_siLevel <= siLogLevel)
				{
					//Continue with log
				}
				else
				{
					//Log level not met, test if error or warning
					//1=warning; 2=error
					if (par_siWarnErr == 1 || par_siWarnErr == 2)
					{
						//Continue with log
					}
					else
					{
						return false;
					}
				}
			}

			bool bWebservice = false;
			if (HttpContext.Current.Request.UserAgent.IndexOf("MS Web Services Client") + 1 > 0)
			{
				bWebservice = true;
			}

			int iResult = 0;
			System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
			System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader oReader = null;



			oCommand.CommandText = "pWriteLog";
			oCommand.CommandType = CommandType.StoredProcedure;
			oCommand.Connection = oConnection;

			//parameter
			System.Data.SqlClient.SqlParameter strProc = new System.Data.SqlClient.SqlParameter("@par_sModule", SqlDbType.VarChar);
			strProc.Value = par_sProc.Substring(0, Math.Min(80, par_sProc.Length));
            oCommand.Parameters.Add(strProc);

			//parameter
			System.Data.SqlClient.SqlParameter strMessage = new System.Data.SqlClient.SqlParameter("@par_sMessage", SqlDbType.VarChar);
			strMessage.Value = par_sMessage.Substring(0, Math.Min(80, par_sProc.Length)); //*** MI 3/5/07
            oCommand.Parameters.Add(strMessage);

			//parameter
			System.Data.SqlClient.SqlParameter strSessionID = new System.Data.SqlClient.SqlParameter("@par_sSessionID", SqlDbType.VarChar);
            string sessionID = HttpContext.Current?.Session?.SessionID ?? "";
            strSessionID.Value = sessionID.Substring(0, Math.Min(80, sessionID.Length));

            oCommand.Parameters.Add(strSessionID);

			//parameter
			System.Data.SqlClient.SqlParameter tiLevel = new System.Data.SqlClient.SqlParameter("@par_tiLevel", SqlDbType.TinyInt);
			tiLevel.Value = par_siLevel;
			oCommand.Parameters.Add(tiLevel);

			//parameter
			System.Data.SqlClient.SqlParameter tiWarnErr = new System.Data.SqlClient.SqlParameter("@par_tiWarnErr", SqlDbType.TinyInt);
			tiLevel.Value = par_siWarnErr;
			oCommand.Parameters.Add(tiWarnErr);

			//parameter
			System.Data.SqlClient.SqlParameter bStack = new System.Data.SqlClient.SqlParameter("@par_bStack", SqlDbType.Bit);
			bStack.Value = par_bStack;
			oCommand.Parameters.Add(bStack);

			//parameter
			System.Data.SqlClient.SqlParameter bTrace = new System.Data.SqlClient.SqlParameter("@par_bTrace", SqlDbType.Bit);
			bTrace.Value = par_bTrace;
			oCommand.Parameters.Add(bTrace);

			//parameter
			System.Data.SqlClient.SqlParameter tiPurpose = new System.Data.SqlClient.SqlParameter("@par_tiPurpose", SqlDbType.TinyInt);
			tiPurpose.Value = par_tiPurpose;
			oCommand.Parameters.Add(tiPurpose);

			//parameter
			System.Data.SqlClient.SqlParameter bWS = new System.Data.SqlClient.SqlParameter("@par_bWebService", SqlDbType.Bit);
			bWS.Value = bWebservice;
			oCommand.Parameters.Add(bWS);

			//return parameter
			System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
			retValParam.Direction = ParameterDirection.ReturnValue;
			oCommand.Parameters.Add(retValParam);

			//execute

			oReader = oCommand.ExecuteReader();

			//Now you can grab the output parameter's value...
			iResult = Convert.ToInt16(retValParam.Value);

			oReader.Close();
			oConnection.Close();















			return true;
		}
		public void LogError(string par_sMessage = "", string par_sProcedure = "", bool par_bDisplay = true)
		{
			//CS In progress, RH review/finish
			//Need replacement for "exception.", goSM.SetData, goUI.postMessage

			//PORT syntax, but leave impertinent information not set (HF Files, Windows dir, etc)

			//PURPOSE:
			//		Log an error
			//PARAMETERS:
			//		par_sMessage:	If not sent, the message is built here
			//		par_sProcedure: if not sent, the exception/procedure is used (used only from script, where the internal procedure 
			//						name is not usable). Windev allows only about 100 scripts NAMES during an exe life, and therefore we 
			//						are reusing generic scripts names.
			//		par_bDisplay:	If false (true by default), the error is logged but not displayed to the user. This is used mainly in 
			//						the case of file reindexation needed, where another message is used to propose a solution to the user

			string sProc = "clLog::LogError";
			//This function writes in a text file the date, time 
			//software name, version, and the error message...
			bool bDisplay = par_bDisplay;

			//CS: Commenting all until have what we need.
			//Dim sCode As String = Exception.Code
			//Dim sError As String = Exception.Error
			//Dim sInfo As String = Exception.Info
			//Dim sDuring As String = Exception.Pendant
			//Dim sObject As String = Exception.objet
			//Dim sProcedure As String = Trim(par_sProcedure)
			//If sProcedure = "" Then
			//    sProcedure = Exception.Procedure
			//End If
			//Dim sLine As String = Exception.ligne
			//Dim sMessage As String = Exception.Message
			//Dim sWrkString As String = ""

			//Dim sDateString As String = Format(goTr.NowUTC(), "yyyyMMdd")  'DateSys()
			//Dim sTimeString As String = Format(goTr.NowUTC(), "HHmmssff")  'TimeSys()                     'TimeSys()

			//'Separator
			//goTR.StrWrite(sWrkString, "BEGIN", StrDup(50, "-")) 'repete("-", 50))

			//'Date+Time
			//goTR.StrWrite(sWrkString, "Date", goTR.DateToString(sDateString))
			//goTR.StrWrite(sWrkString, "Time", goTR.TimeToFullString(sTimeString))

			//'Message
			//goTR.StrWrite(sWrkString, "Message", par_sMessage)

			//'Error message
			//goTR.StrWrite(sWrkString, "Code", sCode)
			//goTR.StrWrite(sWrkString, "Error", sError)
			//goTR.StrWrite(sWrkString, "Info", sInfo)
			//goTR.StrWrite(sWrkString, "During", sDuring)
			//goTR.StrWrite(sWrkString, "Object", sObject)
			//goTR.StrWrite(sWrkString, "Procedure ", sProcedure)
			//goTR.StrWrite(sWrkString, "Line", sLine)

			//'Software()
			//goTR.StrWrite(sWrkString, "Software", Infoexe("Name"))
			//goTR.StrWrite(sWrkString, "Version", goP.gsVersion)
			//goTR.StrWrite(sWrkString, "Build", infoexe("Version"))

			//'HF(context)
			//goTR.StrWrite(sWrkString, "HF file", h.nomfichier)
			//goTR.StrWrite(sWrkString, "HF error", goData.GethError())
			//goTR.StrWrite(sWrkString, "HF internal error", h.erreurInt)
			//goTR.StrWrite(sWrkString, "HF comments", h.Commentaire)


			//'System(Information)
			//goTR.StrWrite(sWrkString, "Files directory", goP.gsFilesDir)  'Windows platform
			//goTR.StrWrite(sWrkString, "Screen Horizontal Size", NumTostring(SysXRes()))
			//goTR.StrWrite(sWrkString, "Screen Vertical Size", NumToString(SysYRes()))
			//goTR.StrWrite(sWrkString, "Colors", NumToString(syscolorRes()))   'Color numbers
			//goTR.StrWrite(sWrkString, "Memory", NumToString(sysspace()))      'Total memory in bytes
			//goTR.StrWrite(sWrkString, "Windows Directory", sysDir(True))  'Windows directory
			//goTR.StrWrite(sWrkString, "System Directory", Sysdir(False))  'System directory
			//goTR.StrWrite(sWrkString, "Windows ", NumToString(sysversionwindows()))   'Windows type
			//goTR.StrWrite(sWrkString, "Windows Version", NumToString(sysversionwindows(SysVersionNum�ro)))    'Windows version
			//goTR.StrWrite(sWrkString, "Windows Build", NumToString(sysversionwindows(SysVersionCompil)))  'Windows build
			//goTR.StrWrite(sWrkString, "Windows Platform", NumToString(sysversionwindows(SysVersionPlateForme)))   'Windows platform

			//'Separator
			//goTR.StrWrite(sWrkString, "END", StrDup(50, "-"))

			//Dim lResult As Long = Write(lErrFileHandle, sWrkString) 'lErrFileHandle is class variable
			//If lResult = -1 Then
			//    MsgBox(goTR.MessComplete("Can't write the error in file '[1]'!", sLogFileName, sWrkString), 6, "Selltis") 'MessTranslate

			//    '5004:Can't write the error in file '[1]'!		
			//End If
			//'Log line
			//Log("ERROR " & sObject & "." & sProcedure, _
			//    sInfo & " Date " & goTR.DateToString(sDateString) & _
			//    " Time " & goTR.TimeToFullString(sTimeString), 1) 'SELL_LOGLEVEL_STANDARD

			//'Error display
			//Select Case goP.sRunMode  'needs to be done in below 2 stmts

			//    Case clC.SELL_MODE_API
			//        'We send back the error information to the VB side API
			//        'Retrieve error information
			//        goTR.StrWrite(sError, "NUMBER", sCode)
			//        goTR.StrWrite(sError, "DESCRIPTION", sMessage)
			//        goTR.StrWrite(sError, "MODULE", sObject & " " & sProcedure & " " & sLine)
			//        goP.SellTrace(goP.gsUserID, "Exception handling " & sError, "goLog.LogError")
			//        'put it in the shared memory
			//        goSM.SetData(sError)
			//        'Then send the message signaling the error
			//        goUI.postMessage(glHwndActiveX, glMessage, 0, 0, sProc)
			//    Case Else   'General case
			//        If bDisplay Then    'JL - Faux dans les cas n�cessitant une r�indexation de fichier.
			//            goMsgBox.MessageBeep(MB_ICONERROR)
			//            'Open("WMETAPRO", "VIEW", sWrkString, MessTraduit(5010), MessTraduit(5018))
			//            Open("WMETAPRO", "VIEW", sWrkString, "Error", "An error occurred. Please send the details to your system administrator.") 'MessTranslate
			//            '5018:An error occurred. Please send the details to your system administrator.
			//            '5010:Error
			//        End If
			//End Select


		}

		public bool WriteToEventLog(string Entry, EventLogEntryType EventType = EventLogEntryType.Information, int EventID = 0)
		{

			//*************************************************************
			//PURPOSE: Write Entry to Event Log using VB.NET
			//PARAMETERS: Entry - Value to Write
			//            AppName - Name of Client Application. Needed 
			//              because before writing to event log, you must 
			//              have a named EventLog source. 
			//            EventType - Entry Type, from EventLogEntryType 
			//              Structure e.g., EventLogEntryType.Warning, 
			//              EventLogEntryType.Error
			//            LogName: Name of Log (System, Application; 
			//              Security is read-only) If you 
			//              specify a non-existent log, the log will be
			//              created

			//RETURNS:   True if successful, false if not

			//EXAMPLES: 
			//1. Simple Example, Accepting All Defaults
			//    WriteToEventLog "Hello Event Log"

			//2.  Specify EventSource, EventType, and LogName
			//    WriteToEventLog("Danger, Danger, Danger", "MyVbApp", _
			//                      EventLogEntryType.Warning, "System")
			//
			//NOTE:     EventSources are tightly tied to their log. 
			//          So don't use the same source name for different 
			//          logs, and vice versa
			//******************************************************

			EventLog objEventLog = new EventLog();
			string sInfo = "Session ID: " + HttpContext.Current.Session.SessionID;

			// We dont need event logging.  This should be handled differently.
			return false;
			//Try
			//Register the App as an Event Source
			if (!EventLog.SourceExists("Selltis"))
			{
					EventLog.CreateEventSource("Selltis", "Application");
				}

				objEventLog.Source = "Selltis";

				//WriteEntry is overloaded; this is one
				//of 10 ways to call it

				// Try
				goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
					sInfo = sInfo + "\r\n" + "User: " + goP.sUserName;
				//Catch ex As Exception
				//    'Ignore
				//End Try

				objEventLog.WriteEntry(Entry + "\r\n" + "\r\n" + sInfo, EventType, EventID);
				return true;
			//Catch Ex As Exception
			//    Return False

			//End Try

		}

		public bool SetError(long par_lErrorNumber = 0, string par_sProcedure = "", string par_sLocalMessage = "", object par_vPar1 = null, object par_vPar2 = null, object par_vPar3 = null, object par_vPar4 = null, object par_vPar5 = null, object par_vPar6 = null, object par_vPar7 = null, object par_vPar8 = null, object par_vPar9 = null, object par_vPar10 = null, bool bSilent = false)
		{


			//PURPOSE:
			//		Prepare the return information of a method or function... 
			//PARAMETERS:
			//		par_lErrorNumber:		Number of the error (metadat GLOBAL, ERR_xxx) where xxx=number
			//								0=no error
			//		par_sProcedure : 		Procedure /method/code in which the error occurred
			//		par_sLocalMessage:		Local message that will replace the default message we should 
			//								find in metadata... Use this when you want to override the default 
			//								message by prepared for the calling process...
			//		par_vPar1..par_vPar10: optional values that will replace in the message the [1]..[10]
			//RETURNS:
			//		True if everything OK, false if a problem occurred (message number not found, 
			//		invalid parameters...), in that case, the Procedure  will at least set the error 
			//		number and a default message saying "Contact technical support"
			//HOW IT WORKS:
			//		The method will have to return false, and, before that, the developer will 
			//		call SetError to prepare the corresponding information (N� of error, cause 
			//		of the problem...)
			//		The calling process will have to call GetLastError and related methods to 
			//		know/display the cause of the error...
			//		
			//		After processing the error, the calling proces must consider to reset the error to 0 by 
			//		doing a goLog:SetError(). If this is not done, the error will be propagated to all 
			//		following processes, and therefore will abort any process that test the result of a 
			//		function
			//
			//EXAMPLE:
			//		goLog:SetError(10000, sProc, "", "Company") with sProc="clData:hAdd", the default message 
			//		and "Company" to be incorporated in this message

			bool bResult = true;
			//Not logging for speed optimisation
			//sProc is string="clLog::SetError"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_lErrorNumber, SELL_LOGLEVEL_DETAILS)
			string sString = "";
			string sMess = null;
			//if par_lErrorNumber<>0 then	'10103 and par_sProcedure="clData::LKAdd" then
			//	stop
			//END

			// Try



			if (par_lErrorNumber == 0)
			{
					lLastError = 0;
					sLastErrorProcedure = "";
					sLastErrorMessage = "";
					sPar1 = "";
					sPar2 = "";
					sPar3 = "";
					sPar4 = "";
					sPar5 = "";
					sPar6 = "";
					sPar7 = "";
					sPar8 = "";
					sPar9 = "";
					sPar10 = "";
					return true;
				}

				long lErrorNumber = par_lErrorNumber;
				string sProcedure = par_sProcedure;
				string sLocalMessage = par_sLocalMessage;
				string sPar1a = Convert.ToString(par_vPar1);
				string sPar2a = Convert.ToString(par_vPar2);
				string sPar3a = Convert.ToString(par_vPar3);
				string sPar4a = Convert.ToString(par_vPar4);
				string sPar5a = Convert.ToString(par_vPar5);
				string sPar6a = Convert.ToString(par_vPar6);
				string sPar7a = Convert.ToString(par_vPar7);
				string sPar8a = Convert.ToString(par_vPar8);
				string sPar9a = Convert.ToString(par_vPar9);
				string sPar10a = Convert.ToString(par_vPar10);

				string sLastmessage = "";
				//Set the error number in memory, clean the error message

				//Try to find/prepare the error message
				//Set the error message in memory
				if (sLocalMessage.Trim(' ') != "")
				{
					sLastmessage = goTR.MessComplete(sLocalMessage, sPar1a, sPar2a, sPar3a, sPar4a, sPar5a, sPar6a, sPar7a, sPar8a, sPar9a, sPar10a);

				}
				else
				{
					//We have to read metadata to find out the default message, if it exists
					sMess = GetErrorMessage(lErrorNumber); //Ex pageread
					sLastmessage = goTR.MessComplete(sMess, sPar1a, sPar2a, sPar3a, sPar4a, sPar5a, sPar6a, sPar7a, sPar8a, sPar9a, sPar10a);

					bResult = false;
				}

				lLastError = lErrorNumber;
				sLastErrorProcedure = sProcedure;
				sLastErrorMessage = sLastmessage;
				sPar1 = sPar1a;
				sPar2 = sPar2a;
				sPar3 = sPar3a;
				sPar4 = sPar4a;
				sPar5 = sPar5a;
				sPar6 = sPar6a;
				sPar7 = sPar7a;
				sPar8 = sPar8a;
				sPar9 = sPar9a;
				sPar10 = sPar10a;




				string sLogString = "Proceedure: " + sProcedure + "\r\n" + "Number: " + lLastError + "\r\n" + "Message: " + sLastmessage + "\r\n" + "\r\n";

				if (goErr.exLastError == null)
				{

				}
				else
				{
					sLogString = sLogString + "Exception info: " + "\r\n" + goErr.exLastError.ToString() + "\r\n" + goErr.exLastError.StackTrace.ToString();
				}


				if (bSilent)
				{
					//WriteToEventLog(sLogString, EventLogEntryType.Warning, lErrorNumber)
					Log(sProcedure, sLogString, 1, false, false, 1);
				}
				else
				{
					WriteToEventLog(sLogString, EventLogEntryType.Error, (int)lErrorNumber);
					Log(sProcedure, sLogString, 1, false, false, 2);
				}

				if (bSilent != true)
				{

					//==> We need to test to environment, i.e. web, web service, local exe, etc. and act accordingly
					// For web we will redirect to error page

					if (goP.sRunMode == "Webservice")
					{
						HttpContext.Current.Response.Redirect("~/Pages/Err1.aspx", true);
					}
					else if (goP.sRunMode == "Mobileservice")
					{
						//do nothing
					}
					else
					{
						HttpContext.Current.Session[clSettings.GetHostName() + "_ErrorMessage"] = sLogString;

						var context = new RequestContext(new HttpContextWrapper(System.Web.HttpContext.Current), new RouteData());
						var urlHelper = new UrlHelper(context);
					//System.Web.HttpContext.Current.Response.Redirect(urlHelper.Action("LogError1", "ErrorLog", New With {Key .sErrorLogs = sLogString}, System.Web.HttpContext.Current.Request.Url.Scheme), True) '28032019 No longer need this as we are using elmah redirect..J

					// Dim _url As string = urlHelper.Action("ActionName", "ControllerName", New {sErrorLog=sLogString}, Request.Url.Scheme)
					// HttpContext.Current.RewritePath(HttpContext.Current.Request.ApplicationPath + "ErrorLog/LogError", "pathinfo", "sErrorLog=" & sLogString & "")
					//HttpContext.Current.Response.Redirect("~/Pages/Err1.aspx", True)
				}


				}






			//Catch ex As Exception

			//    WriteToEventLog("Exception in clLog.SetError: " & ex.Message, EventLogEntryType.Error)

			//End Try






			return bResult;

		}
		public void SetErrorMsg(string par_sMessage)
		{
			//CS OK

			//Set the error message
			sErrorMsg = par_sMessage;
		}

		public bool SetWarning(long par_lErrorNumber = 0, string par_sProcedure = "", string par_sLocalMessage = "", object par_vPar1 = null, object par_vPar2 = null, object par_vPar3 = null, object par_vPar4 = null, object par_vPar5 = null, object par_vPar6 = null, object par_vPar7 = null, object par_vPar8 = null, object par_vPar9 = null, object par_vPar10 = null)
		{
			//MI 10/1/07 Made the same as clError.SetWarning.
			//PURPOSE:
			//		Log an error in the log file but without reporting an error to the calling function.
			//PARAMETERS:
			//		par_lErrorNumber:		Number of the error (metadat GLOBAL, ERR_xxx) where xxx=number
			//								0=no error
			//		par_sProcedure : 		Procedure /method/code in which the error occurred
			//		par_sLocalMessage:		Local message that will replace the default message we should 
			//								find in metadata... Use this when you want to override the default 
			//								message by prepared for the calling process...
			//		par_vPar1..par_vPar10: optional values that will replace in the message the [1]..[10]
			//RETURNS:
			//		True if everything OK, false if a problem occurred (message number not found, 
			//		invalid parameters...), in that case, the Procedure  will at least set the error 
			//		number and a default message saying "Contact technical support"
			//HOW IT WORKS:
			//		In case an error is really non critical, we use this method instead of doing a seterror(errNumber) 
			//		followed by a setError(0).
			//		The calling process will not be aware that an error occurred, it will just be recorded in XL file.
			//EXAMPLE:
			//		goLog:SetWarning(10000, sProc, "", "Company") with sProc="clData:hAdd", the default message 
			//		and "Company" to be incorporated in this message

			//Dim bResult As Boolean = False

			//*** MI 10/1/07 commented the following section and added a call to goLog.SetError(), 
			//copying what is done in clError.SetWarning.
			//Dim sProc As String = "clLog::SetWarning"
			//'if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_lErrorNumber, SELL_LOGLEVEL_DETAILS)
			//Dim sString As String = ""
			//Dim sMess As String
			//Dim SMessage As String = ""
			//'Try to find/prepare the error message
			//If Trim(par_sLocalMessage) <> "" Then
			//    sMess = goTR.MessComplete(par_sLocalMessage, par_vPar1, _
			//               par_vPar2, par_vPar3, par_vPar4, _
			//               par_vPar5, par_vPar6, par_vPar7, _
			//               par_vPar8, par_vPar9, par_vPar10)

			//Else
			//    'We have to read metadata to find out the default message, if it exists
			//    '	if goP:bMetaAvailable then
			//    sString = GetErrorMessage(par_lErrorNumber)   'goMeta:PageRead("GLOBAL", "ERR_"+numtostring(:lLastError,"05d"), "")
			//    sMess = goTR.StrRead(sString, "TXT", "")
			//    '	else
			//    '		sMess=""
			//    '	end

			//    sMess = goTR.MessComplete(sMess, par_vPar1, par_vPar2, par_vPar3, par_vPar4, _
			//             par_vPar5, par_vPar6, par_vPar7, _
			//             par_vPar8, par_vPar9, par_vPar10)

			//    bResult = False
			//End If

			//*** MI 10/1/07 Added
			goLog.SetError(par_lErrorNumber, par_sProcedure, par_sLocalMessage, par_vPar1, par_vPar2, par_vPar3, par_vPar4, par_vPar5, par_vPar6, par_vPar7, par_vPar8, par_vPar9, par_vPar10, true);

			//--------- OLD CODE ----------
			//Log the error in clLog
			//CS: commenting here to end for now until have numtostring and other functions
			//dim sWrkString as string="[W"+numtostring(par_lErrorNumber,"05d") & "]" & _									'Error number
			//						 vbtab & goP.GetCurrentUserAbbrev() & numtostring(goP.GetCurrentUAID(),"05d") & _		'Session
			//        vbTAB & par_sProcedure & vbTAB                                                  'Procedure 

			//        Dim sDateString As String = Format(goTr.NowUTC(), "yyyyMMdd") 'DateSys()
			//        Dim sTimeString As String = Format(goTr.NowUTC(), "HHmmssff") 'TimeSys()


			//        sWrkString = sWrkString & sDateString & " "
			//        sWrkString = sWrkString & sTimeString & ":" & vbTab

			//        'Message (automatically removes TAB and RC)
			//sWrkString=sWrkString & goTr.Replace(left(sMess,1000), vbtab, " - ") & vbtab & numToString(clLogObj.lLevel) & vbtab& clProject.sRunMode
			//        sWrkstring = goTr.Replace(sWrkString, vbCrLf, " - ")

			//        If bTrace Then goP.SellTrace(goP.gsUserID, goTr.Replace(sWrkString, vbTab, " - "))


			//        Dim bFileClosed As Boolean = False
			//        If lFileHandle <= 0 Then
			//            bFileClosed = True
			//            OpenLogFile()
			//        End If

			//        fpositionne(lFileHandle, 0, fpfin)
			//        'And write in it
			//dim lResult as integer=fwrite(:lFileHandle,sWrkString & vbcrlf)
			//        If lResult = -1 Then
			//            Beep()
			//	info(goTr:MessComplete(MessTraduit(5001),:sLogFileName))   '5001:Impossible to write in the log file [1...
			//            bResult = False
			//        End If
			//        ManageLogSize()

			//        If bFileClosed Then
			//            CloseLogFile()
			//        End If

			//Return bResult
// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public int GetLogLevel()
		{
			string sProc = "clLog::GetLogLevel";
			//PURPOSE:
			//       Returns current log level for the current user.
			//ALSO SEE:
			//       SetLogLevel

			return this.siLogLevel;

		}



		public void SetLogLevel(short par_siLevel, bool par_bDontWrite = false)
		{
			//MI 3/3/09 Added deleting POP pages for non-XX products.

			//PURPOSE:
			//		Set the current Log level to the parameter
			//PARAMETERS:
			//		par_siLevel:	Level to use
			//		par_bDontWrite:	IF true, information is not written to POP 
			//RETURNS:
			//		Nothing
			//HOW IT WORKS:
			//		Levels available:
			//		0 - Off. Error messages are still logged.
			//		1 - BASIC Always ON, only Entry and Exit of the main modules are logged (and errors, of course)
			//		2 - DETAILS Details: main functionalities of each module are also logged
			//		3 - DEBUG: Everything is logged
			//EXAMPLE:
			//		goLog:SetLogLevel(3) set the maximum log level (debug)

			DataTable dtProducts = null;
			int i = 0;
			string sProduct = null;

			this.siLogLevel = par_siLevel;

			if (!par_bDontWrite)
			{
				System.Data.SqlClient.SqlConnection tempVar = null;
				goMeta.LineWrite(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "LOGLEVEL", par_siLevel.ToString(), ref tempVar, "", "XX");
				//Delete per-product POP pages in case they are found. Note 3/3/09:
				//During the transition to product support, some portions of POP MD
				//was written (erroneously) under SA product. This made those properties
				//unchangable because they were read from SA, but written to XX. We still
				//can't be certain that some facilities won't write POP MD under SA.
				//That's why we delete all such pages.
				dtProducts = goP.GetProductList();
				for (i = 1; i <= dtProducts.Rows.Count; i++)
				{
					sProduct = dtProducts.Rows[i - 1]["TXT_ProductCode"].ToString();
					if (sProduct != "XX")
					{
						goMeta.PageDelete(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", sProduct);
					}
				}
			}

		}

		public clLog()
		{

		}
	}

}
