﻿CREATE TABLE [dbo].[TD_Related_AC] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_ToDo_Related_Activity_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_AC] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_Related_AC] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_AC_Related_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_Related_AC] FOREIGN KEY ([GID_AC]) REFERENCES [dbo].[AC] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_Related_AC] NOCHECK CONSTRAINT [LNK_AC_Related_TD];


GO
ALTER TABLE [dbo].[TD_Related_AC] NOCHECK CONSTRAINT [LNK_TD_Related_AC];


GO
CREATE CLUSTERED INDEX [IX_TD_Related_AC]
    ON [dbo].[TD_Related_AC]([GID_AC] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_AC_Related_TD]
    ON [dbo].[TD_Related_AC]([GID_TD] ASC);

