﻿CREATE TABLE [dbo].[ZC] (
    [GID_ID]             UNIQUEIDENTIFIER CONSTRAINT [DF_ZC_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),(0))+CONVERT([binary](2),'ZC',(0)))+CONVERT([binary](6),getutcdate(),(0)),(0))) ROWGUIDCOL NOT NULL,
    [BI__ID]             BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]           NVARCHAR (80)    NULL,
    [DTT_CreationTime]   DATETIME         CONSTRAINT [DF_ZC_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]       TINYINT          NULL,
    [TXT_ModBy]          VARCHAR (4)      NULL,
    [DTT_ModTime]        DATETIME         CONSTRAINT [DF_ZC_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_ZipCodeName]    NVARCHAR (50)    NULL,
    [MMO_ImportData]     NTEXT            NULL,
    [SI__ShareState]     TINYINT          CONSTRAINT [DF_ZC_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]   UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]       VARCHAR (50)     NULL,
    [TXT_ExternalID]     NVARCHAR (80)    NULL,
    [TXT_ExternalSource] VARCHAR (10)     NULL,
    [TXT_ImpJobID]       VARCHAR (20)     NULL,
    CONSTRAINT [PK_ZC] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_ZC_TXT_ImportID]
    ON [dbo].[ZC]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_ZC_Name]
    ON [dbo].[ZC]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_ZC_CreationTime]
    ON [dbo].[ZC]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_ZC_BI__ID]
    ON [dbo].[ZC]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_ZC_ZipCodeName]
    ON [dbo].[ZC]([TXT_ZipCodeName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_ZC_ModDateTime]
    ON [dbo].[ZC]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_ZC_CreatedBy_US]
    ON [dbo].[ZC]([GID_CreatedBy_US] ASC);

