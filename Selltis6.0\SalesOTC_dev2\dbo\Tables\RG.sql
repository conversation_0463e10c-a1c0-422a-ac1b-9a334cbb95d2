﻿CREATE TABLE [dbo].[RG] (
    [GID_ID]             UNIQUEIDENTIFIER CONSTRAINT [DF_RG_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'RG',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]             BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]           NVARCHAR (80)    NULL,
    [DTT_CreationTime]   DATETIME         CONSTRAINT [DF_RG_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]       TINYINT          NULL,
    [TXT_ModBy]          VARCHAR (4)      NULL,
    [DTT_ModTime]        DATETIME         CONSTRAINT [DF_RG_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_RegionName]     NVARCHAR (50)    NULL,
    [MMO_ImportData]     NTEXT            NULL,
    [SI__ShareState]     TINYINT          CONSTRAINT [DF_RG_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]   UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]       VARCHAR (50)     NULL,
    [TXT_ExternalID]     NVARCHAR (80)    NULL,
    [TXT_ExternalSource] VARCHAR (10)     NULL,
    [TXT_ImpJobID]       VARCHAR (20)     NULL,
    [TXT_RegionData]     NVARCHAR (300)   NULL,
    CONSTRAINT [PK_RG] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_RG_RegionName]
    ON [dbo].[RG]([TXT_RegionName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RG_CreatedBy_US]
    ON [dbo].[RG]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RG_ModDateTime]
    ON [dbo].[RG]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RG_Name]
    ON [dbo].[RG]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RG_CreationTime]
    ON [dbo].[RG]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_RG_BI__ID]
    ON [dbo].[RG]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RG_TXT_ImportID]
    ON [dbo].[RG]([TXT_ImportID] ASC);

