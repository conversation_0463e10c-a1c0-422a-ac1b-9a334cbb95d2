﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="Selltis.Custom.HTE.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <applicationSettings>
        <Selltis.Custom.HTE.Properties.Settings>
            <setting name="Selltis_Custom_HTE_com_selltis_hyspeco_WebService_RowSet" serializeAs="String">
                <value>https://hyspeco.selltis.com/webservice/rowset.asmx</value>
            </setting>
        </Selltis.Custom.HTE.Properties.Settings>
    </applicationSettings>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" /></startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.1.0" newVersion="13.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="MailBee.NET" publicKeyToken="cd85b70fb26f9fc1" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-11.2.0.590" newVersion="11.2.0.590" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
