﻿CREATE TABLE [dbo].[ET] (
    [GID_ID]                UNIQUEIDENTIFIER CONSTRAINT [DF_ET_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'ET',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]              NVARCHAR (80)    NULL,
    [DTT_CreationTime]      DATETIME         CONSTRAINT [DF_ET_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]          TINYINT          NULL,
    [TXT_ModBy]             VARCHAR (4)      NULL,
    [DTT_ModTime]           DATETIME         CONSTRAINT [DF_ET_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_EmailTemplateName] NVARCHAR (50)    NULL,
    [MMO_ImportData]        NTEXT            NULL,
    [SI__ShareState]        TINYINT          CONSTRAINT [DF_ET_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]      UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]          VARCHAR (50)     NULL,
    [TXT_ExternalID]        NVARCHAR (80)    NULL,
    [TXT_ExternalSource]    VARCHAR (10)     NULL,
    [TXT_ImpJobID]          VARCHAR (20)     NULL,
    [MMR_TEMPLATECONTENT]   NTEXT            NULL,
    CONSTRAINT [PK_ET] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_ET_EmailTemplateName]
    ON [dbo].[ET]([TXT_EmailTemplateName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_ET_CreatedBy_US]
    ON [dbo].[ET]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_ET_ModDateTime]
    ON [dbo].[ET]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_ET_Name]
    ON [dbo].[ET]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_ET_CreationTime]
    ON [dbo].[ET]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_ET_BI__ID]
    ON [dbo].[ET]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_ET_TXT_ImportID]
    ON [dbo].[ET]([TXT_ImportID] ASC);

