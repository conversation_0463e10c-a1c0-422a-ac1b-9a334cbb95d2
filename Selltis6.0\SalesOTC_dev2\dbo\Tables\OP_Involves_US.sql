﻿CREATE TABLE [dbo].[OP_Involves_US] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Opp_Involves_User_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_OP_Involves_User] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_OP_Involves_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_InvolvedIn_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[OP_Involves_US] NOCHECK CONSTRAINT [LNK_OP_Involves_US];


GO
ALTER TABLE [dbo].[OP_Involves_US] NOCHECK CONSTRAINT [LNK_US_InvolvedIn_OP];


GO
CREATE CLUSTERED INDEX [IX_US_InvolvedIn_OP]
    ON [dbo].[OP_Involves_US]([GID_OP] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_Involves_US]
    ON [dbo].[OP_Involves_US]([GID_US] ASC);

