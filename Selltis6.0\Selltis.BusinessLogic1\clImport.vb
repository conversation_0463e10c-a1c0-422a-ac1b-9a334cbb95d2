﻿Imports Microsoft.VisualBasic
Imports System.Web.Security
Imports System.Web

Public Class clImport

    Private dtSource As DataTable
    Private dtSourceUnique As DataTable
    Private bAllowSourceDupes As Boolean = False
    Private dtMap As DataTable
    Private sFileName As String
    Private sImpJobID As String
    Private sKeySource As String
    Private sKeyDest As String
    Private dtKeyDest As DataTable
    Private dtKeySource As DataTable
    Private iHighestBIID As Integer

    'From MI clDiaDeskMan example
    Public bBackFromSubdialog As Boolean
    Public sMessageBoxPurpose As String

    Dim goP As clProject
    Dim goMeta As clMetaData
    Dim goTR As clTransform
    Dim goData As clData
    Dim goErr As clError
    Dim goLog As clLog
    Dim goDef As clDefaults

#Region "Public Properties"
    Public Property SourceDataTable() As DataTable
        'Raw source data read by sending UI
        Get
            Return dtSource
        End Get
        Set(ByVal value As DataTable)
            dtSource = value
        End Set
    End Property
    Public Property SourceDataTableUnique() As DataTable
        'Raw source data after dupes merged - dupes identified by source key field(s)
        Get
            Return dtSourceUnique
        End Get
        Set(ByVal value As DataTable)
            dtSourceUnique = value
        End Set
    End Property
    Public Property MapTable() As DataTable
        'Datatable of mapped fileds and properties
        Get
            Return dtMap
        End Get
        Set(ByVal value As DataTable)
            dtMap = value
        End Set
    End Property
    Public Property FileName() As String
        'Name of the file to import to
        Get
            Return sFileName
        End Get
        Set(ByVal value As String)
            sFileName = value
        End Set
    End Property
    Public Property SourceKey() As String
        'String representing the source field(s) and properties that make up the source key
        Get
            Return sKeySource
        End Get
        Set(ByVal value As String)
            sKeySource = value
        End Set
    End Property
    Public Property SelltisKey() As String
        'String representing the Selltis field(s) and properties that make up the Selltis key
        Get
            Return sKeyDest
        End Get
        Set(ByVal value As String)
            sKeyDest = value
        End Set
    End Property
    Public Property ImportJobID() As String
        'Id of this import job
        Get
            Return sImpJobID
        End Get
        Set(ByVal value As String)
            sImpJobID = value
        End Set
    End Property
    Public Property HighestBIID() As Integer
        'BI__ID value needed for start value of record when processing sys_names
        Get
            Return iHighestBIID
        End Get
        Set(ByVal value As Integer)
            iHighestBIID = value
        End Set
    End Property
    Public Property AllowSourceDupes() As Boolean
        'Set to True to import all source records regardless of duplicate keys
        Get
            Return bAllowSourceDupes
        End Get
        Set(ByVal value As Boolean)
            bAllowSourceDupes = value
        End Set
    End Property
#End Region

    'Owner: PJ
    'MI 8/13/09 Resolved VS2008 warnings per PJ

    Public Sub New()

    End Sub

    Public Function Logon(ByVal sUser As String, ByVal sPassword As String) As Boolean

        'PURPOSE:
        '		Validate logon and establish session
        'PARAMETERS:
        '		sUser
        '       sPassword
        'RETURNS:
        '		True/False
        'AUTHOR: RH

        'Dim sb As Boolean
        'sb = Membership.ValidateUser(sUser, sPassword)
        Dim _memberShip As New clSelltisMembershipProviderNew()
        Dim sb As Boolean = _memberShip.ValidateUser(sUser, sPassword)

        If sb Then
            Dim Init As New clInit
            Dim sID As String = HttpContext.Current.Session.LCID
        End If

        Return sb

    End Function
    Public Function ValidateSession() As Boolean

        Try
            Dim oP As clProject = HttpContext.Current.Session("goP")
            If oP.gsUserID = "" Then

                HttpContext.Current.Session.Abandon()

                Return False
            Else
                Return True
            End If
        Catch ex As Exception
            HttpContext.Current.Session.Abandon()
            Return False
        End Try

    End Function

    Public Function Initialize(Optional ByVal sImpJobID As String = "") As Boolean

        'PURPOSE:
        '		Initialize SQL import-related objects
        'PARAMETERS:
        '		none
        'RETURNS:
        '		True/False
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        goP = HttpContext.Current.Session("goP")
        goTR = HttpContext.Current.Session("goTr")
        goMeta = HttpContext.Current.Session("goMeta")
        goData = HttpContext.Current.Session("goData")
        goErr = HttpContext.Current.Session("goErr")
        goLog = HttpContext.Current.Session("goLog")
        goDef = HttpContext.Current.Session("goDef")

        Dim dtLog As New DataTable

        Try
            'Create Log table. This will be returned to client
            dtLog.Columns.Add("Time")
            dtLog.Columns.Add("Level")
            dtLog.Columns.Add("Process")
            dtLog.Columns.Add("Status")

            goP.SetVar("ImportSQL_dtLog", dtLog)

            Return True

        Catch ex As Exception

            'LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
            Return Nothing

        End Try

    End Function

    Private Function CreateKeyDatatable(ByVal sKey As String) As DataTable

        'PURPOSE:		Create datatable of key params to be stored in session and used throughout import process
        '               Import utility has text fields for source and selltis keys. Values are strings that can include
        '               field codes for more than one field.

        'PARAMETERS:    sKey: String containing key params. Supports
        '                       FLD=    Name of the field in the data table
        '                       LEN=    Length of the field value
        '                       NOR=    Normalized (True/False)
        '                       PAD=    -- not supported. multiple params --

        'CALLED FROM:   InitializeLocalVars()   
        '		
        'RETURNS:       DataTable of field(s) and parameters
        '		
        'AUTHOR: PJ
        'USED By: Import Utility 2.0

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Dim dtKey As New DataTable
        Dim dc As DataColumn
        Dim dr As DataRow
        Dim sCol As String = "FLD,LEN,NOR"
        Dim aCol() As String = Split(sCol, ",")
        Dim iStart As Integer = 1
        Dim iEnd As Integer
        Dim sFieldCode As String = ""
        Dim aFieldCode() As String
        Dim sFieldCodeName As String
        Dim sLength As String
        Dim sNormalize As String

        Try
            For i As Integer = 0 To aCol.GetUpperBound(0)
                dc = New DataColumn
                dc.ColumnName = aCol(i)
                dtKey.Columns.Add(dc)
            Next

            Do While iStart > 0
                dr = dtKey.NewRow
                iStart = InStr(sKey, "(%")
                If iStart > 0 Then
                    iEnd = InStr(sKey, "%)") + 1
                    If iEnd > 0 Then
                        sFieldCode = Mid(sKey, iStart, iEnd - iStart + 1)
                        aFieldCode = Split(sFieldCode, "|")
                        If UCase(aFieldCode(0)).Contains("FLD=") Then
                            sFieldCodeName = Replace(aFieldCode(0), "FLD=", "")
                        Else
                            sFieldCodeName = sFieldCode    'This for backward compat. NO! for other values (%Zip%)
                            'Exit For
                        End If

                        sFieldCodeName = RemoveFieldCodeTags(sFieldCodeName)
                        dr("FLD") = sFieldCodeName

                        If aFieldCode.GetUpperBound(0) > 0 Then
                            If UCase(aFieldCode(1)).Contains("LEN=") Then
                                sLength = Replace(aFieldCode(1), "LEN=", "")
                                sLength = RemoveFieldCodeTags(sLength)
                                dr("LEN") = sLength
                            End If
                            If aFieldCode.GetUpperBound(0) > 1 Then
                                If UCase(aFieldCode(2)).Contains("NOR=") Then
                                    sNormalize = Replace(aFieldCode(2), "NOR=", "")
                                    sNormalize = RemoveFieldCodeTags(sNormalize)
                                    dr("NOR") = sNormalize
                                End If
                            End If
                        End If

                        dtKey.Rows.Add(dr)
                    End If
                End If
                sKey = Replace(sKey, sFieldCode, "")    'Remove this field code from key string in order to find next field code
            Loop

            Return dtKey

        Catch ex As Exception

            'LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
            Return Nothing

        End Try

    End Function

    Public Function Cleanup(ByVal sIMPJobID As String) As Boolean

        'PURPOSE:
        '		Clear session variables
        'PARAMETERS:
        '		sIMPJobID: not used. No need to make session var's job specific since user can only run one job at a time(?)
        'RETURNS:
        '		
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Try
            Dim goP As clProject = HttpContext.Current.Session("goP")
            goP.SetVar("ImportSQL_dtSource", Nothing)
            goP.SetVar("ImportSQL_dtSourceDupes", Nothing)
            goP.SetVar("ImportSQL_dtMap", Nothing)
            goP.SetVar("ImportSQL_dtLST", Nothing)
            goP.SetVar("ImportSQL_dtTransformed", Nothing)
            goP.SetVar("ImportSQL_dtTransformedNew", Nothing)
            goP.SetVar("ImportSQL_dtTransformedUpd", Nothing)

        Catch ex As Exception

            'LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
            Return False

        End Try

        Return True

    End Function

    Public Function GetLog(ByVal sIMPJobID As String) As DataTable

        'PURPOSE:
        '		Return log file to user in a datatable for local display
        '       Called by consumer as needed
        'PARAMETERS:
        '		none
        'RETURNS:
        '		Datatable, instantiated in Initialize above
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Try
            Dim goP As clProject = HttpContext.Current.Session("goP")

            Dim dtLog As DataTable = goP.GetVar("ImportSQL_dtLog")

            dtLog.TableName = "dtLog"

            Return dtLog

        Catch ex As Exception

            'LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
            Return Nothing

        End Try


    End Function

    Public Function GetTables() As DataTable

        'PURPOSE:
        '		Get list of tables for this database.
        'PARAMETERS:
        '		nihil
        'RETURNS:
        '		delimited string
        'AUTHOR: PJ
        'USED By: Import Utilty 2.0

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Dim dt As New DataTable
        Dim settings As String = HttpContext.Current.Session("Connection")
        Dim sqlConnection1 As New Data.SqlClient.SqlConnection(settings)
        Dim cmd As New SqlClient.SqlCommand
        Dim reader As SqlClient.SqlDataReader

        Try

            sqlConnection1.Open()

            cmd.CommandText = "pGetTables"
            cmd.CommandType = CommandType.StoredProcedure
            cmd.Connection = sqlConnection1

            reader = cmd.ExecuteReader()

            If reader.HasRows Then

                dt.Load(reader)

            Else
                '==> raise error
            End If

            reader.Close()
            sqlConnection1.Close()
            'LoadTableData = True

            dt.TableName = "Tables"

            Return dt

        Catch ex As Exception

            'LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
            Return Nothing

        End Try


    End Function

    Public Function GetFields() As DataTable

        'PURPOSE:
        '		Get list of fields for this database.
        'PARAMETERS:
        '		nihil
        'RETURNS:
        '		delimited string
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Dim dt As New DataTable
        Dim settings As String = HttpContext.Current.Session("Connection")
        Dim sqlConnection1 As New Data.SqlClient.SqlConnection(settings)
        Dim cmd As New SqlClient.SqlCommand
        Dim reader As SqlClient.SqlDataReader

        Try

            sqlConnection1.Open()

            cmd.CommandText = "pGetFields"
            cmd.CommandType = CommandType.StoredProcedure
            cmd.Connection = sqlConnection1

            reader = cmd.ExecuteReader()

            If reader.HasRows Then

                dt.Load(reader)

            Else
                '==> raise error
            End If

            reader.Close()
            sqlConnection1.Close()
            'LoadTableData = True

            dt.TableName = "Fields"

            Return dt

        Catch ex As Exception

            'LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
            Return Nothing

        End Try


    End Function

    Public Function DedupeSourceData(ByVal par_dtSource As DataTable, _
                                     ByVal par_sFile As String, _
                                     ByVal par_dtMap As DataTable, _
                                     ByVal par_bUseNormalizedKey As Boolean, _
                                     ByVal par_sIMPJobID As String, _
                                     Optional ByVal par_sSourceKey As String = "") As Boolean

        'PURPOSE:
        '		Merge duplicates in the soruce datatable. Duplicates are identified by a key field 
        '       or combinatioin of fields.
        '       Only non-NULL values are merged
        'PARAMETERS:
        '		dtSource:           This is the datatable created by the consumer containing the raw source data
        '                           that has not been transformed yet.
        '       sFile:              SQL table name
        '       dtMap:              Field mapping, with column for 'Key' fields (True/False/Null)
        '       bUsenormalizedKey:  Normalized key values use 'NormalizeValue' function
        '       sIMPJobIDP:         Id of the import job, used for filtering field TXT_ImpJobID. Created by import utils
        '       sSourceKey:         String value of source key containing one or more fields with LEN, NOR, ..
        'RETURNS:
        '		True/False. De-duped datatable is stored in session variable, replaceing par_dtSource
        'AUTHOR: PJ

        'PJ: Add param to pass back the de-duped datatable. May want to take results back to source.

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Dim sID As String = HttpContext.Current.Session.LCID

        Dim goP As clProject = HttpContext.Current.Session("goP")
        Dim goTR As clTransform = HttpContext.Current.Session("goTR")

        Dim dtSourceDupes As New DataTable
        Dim dcPrimaryKey As DataColumn
        Dim iKeyCount As Integer = -1
        Dim sKeyExpression As String = ""
        Dim dr As DataRow = Nothing
        Dim dvMergeTo As DataView
        Dim iMergeCount As Integer = 0
        Dim sExpression As String = ""
        Dim sFilter As String = ""
        Dim sFilterPart As String = ""
        Dim sColName As String = ""
        Dim aKeys(0) As DataColumn
        Dim sKeyValue As String = ""

        Try

            '--- Create table for source keys with columns for FLD, LEN, NOR
            'Compat with Import applet: Check if dtMap has 'Key' column and if value is 1 or True
            '   Key|SQLFieldName|Expression
            If sKeySource = "" Then
                If Not par_dtMap Is Nothing Then
                    If par_dtMap.Columns.Contains("Key") Then
                        For Each row As DataRow In par_dtMap.Rows
                            If Not row("Key").ToString = "System.DBNull" Then
                                If row("Key").ToString = "True" Or row("Key").ToString = "1" Then
                                    sKeySource = row("Expression")
                                End If
                            End If
                        Next
                    End If
                End If
            End If

            If sKeySource <> "" Then
                dtKeySource = CreateKeyDatatable(sKeySource)
                If dtKeySource.Rows.Count > 0 Then
                    iKeyCount = dtKeySource.Rows.Count
                End If
            Else
                LogLine(0, "Dedupe Source Data: Key not set.", "Warning")
                Return True
            End If

            '--- Add UniqueKey column to dtSource
            If Not par_dtSource.Columns.Contains("UniqueKey") Then
                par_dtSource.Columns.Add("UniqueKey")
            End If

            '--- Populate UniqueKey value in each row of dtSource
            For i As Integer = 0 To par_dtSource.Rows.Count - 1
                dr = par_dtSource.Rows(i)
                If sKeySource <> "" Then
                    sKeyValue = GetFieldValFromDataTable(par_dtSource, sKeySource, i)
                End If
                dr("UniqueKey") = sKeyValue
            Next

            '--- Create dtSourceUnique and set UniqueKey as key column
            dtSourceUnique = par_dtSource.Clone
            If Not bAllowSourceDupes Then
                If iKeyCount > 0 Then
                    'NEW way
                    dcPrimaryKey = dtSourceUnique.Columns("UniqueKey")
                    ReDim aKeys(0)
                    aKeys(0) = dcPrimaryKey
                    dtSourceUnique.PrimaryKey = aKeys
                End If
            End If

            '--- Copy rows from dtSource to dtSourceUnique. Catch errors which are likely due to dupe key
            For i As Integer = 0 To par_dtSource.Rows.Count - 1
                Try
                    dr = par_dtSource.Rows(i)
                    'Replace nulls in primary keys with blank string, "". Otherwise ImportRow will fail since key col's can't be null
                    For z As Integer = 0 To par_dtSource.Columns.Count - 1
                        sColName = par_dtSource.Columns(z).ColumnName
                        'If oHashTable.Contains(sColName) Then
                        If sColName = "UniqueKey" Then
                            'For this primary key row:
                            If IsDBNull(dr.Item(sColName)) Then
                                dr.Item(sColName) = ""
                            End If
                            'If normalizing this key, copy normalized value to TXT_IDNormalized
                            If par_bUseNormalizedKey Then
                                dr.Item("TXT_IDNormalized") = dr.Item("TXT_IDNormalized") & NormalizeValue(dr.Item(sColName), "")
                            End If
                        End If
                    Next
                    dtSourceUnique.ImportRow(dr)

                Catch ex As Exception
                    'Row could not be imported into par_dtSourceUnique, probably due to key violation. Try updating
                    'Find row in par_dtSourceUnique
                    '2/10: Update this to allow multiple key fields, using aKeys
                    'Try
                    If iKeyCount > -1 Then
                            sFilter = ""
                            sFilterPart = ""
                            'Keys exist, so create filter string to be used in RowFilter
                            For j As Integer = 0 To aKeys.GetUpperBound(0)
                                If IsDBNull(dr.Item(aKeys(j).ColumnName)) Then  'this is the key's value
                                    'value is null
                                    Dim sTestName As String = aKeys(j).ColumnName.ToString
                                    Dim stype As String = par_dtSource.Columns(sTestName).DataType.ToString
                                    dr.Item(aKeys(j).ColumnName) = ""
                                End If

                                sFilterPart = aKeys(j).ColumnName & " = '" & goTR.PrepareForSQL(dr.Item(aKeys(j).ColumnName)) & "'"
                                If sFilter = "" Then
                                    sFilter = sFilterPart
                                Else
                                    sFilter += " AND " & sFilterPart
                                End If
                            Next

                            dtSourceUnique.DefaultView.RowFilter = sFilter
                            dvMergeTo = dtSourceUnique.DefaultView  'dvMerge to contains the record to merge TO

                            If dvMergeTo.Count > 0 Then
                                'update this row in the datatable, column by column. Look for non-blank/null values only
                                For j As Integer = 0 To par_dtSource.Columns.Count - 1
                                    'This is the merge: Last one wins, overwriting values in 0th row record, with non-blank/null values in ith row record
                                    If Not IsDBNull(par_dtSource.Rows(i).Item(j)) Then
                                        'Get datatype of column and check for null, blank

                                        Dim sType As String = par_dtSource.Columns(j).DataType.ToString

                                        If par_dtSource.Rows(i).Item(j).ToString <> "" Then
                                            dvMergeTo.Item(0).Item(j) = par_dtSource.Rows(i).Item(j).ToString
                                        End If
                                    End If
                                Next
                                'Log
                                iMergeCount += 1
                                LogLine(1, "Merged '" & sFilter, "OK")
                            End If

                        End If

                    'Catch ex1 As Exception
                    '    'Merge failed. Return error message to the log. 
                    '    LogLine(0, "Error adding/merging record " & i, "Failed")
                    'End Try

                End Try

            Next    'par_dtSource.Row

            'for easy inspection, sort by key fld
            'par_dtSourceUnique.DefaultView.Sort = aKeys.GetValue(0).ToString

            'STEP 4: Save datatables in session var's
            goP.SetVar("ImportSQL_par_dtSource", dtSourceUnique)
            goP.SetVar("ImportSQL_par_dtSourceDupes", dtSourceDupes)    'Not used in later process, but may want to return to user
            goP.SetVar("ImportSQL_par_dtMap", par_dtMap)

            LogLine(0, "Source Data: " & iMergeCount & " records merged", "OK")

        Catch ex As Exception

            'LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
            Return False

        End Try

        Return True

    End Function

    Public Function GetSourceDupes(ByVal sIMPJobID As String) As DataTable

        'PURPOSE:
        '		Return datatable of dupes to consumer
        '       Called by consumer as needed
        'PARAMETERS:
        '		sIMPJobID:  Id of the import job, used for filtering field TXT_ImpJobID. Created by import utils
        'RETURNS:
        '		Datatable of duplicate records
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Try
            Dim goP As clProject = HttpContext.Current.Session("goP")

            Dim dtSourceDupes As DataTable = goP.GetVar("ImportSQL_dtSourceDupes" & sIMPJobID)

            dtSourceDupes.TableName = "dtSourceDupes"

            Return dtSourceDupes

        Catch ex As Exception

            'LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
            Return Nothing

        End Try


    End Function

    Public Function TransformTable(ByVal par_sSQLTableName As String, _
                                   ByVal par_dtSourceUnique As DataTable, _
                                   ByVal par_dtMap As DataTable, _
                                   ByVal par_bUseNormalizedKey As Boolean, _
                                   ByVal par_bIgnoreTransformErrors As Boolean, _
                                   ByVal sIMPJobID As String, _
                                   Optional ByVal par_iOffsetMinutes As Integer = 0, _
                                   Optional ByVal par_bCreateMissingLinkedRecords As Boolean = False, _
                                   Optional ByVal par_bCreateMissingLSTValues As Boolean = False) As Boolean

        'PURPOSE:
        '		Validation: source datatable is inserted cell by cell into dtTransformed. dtTransformed column are sql data typed
        '       Validation fails in the following ways:
        '           CHK, ,"CUR", "DR_", "INT", "LI_", "BI_", "SI_", "SR_": Replaced with 0
        '           MLS: Lookup used with LST_ metadata. If not found, set to 0
        '           DTE: Replaced with Selltis empty date '1753-01-02 23:59:59.000'
        '           TXT, MMO: Length truncated
        '       Concatenation: Values from multiple source columns can be concatenated into one dtTransformed column
        '       Supported Functions: Specific VB-like functions are supported. Currenty: Propercase, FormatPhone
        'PARAMETERS:
        '       par_sSQLTableName:      2-char SQL table name
        '       par_dtSourceUnique:     Source datatable. Set to 'Nothing' if the datatable is created by DedupeSourceData() above
        '                                   Param is available here for calling this method first
        '       par_dtMap:              Map datatable. Columns are:
        '                                   Col1
        '                                   Col2...
        '       par_bUseNormalizedKey:  True if normalizing CO/CN
        '       par_bIgnoreTransformErrors: True to ignore transform errors and not log them. Increases performance
        '       sIMPJobID:              Id of the import job, used for filtering field TXT_ImpJobID. Created by import utils
        '       par_iOffsetMinutes:     Time offset for datetime fields. Timeless dates import as 00:00:00 GMT
        '       par_bCreateMissingLinkedRecords:    True to create linked records for selection type links
        '       par_bCreateMissingLSTValues:        True to create LST metadata

        'RETURNS:
        '		True/False. Transformed datatable is stored in session variable
        'AUTHOR: PJ
        'USED BY: Integrator APP

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        goP = HttpContext.Current.Session("goP")
        goData = HttpContext.Current.Session("goData")
        goTR = HttpContext.Current.Session("goTr")

        Dim sExpression As String = ""
        Dim sExpressionValue As String = ""
        Dim dtDestinationSchema As New DataTable
        Dim dtTransformed As New DataTable
        Dim dtTransformedUpd As New DataTable
        Dim dtTransformedNew As New DataTable
        Dim sDestFieldName As String = ""
        Dim colDestFieldExpression As New Collection
        Dim dcFromSchema As DataColumn
        Dim dcTransTable As DataColumn
        Dim dcTransTableNew As DataColumn
        Dim iError As Integer = 0
        Dim sError As String = ""
        Dim oTr As New clTransform
        Dim dtLST As New DataTable
        Dim bKeysAdded As Boolean = False

        Try

            'Get LST_ metadata and put in session var
            dtLST = GetLSTMetadata(par_sSQLTableName)
            goP.SetVar("ImportSQL_dtLST", dtLST)

            'If source data deduped first
            If par_dtSourceUnique Is Nothing Then par_dtSourceUnique = goP.GetVar("ImportSQL_par_dtSource")
            If par_dtSourceUnique Is Nothing Then
                LogLine(0, sProc & ": Unable to read source data", "Failed")
                Return False
            End If

            If par_dtMap Is Nothing Then par_dtMap = goP.GetVar("ImportSQL_par_dtMap")
            dtMap = par_dtMap
            If par_dtMap Is Nothing Then
                LogLine(0, sProc & ": Unable to read map data", "Failed")
                Return False
            End If

            'Get schema from destination table for copying column objects REDO this METHOD - gets top 1 record
            dtDestinationSchema = GetTableSchemaDatatable(par_sSQLTableName)
            If dtDestinationSchema.Columns.Count = 0 Then
                'Get schema failed to return datatable. Record in log
                LogLine(0, sProc & ": Get SQL Table Schema", "Failed")
                Return False
            Else
                'LogLine(0, "Get SQL Table Schema", "OK")

            End If

            ' ----------------- Create dtTransformed columns ------------------------
            'Read destination mapping from map table and load those columns from schema table into trans tables
            Try
                For k As Integer = 0 To par_dtMap.Rows.Count - 1
                    If Not IsDBNull(par_dtMap.Rows(k).Item("Expression")) Then
                        sExpression = par_dtMap.Rows(k).Item("Expression")
                        If sExpression <> "" Then
                            'Field is mapped. add schema column to trans table
                            sDestFieldName = par_dtMap.Rows(k).Item("SQLFieldName")
                            'V2: If sDestFieldName contains (%..%) then this is the key row in the map and should be ignored
                            If Not sDestFieldName.Contains("(%") Then
                                'V2: If this is a link, par_dtMap value is 'LNK_TeamLeader_US', but dtDestinationSchema value is
                                '   'TXT_LNK_TeamLeader_US'. Need to get link type here and substitute temp field
                                If Left(sDestFieldName, 4) = "LNK_" Then
                                    'Add temp link fields to SQL table and dtDestinationSchema
                                    Dim oSchema As New clSchema
                                    Dim aLinks As New clArray
                                    aLinks.Add(sDestFieldName)
                                    If Not oSchema.SchemaAddFieldsToTable(par_sSQLTableName, "", aLinks, True) Then
                                        LogLine(0, sProc & ": Invalid Destination field name, '" & sDestFieldName & "'. Import terminated.", "Failed")
                                    End If

                                    Dim sLinkType As String = Split(goData.LK_GetType(par_sSQLTableName, sDestFieldName), vbTab)(4)
                                    Select Case sLinkType
                                        Case "N1"
                                            sDestFieldName = "TXT_" & sDestFieldName
                                        Case "NN"
                                            sDestFieldName = "MMO_" & sDestFieldName
                                        Case Else
                                    End Select
                                    'If temp link field, Reload schema table (top 1 rec)
                                    dtDestinationSchema = GetTableSchemaDatatable(par_sSQLTableName)
                                End If
                                'Add dest field name and expression to collection for use below
                                colDestFieldExpression.Add(sExpression, sDestFieldName)
                                'Get column schema
                                dcFromSchema = dtDestinationSchema.Columns(sDestFieldName)
                                If IsNothing(dcFromSchema) Then
                                    'MsgBox("Invalid Destination field name, '" & sDestFieldName & "'")  'Put in status window
                                    LogLine(0, sProc & vbCrLf & "Invalid Destination field name, '" & sDestFieldName & "'. Import terminated.", "Failed")
                                    Return False
                                End If
                                'Fix: use clone
                                dcTransTable = New DataColumn
                                With dcTransTable
                                    .ColumnName = dcFromSchema.ColumnName
                                    .DataType = dcFromSchema.DataType
                                    .MaxLength = dcFromSchema.MaxLength
                                    .AllowDBNull = True
                                End With
                                dtTransformed.Columns.Add(dcTransTable)

                            End If
                        End If
                    End If
                Next

                'Add UniqueKey column to dtTransformed. This column has to be added because it is not
                '   part of the map and does not get created in the above loop
                dcTransTableNew = New DataColumn
                With dcTransTableNew
                    .ColumnName = "UniqueKey"
                    .DataType = System.Type.GetType("System.String")
                    .MaxLength = 200
                    .AllowDBNull = True
                End With
                dtTransformed.Columns.Add(dcTransTableNew)

            Catch ex As Exception
                'LogLine(0, sProc & ": Create transform table columns", "Failed")
                Return False

            End Try
            '-------------------------------------------------------------------------------

            'If using Normalized keys, add TXT_IDNormalized to each table
            If par_bUseNormalizedKey Then
                dtTransformed.Columns.Add("TXT_IDNormalized")
            End If

            'Add ImportJobID column. If this is mapped, cannot add here because it's in the table already
            If Not dtTransformed.Columns.Contains("TXT_ImpJobID") Then
                dtTransformed.Columns.Add("TXT_ImpJobID")
            End If
            '== Empty transform tables are now created with all mapped columns ==

            'Add rows to transform table
            For iRow As Integer = 0 To par_dtSourceUnique.Rows.Count - 1
                dtTransformed.Rows.Add()
            Next

            '--------------- For each column in transfer table, fill row by row ----------------------
            For iCol As Integer = 0 To dtTransformed.Columns.Count - 1

                '4/18: Get dt of linked file for LNK_, with field values that are link key values.
                Dim sFieldName As String = dtTransformed.Columns(iCol).ColumnName
                Dim sLinkField As String = ""
                Dim sLinkedFile As String = ""
                Dim sLinkKeyField As String = "" 'get from map!
                Dim dtLinkedKeys As DataTable = Nothing
                Dim dtLinkedKeysAdd As DataTable = Nothing

                If sFieldName.Contains("LNK_") Then
                    'What is link name (not temp field name)
                    If sFieldName.StartsWith("TXT_LNK_") Or sFieldName.StartsWith("MMO_LNK_") Then
                        sLinkField = goTR.FromTo(sFieldName, 5)
                    End If
                    'Get link file
                    sLinkedFile = Right(sLinkField, 2)
                    'Get link key field
                    dtMap.DefaultView.RowFilter = "SQLFieldName='" & sLinkField & "'"
                    If dtMap.DefaultView.Count > 0 Then
                        If Not IsDBNull(dtMap.DefaultView.Item(0).Item("LinkKeyField")) Then
                            sLinkKeyField = dtMap.DefaultView.Item(0).Item("LinkKeyField")
                        Else
                            sLinkKeyField = "GID_ID"
                        End If
                        'Get dataset of existing link key values
                        Dim sSQL As String = "SELECT " & sLinkKeyField & ", SYS_NAME FROM " & sLinkedFile
                        dtLinkedKeys = RunSQLSelect(sSQL)
                        'Create Add table for linked key values
                        '   need to have dt with column name and type same as dtLinkedKeys
                        If Not dtLinkedKeys Is Nothing Then
                            dtLinkedKeysAdd = dtLinkedKeys.Clone
                        End If
                    End If
                End If

                iError = 0
                sError = ""
                For iRow As Integer = 0 To par_dtSourceUnique.Rows.Count - 1
                    sExpression = ""
                    sExpressionValue = ""

                    Try
                        'If using Normalized keys
                        If dtTransformed.Columns(iCol).ColumnName = "TXT_IDNormalized" Then
                            sExpressionValue = par_dtSourceUnique.Rows(iRow).Item("TXT_IDNormalized")
                            dtTransformed.Rows(iRow).Item(dtTransformed.Columns(iCol).ColumnName) = sExpressionValue
                        Else

                            'For this column, iCol, what is sExpression??
                            'If field is ImportJobID then fill with constant value
                            If dtTransformed.Columns(iCol).ColumnName = "TXT_ImpJobID" Then
                                sExpressionValue = sIMPJobID
                                dtTransformed.Rows(iRow).Item(dtTransformed.Columns(iCol).ColumnName) = sExpressionValue

                            Else
                                'Special case for UniqueKey column (not part of source or dest schema)
                                If dtTransformed.Columns(iCol).ColumnName = "UniqueKey" Then
                                    sExpression = "(%UniqueKey%)"
                                Else
                                    sExpression = colDestFieldExpression(dtTransformed.Columns(iCol).ColumnName)
                                End If

                                'Translate sExpression into string
                                'debug
                                If dtTransformed.Columns(iCol).ColumnName.Contains("UniqueKey") Then
                                    Dim s As String = ""
                                End If

                                sExpressionValue = GetFieldValFromDataTable(par_dtSourceUnique, sExpression, iRow, par_sSQLTableName, dtTransformed.Columns(iCol).ColumnName, 480, , par_bCreateMissingLSTValues)

                                'Handle nulls for some data types
                                If sExpressionValue = "" Or sExpressionValue Is Nothing Then
                                    If Microsoft.VisualBasic.Left(dtTransformed.Columns(iCol).ColumnName, 3) <> "TXT" Then  'debug, not needed
                                        Select Case Microsoft.VisualBasic.Left(dtTransformed.Columns(iCol).ColumnName, 3)
                                            Case "CHK", "CMB", "CUR", "DR_", "INT", "LI_", "BI_", "MLS", "SEL", "SI_", "SR_"
                                                sExpressionValue = 0
                                            Case "DTE", "DTT", "TME"
                                                sExpressionValue = "1753-01-02 23:59:59.000"
                                        End Select
                                    End If
                                Else
                                    If Len(sExpressionValue) > 4 Then
                                        If Mid(sExpressionValue, 1, 5) = "Error" Then
                                            iError += 1
                                            If iError < 20 Then
                                                'PJ ok to remove this? 1/28/09
                                                'LogLine(1, sProc & vbCrLf & "EvalExpression returned '" & sExpressionValue & "'", "Warning")
                                            End If
                                        End If
                                    End If

                                    sExpressionValue = goTR.PrepareForSQL(sExpressionValue)
                                    dtTransformed.Rows(iRow).Item(dtTransformed.Columns(iCol).ColumnName) = sExpressionValue    'EvalExpression(sExpression, iRow)

                                    If dtTransformed.Columns(iCol).ColumnName.Contains("_LNK_") Then
                                        'LNK_: Does record exist in linked table? If not, create it
                                        '   filter dtLinkedKeys
                                        If par_bCreateMissingLinkedRecords Then
                                            'Parse sExpression into array
                                            Dim aExpressionValue() As String = Split(sExpressionValue, vbCrLf)
                                            For i As Integer = 0 To aExpressionValue.GetUpperBound(0)
                                                dtLinkedKeys.DefaultView.RowFilter = sLinkKeyField & "='" & aExpressionValue(i) & "'"
                                                If dtLinkedKeys.DefaultView.Count = 0 Then
                                                    'Add value to dtLinkedKeysAdd if it doesn't exist in add table
                                                    dtLinkedKeysAdd.DefaultView.RowFilter = sLinkKeyField & "='" & aExpressionValue(i) & "'"
                                                    If dtLinkedKeysAdd.DefaultView.Count = 0 Then
                                                        Dim row As DataRow = dtLinkedKeysAdd.NewRow
                                                        row(sLinkKeyField) = aExpressionValue(i)
                                                        row("SYS_NAME") = aExpressionValue(i)
                                                        dtLinkedKeysAdd.Rows.Add(row)
                                                        bKeysAdded = True
                                                        LogLine(1, "Added " & sLinkKeyField & "='" & aExpressionValue(i) & "' to table " & sLinkedFile, "OK")
                                                    End If
                                                End If
                                            Next
                                        End If
                                    End If
                                End If
                            End If
                        End If


                    Catch ex As Exception
                        'Check if 'Ignore Errors' is checked. If so, clear the value or truncate if txt or mmo
                        If par_bIgnoreTransformErrors Then
                            Select Case Microsoft.VisualBasic.Left(dtTransformed.Columns(iCol).ColumnName, 3)
                                Case "CHK", "CMB", "CUR", "DR_", "INT", "LI_", "BI_", "LST", "SEL", "SI_", "SR_", "MLS"
                                    sExpressionValue = 0
                                Case "DTE", "DTT", "TME"
                                    'sExpressionValue = ""
                                    sExpressionValue = "1753-01-02 23:59:59.000"
                                Case "TXT", "MMO", "MMR", "SYS", "TEL"
                                    If Len(sExpressionValue) > dtTransformed.Columns(iCol).MaxLength Then
                                        LogLine(1, sProc & "VALIDATE__Truncate " & dtTransformed.Columns(iCol).ColumnName & ", Value='" & sExpressionValue & "'", "Warning")
                                        sExpressionValue = Left(sExpressionValue, dtTransformed.Columns(iCol).MaxLength)
                                        ''5/16/11 Truncate - was getting error with maxlength on UniqueKey field
                                        'If Len(sExpressionValue) > dtTransformed.Columns(iCol).MaxLength Then
                                        '    sExpressionValue = Left(sExpressionValue, dtTransformed.Columns(iCol).MaxLength)
                                        'End If
                                    End If
                            End Select
                            dtTransformed.Rows(iRow).Item(dtTransformed.Columns(iCol).ColumnName) = sExpressionValue    'EvalExpression(sExpression, iRow)
                        Else
                            LogLine(1, sProc & "VALIDATE__" & dtTransformed.Columns(iCol).ColumnName & ", Value='" & sExpressionValue & "'", "Warning")
                            'Return Nothing
                        End If
                    End Try
                Next 'Row

                'Upload dtLinkedKeysAdd

                If bKeysAdded Then
                    SQLBulkAdd(sLinkedFile, dtLinkedKeysAdd, sIMPJobID)
                    bKeysAdded = False
                End If
            Next 'Column

            dtTransformedNew = dtTransformed.Clone
            dtTransformedUpd = dtTransformed.Clone

            'Save dtTransform to session
            goP.SetVar("ImportSQL_dtTransformed", dtTransformed)
            goP.SetVar("ImportSQL_dtTransformedNew", dtTransformedNew)
            goP.SetVar("ImportSQL_dtTransformedUpd", dtTransformedUpd)

            LogLine(0, "Source data validation complete", "OK")

            Return True

        Catch ex As Exception

            'LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
            Return False

        End Try

    End Function

    Public Function TransformTableSplit(ByVal par_sSQLTableName As String, _
                                        ByVal par_bUseNormalizedKey As Boolean, _
                                        ByVal par_iKeyType As String, _
                                        ByVal par_sIMPJobID As String, _
                                        Optional ByVal bDupeCheck As Boolean = True, _
                                        Optional ByVal par_sSourceKey As String = "", _
                                        Optional ByVal par_sDestKey As String = "") As Boolean

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        '^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        'At this point, dtTransformed is created.
        'Now put update records into dtTransformedUpd and delete from dtTransformed
        'dtTrnasformedUpd will be inserted row by row into SQL db

        'This part modified to handle duplicate IDs in the source data
        'Source IDs need to be added to the dtKey table as duplicates are being evaluated
        ' A dupe source ID should result in the first instance being added to dtTransformedNew
        'and duplicate instances being addded to dtTransformedUpd. << Still accurate??

        'PARAMS:
        '   par_sSQLTableName:  Selltis File/Table name
        '   par_bUseNormalizedKey:  True if using normalized key. For back compat
        '   iKeyType:   0 = Source field mapped to field in SQL table
        '               1 = Source ID is in TN table in SQL
        '               2 = Dynamic keys provided in par_sSourceKey and par_sDestKey (new functionality V2010)
        '               (This param for back compat.)
        '   par_sIMPJobID:  Import ID
        '   par_sSourceKey: String containing source key field(s) and properties
        '   par_sDestKey: String containing Selltis key field(s) and properties

        Dim goTr As clTransform = HttpContext.Current.Session("goTr")
        Dim goP As clProject = HttpContext.Current.Session("goP")

        Dim dtMap As DataTable = Nothing
        Dim dtTransformed As DataTable = goP.GetVar("ImportSQL_dtTransformed")
        Dim dtTransformedUpd As DataTable = goP.GetVar("ImportSQL_dtTransformedUpd")
        Dim dtTransformedNew As DataTable = goP.GetVar("ImportSQL_dtTransformedNew")
        Dim dtKeyDest As DataTable
        'Dim sDestKey As String = ""
        Dim sDestKeyColumnName As String = ""
        Dim sSourceKeyColumnName As String = ""
        Dim sKeyField As String = ""
        Dim dvKey As DataView = Nothing
        Dim dvUpd As DataView
        Dim dr As DataRow = Nothing
        Dim iCount As Integer = 0
        Dim sFilter As String = ""
        Dim sFilterPart As String = ""
        Dim sNormValue As String = ""
        Dim iUseExtID As Integer = 0
        Dim bKeysExist As Boolean = False
        Dim iKeyCount As Integer = -1


        Try

            'no dupechecking
            If Not bDupeCheck Then
                dtTransformedNew = dtTransformed
            Else

                dtMap = MapTable

                'Check tables stored in session
                If dtTransformed Is Nothing Or dtTransformedNew Is Nothing Or dtTransformedUpd Is Nothing Then
                    LogLine(0, sProc & ": Unable to read source data", "Failed")
                    Return False
                Else
                    'Add UniqueKey column to each table
                    If Not dtTransformed.Columns.Contains("UniqueKey") Then
                        dtTransformed.Columns.Add("UniqueKey")
                    End If
                    If Not dtTransformedNew.Columns.Contains("UniqueKey") Then
                        dtTransformedNew.Columns.Add("UniqueKey")
                    End If
                    If Not dtTransformedUpd.Columns.Contains("UniqueKey") Then
                        dtTransformedUpd.Columns.Add("UniqueKey")
                    End If
                End If
                If dtMap Is Nothing Then
                    LogLine(0, sProc & ": Unable to read map data", "Failed")
                    Return False
                End If

                Select Case par_iKeyType

                    Case 0
                        '-- Duplicate records identified by specific field(s) in SQL table
                        '-- This is iKeyType = 0
                        '-- Get Primary key columns from SQL. Key fields are indicated by 'Key' column in dtMap (true or null)
                        '-- Supports: multiple, mapped fields. Single source field per destination field. No functions
                        '-- V2: support for multiple keys  using one row in dtMap with field code expression, '(%TXT_NameFirst%) (%TXT_NameLast%)'

                        Dim aKeys() As String = Nothing

                        For i As Integer = 0 To dtMap.Rows.Count - 1
                            If Not IsDBNull(dtMap.Rows(i).Item("Key")) Then
                                If dtMap.Rows(i).Item("Key") = True Then
                                    sDestKeyColumnName = dtMap.Rows(i).Item("SQLFieldName")
                                    'V2: Add source key:
                                    sSourceKeyColumnName = dtMap.Rows(i).Item("Expression")


                                    iKeyCount += 1
                                    ReDim Preserve aKeys(iKeyCount)
                                    aKeys(iKeyCount) = sDestKeyColumnName
                                    bKeysExist = True
                                    If sDestKeyColumnName = "TXT_ExternalID" Or sDestKeyColumnName = "TXT_ExternalID" Then
                                        iUseExtID += 1  'If this = 2 then we know to use TN table with external IDs
                                    End If
                                    'Temporary while multiple key system is worked out
                                    'V2: sDestKeycolumnName is an expression which may contain multiple field codes
                                    sKeyField = sDestKeyColumnName
                                End If
                            End If
                        Next

                        'Now have SQL key column names in array aKeys

                        'If sKeyField <> "" Then
                        If bKeysExist Then

                            'Get table of key values if sKeyField is passed
                            'New - handle TN table
                            If iUseExtID <> 2 Then
                                dtKeyDest = LoadRecordIDs(par_sSQLTableName, 0, aKeys)
                            Else
                                'iUseExtID = 2, which means key fields are TXT_ExternalID and TXT_ExternalSource
                                dtKeyDest = LoadRecordIDs(par_sSQLTableName, 1, aKeys)
                            End If

                            If dtKeyDest Is Nothing Then
                                LogLine(0, "Duplicate checking: Unable to load key table.", "Failed")
                                Exit Function
                            End If

                            'V2: Add UniqueID column to dtKey and fill based on SQL Key expression (%TXT_NameFirst%) (%TXT_NameLast%)
                            If dtKeyDest.Columns.Contains("UniqueKey") Then
                                dtKeyDest.Columns.Remove("UniqueKey")
                            Else

                                dtKeyDest.Columns.Add("UniqueKey")
                                'key expression is sKeyField
                                For Each drKey As DataRow In dtKeyDest.Rows
                                    Dim sKeyExpression As String = sKeyField
                                    Dim iStart As Integer = 1
                                    Dim iEnd As Integer = 1
                                    Dim sFieldCode As String = ""
                                    Dim sFieldName As String = ""
                                    Dim sFieldVal As String = ""

                                    'Compat with applet, single field may not have (% and %):
                                    If Not sKeyExpression.Contains("(%") And Not sKeyExpression.Contains("%)") Then
                                        sKeyExpression = "(%" & sKeyExpression & "%)"
                                    End If

                                    '-------
                                    Do While iStart > 0
                                        iStart = InStr(sKeyExpression, "(%")
                                        If iStart > 0 Then
                                            iEnd = InStr(sKeyExpression, "%)") + 1
                                            If iEnd > 0 Then
                                                sFieldCode = Mid(sKeyExpression, iStart, iEnd - iStart + 1)
                                                'Replace
                                                'sFieldCode is the column in dtKey
                                                sFieldName = Replace(sFieldCode, "(%", "")
                                                sFieldName = Replace(sFieldName, "%)", "")
                                                sFieldVal = drKey.Item(sFieldName)
                                                sKeyExpression = Replace(sKeyExpression, sFieldCode, sFieldVal)
                                            End If
                                        End If
                                    Loop
                                    drKey.Item("UniqueKey") = sKeyExpression
                                Next
                            End If

                            '2/6 STOP HERE
                            'Next: if using TN, dttransupd needs GID_ID of target record, which is GID_InternalID
                            'so, rename GID_InternalID to GID_ID??
                            'then check if this ID actually exists in target table

                            'Replace dbNull with empty string in dtKey (SQL data). Needed for string comparison?
                            For Each row As DataRow In dtKeyDest.Rows
                                For Each col As DataColumn In dtKeyDest.Columns
                                    If IsDBNull(row.Item(col)) Then row.Item(col) = ""
                                Next
                            Next

                            'Add IDNormalized column to SQL key table and set aKeys. Create normalized values
                            If par_bUseNormalizedKey Then
                                dtKeyDest.Columns.Add("TXT_IDNormalized")
                                ReDim aKeys(0)
                                aKeys(0) = "TXT_IDNormalized"
                                'create TXT_IDNormalized value by looping thru key table rows
                                For Each row As DataRow In dtKeyDest.Rows
                                    sNormValue = ""
                                    For Each col As DataColumn In dtKeyDest.Columns
                                        If col.ColumnName <> "TXT_IDNormalized" And col.ColumnName <> "GID_ID" Then
                                            sNormValue &= NormalizeValue(row.Item(col), par_sSQLTableName)
                                        End If
                                    Next
                                    row.Item("TXT_IDNormalized") = sNormValue
                                Next
                            End If

                            'set uniqueID in source 
                            EvalUniqueSourceID(dtTransformed, dtMap)

                            'Loop thru dtTransformed and move rows to either Add or Edit dt's
                            While dtTransformed.Rows.Count > 0
                                'Always working with row 0
                                ' Get key for dtTransformed row
                                dr = dtTransformed.Rows(0)
                                sFilter = dr("UniqueKey")
                                sFilter = "UniqueKey='" & sFilter & "'"

                                'Does this key exist in destination keys?
                                dtKeyDest.DefaultView.RowFilter = sFilter
                                If dtKeyDest.DefaultView.Count > 0 Then
                                    'This is an edit/update
                                    'If dvKey.Count > 0 Then
                                    'Move this row to dtTransformedUpd, checking first if key exists in dtTransformedUpd
                                    'dtTransformedUpd.DefaultView.RowFilter = sKeyField & " = '" & goTr.PrepareForSQL(sDestKey) & "'"
                                    dtTransformedUpd.DefaultView.RowFilter = sFilter
                                    dvUpd = dtTransformedUpd.DefaultView
                                    If dvUpd.Count = 0 Then
                                        'key doesn't exist
                                        'Change key value to GID_ID
                                        dr("UniqueKey") = dtKeyDest.DefaultView.Item(0).Item("GID_ID")
                                        'Import
                                        dtTransformedUpd.ImportRow(dr)
                                    Else
                                        'do nothing. don't want to add duplicate
                                    End If

                                Else
                                    dtTransformedNew.ImportRow(dr)
                                    'Add key to dtKey. No need to check if value already exists in dtKey b/c it will be caught in viewfilter above(?)
                                    'DO NOT want to do this now. >>>Source table has been deduped!!
                                    'USE GID_ID in dtKey now and update may be faster in SQLEdit
                                    'drKey = dtKey.NewRow
                                    'drKey.Item(0) = dr.Item(sKeyField)    'first col in dtKey is the key value
                                    'dtKey.Rows.Add(drKey)

                                End If
                                dtTransformed.Rows.Remove(dr)

                                'Else       'Old way
                                '    'key value in source data is null, so do not import
                                '    dtTransformed.Rows.Remove(dr)
                                'End If
                                'Next
                            End While

                            'Write dt's to session var's
                        Else
                            dtTransformedNew = dtTransformed
                        End If

                    Case 1

                        '-- Duplicate records identified by external ID and source in TN table
                        '-- This is iKeyType = 1
                        '-- Get filtered TN records from SQL.
                        'LogLine(0, "Begin duplicate checking", "")

                        dtKeyDest = LoadRecordIDs(par_sSQLTableName, 1, Nothing)
                        If dtKeyDest Is Nothing Then
                            LogLine(0, "Duplicate checking: Unable to load key table.", "Failed")
                            Exit Function
                        End If

                        'Iterate through source data rows
                        While dtTransformed.Rows.Count > 0
                            dr = dtTransformed.Rows(iCount) 'iCount is always 0 b/c always working with first row. we delete all rows

                            'aKeys contains TXT_ExternalSource and TXT_ExternalID
                            Dim aKeys(1) As String
                            aKeys(0) = "TXT_ExternalSource"

                            sFilter = ""
                            sFilterPart = ""
                            For i As Integer = 0 To aKeys.GetUpperBound(0)
                                'aSourceKeyVal(i) = dr.Item(aKeys(i))   'needed?
                                If IsDBNull(dr.Item(aKeys(i))) Then dr.Item(aKeys(i)) = ""
                                sFilterPart = aKeys(i) & " = '" & goTr.PrepareForSQL(dr.Item(aKeys(i))) & "'"
                                If sFilter = "" Then
                                    sFilter = sFilterPart
                                Else
                                    sFilter += " AND " & sFilterPart
                                End If
                            Next
                            'sDestKey = dr.Item(sKeyField)  'Old way with single field

                            'If Not IsDBNull(dr.Item(sKeyField)) Then   'Old way

                            'dtKey.DefaultView.RowFilter = sKeyField & " = '" & goTr.PrepareForSQL(sDestKey) & "'"
                            dtKeyDest.DefaultView.RowFilter = sFilter
                            dvKey = dtKeyDest.DefaultView

                        End While

                        If Not IsNothing(dvKey) Then
                            If dvKey.Count > 0 Then
                                'Move this row to dtTransformedUpd, checking first if key exists in dtTransformedUpd
                                'dtTransformedUpd.DefaultView.RowFilter = sKeyField & " = '" & goTr.PrepareForSQL(sDestKey) & "'"
                                dtTransformedUpd.DefaultView.RowFilter = sFilter
                                dvUpd = dtTransformedUpd.DefaultView
                                If dvUpd.Count = 0 Then
                                    'key doesn't exist
                                    dtTransformedUpd.ImportRow(dr)
                                Else
                                    'do nothing. don't want to add duplicate
                                End If

                            Else
                                dtTransformedNew.ImportRow(dr)

                            End If
                        End If

                        dtTransformed.Rows.Remove(dr)

                    Case 2
                        '-- Duplicate records identified by Key text fields in new import utility
                        '-- Key fields can contain field codes and properties in style of GetLinePart
                        Dim dtKeys As DataTable
                        Dim sKeys As String = ""
                        Dim aKeys() As String = Nothing
                        Dim sResult As String = ""
                        Dim iStart As Integer = 1
                        Dim sFieldCode As String = ""
                        Dim sFieldCodeName As String = ""
                        'Dim iFieldCode As Integer = 0

                        If sKeySource <> "" And sKeyDest <> "" Then
                            'LogLine(0, "Begin duplicate checking", "")
                            'Parse dest key string for Selltis field names
                            dtKeys = CreateKeyDatatable(sKeyDest)
                            For Each row As DataRow In dtKeys.Rows
                                sKeys += row("FLD") & ","
                            Next
                            If sKeys.EndsWith(",") Then sKeys.TrimEnd(",")
                            aKeys = Split(sKeys, ",")

                            'Get table of key values
                            dtKeyDest = LoadRecordIDs(par_sSQLTableName, 2, aKeys)
                            If dtKeyDest Is Nothing Then
                                LogLine(0, "Duplicate checking: Unable to load key table.", "Failed")
                                Exit Function
                            End If

                            'Add UniqueID column to dtKey and fill based on SQL Key expression (%TXT_NameFirst%) (%TXT_NameLast%)
                            If Not dtKeyDest.Columns.Contains("UniqueKey") Then
                                dtKeyDest.Columns.Add("UniqueKey")
                            End If

                            '2/6 STOP HERE
                            'Next: if using TN, dttransupd needs GID_ID of target record, which is GID_InternalID
                            'so, rename GID_InternalID to GID_ID??
                            'then check if this ID actually exists in target table

                            'Set UniqueKey in dtKeyDest
                            For i As Integer = 0 To dtKeyDest.Rows.Count - 1
                                dtKeyDest.Rows(i).Item("UniqueKey") = GetFieldValFromDataTable(dtKeyDest, sKeyDest, i)
                            Next

                            'Replace dbNull with empty string in dtKey (SQL data). Needed for string comparison?
                            For Each row As DataRow In dtKeyDest.Rows
                                For Each col As DataColumn In dtKeyDest.Columns
                                    If IsDBNull(row.Item(col)) Then row.Item(col) = ""
                                Next
                            Next

                            'set uniqueID in source *****   HERE 4/7  LOOK AT FUNCTION BELOW:::   *****
                            'Is the unique key value filled in dtTransformed (source data)?
                            'EvalUniqueSourceID(dtTransformed, dtMap)



                            'Loop thru dtTransformed and move rows to either Add or Edit dt's
                            While dtTransformed.Rows.Count > 0
                                'dtKey.DefaultView.RowFilter = sKeyField & " = '" & goTr.PrepareForSQL(sDestKey) & "'"
                                'dtKeyDest.DefaultView.RowFilter = sFilter
                                'dvKey = dtKeyDest.DefaultView

                                'Always working with row 0
                                ' Get key for dtTransformed row
                                dr = dtTransformed.Rows(0)
                                If IsDBNull(dr("UniqueKey")) Then dr("UniqueKey") = ""

                                sFilter = goTr.PrepareForSQL(dr("UniqueKey"))
                                sFilter = "UniqueKey='" & sFilter & "'"

                                'Does this key exist in destination keys?
                                dtKeyDest.DefaultView.RowFilter = sFilter
                                If dtKeyDest.DefaultView.Count > 0 Then
                                    'This is an edit/update
                                    'If dvKey.Count > 0 Then
                                    'Move this row to dtTransformedUpd, checking first if key exists in dtTransformedUpd
                                    'dtTransformedUpd.DefaultView.RowFilter = sKeyField & " = '" & goTr.PrepareForSQL(sDestKey) & "'"
                                    dtTransformedUpd.DefaultView.RowFilter = sFilter
                                    dvUpd = dtTransformedUpd.DefaultView
                                    If dvUpd.Count = 0 Then
                                        'key doesn't exist
                                        'Change key value to GID_ID
                                        dr("UniqueKey") = dtKeyDest.DefaultView.Item(0).Item("GID_ID")
                                        'Import
                                        dtTransformedUpd.ImportRow(dr)
                                    Else
                                        'do nothing. don't want to add duplicate
                                    End If

                                Else
                                    dtTransformedNew.ImportRow(dr)
                                    'Add key to dtKey. No need to check if value already exists in dtKey b/c it will be caught in viewfilter above(?)
                                    'DO NOT want to do this now. >>>Source table has been deduped!!
                                    'USE GID_ID in dtKey now and update may be faster in SQLEdit
                                    'drKey = dtKey.NewRow
                                    'drKey.Item(0) = dr.Item(sKeyField)    'first col in dtKey is the key value
                                    'dtKey.Rows.Add(drKey)

                                End If
                                dtTransformed.Rows.Remove(dr)

                                'Else       'Old way
                                '    'key value in source data is null, so do not import
                                '    dtTransformed.Rows.Remove(dr)
                                'End If
                                'Next
                            End While

                        End If

                        'Write dt's to session var's
                        'Else
                        'dtTransformedNew = dtTransformed
                        'End If


                    Case Else

                End Select

            End If

            'Prepare tables for import
            'dtTransformedNew: remove UniqueKey column
            If dtTransformedNew.Columns.Contains("UniqueKey") Then dtTransformedNew.Columns.Remove("UniqueKey")
            'dtTransformedUpd: rename UniqueKey to GID_ID
            If Not dtTransformedUpd.Columns.Contains("GID_ID") Then
                If dtTransformedUpd.Columns.Contains("UniqueKey") Then dtTransformedUpd.Columns("UniqueKey").ColumnName = "GID_ID"
            End If

            'Update session dt's
            goP.SetVar("ImportSQL_dtTransformed", dtTransformed)
            goP.SetVar("ImportSQL_dtTransformedNew", dtTransformedNew)
            goP.SetVar("ImportSQL_dtTransformedUpd", dtTransformedUpd)

            LogLine(0, "Duplicate check resulted in " & dtTransformedNew.Rows.Count & " adds, and " & dtTransformedUpd.Rows.Count & " updates", "OK")

            Return True

        Catch ex As Exception

            'LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
            Return False

        End Try

    End Function

    Private Function GetFieldValFromDataTable(ByRef par_dt As DataTable, _
                                              ByVal par_sExpression As String, _
                                              ByVal par_iRow As Integer, _
                                              Optional ByVal par_sSelltisFile As String = "", _
                                              Optional ByVal par_sDestFieldName As String = "", _
                                              Optional ByVal par_iOffsetMinutes As Integer = 0, _
                                              Optional ByVal par_sNormalizeCOorCN As String = "", _
                                              Optional ByVal par_bCreateMissingLSTValues As Boolean = False) As String

        'PURPOSE:
        '		Evaluate field codes. Like a GetFieldVal for source and destination data tables
        '       Values are read from the fields in iRow, and transformed into Selltis friendly data
        'CALLED FROM:
        '       DedupeSourceData
        'PARAMETERS:
        '       par_dt:                 Datatable containing the value to search
        '       par_sExpression:        String expression to evaluate. Usually contains field codes
        '       par_iRow:               Row to read from dt
        '       par_sSelltisFile:       Selltis File name
        '       par_sDestFieldName:     Selltis field name
        '       par_iOffsetMinutes:     Number of minutes to offset datetime values
        '       par_sNormalizeCOorCN:   'CO' or 'CN' if key is to be normalized for CO or CN data
        '       par_bCreateMissingLSTValues:    True to create LST metadata lines for values that do not exist. No undo
        'RETURNS:
        '		String
        'AUTHOR: PJ

        'PJ: Some known issues with '%' appearing in source data. Will cause evaluation of one value to fail, but not critical

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Dim sExpression As String = par_sExpression
        Dim iStart As Integer = 1
        Dim iEnd As Integer = 1
        Dim sFieldCode As String = ""
        Dim sFieldVal As String = ""
        Dim aFieldCode() As String
        Dim sFieldCodeName As String
        Dim sLength As String = ""
        Dim sNormalize As String = ""
        Dim sReturnValue As String

        Dim iResult As Integer = -1
        Dim sPrefix As String = Mid(par_sDestFieldName, 1, 4)
        Dim sSourceVal As String = ""
        Dim sResult As String = ""

        Try

            'Get instances of (% followed by %)
            Do While iStart > 0
                iStart = InStr(sExpression, "(%")
                If iStart > 0 Then
                    iEnd = InStr(sExpression, "%)") + 1
                    If iEnd > 0 Then
                        sFieldCode = Mid(sExpression, iStart, iEnd - iStart + 1)
                        'sFieldCode = Replace(sFieldCode, "(%", "")
                        'sFieldCode = Replace(sFieldCode, "%)", "")
                        'V2: Field codes have properties FLD_, LEN_, ??
                        aFieldCode = Split(sFieldCode, "|")
                        'For i As Integer = 0 To aFieldCode.GetUpperBound(0)
                        If UCase(aFieldCode(0)).Contains("FLD=") Then
                            sFieldCodeName = Replace(aFieldCode(0), "FLD=", "")
                        Else
                            sFieldCodeName = sFieldCode    'This for backward compat. NO! for other values (%Zip%)
                            'Exit For
                        End If
                        sFieldCodeName = RemoveFieldCodeTags(sFieldCodeName)
                        If aFieldCode.GetUpperBound(0) > 0 Then
                            If UCase(aFieldCode(1)).Contains("LEN=") Then
                                sLength = Replace(aFieldCode(1), "LEN=", "")
                                sLength = RemoveFieldCodeTags(sLength)
                            End If
                            If aFieldCode.GetUpperBound(0) > 1 Then
                                If UCase(aFieldCode(2)).Contains("NOR=") Then
                                    sNormalize = Replace(aFieldCode(2), "NOR=", "")
                                    sNormalize = RemoveFieldCodeTags(sNormalize)
                                End If
                            End If
                        End If

                        'Next

                        If Not IsDBNull(par_dt.Rows(par_iRow).Item(sFieldCodeName)) Then
                            sFieldVal = par_dt.Rows(par_iRow).Item(sFieldCodeName)
                        Else
                            sFieldVal = ""
                        End If

                        'debug
                        If sFieldVal.Contains("OLIN CHEMICAL") Then
                            Debug.WriteLine("Here")
                        End If
                        '

                        If UCase(sNormalize) = "1" Then
                            sFieldVal = NormalizeValue(sFieldVal, par_sNormalizeCOorCN)
                        End If

                        If IsNumeric(sLength) Then
                            sFieldVal = goTR.TruncateText(sFieldVal, goTR.StringToNum(sLength), False)
                        End If

                        'remove leading % from value, so as to not confuse when Propercase(%Olin Equip)
                        Do While InStr(sFieldVal, "%") = 1
                            sFieldVal = Right(sFieldVal, Len(sFieldVal) - 1)
                        Loop

                        'sExpression = Replace(sExpression, sFieldCode, GetSourceValue(sSQLTableName, dtSource, sFieldCode, iRow, sDestFieldName, iOffsetMinutes))    '<<enuf to keep rows aligned properly??
                        sExpression = Replace(sExpression, sFieldCode, sFieldVal)
                        '
                    End If
                End If
            Loop

            '!!Does this take care of source and dest values?
            'sExpression = Replace(sExpression, vbCrLf, " ") 'replace VbCrLf
            sExpression = Trim(sExpression)                 'remove trailing spaces
            If sExpression.EndsWith(vbCrLf) Then
                sExpression = Left(sExpression, Len(sExpression) - 2)
            End If

            sReturnValue = sExpression

            'debug
            If sReturnValue.Contains("OLIN CHEMICAL") Then
                Debug.WriteLine("Here")
            End If

            '--- Selltis-ize the data for Sellts field types ---
            Select Case sPrefix
                Case "MLS_"
                    'Lookup: All lst values for this table are in dtLSTMetadata
                    'iResult = GetLSTValue(sFieldCode, dtSource.Rows(iRow).Item(sFieldCode))
                    iResult = GetLSTValue(par_sSelltisFile, par_sDestFieldName, sReturnValue, par_bCreateMissingLSTValues)
                    If iResult = -1 Then
                        Return "Error: Cannot find value for " & par_sExpression & ": " & par_dt.Rows(par_iRow).Item(sFieldCode)
                    Else
                        sResult = iResult.ToString
                    End If

                Case "CHK_"
                    Select Case UCase(sReturnValue)
                        Case "TRUE", "YES", "CHECKED", "1"
                            sResult = 1
                        Case "FALSE", "NO", "UNCHECKED", "0"
                            sResult = 0
                    End Select
                Case "CUR_"
                    sReturnValue = Replace(sReturnValue, "$", "")
                    sReturnValue = Replace(sReturnValue, ",", "")
                    'PJ 10/11/12: Euro symbol replacement
                    sReturnValue = Replace(sReturnValue, Chr(128), "")
                    sResult = sReturnValue

                Case "DTT_"
                    If sReturnValue = "" Then
                        sResult = "1753-01-02 23:59:59.000"
                    Else
                        If InStr(sReturnValue, "-") = 0 And InStr(sReturnValue, "/") = 0 Then
                            If Len(sReturnValue) = 8 Then
                                'Add hyphen between yyyy-mm-dd
                                sResult = Left(sReturnValue, 4) & "-" & Mid(sReturnValue, 5, 2) & "-" & Mid(sReturnValue, 7, 2)
                            End If
                        Else
                            sResult = sReturnValue
                        End If
                        'GMT offset
                        If par_iOffsetMinutes <> 0 Then
                            Dim myDate As DateTime = CType(sReturnValue, DateTime)
                            Dim myTime As String = DatePart(DateInterval.Hour, myDate)
                            sResult = DateAdd(DateInterval.Minute, par_iOffsetMinutes, myDate)

                        End If
                    End If

                Case Else
                    sResult = sReturnValue
            End Select

            'Need to wrap leading and trailing % with ''. Othewise could result in 
            'a parsing error if the returned value is part of a function, such as 
            'Propercase(%ABC CHEMICAL). Code will see this as an invalid field code.

            If sResult <> "" Then
                Do While Left(sResult, 1) = "%"
                    sResult = "'" & sResult
                Loop
                Do While Right(sResult, 1) = "%"
                    sResult = sResult & "'"
                Loop
            End If


            '--- Run Supported Functions ---
            Dim iFunctionStart As Integer = 0   'InStr(sExpression, "Propercase(")
            Dim sFunction As String = ""    'portion of sExpression to replace
            Dim bFuncEndFound As Boolean = False
            Dim sParam As String = ""
            Dim sParamProper As String = ""
            Dim iParamStart As Integer
            Dim iParamEnd As Integer
            Dim bParamEndFound As Boolean = False
            Dim iOpenParenCt As Integer = 0
            Dim iCloseParenCt As Integer = 0
            Dim iCharPos As Integer = 0
            Dim sChar As String

            'Functions - Propercase
            Do
                iFunctionStart = InStr(sResult, "Propercase(")
                If iFunctionStart = 0 Then Exit Do
                'If iFunctionStart > 0 Then      'ifunctionStart is first char of string to replace with returned value
                'remove everything to the left of function
                sFunction = Mid(sResult, iFunctionStart)    ', Len(sResult) - iFunctionStart + 1) 'everything to right

                'On what char does sfunction end - closing paren
                'iOpenParenCt = 1
                iCharPos = 0
                iOpenParenCt = 0
                iCloseParenCt = 0
                bFuncEndFound = False
                Do While bFuncEndFound = False
                    iCharPos += 1   'increment char position
                    sChar = Mid(sFunction, iCharPos, 1)    'get next char
                    If sChar = ")" Then
                        iCloseParenCt += 1
                        If iCloseParenCt = iOpenParenCt Then
                            bFuncEndFound = True
                            iParamEnd = iCharPos
                        End If
                    ElseIf sChar = "(" Then
                        iOpenParenCt += 1
                        If iOpenParenCt = 1 Then
                            iParamStart = iCharPos
                        End If
                    End If
                    If iCharPos = Len(sFunction) Then Exit Do
                Loop

                If iParamEnd > iParamStart Then
                    sFunction = Mid(sFunction, 1, iParamEnd)
                    sParam = Mid(sFunction, iParamStart + 1, iParamEnd - iParamStart - 1)
                    sParamProper = StrConv(sParam, VbStrConv.ProperCase)

                    sResult = Replace(sResult, sFunction, sParamProper)
                Else
                    'can't parse, possibly because field value contains unpaired '(' or ')'
                    sResult = Replace(sResult, "Propercase(", "")
                End If

            Loop

            'Functions - Format PHone
            Dim sNumericPortion As String = ""
            Dim sExtension As String = ""
            Dim sPhoneFormatted As String = ""

            Do
                iFunctionStart = InStr(sResult, "FormatPhone(")
                If iFunctionStart = 0 Then Exit Do
                'If iFunctionStart > 0 Then      'ifunctionStart is first char of string to replace with returned value
                'remove everything to the left of function
                sFunction = Mid(sResult, iFunctionStart)    ', Len(sResult) - iFunctionStart + 1) 'everything to right

                'On what char does sfunction end - closing paren
                'iOpenParenCt = 1
                iCharPos = 0
                iOpenParenCt = 0
                iCloseParenCt = 0
                bFuncEndFound = False

                'Do While bFuncEndFound = False
                Do
                    iCharPos += 1   'increment char position
                    sChar = Mid(sFunction, iCharPos, 1)    'get next char
                    If sChar = ")" Then
                        iCloseParenCt += 1
                        If iCloseParenCt = iOpenParenCt Then
                            bFuncEndFound = True
                            iParamEnd = iCharPos
                        End If
                    ElseIf sChar = "(" Then
                        iOpenParenCt += 1
                        If iOpenParenCt = 1 Then
                            iParamStart = iCharPos
                        End If
                    End If
                    If iCharPos = Len(sFunction) Then Exit Do
                Loop

                If iParamEnd > iParamStart Then
                    sFunction = Mid(sFunction, 1, iParamEnd)
                    sParam = Mid(sFunction, iParamStart + 1, iParamEnd - iParamStart - 1)
                    'sParamProper = StrConv(sParam, VbStrConv.ProperCase)
                    'format phone which is sParam
                    'Strip ( ) - . , 
                    sParam = Replace(sParam, "(", "")
                    sParam = Replace(sParam, ")", "")
                    sParam = Replace(sParam, "-", "")
                    sParam = Replace(sParam, ".", "")
                    sParam = Replace(sParam, ",", "")
                    sParam = Replace(sParam, " ", "")

                    'If non-numeric found (x, ext, ????) then take number to left as phone #, leave non-numeric intact
                    sNumericPortion = sParam
                    For i As Integer = 1 To Len(sParam)
                        If Not IsNumeric(Mid(sParam, i, 1)) Then
                            'text found (ext?)
                            sNumericPortion = Mid(sParam, 1, i - 1)
                            sExtension = Mid(sParam, i, Len(sParam) - i)
                            Exit For
                        End If
                    Next

                    'Select case on len and insert -
                    Select Case Len(sNumericPortion)
                        Case 7
                            sPhoneFormatted = Mid(sNumericPortion, 1, 3) & "-" & Mid(sNumericPortion, 4, 4)
                        Case 8
                            sPhoneFormatted = Mid(sNumericPortion, 1, 1) & "-" & Mid(sNumericPortion, 2, 3) & "-" & Mid(sNumericPortion, 5, 4)
                        Case 10
                            sPhoneFormatted = Mid(sNumericPortion, 1, 3) & "-" & Mid(sNumericPortion, 4, 3) & "-" & Mid(sNumericPortion, 7, 4)
                        Case 11
                            sPhoneFormatted = Mid(sNumericPortion, 1, 1) & "-" & Mid(sNumericPortion, 2, 3) & "-" & Mid(sNumericPortion, 5, 3) & "-" & Mid(sNumericPortion, 8, 4)
                        Case Else
                            sPhoneFormatted = sNumericPortion
                    End Select

                    If sExtension <> "" Then
                        sPhoneFormatted = sPhoneFormatted & " " & sExtension
                    End If

                    sResult = Replace(sResult, sFunction, sPhoneFormatted)
                Else
                    'can't parse, possibly because field value contains unpaired '(' or ')'
                    sResult = Replace(sResult, "FormatPhone(", "")
                End If

            Loop

            'Remove leading/trailing vbcrlf
            Do While Mid(sResult, 1, 2) = vbCrLf
                sResult = Mid(sResult, 3)
            Loop
            Do While Right(sResult, 2) = vbCrLf
                sResult = Mid(sResult, 1, Len(sResult) - 2)
            Loop

            sResult = Trim(sResult)

            Return sResult

        Catch ex As Exception

            'LogLine(0, sProc & ": " & ex.Message, "Failed")
            Return Nothing

        End Try

    End Function

    Private Function EvalExpression(ByVal sSQLTableName As String, ByRef dtSource As DataTable, ByVal sExpression As String, ByVal iRow As Integer, ByVal sDestFieldName As String, Optional ByVal iOffsetMinutes As Integer = 0) As String

        'PURPOSE:
        '		Evaluate source field codes. Like a GetFieldVal for source data.   4/6/11: Replace top portion with GetFieldValFromDatatable??
        '       Execute custom functions after field codes are evaluated
        'CALLED FROM:
        '       TransformTable
        'PARAMETERS:
        '		sSQLTableName: This is the name of the destination SQL table
        '       dtSource: Source datatable (has been deduped)
        '       sExpression: expression to be evaluated, usually taken from the map table. 
        '           example: 'Propercase((%TXT_CompanyName%))'
        '       iRow:       row of dtSource to read
        '       sDestFieldName:     Selltis field name this expression is mapped to
        '       iOffsetMinutes:     offset minutes for datetime fields
        'RETURNS:
        '		String
        'AUTHOR: PJ

        'PJ: Some known issues with '%' appearing in source data. Will cause evaluation of one value to fail, but not critical

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Dim iStart As Integer = 1
        Dim iEnd As Integer = 1
        Dim sFieldCode As String = ""
        Dim sFieldVal As String = ""
        Dim aFieldCode() As String
        Dim sFieldCodeName As String = ""

        Try

            'Replace source field values with field values from source data
            'Get instances of (% followed by %)
            Do While iStart > 0
                iStart = InStr(sExpression, "(%")
                If iStart > 0 Then
                    iEnd = InStr(sExpression, "%)") + 1
                    If iEnd > 0 Then
                        sFieldCode = Mid(sExpression, iStart, iEnd - iStart + 1)
                        'V2: Field codes have properties FLD_, LEN_, ??
                        aFieldCode = Split(sFieldCode, "|")
                        For i As Integer = 0 To aFieldCode.GetUpperBound(0)
                            If UCase(aFieldCode(i)).Contains("FLD=") Then
                                sFieldCodeName = Replace(aFieldCode(i), "FLD=", "")
                            Else
                                sFieldCodeName = sFieldCode
                                'Exit For
                            End If
                        Next
                        'Replace
                        sFieldVal = GetSourceValue(sSQLTableName, dtSource, sFieldCodeName, iRow, sDestFieldName, iOffsetMinutes)
                        If Mid(sFieldVal, 1, 5) <> "Error" Then
                            'sExpression = Replace(sExpression, sFieldCode, GetSourceValue(sSQLTableName, dtSource, sFieldCode, iRow, sDestFieldName, iOffsetMinutes))    '<<enuf to keep rows aligned properly??
                            sExpression = Replace(sExpression, sFieldCode, sFieldVal)
                        Else
                            sExpression = sFieldVal 'this is the error
                        End If
                    End If
                End If
            Loop

            'Replace keywords in (- -)


            'Replace 

            Dim iFunctionStart As Integer = 0   'InStr(sExpression, "Propercase(")
            Dim sFunction As String = ""    'portion of sExpression to replace
            Dim bFuncEndFound As Boolean = False
            Dim sParam As String = ""
            Dim sParamProper As String = ""
            Dim iParamStart As Integer
            Dim iParamEnd As Integer
            Dim bParamEndFound As Boolean = False
            Dim iOpenParenCt As Integer = 0
            Dim iCloseParenCt As Integer = 0
            Dim iCharPos As Integer = 0
            Dim sChar As String

            'Functions - Propercase
            Do
                iFunctionStart = InStr(sExpression, "Propercase(")
                If iFunctionStart = 0 Then Exit Do
                'If iFunctionStart > 0 Then      'ifunctionStart is first char of string to replace with returned value
                'remove everything to the left of function
                sFunction = Mid(sExpression, iFunctionStart)    ', Len(sExpression) - iFunctionStart + 1) 'everything to right

                'On what char does sfunction end - closing paren
                'iOpenParenCt = 1
                iCharPos = 0
                iOpenParenCt = 0
                iCloseParenCt = 0
                bFuncEndFound = False
                Do While bFuncEndFound = False
                    iCharPos += 1   'increment char position
                    sChar = Mid(sFunction, iCharPos, 1)    'get next char
                    If sChar = ")" Then
                        iCloseParenCt += 1
                        If iCloseParenCt = iOpenParenCt Then
                            bFuncEndFound = True
                            iParamEnd = iCharPos
                        End If
                    ElseIf sChar = "(" Then
                        iOpenParenCt += 1
                        If iOpenParenCt = 1 Then
                            iParamStart = iCharPos
                        End If
                    End If
                    If iCharPos = Len(sFunction) Then Exit Do
                Loop

                If iParamEnd > iParamStart Then
                    sFunction = Mid(sFunction, 1, iParamEnd)
                    sParam = Mid(sFunction, iParamStart + 1, iParamEnd - iParamStart - 1)
                    sParamProper = StrConv(sParam, VbStrConv.ProperCase)

                    sExpression = Replace(sExpression, sFunction, sParamProper)
                Else
                    'can't parse, possibly because field value contains unpaired '(' or ')'
                    sExpression = Replace(sExpression, "Propercase(", "")
                End If

            Loop

            'Functions - Format PHone
            Dim sNumericPortion As String = ""
            Dim sExtension As String = ""
            Dim sPhoneFormatted As String = ""

            Do
                iFunctionStart = InStr(sExpression, "FormatPhone(")
                If iFunctionStart = 0 Then Exit Do
                'If iFunctionStart > 0 Then      'ifunctionStart is first char of string to replace with returned value
                'remove everything to the left of function
                sFunction = Mid(sExpression, iFunctionStart)    ', Len(sExpression) - iFunctionStart + 1) 'everything to right

                'On what char does sfunction end - closing paren
                'iOpenParenCt = 1
                iCharPos = 0
                iOpenParenCt = 0
                iCloseParenCt = 0
                bFuncEndFound = False

                'Do While bFuncEndFound = False
                Do
                    iCharPos += 1   'increment char position
                    sChar = Mid(sFunction, iCharPos, 1)    'get next char
                    If sChar = ")" Then
                        iCloseParenCt += 1
                        If iCloseParenCt = iOpenParenCt Then
                            bFuncEndFound = True
                            iParamEnd = iCharPos
                        End If
                    ElseIf sChar = "(" Then
                        iOpenParenCt += 1
                        If iOpenParenCt = 1 Then
                            iParamStart = iCharPos
                        End If
                    End If
                    If iCharPos = Len(sFunction) Then Exit Do
                Loop

                If iParamEnd > iParamStart Then
                    sFunction = Mid(sFunction, 1, iParamEnd)
                    sParam = Mid(sFunction, iParamStart + 1, iParamEnd - iParamStart - 1)
                    'sParamProper = StrConv(sParam, VbStrConv.ProperCase)
                    'format phone which is sParam
                    'Strip ( ) - . , 
                    sParam = Replace(sParam, "(", "")
                    sParam = Replace(sParam, ")", "")
                    sParam = Replace(sParam, "-", "")
                    sParam = Replace(sParam, ".", "")
                    sParam = Replace(sParam, ",", "")

                    'If non-numeric found (x, ext, ????) then take number to left as phone #, leave non-numeric intact
                    sNumericPortion = sParam
                    For i As Integer = 1 To Len(sParam)
                        If Not IsNumeric(Mid(sParam, i, 1)) Then
                            'text found (ext?)
                            sNumericPortion = Mid(sParam, 1, i - 1)
                            sExtension = Mid(sParam, i, Len(sParam) - i)
                            Exit For
                        End If
                    Next

                    'Select case on len and insert -
                    Select Case Len(sNumericPortion)
                        Case 7
                            sPhoneFormatted = Mid(sNumericPortion, 1, 3) & "-" & Mid(sNumericPortion, 4, 4)
                        Case 8
                            sPhoneFormatted = Mid(sNumericPortion, 1, 1) & "-" & Mid(sNumericPortion, 2, 3) & "-" & Mid(sNumericPortion, 5, 4)
                        Case 10
                            sPhoneFormatted = Mid(sNumericPortion, 1, 3) & "-" & Mid(sNumericPortion, 4, 3) & "-" & Mid(sNumericPortion, 7, 4)
                        Case 11
                            sPhoneFormatted = Mid(sNumericPortion, 1, 1) & "-" & Mid(sNumericPortion, 2, 3) & "-" & Mid(sNumericPortion, 5, 3) & "-" & Mid(sNumericPortion, 8, 4)
                    End Select

                    If sExtension <> "" Then
                        sPhoneFormatted = sPhoneFormatted & " " & sExtension
                    End If

                    sExpression = Replace(sExpression, sFunction, sPhoneFormatted)
                Else
                    'can't parse, possibly because field value contains unpaired '(' or ')'
                    sExpression = Replace(sExpression, "FormatPhone(", "")
                End If

            Loop

            'Remove leading/trailing vbcrlf
            Do While Mid(sExpression, 1, 2) = vbCrLf
                sExpression = Mid(sExpression, 3)
            Loop
            Do While Right(sExpression, 2) = vbCrLf
                sExpression = Mid(sExpression, 1, Len(sExpression) - 2)
            Loop


            Return sExpression

        Catch ex As Exception

            'LogLine(1, sProc & ": " & ex.Message & "; Could not evaluate expression " & sExpression, "Failed")
            Return ""

        End Try


    End Function

    Private Function GetSourceValue(ByVal sSQLTableName As String, ByRef dtSource As DataTable, ByVal sFieldCode As String, ByVal iRow As Integer, ByVal sDestFieldName As String, Optional ByVal iOffsetMinutes As Integer = 0) As String

        'pj 12/6/2011 Why is this commented out?

        'PURPOSE:
        '		Gets value from Source datatable
        'CALLED FROM:
        '       EvalExpression
        'PARAMETERS:
        '		sSQLTableName: This is the name of the destination SQL table
        '       dtSource: Source datatable (has been deduped)
        '       sFieldCode: field to be evaluated 
        '       iRow: row in dtSource being evaluated
        '       sDestFieldName: SQL field name being edited
        '       iOffsetMinutes:     offset minutes for datetime fields
        'RETURNS:
        '		String
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Dim iResult As Integer = -1
        Dim sPrefix As String = ""
        Dim sSourceVal As String = ""
        Dim sResult As String = ""

        Try
            sFieldCode = Replace(sFieldCode, "(%", "")
            sFieldCode = Replace(sFieldCode, "%)", "")

            'Check if is null - other field types??

            'If Not IsDBNull(dtSource.Rows(iRow).Item(sFieldCode)) Then  'want destination field here
            '    sPrefix = Mid(sDestFieldName, 1, 4)
            '    Select Case sPrefix
            '        Case "MLS_"
            '            'Lookup: All lst values for this table are in dtLSTMetadata
            '            'iResult = GetLSTValue(sFieldCode, dtSource.Rows(iRow).Item(sFieldCode))
            '            iResult = GetLSTValue(sSQLTableName, sDestFieldName, dtSource.Rows(iRow).Item(sFieldCode))
            '            If iResult = -1 Then
            '                Return "Error: Cannot find value for " & sFieldCode & ": " & dtSource.Rows(iRow).Item(sFieldCode)
            '            Else
            '                sResult = iResult.ToString
            '            End If

            '        Case "CHK_"
            '            sSourceVal = dtSource.Rows(iRow).Item(sFieldCode)
            '            Select Case UCase(sSourceVal)
            '                Case "TRUE", "YES", "CHECKED"
            '                    sResult = 1
            '                Case "FALSE", "NO", "UNCHECKED"
            '                    sResult = 0
            '            End Select
            '        Case "CUR_"
            '            sSourceVal = dtSource.Rows(iRow).Item(sFieldCode)
            '            sSourceVal = Replace(sSourceVal, "$", "")
            '            sSourceVal = Replace(sSourceVal, ",", "")
            '            sResult = sSourceVal

            '        Case "DTT_"
            '            sSourceVal = dtSource.Rows(iRow).Item(sFieldCode)
            '            If InStr(sSourceVal, "-") = 0 And InStr(sSourceVal, "/") = 0 Then
            '                If Len(sSourceVal) = 8 Then
            '                    'Add hyphen between yyyy-mm-dd
            '                    sResult = Left(sSourceVal, 4) & "-" & Mid(sSourceVal, 5, 2) & "-" & Mid(sSourceVal, 7, 2)
            '                End If
            '            Else
            '                sResult = sSourceVal
            '            End If
            '            'GMT offset
            '            If iOffsetMinutes <> 0 Then
            '                Dim myDate As DateTime = CType(sResult, DateTime)
            '                Dim myTime As String = DatePart(DateInterval.Hour, myDate)
            '                sResult = DateAdd(DateInterval.Minute, iOffsetMinutes, myDate)

            '            End If


            '        Case Else
            '            sResult = dtSource.Rows(iRow).Item(sFieldCode)
            '    End Select
            'End If

            ''Need to wrap leading and trailing % with ''. Othewise could result in 
            ''a parsing error if the returned value is part of a function, such as 
            ''Propercase(%ABC CHEMICAL). Code will see this as an invalid field code.

            'If sResult <> "" Then
            '    Do While Left(sResult, 1) = "%"
            '        sResult = "'" & sResult
            '    Loop
            '    Do While Right(sResult, 1) = "%"
            '        sResult = sResult & "'"
            '    Loop
            'End If

            Return sResult

        Catch ex As Exception

            'LogLine(1, sProc & ": " & ex.Message & "; Could not get source field value " & sFieldCode, "Failed")
            Return ""

        End Try

    End Function

    Private Function EvalUniqueSourceID(ByRef dtTransformed As DataTable, ByRef dtMap As DataTable) As Boolean

        'PURPOSE: 
        '      Fill UniqueID in source data
        'CALLED FROM:
        '       EvalExpression
        'PARAMETERS:
        '		dtTransformed:  Datatable with transformed/validated source data
        '       dtmap:          Map table
        'RETURNS:
        '		True/False
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Try
            'dtTransformed is source data, with Selltis field names and UniqueID column
            'dtMap has one row with 'Key' field = True
            If Not dtTransformed.Columns.Contains("UniqueKey") Then
                dtTransformed.Columns.Add("UniqueKey")
            End If

            'What is key source expression? Comes from dtMap, 'Key' row, field 'Expression'
            Dim sSourceKeyExpression As String = ""
            Dim sSourceKey As String = ""
            For Each drMap As DataRow In dtMap.Rows
                If Not IsDBNull(drMap("Key")) Then
                    If drMap("Key") = "True" Then
                        sSourceKey = drMap("Expression")
                    End If
                End If
            Next

            'Translate sSourceKeyExpression into Selltis field names. Fields must be mapped!
            Dim iStart As Integer = 1
            Dim iEnd As Integer = 1
            Dim sFieldCodeSource As String = ""
            Dim sFieldCodeDest As String = ""
            Dim sFieldName As String = ""
            Dim sFieldVal As String = ""

            For Each drTrans As DataRow In dtTransformed.Rows
                sSourceKeyExpression = sSourceKey
                Do 'While iStart > 0
                    iStart = InStr(sSourceKeyExpression, "(%")
                    If iStart > 0 Then
                        iEnd = InStr(sSourceKeyExpression, "%)") + 1
                        If iEnd > 0 Then
                            'Pick out the field code from string of field codes
                            sFieldCodeSource = Mid(sSourceKeyExpression, iStart, iEnd - iStart + 1)
                            'Consult dtMap for name of Selltis field
                            Try
                                sFieldCodeDest = ""
                                sFieldVal = ""
                                For Each drMap As DataRow In dtMap.Rows
                                    If drMap.Item("Expression").ToString <> "" Then
                                        If drMap.Item("Expression") = sFieldCodeSource Then
                                            'the field is mapped
                                            sFieldCodeDest = drMap.Item("SQLFieldName")
                                            If sFieldCodeDest.Contains("(%") Then
                                                'This is the key row in dtMap - not the mapped row
                                                sFieldCodeDest = ""
                                            Else
                                                'This is the mapped row, exit map
                                                Exit For
                                            End If
                                        End If
                                    End If
                                Next
                                'Now we have sFieldCode = (%City%) and sFieldCodeDest = TXT_CityMailing
                                If sFieldCodeDest <> "" Then
                                    'Get source field value from source datatable
                                    If Not IsDBNull(drTrans(sFieldCodeDest)) Then
                                        sFieldVal = drTrans(sFieldCodeDest)
                                    Else
                                        sFieldVal = ""
                                    End If
                                Else
                                    sFieldVal = ""
                                End If
                                sSourceKeyExpression = Replace(sSourceKeyExpression, sFieldCodeSource, sFieldVal)

                            Catch ex2 As Exception
                                Debug.WriteLine(ex2.Message)
                            End Try
                        End If
                    Else
                        Exit Do
                    End If
                Loop
                drTrans("UniqueKey") = sSourceKeyExpression
            Next

            Return True

        Catch ex As Exception

            'LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
            Return False

        End Try

    End Function

    Private Function GetLSTValue(ByVal sSQLTableName As String, ByVal sFieldCode As String, ByVal sValue As String, Optional ByVal par_bCreateMissingLSTValues As Boolean = False) As Integer

        'PURPOSE:
        '		Lookup for MLS_ fields
        'CALLED FROM:
        '       GetSourceValue
        'PARAMETERS:
        '		sSQLTableName: This is the name of the destination SQL table
        '       sFieldCode: SQL field name
        '       sValue: value to lookup in LST metadata. Ex, 'Open', 'Customer' (not integer values)
        '       par_bCreateMissingLSTValues:    True to create LST_ metadata values
        'RETURNS:
        '		Integer equal to LST_ TXT_Property
        '       -1 if value not found
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Try

            Dim oTR As New clTransform
            Dim sFilter As String = ""
            Dim sFieldNameNoPrefix As String = ""
            Dim dtLST As New DataTable
            Dim dvLST As New DataView
            Dim goP As clProject = HttpContext.Current.Session("goP")

            oTR = HttpContext.Current.Session("goTR")
            dtLST = goP.GetVar("ImportSQL_dtLST")

            'Ouch! not here!
            'dtLST = GetLSTMetadata(sSQLTableName)

            sFieldNameNoPrefix = Replace(sFieldCode, "MLS_", "")
            'Need to handle ' in filter, as for case of "Don't know"
            sValue = oTR.PrepareForSQL(sValue)

            sFilter = "TXT_Page = 'LST_" & sSQLTableName & ":" & sFieldNameNoPrefix & "' AND TXT_Value = '" & goTR.PrepareForSQL(sValue) & "' AND TXT_Property <> 'Default'"

            dtLST.DefaultView.RowFilter = sFilter
            dvLST = dtLST.DefaultView

            'If dtLST.Rows.Count = 1 Then
            If dvLST.Count > 0 Then
                'Return dtLST.Rows(0).Item("TXT_Property")   'to int
                Return dvLST(0).Item("TXT_Property")
            Else


                'For MLS_ fields, lookup values in LST metadata and add if missing
                '   From top of function, we have dtLST which contains TXT_Page, TXT_Property, TXT_Value, so need to
                '   filter the dt for TXT_Page=LST_[File]:[Field]  (Field name without prefix)
                If par_bCreateMissingLSTValues Then
                    'If sFieldName.StartsWith("MLS_") Then
                    'Add LST_ line to MD
                    '   Get highest index
                    Dim iHighestIndex As Integer = 0
                    Dim sProperty As String = ""
                    Dim sPage As String = ""
                    For Each row As DataRow In dtLST.Rows
                        sPage = "LST_" & FileName & ":" & goTR.FromTo(sFieldCode, 5)
                        If UCase(row("TXT_Page")) = UCase(sPage) Then
                            sProperty = row("TXT_Property").ToString
                            'If sProperty.Contains("_") Then
                            'get integer to right of _
                            If IsNumeric(sProperty) Then
                                If CInt(sProperty) > iHighestIndex And sProperty <> "99" Then
                                    iHighestIndex = CInt(sProperty)
                                End If
                            End If
                            'End If
                        End If
                    Next row
                    'Add LST value
                    goMeta.LineWrite("", sPage, "US_" & (iHighestIndex + 1).ToString, sValue, , , "XX")
                    LogLine(1, "Added item '" & sValue & "' for list " & sFieldCode, "OK")

                    'Get updated MD of list
                    dtLST = GetLSTMetadata(FileName)
                    goP.SetVar("ImportSQL_dtLST", dtLST)
                    Return iHighestIndex + 1

                Else

                    Return -1
                End If
            End If


        Catch ex As Exception

            'LogLine(1, sProc & ": " & ex.Message & "; Error evaluating MLS field, " & sFieldCode, "Failed")
            Return -1

        End Try

    End Function

    Public Function GetLSTMetadata(ByVal sSQLTableName As String) As DataTable

        'PURPOSE:
        '		Load LST_ metadata for MLS_ lookup. Loaded once at beginning of import session
        'CALLED FROM:
        '       Initialize
        'PARAMETERS:
        '		sSQLTableName: This is the name of the destination SQL table
        'RETURNS:
        '		Datatable
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Dim dt As New DataTable
        Dim settings As String = HttpContext.Current.Session("Connection")
        Dim sqlConnection1 As New Data.SqlClient.SqlConnection(settings)

        Try

            sqlConnection1.Open()

            Dim cmd As New SqlClient.SqlCommand
            Dim reader As SqlClient.SqlDataReader
            Dim mColl As New Collection
            Dim sSQL As String = ""
            Dim sTable As String = sSQLTableName
            'testing
            'Dim sTable As String = "CO"


            'sSQL = "SELECT * FROM" & modGlobal.sDestinationTable
            sSQL = "SELECT TXT_Page, TXT_Property, TXT_Value FROM MD WHERE TXT_Page LIKE 'LST_" & sSQLTableName & "%'"

            'Try
            cmd.CommandText = sSQL
            cmd.CommandType = CommandType.Text
            cmd.Connection = sqlConnection1

            reader = cmd.ExecuteReader()

            If reader.HasRows Then

                dt.Load(reader)

            Else
                '==> raise error
            End If

            reader.Close()
            sqlConnection1.Close()

            dt.TableName = "LSTMetadata"

            Return dt
        Catch ex As Exception

            'LogLine(0, sProc & ": " & ex.Message, "Failed")
            Return Nothing

        End Try

    End Function

    Public Function LogLine(ByVal iLogLevel As String, ByVal sProcess As String, ByVal sStatus As String) As Boolean

        'PURPOSE:
        '		Write lines to log datatable
        'CALLED FROM:
        '       everywhere
        'PARAMETERS:
        '		iLogLevel:  0: Process start and end
        '                   1: warning or failure of individual data methods, non-critical
        'RETURNS:
        '		True/False
        'AUTHOR: PJ

        'This would be edited in each project depending on how we need to log.
        'Perhaps all logging is done to the status table????

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Try

            Dim oP As clProject = HttpContext.Current.Session("goP")
            Dim sID As String = HttpContext.Current.Session.LCID
            Dim sIMPJobID As String = oP.GetVar("ImportSQL_sIMPJobID")

            'Testing:
            Dim sdtLog As String = oP.GetVar("ImportSQL_dtLog").ToString
            '------
            Dim dtLog As DataTable = oP.GetVar("ImportSQL_dtLog")

            Dim aLine(3) As Object

            aLine(0) = DateTime.Now.ToString
            aLine(1) = iLogLevel
            aLine(2) = sProcess
            aLine(3) = sStatus

            dtLog.Rows.Add(aLine)

            oP.SetVar("ImportSQL_dtLog", dtLog)

        Catch ex As Exception

            'LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
            Return False

        End Try

    End Function

    Public Function SQLBulkAdd(ByVal par_sSQLTableName As String, ByVal par_dtAdd As DataTable, ByVal sIMPJobID As String) As Boolean

        'PURPOSE:
        '		Use SQLBulkCopy method to add NEW records to destination SQL table
        'CALLED FROM:
        '       Public: web service consumer
        'PARAMETERS:
        '		par_sSQLTableName: name of destination SQL table
        '       par_dtAdd:  Datatable to be bulk added
        '       sImpJobID:  Import job ID
        'RETURNS:
        '		True/False
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        'LogLine(0, "SQLBulkCopy", "Started")
        goTR = HttpContext.Current.Session("goTr")
        goP = HttpContext.Current.Session("goP")

        If goP Is Nothing Then
            LogLine(0, sProc & ": Unable to retreive session objects", "Failed")
            Return False
        End If

        Dim dtAdd As DataTable
        If par_dtAdd Is Nothing Then
            dtAdd = goP.GetVar("ImportSQL_dtTransformedNew")
        Else
            dtAdd = par_dtAdd
        End If

        ''Dim dtAdd As DataTable = goP.GetVar("ImportSQL_dtTransformedNew")
        If dtAdd Is Nothing Then
            LogLine(0, sProc & ": Add table does not exist", "Failed")
            Return False
        Else
            If dtAdd.Rows.Count = 0 Then
                LogLine(0, sProc & ": Add table does not contain any rows", "OK")
                Return True
            End If
        End If

        Try

            'Get highest BI__ID in this file, for sys_name processing. HOW WORK FOR MULTIPLE PASSES OF SAME FILE OR MULT FILES?
            Dim oRS As clRowSet
            oRS = New clRowSet(par_sSQLTableName, 3, , "BI__ID Desc", "BI__ID", 1)
            If oRS.GetFirst = 1 Then
                iHighestBIID = goTR.StringToNum(oRS.GetFieldVal("BI__ID"))
            Else
                iHighestBIID = 0
            End If


            Dim settings As String = HttpContext.Current.Session("Connection")
            'sConnectionString = "Data Source=PSSQL001\SQL2005;Initial Catalog=PJTest;User ID=sa;Password=********;connect timeout=60"
            Dim sqlConnection1 As New Data.SqlClient.SqlConnection(settings)
            sqlConnection1.Open()

            Dim oSBC As System.Data.SqlClient.SqlBulkCopy = New System.Data.SqlClient.SqlBulkCopy(settings, System.Data.SqlClient.SqlBulkCopyOptions.FireTriggers)

            oSBC.DestinationTableName = "[" & par_sSQLTableName & "]"
            oSBC.BulkCopyTimeout = 300

            'dtTransformed col names are SQL field names so source and dest mapping is the same (dtTrans colname)
            Dim cm As SqlClient.SqlBulkCopyColumnMapping
            For i As Integer = 0 To dtAdd.Columns.Count - 1
                cm = oSBC.ColumnMappings.Add(dtAdd.Columns(i).ColumnName, dtAdd.Columns(i).ColumnName)
            Next

            'Break this into 1,000 record increments and update status table
            'AddHandler oSBC.SqlRowsCopied, AddressOf OnSBCRowsImported     '<<< Will need to be done the ASP way
            oSBC.NotifyAfter = 1000

            Try
                oSBC.WriteToServer(dtAdd)

                LogLine(0, "Added " & dtAdd.Rows.Count & " new records in table " & par_sSQLTableName, "OK")

            Catch ex1 As Exception

                'LogLine(0, "Bulk copy: Error writing dtAdd to SQL table for table " & par_sSQLTableName & ". " & ex1.Message, "Failed")
                Return False

            End Try

            'LogLine(0, "SQL Bulk Copy: Imported " & dtAdd.Rows.Count & " records", "OK")

            'Destroy datatable
            dtAdd = Nothing
            goP.SetVar("ImportSQL_dtTransformedNew", Nothing)

            sqlConnection1.Close()

            Return True

        Catch ex As Exception

            'LogLine(0, "SQL Bulk Copy: " & ex.Message, "Failed")
            Return False

        End Try


    End Function

    Public Function SQLEdit(ByVal sSQLTableName As String, _
                     ByVal sSQLTempTableName As String, _
                     ByVal dtMap As DataTable, _
                     ByVal sIMPJobID As String) As Boolean

        'PURPOSE:
        '		Use SQLBulkCopy method to UPDATE records in destination SQL table.
        '       This is done by creating a temp table in SQL db, bulk copying datatable to this temp table
        '       and then executing an UPDATE statement, then dropping the temp table
        'CALLED FROM:
        '       Public: web service consumer
        'PARAMETERS:
        '		sSQLTableName: name of destination SQL table
        '       sSQLTempTableName: name of the temporary SQL table. will be dropped after UPDATE
        '       dtMap: map table
        '       sIMPJobID:  Import job ID
        'RETURNS:
        '		True/False
        'AUTHOR: PJ

        'LogLine(0, "SQLBulkEdit", "Started")

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Dim dtEdit As DataTable = Nothing
        Dim sFields As String = ""
        Dim sValues As String = ""
        Dim sSQL As String = ""
        Dim sSet As String = ""
        Dim iErrorCount As Integer = 0

        Dim aKeys() As String = Nothing
        Dim sDestKeyColumnName As String = ""
        Dim bKeysExist As Boolean = False
        Dim iKeyCount As Integer = -1
        Dim iUseExtID As Integer = 0
        Dim sKeyField As String = ""
        Dim sWherePart As String = ""

        Dim goP As clProject = HttpContext.Current.Session("goP")
        Dim goTR As clTransform = HttpContext.Current.Session("goTR")

        'Try
        If goP Is Nothing Then
                LogLine(0, sProc & ": Unable to retreive session objects", "Failed")
                Return False
            End If

            dtEdit = goP.GetVar("ImportSQL_dtTransformedUpd")

            If dtEdit Is Nothing Then
                LogLine(0, sProc & ": Edit table does not exist", "Failed")
                Return False
            Else
                If dtEdit.Rows.Count = 0 Then
                    LogLine(0, sProc & ": Edit table does not contain any rows", "OK")
                    Return True
                End If
            End If

            If dtMap Is Nothing Then
                dtMap = goP.GetVar("ImportSQL_dtMap")
            End If
            If dtMap Is Nothing Then
                LogLine(0, sProc & ": Unable to read map data", "Failed")
                Return False
            End If

            'NEW WAY
            'key field is always GID_ID
            sKeyField = "GID_ID"

        ''OLD WAY
        ''aKeys contians key field(s) used in WHERE statement for SQL UPDATE below
        ''Separate function ---
        'For i As Integer = 0 To dtMap.Rows.Count - 1
        '    If Not IsDBNull(dtMap.Rows(i).Item("Key")) Then
        '        If dtMap.Rows(i).Item("Key") = True Then
        '            sDestKeyColumnName = dtMap.Rows(i).Item("SQLFieldName")
        '            iKeyCount += 1
        '            ReDim Preserve aKeys(iKeyCount)
        '            aKeys(iKeyCount) = sDestKeyColumnName
        '            bKeysExist = True
        '            If sDestKeyColumnName = "TXT_ExternalID" Or sDestKeyColumnName = "TXT_ExternalID" Then
        '                iUseExtID += 1  'If this = 2 then we know to use TN table with external IDs
        '            End If
        '            'Temporary while multiple key system is worked out
        '            sKeyField = sDestKeyColumnName
        '        End If
        '    End If
        'Next
        '------------

        'If aKeys.Length = 0 Then
        '    LogLine(0, sProc & ": Key field(s) not defined. Update stopped", "Failed")
        '    Return False
        'End If
        'Catch ex As Exception

        '    LogLine(0, sProc & ": " & ex.Message, "Failed")

        'End Try

        Dim settings As String = HttpContext.Current.Session("Connection")
        Dim sqlConnection1 As New Data.SqlClient.SqlConnection(settings)
        sqlConnection1.Open()

        'Drop table if already exists
        'Try
        Dim cmd1 As New System.Data.SqlClient.SqlCommand
            sSQL = "DROP TABLE " & sSQLTempTableName
            cmd1.CommandText = sSQL
            cmd1.CommandType = System.Data.CommandType.Text
            cmd1.Connection = sqlConnection1
            If cmd1.ExecuteNonQuery() = 1 Then

            End If

        'Catch ex As Exception
        '    'Do nothing. Table may not exist

        'End Try

        'Create temp table
        Try
            Dim cmd2 As New System.Data.SqlClient.SqlCommand
            sSQL = "SELECT TOP 0 * INTO " & sSQLTempTableName & " FROM " & sSQLTableName & vbCrLf
            'V2: Don't drop fields, where clause depends on GID_ID now
            'sSQL += "ALTER TABLE " & sSQLTempTableName & vbCrLf
            'sSQL += "DROP COLUMN GID_ID" & vbCrLf
            'sSQL += "ALTER TABLE " & sSQLTempTableName & vbCrLf
            'sSQL += "DROP COLUMN BI__ID"
            'V2: Need to allow nulls in GID_ID and BI__ID
            sSQL += "ALTER TABLE " & sSQLTempTableName & vbCrLf
            sSQL += "ALTER COLUMN GID_ID varchar(50) NULL"

            cmd2.CommandText = sSQL
            cmd2.CommandType = System.Data.CommandType.Text
            cmd2.Connection = sqlConnection1
            If cmd2.ExecuteNonQuery() = 1 Then

            End If
            'LogLine(0, "Creating SQL edit table", "OK")
        Catch ex As Exception

            'LogLine(0, sProc & ": Error creating SQL edit table", "Failed")
            Return False

        End Try

        'Upload the datatable to temp table
        Try
            Dim oSBC As System.Data.SqlClient.SqlBulkCopy = New System.Data.SqlClient.SqlBulkCopy(settings, System.Data.SqlClient.SqlBulkCopyOptions.FireTriggers)

            oSBC.DestinationTableName = sSQLTempTableName
            oSBC.BulkCopyTimeout = 300

            'dtTransformed col names are SQL field names so source and dest mapping is the same (dtTrans colname)
            oSBC.ColumnMappings.Clear()
            For i As Integer = 0 To dtEdit.Columns.Count - 1
                'Dim sTest As String = dtEdit.Columns(i).ColumnName
                'V2: using GID_ID as key, do not map
                'If dtEdit.Columns(i).ColumnName <> "GID_ID" Then
                oSBC.ColumnMappings.Add(dtEdit.Columns(i).ColumnName, dtEdit.Columns(i).ColumnName)
                'End If
            Next

            'Break this into 1,000 record increments and update status table
            'AddHandler oSBC.SqlRowsCopied, AddressOf OnSBCRowsImported     '<<< Will need to be done the ASP way
            oSBC.NotifyAfter = 1000

            oSBC.WriteToServer(dtEdit)  'DEGUG this 

            'LogLine(0, "Uploading edit datatable to SQL", "OK")

        Catch ex As Exception

            'LogLine(0, sProc & ": Error uploading edit datatable to SQL", "Failed")
            Return False

        End Try

        'Run update

        'String of fields to update
        '= ISNULL(  will only update non-null values from source data
        'May want to have option to clear all fields and import new values only. In this case, drop the '= ISNULL('
        For i As Integer = 0 To dtEdit.Columns.Count - 1
            Dim sColumnName As String = dtEdit.Columns(i).ColumnName
            'V2: GID_ID not mapped
            If sColumnName <> "GID_ID" Then
                If sSet <> "" Then
                    sSet += ", " & vbCrLf
                End If
                sSet += sSQLTableName & "." & sColumnName & _
                    " = ISNULL(" & sSQLTempTableName & "." & sColumnName & _
                    ", " & sSQLTableName & "." & sColumnName & ")"
            End If
        Next
        sSet += vbCrLf

        Try
            Dim cmd3 As New System.Data.SqlClient.SqlCommand
            sSQL = "UPDATE " & sSQLTableName & vbCrLf
            sSQL += "SET " & vbCrLf
            sSQL += sSet
            sSQL += "FROM " & sSQLTableName & ", " & sSQLTempTableName & vbCrLf

            '2/16: this needs to allow for multiple key fields:
            'sSQL += "WHERE " & sSQLTableName & "." & sKeyField & " = " & sSQLTempTableName & "." & sKeyField

            sSQL += "WHERE "

            'OLD WAY
            'For i As Integer = 0 To aKeys.GetUpperBound(0)
            '    If sWherePart = "" Then
            '        sWherePart = sSQLTableName & "." & aKeys(i) & " = " & sSQLTempTableName & "." & aKeys(i)
            '    Else
            '        sWherePart += " AND " & sSQLTableName & "." & aKeys(i) & " = " & sSQLTempTableName & "." & aKeys(i)
            '    End If
            'Next

            'NEW WAY
            sWherePart = "CAST (" & sSQLTableName & ".GID_ID AS VARCHAR(50))" & " = " & sSQLTempTableName & ".GID_ID"

            sSQL += sWherePart

            '------------
            cmd3.CommandText = sSQL
            cmd3.CommandType = System.Data.CommandType.Text
            cmd3.Connection = sqlConnection1
            If cmd3.ExecuteNonQuery() = 1 Then

            End If

            LogLine(0, "Edited " & dtEdit.Rows.Count.ToString & " records", "OK")

        Catch ex As Exception

            'LogLine(0, sProc & ": Error updating SQL target table", "Failed")
            Return False

        End Try

        'Drop table
        Try
            Dim cmd2 As New System.Data.SqlClient.SqlCommand
            sSQL = "DROP TABLE " & sSQLTempTableName
            cmd2.CommandText = sSQL
            cmd2.CommandType = System.Data.CommandType.Text
            cmd2.Connection = sqlConnection1
            If cmd2.ExecuteNonQuery() = 1 Then

            End If

            'LogLine(0, "Dropping SQL edit table", "OK")

        Catch ex As Exception

            'LogLine(0, sProc & ": Error dropping temp SQL table", "Failed")
            Return False

        End Try

        'Destroy datatable
        dtEdit = Nothing
        goP.SetVar("ImportSQL_dtTransformedUpd", Nothing)

        sqlConnection1.Close()
        Return True


    End Function

    Public Function SQLBulkDelete(ByVal sSQLTableName As String, ByVal sCondition As String) As Boolean

        'PURPOSE:
        '		Run sql delete command for table
        'CALLED FROM:
        '       
        'PARAMETERS:
        '		sSQLTableName: name of SQL table
        '       sCondition: this is the WHERE clause
        'RETURNS:
        '		True/False
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        LogLine(0, "SQLBulkDelete", "Started")

        Dim goP As clProject = HttpContext.Current.Session("goP")
        If goP Is Nothing Then
            LogLine(0, sProc & ": Unable to retreive session objects", "Failed")
            Return False
        End If

        Dim dt As New DataTable
        Dim settings As String = HttpContext.Current.Session("Connection")
        Dim sqlConnection1 As New Data.SqlClient.SqlConnection(settings)

        Try

            sqlConnection1.Open()

            Dim cmd As New SqlClient.SqlCommand
            Dim mColl As New Collection
            Dim sSQL As String = ""
            Dim sTable As String = sSQLTableName

            sSQL = "DELETE " & sSQLTableName
            If sCondition <> "" Then
                sSQL += " WHERE " & sCondition
            End If

            cmd.CommandText = sSQL
            cmd.CommandType = CommandType.Text
            cmd.Connection = sqlConnection1

            'If cmd.ExecuteNonQuery() = 1 Then
            '    sqlConnection1.Close()
            '    Return True
            'Else
            '    sqlConnection1.Close()
            '    Return False
            'End If

            'Above return value is # of records deleted, so just call and then need to check results in calling code
            cmd.ExecuteNonQuery()

        Catch ex As Exception

            'LogLine(0, sProc & ": " & ex.Message, "Failed")
            Return False

        End Try

        Return True

    End Function

    Function CreateTempFields(ByVal sFileName As String) As Boolean

        'This should be replaced by individual temp fields, not all fields

        Dim oSchema As New clSchema
        Dim bResult As Boolean

        bResult = oSchema.SchemaAddFields(sFileName & "|" & sFileName)

        Return bResult

    End Function
    Function DeleteTempFields(ByVal sFileName As String) As Boolean

        Dim oSchema As New clSchema
        Dim bResult As Boolean

        bResult = oSchema.SchemaRemoveFields(sFileName & "|" & sFileName)

        Return bResult

    End Function
    Function PostProcessImportData(ByVal sFileName As String, ByVal sImportJobID As String) As Boolean

        Dim oSchema As New clSchema
        Dim bResult As Boolean

        bResult = oSchema.ImportProcessFields(sFileName & "|" & sFileName, , , , True, True, sImportJobID)

        Return bResult

    End Function

    Public Function TestCreateSessionVar(ByVal sValue As String) As Boolean

        Dim oP As clProject = HttpContext.Current.Session("goP")

        oP.SetVar("ImportSQL_Test", sValue)
        Dim s As String = oP.GetVar("ImportSQL_Test")

        Return True

    End Function

    Public Function TestGetSessionVar() As String

        Dim oP As clProject = HttpContext.Current.Session("goP")

        Dim s As String = oP.GetVar("ImportSQL_Test")
        Return oP.GetVar("ImportSQL_Test")

    End Function

    Private Function GetTableSchemaDatatable(ByVal sSQLTableName As String) As DataTable 'As Boolean

        'PURPOSE:
        '		Get destination SQL table schema as empty datatable
        'PARAMETERS:
        '		sSQLTableName: This is the name of the destination SQL table
        'RETURNS:
        '		0-row datatable
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Dim dtTableSchema As New DataTable
        Dim settings As String = HttpContext.Current.Session("Connection")
        Dim sqlConnection1 As New Data.SqlClient.SqlConnection(settings)

        'LogLine(0, sProc & "Getting SQL table schema", "Started")
        goData = HttpContext.Current.Session("goData")

        Try
            'reload schema
            goData.LoadSchemaData()

            sqlConnection1.Open()

            Dim cmd As New SqlClient.SqlCommand
            Dim reader As SqlClient.SqlDataReader
            Dim mColl As New Collection
            Dim sSQL As String = ""
            Dim sSQL2 As String = ""
            Dim sTable As String = sSQLTableName

            sSQL = "SELECT TOP 1 * FROM [" & sTable & "]"

            Try
                cmd.CommandText = sSQL
                cmd.CommandType = CommandType.Text
                cmd.Connection = sqlConnection1

                reader = cmd.ExecuteReader()

                If reader.HasRows = False Then
                    'close reader
                    reader.Close()
                    'SQL table has 0 rows, need to add 1 row to get columns returned
                    sSQL2 = "INSERT INTO [" & sTable & "] (SYS_NAME)" & vbCrLf & _
                        "VALUES ('zTest')"
                    cmd.CommandText = sSQL2
                    cmd.CommandType = CommandType.Text
                    cmd.Connection = sqlConnection1
                    cmd.ExecuteNonQuery()

                    cmd.CommandText = sSQL
                    cmd.CommandType = CommandType.Text
                    cmd.Connection = sqlConnection1
                    reader = cmd.ExecuteReader()

                End If
            Catch ex1 As Exception

                'LogLine("0", sProc & ": " & ex1.Message, "Failed")
                Return Nothing

                'SQL table has 0 rows, need to add 1 row to get columns returned
                'sSQL2 = "INSERT INTO " & sTable & " (SYS_NAME)" & vbCrLf & _
                '    "VALUES (zTest)"
                'cmd.CommandText = sSQL2
                'cmd.CommandType = CommandType.Text
                'cmd.Connection = sqlConnection1
                'cmd.ExecuteNonQuery()


            End Try

            'Remove zTest row
            '?
            If reader.HasRows Then

                dtTableSchema.Load(reader)

                'if Selltis structure, 
                'Try
                dtTableSchema.Columns("GID_ID").AllowDBNull = True
                    dtTableSchema.Columns("BI__ID").AllowDBNull = True
                'Catch
                '    'Ignore: table doesn't have these fields
                'End Try

            Else
                '==> raise error
            End If

            reader.Close()
            sqlConnection1.Close()

            'LogLine(0, sProc & "Getting SQL table schema", "OK")

            Return dtTableSchema
        Catch ex As Exception

            'LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
            Return Nothing

        End Try

    End Function

    Private Function LoadRecordIDs(ByVal sSQLTableName As String, ByVal iKeyType As Integer, ByVal aFields() As String) As DataTable 'As Boolean

        'PURPOSE:
        '		Get record ID (GID_ID) and key columns from SQL destination table
        'CALLED FROM:
        '       TransformTableSplit
        'PARAMETERS:
        '		sSQLTableName: This is the name of the destination SQL table
        '       sFields: comma delimited string of fields to return. These are the fields used to determine key value
        'RETURNS:
        '		datatable of GID_IDs and key column(s)
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Dim dtTableSchema As New DataTable
        Dim settings As String = HttpContext.Current.Session("Connection")
        Dim sqlConnection1 As New Data.SqlClient.SqlConnection(settings)

        'LogLine(0, sProc & "Getting SQL record IDs", "Started")

        Try

            sqlConnection1.Open()

            Dim cmd As New SqlClient.SqlCommand
            Dim reader As SqlClient.SqlDataReader
            Dim mColl As New Collection
            Dim sSQL As String = ""
            Dim sTable As String = sSQLTableName
            Dim sFields As String = ""

            'testing
            'Dim sTable As String = "CO"

            Select Case iKeyType
                Case 0, 2
                    'Split string array of SQL fields
                    For z As Integer = 0 To aFields.GetUpperBound(0)
                        If aFields(z) <> "" Then
                            If sFields = "" Then
                                sFields = aFields(z)
                            Else
                                sFields += ", " & aFields(z)
                            End If
                        End If
                    Next

                    If sFields = "" Then Return Nothing

                    ''V2: sFields may contain multiple field codes (%TXT_NameFirst%) (%TXT_NameLast%)
                    ''   so need to parse sFields and strip (%%) chars
                    'Dim iStart As Integer = 1
                    'Dim iEnd As Integer = 1
                    'Dim sFieldCode As String = ""
                    'Dim sFieldName As String = ""
                    'Dim aFieldName As New clArray

                    'Replace source field values with field values from source data
                    'Get instances of (% followed by %)
                    '5/27/11: aFields has field names already parsed
                    'Do While iStart > 0
                    '    iStart = InStr(sFields, "(%")
                    '    If iStart > 0 Then
                    '        iEnd = InStr(sFields, "%)") + 1
                    '        If iEnd > 0 Then
                    '            sFieldCode = Mid(sFields, iStart, iEnd - iStart + 1)
                    '            'Replace
                    '            'sFieldVal = GetSourceValue(sSQLTableName, dtSource, sFieldCode, iRow, sDestFieldName, iOffsetMinutes)
                    '            sFieldName = Replace(sFieldCode, "(%", "")
                    '            sFieldName = Replace(sFieldName, "%)", "")
                    '            aFieldName.Add(sFieldName)
                    '            sFields = Replace(sFields, sFieldCode, "")
                    '            'If Mid(sFieldVal, 1, 5) <> "Error" Then
                    '            'sFields = Replace(sFields, sFieldCode, GetSourceValue(sSQLTableName, dtSource, sFieldCode, iRow, sDestFieldName, iOffsetMinutes))    '<<enuf to keep rows aligned properly??
                    '            'sFields = Replace(sFields, sFieldCode, sFieldVal)
                    '            'Else
                    '            'sFields = sFieldVal 'this is the error
                    '            'End If
                    '        End If
                    '    End If
                    'Loop
                    'Put sFieldName  into sFields string
                    ''sFields = ""
                    ''For i As Integer = 0 To aFields.GetUpperBound(0)
                    ''    If aFields(i) <> "" Then
                    ''        If sFields = "" Then
                    ''            sFields = aFields(i)
                    ''        Else
                    ''            sFields += "," & aFields(i)
                    ''        End If
                    ''    End If
                    ''Next
                    'V2: END
                    '---------------

                    'don't want GID_ID in table, because can't have it null when adding new ID's
                    'Bah Humbug. We do want it. Not adding keys to dtKey anymore since dtSource is deduped
                    sFields = sFields & ", GID_ID"

                    'sSQL = "SELECT * FROM" & modGlobal.sDestinationTable
                    sSQL = "SELECT " & sFields & " FROM " & sTable

                Case 1
                    'Use TN table
                    sSQL = "SELECT TXT_ExternalID, TXT_ExternalSource, GID_InternalID FROM TN" & _
                            " WHERE TXT_File = '" & sSQLTableName & "'"

                Case Else

            End Select
            'Try
            If sSQL = "" Then Return Nothing

            cmd.CommandText = sSQL
            cmd.CommandType = CommandType.Text
            cmd.Connection = sqlConnection1

            reader = cmd.ExecuteReader()

            If reader.HasRows Then

                dtTableSchema.Load(reader)

            Else
                '==> raise error
            End If

            reader.Close()
            sqlConnection1.Close()
            'LoadFieldData = True

        Catch ex As SqlTypes.SqlTypeException

            'LogLine(0, "LoadRecordIDs: " & ex.Message, "Failed")
            Return Nothing

        End Try

        Return dtTableSchema

    End Function

    Private Function NormalizeValue(ByVal sText As String, ByVal par_s1 As String) As String

        'Company name only for now
        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Dim sReturn As String = sText
        Dim iPosStart As Integer = 0
        Dim iPosEnd As Integer = 0
        Dim sSuffix As String = ""
        Dim sName As String = ""

        'Temporary
        'par_s1 = "CO"
        'Try

        Select Case UCase(par_s1)

                Case "CO"
                    'Get suffix. Suffix defined as characters following the last space char
                    iPosStart = InStrRev(sText, " ")
                    If iPosStart > 0 Then
                        'there is a space. what's to the right?
                        sSuffix = Right(sText, Len(sText) - iPosStart + 1)
                        sName = Left(sText, iPosStart - 1)
                        Select Case UCase(sSuffix)
                            Case " LLC", " L.L.C.", " INC", " INC.", " INCORPORATED", " CORPORATION", " CO", " CO.", " COMP", " COMP.", " COMPANY", " CORP", " CORP.", " LTD", " LTD."
                                sReturn = sName
                            Case Else
                                sReturn = sText
                        End Select
                    End If

                    'Remove any chars inside parens (). Like 'ChemoPharm Labratories (CP Industries)'
                    iPosStart = 0
                    iPosEnd = 0
                    iPosStart = InStr(sReturn, "(")
                    If iPosStart > 0 Then
                        iPosEnd = InStr(sReturn, ")")
                        If iPosEnd > iPosStart Then
                            'there is a pair of parens
                            sReturn = Mid(sReturn, 1, iPosStart - 1) & Mid(sReturn, iPosEnd + 1)
                        End If
                    End If

                Case "CN"


                Case Else

                    sReturn = sText

            End Select

            'Remove punctuation and non-alphanumerics
            sReturn = Replace(sReturn, "!", "")
            sReturn = Replace(sReturn, "@", "")
            sReturn = Replace(sReturn, "#", "")
            sReturn = Replace(sReturn, "$", "")
            sReturn = Replace(sReturn, "%", "")
            sReturn = Replace(sReturn, "^", "")
            sReturn = Replace(sReturn, "&", "")
            sReturn = Replace(sReturn, "*", "")
            sReturn = Replace(sReturn, "(", "")
            sReturn = Replace(sReturn, ")", "")
            sReturn = Replace(sReturn, "-", "")
            sReturn = Replace(sReturn, "+", "")
            sReturn = Replace(sReturn, "_", "")
            sReturn = Replace(sReturn, "=", "")
            sReturn = Replace(sReturn, "\", "")
            sReturn = Replace(sReturn, "|", "")
            sReturn = Replace(sReturn, "'", "")
            sReturn = Replace(sReturn, Chr(34), "")
            sReturn = Replace(sReturn, ",", "")
            sReturn = Replace(sReturn, ".", "")
            sReturn = Replace(sReturn, "?", "")
            sReturn = Replace(sReturn, "/", "")
            sReturn = Replace(sReturn, "~", "")
            sReturn = Replace(sReturn, ">", "")
            sReturn = Replace(sReturn, "<", "")

            'Remove spaces
            sReturn = Replace(sReturn, " ", "")

        'Catch ex As Exception

        '    LogLine(0, sProc & ": " & ex.Message, "Failed")

        'End Try

        Return sReturn

    End Function

    Public Function LoadDatatable(ByVal dt As DataTable, ByVal sInstructions As String, ByRef par_oReturn As Object) As Boolean

        'PURPOSE:
        '		Upload datatable of source data (in memory), processed by script (not mapped directly to a File)
        'CALLED FROM:
        '       ws consumer
        'PARAMETERS:
        '		dt: source datatable
        '       sInstructions: string including the function to run and parameters
        'RETURNS:
        '		True/False
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        Dim sReturn As String = ""

        Try

            'Call function to process data
            Dim goScr As clScrMngRowSet = HttpContext.Current.Session("goScr")
            goScr.RunScript("Import_ManageImports", dt, , sInstructions, , , , , sReturn)

            par_oReturn = sReturn

        Catch ex As Exception

            'LogLine(0, sProc & ": Error uploading datatable to Selltis server. " & ex.Message, "Failed")
            Return False

        End Try

        Return True

    End Function

    Public Function PostProcessLinkData() As Boolean

        'PURPOSE:
        '		Create link data for mapped links
        'CALLED FROM:
        '       calling UI
        'PARAMETERS:

        'RETURNS:
        '		True/False
        'AUTHOR: PJ

        Dim goSchema As New clSchema
        'Dim goSchemaPJ As New clSchemaPJ
        Dim bResult As Boolean
        Dim sSQL As String
        Dim sLinkName As String
        Dim sLinkKeyField As String
        Dim sTempField As String

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Try

            'Read Map to find mapped links and link key field
            For Each row As DataRow In dtMap.Rows
                If row("Expression").ToString <> "" Then
                    sLinkName = row("SQLFieldName")
                    If sLinkName.StartsWith("LNK_") Then
                        If IsDBNull(row("LinkKeyField")) Then
                            'log error
                            LogLine(0, "Process links: Key field for " & sLinkName & " is not set", "Failed")
                        Else
                            sLinkKeyField = row("LinkKeyField")

                            'Prefix link name with TXT_ or MMO_
                            If goTR.FromTo(goData.LKGetType(sFileName, sLinkName), 2, 2) = "1" Then
                                '1 link
                                sTempField = "TXT_" & sLinkName
                            Else
                                'N link
                                sTempField = "MMO_" & sLinkName
                            End If
                            sSQL = goSchema.GenerateLinkCreationSQL(sFileName, sTempField, , , True, sImpJobID, , , sLinkKeyField)
                            If sSQL <> "" Then
                                'execute query
                                bResult = goData.RunSQLQuery(sSQL)
                                If bResult Then
                                    LogLine(0, "Process links: " & sLinkName, "OK")
                                Else
                                    LogLine(0, "Process links: " & sLinkName, "Failed")
                                End If
                            End If
                        End If
                    End If
                End If
            Next

            'bResult = goSchema.ImportProcessFields(sFileName, , , , True, True, sImpJobID)
            'If bResult Then
            '    LogLine(0, "Process Links", "OK")
            'Else
            '    LogLine(0, "Process Links", "Failed")
            'End If

            Return True

        Catch ex As Exception

            'LogLine(0, sProc & vbCrLf & ex.Message, "Failed")
            Return False

        End Try

    End Function

    Public Function PostProcessSysNames() As Boolean

        'PURPOSE:
        '		Run sys_name creation
        'CALLED FROM:
        '       calling UI
        'PARAMETERS:

        'RETURNS:
        '		True/False
        'AUTHOR: PJ

        Dim goSchema As New clSchema
        Dim bResult As Boolean

        bResult = goSchema.ImportUpdateSysNames(sFileName & "|" & sFileName, iHighestBIID, , , , , )

        If bResult Then
            LogLine(0, "Generate SysNames", "OK")
        Else
            LogLine(0, "Generate SysNames", "Failed")
        End If

        Return True

    End Function

    Public Function ConvertDateTimes(ByVal sSQLTableName As String, ByVal sFieldName As String, ByVal par_iMinutes As Integer, Optional ByVal sImpJobID As String = "") As Boolean
        'CS added 2/9/10. HSI site update continually failed because this function was missing.
        'PURPOSE:
        '		Adjust times for GMT offset
        'CALLED FROM:
        '       
        'PARAMETERS:
        '		sSQLTableName: name of SQL table
        '       iTimeOffsetMinutes: minutes to adjust the time (6 hrs = 480 min)
        '       sImpJobID: value of TXT_ImpJobID used for filtering records to update
        'RETURNS:
        '		True/False
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Try

            'LogLine(0, "ConvertDateTimes", "Started")

            Dim goP As clProject = HttpContext.Current.Session("goP")
            If goP Is Nothing Then
                LogLine(0, sProc & ": Unable to retreive session objects", "Failed")
                Return False
            End If

            Dim dt As New DataTable
            Dim settings As String = HttpContext.Current.Session("Connection")
            Dim sqlConnection1 As New Data.SqlClient.SqlConnection(settings)

            sqlConnection1.Open()

            Dim cmd As New SqlClient.SqlCommand
            Dim sSQL As String = ""

            sSQL = "UPDATE [" & sSQLTableName & "]" & Chr(13) & Chr(10)
            sSQL = sSQL & "SET [" & sFieldName & "] = DateAdd(mi, " & par_iMinutes & ", [" & sFieldName & "])" & Chr(13) & Chr(10)
            sSQL = sSQL & "WHERE " & Chr(13) & Chr(10)
            sSQL = sSQL & "[" & sFieldName & "] is not null" & Chr(13) & Chr(10)
            sSQL = sSQL & " and [" & sFieldName & "] <> '1753-01-02 23:59:59.000'" & Chr(13) & Chr(10)
            If sImpJobID <> "" Then
                sSQL = sSQL & " and [TXT_ImpJobID = '" & sImpJobID & "'"
            End If

            cmd.CommandText = sSQL
            cmd.CommandType = CommandType.Text
            cmd.Connection = sqlConnection1

            If cmd.ExecuteNonQuery() > 0 Then
                sqlConnection1.Close()
                Return True
            Else
                sqlConnection1.Close()
                Return False
            End If

        Catch ex As Exception

            'LogLine(0, sProc & ": " & ex.Message, "Failed")
            Return Nothing

        End Try

    End Function

    Public Function RunSQLSelect(ByVal sSQLStatement As String, Optional ByVal sDataTableName As String = "") As DataTable

        'PURPOSE:
        '		Generic RunSQLSELECT
        'CALLED FROM:
        '       Initialize
        'PARAMETERS:
        '		sSQLStatement
        'RETURNS:
        '		Datatable
        'AUTHOR: PJ

        Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name

        Dim dt As New DataTable
        Dim settings As String = HttpContext.Current.Session("Connection")
        Dim sqlConnection1 As New Data.SqlClient.SqlConnection(settings)

        Try

            sqlConnection1.Open()

            Dim cmd As New SqlClient.SqlCommand
            Dim reader As SqlClient.SqlDataReader
            Dim mColl As New Collection

            'Try
            cmd.CommandText = sSQLStatement
            cmd.CommandType = CommandType.Text
            cmd.Connection = sqlConnection1

            reader = cmd.ExecuteReader()

            If reader.HasRows Then

                dt.Load(reader)

            Else
                '==> raise error
            End If

            reader.Close()
            sqlConnection1.Close()

            dt.TableName = sDataTableName

            Return dt
        Catch ex As Exception

            'LogLine(0, sProc & ": " & ex.Message, "Failed")
            Return Nothing

        End Try

    End Function

    Private Function RemoveFieldCodeTags(ByVal par_sValue As String) As String

        Dim sValue As String = par_sValue
        sValue = Replace(sValue, "(%", "")
        sValue = Replace(sValue, "%)", "")

        Return sValue

    End Function



End Class
