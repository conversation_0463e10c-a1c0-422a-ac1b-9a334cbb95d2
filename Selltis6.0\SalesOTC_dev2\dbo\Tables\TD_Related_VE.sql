﻿CREATE TABLE [dbo].[TD_Related_VE] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_ToDo_Related_Vendor_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_VE] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_Related_VE] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_TD_Related_VE] FOREIGN KEY ([GID_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_VE_Connected_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_Related_VE] NOCHECK CONSTRAINT [LNK_TD_Related_VE];


GO
ALTER TABLE [dbo].[TD_Related_VE] NOCHECK CONSTRAINT [LNK_VE_Connected_TD];


GO
CREATE CLUSTERED INDEX [IX_VE_Connected_TD]
    ON [dbo].[TD_Related_VE]([GID_TD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_Related_VE]
    ON [dbo].[TD_Related_VE]([GID_VE] ASC);

