﻿CREATE TABLE [dbo].[US_MEMBERUSERS_US] (
    [GID_ID]  UNIQUEIDENTIFIER CONSTRAINT [DF_US_MEMBERUSERS_US_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_US]  UNIQUEIDENTIFIER NOT NULL,
    [GID_US2] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_US_MEMBERUSERS_US] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_US_MEMBERUSERS_US] FOREIGN KEY ([GID_US2]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_RELATED_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[US_MEMBERUSERS_US] NOCHECK CONSTRAINT [LNK_US_MEMBERUSERS_US];


GO
ALTER TABLE [dbo].[US_MEMBERUSERS_US] NOCHECK CONSTRAINT [LNK_US_RELATED_US];


GO
CREATE NONCLUSTERED INDEX [IX_US_MEMBERUSERS_US]
    ON [dbo].[US_MEMBERUSERS_US]([GID_US] ASC, [GID_US2] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_RELATED_US]
    ON [dbo].[US_MEMBERUSERS_US]([GID_US2] ASC, [GID_US] ASC);

