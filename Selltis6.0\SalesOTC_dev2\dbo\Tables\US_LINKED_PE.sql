﻿CREATE TABLE [dbo].[US_LINKED_PE] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_US_LINKED_PE_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    [GID_PE] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_US_LINKED_PE] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PE_CONNECTED_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_LINKED_PE] FOREIGN KEY ([GID_PE]) REFERENCES [dbo].[PE] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[US_LINKED_PE] NOCHECK CONSTRAINT [LNK_PE_CONNECTED_US];


GO
ALTER TABLE [dbo].[US_LINKED_PE] NOCHECK CONSTRAINT [LNK_US_LINKED_PE];


GO
CREATE NONCLUSTERED INDEX [IX_US_LINKED_PE]
    ON [dbo].[US_LINKED_PE]([GID_US] ASC, [GID_PE] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PE_CONNECTED_US]
    ON [dbo].[US_LINKED_PE]([GID_PE] ASC, [GID_US] ASC);

