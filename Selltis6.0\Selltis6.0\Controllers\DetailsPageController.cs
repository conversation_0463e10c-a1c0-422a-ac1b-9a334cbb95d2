﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Data;
using Selltis.BusinessLogic;
using Selltis.Core;
using Selltis.MVC.Models;
using System.IO;

namespace Selltis.MVC.Controllers
{
    public class DetailsPageController : Controller
    {
        // GET: DetailsPage
        public ActionResult Index()
        {
            return View();
        }

        private clData goData;
        private clMetaData goMeta;
        private clTransform goTR;
        private clProject goP;
        

        public ActionResult CompanyDetails(string sCompanyId)
        {
            //ViewBag.scomid = sCompanyId;

            Selltis.MVC.Models.CompanyDetails _coDetails = new Models.CompanyDetails();

            goData = (clData)Util.GetInstance("data");
            System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
            System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
            System.Data.SqlClient.SqlDataReader reader = null;
            cmd.CommandText = "GetCompanyDetailsByID";
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Connection = sqlConnection1;

            //parameter
            System.Data.SqlClient.SqlParameter coId = new System.Data.SqlClient.SqlParameter("@COGidId", System.Data.SqlDbType.VarChar);
            coId.Value = sCompanyId;
            cmd.Parameters.Add(coId);

            reader = cmd.ExecuteReader();

            DataSet ds = new DataSet();

            DataTable dt1 = new DataTable();
            DataTable dt2 = new DataTable();
            DataTable dt3 = new DataTable();
            DataTable dt4 = new DataTable();
            DataTable dt5 = new DataTable();
            DataTable dt6 = new DataTable();
            DataTable dt7 = new DataTable();

            dt1.TableName = "dt1";
            dt2.TableName = "dt2";
            dt3.TableName = "dt3";
            dt4.TableName = "dt4";
            dt5.TableName = "dt5";
            dt6.TableName = "dt6";
            dt7.TableName = "dt7";

            ds.Tables.Add(dt1);
            ds.Tables.Add(dt2);
            ds.Tables.Add(dt3);
            ds.Tables.Add(dt4);
            ds.Tables.Add(dt5);
            ds.Tables.Add(dt6);
            ds.Tables.Add(dt7);

            ds.Load(reader, LoadOption.OverwriteChanges, dt1, dt2, dt3, dt4, dt5, dt6,dt7);

            //tiles
            if (dt1 != null && dt1.Rows.Count > 0)
            {
                _coDetails.ActiveOPCount = Convert.ToInt32(dt1.Rows[0]["ActiveOPCount"]);
                _coDetails.ActiveOPValue = Convert.ToDouble(dt1.Rows[0]["ActiveOPValue"]);
            }

            //Linked OPs
            if (dt2 != null && dt2.Rows.Count > 0)
            {
                _coDetails.LinkedOPs = new List<OPDetails>();
                _coDetails.OPCount = dt2.Rows.Count;
                foreach (DataRow _dr in dt2.Rows)
                {
                    OPDetails _newop = new OPDetails();
                    _newop.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _newop.OPName = Convert.ToString(_dr["TXT_OPPORTUNITYNAME"]);
                    _newop.LOB = Convert.ToString(_dr["LOB"]);
                    _newop.OPType = Convert.ToString(_dr["OPType"]);
                    _newop.OPOwner = Convert.ToString(_dr["OppOwner"]);
                    _newop.Stage = Convert.ToString(_dr["Stage"]);
                    _newop.Amount = Convert.ToDouble(_dr["CUR_AMOUNT"]);
                    _newop.ExpCloseDate = Convert.ToDateTime(_dr["DTT_ExpCloseDate"]);
                    _newop.TotalAmount = Convert.ToDouble(_dr["CUR_TOTALAMOUNT"]);
                    _newop.OppNumber = Convert.ToString(_dr["TXT_OPPNO"]);
                    _newop.Currency = Convert.ToString(_dr["Currency"]);
                    _coDetails.LinkedOPs.Add(_newop);
                }
            }

            //Linked CNs
            if (dt3 != null && dt3.Rows.Count > 0)
            {
                _coDetails.LinkedCNs = new List<CNDetails>();
                _coDetails.CNCount = dt3.Rows.Count;
                foreach (DataRow _dr in dt3.Rows)
                {
                    CNDetails _newcn = new CNDetails();
                    _newcn.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _newcn.CNName = Convert.ToString(_dr["Name"]);
                    _newcn.Title = Convert.ToString(_dr["TXT_TitleText"]);
                    _newcn.Email = Convert.ToString(_dr["Eml_Email"]);
                    _newcn.Phone = Convert.ToString(_dr["TEL_BusPhone"]);
                    _newcn.Mobile = Convert.ToString(_dr["TEL_CellPhone"]);
                    _newcn.ContactofUser = Convert.ToString(_dr["ContactofUser"]);

                    _coDetails.LinkedCNs.Add(_newcn);
                }
            }

            //Ac details
            if (dt4 != null && dt4.Rows.Count > 0)
            {
                _coDetails.LinkedACs = new List<ACDetails>();
                _coDetails.ACCount = dt4.Rows.Count;

                clUtil goUt = new clUtil();

                foreach (DataRow _dr in dt4.Rows)
                {
                    ACDetails _acdet = new ACDetails();
                    _acdet.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _acdet.Date = Convert.ToDateTime(_dr["DTT_StartTime"]);
                    _acdet.Notes = Convert.ToString(_dr["Notes"]);// goUt.StripHTML(Convert.ToString(_dr["Notes"]));
                    _acdet.Type = Convert.ToString(_dr["ACType"]);

                    _coDetails.LinkedACs.Add(_acdet);
                }
            }
            //Company Details
            if (dt5 != null && dt5.Rows.Count > 0)
            {
                _coDetails.CompanyName = Convert.ToString(dt5.Rows[0]["Txt_CompanyName"]);
                _coDetails.Gid_id = Convert.ToString(dt5.Rows[0]["Gid_id"]);
                _coDetails.NAVID = Convert.ToString(dt5.Rows[0]["TXT_NAVID"]);
                _coDetails.LOB = Convert.ToString(dt5.Rows[0]["TXT_NAVREGION"]);
                _coDetails.Address = Convert.ToString(dt5.Rows[0]["TXT_AddrMailing"]);
                _coDetails.PhoneNo = Convert.ToString(dt5.Rows[0]["TEL_PhoneNo"]);
                _coDetails.City = Convert.ToString(dt5.Rows[0]["TXT_CityMailing"]);
                _coDetails.State = Convert.ToString(dt5.Rows[0]["TXT_StateMailing"]);
                _coDetails.Zip = Convert.ToString(dt5.Rows[0]["TXT_ZipMailing"]);
                _coDetails.SiteType = Convert.ToString(dt5.Rows[0]["SiteType"]);
                _coDetails.CustNo = Convert.ToString(dt5.Rows[0]["Txt_CustNo"]);
                _coDetails.CompanyDescription = Convert.ToString(dt5.Rows[0]["Description"]);
                _coDetails.Billable = Convert.ToString(dt5.Rows[0]["Billable"]);
                _coDetails.Active = Convert.ToString(dt5.Rows[0]["Active"]);
                _coDetails.TeamLeader = Convert.ToString(dt5.Rows[0]["TeamLeader"]);
                _coDetails.ParentCompany = Convert.ToString(dt5.Rows[0]["ParentCompanyName"]);
                _coDetails.CreationDate = Convert.ToDateTime(dt5.Rows[0]["DTT_CreationTime"]);
                _coDetails.CreatedBy = Convert.ToString(dt5.Rows[0]["CreatedBy"]);
                _coDetails.ModifDate = Convert.ToDateTime(dt5.Rows[0]["DTT_ModTime"]);
                _coDetails.ModifBy = Convert.ToString(dt5.Rows[0]["ModBy"]);

                _coDetails.code = Convert.ToString(dt5.Rows[0]["TXT_CODE"]);
                _coDetails.Currency = Convert.ToString(dt5.Rows[0]["MLS_CURRENCY"]);
                _coDetails.AccountType = Convert.ToString(dt5.Rows[0]["MLS_ACCOUNTTYPE"]);
                _coDetails.Region = Convert.ToString(dt5.Rows[0]["MLS_REGION"]);
                _coDetails.WebPage = Convert.ToString(dt5.Rows[0]["URL_WEBPAGE"]);

                //_coDetails.CountryMail = Convert.ToString(dt5.Rows[0]["TXT_COUNTRYMAILING"]);
                 _coDetails.CountryMail = Convert.ToString(dt5.Rows[0]["County"]);

                _coDetails.AddrBill = Convert.ToString(dt5.Rows[0]["TXT_ADDRBILLING"]);
                _coDetails.CityBill = Convert.ToString(dt5.Rows[0]["TXT_CITYBILLING"]);
                _coDetails.StateBill = Convert.ToString(dt5.Rows[0]["TXT_STATEBILLING"]);
                // _coDetails.CountryBill = Convert.ToString(dt5.Rows[0]["TXT_COUNTRYBILLING"]);
                _coDetails.CountryBill = Convert.ToString(dt5.Rows[0]["CountyBilling"]);
                _coDetails.ZipBill = Convert.ToString(dt5.Rows[0]["TXT_ZIPBILLING"]);

                _coDetails.AddrShip = Convert.ToString(dt5.Rows[0]["TXT_ADDRSHIPPING"]);
                _coDetails.CityShip = Convert.ToString(dt5.Rows[0]["TXT_CITYSHIPPING"]);
                _coDetails.StateShip = Convert.ToString(dt5.Rows[0]["TXT_STATESHIPPING"]);
                // _coDetails.CountryShip = Convert.ToString(dt5.Rows[0]["TXT_COUNTRYSHIPPING"]);
                _coDetails.CountryShip = Convert.ToString(dt5.Rows[0]["CountyShipping"]);
                _coDetails.ZipShip = Convert.ToString(dt5.Rows[0]["TXT_ZIPSHIPPING"]);


                _coDetails.CREDITLIMIT = Convert.ToString(dt5.Rows[0]["CUR_CREDITLIMIT"]);
                _coDetails.CURRENTBALANCE = Convert.ToString(dt5.Rows[0]["CUR_CURRENTBALANCE"]);
                _coDetails.BLOCKED = Convert.ToString(dt5.Rows[0]["CUR_BLOCKED"]);
                _coDetails.CNAVID = Convert.ToString(dt5.Rows[0]["TXT_NAVID"]);
                _coDetails.CompanyDesc = Convert.ToString(dt5.Rows[0]["TXT_DESCRIPTION"]);

            }

            _coDetails.Key = Guid.NewGuid().ToString();

            //OP count by status
            if (dt6 != null && dt6.Rows.Count > 0)
            {
                int _totalCount = 0;
                for (int i = 0; i < dt6.Rows.Count; i++)
                {
                    int _istage = Convert.ToInt32(dt6.Rows[i]["MLS_Stage"]);
                    int _count = Convert.ToInt32(dt6.Rows[i]["OPCount"]);
                    _totalCount = _totalCount + _count;
                    if (_istage == 1)
                    {
                        _coDetails.OPCount_Creating = _count;
                    }
                    else if (_istage == 2)
                    {
                        _coDetails.OPCount_Proposed = _count;
                    }
                    else if (_istage == 3)
                    {
                        _coDetails.OPCount_Negotiations = _count;
                    }
                    else if (_istage == 4)
                    {
                        _coDetails.OPCount_Hold = _count;
                    }
                    else if (_istage == 5 || _istage == 6)
                    {
                        _coDetails.OPCount_Closed = _coDetails.OPCount_Closed + _count;
                    }

                }
                _coDetails.OPCount_Total = _totalCount;
            }

            // Address Details
            //Linked CNs
            if (dt7 != null && dt7.Rows.Count > 0)
            {
                _coDetails.BillingAdd = new List<Address>();
                _coDetails.shippingAdd = new List<Address>();
                _coDetails.Billingaddcount = dt7.Rows.Count;
                int billaddcount = 0;
                int shippingaddcount = 0;
                foreach (DataRow _dr in dt7.Rows)
                {
                    Address _address = new Address();                  
                    _address.Type = Convert.ToString(_dr["MLS_ADDRESSTYPE"]);
                    if (_address.Type == "1")
                    {
                        _address.address = Convert.ToString(_dr["AddressLine"]);
                        _address.state = Convert.ToString(_dr["TXT_STATE"]);
                        _address.city = Convert.ToString(_dr["TXT_CITY"]);
                        _address.zip = Convert.ToString(_dr["TXT_ZIP"]);
                        _address.country = Convert.ToString(_dr["TXT_CountryName"]);
                        billaddcount = billaddcount + 1;
                        _coDetails.BillingAdd.Add(_address);
                    }
                    if (_address.Type == "2")
                    {
                        _address.address = Convert.ToString(_dr["AddressLine"]);
                        _address.state = Convert.ToString(_dr["TXT_STATE"]);
                        _address.city = Convert.ToString(_dr["TXT_CITY"]);
                        _address.zip = Convert.ToString(_dr["TXT_ZIP"]);
                        _address.country = Convert.ToString(_dr["TXT_CountryName"]);
                        shippingaddcount = shippingaddcount + 1;
                        _coDetails.shippingAdd.Add(_address);
                    }
                }
                _coDetails.Billingaddcount = billaddcount;
                _coDetails.shippingaddcount = shippingaddcount;
            }
            Util.SetSessionValue("CO_selectedprofilerecid", sCompanyId);
            return View(_coDetails);

        }

        public ActionResult ContactDetails(string sContactId)
        {
            //ViewBag.scomid = sCompanyId;

            Selltis.MVC.Models.ContactDetails _coDetails = new Models.ContactDetails();

            goData = (clData)Util.GetInstance("data");
            System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
            System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
            System.Data.SqlClient.SqlDataReader reader = null;
            cmd.CommandText = "GetContactDetailsByID";
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Connection = sqlConnection1;

            //parameter
            System.Data.SqlClient.SqlParameter coId = new System.Data.SqlClient.SqlParameter("@GidId", System.Data.SqlDbType.VarChar);
            coId.Value = sContactId;
            cmd.Parameters.Add(coId);

            reader = cmd.ExecuteReader();

            DataSet ds = new DataSet();

            DataTable dt1 = new DataTable();
            DataTable dt2 = new DataTable();
            DataTable dt3 = new DataTable();
            DataTable dt4 = new DataTable();
            DataTable dt5 = new DataTable();

            dt1.TableName = "dt1";
            dt2.TableName = "dt2";
            dt3.TableName = "dt3";
            dt4.TableName = "dt4";
            dt5.TableName = "dt5";

            ds.Tables.Add(dt1);
            ds.Tables.Add(dt2);
            ds.Tables.Add(dt3);
            ds.Tables.Add(dt4);
            ds.Tables.Add(dt5);

            ds.Load(reader, LoadOption.OverwriteChanges, dt1, dt2, dt3, dt4, dt5);

            //tiles
            if (dt1 != null && dt1.Rows.Count > 0)
            {
                _coDetails.Gid_id = Convert.ToString(dt1.Rows[0]["Gid_Id"]);
                _coDetails.CNName = Convert.ToString(dt1.Rows[0]["Name"]);
                _coDetails.Title = Convert.ToString(dt1.Rows[0]["TXT_TitleText"]);
                _coDetails.Email = Convert.ToString(dt1.Rows[0]["Eml_Email"]);
                _coDetails.Phone = Convert.ToString(dt1.Rows[0]["TEL_BusPhone"]);
                _coDetails.Mobile = Convert.ToString(dt1.Rows[0]["TEL_CellPhone"]);
                _coDetails.ContactofUser = Convert.ToString(dt1.Rows[0]["ContactofUser"]);
                _coDetails.AccountName = Convert.ToString(dt1.Rows[0]["AccountName"]);
                _coDetails.AddressMailing = Convert.ToString(dt1.Rows[0]["AddressMailing"]);
                _coDetails.CityMailing = Convert.ToString(dt1.Rows[0]["CityMailing"]);
                _coDetails.StateMailing = Convert.ToString(dt1.Rows[0]["StateMailing"]);
                _coDetails.ZipMailing = Convert.ToString(dt1.Rows[0]["ZipMailing"]);

                // _coDetails.COUNTRYMAILING = Convert.ToString(dt1.Rows[0]["TXT_COUNTRYMAILING"]);
                _coDetails.COUNTRYMAILING = Convert.ToString(dt1.Rows[0]["maillinCountry"]);
                _coDetails.NameFirst = Convert.ToString(dt1.Rows[0]["TXT_NameFirst"]);
                _coDetails.NameLast = Convert.ToString(dt1.Rows[0]["TXT_NAMELAST"]);
                _coDetails.Industry = Convert.ToString(dt1.Rows[0]["Industry"]);
                _coDetails.Source = Convert.ToString(dt1.Rows[0]["Source"]);
                _coDetails.ContactCode = Convert.ToString(dt1.Rows[0]["TXT_CONTACTCODE"]);
                _coDetails.Salutation = Convert.ToString(dt1.Rows[0]["MLS_SALUTATION"]);
                _coDetails.Level = Convert.ToString(dt1.Rows[0]["MLS_LEVEL"]);
                _coDetails.FunctionArea = Convert.ToString(dt1.Rows[0]["TXT_FUNCTIONALAREA"]);

                _coDetails.Chk_NoLongerG = Convert.ToString(dt1.Rows[0]["CHK_NOLONGERWITHCOMPANY"]);
                _coDetails.Chk_DoNotCall = Convert.ToString(dt1.Rows[0]["CHK_DONOTCALL"]);
                _coDetails.Chk_Email = Convert.ToString(dt1.Rows[0]["CHK_EMAILOPTOUT"]);

                _coDetails.OtherAdd = Convert.ToString(dt1.Rows[0]["TXT_ADDROTHER"]);
                _coDetails.OtherCity = Convert.ToString(dt1.Rows[0]["TXT_CITYOTHER"]);
                _coDetails.OtherState = Convert.ToString(dt1.Rows[0]["TXT_STATEOTHER"]);
                _coDetails.OtherZip = Convert.ToString(dt1.Rows[0]["TXT_ZIPOTHER"]);
                //_coDetails.OtherCountry = Convert.ToString(dt1.Rows[0]["TXT_COUNTRYOTHER"]);
                _coDetails.OtherCountry = Convert.ToString(dt1.Rows[0]["otherCounty"]);
                _coDetails.AccountId = Convert.ToString(dt1.Rows[0]["AccountId"]);
            }

            //Linked OPs
            if (dt2 != null && dt2.Rows.Count > 0)
            {
                _coDetails.LinkedOPs = new List<OPDetails>();
                _coDetails.OPCount = dt2.Rows.Count;
                foreach (DataRow _dr in dt2.Rows)
                {
                    OPDetails _newop = new OPDetails();
                    _newop.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _newop.OPName = Convert.ToString(_dr["TXT_OPPORTUNITYNAME"]);
                    _newop.LOB = Convert.ToString(_dr["LOB"]);
                    _newop.OPType = Convert.ToString(_dr["OPType"]);
                    _newop.OPOwner = Convert.ToString(_dr["OppOwner"]);
                    _newop.Stage = Convert.ToString(_dr["Stage"]);
                    _newop.Amount = Convert.ToDouble(_dr["CUR_AMOUNT"]);
                    _newop.ExpCloseDate = Convert.ToDateTime(_dr["DTT_ExpCloseDate"]);
                    _newop.TotalAmount = Convert.ToDouble(_dr["CUR_TOTALAMOUNT"]);
                    _newop.OppNumber = Convert.ToString(_dr["TXT_OPPNO"]);
                    _newop.Currency = Convert.ToString(_dr["Currency"]);
                    _coDetails.LinkedOPs.Add(_newop);
                }
            }

            //Ac details
            if (dt3 != null && dt3.Rows.Count > 0)
            {
                _coDetails.LinkedACs = new List<ACDetails>();
                _coDetails.ACCount = dt3.Rows.Count;

                clUtil goUt = new clUtil();

                foreach (DataRow _dr in dt3.Rows)
                {
                    ACDetails _acdet = new ACDetails();
                    _acdet.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _acdet.Date = Convert.ToDateTime(_dr["DTT_StartTime"]);
                    _acdet.Notes = Convert.ToString(_dr["Notes"]); //goUt.StripHTML(Convert.ToString(_dr["Notes"]));
                    _acdet.Type = Convert.ToString(_dr["ACType"]);

                    _coDetails.LinkedACs.Add(_acdet);
                }
            }


            //tiles
            if (dt4 != null && dt4.Rows.Count > 0)
            {
                _coDetails.ActiveOPCount = Convert.ToInt32(dt4.Rows[0]["ActiveOPCount"]);
                _coDetails.ActiveOPValue = Convert.ToDouble(dt4.Rows[0]["ActiveOPValue"]);
            }

            //OP count by status
            if (dt5 != null && dt5.Rows.Count > 0)
            {
                int _totalCount = 0;
                for (int i = 0; i < dt5.Rows.Count; i++)
                {
                    int _istage = Convert.ToInt32(dt5.Rows[i]["MLS_Stage"]);
                    int _count = Convert.ToInt32(dt5.Rows[i]["OPCount"]);
                    _totalCount = _totalCount + _count;
                    if (_istage == 1)
                    {
                        _coDetails.OPCount_Creating = _count;
                    }
                    else if (_istage == 2)
                    {
                        _coDetails.OPCount_Proposed = _count;
                    }
                    else if (_istage == 3)
                    {
                        _coDetails.OPCount_Negotiations = _count;
                    }
                    else if (_istage == 4)
                    {
                        _coDetails.OPCount_Hold = _count;
                    }
                    else if (_istage == 5 || _istage == 6)
                    {
                        _coDetails.OPCount_Closed = _coDetails.OPCount_Closed + _count;
                    }

                }
                _coDetails.OPCount_Total = _totalCount;
            }

            _coDetails.Key = Guid.NewGuid().ToString();
            Util.SetSessionValue("CN_selectedprofilerecid", sContactId);
            return View(_coDetails);

        }

        public ActionResult OPDetails(string sOPId)
        {
            //ViewBag.scomid = sCompanyId;

            Selltis.MVC.Models.OPDetails _coDetails = new Models.OPDetails();

            goData = (clData)Util.GetInstance("data");
            System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
            System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
            System.Data.SqlClient.SqlDataReader reader = null;
            cmd.CommandText = "GetOPDetailsByID";
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Connection = sqlConnection1;

            //parameter
            System.Data.SqlClient.SqlParameter coId = new System.Data.SqlClient.SqlParameter("@GidId", System.Data.SqlDbType.VarChar);
            coId.Value = sOPId;
            cmd.Parameters.Add(coId);

            reader = cmd.ExecuteReader();

            DataSet ds = new DataSet();

            DataTable dt1 = new DataTable();
            DataTable dt2 = new DataTable();
            DataTable dt3 = new DataTable();
            DataTable dt4 = new DataTable();

            DataTable dt5 = new DataTable();
            DataTable dt6 = new DataTable();
            DataTable dt7 = new DataTable();
            DataTable dt8 = new DataTable();

            dt1.TableName = "dt1";
            dt2.TableName = "dt2";
            dt3.TableName = "dt3";
            dt4.TableName = "dt4";
            dt5.TableName = "dt5";
            dt6.TableName = "dt6";
            dt7.TableName = "dt7";
            dt8.TableName = "dt8";

            ds.Tables.Add(dt1);
            ds.Tables.Add(dt2);
            ds.Tables.Add(dt3);
            ds.Tables.Add(dt4);
            ds.Tables.Add(dt5);
            ds.Tables.Add(dt6);
            ds.Tables.Add(dt7);
            ds.Tables.Add(dt8);

            ds.Load(reader, LoadOption.OverwriteChanges, dt1, dt2, dt3, dt4, dt5, dt6, dt7, dt8);

            //tiles
            if (dt1 != null && dt1.Rows.Count > 0)
            {
                _coDetails.Gid_id = Convert.ToString(dt1.Rows[0]["Gid_Id"]);
                _coDetails.OPName = Convert.ToString(dt1.Rows[0]["TXT_OPPORTUNITYNAME"]);
                _coDetails.LOB = Convert.ToString(dt1.Rows[0]["LOB"]);
                _coDetails.OPType = Convert.ToString(dt1.Rows[0]["OPType"]);
                _coDetails.OPOwner = Convert.ToString(dt1.Rows[0]["OppOwner"]);
                _coDetails.InvolvesUser = Convert.ToString(dt1.Rows[0]["InvolvesUser"]);
                _coDetails.Stage = Convert.ToString(dt1.Rows[0]["Stage"]);
                _coDetails.Amount = Convert.ToDouble(dt1.Rows[0]["CUR_AMOUNT"]);
                _coDetails.RealizedAmount = Convert.ToDouble(dt1.Rows[0]["CUR_REALIZEDAMOUNT"]);
                _coDetails.Contact = Convert.ToString(dt1.Rows[0]["Contact"]);
                _coDetails.Reason = Convert.ToString(dt1.Rows[0]["Reason"]);
                _coDetails.Region = Convert.ToString(dt1.Rows[0]["Region"]);
                _coDetails.Probability = Convert.ToDouble(dt1.Rows[0]["SI__Probability"]);
                _coDetails.UnitCondition = Convert.ToString(dt1.Rows[0]["UnitCondition"]);
                _coDetails.Project = Convert.ToString(dt1.Rows[0]["Project"]);
                _coDetails.AccountName = Convert.ToString(dt1.Rows[0]["AccountName"]);
                _coDetails.RiskLevel = Convert.ToString(dt1.Rows[0]["RiskLevel"]);
                _coDetails.TypeBid = Convert.ToString(dt1.Rows[0]["TypeBid"]);

                _coDetails.Currency = Convert.ToString(dt1.Rows[0]["Currency"]);
                _coDetails.Revision = Convert.ToString(dt1.Rows[0]["TXT_REVISION"]);
                _coDetails.Description = Convert.ToString(dt1.Rows[0]["TXT_Description"]);
                _coDetails.Company = Convert.ToString(dt1.Rows[0]["Company"]);
               
                if (DBNull.Value != dt1.Rows[0]["DTT_ExpCloseDate"] )
                    _coDetails.ExpCloseDate = Convert.ToDateTime(dt1.Rows[0]["DTT_ExpCloseDate"]);
                if (DBNull.Value != dt1.Rows[0]["DTT_NextActionDate"])
                    _coDetails.NextCloseDate = Convert.ToDateTime(dt1.Rows[0]["DTT_NextActionDate"]);

                _coDetails.RentalAmount = Convert.ToDouble(dt1.Rows[0]["CUR_RENTALAMOUNT"]);
                _coDetails.TotalAmount = Convert.ToDouble(dt1.Rows[0]["CUR_TOTALAMOUNT"]);
                _coDetails.NextAction = Convert.ToString(dt1.Rows[0]["NextAction"]);

                _coDetails.TypeOfBusiness = Convert.ToString(dt1.Rows[0]["TypeOfBusiness"]);
                _coDetails.AccountId = Convert.ToString(dt1.Rows[0]["AccountId"]);
                _coDetails.ContactId = Convert.ToString(dt1.Rows[0]["ContactId"]);

                _coDetails.Contact = Convert.ToString(dt1.Rows[0]["Contact"]);
                _coDetails.OppNumber = Convert.ToString(dt1.Rows[0]["OppNumber"]);
                _coDetails.ServiceLocation = Convert.ToString(dt1.Rows[0]["RequestedServiceLocation"]);
                _coDetails.Comments = Convert.ToString(dt1.Rows[0]["Questions_Comments"]);
                _coDetails.PrimaryQuote = Convert.ToString(dt1.Rows[0]["PrimaryQuote"]);
                _coDetails.QuotingForRental = Convert.ToString(dt1.Rows[0]["QuotingForRentalReturn"]);
                _coDetails.QuotePreparedForContact = Convert.ToString(dt1.Rows[0]["QuotePreparedForContact"]);
                _coDetails.BillingAdd = Convert.ToString(dt1.Rows[0]["BillingAddress"]);
                _coDetails.BillingComments = Convert.ToString(dt1.Rows[0]["BillingAdditionalComments"]);
                _coDetails.BillingPhone = Convert.ToString(dt1.Rows[0]["BillingPhone"]);
                _coDetails.BillingEmail = Convert.ToString(dt1.Rows[0]["BillingEmail"]);

                _coDetails.ShippingAdd = Convert.ToString(dt1.Rows[0]["ShippingAddress"]);
                _coDetails.ShippingPhone = Convert.ToString(dt1.Rows[0]["ShippingPhone"]);
                _coDetails.ShippingEmail = Convert.ToString(dt1.Rows[0]["ShippingEmail"]);
                _coDetails.ShippingComments = Convert.ToString(dt1.Rows[0]["ShippingAdditionalComments"]);
                _coDetails.ShippingSpecified = Convert.ToString(dt1.Rows[0]["ShippingSpecifiedCarrier"]);
                _coDetails.ShippingAgent = Convert.ToString(dt1.Rows[0]["ShippingAgent"]);
                _coDetails.ShipFromLocation = Convert.ToString(dt1.Rows[0]["ShipFromLocation"]);
                _coDetails.FinalDestination = Convert.ToString(dt1.Rows[0]["FinalDestination"]);

                _coDetails.PaymentTerms = Convert.ToString(dt1.Rows[0]["PaymentTerms"]);
                _coDetails.ShippingTerms = Convert.ToString(dt1.Rows[0]["ShippingTerms"]);
                _coDetails.PlaceOfDelivery = Convert.ToString(dt1.Rows[0]["PlaceOfDelivery"]);
                _coDetails.PriceValidity = Convert.ToString(dt1.Rows[0]["PriceVAlidaity"]);
                _coDetails.leadTime = Convert.ToString(dt1.Rows[0]["LeadTime"]);
                _coDetails.RentalRates = Convert.ToString(dt1.Rows[0]["RentalRates"]);
                _coDetails.PO = Convert.ToString(dt1.Rows[0]["PO"]);
                _coDetails.OrderNumber = Convert.ToString(dt1.Rows[0]["OrderNumber"]);
                _coDetails.ReqShipdate = Convert.ToString(dt1.Rows[0]["RequestedShipdate"]);

                _coDetails.Procurement = Convert.ToString(dt1.Rows[0]["ProcurementPreference"]);
                _coDetails.ScoprFit = Convert.ToString(dt1.Rows[0]["ScopeFitCapabilities"]);
                _coDetails.Urgency = Convert.ToString(dt1.Rows[0]["Urgency"]);
                _coDetails.TimeLine = Convert.ToString(dt1.Rows[0]["TimeLine"]);
                _coDetails.DecisionMaker = Convert.ToString(dt1.Rows[0]["DecisionMaker"]);
                _coDetails.CreatedDate = Convert.ToDateTime(dt1.Rows[0]["CreatedDate"]);

                //clUtil goUt = new clUtil();

                _coDetails.lead = Convert.ToString(dt1.Rows[0]["LeadName"]);
                _coDetails.PrimarySource = Convert.ToString(dt1.Rows[0]["SourceName"]);
                _coDetails.Notes = Convert.ToString(dt1.Rows[0]["Notes"]); // goUt.StripHTML(Convert.ToString(dt1.Rows[0]["Notes"]));
                _coDetails.Introduction = Convert.ToString(dt1.Rows[0]["Introduction"]);
                _coDetails.Journal = Convert.ToString(dt1.Rows[0]["Journal"]);// goUt.StripHTML(Convert.ToString(dt1.Rows[0]["Journal"]));

                _coDetails.MiniRentalDays = Convert.ToString(dt1.Rows[0]["MiniRentalDays"]);
                _coDetails.CapEx = Convert.ToString(dt1.Rows[0]["CapExReq"]);
                _coDetails.TypeOfBilling = Convert.ToString(dt1.Rows[0]["TypeOfBilling"]);
                _coDetails.UnitType = Convert.ToString(dt1.Rows[0]["UnitType"]);
                _coDetails.Ordered = Convert.ToString(dt1.Rows[0]["Ordered"]);
                _coDetails.Pricebook = Convert.ToString(dt1.Rows[0]["PriceBook"]);
                _coDetails.WaitingForAccountNAVID = Convert.ToString(dt1.Rows[0]["WaitingForAccountNAVID"]);
            }

            //Ac details
            if (dt2 != null && dt2.Rows.Count > 0)
            {
                _coDetails.LinkedACs = new List<ACDetails>();
                _coDetails.ACCount = dt2.Rows.Count;

                clUtil goUt = new clUtil();

                foreach (DataRow _dr in dt2.Rows)
                {
                    ACDetails _acdet = new ACDetails();
                    _acdet.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _acdet.Date = Convert.ToDateTime(_dr["DTT_StartTime"]);
                    _acdet.Notes = Convert.ToString(_dr["Notes"]); // goUt.StripHTML(Convert.ToString(_dr["Notes"]));
                    _acdet.Type = Convert.ToString(_dr["ACType"]);
                    _acdet.Contact = Convert.ToString(_dr["Contact"]);

                    _coDetails.LinkedACs.Add(_acdet);
                }
            }

            //Invloved CNs
            if (dt3 != null && dt3.Rows.Count > 0)
            {
                _coDetails.InvlovedCNs = new List<Contact>();
                _coDetails.InvlovedCNCount = dt3.Rows.Count;

                foreach (DataRow _dr in dt3.Rows)
                {
                    Contact _newcn = new Contact();

                    _newcn.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _newcn.CNName = Convert.ToString(_dr["Name"]);
                    _newcn.Title = Convert.ToString(_dr["TXT_TitleText"]);
                    _newcn.Email = Convert.ToString(_dr["Eml_Email"]);
                    _newcn.Phone = Convert.ToString(_dr["TEL_BusPhone"]);
                    _newcn.Mobile = Convert.ToString(_dr["TEL_CellPhone"]);
                    _newcn.ContactofUser = Convert.ToString(_dr["ContactofUser"]);
                    _newcn.AccountName = Convert.ToString(_dr["Account"]);

                    _coDetails.InvlovedCNs.Add(_newcn);
                }
            }

            //Linked Quotes
            if (dt4 != null && dt4.Rows.Count > 0)
            {
                _coDetails.LinkedQTs = new List<Quote>();
                _coDetails.LinkedQTsCount = dt4.Rows.Count;

                double dPrimaryQTTotalAmt = 0.0;

                foreach (DataRow _dr in dt4.Rows)
                {
                    Quote _qt = new Quote();

                    _qt.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _qt.QTNo = Convert.ToString(_dr["TXT_QuoteNo"]);
                    _qt.LinkedOppNo = Convert.ToString(_dr["OppNo"]);
                    _qt.LinkedOppType = Convert.ToString(_dr["OppType"]);
                    _qt.SubTotal = Convert.ToDouble(_dr["CUR_TOTALAMOUNT"]);
                    _qt.ContactName = Convert.ToString(_dr["Contact"]);
                    _qt.Email = Convert.ToString(_dr["Email"]);
                    _qt.Type = Convert.ToString(_dr["Type"]);
                    _qt.Currency = Convert.ToString(_dr["Currency"]);
                    _qt.PrimaryQuote = Convert.ToString(_dr["IsPrimary"]);
                    _qt.CreatedDate =Convert.ToDateTime(_dr["CreatedDate"]);
                    _qt.QuoteStatus = Convert.ToString(_dr["QuoteStatus"]);

                    if (_qt.PrimaryQuote == "1")
                    {
                        dPrimaryQTTotalAmt += _qt.SubTotal;
                    }

                    if (!string.IsNullOrEmpty(Convert.ToString(_dr["QTDoc"])))
                    {
                        _qt.QTDoc = "/CreateForm/DownloadAzureFile?sFileName=" + Convert.ToString(_dr["QTDoc"]) + "&sViewName=QT&sGidId=" + _qt.Gid_id + "&sFieldName=ADR_ATTACHMENTS";
                    }
                    else
                    {
                        _qt.QTDoc = "";
                    }

                    _coDetails.LinkedQTs.Add(_qt);
                }

                _coDetails.PrimaryQTTotalAmount = dPrimaryQTTotalAmt;
            }

            if (dt5 != null && dt5.Rows.Count > 0)
            {
                _coDetails.opCount = dt5.Rows.Count;
                foreach (DataRow _dr in dt5.Rows)
                {
                    _coDetails.No_of_Days_OP_Created = Convert.ToInt32(_dr["No_of_Days_OP_Created"]);
                    _coDetails.op_type = Convert.ToString((_dr["OppType"]));
                }

            }
            if (dt6 != null && dt6.Rows.Count > 0)
            {
                _coDetails.QTopCount = dt6.Rows.Count;
                foreach (DataRow _dr in dt6.Rows)
                {
                    _coDetails.No_of_Days_QT_Created = Convert.ToInt32(_dr["No_of_Days_QT_Created"]);
                    _coDetails.QTOP_TYPE = Convert.ToString((_dr["OppType"]));
                }

            }
            if (dt7 != null && dt7.Rows.Count > 0)
            {
                _coDetails.QT1REVCount = dt7.Rows.Count;
                foreach (DataRow _dr in dt7.Rows)
                {
                    _coDetails.No_of_Days_QT_1REV_Created = Convert.ToInt32(_dr["No_of_Days_QT_1REV_Created"]);
                    _coDetails.QT1REVOP_TYPE = Convert.ToString((_dr["OppType"]));
                }
            }
            if (dt8 != null && dt8.Rows.Count > 0)
            {
                _coDetails.QT2REVCount = dt8.Rows.Count;
                foreach (DataRow _dr in dt8.Rows)
                {
                    _coDetails.No_of_Days_QT_2REV_Created = Convert.ToInt32(_dr["No_of_Days_QT_2REV_Created"]);
                    _coDetails.QT2REVOP_TYPE = Convert.ToString((_dr["OppType"]));
                }

            }

            _coDetails.Key = Guid.NewGuid().ToString();

            return View(_coDetails);

        }

        public ActionResult PriorityDetails()
        {
            
            //ViewBag.scomid = sCompanyId;
            clProject goP = (clProject)Util.GetInstance("p");

            if (goP == null)
            {
               return RedirectToAction("Logout", "Login");
            }

            string userid = goP.GetMe("GID_ID");
            Selltis.MVC.Models.PriorityDetails _coDetails = new Models.PriorityDetails();

            goData = (clData)Util.GetInstance("data");
            System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
            System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
            System.Data.SqlClient.SqlDataReader reader = null;
            cmd.CommandText = "GetPriorityDetailsByUser";
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Connection = sqlConnection1;

            //parameter
            System.Data.SqlClient.SqlParameter coId = new System.Data.SqlClient.SqlParameter("@Userid", System.Data.SqlDbType.VarChar);
            coId.Value = userid;
            cmd.Parameters.Add(coId);

            reader = cmd.ExecuteReader();

            DataSet ds = new DataSet();

            DataTable dt1 = new DataTable();
            DataTable dt2 = new DataTable();
            DataTable dt3 = new DataTable();
            DataTable dt4 = new DataTable();
            DataTable dt5 = new DataTable();
            DataTable dt6 = new DataTable();
            DataTable dt7 = new DataTable();
            DataTable dt8 = new DataTable();
            DataTable dt9 = new DataTable();
            DataTable dt10 = new DataTable();

            dt1.TableName = "dt1";
            dt2.TableName = "dt2";
            dt3.TableName = "dt3";
            dt4.TableName = "dt4";
            dt5.TableName = "dt5";
            dt6.TableName = "dt6";
            dt7.TableName = "dt7";
            dt8.TableName = "dt8";
            dt9.TableName = "dt9";
            dt10.TableName = "dt10";


            ds.Tables.Add(dt1);
            ds.Tables.Add(dt2);
            ds.Tables.Add(dt3);
            ds.Tables.Add(dt4);
            ds.Tables.Add(dt5);
            ds.Tables.Add(dt6);
            ds.Tables.Add(dt7);
            ds.Tables.Add(dt8);
            ds.Tables.Add(dt9);
            ds.Tables.Add(dt10);

            ds.Load(reader, LoadOption.OverwriteChanges, dt1, dt2, dt3, dt4, dt5, dt6, dt7, dt8,dt9,dt10);

            //op details
            if (dt1 != null && dt1.Rows.Count > 0)
            {
                _coDetails.LinkedOPs = new List<OPDetails>();
                _coDetails.OPCount = dt1.Rows.Count;
                foreach (DataRow _dr in dt1.Rows)
                {
                    OPDetails _newop = new OPDetails();
                    _newop.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _newop.OPName = Convert.ToString(_dr["TXT_OPPORTUNITYNAME"]);
                    _newop.LOB = Convert.ToString(_dr["LOB"]);
                    _newop.OPType = Convert.ToString(_dr["OPType"]);
                    _newop.OPOwner = Convert.ToString(_dr["OppOwner"]);
                    _newop.Stage = Convert.ToString(_dr["Stage"]);
                    _newop.Amount = Convert.ToDouble(_dr["CUR_AMOUNT"]);
                    _newop.ExpCloseDate = Convert.ToDateTime(_dr["DTT_ExpCloseDate"]);
                    _newop.Contact = Convert.ToString(_dr["ContactName"]);
                    _newop.Company = Convert.ToString(_dr["AccountName"]);
                    _newop.Probability = Convert.ToDouble(_dr["SI__Probability"]);
                    _newop.RentalAmount = Convert.ToDouble(_dr["CUR_RENTALAMOUNT"]);
                    _newop.TotalAmount = Convert.ToDouble(_dr["CUR_TOTALAMOUNT"]);
                    _newop.TotalAmountUSD = Convert.ToDouble(_dr["CUR_TOTALAMOUNTUSD"]);
                    _newop.Currency = Convert.ToString(_dr["Currency"]);
                    _newop.OppNumber= Convert.ToString(_dr["OppNo"]);
                    _coDetails.LinkedOPs.Add(_newop);

                }
            }

            //Ac details
            if (dt2 != null && dt2.Rows.Count > 0)
            {
                _coDetails.LinkedACs = new List<ACDetails>();
                _coDetails.ACCount = dt2.Rows.Count;

                clUtil goUt = new clUtil();

                foreach (DataRow _dr in dt2.Rows)
                {
                    ACDetails _acdet = new ACDetails();
                    _acdet.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _acdet.Date = Convert.ToDateTime(_dr["DTT_StartTime"]);
                    _acdet.Notes = Convert.ToString(_dr["Notes"]); //goUt.StripHTML(Convert.ToString(_dr["Notes"]));
                    _acdet.Type = Convert.ToString(_dr["ACType"]);
                    _acdet.Contact = Convert.ToString(_dr["Contact"]);

                    _coDetails.LinkedACs.Add(_acdet);
                }
            }

            //Linked CNs
            if (dt3 != null && dt3.Rows.Count > 0)
            {
                _coDetails.LinkedCNs = new List<CNDetails>();
                _coDetails.CNCount = dt3.Rows.Count;
                foreach (DataRow _dr in dt3.Rows)
                {
                    CNDetails _newcn = new CNDetails();
                    _newcn.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _newcn.CNName = Convert.ToString(_dr["Name"]);
                    _newcn.Title = Convert.ToString(_dr["TXT_TitleText"]);
                    _newcn.Email = Convert.ToString(_dr["Eml_Email"]);
                    _newcn.Phone = Convert.ToString(_dr["TEL_BusPhone"]);
                    _newcn.Mobile = Convert.ToString(_dr["TEL_CellPhone"]);
                    _newcn.ContactofUser = Convert.ToString(_dr["ContactofUser"]);
                    _newcn.AccountName = Convert.ToString(_dr["Account"]);

                    _coDetails.LinkedCNs.Add(_newcn);
                }
            }

            //Tiles
            if (dt4 != null && dt4.Rows.Count > 0)
            {
                foreach (DataRow _dr in dt4.Rows)
                {

                    _coDetails.OPActiveCount = Convert.ToInt32(_dr["OPActiveCount"]);
                    _coDetails.QTTotalCount = Convert.ToInt32(_dr["QTTotalCount"]);
                    _coDetails.APTotalCount = Convert.ToInt32(_dr["APTotalCount"]);
                    _coDetails.CNTotalCount = Convert.ToInt32(_dr["CNTotalCount"]);
                    _coDetails.OPTotalAmount = Convert.ToDouble(_dr["OPTotalAmount"]);
                    _coDetails.OPFollowUpCount = Convert.ToInt32(_dr["OPFollowUpCount"]);
                }
            }

            _coDetails.Key = Guid.NewGuid().ToString();

            //OP count by status
            if (dt5 != null && dt5.Rows.Count > 0)
            {
                int _totalCount = 0;
                for (int i = 0; i < dt5.Rows.Count; i++)
                {
                    int _istage = Convert.ToInt32(dt5.Rows[i]["MLS_Stage"]);
                    int _count = Convert.ToInt32(dt5.Rows[i]["OPCount"]);

                    //if (_istage == 1)
                    //{
                    //    _coDetails.OPCount_Creating = _count;
                    //}
                    //else if (_istage == 2)
                    //{
                    //    _coDetails.OPCount_Proposed = _count;
                    //}
                    //else if (_istage == 3)
                    //{
                    //    _coDetails.OPCount_Negotiations = _count;
                    //}
                    //else if (_istage == 4)
                    //{
                    //    _coDetails.OPCount_Hold = _count;
                    //}
                    //else if (_istage == 5 || _istage == 6)
                    //{
                    //    _coDetails.OPCount_Closed = _coDetails.OPCount_Closed + _count;
                    //}
                    if (_istage == 3) //Won (Support)
                    {
                        _coDetails.OPCount_Creating = _count;
                        _totalCount += _count;
                    }
                    else if (_istage == 7) //Hot Prospect (Advocating)
                    {
                        _coDetails.OPCount_Hold = _count;
                        _totalCount += _count;
                    }
                    else if (_istage == 6) //Interested Prospect (Discovery)
                    {
                        _coDetails.OPCount_Won = _count;
                        _totalCount += _count;
                    }
                    else if (_istage == 5 ) //Qualified Prospect (Relating/Discovery)
                    {
                        _coDetails.OPCount_QualifiedProspect = _count;
                        _totalCount += _count;
                    }
                    else if (_istage == 1) //Potential Prospect (Relating)
                    {
                        _coDetails.OPCount_PotentialProspect = _count;
                        _totalCount += _count;
                    }
                    else if (_istage == 4) //Lost
                    {
                        _coDetails.OPCount_Lost = _count;
                        _totalCount += _count;
                    }

                }
                _coDetails.OPCount_Total = _totalCount;
            }

            //Linked COs
            if (dt6 != null && dt6.Rows.Count > 0)
            {
                _coDetails.LinkedCOs = new List<Company>();
                _coDetails.COCount = dt6.Rows.Count;
                foreach (DataRow _dr in dt6.Rows)
                {
                    Company _codet = new Company();
                    _codet.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _codet.CompanyName = Convert.ToString(_dr["TXT_CompanyName"]);
                    _codet.NAVID = Convert.ToString(_dr["TXT_NAVID"]);
                    _codet.PhoneNo = Convert.ToString(_dr["TEL_PhoneNo"]);
                    _codet.Address = Convert.ToString(_dr["TXT_AddrMailing"]);
                    _codet.City = Convert.ToString(_dr["TXT_CityMailing"]);
                    _codet.State = Convert.ToString(_dr["TXT_StateMailing"]);
                    _codet.Zip = Convert.ToString(_dr["TXT_ZipMailing"]);
                    _codet.Country = Convert.ToString(_dr["TXT_CountryMailing"]);                  
                    _codet.AccountType = Convert.ToString(_dr["AccountType"]);
                    _codet.Currency = Convert.ToString(_dr["Currency"]);
                    _codet.Code = Convert.ToString(_dr["TXT_Code"]);
                    _codet.Industry = Convert.ToString(_dr["Industry"]);

                    _coDetails.LinkedCOs.Add(_codet);
                }
            }

            //Linked WOs
            if (dt7 != null && dt7.Rows.Count > 0)
            {
                _coDetails.LinkedWLs = new List<WebLeadDetails>();
                _coDetails.WLCount = dt7.Rows.Count;

                foreach (DataRow _dr in dt7.Rows)
                {
                    WebLeadDetails _wlDetails = new WebLeadDetails();
                    _wlDetails.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _wlDetails.Name = Convert.ToString(_dr["Name"]);
                    _wlDetails.Stage = Convert.ToString(_dr["STAGE"]);
                    _wlDetails.ProductType = Convert.ToString(_dr["TYPEOFREQUEST"]);
                    _wlDetails.TypeOfReq = Convert.ToString(_dr["PRODUCTTYPE"]);
                    _wlDetails.Email = Convert.ToString(_dr["EML_EMAIL"]);
                    _wlDetails.CompanyName = Convert.ToString(_dr["TXT_COMPANYNAME"]);
                    _wlDetails.City = Convert.ToString(_dr["TXT_CITY"]);
                     _wlDetails.County = Convert.ToString(_dr["TXT_COUNTRY"]);
                  
                    _wlDetails.PhoneNo = Convert.ToString(_dr["TEL_PHONENUMBER"]);

                    _wlDetails.WLNO = Convert.ToString(_dr["TXT_WLNO"]);
                    _wlDetails.IndustryName = Convert.ToString(_dr["Txt_Industry"]);
                    _wlDetails.RelatedContact = Convert.ToString(_dr["Related_Contact"]);
                    _wlDetails.RelatedComapny = Convert.ToString(_dr["Related_Company"]);

                    _coDetails.LinkedWLs.Add(_wlDetails);
                }
            }

            //WLs count by Stage           
            if (dt8 != null && dt8.Rows.Count > 0)
            {
                int _totalCount = 0;
                for (int i = 0; i < dt8.Rows.Count; i++)
                {
                    int _istage = Convert.ToInt32(dt8.Rows[i]["MLS_Stage"]);
                    int _count = Convert.ToInt32(dt8.Rows[i]["WLCount"]);
                    _totalCount += _count;
                    if (_istage == 2)
                    {
                        _coDetails.WLCount_Assigned = _count;
                    }
                    else if (_istage == 3)
                    {
                        _coDetails.WLCount_Qualified = _count;
                    }
                    else if (_istage == 7)
                    {
                        _coDetails.WLCount_CallPlacedToCustomer = _count;
                    }
                    else if (_istage == 6)
                    {
                        _coDetails.WLCount_EmailSentToCustomer = _count;
                    }
                }
                _coDetails.WLCount_Total = _totalCount;
            }

            //AP Top 5 Details
            if (dt9 != null && dt9.Rows.Count > 0)
            {
                _coDetails.LinkedAPs = new List<APDetails>();
                _coDetails.APCount = dt9.Rows.Count;

                clUtil goUt = new clUtil();

                foreach (DataRow _dr in dt9.Rows)
                {
                    APDetails _apdet = new APDetails();
                    _apdet.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                   _apdet.Description= Convert.ToString(_dr["TXT_Description"]);
                    _apdet.Type = Convert.ToString(_dr["APType"]);
                    _apdet.StartDate = Convert.ToString(_dr["DTT_StartTime"]);
                    _apdet.EndDate = Convert.ToString(_dr["DTT_EndTime"]);

                    _coDetails.LinkedAPs.Add(_apdet);
                }
            }
            //CIR top 5 details
            if (dt10 != null && dt10.Rows.Count > 0)
            {
                _coDetails.LinkedCIRs = new List<CIRDetails>();
                _coDetails.CIRCount = dt10.Rows.Count;

                clUtil goUt = new clUtil();

                foreach (DataRow _dr in dt10.Rows)
                {
                    CIRDetails _cirdet = new CIRDetails();
                    _cirdet.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _cirdet.CIR_No = Convert.ToString(_dr["CIR_No"]);
                    _cirdet.CustomerReportIncidentContact = Convert.ToString(_dr["CustomerReportIncidentContact"]);
                    _cirdet.Account = Convert.ToString(_dr["Account"]);
                    _cirdet.HooverCSOrederNo = Convert.ToString(_dr["HooverCSOrederNo"]);
                    _cirdet.DateReported = Convert.ToString(_dr["DateReported"]);
                    _cirdet.DateOfIncident = Convert.ToString(_dr["DateOfIncident"]);
                    _cirdet.AssignedSalesPerson = Convert.ToString(_dr["AssignedSalesPerson"]);
                    _cirdet.Urgency = Convert.ToString(_dr["Urgency"]);
                    _cirdet.Status = Convert.ToString(_dr["Status"]);
                    _cirdet.CIRCategory = Convert.ToString(_dr["CIRCategory"]);

                    _coDetails.LinkedCIRs.Add(_cirdet);
                }
            }

            return View(_coDetails);

        }

        public ActionResult QTDetails(string sQTId)
        {
            Selltis.MVC.Models.QTDetails _qtDetails = new Models.QTDetails();

            goData = (clData)Util.GetInstance("data");
            System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
            System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
            System.Data.SqlClient.SqlDataReader reader = null;
            cmd.CommandText = "GetQTDetailsByID";
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Connection = sqlConnection1;

            System.Data.SqlClient.SqlParameter coId = new System.Data.SqlClient.SqlParameter("@GidId", System.Data.SqlDbType.VarChar);
            coId.Value = sQTId;

            cmd.Parameters.Add(coId);

            reader = cmd.ExecuteReader();

            DataSet ds = new DataSet();

            DataTable dt1 = new DataTable();
            DataTable dt2 = new DataTable();
            DataTable dt3 = new DataTable();
            DataTable dt4 = new DataTable();

            dt1.TableName = "dt1";
            dt2.TableName = "dt2";
            dt3.TableName = "dt3";
            dt4.TableName = "dt4";

            ds.Tables.Add(dt1);
            ds.Tables.Add(dt2);
            ds.Tables.Add(dt3);
            ds.Tables.Add(dt4);

            ds.Load(reader, LoadOption.OverwriteChanges, dt1, dt2, dt3, dt4);

            if (dt1 != null && dt1.Rows.Count > 0)
            {
                _qtDetails.Gid_id = Convert.ToString(dt1.Rows[0]["Gid_Id"]);
                _qtDetails.QTOwner = Convert.ToString(dt1.Rows[0]["Owner"]);
                _qtDetails.OPType = Convert.ToString(dt1.Rows[0]["MLS_OPPORTUNITYTYPE"]);
                _qtDetails.QTNumber = Convert.ToString(dt1.Rows[0]["TXT_QuoteNo"]);
                _qtDetails.OPName = Convert.ToString(dt1.Rows[0]["TXT_OPPORTUNITYNAME"]);
                _qtDetails.NetAmount = Convert.ToString(dt1.Rows[0]["CUR_TOTALAMOUNT"]);
                _qtDetails.PrimaryQuote = Convert.ToString(dt1.Rows[0]["CHK_PRIMARYQUOTE"]);
                _qtDetails.StartDate = Convert.ToString(dt1.Rows[0]["DTT_CreationTime"]);
                _qtDetails.ExpiresOn = Convert.ToString(dt1.Rows[0]["DTT_DateClosed"]);
                _qtDetails.PrimaryContact = Convert.ToString(dt1.Rows[0]["ContactName"]);
                _qtDetails.Type = Convert.ToString(dt1.Rows[0]["MLS_TYPE"]);
                _qtDetails.PO = Convert.ToString(dt1.Rows[0]["TXT_PONO"]);
                _qtDetails.SalesRep = Convert.ToString(dt1.Rows[0]["SalesRep"]);
                _qtDetails.PaymentTerms = Convert.ToString(dt1.Rows[0]["MLS_PAYMENTTERMS"]);
                _qtDetails.RelatedOpportunity = Convert.ToString(dt1.Rows[0]["LNK_RELATED_OP"]);
                _qtDetails.UnitType = Convert.ToString(dt1.Rows[0]["MLS_UNITTYPE"]);
                _qtDetails.CapExRequired = Convert.ToString(dt1.Rows[0]["CHK_CAPEXREQUIRED"]);
                _qtDetails.Account = Convert.ToString(dt1.Rows[0]["txt_CompanyName"]);

                _qtDetails.QuotePreparedForContact = Convert.ToString(dt1.Rows[0]["ContactName"]);
                _qtDetails.PrimaryContactEmail = Convert.ToString(dt1.Rows[0]["EML_Email"]);

                _qtDetails.BillingAdd = Convert.ToString(dt1.Rows[0]["TXT_BILLINGADDRESS"]);
                _qtDetails.billingContactName = Convert.ToString(dt1.Rows[0]["TXT_BILLINGCONTACTNAME"]);
                _qtDetails.BillingEmail = Convert.ToString(dt1.Rows[0]["TXT_BILLINGPHONE"]);
                _qtDetails.BillingPhone = Convert.ToString(dt1.Rows[0]["EML_BILLINGEMAIL"]);
                _qtDetails.BillingAdditionalComments = Convert.ToString(dt1.Rows[0]["TXT_BILLINGADDITIONALCOMMENTS"]);

                _qtDetails.ShippingAgent = Convert.ToString(dt1.Rows[0]["MLS_SHIPPINGAGENT"]);
                _qtDetails.ShipFromLocation = Convert.ToString(dt1.Rows[0]["MLS_SHIPFROMLOCATION"]);
                _qtDetails.ShippingAdds = Convert.ToString(dt1.Rows[0]["TXT_SHIPPINGADDRESS"]);
                _qtDetails.FinalDestination = Convert.ToString(dt1.Rows[0]["MLS_FINALDESTINATION"]);
                _qtDetails.ShippingCarrier = Convert.ToString(dt1.Rows[0]["TXT_SHIPPINGSPECIFIEDCARRIER"]);
                _qtDetails.ShippingContactName = Convert.ToString(dt1.Rows[0]["TXT_SHIPPINGCONTACTNAME"]);
                _qtDetails.ShippingEmail = Convert.ToString(dt1.Rows[0]["EML_SHIPPINGEMAIL"]);
                _qtDetails.ShippingPhone = Convert.ToString(dt1.Rows[0]["TEL_SHIPPINGPHONE"]);
                _qtDetails.ShippingAdditionalComments = Convert.ToString(dt1.Rows[0]["TXT_SHIPPINGADDITIONALCOMMENTS"]);


                _qtDetails.LeadTime = Convert.ToString(dt1.Rows[0]["MMO_LEADTIME"]);
                _qtDetails.RentalRates = Convert.ToString(dt1.Rows[0]["MMO_RentalRates"]);
                _qtDetails.ReqShipDate = Convert.ToString(dt1.Rows[0]["DTT_REQUESTEDSHIPDATE"]);
                _qtDetails.PlaceOfDelivery = Convert.ToString(dt1.Rows[0]["MLS_PLACEOFDELIVERY"]);
                _qtDetails.PriceValidity = Convert.ToString(dt1.Rows[0]["DTT_PRICEVALIDITY"]);


                _qtDetails.Introduction = Convert.ToString(dt1.Rows[0]["MMO_Introduction"]);
                _qtDetails.Notes = Convert.ToString(dt1.Rows[0]["MMO_Notes"]);

            }
            _qtDetails.Key = Guid.NewGuid().ToString();

            return View(_qtDetails);
        }

        public ActionResult WLDetails(string sWLId)
        {

            Selltis.MVC.Models.WebLeadDetails _wlDetails = new Models.WebLeadDetails();
            clProject goP = (clProject)Util.GetInstance("p");
            // string userid = goP.GetMe("GID_ID");            

            goData = (clData)Util.GetInstance("data");
            System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
            System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
            System.Data.SqlClient.SqlDataReader reader = null;
            cmd.CommandText = "GetWLDetailsById";
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Connection = sqlConnection1;

            //parameter
            System.Data.SqlClient.SqlParameter coId = new System.Data.SqlClient.SqlParameter("@WLId", System.Data.SqlDbType.VarChar);
            coId.Value = sWLId;
            cmd.Parameters.Add(coId);

            reader = cmd.ExecuteReader();

            DataSet ds = new DataSet();

            DataTable dt1 = new DataTable();
            DataTable dt2 = new DataTable();
            DataTable dt3 = new DataTable();
            //DataTable dt4 = new DataTable();


            dt1.TableName = "dt1";
            dt2.TableName = "dt2";
            dt3.TableName = "dt3";
            // dt4.TableName = "dt4";

            ds.Tables.Add(dt1);
            ds.Tables.Add(dt2);
            ds.Tables.Add(dt3);
            // ds.Tables.Add(dt4);

            ds.Load(reader, LoadOption.OverwriteChanges, dt1, dt2, dt3);

            if (dt1 != null && dt1.Rows.Count > 0)
            {
                _wlDetails.Name = Convert.ToString(dt1.Rows[0]["Name"]);
                _wlDetails.FName = Convert.ToString(dt1.Rows[0]["TXT_FIRSTNAME"]);
                _wlDetails.LName = Convert.ToString(dt1.Rows[0]["TXT_LASTNAME"]);
                _wlDetails.Stage = Convert.ToString(dt1.Rows[0]["STAGE"]);
                _wlDetails.ProductType = Convert.ToString(dt1.Rows[0]["TYPEOFREQUEST"]);
                _wlDetails.CompanyName = Convert.ToString(dt1.Rows[0]["TXT_COMPANYNAME"]);
                _wlDetails.TypeOfReq = Convert.ToString(dt1.Rows[0]["PRODUCTTYPE"]);
                _wlDetails.IndustryName = Convert.ToString(dt1.Rows[0]["TXT_IndustryName"]);
                _wlDetails.Email = Convert.ToString(dt1.Rows[0]["EML_EMAIL"]);
                _wlDetails.PhoneNo = Convert.ToString(dt1.Rows[0]["TEL_PHONENUMBER"]);
                _wlDetails.City = Convert.ToString(dt1.Rows[0]["TXT_CITY"]);
                //_wlDetails.County = Convert.ToString(dt1.Rows[0]["TXT_COUNTRY"]);
                _wlDetails.County = Convert.ToString(dt1.Rows[0]["County"]);
                _wlDetails.COMMENTS = Convert.ToString(dt1.Rows[0]["MMO_COMMENTS"]);
                _wlDetails.CreationDateTimeInHubSpot = Convert.ToString(dt1.Rows[0]["DTT_CreationDateTimeInHubSpot"]);
                _wlDetails.AssignedUser = Convert.ToString(dt1.Rows[0]["AssignedUser"]);
                _wlDetails.RelatedContact = Convert.ToString(dt1.Rows[0]["RelatedContact"]);
                _wlDetails.RelatedComapny = Convert.ToString(dt1.Rows[0]["RelatedComapny"]);
                _wlDetails.WLNO = Convert.ToString(dt1.Rows[0]["TXT_WLNO"]);
                _wlDetails.ShowQualifyLeadLink = 0;
                _wlDetails.Gid_id = Convert.ToString(dt1.Rows[0]["Gid_id"]);

                if ((_wlDetails.Stage == "New" || _wlDetails.Stage == "Assigned") && !string.IsNullOrEmpty(_wlDetails.RelatedContact))
                {
                    _wlDetails.ShowQualifyLeadLink = 1;
                }

                _wlDetails.Key = Guid.NewGuid().ToString();
            }

            //Linked OPPs
            if (dt2 != null && dt2.Rows.Count > 0)
            {
                _wlDetails.LinkedOPs = new List<OPDetails>();
                _wlDetails.OPCount = dt2.Rows.Count;

                foreach (DataRow _dr in dt2.Rows)
                {
                    OPDetails _newop = new OPDetails();
                    _newop.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _newop.OPName = Convert.ToString(_dr["TXT_OPPORTUNITYNAME"]);
                    _newop.LOB = Convert.ToString(_dr["LOB"]);
                    _newop.OPType = Convert.ToString(_dr["OPType"]);
                    _newop.OPOwner = Convert.ToString(_dr["OppOwner"]);
                    _newop.Stage = Convert.ToString(_dr["Stage"]);
                    _newop.Amount = Convert.ToDouble(_dr["CUR_AMOUNT"]);
                    _newop.ExpCloseDate = Convert.ToDateTime(_dr["DTT_ExpCloseDate"]);
                    _newop.Contact = Convert.ToString(_dr["ContactName"]);
                    _newop.Company = Convert.ToString(_dr["AccountName"]);
                    _newop.Probability = Convert.ToDouble(_dr["SI__Probability"]);
                    _newop.RentalAmount = Convert.ToDouble(_dr["CUR_RENTALAMOUNT"]);
                    _newop.TotalAmount = Convert.ToDouble(_dr["CUR_TOTALAMOUNT"]);
                    _newop.TotalAmountUSD = Convert.ToDouble(_dr["CUR_TOTALAMOUNTUSD"]);
                    _newop.Currency = Convert.ToString(_dr["Currency"]);
                    _wlDetails.LinkedOPs.Add(_newop);

                }
            }

            //Ac details
            if (dt3 != null && dt3.Rows.Count > 0)
            {
                _wlDetails.LinkedACs = new List<ACDetails>();
                _wlDetails.ACCount = dt3.Rows.Count;

                clUtil goUt = new clUtil();

                foreach (DataRow _dr in dt3.Rows)
                {
                    ACDetails _acdet = new ACDetails();
                    _acdet.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _acdet.Date = Convert.ToDateTime(_dr["DTT_StartTime"]);
                    _acdet.Notes = Convert.ToString(_dr["Notes"]); //goUt.StripHTML(Convert.ToString(_dr["Notes"]));
                    _acdet.Type = Convert.ToString(_dr["ACType"]);
                    _acdet.Contact = Convert.ToString(_dr["Contact"]);

                    _wlDetails.LinkedACs.Add(_acdet);
                }
            }

            return View(_wlDetails);

        }

        public ActionResult PriorityDetails_Readonly()
        {

            //ViewBag.scomid = sCompanyId;
            clProject goP = (clProject)Util.GetInstance("p");

            if (goP == null)
            {
                return RedirectToAction("Logout", "Login");
            }

            string userid = goP.GetMe("GID_ID");
            Selltis.MVC.Models.PriorityDetails _coDetails = new Models.PriorityDetails();

            goData = (clData)Util.GetInstance("data");
            System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
            System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
            System.Data.SqlClient.SqlDataReader reader = null;
            cmd.CommandText = "GetPriorityDetailsByUser_ReadOnly";
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Connection = sqlConnection1;

            //parameter
            //System.Data.SqlClient.SqlParameter coId = new System.Data.SqlClient.SqlParameter("@Userid", System.Data.SqlDbType.VarChar);
            //coId.Value = userid;
            //cmd.Parameters.Add(coId);

            reader = cmd.ExecuteReader();

            DataSet ds = new DataSet();

            //DataTable dt1 = new DataTable();
            //DataTable dt2 = new DataTable();
            //DataTable dt3 = new DataTable();
            DataTable dt4 = new DataTable();
            DataTable dt5 = new DataTable();
            //DataTable dt6 = new DataTable();
            //DataTable dt7 = new DataTable();
            DataTable dt8 = new DataTable();
            //DataTable dt9 = new DataTable();

            //dt1.TableName = "dt1";
            //dt2.TableName = "dt2";
            //dt3.TableName = "dt3";
            dt4.TableName = "dt4";
            dt5.TableName = "dt5";
            //dt6.TableName = "dt6";
            //dt7.TableName = "dt7";
            dt8.TableName = "dt8";
            //dt9.TableName = "dt9";


            //ds.Tables.Add(dt1);
            //ds.Tables.Add(dt2);
            //ds.Tables.Add(dt3);
            ds.Tables.Add(dt4);
            ds.Tables.Add(dt5);
            //ds.Tables.Add(dt6);
            //ds.Tables.Add(dt7);
            ds.Tables.Add(dt8);
            //ds.Tables.Add(dt9);

            ds.Load(reader, LoadOption.OverwriteChanges, dt4, dt5, dt8);

            //op details
            //if (dt1 != null && dt1.Rows.Count > 0)
            //{
            //    _coDetails.LinkedOPs = new List<OPDetails>();
            //    _coDetails.OPCount = dt1.Rows.Count;
            //    foreach (DataRow _dr in dt1.Rows)
            //    {
            //        OPDetails _newop = new OPDetails();
            //        _newop.Gid_id = Convert.ToString(_dr["Gid_Id"]);
            //        _newop.OPName = Convert.ToString(_dr["TXT_OPPORTUNITYNAME"]);
            //        _newop.LOB = Convert.ToString(_dr["LOB"]);
            //        _newop.OPType = Convert.ToString(_dr["OPType"]);
            //        _newop.OPOwner = Convert.ToString(_dr["OppOwner"]);
            //        _newop.Stage = Convert.ToString(_dr["Stage"]);
            //        _newop.Amount = Convert.ToDouble(_dr["CUR_AMOUNT"]);
            //        _newop.ExpCloseDate = Convert.ToDateTime(_dr["DTT_ExpCloseDate"]);
            //        _newop.Contact = Convert.ToString(_dr["ContactName"]);
            //        _newop.Company = Convert.ToString(_dr["AccountName"]);
            //        _newop.Probability = Convert.ToDouble(_dr["SI__Probability"]);
            //        _newop.RentalAmount = Convert.ToDouble(_dr["CUR_RENTALAMOUNT"]);
            //        _newop.TotalAmount = Convert.ToDouble(_dr["CUR_TOTALAMOUNT"]);
            //        _newop.TotalAmountUSD = Convert.ToDouble(_dr["CUR_TOTALAMOUNTUSD"]);
            //        _newop.Currency = Convert.ToString(_dr["Currency"]);
            //        _newop.OppNumber = Convert.ToString(_dr["OppNo"]);
            //        _coDetails.LinkedOPs.Add(_newop);

            //    }
            //}

            ////Ac details
            //if (dt2 != null && dt2.Rows.Count > 0)
            //{
            //    _coDetails.LinkedACs = new List<ACDetails>();
            //    _coDetails.ACCount = dt2.Rows.Count;

            //    clUtil goUt = new clUtil();

            //    foreach (DataRow _dr in dt2.Rows)
            //    {
            //        ACDetails _acdet = new ACDetails();
            //        _acdet.Gid_id = Convert.ToString(_dr["Gid_Id"]);
            //        _acdet.Date = Convert.ToDateTime(_dr["DTT_StartTime"]);
            //        _acdet.Notes = Convert.ToString(_dr["Notes"]); //goUt.StripHTML(Convert.ToString(_dr["Notes"]));
            //        _acdet.Type = Convert.ToString(_dr["ACType"]);
            //        _acdet.Contact = Convert.ToString(_dr["Contact"]);

            //        _coDetails.LinkedACs.Add(_acdet);
            //    }
            //}

            ////Linked CNs
            //if (dt3 != null && dt3.Rows.Count > 0)
            //{
            //    _coDetails.LinkedCNs = new List<CNDetails>();
            //    _coDetails.CNCount = dt3.Rows.Count;
            //    foreach (DataRow _dr in dt3.Rows)
            //    {
            //        CNDetails _newcn = new CNDetails();
            //        _newcn.Gid_id = Convert.ToString(_dr["Gid_Id"]);
            //        _newcn.CNName = Convert.ToString(_dr["Name"]);
            //        _newcn.Title = Convert.ToString(_dr["TXT_TitleText"]);
            //        _newcn.Email = Convert.ToString(_dr["Eml_Email"]);
            //        _newcn.Phone = Convert.ToString(_dr["TEL_BusPhone"]);
            //        _newcn.Mobile = Convert.ToString(_dr["TEL_CellPhone"]);
            //        _newcn.ContactofUser = Convert.ToString(_dr["ContactofUser"]);
            //        _newcn.AccountName = Convert.ToString(_dr["Account"]);

            //        _coDetails.LinkedCNs.Add(_newcn);
            //    }
            //}

            //Tiles
            if (dt4 != null && dt4.Rows.Count > 0)
            {
                foreach (DataRow _dr in dt4.Rows)
                {

                    _coDetails.OPActiveCount = Convert.ToInt32(_dr["OPActiveCount"]);
                    _coDetails.QTTotalCount = Convert.ToInt32(_dr["QTTotalCount"]);
                    _coDetails.APTotalCount = Convert.ToInt32(_dr["APTotalCount"]);
                    _coDetails.CNTotalCount = Convert.ToInt32(_dr["CNTotalCount"]);
                    _coDetails.OPTotalAmount = Convert.ToDouble(_dr["OPTotalAmount"]);
                    _coDetails.OPFollowUpCount = Convert.ToInt32(_dr["OPFollowUpCount"]);
                }
            }

            _coDetails.Key = Guid.NewGuid().ToString();

            //OP count by status
            if (dt5 != null && dt5.Rows.Count > 0)
            {
                int _totalCount = 0;
                for (int i = 0; i < dt5.Rows.Count; i++)
                {
                    int _istage = Convert.ToInt32(dt5.Rows[i]["MLS_Stage"]);
                    int _count = Convert.ToInt32(dt5.Rows[i]["OPCount"]);

                    //if (_istage == 1)
                    //{
                    //    _coDetails.OPCount_Creating = _count;
                    //}
                    //else if (_istage == 2)
                    //{
                    //    _coDetails.OPCount_Proposed = _count;
                    //}
                    //else if (_istage == 3)
                    //{
                    //    _coDetails.OPCount_Negotiations = _count;
                    //}
                    //else if (_istage == 4)
                    //{
                    //    _coDetails.OPCount_Hold = _count;
                    //}
                    //else if (_istage == 5 || _istage == 6)
                    //{
                    //    _coDetails.OPCount_Closed = _coDetails.OPCount_Closed + _count;
                    //}

                    if (_istage == 1) //open
                    {
                        _coDetails.OPCount_Creating = _count;
                        _totalCount += _count;
                    }
                    else if (_istage == 2) //onhold
                    {
                        _coDetails.OPCount_Hold = _count;
                        _totalCount += _count;
                    }
                    else if (_istage == 3) //won
                    {
                        _coDetails.OPCount_Won = _count;
                        _totalCount += _count;
                    }
                    else if (_istage == 4) //Lost
                    {
                        _coDetails.OPCount_Lost = _count;
                        _totalCount += _count;
                    }

                }
                _coDetails.OPCount_Total = _totalCount;
            }

            //Linked COs
            //if (dt6 != null && dt6.Rows.Count > 0)
            //{
            //    _coDetails.LinkedCOs = new List<Company>();
            //    _coDetails.COCount = dt6.Rows.Count;
            //    foreach (DataRow _dr in dt6.Rows)
            //    {
            //        Company _codet = new Company();
            //        _codet.Gid_id = Convert.ToString(_dr["Gid_Id"]);
            //        _codet.CompanyName = Convert.ToString(_dr["TXT_CompanyName"]);
            //        _codet.NAVID = Convert.ToString(_dr["TXT_NAVID"]);
            //        _codet.PhoneNo = Convert.ToString(_dr["TEL_PhoneNo"]);
            //        _codet.Address = Convert.ToString(_dr["TXT_AddrMailing"]);
            //        _codet.City = Convert.ToString(_dr["TXT_CityMailing"]);
            //        _codet.State = Convert.ToString(_dr["TXT_StateMailing"]);
            //        _codet.Zip = Convert.ToString(_dr["TXT_ZipMailing"]);
            //        _codet.Country = Convert.ToString(_dr["TXT_CountryMailing"]);
            //        _codet.AccountType = Convert.ToString(_dr["AccountType"]);
            //        _codet.Currency = Convert.ToString(_dr["Currency"]);
            //        _codet.Code = Convert.ToString(_dr["TXT_Code"]);
            //        _codet.Industry = Convert.ToString(_dr["Industry"]);

            //        _coDetails.LinkedCOs.Add(_codet);
            //    }
            //}

            //Linked WOs
            //if (dt7 != null && dt7.Rows.Count > 0)
            //{
            //    _coDetails.LinkedWLs = new List<WebLeadDetails>();
            //    _coDetails.WLCount = dt7.Rows.Count;

            //    foreach (DataRow _dr in dt7.Rows)
            //    {
            //        WebLeadDetails _wlDetails = new WebLeadDetails();
            //        _wlDetails.Gid_id = Convert.ToString(_dr["Gid_Id"]);
            //        _wlDetails.Name = Convert.ToString(_dr["Name"]);
            //        _wlDetails.Stage = Convert.ToString(_dr["STAGE"]);
            //        _wlDetails.ProductType = Convert.ToString(_dr["TYPEOFREQUEST"]);
            //        _wlDetails.TypeOfReq = Convert.ToString(_dr["PRODUCTTYPE"]);
            //        _wlDetails.Email = Convert.ToString(_dr["EML_EMAIL"]);
            //        _wlDetails.CompanyName = Convert.ToString(_dr["TXT_COMPANYNAME"]);
            //        _wlDetails.City = Convert.ToString(_dr["TXT_CITY"]);
            //        _wlDetails.County = Convert.ToString(_dr["TXT_COUNTRY"]);

            //        _wlDetails.PhoneNo = Convert.ToString(_dr["TEL_PHONENUMBER"]);

            //        _wlDetails.WLNO = Convert.ToString(_dr["TXT_WLNO"]);
            //        _wlDetails.IndustryName = Convert.ToString(_dr["Txt_Industry"]);
            //        _wlDetails.RelatedContact = Convert.ToString(_dr["Related_Contact"]);
            //        _wlDetails.RelatedComapny = Convert.ToString(_dr["Related_Company"]);

            //        _coDetails.LinkedWLs.Add(_wlDetails);
            //    }
            //}

            //WLs count by Stage           
            if (dt8 != null && dt8.Rows.Count > 0)
            {
                int _totalCount = 0;
                for (int i = 0; i < dt8.Rows.Count; i++)
                {
                    int _istage = Convert.ToInt32(dt8.Rows[i]["MLS_Stage"]);
                    int _count = Convert.ToInt32(dt8.Rows[i]["WLCount"]);
                    _totalCount += _count;
                    if (_istage == 2)
                    {
                        _coDetails.WLCount_Assigned = _count;
                    }
                    else if (_istage == 3)
                    {
                        _coDetails.WLCount_Qualified = _count;
                    }
                }
                _coDetails.WLCount_Total = _totalCount;
            }

            ////AP Top 5 Details
            //if (dt9 != null && dt9.Rows.Count > 0)
            //{
            //    _coDetails.LinkedAPs = new List<APDetails>();
            //    _coDetails.APCount = dt9.Rows.Count;

            //    clUtil goUt = new clUtil();

            //    foreach (DataRow _dr in dt9.Rows)
            //    {
            //        APDetails _apdet = new APDetails();
            //        _apdet.Gid_id = Convert.ToString(_dr["Gid_Id"]);
            //        _apdet.Description = Convert.ToString(_dr["TXT_Description"]);
            //        _apdet.Type = Convert.ToString(_dr["APType"]);
            //        _apdet.StartDate = Convert.ToString(_dr["DTT_StartTime"]);
            //        _apdet.EndDate = Convert.ToString(_dr["DTT_EndTime"]);

            //        _coDetails.LinkedAPs.Add(_apdet);
            //    }
            //}

            return View(_coDetails);

        }

        public ActionResult Priority_TaskDetails()
        {
            clProject goP = (clProject)Util.GetInstance("p");
            string userid = goP.GetMe("GID_ID");
            Selltis.MVC.Models.Priority_TaskDetails _ptDetails = new Models.Priority_TaskDetails();

            goData = (clData)Util.GetInstance("data");
            System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
            System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
            System.Data.SqlClient.SqlDataReader reader = null;
            cmd.CommandText = "GetTasksByUser";
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Connection = sqlConnection1;

            //parameter
            System.Data.SqlClient.SqlParameter coId = new System.Data.SqlClient.SqlParameter("@UserId", System.Data.SqlDbType.VarChar);
            coId.Value = userid;
            cmd.Parameters.Add(coId);

            reader = cmd.ExecuteReader();

            DataSet ds = new DataSet();

            DataTable dt1 = new DataTable();
            DataTable dt2 = new DataTable();
            DataTable dt3 = new DataTable();
            DataTable dt4 = new DataTable();
            DataTable dt5 = new DataTable();
            DataTable dt6 = new DataTable();
            DataTable dt7 = new DataTable();
           

            dt1.TableName = "dt1";
            dt2.TableName = "dt2";
            dt3.TableName = "dt3";
            dt4.TableName = "dt4";
            dt5.TableName = "dt5";
            dt6.TableName = "dt6";
            dt7.TableName = "dt7";
           


            ds.Tables.Add(dt1);
            ds.Tables.Add(dt2);
            ds.Tables.Add(dt3);
            ds.Tables.Add(dt4);
            ds.Tables.Add(dt5);
            ds.Tables.Add(dt6);
            ds.Tables.Add(dt7);
            //ds.Tables.Add(dt8);
            //ds.Tables.Add(dt9);
            //ds.Tables.Add(dt10);

            ds.Load(reader, LoadOption.OverwriteChanges, dt1, dt2, dt3, dt4,dt5,dt6,dt7);

            //My Open Tasks
          
            if (dt1 != null && dt1.Rows.Count > 0)
            {
           
                //TaskCountByPriority _codet = new TaskCountByPriority();

                for (int i = 0; i < dt1.Rows.Count; i++)
                {                 
                   _ptDetails.MyOpenTasksCount = Convert.ToInt32(dt1.Rows[i]["MyOpenTasksCount"]);
                  
                }               
               
            }
            // My due for this week
            if (dt2 != null && dt2.Rows.Count > 0)
            {

                //TaskCountByPriority _codet = new TaskCountByPriority();

                for (int i = 0; i < dt2.Rows.Count; i++)
                {
                    _ptDetails.MyTasksDueThisWeekCount = Convert.ToInt32(dt2.Rows[i]["MyTasksDueThisWeekCount"]);

                }

            }
             //My Overdue Tasks
           
            if (dt3 != null && dt3.Rows.Count > 0)
            {
               
                for (int i = 0; i < dt3.Rows.Count; i++)
                {
                    _ptDetails.MyOverDueTasksCount = Convert.ToInt32(dt3.Rows[i]["MyOverDueTasksCount"]);
                }              
            }

            //My Completed Tasks 
           
            if (dt4 != null && dt4.Rows.Count > 0)
            {
               
                for (int i = 0; i < dt4.Rows.Count; i++)
                {
                    _ptDetails.MyCompletedTasksCount = Convert.ToInt32(dt4.Rows[i]["MyCompletedTasksCount"]);
                   
                }
               
               
            }

            //--My Tasks - New (Top 5)          
            
            if (dt5 != null && dt5.Rows.Count > 0)
            {
                _ptDetails.LinkedTSs = new List<TaskDetails>();

                _ptDetails.TSCount = dt5.Rows.Count;

                for (int i = 0; i < dt5.Rows.Count; i++)
                {
                    TaskDetails _codet = new TaskDetails();
                    _codet.Description = Convert.ToString(dt5.Rows[i]["TXT_Description"]);
                    _codet.ServiceTicket = Convert.ToString(dt5.Rows[i]["TXT_SERVICETICKET"]);
                    _codet.StartDate = Convert.ToDateTime(dt5.Rows[i]["DTT_StartDate"]);
                    _codet.DueDate = Convert.ToDateTime(dt5.Rows[i]["DTT_DueTime"]);
                    _codet.TaskCategoryName = Convert.ToString(dt5.Rows[i]["TXT_TaskCategoryName"]);
                    _codet.TaskSubCategoryName = Convert.ToString(dt5.Rows[i]["TXT_TaskSubCategoryName"]);
                    _codet.CompanyName = Convert.ToString(dt5.Rows[i]["TXT_CompanyName"]);
                    _codet.Requestor = Convert.ToString(dt5.Rows[i]["Requestor"]);
                    _codet.TaskStatus = Convert.ToString(dt5.Rows[i]["TaskStatus"]);
                    _codet.Gid_id = Convert.ToString(dt5.Rows[i]["GID_ID"]);
                    _ptDetails.LinkedTSs.Add(_codet);

                }

            }


            //--My Recent accounts
            if (dt6 != null && dt6.Rows.Count > 0)
            {
                _ptDetails.LinkedCOs = new List<Company>();
                _ptDetails.COCount = dt6.Rows.Count;
                foreach (DataRow _dr in dt6.Rows)
                {
                    Company _codet = new Company();
                    _codet.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _codet.CompanyName = Convert.ToString(_dr["TXT_CompanyName"]);
                    _codet.NAVID = Convert.ToString(_dr["TXT_NAVID"]);
                    _codet.PhoneNo = Convert.ToString(_dr["TEL_PhoneNo"]);
                    _codet.Address = Convert.ToString(_dr["TXT_AddrMailing"]);
                    _codet.City = Convert.ToString(_dr["TXT_CityMailing"]);
                    _codet.State = Convert.ToString(_dr["TXT_StateMailing"]);
                    _codet.Zip = Convert.ToString(_dr["TXT_ZipMailing"]);
                    _codet.Country = Convert.ToString(_dr["TXT_CountryMailing"]);
                    _codet.AccountType = Convert.ToString(_dr["AccountType"]);
                    _codet.Currency = Convert.ToString(_dr["Currency"]);
                    _codet.Code = Convert.ToString(_dr["TXT_Code"]);
                    _codet.Industry = Convert.ToString(_dr["Industry"]);

                    _ptDetails.LinkedCOs.Add(_codet);
                }
            }

            _ptDetails.Key= Guid.NewGuid().ToString();
            //Ac details
            if (dt7 != null && dt7.Rows.Count > 0)
            {
                _ptDetails.LinkedACs = new List<ACDetails>();
                _ptDetails.ACCount = dt7.Rows.Count;

                clUtil goUt = new clUtil();

                foreach (DataRow _dr in dt7.Rows)
                {
                    ACDetails _acdet = new ACDetails();
                    _acdet.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _acdet.Date = Convert.ToDateTime(_dr["DTT_StartTime"]);
                    _acdet.Notes = Convert.ToString(_dr["Notes"]); //goUt.StripHTML(Convert.ToString(_dr["Notes"]));
                    _acdet.Type = Convert.ToString(_dr["ACType"]);
                    _acdet.Contact = Convert.ToString(_dr["Contact"]);

                    _ptDetails.LinkedACs.Add(_acdet);
                }
            }
           
            return View(_ptDetails);
        }
               
        //Dashboard page ex: My Priorities
        public ActionResult PRP(string sPRPId)
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");

            ProfilePage _profilePage = new ProfilePage(sPRPId);

            //do loop for view count
            for (int i = 1; i <= _profilePage.ViewCount; i++)
            {
                //get view type
                string sViewId = goTR.StrRead(_profilePage.ProfilePageMetaData, "View" + i.ToString() + "Id", "");
                if (!string.IsNullOrEmpty(sViewId))
                {
                    string sViewMD = goMeta.PageRead("Global", sViewId, "", true);
                    string sViewType = goTR.StrRead(sViewMD, "ViewType", "");

                    switch (sViewType.ToUpper())
                    {
                        case "TILE":
                            TileView _tileView = new TileView(sViewId, sViewMD);
                            _tileView.ViewType = sViewType;
                            //_profilePage.PageHTML = _profilePage.PageHTML + RenderPartialViewToString("~/Views/DetailsPage/_TileView.cshtml", _tileView);
                            _profilePage.TopViewHTML = _profilePage.TopViewHTML + RenderPartialViewToString("~/Views/DetailsPage/_TileView.cshtml", _tileView);
                            break;
                        case "MV":
                            MiniView _miniView = new MiniView(sViewId, sViewMD);
                            _miniView.ViewType = sViewType;
                            _profilePage.BottomViewHTML = _profilePage.BottomViewHTML + RenderPartialViewToString("~/Views/DetailsPage/_MiniView.cshtml", _miniView);
                            break;
                        case "CHART":
                            ChartProfileView _chartview = new ChartProfileView(sViewId, sViewMD);
                            _profilePage.BottomViewHTML = _profilePage.BottomViewHTML + RenderPartialViewToString("~/Views/DetailsPage/_ChartView.cshtml", _chartview);
                            break;
                        default:
                            break;
                    }

                }

            }

            return View(_profilePage);
        }

        //File Profile Page (CO,CN, OP)
        public ActionResult PRF(string sPRPId,string sRecId)
        {
            Session["EventName"] = null;
            Session["QT_AttachdataPrintName"] = null;
            Session["QT_AttachdataPrint"] = null;
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");

            sRecId = Convert.ToString(Request.QueryString["sRecId"]);
            sPRPId = Convert.ToString(Request.QueryString["sPRPId"]);
            string ampRecId = Convert.ToString(Request.QueryString["amp;sRecId"]);
            if (sRecId == null)  sRecId = ampRecId;
            
            ProfilePage _profilePage = new ProfilePage(sPRPId);


            //get title
            string Row1Fields = goTR.StrRead(_profilePage.ProfilePageMetaData, "HeadRow1", "");
            string Row2Fields = goTR.StrRead(_profilePage.ProfilePageMetaData, "HeadRow2", "");
            string Row1Seperator = goTR.StrRead(_profilePage.ProfilePageMetaData, "HeadRow1Seperator", "");
            string Row2Seperator = goTR.StrRead(_profilePage.ProfilePageMetaData, "HeadRow2Seperator", "");
            _profilePage.File = goTR.StrRead(_profilePage.ProfilePageMetaData, "File", "");
            _profilePage.FileLabel = goTR.StrRead(_profilePage.ProfilePageMetaData, "FileLabel", "");
            _profilePage.RecordId = sRecId;
            Util.SetSessionValue(_profilePage.File + "_selectedprofilerecid", sRecId);

            string sFields = "Gid_id," + Row1Fields.Replace("|", ",") + "," + Row2Fields.Replace("|", ",");

            clRowSet rsData = new clRowSet(_profilePage.File, clC.SELL_READONLY, "GID_ID='"+ sRecId +"'", "", sFields);
            if(rsData.GetFirst()==1)
            {
                var strfields = Row1Fields.Split('|');
                string sval = "";
               
                foreach (string _field in strfields)
                {
                    sval = Convert.ToString(rsData.GetFieldVal(_field.Trim()));

                    if (_field.Trim().StartsWith("URL_") && !string.IsNullOrEmpty(sval))
                    {
                        sval = GetLinkHTML(sval);
                    }
                    else if (_field.Trim().StartsWith("CHK_") && !string.IsNullOrEmpty(sval))
                    {
                        string fieldLabel  = goData.GetFieldLabel(rsData.sRSFile, _field.Trim());
                        if (sval == "Unchecked")
                        {
                            sval = fieldLabel + "(No)";
                        }
                        else
                        {
                            sval = fieldLabel + "(Yes)";
                        }
                    }
                    if (string.IsNullOrEmpty(_profilePage.TitleRow1))
                    {
                        _profilePage.TitleRow1 = sval;
                    }
                    else
                    {
                        _profilePage.TitleRow1 = _profilePage.TitleRow1 + " | " + sval;
                    }
                }

                strfields = Row2Fields.Split('|');
                foreach (string _field in strfields)
                {
                    sval = Convert.ToString(rsData.GetFieldVal(_field.Trim()));
                   
                    if (_field.Trim().StartsWith("URL_") && !string.IsNullOrEmpty(sval))
                    {
                        sval = GetLinkHTML(sval);
                    }
                    else if (_field.Trim().StartsWith("CHK_") && !string.IsNullOrEmpty(sval))
                    {
                        string fieldLabel = goData.GetFieldLabel(rsData.sRSFile, _field.Trim());
                        if (sval == "Unchecked")
                        { 
                            sval = fieldLabel + "(No)";
                        }
                        else {
                            sval = fieldLabel + "(Yes)";
                        }
                    }
                    if (string.IsNullOrEmpty(_profilePage.TitleRow2))
                    {
                        _profilePage.TitleRow2 = sval;
                    }
                    else
                    {
                        _profilePage.TitleRow2 = _profilePage.TitleRow2 + " | " + sval;
                    }
                }

            }


            //do loop for view count
            for (int i = 1; i <= _profilePage.ViewCount; i++)
            {
                //get view type
                string sViewId = goTR.StrRead(_profilePage.ProfilePageMetaData, "View" + i.ToString() + "Id", "");
                if (!string.IsNullOrEmpty(sViewId))
                {
                    string sViewMD = goMeta.PageRead("Global", sViewId, "", true);
                    string sViewType = goTR.StrRead(sViewMD, "ViewType", "");

                    switch (sViewType.ToUpper())
                    {
                        case "TILE":
                            TileView _tileView = new TileView(sViewId, sViewMD, sRecId);
                            _tileView.ViewType = sViewType;
                            _profilePage.TopViewHTML = _profilePage.TopViewHTML + RenderPartialViewToString("~/Views/DetailsPage/_TileView.cshtml", _tileView);
                            break;
                        case "MV":
                            MiniView _miniView = new MiniView(sViewId, sViewMD, sRecId);
                            _miniView.ViewType = sViewType;
                            _profilePage.BottomViewHTML = _profilePage.BottomViewHTML + RenderPartialViewToString("~/Views/DetailsPage/_MiniView.cshtml", _miniView);
                            break;
                        case "CHART":
                            ChartProfileView _chartview = new ChartProfileView(sViewId, sViewMD, sRecId);
                            _profilePage.BottomViewHTML = _profilePage.BottomViewHTML + RenderPartialViewToString("~/Views/DetailsPage/_ChartView.cshtml", _chartview);
                            break;
                        default:
                            break;
                    }

                }

            }
            
            return View(_profilePage);
        }

        private static string GetLinkHTML(string sval)
        {
            if (sval.ToLower().Contains("linkedin"))
            {
                sval = "<a href='" + sval + "' style=\"color:white !important;\" target='new' title='LinkedIn'><i class=\"entypo-linkedin\"></i></a>";
            }
            else if (sval.ToLower().Contains("facebook"))
            {
                sval = "<a href='" + sval + "' style=\"color:white !important;\" target='new' title='Facebook'><i class=\"entypo-facebook\"></i></a>";
            }
            else
            {
                sval = "<a href='" + sval + "' style=\"color:white !important;\" target='new' title='Link'><span class=\"entypo-link\"></span></a>";
            }

            return sval;
        }

        protected string RenderPartialViewToString(string viewName, object model, string MasterSelID = null)
        {
            if (string.IsNullOrEmpty(viewName))
                viewName = ControllerContext.RouteData.GetRequiredString("action");
            //if (!string.IsNullOrEmpty(MasterSelID))
            //{
            var _model = (ProfileView)model;           
            model = _model;
            //}
            ViewData.Model = model;

            using (StringWriter sw = new StringWriter())
            {
                ViewEngineResult viewResult = ViewEngines.Engines.FindPartialView(ControllerContext, viewName);
                ViewContext viewContext = new ViewContext(ControllerContext, viewResult.View, ViewData, TempData, sw);
                viewResult.View.Render(viewContext, sw);
                return sw.GetStringBuilder().ToString();
            }
        }

        //public string RunCusScript(string sScriptName,string sGidId)
        //{
        //    ScriptManager _scriptManager = new ScriptManager();
        //    object par_doCallingObject = null;
        //    object par_oReturn = null;
        //    bool par_bRunNext = true;
        //    string par_sSections = "";

        //    _scriptManager.RunScript(sScriptName, ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, sGidId);
        //    return par_oReturn.ToString();
        //}

        public ActionResult ContactV2Details(string sContactId)
        {
            //ViewBag.scomid = sCompanyId;

            Selltis.MVC.Models.ContactDetails _coDetails = new Models.ContactDetails();

            goData = (clData)Util.GetInstance("data");
            System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
            System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
            System.Data.SqlClient.SqlDataReader reader = null;
            cmd.CommandText = "GetContactDetailsByID";
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Connection = sqlConnection1;

            //parameter
            System.Data.SqlClient.SqlParameter coId = new System.Data.SqlClient.SqlParameter("@GidId", System.Data.SqlDbType.VarChar);
            coId.Value = sContactId;
            cmd.Parameters.Add(coId);

            reader = cmd.ExecuteReader();

            DataSet ds = new DataSet();

            DataTable dt1 = new DataTable();
            DataTable dt2 = new DataTable();
            DataTable dt3 = new DataTable();
            DataTable dt4 = new DataTable();
            DataTable dt5 = new DataTable();

            dt1.TableName = "dt1";
            dt2.TableName = "dt2";
            dt3.TableName = "dt3";
            dt4.TableName = "dt4";
            dt5.TableName = "dt5";

            ds.Tables.Add(dt1);
            ds.Tables.Add(dt2);
            ds.Tables.Add(dt3);
            ds.Tables.Add(dt4);
            ds.Tables.Add(dt5);

            ds.Load(reader, LoadOption.OverwriteChanges, dt1, dt2, dt3, dt4, dt5);

            //tiles
            if (dt1 != null && dt1.Rows.Count > 0)
            {
                _coDetails.Gid_id = Convert.ToString(dt1.Rows[0]["Gid_Id"]);
                _coDetails.CNName = Convert.ToString(dt1.Rows[0]["Name"]);
                _coDetails.Title = Convert.ToString(dt1.Rows[0]["TXT_TitleText"]);
                _coDetails.Email = Convert.ToString(dt1.Rows[0]["Eml_Email"]);
                _coDetails.Phone = Convert.ToString(dt1.Rows[0]["TEL_BusPhone"]);
                _coDetails.Mobile = Convert.ToString(dt1.Rows[0]["TEL_CellPhone"]);
                _coDetails.ContactofUser = Convert.ToString(dt1.Rows[0]["ContactofUser"]);
                _coDetails.AccountName = Convert.ToString(dt1.Rows[0]["AccountName"]);
                _coDetails.AddressMailing = Convert.ToString(dt1.Rows[0]["AddressMailing"]);
                _coDetails.CityMailing = Convert.ToString(dt1.Rows[0]["CityMailing"]);
                _coDetails.StateMailing = Convert.ToString(dt1.Rows[0]["StateMailing"]);
                _coDetails.ZipMailing = Convert.ToString(dt1.Rows[0]["ZipMailing"]);

                // _coDetails.COUNTRYMAILING = Convert.ToString(dt1.Rows[0]["TXT_COUNTRYMAILING"]);
                _coDetails.COUNTRYMAILING = Convert.ToString(dt1.Rows[0]["maillinCountry"]);
                _coDetails.NameFirst = Convert.ToString(dt1.Rows[0]["TXT_NameFirst"]);
                _coDetails.NameLast = Convert.ToString(dt1.Rows[0]["TXT_NAMELAST"]);
                _coDetails.Industry = Convert.ToString(dt1.Rows[0]["Industry"]);
                _coDetails.Source = Convert.ToString(dt1.Rows[0]["Source"]);
                _coDetails.ContactCode = Convert.ToString(dt1.Rows[0]["TXT_CONTACTCODE"]);
                _coDetails.Salutation = Convert.ToString(dt1.Rows[0]["MLS_SALUTATION"]);
                _coDetails.Level = Convert.ToString(dt1.Rows[0]["MLS_LEVEL"]);
                _coDetails.FunctionArea = Convert.ToString(dt1.Rows[0]["TXT_FUNCTIONALAREA"]);

                _coDetails.Chk_NoLongerG = Convert.ToString(dt1.Rows[0]["CHK_NOLONGERWITHCOMPANY"]);
                _coDetails.Chk_DoNotCall = Convert.ToString(dt1.Rows[0]["CHK_DONOTCALL"]);
                _coDetails.Chk_Email = Convert.ToString(dt1.Rows[0]["CHK_EMAILOPTOUT"]);

                _coDetails.OtherAdd = Convert.ToString(dt1.Rows[0]["TXT_ADDROTHER"]);
                _coDetails.OtherCity = Convert.ToString(dt1.Rows[0]["TXT_CITYOTHER"]);
                _coDetails.OtherState = Convert.ToString(dt1.Rows[0]["TXT_STATEOTHER"]);
                _coDetails.OtherZip = Convert.ToString(dt1.Rows[0]["TXT_ZIPOTHER"]);
                //_coDetails.OtherCountry = Convert.ToString(dt1.Rows[0]["TXT_COUNTRYOTHER"]);
                _coDetails.OtherCountry = Convert.ToString(dt1.Rows[0]["otherCounty"]);
                _coDetails.AccountId = Convert.ToString(dt1.Rows[0]["AccountId"]);
            }

            //Linked OPs
            if (dt2 != null && dt2.Rows.Count > 0)
            {
                _coDetails.LinkedOPs = new List<OPDetails>();
                _coDetails.OPCount = dt2.Rows.Count;
                foreach (DataRow _dr in dt2.Rows)
                {
                    OPDetails _newop = new OPDetails();
                    _newop.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _newop.OPName = Convert.ToString(_dr["TXT_OPPORTUNITYNAME"]);
                    _newop.LOB = Convert.ToString(_dr["LOB"]);
                    _newop.OPType = Convert.ToString(_dr["OPType"]);
                    _newop.OPOwner = Convert.ToString(_dr["OppOwner"]);
                    _newop.Stage = Convert.ToString(_dr["Stage"]);
                    _newop.Amount = Convert.ToDouble(_dr["CUR_AMOUNT"]);
                    _newop.ExpCloseDate = Convert.ToDateTime(_dr["DTT_ExpCloseDate"]);
                    _newop.TotalAmount = Convert.ToDouble(_dr["CUR_TOTALAMOUNT"]);
                    _newop.OppNumber = Convert.ToString(_dr["TXT_OPPNO"]);
                    _newop.Currency = Convert.ToString(_dr["Currency"]);
                    _coDetails.LinkedOPs.Add(_newop);
                }
            }

            //Ac details
            if (dt3 != null && dt3.Rows.Count > 0)
            {
                _coDetails.LinkedACs = new List<ACDetails>();
                _coDetails.ACCount = dt3.Rows.Count;

                clUtil goUt = new clUtil();

                foreach (DataRow _dr in dt3.Rows)
                {
                    ACDetails _acdet = new ACDetails();
                    _acdet.Gid_id = Convert.ToString(_dr["Gid_Id"]);
                    _acdet.Date = Convert.ToDateTime(_dr["DTT_StartTime"]);
                    _acdet.Notes = Convert.ToString(_dr["Notes"]); //goUt.StripHTML(Convert.ToString(_dr["Notes"]));
                    _acdet.Type = Convert.ToString(_dr["ACType"]);

                    _coDetails.LinkedACs.Add(_acdet);
                }
            }


            //tiles
            if (dt4 != null && dt4.Rows.Count > 0)
            {
                _coDetails.ActiveOPCount = Convert.ToInt32(dt4.Rows[0]["ActiveOPCount"]);
                _coDetails.ActiveOPValue = Convert.ToDouble(dt4.Rows[0]["ActiveOPValue"]);
            }

            //OP count by status
            if (dt5 != null && dt5.Rows.Count > 0)
            {
                int _totalCount = 0;
                for (int i = 0; i < dt5.Rows.Count; i++)
                {
                    int _istage = Convert.ToInt32(dt5.Rows[i]["MLS_Stage"]);
                    int _count = Convert.ToInt32(dt5.Rows[i]["OPCount"]);
                    _totalCount = _totalCount + _count;
                    if (_istage == 1)
                    {
                        _coDetails.OPCount_Creating = _count;
                    }
                    else if (_istage == 2)
                    {
                        _coDetails.OPCount_Proposed = _count;
                    }
                    else if (_istage == 3)
                    {
                        _coDetails.OPCount_Negotiations = _count;
                    }
                    else if (_istage == 4)
                    {
                        _coDetails.OPCount_Hold = _count;
                    }
                    else if (_istage == 5 || _istage == 6)
                    {
                        _coDetails.OPCount_Closed = _coDetails.OPCount_Closed + _count;
                    }

                }
                _coDetails.OPCount_Total = _totalCount;
            }

            _coDetails.Key = Guid.NewGuid().ToString();

            return View(_coDetails);

        }

    }
}