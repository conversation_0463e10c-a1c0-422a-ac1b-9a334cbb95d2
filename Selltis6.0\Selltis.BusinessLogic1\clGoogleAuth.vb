﻿Imports Microsoft.VisualBasic
Imports Google.GData.Client

Public Class clGoogleAuth

    Private clientId As String = "316819080531.apps.googleusercontent.com"
    Private clientSecret As String = "72TLUOczva90D1nagN7p19Bi"
    Private applicationName As String = "Selltis Link™ for Google®"
    Private redirectUri As String = "urn:ietf:wg:oauth:2.0:oob"
    ' Requesting access to Contacts, Calendat, and Tasks APIs
    Private scopes As String = "https://www.google.com/m8/feeds/ https://www.googleapis.com/auth/calendar https://www.googleapis.com/auth/tasks"


    Public Function GetParametersFromRefreshToken(ByVal sRefreshToken As String) As OAuth2Parameters

        Dim parameters As New OAuth2Parameters()
        Try

            parameters.ClientId = clientId
            parameters.ClientSecret = clientSecret
            parameters.RedirectUri = redirectUri
            parameters.Scope = scopes
            parameters.ApprovalPrompt = "force"

            parameters.RefreshToken = sRefreshToken

            OAuthUtil.RefreshAccessToken(parameters)
            Return parameters


        Catch ex As Exception
            'xxx log error
            Return parameters
        End Try


    End Function

    Public Function GetRefreshTokenFromAuthCode(ByVal sAuthCode As String) As String


        Try

            Dim parameters As New OAuth2Parameters()
            parameters.ClientId = clientId
            parameters.ClientSecret = clientSecret
            parameters.RedirectUri = redirectUri
            parameters.Scope = scopes
            parameters.ApprovalPrompt = "force"

            parameters.AccessCode = sAuthCode
            OAuthUtil.GetAccessToken(parameters)

            OAuthUtil.RefreshAccessToken(parameters)
            Return parameters.RefreshToken

        Catch ex As Exception

            'xxx log error
            Return ""

        End Try



    End Function

    Public Function GetAuthenticationURL() As String


        Dim parameters As New OAuth2Parameters()
        parameters.ClientId = clientId
        parameters.ClientSecret = clientSecret
        parameters.RedirectUri = redirectUri
        parameters.Scope = scopes
        parameters.ApprovalPrompt = "force"

        Dim url As String = OAuthUtil.CreateOAuth2AuthorizationUrl(parameters)

        Return url


    End Function

End Class
