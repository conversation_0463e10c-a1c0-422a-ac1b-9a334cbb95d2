﻿CREATE TABLE [dbo].[EX_Related_AC] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Expense_Related_Activity_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_EX] UNIQUEIDENTIFIER NOT NULL,
    [GID_AC] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_EX_Related_AC] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_AC_Connected_EX] FOREIGN KEY ([GID_EX]) REFERENCES [dbo].[EX] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_EX_Related_AC] FOREIGN KEY ([GID_AC]) REFERENCES [dbo].[AC] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[EX_Related_AC] NOCHECK CONSTRAINT [LNK_AC_Connected_EX];


GO
ALTER TABLE [dbo].[EX_Related_AC] NOCHECK CONSTRAINT [LNK_EX_Related_AC];


GO
CREATE CLUSTERED INDEX [IX_EX_Related_AC]
    ON [dbo].[EX_Related_AC]([GID_AC] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_AC_Connected_EX]
    ON [dbo].[EX_Related_AC]([GID_EX] ASC);

