﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.IO;
using System.Net;
using Newtonsoft.Json;
//using System.Web.Script.Serialization;
using System.Xml;
using System.Text.RegularExpressions;
using System.Diagnostics;
using Selltis.BusinessLogic;
using Selltis.Core;

namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clProject goP;
        private clLog goLog;
        private clError goErr;
        private clPerm goPerm;
        private ClUI goUI;
        ScriptManager scriptManager = new ScriptManager();

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";

        public string sError;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();
            Util.SetSessionValue("SkipQLSpecificLogic", "Y");
        }
        public ScriptsCustom()
        {
            Initialize();
        }

        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}




            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }

        public bool AC_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool AC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CalcQuoteTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO 11262015 TKT 815: Disabled Merged checkbox
            doForm.SetControlState("CHK_Merged", 4);

            par_doCallingObject = doForm;
            return true;
        }
        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO 11262015 TKT 815: Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CN_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");

                par_doCallingObject = doForm;
                return false;
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO 11262015 TKT 815: Merge Functionality - run at end of CN_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (doForm.doRS.GetFieldVal("CHK_Merged", 2) != null && Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    
                        if (Convert.ToString(doForm.oVar.GetVar("CN_Merge")) != "1")
                        {
                            // Don't allow merge of contact to itself
                            string GID_ID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));
                            string LNK_Mergedto_CN_Var = (doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID") == null) ? null : doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID").ToString();
                            if (GID_ID == LNK_Mergedto_CN_Var)
                            {
                                //doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CN", "MergeFail");
                                doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CN", "MergeFail");
                            }
                            else
                            {
                                string LNK_MergedTo_CN = Convert.ToString(doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name"));
                                //doForm.MessageBox("This record will be merged to the target record, '" + doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null/* Conversion error: Set to default value for this argument */, "YES", "NO", "CANCEL", "CN", "Merge");
                                doForm.MessageBox("This record will be merged to the target record, '" + LNK_MergedTo_CN + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CN", "Merge");

                            }
                                
                        }
                }
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool CN_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CN_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO 11262015 TKT 815: Disabled Merged checkbox
            doForm.SetControlState("CHK_Merged", 4);

            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO 11262015 TKT 815: Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CO_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                par_doCallingObject = doForm;
                return false;
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // SKO 11262015 TKT 815: Merge Functionality - run at end of CO_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (doForm.doRS.GetFieldVal("CHK_Merged", 2) != null && Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                        if (Convert.ToString(doForm.oVar.GetVar("CO_Merge")) != "1")
                        {
                            // Don't allow merge of company to itself
                            string GID_ID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));
                            string LNK_Mergedto_CO_Var = (doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID") == null) ? null : doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID").ToString();
                            if (GID_ID == LNK_Mergedto_CO_Var)
                            {
                                //doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "MessageBoxEvent", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm, null/* Conversion error: Set to default value for this argument */, "OK", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "CO", "MergeFail");
                                doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CO", "MergeFail");
                            }
                            else
                            {
                                string LNK_MergedTo_CO = Convert.ToString(doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name"));
                                doForm.MessageBox("This record will be merged to the target record, '" + LNK_MergedTo_CO + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CO", "Merge");
                            }
                        }
                }
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool MergeRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 7/8/2014 Added for merge
            par_bRunNext = false;

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;     // Record being merged, will be deactivated
            clRowSet doRSMergeTo;                         // Good record, stays active

            clArray aFields;
            clArray aLinks;
            string sField;
            string sFieldType;
            clArray doLink = new clArray();
            string[] sLinkType;
            string sReturn = "";

            try
            {
                // Enumerate schema
                // aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                // aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                // Get mergeto record from rowset of merged record. User selects mergeto record on the form
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + Convert.ToString(doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName())) + "'", "", "**", -1, "", "", "", "", "", true, true, false, false, -1, "", true);                             
                if (doRSMergeTo.GetFirst() == 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Microsoft.VisualBasic.Strings.Left(sField, 3);
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                {
                                    if (Convert.ToString(doRSMergeTo.GetFieldVal(sField)) == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    break;
                                }

                            case "MMO":
                                {
                                    // Append
                                    if (Convert.ToString(doRSMergeTo.GetFieldVal(sField)) == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    else
                                        doRSMergeTo.SetFieldVal(sField, Convert.ToString(doRSMergeTo.GetFieldVal(sField)) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + Convert.ToString(doRSMerge.GetFieldVal("SYS_Name")) + " ==" + Constants.vbCrLf + Convert.ToString(doRSMerge.GetFieldVal(sField)));
                                    break;
                                }

                            case "CHK":
                                {
                                    if (doRSMergeTo.GetFieldVal(sField, 2) != null && Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }

                            case "MLS":
                                {
                                    if (doRSMergeTo.GetFieldVal(sField, 2) != null && Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }
                        }
                    }

                    for (int i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        // If NN link, copy all. If N1, copy only if blank
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(Convert.ToInt64(i))), Strings.Chr(9).ToString());
                        if (sLinkType[4] == "NN" | sLinkType[1] == "2")
                        {
                            oTable = null;  //SB 02222019 Pass it as null, otherwise it will throw error.
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else if (doRSMergeTo.GetFieldVal(aLinks.GetItem(i)) != null && doRSMergeTo.GetFieldVal(aLinks.GetItem(i)).ToString() == "")
                        {
                            oTable = null;  //SB 02222019 Pass it as null, otherwise it will throw error.
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                    }

                    // Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    // Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    // Check Active if exists
                    if (goData.IsFieldValid(doRSMerge.GetFileName(), "CHK_ACTIVEFIELD") == true)
                        doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);

                    // Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    // Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    // Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();
                }

                sReturn = "Success";
            }
            catch (Exception ex)
            {
                sReturn = "Failed";
            }

            par_oReturn = sReturn;
            par_doCallingObject = doRSMerge;
            return true;
        }
        public bool MessageBoxEvent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // Par_s5 will always be the name of the script that called doform.MessageBox
            // Par_s1 will be whatever button the user clicked.
            // Par_s2-Par_s4 can be whatever else you want to pass.
            // In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sJournal = "";
            string sWork = "";
            string sSolution = "";
            string sClosenotes = "";
            string sCorrectiveaction = "";

            switch (Strings.UCase(par_s5))
            {
                case "MERGE":
                    {
                        par_bRunNext = false;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // run merge script, continue save
                                    par_doCallingObject = doForm.doRS;
                                    //goScr.RunScript("MergeRecord", doForm.doRS);
                                    scriptManager.RunScript("MergeRecord", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                                    doForm.doRS = (clRowSet)par_doCallingObject;
                                    break;
                                }

                            case "NO":
                                {
                                    // Clear merged to co linkbox, continue save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    break;
                                }

                            case "CANCEL":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "MERGEFAIL":
                    {
                        par_bRunNext = false;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool CO_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }        
        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFileName = "", string par_sReverseDirection = "", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here. To not override the default sort, par_oReturn must be "".
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);


            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {

                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm = (Form)par_doCallingObject;

            doForm.doRS.SetFieldVal("DTT_OppDate", "Today|Now");
            ClearLineFields(doForm);
            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;


            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                doRS.SetFieldVal("CUR_VALUE", curValue);
                doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

            }
            par_doCallingObject = doRS;
            par_bRunNext = false;
            return true;
        }
        public bool OP_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm =(Form)par_doCallingObject;
            par_doCallingObject = doForm;
            return true;
        }

        public bool OL_RecordBeforeDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.oVar.SetVar("OppID", doRS.GetFieldVal("LNK_IN_OP"));

            par_doCallingObject = doRS;

            return true;
        }

        public bool OL_RecordAfterDelete_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_s2 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sID = doRS.oVar.GetVar("OppID").ToString();

            if (!string.IsNullOrEmpty(sID))
            {
                clRowSet doOPLines = default(clRowSet);

                //Create a rowset of quote lines linked to the deleted quote.
                doOPLines = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo,Gid_id", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    StringBuilder sbQuery = new StringBuilder();
                    for (int i = 1; i <= doOPLines.Count(); i++)
                    {
                        double iLineno = Convert.ToDouble(doOPLines.GetFieldVal("SR__LineNo"));
                        string sGid_id = Convert.ToString(doOPLines.GetFieldVal("Gid_ID"));
                        string sTemp = "#" + goTR.Pad(i.ToString(), 6, " ", "L", true, "R");
                        sbQuery.AppendLine("Update OL Set SR__LineNo='" + i.ToString() + "',sys_name='" + sTemp + "' where Gid_id='" + sGid_id + "' AND SR__LineNo = '" + iLineno.ToString() + "'");

                        if (doOPLines.GetNext() == 0)
                        {
                            break;
                        }

                    }
                    if (!string.IsNullOrEmpty(sbQuery.ToString()))
                    {
                        bool bretval = goData.RunSQLQuery(sbQuery.ToString());
                    }

                }

                doOPLines = null;

            }

            par_doCallingObject = doRS;

            return true;
        }
        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
       {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }
            par_doCallingObject = doForm;
            par_bRunNext = false;
            return true;
        }
        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }

        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));


            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_FOR_MO,LNK_RELATED_PD,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);
            rsOL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);

            //This will generate line no's in mobile.
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }

            par_doCallingObject = doRS;

            return true;
        }

        public bool PR_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool PR_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }        
        public bool QL_FormOnLoadRecord_pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            // If gbWriteLog Then Dim oLog As Object = New clLogObj(sProc, "Start", 3)

            // PORTING FROM COMMENCE IN PROGRESS. MI.

            // ******************************
            // DIFFERS FROM SHIPPING DATABASE
            // ******************************

            // --- HISTORY ---
            // 2005/08/17 10:46:47 MAR Added traces to diagnose MLS_REASONWONLOST not being pulled properly from Quote

            // goP.TraceLine("", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            // SKO 11052015 Ticket#786
            par_bRunNext = false;


            long lLine;
            long lHighestLine;
            string sWork;
            string sColor = Convert.ToString(goP.GetVar("sMandatoryFieldColor"));

            // goP.TraceLine("GetFieldVal('LNK_IN_QT%%LNK_CREDITEDTO_US',2): '" & doForm.dors.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2) & "'", "", sProc)
            // goP.TraceLine("GetFieldVal('LNK_IN_QT%%LNK_CREDITEDTO_US',1): '" & doForm.dors.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 1) & "'", "", sProc)

            // Set mandatory field color
            // doForm.SetFieldProperty("LNK_FOR_MO", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("SR__QTY", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("CUR_COST", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("SR__LINENO", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("LNK_CreditedTo_US", "LABELCOLOR", sColor)
            // doForm.SetFieldProperty("LNK_Peer_US", "LABELCOLOR", sColor)
            doForm.SetFieldProperty("LNK_IN_QT", "LABELCOLOR", sColor);

            // Set button tooltips
            doForm.SetFieldProperties("BTN_RecalcTotals", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Recalculate Quote Line totals");
            doForm.SetFieldProperties("BTN_UseModelPrice", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Fill unit price and cost from the linked Model");
            doForm.SetFieldProperties("BTN_INSERTSPEC", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend specifications from the linked Model");

            // CS: Set status and reason fields grayed out
            // 7/3/07: Can set status independnet of Quote Status doForm.setcontrolstate("MLS_STATUS", 4)
            // 5/28/10: Allow setting reason independent of Quote status
            // doForm.SetControlState("MLS_REASONWONLOST", 4)

            // CS: 5/21/09: Gray out CUR_Cost field on the QL. The Cost will be calculated based on Model Cost * Qty
            // If a customer needs to manually enter costs, this can be customized to ungray the field
            // SKO 11052015 Ticket#786 please make subtotal and total cost editable
            // doForm.SetControlState("CUR_COST", 4)

            // If save button is disabled, disable Save and Create unlinked button
            if (doForm.SaveEnabled() == false)
                doForm.SetControlState("BTN_SAVECRU", 4);


            // Grayed fields
            doForm.SetControlState("MMO_ImportData", 4);
            // SKO 11052015 Ticket#786 please make subtotal and total cost editable
            // doForm.SetControlState("cur_subtotal", 4)
            doForm.SetControlState("sr__salestaxpercent", 4);
            doForm.SetControlState("cur_salestax", 4);

            // locked
            doForm.SetControlState("lnk_for_pd", 1);
            doForm.SetControlState("lnk_related_dv", 1);
            doForm.SetControlState("lnk_involves_us", 1);
            doForm.SetControlState("lnk_related_ve", 1);

            // grayed button
            doForm.SetControlState("LNK_IN_QT", 5);
            doForm.SetControlState("LNK_TO_CO", 5);

            // ----------- DEFAULT VALUES ------------
            if (doForm.GetMode() == "CREATION")
            {
                if (doForm.doRS.GetLinkCount("LNK_IN_QT") < 1)
                {
                    //doForm.MessageBox("You are creating a Quote Line that is not linked to a Quote." + Constants.vbCrLf + Constants.vbCrLf + "To create a new Quote Line, open an existing Quote or create a new Quote and click New on its Details tab.");
                    doForm.MessageBox("You are creating a Quote Line that is not linked to a Quote." + System.Environment.NewLine + System.Environment.NewLine + "To create a new Quote Line, open an existing Quote or create a new Quote and click New on its Details tab.");
                    par_doCallingObject = doForm;
                    return true;
                }
                else
                {
                    // CS 6/22/09: Create rowset for getting all Quote values instead of all double hops below (creating multiple rowsets)
                    clRowSet doRSQT = new clRowSet("QT", 3, "GID_ID='" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_IN_QT")) + "'", "", "DTT_TIME,DTE_EXPCLOSEDATE,LNK_CREDITEDTO_US,LNK_PEER_US,LNK_RELATED_PR,LNK_TO_CO,SR__SALESTAXPERCENT");
                    // doForm.doRS.SetFieldVal("DTT_QTETIME", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTT_TIME"))
                    // doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTE_EXPCLOSEDATE"))
                    // doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2), 2)
                    // doForm.doRS.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_PEER_US", 2), 2)
                    // doForm.doRS.SetFieldVal("LNK_RELATED_PR", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_RELATED_PR", 2), 2)
                    // doForm.doRS.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_TO_CO", 2), 2)
                    // doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"))
                    if (doRSQT.GetFirst() == 1)
                    {
                        doForm.doRS.SetFieldVal("DTT_QTETIME", doRSQT.GetFieldVal("DTT_TIME"));
                        doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doRSQT.GetFieldVal("DTE_EXPCLOSEDATE"));
                        doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doRSQT.GetFieldVal("LNK_CREDITEDTO_US", 2), 2);
                        doForm.doRS.SetFieldVal("LNK_PEER_US", doRSQT.GetFieldVal("LNK_PEER_US", 2), 2);
                        doForm.doRS.SetFieldVal("LNK_RELATED_PR", doRSQT.GetFieldVal("LNK_RELATED_PR", 2), 2);
                        doForm.doRS.SetFieldVal("LNK_TO_CO", doRSQT.GetFieldVal("LNK_TO_CO", 2), 2);
                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", doRSQT.GetFieldVal("SR__SALESTAXPERCENT"));
                    }

                    // CS 8/27/08 If WOP to manage users independent of QT is off, always gray out fields on QL
                    if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_USERMGMT") != null && doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_USERMGMT").ToString() != "1")
                    {
                            doForm.SetControlState("LNK_CREDITEDTO_US", 4);
                            doForm.SetControlState("LNK_PEER_US", 4);
                    }

                    // -------- Status, Reason Won/Lost -------
                    // ******************************
                    // In Selltis DB, Status and Reason Won/Lost are the same as in Quote
                    // goP.TraceLine("Setting MLS_STATUS from Quote: '" & doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2) & "'", "", sProc)
                    // goLog.Log("QL_FormOnLoadRecord", doForm.dors.getfieldval("LNK_IN_QT%%MLS_status", 2), , , True)
                    clArray oArray = new clArray();
                    string sVal = "";
                    clArray oArrayReason = new clArray(); // CS 5/28/10: Allow setting reason for each QL if can set status ind of QT
                    string sReason = "";
                    // CS 7/3/07: Allow setting QL status independent of QT
                    // oArray = doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2)
                    // If oArray.GetDimension > 0 Then sVal = oArray.GetItem(1)
                    // If sVal <> "" Then
                    // doForm.doRS.SetFieldVal("MLS_Status", sVal, 2)
                    // End If
                    // oArray = Nothing
                    // sVal = ""


                    // If quote set to set status of QL from QT, set it here.
                    // CS 6/12/08: Changed code from setting status using friendly value.
                    // This caused issue b/c the friendly names did not match (ie On Hold in QT vs 20 On Hold in QL)

                    // CS 6/20/08 If WOP is off, always gray out status and set to
                    // same as QT
                    // CS 5/28/10: Do same for Reason Won/Lost
                    if ((doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_STATUSMGMT") != null) && doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_STATUSMGMT").ToString() != "1")
                    {
                        doForm.SetControlState("MLS_STATUS", 4);
                        doForm.SetControlState("MLS_REASONWONLOST", 4);
                        oArray = (clArray)doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2);
                        oArrayReason = (clArray)doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2);
                        sVal = oArray.GetItem(1);
                        sReason = oArrayReason.GetItem(1);
                        if (sVal != "")
                        {
                            doForm.doRS.SetFieldVal("MLS_STATUS", goTR.StringToNum(sVal, "", ref par_iValid), 2);
                            doForm.SetControlState("MLS_STATUS", 4);
                        }
                        if (sReason != "")
                        {
                            doForm.doRS.SetFieldVal("MLS_REASONWONLOST", goTR.StringToNum(sReason, "", ref par_iValid), 2);
                            doForm.SetControlState("MLS_REASONWONLOST", 4);
                        }
                        sReason = "";
                        oArrayReason = null/* TODO Change to default(_) if this is not a reference type */;
                    }
                    else if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_IN_QT%%CHK_UPDQLSTATUS")).ToUpper() == "CHECKED")
                    {
                        oArray = (clArray)doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2);
                        oArrayReason = (clArray)doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2);
                        sVal = oArray.GetItem(1);
                        sReason = oArrayReason.GetItem(1);
                        if (sVal != "")
                        {
                            // doForm.doRS.SetFieldVal("MLS_STATUS", doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 1), 1)
                            doForm.doRS.SetFieldVal("MLS_STATUS", goTR.StringToNum(sVal, "", ref par_iValid), 2);
                            doForm.SetControlState("MLS_STATUS", 4);
                        }
                        else
                            // status active
                            doForm.SetControlState("MLS_STATUS", 0);
                        sVal = "";
                        oArray = null/* TODO Change to default(_) if this is not a reference type */;
                        if (sReason != "")
                        {
                            doForm.doRS.SetFieldVal("MLS_reasonwonlost", goTR.StringToNum(sReason, "", ref par_iValid), 2);
                            doForm.SetControlState("MLS_REASONWONLOST", 4);
                        }
                        else
                            // status active
                            doForm.SetControlState("MLS_REASONWONLOST", 0);
                        sReason = "";
                        oArrayReason = null/* TODO Change to default(_) if this is not a reference type */;
                    }

                    // CS 5/28/10 commented
                    // oArray = doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2)
                    // If oArray.GetDimension > 0 Then sVal = oArray.GetItem(1)
                    // If sVal <> "" Then
                    // doForm.doRS.SetFieldVal("MLS_REASONWONLOST", sVal, 2)
                    // End If


                    // doForm.dors.SetFieldVal("MLS_STATUS", doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2), 2)
                    // goP.TraceLine("Setting MLS_REASONWONLOST from Quote: '" & doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2) & "'", "", sProc)
                    // doForm.dors.SetFieldVal("MLS_REASONWONLOST", doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2), 2)
                    // ******************************
                    // ----------------------------------------
                    //goScr.RunScript("Quotline_CalcTotal", doForm);      // Runs Quotline_CalcTotal
                    par_doCallingObject = doForm;
                    bool runnext = true;
                    scriptManager.RunScript("Quotline_CalcTotal", ref par_doCallingObject, ref par_oReturn, ref runnext, ref par_sSections );
                }
                // ------- Set Line number to highest used line + 1 ----------
                clRowSet doRS = null;
                lHighestLine = 0;
                // goP.TraceLine("Starting a rowset on all Quote's Quote Lines", "", sProc)


                // CS: The code below would get quote lines that existed with a null lnk_in_qt. There
                // are 8 in the db with no linked quote. I am changing this to only run if lnk_in_qt
                // is not blank.

                //if (doForm.doRS.GetFieldVal("LNK_In_QT", 1) != "")
                if (!string.IsNullOrEmpty(Convert.ToString(doForm.doRS.GetFieldVal("LNK_In_QT", 1))))
                {

                    // doRS = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("LNK_In_QT") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    // *** MI 11/14/07 Optimization
                    // doRS = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("LNK_In_QT") & "'", , "SR__LINENO", , , , , , , doForm.doRS.bBypassValidation)
                    // *** MI 11/21/07 Optimization: use a read-only rowset
                    doRS = new clRowSet("QL", 3, "LNK_IN_QT='" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_In_QT")) + "'", "", "SR__LINENO");
                    if (doRS.GetFirst() == 1)
                    {
                        do
                        {
                            // goP.TraceLine("Reading Quote Line '" & doRS.GetFieldVal("SR__LINENO", 2) & "'", "", sProc)
                            lLine = (doRS.GetFieldVal("SR__LINENO", 2) == null) ? -1 : Convert.ToInt64(doRS.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doRS.GetNext() == 0)
                                break;
                        }
                        while (true);
                    }
                    else
                    {
                    }
                    // delete(doRS)
                    doRS = null;
                }
                // goP.TraceLine("Highest line: '" & lHighestLine & "'", "", sProc)
                sWork = Convert.ToString(lHighestLine);
                sWork = goTR.ExtractString(sWork, 1, ".");
                if (sWork == clC.EOT.ToString())
                    sWork = "0";
                // goP.TraceLine("Integer part of highest line: '" & sWork & "'", "", sProc)
                lHighestLine = Convert.ToInt32(sWork) + 1;
                // goP.TraceLine("Highest line after '+1': '" & lHighestLine & "'", "", sProc)
                doForm.doRS.SetFieldVal("SR__LINENO", lHighestLine, 2);
            }
            else
                // goP.TraceLine("LNK_IN_QT link count: " & doForm.dors.GetLinkCount("LNK_IN_QT"), "", sProc)
                if (doForm.doRS.GetLinkCount("LNK_IN_QT") > 0)
                {
                    // CS 6/22/09: Create rowset for getting all Quote values instead of all double hops below (creating multiple rowsets)
                    clRowSet doRSQT = new clRowSet("QT", 3, "GID_ID='" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_IN_QT")) + "'", "", "DTE_TIME,TME_TIME,DTE_EXPCLOSEDATE,LNK_CREDITEDTO_US,LNK_PEER_US,LNK_RELATED_PR,LNK_TO_CO,MLS_REASONWONLOST");
                    if (doRSQT.GetFirst() == 1)
                    {
                        doForm.doRS.SetFieldVal("DTE_QTETIME", doRSQT.GetFieldVal("DTE_TIME"));
                        doForm.doRS.SetFieldVal("TME_QTETIME", doRSQT.GetFieldVal("TME_TIME"));

                        if (goTR.IsDate((doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1) == null) ? "" : doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1).ToString()) != true)
                        {
                            doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doRSQT.GetFieldVal("DTE_EXPCLOSEDATE"));
                        }
                        if (doForm.doRS.GetLinkCount("LNK_CREDITEDTO_US") < 1)
                        {
                            doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doRSQT.GetFieldVal("LNK_CREDITEDTO_US", 2), 2);
                        }
                        if (doForm.doRS.GetLinkCount("LNK_PEER_US") < 1)
                        {
                            doForm.doRS.SetFieldVal("LNK_PEER_US", doRSQT.GetFieldVal("LNK_PEER_US", 2), 2);
                        }
                        doForm.doRS.SetFieldVal("LNK_RELATED_PR", doRSQT.GetFieldVal("LNK_RELATED_PR", 2), 2);
                        doForm.doRS.SetFieldVal("LNK_TO_CO", doRSQT.GetFieldVal("LNK_TO_CO", 2), 2);
                    }
                    // CS 6/22/09---------
                    // doForm.doRS.SetFieldVal("DTE_QTETIME", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTE_TIME"))
                    // doForm.doRS.SetFieldVal("TME_QTETIME", doForm.doRS.GetFieldVal("LNK_IN_QT%%TME_TIME"))

                    // If goTR.IsDate(doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE", 1)) <> True Then
                    // doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTE_EXPCLOSEDATE"))
                    // End If
                    // If doForm.doRS.GetLinkCount("LNK_CREDITEDTO_US") < 1 Then
                    // doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2), 2)
                    // End If
                    // If doForm.doRS.GetLinkCount("LNK_PEER_US") < 1 Then
                    // doForm.doRS.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_PEER_US", 2), 2)
                    // End If
                    // doForm.doRS.SetFieldVal("LNK_RELATED_PR", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_RELATED_PR", 2), 2)
                    // doForm.doRS.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_TO_CO", 2), 2)
                    // CS -------------


                    // -------- Status, Reason Won/Lost -------
                    // ******************************
                    // In Selltis DB, Status and Reason Won/Lost are the same as in Quote
                    // goP.TraceLine("Setting MLS_STATUS from Quote: '" & doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 2) & "'", "", sProc)
                    // CS 7/3/07: Allow setting status of QL independent of QT
                    // doForm.doRS.SetFieldVal("MLS_STATUS", doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_STATUS", 1), 1)
                    // goP.TraceLine("Setting MLS_REASONWONLOST from Quote: '" & doForm.dors.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 2) + "'", "", sProc)
                    // doForm.doRS.SetFieldVal("MLS_REASONWONLOST", doForm.doRS.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST", 1), 1)
                    // 'In Selltis DB, Status and Reason Won/Lost are the same as in Quote
                    // If doForm.GetFieldVal("MLS_STATUS",2) <> doForm.GetFieldVal("LNK_IN_QT%%MLS_STATUS",2) Then
                    // doForm.SetFieldVal("MLS_STATUS",doForm.GetFieldVal("LNK_IN_QT%%MLS_STATUS",2),2)
                    // End If
                    // If doForm.GetFieldVal("MLS_REASONWONLOST",2) <> doForm.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST",2) Then
                    // doForm.SetFieldVal("MLS_REASONWONLOST",doForm.GetFieldVal("LNK_IN_QT%%MLS_REASONWONLOST",2),2)
                    // End If
                    // ******************************
                    // ----------------------------------------
                    // If quote set to set status of QL from QT, gray out the Status selection on the QL.
                    // QLEditStatus is set in the button click of the Edit button on the QT form.
                    // CS 6/12/08: 
                    // NOTE: There is an issue if you open a QT that previously had the 'Update 
                    // QL status' checkbox checked and now uncheck it and click the Edit button to edit the QL.
                    // In this case the code below still reports the Update QL Status as checked and therefore
                    // the status on the QL you are trying to edit is still grayed out.
                    // CS 6/20/08: 
                    // Could be directly opening a QL record which means we need to check
                    // the linked QT OR could be opening from the Edit button of the QT form
                    // or double clicking the line from the LInes linkbox on Quote
                    string sMOde = doForm.LinkboxAction;
                    if (sMOde == null)
                    {
                        sMOde = "";
                    }
                    // CS 6/20/08 If WOP to manage QLs is off, always gray out status field of QL
                    // During save of QT, the QL status will be updated to match the QT, even if it had 
                    // previously been on.
                    if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_STATUSMGMT")) != "1")
                    {
                        doForm.SetControlState("MLS_STATUS", 4);
                        doForm.SetControlState("MLS_REASONWONLOST", 4); // CS 5/28/10
                    }
                    else if (Strings.InStr(sMOde.ToUpper(), "CLICKTHRU_QT_LNK_CONNECTED_QL") == 0 & (Convert.ToString(doForm.oVar.GetVar("QLOpenFromEditButton"))) != "1")
                    {
                        // Try to check the linkboxaction to determine if QL form is being opened
                        // as a result of a linkbox click through
                        // If QL not opened as result of click thru in Lines linkbox or from clicking edit button need to get QT status
                        // from Quote link; otherwise need from QT form in history
                        if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_IN_QT%%CHK_UPDQLSTATUS")).ToUpper() == "CHECKED")
                        {
                            // If (UCase(doForm.doRS.GetFieldVal("LNK_IN_QT%%CHK_UPDQLSTATUS")) = "CHECKED" AND goP.GetVar(") Or goP.GetVar("QLEditStatus") <> "1" Then
                            doForm.SetControlState("MLS_STATUS", 4);
                            doForm.SetControlState("MLS_REASONWONLOST", 4); // CS 5/28/10
                        }
                        else
                        {
                            // status active
                            doForm.SetControlState("MLS_STATUS", 0);
                            doForm.SetControlState("MLS_REASONWONLOST", 0); // CS 5/28/10
                        }
                    }
                    else
                    {
                        //// New: Get QT form in history for this QL
                        //string sQuoteID = (doForm.doRS.GetFieldVal("LNK_IN_QT") == null) ? "" : doForm.doRS.GetFieldVal("LNK_IN_QT").ToString();
                        //int i;
                        //Form oForm = default(Form);
                        //string sHistory = "";
                        //clArray oHistory = goUI.GetUIObjectGUIDs;
                        //if (oHistory.GetDimension() > 0)
                        //{

                        //    // CS 9/9/08: Loop thru items in UI stack and find QT for this QL
                        //    for (i = 1; i <= oHistory.GetDimension(); i++)
                        //    {
                        //        sHistory = oHistory.GetItem(i);
                        //        // Check if a form, if so, check if is the linked QT for the QL
                        //        object oObject = goUI.GetUIObject(sHistory);
                        //        if (Strings.UCase(oObject.GetType().ToString()) == "CLFORM")
                        //        {
                        //            oForm = (Form)oObject;
                        //            if (oForm.GetRecordID() == sQuoteID)
                        //            {
                        //                if (Convert.ToString(oForm.doRS.GetFieldVal("CHK_UPDQLSTATUS")).ToUpper() == "CHECKED")
                        //                {
                        //                    doForm.SetControlState("MLS_STATUS", 4);
                        //                    doForm.SetControlState("MLS_REASONWONLOST", 4); // CS 5/28/10
                        //                }
                        //                else
                        //                {
                        //                    // status active
                        //                    doForm.SetControlState("MLS_STATUS", 0);
                        //                    doForm.SetControlState("MLS_REASONWONLOST", 0); // CS 5/28/10
                        //                }
                        //                break;
                        //            }
                        //        }
                        //        else
                        //        {
                        //        }
                        //    }
                        //}


                        // New: Get QT form in history for this QL
                        string sQuoteID = Convert.ToString(doForm.doRS.GetFieldVal("LNK_IN_QT"));
                        int i;
                        Form oForm = default(Form);
                        string sHistory = "";
                        //clArray oHistory = goUI.GetUIObjectGUIDs;
                        clArray oHistory = (clArray)doForm.doRS.GetFieldVal("LNK_IN_QT", 2);
                        if (oHistory.GetDimension() > 0)
                        {

                            // CS 9/9/08: Loop thru items in UI stack and find QT for this QL
                            for (i = 1; i <= oHistory.GetDimension(); i++)
                            {
                                sHistory = oHistory.GetItem(i);
                                // Check if a form, if so, check if is the linked QT for the QL
                                //object oObject = goUI.GetUIObject(sHistory);

                                string LastSelectedItemInHistory = "";
                                if (Util.GetSessionValue("SelectedHistoryItem") != null)
                                {
                                    LastSelectedItemInHistory = Util.GetSessionValue("SelectedHistoryItem").ToString();
                                }
                                string ObjectType = "";
                                List<HistoryItems> _desktopHistoryObjects = new List<HistoryItems>();
                                _desktopHistoryObjects = (List<HistoryItems>)Util.GetSessionValue("DesktopHistoryObjects");
                                ObjectType = _desktopHistoryObjects.Find(e => e.Key == LastSelectedItemInHistory).IsDesktop_or_Form.ToLower();

                                if (Strings.UCase(ObjectType) == "CLFORM")
                                {
                                    //oForm = (Form)oObject;
                                    if (_desktopHistoryObjects.Find(e => e.Key == LastSelectedItemInHistory).RecordId == sQuoteID)
                                    {
                                        if (Convert.ToString(oForm.doRS.GetFieldVal("CHK_UPDQLSTATUS")).ToUpper() == "CHECKED")
                                        {
                                            doForm.SetControlState("MLS_STATUS", 4);
                                            doForm.SetControlState("MLS_REASONWONLOST", 4); // CS 5/28/10
                                        }
                                        else
                                        {
                                            // status active
                                            doForm.SetControlState("MLS_STATUS", 0);
                                            doForm.SetControlState("MLS_REASONWONLOST", 0); // CS 5/28/10
                                        }
                                        break;
                                    }
                                }
                                else
                                {
                                }
                            }
                        }
                    }


                    // CS 8/27/08: Quote line users mgmt
                    if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_USERMGMT")) != "1")
                    {
                        doForm.SetControlState("LNK_CREDITEDTO_US", 4);
                        doForm.SetControlState("LNK_PEER_US", 4);
                    }
                    else
                    {
                        doForm.SetControlState("LNK_CREDITEDTO_US", 0);
                        doForm.SetControlState("LNK_PEER_US", 0);
                    }
                }

            // CS: think not needed---------- VARIABLES -----------
            // doForm.oVar.SetVar("sForSKUOrigVal", doForm.dors.GetFieldVal("LNK_FOR_MO", 2))
            // doForm.oVar.SetVar("srQtyEnterVal", doForm.dors.GetFieldVal("SR__QTY", 2))

            // ----------- STATES ----------
            // ******************************
            // In Selltis DB, Status and Reason Won/Lost are the same as in Quote
            // CS: Commenting b/c SetFieldProperty causes syntax error
            // doForm.SetFieldProperty("MLS_STATUS", "STATE", Grayed)
            // doForm.SetFieldProperty("MLS_REASONWONLOST", "STATE", Grayed)
            // ******************************


            goP.SetVar("QLEditStatus", "");

            par_doCallingObject = doForm;
            return true;
        }
        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = null;
            clRowSet doRS1 = null;
            // If gbWriteLog Then Dim oLog As Object = New clLogObj(sProc, "Start", 3)
            if (par_s1 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
            }
                

            // SKO 11052015 Ticket#786
            par_bRunNext = false;

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Begin-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)



            decimal cPriceUnit;
            double rQtyFld;
            double rDiscPerc;
            decimal cDiscAddAmt;
            decimal cCostVal;
            decimal cWork;
            decimal cSubtotal;
            double rSalesTaxPerc;
            decimal cSKUCost;
            string sSKUCost;
            // SKO 11052015 Ticket#786
            double rGrossProfitPerc;
            decimal cSellPrice;

            // PURPOSE:
            // Calc Subtotal if Include is checked, otherwise enter 0 as subtotal
            // Field 'Price Unit No Disc' = Unit Price before discount
            // Field 'Price Unit' = Unit Price after discount
            // RETURNS:
            // True.

            // goP.TraceLine("", "", sProc)

            // CS Need to check if coming from RecOnSave b/c in that case we are working with a rowset and 
            // otherwise we are on a form.
            if (par_s1 != "doRS")
            {
                if ((doForm.doRS.GetFieldVal("CHK_INCLUDE", 2) != null) && Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_INCLUDE", 2)) != 1)
                {
                    // Set calculated fields to 0
                    doForm.doRS.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_COST", 0, 2);
                    doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_SALESTAX", 0, 2);
                }
                else
                {
                    cPriceUnit = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_PRICEUNIT", 2));
                    rQtyFld = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY", 2));
                    rDiscPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__DISCPERCENT", 2));
                    cDiscAddAmt = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_DISCADDLAMT", 2));
                    // CS 5/21/09: Always calculate total cost based on linked model's cost * qty
                    // cCostVal = doForm.doRS.GetFieldVal("CUR_COST", 2)
                    // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")
                    // If sSKUCost <> "" Then
                    // cSKUCost = goTR.StringToCurr(sSKUCost)
                    // Else
                    // cSKUCost = 0
                    // End If
                    // cCostVal = cSKUCost * rQtyFld
                    // doForm.doRS.SetFieldVal("CUR_COST", cCostVal)


                    // Copy Cost from SKU and multiply it by Qty if the user edited Qty or if Qty is 0
                    // (we set it to 0 above if it is blank)
                    // bUpdCostVal is set to 1 in ControlOnLeave script for 'For Model'.

                    // Check if form variable has been set otherwise get an error comparing srQtyEnterVal as 
                    // a string ("") to a double (rQtyFld)
                    // Per MI, we can get rid of the form var dealing with changing the qty. We will always
                    // recalc regardless of it.
                    // If doForm.oVar.GetVar("srQtyEnterVal") <> "" Then
                    // If (rQtyFld <> doForm.oVar.GetVar("srQtyEnterVal")) Or (doForm.oVar.GetVar("bUpdCostVal")) = 1 Then
                    // If doForm.oVar.Getvar("bUpdCostVal") <> "" Then
                    // If (doForm.oVar.GetVar("bUpdCostVal")) = 1 Then
                    // CS: Moving this to QL_FormControlOnChange_USEMODELPRICE. When the user clicks this button the linked model's cost
                    // will be used.
                    // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")
                    // If sSKUCost <> "" Then 'CS added this If b/c if coming from QL_FormOnLoad the value
                    // 'is blank for cost and get error
                    // cSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")

                    // cCostVal = cSKUCost * rQtyFld
                    // doForm.doRS.SetFieldVal("CUR_COST", cCostVal)
                    // End If
                    // End If

                    // Calculate unit price after discount
                    cWork = cPriceUnit - Math.Round((cPriceUnit * Convert.ToDecimal(rDiscPerc) / 100), 2);
                    doForm.doRS.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork));

                    // Calculate total cost
                    // SKO 11052015 Ticket#786 total cost(CUR_COST) =  unit cost(CUR_PRICEUNITAFTERDISC) x qty(SR__QTY)
                    cCostVal = cWork * Convert.ToDecimal(rQtyFld);
                    doForm.doRS.SetFieldVal("CUR_COST", cCostVal);

                    // SKO 11052015 Ticket#786 Sell Price=Unit Cost / (1-GP%)  CUR_SELLPRICE = CUR_PRICEUNITAFTERDISC / (1-SR__GROSSPROFIT%)
                    // Calculate Sell Price
                    rGrossProfitPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__GROSSPROFIT", 2));
                    // cSellPrice = cWork - (cWork * rGrossProfitPerc / 100)
                    cSellPrice = Math.Round(((cWork / (1 - (Convert.ToDecimal(rGrossProfitPerc) / 100))) + cDiscAddAmt), 2);
                    doForm.doRS.SetFieldVal("CUR_SELLPRICE", goTR.RoundCurr(cSellPrice));

                    // Calculate Subtotal
                    // SKO 11052015 Ticket#786 Subtotal(CUR_SUBTOTAL) =  Sell Price (CUR_SELLPRICE) x Qty(SR__QTY)
                    // cSubtotal = (cPriceUnit * rQtyFld) - (cPriceUnit * rQtyFld * rDiscPerc / 100) + cDiscAddAmt
                    cSubtotal = Math.Round(cSellPrice * Convert.ToDecimal(rQtyFld), 2);
                    doForm.doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cSubtotal));

                    // Calc Gross Profit
                    // CS 5/13/09 Remove qty b/c cur_cost already considers qty
                    // cWork = cSubtotal - (cCostVal * rQtyFld) 'CS 6/13/07: Added rQtyField per DF
                    cWork = cSubtotal - cCostVal;
                    doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));

                    // Sales tax
                    if ((doForm.doRS.GetFieldVal("CHK_TAXABLE", 2) == null) ? false : Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_TAXABLE", 2)))
                    {
                        // CS 6/2/09: Get value from variable if set
                        if (string.IsNullOrEmpty(Convert.ToString(goP.GetVar("QuoteInfo"))))
                        {
                            rSalesTaxPerc = (doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT") == null) ? 0 : Convert.ToDouble(doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"));
                        }
                        else
                        {
                            rSalesTaxPerc = goTR.StringToNum(goTR.StrRead(Convert.ToString(goP.GetVar("QuoteInfo")), "QT_SALESTAXPERCENT", "", false), "", ref par_iValid);
                        }


                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", rSalesTaxPerc);
                        doForm.doRS.SetFieldVal("CUR_SALESTAX", cSubtotal * Convert.ToDecimal(rSalesTaxPerc) / 100);
                    }
                    else
                    {
                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 0);
                        doForm.doRS.SetFieldVal("CUR_SALESTAX", 0);
                    }
                }
            }
            else if ((doRS1.GetFieldVal("CHK_INCLUDE", 2) != null) && Convert.ToInt32(doRS1.GetFieldVal("CHK_INCLUDE", 2)) != 1)
            {
                // Set calculated fields to 0
                doRS1.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                doRS1.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                doRS1.SetFieldVal("CUR_COST", 0, 2);
                doRS1.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                doRS1.SetFieldVal("CUR_SALESTAX", 0, 2);
            }
            else
            {
                cPriceUnit = Convert.ToDecimal(doRS1.GetFieldVal("CUR_PRICEUNIT", 2));
                rQtyFld = Convert.ToDouble(doRS1.GetFieldVal("SR__QTY", 2));
                rDiscPerc = Convert.ToDouble(doRS1.GetFieldVal("SR__DISCPERCENT", 2));
                cDiscAddAmt = Convert.ToDecimal(doRS1.GetFieldVal("CUR_DISCADDLAMT", 2));
                // cCostVal = doRS1.GetFieldVal("CUR_COST", 2)
                // CS 5/21/09: Always calculate total cost based on linked model's cost * qty
                // Dim sModel As String = goP.GetVar("QuoteLineInfo_" & doRS1.getfieldval("GID_ID"))
                // If sModel = "" Then
                // sSKUCost = doRS1.GetFieldVal("LNK_FOR_MO%%CUR_COST")
                // Else
                // sSKUCost = goTR.StrRead(sModel, "MO_CUR_COST", , False)
                // End If
                // If sSKUCost <> "" Then
                // cSKUCost = goTR.StringToCurr(sSKUCost)
                // Else
                // cSKUCost = 0
                // End If
                // cCostVal = cSKUCost * rQtyFld
                // doRS1.SetFieldVal("CUR_COST", cCostVal)

                // 'CS: only set the cost if it is 0
                // If cCostVal = 0 Then
                // cSKUCost = goTR.StringToCurr(doRS1.GetFieldVal("LNK_FOR_MO%%CUR_COST"))
                // cCostVal = cSKUCost * rQtyFld
                // doRS1.SetFieldVal("CUR_COST", goTR.RoundCurr(cCostVal))
                // End If

                // Calculate unit price after discount
                cWork = cPriceUnit - Math.Round((cPriceUnit * Convert.ToDecimal(rDiscPerc) / 100), 2);
                doRS1.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork));

                // Calculate total cost
                // SKO 11052015 Ticket#786 total cost(CUR_COST) =  unit cost(CUR_PRICEUNITAFTERDISC) x qty(SR__QTY)
                cCostVal = cWork * Convert.ToDecimal(rQtyFld);
                doRS1.SetFieldVal("CUR_COST", cCostVal);

                // SKO 11052015 Ticket#786 Sell Price=Unit Cost / (1-GP%)  CUR_SELLPRICE = CUR_PRICEUNITAFTERDISC / (1-SR__GROSSPROFIT%)
                // Calculate Sell Price
                rGrossProfitPerc = Convert.ToDouble(doRS1.GetFieldVal("SR__GROSSPROFIT", 2));
                // cSellPrice = cWork - (cWork * rGrossProfitPerc / 100)
                cSellPrice = Math.Round(((cWork / (1 - (Convert.ToDecimal(rGrossProfitPerc) / 100))) + cDiscAddAmt), 2);
                doRS1.SetFieldVal("CUR_SELLPRICE", goTR.RoundCurr(cSellPrice));

                // Calculate Subtotal
                // SKO 11052015 Ticket#786 Subtotal(CUR_SUBTOTAL) =  Sell Price (CUR_SELLPRICE) x Qty(SR__QTY)
                // cSubtotal = (cPriceUnit * rQtyFld) - (cPriceUnit * rQtyFld * rDiscPerc / 100) + cDiscAddAmt
                cSubtotal = Math.Round(cSellPrice * Convert.ToDecimal(rQtyFld), 2);
                doRS1.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cSubtotal));

                // Calc Gross Profit
                cWork = cSubtotal - cCostVal;
                // CS 5/13/09 Remove qty b/c cur_cost already considers qty
                // cWork = cSubtotal - (cCostVal * rQtyFld) 'CS Added rQtyField 
                doRS1.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));

                // Sales tax
                if ((doRS1.GetFieldVal("CHK_TAXABLE", 2) == null) ? false : Convert.ToBoolean(doRS1.GetFieldVal("CHK_TAXABLE", 2)))
                {
                    // CS 6/2/09:
                    if (string.IsNullOrEmpty(Convert.ToString(goP.GetVar("QuoteInfo"))))
                    {
                        // CS 10/19/10: Check if have a linked QT. Should always, but causes error if not.
                        if (doRS1.GetLinkCount("LNK_IN_QT") > 0)
                        {
                            rSalesTaxPerc = (doRS1.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT") == null) ? 0 : Convert.ToDouble(doRS1.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"));
                        }
                        else
                        {
                            rSalesTaxPerc = 0;
                        }
                    }
                    else
                    {
                        rSalesTaxPerc = goTR.StringToNum(goTR.StrRead(Convert.ToString(goP.GetVar("QuoteInfo")), "QT_SALESTAXPERCENT", "", false), "", ref par_iValid);
                    }

                    doRS1.SetFieldVal("SR__SALESTAXPERCENT", rSalesTaxPerc);
                    doRS1.SetFieldVal("CUR_SALESTAX", cSubtotal * Convert.ToDecimal(rSalesTaxPerc) / 100);
                }
                else
                {
                    doRS1.SetFieldVal("SR__SALESTAXPERCENT", 0);
                    doRS1.SetFieldVal("CUR_SALESTAX", 0);
                }
                par_doCallingObject = doRS1;
            }
            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("End-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)

            //par_doCallingObject = doForm;

            if (par_s1 == "doRS")
            {
                par_doCallingObject = doRS1;
            }
            else
            {
                par_doCallingObject = doForm;
            }

            return true;
        }
        public bool Quotline_FillItem_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // goP.TraceLine("", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            // SKO 11092015 Ticket#788
            par_bRunNext = false;

            // PURPOSE:
            // Fill the TXT_MODEL field
            // RETURNS:
            // True.

            // CS: Original code 10/10/07
            // If Trim(doForm.dors.GetFieldVal("TXT_MOdel")) = "" Then
            // If doForm.dors.GetLinkCount("LNK_FOR_MO") > 0 Then
            // 'PJ 10/12/01 Remmed end of line per BKG request
            // 'MI 4/2/08: MMO_Description is replaced with TXT_Description as of 4/3/08.
            // 'If you reenable this code, you MUST test:
            // If goData.IsFieldValid("MO", "TXT_Description") Then
            // '-> Use TXT_Description field's value 
            // Else
            // '-> Use MMO_Description
            // End If
            // doForm.dors.SetFieldVal("TXT_MODEL", doForm.dors.GetFieldVal("LNK_FOR_MO%%MMO_DESCRIPTION")) '& " - " & doForm.GetFieldVal("LNK_FOR_MO%%TXT_MODELNAME")
            // End If
            // End If

            string sSKUPrice;
            decimal cSKUPrice;
            string sSKUCost;
            decimal cSKUCost;
            decimal rQtyFld;
            decimal cCostVal;
            string sModel;
            string sLinkedModel = "";
            string sUnit;
            clRowSet doRSModel;
            double rCostDiscPerc;
            double rGrossProfitPerc;


            // If no model selected, return
            if (doForm.doRS.GetLinkCount("LNK_FOR_MO") == 0)
            {
                par_doCallingObject = doForm;
                return true;
            }

            if (goData.IsFieldValid("MO", "TXT_Description"))
            {
                // Cs 6/22/09
                doRSModel = new clRowSet("MO", 3, "GID_ID='" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO")) + "'", "", "TXT_Description,txt_unittext,lnk_of_pd,cur_price,cur_cost,SR__DISCFROMRETAIL,SR__MARGIN");
            }
            else
                doRSModel = new clRowSet("MO", 3, "GID_ID='" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO")) + "'", "", "MMO_Description,txt_unittext,lnk_of_pd,cur_price,cur_cost,SR__DISCFROMRETAIL,SR__MARGIN");

            if (doRSModel.GetFirst() != 1)
            {
                par_doCallingObject = doForm;
                return true;
            }

            // Set Unit price to linked model's price
            // sSKUPrice = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_PRICE", 1) '6/22/09
            sSKUPrice = Convert.ToString(doRSModel.GetFieldVal("CUR_PRICE", 2));
            // If sSKUPrice <> "" Then
            cSKUPrice = Convert.ToDecimal(sSKUPrice);
            // End If

            doForm.doRS.SetFieldVal("CUR_PRICEUNIT", cSKUPrice, 2);

            // SKO 11092015 Ticket#788
            rCostDiscPerc = Convert.ToDouble(doRSModel.GetFieldVal("SR__DISCFROMRETAIL", 2));
            // If rCostDiscPerc <> 0 Then
            doForm.doRS.SetFieldVal("SR__DISCPERCENT", rCostDiscPerc);
            // End If

            // SKO 11092015 Ticket#788
            rGrossProfitPerc = Convert.ToDouble(doRSModel.GetFieldVal("SR__MARGIN", 2));
            // If rGrossProfitPerc <> 0 Then
            doForm.doRS.SetFieldVal("SR__GROSSPROFIT", rGrossProfitPerc);
            // End If

            // Set Cost to linked model's cost
            // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST", 1) 'Cs 6/22/09
            sSKUCost = Convert.ToString(doRSModel.GetFieldVal("CUR_COST", 1));
            // If sSKUCost <> "" Then
            // cSKUCost = sSKUCost
            // rQtyFld = doForm.doRS.GetFieldVal("SR__QTY")
            // cCostVal = cSKUCost * rQtyFld
            // doForm.doRS.SetFieldVal("CUR_COST", goTR.RoundCurr(cCostVal))
            // End If

            // If txt_model is blank set to linked model's description
            sModel = Convert.ToString(doForm.doRS.GetFieldVal("TXT_Model"));
            if (sModel == "")
            {
                // MI 4/2/08 MO.TXT_Description replaces MMO_Description as of 4/3/08, but MMO_Description remains in existing DBs.
                if (goData.IsFieldValid("MO", "TXT_Description"))
                {
                    // Cs 6/22/09                
                    sLinkedModel = Convert.ToString(doRSModel.GetFieldVal("TXT_Description", 1));
                }
                else
                {
                    // sLinkedModel = doForm.doRS.GetFieldVal("LNK_FOR_MO%%MMO_DESCRIPTION", 1)
                    sLinkedModel = Convert.ToString(doRSModel.GetFieldVal("MMO_DESCRIPTION", 1));
                }
                doForm.doRS.SetFieldVal("TXT_Model", sLinkedModel);
            }

            // Fill unit with linked model's unit
            // sUnit = doForm.doRS.GetFieldVal("LNK_FOR_MO%%txt_unittext") '6/22/09
            sUnit = Convert.ToString(doRSModel.GetFieldVal("txt_unittext"));
            doForm.doRS.SetFieldVal("TXT_Unit", sUnit);

            par_doCallingObject = doForm;
            bool runnext = true;
            //goScr.RunScript("Quotline_CheckTaxable", doForm);
            scriptManager.RunScript("Quotline_CheckTaxable", ref par_doCallingObject, ref par_oReturn, ref runnext, ref par_sSections);
            doForm = (Form)par_doCallingObject; par_doCallingObject = doForm.doRS;
            runnext = true;
            //goScr.RunScript("Quotline_ConnectVendors", doForm.doRS);
            scriptManager.RunScript("Quotline_ConnectVendors", ref par_doCallingObject, ref par_oReturn, ref runnext, ref par_sSections);
            doForm.doRS = (clRowSet)par_doCallingObject; par_doCallingObject = doForm;
            runnext = true;
            //goScr.RunScript("Quotline_CalcTotal", doForm);        // runs CalcTotal
            scriptManager.RunScript("Quotline_CalcTotal", ref par_doCallingObject, ref par_oReturn, ref runnext, ref par_sSections);
            doForm = (Form)par_doCallingObject;
            // goScr.RunScript("Quotline_FillItem", doForm)


            // Set Product link to Model's Product
            doForm.doRS.ClearLinkAll("LNK_FOR_PD");
            // doForm.doRS.SetFieldVal("LNK_FOR_PD", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 2), 2) 'cs 6/22/09
            doForm.doRS.SetFieldVal("LNK_FOR_PD", doRSModel.GetFieldVal("LNK_OF_PD", 2), 2);

            par_doCallingObject = doForm;
            return true;
        }
        public bool AutoQuoteDuplicate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // MI 4/25/07 CREATED BY MI 4/22/07
            // PURPOSE:
            // Duplicate an existing Quote and its line items allowing the user to connect a different
            // Contact, Company, etc.

            string sID;
            clRowSet doRowset;
            string sFileName;
            Form doF = default(Form);
            string sOrigQuoteName;
            string sOrigQuoteID;

            // SKO 11182015 Ticket#800
            par_bRunNext = false;

            // Check selected record
            sID = goUI.GetLastSelected("SELECTEDRECORDID");
            // goP.TraceLine("sID: " & sID & "'", "", sProc)
            sFileName = (goTR.GetFileFromSUID(sID).ToString().ToUpper());
            if (sFileName != "QT")
            {
                //goUI.NewWorkareaMessage("Please select a Quote first.");
                goUI.NewWorkareaMessage("Please select a Quote first.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            // Check if have permissions
            if (goData.GetAddPermission("QT") == false)
            {
                //goUI.NewWorkareaMessage("You cannot duplicate the selected Quote because you don't have permissions to create Quotes.");
                goUI.NewWorkareaMessage("You cannot duplicate the selected Quote because you don't have permissions to create Quotes.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }


            // Copy the selected record
            // Get doRowset of current record
            doRowset = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + sID + "'", "", "*,LNK_CONNECTED_QL%%SYS_Name", 1);
            if (doRowset.Count() < 1)
            {
                goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user. Select a different record and start again.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }
            else
            {
                sOrigQuoteName = Convert.ToString(doRowset.GetFieldVal("SYS_Name"));
                sOrigQuoteID = Convert.ToString(doRowset.GetFieldVal("GID_ID"));
            }

            // Create the new Quote form
            doF = new Form(sFileName, "", "CRU_" + sFileName);
            doF.oVar.SetVar("QuoteOpeningMode", "Duplicate");
            doF.oVar.SetVar("QuoteOrinalQuoteID", sID);
            string LNK_Connected_QL = Convert.ToString(doRowset.GetFieldVal("LNK_Connected_QL%%SYS_Name"));
            doF.SetControlVal("NDB_MMO_Lines", LNK_Connected_QL);
            doF.doRS.SetFieldVal("TXT_Description", doRowset.GetFieldVal("TXT_Description", 2), 2);
            doF.doRS.SetFieldVal("CUR_Subtotal", doRowset.GetFieldVal("CUR_Subtotal", 2), 2);
            doF.doRS.SetFieldVal("CUR_Total", doRowset.GetFieldVal("CUR_Total", 2), 2);
            // Set History tab & Cloned from Quote
            doF.doRS.SetFieldVal("MMO_History", "Duplicated from Quote" + sOrigQuoteName);
            doF.doRS.SetLinkVal("LNK_ClonedFrom_QT", sOrigQuoteID);
            // VS 12152015 TKT#846 : New fields 
            doF.doRS.SetFieldVal("LNK_RELATED_PR", doRowset.GetFieldVal("LNK_RELATED_PR", 2), 2);
            // Cover Tab
            doF.doRS.SetFieldVal("LNK_TEMPLATE_DO", doRowset.GetFieldVal("LNK_TEMPLATE_DO", 2), 2);
            doF.oVar.SetVar("QuoteOrinalCOVERLETTER", doRowset.GetFieldVal("MMO_COVERLETTER", 2));
            // doF.doRS.SetFieldVal("MMO_COVERLETTER", doRowset.GetFieldVal("MMO_COVERLETTER", 2), 2)
            doF.doRS.SetFieldVal("TXT_ABOVESIGNATURE", doRowset.GetFieldVal("TXT_ABOVESIGNATURE", 2), 2);
            doF.doRS.SetFieldVal("TXT_SIGNATURE", doRowset.GetFieldVal("TXT_SIGNATURE", 2), 2);
            doF.doRS.SetFieldVal("CHK_USESIGNATURE", doRowset.GetFieldVal("CHK_USESIGNATURE", 2), 2);
            doF.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doRowset.GetFieldVal("MMO_UNDERSIGNATURE", 2), 2);
            // Closing
            doF.oVar.SetVar("QuoteOrinalCLOSING", doRowset.GetFieldVal("MMO_CLOSING", 2));
            // doF.doRS.SetFieldVal("MMO_CLOSING", doRowset.GetFieldVal("MMO_CLOSING", 2), 2)

            // SKO 11182015 Ticket#800 When creating a cloned qt GP%, ship date, shipping terms fields should be copied over 
            // doF.doRS.SetFieldVal("TXT_PROPOSEDSHIPPING", doRowset.GetFieldVal("TXT_PROPOSEDSHIPPING", 2), 2)
            // doF.doRS.SetFieldVal("TXT_SHIPPINGTERMS", doRowset.GetFieldVal("TXT_SHIPPINGTERMS", 2), 2)
            doF.oVar.SetVar("QuoteOrinalPROPOSEDSHIPPING", doRowset.GetFieldVal("TXT_PROPOSEDSHIPPING", 2));
            doF.oVar.SetVar("QuoteOrinalSHIPPINGTERMS", doRowset.GetFieldVal("TXT_SHIPPINGTERMS", 2));

            doF.MessagePanel("This is a duplicate of the Quote '" + sOrigQuoteName + "'." + Constants.vbCrLf + "Fill out the form and click Save or click Modify Lines to add, edit or remove them.", "#FFFFBO", "#000000", "Info.gif");

            // 'Copy to the new rowset
            // If Not goData.CopyRecord(doRowset, doF.doRS) Then
            // goErr.SetError(35000, sProc, "Copying the selected Quote '" & sID & "' failed.")
            // Return False
            // End If
            // doF.doRS.SetFieldVal("GID_ID", goData.GenerateID(sFileName))
            // doF.doRS.ClearLinkAll("LNK_OriginatedBy_CN")
            // doF.doRS.ClearLinkAll("LNK_To_CO")

            // 'Save the new record (?)
            // If doNewRowset.Commit() = 0 Then
            // goErr.SetWarning(30200, sProc, "", "An error occurred duplicating the selected Quote.", "", "", "", "", "", "", "", "", "")
            // doRowset = Nothing
            // doNewRowset = Nothing
            // Return False
            // End If

            goUI.Queue("FORM", doF);

            // Clean up objects
            doRowset = null/* TODO Change to default(_) if this is not a reference type */;

            return true;
        }
        public bool AutoQuoteCreateRevision_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 1/24/08 Changed from 1 to 0 to make the first rev to be Rev 1 not 2.
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sID;
            clRowSet doRowset;
            string sFileName;
            Form doF = default(Form);
            clRowSet doAllQTs;
            string sQuoteNo;
            string sQuoteOnlyNo;      // Quote number without the revision
            int iRevNo;
            int iRevNoTemp;
            int iLen;
            int i;
            int iPos;
            string sQuoteTitleOnly;

            // SKO 12302015 Ticket#870
            par_bRunNext = false;

            // Check selected record
            sID = goUI.GetLastSelected("SELECTEDRECORDID");
            // goP.TraceLine("sID: " & sID & "'", "", sProc)
            sFileName = (goTR.GetFileFromSUID(sID).ToString().ToUpper());
            if (sFileName != "QT")
            {
                goUI.NewWorkareaMessage("Please select a Quote first.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            // Check if have permissions
            if (goData.GetAddPermission("QT") == false)
            {
                goUI.NewWorkareaMessage("You cannot create a revision of the selected Quote because you don't have permissions to create Quotes.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            // Copy the selected record
            // Get doRowset of current record
            doRowset = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user. Select a different record and start again.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            // ------------------- Update revision no in Quote No -------------------
            //sQuoteNo = Trim(doRowset.GetFieldVal("TXT_QuoteNo"));
            sQuoteNo = Convert.ToString(doRowset.GetFieldVal("TXT_QuoteNo")).Trim();
            if (sQuoteNo == "")
            {
                //if (!goScr.RunScript("Quote_GenerateQuoteNo", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, sQuoteNo))
                if (!scriptManager.RunScript("Quote_GenerateQuoteNo", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", sQuoteNo))
                    return false;
            }
            if (sQuoteNo == "")
            {
                // Should never happen
                goErr.SetError(35000, sProc, "Quote number is blank.");
                return false;
            }

            sQuoteOnlyNo = sQuoteNo;
            // Get the quote no without the revision number
            iPos = Strings.InStr(sQuoteOnlyNo, "-");
            if (iPos > 0)
                sQuoteOnlyNo = Strings.Left(sQuoteOnlyNo, iPos - 1);
            // Limit the length of the quote no just in case someone mangled it by hand
            iLen = Strings.Len(sQuoteOnlyNo);
            if (iLen > 14)
            {
                iLen = 14;
                sQuoteOnlyNo = Strings.Left(sQuoteOnlyNo, 14);
            }

            // Determine the highest revision no
            doAllQTs = new clRowSet(sFileName, clC.SELL_READONLY, "TXT_QuoteNo[='" + ((goTR.PrepareForSQL(sQuoteOnlyNo) == null) ? "" : goTR.PrepareForSQL(sQuoteOnlyNo).ToString()) + "'", "DTT_Time DESC", "TXT_QuoteNo");    // MI 11/6/08 Added goTr.PrepareForSQL
            iRevNo = 0;  // *** MI 1/24/08 Changed from 1 to 0 to make the first rev to be Rev 1 not 2.
            for (i = 1; i <= doAllQTs.Count(); i++)
            {
                sQuoteNo = Convert.ToString(doAllQTs.GetFieldVal("TXT_QuoteNO"));
                iPos = Strings.InStr(sQuoteNo, "-");
                if (iPos > 0)
                {
                    // The number contains a revision no
                    iRevNoTemp = Convert.ToInt32(goTR.StringToNum(Strings.Right(sQuoteNo, 3), "", ref par_iValid));
                    if (iRevNoTemp > iRevNo)
                        iRevNo = iRevNoTemp;
                }
                if (doAllQTs.GetNext() != 1)
                    break;
            }

            // Advance the revision number by 1, but not if revision is 1.
            iRevNo = iRevNo + 1;
            if (iRevNo > 999)
                iRevNo = 999;
            // Remove all spaces - there can be a space after the user code if shorter than 4 chars
            sQuoteNo = goTR.Replace(sQuoteOnlyNo, " ", "_").ToString();
            // 'Ensure fixed length - commented because it makes numbers ugly with "_"
            // sQuoteNo = goTR.Pad(sQuoteNo, 14, "_")
            sQuoteNo = sQuoteOnlyNo + "-" + goTR.Pad(iRevNo.ToString(), 3, "0", "L");
            if (doAllQTs != null)
                doAllQTs = null/* TODO Change to default(_) if this is not a reference type */;

            // Create the new form
            doF = new Form(sFileName, "", "CRU_" + sFileName);

            // Copy this quote to the new form's rowset            
            clRowSet Temp_RS = doF.doRS;
            if (!goData.CopyRecord(ref doRowset, ref Temp_RS))
            {
                goErr.SetError(35000, sProc, "Copying the selected Quote '" + sID + "' failed.");
                return false;
            }

            // doF.doRS.SetFieldVal("GID_ID", goData.GenerateID(sFileName))
            doF.doRS.SetFieldVal("LNK_CreatedBy_US", goP.GetUserTID());
            doF.doRS.SetFieldVal("DTT_Time", "Today|Now");
            // CS 8/3/07: Reason and Status of revised QT should be same as original QT
            // doF.doRS.SetFieldVal("MLS_Status", 0, clC.SELL_SYSTEM)    'Open
            // doF.doRS.SetFieldVal("MLS_ReasonWonLost", 0, clC.SELL_SYSTEM)    '<Make selection>
            doF.doRS.SetFieldVal("DTT_DateClosed", "");
            // Set History tab & Cloned From Qt
            doF.doRS.SetFieldVal("MMO_History", "Revision of Quote" + doRowset.GetFieldVal("SYS_Name"));
            doF.doRS.SetLinkVal("LNK_ClonedFrom_QT", sID);
            sQuoteTitleOnly = Convert.ToString(doRowset.GetFieldVal("TXT_QuoteTitle"));
            iPos = Strings.InStr(sQuoteTitleOnly, " REV ");
            if (iPos > 0)
                sQuoteTitleOnly = Strings.Left(sQuoteTitleOnly, iPos - 1);
            doF.doRS.SetFieldVal("TXT_QuoteTitle", sQuoteTitleOnly + " REV " + iRevNo.ToString());
            doF.doRS.SetFieldVal("TXT_QuoteNo", sQuoteNo);
            doF.doRS.ClearLinkAll("LNK_Connected_QL");
            string LNK_Connected_QL = Convert.ToString(doRowset.GetFieldVal("LNK_Connected_QL%%SYS_Name"));
            doF.SetControlVal("NDB_MMO_Lines", LNK_Connected_QL);      // Lines will display as a memo
            doF.oVar.SetVar("QuoteOpeningMode", "Revision");
            doF.oVar.SetVar("QuoteOrinalQuoteID", doRowset.GetFieldVal("GID_ID"));

            // SKO 12302015 Ticket#870 : New fields 
            doF.doRS.SetFieldVal("LNK_RELATED_PR", doRowset.GetFieldVal("LNK_RELATED_PR", 2), 2);
            // Cover Tab
            doF.doRS.SetFieldVal("LNK_TEMPLATE_DO", doRowset.GetFieldVal("LNK_TEMPLATE_DO", 2), 2);
            doF.oVar.SetVar("QuoteOrinalCOVERLETTER", doRowset.GetFieldVal("MMO_COVERLETTER", 2));
            // doF.doRS.SetFieldVal("MMO_COVERLETTER", doRowset.GetFieldVal("MMO_COVERLETTER", 2), 2)
            doF.doRS.SetFieldVal("TXT_ABOVESIGNATURE", doRowset.GetFieldVal("TXT_ABOVESIGNATURE", 2), 2);
            doF.doRS.SetFieldVal("TXT_SIGNATURE", doRowset.GetFieldVal("TXT_SIGNATURE", 2), 2);
            doF.doRS.SetFieldVal("CHK_USESIGNATURE", doRowset.GetFieldVal("CHK_USESIGNATURE", 2), 2);
            doF.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doRowset.GetFieldVal("MMO_UNDERSIGNATURE", 2), 2);
            // Closing
            doF.oVar.SetVar("QuoteOrinalCLOSING", doRowset.GetFieldVal("MMO_CLOSING", 2));
            // doF.doRS.SetFieldVal("MMO_CLOSING", doRowset.GetFieldVal("MMO_CLOSING", 2), 2)

            // SKO 12302015 Ticket#870 : When creating a cloned qt GP%, ship date, shipping terms fields should be copied over 
            doF.oVar.SetVar("QuoteOrinalPROPOSEDSHIPPING", doRowset.GetFieldVal("TXT_PROPOSEDSHIPPING", 2));
            doF.oVar.SetVar("QuoteOrinalSHIPPINGTERMS", doRowset.GetFieldVal("TXT_SHIPPINGTERMS", 2));

            doF.MessagePanel("This is a revision of the Quote '" + Convert.ToString(doRowset.GetFieldVal("SYS_Name")) + "'." + Constants.vbCrLf + "Check the Title and Quote Number and click Save or click Modify Lines to add, edit or remove them.", "#FFFFBO", "#000000", "Info.gif");

            goUI.Queue("FORM", doF);

            // Clean up objects
            doRowset = null/* TODO Change to default(_) if this is not a reference type */;

            return true;
        }
        public bool QL_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.GetMode() == "CREATION")
            {
                string QuoteOpeningMode = Convert.ToString(doForm.oVar.GetVar("QuoteOpeningMode"));
                switch (QuoteOpeningMode)
                {
                    case "Duplicate":
                    case "Revision":
                        {
                            // SKO 11182015 Ticket#800 When creating a cloned qt GP%, ship date, shipping terms fields should be copied over 
                            doForm.doRS.SetFieldVal("TXT_PROPOSEDSHIPPING", doForm.oVar.GetVar("QuoteOrinalPROPOSEDSHIPPING"), 2);
                            doForm.doRS.SetFieldVal("TXT_SHIPPINGTERMS", doForm.oVar.GetVar("QuoteOrinalSHIPPINGTERMS"), 2);
                            // VS 12152015 TKT#846 : New fields 
                            doForm.doRS.SetFieldVal("MMO_COVERLETTER", doForm.oVar.GetVar("QuoteOrinalCOVERLETTER"), 2);
                            doForm.doRS.SetFieldVal("MMO_CLOSING", doForm.oVar.GetVar("QuoteOrinalCLOSING"), 2);
                            break;
                        }
                }
            }

            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }

            return true;

        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool QT_FormAfterSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // MI 4/26/07

            Form doForm = (Form)par_doCallingObject;

            // SKO 11182015 Ticket#800
            par_bRunNext = false;

            string sID;
            clRowSet doRS;
            // Dim doOrigQL As clRowSet
            clRowSet doNewQL;
            clRowSet doOrigQuote;
            int i;
            bool bQLNotFound = false;
            string sQuoteOpeningMode;
            string sMessage;

            if (doForm.GetMode() == "CREATION")
            {
                if (Convert.ToString(doForm.oVar.GetVar("QT_AddQuoteLine")) == "1")
                {
                    // Redisplay the same form so the user thinks the form never went away
                    Form doFormSame = new Form("QT", Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), "");
                    //goUI.Queue("FORM", doFormSame); //Commenting this line 22022019 tickt #2678:While adding ql once you click ADD Quote line Button >New QL form should appear but here we have to click "ADD" again (Global change,It is happening on ati70.selltis.com)..J
                }
                sQuoteOpeningMode = Convert.ToString(doForm.oVar.GetVar("QuoteOpeningMode"));
                switch (sQuoteOpeningMode)
                {
                    case "Duplicate":
                    case "Revision":
                        {
                            // Create Quote Lines by copying the original ones
                            sID = Convert.ToString(doForm.oVar.GetVar("QuoteOrinalQuoteID"));
                            doRS = new clRowSet("QL", clC.SELL_EDIT, "LNK_In_QT='" + sID + "'", "", "*");
                            // For each quote line found create a new one, linked to the new Quote
                            goP.SetVar("bDoNotUpdateQuote", "1");
                            for (i = 1; i <= doRS.Count(); i++)
                            {
                                switch (sQuoteOpeningMode)
                                {
                                    case "Duplicate":
                                        {
                                            // ---- Technique 1: copy only model-related fields ----
                                            doNewQL = new clRowSet("QL", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), "", true); // bBypassValidation
                                            // doNewQL.SetFieldVal("LNK_in_QT", doForm.doRS.GetFieldVal("GID_ID"))
                                            doNewQL.SetFieldVal("LNK_For_MO", doRS.GetFieldVal("LNK_For_MO", 2), 2);
                                            if (doNewQL.GetLinkCount("LNK_CreditedTo_US") < 1)
                                            {
                                                doNewQL.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));
                                            }
                                            // doNewQL.SetFieldVal("LNK_CreatedBy_US", goP.GetUserTID())      'System linked - filled automatically
                                            if (doNewQL.GetLinkCount("LNK_Peer_US") < 1)
                                            {
                                                doNewQL.SetFieldVal("LNK_Peer_US", doNewQL.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
                                            }
                                            doNewQL.SetFieldVal("SR__LineNo", doRS.GetFieldVal("SR__LineNo", 2), 2);
                                            doNewQL.SetFieldVal("SR__Qty", doRS.GetFieldVal("SR__Qty", 2), 2);
                                            doNewQL.SetFieldVal("TXT_Unit", doRS.GetFieldVal("TXT_Unit"));
                                            doNewQL.SetFieldVal("CUR_PriceUnit", doRS.GetFieldVal("CUR_PriceUnit", 2), 2);
                                            doNewQL.SetFieldVal("SR__DiscPercent", doRS.GetFieldVal("SR__DiscPercent", 2), 2);
                                            doNewQL.SetFieldVal("CUR_Subtotal", doRS.GetFieldVal("CUR_Subtotal", 2), 2);
                                            doNewQL.SetFieldVal("CUR_DiscAddlAmt", doRS.GetFieldVal("CUR_DiscAddlAmt", 2), 2);
                                            doNewQL.SetFieldVal("CUR_PriceUnitAfterDisc", doRS.GetFieldVal("CUR_PriceUnitAfterDisc", 2), 2);
                                            doNewQL.SetFieldVal("CUR_Cost", doRS.GetFieldVal("CUR_Cost", 2), 2);
                                            doNewQL.SetFieldVal("CUR_GrossProfit", doRS.GetFieldVal("CUR_GrossProfit", 2), 2);
                                            doNewQL.SetFieldVal("CHK_Taxable", doRS.GetFieldVal("CHK_Taxable", 2), 2);
                                            doNewQL.SetFieldVal("CHK_Report", doRS.GetFieldVal("CHK_Report", 2), 2);
                                            doNewQL.SetFieldVal("CHK_Include", doRS.GetFieldVal("CHK_Include", 2), 2);
                                            doNewQL.SetFieldVal("TXT_Model", doRS.GetFieldVal("TXT_Model"));
                                            doNewQL.SetFieldVal("MMO_Details", doRS.GetFieldVal("MMO_Details"));
                                            // SKO 11182015 Ticket#800 When creating a cloned qt GP%, ship date, shipping terms fields should be copied over
                                            doNewQL.SetFieldVal("SR__GROSSPROFIT", doRS.GetFieldVal("SR__GROSSPROFIT", 2), 2);

                                            // 'Fields filled in QL_FormOnLoadRecord:
                                            // doForm.doRS.SetFieldVal("DTT_QTETIME", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTT_TIME"))
                                            // doForm.doRS.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("LNK_IN_QT%%DTE_EXPCLOSEDATE"))
                                            // doForm.doRS.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_CREDITEDTO_US", 2), 2)
                                            // doForm.doRS.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_PEER_US", 2), 2)
                                            // doForm.doRS.SetFieldVal("LNK_RELATED_PR", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_RELATED_PR", 2), 2)
                                            // doForm.doRS.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_IN_QT%%LNK_TO_CO", 2), 2)
                                            // doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"))

                                            // 'Fields filled in CRL_QL:
                                            // LNK_IN_QT=<%GID_ID%>
                                            // LNK_Related_PR=<%LNK_Related_PR%>
                                            // MLS_REASONWONLOST=<%MLS_REASONWONLOST%>
                                            // MLS_Status=<%MLS_Status%>

                                            if (doNewQL.Commit() != 1)
                                            {
                                                goP.DeleteVar("bDoNotUpdateQuote");
                                                // CS 6/23/08 Reset var
                                                goP.SetVar("USEQTSTATUS", "");
                                                // CS 8/27/08 Reset var
                                                goP.SetVar("USEQTUSERS", "");
                                                // MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                                                goErr.SetError(35000, sProc, "Error committing an add rowset for the new Quote Line.");
                                                par_doCallingObject = doForm;
                                                return false;
                                            }

                                            break;
                                        }

                                    case "Revision":
                                        {
                                            // ---- Copy all fields, then reset them as needed ------
                                            // doOrigQL = New clRowSet("QL", clC.SELL_EDIT, "GID_ID='" & doRS.GetFieldVal("GID_ID") & "'")
                                            // If doOrigQL.Count < 1 Then
                                            // 'Record not found - skip it and tell the user later
                                            // bQLNotFound = True
                                            // Else
                                            doNewQL = new clRowSet("QL", clC.SELL_ADD, "", "", "", -1, "", "", "CRU_QL", "", "", true); // True: bBypassValidation
                                            if (!goData.CopyRecord(ref doRS, ref doNewQL))
                                            {
                                                goP.DeleteVar("bDoNotUpdateQuote");
                                                // CS 6/23/08 Reset var
                                                goP.SetVar("USEQTSTATUS", "");
                                                // CS 8/27/08 Reset var
                                                goP.SetVar("USEQTUSERS", "");
                                                goErr.SetError(35000, sProc, "Error running goData.CopyRecord(). Are both rowsets in clc.SELL_EDIT mode?");
                                                par_doCallingObject = doForm;
                                                return false;
                                            }
                                            else
                                            {
                                                // Link the line to the new quote
                                                doNewQL.ClearLinkAll("LNK_In_QT");
                                                doNewQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("GID_ID"), clC.SELL_SYSTEM);
                                                // Reset datetime, quote datetime
                                                doNewQL.SetFieldVal("DTT_Time", "Today|Now");
                                                doNewQL.SetFieldVal("DTT_QTETIME", doForm.doRS.GetFieldVal("DTT_Time", clC.SELL_SYSTEM), clC.SELL_SYSTEM);
                                                // Reset Status, Reason, and Completed
                                                // CS 8/3/07: Per PJ, statuses/reasons should remain as they were in original QT
                                                // doNewQL.SetFieldVal("MLS_Status", 0, clC.SELL_SYSTEM)           'Open
                                                // doNewQL.SetFieldVal("MLS_ReasonWonLost", 0, clC.SELL_SYSTEM)    '<Make selection>
                                                doNewQL.SetFieldVal("DTT_TimeCompleted", "");
                                                if (doNewQL.Commit() != 1)
                                                {
                                                    goP.DeleteVar("bDoNotUpdateQuote");
                                                    // CS 6/23/08 Reset var
                                                    goP.SetVar("USEQTSTATUS", "");
                                                    // CS 8/27/08 Reset var
                                                    goP.SetVar("USEQTUSERS", "");
                                                    // MI 3/31/09 added 35000, sproc. The string was in the first parameter.
                                                    goErr.SetError(35000, sProc, "Error committing an add rowset for the new Quote Line.");
                                                    par_doCallingObject = doForm;
                                                    return false;
                                                }
                                            }

                                            break;
                                        }
                                }
                                if (doRS.GetNext() != 1)
                                    break;
                            }
                            goP.DeleteVar("bDoNotUpdateQuote");

                            if (sQuoteOpeningMode == "Revision")
                            {
                                // Set Status of the original quote to Revised (6)
                                doOrigQuote = new clRowSet("QT", clC.SELL_EDIT, "GID_ID='" + sID + "'", "", "*");
                                if (doOrigQuote.Count() > 0)
                                {
                                    doOrigQuote.SetFieldVal("MLS_Status", 6, clC.SELL_SYSTEM);
                                    if (doOrigQuote.Commit() != 1)
                                        sMessage = "The Status of the original Quote can't be changed to 'Revised'. It will be reported in totals as a duplicate of the quote you just created. Please contact your Selltis administrrator. Quote Name: '" + Convert.ToString(doOrigQuote.GetFieldVal("SYS_Name")) + "'. Quote ID: '" + sID + "'.";
                                    // Change the status of all linked QLs to 'revised (6)'
                                    // Have this above: doRS = New clRowSet("QL", clC.SELL_EDIT, "LNK_In_QT='" & sID & "'")
                                    if (doRS.GetFirst() == 1)
                                    {
                                        do
                                        {
                                            doRS.SetFieldVal("MLS_STATUS", 6, 2);
                                            if (doRS.Commit() != 1)
                                                sMessage = "The Status of one or more of the original Quote lines can't be changed to 'Revised'. Please contact your Selltis administrator. Quote Name: '" + Convert.ToString(doOrigQuote.GetFieldVal("SYS_NAME")) + "'. Quote ID: '" + sID + "'.";
                                            if (doRS.GetNext() == 0)
                                                break;
                                        }
                                        while (true);
                                    }
                                }
                            }

                            // Recalc the new quote


                            par_doCallingObject = doForm;
                            //goScr.RunScript("CalcQuoteTotal", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, doForm.doRS.GetFieldVal("GID_ID"));
                            scriptManager.RunScript("CalcQuoteTotal", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
                            doForm = (Form)par_doCallingObject;

                            if (doForm.oVar.GetVar("QuoteDuplicateManageLines") != null && Convert.ToString(doForm.oVar.GetVar("QuoteDuplicateManageLines")) == "1")
                            {
                                // Redisplay the Quote form
                                Form doFormSame = new Form("QT",Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")), "");
                                if (bQLNotFound)
                                {
                                    doFormSame.MessageBox("One or more Quote Lines from the original Quote couldn't be created because they don't exist. They may have been deleted by another user.");
                                }
                                doFormSame.MessagePanel("Click the buttons next to the Lines linkbox to create, edit, or remove the lines.", "#FFFFBO", "#000000", "info.gif");
                                goUI.Queue("FORM", doFormSame);
                            }

                            break;
                        }
                }
            }

            // CS 6/23/08 Reset var
            goP.SetVar("USEQTSTATUS", "");
            // CS 8/27/08
            goP.SetVar("USEQTUSERS", "");

            // CS 7/29/09 Reset variable that told us we had opened a QT in a form. This variable is set in QT_formOnloadrecord and is used in
            // QT_RecordOnSave to tell us that we need to call RecalcTotals if all we did was edit a QT via rowset (not from a form), and not edit QLS
            goP.SetVar("OpenQTForm", "");

            par_doCallingObject = doForm;
            return true;
        }
        // Function Quotline_CalcTotal_Pre(Optional ByRef par_doCallingObject As Object = vbNull, Optional ByVal par_doArray As clArray = Nothing, Optional ByVal par_s1 As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "", Optional ByRef par_oReturn As Object = vbNull, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
        // par_doCallingObject: Form object calling this script. Do not delete in script!
        // par_doArray: Unused.
        // par_s1: Unused.
        // par_s2 to par_s5: Unused.
        // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
        // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
        // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

        // Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
        // goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        // Return True
        // End Function
        public bool Quotline_CalcTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool TD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }

        // SGR 11162015 added event _pre new
        public bool Quote_EmailSubject_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // CS 10/16/07 Created.
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // Purpose: Fills the subject of the email when sending a QT via email
            // This function is called by WebService/Rowset.asmx when a PC Link send job
            // to send a Quote via email is processed.
            // It can be customized the same as all other scripts by creating a _Pre script and
            // setting par_bRunNext=False

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            clRowSet doRS = (clRowSet)HttpContext.Current.Session["WS_RS_" + HttpContext.Current.Session.SessionID];
            //clRowSet doRS = (clRowSet)Util.GetSessionValue("WS_RS_" + HttpContext.Current.Session.SessionID);
            string sSubject;
            string sReturnValue;
            // Check if quote # field is filled
            string TXT_QuoteNo = Convert.ToString(doRS.GetFieldVal("TXT_QuoteNo")).Trim();
            if (TXT_QuoteNo != "")
            {
                // SGR 11162015 Commented one line of code.
                // sSubject = "Quote #" & doRS.GetFieldVal("TXT_QUOTENO", clC.SELL_FRIENDLY) & " from " & doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%COMPANYNAME", clC.SELL_FRIENDLY) & " by " & goP.GetUserName
                sSubject = doRS.GetFieldVal("TXT_DESCRIPTION", clC.SELL_FRIENDLY) + " Quote # " + doRS.GetFieldVal("TXT_QUOTENO", clC.SELL_FRIENDLY) + " by " + goP.GetUserName();
            }
            else
            {
                sSubject = "Quote from " + doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%COMPANYNAME", clC.SELL_FRIENDLY) + " by " + goP.GetUserName();
            }

            par_oReturn = sSubject;

            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote_Draft.docx";
                }
                else if (sQTTemplate == "Project Equipment List")
                {
                    return "cus_corr_ms word_quote_Project Equip List_Draft.docx";
                }
                

            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_corr_ms word_quote.docx";
                }
                else if (sQTTemplate == "Project Equipment List")
                {
                    return "cus_corr_ms word_quote_Project Equip List.docx";
                }
               

            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }

            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);

                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }


        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));


            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            // rsQL.SetFieldVal("CUR_Cost", curCost);

            rsQL.SetFieldVal("SR__Qty", dQty);
            rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);

            //rsQL.SetFieldVal("TXT_Model", sModelText);      
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }
        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
            }

        }

        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }

        public bool QT_MoveQuoteLine_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            par_bRunNext = false;

            string sDirection = "";
            string sLtGt = "";
            string sFirstLast = "";
            string sMoveSortDir = "";

            string sRecID = "";
            string sQuoteID = "";
            string sSelLineNO = "";
            string sMoveLineNO = "";
            double dSelLineNO = 0;
            double dMoveLineNO = 0;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot edit Quote Lines.");
                return true;
            }

            if (par_s3.ToUpper() == "UP")
            {
                sDirection = "up";
                sLtGt = "<";
                sFirstLast = "first";
                sMoveSortDir = "desc";
            }
            else if (par_s3.ToUpper() == "DOWN")
            {
                sDirection = "down";
                sLtGt = ">";
                sFirstLast = "last";
                sMoveSortDir = "asc";
            }
            else
            {
                return false;
            }

            sQuoteID = doForm.doRS.GetFieldVal("GID_ID").ToString();
            sRecID = doForm.GetLinkSelection("LNK_Connected_QL");

            if (string.IsNullOrEmpty(sRecID))
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }

            clRowSet doQLSelRS = new clRowSet("QL", clC.SELL_EDIT, "GID_ID=" + sRecID, "", "*,**", 1, par_bBypassValidation: true);
            if (doQLSelRS.GetFirst() == 0)
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }
            sSelLineNO = doQLSelRS.GetFieldVal("SR__LineNo").ToString();
            dSelLineNO = Convert.ToDouble(doQLSelRS.GetFieldVal("SR__LineNo"));

            clRowSet doQLMoveRS = new clRowSet("QL", clC.SELL_EDIT, ("LNK_IN_QT=" + sQuoteID + " AND SR__LineNo" + sLtGt + sSelLineNO), "SR__LineNo " + sMoveSortDir, "*,**", 1, par_bBypassValidation: true);
            if (doQLMoveRS.GetFirst() == 0)
            {
                doForm.MessageBox("This is the " + sFirstLast + " Qoute Line. Please select another Qoute Line to move " + sDirection + ".");
                par_doCallingObject = doForm;
                return false;
            }
            sMoveLineNO = doQLMoveRS.GetFieldVal("SR__LineNo").ToString();
            dMoveLineNO = Convert.ToDouble(doQLMoveRS.GetFieldVal("SR__LineNo"));

            doQLSelRS.SetFieldVal("SR__LineNo", dMoveLineNO, 2);
            doQLMoveRS.SetFieldVal("SR__LineNo", dSelLineNO, 2);

            if (doQLSelRS.Commit() == 0 || doQLMoveRS.Commit() == 0)
            {
                doForm.MessageBox("Could not save the Quote Lines. Please reopen the quote and try again.");
                par_doCallingObject = doForm;
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");
            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }



            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));

            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECLOSED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
                rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("Cur_UnitPrice", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }


    }

}