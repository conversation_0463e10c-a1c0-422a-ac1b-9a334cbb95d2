﻿using System;
using System.IO;
using System.Text;
using System.Security.Cryptography;

namespace Selltis.BusinessLogic
{
	public class clEncrypt2
	{

		// define the triple des provider
		private TripleDESCryptoServiceProvider m_des = new TripleDESCryptoServiceProvider();

		// define the string handler
		private UTF8Encoding m_utf8 = new UTF8Encoding();

		// define the local property arrays
		private byte[] m_key = {1, 2, 3, 4, 5, 6, 7, 8, 9, 120, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24};
		private byte[] m_iv = {8, 7, 6, 5, 4, 3, 2, 1};


		public byte[] Encrypt(byte[] input)
		{
			return Transform(input, m_des.CreateEncryptor(m_key, m_iv));
		}

		public byte[] Decrypt(byte[] input)
		{
			return Transform(input, m_des.CreateDecryptor(m_key, m_iv));
		}

		public string Encrypt(string text)
		{
			byte[] input = m_utf8.GetBytes(text);
			byte[] output = Transform(input, m_des.CreateEncryptor(m_key, m_iv));
			return Convert.ToBase64String(output);
		}

		public string Decrypt(string text)
		{
			byte[] input = Convert.FromBase64String(text);
			byte[] output = Transform(input, m_des.CreateDecryptor(m_key, m_iv));
			return m_utf8.GetString(output);
		}

		private byte[] Transform(byte[] input, ICryptoTransform CryptoTransform)
		{
			// create the necessary streams
			MemoryStream memStream = new MemoryStream();
			CryptoStream cryptStream = new CryptoStream(memStream, CryptoTransform, CryptoStreamMode.Write);
			// transform the bytes as requested
			cryptStream.Write(input, 0, input.Length);
			cryptStream.FlushFinalBlock();
			// Read the <A class=iAs style="FONT-WEIGHT: normal; FONT-SIZE: 100%; PADDING-BOTTOM: 1px; COLOR: darkgreen; BORDER-BOTTOM: darkgreen 0.07em solid; BACKGROUND-COLOR: transparent; TEXT-DECORATION: underline" href="#" target=_blank itxtdid="3899619">memory</A> stream and convert it back into byte array
			memStream.Position = 0;
			byte[] result = new byte[((int)(memStream.Length - 1)) + 1];
			memStream.Read(result, 0, (int)result.Length);
			// close and release the streams
			memStream.Close();
			cryptStream.Close();
			// hand back the encrypted buffer
			return result;
		}

	}

}
