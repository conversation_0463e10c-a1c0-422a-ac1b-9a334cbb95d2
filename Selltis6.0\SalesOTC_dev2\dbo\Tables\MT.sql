﻿CREATE TABLE [dbo].[MT] (
    [GID_ID]              UNIQUEIDENTIFIER CONSTRAINT [DF_MT_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'MT',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]              BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]            NVARCHAR (80)    NULL,
    [DTT_CreationTime]    DATETIME         CONSTRAINT [DF_MT_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]        TINYINT          NULL,
    [TXT_ModBy]           VARCHAR (4)      NULL,
    [DTT_ModTime]         DATETIME         CONSTRAINT [DF_MT_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_ManmeetTestName] NVARCHAR (50)    NULL,
    [MMO_ImportData]      NTEXT            NULL,
    [SI__ShareState]      TINYINT          CONSTRAINT [DF_MT_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]    UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]        VARCHAR (50)     NULL,
    [TXT_ExternalID]      NVARCHAR (80)    NULL,
    [TXT_ExternalSource]  VARCHAR (10)     NULL,
    [TXT_ImpJobID]        VARCHAR (20)     NULL,
    [MMO_NITES]           NTEXT            NULL,
    CONSTRAINT [PK_MT] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_MT_ManmeetTestName]
    ON [dbo].[MT]([TXT_ManmeetTestName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MT_CreatedBy_US]
    ON [dbo].[MT]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MT_ModDateTime]
    ON [dbo].[MT]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MT_Name]
    ON [dbo].[MT]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MT_CreationTime]
    ON [dbo].[MT]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_MT_BI__ID]
    ON [dbo].[MT]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MT_TXT_ImportID]
    ON [dbo].[MT]([TXT_ImportID] ASC);

