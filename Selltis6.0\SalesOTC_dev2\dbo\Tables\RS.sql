﻿CREATE TABLE [dbo].[RS] (
    [GID_ID]              UNIQUEIDENTIFIER CONSTRAINT [DF_RS_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'RS',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]              BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]            NVARCHAR (80)    NULL,
    [FIL_Attachments]     NTEXT            NULL,
    [DTT_CreationTime]    DATETIME         CONSTRAINT [DF_RS_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]        TINYINT          NULL,
    [DTT_DatePurchased]   DATETIME         NULL,
    [CHK_InUse]           TINYINT          NULL,
    [TXT_InventoryNo]     NVARCHAR (16)    NULL,
    [MMO_Notes]           NTEXT            NULL,
    [TXT_OrigCreatedBy]   VARCHAR (4)      NULL,
    [DTT_OrigCreatedTime] DATETIME         NULL,
    [TXT_ResourceName]    NVARCHAR (35)    NULL,
    [TXT_SerialNo]        VARCHAR (24)     NULL,
    [URL_URLs]            NTEXT            NULL,
    [TXT_ModBy]           VARCHAR (4)      NULL,
    [DTT_ModTime]         DATETIME         CONSTRAINT [DF_RS_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [MMO_ImportData]      NTEXT            NULL,
    [SI__ShareState]      TINYINT          CONSTRAINT [DF_RS_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]    UNIQUEIDENTIFIER NULL,
    [GID_At_LO]           UNIQUEIDENTIFIER NULL,
    [GID_UsedBy_CN]       UNIQUEIDENTIFIER NULL,
    [GID_UsedBy_US]       UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]        VARCHAR (50)     NULL,
    [TXT_ExternalID]      NVARCHAR (120)   NULL,
    [TXT_ExternalSource]  VARCHAR (10)     NULL,
    [TXT_ImpJobID]        VARCHAR (20)     NULL,
    [ADR_Attachments]     NTEXT            NULL,
    CONSTRAINT [PK_RS] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_RS_At_LO] FOREIGN KEY ([GID_At_LO]) REFERENCES [dbo].[LO] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_RS_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_RS_UsedBy_CN] FOREIGN KEY ([GID_UsedBy_CN]) REFERENCES [dbo].[CN] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_RS_UsedBy_US] FOREIGN KEY ([GID_UsedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[RS] NOCHECK CONSTRAINT [LNK_RS_At_LO];


GO
ALTER TABLE [dbo].[RS] NOCHECK CONSTRAINT [LNK_RS_CreatedBy_US];


GO
ALTER TABLE [dbo].[RS] NOCHECK CONSTRAINT [LNK_RS_UsedBy_CN];


GO
ALTER TABLE [dbo].[RS] NOCHECK CONSTRAINT [LNK_RS_UsedBy_US];


GO
CREATE NONCLUSTERED INDEX [IX_RS_CreationTime]
    ON [dbo].[RS]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RS_TXT_ImportID]
    ON [dbo].[RS]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RS_ModDateTime]
    ON [dbo].[RS]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RS_UsedBy_CN]
    ON [dbo].[RS]([GID_UsedBy_CN] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RS_At_LO]
    ON [dbo].[RS]([GID_At_LO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RS_Name]
    ON [dbo].[RS]([SYS_NAME] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_RS_BI__ID]
    ON [dbo].[RS]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RS_UsedBy_US]
    ON [dbo].[RS]([GID_UsedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RS_ResourceName]
    ON [dbo].[RS]([TXT_ResourceName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RS_CreatedBy_US]
    ON [dbo].[RS]([GID_CreatedBy_US] ASC);


GO
CREATE TRIGGER trRSUpdateTN
ON [RS]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in RS table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(120)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'RS'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable
					and tn2.GID_InternalID = inserted.GID_ID)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [RS]
			SET [RS].TXT_ExternalSource = '', [RS].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [RS].GID_ID = in1.GID_ID
				and ISNULL([RS].TXT_ExternalSource, '') <> ''
				and ISNULL([RS].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trRSUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trRSUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trRSUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!