'Owner: MI

Imports Microsoft.VisualBasic
Imports System
Imports System.Web
Imports System.Data
Imports System.Web.UI.WebControls

Public Class clTable
    'MI 5/24/07 Added bReturnStringType public property, when set to true GetVal returns a string
    'MI 2/7/07
    'MI 10/4/06 bug fix in Insert.
    'PURPOSE:
    '       Manage data in DataTable and a UI control bound to it. The purpose is to facilitate working
    '       with DataTables and make it similar to NGP Table functions. 
    '       Currently only the ListBox control can be bound to the datatable via BindListBox() method..
    '       Row index numbers are 1-based here.

    'Unused global objects are commented out
    'Dim goP As clProject
    'Dim goTR As clTransform
    'Dim goMeta As clMetaData
    'Dim goData As clData
    Dim goErr As clError
    'Dim goLog As clLog
    'Dim goDef As clDefaults

    'Private sViewDefaults As String
    Public dt As DataTable
    'Public ds As DataSet
    Public Par_oListBox As ListBox
    Public sValueColName As String     'bound to listbox 'value': primary key column for the datatable
    Public sTextColName As String      'bound to listbox as 'text'
    Public lSelectedRow As Long = 0    'Index (1-based) that corresponds to the DataTable's 'selected' row (0-based index). Corresponds to listbox.SelectedIndex + 1 (MI 11/24/09 Is + 1 true?).
    Public bReturnStringType As Boolean = False     'When true, GetVal() returns .ToString value regardless of the data type in a column.

    Private column As DataColumn
    Private row As DataRow
    Private sEmptyValue As String = "~!@#$%^&*()_+_)(*&^%$#@!~"


    Public Sub Initialize()
        Dim sProc As String = "clTable::Initialize"

        'goP = HttpContext.Current.Session("goP")
        'goTR = HttpContext.Current.Session("goTr")
        'goMeta = HttpContext.Current.Session("goMeta")
        'goData = HttpContext.Current.Session("goData")
        goErr = HttpContext.Current.Session("goErr")
        'goLog = HttpContext.Current.Session("goLog")
        'goDef = HttpContext.Current.Session("goDef")

    End Sub

    Public Sub New()
        'Overloaded to be able to declare a global object within a page before 
        'go objects become available.

    End Sub

    Public Sub New(ByRef par_dtDataTable As DataTable, _
                Optional ByVal par_sValueColName As String = "Value", _
                Optional ByVal par_sTextColName As String = "Text", _
                Optional ByVal par_bReturnStringType As Boolean = False)
        'PURPOSE:
        '       Initializes the clTable object and creates the following columns:
        '           'Index' (long)
        '           par_sValueColName ('Value' by default) (object)
        '           par_sTextColName ('Text' by default) (string)
        '       Index column is used to keep the IndexOf value in the table so it can
        '       be used for sorting in datarow.Select(). If you want to bind the datatable to
        '       a listbox, run BindListbox after running clTable methods. This will populate
        '       the listbox and select the appropriate item in it.
        '       If you need to, you can set the datatable to be part of a dataset like this:
        '           Dim dsDataSet As DataSet
        '           dsDataSet = New DataSet()
        '           dsDataSet.Tables.Add(dtThisDataTable)
        'PARAMETERS:
        '       par_dtDataTable: DataTable object. Instantiate this object like this:
        '               Dim dtDataTable As New DataTable("ListOfFields")
        '               'Create a new clTable object
        '               Dim t as New clTable(dtDataTable)
        '           Note that you can declare a global clTable object without providing
        '           any parameters.
        '       par_sValueColName: Name of the 'Value' column. Default is 'Value'. 
        '           This is the 'key' value by which each row is identified in the listbox,
        '           if it is bound. The value must be unique.
        '           This becomes the PrimaryKeyColumn for the datatable.
        '           If you provide this parameter, the column 'Value' is created as the
        '           first column. Don't attempt to create it with AddColumn().
        '       par_sTextColName: Name of the 'text' column. Default is 'Text'.
        '           This column is bound to the listbox's .Text property. This is what the
        '           user will see in the 
        '           listbox. If you provide this parameter, the column 'Text' is created 
        '           automatically as the second column (or first column if par_sValueColName
        '           is blank. Don't attempt to create it with AddColumn().
        '       par_bReturnStringType: False by default. If true, GetVal returns all values
        '           as string. DBNulls get returned as "".

        Dim sProc As String = "clTable::New"
        sValueColName = par_sValueColName
        sTextColName = par_sTextColName

        Initialize()

        If par_dtDataTable Is Nothing Then
            goErr.SetError(30012, sProc, , "par_dtDataTable")
            '30012: Object '[1]' is not assigned.
        End If

        bReturnStringType = par_bReturnStringType

        dt = par_dtDataTable

        'Enforce
        'Value column
        If par_sValueColName = "" Then
            goErr.SetError(35000, sProc, "par_sValueColName must not be blank.")
        End If
        'Text column is required
        If par_sTextColName = "" Then
            goErr.SetError(35000, sProc, "par_sTextColName must not be blank.")
        End If

        'Create default columns
        Me.AddColumn("Index", System.Type.GetType("System.Int64"), True)
        Me.AddColumn(par_sValueColName, , True)
        If par_sTextColName <> "" Then Me.AddColumn(par_sTextColName)

        'Set primary key column on the index
        Dim PrimaryKeyColumns(0) As DataColumn
        PrimaryKeyColumns(0) = dt.Columns(sValueColName)
        'PrimaryKeyColumns(0) = dt.Columns("Index")
        'Set the column to primary key makes the column unique
        dt.PrimaryKey = PrimaryKeyColumns

    End Sub

    Function Add(Optional ByVal par_sTableName As String = "", _
                    Optional ByVal par_drDataRow As System.Data.DataRow = Nothing) As Boolean
        'PURPOSE:
        '       Appends a row to the end of the table. Does not change the selected row.
        '       In NGP was used to create a table or add a whole row
        '       of tab-delimited values.
        'PARAMETERS:
        '       par_sTableName: ignored.
        '       par_drDataRow: Optional: DataRow class that has the values of a whole datatable
        '           row. This is useful when copying a row. The structure must be
        '           the same. If this parameter is omitted, an empty row is added
        '           for you to fill it with tTable.SetVal().
        '           IMPORTANT: If one of the columns has the Unique property set,
        '           only one blank row can be created. Adding a second blank row will
        '           raise an error.
        'RETURNS:
        '       Boolean: True if a row added, False otherwise.

        Dim sProc As String = "clTable::Add"

        Dim bResult As Boolean = False
        Dim lEmptyRow As Long = 0

        'Try
        If par_drDataRow Is Nothing Then
                lEmptyRow = Seek(Me.sValueColName, sEmptyValue)
                If lEmptyRow > 0 Then
                    'Delete empty row
                    Delete("", lEmptyRow)
                End If
                Dim drNewRow As DataRow = dt.NewRow()
                drNewRow.Item("Index") = dt.Rows.Count
                'Set Value to blank - NULL value is not allowed
                drNewRow.Item(Me.sValueColName) = sEmptyValue
                dt.Rows.Add(drNewRow)
            Else
                dt.Rows.Add(par_drDataRow)
            End If

            'Leave the selected row unchanged
            'Me.lSelectedRow = dt.Rows.Count()

            RegenerateIndexCol()

            bResult = True
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return bResult

    End Function

    Function AddColumn(ByVal par_sName As String, _
                    Optional ByVal par_tDataType As System.Type = Nothing, _
                    Optional ByVal par_bUnique As Boolean = False) As Boolean
        'MI 7/10/07
        'PURPOSE:
        '       Adds a column to the .Columns collection to the right of other columns
        '       Once added, columns can't be removed. Keep in mind that New()
        '       adds 'Index', 'Value' and, optionally, 'Text' columns.
        'PARAMETERS:
        '       par_sName: Name of the column.
        '       par_tDataType: pass a value such as System.Type.GetType("System.Int32") value.
        '           "System.String" is the default. The GetType string is case sensitive.
        '       par_bUnique: Enforce uniqueness of values in this column. If True,
        '           NULL values (Nothing) can't be set in this column.

        Dim sProc As String = "clTable::AddColumn"

        Dim bResult As Boolean = True

        'Try
        'Create columns
        column = New DataColumn()
            If par_tDataType Is Nothing Then
                column.DataType = System.Type.GetType("System.String")
            Else
                column.DataType = par_tDataType
            End If
            column.Unique = par_bUnique
            column.ColumnName = par_sName
            dt.Columns.Add(column)
            'Catch ex As Exception
            '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '        goErr.SetError(ex, 45105, sProc)
            '    End If
            'End Try

            Return bResult

    End Function

    Function BindListbox(ByRef par_oListBox As Object) As Boolean  'par_oListBox as ListBox
        'MI 12/1/09 Fixed error 'Selected row is greater than the combobox count' when combo is empty.
        'MI 11/24/09 added dealing with dropdownlist: now selecting element 1 if selection not defined.
        'PURPOSE:
        '       Bind the datatable to a listbox control and select an item in it.
        'PARAMETERS:
        '       par_oListBox: listbox control to bind. For dropdownlist control, use BindCombobox.
        '       par_bCombo: DEPRECATED Now dropdownlist vs listbox is determined from the
        '           object passed in par_oListBox. 
        '           Original NGP notes: Set to true if the control is a dropdownlist (combo).
        '           When set to true, if the selection is undefined t.Selected() is -1,
        '           the selected item will be set to the first element (index 0) 
        '           because dropdownlist doesn't allow not having an element selected.
        'RETURNS:
        '       True.

        Dim sProc As String = "clTable::BindListbox"
        Dim sType As String
        Dim bCombo As Boolean = False

        'Try
        sType = par_oListBox.GetType.ToString
            If UCase(sType) = "SYSTEM.WEB.UI.WEBCONTROLS.DROPDOWNLIST" Then
                bCombo = True
            End If


            'dt.AcceptChanges()
            'DEBUG
            Dim sSelectedvalue As String = par_oListBox.SelectedValue
            'END DEBUG

            par_oListBox.items.Clear()
            par_oListBox.DataSource = dt
            par_oListBox.DataValueField = sValueColName
            par_oListBox.DataTextField = sTextColName
            par_oListBox.SelectedIndex = -1    'MI 11/24/09 commenting. -1 doesn't work in combos and is redundant in lists after CLear()
            par_oListBox.DataBind()
            If bCombo Then
                'dropdownlist
                'Select item in the listbox
                If lSelectedRow > par_oListBox.Items.Count Then
                    goErr.SetError(35000, sProc, "Selected row is greater than the combobox count.")
                    'MessTranslate()
                End If
                If par_oListBox.Items.Count > 0 Then
                    'Select first item if nothing is to be selected
                    If lSelectedRow < 1 Then
                        lSelectedRow = 1
                    End If
                    'Select an item
                    par_oListBox.SelectedIndex = lSelectedRow - 1
                End If
            Else
                'listbox
                'Select item in the listbox
                If lSelectedRow < 1 Then
                    lSelectedRow = -1
                End If
                If lSelectedRow > par_oListBox.Items.Count Then
                    goErr.SetError(35000, sProc, "Selected row is greater than the ListBox count.")
                    'MessTranslate()
                End If
                If par_oListBox.Items.Count > 0 Then
                    If lSelectedRow = -1 Then
                        par_oListBox.SelectedIndex = lSelectedRow
                    Else
                        'A row is selected
                        par_oListBox.SelectedIndex = lSelectedRow - 1
                    End If
                End If
            End If
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return True

    End Function
    Function Count(Optional ByVal par_sTableName As String = "", _
                    Optional ByVal par_iType As Integer = 1) As Long
        'PURPOSE:
        '       Count the no of rows or columns in the table.
        'PARAMETERS:
        '       par_sTableName: can be left blank - for backward compatibility.
        '       par_iType: 1: number of rows; 2: number of columns.
        'RETURNS:
        '       Long: no of rows or columns in the table.

        Dim sProc As String = "clTable::Count"

        ' Try
        If par_iType = 1 Then
                Return dt.Rows.Count
            Else
                Return dt.Columns.Count
            End If
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

    End Function
    Function Delete(ByVal par_sTable As String, _
                    Optional ByVal par_lRow As Long = -1) As Boolean
        'PURPOSE:
        '       Deletes a row from the table based on its index (1-based).
        '       Adjusts selected row as needed. If row is not provided,
        '       the selected row is deleted. 
        'PARAMETERS:
        '       par_sTable: for backward compatibility - can be "".
        '       par_lRow: Optional: Index of the row to delete (1-based).
        '           If < 1, the currently selected row is deleted. If the
        '           selected row is invalid, no row is deleted and 
        '           result returned is False.
        'RETURNS:
        '       Boolean: true if successful, False if the row is invalid
        '       (> Count or < 1 - error is not raised).

        Dim sProc As String = "clTable::Delete"
        Dim bResult As Boolean = True

        'Try
        If par_lRow < 1 Then
                par_lRow = Me.lSelectedRow
            End If
            If par_lRow > Count() Or par_lRow < 1 Then
                'Invalid row
                bResult = False
            Else
                If dt.Rows.Count < 1 Then
                    'Nothing to delete
                    bResult = False
                Else
                    'Delete
                    dt.Rows.RemoveAt(par_lRow - 1)
                    dt.AcceptChanges()
                    'Adjust selected row
                    If lSelectedRow > par_lRow Then
                        lSelectedRow = lSelectedRow - 1
                    End If
                    If lSelectedRow > dt.Rows.Count() Then
                        lSelectedRow = dt.Rows.Count()
                    End If
                    If lSelectedRow < 1 Then
                        lSelectedRow = -1
                    End If
                    Me.RegenerateIndexCol()
                End If
            End If
            'Catch ex As Exception
            '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '        goErr.SetError(ex, 45105, sProc)
            '    End If
            'End Try

            Return bResult

    End Function
    Function DeleteAll(Optional ByVal par_sTableName As String = "") As Boolean
        'PURPOSE:
        '       Delete all rows in the datatable.
        'PARAMETERS:
        '       par_sTableName: ignored.
        'RETURNS:
        '       True or raises an error.

        Dim sProc As String = "clTable::DeleteAll"

        'Try
        dt.Rows.Clear()
            dt.AcceptChanges()
            lSelectedRow = -1
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return True
    End Function
    Function GetSelectedValue() As Object
        'PURPOSE:
        '       Returns the 'Value' column value for the currently selected row.
        'RETURNS:
        '       Object: value in datatype of the column.

        Dim sProc As String = "clTable::GetSelectedValue"
        Dim oResult As Object = Nothing
        Dim sResult As String = ""

        'Try
        If lSelectedRow > dt.Rows.Count Then
                goErr.SetError(35000, sProc, "lSelectedRow exceeds row count of the DataTable.")
                If bReturnStringType Then
                    sResult = ""
                    Return sResult
                Else
                    Return Nothing
                End If
            End If

            If lSelectedRow < 1 Then
                If bReturnStringType Then
                    sResult = ""
                    Return sResult
                Else
                    Return Nothing
                End If
                'Select Case dt.Columns(sValueColName).DataType.ToString
                '    Case "System.String", "System.Char", "System.Byte"
                '        Return ""
                '    Case "System.DateTime"
                '        Return ""
                '    Case Else   'Numeric
                '        Return 0
                'End Select
            End If
            oResult = dt.Rows.Item(lSelectedRow - 1).Item(Me.sValueColName)
            If bReturnStringType Then
                If oResult Is System.DBNull.Value Then
                    sResult = ""
                    Return sResult
                Else
                    If oResult Is Nothing Then
                        sResult = ""
                        Return sResult
                    Else
                        Return oResult.ToString
                    End If
                End If
            End If

            'Catch ex As Exception
            '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '        goErr.SetError(ex, 45105, sProc)
            '    End If
            'End Try

            Return oResult

    End Function

    Function GetVal(ByVal par_sColumnName As String, _
                    Optional ByVal par_lRow As Long = -1) As Object
        'PURPOSE:
        '       Retrieves a value from a particular cell (row/column intersection). In NGP,
        '       the equivalent syntax is <value> = COL_COLUMNNAME_TABLENAME[index].
        'PARAMETERS:
        '       par_sColumnName: Name of the column
        '       par_lRow: index of the row from which to retrieve the value (1-based index).
        '           If out of range, an error is raised. If < 1 the currently selected row
        '           is used. If no row is selected, Nothing will be returned.
        'RETURNS:
        '       Value in data type of the column or, if bReturnStringType is True, the value
        '       as string. If the value is DBNull or Nothing, "" is returned.
        '       If lRow is out of range, an error is raised.
        '       WARNING: if you don't set par_bReturnStringType parameter in New. to True,
        '       keep in mind that the value CAN be system.DBNull.value!
        '       Use t.GetVal().ToString to turn it into "".

        Dim sProc As String = "clTable::GetVal"

        Dim oResult As Object = Nothing
        Dim sResult As String = ""
        Dim lRow As Long = par_lRow

        'Try
        If lRow < 1 Then
                'Use selected row
                lRow = Me.lSelectedRow
                If lRow < 1 Then
                    If bReturnStringType Then
                        sResult = ""
                        Return sResult
                    Else
                        Return System.DBNull.Value
                    End If
                End If
            End If
            If lRow > dt.Rows.Count Then
                goErr.SetError(35000, sProc, "lRow exceeds row count of the DataTable.")
                If bReturnStringType Then
                    sResult = ""
                    Return sResult
                Else
                    Return System.DBNull.Value
                End If
            End If
            If dt.Rows.Count < 1 Then
                If bReturnStringType Then
                    sResult = ""
                    Return sResult
                Else
                    Return System.DBNull.Value
                End If
            Else
                oResult = dt.Rows(lRow - 1).Item(par_sColumnName)
                If bReturnStringType Then
                    If oResult Is System.DBNull.Value Then
                        sResult = ""
                        Return sResult
                    Else
                        If oResult Is Nothing Then
                            sResult = ""
                            Return sResult
                        Else
                            sResult = oResult.ToString
                            Return sResult
                        End If
                    End If
                End If
            End If

            'Catch ex As Exception
            '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '        goErr.SetError(ex, 45105, sProc)
            '    End If
            'End Try

            Return oResult

    End Function
    Function Insert(Optional ByVal par_sTableName As String = "", _
                    Optional ByVal par_drDataRow As System.Data.DataRow = Nothing, _
                    Optional ByVal par_lRowNo As Long = 0) As Boolean
        'PURPOSE:
        '       Inserts a row at an index position (1-based). To append a row to 
        '       the bottom of the datatable, use Add(). Does not make the inserted
        '       row the selected row. 
        'PARAMETERS:
        '       par_sTableName: ignored.
        '       par_drDataRow: DataRow class that contains the values of a whole
        '           data row from a DataTable. If omitted, a blank row is inserted
        '           unless par_lRowno is 0 or greater than Count, in which case
        '           this works like Add().
        '       par_lRowNo: The number of the row where to insert the new row.
        '           Rows below get moved one position down. If this parameter is
        '           not specified, the row is inserted before the currently
        '           selected row (lSelectedRow). If lSelectedRow < 1 or > Count(),
        '           the method calls Add().
        'EXAMPLE:
        '       In NGP:
        '           'Insert a copy of the selected row
        '           TableInsert("TBL_FIELDS",TBL_FIELDS[TBL_FIELDS],TBL_FIELDS)
        '           'Insert a blank row in position 1
        '           TableInsert("TBL_AVAIL","",1)

        Dim sProc As String = "clTable::Insert"
        Dim drNewRow As DataRow
        Dim lRowNo As Long = par_lRowNo
        Dim lEmptyRow As Long
        Dim lDuplicateValueRow As Long

        'Try
        If lRowNo < 0 Or lRowNo > dt.Rows.Count Then
                'Unsupported row no
                Return Add(par_sTableName, par_drDataRow)
            ElseIf lRowNo = 0 Then
                lRowNo = Me.lSelectedRow
                'Selected row is 0 or out of whack
                If Me.lSelectedRow < 1 Or Me.lSelectedRow > dt.Rows.Count Then
                    Return Add(par_sTableName, par_drDataRow)
                End If
            End If

            If par_drDataRow Is Nothing Then
                'If an empty row exists due to an unsuccessful Insert or Add, remove it
                lEmptyRow = Seek(Me.sValueColName, sEmptyValue)
                If lEmptyRow > 0 Then
                    'Delete empty row
                    Delete("", lEmptyRow)
                End If
                'Create a new row
                drNewRow = dt.NewRow()
                drNewRow.Item("Index") = dt.Rows.Count
                drNewRow.Item(Me.sValueColName) = sEmptyValue    'Null not allowed System.DBNull.Value
                dt.Rows.InsertAt(drNewRow, lRowNo - 1)
            Else
                'Does a duplicate 'Value' exist?
                lDuplicateValueRow = Seek(sValueColName, par_drDataRow.Item(dt.Columns(Me.sValueColName).Ordinal))
                If lDuplicateValueRow > 0 Then
                    goErr.SetWarning(35000, sProc, "Duplicate value exists in row '" & lDuplicateValueRow.ToString & "': '" & par_drDataRow.Item(dt.Columns(Me.sValueColName).Ordinal).ToString & "'.")
                    'MessTranslate
                    Return False
                End If
                dt.Rows.InsertAt(par_drDataRow, lRowNo - 1)
            End If

            RegenerateIndexCol()

            'Let the selected row remain unchanged
            'lSelectedRow = lRowNo

            'Catch ex As Exception
            '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '        goErr.SetError(ex, 45105, sProc)
            '    End If
            'End Try

            Return True

    End Function
    Function MoveDown() As Boolean
        'MI 10/4/06 Ported from NGP.
        'OWNER: MI		3/13/2002

        'PURPOSE:
        '		Move the selected row in a cltable object one position down
        '       and select that row.
        'PARAMETERS:
        'RETURNS:
        '		Boolean - indicates whether the selected row can be moved down
        '			This can be useful if you need to gray out or disable a button
        '			with which the user is moving through the table
        '			True: row can be moved down at least one more position
        '			False: row is on the lowest position, can't be moved farther
        'EXAMPLE:
        '		gw.tFields.MoveDown()

        Dim sProc As String = "clTable::MoveDown"

        Dim sTable As String = ""
        Dim iRow As Integer
        Dim iRowCount As Integer
        Dim dr As DataRow
        Dim sColName As String
        Dim oValue As Object
        Dim oValueColValue As Object = ""
        Dim i As Integer
        Dim bOrigReturnStringType As Boolean = bReturnStringType

        'Don't alter data type returned by GetVal to string
        bReturnStringType = False

        iRowCount = Count()
        iRow = Selected()
        'Prevent out of range index exception in dt below
        If iRow < 0 Then iRow = 1
        If iRow < iRowCount Then
            'Get the current row's DataRow object
            dr = Me.dt.Rows(iRow - 1)
            If dr Is Nothing Then
                'Return the GetVal data type to original setting
                bReturnStringType = bOrigReturnStringType
                goErr.SetError(35000, sProc, "Datatable row is null (0-based index):  '" & iRow - 1 & "'.")
            End If
            'We can't just copy the row (say using dt.ImportRow) because the 
            'value and index columns must not have duplicates.
            'That's why we do this the long way: create a blank row, populate it,
            'delete the existing row, then modify the new row.
            If Not Insert(sTable, , iRow + 2) Then
                'Return the GetVal data type to original setting
                bReturnStringType = bOrigReturnStringType
                goErr.SetError(35000, sProc, "Inserting a blank row failed at row (1-based index) '" & iRow - 1 & "'.")
            End If
            'Populate values in the new row
            For i = 0 To dt.Columns.Count - 1
                sColName = dt.Columns(i).ColumnName
                If sColName = "Index" Then
                    'Count should be unique if RegenerateIndexCol is run whenever modifying rows
                    oValue = dt.Rows.Count
                ElseIf sColName = Me.sValueColName Then
                    oValueColValue = GetVal(sColName, iRow)
                    oValue = Me.sEmptyValue
                Else
                    oValue = GetVal(sColName, iRow)
                End If
                SetVal(sColName, iRow + 2, oValue)
            Next

            SelectPlus(sTable, iRow + 2)
            Delete(sTable, iRow)
            SetVal(Me.sValueColName, iRow + 1, oValueColValue)
        End If

        Me.RegenerateIndexCol()

        'Return the GetVal data type to original setting
        bReturnStringType = bOrigReturnStringType

        If Selected(sTable) > iRowCount - 1 Then
            Return False
        Else
            Return True
        End If

    End Function
    Function MoveUp() As Boolean
        'MI 10/4/06 Ported from NGP.
        'OWNER: MI		3/13/2002

        'PURPOSE:
        '		Move the selected row in a table one position upward
        '       and select that row.
        'PARAMETERS:
        '		par_sWindowName: <window name>
        '		par_sTableName: <name of the table>
        'RETURNS:
        '		Boolean - indicates whether the selected row can be moved up
        '			This can be useful if you need to gray out or disable a button
        '			with which the user is moving through the table
        '			True: row can be moved up at least one more position
        '			False: row is on position 1, can't be moved farther
        'EXAMPLE:
        '		tTable.MoveUp()

        Dim sProc As String = "clTable::MoveUp"

        Dim sTable As String = ""
        Dim iRow As Integer
        Dim dr As DataRow
        Dim i As Integer
        Dim sColName As String
        Dim oValue As Object
        Dim oValueColValue As Object = ""
        Dim bOrigReturnStringType As Boolean = bReturnStringType

        'Don't alter data type returned by GetVal to string
        bReturnStringType = False

        iRow = Selected()
        If iRow > 1 Then
            'Get the current row's DataRow object
            dr = Me.dt.Rows(iRow - 1)
            If dr Is Nothing Then
                'Return the GetVal data type to original setting
                bReturnStringType = bOrigReturnStringType
                goErr.SetError(35000, sProc, "Datatable row is null (0-based index): '" & iRow - 1 & "'.")
            End If
            'We can't just copy the row (say using dt.ImportRow) because the 
            'value and index columns must not have duplicates.
            'That's why we do this the long way: create a blank row, populate it,
            'delete the existing row, then modify the new row.
            'Insert a blank row
            If Not Insert(sTable, , iRow - 1) Then
                'Return the GetVal data type to original setting
                bReturnStringType = bOrigReturnStringType
                goErr.SetError(35000, sProc, "Inserting a blank row failed at row (1-based index) '" & iRow - 1 & "'.")
            End If
            'Populate values in the new row
            For i = 0 To dt.Columns.Count - 1
                sColName = dt.Columns(i).ColumnName
                If sColName = "Index" Then
                    'Count should be unique if RegenerateIndexCol is run whenever modifying rows
                    oValue = dt.Rows.Count
                ElseIf sColName = Me.sValueColName Then
                    oValueColValue = GetVal(sColName, iRow + 1)
                    oValue = Me.sEmptyValue
                Else
                    oValue = GetVal(sColName, iRow + 1)
                End If
                SetVal(sColName, iRow - 1, oValue)
            Next
            SelectPlus(sTable, iRow - 1)
            Delete(sTable, iRow + 1)
            SetVal(Me.sValueColName, iRow - 1, oValueColValue)
        End If

        Me.RegenerateIndexCol()

        'Return the GetVal data type to original setting
        bReturnStringType = bOrigReturnStringType

        If Selected(sTable) < 2 Then
            Return False
        Else
            Return True
        End If

    End Function

    Function MoveToTable(ByRef par_oToTable As clTable, _
                        Optional ByRef par_oFromListBox As ListBox = Nothing, _
                        Optional ByRef par_oToListBox As ListBox = Nothing, _
                        Optional ByVal par_lTable1Row As Long = 0, _
                        Optional ByVal par_lTable2Row As Long = 0, _
                        Optional ByVal par_sMode As String = "Insert", _
                        Optional ByVal par_bCopyOnly As Boolean = False) As Boolean
        'MI 10/24/06	Added checking for duplicate in the value column
        'MI 10/5/06 Ported from MoveFromTableToTable in NGP.

        'AUTHOR: MI 3/2/2003
        'PURPOSE:
        '		Move or copy a row of data from one table to another and make that
        '       the selected row in the target table. The columns of the two tables
        '       must match. If par_sMode is "Insert",
        '       the new row is inserted above the selected line in table 2
        '		If "Append", the new row is added on the bottom of table 2. If par_bCopyOnly
        '       is True, the row is not removed from the 'from' table.
        '       If listbox parameters are passed, from and to listboxes are bound to the
        '       tables.
        '		Both tables MUST have exactly the same columns including masks and widths!
        '       Moving/copying will fail and False will be returned if me.sValueColName column
        '       in the target table already contains the value being moved/copied.
        'PARAMETERS:
        '		par_oToTable: clTable into which to insert the row.
        '       par_oFromListBox: Optional: listbox bound to the 'from' table.
        '		par_oToListBox: Optional: Listbox bound to the target table.
        '		par_lTable1Row: Optional: row of table 1. If < 1, move or copy the 
        '           currently selected row.
        '		par_lTable2Row: Optional: row of table 2. If < 1, insert above currently
        '           selected row. Ignored if par_sMode is not "Insert".
        '		par_sMode: "Insert" or "Append".
        '		par_bCopyOnly: If true, don't remove the row from table 1.
        'RETURNS:
        '		Boolean: True if successful or False if the Value in the target table
        '           would be duplicated. In that case the row is not moved/copied.
        'HOW IT WORKS:
        '		Takes row from table 1 and inserts it above the selected row of table 2,
        '		then deletes the row from table 1. Data binds the listboxes.
        'EXAMPLES (NGP):
        '		oTable:MoveFromTableToTable(FenEnExecution()+".TBL_1",FenEnExecution()+".TBL_2")
        '			'inserts selected row from table 1 above selected row of table 2
        '		oTable:MoveFromTableToTable(FenEnExecution()+".TBL_1",FenEnExecution()+".TBL_2",3,2)
        '			'Inserts row 3 from table 1 above row 2 of table 2


        Dim sProc As String = "clTable::MoveToTable"
        Dim bResult As Boolean = True
        Dim sColName As String = ""
        Dim oValue As Object = Nothing
        Dim oValueColValue As Object = Nothing
        Dim i As Integer = 0
        Dim lDuplicateValueRow As Long
        Dim sToColumnName As String = ""
        Dim bOrigReturnStringType As Boolean = bReturnStringType

        'Try
        Dim iRow1 As Integer
            Dim iRow2 As Integer

            If par_oToTable Is Nothing Then
                goErr.SetError(30012, sProc, "", "par_oToTable")
                '30012: Object '[1]' is not assigned.
            End If

            If par_lTable1Row < 1 Then
                iRow1 = Selected()
            Else
                iRow1 = par_lTable1Row
            End If

            If Count() < 1 Then
                'No rows in the 'from' table
                Return False
            End If

            'Set getVal return type to non-string so the values don't get changed
            bReturnStringType = False

            If par_sMode = "Insert" Then
                If par_lTable2Row < 1 Then
                    iRow2 = par_oToTable.Selected()
                Else
                    iRow2 = par_lTable2Row
                End If
                'Does a duplicate 'Value' exist in the target table?
                lDuplicateValueRow = par_oToTable.Seek(par_oToTable.sValueColName, GetVal(Me.sValueColName, iRow1))
                If lDuplicateValueRow > 0 Then
                    'Return the GetVal data type to original setting
                    bReturnStringType = bOrigReturnStringType
                    goErr.SetWarning(35000, sProc, "Duplicate value exists in the target table in row '" & lDuplicateValueRow.ToString & "': '" & GetVal(Me.sValueColName, iRow1) & "'.")
                    'MessTranslate
                    'Exit without moving the row
                    Return False
                End If
                'Insert a row
                par_oToTable.Insert(, , iRow2)
                'If the 'to' table was blank, now we are dealing with row 1
                If iRow2 < 1 Then iRow2 = 1
                'Populate cell values column by column
                For i = 0 To dt.Columns.Count - 1
                    sColName = dt.Columns(i).ColumnName
                    sToColumnName = par_oToTable.dt.Columns(i).ColumnName
                    'Not comparing column names because we are allowing them to be different.
                    'However, the purpose, count, and type of columns must match.
                    If sColName = "Index" Then
                        'Count should be unique if RegenerateIndexCol is run whenever modifying rows
                        oValue = par_oToTable.Count
                    ElseIf sColName = Me.sValueColName Then
                        oValueColValue = GetVal(sColName, iRow1)
                        'oValue = Me.sEmptyValue
                        oValue = oValueColValue
                    Else
                        oValue = GetVal(sColName, iRow1)
                    End If
                    par_oToTable.SetVal(sToColumnName, iRow2, oValue)
                Next
                'Select the inserted row
                par_oToTable.SelectPlus("", iRow2)
                ''Set the original value in the 'Value' column
                'par_oToTable.SetVal(par_oToTable.sValueColName, iRow2, oValueColValue)
            Else
                'Append
                'Does a duplicate 'Value' exist in the target table?
                lDuplicateValueRow = par_oToTable.Seek(par_oToTable.sValueColName, GetVal(Me.sValueColName, iRow1))
                If lDuplicateValueRow > 0 Then
                    'Return the GetVal data type to original setting
                    bReturnStringType = bOrigReturnStringType
                    goErr.SetWarning(35000, sProc, "Duplicate value exists in the target table in row '" & lDuplicateValueRow.ToString & "': '" & GetVal(Me.sValueColName, iRow1) & "'.")
                    'MessTranslate
                    'Exit without moving the row
                    Return False
                End If
                par_oToTable.Add()
                iRow2 = par_oToTable.Count()
                'Populate cell values column by column
                For i = 0 To dt.Columns.Count - 1
                    sColName = dt.Columns(i).ColumnName
                    'Not comparing column names because we are allowing them to be different.
                    'However, the purpose, count, and type of columns must match.
                    If sColName = "Index" Then
                        'Count should be unique if RegenerateIndexCol is run whenever modifying rows
                        oValue = iRow2
                    ElseIf sColName = Me.sValueColName Then
                        oValueColValue = GetVal(sColName, iRow1)
                        'oValue = Me.sEmptyValue
                        oValue = oValueColValue
                    Else
                        oValue = GetVal(sColName, iRow1)
                    End If
                    par_oToTable.SetVal(par_oToTable.dt.Columns(i).ColumnName, iRow2, oValue)
                Next
                'Select the added row
                par_oToTable.SelectPlus("", iRow2)
                ''Set the original value in the 'Value' column
                'par_oToTable.SetVal(par_oToTable.sValueColName, iRow2, oValueColValue)
            End If

            If Not par_bCopyOnly Then
                Delete("", iRow1)
                RegenerateIndexCol()
            End If

            par_oToTable.RegenerateIndexCol()

            If Not par_oFromListBox Is Nothing Then
                Me.BindListbox(par_oFromListBox)
            End If
            If Not par_oToListBox Is Nothing Then
                par_oToTable.BindListbox(par_oToListBox)
            End If

            'Return the GetVal data type to original setting
            bReturnStringType = bOrigReturnStringType

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return bResult

    End Function
    Function Position(Optional ByVal par_sTableName As String = "") As Long
        'MI 12/16/11 Removed Dim lResult As Long = 0.
        'PURPOSE:
        '       Calls Selected, which returns the selected row in the datatable (1-based index).
        '       Else, returns 0.
        '       In NGP this was returning the index of the top-most displayed row.

        Dim sProc As String = "clTable::Position"

        Return Selected(par_sTableName)

    End Function
    Sub RegenerateIndexCol()
        'PURPOSE:
        '       Reestablish values in the index column in the order
        '       of rows. Values in the index column should reflect the
        '       the actual row index values of the underlying
        '       datatable (0-based index).

        Dim sProc As String = "clTable::RegenerateIndexCol"

        'Try
        Dim l As Long

            'First pass puts very large nos to avoid duplicate value errors
            For l = 0 To dt.Rows.Count - 1
                dt.Rows(l).Item("Index") = l + 2000000000
            Next

            'Second pass populates "right" numbers
            For l = 0 To dt.Rows.Count - 1
                dt.Rows(l).Item("Index") = l
            Next
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

    End Sub
    Function Seek(ByVal par_sColumnName As String, _
                    ByVal par_oValue As Object, _
                    Optional ByVal par_bExact As Boolean = True, _
                    Optional ByVal par_lStartRow As Long = 1) As Long
        'PURPOSE:
        '       Returns an index (1-based) of the row which starts with 
        '       a value in a specified column. If not found, -1 is returned.
        'PARAMETERS:
        '       par_sColumnName: Name of the column in which to find a value.
        '       par_oValue: Value to find. Data type must match or convert
        '           to the column's data type.
        '       par_bExact: 'False' observed only if the column is of System.String
        '           type. When true (default), the whole value must match,
        '           else the value must begin with par_oValue. In case of all
        '           other data types, the value must match. String comparisons
        '           are always case insensitive.
        '       par_lStartRow: row at which to start the search. Default is 1.
        'RETURNS:
        '       Long: Index of the found row (1-based) or -1 if not found.

        Dim sProc As String = "clTable::Seek"
        Dim lResult As Long = -1
        'Dim dr As DataRow
        Dim dr1 As DataRow()
        Dim l As Long
        Dim lTemp As Long

        'Try
        'Abandoned technique - not flexible enough
        'If UCase(par_sColumnName) = UCase(Me.sValueColName) Then
        '    dr = dt.Rows.Find(par_oValue)
        '    If dr Is Nothing Then
        '        lResult = -1
        '    Else
        '        lResult = dt.Rows.IndexOf(dr) + 1
        '    End If
        'Else

        If par_bExact Then
                Select Case dt.Columns(par_sColumnName).DataType.ToString
                    Case "System.String", "System.Char", "System.Byte", "System.DateTime", "System.Guid"
                        dr1 = dt.Select(par_sColumnName & " = '" & par_oValue.ToString & "'", "Index ASC")
                    Case Else   'Numeric
                        dr1 = dt.Select(par_sColumnName & " = " & par_oValue.ToString & "", "Index ASC")
                End Select
            Else
                Select Case dt.Columns(par_sColumnName).DataType.ToString
                    Case "System.String"
                        dr1 = dt.Select(par_sColumnName & " LIKE '" & par_oValue.ToString & "%'", "Index ASC")
                    Case "System.Char", "System.Byte", "System.Guid"
                        dr1 = dt.Select(par_sColumnName & " = '" & par_oValue.ToString & "'", "Index ASC")
                    Case "System.DateTime"
                        dr1 = dt.Select(par_sColumnName & " = '" & par_oValue.ToString & "'", "Index ASC")
                    Case Else   'Numeric
                        dr1 = dt.Select(par_sColumnName & " = " & par_oValue.ToString & "", "Index ASC")
                End Select
            End If
            If dr1 Is Nothing Then
                lResult = -1
            Else
                'GetUpperBound returns -1 when dr length=0.
                If dr1.GetUpperBound(0) < 0 Then
                    lResult = -1
                Else
                    If par_lStartRow <= 1 Then
                        lResult = dt.Rows.IndexOf(dr1(0)) + 1
                    Else
                        'Find a match after par_lStartRow
                        lResult = -1
                        For l = 0 To dr1.GetUpperBound(0)
                            lTemp = dt.Rows.IndexOf(dr1(l))
                            If lTemp >= par_lStartRow - 1 Then
                                lResult = lTemp + 1
                                Exit For
                            End If
                        Next
                    End If
                End If
            End If
        'End If

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return lResult

    End Function
    Function Selected(Optional ByVal par_sTableName As String = "") As Long
        'PURPOSE:
        '       Returns the index of the 'selected' row in the datatable (1-based index).

        Dim sProc As String = "clTable::Selected"
        Dim lResult As Long = 0

        'Try
        lResult = lSelectedRow
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return lResult

    End Function
    Function SelectPlus(ByVal par_sTableName As String, ByVal par_lRow As Long) As Boolean
        'PURPOSE:
        '       Set the selected row in the datatable (1-based index).
        '       If par_lRow is greater than no of rows in datatable,
        '       the selected row remains unchanged. The name of this method
        '       is kept for backward compatibility with NGP. 
        'PARAMETERS:
        '       par_sTable: Ignored.
        '       par_lRow: Row to select (1-based index). If negative,
        '           no row is selected (lSelectedRow is -1).
        'RETURNS:
        '       Boolean: If par_lRow is > datatable count (adjusted for
        '           0-based index), False, else True. If par_lRow
        '           is < 1, it is set to True.

        Dim sProc As String = "clTable::SelectPlus"
        Dim bResult As Boolean = True
        Dim lRow As Long = par_lRow

        'Try
        If lRow > dt.Rows.Count Then
                bResult = False
                'Selection remains unchanged
            Else
                'If negative, make row -1 selected (no row is selected)
                If lRow < 1 Then
                    lRow = -1
                    bResult = False
                End If
                'Change the selected row
                lSelectedRow = lRow
            End If
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return bResult
    End Function
    Function SetVal(ByVal par_sColumnName As String, _
                    ByVal par_lRow As Long, _
                    ByVal par_oValue As Object) As Boolean
        'PURPOSE:
        '       Sets a value to a particular cell (row/column intersection) without
        '       making that row 'selected'. In NGP,
        '       the equivalent syntax is COL_COLUMNNAME_TABLENAME[index] = <value>.
        '       If setting a value to the 'Value' column, you must make sure it is
        '       unique, otherwise an exception will be raised. If a duplicate value
        '       error gets raised, the Value column for sSelectedRow will be blank.
        '       This will cause an error when performing .Add or .Insert. 
        '       Do not try to set a value to the "Index" column because it will be
        '       replaced when the column is automatically regenerated.
        'RETURNS:
        '       Boolean: True when successful, False if not.
        'PARAMETERS:
        '       par_sColumnName: Name of the column
        '       par_lRow: index of the row in which to set the value (1-based index).
        '           If < 1, the value is set in the currently selected row. If no
        '           row is selected, the value is not set and False is returned.
        '       par_oValue: Value to set. Must match or convert to the column's data type.

        Dim sProc As String = "clTable::SetVal"

        Dim bResult As Boolean = True
        Dim lRow As Long = par_lRow

        'Try
        If lRow < 1 Then
                'Use selected row
                lRow = Me.lSelectedRow
                If lRow < 1 Then
                    Return False
                End If
            End If
            If lRow > dt.Rows.Count Then
                goErr.SetError(35000, sProc, "lRow exceeds Rows.Count of the DataTable.")
                bResult = False
            End If
            If dt.Rows.Count < 1 Then
                'The table is empty
                Return False
            End If
            dt.Rows(lRow - 1).Item(par_sColumnName) = par_oValue
            'Catch ex As Exception
            '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '        goErr.SetError(ex, 45105, sProc)
            '    End If
            'End Try

            Return bResult

    End Function
    Function Sort(ByVal par_sColumnList As String) As Boolean
        'PURPOSE:
        '       Sort on one or more columns, asc or desc per column, not case sensitive.
        '       Selects the row that was selected prior to sorting.
        'PARAMETERS:
        '       par_sColumnList: SQL-syntax ORDER BY list of columns. Example:
        '               Text ASC, Type DESC, Value ASC
        '           IMPORTANT: This is different than in NGP, where the syntax was: 
        '               [+|-]Column1[ & vbTab & [+|-]Column2 ...]
        '           where '+' means 'ASC' and '-' means 'DESC'.
        '           "" value is allowed. 

        Dim sProc As String = "clTable::Sort"

        'Try
        Dim oKeyValue As Object = GetSelectedValue()

            'DataSet management is not supported
            'If Not ds Is Nothing Then
            '    ds = New DataSet()
            '    ds.Tables.Add(dt)
            'End If

            'Sort using defaultview       
            Dim dt2 As New DataTable("Sorted")
            ''Example of how to filter DefaultView
            ''Careful: this permanently replaces the global datatable with the filtered set
            'gw.dtAvail.DefaultView.RowFilter = "Field LIKE '<[%]CHK_%'"
            dt.DefaultView.Sort = par_sColumnList
            dt = dt.DefaultView.ToTable()

            'Set primary key
            Dim PrimaryKeyColumns(0) As DataColumn
            PrimaryKeyColumns(0) = dt.Columns(sValueColName)
            dt.PrimaryKey = PrimaryKeyColumns

            ''Dataset, if used, requires that the table be added after the sort.
            'DataSet = New DataSet()
            'If DataSet.Tables.IndexOf(gw.dtAvail) < 0 Then
            '    DataSet.Tables.Add(gw.dtAvail)
            'Else
            '    If DataSet.Tables.CanRemove(gw.dtAvail) Then
            '        DataSet.Tables.Remove(gw.dtAvail)
            '        DataSet.Tables.Add(gw.dtAvail)
            '    Else
            '        'Can't remove table from dataset, raise error?
            '        DataSet.Tables.Add(gw.dtAvail)
            '    End If
            'End If

            'Select the previously selected row
            If Not oKeyValue Is Nothing Then
                Me.lSelectedRow = Seek(sValueColName, oKeyValue)
            Else
                'Select first row
                If Me.Count() > 0 Then
                    Me.lSelectedRow = 1
                End If
            End If

            ''To databind to a grid control
            'DataGrid1.SetDataBinding(DataSet, "Avail")

            'Catch ex As Exception
            '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '        goErr.SetError(ex, 45105, sProc)
            '    End If
            'End Try

            Return True

    End Function

    Function zz_Adjust(ByVal par_s As String) As Boolean
        'Deprecated: in NGP allowed to define columns run-time.
        'Use AddColumn instead.
        'RETURNS:
        '       False.

        Dim sProc As String = "clTable::Adjust"

        Return False

    End Function

    Function zz_Modify(ByVal par_sColumnName As String, ByVal par_oValue As Object) As Boolean
        'PURPOSE:
        '       NOT SUPPORTED currently. Use SetVal() instead.
        '       Modify value of whole row or one cell (by column name).

        Dim sProc As String = "clTable::Modify"

        'Try

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

        Return False

    End Function

End Class