﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Selltis.Core
{
    public class MessageBox
    {
        public bool PNL_MessageBoxVisible { get; set; }
        public bool MessageBoxDisplay { get; set; }
        public bool IsError { get; set; }
        public string sEvent { get; set; }
        public string ErrorMessage { get; set; }
        public int iStyle { get; set; }      
        public string LBL_MsgBoxTitle { get; set; }      
        public string LBL_MsgBoxMessage { get; set; }      
        public bool TXT_MsgBoxInputVisible { get; set; }
        public string TXT_MsgBoxInputText { get; set; }
        public bool TXT_MsgBoxInput_Focus { get; set; }
        public bool MessageBoxRichText { get; set; }      
        public bool TXT_MsgBoxInputRichVisible { get; set; }      
        public string TXT_MsgBoxInputRichContent { get; set; }
      
        //BTN1
        public bool BTN_MsgBox1Visible { get; set; }     
        public string BTN_MsgBox1Text { get; set; }
        public double BTN_MsgBox1Width { get; set; }
        public string BTN_MsgBox1ToolTip { get; set; }
        public string BTN_MsgBox1_onClick { get; set; }
        public bool BTN_MsgBox1_Focus { get; set; }

        //BTN2
        public bool BTN_MsgBox2Visible { get; set; }
        public string BTN_MsgBox2Text { get; set; }
        public double BTN_MsgBox2Width { get; set; }
        public string BTN_MsgBox2ToolTip { get; set; }
        public string BTN_MsgBox2_onClick { get; set; }
        public bool BTN_MsgBox2_Focus { get; set; }


        //BTN3
        public bool BTN_MsgBox3Visible { get; set; }
        public string BTN_MsgBox3Text { get; set; }
        public double BTN_MsgBox3Width { get; set; }
        public string BTN_MsgBox3ToolTip { get; set; }
        public string BTN_MsgBox3_onClick { get; set; }
        public bool BTN_MsgBox3_Focus { get; set; }
    }
    public class MessageBoxPanel
    {
        public bool MessagePanelDisplay { get; set; }
        public string MessagePanelMessage { get; set; }
        public string MessagePanelBackgroundColor { get; set; }
        public string MessagePanelTextColor { get; set; }
        public string MessagePanelImage { get; set; }
    }
}
