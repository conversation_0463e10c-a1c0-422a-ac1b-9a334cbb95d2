﻿CREATE TABLE [dbo].[TD_Involves_CN] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_ToDo_Involves_Contact_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [G<PERSON>_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_CN] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_Involves_CN] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CN_InvolvedIn_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_Involves_CN] FOREIGN KEY ([GID_CN]) REFERENCES [dbo].[CN] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_Involves_CN] NOCHECK CONSTRAINT [LNK_CN_InvolvedIn_TD];


GO
ALTER TABLE [dbo].[TD_Involves_CN] NOCHECK CONSTRAINT [LNK_TD_Involves_CN];


GO
CREATE CLUSTERED INDEX [IX_CN_InvolvedIn_TD]
    ON [dbo].[TD_Involves_CN]([GID_TD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_Involves_CN]
    ON [dbo].[TD_Involves_CN]([GID_CN] ASC);

