﻿CREATE TABLE [dbo].[EX] (
    [GID_ID]               UNIQUEIDENTIFIER CONSTRAINT [DF_EX_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'EX',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]               BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]             NVARCHAR (80)    NULL,
    [CUR_Amount]           MONEY            NULL,
    [CHK_Approved]         TINYINT          NULL,
    [CUR_ApprovedAmount]   MONEY            NULL,
    [TXT_ApprovedBy]       VARCHAR (4)      NULL,
    [DTT_ApprovedDate]     DATETIME         NULL,
    [FIL_Attachments]      NTEXT            NULL,
    [CUR_BillableAmount]   MONEY            NULL,
    [CHK_Billable]         TINYINT          NULL,
    [DTT_BilledDate]       DATETIME         NULL,
    [MMO_BillingNotes]     NTEXT            NULL,
    [TXT_BillingRef]       NVARCHAR (20)    NULL,
    [CUR_CostToOurCo]      MONEY            NULL,
    [DTT_CreationTime]     DATETIME         CONSTRAINT [DF_EX_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [MLS_Currency]         SMALLINT         NULL,
    [CHK_Billed]           TINYINT          NULL,
    [SI__Day]              TINYINT          NULL,
    [CHK_DemoData]         TINYINT          NULL,
    [SR__ExchRate]         REAL             NULL,
    [CHK_Free]             TINYINT          NULL,
    [CUR_MileageRate]      MONEY            NULL,
    [SR__MilesDriven]      REAL             NULL,
    [SI__Month]            TINYINT          NULL,
    [MMO_Notes]            NTEXT            NULL,
    [CHK_Reimbursable]     TINYINT          NULL,
    [CHK_Reimbursed]       TINYINT          NULL,
    [CUR_ReimbursedAmount] MONEY            NULL,
    [TXT_ReimbursedBy]     VARCHAR (4)      NULL,
    [DTT_ReimbursedDate]   DATETIME         NULL,
    [DTT_Time]             DATETIME         NULL,
    [MLS_Type]             SMALLINT         NULL,
    [URL_URLs]             NTEXT            NULL,
    [TXT_Year]             CHAR (4)         NULL,
    [TXT_ModBy]            VARCHAR (4)      NULL,
    [DTT_ModTime]          DATETIME         CONSTRAINT [DF_EX_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [MMO_ImportData]       NTEXT            NULL,
    [SI__ShareState]       TINYINT          CONSTRAINT [DF_EX_SI__ShareState] DEFAULT ((2)) NULL,
    [CHK_Report]           TINYINT          CONSTRAINT [DF_EX_CHK_Report] DEFAULT ((1)) NULL,
    [GID_CreatedBy_US]     UNIQUEIDENTIFIER NULL,
    [GID_Bill_CO]          UNIQUEIDENTIFIER NULL,
    [GID_For_US]           UNIQUEIDENTIFIER NULL,
    [GID_For_LO]           UNIQUEIDENTIFIER NULL,
    [GID_Related_EA]       UNIQUEIDENTIFIER NULL,
    [GID_Related_EC]       UNIQUEIDENTIFIER NULL,
    [GID_To_VE]            UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]         VARCHAR (50)     NULL,
    [TXT_ExternalID]       NVARCHAR (120)   NULL,
    [TXT_ExternalSource]   VARCHAR (10)     NULL,
    [TXT_ImpJobID]         VARCHAR (20)     NULL,
    [ADR_Attachments]      NTEXT            NULL,
    CONSTRAINT [PK_EX] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_EX_Bill_CO] FOREIGN KEY ([GID_Bill_CO]) REFERENCES [dbo].[CO] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_EX_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_EX_For_LO] FOREIGN KEY ([GID_For_LO]) REFERENCES [dbo].[LO] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_EX_For_US] FOREIGN KEY ([GID_For_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_EX_Related_EA] FOREIGN KEY ([GID_Related_EA]) REFERENCES [dbo].[EA] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_EX_Related_EC] FOREIGN KEY ([GID_Related_EC]) REFERENCES [dbo].[EC] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_EX_To_VE] FOREIGN KEY ([GID_To_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[EX] NOCHECK CONSTRAINT [LNK_EX_Bill_CO];


GO
ALTER TABLE [dbo].[EX] NOCHECK CONSTRAINT [LNK_EX_CreatedBy_US];


GO
ALTER TABLE [dbo].[EX] NOCHECK CONSTRAINT [LNK_EX_For_LO];


GO
ALTER TABLE [dbo].[EX] NOCHECK CONSTRAINT [LNK_EX_For_US];


GO
ALTER TABLE [dbo].[EX] NOCHECK CONSTRAINT [LNK_EX_Related_EA];


GO
ALTER TABLE [dbo].[EX] NOCHECK CONSTRAINT [LNK_EX_Related_EC];


GO
ALTER TABLE [dbo].[EX] NOCHECK CONSTRAINT [LNK_EX_To_VE];


GO
CREATE NONCLUSTERED INDEX [IX_EX_Related_EA]
    ON [dbo].[EX]([GID_Related_EA] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_For_LO]
    ON [dbo].[EX]([GID_For_LO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_CreatedBy_US]
    ON [dbo].[EX]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_Name]
    ON [dbo].[EX]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_TXT_ImportID]
    ON [dbo].[EX]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_Bill_CO]
    ON [dbo].[EX]([GID_Bill_CO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_ModDateTime]
    ON [dbo].[EX]([DTT_ModTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_EX_BI__ID]
    ON [dbo].[EX]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_To_VE]
    ON [dbo].[EX]([GID_To_VE] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_DateTime]
    ON [dbo].[EX]([DTT_Time] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_Related_EC]
    ON [dbo].[EX]([GID_Related_EC] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_For_US]
    ON [dbo].[EX]([GID_For_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_CreationTime]
    ON [dbo].[EX]([DTT_CreationTime] ASC);


GO
CREATE TRIGGER trEXUpdateTN
ON [EX]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in EX table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(120)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'EX'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable
					and tn2.GID_InternalID = inserted.GID_ID)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [EX]
			SET [EX].TXT_ExternalSource = '', [EX].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [EX].GID_ID = in1.GID_ID
				and ISNULL([EX].TXT_ExternalSource, '') <> ''
				and ISNULL([EX].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trEXUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trEXUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trEXUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!