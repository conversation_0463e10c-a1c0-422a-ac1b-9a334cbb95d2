﻿CREATE TABLE [dbo].[R4] (
    [GID_ID]                      UNIQUEIDENTIFIER CONSTRAINT [DF_R4_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),(0))+CONVERT([binary](2),'R4',(0)))+CONVERT([binary](6),getutcdate(),(0)),(0))) NOT NULL,
    [BI__ID]                      BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                    NVARCHAR (80)    NULL,
    [DTT_CreationTime]            DATETIME         CONSTRAINT [DF_R4_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]                TINYINT          NULL,
    [TXT_ModBy]                   VARCHAR (4)      NULL,
    [DTT_ModTime]                 DATETIME         CONSTRAINT [DF_R4_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_OSRDashboardDataName]    NVARCHAR (50)    NULL,
    [MMO_ImportData]              NTEXT            NULL,
    [SI__ShareState]              TINYINT          CONSTRAINT [DF_R4_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]            UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]                VARCHAR (50)     NULL,
    [TXT_ExternalID]              NVARCHAR (80)    NULL,
    [TXT_ExternalSource]          VARCHAR (10)     NULL,
    [TXT_ImpJobID]                VARCHAR (20)     NULL,
    [GID_USERID]                  UNIQUEIDENTIFIER NULL,
    [DTT_LASTREFRESHEDDATE]       DATETIME         NULL,
    [GID_RefreshedBy]             UNIQUEIDENTIFIER NULL,
    [SI__NewAccounts]             TINYINT          NULL,
    [SI__TeamSellAccounts]        TINYINT          NULL,
    [SI__OverservedAccounts]      TINYINT          NULL,
    [SI__UnderservedAccounts]     TINYINT          NULL,
    [SI__OverduePrimaryAccounts]  TINYINT          NULL,
    [SI__NewContacts]             TINYINT          NULL,
    [SI__KeyContacts]             TINYINT          NULL,
    [SI__OverdueContacts]         TINYINT          NULL,
    [SI__TasksDue10days]          TINYINT          NULL,
    [SI__OverdueTasks]            TINYINT          NULL,
    [SI__TeamSellAC]              TINYINT          NULL,
    [SI__ThisMonthSalesVisits]    TINYINT          NULL,
    [SI__LastMonthSalesVisits]    TINYINT          NULL,
    [SI__ThisMonthOthAC]          TINYINT          NULL,
    [SI__LastMonthOthAC]          TINYINT          NULL,
    [SI__MyLeads]                 TINYINT          NULL,
    [SI__TeamSellLeads]           TINYINT          NULL,
    [SI__NewLeads]                TINYINT          NULL,
    [SI__OverdueLeads]            TINYINT          NULL,
    [CUR_MyLeadsExpVal]           MONEY            NULL,
    [CUR_TeamSellLeadsExpVal]     MONEY            NULL,
    [CUR_NewLeadsExpVal]          MONEY            NULL,
    [CUR_OverdueLeadsExpVal]      MONEY            NULL,
    [CUR_MyLeadsWghVal]           MONEY            NULL,
    [CUR_TeamSellLeadsWghVal]     MONEY            NULL,
    [CUR_NewLeadsWghVal]          MONEY            NULL,
    [CUR_OverdueLeadsWghVal]      MONEY            NULL,
    [SI__OpenOpps]                TINYINT          NULL,
    [SI__TeamSellOpps]            TINYINT          NULL,
    [SI__DueNext30DaysOpps]       TINYINT          NULL,
    [SI__OverdueOpps]             TINYINT          NULL,
    [CUR_OpenOppsExpVal]          MONEY            NULL,
    [CUR_TeamSellOppsExpVal]      MONEY            NULL,
    [CUR_DueNext30DaysOppsExpVal] MONEY            NULL,
    [CUR_OverdueOppsExpVal]       MONEY            NULL,
    [CUR_OpenOppsWghVal]          MONEY            NULL,
    [CUR_TeamSellOppsWghVal]      MONEY            NULL,
    [CUR_DueNext30DaysOppsWghVal] MONEY            NULL,
    [CUR_OverdueOppsWghVal]       MONEY            NULL,
    [SI__WonOpps]                 TINYINT          NULL,
    [SI__LostOpps]                TINYINT          NULL,
    [SI__CancelledOpps]           TINYINT          NULL,
    [CUR_WonOpps]                 MONEY            NULL,
    [CUR_LostOpps]                MONEY            NULL,
    [CUR_CancelledOpps]           MONEY            NULL,
    [SR__WonOppsRate]             REAL             NULL,
    [SR__LostOppsRate]            REAL             NULL,
    [SR__CancelledOppsRate]       REAL             NULL,
    [SI__OpenRFQs]                TINYINT          NULL,
    [SI__OpenSQs]                 TINYINT          NULL,
    [SI__NewSQs]                  TINYINT          NULL,
    [SI__OverdueSQs]              TINYINT          NULL,
    [SI__SQWon]                   TINYINT          NULL,
    [SI__SQLost]                  TINYINT          NULL,
    [SI__SQCancelled]             TINYINT          NULL,
    [CUR_OpenRFQs]                MONEY            NULL,
    [CUR_OpenSQs]                 MONEY            NULL,
    [CUR_NewSQs]                  MONEY            NULL,
    [CUR_OverdueSQs]              MONEY            NULL,
    [CUR_SQWon]                   MONEY            NULL,
    [CUR_SQLost]                  MONEY            NULL,
    [CUR_SQCancelled]             MONEY            NULL,
    [SR__OpenRFQsMargin]          REAL             NULL,
    [SR__OpenSQsMargin]           REAL             NULL,
    [SR__NewSQsMargin]            REAL             NULL,
    [SR__OverdueSQsMargin]        REAL             NULL,
    [SR__SQWonRate]               REAL             NULL,
    [SR__SQLostRate]              REAL             NULL,
    [SR__SQCancelledRate]         REAL             NULL,
    [CUR_QUOTEPIPELINE]           MONEY            NULL,
    [SI__TaskCount]               TINYINT          NULL,
    CONSTRAINT [PK_R4] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);

