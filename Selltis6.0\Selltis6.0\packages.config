﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net461" />
  <package id="bootstrap" version="3.0.0" targetFramework="net451" />
  <package id="elmah" version="1.2.2" targetFramework="net451" />
  <package id="elmah.corelibrary" version="1.2.2" targetFramework="net451" />
  <package id="elmah.sqlserver" version="1.2" targetFramework="net451" />
  <package id="EntityFramework" version="6.0.0" targetFramework="net451" />
  <package id="jQuery" version="1.10.2" targetFramework="net451" />
  <package id="jQuery.Validation" version="1.11.1" targetFramework="net451" />
  <package id="Microsoft.AspNet.Identity.Core" version="1.0.0" targetFramework="net451" />
  <package id="Microsoft.AspNet.Identity.EntityFramework" version="1.0.0" targetFramework="net451" />
  <package id="Microsoft.AspNet.Identity.Owin" version="1.0.0" targetFramework="net451" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.9" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor" version="3.2.9" targetFramework="net461" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.9" targetFramework="net461" />
  <package id="Microsoft.Identity.Client" version="4.48.1" targetFramework="net461" />
  <package id="Microsoft.IdentityModel.Abstractions" version="6.22.0" targetFramework="net461" requireReinstallation="true" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="6.8.0" targetFramework="net461" />
  <package id="Microsoft.IdentityModel.Logging" version="6.8.0" targetFramework="net461" />
  <package id="Microsoft.IdentityModel.Protocols" version="6.8.0" targetFramework="net461" />
  <package id="Microsoft.IdentityModel.Protocols.OpenIdConnect" version="6.8.0" targetFramework="net461" />
  <package id="Microsoft.IdentityModel.Tokens" version="6.8.0" targetFramework="net461" />
  <package id="Microsoft.jQuery.Unobtrusive.Validation" version="3.0.0" targetFramework="net451" />
  <package id="Microsoft.Owin" version="4.2.2" targetFramework="net461" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="4.1.1" targetFramework="net461" />
  <package id="Microsoft.Owin.Security" version="4.2.2" targetFramework="net461" />
  <package id="Microsoft.Owin.Security.Cookies" version="4.1.1" targetFramework="net461" />
  <package id="Microsoft.Owin.Security.Facebook" version="2.0.0" targetFramework="net451" />
  <package id="Microsoft.Owin.Security.Google" version="2.0.0" targetFramework="net451" />
  <package id="Microsoft.Owin.Security.MicrosoftAccount" version="2.0.0" targetFramework="net451" />
  <package id="Microsoft.Owin.Security.OAuth" version="4.2.2" targetFramework="net461" />
  <package id="Microsoft.Owin.Security.OpenIdConnect" version="4.1.1" targetFramework="net461" />
  <package id="Microsoft.Owin.Security.Twitter" version="2.0.0" targetFramework="net451" />
  <package id="Microsoft.PowerBI.Api" version="4.11.0" targetFramework="net461" requireReinstallation="true" />
  <package id="Microsoft.Rest.ClientRuntime" version="2.3.21" targetFramework="net461" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net461" />
  <package id="Modernizr" version="2.6.2" targetFramework="net451" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net461" />
  <package id="Owin" version="1.0" targetFramework="net451" />
  <package id="Respond" version="1.2.0" targetFramework="net451" />
  <package id="System.IdentityModel.Tokens.Jwt" version="6.8.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.Cng" version="4.7.0" targetFramework="net461" requireReinstallation="true" />
  <package id="WebGrease" version="1.5.2" targetFramework="net451" />
</packages>