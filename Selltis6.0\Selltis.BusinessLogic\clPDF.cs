﻿using System;
using System.Web;
using ExpertPdf.HtmlToPdf;

namespace Selltis.BusinessLogic
{
	public class clPDF
	{

		private clError goErr;
		private clLog goLog;
		//Dim goUI As clUI
		private clProject goP;
		private clData goData;
		private clMetaData goMeta;
		private clTransform goTR;

		public clPDF()
		{
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			//goUI = HttpContext.Current.Session("goUI")

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTR"];
		}

		public bool HTMLToPDF(string par_sFilename, string par_sHTML, string par_sPageSize = "LETTER", string par_sOrientation = "LANDSCAPE", bool par_bShowHeader = false, string par_sHeaderText = "", string par_sHeaderTextAlignment = "RIGHT", int par_iLeftMargin = 35, int par_iRightMargin = 35, int par_iTopMargin = 35, int par_iBottomMargin = 35, int par_iHeaderHeight = 30, int par_iFooterHeight = 35, string par_sHeaderFontName = "Verdana", string par_sFooterFontName = "Verdana", int par_iHeaderFontSize = 9, int par_iFooterFontSize = 9, int par_iPageWidth = 0)
		{
			//AUTHOR: WT
			//MI 9/23/09 Added page sizes, made other mods.
			//PARAMETERS:
			//       par_sPageSize = page size. Supported: A4, LEGAL, LETTER. Unsupported sizes are processed as LETTER.
			//       par_sFilename = name of file, including .pdf extension
			//       par_sHTML = complete HTML to convert
			//       par_sOrientation = can be LANDSCAPE or PORTRAIT
			//       par_bShowHeader = header on, True.  header off, False.
			//       par_sHeaderText = header text
			//       par_sHeaderAlignment = header text alignment, possible values are CENTER, LEFT, RIGHT
			//       par_iLeftMargin = 50
			//       par_iRightMargin = 50
			//       par_iTopMargin = 50
			//       par_iBottomMargin = 50

			string sProc = "clPDF::HTMLToPDF";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			try
			{
				ExpertPdf.HtmlToPdf.PdfConverter PdfConverter = new ExpertPdf.HtmlToPdf.PdfConverter();

				//goLog.Log(sProc, "62", clC.SELL_LOGLEVEL_DEBUG, True, True)

				switch (par_sPageSize.ToUpper())
				{
					case "A4":
						PdfConverter.PdfDocumentOptions.PdfPageSize = PdfPageSize.A4;
						break;
					case "LEGAL":
						PdfConverter.PdfDocumentOptions.PdfPageSize = PdfPageSize.Legal;
						break;
					default:
						//LETTER and other unsupported sizes
						PdfConverter.PdfDocumentOptions.PdfPageSize = PdfPageSize.Letter;
						break;
				}

				//goLog.Log(sProc, "74", clC.SELL_LOGLEVEL_DEBUG, True, True)

				switch (par_sOrientation)
				{
					case "LANDSCAPE":
						PdfConverter.PdfDocumentOptions.PdfPageOrientation = PDFPageOrientation.Landscape;
						break;
					default:
						//PORTRAIT
						PdfConverter.PdfDocumentOptions.PdfPageOrientation = PDFPageOrientation.Portrait;
						break;
				}

				//goLog.Log(sProc, "84", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfDocumentOptions.PdfCompressionLevel = PdfCompressionLevel.Normal;

				//goLog.Log(sProc, "88", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfDocumentOptions.LeftMargin = par_iLeftMargin;

				//goLog.Log(sProc, "92", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfDocumentOptions.RightMargin = par_iRightMargin;

				//goLog.Log(sProc, "96", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfDocumentOptions.TopMargin = par_iTopMargin;

				//goLog.Log(sProc, "100", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfDocumentOptions.BottomMargin = par_iBottomMargin;

				//goLog.Log(sProc, "104", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfDocumentOptions.GenerateSelectablePdf = true;

				//goLog.Log(sProc, "108", clC.SELL_LOGLEVEL_DEBUG, True, True)

				//MI 9/24/09 Per WT: FitWidth won't work until you know the width of the desktops largest table.
				//then you set pdfconverter.PageWidth  to that number, then the html would fit.
				PdfConverter.PdfDocumentOptions.FitWidth = true;

				PdfConverter.PageWidth = par_iPageWidth;

				//goLog.Log(sProc, "114", clC.SELL_LOGLEVEL_DEBUG, True, True)

				//PdfConverter.PdfDocumentOptions.FitHeight = True

				PdfConverter.AvoidImageBreak = true;

				//goLog.Log(sProc, "120", clC.SELL_LOGLEVEL_DEBUG, True, True)

				//PdfConverter.AvoidTextBreak = True

				//header
				PdfConverter.PdfDocumentOptions.ShowHeader = par_bShowHeader;

				//goLog.Log(sProc, "127", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfHeaderOptions.DrawHeaderLine = false;

				//goLog.Log(sProc, "131", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfHeaderOptions.HeaderText = par_sHeaderText;

				//goLog.Log(sProc, "135", clC.SELL_LOGLEVEL_DEBUG, True, True)

				switch (par_sHeaderTextAlignment)
				{
					case "LEFT":
						PdfConverter.PdfHeaderOptions.HeaderTextAlign = HorizontalTextAlign.Left;
						break;
					case "RIGHT":
						PdfConverter.PdfHeaderOptions.HeaderTextAlign = HorizontalTextAlign.Right;
						break;
					case "CENTER":
						PdfConverter.PdfHeaderOptions.HeaderTextAlign = HorizontalTextAlign.Center;
						break;
					default:
						PdfConverter.PdfHeaderOptions.HeaderTextAlign = HorizontalTextAlign.Left;
						break;
				}

				//goLog.Log(sProc, "148", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfHeaderOptions.HeaderHeight = par_iHeaderHeight;

				//goLog.Log(sProc, "152", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfHeaderOptions.HeaderTextFontName = par_sHeaderFontName;

				//goLog.Log(sProc, "156", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfHeaderOptions.HeaderTextFontSize = par_iHeaderFontSize;

				//goLog.Log(sProc, "160", clC.SELL_LOGLEVEL_DEBUG, True, True)


				//footer
				PdfConverter.PdfDocumentOptions.ShowFooter = true;

				//goLog.Log(sProc, "166", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfFooterOptions.DrawFooterLine = false;

				//goLog.Log(sProc, "170", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfFooterOptions.PageNumberingFormatString = "Page &p; of &P;";

				//goLog.Log(sProc, "174", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfFooterOptions.ShowPageNumber = true;

				//goLog.Log(sProc, "178", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfFooterOptions.FooterHeight = par_iFooterHeight;

				//goLog.Log(sProc, "182", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfFooterOptions.FooterTextFontName = par_sFooterFontName;

				//goLog.Log(sProc, "186", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfFooterOptions.FooterTextFontSize = par_iFooterFontSize;

				//goLog.Log(sProc, "190", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfFooterOptions.PageNumberTextFontName = par_sFooterFontName;

				//goLog.Log(sProc, "194", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.PdfFooterOptions.PageNumberTextFontSize = par_iFooterFontSize;

				//goLog.Log(sProc, "198", clC.SELL_LOGLEVEL_DEBUG, True, True)

				string sPath = HttpContext.Current.Server.MapPath("~/Temp/");

				//goLog.Log(sProc, "202", clC.SELL_LOGLEVEL_DEBUG, True, True)

				PdfConverter.LicenseKey = "CSI7KTEpODo/KT8nOSk6OCc4OycwMDAw"; //"3fbs/eX97+Xs/ejz7f3u7PPs7/Pk5OTk"

				//goLog.Log(sProc, "206", clC.SELL_LOGLEVEL_DEBUG, True, True)

				byte[] downloadBytes = PdfConverter.GetPdfBytesFromHtmlString(par_sHTML);

				//goLog.Log(sProc, "210", clC.SELL_LOGLEVEL_DEBUG, True, True)

				//let's test for directory
				if (System.IO.Directory.Exists(HttpContext.Current.Server.MapPath("~\\Temp\\")))
				{
					//do nothing
				}
				else
				{
					try
					{
						//try to create directory
						System.IO.Directory.CreateDirectory(HttpContext.Current.Server.MapPath("..") + "\\Temp");
					}
					catch (Exception ex)
					{
						//goErr.SetError(ex, 45105, sProc)
						return Convert.ToBoolean("");
					}
				}

				//goLog.Log(sProc, "225", clC.SELL_LOGLEVEL_DEBUG, True, True)

				// Delete the file if it exists.
				if (System.IO.File.Exists(sPath + par_sFilename))
				{
					System.IO.File.Delete(sPath + par_sFilename);
				}

				//goLog.Log(sProc, "232", clC.SELL_LOGLEVEL_DEBUG, True, True)

				//create the new file
				System.IO.FileStream fs = System.IO.File.Create(sPath + par_sFilename);

				//goLog.Log(sProc, "237", clC.SELL_LOGLEVEL_DEBUG, True, True)

				fs.Write(downloadBytes, 0, downloadBytes.Length);

				//goLog.Log(sProc, "241", clC.SELL_LOGLEVEL_DEBUG, True, True)

				fs.Close();

				//goLog.Log(sProc, "245", clC.SELL_LOGLEVEL_DEBUG, True, True)

				return true;

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return false;

			}
		}

	}

}
