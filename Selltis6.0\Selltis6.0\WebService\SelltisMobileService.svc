﻿<%@ ServiceHost Language="VB" Debug="true" Service="SelltisMobileService" %>

'Author: V_T

Imports System.IO
Imports System.Collections.Generic
Imports System.Data
Imports System.Linq
Imports System.Runtime.Serialization
Imports System.ServiceModel
Imports System.ServiceModel.Web
Imports System.ServiceModel.Configuration
Imports System.Text
Imports System.Configuration
Imports System.ServiceModel.Activation
Imports ImapUtility
Imports System.Reflection
Imports Newtonsoft.Json
Imports Newtonsoft.Json.Converters
Imports System.Device.Location
Imports Selltis.BusinessLogic
Imports System.Diagnostics

<AspNetCompatibilityRequirements(RequirementsMode:=AspNetCompatibilityRequirementsMode.Allowed)>
Public Class SelltisMobileService
    Implements ISelltisMobileService

#Region "Web Methods"

    Private sFailMesage As String = "You must upgrade your mobile application"
    Private sFailMesage1 As String = "Mobile application metadata must be updated"
    Private sMPTableName As String = "XM"
    Private sMFTableName As String = "XF"


    Private gbHasSession As Boolean = False
    Private gbHasWOP As Boolean = False
    Private gsWOP As String = ""
    Private gItem_downloaded As String = ""
    Private gbIsCurrentPagePostback As Boolean = False
    Private gcErrors As New Collection

    Private RoutingUserEmailID As String = ""
    Private goP As clProject
    Private goMeta As clMetaData
    Private goTR As clTransform
    Private goData As clData
    Private goLog As clLog

    Public Sub DoWork() Implements ISelltisMobileService.DoWork
    End Sub



    Private Function MPRP() As MobileProfilePage ''userName As String, password As String,


        Dim _mobileprofilepage As New MobileProfilePage()
        Try
            'Dim sb As Boolean


            ''sb = Membership.ValidateUser(userName, password)
            'Dim _memberShip As New clSelltisMembershipProviderNew()
            'sb = _memberShip.ValidateUser(userName, password)

            'If sb = False Then
            '    _mobileprofilepage.Status = "Fail"
            '    _mobileprofilepage.ErrorMessage = "Invalid Username or Password"
            '    Return _mobileprofilepage
            'End If

            'Dim Init As New clInit

            Dim goP As clProject = HttpContext.Current.Session("goP")
            ''goP.sRunMode = "Mobileservice"

            ''get mobile profile page data

            Dim goMeta As clMetaData = HttpContext.Current.Session("goMeta")
            Dim goTR As clTransform = HttpContext.Current.Session("goTr")

            Dim pageId As String = goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "MOBILE_LANDINGPAGE")

            Dim sProfilePageMD As String = goMeta.PageRead("GLOBAL", pageId)
            Dim Title As String = goTR.StrRead(sProfilePageMD, "Title", "")
            Dim ViewCount As Integer = Convert.ToInt32(goTR.StrRead(sProfilePageMD, "ViewCount", "0"))

            _mobileprofilepage.Title = Title
            _mobileprofilepage.ViewCount = ViewCount
            _mobileprofilepage.ProfileViews = New List(Of MobileProfileView)

            For i = 1 To ViewCount

                Dim _mobileprofileview As New MobileProfileView()

                Dim sViewId As String = goTR.StrRead(sProfilePageMD, "View" & i.ToString() & "Id", "")
                Dim sViewMD As String = goMeta.PageRead("Global", sViewId, "", True)
                Dim sViewType As String = goTR.StrRead(sViewMD, "ViewType", "")
                Dim Condition As String = goTR.StrRead(sViewMD, "Condition", "")

                Dim sFile As String = goTR.StrRead(sViewMD, "FILE", "")
                Dim NavigatorId As String = goTR.StrRead(sViewMD, "NavigateDekstopId", "")
                Dim ViewTitle As String = goTR.StrRead(sViewMD, "Title", "")
                Dim Icon As String = goTR.StrRead(sViewMD, "Icon", "entypo-chart-bar")
                Dim BgColor As String = goTR.StrRead(sViewMD, "BgColor", "#373E4A")
                Dim GroupTotal As String = "0"


                _mobileprofileview.AddNewLink = 0
                _mobileprofileview.BgColor = BgColor
                _mobileprofileview.Icon = Icon
                _mobileprofileview.NavigateToPageId = NavigatorId
                _mobileprofileview.ViewId = sViewId
                _mobileprofileview.ViewType = sViewType
                _mobileprofileview.ViewTitle = ViewTitle
                _mobileprofileview.FileName = sFile
                _mobileprofileview.Condition = Condition

                If sViewType.ToUpper() = "TILE" Then

                    Dim GroupByField As String = goTR.StrRead(sViewMD, "GroupBy", "")
                    Dim AggregateType As String = goTR.StrRead(sViewMD, "AggregateType", "")
                    Dim AggregateField As String = goTR.StrRead(sViewMD, "AgField", "")
                    AggregateField = AggregateField + "|" + AggregateType.ToUpper()

                    _mobileprofileview.Sort = GroupByField + " asc"


                    Dim clrowset As New clRowSet(sFile, clC.SELL_GROUPBYWITHROLLUP, Condition, GroupByField, AggregateField)
                    clrowset.ToTable()
                    Dim dtData As DataTable = clrowset.dtTransTable

                    For Each _row As DataRow In dtData.Rows

                        Dim iGroupTotal As Integer = Convert.ToInt32(_row("INT_" & GroupByField & "_Grouping"))

                        If iGroupTotal = 1 Then
                            GroupTotal = Convert.ToString(_row(AggregateField))
                            If String.IsNullOrEmpty(GroupTotal) Then
                                GroupTotal = "0"
                            End If
                            Exit For
                        End If

                    Next


                    _mobileprofileview.GroupName = ViewTitle
                    _mobileprofileview.GroupValue = GroupTotal


                ElseIf sViewType.ToUpper() = "MV" Then

                    Dim SortField As String = goTR.StrRead(sViewMD, "SortField", "BI__ID desc")
                    Dim Row1Fields As String = goTR.StrRead(sViewMD, "Row1", "")
                    Dim Row2Fields As String = goTR.StrRead(sViewMD, "Row2", "")
                    Dim sFields As String = "Gid_id," & Row1Fields.Replace("|", ",")

                    _mobileprofileview.Sort = SortField

                    Dim rsData As clRowSet = New clRowSet(sFile, clC.SELL_READONLY, Condition, SortField, sFields, 5)

                    _mobileprofileview.RowsData = New List(Of RowData)

                    For index = 1 To rsData.Count

                        Dim _rowdata As New RowData()

                        If Not String.IsNullOrEmpty(Row1Fields) Then

                            Dim strfields = Row1Fields.Split("|"c)

                            ''_rowdata.GidId = rsData.GetFieldVal("GID_ID")

                            For Each _field As String In strfields
                                If String.IsNullOrEmpty(_rowdata.Row1Data) Then
                                    _rowdata.Row1Data = rsData.GetFieldVal(_field.Trim())
                                Else
                                    _rowdata.Row1Data = _rowdata.Row1Data & " | " & rsData.GetFieldVal(_field.Trim())
                                End If
                            Next

                        End If

                        If Not String.IsNullOrEmpty(Row2Fields) Then

                            Dim strfields2 = Row2Fields.Split("|"c)

                            For Each _field2 As String In strfields2
                                If String.IsNullOrEmpty(_rowdata.Row2Data) Then
                                    _rowdata.Row2Data = rsData.GetFieldVal(_field2.Trim())
                                Else
                                    _rowdata.Row2Data = _rowdata.Row2Data & " | " & rsData.GetFieldVal(_field2.Trim())
                                End If
                            Next

                        End If

                        _rowdata.GidId = rsData.GetFieldVal("GID_ID")

                        _mobileprofileview.RowsData.Add(_rowdata)

                        If (rsData.GetNext() <> 1) Then
                            Exit For
                        End If

                    Next

                End If

                _mobileprofilepage.ProfileViews.Add(_mobileprofileview)

            Next

            Logoff()

            _mobileprofilepage.Status = "Success"

            Return _mobileprofilepage

        Catch ex As Exception
            _mobileprofilepage.Status = "Fail"
            _mobileprofilepage.ErrorMessage = ex.ToString()
            Return _mobileprofilepage
        End Try

    End Function

    <WebInvoke(Method:="GET", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="Login?sUser={userName}&sPassword={password}&sAppversion={App_Version}&sAppdeviceName={App_DeviceName}&sMD_Version={MD_Version}")>
    Public Function Login(userName As String, password As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput Implements ISelltisMobileService.Login

        Dim objclServiceOutput As New clServiceOutput()

        Try
            Dim sb As Boolean

            'sb = Membership.ValidateUser(userName, password)
            Dim _memberShip As New clSelltisMembershipProviderNew()
            If IsSSOEnabled() = "true" Then
                sb = _memberShip.ValidateUser_with_EmailId(password, userName)
            Else
                sb = _memberShip.ValidateUser(userName, password)
            End If

            If sb = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "Invalid Username or Password"
                Return objclServiceOutput
            End If

            Dim Init As New clInit

            Dim goP As clProject = HttpContext.Current.Session("goP")
            goP.sRunMode = "Mobileservice"

            'check MD Version
            If String.IsNullOrEmpty(MD_Version) = False Then

                Dim sMobileServiceVersion As String = goP.GetVersion("MobileService")
                If sMobileServiceVersion.ToLower().Equals(MD_Version.ToLower()) = False Then
                    objclServiceOutput.Status = "Fail"
                    objclServiceOutput.Data = sFailMesage
                    Return objclServiceOutput
                End If

            End If

            Dim sCurr_MD_Version As String = GetCurrentMDVersion()

            'MS 5/12 commented by Manmeet, dont by pass Validation
            'goP.SetVar("bBypassValidation", True)
            'goP.SetVar("bNoRecordOnSave", False)

            Dim Returnstring As String
            Dim sName As String = goP.GetMe("Name")
            Dim sCode As String = goP.GetMe("Code")
            Dim sUsername As String = goP.GetMe("LOGONNAME")

            'XL Log
            Dim sMessage As String = sName & " logging in as: '" & userName & "', Device '" & App_DeviceName & "', AppVersion '" & App_Version & "'."
            goLog = HttpContext.Current.Session("goLog")
            goLog.Log("SelltisMobileService:Login", sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)

            Dim UserFCMTokenIDAndDeviceName As String = "##"
            Dim FCMTokenId As String = ""
            Dim FCMTokenDevice As String = ""
            Dim godata As clData = DirectCast(HttpContext.Current.Session("goData"), clData)
            If godata.IsFieldValid("US", "TXT_FCMToken") And godata.IsFieldValid("US", "TXT_FCMTokenDevice") Then
                UserFCMTokenIDAndDeviceName = _memberShip.getFCMTokenIdDevice(HttpContext.Current.Session("USERID"), FCMTokenId, FCMTokenDevice)
            End If

            Dim bEnabledTwoFactorAuth As Boolean = False
            Dim sAuthMessage As String = ""

            _memberShip.TwoFactorAuthentication(bEnabledTwoFactorAuth, sAuthMessage)

            Dim goMeta As clMetaData = HttpContext.Current.Session("goMeta")
            Dim sAutoCRLACSave As String = "False"
            If goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "ENABLE_AUTOSAVE_CRLAC_EMAILSENT_CALLPLACED") = "1" Then
                sAutoCRLACSave = "True"
            End If

            Returnstring = HttpContext.Current.Session("USERID") & "#" & sName & "#" & sCode & "#" & sUsername & "#" & sCurr_MD_Version & UserFCMTokenIDAndDeviceName & "#" & bEnabledTwoFactorAuth & "#" & sAuthMessage & "#" & sAutoCRLACSave

            Logoff()

            objclServiceOutput.Status = "Success"
            objclServiceOutput.Data = Returnstring 'sb.ToString()

            Return objclServiceOutput

        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try

    End Function


    <WebInvoke(Method:="GET", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="GetDataPreAdd?sUser={userName}&sPassword={password}&filename={filename}&field={field}&createType={createType}&createFrom={createFrom}&sAppversion={App_Version}&sAppdeviceName={App_DeviceName}&sMD_Version={MD_Version}")>
    Public Function GetDataPreAdd(userName As String, password As String, filename As String, field As String,
                               createType As String, createFrom As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput Implements ISelltisMobileService.GetDataPreAdd

        Dim sw As New Stopwatch()
        Dim objclServiceOutput As New clServiceOutput()

        Try
            'clCache.WriteLog("Request in")
            sw.Start()

            userName = HttpUtility.UrlDecode(userName)
            password = HttpUtility.UrlDecode(password)
            filename = HttpUtility.UrlDecode(filename)
            field = HttpUtility.UrlDecode(field)
            createType = HttpUtility.UrlDecode(createType)
            createFrom = HttpUtility.UrlDecode(createFrom)

            Dim sb As Boolean
            'sb = Membership.ValidateUser(userName, password)
            Dim _memberShip As New clSelltisMembershipProviderNew()
            If IsSSOEnabled() = "true" Then
                sb = _memberShip.ValidateUser_with_EmailId(password, userName)
            Else
                sb = _memberShip.ValidateUser(userName, password)
            End If

            'clCache.WriteLog("Membership.ValidateUser after")

            If sb = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "Invalid Username or Password"
                Return objclServiceOutput
            End If

            Dim Init As New clInit
            HttpContext.Current.Session("goP").sRunMode = "Mobileservice"

            'check MD version
            If ValidateMDVersion(MD_Version) = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = sFailMesage1
                Return objclServiceOutput
            End If

            'clCache.WriteLog("Session init done")

            Dim rs As New clRowSet(filename, clC.SELL_ADD, , , , , , , createType, createFrom, , True) 'Insert

            rs.ToTable()

            If rs.GetFirst = 1 Then

                Dim rows As New List(Of Dictionary(Of String, Object))()
                Dim row As Dictionary(Of String, Object)
                Dim serializer As New System.Web.Script.Serialization.JavaScriptSerializer()

                Dim fieldArray() As String = field.Split(",")
                row = New Dictionary(Of String, Object)()
                For Each f In fieldArray
                    row.Add(f, rs.GetFieldVal(f))
                Next
                rows.Add(row)

                objclServiceOutput.Status = "Success"
                objclServiceOutput.Data = serializer.Serialize(rows)

            Else
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "No Records Found"
            End If

            Logoff()

            ' clCache.WriteLog("Converted to Json")

            sw.Stop()

            objclServiceOutput.TurnAroundTime = sw.ElapsedMilliseconds.ToString()

            Return objclServiceOutput

        Catch ex As Exception
            sw.Stop()
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try

    End Function

    <WebInvoke(Method:="GET", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="GetData?sUser={userName}&sPassword={password}&filename={filename}&filter={filter}&sortorder={sortorder}&field={field}&recordstart={recordstart}&recordend={recordend}&iLinksTop={iLinksTop}&sAppversion={App_Version}&sAppdeviceName={App_DeviceName}&sMD_Version={MD_Version}")>
    Public Function GetData(userName As String, password As String, filename As String, filter As String, sortorder As String, field As String,
                            recordstart As Integer, recordend As Integer, iLinksTop As Integer, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput Implements ISelltisMobileService.GetData

        Dim sw As New Stopwatch()
        Dim objclServiceOutput As New clServiceOutput()

        Try

            'clCache.WriteLog("Request in")
            sw.Start()

            userName = HttpUtility.UrlDecode(userName)
            password = HttpUtility.UrlDecode(password)
            filename = HttpUtility.UrlDecode(filename)
            filter = HttpUtility.UrlDecode(filter)
            sortorder = HttpUtility.UrlDecode(sortorder)
            field = HttpUtility.UrlDecode(field)

            Dim sb As Boolean
            'sb = Membership.ValidateUser(userName, password)
            Dim _memberShip As New clSelltisMembershipProviderNew()
            If IsSSOEnabled() = "true" Then
                sb = _memberShip.ValidateUser_with_EmailId(password, userName)
            Else
                sb = _memberShip.ValidateUser(userName, password)
            End If
            'clCache.WriteLog("Membership.ValidateUser after")

            If sb = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "Invalid Username or Password"
                Return objclServiceOutput
            End If

            Dim Init As New clInit
            HttpContext.Current.Session("goP").sRunMode = "Mobileservice"

            'check MD version
            If ValidateMDVersion(MD_Version) = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = sFailMesage1
                Return objclServiceOutput
            End If

            'clCache.WriteLog("Session init done")

            If filename = "$WebMail$" Then

                Dim filter_sfoldername As String = GetFilter(filter, "txt_foldername")
                Dim filter_sdaysback As String = GetFilter(filter, "txt_daysback")
                Dim filter_ssubject As String = GetFilter(filter, "txt_subject")
                Dim filter_sfromemailid As String = GetFilter(filter, "eml_fromemailid")
                Dim uid As String = GetFilter(filter, "txt_uid")
                Dim sid As String = GetFilter(filter, "txt_mid")
                Dim intdaysback As Integer = 0

                If Not String.IsNullOrEmpty(filter_sdaysback) Then
                    Integer.TryParse(filter_sdaysback, intdaysback)
                End If

                If String.IsNullOrEmpty(filter) Then
                    objclServiceOutput = GetFolders()
                Else
                    If String.IsNullOrEmpty(uid) Then
                        objclServiceOutput = GetMails(IIf(String.IsNullOrEmpty(filter_sfoldername), "Inbox", filter_sfoldername), intdaysback, filter_ssubject, filter_sfromemailid, recordstart, recordend)
                    Else
                        objclServiceOutput = GetMailContent(filter_sfoldername, uid, sid)
                    End If
                End If

            ElseIf filename = "$Attachments$" Then

                Dim sFileGid_id As String = GetFilter(filter, "txt_file_gid_id")
                Dim sFieldName As String = GetFilter(filter, "txt_fieldname")
                Dim sGid_id As String = GetFilter(filter, "txt_gid_id")
                Dim sSource As String = GetFilter(filter, "txt_source")
                If String.IsNullOrEmpty(sGid_id) Then
                    objclServiceOutput = GetAttachmentsList(sFileGid_id, sFieldName)
                Else
                    objclServiceOutput = DownloadAttachment(sGid_id, sSource)
                End If

            ElseIf filename = "$Data$" Then 'mobile details page data

                Dim sGid_id As String = GetFilter(filter, "txt_gid_id")
                Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
                objclServiceOutput = GetMobileDetailsPage(sPage_Gid_Id, sGid_id)

            ElseIf filename = "$ViewMP$" Then 'mobile view page MP data

                Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
                'returns MP data and filter field & label names, default filter, filter sort from MF
                objclServiceOutput = GetMobileViewMP(sPage_Gid_Id)

            ElseIf filename = "$ViewMF$" Then 'mobile view page MF data

                Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
                Dim sFilter As String = GetFilter(filter, "txt_filter")
                Dim ssortorder As String = GetFilter(filter, "txt_sortorder")
                Dim sfields As String = String.Empty 'get the data from mf where type=body and header
                Dim topRecord As String = GetFilter(filter, "txt_toprecord")
                Dim filterPagesize As String = GetFilter(filter, "txt_pagesize")
                Dim filterLinkstop As String = GetFilter(filter, "txt_Linkstop")

                Dim pageSize As Integer = 0
                Dim LinksTop As Integer = 0

                If Not String.IsNullOrEmpty(filterPagesize) Then
                    Integer.TryParse(filterPagesize, pageSize)
                End If

                If Not String.IsNullOrEmpty(filterLinkstop) Then
                    Integer.TryParse(filterLinkstop, LinksTop)
                End If

                objclServiceOutput = GetMobileViewMF(sPage_Gid_Id, sFilter, ssortorder, topRecord, pageSize, LinksTop)

            ElseIf filename = "$PreAdd$" Then 'mobile add page with default values data

                Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
                Dim sCreatefrom As String = GetFilter(filter, "txt_createfrom")
                Dim sCreateType As String = GetFilter(filter, "txt_createtype")

                objclServiceOutput = GetMobileAdd_EditPage(sPage_Gid_Id, "", sCreatefrom, sCreateType)


            ElseIf filename = "$Edit$" Then 'mobile edit page data

                Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
                Dim sGid_id As String = GetFilter(filter, "txt_gid_id")

                objclServiceOutput = GetMobileAdd_EditPage(sPage_Gid_Id, sGid_id, "", "")

            Else

                'Dim rs As New clRowSet(filename, clC.SELL_READONLY, filter, sortorder, field, 100000, "", "", "", "", "", "", False, False, False, iLinksTop, "", False, False)
                Dim rs As New clRowSet(filename, clC.SELL_READONLY, filter, sortorder, field, 100000, "", "", "", "", "", False, False, False, False, iLinksTop, "", False, False, 1800)

                rs.ToTable("", "", True, recordstart, recordend)
                ' clCache.WriteLog("rs.totable() done")
                If rs.dtTransTable.Rows.Count > 0 Then
                    Dim dt As DataTable = rs.dtTransTable.Clone
                    For i As Integer = 0 To rs.dtTransTable.Rows.Count - 1
                        dt.ImportRow(rs.dtTransTable.Rows(i))
                    Next
                    dt = FormatDataTable(dt)

                    objclServiceOutput.Status = "Success"
                    objclServiceOutput.Data = ConvertDataTableToJsonString(dt)
                Else
                    objclServiceOutput.Status = "Fail"
                    objclServiceOutput.Data = "No Records Found"
                End If
            End If

            Logoff()

            ' clCache.WriteLog("Converted to Json")

            sw.Stop()

            objclServiceOutput.TurnAroundTime = sw.ElapsedMilliseconds.ToString()

            Return objclServiceOutput

        Catch ex As Exception
            sw.Stop()
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try

    End Function

    <WebInvoke(Method:="GET", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="GetDataBrowse?sUser={userName}&sPassword={password}&filename={filename}&filter={filter}&sortorder={sortorder}&field={field}&topRecord={topRecord}&pageSize={pageSize}&iLinksTop={iLinksTop}&sAppversion={App_Version}&sAppdeviceName={App_DeviceName}&sMD_Version={MD_Version}")>
    Public Function GetDataBrowse(userName As String, password As String, filename As String, filter As String, sortorder As String, field As String,
                            topRecord As String, pageSize As Integer, iLinksTop As Integer, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput Implements ISelltisMobileService.GetDataBrowse

        Dim sw As New Stopwatch()
        Dim objclServiceOutput As New clServiceOutput()

        Try

            'clCache.WriteLog("Request in")
            sw.Start()

            userName = HttpUtility.UrlDecode(userName)
            password = HttpUtility.UrlDecode(password)
            filename = HttpUtility.UrlDecode(filename)
            filter = HttpUtility.UrlDecode(filter)
            sortorder = HttpUtility.UrlDecode(sortorder)
            field = HttpUtility.UrlDecode(field)

            Dim sb As Boolean
            'sb = Membership.ValidateUser(userName, password)
            Dim _memberShip As New clSelltisMembershipProviderNew()
            If IsSSOEnabled() = "true" Then
                sb = _memberShip.ValidateUser_with_EmailId(password, userName)
            Else
                sb = _memberShip.ValidateUser(userName, password)
            End If
            'clCache.WriteLog("Membership.ValidateUser after")

            If sb = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "Invalid Username or Password"
                Return objclServiceOutput
            End If

            Dim Init As New clInit
            HttpContext.Current.Session("goP").sRunMode = "Mobileservice"

            'check MD version
            If ValidateMDVersion(MD_Version) = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = sFailMesage1
                Return objclServiceOutput
            End If

            'from WT -- BEGIN
            'notice new parameters: topRecord & pageSize
            'topRecord is "" if this is the first visit to the view, OR if the user click paginator 'first'
            'topRecord is the GUID of the LAST record in the previous rowset, if you just clicked 'next'. you need to store this locally after every call to this method
            'topRecord is the GUID of the FIRST record in the previous rowset, if you just clicked 'prev'. you need to store this locally after every call to this metohd
            'if the user clicked 'last' then topRecord is "" AND you need to reverse sort
            'pageSize is the number of records you want to display in this page, parameterized so you can adjust as needed from view to view

            Dim oUtil As New clUtil
            If String.IsNullOrEmpty(topRecord) Then
                topRecord = ""
            End If
            Dim topRecordMD As String = oUtil.GetTopRec(filename, sortorder, topRecord)
            Dim rs As New clRowSet(filename, clC.SELL_READONLY, filter, sortorder, field, pageSize + 1, , "", , , topRecordMD, False, False, False, False, iLinksTop, "", False, False, 1800)

            'in your code, you will have to store the first and last GUID in this rowset. you will use this as topRecord in the next call
            'if you set pageSize as 20 
            '   ...and this rowset returns 21 records, then you know that there is indeed a NEXT/PREV (depends on your direction) page, and you can style your pagination controls accordingly
            '   ...and this rowset returns 20 records or less, then you have reached the end of the table and there is no NEXT/PREV (depends on your direction) page
            'before displaying the rowset results, remove the extra record
            'you call this method each time a user clicks first/prev/next/last paginators
            'from WT -- END


            'clCache.WriteLog("Session init done")

            rs.ToTable()

            ' clCache.WriteLog("rs.totable() done")

            If rs.dtTransTable.Rows.Count > 0 Then

                Dim dt As DataTable = rs.dtTransTable.Clone

                Dim startCount As Integer
                Dim endCount As Integer

                startCount = 0
                endCount = rs.dtTransTable.Rows.Count

                'if first time, we should return first record. Otherwise, ignore first record

                If String.IsNullOrEmpty(topRecordMD) Then
                    startCount = 0
                    If (rs.dtTransTable.Rows.Count > pageSize) Then
                        endCount = endCount - 1
                    End If
                Else
                    startCount = 1
                End If

                endCount = endCount - 1

                'If endCount > pageSize Then
                '    'more records in the database
                '    endCount = endCount - 2
                'Else
                '    endCount = endCount - 1
                'End If

                For i As Integer = startCount To endCount
                    dt.ImportRow(rs.dtTransTable.Rows(i))
                Next

                objclServiceOutput.Status = "Success"
                objclServiceOutput.Data = ConvertDataTableToJsonString(dt)

            Else
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "No Records Found"
            End If

            Logoff()

            ' clCache.WriteLog("Converted to Json")

            sw.Stop()

            objclServiceOutput.TurnAroundTime = sw.ElapsedMilliseconds.ToString()

            Return objclServiceOutput

        Catch ex As Exception
            sw.Stop()
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try

    End Function

    <WebInvoke(Method:="POST", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="LoginNew?sUser={userName}&sPassword={password}&sAppversion={App_Version}&sAppdeviceName={App_DeviceName}&sMD_Version={MD_Version}")>
    Public Function LoginNew(userName As String, password As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutputForLogin Implements ISelltisMobileService.LoginNew

        Dim objclServiceOutput As New clServiceOutputForLogin()

        Try
            Dim sb As Boolean

            'sb = Membership.ValidateUser(userName, password)
            Dim _memberShip As New clSelltisMembershipProviderNew()
            If IsSSOEnabled() = "true" Then
                sb = _memberShip.ValidateUser_with_EmailId(password, userName)
            Else
                sb = _memberShip.ValidateUser(userName, password)
            End If

            If sb = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "Invalid Username or Password"
                Return objclServiceOutput
            End If

            Dim Init As New clInit

            Dim goP As clProject = HttpContext.Current.Session("goP")
            goP.sRunMode = "Mobileservice"

            'check MD Version
            If String.IsNullOrEmpty(MD_Version) = False Then

                Dim sMobileServiceVersion As String = goP.GetVersion("MobileService")
                If sMobileServiceVersion.ToLower().Equals(MD_Version.ToLower()) = False Then
                    objclServiceOutput.Status = "Fail"
                    objclServiceOutput.Data = sFailMesage
                    Return objclServiceOutput
                End If

            End If

            Dim sCurr_MD_Version As String = GetCurrentMDVersion()

            'MS 5/12 commented by Manmeet, dont by pass Validation
            'goP.SetVar("bBypassValidation", True)
            'goP.SetVar("bNoRecordOnSave", False)

            'Dim Returnstring As String
            Dim sName As String = goP.GetMe("Name")
            Dim sCode As String = goP.GetMe("Code")
            Dim sUsername As String = goP.GetMe("LOGONNAME")

            'XL Log
            Dim sMessage As String = sName & " logging in as: '" & userName & "', Device '" & App_DeviceName & "', AppVersion '" & App_Version & "'."
            goLog = HttpContext.Current.Session("goLog")
            goLog.Log("SelltisMobileService:Login", sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)

            Dim UserFCMTokenIDAndDeviceName As String = "##"
            Dim FCMTokenId As String = ""
            Dim FCMTokenDevice As String = ""
            Dim godata As clData = DirectCast(HttpContext.Current.Session("goData"), clData)
            If godata.IsFieldValid("US", "TXT_FCMToken") And godata.IsFieldValid("US", "TXT_FCMTokenDevice") Then
                UserFCMTokenIDAndDeviceName = _memberShip.getFCMTokenIdDevice(HttpContext.Current.Session("USERID"), FCMTokenId, FCMTokenDevice)
            End If

            Dim bEnabledTwoFactorAuth As Boolean = False
            Dim sAuthMessage As String = ""

            _memberShip.TwoFactorAuthentication(bEnabledTwoFactorAuth, sAuthMessage)

            Dim goMeta As clMetaData = HttpContext.Current.Session("goMeta")
            Dim sAutoCRLACSave As String = "False"
            If goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "ENABLE_AUTOSAVE_CRLAC_EMAILSENT_CALLPLACED") = "1" Then
                sAutoCRLACSave = "True"
            End If

            Dim sAppVer As String
            sAppVer = goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "MOBILE_APP_VERSION", "V1")

            'Returnstring = HttpContext.Current.Session("USERID") & "#" & sName & "#" & sCode & "#" & sUsername & "#" & sCurr_MD_Version & UserFCMTokenIDAndDeviceName & "#" & bEnabledTwoFactorAuth & "#" & sAuthMessage & "#" & sAutoCRLACSave
            objclServiceOutput.UserId = HttpContext.Current.Session("USERID")
            objclServiceOutput.Name = sName
            objclServiceOutput.Code = sCode
            objclServiceOutput.UserName = sUsername
            objclServiceOutput.Curr_MD_Version = sCurr_MD_Version
            objclServiceOutput.UserFCMTokenID = FCMTokenId
            objclServiceOutput.FCMTokenDeviceName = FCMTokenDevice
            objclServiceOutput.EnabledTwoFactorAuth = bEnabledTwoFactorAuth
            objclServiceOutput.TwoFactorAuthMessage = sAuthMessage
            objclServiceOutput.AutoCRLACSave = sAutoCRLACSave
            objclServiceOutput.AppVersion = sAppVer
            objclServiceOutput.CustomerLogo = Selltis.Core.Util.GetCustomLogo()
            objclServiceOutput.IsUserAdmin = Selltis.Core.Util.IsUserAdmin()

            Logoff()

            objclServiceOutput.Status = "Success"
            objclServiceOutput.Data = "Login successfull" 'sb.ToString()

            Return objclServiceOutput

        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try

    End Function

    Private Function IsSSOEnabled() As String
        Try
            If Selltis.Core.Util.GetSessionValue("SiteSettings") Is Nothing Then
                clSettings.LoadSiteSettings()
            End If

            Return clSettings.GetUseSSO().ToLower()

        Catch
            Return "false"
        End Try
    End Function

    <WebInvoke(Method:="POST", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="LoadSiteSettings")>
    Public Function LoadSiteSettings() As clSiteSettings Implements ISelltisMobileService.LoadSiteSettings
        Dim objsitesettings As New clSiteSettings
        Try

            If Selltis.Core.Util.GetSessionValue("SiteSettings") Is Nothing Then
                clSettings.LoadSiteSettings()
            End If

            objsitesettings.EnableSSO = clSettings.GetUseSSO()
            objsitesettings.ClientId = System.Configuration.ConfigurationManager.AppSettings("ClientId")
            objsitesettings.TenantId = System.Configuration.ConfigurationManager.AppSettings("Tenant")


            Return objsitesettings

        Catch
            objsitesettings.EnableSSO = "false"
            Return objsitesettings
        End Try

    End Function

    '<WebInvoke(Method:="POST", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="LoginWithEmail?sEmailID={EmailID}&sAppversion={App_Version}&sAppdeviceName={App_DeviceName}&sMD_Version={MD_Version}")>
    'Public Function LoginWithEmail(EmailID As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutputForLogin Implements ISelltisMobileService.LoginWithEmail

    '    Dim objclServiceOutput As New clServiceOutputForLogin()

    '    Try
    '        Dim sb As Boolean

    '        'sb = Membership.ValidateUser(userName, password)
    '        Dim sUSname As String

    '        Dim _memberShip As New clSelltisMembershipProviderNew()
    '        sb = _memberShip.ValidateUser_with_EmailId(EmailID, sUSname)

    '        If sb = False Then
    '            objclServiceOutput.Status = "Fail"
    '            objclServiceOutput.Data = "A valid login does not exist with the email '" + EmailID + "' in Selltis, so please contact Selltis administrator."
    '            Return objclServiceOutput
    '        End If

    '        Dim Init As New clInit

    '        Dim goP As clProject = HttpContext.Current.Session("goP")
    '        goP.sRunMode = "Mobileservice"

    '        'check MD Version
    '        If String.IsNullOrEmpty(MD_Version) = False Then

    '            Dim sMobileServiceVersion As String = goP.GetVersion("MobileService")
    '            If sMobileServiceVersion.ToLower().Equals(MD_Version.ToLower()) = False Then
    '                objclServiceOutput.Status = "Fail"
    '                objclServiceOutput.Data = sFailMesage
    '                Return objclServiceOutput
    '            End If

    '        End If

    '        Dim sCurr_MD_Version As String = GetCurrentMDVersion()

    '        'MS 5/12 commented by Manmeet, dont by pass Validation
    '        'goP.SetVar("bBypassValidation", True)
    '        'goP.SetVar("bNoRecordOnSave", False)

    '        'Dim Returnstring As String
    '        Dim sName As String = goP.GetMe("Name")
    '        Dim sCode As String = goP.GetMe("Code")
    '        Dim sUsername As String = goP.GetMe("LOGONNAME")

    '        'XL Log
    '        Dim sMessage As String = sName & " logging in as: '" & sUSname & "', Device '" & App_DeviceName & "', AppVersion '" & App_Version & "'."
    '        goLog = HttpContext.Current.Session("goLog")
    '        goLog.Log("SelltisMobileService:Login", sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)

    '        Dim UserFCMTokenIDAndDeviceName As String = "##"
    '        Dim FCMTokenId As String = ""
    '        Dim FCMTokenDevice As String = ""
    '        Dim godata As clData = DirectCast(HttpContext.Current.Session("goData"), clData)
    '        If godata.IsFieldValid("US", "TXT_FCMToken") And godata.IsFieldValid("US", "TXT_FCMTokenDevice") Then
    '            UserFCMTokenIDAndDeviceName = _memberShip.getFCMTokenIdDevice(HttpContext.Current.Session("USERID"), FCMTokenId, FCMTokenDevice)
    '        End If

    '        Dim bEnabledTwoFactorAuth As Boolean = False
    '        Dim sAuthMessage As String = ""

    '        _memberShip.TwoFactorAuthentication(bEnabledTwoFactorAuth, sAuthMessage)

    '        Dim goMeta As clMetaData = HttpContext.Current.Session("goMeta")
    '        Dim sAutoCRLACSave As String = "False"
    '        If goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "ENABLE_AUTOSAVE_CRLAC_EMAILSENT_CALLPLACED") = "1" Then
    '            sAutoCRLACSave = "True"
    '        End If

    '        Dim sAppVer As String
    '        sAppVer = goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "MOBILE_APP_VERSION", "V1")

    '        'Returnstring = HttpContext.Current.Session("USERID") & "#" & sName & "#" & sCode & "#" & sUsername & "#" & sCurr_MD_Version & UserFCMTokenIDAndDeviceName & "#" & bEnabledTwoFactorAuth & "#" & sAuthMessage & "#" & sAutoCRLACSave
    '        objclServiceOutput.UserId = HttpContext.Current.Session("USERID")
    '        objclServiceOutput.Name = sName
    '        objclServiceOutput.Code = sCode
    '        objclServiceOutput.UserName = sUsername
    '        objclServiceOutput.Curr_MD_Version = sCurr_MD_Version
    '        objclServiceOutput.UserFCMTokenID = FCMTokenId
    '        objclServiceOutput.FCMTokenDeviceName = FCMTokenDevice
    '        objclServiceOutput.EnabledTwoFactorAuth = bEnabledTwoFactorAuth
    '        objclServiceOutput.TwoFactorAuthMessage = sAuthMessage
    '        objclServiceOutput.AutoCRLACSave = sAutoCRLACSave
    '        objclServiceOutput.AppVersion = sAppVer
    '        objclServiceOutput.CustomerLogo = Selltis.Core.Util.GetCustomLogo()

    '        Logoff()

    '        objclServiceOutput.Status = "Success"
    '        objclServiceOutput.Data = "Login successfull" 'sb.ToString()

    '        Return objclServiceOutput

    '    Catch ex As Exception
    '        objclServiceOutput.Status = "Fail"
    '        objclServiceOutput.Data = ex.ToString()
    '        Return objclServiceOutput
    '    End Try

    'End Function

    <WebInvoke(Method:="POST", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="/SaveData")>
    Public Function SaveData(ByVal cldata As cljsondata) As clServiceOutput Implements ISelltisMobileService.SaveData
        Dim sw As New Stopwatch()
        Dim objclServiceOutput As New clServiceOutput()
        Try

            'cldata = GetCreateNewTestData()  'GetLogMailTestData() 'GetUpdateTestData() '

            sw.Start()

            Dim sb As Boolean
            'sb = Membership.ValidateUser(cldata.username, cldata.password)
            Dim _memberShip As New clSelltisMembershipProviderNew()
            If IsSSOEnabled() = "true" Then
                sb = _memberShip.ValidateUser_with_EmailId(cldata.password, cldata.username)
            Else
                sb = _memberShip.ValidateUser(cldata.username, cldata.password)
            End If

            If sb = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "Invalid Username or Password"
                Return objclServiceOutput
            End If

            Dim Init As New clInit
            HttpContext.Current.Session("goP").sRunMode = "Mobileservice"

            'check MD version
            If ValidateMDVersion(cldata.MD_Version) = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = sFailMesage1
                Return objclServiceOutput
            End If

            If cldata.filename = "$WebMail$" Then

                Dim suid As String = GetFields(cldata, "txt_suid")
                Dim sfoldername As String = GetFields(cldata, "txt_foldername")
                objclServiceOutput = LogMail(suid, sfoldername)

            ElseIf cldata.filename = "$Attachments$" Then

                objclServiceOutput = UploadAttachment(cldata)

            Else

                HttpContext.Current.Session("goP").SetVar("EmailAlertsSave", "ON")

                Dim clMobileSave As Object
                Dim oUtil As New clUtil
                Dim args() As Object = {cldata}
                'clMobileSave = oUtil.CreateCustomClass("clSelltisMobileBase", "cus_clSelltisMobileCustom", args)
                clMobileSave = oUtil.CreateCustomClass("Selltis.BusinessLogic.clSelltisMobileBase", "Selltis.BusinessLogic.cus_clSelltisMobileCustom", args)
                clMobileSave = CType(clMobileSave, clSelltisMobileBase)

                objclServiceOutput = clMobileSave.Save()

                HttpContext.Current.Session("goP").SetVar("EmailAlertsSave", "")

            End If

            Logoff()

            sw.Stop()
            objclServiceOutput.TurnAroundTime = sw.ElapsedMilliseconds.ToString()

            Return objclServiceOutput
        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try
    End Function

    <WebInvoke(Method:="GET", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="/SubmitTwoFactorAuth?AuthCode={AuthCode}&UserId={UserId}")>
    Public Function SubmitTwoFactorAuth(ByVal AuthCode As String, ByVal UserId As String) As clServiceOutput Implements ISelltisMobileService.SubmitTwoFactorAuth
        Dim sw As New Stopwatch()
        Dim objclServiceOutput As New clServiceOutput()
        Try

            sw.Start()

            Dim _memberShip As New clSelltisMembershipProviderNew()
            Dim bValidAuth As Boolean = _memberShip.SubmitTwoFactorAuth(AuthCode, UserId)

            If bValidAuth Then
                objclServiceOutput.Status = "Success"
            Else
                objclServiceOutput.Status = "Fail"
            End If

            sw.Stop()
            objclServiceOutput.TurnAroundTime = sw.ElapsedMilliseconds.ToString()

            Return objclServiceOutput
        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try
    End Function


    <WebInvoke(Method:="POST", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="/UpdateFCMToken")>
    Public Function UpdateFCMToken(ByVal _clValidateToken As clFCMUpdate) As clServiceOutput Implements ISelltisMobileService.UpdateFCMToken
        'Dim sw As New Stopwatch()
        Dim objclServiceOutput As New clServiceOutput()
        Try

            Dim sb As Boolean

            Dim _memberShip As New clSelltisMembershipProviderNew()
            If IsSSOEnabled() = "true" Then
                sb = _memberShip.ValidateUser_with_EmailId(_clValidateToken.Password, _clValidateToken.UserName)
            Else
                sb = _memberShip.ValidateUser(_clValidateToken.UserName, _clValidateToken.Password)
            End If

            If sb = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "Invalid Username or Password"
                Return objclServiceOutput
            End If

            Dim Init As New clInit

            Dim goP As clProject = HttpContext.Current.Session("goP")
            goP.sRunMode = "Mobileservice"

            'check MD Version
            If String.IsNullOrEmpty(_clValidateToken.MD_Version) = False Then

                Dim sMobileServiceVersion As String = goP.GetVersion("MobileService")
                If sMobileServiceVersion.ToLower().Equals(_clValidateToken.MD_Version.ToLower()) = False Then
                    objclServiceOutput.Status = "Fail"
                    objclServiceOutput.Data = "You must upgrade your mobile application"
                    Return objclServiceOutput
                End If

            End If

            Dim sCurr_MD_Version As String = GetCurrentMDVersion()

            'MS 5/12 commented by Manmeet, dont by pass Validation
            'goP.SetVar("bBypassValidation", True)
            'goP.SetVar("bNoRecordOnSave", False)

            'Dim Returnstring As String
            Dim sName As String = goP.GetMe("Name")
            Dim sCode As String = goP.GetMe("Code")
            Dim sUsername As String = goP.GetMe("LOGONNAME")

            'XL Log
            Dim sMessage As String = sName & " logging in as: '" & _clValidateToken.UserName & "', Device '" & _clValidateToken.App_DeviceName & "', AppVersion '" & _clValidateToken.App_Version & "'."
            goLog = HttpContext.Current.Session("goLog")
            goLog.Log("SelltisMobileService:Login", sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)

            Dim godata As clData = DirectCast(HttpContext.Current.Session("goData"), clData)
            If godata.IsFieldValid("US", "TXT_FCMToken") And godata.IsFieldValid("US", "TXT_FCMTokenDevice") Then
                Dim Query As String = "Update US Set TXT_FCMToken = '" & _clValidateToken.FCMTokenId & "',TXT_FCMTokenDevice = '" & _clValidateToken.FCMTokenDevice & "' where  GID_ID= '" & HttpContext.Current.Session("USERID").ToString() & "'"
                godata.RunSQLQuery(Query)
                objclServiceOutput.Status = "Success"
                objclServiceOutput.Data = "FCM Token Updated"
            Else
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "TXT_FCMToken and TXT_FCMTokenDevice Fields are not exist"
            End If

            'objclServiceOutput.Status = "Success"
            'objclServiceOutput.Data = "FCM Token Updated"

            Return objclServiceOutput

        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try

    End Function

    <WebInvoke(Method:="GET", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="GetSingleMetaData?sUser={userName}&sPassword={password}&sectionname={sectionname}&pagename={pagename}&propertyname={propertyname}&sAppversion={App_Version}&sAppdeviceName={App_DeviceName}&sMD_Version={MD_Version}")>
    Public Function GetSingleMetaData(userName As String, password As String, sectionname As String, pagename As String, propertyname As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput Implements ISelltisMobileService.GetSingleMetaData

        Dim objclServiceOutput As New clServiceOutput()

        Try

            userName = HttpUtility.UrlDecode(userName)
            password = HttpUtility.UrlDecode(password)
            sectionname = HttpUtility.UrlDecode(sectionname)
            pagename = HttpUtility.UrlDecode(pagename)
            propertyname = HttpUtility.UrlDecode(propertyname)

            Dim sb As Boolean
            'sb = Membership.ValidateUser(userName, password)
            Dim _memberShip As New clSelltisMembershipProviderNew()
            If IsSSOEnabled() = "true" Then
                sb = _memberShip.ValidateUser_with_EmailId(password, userName)
            Else
                sb = _memberShip.ValidateUser(userName, password)
            End If

            If sb = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "Invalid Username or Password"
                Return objclServiceOutput
            End If

            Dim Init As New clInit
            HttpContext.Current.Session("goP").sRunMode = "Mobileservice"

            'check MD version
            If ValidateMDVersion(MD_Version) = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = sFailMesage1
                Return objclServiceOutput
            End If

            Dim goMeta As clMetaData
            Dim goTR As clTransform
            goMeta = HttpContext.Current.Session("goMeta")
            goTR = HttpContext.Current.Session("goTr")

            Dim sPage As String = goMeta.PageRead(sectionname, pagename)
            Dim sdata As String = goTR.StrRead(sPage, propertyname)

            Logoff()

            objclServiceOutput.Status = "Success"
            objclServiceOutput.Data = sdata 'GetJsonString(sdata)
            Return objclServiceOutput

        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try
    End Function

    <WebInvoke(Method:="GET", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="GetMLSValues?sUser={userName}&sPassword={password}&tableName={tableName}&fieldName={fieldName}&sAppversion={App_Version}&sAppdeviceName={App_DeviceName}&sMD_Version={MD_Version}")>
    Public Function GetMLSValues(userName As String, password As String, tableName As String, fieldName As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput Implements ISelltisMobileService.GetMLSValues
        Dim objclServiceOutput As New clServiceOutput()

        Try

            userName = HttpUtility.UrlDecode(userName)
            password = HttpUtility.UrlDecode(password)
            tableName = HttpUtility.UrlDecode(tableName)
            fieldName = HttpUtility.UrlDecode(fieldName)

            Dim sb As Boolean
            'sb = Membership.ValidateUser(userName, password)
            Dim _memberShip As New clSelltisMembershipProviderNew()
            If IsSSOEnabled() = "true" Then
                sb = _memberShip.ValidateUser_with_EmailId(password, userName)
            Else
                sb = _memberShip.ValidateUser(userName, password)
            End If

            If sb = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "Invalid Username or Password"
                Return objclServiceOutput
            End If

            Dim Init As New clInit
            HttpContext.Current.Session("goP").sRunMode = "Mobileservice"

            'check MD version
            If ValidateMDVersion(MD_Version) = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = sFailMesage1
                Return objclServiceOutput
            End If

            Dim goTR As clTransform
            goTR = HttpContext.Current.Session("goTr")

            Dim oMLSList As New clList
            Dim aList As clArray
            Dim sListItemKey As String
            Dim sListItemValue As String
            Dim list As List(Of KeyValuePair(Of String, String)) = New List(Of KeyValuePair(Of String, String))

            aList = oMLSList.GetList(tableName, fieldName)
            For x = 1 To aList.GetDimension
                sListItemValue = aList.GetItem(x)
                sListItemKey = oMLSList.LReadSeek(tableName & ":" & goTR.Replace(fieldName, "MLS_", ""), "VALUE", sListItemValue)
                list.Add(New KeyValuePair(Of String, String)(sListItemKey, sListItemValue))
            Next

            Logoff()

            objclServiceOutput.Status = "Success"
            objclServiceOutput.Data = GetJsonString(list)
            Return objclServiceOutput

        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try
    End Function

    <WebInvoke(Method:="GET", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="GetMetaData?sUser={userName}&sPassword={password}&sectionname={sectionname}&pagename={pagename}&sAppversion={App_Version}&sAppdeviceName={App_DeviceName}&sMD_Version={MD_Version}")>
    Public Function GetMetaData(userName As String, password As String, sectionname As String, pagename As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput Implements ISelltisMobileService.GetMetaData
        Dim objclServiceOutput As New clServiceOutput()

        Try

            userName = HttpUtility.UrlDecode(userName)
            password = HttpUtility.UrlDecode(password)
            sectionname = HttpUtility.UrlDecode(sectionname)
            pagename = HttpUtility.UrlDecode(pagename)

            Dim sb As Boolean
            'sb = Membership.ValidateUser(userName, password)
            Dim _memberShip As New clSelltisMembershipProviderNew()
            If IsSSOEnabled() = "true" Then
                sb = _memberShip.ValidateUser_with_EmailId(password, userName)
            Else
                sb = _memberShip.ValidateUser(userName, password)
            End If

            If sb = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "Invalid Username or Password"
                Return objclServiceOutput
            End If

            Dim Init As New clInit
            HttpContext.Current.Session("goP").sRunMode = "Mobileservice"

            'check MD version
            If ValidateMDVersion(MD_Version) = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = sFailMesage1
                Return objclServiceOutput
            End If

            Dim goMeta As clMetaData
            goMeta = HttpContext.Current.Session("goMeta")

            Dim sPage As String = goMeta.PageRead(sectionname, pagename)

            Logoff()

            objclServiceOutput.Status = "Success"
            objclServiceOutput.Data = sPage 'GetJsonString(sPage)
            Return objclServiceOutput

        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try
    End Function

    <WebInvoke(Method:="GET", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="GetDataObject?sUser={userName}&sPassword={password}&filename={filename}&filter={filter}&sortorder={sortorder}&field={field}&recordstart={recordstart}&recordend={recordend}&iLinksTop={iLinksTop}&sAppversion={App_Version}&sAppdeviceName={App_DeviceName}&sMD_Version={MD_Version}")>
    Public Function GetDataObject(userName As String, password As String, filename As String, filter As String, sortorder As String, field As String,
                            recordstart As Integer, recordend As Integer, iLinksTop As Integer, App_Version As String, App_DeviceName As String, MD_Version As String) As clOutput Implements ISelltisMobileService.GetDataObject

        Dim sw As New Stopwatch()
        Dim objclOutput As New clOutput

        Dim filter2 As String = HttpUtility.UrlDecode(filter)
        Dim condition As String = GetFilter(filter2, "txt_filter")
        Return GetDataObject(userName, password, filename, filter, condition, sortorder, field, recordstart, recordend, iLinksTop, App_Version, App_DeviceName, MD_Version, "false")

        'Try

        '    'clCache.WriteLog("Request in")
        '    sw.Start()

        '    userName = HttpUtility.UrlDecode(userName)
        '    password = HttpUtility.UrlDecode(password)
        '    filename = HttpUtility.UrlDecode(filename)
        '    filter = HttpUtility.UrlDecode(filter)
        '    sortorder = HttpUtility.UrlDecode(sortorder)
        '    field = HttpUtility.UrlDecode(field)

        '    Dim sb As Boolean
        '    sb = Membership.ValidateUser(userName, password)

        '    If sb = False Then

        '        objclOutput.objStatus = New clStatus()
        '        objclOutput.objStatus.status = "Fail"
        '        objclOutput.objStatus.Errormessage = "Invalid Username / Password"
        '        Return objclOutput

        '    End If

        '    Dim Init As New clInit
        '    HttpContext.Current.Session("goP").sRunMode = "Mobileservice"

        '    'check MD version
        '    If ValidateMDVersion(MD_Version) = False Then
        '        objclOutput.objStatus.status = "Fail"
        '        objclOutput.objStatus.Errormessage = sFailMesage1
        '        Return objclOutput
        '    End If

        '    'clCache.WriteLog("Session init done")

        '    If filename = "$Data$" Then 'mobile details page data

        '        Dim sGid_id As String = GetFilter(filter, "txt_gid_id")
        '        Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
        '        objclOutput = GetMobileDetailsPage_Object(sPage_Gid_Id, sGid_id)

        '    ElseIf filename = "$ViewMP$" Then 'mobile view page MP data

        '        Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
        '        'returns MP data and filter field & label names, default filter, filter sort from MF
        '        objclOutput = GetMobileViewMP_Object(sPage_Gid_Id)

        '    ElseIf filename = "$ViewMF$" Then 'mobile view page MF data

        '        Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
        '        Dim sFilter As String = GetFilter(filter, "txt_filter")
        '        Dim ssortorder As String = GetFilter(filter, "txt_sortorder")
        '        Dim sfields As String = String.Empty 'get the data from mf where type=body and header
        '        Dim topRecord As String = GetFilter(filter, "txt_toprecord")
        '        Dim filterPagesize As String = GetFilter(filter, "txt_pagesize")
        '        Dim filterLinkstop As String = GetFilter(filter, "txt_linkstop")

        '        ''Filters for finding near by companies and contacts
        '        Dim sLatitude As String = GetFilter(filter, "txt_latitude")
        '        Dim slongitude As String = GetFilter(filter, "txt_longitude")
        '        Dim sRange As String = GetFilter(filter, "txt_range")
        '        Dim sMeasureType As String = GetFilter(filter, "txt_measuretype")
        '        Dim sFilterName As String = GetFilter(filter, "txt_filtername")
        '        Dim sUserId As String = GetFilter(filter, "txt_userid")

        '        'filter=txt_page_gid_id=34363637-3266-6536-584d-31322f32322f' AND txt_FilterName='Key Company' 
        '        'AND txt_sortorder='SYS_NAME' AND txt_toprecord='' AND txt_pagesize='100' AND txt_Linkstop=1 AND txt_latitude='17.427020' AND 
        '        'txt_longitude='78.415700' AND txt_range='20' AND txt_measuretype='kilometers' AND  txt_UserId='b63d3ed0-b5f9-4d63-5553-a3c601421636'

        '        Dim pageSize As Integer = 0
        '        Dim LinksTop As Integer = 0

        '        If Not String.IsNullOrEmpty(filterPagesize) Then
        '            Integer.TryParse(filterPagesize, pageSize)
        '        End If

        '        If Not String.IsNullOrEmpty(filterLinkstop) Then
        '            Integer.TryParse(filterLinkstop, LinksTop)
        '        End If

        '        objclOutput = GetMobileViewMF_Object(sPage_Gid_Id, sFilter, ssortorder, topRecord, pageSize, LinksTop, sLatitude, slongitude, sRange, sMeasureType, sFilterName, sUserId)

        '    ElseIf filename = "$PreAdd$" Then 'mobile add page with default values data

        '        Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
        '        Dim sCreatefrom As String = GetFilter(filter, "txt_createfrom")
        '        Dim sCreateType As String = GetFilter(filter, "txt_createtype")

        '        objclOutput = GetMobileAdd_EditPage_Object(sPage_Gid_Id, "", sCreatefrom, sCreateType)

        '    ElseIf filename = "$Edit$" Then 'mobile edit page data

        '        Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
        '        Dim sGid_id As String = GetFilter(filter, "txt_gid_id")

        '        objclOutput = GetMobileAdd_EditPage_Object(sPage_Gid_Id, sGid_id, "", "")

        '    Else
        '        objclOutput.objStatus.status = "Fail"
        '        objclOutput.objStatus.Errormessage = "Invalid file name"
        '    End If

        '    Logoff()
        '    sw.Stop()

        '    Return objclOutput

        'Catch ex As Exception
        '    objclOutput.objStatus.status = "Fail"
        '    objclOutput.objStatus.Errormessage = ex.ToString()
        '    Return objclOutput
        'End Try

    End Function

    <WebInvoke(Method:="GET", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="DeleteAttachment?sUser={userName}&sPassword={password}&GidId={GidId}&sAppversion={App_Version}&sAppdeviceName={App_DeviceName}&sMD_Version={MD_Version}")>
    Public Function DeleteAttachment(userName As String, password As String, GidId As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput Implements ISelltisMobileService.DeleteAttachment

        Dim objclOutput As New clServiceOutput

        Try
            userName = HttpUtility.UrlDecode(userName)
            password = HttpUtility.UrlDecode(password)
            GidId = HttpUtility.UrlDecode(GidId)

            Dim sb As Boolean
            'sb = Membership.ValidateUser(userName, password)
            Dim _memberShip As New clSelltisMembershipProviderNew()
            If IsSSOEnabled() = "true" Then
                sb = _memberShip.ValidateUser_with_EmailId(password, userName)
            Else
                sb = _memberShip.ValidateUser(userName, password)
            End If

            If sb = False Then
                objclOutput.Status = "Fail"
                objclOutput.Data = "Invalid Username or Password"
                Return objclOutput
            End If

            Dim Init As New clInit
            HttpContext.Current.Session("goP").sRunMode = "Mobileservice"

            'check MD version
            If ValidateMDVersion(MD_Version) = False Then
                objclOutput.Status = "Fail"
                objclOutput.Data = sFailMesage1
                Return objclOutput
            End If

            objclOutput = DeleteAttachment(GidId)

            Logoff()

            Return objclOutput

        Catch ex As Exception
            objclOutput.Status = "Fail"
            objclOutput.Data = ex.ToString()
            Return objclOutput
        End Try

    End Function

    <WebInvoke(Method:="GET", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="GetDataObject2?sUser={userName}&sPassword={password}&filename={filename}&filter={filter}&condition={condition}&sortorder={sortorder}&field={field}&recordstart={recordstart}&recordend={recordend}&iLinksTop={iLinksTop}&sAppversion={App_Version}&sAppdeviceName={App_DeviceName}&sMD_Version={MD_Version}&IsServiceCall={IsServiceCall}")>
    Public Function GetDataObject(userName As String, password As String, filename As String, filter As String, condition As String, sortorder As String, field As String,
                            recordstart As Integer, recordend As Integer, iLinksTop As Integer, App_Version As String, App_DeviceName As String, MD_Version As String, IsServiceCall As String) As clOutput Implements ISelltisMobileService.GetDataObject2

        Dim sw As New Stopwatch()
        Dim objclOutput As New clOutput

        Try

            'clCache.WriteLog("Request in")
            sw.Start()

            userName = HttpUtility.UrlDecode(userName)
            password = HttpUtility.UrlDecode(password)
            filename = HttpUtility.UrlDecode(filename)
            filter = HttpUtility.UrlDecode(filter)
            sortorder = HttpUtility.UrlDecode(sortorder)
            field = HttpUtility.UrlDecode(field)
            condition = HttpUtility.UrlDecode(condition)
            IsServiceCall = HttpUtility.UrlDecode(IsServiceCall)

            If String.IsNullOrEmpty(IsServiceCall) Then
                IsServiceCall = "true"
            End If


            Dim sb As Boolean
            Dim _memberShip As New clSelltisMembershipProviderNew()
            If IsSSOEnabled() = "true" Then
                sb = _memberShip.ValidateUser_with_EmailId(password, userName)
            Else
                sb = _memberShip.ValidateUser(userName, password)
            End If


            If sb = False Then

                objclOutput.objStatus = New clStatus()
                objclOutput.objStatus.status = "Fail"
                objclOutput.objStatus.Errormessage = "Invalid Username / Password"
                Return objclOutput

            End If

            Dim Init As New clInit
            HttpContext.Current.Session("goP").sRunMode = "Mobileservice"

            'check MD version
            If ValidateMDVersion(MD_Version) = False Then
                objclOutput.objStatus.status = "Fail"
                objclOutput.objStatus.Errormessage = sFailMesage1
                Return objclOutput
            End If

            'clCache.WriteLog("Session init done")

            If filename = "$Data$" Then 'mobile details page data

                Dim sGid_id As String = GetFilter(filter, "txt_gid_id")
                Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
                objclOutput = GetMobileDetailsPage_Object(sPage_Gid_Id, sGid_id)

            ElseIf filename = "$MPRP$" Then 'mobile landing page

                objclOutput.objStatus.status = "Success"
                objclOutput.ProfilePage = MPRP()

            ElseIf filename = "$ViewMP$" Then 'mobile view page MP data

                Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
                Dim sShowAllMFFields As String = GetFilter(filter, "show_all_mf_fields")
                If String.IsNullOrEmpty(sShowAllMFFields) Then
                    sShowAllMFFields = "False"
                End If
                'returns MP data and filter field & label names, default filter, filter sort from MF
                objclOutput = GetMobileViewMP_Object(sPage_Gid_Id, Convert.ToBoolean(sShowAllMFFields))

            ElseIf filename = "$ViewMF$" Then 'mobile view page MF data

                If String.IsNullOrEmpty(condition) = False And IsServiceCall.ToLower() = "true" Then
                    condition = condition.Trim().Replace("|", "=").Replace("~", " AND ")
                    condition = condition.Replace("?^", "|")
                End If

                Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
                Dim sFilter As String = condition  'GetFilter(filter, "txt_filter")
                Dim ssortorder As String = GetFilter(filter, "txt_sortorder")
                Dim sfields As String = String.Empty 'get the data from mf where type=body and header
                Dim topRecord As String = GetFilter(filter, "txt_toprecord")
                Dim filterPagesize As String = GetFilter(filter, "txt_pagesize")
                Dim filterLinkstop As String = GetFilter(filter, "txt_linkstop")

                ''Filters for finding near by companies and contacts
                Dim sLatitude As String = GetFilter(filter, "txt_latitude")
                Dim slongitude As String = GetFilter(filter, "txt_longitude")
                Dim sRange As String = GetFilter(filter, "txt_range")
                Dim sMeasureType As String = GetFilter(filter, "txt_measuretype")
                Dim sFilterName As String = GetFilter(filter, "txt_filtername")
                Dim sUserId As String = GetFilter(filter, "txt_userid")

                'filter=txt_page_gid_id=34363637-3266-6536-584d-31322f32322f' AND txt_FilterName='Key Company' 
                'AND txt_sortorder='SYS_NAME' AND txt_toprecord='' AND txt_pagesize='100' AND txt_Linkstop=1 AND txt_latitude='17.427020' AND 
                'txt_longitude='78.415700' AND txt_range='20' AND txt_measuretype='kilometers' AND  txt_UserId='b63d3ed0-b5f9-4d63-5553-a3c601421636'

                Dim pageSize As Integer = 0
                Dim LinksTop As Integer = 0

                If Not String.IsNullOrEmpty(filterPagesize) Then
                    Integer.TryParse(filterPagesize, pageSize)
                End If

                If Not String.IsNullOrEmpty(filterLinkstop) Then
                    Integer.TryParse(filterLinkstop, LinksTop)
                End If

                objclOutput = GetMobileViewMF_Object(sPage_Gid_Id, sFilter, ssortorder, topRecord, pageSize, LinksTop, sLatitude, slongitude, sRange, sMeasureType, sFilterName, sUserId)

            ElseIf filename = "$PreAdd$" Then 'mobile add page with default values data

                Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
                Dim sCreatefrom As String = GetFilter(filter, "txt_createfrom")
                Dim sCreateType As String = GetFilter(filter, "txt_createtype")

                objclOutput = GetMobileAdd_EditPage_Object(sPage_Gid_Id, "", sCreatefrom, sCreateType)

            ElseIf filename = "$Edit$" Then 'mobile edit page data

                Dim sPage_Gid_Id As String = GetFilter(filter, "txt_page_gid_id")
                Dim sGid_id As String = GetFilter(filter, "txt_gid_id")

                objclOutput = GetMobileAdd_EditPage_Object(sPage_Gid_Id, sGid_id, "", "")

            ElseIf filename = "$Script$" Then

                Dim _script As New Selltis.Core.ScriptManager()
                Dim sfieldval As String = GetFilter(filter, "fieldval")
                Dim sretval As String = ""
                Dim filterLinkstop As String = GetFilter(filter, "txt_linkstop")
                _script.RunScript(filename + "_MobileFormControlOnChange_" + field, sfieldval, sretval, False, "", Nothing)

            Else
                objclOutput.objStatus.status = "Fail"
                objclOutput.objStatus.Errormessage = "Invalid file name"
            End If

            Logoff()
            sw.Stop()

            Return objclOutput

        Catch ex As Exception
            objclOutput.objStatus.status = "Fail"
            objclOutput.objStatus.Errormessage = ex.ToString()
            Return objclOutput
        End Try

    End Function


    ''this for testing
    <WebInvoke(Method:="GET", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="GetpValue")>
    Public Function GetpValue() As String Implements ISelltisMobileService.GetpValue

        Try
            Dim random As New Random()
            Return random.Next(0, 20) + random.NextDouble()

        Catch ex As Exception
            Return "20.5"
        End Try

    End Function

#End Region

#Region "Private Functions"

    Private Function Logoff() As Boolean

        'If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")
        'HttpContext.Current.Session("WS_RS_" & HttpContext.Current.Session.SessionID) = Nothing
        HttpContext.Current.Session.Clear()
        HttpContext.Current.Session.Abandon()
        GC.Collect()
        Return True

    End Function

    Private Function GetJsonString(ByVal obj As Object) As String
        Dim s As New System.Web.Script.Serialization.JavaScriptSerializer()
        Return s.Serialize(obj)
    End Function

    Private Function ConvertDataTableToJsonString(ByVal dt As DataTable) As String
        Dim serializer As New System.Web.Script.Serialization.JavaScriptSerializer()
        Dim rows As New List(Of Dictionary(Of String, Object))()
        Dim row As Dictionary(Of String, Object)

        For Each dr As DataRow In dt.Rows
            row = New Dictionary(Of String, Object)()
            For Each col As DataColumn In dt.Columns
                row.Add(col.ColumnName, dr(col))
            Next
            rows.Add(row)
        Next
        Return serializer.Serialize(rows)
    End Function

    Private Function GetCreateNewTestData() As cljsondata

        Dim objdata As New cljsondata()

        objdata.username = "sysuser"
        objdata.password = "windev.55"
        objdata.IsNew = True
        objdata.filename = "CN"


        '{"IsNew":true,"createFrom":"","createType":"","filename":"CN","filter":"","lstData":[{"FieldName":"TXT_NAMELAST","FieldValue":"k"},
        '    {"FieldName":"TXT_NAMEFIRST","FieldValue":"pratap"},{"FieldName":"TXT_NICKNAME","FieldValue":"pk"},
        '    {"FieldName":"LNK_RELATED_CO","FieldValue":"84723df1-7330-4ece-434f-a48b0108cc5d"},{"FieldName":"TEL_CELLPHONE","FieldValue":"8121390970"},
        '    {"FieldName":"TEL_HOMEPHONE","FieldValue":""},{"FieldName":"TEL_BUSPHONE","FieldValue":""},{"FieldName":"EML_EMAIL","FieldValue":""},
        '    {"FieldName":"TXT_ADDRBUSINESS","FieldValue":""},{"FieldName":"TXT_CITYBUSINESS","FieldValue":""},{"FieldName":"TXT_STATEBUSINESS","FieldValue":""},
        '    {"FieldName":"TXT_ZIPBUSINESS","FieldValue":""},{"FieldName":"TXT_COUNTRYBUSINESS","FieldValue":""},{"FieldName":"INT_REVIEWINTERVAL","FieldValue":"30"},
        '    {"FieldName":"DTE_NEXTCONTACTDATE","FieldValue":""},{"FieldName":"CHK_REVIEW","FieldValue":"0"},{"FieldName":"CHK_KEY","FieldValue":"0"},
        '    {"FieldName":"MMO_NOTE","FieldValue":""}],"password":"1ncarcerat3d","sAppdeviceName":"Motorola XT1033","sAppversion":"2.4","username":"system"}


        Dim c1 As New clFields()
        c1.FieldName = "TXT_NAMELAST"
        c1.FieldValue = "k"
        objdata.lstData.Add(c1)

        Dim c2 As New clFields()
        c2.FieldName = "TXT_NAMEFIRST"
        c2.FieldValue = "pratap"
        objdata.lstData.Add(c2)

        Dim c3 As New clFields()
        c3.FieldName = "LNK_RELATED_CO"
        c3.FieldValue = "84723df1-7330-4ece-434f-a48b0108cc5d"
        objdata.lstData.Add(c3)

        'Dim c4 As New clFields()
        'c4.FieldName = "txt_filename"
        'c4.FieldValue = "073a58b4-6db0-4bd0-a833-1db68d4678cb.jpg"
        'objdata.lstData.Add(c4)

        'Dim c5 As New clFields()
        'c5.FieldName = "txt_filedata"

        'Dim bytes = File.ReadAllBytes("D:\073a58b4-6db0-4bd0-a833-1db68d4678cb.jpg")
        'Dim _file As String = Convert.ToBase64String(bytes)

        'c5.FieldValue = _file
        'objdata.lstData.Add(c5)

        'Dim data() As Byte
        'data = File.ReadAllBytes("D:\073a58b4-6db0-4bd0-a833-1db68d4678cb.jpg")
        'objdata.FileData = data

        ' '''*************************

        'Dim c5 As New clFields()
        'c5.FieldName = "MLS_PRIORITY"
        'c5.FieldValue = "3"
        'objdata.lstData.Add(c5)

        'Dim c6 As New clFields()
        'c6.FieldName = "MLS_STAGE"
        'c6.FieldValue = "Needs Identified"
        'objdata.lstData.Add(c6)


        'Dim c7 As New clFields()
        'c7.FieldName = "LNK_FOR_PD"
        'c7.FieldValue = "906fbdd4-9c5b-4789-5044-990e014998a5"
        'objdata.lstData.Add(c7)


        'Dim c8 As New clFields()
        'c8.FieldName = "CUR_UNITVALUE"
        'c8.FieldValue = "$5.00"
        'objdata.lstData.Add(c8)


        'Dim c9 As New clFields()
        'c9.FieldName = "SR__Qty"
        'c9.FieldValue = "3"
        'objdata.lstData.Add(c9)


        'Dim c10 As New clFields()
        'c10.FieldName = "SI__PROBABILITY"
        'c10.FieldValue = "10"
        'objdata.lstData.Add(c10)


        'Dim c11 As New clFields()
        'c11.FieldName = "DTE_EXPCLOSEDATE"
        'c11.FieldValue = "2015-09-30"
        'objdata.lstData.Add(c11)

        'Dim c12 As New clFields()
        'c12.FieldName = "DTE_NEXTACTIONDATE"
        'c12.FieldValue = "2015-09-25"
        'objdata.lstData.Add(c12)


        'Dim c13 As New clFields()
        'c13.FieldName = "MMO_NEXTACTION"
        'c13.FieldValue = "next action 25"
        'objdata.lstData.Add(c13)



        Return objdata

    End Function

    Private Function GetUpdateTestData() As cljsondata

        Dim lstdata As New List(Of clFields)()

        Dim c1 As New clFields()
        c1.FieldName = "TXT_NAMELAST"
        c1.FieldValue = "Tankala"
        lstdata.Add(c1)

        Dim c2 As New clFields()
        c2.FieldName = "TXT_NAMEFIRST"
        c2.FieldValue = "venkatesh"
        lstdata.Add(c2)
        '
        'string sdata = ser.Serialize(lstdata);

        Dim c3 As New clFields()
        c3.FieldName = "MLS_TYPE"
        c3.FieldValue = "Call Received"
        lstdata.Add(c3)

        Dim c4 As New clFields()
        c4.FieldName = "MLS_PURPOSE"
        c4.FieldValue = "Inquiry"
        lstdata.Add(c4)


        Dim filename As String = "AC"
        Dim filter As String = "GID_ID=2b88af66-6d64-4885-434e-a43500a1025b"

        Dim jsondata As New cljsondata()
        jsondata.filename = filename
        jsondata.filter = filter
        jsondata.username = "manmeets"
        jsondata.password = "Infodat2"
        jsondata.lstData = lstdata
        jsondata.IsNew = True

        Return jsondata

    End Function

    Private Function GetLogMailTestData() As cljsondata
        Dim lstdata As New List(Of clFields)()

        Dim c1 As New clFields()
        c1.FieldName = "txt_suid"
        c1.FieldValue = "28195"
        lstdata.Add(c1)

        Dim c2 As New clFields()
        c2.FieldName = "txt_foldername"
        c2.FieldValue = "Inbox"
        lstdata.Add(c2)

        Dim filename As String = "$LogMail$"
        Dim filter As String = ""

        Dim jsondata As New cljsondata()
        jsondata.filename = filename
        jsondata.filter = filter
        jsondata.username = "venkatesht"
        jsondata.password = "infodat@12345"
        jsondata.lstData = lstdata
        jsondata.IsNew = False

        Return jsondata
    End Function

    <WebInvoke(Method:="GET", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="CheckAddPermissions?sUser={userName}&sPassword={password}&filename={filename}&sAppversion={App_Version}&sAppdeviceName={App_DeviceName}")>
    Private Function CheckAddPermissions(userName As String, password As String, filename As String, App_Version As String, App_DeviceName As String) As clServiceOutput Implements ISelltisMobileService.CheckAddPermissions

        Dim objclServiceOutput As New clServiceOutput()

        Try
            'clCache.WriteLog("Request in")

            userName = HttpUtility.UrlDecode(userName)
            password = HttpUtility.UrlDecode(password)
            filename = HttpUtility.UrlDecode(filename)

            Dim sb As Boolean
            'sb = Membership.ValidateUser(userName, password)
            Dim _memberShip As New clSelltisMembershipProviderNew()
            If IsSSOEnabled() = "true" Then
                sb = _memberShip.ValidateUser_with_EmailId(password, userName)
            Else
                sb = _memberShip.ValidateUser(userName, password)
            End If

            'clCache.WriteLog("Membership.ValidateUser after")

            If sb = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "Invalid Username or Password"
                Return objclServiceOutput
            End If

            Dim Init As New clInit
            HttpContext.Current.Session("goP").sRunMode = "Webservice"

            Dim goData As clData = HttpContext.Current.Session("goData")

            If goData.GetAddPermission(filename) = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "Permissions denied adding record"
            Else
                objclServiceOutput.Status = "Success"
                objclServiceOutput.Data = "Permissions granted adding record"
            End If

            Logoff()

            Return objclServiceOutput

        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try

    End Function
    <WebInvoke(Method:="POST", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="Get_MobileVersionCode?iType={iType}")>
    Private Function Get_MobileVersionCode(ByVal iType As Integer) As clversion Implements ISelltisMobileService.Get_MobileVersionCode


        Dim objclvesrion As New clversion()
        Dim goMeta As clMetaData = HttpContext.Current.Session("goMeta")

        If iType = 2 Then
            objclvesrion.VersionCode = goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "Mobile_iOS_VersionCode", "")

        ElseIf iType = 1 Then
            objclvesrion.VersionCode = goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "Mobile_Andriod_versionCode", "")
        End If

        Return objclvesrion


    End Function


    <WebInvoke(Method:="GET", RequestFormat:=WebMessageFormat.Json, ResponseFormat:=WebMessageFormat.Json, UriTemplate:="CheckRecordEditPermissions?sUser={userName}&sPassword={password}&RecordId={RecordId}&sAppversion={App_Version}&sAppdeviceName={App_DeviceName}")>
    Private Function CheckRecordEditPermissions(userName As String, password As String, RecordId As String, App_Version As String, App_DeviceName As String) As clServiceOutput Implements ISelltisMobileService.CheckRecordEditPermissions

        Dim objclServiceOutput As New clServiceOutput()

        Try
            'clCache.WriteLog("Request in")

            userName = HttpUtility.UrlDecode(userName)
            password = HttpUtility.UrlDecode(password)
            RecordId = HttpUtility.UrlDecode(RecordId)

            Dim sb As Boolean
            ' sb = Membership.ValidateUser(userName, password)
            Dim _memberShip As New clSelltisMembershipProviderNew()
            If IsSSOEnabled() = "true" Then
                sb = _memberShip.ValidateUser_with_EmailId(password, userName)
            Else
                sb = _memberShip.ValidateUser(userName, password)
            End If
            'clCache.WriteLog("Membership.ValidateUser after")

            If sb = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "Invalid Username or Password"
                Return objclServiceOutput
            End If

            Dim Init As New clInit
            HttpContext.Current.Session("goP").sRunMode = "Webservice"

            Dim goData As clData = HttpContext.Current.Session("goData")

            If goData.GetRecordPermission(RecordId, "E") = False Then
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "Permissions denied editing record"
            Else
                objclServiceOutput.Status = "Success"
                objclServiceOutput.Data = "Permissions granted editing record"
            End If

            Logoff()

            Return objclServiceOutput

        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try

    End Function

    Private Function Serialize(value As Object) As String
        Dim type As Type = value.[GetType]()

        Dim json As New Newtonsoft.Json.JsonSerializer()

        json.NullValueHandling = NullValueHandling.Ignore

        json.ObjectCreationHandling = Newtonsoft.Json.ObjectCreationHandling.Replace
        json.MissingMemberHandling = Newtonsoft.Json.MissingMemberHandling.Ignore
        json.ReferenceLoopHandling = ReferenceLoopHandling.Ignore

        If type = GetType(DataTable) Then
            json.Converters.Add(New DataTableConverter())
        ElseIf type = GetType(DataSet) Then
            json.Converters.Add(New DataSetConverter())
        End If

        Dim sw As New StringWriter()
        Dim writer As Newtonsoft.Json.JsonTextWriter = New JsonTextWriter(sw)

        writer.Formatting = Formatting.None

        'If Me.FormatJsonOutput Then
        '    writer.Formatting = Formatting.Indented
        'Else
        '    writer.Formatting = Formatting.None
        'End If

        writer.QuoteChar = """"c
        json.Serialize(writer, value)

        Dim output As String = sw.ToString()
        writer.Close()
        sw.Close()

        Return output
    End Function

    Private Function GetStatusDataTable(isSuccess As Boolean, message As String) As DataTable
        Dim DtStatus As New DataTable
        DtStatus.Columns.Add("Status")
        DtStatus.Columns.Add("ErrorMessage")
        DtStatus.TableName = "DtStatus"
        Dim dr As DataRow
        dr = DtStatus.NewRow()
        dr(0) = IIf(isSuccess, "Success", "Fail")
        dr(1) = message
        DtStatus.Rows.Add(dr)
        Return DtStatus
    End Function

    Private Function ConvertDataTableToList(ByVal dt As DataTable) As List(Of Dictionary(Of String, String))

        Dim serializer As New System.Web.Script.Serialization.JavaScriptSerializer()
        Dim rows As New List(Of Dictionary(Of String, String))()
        Dim row As Dictionary(Of String, String)

        For Each dr As DataRow In dt.Rows
            row = New Dictionary(Of String, String)()
            For Each col As DataColumn In dt.Columns
                row.Add(col.ColumnName, dr(col))
            Next
            rows.Add(row)
        Next
        Return rows

    End Function

    Private Function GetCurrentMDVersion() As String
        'Try
        Dim godata As clData = HttpContext.Current.Session("goData")
        Dim sMDvesrion As String = godata.GetCurrentMDVersion()
        Return sMDvesrion
        'Catch ex As Exception
        'Throw ex
        'End Try
    End Function

    Private Function ValidateMDVersion(sDevice_MD_Version As String, Optional sCurr_MD_Version As String = "") As Boolean
        'Try

        If String.IsNullOrEmpty(sDevice_MD_Version) Then
            Return True
        End If

        If String.IsNullOrEmpty(sCurr_MD_Version) Then
            sCurr_MD_Version = GetCurrentMDVersion()
        End If

        Return sCurr_MD_Version.Equals(sDevice_MD_Version)

        'Catch ex As Exception
        'Throw ex
        'End Try
    End Function

#End Region

#Region "Attachments Private Functions"

    Private Function GetAttachmentsList(File_Gid_Id As String, FieldName As String) As clServiceOutput

        Dim objclServiceOutput As New clServiceOutput()

        Try

            File_Gid_Id = HttpUtility.UrlDecode(File_Gid_Id)
            FieldName = HttpUtility.UrlDecode(FieldName)

            Dim objclAttachments As New clAttachments()
            Dim dt As DataTable = objclAttachments.GetAttachments(FieldName, File_Gid_Id)

            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then

                Dim strattachment As String = String.Empty

                For i As Integer = 0 To dt.Rows.Count - 1
                    If String.IsNullOrEmpty(strattachment) Then
                        strattachment = dt.Rows(i)("TXT_AttachmentName").ToString() & "~" & dt.Rows(i)("GID_ID").ToString()
                    Else
                        strattachment = strattachment & "|" & dt.Rows(i)("TXT_AttachmentName").ToString() & "~" & dt.Rows(i)("GID_ID").ToString()
                    End If
                Next

                objclServiceOutput.Status = "Success"
                objclServiceOutput.Data = strattachment

            Else
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "No Records Found"
            End If

            Return objclServiceOutput

        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try
    End Function

    Public Function DownloadAttachment(Gid_Id As String, Source As String) As clServiceOutput

        Dim objclServiceOutput As New clServiceOutput()
        Dim objclAttachment As New clAttachments()

        Dim filedata As Byte() = objclAttachment.GetFile(Gid_Id, Source)

        If filedata IsNot Nothing Then
            objclServiceOutput.FileData = filedata
            objclServiceOutput.Status = "Success"
        Else
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = "File Not found"
        End If

        Return objclServiceOutput

    End Function

    Public Function UploadAttachment(ByVal _cldata As cljsondata) As clServiceOutput

        Dim objclServiceOutput As New clServiceOutput()

        Try

            Dim objclAttachments As New clAttachments()

            Dim ViewName As String = GetFields(_cldata, "txt_viewname")
            Dim File_Gid_Id As String = GetFields(_cldata, "txt_file_gid_id")
            Dim FieldName As String = GetFields(_cldata, "txt_fieldname")
            Dim FileName As String = GetFields(_cldata, "txt_filename")
            Dim sFileData As String = GetFields(_cldata, "txt_filedata")

            Dim sUploadStatus As String = objclAttachments.UploadAttachment(ViewName, File_Gid_Id, FieldName, FileName, sFileData)

            If String.IsNullOrEmpty(sUploadStatus) Then
                objclServiceOutput.Data = FileName & " has been uploaded successfully"
                objclServiceOutput.Status = "Success"
            Else
                objclServiceOutput.Data = sUploadStatus
                objclServiceOutput.Status = "Fail"
            End If

            Return objclServiceOutput

        Catch ex As Exception
            objclServiceOutput.Data = ex.ToString()
            objclServiceOutput.Status = "Fail"
            Return objclServiceOutput
        End Try

    End Function

    Public Function DeleteAttachment(Gid_Id As String) As clServiceOutput

        Dim objclServiceOutput As New clServiceOutput()

        Try

            Gid_Id = HttpUtility.UrlDecode(Gid_Id)
            Dim objclAttachments As New clAttachments()
            Dim strretval As String = objclAttachments.DeleteAttachment(Gid_Id)

            If strretval = "Success" Then
                objclServiceOutput.Status = "Success"
                objclServiceOutput.Data = "File Deleted Successfully"
            Else
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = strretval
            End If

            Return objclServiceOutput

        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try
    End Function

#End Region

#Region "Webmail Private Functions"

    Private Function GetMails(folderName As String, daysBack As Integer, Filter1 As String, Filter2 As String, iFirstItem As Integer, iNumberofItems As Integer) As clServiceOutput

        Dim objclserviceoutput As New clServiceOutput()

        Try

            Dim currUsermailsettings = GetUserWebMailSettings()

            If currUsermailsettings Is Nothing Then
                objclserviceoutput.Status = "Fail"
                objclserviceoutput.Data = "You do not have an IMAP server setting. Please configure Webmail Settings in your Personal Options in selltis website."
                Return objclserviceoutput
            End If


            If currUsermailsettings.ServerName <> "" Then

                folderName = HttpUtility.UrlDecode(folderName)
                Filter1 = HttpUtility.UrlDecode(Filter1)
                Filter2 = HttpUtility.UrlDecode(Filter2)

                Dim utility As ImapUtility.ImapUtility = New ImapUtility.ImapUtility
                Dim ds As DataSet
                ds = utility.GetMails(currUsermailsettings.UserName, currUsermailsettings.Password, currUsermailsettings.ServerName, currUsermailsettings.PortNumber, folderName, iFirstItem, iNumberofItems, daysBack, Filter1, Filter2)



                If ds IsNot Nothing AndAlso ds.Tables.Count > 0 Then

                    Dim dt As New DataTable
                    dt = ds.Tables(0)

                    If dt.Rows.Count > 0 Then
                        objclserviceoutput.Status = "Success"
                        objclserviceoutput.Data = ConvertDataTableToJsonString(dt)
                    Else
                        objclserviceoutput.Status = "Fail"
                        objclserviceoutput.Data = "No records found"
                    End If
                Else
                    objclserviceoutput.Status = "Fail"
                    objclserviceoutput.Data = "No records found"
                End If
                Return objclserviceoutput


            Else
                objclserviceoutput.Status = "Fail"
                objclserviceoutput.Data = "You do not have an IMAP server setting. Please configure Webmail Settings in your Personal Options in selltis website."
                Return objclserviceoutput
            End If

        Catch ex As Exception
            objclserviceoutput.Status = "Fail"
            objclserviceoutput.Data = ex.ToString()
            Return objclserviceoutput
        End Try

    End Function

    Private Function GetMailContent(sFolder As String, sUid As String, Optional ByVal sMid As String = "") As clServiceOutput

        Dim objclserviceoutput As New clServiceOutput()

        Try

            Dim mailcontent = GetMailContent(sFolder, sUid)

            If mailcontent IsNot Nothing Then
                objclserviceoutput.Data = GetJsonString(mailcontent)
                objclserviceoutput.Status = "Success"
            Else
                objclserviceoutput.Data = "No data found"
                objclserviceoutput.Status = "Fail"
            End If
            Return objclserviceoutput
        Catch ex As Exception
            objclserviceoutput.Data = ex.ToString()
            objclserviceoutput.Status = "Fail"
            Return objclserviceoutput
        End Try
    End Function

    Private Function GetMailContent(sFolder As String, sUid As String) As ImapUtility.ImapUtility.SellMailItem

        Dim EmailUsername As String
        Dim EmailPwd As String
        Dim serverName As String
        Dim portNumber As Integer

        Dim currUsermailsettings = GetUserWebMailSettings()

        EmailUsername = currUsermailsettings.UserName
        EmailPwd = currUsermailsettings.Password
        serverName = currUsermailsettings.ServerName
        portNumber = currUsermailsettings.PortNumber

        sFolder = HttpUtility.UrlDecode(sFolder)
        sUid = HttpUtility.UrlDecode(sUid)

        sFolder = IIf(String.IsNullOrEmpty(sFolder), "Inbox", sFolder)

        Dim utility As ImapUtility.ImapUtility = New ImapUtility.ImapUtility

        Return utility.GetSelectedMailContent(EmailUsername, EmailPwd, serverName, portNumber, sUid, sFolder, "")

    End Function

    Private Function LogMail(suid As String, sFoldername As String) As clServiceOutput

        Dim objclserviceoutput As New clServiceOutput()

        Try

            Dim LogData = GetMailContent(sFoldername, suid)

            Dim dt As New DataTable()
            dt.Columns.Add("Type")
            dt.Columns.Add("From")
            dt.Columns.Add("To")
            dt.Columns.Add("CC")
            dt.Columns.Add("BCC")
            dt.Columns.Add("Subject")
            dt.Columns.Add("Body")
            dt.Columns.Add("Attachments")
            dt.Columns.Add("Date")
            dt.Columns.Add("Time")
            dt.TableName = "email"

            Dim To_Address As String = ""
            Dim cc_Address As String = ""
            Dim Bcc_Address As String = ""
            Dim Attachments As String = ""

            To_Address = ConvertCollectionToString(LogData.c_To)
            cc_Address = ConvertCollectionToString(LogData.c_CC)
            Bcc_Address = ConvertCollectionToString(LogData.c_BCC)
            Attachments = ConvertCollectionToString(LogData.c_Attachments)

            dt.Rows.Add(sFoldername, LogData.s_From, LogData.c_To.ToString(), LogData.c_CC.ToString(), LogData.c_BCC.ToString(), LogData.s_Subject, LogData.s_TextBody, LogData.c_Attachments.ToString(), LogData.dt_DateReceived, LogData.dt_DateReceived)

            Dim clE As New clEmail
            Dim result = clE.LogMessage(dt)

            If result = "1" Then
                objclserviceoutput.Status = "Success"
                objclserviceoutput.Data = "Log mail done successfully"
            Else
                objclserviceoutput.Status = "Fail"
                objclserviceoutput.Data = result
            End If

            Return objclserviceoutput

        Catch ex As Exception
            objclserviceoutput.Status = "Fail"
            objclserviceoutput.Data = ex.ToString()
            Return objclserviceoutput
        End Try
    End Function

    Private Function Reply(folderName As String, MailItem As ImapUtility.ImapUtility.SellMailItem) As clServiceOutput
        Dim objclserviceoutput As New clServiceOutput()
        Try
            Dim EmailUsername As String
            Dim EmailPwd As String
            Dim serverName As String
            Dim portNumber As Integer
            Dim smtpServer As String
            Dim smtpPortNumber As String
            Dim smtpuserName As String
            Dim smtpPassword As String

            Dim Imapsettings = GetUserWebMailSettings()

            EmailUsername = Imapsettings.UserName
            EmailPwd = Imapsettings.Password
            serverName = Imapsettings.ServerName
            portNumber = Imapsettings.PortNumber
            smtpServer = Imapsettings.SmtpServerName
            smtpPortNumber = Imapsettings.SmtpPortnumber
            smtpuserName = Imapsettings.SmtpUserName
            smtpPassword = Imapsettings.SmtpPassword

            folderName = HttpUtility.UrlDecode(folderName)

            Dim utility As ImapUtility.ImapUtility = New ImapUtility.ImapUtility
            Dim data = utility.SendMail(MailItem, folderName, EmailUsername, EmailPwd, serverName, portNumber, smtpServer, smtpPortNumber, smtpuserName, smtpPassword)

            If data IsNot Nothing Then
                objclserviceoutput.Status = "Success"
                objclserviceoutput.Data = data
            Else
                objclserviceoutput.Status = "Fail"
                objclserviceoutput.Data = "Send mail failed"
            End If
            Return objclserviceoutput
        Catch ex As Exception
            objclserviceoutput.Status = "Fail"
            objclserviceoutput.Data = ex.ToString()
            Return objclserviceoutput
        End Try

    End Function

    Private Function GetUserWebMailSettings() As UserMailSettings
        Dim result As New UserMailSettings
        'Try
        Dim oMeta As clMetaData = HttpContext.Current.Session("goMeta")
        Dim goP As clProject = HttpContext.Current.Session("goP")
        result.UserName = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "IMAP_USER", "")
        Dim encrypPwd = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "IMAP_PASSWORD", "")
        Dim oCrypt2 As New clEncrypt2
        result.Password = oCrypt2.Decrypt(encrypPwd)
        result.ServerName = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "IMAP_SERVER", "")
        result.PortNumber = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "IMAP_SERVER_PORT", "143")
        result.SmtpServerName = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SMTP_SERVER", "")
        result.SmtpPortnumber = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SMTP_SERVER_PORT", "587")
        result.SmtpUserName = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SMTP_USER", "")
        result.SmtpPassword = oMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SMTP_PASSWORD", "")
        Return result
        'Catch ex As Exception
        'Return Nothing
        'End Try
    End Function

    Private Function GetFilter(sFilterText As String, sFieldName As String) As String
        If String.IsNullOrEmpty(sFilterText) Then
            Return ""
        End If

        sFilterText = sFilterText.Replace("AND", System.Environment.NewLine).Replace("'", "")

        Dim parts As String() = sFilterText.Split(System.Environment.NewLine)

        For Each part In parts
            'If part.ToLower() = sFieldName Then
            Dim sData = part.Split(New Char() {"="c})
            If sData IsNot Nothing AndAlso sData.Length = 2 Then
                If sData(0).ToString().ToLower().Trim() = sFieldName Then
                    Dim sretval As String = sData(1).ToString().Trim().Replace("|", "=").Replace("~", " AND ")
                    sretval = sretval.Replace("?^", "|")
                    Return sretval
                    'Return sData(1).ToString().Trim().Replace("|", "=").Replace("~", " AND ")
                End If
            Else
                Return ""
            End If
            'End If
        Next

        Return ""
    End Function

    Private Function GetFields(cldata As cljsondata, sFieldName As String) As String

        Dim field = (From item In cldata.lstData
                     Where item.FieldName.ToLower() = sFieldName
                     Select item)

        If field IsNot Nothing Then
            Return field.FirstOrDefault().FieldValue
        End If

        Return ""
    End Function

    Private Function ConvertCollectionToString(ByVal cAddresses As Collection) As String

        Dim sReturn As String = ""
        If cAddresses Is Nothing Then Return ""

        For Each Addr In cAddresses
            sReturn = sReturn & Addr & ", "
        Next

        If Len(sReturn) > 2 Then
            Return Microsoft.VisualBasic.Strings.Left(sReturn, Len(sReturn) - 2)
        Else
            Return ""
        End If

    End Function

    Private Function GetFolders() As clServiceOutput

        Dim objclserviceoutput As New clServiceOutput()

        Try

            Dim currUsermailsettings = GetUserWebMailSettings()

            If currUsermailsettings Is Nothing Then
                objclserviceoutput.Status = "Fail"
                objclserviceoutput.Data = "You do not have an IMAP server setting. Please configure Webmail Settings in your Personal Options in selltis website."
                Return objclserviceoutput
            End If

            If currUsermailsettings.ServerName <> "" Then

                Dim utility As ImapUtility.ImapUtility = New ImapUtility.ImapUtility

                Dim dt As DataTable
                dt = utility.GetFolders(currUsermailsettings.UserName, currUsermailsettings.Password, currUsermailsettings.ServerName, currUsermailsettings.PortNumber)

                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then

                    Dim dt1 As New DataTable
                    Dim clslist1 As List(Of WebmailFolders) = New List(Of WebmailFolders)

                    dt1.Columns.Add("FolderName")
                    dt1.Columns.Add("ParentFolderName")

                    Dim dv0 As DataView = New DataView(dt, "Level = 0", "", DataViewRowState.CurrentRows)

                    If dv0 IsNot Nothing And dv0.ToTable() IsNot Nothing Then

                        For index = 0 To dv0.ToTable.Rows.Count - 1 Step 1

                            Dim item As New WebmailFolders()
                            item.FolderName = dv0.ToTable().Rows(index)("Name").ToString()

                            Dim dv1 As DataView = New DataView(dt, "Level = 1", "", DataViewRowState.CurrentRows)
                            If dv1 IsNot Nothing And dv1.ToTable() IsNot Nothing Then
                                item.ChildFolder = New List(Of WebmailFolders)()
                                For i As Integer = 0 To dv1.ToTable.Rows.Count - 1
                                    If dv1.ToTable.Rows(i)("Name").ToString().StartsWith(item.FolderName & "/") Then
                                        Dim subitem As New WebmailFolders()
                                        subitem.FolderName = dv1.ToTable.Rows(i)("Name").ToString().Split("/").GetValue(1).ToString()
                                        item.ChildFolder.Add(subitem)
                                    End If
                                Next

                            End If

                            clslist1.Add(item)

                        Next

                    End If

                    objclserviceoutput.Status = "Success"
                    objclserviceoutput.Data = GetJsonString(clslist1)

                Else
                    objclserviceoutput.Status = "Fail"
                    objclserviceoutput.Data = "No records found"
                End If

                Return objclserviceoutput

            Else
                objclserviceoutput.Status = "Fail"
                objclserviceoutput.Data = "You do not have an IMAP server setting. Please configure Webmail Settings in your Personal Options in selltis website."
                Return objclserviceoutput
            End If

        Catch ex As Exception
            objclserviceoutput.Status = "Fail"
            objclserviceoutput.Data = ex.ToString()
            Return objclserviceoutput
        End Try

    End Function


#End Region

#Region "Mobile Custom Fields Private Functions"

    Private Function GetMobileDetailsPage(Page_Gid_Id As String, GID_ID As String) As clServiceOutput

        Dim objclServiceOutput As New clServiceOutput()

        Try

            Dim ds As DataSet = GetMobileDetailsPage_DataSet(Page_Gid_Id, GID_ID)

            objclServiceOutput.Status = "Success"
            objclServiceOutput.Data = Serialize(ds)

            Return objclServiceOutput

        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.ToString()
            Return objclServiceOutput
        End Try

    End Function

    Private Function GetMobileDetailsPage_Object(Page_Gid_Id As String, GID_ID As String) As clOutput

        Dim objcloutput As New clOutput()

        Try

            Dim ds As DataSet = GetMobileDetailsPage_DataSet(Page_Gid_Id, GID_ID)

            If ds IsNot Nothing And ds.Tables.Count = 2 Then

                Dim lstmp = ConvertToList(Of clMP)(ds.Tables(0))
                Dim lstmf = ConvertToList(Of clMF)(ds.Tables(1))

                objcloutput.lstMF = lstmf
                objcloutput.lstMP = lstmp
                objcloutput.objStatus.status = "Success"
                objcloutput.objStatus.Errormessage = ""
                Return objcloutput

            Else
                objcloutput.objStatus.status = "Fail"
                objcloutput.objStatus.Errormessage = "No records found"
                Return objcloutput
            End If

        Catch ex As Exception
            objcloutput.objStatus.status = "Fail"
            objcloutput.objStatus.Errormessage = ex.Message
            Return objcloutput
        End Try

    End Function

    Private Function GetMobileViewMF(PageGidId As String, Filter As String, SortOrder As String, topRecord As String, pageSize As Integer, linksTop As Integer) As clServiceOutput

        Dim objclServiceOutput As New clServiceOutput()

        Try

            Dim dt As DataTable = GetMobileViewMF_DataTable(PageGidId, Filter, SortOrder, topRecord, pageSize, linksTop)

            If dt IsNot Nothing Then
                If dt.Rows.Count > 0 Then
                    objclServiceOutput.Status = "Success"
                    objclServiceOutput.Data = ConvertDataTableToJsonString(dt)
                Else
                    objclServiceOutput.Status = "Fail"
                    objclServiceOutput.Data = "No records found"
                End If
            Else
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "No records found"
            End If

            Return objclServiceOutput

        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.Message
            Return objclServiceOutput
        End Try

    End Function

    Private Function GetMobileViewMF_Object(PageGidId As String, Filter As String, SortOrder As String,
                                            topRecord As String, pageSize As Integer, linksTop As Integer, Latitude As String, Longitude As String, Range As String, MeasureType As String, FilterName As String, UserId As String) As clOutput

        Dim objclOutput As New clOutput()

        Try

            If String.IsNullOrEmpty(PageGidId) Then
                objclOutput.objStatus.status = "Fail"
                objclOutput.objStatus.Errormessage = "Page Gid_Id cannot be blank"
                Return objclOutput
            End If

            Dim ds As DataSet = GetMobileViewMP_DataSet(PageGidId, True)

            If ds Is Nothing Then
                objclOutput.objStatus.status = "Fail"
                objclOutput.objStatus.Errormessage = "Invalid Mobile Page Id"
                Return objclOutput
            End If

            If ds.Tables.Count = 2 AndAlso ds.Tables(0) IsNot Nothing AndAlso ds.Tables(1) IsNot Nothing Then
                Dim lstmp = ConvertToList(Of clMP)(ds.Tables(0))
                Dim lstmf = ConvertToList(Of clMF)(ds.Tables(1))
                objclOutput.lstMF = lstmf
                objclOutput.lstMP = lstmp
            Else
                objclOutput.objStatus.status = "Fail"
                objclOutput.objStatus.Errormessage = "Invalid Mobile Page Id"
                Return objclOutput
            End If

            Dim dt As DataTable = GetMobileViewMF_DataTable(PageGidId, Filter, SortOrder, topRecord, pageSize, linksTop, Latitude, Longitude, Range, MeasureType, FilterName, UserId)

            If dt IsNot Nothing Then

                If dt.Rows.Count > 0 Then

                    Dim dtTrans As New DataTable
                    dtTrans.TableName = "Data"
                    Dim iTransTableColCount = 0

                    For index = 0 To ds.Tables(1).Rows.Count - 1
                        If ds.Tables(1).Rows(index)("TXT_CATEGORY").ToString() <> "Filter" Then
                            dtTrans.Columns.Add(ds.Tables(1).Rows(index)("TXT_FIELDNAMES").ToString())
                            iTransTableColCount = iTransTableColCount + 1
                        End If
                    Next

                    If String.IsNullOrEmpty(Latitude) = False AndAlso String.IsNullOrEmpty(Longitude) = False AndAlso String.IsNullOrEmpty(Range) = False AndAlso String.IsNullOrEmpty(MeasureType) = False Then
                        dtTrans.Columns.Add("COLOR")
                        iTransTableColCount = iTransTableColCount + 1
                    End If

                    Dim dr As DataRow

                    For index = 0 To dt.Rows.Count - 1

                        dr = dtTrans.NewRow

                        For i = 0 To iTransTableColCount - 1

                            Dim str() As String = dtTrans.Columns(i).ColumnName.Split(",")
                            Dim sVal As String = String.Empty

                            For Each sCol As String In str

                                If String.IsNullOrEmpty(sVal) Then
                                    sVal = dt.Rows(index)(sCol).ToString()
                                Else
                                    sVal = sVal & "," & dt.Rows(index)(sCol).ToString()
                                End If

                            Next

                            'Try
                            If dtTrans.Columns(i).ColumnName.ToLower().Contains("mmo_") Then
                                sVal = dt.Rows(index)(dtTrans.Columns(i).ColumnName).ToString()
                                'sVal = sVal.Replace("<p>", "").Replace("</p>", " ").Replace("&nbsp;", "")
                                Dim goUtil As clUtil = New clUtil()
                                sVal = goUtil.StripHTML(sVal)
                                sVal = sVal.Replace("<br />", "").Replace("<BR />", "")
                            End If
                            'Catch ex As Exception

                            'End Try

                            dr(i) = sVal

                        Next

                        dtTrans.Rows.Add(dr)

                    Next

                    objclOutput.Datarows = ConvertDataTableToList(dtTrans)
                    objclOutput.objStatus.status = "Success"
                    objclOutput.objStatus.Errormessage = ""
                Else
                    objclOutput.objStatus.status = "Fail"
                    objclOutput.objStatus.Errormessage = "No records found"
                End If
            Else
                objclOutput.objStatus.status = "Fail"
                objclOutput.objStatus.Errormessage = "No records found"
            End If

            Return objclOutput

        Catch ex As Exception
            objclOutput.objStatus.status = "Fail"
            objclOutput.objStatus.Errormessage = ex.Message
            Return objclOutput
        End Try

    End Function

    Private Function GetMobileViewMP(Page_GID_ID As String) As clServiceOutput
        'returs the MP data
        'and MF filters by page id

        Dim objclServiceOutput As New clServiceOutput()

        Try
            Dim ds As DataSet = GetMobileViewMP_DataSet(Page_GID_ID)

            If ds IsNot Nothing Then
                objclServiceOutput.Status = "Success"
                objclServiceOutput.Data = Serialize(ds)
                Return objclServiceOutput
            Else
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "No records found"
                Return objclServiceOutput
            End If

        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.Message
            Return objclServiceOutput
        End Try

    End Function

    Private Function GetMobileViewMP_Object(Page_GID_ID As String, Optional sShowAllMFFields As Boolean = False) As clOutput
        'returs the MP data
        'and MF filters by page id

        Dim objcloutput As New clOutput()

        Try

            Dim ds As DataSet = GetMobileViewMP_DataSet(Page_GID_ID, sShowAllMFFields)

            If ds IsNot Nothing And ds.Tables.Count = 2 Then

                Dim lstmp = ConvertToList(Of clMP)(ds.Tables(0))
                Dim lstmf = ConvertToList(Of clMF)(ds.Tables(1))
                objcloutput.lstMF = lstmf
                objcloutput.lstMP = lstmp
                objcloutput.objStatus.status = "Success"
                objcloutput.objStatus.Errormessage = ""
                'V_T addded to retruns the current MD version in the reset MD cache
                objcloutput.CurrentMDVersion = GetCurrentMDVersion()

                Return objcloutput

            Else
                objcloutput.objStatus.status = "Fail"
                objcloutput.objStatus.Errormessage = "No records found"
                Return objcloutput
            End If

        Catch ex As Exception
            objcloutput.objStatus.status = "Fail"
            objcloutput.objStatus.Errormessage = ex.Message
            Return objcloutput
        End Try

    End Function

    Private Function GetMobileAdd_EditPage(Page_Gid_Id As String, sGid_id As String, Createfrom As String, Createtype As String) As clServiceOutput

        Dim objclServiceOutput As New clServiceOutput()

        Try
            Dim ds As DataSet = GetMobileAdd_EditPage_DataSet(Page_Gid_Id, sGid_id, Createfrom, Createtype)

            If ds IsNot Nothing Then
                objclServiceOutput.Status = "Success"
                objclServiceOutput.Data = Serialize(ds)
            Else
                objclServiceOutput.Status = "Fail"
                objclServiceOutput.Data = "No records found"
            End If
            Return objclServiceOutput

        Catch ex As Exception
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = ex.Message
            Return objclServiceOutput
        End Try

    End Function

    Private Function GetMobileAdd_EditPage_Object(Page_Gid_Id As String, sGid_id As String, Createfrom As String, Createtype As String) As clOutput

        Dim objclOutput As New clOutput()

        Try
            Dim ds As DataSet = GetMobileAdd_EditPage_DataSet(Page_Gid_Id, sGid_id, Createfrom, Createtype)

            If ds IsNot Nothing AndAlso ds.Tables.Count = 2 Then

                Dim lstmp = ConvertToList(Of clMP)(ds.Tables(0))
                Dim lstmf = ConvertToList(Of clMF)(ds.Tables(1))

                objclOutput.lstMF = lstmf
                objclOutput.lstMP = lstmp
                objclOutput.objStatus.status = "Success"
                objclOutput.objStatus.Errormessage = ""
                Return objclOutput

            Else
                objclOutput.objStatus.status = "Fail"
                objclOutput.objStatus.Errormessage = "No records found"
                Return objclOutput
            End If

        Catch ex As Exception
            objclOutput.objStatus.status = "Fail"
            objclOutput.objStatus.Errormessage = ex.Message
            Return objclOutput
        End Try

    End Function

    Private Function ConvertToList(Of T)(table As DataTable) As IList(Of T)
        'Try
        If table Is Nothing Then
            Return Nothing
        End If
        Dim rows As New List(Of DataRow)()
        For Each row As DataRow In table.Rows
            rows.Add(row)
        Next
        Return ConvertTo(Of T)(rows)
        'Catch ex As Exception
        'Throw ex
        'End Try
    End Function

    Private Function ConvertTo(Of T)(rows As IList(Of DataRow)) As IList(Of T)
        'Try
        Dim list As IList(Of T) = Nothing
        If rows IsNot Nothing Then
            list = New List(Of T)()
            For Each row As DataRow In rows
                Dim item As T = CreateItem(Of T)(row)
                list.Add(item)
            Next
        End If
        Return list
        'Catch ex As Exception
        'Throw ex
        'End Try
    End Function

    Private Function CreateItem(Of T)(row As DataRow) As T

        'Try

        Dim columnName As String
        Dim obj As T = Nothing

        If row IsNot Nothing Then

            obj = Activator.CreateInstance(Of T)()

            For Each column As DataColumn In row.Table.Columns

                columnName = column.ColumnName
                Dim prop As PropertyInfo = obj.[GetType]().GetProperty(columnName)

                Try

                    Dim value As Object = If((row(columnName).[GetType]() = GetType(DBNull)), Nothing, row(columnName))
                    If prop IsNot Nothing Then
                        prop.SetValue(obj, value, Nothing)
                    End If

                Catch
                End Try

            Next

        End If

        Return obj

        'Catch ex As Exception
        'Throw ex
        'End Try

    End Function

    Private Function GetMobileDetailsPage_DataSet(Page_Gid_Id As String, GID_ID As String) As DataSet

        'Try

        Dim sCurrField As String = ""
        Dim godata As clData = HttpContext.Current.Session("goData")
        goTR = HttpContext.Current.Session("goTR")

        Dim rs = New clRowSet(sMPTableName, clC.SELL_READONLY, "GID_ID='" & Page_Gid_Id & "'", "", "GID_ID,TXT_VIEW_NAME,TXT_PAGE_TYPE,TXT_PAGE_TITLE,TXT_KEY,SI__NAVBAR_HOME,SI__NAVBAR_JOURNAL,SI__NAVBAR_CREATELINKED,TXT_CREATELINKED_TYPE,SI__NAVBAR_EDIT,TXT_EDIT_PAGE_ID,SI__NAVBAR_MARKREVIEWED,DTT_CREATIONTIME,TXT_DETAILS_PAGE_ID,SI__NAVBAR_ADD,TXT_ADD_TYPE,TXT_ICON_NAME,SI__PAGE_SIZE,SI__LINKS_TOP_COUNT,TXT_SWIPE1FIELD,TXT_SWIPE2FIELD,TXT_SWIPE1EDITPAGEID,TXT_SWIPE2EDITPAGEID,SI__SEARCH,TXT_SEARCH,SI__ROLLODEX,TXT_ROLLODEX,TXT_SWIPE3FIELD,TXT_SWIPE3EDITPAGEID,TXT_SWIPE1CLPAGEIDS,TXT_SWIPE2CLPAGEIDS,TXT_SWIPE3CLPAGEIDS,SI__CACHEDATA,SI__NEARME,TXT_NEARME_PAGEID,TXT_MAINTITLE,SI__NAVBAR_SCAN,TXT_SCANFIELD,SI__SAVE_CREATE_ANOTHER,SI__SHOWASBTNINHOMEPAGE", , , , , , True)
        rs.ToTable()
        Dim dtMP As DataTable = rs.dtTransTable
        Dim dtMF As New DataTable()
        rs = Nothing
        Dim sViewName As String = String.Empty


        If dtMP.Rows.Count > 0 Then
            sViewName = dtMP.Rows(0)("TXT_VIEW_NAME").ToString()
            Dim rsmf = New clRowSet(sMFTableName, clC.SELL_READONLY, "TXT_MP_GID_ID='" & Page_Gid_Id & "' ", "SR__sortordernew", "GID_ID,TXT_MP_GID_ID,TXT_CATEGORY,TXT_FIELDTYPE,TXT_FIELDNAMES,TXT_LABELNAME,TXT_GROUP_NAME,TXT_TITLE,TXT_WIDGETTYPE,TXT_WIDGETVALUE,TXT_FIELDVALUE,SI__IS_REQUIRED,TXT_REQUIRED_MESSAGE,SR__SORTORDER,TXT_ICON_NAME,TXT_FIELD_LOCATION,SI__FILTER_DEFAULT,TXT_FILTER_SORT,SI__FIELD_VISIBLE,INT_ROWNUMBER,TXT_CRL_TYPE,SI__CRL_VISIBLE,TXT_CRL_PAGEID,TXT_CRL,TXT_CRL_DEFAULT,SR__SORTORDERNEW,SI__SHOWLABEL,TXT_SEARCHFILTERKEY,TXT_SEARCHHINTTEXT,TXT_LNKSEARCHFILTERKEY,TXT_LNKSEARCHHINTTEXT ", , , , , , True)
            rsmf.ToTable()
            dtMF = rsmf.dtTransTable
            rsmf = Nothing
        End If

        dtMF.Columns.Add("DT_DataValue")

        If Not String.IsNullOrEmpty(GID_ID) Then

            Dim rsview = New clRowSet(sViewName, clC.SELL_EDIT, "GID_ID='" & GID_ID & "' ", "", "*", , , , , , , True)
            rsview.ToTable()

            If rsview.dtTransTable.Rows.Count > 0 Then

                ''***************************************************
                ''replace the header page title text keywords with data
                Dim sPageTitleText As String = dtMP.Rows(0)("TXT_PAGE_TITLE").ToString()
                sPageTitleText = GetPageTitleText(sPageTitleText, sViewName, rsview)
                dtMP.Rows(0)("TXT_PAGE_TITLE") = sPageTitleText
                dtMP.AcceptChanges()
                ''*****************************************************

                For i As Integer = 0 To dtMF.Rows.Count - 1 Step 1

                    Dim sFieldName As String = String.Empty
                    Dim sFieldCategory As String = String.Empty
                    Dim sFieldType As String = String.Empty

                    sCurrField = dtMF.Rows(i)("TXT_FIELDNAMES").ToString()
                    sFieldName = dtMF.Rows(i)("TXT_FIELDNAMES").ToString().Trim()
                    sFieldCategory = dtMF.Rows(i)("TXT_CATEGORY").ToString().ToLower()
                    sFieldType = dtMF.Rows(i)("TXT_FIELDTYPE").ToString().ToLower()

                    If sFieldName <> "" And sFieldCategory <> "additional info" Then

                        Dim sval As String = String.Empty

                        If sFieldName.Contains(",") Then

                            Dim sFields() As String = sFieldName.Split(",")

                            For Each StrField As String In sFields

                                Dim sTempval As String = String.Empty

                                If godata.IsFieldValid(sViewName, StrField) Then

                                    If StrField.StartsWith("LNK_") And StrField.Contains("%%") Then

                                        sTempval = rsview.GetFieldVal(StrField)

                                        If sFieldType <> "text" Then 'link field

                                            StrField = StrField.Replace(StrField.Split("%%").GetValue(1), "GID_ID")
                                            sTempval = rsview.GetFieldVal(StrField) & "|" & sTempval

                                            ''get the link table name
                                            Dim sLinkTableName As String = goTR.GetFileFromLinkName(sFieldName)
                                            ''get the details page id of the link table name
                                            Dim rsDetPageInfo = New clRowSet(sMPTableName, 3, "TXT_VIEW_NAME='" & sLinkTableName & "' and TXT_PAGE_TYPE='Details Page'", "", "GID_ID", 1)
                                            rsDetPageInfo.ToTable()
                                            Dim dt As DataTable = rsDetPageInfo.dtTransTable
                                            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                                                sTempval = sTempval & "|" & dt.Rows(0)(0).ToString()
                                            End If
                                            rsDetPageInfo = Nothing

                                        End If

                                    Else
                                        sTempval = rsview.GetFieldVal(StrField)

                                        If StrField.ToLower().Contains("mmo_") Then
                                            'sTempval = sTempval.Replace("<p>", "").Replace("</p>", " ").Replace("&nbsp;", " ")
                                            Dim goUtil As clUtil = New clUtil()
                                            sval = goUtil.StripHTML(sval)
                                            sval = sval.Replace("<br />", "").Replace("<BR />", "")
                                        End If

                                    End If

                                    If String.IsNullOrEmpty(sval) Then
                                        sval = sTempval
                                    Else
                                        sval = sval & "," & sTempval
                                    End If

                                Else
                                    sval = "Error: Invalid Field Name"
                                    GoTo InvalidField
                                End If

                            Next

                        Else

                            If godata.IsFieldValid(sViewName, sFieldName) Then

                                If sFieldName.StartsWith("LNK_") And sFieldName.Contains("%%") Then

                                    sval = rsview.GetFieldVal(sFieldName)

                                    If sFieldType <> "text" Then 'link field

                                        sFieldName = sFieldName.Replace(sFieldName.Split("%%").GetValue(2), "GID_ID")
                                        sval = sval & "|" & rsview.GetFieldVal(sFieldName)

                                        ''get the link table name
                                        Dim sLinkTableName As String = goTR.GetFileFromLinkName(sFieldName)
                                        ''get the details page id of the link table name
                                        Dim rsDetPageInfo = New clRowSet(sMPTableName, 3, "TXT_VIEW_NAME='" & sLinkTableName & "' and TXT_PAGE_TYPE='Details Page'", "", "GID_ID", 1)
                                        rsDetPageInfo.ToTable()
                                        Dim dt As DataTable = rsDetPageInfo.dtTransTable
                                        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                                            sval = sval & "|" & dt.Rows(0)(0).ToString()
                                        End If
                                        rsDetPageInfo = Nothing

                                    End If

                                Else
                                    sval = rsview.GetFieldVal(sFieldName)
                                    If sFieldName.ToLower().Contains("mmo_") Then
                                        'sval = sval.Replace("<p>", "").Replace("</p>", " ").Replace("&nbsp;", " ")
                                        Dim goUtil As clUtil = New clUtil()
                                        sval = goUtil.StripHTML(sval)
                                        sval = sval.Replace("<br />", "").Replace("<BR />", "")
                                    End If
                                End If

                            Else
                                sval = "Error: Invalid Field Name"
                                GoTo InvalidField
                            End If

                        End If

                        dtMF.Rows(i)("DT_DataValue") = sval

                    ElseIf sFieldCategory = "additional info" Then

                        'get the additional info page Id by additional info page
                        Dim sCond As String = String.Empty
                        Dim sViewType As String = String.Empty
                        Dim bIsNewData As Boolean = False

                        If sFieldType.Contains("$") Then

                            'ex: $AC$, $OP$ -- then get the view page id of the table
                            sFieldType = sFieldType.Replace("$", "").ToUpper()

                            If sFieldType.Contains("_") Then
                                ''i.e. new data ex:  $AP_Calendar Page_61343530-6438-3635-584d-31312f31322f$
                                bIsNewData = True
                            End If

                            If bIsNewData = False Then
                                'this code no need to excute for the new data values as it already contaiins the page gidid and page type
                                If sFieldType.Contains("-") Then
                                    Dim sTemp() As String = sFieldType.Split("-")
                                    sFieldType = sTemp.GetValue(0).ToString()
                                    sViewType = sTemp.GetValue(1).ToString()
                                Else
                                    sViewType = "View Page"
                                End If

                                sCond = "txt_view_name='" & sFieldType & "' and txt_page_Type='" & sViewType & "'"

                            End If

                        Else
                            'ex: Properties
                            sCond = "txt_page_type='Additional Info Page' and TXT_View_Name='" & sViewName & "' and txt_page_title='" & sFieldType & "'"
                            sViewType = "Additional Info Page"
                        End If

                        If bIsNewData = False Then

                            If sViewType.ToLower() = "view page" Then ''fix specific to IOS
                                sViewType = "View Page"
                            End If

                            Dim rsAddInfo = New clRowSet(sMPTableName, 3, sCond, "", "GID_ID", 1)
                            rsAddInfo.ToTable()
                            Dim dt As DataTable = rsAddInfo.dtTransTable
                            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                                dtMF.Rows(i)("DT_DataValue") = dt.Rows(0)(0).ToString().ToLower()
                                dtMF.Rows(i)("TXT_WIDGETTYPE") = sViewType
                            End If
                            rsAddInfo = Nothing

                        Else ''true i.e. new data ex:  $AP_Calendar Page_61343530-6438-3635-584d-31312f31322f$

                            Dim items() As String
                            items = sFieldType.Split("_")
                            dtMF.Rows(i)("DT_DataValue") = items.GetValue(2).ToString().ToLower() 'Page GIDID
                            dtMF.Rows(i)("TXT_WIDGETTYPE") = items.GetValue(1).ToString()  '' Page Type

                            If dtMF.Rows(i)("TXT_WIDGETTYPE").ToString().ToLower() = "view page" Then ''fix specific to IOS
                                dtMF.Rows(i)("TXT_WIDGETTYPE") = "View Page"
                            End If

                        End If

                    Else
                        dtMF.Rows(i)("DT_DataValue") = ""
                    End If

                Next

            End If

            rsview = Nothing

        End If

        dtMF.AcceptChanges()

        Dim ds As New DataSet

        ds.Tables.Add(dtMP)
        ds.Tables.Add(dtMF)

        Return ds

InvalidField: Throw New Exception("Column '" & sCurrField & "' does not belong to table " & sViewName & ".")

        'Catch ex As Exception
        'Throw ex
        'End Try

    End Function

    Private Function GetMobileViewMF_DataTable(PageGidId As String, Filter As String, SortOrder As String, _
                                               topRecord As String, pageSize As Integer, linksTop As Integer, Optional Latitude As String = "", Optional Longitude As String = "", Optional Range As String = "", Optional MeasureType As String = "", Optional FilterName As String = "", Optional UserId As String = "") As DataTable

        'Try

        Dim rs = New clRowSet(sMPTableName, clC.SELL_READONLY, "GID_ID='" & PageGidId & "' ", "", "GID_ID,TXT_VIEW_NAME,TXT_PAGE_TYPE,TXT_PAGE_TITLE,TXT_KEY,SI__NAVBAR_HOME,SI__NAVBAR_JOURNAL,SI__NAVBAR_CREATELINKED,TXT_CREATELINKED_TYPE,SI__NAVBAR_EDIT,TXT_EDIT_PAGE_ID,SI__NAVBAR_MARKREVIEWED,DTT_CREATIONTIME,TXT_DETAILS_PAGE_ID,SI__LINKS_TOP_COUNT,SI__CACHEDATA,SI__NEARME,TXT_NEARME_PAGEID,TXT_MAINTITLE,TXT_ICON_NAME,SI__NAVBAR_SCAN,TXT_SCANFIELD,SI__SAVE_CREATE_ANOTHER,SI__SHOWASBTNINHOMEPAGE ", , , , , , True)
        rs.ToTable()
        Dim dtMP As DataTable = rs.dtTransTable
        Dim dtMF As New DataTable()
        rs = Nothing
        Dim sViewName As String = String.Empty
        Dim sCurrField As String = String.Empty
        Dim sPageType As String = String.Empty

        If dtMP.Rows.Count > 0 Then

            Dim godata As clData = HttpContext.Current.Session("goData")
            Dim goTr As clTransform = HttpContext.Current.Session("goTr")

            sViewName = dtMP.Rows(0)("TXT_VIEW_NAME").ToString()
            sPageType = dtMP.Rows(0)("TXT_PAGE_TYPE").ToString()

            ''check near by
            If sPageType = "Map Page" Then
                Return godata.GetNearBy(Latitude, Longitude, Range, MeasureType, FilterName, UserId, PageGidId)
            End If

            Dim rsmf = New clRowSet(sMFTableName, clC.SELL_READONLY, "TXT_MP_GID_ID='" & dtMP.Rows(0)("GID_ID").ToString() & "' AND (TXT_CATEGORY='Header' OR TXT_CATEGORY = 'Body')  ", "SR__sortordernew", "TXT_FIELDNAMES", , , , , , True)
            rsmf.ToTable()
            dtMF = rsmf.dtTransTable
            rsmf = Nothing

            Dim sFieldNames As String = String.Empty

            For index = 0 To dtMF.Rows.Count - 1

                sCurrField = dtMF.Rows(index)("TXT_FIELDNAMES").ToString()

                'V_T this code is for validating the fields
                If sCurrField.Contains(",") Then
                    Dim str() = sCurrField.Split(",")
                    For Each s As String In str
                        sCurrField = s
                        If godata.IsFieldValid(sViewName, sCurrField.Trim()) = False Then
                            GoTo InvalidField
                        End If
                    Next
                Else
                    If godata.IsFieldValid(sViewName, sCurrField.Trim()) = False Then
                        GoTo InvalidField
                    End If
                End If


                If String.IsNullOrEmpty(sFieldNames) Then
                    sFieldNames = dtMF.Rows(index)("TXT_FIELDNAMES").ToString().Trim()
                Else
                    sFieldNames = sFieldNames & "," & dtMF.Rows(index)("TXT_FIELDNAMES").ToString().Trim()
                End If
            Next

            Dim iILinkstop As Integer = CInt(dtMP.Rows(0)("SI__LINKS_TOP_COUNT").ToString())

            Dim oUtil As New clUtil
            If String.IsNullOrEmpty(topRecord) Then
                topRecord = ""
            End If

            Dim topRecordMD As String = oUtil.GetTopRec(sViewName, SortOrder, topRecord)
            Dim rsdata As New clRowSet(sViewName, clC.SELL_READONLY, Filter, SortOrder, sFieldNames, pageSize + 1, , "", , , topRecordMD, False, False, False, False, iILinkstop, "", False, False, 1800)

            rsdata.ToTable()

            If rsdata.dtTransTable.Rows.Count > 0 Then

                Dim dt As DataTable = rsdata.dtTransTable.Clone

                Dim startCount As Integer
                Dim endCount As Integer

                startCount = 0
                endCount = rsdata.dtTransTable.Rows.Count

                If String.IsNullOrEmpty(topRecordMD) Then
                    startCount = 0
                    If (rsdata.dtTransTable.Rows.Count > pageSize) Then
                        endCount = endCount - 1
                    End If
                Else
                    startCount = 1
                End If

                endCount = endCount - 1

                For i As Integer = startCount To endCount
                    dt.ImportRow(rsdata.dtTransTable.Rows(i))
                Next

                Return dt

            Else
                Return Nothing
            End If

        Else
            Return Nothing
        End If

InvalidField: Throw New Exception("Column '" & sCurrField & "' does not belong to table " & sViewName & ".")

        'Catch ex As Exception
        'Throw ex
        'End Try

    End Function

    Private Function GetMobileViewMP_DataSet(Page_GID_ID As String, Optional bIsShowAllMFFields As Boolean = False) As DataSet
        'returns the MP data
        'and MF filters by page id

        'Try
        Dim rs As clRowSet

        If String.IsNullOrEmpty(Page_GID_ID) Then
            rs = New clRowSet(sMPTableName, clC.SELL_READONLY, "CHK_PAGEISFORQUICKFORMS=0", "SR__DISPLAYORDER", "GID_ID,TXT_VIEW_NAME,TXT_PAGE_TYPE,TXT_PAGE_TITLE,TXT_KEY,SI__NAVBAR_HOME,SI__NAVBAR_JOURNAL,SI__NAVBAR_CREATELINKED,TXT_CREATELINKED_TYPE,SI__NAVBAR_EDIT,TXT_EDIT_PAGE_ID,SI__NAVBAR_MARKREVIEWED,DTT_CREATIONTIME,TXT_DETAILS_PAGE_ID,SI__NAVBAR_ADD,TXT_ADD_TYPE,TXT_ICON_NAME,SI__PAGE_SIZE,SI__LINKS_TOP_COUNT,TXT_SWIPE1FIELD,TXT_SWIPE2FIELD,TXT_SWIPE1EDITPAGEID,TXT_SWIPE2EDITPAGEID,SI__SEARCH,TXT_SEARCH,SI__ROLLODEX,TXT_ROLLODEX,TXT_SWIPE3FIELD,TXT_SWIPE3EDITPAGEID,TXT_SWIPE1CLPAGEIDS,TXT_SWIPE2CLPAGEIDS,TXT_SWIPE3CLPAGEIDS,SI__CACHEDATA,SI__NEARME,TXT_NEARME_PAGEID,TXT_MAINTITLE,SI__NAVBAR_SCAN,TXT_SCANFIELD,SI__SAVE_CREATE_ANOTHER,SI__SHOWASBTNINHOMEPAGE", , , , , , True)
        Else
            rs = New clRowSet(sMPTableName, clC.SELL_READONLY, "GID_ID='" & Page_GID_ID & "'", "SR__DISPLAYORDER", "GID_ID,TXT_VIEW_NAME,TXT_PAGE_TYPE,TXT_PAGE_TITLE,TXT_KEY,SI__NAVBAR_HOME,SI__NAVBAR_JOURNAL,SI__NAVBAR_CREATELINKED,TXT_CREATELINKED_TYPE,SI__NAVBAR_EDIT,TXT_EDIT_PAGE_ID,SI__NAVBAR_MARKREVIEWED,DTT_CREATIONTIME,TXT_DETAILS_PAGE_ID,SI__NAVBAR_ADD,TXT_ADD_TYPE,TXT_ICON_NAME,SI__PAGE_SIZE,SI__LINKS_TOP_COUNT,TXT_SWIPE1FIELD,TXT_SWIPE2FIELD,TXT_SWIPE1EDITPAGEID,TXT_SWIPE2EDITPAGEID,SI__SEARCH,TXT_SEARCH,SI__ROLLODEX,TXT_ROLLODEX,TXT_SWIPE3FIELD,TXT_SWIPE3EDITPAGEID,TXT_SWIPE1CLPAGEIDS,TXT_SWIPE2CLPAGEIDS,TXT_SWIPE3CLPAGEIDS,SI__CACHEDATA,SI__NEARME,TXT_NEARME_PAGEID,TXT_MAINTITLE,SI__NAVBAR_SCAN,TXT_SCANFIELD,SI__SAVE_CREATE_ANOTHER,SI__SHOWASBTNINHOMEPAGE", , , , , , True)
        End If

        rs.ToTable()

        Dim dtMP As DataTable = rs.dtTransTable
        rs = Nothing

        Dim sViewName As String = String.Empty
        Dim ds As New DataSet

        If dtMP.Rows.Count > 0 Then

            ds.Tables.Add(dtMP)

            Dim sPageType As String = String.Empty '$Sys$ -- Create Linked (Sys)

            For index = 0 To dtMP.Rows.Count - 1

                Page_GID_ID = dtMP.Rows(index)("GID_ID").ToString()
                sPageType = dtMP.Rows(index)("TXT_PAGE_TYPE").ToString()

                Dim sCond As String = String.Empty
                If bIsShowAllMFFields Then
                    sCond = "TXT_MP_GID_ID='" & Page_GID_ID & "'"
                Else
                    sCond = "TXT_MP_GID_ID='" & Page_GID_ID & "' AND TXT_CATEGORY='Filter' "
                End If

                Dim rsmf = New clRowSet(sMFTableName, clC.SELL_READONLY, sCond, "SR__sortordernew", "GID_ID,TXT_MP_GID_ID,TXT_FIELDNAMES,TXT_LABELNAME,SI__FILTER_DEFAULT,TXT_FILTER_SORT,TXT_FIELD_LOCATION,SI__FIELD_VISIBLE,TXT_CRL_TYPE,SI__CRL_VISIBLE,TXT_CRL_PAGEID,TXT_CRL,TXT_CRL_DEFAULT,TXT_CATEGORY,INT_ROWNUMBER,TXT_MAP_LINKBOX,SI__SHOWLABEL,TXT_SEARCHFILTERKEY,TXT_SEARCHHINTTEXT,TXT_LNKSEARCHFILTERKEY,TXT_LNKSEARCHHINTTEXT ", , , , , , True)
                rsmf.ToTable()

                Dim dtMF As New DataTable()
                dtMF = rsmf.dtTransTable
                If dtMF.Rows.Count > 0 Then
                    If ds.Tables.Count = 2 Then
                        ds.Tables(1).Merge(dtMF)
                    Else
                        ds.Tables.Add(dtMF)
                    End If
                End If
                rsmf = Nothing

                ''create linked page type fields
                If sPageType = "$Sys$-CreateLinked" Then
                    Dim rsmf1 = New clRowSet(sMFTableName, clC.SELL_READONLY, "TXT_MP_GID_ID='" & Page_GID_ID & "' ", "SR__sortordernew", "GID_ID,TXT_MP_GID_ID,TXT_FIELDNAMES,TXT_LABELNAME,SI__FILTER_DEFAULT,TXT_FILTER_SORT,TXT_FIELD_LOCATION,SI__FIELD_VISIBLE,TXT_CRL_TYPE,SI__CRL_VISIBLE,TXT_CRL_PAGEID,TXT_CRL,TXT_CRL_DEFAULT,TXT_CATEGORY,INT_ROWNUMBER,TXT_MAP_LINKBOX,SI__SHOWLABEL,TXT_SEARCHFILTERKEY,TXT_SEARCHHINTTEXT,TXT_LNKSEARCHFILTERKEY,TXT_LNKSEARCHHINTTEXT ", , , , , , True)
                    rsmf1.ToTable()

                    Dim dtMF1 As New DataTable()
                    dtMF1 = rsmf1.dtTransTable
                    If dtMF1.Rows.Count > 0 Then
                        If ds.Tables.Count = 2 Then
                            ds.Tables(1).Merge(dtMF1)
                        Else
                            ds.Tables.Add(dtMF1)
                        End If
                    End If
                    rsmf1 = Nothing
                End If

                sPageType = String.Empty

            Next

        End If

        Return ds

        'Catch ex As Exception
        'Throw ex
        'End Try

    End Function

    Private Function GetMobileAdd_EditPage_DataSet(Page_Gid_Id As String, sGid_id As String, Createfrom As String, Createtype As String) As DataSet

        'Try
        Dim godata As clData = HttpContext.Current.Session("goData")
        Dim goTr As clTransform = HttpContext.Current.Session("goTr")
        Dim sCurrField As String = ""

        Dim rs = New clRowSet(sMPTableName, clC.SELL_READONLY, "GID_ID='" & Page_Gid_Id & "'", "", "GID_ID,TXT_VIEW_NAME,TXT_PAGE_TYPE,TXT_PAGE_TITLE,TXT_KEY,SI__NAVBAR_HOME,SI__NAVBAR_JOURNAL,SI__NAVBAR_CREATELINKED,TXT_CREATELINKED_TYPE,SI__NAVBAR_EDIT,TXT_EDIT_PAGE_ID,SI__NAVBAR_MARKREVIEWED,TXT_DETAILS_PAGE_ID,TXT_ICON_NAME,SI__CACHEDATA,SI__NEARME,TXT_NEARME_PAGEID,TXT_MAINTITLE,SI__NAVBAR_SCAN,TXT_SCANFIELD,SI__SAVE_CREATE_ANOTHER,SI__SHOWASBTNINHOMEPAGE", , , , , , True)
        rs.ToTable()
        Dim dtMP As DataTable = rs.dtTransTable
        Dim dtMF As New DataTable()
        rs = Nothing
        Dim sViewName As String = String.Empty

        If dtMP.Rows.Count > 0 Then
            sViewName = dtMP.Rows(0)("TXT_VIEW_NAME").ToString()
            Dim rsmf = New clRowSet(sMFTableName, clC.SELL_READONLY, "TXT_MP_GID_ID='" & Page_Gid_Id & "' AND SI__FIELD_VISIBLE='1' ", "SR__sortordernew", "GID_ID,TXT_MP_GID_ID,TXT_CATEGORY,TXT_FIELDTYPE,TXT_FIELDNAMES,TXT_LABELNAME,TXT_GROUP_NAME,TXT_TITLE,TXT_WIDGETTYPE,TXT_WIDGETVALUE,TXT_FIELDVALUE,TXT_ICON_NAME,SI__IS_REQUIRED,TXT_REQUIRED_MESSAGE,SR__SORTORDER,TXT_ICON_NAME,TXT_FIELD_LOCATION,SI__FILTER_DEFAULT,TXT_FILTER_SORT,SI__FIELD_VISIBLE,INT_ROWNUMBER,TXT_MAP_LINKBOX,TXT_SEARCHFILTERKEY,TXT_SEARCHHINTTEXT,TXT_LNKSEARCHFILTERKEY,TXT_LNKSEARCHHINTTEXT ", , , , , , True)
            rsmf.ToTable()
            dtMF = rsmf.dtTransTable
            rsmf = Nothing
        End If

        dtMF.Columns.Add("DT_DataValue")

        Dim rsdata As clRowSet = Nothing
        Dim sReqFileds As String = ""
        Dim sHideFields As String = ""

        Try
            If (Not String.IsNullOrEmpty(sGid_id)) Then
                Createfrom = sGid_id ''Edit Mode
            End If

            Dim _form As New Selltis.Core.Form(sViewName, Createfrom, Createtype)
            If (_form IsNot Nothing AndAlso _form.TableName <> "") Then
                Dim _scriptManager As New Selltis.Core.ScriptManager
                Dim par_doCallingObject As Object = _form
                Dim par_oReturn As Object = Nothing
                Dim par_bRunNext As Boolean = True
                Dim par_sSections As String = ""
                _scriptManager.RunScript(_form.TableName + "_FormOnLoadRecord", par_doCallingObject, par_oReturn, par_bRunNext, par_sSections, Nothing)
                _form = par_doCallingObject
                rsdata = _form.doRS
                sReqFileds = Convert.ToString(_form.oVar.GetVar("MobileReqFields"))
                sHideFields = Convert.ToString(_form.oVar.GetVar("MobileHideFields"))
            End If
        Catch ex As Exception
            goLog.Log("SelltisMobileService:Error", ex.ToString, clC.SELL_LOGLEVEL_DEBUG, , , , 2)
        End Try

        If rsdata Is Nothing Then
            If String.IsNullOrEmpty(sGid_id) Then
                rsdata = New clRowSet(sViewName, clC.SELL_ADD, "", "", "**", -1, "", "", Createtype, Createfrom, , True) 'Pre Add
            Else
                rsdata = New clRowSet(sViewName, clC.SELL_EDIT, "GID_ID='" & sGid_id & "' ", "", "*", , , , , , , True)
            End If
        End If



        rsdata.ToTable()

        If rsdata.dtTransTable.Rows.Count > 0 Then

            ''***************************************************
            ''replace the header page title text keywords with data
            Dim sPageTitleText As String = dtMP.Rows(0)("TXT_PAGE_TITLE").ToString()
            sPageTitleText = GetPageTitleText(sPageTitleText, sViewName, rsdata)
            dtMP.Rows(0)("TXT_PAGE_TITLE") = sPageTitleText
            dtMP.AcceptChanges()
            ''*****************************************************

            For i As Integer = 0 To dtMF.Rows.Count - 1 Step 1

                Dim sFieldName As String = String.Empty
                Dim sControlType As String = String.Empty

                sCurrField = dtMF.Rows(i)("TXT_FIELDNAMES").ToString().Trim()
                sFieldName = dtMF.Rows(i)("TXT_FIELDNAMES").ToString().Trim()
                sControlType = dtMF.Rows(i)("TXT_WIDGETTYPE").ToString().ToLower()

                If sFieldName <> "" And godata.IsFieldValid(sViewName, sFieldName) Then

                    Dim sval As String = String.Empty

                    If sFieldName.Contains(",") Then

                        Dim sFields() As String = sFieldName.Split(",")

                        For Each StrField As String In sFields

                            Dim sTempval As String = String.Empty

                            If sFieldName.StartsWith("LNK_") And sFieldName.Contains("%%") Then
                                sTempval = rsdata.GetFieldVal(StrField)
                                sFieldName = sFieldName.Replace(sFieldName.Split("%%").GetValue(2), "GID_ID")
                                sTempval = rsdata.GetFieldVal(sFieldName) & "~" & sval
                            Else
                                sTempval = rsdata.GetFieldVal(StrField)
                            End If

                            If sFieldName.StartsWith("mmo_") Then
                                Dim goUtil As clUtil = New clUtil()
                                sval = goUtil.StripHTML(sval)
                                sval = sval.Replace("<br />", "").Replace("<BR />", "")
                            End If

                            If String.IsNullOrEmpty(sval) Then
                                sval = sTempval
                            Else
                                sval = sval & "|" & sTempval
                            End If

                        Next

                    Else

                        If sFieldName.StartsWith("LNK_") And sFieldName.Contains("%%") Then
                            sval = rsdata.GetFieldVal(sFieldName)
                            sFieldName = sFieldName.Replace(sFieldName.Split("%%").GetValue(2), "GID_ID")
                            sval = rsdata.GetFieldVal(sFieldName) & "~" & sval
                        Else
                            sval = rsdata.GetFieldVal(sFieldName)
                        End If

                        If sControlType = "dropdownlist" Then
                            'get the MLS Values
                            Dim objList As New clList()
                            Dim sMLSList As String = String.Empty
                            sMLSList = objList.GetList(sViewName, sFieldName).CopyAllInString().Replace(vbCrLf, "|")
                            dtMF.Rows(i)("TXT_FIELDVALUE") = sMLSList
                        ElseIf sControlType = "linkbox" Then
                            'get the link type & link table name
                            Dim sLinkType As String = godata.LKGetType(sViewName, sFieldName)
                            Dim sLinkTableName As String = goTr.GetFileFromLinkName(sFieldName)
                            dtMF.Rows(i)("TXT_FIELDVALUE") = sLinkType & "|" & sLinkTableName

                            If sLinkType = "NN" Then

                                ''sval contains the data in the below format
                                '' GIDIDs (newline sepearted) & SysNames(newline seperated) seperated with ~
                                '' the below code transforms the sval data to GIDID~SYSName seperated with '|'
                                ''ex data
                                '75ad7ca3-e963-4b5e-434e-a4a700a4bc75
                                '50798d00-7e3d-4196-434e-a46500a4b947~GLS RRRR, Glsb (AAA111)
                                'Kumar, PRatap 040 667788 Android Developer (Infodat pvt tech)                                   

                                Dim sVals() As String = sval.Split("~") 'part1 - gidids & part2 - sysnames
                                Dim sTempval As String = String.Empty
                                Dim sPart1 As String = ""
                                Dim sPart2 As String = ""

                                If sVals.Length > 1 Then

                                    sPart1 = sVals.GetValue(0).ToString()
                                    sPart2 = sVals.GetValue(1).ToString()

                                    Dim sVals1() As String = sPart1.Split(Environment.NewLine)
                                    Dim sVals2() As String = sPart2.Split(Environment.NewLine)

                                    For index As Integer = 0 To sVals1.Length - 1
                                        If String.IsNullOrEmpty(sTempval) Then
                                            sTempval = sVals1.GetValue(index).ToString() & "~" & sVals2.GetValue(index).ToString()
                                        Else
                                            sTempval = sTempval & "|" & sVals1.GetValue(index).ToString() & "~" & sVals2.GetValue(index).ToString()
                                        End If
                                    Next

                                End If

                                sval = sTempval
                            End If
                        ElseIf sFieldName.StartsWith("mmo_") Then
                            Dim goUtil As clUtil = New clUtil()
                            sval = goUtil.StripHTML(sval)
                            sval = sval.Replace("<br />", "").Replace("<BR />", "")
                        End If

                    End If

                    dtMF.Rows(i)("DT_DataValue") = sval

                Else
                    dtMF.Rows(i)("DT_DataValue") = "Error: Invalid Field Name"
                    GoTo InvalidField
                End If

                ''sReqFileds
                ''sHideFields

                If sReqFileds.Contains(sFieldName) Then
                    dtMF.Rows(i)("SI__IS_REQUIRED") = 1
                End If

                If sHideFields.Contains(sFieldName) Then
                    dtMF.Rows(i)("SI__FIELD_VISIBLE") = 0
                End If

            Next

        End If

        rsdata = Nothing

        dtMF.AcceptChanges()

        Dim ds As New DataSet
        ds.Tables.Add(dtMP)
        ds.Tables.Add(dtMF)

        Return ds

InvalidField: Throw New Exception("Column '" & sCurrField & "' does not belong to table " & sViewName & ".")

        'Catch ex As Exception
        'Throw ex
        'End Try

    End Function

    'FormatDataTable
    Private Function FormatDataTable(dt As DataTable) As DataTable
        If dt.Columns.Count > 0 Then
            Dim goUtil As clUtil = New clUtil()
            For i As Int32 = 0 To dt.Columns.Count - 1
                If dt.Columns(i).ColumnName.ToUpper().Contains("MMO_") Then
                    For j As Int32 = 0 To dt.Rows.Count - 1
                        dt(j)(i) = goUtil.StripHTML(dt(j)(i).ToString())
                    Next
                End If
            Next
            Return dt
        End If
        Return dt
    End Function

    Private Function GetPageTitleText(sPageTitleText As String, sViewName As String, rsdata As clRowSet) As String

        If sPageTitleText.Contains("[") AndAlso sPageTitleText.Contains("]") Then

            Dim godata As clData = HttpContext.Current.Session("goData")
            Dim goTr As clTransform = HttpContext.Current.Session("goTr")

            Dim index1 As Integer = sPageTitleText.IndexOf("["c)
            Dim index2 As Integer = sPageTitleText.IndexOf("]"c)

            If index1 >= 0 AndAlso index2 > 0 Then

                Dim sKeyword = sPageTitleText.Substring(index1 + 1, (index2 - (index1 + 1)))

                Dim sKeywords() = sKeyword.Split(",")

                Dim sTitleval As String = String.Empty

                For Each skey As String In sKeywords

                    If godata.IsFieldValid(sViewName, skey) Then

                        If String.IsNullOrEmpty(sTitleval) Then
                            sTitleval = rsdata.GetFieldVal(skey)
                        Else
                            sTitleval = sTitleval & "," & rsdata.GetFieldVal(skey)
                        End If

                    End If

                Next

                sPageTitleText = sPageTitleText.Replace(sKeyword, sTitleval).Replace("[", "").Replace("]", "").Trim()

            End If

        End If

        Return sPageTitleText

    End Function

#End Region



End Class




<ServiceContract> _
Public Interface ISelltisMobileService

    <OperationContract()>
    Sub DoWork()

    '<OperationContract()>
    'Function MPRP(userName As String, password As String, sPageId As String) As MobileProfilePage

    <OperationContract()>
    Function Login(userName As String, password As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput

    <OperationContract()>
    Function LoginNew(userName As String, password As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutputForLogin

    <OperationContract()>
    Function LoadSiteSettings() As clSiteSettings

    '<OperationContract()>
    'Function LoginWithEmail(EmailID As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutputForLogin

    <OperationContract()>
    Function GetData(userName As String, password As String, filename As String, filter As String, sortorder As String, field As String, recordstart As Integer, recordend As Integer, iLinksTop As Integer, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput

    <OperationContract()>
    Function GetDataBrowse(userName As String, password As String, filename As String, filter As String, sortorder As String, field As String, topRecord As String, pageSize As Integer, iLinksTop As Integer, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput

    <OperationContract()>
    Function GetDataPreAdd(userName As String, password As String, filename As String, field As String, createType As String, createFrom As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput

    <OperationContract()>
    Function SaveData(cldata As cljsondata) As clServiceOutput

    <OperationContract()>
    Function SubmitTwoFactorAuth(AuthCode As String, UserId As String) As clServiceOutput

    <OperationContract()>
    Function UpdateFCMToken(_clValidateToken As clFCMUpdate) As clServiceOutput

    <OperationContract()>
    Function GetMLSValues(userName As String, password As String, tableName As String, fieldName As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput

    <OperationContract()>
    Function GetSingleMetaData(userName As String, password As String, sectionname As String, pagename As String, propertyname As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput

    <OperationContract()>
    Function GetMetaData(userName As String, password As String, sectionname As String, pagename As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput

    <OperationContract()>
    Function CheckAddPermissions(userName As String, password As String, filename As String, App_Version As String, App_DeviceName As String) As clServiceOutput

    <OperationContract()>
    Function Get_MobileVersionCode(iType As Integer) As clversion
    <OperationContract()>
    Function CheckRecordEditPermissions(userName As String, password As String, RecordId As String, App_Version As String, App_DeviceName As String) As clServiceOutput

    '<OperationContract()>
    'Function TestPHP(name As String) As String

    '<OperationContract()>
    'Function AddActivity(Notes As String, Type As String, PURPOSE As String, IsMail As String) As String

    <OperationContract()>
    Function GetDataObject(userName As String, password As String, filename As String, filter As String, sortorder As String, field As String, recordstart As Integer, recordend As Integer, iLinksTop As Integer, App_Version As String, App_DeviceName As String, MD_Version As String) As clOutput

    <OperationContract()>
    Function GetDataObject2(userName As String, password As String, filename As String, filter As String, condition As String, sortorder As String, field As String, recordstart As Integer, recordend As Integer, iLinksTop As Integer, App_Version As String, App_DeviceName As String, MD_Version As String, IsServiceCall As String) As clOutput

    <OperationContract()>
    Function DeleteAttachment(userName As String, password As String, GidId As String, App_Version As String, App_DeviceName As String, MD_Version As String) As clServiceOutput

    <OperationContract()>
    Function GetpValue() As String

End Interface



