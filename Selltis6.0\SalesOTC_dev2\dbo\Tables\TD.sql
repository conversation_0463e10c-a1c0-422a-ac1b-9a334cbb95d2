﻿CREATE TABLE [dbo].[TD] (
    [GID_ID]                      UNIQUEIDENTIFIER CONSTRAINT [DF_TD_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'TD',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                      BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                    NVARCHAR (80)    NULL,
    [FIL_Attachments]             NTEXT            NULL,
    [CHK_Completed]               TINYINT          NULL,
    [DTT_CreationTime]            DATETIME         CONSTRAINT [DF_TD_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [DTT_DateCompleted]           DATETIME         NULL,
    [CHK_DemoData]                TINYINT          NULL,
    [TXT_Description]             NVARCHAR (50)    NULL,
    [DTT_DueTime]                 DATETIME         NULL,
    [DTT_EnteredDate]             DATETIME         NULL,
    [FIL_File]                    NTEXT            NULL,
    [MMO_History]                 NTEXT            NULL,
    [MMO_Notes]                   NTEXT            NULL,
    [CHK_ExtDevReview]            TINYINT          NULL,
    [CHK_Open]                    TINYINT          NULL,
    [MLS_Priority]                SMALLINT         NULL,
    [CHK_Received]                TINYINT          NULL,
    [CHK_Report]                  TINYINT          CONSTRAINT [DF_ToDo_CHK_Report] DEFAULT ((1)) NULL,
    [DTT_StartDate]               DATETIME         NULL,
    [MLS_Status]                  SMALLINT         NULL,
    [MLS_Type]                    SMALLINT         NULL,
    [URL_URLs]                    NTEXT            NULL,
    [TXT_ModBy]                   VARCHAR (4)      NULL,
    [DTT_ModTime]                 DATETIME         CONSTRAINT [DF_TD_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [MMO_ImportData]              NTEXT            NULL,
    [MMO_PalmData]                NTEXT            NULL,
    [SI__ShareState]              TINYINT          CONSTRAINT [DF_TD_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]            UNIQUEIDENTIFIER NULL,
    [GID_From_SO]                 UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]                VARCHAR (50)     NULL,
    [SI__DueTimeDay]              TINYINT          NULL,
    [SI__DueTimeMonth]            TINYINT          NULL,
    [TXT_DueTimeYear]             CHAR (4)         NULL,
    [TXT_ExternalID]              NVARCHAR (120)   NULL,
    [TXT_ExternalSource]          VARCHAR (10)     NULL,
    [TXT_ImpJobID]                VARCHAR (20)     NULL,
    [ADR_Attachments]             NTEXT            NULL,
    [TXT_SERVICETICKET]           VARCHAR (16)     NULL,
    [GID_ASSIGNEDTO_US]           UNIQUEIDENTIFIER NULL,
    [SI__EstimatedTimeToComplete] AS               (datediff(day,[DTT_STARTDATE],[DTT_DueTime])+(1)),
    [SI__ActualTimeToComplete]    AS               (case [CHK_Completed] when (1) then datediff(day,[DTT_STARTDATE],[DTT_DateCompleted])+(1) else (0) end),
    [CHK_DUEALERTSENT]            TINYINT          NULL,
    [GID_RELATED_TC]              UNIQUEIDENTIFIER NULL,
    [GID_RELATED_TS]              UNIQUEIDENTIFIER NULL,
    [DTT_FollowupDate]            DATETIME         NULL,
    [MLS_TaskStatus]              SMALLINT         NULL,
    [MLS_TaskRelatedStatus]       SMALLINT         NULL,
    [Gid_Related_CO]              UNIQUEIDENTIFIER NULL,
    [MMO_JOURNAL]                 NTEXT            NULL,
    [MLS_JOURNALTYPE]             SMALLINT         NULL,
    CONSTRAINT [PK_TD] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_TD_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_From_SO] FOREIGN KEY ([GID_From_SO]) REFERENCES [dbo].[SO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD] NOCHECK CONSTRAINT [LNK_TD_CreatedBy_US];


GO
ALTER TABLE [dbo].[TD] NOCHECK CONSTRAINT [LNK_TD_From_SO];


GO
CREATE NONCLUSTERED INDEX [IX_TD_TXT_ImportID]
    ON [dbo].[TD]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_Description]
    ON [dbo].[TD]([TXT_Description] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_ModDateTime]
    ON [dbo].[TD]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_StatusDueDateTime]
    ON [dbo].[TD]([MLS_Status] ASC, [DTT_DueTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_StatusPrioDueDateTmeRev]
    ON [dbo].[TD]([MLS_Status] ASC, [MLS_Priority] ASC, [DTT_DueTime] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_StatusPrioDueDateTme]
    ON [dbo].[TD]([MLS_Status] ASC, [MLS_Priority] ASC, [DTT_DueTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_CreationTime]
    ON [dbo].[TD]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_From_SO]
    ON [dbo].[TD]([GID_From_SO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_StatusDueDateTimeRev]
    ON [dbo].[TD]([MLS_Status] ASC, [DTT_DueTime] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_CreatedBy_US]
    ON [dbo].[TD]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_EntDte]
    ON [dbo].[TD]([DTT_EnteredDate] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_DueDateTime]
    ON [dbo].[TD]([DTT_DueTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_TD_BI__ID]
    ON [dbo].[TD]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_Name]
    ON [dbo].[TD]([SYS_NAME] ASC);


GO
CREATE TRIGGER trTDUpdateTN
ON [TD]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in TD table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(120)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'TD'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable
					and tn2.GID_InternalID = inserted.GID_ID)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [TD]
			SET [TD].TXT_ExternalSource = '', [TD].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [TD].GID_ID = in1.GID_ID
				and ISNULL([TD].TXT_ExternalSource, '') <> ''
				and ISNULL([TD].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trTDUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trTDUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trTDUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!