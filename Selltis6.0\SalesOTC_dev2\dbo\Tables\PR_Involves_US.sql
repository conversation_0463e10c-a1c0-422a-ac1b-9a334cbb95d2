﻿CREATE TABLE [dbo].[PR_Involves_US] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Project_Involves_User_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PR] UNIQUEIDENTIFIER NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PR_Involves_US] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PR_Involves_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_InvolvedIn_PR] FOREIGN KEY ([GID_PR]) REFERENCES [dbo].[PR] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PR_Involves_US] NOCHECK CONSTRAINT [LNK_PR_Involves_US];


GO
ALTER TABLE [dbo].[PR_Involves_US] NOCHECK CONSTRAINT [LNK_US_InvolvedIn_PR];


GO
CREATE CLUSTERED INDEX [IX_US_InvolvedIn_PR]
    ON [dbo].[PR_Involves_US]([GID_PR] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_Involves_US]
    ON [dbo].[PR_Involves_US]([GID_US] ASC);

