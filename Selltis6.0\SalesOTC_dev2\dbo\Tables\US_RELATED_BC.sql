﻿CREATE TABLE [dbo].[US_RELATED_BC] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_US_RELATED_BC_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    [GID_BC] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_US_RELATED_BC] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_BC_CONNECTED_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_RELATED_BC] FOREIGN KEY ([GID_BC]) REFERENCES [dbo].[BC] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[US_RELATED_BC] NOCHECK CONSTRAINT [LNK_BC_CONNECTED_US];


GO
ALTER TABLE [dbo].[US_RELATED_BC] NOCHECK CONSTRAINT [LNK_US_RELATED_BC];


GO
CREATE NONCLUSTERED INDEX [IX_US_RELATED_BC]
    ON [dbo].[US_RELATED_BC]([GID_US] ASC, [GID_BC] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_BC_CONNECTED_US]
    ON [dbo].[US_RELATED_BC]([GID_BC] ASC, [GID_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_RELATED_BC_GID_BC]
    ON [dbo].[US_RELATED_BC]([GID_BC] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_RELATED_BC_GID_US]
    ON [dbo].[US_RELATED_BC]([GID_US] ASC);

