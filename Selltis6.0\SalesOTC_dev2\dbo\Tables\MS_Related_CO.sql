﻿CREATE TABLE [dbo].[MS_Related_CO] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Message_Related_Company_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_MS] UNIQUEIDENTIFIER NOT NULL,
    [GID_CO] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MS_Related_CO] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CO_Connected_MS] FOREIGN KEY ([GID_MS]) REFERENCES [dbo].[MS] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_MS_Related_CO] FOREIGN KEY ([GID_CO]) REFERENCES [dbo].[CO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MS_Related_CO] NOCHECK CONSTRAINT [LNK_CO_Connected_MS];


GO
ALTER TABLE [dbo].[MS_Related_CO] NOCHECK CONSTRAINT [LNK_MS_Related_CO];


GO
CREATE CLUSTERED INDEX [IX_CO_Connected_MS]
    ON [dbo].[MS_Related_CO]([GID_MS] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MS_Related_CO]
    ON [dbo].[MS_Related_CO]([GID_CO] ASC);

