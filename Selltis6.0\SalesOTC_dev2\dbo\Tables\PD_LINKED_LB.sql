﻿CREATE TABLE [dbo].[PD_LINKED_LB] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_PD_LINKED_LB_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PD] UNIQUEIDENTIFIER NOT NULL,
    [GID_LB] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PD_LINKED_LB] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_LB_CONNECTED_PD] FOREIGN KEY ([GID_PD]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PD_LINKED_LB] FOREIGN KEY ([GID_LB]) REFERENCES [dbo].[LB] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PD_LINKED_LB] NOCHECK CONSTRAINT [LNK_LB_CONNECTED_PD];


GO
ALTER TABLE [dbo].[PD_LINKED_LB] NOCHECK CONSTRAINT [LNK_PD_LINKED_LB];


GO
CREATE NONCLUSTERED INDEX [IX_PD_LINKED_LB]
    ON [dbo].[PD_LINKED_LB]([GID_PD] ASC, [GID_LB] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_LB_CONNECTED_PD]
    ON [dbo].[PD_LINKED_LB]([GID_LB] ASC, [GID_PD] ASC);

