﻿CREATE TABLE [dbo].[MC_RELATED_OP] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_MC_RELATED_OP_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [<PERSON><PERSON>_<PERSON>] UNIQUEIDENTIFIER NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MC_RELATED_OP] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_MC_RELATED_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_RELATED_MC] FOREIGN KEY ([GID_MC]) REFERENCES [dbo].[MC] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MC_RELATED_OP] NOCHECK CONSTRAINT [LN<PERSON>_MC_RELATED_OP];


GO
ALTER TABLE [dbo].[MC_RELATED_OP] NOCHECK CONSTRAINT [LNK_OP_RELATED_MC];


GO
CREATE NONCLUSTERED INDEX [IX_MC_RELATED_OP]
    ON [dbo].[MC_RELATED_OP]([GID_MC] ASC, [GID_OP] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_RELATED_MC]
    ON [dbo].[MC_RELATED_OP]([GID_OP] ASC, [GID_MC] ASC);

