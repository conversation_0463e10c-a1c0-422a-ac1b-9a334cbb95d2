﻿Imports Microsoft.VisualBasic
Imports System.Net
Imports System.Collections.Generic
Imports System.IO
Imports System.Xml.Serialization
Imports System.Web.Script.Serialization
Imports System.Text

Public Class EfcOnlineApi
    Public Shared ApplicationId As String = "86D8057D-2032-4824-A70D-CBC045231507"
    ' This ApplicationId is provided by eFileCabinet
    Private Shared BaseUrl As String = "https://api.efilecabinetonline.com/"


    Public Shared Property AuthToken() As String
        Get
            Return m_AuthToken
        End Get
        Set(value As String)
            m_AuthToken = value
        End Set
    End Property
    Private Shared m_AuthToken As String

    Public Shared Function GenerateUserAuthToken(username As String, password As String) As String

        Dim url As String = BaseUrl & "EfcPartner/GenerateUserAuthToken"

        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.Method = "POST"

        w.ContentLength = 0

        w.Headers.Add("PartnerApplicationId", ApplicationId)

        w.Headers.Add("EfcUserName", username)

        w.Headers.Add("EfcUserPassword", password)

        AuthToken = GetResponseString(w)

        'GetResponseString(w)
        Return AuthToken

    End Function



#Region "EfcObject Services"

    Public Shared Function GetCabinets() As List(Of EfcObject)

        Dim efcos As List(Of EfcObject) = Nothing

        Dim url As String = BaseUrl & "EfcObject/GetCabinets"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectTypeFlag", CLng(EfcObject.EfcObjectTypeFlag.AllFoldersAndFiles).ToString())



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efcos = Deserializer.DeserializeJson(Of List(Of EfcObject))(rs.ReadToEnd())
        End Using

        r.Close()

        Return efcos

    End Function

    Public Shared Function GetChildren(EfcObjectId As Integer) As List(Of EfcObject)

        Dim efcos As List(Of EfcObject) = Nothing

        Dim url As String = BaseUrl & "EfcObject/GetChildren"


        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)


        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Timeout = 600000

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())

        w.Headers.Add("EfcObjectTypeFlag", CLng(EfcObject.EfcObjectTypeFlag.AllFoldersAndFiles).ToString())



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efcos = Deserializer.DeserializeJson(Of List(Of EfcObject))(rs.ReadToEnd())
        End Using


        r.Close()
        Return efcos

    End Function

    Public Shared Function GetDescendants(EfcObjectId As Integer) As List(Of EfcObject)

        Dim efcos As List(Of EfcObject) = Nothing

        Dim url As String = BaseUrl & "EfcObject/GetDescendants"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.ContentLength = 0

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efcos = Deserializer.DeserializeJson(Of List(Of EfcObject))(rs.ReadToEnd())
        End Using


        r.Close()
        Return efcos

    End Function

    Public Shared Function GetEfcObjectById(EfcObjectId As Integer) As EfcObject

        Dim efco As EfcObject = Nothing

        Dim url As String = BaseUrl & "EfcObject/Get"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())





        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efco = Deserializer.Deserialize(Of EfcObject)(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return efco

    End Function

    Public Shared Function GetComments(EfcObjectId As Integer) As List(Of EfcComment)

        Dim efcC As List(Of EfcComment) = Nothing

        Dim url As String = BaseUrl & "EfcObject/GetComments"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())





        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efcC = Deserializer.Deserialize(Of List(Of EfcComment))(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return efcC

    End Function

    Public Shared Function GetObjectsVisibleToUser(EfcUserId As Integer, typesToGet As EfcObject.EfcObjectTypeFlag) As List(Of EfcObject)

        Dim efcos As List(Of EfcObject) = Nothing

        Dim url As String = BaseUrl & "EfcObject/GetObjectsVisibleToUser"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentLength = 0

        w.ContentType = "application/json"

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcUserId", EfcUserId.ToString())

        w.Headers.Add("EfcObjectTypeFlag", CLng(typesToGet).ToString())



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efcos = Deserializer.DeserializeJson(Of List(Of EfcObject))(rs.ReadToEnd())
        End Using

        r.Close()

        Return efcos

    End Function

    Public Shared Function CreateEfcObject(o As EfcObject) As EfcObject

        Dim efco As EfcObject = Nothing

        Dim url As String = BaseUrl & "EfcObject/Create"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.Headers.Add("EfcAuthToken", AuthToken)



        SetPostData(w, Serializer.Serialize(Of EfcObject)(o))



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efco = Deserializer.Deserialize(Of EfcObject)(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return efco

    End Function

    Public Shared Function CreateComment(c As EfcComment) As EfcComment

        Dim efcC As EfcComment = Nothing

        Dim url As String = BaseUrl & "EfcObject/CreateComment"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", c.EfcObjectId.ToString())


        SetPostData(w, Serializer.Serialize(Of EfcComment)(c))


        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efcC = Deserializer.Deserialize(Of EfcComment)(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return efcC

    End Function

    Public Shared Function UpdateEfcObject(o As EfcObject) As EfcObject

        Dim efco As EfcObject = Nothing

        Dim url As String = BaseUrl & "EfcObject/Update"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.Headers.Add("EfcAuthToken", AuthToken)



        SetPostData(w, Serializer.Serialize(Of EfcObject)(o))



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efco = Deserializer.Deserialize(Of EfcObject)(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return efco

    End Function

    Public Shared Function CopyEfcObjectById(EfcObjectId As Integer, toParentId As Integer) As EfcObject

        Dim CopiedEfco As EfcObject = Nothing

        Dim url As String = BaseUrl & "EfcObject/Copy"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())

        w.Headers.Add("ParentId", toParentId.ToString())



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            CopiedEfco = Deserializer.Deserialize(Of EfcObject)(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return CopiedEfco

    End Function

    Public Shared Function DeleteEfcObject(o As EfcObject) As EfcObject

        Dim efco As EfcObject = Nothing

        Dim url As String = BaseUrl & "EfcObject/Delete"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.Headers.Add("EfcAuthToken", AuthToken)



        SetPostData(w, Serializer.Serialize(Of EfcObject)(o))



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efco = Deserializer.Deserialize(Of EfcObject)(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return efco

    End Function

    Public Shared Function PurgeEfcObject(o As EfcObject) As EfcObject

        Dim efco As EfcObject = Nothing

        Dim url As String = BaseUrl & "EfcObject/Purge"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.Headers.Add("EfcAuthToken", AuthToken)



        SetPostData(w, Serializer.Serialize(Of EfcObject)(o))



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efco = Deserializer.Deserialize(Of EfcObject)(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return efco

    End Function

    Public Shared Function SimpleSearch(SearchCriteria As String) As List(Of EfcObject)

        Dim searchResults As List(Of EfcObject) = Nothing

        Dim url As String = BaseUrl & "EfcSearch/SearchSimple"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectTypeFlag", CLng(EfcObject.EfcObjectTypeFlag.AllFoldersAndFiles).ToString())

        w.Headers.Add("SearchString", SearchCriteria)



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            searchResults = Deserializer.DeserializeJson(Of List(Of EfcObject))(rs.ReadToEnd())
        End Using

        r.Close()

        Return searchResults

    End Function

    Public Shared Function UploadEfcObjectData(EfcObjectId As Integer, filePath As String) As Boolean

        Dim fileLength As Integer = 0

        Dim fileChunks As List(Of Byte()) = Nothing

        Dim i As Integer = 0



        UploadInitialize(EfcObjectId)



        fileChunks = FileToArrayChunked(filePath, 4096)
        ' = 0x1000
        'fileChunks = FileToArrayChunked(filePath, 65536); // = 0x10000

        For Each nextChunk As Byte() In fileChunks

            i += 1

            'client.UploadEfcObjectDataChunk(AuthToken, f.EfcObjectId, nextChunk, i);

            UploadChunk(New FileContentsChunk() With { _
                 .EfcObjectId = EfcObjectId, _
                 .ContentsOrderId = i, _
                 .ContentsChunk = nextChunk _
            })


            fileLength += nextChunk.Length
        Next



        Return UploadFinalize(EfcObjectId, fileLength)

    End Function

    Private Shared Function UploadChunk(o As FileContentsChunk) As Boolean

        Dim url As String = BaseUrl & "EfcObject/UploadChunk"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.Headers.Add("EfcAuthToken", AuthToken)



        SetPostData(w, Serializer.Serialize(Of FileContentsChunk)(o))



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        r.Close()

        Return r.StatusCode = HttpStatusCode.OK

    End Function

    Private Shared Function UploadInitialize(EfcObjectId As Integer) As Boolean

        Dim url As String = BaseUrl & "EfcObject/UploadInitialize"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        r.Close()

        Return r.StatusCode = HttpStatusCode.OK

    End Function

    Private Shared Function UploadFinalize(EfcObjectId As Integer, FileBytesLength As Integer) As Boolean

        Dim url As String = BaseUrl & "EfcObject/UploadFinalize"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())

        w.Headers.Add("FileBytesLength", FileBytesLength.ToString())



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        r.Close()

        Return r.StatusCode = HttpStatusCode.OK

    End Function

    Public Shared Function UploadStream(EfcObjectId As Integer, sourceStream As Stream) As Boolean

        Dim url As String = BaseUrl & "EfcObject/UploadStream"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = sourceStream.Length

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())



        Dim requestStream As Stream = w.GetRequestStream()



        Dim bufferSize As Integer = 104856
        ' was 1024
        Dim bytesRead As Integer = 0

        Dim buffer As Byte() = New Byte(bufferSize - 1) {}



        While (InlineAssignHelper(bytesRead, sourceStream.Read(buffer, 0, bufferSize))) > 0
            requestStream.Write(buffer, 0, bytesRead)
        End While



        requestStream.Close()



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)
        r.Close()
        Return r.StatusCode = HttpStatusCode.OK

    End Function

    Public Shared Function UploadImages(EfcObjectId As Integer, sourceStream As Stream) As Boolean

        Dim url As String = BaseUrl & "EfcObject/UploadImages"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = sourceStream.Length

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())



        Dim requestStream As Stream = w.GetRequestStream()



        Dim bufferSize As Integer = 104856
        ' was 1024
        Dim bytesRead As Integer = 0

        Dim buffer As Byte() = New Byte(bufferSize - 1) {}



        While (InlineAssignHelper(bytesRead, sourceStream.Read(buffer, 0, bufferSize))) > 0
            requestStream.Write(buffer, 0, bytesRead)
        End While



        requestStream.Close()



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)
        r.Close()
        Return r.StatusCode = HttpStatusCode.OK

    End Function

    Public Shared Function AppendToPDF(EfcObjectId As Integer, sourceStream As Stream) As Boolean

        Dim url As String = BaseUrl & "EfcObject/AppendToPDF"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = sourceStream.Length

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())



        Dim requestStream As Stream = w.GetRequestStream()



        Dim bufferSize As Integer = 104856
        ' was 1024
        Dim bytesRead As Integer = 0

        Dim buffer As Byte() = New Byte(bufferSize - 1) {}



        While (InlineAssignHelper(bytesRead, sourceStream.Read(buffer, 0, bufferSize))) > 0
            requestStream.Write(buffer, 0, bytesRead)
        End While



        requestStream.Close()



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)
        r.Close()
        Return r.StatusCode = HttpStatusCode.OK

    End Function



    Public Shared Function DownloadEfcObject(EfcObjectId As Integer) As Byte()

        Dim efco As EfcObjectFileContents = Nothing



        Dim url As String = BaseUrl & "EfcObject/GetFileContents"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)



        Using rs As New StreamReader(r.GetResponseStream())


            efco = Deserializer.Deserialize(Of EfcObjectFileContents)(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return efco.Contents

    End Function

    Public Shared Function DownloadFileStream(EfcObjectId As Integer, ByRef length As Long) As MemoryStream

        Dim url As String = BaseUrl & "EfcObject/GetFileContentsStream"


        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        length = r.ContentLength

        Dim ms As New MemoryStream()

        r.GetResponseStream().CopyTo(ms)

        r.Close()

        Return ms

    End Function



    Public Shared Function GetViewPage(EfcObjectId As Integer, pageNumber As Integer, ByRef totalPages As Integer) As Byte()

        Dim result As Byte() = Nothing

        totalPages = -1



        Dim url As String = BaseUrl & "EfcObject/GetViewPage"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "image/png"

        w.ContentType = "image/png"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())

        w.Headers.Add("PageNumber", pageNumber.ToString())



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)



        If r.StatusCode = HttpStatusCode.OK Then

            totalPages = Integer.Parse(r.Headers("TotalPages"))



            Using rs As New StreamReader(r.GetResponseStream())

                Dim responseLength As Long = r.ContentLength

                If responseLength > 0 Then

                    result = New Byte(responseLength - 1) {}



                    Dim bytesRead As Integer = 0

                    Dim totalBytes As Integer = 0

                    While (InlineAssignHelper(bytesRead, rs.BaseStream.Read(result, CInt(totalBytes), CInt(responseLength - totalBytes)))) > 0


                        totalBytes += bytesRead

                    End While

                End If

            End Using
        End If

        r.Close()

        Return result

    End Function

    Public Shared Function GetViewPageScaled(EfcObjectId As Integer, pageNumber As Integer, maxWidth As Integer, maxHeight As Integer, ByRef totalPages As Integer) As Byte()

        Dim result As Byte() = Nothing

        totalPages = -1



        Dim url As String = BaseUrl & "EfcObject/GetViewPageScaled"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "image/jpeg"

        w.ContentType = "image/jpeg"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())

        w.Headers.Add("PageNumber", pageNumber.ToString())

        w.Headers.Add("MaxWidth", maxWidth.ToString())

        w.Headers.Add("MaxHeight", maxHeight.ToString())

        w.Timeout = 3600000



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)



        If r.StatusCode = HttpStatusCode.OK Then

            totalPages = Integer.Parse(r.Headers("TotalPages"))



            Using rs As New StreamReader(r.GetResponseStream())

                Dim responseLength As Long = r.ContentLength

                If responseLength > 0 Then

                    result = New Byte(responseLength - 1) {}



                    Dim bytesRead As Integer = 0

                    Dim totalBytes As Integer = 0

                    While (InlineAssignHelper(bytesRead, rs.BaseStream.Read(result, CInt(totalBytes), CInt(responseLength - totalBytes)))) > 0


                        totalBytes += bytesRead

                    End While

                End If

            End Using
        End If

        r.Close()

        Return result

    End Function





    Public Shared Function GetThumbnail(EfcObjectId As Integer) As Byte()

        Dim result As Byte() = Nothing



        Dim url As String = BaseUrl & "EfcObject/GetThumbnail"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "image/png"

        w.ContentType = "image/png"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)



        If r.StatusCode = HttpStatusCode.OK Then

            Using rs As New StreamReader(r.GetResponseStream())

                Dim responseLength As Long = r.ContentLength

                result = New Byte(responseLength - 1) {}



                Dim bytesRead As Integer = 0

                Dim totalBytes As Integer = 0

                While (InlineAssignHelper(bytesRead, rs.BaseStream.Read(result, CInt(totalBytes), CInt(responseLength - totalBytes)))) > 0


                    totalBytes += bytesRead

                End While

            End Using
        End If

        r.Close()

        Return result

    End Function



    Public Shared Function GetAncestry(EfcObjectId As Integer) As List(Of EfcObject)

        Dim efcos As List(Of EfcObject) = Nothing

        Dim url As String = BaseUrl & "EfcObject/GetAncestry"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())





        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efcos = Deserializer.DeserializeJson(Of List(Of EfcObject))(rs.ReadToEnd())
        End Using

        r.Close()

        Return efcos

    End Function



    Public Shared Function GenerateUploadToken() As String

        Dim url As String = BaseUrl & "EfcObject/GetUploadToken"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.Method = "POST"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)



        Return GetResponseString(w)

    End Function


#Region "Profile Calls"

    Public Shared Function GetEfcObjectProfile(EfcObjectId As Integer) As EfcObjectProfile

        Dim efcop As EfcObjectProfile = Nothing

        Dim url As String = BaseUrl & "EfcProfile/Get"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.ContentLength = 0

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcObjectId", EfcObjectId.ToString())





        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efcop = Deserializer.Deserialize(Of EfcObjectProfile)(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return efcop

    End Function

    Public Shared Function UpdateEfcProfile(o As EfcObjectProfile) As EfcObjectProfile

        Dim efcop As EfcObjectProfile = Nothing

        Dim url As String = BaseUrl & "EfcProfile/Update"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.Headers.Add("EfcAuthToken", AuthToken)



        SetPostData(w, Serializer.Serialize(Of EfcObjectProfile)(o))



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efcop = Deserializer.Deserialize(Of EfcObjectProfile)(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return efcop

    End Function

    Public Shared Function CreateEfcProfile(name As String, ProfileItems As List(Of EfcProfileItem)) As EfcProfile

        Dim efcp As EfcProfile = Nothing

        Dim url As String = BaseUrl & "EfcProfile/CreateProfile"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcProfileName", name)


        SetPostData(w, Serializer.Serialize(Of List(Of EfcProfileItem))(ProfileItems))



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efcp = Deserializer.Deserialize(Of EfcProfile)(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return efcp

    End Function

    Public Shared Function CreateProfileItem(ProfileItem As EfcProfileItem) As EfcProfileItem

        Dim efcpi As EfcProfileItem = Nothing

        Dim url As String = BaseUrl & "EfcProfile/CreateProfileItem"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.Headers.Add("EfcAuthToken", AuthToken)


        SetPostData(w, Serializer.Serialize(Of EfcProfileItem)(ProfileItem))



        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())


            efcpi = Deserializer.Deserialize(Of EfcProfileItem)(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return efcpi
    End Function

    Public Shared Function GetProfileItems(ProfileId As Integer) As List(Of EfcProfileItem)

        Dim efcpi As List(Of EfcProfileItem) = Nothing

        Dim url As String = BaseUrl & "EfcProfile/GetProfileItems"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.Headers.Add("EfcAuthToken", AuthToken)

        w.Headers.Add("EfcProfileId", ProfileId.ToString())


        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())
            efcpi = Deserializer.Deserialize(Of List(Of EfcProfileItem))(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return efcpi
    End Function

    Public Shared Function GetProfiles() As List(Of EfcProfile)

        Dim efcp As List(Of EfcProfile) = Nothing

        Dim url As String = BaseUrl & "EfcProfile/GetProfiles"



        Dim w As HttpWebRequest = TryCast(WebRequest.Create(url), HttpWebRequest)

        w.Method = "POST"

        w.Accept = "application/json"

        w.ContentType = "application/json"

        w.Headers.Add("EfcAuthToken", AuthToken)


        Dim r As HttpWebResponse = DirectCast(w.GetResponse(), HttpWebResponse)

        Using rs As New StreamReader(r.GetResponseStream())
            efcp = Deserializer.Deserialize(Of List(Of EfcProfile))(rs.ReadToEnd(), r.ContentType)
        End Using

        r.Close()

        Return efcp
    End Function

#End Region

#End Region



#Region "Request/Response Helpers"



    Private Shared Function FileToArrayChunked(filePath As String, chunkSize As Integer) As List(Of Byte())


        Dim fs As Stream = New FileStream(filePath, FileMode.Open, FileAccess.Read)

        Dim bytes As New List(Of Byte())()

        Dim cursor As Integer = 0

        Dim nextChunkSize As Integer = chunkSize

        Dim buffer As Byte() = New Byte(chunkSize - 1) {}

        While cursor < fs.Length

            If cursor + chunkSize > fs.Length Then
                nextChunkSize = CInt(fs.Length - cursor)

                buffer = New Byte(nextChunkSize - 1) {}
            Else



                buffer = New Byte(nextChunkSize - 1) {}
            End If
            fs.Read(buffer, 0, nextChunkSize)

            bytes.Add(buffer)

            cursor += nextChunkSize
        End While

        fs.Close()

        Return bytes

    End Function


    Private Shared Sub SetPostData(ByRef w As HttpWebRequest, PostData As String)

        Dim postBuffer As Byte() = Encoding.UTF8.GetBytes(PostData)

        w.ContentLength = postBuffer.Length

        Dim sPostData As Stream = w.GetRequestStream()

        sPostData.Write(postBuffer, 0, postBuffer.Length)

        sPostData.Close()

    End Sub



    Private Shared Function GetResponseString(w As HttpWebRequest) As String

        Return GetResponseRaw(w).Replace("""", "")

    End Function


    Private Shared Function GetResponseRaw(w As HttpWebRequest) As String

        Dim responseText As String = ""

        'Try

        Dim r As HttpWebResponse = TryCast(w.GetResponse(), HttpWebResponse)

            Dim rs As New StreamReader(r.GetResponseStream())

            responseText = rs.ReadToEnd()

            r.Close()


            rs.Close()

        'Catch ex As Exception


        '    responseText = ex.ToString()
        'End Try

        Return responseText

    End Function
    Private Shared Function InlineAssignHelper(Of T)(ByRef target As T, value As T) As T
        target = value
        Return value
    End Function

#End Region

End Class



Public NotInheritable Class Deserializer
    Private Sub New()
    End Sub
    Public Shared Function Deserialize(Of T As {Class, New})(s As String, Optional MediaTypeFormatter As String = "none") As T
        If MediaTypeFormatter.Contains("/xml") Then
            Return DeserializeXml(Of T)(s)
        ElseIf MediaTypeFormatter.Contains("/json") Then
            Return DeserializeJson(Of T)(s)
        Else
            Dim ret As T = Nothing
            If ret Is Nothing Then
                ret = DeserializeXml(Of T)(s)
            End If
            If ret Is Nothing Then
                ret = DeserializeJson(Of T)(s)
            End If
            Return ret
        End If
    End Function

    Public Shared Function DeserializeXml(Of T As {Class, New})(s As String) As T
        Dim serializer = New XmlSerializer(GetType(T))
        Using reader = New StringReader(s)
            Try
                Return DirectCast(serializer.Deserialize(reader), T)
            Catch
                ' Could not be deserialized to this type. 
                Return Nothing
            End Try
        End Using
    End Function

    Public Shared Function DeserializeJson(Of T As {Class, New})(s As String) As T
        Try
            Dim ser As New JavaScriptSerializer()
            ser.MaxJsonLength = Integer.MaxValue
            Return ser.Deserialize(Of T)(s)
        Catch
            Return Nothing
        End Try
    End Function

End Class

Public NotInheritable Class Serializer
    Private Sub New()
    End Sub
    Public Shared Function Serialize(Of T As {Class, New})(e As T) As String
        Dim ser As New JavaScriptSerializer()
        ser.MaxJsonLength = Integer.MaxValue
        Return ser.Serialize(e)
    End Function
End Class


Class EfcLogin

    Public Property username() As String
        Get
            Return m_username
        End Get
        Set(value As String)
            m_username = value
        End Set
    End Property
    Private m_username As String

    Public Property password() As String
        Get
            Return m_password
        End Get
        Set(value As String)
            m_password = value
        End Set
    End Property
    Private m_password As String

    Public Property AuthToken() As String
        Get
            Return m_AuthToken
        End Get
        Set(value As String)
            m_AuthToken = value
        End Set
    End Property
    Private m_AuthToken As String

End Class



Public Class EfcObject

    Public Property EfcObjectId() As Integer
        Get
            Return m_EfcObjectId
        End Get
        Set(value As Integer)
            m_EfcObjectId = value
        End Set
    End Property
    Private m_EfcObjectId As Integer

    Public Property ParentEfcObjectId() As Integer
        Get
            Return m_ParentEfcObjectId
        End Get
        Set(value As Integer)
            m_ParentEfcObjectId = value
        End Set
    End Property
    Private m_ParentEfcObjectId As Integer

    Public Property Name() As String
        Get
            Return m_Name
        End Get
        Set(value As String)
            m_Name = value
        End Set
    End Property
    Private m_Name As String

    Public Property Extension() As String
        Get
            Return m_Extension
        End Get
        Set(value As String)
            m_Extension = value
        End Set
    End Property
    Private m_Extension As String

    Public Property TypeString() As String
        Get
            Return m_TypeString
        End Get
        Set(value As String)
            m_TypeString = value
        End Set
    End Property
    Private m_TypeString As String

    Public Property TypeEnum() As Integer
        Get
            Return m_TypeEnum
        End Get
        Set(value As Integer)
            m_TypeEnum = value
        End Set
    End Property
    Private m_TypeEnum As Integer

    Public Property SizeInBytes() As Long
        Get
            Return m_SizeInBytes
        End Get
        Set(value As Long)
            m_SizeInBytes = value
        End Set
    End Property
    Private m_SizeInBytes As Long

    Public Property CreatedByUser() As EfcUser
        Get
            Return m_CreatedByUser
        End Get
        Set(value As EfcUser)
            m_CreatedByUser = value
        End Set
    End Property
    Private m_CreatedByUser As EfcUser

    Public Property DateCreated() As DateTime
        Get
            Return m_DateCreated
        End Get
        Set(value As DateTime)
            m_DateCreated = value
        End Set
    End Property
    Private m_DateCreated As DateTime

    Public Property DateModified() As DateTime
        Get
            Return m_DateModified
        End Get
        Set(value As DateTime)
            m_DateModified = value
        End Set
    End Property
    Private m_DateModified As DateTime

    Public Property ApplicationData() As String
        Get
            Return m_ApplicationData
        End Get
        Set(value As String)
            m_ApplicationData = value
        End Set
    End Property
    Private m_ApplicationData As String




    <Flags> _
    Public Enum EfcObjectTypeFlag

        '****************************************
        '
        '             * Flags
        '
        '             ****************************************




        ' The flag for None is                 00000000

        None = &H0



        ' The flag for Cabinet is              00000001

        Cabinet = &H1



        ' The flag for Drawer is               00000010

        Drawer = &H2



        ' The flag for Folder is               00000100

        Folder = &H4



        ' The flag for File is                 00001000

        File = &H8

        ' The flag for Search is

        Search = &H9


        AllFoldersAndFiles = Cabinet Or Drawer Or Folder Or File

    End Enum

End Class



Public Class EfcObjectFileContents

    Public Property EfcObjectId() As Integer
        Get
            Return m_EfcObjectId
        End Get
        Set(value As Integer)
            m_EfcObjectId = value
        End Set
    End Property
    Private m_EfcObjectId As Integer

    Public Property LastWriteTimeUtc() As DateTime
        Get
            Return m_LastWriteTimeUtc
        End Get
        Set(value As DateTime)
            m_LastWriteTimeUtc = value
        End Set
    End Property
    Private m_LastWriteTimeUtc As DateTime

    Public Property LastWriteTimeOA() As Double
        Get
            Return m_LastWriteTimeOA
        End Get
        Set(value As Double)
            m_LastWriteTimeOA = value
        End Set
    End Property
    Private m_LastWriteTimeOA As Double

    Public Property Contents() As Byte()
        Get
            Return m_Contents
        End Get
        Set(value As Byte())
            m_Contents = value
        End Set
    End Property
    Private m_Contents As Byte()

End Class



Public Class FileContentsChunk

    Public Property EfcObjectId() As Integer
        Get
            Return m_EfcObjectId
        End Get
        Set(value As Integer)
            m_EfcObjectId = value
        End Set
    End Property
    Private m_EfcObjectId As Integer

    Public Property ContentsChunk() As Byte()
        Get
            Return m_ContentsChunk
        End Get
        Set(value As Byte())
            m_ContentsChunk = value
        End Set
    End Property
    Private m_ContentsChunk As Byte()

    Public Property ContentsOrderId() As Integer
        Get
            Return m_ContentsOrderId
        End Get
        Set(value As Integer)
            m_ContentsOrderId = value
        End Set
    End Property
    Private m_ContentsOrderId As Integer

End Class



Public Class EfcUser

    Public Property EfcUserId() As Integer
        Get
            Return m_EfcUserId
        End Get
        Set(value As Integer)
            m_EfcUserId = value
        End Set
    End Property
    Private m_EfcUserId As Integer

    Public Property UserName() As String
        Get
            Return m_UserName
        End Get
        Set(value As String)
            m_UserName = value
        End Set
    End Property
    Private m_UserName As String

    Public Property Password() As String
        Get
            Return m_Password
        End Get
        Set(value As String)
            m_Password = value
        End Set
    End Property
    Private m_Password As String

    Public Property NameFirst() As String
        Get
            Return m_NameFirst
        End Get
        Set(value As String)
            m_NameFirst = value
        End Set
    End Property
    Private m_NameFirst As String

    Public Property NameLast() As String
        Get
            Return m_NameLast
        End Get
        Set(value As String)
            m_NameLast = value
        End Set
    End Property
    Private m_NameLast As String

    Public Property Role() As EfcUserRoleType
        Get
            Return m_Role
        End Get
        Set(value As EfcUserRoleType)
            m_Role = value
        End Set
    End Property
    Private m_Role As EfcUserRoleType



    Public Enum EfcUserRoleType

        Standard = 0

        Portal = 1

        Admin = 2

        MasterAccount = 4

    End Enum

End Class



Public Class EfcAccount

    Public Property EmailAddress() As String
        Get
            Return m_EmailAddress
        End Get
        Set(value As String)
            m_EmailAddress = value
        End Set
    End Property
    Private m_EmailAddress As String

    Public Property AuthToken() As String
        Get
            Return m_AuthToken
        End Get
        Set(value As String)
            m_AuthToken = value
        End Set
    End Property
    Private m_AuthToken As String

    Public Property EfcUserId() As Integer
        Get
            Return m_EfcUserId
        End Get
        Set(value As Integer)
            m_EfcUserId = value
        End Set
    End Property
    Private m_EfcUserId As Integer

End Class



Public Class EfcUserPermission

    Public Property EfcUserId() As Integer
        Get
            Return m_EfcUserId
        End Get
        Set(value As Integer)
            m_EfcUserId = value
        End Set
    End Property
    Private m_EfcUserId As Integer

    Public Property EfcObjectId() As Integer
        Get
            Return m_EfcObjectId
        End Get
        Set(value As Integer)
            m_EfcObjectId = value
        End Set
    End Property
    Private m_EfcObjectId As Integer

    Public Property PushDown() As Boolean
        Get
            Return m_PushDown
        End Get
        Set(value As Boolean)
            m_PushDown = value
        End Set
    End Property
    Private m_PushDown As Boolean

    Public Property PermissionType() As EfcPermissionType
        Get
            Return m_PermissionType
        End Get
        Set(value As EfcPermissionType)
            m_PermissionType = value
        End Set
    End Property
    Private m_PermissionType As EfcPermissionType

End Class



Public Class EfcGroup

    Public Property EfcGroupId() As Integer
        Get
            Return m_EfcGroupId
        End Get
        Set(value As Integer)
            m_EfcGroupId = value
        End Set
    End Property
    Private m_EfcGroupId As Integer

    Public Property Name() As String
        Get
            Return m_Name
        End Get
        Set(value As String)
            m_Name = value
        End Set
    End Property
    Private m_Name As String

    Public Property Description() As String
        Get
            Return m_Description
        End Get
        Set(value As String)
            m_Description = value
        End Set
    End Property
    Private m_Description As String

End Class



Public Class EfcGroupUserChange

    Public Property EfcGroupId() As Integer
        Get
            Return m_EfcGroupId
        End Get
        Set(value As Integer)
            m_EfcGroupId = value
        End Set
    End Property
    Private m_EfcGroupId As Integer

    Public Property EfcUserId() As Integer
        Get
            Return m_EfcUserId
        End Get
        Set(value As Integer)
            m_EfcUserId = value
        End Set
    End Property
    Private m_EfcUserId As Integer

    Public Property Action() As EfcAction
        Get
            Return m_Action
        End Get
        Set(value As EfcAction)
            m_Action = value
        End Set
    End Property
    Private m_Action As EfcAction

End Class



Public Class EfcGroupPermission

    Public Property EfcGroupId() As Integer
        Get
            Return m_EfcGroupId
        End Get
        Set(value As Integer)
            m_EfcGroupId = value
        End Set
    End Property
    Private m_EfcGroupId As Integer

    Public Property EfcObjectId() As Integer
        Get
            Return m_EfcObjectId
        End Get
        Set(value As Integer)
            m_EfcObjectId = value
        End Set
    End Property
    Private m_EfcObjectId As Integer

    Public Property PushDown() As Boolean
        Get
            Return m_PushDown
        End Get
        Set(value As Boolean)
            m_PushDown = value
        End Set
    End Property
    Private m_PushDown As Boolean

    Public Property PermissionType() As EfcPermissionType
        Get
            Return m_PermissionType
        End Get
        Set(value As EfcPermissionType)
            m_PermissionType = value
        End Set
    End Property
    Private m_PermissionType As EfcPermissionType

End Class



Public Enum EfcPermissionType
    View = 0

    Edit = 1

    Delete = 2

    Admin = 3
End Enum



Public Enum EfcAction

    None = 0

    Add = 1

    Remove = 2

End Enum



Public Class EfcObjectProfile
    Public Property EfcObjectId() As Integer
        Get
            Return m_EfcObjectId
        End Get
        Set(value As Integer)
            m_EfcObjectId = value
        End Set
    End Property
    Private m_EfcObjectId As Integer

    Public Property EfcProfile() As EfcProfile
        Get
            Return m_EfcProfile
        End Get
        Set(value As EfcProfile)
            m_EfcProfile = value
        End Set
    End Property
    Private m_EfcProfile As EfcProfile

    Public Property Items() As List(Of EfcProfileItem)
        Get
            Return m_Items
        End Get
        Set(value As List(Of EfcProfileItem))
            m_Items = value
        End Set
    End Property
    Private m_Items As List(Of EfcProfileItem)
End Class



Public Class EfcProfile
    Public Property EfcProfileId() As Integer
        Get
            Return m_EfcProfileId
        End Get
        Set(value As Integer)
            m_EfcProfileId = value
        End Set
    End Property
    Private m_EfcProfileId As Integer

    Public Property Name() As String
        Get
            Return m_Name
        End Get
        Set(value As String)
            m_Name = value
        End Set
    End Property
    Private m_Name As String
End Class



Public Class EfcProfileItem
    Public Property EfcProfileItemId() As Integer
        Get
            Return m_EfcProfileItemId
        End Get
        Set(value As Integer)
            m_EfcProfileItemId = value
        End Set
    End Property
    Private m_EfcProfileItemId As Integer

    Public Property Name() As String
        Get
            Return m_Name
        End Get
        Set(value As String)
            m_Name = value
        End Set
    End Property
    Private m_Name As String

    Public Property Value() As String
        Get
            Return m_Value
        End Get
        Set(value As String)
            m_Value = value
        End Set
    End Property
    Private m_Value As String

    Public Property Type() As String
        Get
            Return m_Type
        End Get
        Set(value As String)
            m_Type = value
        End Set
    End Property
    Private m_Type As String

    Public Property TypeEnum() As EfcProfileItemType
        Get
            Return m_TypeEnum
        End Get
        Set(value As EfcProfileItemType)
            m_TypeEnum = value
        End Set
    End Property
    Private m_TypeEnum As EfcProfileItemType

    Public Property ValueOptions() As EfcProfileItemOption()
        Get
            Return m_ValueOptions
        End Get
        Set(value As EfcProfileItemOption())
            m_ValueOptions = value
        End Set
    End Property
    Private m_ValueOptions As EfcProfileItemOption()
End Class



Public Enum EfcProfileItemType
    ' Item types

    Text = 1

    Number = 2

    [Date] = 3

    Currency = 4

    PhoneNumber = 5

    List = 6

    EfcObjectChildren = 7
End Enum

Public Class EfcProfileItemOption
    Public Property Value() As String
        Get
            Return m_Value
        End Get
        Set(value As String)
            m_Value = value
        End Set
    End Property
    Private m_Value As String
End Class

Public Class EfcComment
    Public Property CommentId() As Integer
        Get
            Return m_CommentId
        End Get
        Set(value As Integer)
            m_CommentId = value
        End Set
    End Property
    Private m_CommentId As Integer
    Public Property Text() As String
        Get
            Return m_Text
        End Get
        Set(value As String)
            m_Text = value
        End Set
    End Property
    Private m_Text As String
    Public Property CreatedByUser() As EfcUser
        Get
            Return m_CreatedByUser
        End Get
        Set(value As EfcUser)
            m_CreatedByUser = value
        End Set
    End Property
    Private m_CreatedByUser As EfcUser
    Public Property DateCreated() As DateTime
        Get
            Return m_DateCreated
        End Get
        Set(value As DateTime)
            m_DateCreated = value
        End Set
    End Property
    Private m_DateCreated As DateTime
    Public Property DateModified() As System.Nullable(Of DateTime)
        Get
            Return m_DateModified
        End Get
        Set(value As System.Nullable(Of DateTime))
            m_DateModified = value
        End Set
    End Property
    Private m_DateModified As System.Nullable(Of DateTime)
    Public Property EfcObjectId() As Integer
        Get
            Return m_EfcObjectId
        End Get
        Set(value As Integer)
            m_EfcObjectId = value
        End Set
    End Property
    Private m_EfcObjectId As Integer
End Class

Public Class AccountInfo
    Public Property AccountStatus() As AccountStatusType
        Get
            Return m_AccountStatus
        End Get
        Set(value As AccountStatusType)
            m_AccountStatus = value
        End Set
    End Property
    Private m_AccountStatus As AccountStatusType

    Public Property NeedsSecurityQuestion() As Boolean
        Get
            Return m_NeedsSecurityQuestion
        End Get
        Set(value As Boolean)
            m_NeedsSecurityQuestion = value
        End Set
    End Property
    Private m_NeedsSecurityQuestion As Boolean

    Public Property Expiration() As System.Nullable(Of DateTime)
        Get
            Return m_Expiration
        End Get
        Set(value As System.Nullable(Of DateTime))
            m_Expiration = value
        End Set
    End Property
    Private m_Expiration As System.Nullable(Of DateTime)
End Class

Public Enum AccountStatusType
    Alpha = 101
    Beta = 102
    Unchanged = 0
    Trial = 1
    Active = 2
    ActivePastDue = 3
    Inactive = 4
    Purged = 5
    TrialExpired = 6
End Enum
