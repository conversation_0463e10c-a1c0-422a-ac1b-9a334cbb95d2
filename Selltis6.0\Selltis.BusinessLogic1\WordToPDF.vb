﻿Imports System.IO
Imports System.Text
Imports System.Text.RegularExpressions
Imports DocumentFormat.OpenXml.Packaging
Imports DocumentFormat.OpenXml.Wordprocessing

Imports Telerik.Windows.Documents.Common.FormatProviders
Imports Telerik.Windows.Documents.Flow.FormatProviders.Docx
'Imports Telerik.Windows.Documents.Flow.FormatProviders.Html
Imports Telerik.Windows.Documents.Flow.FormatProviders.Pdf
'Imports Telerik.Windows.Documents.Flow.FormatProviders.Rtf
'Imports Telerik.Windows.Documents.Flow.FormatProviders.Txt
'Imports Telerik.Windows.Documents.Flow.Model



Public Class WordDocumentHelper
    Private IsRecurringTable As Boolean = False

    Dim bShowHTML As Integer
    Dim bHideZeroCurValue As Integer
    Public iCHKPrintType As Integer = 1 ''1 -- Checked/Unchecked, 2-- Yes/No , 3-- True/False
    Public Shared DEF_FONT_SIZE As Integer
    Public Shared DEF_FONT_NAME As String

    Public Function ProcessDocument(ByVal filename As String, ByVal DoRs As clRowSet, Optional ByVal iFlag As Integer = 0, Optional ByVal iShowHTML As Integer = 0, Optional ByVal ibHideZeroCurValue As Integer = 0, Optional ByVal iDocType As Integer = 1, Optional ByVal sFileDisplayName As String = "", Optional ByVal iDefFontSize As Integer = 20, Optional ByVal sDefFontName As String = "Times New Roman") As MemoryStream
        Dim wordprocessingDocument As WordprocessingDocument = WordprocessingDocument.Open(filename, True)
        Dim body As Body = wordprocessingDocument.MainDocumentPart.Document.Body

        ''iDocType -- 1-PDF (Default), 2--MS Word (.docx)

        bShowHTML = iShowHTML
        bHideZeroCurValue = ibHideZeroCurValue
        DEF_FONT_SIZE = iDefFontSize
        DEF_FONT_NAME = sDefFontName

        Dim _maindocpart As MainDocumentPart = wordprocessingDocument.MainDocumentPart

        ''Header
        For Each item In wordprocessingDocument.MainDocumentPart.HeaderParts

            For Each Hitem In item.Header.ChildElements

                If Hitem.[GetType]().FullName = "DocumentFormat.OpenXml.Wordprocessing.Paragraph" Then
                    Dim _innertext As String = Hitem.InnerText
                    If Not String.IsNullOrEmpty(_innertext) Then
                        Dim matches As MatchCollection = Regex.Matches(_innertext, "\(\%(.*?)\%\)")
                        If matches.Count > 0 Then
                            ProcessParagraph(Hitem, 0, DoRs, _maindocpart)
                        End If
                    End If

                ElseIf Hitem.[GetType]().FullName = "DocumentFormat.OpenXml.Wordprocessing.Table" Then
                    Dim table As DocumentFormat.OpenXml.Wordprocessing.Table = CType(Hitem, DocumentFormat.OpenXml.Wordprocessing.Table)
                    ProcessTable(table, DoRs, _maindocpart)
                End If
            Next

        Next

        ''Body
        For Each item In body.ChildElements

            If item.[GetType]().FullName = "DocumentFormat.OpenXml.Wordprocessing.Paragraph" Then
                ProcessParagraph(item, 0, DoRs, _maindocpart)
            ElseIf item.[GetType]().FullName = "DocumentFormat.OpenXml.Wordprocessing.Table" Then
                Dim table As DocumentFormat.OpenXml.Wordprocessing.Table = CType(item, DocumentFormat.OpenXml.Wordprocessing.Table)
                ProcessTable(table, DoRs, _maindocpart)
            End If
        Next

        ''Footer
        For Each item In wordprocessingDocument.MainDocumentPart.FooterParts

            For Each Fitem In item.Footer.ChildElements

                If Fitem.[GetType]().FullName = "DocumentFormat.OpenXml.Wordprocessing.Paragraph" Then
                    Dim _innertext As String = Fitem.InnerText
                    If Not String.IsNullOrEmpty(_innertext) Then
                        Dim matches As MatchCollection = Regex.Matches(_innertext, "\(\%(.*?)\%\)")
                        If matches.Count > 0 Then
                            ProcessParagraph(Fitem, 0, DoRs, _maindocpart)
                        End If
                    End If

                ElseIf Fitem.[GetType]().FullName = "DocumentFormat.OpenXml.Wordprocessing.Table" Then
                    Dim table As DocumentFormat.OpenXml.Wordprocessing.Table = CType(Fitem, DocumentFormat.OpenXml.Wordprocessing.Table)
                    ProcessTable(table, DoRs, _maindocpart)
                End If
            Next

        Next

        If String.IsNullOrEmpty(sFileDisplayName) Then
            sFileDisplayName = filename
        End If

        wordprocessingDocument.PackageProperties.Title = sFileDisplayName

        wordprocessingDocument.Close()

        If iDocType = 2 Then ''word format
            Return ConvertWordToStream(filename)
        Else
            Return ConvertWordToPDFStream(filename)
        End If

        '' Return ConvertWordToPDFStream(filename)

    End Function

    Private Sub ProcessTable(ByVal table As Table, ByVal DoRs As clRowSet, ByVal _maindocpart As MainDocumentPart)
        Dim _rowindex As Integer = 0

        For Each row In table.ChildElements

            If row.[GetType]().FullName = "DocumentFormat.OpenXml.Wordprocessing.TableRow" Then
                ProcessRow(row, _rowindex, DoRs, _maindocpart)
                _rowindex += 1
            End If
        Next

        IsRecurringTable = False
    End Sub

    Private Sub ProcessRow(ByVal row As DocumentFormat.OpenXml.OpenXmlElement, ByVal iRowId As Integer, ByVal DoRs As clRowSet, ByVal _maindocpart As MainDocumentPart)
        For Each _cell In row.ChildElements

            If _cell.LocalName = "tc" AndAlso Not String.IsNullOrEmpty(_cell.InnerText) Then
                ProcessCell(_cell, iRowId, DoRs, _maindocpart)
            End If
        Next
    End Sub
    Private Sub ProcessCell(ByVal _cell As DocumentFormat.OpenXml.OpenXmlElement, ByVal iRowId As Integer, ByVal DoRs As clRowSet, ByVal _maindocpart As MainDocumentPart)
        Dim cell As TableCell = CType(_cell, TableCell)

        For Each _cellitem In cell.ChildElements

            If _cellitem.[GetType]().FullName = "DocumentFormat.OpenXml.Wordprocessing.Paragraph" Then
                Dim _innertext As String = _cellitem.InnerText
                If Not String.IsNullOrEmpty(_innertext) Then
                    Dim matches As MatchCollection = Regex.Matches(_innertext, "\(\%(.*?)\%\)")
                    If matches.Count > 0 Then
                        ProcessParagraph(_cellitem, iRowId, DoRs, _maindocpart)
                    End If
                End If
            End If
        Next
    End Sub

    Private Sub ProcessParagraph(ByVal item As DocumentFormat.OpenXml.OpenXmlElement, ByVal iRowId As Integer, ByVal DoRs As clRowSet, ByVal _maindocpart As MainDocumentPart)
        Dim _innertext As String = item.InnerText
        Dim sFieldName As String = ""

        If Not String.IsNullOrEmpty(_innertext) Then
            Dim p As DocumentFormat.OpenXml.Wordprocessing.Paragraph = CType(item, Paragraph)
            Dim matches As MatchCollection = Regex.Matches(_innertext, "\(\%(.*?)\%\)")

            If matches.Count > 0 Then

                For Each _field In matches
                    Dim _fieldname As String = _field.ToString()
                    Dim _fieldvalue As String = ""

                    If _fieldname.Replace("(%", "").Replace("%)", "").StartsWith("LNK_") Then

                        If _fieldname.ToUpper().Contains("LNK_CONNECTED_QL") OrElse _fieldname.Contains("LNK_RENTAL_QL") OrElse _fieldname.Contains("LNK_SALES_QL") OrElse _fieldname.Contains("LNK_CONNECTED_II") Then

                            If item.Parent.LocalName = "tc" AndAlso IsRecurringTable = False Then
                                IsRecurringTable = True
                                Dim _currow = item.Parent.Parent
                                Dim iLinkCount As Integer = GetLinkCount(_fieldname, DoRs)

                                For i As Integer = 2 To iLinkCount
                                    ''CType(item.Parent.Parent.Parent, Table).Append(_currow.CloneNode(True))
                                    CType(item.Parent.Parent.Parent, Table).InsertAfter(_currow.CloneNode(True), _currow)
                                Next

                            End If
                        End If

                        If IsRecurringTable Then
                            _fieldvalue = GetLinkValue(_fieldname, iRowId, DoRs)
                        Else
                            _fieldvalue = GetLinkValue(_fieldname, 1, DoRs)
                        End If
                    ElseIf _fieldname.ToUpper().Contains("PAGEBREAK") Then
                        _fieldvalue = ""
                    Else
                        _fieldvalue = GetFieldValue(_fieldname, DoRs)
                    End If

                    ProcessDateFields(_innertext, _fieldvalue)

                    _innertext = _innertext.Replace(_fieldname, _fieldvalue)
                    sFieldName = _fieldname
                Next

                ''run here
                If (sFieldName.ToUpper().Contains("MMO") Or sFieldName.ToUpper().Contains("MMR")) Then
                    If ContainsHTML(_innertext) Then
                        UpdateMMR(_innertext, p, _maindocpart, sFieldName, iRowId)
                    Else
                        UpdateValue(_innertext, p)
                    End If
                ElseIf sFieldName.ToUpper().Contains("PAGEBREAK") Then
                    UpdateValue("", p)
                    InsertPageBreak(p)
                Else
                    UpdateValue(_innertext, p)
                    'If ContainsHTML(_innertext) Then
                    '    UpdateMMR(_innertext, p, _maindocpart, sFieldName, iRowId)
                    'Else
                    '    UpdateValue(_innertext, p)
                    'End If
                End If

            End If

        End If
    End Sub
    Private Shared Function ContainsHTML(ByVal strText As String) As Boolean

        If strText <> System.Web.HttpUtility.HtmlEncode(strText) Then
            Return True
        End If

        Return False
    End Function

    Private Shared Sub UpdateMMR(ByVal _innertext As String, ByVal p As DocumentFormat.OpenXml.Wordprocessing.Paragraph, ByVal _maindocpart As MainDocumentPart, ByVal sFieldName As String, ByVal iRowId As Integer)

        Dim i As Integer = 0

        Dim rpr As RunProperties = Nothing

        For Each run As Run In p.Elements(Of Run)()
            For Each text As Text In run.Elements(Of Text)()
                text.Text = ""

                If rpr Is Nothing Then
                    rpr = run.RunProperties
                End If

            Next
        Next

        Dim sfontName As String = IIf(String.IsNullOrEmpty(DEF_FONT_NAME), "Times New Roman", DEF_FONT_NAME)
        Dim fontsize As String = IIf(DEF_FONT_SIZE = Nothing Or DEF_FONT_SIZE <= 0, "20", DEF_FONT_SIZE.ToString())

        If rpr IsNot Nothing AndAlso rpr.RunFonts IsNot Nothing Then

            If rpr.RunFonts.Ascii Is Nothing Then
                sfontName = DEF_FONT_NAME
            Else
                sfontName = rpr.RunFonts.Ascii.Value
            End If
            If rpr.FontSize IsNot Nothing Then
                fontsize = rpr.FontSize.Val.Value
            End If

        End If

        Dim ifontsize As Integer = 0

        Integer.TryParse(fontsize, ifontsize)

        ifontsize = (ifontsize / 2)

        _innertext = _innertext.Replace("<p>", "").Replace("</p>", "<br>")

        ''remove the last <br>
        If _innertext.EndsWith("<br>") Then
            _innertext = _innertext.Substring(0, _innertext.Length - 4)
        End If

        _innertext = "<html><head></head><body style=""font-size:" & ifontsize.ToString & "pt;font-family: " & sfontName & ";"">" & _innertext & "</body></html>"

        Dim AltChunkId As String = sFieldName.Replace("%", "").Replace("(", "").Replace(")", "").Replace("_", "") & iRowId.ToString()
        Dim ms As MemoryStream = New MemoryStream(Encoding.UTF8.GetBytes(_innertext))
        Dim formatImportPart As AlternativeFormatImportPart = _maindocpart.AddAlternativeFormatImportPart(AlternativeFormatImportPartType.Html, AltChunkId)
        formatImportPart.FeedData(ms)
        Dim altChunk As AltChunk = New AltChunk()
        altChunk.Id = AltChunkId

        p.Parent.InsertBefore(altChunk, p)

        'For Each run As Run In p.Elements(Of Run)()
        '    For Each text As Text In run.Elements(Of Text)()
        '        run.RunProperties = rpr.Clone()
        '    Next
        'Next

    End Sub

    Private Shared Sub ProcessDateFields(ByRef _innertext As String, ByRef _fieldvalue As String)
        If _innertext.Contains("<SHORTDATE>") Then
            _innertext = Replace(_innertext, "<SHORTDATE>", "")
            _innertext = Replace(_innertext, "</SHORTDATE>", "")
            _fieldvalue = FormatDateTime(CDate(_fieldvalue), 2)
        ElseIf _innertext.Contains("<LONGDATE>") Then
            _innertext = Replace(_innertext, "<LONGDATE>", "")
            _innertext = Replace(_innertext, "</LONGDATE>", "")
            _fieldvalue = FormatDateTime(CDate(_fieldvalue), 1)
        End If
    End Sub

    Private Sub UpdateValue(ByVal _innertext As String, ByVal p As DocumentFormat.OpenXml.Wordprocessing.Paragraph)
        Dim i As Integer = 0

        For Each run As Run In p.Elements(Of Run)()

            For Each text As Text In run.Elements(Of Text)()

                If i = 0 Then
                    text.Text = _innertext
                Else
                    text.Text = ""
                End If

                i += 1
            Next
        Next
    End Sub

    Private Sub InsertPageBreak(ByVal p As DocumentFormat.OpenXml.Wordprocessing.Paragraph)
        Dim _pb = New Paragraph(New Run(New Break() With {.Type = BreakValues.Page}))
        p.Parent.InsertBefore(_pb, p)
    End Sub

    Public Function GetLinkCount(ByVal strCode As String, ByVal DoRs As clRowSet) As Long

        strCode = GetSQLFieldName(strCode)
        ''[Returns the number of linked records for the given code]

        If strCode = "LNK_XX_XX" Then Return 0

        GetLinkName(strCode)

        Dim doLink As clArray = New clArray()
        Dim oTable As DataTable = Nothing

        If DoRs.GetFileName() = "QT" Then
            doLink = DoRs.GetLinkVal(GetSQLFieldName(strCode + "%%CHK_INCLUDE"), doLink, True, 0, -1, "A_a", oTable)
        Else
            doLink = DoRs.GetLinkVal(GetSQLFieldName(strCode + "%%GID_ID"), doLink, True, 0, -1, "A_a", oTable)
        End If

        Dim _iLinkcount As Integer = 0

        If DoRs.GetFileName() = "QT" Then
            For index = 1 To doLink.GetDimension()
                If doLink.GetItem(index).ToString() = "Checked" Then
                    _iLinkcount = _iLinkcount + 1
                End If
            Next
        Else
            _iLinkcount = doLink.GetDimension()
        End If

        Return _iLinkcount

    End Function

    Private Sub GetLinkName(ByRef strCode As String)
        Dim strSplit() As String
        Dim iDQ As Integer
        If Left(strCode, 2) = "(%" Then
            strCode = Mid(strCode, 3, (Len(strCode) - 4))
        End If

        iDQ = InStr(strCode, "%%")
        If iDQ <> 0 Then
            strSplit = Split(strCode, "%%")
            strCode = strSplit(0)
        End If
    End Sub

    Private Function GetFieldValue(ByVal FieldName As String, ByVal DoRs As clRowSet) As String

        Dim sValue As String = DoRs.GetFieldVal(GetSQLFieldName(FieldName)).ToString()
        sValue = FormatFieldValue(FieldName, sValue)
        Return sValue

    End Function

    Private Function FormatFieldValue(FieldName As String, sValue As String) As String
        ''for CUR_ fields , print NULL when it is zero
        If (FieldName.ToUpper().Contains("CUR_") = True AndAlso bHideZeroCurValue = 1) Then
            If sValue = "0" Or sValue = "0.00" Or sValue = "0.0" Or sValue = "$0.00" Then
                sValue = ""
            End If
            'ElseIf (FieldName.ToUpper().Contains("MMO_")) Then
            '    Dim goUt As New clUtil()
            '    sValue = goUt.StripHTML(sValue)
            ''iCHKPrintType
        ElseIf (FieldName.ToUpper().Contains("CHK_")) Then

            If iCHKPrintType = 2 Then
                If sValue = "Checked" Then
                    sValue = "Yes"
                Else
                    sValue = "No"
                End If
            ElseIf iCHKPrintType = 3 Then
                If sValue = "Checked" Then
                    sValue = "True"
                Else
                    sValue = "False"
                End If
            End If

        End If

        Return sValue
    End Function

    Dim HTLinks As New Hashtable()

    Private Function GetLinkValue(ByVal LNKFieldName As String, ByVal RowIndex As Integer, ByVal DoRs As clRowSet) As String
        Dim doLink As clArray = New clArray()
        Dim oTable As DataTable = Nothing
        Dim sValue As String = ""

        If LNKFieldName.ToUpper().Contains("LNK_CONNECTED_QL") OrElse LNKFieldName.ToUpper().Contains("LNK_RENTAL_QL") OrElse LNKFieldName.ToUpper().Contains("LNK_SALES_QL") Then

            ''loop all items
            ''add all included items to another array
            ''get the item based on row index from new array
            Dim strcode As String = GetSQLFieldName(LNKFieldName)
            GetLinkName(strcode)

            Dim doLinkIncludedItems As clArray = New clArray()

            If RowIndex = 1 Then

                Dim doLinktmp As clArray = New clArray()
                Dim oTabletmp As DataTable = Nothing

                doLinktmp = DoRs.GetLinkVal(GetSQLFieldName(strcode + "%%CHK_INCLUDE"), doLinktmp, True, 0, -1, "A_a", oTabletmp)
                doLink = DoRs.GetLinkVal(GetSQLFieldName(LNKFieldName), doLink, True, 0, -1, "A_a", oTable)

                For index = 1 To doLinktmp.GetDimension()
                    If doLinktmp.GetItem(index).ToString() = "Checked" Then
                        doLinkIncludedItems.Add(doLink.GetItem(index))
                    End If
                Next

                If Not HTLinks.ContainsKey(LNKFieldName) Then
                    HTLinks.Add(LNKFieldName, doLinkIncludedItems)
                End If

            Else
                doLinkIncludedItems = HTLinks(LNKFieldName)
            End If

            sValue = doLinkIncludedItems.GetItem(RowIndex).ToString()

            'Dim doLinkIncludedItems As clArray = New clArray()
            'Dim doLinktmp As clArray = New clArray()
            'Dim oTabletmp As DataTable = Nothing

            'doLinktmp = DoRs.GetLinkVal(GetSQLFieldName(strcode + "%%CHK_INCLUDE"), doLinktmp, True, 0, -1, "A_a", oTabletmp)
            'doLink = DoRs.GetLinkVal(GetSQLFieldName(LNKFieldName), doLink, True, 0, -1, "A_a", oTable)

            'For index = 1 To doLinktmp.GetDimension()
            '    If doLinktmp.GetItem(index).ToString() = "Checked" Then
            '        doLinkIncludedItems.Add(doLink.GetItem(index))
            '    End If
            'Next

            'sValue = doLinkIncludedItems.GetItem(RowIndex).ToString()

        Else
            doLink = DoRs.GetLinkVal(GetSQLFieldName(LNKFieldName), doLink, True, 0, -1, "A_a", oTable)
            sValue = doLink.GetItem(RowIndex).ToString()
        End If

        ''for CUR_ fields , print NULL when it is zero
        If (LNKFieldName.ToUpper().Contains("CUR_")) Then
            If sValue = "0" Or sValue = "0.00" Or sValue = "0.0" Or sValue = "$0.00" Then
                sValue = ""
            End If
        End If

        Return sValue

    End Function

    Private Function GetSQLFieldName(ByVal sFieldName As String) As String
        Return sFieldName.Replace("(%", "").Replace("%)", "")
    End Function

    Private Sub ConvertWordToPDF(ByVal docFileName As String)
        Dim outFile As String = docFileName.ToLower().Replace(".docx", ".pdf")
        Dim dc As SautinSoft.Document.DocumentCore = SautinSoft.Document.DocumentCore.Load(docFileName)

        dc.Save(outFile)
    End Sub

    Public Function ConvertWordToPDFStream(ByVal docFileName As String) As MemoryStream
        Dim inpData() As Byte = File.ReadAllBytes(docFileName)
        Dim outData() As Byte = Nothing
        Using msInp As New MemoryStream(inpData)

            SautinSoft.Document.DocumentCore.Serial = "50017096491"

            ' Load a document.
            Dim dc As SautinSoft.Document.DocumentCore = SautinSoft.Document.DocumentCore.Load(msInp, New SautinSoft.Document.DocxLoadOptions())

            ' Save the document to PDF format.
            Using outMs As New MemoryStream()
                dc.Save(outMs, New SautinSoft.Document.PdfSaveOptions)
                outData = outMs.ToArray()
            End Using

            Dim stream As New MemoryStream(outData)
            Return stream

        End Using

    End Function

    Public Function ConvertWordToStream(ByVal docFileName As String) As MemoryStream
        Dim inpData() As Byte = File.ReadAllBytes(docFileName)
        Dim outData() As Byte = Nothing
        Using msInp As New MemoryStream(inpData)

            SautinSoft.Document.DocumentCore.Serial = "50017096491"
            ' Load a document.
            Dim dc As SautinSoft.Document.DocumentCore = SautinSoft.Document.DocumentCore.Load(msInp, New SautinSoft.Document.DocxLoadOptions())

            ' Save the document to PDF format.
            Using outMs As New MemoryStream()
                dc.Save(outMs, New SautinSoft.Document.DocxSaveOptions())
                outData = outMs.ToArray()
            End Using

            Dim stream As New MemoryStream(outData)
            Return stream

        End Using

    End Function

    Private Function ConvertWordToPDFStream2(ByVal docFileName As String) As MemoryStream

        Dim fileFormatProvider As IFormatProvider(Of Telerik.Windows.Documents.Flow.Model.RadFlowDocument) = Nothing
        Dim document As Telerik.Windows.Documents.Flow.Model.RadFlowDocument = Nothing
        Dim fileDownloadName As String = "{0}.{1}"
        Dim convertTo As String = "pdf"

        fileFormatProvider = New DocxFormatProvider()

        Dim inpData() As Byte = File.ReadAllBytes(docFileName)
        Dim msInp As New MemoryStream(inpData)

        document = fileFormatProvider.Import(msInp)
        ''fileDownloadName = String.Format(fileDownloadName, Path.GetFileNameWithoutExtension(docFileName), convertTo)

        Dim convertFormatProvider As New PdfFormatProvider()
        Dim mimeType As String = "application/pdf"
        Dim outData() As Byte = Nothing

        Using Ms As New MemoryStream()
            convertFormatProvider.Export(document, Ms)
            outData = Ms.ToArray()
        End Using

        Dim stream As New MemoryStream(outData)
        Return stream

    End Function

End Class


