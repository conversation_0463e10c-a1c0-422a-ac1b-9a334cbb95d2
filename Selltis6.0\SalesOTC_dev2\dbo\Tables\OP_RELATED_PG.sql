﻿CREATE TABLE [dbo].[OP_RELATED_PG] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_OP_RELATED_PG_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    [GID_PG] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_OP_RELATED_PG] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_OP_RELATED_PG] FOREIGN KEY ([GID_PG]) REFERENCES [dbo].[PG] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PG_CONNECTED_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[OP_RELATED_PG] NOCHECK CONSTRAINT [LNK_OP_RELATED_PG];


GO
ALTER TABLE [dbo].[OP_RELATED_PG] NOCHECK CONSTRAINT [LNK_PG_CONNECTED_OP];


GO
CREATE NONCLUSTERED INDEX [IX_OP_RELATED_PG]
    ON [dbo].[OP_RELATED_PG]([GID_OP] ASC, [GID_PG] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PG_CONNECTED_OP]
    ON [dbo].[OP_RELATED_PG]([GID_PG] ASC, [GID_OP] ASC);

