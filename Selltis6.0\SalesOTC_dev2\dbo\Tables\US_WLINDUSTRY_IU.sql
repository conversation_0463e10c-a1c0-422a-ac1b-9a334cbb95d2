﻿CREATE TABLE [dbo].[US_WLINDUSTRY_IU] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_US_WLINDUSTRY_IU_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_US] UNIQUE<PERSON>ENTIFIER NOT NULL,
    [GID_IU] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_US_WLINDUSTRY_IU] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_IU_WEBLEADUSER_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_WLINDUSTRY_IU] FOREIGN KEY ([GID_IU]) REFERENCES [dbo].[IU] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[US_WLINDUSTRY_IU] NOCHECK CONSTRAINT [LNK_IU_WEBLEADUSER_US];


GO
ALTER TABLE [dbo].[US_WLINDUSTRY_IU] NOCHECK CONSTRAINT [LNK_US_WLINDUSTRY_IU];


GO
CREATE NONCLUSTERED INDEX [IX_US_WLINDUSTRY_IU]
    ON [dbo].[US_WLINDUSTRY_IU]([GID_US] ASC, [GID_IU] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_IU_WEBLEADUSER_US]
    ON [dbo].[US_WLINDUSTRY_IU]([GID_IU] ASC, [GID_US] ASC);

