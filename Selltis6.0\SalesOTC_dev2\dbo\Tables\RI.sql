﻿CREATE TABLE [dbo].[RI] (
    [GID_ID]             UNIQUEIDENTIFIER CONSTRAINT [DF_RI_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),(0))+CONVERT([binary](2),'RI',(0)))+CONVERT([binary](6),getutcdate(),(0)),(0))) ROWGUIDCOL NOT NULL,
    [BI__ID]             BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]           NVARCHAR (80)    NULL,
    [DTT_CreationTime]   DATETIME         CONSTRAINT [DF_RI_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]       TINYINT          NULL,
    [TXT_ModBy]          VARCHAR (4)      NULL,
    [DTT_ModTime]        DATETIME         CONSTRAINT [DF_RI_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_RigName]        NVARCHAR (50)    NULL,
    [MMO_ImportData]     NTEXT            NULL,
    [SI__ShareState]     TINYINT          CONSTRAINT [DF_RI_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]   UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]       VARCHAR (50)     NULL,
    [TXT_ExternalID]     NVARCHAR (80)    NULL,
    [TXT_ExternalSource] VARCHAR (10)     NULL,
    [TXT_ImpJobID]       VARCHAR (20)     NULL,
    [GID_Primary_CO]     UNIQUEIDENTIFIER NULL,
    [GID_Related_LO]     UNIQUEIDENTIFIER NULL,
    [MMO_Notes]          NTEXT            NULL,
    CONSTRAINT [PK_RI] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_RI_RigName]
    ON [dbo].[RI]([TXT_RigName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RI_CreatedBy_US]
    ON [dbo].[RI]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RI_TXT_ImportID]
    ON [dbo].[RI]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RI_CreationTime]
    ON [dbo].[RI]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RI_ModDateTime]
    ON [dbo].[RI]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RI_Name]
    ON [dbo].[RI]([SYS_NAME] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_RI_BI__ID]
    ON [dbo].[RI]([BI__ID] ASC);

