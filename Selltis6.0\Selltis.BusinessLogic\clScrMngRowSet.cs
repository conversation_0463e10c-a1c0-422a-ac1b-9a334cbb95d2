﻿using System;
using System.Web;
using System.Reflection;
using System.Configuration;
using System.Data;

namespace Selltis.BusinessLogic
{
	public class clScrMngRowSet
	{

		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clLog goLog;
		private clDefaults goDef;
		private clScrMngRowSet goScr;
		private clBaseScripts oScripts = new clBaseScripts();
		private clScriptsRowSetCustom oScriptsCustom = new clScriptsRowSetCustom();

		public void Initialize()
		{

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			goDef = (Selltis.BusinessLogic.clDefaults)HttpContext.Current.Session["goDef"];
			goScr = (Selltis.BusinessLogic.clScrMngRowSet)HttpContext.Current.Session["goScr"];

			oScripts.Initialize();
			oScriptsCustom.Initialize();


		}

		public bool RunScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5, ref object par_oReturn, ref bool par_bRunNext)
		{
			string tempVar = "";
			return RunScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref par_bRunNext, ref tempVar);
		}

		public bool RunScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5, ref object par_oReturn)
		{
			bool tempVar = true;
			string tempVar2 = "";
			return RunScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref tempVar, ref tempVar2);
		}

		public bool RunScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, "", "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunScript(par_sScriptName, ref par_doCallingObject, par_doArray, "", "", "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunScript(string par_sScriptName, ref object par_doCallingObject)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunScript(par_sScriptName, ref par_doCallingObject, null, "", "", "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunScript(string par_sScriptName)
		{
			object tempVar = null;
			object tempVar2 = null;
			bool tempVar3 = true;
			string tempVar4 = "";
			return RunScript(par_sScriptName, ref tempVar, null, "", "", "", "", "", ref tempVar2, ref tempVar3, ref tempVar4);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Function RunScript(ByVal par_sScriptName As String, Optional ByRef par_doCallingObject As Object = Nothing, Optional ByVal par_doArray As Object = Nothing, Optional ByVal par_s1 As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "", Optional ByRef par_oReturn As Object = Nothing, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
		public bool RunScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections)
		{
			//Function RunScript(ByVal par_sScriptName As String, Optional ByRef par_doCallingObject As Object = Nothing, Optional ByVal par_doArray As Object = Nothing, Optional ByVal par_s1 As Object = "", Optional ByVal par_s2 As Object = "", Optional ByVal par_s3 As Object = "", Optional ByVal par_s4 As Object = "", Optional ByVal par_s5 As Object = "", Optional ByRef par_oReturn As Object = Nothing, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
			//MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.
			//       Also resetting errors when returning True to prevent casual warnings from being
			//       interpreted by the calling code as errors to display to the user.
			//MI 10/25/07 Added par_sSections.

			//RE WARNINGS: We want warnings to survive only 'upstream' when a script sets Return False or par_bRunNext=False.
			//Warnings are reset (goErr.SetError()) before running the Pre, main, and Post scripts. This ensures that a warning set in a 
			//previously run script won't be picked up within the script we are invoking and erroneously interpreted as
			//a warning. If the script doesn't test warnings, all of this is irrelevant, but if testing code is added later,
			//the coder will be able to rely that the slate was cleaned when the script was invoked.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start: '" + par_sScriptName + "'.", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//Return oScripts._RunScriptManager(par_sScriptName, _
			//                                    par_doCallingObject, _
			//                                    par_doArray, _
			//                                    par_s1, _
			//                                    par_s2, _
			//                                    par_s3, _
			//                                    par_s4, _
			//                                    par_s5, _
			//                                    par_oReturn, _
			//                                    par_bRunNext, _
			//                                    par_sSections)

			bool bResult = false;
			System.Type mType = typeof(clBaseScripts);
			System.Reflection.MethodInfo mInfo = mType.GetMethod(par_sScriptName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.IgnoreCase);

			if (mInfo == null)
			{
				var dll = Assembly.LoadFile(HttpContext.Current.Request.MapPath("/") + "//bin" + "//Selltis.Core.dll");
				mType = dll.GetType("Selltis.Core.Scripts");
				mInfo = mType.GetMethod(par_sScriptName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.IgnoreCase);

			}

			//TO-DO
			//bResult = goScr.RunCustomScript(par_sScriptName & "_Pre", par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)

			//If Not mInfo Is Nothing Then
			//    Dim oParamArray() As Object = {par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections}
			//    If par_bRunNext And bResult Then
			//        goErr.SetError()   'MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.
			//        Dim oClass = Activator.CreateInstance(mType)
			//        'Dim objScrt = TryCast(oClass, clBaseScripts)
			//        oClass.Initialize()
			//        bResult = mInfo.Invoke(oClass, oParamArray)
			//        par_bRunNext = oParamArray(8)     'bRunNext
			//    End If
			//Else
			//    If par_bRunNext And bResult Then bResult = goScr.RunCustomScript(par_sScriptName, par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)

			//End If

			//If par_bRunNext And bResult Then bResult = goScr.RunCustomScript(par_sScriptName & "_Post", par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)

			if (mInfo != null)
			{
				//Method found in clScripts
				//Pre script: if not found returns True and bRunNext remains True
				bResult = goScr.RunCustomScript(par_sScriptName + "_Pre", ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref par_bRunNext, ref par_sSections);
				//Main script - runs only if the pre script isn't overriding it (bRunNext = True) and didn't return false
				//Dim oParamArray() As Object = {par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections}
				object[] oParamArray = {par_doCallingObject, par_oReturn, par_bRunNext, par_sSections, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5};
				if (par_bRunNext && bResult)
				{
					goErr.SetError(); //MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.
					dynamic oClass = Activator.CreateInstance(mType);
					//Dim objScrt = TryCast(oClass, clBaseScripts)
					oClass.Initialize();
					bResult = Convert.ToBoolean(mInfo.Invoke(oClass, oParamArray));
					//par_bRunNext = oParamArray(8)     'bRunNext
					par_bRunNext = Convert.ToBoolean(oParamArray[2]);
				}
				//Post script - run only if the main script didn't return false and the _Pre script didn't set bRunNext to false
				if (par_bRunNext && bResult)
				{
					//bResult = goScr.RunCustomScript(par_sScriptName & "_Post", oParamArray(0), oParamArray(1), oParamArray(2), oParamArray(3), oParamArray(4), oParamArray(5), oParamArray(6), oParamArray(7), oParamArray(8), oParamArray(9))
					bResult = goScr.RunCustomScript(par_sScriptName + "_Post", ref oParamArray[0], oParamArray[4], Convert.ToString(oParamArray[5]), Convert.ToString(oParamArray[6]), Convert.ToString(oParamArray[7]), Convert.ToString(oParamArray[8]), Convert.ToString(oParamArray[9]), ref oParamArray[1], ref par_bRunNext, ref par_sSections);
					//Set all ByRef variables in case the called custom script modified them
					par_doCallingObject = oParamArray[0];
					par_oReturn = oParamArray[1];
					//par_bRunNext = Convert.ToBoolean(oParamArray[2]);
					//par_sSections = Convert.ToString(oParamArray[3]);
				}
			}
			else
			{
				//'NEW CODE 02242016 RUNSCRIPT USING DLL NAME/PATH
				//bResult = RunScriptLoadDllCSHARP(par_sScriptName, par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, par_oReturn, par_bRunNext, par_sSections)

				//Method not found in clScripts, check whether we have a custom '_PRE', main, or '_POST' script
				bResult = goScr.RunCustomScript(par_sScriptName + "_Pre", ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref par_bRunNext, ref par_sSections);
				if (par_bRunNext && bResult)
				{
					bResult = goScr.RunCustomScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref par_bRunNext, ref par_sSections);
				}
				if (par_bRunNext && bResult)
				{
					bResult = goScr.RunCustomScript(par_sScriptName + "_Post", ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref par_bRunNext, ref par_sSections);
				}
			}

			//MI 3/31/09 Added. If result is true, we reset warnings so that they are not erroneously
			//picked up in the calling code. Warnings that are not followed with Return false are considered
			//informational and are not meant to be handled by the calling code. There can be multiple
			//warnings raised within a code module or modules it calls. In this case, only the last 
			//warning would be picked up and possibly reported by the calling code. Such cases must be
			//dealt with explicitly, capturing and storing each warning as it occurs, then returned together
			//as a single warning followed by Return false.
			//For example: a form is calling FormOnSave script, which calls GetFieldVal, which raises a warning. 
			//If FormOnSave returns False, the form will get the warning and display it in a warning panel. 
			//If FormOnSave returns True, the form won't look for warnings anyway, but assuming that it does
			//it will not get the warning because we reset it here. 
			if (bResult)
			{
				goErr.SetError();
			}

			return bResult;


			//---------------- OLD NGP CODE -------------------
			//'CS In progress, RH or MI finish porting
			//'PORT. This is the method that gets called from scripts and inside WD code
			//'to run a particular script.

			//'---------
			//'SET ERROR
			//'---------
			//'LAST MOD: MI 1/19/05		AUTHOR: FH 4/5/04
			//'PURPOSE:
			//'		Front end for starting a script (from a script or from WinDev code).
			//'		Originally this was a procedure in W_PROC.
			//'PARAMETERS:
			//'		par_sScriptPageIDOrName:	Page ID of the script or Name of the script
			//'			All other parameter depends of the calling code:
			//'		par_doCallingObject: 
			//'		par_doArray: 
			//'		par_s1: 
			//'		par_s2: 
			//'		par_s3: 
			//'		par_s4: 
			//'		par_s5: 
			//'RETURNS:
			//'		True of false, with a possible SetError. If script not found, unloadable, etc, returns -1
			//'HOW IT WORKS:
			//'		Try to find the script meta record, based on it's pageID or it's name, then try to 
			//'		load and execute the script
			//'EXAMPLE:
			//'		if not goScr:RunScript(sScriptName) then

			//Dim sProc As String = "clScrMng::RunScript"
			//'if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_sScriptPageIDOrName, SELL_LOGLEVEL_DEBUG)

			//goLog.SetError()
			//Dim lErr As Long = 0

			//Dim sPageID As String
			//'Dim sName As String
			//Dim sMeta As String
			//Dim iResult As Integer
			//'First try to find the script
			//If goTR.IsPageID(par_sScriptPageIDOrName) Then
			//    sPageID = par_sScriptPageIDOrName
			//    'Look if we find the record
			//    sMeta = Trim(goMeta.PageRead(goP.GetUserTID(), sPageID))
			//    If sMeta = "" Then
			//        goLog.SetError(10506, sProc, "", sPageID)
			//        ' The requested script '[1]' was not found. 
			//        '
			//        'Please contact Sellis support.
			//        Return (-1)
			//    End If
			//    'Else  'CS: Commented out Else b/c of hReadSeek
			//    '    sName = UCase(par_sScriptPageIDOrName)
			//    '    If Not goData.hReadSeek("_META", "MetaTypeSort1", goTR.Complete("SCR", dimension(_META.TXT_MetaType)) & goTR.Complete(sName, dimension(_META.TXT_SortValue1))) Then
			//    '        goLog.SetError(10506, sProc, "", sName)
			//    '        ' The requested script '[1]' was not found. 
			//    '        '
			//    '        'Please contact Sellis support.
			//    '        Return (-1)
			//    '    End If
			//    '    'Here, we found it, we can take its pageID
			//    '    sPageID = _META.TXT_Page
			//End If

			//'And then try to load it
			//'doScript is object dynamic=new clscript()

			//'CS Commented out here to end until clScript available
			//'Dim doScript As New clScript
			//'iResult = doScript.Init(sPageID)
			//'If iResult Then
			//'    iResult = doScript : Execute(par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5)
			//'    End

			//'    Select Case goLog.GetLastError()
			//'        Case "E00000"
			//'            'No error was set
			//'        Case "E10505", "E10901"
			//'            '10505: Recursivity forbidden. 10901: exception during execution
			//'            'Display these errors immediately since they are critical.
			//'            goLog.DisplayLastError()
			//'        Case Else
			//'            'Pass the error to the calling code without displaying it
			//'            'The calling code decides whether to "know" about the error or not.
			//'            lErr = goLog.SaveLastError()
			//'    End Select

			//'    delete(doScript)

			//'    If lErr > 0 Then
			//'        goLog.RestoreLastError(lErr)
			//'        goLog.DeleteSavedLastErrorNumber(lErr)
			//'    End If

			//Return (iResult)
		}

		public bool IsSectionEnabled(string par_sProc, string par_sSections, string par_sSectionName, bool par_bRunByDefault = true)
		{
			//MI 10/25/07 Created.
			//PURPOSE:
			//       To be used within script methods in clScripts or cus_clScriptsCustom
			//       to test whether a code "section" should be executed or not.
			//       This allows _Pre scripts or code calling goScr.RunScript() to suppress 
			//       or force portions of the main script to run. It can also suppress/force
			//       sections of _Post scripts. For more info, see comments about the 
			//       par_sSections parameter in clScripts._RunScriptManager.
			//FUTURE PLANS:
			//       A UI facility will allow defining which script sections to enable or
			//       disable. This method will read that definition from MD and merge it
			//       with par_sSectionsIni using gotr.MergeIniStrings(). par_sSectionsIni
			//       will have precedence so that the MD always can be overriden programmatically.
			//PARAMETERS:
			//       par_sProc: The sProc parameter that defines the class and method name
			//           in every script. If the script doesn't have a variable sProc,
			//           create it as "Script::ScriptName" both in main and custom scripts.
			//       par_sSections: Ini string that contains the definitions of which
			//           section to suppress and which to force run. This is passed to
			//           scripts via goScr.RunScript() through par_sSections parameter. 
			//           Supported format is:
			//               SectionName1=1
			//               SectionName2=0
			//           where 1 means 'run' and 0 means 'do not run'.
			//       par_sSectionName: Name of the section.
			//       par_bRunByDefault: Optional: If true (default), the section will run 
			//           if an override is not found
			//RETURNS:
			//       True if the section is enabled, False otherwise

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start: '" + par_sProc + "' section '" + par_sSectionName + "'.", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sDefault = null;
			bool bResult = false;

			//Try
			if (par_bRunByDefault)
			{
					sDefault = "1";
				}
				else
				{
					sDefault = "0";
				}

				if (goTR.StrRead(par_sSections, par_sSectionName, sDefault, false) == "1")
				{
					bResult = true;
				}
				else
				{
					bResult = false;
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return bResult;

		}

		private AppDomain domain = null;

		public bool RunCustomScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5, ref object par_oReturn, ref bool par_bRunNext)
		{
			string tempVar = "";
			return RunCustomScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref par_bRunNext, ref tempVar);
		}

		public bool RunCustomScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5, ref object par_oReturn)
		{
			bool tempVar = true;
			string tempVar2 = "";
			return RunCustomScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref tempVar, ref tempVar2);
		}

		public bool RunCustomScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunCustomScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunCustomScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunCustomScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunCustomScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunCustomScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunCustomScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunCustomScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunCustomScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunCustomScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, "", "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunCustomScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunCustomScript(par_sScriptName, ref par_doCallingObject, par_doArray, "", "", "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunCustomScript(string par_sScriptName, ref object par_doCallingObject)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunCustomScript(par_sScriptName, ref par_doCallingObject, null, "", "", "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunCustomScript(string par_sScriptName)
		{
			object tempVar = null;
			object tempVar2 = null;
			bool tempVar3 = true;
			string tempVar4 = "";
			return RunCustomScript(par_sScriptName, ref tempVar, null, "", "", "", "", "", ref tempVar2, ref tempVar3, ref tempVar4);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Function RunCustomScript(ByVal par_sScriptName As String, Optional ByRef par_doCallingObject As Object = Nothing, Optional ByVal par_doArray As Object = Nothing, Optional ByVal par_s1 As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "", Optional ByRef par_oReturn As Object = Nothing, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
		public bool RunCustomScript(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections)
		{
			//Function RunCustomScript(ByVal par_sScriptName As String, Optional ByRef par_doCallingObject As Object = Nothing, Optional ByVal par_doArray As Object = Nothing, Optional ByVal par_s1 As Object = "", Optional ByVal par_s2 As Object = "", Optional ByVal par_s3 As Object = "", Optional ByVal par_s4 As Object = "", Optional ByVal par_s5 As Object = "", Optional ByRef par_oReturn As Object = Nothing, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
			//MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.
			//MI 10/25/07 Added par_sSections.

			try
			{



				string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
				goLog.Log(sProc, "Start: '" + par_sScriptName + "'", (short)clC.SELL_LOGLEVEL_DEBUG, true);

				//Return oScriptsCustom._RunScriptManager(par_sScriptName, _
				//                                    par_doCallingObject, _
				//                                    par_doArray, _
				//                                    par_s1, _
				//                                    par_s2, _
				//                                    par_s3, _
				//                                    par_s4, _
				//                                    par_s5, _
				//                                    par_oReturn, _
				//                                    par_bRunNext, _
				//                                    par_sSections)


				bool bResult = false;
				//Dim bRunNext As Boolean = True

				//Dim dll = Assembly.LoadFile(HttpContext.Current.Request.MapPath("/") & "//bin" & "//Selltis.Core.dll")
				//Dim mType As System.Type = dll.GetType("Selltis.Core.ScriptsCustom")

				//Dim sHostName As String = ""
				//Dim sCustomDLLPath As String = ""

				//#If DEBUG Then
				//        sCustomDLLPath = HttpContext.Current.Request.MapPath("/") + "bin" + "//Selltis.Custom.dll"
				//#End If

				//read the "HostingEnvironment" from web config and if test then get the port number
				//Dim sHostingEnvironment As String = ConfigurationManager.AppSettings("HostingEnvironment").ToString().ToLower()
				//Dim sCustomDLLFolder As String = ""
				//If sHostingEnvironment = "debugging" Then
				//    sCustomDLLFolder = HttpContext.Current.Request.MapPath("/") + "bin"
				//    sCustomDLLPath = sCustomDLLFolder + "//Selltis.Custom.dll"
				//End If

				//If String.IsNullOrEmpty(sCustomDLLPath) Then
				//    If sHostingEnvironment = "staging" Then
				//        sHostName = HttpContext.Current.Request.Url.Port.ToString()
				//    Else
				//        sHostName = HttpContext.Current.Request.Url.Host 'dev.selltis.com
				//        sHostName = sHostName.Replace(".selltis.com", "") 'dev
				//    End If
				//    'sCustomDLLPath = (Convert.ToString(HttpContext.Current.Request.MapPath("/") + "bin\CustomFiles\") & sHostName) + "\Selltis.Custom.dll"
				//    sCustomDLLFolder = (Convert.ToString(HttpContext.Current.Request.MapPath("/") + "bin\CustomFiles\") & sHostName)
				//    sCustomDLLPath = sCustomDLLFolder + "\Selltis.Custom.dll"
				//End If

				// Dim sCustomDLLName As String = DirectCast(HttpContext.Current.Session("SiteSettings"), DataTable).Rows(0)("CustomDLLName")
				//Dim dll = Assembly.LoadFile(HttpContext.Current.Request.MapPath("/") + "bin\" + sCustomDLLName)
				//Dim mType As System.Type = dll.GetType("Selltis.Custom.ScriptsCustom")

				if (HttpContext.Current.Session["CUSTOMDLLEXISTED"] != null)
				{
					if (Convert.ToBoolean(HttpContext.Current.Session["CUSTOMDLLEXISTED"]) == false)
					{
						return true;
					}
				}


				string sHostingEnvironment = ConfigurationManager.AppSettings["HostingEnvironment"].ToString().ToLower();
				string sCusFilesPath = ConfigurationManager.AppSettings["CustomFilesPath"].ToString();
				string sHostName = clSettings.GetHostName();

				string substring = sCusFilesPath.Substring(1, 2);
				if (substring == ":\\")
				{
					sCusFilesPath = System.Configuration.ConfigurationManager.AppSettings["CustomFilesPath"].ToString();
				}
				else
				{
					sCusFilesPath = HttpContext.Current.Server.MapPath(System.Configuration.ConfigurationManager.AppSettings["CustomFilesPath"].ToString());
				}

				string sPortNumber = HttpContext.Current.Request.Url.Port.ToString();

				if (sHostingEnvironment == "staging")
				{
					sHostName = sHostName + "_" + sPortNumber;
				}
				else if (sHostingEnvironment == "debugging")
				{
					sHostName = "default";
				}

				string sCustomDLLName = Convert.ToString(((DataTable)HttpContext.Current.Session[sHostName + "_SiteSettings"]).Rows[0]["CustomDLLName"]);
				string sCustomDLLFilePath = sCusFilesPath + sHostName + "\\\\" + sCustomDLLName;

				string cusDLLFilePath = clSettings.GetCustomDLL_FilePath();

				System.Reflection.Assembly dll = null;

				if (sHostingEnvironment == "debugging")
				{
					dll = Assembly.LoadFrom(cusDLLFilePath);
				}
				else
				{
					dll = Assembly.LoadFile(cusDLLFilePath);
				}

				System.Type mType = dll.GetType("Selltis.Custom.ScriptsCustom");

				//Dim domaininfo As New AppDomainSetup()
				//domaininfo.ApplicationBase = HttpContext.Current.Request.MapPath("/") + "bin\"
				//Dim adevidence As System.Security.Policy.Evidence = AppDomain.CurrentDomain.Evidence
				//domain = AppDomain.CreateDomain("MyDomain", adevidence, domaininfo)

				//Dim type As Type = GetType(Proxy)
				//Dim value = DirectCast(domain.CreateInstanceAndUnwrap(type.Assembly.FullName, type.FullName), Proxy)

				//Dim dll = value.GetAssembly(HttpContext.Current.Request.MapPath("/") + "bin\" + sCustomDLLName)
				//Dim mType = dll.GetType("Selltis.Custom.ScriptsCustom")

				System.Reflection.MethodInfo mInfo = mType.GetMethod(par_sScriptName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.IgnoreCase);

				if (mInfo != null)
				{
					object[] oParamArray = {par_doCallingObject, par_oReturn, par_bRunNext, par_sSections, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5};
					if (par_bRunNext)
					{
						goErr.SetError(); //MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.
						dynamic oClass = Activator.CreateInstance(mType);
						//Dim objScrt = TryCast(oClass, clBaseScripts)
						oClass.Initialize();
						bResult = Convert.ToBoolean(mInfo.Invoke(oClass, oParamArray));
						//bResult = mInfo.Invoke(oScriptsCustom, oParamArray)
						//Set all ByRef variables in case the called custom script modified them
						par_doCallingObject = oParamArray[0];
						par_oReturn = oParamArray[1];
						par_bRunNext = Convert.ToBoolean(oParamArray[2]);
						par_sSections = Convert.ToString(oParamArray[3]);
					}
				}
				else
				{
					//Undefined script is same as blank script = success = Return True
					//par_bRunNext = True
					bResult = true;
				}

				return bResult;

			}
			catch (Exception ex)
			{
				if (domain != null)
				{
					AppDomain.Unload(domain);
				}
				return false;
			}
			finally
			{
				if (domain != null)
				{
					AppDomain.Unload(domain);
				}
			}

		}


		public bool RunScriptLoadDllCSHARP(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5, ref object par_oReturn, ref bool par_bRunNext)
		{
			string tempVar = "";
			return RunScriptLoadDllCSHARP(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref par_bRunNext, ref tempVar);
		}

		public bool RunScriptLoadDllCSHARP(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5, ref object par_oReturn)
		{
			bool tempVar = true;
			string tempVar2 = "";
			return RunScriptLoadDllCSHARP(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref tempVar, ref tempVar2);
		}

		public bool RunScriptLoadDllCSHARP(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunScriptLoadDllCSHARP(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunScriptLoadDllCSHARP(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunScriptLoadDllCSHARP(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunScriptLoadDllCSHARP(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunScriptLoadDllCSHARP(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunScriptLoadDllCSHARP(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunScriptLoadDllCSHARP(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunScriptLoadDllCSHARP(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunScriptLoadDllCSHARP(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, "", "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunScriptLoadDllCSHARP(string par_sScriptName, ref object par_doCallingObject, object par_doArray)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunScriptLoadDllCSHARP(par_sScriptName, ref par_doCallingObject, par_doArray, "", "", "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunScriptLoadDllCSHARP(string par_sScriptName, ref object par_doCallingObject)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return RunScriptLoadDllCSHARP(par_sScriptName, ref par_doCallingObject, null, "", "", "", "", "", ref tempVar, ref tempVar2, ref tempVar3);
		}

		public bool RunScriptLoadDllCSHARP(string par_sScriptName)
		{
			object tempVar = null;
			object tempVar2 = null;
			bool tempVar3 = true;
			string tempVar4 = "";
			return RunScriptLoadDllCSHARP(par_sScriptName, ref tempVar, null, "", "", "", "", "", ref tempVar2, ref tempVar3, ref tempVar4);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Function RunScriptLoadDllCSHARP(ByVal par_sScriptName As String, Optional ByRef par_doCallingObject As Object = Nothing, Optional ByVal par_doArray As Object = Nothing, Optional ByVal par_s1 As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "", Optional ByRef par_oReturn As Object = Nothing, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
		public bool RunScriptLoadDllCSHARP(string par_sScriptName, ref object par_doCallingObject, object par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections)
		{
			//Function RunScript(ByVal par_sScriptName As String, Optional ByRef par_doCallingObject As Object = Nothing, Optional ByVal par_doArray As Object = Nothing, Optional ByVal par_s1 As Object = "", Optional ByVal par_s2 As Object = "", Optional ByVal par_s3 As Object = "", Optional ByVal par_s4 As Object = "", Optional ByVal par_s5 As Object = "", Optional ByRef par_oReturn As Object = Nothing, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
			//MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.
			//       Also resetting errors when returning True to prevent casual warnings from being
			//       interpreted by the calling code as errors to display to the user.
			//MI 10/25/07 Added par_sSections.

			//RE WARNINGS: We want warnings to survive only 'upstream' when a script sets Return False or par_bRunNext=False.
			//Warnings are reset (goErr.SetError()) before running the Pre, main, and Post scripts. This ensures that a warning set in a 
			//previously run script won't be picked up within the script we are invoking and erroneously interpreted as
			//a warning. If the script doesn't test warnings, all of this is irrelevant, but if testing code is added later,
			//the coder will be able to rely that the slate was cleaned when the script was invoked.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start: '" + par_sScriptName + "'.", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//Return oScripts._RunScriptManager(par_sScriptName, _
			//                                    par_doCallingObject, _
			//                                    par_doArray, _
			//                                    par_s1, _
			//                                    par_s2, _
			//                                    par_s3, _
			//                                    par_s4, _
			//                                    par_s5, _
			//                                    par_oReturn, _
			//                                    par_bRunNext, _
			//                                    par_sSections)

			bool bResult = false;
			var dll = Assembly.LoadFile(HttpContext.Current.Request.MapPath("/") + "//bin" + "//Selltis.Core.dll");
			//Dim mType As System.Type = Type.GetType("Selltis.BusinessLogic.clBaseScripts")
			System.Type mType = dll.GetType("Selltis.Core.Scripts");

			System.Reflection.MethodInfo mInfo = mType.GetMethod(par_sScriptName, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.IgnoreCase);
			if (mInfo != null)
			{
				//Method found in clScripts
				//Pre script: if not found returns True and bRunNext remains True
				bResult = goScr.RunCustomScript(par_sScriptName + "_Pre", ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref par_bRunNext, ref par_sSections);
				//Main script - runs only if the pre script isn't overriding it (bRunNext = True) and didn't return false
				/////////////////SCRIPT PART//////////////
				object[] oParamArray = {par_doCallingObject, par_oReturn, par_bRunNext, par_sSections, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5};
				if (par_bRunNext && bResult)
				{
					goErr.SetError(); //MI 3/31/09 Added goErr.SetError() to clear last warning so the invoked script doesn't erroneously 'see' it.

					//OLD CODE 02242016
					//Dim oClass = Activator.CreateInstance(mType)
					//Dim objScrt = TryCast(oClass, clBaseScripts)
					//objScrt.Initialize()
					//bResult = mInfo.Invoke(objScrt, oParamArray)
					//NEW CODE 02242016
					dynamic cClass = Activator.CreateInstance(mType);
					cClass.Initialize();
					bResult = Convert.ToBoolean(mInfo.Invoke(cClass, oParamArray));
					par_bRunNext = Convert.ToBoolean(oParamArray[2]); //bRunNext
				}

				//Post script - run only if the main script didn't return false and the _Pre script didn't set bRunNext to false
				if (par_bRunNext && bResult)
				{
					bResult = goScr.RunCustomScript(par_sScriptName + "_Post", ref oParamArray[0], oParamArray[4], Convert.ToString(oParamArray[5]), Convert.ToString(oParamArray[6]), Convert.ToString(oParamArray[7]), Convert.ToString(oParamArray[8]), Convert.ToString(oParamArray[9]), ref oParamArray[1], ref par_bRunNext, ref par_sSections);
					//Set all ByRef variables in case the called custom script modified them
					par_doCallingObject = oParamArray[0];
					par_oReturn = oParamArray[1];
					//par_bRunNext = Convert.ToBoolean(oParamArray[2]);
					//par_sSections = Convert.ToString(oParamArray[3]);
				}
			}
			else
			{
				//Method not found in clScripts, check whether we have a custom '_PRE', main, or '_POST' script
				bResult = goScr.RunCustomScript(par_sScriptName + "_Pre", ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref par_bRunNext, ref par_sSections);
				if (par_bRunNext && bResult)
				{
					bResult = goScr.RunCustomScript(par_sScriptName, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref par_bRunNext, ref par_sSections);
				}
				if (par_bRunNext && bResult)
				{
					bResult = goScr.RunCustomScript(par_sScriptName + "_Post", ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, par_s5, ref par_oReturn, ref par_bRunNext, ref par_sSections);
				}
			}

			//MI 3/31/09 Added. If result is true, we reset warnings so that they are not erroneously
			//picked up in the calling code. Warnings that are not followed with Return false are considered
			//informational and are not meant to be handled by the calling code. There can be multiple
			//warnings raised within a code module or modules it calls. In this case, only the last 
			//warning would be picked up and possibly reported by the calling code. Such cases must be
			//dealt with explicitly, capturing and storing each warning as it occurs, then returned together
			//as a single warning followed by Return false.
			//For example: a form is calling FormOnSave script, which calls GetFieldVal, which raises a warning. 
			//If FormOnSave returns False, the form will get the warning and display it in a warning panel. 
			//If FormOnSave returns True, the form won't look for warnings anyway, but assuming that it does
			//it will not get the warning because we reset it here. 
			if (bResult)
			{
				goErr.SetError();
			}

			return bResult;
		}
	}

	public class Proxy : MarshalByRefObject
	{
		public Assembly GetAssembly(string assemblyPath)
		{
			try
			{
				return Assembly.LoadFile(assemblyPath);
			}
			catch (Exception generatedExceptionName)
			{
				// throw new InvalidOperationException(ex);
				return null;
			}
		}
	}

}
