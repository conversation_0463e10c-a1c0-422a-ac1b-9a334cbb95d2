﻿using System;
using System.Collections;
using System.Data;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

using Telerik.Windows.Documents.Common.FormatProviders;
using Telerik.Windows.Documents.Flow.FormatProviders.Docx;
//Imports Telerik.Windows.Documents.Flow.FormatProviders.Html
using Telerik.Windows.Documents.Flow.FormatProviders.Pdf;
//Imports Telerik.Windows.Documents.Flow.FormatProviders.Rtf
//Imports Telerik.Windows.Documents.Flow.FormatProviders.Txt
//Imports Telerik.Windows.Documents.Flow.Model



namespace Selltis.BusinessLogic
{
	public class WordDocumentHelper
	{
		private bool IsRecurringTable = false;

		private int bShowHTML;
		private int bHideZeroCurValue;
		public int iCHKPrintType = 1; //'1 -- Checked/Unchecked, 2-- Yes/No , 3-- True/False
		public static int DEF_FONT_SIZE;
		public static string DEF_FONT_NAME;

		public MemoryStream ProcessDocument(string filename, clRowSet DoRs, int iFlag = 0, int iShowHTML = 0, int ibHideZeroCurValue = 0, int iDocType = 1, string sFileDisplayName = "", int iDefFontSize = 20, string sDefFontName = "Times New Roman")
		{
			WordprocessingDocument wordprocessingDocument = WordprocessingDocument.Open(filename, true);
			Body body = wordprocessingDocument.MainDocumentPart.Document.Body;

			//'iDocType -- 1-PDF (Default), 2--MS Word (.docx)

			bShowHTML = Convert.ToInt32(iShowHTML);
			bHideZeroCurValue = Convert.ToInt32(ibHideZeroCurValue);
			DEF_FONT_SIZE = Convert.ToInt32(iDefFontSize);
			DEF_FONT_NAME = sDefFontName;

			MainDocumentPart _maindocpart = wordprocessingDocument.MainDocumentPart;

			//'Header
			foreach (var item in wordprocessingDocument.MainDocumentPart.HeaderParts)
			{

				foreach (DocumentFormat.OpenXml.OpenXmlElement Hitem in item.Header.ChildElements)
				{

					if (Hitem.GetType().FullName == "DocumentFormat.OpenXml.Wordprocessing.Paragraph")
					{
						string _innertext = Hitem.InnerText;
						if (!string.IsNullOrEmpty(_innertext))
						{
							MatchCollection matches = Regex.Matches(_innertext, "\\(\\%(.*?)\\%\\)");
							if (matches.Count > 0)
							{
								ProcessParagraph(Hitem, 0, DoRs, _maindocpart);
							}
						}

					}
					else if (Hitem.GetType().FullName == "DocumentFormat.OpenXml.Wordprocessing.Table")
					{
						DocumentFormat.OpenXml.Wordprocessing.Table table = (DocumentFormat.OpenXml.Wordprocessing.Table)Hitem;
						ProcessTable(table, DoRs, _maindocpart);
					}
				}

			}

			//'Body
			foreach (DocumentFormat.OpenXml.OpenXmlElement item in body.ChildElements)
			{

				if (item.GetType().FullName == "DocumentFormat.OpenXml.Wordprocessing.Paragraph")
				{
					ProcessParagraph(item, 0, DoRs, _maindocpart);
				}
				else if (item.GetType().FullName == "DocumentFormat.OpenXml.Wordprocessing.Table")
				{
					DocumentFormat.OpenXml.Wordprocessing.Table table = (DocumentFormat.OpenXml.Wordprocessing.Table)item;
					ProcessTable(table, DoRs, _maindocpart);
				}
			}

			//'Footer
			foreach (var item in wordprocessingDocument.MainDocumentPart.FooterParts)
			{

				foreach (DocumentFormat.OpenXml.OpenXmlElement Fitem in item.Footer.ChildElements)
				{

					if (Fitem.GetType().FullName == "DocumentFormat.OpenXml.Wordprocessing.Paragraph")
					{
						string _innertext = Fitem.InnerText;
						if (!string.IsNullOrEmpty(_innertext))
						{
							MatchCollection matches = Regex.Matches(_innertext, "\\(\\%(.*?)\\%\\)");
							if (matches.Count > 0)
							{
								ProcessParagraph(Fitem, 0, DoRs, _maindocpart);
							}
						}

					}
					else if (Fitem.GetType().FullName == "DocumentFormat.OpenXml.Wordprocessing.Table")
					{
						DocumentFormat.OpenXml.Wordprocessing.Table table = (DocumentFormat.OpenXml.Wordprocessing.Table)Fitem;
						ProcessTable(table, DoRs, _maindocpart);
					}
				}

			}

			if (string.IsNullOrEmpty(sFileDisplayName))
			{
				sFileDisplayName = filename;
			}

			wordprocessingDocument.PackageProperties.Title = sFileDisplayName;

			wordprocessingDocument.Close();

			if (iDocType == 2) //'word format
			{
				return ConvertWordToStream(filename);
			}
			else
			{
				return ConvertWordToPDFStream(filename);
			}

			//' Return ConvertWordToPDFStream(filename)

		}

		private void ProcessTable(Table table, clRowSet DoRs, MainDocumentPart _maindocpart)
		{
			int _rowindex = 0;

			foreach (DocumentFormat.OpenXml.OpenXmlElement row in table.ChildElements)
			{

				if (row.GetType().FullName == "DocumentFormat.OpenXml.Wordprocessing.TableRow")
				{
					ProcessRow(row, _rowindex, DoRs, _maindocpart);
					_rowindex += 1;
				}
			}

			IsRecurringTable = false;
		}

		private void ProcessRow(DocumentFormat.OpenXml.OpenXmlElement row, int iRowId, clRowSet DoRs, MainDocumentPart _maindocpart)
		{
			foreach (DocumentFormat.OpenXml.OpenXmlElement _cell in row.ChildElements)
			{

				if (_cell.LocalName == "tc" && !string.IsNullOrEmpty(_cell.InnerText))
				{
					ProcessCell(_cell, iRowId, DoRs, _maindocpart);
				}
			}
		}
		private void ProcessCell(DocumentFormat.OpenXml.OpenXmlElement _cell, int iRowId, clRowSet DoRs, MainDocumentPart _maindocpart)
		{
			TableCell cell = (TableCell)_cell;

			foreach (DocumentFormat.OpenXml.OpenXmlElement _cellitem in cell.ChildElements)
			{

				if (_cellitem.GetType().FullName == "DocumentFormat.OpenXml.Wordprocessing.Paragraph")
				{
					string _innertext = _cellitem.InnerText;
					if (!string.IsNullOrEmpty(_innertext))
					{
						MatchCollection matches = Regex.Matches(_innertext, "\\(\\%(.*?)\\%\\)");
						if (matches.Count > 0)
						{
							ProcessParagraph(_cellitem, iRowId, DoRs, _maindocpart);
						}
					}
				}
			}
		}

		private void ProcessParagraph(DocumentFormat.OpenXml.OpenXmlElement item, int iRowId, clRowSet DoRs, MainDocumentPart _maindocpart)
		{
			string _innertext = item.InnerText;
			string sFieldName = "";

			if (!string.IsNullOrEmpty(_innertext))
			{
				DocumentFormat.OpenXml.Wordprocessing.Paragraph p = (Paragraph)item;
				MatchCollection matches = Regex.Matches(_innertext, "\\(\\%(.*?)\\%\\)");

				if (matches.Count > 0)
				{

					foreach (System.Text.RegularExpressions.Match _field in matches)
					{
						string _fieldname = _field.ToString();
						string _fieldvalue = "";

						if (_fieldname.Replace("(%", "").Replace("%)", "").StartsWith("LNK_"))
						{

							if (_fieldname.ToUpper().Contains("LNK_CONNECTED_QL") || _fieldname.Contains("LNK_RENTAL_QL") || _fieldname.Contains("LNK_SALES_QL") || _fieldname.Contains("LNK_CONNECTED_II"))
							{

								if (item.Parent.LocalName == "tc" && IsRecurringTable == false)
								{
									IsRecurringTable = true;
									var _currow = item.Parent.Parent;
									int iLinkCount = (int)GetLinkCount(_fieldname, DoRs);

									for (int i = 2; i <= iLinkCount; i++)
									{
										//'CType(item.Parent.Parent.Parent, Table).Append(_currow.CloneNode(True))
										((Table)item.Parent.Parent.Parent).InsertAfter(_currow.CloneNode(true), _currow);
									}

								}
							}

							if (IsRecurringTable)
							{
								_fieldvalue = GetLinkValue(_fieldname, iRowId, DoRs);
							}
							else
							{
								_fieldvalue = GetLinkValue(_fieldname, 1, DoRs);
							}
						}
						else if (_fieldname.ToUpper().Contains("PAGEBREAK"))
						{
							_fieldvalue = "";
						}
						else
						{
							_fieldvalue = GetFieldValue(_fieldname, DoRs);
						}

						ProcessDateFields(ref _innertext, ref _fieldvalue);

						_innertext = _innertext.Replace(_fieldname, _fieldvalue);
						sFieldName = _fieldname;
					}

					//'run here
					if (sFieldName.ToUpper().Contains("MMO") | sFieldName.ToUpper().Contains("MMR"))
					{
						if (ContainsHTML(_innertext))
						{
							UpdateMMR(_innertext, p, _maindocpart, sFieldName, iRowId);
						}
						else
						{
							UpdateValue(_innertext, p);
						}
					}
					else if (sFieldName.ToUpper().Contains("PAGEBREAK"))
					{
						UpdateValue("", p);
						InsertPageBreak(p);
					}
					else
					{
						UpdateValue(_innertext, p);
						//If ContainsHTML(_innertext) Then
						//    UpdateMMR(_innertext, p, _maindocpart, sFieldName, iRowId)
						//Else
						//    UpdateValue(_innertext, p)
						//End If
					}

				}

			}
		}
		private static bool ContainsHTML(string strText)
		{

			if (strText != System.Web.HttpUtility.HtmlEncode(strText))
			{
				return true;
			}

			return false;
		}

		private static void UpdateMMR(string _innertext, DocumentFormat.OpenXml.Wordprocessing.Paragraph p, MainDocumentPart _maindocpart, string sFieldName, int iRowId)
		{

			int i = 0;

			RunProperties rpr = null;

			foreach (Run run in p.Elements<Run>())
			{
				foreach (Text text in run.Elements<Text>())
				{
					text.Text = "";

					if (rpr == null)
					{
						rpr = run.RunProperties;
					}

				}
			}

			string sfontName = string.IsNullOrEmpty(DEF_FONT_NAME) ? "Times New Roman" : DEF_FONT_NAME;
			string fontsize = (DEF_FONT_SIZE == 0 || DEF_FONT_SIZE <= 0) ? "20" : DEF_FONT_SIZE.ToString();

			if (rpr != null && rpr.RunFonts != null)
			{

				if (rpr.RunFonts.Ascii == null)
				{
					sfontName = DEF_FONT_NAME;
				}
				else
				{
					sfontName = rpr.RunFonts.Ascii.Value;
				}
				if (rpr.FontSize != null)
				{
					fontsize = rpr.FontSize.Val.Value;
				}

			}

			int ifontsize = 0;

			int.TryParse(fontsize, out ifontsize);

			ifontsize = Convert.ToInt32((ifontsize / 2.0));

			_innertext = _innertext.Replace("<p>", "").Replace("</p>", "<br>");

			//'remove the last <br>
			if (_innertext.EndsWith("<br>"))
			{
				_innertext = _innertext.Substring(0, _innertext.Length - 4);
			}

			_innertext = "<html><head></head><body style=\"font-size:" + ifontsize.ToString() + "pt;font-family: " + sfontName + ";\">" + _innertext + "</body></html>";

			string AltChunkId = sFieldName.Replace("%", "").Replace("(", "").Replace(")", "").Replace("_", "") + iRowId.ToString();
			MemoryStream ms = new MemoryStream(Encoding.UTF8.GetBytes(_innertext));
			AlternativeFormatImportPart formatImportPart = _maindocpart.AddAlternativeFormatImportPart(AlternativeFormatImportPartType.Html, AltChunkId);
			formatImportPart.FeedData(ms);
			AltChunk altChunk = new AltChunk();
			altChunk.Id = AltChunkId;

			p.Parent.InsertBefore(altChunk, p);

			//For Each run As Run In p.Elements(Of Run)()
			//    For Each text As Text In run.Elements(Of Text)()
			//        run.RunProperties = rpr.Clone()
			//    Next
			//Next

		}

		private static void ProcessDateFields(ref string _innertext, ref string _fieldvalue)
		{
			if (_innertext.Contains("<SHORTDATE>"))
			{
				_innertext = _innertext.Replace("<SHORTDATE>", "");
				_innertext = _innertext.Replace("</SHORTDATE>", "");
				_fieldvalue = DateTime.Parse(_fieldvalue).ToString("");
			}
			else if (_innertext.Contains("<LONGDATE>"))
			{
				_innertext = _innertext.Replace("<LONGDATE>", "");
				_innertext = _innertext.Replace("</LONGDATE>", "");
				_fieldvalue = DateTime.Parse(_fieldvalue).ToString("");
			}
		}

		private void UpdateValue(string _innertext, DocumentFormat.OpenXml.Wordprocessing.Paragraph p)
		{
			int i = 0;

			foreach (Run run in p.Elements<Run>())
			{

				foreach (Text text in run.Elements<Text>())
				{

					if (i == 0)
					{
						text.Text = _innertext;
					}
					else
					{
						text.Text = "";
					}

					i += 1;
				}
			}
		}

		private void InsertPageBreak(DocumentFormat.OpenXml.Wordprocessing.Paragraph p)
		{
			var _pb = new Paragraph(new Run(new Break() {Type = BreakValues.Page}));
			p.Parent.InsertBefore(_pb, p);
		}

		public long GetLinkCount(string strCode, clRowSet DoRs)
		{

			strCode = GetSQLFieldName(strCode);
			//'[Returns the number of linked records for the given code]

			if (strCode == "LNK_XX_XX")
			{
				return 0;
			}

			GetLinkName(ref strCode);

			clArray doLink = new clArray();
			DataTable oTable = null;

			if (DoRs.GetFileName() == "QT")
			{
				doLink = DoRs.GetLinkVal(GetSQLFieldName(strCode + "%%CHK_INCLUDE"), ref doLink, true, 0, -1, "A_a", ref oTable);
			}
			else
			{
				doLink = DoRs.GetLinkVal(GetSQLFieldName(strCode + "%%GID_ID"), ref doLink, true, 0, -1, "A_a", ref oTable);
			}

			int _iLinkcount = 0;

			if (DoRs.GetFileName() == "QT")
			{
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of doLink.GetDimension() for every iteration:
				int tempVar = (int)doLink.GetDimension();
				for (var index = 1; index <= tempVar; index++)
				{
					if (doLink.GetItem(index).ToString() == "Checked")
					{
						_iLinkcount = Convert.ToInt32(_iLinkcount + 1);
					}
				}
			}
			else
			{
				_iLinkcount = Convert.ToInt32((int)doLink.GetDimension());
			}

			return _iLinkcount;

		}

		private void GetLinkName(ref string strCode)
		{
			string[] strSplit = null;
			int iDQ = 0;
			if (strCode.Substring(0, 2) == "(%")
			{
				strCode = strCode.Substring(2, (strCode.Length - 4));
			}

			iDQ = Convert.ToInt32(strCode.IndexOf("%%") + 1);
			if (iDQ != 0)
			{
				strSplit = Microsoft.VisualBasic.Strings.Split(strCode, "%%");
				strCode = strSplit[0];
			}
		}

		private string GetFieldValue(string FieldName, clRowSet DoRs)
		{

			string sValue = DoRs.GetFieldVal(GetSQLFieldName(FieldName)).ToString();
			sValue = FormatFieldValue(FieldName, sValue);
			return sValue;

		}

		private string FormatFieldValue(string FieldName, string sValue)
		{
			//'for CUR_ fields , print NULL when it is zero
			if (FieldName.ToUpper().Contains("CUR_") == true && bHideZeroCurValue == 1)
			{
				if (sValue == "0" || sValue == "0.00" || sValue == "0.0" || sValue == "$0.00")
				{
					sValue = "";
				}
				//ElseIf (FieldName.ToUpper().Contains("MMO_")) Then
				//    Dim goUt As New clUtil()
				//    sValue = goUt.StripHTML(sValue)
				//'iCHKPrintType
			}
			else if (FieldName.ToUpper().Contains("CHK_"))
			{

				if (iCHKPrintType == 2)
				{
					if (sValue == "Checked")
					{
						sValue = "Yes";
					}
					else
					{
						sValue = "No";
					}
				}
				else if (iCHKPrintType == 3)
				{
					if (sValue == "Checked")
					{
						sValue = "True";
					}
					else
					{
						sValue = "False";
					}
				}

			}

			return sValue;
		}

		private Hashtable HTLinks = new Hashtable();

		private string GetLinkValue(string LNKFieldName, int RowIndex, clRowSet DoRs)
		{
			clArray doLink = new clArray();
			DataTable oTable = null;
			string sValue = "";

			if (LNKFieldName.ToUpper().Contains("LNK_CONNECTED_QL") || LNKFieldName.ToUpper().Contains("LNK_RENTAL_QL") || LNKFieldName.ToUpper().Contains("LNK_SALES_QL"))
			{

				//'loop all items
				//'add all included items to another array
				//'get the item based on row index from new array
				string strcode = GetSQLFieldName(LNKFieldName);
				GetLinkName(ref strcode);

				clArray doLinkIncludedItems = new clArray();

				if (RowIndex == 1)
				{

					clArray doLinktmp = new clArray();
					DataTable oTabletmp = null;

					doLinktmp = DoRs.GetLinkVal(GetSQLFieldName(strcode + "%%CHK_INCLUDE"), ref doLinktmp, true, 0, -1, "A_a", ref oTabletmp);
					doLink = DoRs.GetLinkVal(GetSQLFieldName(LNKFieldName), ref doLink, true, 0, -1, "A_a", ref oTable);

// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of doLinktmp.GetDimension() for every iteration:
					int tempVar = (int)doLinktmp.GetDimension();
					for (var index = 1; index <= tempVar; index++)
					{
						if (doLinktmp.GetItem(index).ToString() == "Checked")
						{
							doLinkIncludedItems.Add(doLink.GetItem(index));
						}
					}

					if (!HTLinks.ContainsKey(LNKFieldName))
					{
						HTLinks.Add(LNKFieldName, doLinkIncludedItems);
					}

				}
				else
				{
					doLinkIncludedItems = (Selltis.BusinessLogic.clArray)HTLinks[LNKFieldName];
				}

				sValue = doLinkIncludedItems.GetItem(RowIndex).ToString();

				//Dim doLinkIncludedItems As clArray = New clArray()
				//Dim doLinktmp As clArray = New clArray()
				//Dim oTabletmp As DataTable = Nothing

				//doLinktmp = DoRs.GetLinkVal(GetSQLFieldName(strcode + "%%CHK_INCLUDE"), doLinktmp, True, 0, -1, "A_a", oTabletmp)
				//doLink = DoRs.GetLinkVal(GetSQLFieldName(LNKFieldName), doLink, True, 0, -1, "A_a", oTable)

				//For index = 1 To doLinktmp.GetDimension()
				//    If doLinktmp.GetItem(index).ToString() = "Checked" Then
				//        doLinkIncludedItems.Add(doLink.GetItem(index))
				//    End If
				//Next

				//sValue = doLinkIncludedItems.GetItem(RowIndex).ToString()

			}
			else
			{
				doLink = DoRs.GetLinkVal(GetSQLFieldName(LNKFieldName), ref doLink, true, 0, -1, "A_a", ref oTable);
				sValue = doLink.GetItem(RowIndex).ToString();
			}

			//'for CUR_ fields , print NULL when it is zero
			if (LNKFieldName.ToUpper().Contains("CUR_"))
			{
				if (sValue == "0" || sValue == "0.00" || sValue == "0.0" || sValue == "$0.00")
				{
					sValue = "";
				}
			}

			return sValue;

		}

		private string GetSQLFieldName(string sFieldName)
		{
			return sFieldName.Replace("(%", "").Replace("%)", "");
		}

		private void ConvertWordToPDF(string docFileName)
		{
			string outFile = docFileName.ToLower().Replace(".docx", ".pdf");
			SautinSoft.Document.DocumentCore dc = SautinSoft.Document.DocumentCore.Load(docFileName);

			dc.Save(outFile);
		}

		public MemoryStream ConvertWordToPDFStream(string docFileName)
		{
			byte[] inpData = File.ReadAllBytes(docFileName);
			byte[] outData = null;
			using (MemoryStream msInp = new MemoryStream(inpData))
			{

				SautinSoft.Document.DocumentCore.Serial = "50017096491";

				// Load a document.
				SautinSoft.Document.DocumentCore dc = SautinSoft.Document.DocumentCore.Load(msInp, new SautinSoft.Document.DocxLoadOptions());

				// Save the document to PDF format.
				using (MemoryStream outMs = new MemoryStream())
				{
					dc.Save(outMs, new SautinSoft.Document.PdfSaveOptions());
					outData = outMs.ToArray();
				}

				MemoryStream stream = new MemoryStream(outData);
				return stream;

			}

		}

		public MemoryStream ConvertWordToStream(string docFileName)
		{
			byte[] inpData = File.ReadAllBytes(docFileName);
			byte[] outData = null;
			using (MemoryStream msInp = new MemoryStream(inpData))
			{

				SautinSoft.Document.DocumentCore.Serial = "50017096491";
				// Load a document.
				SautinSoft.Document.DocumentCore dc = SautinSoft.Document.DocumentCore.Load(msInp, new SautinSoft.Document.DocxLoadOptions());

				// Save the document to PDF format.
				using (MemoryStream outMs = new MemoryStream())
				{
					dc.Save(outMs, new SautinSoft.Document.DocxSaveOptions());
					outData = outMs.ToArray();
				}

				MemoryStream stream = new MemoryStream(outData);
				return stream;

			}

		}

		private MemoryStream ConvertWordToPDFStream2(string docFileName)
		{

			IFormatProvider<Telerik.Windows.Documents.Flow.Model.RadFlowDocument> fileFormatProvider = null;
			Telerik.Windows.Documents.Flow.Model.RadFlowDocument document = null;
			string fileDownloadName = "{0}.{1}";
			string convertTo = "pdf";

			fileFormatProvider = new DocxFormatProvider();

			byte[] inpData = File.ReadAllBytes(docFileName);
			MemoryStream msInp = new MemoryStream(inpData);

			document = fileFormatProvider.Import(msInp);
			//'fileDownloadName = String.Format(fileDownloadName, Path.GetFileNameWithoutExtension(docFileName), convertTo)

			PdfFormatProvider convertFormatProvider = new PdfFormatProvider();
			string mimeType = "application/pdf";
			byte[] outData = null;

			using (MemoryStream Ms = new MemoryStream())
			{
				convertFormatProvider.Export(document, Ms);
				outData = Ms.ToArray();
			}

			MemoryStream stream = new MemoryStream(outData);
			return stream;

		}

	}



}
