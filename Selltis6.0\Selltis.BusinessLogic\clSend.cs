﻿using System;
using System.Data;
using System.Web;

namespace Selltis.BusinessLogic
{
	public class clSend
	{

		//OWNER: RH as of 2/8/2012
		//MI 10/29/10 Added support for Above Sig, Under Signature.

		private clProject goP;
		private clTransform goTR;
		private clMetaData goMeta;
		private clData goData;
		private clError goErr;
		private clLog goLog;
		private clDefaults goDef;
		private clScrMngRowSet goScr;
		private SDI INI;


		public struct MailObject
		{

			public string sTo;
			public string sCC;
			public string sBCC;
			public string sSubject;
			public string sBody;
			public string sFiles;
			public string sAboveSig;
			public string sUnderSig;

		}
		public struct SDI
		{
			public string Address;
			public int BroadcastCount;
			public string BroadcastLink;
			public string BroadcastIDs;
			public string DB;
			public string File;
			public string ID;
			public bool Individualized;
			public string InstanceCode;
			public bool IsSingle;
			public string Mode;
			public string ModeExt;
			public string Password;
			public bool Preview;
			public bool PrintLetterHead;
			public string TempDir;
			public string Template;
			public string TemplType;
			public string User;
			public string SendLink;
			public string ObjectType;
			public string Share;
			public string ViewName;
			public string DesktopName;
			public string SubjectField;
			public string QuoteNumber;

		}

		//*******************************************************************************
		//INITIALIZE
		public clSend()
		{
			Initialize();
		}
		public void Initialize()
		{

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			goDef = (Selltis.BusinessLogic.clDefaults)HttpContext.Current.Session["goDef"];
			goScr = (Selltis.BusinessLogic.clScrMngRowSet)HttpContext.Current.Session["goScr"];

		}
		//*******************************************************************************
		//SMTP EMail
		public bool SMTPTrigger(string par_sINI)
		{
			//PURPOSE:
			//       Send a correspondence email via our SMTP server. This method reads
			//       data from an AC or QT record. To send a generic e-mail via the server,
			//       use clEmail.SendSMTPEmail or SendSMTPEmailHTMLWithEmbeddedImages.

			//MI 2/1/12 Added note on purpose above.
			//MI 5/14/09 Changed SYS_Name to TXT_NameFirst & " " & TXT_NameLast
			//MI 5/13/09 SMTPTrigger: added redirecting to Mobile_diaSendPreview.aspx if product is Mobile.

			string sProc = "clSend::SMTPTrigger";
			string sReplyName = null;
			string sReplyAddress = null;
			string sBody = null;

			//Try

			MailObject oMailItem = new MailObject();
				string sMIID = goTR.DateToInteger(goTR.NowUTC()).ToString(); //*** MI 10/1/07


				if (goTR.StrRead(par_sINI, "OBJECTTOSEND") == "CORR")
				{
					//Correspondence sending

					clRowSet oCorrRS = new clRowSet(goTR.StrRead(par_sINI, "FILE"), clC.SELL_EDIT, "GID_ID='" + goTR.StrRead(par_sINI, "ID") + "'");
					if (oCorrRS.Count() < 1)
					{
						goErr.SetWarning(35000, "The record doesn't exist and can't be e-mailed via SMTP server. It may have been deleted by another user. If the problem persists, please contact your Selltis administrator. GID_ID: '" + goTR.StrRead(par_sINI, "ID") + "', file: '" + goTR.StrRead(par_sINI, "FILE") + "'.");
						return false;
					}

					if (goTR.StrRead(par_sINI, "FILE") == "AC")
					{
						oMailItem.sSubject = Convert.ToString(oCorrRS.GetFieldVal("TXT_SUBJ", clC.SELL_FRIENDLY));
						oMailItem.sBody = Convert.ToString(oCorrRS.GetFieldVal("MMO_LETTER", clC.SELL_FRIENDLY));
						oMailItem.sAboveSig = Convert.ToString(oCorrRS.GetFieldVal("TXT_ABOVESIGNATURE", clC.SELL_FRIENDLY));
						oMailItem.sUnderSig = Convert.ToString(oCorrRS.GetFieldVal("MMO_UNDERSIGNATURE", clC.SELL_FRIENDLY));
					}
					else if (goTR.StrRead(par_sINI, "FILE") == "QT")
					{
						oMailItem.sSubject = Convert.ToString(oCorrRS.GetFieldVal("TXT_DESCRIPTION", clC.SELL_FRIENDLY));
						oMailItem.sBody = Convert.ToString(oCorrRS.GetFieldVal("MMO_COVERLETTER", clC.SELL_FRIENDLY));
					}

					oMailItem.sTo = GetTO(ref oCorrRS);
					oMailItem.sCC = GetCC(ref oCorrRS);
					oMailItem.sBCC = GetBCC(ref oCorrRS);
					oMailItem.sFiles = "(none)";

				}
				else
				{
					//Record details sending, view sending?

				}

				if (bool.Parse(goTR.StrRead(par_sINI, "PREVIEW")) == true)
				{
					goP.SetVar(sMIID, oMailItem);
					goP.SetVar(sMIID + "_sSendINI", par_sINI);
					//Open the preview page
					if (goP.GetProduct() == "MB")
					{
						HttpContext.Current.Response.Redirect("Mobile_DiaEmailPreview.aspx?" + sMIID);
					}
					else
					{
						//SA
						HttpContext.Current.Response.Redirect("diaEmailPreview.aspx?" + sMIID);
					}

				}
				else
				{

					//Send
					clEmail oEMail = new clEmail();
					//MI 5/14/09 Changed SYS_Name to TXT_NameLast,TXT_NameFirst
					clRowSet rs = new clRowSet("US", 3, "GID_ID='" + goP.GetUserTID() + "'", "", "TXT_NameLast,TXT_NameFirst,EML_email");
					if (rs.Count() < 1)
					{
						//Record may have been deleted - do not raise error so the calling page can display the following 'friendly' error
						goErr.SetWarning(35000, sProc, "Sending via SMTP failed because current user's User record can't be found. GID_ID: '" + goP.GetUserTID() + "'.");
						return false;
					}
					//MI 5/14/09 Changed SYS_Name to TXT_NameFirst & " " & TXT_NameLast
					sReplyName = rs.GetFieldVal("TXT_NameFirst").ToString() + " " + rs.GetFieldVal("TXT_NameLast").ToString();
					sReplyAddress = Convert.ToString(rs.GetFieldVal("EML_EMail"));

					//MI 10/29/10 Added support for Above/Under Sig
					sBody = oMailItem.sBody;
					if (!(oMailItem.sAboveSig.ToString() == ""))
					{
						sBody += "\r\n" + "\r\n" + oMailItem.sAboveSig.ToString();
					}
					if (!(oMailItem.sUnderSig.ToString() == ""))
					{
						sBody += "\r\n" + "\r\n" + oMailItem.sUnderSig.ToString();
					}

					if (oEMail.SendSMTPEmailNew(oMailItem.sSubject, sBody, oMailItem.sTo, oMailItem.sCC, oMailItem.sBCC, "", sReplyName, sReplyAddress, ""))
					{
						//Mark the record as sent
						MarkRecordAsSent(goTR.StrRead(par_sINI, "ID", null, false), goTR.StrRead(par_sINI, "OBJECTTOSEND", null, false));
						return true;

					}
					else
					{
						//Warning is set by SendSMTPEmail
						return false;

					}

				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}
		public string GetTO(ref clRowSet oRS)
		{

			string sTempStr = "";

			if (oRS.GetFileName() == "AC")
			{
				if (Convert.ToString(oRS.GetFieldVal("EML_EMAIL", clC.SELL_FRIENDLY)) == "")
				{
					sTempStr = Convert.ToString(oRS.GetFieldVal("LNK_RELATED_CN%%EML_EMAIL", clC.SELL_FRIENDLY));
				}
				else
				{
					sTempStr = Convert.ToString(oRS.GetFieldVal("EML_EMAIL", clC.SELL_FRIENDLY));
				}

			}
			else if (oRS.GetFileName() == "QT")
			{
				if (Convert.ToString(oRS.GetFieldVal("EML_EMAIL", clC.SELL_FRIENDLY)) == "")
				{
					sTempStr = Convert.ToString(oRS.GetFieldVal("LNK_TO_CN%%EML_EMAIL", clC.SELL_FRIENDLY));
				}
				else
				{
					sTempStr = Convert.ToString(oRS.GetFieldVal("EML_EMAIL", clC.SELL_FRIENDLY));
				}

			}

			sTempStr = goTR.Replace(sTempStr, "\r\n", "; ");

			return sTempStr;

		}
		public string GetCC(ref clRowSet oRS)
		{
			string sproc = "clSend::GetCC";

			string sTempStr = "";

			sTempStr = Convert.ToString(oRS.GetFieldVal("LNK_CC_CN%%EML_EMAIL", clC.SELL_FRIENDLY));
			sTempStr = goTR.Replace(sTempStr, "\r\n", "; ");

			return sTempStr;

		}
		public string GetBCC(ref clRowSet oRS)
		{
			string sproc = "clSend::GetBCC";

			string sTempStr = "";

			sTempStr = Convert.ToString(oRS.GetFieldVal("LNK_BC_CN%%EML_EMAIL", clC.SELL_FRIENDLY));
			sTempStr = goTR.Replace(sTempStr, "\r\n", "; ");

			return sTempStr;

		}
		//*******************************************************************************
		//TEMPLATE PROCESSING
		public void InitializeProcesses(string sINI)
		{

			string sTemplateName = "";

			//[Load INI data]
			INI.User = goTR.StrRead(sINI, "USER", "");
			INI.File = goTR.StrRead(sINI, "FILE", "");
			INI.Share = goTR.StrRead(sINI, "SHARE", "");
			INI.ViewName = goTR.StrRead(sINI, "ViewName", "Selltis View");
			INI.DesktopName = goTR.StrRead(sINI, "DesktopName", "Selltis Desktop");
			INI.TempDir = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location) + "\\Temp\\";
			//xxx validate
			INI.ID = goTR.StrRead(sINI, "ID", "");
			//xxx validate
			INI.Template = goTR.StrRead(sINI, "TEMPLATEPATH", "");
			sTemplateName = INI.Template;
			INI.Template = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location) + "\\SendTemp\\" + INI.Template;

			//xxx validate
			INI.Individualized = bool.Parse(goTR.StrRead(sINI, "INDIVIDUALIZEDEMAIL", ""));
			INI.Preview = bool.Parse(goTR.StrRead(sINI, "PREVIEW", ""));
			INI.Address = goTR.StrRead(sINI, "ADDRESS", "");
			INI.ObjectType = goTR.StrRead(sINI, "OBJECTTOSEND", "CORR");
			INI.TemplType = InitTemplType();
			INI.InstanceCode = GetTimeString();


			//xxx validate
			INI.Mode = goTR.StrRead(sINI, "MODE", "");
			INI.ModeExt = goTR.StrRead(sINI, "MODEEXE", "");


			if (INI.File.ToUpper() == "AC")
			{
				INI.BroadcastLink = "LNK_RELATED_CN";
				INI.SendLink = "LNK_RELATED_CN";
			}
			else if (INI.File.ToUpper() == "QT")
			{
				INI.BroadcastLink = "LNK_TO_CN";
				INI.SendLink = "LNK_TO_CN";
			}


			//xxx add loading of ids from text files



		}

		public string InitTemplType()
		{
				string tempInitTemplType = null;

			if (INI.Template.Length < 4)
			{
				INI.Template = "unk";
			}


			switch (INI.Template.Substring(INI.Template.Length - 3).ToLower())
			{
				case "txt":

					tempInitTemplType = "Text";

					if (INI.Individualized == true)
					{
						INI.IsSingle = true;
					}
					else
					{
						INI.IsSingle = false;
					}
					break;

				case "htm":
				case "tml":

					tempInitTemplType = "HTML";

					if (INI.BroadcastCount > 1)
					{
						INI.IsSingle = false;
					}
					else
					{
						INI.IsSingle = true;
					}
					break;

				case "doc":
				case "dot":

					tempInitTemplType = "Word";

					if (INI.Mode == "EMAIL")
					{
						if (INI.Individualized == true)
						{
							INI.IsSingle = false;
						}
						else
						{
							INI.IsSingle = true;
						}
					}
					else
					{
						if (INI.BroadcastCount > 1)
						{
							INI.IsSingle = false;
						}
						else
						{
							INI.IsSingle = true;
						}
					}
					break;

				default:

					tempInitTemplType = "Unk";

					if (INI.Individualized == true)
					{
						INI.IsSingle = true;
					}
					else
					{
						INI.IsSingle = false;
					}
					break;



			}



			return tempInitTemplType;
		}
		public string GetTimeString()
		{

			DateTime sTime = goTR.NowUTC(); //*** MI 10/1/07
			//GetLocalTime(sTime)

			return sTime.Year + PadLeadingZeros(sTime.Month.ToString(), 2) + PadLeadingZeros(sTime.Day.ToString(), 2) + PadLeadingZeros(sTime.Hour.ToString(), 2) + PadLeadingZeros(sTime.Minute.ToString(), 2) + PadLeadingZeros(sTime.Second.ToString(), 2) + PadLeadingZeros(sTime.Millisecond.ToString(), 3).Substring(0, 2);

		}

		public string PadLeadingZeros(string strForm, int intLen)
		{
				string tempPadLeadingZeros = null;

			if (strForm.Length < intLen)
			{
				while (strForm.Length != intLen)
				{
					strForm = "0" + strForm;
				}
				tempPadLeadingZeros = strForm;
			}
			else
			{
				tempPadLeadingZeros = strForm;
			}

			return tempPadLeadingZeros;
		}


		//*******************************************************************************
		//PC LINK INTERFACE
		public DataTable GetSendJobs()
		{

			string sproc = "clSend::GetSendJobs";

			DataTable dtSend = new DataTable();
			//Dim i As Integer
			string sSendPage = "";
			string sLog = "";

			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();

			System.Data.SqlClient.SqlDataReader reader = null;
			Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();
			//Dim dtNow As DateTime = goTr.NowServer()    '*** MI 10/1/07
			//Dim bFired As Boolean

			//dtNow = dtNow.AddMilliseconds(-dtNow.Millisecond)    '*** MI 10/1/07
			//dtNow = dtNow.AddSeconds(-dtNow.Second)    '*** MI 10/1/07

			//Try

			cmd.CommandText = "pGetSendJobs";
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				System.Data.SqlClient.SqlParameter uResult = new System.Data.SqlClient.SqlParameter("@par_bDetails", SqlDbType.Bit);
				uResult.Value = 1;
				cmd.Parameters.Add(uResult);

				//uResult = New SqlClient.SqlParameter("@par_sPageID", SqlDbType.NVarChar, 40)
				//uResult.Value = goP.GetUserTID
				//cmd.Parameters.Add(uResult)

				reader = cmd.ExecuteReader();

				if (reader.HasRows)
				{
					dtSend.Load(reader);
				}
				else
				{
					//==> raise error
				}

				reader.Close();
				sqlConnection1.Close();

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sproc)
			//    End If
			//End Try

			//If dtSend.Rows.Count > 0 Then

			//sLog = "Server time: " & dtNow.ToString & "[cr]"
			//For i = 0 To dtAge.Rows.Count - 1
			//    sAgePage = goMeta.PageRead("GLOBAL", dtAge.Rows(i).Item("Page").ToString)

			//    If goTR.StrRead(sAgePage, "ACTIVE") = "1" Then
			//        If TestTimerTrigger(dtAge.Rows(i).Item("Page").ToString, sAgePage, dtNow) = True Then
			//            '==> Execute automator
			//            goMeta.LineWrite("GLOBAL", "PTA_" & goTR.FromTo(dtAge.Rows(i).Item("Page").ToString, 5), "LASTDATE", goTR.DateTimeToString(dtNow))
			//            '==> Compile and write log

			//            If goScr.RunScript(goTR.StrRead(sAgePage, "A_01_EXECUTE")) = False Then
			//                sLog = sLog & "[cr]" & goTR.StrRead(sAgePage, "US_NAME") & "  [False]"
			//            Else
			//                sLog = sLog & "[cr]" & goTR.StrRead(sAgePage, "US_NAME") & "  [True]"
			//            End If

			//            bFired = True

			//        End If
			//    End If
			//Next
			//If bFired = True Then
			//    Return sLog
			//Else
			//    Return "None triggered"
			//End If

			//End If

			return dtSend;



		}
		public int GetSendJobCount()
		{







			string sproc = "clSend::GetSendJobCount";
			string sSendPage = "";
			string sLog = "";
			int iVal = 0;

			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();

			//Try

			cmd.CommandText = "pGetSendJobCount";
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				iVal = Convert.ToInt32(cmd.ExecuteScalar());

				sqlConnection1.Close();
				return iVal;

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sproc)
			//    End If
			//End Try

			return 0;

		}

		public int GetSendJobCountVer(string sVerLocal)
		{

			bool bOldPCLink = false;
			try
			{
				int ia1 = 0;
				int ia2 = 0;
				int ia3 = 0;

				ia1 = int.Parse(goTR.ExtractString(sVerLocal, 1, "."));
				ia2 = int.Parse(goTR.ExtractString(sVerLocal, 2, "."));
				ia3 = int.Parse(goTR.ExtractString(sVerLocal, 3, "."));

				if (ia1 == 2 && ia2 == 0 && ia3 <= 21)
				{
					bOldPCLink = true;
				}
				else
				{
					bOldPCLink = false;
				}

			}
			catch (Exception ex)
			{
				bOldPCLink = false;
			}

			string sproc = "clSend::GetSendJobCount";
			string sSendPage = "";
			string sLog = "";
			int iVal = 0;


			if (bOldPCLink == false && goP.TestVersion(goP.GetVersion("PCLink"), sVerLocal) == "-")
			{
				return -1;
			}


			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();

			//Try

			cmd.CommandText = "pGetSendJobCount";
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				iVal = Convert.ToInt32(cmd.ExecuteScalar());

				sqlConnection1.Close();
				return iVal;

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sproc)
			//    End If
			//End Try

			return 0;

		}

		public DateTime GetFileVersionDate(string FName, string sPath)
		{


			string lpath = "";

			try
			{

				if (sPath.ToLower() == "temp")
				{
					lpath = goP.sPath + "\\Temp\\";
				}
				else if (sPath.ToLower() == "templates")
				{
					lpath = goP.sPath + "\\Templates\\";
				}
				else if (sPath.ToLower() == "downloads")
				{
					lpath = goP.sPath + "\\Downloads\\";
				}
				else
				{
					lpath = sPath;
				}
				lpath = goTR.Replace(lpath, "\\\\", "\\");

				System.IO.FileInfo fi = new System.IO.FileInfo(lpath + FName);

				return fi.LastWriteTimeUtc;

			}
			catch (Exception ex)
			{

				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, lpath, , lpath)
				//End If

				return goTR.NowServer();

			}

		}
		public string AddSendJob(string par_sSendName, string par_sIDOfObjectToSend, string par_sTemplatePath, string par_sObjectToSend = "CORR", string par_sMode = "EMAIL", string par_sFile = "", bool par_bIndividualizedEmail = false, bool par_bPreview = true, string par_sTo = "", string par_sCc = "", string par_sBc = "")
		{
			//MI 1/3/08 Added case "FILE" to par_sMode.
			//MI 1/16/07 Created.
			//PURPOSE:
			//       Create SNQ_ (send queue) metadata page for processing by
			//       a client sending utility.
			//PARAMETERS:
			//       par_sSendName: Descriptive 'Name' of the send job in default language.
			//           This name will be displayed in the client utility that allows managing
			//           queued jobs. Suggested format is:
			//           Case "CORR"
			//                sSendName = goDef.GetSendFormatLabel(sSendType) & " " & goData.GetFileLabelFromName(sFileName) & " '" & sSendName & "'"
			//           Case "RECORD"
			//             sSendName = "Record details for " & goData.GetFileLabelFromName(sFileName) & " '" & sSendName & "'"
			//           Case "VIEW"
			//                sSendName = "View '" & sSendName & "'"
			//       par_sIDOfObjectToSend: ID as string of the object to send. This can be
			//           a record ID of the record or ID of the view.
			//       par_sTemplatePath: filename of the send template (defined in SND_ metadata). In some
			//           cases may be optional. See clSend for details.
			//       par_sObjectToSend: "CORR" for correspondence (AC and QT), "VIEW", or "RECORD" for record details.
			//       par_sMode: how to send: "EMAIL", "FAX", "LETTER", "FILE". Use "FILE" when sending from
			//           non-correspondence files. "LETTER" may not be supported even for CORR object?
			//       par_sFile: Filename of the view to send. To retrieve a filename from VIE_ MD,
			//           use goTr.StrRead(sViewMDPage, "FILE", ""). Optional for record and correspondence
			//           sending.
			//       par_bIndividualizedEmail: if True, a correspondence template will be processed in the
			//           "individualized" mode: an e-mail will be generated for each recipient, a letter
			//           will be generated with a section for each recipient, etc. Default: false.
			//       par_bPreview: If True, the e-mail, fax, or letter will be displayed before it is sent.
			//           Default: True
			//       par_sTo: 'ADDRESS' property: the 'To' e-mails, address, or fax numbers. If provided, this information
			//           overrides information from the correspondence record. In the case of view or record
			//           details sending, this information is mandatory unless the user is expected to enter
			//           this info in the preview window.
			//       par_sCc: 'CC' property (not supported yet).
			//       par_sBc: 'BC' property (not supported yet).
			//RETURNS: Page ID of the SNQ_ page created or "" if an error occurred.

			string sProc = "clSend::AddSendJob";
			string sPageID = null;
			string sFile = par_sFile;
			string sVals = "";

			//Validate or retrieve filename
			if (sFile == "")
			{
				sFile = goTR.GetFileFromSUID(par_sIDOfObjectToSend);
			}
			if (!goData.IsFileValid(sFile))
			{
				goErr.SetError(10100, sProc, "", sFile);
				//10100: Invalid file name '[1]'. [2]
				return "";
			}

			//validate object to send
			switch (par_sObjectToSend.ToUpper())
			{
				case "CORR":
				case "VIEW":
				case "RECORD":
				case "REQUEST":
				break;
					//Pass
				default:
					goErr.SetError(10103, sProc, "", "par_sObjectToSend", sProc, par_sObjectToSend);
					break;
					//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
			}

			//Validate sending mode
			switch (par_sMode.ToUpper())
			{
				case "EMAIL":
				case "FAX":
				case "LETTER":
				case "FILE":
				case "S3UPLOAD":
				case "S3DOWNLOAD":
				case "EXCELUPLOAD":
				case "MEETINGREQUEST":
				break;
					//Pass
				default:
					goErr.SetError(10103, sProc, "", "par_sMode", sProc, par_sMode);
					break;
					//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
			}

			//Concat the string
			goTR.StrWrite(ref sVals, "US_NAME", goDef.GetMetaTypeFromPrefix("SNQ_"));
			goTR.StrWrite(ref sVals, "MODE", par_sMode);
			goTR.StrWrite(ref sVals, "FILE", sFile);
			goTR.StrWrite(ref sVals, "ID", par_sIDOfObjectToSend);
			goTR.StrWrite(ref sVals, "TEMPLATEPATH", par_sTemplatePath);
			goTR.StrWrite(ref sVals, "INDIVIDUALIZEDEMAIL", goTR.CheckBoxToText(par_bIndividualizedEmail ? -1 : 0, true));
			goTR.StrWrite(ref sVals, "PREVIEW", goTR.CheckBoxToText(par_bPreview ? -1 : 0, true));
			if (par_sTo != "") //To be chaged to "TO"
			{
				goTR.StrWrite(ref sVals, "ADDRESS", par_sTo);
			}
			if (par_sCc != "")
			{
				goTR.StrWrite(ref sVals, "CC", par_sCc);
			}
			if (par_sBc != "")
			{
				goTR.StrWrite(ref sVals, "BC", par_sBc);
			}
			goTR.StrWrite(ref sVals, "OBJECTTOSEND", par_sObjectToSend); //VIEW, RECORD, or CORR
			goTR.StrWrite(ref sVals, "SENDNAME", par_sSendName);

			sPageID = "SNQ_" + goData.GenSUID("XX");
			if (!goMeta.PageWrite(goP.GetUserTID(), sPageID, sVals))
			{
				sPageID = "";
			}

			return sPageID;

		}
		//*******************************************************************************
		//Public Function StartEmailLogging() As Boolean
		//    'AUTHOR: MI 3/6/07
		//    'PURPOSE:
		//    '       Create a send job (SNQ_ MD page) to start logging e-mails
		//    '       This is for initiating logging emails from the browser.
		//    'RETURNS:
		//    '       True or False if unsuccessful.

		//    Dim sProc As String = "clSend::StartEmailLogging"
		//    Dim sPageID As String
		//    Dim sIni As String = ""

		//    goTR.StrWrite(sIni, "MODE", "LOGEMAIL")
		//    goTR.StrWrite(sIni, "US_NAME", "LOGEMAIL")
		//    goTR.StrWrite(sIni, "SENDNAME", "Start logging e-mail")

		//    sPageID = "SNQ_" & goData.GenSUID("XX")
		//    If Not goMeta.PageWrite(goP.GetUserTID(), sPageID, sIni) Then
		//        goErr.SetError(35000, sProc, "Error writing SNQ_ metadata to initiate logging e-mail.")
		//        Return False
		//    End If

		//    Return True

		//End Function

		public bool CreateOutlookMeetingRequest(string sSubject, string sBody, DateTime dtStart, DateTime dtEnd, string sRequiredAttendees, string sOptionalAttendees, bool bReminderSet = false, int iReminderMinutesBeforeStart = 5, string sLocation = "", bool bIsOnlineMeeting = false, bool bAllDayEvent = false, string sAttachmentFile = "")
		{

			//AUTHOR: RH 2/9/2012
			//PURPOSE:
			//       Create a TMP md page to hold details of Outlook Meeting Request
			//       Create a send job (SNQ_ MD page) to create Outlook Meeting Request
			//PARAMETERS:
			//       sSubject    Subject
			//       sBody       Body
			//       dtStart     Date/Time of meeting start
			//       dtEnd       Date/Time of meeting end
			//       sRequiredAttendees      Comma delimited list of email addresses 
			//       sOptionalAttendees      Comma delimited list of email addresses 
			//       bReminderSet    Boolean for whether to set reminder
			//       iReminderMinutesBeforeStart Number of minutes before meeting for reminder
			//       sLocation   Meeting Location
			//       bIsOnlineMeeting    Boolean whether online
			//       bAllDayEvent        Boolean whether all day event
			//       sAttachmentFile     Name of file that exists in the temp folder to attach to the request


			//RETURNS:
			//       True or False if unsuccessful.

			string sProc = "clSend::CreateOutlookMeetingRequest";
			string sTMPPageID = "TMP_" + goData.GenSUID("XX");
			string sIni = "";

			goTR.StrWrite(ref sIni, "SUBJECT", sSubject);
			goTR.StrWrite(ref sIni, "BODY", sBody);
			goTR.StrWrite(ref sIni, "DTSTART", dtStart.ToString());
			goTR.StrWrite(ref sIni, "DTEND", dtEnd.ToString());
			goTR.StrWrite(ref sIni, "REQUIREDATTENDEES", sRequiredAttendees);
			goTR.StrWrite(ref sIni, "OPTIONALATTENDEES", sOptionalAttendees);
			goTR.StrWrite(ref sIni, "REMINDERSET", bReminderSet.ToString());
			goTR.StrWrite(ref sIni, "REMINDERMINUTES", iReminderMinutesBeforeStart.ToString());
			goTR.StrWrite(ref sIni, "LOCATION", sLocation);
			goTR.StrWrite(ref sIni, "ISMEETINGONLINE", bIsOnlineMeeting);
			goTR.StrWrite(ref sIni, "ALLDAYEVENT", bAllDayEvent);
			goTR.StrWrite(ref sIni, "ATTACHMENT", sAttachmentFile);

			if (!goMeta.PageWrite("GLOBAL", sTMPPageID, sIni))
			{
				goErr.SetError(35000, sProc, "Error writing TMP_ metadata to initiate an Outlook Meeting Request.");
				return false;
			}
			sIni = "";

			AddSendJob("Create Meeting Request", sTMPPageID, "", "REQUEST", "MEETINGREQUEST", "AC");

			return true;


		}
		public bool MarkRecordAsSent(string par_sID, string par_sObjectToSend)
		{
			//PURPOSE:
			//       Set in correspondence or record being sent field CHK_Sent to checked and
			//       update MMO_History.
			//PARAMETERS:
			//       par_sID: GID_ID of the record sent.
			//       par_sObjectToSend: "RECORD" or "CORR". If another value is passed, False is returned.
			//RETURNS:
			//       True on success, False otherwise. Sets error if file not valid. If Commit fails,
			//       sets warning and returns False.

			string sProc = "clSend::MarkRecordAsSent";

			string sID = null;
			string sFile = null;
			clRowSet doRS = null;

			//Try

			if (par_sObjectToSend.ToUpper() != "RECORD" && par_sObjectToSend.ToUpper() != "CORR")
			{
					return false;
				}

				sID = par_sID;
				sFile = goTR.GetFileFromSUID(sID);
				if (!goData.IsFileValid(sFile))
				{
					goErr.SetError(10100, sProc, "", sFile, "File retrieved from ID '" + sID + "'.");
					//10100: Invalid file name '[1]'. [2]
				}
				doRS = new clRowSet(sFile, clC.SELL_EDIT, "GID_ID=" + sID, "", "CHK_Sent, MMO_History", 1);
				//If we can't read the record, it got deleted by someone else. Do not raise error.
				if (doRS.GetFirst() == 1)
				{
					doRS.SetFieldVal("CHK_Sent", 1, clC.SELL_SYSTEM);
					switch (par_sObjectToSend.ToUpper())
					{
						case "RECORD":
							doRS.SetFieldVal("MMO_History", goTR.AddLineToRecHistory(Convert.ToString(doRS.GetFieldVal("MMO_History")), "Record Sent", sFile));
							break;
						default: //CORR
							doRS.SetFieldVal("MMO_History", goTR.AddLineToRecHistory(Convert.ToString(doRS.GetFieldVal("MMO_History")), "Sent", sFile));
							break;
					}
					if (doRS.Commit() != 1)
					{
						goErr.SetWarning(35000, sProc, "Updating CHK_Sent failed in record '" + sFile + "." + sID + "'.");
						return false;
					}
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return true;

		}
		//*******************************************************************************



	}

}
