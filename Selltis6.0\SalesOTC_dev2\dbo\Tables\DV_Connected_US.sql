﻿CREATE TABLE [dbo].[DV_Connected_US] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Division_Connected_User_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_DV] UNIQUEIDENTIFIER NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_DV_Connected_US] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_DV_Connected_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_Related_DV] FOREIGN KEY ([GID_DV]) REFERENCES [dbo].[DV] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[DV_Connected_US] NOCHECK CONSTRAINT [LNK_DV_Connected_US];


GO
ALTER TABLE [dbo].[DV_Connected_US] NOCHECK CONSTRAINT [LNK_US_Related_DV];


GO
CREATE CLUSTERED INDEX [IX_DV_Connected_US]
    ON [dbo].[DV_Connected_US]([GID_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_Related_DV]
    ON [dbo].[DV_Connected_US]([GID_DV] ASC);

