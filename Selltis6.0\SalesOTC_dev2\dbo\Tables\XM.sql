﻿CREATE TABLE [dbo].[XM] (
    [GID_ID]                  UNIQUEIDENTIFIER NOT NULL,
    [TXT_VIEW_NAME]           VARCHAR (50)     NOT NULL,
    [TXT_PAGE_TYPE]           VARCHAR (50)     NULL,
    [TXT_PAGE_TITLE]          VARCHAR (100)    NULL,
    [TXT_KEY]                 VARCHAR (50)     NULL,
    [SI__NAVBAR_HOME]         TINYINT          NULL,
    [SI__NAVBAR_JOURNAL]      TINYINT          NULL,
    [SI__NAVBAR_CREATELINKED] TINYINT          NULL,
    [TXT_CREATELINKED_TYPE]   VARCHAR (1000)   NULL,
    [SI__NAVBAR_EDIT]         TINYINT          NULL,
    [TXT_EDIT_PAGE_ID]        VARCHAR (50)     NULL,
    [SI__NAVBAR_MARKREVIEWED] TINYINT          NULL,
    [DTT_CREATIONTIME]        DATETIME         NULL,
    [SYS_Name]                NVARCHAR (320)   NULL,
    [SI__ShareState]          TINYINT          NULL,
    [GID_CreatedBy_US]        UNIQUEIDENTIFIER NULL,
    [TXT_DETAILS_PAGE_ID]     NVARCHAR (50)    NULL,
    [SI__NAVBAR_ADD]          INT              NULL,
    [TXT_ADD_TYPE]            NVARCHAR (50)    NULL,
    [TXT_ICON_NAME]           NVARCHAR (50)    NULL,
    [SI__PAGE_SIZE]           SMALLINT         NULL,
    [SI__LINKS_TOP_COUNT]     SMALLINT         NULL,
    [TXT_SWIPE1FIELD]         VARCHAR (100)    NULL,
    [TXT_SWIPE2FIELD]         VARCHAR (100)    NULL,
    [TXT_SWIPE1EDITPAGEID]    VARCHAR (100)    NULL,
    [TXT_SWIPE2EDITPAGEID]    VARCHAR (100)    NULL,
    [SI__SEARCH]              INT              NULL,
    [TXT_SEARCH]              VARCHAR (100)    NULL,
    [SI__ROLLODEX]            INT              NULL,
    [TXT_ROLLODEX]            VARCHAR (100)    NULL,
    [TXT_SWIPE3FIELD]         VARCHAR (100)    NULL,
    [TXT_SWIPE3EDITPAGEID]    VARCHAR (100)    NULL,
    [TXT_SWIPE1CLPAGEIDS]     VARCHAR (1000)   NULL,
    [TXT_SWIPE2CLPAGEIDS]     VARCHAR (1000)   NULL,
    [TXT_SWIPE3CLPAGEIDS]     VARCHAR (1000)   NULL,
    [DTT_ModTime]             DATETIME         NULL,
    [SI__CACHEDATA]           TINYINT          NULL,
    [SI__NEARME]              INT              NULL,
    [TXT_NEARME_PAGEID]       NVARCHAR (50)    NULL,
    [TXT_MAINTITLE]           NVARCHAR (150)   NULL,
    [SI__NAVBAR_SCAN]         INT              NULL,
    [TXT_SCANFIELD]           VARCHAR (100)    NULL,
    [SR__DISPLAYORDER]        REAL             NULL,
    [SI__SHOWASBTNINHOMEPAGE] TINYINT          NULL,
    [SI__SAVE_CREATE_ANOTHER] TINYINT          NULL,
    [CHK_PAGEISFORQUICKFORMS] TINYINT          NULL,
    CONSTRAINT [PK_XM] PRIMARY KEY CLUSTERED ([GID_ID] ASC) WITH (FILLFACTOR = 95)
);

