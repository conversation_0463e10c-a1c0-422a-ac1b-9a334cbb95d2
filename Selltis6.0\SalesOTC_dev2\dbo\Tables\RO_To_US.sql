﻿CREATE TABLE [dbo].[RO_To_US] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Routing_To_User_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_RO] UNIQUEIDENTIFIER NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_RO_To_US] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_RO_To_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_Connected_RO] FOREIGN KEY ([GID_RO]) REFERENCES [dbo].[RO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[RO_To_US] NOCHECK CONSTRAINT [LNK_RO_To_US];


GO
ALTER TABLE [dbo].[RO_To_US] NOCHECK CONSTRAINT [LNK_US_Connected_RO];


GO
CREATE CLUSTERED INDEX [IX_US_Connected_RO]
    ON [dbo].[RO_To_US]([GID_RO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RO_To_US]
    ON [dbo].[RO_To_US]([GID_US] ASC);

