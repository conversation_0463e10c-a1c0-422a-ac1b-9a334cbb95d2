﻿CREATE TABLE [dbo].[QL_Involves_US] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_QuotLine_Involves_User_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_QL] UNIQUEIDENTIFIER NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_QL_Involves_US] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_QL_Involves_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_InvolvedIn_QL] FOREIGN KEY ([GID_QL]) REFERENCES [dbo].[QL] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[QL_Involves_US] NOCHECK CONSTRAINT [LNK_QL_Involves_US];


GO
ALTER TABLE [dbo].[QL_Involves_US] NOCHECK CONSTRAINT [LNK_US_InvolvedIn_QL];


GO
CREATE CLUSTERED INDEX [IX_US_InvolvedIn_QL]
    ON [dbo].[QL_Involves_US]([GID_QL] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_Involves_US]
    ON [dbo].[QL_Involves_US]([GID_US] ASC);

