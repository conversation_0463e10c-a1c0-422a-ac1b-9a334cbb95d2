﻿CREATE TABLE [dbo].[OL_INVOLVES_US] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_OL_INVOLVES_US_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_OL] UNIQUEIDENTIFIER NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_OL_INVOLVES_US] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_OL_INVOLVES_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_INVOLVED_OL] FOREIGN KEY ([GID_OL]) REFERENCES [dbo].[OL] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[OL_INVOLVES_US] NOCHECK CONSTRAINT [LNK_OL_INVOLVES_US];


GO
ALTER TABLE [dbo].[OL_INVOLVES_US] NOCHECK CONSTRAINT [LNK_US_INVOLVED_OL];


GO
CREATE NONCLUSTERED INDEX [IX_OL_INVOLVES_US]
    ON [dbo].[OL_INVOLVES_US]([GID_OL] ASC, [GID_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_INVOLVED_OL]
    ON [dbo].[OL_INVOLVES_US]([GID_US] ASC, [GID_OL] ASC);

