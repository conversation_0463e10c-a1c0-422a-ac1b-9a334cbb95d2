﻿using System;
using System.Web;

//author: wt

namespace Selltis.BusinessLogic
{
	public class clHistoryItem
	{

		private clError goErr;

		private string gsType;
		private string gsGuid;
		private string gsTitle;
		private string gsSeqNo;
		private string gsNDBType;
		private string gsNDBGUID;
		private string gsSingleReferenceKey;

		public string Type
		{
			get
			{
				return gsType;
			}
			set
			{
				gsType = value;
			}
		}

		public string GUID
		{
			get
			{
				return gsGuid;
			}
			set
			{
				gsGuid = value;
			}
		}

		public string Title
		{
			get
			{
				return gsTitle;
			}
			set
			{
				gsTitle = value;
			}
		}

		public string SeqNo
		{
			get
			{
				return gsSeqNo;
			}
			set
			{
				gsSeqNo = value;
			}
		}

		public string NDBType
		{
			get
			{
				return gsNDBType;
			}
			set
			{
				gsNDBType = value;
			}
		}

		public string NDBGUID
		{
			get
			{
				return gsNDBGUID;
			}
			set
			{
				gsNDBGUID = value;
			}
		}

		public string SingleReferenceKey
		{
			get
			{
				return gsSingleReferenceKey;
			}
			set
			{
				gsSingleReferenceKey = value;
			}
		}

		private void Initialize()
		{
			string sProc = "Default.aspx::Initialize";
			// Try
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public clHistoryItem(string sType, string sGUID, string sTitle, string sSeqNo, string sSpecialType = "", string sSpecialGUID = "", string sSingleReferenceKey = "")
		{
			string sProc = "clHistoryItem::New";
			// Try
			Initialize();
				gsType = sType;
				gsGuid = sGUID;
				gsTitle = sTitle;

				//if there is a hard return here, then the history panel will error
				gsTitle = gsTitle.Replace("\r", "");
				gsTitle = gsTitle.Replace("\n", "");

				gsSeqNo = sSeqNo;
				gsNDBType = sSpecialType;
				gsNDBGUID = sSpecialGUID;

				gsSingleReferenceKey = sSingleReferenceKey;

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

	}
}
