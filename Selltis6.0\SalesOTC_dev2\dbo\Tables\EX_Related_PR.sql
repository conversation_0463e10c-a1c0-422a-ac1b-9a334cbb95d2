﻿CREATE TABLE [dbo].[EX_Related_PR] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Expense_Related_Project_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_EX] UNIQUEIDENTIFIER NOT NULL,
    [GID_PR] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_EX_Related_PR] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_EX_Related_PR] FOREIGN KEY ([GID_PR]) REFERENCES [dbo].[PR] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PR_Connected_EX] FOREIGN KEY ([GID_EX]) REFERENCES [dbo].[EX] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[EX_Related_PR] NOCHECK CONSTRAINT [LNK_EX_Related_PR];


GO
ALTER TABLE [dbo].[EX_Related_PR] NOCHECK CONSTRAINT [LNK_PR_Connected_EX];


GO
CREATE CLUSTERED INDEX [IX_PR_Connected_EX]
    ON [dbo].[EX_Related_PR]([GID_EX] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_Related_PR]
    ON [dbo].[EX_Related_PR]([GID_PR] ASC);

