﻿CREATE TABLE [dbo].[MS_Related_AC] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Message_Related_Activity_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_MS] UNIQUEIDENTIFIER NOT NULL,
    [GID_AC] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MS_Related_AC] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_AC_Connected_MS] FOREIGN KEY ([GID_MS]) REFERENCES [dbo].[MS] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_MS_Related_AC] FOREIGN KEY ([GID_AC]) REFERENCES [dbo].[AC] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MS_Related_AC] NOCHECK CONSTRAINT [LNK_AC_Connected_MS];


GO
ALTER TABLE [dbo].[MS_Related_AC] NOCHECK CONSTRAINT [LNK_MS_Related_AC];


GO
CREATE CLUSTERED INDEX [IX_AC_Connected_MS]
    ON [dbo].[MS_Related_AC]([GID_MS] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MS_Related_AC]
    ON [dbo].[MS_Related_AC]([GID_AC] ASC);

