using Selltis.BusinessLogic;
using System;
using System.Collections.Generic;

public class clInfoMessage
{
    protected List<string> cArrayCollection = new List<string>();

    public void AddInfo(string par_sString)
    {
        cArrayCollection.Add(par_sString);
    }

    public void ClearArray()
    {
        cArrayCollection.Clear();
    }

    public bool CopyAllIn(ref clArray par_doIM)
    {
        try
        {
            for (int i = 0; i < cArrayCollection.Count; i++)
            {
                par_doIM.AddInfo(cArrayCollection[i]);
            }

            return true;
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    public string CopyAllInString()
    {
        string sString = "";

        for (int i = 0; i < cArrayCollection.Count; i++)
        {
            sString += cArrayCollection[i] + Environment.NewLine;
        }

        return sString;
    }

    public int GetDimension()
    {
        return cArrayCollection.Count;
    }

    public string GetInfo(int par_lItem)
    {
        if (par_lItem < 1 || par_lItem > cArrayCollection.Count)
        {
            return "";
        }

        return cArrayCollection[par_lItem - 1];
    }

    public bool ModInfo(int par_lItem, string par_sNewValue)
    {
        if (par_lItem < 1 || par_lItem > cArrayCollection.Count)
        {
            return false;
        }

        cArrayCollection[par_lItem - 1] = par_sNewValue;
        return true;
    }

    public bool DeleteInfo(int par_lItem)
    {
        if (par_lItem < 1 || par_lItem > cArrayCollection.Count)
        {
            return false;
        }

        cArrayCollection.RemoveAt(par_lItem - 1);
        return true;
    }

    public int SeekInfo(string par_sInfo, bool par_bMode = false, bool par_bCaseSensitive = true)
    {
        for (int i = 0; i < cArrayCollection.Count; i++)
        {
            if (par_bMode)
            {
                if (par_bCaseSensitive)
                {
                    if (cArrayCollection[i] == par_sInfo)
                    {
                        return i + 1;
                    }
                }
                else
                {
                    if (cArrayCollection[i].ToUpper() == par_sInfo.ToUpper())
                    {
                        return i + 1;
                    }
                }
            }
            else
            {
                if (par_bCaseSensitive)
                {
                    if (cArrayCollection[i].StartsWith(par_sInfo))
                    {
                        return i + 1;
                    }
                }
                else
                {
                    if (cArrayCollection[i].ToUpper().StartsWith(par_sInfo.ToUpper()))
                    {
                        return i + 1;
                    }
                }
            }
        }

        return 0;
    }
}
