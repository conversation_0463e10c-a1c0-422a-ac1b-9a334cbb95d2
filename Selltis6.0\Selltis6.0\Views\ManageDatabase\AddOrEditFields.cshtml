﻿@model Selltis.MVC.Models.TableFieldsModel
@using Selltis.Core;
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    .panel-title {
        line-height: 34px !important;
    }

    .input-group[class*="col-"] {
        float: left !important;
        padding-left: 0;
        padding-right: 0;
    }

    #divfrmtitle {
        line-height: 38px;
        font-weight: 600 !important;
        font-size: 15px !important;
    }

    .card {
        background: #fff;
        margin-bottom: 20px;
        box-shadow: 0 2px 2px rgba(0,0,0,.15);
        margin-left: 10%;
        margin-right: 10%;
        min-height: 610px;
        /*margin-top: 1%;*/
    }

    .block-header {
        padding: 0 22px;
    }

    .block-header {
        margin-bottom: 25px;
        position: relative;
        /*padding-left: 120px;*/
    }

    /*a {
        background: transparent;
        font-size: 11px !important;
        font-family: Arial !important;
    }*/

    .anchorlink {
        font-size: 12px !important;
        font-family: Arial !important;
    }

    .dropdown-menu > li > a {
        display: block;
        padding: 10px 15px;
        clear: both;
        color: #666666;
        font-weight: normal;
        line-height: 1.49;
        white-space: nowrap;
    }

    .block-header > h2 {
        font-size: 15px;
        color: #777;
        margin: 0;
        font-weight: 400;
        text-transform: uppercase;
        font-family: Arial;
    }

    .actions {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .media > .pull-right {
        padding-left: 15px;
    }

    .actions > a, .actions > li > a {
        width: 30px;
        height: 30px;
        display: inline-block;
        text-align: center;
        padding-top: 5px;
    }

    a:focus, a:hover {
        color: #0a6ebd;
        text-decoration: none;
    }

    a {
        background-color: transparent;
    }

    .actions > a > i, .actions > li > a > i {
        color: #adadad;
        font-size: 20px;
    }

    .list-group .list-group-item {
        border: 0;
        margin: 0;
        padding: 15px 23px;
    }

    .card {
        box-shadow: 0 1px 1px rgba(0,0,0,.15);
        border: 1px solid #cccccc;
    }

    .lgi-attrs {
        list-style: none;
        padding: 0;
        margin: 0;
    }

        .lgi-attrs > li {
            display: inline-block;
            border: 1px solid #e0e0e0;
            margin: 2px 2px 2px 0;
            padding: 2px 5px;
        }

        .lgi-attrs > li, .lgi-text {
            font-size: 12px;
            color: #777;
        }

    .maintitle {
        font-family: Arial;
        font-size: 13px;
        font-weight: 400;
        color: #000000 !important;
    }

    .subtitle {
        font-family: Arial;
        font-size: 12px;
        font-weight: 400;
        color: #666 !important;
    }

    .btn-info1 {
        color: #ffffff;
        background-color: #82E0AA;
    }

    .btn-infoL1 {
        color: #ffffff;
        background-color: #F2CCCC;
    }

    .btn-infoL2 {
        color: #ffffff;
        background-color: #E5BDE3;
    }

    .btn-infoL3 {
        color: #ffffff;
        background-color: #A9B0EC;
    }

    .list-group.lg-odd-black .list-group-item:nth-child(even) {
        background-color: #F7F7F7;
    }

    .input-group[class*="col-"] {
        float: left !important;
        padding-left: 0;
        padding-right: 0;
    }

    a,
    a label {
        cursor: pointer;
        color: black;
    }


    .k-dropdown-wrap.k-state-default.k-state-focused {
        border: none;
        box-shadow: none;
        border-bottom: 2px solid #428bca;
    }

    div.list-group-item.divLast {
        border-bottom: 2px #2196F3 solid;
    }

    .z-depth-2 {
        /*box-shadow: 0 8px 17px rgba(0,0,0,.7),0 6px 20px rgba(0,0,0,.19);*/
        background-color: #428bca !important;
    }

    .blackiconcolor {
        color: #3bafda;
    }

    /*Form Header to be shown always..S1*/
    #VisibleContentId {
        position: fixed;
        z-index: 600;
        width: 85.9%;
    }

     /*Show click to go to top button when scroll down..S1*/
    .back-to-top {
        cursor: pointer;
        position: fixed;
        bottom: 20px;
        right: 20px;
        display: none;
    }
</style>

<script>
    $(document).on('click', '.list-group .list-group-item', function (e)
    {
        var $this = $(this);
        sessionStorage.setItem("LastSelectedField" + '@Model.FileName', $(this)[0].id);
        var current = document.querySelector('.divLast');
        var current1 = document.querySelector('.z-depth-2');
        if (current) {
            current.classList.remove('divLast');
        }
        if (current1) {
            current1.classList.remove('z-depth-2');
        }
        $this.addClass('divLast');
        $this.addClass('z-depth-2');
    }
    );
</script>

<section id="content_wrapper" style="background-color: #EEEEEE;min-height:103%;">
    <section id="content" class="animated fadeIn" style="padding-left:0px;padding-top:0px;padding-right:0px;padding-bottom:0px;height: 100%;">

        <div class="row" id="VisibleContentId" style="background-color: #EEEEEE;">
            <div class="col-md-12">
                <div class="panel panel-visible" id="spy2" style="margin-bottom: 5px !important; background-color: #e7e7e7;height:38px;">
                    <div class="topbar-left">
                        <div class="panel-heading" style="background: #e9e9e9!important;height:38px;">
                            <div class="panel-heading-btn" style="float:right;margin-top: 3px;">
                                <button type="button" class="btn btn-sm btn-primary" title="Close" onclick="onCancel()" id="btClose"><i class="fa fa-times"></i></button>
                            </div>
                            <span id="divfrmtitle" style="line-height:38px;font-weight:bold;" class="panel-title"><i class="fa fa-files-o" aria-hidden="true"></i>&nbsp;&nbsp;Add / Edit Fields</span>
                        </div>
                    </div>
                </div>
                <div class="row" style="height:10px;"></div>
                <div class="row" id="FilterHeaderId" style="border: 1px solid #cccccc;background-color:#FAFAFA !important;margin-left: 10%;margin-right: calc(10% - 5px);">
                    <div class="col-md-6" style="padding-top: 10px; padding-left: 10px; padding-bottom: 10px; margin-top: 3px;">
                        <div class="col-md-6">
                            <span>
                                File Name&nbsp;:&nbsp;@Model.FileName
                            </span>
                        </div>
                        <div class="col-md-6">
                            <span>
                                <span>
                                    File Label&nbsp;:&nbsp;@Model.LabelName
                                </span>
                            </span>
                        </div>
                    </div>
                    <div class="pull-right">
                        <div class="col-md-2" style="padding-top:7px;width:100%">
                            <label id="lbltxtCount" style="float:left;padding-right:10px;">Count:&nbsp;@Model._TableFields.Count </label>
                            <button type="button" class="btn btn-sm btn-primary" title="Add new field" onclick="AddNewField()" id="btnNew"><i class="fa fa-plus"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="DummyDivForFormContent" style="height:94px;"></div>

        <div class="card">
            <div id="eventList" class="list-group lg-odd-black">
                @foreach (var Field in Model._TableFields)
                            {
                            <div class="list-group-item" id="@Field.FieldName">
                                <div class="pull-left" style="padding-right: 10px;">
                                    <span style="border-radius:64px;" class="btn btn-circle-sm btn-info">@Util.GetFistCharOfString(Field.FieldName)</span>
                                </div>

                                <div class="pull-right">
                                    <div class="actions dropdown apps" onclick="fieldActionsClick('@Field.FieldName')">
                                        <a href="" data-toggle="dropdown" aria-expanded="true">
                                            <i class="fa fa-ellipsis-v"></i>
                                        </a>
                                        <ul class="dropdown-menu dropdown-menu-right">
                                            <li>
                                                <a onclick="onEditFieldClick('@Model.FileName', '@Field.FieldName')" href="#"><i class="fa fa-pencil"></i>&nbsp;&nbsp;Edit</a>
                                            </li>
                                            <li>
                                                <a onclick="onFieldDeleteClick('@Model.FileName', '@Field.FieldName')" href="#"><i class="fa fa-trash-o"></i>&nbsp;&nbsp;Delete</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="media-body">
                                    <div class="maintitle"><a class="anchorlink" onclick="onEditFieldClick('@Model.FileName', '@Field.FieldName')">@Field.Label</a></div>
                                    @*<div class="subtitle">@TableData[1]</div>*@
                                    <ul class="lgi-attrs">
                                        @if (string.IsNullOrEmpty(Field.FieldName) == false)
                                            {
                                            <li class="btn-infoL1">@Field.FieldName</li>
                                            }
                                        @if (string.IsNullOrEmpty(Field.FieldType) == false)
                                            {
                                            <li class="btn-infoL2">@Field.FieldType</li>
                                            }
                                        @if (string.IsNullOrEmpty(Field.Length) == false)
                                            {
                                            <li class="btn-infoL3">@Field.Length</li>
                                            }
                                    </ul>
                                </div>

                            </div>
                            }
            </div>
        </div>
    </section>
</section>

@*Show click to go to top button when scroll down..S1*@
<a id="back-to-top" href="#" class="btn btn-primary btn-sm back-to-top" role="button" title="Go to top" data-placement="left"><span class="glyphicon glyphicon-chevron-up"></span></a>

<script>
    function onCancel() {
        showProgress();
        window.location.href = '/ManageDatabase/ManageDatabase';
    }
    function AddNewField() {
        showProgress();
        window.location.href = '/ManageDatabase/AddOrEditSelectedField?FileName=' + '@Model.FileName';
    }
    function onEditFieldClick(filename, fieldname) {
        showProgress();
        sessionStorage.setItem("LastSelectedField" + '@Model.FileName', fieldname);
        window.location.href = '/ManageDatabase/AddOrEditSelectedField?FileName=' + filename + '&FieldName=' + fieldname;
    }
    $(document).ready(function () {
        //Setting Header & Tabstrip width.. S1
        setTimeout(SettingHeaderHeightWidth, 200);

        var LastSelectedField = sessionStorage.getItem("LastSelectedField" + '@Model.FileName');

        if (LastSelectedField == null || LastSelectedField == "" || LastSelectedField == undefined) {

            $("div.list-group-item:first").addClass("divLast");
            $("div.list-group-item:first").addClass("z-depth-2");

        }
        else {
            $('#' + LastSelectedField).addClass("divLast");
            $('#' + LastSelectedField).addClass("z-depth-2");

            $('html, body').animate({
                scrollTop: $('#' + LastSelectedField).offset().top - 170
            }, 100);
        }

        //var VisibleContentIdHeight = $('#VisibleContentId').height();
        //$('#DummyDivForFormContent').height(VisibleContentIdHeight - 5);
    });
    function onFieldDeleteClick(filename, fieldname) {
        var result = confirm("Are you sure, you want to delete?");

        if (result) {
            showProgress();
            $.ajax({
                url: '/ManageDatabase/DeleteField',
                data: { FileName: filename, FieldName: fieldname },
                async: true,
                cache: false,
                success: function (data) {
                    if (data == "success") {
                        sessionStorage.removeItem("LastSelectedField" + '@Model.FileName');
                        location.href = '/ManageDatabase/AddOrEditFields?TableName=' + filename;
                    }
                    else if (data == "delete disabled") {
                        hideProgress();
                        alert("This field is disabled for update and delete. ");
                    }
                    else if (data == "fail") {
                        hideProgress();
                    }
                    else {
                        window.location.href = '/ErrorLog/LogError?sErrorLogs=ScriptsError';
                    }
                },
                error: function (data) {
                    hideProgress();
                    alert(data.responseText);
                }
            })
        }
    }
    function fieldActionsClick(fieldname) {
        var lastSelectedField = sessionStorage.getItem("LastSelectedField" + '@Model.FileName');

        if (lastSelectedField != fieldname) {
            if (lastSelectedField == null || lastSelectedField == undefined || lastSelectedField == "") {
                $("div.list-group-item:first").removeClass("divLast");
                $("div.list-group-item:first").removeClass("z-depth-2");
            }
            else {
                $('#' + lastSelectedField).removeClass("divLast");
                $('#' + lastSelectedField).removeClass("z-depth-2");
            }

            $('#' + fieldname).addClass('divLast');
            $('#' + fieldname).addClass('z-depth-2');
            sessionStorage.setItem("LastSelectedField" + '@Model.FileName', fieldname);
        }
    }
    function SortChanged() {
        //var selectedSortVal = $("#Sort").val();
        //window.location.href = "/ManageDatabase/AddOrEditFields?TableName=" + selectedSortVal;

    }

    //Set Responsive Header Height and width..S1
    function SettingHeaderHeightWidth() {
        var SideMenuWidth = $('#sidebar_left').width();
        var winWidth = $(window).width();
        $('#VisibleContentId').width(winWidth - SideMenuWidth);

        var VisibleContentIdHeight = $('#VisibleContentId').height();
        $('#DummyDivForFormContent').height(VisibleContentIdHeight - 5);
    }

    $(window).resize(function () {
        setTimeout(SettingHeaderHeightWidth(), 200);
    });

    $(window).scroll(function () {
        //$('#eventList').scroll(function () {
        if ($(this).scrollTop() > 50) {
            $('#back-to-top').fadeIn();
        } else {
            $('#back-to-top').fadeOut();
        }
    });
    // scroll body to 0px on click
    $('#back-to-top').click(function () {
        $('#back-to-top').tooltip('hide');
        $('body,html').animate({
            //$('#eventList').animate({
            scrollTop: 0
        }, 800);
        return false;
    });
    //$('#back-to-top').tooltip('show');
</script>