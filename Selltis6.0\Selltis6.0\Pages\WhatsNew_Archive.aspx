<%@ Page Title="" Language="VB"  Debug="true"  %>

<%@ Register TagPrefix="telerik" Namespace="Telerik.Web.UI" Assembly="Telerik.Web.UI" %>

<%@ Import Namespace="Microsoft.VisualBasic" %>
<%@ Import Namespace="Telerik.Web.UI" %>
<%@ Import Namespace="Telerik.Web.UI.OrgChartStyles" %>
<%@ Import Namespace="System.Data" %>
<%@ Import Namespace="System.Data.SqlClient" %>
<%@ Import Namespace="System.Collections.Generic" %>
<%@ Import Namespace="Selltis.BusinessLogic" %>

<script runat="server">
    'OWNER: CS CREATED 8/20
	'MI 4/24/09 added viewport meta tag to control page zooming. Added hyperlinks to archives on top of the page.
    
    'PURPOSE:
    '		present 'what's new' information on releases
    'URL PARAMETERS:


    Dim goP As clProject
    Dim goMeta As clMetaData
    Dim goTR As clTransform
    Dim goData As clData
    Dim goErr As clError
    Dim goLog As clLog
    Dim goDef As clDefaults
    Dim goUI As clUI
    'Dim gw As cldiaTemplate
    
    '"Global" variables are now in gw, "global window" object

    
    Private Sub Initialize()
        Dim sProc As String = "WhatsNew.aspx::Initialize"
        goP = HttpContext.Current.Session("goP")
        goTR = HttpContext.Current.Session("goTr")
        goMeta = HttpContext.Current.Session("goMeta")
        goData = HttpContext.Current.Session("goData")
        goErr = HttpContext.Current.Session("goErr")
        goLog = HttpContext.Current.Session("goLog")
        goDef = HttpContext.Current.Session("goDef")
        goUI = HttpContext.Current.Session("goUI")
    End Sub
    
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim sProc As String = "WhatsNew.aspx::Page_Load"
        Dim sReturn As String = "~/Pages/Redirect.aspx?url=../pages/default.aspx"
        If HttpContext.Current.Session.IsNewSession = True Then HttpContext.Current.Response.Redirect(sReturn, True)
        If IsPostBack Then
            Try
                goP = HttpContext.Current.Session("goP")
                If IsNothing(goP) Then HttpContext.Current.Response.Redirect(sReturn, True)
            Catch ex As Exception
                HttpContext.Current.Response.Redirect(sReturn, True)
            End Try
        End If
        
        Try
            Dim Init As New clInit
            
            'Initialize global objects
            Initialize()
            
            Dim sEvent As String
            sEvent = Request.Form("__EVENTTARGET")
            Select Case sEvent
                Case "SaveState"
                    Response.Redirect("../Pages/blank.aspx?type=load")
                Case Else
                    'We need BTN_Cancel code to execute on the server.
                    'If no postback were desired when the page is closed, use this:
                    'Me.btn_Cancel.Attributes("onclick") = "javascript:parent.RefreshContent('SiteWorkarea','CLOSEDIALOG'); return false;"
                    'Initialize the page "global" object
                    
                    'If Not IsPostBack Then
                    '    If HttpUtility.UrlDecode(Request.QueryString("backfromsubdialog")) = "1" Then
                    '        'This is a return from a sub dialog such as diaFiltCnd back to diaFiltPro
                    '        '==> Edit
                    '        gw = HttpContext.Current.Session("diaThisPage")
                    '        gw.bBackFromSubdialog = True
                    '    Else
                    '        'Reset the session-scope object for this page, for ex set diaFiltCnd to clDiaFiltCnd
                    '        '==> Edit
                    '        HttpContext.Current.Session("diaThisPage") = New cldiaTemplate()     '==> Edit
                    '    End If
                    'End If
                    
                    '==> Edit
                    'gw = HttpContext.Current.Session("diaThisPage")
                    OnOpen()
            End Select

        Catch ex As Exception
            If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
                goErr.SetError(ex, 45105, sProc)
            End If
        End Try
    End Sub

    
    
    Protected Sub OnOpen()
        Dim sProc As String = "WhatsNew.aspx::OnOpen"
        
        Try
            
            If Not IsPostBack Then
                'If Not gw.bBackFromSubdialog Then
                    
                '    '==> Add code here
                    
                '    'Set information about this page's opening mode and the page to return to
                '    gw.sMode = HttpUtility.UrlDecode(Request.QueryString("mode"))
                '    gw.sReturnURL = HttpUtility.UrlDecode(Request.QueryString("returnto"))
                '    If gw.sReturnURL = "" Then gw.sReturnURL = Request.RawUrl
                'End If
                
            End If
        
        Catch ex As Exception
            If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
                goErr.SetError(ex, 45105, sProc)
            End If
        End Try

    End Sub
    
    
    Protected Sub Close()
        Dim sProc As String = "WhatsNew.aspx::Close"
        'Close the page. This is called from BTN_OK and BTN_Cancel OnClick events.
        
        Try
            
            Dim sTemp As String = ""
        
            'If no postback were desired when the page is closed, use this:
            'Me.btn_Cancel.Attributes("onclick") = "javascript:parent.RefreshContent('SiteWorkarea','CLOSEDIALOG'); return false;"

            '--------------- REDIRECT TO PRECEDING DIALOG PAGE -------------------
            'If UCase(gw.sReturnURL) = "DIAXXXXXXX.ASPX" Then    '==> Edit
            '    goTR.URLWrite(sTemp, , "diaXxxxXxx.aspx")       '==> Edit
            '    'goTR.URLWrite(sTemp, "returnto", gw.sReturnURL)
            '    goTR.URLWrite(sTemp, "backfromsubdialog", "1")
            '    Response.Redirect(sTemp)
            'Else
            '    Response.Redirect(goUI.Navigate("CLOSEDIALOG", ""))
            'End If

              'Response.Redirect(goUI.Navigate("CLOSEDIALOG", "").Replace("../", ""))

        Catch ex As Exception
            If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
                goErr.SetError(ex, 45105, sProc)
            End If
        End Try

    End Sub
    
    
    '--------------- EVENTS ------------------

    Protected Sub btn_Cancel_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim sProc As String = "WhatsNew.aspx::btnCancel_Click"
        Try
            Close()
        Catch ex As Exception
            If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
                goErr.SetError(ex, 45105, sProc)
            End If
        End Try
    End Sub
    
    
    Protected Sub btn_CustomModification_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim sProc As String = "WhatsNew.aspx::btnCustomModification_Click"
        Try
            Response.Redirect("~" & Selltis.Core.Util.GetCustomWhatsNewFile().ToString & "/cus_WhatsNewCustom.html", False)
        Catch ex As Exception
            If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
                goErr.SetError(ex, 45105, sProc)
            End If
        End Try
    End Sub

    

    '----------------------- PROCEDURES --------------------------

    'Protected Sub Xxxxx()
    '    Dim sProc As String = gw.fenenexecution & "::????????"
    '    Try
            
    '    Catch ex As Exception
    '        If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
    '            goErr.SetError(ex, 45105, sProc)
    '        End If
    '    End Try
    'End Sub
    
    'WHATS NEW FORMAT: <b>What</b> | what happens | under what condition(s) | previous behavior
    
    
    
    
    


</script>


<html xmlns="http://www.w3.org/1999/xhtml" >

<head id="Head1" runat="server">
    <title>What's New Archive</title>
    <meta name="viewport" content="width=320" />
</head>

<body onload="javascript:if(parent.Workarea_LoadPage_NoPostback){parent.Workarea_LoadPage_NoPostback('What\'s New Archive','CLOSEDIALOG');}" style="font-size: 8pt">
<!-- The above assumes no need for postback in case the user browses away from the dialog. This is typically the case -->
    <form id="form1" runat="server">
        <div id="IPADSCROLL">

<!--
-->

<script type="text/javascript">
    window.onresize = SetScroll;
    window.onorientationchange = SetScroll;

    SetScroll();

    function SetScroll() {
        try {

            if ('<%=Session("SA_DEVICE")%>' != 'IPAD') { return; }

            var winW = parent.document.getElementById("CONTENTWIDTH").value; 
            var winH = parent.document.getElementById("CONTENTHEIGHT").value;        


            //set entire desktop width, the width of the browser
            document.getElementById("IPADSCROLL").style.height = winH + 'px';
            document.getElementById("IPADSCROLL").style.width = winW + 'px';
            document.getElementById("IPADSCROLL").style.overflow = 'scroll';

        } //end try
        catch (err) {

        }
    }
</script>


    <div style="background-color: white;">
        <table cellpadding="2" cellspacing="0" id="TABLE1" style="font-family: verdana;font-size:8pt;">           
            <tr>
                <td>                  
                    <%--<asp:HyperLink ID="HyperLink1" runat="server" Font-Bold="True" NavigateUrl="~/Pages/cus_WhatsNewCustom.aspx">Custom Modifications</asp:HyperLink>--%>
                    <asp:LinkButton ID="lnkCustomModifications" runat="server" Font-Bold="True" OnClick="btn_CustomModification_Click">Custom Modifications</asp:LinkButton><br />
                    <br />
                    <asp:HyperLink ID="HyperLink4" runat="server" Font-Bold="True" NavigateUrl="~/Pages/WhatsNew.aspx">Current Year</asp:HyperLink><br />
                    <br /> 
                    <asp:HyperLink ID="HyperLink10" runat="server" Font-Bold="True" NavigateUrl="~/Pages/WhatsNew_20152016.aspx">Archive for Year 2015 and 2016</asp:HyperLink><br />
                    <br />                                                                      
                    <asp:HyperLink ID="HyperLink9" runat="server" Font-Bold="True" NavigateUrl="~/Pages/WhatsNew_20132014.aspx">Archive for Year 2013 and 2014</asp:HyperLink><br />
                    <br />                                                                       
                    <asp:HyperLink ID="HyperLink7" runat="server" Font-Bold="True" NavigateUrl="~/Pages/WhatsNew_2012.aspx">Archive for Year 2012</asp:HyperLink><br />
                    <br />                                                                       
                    <asp:HyperLink ID="HyperLink8" runat="server" Font-Bold="True" NavigateUrl="~/Pages/WhatsNew_2011.aspx">Archive for Year 2011</asp:HyperLink><br />
                    <br />                                                                       
                    <asp:HyperLink ID="HyperLink6" runat="server" Font-Bold="True" NavigateUrl="~/Pages/WhatsNew_2010.aspx">Archive for Year 2010</asp:HyperLink><br />
                    <br />                                                                       
                    <asp:HyperLink ID="HyperLink5" runat="server" Font-Bold="True" NavigateUrl="~/Pages/WhatsNew_2009.aspx">Archive for Year 2009</asp:HyperLink><br />
                    <br />                                                                      
                    <asp:HyperLink ID="HyperLink2" runat="server" Font-Bold="True" NavigateUrl="~/Pages/WhatsNew_2008.aspx">Archive for Year 2008</asp:HyperLink><br />
                    <br />                                                                       
                    <asp:HyperLink ID="HyperLink3" runat="server" Font-Bold="True" NavigateUrl="~/Pages/WhatsNew_2007.aspx">Archive for Year 2007</asp:HyperLink><br />
                    <br />
                </td>
                <td style="width: 16px">
                </td>
            </tr>
            
            <tr>
                <td align="center" valign="top"><hr />
                                <%--<asp:Button ID="btn_Cancel" runat="server" Text="Close" OnClick="btn_Cancel_Click" Height="24px" Width="120px" />--%>
                </td>
                <td align="center" style="width: 16px" valign="top">
                </td>
            </tr>           
           
        </table>
        
        </div>
        

<!--
-->
    </div>
    </form>
</body>
</html>
