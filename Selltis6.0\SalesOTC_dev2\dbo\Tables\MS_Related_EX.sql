﻿CREATE TABLE [dbo].[MS_Related_EX] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Message_Related_Expense_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_MS] UNIQUEIDENTIFIER NOT NULL,
    [GID_EX] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MS_Related_EX] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_EX_Connected_MS] FOREIGN KEY ([GID_MS]) REFERENCES [dbo].[MS] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_MS_Related_EX] FOREIGN KEY ([GID_EX]) REFERENCES [dbo].[EX] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MS_Related_EX] NOCHECK CONSTRAINT [LNK_EX_Connected_MS];


GO
ALTER TABLE [dbo].[MS_Related_EX] NOCHECK CONSTRAINT [LNK_MS_Related_EX];


GO
CREATE CLUSTERED INDEX [IX_EX_Connected_MS]
    ON [dbo].[MS_Related_EX]([GID_MS] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MS_Related_EX]
    ON [dbo].[MS_Related_EX]([GID_EX] ASC);

