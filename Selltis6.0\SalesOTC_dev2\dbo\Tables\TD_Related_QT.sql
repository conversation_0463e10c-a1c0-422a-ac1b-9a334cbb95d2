﻿CREATE TABLE [dbo].[TD_Related_QT] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_ToDo_Related_Quote_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_QT] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_Related_QT] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_QT_Related_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_Related_QT] FOREIGN KEY ([GID_QT]) REFERENCES [dbo].[QT] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_Related_QT] NOCHECK CONSTRAINT [LNK_QT_Related_TD];


GO
ALTER TABLE [dbo].[TD_Related_QT] NOCHECK CONSTRAINT [LNK_TD_Related_QT];


GO
CREATE CLUSTERED INDEX [IX_QT_Related_TD]
    ON [dbo].[TD_Related_QT]([GID_TD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_Related_QT]
    ON [dbo].[TD_Related_QT]([GID_QT] ASC);

