﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8CC83905-5157-4BB0-8AFD-BC74349029C1}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Selltis.Custom.AutoInc</RootNamespace>
    <AssemblyName>Selltis.Custom.AutoInc</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\Selltis6.0\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Ads.Common">
      <HintPath>..\Common\P21SDK_Lib\Ads.Common.dll</HintPath>
    </Reference>
    <Reference Include="Ads.Common.License">
      <HintPath>..\Common\P21SDK_Lib\Ads.Common.License.dll</HintPath>
    </Reference>
    <Reference Include="Ads.Common.Service">
      <HintPath>..\Common\P21SDK_Lib\Ads.Common.Service.dll</HintPath>
    </Reference>
    <Reference Include="Ads.Soa.Business">
      <HintPath>..\Common\P21SDK_Lib\Ads.Soa.Business.dll</HintPath>
    </Reference>
    <Reference Include="Ads.Soa.DomainObject">
      <HintPath>..\Common\P21SDK_Lib\Ads.Soa.DomainObject.dll</HintPath>
    </Reference>
    <Reference Include="Ads.Soa.Rest">
      <HintPath>..\Common\P21SDK_Lib\Ads.Soa.Rest.dll</HintPath>
    </Reference>
    <Reference Include="Ads.Soa.Soap">
      <HintPath>..\Common\P21SDK_Lib\Ads.Soa.Soap.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Newtonsoft.Json.5.0.6\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="P21.Business">
      <HintPath>..\Common\P21SDK_Lib\P21.Business.dll</HintPath>
    </Reference>
    <Reference Include="P21.Common">
      <HintPath>..\Common\P21SDK_Lib\P21.Common.dll</HintPath>
    </Reference>
    <Reference Include="P21.DataAccess">
      <HintPath>..\Common\P21SDK_Lib\P21.DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="P21.DomainObject">
      <HintPath>..\Common\P21SDK_Lib\P21.DomainObject.dll</HintPath>
    </Reference>
    <Reference Include="P21.DomainObject.UDF">
      <HintPath>..\Common\P21SDK_Lib\P21.DomainObject.UDF.dll</HintPath>
    </Reference>
    <Reference Include="P21.FormsAndReporting.Model">
      <HintPath>..\Common\P21SDK_Lib\P21.FormsAndReporting.Model.dll</HintPath>
    </Reference>
    <Reference Include="P21.Soa.Service">
      <HintPath>..\Common\P21SDK_Lib\P21.Soa.Service.dll</HintPath>
    </Reference>
    <Reference Include="P21.UI">
      <HintPath>..\Common\P21SDK_Lib\P21.UI.dll</HintPath>
    </Reference>
    <Reference Include="P21.UI.BulkEditor">
      <HintPath>..\Common\P21SDK_Lib\P21.UI.BulkEditor.dll</HintPath>
    </Reference>
    <Reference Include="P21.UI.BulkEditor.Model">
      <HintPath>..\Common\P21SDK_Lib\P21.UI.BulkEditor.Model.dll</HintPath>
    </Reference>
    <Reference Include="P21.UI.BulkEditor.Service">
      <HintPath>..\Common\P21SDK_Lib\P21.UI.BulkEditor.Service.dll</HintPath>
    </Reference>
    <Reference Include="P21.UI.Service">
      <HintPath>..\Common\P21SDK_Lib\P21.UI.Service.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ScriptsCustom.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Selltis.BusinessLogic\Selltis.Data.csproj">
      <Project>{0718c809-a336-45b1-9029-253af6e1fc66}</Project>
      <Name>Selltis.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\Selltis.Core\Selltis.Core.csproj">
      <Project>{6f68b0de-b188-4aa1-aaae-5d90bc4a6942}</Project>
      <Name>Selltis.Core</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>