﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.IO;
using System.Net;
using System.Xml;
using System.Text.RegularExpressions;
using System.Diagnostics;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Data.SqlClient;

namespace Selltis.Custom
{
    public class ScriptsCustom
    {
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clProject goP;
        private clLog goLog;
        private clError goErr;
        private clPerm goPerm;
        private ClUI goUI;
        ScriptManager scriptManager = new ScriptManager();

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";
        SqlConnection par_oConnection = null;
        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();

            Util.SetSessionValue("SkipQLSpecificLogic", "Y");
        }
        public ScriptsCustom()
        {
            Initialize();
        }
        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //*** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            //try
            //{


            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }

            //}

            return true;

        }
        public bool AC_FormAfterSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //TLD 7/17/2012 Added for Create Quote
            if (doForm.GetControlVal("CHK_CREATEQT") == "CHECKED")
            {
                string GIDID = (doForm.doRS.GetFieldVal("GID_ID") == null) ? "" : doForm.doRS.GetFieldVal("GID_ID").ToString();
                Form doFormQT = new Form("QT", GIDID, "CRL_QT");
                //VS 07262016 TKT#1172 : Close the AC when creating a quote from AC create QT on Save.
                doFormQT.doRS.oVar.SetVar("COMPLETEAC", "1");
                doFormQT.doRS.oVar.SetVar("COMPLETEACRECORDID", doForm.doRS.GetFieldVal("GID_ID"));

                goUI.Queue("FORM", doFormQT);
            }

            return true;

        }
        public bool AC_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //TLD 9/28/2012 Default to Service Issue tab
            //when purpose is Service
            long MLSPurpose = (doForm.doRS.GetFieldVal("MLS_Purpose", 2) == null) ? 0 : Convert.ToInt64(doForm.doRS.GetFieldVal("MLS_Purpose", 2));
            if (MLSPurpose == 6 | doForm.GetOpeningMode() == "SERVICEISSUE")
            {
                doForm.MoveToTab(13);
                //Service Issue
            }

            bool bRunNext = true;
            scriptManager.RunScript("ServiceRMA_ManageControlState", ref par_doCallingObject, ref par_oReturn, ref bRunNext, ref par_sSections);


            doForm.doRS.oVar.SetVar("Current_LNK_NEXTACTION_US", doForm.doRS.GetFieldVal("LNK_NEXTACTION_US"));

            return true;

        }

        public bool AC_FormControlOnChange_MLS_PRODUCTISSUETYPE_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            //try
            //{
            Form doForm = (Form)par_doCallingObject;

            bool bRunNext = true;
            scriptManager.RunScript("ServiceRMA_ManageControlState", ref par_doCallingObject, ref par_oReturn, ref bRunNext, ref par_sSections);

            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}

            return true;
        }

        public bool ServiceRMA_ManageControlState_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: doForm.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            //try
            //{

            Form doForm = (Form)par_doCallingObject;

            doForm.SetControlState("CHK_NEWADVOFPROBLEMENTINSYSTEM", 2);
            doForm.SetControlState("CHK_NEWADVOFREQUESTENTINSYSTEM", 2);
            doForm.SetControlState("CHK_TICKETISSUED", 2);
            doForm.SetControlState("CHK_COMPLETED", 2);
            doForm.SetControlState("CHK_RMAPAPERWORKSENTTOCUST", 2);
            doForm.SetControlState("CHK_RMAPAPERWORKRETURNED", 2);
            doForm.SetControlState("CHK_RMAISSUEDENTINSYSTEM", 2);
            doForm.SetControlState("CHK_PORECEIVEDFOREVALUAION", 2);
            doForm.SetControlState("CHK_ITEMSHIPPEDTOFLMFACTORYTRACKING", 2);
            doForm.SetControlState("CHK_ITEMEVALUATED", 2);
            doForm.SetControlState("CHK_ITEMREPAIRQUOTED", 2);
            doForm.SetControlState("CHK_REPAIRAUTHORIZED", 2);
            doForm.SetControlState("CHK_POUPDATED", 2);
            doForm.SetControlState("CHK_ITEMREPAIRED", 2);
            doForm.SetControlState("CHK_ITEMSHIPPEDBACKTOCUSTTRACKING", 2);
            doForm.SetControlState("CHK_FUTOCUST", 2);
            doForm.SetControlState("CHK_NEWITEMMANUFACTURED", 2);
            doForm.SetControlState("CHK_ITEMCORRECTED", 2);
            doForm.SetControlState("CHK_PRBLMREMEDIEDDESCRIPTION", 2);

            switch (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_PRODUCTISSUETYPE", 2)))
            {
                case 1:
                    //Fields to be visible for -Item Returned
                    doForm.SetControlState("CHK_NEWADVOFPROBLEMENTINSYSTEM", 0);
                    doForm.SetControlState("CHK_RMAPAPERWORKSENTTOCUST", 0);
                    doForm.SetControlState("CHK_RMAPAPERWORKRETURNED", 0);
                    doForm.SetControlState("CHK_RMAISSUEDENTINSYSTEM", 0);
                    doForm.SetControlState("CHK_ITEMSHIPPEDTOFLMFACTORYTRACKING", 0);
                    doForm.SetControlState("CHK_ITEMCORRECTED", 0);
                    doForm.SetControlState("CHK_ITEMSHIPPEDBACKTOCUSTTRACKING", 0);
                    doForm.SetControlState("CHK_FUTOCUST", 0);
                    doForm.SetControlState("CHK_COMPLETED", 0);

                    break;
                case 2:
                    //Fields to be visible for -Item Not Returned
                    doForm.SetControlState("CHK_NEWADVOFPROBLEMENTINSYSTEM", 0);
                    doForm.SetControlState("CHK_NEWITEMMANUFACTURED", 0);
                    doForm.SetControlState("CHK_ITEMSHIPPEDBACKTOCUSTTRACKING", 0);
                    doForm.SetControlState("CHK_FUTOCUST", 0);
                    doForm.SetControlState("CHK_COMPLETED", 0);

                    break;
                case 3:
                    //Fields to be visible for -Repair
                    doForm.SetControlState("CHK_NEWADVOFREQUESTENTINSYSTEM", 0);
                    doForm.SetControlState("CHK_RMAPAPERWORKSENTTOCUST", 0);
                    doForm.SetControlState("CHK_RMAPAPERWORKRETURNED", 0);
                    doForm.SetControlState("CHK_RMAISSUEDENTINSYSTEM", 0);
                    doForm.SetControlState("CHK_PORECEIVEDFOREVALUAION", 0);
                    doForm.SetControlState("CHK_ITEMSHIPPEDTOFLMFACTORYTRACKING", 0);
                    doForm.SetControlState("CHK_ITEMEVALUATED", 0);
                    doForm.SetControlState("CHK_ITEMREPAIRQUOTED", 0);
                    doForm.SetControlState("CHK_REPAIRAUTHORIZED", 0);
                    doForm.SetControlState("CHK_POUPDATED", 0);
                    doForm.SetControlState("CHK_ITEMREPAIRED", 0);
                    doForm.SetControlState("CHK_ITEMSHIPPEDBACKTOCUSTTRACKING", 0);
                    doForm.SetControlState("CHK_FUTOCUST", 0);
                    doForm.SetControlState("CHK_COMPLETED", 0);

                    break;
                case 4:
                    //Fields to be visible for -Solved over the phone
                    doForm.SetControlState("CHK_NEWADVOFPROBLEMENTINSYSTEM", 0);
                    doForm.SetControlState("CHK_PRBLMREMEDIEDDESCRIPTION", 0);
                    doForm.SetControlState("CHK_COMPLETED", 0);
                    break;
                case 5:
                    //Fields to be visible for -Solved over the phone w/Service trip
                    doForm.SetControlState("CHK_NEWADVOFPROBLEMENTINSYSTEM", 0);
                    doForm.SetControlState("CHK_TICKETISSUED", 0);
                    doForm.SetControlState("CHK_COMPLETED", 0);

                    break;
            }

            par_doCallingObject = doForm;

            return true;
            //}
            //catch (Exception ex)
            //{
            //    if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
            //    {
            //        goErr.SetError(ex, 45105, sProc);
            //    }
            //}

            return true;

        }



        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //TLD 7/17/2012 ----- Enforce Product if Type is Sales Visit
            long MLSPurpose = (doForm.doRS.GetFieldVal("MLS_Purpose", 2) == null) ? 0 : Convert.ToInt64(doForm.doRS.GetFieldVal("MLS_Purpose", 2));
            if (MLSPurpose == 11 & doForm.doRS.IsLinkEmpty("LNK_Related_PD") == true)
            {
                doForm.MoveToTab(4);
                //Details
                doForm.MoveToField("LNK_Related_PD");
                goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("LNK_RELATED_PD"), "", "", "", "", "", "", "", "", "LNK_RELATED_PD");
                return false;
            }

            //TLD 9/28/2012 ----- Enforce Product if Purpose is Service Issue
            if (MLSPurpose == 6)
            {
                //------------Enforce Product if Purpose is Service Issue
                if (doForm.doRS.IsLinkEmpty("LNK_Related_PD") == true)
                {
                    doForm.MoveToTab(13);
                    //Service Issue
                    doForm.MoveToField("LNK_Related_PD");
                    goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("LNK_RELATED_PD"), "", "", "", "", "", "", "", "", "LNK_RELATED_PD");
                    return false;
                }

                //------------Enforce Next Action Date if Purpose is Service Issue
                string DTE_NEXTACTIONDATE = (doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1) == null) ? null : doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1).ToString();
                if (string.IsNullOrEmpty(DTE_NEXTACTIONDATE))
                {
                    doForm.MoveToTab(13);
                    //Service Issue
                    doForm.MoveToField("DTE_NEXTACTIONDATE");
                    goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE");
                    return false;
                }

                //TLD 9/23/2014 Commented, now they don't want this
                //'TLD 9/22/2014 Added
                //'------------Enforce Service Issue if Purpose is Service Issue
                //If doForm.doRS.GetFieldVal("TXT_SrvcIssue") = "" Then
                //    doForm.MoveToTab(13)     'Service/RMA Issue
                //    doForm.MoveToField("TXT_SrvcIssue")
                //    goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("TXT_SrvcIssue"), "", "", "", "", "", "", "", "", "TXT_SrvcIssue")
                //    Return False
                //End If

                //'------------Enforce RMA # if Purpose is Service Issue
                //If doForm.doRS.GetFieldVal("TXT_RGANo") = "" Then
                //    doForm.MoveToTab(13)     'Service/RMA Issue
                //    doForm.MoveToField("TXT_RGANo")
                //    goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("TXT_RGANo"), "", "", "", "", "", "", "", "", "TXT_RGANo")
                //    Return False
                //End If

                //'------------Enforce Related Activity Logs if Purpose is Service Issue
                //If doForm.doRS.IsLinkEmpty("LNK_Related_AC") = True Then
                //    doForm.MoveToTab(9)     'Actions
                //    doForm.MoveToField("LNK_Related_AC")
                //    goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("LNK_Related_AC"), "", "", "", "", "", "", "", "", "LNK_Related_AC")
                //    Return False
                //End If
            }
            par_doCallingObject = doForm;
            return true;

        }
        public bool AC_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //TLD 9/28/2012 ------Call script to send email(s)
            //Not already run
            string AC_AC_NotificationEmailSent = (doForm.oVar.GetVar("AC_NotificationEmailSent") == null) ? "" : doForm.oVar.GetVar("AC_NotificationEmailSent").ToString();
            if (AC_AC_NotificationEmailSent != "1")
            {
                //Checked
                if (doForm.doRS.GetFieldVal("CHK_NotificationEmail", 2) != null)
                {
                    if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_NotificationEmail", 2)) == 1)
                    {
                        //goScr.RunScript("SendEmails", doForm, , "AC");
                        par_doCallingObject = doForm;
                        bool runnext = true;
                        scriptManager.RunScript("SendEmails", ref par_doCallingObject, ref par_oReturn, ref runnext, ref par_sSections, null, "AC");
                    }
                }
            }

            return true;

        }
        public bool AC_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool AC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            //SKO ******** TKT#1054 : Profile Matrix
            //CS 11/19/2014 If Activity is of type Sales Visit, update linked Related Company.Last AC Sales date
            //new ac
            if (doRS.GetInfo("TYPE") == "2")
            {
                //sales visit
                int MLS_TYPE = (doRS.GetFieldVal("MLS_TYPE", 2) == null) ? 0 : Convert.ToInt32(doRS.GetFieldVal("MLS_TYPE", 2));
                if (MLS_TYPE == 11)
                {
                    if (doRS.GetLinkCount("LNK_RELATED_CO") > 0)
                    {
                        clArray doCompanies = new clArray();
                        doCompanies = (clArray)doRS.GetFieldVal("LNK_RELATED_CO", 2);
                        //Bipass reconsave and validation to speed up AC save
                        for (int i = 1; i <= doCompanies.GetDimension(); i++)
                        {
                            clRowSet doRSCompany = new clRowSet("CO", 1, "GID_ID='" + doCompanies.GetItem(i) + "'", "", "DTT_LastACSales", -1, "", "", "", "", "", true, true);
                            if (doRSCompany.GetFirst() == 1)
                            {
                                doRSCompany.SetFieldVal("DTT_LastACSales", doRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                //log error but proceed
                                if (doRSCompany.Commit() == 0)
                                {
                                    string TXT_CompanyName = (doRSCompany.GetFieldVal("TXT_CompanyName") == null) ? "" : doRSCompany.GetFieldVal("TXT_CompanyName").ToString();
                                    goLog.Log(sProc, "CO update of last AC sales visit date field failed for CO " + TXT_CompanyName + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                }
                            }
                            doRSCompany = null;
                        }
                    }
                }
                else
                {
                    //Activity is not of type sales visit
                    if (doRS.GetLinkCount("LNK_RELATED_CO") > 0)
                    {
                        clArray doCompanies = new clArray();
                        doCompanies = (clArray)doRS.GetFieldVal("LNK_RELATED_CO", 2);
                        //Bipass reconsave and validation to speed up AC save
                        for (int i = 1; i <= doCompanies.GetDimension(); i++)
                        {
                            clRowSet doRSCompany = new clRowSet("CO", 1, "GID_ID='" + doCompanies.GetItem(i) + "'", "", "DTT_LastAC", -1, "", "", "", "", "", true, true);
                            if (doRSCompany.GetFirst() == 1)
                            {
                                doRSCompany.SetFieldVal("DTT_LastAC", doRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                //log error but proceed
                                if (doRSCompany.Commit() == 0)
                                {
                                    string TXT_CompanyName = (doRSCompany.GetFieldVal("TXT_CompanyName") == null) ? "" : doRSCompany.GetFieldVal("TXT_CompanyName").ToString();
                                    goLog.Log(sProc, "CO update of last AC date field failed for CO " + TXT_CompanyName + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                }
                            }
                            doRSCompany = null;
                        }
                    }

                }
            }

            //CS 11/19/2014 If Activity is of type Sales Visit, update linked Related Contact.Last AC Sales Date
            //new ac
            if (doRS.GetInfo("TYPE") == "2")
            {
                //sales visit
                int MLS_TYPE = (doRS.GetFieldVal("MLS_TYPE", 2) == null) ? 0 : Convert.ToInt32(doRS.GetFieldVal("MLS_TYPE", 2));
                if (MLS_TYPE == 11)
                {
                    if (doRS.GetLinkCount("LNK_RELATED_CN") > 0)
                    {
                        clArray doContacts = new clArray();
                        doContacts = (clArray)doRS.GetFieldVal("LNK_RELATED_CN", 2);
                        //Bipass reconsave and validation to speed up AC save
                        for (int i = 1; i <= doContacts.GetDimension(); i++)
                        {
                            clRowSet doRSContact = new clRowSet("CN", 1, "GID_ID='" + doContacts.GetItem(i) + "'", "", "DTT_LastACSales", -1, "", "", "", "", "", true, true);
                            if (doRSContact.GetFirst() == 1)
                            {
                                doRSContact.SetFieldVal("DTT_LastACSales", doRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                //log error but proceed
                                if (doRSContact.Commit() == 0)
                                {
                                    string TXT_NameLast = (doRSContact.GetFieldVal("TXT_NameLast") == null) ? "" : doRSContact.GetFieldVal("TXT_NameLast").ToString();
                                    goLog.Log(sProc, "CN update of last AC sales visit date field failed for CN " + TXT_NameLast + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                }
                            }
                            doRSContact = null;
                        }
                    }
                }
                else
                {
                    //Activity is not of type sales visit, so update Related Contact. Last AC date
                    if (doRS.GetLinkCount("LNK_RELATED_CN") > 0)
                    {
                        clArray doContacts = new clArray();
                        doContacts = (clArray)doRS.GetFieldVal("LNK_RELATED_CN", 2);
                        //Bipass reconsave and validation to speed up AC save
                        for (int i = 1; i <= doContacts.GetDimension(); i++)
                        {
                            clRowSet doRSContact = new clRowSet("CN", 1, "GID_ID='" + doContacts.GetItem(i) + "'", "", "DTT_LastAC", -1, "", "", "", "", "", true, true);
                            if (doRSContact.GetFirst() == 1)
                            {
                                doRSContact.SetFieldVal("DTT_LastAC", doRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                //log error but proceed
                                if (doRSContact.Commit() == 0)
                                {
                                    string TXT_NameLast = (doRSContact.GetFieldVal("TXT_NameLast") == null) ? "" : doRSContact.GetFieldVal("TXT_NameLast").ToString();
                                    goLog.Log(sProc, "CN update of last AC date field failed for CN " + TXT_NameLast + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                }
                            }
                            doRSContact = null;
                        }
                    }
                }
            }

            string sLetter = HttpUtility.UrlDecode(Convert.ToString(doRS.GetFieldVal("MMO_LETTER")));
            sLetter = sLetter.Replace("<br />", "").Replace("<br/>", "").Replace("<br>", "").Replace("<p>&nbsp;</p>", "").Replace("<P>&nbsp;</P>", "");
            doRS.SetFieldVal("MMO_LETTER", sLetter);
            par_doCallingObject = doRS;

            if (doRS.oVar.GetVar("Current_LNK_NEXTACTION_US") != null && (Convert.ToString(doRS.oVar.GetVar("Current_LNK_NEXTACTION_US")) != Convert.ToString(doRS.GetFieldVal("LNK_NEXTACTION_US"))))
            {
                string sSubject = "";
                string sBody = "";
                clEmail oEmail = new clEmail();
                string sToEmail = "";
                string sFromEmail = "Selltis";
                clArray doLink = new clArray();

                sSubject = Convert.ToString(doRS.GetFieldVal("TXT_ISSUEDESCRIPTION"));
                sBody = Convert.ToString(doRS.GetFieldVal("MMO_NEXTACTION"));
                sToEmail = Convert.ToString(doRS.GetFieldVal("LNK_NEXTACTION_US%%EML_EMAIL"));

                if (string.IsNullOrEmpty(sToEmail))
                {
                    goLog.Log(sProc, "Notification Email failed for Activity Log " + doRS.GetCurrentRecID() + ".  No email addresses for Notification Users.", 1, false, true);
                    return true;
                    //No email addresses
                }

                //Send Email to user
                if (oEmail.SendSMTPEmail(sSubject, sBody, sToEmail, "", "", "", "Do Not Reply", sFromEmail, "", true) == true)
                {

                }
            }



            return true;

        }
        public bool AutoCOUpdate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //SKO ******** TKT#1054 : Profile Matrix
            //TLD 7/24/2012
            //PURPOSE:
            //       Called by agent to update 3 custom date fields that
            //       record latest AC of type Sales Visit, latest OP and latest Quote
            //A_01_EXECUTE=AutoCOUpdate
            //A_01_OBJSHARED = 1
            //A_01_TYPE = RUNSCRIPT
            //A_ORDER=1,
            //ACTIONS=1,
            //ACTIVE = 1
            //E_TM_HOUR = 19 '7:00 PM
            //E_TM_INTERVAL = 3 'Every day
            //E_TM_MINUTE = 0 'top of hour
            //EVENT=TIMER
            //US_NAME=AutoCOUpdate
            //US_PURPOSE=Runs daily Update of Company records
            //SORTVALUE1=TIMER_ACTIVE

            long lBiID = 0;
            long lastBiId = 0;
            clRowSet doRS = default(clRowSet);
            clRowSet doNewRS = default(clRowSet);
            long iCount = 0;
            bool bUpdateCO = false;
            string par_sDelim = "";
            string sNowDate = goTR.DateTimeToSysString(goTR.NowLocal(), ref par_iValid, ref par_sDelim);
            string sRecID = "";
            string sWOP = goMeta.PageRead("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED");
            int iFailedOther = 0;
            int iFailedPerm = 0;
            int iFailedTotal = 0;
            int iSuccess = 0;

            try
            {
                do
                {
                    doRS = new clRowSet("CO", 1, "CHK_TargetAcct=1 AND bi__id > " + lBiID + " AND (LNK_CONNECTED_AC%%BI__ID>0 OR LNK_CONNECTED_OP%%BI__ID>0 OR LNK_RECEIVED_QT%%BI__ID>0)", "bi__id asc", "*, LNK_Connected_OP, LNK_Connected_AC, LNK_Received_QT", 50, "", "", "", "", "", true, true, true);
                    iCount = iCount + doRS.Count();
                    if (doRS.GetFirst() == 1)
                    {
                        lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));

                        do
                        {
                            bUpdateCO = false;
                            //until set to true below
                            sRecID = (doRS.GetCurrentRecID() == null) ? "" : doRS.GetCurrentRecID().ToString();

                            //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit
                            if (doRS.GetLinkCount("LNK_Connected_AC") > 0)
                            {
                                doNewRS = new clRowSet("AC", 3, "MLS_Type=11 AND LNK_Related_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastACSales", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }
                            //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                            //TLD 5/18/2011 -----------Added to include date of latest OP
                            if (doRS.GetLinkCount("LNK_Connected_OP") > 0)
                            {
                                doNewRS = new clRowSet("OP", 3, "LNK_For_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastOP", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }
                            //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                            //TLD 5/18/2011 -----------Added to include date of latest QT
                            if (doRS.GetLinkCount("LNK_Received_QT") > 0)
                            {
                                doNewRS = new clRowSet("QT", 3, "LNK_To_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                                if (doNewRS.GetFirst() == 1)
                                {
                                    doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                    bUpdateCO = true;
                                }
                            }

                            //Update CO
                            if (bUpdateCO == true)
                            {
                                if (doRS.Commit() == 0)
                                {
                                    if (goErr.GetLastError("NUMBER") == "E47250")
                                    {
                                        //Commit failed b/c user has no permissions to edit record; log it, but proceed
                                        iFailedPerm = iFailedPerm + 1;
                                        goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " due to permissions.", 1, false, true);
                                        //testing
                                    }
                                    else
                                    {
                                        //Commit failed for some other reason.
                                        iFailedOther = iFailedOther + 1;
                                        goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                        //testing
                                    }
                                }
                            }
                            if (doRS.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                        lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                        //get last BI__ID processed
                    }
                    else
                    {
                        break; // TODO: might not be correct. Was : Exit Do
                    }
                } while (true);

                //Check once more for any newly added records
                doRS = new clRowSet("CO", 1, "CHK_TargetAcct=1 AND bi__id > " + lBiID + " AND (LNK_CONNECTED_AC%%BI__ID>0 OR LNK_CONNECTED_OP%%BI__ID>0 OR LNK_RECEIVED_QT%%BI__ID>0)", "bi__id asc", "*, LNK_Connected_OP, LNK_Connected_AC, LNK_Received_QT", -1, "", "", "", "", "", true, true, true);
                iCount = iCount + doRS.Count();
                if (doRS.GetFirst() == 1)
                {
                    do
                    {
                        bUpdateCO = false;
                        //until set to true below
                        sRecID = (doRS.GetCurrentRecID() == null) ? "" : doRS.GetCurrentRecID().ToString();

                        //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit
                        if (doRS.GetLinkCount("LNK_Connected_AC") > 0)
                        {
                            doNewRS = new clRowSet("AC", 3, "MLS_Type=11 AND LNK_Related_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastACSales", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }
                        //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                        //TLD 5/18/2011 -----------Added to include date of latest OP
                        if (doRS.GetLinkCount("LNK_Connected_OP") > 0)
                        {
                            doNewRS = new clRowSet("OP", 3, "LNK_For_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastOP", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }
                        //TLD 5/18/2011 -----------Added to include date of latest AC, Sales Visit

                        //TLD 5/18/2011 -----------Added to include date of latest QT
                        if (doRS.GetLinkCount("LNK_Received_QT") > 0)
                        {
                            doNewRS = new clRowSet("QT", 3, "LNK_To_CO='" + sRecID + "'", "DTT_CreationTime DESC", "DTT_CreationTime", 1);
                            if (doNewRS.GetFirst() == 1)
                            {
                                doRS.SetFieldVal("DTT_LastQT", doNewRS.GetFieldVal("DTT_CreationTime", 2), 2);
                                bUpdateCO = true;
                            }
                        }

                        //Update CO
                        if (bUpdateCO == true)
                        {
                            if (doRS.Commit() == 0)
                            {
                                if (goErr.GetLastError("NUMBER") == "E47250")
                                {
                                    //Commit failed b/c user has no permissions to edit record; log it, but proceed
                                    iFailedPerm = iFailedPerm + 1;
                                    goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " due to permissions.", 1, false, true);
                                    //testing
                                }
                                else
                                {
                                    //Commit failed for some other reason.
                                    iFailedOther = iFailedOther + 1;
                                    goLog.Log(sProc, "CO update of last custom date fields failed for CO " + ((doRS.GetFieldVal("TXT_CompanyName") == null) ? "" : doRS.GetFieldVal("TXT_CompanyName").ToString()) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                                    //testing
                                }
                            }
                        }

                        if (doRS.GetNext() == 0)
                            break; // TODO: might not be correct. Was : Exit Do
                    } while (true);
                    lBiID = (doRS.GetFieldVal("BI__ID") == null) ? 0 : Convert.ToInt64(doRS.GetFieldVal("BI__ID"));
                    //get last bi__id processed        
                }
                iFailedTotal = iFailedOther + iFailedPerm;
                iSuccess = Convert.ToInt32(iCount) - iFailedTotal;

                //Write to WOP
                goTR.StrWrite(ref sWOP, "AUTOCOUPDATE", "Started " + sNowDate + " and Completed " + goTR.DateTimeToSysString(goTR.NowLocal(), ref par_iValid, ref par_sDelim) + " with " + iSuccess.ToString() + " successful updates; " + iFailedPerm.ToString() + " failed updates due to permissions; " + iFailedOther.ToString() + " total failed updates.");
                goMeta.PageWrite("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED", sWOP);

                iCount = 0;
                iFailedOther = 0;
                iFailedPerm = 0;
                iFailedTotal = 0;
                iSuccess = 0;
                doRS = null;
                lBiID = 0;

            }
            catch (Exception ex)
            {
                goTR.StrWrite(ref sWOP, "AUTOCOUPDATE", "Failed at Record " + sRecID + " " + goErr.GetLastError("NUMBER"));
                goMeta.PageWrite("GLOBAL", "OTH_DAILY_AUTOCOUPDATE_PROCESSED", sWOP);
            }

            return true;

        }
        public bool AutoAlertEveryDay_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter in all rowsets to get all users' private recs
            //MI 10/19/07 started converting the script to be aware of the start of the day depending on the user's time zone
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DETAILS, true);

            Project_Timing_Alerts();

            //TLD 9/24/2012 Disable alerts
            //TLD 7/17/2013 Now they want the Overdue Quotes alert to run
            //goTR.StrWrite(par_sSections, "AlertOverdueQuotes", "0")
            // goTR.StrWrite(ref par_sSections, "AlertOverdueOpps", "0");
            goTR.StrWrite(ref par_sSections, "AlertOverdueLeads", "0");
            //TLD 9/26/2012 Disable more alerts
            goTR.StrWrite(ref par_sSections, "AlertOverdueContacts", "0");
            goTR.StrWrite(ref par_sSections, "AlertOverdueProjects", "0");

            //TLD 9/28/2012 Added for custom alerts
            Form doForm = (Form)par_doCallingObject;
            //*** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
            //It is unlikely that there would be non-shared User records, but just in case...
            clRowSet rsUsers = new clRowSet("US", clC.SELL_READONLY, "CHK_ACTIVEFIELD=1", "SYS_Name", "GID_ID", -1, "", "", "", "", "", false, false, true);
            //goLog.Log(sProc, "Users count: " & rsUsers.Count)
            //No active users
            if (rsUsers.Count() == 0)
                return true;

            //PURPOSE:
            //		Every day display alerts about things that require review or followup,
            //		update Appt dates (reschedule)/To Do due dates.
            //		The alerts are typically set up to open an appropriate desktop.
            //		This replaces 'alarm' agents in Selltis 1.0. The name of the agent 
            //		is on the top of each section of code.
            //       MI 10/19/07: Making the script aware of ea user's time zone so that
            //       the alerts, auto-updates, rescheduling, etc. occurs after midnight
            //       user's time and not server's time. See additional notes in the code.
            //HOW THIS WORKS:
            //       This script runs hourly by an automator AGE_2005072616363748192MAR 00002XX
            //       that has the following definition:
            //           A_01_EXECUTE = AutoAlertEveryDay
            //           A_01_OBJSHARED = 1
            //           A_01_TYPE = RUNSCRIPT
            //           A_ORDER=1,
            //           ACTIONS=1,
            //           ACTIVE = 1
            //           E_TM_HOUR = 0
            //           E_TM_INTERVAL = 2
            //           E_TM_MINUTE = 1
            //           E_TM_MISSED = 1
            //           EVENT=TIMER
            //           US_NAME=AutoAlertEveryDay
            //           US_PURPOSE=Add alerts every morning for followup of leads, contacts, to dos, etc.
            //           SORTVALUE1=TIMER_ACTIVE
            //       Make sure the timer automator engine is running on this site's web server.
            //WARNING:
            //       Tread carefully - this is a very sensitive area.

            clRowSet doRS = default(clRowSet);
            string sResult = null;
            //Dim sStartDate As String
            //Dim sDueDate As String
            bool bMore = true;
            string sCurrentUser = "";
            //Dim dtDate As Date
            //Dim lDays As Long
            string sPointers = null;
            string sPointerDateTime = null;
            DateTime dtPointerDate = default(DateTime);
            PublicDomain.TzTimeZone zone = default(PublicDomain.TzTimeZone);
            DateTime dtUsersNow = default(DateTime);
            //User time zone 'now'
            DateTime dtUsersToday = default(DateTime);
            //User time zone Today (now.Date())
            string sDateTime = null;
            DateTime dtDateTime = default(DateTime);
            string par_sDelim = "|";
            //Dim sStartTime As String
            //Dim sEndTime As String

            //Get a page with date stamps of last dates when daily timer script was processed for ea user. Ex:
            //   USER1GID_ID=2008-03-25
            //   USER2GID_ID=2008-03-22
            //   ...
            //where USERnGID_ID is a Selltis global ID (SUID).
            sPointers = goMeta.PageRead("GLOBAL", "OTH_DAILY_CUSTOM_SCRIPT_PROCESSED");


            //--------------------- Non-user-dependent updates -----------------------


            //---------------------- Per-user processing loop ------------------

            while (bMore == true)
            {
                //The timer automator fires this script every hour.
                //Here we make sure the actual update is made only once in a day for each user.
                //This is very important because we are modifying data (changing Appointment 
                //date field values, for example) for the whole day. This should not be done 
                //piece meal or it will be confusing to users.

                //A 'day' is seen from the perspective of the currently processed user's
                //current time zone. If the user switches to a different time zone within 
                //the same day, the processing should not occur again until the next day 
                //starts in that time zone. As a consequence, for users traveling west 
                //this script may not fire for up to an extra day. Users traveling east 
                //will have the processing occur in less than 24 hours from the last time it ran.

                //Read user's 'last processed' date (recorded in user's time zone at the time)
                sCurrentUser = (rsUsers.GetFieldVal("GID_ID", clC.SELL_FRIENDLY) == null) ? null : rsUsers.GetFieldVal("GID_ID", clC.SELL_FRIENDLY).ToString();
                //CS REMOVE 1/6/12
                //goLog.Log(sProc, "Current user: " & rsUsers.GetFieldVal("SYS_NAME"))

                //DEBUG 
                //goLog.Log(sProc, "  Processing user '" & goData.GetRecordNameByID(sCurrentUser) & "' [" & sCurrentUser & "]", clC.SELL_LOGLEVEL_DETAILS)
                //END DEBUG

                //Get user's time zone
                zone = goTR.UTC_GetUserTimeZone(sCurrentUser);
                //Get user's 'now' and 'today at midnight' in the user's current time zone
                dtUsersNow = zone.ToLocalTime(goTR.NowUTC());
                //User's current datetime in his/her time zone
                dtUsersToday = dtUsersNow.Date;
                //Midnight on user's current date in his/her time zone
                //Get the 'last processed' date (no time) as a local datetime
                //Pointers are written as local datetimes in user's last login time zone.
                //sPointerDateTime = Strings.Left(goTR.StrRead(sPointers, sCurrentUser, "", false), 10);
                sPointerDateTime = Strings.Left(goTR.StrRead(sPointers, sCurrentUser, "", false), 10);
                if (string.IsNullOrEmpty(sPointerDateTime))
                {
                    //Leaving one whole day ahead of 'blank' datetime: '1753-01-02 23:59:59.000'.
                    dtPointerDate = goTR.StringToDate("1753-01-04", "", ref par_iValid);
                }
                else
                {
                    dtPointerDate = goTR.StringToDate(sPointerDateTime, clC.SELL_FORMAT_DATEDEF, ref par_iValid).Date;
                }
                dtPointerDate = DateTime.SpecifyKind(dtPointerDate, DateTimeKind.Local);

                //*** MI 10/23/07 Replaced >= with just = to avoid not processing for days or months in case 
                //the server clock was accidentally set ahead and the processing occurred... In that case, 
                //pointer datetimes set far in the future would preclude running this.
                //If dtPointerDate >= dtUsersToday Then GoTo ProcessNextUser

                //DEBUG
                //goLog.Log(sProc, "    Pointer date: '" & goTR.DateTimeToSysString(dtPointerDate) & "' User's date: '" & goTR.DateTimeToSysString(dtUsersToday) & "'", clC.SELL_LOGLEVEL_DETAILS)
                //END DEBUG

                if (dtPointerDate == dtUsersToday)
                    goto ProcessNextUser;
                //If dtPointerDate = dtUsersToday Then
                //    'CS 1/16/12
                //    goLog.Log(sProc, "Go to ProcessNextUser", , , True)
                //    GoTo ProcessNextUser
                //End If


                //--------- Set 'today' -------------
                //sDateTime replaces the Selltis keyword 'Today' with the datetime value that corresponds to midnight
                //in the currently processed user's time zone
                dtDateTime = goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday));

                sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_sDelim);

                //--------- Set 'tomorrow' -----------
                //Set user's 'tomorrow at midnight' as local datetime for filtering
                dtDateTime = goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday));
                dtDateTime = goTR.AddDay(dtDateTime, 1);
                sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_sDelim);


                //------- Activities -- Service Issue Overdue ----------
                //sDateTime: tomorrow
                //*** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                doRS = new clRowSet("AC", clC.SELL_READONLY, "MLS_STATUS=0 AND MLS_PURPOSE=6 AND DTT_NEXTACTIONDATE<'" + sDateTime + "' AND LNK_CreditedTo_US='" + sCurrentUser + "'", "", "GID_ID", 1, "", "", "", "", "", false, false, true);
                //Create a rowset with the condition
                if (doRS.GetFirst() == 1)
                {
                    //Open Desktop Acts - Service Issues Overdue
                    sResult = goUI.AddAlert("Service Issue Overdue", "OPENDESKTOP", "DSK_3E9B6EDD-48A9-47C1-5858-A0DA0128E618", sCurrentUser, "ACTIVITY16.gif").ToString();
                    doRS = null;
                }



                //TLD 9/28/2012 Updated to custom MD
                //Update the daily processing pointer with current datetime in the processed user's time zone
                goMeta.LineWrite("GLOBAL", "OTH_DAILY_CUSTOM_SCRIPT_PROCESSED", sCurrentUser, goTR.DateTimeToSysString(dtUsersNow, ref par_iValid, ref par_sDelim), ref par_oConnection);
                ProcessNextUser:


                if (rsUsers.GetNext() == 0)
                    bMore = false;

            }

            rsUsers = null;

            //DEBUG:
            //goLog.Log(sProc, "End: Return True")
            //END DEBUG

            return true;

        }

        public bool Project_Timing_Alerts()
        {
            try
            {

                goLog.Log("Project_Timing_Alerts", "Start", clC.SELL_LOGLEVEL_DEBUG, false, true, 1);

                string sCondtion = "(CHK_Q10=1 AND (DTT_Q10>='today' AND DTT_Q10<='today|23:59:59'))";
                sCondtion = sCondtion + " OR (CHK_Q20=1 AND (DTT_Q20>='today' AND DTT_Q20<='today|23:59:59'))";
                sCondtion = sCondtion + " OR (CHK_Q30=1 AND (DTT_Q30>='today' AND DTT_Q30<='today|23:59:59'))";
                sCondtion = sCondtion + " OR (CHK_Q40=1 AND (DTT_Q40>='today' AND DTT_Q40<='today|23:59:59'))";
                sCondtion = sCondtion + " OR (CHK_Q50=1 AND (DTT_Q50>='today' AND DTT_Q50<='today|23:59:59'))";
                sCondtion = sCondtion + " OR (CHK_Q90=1 AND (DTT_Q90>='today' AND DTT_Q90<='today|23:59:59'))";

                clRowSet rsPRs = new clRowSet("PR", clC.SELL_READONLY, sCondtion, "SYS_Name", "LNK_ORIGINATEDBY_US%%GID_ID,GID_ID,TXT_PROJECTNAME,DTE_Q10,DTE_Q20,DTE_Q30,DTE_Q40,DTE_Q50,DTE_Q90", -1, "", "", "", "", "", false, false, true);

                bool bMore = true;

                if (rsPRs.Count() == 0)
                {
                    goLog.Log("Project_Timing_Alerts", "No records found", clC.SELL_LOGLEVEL_DEBUG, false, false, 1);
                    return true;
                }


                DateTime dtQ10 = default(DateTime);
                DateTime dtQ20 = default(DateTime);
                DateTime dtQ30 = default(DateTime);
                DateTime dtQ40 = default(DateTime);
                DateTime dtQ50 = default(DateTime);
                DateTime dtQ90 = default(DateTime);

                string sCurrentUser = "";
                string sProjectName = "";
                string sRecordId = "";

                while (bMore == true)
                {
                    sCurrentUser = (rsPRs.GetFieldVal("LNK_ORIGINATEDBY_US%%GID_ID", clC.SELL_FRIENDLY) == null) ? null : rsPRs.GetFieldVal("LNK_ORIGINATEDBY_US%%GID_ID", clC.SELL_FRIENDLY).ToString();
                    sProjectName = rsPRs.GetFieldVal("TXT_PROJECTNAME").ToString();
                    sRecordId = rsPRs.GetFieldVal("GID_ID").ToString();

                    dtQ10 = rsPRs.GetFieldVal("DTE_Q10").ToString() == "" ? default(DateTime) : Convert.ToDateTime(rsPRs.GetFieldVal("DTE_Q10"));
                    dtQ20 = rsPRs.GetFieldVal("DTE_Q20").ToString() == "" ? default(DateTime) : Convert.ToDateTime(rsPRs.GetFieldVal("DTE_Q20"));
                    dtQ30 = rsPRs.GetFieldVal("DTE_Q30").ToString() == "" ? default(DateTime) : Convert.ToDateTime(rsPRs.GetFieldVal("DTE_Q30"));
                    dtQ40 = rsPRs.GetFieldVal("DTE_Q40").ToString() == "" ? default(DateTime) : Convert.ToDateTime(rsPRs.GetFieldVal("DTE_Q40"));
                    dtQ50 = rsPRs.GetFieldVal("DTE_Q50").ToString() == "" ? default(DateTime) : Convert.ToDateTime(rsPRs.GetFieldVal("DTE_Q50"));
                    dtQ90 = rsPRs.GetFieldVal("DTE_Q90").ToString() == "" ? default(DateTime) : Convert.ToDateTime(rsPRs.GetFieldVal("DTE_Q90"));

                    if (dtQ10.Date == DateTime.Today)
                    {
                        goUI.AddAlert("Project Budgetary Quote Alert - " + sProjectName + " ", clC.SELL_ALT_OPENRECORD, sRecordId, sCurrentUser, "Project16.GIF");
                    }
                    if (dtQ90.Date == DateTime.Today)
                    {
                        goUI.AddAlert("Project Close Alert - " + sProjectName + " ", clC.SELL_ALT_OPENRECORD, sRecordId, sCurrentUser, "Project16.GIF");
                    }
                    if (dtQ20.Date == DateTime.Today)
                    {
                        goUI.AddAlert("Project Formal Quote Alert - " + sProjectName + " ", clC.SELL_ALT_OPENRECORD, sRecordId, sCurrentUser, "Project16.GIF");
                    }
                    if (dtQ30.Date == DateTime.Today)
                    {
                        goUI.AddAlert("Project Preliminary Drawings Alert - " + sProjectName + " ", clC.SELL_ALT_OPENRECORD, sRecordId, sCurrentUser, "Project16.GIF");
                    }
                    if (dtQ40.Date == DateTime.Today)
                    {
                        goUI.AddAlert("Project Certified Drawings Alert - " + sProjectName + " ", clC.SELL_ALT_OPENRECORD, sRecordId, sCurrentUser, "Project16.GIF");
                    }
                    if (dtQ50.Date == DateTime.Today)
                    {
                        goUI.AddAlert("Project Ship Date Alert - " + sProjectName + " ", clC.SELL_ALT_OPENRECORD, sRecordId, sCurrentUser, "Project16.GIF");
                    }

                    if (rsPRs.GetNext() == 0)
                        bMore = false;
                }

                goLog.Log("Project_Timing_Alerts", "Completed", clC.SELL_LOGLEVEL_DEBUG, false, false, 1);

                return true;
            }
            catch (Exception ex)
            {
                goLog.Log("Project_Timing_Alerts", "Error - " + ex.ToString(), clC.SELL_LOGLEVEL_DEBUG, false, false, 1);

                return true;
            }
        }

        public bool PR_ViewControlOnChange_BTN_PRTimingAlertsTest_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Project_Timing_Alerts();


            return true;

        }

        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool CalcQuoteTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool CN_FormControlOnChange_LNK_RELATED_CO_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //TLD 9/22/2011 Auto fill TXT_SiteName

            Form doForm = (Form)par_doCallingObject;

            //TLD 9/22/2011 Always replace
            //Clear in case Company has no site name?
            doForm.doRS.SetFieldVal("TXT_SiteName", "");
            doForm.doRS.SetFieldVal("TXT_SiteName", doForm.doRS.GetFieldVal("LNK_Related_CO%%TXT_SiteName"));


            //VS 06142016 TKT#1111 : Do not prepend the Company Name to New Contact Address
            par_bRunNext = false;

            string sWork = null;
            //Dim bFillAddress As Boolean
            string sCustNameL = null;
            string sStreet = null;
            int lWork = 0;
            bool bAddressBlank = false;

            goErr.SetError();

            //CS 6/25/07: Per CD and PJ, only write values if the value is blank; don't overwrite what is there

            if (doForm.doRS.GetLinkCount("LNK_Related_CO") < 1)
            {
                doForm.MoveToField("LNK_Related_CO");
                return false;
            }

            //CS 6/19/09: Change technique to optimize
            clRowSet doCompany = new clRowSet("CO", 3, "GID_ID='" + ((doForm.doRS.GetFieldVal("LNK_RELATED_CO") == null) ? "" : doForm.doRS.GetFieldVal("LNK_RELATED_CO").ToString()) + "'", "", "TXT_COMPANYNAME,TEL_PHONENO,TEL_FAXNO,URL_WEBPAGE,TXT_ADDRMAILING,TXT_CITYMAILING,TXT_STATEMAILING,TXT_ZIPMAILING,TXT_COUNTRYMAILING,LNK_RELATED_IU");
            if (doCompany.GetFirst() != 1)
            {
                return false;
            }
            string TXT_COMPANYNAMETEXT = (doForm.doRS.GetFieldVal("TXT_COMPANYNAMETEXT") == null) ? "" : doForm.doRS.GetFieldVal("TXT_COMPANYNAMETEXT").ToString();
            if (string.IsNullOrEmpty(Strings.Trim(TXT_COMPANYNAMETEXT)))
            {
                doForm.doRS.SetFieldVal("TXT_COMPANYNAMETEXT", doCompany.GetFieldVal("TXT_COMPANYNAME"));
            }
            string TEL_BUSPHONE = (doForm.doRS.GetFieldVal("TEL_BUSPHONE") == null) ? "" : doForm.doRS.GetFieldVal("TEL_BUSPHONE").ToString();
            if (string.IsNullOrEmpty(Strings.Trim(TEL_BUSPHONE)))
            {
                doForm.doRS.SetFieldVal("TEL_BUSPHONE", doCompany.GetFieldVal("TEL_PHONENO"));
            }
            string TEL_FAX = (doForm.doRS.GetFieldVal("TEL_FAX") == null) ? "" : doForm.doRS.GetFieldVal("TEL_FAX").ToString();
            if (string.IsNullOrEmpty(Strings.Trim(TEL_FAX)))
            {
                doForm.doRS.SetFieldVal("TEL_FAX", doCompany.GetFieldVal("TEL_FAXNO"));
            }

            //Filling Main Phone is disabled because the Palm Pilot link is limited to 5 phone no's
            //including e-mail. Main Phone syncing reduces the number of available phone numbers.
            //'Main Phone' is generally the same as the Contact's 'Bus Phone'.
            string URL_WEB = (doForm.doRS.GetFieldVal("URL_WEB") == null) ? "" : doForm.doRS.GetFieldVal("URL_WEB").ToString();
            if (string.IsNullOrEmpty(Strings.Trim(URL_WEB)))
            {
                doForm.doRS.SetFieldVal("URL_WEB", doCompany.GetFieldVal("URL_WEBPAGE"));
            }

            sCustNameL = (doCompany.GetFieldVal("TXT_COMPANYNAME") == null) ? "" : doCompany.GetFieldVal("TXT_COMPANYNAME").ToString();
            sStreet = (doCompany.GetFieldVal("TXT_ADDRMAILING") == null) ? "" : doCompany.GetFieldVal("TXT_ADDRMAILING").ToString();
            //Remove the Company name from Company's address, if on first line
            if (!string.IsNullOrEmpty(sStreet))
            {
                lWork = Strings.InStr(sStreet, Constants.vbCrLf);
                if (lWork > 0)
                {
                    sWork = Strings.Mid(sStreet, 1, lWork - 1);
                    if (sWork == sCustNameL)
                    {
                        sStreet = goTR.FromTo(sStreet, lWork + 2);
                    }
                }
                string TXT_ADDRBUSINESS = (doForm.doRS.GetFieldVal("TXT_ADDRBUSINESS") == null) ? "" : doForm.doRS.GetFieldVal("TXT_ADDRBUSINESS").ToString();
                if (string.IsNullOrEmpty(TXT_ADDRBUSINESS))
                {
                    bAddressBlank = true;
                    //VS 06142016 TKT#1111
                    //doForm.doRS.SetFieldVal("TXT_ADDRBUSINESS", sCustNameL & vbCrLf & sStreet)
                    doForm.doRS.SetFieldVal("TXT_ADDRBUSINESS", sStreet);
                }
            }

            if (bAddressBlank == true)
            {
                doForm.doRS.SetFieldVal("TXT_CITYBUSINESS", doCompany.GetFieldVal("TXT_CITYMAILING"));
                doForm.doRS.SetFieldVal("TXT_STATEBUSINESS", doCompany.GetFieldVal("TXT_STATEMAILING"));
                doForm.doRS.SetFieldVal("TXT_ZIPBUSINESS", doCompany.GetFieldVal("TXT_ZIPMAILING"));
                doForm.doRS.SetFieldVal("TXT_COUNTRYBUSINESS", doCompany.GetFieldVal("TXT_COUNTRYMAILING"));
                bAddressBlank = false;
            }


            //Connect Related Industry Code from Related Company
            doForm.doRS.SetFieldVal("LNK_RELATED_IU", doCompany.GetFieldVal("LNK_RELATED_IU"));
            par_doCallingObject = doForm;
            return true;

        }
        public bool CN_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //TLD 9/30/2011 Disable merge -- if user checks manually,
            //merge does not work correctly -- this also in FRM MD
            //but save of form overwrites that
            doForm.SetControlState("CHK_Merged", 4);

            par_doCallingObject = doForm;

            return true;

        }
        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //TLD 9/22/2011 Click cancel on merge
            if (doForm.oVar.GetVar("CancelSave") != null && doForm.oVar.GetVar("CancelSave").ToString() == "1")
            {
                doForm.oVar.SetVar("CN_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");

                par_doCallingObject = doForm;
                return false;
            }

            return true;

        }
        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //---------------Merge
            //TLD 9/20/2012 Updated to use generic messagebox event
            //TLD 7/17/2012 Added to prevent user from merging CN to itself
            //TLD 9/22/2011 Merge Functionality - run at end of CN_FormOnSave_Post
            //If doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0 Then
            //    If doForm.doRS.GetFieldVal("CHK_Merged", 2) = 0 Then
            //        If doForm.oVar.GetVar("CN_Merge") <> "1" Then
            //            'Don't allow merge of contact to itself
            //            If doForm.doRS.GetFieldVal("GID_ID") = doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID") Then
            //                doForm.MessageBox("You cannot merge a contact to itself.  Please select a different merge to contact.", clC.SELL_MB_OK, , , , , , "MessageBoxEvent", , , doForm, , "OK", , , , "MergeCNFail")
            //            Else
            //                doForm.MessageBox("This contact will be merged to the target contact, '" & doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") & "'. Blank fields on the target contact will be filled from this contact record and all links will be copied to the target contact. Are you sure you want to merge this contact?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "MergeCN")
            //            End If
            //        End If
            //    End If
            //End If
            //SGR TKT:#1091 05162015Commented below lines of code
            //If doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0 Then
            //    If doForm.doRS.GetFieldVal("CHK_Merged", 2) = 0 Then
            //        If doForm.oVar.GetVar("CN_Merge") <> "1" Then
            //            'TLD 11/9/2011 Don't allow merge of contact to itself
            //            If doForm.doRS.GetFieldVal("GID_ID") = doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID") Then
            //                doForm.MessageBox("You cannot merge a contact to itself.  Please select a different merge to contact.", clC.SELL_MB_OK, , , , , , "MessageBoxEvent", , , doForm, , "OK", , , "CN", "MergeFail")
            //            Else
            //                doForm.MessageBox("This contact will be merged to the target contact, '" & doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") & "'. Blank fields on the target contact will be filled from this contact record and all links will be copied to the target contact. Are you sure you want to merge this contact?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", "CN", "Merge")
            //            End If
            //        End If
            //    End If
            //End If

            //SGR TKT:#1091 05162015 Updated for generic messagebox, Merge Functionality - run at end of   CN_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (doForm.doRS.GetFieldVal("CHK_Merged", 2) != null)
                {
                    if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                    {
                        if (doForm.oVar.GetVar("CN_Merge") != null && doForm.oVar.GetVar("CN_Merge").ToString() != "1")
                        {
                            //Don't allow merge of contact to itself
                            string GID_ID = (doForm.doRS.GetFieldVal("GID_ID") == null) ? null : doForm.doRS.GetFieldVal("GID_ID").ToString();
                            string LNK_Mergedto_CN = (doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID") == null) ? null : doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID").ToString();
                            if (GID_ID == LNK_Mergedto_CN)
                            {
                                doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CN", "MergeFail");
                            }
                            else
                            {
                                doForm.MessageBox("This record will be merged to the target record, '" + ((doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name") == null) ? "" : doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name").ToString()) + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CN", "Merge");
                            }
                        }
                    }
                }
            }
            //---------------End Merge

            return true;

        }
        public bool CN_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool CN_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            //TLD 9/22/2011 Always replace
            //Clear in case Company has no site name?
            doRS.SetFieldVal("TXT_SiteName", "");
            doRS.SetFieldVal("TXT_SiteName", doRS.GetFieldVal("LNK_Related_CO%%TXT_SiteName"));

            par_doCallingObject = doRS;

            return true;

        }
        public bool CO_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //TLD 9/30/2011 Disable merge -- if user checks manually,
            //merge does not work correctly -- this also in FRM MD
            //but save of form overwrites that
            doForm.SetControlState("CHK_Merged", 4);

            par_doCallingObject = doForm;

            return true;

        }
        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //TLD 9/22/2011 Click cancel on merge
            if (doForm.oVar.GetVar("CancelSave") != null && doForm.oVar.GetVar("CancelSave").ToString() == "1")
            {
                doForm.oVar.SetVar("CO_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");

                par_doCallingObject = doForm;
                return false;
            }

            return true;

        }
        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //----------------Merge
            //TLD 9/20/2012 Updated to use generic messagebox event
            //TLD 7/17/2012 Prevent user from merging CO to itself
            //TLD 9/30/2011 Oops!  Copied wrong script, here's the
            //correct stuff
            //Merge Functionality - run at end of CO_FormOnSave_Post
            //If doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0 Then
            //    If doForm.doRS.GetFieldVal("CHK_Merged", 2) = 0 Then
            //        If doForm.oVar.GetVar("CO_Merge") <> "1" Then
            //            'Don't allow merge of company to itself
            //            If doForm.doRS.GetFieldVal("GID_ID") = doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID") Then
            //                doForm.MessageBox("You cannot merge a company to itself.  Please select a different merge to company.", clC.SELL_MB_OK, , , , , , "MessageBoxEvent", , , doForm, , "OK", , , , "MergeCOFail")
            //            Else
            //                doForm.MessageBox("This company will be merged to the target company, '" & doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") & "'. Blank fields on the target company will be filled from this company record and all links will be copied to the target company. Are you sure you want to merge this company?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", , "MergeCO")
            //            End If
            //        End If
            //    End If
            //End If
            //If doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0 Then
            //    If doForm.doRS.GetFieldVal("CHK_Merged", 2) = 0 Then
            //        If doForm.oVar.GetVar("CO_Merge") <> "1" Then
            //            'Don't allow merge of company to itself
            //            If doForm.doRS.GetFieldVal("GID_ID") = doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID") Then
            //                doForm.MessageBox("You cannot merge a company to itself.  Please select a different merge to company.", clC.SELL_MB_OK, , , , , , "MessageBoxEvent", , , doForm, , "OK", , , "CO", "MergeFail")
            //            Else
            //                doForm.MessageBox("This company will be merged to the target company, '" & doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") & "'. Blank fields on the target company will be filled from this company record and all links will be copied to the target company. Are you sure you want to merge this company?", clC.SELL_MB_YESNOCANCEL, , , , , , "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, , "YES", "NO", "CANCEL", "CO", "Merge")
            //            End If
            //        End If
            //    End If
            //End If

            //SGR TKT:#1091 05162016 Updated for generic messagebox, Merge Functionality - run at end of   CO_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (doForm.doRS.GetFieldVal("CHK_Merged", 2) != null)
                {
                    if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                    {
                        if (doForm.oVar.GetVar("CO_Merge") != null && doForm.oVar.GetVar("CO_Merge").ToString() != "1")
                        {
                            //Don't allow merge of company to itself
                            string GID_ID = (doForm.doRS.GetFieldVal("GID_ID") == null) ? null : doForm.doRS.GetFieldVal("GID_ID").ToString();
                            string LNK_Mergedto_CO_GIDID = (doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID") == null) ? "" : doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID").ToString();
                            if (GID_ID == LNK_Mergedto_CO_GIDID)
                            {
                                doForm.MessageBox("You cannot merge a record to itself.  Please select a different merge to record.", clC.SELL_MB_OK, "Selltis", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CO", "MergeFail");
                            }
                            else
                            {
                                doForm.MessageBox("This record will be merged to the target record, '" + ((doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name") == null) ? "" : doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name").ToString()) + "'. Blank fields on the target record will be filled from this record and all links will be copied to the target record. Are you sure you want to merge this record?", clC.SELL_MB_YESNOCANCEL, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CO", "Merge");
                            }
                        }
                    }
                }
            }
            //----------------End Merge

            return true;

        }
        public bool CO_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool CO_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            //TLD 7/18/2014 Added for Target Account Mgmt
            string sCurVol1 = "";
            string sPotVol1 = "";

            //TLD 7/18/2014 Set TXT_CurrAndPot
            string MLS_CURVOLUME = (doRS.GetFieldVal("MLS_CURVOLUME") == null) ? "" : doRS.GetFieldVal("MLS_CURVOLUME").ToString();
            sCurVol1 = Strings.Left(MLS_CURVOLUME, 1);
            string MLS_POTVOLUME = (doRS.GetFieldVal("MLS_POTVOLUME") == null) ? "" : doRS.GetFieldVal("MLS_POTVOLUME").ToString();
            sPotVol1 = Strings.Left(MLS_POTVOLUME, 1);
            //for now record a "Z" if it is make selection
            if (sCurVol1 == "<")
            {
                sCurVol1 = "Z";
            }
            //for now record a "Z" if it is make selection
            if (sPotVol1 == "<")
            {
                sPotVol1 = "Z";
            }

            //set field to cur & pot
            doRS.SetFieldVal("TXT_CURRANDPOT", sCurVol1 + sPotVol1);

            //SKO ******** TKT#1054 : Profile Matrix
            string sCurVol = "";
            string sPotVol = "";
            int iCurCount = (doRS.GetLinkCount("LNK_CurrentProduct_PD") == null) ? 0 : Convert.ToInt32(doRS.GetLinkCount("LNK_CurrentProduct_PD"));
            int iPotCount = (doRS.GetLinkCount("LNK_PotentialProduct_PD") == null) ? 0 : Convert.ToInt32(doRS.GetLinkCount("LNK_PotentialProduct_PD"));

            //---------TLD 7/24/2012 Target Account Matrix Profiling
            //Copy LNK_Current_PD to LNK_Potential_PD
            //doRS.SetFieldVal("LNK_PotentialProduct_PD", doRS.GetFieldVal("LNK_CurrentProduct_PD"))

            //Record total selections from LNK_Current_PD and LNK_Potential_PD
            doRS.SetFieldVal("INT_CURCOUNT", iCurCount, 2);
            doRS.SetFieldVal("INT_POTCOUNT", iPotCount, 2);

            //Calculate Potential Percentage & Potential Portfolio
            clRowSet doPDRS = new clRowSet("PD", 3, "", "", "GID_ID");
            int iPDCount = Convert.ToInt32(doPDRS.Count());
            if (iPDCount != 0)
            {
                if (iPotCount != 0)
                {
                    doRS.SetFieldVal("SR__POTENTIALPERC", (iCurCount / iPotCount) * 100, 2);
                }
                doRS.SetFieldVal("SR__POTENTIALPORTFOLIO", (iPotCount / iPDCount) * 100, 2);

                doRS.SetFieldVal("SR__ProdLinePot", (iPotCount - iCurCount), 2);
                string MLS_CURRENTVOLUME = (doRS.GetFieldVal("MLS_CURRENTVOLUME") == null) ? "" : doRS.GetFieldVal("MLS_CURRENTVOLUME").ToString();
                string MLS_POTENTIALVOLUME = (doRS.GetFieldVal("MLS_POTENTIALVOLUME") == null) ? "" : doRS.GetFieldVal("MLS_POTENTIALVOLUME").ToString();
                sCurVol = Strings.Left(MLS_CURRENTVOLUME, 1);
                sPotVol = Strings.Left(MLS_POTENTIALVOLUME, 1);
                //for now record a "Z" if it is make selection
                if (sCurVol == "<")
                {
                    sCurVol = "Z";
                }
                //for now record a "Z" if it is make selection
                if (sPotVol == "<")
                {
                    sPotVol = "Z";
                }

                //set field to cur & pot
                doRS.SetFieldVal("TXT_CURANDPOT", sCurVol + sPotVol);
            }

            //---------- Target Account -----------------
            //Set Product Potential Quadrant
            double rTotalPortfolio = (doRS.GetFieldVal("SR__POTENTIALPORTFOLIO", 2) == null) ? 0 : Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPORTFOLIO", 2));
            double rPotentialProduct = (doRS.GetFieldVal("SR__POTENTIALPERC", 2) == null) ? 0 : Convert.ToDouble(doRS.GetFieldVal("SR__POTENTIALPERC", 2));

            if (rTotalPortfolio >= 51 & rTotalPortfolio <= 100)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                {
                    //Set to 1
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "1");
                }

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                {
                    //Set to 3
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "3");
                }
            }

            if (rTotalPortfolio >= 0 & rTotalPortfolio <= 50)
            {
                if (rPotentialProduct >= 51 & rPotentialProduct <= 100)
                {
                    //Set to 2
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "2");
                }

                if (rPotentialProduct >= 0 & rPotentialProduct <= 50)
                {
                    //Set to 4
                    doRS.SetFieldVal("TXT_PRODPOTQUAD", "4");
                }
            }

            //Because COs are updated nightly to set custom
            //date fields, need to write to custom mod time and mod by fields
            //AutoCOUpdate does NOT run recordonsave
            doRS.SetFieldVal("TXT_CusModBy", goP.GetMe("CODE"));
            doRS.SetFieldVal("DTT_CusModTime", "Today|Now");
            //---------TLD 7/24/2012 End Target Account Matrix Profiling
            par_doCallingObject = doRS;
            return true;

        }
        public bool CN_FormControlOnChange_BTN_FillFromCO_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //TLD 9/22/2011 Always replace
            //Clear in case Company has no site name?
            doForm.doRS.SetFieldVal("TXT_SiteName", "");
            doForm.doRS.SetFieldVal("TXT_SiteName", doForm.doRS.GetFieldVal("LNK_Related_CO%%TXT_SiteName"));
            par_doCallingObject = doForm;
            return true;

        }
        public bool Contact_FillFromCompany_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: clRowset object containing the Contact record to be edited
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //PURPOSE:
            //       Enter Phone and Address information in Contact rowset fields from the linked Company rowset.
            //       If no Company is linked, this returns False. This doesn't Commit the Contact
            //       rowset, just runs SetFieldVals against it. The rowset is returned byRef via par_doCallingObject.
            //PARAMETERS USED:
            //       par_doCallingObject: clRowset object containing the Contact record
            //       par_doArray: Unused.
            //       par_oRSCO: NOT USED. Was renamed from par_s1, but this throws a type conversion error. par_s1
            //           must remain a string.
            //           Optional: Rowset of the Company from which to read the information.
            //           If sent, the rowset MUST be instantiated at least with the following fields: 
            //           "TXT_COMPANYNAME,TEL_PHONENO,TEL_FAXNO,URL_WEBPAGE,TXT_ADDRMAILING,TXT_CITYMAILING,TXT_STATEMAILING,TXT_COUNTRYMAILING,TXT_ZIPMAILING"
            //           If this parameter is Nothing, the CO will be evaluated from LNK_Related_CO in CN rowset.
            //RETURNS:
            //       True on success, False if the CN record has no linked CO.

            //MI 11/18/10 Removed support for par_oRSCO.
            //MI 11/12/10 Created.

            //VS 06142016 TKT#1111 : Do not prepend the Company Name to New Contact Address
            par_bRunNext = false;

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sWork = null;
            string sCustNameL = null;
            string sStreet = null;
            long lWork = 0;
            clRowSet doRSCO = default(clRowSet);

            //MI 11/18/10 Disabled par_oRSCO
            //        If par_oRSCO Is Nothing Then
            //Company rowset not passed, evaluate Company from the CN rowset
            //CS 6/19/09 Change how we are doing the below
            string LNK_RELATED_CO = (doRS.GetFieldVal("LNK_RELATED_CO") == null) ? "" : doRS.GetFieldVal("LNK_RELATED_CO").ToString();
            doRSCO = new clRowSet("CO", 3, "GID_ID='" + LNK_RELATED_CO + "'", "", "TXT_COMPANYNAME,TEL_PHONENO,TEL_FAXNO,URL_WEBPAGE,TXT_ADDRMAILING,TXT_CITYMAILING,TXT_STATEMAILING,TXT_COUNTRYMAILING,TXT_ZIPMAILING");
            if (doRSCO.GetFirst() != 1)
            {
                return false;
            }
            //Else
            //doRSCO = par_oRSCO
            //End If

            string TXT_COMPANYNAMETEXT = (doRS.GetFieldVal("TXT_COMPANYNAMETEXT") == null) ? "" : doRS.GetFieldVal("TXT_COMPANYNAMETEXT").ToString();
            if (string.IsNullOrEmpty(Strings.Trim(TXT_COMPANYNAMETEXT)))
            {
                doRS.SetFieldVal("TXT_COMPANYNAMETEXT", doRSCO.GetFieldVal("TXT_COMPANYNAME"));
            }
            else
            {
                sWork = (doRSCO.GetFieldVal("TXT_COMPANYNAME") == null) ? "" : doRSCO.GetFieldVal("TXT_COMPANYNAME").ToString();
                if (!string.IsNullOrEmpty(sWork))
                {
                    //And doForm.ovar.getvar("CN_FormControlOnChange_BTN_FillFromCO_NameChecked") <> "1" Then
                    if (sWork != Strings.Trim(TXT_COMPANYNAMETEXT))
                    {
                        doRS.SetFieldVal("TXT_COMPANYNAMETEXT", sWork);

                    }
                }
            }
            string TEL_BUSPHONE = (doRS.GetFieldVal("TEL_BUSPHONE") == null) ? "" : doRS.GetFieldVal("TEL_BUSPHONE").ToString();
            if (string.IsNullOrEmpty(Strings.Trim(TEL_BUSPHONE)))
            {
                doRS.SetFieldVal("TEL_BUSPHONE", doRSCO.GetFieldVal("TEL_PHONENO"));
            }
            else
            {

                sWork = (doRSCO.GetFieldVal("TEL_BUSPHONE") == null) ? "" : doRSCO.GetFieldVal("TEL_BUSPHONE").ToString();
                if (!string.IsNullOrEmpty(sWork))
                {
                    //And doForm.ovar.getvar("CN_FormControlOnChange_BTN_FillFromCO_BusPhoneChecked") <> "1" Then
                    if (sWork != Strings.Trim(TEL_BUSPHONE))
                    {
                        doRS.SetFieldVal("TEL_BUSPHONE", sWork);
                    }
                }
            }
            string TEL_FAX = (doRS.GetFieldVal("TEL_FAX") == null) ? "" : doRS.GetFieldVal("TEL_FAX").ToString();
            if (string.IsNullOrEmpty(Strings.Trim(TEL_FAX)))
            {
                doRS.SetFieldVal("TEL_FAX", doRSCO.GetFieldVal("TEL_FAXNO"));
            }
            else
            {
                sWork = (doRSCO.GetFieldVal("TEL_FAXNO") == null) ? "" : doRSCO.GetFieldVal("TEL_FAXNO").ToString();
                if (!string.IsNullOrEmpty(sWork))
                {
                    //And doForm.ovar.getvar(System.Reflection.MethodInfo.GetCurrentMethod().Name & "_FaxChecked") <> "1" Then
                    if (sWork != Strings.Trim(TEL_FAX))
                    {
                        doRS.SetFieldVal("TEL_FAX", sWork);
                    }
                }
            }
            //Filling Main Phone is disabled because the Palm Pilot link is limited to 5 phone no's
            //including e-mail. Main Phone syncing reduces the number of available phone numbers.
            //'Main Phone' is generally the same as the Contact's 'Bus Phone'.
            string URL_WEB = (doRS.GetFieldVal("URL_WEB") == null) ? "" : doRS.GetFieldVal("URL_WEB").ToString();
            if (string.IsNullOrEmpty(Strings.Trim(URL_WEB)))
            {
                doRS.SetFieldVal("URL_WEB", doRSCO.GetFieldVal("URL_WEBPAGE"));
            }
            else
            {
                sWork = (doRSCO.GetFieldVal("URL_WEBPAGE") == null) ? "" : doRSCO.GetFieldVal("URL_WEBPAGE").ToString();
                if (!string.IsNullOrEmpty(sWork))
                {
                    //And doForm.ovar.getvar(System.Reflection.MethodInfo.GetCurrentMethod().Name & "_WebChecked") <> "1" Then
                    if (sWork != Strings.Trim(URL_WEB))
                    {
                        doRS.SetFieldVal("URL_WEB", sWork);
                    }
                }
            }

            sCustNameL = (doRSCO.GetFieldVal("TXT_COMPANYNAME") == null) ? "" : doRSCO.GetFieldVal("TXT_COMPANYNAME").ToString();
            sStreet = (doRSCO.GetFieldVal("TXT_ADDRMAILING") == null) ? "" : doRSCO.GetFieldVal("TXT_ADDRMAILING").ToString();
            //Remove the Company name from Company's address, if on first line
            if (!string.IsNullOrEmpty(sStreet))
            {
                lWork = Strings.InStr(sStreet, Constants.vbCrLf);
                if (lWork > 0)
                {
                    sWork = Strings.Mid(sStreet, 1, Convert.ToInt32(lWork) - 1);
                    if (sWork == sCustNameL)
                    {
                        sStreet = goTR.FromTo(sStreet, lWork + 2);
                    }
                }
                //VS 06142016 TKT#1111
                //par_doCallingObject.SetFieldVal("TXT_ADDRBUSINESS", sCustNameL & vbCrLf & sStreet)
                doRS.SetFieldVal("TXT_ADDRBUSINESS", sStreet);
            }

            doRS.SetFieldVal("TXT_CITYBUSINESS", doRSCO.GetFieldVal("TXT_CITYMAILING"));
            doRS.SetFieldVal("TXT_STATEBUSINESS", doRSCO.GetFieldVal("TXT_STATEMAILING"));
            doRS.SetFieldVal("TXT_ZIPBUSINESS", doRSCO.GetFieldVal("TXT_ZIPMAILING"));
            doRS.SetFieldVal("TXT_COUNTRYBUSINESS", doRSCO.GetFieldVal("TXT_COUNTRYMAILING"));

            par_doCallingObject = doRS;
            return true;
        }
        public bool FIND_FormControlOnChange_BTN_QLCancel_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //TLD 9/20/2012 Added for Quote Line Find
            par_bRunNext = false;

            Form oForm = (Form)par_doCallingObject;

            //Find dialog; Quote tab Cancel button
            //If click Cancel, close the form
            oForm.CloseOnReturn = true;

            return true;

        }
        public bool FIND_FormControlOnChange_BTN_QLSearch_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            //CS 8/17/09: Do NOT consider already saved filter of the desktop.
            //VS 06082016 TKT#1111 : Add CO and CreditedTo_US to find

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //TLD 9/20/2012 Added for Quote Line Find
            par_bRunNext = false;

            Form oForm = (Form)par_doCallingObject;

            //Find dialog; Quote Line tab Search button
            string sModel = "";
            //TLD 9/21/2012 Added for Details
            string sDetails = "";

            string sView = null;
            int iCondCount = 0;
            string sViewCondition = null;
            string sNewCondition = null;
            int iOrigCondCount = 0;
            int i = 0;
            string sFilter = "";
            string sCompany = "";
            string sCreditedTo = "";

            //Get values from form
            sModel = Strings.Trim(oForm.GetControlVal("NDB_TXT_MODEL"));
            //TLD 9/21/2012 Details
            sDetails = oForm.GetControlVal("NDB_MMO_DETAILS");
            //VS
            sCompany = Strings.Trim(oForm.GetControlVal("NDB_TXT_COMPANY"));
            if (!string.IsNullOrEmpty(sCompany))
            {
                sCompany = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCompany), "LNK_TO_CO%%SYS_NAME", "QL", true);
            }
            sCreditedTo = Strings.Trim(oForm.GetControlVal("NDB_TXT_CREDITEDTO"));
            if (!string.IsNullOrEmpty(sCreditedTo))
            {
                sCreditedTo = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCreditedTo), "LNK_CREDITEDTO_US%%SYS_NAME", "", true, false);
            }

            //Use values to filter Quote Line - Search Results desktop if it exists
            string Key = Guid.NewGuid().ToString();
            Desktop oDesktop = new Desktop("GLOBAL", "DSK_8C29FFB1-D2C0-4F94-5858-A0D201278DD9", false, Key);
            //Edit views in DT

            //View 1:Quote - Search Results
            sView = oDesktop.GetViewMetadata("VIE_04887E60-7F0A-407E-5858-A0D201278DD8");
            //iCondCount = goTR.StringToNum(goTR.StrRead(sView, "CCOUNT"))

            //'If CCOUNT=1 need to check if C1FIELDNAME=<%ALL%> b/c in that case I need to overwrite
            //If iCondCount = 1 Then
            //    If goTR.StrRead(sView, "C1FIELDNAME") = "<%ALL%>" Then
            //        iCondCount = 0 'Will overwrite these values
            //    End If
            //End If
            //Original condition count
            iOrigCondCount = iCondCount;

            //Only want to filter if the NDB fields contained a value
            //Get the total # of conditions
            if (!string.IsNullOrEmpty(sModel))
                iCondCount = iCondCount + 1;
            //TLD 9/21/2012 Details
            if (!string.IsNullOrEmpty(sDetails))
                iCondCount = iCondCount + 1;
            //VS
            if (!string.IsNullOrEmpty(sCompany))
                iCondCount = iCondCount + 1;
            if (!string.IsNullOrEmpty(sCreditedTo))
                iCondCount = iCondCount + 1;

            //Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            if (!string.IsNullOrEmpty(sModel))
            {
                //Add 'Model' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_MODEL%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "[");
                //contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sModel);
                i = i + 1;
                if (!string.IsNullOrEmpty(sFilter))
                {
                    sFilter = sFilter + " AND TXT_MODEL['" + sModel + "'";
                }
                else
                {
                    sFilter = "TXT_MODEL['" + sModel + "'";
                }
            }
            //TLD 9/21/2012 Details
            if (!string.IsNullOrEmpty(sDetails))
            {
                //Add 'Details' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MMO_DETAILS%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "[");
                //contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sDetails);
                i = i + 1;
                if (!string.IsNullOrEmpty(sFilter))
                {
                    sFilter = sFilter + " AND MMO_DETAILS['" + sDetails + "'";
                }
                else
                {
                    sFilter = "MMO_DETAILS['" + sDetails + "'";
                }
            }
            //VS 06082016
            if (!string.IsNullOrEmpty(sCompany))
            {
                //Add 'To Company' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_TO_CO%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "[");
                //contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCompany);
                i = i + 1;
                if (!string.IsNullOrEmpty(sFilter))
                {
                    sFilter = sFilter + " AND LNK_TO_CO%%SYS_NAME[" + sCompany + "";
                }
                else
                {
                    sFilter = "LNK_TO_CO%%SYS_NAME[" + sCompany + "";
                }
            }
            if (!string.IsNullOrEmpty(sCreditedTo))
            {
                //Add 'Credited To User' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_CREDITEDTO_US%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "[");
                //contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCreditedTo);
                i = i + 1;
                if (!string.IsNullOrEmpty(sFilter))
                {
                    sFilter = sFilter + " AND LNK_CREDITEDTO_US%%SYS_NAME[" + sCreditedTo + "";
                }
                else
                {
                    sFilter = "LNK_CREDITEDTO_US%%SYS_NAME[" + sCreditedTo + "";
                }
            }


            //Edit CONDITION= line in view MD
            //sViewCondition = goTR.StrRead(sView, "CONDITION")
            //If sViewCondition = "" Then
            sNewCondition = sFilter;
            //No filter in view already
            //Else
            //    sNewCondition = sViewCondition & " AND " & sFilter
            //End If
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            oDesktop.SetViewMetadata("VIE_04887E60-7F0A-407E-5858-A0D201278DD8", sView);

            //Load current view dataset with find filtertext..J
            SessionViewInfo _sessionViewInfo = new SessionViewInfo();
            _sessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + "VIE_04887E60-7F0A-407E-5858-A0D201278DD8");
            _sessionViewInfo.ViewMetaData = sView;
            _sessionViewInfo.ViewCondition = sNewCondition;
            Util.LoadViewData("VIE_04887E60-7F0A-407E-5858-A0D201278DD8", _sessionViewInfo.SortText, 1, "", false, Key);
            //update all the child views data sets
            Util.LoadViewDataSets(_sessionViewInfo.TopViewCount, _sessionViewInfo.TabViewCount, "VIE_04887E60-7F0A-407E-5858-A0D201278DD8", true, Key);

            oDesktop = new Desktop("GLOBAL", "DSK_8C29FFB1-D2C0-4F94-5858-A0D201278DD9", true, Key);
            sView = "";
            sViewCondition = "";
            sNewCondition = "";
            iCondCount = 0;


            //Que OP Search Results desktop
            goUI.Queue("DESKTOP", oDesktop);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));

            return true;

        }

        public bool FIND_FormControlOnChange_BTN_ACSearch_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            //CS 8/17/09: Do NOT consider already saved filter of the desktop.
            //VS 06082016 TKT#1111 : Add CO and CreditedTo_US to find

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //TLD 9/20/2012 Added for Quote Line Find
            par_bRunNext = false;

            Form oForm = (Form)par_doCallingObject;

            //Find dialog; Activity tab Search button
            string sNotes = null;
            string sRgaNo = null;
            string sCreditedToUser = null;
            string sType = null;
            string sPurpose = null;
            string sCorr = null;


            string sView = null;
            int iCondCount = 0;
            //Cs 8/17/09
            string sViewCondition = null;
            string sNewCondition = null;
            int iOrigCondCount = 0;
            int i = 0;
            string sFilter = "";

            //Get values from form
            sNotes = Strings.Trim(oForm.GetControlVal("NDB_TXT_NOTES"));
            if (!string.IsNullOrEmpty(sNotes))
            {
                sNotes = goTR.ConvertStringForQS(goTR.PrepareForSQL(sNotes), "MMO_NOTES", "AC", true);
            }
            sCreditedToUser = Strings.Trim(oForm.GetControlVal("NDB_TXT_CREDITEDTO"));
            if (!string.IsNullOrEmpty(sCreditedToUser))
            {
                sCreditedToUser = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCreditedToUser), "LNK_CREDITEDTO_US", "AC", true);
            }
            sType = oForm.GetControlVal("NDB_MLS_ACTYPE");
            sPurpose = oForm.GetControlVal("NDB_MLS_ACPURPOSE");
            sCorr = Strings.UCase(oForm.GetControlVal("NDB_CHK_CORR"));

            sRgaNo = Strings.Trim(oForm.GetControlVal("NDB_TXT_RGANO"));
            if (!string.IsNullOrEmpty(sRgaNo))
            {
                sRgaNo = goTR.ConvertStringForQS(goTR.PrepareForSQL(sRgaNo), "TXT_RGANO", "AC", true);
            }

            //Use values to filter Actvity - Search Results desktop if it exists
            string Key = Guid.NewGuid().ToString();
            Desktop oDesktop = new Desktop("GLOBAL", "DSK_3B850403-**************-9AF000EC8EF1", false, Key);
            //Edit views in DT

            //View 1:Activities - Search Results
            sView = oDesktop.GetViewMetadata("VIE_5E1489C4-5E83-4BB5-5858-9AF000EC8EF1");
            //iCondCount = goTR.StringToNum(goTR.StrRead(sView, "CCOUNT"))


            //If CCOUNT=1 need to check if C1FIELDNAME=<%ALL%> b/c in that case I need to overwrite
            //If iCondCount = 1 Then
            //    If goTR.StrRead(sView, "C1FIELDNAME") = "<%ALL%>" Then
            //        iCondCount = 0 'Will overwrite these values
            //    End If
            //End If
            //'Original condition count
            //iOrigCondCount = iCondCount

            //Only want to filter if the NDB fields contained a value
            //Get the total # of conditions            
            if (!string.IsNullOrEmpty(sNotes) & sNotes != "''")
                iCondCount = iCondCount + 1;
            if (!string.IsNullOrEmpty(sCreditedToUser))
                iCondCount = iCondCount + 1;
            if (sType != "0")
                iCondCount = iCondCount + 1;
            if (sPurpose != "0")
                iCondCount = iCondCount + 1;
            if (sCorr == "CHECKED")
                iCondCount = iCondCount + 2;
            if (!string.IsNullOrEmpty(sRgaNo) & sRgaNo != "''")
                iCondCount = iCondCount + 1;
            //corr rcvd and corr sent

            //Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            int par_iValid = 4;

            if (!string.IsNullOrEmpty(sNotes))
            {
                //Add 'Notes' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MMO_NOTES%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "[");
                //contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sNotes);
                i = i + 1;
                if (!string.IsNullOrEmpty(sFilter))
                {
                    sFilter = sFilter + " AND MMO_NOTES[" + sNotes + "";
                }
                else
                {
                    //sFilter = "MMO_NOTES['" & sNotes & "'"
                    sFilter = "MMO_NOTES[" + sNotes + "";
                }
            }
            if (!string.IsNullOrEmpty(sCreditedToUser))
            {
                //Add Credited to User Sys Name condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_CREDITEDTO_US%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "[");
                //contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCreditedToUser);
                i = i + 1;
                if (!string.IsNullOrEmpty(sFilter))
                {
                    sFilter = sFilter + " AND LNK_CREDITEDTO_US%%SYS_NAME[" + sCreditedToUser + "";
                }
                else
                {
                    //sFilter = "LNK_CREDITEDTO_US%%SYS_NAME['" & sCreditedToUser & "'"
                    sFilter = "LNK_CREDITEDTO_US%%SYS_NAME[" + sCreditedToUser + "";
                }
            }
            if (sType != "0")
            {
                //Add Type condition

                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MLS_TYPE%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "=");
                //Equals
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", goTR.StringToNum(sType, "", ref par_iValid));
                i = i + 1;
                if (!string.IsNullOrEmpty(sFilter))
                {
                    sFilter = sFilter + " AND MLS_TYPE=" + goTR.StringToNum(sType, "", ref par_iValid) + "";
                }
                else
                {
                    sFilter = "MLS_TYPE=" + goTR.StringToNum(sType, "", ref par_iValid) + "";
                }
            }
            if (sPurpose != "0")
            {
                //Add Purpose condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MLS_PURPOSE%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "=");
                //Equals
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", goTR.StringToNum(sPurpose, "", ref par_iValid));
                i = i + 1;
                if (!string.IsNullOrEmpty(sFilter))
                {
                    sFilter = sFilter + " AND MLS_PURPOSE=" + goTR.StringToNum(sPurpose, "", ref par_iValid) + "";
                }
                else
                {
                    sFilter = "MLS_PURPOSE=" + goTR.StringToNum(sPurpose, "", ref par_iValid) + "";
                }
            }
            if (sCorr == "CHECKED")
            {
                //Add Corr Received condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%CHK_CORRRECEIVED%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "1");
                //Equals 1
                //goTR.StrWrite(sView, "C" & i & "VALUE1", "1")
                goTR.StrWrite(ref sView, "C" + i + "PARENBEFORE", "(");
                goTR.StrWrite(ref sView, "C" + i + "KEYWORD", "OR");
                i = i + 1;
                //Add Corr Sent condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%CHK_CORRSENT%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "1");
                //Equals 1
                goTR.StrWrite(ref sView, "C" + i + "PARENAFTER", ")");

                //goTR.StrWrite(sView, "C" & i & "VALUE1", "1")
                i = i + 1;
                if (!string.IsNullOrEmpty(sFilter))
                {
                    sFilter = sFilter + " AND (CHK_CORRRECEIVED=1 OR CHK_CORRSENT=1)";
                }
                else
                {
                    sFilter = "(CHK_CORRRECEIVED=1 OR CHK_CORRSENT=1)";
                }
            }
            if (!string.IsNullOrEmpty(sRgaNo))
            {
                //Add 'Notes' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_RGANO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "[");
                //contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sRgaNo);
                i = i + 1;
                if (!string.IsNullOrEmpty(sFilter))
                {
                    sFilter = sFilter + " AND TXT_RGANO[" + sRgaNo + "";
                }
                else
                {
                    //sFilter = "MMO_NOTES['" & sRgaNo & "'"
                    sFilter = "TXT_RGANO[" + sRgaNo + "";
                }
            }


            //Edit CONDITION= line in view MD
            //sViewCondition = goTR.StrRead(sView, "CONDITION")
            //If sViewCondition = "" Then
            sNewCondition = sFilter;
            //No filter in view already
            //Else
            //sNewCondition = sViewCondition & " AND " & sFilter
            //End If
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            oDesktop.SetViewMetadata("VIE_5E1489C4-5E83-4BB5-5858-9AF000EC8EF1", sView);

            //Load current view dataset with find filtertext..J
            SessionViewInfo _sessionViewInfo = new SessionViewInfo();
            _sessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + "VIE_5E1489C4-5E83-4BB5-5858-9AF000EC8EF1");
            _sessionViewInfo.ViewMetaData = sView;
            _sessionViewInfo.ViewCondition = sNewCondition;
            Util.LoadViewData("VIE_5E1489C4-5E83-4BB5-5858-9AF000EC8EF1", _sessionViewInfo.SortText, 1, "", false, Key);
            //update all the child views data sets
            Util.LoadViewDataSets(_sessionViewInfo.TopViewCount, _sessionViewInfo.TabViewCount, "VIE_5E1489C4-5E83-4BB5-5858-9AF000EC8EF1", true, Key);

            oDesktop = new Desktop("GLOBAL", "DSK_3B850403-**************-9AF000EC8EF1", true, Key);
            sView = "";
            sViewCondition = "";
            sNewCondition = "";
            iCondCount = 0;

            //Que Activity Search Results desktop
            goUI.Queue("DESKTOP", oDesktop);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));

            par_doCallingObject = oForm;

            return true;

        }


        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFileName = "", string par_sReverseDirection = "0", string par_s3 = "NONE", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_sFileName: file for which to return the sort.
            //par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            //PURPOSE:
            //	Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            //       By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            //       add CASEs for them here. To not override the default sort, par_oReturn must be "".
            //       IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            //       with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            //       the sort likely should be ASC.
            //RETURNS:
            //		Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            //Select Case (par_sFileName)
            //    Case "AA"
            //        'This is a reverse sort, typically used for datetime fields
            //        If par_sReverseDirection = "1" Then
            //            sResult = "SYS_NAME ASC"
            //        Else
            //            sResult = "SYS_NAME DESC"
            //        End If
            //    Case "BB"
            //        'Reverse sort on Creation datetime
            //        If par_sReverseDirection = "1" Then
            //            sResult = "DTT_CREATIONTIME ASC"
            //        Else
            //            sResult = "DTT_CREATIONTIME DESC"
            //        End If
            //        'Case Else
            //        '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            //        '    'it is not needed here
            //End Select

            par_oReturn = sResult;

            return true;

        }
        public bool MergeRecord(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //TLD 9/22/2011 Added for merge

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;
            //Record being merged, will be deactivated
            clRowSet doRSMergeTo = default(clRowSet);
            //Good record, stays active

            clArray aFields = default(clArray);
            clArray aLinks = default(clArray);
            string sField = null;
            string sFieldType = null;
            clArray doLink = new clArray();
            string[] sLinkType = null;
            string sReturn = "";

            try
            {
                //Enumerate schema
                //aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                //aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                //Get mergeto record from rowset of merged record. User selects mergeto record on the form
                string LNK_MergedTo = (doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName()) == null) ? "" : doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName()).ToString();
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + LNK_MergedTo + "'", "", "**", -1, "", "", "", "", "", true, true, false, false, -1, "", true);

                if (doRSMergeTo.GetFirst() == 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Strings.Left(sField, 3);
                        string sFieldVal = (doRSMergeTo.GetFieldVal(sField) == null) ? "" : doRSMergeTo.GetFieldVal(sField).ToString();
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                if (string.IsNullOrEmpty(sFieldVal))
                                {
                                    doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                }
                                break;
                            case "MMO":
                                //Append
                                if (string.IsNullOrEmpty(sFieldVal))
                                {
                                    doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                }
                                else
                                {
                                    doRSMergeTo.SetFieldVal(sField, ((doRSMergeTo.GetFieldVal(sField) == null) ? "" : doRSMergeTo.GetFieldVal(sField).ToString()) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + ((doRSMerge.GetFieldVal("SYS_Name") == null) ? "" : doRSMerge.GetFieldVal("SYS_Name").ToString()) + " ==" + Constants.vbCrLf + ((doRSMerge.GetFieldVal(sField) == null) ? "" : doRSMerge.GetFieldVal(sField).ToString()));
                                }
                                break;
                            case "CHK":
                                if (doRSMergeTo.GetFieldVal(sField, 2) != null)
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    }
                                }
                                break;
                            case "MLS":
                                if (doRSMergeTo.GetFieldVal(sField, 2) != null)
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                    {
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    }
                                }
                                break;
                        }

                    }

                    for (long i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        //If NN link, copy all. If N1, copy only if blank
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Strings.Chr(9).ToString());
                        //NN OR Direction = 2 (backside of N1 or NN link should copy all)
                        if (sLinkType[4] == "NN" | sLinkType[1] == "2")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else
                        {
                            string aLinksVal = (doRSMergeTo.GetFieldVal(aLinks.GetItem(i)) == null) ? "" : doRSMergeTo.GetFieldVal(aLinks.GetItem(i)).ToString();
                            if (string.IsNullOrEmpty(aLinksVal))
                            {
                                oTable = null;
                                doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                                doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                            }
                        }
                    }

                    //Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    //Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);
                    //Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    //Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    //Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();

                    par_doCallingObject = doRSMerge;

                }

                sReturn = "Success";


            }
            catch (Exception ex)
            {
                sReturn = "Failed";

            }

            par_oReturn = sReturn;

            return true;

        }
        public bool MessageBoxEvent_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            //the user's response.
            //Par_s5 will always be the name of the script that called doform.MessageBox
            //Par_s1 will be whatever button the user clicked.
            //Par_s2-Par_s4 can be whatever else you want to pass.
            //In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            //After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc = null;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = null;
            Desktop doDesktop = null;

            //28022019 tckt #2691:user getting an error when he tries to click complete all while on the Corr Inbox - My dsk..J
            //Form doForm = (Form)par_doCallingObject;  //Commented this line and added below conditions to fix above issue..J
            if (par_s5.ToUpper().Contains("FORM"))
            {
                doForm = (Form)par_doCallingObject;
            }
            else if (par_s5.ToUpper().Contains("VIEW"))
            {
                doDesktop = (Desktop)par_doCallingObject;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
            }

            string sJournal = "";
            string sWork = "";
            int lWork = 0;

            switch (Strings.UCase(par_s5))
            {

                //TLD 9/28/2012 Need to prepend journal to notes field
                case "AC_FORMCONTROLONCHANGE_BTN_INSERTLINE":
                    switch (Strings.UCase(par_s1))
                    {
                        case "OK":
                            string JournalWithHardReturns = (doForm.oVar.GetVar("JournalWithHardReturns") == null) ? "" : doForm.oVar.GetVar("JournalWithHardReturns").ToString();
                            if (!string.IsNullOrEmpty(JournalWithHardReturns))
                            {
                                sWork = JournalWithHardReturns;
                                lWork = Strings.Len(sWork) - ((doForm.oVar.GetVar("lLenJournal") == null) ? 0 : Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")));
                                sWork = Strings.Left(sWork, lWork);
                                if (((doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS") == null) ? "" : doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS").ToString()) != "0")
                                {
                                    sWork = goTR.Replace(sWork, Constants.vbCrLf, (Strings.Chr(32) + Strings.Chr(32) + Strings.Chr(32)).ToString());
                                    doForm.doRS.SetFieldVal("MMO_NOTES", sWork + Constants.vbCrLf + ((doForm.doRS.GetFieldVal("MMO_Notes") == null) ? "" : doForm.doRS.GetFieldVal("MMO_Notes").ToString()));
                                }
                                else
                                {
                                    doForm.doRS.SetFieldVal("MMO_NOTES", sWork + Constants.vbCrLf + ((doForm.doRS.GetFieldVal("MMO_Notes") == null) ? "" : doForm.doRS.GetFieldVal("MMO_Notes").ToString()));
                                }
                            }
                            break;
                    }

                    break;
                //'TLD 9/20/2012 -------Commented, updated for generic event
                //'TLD 9/22/2011 CO Merge Record
                //Case "MERGECO"
                //    doForm.oVar.SetVar("CO_Merge", "1")
                //    Select Case UCase(par_s1)
                //        Case "YES"
                //            'run merge script, continue save
                //            goScr.RunScript("MergeRecord", doForm.doRS)

                //        Case "NO"
                //            'Clear merged to co linkbox, continue save
                //            doForm.doRS.ClearLinkAll("LNK_MergedTo_CO")

                //        Case "CANCEL"
                //            'Clear merged to co linkbox, cancel save
                //            doForm.doRS.ClearLinkAll("LNK_MergedTo_CO")
                //            doForm.oVar.SetVar("CancelSave", "1")
                //    End Select

                //    'TLD 12/17/2012 Added for Merge CO to itself
                //Case "MERGECOFAIL"
                //    doForm.oVar.SetVar("CO_Merge", "1")
                //    Select Case UCase(par_s1)
                //        Case "OK"
                //            'Clear merged to co linkbox, cancel save
                //            doForm.doRS.ClearLinkAll("LNK_MergedTo_CO")
                //            doForm.oVar.SetVar("CancelSave", "1")
                //    End Select

                //    'TLD 9/22/2011 CN Merge Record
                //Case "MERGECN"
                //    doForm.oVar.SetVar("CN_Merge", "1")
                //    Select Case UCase(par_s1)
                //        Case "YES"
                //            'run merge script, continue save
                //            goScr.RunScript("MergeRecord", doForm.doRS)

                //        Case "NO"
                //            'Clear merged to cn linkbox, continue save
                //            doForm.doRS.ClearLinkAll("LNK_MergedTo_CN")

                //        Case "CANCEL"
                //            'Clear merged to cn linkbox, cancel save
                //            doForm.doRS.ClearLinkAll("LNK_MergedTo_CN")
                //            doForm.oVar.SetVar("CancelSave", "1")
                //    End Select

                //    'TLD 12/17/2012 Added for Merge CN to itself
                //Case "MERGECNFAIL"
                //    doForm.oVar.SetVar("CN_Merge", "1")
                //    Select Case UCase(par_s1)
                //        Case "OK"
                //            'Clear merged to co linkbox, cancel save
                //            doForm.doRS.ClearLinkAll("LNK_MergedTo_CN")
                //            doForm.oVar.SetVar("CancelSave", "1")
                //    End Select
                //    'TLD 9/20/2012 -------End Commented, updated for generic event

                //For Merge records
                case "MERGE":
                    doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                    switch (Strings.UCase(par_s1))
                    {
                        case "YES":
                            //run merge script, continue save
                            par_doCallingObject = doForm.doRS;
                            bool runnext = true;
                            scriptManager.RunScript("MergeRecord", ref par_doCallingObject, ref par_oReturn, ref runnext, ref par_sSections);
                            doForm.doRS = (clRowSet)par_doCallingObject;

                            break;
                        case "NO":
                            //Clear merged to co linkbox, continue save
                            doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);

                            break;
                        case "CANCEL":
                            //Clear merged to co linkbox, cancel save
                            doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                            doForm.oVar.SetVar("CancelSave", "1");
                            break;
                    }

                    break;
                //Added for Merge record to itself
                case "MERGEFAIL":
                    doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                    switch (Strings.UCase(par_s1))
                    {
                        case "OK":
                            //Clear merged to co linkbox, cancel save
                            doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                            doForm.oVar.SetVar("CancelSave", "1");
                            break;
                    }
                    break;
            }

            //28022019 tckt #2691:user getting an error when he tries to click complete all while on the Corr Inbox - My dsk..J
            //par_doCallingObject = doForm;  //Commented this line and added below conditions to fix above issue..J
            if (par_s5.ToUpper().Contains("FORM"))
            {
                par_doCallingObject = doForm;
            }
            else if (par_s5.ToUpper().Contains("VIEW"))
            {
                par_doCallingObject = doDesktop;
            }
            else
            {
                par_doCallingObject = doForm;
            }

            return true;

        }
        public bool OP_FormControlOnChange_BTN_LINKCOMPANY_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //TLD 9/22/2011 Fill Site Name
            //Clear in case select CO has no site?
            doForm.doRS.SetFieldVal("TXT_SiteName", "");
            doForm.doRS.SetFieldVal("TXT_SiteName", doForm.doRS.GetFieldVal("LNK_For_CO%%TXT_SiteName"));
            par_doCallingObject = doForm;
            return true;

        }
        public bool OP_FormControlOnChange_LNK_FOR_CO_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //TLD 9/22/2011 Fill Site Name
            //Clear in case select CO has no site?
            doForm.doRS.SetFieldVal("TXT_SiteName", "");
            doForm.doRS.SetFieldVal("TXT_SiteName", doForm.doRS.GetFieldVal("LNK_For_CO%%TXT_SiteName"));
            par_doCallingObject = doForm;
            return true;

        }
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);

            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {
                
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }

            par_doCallingObject = doForm;

            return true;

        }
        public bool OP_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //Dim doForm As clForm = par_doCallingObject
            //'SKO ******** TKT#1054 : Profile Matrix
            //Dim iStage As Integer = doForm.doRS.GetFieldVal("MLS_STAGE", 2)
            //goP.SetVar("OppStageOnLoad", iStage)

            return true;

        }
        public bool OP_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool OP_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            //VS 02162016 TKT#957 : Profile Matrix
            // string sDateStamp = "";
            //Dim iStage As Integer = doForm.doRS.GetFieldVal("MLS_STAGE", 2)

            //CS 11/19/2014 Update stage dates if stage changed.
            //If goP.GetVar("OppStageOnLoad") <> goTR.NumToString(iStage) Then
            //Check what the stage is and update the date field with today/now
            doForm.doRS.SetFieldVal("DTT_OppDate", "Today|Now");

            par_doCallingObject = doForm;
            //Select Case iStage
            //    Case 10 'Lead
            //        doForm.doRS.SetFieldVal("DTT_LeadDate", "Today|Now")
            //    Case 20 'Opp
            //        doForm.doRS.SetFieldVal("DTT_OppDate", "Today|Now")
            //    Case 30 'Quote
            //        doForm.doRS.SetFieldVal("DTT_QuoteDate", "Today|Now")
            //End Select
            //End If

            return true;

        }
        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool OP_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            //TLD 9/22/2011 Always replace
            //Clear in case Company has no site name?
            doRS.SetFieldVal("TXT_SiteName", "");
            doRS.SetFieldVal("TXT_SiteName", doRS.GetFieldVal("LNK_For_CO%%TXT_SiteName"));
            par_doCallingObject = doRS;
            //SKO ******** TKT#1054 : Profile Matrix
            //Dim doRS As clRowSet = par_doCallingObject
            string sField = "";

            DateTime dDate = default(DateTime);
            //CS 11/19/2014 Update linked For Company.Last date or Involves Contact Last date if the opp stage changed
            //If doRS.GetInfo("TYPE") = "2" Then 'new op
            if (doRS.GetLinkCount("LNK_FOR_CO") > 0 | doRS.GetLinkCount("LNK_INVOLVES_CN") > 0)
            {
                //Determine what stage the Op on disk is compared to the Op we are saving. 
                //This will determine which Opp date field to set in the Company/Contact.
                clRowSet doRSOP = new clRowSet("OP", 3, "GID_ID='" + doRS.GetCurrentRecID() + "'");
                //opp already exists on disk
                if (doRSOP.GetFirst() == 1)
                {
                    //Dim iSavedStage As Integer = doRSOP.GetFieldVal("MLS_STAGE", 2)
                    //If iSavedStage <> doRS.GetFieldVal("MLS_STAGE", 2) Then
                    //Select Case doRS.GetFieldVal("MLS_STAGE", 2)
                    //    Case 10 'Lead
                    //        sField = "DTT_LASTLEAD" 'field to update in company/contact
                    //        dDate = doRS.GetFieldVal("DTT_LeadDate", 2) 'value to update company/contact
                    //    Case 20 'Opp
                    //        sField = "DTT_LASTOP"
                    //        dDate = doRS.GetFieldVal("DTT_OppDate", 2)
                    //    Case 30 'Quote
                    //        sField = "DTT_LASTQT"
                    //        dDate = doRS.GetFieldVal("DTT_QuoteDate", 2)
                    //    Case Else
                    //        'date did not change; do nothing
                    //        sField = ""
                    //End Select

                    sField = "DTT_LASTOP";
                    dDate = (doRS.GetFieldVal("DTT_OppDate", 2) == null) ? default(DateTime) : Convert.ToDateTime(doRS.GetFieldVal("DTT_OppDate", 2));
                    //End If
                }
                else
                {
                    //This is a new Opp; only set dates if not make selection
                    //Select Case doRS.GetFieldVal("MLS_STAGE", 2)
                    //    Case 10 'Lead
                    //        sField = "DTT_LASTLEAD" 'field to update in company/contact
                    //        dDate = doRS.GetFieldVal("DTT_LeadDate", 2) 'value to update company/contact
                    //    Case 20 'Opp
                    //        sField = "DTT_LASTOP"
                    //        dDate = doRS.GetFieldVal("DTT_OppDate", 2)
                    //    Case 30 'Quote
                    //        sField = "DTT_LASTQT"
                    //        dDate = doRS.GetFieldVal("DTT_QuoteDate", 2)
                    //    Case Else
                    //        'date did not change; do nothing
                    //        sField = ""
                    //End Select
                    sField = "DTT_LASTOP";
                    dDate = (doRS.GetFieldVal("DTT_OppDate", 2) == null) ? default(DateTime) : Convert.ToDateTime(doRS.GetFieldVal("DTT_OppDate", 2));

                }

                if (!string.IsNullOrEmpty(sField))
                {
                    //Update Companies
                    clArray doCompanies = new clArray();
                    doCompanies = (clArray)doRS.GetFieldVal("LNK_FOR_CO", 2);
                    //Bipass reconsave and validation to speed up AC save
                    for (int i = 1; i <= doCompanies.GetDimension(); i++)
                    {
                        clRowSet doRSCompany = new clRowSet("CO", 1, "GID_ID='" + doCompanies.GetItem(i) + "'", "", sField, -1, "", "", "", "", "", true, true);
                        if (doRSCompany.GetFirst() == 1)
                        {
                            doRSCompany.SetFieldVal(sField, dDate, 2);
                            //log error but proceed
                            if (doRSCompany.Commit() == 0)
                            {
                                goLog.Log(sProc, "CO update of last date field failed for CO " + ((doRSCompany.GetFieldVal("TXT_CompanyName") == null) ? "" : doRSCompany.GetFieldVal("TXT_CompanyName").ToString()) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                            }
                        }
                        doRSCompany = null;
                    }

                    //Update Contacts
                    clArray doContacts = new clArray();
                    doContacts = (clArray)doRS.GetFieldVal("LNK_INVOLVES_CN", 2);
                    //Bipass reconsave and validation to speed up AC save
                    for (int i = 1; i <= doContacts.GetDimension(); i++)
                    {
                        clRowSet doRSContact = new clRowSet("CN", 1, "GID_ID='" + doContacts.GetItem(i) + "'", "", sField, -1, "", "", "", "", "", true, true);
                        if (doRSContact.GetFirst() == 1)
                        {
                            doRSContact.SetFieldVal(sField, dDate, 2);
                            //log error but proceed
                            if (doRSContact.Commit() == 0)
                            {
                                goLog.Log(sProc, "CN update of last date field failed for CN " + ((doRSContact.GetFieldVal("TXT_NameLast") == null) ? "" : doRSContact.GetFieldVal("TXT_NameLast").ToString()) + " with error " + goErr.GetLastError("NUMBER") + ".", 1, false, true);
                            }
                        }
                        doRSContact = null;
                    }
                }
            }

            return true;

        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            return true;

        }
        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }
        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));   
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));          
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));



            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_FOR_MO,LNK_RELATED_PD,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);


            par_doCallingObject = doRS;

            return true;
        }



        public bool PR_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool PR_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool PR_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool PR_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool PR_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool PR_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool QL_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            
            return true;

        }
        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool QL_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool QL_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool QL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool QL_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool QT_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sQuoteNo = "";
            string sPOP = null;
            //Personal Options

            //goP.TraceLine("", "", sProc)

            //Set journal field locked
            doForm.SetControlState("MMO_Journal", 1);
            doForm.SetControlState("MMR_Journal", 1);
            doForm.SetControlState("MMO_History", 4);
            doForm.SetControlState("LNK_Connected_QL", 5);
            // 5=button grayed, 6=button invisible
            doForm.SetControlState("GID_ID", 4);
            doForm.SetControlState("NDB_MMO_Lines", 2);
            //2=invisible    '*** MI 4/25/07
            doForm.SetControlState("BTN_LineMod", 2);
            //2=invisible    '*** MI 4/25/07

            //7/5/07: Set checkbox allowing you update QL status to visible if WOP on
            if (doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QL_STATUSMGMT").ToString() == "1")
            {
                doForm.SetControlState("CHK_UPDQLSTATUS", 0);
            }
            else
            {
                doForm.SetControlState("CHK_UPDQLSTATUS", 2);
            }

            //If Save button is disabled, disable Send and Mod button
            if (doForm.SaveEnabled() == false)
            {
                doForm.SetControlState("BTN_CORR", 4);
            }

            if (doForm.GetMode() == "CREATION")
            {
                //It's faster to read multiple properties with goTr.StrRead from a page returned by gometa.PageRead
                //than using GetFieldVal("MTA_MEID%%PAGENAME%%PROPERTY") or goMeta.LineRead syntax for each property.
                sPOP = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS");
                //goP.TraceLine("GetMode is 'Creation', filling fields with default values from Personal Options", "", sProc)
                doForm.doRS.SetFieldVal("MMO_COVERLETTER", doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                doForm.doRS.SetFieldVal("MMO_CLOSING", doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_MESSAGE"));
                doForm.doRS.SetFieldVal("LNK_TAKENAT_LO", goTR.StrRead(sPOP, "IAMINLOCATION", "", false));
                doForm.doRS.SetFieldVal("LNK_RELATED_DM", goTR.StrRead(sPOP, "QUOTE_DELIVERY", "", false));
                doForm.doRS.SetFieldVal("CHK_INCLUDELETTERHEAD", goTR.StrRead(sPOP, "QUOTELETTERHEAD", "1", false));
                doForm.doRS.SetFieldVal("CHK_INCLUDETAXCHARGES", goTR.StrRead(sPOP, "QUOTETAX", "1", false));
                doForm.doRS.SetFieldVal("CHK_INCLUDETOTALS", goTR.StrRead(sPOP, "QUOTETOTALS", "1", false));
                doForm.doRS.SetFieldVal("CHK_INCLUDETERMS", goTR.StrRead(sPOP, "QUOTETERMS", "1", false));
                doForm.doRS.SetFieldVal("DTE_VALIDUNTILDATE", goTR.StrRead(sPOP, "QUOTEVALID", "30", false) + " days from today");
                doForm.doRS.SetFieldVal("DTE_NEXTACTIONDATE", goTR.StrRead(sPOP, "QUOTENEXTACTION", "14", false) + " days from today");
                doForm.doRS.SetFieldVal("TXT_FOB", goTR.StrRead(sPOP, "QUOTEFOB", "", true));
                doForm.doRS.SetFieldVal("TXT_PROPOSEDSHIPPING", goTR.StrRead(sPOP, "QUOTESHIPDATE", "", true));
                doForm.doRS.SetFieldVal("TXT_SHIPPINGTERMS", goTR.StrRead(sPOP, "QUOTESHIPTERMS", "", true));
                doForm.oVar.SetVar("lLenJournal", Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")));

                //CS 6/27/07, per DF and MI: Fill the Address,fax,email field if Originated by CN is filled
                if (doForm.doRS.GetLinkCount("LNK_OriginatedBY_CN") == 1)
                {
                    par_doCallingObject = doForm;
                    //QT_FormControlOnChange_BTN_FILLFROMCN_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                    doForm = (Form)par_doCallingObject;
                }


                //Generate the Quote # unless already generated (in Create Quote Revision script, for example)
                if (string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_QuoteNo").ToString()))
                {
                    if (!scriptManager.RunScript("Quote_GenerateQuoteNo", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", sQuoteNo))
                        return false;
                    doForm.doRS.SetFieldVal("TXT_QUOTENO", par_oReturn);// doForm.doRS.SetFieldVal("TXT_QUOTENO", sQuoteNo);
                    //par_bRunNext = true;
                }

                switch (doForm.oVar.GetVar("QuoteOpeningMode").ToString())
                {
                    case "Duplicate":
                    case "Revision":
                        doForm.SetControlState("CUR_TOTAL", 4);
                        //4=Grayed
                        doForm.SetControlState("CUR_SUBTOTAL", 4);
                        doForm.SetControlState("BTN_LINENEW", 2);
                        //2=invisible
                        break;
                    default:
                        doForm.SetControlState("CUR_TOTAL", 2);
                        doForm.SetControlState("CUR_SUBTOTAL", 2);
                        doForm.SetControlState("BTN_LINENEW", 0);
                        //0=Active
                        break;
                }
                doForm.SetControlState("BTN_LINEEDIT", 2);
                doForm.SetControlState("BTN_LINEDELETE", 2);
                doForm.SetControlState("BTN_LINEDUPLICATE", 2);
                doForm.SetControlState("BTN_RECALC", 2);

                //CS: Fill signature since no longer in ControlOnEnter
                object dors = doForm.doRS;
                scriptManager.RunScript("FillSignature", ref dors, ref par_oReturn, ref par_bRunNext, ref par_sSections);

                //CS 8/4/08
                //Check if this Quote was created as a result of a Create Linked from an OP.
                //copy OP MMO_Journal to Quote MMO_Journal when the QT is created from the OP
                string sID = doForm.GetCreateLinkedSourceRecSUID;
                //If the ID is not blank, find out if coming from an AC
                if (!string.IsNullOrEmpty(sID) & sID != null)
                {
                    if (goTR.GetFileFromSUID(sID) == "OP")
                    {
                        clRowSet doRSOP = new clRowSet("OP", 3, "GID_ID='" + sID + "'", "", "GID_ID,MMO_JOURNAL");
                        if (doRSOP.GetFirst() == 1)
                        {
                            doForm.doRS.SetFieldVal("MMO_JOURNAL", doRSOP.GetFieldVal("MMO_JOURNAL"));
                        }
                    }
                    else if (goTR.GetFileFromSUID(sID) == "AC")
                    {
                        //If quote being created from a lead AC, copy AC Journal to QT journal
                        //CS 8/4/08
                        //Check if this AC is Lead
                        clRowSet doRSAC = new clRowSet("AC", 3, "GID_ID='" + sID + "'", "", "GID_ID,MMO_JOURNAL,MLS_PURPOSE");
                        if (doRSAC.GetFirst() == 1)
                        {
                            //Lead
                            if (Convert.ToInt32(doRSAC.GetFieldVal("MLS_PURPOSE", 2)) == 8)
                            {
                                doForm.doRS.SetFieldVal("MMO_JOURNAL", doRSAC.GetFieldVal("MMO_JOURNAL"));
                            }
                        }
                        else
                        {
                            //Can't find Activity
                        }
                    }
                }

                //	doForm.MoveToTab(3)		'Details
            }
            else
            {
                //goP.TraceLine("GetMode is not 'Creation', filling variables.", "", sProc)
                doForm.oVar.SetVar("rSalesTaxEnterVal", doForm.doRS.GetFieldVal("SR__SALESTAXPERCENT", 2));
                //CS doForm.oVar.SetVar("lLenJournal", Len(doForm.dors.GetFieldVal("MMO_NOTES")))
                doForm.oVar.SetVar("lLenJournal", Strings.Len(doForm.doRS.GetFieldVal("MMO_JOURNAL")));

                //Gray out total and subtotal
                doForm.SetControlState("CUR_TOTAL", 4);
                doForm.SetControlState("CUR_SUBTOTAL", 4);

                //Hide label about saving Quote header in edit mode
                //doForm.setcontrolstate("NDB_LBL_NewQt", 2)


            }

            doForm.MoveToTab(1);
            //Lines

            doForm.oVar.SetVar("iStatus", doForm.doRS.GetFieldVal("MLS_STATUS", 2));

            //goP.TraceLine("Setting link property of Connected QuotLine to 'HeadingVisible'.", "", sProc)
            //CS: Not available yet. doForm.SetLinkProperty("LNK_CONNECTED_QL", "HeadingVisible", True)

            //----------- MANDATORY FIELDS -------------
            //goP.TraceLine("Starting to set the color of mandatory fields", "", sProc)
            string sColor = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_TO_CO", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_ORIGINATEDBY_CN", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_CREDITEDTO_US", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_PEER_US", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("LNK_TAKENAT_LO", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("DTE_VALIDUNTILDATE", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("DTE_TIME", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("TME_TIME", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("DTE_EXPCLOSEDATE", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("DTE_NEXTACTIONDATE", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("TXT_ProposedShipping", "LABELCOLOR", sColor);

            doForm.SetFieldProperty("LNK_CONNECTED_QL", "FONTNAMES", "Courier New,Courier");
            doForm.SetFieldProperty("LNK_CONNECTED_QL", "FONTSIZE", 8);
            doForm.SetFieldProperty("NDB_MMO_LINES", "FONTNAMES", "Courier New,Courier");
            doForm.SetFieldProperty("NDB_MMO_LINES", "FONTSIZE", 8);

            //Set button tooltips
            doForm.SetFieldProperties("BTN_LINKCOMPANY", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Link Company of the linked Contact");
            doForm.SetFieldProperties("BTN_LINENEW", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Add a new Quote Line to the Quote");
            doForm.SetFieldProperties("BTN_LINEEDIT", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Edit the selected Quote Line");
            doForm.SetFieldProperties("BTN_LINEDUPLICATE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Duplicate the selected Quote Line");
            doForm.SetFieldProperties("BTN_LINEDELETE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Delete the selected Quote Line");
            doForm.SetFieldProperties("BTN_RECALC", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Recalculate Quote totals");
            doForm.SetFieldProperties("BTN_INSERTLINE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend a line in the journal");
            doForm.SetFieldProperties("BTN_RECALCULATE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Recalculate totals");
            doForm.SetFieldProperties("BTN_INSERTDATE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend the date and time in the Cover Letter field");
            doForm.SetFieldProperties("BTN_INSERTTEXTBLOCK", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend text from the linked Document in the Cover Letter field");
            doForm.SetFieldProperties("BTN_INSERTDATE_1", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend the date and time in the Message field");
            doForm.SetFieldProperties("BTN_INSERTTEXTBLOCK_1", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend text from the linked Document in the Message field");
            doForm.SetFieldProperties("BTN_INSERTDATE_2", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend the date and time in the Notes field");
            doForm.SetFieldProperties("BTN_INSERTPRESALE", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Prepend the presale questions defined in Workgroup options in the Questionnaire field");
            doForm.SetFieldProperties("BTN_LINKCOMPANIES", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Link Companies of the linked Contacts");
            doForm.SetFieldProperties("BTN_CORR", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Send and close", "^^^^^");

            //Grayed fields
            doForm.SetControlState("MMO_ImportData", 4);
            doForm.SetControlState("MMO_SendData", 4);
            doForm.SetControlState("cur_subtotalt", 4);
            doForm.SetControlState("cur_salestax", 4);

            //----------------- DUPLICATION MODE --------------------    '*** MI 4/25/07
            switch (doForm.oVar.GetVar("QuoteOpeningMode").ToString())
            {
                case "Duplicate":
                case "Revision":
                    doForm.SetControlState("NDB_MMO_Lines", 1);
                    //1=Inactive
                    //doForm.SetControlState("LNK_Connected_QL", 2);
                    //2=invisible
                    //If save is grayed don't make this active
                    if (doForm.SaveEnabled() == true)
                    {
                        doForm.SetControlState("BTN_LineMod", 0);
                        //0=active
                    }
                    break;
            }

            //CS 7/29/09: Setting a variable that is used in QT RecONSave to let us know that we are coming from a form and not a rowset (ie code).
            //This is needed b/c when a QT is being edited via a rowset (that did not initiate from the form), we need to recalc the QT total when 
            //the QT is commited. QL RecOnSave is where calcing the quote total is done, but not in this case of just updating a QT and not QLs.
            //This var is reset at the end of QT_RecOnSave.
            goP.SetVar("OpenQTForm", "1");

            //RN 04/JUL/2017 set tabinfocus to zero 
            if (doForm.GetMode() == "CREATION")
            {
                doForm.MoveToTab(0);
            }
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            //doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            doForm.doRS.SetFieldVal("CHK_LINEInclude", "1", 2);
            par_bRunNext = false;
            par_doCallingObject = doForm;
            return true;

        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            //doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            switch (doForm.oVar.GetVar("QuoteOpeningMode").ToString())
            {
                case "Duplicate":
                case "Revision":
                    doForm.SetControlState("NDB_MMO_Lines", 1);
                    //1=Inactive
                    doForm.SetControlState("LNK_Connected_QL", 0);
                    //2=invisible
                    //If save is grayed don't make this active
                    if (doForm.SaveEnabled() == true)
                    {
                        doForm.SetControlState("BTN_LineMod", 0);
                        //0=active
                    }
                    break;
            }


            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);
            doForm.doRS.SetFieldVal("CHK_LINEInclude", "1", 2);
            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }

            return true;

        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //SKO 02/11/2016  Ticket#952 Please make this field requird on Save if: Status is "30 Won" or "40 Lost"
            //US_2=30 Won,US_3=40 Lost
            int MLS_Status = (doForm.doRS.GetFieldVal("MLS_STATUS", 2) == null) ? 0 : Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2));
            if (MLS_Status == 2 | MLS_Status == 3)
            {
                string DTE_CloseDate = (doForm.doRS.GetFieldVal("DTE_CloseDate", 1) == null) ? "" : doForm.doRS.GetFieldVal("DTE_CloseDate", 1).ToString();
                if (string.IsNullOrEmpty(DTE_CloseDate))
                {
                    doForm.MoveToField("DTE_CloseDate");
                    goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("DTE_CloseDate"), "", "", "", "", "", "", "", "", "DTE_CloseDate");
                    return false;
                }

            }

            return true;

        }
        public bool QT_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            if (doRS.oVar.GetVar("COMPLETEAC") != null && doRS.oVar.GetVar("COMPLETEAC").ToString() == "1")
            {
                //VS 07262016 TKT#1172 : Close the AC when creating a quote from AC create QT on Save.
                clRowSet doACRS = new clRowSet("AC", clC.SELL_EDIT, "GID_ID='" + doRS.oVar.GetVar("COMPLETEACRECORDID") + "'", "", "", -1, "", "", "", "", "", true, true);
                //Completed
                int MLS_Status = (doACRS.GetFieldVal("MLS_STATUS", 2) == null) ? 0 : Convert.ToInt32(doACRS.GetFieldVal("MLS_STATUS", 2));
                if (MLS_Status != 1)
                {
                    doACRS.SetFieldVal("MLS_STATUS", 1);
                    //Completed
                    doACRS.Commit();
                }
            }

            return true;

        }
        public bool Quote_FillAddress_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //TLD 9/28/2012 Prevent main from running
            //par_bRunNext = false;  //To fix the bug, when create a new link quote for contacts it skips the generatequote number function hence its commented..J

            Form doForm = (Form)par_doCallingObject;

            //goP.TraceLine("", "", sProc)

            //PURPOSE:
            //       TLD 9/28/2012 Do not fill country
            //		Fill the address field, first checking whether it is empty.
            //RETURNS:
            //		True

            string sContactName = "";
            string sMailingAddr = null;
            string sFirstName = null;
            string sLastName = null;
            //Dim sCompName as string
            string sAddrMail = null;
            string sCityMail = null;
            string sStateMail = null;
            string sZipMail = null;
            //TLD 9/28/2012 Commented, don't fill
            //Dim sCountryMail As String

            string TXT_ADDRESSMAILING = (doForm.doRS.GetFieldVal("TXT_ADDRESSMAILING") == null) ? "" : doForm.doRS.GetFieldVal("TXT_ADDRESSMAILING").ToString();
            if (!string.IsNullOrEmpty(TXT_ADDRESSMAILING))
                return true;
            if (doForm.doRS.GetLinkCount("LNK_ORIGINATEDBY_CN") < 1)
                return true;

            //CS 6/22/09: Create CN rowset to get CN fields
            clRowSet doRSContact = new clRowSet("CN", 3, "GID_ID='" + ((doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN") == null) ? "" : doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN").ToString()) + "'", "", "TXT_NAMEFIRST,TXT_NAMELAST,TXT_ADDRBUSINESS,TXT_CITYBUSINESS,TXT_STATEBUSINESS,TXT_ZIPBUSINESS,TXT_COUNTRYBUSINESS");
            if (doRSContact.GetFirst() == 1)
            {
                sFirstName = (doRSContact.GetFieldVal("TXT_NAMEFIRST") == null) ? "" : doRSContact.GetFieldVal("TXT_NAMEFIRST").ToString();
                sLastName = (doRSContact.GetFieldVal("TXT_NAMELAST") == null) ? "" : doRSContact.GetFieldVal("TXT_NAMELAST").ToString();
                if (!string.IsNullOrEmpty(sFirstName))
                {
                    sContactName = sFirstName + " ";
                }
                sContactName += sLastName;

                sAddrMail = (doRSContact.GetFieldVal("TXT_ADDRBUSINESS") == null) ? "" : doRSContact.GetFieldVal("TXT_ADDRBUSINESS").ToString();
                sCityMail = (doRSContact.GetFieldVal("TXT_CITYBUSINESS") == null) ? "" : doRSContact.GetFieldVal("TXT_CITYBUSINESS").ToString();
                sStateMail = (doRSContact.GetFieldVal("TXT_STATEBUSINESS") == null) ? "" : doRSContact.GetFieldVal("TXT_STATEBUSINESS").ToString();
                sZipMail = (doRSContact.GetFieldVal("TXT_ZIPBUSINESS") == null) ? "" : doRSContact.GetFieldVal("TXT_ZIPBUSINESS").ToString();
                //TLD 9/28/2012 Comment, don't fill
                //sCountryMail = doRSContact.GetFieldVal("TXT_COUNTRYBUSINESS")

                //Start building the mailing address
                sMailingAddr = sContactName;
                if (!string.IsNullOrEmpty(sAddrMail))
                {
                    sMailingAddr = sMailingAddr + Constants.vbCrLf + sAddrMail;
                }
                if (!string.IsNullOrEmpty(sCityMail))
                {
                    sMailingAddr = sMailingAddr + Constants.vbCrLf + sCityMail;
                }
                if (!string.IsNullOrEmpty(sStateMail))
                {
                    sMailingAddr = sMailingAddr + ", " + sStateMail;
                }
                if (!string.IsNullOrEmpty(sZipMail))
                {
                    sMailingAddr = sMailingAddr + " " + sZipMail;
                }
                //TLD 9/28/2012 Comment, dont' fill
                //If sCountryMail <> "" Then
                //    sMailingAddr = sMailingAddr & vbCrLf & sCountryMail
                //End If
                doForm.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailingAddr);
                par_doCallingObject = doForm;
            }

            return true;

        }
        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool Quotline_CalcTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool Quote_GenerateQuoteNo_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //MI 10/1/07 Changed now to goTR.NowUTC().
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            par_bRunNext = false;

            //PURPOSE:
            //		Generate the old Selltis-style Quote number (10 digits plus user code)
            //       On 10/1/07, MI changed 'now' to the UTC value, goTR.UTC_ServerToUTC(Now).
            //       This may cause the duplication of some ID numbers in existing databases.
            //RETURNS:
            //		Code number via return parameter.

            //VS 12032014 Ticket#1111: Generate Sequential Quote Number starting from 5000

            string sQuoteNo = "";
            int iQuoteNo = Convert.ToInt32(goMeta.LineRead("", "OTH_QuoteNoSequential", "NEXTQuoteNo"));
            //string iQuoteNoText = iQuoteNo.ToString().PadLeft(4, "0");
            string iQuoteNoText = goMeta.LineRead("", "OTH_QuoteNoSequential", "NEXTQuoteNo").PadLeft(4);
            sQuoteNo = iQuoteNoText + goP.GetUserCode();

            goMeta.LineWrite("", "OTH_QuoteNoSequential", "NEXTQuoteNo", iQuoteNo + 1, ref par_oConnection);

            par_oReturn = sQuoteNo;

            return true;

        }
        public bool SendEmails_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter in all rowsets to get all users' private recs
            //MI 10/19/07 started converting the script to be aware of the start of the day depending on the user's time zone
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: FileName (e.g., AC or OP)
            //par_s2: Team Leader User from CO
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DETAILS, true);

            Form doForm = (Form)par_doCallingObject;

            //TLD 9/28/2012

            //PURPOSE:
            //		Called from AC FormOnSave right now

            clRowSet doCNRS = null;
            string sSubject = "";
            string sCompany = "";
            string sContact = "";
            string sBody = "";
            clEmail oEmail = new clEmail();
            string sToEmail = "";
            string sFromEmail = "";
            clArray doLink = new clArray();
            string sJournal = "";
            string sDateStamp = "";

            //<NAME_EMAIL>
            //to avoid customer email server relay errors?
            sFromEmail = "<EMAIL>";

            //Get CO & CN RSs
            switch (par_s1)
            {
                case "AC":
                    //Notification Emails

                    //Check history to see if already sent for this record
                    string sHistory = (doForm.doRS.GetFieldVal("MMO_History") == null) ? "" : doForm.doRS.GetFieldVal("MMO_History").ToString();
                    //already ent
                    if (Strings.InStr(sHistory, "Notification Email Sent") > 0)
                    {
                        return true;
                    }

                    //Get To User from LNK_Notification_US
                    oTable = null;
                    doLink = doForm.doRS.GetLinkVal("LNK_Notification_US", ref doLink, true, 0, -1, "A_a", ref oTable);
                    for (int i = 1; i <= doLink.GetDimension(); i++)
                    {
                        if (string.IsNullOrEmpty(sToEmail))
                        {
                            sToEmail = (goData.GetFieldValueFromRec(doLink.GetItem(i), "EML_Email") == null) ? "" : goData.GetFieldValueFromRec(doLink.GetItem(i), "EML_Email").ToString();
                        }
                        else
                        {
                            sToEmail += "," + ((goData.GetFieldValueFromRec(doLink.GetItem(i), "EML_Email") == null) ? "" : goData.GetFieldValueFromRec(doLink.GetItem(i), "EML_Email").ToString());
                        }
                    }


                    if (string.IsNullOrEmpty(sToEmail))
                    {
                        goLog.Log(sProc, "Notification Email failed for Activity Log " + doForm.doRS.GetCurrentRecID() + ".  No email addresses for Notification Users.", 1, false, true);
                        return true;
                        //No email addresses
                    }

                    //Subject
                    sSubject = "Service Issue � " + ((doForm.doRS.GetFieldVal("TXT_NotificationSubj") == null) ? "" : doForm.doRS.GetFieldVal("TXT_NotificationSubj").ToString());

                    //Build contact string
                    oTable = null;
                    doLink = doForm.doRS.GetLinkVal("LNK_Related_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
                    for (int i = 1; i <= doLink.GetDimension(); i++)
                    {
                        doCNRS = new clRowSet("CN", 3, "GID_ID='" + doLink.GetItem(i) + "'", "", "TXT_NameFirst, TXT_NameLast, TEL_BusPhone, EML_Email");
                        if (doCNRS.GetFirst() == 1)
                        {
                            string TXT_NameFirst = (doCNRS.GetFieldVal("TXT_NameFirst") == null) ? "" : doCNRS.GetFieldVal("TXT_NameFirst").ToString();
                            string TXT_NameLast = (doCNRS.GetFieldVal("TXT_NameLast") == null) ? "" : doCNRS.GetFieldVal("TXT_NameLast").ToString();
                            string TEL_BusPhone = (doCNRS.GetFieldVal("TEL_BusPhone") == null) ? "" : doCNRS.GetFieldVal("TEL_BusPhone").ToString();
                            string EML_Email = (doCNRS.GetFieldVal("EML_Email") == null) ? "" : doCNRS.GetFieldVal("EML_Email").ToString();
                            if (string.IsNullOrEmpty(sContact))
                            {
                                sContact = TXT_NameFirst + " " + TXT_NameLast;
                                sContact += Constants.vbCrLf + TEL_BusPhone;
                                sContact += Constants.vbCrLf + EML_Email;
                            }
                            else
                            {
                                sContact += Constants.vbCrLf + Constants.vbCrLf + TXT_NameFirst + " " + TXT_NameLast;
                                sContact += Constants.vbCrLf + TEL_BusPhone;
                                sContact += Constants.vbCrLf + EML_Email;
                            }
                            doCNRS = null;
                        }
                    }


                    //Build company string
                    oTable = null;
                    doLink = doForm.doRS.GetLinkVal("LNK_Related_CO", ref doLink, true, 0, -1, "A_a", ref oTable);
                    for (int i = 1; i <= doLink.GetDimension(); i++)
                    {
                        if (string.IsNullOrEmpty(sCompany))
                        {
                            sCompany = (goData.GetFieldValueFromRec(doLink.GetItem(i), "TXT_CompanyName") == null) ? "" : goData.GetFieldValueFromRec(doLink.GetItem(i), "TXT_CompanyName").ToString();
                        }
                        else
                        {
                            sCompany += "," + ((goData.GetFieldValueFromRec(doLink.GetItem(i), "TXT_CompanyName") == null) ? "" : goData.GetFieldValueFromRec(doLink.GetItem(i), "TXT_CompanyName").ToString());
                        }
                    }


                    //Get journal entry to first carriage return
                    //should be latest journal entry
                    sJournal = (doForm.doRS.GetFieldVal("MMO_Journal") == null) ? "" : doForm.doRS.GetFieldVal("MMO_Journal").ToString();
                    int iPos = Strings.InStr(sJournal, Constants.vbCrLf);
                    if (iPos > 0)
                    {
                        sJournal = Strings.Left(sJournal, iPos - 1);
                    }

                    sBody = sContact + "<BR>" + sCompany + "<BR>" + sJournal + "<BR><BR>";
                    sBody += "Recorded by: " + ((doForm.doRS.GetFieldVal("LNK_CreditedTo_US%%TXT_NameFirst") == null) ? "" : doForm.doRS.GetFieldVal("LNK_CreditedTo_US%%TXT_NameFirst").ToString()) + " " + ((doForm.doRS.GetFieldVal("LNK_CreditedTo_US%%TXT_NameLast") == null) ? "" : doForm.doRS.GetFieldVal("LNK_CreditedTo_US%%TXT_NameLast").ToString());
                    sBody += "RGA #: " + ((doForm.doRS.GetFieldVal("TXT_RGANo") == null) ? "" : doForm.doRS.GetFieldVal("TXT_RGANo").ToString());

                    //Send Email to user
                    if (oEmail.SendSMTPEmail(sSubject, sBody, sToEmail, "", "", "", "Do Not Reply", sFromEmail, "", true) == true)
                    {
                        //'success, write to history, reset var
                        //goScr.RunScript("GetDateTimeStamp", doForm, , "NEUTRAL", , "CODE", "USERNOOFFSETLABEL", , sDateStamp);
                        par_doCallingObject = doForm;
                        par_oReturn = sDateStamp;
                        bool runnext = true;
                        scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref runnext, ref par_sSections, null, "NEUTRAL", "", "CODE", "USERNOOFFSETLABEL", "");
                        sDateStamp = par_oReturn.ToString();
                        //returns var sDateStamp
                        doForm.doRS.SetFieldVal("MMO_History", sDateStamp + " Notification Email Sent" + Constants.vbCrLf + sHistory);
                        doForm.oVar.SetVar("AC_NotificationEmailSent", "");
                        par_doCallingObject = doForm;
                    }
                    else
                    {
                        //Write to XL Log
                        goLog.Log(sProc, "AC Notification email send for '" + sSubject + ((doForm.doRS.GetFieldVal("GID_ID") == null) ? "" : doForm.doRS.GetFieldVal("GID_ID").ToString()) + " failed with error " + goErr.GetLastError("NUMBER") + "'", clC.SELL_LOGLEVEL_NONE, true);
                    }
                    //Clear variables
                    sFromEmail = "";
                    sContact = "";
                    sCompany = "";
                    sSubject = "";
                    sBody = "";
                    sDateStamp = "";
                    sHistory = "";
                    sJournal = "";
                    doLink = null;
                    break;
            }

            return true;

        }

        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            //double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));
            string sMODescription = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%MMO_SPECIFICATIONS"));
            string sModelText = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_DESCRIPTION"));


            //if (curUnitPrice <= 0)
            //{
            //    goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
            //    doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
            //    par_doCallingObject = doForm;
            //    return false;
            //}

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_ORIGINATEDBY_CN,LNK_FOR_MO,MMO_DETAILS,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            // rsQL.SetFieldVal("CUR_Cost", curCost);

            rsQL.SetFieldVal("SR__Qty", dQty);
            //rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);
            rsQL.SetFieldVal("MMO_DETAILS", sMODescription);

            rsQL.SetFieldVal("TXT_Model", sModelText);      
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");


            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }

        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
                

            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
            }

        }

        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", true);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }

        public bool QT_MoveQuoteLine_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            par_bRunNext = false;

            string sDirection = "";
            string sLtGt = "";
            string sFirstLast = "";
            string sMoveSortDir = "";

            string sRecID = "";
            string sQuoteID = "";
            string sSelLineNO = "";
            string sMoveLineNO = "";
            double dSelLineNO = 0;
            double dMoveLineNO = 0;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot edit Quote Lines.");
                return true;
            }

            if (par_s3.ToUpper() == "UP")
            {
                sDirection = "up";
                sLtGt = "<";
                sFirstLast = "first";
                sMoveSortDir = "desc";
            }
            else if (par_s3.ToUpper() == "DOWN")
            {
                sDirection = "down";
                sLtGt = ">";
                sFirstLast = "last";
                sMoveSortDir = "asc";
            }
            else
            {
                return false;
            }

            sQuoteID = doForm.doRS.GetFieldVal("GID_ID").ToString();
            sRecID = doForm.GetLinkSelection("LNK_Connected_QL");

            if (string.IsNullOrEmpty(sRecID))
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }

            clRowSet doQLSelRS = new clRowSet("QL", clC.SELL_EDIT, "GID_ID=" + sRecID, "", "*,**", 1, par_bBypassValidation: true);
            if (doQLSelRS.GetFirst() == 0)
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }
            sSelLineNO = doQLSelRS.GetFieldVal("SR__LineNo").ToString();
            dSelLineNO = Convert.ToDouble(doQLSelRS.GetFieldVal("SR__LineNo"));

            clRowSet doQLMoveRS = new clRowSet("QL", clC.SELL_EDIT, ("LNK_IN_QT=" + sQuoteID + " AND SR__LineNo" + sLtGt + sSelLineNO), "SR__LineNo " + sMoveSortDir, "*,**", 1, par_bBypassValidation: true);
            if (doQLMoveRS.GetFirst() == 0)
            {
                doForm.MessageBox("This is the " + sFirstLast + " Qoute Line. Please select another Qoute Line to move " + sDirection + ".");
                par_doCallingObject = doForm;
                return false;
            }
            sMoveLineNO = doQLMoveRS.GetFieldVal("SR__LineNo").ToString();
            dMoveLineNO = Convert.ToDouble(doQLMoveRS.GetFieldVal("SR__LineNo"));

            doQLSelRS.SetFieldVal("SR__LineNo", dMoveLineNO, 2);
            doQLMoveRS.SetFieldVal("SR__LineNo", dSelLineNO, 2);

            if (doQLSelRS.Commit() == 0 || doQLMoveRS.Commit() == 0)
            {
                doForm.MessageBox("Could not save the Quote Lines. Please reopen the quote and try again.");
                par_doCallingObject = doForm;
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");
            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;
        }
        public bool AutoQuoteDuplicate_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //MI 4/25/07 CREATED BY MI 4/22/07
            //PURPOSE:
            //       Duplicate an existing Quote and its line items allowing the user to connect a different
            //       Contact, Company, etc.

            string sID = null;
            clRowSet doRowset = default(clRowSet);
            string sFileName = null;
            Form doF = default(Form);
            string sOrigQuoteName = null;
            string sOrigQuoteID = null;

            //Check selected record
            //goUI - Not Implemented
            //sID = HttpContext.Current.Session["SelectedRecordID"].ToString(); //goUI.GetLastSelected("SELECTEDRECORDID");

            string Key = "";
            if (Util.GetSessionValue("LastDesktopHistoryKey") != null)
            {
                Key = Util.GetSessionValue("LastDesktopHistoryKey").ToString();
            }

            if (Util.GetSessionValue(Key + "_" + "SelectedRecordID") != null)
                sID = Util.GetSessionValue(Key + "_" + "SelectedRecordID").ToString();

            //goP.TraceLine("sID: " & sID & "'", "", sProc)
            sFileName = Strings.UCase(goTR.GetFileFromSUID(sID));
            if (sFileName != "QT")
            {
                //goUI - Not Implemented
                goUI.NewWorkareaMessage("Please select a Quote first.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            //Check if have permissions
            if (goData.GetAddPermission("QT") == false)
            {
                //goUI - Not Implemented
                goUI.NewWorkareaMessage("You cannot duplicate the selected Quote because you don't have permissions to create Quotes.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }


            //Copy the selected record
            //Get doRowset of current record
            doRowset = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + sID + "'", "", "*,LNK_CONNECTED_QL%%SYS_Name", 1);
            if (doRowset.Count() < 1)
            {
                //goUI - Not Implemented
                goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user. Select a different record and start again.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }
            else
            {
                sOrigQuoteName = doRowset.GetFieldVal("SYS_Name").ToString();
                sOrigQuoteID = doRowset.GetFieldVal("GID_ID").ToString();
            }

            //Create the new Quote form
            doF = new Form(sFileName, "", "CRU_" + sFileName);
            doF.oVar.SetVar("QuoteOpeningMode", "Duplicate");
            doF.oVar.SetVar("QuoteOrinalQuoteID", sID);
            //doF.SetControlVal("NDB_MMO_Lines", doRowset.GetFieldVal("LNK_Connected_QL%%SYS_Name").ToString());
            doF.doRS.SetFieldVal("TXT_Description", doRowset.GetFieldVal("TXT_Description", 2), 2);
            doF.doRS.SetFieldVal("CUR_Subtotal", doRowset.GetFieldVal("CUR_Subtotal", 2), 2);
            doF.doRS.SetFieldVal("CUR_Total", doRowset.GetFieldVal("CUR_Total", 2), 2);
            //Set History tab & Cloned from Quote
            doF.doRS.SetFieldVal("MMO_History", "Duplicated from Quote" + sOrigQuoteName);
            doF.doRS.SetLinkVal("LNK_ClonedFrom_QT", sOrigQuoteID);

            //get the lines from original quote
            //loop the lines'
            //create new line and add it to duplicated QT

            clRowSet rsQL = new clRowSet("QL", clC.SELL_READONLY, "LNK_IN_QT='" + sOrigQuoteID + "'", "", "*");
            for (int i = 1; i <= rsQL.Count(); i++)
            {
                clRowSet doNewQL = new clRowSet("QL" +
                    "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doF.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                doNewQL.SetFieldVal("LNK_FOR_MO", rsQL.GetFieldVal("LNK_FOR_MO", 2), 2);
                doNewQL.SetFieldVal("LNK_TO_CO", rsQL.GetFieldVal("LNK_TO_CO"));
                doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsQL.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsQL.GetFieldVal("LNK_CREDITEDTO_US"));
                doNewQL.SetFieldVal("LNK_INVOLVES_US", rsQL.GetFieldVal("LNK_INVOLVES_US"));

                doNewQL.SetFieldVal("MMO_DETAILS", rsQL.GetFieldVal("MMO_DETAILS"));
                doNewQL.SetFieldVal("TXT_MODEL", rsQL.GetFieldVal("TXT_MODEL"));

                if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                {
                    doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                }

                doNewQL.SetFieldVal("SR__LineNo", rsQL.GetFieldVal("SR__LineNo", 2), 2);
                doNewQL.SetFieldVal("SR__Qty", rsQL.GetFieldVal("SR__Qty", 2), 2);
                doNewQL.SetFieldVal("MLS_STATUS", rsQL.GetFieldVal("MLS_STATUS", 2), 2);

                doNewQL.SetFieldVal("Cur_COST", rsQL.GetFieldVal("Cur_COST", 2), 2);
                doNewQL.SetFieldVal("CUR_PriceUnit", rsQL.GetFieldVal("CUR_PriceUnit", 2), 2);
                doNewQL.SetFieldVal("CHK_Include", "1", 2);

                if (doNewQL.Commit() != 1)
                {
                    //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                    goErr.SetError(35000, "Duplicate Quote", "Error committing an add rowset for the new Quote Line.");
                    return false;
                }
                if (rsQL.GetNext() != 1)
                    break; // Exit For
            }

            // doF.doRS.SetFieldVal("LNK_CONNECTED_QL", doRowset.GetFieldVal("LNK_CONNECTED_QL", 2), 2);

            doF.MessagePanel("This is a duplicate of the Quote '" + sOrigQuoteName + "'." + Environment.NewLine + "Fill out the form and click Save & Leave Open to see the lines.", "#FFFFB0", "#000000", "Info.gif");

            //'Copy to the new rowset
            //If Not goData.CopyRecord(doRowset, doF.doRS) Then
            //    goErr.SetError(35000, sProc, "Copying the selected Quote '" & sID & "' failed.")
            //    Return False
            //End If
            //doF.doRS.SetFieldVal("GID_ID", goData.GenerateID(sFileName))
            //doF.doRS.ClearLinkAll("LNK_OriginatedBy_CN")
            //doF.doRS.ClearLinkAll("LNK_To_CO")

            //'Save the new record (?)
            //If doNewRowset.Commit() = 0 Then
            //    goErr.SetWarning(30200, sProc, "", "An error occurred duplicating the selected Quote.", "", "", "", "", "", "", "", "", "")
            //    doRowset = Nothing
            //    doNewRowset = Nothing
            //    Return False
            //End If

            doF.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doF.RefreshLinkNames("LNK_CONNECTED_QL");

            goUI.Queue("FORM", doF);

            //Clean up objects
            doRowset = null;
            par_bRunNext = false;
            par_doCallingObject = doF;
            return true;
        }

        public bool AutoQuoteCreateRevision_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //MI 1/24/08 Changed from 1 to 0 to make the first rev to be Rev 1 not 2.
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sID = null;
            clRowSet doRowset = default(clRowSet);
            string sFileName = null;
            Form doF = default(Form);
            clRowSet doAllQTs = default(clRowSet);
            string sQuoteNo = null;
            string sQuoteOnlyNo = null;
            //Quote number without the revision
            int iRevNo = 0;
            int iRevNoTemp = 0;
            int iLen = 0;
            int i = 0;
            int iPos = 0;
            string sQuoteTitleOnly = null;
            string sOrigQuoteName = null;
            string sOrigQuoteID = null;
            //Check selected record
            //goUI - Not Implemented
            //sID = goUI.GetLastSelected("SELECTEDRECORDID");

            string Key = "";
            if (Util.GetSessionValue("LastDesktopHistoryKey") != null)
            {
                Key = Util.GetSessionValue("LastDesktopHistoryKey").ToString();
            }

            if (Util.GetSessionValue(Key + "_" + "SelectedRecordID") != null)
                sID = Util.GetSessionValue(Key + "_" + "SelectedRecordID").ToString();

            Desktop doDesktop = (Desktop)par_doCallingObject;

            //string sessionUserId = HttpContext.Current.Session["SelectedRecordID"] as string;

            if (!string.IsNullOrEmpty(sID))
            {
                //sID = HttpContext.Current.Session["SelectedRecordID"].ToString();

                //goP.TraceLine("sID: " & sID & "'", "", sProc)
                sFileName = Strings.UCase(goTR.GetFileFromSUID(sID));
                if (sFileName != "QT")
                {
                    //goUI - Not Implemented
                    goUI.NewWorkareaMessage("Please select a Quote first.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, null);
                    //doDesktop.MessageBox(ref par_doCallingObject, "Please select a Quote first.");
                    return true;
                }

            }
            else
            {
                goUI.NewWorkareaMessage("Please select a Quote first.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }


            //Check if have permissions
            if (goData.GetAddPermission("QT") == false)
            {
                //goUI - Not Implemented
                goUI.NewWorkareaMessage("You cannot create a revision of the selected Quote because you don't have permissions to create Quotes.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            //Copy the selected record
            //Get doRowset of current record
            doRowset = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI - Not Implemented
                goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user. Select a different record and start again.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }

            //------------------- Update revision no in Quote No -------------------
            sQuoteNo = Strings.Trim(doRowset.GetFieldVal("TXT_QuoteNo").ToString());
            if (string.IsNullOrEmpty(sQuoteNo))
            {
                object callingObject = null;
                if (!scriptManager.RunScript("Quote_GenerateQuoteNo", ref callingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", sQuoteNo))
                {
                    return false;
                }
                else
                {
                    sQuoteNo = par_oReturn.ToString();
                }
            }
            if (string.IsNullOrEmpty(sQuoteNo))  //sQuoteNo
            {
                //Should never happen
                goErr.SetError(35000, sProc, "Quote number is blank.");
                return false;
            }

            sQuoteOnlyNo = sQuoteNo;// sQuoteNo;
            //Get the quote no without the revision number
            iPos = Strings.InStr(sQuoteOnlyNo, "-");
            if (iPos > 0)
            {
                sQuoteOnlyNo = Strings.Left(sQuoteOnlyNo, iPos - 1);
            }
            //Limit the length of the quote no just in case someone mangled it by hand
            iLen = Strings.Len(sQuoteOnlyNo);
            if (iLen > 14)
            {
                iLen = 14;
                sQuoteOnlyNo = Strings.Left(sQuoteOnlyNo, 14);
            }

            //Determine the highest revision no
            doAllQTs = new clRowSet(sFileName, clC.SELL_READONLY, "TXT_QuoteNo[='" + goTR.PrepareForSQL(sQuoteOnlyNo) + "'", "DTT_Time DESC", "TXT_QuoteNo");
            //MI 11/6/08 Added goTr.PrepareForSQL
            iRevNo = 0;
            //*** MI 1/24/08 Changed from 1 to 0 to make the first rev to be Rev 1 not 2.
            for (i = 1; i <= doAllQTs.Count(); i++)
            {
                sQuoteNo = doAllQTs.GetFieldVal("TXT_QuoteNO").ToString();
                iPos = Strings.InStr(sQuoteNo, "-");
                if (iPos > 0)
                {
                    //The number contains a revision no
                    int par_iValid = 4;
                    iRevNoTemp = Convert.ToInt32(goTR.StringToNum(Strings.Right(sQuoteNo, 3), "", ref par_iValid));
                    if (iRevNoTemp > iRevNo)
                    {
                        iRevNo = iRevNoTemp;
                    }
                }
                if (doAllQTs.GetNext() != 1)
                    break; // TODO: might not be correct. Was : Exit For
            }

            //Advance the revision number by 1, but not if revision is 1.
            iRevNo = iRevNo + 1;
            if (iRevNo > 999)
                iRevNo = 999;
            //Remove all spaces - there can be a space after the user code if shorter than 4 chars
            sQuoteNo = goTR.Replace(sQuoteOnlyNo, " ", "_");
            //'Ensure fixed length - commented because it makes numbers ugly with "_"
            //sQuoteNo = goTR.Pad(sQuoteNo, 14, "_")
            sQuoteNo = sQuoteOnlyNo + "-" + goTR.Pad(iRevNo.ToString(), 3, "0", "L");
            if ((doAllQTs != null))
                doAllQTs = null;

            //Create the new form
            doF = new Form(sFileName, "", "CRU_" + sFileName);

            //Copy this quote to the new form's rowset
            clRowSet dof_dors = doF.doRS;
            if (!goData.CopyRecord(ref doRowset, ref dof_dors))
            {
                goErr.SetError(35000, sProc, "Copying the selected Quote '" + sID + "' failed.");
                return false;
            }

            //doF.doRS.SetFieldVal("GID_ID", goData.GenerateID(sFileName))
            doF.doRS.SetFieldVal("LNK_CreatedBy_US", goP.GetUserTID());
            doF.doRS.SetFieldVal("DTT_Time", "Today|Now");
            //CS 8/3/07: Reason and Status of revised QT should be same as original QT
            //doF.doRS.SetFieldVal("MLS_Status", 0, clC.SELL_SYSTEM)    'Open
            //doF.doRS.SetFieldVal("MLS_ReasonWonLost", 0, clC.SELL_SYSTEM)    '<Make selection>
            doF.doRS.SetFieldVal("DTT_DateClosed", "");
            //Set History tab & Cloned From Qt
            doF.doRS.SetFieldVal("MMO_History", "Revision of Quote" + doRowset.GetFieldVal("SYS_Name"));
            doF.doRS.SetLinkVal("LNK_ClonedFrom_QT", sID);
            sQuoteTitleOnly = doRowset.GetFieldVal("TXT_QuoteTitle").ToString();
            iPos = Strings.InStr(sQuoteTitleOnly, " REV ");
            if (iPos > 0)
            {
                sQuoteTitleOnly = Strings.Left(sQuoteTitleOnly, iPos - 1);
            }
            doF.doRS.SetFieldVal("TXT_QuoteTitle", sQuoteTitleOnly + " REV " + iRevNo.ToString());
            doF.doRS.SetFieldVal("TXT_QuoteNo", sQuoteNo);
            doF.doRS.ClearLinkAll("LNK_Connected_QL");
            doF.doRS.SetFieldVal("LNK_CONNECTED_QL", doRowset.GetFieldVal("LNK_CONNECTED_QL", 2), 2);
            //doF.SetControlVal("NDB_MMO_Lines", doRowset.GetFieldVal("LNK_Connected_QL%%SYS_Name").ToString());
            //Lines will display as a memo
            doF.oVar.SetVar("QuoteOpeningMode", "Revision");
            doF.oVar.SetVar("QuoteOrinalQuoteID", doRowset.GetFieldVal("GID_ID"));

            doRowset = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + sID + "'", "", "*,LNK_CONNECTED_QL%%SYS_Name", 1);
            if (doRowset.Count() < 1)
            {
                //goUI - Not Implemented
                goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user. Select a different record and start again.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                return true;
            }
            else
            {
                sOrigQuoteName = doRowset.GetFieldVal("SYS_Name").ToString();
                sOrigQuoteID = doRowset.GetFieldVal("GID_ID").ToString();
            }

            //Create the new Quote form
            doF = new Form(sFileName, "", "CRU_" + sFileName);
            doF.oVar.SetVar("QuoteOpeningMode", "Revision");
            doF.oVar.SetVar("QuoteOrinalQuoteID", sID);
            //doF.SetControlVal("NDB_MMO_Lines", doRowset.GetFieldVal("LNK_Connected_QL%%SYS_Name").ToString());
            doF.doRS.SetFieldVal("TXT_Description", doRowset.GetFieldVal("TXT_Description", 2), 2);
            doF.doRS.SetFieldVal("CUR_Subtotal", doRowset.GetFieldVal("CUR_Subtotal", 2), 2);
            doF.doRS.SetFieldVal("CUR_Total", doRowset.GetFieldVal("CUR_Total", 2), 2);
            //Set History tab & Cloned from Quote
            doF.doRS.SetFieldVal("MMO_History", "Revision from Quote" + sOrigQuoteName);
            doF.doRS.SetLinkVal("LNK_ClonedFrom_QT", sOrigQuoteID);

            //get the lines from original quote
            //loop the lines'
            //create new line and add it to duplicated QT

            clRowSet rsQL = new clRowSet("QL", clC.SELL_READONLY, "LNK_IN_QT='" + sOrigQuoteID + "'", "", "*");
            for (int j = 1; j <= rsQL.Count(); j++)
            {
                clRowSet doNewQL = new clRowSet("QL" +
                    "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doF.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                doNewQL.SetFieldVal("LNK_FOR_MO", rsQL.GetFieldVal("LNK_FOR_MO", 2), 2);
                doNewQL.SetFieldVal("LNK_TO_CO", rsQL.GetFieldVal("LNK_TO_CO"));
                //doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsQL.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsQL.GetFieldVal("LNK_CREDITEDTO_US"));
                doNewQL.SetFieldVal("LNK_INVOLVES_US", rsQL.GetFieldVal("LNK_INVOLVES_US"));

                doNewQL.SetFieldVal("MMO_DETAILS", rsQL.GetFieldVal("MMO_DETAILS"));
                doNewQL.SetFieldVal("TXT_MODEL", rsQL.GetFieldVal("TXT_MODEL"));

                if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                {
                    doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                }

                doNewQL.SetFieldVal("SR__LineNo", rsQL.GetFieldVal("SR__LineNo", 2), 2);
                doNewQL.SetFieldVal("SR__Qty", rsQL.GetFieldVal("SR__Qty", 2), 2);
                doNewQL.SetFieldVal("MLS_STATUS", rsQL.GetFieldVal("MLS_STATUS", 2), 2);

                doNewQL.SetFieldVal("Cur_COST", rsQL.GetFieldVal("Cur_COST", 2), 2);
                doNewQL.SetFieldVal("CUR_PriceUnit", rsQL.GetFieldVal("CUR_PriceUnit", 2), 2);
                doNewQL.SetFieldVal("CHK_Include", "1", 2);

                if (doNewQL.Commit() != 1)
                {
                    //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                    goErr.SetError(35000, "Revision Quote", "Error committing an add rowset for the new Quote Line.");
                    return false;
                }
                if (rsQL.GetNext() != 1)
                    break; // Exit For
            }

            // doF.doRS.SetFieldVal("LNK_CONNECTED_QL", doRowset.GetFieldVal("LNK_CONNECTED_QL", 2), 2);

            doF.MessagePanel("This is a Revision of the Quote '" + sOrigQuoteName + "'." + Environment.NewLine + "Fill out the form and click Save & Leave Open to see the lines.", "#FFFFB0", "#000000", "Info.gif");

            //'Copy to the new rowset
            //If Not goData.CopyRecord(doRowset, doF.doRS) Then
            //    goErr.SetError(35000, sProc, "Copying the selected Quote '" & sID & "' failed.")
            //    Return False
            //End If
            //doF.doRS.SetFieldVal("GID_ID", goData.GenerateID(sFileName))
            //doF.doRS.ClearLinkAll("LNK_OriginatedBy_CN")
            //doF.doRS.ClearLinkAll("LNK_To_CO")

            //'Save the new record (?)
            //If doNewRowset.Commit() = 0 Then
            //    goErr.SetWarning(30200, sProc, "", "An error occurred duplicating the selected Quote.", "", "", "", "", "", "", "", "", "")
            //    doRowset = Nothing
            //    doNewRowset = Nothing
            //    Return False
            //End If

            doF.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doF.RefreshLinkNames("LNK_CONNECTED_QL");
            //doRowset = null;
            //par_bRunNext = false;
            //par_doCallingObject = doF;
            return true;
        }
        public bool TD_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool TD_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool TD_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool TD_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool TD_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }
        public bool TD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;

        }

        //public New()
        //{
        //}
        //protected override void Finalize()
        //{
        //    base.Finalize();
        //}
        //SGR TKT#:1121 06142016
        public bool AP_FormControlOnChange_BTN_PREPEND(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string snotes = string.Empty;
            string MMO_Notes = (doForm.doRS.GetFieldVal("MMO_NOTES", 2) == null) ? "" : doForm.doRS.GetFieldVal("MMO_NOTES", 2).ToString();
            if (!string.IsNullOrEmpty(MMO_Notes))
            {
                snotes = "Leads:" + Constants.vbCrLf + Constants.vbCrLf + "Purpose of Meeting:" + Constants.vbCrLf + Constants.vbCrLf + "Information Covered:" + Constants.vbCrLf + Constants.vbCrLf + "Customer‘s Issues:" + Constants.vbCrLf + Constants.vbCrLf + "Plan of Action:" + Constants.vbCrLf + Constants.vbCrLf + "Follow Up:" + Constants.vbCrLf + Constants.vbCrLf + ((doForm.doRS.GetFieldVal("MMO_NOTES", 2) == null) ? "" : doForm.doRS.GetFieldVal("MMO_NOTES", 2).ToString()) + Constants.vbCrLf + Constants.vbCrLf;
            }
            else
            {
                snotes = "Leads:" + Constants.vbCrLf + Constants.vbCrLf + "Purpose of Meeting:" + Constants.vbCrLf + Constants.vbCrLf + "Information Covered:" + Constants.vbCrLf + Constants.vbCrLf + "Customer‘s Issues:" + Constants.vbCrLf + Constants.vbCrLf + "Plan of Action:" + Constants.vbCrLf + Constants.vbCrLf + "Follow Up:" + Constants.vbCrLf + Constants.vbCrLf;
            }


            doForm.doRS.SetFieldVal("MMO_NOTES", snotes);
            par_doCallingObject = doForm;

            return true;
        }

        public bool AC_FormControlOnChange_BTN_INSERTTEXTBLOCK_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //TKT #1848 append/prepend lead prompts where the cursor position is placed.

            par_bRunNext = false;
            Form doForm = (Form)par_doCallingObject;


            if (doForm.doRS.GetLinkCount("LNK_Template_DO") != 0)
            {
                doForm.oVar.SetVar("MMO_LETTER_IsAppendTextInCursorPosition", "true");
                doForm.oVar.SetVar("MMO_LETTER_AppendText", doForm.doRS.GetFieldVal("LNK_TEMPLATE_DO%%MMO_NOTES").ToString());

                doForm.doRS.ClearLinkAll("LNK_Template_DO");
            }

            doForm.MoveToTab(1);
            doForm.MoveToField("MMO_LETTER");

            par_doCallingObject = doForm;
            return true;
        }

        public bool AC_FormControlOnChange_BTN_INSERTTEXTBLOCK_1_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //TKT #1848 append/prepend lead prompts where the cursor position is placed.
            par_bRunNext = false;
            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.GetLinkCount("LNK_Template_DO") != 0)
            {
                if (string.IsNullOrEmpty(doForm.doRS.GetFieldVal("MMO_NOTES").ToString()))
                {
                    doForm.doRS.SetFieldVal("MMO_NOTES", doForm.doRS.GetFieldVal("LNK_Template_DO%%MMO_Notes"));
                    doForm.MoveToField("MMO_NOTES");
                }
                else
                {
                    doForm.oVar.SetVar("MMO_NOTES_IsAppendTextInCursorPosition", "true");
                    doForm.oVar.SetVar("MMO_NOTES_AppendText", doForm.doRS.GetFieldVal("LNK_TEMPLATE_DO%%MMO_NOTES").ToString());
                }
                doForm.doRS.ClearLinkAll("LNK_Template_DO");
            }
            par_doCallingObject = doForm;
            return true;
        }
        //RN #1873 Multiselect functionality added.
        public bool AC_ViewControlOnChange_BTN_COMPLETE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            string sID = null;

            Desktop doDesktop = (Desktop)par_doCallingObject;
            clRowSet doRS = default(clRowSet);

            //Get selected record and uncheck ext dev review
            //goUI - Not Implemented
            sID = goUI.GetLastSelected("SELECTEDRECORDID");
            string Key = "";
            if (Util.GetSessionValue("LastDesktopHistoryKey") != null)
            {
                Key = Util.GetSessionValue("LastDesktopHistoryKey").ToString();
            }

            //if (Util.GetSessionValue(Key + "_" + "SelectedRecordID") != null)
            //    sID = Util.GetSessionValue(Key + "_" + "SelectedRecordID").ToString();
            sID = GetSelectedRecordIds(Key);

            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select an Activity.",0,"Selltis","","","","","","","",ref par_doCallingObject)
                doDesktop.MessageBox(ref par_doCallingObject, "Please check an Activity.");
                return true;
            }
            string[] RecordIds = sID.Split(',');
            foreach (var RecordId in RecordIds)
            {
                if (!string.IsNullOrEmpty(RecordId))
                {
                    doRS = new clRowSet("AC", 1, "GID_ID='" + RecordId + "'", "GID_ID A", "*");

                    if (doRS.GetFirst() == 1)
                    {
                        //Record found
                        //Check if have permission to edit rec
                        if (goData.GetRecordPermission(doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                        {
                            doDesktop.MessageBox(ref par_doCallingObject, "You do not have permission to edit this Activity.");
                            return false;
                        }
                        //Activities do have to pass validation to be marked as completed.
                        doRS.bBypassValidation = false;
                        doRS.SetFieldVal("MLS_Status", 1, 2);

                        if (doRS.Commit() == 0)
                        {
                            if (goErr.GetLastError("NUMBER") == "E47260")
                            {
                                //goUI - Not Implemented
                                goUI.NewWorkareaMessage("The selected record is missing one or more required fields. Please open the record and fill in required fields.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                            }
                            else
                            {
                                goErr.SetError(35000, sProc, "Completing Activity failed.");
                                return false;
                            }
                            //goErr.SetError(35000, sProc, "Completing Activity failed.")
                            //Return False
                        }
                        else
                        {
                            //goUI - Not Implemented
                            //goUI.RefreshOpenDesktop();
                        }
                    }
                }
            }


            par_doCallingObject = doDesktop;
            return true;
        }

        //RN #1873 Multiselect functionality added.
        private string GetSelectedRecordIds(string Key)
        {
            string LastSelectedMultiSelect = "0";
            string sReturnValue = "";
            if (Util.GetSessionValue(Key + "_" + "LastSelectedMultiSelect") != null)
            {
                LastSelectedMultiSelect = Util.GetSessionValue(Key + "_" + "LastSelectedMultiSelect").ToString();
            }

            if (LastSelectedMultiSelect == "0" && Util.GetSessionValue(Key + "_" + "SelectedRecordID") != null)
            {
                return Util.GetSessionValue(Key + "_" + "SelectedRecordID").ToString();
            }
            else if (LastSelectedMultiSelect == "1" && Util.GetSessionValue(Key + "_" + "MultiSelectedRecords") != null)
            {
                sReturnValue = Util.GetSessionValue(Key + "_" + "MultiSelectedRecords").ToString();

                Util.ClearSessionValue(Key + "_" + "MultiSelectedRecords");
                return sReturnValue;
            }
            else
            {
                return sReturnValue;
            }
        }

        public bool GenerateSysName_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //MI 10/23/07 Added 'UTC' to AT Name.
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sTemp4 = "";
            string sTemp5 = "";
            string sTemp6 = "";
            string sFileName = doRS.GetFileName();
            string sResult = "";
            clRowSet doLink = default(clRowSet);

            switch (Strings.UCase(sFileName))
            {
                //SB 06072019 Tkt#2876 Quote Line Sys Name 
                case "QL":
                    //==> QUOTLINE NEW:	DTE_QteTime+" "+LNK_CreditedTo_USER%%TXT_Code+" "+LNK_To_Company%%TXT_CompanyName+" "+...
                    //					TXT_Model+" "+CUR_Subtotal

                    //CS 5/17/10: Added an additional char of padding to line no to accomodate > 99 quote lines sorting properly in the
                    //QL linkbox. Currently they sort like 1,2,..10,100,101,11. Also added a space to Model. Removed 2 spaces from Company.Cust Code.

                    if (!doRS.IsLoaded("SR__LINENO"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".SR__LINENO");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }
                    if (!doRS.IsLoaded("CUR_PRICEUNIT"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".CUR_PRICEUNIT");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    sTemp3 = doRS.GetFieldVal("LNK_FOR_MO%%TXT_DESCRIPTION", 0, -1, false, 1).ToString();
                    if (string.IsNullOrEmpty(sTemp3))
                    {
                        //No records linked
                        sTemp3 = "?";
                    }

                    if (!doRS.IsLoaded("TXT_TAGNO"))
                    {
                        goErr.SetError(35103, sProc, "", sFileName + ".TXT_TAGNO");
                        ////35103: SYS_Name can't be generated because field '[1]' is not in the rowset. Be sure the rowset contains all fields referenced in the script 'GenerateSysName'.
                    }

                    sTemp = Convert.ToString(doRS.GetFieldVal("SR__LINENO", 1));
                    sTemp2 = Convert.ToString(doRS.GetFieldVal("CUR_PRICEUNIT", 1));
                    sTemp4 = Convert.ToString(doRS.GetFieldVal("TXT_TAGNO", 1));

                    sTemp4 = string.IsNullOrEmpty(sTemp4) ? "?" : sTemp4;

                    int sTemp3length = sTemp3.Length > 40 ? 40 : sTemp3.Length;

                    string sSpace = " ";
                    try
                    {
                        if (!sTemp.Substring(0, 2).Contains(".") || sTemp.Substring(0, 1) == "0")
                        {
                            sSpace = "";
                        }
                    }
                    catch (Exception ex)
                    {
                        sSpace = "";
                    }

                    sResult = sSpace + goTR.Pad(sTemp, 5, "", "R", true) + "   " + sTemp2 + "   " + goTR.Pad(sTemp3, sTemp3length, "  ", "R", true) + "  " + goTR.Pad(sTemp4, 10, "   ", "R", true);

                    par_bRunNext = false;
                    par_oReturn = sResult;

                    break;

            }
            par_doCallingObject = doRS;     //Assign doRS to par_doCallingObject
            return true;

        }

        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_Corr_MS Word_Quote_Rev_020623_draft.docx";
                }
                else if (sQTTemplate == "Berthold rev 020623")
                {
                    return "cus_Corr_MS Word_Quote-Berthold_Rev_020623_draft.docx";
                }
                else if (sQTTemplate == "Oseco rev 020623")
                {
                    return "cus_Corr_MS Word_Quote-OSECO_Rev_020623_draft.docx";
                }
                else if (sQTTemplate == "Quote Berthold Commission")
                {
                    return "cus_Quote-Berthold-Commission_draft.docx";
                }
                else if (sQTTemplate == "Quote Protego - Word")
                {
                    return "cus_Corr_protego_MS Word_Quote_draft.docx";
                }
            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_Corr_MS Word_Quote_Rev_020623.docx";
                }
                else if (sQTTemplate == "Berthold rev 020623")
                {
                    return "cus_Corr_MS Word_Quote-Berthold_Rev_020623.docx";
                }
                else if (sQTTemplate == "Oseco rev 020623")
                {
                    return "cus_Corr_MS Word_Quote-OSECO_Rev_020623.docx";
                }
                else if (sQTTemplate == "Quote Berthold Commission")
                {
                    return "cus_Quote-Berthold-Commission.docx";
                }
                else if (sQTTemplate == "Quote Protego - Word")
                {
                    return "cus_Corr_protego_MS Word_Quote.docx";
                }
            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }
            if (doForm.Save(3) != 1)
            {
                goLog.SetErrorMsg("Save failed for QT PDF Generation ");
                //return false;
            }
            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);
                        
                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }

        public bool OL_RecordBeforeDelete_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            doRS.oVar.SetVar("OppID", doRS.GetFieldVal("LNK_IN_OP"));

            par_doCallingObject = doRS;

            return true;
        }

        public bool OL_RecordAfterDelete_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            ////par_doCallingObject: Form object calling this script. Do not delete in script!
            ////par_doArray: Unused.
            ////par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            ////par_s2 to par_s5: Unused.
            ////par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            ////par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            ////par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;

            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sID = doRS.oVar.GetVar("OppID").ToString();

            if (!string.IsNullOrEmpty(sID))
            {
                clRowSet doOPLines = default(clRowSet);

                //Create a rowset of quote lines linked to the deleted quote.
                doOPLines = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo,Gid_id", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    StringBuilder sbQuery = new StringBuilder();
                    for (int i = 1; i <= doOPLines.Count(); i++)
                    {
                        double iLineno = Convert.ToDouble(doOPLines.GetFieldVal("SR__LineNo"));
                        string sGid_id = Convert.ToString(doOPLines.GetFieldVal("Gid_ID"));
                        string sTemp = "#" + goTR.Pad(i.ToString(), 6, " ", "L", true, "R");
                        sbQuery.AppendLine("Update OL Set SR__LineNo='" + i.ToString() + "',sys_name='" + sTemp + "' where Gid_id='" + sGid_id + "' AND SR__LineNo = '" + iLineno.ToString() + "'");

                        if (doOPLines.GetNext() == 0)
                        {
                            break;
                        }

                    }
                    if (!string.IsNullOrEmpty(sbQuery.ToString()))
                    {
                        bool bretval = goData.RunSQLQuery(sbQuery.ToString());
                    }

                }

                doOPLines = null;

            }

            par_doCallingObject = doRS;

            return true;
        }


        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS" ,2)) != 0)
            {
                 Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                 par_doCallingObject = _desktop;
                 return false;
            }
            
            

            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));

            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECLOSED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
                rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);

                    
                    doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2); 
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));                   
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("Cur_UnitPrice", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);
                   
                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }
        public bool FD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sTitle = "";
            string sUserGid = "";

            sTitle = Convert.ToString(doRS.GetFieldVal("TXT_TITLE"));
            sUserGid = Convert.ToString(doRS.GetFieldVal("LNK_TO_US"));

            goUI.AddAlert(sTitle + "  file is ready to download", "OPENDESKTOP", "DSK_4E8207CE-28DE-4F55-5858-B0B300B6D2B2", sUserGid, "").ToString();

            par_doCallingObject = doRS;
            return true;
        }

    }


}
