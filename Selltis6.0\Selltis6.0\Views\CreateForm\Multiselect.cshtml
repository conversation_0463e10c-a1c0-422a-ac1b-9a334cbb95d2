﻿@using Kendo.Mvc.UI;
@using Selltis.MVC.Models;
@using Selltis.BusinessLogic;
@using Selltis.Core;

@model Selltis.MVC.Models.ControlAttributes


@{
    bool Enable = true;
    string display = "block";
    clData goData = (clData)Util.GetInstance("data");

    if (goData.IsFieldSystem(Model.field, Model.TableName))
    {
        Enable = false;
    }
    if (Model.ControlState != null && Model.ControlState.FieldPropertiy.State != null && (Model.ControlState.FieldPropertiy.State == 1 || Model.ControlState.FieldPropertiy.State == 4 || Model.ControlState.FieldPropertiy.State == 5))
    {
        Enable = false;
    }
    else if (Model.ControlState != null && Model.ControlState.FieldPropertiy.State != null && (Model.ControlState.FieldPropertiy.State == 2))//Invisible
    {
        display = "none";
    }

    string sStyle = "margin-bottom:12px !important;";

    if (Model.IsHeaderControl)
    {
        sStyle = "";
    }
}
<script>
    var IsFiltering = false;
    var IsSelectField = false;

    function onSelect(e) {
        //debugger

        var dataItem = this.dataItem(e.item.index());
        IsSelectField = true;
        showProgress();
        $.ajax({
            url: '/CreateForm/SetLNKMultiSelectData',
            data: { Value: dataItem.Value, IsNDB: '@Model.IsNDB', sField: '@Model.field', TableName: '@Model.TableName', FormKey: '@Model.formkey', IsNNType: '@Model.isNN_Type', FormHistoryKey : '@Model.FormHistoryKey' },
            cache: false,
            success: function (data) {

                var onChangeField = "";

                //debugger
                if (data.indexOf('||') > 0) {
                    var fields = data.split('||');
                    for (i = 0; i < fields.length; i++) {

                        var fieldLength = fields[i].split('_');
                        if (fieldLength.length == 3) {
                            onChangeField = fields[i];
                        }

                        var LNKgrid = $("#" + fields[i]).data("kendoGrid");
                        if (LNKgrid != null)
                            LNKgrid.dataSource.read();
                        else if ($("#" + fields[i]).data('kendoMultiSelect') != null) {
                            $("#" + fields[i]).data('kendoMultiSelect').dataSource.read();
                        }
                    }
                }
                else {
                    onChangeField = '@Model.field.Replace(" ","")';
                    var LNKgrid = $("#" + '@Model.field.Replace(" ","")').data("kendoGrid");
                    if (LNKgrid == null && '@Model.IsNDB' == 'True') {
                        LNKgrid = $("#NDB_" + '@Model.field.Replace(" ","")').data("kendoGrid");
                    }
                    if (LNKgrid != null)
                        LNKgrid.dataSource.read();
                    else if ($("#" + '@Model.field.Replace(" ","")').data('kendoMultiSelect') != null || $("#NDB_" + '@Model.field.Replace(" ","")').data('kendoMultiSelect') != null) {

                        $.ajax({
                            url: '/CreateForm/LoadLNKMultiSelectData',
                            data: { sField: '@Model.field', FormKey: '@Model.formkey', FormHistoryKey : '@Model.FormHistoryKey' },
                            cache: false,
                            async: false,
                            dataType: "json",
                            type: "GET",
                            contentType: 'application/json',
                            success: function (result) {
                                if ($("#" + '@Model.field').data("kendoMultiSelect"))
                                    $("#" + '@Model.field').data("kendoMultiSelect").setDataSource(new kendo.data.DataSource({ data: result }));
                                else if ($("#NDB_" + '@Model.field').data("kendoMultiSelect"))
                                    $("#NDB_" + '@Model.field').data("kendoMultiSelect").setDataSource(new kendo.data.DataSource({ data: result }));

                            },
                            error: function (data) {

                            }
                        })

                        @*$("#" + '@Model.field.Replace(" ","")').data('kendoMultiSelect').dataSource.read();*@
                        //refreshData();
                    }
                }

                if ('@Model.IsNDB' != 'True') {
                    MessageBoxClick('RUNSCRIPT', '@Model.TableName' + '_FormControlOnChange_' + onChangeField, undefined, undefined, true);
                }

                hideProgress();
            },
            error: function (data) {
                hideProgress();
            }
        })
    }

    //function onDeSelect(e) {
    //    var dataItem = this.dataItem(e.item.index());
    //}
    function onOpen(e) {
        $("#" + '@Model.field.Replace(" ","")').data('kendoMultiSelect').dataSource.read();
    }
    function onClose(e) {
        @*$("#" + '@Model.field.Replace(" ","")').data('kendoMultiSelect').dataSource.read();*@

        //To clear data source from showing last searched items..J
        var dataItem = $("#" + '@Model.field').data("kendoMultiSelect").dataItems();

        if (dataItem == undefined || dataItem == null || dataItem.length <= 0) {
            //clear data source when there are no selected data items..J
            $("#" + '@Model.field').data("kendoMultiSelect").setDataSource(new kendo.data.DataSource({ data: null }));
        }
        else {
            //clear data source when there are selected data items..J
            $("#" + '@Model.field').data("kendoMultiSelect").dataSource.read();
        }

        IsFiltering = false;
        IsSelectField = true;
    }

    function onChange(e) {

        //showProgress();
        //debugger
        var previous = this._savedOld;
        var current = this.value();
        var differenceGID_IDs = [];

        if (previous) {
            differenceGID_IDs = $(previous).not(current).get();
        }

        this._savedOld = current.slice(0);

        IsFiltering == false;
        IsSelectField = true;

        if (differenceGID_IDs.length > 0) {
            showProgress();
            if (differenceGID_IDs.length == 1 && differenceGID_IDs[0] == "") {
                hideProgress();
                return;
            }
            else {
                $.ajax({
                    url: '/CreateForm/RemoveLNKMultiSelectData',
                    data: { Values: JSON.stringify(differenceGID_IDs), IsNDB: '@Model.IsNDB', sField: '@Model.field', TableName: '@Model.TableName', FormKey: '@Model.formkey', IsNNType: '@Model.isNN_Type', FormHistoryKey : '@Model.FormHistoryKey' },
                    cache: false,
                    success: function (data) {
                        var onChangeField = "";
                        if (data.indexOf('||') > 0) {
                            var fields = data.split('||');
                            for (i = 0; i < fields.length; i++) {

                                var fieldLength = fields[i].split('_');
                                if (fieldLength.length == 3) {
                                    onChangeField = fields[i];
                                }

                                var LNKgrid = $("#" + fields[i]).data("kendoGrid");
                                if (LNKgrid != null)
                                    LNKgrid.dataSource.read();
                                else if ($("#" + fields[i]).data('kendoMultiSelect') != null) {
                                    //$("#" + fields[i]).data('kendoMultiSelect').dataSource.read();
                                }
                            }
                        }
                        else {
                            onChangeField = '@Model.field.Replace(" ","")';
                            var LNKgrid = $("#" + '@Model.field.Replace(" ","")').data("kendoGrid");
                            if (LNKgrid == null && '@Model.IsNDB' == 'True') {
                                LNKgrid = $("#NDB_" + '@Model.field.Replace(" ","")').data("kendoGrid");
                            }
                            if (LNKgrid != null)
                                LNKgrid.dataSource.read();

                            else if ($("#" + '@Model.field.Replace(" ","")').data('kendoMultiSelect') != null || $("#NDB_" + '@Model.field.Replace(" ","")').data('kendoMultiSelect') != null) {
                                showProgress();
                                $.ajax({
                                    url: '/CreateForm/LoadLNKMultiSelectData',
                                    data: { sField: '@Model.field', FormKey: '@Model.formkey', FormHistoryKey: '@Model.FormHistoryKey' },
                                    cache: false,
                                    async: false,
                                    dataType: "json",
                                    type: "GET",
                                    contentType: 'application/json',
                                    success: function (result) {
                                        if ($("#" + '@Model.field').data("kendoMultiSelect"))
                                            $("#" + '@Model.field').data("kendoMultiSelect").setDataSource(new kendo.data.DataSource({ data: result }));
                                        else if ($("#NDB_" + '@Model.field').data("kendoMultiSelect"))
                                            $("#NDB_" + '@Model.field').data("kendoMultiSelect").setDataSource(new kendo.data.DataSource({ data: result }));
                                        hideProgress();
                                    },
                                    error: function (data) {
                                        hideProgress();
                                    }
                                })

                                @*$("#" + '@Model.field.Replace(" ","")').data('kendoMultiSelect').dataSource.read();*@
                                //refreshData();
                            }
                        }
                        if ('@Model.IsNDB' != 'True') {
                            MessageBoxClick('RUNSCRIPT', '@Model.TableName' + '_FormControlOnChange_' + onChangeField, undefined, undefined, true);
                        }


                        hideProgress();
                    },
                    error: function (data) {
                        hideProgress();
                    }
                })
            }

        }
    }

    function onDataBound(e) {

        //debugger
        var SelectedNodeId = '';

        if (IsFiltering == false || IsSelectField == true) {

            //refreshData();
            //debugger
            var multiselect = $("#" + '@Model.field').data("kendoMultiSelect");

            var fieldValue = GetLNKControlValue('@Model.field');

            fieldValue = fieldValue.replace(/(\r\n)/g, ",");

            SelectedNodeId = fieldValue;

            if (SelectedNodeId != undefined && SelectedNodeId != null && SelectedNodeId != "") {
                if (SelectedNodeId.indexOf(',') > -1) {
                    var SelectedNodeIds = SelectedNodeId.split(',');

                    $("#" + '@Model.field').data("kendoMultiSelect").value(SelectedNodeIds);

                    this._savedOld = SelectedNodeIds.slice(0);
                }
                else {
                    this._savedOld = SelectedNodeId.split(',').slice(0);
                    multiselect.value(SelectedNodeId);
                }
                $("#lblmore" + '@Model.field').hide();

                $("#lbl" + '@Model.field').parent().removeClass("fg-toggledLNK");
                $("#lbl" + '@Model.field').parent().addClass("fg-toggledLNK");
            }
            else {
                $("#lbl" + '@Model.field').parent().removeClass("fg-toggledLNK");
            }

        }
        else {
            //IsFiltering = false;
            @*$("#" + '@Model.field').data("kendoMultiSelect").open();*@
        }

    }

    @*function SelectAll() {
        var listItems = $("#" + '@Model.field').data("kendoMultiSelect").dataSource;
        var data = listItems._data;
        var _values = [];
        for (var j = 0; j < listItems._data.length; j++) {
            _values.push(listItems._data[j].Value);
        }
        var multiselect = $("#" + '@Model.field').data("kendoMultiSelect");
        multiselect.value(_values);
    }*@

    function refreshData() {
        var multiselect = $("#" + '@Model.field').data("kendoMultiSelect");

        var fieldValue = GetLNKControlValue('@Model.field');

        fieldValue = fieldValue.replace(/(\r\n)/g, ",");

        SelectedNodeId = fieldValue;

        if (SelectedNodeId.indexOf(',') > -1) {

            var SelectedNodeIds = SelectedNodeId.split(',');

            multiselect.value(SelectedNodeIds);

            this._savedOld = SelectedNodeIds.slice(0);

            $("#lblmore" + '@Model.field').hide();

        }
        else {
            this._savedOld = SelectedNodeId.split(',').slice(0);
            multiselect.value(SelectedNodeId);
            $("#lblmore" + '@Model.field').hide();
        }
    }

    function onFiltering(e) {


        //debugger
        if (e.filter != undefined) {
            IsFiltering = true;
            IsSelectField = false;

            var keyvalue = e.filter.value;

            if (keyvalue.length >= 1) {

                $("#lbl" + '@Model.field').parent().addClass("fg-toggledLNK");

                showProgress();
                $.ajax({
                    url: "/CreateForm/GetMultiSelectLNKFieldValues",
                    data: {
                        sField: '@Model.field', sValue: '@Model.value', sMaxRecordCount: '@Model.MaxRecordCount', TableName: '@Model.TableName',
                        isNNType: '@Model.isNN_Type', FormKey: '@Model.formkey', Label: '@Model.label', SearchText: keyvalue, FormHistoryKey : '@Model.FormHistoryKey'
                    },
                    dataType: "json",
                    type: "GET",
                    contentType: 'application/json',
                    cache: false,
                    success: function (result) {

                        var listItems = $("#" + '@Model.field').data("kendoMultiSelect").dataSource;
                       
                        //set data source when there are no selected data items..J
                        if (listItems._data == undefined || listItems._data == null || listItems._data.length <= 0) {

                            $("#" + '@Model.field').data("kendoMultiSelect").setDataSource(new kendo.data.DataSource({ data: result }));
                        }
                        else {

                            for (var i = 0; i < result.length; i++) {

                                var isExist = false;
                                for (var j = 0; j < listItems._data.length; j++) {
                                    if (listItems._data[j].Value == result[i].Value) {
                                        isExist = true;
                                        break;
                                    }
                                }
                                if (isExist == false) {
                                    $("#" + '@Model.field').data("kendoMultiSelect").dataSource.add({
                                        Text: result[i].Text,
                                        Value: result[i].Value
                                    })
                                }

                            }

                        }



                        @*if (result != undefined && result != null && result.length > 0) {
                            var listItems = $("#" + '@Model.field').data("kendoMultiSelect").dataSource;

                            for (var i = 0; i < result.length; i++) {

                                if (listItems._data == undefined && listItems._data == null && listItems._data.length <= 0) {
                                    $("#" + '@Model.field').data("kendoMultiSelect").dataSource.add({
                                        Text: result[i].Text,
                                        Value: result[i].Value
                                    })
                                }
                                else {
                                    var isExist = false;
                                    for (var j = 0; j < listItems._data.length; j++) {
                                        if (listItems._data[j].Value == result[i].Value) {
                                            isExist = true;
                                            break;
                                        }
                                    }
                                    if (isExist == false) {
                                        $("#" + '@Model.field').data("kendoMultiSelect").dataSource.add({
                                            Text: result[i].Text,
                                            Value: result[i].Value
                                        })
                                    }
                                }

                            }

                           // $("#" + '@Model.field').data("kendoMultiSelect").open();
                        }*@

                        hideProgress();
                    },
                    error: function (result) {
                        hideProgress();
                    }
                })
            }
            else if (keyvalue.length == 0) {

                //To clear data source from showing last searched items..J
                var dataItem = $("#" + '@Model.field').data("kendoMultiSelect").dataItems();

                if (dataItem == undefined || dataItem == null || dataItem.length <= 0) {
                    //clear data source when there are no selected data items..J
                    $("#" + '@Model.field').data("kendoMultiSelect").setDataSource(new kendo.data.DataSource({ data: null }));
                }
                else {
                    //clear data source when there are selected data items..J
                    $("#" + '@Model.field').data("kendoMultiSelect").dataSource.read();
                }
                $("#lbl" + '@Model.field').parent().removeClass("fg-toggledLNK");
            }
        }
        else {
            @*$("#" + '@Model.field').data("kendoMultiSelect").dataSource.read();*@
        }
    }
</script>

<div id="<EMAIL>" style="display:@display;@sStyle">
    <input type="hidden" id="<EMAIL>" value="@Model.value" />
    @(Html.Kendo().MultiSelect()
            //.HtmlAttributes(new { @class = "demo-section" })
        .HtmlAttributes(new { @title = Model.title, @tabindex = @Model.ControlTabIndex })
        .Name(Model.field)
        .DataTextField("Text")
        .DataValueField("Value")
        .Enable(Enable)
                .Filter("contains")
            // .Value(_val)
        .Value(Model.value.Split(','))
            //.Value(new string[] { })
                .Events(e =>
                {
                    e.Select("onSelect")
                     .Change("onChange")
                     .DataBound("onDataBound")
                     .Filtering("onFiltering")
                        //.Deselect("onDeSelect")
                        //.Open("onOpen")
                     .Close("onClose");
                })

        //.AutoClose(true)
        .DataSource(source =>
        {
            source.Read(read =>
            {
                read.Action("LoadLNKMultiSelectData", "CreateForm", new { sField = Model.field, FormKey = Model.formkey, FormHistoryKey = Model.FormHistoryKey });
            })
            .ServerFiltering(false);
        })
                .FooterTemplate("<span style=\"cursor:pointer;\" onclick=\"LNKMultiSelectRefreshClick('" + Model.field + "','','" + Model.MaxRecordCount + "','" + Model.TableName + "','','" + Model.formkey + "','')\">&nbsp;<i class=\"fa fa-refresh\"></i>&nbsp;Refresh</span> <span style=\"float:right;\"><a onclick=\"LnkLabelClick('" + Model.field + "', '" + Model.TableName + "', '', '', '" + Model.isNN_Type + "', '" + Model.formkey + "','" + Model.label + "')\" style=\"cursor:pointer;\">Click here to search</a></span>")
    )

</div>

<script>

    $(document).ready(function () {
        var multiselect = $("#" + '@Model.field').data("kendoMultiSelect");
        multiselect.input.on("focus", function () {
            $("#lbl" + '@Model.field').parent().addClass("fg-toggledLNK");

        });

        @*if ('@Enable' == 'False')
        {
            multiselect.enable(false);
        }*@

        @*var multiselectSelectedVal = $("#" + '@Model.field').data("kendoMultiSelect");
        var SelectedNodeId = '@Model.value';
        if (SelectedNodeId.indexOf(',') > -1) {
            var SelectedNodeIds = SelectedNodeId.split(',');
            multiselectSelectedVal.value(SelectedNodeIds);
        }
        else {
            multiselectSelectedVal.value(SelectedNodeId);
        }*@

        //remove kendo tooltip..J
        //$(".k-multiselect").mouseover(function () {
        //    var tooltip = $(this).find("select[multiple='multiple']").attr("title");
        //    $(this).kendoTooltip({
        //        content: tooltip,
        //        autoHide: true,
        //    });
        //});

        if ('@Model.Scripts' != "") {

            @*$("#@Model.field").data("kendoMultiSelect").bind("change", function () {
                //debugger;
                @*RunScript('@Model.Scripts');
            });*@
        }



    })


    $("#cdiv_" + '@Model.field').on("dblclick", ".k-button", function (e) {
       //debugger
        var Text = e.currentTarget.innerText;
        var dataItem = $("#" + '@Model.field').data("kendoMultiSelect").dataItems();

        var ResultValue = "";

        if (dataItem != null && dataItem.length > 0) {
            for (var i = 0; i < dataItem.length; i++) {

                if (Text.replace(/ /g, '') == dataItem[i].Text.replace(/ /g, ''))
                {
                    ResultValue = dataItem[i].Value;
                    break;
                }

            }
        }

        if (ResultValue != null && ResultValue != "") {

            UpdateDoRS(true);
            showProgress();
            $.ajax({
                url: '/CreateForm/GetTypeValue',
                data: { LinkName: '@Model.field', Gid_id: ResultValue },
                async: true,
                cache: false,
                success: function (data) {
                    //debugger
                    if (data != "") {
                        var _type = data;
                        var viewkey = '';
                        var recordid = '';
                        if ('@Model.ViewKey' == '' || '@Model.ViewKey' == null)
                            viewkey = 'KEY';
                        else
                            viewkey = '@Model.ViewKey';
                        if ('@Model.RecordId' == '' || '@Model.RecordId' == null)
                            recordid = 'MASTERID';
                        else
                            recordid = '@Model.RecordId';

                        var HistoryKey = '@Util.GetSessionValue("LastDesktopHistoryKey")';
                        if (HistoryKey == null || HistoryKey == undefined || HistoryKey == '') {
                            HistoryKey = "HISTORYKEY";
                        }
                        
                        var url = "/CreateForm/CreateForm/" + _type + "/" + ResultValue + "/TYPE/false/false/" + viewkey + "/false/" + recordid + "/" + '@Model.formkey.Replace(":","||")' + "/" + '@Model.field' + "/" + HistoryKey;
                        sessionStorage.setItem("LNKItem" + '@Model.formkey', "Yes")
                        window.location = url;

                    }
                },
                error: function () {
                    hideProgress();
                    alert(data.responseText);
                }
            })
        }

    });

    @*$("#" + '@Model.field').data().kendoMultiSelect.input.on('keydown', function (e) {

        var key = e.key;
        debugger
        if(key.length >= 3)
        {
            //string sField, string sValue, string sMaxRecordCount, string TableName, string isNNType, string FormKey, string Label, string SearchText
            $.ajax({
                url: "\CreateForm\GetMultiSelectLNKFieldValues",
                data: {sField:'@Model.field',sValue:'@Model.value',sMaxRecordCount:'@Model.MaxRecordCount',TableName:'@Model.TableName',
                    isNNType: '@Model.isNN_Type', FormKey: '@Model.formkey', Label: '@Model.label', SearchText: key
                },
                cache: false,
                async: false,
                success:function(data)
                {
                    debugger
                },
                error:function(data)
                {

                }
            })
        }
        else
        {
            return;
        }
    })*@
</script>
<style>
    .k-callout {
        display: none;
    }

    .k-tooltip-content {
        /*background-color: #E4E5F0;*/
        color: black;
    }

    .k-multiselect .k-button {
        color: #555;
    }

    .k-multiselect-wrap li {
        /*padding: 0px !important;*/
        line-height: normal !important;
    }

    .k-multiselect .k-button {
        border-color: #959595 !important;
    }

    .k-footer {
        background-color: #EEEEEE;
        box-sizing: content-box !important;
    }

    .k-nodata {
        display: none !important;
    }

    .k-item.k-state-focused {
        background-color: #EBEBEB !important;
        border-radius: 0px !important;
        box-shadow: none !important;
    }

        .k-item.k-state-focused.k-state-selected {
            background-color: #428bca !important;
        }

    .k-multiselect .k-list-container.k-popup.k-group.k-reset {
        height: auto !important;
    }

    .k-multiselect-wrap .k-input {
        background-color: transparent !important;
    }
</style>
