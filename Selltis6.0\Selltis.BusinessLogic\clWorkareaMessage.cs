﻿using System;
using System.Web;

namespace Selltis.BusinessLogic
{
	public class clWorkareaMessage
	{

#region Declarations

		private clError goErr;

		private string gsMBFormMode = "NORMAL";
		private string gsMBScriptInitiatedSave = "";
		private bool gbMBDisplay = false;
		private string gsMBMessage = "";
		private int giMBStyle = clC.SELL_MB_OK;
		private string gsMBTitle = "";
		private string gsMBButton1Label = "";
		private string gsMBButton2Label = "";
		private string gsMBButton3Label = "";
		private string gsMBInputDefaultValue = "";
		private string gsMBButton1Script = "";
		private string gsMBButton2Script = "";
		private string gsMBButton3Script = "";
		private string gsMBPar1 = "";
		private string gsMBPar2 = "";
		private string gsMBPar3 = "";
		private string gsMBPar4 = "";
		private string gsMBPar5 = "";
		private object goCallingObject;
        private static object tempVar;

        #endregion

        #region Public Properties

        public string MessageBoxButton1Label
		{
			get
			{
				return gsMBButton1Label;
			}
		}

		public string MessageBoxButton1Script
		{
			get
			{
				return gsMBButton1Script;
			}
		}

		public string MessageBoxButton2Label
		{
			get
			{
				return gsMBButton2Label;
			}
		}

		public string MessageBoxButton2Script
		{
			get
			{
				return gsMBButton2Script;
			}
		}

		public string MessageBoxButton3Label
		{
			get
			{
				return gsMBButton3Label;
			}
		}

		public string MessageBoxButton3Script
		{
			get
			{
				return gsMBButton3Script;
			}
		}

		public bool MessageBoxDisplay
		{
			get
			{
				return gbMBDisplay;
			}
		}

		public string MessageBoxFormMode
		{
			get
			{
				return gsMBFormMode;
			}
		}

		public string MessageBoxInputDefaultValue
		{
			get
			{
				return gsMBInputDefaultValue;
			}
		}

		public string MessageBoxMessage
		{
			get
			{
				return gsMBMessage;
			}
		}

		public string MessageBoxPar1
		{
			get
			{
				return gsMBPar1;
			}
		}

		public string MessageBoxPar2
		{
			get
			{
				return gsMBPar2;
			}
		}

		public string MessageBoxPar3
		{
			get
			{
				return gsMBPar3;
			}
		}

		public string MessageBoxPar4
		{
			get
			{
				return gsMBPar4;
			}
		}

		public string MessageBoxPar5
		{
			get
			{
				return gsMBPar5;
			}
		}

		public string MessageBoxScriptInitiatedSave
		{
			get
			{
				return gsMBScriptInitiatedSave;
			}
		}

		public int MessageBoxStyle
		{
			get
			{
				return giMBStyle;
			}
		}

		public string MessageBoxTitle
		{
			get
			{
				return gsMBTitle;
			}
		}

		public object MessageBoxCallingObject
		{
			get
			{
				return goCallingObject;
			}
		}

#endregion

#region Private Methods

		private void Initialize()
		{
			string sProc = "clWorkareaMessage:Initialize";
			//Try
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

#endregion

#region Public Methods

		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script, ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2, string par_s3, string par_s4): this(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, par_s4, "")
		{
		}

		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script, ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2, string par_s3): this(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref par_doCallingObject, par_doArray, par_s1, par_s2, par_s3, "", "")
		{
		}

		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script, ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2): this(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref par_doCallingObject, par_doArray, par_s1, par_s2, "", "", "")
		{
		}

		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script, ref object par_doCallingObject, clArray par_doArray, string par_s1): this(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref par_doCallingObject, par_doArray, par_s1, "", "", "", "")
		{
		}

		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script, ref object par_doCallingObject, clArray par_doArray): this(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref par_doCallingObject, par_doArray, "", "", "", "", "")
		{
		}

		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script, ref object par_doCallingObject): this(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref par_doCallingObject, null, "", "", "", "", "")
		{
		}

		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script): this(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, par_sButton3Script, ref tempVar, null, "", "", "", "", "")
		{
		}

		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script): this(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, par_sButton2Script, "", ref tempVar, null, "", "", "", "", "")
		{
		}

		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script): this(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, par_sButton1Script, "", "", ref tempVar, null, "", "", "", "", "")
		{
		}

		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue): this(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, par_sInputDefaultValue, "", "", "", ref tempVar, null, "", "", "", "", "")
		{
			tempVar = null;
        }

		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label): this(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, par_sButton3Label, "", "", "", "", ref tempVar, null, "", "", "", "", "")
		{
		}

		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label): this(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, par_sButton2Label, "", "", "", "", "", ref tempVar, null, "", "", "", "", "")
		{
		}

		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label): this(par_sMessage, par_iStyle, par_sTitle, par_sButton1Label, "", "", "", "", "", "", ref tempVar, null, "", "", "", "", "")
		{
		}

		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle): this(par_sMessage, par_iStyle, par_sTitle, "", "", "", "", "", "", "", ref tempVar, null, "", "", "", "", "")
		{
		}

		public clWorkareaMessage(string par_sMessage, int par_iStyle): this(par_sMessage, par_iStyle, "Selltis", "", "", "", "", "", "", "", ref tempVar, null, "", "", "", "", "")
		{
		}

		public clWorkareaMessage(string par_sMessage): this(par_sMessage, clC.SELL_MB_OK, "Selltis", "", "", "", "", "", "", "", ref tempVar, null, "", "", "", "", "")
		{
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Public Sub New(ByVal par_sMessage As String, Optional ByVal par_iStyle As Integer = clC.SELL_MB_OK, Optional ByVal par_sTitle As String = "Selltis", Optional ByVal par_sButton1Label As String = "", Optional ByVal par_sButton2Label As String = "", Optional ByVal par_sButton3Label As String = "", Optional ByVal par_sInputDefaultValue As String = "", Optional ByVal par_sButton1Script As String = "", Optional ByVal par_sButton2Script As String = "", Optional ByVal par_sButton3Script As String = "", Optional ByRef par_doCallingObject As Object = Nothing, Optional ByVal par_doArray As clArray = Nothing, Optional ByVal par_s1 As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "")
		public clWorkareaMessage(string par_sMessage, int par_iStyle, string par_sTitle, string par_sButton1Label, string par_sButton2Label, string par_sButton3Label, string par_sInputDefaultValue, string par_sButton1Script, string par_sButton2Script, string par_sButton3Script, ref object par_doCallingObject, clArray par_doArray, string par_s1, string par_s2, string par_s3, string par_s4, string par_s5)
		{

			//par_doCallingObject needs default value of "doForm" per Mario, which I guess is a refrence to ME...

			//- par_iStyle is not Microsoft.VisualBasic.MsgBoxStyle data type to allow supporting
			//InputBox as a style and not to have to support all standard MsgBox styles. Initially,
			//the following styles should be supported:
			//        SELL_MB_OK (default)
			//        SELL_MB_YESNO()
			//        SELL_MB_YESNOCANCEL()
			//        SELL_MB_INPUTBOX (OK button only)
			//        SELL_MB_ABORTRETRYIGNORE = &H2
			//        SELL_MB_OKCANCEL = &H1
			//        SELL_MB_RETRYCANCEL = &H5
			//        SELL_MB_DEFBUTTON1()
			//        SELL_MB_DEFBUTTON2()
			//        SELL_MB_DEFBUTTON3()
			//Icons are not supported to keep the spec tight.
			//Styles can be added (for ex clc.SELL_MB_YESNOCANCEL + clc.SELL_MB_DEFBUTTON2).

			//- par_sButton<1-3>Label allows applying custom labels to the buttons. Leaving these parameters
			//blank causes default button labels to be used. In the case of SELL_MB_INPUTBOX,
			//par_sButton1Label relabels the OK button.

			//- par_sInputDefaultValue is the value to enter into the input field by default.

			//- par_sButton<1-3>Script allows defining script procedure names that will be executed when each button
			//is clicked. If a script is not defined, the button click causes Return False to be executed.

			//- Last 7 parameters are passed to the called script(s) unchanged. In the form context, par_doCallingObject 
			//should always be doForm (set by default).

			string sProc = "clWorkareaMessage::MessageBox";
			//Try

			Initialize();

				gbMBDisplay = true;
				gsMBMessage = par_sMessage;
				giMBStyle = par_iStyle;
				gsMBTitle = par_sTitle;
				gsMBButton1Label = par_sButton1Label;
				gsMBButton2Label = par_sButton2Label;
				gsMBButton3Label = par_sButton3Label;
				gsMBInputDefaultValue = par_sInputDefaultValue;
				gsMBButton1Script = par_sButton1Script;
				gsMBButton2Script = par_sButton2Script;
				gsMBButton3Script = par_sButton3Script;
				gsMBPar1 = par_s1;
				gsMBPar2 = par_s2;
				gsMBPar3 = par_s3;
				gsMBPar4 = par_s4;
				gsMBPar5 = par_s5;

			//redirect - open form
			//goUI.SetVar(GUID, Me)
			//HttpContext.Current.Response.Redirect(goUI.Navigate("FORMOBJECT", GUID))

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public string GetButtonLabel(string sDefault, string sSetting)
		{
			string sProc = "clWorkareaMessage::GetButtonLabel";
			try
			{
				switch (sSetting)
				{
					case "":
						return sDefault;
					default:
						return sSetting;
				}
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return "";
			}
		}

#endregion

	}

}
