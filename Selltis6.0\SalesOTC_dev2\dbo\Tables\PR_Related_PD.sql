﻿CREATE TABLE [dbo].[PR_Related_PD] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Project_Related_Product_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PR] UNIQUEIDENTIFIER NOT NULL,
    [GID_PD] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PR_Related_PD] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PD_Connected_PR] FOREIGN KEY ([GID_PR]) REFERENCES [dbo].[PR] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PR_Related_PD] FOREIGN KEY ([GID_PD]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PR_Related_PD] NOCHECK CONSTRAINT [LNK_PD_Connected_PR];


GO
ALTER TABLE [dbo].[PR_Related_PD] NOCHECK CONSTRAINT [LNK_PR_Related_PD];


GO
CREATE CLUSTERED INDEX [IX_PD_Connected_PR]
    ON [dbo].[PR_Related_PD]([GID_PR] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_Related_PD]
    ON [dbo].[PR_Related_PD]([GID_PD] ASC);

