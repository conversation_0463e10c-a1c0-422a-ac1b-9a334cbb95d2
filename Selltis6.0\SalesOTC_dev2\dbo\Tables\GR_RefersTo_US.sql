﻿CREATE TABLE [dbo].[GR_RefersTo_US] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Group_RefersTo_User_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_GR] UNIQUEIDENTIFIER NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_GR_RefersTo_US] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_GR_RefersTo_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_Related_GR] FOREIGN KEY ([GID_GR]) REFERENCES [dbo].[GR] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[GR_RefersTo_US] NOCHECK CONSTRAINT [LNK_GR_RefersTo_US];


GO
ALTER TABLE [dbo].[GR_RefersTo_US] NOCHECK CONSTRAINT [LNK_US_Related_GR];


GO
CREATE CLUSTERED INDEX [IX_GR_RefersTo_US]
    ON [dbo].[GR_RefersTo_US]([GID_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_Related_GR]
    ON [dbo].[GR_RefersTo_US]([GID_GR] ASC);

