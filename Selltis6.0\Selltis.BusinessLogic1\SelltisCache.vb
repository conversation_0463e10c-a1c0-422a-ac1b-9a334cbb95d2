﻿Imports Microsoft.VisualBasic
Imports System.Web.Caching
Imports System.IO
Imports System.Web

Public NotInheritable Class clCache

    Public Shared Sub AddtoCache(ByVal Key As String, ByVal Data As Object)
        'Try
        Key = PrepareKey(Key)
            Dim cacheddata As Object
            cacheddata = GetItemFromCache(Key)
            If cacheddata Is Nothing Then
                HttpRuntime.Cache.Insert(Key, Data)
                'WriteLog("Insert into cache with key =" & Key & " & value = " & Data)
            End If
        'Catch ex As Exception

        'End Try
    End Sub

    Public Shared Function GetItemFromCache(ByVal Key As String) As Object
        'Try
        Key = PrepareKey(Key)
            Dim cacheddata As Object
            cacheddata = HttpRuntime.Cache(Key) 'CStr(HttpRuntime.Cache(Key))
            If Not cacheddata Is Nothing Then
                'WriteLog("read data from cache with key =" & Key)
                Return cacheddata
            Else
                Return Nothing
            End If
        'Catch ex As Exception
        '    Return Nothing
        'End Try
    End Function

    Public Shared Sub RemoveItemFromCache(ByVal Key As String)
        'Try
        Key = PrepareKey(Key)
            Dim cacheddata As Object
            cacheddata = GetItemFromCache(Key)
            If Not cacheddata Is Nothing Then
                HttpRuntime.Cache.Remove(Key)
            End If
        'Catch ex As Exception

        'End Try
    End Sub

    Public Shared Function MakeCacheKey(section As String, page As String) As String

        ''Dim sHostingEnvironment As String = System.Configuration.ConfigurationManager.AppSettings("HostingEnvironment").ToString().ToLower()
        ''Dim sHostName As String = ""
        'If sHostingEnvironment = "debugging" Then
        '    sHostName = "default"
        'ElseIf sHostingEnvironment = "staging" Then
        '    sHostName = HttpContext.Current.Request.Url.Host.ToString().ToLower() + "_" + HttpContext.Current.Request.Url.Port.ToString()
        'Else
        '    sHostName = HttpContext.Current.Request.Url.Host.ToString().ToLower()
        'End If
        Dim sHostName As String = GetHostName()
        Return sHostName & "|" & section & "|" & page

        ''Return section & "|" & page
    End Function

    Public Shared Function IsCachable(section As String, page As String) As Boolean
        'WriteLog(section & "|" & page)

        Dim UseCache As Boolean
        Dim sHostName As String = clSettings.GetHostName()
        'Boolean.TryParse(System.Configuration.ConfigurationManager.AppSettings("UseCache"), UseCache)
        If Not DirectCast(HttpContext.Current.Session(sHostName + "_SiteSettings"), DataTable).Rows(0)("UseCache") = Nothing Then
            Boolean.TryParse(DirectCast(HttpContext.Current.Session(sHostName + "_SiteSettings"), DataTable).Rows(0)("UseCache"), UseCache)
        Else
            UseCache = False
        End If

        If UseCache = False Then
            Return False
        End If

        Dim pageType As String
        pageType = Left(page, 3).ToLower()

        If pageType = "alt" Or pageType = "pta" Or pageType = "snd" Or pageType = "snq" Or pageType = "age" Or pageType = "oth" Then
            Return False
        End If

        If section = "GLOBAL" Or String.IsNullOrEmpty(section) Then
            Return True
        Else
            Return False
        End If

    End Function

    Public Shared Function IsCachable(sSQLString As String, Optional ByVal TableName As String = "", Optional ByVal Var1 As String = "") As Boolean
        'WriteLog(section & "|" & page)
        'Var1 -- is a dummy parameter to make the signature different from the other overloaded function
        Dim UseCache As Boolean
        'Boolean.TryParse(System.Configuration.ConfigurationManager.AppSettings("UseCache"), UseCache)
        If Not DirectCast(HttpContext.Current.Session(clSettings.GetHostName() + "_SiteSettings"), DataTable).Rows(0)("UseCache") = Nothing Then
            Boolean.TryParse(DirectCast(HttpContext.Current.Session(clSettings.GetHostName() + "_SiteSettings"), DataTable).Rows(0)("UseCache"), UseCache)
        Else
            UseCache = False
        End If



        If UseCache = False Then
            Return False
        End If

        If sSQLString.ToLower().Contains("select") _
            And (sSQLString.ToLower().Contains("from [xm]") Or sSQLString.ToLower().Contains("from [xf]")) _
                 And (String.IsNullOrEmpty(TableName) = False And (TableName.ToLower() = "xm" Or TableName.ToLower() = "xf")) Then
            Return True
        ElseIf sSQLString.ToLower().Contains("pgetcurrentmdversion") Then
            Return True
        ElseIf sSQLString.ToLower().Contains("pgettables") Then
            Return True
        ElseIf sSQLString.ToLower().Contains("pgetfields") Then
            Return True
        ElseIf sSQLString.ToLower().Contains("pgetlinks") Then
            Return True
        ElseIf sSQLString.ToLower().Contains("pgetlists") Then
            Return True
        ElseIf sSQLString.ToLower().Contains("pgetactivemenuautomators") Then
            Return True
        ElseIf sSQLString.ToLower().Contains("pgetactivebtnautomators") Then
            Return True
        Else
            Return False
        End If

    End Function

    Public Shared Sub ClearCache()
        'Try
        Dim sHostName As String = GetHostName()
            Dim cacheitems = HttpRuntime.Cache.GetEnumerator()

            While cacheitems.MoveNext
                If cacheitems.Key.ToString().ToLower().Contains(sHostName.ToLower()) Then
                    HttpContext.Current.Cache.Remove(cacheitems.Key)
                End If
            End While
        '' WriteLog("Clears the cache")
        'Catch ex As Exception

        'End Try
    End Sub

    Public Shared Sub WriteLog(data As String)
        'Return
        'Try
        Dim sHostName As String = GetHostName()

            Dim sLogFilePath As String = System.Configuration.ConfigurationManager.AppSettings("LogFilePath").ToString().ToLower()

            If (Not System.IO.Directory.Exists(sLogFilePath & "LogFiles")) Then
                System.IO.Directory.CreateDirectory(sLogFilePath & "LogFiles")
            End If

            If (Not System.IO.Directory.Exists(sLogFilePath & "LogFiles\" & sHostName)) Then
                System.IO.Directory.CreateDirectory(sLogFilePath & "LogFiles\" & sHostName)
            End If

            Dim _DestinationPath As String = sLogFilePath & "LogFiles\" & sHostName & "\CacheLog_" + DateTime.Now.ToString("dd-MM-yyyy") + ".txt"

            If (File.Exists(_DestinationPath)) Then
                Using sw As StreamWriter = File.AppendText(_DestinationPath)
                    sw.WriteLine(vbCrLf & DateTime.Now & " :" & vbCrLf & data)
                    sw.Flush()
                    sw.Close()
                End Using
            Else
                Using sw As StreamWriter = File.CreateText(_DestinationPath)
                    sw.WriteLine(vbCrLf & DateTime.Now & " :" & vbCrLf & data)
                    sw.Flush()
                    sw.Close()
                End Using
            End If
        'Catch ex As Exception

        'End Try
    End Sub

    Private Shared Function GetHostName() As String
        Dim sHostingEnvironment As String = System.Configuration.ConfigurationManager.AppSettings("HostingEnvironment").ToString().ToLower()
        Dim sHostName As String = ""
        If sHostingEnvironment = "debugging" Then
            sHostName = "default"
        ElseIf sHostingEnvironment = "staging" Then
            sHostName = HttpContext.Current.Request.Url.Host.ToString().ToLower() + "_" + HttpContext.Current.Request.Url.Port.ToString()
        Else
            sHostName = HttpContext.Current.Request.Url.Host.ToString().ToLower()
        End If
        Return sHostName
    End Function

    Private Shared Function PrepareKey(key As String) As String
        Return GetHostName() + "_" + key
    End Function

End Class
