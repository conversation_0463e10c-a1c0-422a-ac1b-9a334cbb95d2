﻿using System;

//Owner MI as of 4/4/06 was CS previously

namespace Selltis.BusinessLogic
{
	public class clC
	{
		//MI 10/1/13 Added SELL_EXPIRE_PASSWORD_MAX_DAYS.
		//MI 7/12/10 Added SELL_BLANK_SYSDATETIME.
		//MI 3/25/10 Added SELL_SUIDxxxxxx.
		//MI 12/10/09 Added SELL_GROUPBY, SELL_COUNT, SELL_VIEW_CHART.
		//MI 10/9/09 added SELL_MAX_LOGINATTEMPTS and SELL_FAILEDLOGINTIMEOUTMINS.
		//MI 3/9/09 Added SELL_LinkProcessModes.
		//MI 2/11/09 Added SELL_SYSTEMFORMAT.
		//MI 11/4/08 Added SELL_LINKSTOP
		//MI 8/28/08 Added SELL_EXTRACT_BETWEENSINGLEQUOTES
		//MI 7/17/07 Added SELL_FORM_COLUMNWIDTHDEF, SELL_FORM_COLUMNLBLWIDTHDEF
		//MI 3/5/07 Added SELL_LOGLINE_MAXLENGTH. Changed SELL_METAVALUELIMIT from 4000 to 3800.
		//MI 2/28/07 Added Control state section.
		//MI 9/20/06 Added SELL_FIELDTYPE_GID
		//MI 9/12/06 Added SELL_SQLSTRING.
		//MI 9/11/06 Added HISTORY constants for WT.
		//MI 8/28/06 Added MessageBox constants.
		//MI 7/25/06 Added SELL_FIELDTYPE constants.
		//7/17/06    MI  Entered new constants for RH. Changed SELL_EARLIEST_DATE to SELL_BLANK_DATETIME.
		//4/27/06    MI  Modified SELL_BLANK_DATETIME to 1/2/1753 to leave 1/1/1753 for recording date-less times.

		//Public constants from selltis.wl    

		public const string SELL_FORMAT_DATETIME = "yyyy-MM-dd HH:mm:ss.fff";

		//SELL_BLANK_DATETIME represents a date and time with no value. A blank datetime
		//could be expressed as NULL in SS, but .net doesn't
		//support NULLs in datetime variables. The value is set to 1 second before
		//midnight of 1/3/1753. This indicates blank date and blank time.
		//A value of 1753-01-03 00:00:00.000 is 1/3/1753 at midnight, a supported
		//date and time.
		//
		//The reason for 1/2/1753 is to leave the whole day of 1/1/1753 open
		//for recording date-less times. Dateless times raise many issues are this notion
		//is not supported as of 7/17/06.
		//
		//You can't express a date with a blank time. All-day appts
		//for example have a time span from 00:00:00.000 to 23:59:59.996. SellSQL
		//doesn't support the notion of untimed appointments, as NGP did. This greatly
		//facilitates filtering and sorting since time is always part of the datetime
		//date type.
		//
		//The reason for the blank datetime value being 1 second before midnight and not
		//1 millisecond before is that datetimes passed as strings lose milliseconds,
		//so testing If dtDate = clC.SELL_BLANK_DATETIME wouldn't catch such values.
		public const string SELL_BLANK_DATETIME = "1753-01-02 23:59:59.000"; //not "1753-01-01 00:00:00.000"
		public const string SELL_BLANK_SYSDATETIME = "1753-01-02|23:59:59.000";
		public static readonly DateTime SELL_BLANK_DTDATETIME = new DateTime(1753, 1, 2, 23, 59, 59);

		// FORMATS
		//Use this in the par_sFormat parameter of goTr.StringToXxx or XxxToString methods
		//when you are parsing a number, date, time, currency value as text and you need 
		//the format to stay consistent and predictable.
		//The 'system' format is the way SQL displays and accepts non-string values in queries,
		//for example numbers as '-24553456.42775', datetimes as '2010-04-23 23:54:21.698', etc.
		public const string SELL_SYSTEMFORMAT = "SYSTEM";
		// DEFAULT FORMATS
		public const string SELL_FORMAT_TIMEDEF = "HH:mm:ss.fff";
		public const string SELL_FORMAT_DATEDEF = "yyyy-MM-dd";
		// FORMATS FOR RETURNED DATA TYPES
		public const int SELL_FORMAT_STRING = 0;
		public const int SELL_FORMAT_SYSTEM = 1;
		public const int SELL_FORMAT_SECOND = 2;
		public const int SELL_FORMAT_MINUTE = 3;
		public const int SELL_FORMAT_TIME = 4;
		public const int SELL_FORMAT_DATE = 5;
		public const int SELL_FORMAT_LONGDATE = 6;
		public const int SELL_FORMAT_NUM = 7;
		public const int SELL_FORMAT_CURR = 8;
		public const int SELL_FORMAT_HUNDREDTH = 9;

		//FIELD TYPES
		public const int SELL_FIELDTYPE_OTHER = 0;
		public const int SELL_FIELDTYPE_MEMO = 1;
		public const int SELL_FIELDTYPE_NUMERIC = 2;
		public const int SELL_FIELDTYPE_DATETIME = 3;
		public const int SELL_FIELDTYPE_GID = 4;

		//To be removed after WT reads MAXHISTORYITEMS from POP_PERSONAL_OPTIONS
		public const int SELL_HISTORY_MAXITEMS = 20;
		public const int SELL_HISTORY_MAXFORMS = 5;

		//LOGIN
		//Public Const SELL_MAX_LOGINATTEMPTS As Integer = 5          'MI 10/9/09
		//Public Const SELL_FAILEDLOGINTIMEOUTMINS As Integer = 5          'MI 10/9/09
		//XU count is multiplied by this number to determine max allowed number of failed login attempts workgroup-wide per period set with SELL_GROUPLOGINFAILURETIMEOUTMINUTES
		public const int SELL_MAXFAILEDTOTALLOGINATTEMPTSMULTIPLIER = 3;
		//Maximum number of failed user login attempts per period set with SELL_USERLOGINFAILURETIMEOUTMINUTES
		public const int SELL_MAXFAILEDUSERLOGINATTEMPTS = 5; //MI 10/9/09
		//Number of minutes during which failed user login attempts that exceed SELL_MAXFAILEDUSERLOGINATTEMPTS will cause a lockout
		public const int SELL_USERLOGINFAILURELOCKOUTMINUTES = 5; //MI 10/13/09
		//Number of minutes during which workgroup-wide failed attempts that exceed SELL_MAXFAILEDTOTALLOGINATTEMPTSMULTIPLIER will cause a lockout
		public const int SELL_GROUPLOGINFAILURELOCKOUTMINUTES = 5; //MI 10/13/09

		// VARIOUS
		public const int SELL_EXPIRE_PASSWORD_MAX_DAYS = 3700; //MI 10/1/13
		public const string SELL_ALT_RUNSCRIPT = "RUNSCRIPT";
		public const string SELL_AGE_OPENNDBFORM = "OPENNDBFORM";
		public const int SELL_MESS_SHOWALERT = 10000;
		public const string SELL_ALERT_SHAREMEM = "SELLTIS_ALERT_SHAREMEM";
		public const string SELL_SELLTIS_DIALOG = "SELLTIS_DIALOG";
		public const string SELL_SELLTIS_ALERT = "SELLTIS_ALERT";
		public const string SELL_DEFAULTFONT_NAME = "Tahoma";
		public const int SELL_DEFAULTFONT_SIZE = 8;
		public const int SELL_MAX_INDEX = 999999999;
		public const string SELL_ROWSET_TYPE = "TYPE";
		public const string SELL_ROWSET_FIELDS = "FIELDS";
		public const string SELL_ROWSET_CONDITION = "CONDITION";
		public const string SELL_ROWSET_SORT = "SORT";
		public const string SELL_ROWSET_TOP = "TOP";
		public const string SELL_ROWSET_TABLE = "TABLE";
		public const string SELL_DUPLICATEVALUE_PARAM = "PARAM";
		public const string SELL_DUPLICATEVALUE_FILE = "FILE";
		public const string SELL_DUPLICATEVALUE_KEYNAME = "KEY";
		public const string SELL_AMODE_EDITFORM = "EDIT";
		public const string SELL_AMODE_CREATELINKED = "CREATELINKEDBYID";
		public const string SELL_AMODE_CREATEUNLINKED = "CREATEUNLINKED";
		public const string SELL_AMODE_CREATEBLANK = "CREATEBLANK";
		public const string SELL_DELETE_SILENT = "DeleteSilent";
		public const string SELL_CANCEL_SILENT = "CancelSilent";
		public const string SELL_SAVEANDCLOSE_SILENT = "SaveAndCloseSilent";
		public const string SELL_SAVENOCLOSE_SILENT = "SaveNoCloseSilent";
		public const int SELL_ARRAY_LINKS = 1;
		public const int SELL_ARRAY_FIELDS = 2;
		public const int SELL_ARRAY_FILES = 3;
		public const int SELL_FILTER_CONDITION = 1;
		public const int SELL_FILTER_SORTORDER = 2;
		public const string SELL_COL_ADDED = "A";
		public const string SELL_COL_DELETED = "D";
		public const int SELL_FORM_DISPLAY = 0;
		public const int SELL_FORM_REDISPLAY = 1;
		public const int SELL_FORM_COLUMNWIDTHDEF = 200;
		public const int SELL_FORM_COLUMNLBLWIDTHDEF = 100;
		public const int SELL_FRIENDLY = 1;
		public const int SELL_SYSTEM = 2;
		public const int SELL_DISPLAYED = 3;
		public const int SELL_SQLSTRING = 4; //*** MI 9/12/06

		//WT 12/16/13
		public const string SELL_AGE_OPENCVS = "OPENCVS";
		public const string SELL_AGE_OPENFRF = "OPENFRF";
		public const int SELL_FORM_TYPE_NDB = 1;
		public const int SELL_FORM_TYPE_CVS = 2;
		public const int SELL_FORM_TYPE_FRF = 3;

		public const int SELL_EDIT = 1; //Edit type rowset
		public const int SELL_ADD = 2; //Add type rowset
		public const int SELL_READONLY = 3; //Add type rowset
		public const int SELL_GROUPBY = 4; //MI 12/10/09 added
		public const int SELL_GROUPBYWITHROLLUP = 5; //MI 1/8/10 added
		public const int SELL_COUNT = 6; //MI 12/10/09 added
		public const int SELL_READONLY_ALL = 7; //VT 5/22/23 Add type rowset

		public const int SELL_RESULT_ERROR = 0;
		public const int SELL_RESULT_OK = 1;
		public const int SELL_RESULT_ABORT = 2;
		public const int SELL_RESULT_CANCEL = -1;
		public const string SELL_USER_ALL = "ALL";
		//ACTION TO TAKE
		public const int SELL_FILE_ADD = 1;
		public const int SELL_FILE_DELETE = 2;
		//Alerts
		public const string SELL_ALT_OPENRECORD = "OPENRECORD";
		public const string SELL_ALT_OPENDESKTOP = "OPENDESKTOP";
		public const string SELL_ALT_OPENURL = "OPENURL";
		public const string SELL_ALT_OPENURLEXTERNAL = "OPENURLEXTERNAL";
		public const string SELL_ALT_OPENNEWFORM = "OPENNEWFORM";
		public const string SELL_ALT_OPENNEWNDBFORM = "OPENNEWNDBFORM";
		//Licences
		public const int SELL_LICENCE_LAN = 1;
		public const int SELL_LICENCE_SYNC = 2;
		public const int SELL_LICENCE_CARDLINK = 3;
		public const int SELL_LICENCE_OUTLOOKPERS = 4;
		public const int SELL_LICENCE_OUTLOOKWRK = 5;
		//Lists
		public const int SELL_LISTNAME = 1;
		public const int SELL_ITEMKEY = 2;
		public const int SELL_ITEMVALUE = 3;
		// SELLTRAN MESSAGES
		public const int SELL_TRAN_ERROR = 0;
		public const int SELL_TRAN_TOOLTIP = 1;
		public const int SELL_TRAN_PROCESS = 2;
		// ALERT FIRE TIMER EVERY
		public const int SELL_ALERT_EVERY_NMINUTES = 1;
		public const int SELL_ALERT_EVERY_HOUR = 2;
		public const int SELL_ALERT_EVERY_DAY = 3;
		public const int SELL_ALERT_EVERY_WEEK = 4;
		public const int SELL_ALERT_EVERY_WEEKDAY = 5;
		public const int SELL_ALERT_EVERY_MONTH = 6;
		// DB ACCESS MODE
		public const string SELL_DBMODE_NODB = ""; //no DB opened
		public const string SELL_DBMODE_LAN = "LAN"; //Access to the main DB Via the network
		public const string SELL_DBMODE_SYNC = "SYNC"; //Sync client (local DB part of a workgroup)
		public const string SELL_DBMODE_STANDALONE = "STDALONE"; //Local DB not part of a workgroup
		public const string SELL_DBMODE_SERVER = "SERVER"; //used when accessing locally the server DB (mainly sync engine)
		// TYPEs OF USER/PERMISSIONS
		public const int SELL_AUTHOR_NONE = 0;
		public const int SELL_AUTHOR_PARTIAL = 1;
		public const int SELL_AUTHOR_FULL = 2;
		public const int SELL_ADMIN = 3;
		public const int SELL_CREATENEWDB = 4;
		public const int SELL_BACKUP = 6;
		public const int SELL_IMPORT = 7;
		public const int SELL_EXPORT = 8;
		public const int SELL_TRANSFERIN = 9;
		public const int SELL_TRANSFEROUT = 10;
		public const int SELL_PERIODICBILLING = 11;
		public const int SELL_REIMBURSE = 12;
		public const int SELL_APPROVEREIMBURSE = 13;
		public const int SELL_PRINT = 14;
		public const int SELL_SEND = 18;
		public const int SELL_DEFAULT = 9;
		public const int SELL_NO = 0;
		public const int SELL_SELECTIVE = 1;
		public const int SELL_FULL = 2;
		public const int SELL_READ = 1;
		public const int SELL_WRITE = 2;
		public const int SELL_DELETE = 3;
		// FIELD ICONS
		public const int SELL_MAX_FIELD_ICONS = 1000;
		// MLS LIST
		public const int SELL_MAX_LIST_ITEMS = 100;
		public const int SELL_MAX_AUTOMATOR_ACTIONS = 99;
		public const string SELL_SHARED_CHAR = "*";
		// FILTER SETTINGS
		public const string SELL_CUSTOMORDER = "Custom";
		public const string SELL_REVERSEORDER = "1";
		public const string SELL_NORMALORDER = "0";
		public const string SELL_ASCENDING = "1";
		public const string SELL_DESCENDING = "2";
		// DATE/TIME MANAGEMENT
		public const int SELL_TYPE_FIXE = 0;
		public const int SELL_TYPE_KEYWORD = 1;
		public const int SELL_TYPE_ABBREV = 2;
		public const int SELL_TYPE_INVALID = 3;
		public const int SELL_TYPE_VALID = 4;
		// Passwordmanagement
		public const int SELL_PASSWORD_INCLEAR = 0;
		public const int SELL_PASSWORD_CRYPTED = 1;
		// SHARING STATE OF A RECORD
		// do NOT replace the value 0/1/2 by strings
		// it's stored as a SI in the file
		public const int SELL_LOCAL = 0;
		public const int SELL_PROTECTED = 1;
		public const int SELL_SHARED = 2;
		// TYPE OF RECORD TO CREATE
		public const string SELL_NOTSPECIFIED = "NOT SPECIFIED";
		public const string SELL_LEAD = "LEAD";
		public const string SELL_NOTE = "NOTE";
		public const string SELL_CORRESPONDANCE = "CORR";
		public const string SELL_EMAIL = "EMAIL";
		public const string SELL_LETTER = "LETTER";
		public const string SELL_FAX = "FAX";
		// CALLING MODES FOR McreateFormLinked
		// and MCreateFormUnLinked
		public const int SELL_FROMMAIN = 0;
		public const int SELL_FROMFORM = 1;
		// LINKBOX
		//OPENING MODES
		//ON THE FORM
		public const int SELL_LBOX_COMBOLIKE = 1;
		public const int SELL_LBOX_LISTBOX = 2;
		public const int SELL_LBOX_TABLE = 3;
		//IN THE SELECTOR
		public const int SELL_LBOX_ONEPANE = 4;
		public const int SELL_LBOX_TWOPANES = 5;
		public const int SELL_LBOX_TWOPANESALL = 6;
		//PARAMETERS REQUESTS 
		public const int SELL_LBOX_FILTER = 10;
		public const int SELL_LBOX_COLFORM = 11;
		public const int SELL_LBOX_COLSEL = 12;
		//MISC 
		public const int SELL_LBOX_FORM = 1;
		public const int SELL_LBOX_SELECTOR = 2;
		public const int SELL_FROMMEMORY = 1;
		// GETLASTERROR
		public const string SELL_ERROR_NUMBER = "NUMBER";
		public const string SELL_ERROR_MESSAGE = "MESSAGE";
		public const string SELL_ERROR_PROC = "PROCEDURE";
		public const string SELL_ERROR_PARAMS = "PARAMS";
		// GETLINEVALUE, GETLINEELEMENT, GETLINEPART:
		// String Extraction
		public const int SELL_EXTRACT_OUT = 0;
		public const int SELL_EXTRACT_PERHAPSENTERING = 1;
		public const int SELL_EXTRACT_ENTERING = 2;
		public const int SELL_EXTRACT_IN = 3;
		public const int SELL_EXTRACT_PERHAPSQUITTING = 4;
		public const int SELL_EXTRACT_QUITTING = 5;
		public const int SELL_EXTRACT_BETWEENQUOTES = 6;
		public const int SELL_EXTRACT_ONASPACE = 7;
		public const int SELL_EXTRACT_BETWEENSINGLEQUOTES = 8;
		//These are for GetLineElement:
		public const string SELL_EXTRACT_SEP = "S";
		public const string SELL_EXTRACT_FIELD = "F";
		// LINKS
		public const string SELL_LINK_1TON = "1N";
		public const string SELL_LINK_NTO1 = "N1";
		public const string SELL_LINK_NTON = "NN";
		// FUNCTIONING MODES
		public const string SELL_MODE_MAIN = "SellMain";
		public const string SELL_MODE_API = "API";
		public const string SELL_MODE_APIDLL = "APIDLL";
		public const string SELL_MODE_NEWINSTANCE = "SellInstance";
		public const string SELL_MODE_SEARCH = "Search";
		public const string SELL_MODE_SYNC = "Sync";
		public const string SELL_MODE_AUTO = "Automator";
		public const string SELL_MODE_ADMINCONS = "AdminCons";
		public const string SELL_MODE_MODFILE = "ModFile";
		public const string SELL_MODE_DDESERV = "DDEServ";
		public const string SELL_MODE_TOEXCEL = "ToExcel";
		public const string SELL_MODE_SELLSEND = "SellSend";
		public const string SELL_MODE_WEB = "SellWeb";
		public const string SELL_MODE_CARDSCAN = "CardScanLink";
		public const string SELL_MODE_OUTLOOK = "OutlookLink";
		public const string SELL_MODE_PALM = "PalmLink";
		public const string SELL_MODE_SELLTRAN = "Tran";
		public const string SELL_MODE_FORM = "Form";
		public const string SELL_MODE_IMPORT = "Import";
		public const string SELL_MODE_ROWSET = "Rowset";
		public const string SELL_MODE_SPLA = "SellSpla";
		public const string SELL_MODE_ALERT = "Alert";
		// CONDUITS
		public const string SELL_CONDUIT_OUTLOOKCAL = "OUTLOOKCAL";
		public const string SELL_CONDUIT_OUTLOOKCONTACT = "OUTLOOKCONTACT";
		public const string SELL_CONDUIT_PALMADDRESS = "PALMADDRESSBOOK";
		public const string SELL_CONDUIT_PALMDATE = "PALMDATEBOOK";
		public const string SELL_CONDUIT_PALMMEMO = "PALMMEMO";
		public const string SELL_CONDUIT_PALMTODO = "PALMTODO";
		// File types (in PRNTEMPL)
		public const int SELL_TEMPLATE_LIST = 1; //Template HTML for lists
		public const int SELL_TEMPLATE_ONERECORD = 2; //Template HTML used for Forms, multi-fields or single-field
		public const int SELL_TEMPLATE_WEEKCAL = 3; //Template HTML for week calendars
		public const int SELL_TEMPLATE_MONTHCAL = 4; //Template HTML for month calendars
		public const int SELL_TEMPLATE_DAYCAL = 5; //Template HTML for day calendars
		// Miscellaneous
		public const int SELL_HISTORY_MAXCHAR = 4000; //How many characters to keep in MMO_History fields.
		public const int SELL_MEMOLIMIT = 32000; //Maximum size for memos
		public const int SELL_METALIMIT = 1000000000; //Maximum size for meta's memo (not direct edition) was 64000
		public const int SELL_METAVALUELIMIT = 3800; //Maximum size for meta line's value (nvarchar(4000) in SS)
		public const int SELL_COMBOLIMIT = 6500; //Max no of elements in combos and listboxes
		public const int SELL_MINIMUMPACKET = 50; //Minimum sync packet size is 50 Ko
		// SellTrace
		public const int SELLTRACE_USER = 0;
		public const int SELLTRACE_ALL = 1;
		public const int SELLTRACE_OFF = 2;
		public const int SELLTRACE_USER_AND_EMPTY = 3;
		// Calendar
		public const int CAL_TIMEBAR_YES = 1;
		public const int CAL_TIMEBAR_NO = 2;
		public const int CAL_TIMEBAR_IFNEEDED = 3;
		public const int CAL_SELECTFIRSTDATE = 1;
		public const int CAL_SELECTLASTDATE = 2;
		public const int CAL_SELECTNOCHANGE = 3;
		// View types
		// Supported view types (Typically used in gwsVals, etc.)
		public const string SELL_FORM = "FORM"; //Form window
		public const string SELL_RECORD = "RECORD"; //One record from a list view
		public const string SELL_VIEW_LIST = "LIST"; //List view
		public const string SELL_VIEW_LISTFILTER = "LISTFILTER"; //List Filter view
		public const string SELL_VIEW_LISTREPORT = "LISTREPORT"; //List report view
		public const string SELL_VIEW_DAYCAL = "CALDAY"; //Day Calendar view
		public const string SELL_VIEW_WEEKCAL = "CALWEEK"; //Week Calendar view
		public const string SELL_VIEW_MONTHCAL = "CALMONTH"; //Month Calendar view
		public const string SELL_VIEW_YEARCAL = "CALYEAR"; //Year Calendar view
		public const string SELL_VIEW_MFIELD = "MFIELD"; //Multi-field view
		public const string SELL_VIEW_SFIELD = "SFIELD"; //Single-field view
		public const string SELL_VIEW_CHART = "CHART"; //Chart view 'MI 12/10/09 added
		public const int SELL_VIEW_DEFAULTTOP = 10; //The 'First' no of lines in views
		public const string SELL_LINKBOX_CONTROL = "LINKBOX_CONTROL"; //Linkbox control on a form
		public const int SELL_LISTVIEW_SHOWTOPMAX = 1000; //Max number of recs supported in list view
		public const int SELL_REPORTVIEW_SHOWTOPMAX = 10000; //Max number of recs supported in report view
		public const int SELL_CHARTVIEW_SHOWTOPMAX = 10000; //Max number of recs supported in chart view
		public const int SELL_VIEWPRINT_MAXRECORDS = 10000; //Max number of recs supported in list and report view printing and sending to Excel
		// Event logging
		public const int SELL_LOGLEVEL_NONE = 0;
		public const int SELL_LOGLEVEL_STANDARD = 1;
		public const int SELL_LOGLEVEL_DETAILS = 2;
		public const int SELL_LOGLEVEL_DEBUG = 3;
		public const int SELL_LOGLINE_MAXLENGTH = 7600; //Size of XL.TXT_Message field
		// TimeZone management [FH]
		public const int TIME_ZONE_ID_DAYLIGHT = 2;
		public const int TIME_ZONE_ID_STANDARD = 1;
		public const int TIME_ZONE_ID_UNKNOWN = 0;
		//Return values:
		public const int IDABORT = 3;
		public const int IDCANCEL = 2;
		public const int IDIGNORE = 5;
		public const int IDNO = 7;
		public const int IDOK = 1;
		public const int IDRETRY = 4;
		public const int IDYES = 6;

		//This is a native constant in Windev
		public static readonly char EOT = (char)4;

		//From clTransform 
		public const int SELL_YEAR_END = 49; //used in CompleteYear()

		//Error management
		public const string EX_THREAD_ABORT_MESSAGE = "Thread was being aborted.";

		//MessageBox
		//Type:
		public const int SELL_MB_ABORTRETRYIGNORE = 0x2;
		public const int SELL_MB_OK = 0x0;
		public const int SELL_MB_OKCANCEL = 0x1;
		public const int SELL_MB_RETRYCANCEL = 0x5;
		public const int SELL_MB_YESNO = 0x4;
		public const int SELL_MB_YESNOCANCEL = 0x3;
		public const int SELL_MB_INPUTBOX = 0x8;
		//Icons:
		public const int SELL_MB_ICONBLANK = 0x0;
		public const int SELL_MB_ICONEXCLAMATION = 0x30;
		public const int SELL_MB_ICONWARNING = 0x30;
		public const int SELL_MB_ICONINFORMATION = 0x40;
		public const int SELL_MB_ICONASTERISK = 0x40;
		public const int SELL_MB_ICONQUESTION = 0x20;
		public const int SELL_MB_ICONSTOP = 0x10;
		public const int SELL_MB_ICONERROR = 0x10;
		public const int SELL_MB_ICONHAND = 0x10;
		//Default buttons:
		public const int SELL_MB_DEFBUTTON1 = 0x0;
		public const int SELL_MB_DEFBUTTON2 = 0x100;
		public const int SELL_MB_DEFBUTTON3 = 0x200;
		public const int SELL_MB_DEFBUTTON4 = 0x300;
		//Modality:
		public const int SELL_MB_APPLMODAL = 0x0;
		public const int SELL_MB_SYSTEMMODAL = 0x1000;
		public const int SELL_MB_TASKMODAL = 0x2000;
		//Other values:
		public const int SELL_MB_TOPMOST = 0x40000;

		// Automators
		public const string SELL_AGE_RUNSCRIPT = "RUNSCRIPT";
		public const string SELL_AGE_OPENRECORD = "OPENRECORD";
		public const string SELL_AGE_OPENDESKTOP = "OPENDESKTOP";
		public const string SELL_AGE_OPENURL = "OPENURL";
		public const string SELL_AGE_OPENURLEXTERNAL = "OPENURLEXTERNAL";

		//Control state (note different meanings for controls and linkboxes)
		public const int SELL_STATE_UNCHANGED = -1; //Control: unchanged. Linkbox: unchanged.
		public const int SELL_STATE_ACTIVE = 0; //Control: active. Linkbox: visible, click-through working, button active.
		public const int SELL_STATE_INACTIVE = 1; //Control: locked out. Linkbox: visible, click-through working, button active, but linkbox selector controls grayed out.
		public const int SELL_STATE_INVISIBLE = 2; //Control: invisible. Linkbox: invisible, button invisible.
		public const int SELL_STATE_GRAYED = 4; //Control: grayed out. Linkbox: grayed out, click-through not working, button grayed out.
		public const int SELL_STATE_LINKBUTTONGRAYED = 5; //Control: unchanged. Linkbox: visible, click-through working, button grayed out.
		public const int SELL_STATE_LINKBUTTONINVISIBLE = 6; //Control: unchanged. Linkbox: visible, click-through working, button invisible.

		//Links top default
		public const int SELL_LINKSTOP = 5; //no of linked recs returned by default in a view 'MI 11/4/08

		public const int SELL_DATERANGE_STARTTIME = 1;
		public const int SELL_DATERANGE_ENDTIME = 2;
		public const int SELL_DATERANGE_LABEL = 3;
		public const int SELL_DATERANGE_PERIOD = 4; //word usable in date phrases that denotes the range: day, week, month, quarter, year.

		//Mobile
		public const int SELL_MOBILEEXPIRELOGININDAYS = 365; //Default no of days in which to expire a mobile login cookie
		public const int SELL_MOBILEEXPIRELOGININDAYSMAX = 3650; //Max no of days for mobile login expiration.

		//Post-import processing modes
		public const int SELL_LinkProcessMode_ImportID = 1; //(existing mode)
		public const int SELL_LinkProcessMode_GID_ID = 2; //(by explicit GID_ID value)
		public const int SELL_LinkProcessMode_TN = 3; //(by Translation [TN] record)
		public const int SELL_LinkProcessMode_Mixed = 4; //(mix of the above 3 modes)

		public const string SELL_SUIDFakePreFile = "11111111-1111-1111-"; //Left portion of 'fake' Selltis unique ID (before the 4 chars that identify the file)
		public const string SELL_SUIDFakePostFile = "-111111111111"; //Right portion of 'fake' Selltis unique ID (after the 4 chars that identify the file)
		public const string SELL_SUIDAnyPostFile = "-111111111112"; //Right portion of '(Any)' Selltis unique ID (after the 4 chars that identify the file)





		public clC()
		{

		}
	}

}
