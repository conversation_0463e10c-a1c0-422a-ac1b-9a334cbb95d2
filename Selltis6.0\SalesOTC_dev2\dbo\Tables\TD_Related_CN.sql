﻿CREATE TABLE [dbo].[TD_Related_CN] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_ToDo_Related_Contact_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_CN] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_Related_CN] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CN_Connected_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_Related_CN] FOREIGN KEY ([GID_CN]) REFERENCES [dbo].[CN] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_Related_CN] NOCHECK CONSTRAINT [LNK_CN_Connected_TD];


GO
ALTER TABLE [dbo].[TD_Related_CN] NOCHECK CONSTRAINT [LNK_TD_Related_CN];


GO
CREATE CLUSTERED INDEX [IX_CN_Connected_TD]
    ON [dbo].[TD_Related_CN]([GID_TD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_Related_CN]
    ON [dbo].[TD_Related_CN]([GID_CN] ASC);

