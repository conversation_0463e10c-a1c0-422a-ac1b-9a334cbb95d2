Imports Microsoft.VisualBasic
Imports System.Data
Imports System.Web
Imports System.Web.Security
Imports System.Configuration

Public Class SelltisMembershipProvider
    Inherits MembershipProvider
    'OWNER: RH
    'MI 10/24/13 Deprecated deactivation date notion, commented code that tests deactivation.
    'MI 8/13/09 Added Return Nothing to GetUser() (plus one override function). 
    'MI 4/27/09 Bug fix: ValidateUser: A login without a User record linked or for which there is no User record caused a lockup during login. 


    '---for database access use---
    Private connStr As String
    Private comm As New OleDb.OleDbCommand        'Data.SqlClient.SqlCommand 

    Private _requiresQuestionAndAnswer As Boolean
    Private _minRequiredPasswordLength As Integer

    Public Overrides Sub Initialize(ByVal name As String, ByVal config As System.Collections.Specialized.NameValueCollection)

        '===retrives the attribute values set in 
        'web.config and assign to local variables===

        If config("requiresQuestionAndAnswer") = "true" Then _
            _requiresQuestionAndAnswer = True

        'Dim sConnectionString As String = ""
        'Dim sUrl As String
        'Dim sHostingEnvironment As String = ConfigurationManager.AppSettings("HostingEnvironment").ToString().ToLower()

        ''#If DEBUG Then
        ''        sConnectionString = "SelltisConnectionString"
        ''#End If

        'If sHostingEnvironment = "debugging" Then
        '    sConnectionString = "SelltisConnectionString"
        'End If

        'If String.IsNullOrEmpty(sConnectionString) Then

        '    If sHostingEnvironment = "staging" Then
        '        'site url is IP address , when hosted in local / test server
        '        sUrl = HttpContext.Current.Request.Url.Port.ToString()
        '    Else
        '        sUrl = HttpContext.Current.Request.Url.Host
        '        sUrl = sUrl.ToLower().Replace(".selltis.com", "")
        '    End If
        '    sConnectionString = sUrl & "_SelltisConnectionString"

        'End If

        'connStr = ConfigurationManager.ConnectionStrings(sConnectionString).ConnectionString 'config("connectionString")
        Dim sHostName As String = clSettings.GetHostName()
        If HttpContext.Current.Session(sHostName + "_SiteSettings") Is Nothing Then
            clSettings.LoadSiteSettings()
        End If

        connStr = DirectCast(HttpContext.Current.Session(sHostName + "_SiteSettings"), DataTable).Rows(0)("ConnectionString")

        MyBase.Initialize(name, config)
    End Sub

    Public Overrides Property ApplicationName() As String
        Get
            ApplicationName = Me.Name.ToString()
        End Get
        Set(ByVal value As String)
        End Set
    End Property

    Public Overrides Function ChangePassword(ByVal username As String, ByVal oldPassword As String, ByVal newPassword As String) As Boolean

    End Function

    Public Overrides Function ChangePasswordQuestionAndAnswer(ByVal username As String, ByVal password As String, ByVal newPasswordQuestion As String, ByVal newPasswordAnswer As String) As Boolean

    End Function

    Public Overrides Function CreateUser(ByVal username As String, ByVal password As String, ByVal email As String, ByVal passwordQuestion As String, ByVal passwordAnswer As String, ByVal isApproved As Boolean, ByVal providerUserKey As Object, ByRef status As System.Web.Security.MembershipCreateStatus) As System.Web.Security.MembershipUser

        Dim conn As New Data.SqlClient.SqlConnection(connStr)   'OleDb.OleDbConnection(connStr)

        '----perform checking all the relevant checks here
        ' and set the status of the error accordingly, e.g.:
        'status = MembershipCreateStatus.InvalidPassword
        'status = MembershipCreateStatus.InvalidAnswer
        'status = MembershipCreateStatus.InvalidEmail

        '---add the user to the database
        Try
            conn.Open()
            Dim sql As String = "INSERT INTO XU VALUES (" & _
                   "@username, @password, @email, " & _
                   " @passwordQuestion, @passwordAnswer )"
            Dim comm As New Data.SqlClient.SqlCommand(sql, conn)
            comm.Parameters.AddWithValue("@username", username)
            comm.Parameters.AddWithValue("@password", password)
            comm.Parameters.AddWithValue("@email", email)
            comm.Parameters.AddWithValue("@passwordQuestion", passwordQuestion)
            comm.Parameters.AddWithValue("@passwordAnswer", passwordAnswer)
            Dim result As Integer = comm.ExecuteNonQuery()
            conn.Close()

            status = MembershipCreateStatus.Success
            Dim user As New MembershipUser("SelltisMembershipProvider", username, Nothing, email, passwordQuestion, Nothing, True, False, Now, Nothing, Nothing, Nothing, Nothing)
            Return user
        Catch ex As Exception

            MsgBox(ex.ToString)
            '---failed; determine the reason why
            status = MembershipCreateStatus.UserRejected
            Return Nothing
        End Try
    End Function

    Public Overrides Function DeleteUser(ByVal username As String, ByVal deleteAllRelatedData As Boolean) As Boolean

    End Function

    Public Overrides ReadOnly Property EnablePasswordReset() As Boolean
        Get

        End Get
    End Property

    Public Overrides ReadOnly Property EnablePasswordRetrieval() As Boolean
        Get

        End Get
    End Property

    Public Overrides Function FindUsersByEmail(ByVal emailToMatch As String, ByVal pageIndex As Integer, ByVal pageSize As Integer, ByRef totalRecords As Integer) As System.Web.Security.MembershipUserCollection
        Dim ret As New System.Web.Security.MembershipUserCollection
        Return ret
    End Function

    Public Overrides Function FindUsersByName(ByVal usernameToMatch As String, ByVal pageIndex As Integer, ByVal pageSize As Integer, ByRef totalRecords As Integer) As System.Web.Security.MembershipUserCollection
        Dim ret As New System.Web.Security.MembershipUserCollection
        Return ret

    End Function

    Public Overrides Function GetAllUsers(ByVal pageIndex As Integer, ByVal pageSize As Integer, ByRef totalRecords As Integer) As System.Web.Security.MembershipUserCollection
        Dim ret As New System.Web.Security.MembershipUserCollection
        Return ret
    End Function

    Public Overrides Function GetNumberOfUsersOnline() As Integer

    End Function

    Public Overrides Function GetPassword(ByVal username As String, ByVal answer As String) As String
        Return ""
    End Function

    Public Overloads Overrides Function GetUser(ByVal username As String, ByVal userIsOnline As Boolean) As System.Web.Security.MembershipUser
        Return Nothing
    End Function

    Public Overloads Overrides Function GetUser(ByVal providerUserKey As Object, ByVal userIsOnline As Boolean) As System.Web.Security.MembershipUser
        Return Nothing
    End Function

    Public Overrides Function GetUserNameByEmail(ByVal email As String) As String
        Return ""

    End Function

    Public Overrides ReadOnly Property MaxInvalidPasswordAttempts() As Integer
        Get

        End Get
    End Property

    Public Overrides ReadOnly Property MinRequiredNonAlphanumericCharacters() As Integer
        Get

        End Get
    End Property

    Public Overrides ReadOnly Property MinRequiredPasswordLength() As Integer
        Get

        End Get
    End Property

    Public Overrides ReadOnly Property PasswordAttemptWindow() As Integer
        Get

        End Get
    End Property

    Public Overrides ReadOnly Property PasswordFormat() As System.Web.Security.MembershipPasswordFormat
        Get

        End Get
    End Property

    Public Overrides ReadOnly Property PasswordStrengthRegularExpression() As String

        Get
            Return ""

        End Get
    End Property

    Public Overrides ReadOnly Property RequiresQuestionAndAnswer() As Boolean
        Get
            If _requiresQuestionAndAnswer = True Then
                Return True
            Else
                Return False
            End If
        End Get
    End Property

    Public Overrides ReadOnly Property RequiresUniqueEmail() As Boolean
        Get

        End Get
    End Property

    Public Overrides Function ResetPassword(ByVal username As String, ByVal answer As String) As String
        Return ""
    End Function

    Public Overrides Function UnlockUser(ByVal userName As String) As Boolean

    End Function

    Public Overrides Sub UpdateUser(ByVal user As System.Web.Security.MembershipUser)

    End Sub

    Public Overrides Function ValidateUser(ByVal username As String, ByVal password As String) As Boolean
        'MI 11/2/11 Changed text of the login lockout message.
        'MI 10/9/09 Added login timeout on 5 failed login attempts.
        'MI 7/29/09 Added checking GLOBAL MOBILE perm if user doesn't have an explicit one. This fixes default Mobile permission not working.
        'MI 4/27/09 Bug fix: A login not linked to a User record caused a lockup during login. 
        'DVF 4/2/09 Added IP address to login logging.
        'MI 3/12/09 Added logging login success and failure.
        'MI 11/19/08 Added 'cus_' to 'Agreement.aspx'.
        'MI 5/16/08 Added brackets in SQL statement(s).
        'MI 5/4/07 Implemented redirecting to Agreement.aspx
        'MI 3/12/07 Modified to work with XU table

        'IMPORTANT: Don't just Return True, but GOTO LoginSucceeded!

        'Dim conn As New OleDb.OleDbConnection(connStr)
        Dim sProc As String = "clSelltisMembershipProvider::ValidateUser"
        Dim sID As String = HttpContext.Current.Session.SessionID

        Dim sMessage As String
        Dim conn As New Data.SqlClient.SqlConnection(connStr)
        Dim sClientIP As String
        Dim sTemp As String
        Dim dtNow As DateTime = Now
        Dim sNow As String
        Dim sql As String
        Dim sql2 As String
        Dim comm As Data.SqlClient.SqlCommand
        Dim comm2 As Data.SqlClient.SqlCommand
        Dim reader As Data.SqlClient.SqlDataReader = Nothing
        Dim reader2 As Data.SqlClient.SqlDataReader
        Dim iLogins As Integer
        Dim iFailureCount As Integer
        'Added to test case of password  RH 12/4/09
        Dim sPassword As String = password
        Dim bAgree As Boolean = True        'End
        Dim oMD As New clMembershipData
        Dim bAdminBypass As Boolean
        sClientIP = HttpContext.Current.Request.UserHostAddress
        conn.Open()
        oMD.WriteAuthenticationMessage(username, password, "")

        Try

            If HttpContext.Current.Session("sProduct") Is Nothing Then
                HttpContext.Current.Session("sProduct") = "SA"
            Else
                If HttpContext.Current.Session("sProduct").ToString = "" Then
                    HttpContext.Current.Session("sProduct") = "SA"
                End If
            End If

            SetSession(conn, HttpContext.Current.Session("sProduct"))
            HttpContext.Current.Session("LASTLOGINFAILUREREASON") = ""
            HttpContext.Current.Session("PASSWORD") = password


            '---------------- MI 10/14/09 login lockout changes ---------------
            'Determine number of logins (users who can log in)
            sql = "Select COUNT(*) From [XU] " & _
                "WHERE [CHK_LoginGroup] IS null or [CHK_LoginGroup] = 0"
            comm = New Data.SqlClient.SqlCommand(sql, conn)
            iLogins = comm.ExecuteScalar()
            If iLogins < 1 Then iLogins = 1

            'Count total workgroup-wide number of failures within last 5 minutes. Disallow login if necessary.
            'This is done to slow down brute force attacks. We can add notifying the admin here.
            sql = "SELECT COUNT(*) FROM [XL] WHERE([SI__Purpose] = 2 And [DTT_CreationTime] > DateAdd(Minute, -" & clC.SELL_GROUPLOGINFAILURELOCKOUTMINUTES & ", GetUTCDate()))"
            comm = New Data.SqlClient.SqlCommand(sql, conn)
            iFailureCount = comm.ExecuteScalar()
            If iFailureCount >= iLogins * clC.SELL_MAXFAILEDTOTALLOGINATTEMPTSMULTIPLIER Then
                'DEBUG
                'If iFailureCount > 8 Then
                sMessage = "Workgroup logins locked out: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE)
                'Communicate back to login.aspx the reason for login failure. We don't want to alarm users nor reveal too much about why.
                'MI 11/2/11 Changed the text from "Try logging in after a few minutes."
                HttpContext.Current.Session("LASTLOGINFAILUREREASON") = "The system is temporarily locked out for security reasons. Try again in a few minutes."
                Return False
            End If

            'Count login failures of the user logging in. If > allowed number, 
            'lock the user out until enough time passes for the number to drop.
            sql = "SELECT COUNT(*) FROM [XL] WHERE([SI__Purpose] = 2) and [DTT_CreationTime] > DateAdd(minute, -" & clC.SELL_USERLOGINFAILURELOCKOUTMINUTES.ToString & ", GetUTCDate()) and TXT_Message LIKE 'Login failed: ''" & username & "''%' "
            comm = New Data.SqlClient.SqlCommand(sql, conn)
            iFailureCount = comm.ExecuteScalar()
            If iFailureCount >= clC.SELL_MAXFAILEDUSERLOGINATTEMPTS Then
                'User login failed more than the allowed no of times within the allowed period
                sMessage = "User login locked out: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE)
                'Communicate back to login.aspx the reason for login failure.
                'MI 11/2/11 Changed the text from "Try logging in after a few minutes."
                HttpContext.Current.Session("LASTLOGINFAILUREREASON") = "You are temporarily locked out for security reasons. Try again in a few minutes."
                Return False
            End If
            '------------------------------------------

            sNow = Right("0000" & dtNow.Year.ToString, 4) & "-" & Right("00" & dtNow.Month.ToString, 2) & "-" & Right("00" & dtNow.Day.ToString, 2) & " " & Right("00" & dtNow.Hour.ToString, 2) & ":" & Right("00" & dtNow.Minute.ToString, 2) & ":" & Right("00" & dtNow.Second.ToString, 2) & "." & Right("000" & dtNow.Millisecond.ToString, 3)
            ''Encrypt the connection string and set it in a session var
            'Dim oCrypt As New Crypto.SymmCrypto
            'oCrypt.SymmCrypto(Crypto.SymmCrypto.SymmProvEnum.DES)
            'Dim s As String = oCrypt.Encrypting(connStr, "SellKey")
            'HttpContext.Current.Session("ConnString") = s

            HttpContext.Current.Session("ConnString") = connStr
            username = username.ToUpper

            'This code is SQL injection attack-proof
            sql = "Select * From [XU] WHERE " & _
                   "[TXT_logonname]=@username" ' AND [TXT_password]=@password"
            comm = New Data.SqlClient.SqlCommand(sql, conn)
            comm.Parameters.AddWithValue("@username", username)
            'comm.Parameters.AddWithValue("@password", password)

            'DO NOT ENABLE! The following block allows these SQL injection attacks to succeed:
            '   NOW PLUGGED:    ' or 1=1 --
            '   LETS ME IN:     ' or XU.TXT_LogonName = 'MARIOI' --
            '   SUCCEEDS:       ' or 1=1; drop table AA; --
            '   SHOULD SUCCEED: ; shutdown with nowait; --
            'Dim sql As String = "Select * From [XU] WHERE " & _testig of
            '       "[TXT_logonname]='" & username & "' AND [TXT_password]='" & password & "'"
            'Dim comm As New Data.SqlClient.SqlCommand(sql, conn)

            reader = comm.ExecuteReader

            If reader.HasRows Then
                'Log("RHTEST", "MESSAGE 4")
                Dim dt As New DataTable
                Dim bReinit As Boolean

                dt.Load(reader)
                'Loading the datatable closes the reader

                'More than one row returned? Either there is a duplicate username/password or
                'somehow the query got compromised (SQL injection?)
                If dt.Rows.Count <> 1 Then
                    'No User referenced in the login
                    sMessage = "Login failed because multiple Login (XU) records were returned for this login: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                    Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)
                    conn.Close()
                    Return False
                End If

                'Test case of password
                If sPassword.Length < 3 Then Return False 'handles "" and Null cases.
                If sPassword <> dt.Rows(0).Item("Txt_password") Then
                    'If password for user fails, test if Admin password
                    If oMD.TestIfPasswordIsAdmin(sPassword) = True Then
                        'Password is Admin.. log and continue
                        sMessage = "Admin logging in as: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                        Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)
                        bAdminBypass = True
                    Else
                        'Password is not Admin.. fail
                        sMessage = "Login failed: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                        Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)
                        conn.Close()
                        Return False
                    End If
                End If
                'End

                '**************************************************************
                '                User Login and Password Tests

                If bAdminBypass = False Then



                    If oMD.TestIfLoginIsEnabled(username) = False Then
                        sMessage = "Login failed as not enabled: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                        Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)
                        oMD.WriteAuthenticationMessage(username, password, "3")
                        Return False
                    End If

                    'MI 10/24/13 Deprecated deactivation date notion
                    'If oMD.TestIfLoginIsDeactivated(username) Then
                    '    sMessage = "Login failed as past deactivation date: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                    '    Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)
                    '    oMD.WriteAuthenticationMessage(username, password, "4")
                    '    Return False
                    'End If

                    If oMD.TestIfPasswordHasExpired(username) Then
                        sMessage = "Login password has expired: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                        Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)
                        oMD.WriteAuthenticationMessage(username, password, "7")
                        Return False
                    End If



                    If oMD.TestIfPasswordIsTemporary(username, password) Then
                        If oMD.TestIfTemporaryPasswordHasExpired(username, password) Then
                            sMessage = "Login failed because password is temporary and has expired: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                            Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)
                            oMD.WriteAuthenticationMessage(username, password, "1")
                            Return False
                        Else
                            sMessage = "Login failed because password is temporary: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                            Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)
                            oMD.WriteAuthenticationMessage(username, password, "2")
                            Return False
                        End If
                    End If

                End If

                '**************************************************************


                If HttpContext.Current.Session("USERID") <> "" Then
                    bReinit = True
                End If

                sTemp = dt.Rows(0).Item("GID_UserID").ToString

                HttpContext.Current.Session("USERID") = sTemp
                HttpContext.Current.Session("LOGINID") = dt.Rows(0).Item("GID_ID").ToString
                HttpContext.Current.Session("LOGINNAME") = dt.Rows(0).Item("TXT_LogonName").ToString

                '---------------------------------
                'SHOULD THIS BE REENABLED HERE?
                'MI 4/27/09 Moved the following block to after login validations take place.
                'With this code here the initialization was failing if the login isn't valid
                'causing the Catch to fail the login. To the user this looks like the page
                'returns without any indication of login failure.
                'If bReinit = True Then
                '    Dim oInit As New clInit
                '    oInit.ReInitialize()
                'End If
                '---------------------------------

                'Is there a User for this login?
                If Trim(sTemp) = "" Then
                    'No User referenced in the login
                    sMessage = "Login failed because no User (US) record is associated with this login: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                    Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)
                    conn.Close()
                    Return False
                End If

                'Is there an actual User record referenced in this login?
                sql2 = "SELECT GID_ID FROM US WHERE GID_ID = '" & sTemp & "'"
                comm2 = New Data.SqlClient.SqlCommand(sql2, conn)
                reader2 = comm2.ExecuteReader
                If Not reader2.HasRows Then
                    'User isn't allowed to log onto MB
                    sMessage = "Login failed because User record '" & sTemp & "' referenced in login: '" & username & "' cannot be found. IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                    Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)
                    reader2.Close()
                    conn.Close()
                    Return False
                End If
                reader2.Close()
                If bAdminBypass = False Then


                    'Check product permission
                    Select Case HttpContext.Current.Session("sProduct")
                        '--> Code additional product permissions as additional CASEs.
                        Case "MB"
                            sql2 = "SELECT GID_ID FROM XP WHERE GID_Section = @loginid and TXT_Page = 'FEATURES' and TXT_Property = 'MOBILE' and TXT_Value = '1'"
                            comm2 = New Data.SqlClient.SqlCommand(sql2, conn)
                            comm2.Parameters.AddWithValue("@loginid", HttpContext.Current.Session("LOGINID"))
                            reader2 = comm2.ExecuteReader
                            If Not reader2.HasRows Then
                                'MI 7/29/09 User doesn't have an explicit Mobile permission, is there explicit denied perm?
                                reader2.Close()
                                sql2 = "SELECT GID_ID FROM XP WHERE GID_Section = @loginid and TXT_Page = 'FEATURES' and TXT_Property = 'MOBILE' and TXT_Value = '0'"
                                comm2 = New Data.SqlClient.SqlCommand(sql2, conn)
                                comm2.Parameters.AddWithValue("@loginid", HttpContext.Current.Session("LOGINID"))
                                reader2 = comm2.ExecuteReader
                                If reader2.HasRows Then
                                    'MI 7/29/09 Perm explicitly denied
                                    sMessage = "Login failed due to no product permission: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                                    Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)
                                    reader2.Close()
                                    conn.Close()
                                    Return False
                                Else
                                    'MI 7/29/09 Denied perm not found. Check default permission.
                                    reader2.Close()
                                    sql2 = "SELECT GID_ID FROM XP WHERE GID_Section is NULL and TXT_Page = 'FEATURES' and TXT_Property = 'MOBILE' and TXT_Value = '1'"
                                    comm2 = New Data.SqlClient.SqlCommand(sql2, conn)
                                    reader2 = comm2.ExecuteReader
                                    If Not reader2.HasRows Then
                                        'User isn't allowed to log onto MB
                                        sMessage = "Login failed due to no product permission: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                                        Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)
                                        reader2.Close()
                                        conn.Close()
                                        Return False
                                    End If
                                End If
                            End If
                            reader2.Close()
                        Case Else       'SA
                            'Product permission for SA is implied currently, but could be checked in the future
                    End Select

                    'Check User Agreement acceptance
                    If dt.Rows(0).Item("DTT_AgrAccepted").ToString = "" Then
                        'User hasn't accepted the User agreement - redirect to Agreement.aspx
                        conn.Close()
                        'MI 4/27/09 Reinit the project "just in case"
                        'If bReinit = True Then
                        '    Dim oInit As New clInit
                        '    oInit.ReInitialize()
                        'End If
                        HttpContext.Current.Session("UserAgreementAccepted") = Nothing
                        HttpContext.Current.Session("PASSWORD") = password.ToString

                        bAgree = False

                        'We've left this page at this point
                    Else
                        'User accepted the user agreement
                        HttpContext.Current.Session("UserAgreementAccepted") = "1"
                        sMessage = "Login successful: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                        Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 1, , , , , username)
                        'Log UserAgent if not webservice client
                        If InStr(HttpContext.Current.Request.UserAgent, "MS Web Services Client") = 0 Then
                            Log(sProc, HttpContext.Current.Request.UserAgent, clC.SELL_LOGLEVEL_NONE, , , , 5, HttpContext.Current.Request.Browser.Browser, HttpContext.Current.Request.Browser.Version, HttpContext.Current.Request.Browser.MajorVersion, HttpContext.Current.Request.Browser.MinorVersion, username)
                        End If
                        conn.Close()
                        GoTo LoginSucceeded
                    End If

                    If bAgree = False Then
                        GoTo RedirectSection
                    End If

                End If
LoginSucceeded:
                'MI 4/27/09 Moved here. See note above the commented block of code above.
                If bReinit = True Then
                    Dim oInit As New clInit
                    oInit.ReInitialize()
                End If

                HttpContext.Current.Session("PASSWORD") = ""

                Return True

            Else
                sMessage = "Login failed: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
                Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2, , , , , username)
                conn.Close()
                Return False
            End If


        Catch ex As Exception

            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    sMessage = "Login failed due to unknown error: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'. Error: " & ex.Message
            '    Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2, , , , , username)
            '    conn.Close()
            'End If
            Return False

        End Try

        'Should never get here
        conn.Close()
        Return False

RedirectSection:

        'HttpContext.Current.Response.Redirect("cus_Agreement.aspx", False) 'MI 11/19/08 added 'cus_'   '*** MI 10/18/07 added True
        HttpContext.Current.Session(clSettings.GetHostName() & "_" & "bIsRedirectToAgreement") = "True"
        Return False

    End Function

    Private Sub SetSession(ByRef conn As Data.SqlClient.SqlConnection, ByRef sProduct As String)

        Dim cmd As New System.Data.SqlClient.SqlCommand

        cmd.CommandText = "pInitSession"
        cmd.CommandType = System.Data.CommandType.StoredProcedure
        cmd.Connection = conn

        Dim uSection As New System.Data.SqlClient.SqlParameter("@par_uUserID", SqlDbType.UniqueIdentifier)
        uSection.Value = StringToGuid("F0272467-3EC5-48F7-5553-987900B57A11")
        cmd.Parameters.Add(uSection)

        Dim strPage As New System.Data.SqlClient.SqlParameter("@par_sUserCode", SqlDbType.Char)
        strPage.Value = "SYST"
        cmd.Parameters.Add(strPage)

        Dim strProduct As New System.Data.SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar)
        strProduct.Value = sProduct
        cmd.Parameters.Add(strProduct)
        cmd.ExecuteNonQuery()


    End Sub




    Function Log(ByVal par_sProc As String, _
                    ByVal par_sMessage As String, _
                    Optional ByVal par_siLevel As Short = 1, _
                    Optional ByVal par_bStack As Boolean = False, _
                    Optional ByVal par_bTrace As Boolean = False, _
                    Optional ByVal par_siWarnErr As Short = 0, _
                    Optional ByVal par_tiPurpose As Short = 0, _
                    Optional ByVal par_sBrowser As String = "", _
                    Optional ByVal par_sVersion As String = "", _
                    Optional ByVal par_iMajorVersion As Short = 0, _
                    Optional ByVal par_dMinorVersion As Double = 0, _
                    Optional ByVal par_sUser As String = "") As Boolean

        'MI 3/12/09 Added par_tiPurpose.
        'MI 3/3/09 Changed sproc reference.
        'MI 3/5/07 Added clC.SELL_LOGLINE_MAXLENGTH as length of message.

        'PURPOSE:	Write in a text file a line to log current task/message
        '
        'PARAMETERS:
        '		par_sModule: 	Module Name (the name of the module where the call is made)
        '		par_sMessage: 	The message to log 
        '		par_siLevel:	Level of the log operation
        '		Levels available:
        '		0 - None: no logging except error messages
        '		1 - Always ON, only Entry and Exit of the main modules are logged (and errors, of course)
        '		2 - Details: main functionalities of each module are also logged
        '		3 - Debug: Everything is logged
        '       par_bStack: True if method stack log
        '       par_bTrace: Used by trace system to flag as a tracg log item
        '       par_siWarnErr: 0=not error or warning; 1=warning; 2=error
        '       par_tiPurpose: 0 (default)=unassigned; 1=login success; 2=login failure; 10=UI element start
        'RETURNS:	
        '		True if the operation was logged, False if not (level of the operation > current level)

        'EXAMPLE:	goLog.Log("clImpExp:ExecuteImport", "Going to process script after line", 3)



        Dim sProc As String = "clSelltisMembershipProvider::Log"

        Dim bWebservice As Boolean
        If InStr(HttpContext.Current.Request.UserAgent, "MS Web Services Client") > 0 Then
            bWebservice = True
        End If


        Dim iResult As Integer
        Dim oConnection As Data.SqlClient.SqlConnection = GetConnection()

        Dim oCommand As New SqlClient.SqlCommand
        Dim oReader As SqlClient.SqlDataReader

        oCommand.CommandText = "pWriteLog"
        oCommand.CommandType = CommandType.StoredProcedure
        oCommand.Connection = oConnection

        'parameter
        Dim strProc As New SqlClient.SqlParameter("@par_sModule", SqlDbType.VarChar)
        strProc.Value = Left(par_sProc, 80)
        oCommand.Parameters.Add(strProc)

        'parameter
        Dim strMessage As New SqlClient.SqlParameter("@par_sMessage", SqlDbType.VarChar)
        strMessage.Value = Left(par_sMessage, clC.SELL_LOGLINE_MAXLENGTH)       '*** MI 3/5/07
        oCommand.Parameters.Add(strMessage)

        'parameter
        Dim strSessionID As New SqlClient.SqlParameter("@par_sSessionID", SqlDbType.VarChar)
        strSessionID.Value = Left(HttpContext.Current.Session.SessionID, 30)
        oCommand.Parameters.Add(strSessionID)

        'parameter
        Dim tiLevel As New SqlClient.SqlParameter("@par_tiLevel", SqlDbType.TinyInt)
        tiLevel.Value = par_siLevel
        oCommand.Parameters.Add(tiLevel)

        'parameter
        Dim tiWarnErr As New SqlClient.SqlParameter("@par_tiWarnErr", SqlDbType.TinyInt)
        tiLevel.Value = par_siWarnErr
        oCommand.Parameters.Add(tiWarnErr)

        'parameter
        Dim bStack As New SqlClient.SqlParameter("@par_bStack", SqlDbType.Bit)
        bStack.Value = par_bStack
        oCommand.Parameters.Add(bStack)

        'parameter
        Dim bTrace As New SqlClient.SqlParameter("@par_bTrace", SqlDbType.Bit)
        bTrace.Value = par_bTrace
        oCommand.Parameters.Add(bTrace)

        'parameter
        Dim tiPurpose As New SqlClient.SqlParameter("@par_tiPurpose", SqlDbType.TinyInt)
        tiPurpose.Value = par_tiPurpose
        oCommand.Parameters.Add(tiPurpose)

        'parameter
        Dim strBrowser As New SqlClient.SqlParameter("@par_sBrowser", SqlDbType.VarChar)
        strBrowser.Value = par_sBrowser
        oCommand.Parameters.Add(strBrowser)

        'parameter
        Dim strVersion As New SqlClient.SqlParameter("@par_sVersion", SqlDbType.VarChar)
        strVersion.Value = par_sVersion
        oCommand.Parameters.Add(strVersion)

        'parameter
        Dim iMajorVersion As New SqlClient.SqlParameter("@par_iMajorVersion", SqlDbType.Int)
        iMajorVersion.Value = par_iMajorVersion
        oCommand.Parameters.Add(iMajorVersion)

        'parameter
        Dim dMinorVersion As New SqlClient.SqlParameter("@par_iMinorVersion", SqlDbType.Float)
        dMinorVersion.Value = par_dMinorVersion
        oCommand.Parameters.Add(dMinorVersion)

        'parameter
        Dim strUser As New SqlClient.SqlParameter("@par_sLoginName", SqlDbType.VarChar)
        strUser.Value = par_sUser
        oCommand.Parameters.Add(strUser)

        'return parameter
        Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
        retValParam.Direction = ParameterDirection.ReturnValue
        oCommand.Parameters.Add(retValParam)

        'parameter
        Dim bWS As New SqlClient.SqlParameter("@par_bWebService", SqlDbType.Bit)
        bWS.Value = bWebservice
        oCommand.Parameters.Add(bWS)

        'execute

        oReader = oCommand.ExecuteReader()

        'Now you can grab the output parameter's value...
        iResult = Convert.ToInt16(retValParam.Value)

        oReader.Close()
        oConnection.Close()




        Return True
    End Function

    Public Function GetConnection(Optional ByVal par_iTimeout As Integer = 60) As SqlClient.SqlConnection

        'MI 5/17/06 Replaced setting ID and usercode from ConfigurationManager.AppSettings
        '           with using goP methods.
        'Estabishes a connection to SQL server.  Calls pInitSession sp to provide user info.
        'IMPORTANT: goP.InitProject must run before this runs the first time, otherwise
        '           this method will set the wrong user information in the SQL Server 
        '           session (see pInitSession sproc in SS).

        Dim settings As String = connStr
        ' Dim iTimeout As Integer = InStr(LCase(settings), "timeout=")

        settings = Replace(settings, "timeout=x", "timeout=" & par_iTimeout)

        Dim sqlConnection1 As New Data.SqlClient.SqlConnection(settings)


        Dim cmd As New System.Data.SqlClient.SqlCommand

        'Try
        cmd.CommandText = "pInitSession"
            cmd.CommandType = System.Data.CommandType.StoredProcedure
            cmd.Connection = sqlConnection1

            Dim uSection As New System.Data.SqlClient.SqlParameter("@par_uUserID", SqlDbType.UniqueIdentifier)
            'We don't have the real user login at this point, setting to SYSTEM login until we log on the user
            uSection.Value = StringToGuid("F0272467-3EC5-48F7-5553-987900B57A11")  'MI 1/14/09  '*** MI 5/17/06
            'uSection.Value = StringToGuid(goP.GetUserTID())  '*** MI 5/17/06
            cmd.Parameters.Add(uSection)

            Dim strPage As New System.Data.SqlClient.SqlParameter("@par_sUserCode", SqlDbType.Char)
            strPage.Value = "SYST"  'MI 1/14/09 changed from RAH to SYST    '*** MI 5/17/06
            cmd.Parameters.Add(strPage)

            Dim strProduct As New System.Data.SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar)
            strProduct.Value = HttpContext.Current.Session("sProduct")
            cmd.Parameters.Add(strProduct)

            sqlConnection1.Open()
            cmd.ExecuteNonQuery()

        'Catch ex As Exception
        '    '==> raise error

        'End Try

        Return sqlConnection1

    End Function

    Private Function StringToGuid(ByVal sValue As String) As System.Guid
        Dim guidValue As System.Guid = CType(System.ComponentModel.TypeDescriptor.GetConverter(guidValue).ConvertFrom(sValue), System.Guid)
        Return guidValue
    End Function


End Class
