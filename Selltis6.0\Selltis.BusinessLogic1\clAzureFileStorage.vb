﻿Imports Microsoft.WindowsAzure.Storage.File
Imports Microsoft.WindowsAzure.Storage
Imports Microsoft.Azure
Imports Microsoft.WindowsAzure.StorageClient
Imports System.Web
Imports System.IO


Public NotInheritable Class clAzureFileStorage

    'Dim goErr As clError

    'Public Sub New()

    '    'goP = HttpContext.Current.Session("goP")
    '    'goTR = HttpContext.Current.Session("goTr")
    '    'goMeta = HttpContext.Current.Session("goMeta")
    '    'goData = HttpContext.Current.Session("goData")
    '    goErr = HttpContext.Current.Session("goErr")
    '    'goLog = HttpContext.Current.Session("goLog")
    '    'goScr = HttpContext.Current.Session("goScr")

    'End Sub

    Public Shared Function GetRootDirectory() As CloudFileDirectory

        '  Try
        Dim storageAccount As CloudStorageAccount = CloudStorageAccount.Parse(CloudConfigurationManager.GetSetting("StorageConnectionString"))
            Dim fileClient As CloudFileClient = storageAccount.CreateCloudFileClient()
            ' Get a reference to the file share we created previously.
            Dim share As CloudFileShare = fileClient.GetShareReference("attachments")

            '' WriteLog("Getting root directory")

            ' Ensure that the share exists.
            If share.Exists() Then

                'Dim policyName As String = "sampleSharePolicy" + DateTime.UtcNow.Ticks.ToString()

                '' Create a new shared access policy and define its constraints.
                'Dim sharedPolicy As New SharedAccessFilePolicy()
                'sharedPolicy.SharedAccessExpiryTime = DateTime.UtcNow.AddHours(24)
                'sharedPolicy.Permissions = SharedAccessFilePermissions.Read Or SharedAccessFilePermissions.Write

                '' Get existing permissions for the share.
                'Dim permissions As FileSharePermissions = share.GetPermissions()

                '' Add the shared access policy to the share's policies. Note that each policy must have a unique name.
                'permissions.SharedAccessPolicies.Add(policyName, sharedPolicy)
                'share.SetPermissions(permissions)

                'WriteLog("Shared Policy created successfully")

                ' Get a reference to the root directory for the share.
                Dim rootDir As CloudFileDirectory = share.GetRootDirectoryReference()

                If rootDir Is Nothing Then
                    '' WriteLog("Root Directory is nothing")
                End If

                Return rootDir
            Else
                '' WriteLog("Share does not existed")
            End If
        'Catch ex As Exception
        '    ' WriteLog(ex.ToString())
        'End Try

        Return Nothing

    End Function

    Public Shared Function CreateDirectory(rootDir As CloudFileDirectory, FolderName As String, Optional ByVal isCreateDirectoryIfNotExists As Boolean = True) As CloudFileDirectory

        ' Try

        'WriteLog("Creating Directory --> " + FolderName)
        Dim sampleDir As CloudFileDirectory = rootDir.GetDirectoryReference(FolderName)
            If sampleDir.Exists() = False AndAlso isCreateDirectoryIfNotExists = True Then
                'if not exists create the directory
                ''WriteLog("Directory Creating --> " + FolderName)

                sampleDir.Create()

                '' WriteLog("Directory Created --> " + FolderName)
                '  WriteLog("Directory Created --> " + FolderName)
            End If
            Return sampleDir
        'Catch ex As Exception
        '    '' WriteLog("Error in create directory --> " + FolderName + " --> " + ex.Message)
        'End Try

    End Function

    'Public Shared Sub UploadFile(FolderPath As CloudFileDirectory, LocalfilePath As String)
    '    Dim fileName As String = System.IO.Path.GetFileName(LocalfilePath)
    '    Dim cloudFile As CloudFile = FolderPath.GetFileReference(fileName)
    '    cloudFile.UploadFromFile(LocalfilePath)
    'End Sub

    Public Shared Sub UploadFromStream(FolderPath As CloudFileDirectory, _stream As System.IO.Stream, FileName As String)

        ' Try
        Dim cloudFile As CloudFile = FolderPath.GetFileReference(FileName)
            cloudFile.UploadFromStream(_stream)
        'Catch ex As Exception
        '    '  WriteLog(ex.ToString())
        'End Try

    End Sub

    Public Shared Sub UploadFromStream(sViewName As String, sGidId As String, sFieldName As String, sTempId As String, isTempPath As Boolean, _stream As System.IO.Stream, FileName As String)

        ' Try
        Dim FinalFolder = Nothing
            If isTempPath Then
                FinalFolder = GetFinalFolder("", "", "", sTempId, True)
            Else
                FinalFolder = GetFinalFolder(sViewName, sGidId, sFieldName, "", False)
            End If
            Dim cloudFile As CloudFile = FinalFolder.GetFileReference(FileName)
            cloudFile.UploadFromStream(_stream)
        'Catch ex As Exception
        '    '  WriteLog(ex.ToString())
        'End Try

    End Sub

    Public Shared Function CopyFile(SourceFolderPath As CloudFileDirectory, DestinationFolderPath As CloudFileDirectory, FileName As String) As String

        ' Try
        Dim sourceFile As CloudFile = SourceFolderPath.GetFileReference(FileName)
            Dim destFile As CloudFile = DestinationFolderPath.GetFileReference(FileName)
            Dim sReturn As String = destFile.StartCopy(sourceFile)
            Return sReturn
        'Catch ex As Exception
        '    '  WriteLog(ex.ToString())
        'End Try

    End Function

    Public Shared Function DownloadFileToStream(FolderPath As CloudFileDirectory, FileName As String) As System.IO.MemoryStream

        Dim sFile As CloudFile = FolderPath.GetFileReference(FileName)
        Dim tStream As System.IO.MemoryStream = New System.IO.MemoryStream()
        sFile.DownloadToStream(tStream)
        Return tStream

    End Function

    Public Shared Function DownloadFileToStream(sViewName As String, sGidId As String, sFieldName As String, sTempId As String, isTempPath As Boolean, FileName As String) As System.IO.MemoryStream
        Dim FinalFolder = Nothing
        If isTempPath Then
            FinalFolder = GetFinalFolder("", "", "", sTempId, True)
        Else
            FinalFolder = GetFinalFolder(sViewName, sGidId, sFieldName, "", False)
        End If
        Dim sFile As CloudFile = FinalFolder.GetFileReference(FileName)
        Dim tStream As System.IO.MemoryStream = New System.IO.MemoryStream()
        sFile.DownloadToStream(tStream)
        Return tStream

    End Function

    Public Shared Function GetFile(sViewName As String, sGidId As String, sFieldName As String, sFileName As String) As Byte()

        Dim FileFolder = GetFinalFolder(sViewName, sGidId, sFieldName, "", False)
        Dim ms As System.IO.MemoryStream = DownloadFileToStream(FileFolder, sFileName)

        If ms IsNot Nothing Then
            Return ms.ToArray
        End If

        Return Nothing

    End Function

    'Public Shared Function DownloadFileToByteArray(FolderPath As CloudFileDirectory, FileName As String) As Byte()

    '    Dim sFile As CloudFile = FolderPath.GetFileReference(FileName)
    '    Dim sTarget() As Byte
    '    sFile.DownloadToByteArray(sTarget, 0)
    '    Return sTarget

    'End Function

    Public Shared Function DeleteFile(FolderPath As CloudFileDirectory, FileName As String) As Boolean

        Dim sFile As CloudFile = FolderPath.GetFileReference(FileName)
        Return sFile.DeleteIfExists()

    End Function

    Public Shared Function DeleteFile(sViewName As String, sGidId As String, sFieldName As String, sTempId As String, isTempPath As Boolean, FileName As String) As Boolean
        Dim FinalFolder = Nothing
        If isTempPath Then
            FinalFolder = GetFinalFolder("", "", "", sTempId, True)
        Else
            FinalFolder = GetFinalFolder(sViewName, sGidId, sFieldName, "", False)
        End If
        Dim sFile As CloudFile = FinalFolder.GetFileReference(FileName)
        Return sFile.DeleteIfExists()

    End Function

    'Public Shared Function DeleteFolder(FolderPath As CloudFileDirectory) As Boolean

    '    Return FolderPath.DeleteIfExists()

    'End Function

    Public Shared Function GetFinalFolder(sViewName As String, sGidId As String, sFieldName As String, sTempId As String, isTempPath As Boolean, Optional ByVal hostName As String = "") As CloudFileDirectory

        Dim rootDir = GetRootDirectory()
        Dim host = Nothing
        If hostName = "" Then
            host = clSettings.GetHostName()
        Else
            host = hostName
        End If
        Dim HostDir = CreateDirectory(rootDir, host)

        If HostDir IsNot Nothing Then
            If isTempPath Then
                Dim TempFileDir = CreateDirectory(HostDir, "TempFiles")
                Dim TempDir = CreateDirectory(TempFileDir, sTempId) 'sPath is the Temp Id
                Return TempDir
            Else

                Dim viewDir = CreateDirectory(HostDir, sViewName)
                Dim gidIdDir = CreateDirectory(viewDir, sGidId)
                Dim fiedNameDir = CreateDirectory(gidIdDir, sFieldName)

                Return fiedNameDir

            End If
        End If

        Return Nothing

    End Function

    Public Shared Function IsFileExists(FolderName As CloudFileDirectory, FileName As String) As Boolean
        Dim sFile As CloudFile = FolderName.GetFileReference(FileName)
        Return sFile.Exists()
    End Function

    Public Shared Function IsFileExists(sViewName As String, sGidId As String, sFieldName As String, sTempId As String, isTempPath As Boolean, FileName As String) As Boolean
        Dim FinalFolder = Nothing
        If isTempPath Then
            FinalFolder = GetFinalFolder("", "", "", sTempId, True)
        Else
            FinalFolder = GetFinalFolder(sViewName, sGidId, sFieldName, "", False)
        End If
        Dim sFile As CloudFile = FinalFolder.GetFileReference(FileName)
        Return sFile.Exists()
    End Function

    Public Shared Function GetFileSize(FolderName As CloudFileDirectory, FileName As String) As Integer
        Dim sFile As CloudFile = FolderName.GetFileReference(FileName)
        If sFile.Exists() Then
            Return DownloadFileToStream(FolderName, FileName).ToArray().Length
        End If
        Return 0
    End Function


    Public Shared Sub WriteLog(data As String)
        ' Return
        'Dim goP As clProject = HttpContext.Current.Session("goP")
        'Dim sName As String = goP.GetMe("Name")
        Dim _DestinationPath As String = HttpContext.Current.Server.MapPath(HttpContext.Current.Request.ApplicationPath) & "AzureFileStorageLog.txt"
        Dim str As StreamWriter = New StreamWriter(_DestinationPath, True)
        str.WriteLine(DateTime.Now.ToString() & " : " & data)
        str.Flush()
        str.Close()
    End Sub

    Public Shared Function Upload_File_To_Blob(fStream As System.IO.Stream, sFileName As String, sContentType As String, sContainerName As String) As String
        Dim storageacc As CloudStorageAccount = CloudStorageAccount.Parse(CloudConfigurationManager.GetSetting("StorageConnectionString"))
        Dim blobClient As Blob.CloudBlobClient = storageacc.CreateCloudBlobClient()
        Dim container As Blob.CloudBlobContainer = blobClient.GetContainerReference(sContainerName) ''("editorimages")
        Dim blockBlob As Blob.CloudBlockBlob = container.GetBlockBlobReference(sFileName)
        blockBlob.Properties.ContentType = sContentType ''"image/png"
        blockBlob.UploadFromStream(fStream)
        Return blockBlob.Uri.AbsoluteUri()
    End Function


End Class
