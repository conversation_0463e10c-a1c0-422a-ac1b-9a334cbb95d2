﻿CREATE TABLE [dbo].[SI_RELATED_OP] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_SI_RELATED_OP_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_SI] UNIQUEIDENTIFIER NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_SI_RELATED_OP] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_OP_LINKED_SI] FOREIGN KEY ([GID_SI]) REFERENCES [dbo].[SI] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_SI_RELATED_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[SI_RELATED_OP] NOCHECK CONSTRAINT [LNK_OP_LINKED_SI];


GO
ALTER TABLE [dbo].[SI_RELATED_OP] NOCHECK CONSTRAINT [LNK_SI_RELATED_OP];


GO
CREATE NONCLUSTERED INDEX [IX_SI_RELATED_OP]
    ON [dbo].[SI_RELATED_OP]([GID_SI] ASC, [GID_OP] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_LINKED_SI]
    ON [dbo].[SI_RELATED_OP]([GID_OP] ASC, [GID_SI] ASC);

