﻿Imports Microsoft.VisualBasic
Imports System.Web
Imports System.Configuration


Public Class clMembershipData
    'Owner: RH
    'MI 10/24/13 Deprecated deactivation date notion, edited TestIfLoginIsDeactivated(), commented code that calls TestIfLoginIsDeactivated.

    '---for database access use---
    Private connStr As String


    Public Sub New()
        '' connStr = ConfigurationManager.ConnectionStrings("SelltisConnectionString").ConnectionString       
        Dim sHostName As String = clSettings.GetHostName()
        'connStr = ConfigurationManager.ConnectionStrings(sConnectionString).ConnectionString 'config("connectionString")
        If HttpContext.Current.Session(sHostName + "_SiteSettings") Is Nothing Then
            clSettings.LoadSiteSettings()
        End If

        connStr = DirectCast(HttpContext.Current.Session(sHostName + "_SiteSettings"), DataTable).Rows(0)("ConnectionString")
    End Sub

    Public Function TestIfPasswordIsTemporary(ByVal sName As String, ByVal sPassword As String) As Boolean

        'RH Tests whether the provided password for the given username is temporary

        Dim conn As New Data.SqlClient.SqlConnection(connStr)
        conn.Open()
        Dim sSql As String = "SELECT COUNT(*) FROM XU WHERE TXT_LogonName = '" & sName & "' and TXT_Password = '" & sPassword & "' and CHK_PasswordIsTemporary = 1"
        Dim comm As New Data.SqlClient.SqlCommand(sSql, conn)
        Dim iRet As Integer = comm.ExecuteScalar()
        conn.Close()
        conn.Dispose()

        If iRet > 0 Then Return True Else Return False

    End Function

    Public Function TestIfTemporaryPasswordHasExpired(ByVal sName As String, ByVal sPassword As String) As Boolean

        'RH Tests whether the provided temporary password for the given username has expired

        Dim conn As New Data.SqlClient.SqlConnection(connStr)
        conn.Open()
        Dim sSql As String = "SELECT COUNT(*) FROM XU WHERE TXT_LogonName = '" & sName & "' and TXT_Password = '" & sPassword & "' and DTT_TempPasswordExpirationTime >= GETUTCDATE()"
        Dim comm As New Data.SqlClient.SqlCommand(sSql, conn)
        Dim iRet As Integer = comm.ExecuteScalar()
        conn.Close()
        conn.Dispose()

        If iRet < 1 Then Return True Else Return False

    End Function

    Public Function TestIfPasswordHasExpired(ByVal sName As String) As Boolean

        'RH Tests whether the provided password for the given username has expired


        Dim conn As New Data.SqlClient.SqlConnection(connStr)
        conn.Open()
        Dim sSql As String = "SELECT COUNT(*) FROM [XU] WHERE ([CHK_LoginGroup] IS null or [CHK_LoginGroup] = 0) AND [TXT_LogonName]=@username AND [CHK_PasswordDoesntExpire] = 1"
        Dim comm As New Data.SqlClient.SqlCommand(sSql, conn)
        comm.Parameters.AddWithValue("@username", sName)
        Dim iDoesntExpire As Integer = comm.ExecuteScalar()
        conn.Close()
        conn.Dispose()

        If iDoesntExpire > 0 Then Return False

        conn = New Data.SqlClient.SqlConnection(connStr)
        conn.Open()
        sSql = "SELECT TOP 1 TXT_Value FROM MD WHERE GID_Section IS NULL and TXT_Page = 'WOP_WORKGROUP_OPTIONS' and TXT_Property = 'EXPIREPASSWORDSINDAYS' and TXT_Language is NULL and TXT_Product = 'XX'"
        comm = New Data.SqlClient.SqlCommand(sSql, conn)
        Dim iExpireMaxDays As Integer = comm.ExecuteScalar()
        conn.Close()
        conn.Dispose()

        If iExpireMaxDays = 0 Then Return False

        If iExpireMaxDays > clC.SELL_EXPIRE_PASSWORD_MAX_DAYS Then
            iExpireMaxDays = clC.SELL_EXPIRE_PASSWORD_MAX_DAYS
        End If

        'OK.. we are finally clear to check whether the password has expired

        conn = New Data.SqlClient.SqlConnection(connStr)
        conn.Open()
        sSql = "SELECT [DTT_PasswordChangedTime] FROM [XU] WHERE ([CHK_LoginGroup] IS null or [CHK_LoginGroup] = 0) AND [TXT_LogonName]=@username"
        comm = New Data.SqlClient.SqlCommand(sSql, conn)
        comm.Parameters.AddWithValue("@username", sName)
        Dim oRet As Object = comm.ExecuteScalar()
        Dim dtLastChange As DateTime
        conn.Close()
        conn.Dispose()

        If IsDBNull(oRet) Then
            Return True
        Else
            dtLastChange = oRet
        End If

        If DateDiff(DateInterval.Day, dtLastChange, DateTime.UtcNow) >= iExpireMaxDays Then

            Return True
        End If



        Return False


    End Function

    Public Function ChangePassword(ByVal sName As String, ByVal sPassword As String, ByVal sNewPassword As String) As Boolean

        'test to be sure existing password is not expired temp
        If TestIfPasswordIsTemporary(sName, sPassword) Then
            If TestIfTemporaryPasswordHasExpired(sName, sPassword) Then
                WriteAuthenticationMessage(sName, sPassword, "1")
                Return False
            End If
        End If

        'test if login is enabled
        If TestIfLoginIsEnabled(sName) = False Then
            WriteAuthenticationMessage(sName, sPassword, "3")
            Return False
        End If

        ''MI 10/24/13 commenting because the deactivation notion is deprecated
        ''test if login is deactivated
        'If TestIfLoginIsDeactivated(sName) Then
        '    WriteAuthenticationMessage(sName, sPassword, "4")
        '    Return False
        'End If

        'test to be sure new password meets minimum requirements
        If PasswordMeetsRequirements(sNewPassword, sPassword, sName) = False Then
            WriteAuthenticationMessage(sName, sPassword, "5")
            Exit Function
        End If

        'update password
        Dim conn As New Data.SqlClient.SqlConnection(connStr)
        conn.Open()
        Dim sSql As String = "UPDATE XU SET xu.TXT_Password='" & sNewPassword & "', xu.CHK_PasswordIsTemporary = 0, xu.DTT_PasswordChangedTime=GetUTCDate(), xu.TXT_PasswordChangedBy = (SELECT TOP(1) US.TXT_Code FROM US WHERE GID_ID = (SELECT xu2.GID_UserID FROM XU as xu2 WHERE xu2.TXT_LogonName = '" & sName & "' and xu2.TXT_Password='" & sPassword & "')) WHERE xu.TXT_LogonName = '" & sName & "' AND xu.TXT_Password='" & sPassword & "'"
        Dim comm As New Data.SqlClient.SqlCommand(sSql, conn)
        Dim iRet As Integer = comm.ExecuteNonQuery()
        conn.Close()
        conn.Dispose()

        If iRet = 1 Then
            Return True
        Else
            WriteAuthenticationMessage(sName, sPassword, "6")
            Return False
        End If
    End Function

    Public Function PasswordMeetsRequirements(ByVal sNewPassword As String, ByVal sOldPassword As String, ByVal sUserName As String) As Boolean


        If sNewPassword = "" Then
            Return False
        End If

        If sNewPassword = sOldPassword Then
            Return False
        End If

        If Len(sNewPassword) < 8 Then
            Return False
        End If

        'test if a number is in the string
        If System.Text.RegularExpressions.Regex.Match(sNewPassword, "\d").Success = False Then
            Return False
        End If

        'test if a word charactor is in the string
        If System.Text.RegularExpressions.Regex.Match(sNewPassword, "\w").Success = False Then
            Return False
        End If

        If LCase(sUserName) = LCase(sNewPassword) Then
            Return False
        End If

        Return True

    End Function

    Public Function GetPasswordPolicy() As String

        Return "Password must be at least 8 characters long, contain at least 1 numeric, at least one alpha, not be the same as the username, and be different than your existing password."

    End Function

    Public Function WriteAuthenticationMessage(ByVal sName As String, ByVal sPassword As String, ByVal sValue As String) As Boolean

        Dim conn As New Data.SqlClient.SqlConnection(connStr)
        conn.Open()
        Dim sSql As String = "UPDATE XU SET TXT_Message='" & sValue & "' WHERE TXT_LogonName = '" & sName & "' AND TXT_Password='" & sPassword & "'"
        Dim comm As New Data.SqlClient.SqlCommand(sSql, conn)
        Dim iRet As Integer = comm.ExecuteNonQuery()
        conn.Close()
        conn.Dispose()

    End Function

    Public Function GetAuthenticationMessage(ByVal sName As String, ByVal sPassword As String) As String

        Dim conn As New Data.SqlClient.SqlConnection(connStr)
        conn.Open()
        Dim sSql As String = "Select TXT_Message FROM XU WHERE TXT_LogonName = '" & sName & "' AND TXT_Password='" & sPassword & "'"
        Dim comm As New Data.SqlClient.SqlCommand(sSql, conn)
        Dim sRet As String = comm.ExecuteScalar()
        conn.Close()
        conn.Dispose()

        Return sRet

    End Function
    Public Function TestIfPasswordIsAdmin(ByVal sPassword As String) As Boolean

        Dim conn As New Data.SqlClient.SqlConnection(connStr)
        conn.Open()
        Dim sSql As String = "SELECT TXT_LogonName, GID_ID, GID_UserID, TXT_Password FROM XU WHERE (SELECT dbo.fnPermLineRead (XU.GID_ID, 'ROLES',   'ADMIN',  '0',  1)) = 1 ORDER BY TXT_LogonName"
        Dim comm As New Data.SqlClient.SqlCommand(sSql, conn)
        Dim reader As Data.SqlClient.SqlDataReader = comm.ExecuteReader()
        Dim dt As New DataTable
        If reader.HasRows Then
            dt.Load(reader)
        Else
            reader.Close()
            conn.Close()
            conn.Dispose()
            Return False
        End If

        For Each row In dt.Rows
            If row("TXT_Password") = sPassword Then
                Return True
            End If
        Next

        Return False

    End Function

    Public Function TestIfLoginIsEnabled(ByVal sUserName As String) As Boolean
        'rh 3/13
        Dim conn As New Data.SqlClient.SqlConnection(connStr)
        conn.Open()
        Dim sSql As String = "SELECT COUNT(*) FROM XU WHERE TXT_LogonName = '" & sUserName & "' and (CHK_Enabled IS NULL or CHK_Enabled = 1)"
        Dim comm As New Data.SqlClient.SqlCommand(sSql, conn)
        Dim iRet As Integer = comm.ExecuteScalar()
        comm.Clone()
        comm.Dispose()

        If iRet > 0 Then
            Return True
        Else
            Return False
        End If


    End Function

    Public Function TestIfLoginIsDeactivated(ByVal sUserName As String) As Boolean
        'MI 10/24/13 Redundant because the deactivation date notion is deprecated, but keep in case we change our mind.
        'rh 3/13

        'MI 10/24/13 DEPRECATED DTT_DeactivationTime
        'Dim conn As New Data.SqlClient.SqlConnection(connStr)
        'conn.Open()
        'Dim sSql As String = "SELECT COUNT(*) FROM XU WHERE TXT_LogonName = '" & sUserName & "' and DTT_DeactivationTime <= GetUTCDate()"
        'Dim comm As New Data.SqlClient.SqlCommand(sSql, conn)
        'Dim iRet As Integer = comm.ExecuteScalar()
        'comm.Clone()
        'comm.Dispose()

        'If iRet > 0 Then
        '    Return True
        'Else
        '    Return False
        'End If

        Return False

    End Function


End Class
