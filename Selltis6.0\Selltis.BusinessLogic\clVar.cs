﻿using System;
using System.Collections;

//OWNER: RH



namespace Selltis.BusinessLogic
{
	public class clVar
	{

		//MI 8/21/06 Changed Collection object to SortedList to support DeleteVarsByName.
		//MI 8/21/06 Added DeleteVarsByName

		//OWNER: MI
		//PURPOSE:
		//       Allow storing "global variables" accessible within an object of appropriate scope. For example,
		//       clProject inherits from clVar, allowing us to persist on the goP. level
		//       as many "variables" as necessary.
		//IMPLEMENTATION NOTE:
		//       "Variables" are stored as elements of a sorted list that contains key/value pairs.
		//       SortedList is a .net object that is automatically sorted by key. We chose this object
		//       to be able to find elements by index, partial key, and partial value. Collection doesn't
		//       allow finding the key by index or value.
		//       IMPORTANT: Because the sorting is performed automatically, index values change on
		//       each insertion and deletion.

		//Private cCollection As New Collection      'old object

		private SortedList slCollection = new SortedList();

		public clVar()
		{

		}

		public void DeleteAllVar()
		{

			//PURPOSE:
			//		Delete ALL items in the collection
			//PARAMETERS:
			//		N/A
			//RETURNS:
			//		Nothing
			//HOW IT WORKS:
			//		Clears the collection
			//EXAMPLE:
			//		oForm.DeleteAllVar()

			slCollection.Clear();

		}

		public bool DeleteVar(string par_sVarName)
		{
			//PURPOSE:
			//		Delete the value and name corresponding to a var name
			//PARAMETERS:
			//		par_sVarName:	Name of the var we want to delete
			//RETURNS:
			//		True if job done, false if not (ie the var was not found)
			//EXAMPLE:
			//		if not oForm.DeleteVar("MyVar") then

			par_sVarName = par_sVarName.ToUpper();

			if (slCollection.Contains(par_sVarName))
			{
				slCollection.Remove(par_sVarName);
				return true;
			}
			else
			{
				return false;
			}
		}
		public bool DeleteVarsByName(string par_sStartsWith)
		{
			//AUTHOR: MI 8/21/06
			//PURPOSE:
			//		Delete items whose var name starts with par_sStartsWith.
			//       The search is case insensitive.
			//PARAMETERS:
			//		par_sStartsWith: String with which the var name starts.
			//           CAUTION: If this is a blank string, no vars are deleted and
			//           False is returned. To delete all vars, use DeleteAllVar().
			//RETURNS:
			//		True if successful, false if no vars were found. False if
			//           par_sStartsWith is blank.
			//EXAMPLE:
			//		if not goP.DeleteVars("diaMetaBrowser") then

			string sStartsWith = par_sStartsWith.ToUpper();
			int i = 0;
			bool bResult = false;
			int iLen = sStartsWith.Length;

			if (iLen < 1)
			{
				//Search string is blank, return False and don't delete any vars
				return false;
			}

			//0-based index
			do
			{
				if (i > slCollection.Count - 1)
				{
					break;
				}
				if (slCollection.GetKey(i).ToString().ToUpper().Substring(0, iLen) == sStartsWith)
				{
					slCollection.RemoveAt(i);
					i = i - 1;
					bResult = true;
				}
				i = i + 1;
			} while (true);

			return bResult;

		}

		public object GetVar(string par_sVarName)
		{

			//PURPOSE:
			//		Get the value corresponding to a var name
			//PARAMETERS:
			//		par_sVarName:	Name of the var we want to get
			//RETURNS:
			//		The value found, or an empty string, if not found
			//HOW IT WORKS:
			//		Pulls the value out of the storage collection
			//EXAMPLE:
			//		oForm.GetVar("MyVar")

			par_sVarName = par_sVarName.ToUpper();

			if (slCollection.Contains(par_sVarName))
			{
				return slCollection[par_sVarName];
			}
			else
			{
				return "";
			}

		}
		public bool SetVar(string par_sVar, object par_vVal)
		{

			//PURPOSE:
			//		Add or modify a variable value in the arrays
			//PARAMETERS:
			//		par_sVar:		Name of the variable
			//		par_vVal:		Value to store
			//RETURNS:
			//		True if job done, false if the name of the variable was empty
			//HOW IT WORKS:
			//		Look in the array if the variable already exist. If true, modify the value, if false, add a line in the array or 
			//		use the first available space, if any
			//EXAMPLE:
			//		oForm:SetVar("MyVar", 23)	//- we use oForm because clVar is generally not used directly, but several classes 
			//									//inherit it
			//	

			if (par_sVar == "")
			{
				return false;
			}

			try
			{
				par_sVar = par_sVar.ToUpper();

				if (slCollection.Contains(par_sVar))
				{
					slCollection.Remove(par_sVar);
				}

				slCollection.Add(par_sVar, par_vVal);

				return true;
			}
			catch (Exception ex)
			{
				return false;
			}

		}

	}

}
