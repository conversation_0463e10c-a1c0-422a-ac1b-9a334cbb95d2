﻿@*@model Selltis.MVC.Models.ListItems*@
@*@model List<Selltis.MVC.Models.DesktopListItems>*@
@using Selltis.Core;
@model Selltis.MVC.Models.ManageDesktops
@{
    ViewBag.Title = "OpenDesktopLayout2";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@*<link href="~/Content/App.theme1.css" rel="stylesheet" />*@

<style type="text/css">
    /*check box*/
    .toggle-switch .ts-helper {
        display: inline-block;
        position: relative;
        width: 30px;
        height: 10px;
        border-radius: 8px;
        background: rgba(0,0,0,.26);
        -webkit-transition: background .28s cubic-bezier(.4,0,.2,1);
        transition: background .28s cubic-bezier(.4,0,.2,1);
        vertical-align: middle;
        cursor: pointer;
    }

    .toggle-switch[data-ts-color=blue] input:not(:disabled):checked + .ts-helper {
        background: rgba(33,150,243,.5);
    }

        .toggle-switch[data-ts-color=blue] input:not(:disabled):checked + .ts-helper:before {
            background: #2196F3;
        }

    .toggle-switch input:checked + .ts-helper:before {
        left: 20px;
    }

    .toggle-switch .ts-helper:before {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        width: 16px;
        height: 16px;
        background: #fafafa;
        box-shadow: 0 2px 8px rgba(0,0,0,.28);
        border-radius: 50%;
        webkit-transition: left .28s cubic-bezier(.4,0,.2,1),background .28s cubic-bezier(.4,0,.2,1),box-shadow .28s cubic-bezier(.4,0,.2,1);
        transition: left .28s cubic-bezier(.4,0,.2,1),background .28s cubic-bezier(.4,0,.2,1),box-shadow .28s cubic-bezier(.4,0,.2,1);
    }


    body {
        font-family: Arial;
        font-size: 13px;
        font-weight: 400;
        color: #666 !important;
    }

    .actions {
        list-style: none;
        padding: 0;
        z-index: 3;
        margin: 0;
    }

        .actions > li {
            display: inline-block;
            vertical-align: baseline;
        }

            .actions > a,
            .actions > li > a {
                width: 30px;
                height: 30px;
                display: inline-block;
                padding-top: 5px;
            }

                .actions > a > i,
                .actions > li > a > i {
                    color: #adadad;
                    font-size: 20px;
                }

                .actions.open > a > i,
                .actions > a:hover > i,
                .actions > li.open > a > i,
                .actions > li > a:hover > i {
                    color: #000;
                }

            .actions.open > a:before,
            .actions > li.open > a:before {
                -webkit-transform: scale(1);
                -ms-transform: scale(1);
                -o-transform: scale(1);
                transform: scale(1);
                opacity: 1;
                filter: alpha(opacity=100);
            }

        .actions.actions-alt > li.open > a > i,
        .actions.actions-alt > li > a > i,
        .actions.actions-alt > li > a > i:hover {
            color: #fff;
        }

        .actions.open {
            z-index: 4;
        }

    .action-header {
        padding: 25px 30px;
        line-height: 100%;
        position: relative;
        z-index: 1;
        min-height: 65px;
        background-color: #F7F7F7;
    }

        .action-header .actions {
            position: absolute;
            top: 8px;
            right: 17px;
            z-index: 10;
        }


    .lgi-img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
    }
    /*.action-header {
        padding: 25px 30px;
        line-height: 100%;
        position: relative;
        z-index: 1;
        min-height: 65px;
        background-color: #F7F7F7;
            height: 50px !important;
    }*/

    .list-group {
        margin-bottom: 20px;
        padding-left: 0;
    }

    .card {
        background: #fff;
        margin-bottom: 20px;
        box-shadow: 0 2px 2px rgba(0,0,0,.15);
        margin-left: 10%;
        margin-right: 10%;
        min-height: 610px;
        /*margin-top: 1%;*/
    }

    .block-header {
        padding: 0 22px;
    }

    .block-header {
        margin-bottom: 25px;
        position: relative;
        /*padding-left: 120px;*/
    }

    /*a {
        background: transparent;
        font-size: 11px !important;
        font-family: Arial !important;
    }*/

    .anchorlink {
        font-size: 12px !important;
        font-family: Arial !important;
    }

    .dropdown-menu > li > a {
        display: block;
        padding: 10px 15px;
        clear: both;
        color: #666666;
        font-weight: normal;
        line-height: 1.49;
        white-space: nowrap;
    }

    .block-header > h2 {
        font-size: 15px;
        color: #777;
        margin: 0;
        font-weight: 400;
        text-transform: uppercase;
        font-family: Arial;
    }

    .actions {
        list-style: none;
        padding: 0;
        z-index: 3;
        margin: 0;
    }

    .media > .pull-right {
        padding-left: 15px;
    }

    .actions > a, .actions > li > a {
        width: 30px;
        height: 30px;
        display: inline-block;
        text-align: center;
        padding-top: 5px;
    }

    a:focus, a:hover {
        color: #0a6ebd;
        text-decoration: none;
    }

    a {
        background-color: transparent;
    }

    .actions > a > i, .actions > li > a > i {
        color: #adadad;
        font-size: 20px;
    }

    .list-group .list-group-item {
        border: 0;
        margin: 0;
        padding: 15px 23px;
    }

    .card {
        box-shadow: 0 1px 1px rgba(0,0,0,.15);
    }

    .lgi-attrs {
        list-style: none;
        padding: 0;
        margin: 0;
    }

        .lgi-attrs > li {
            display: inline-block;
            border: 1px solid #e0e0e0;
            margin: 2px 2px 2px 0;
            padding: 2px 5px;
        }

        .lgi-attrs > li, .lgi-text {
            font-size: 12px;
            color: #777;
        }

    .maintitle {
        font-family: Arial;
        font-size: 13px;
        font-weight: 400;
        color: #000000 !important;
    }

    .subtitle {
        font-family: Arial;
        font-size: 12px;
        font-weight: 400;
        color: #666 !important;
    }



    .btn-info1 {
        color: #ffffff;
        background-color: #82E0AA;
    }


    .btn-info2 {
        color: #ffffff;
        background-color: #5D6D7E;
    }


    .btn-info3 {
        color: #ffffff;
        background-color: #AF7AC5;
    }


    .btn-info4 {
        color: #ffffff;
        background-color: #F1948A;
    }

    .btn-info5 {
        color: #ffffff;
        background-color: #2EDECC;
    }

    .btn-info6 {
        color: #ffffff;
        background-color: #FF5833;
    }

    .btn-infoL1 {
        color: #ffffff;
        background-color: #F2CCCC;
    }

    .btn-infoL2 {
        color: #ffffff;
        background-color: #E5BDE3;
    }

    .btn-infoL3 {
        color: #ffffff;
        background-color: #A9B0EC;
    }

    .btn-infoL4 {
        color: #ffffff;
        background-color: #B1F3C7;
    }

    .btn-infoL5 {
        color: #ffffff;
        background-color: #F0F3B1;
    }

    .btn-infoL6 {
        color: #ffffff;
        background-color: #F3DBB1;
    }


    .btn-infoL7 {
        color: #ffffff;
        background-color: #F0CEAE;
    }


    .btn-infoL8 {
        color: #ffffff;
        background-color: #F58A77;
    }

    .btn-infoL9 {
        color: #ffffff;
        background-color: #81CFF9;
    }

    .btn-infoL10 {
        color: #ffffff;
        background-color: #DBA5F2;
    }

    .list-group.lg-odd-black .list-group-item:nth-child(even) {
        background-color: #F7F7F7;
    }

    .input-group[class*="col-"] {
        float: left !important;
        padding-left: 0;
        padding-right: 0;
    }

    a,
    a label {
        cursor: pointer;
        color: black;
    }


    .k-combobox .k-dropdown-wrap .k-input {
        border: none;
        border-radius: 0px 0px;
        box-shadow: none !important;
        font-family: Arial;
        font-size: 12px;
    }

    .k-combobox .k-dropdown-wrap {
        border: none;
        border-bottom: 2px solid #dddddd;
        border-radius: 0px 0px;
    }

    .k-combobox .k-select {
        border: none;
        border-radius: 0px 0px;
    }

    .k-dropdown-wrap.k-state-default.k-state-focused {
        border: none;
        box-shadow: none;
        border-bottom: 2px solid #428bca;
    }

    .k-combobox {
        cursor: pointer;
    }

    div.list-group-item.divLast {
        border-bottom: 2px #2196F3 solid;
    }

    /*.divLast {
        border-bottom: 2px #2196F3 solid;
    }*/

    .z-depth-2 {
        /*box-shadow: 0 8px 17px rgba(0,0,0,.7),0 6px 20px rgba(0,0,0,.19);*/
        background-color: #428bca !important;
    }

    .blackiconcolor {
        color: #3bafda;
    }

    /*Show click to go to top button when scroll down..J*/
    .back-to-top {
        cursor: pointer;
        position: fixed;
        bottom: 20px;
        right: 20px;
        display: none;
    }

    #divfrmtitle {
        line-height: 38px;
        font-weight: 600 !important;
        font-size: 15px !important;
    }

    .panel-heading.panelhead {
        background: #e9e9e9 !important;
        height: 38px;
    }

    .panel-title {
        line-height: 34px !important;
    }

    /*body{
       overflow-y:hidden;
       min-height:100% !important;
    }*/

    /*Form Header to be shown always..S1*/
    #VisibleContentId {
        position: fixed;
        z-index: 600;
        width: 85.9%;
    }
</style>

<script type="text/javascript">

    $(document).on('click', '.list-group .list-group-item', function (e) {
        var $this = $(this);
        var current = document.querySelector('.divLast');
        var current1 = document.querySelector('.z-depth-2');
        if (current) {
            current.classList.remove('divLast');
        }
        if (current1) {
            current1.classList.remove('z-depth-2');
        }
        $this.addClass('divLast');
        $this.addClass('z-depth-2');
    }
    );
</script>


<section id="content_wrapper" style="background-color: #EEEEEE;min-height:103%;">
    <section id="content" class="animated fadeIn" style="padding-left:0px;padding-top:0px;padding-right:0px;padding-bottom:0px; height:100%;">

        <div class="row" id="VisibleContentId" style="background-color: #EEEEEE;">
            <div class="col-md-12">
                <div class="panel panel-visible" id="spy2" style="margin-bottom: 5px !important; background-color: #e7e7e7;height:38px;">
                    <div class="topbar-left">


                        <div class="panel-heading panelhead" style="background: #e9e9e9!important;height:38px;">
                            <div class="panel-heading-btn" style="float:right;margin-top: 3px;">
                                <button type="button" class="btn btn-sm btn-primary" title="New" onclick="onNewClick()" id="btnNew"><i class="fa fa-plus-square"></i></button>

                                <button type="button" class="btn btn-sm btn-primary" title="Import" onclick="onTransferInClick()" id="btnImport"><i class="fa fa-sign-in"></i></button>

                                <button type="button" class="btn btn-sm btn-primary" title="Refresh" onclick="onRefreshClick()" id="btRefresh"><i class="fa fa-refresh"></i></button>

                                <!--Changed from help.selltis.com/sale/index.html to below link. ref to tickt #2235: user manual link is  broken.  When click on Help and Index ..J-->
                                <button type="button" class="btn btn-sm btn-primary" title="Help" id="btHelp" onclick="window.open('http://selltis.com/wp-content/uploads/2017/05/Selltis-CRM-Users-Guide.pdf#page=32', '_blank'); return false;"><i class="fa fa-question"></i></button>

                                <button type="button" class="btn btn-sm btn-primary" title="Close" onclick="onCancel()" id="btClose"><i class="fa fa-times"></i></button>

                            </div>
                            @*<span id="divfrmtitle" style="line-height:38px;font-weight:bold;" class="panel-title"><i class="glyphicon glyphicon-th-large"></i>&nbsp;&nbsp;Open / Manage Desktops</span>*@
                            <h5 id="divfrmtitle" style="line-height:38px;font-weight:bold;" class="panel-title"><i class="glyphicon glyphicon-th-large"></i>&nbsp;&nbsp;Open / Manage Desktops</h5>
                        </div>




                    </div>
                </div>

                <div class="row" id="hiddenDiv" style="display:none">

                    <div class="row">
                        <div class="col-md-6">
                            <textarea id="txtDetails" style="width:100%;height:235px;">@Model.TextDetails</textarea>
                            @*<input id="txtDetails" type="text" inputmode="MultiLine" readonly="readonly" visible="false" style="width:417px;height:235px"   />*@
                        </div>

                        <div class="col-md-5" id="divTransferIn">
                            <div class="row">
                                <div class="col-md-1"><img id="imgDetailsTip" src="~/Content/Images/info.gif" /></div>
                                @if (!String.IsNullOrEmpty(@Model.DetailsTipText))
                                {
                                    <div class="col-lg-11"><label id="lblDetailsTip">@Model.DetailsTipText</label></div>
                                }
                                else
                                {
                                    <div class="col-lg-11"><label id="lblDetailsTip"></label></div>
                                }

                            </div>
                            <div class="row">
                                <div class="col-md-1"></div>
                                <div class="col-md-11"><label id="lblTransferInMode">If the desktop and views you are transferring in exist:</label></div>

                            </div>
                            <div class="row">
                                <div class="col-md-1"></div>
                                <div class="col-md-11">
                                    @Html.HiddenFor(m => m.SELTransferInImportRadiobuttons)
                                    @if (Model.SELTransferInImportRadiobuttons == "NEW")
                                    {
                                        @(Html.Kendo().RadioButton().Name("SEL_TrnsfrInModeCreateNew").HtmlAttributes(new { @name = "SEL_TransferInMode", @id = "rdobtn_SEL_TrnsfrInModeCreateNew" }).Label(" Create new").Checked(true))
                                    }
                                    else
                                    {
                                        @(Html.Kendo().RadioButton().Name("SEL_TrnsfrInModeCreateNew").HtmlAttributes(new { @name = "SEL_TransferInMode", @id = "rdobtn_SEL_TrnsfrInModeCreateNew", @onclick = "" }).Label(" Create new"))
                                    }
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-1"></div>
                                <div class="col-md-11">
                                    @Html.HiddenFor(m => m.SELTransferInImportRadiobuttons)
                                    @if (Model.SELTransferInImportRadiobuttons == "OVERWRITE")
                                    {
                                        @(Html.Kendo().RadioButton().Name("SEL_TrnsfrInModeOverride").HtmlAttributes(new { @name = "SEL_TransferInMode", @id = "rdobtn_SEL_TrnsfrInModeOverride" }).Label(" Overwrite existing (subject to permissions; removed views will be deleted permanently!)").Checked(true))                                            }
                                    else
                                    {
                                        @(Html.Kendo().RadioButton().Name("SEL_TrnsfrInModeOverride").HtmlAttributes(new { @name = "SEL_TransferInMode", @id = "rdobtn_SEL_TrnsfrInModeOverride", @onclick = "" }).Label(" Overwrite existing (subject to permissions; removed views will be deleted permanently!)"))
                                    }

                                </div>
                            </div>
                            <div class="row" id="divChkShared">
                                <div class="col-md-1"></div>
                                <div class="col-md-11">@Html.CheckBoxFor(m => m.IsShared, new { @id = "chk_shared" }) Create as shared</div>
                            </div>
                            <div class="row" id="divPermissionsWarning" style="display:none">
                                <div class="col-md-1"><img id="imgDetailsTip" src="~/Content/Images/info.gif" /></div>
                                <div class="col-md-11"><label id="lblPermissionsWarning">Sharing is disabled because you are not an author</label></div>
                            </div>
                            <div class="row" id="divlblTransferInName">
                                <div class="col-md-1"></div>
                                <div class="col-md-11"><label id="lblTransferInName">Desktop Name:</label></div>
                            </div>
                            <div class="row" id="divtxtTransferInName">
                                <div class="col-md-1"></div>
                                <div class="col-md-11"><input id="txtTransferInName" type="text" style="width:139px" /></div>
                            </div>
                            @*<div class="row">
                                    <div class="col-md-1"></div>
                                    <div class="col-md-11"><label id="lblTransferInProduct">For Product:</label></div>
                                </div>*@
                            @*<div class="row">
                                    <div class="col-md-1"></div>
                                    <div class="col-md-11">
                                        @(Html.Kendo().DropDownList()
                                          .Name("TransferInProduct")
                                          .BindTo(new List<SelectListItem>()
                                            {
                                                new SelectListItem() {
                                                    Text = "Sales", Value = "SA"
                                                }
                                            })
                                          .HtmlAttributes(new { style = "width: 139px;" })
                                        )
                                    </div>
                                </div>*@
                            <div class="row" style="margin-top:1%;">
                                <div class="col-md-1"></div>
                                <div class="col-md-11"><button id="btnTransferInImport" style="width:139px" onclick="onTransferInImportClick()">Transfer In Now</button></div>
                            </div>
                            <div class="row">
                                <div class="col-md-1"></div>
                                <div class="col-md-11"><label id="lblTransferInImport">Note: this can take a few minutes</label></div>
                            </div>
                        </div>

                        <div class="col-md-5" id="divTransferOut">
                            <div class="row">
                                <div class="col-md-1"><img id="imgDetailsTip" src="~/Content/Images/info.gif" /></div>
                                @if (!String.IsNullOrEmpty(@Model.LBL_DetailsTipText))
                                {
                                    <div class="col-lg-11"><label id="lblDetailsTipLabel">@Model.LBL_DetailsTipText</label></div>
                                }
                                else
                                {
                                    <div class="col-lg-11"><label id="lblDetailsTipLabel"></label></div>
                                }
                            </div>
                        </div>
                    </div>

                </div>

                <div class="row" id="PNL_DeleteMessageBox" style="display: none;">
                    <div class="col-md-2">

                    </div>
                    <div class="col-md-8">
                        <table class="table-curved" cellpadding="0" cellspacing="0" style="border-right: silver 0px; border-top: silver 0px; overflow: hidden; border-left: silver 0px; border-bottom: silver 0px; height: 8px; width: 100%; background: #FFFFB0;">
                            <tr style="background-color: #5b97cb">
                                <td style="height: 21px;">
                                    <table cellpadding="0" cellspacing="0" style="width: 100%">
                                        <tr>
                                            <td style="width: 90%; height: 17px;">
                                                <label id="LBL_MsgBoxTitle"
                                                       style="background-color: #5B97CB; border-color: #5B97CB; border-style: Solid; border-width: 2px; margin: 0px; font-weight: bold; font-family: Open Sans, Helvetica, Arial, sans-serif; font-size: 13px; color: white; width: 100%; margin-left: 10px;margin-top: -2px;">
                                                    LBL_MsgBoxTitle
                                                </label>
                                            </td>
                                            <td align="right" style="width: 10%; height: 17px; vertical-align: top;" valign="top">
                                                @*<img id="IMG_MsgBoxCloseDelete" src="~/Content/Images/CloseWhiteNoBorder.gif" style="cursor: pointer; border-width: 0px; margin-right: 10px;margin-top: 5px;" />*@
                                                <label id="IMG_MsgBoxCloseDelete" style="cursor: pointer; border-width: 0px; margin-right: 10px;margin-top: -2px;color: white;"><b>X</b></label>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" valign="top" style="vertical-align: top; padding: 4px 4px 0px 4px;">
                                    <label id="LBL_MsgBoxMessage"
                                           style="font-family:Open Sans, Helvetica, Arial, sans-serif;font-size:12px;margin-top: -5px;margin-bottom:2px;">
                                        LBL_MsgBoxMessage
                                    </label>
                                </td>
                            </tr>

                            <tr>
                                <td align="center" style="height: 30px; vertical-align: middle; padding: 4px 4px 4px 4px;" valign="middle">
                                    <input type="button" class="btn btn-xs btn-primary" name="BTN_MsgBox1" id="BTN_MsgBox1" value="Yes" style="display:none" onclick="onBTN_MsgBox1Click()" />
                                    <input type="button" class="btn btn-xs btn-primary" name="BTN_MsgBox2" id="BTN_MsgBox2" value="No" style="display:none" onclick="onBTN_MsgBox2Click()" />
                                    <input type="button" class="btn btn-xs btn-primary" name="BTN_MsgBox3" id="BTN_MsgBox3" value="Cancel" style="display:none" onclick="onBTN_MsgBox3Click()" />
                                </td>
                            </tr>
                        </table>

                    </div>
                    <div class="col-md-2">

                    </div>
                </div>

                <div class="row" id="PNL_DuplicateMessageBox" style="display:none;">
                    <div class="col-md-2">

                    </div>
                    <div class="col-md-8" style="background-color: #FFFFB0;border-radius: 10px 10px 10px 10px;">

                        <div class="row" style="background-color: #5B97CB;border-radius: 10px 10px 0px 0px;height: 25px;">
                            <div class="col-md-11">
                                <label id="LBL_Duplicate" style="color:white; font-weight:400; margin-left:7px;">
                                    <b>Duplicate Desktop</b>
                                </label>
                            </div>
                            <div class="col-md-1">
                                @*<img id="IMG_MsgBoxCloseDuplicate" src="~/Content/Images/CloseWhiteNoBorder.gif" style="cursor: pointer; border-width: 0px; margin-right: 10px;margin-top: 5px;" />*@
                                <label id="IMG_MsgBoxCloseDuplicate" style="cursor: pointer; border-width: 0px; margin-right: 5px;color: white; float:right;"><b>X</b></label>
                            </div>
                        </div>

                        <div class="row" style="margin-left:5px; margin-right:5px;">
                            <div id="div_LBL_DuplText" class="row" style="margin-top:2px;" style="display:none">
                                <label id="LBL_DuplText"></label>
                            </div>

                            <div class="row" style="margin-top:2px;">
                                <div class="col-md-5">
                                    <div class="col-md-4">
                                        <label id="LBL_Duplicate" style="float:right;">Name</label>
                                    </div>
                                    <div class="col-md-8">
                                        <input id="TXT_DuplName" type="text" style="width:100%;" />
                                    </div>
                                </div>
                                <div class="col-md-7">
                                    <div class="col-md-2">
                                        @if (@Model.CHK_DuplSharedEnabled)
                                        {
                                            @Html.CheckBoxFor(m => m.CHK_DuplSharedChecked, new { @id = "CHK_DuplShared" })
                                        }
                                        else
                                        {
                                            @Html.CheckBoxFor(m => m.CHK_DuplSharedChecked, new { @id = "CHK_DuplShared", @disabled = "disabled" })
                                        }
                                        <label for="CHK_DuplShared">Shared</label>                                       
                                    </div>
                                    <div id="CHK_DuplOverwriteExistingdiv" class="col-md-10" style="display:none">
                                        @Html.CheckBoxFor(m => m.IsShared, new { @id = "CHK_DuplOverwriteExisting" }) <label for="CHK_DuplOverwriteExisting">Overwrite existing desktop if found (keep same ID)</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top:2px;">
                                <div class="col-md-5">
                                    <div class="col-md-4">
                                        <label id="LBL_DuplNode" style="float:right;">Under Folder</label>
                                    </div>
                                    <div class="col-md-8">
                                        @(Html.Kendo().DropDownList()
                                                      .Name("CMB_DuplNode")
                                                      .DataTextField("Text")
                                                      .DataValueField("Value")
                                                      .DataSource(source =>
                                                      {
                                                          source.Read(read =>
                                                          {
                                                              read.Action("GetUnderNodeInfoList", "ManageDesktops");
                                                          });
                                                      })
                                                      .AutoBind(false)
                                                      //.BindTo(Model.FillUnderNodes)
                                                      //.Value(Model.CMB_DuplNodeSelectedValue)
                                                      .HtmlAttributes(new { @style = "width:100%" })
                                        )
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top:2px;">
                                <label id="LBL_DuplOverwriteExisting" style="width:100%;">Note: If the duplicate desktop should have the same identity under the other product (for example, because alerts open it both in Sales and Mobile), check Overwrite existing. Be careful, though: if the desktop exists and has been modified in the other product, Overwrite existing will remove those changes.</label>
                            </div>
                            <div class="row" align="center" style="margin-top:2px;">
                                <input type="button" class="btn btn-sm btn-primary" name="BTN_DuplOK" id="BTN_DuplOK" value="Create Desktop" onclick="BTN_DuplOK_Click()" />
                                <input type="button" class="btn btn-sm btn-primary" name="BTN_DuplCancel" id="BTN_DuplCancel" value="Cancel" onclick="BTN_DuplCancel_Click()" />
                            </div>
                            <div class="row" style="margin-top:2px;"></div>

                        </div>

                    </div>
                    <div class="col-md-2">

                    </div>
                </div>

                <div class="row" id="pnlError" style="display: none; background-color: #FFFFD0; border-color: Gray; border-width: 1px; border-style: Solid; height: 22px; width: 100%; border-radius: 5px; padding: 2px 3px; ">
                    <div class="col-md-12">

                        <div class="col-md-12">
                            <span><i class="fa fa-exclamation-triangle" aria-hidden="true"></i></span>
                            <span id="lblError" style="color:Black;font-family:Verdana,Arial,Helvetica;font-size:8pt;">
                            </span>
                        </div>
                    </div>
                </div>

                <div class="row" style="height:10px;"></div>

                <div class="row" id="DesktopManagerHeader" style="border: 1px solid #cccccc;background-color:#FAFAFA !important;margin-left: 10%;margin-right: calc(10% - 6px);">
                    <div class="col-md-11">
                        <div class="col-md-3  input-group" style="padding-top:5px;padding-left:10px;padding-bottom:5px;">
                            <input type="text" name="SearchDesktop" id="SearchDesktop" placeholder="Enter Desktop Name.." class="form-control input-sm" />
                            @*@Html.TextBox("SearchDesktop", "Value", new { placeholder = "Enter Desktop Name..", @class = "form-control input-sm", @style = "display:block" })*@
                            <span class="input-group-addon" style="cursor:pointer;" onclick="OnSearchClick()">
                                <i class="fa fa-search" style="color: #3498DB; "></i>
                            </span>
                        </div>
                        @*<div class="col-md-3" style="padding-top:5px;padding-left:10px;padding-bottom:5px;">
                                 @(Html.Kendo().ComboBox()
                             .HtmlAttributes(new { style = "width:100%" })
                             .Name("Sort")
                             .DataTextField("Text")
                             .DataValueField("Value")
                             .AutoBind(true)
                             .SelectedIndex(0)
                            // .Events(e => e.Change("ComboBoxChanged"))
                             .BindTo(new List<SelectListItem>() {
                             new SelectListItem() {
                             Text = "- Select Sort Type -", Value = "1"
                             },
                             new SelectListItem() {
                             Text = "Desktop Name", Value = "Text"
                             },
                             new SelectListItem() {
                             Text = "Type", Value = "Type"
                             }
                             //new SelectListItem() {
                             //  Text = "Modified Date", Value = "ModifiedDate"
                             //},
                             //new SelectListItem() {
                             //Text = "Modified By", Value = "ModifiedBy"
                             //},
                             //new SelectListItem() {
                             //Text = "Created Date", Value = "CreatedDate"
                             //},
                             //new SelectListItem() {
                             //Text = "Created By", Value = "CreatedBy"
                             //}
                             })
                                 )
                             </div>*@
                        <div class="col-md-3" style="padding-top:5px;padding-left:10px;padding-bottom:5px;">
                            <div class="toggle-switch toggle-switch-demo" data-ts-color="blue">
                                <label for="billable" class="ts-label anchorlink" style="padding-right:10px;">Show Shared Only</label>
                                <input id="billable" type="checkbox" style="display:none">
                                <label for="billable" class="ts-helper"></label>
                            </div>
                        </div>
                        <div class="col-md-1" style="padding-top:8px;">
                            <button class=" btn btn-xs btn-primary m-l-5" onclick="onGoClick();" type="button">
                                Go
                            </button>
                        </div>

                    </div>
                    <div class="col-md-1" style="padding-top:8px;">
                        <label id="lblDesktopsListCount" style="float:right;padding-right:10px;">Count:@Model.DesktopListItems.Count </label>
                    </div>
                </div>
            </div>
        </div>
        <div id="DummyDivForFormContent" style="height:94px;"></div>
        <div class="card">
            <div class="">
                <div class="col-md-12">

                </div>

            </div>
            <div class="row">
                @*<br />*@
            </div>
            <input type="hidden" value="@ViewBag.SelectedDesktopID" id="hdnDesktopId" />
            <div id="eventList" class="list-group lg-odd-black">
                @foreach (var person in Model.DesktopListItems)
                {

                    <div class="list-group-item" id="@person.Value" onclick="onRowSelected(@person.Index)" ondblclick="onOpenClick(@person.Index)">
                        @*z-depth-2 divLast*@

                        <div class="pull-left" style="padding-right: 10px;">
                            @*<img class="lgi-img" src="~/Content/Images/Profile1.png" alt=" ">*@
                            @if (@person.Type == "View")
                            {
                                <span style="border-radius:64px;" class="btn btn-circle-sm btn-info" onclick="onOpenClick(@person.Index);return false;">V</span>
                            }
                            else if (@person.Type == "Linked View")
                            {
                                <span style="border-radius:64px;" class="btn btn-circle-sm btn-info1" onclick="onOpenClick(@person.Index);return false;">LV</span>
                            }
                            else if (@person.Type == "Tabbed View")
                            {
                                <span style="border-radius:64px;" class="btn btn-circle-sm btn-info2" onclick="onOpenClick(@person.Index);return false;">TV</span>
                            }
                            else if (@person.Type == "Calendar")
                            {
                                <span style="border-radius:64px;" class="btn btn-circle-sm btn-info3" onclick="onOpenClick(@person.Index);return false;">C</span>
                            }
                            else if (@person.Type == "Chart")
                            {
                                <span style="border-radius:64px;" class="btn btn-circle-sm btn-info4" onclick="onOpenClick(@person.Index);return false;">Ch</span>
                            }
                            else if (@person.Type == "Report")
                            {
                                <span style="border-radius:64px;" class="btn btn-circle-sm btn-info5" onclick="onOpenClick(@person.Index);return false;">R</span>
                            }
                            else
                            {
                                <span style="border-radius:64px;" class="btn btn-circle-sm btn-info6" onclick="onOpenClick(@person.Index);return false;">V</span>
                            }
                        </div>

                        <div class="pull-right">
                            <div class="actions dropdown apps">
                                <a href="" data-toggle="dropdown" aria-expanded="true">
                                    <i class="fa fa-ellipsis-v"></i>
                                </a>

                                <ul class="dropdown-menu dropdown-menu-right">
                                    <li>
                                        <a href="" onclick="onOpenClick(@person.Index);return false;"><i class="fa fa-folder-open-o"></i>&nbsp;&nbsp;Open</a>
                                    </li>
                                    <li>
                                        <a href="" onclick="onEditClick(@person.Index);return false;"><i class="fa fa-pencil"></i>&nbsp;&nbsp;Edit</a>
                                    </li>
                                    <li>
                                        <a href="" onclick="onDuplicateClick(@person.Index); return false;"><i class="fa fa-files-o"></i>&nbsp;&nbsp;Duplicate</a>
                                    </li>
                                    <li>
                                        <a href="" onclick="onDeleteClick(@person.Index); return false;"><i class="fa fa-trash-o"></i>&nbsp;&nbsp;Delete</a>
                                    </li>
                                    <li>
                                        <a href="" onclick="onTransferOutClick(@person.Index); return false;"><i class="fa fa-sign-out"></i>&nbsp;&nbsp;Export</a>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="media-body">
                            <div class="maintitle"><a class="anchorlink" onclick="onOpenClick(@person.Index)">@person.Text</a></div>
                            <div class="subtitle">@person.Description</div>
                            @*@person.Value*@
                            <ul class="lgi-attrs">
                                @if (@person.IsShared == "True")
                                {
                                    <li><i title="Shared" class="fa fa-1x fa-users blackiconcolor"></i></li>
                                }
                                @{ int counter = 0;
                                }

                                @foreach (string s in @person.DesktopNames.Split(','))
                                {
                                    var classes = new[] { "btn-infoL1", "btn-infoL2", "btn-infoL3", "btn-infoL4", "btn-infoL5", "btn-infoL6", "btn-infoL7", "btn-infoL8", "btn-infoL9", "btn-infoL10" };

                                    if (s != "")
                                    {
                                        var className = classes[counter];
                                        <li class="@className">@s</li>
                                        counter++;
                                        if (counter == 10)
                                        {
                                            counter = 0;
                                        }
                                    }
                                }
                                @*<li>Created by: @person.CreatedBy</li>
                                    <li>Modified by: @person.ModifiedBy</li>*@
                            </ul>
                        </div>
                    </div>
                                    }

            </div>
        </div>
    </section>
</section>

@*Show click to go to top button when scroll down..J*@
<a id="back-to-top" href="#" class="btn btn-primary btn-sm back-to-top" role="button" title="Go to top" data-placement="left"><span class="glyphicon glyphicon-chevron-up"></span></a>

<script type="text/javascript">

    function onGoClick() {
        showProgress();

        var SelectedDesktopId = $("#hdnDesktopId").val();
        var _searchvalue = $("#SearchDesktop").val();
        //var _sortType = $("#Sort").data("kendoComboBox").value();
        //var _sortType = $("#Sort").data("kendoComboBox").value();
        var _check = $("#billable").is(':checked');

        window.location.href = "/ManageDesktops/OpenDesktopManager?DesktopId=" + SelectedDesktopId + "&SearchValue=" + _searchvalue + "&SortBy=" + "" + "&Showshared=" + _check;

    }

    function onRefreshClick() {
        showProgress();
        var SelectedDesktopId = $("#hdnDesktopId").val();
        window.location.href = "/ManageDesktops/OpenDesktopManager?DesktopId=" + SelectedDesktopId + "&SearchValue=" + "" + "&SortBy=" + "" + "&Showshared=" + "";
    }

    //function ComboBoxChanged(e) {
    //    var value = this.value();
    //    var SelectedDesktopId = $("#hdnDesktopId").val();
    //    var _searchvalue = $("#SearchDesktop").val();
    //    var _check = $("#billable").is(':checked');
    //    window.location.href = "/ManageDesktops/OpenDesktopManager?DesktopId=" + SelectedDesktopId + "&SearchValue=" + "" + "&SortBy=" + value + "&Showshared=" + "";

    //}


    //$('#billable').change(function () {
    //    // $('#billable').val($(this).is(':checked'));
    //    var SelectedDesktopId = $("#hdnDesktopId").val();
    //    var _check = $("#billable").is(':checked');
    //    var _searchvalue = $("#SearchDesktop").val();
    //    window.location.href = "/ManageDesktops/OpenDesktopManager?DesktopId=" + SelectedDesktopId + "&SearchValue=" + "" + "&SortBy=" + "" + "&Showshared="+_check;

    //});


    $("#SearchDesktop").on("keydown", function (e) {
        //debugger;
        //var SelectedDesktopId = $("#hdnDesktopId").val();
        //var _searchvalue = $("#SearchDesktop").val();
        //if (e.keyCode == 13) {
        //    if (_searchvalue.trim() == "") {
        //        alert("Please enter Desktop Name ");
        //    }
        //    else {
        //        window.location.href = "/ManageDesktops/OpenDesktopManager?DesktopId=" + SelectedDesktopId + "&SearchValue=" + _searchvalue + "&SortBy=" + "" + "&Showshared=" + "";;
        //    }
        //}


        if (e.keyCode == 13) {
            onGoClick();
        }


    })

    function OnSearchClick() {
        //debugger;

        onGoClick();

        //var _searchvalue = $("#SearchDesktop").val();
        //if (_searchvalue.trim() == "") {
        //    alert("Please enter Desktop Name ");
        //}
        //else {
        //    var SelectedDesktopId = $("#hdnDesktopId").val();
        //    window.location.href = "/ManageDesktops/OpenDesktopManager?DesktopId=" + SelectedDesktopId + "&SearchValue=" + _searchvalue + "&SortBy=" + "" + "&Showshared=" + "";;
        //}
    }

    function onRowSelected(Index) {
        //  var grid = $("#DesktopListGrid").data("kendoGrid");
        //  var dataRows = grid.items();
        var rowIndex = Index;
        if (rowIndex >= 0) {
            $("#divCreatedModifiedUnderNode").show();
        }
        $("#hiddenDiv").hide();
        $.ajax({
            url: "/ManageDesktops/SelectedIndexDetails",
            type: "POST",
            data: { SelectedIndex: rowIndex },
            success: function (data) {

                $("#hdnDesktopId").val(data.SelectedDesktopId);
                $('#STA_CREATEDBYLABEL').text(data.StaCreatedBy);
                $('#STA_MODBYLABEL').text(data.StaModeBy);
                $('#STA_DESKTOPNODELABEL').text(data.StaDesktopNode);
                Scripts(data);
                //$("#hiddenDiv").hide();
            },
            error: function (data) {

            }
        })
    }

    $(document).ready(function () {

        //Setting Header & Tabstrip width.. S1
        setTimeout(SettingHeaderHeightWidth, 200);

        //Show click to go to top button when scroll down..J
        $(window).scroll(function () {
            //$('#eventList').scroll(function () {
            if ($(this).scrollTop() > 50) {
                $('#back-to-top').fadeIn();
            } else {
                $('#back-to-top').fadeOut();
            }
            //CheckDesktopManagerHeader();
        });
        // scroll body to 0px on click
        $('#back-to-top').click(function () {
            $('#back-to-top').tooltip('hide');
            $('body,html').animate({
                //$('#eventList').animate({
                scrollTop: 0
            }, 800);
            return false;
        });
        $('#back-to-top').tooltip('show');

        //CheckDesktopManagerHeader();

        //  debugger;

        //$("#Sort").data("kendoComboBox").input.on("focus", function () {
        //    $("#Sort").data("kendoComboBox").open();
        //});

        $("#pnlError").hide();

        if ('@Model.DesktopListCount' == '0') {
            $("#pnlError").show();
            $("#lblError").html("No desktops found");
        }

        var SelectedDesktopId = $("#hdnDesktopId").val();
        var ID = SelectedDesktopId.replace(" ", "");

        if (ID != "" && ($('#' + ID + "D").length > 0)) {

            $('#' + ID + "D").addClass("divLast");
            $('#' + ID + "D").addClass("z-depth-2");

            $('html, body').animate({
                //$('#eventList').animate({
                scrollTop: $('#' + ID + "D").offset().top - 180
            }, 100);

        }
        else {
            $("div.list-group-item:first").addClass("divLast");
            $("div.list-group-item:first").addClass("z-depth-2");
        }


        //$("div.list-group list-group-item:first").addClass("divLast");
        //$(".list-group list-group-item").first().addClass("divLast");
        //$(".list-group list-group-item").eq(0).addClass("z-depth-2");

        //$(".list-group  list-group-item:eq(0)").addClass("active");
        //if ($("#hdnDesktopId").val().indexOf("|SA") == -1) {
        //    SelectedDesktopId = $("#hdnDesktopId").val().concat("|SA");
        //}
        // $('.list-group > list-group-item').removeClass('active');

        @*$('@ViewBag.SelectedDesktopID').find('.list-group-item').addClass("divLast");
            $('@ViewBag.SelectedDesktopID').addClass("divLast");
            $('@ViewBag.SelectedDesktopID').addClass("z-depth-2");*@


        var _obj = new Object();
        _obj.IsProductLabelVisible = '@Model.IsProductLabelVisible';
        _obj.IsForProductVisible = '@Model.IsForProductVisible';
        _obj.IsOpenButtonEnable = '@Model.IsOpenButtonEnable';
        _obj.IsEditButtonEnable = '@Model.IsEditButtonEnable';
        _obj.IsDuplicateButtonEnable = '@Model.IsDuplicateButtonEnable';
        _obj.IsDeleteButtonEnable = '@Model.IsDeleteButtonEnable';
        _obj.IsTransferInButtonEnable = '@Model.IsTransferInButtonEnable';
        _obj.IsTransferOutButtonEnable = '@Model.IsTransferOutButtonEnable';

        Scripts(_obj);

        // debugger;
        if ('@ViewBag.SharedOnly' == 'true') {
            $('#billable').prop('checked', true);
        }
        if ('@ViewBag.SearchText' != '') {
            $('#SearchDesktop').val('@ViewBag.SearchText');
        }
        if ('@ViewBag.SortBy' != '') {
            $('#Sort').data("kendoComboBox").value('@ViewBag.SortBy');
        }

        ////List items css changed to scroll and set height
        //var docHeight = $(window).height();
        //var onePercentValue = Math.round((docHeight) / 100);
        //docHeight = docHeight - 127 - onePercentValue - $('#DesktopManagerHeader').outerHeight();

        //$(".card").css('min-height', docHeight);
        //$("#eventList").css('height', docHeight);
        //$("#eventList").css('max-height', docHeight);
        //$("#eventList").css('min-height', docHeight);

        //Enable focus to the search box on load..J
        $("#SearchDesktop").focus();

    });
    function onOpenClick(Index) {
        //debugger;

        sessionStorage.removeItem("type");
        //  var grid = $("#DesktopListGrid").data("kendoGrid");
        //  var dataRows = grid.items();
        var rowIndex = Index;// dataRows.index(grid.select());
        showProgress();
        var mode = "Manage";
        $.ajax({
            url: "/ManageDesktops/OnOpenClick",
            type: "POST",
            data: { SelectedIndex: rowIndex, Mode: mode },
            success: function (data) {

                //clear previous splitter changes from browser cookie..J
                ClearDesktopSplitterCookie()
                //debugger
                window.location.href = data;
            },
            error: function (data) {

            }
        })
    }

    function ClearDesktopSplitterCookie() {

        var SelectedDesktopId = $("#hdnDesktopId").val();
        if (SelectedDesktopId.indexOf('|SA') > -1) {
            SelectedDesktopId = SelectedDesktopId.replace('|SA', '');
        }

        $.ajax({
            url: "/ManageDesktops/GetViewIdsInDesktop",
            type: "POST",
            data: { DesktopId: SelectedDesktopId },
            success: function (data) {

                //To clear views panes sizes..J
                if (data.indexOf(',') > -1) {
                    var ViewIDs = data.split(',');
                    for (var i = 0; i < ViewIDs.length; i++) {
                        eraseCookie("DS_" + SelectedDesktopId + "_view" + ViewIDs[i].replace(' ', ''));
                    }
                }

                //To clear top and tab view panes sizes..J
                eraseCookie("DS_" + SelectedDesktopId + "_splitterT");
                eraseCookie("DS_" + SelectedDesktopId + "_divheight");
            },
            error: function (data) {

            }
        })
    }

    function createCookie(cookieName, cookieVal, days) {
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            var expires = ";expires=" + date.toGMTString();
        }
        else var expires = "";
        document.cookie = cookieName + "=" + cookieVal + expires + "; path=/";
    }

    function eraseCookie(name) {
        createCookie(name, "", -1);
    }

    function onNewClick() {
        showProgress();
        $.ajax({
            url: "/ManageDesktops/OnNewClick",
            type: "POST",
            success: function (data) {
                window.location.href = data;
            },
            error: function (data) {

            }
        })
    }

    function onEditClick(Index) {

        var rowIndex = Index;
        showProgress();
        $.ajax({
            url: "/ManageDesktops/OnEditClick",
            type: "POST",
            data: { SelectedIndex: rowIndex },
            success: function (data) {
                window.location.href = data;
            },
            error: function (data) {

            }
        })
    }

    function onDuplicateClick(Index) {
        // debugger;
        showProgress();
        $("#CHK_DuplOverwriteExistingdiv").hide();
        document.getElementById("LBL_DuplOverwriteExisting").style.display = "none";

        var DuplicateNode = $('#CMB_DuplNode').val();
        var DuplicateIsShared = $('#CHK_DuplShared')[0].checked;

        $.ajax({
            url: "/ManageDesktops/OnDuplicateClick",
            type: "POST",
            data: { DuplicateNode: DuplicateNode, DuplicateIsShared: DuplicateIsShared },
            success: function (data) {
                // debugger;
                DisplayDuplicateMessageBox(data);
                $("html, body").animate({ scrollTop: 0 }, 100);
                hideProgress();
            },
            error: function (data) {
                hideProgress();
            }
        })
    }

    function DisplayDuplicateMessageBox(data) {
        // debugger;
        if (data.PNL_DuplicateVisible == true) {
            $("#PNL_DuplicateMessageBox").show();
            $("#TXT_DuplName").val(data.TXT_DuplName);
            if (data.CHK_DuplSharedChecked == true) {
                $("#CHK_DuplShared").prop("checked", true);
            }
            else {
                $("#CHK_DuplShared").prop("checked", false);
            }
            if (data.CHK_DuplSharedEnabled == true) {
                $("#CHK_DuplShared").prop("visible", true);
            }
            else {
                $("#CHK_DuplShared").prop("visible", false);
            }
            if (data.LBL_DuplProductVisible == true) {
                $("#forProductDiv").show();
            }
            else {
                $("#forProductDiv").hide();
            }
        }
        else {
            $("#PNL_DuplicateMessageBox").hide();
        }
    }

    function onCancel() {
        showProgress();
        var desktopId = '@Util.GetSessionValue("DesktopId")'; @*//'@ViewBag.SelectedDesktopID';*@
        @*var webClintBtnId = '@ViewBag.WebClintBtnId';*@

        var FolderId = '@ViewBag.FolderId';
        if (FolderId == null || FolderId == '' || FolderId == undefined) {
            FolderId = 'FOLDERID';
        }
        var HistoryKey = '@ViewBag.HistoryKey';

        if (desktopId != '') {
            //window.location = '/Desktop/LoadDesktop?DesktopId=' + desktopId;
            //window.location = '/Desktop/LoadDesktop?DesktopId=' + desktopId + '&FolderId=' + FolderId + '&Key=' + HistoryKey;
            window.location.href = "/Desktop/LoadDesktop/" + desktopId + "/true/" + FolderId + "/" + HistoryKey;
        }
        else {
            //window.history.back();
            window.location = '/Login/LoginSubmit';
        }
    }

    function onDeleteClick(Index) {
        // debugger
        //$("#hiddenDiv").hide();

        //  var grid = $("#DesktopListGrid").data("kendoGrid");
        //   var dataRows = grid.items();
        var rowIndex = Index; //dataRows.index(grid.select());
        showProgress();
        $.ajax({
            url: "/ManageDesktops/OnDeleteClick",
            type: "POST",
            data: { SelectedIndex: rowIndex },
            success: function (data) {
                //debugger;
                DisplayMessageBox(data);

                $("#PNL_DeleteMessageBox").show();
                $("html, body").animate({ scrollTop: 0 }, 100);
                hideProgress();
            },
            error: function (data) {
                hideProgress();
            }
        })
    }

    $("#IMG_MsgBoxCloseDuplicate").click(function () {
        $("#PNL_DuplicateMessageBox").hide();
    });

    $("#IMG_MsgBoxCloseDelete").click(function () {
        $("#PNL_DeleteMessageBox").hide();
    });

    $("input[name='SEL_TransferInMode']").change(function () {
        if (this.id.indexOf("Override") > 0) {
            $('#divChkShared').hide();
            //$('#divPermissionsWarning').hide();
            $('#divlblTransferInName').hide();
            $('#divtxtTransferInName').hide();
        }
        else {
            $('#divChkShared').show();
            //$('#divPermissionsWarning').show();
            $('#divlblTransferInName').show();
            $('#divtxtTransferInName').show();
        }
    });

    function DisplayMessageBox(data) {
        $("#pnlError").hide();

        if (data.MessageBoxDisplay == true) {

            $("#PNL_DeleteMessageBox").show();
            $("#LBL_MsgBoxTitle").text(data.LBL_MsgBoxTitleText);
            var _LBL_MsgBoxMessage = data.LBL_MsgBoxMessageText.replace("/n/n", "</br></br>").replace("/n", "</br>");
            $("#LBL_MsgBoxMessage").html(_LBL_MsgBoxMessage);


            if (data.BTN_MsgBox1Visible == true) {
                $("#BTN_MsgBox1").show();

                $("#BTN_MsgBox1").attr('value', data.BTN_MsgBox1Text);
                $("#BTN_MsgBox1").prop('value', data.BTN_MsgBox1Text);
                $("#BTN_MsgBox1").html(data.BTN_MsgBox1Text);
            }
            else {
                $("#BTN_MsgBox1").hide();
            }

            if (data.BTN_MsgBox2Visible == true) {
                $("#BTN_MsgBox2").show();

                $("#BTN_MsgBox2").attr('value', data.BTN_MsgBox2Text);
                $("#BTN_MsgBox2").prop('value', data.BTN_MsgBox2Text);
                $("#BTN_MsgBox2").html(data.BTN_MsgBox2Text);
            }
            else {
                $("#BTN_MsgBox2").hide();
            }

            if (data.BTN_MsgBox3Visible == true) {
                $("#BTN_MsgBox3").show();

                $("#BTN_MsgBox3").attr('value', data.BTN_MsgBox3Text);
                $("#BTN_MsgBox3").prop('value', data.BTN_MsgBox3Text);
                $("#BTN_MsgBox3").html(data.BTN_MsgBox3Text);
            }
            else {
                $("#BTN_MsgBox3").hide();
            }

        }
        else if (data.IsError == true) {
            $("#pnlError").show();
        }
    }

    function Scripts(data) {
        var IsProductLabelVisible = data.IsProductLabelVisible.toString();
        var IsForProductVisible = data.IsForProductVisible.toString();
        if (IsProductLabelVisible.toLowerCase() == "true") {
            $("#lblForProduct").visible = true;
        }
        else {
            $("#lblForProduct").visible = false;
        }

        if (IsForProductVisible.toLowerCase() == "true") {
            $("#txtForProduct").visible = true;
        }
        else {
            $("#txtForProduct").visible = false;
        }

        var IsOpenButtonEnable = data.IsOpenButtonEnable.toString();
        var IsEditButtonEnable = data.IsEditButtonEnable.toString();
        var IsDuplicateButtonEnable = data.IsDuplicateButtonEnable.toString();
        var IsDeleteButtonEnable = data.IsDeleteButtonEnable.toString();
        var IsTransferInButtonEnable = data.IsTransferInButtonEnable.toString();
        var IsTransferOutButtonEnable = data.IsTransferOutButtonEnable.toString();

        //Enable open button
        if (IsOpenButtonEnable.toLowerCase() == "true") {
            $('#btnOpen').prop("disabled", false);
        }
        else {
            $('#btnOpen').prop("disabled", true);
        }

        //Enable Edit button
        if (IsEditButtonEnable.toLowerCase() == "true") {
            $('#btnEdit').prop("disabled", false);
        }
        else {
            $('#btnEdit').prop("disabled", true);
        }

        //Enable Duplicate button
        if (IsDuplicateButtonEnable.toLowerCase() == "true") {
            $('#btnDuplicate').prop("disabled", false);
        }
        else {
            $('#btnDuplicate').prop("disabled", true);
        }

        //Enable Delete button
        if (IsDeleteButtonEnable.toLowerCase() == "true") {
            $('#btnDelete').prop("disabled", false);
        }
        else {
            $('#btnDelete').prop("disabled", true);
        }

        //Enable TransferIn button
        if (IsTransferInButtonEnable.toLowerCase() == "true") {
            $('#btnTransferIn').prop("disabled", false);
        }
        else {
            $('#btnTransferIn').prop("disabled", true);
        }
        //Enable TransferOut button
        if (IsTransferOutButtonEnable.toLowerCase() == "true") {
            $('#btnTransferOut').prop("disabled", false);
        }
        else {
            $('#btnTransferOut').prop("disabled", true);
        }
    }
    function onTransferInImportClick() {

        //var grid = $("#DesktopListGrid").data("kendoGrid");
        //var dataRows = grid.items();
        //var rowIndex = dataRows.index(grid.select());
        showProgress();
        var details = $('#txtDetails').val();
        var TransferInName = $('#txtTransferInName').val();
        var Details = $('#txtDetails').val();
        //var TransferInProduct = $('#TransferInProduct').val();
        var isNew = $('#rdobtn_SEL_TrnsfrInModeCreateNew')[0].checked;
        var chk_shared = $('#chk_shared')[0].checked;

        $.ajax({
            url: '/ManageDesktops/OnTransferInImportClick',
            data: { TransferInName: TransferInName, Details: Details, IsNew: isNew, ChkShared: chk_shared },
            type: 'POST',
            success: function (data) {
                //debugger
                if (data.MessageBoxDisplay == true) {
                    DisplayMessageBox(data);
                }
                else {

                    //debugger
                    var NewlyCreatedOrEditedDesktopId = data.NewlyCreatedOrEditedDesktopId.replace('|SA', '')
                    $("#hdnDesktopId").val(NewlyCreatedOrEditedDesktopId.toUpperCase())

                    //  $('#DesktopListGrid').data('kendoGrid').dataSource.read();

                    $('#STA_CREATEDBYLABEL').text(data.StaCreatedBy);
                    $('#STA_MODBYLABEL').text(data.StaModeBy);
                    $('#STA_DESKTOPNODELABEL').text(data.StaDesktopNode);
                    $("#desktopListCount").text(data.DesktopListCount);
                    $("#hiddenDiv").hide();
                }
                hideProgress();

            },
            error: function (data) {
                hideProgress();
            }
        })
    }


    function onTransferInClick() {
        //debugger;
        $("#hiddenDiv").show();
        $("#divTransferIn").show();
        $("#divTransferOut").hide();
        $('#txtDetails').prop("readonly", false);

        var grid = $("#DesktopListGrid").data("kendoGrid");
        var dataRows = grid.items();
        var rowIndex = dataRows.index(grid.select());

        $.ajax({
            url: "/ManageDesktops/OnTransferInClick",
            type: "POST",
            data: { SelectedIndex: rowIndex },
            success: function (data) {
                $('#STA_CREATEDBYLABEL').text(data.StaCreatedBy);
                $('#STA_MODBYLABEL').text(data.StaModeBy);
                $('#STA_DESKTOPNODELABEL').text(data.StaDesktopNode);
                $('#lblDetailsTip').text(data.DetailsTipText);
                $('#txtDetails').text(data.TextDetails);
                Scripts(data);
                if (data.IsPermissionsWarningLabelVisible.toString().toLowerCase() == "true") {
                    $("#CHK_DuplOverwriteExisting").prop('checked', false);
                    $('#CHK_DuplOverwriteExisting').prop("disabled", true);
                    $('#divPermissionsWarning').show();
                }
            },
            error: function (data) {

            }
        })
    }
    function onTransferOutClick(Index) {
        //debugger;
        $("#hiddenDiv").show();
        $("#divTransferIn").hide();
        $("#divTransferOut").show();
        $('#txtDetails').prop("readonly", true);

        // var grid = $("#DesktopListGrid").data("kendoGrid");
        // var dataRows = grid.items();
        var rowIndex = Index; //dataRows.index(grid.select());
        showProgress();
        $.ajax({
            url: "/ManageDesktops/OnTransferOutClick",
            type: "POST",
            data: { SelectedIndex: rowIndex },
            success: function (data) {
                $('#STA_CREATEDBYLABEL').text(data.StaCreatedBy);
                $('#STA_MODBYLABEL').text(data.StaModeBy);
                $('#STA_DESKTOPNODELABEL').text(data.StaDesktopNode);
                $('#lblDetailsTipLabel').text(data.LBL_DetailsTipText);
                $('#txtDetails').text(data.TextDetails);
                Scripts(data);
                hideProgress();
            },
            error: function (data) {
                hideProgress();
            }
        })

        $("html, body").animate({ scrollTop: 0 }, 100);
    }

    function BTN_DuplOK_Click() {
        showProgress();
        var DuplicateName = $('#TXT_DuplName').val();
        var DuplicateNode = $('#CMB_DuplNode').val();
        var DuplicateNodeName = $("#CMB_DuplNode").data("kendoDropDownList").text();
        var DuplicateIsShared = $('#CHK_DuplShared')[0].checked;
        var DuplicateIsOverWriteExisting = $('#CHK_DuplOverwriteExisting')[0].checked;


        $.ajax({
            url: "/ManageDesktops/OnDuplicateOkClick",
            type: "POST",
            data: { DuplicateName: DuplicateName, DuplicateNode: DuplicateNode, DuplicateNodeName: DuplicateNodeName, DuplicateIsShared: DuplicateIsShared, DuplicateIsOverWriteExisting: DuplicateIsOverWriteExisting },
            success: function (data) {
                $("#hdnDesktopId").val(data.DuplicateCreatedDesktopId);

                DisplayDuplicateMessageBox(data);
                DisplayMessageBox(data)
                hideProgress();
            },
            error: function (data) {
                hideProgress();
            }
        })
    }


    function BTN_DuplCancel_Click() {
        $("#TXT_DuplName").text("");
        $('#chk_shared').prop('checked', false);
        $("#PNL_DuplicateMessageBox").hide();
    }

    function onBTN_MsgBox1Click() {
        showProgress();
        $.ajax({
            url: "/ManageDesktops/OnBTN_MsgBox1Click",
            type: "POST",
            success: function (data) {

                if (data.MessageBoxPurpose == "DeleteDesktop") {
                    //  var grid = $("#DesktopListGrid").data("kendoGrid");
                    //   var dataRows = grid.items();
                    //   var rowIndex = dataRows.index(grid.select());
                    //   var row = dataRows[rowIndex + 1];

                    //  if (row != null) {
                    //   if (row.textContent.indexOf("DSK_") > -1) {
                    //   var DesktopIdAfterDeletedRecord = row.textContent.substring(row.textContent.indexOf("DSK_"), row.textContent.length);
                    //  $("#hdnDesktopId").val(DesktopIdAfterDeletedRecord);
                    //  }
                    //   }
                }
                $("#pnlError").hide();
                $("#PNL_DeleteMessageBox").hide();

                // $('#DesktopListGrid').data('kendoGrid').dataSource.read();
                window.location.reload(true);
            },
            error: function (data) {
                hideProgress();
            }
        })
    }
    function onBTN_MsgBox2Click() {
        $("#pnlError").hide();
        $("#PNL_DeleteMessageBox").hide();
    }
    function onBTN_MsgBox3Click() {
        $("#pnlError").hide();
        $("#PNL_DeleteMessageBox").hide();
    }
    function CheckDesktopManagerHeader() {
        //var elementPosition = $('#DesktopManagerHeader').offset();
        if ($(window).scrollTop() >= 50) {
            //elementPosition.top
            $('#DesktopManagerHeader').css({
                position: "fixed", top: 60
            });
        } else {
            $('#DesktopManagerHeader').css('position', 'static');
        }
    }


    //Set Responsive Header Height and width..S1
    function SettingHeaderHeightWidth() {
        var SideMenuWidth = $('#sidebar_left').width();
        var winWidth = $(window).width();
        $('#VisibleContentId').width(winWidth - SideMenuWidth);

        var VisibleContentIdHeight = $('#VisibleContentId').height();
        $('#DummyDivForFormContent').height(VisibleContentIdHeight - 5);
    }

    $(window).resize(function () {
        setTimeout(SettingHeaderHeightWidth(), 200);
    });

    //Enable auto input & focus to the Search box..J
    $(document).keydown(function (e) {
        if ($(this)[0].activeElement.nodeName != 'TEXTAREA' && $(this)[0].activeElement.nodeName != 'INPUT') {
            if ($('#SearchDesktop').length > 0) {
                document.getElementById("SearchDesktop").focus();
            }
        }
    });
</script>