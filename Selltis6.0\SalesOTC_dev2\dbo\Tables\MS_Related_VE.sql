﻿CREATE TABLE [dbo].[MS_Related_VE] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Message_Related_Vendor_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_MS] UNIQUEIDENTIFIER NOT NULL,
    [GID_VE] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MS_Related_VE] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_MS_Related_VE] FOREIGN KEY ([GID_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_VE_Connected_MS] FOREIGN KEY ([GID_MS]) REFERENCES [dbo].[MS] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MS_Related_VE] NOCHECK CONSTRAINT [LNK_MS_Related_VE];


GO
ALTER TABLE [dbo].[MS_Related_VE] NOCHECK CONSTRAINT [LNK_VE_Connected_MS];


GO
CREATE CLUSTERED INDEX [IX_VE_Connected_MS]
    ON [dbo].[MS_Related_VE]([GID_MS] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MS_Related_VE]
    ON [dbo].[MS_Related_VE]([GID_VE] ASC);

