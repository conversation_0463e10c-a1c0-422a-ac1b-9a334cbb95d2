﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Selltis.MVC.Models
{
    public class PriorityDetails
    {
        //contact details
        public IList<OPDetails> LinkedOPs { get; set; }
        public int OPCount { get; set; }

        //Linked Web Leads
        public IList<WebLeadDetails> LinkedWLs { get; set; }
        public int WLCount { get; set; }

        //Linked CNs
        public IList<CNDetails> LinkedCNs { get; set; }
        public int CNCount { get; set; }

        //Linked ACs
        public IList<ACDetails> LinkedACs { get; set; }
        public int ACCount { get; set; }

        //Recent COs
        public IList<Company> LinkedCOs { get; set; }
        public int COCount { get; set; }

        public int QTTotalCount { get; set; }

        public int APTotalCount { get; set; }

        public int CNTotalCount { get; set; }


        public double OPTotalAmount { get; set; }

        public int OPActiveCount { get; set; }
        public int OPFollowUpCount { get; set; }

        public string Key { get; set; }

        public int OPCount_Total { get; set; }
        public int OPCount_Creating { get; set; }
        public int OPCount_Proposed { get; set; }
        public int OPCount_Negotiations { get; set; }
        public int OPCount_Hold { get; set; }
        public int OPCount_Won { get; set; }
        public int OPCount_QualifiedProspect { get; set; }
        public int OPCount_PotentialProspect { get; set; }
        public int OPCount_Lost { get; set; }
        public string WLFName { get; set; }

        public int WLCount_Total { get; set; }
        public int WLCount_Assigned { get; set; }
        public int WLCount_Qualified { get; set; }
        public int WLCount_EmailSentToCustomer { get; set; }
        public int WLCount_CallPlacedToCustomer { get; set; }

        //Ap Details
        public IList<APDetails> LinkedAPs { get; set; }
        public int APCount { get; set; }

        public IList<CIRDetails> LinkedCIRs { get; set; }
        public int CIRCount { get; set; }
    }

    public class QTDetails
    {

        public string Key { get; set; }
        public string Gid_id { get; set; }
        public string QTOwner { get; set; }

        public string OPType { get; set; }

        public string QTNumber { get; set; }
        public string OPName { get; set; }
        public string NetAmount { get; set; }

        public string PrimaryQuote { get; set; }

        public string Account { get; set; }

        public string StartDate { get; set; }

        public string ExpiresOn { get; set; }

        public string PrimaryContact { get; set; }

        public string Type { get; set; }
        public string PO { get; set; }

        public string SalesRep { get; set; }

        public string PaymentTerms { get; set; }

        public string RelatedOpportunity { get; set; }

        public string UnitType { get; set; }

        public string CapExRequired { get; set; }

        public string QuotePreparedForContact { get; set; }

        public string PrimaryQuoteNo { get; set; }
        public string PrimaryContactEmail { get; set; }
        public string BillingAdd { get; set; }
        public string BillingAdditionalComments { get; set; }
        public string billingContactName { get; set; }
        public string BillingPhone { get; set; }
        public string BillingEmail { get; set; }
        public string ShippingAgent { get; set; }
        public string ShipFromLocation { get; set; }
        public string FinalDestination { get; set; }
        public string ShippingCarrier { get; set; }
        public string ShippingAdds { get; set; }
        public string ShippingAdditionalComments { get; set; }
        public string ShippingContactName { get; set; }
        public string ShippingPhone { get; set; }
        public string ShippingEmail { get; set; }

        public string LeadTime { get; set; }
        public string RentalRates { get; set; }
        public string ReqShipDate { get; set; }
        public string PlaceOfDelivery { get; set; }
        public string PriceValidity { get; set; }

        public string Introduction { get; set; }
        public string Notes { get; set; }


    }

    public class WebLeadDetails
    {
        public string Key { get; set; }
        public string Gid_id { get; set; }
        public string Name { get; set; }
        public string FName { get; set; }
        public string LName { get; set; }
        public string Stage { get; set; }
        public string TypeOfReq { get; set; }
        public string CompanyName { get; set; }
        public string ProductType { get; set; }
        public string Email { get; set; }
        public string PhoneNo { get; set; }
        public string City { get; set; }
        public string County { get; set; }

        public string IndustryName { get; set; }
        public string COMMENTS { get; set; }
        public string CreationDateTimeInHubSpot { get; set; }
        public string AssignedUser { get; set; }
        public string RelatedComapny { get; set; }
        public string RelatedContact { get; set; }
        public string WLNO { get; set; }

        //Linked OPs
        public IList<OPDetails> LinkedOPs { get; set; }
        public int OPCount { get; set; }

        //Linked ACs
        public IList<ACDetails> LinkedACs { get; set; }
        public int ACCount { get; set; }

        //Linked CNs
        public IList<CNDetails> LinkedCNs { get; set; }
        public int CNCount { get; set; }

        public int ShowQualifyLeadLink { get; set; }

    }

    //public class WLDetails 
    //{
    //   public string FName { get; set; }
    //}

    //PR this for SynapaticDetails 
    //public class SynapaticDetails
    //{
    //    public IList<JODetails> LinkedJOs { get; set; }
    //    public int JOCount { get; set; }

    //    public IList<HODetails> LinkedHOs { get; set; }
    //    public int HOCount { get; set; }

    //    public IList<HSDetails> LinkedHSs { get; set; }
    //    public int HSCount { get; set; }

    //    public IList<JobsBySupervisor> JobsBySupervisor { get; set; }
    //    public IList<JobsByRegion> JobsByRegion { get; set; }
    //    public IList<JobsByStage> JobsByStage { get; set; }

    //    public double JOTotalAmount { get; set; }

    //    public int JOActiveCount { get; set; }


    //    public string Key { get; set; }

    //    public int JOCount_Total { get; set; }
    //    public int JOCount_Proposal { get; set; }
    //    public int JOCount_Design { get; set; }
    //    public int JOCount_Permitting { get; set; }
    //    public int JOCount_Scheduling { get; set; }
    //    public int JOCount_Install { get; set; }
    //    public int JOCount_Testing { get; set; }

    //    public int JOCount_South { get; set; }
    //    public int JOCount_SouthWest { get; set; }
    //    public int JOCount_West { get; set; }
    //    public int JOCount_DFW { get; set; }

    //    public int JOCount_ThisWeek { get; set; }

    //}

    //public class JODetails
    //{
    //    public string Key { get; set; }
    //    public string Gid_id { get; set; }
    //    public string JobName { get; set; }
    //    public string JobStage { get; set; }
    //    public string Utility { get; set; }
    //    public string PVModules { get; set; }
    //    public string Inverter { get; set; }
    //    public string PropertyAddress { get; set; }
    //    public double KWH { get; set; }
    //    public double EstimatedKWH { get; set; }
    //    public double SFT { get; set; }
    //    public string JobNo { get; set; }
    //    public double Estimatedvakue { get; set; }
    //    public string ModuleBrand { get; set; }
    //    public string House { get; set; }
    //    public string HouseOwner { get; set; }
    //    public string ModuleLoaction { get; set; }
    //    public string InverterBrand { get; set; }
    //    public string VendorSource { get; set; }
    //    public string InverterLeadTime { get; set; }
    //    public string Source { get; set; }
    //    public string NoofPVCells { get; set; }
    //    public string PVLeadTime { get; set; }
    //    public string CrewName { get; set; }
    //    public string CrewSupervisor { get; set; }
    //    public string Email { get; set; }
    //    public string Mobile { get; set; }
    //    public DateTime ExpStrDate { get; set; }
    //    public DateTime ExpEndDate { get; set; }
    //    public DateTime ActSrtDate { get; set; }
    //    public DateTime ActEndDate { get; set; }

    //    public HSDetails houseDetails { get; set; }

    //    //DTT_SalesStartDate,DTT_SalesEndDate,TXT_SalesSalesPerson,TXT_SalesPM,Isnull(MMO_SalesNotes ,'') As SalesNotes
    //    public DateTime SalesStartDate { get; set; }
    //    public DateTime SalesEndDate { get; set; }
    //    public string SalesSalesPerson { get; set; }
    //    public string SalesPM { get; set; }
    //    public string SalesNotes { get; set; }


    //    public DateTime SurveyStartDate { get; set; }
    //    public DateTime SurveyEndDate { get; set; }
    //    public string SurveySalesPerson { get; set; }
    //    public string SurveyPM { get; set; }
    //    public string SurveyNotes { get; set; }


    //    public DateTime DesignStartDate { get; set; }
    //    public DateTime DesignEndDate { get; set; }
    //    public string DesignSalesPerson { get; set; }
    //    public string DesignPM { get; set; }
    //    public string DesignNotes { get; set; }


    //    public DateTime InspectionStartDate { get; set; }
    //    public DateTime InspectionEndDate { get; set; }
    //    public string InspectionSalesPerson { get; set; }
    //    public string InspectionPM { get; set; }
    //    public string InspectionNotes { get; set; }


    //    public DateTime PermittingStartDate { get; set; }
    //    public DateTime PermittingEndDate { get; set; }
    //    public string PermittingSalesPerson { get; set; }
    //    public string PermittingPM { get; set; }
    //    public string PermittingNotes { get; set; }


    //}
    //public class HODetails
    //{
    //    public string Gid_id { get; set; }
    //    public string FirstName { get; set; }
    //    public string LastName { get; set; }
    //    public string PrimaryPhoneNumber { get; set; }
    //    public bool Active { get; set; }
    //    public string PrimaryAddress { get; set; }
    //    public string PrimaryCellPhone { get; set; }

    //}
    //public class HSDetails
    //{
    //    public string Gid_id { get; set; }
    //    public string FirstName { get; set; }
    //    public string LastName { get; set; }
    //    public string PropertyAddress { get; set; }
    //    public string UtilityName { get; set; }
    //    public string AccountNumber { get; set; }
    //    public string ESIID { get; set; }
    //    public double SFT { get; set; }
    //    public string HOA { get; set; }
    //    public string Region { get; set; }
    //    public string TelNo { get; set; }
    //}

    //public class JobsBySupervisor
    //{
    //    public string Supervisior { get; set; }
    //    public int JobsCount { get; set; }

    //}

    //public class JobsByRegion
    //{
    //    public string Region { get; set; }
    //    public int JobsCount { get; set; }

    //}

    //public class JobsByStage
    //{
    //    public string Stage { get; set; }
    //    public int JobsCount { get; set; }

    //}

    public class Priority_TaskDetails
    {       
        public string Key { get; set; }

        //public TaskCountByPriority TotalTasks { get; set; }
        public int MyOverDueTasksCount { get; set; }
        public int MyOpenTasksCount { get; set; }
        public int MyTasksDueThisWeekCount { get; set; }
        public int MyCompletedTasksCount { get; set; }

        public int totaltaskcount { get; set; }
        public IList<TaskDetails> LinkedTSs { get; set; }
        public int TSCount { get; set; }

        //Recent COs
        public IList<Company> LinkedCOs { get; set; }
        public int COCount { get; set; }
        //Linked ACs
        public IList<ACDetails> LinkedACs { get; set; }
        public int ACCount { get; set; }

        //cerate remaining

    }

    public class TaskCountByPriority
    {
        public int TaskCount_Total { get; set; }
        public int MyOpenTasksCount { get; set; }
        public int MyTasksDueThisWeekCount { get; set; }
        public int MyCompletedTasksCount { get; set; }
        //public int TaskCount_P3 { get; set; }
        //public int TaskCount_P4 { get; set; }
        //public int TaskCount_P5 { get; set; }
    }

    public class TaskDetails
    {
        public string Description { get; set; }
        public string ServiceTicket { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime DueDate { get; set; }
        public string TaskCategoryName { get; set; }
        public string TaskSubCategoryName { get; set; }
        public string CompanyName { get; set; }
        public string TaskStatus { get; set; }
            public string Requestor { get; set; }
        public string Gid_id { get; set; }

       
    }

    //AP details
    public class APDetails 
    {
        public string Key { get; set; }
        public string Gid_id { get; set; }
        public string Type { get; set; }
        public string Description { get; set; }

        public string StartDate { get; set; }
        public string EndDate { get; set; }

    }
    public class CIRDetails
    {
        public string Gid_id { get; set; }
        public string CIR_No { get; set; }
        public string CustomerReportIncidentContact { get; set; }

        public string Account { get; set; }
        public string HooverCSOrederNo { get; set; }
        public string DateReported { get; set; }
        public string DateOfIncident { get; set; }

        public string AssignedSalesPerson { get; set; }
        public string Urgency { get; set; }
        public string Status { get; set; }
        public string CIRCategory { get; set; }

    }


}