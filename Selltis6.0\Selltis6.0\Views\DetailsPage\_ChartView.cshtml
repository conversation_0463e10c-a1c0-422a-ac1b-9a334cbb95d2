﻿@model Selltis.MVC.Models.ChartProfileView

<div class="col-xl-6 col-xs-12">

    @*<div class="d-flex justify-content-between align-items-center mt-20">
        <div class="d-flex justify-content-start align-items-center">*@
            <div class="mb-20 mt-20"> <h4 class="m-0" title="@Model.FilterToolTip">@Model.Title</h4></div>

            <div  style="width: 100% !important; border: 1px solid #f5f5f6; margin:5px">               
                @(Html.Kendo().Chart()
                    .Name("pfchart_" + Model.ViewId)
                    .HtmlAttributes(new { style = "width:100% !important;" })
                    .Legend(legend => legend.Position(ChartLegendPosition.Bottom))
                    .ChartArea(chartArea => chartArea
                        .Background("transparent")
                    )
                    .Series(series =>
                    {
                        if (Model.ChartType == Selltis.Core.Chart.ChartType.Column)
                        {
                            series.Column(Model.YAxis).Name(Model.SeriesName).Color(Model.Color).Labels(c =>
                            {
                                if (Model.SeriesDataTypeIsCurrency)
                                {
                                    c.Visible(true).Font("9px Arial").Format("$##,#").Margin(-5).Background("none");
                                }
                                else
                                {
                                    c.Visible(true).Template(" #= value #").Font("9px Arial").Margin(-5).Background("none");
                                }
                            });
                        }
                        else if (Model.ChartType == Selltis.Core.Chart.ChartType.Line)
                        {
                            series.Line(Model.YAxis).Name(Model.SeriesName).Color(Model.Color).Labels(c =>
                            {
                                if (Model.SeriesDataTypeIsCurrency)
                                {
                                    c.Visible(true).Font("9px Arial").Format("$##,#").Margin(-5).Background("none");
                                }
                                else
                                {
                                    c.Visible(true).Template(" #= value #").Font("9px Arial").Margin(-5).Background("none");
                                }
                            });
                        }
                        else if (Model.ChartType == Selltis.Core.Chart.ChartType.Bar)
                        {
                            series.Bar(Model.YAxis).Name(Model.SeriesName).Color(Model.Color).Labels(c =>
                            {
                                if (Model.SeriesDataTypeIsCurrency)
                                {
                                    c.Visible(true).Font("9px Arial").Format("$##,#").Margin(-5).Background("none");
                                }
                                else
                                {
                                    c.Visible(true).Template(" #= value #").Font("9px Arial").Margin(-5).Background("none");
                                }
                            });
                        }
                    })

                    .CategoryAxis(axis => axis
                        .Name("series-axis")
                        .Line(line => line.Visible(false))
                        .MajorGridLines(lines => lines.Visible(false))

                    )

                    .CategoryAxis(axis => axis
                        .Name(Model.CategoryName)
                        .Categories(Model.XAxis).Title(Model.CategoryName)
                        .Labels(c =>
                        {
                            c.Visible(true).Font("9px Arial").Background("none");
                        })
                    )
                    .ValueAxis(axis => axis
                        .Numeric()
                        .Labels(c =>
                        {
                            if (Model.SeriesDataTypeIsCurrency)
                            {
                                c.Visible(true).Font("10px Arial").Format("$##,#").Margin(-5).Background("none");
                            }
                            else
                            {
                                c.Visible(true).Template(" #= value #").Font("10px Arial").Margin(-5).Background("none");
                            }
                        })
                        .AxisCrossingValue(0, int.MinValue)
                    )
                    .Tooltip(tooltip =>
                    {
                        if (Model.SeriesDataTypeIsCurrency)
                        {

                            tooltip.Visible(true).Template("#= series.name #: #= kendo.toString(value, 'c0')#");
                        }
                        else
                        {
                            tooltip.Visible(true).Format("{0}").Template("#= series.name #: #= value #");
                        }
                    }
                    )
                  )
            </div>

        @*</div>

    </div>*@

</div>
