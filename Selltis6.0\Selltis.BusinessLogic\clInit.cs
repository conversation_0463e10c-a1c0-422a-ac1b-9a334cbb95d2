﻿using System;

//OWNER: RH
using System.Web;
using System.Configuration;
using System.IO;
using System.Data;

namespace Selltis.BusinessLogic
{
	public class clInit
	{

		//MI 3/13/08 Added goTr.InitClass()

		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clLog goLog;
		private clDefaults goDef;
		private clScrMngRowSet goScr;
		private clNumMask goNumMask;
		// Dim goUI As clUI
		private clHistory goHist;
		private clPerm goPerm;


		public clInit()
		{
			try
			{

				if (HttpContext.Current.Session["USERID"].ToString() == "")
				{

				}
			}
			catch (Exception ex)
			{
				HttpContext.Current.Response.Redirect("../pages/redirect.aspx", true);
			}


			InitializeSession();


		}

		private bool InitializeSession()
		{
			//MI 11/1/07 Added testing session("Connection") is NULL - was raising exception if NULL

			try
			{
				string sHostName = clSettings.GetHostName();
				if (HttpContext.Current.Session[sHostName + "_SiteSettings"] == null)
				{
					clSettings.LoadSiteSettings();
				}

				if (HttpContext.Current.Session["Connection"] == null)
				{
					return Initialize();
				}
				else
				{
					if (HttpContext.Current.Session["Connection"].ToString() == "")
					{
						return Initialize();
					}
					else
					{
						return true;
					}
				}
			}
			catch (Exception ex)
			{
				return Initialize();
			}

		}

		private bool Initialize()
		{
				bool tempInitialize = false;
			//MI 11/9/09 Moved NumMask, goTr and time zone initialization above loading lists.
			//MI 1/14/09 Added goP.LoadProductList()
			string sProc = "clInit::Initialize";


			try
			{
				HttpContext.Current.Session["goP"] = new clProject();
				HttpContext.Current.Session["goTr"] = new clTransform();
				HttpContext.Current.Session["goMeta"] = new clMetaData();
				HttpContext.Current.Session["goData"] = new clData();
				HttpContext.Current.Session["goErr"] = new clError();
				HttpContext.Current.Session["goLog"] = new clLog();
				HttpContext.Current.Session["goDef"] = new clDefaults();
				HttpContext.Current.Session["goScr"] = new clScrMngRowSet();
				HttpContext.Current.Session["goNumMask"] = new clNumMask();
				//HttpContext.Current.Session("goUI") = New clUI
				HttpContext.Current.Session["goHist"] = new clHistory();
				HttpContext.Current.Session["goPerm"] = new clPerm();

				//HttpContext.Current.Session("Connection") = ConfigurationManager.ConnectionStrings(sConnectionString).ConnectionString ''ConfigurationManager.ConnectionStrings("SelltisConnectionString").ConnectionString

				HttpContext.Current.Session["Connection"] = ((DataTable)HttpContext.Current.Session[clSettings.GetHostName() + "_SiteSettings"]).Rows[0]["ConnectionString"];


				goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
				goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
				goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
				goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
				goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
				goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
				goDef = (Selltis.BusinessLogic.clDefaults)HttpContext.Current.Session["goDef"];
				goScr = (Selltis.BusinessLogic.clScrMngRowSet)HttpContext.Current.Session["goScr"];
				goNumMask = (Selltis.BusinessLogic.clNumMask)HttpContext.Current.Session["goNumMask"];
				//goUI = HttpContext.Current.Session("goUI")
				goHist = (Selltis.BusinessLogic.clHistory)HttpContext.Current.Session["goHist"];
				goPerm = (Selltis.BusinessLogic.clPerm)HttpContext.Current.Session["goPerm"];

				goP.Initialize();
				goTR.Initialize();
				goMeta.Initialize();
				goData.Initialize();
				goErr.Initialize();
				goLog.Initialize();
				goDef.Initialize();
				goScr.Initialize();
				goNumMask.Initialize();
				//goUI.Initialize()
				goHist.Initialize();
				goPerm.Initialize();

				goP.InitClass();

				goLog.SetLogLevel(Convert.ToInt16(goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "LOGLEVEL", "0", true)));
				goP.SellTraceMode(Convert.ToInt64(goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "TRACELEVEL", clC.SELLTRACE_USER_AND_EMPTY.ToString(), true)));

				//MI 11/9/09 Moved here from below the list loading block
				//------- Initialization code from W_MAIN in NGP -------
				goNumMask.InitClass();
				goTR.InitClass(); //*** MI added 3/13/08
				goTR.InitDateTime();
				goTR.UTC_InitTimeZones(); //*** MI added 9/11/07
				//Initialize time zone
				goP.InitProject2(); //*** MI added 9/13/07

				//------------- Load lists -------------
				//Load product list
				goP.LoadProductList();
				//Load schema datatables
				goData.LoadSchemaData();

				goP.VerifyDatabaseID();

				tempInitialize = true;
				//HttpContext.Current.Session.Timeout = 30



			}
			catch (Exception ex)
			{
				//Error initializing the project.
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If

				tempInitialize = false;
			}

			return tempInitialize;
		}

		public bool ReInitialize()
		{

			return Initialize();




		}


	}

}
