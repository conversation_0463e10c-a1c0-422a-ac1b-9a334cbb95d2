﻿CREATE TABLE [dbo].[MC_RELATED_GR] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_MC_RELATED_GR_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [<PERSON><PERSON>_<PERSON>] UNIQUEIDENTIFIER NOT NULL,
    [GID_GR] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MC_RELATED_GR] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_GR_CONNECTED_MC] FOREIGN KEY ([G<PERSON>_<PERSON>]) REFERENCES [dbo].[MC] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_MC_RELATED_GR] FOREIGN KEY ([GID_GR]) REFERENCES [dbo].[GR] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MC_RELATED_GR] NOCHECK CONSTRAINT [LNK_GR_CONNECTED_MC];


GO
ALTER TABLE [dbo].[MC_RELATED_GR] NOCHECK CONSTRAINT [LNK_MC_RELATED_GR];


GO
CREATE NONCLUSTERED INDEX [IX_MC_RELATED_GR]
    ON [dbo].[MC_RELATED_GR]([GID_MC] ASC, [GID_GR] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_GR_CONNECTED_MC]
    ON [dbo].[MC_RELATED_GR]([GID_GR] ASC, [GID_MC] ASC);

