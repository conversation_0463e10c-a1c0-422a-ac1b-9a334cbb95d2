﻿CREATE TABLE [dbo].[MJ] (
    [GID_ID]                 UNIQUEIDENTIFIER CONSTRAINT [DF_MJ_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'MJ',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                 BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]               NVARCHAR (80)    NULL,
    [DTT_CreationTime]       DATETIME         CONSTRAINT [DF_MJ_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]           TINYINT          NULL,
    [TXT_ModBy]              VARCHAR (4)      NULL,
    [DTT_ModTime]            DATETIME         CONSTRAINT [DF_MJ_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_MarketingJobName]   NVARCHAR (50)    NULL,
    [MMO_ImportData]         NTEXT            NULL,
    [SI__ShareState]         TINYINT          CONSTRAINT [DF_MJ_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]       UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]           VARCHAR (50)     NULL,
    [TXT_ExternalID]         NVARCHAR (80)    NULL,
    [TXT_ExternalSource]     VARCHAR (10)     NULL,
    [TXT_ImpJobID]           VARCHAR (20)     NULL,
    [CHK_3ContactsValidated] TINYINT          NULL,
    [BI__ContactCount]       BIGINT           NULL,
    [MLS_List]               SMALLINT         NULL,
    CONSTRAINT [PK_MJ] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_MJ_MarketingJobName]
    ON [dbo].[MJ]([TXT_MarketingJobName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MJ_CreatedBy_US]
    ON [dbo].[MJ]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MJ_ModDateTime]
    ON [dbo].[MJ]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MJ_Name]
    ON [dbo].[MJ]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MJ_CreationTime]
    ON [dbo].[MJ]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_MJ_BI__ID]
    ON [dbo].[MJ]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MJ_TXT_ImportID]
    ON [dbo].[MJ]([TXT_ImportID] ASC);

