﻿CREATE TABLE [dbo].[PS_Related_CO] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_PS_Related_CO_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PS] UNIQUEIDENTIFIER NOT NULL,
    [GID_CO] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PS_Related_CO] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CO_Connected_PS] FOREIGN KEY ([GID_PS]) REFERENCES [dbo].[PS] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PS_Related_CO] FOREIGN KEY ([GID_CO]) REFERENCES [dbo].[CO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PS_Related_CO] NOCHECK CONSTRAINT [LNK_CO_Connected_PS];


GO
ALTER TABLE [dbo].[PS_Related_CO] NOCHECK CONSTRAINT [LNK_PS_Related_CO];


GO
CREATE NONCLUSTERED INDEX [IX_CO_Connected_PS]
    ON [dbo].[PS_Related_CO]([GID_CO] ASC, [GID_PS] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PS_Related_CO]
    ON [dbo].[PS_Related_CO]([GID_PS] ASC, [GID_CO] ASC);

