﻿CREATE TABLE [dbo].[OP_RELATED_MO] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_OP_RELATED_MO_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    [GID_MO] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_OP_RELATED_MO] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_MO_CONNECTED_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_RELATED_MO] FOREIGN KEY ([GID_MO]) REFERENCES [dbo].[MO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[OP_RELATED_MO] NOCHECK CONSTRAINT [LNK_MO_CONNECTED_OP];


GO
ALTER TABLE [dbo].[OP_RELATED_MO] NOCHECK CONSTRAINT [LNK_OP_RELATED_MO];


GO
CREATE NONCLUSTERED INDEX [IX_MO_CONNECTED_OP]
    ON [dbo].[OP_RELATED_MO]([GID_MO] ASC, [GID_OP] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_RELATED_MO]
    ON [dbo].[OP_RELATED_MO]([GID_OP] ASC, [GID_MO] ASC);

