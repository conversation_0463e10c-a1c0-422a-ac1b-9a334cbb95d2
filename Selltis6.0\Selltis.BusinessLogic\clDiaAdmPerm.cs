﻿using System;
using System.Data;

//OWNER: MI

namespace Selltis.BusinessLogic
{
	public class clDiaAdmPerm
	{
		//Inherits clVar

		//MI 3/12/13 Added sLogonPass.
		//MI 8/25/09 Enabled login groups.
		//MI 3/15/07 Created.

		//Enable global objects only if needed
		//Private goP As clProject
		//Private goMeta As clMetaData
		//Private goTR As clTransform
		//Private goData As clData
		//Private goErr As clError
		//Private goLog As clLog
		//Private goUI As clUI
		//Private goHist As clHistory

		public string par_sMode; //Page opening mode
		public string sReturnURL = "";
		public bool bBackFromSubdialog;
		public string sMessageBoxPurpose;

		//Parent dialog object
		//==> Replace or remove
		//Public oView As cldiaViewPro
		public string sTitle = "User/Group Permissions";
		public string fenenexecution = "diaAdmPerm.aspx";

		public string sVals = ""; //Main work string
		public int iCurrentPlane; //number of the current plane (Multiview view)
		public string sMode = ""; //Window opening mode in uppercase
		public string sResult = ""; //Result string returned by the window
		public string sFileName = ""; //Main file
		public string sRoles = ""; //Roles permission page
		public string sFeatures = ""; //Feature permission page
		public string sAccess = ""; //Selective access permission page
		public string sGlobalAccess = ""; //GLOBAL (default) selective access permissions
		//Public sGroupAccess As String = ""  'Access permissions of the selected Login's Login Group
		public string sGlobalFeatures = ""; //GLOBAL (default) features
		public string sLastLogin = ""; //Last selected user login
		public string sLastGroup = ""; //Last selected user login group
		public string sAccessFilterTypeEdited; //R', 'E', or 'D'
		public string sAccessFilter;
		public string sAccessCondSummary;
		public string sDefaultCond = ""; //Default condition statement
		public string sResultFromDiaFiltPro;
		public string sNeverLabel; //Label for '(never)'

		//Table declarations
		public DataTable dtLogins = new DataTable();
		public clTable tLogins = new clTable();
		public DataTable dtAccess = new DataTable();
		public clTable tAccess = new clTable();


		public void Initialize()
		{
			string sProc = "clDiaAdmPerm::Initialize";
			//Try
			//   'Enable only if needed
			//   goP = HttpContext.Current.Session("goP")
			//   goTR = HttpContext.Current.Session("goTr")
			//   goMeta = HttpContext.Current.Session("goMeta")
			//   goData = HttpContext.Current.Session("goData")
			//   goErr = HttpContext.Current.Session("goErr")
			//   goLog = HttpContext.Current.Session("goLog")
			//   goUI = HttpContext.Current.Session("goUI")
			//   goHist = HttpContext.Current.Session("goHist")
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public clDiaAdmPerm()
		{

		}

		~clDiaAdmPerm()
		{
// INSTANT C# NOTE: The base class Finalize method is automatically called from the destructor:
			//base.Finalize();
		}
	}

}
