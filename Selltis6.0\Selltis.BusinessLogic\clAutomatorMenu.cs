﻿using System;
using System.Data;


//Author: WT

using System.Web;

namespace Selltis.BusinessLogic
{
	public class clAutomatorMenu
	{

		private clProject goP;
		private clError goErr;
		private clData goData;
		private clTransform goTR;

		private void Initialize()
		{
			string sProc = "clAutomatorMenu:Initialize";
			// Try
			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
				goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
				goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
				goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public clAutomatorMenu()
		{
			string sProc = "clAutomatorMenu::New";
			// Try
			Initialize();
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		//Public Function GetMenu() As MenuItem

		//    Dim sProc As String = "clAutomatorMenu::GetMenu"
		//    Try

		//        Dim oMenuItem As MenuItem
		//        Dim oSubMenuItem As MenuItem
		//        Dim iResult As Integer = 0

		//        '*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
		//        Dim oConnection As SqlClient.SqlConnection = goData.GetConnection
		//        Dim oCommand As New SqlClient.SqlCommand
		//        Dim oReader As SqlClient.SqlDataReader

		//        oMenuItem = New MenuItem("Tasks")
		//        'oMenuItem.Selectable = False
		//        oMenuItem.NavigateUrl = "javascript:returnFalse();"
		//        oMenuItem.SeparatorImageUrl = "../Images/Blank16.gif"

		//        oCommand.CommandText = "pGetActiveMenuAutomators"
		//        oCommand.CommandType = CommandType.StoredProcedure
		//        oCommand.Connection = oConnection

		//        'return parameter
		//        Dim retValParam As New SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int)
		//        retValParam.Direction = ParameterDirection.ReturnValue
		//        oCommand.Parameters.Add(retValParam)

		//        'execute            
		//        oReader = oCommand.ExecuteReader()

		//        'read returned value
		//        If oReader.HasRows Then
		//            Do While oReader.Read()
		//                Dim sSection As String = Trim(oReader("Section").ToString)
		//                Dim sPage As String = Trim(oReader("Page").ToString)
		//                Dim sName As String = Trim(oReader("Name").ToString)
		//                Dim sIcon As String = Trim(oReader("Icon").ToString)
		//                Dim sTooltip As String = Trim(oReader("Tooltip").ToString)
		//                Dim sType As String = Trim(oReader("A_01_TYPE").ToString)
		//                Dim sExecute As String = Trim(oReader("A_01_EXECUTE").ToString)

		//                If sIcon = "" Then sIcon = "blank16.gif"

		//                Select Case sType
		//                    Case clC.SELL_AGE_RUNSCRIPT
		//                        oSubMenuItem = New MenuItem()
		//                        'oSubMenuItem.ImageUrl = "../Images/" & sIcon
		//                        oSubMenuItem.Text = sTooltip
		//                        oSubMenuItem.NavigateUrl = "javascript:RefreshContent('SiteRunScript','RunScript.aspx?scr=" & sExecute & "');"
		//                        oMenuItem.ChildItems.Add(oSubMenuItem)

		//                    Case clC.SELL_AGE_OPENRECORD
		//                        Dim sArgs As String = "FORM||" & goTR.GetFileFromSUID(sExecute) & "||" & sExecute & "|"
		//                        oSubMenuItem = New MenuItem()
		//                        'oSubMenuItem.ImageUrl = "../Images/" & sIcon
		//                        oSubMenuItem.Text = sTooltip
		//                        oSubMenuItem.NavigateUrl = "javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"
		//                        oMenuItem.ChildItems.Add(oSubMenuItem)

		//                    Case clC.SELL_AGE_OPENDESKTOP
		//                        Dim sArgs As String = "DESKTOP||" & goP.GetUserTID & "|" & sExecute
		//                        oSubMenuItem = New MenuItem()
		//                        'oSubMenuItem.ImageUrl = "../Images/" & sIcon
		//                        oSubMenuItem.Text = sTooltip
		//                        oSubMenuItem.NavigateUrl = "javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"
		//                        oMenuItem.ChildItems.Add(oSubMenuItem)

		//                    Case clC.SELL_AGE_OPENURL
		//                        Dim sArgs As String = "URL|" & sExecute
		//                        oSubMenuItem = New MenuItem()
		//                        'oSubMenuItem.ImageUrl = "../Images/" & sIcon
		//                        oSubMenuItem.Text = sTooltip
		//                        oSubMenuItem.NavigateUrl = "javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"
		//                        oMenuItem.ChildItems.Add(oSubMenuItem)

		//                    Case clC.SELL_AGE_OPENURLEXTERNAL
		//                        Dim sArgs As String = "URL|" & sExecute
		//                        oSubMenuItem = New MenuItem()
		//                        'oSubMenuItem.ImageUrl = "../Images/" & sIcon
		//                        oSubMenuItem.Text = sTooltip
		//                        oSubMenuItem.NavigateUrl = "javascript:window.open('" & sExecute & "','SelltisURL', '');"
		//                        oMenuItem.ChildItems.Add(oSubMenuItem)

		//                    Case clC.SELL_AGE_OPENNDBFORM
		//                        Dim sArgs As String = "NDBFORM|" & sExecute
		//                        oSubMenuItem = New MenuItem()
		//                        oSubMenuItem.Text = sTooltip
		//                        oSubMenuItem.NavigateUrl = "javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"
		//                        oMenuItem.ChildItems.Add(oSubMenuItem)

		//                    Case clC.SELL_AGE_OPENCVS
		//                        Dim sArgs As String = "CVS|" & sExecute
		//                        oSubMenuItem = New MenuItem()
		//                        oSubMenuItem.Text = sTooltip
		//                        oSubMenuItem.NavigateUrl = "javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"
		//                        oMenuItem.ChildItems.Add(oSubMenuItem)


		//                    Case clC.SELL_AGE_OPENFRF
		//                        Dim sArgs As String = "FRF|" & sExecute
		//                        oSubMenuItem = New MenuItem()
		//                        oSubMenuItem.Text = sTooltip
		//                        oSubMenuItem.NavigateUrl = "javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"
		//                        oMenuItem.ChildItems.Add(oSubMenuItem)


		//                    Case Else
		//                        oSubMenuItem = New MenuItem()
		//                        'oSubMenuItem.ImageUrl = "../Images/" & sIcon
		//                        oSubMenuItem.Text = sTooltip
		//                        oSubMenuItem.NavigateUrl = "javascript:return false;"
		//                        oMenuItem.ChildItems.Add(oSubMenuItem)

		//                End Select

		//            Loop

		//        Else
		//            oMenuItem.Enabled = False
		//        End If

		//        oReader.Close()
		//        oConnection.Close()

		//        If iResult = 0 Then
		//            Return oMenuItem
		//        Else
		//            '==> error
		//            Return New MenuItem
		//        End If

		//    Catch ex As Exception
		//        If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
		//            goErr.SetError(ex, 45105, sProc)
		//        End If
		//        Return New MenuItem
		//    End Try

		//End Function

		public string GetCSSMenu() //MenuItem
		{

			string sProc = "clAutomatorMenu::GetCSSMenu";
			try
			{

				//*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
				System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;

				oCommand.CommandText = "pGetActiveMenuAutomators";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				string sHTML = "";

				//return parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				//execute            
				oReader = oCommand.ExecuteReader();

				if (oReader.HasRows)
				{

					//sHTML = sHTML & "<li>"
					//sHTML = sHTML & "<a class=""parent"">Tasks</a>"
					sHTML = sHTML + "<ul>";
					//sHTML = sHTML & "<li class=""arrow""></li>"

					while (oReader.Read())
					{
						string sSection = oReader["Section"].ToString().Trim(' ');
						string sPage = oReader["Page"].ToString().Trim(' ');
						string sName = oReader["Name"].ToString().Trim(' ');
						string sIcon = oReader["Icon"].ToString().Trim(' ');
						string sTooltip = oReader["Tooltip"].ToString().Trim(' ');
						string sType = oReader["A_01_TYPE"].ToString().Trim(' ');
						string sExecute = oReader["A_01_EXECUTE"].ToString().Trim(' ');
						string sLabel = "";
						if (sTooltip.Length > 60)
						{
							sLabel = sTooltip.Substring(0, 60) + "...";
						}
						else
						{
							sLabel = sTooltip;
						}

						if (sIcon == "")
						{
							sIcon = "blank16.gif";
						}

						switch (sType)
						{
							case clC.SELL_AGE_RUNSCRIPT:
							{
								sHTML = sHTML + "<li style=\"cursor:pointer;\" title='" + sTooltip + "' onclick=\"javascript:TasksContent('" + sExecute + "','" + sType + "');\">" + sLabel + "</li>";
								break;

								//sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:TasksContent('" + sExecute + "','" + sType + "');"">" & sLabel & "</a></li>"
								//sHTML = sHTML & "<li><a onclick=""javascript:RefreshContent('SiteRunScript','RunScript.aspx?scr=" & sExecute & "');"">" & sTooltip & "</a></li>"

							}
							case clC.SELL_AGE_OPENRECORD:
							{
								string sArgs = "FORM|" + goTR.GetFileFromSUID(sExecute) + "|" + sExecute;
								sHTML = sHTML + "<li style=\"cursor:pointer;\" title='" + sTooltip + "' onclick=\"javascript:TasksContent('" + sArgs + "','" + sType + "');\">" + sLabel + "</li>";
								break;

								//sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</a></li>"
								//sHTML = sHTML & "<li><a onclick=""javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"">" & sTooltip & "</a></li>"

							}
							case clC.SELL_AGE_OPENDESKTOP:
							{
								string sArgs = "DESKTOP|" + goP.GetUserTID() + "|" + sExecute;
								sHTML = sHTML + "<li style=\"cursor:pointer;\" title='" + sTooltip + "' onclick=\"javascript:TasksContent('" + sArgs + "','" + sType + "');\">" + sLabel + "</li>";
								break;
								//sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</a></li>"
								//sHTML = sHTML & "<li><a onclick=""javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"">" & sTooltip & "</a></li>"

							}
							case clC.SELL_AGE_OPENURL:
							{
								string sArgs = "URL|" + sExecute;
								sHTML = sHTML + "<li style=\"cursor:pointer;\" title='" + sTooltip + "' onclick=\"javascript:TasksContent('" + sArgs + "','" + sType + "');\">" + sLabel + "</li>";
								break;
								//sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</a></li>"
								//sHTML = sHTML & "<li><a onclick=""javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"">" & sTooltip & "</a></li>"

							}
							case clC.SELL_AGE_OPENURLEXTERNAL:
							{
								string sArgs = "URL|" + sExecute;
								sHTML = sHTML + "<li style=\"cursor:pointer;\" title='" + sTooltip + "' onclick=\"javascript:window.open('" + sExecute + "','SelltisURL', '');\">" + sLabel + "</li>";
								break;
								//sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:window.open('" & sExecute & "','SelltisURL', '');"">" & sLabel & "</a></li>"

							}
							case clC.SELL_AGE_OPENNDBFORM:
							{
								string sArgs = "NDBFORM|" + sExecute;
								sHTML = sHTML + "<li style=\"cursor:pointer;\" title='" + sTooltip + "' onclick=\"javascript:TasksContent('" + sArgs + "','" + sType + "');\">" + sLabel + "</li>";
								break;
								//sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</a></li>"
								//sHTML = sHTML & "<li><a onclick=""javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"">" & sTooltip & "</a></li>"

							}
							case clC.SELL_AGE_OPENCVS:
							{
								string sArgs = "CVS|" + sExecute;
								sHTML = sHTML + "<li style=\"cursor:pointer;\" title='" + sTooltip + "' onclick=\"javascript:TasksContent('" + sArgs + "','" + sType + "');\">" + sLabel + "</li>";
								break;
								//sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</a></li>"
								//sHTML = sHTML & "<li><a onclick=""javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"">" & sTooltip & "</a></li>"

							}
							case clC.SELL_AGE_OPENFRF:
							{
								string sArgs = "FRF|" + sExecute;
								sHTML = sHTML + "<li style=\"cursor:pointer;\" title='" + sTooltip + "' onclick=\"javascript:TasksContent('" + sArgs + "','" + sType + "');\">" + sLabel + "</li>";
								break;
								//sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:TasksContent('" & sArgs & "','" + sType + "');"">" & sLabel & "</a></li>"
								//sHTML = sHTML & "<li><a onclick=""javascript:RefreshContent('SiteWorkarea','" & sArgs & "');"">" & sTooltip & "</a></li>"

							}
							default:
							{
								sHTML = sHTML + "<li style=\"cursor:pointer;\" title='" + sTooltip + "' onclick=\"javascript:return false;\">" + sLabel + "</li>";
								break;
								//sHTML = sHTML & "<li style=""cursor:pointer;""><a title='" & sTooltip & "' onclick=""javascript:return false;"">" & sLabel & "</a></li>"

							}
						}

					}

					//'sHTML = sHTML & "</ul></li>"
					sHTML = sHTML + "</ul>";

				}
				else
				{
					//'sHTML = sHTML & "<li><a class=""disabled"">Tasks</a></li>"
					sHTML = sHTML + "</ul>";

				}

				oReader.Close();
				oConnection.Close();

				return sHTML;

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return "";
			}

		}

	}

}
