﻿CREATE TABLE [dbo].[TD_INVOLVES_US] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_TD_INVOLVES_US_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [G<PERSON>_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_INVOLVES_US] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_TD_INVOLVES_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_RELATED_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_INVOLVES_US] NOCHECK CONSTRAINT [LNK_TD_INVOLVES_US];


GO
ALTER TABLE [dbo].[TD_INVOLVES_US] NOCHECK CONSTRAINT [LNK_US_RELATED_TD];


GO
CREATE NONCLUSTERED INDEX [IX_TD_INVOLVES_US]
    ON [dbo].[TD_INVOLVES_US]([GID_TD] ASC, [GID_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_US_RELATED_TD]
    ON [dbo].[TD_INVOLVES_US]([GID_US] ASC, [GID_TD] ASC);

