﻿CREATE TABLE [dbo].[OP_Involves_CO] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Opp_Involves_Company_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    [GID_CO] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_OP_Involves_CO] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CO_InvolvedIn_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_Involves_CO] FOREIGN KEY ([GID_CO]) REFERENCES [dbo].[CO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[OP_Involves_CO] NOCHECK CONSTRAINT [LNK_CO_InvolvedIn_OP];


GO
ALTER TABLE [dbo].[OP_Involves_CO] NOCHECK CONSTRAINT [LNK_OP_Involves_CO];


GO
CREATE CLUSTERED INDEX [IX_CO_InvolvedIn_OP]
    ON [dbo].[OP_Involves_CO]([GID_OP] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_Involves_CO]
    ON [dbo].[OP_Involves_CO]([GID_CO] ASC);

