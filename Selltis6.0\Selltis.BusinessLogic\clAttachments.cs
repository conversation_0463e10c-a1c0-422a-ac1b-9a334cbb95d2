﻿using System;

//V_T 6/8/2015
//Manage all attachments functions
using System.IO;
using System.Web;
using System.Text;
using Microsoft.WindowsAzure.Storage.File;
using System.Data;
using System.Collections;
using System.Collections.Generic;

namespace Selltis.BusinessLogic
{
	public class clAttachments
	{

		private clMetaData goMeta;
		private clProject goP;
		private clError goErr;

		public clAttachments()
		{
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
		}

		public string GetAttachmentsSource()
		{
			string sAttachmentSource = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "ATTACHMENTS_SOURCE", "", true);
			return sAttachmentSource;
		}

		public string[] GetAllowedFileExtensions()
		{
			string sExtensions = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "SELLTIS_SERVER_ALLOWED_FILE_EXTENSIONS", ".txt,.doc,.docx,.xls,.xlsx,.csv,.pdf,.jpg,.jpeg,.png,.bmp,.gif,.ppt,.pptx,.log,.html", true);
			return sExtensions.Split(',');
		}

		public string GetRootPath()
		{
			// Return System.Configuration.ConfigurationManager.AppSettings("AttachmentsPath").ToString()
			return Convert.ToString(((DataTable)HttpContext.Current.Session[clSettings.GetHostName() + "_SiteSettings"]).Rows[0]["AttachmentsPath"]);
		}

		public string GetRootTempPath()
		{
			// Return System.Configuration.ConfigurationManager.AppSettings("AttachmentsTempPath").ToString()
			return Convert.ToString(((DataTable)HttpContext.Current.Session[clSettings.GetHostName() + "_SiteSettings"]).Rows[0]["AttachmentsTempPath"]);
		}

		public long GetCurrentAttachmentsFolderSize(string ViewName)
		{
			var rs = new clRowSet("AD", clC.SELL_GROUPBY, "TXT_FILENAME='" + ViewName + "'", "TXT_FILENAME", "DR__ATTACHMENT|SUM", -1, "", "", "", "", "", true);
			rs.ToTable();

			DataTable dt = rs.dtTransTable;
			long CurrentFolderSize = 0;
			if (dt != null && dt.Rows.Count > 0)
			{
				long.TryParse(dt.Rows[0][0].ToString(), out CurrentFolderSize);
			}
			return CurrentFolderSize;
		}

		public string ADR_to_Link(string par_ADR_Value, string par_File_GID_ID, string par_sField_Name, string par_sViewName)
		{
			//V_T Created 5/6
			//Returns the friendly value of the ADR fields

			string sProc = "clAttachments::ADR_to_Link";
			try
			{
				if (string.IsNullOrEmpty(par_File_GID_ID))
				{
					return string.Empty;
				}

				string ADRString = string.Empty;

				DataTable dt = GetAttachments(par_sField_Name, par_File_GID_ID);

				if (dt != null && dt.Rows.Count > 0)
				{

					for (var index = 0; index < dt.Rows.Count; index++)
					{
						//TXT_Source,TXT_ObjectId

						string sAttachmentSource = "Selltis";
						string sGidId = string.Empty;
						string sAttachmentName = string.Empty;

						if (!Convert.IsDBNull(dt.Rows[index]["TXT_Source"]))
						{
							sAttachmentSource = dt.Rows[index]["TXT_Source"].ToString();
						}
						if (!Convert.IsDBNull(dt.Rows[index]["TXT_AttachmentName"]))
						{
							sAttachmentName = dt.Rows[index]["TXT_AttachmentName"].ToString();
						}
						if (!Convert.IsDBNull(dt.Rows[index]["GID_ID"]))
						{
							sGidId = dt.Rows[index]["GID_ID"].ToString();
						}

						if (sAttachmentSource == "EFileCabinet")
						{
							//need ObjectId to download the file from efile cabinet
							if (!Convert.IsDBNull(dt.Rows[index]["TXT_ATTACHMENTDIRECTORYPATH"]))
							{
								sGidId = dt.Rows[index]["TXT_ATTACHMENTDIRECTORYPATH"].ToString();
							}
						}

						string AttachmentsStorageType = System.Configuration.ConfigurationManager.AppSettings["AttachmentsStorageType"].ToString();

						if (AttachmentsStorageType == "Server")
						{
							if (string.IsNullOrEmpty(ADRString))
							{
								ADRString = GetFileDownloadURL("dType=1&sGidid=" + sGidId + "&sSource=" + sAttachmentSource + "&sFileName=" + sAttachmentName + "", sAttachmentName);
							}
							else
							{
								ADRString = ADRString + "<BR>" + GetFileDownloadURL("dType=1&sGidid=" + sGidId + "&sSource=" + sAttachmentSource + "&sFileName=" + sAttachmentName + "", sAttachmentName);
							}
						}
						else
						{
							if (string.IsNullOrEmpty(ADRString))
							{
								ADRString = GetFileDownloadURLAzure("sFileName=" + sAttachmentName + "&sViewName=" + par_sViewName + "&sGidId=" + par_File_GID_ID + "&sFieldName=" + par_sField_Name + "  ", sAttachmentName);
							}
							else
							{
								ADRString = ADRString + "<BR>" + GetFileDownloadURLAzure("sFileName=" + sAttachmentName + "&sViewName=" + par_sViewName + "&sGidId=" + par_File_GID_ID + "&sFieldName=" + par_sField_Name + "  ", sAttachmentName);
							}
						}

						//If sAttachmentSource = "Selltis" Then
						//    If String.IsNullOrEmpty(ADRString) Then
						//        ADRString = GetFileDownloadURL("Gidid=" & dt.Rows(index)("GID_ID").ToString() & "&sSource=Selltis", dt.Rows(index)("TXT_AttachmentName").ToString())
						//    Else
						//        ADRString = ADRString & "<BR>" & GetFileDownloadURL("Gidid=" & dt.Rows(index)("GID_ID").ToString() & "&sSource=Selltis", dt.Rows(index)("TXT_AttachmentName").ToString())
						//    End If
						//ElseIf sAttachmentSource = "EFileCabinet" Then
						//    If String.IsNullOrEmpty(ADRString) Then
						//        ADRString = GetFileDownloadURL("sfilename=" & dt.Rows(index)("TXT_AttachmentName").ToString() & "&objectid=" & dt.Rows(index)("TXT_ObjectId").ToString() & "&sSource=EFileCabinet", dt.Rows(index)("TXT_AttachmentName").ToString())
						//    Else
						//        ADRString = ADRString & "<BR>" & GetFileDownloadURL("sfilename=" & dt.Rows(index)("TXT_AttachmentName").ToString() & "&objectid=" & dt.Rows(index)("TXT_ObjectId").ToString() & "&sSource=EFileCabinet", dt.Rows(index)("TXT_AttachmentName").ToString())
						//    End If
						//End If

					}

				}

				return ADRString;

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, , sProc)
				//End If
				return "";
			}

		}
		public System.IO.MemoryStream DownloadFileToStream(string sViewName, string sGidId, string sFieldName, string sTempId, bool isTempPath, string FileName)
		{
			CloudFileDirectory FinalFolder = null;
			if (isTempPath)
			{
				FinalFolder = clAzureFileStorage.GetFinalFolder("", "", "", sTempId, true);
			}
			else
			{
				FinalFolder = clAzureFileStorage.GetFinalFolder(sViewName, sGidId, sFieldName, "", false);
			}
			CloudFile sFile = FinalFolder.GetFileReference(FileName);
			System.IO.MemoryStream tStream = new System.IO.MemoryStream();
			sFile.DownloadToStream(tStream);
			return tStream;

		}
		public byte[] GetFile(string Gid_Id, string sSource, string sFilePath = "")
		{

			Guid NewGuid = new Guid();

			if (!string.IsNullOrEmpty(Gid_Id) && Guid.TryParse(Gid_Id, out NewGuid))
			{


				//Dim dt As DataTable = GetAD_DataTable("GID_ID='" + Gid_Id + "' ", "TXT_ATTACHMENTDIRECTORYPATH,TXT_Source")
				string AttachmentsStorageType = System.Configuration.ConfigurationManager.AppSettings["AttachmentsStorageType"].ToString();
				DataTable dt = null;

				if (AttachmentsStorageType == "Server")
				{
					dt = GetAD_DataTable("GID_ID='" + Gid_Id + "' ", "TXT_ATTACHMENTDIRECTORYPATH,TXT_Source");
				}
				else
				{
					dt = GetAD_DataTable("GID_ID='" + Gid_Id + "' ", "TXT_AttachmentName,TXT_FILENAME,TXT_FILEGIDID,TXT_FieldName,TXT_ATTACHMENTDIRECTORYPATH,TXT_Source");
				}


				if (dt != null && dt.Rows.Count > 0)
				{

					//Dim sSource As String = "Selltis"
					if (!Convert.IsDBNull(dt.Rows[0]["TXT_Source"]))
					{
						sSource = dt.Rows[0]["TXT_Source"].ToString();
					}

					if (sSource == "Selltis")
					{

						if (AttachmentsStorageType == "Server")
						{
							string filename = dt.Rows[0]["TXT_ATTACHMENTDIRECTORYPATH"].ToString(); //GetFullFilePathByGID_ID(Gid_Id)
							string rootpath = GetRootPath(); //System.Configuration.ConfigurationManager.AppSettings("AttachmentsPath").ToString()
							filename = rootpath + filename;
							if (File.Exists(filename))
							{
								return GetServerFile(filename);
							}
							else
							{
								return null;
							}
						}
						else
						{
							string filename = dt.Rows[0]["TXT_AttachmentName"].ToString();
							string viewname = dt.Rows[0]["TXT_FILENAME"].ToString();
							string gidid = dt.Rows[0]["TXT_FILEGIDID"].ToString();
							string fieldname = dt.Rows[0]["TXT_FieldName"].ToString();

							if (filename != "" && filename != "")
							{
								return clAzureFileStorage.GetFile(viewname, gidid, fieldname, filename);
							}
							else
							{
								return null;
							}
						}

					}
					else if (sSource == "EFileCabinet") //EFC
					{

						int iEfcObjectId = 0;
						int.TryParse(dt.Rows[0]["TXT_ATTACHMENTDIRECTORYPATH"].ToString(), out iEfcObjectId);
						if (iEfcObjectId > 0)
						{
							return GetEFCFile(iEfcObjectId);
						}

					}

				}

				dt = null;

			}
			else if (sSource == "EFileCabinet")
			{
				int iEfcObjectId = 0;
				if (!string.IsNullOrEmpty(Gid_Id))
				{
					int.TryParse(Gid_Id, out iEfcObjectId);
				}
				else
				{
					int.TryParse(sFilePath, out iEfcObjectId);
				}

				if (iEfcObjectId > 0)
				{
					return GetEFCFile(iEfcObjectId);
				}
			}
			else if (sSource == "Selltis")
			{
				string AttachmentsStorageType = System.Configuration.ConfigurationManager.AppSettings["AttachmentsStorageType"].ToString();
				if (AttachmentsStorageType == "Server")
				{
					return GetServerFile(sFilePath);
				}
				else
				{
					return null;
				}
			}

			return null;

		}

		private byte[] GetEFCFile(int iEfcObjectId)
		{
			clEFCAttachments objclEFCAttachments = new clEFCAttachments();
			var storeStream = objclEFCAttachments.DownloadFile(iEfcObjectId);
			var bytes = storeStream.ToArray();
			return bytes;
		}

		private byte[] GetServerFile(string filename)
		{
			string extension = System.IO.Path.GetExtension(filename.ToString());
			string mimetype = GetMIMEType(extension);

			if (!string.IsNullOrEmpty(mimetype))
			{
				var bytes = System.IO.File.ReadAllBytes(filename.ToString());
				return bytes;
			}
			else
			{
				return null;
			}
		}

		public string GetFileDownloadURL(string sQuerystring, string sLinkName)
		{

			return "<a " + "href='" + "/CreateForm/Download?" + sQuerystring + "'>" + sLinkName + "</a>";
			//  Return "<a href='#' onclick=""window.open('../Pages/FileDownloader.aspx?" & sQuerystring & "', 'new', '', false);"">" & sLinkName & "</a>"
		}

		public string GetFileDownloadURLAzure(string sQuerystring, string sLinkName)
		{

			return "<a " + "href='" + "/CreateForm/DownloadAzure?" + sQuerystring + "'>" + sLinkName + "</a>";

			//'DownloadAzure(string sFileName, string sViewName, string sGidId, string sFieldName, string sTempid)

		}

		public string DeleteAttachment(string Gid_Id)
		{

			// Try
			string strFilepath = GetFullFilePathByGID_ID(Gid_Id);
			var oRs = new clRowSet("AD", clC.SELL_EDIT, "GID_ID='" + Gid_Id + "'", "", "**");

			string sFieldName = null;
			string sFileName = null;
			string sAttachmentName = null;
			string sKey = clSettings.GetHostName() + "_";

			if (oRs.GetFirst() != 0)
			{
				sFieldName = Convert.ToString(oRs.GetFieldVal("TXT_FIELDNAME"));
				sFileName = Convert.ToString(oRs.GetFieldVal("TXT_FILENAME"));
				sAttachmentName = Convert.ToString(oRs.GetFieldVal("SYS_NAME"));
				string sAttachmentFileGIDID = Convert.ToString(oRs.GetFieldVal("TXT_FILEGIDID"));

				oRs.DeleteRecord();
				oRs = null;

			//Delete Files from Table
			var rs = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID = '" + sAttachmentFileGIDID + "'", "", sFieldName, 1, "", "", "", "", "", true, true);
			string AllAttachemnts = Convert.ToString(rs.GetFieldVal(sFieldName));
			AllAttachemnts = AllAttachemnts.Replace(sAttachmentName, "");
			rs.SetFieldVal(sFieldName, AllAttachemnts);
			int result = rs.Commit();

				//Delete Files
				string AttachmentsStorageType = System.Configuration.ConfigurationManager.AppSettings["AttachmentsStorageType"].ToString();
				if (AttachmentsStorageType == "Server")
				{
					if (!string.IsNullOrEmpty(strFilepath))
					{
						strFilepath = GetRootPath() + strFilepath;
						if (File.Exists(strFilepath))
						{
							File.Delete(strFilepath);
							return "Success";
						}
					}
					else
					{
						return "File not existed";
					}
				}
				else
				{
					//Delete file from cloud
					var finalDir = clAzureFileStorage.GetFinalFolder(sFileName, sAttachmentFileGIDID, sFieldName, "", false);
					bool retval = clAzureFileStorage.DeleteFile(finalDir, sAttachmentName);

					if (retval)
					{
						return "Success";
					}
					else
					{
						return "File not existed error in file storage";
					}
				}
			}
			else
			{
				return "File not existed";
			}



			//'delete from folder if exists (for selltis file only)
			//'
			//Dim AttachmentsStorageType As String = System.Configuration.ConfigurationManager.AppSettings("AttachmentsStorageType").ToString()
			//    If AttachmentsStorageType = "Server" Then
			//        If Not String.IsNullOrEmpty(strFilepath) Then
			//            strFilepath = GetRootPath() & strFilepath
			//            If File.Exists(strFilepath) Then
			//                File.Delete(strFilepath)
			//                Return "Success"
			//            End If
			//        Else
			//            Return "File not existed"
			//        End If
			//    Else
			//        ''AC/63373361-6332-3339-4143-31312f332f32/ADR_Attachments/2.PNG

			//        Dim str() = strFilepath.Split("/")

			//        If str.Length = 4 Then
			//            Dim viewname As String = str.GetValue(0).ToString()
			//            Dim gidid As String = str.GetValue(1).ToString()
			//            Dim fieldname As String = str.GetValue(2).ToString()
			//            Dim filename As String = str.GetValue(3).ToString()

			//            Dim finalDir = clAzureFileStorage.GetFinalFolder(viewname, gidid, fieldname, "", False)
			//            Dim retval As Boolean = clAzureFileStorage.DeleteFile(finalDir, filename)

			//            If retval Then
			//                Return "Success"
			//            Else
			//                Return "File not existed"
			//            End If
			//        Else
			//            Return "File not existed"
			//        End If
			//    End If

			return "File not existed";

			//Catch ex As Exception
			//    Return ex.ToString()
			//End Try

		}

		public string DeleteAttachment(string ViewName, string ViewGID, string FieldName)
		{

			// Try
			var rs = new clRowSet(ViewName, clC.SELL_EDIT, "GID_ID = '" + ViewGID + "'", "", FieldName, 1, "", "", "", "", "", true, true);
				rs.SetFieldVal(FieldName, "");
				int retval = rs.Commit();

				string strFilepath = ""; //GetFullFilePathByGID_ID(Gid_Id)
				var oRs = new clRowSet("AD", clC.SELL_EDIT, "TXT_FILEGIDID='" + ViewGID + "'", "", "**");
				if (oRs.GetFirst() != 0)
				{
					do
					{
						strFilepath = Convert.ToString(oRs.GetFieldVal("TXT_ATTACHMENTDIRECTORYPATH"));
						if (!string.IsNullOrEmpty(strFilepath))
						{
							strFilepath = GetRootPath() + strFilepath;
							if (File.Exists(strFilepath))
							{
								File.Delete(strFilepath);
							}
						}
						oRs.DeleteRecord();

						if (oRs.GetNext() == 0)
						{
							break;
						}
					} while (true);
				}
				oRs = null;
				rs = null;

				//delete from folder if exists (for selltis file only)
				//
				//If Not String.IsNullOrEmpty(strFilepath) Then
				//    strFilepath = GetRootPath() & strFilepath
				//    If File.Exists(strFilepath) Then
				//        File.Delete(strFilepath)
				//    End If
				//Else
				//    Return "File not existed"
				//End If

				return "Success";
			//Catch ex As Exception
			//    Return ex.ToString()
			//End Try

		}

		public void DeleteAllAttachmentsByFileGidId(string sFileGidId, string sViewName)
		{

			var oRs = new clRowSet("AD", clC.SELL_EDIT, "TXT_FILEGIDID='" + sFileGidId + "'", "", "**");
			if (oRs.GetFirst() != 0)
			{
				int retval = oRs.DeleteRecord();
				if (retval == 1)
				{
					string sFolderPath = Path.Combine(GetRootPath(), sViewName + "\\" + sFileGidId);
					if (Directory.Exists(sFolderPath))
					{
						DeleteFolder(sFolderPath);
					}
				}
			}
			oRs = null;

		}

		public bool CopyAttachments(string Source_File_Gid_Id, string Destination_File_Gid_Id, string sViewName = "")
		{
			string sProc = "clTransform::CopyAttachments";
			try
			{
				string sFields = "TXT_FIELDNAME,TXT_ATTACHMENTNAME,TXT_FILENAME,TXT_ATTACHMENTEXTENSION,DR__ATTACHMENT,TXT_ATTACHMENTDIRECTORYPATH,TXT_SOURCE";

				DataTable dtFiles = GetAD_DataTable("TXT_FileGIDID='" + Source_File_Gid_Id + "' ", sFields);



				if (dtFiles != null && dtFiles.Rows.Count > 0)
				{
					string sHostName = clSettings.GetHostName();
					//Dim RootPath As String = System.Configuration.ConfigurationManager.AppSettings("AttachmentsPath").ToString()
					string RootPath = Convert.ToString(((DataTable)HttpContext.Current.Session[sHostName + "_SiteSettings"]).Rows[0]["AttachmentsPath"]);
					for (var index = 0; index < dtFiles.Rows.Count; index++)
					{

						string ViewName = string.Empty;
						string FileName = string.Empty;
						string FileExtension = string.Empty;
						string SourceFilePath = string.Empty;
						string FileSize = string.Empty;
						string FieldName = string.Empty;
						string NewFilePath = string.Empty;
						string sSource = string.Empty;
						//Dim sSourceObjectId As String = String.Empty

						ViewName = string.IsNullOrEmpty(sViewName) ? dtFiles.Rows[index]["TXT_FILENAME"].ToString() : sViewName;
						FileName = dtFiles.Rows[index]["TXT_AttachmentName"].ToString();
						FileExtension = dtFiles.Rows[index]["TXT_ATTACHMENTEXTENSION"].ToString();
						SourceFilePath = RootPath + dtFiles.Rows[index]["TXT_ATTACHMENTDIRECTORYPATH"].ToString();
						FileSize = dtFiles.Rows[index]["DR__ATTACHMENT"].ToString();
						FieldName = dtFiles.Rows[index]["TXT_FIELDNAME"].ToString(); //dtFiles.Rows(index)("TXT_ATTACHMENTDIRECTORYPATH").ToString().Split("/").GetValue(2).ToString() '
						sSource = Convert.IsDBNull(dtFiles.Rows[index]["TXT_SOURCE"]) ? "Selltis" : dtFiles.Rows[index]["TXT_SOURCE"].ToString();
						//sSourceObjectId = IIf(IsDBNull(dtFiles.Rows(index)("TXT_OBJECTID")), "", dtFiles.Rows(index)("TXT_OBJECTID").ToString())
						NewFilePath = ViewName + "\\" + Destination_File_Gid_Id + "\\" + FieldName; //& "\" & FileName

						if (System.IO.File.Exists(SourceFilePath))
						{

							int retval = SaveAttachment(ViewName, Destination_File_Gid_Id, FileExtension, FileSize, FileName, NewFilePath + "\\" + FileName, FieldName, sSource);
							if (retval == 1)
							{

								string AttachmentsStorageType = System.Configuration.ConfigurationManager.AppSettings["AttachmentsStorageType"].ToString();
								if (AttachmentsStorageType == "Server")
								{
									NewFilePath = RootPath + NewFilePath;
									if (!System.IO.Directory.Exists(NewFilePath))
									{
										System.IO.Directory.CreateDirectory(NewFilePath);
									}
									if (!System.IO.File.Exists(NewFilePath + "\\" + FileName))
									{
										System.IO.File.Copy(SourceFilePath, NewFilePath + "\\" + FileName);
									}
								}
								else
								{
									var sourceFileFolder = clAzureFileStorage.GetFinalFolder(ViewName, Source_File_Gid_Id, FieldName, "", false);
									var destinationFileFolder = clAzureFileStorage.GetFinalFolder(ViewName, Destination_File_Gid_Id, FieldName, "", false);

									if (!clAzureFileStorage.IsFileExists(destinationFileFolder, FileName))
									{
										clAzureFileStorage.CopyFile(sourceFileFolder, destinationFileFolder, FileName);
									}
								}

							}
							// rsnew = Nothing
						}
					}
					//rs = Nothing
				}
				else
				{
					return false;
				}

				return true;
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, , sProc)
				//End If
				return false;
			}
		}

		public int SaveAttachment(string sViewName, string sGID, string sFileExtension, string filesize, string sFilename, string sFileFullpath, string sFieldName, string sSource)
		{
			string sProc = "clTransform::SaveAttachment";
			try
			{

				if (!string.IsNullOrEmpty(sFilename))
				{
					var rs = new clRowSet("AD", clC.SELL_ADD, "", "", "", -1, "", "", "", "", "", true);
					rs.SetFieldVal("TXT_FILENAME", sViewName, 1);
					rs.SetFieldVal("TXT_FILEGIDID", sGID, 1);
					rs.SetFieldVal("TXT_ATTACHMENTEXTENSION", sFileExtension, 1);
					rs.SetFieldVal("DR__ATTACHMENT", filesize, 1);
					rs.SetFieldVal("TXT_AttachmentName", sFilename, 1);
					rs.SetFieldVal("TXT_ATTACHMENTDIRECTORYPATH", sFileFullpath, 1);
					rs.SetFieldVal("TXT_FIELDNAME", sFieldName, 1);
					rs.SetFieldVal("TXT_SOURCE", sSource, 1);
					//rs.SetFieldVal("txt_ObjectId", sSourceObjectId, 1)
					return rs.Commit();
				}
				else
				{
					return 0;
				}

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, , sProc)
				//End If
				return 0;
			}
		}

		public DataTable GetAttachments(string par_sField_Name, string par_File_GID_ID)
		{

			par_sField_Name = par_sField_Name.Replace("ADV", "ADR");

			DataTable dt = null;
			if (string.IsNullOrEmpty(par_sField_Name))
			{
				dt = GetAD_DataTable("TXT_FILEGIDID='" + par_File_GID_ID + "' ", "TXT_AttachmentName,GID_ID,TXT_Source,TXT_ATTACHMENTDIRECTORYPATH");
			}
			else
			{
				dt = GetAD_DataTable("TXT_FILEGIDID='" + par_File_GID_ID + "' AND TXT_FIELDNAME='" + par_sField_Name + "' ", "TXT_AttachmentName,GID_ID,TXT_Source,TXT_ATTACHMENTDIRECTORYPATH");
			}

			return dt;

		}

		public string GetAttachments_ADR(string par_sField_Name, string par_File_GID_ID, string par_Source)
		{
			DataTable dtAttachments = GetAttachments(par_sField_Name, par_File_GID_ID);
			DataView dv = new DataView(dtAttachments, "TXT_Source='" + par_Source + "'", "", DataViewRowState.CurrentRows);
			StringBuilder sAdrAttachments = new StringBuilder();

			if (dv != null && dv.ToTable() != null)
			{
				DataTable dtret = dv.ToTable();
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of dtret.Rows().Count for every iteration:
				int tempVar = dtret.Rows.Count;
				for (var index = 0; index < tempVar; index++)
				{
					sAdrAttachments.Append(dtret.Rows[index]["TXT_AttachmentName"].ToString() + "~" + dtret.Rows[index]["TXT_ATTACHMENTDIRECTORYPATH"].ToString() + "|");
				}
				if (sAdrAttachments.Length > 0)
				{
					return sAdrAttachments.ToString().Remove(sAdrAttachments.Length - 1, 1);
				}
				return "";
			}

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return null;
		}

		public int DeleteFiles(string sFieldName, string sViewName, string sGID, string AttachmentsPath)
		{

			string sDeletedFileNames = string.Empty;
			string sKey = clSettings.GetHostName() + "_";
			if (System.Web.HttpContext.Current.Session[sKey + sFieldName + "_DeletedFiles"] != null)
			{

				sDeletedFileNames = System.Web.HttpContext.Current.Session[sKey + sFieldName + "_DeletedFiles"].ToString();

				if (!string.IsNullOrEmpty(sDeletedFileNames))
				{

					string[] strDeleteFiles;
					strDeleteFiles = sDeletedFileNames.Split('|');

					foreach (string sdelfile in strDeleteFiles)
					{

						string sDeleteFilePath = AttachmentsPath + sViewName + "/" + sGID + "/" + sFieldName + "/" + sdelfile;

						//delete from db
						var oRs = new clRowSet("AD", clC.SELL_EDIT, "TXT_FileGIDID='" + sGID + "' AND TXT_AttachmentName='" + sdelfile + "'", "", "**");
						if (oRs.GetFirst() != 0)
						{
							oRs.DeleteRecord();
						}
						oRs = null;

						if (System.IO.File.Exists(sDeleteFilePath))
						{
							//Try
							//delete from folder
							if (File.Exists(sDeleteFilePath))
							{
									System.IO.File.Delete(sDeleteFilePath);
								}
							//Catch ex As Exception
							//End Try
						}
					}
					//clear the session value
					System.Web.HttpContext.Current.Session[sKey + sFieldName + "_DeletedFiles"] = "";
				}
			}
// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return 0;
		}

		public int DeleteFilesAzure(string sFieldName, string sViewName, string sGID, string AttachmentsPath)
		{

			string sDeletedFileNames = string.Empty;
			string sKey = clSettings.GetHostName() + "_";
			if (System.Web.HttpContext.Current.Session[sKey + sFieldName + "_DeletedFiles"] != null)
			{

				sDeletedFileNames = System.Web.HttpContext.Current.Session[sKey + sFieldName + "_DeletedFiles"].ToString();

				if (!string.IsNullOrEmpty(sDeletedFileNames))
				{

					string[] strDeleteFiles;
					strDeleteFiles = sDeletedFileNames.Split('|');

					foreach (string sdelfile in strDeleteFiles)
					{

						//'Dim sDeleteFilePath As String = AttachmentsPath & sViewName & "/" & sGID & "/" & sFieldName & "/" & sdelfile

						//delete from db
						var oRs = new clRowSet("AD", clC.SELL_EDIT, "TXT_FileGIDID='" + sGID + "' AND TXT_AttachmentName='" + sdelfile + "'", "", "**");
						if (oRs.GetFirst() != 0)
						{
							oRs.DeleteRecord();
						}
						oRs = null;

						//If System.IO.File.Exists(sDeleteFilePath) Then
						//    Try
						//        ''delete from folder
						//        If File.Exists(sDeleteFilePath) Then
						//            System.IO.File.Delete(sDeleteFilePath)
						//        End If

						//    Catch ex As Exception
						//    End Try
						//End If

						if (sdelfile != "" && sdelfile != "")
						{
							var FinalDir = clAzureFileStorage.GetFinalFolder(sViewName, sGID, sFieldName, "", false);
							clAzureFileStorage.DeleteFile(FinalDir, sdelfile);
						}

					}
					//clear the session value
					System.Web.HttpContext.Current.Session[sKey + sFieldName + "_DeletedFiles"] = "";
				}
			}
// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return 0;
		}

		public string GetFullFilePathByGID_ID(string gidid)
		{
			string sFilepath = string.Empty;
			DataTable dt = GetAD_DataTable("GID_ID='" + gidid.ToString() + "' ", "TXT_ATTACHMENTDIRECTORYPATH");
			if (dt != null && dt.Rows.Count > 0)
			{
				sFilepath = dt.Rows[0]["TXT_ATTACHMENTDIRECTORYPATH"].ToString();
			}
			return sFilepath;
		}

		private string GetMIMEType(string extension)
		{
			switch (extension.ToLower())
			{
				case ".3dm":
					return "x-world/x-3dmf";
				case ".3dmf":
					return "x-world/x-3dmf";
				case ".a":
					return "application/octet-stream";
				case ".aab":
					return "application/x-authorware-bin";
				case ".aam":
					return "application/x-authorware-map";
				case ".aas":
					return "application/x-authorware-seg";
				case ".abc":
					return "text/vnd.abc";
				case ".acgi":
					return "text/html";
				case ".afl":
					return "video/animaflex";
				case ".ai":
					return "application/postscript";
				case ".aif":
					return "audio/aiff";
				case ".aifc":
					return "audio/aiff";
				case ".aiff":
					return "audio/aiff";
				case ".aim":
					return "application/x-aim";
				case ".aip":
					return "text/x-audiosoft-intra";
				case ".ani":
					return "application/x-navi-animation";
				case ".aos":
					return "application/x-nokia-9000-communicator-add-on-software";
				case ".aps":
					return "application/mime";
				case ".arc":
					return "application/octet-stream";
				case ".arj":
					return "application/arj";
				case ".art":
					return "image/x-jg";
				case ".asf":
					return "video/x-ms-asf";
				case ".asm":
					return "text/x-asm";
				case ".asp":
					return "text/asp";
				case ".asx":
					return "application/x-mplayer2";
				case ".au":
					return "audio/basic";
				case ".avi":
					return "application/x-troff-msvideo";
				case ".avs":
					return "video/avs-video";
				case ".bcpio":
					return "application/x-bcpio";
				case ".bin":
					return "application/mac-binary";
				case ".bm":
					return "image/bmp";
				case ".bmp":
					return "image/bmp";
				case ".boo":
					return "application/book";
				case ".book":
					return "application/book";
				case ".boz":
					return "application/x-bzip2";
				case ".bsh":
					return "application/x-bsh";
				case ".bz":
					return "application/x-bzip";
				case ".bz2":
					return "application/x-bzip2";
				case ".c":
					return "text/plain";
				case ".c++":
					return "text/plain";
				case ".cat":
					return "application/vnd.ms-pki.seccat";
				case ".cc":
					return "text/plain";
				case ".ccad":
					return "application/clariscad";
				case ".cco":
					return "application/x-cocoa";
				case ".cdf":
					return "application/cdf";
				case ".cer":
					return "application/pkix-cert";
				case ".cha":
					return "application/x-chat";
				case ".chat":
					return "application/x-chat";
				case ".class":
					return "application/java";
				case ".com":
					return "application/octet-stream";
				case ".conf":
					return "text/plain";
				case ".cpio":
					return "application/x-cpio";
				case ".cpp":
					return "text/x-c";
				case ".cpt":
					return "application/mac-compactpro";
				case ".crl":
					return "application/pkcs-crl";
				case ".crt":
					return "application/pkix-cert";
				case ".csh":
					return "application/x-csh";
				case ".css":
					return "application/x-pointplus";
				case ".cxx":
					return "text/plain";
				case ".dcr":
					return "application/x-director";
				case ".deepv":
					return "application/x-deepv";
				case ".def":
					return "text/plain";
				case ".der":
					return "application/x-x509-ca-cert";
				case ".dif":
					return "video/x-dv";
				case ".dir":
					return "application/x-director";
				case ".dl":
					return "video/dl";
				case ".doc":
					return "application/msword";
				case ".docx":
					return "application/msword";
				case ".dot":
					return "application/msword";
				case ".dp":
					return "application/commonground";
				case ".drw":
					return "application/drafting";
				case ".dump":
					return "application/octet-stream";
				case ".dv":
					return "video/x-dv";
				case ".dvi":
					return "application/x-dvi";
				case ".dwf":
					return "drawing/x-dwf (old)";
				case ".dwg":
					return "application/acad";
				case ".dxf":
					return "application/dxf";
				case ".dxr":
					return "application/x-director";
				case ".el":
					return "text/x-script.elisp";
				case ".elc":
					return "application/x-bytecode.elisp (compiled elisp)";
				case ".env":
					return "application/x-envoy";
				case ".eps":
					return "application/postscript";
				case ".es":
					return "application/x-esrehber";
				case ".etx":
					return "text/x-setext";
				case ".evy":
					return "application/envoy";
				case ".exe":
					return "application/octet-stream";
				case ".f":
					return "text/plain";
				case ".f77":
					return "text/x-fortran";
				case ".f90":
					return "text/plain";
				case ".fdf":
					return "application/vnd.fdf";
				case ".fif":
					return "application/fractals";
				case ".fli":
					return "video/fli";
				case ".flo":
					return "image/florian";
				case ".flx":
					return "text/vnd.fmi.flexstor";
				case ".fmf":
					return "video/x-atomic3d-feature";
				case ".for":
					return "text/plain";
				case ".fpx":
					return "image/vnd.fpx";
				case ".frl":
					return "application/freeloader";
				case ".funk":
					return "audio/make";
				case ".g":
					return "text/plain";
				case ".g3":
					return "image/g3fax";
				case ".gif":
					return "image/gif";
				case ".gl":
					return "video/gl";
				case ".gsd":
					return "audio/x-gsm";
				case ".gsm":
					return "audio/x-gsm";
				case ".gsp":
					return "application/x-gsp";
				case ".gss":
					return "application/x-gss";
				case ".gtar":
					return "application/x-gtar";
				case ".gz":
					return "application/x-compressed";
				case ".gzip":
					return "application/x-gzip";
				case ".h":
					return "text/plain";
				case ".hdf":
					return "application/x-hdf";
				case ".help":
					return "application/x-helpfile";
				case ".hgl":
					return "application/vnd.hp-hpgl";
				case ".hh":
					return "text/plain";
				case ".hlb":
					return "text/x-script";
				case ".hlp":
					return "application/hlp";
				case ".hpg":
					return "application/vnd.hp-hpgl";
				case ".hpgl":
					return "application/vnd.hp-hpgl";
				case ".hqx":
					return "application/binhex";
				case ".hta":
					return "application/hta";
				case ".htc":
					return "text/x-component";
				case ".htm":
					return "text/html";
				case ".html":
					return "text/html";
				case ".htmls":
					return "text/html";
				case ".htt":
					return "text/webviewhtml";
				case ".htx":
					return "text/html";
				case ".ice":
					return "x-conference/x-cooltalk";
				case ".ico":
					return "image/x-icon";
				case ".idc":
					return "text/plain";
				case ".ief":
					return "image/ief";
				case ".iefs":
					return "image/ief";
				case ".iges":
					return "application/iges";
				case ".igs":
					return "application/iges";
				case ".ima":
					return "application/x-ima";
				case ".imap":
					return "application/x-httpd-imap";
				case ".inf":
					return "application/inf";
				case ".ins":
					return "application/x-internett-signup";
				case ".ip":
					return "application/x-ip2";
				case ".isu":
					return "video/x-isvideo";
				case ".it":
					return "audio/it";
				case ".iv":
					return "application/x-inventor";
				case ".ivr":
					return "i-world/i-vrml";
				case ".ivy":
					return "application/x-livescreen";
				case ".jam":
					return "audio/x-jam";
				case ".jav":
					return "text/plain";
				case ".java":
					return "text/plain";
				case ".jcm":
					return "application/x-java-commerce";
				case ".jfif":
					return "image/jpeg";
				case ".jfif-tbnl":
					return "image/jpeg";
				case ".jpe":
					return "image/jpeg";
				case ".jpeg":
					return "image/jpeg";
				case ".jpg":
					return "image/jpeg";
				case ".jps":
					return "image/x-jps";
				case ".js":
					return "application/x-javascript";
				case ".jut":
					return "image/jutvision";
				case ".kar":
					return "audio/midi";
				case ".ksh":
					return "application/x-ksh";
				case ".la":
					return "audio/nspaudio";
				case ".lam":
					return "audio/x-liveaudio";
				case ".latex":
					return "application/x-latex";
				case ".lha":
					return "application/lha";
				case ".lhx":
					return "application/octet-stream";
				case ".list":
					return "text/plain";
				case ".lma":
					return "audio/nspaudio";
				case ".log":
					return "text/plain";
				case ".lsp":
					return "application/x-lisp";
				case ".lst":
					return "text/plain";
				case ".lsx":
					return "text/x-la-asf";
				case ".ltx":
					return "application/x-latex";
				case ".lzh":
					return "application/octet-stream";
				case ".lzx":
					return "application/lzx";
				case ".m":
					return "text/plain";
				case ".m1v":
					return "video/mpeg";
				case ".m2a":
					return "audio/mpeg";
				case ".m2v":
					return "video/mpeg";
				case ".m3u":
					return "audio/x-mpequrl";
				case ".man":
					return "application/x-troff-man";
				case ".map":
					return "application/x-navimap";
				case ".mar":
					return "text/plain";
				case ".mbd":
					return "application/mbedlet";
				case ".mc$":
					return "application/x-magic-cap-package-1.0";
				case ".mcd":
					return "application/mcad";
				case ".mcf":
					return "image/vasa";
				case ".mcp":
					return "application/netmc";
				case ".me":
					return "application/x-troff-me";
				case ".mht":
					return "message/rfc822";
				case ".mhtml":
					return "message/rfc822";
				case ".mid":
					return "application/x-midi";
				case ".midi":
					return "application/x-midi";
				case ".mif":
					return "application/x-frame";
				case ".mime":
					return "message/rfc822";
				case ".mjf":
					return "audio/x-vnd.audioexplosion.mjuicemediafile";
				case ".mjpg":
					return "video/x-motion-jpeg";
				case ".mm":
					return "application/base64";
				case ".mme":
					return "application/base64";
				case ".mod":
					return "audio/mod";
				case ".moov":
					return "video/quicktime";
				case ".mov":
					return "video/quicktime";
				case ".movie":
					return "video/x-sgi-movie";
				case ".mp2":
					return "audio/mpeg";
				case ".mp3":
					return "audio/mpeg3";
				case ".mpa":
					return "audio/mpeg";
				case ".mpc":
					return "application/x-project";
				case ".mpe":
					return "video/mpeg";
				case ".mpeg":
					return "video/mpeg";
				case ".mpg":
					return "audio/mpeg";
				case ".mpga":
					return "audio/mpeg";
				case ".mpp":
					return "application/vnd.ms-project";
				case ".mpt":
					return "application/x-project";
				case ".mpv":
					return "application/x-project";
				case ".mpx":
					return "application/x-project";
				case ".mrc":
					return "application/marc";
				case ".ms":
					return "application/x-troff-ms";
				case ".mv":
					return "video/x-sgi-movie";
				case ".my":
					return "audio/make";
				case ".mzz":
					return "application/x-vnd.audioexplosion.mzz";
				case ".nap":
					return "image/naplps";
				case ".naplps":
					return "image/naplps";
				case ".nc":
					return "application/x-netcdf";
				case ".ncm":
					return "application/vnd.nokia.configuration-message";
				case ".nif":
					return "image/x-niff";
				case ".niff":
					return "image/x-niff";
				case ".nix":
					return "application/x-mix-transfer";
				case ".nsc":
					return "application/x-conference";
				case ".nvd":
					return "application/x-navidoc";
				case ".o":
					return "application/octet-stream";
				case ".oda":
					return "application/oda";
				case ".omc":
					return "application/x-omc";
				case ".omcd":
					return "application/x-omcdatamaker";
				case ".omcr":
					return "application/x-omcregerator";
				case ".p":
					return "text/x-pascal";
				case ".p10":
					return "application/pkcs10";
				case ".p12":
					return "application/pkcs-12";
				case ".p7a":
					return "application/x-pkcs7-signature";
				case ".p7c":
					return "application/pkcs7-mime";
				case ".p7m":
					return "application/pkcs7-mime";
				case ".p7r":
					return "application/x-pkcs7-certreqresp";
				case ".p7s":
					return "application/pkcs7-signature";
				case ".part":
					return "application/pro_eng";
				case ".pas":
					return "text/pascal";
				case ".pbm":
					return "image/x-portable-bitmap";
				case ".pcl":
					return "application/vnd.hp-pcl";
				case ".pct":
					return "image/x-pict";
				case ".pcx":
					return "image/x-pcx";
				case ".pdb":
					return "chemical/x-pdb";
				case ".pdf":
					return "application/pdf";
				case ".pfunk":
					return "audio/make";
				case ".pgm":
					return "image/x-portable-graymap";
				case ".pic":
					return "image/pict";
				case ".pict":
					return "image/pict";
				case ".pkg":
					return "application/x-newton-compatible-pkg";
				case ".pko":
					return "application/vnd.ms-pki.pko";
				case ".pl":
					return "text/plain";
				case ".plx":
					return "application/x-pixclscript";
				case ".pm":
					return "image/x-xpixmap";
				case ".pm4":
					return "application/x-pagemaker";
				case ".pm5":
					return "application/x-pagemaker";
				case ".png":
					return "image/png";
				case ".pnm":
					return "application/x-portable-anymap";
				case ".pot":
					return "application/mspowerpoint";
				case ".pov":
					return "model/x-pov";
				case ".ppa":
					return "application/vnd.ms-powerpoint";
				case ".ppm":
					return "image/x-portable-pixmap";
				case ".pps":
					return "application/mspowerpoint";
				case ".ppt":
					return "application/mspowerpoint";
				case ".ppz":
					return "application/mspowerpoint";
				case ".pre":
					return "application/x-freelance";
				case ".prt":
					return "application/pro_eng";
				case ".ps":
					return "application/postscript";
				case ".psd":
					return "application/octet-stream";
				case ".pvu":
					return "paleovu/x-pv";
				case ".pwz":
					return "application/vnd.ms-powerpoint";
				case ".py":
					return "text/x-script.phyton";
				case ".pyc":
					return "application/x-bytecode.python";
				case ".qcp":
					return "audio/vnd.qcelp";
				case ".qd3":
					return "x-world/x-3dmf";
				case ".qd3d":
					return "x-world/x-3dmf";
				case ".qif":
					return "image/x-quicktime";
				case ".qt":
					return "video/quicktime";
				case ".qtc":
					return "video/x-qtc";
				case ".qti":
					return "image/x-quicktime";
				case ".qtif":
					return "image/x-quicktime";
				case ".ra":
					return "audio/x-pn-realaudio";
				case ".ram":
					return "audio/x-pn-realaudio";
				case ".ras":
					return "application/x-cmu-raster";
				case ".rast":
					return "image/cmu-raster";
				case ".rexx":
					return "text/x-script.rexx";
				case ".rf":
					return "image/vnd.rn-realflash";
				case ".rgb":
					return "image/x-rgb";
				case ".rm":
					return "application/vnd.rn-realmedia";
				case ".rmi":
					return "audio/mid";
				case ".rmm":
					return "audio/x-pn-realaudio";
				case ".rmp":
					return "audio/x-pn-realaudio";
				case ".rng":
					return "application/ringing-tones";
				case ".rnx":
					return "application/vnd.rn-realplayer";
				case ".roff":
					return "application/x-troff";
				case ".rp":
					return "image/vnd.rn-realpix";
				case ".rpm":
					return "audio/x-pn-realaudio-plugin";
				case ".rt":
					return "text/richtext";
				case ".rtf":
					return "application/rtf";
				case ".rtx":
					return "application/rtf";
				case ".rv":
					return "video/vnd.rn-realvideo";
				case ".s":
					return "text/x-asm";
				case ".s3m":
					return "audio/s3m";
				case ".saveme":
					return "application/octet-stream";
				case ".sbk":
					return "application/x-tbook";
				case ".scm":
					return "application/x-lotusscreencam";
				case ".sdml":
					return "text/plain";
				case ".sdp":
					return "application/sdp";
				case ".sdr":
					return "application/sounder";
				case ".sea":
					return "application/sea";
				case ".set":
					return "application/set";
				case ".sgm":
					return "text/sgml";
				case ".sgml":
					return "text/sgml";
				case ".sh":
					return "application/x-bsh";
				case ".shar":
					return "application/x-bsh";
				case ".shtml":
					return "text/html";
				case ".sid":
					return "audio/x-psid";
				case ".sit":
					return "application/x-sit";
				case ".skd":
					return "application/x-koan";
				case ".skm":
					return "application/x-koan";
				case ".skp":
					return "application/x-koan";
				case ".skt":
					return "application/x-koan";
				case ".sl":
					return "application/x-seelogo";
				case ".smi":
					return "application/smil";
				case ".smil":
					return "application/smil";
				case ".snd":
					return "audio/basic";
				case ".sol":
					return "application/solids";
				case ".spc":
					return "application/x-pkcs7-certificates";
				case ".spl":
					return "application/futuresplash";
				case ".spr":
					return "application/x-sprite";
				case ".sprite":
					return "application/x-sprite";
				case ".src":
					return "application/x-wais-source";
				case ".ssi":
					return "text/x-server-parsed-html";
				case ".ssm":
					return "application/streamingmedia";
				case ".sst":
					return "application/vnd.ms-pki.certstore";
				case ".step":
					return "application/step";
				case ".stl":
					return "application/sla";
				case ".stp":
					return "application/step";
				case ".sv4cpio":
					return "application/x-sv4cpio";
				case ".sv4crc":
					return "application/x-sv4crc";
				case ".svf":
					return "image/vnd.dwg";
				case ".svr":
					return "application/x-world";
				case ".swf":
					return "application/x-shockwave-flash";
				case ".t":
					return "application/x-troff";
				case ".talk":
					return "text/x-speech";
				case ".tar":
					return "application/x-tar";
				case ".tbk":
					return "application/toolbook";
				case ".tcl":
					return "application/x-tcl";
				case ".tcsh":
					return "text/x-script.tcsh";
				case ".tex":
					return "application/x-tex";
				case ".texi":
					return "application/x-texinfo";
				case ".texinfo":
					return "application/x-texinfo";
				case ".text":
					return "application/plain";
				case ".tgz":
					return "application/gnutar";
				case ".tif":
					return "image/tiff";
				case ".tiff":
					return "image/tiff";
				case ".tr":
					return "application/x-troff";
				case ".tsi":
					return "audio/tsp-audio";
				case ".tsp":
					return "application/dsptype";
				case ".tsv":
					return "text/tab-separated-values";
				case ".turbot":
					return "image/florian";
				case ".txt":
					return "text/plain";
				case ".uil":
					return "text/x-uil";
				case ".uni":
					return "text/uri-list";
				case ".unis":
					return "text/uri-list";
				case ".unv":
					return "application/i-deas";
				case ".uri":
					return "text/uri-list";
				case ".uris":
					return "text/uri-list";
				case ".ustar":
					return "application/x-ustar";
				case ".uu":
					return "application/octet-stream";
				case ".uue":
					return "text/x-uuencode";
				case ".vcd":
					return "application/x-cdlink";
				case ".vcs":
					return "text/x-vcalendar";
				case ".vda":
					return "application/vda";
				case ".vdo":
					return "video/vdo";
				case ".vew":
					return "application/groupwise";
				case ".viv":
					return "video/vivo";
				case ".vivo":
					return "video/vivo";
				case ".vmd":
					return "application/vocaltec-media-desc";
				case ".vmf":
					return "application/vocaltec-media-file";
				case ".voc":
					return "audio/voc";
				case ".vos":
					return "video/vosaic";
				case ".vox":
					return "audio/voxware";
				case ".vqe":
					return "audio/x-twinvq-plugin";
				case ".vqf":
					return "audio/x-twinvq";
				case ".vql":
					return "audio/x-twinvq-plugin";
				case ".vrml":
					return "application/x-vrml";
				case ".vrt":
					return "x-world/x-vrt";
				case ".vsd":
					return "application/x-visio";
				case ".vst":
					return "application/x-visio";
				case ".vsw":
					return "application/x-visio";
				case ".w60":
					return "application/wordperfect6.0";
				case ".w61":
					return "application/wordperfect6.1";
				case ".w6w":
					return "application/msword";
				case ".wav":
					return "audio/wav";
				case ".wb1":
					return "application/x-qpro";
				case ".wbmp":
					return "image/vnd.wap.wbmp";
				case ".web":
					return "application/vnd.xara";
				case ".wiz":
					return "application/msword";
				case ".wk1":
					return "application/x-123";
				case ".wmf":
					return "windows/metafile";
				case ".wml":
					return "text/vnd.wap.wml";
				case ".wmlc":
					return "application/vnd.wap.wmlc";
				case ".wmls":
					return "text/vnd.wap.wmlscript";
				case ".wmlsc":
					return "application/vnd.wap.wmlscriptc";
				case ".word":
					return "application/msword";
				case ".wp":
					return "application/wordperfect";
				case ".wp5":
					return "application/wordperfect";
				case ".wp6":
					return "application/wordperfect";
				case ".wpd":
					return "application/wordperfect";
				case ".wq1":
					return "application/x-lotus";
				case ".wri":
					return "application/mswrite";
				case ".wrl":
					return "application/x-world";
				case ".wrz":
					return "model/vrml";
				case ".wsc":
					return "text/scriplet";
				case ".wsrc":
					return "application/x-wais-source";
				case ".wtk":
					return "application/x-wintalk";
				case ".xbm":
					return "image/x-xbitmap";
				case ".xdr":
					return "video/x-amt-demorun";
				case ".xgz":
					return "xgl/drawing";
				case ".xif":
					return "image/vnd.xiff";
				case ".xl":
					return "application/excel";
				case ".xla":
					return "application/excel";
				case ".xls":
					return "application/x-excel";
				case ".xlsx":
					return "application/x-msexcel";
				case ".xlb":
					return "application/excel";
				case ".xlc":
					return "application/excel";
				case ".xld":
					return "application/excel";
				case ".xlk":
					return "application/excel";
				case ".xll":
					return "application/excel";
				case ".xlm":
					return "application/excel";
				case ".xlt":
					return "application/excel";
				case ".xlv":
					return "application/excel";
				case ".xlw":
					return "application/excel";
				case ".xm":
					return "audio/xm";
				case ".xml":
					return "application/xml";
				case ".xmz":
					return "xgl/movie";
				case ".xpix":
					return "application/x-vnd.ls-xpix";
				case ".xpm":
					return "image/x-xpixmap";
				case ".x-png":
					return "image/png";
				case ".xsr":
					return "video/x-amt-showrun";
				case ".xwd":
					return "image/x-xwd";
				case ".xyz":
					return "chemical/x-pdb";
				case ".z":
					return "application/x-compress";
				case ".zip":
					return "application/x-compressed";
				case ".zoo":
					return "application/octet-stream";
				case ".zsh":
					return "text/x-script.zsh";
				default:
					return "application/octet-stream";
			}

		}

		public string GetFileNamesFromADRRawText(string sAttachedFileNames)
		{

			string sFileNamesOnly = string.Empty;
			string[] srawfiles = sAttachedFileNames.Split('=');
			string[] strfiles = srawfiles.GetValue(0).ToString().Split('|');
			string tempid = string.Empty;

			if (srawfiles.Length > 1)
			{
				tempid = Convert.ToString(srawfiles.GetValue(1));
			}

			foreach (string sname in strfiles)
			{
				if (string.IsNullOrEmpty(sFileNamesOnly))
				{
					sFileNamesOnly = sname.Split('~').GetValue(0).ToString();
				}
				else
				{
					sFileNamesOnly = sFileNamesOnly + "|" + sname.Split('~').GetValue(0).ToString();
				}
			}

			return sFileNamesOnly;

		}

		//6/19

		//Private Function DeleteFiles(ByVal sFieldName As String, ByVal sViewName As String, ByVal sGID As String, ByVal AttachmentsPath As String) As Integer
		//    Dim objclAttachments As New clAttachments()
		//    objclAttachments.DeleteFiles(sFieldName, sViewName, sGID, AttachmentsPath)
		//End Function

		public void SaveAttachments(string sFieldName, string sAttachedFileNames, string sGID, string sViewName, bool bClearSessionFiles = true)
		{
			//Dim sFieldName As String = String.Empty
			//Dim sAttachedFileNames As String = String.Empty
			string[] srawfiles = null;
			string[] strfiles = null;
			string sFileNamesOnly = string.Empty;
			string tempid = string.Empty;

			string AttachmentsPath = GetRootPath();
			string AttachmentsTempPath = GetRootTempPath();
			//Dim sViewName As String = goRowset.GetFileName

			//sFieldName = ADRFields(index)
			//sAttachedFileNames = ADRFieldsValues(index)
			srawfiles = sAttachedFileNames.Split('=');
			strfiles = srawfiles.GetValue(0).ToString().Split('|');

			//If srawfiles.Length > 1 Then
			//    tempid = srawfiles.GetValue(1)
			//End If

			string skey = clSettings.GetHostName() + "_";

			if (HttpContext.Current.Session[skey + sFieldName + "_TempId"] != null)
			{
				tempid = HttpContext.Current.Session[skey + sFieldName + "_TempId"].ToString();
			}

			//'Delete existing files  
			//'DeleteFiles(sFieldName, sViewName, sGID, AttachmentsPath)

			string AttachmentsStorageType = System.Configuration.ConfigurationManager.AppSettings["AttachmentsStorageType"].ToString();
			if (AttachmentsStorageType == "Server")
			{
				DeleteFiles(sFieldName, sViewName, sGID, AttachmentsPath);
			}
			else
			{
				DeleteFilesAzure(sFieldName, sViewName, sGID, AttachmentsPath);
			}

			//'save new local files
			if (!string.IsNullOrEmpty(tempid)) //'new selltis files attached
			{

				foreach (string sfile in strfiles)
				{

					if (sfile.Contains("~^")) //not a server file
					{
						string[] sfiles = sfile.Split(new string[] {"~^"}, StringSplitOptions.None);
						string strfilename = sfiles.GetValue(0).ToString();
						string strObjectId = sfiles.GetValue(1).ToString();
						if (AttachmentsStorageType == "Server")
						{
							SaveAttachmentToDB(strObjectId, sFieldName, sGID, sViewName, strfilename, "", "", "EFileCabinet");
						}
						else
						{
							SaveAttachmentToDB_Azure(strObjectId, sFieldName, sGID, sViewName, strfilename, "", "", "EFileCabinet");
						}
					}
					else
					{
						if (AttachmentsStorageType == "Server")
						{
							SaveAttachmentToDB(tempid, sFieldName, sGID, sViewName, sfile, AttachmentsTempPath, AttachmentsPath, "Selltis");
						}
						else
						{
							SaveAttachmentToDB_Azure(tempid, sFieldName, sGID, sViewName, sfile, AttachmentsTempPath, AttachmentsPath, "Selltis");
						}
					}

				}

				if (bClearSessionFiles)
				{
					//deleting the temp folder
					// Try
					if (AttachmentsStorageType == "Server")
					{
							if (System.IO.Directory.Exists(AttachmentsTempPath + tempid))
							{
								foreach (string _file in Directory.GetFiles(AttachmentsTempPath + tempid))
								{
									File.Delete(_file);
								}
								System.IO.Directory.Delete(AttachmentsTempPath + tempid);
								System.Threading.Thread.Sleep(1000);
							}
						}
						else
						{
							var tempDir = clAzureFileStorage.GetFinalFolder("", "", "", tempid, true);

							foreach (IListFileItem item in tempDir.ListFilesAndDirectories())
							{
								if (item.GetType() == typeof(CloudFile))
								{

									CloudFile file = (CloudFile)item;
									file.DeleteIfExists();
									//ElseIf item.[GetType]() = GetType(CloudFileDirectory) Then
									//    ' Do whatever
									//    Dim dir As CloudFileDirectory = DirectCast(item, CloudFileDirectory)
								}
							}

							tempDir.DeleteIfExists();
						}
					//Catch ex As Exception
					//End Try
					System.Web.HttpContext.Current.Session[skey + sFieldName + "_Files"] = "";
					System.Web.HttpContext.Current.Session[skey + sFieldName + "_TempId"] = "";
				}

			}

		}

		private void SaveAttachmentToDB(string tempid, string sFieldName, string sGID, string sViewName, string sfile, string AttachmentsTempPath, string AttachmentsPath, string Source)
		{

			string[] sfileinfo = null;
			string temppath = string.Empty;
			string rootpath = string.Empty;
			string sFileExtension = string.Empty;
			string sFilename = string.Empty;
			string filesize = null;
			string sFileFullpath = string.Empty;
			clAttachments objclAttachments = new clAttachments();

			sfileinfo = sfile.Split('~');

			sFilename = sfileinfo.GetValue(0).ToString();
			temppath = AttachmentsTempPath + tempid + "\\" + sFilename; //System.Web.HttpContext.Current.Server.MapPath(AttachmentsPath) & tempid & "\" & sFilename
			rootpath = AttachmentsPath + sViewName + "\\" + sGID + "\\" + sFieldName + "\\"; //System.Web.HttpContext.Current.Server.MapPath(AttachmentsPath) & sViewName & "\" & sGID & "\" & sFieldName & "\"

			if (Source == "Selltis")
			{

				if (System.IO.File.Exists(temppath))
				{

					sFileExtension = System.IO.Path.GetExtension(temppath);
					//filesize = sfileinfo.GetValue(1).ToString()
					sFileFullpath = sViewName + "/" + sGID + "/" + sFieldName + "/" + sFilename; //AttachmentsPath & sViewName & "/" & sGID & "/" & sFieldName & "/" & sFilename

					if (System.IO.File.Exists(temppath))
					{
						FileInfo info = new FileInfo(temppath);
						filesize = info.Length.ToString();
					}

					int retval = SaveAttachment(sViewName, sGID, sFileExtension, filesize, sFilename, sFileFullpath, sFieldName, Source);

					if (retval == 1) //copy the files to root directory
					{

						if (!System.IO.Directory.Exists(rootpath))
						{
							System.IO.Directory.CreateDirectory(rootpath);
						}

						//move the file from temp folder to root folder
						//  Try
						if (System.IO.File.Exists(temppath))
						{
								if (System.IO.File.Exists(rootpath + sFilename))
								{
									System.IO.File.Delete(rootpath + sFilename);
									System.Threading.Thread.Sleep(500);
								}
								System.IO.File.Copy(temppath, rootpath + sFilename);
								System.Threading.Thread.Sleep(1000);
							}
						//Catch ex As Exception
						//End Try

					}


				}

			}
			else if (Source == "EFileCabinet") //'other than selltis
			{
				//tempid is the objectId
				SaveAttachment(sViewName, sGID, sFileExtension, filesize, sFilename, tempid, sFieldName, Source);
			}

		}

		private DataTable GetAD_DataTable(string filter, string fields, string sortfiled = "")
		{
			clRowSet rs = new clRowSet("AD", clC.SELL_EDIT, filter, sortfiled, fields, -1, "", "", "", "", "", false, true);
			rs.ToTable();
			DataTable dt = rs.dtTransTable;
			rs = null;
			return dt;
		}

		public string AttachExtrenalSourceFile(string sSource)
		{

			if (sSource == "EFileCabinet")
			{

				if (HttpContext.Current.Request.Cookies["EFCSelFile"] != null)
				{

					var sCookie = HttpContext.Current.Request.Cookies["EFCSelFile"].Value;
					string[] sEFCFiles = sCookie.Split('|');

					string sField = sEFCFiles.GetValue(0).ToString();
					string sFileName = sEFCFiles.GetValue(1).ToString(); //sEFCFiles.GetValue(1).ToString() & "~^" & sEFCFiles.GetValue(2).ToString()
					string sKey = clSettings.GetHostName() + "_";
					string sDeletedFileNames = string.Empty;
					if (HttpContext.Current.Session[sKey + sField + "_DeletedFiles"] != null)
					{
						sDeletedFileNames = HttpContext.Current.Session[sKey + sField + "_DeletedFiles"].ToString();
					}

					if (sDeletedFileNames.Contains(sFileName))
					{
						HttpContext.Current.Response.Cookies["EFCSelFile"].Expires = DateTime.Now.AddDays(-1);
						return "This file is deleted recently so please save the record first and then upload the same file again";
					}

					if (HttpContext.Current.Session[sKey + sField + "_Files"] != null)
					{

						string strattachments = string.Empty;
						strattachments = HttpContext.Current.Session[sKey + sField + "_Files"].ToString();
						if (strattachments.Contains(sFileName))
						{
							HttpContext.Current.Response.Cookies["EFCSelFile"].Expires = DateTime.Now.AddDays(-1);
							return "File with the same name is already uploaded";
						}
						sFileName = sEFCFiles.GetValue(1).ToString() + "~^" + sEFCFiles.GetValue(2).ToString();
						HttpContext.Current.Session[sKey + sField + "_Files"] = HttpContext.Current.Session[sKey + sField + "_Files"] + "|" + sFileName;

					}
					else
					{
						HttpContext.Current.Session[sKey + sField + "_Files"] = sFileName;
					}

					HttpContext.Current.Response.Cookies["EFCSelFile"].Expires = DateTime.Now.AddDays(-1);

					return "";

				}

			}



// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return null;
		}

		public void CopyTempAttachments(string sFieldName, string sCopyFromId, string sFromValue)
		{
			//Try
			string sKey = clSettings.GetHostName() + "_";
				string sTempid = Guid.NewGuid().ToString();
				HttpContext.Current.Session[sKey + sFieldName + "_Files"] = sFromValue;
				HttpContext.Current.Session[sKey + sFieldName + "_TempId"] = sTempid;
				DataTable dtCreatefromFiles = GetAttachments(sFieldName, sCopyFromId);
				string srootpath = GetRootPath();
				string sTempPath = GetRootTempPath();
				for (var index = 0; index < dtCreatefromFiles.Rows.Count; index++)
				{
					if (dtCreatefromFiles.Rows[index]["TXT_Source"].ToString() == "Selltis")
					{
						string sfilepath = Path.Combine(srootpath, dtCreatefromFiles.Rows[index]["TXT_ATTACHMENTDIRECTORYPATH"].ToString());
						string sfilename = dtCreatefromFiles.Rows[index]["TXT_AttachmentName"].ToString();
						string sTempfilepath = Path.Combine(sTempPath, sTempid);
						if (File.Exists(sfilepath))
						{
							if (!Directory.Exists(sTempfilepath))
							{
								Directory.CreateDirectory(sTempfilepath);
							}
							File.Copy(sfilepath, sTempfilepath + "\\" + sfilename);
						}
					}
				}
			//Catch ex As Exception
			//End Try
		}

		private void DeleteFolder(string sFolderPath)
		{
			try
			{
				if (Directory.Exists(sFolderPath))
				{
					foreach (string _directory in Directory.GetDirectories(sFolderPath))
					{
						DeleteFilesInTheFolder(_directory);
						DeleteFolder(_directory);
						if (Directory.Exists(_directory))
						{
							Directory.Delete(_directory);
						}
					}
					if (Directory.Exists(sFolderPath))
					{
						Directory.Delete(sFolderPath);
					}
				}
			}
			catch (Exception ex)
			{
			}
		}

		private void DeleteFilesInTheFolder(string sFolderPath)
		{
			try
			{
				if (Directory.Exists(sFolderPath))
				{
					foreach (string _file in Directory.GetFiles(sFolderPath))
					{
						File.Delete(_file);
					}
				}
			}
			catch (Exception ex)
			{
			}
		}

		//Public Function UploadAttachment(pViewName As String, pFile_Gid_Id As String, pFieldName As String, Optional pFileFullName As String = "", _
		//                                 Optional pFileName As String = "", Optional pFileData() As Byte = Nothing, Optional pUploadFrom As String = "") As Boolean
		//    Try

		//        Dim Source As String = "Selltis"
		//        Dim FileName As String = ""
		//        Dim FilePath As String = ""
		//        Dim sFilePath As String = ""

		//        If pUploadFrom.ToLower() = "mobile" Then
		//            FileName = pFileName
		//        Else
		//            FileName = Path.GetFileName(pFileFullName)
		//        End If

		//        FilePath = pViewName & "/" & pFile_Gid_Id & "/" & pFieldName '& "/" & FileName
		//        sFilePath = FilePath
		//        FilePath = FilePath & "/" & FileName

		//        ' ''Update the ADR_ field value
		//        'Dim rs = New clRowSet(pViewName, clC.SELL_EDIT, "GID_ID = '" & pFile_Gid_Id & "'", "", pFieldName, 1, "", "")
		//        Dim rs = New clRowSet(pViewName, clC.SELL_EDIT, "GID_ID = '" & pFile_Gid_Id & "'", "", pFieldName, 1, "", "", "", "", "", True, True)
		//        rs.ToTable()

		//        Dim FieldValueFromDB As String = rs.GetFieldVal(pFieldName)
		//        Dim NewValue As String = IIf(String.IsNullOrEmpty(FieldValueFromDB), FileName, FieldValueFromDB & "|" & FileName)

		//        rs.SetFieldVal(pFieldName, NewValue)

		//        Dim retval As Integer = rs.Commit()

		//        If retval = 1 Then

		//            Dim sFileExtension As String = ""
		//            Dim filesize As String = ""

		//            If pUploadFrom.ToLower() = "mobile" Then
		//                sFileExtension = System.IO.Path.GetExtension(pFileName)
		//                filesize = pFileData.Length.ToString()
		//            Else
		//                sFileExtension = System.IO.Path.GetExtension(pFileFullName)
		//                If System.IO.File.Exists(pFileFullName) Then
		//                    Dim info As New FileInfo(pFileFullName)
		//                    filesize = info.Length.ToString()
		//                End If
		//            End If

		//            retval = SaveAttachment(pViewName, pFile_Gid_Id, sFileExtension, filesize, FileName, FilePath, pFieldName, Source)

		//            If retval = 1 Then

		//                FilePath = GetRootPath() & FilePath
		//                sFilePath = GetRootPath() & sFilePath

		//                If Not Directory.Exists(sFilePath) Then
		//                    Directory.CreateDirectory(sFilePath)
		//                End If

		//                If File.Exists(FilePath) Then
		//                    File.Delete(FilePath)
		//                End If

		//                Dim data() As Byte

		//                If pUploadFrom.ToLower() = "mobile" Then
		//                    data = pFileData
		//                Else
		//                    data = File.ReadAllBytes(pFileFullName)
		//                End If

		//                File.WriteAllBytes(FilePath, data)

		//                Return True
		//            End If

		//        End If

		//        Return False

		//    Catch ex As Exception
		//        Throw ex
		//    End Try

		//End Function

		public bool UploadAttachment(string pViewName, string pFile_Gid_Id, string pFieldName, ArrayList pFiles)
		{

			//Try

			string Source = "Selltis";
			string sSourceFileName = "";
			string sSourceFilePath = "";
			string sDestinationFilePath = "";
			string sDestinationFileName = "";

			sDestinationFilePath = pViewName + "/" + pFile_Gid_Id + "/" + pFieldName + "/";


			for (var index = 0; index < pFiles.Count; index++)
			{
				sSourceFileName = string.IsNullOrEmpty(sSourceFileName) ? Path.GetFileName(pFiles[index].ToString()) : sSourceFileName + "|" + Path.GetFileName(pFiles[index].ToString());
			}

			// ''Update the ADR_ field value
			var rs = new clRowSet(pViewName, clC.SELL_EDIT, "GID_ID = '" + pFile_Gid_Id + "'", "", pFieldName, 1, "", "", "", "", "", true, true);
			rs.ToTable();

			string FieldValueFromDB = Convert.ToString(rs.GetFieldVal(pFieldName));
			string NewValue = string.IsNullOrEmpty(FieldValueFromDB) ? sSourceFileName : FieldValueFromDB + "|" + sSourceFileName;

			rs.SetFieldVal(pFieldName, NewValue);

			int retval = rs.Commit();

			if (retval == 1)
			{

				string sFileExtension = "";
				string filesize = "";

				string sDesFolderPath = GetRootPath() + sDestinationFilePath;

				if (!Directory.Exists(sDesFolderPath))
				{
					Directory.CreateDirectory(sDesFolderPath);
				}

				for (var index = 0; index < pFiles.Count; index++)
				{

					sSourceFileName = "";
					sSourceFilePath = "";
					sDestinationFileName = "";

					sSourceFilePath = pFiles[index].ToString();
					sSourceFileName = Path.GetFileName(sSourceFilePath);
					sFileExtension = Path.GetExtension(sSourceFilePath);
					if (System.IO.File.Exists(sSourceFilePath))
					{
						FileInfo info = new FileInfo(sSourceFilePath);
						filesize = info.Length.ToString();
					}

					sDestinationFileName = sDestinationFilePath + sSourceFileName;

					retval = SaveAttachment(pViewName, pFile_Gid_Id, sFileExtension, filesize, sSourceFileName, sDestinationFileName, pFieldName, Source);

					if (retval == 1)
					{

						sDestinationFileName = GetRootPath() + sDestinationFileName;

						if (File.Exists(sDestinationFileName))
						{
							File.Delete(sDestinationFileName);
						}

						byte[] data;

						data = File.ReadAllBytes(sSourceFilePath);

						File.WriteAllBytes(sDestinationFileName, data);

					}

				}

				return true;

			}

			return false;

			//Catch ex As Exception
			//    Throw ex
			//End Try

		}

		public bool UploadAttachment(string pViewName, string pFile_Gid_Id, string pFieldName, string pFileName, string pSFileData, string hostName = "")
		{

			//   Try

			string Source = "Selltis";
			string FileName = "";
			string FilePath = "";
			string sFilePath = "";
			byte[] pFileData = Convert.FromBase64String(pSFileData);

			FileName = pFileName;

			FilePath = pViewName + "/" + pFile_Gid_Id + "/" + pFieldName; //& "/" & FileName
			sFilePath = FilePath;
			FilePath = FilePath + "/" + FileName;

			// ''Update the ADR_ field value
			var rs = new clRowSet(pViewName, clC.SELL_EDIT, "GID_ID = '" + pFile_Gid_Id + "'", "", pFieldName, 1, "", "", "", "", "", true, true);
			rs.ToTable();

			string FieldValueFromDB = Convert.ToString(rs.GetFieldVal(pFieldName));
			string NewValue = string.IsNullOrEmpty(FieldValueFromDB) ? FileName : FieldValueFromDB + "|" + FileName;

			rs.SetFieldVal(pFieldName, NewValue);

			int retval = rs.Commit();

			if (retval == 1)
			{

				string sFileExtension = "";
				string filesize = "";

				sFileExtension = System.IO.Path.GetExtension(pFileName);
				filesize = pFileData.Length.ToString();

				//no need the file path here for azure file storage for mobile attachments..J
				FilePath = string.Empty;

				retval = SaveAttachment(pViewName, pFile_Gid_Id, sFileExtension, filesize, FileName, FilePath, pFieldName, Source);

				if (retval == 1)
				{

					string AttachmentsStorageType = System.Configuration.ConfigurationManager.AppSettings["AttachmentsStorageType"].ToString();
					if (AttachmentsStorageType == "Server")
					{
						FilePath = GetRootPath() + FilePath;
						sFilePath = GetRootPath() + sFilePath;

						if (!Directory.Exists(sFilePath))
						{
							Directory.CreateDirectory(sFilePath);
						}

						if (File.Exists(FilePath))
						{
							File.Delete(FilePath);
						}

						byte[] data;

						data = pFileData;

						File.WriteAllBytes(FilePath, data);
					}
					else
					{
						var finalDir = clAzureFileStorage.GetFinalFolder(pViewName, pFile_Gid_Id, pFieldName, "", false, hostName);
						clAzureFileStorage.DeleteFile(finalDir, FileName);
						MemoryStream stream = new MemoryStream(pFileData);
						clAzureFileStorage.UploadFromStream(finalDir, stream, FileName);
					}

					return true;

				}

			}

			return false;

			//Catch ex As Exception
			//    Throw ex
			//End Try

		}


		//azure file storage related code
		private void SaveAttachmentToDB_Azure(string tempid, string sFieldName, string sGID, string sViewName, string sfile, string AttachmentsTempPath, string AttachmentsPath, string Source)
		{

			string[] sfileinfo = null;
			string temppath = string.Empty;
			string rootpath = string.Empty;
			string sFileExtension = string.Empty;
			string sFilename = string.Empty;
			string filesize = "0";
			string sFileFullpath = string.Empty;
			clAttachments objclAttachments = new clAttachments();

			clTransform goTR;
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];

			sfileinfo = sfile.Split('~');

			sFilename = sfileinfo.GetValue(0).ToString();
			//temppath = AttachmentsTempPath & tempid & "\" & sFilename 'System.Web.HttpContext.Current.Server.MapPath(AttachmentsPath) & tempid & "\" & sFilename 
			//rootpath = AttachmentsPath & sViewName & "\" & sGID & "\" & sFieldName & "\" 'System.Web.HttpContext.Current.Server.MapPath(AttachmentsPath) & sViewName & "\" & sGID & "\" & sFieldName & "\"

			if (Source == "Selltis")
			{

				//If System.IO.File.Exists(temppath) Then

				//sFileExtension = System.IO.Path.GetExtension(temppath)
				//filesize = sfileinfo.GetValue(1).ToString()
				//sFileFullpath = sViewName & "/" & sGID & "/" & sFieldName & "/" & sFilename 'AttachmentsPath & sViewName & "/" & sGID & "/" & sFieldName & "/" & sFilename

				sFileExtension = System.IO.Path.GetExtension(sFilename);
				var TempFolder = clAzureFileStorage.GetFinalFolder("", "", "", tempid, true);

				if (string.IsNullOrEmpty(sFilename) == false)
				{
					filesize = clAzureFileStorage.GetFileSize(TempFolder, sFilename).ToString();
				}

				//If System.IO.File.Exists(temppath) Then
				//    Dim info As New FileInfo(temppath)
				//    filesize = info.Length.ToString()
				//End If

				int retval = 0;


				//To skip duplicate saving..J
				var checkexistrs = new clRowSet("AD", 3, "TXT_FILENAME='" + sViewName + "' AND TXT_FILEGIDID='" + sGID + "' AND TXT_ATTACHMENTEXTENSION='" + sFileExtension + "' AND TXT_AttachmentName='" + goTR.PrepareForSQL(sFilename) + "' AND TXT_ATTACHMENTDIRECTORYPATH='" + sFileFullpath + "' AND TXT_FIELDNAME='" + sFieldName + "' AND TXT_SOURCE='" + Source + "' ");
				if (checkexistrs.GetFirst() == 1)
				{
					retval = 1;
				}
				else
				{
					retval = SaveAttachment(sViewName, sGID, sFileExtension, filesize, sFilename, sFileFullpath, sFieldName, Source);
				}

				if (retval == 1) //copy the files to root directory
				{

					var FieldNameDir = clAzureFileStorage.GetFinalFolder(sViewName, sGID, sFieldName, "", false);

					if (string.IsNullOrEmpty(sFilename) == false && clAzureFileStorage.IsFileExists(TempFolder, sFilename))
					{
						//'copy file from temp folder to the field name directory
						clAzureFileStorage.CopyFile(TempFolder, FieldNameDir, sFilename);
					}

					//If Not System.IO.Directory.Exists(rootpath) Then
					//    System.IO.Directory.CreateDirectory(rootpath)
					//End If

					//'move the file from temp folder to root folder
					//Try
					//    If System.IO.File.Exists(temppath) Then
					//        If System.IO.File.Exists(rootpath & sFilename) Then
					//            System.IO.File.Delete(rootpath & sFilename)
					//            System.Threading.Thread.Sleep(500)
					//        End If
					//        System.IO.File.Copy(temppath, rootpath & sFilename)
					//        System.Threading.Thread.Sleep(1000)
					//    End If
					//Catch ex As Exception
					//End Try

				}

				//End If

			}
			else if (Source == "EFileCabinet") //'other than selltis
			{
				//tempid is the objectId
				SaveAttachment(sViewName, sGID, sFileExtension, filesize, sFilename, tempid, sFieldName, Source);
			}

		}

	}

	public class clEFCAttachments
	{

		public bool LogIntoEFC()
		{

			try
			{
				clMetaData goMeta = null;
				clProject goP = null;
				goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
				goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];

				string sUserName = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "EFILECABINET_USERNAME", "", true);
				string sPassword = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "EFILECABINET_PASSWORD", "", true);
				string sAppId = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "EFILECABINET_APPLICATIONID", "", true);


				clEncrypt2 o = new clEncrypt2();
				sPassword = o.Decrypt(sPassword);

				EfcOnlineApi.ApplicationId = sAppId;

				bool authenticated = false;
				AuthenticateToEfcOnlineAPI(sUserName, sPassword);

				if (!string.IsNullOrEmpty(EfcOnlineApi.AuthToken) && EfcOnlineApi.AuthToken.Length <= 37)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			catch (Exception ex)
			{
				return false;
			}

		}

		public void AuthenticateToEfcOnlineAPI(string username, string password)
		{
			EfcOnlineApi.GenerateUserAuthToken(username, password);
		}

		public List<EfcObject> GetEFCHierarchy(ref string sMessage)
		{

			clMetaData goMeta = null;
			clProject goP = null;
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];

			List<EfcObject> fulltree = new List<EfcObject>();
			List<EfcObject> cabinets = new List<EfcObject>();
			List<EfcObject> drawers = new List<EfcObject>();
			List<EfcObject> foldersAndFiles = new List<EfcObject>();
			List<EfcObject> Filesnew = new List<EfcObject>();
			List<EfcObject> Filesnewa = new List<EfcObject>();
			string FilePath = "";
			List<EfcObject> Files = new List<EfcObject>();
			sMessage = string.Empty;

			if (!LogIntoEFC())
			{
				sMessage = "EFileCabinet authentication failed!";
				return null;
			}

			cabinets = EfcOnlineApi.GetCabinets();

			foreach (EfcObject cab in cabinets)
			{
				if (cab.ParentEfcObjectId == -1)
				{
					cab.ParentEfcObjectId = 0;
				}
			}

			fulltree.AddRange(cabinets);

			if (cabinets.Count == 0)
			{
				sMessage = "No cabinets found";
			}

			return fulltree;

			//If cabinets.Count > 0 Then

			//    For index As Integer = 0 To 0 'cabinets.Count - 1

			//        'cabinets.Count - 1
			//        drawers = New List(Of EfcObject)()
			//        drawers = EfcOnlineApi.GetChildren(cabinets(index).EfcObjectId)
			//        fulltree.AddRange(drawers)

			//        If drawers.Count > 0 Then

			//            For indexd As Integer = 0 To drawers.Count - 1

			//                foldersAndFiles = New List(Of EfcObject)()
			//                foldersAndFiles = EfcOnlineApi.GetChildren(drawers(indexd).EfcObjectId)
			//                fulltree.AddRange(foldersAndFiles)

			//                If foldersAndFiles.Count > 0 Then
			//                    For indexf As Integer = 0 To foldersAndFiles.Count - 1
			//                        Files = New List(Of EfcObject)()
			//                        Files = EfcOnlineApi.GetChildren(foldersAndFiles(indexf).EfcObjectId)
			//                        fulltree.AddRange(Files)
			//                    Next
			//                End If

			//            Next
			//        End If

			//    Next

			//End If

			//Return fulltree

		}

		public List<EfcObject> GetFiles(int iEfcObjectId)
		{
			return EfcOnlineApi.GetChildren(iEfcObjectId);
		}

		public MemoryStream DownloadFile(int iEfcObjectId)
		{

			if (!IsEFCAuthenticated())
			{
				LogIntoEFC();
			}

			MemoryStream storeStream = new MemoryStream();
			long tempVar = 1024;
			storeStream = EfcOnlineApi.DownloadFileStream(iEfcObjectId, ref tempVar);
			return storeStream;

		}



		public bool IsEFCAuthenticated()
		{
			if (!string.IsNullOrEmpty(EfcOnlineApi.AuthToken) && EfcOnlineApi.AuthToken.Length <= 37)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

	}

}
