<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SMTP.Net</name>
    </assembly>
    <members>
        <member name="T:Quiksoft.EasyMail.Internal.MXLookup">
            <summary>
            Summary description for MXLookup.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.Node">
            <summary>
            Summary description for Node.
            </summary>
            
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.BST">
            <summary>
            Summary description for BinaryTree.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.TraversalMethods">
            <summary>
            Summary description for TraversalMethods.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.Message">
            <summary>
            Message Object, stores all message information.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Message.SaveMessage(System.IO.Stream)">
            <summary>
            Save message to file stream.
            </summary>
            <param name="pFileStream">File Stream</param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Message.SaveMessage(System.IO.Stream,System.Text.Encoding)">
            <summary>
            Saves a message to file stream.
            </summary>
            <param name="pFileStream">File Stream</param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Message.SendBody(Quiksoft.EasyMail.SMTP.ISMTPSSL,Quiksoft.EasyMail.Internal.SMTP.Status,System.Int64@,System.Int64)">
            <summary>
            Writes the body of the message to a stream.
            </summary>
            <param name="pIOControl"></param>
            <param name="pStatus"></param>
            <param name="nBytesSent"></param>
            <param name="nTotalBytes"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Message.SendAttachment(Quiksoft.EasyMail.Internal.Attachment,Quiksoft.EasyMail.SMTP.ISMTPSSL,Quiksoft.EasyMail.Internal.SMTP.Status,System.Int64@,System.Int64,System.Text.Encoding)">
            <summary>
            Writes attachment to stream doing any necessary encoding.
            </summary>
            <param name="pAttachment">Attachment to write</param>
            <param name="pIOControl">Stream</param>
            <param name="pStatus"></param>
            <param name="nBytesSent"></param>
            <param name="nTotalBytes"></param>
            <param name="CharsetEncoding"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Message.SendBodyPart(Quiksoft.EasyMail.Internal.BodyPart,Quiksoft.EasyMail.SMTP.ISMTPSSL,Quiksoft.EasyMail.Internal.SMTP.Status,System.Int64@,System.Int64)">
            <summary>
            Write encoded bodypart to a stream.
            </summary>
            <param name="pPart">Part to write</param>
            <param name="pIOControl">Stream</param>
            <param name="pStatus"></param>
            <param name="nBytesSent"></param>
            <param name="nTotalBytes"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Message.SendHeaders(Quiksoft.EasyMail.SMTP.ISMTPSSL,Quiksoft.EasyMail.Internal.SMTP.SMTP_SEND_PARAMS)">
            <summary>
            Sends the header to a socket or file
            </summary>
            <param name="pIOControl">IO stream to send to</param>
            <param name="pParams"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Message.GetAttachmentCount(System.Int32@)">
            <summary>
            Returns the number of attachments a message has.
            </summary>
            <param name="nNonRelatedCount"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Message.SendAddresses(Quiksoft.EasyMail.Internal.Addresses,Quiksoft.EasyMail.SMTP.ISMTPSSL,Quiksoft.EasyMail.Internal.AddressType,System.Text.Encoding)">
            <summary>
            Used to save or send an address.
            </summary>
            <param name="pAddresses">Address Object</param>
            <param name="pIOControl">IO Control object</param>
            <param name="nType">Type of address to use</param>
            <param name="CharsetEncoding"></param>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Message.ImportHTML(System.String,Quiksoft.EasyMail.Internal.SMTP.SMTPImportHTMLFlags,System.Text.Encoding)">
            <summary>
            Imports HTML file in to a message class.
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Message.SMIME">
            <summary>
            The SMIME interface to use for building this message.  The return value of <see cref="!:Quiksoft.EasyMail.SMIME.GetInterface"/>
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Message.BodyParts">
            <summary>
            Collection of body parts.
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Message.From">
            <summary>
            Collection of senders.  Normally only one is used.
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Message.Recipients">
            <summary>
            Recipients, To, CC, and BCC's
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Message.Attachments">
            <summary>
            Collection of Attachment Objects.
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Message.Priority">
            <summary>
            Priority of message.
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Message.CustomHeaders">
            <summary>
            Custom headers, should end with a CRLF
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Message.ReplyTo">
            <summary>
            Gets or sets the reply-to address of the e-mail message.
            </summary>
            <value>Sets the address for which replies to this message should be sent.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Message.ReturnReceiptTo">
            <summary>
            Gets or sets the return receipt address of the e-mail message.
            </summary>
            <value>Specifies an e-mail address to send receipts to.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Message.DispositionNotificationTo">
            <summary>
            Gets or sets the Disposition Notification address of the e-mail message.
            </summary>
            <value>Specifies an e-mail address to send Disposition NotificationTo to.</value>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.Message.HeaderOptions">
            <summary>
            Set the options that the SMTP Object should use when creating messages.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.Message.HeaderOptions.None">
            <summary>
            No options.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.Message.HeaderOptions.NoFrom">
            <summary>
            Do not add "From" header.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.Message.HeaderOptions.NoDate">
            <summary>
            Do not add "Date" header.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.Message.HeaderOptions.NoMessageID">
            <summary>
            Do not add "Message-ID" header.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.Message.HeaderOptions.NoRecipients">
            <summary>
            Do not add "To" header.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.Message.HeaderOptions.NoAuthInFrom">
            <summary>
            When this option is specified along with ESMTP authentication, the authentication account name will not be appended to the end of the MAIL FROM command. This option is to be used for servers which reject the MAIL FROM command when the authentication account name is appended.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.Message.HeaderOptions.AddBCC">
            <summary>
            Add the BCC header to the message.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.AddressType">
            <summary>
            Type of Address
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.AddressType.To">
            <summary>
            Standard To
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.AddressType.CC">
            <summary>
            Carbon copy.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.AddressType.BCC">
            <summary>
            Blind Carbon Copy
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.AddressType.From">
            <summary>
            Address is a From address
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.Recipients">
            <summary>
            Recipients collection object
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.Addresses">
            <summary>
            Addresses collection.  Holds multiple addresses
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.Collection">
            <summary>
            Summary description for Collection.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.Collection.pArray">
            <summary>
            Array that actually holds the collection objects
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.Collection.Position">
            <summary>
            Position at which collection is refferencing
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Collection.GetEnumerator">
            <summary>
            Returns the enumerator.
            </summary>
            <returns>Object</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Collection.MoveNext">
            <summary>
            Moves to next record
            </summary>
            <returns>true on success false if no more records</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Collection.Reset">
            <summary>
            Resets collection to first object
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Collection.GetLastItem">
            <summary>
            Return the last item in the collection
            </summary>
            <returns>Address object</returns>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Collection.Current">
            <summary>
            Returns current object
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Collection.Count">
            <summary>
            Get number of items in collection
            </summary>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Addresses.Add(Quiksoft.EasyMail.Internal.Address)">
            <summary>
            Add an address to the collection
            </summary>
            <param name="pAddress"></param>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Addresses.GetLastItem(Quiksoft.EasyMail.Internal.AddressType)">
            <summary>
            Returns the last item in the collection that is of a certain type.
            </summary>
            <param name="nType">Type of address to return</param>
            <returns>Address object</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Recipients.Add(System.String,System.String,Quiksoft.EasyMail.Internal.AddressType)">
            <summary>
            Add Address to collection
            </summary>
            <param name="strAddress">e-mail Address</param>
            <param name="strName">Friendly Name</param>
            <param name="nType">Type</param>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Recipients.Add(System.String,Quiksoft.EasyMail.Internal.AddressType)">
            <summary>
            Adds an address to the collection
            </summary>
            <param name="strAddress">e-mail Address</param>
            <param name="nType">Type</param>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Recipients.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:Quiksoft.EasyMail.SMTP.Recipient"/> object at the specified position.
            </summary>
            <param name="index">An ordinal index value that specifies which <see cref="T:Quiksoft.EasyMail.SMTP.Recipient"/> to return.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.Senders">
            <summary>
            Sender or FROM collection Object
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Senders.Add(System.String)">
            <summary>
            Adds an email address to the collection
            </summary>
            <param name="strEMailAddress">e-mail Address</param>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Senders.Add(System.String,System.String)">
            <summary>
            Adds and E-mail address to the collection
            </summary>
            <param name="strEMailAddress">E-Mail Address</param>
            <param name="strName">Friendly Name</param>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.Address">
            <summary>
            Address object.  Used to store e-mail addresses.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Address.#ctor(System.String,System.String,Quiksoft.EasyMail.Internal.AddressType)">
            <summary>
            Constructor that creates Address class.
            </summary>
            <param name="strAddress">Email Address</param>
            <param name="strName">User name</param>
            <param name="nType">Type of Address</param>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.Address.m_strAddress">
            <summary>
            Email Address member string
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.Address.m_strName">
            <summary>
            Email Name
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.Address.m_nType">
            <summary>
            Type of Address
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Address.EmailAddress">
            <summary>
            Email Address Property
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Address.Name">
            <summary>
            Email Friendly Name property
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Address.Type">
            <summary>
            Type of address. property
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.Attachments">
            <summary>
            Attachments collection stores attachment objects.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Attachments.Add(Quiksoft.EasyMail.Internal.Attachment)">
            <summary>
            Adds attachment object to collection
            </summary>
            <param name="pAttachment">Attachment Object</param>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.Attachment">
            <summary>
            Attachment Class
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.MimePart">
            <summary>
            Mime Part object
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.MimePart.Encoding">
            <summary>
            Encoding type to use
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.MimePart.Options">
            <summary>
            MimePart Options
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.MimePart.Headers">
            <summary>
            Custom headers to add to MIME part. Must end in CRLF.
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.MimePart.Stream">
            <summary>
            Data of message or FileName.
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.MimePart.Data">
            <summary>
            Data of message or FileName.
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.MimePart.ContentType">
            <summary>
            Content Type of part
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.MimePart.Length">
            <summary>
            Length of part.  Read only
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.Attachment.m_strName">
            <summary>
            Friendly Attachment Name
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.Attachment.Data">
            <summary>
            Attachment data or file name.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.BodyParts">
            <summary>
            Body Parts collection.  Contains all parts of the message.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.BodyParts.Add(Quiksoft.EasyMail.Internal.BodyPart)">
            <summary>
            Adds a body part to the collection
            </summary>
            <param name="pPart">Body Part object</param>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.BodyParts.Add(System.String)">
            <summary>
            Adds a body part to the collection
            </summary>
            <param name="Body">Bodypart text.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.MIMEEncoding">
            <summary>
            Mime encoding used on Mime entity.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.MIMEEncoding.None">
            <summary>
            No encoding used
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.MIMEEncoding.Base64">
            <summary>
            Use Base64
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.MIMEEncoding.QuotedPrintable">
            <summary>
            Use Quoted Printable
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.MIMEEncoding.EightBit">
            <summary>
            8Bit 
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.BodyPart">
            <summary>
            Body Part object
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.BodyPart.CharsetEncoding">
            <summary>
            Charset used.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.HTMLClass">
            <summary>
            Summary description for Class1.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.RecipientType">
            <summary>
            Provides enumerated values for the recipient type.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.RecipientType.To">
            <summary>
            Standard to address.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.RecipientType.CC">
            <summary>
            Carbon copy.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.RecipientType.BCC">
            <summary>
            Blind carbon copy.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.SMTPAuthMode">
            <summary>
            Provides enumerated values for ESMTP Authentication.
            </summary>
            <remarks>ESMTP authentication is an extension to the SMTP protocol which enables a mail server to authenticate the mail sender.</remarks>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.SMTPAuthMode.None">
            <summary>
            No Authentication.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.SMTPAuthMode.AuthLogin">
            <summary>
            Use Auth Login.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.SMTPAuthMode.CramMD5">
            <summary>
            Use Cram-MD5.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.SMTPAuthMode.SASL">
            <summary>
            The <see cref="P:Quiksoft.EasyMail.SMTP.SMTPServer.SASL"/> property is set and the assembly should use this for authentication.
            </summary>
            <remarks>If you set use this value then you must set the <see cref="P:Quiksoft.EasyMail.SMTP.SMTPServer.SASL"/> property to a valid <see cref="T:Quiksoft.EasyMail.SMTP.ISMTPSASL"/> interface.</remarks>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.ImportURIFlags">
            <summary>
            Additional flags that specify how a URI should be imported.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ImportURIFlags.None">
            <summary>
            No additional flags specified.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ImportURIFlags.EmbedImages">
            <summary>
            Download images and embed them in to the message.
            </summary>
            <remarks>This allows the user to view HTML messages offline because all the images are included within the message.</remarks>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ImportURIFlags.CreateAlternative">
            <summary>
            Crate an alternative text representation.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.ISMTPSASL">
            <summary>
            Used to implement a Simple Authentication and Security Layer (SASL) Interface.
            </summary>
            <remarks>
            The SASL interface can be used to add custom authentication mechanisms to the EasyMail assemblies.
            Any class that inherits from this interface will be referred to as a SASL mechanism.
            The Simple Authentication and Security Layer (SASL) is a method for
            adding authentication support to connection-based protocols.  To use
            this specification, a protocol includes a command for identifying and
            authenticating a user to a server and for optionally negotiating a
            security layer for subsequent protocol interactions.
            A list of currently registered SASL mechanisms can be found at the below URL.
            http://www.iana.org/assignments/sasl-mechanisms
            </remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSASL.Init(System.String,System.String)">
            <summary>
            Initializes the SASL mechanism.
            </summary>
            <param name="Account">Account name of user that is logging in.</param>
            <param name="Password">Password of user that is logging in.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSASL.GetMechanismName">
            <summary>
            Gets the name of the mechanism.  
            </summary>
            <returns>Name of mechanism,</returns>
            <remarks>Must be from 1 to 20 characters in	length, 
            consisting of upper-case letters, digits, hyphens, and/or
            underscores
            </remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSASL.GetMechanismCommand(System.Int32)">
            <summary>
            Gets the command to initiate the authentication with the server.
            </summary>
            <param name="Protocol">The protocol which authentication is be requested for.<BR/>
            1-SMTP<BR/>
            2-POP3<BR/>
            3-IMAP4<BR/>
            </param>
            <returns>A string containing the command to send to the server.</returns>
            <remarks>
            The default SASL command for POP3 and SMTP is "AUTH" and the default for IMAP4 is "AUTHENTICATE"</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSASL.DataAvailable(System.String)">
            <summary>
            Called when data is available from the server.
            </summary>
            <param name="Data">Data received from the connection.</param>
            <returns>The response to send back to the server. 
            The mechanism should return a null or empty value if no data is to be sent and the authentication was successful.<br/>
            If an error occurs the mechanism should throw an exception that
            will propagate the back to the caller. <br/>
            <br/>
            </returns>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.SMTPServer">
            <summary>
            Provides properties and methods for an SMTP mail server that will be used when sending e-mail messages.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPServer.#ctor(System.String,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServer">SMTPServer class</see> with the name, port number and timeout value for the SMTP server.
            </summary>
            <param name="Name">Name or address of server</param>
            <param name="Port">Port to connect to.</param>
            <param name="Timeout">The timeout value in seconds.  See remarks.</param>
            <remarks>The Timeout parameter sets the maximum amount of time that the component will wait for a response from the SMTP server, before returning an error. 
            Some devices running the Compact Framework may not support the setting of a timeout.  If you are targeting the Compact Framework you should 
            not use this overload.  See the <see cref="P:Quiksoft.EasyMail.SMTP.SMTPServer.Timeout"/> property for more information.
            </remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPServer.#ctor(System.String,System.Int32,System.Int32,Quiksoft.EasyMail.SMTP.SMTPAuthMode,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServer">SMTPServer class</see> with the name, port number and timeout value for the SMTP server.
            </summary>
            <param name="Name">Name or address of server</param>
            <param name="Port">Port to connect to.</param>
            <param name="Timeout">The timeout value in seconds.  See remarks.</param>
            <param name="AuthMode">Authentication Mode.</param>
            <param name="Account">Name of the authentication account.</param>
            <param name="Password">Password of the authentication account.</param>
            <remarks>The Timeout parameter sets the maximum amount of time that the component will wait for a response from the SMTP server, before returning an error. 
            Some devices running the Compact Framework may not support the setting of a timeout.  If you are targeting the Compact Framework you should 
            not use this overload.  See the <see cref="P:Quiksoft.EasyMail.SMTP.SMTPServer.Timeout"/> property for more information.
            </remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPServer.#ctor(System.String,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServer">SMTPServer Class</see> with the name and port number of the SMTP server.
            </summary>
            <param name="Name">Name or address of server</param>
            <param name="Port">Port to connect to.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPServer.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServer">SMTPServer Class</see> with the SMTP server name.
            </summary>
            <param name="Name">Name or address of server</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPServer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServer">SMTPServer Class</see>
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTPServer.SASL">
            <summary>
            An optional <see cref="T:Quiksoft.EasyMail.SMTP.ISMTPSASL"/> interface used for custom authentication.
            </summary>
            <value>An <see cref="T:Quiksoft.EasyMail.SMTP.ISMTPSASL"/> interface.</value>
            <remarks>To use this value you must set the <see cref="P:Quiksoft.EasyMail.SMTP.SMTPServer.AuthMode"/> property to <see cref="!:AuthMode.SASL"/>.</remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTPServer.Name">
            <summary>
            Gets or sets the name or address of the SMTP server.
            </summary>
            <value>Name or address of the SMTP server</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTPServer.Port">
            <summary>
            Gets or sets the port of the SMTP Server.
            </summary>
            <value>Port to connect to.  Default: 25.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTPServer.LocalEndPoint">
            <summary>
            Gets or sets an optional local endpoint to use when sending messages.
            </summary>
            <remarks>Represents a network endpoint as an IP address and a port number.
            Multihomed hosts can use this property to specify which adapter/IP should be used when creating the connection.  To have an outgoing port automatically assigned use 0 as the port number.
            This property is currently not support when using SSL or Direct Send.</remarks>
            <example>
            smtp.SMTPServers.Add("mail.domain.com");
            smtp.SMTPServers[0].LocalEndPoint = new IPEndPoint(IPAddress.Parse("**********"),0);
            </example>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTPServer.Timeout">
            <summary>
            Gets or sets the timeout value.
            </summary>
            <value>The connection time out value in seconds.  The default value is system dependant.  See remarks.</value>
            <remarks>Sets the maximum amount of time that the component will wait for a response from the SMTP server, before returning an error. 
            Some devices running the Compact Framework may not support the setting of a timeout.  If you are targeting the Compact Framework
            You should check the default value of this property prior to setting it.  If the default value is less than zero,
            then setting the timeout is not supported and any attempts to do so will result in a SocketException being thrown.
            </remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTPServer.AuthMode">
            <summary>
            Gets or sets the authentication mode.
            </summary>
            <value>One of the <see cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthMode">AuthMode</see> values specifying the Authentication mode.  Default: STMPAuthMode.None.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTPServer.Account">
            <summary>
            Gets or sets the name of the user authentication account.
            </summary>
            <value>The name of the user account to be authenticated on the e-mail server.</value>
            <remarks>This property is NOT required to send mail unless you are using authentication. If the <see cref="P:Quiksoft.EasyMail.SMTP.SMTPServer.AuthMode"/> property is set to AuthMode.None (default) this property is ignored.</remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTPServer.Password">
            <summary>
            Gets or sets the password of the authentication account.
            </summary>
            <value>The password of the user account to be authenticated on the e-mail server.</value>
            <remarks>This property is NOT required to send mail unless you are using authentication. If the <see cref="P:Quiksoft.EasyMail.SMTP.SMTPServer.AuthMode"/> property is set to AuthMode.None (default) this property is ignored.</remarks>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.SMTPServerCollection">
            <summary>
            Represents a collection of <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServer">SMTPServer</see> objects.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.Collection">
            <summary>
            An abstract class for a strongly typed collection.
            </summary>
            <remarks>This class is abstract.  It is inherited by the other collection classes and can not be instantiated directly.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Collection.#ctor">
            <summary>
            Initializes a new instance of the Collection class.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Collection.Add(System.Object)">
            <summary>
            Adds the specified object to the end of the Collection.
            </summary>
            <param name="obj">The object to add.</param>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.Collection.pArray">
            <summary>
            Array that actually holds the collection objects
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.Collection.Position">
            <summary>
            Position at which collection is referencing
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Collection.GetEnumerator">
            <summary>
            Returns the enumerator.
            </summary>
            <returns>Object</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Collection.MoveNext">
            <summary>
            Moves to the next record.
            </summary>
            <returns>Returns true on success false if no more records are available.</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Collection.Remove(System.Object)">
            <summary>
            Removes the first occurrence of a specific object from the collection.
            </summary>
            <param name="Obj">The object to remove from the collection.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Collection.Reset">
            <summary>
            Resets the index of the collection to the first object.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Collection.Clear">
            <summary>
            Removes all objects from the collection.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Collection.GetLastItem">
            <summary>
            Return the last item in the collection.
            </summary>
            <returns>A reference to the last object in the collection.</returns>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.Collection.Current">
            <summary>
            Returns current object
            </summary>
            <value>Current object in the collection</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.Collection.Count">
            <summary>
            Gets the number of items in collection.
            </summary>
            <value>Number of items in the collection</value>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPServerCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServerCollection">SMTPServerCollection class</see> 
            </summary>
            <remarks>Add multiple SMTPServer objects to this collection for failover delivery.  Each server will be tried in the order it was added to the collection until a connection is made.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPServerCollection.Add(System.String)">
            <summary>
            Creates and adds a <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServer">SMTPServer object</see> to the collection.
            </summary>
            <param name="Name">Specifies the hostname or IP address of the SMTP server.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPServerCollection.Add(System.String,System.Int32)">
            <summary>
            Adds a MailServer class to the collection.
            </summary>
            <param name="Name">Name of Mail Server</param>
            <param name="Port">Port number to connect to.  Default 25.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPServerCollection.Add(System.String,System.Int32,System.Int32)">
            <summary>
            Adds a MailServer class to the collection.
            </summary>
            <param name="Name">Name of Mail Server</param>
            <param name="Port">Port number to connect to.  Default 25.</param>
            <param name="Timeout">The timeout value in seconds.  See remarks.</param>
            <remarks>The Timeout parameter sets the maximum amount of time that the component will wait for a response from the SMTP server, before returning an error. 
            Some devices running the Compact Framework may not support the setting of a timeout.  If you are targeting the Compact Framework you should 
            not use this overload.  See the <see cref="!:Timeout"/> property for more information.
            </remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPServerCollection.Add(System.String,System.Int32,System.Int32,Quiksoft.EasyMail.SMTP.SMTPAuthMode,System.String,System.String)">
            <summary>
            Adds a MailServer class to the collection.
            </summary>
            <param name="Name">Name of Mail Server</param>
            <param name="Port">Port number to connect to.  (Port 25 is the standard port assigned to SMTP).</param>
            <param name="Timeout">The timeout value in seconds.  See remarks.</param>
            <param name="AuthMode">An <see cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthMode"/> value.</param>
            <param name="Account">Name of the authentication account..</param>
            <param name="Password">Password of the authentication account.</param>
            <remarks>The Timeout parameter sets the maximum amount of time that the component will wait for a response from the SMTP server, before returning an error. 
            Some devices running the Compact Framework may not support the setting of a timeout.  If you are targeting the Compact Framework you should 
            not use this overload.  See the <see cref="!:Timeout"/> property for more information.
            </remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPServerCollection.Add(Quiksoft.EasyMail.SMTP.SMTPServer)">
            <summary>
            Adds a <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServer">SMTPServer object</see> to the collection.
            </summary>
            <param name="SMTPServerObj">A reference to an <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServer"/> object to add to the collection.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTPServerCollection.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServer"/> object at the specified position.
            </summary>
            <param name="index">An ordinal index value that specifies which <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServer"/> to return.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.Address">
            <summary>
            Provides properties and methods for constructing an e-mail Address.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Address.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.Address">Address class</see> with an e-mail address and name.
            </summary>
            <param name="Email">The e-mail address.  i.e. <EMAIL></param>
            <param name="Name">The name of the addressee.  i.e. John Doe</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Address.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.Address">Address class</see> with an e-mail address.
            </summary>
            <param name="Email">The e-mail address.  i.e. <EMAIL></param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Address.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.Address">Address class</see>
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.Address.strAddress">
            <summary>
            Member variable that holds the e-mail Address.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.Address.strName">
            <summary>
            Holds that 
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.Address.Email">
            <summary>
            Gets and sets the e-mail address.
            </summary>
            <value>The e-mail address.  i.e. <EMAIL></value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.Address.Name">
            <summary>
            Gets and sets the name of the addressee.
            </summary>
            <value>The name of the addressee.  i.e. John Doe</value>
            <remarks>This property is optional. If this value is specified, the receiving application may use it to display the addressee's name instead of or in addition to their e-mail address.</remarks>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.Recipient">
            <summary>
            Provides properties and methods for constructing an e-mail recipient.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Recipient.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.Recipient">Recipient class</see>
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Recipient.#ctor(System.String,System.String,Quiksoft.EasyMail.SMTP.RecipientType)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.Recipient">Recipient class</see> with an e-mail address, name and type. 
            </summary>
            <param name="Email">The e-mail address.  i.e. <EMAIL></param>
            <param name="Name">The name of the recipient.  i.e. John Doe</param>
            <param name="Type">Type of recipient. Must be one of the <see cref="T:Quiksoft.EasyMail.SMTP.RecipientType">RecipientType</see> values.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.Recipient.Type">
            <summary>
            Gets or sets the e-mail address type of the recipient.
            </summary>
            <value>One of the <see cref="T:Quiksoft.EasyMail.SMTP.RecipientType">RecipientType</see> values.  Default: RecipientType.To</value>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.RecipientCollection">
            <summary>
            Represents a collection of <see cref="T:Quiksoft.EasyMail.SMTP.Recipient">Recipient</see> objects.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.RecipientCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.RecipientCollection">RecipientCollection class</see>.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.RecipientCollection.Add(Quiksoft.EasyMail.SMTP.Recipient)">
            <summary>
            Adds a recipient object to the collection.
            </summary>
            <param name="RecipientObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.Recipient">Recipient</see> object to add to the collection.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.RecipientCollection.Add(System.String)">
            <summary>
            Creates a <see cref="T:Quiksoft.EasyMail.SMTP.Recipient">Recipient</see> object using specified values and adds it to the <see cref="T:Quiksoft.EasyMail.SMTP.RecipientCollection">RecipientCollection</see>. 
            </summary>
            <param name="Email">The recipient's e-mail address.  i.e. <EMAIL></param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.RecipientCollection.CleanAddressName(System.String)">
            <summary>
            Removes and trims any illegal characters from address name.
            </summary>
            <param name="strName">Name to clean</param>
            <returns>Cleaned address.</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.RecipientCollection.CleanAddress(System.String)">
            <summary>
            Removes and trims any illegal characters from an e-mail address.
            </summary>
            <param name="strAddress">Email Address</param>
            <returns>Cleaned address as string.</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.RecipientCollection.Parse(System.String,Quiksoft.EasyMail.SMTP.RecipientType)">
            <summary>
            Parses a list of email addresses and adds them to the collection.
            </summary>
            <param name="Addresses">A semicolon-delimited list of e-mail addresses that should be parsed. </param>
            <param name="Type">One of the <see cref="T:Quiksoft.EasyMail.SMTP.RecipientType">RecipientType</see> values.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.RecipientCollection.Add(System.String,System.String)">
            <summary>
            Creates a <see cref="T:Quiksoft.EasyMail.SMTP.Recipient">Recipient</see> object using specified values and adds it to the <see cref="T:Quiksoft.EasyMail.SMTP.RecipientCollection">RecipientCollection</see>. 
            </summary>
            <param name="Email">The recipient's e-mail address.  i.e. <EMAIL></param>
            <param name="Name">The name of the recipient.  i.e. John Doe</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.RecipientCollection.Add(System.String,System.String,Quiksoft.EasyMail.SMTP.RecipientType)">
            <summary>
            Creates a <see cref="T:Quiksoft.EasyMail.SMTP.Recipient">Recipient</see> object using specified values and adds it to the <see cref="T:Quiksoft.EasyMail.SMTP.RecipientCollection">RecipientCollection</see>. 
            </summary>
            <param name="Email">The recipient's e-mail address.  i.e. <EMAIL></param>
            <param name="Name">The name of the recipient.  i.e. John Doe</param>
            <param name="Type">One of the <see cref="T:Quiksoft.EasyMail.SMTP.RecipientType">RecipientType</see> values.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.RecipientCollection.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:Quiksoft.EasyMail.SMTP.Recipient"/> object at the specified position.
            </summary>
            <param name="index">An ordinal index value that specifies which <see cref="T:Quiksoft.EasyMail.SMTP.Recipient"/> to return.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.AttachmentEncoding">
            <summary>
            Provides enumerated values for e-mail attachment encoding.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.AttachmentEncoding.None">
            <summary>
            Specifies that no encoding is to be used on the attachment.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.AttachmentEncoding.Base64">
            <summary>
            Specifies that Base64 encoding is to be used on the attachment.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.AttachmentEncoding.QuotedPrintable">
            <summary>
            Specifies that Quoted-Printable encoding is to be used on the attachment.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.AttachmentEncoding.EightBit">
            <summary>
            Specifies that the attachment data is in 8bit format.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.Attachment">
            <summary>
            Provides properties and methods for constructing an e-mail attachment.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Attachment.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.Attachment">Attachment class</see>.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Attachment.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.Attachment">Attachment class</see> with the attachment filename.
            </summary>
            <param name="Filename">Name of the file to attach</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Attachment.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.Attachment">Attachment class</see> with the attachment filename and display name.
            </summary>
            <param name="Filename">Name of the file to attach</param>
            <param name="DisplayName">Filename used to label the attachment in the e-mail message.  See the <see cref="P:Quiksoft.EasyMail.SMTP.Attachment.DisplayName">DisplayName Property</see> for more information.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.Attachment.#ctor(System.IO.Stream,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.Attachment">Attachment class</see> with the attachment stream and display name.
            </summary>
            <param name="Stream">A stream object that contains the attachment.</param>
            <param name="DisplayName">Filename used to label the attachment in the e-mail message.  See the <see cref="P:Quiksoft.EasyMail.SMTP.Attachment.DisplayName">DisplayName Property</see> for more information.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.Attachment.Stream">
            <summary>
            Gets and sets the attachment stream.
            </summary>
            <value>A stream object that is available for reading.  When using a file name this value should be left null.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.Attachment.Filename">
            <summary>
            Gets and sets the filename of the attachment.
            </summary>
            <value>The full path and filename of the attachment.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.Attachment.DisplayName">
            <summary>
            Gets and sets the filename used to label the attachment in the e-mail message.  See remarks.
            </summary>
            <value>The filename used to label the attachment in the e-mail message.  See remarks.</value>
            <remarks>The DisplayName property allows you to label the attachment in the e-mail message with a name that is different than the original filename.  This is useful if you are adding an attachment with a temporary file name such as 03230234.dat and want it to be labeled in the message by a more friendly name such as budget.xls.  If the DisplayName property is not specified, the attachment will be labeled in the message with its original filename.</remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.Attachment.CustomHeaders">
            <summary>
            Gets the <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeaderCollection">CustomHeaderCollection</see> object.
            </summary>
            <value>A <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeaderCollection"/> object.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.Attachment.MimeFlags">
            <summary>
            Gets or sets the MIMEHeaderFlags to use when attaching the file. 
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.Attachment.Encoding">
            <summary>
            Gets or sets the encoding method to use on the attachment.
            </summary>
            <value>One of the <see cref="T:Quiksoft.EasyMail.SMTP.AttachmentEncoding">AttachmentEncoding</see> values specifying the encoding method to use on the attachment.  Default: AttachmentEncoding.Base64.</value>
            <exception cref="T:System.ArgumentException">Thrown when an invalid value is specified.</exception>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.AttachmentCollection">
            <summary>
            Represents a collection of <see cref="T:Quiksoft.EasyMail.SMTP.Attachment">Attachment</see> objects.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.AttachmentCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.AttachmentCollection">AttachmentCollection class</see>
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.AttachmentCollection.Add(Quiksoft.EasyMail.SMTP.Attachment)">
            <summary>
            Adds an attachment object to the collection.
            </summary>
            <param name="AttachmentObj">Attachment object to add to the collection.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.AttachmentCollection.Add(System.String)">
            <summary>
            Creates an <see cref="T:Quiksoft.EasyMail.SMTP.Attachment">Attachment</see> object using specified values and adds it to the <see cref="T:Quiksoft.EasyMail.SMTP.AttachmentCollection">AttachmentCollection</see>. 
            </summary>
            <param name="Filename">The full path and filename of the attachment to add to the collection.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.AttachmentCollection.Add(System.IO.Stream,System.String)">
            <summary>
            Creates an <see cref="T:Quiksoft.EasyMail.SMTP.Attachment">Attachment</see> object using specified values and adds it to the <see cref="T:Quiksoft.EasyMail.SMTP.AttachmentCollection">AttachmentCollection</see>. 
            </summary>
            <param name="Stream">A stream object which contains the attachment to add to the collection.</param>
            <param name="DisplayName">Filename used to identify the attachment in the e-mail message.  See the <see cref="P:Quiksoft.EasyMail.SMTP.Attachment.DisplayName">DisplayName Property</see> for more information.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.AttachmentCollection.Add(System.String,System.String)">
            <summary>
            Creates an <see cref="T:Quiksoft.EasyMail.SMTP.Attachment">Attachment</see> object using specified values and adds it to the <see cref="T:Quiksoft.EasyMail.SMTP.AttachmentCollection">AttachmentCollection</see>. 
            </summary>
            <param name="Filename">The full path and filename of the attachment to add to the collection.</param>
            <param name="DisplayName">Filename used to identify the attachment in the e-mail message.  See the <see cref="P:Quiksoft.EasyMail.SMTP.Attachment.DisplayName">DisplayName Property</see> for more information.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.AttachmentCollection.AddFromURI(System.String,System.String)">
            <summary>
            Creates an <see cref="T:Quiksoft.EasyMail.SMTP.Attachment">Attachment</see> object using specified values and adds it to the <see cref="T:Quiksoft.EasyMail.SMTP.AttachmentCollection">AttachmentCollection</see>. 
            </summary>
            <param name="URI">The URI of the attachment to add.</param>
            <param name="DisplayName">Filename used to identify the attachment in the e-mail message.  See the <see cref="P:Quiksoft.EasyMail.SMTP.Attachment.DisplayName">DisplayName Property</see> for more information.</param>
            <remarks>The attached URI is imported to a stream object at the time of calling this method.
            You may set the GlobalProxySelection object if you wish to use a proxy server to import this data.
            </remarks>
            <exception cref="T:System.Net.WebException">Thrown if there is a problem downloading the attachment.</exception>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.AttachmentCollection.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:Quiksoft.EasyMail.SMTP.Attachment"/> object at the specified position.
            </summary>
            <param name="index">An ordinal index value that specifies which <see cref="T:Quiksoft.EasyMail.SMTP.Attachment"/> to return.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.CustomHeader">
            <summary>
            Provides properties and methods for adding a custom header to an e-mail message.
            </summary>
            <remarks>Custom headers can be used to specify additional information in the message, such as flags or instructions to be interpreted by the receiving application.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.CustomHeader.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeader">CustomHeader class</see>
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.CustomHeader.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeader">CustomHeader class</see> with a name and value.
            </summary>
            <param name="Name">Name of the header.</param>
            <param name="Value">Value of the header.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.CustomHeader.#ctor(System.String,System.String,System.Text.Encoding)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeader">CustomHeader class</see> and encodes the value with the specified System.Text.Encoding object. 
            </summary>
            <param name="Name">Name of the header.</param>
            <param name="Value">Value of the header.</param>
            <param name="CharsetEncoding">The Encoding object to use to encode the header value.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.CustomHeader.Name">
            <summary>
            Gets and sets the name of the header.
            </summary>
            <value>Name of the header.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.CustomHeader.Value">
            <summary>
            Gets or sets the value of the header.
            </summary>
            <value>Value of the header.</value>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.CustomHeaderCollection">
            <summary>
            Represents a collection of <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeader">CustomHeader</see> objects.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.CustomHeaderCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeaderCollection">CustomHeaderCollection class</see>. 
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.CustomHeaderCollection.Add(Quiksoft.EasyMail.SMTP.CustomHeader)">
            <summary>
            Adds a <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeader">CustomHeader</see> object to the <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeaderCollection">CustomHeaderCollection</see>. 
            </summary>
            <param name="CustomHeaderObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeader">CustomHeader</see> object to add to the collection.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.CustomHeaderCollection.Add(System.String,System.String)">
            <summary>
             Creates a <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeader">CustomHeader</see> object using the specified values and adds it to the <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeaderCollection">CustomHeaderCollection</see>. 
            </summary>
            <param name="Name">Name of the header.</param>
            <param name="Value">Value of the header.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.CustomHeaderCollection.Add(System.String,System.String,System.Text.Encoding)">
            <summary>
             Creates a <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeader">CustomHeader</see> object using the specified values, encodes the value and adds it to the <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeaderCollection">CustomHeaderCollection</see>. 
            </summary>
            <param name="Name">Name of the header.</param>
            <param name="Value">Value of the header.</param>
            <param name="CharsetEncoding">The Encoding object to use to encode the header value.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.CustomHeaderCollection.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeader"/> object at the specified position.
            </summary>
            <param name="index">An ordinal index value that specifies which <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeader"/> to return.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.BodyPart">
            <summary>
            Provides properties and methods for constructing a body part of an e-mail message.
            </summary>
            <remarks>The body text is the main body of the message.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPart.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPart">BodyPart class</see> with a body.
            </summary>
            <param name="Body">The text of the body part.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPart.#ctor(System.String,Quiksoft.EasyMail.SMTP.BodyPartFormat)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPart">BodyPart class</see> with a body and its format.
            </summary>
            <param name="Body">The text of the body part</param>
            <param name="Format">One of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartFormat">BodyPartFormat</see> values specifying the format of the body part</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPart.#ctor(System.String,Quiksoft.EasyMail.SMTP.BodyPartFormat,Quiksoft.EasyMail.SMTP.BodyPartEncoding)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPart">BodyPart class</see> with a body, its format and encoding.
            </summary>
            <param name="Body">The text of the body part</param>
            <param name="Format">One of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartFormat">BodyPartFormat</see> values specifying the format of the body part</param>
            <param name="Encoding">One of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartEncoding">BodyPartEncoding</see> values specifying the encoding method to use on the body part</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPart.AppendWebUIControl(System.Web.UI.Control)">
            <summary>
            Appends a System.Web.UI.Control to the body of the message as HTML.
            </summary>
            <param name="UIControl">The UI control you wish to append to the body.</param>
            <remarks>The <see cref="P:Quiksoft.EasyMail.SMTP.BodyPart.Format"/> property must be set to <see cref="F:Quiksoft.EasyMail.SMTP.BodyPartFormat.HTML"/> for the control to render properly.
            Any control which inherits from System.Web.UI.Control can be used.  This includes custom controls as well.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPart.ImportFromURI(System.String,System.String)">
            <summary>
            Imports and sets the <see cref="P:Quiksoft.EasyMail.SMTP.BodyPart.Body"/> property with the downloaded contents of the specified uniform resource identifier (URI).
            To insure the BodyPart is sent correctly, the <see cref="P:Quiksoft.EasyMail.SMTP.BodyPart.Encoding"/> property is automatically set to <see cref="F:Quiksoft.EasyMail.SMTP.BodyPartEncoding.QuotedPrintable"/> and the
            <see cref="P:Quiksoft.EasyMail.SMTP.BodyPart.Format"/> property is automatically set to <see cref="F:Quiksoft.EasyMail.SMTP.BodyPartFormat.HTML"/>.
            </summary>
            <remarks>
            <B>This method should no longer be used, it has been replace with <see cref="M:Quiksoft.EasyMail.SMTP.EmailMessage.ImportFromURI(System.String,System.String,Quiksoft.EasyMail.SMTP.ImportURIFlags)">EMailMessage.ImportFromURI</see>.</B>
            </remarks>
            <param name="URI">The Uri of the requested resource.</param>
            <param name="Base">The base tag that should be the prefixed for all Uri's with relative references.
            i.e.
            Base = "http://www.quiksoft.com/"
            img src="/banner.gif"
            then   
            img src="http://www.quiksoft.com/banner.gif";
            </param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPImportException">Thrown when an error occurs importing a bodypart. See <see cref="T:Quiksoft.EasyMail.SMTP.ImportErrors"/> for detailed error codes.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPart.ConvertHTMLToAlternativeText(System.IO.Stream)">
            <summary>
            Converts an HTML stream to its plain text representation.
            </summary>
            <param name="HTML">A <see cref="T:System.IO.Stream"/> object that contains the html to convert.</param>
            <returns>A string containing the alternative text version of the HTML.</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPart.ConvertHTMLToAlternativeText(System.IO.Stream,System.Text.Encoding)">
            <summary>
            Converts an HTML stream to its plain text representation.
            </summary>
            <param name="HTML">A <see cref="T:System.IO.Stream"/> object that contains the html to convert.</param>
            <param name="CharsetEncoding">A <see cref="T:System.Text.Encoding"/> object that specifies the character encoding of stream.
            Default: iso-8859-1</param>
            <returns>A string containing the alternative text version of the HTML.</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPart.ConvertHTMLToAlternativeText(System.String)">
            <summary>
            Converts an HTML string to a plain text representation.
            </summary>
            <param name="HTML">A string that contains the html to convert.</param>
            <returns>A string containing the alternative text version of the HTML.</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPart.ImportFromURI(System.String,System.String,Quiksoft.EasyMail.SMTP.ImportURIFlags,Quiksoft.EasyMail.SMTP.EmailMessage)">
            <summary>
            Imports and sets the <see cref="P:Quiksoft.EasyMail.SMTP.BodyPart.Body"/> property with the downloaded contents of the specified uniform resource identifier (URI).
            To insure the BodyPart is sent correctly, the <see cref="P:Quiksoft.EasyMail.SMTP.BodyPart.Encoding"/> property is automatically set to <see cref="F:Quiksoft.EasyMail.SMTP.BodyPartEncoding.QuotedPrintable"/> and the
            <see cref="P:Quiksoft.EasyMail.SMTP.BodyPart.Format"/> property is automatically set to <see cref="F:Quiksoft.EasyMail.SMTP.BodyPartFormat.HTML"/>.
            </summary>
            <param name="URI">The Uri of the requested resource.</param>
            <param name="Base">The base tag that should be the prefixed for all Uri's with relative references.
            <param name="Options">Additional options that determine how the URI should be imported.</param>
            <param name="Message">The message object you are importing to, which is needed to embed HTML images.</param>
            i.e.
            Base = "http://www.quiksoft.com/"
            img src="/banner.gif"
            then   
            img src="http://www.quiksoft.com/banner.gif";
            </param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPImportException">Thrown when an error occurs importing a bodypart. See <see cref="T:Quiksoft.EasyMail.SMTP.ImportErrors"/> for detailed error codes.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPart.#ctor">
            <summary>
            Initializes a new instance of the BodyPart class.
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.BodyPart.Body">
            <summary>
            Gets or sets the text of the body part. 
            </summary>
            <value>The text of the body part.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.BodyPart.Format">
            <summary>
            Gets or sets the format of the body part.
            </summary>
            <value>One of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartFormat">BodyPartFormat</see> values specifying the format of the body part.  Default: BodyPartFormat.Plain</value>
            <exception cref="T:System.ArgumentException">Thrown when an invalid value is specified.</exception> 
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.BodyPart.Encoding">
            <summary>
            Gets or sets the encoding method to use on the body part.
            </summary>
            <value>One of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartEncoding">BodyPartEncoding</see> values specifying the encoding method to use on the body part.  Default: BodyPartEncoding.QuotedPrintable</value>
            <exception cref="T:System.ArgumentException">Thrown when an invalid value is specified.</exception>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.BodyPart.CharsetEncoding">
            <summary>
            Gets or sets the character encoding of the e-mail message.
            </summary>
            <value>A System.Text.Encoding object that indicates the charset encoding.  Default: <see cref="P:Quiksoft.EasyMail.SMTP.EmailMessage.CharsetEncoding"/>.</value>
            <remarks>This property should be set for messages composed with character sets other than US-ASCII.  See the System.Text.Encoding class in the MSDN documentation for more information.</remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.BodyPart.CustomHeaders">
            <summary>
            Gets the <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeaderCollection">CustomHeaderCollection</see> object.
            </summary>
            <value>A <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeaderCollection"/> object.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.BodyPart.AutoWrap">
            <summary>
            Gets or sets the maximum number of characters allowed per line before the body is wrapped.
            </summary>
            <remarks>
            Some Internet mail systems have trouble processing messages with lines longer than 76 characters. 
            Setting this property's value to any value greater than 0 turns on the AutoWrap feature and the message body text will be word wrapped so that no line contains more than the number of characters specified.
            When encoding a BodyPart with Quoted-Printable (default) or Base64, you do not need to turn this feature on because the encoding algorithm will automatically wrap the text.  AutoWrap should only be used
            with plain text messages that are not encoded.  If you are unsure, leave this property set to zero.</remarks>
            <value>The maximum number of characters allowed per line before the body text is wrapped.   Default: 0 (off).</value>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.BodyPartFormat">
            <summary>
            Provides enumerated values for e-mail body format.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.BodyPartFormat.Plain">
            <summary>
            Specifies that the body part format is plain text.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.BodyPartFormat.HTML">
            <summary>
            Specifies that the body part format is HTML.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.BodyPartFormat.RTF">
            <summary>
            Specifies that the body part format is RTF.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.BodyPartFormat.XML">
            <summary>
            Specifies that the body part format is XML.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.BodyPartFormat.Enriched">
            <summary>
            Specifies that the body part format is Enriched.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.BodyPartEncoding">
            <summary>
            Provides enumerated values for e-mail body encoding.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.BodyPartEncoding.None">
            <summary>
            Specifies that no encoding is to be used on the body part.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.BodyPartEncoding.Base64">
            <summary>
            Specifies that Base64 encoding is to be used on the body part.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.BodyPartEncoding.QuotedPrintable">
            <summary>
            Specifies that Quoted-Printable encoding is to be used on the body part.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.BodyPartEncoding.EightBit">
            <summary>
            8Bit 
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.BodyPartCollection">
            <summary>
            Represents a collection of the BodyParts that are transmitted with the message.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPartCollection.#ctor">
            <summary>
            Initializes a new instance of the BodyPartCollection class.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPartCollection.Add(System.String)">
            <summary>
            Creates a <see cref="T:Quiksoft.EasyMail.SMTP.BodyPart">BodyPart</see> object using specified values and adds it to the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartCollection">BodyPartCollection</see>. 
            </summary>
            <param name="Body">The text of the body part.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPartCollection.Add(System.String,Quiksoft.EasyMail.SMTP.BodyPartFormat)">
            <summary>
            Creates a <see cref="T:Quiksoft.EasyMail.SMTP.BodyPart">BodyPart</see> object using specified values and adds it to the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartCollection">BodyPartCollection</see>. 
            </summary>
            <param name="Body">The text of the body part.</param>
            <param name="Format">One of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartFormat">BodyPartFormat</see> values specifying the format of the body part.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPartCollection.Add(System.String,Quiksoft.EasyMail.SMTP.BodyPartFormat,Quiksoft.EasyMail.SMTP.BodyPartEncoding)">
            <summary>
            Creates a <see cref="T:Quiksoft.EasyMail.SMTP.BodyPart">BodyPart</see> object using specified values and adds it to the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartCollection">BodyPartCollection</see>. 
            </summary>
            <param name="Body">The text of the body part.</param>
            <param name="Format">One of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartFormat">BodyPartFormat</see> values specifying the format of the body part.</param>
            <param name="Encoding">One of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartEncoding">BodyPartEncoding</see> values specifying the encoding method to use on the body part.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPartCollection.Add(Quiksoft.EasyMail.SMTP.BodyPart)">
            <summary>
            Adds a <see cref="T:Quiksoft.EasyMail.SMTP.BodyPart">BodyPart</see> object to the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartCollection">BodyPartCollection</see>.
            </summary>
            <param name="BodyPartObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPart">BodyPart</see> object to add to the collection.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.BodyPartCollection.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPart"/> object at the specified position.
            </summary>
            <param name="index">An ordinal index value that specifies which <see cref="T:Quiksoft.EasyMail.SMTP.BodyPart"/> to return.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.MIMEHeaderFlags">
            <summary>
            Flags that determines how MIME attachments are added to the message.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.MIMEHeaderFlags.None">
            <summary>
            No Flags.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.MIMEHeaderFlags.NoContentType">
            <summary>
            Do not automatically add content type header.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.MIMEHeaderFlags.NoContentTransferEncoding">
            <summary>
            Do not automatically add content transfer encoding header.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.MIMEHeaderFlags.NoDisposition">
            <summary>
            Do not automatically add the disposition headers.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.MIMEHeaderFlags.NoDescription">
            <summary>
            Do not automatically add the description header.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.MIMEHeaderFlags.GenericContentType">
            <summary>
            Use generic content type application/octet-stream.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.AuthenticationErrors">
            <summary>
            Authentication error codes.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.AuthenticationErrors.None">
            <summary>
            No error code available.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.AuthenticationErrors.AuthenticationFailed">
            <summary>
            ESMTP Authentication failed.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.AuthenticationErrors.AuthenticationNotSupported">
            <summary>
            The selected ESMTP Authentication mode is not supported by the server.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.AuthenticationErrors.AuthenticationProtocolError">
            <summary>
            ESMTP Authentication protocol error.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.ImportErrors">
            <summary>
            Provides enumerated values that describe the errors thrown while importing a URI.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ImportErrors.GetResponse">
            <summary>
            Thrown if there is a problem connecting to or resolving the URI.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ImportErrors.ReadingStream">
            <summary>
            Thrown when reading an error occurs reading from the stream.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ImportErrors.ParsingHTML">
            <summary>
            An error occurred parsing the imported HTML.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.SMTPImportException">
            <summary>
            The exception that is thrown when there is an error importing a bodypart error.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPImportException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SMTPImportException class with a specified error message.
            </summary>
            <param name="Description">The error message string.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPImportException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the SMTPImportException class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="Inner">The inner exception reference.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPImportException.#ctor(System.String,Quiksoft.EasyMail.SMTP.ImportErrors)">
            <summary>
            Initializes a new instance of the SMTPAuthenticationException class with a specified error message 
            and error code.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="ErrorCode">One of the <see cref="T:Quiksoft.EasyMail.SMTP.ImportErrors"/> that describes why this exception was thrown.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPImportException.#ctor(System.String,System.Exception,Quiksoft.EasyMail.SMTP.ImportErrors)">
            <summary>
            Initializes a new instance of the SMTPAuthenticationException class with a specified error message 
            and error code.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="Inner">The inner exception reference.</param>
            <param name="ErrorCode">One of the <see cref="T:Quiksoft.EasyMail.SMTP.ImportErrors"/> that describes why this exception was thrown.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTPImportException.ErrorCode">
            <summary>
            Retrieves error code.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException">
            <summary>
            The exception that is thrown when an authentication error is returned from the SMTP server.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SMTPAuthenticationException class with a specified error message.
            </summary>
            <param name="Description">The error message string.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the SMTPAuthenticationException class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="Inner">The inner exception reference.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException.#ctor(System.String,Quiksoft.EasyMail.SMTP.AuthenticationErrors)">
            <summary>
            Initializes a new instance of the SMTPAuthenticationException class with a specified error message 
            and error code.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="ErrorCode">One of the <see cref="T:Quiksoft.EasyMail.SMTP.AuthenticationErrors"/> that describes why this exception was thrown.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException.#ctor(System.String,System.Exception,Quiksoft.EasyMail.SMTP.AuthenticationErrors)">
            <summary>
            Initializes a new instance of the SMTPAuthenticationException class with a specified error message 
            and error code.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="Inner">The inner exception reference.</param>
            <param name="ErrorCode">One of the <see cref="T:Quiksoft.EasyMail.SMTP.AuthenticationErrors"/> that describes why this exception was thrown.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException.ErrorCode">
            <summary>
            Retrieves error code.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.ProtocolErrors">
            <summary>
            Protocol error codes.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ProtocolErrors.None">
            <summary>
            No error code available.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ProtocolErrors.FromAddress">
            <summary>
            The from address was not formatted correctly or was rejected by the SMTP mail server. 
            Some SMTP mail servers will only accept mail from particular addresses or domains. 
            SMTP mail servers may also reject a from address if the server can not successfully do 
            a reverse lookup on the from address.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ProtocolErrors.RecipientAddress">
            <summary>
            An error was reported in response to a recipient address. The SMTP server may refuse to handle mail for unknown recipients.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ProtocolErrors.DATA">
            <summary>
            Error during DATA command. This can indicate a problem due to the message body or attachments.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ProtocolErrors.EndOfMessageMark">
            <summary>
            Error sending end of message mark.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ProtocolErrors.HELO">
            <summary>
            Error sending the HELO command. Ensure that the domain specified to the 
            Connect() method is correct.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ProtocolErrors.BadResponse">
            <summary>
            Connected but server sent back bad response.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ProtocolErrors.QUIT">
            <summary>
            An error was encountered during the disconnect, because the server did not acknowledge the request 
            to terminate the connection. This error occurs when the server fails to properly reply 
            to the QUIT command. 
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">
            <summary>
            The exception that is thrown when a protocol error is returned from the SMTP server.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPProtocolException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SMTPProtocolException class with a specified error message.
            </summary>
            <param name="Description">The error message string.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPProtocolException.#ctor(System.String,Quiksoft.EasyMail.SMTP.ProtocolErrors)">
            <summary>
            Initializes a new instance of the SMTPProtocolException class with a specified error message.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="ErrorCode">One of the <see cref="T:Quiksoft.EasyMail.SMTP.ProtocolErrors"/> that describes why this exception was thrown.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPProtocolException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the SMTPProtocolException class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="Inner">The inner exception reference.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPProtocolException.#ctor(System.String,System.Exception,Quiksoft.EasyMail.SMTP.ProtocolErrors)">
            <summary>
            Initializes a new instance of the SMTPProtocolException class with a specified error message.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="Inner">The inner exception reference.</param>
            <param name="ErrorCode">One of the <see cref="T:Quiksoft.EasyMail.SMTP.ProtocolErrors"/> that describes why this exception was thrown.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTPProtocolException.ErrorCode">
            <summary>
            Retrieves error code.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.ConnectionErrors">
            <summary>
            Connection error codes.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ConnectionErrors.None">
            <summary>
            No error code available.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ConnectionErrors.ErrorConnecting">
            <summary>
            Error connecting to the mail server.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ConnectionErrors.NotConnected">
            <summary>
            Error not connected to the mail server. Socket not connected. Either you did not call connect or the connection was closed.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ConnectionErrors.CouldNotResolveHost">
            <summary>
            Could not resolve host.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ConnectionErrors.NoMailServerSpecified">
            <summary>
            Mail server not specified.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ConnectionErrors.SocketError">
            <summary>
            A socket error occurred.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ConnectionErrors.NoMXRecordsFound">
            <summary>
            Could not connect using direct connect.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">
            <summary>
            The exception that is thrown when there is a socket connection error.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPConnectionException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SMTPConnectionException class with a specified error message.
            </summary>
            <param name="Description">The error message string.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPConnectionException.#ctor(System.String,Quiksoft.EasyMail.SMTP.ConnectionErrors)">
            <summary>
            Initializes a new instance of the SMTPConnectionException class with a specified error message.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="ErrorCode">One of the <see cref="T:Quiksoft.EasyMail.SMTP.ConnectionErrors"/> that describes why this exception was thrown.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPConnectionException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the SMTPConnectionException class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="Inner">The inner exception reference.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTPConnectionException.#ctor(System.String,System.Exception,Quiksoft.EasyMail.SMTP.ConnectionErrors)">
            <summary>
            Initializes a new instance of the SMTPConnectionException class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="Inner">The inner exception reference.</param>
            <param name="ErrorCode">One of the <see cref="T:Quiksoft.EasyMail.SMTP.ConnectionErrors"/> that describes why this exception was thrown.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTPConnectionException.ErrorCode">
            <summary>
            Retrieves error code.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.FileIOException">
             <summary>
            The exception that is thrown when there is an error related to the file system.
             </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.FileIOException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the FileIOException class with a specified error message.
            </summary>
            <param name="Description">The error message string.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.FileIOException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the FileIOException class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="Inner">The inner exception reference.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.CancelException">
             <summary>
            The exception that is thrown when an operation is canceled using the cancel method.
             </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.CancelException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CancelException class with a specified error message.
            </summary>
            <param name="Description">The error message string.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.CancelException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the CancelException class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="Inner">The inner exception reference.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.BodyPartItem">
            <summary>
            Provides properties and methods for adding embedded content related to one or more body parts.  i.e. Embedded images in an HTML message.  See remarks.
            </summary>
            <remarks>
            The BodyPartItem class is used to attach and associate data with one or more body parts of a message.
            The most common application of this is adding embedded images to an HTML message.  In this example, each image would
            be represented by one BodyPartItem object.  The data is added to the message by specifying its filename in the <see cref="P:Quiksoft.EasyMail.SMTP.BodyPartItem.Filename"/> property.  The data
            is associated with the HTML message by its <see cref="P:Quiksoft.EasyMail.SMTP.BodyPartItem.ContentID"/> which is specified in the HTML as the source for the image data. 
            
            If you are creating HTML messages and you wish to embed content such as images, the <see cref="M:Quiksoft.EasyMail.SMTP.EmailMessage.ImportBody(System.String,Quiksoft.EasyMail.SMTP.BodyPartFormat,Quiksoft.EasyMail.SMTP.ImportBodyFlags)"/> method will
            automatically import an HTML file and create the BodyPartItem objects for you and.  These objects will be automatically added to the 
            <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItemCollection"/>.
            
            The BodyPartItem and BodyPartItemCollection classes are available for developers who wish to manually create the 
            embedded content, or who wish to fine tune it after importing with <see cref="M:Quiksoft.EasyMail.SMTP.EmailMessage.ImportBody(System.String,Quiksoft.EasyMail.SMTP.BodyPartFormat,Quiksoft.EasyMail.SMTP.ImportBodyFlags)"/>.
            
            </remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPartItem.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItem">BodyPartItem class</see>.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPartItem.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItem">BodyPartItem class</see> with the BodyPartItem filename.
            </summary>
            <param name="Filename">The full path and filename to the file which contains the data associated with the BodyPartItem.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPartItem.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItem">BodyPartItem class</see> with the attachment filename and ContentID.
            </summary>
            <param name="Filename">The full path and filename to the file which contains the data associated with the BodyPartItem.</param>
            <param name="ContentID">The content id used to identify the BodyPartItem.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPartItem.#ctor(System.IO.Stream,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItem">BodyPartItem class</see> with the BodyPartItem stream and ContentID.
            </summary>
            <param name="Stream">A stream object that contains the data associated with the BodyPartItem.</param>
            <param name="ContentID">The content id used to identify the BodyPartItem.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.BodyPartItem.Stream">
            <summary>
            Gets and sets the BodyPartItem stream.
            </summary>
            <value>A stream object that is available for reading.  When using a file name this value should be left null.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.BodyPartItem.Filename">
            <summary>
            Gets and sets the filename which contains the data associated with the BodyPartItem.
            </summary>
            <value>The full path and filename to the file which contains the data associated with the BodyPartItem.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.BodyPartItem.CustomHeaders">
            <summary>
            Gets the <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeaderCollection">CustomHeaderCollection</see> object.
            </summary>
            <value>A <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeaderCollection"/> object.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.BodyPartItem.ContentID">
            <summary>
            Gets or sets the content id of the BodyPartItem.
            </summary>
            <value>The content id used to identify the BodyPartItem.</value>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.BodyPartItemCollection">
            <summary>
            Represents a collection of <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItem">BodyPartItem</see> objects.
            </summary>
            <remarks>
            The BodyPartItemCollection class is used to associate data with one or more body parts of a message.
            The most common application of this is adding embedded images to an HTML message.  See the remarks on the 
            <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItem"/> class for more information.
            </remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPartItemCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItemCollection">BodyPartItemCollection class</see>
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPartItemCollection.Add(Quiksoft.EasyMail.SMTP.BodyPartItem)">
            <summary>
            Adds an BodyPartItem object to the collection.
            </summary>
            <param name="BodyPartItemObj">BodyPartItem object to add to the collection.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPartItemCollection.Add(System.String,System.String)">
            <summary>
            Creates an <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItem">BodyPartItem</see> object using specified values and adds it to the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItemCollection">BodyPartItemCollection</see>. 
            </summary>
            <param name="Filename">The full path and filename to the file which contains the data to be associated with the BodyPartItem.</param>
            <param name="ContentID">The content id to identify the BodyPartItem.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.BodyPartItemCollection.Add(System.IO.Stream,System.String)">
            <summary>
            Creates a <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItem">BodyPartItem</see> object using specified values and adds it to the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItemCollection">BodyPartItemCollection</see>. 
            </summary>
            <param name="Stream">A stream object that contains the data associated with the BodyPartItem.</param>
            <param name="ContentID">The content id used to identify the BodyPartItem.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.BodyPartItemCollection.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItem"/> object at the specified position.
            </summary>
            <param name="index">An ordinal index value that specifies which <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItem"/> to return.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.LimitationException">
            <summary>
            The exception that is thrown when an invalid or unsupported option is used.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.LimitationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SMTPFileIOException class with a specified error message.
            </summary>
            <param name="Description">The error message string.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.LimitationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the LimitationException class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="Inner">The inner exception reference.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.StreamException">
             <summary>
            The exception that is thrown when an error occurs saving data to a stream object.
             </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.StreamException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the StreamException class with a specified error message.
            </summary>
            <param name="Description">The error message string.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.StreamException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the StreamException class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="Description">The error message string.</param>
            <param name="Inner">The inner exception reference.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.AuthenticationErrors">
            <summary>
            Authentication error codes.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.AuthenticationErrors.None">
            <summary>
            
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.AuthenticationErrors.AuthenticationFailed">
            <summary>
            ESMTP Authentication failed.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.AuthenticationErrors.AuthenticationNotSupported">
            <summary>
            The selected ESMTP Authentication mode is not supported by the server.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.AuthenticationErrors.AuthenticationProtocolError">
            <summary>
            ESMTP Authentication protocol error.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.SMTPAuthenticationException">
            <summary>
            Exception related to ESMTP Authentication.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.ProtocolErrors">
            <summary>
            Protocol error codes.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.ProtocolErrors.None">
            <summary>
            No error code available.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.ProtocolErrors.FromAddress">
            <summary>
            The from address was not formatted correctly or was rejected by the SMTP mail server. 
            Some SMTP mail servers will only accept mail from particular addresses or domains. 
            SMTP mail servers may also reject a from address if the server can not successfully do 
            a reverse lookup on the from address.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.ProtocolErrors.RecipientAddress">
            <summary>
            An error was reported in response to a recipient address. The SMTP server may refuse to handle mail for unknown recipients.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.ProtocolErrors.DATA">
            <summary>
            Error during DATA command. This can indicate a problem due to the message body or attachments.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.ProtocolErrors.EndOfMessageMark">
            <summary>
            Error sending end of message mark.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.ProtocolErrors.HELO">
            <summary>
            Error sending the HELO command. Ensure that the domain specified to the 
            Connect() method is correct.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.ProtocolErrors.BadResponse">
            <summary>
            Connected but server sent back bad response.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.ProtocolErrors.QUIT">
            <summary>
            An error was encountered during the disconnect, because the server did not acknowledge the request 
            to terminate the connection. This error occurs when the server fails to properly reply 
            to the QUIT command. 
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.SMTPProtocolException">
            <summary>
            Exception related to the Specific Protocol.
            </summary>
            
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.ConnectionErrors">
            <summary>
            Connection error codes.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.ConnectionErrors.None">
            <summary>
            
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.ConnectionErrors.ErrorConnecting">
            <summary>
            Error connecting to the mail server.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.ConnectionErrors.NotConnected">
            <summary>
            Error not connected to the mail server. Socket not connected. Either you did not call connect or the connection was closed.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.ConnectionErrors.CouldNotResolveHost">
            <summary>
            Could not resolve host.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.ConnectionErrors.NoMailServerSpecified">
            <summary>
            Mail server not specified.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.ConnectionErrors.SocketError">
            <summary>
            A socket error occurred.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.SMTPConnectionException">
            <summary>
            Exception related to the socket connection.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.SMTPFileIOException">
            <summary>
            Exception related to the file system.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.SMTPCancelException">
            <summary>
            Thrown when the cancel method is called.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.SMTP.SMTP_STATUS_FLAGS">
            <summary>
            Enumerator that shows the type of callback that was made.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_STATUS_FLAGS.SMTP_PROGRESS_CALLBACK">
            <summary>
            Progress of message being sent
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_STATUS_FLAGS.SMTP_RECIPIENTS_CALLBACK">
            <summary>
            A recipient had been rejected
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_STATUS_FLAGS.SMTP_MESSAGEID_CALLBACK">
            <summary>
            The message ID was created.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.SMTP.Status">
            <summary>
            Callback function definition.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.SMTP.SMTP_DSN_NOTIFYFLAGS">
            <summary>
            Flags that can be set for ESMTP DSN
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_DSN_NOTIFYFLAGS.SMTP_NOTIFYFLAGS_NONE">
            <summary>
            Default, do not send command to server.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_DSN_NOTIFYFLAGS.SMTP_NOTIFYFLAGS_DEFAULT">
            <summary>
            Send the command but do what ever the server default is.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_DSN_NOTIFYFLAGS.SMTP_NOTIFYFLAGS_FAILURE">
            <summary>
            Notify if message fails.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_DSN_NOTIFYFLAGS.SMTP_NOTIFYFLAGS_SUCCESS">
            <summary>
            Notify if message is sent successfully.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_DSN_NOTIFYFLAGS.SMTP_NOTIFYFLAGS_DELAY">
            <summary>
            Notify if message is delayed.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_DSN_NOTIFYFLAGS.SMTP_NOTIFYFLAGS_NEVER">
            <summary>
            Do not notify me.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.SMTP.SMTP_DSN_RETURNFLAGS">
            <summary>
            Flags that can be set when using DSN to request what the server should return on failure.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_DSN_RETURNFLAGS.SMTP_RETURNFLAGS_NONE">
            <summary>
            Do not send command
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_DSN_RETURNFLAGS.SMTP_RETURNFLAGS_DEFAULT">
            <summary>
            Return what ever the server default is.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_DSN_RETURNFLAGS.SMTP_RETURNFLAGS_HEADER">
            <summary>
            Return only the header of the message.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_DSN_RETURNFLAGS.SMTP_RETURNFLAGS_MESSAGE">
            <summary>
            Return the full message.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.SMTP.SMTPMIMEFlags">
            <summary>
            Flags that can be set for each MIME part.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTPMIMEFlags.None">
            <summary>
            No Flags
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTPMIMEFlags.NoContentType">
            <summary>
            Do not add content type header
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTPMIMEFlags.NO_CONTRANS_ENC">
            <summary>
            Do not add content transfer encoding header
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTPMIMEFlags.NO_DISPOSITION">
            <summary>
            Do not add the disposition headers
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTPMIMEFlags.NO_DESCRIPTION">
            <summary>
            Do not add the description header
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTPMIMEFlags.GENERIC_CONTENT_TYPE">
            <summary>
            Use generic content type application/octet-stream
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTPMIMEFlags.PartOfMessage">
            <summary>
            Part of the message, html image etc..
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTPMIMEFlags.TypeFile">
            <summary>
            DATA is a file name.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTPMIMEFlags.TypeStream">
            <summary>
            Stream member is set.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.SMTP.SMTP_SEND_PARAMS">
            <summary>
            Parameters that should be sent to the send method.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_SEND_PARAMS.DSN_ID">
            <summary>
            DSN ID as used in returned messages.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_SEND_PARAMS.DSN_NotifyFlags">
            <summary>
            Flags that say when we want to be notified.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_SEND_PARAMS.DSN_ReturnFlags">
            <summary>
            Flags that say what we want returned.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_SEND_PARAMS.ESMTP_Pipelining">
            <summary>
            Says if we want to use pipelining or not.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_SEND_PARAMS.pStatusCallback">
            <summary>
            Call back of status data.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_SEND_PARAMS.pUserData">
            <summary>
            User data sent back to callback.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP_SEND_PARAMS.Options">
            <summary>
            Sending Options
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.SMTP.AUTH_MODE">
            <summary>
            Set the type of ESMTP Authentication to use.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.AUTH_MODE.SMTP_AUTH_NONE">
            <summary>
            Do not use Authentication
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.AUTH_MODE.SMTP_AUTH_LOGIN">
            <summary>
            Use AUTH Login
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.AUTH_MODE.SMTP_AUTH_CRAMMD5">
            <summary>
            Use Cram MD5. Not yet implemented.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.AUTH_MODE.SASL">
            <summary>
            USE NTLM, not yet implemented.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.SMTP.SMTPImportHTMLFlags">
            <summary>
            Flags to use when importing HTML using the ImportHTML method.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTPImportHTMLFlags.None">
            <summary>
            None
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTPImportHTMLFlags.CreateAlternative">
            <summary>
            Crate an alternative text representation.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.SMTP.SMTP">
            <summary>
            Summary description for SMTP.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP.BUFFER_SIZE">
            <summary>
            Buffer size of strings
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP.MIME_WARNING">
            <summary>
            Options
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP.pSocket">
            <summary>
            Socket class
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP.m_bAuthLogin">
            <summary>
            Set when AUTH Login was used to authenticate
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP.m_bESMTP">
            <summary>
            Set when server support ESMTP and EHLO command was sent
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP.m_bSupportsPipelining">
            <summary>
            Set when the server is able to use pipelining.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP.m_strLogFile">
            <summary>
            Log file to write log to.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP.m_pFileStream">
            <summary>
            Log file stream.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.SMTP.SMTP.m_pStreamWriter">
            <summary>
            Log file stream writer.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.SMTP.SMTP.WasCanceled">
            <summary>
            Returned true if cancel was called.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.SMTP.SMTP.WriteToLog(System.String)">
            <summary>
            Writes a string to the log file.
            </summary>
            <param name="strData">Data to write to log file.</param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.SMTP.SMTP.Connect(System.String,System.Int32,System.String,System.Int32,System.Net.EndPoint)">
            <summary>
            Connects to an SMTP server.
            </summary>
            <param name="strServer">Server Name</param>
            <param name="nPort">Port number to connect to. Default is 25.</param>
            <param name="strDomain">Domain name to send to HELO or EHLO command.</param>
            <param name="nOptions">Options that can be set.</param>
            <returns>returns 0 on success or -1 on error.</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.SMTP.SMTP.Login(Quiksoft.EasyMail.Internal.SMTP.AUTH_MODE,System.String,System.String,Quiksoft.EasyMail.SMTP.ISMTPSASL)">
            <summary>
            Authenticates sender with mail server.
            </summary>
            <param name="Mode">Type of Authentication to use.</param>
            <param name="strAccount">Account name</param>
            <param name="AuthMechanism">An <see cref="!:IIMAP4SASL"/> interface used for custom authentication.</param>
            <param name="strPassword">Account password</param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.SMTP.SMTP.CloseConnection">
            <summary>
            Disconnect for mail server.
            </summary>
            <returns>0</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.SMTP.SMTP.Cancel">
            <summary>
            Called to cancel a pending SMTP operation.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.SMTP.SMTP.AuthLogin(System.String,System.String)">
            <summary>
            Performs internal Auth LOGIN
            </summary>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.SMTP.SMTP.Dispose">
            <summary>
            Closes resources
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.SMTP.SMTP.Disconnect">
            <summary>
            Disconnect from mail server
            </summary>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.SMTP.SMTP.SendMessage(Quiksoft.EasyMail.Internal.Message,System.Int32,Quiksoft.EasyMail.Internal.SMTP.SMTP_SEND_PARAMS)">
            <summary>
            Send message to mail server. Must connect first.
            </summary>
            <param name="pMessage">Message to send</param>
            <param name="nOptions">Options</param>
            <param name="pParams">Send parameters</param>
            <returns>0</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.SMTP.SMTP.SendSMTPCommand(System.String)">
            <summary>
            Sends SMTP command to server and returns response.
            </summary>
            <param name="strCommand">Command to send</param>
            <returns>Response</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.SMTP.SMTP.SendSMTPCommand(System.String,System.Boolean)">
            <summary>
            Sends SMTP command to server and returns response
            </summary>
            <param name="strCommand">Command to send</param>
            <param name="bPipeline">If pipeline then do not receive.</param>
            <returns>Response</returns>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.SMTP.SMTP.Timeout">
            <summary>
            Set the time we should wait for a socket to connect, receive and send data before timing out.
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.SMTP.SMTP.Connnected">
            <summary>
            Tells you if the object is connected.
            </summary>
            <returns>return true if connected otherwise false.</returns>
        </member>
        <member name="P:Quiksoft.EasyMail.Internal.SMTP.SMTP.LogFile">
            <summary>
            File to write status to
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.DispositionNotification">
             <summary>
             Provides properties for constructing a message disposition notification request.
             </summary>
             <remarks>The message disposition notification is used to notify the sender of certain
             message conditions that may occur after successful delivery of the message.
            </remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.DispositionNotification.To">
            <summary>
            Sets the email address for message disposition notifications.
            </summary>
            <remarks>This address will be placed in the Disposition-Notification-To header.
            The presence of this header field in a message is
            merely a request for a message disposition notification.  
            The recipient's user agent is always
            free to silently ignore such a request.
            Dispostion notifications are defined in RFC-2298.
            </remarks>		
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.ImportBodyFlags">
            <summary>
            Provides enumerated values that determine how a file is imported.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ImportBodyFlags.None">
            <summary>
            No options specified.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.ImportBodyFlags.CreateAlternativeText">
            <summary>
            Automatic alternate body text creation. Specifying this option when importing an HTML message automatically creates the alternate body text from the HTML. The alternate body text is stored as a separate bodypart.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.AddressValidationOptions">
            <summary>
            Advanced Options for validating an EmailAddress
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.AddressValidationOptions.None">
            <summary>
            No options specified.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.AddressValidationOptions.IgnoreCase">
            <summary>
            Specifies case-insensitive matching.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.SMTPQueueType">
            <summary>
            Provides enumerated values that determine the queuing agent being used.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.SMTPQueueType.None">
            <summary>
            No queuing agent is being used.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.SMTPQueueType.IMail">
            <summary>
            The queuing agent being used is Ipswitch IMail Server.
            </summary>
            <value>The pickup directory is normally found at [Application Directory]\spool\</value>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.SMTPQueueType.MSSMTPService">
            <summary>
            The queuing agent being used is Microsoft SMTP Service.
            </summary>
            <value>The pickup directory is normally found at Inetpub\mailroot\Pickup\</value>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.SMTPQueueType.Merak">
            <summary>
            The queuing agent being used is Merak Mail Server. 
            </summary>
            <value>The pickup directory is normally found at [Application Directory]\mail\forward\</value>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.EmailMessage">
            <summary>
            Provides properties and methods for constructing an e-mail message.</summary>
            <remarks>
            To send an e-mail message defined by an instance of this class, use the <see cref="T:Quiksoft.EasyMail.SMTP.SMTP">SMTP class</see>. 
            </remarks>
            <seealso cref="T:Quiksoft.EasyMail.SMTP.SMTP">SMTP Class</seealso>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.SaveMessage(System.String,System.Boolean)">
            <summary>
            Creates and RFC822 file that represents the e-mail message.
            </summary>
            <remarks>This method quickly creates an e-mail file based on the properties of the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage"/> object. The e-mail file is formatted to standard specifications (RFC 822) and should be understood by other mail programs.</remarks>
            <param name="FileName">File name and path to where the message should be saved.</param>
            <param name="Overwrite">If set to true the file will be overwriten.</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when file cannot be read.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.SaveMessage(System.String)">
            <summary>
            Creates and RFC822 file that represents the e-mail message.
            </summary>
            <remarks>This method quickly creates an e-mail file based on the properties of the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage"/> object. The e-mail file is formatted to standard specifications (RFC 822) and should be understood by other mail programs.</remarks>
            <param name="FileName">File name and path to where the message should be saved.</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when file cannot be read.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.SaveMessage(System.IO.Stream)">
            <summary>
            Creates and RFC822 message that represents the e-mail message.
            </summary>
            <remarks>This method quickly writes it to a stream based on the properties of the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage"/> object. The stream data is formatted to standard specifications (RFC 822) and should be understood by other mail programs.</remarks>
            <param name="Stream">Stream to where the message should be stored.</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.StreamException">Thrown when error occurs writing data to the stream object.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.SaveMessage(System.IO.Stream,System.Boolean,System.Text.Encoding)">
            <summary>
            Creates and RFC822 message that represents the e-mail message.
            </summary>
            <remarks>This method quickly writes it to a stream based on the properties of the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage"/> object. The stream data is formatted to standard specifications (RFC 822) and should be understood by other mail programs.</remarks>
            <param name="Stream">Stream to where the message should be stored.</param>
            <param name="AddXHeaders">If set to true x-receiver and x-sender headers will be added to the message for each from and recipient address.  If set to false these headers will not be added. </param>
            <param name="CharsetEncoding">A System.Text.Encoding object that indicates the charset encoding to use to convert from string form to binary.  </param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.StreamException">Thrown when error occurs writing data to the stream object.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.SaveMessage(System.IO.Stream,System.Boolean)">
            <summary>
            Creates and RFC822 message that represents the e-mail message.
            </summary>
            <remarks>This method quickly writes it to a stream based on the properties of the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage"/> object. The stream data is formatted to standard specifications (RFC 822) and should be understood by other mail programs.</remarks>
            <param name="Stream">Stream to where the message should be stored.</param>
            <param name="AddXHeaders">If set to true x-receiver and x-sender headers will be added to the message for each from and recipient address.  If set to false these headers will not be added. </param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.StreamException">Thrown when error occurs writing data to the stream object.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage class</see>. 
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.Load(System.Object)">
            <summary>
            Copies data from a Quiksoft.EasyMail.Parse.EmailMessage object in to the object.
            </summary>
            <param name="ParseEmailMessage">A Quiksoft.EasyMail.Parse.EmailMessage object.</param>
            <exception cref="T:System.ArgumentException">Thrown if an invalid argument is specified.</exception>
            <exception cref="T:System.Security.SecurityException">Thrown if an the caller does not have the required permission to access the parse assembly.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> of the parse assembly is not set with a valid license key.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.Load(System.IO.Stream)">
            <summary>
            Parses an RFC822 message as a string and copies the data to the EmailMessage object.
            </summary>
            <param name="RFC822">An RFC822 message stream.</param>
            <remarks>This method requires a licensed version of the Parse assembly.</remarks>
            <exception cref="T:System.IO.FileNotFoundException">Thrown if the parse assembly was not found.</exception>
            <exception cref="T:System.Security.SecurityException">Thrown if an the caller does not have the required permission to access the parse assembly.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> of the parse assembly is not set with a valid license key.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage class</see> and loads data from a <see cref="!:Quiksoft.EasyMail.Parse.EmailMessage"/> object.
            </summary>
            <param name="ParseEmailMessage">A Quiksoft.EasyMail.Parse.EmailMessage object.</param>
            <exception cref="T:System.Security.SecurityException">Thrown if  the caller does not have the required permission to access the parse assembly.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> of the parse assembly is not set with a valid license key.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage class</see> with an RFC822 message stream. 
            </summary>
            <exception cref="T:System.Security.SecurityException">Thrown if an the caller does not have the required permission to access the parse assembly.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> of the parse assembly is not set with a valid license key.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.GetLateAddress(System.Object,System.String)">
            <summary>
            Converts Parse Address Collection to SMTP RecipientCollection 
            </summary>
            <param name="obj"></param>
            <param name="Name"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.GetLateAttachments(System.Object)">
            <summary>
            Converts Parse AttachmentCollection to SMTP AttachmentCollection.
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.GetLateBodyParts(System.Object)">
            <summary>
            Converts all body part and body part items from parse to SMTP EmailMessage.
            </summary>
            <param name="obj"></param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.#ctor(System.String,System.String,System.String,System.String,Quiksoft.EasyMail.SMTP.BodyPartFormat)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage class</see> with the subject, recipient, sender, text and format of the message. 
            </summary>
            <param name="Subject">The subject of the message.</param>
            <param name="RecipientEmail">The recipient's e-mail address.</param>
            <param name="FromEmail">The sender's e-mail address.</param>
            <param name="BodyText">The body text of the message.</param>
            <param name="BodyFormat">One of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartFormat">BodyPartFormat</see> values specifying the format of the body part.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.Reset">
            <summary>
            Clears all existing members and collections.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.ImportFromURI(System.String,System.String,Quiksoft.EasyMail.SMTP.ImportURIFlags)">
            <summary>
            Imports and sets the <see cref="P:Quiksoft.EasyMail.SMTP.BodyPart.Body"/> property with the downloaded contents of the specified uniform resource identifier (URI).
            To insure the BodyPart is sent correctly, the <see cref="P:Quiksoft.EasyMail.SMTP.BodyPart.Encoding"/> property is automatically set to <see cref="F:Quiksoft.EasyMail.SMTP.BodyPartEncoding.QuotedPrintable"/> and the
            <see cref="P:Quiksoft.EasyMail.SMTP.BodyPart.Format"/> property is automatically set to <see cref="F:Quiksoft.EasyMail.SMTP.BodyPartFormat.HTML"/>.
            </summary>
            <param name="URI">The URI of the requested resource.</param>
            <param name="Base">The base tag that should be the prefix for all Uri's with relative references.
            i.e.
            Base = "http://www.quiksoft.com/"
            img src="/banner.gif"
            then   
            img src="http://www.quiksoft.com/banner.gif";
            </param>
            <param name="Flags">One or more of the <see cref="T:Quiksoft.EasyMail.SMTP.ImportURIFlags"/> values specifying how the body is to be imported. </param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPImportException">Thrown when an error occurs importing a bodypart. See <see cref="T:Quiksoft.EasyMail.SMTP.ImportErrors"/> for detailed error codes.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.EmailMessage.ImportBody(System.String,Quiksoft.EasyMail.SMTP.BodyPartFormat,Quiksoft.EasyMail.SMTP.ImportBodyFlags)">
            <summary>
            Imports body part data from a system file with various options.
            </summary>
            <param name="Filename">Path and filename to import.</param>
            <param name="Format">One of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartFormat"/> values specifying the format of the body part to be imported.</param>
            <param name="ImportFlags">One of the <see cref="T:Quiksoft.EasyMail.SMTP.ImportBodyFlags"/> values specifying the way the file is imported.</param>
            <remarks>
            This method automatically creates a <see cref="T:Quiksoft.EasyMail.SMTP.BodyPart"/> object from the imported data.  
            If the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartFormat"/> is specified as BodyPartFormat.HTML, then the imported file data is
            automatically parsed and any references to embedded images stored on the local file system are automatically
            imported into the message as <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItem"/> objects with a unique content id.  The HTML is modified to 
            reference the content id in-lieu-of the reference to the image file.  
            References to images outside of the local file system,
            such as a URL, or references to the file system that can not be accessed are ignored and left as-is in the HTML.
            If the <see cref="T:Quiksoft.EasyMail.SMTP.ImportBodyFlags"/> parameter
            contains the ImportBodyFlags.CreateAlternativeText, this method will also parse the HTML and attempt to render
            a plain text version of the message which will be added as an additional <see cref="T:Quiksoft.EasyMail.SMTP.BodyPart"/> object to support
            an alternative text version of the message for mail readers that do not understand HTML.  This functionality works
            best with simple HTML messages.
            </remarks>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when the file being imported cannot be opened or read.</exception>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.SMIME">
            <summary>
            The SMIME interface to use for building this message.  The return value of <see cref="!:Quiksoft.EasyMail.SMIME.GetInterface"/>
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.CharsetEncoding">
            <summary>
            Gets or sets the character encoding of the e-mail message.
            </summary>
            <value>A System.Text.Encoding object that indicates the charset encoding.  Default: System.Text.Encoding.ASCII.</value>
            <remarks>This property should be set for messages composed with character sets other than US-ASCII.  See the System.Text.Encoding class in the MSDN documentation for more information.</remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.CustomHeaders">
            <summary>
            Gets or sets the <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeaderCollection">CustomHeaderCollection</see> object.
            </summary>
            <value>A <see cref="T:Quiksoft.EasyMail.SMTP.CustomHeaderCollection"/> object.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.BodyParts">
            <summary>
            Gets or sets the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartCollection">BodyPartCollection</see> object.
            </summary>
            <value>A <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartCollection"/> object.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.BodyPartItems">
            <summary>
            Gets or sets the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItemCollection">BodyPartItemCollection</see> object.
            </summary>
            <value>A <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartItemCollection"/> object.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.Subject">
            <summary>
            Gets or sets the subject of the e-mail message..
            </summary>
            <value>The subject line of the e-mail message.</value>
            <example>
            The following example shows how to set this value using the appSettings section of a configuration file.
            &lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot; ?&gt;
            	&lt;configuration&gt;
            		&lt;appSettings&gt;
            		&lt;add key=&quot;Quiksoft.EasyMail.SMTP.EmailMessage.Subject&quot; value=&quot;My Subject&quot; /&gt;
            		&lt;/appSettings&gt;
            	&lt;/configuration&gt;
            </example>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.Priority">
            <summary>
            Gets or sets the priority of the e-mail message..
            </summary>
            <value>The priority value.  See remarks.</value>
            <remarks>Setting this property will cause an "X-Priority" header to be added to the message with the value specified.  Different mail readers may interpret the X-Priority setting differently. Some may not interpret it at all. The popular readers interpret "4"=low, "3"=normal and "1"=high.</remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.Recipients">
            <summary>
            Gets or sets the <see cref="T:Quiksoft.EasyMail.SMTP.RecipientCollection">RecipientCollection</see> object.
            </summary>
            <example>
            <code>
            The following example shows how to set a default value using the appSettings section of a configuration file.
            &lt;?xml version="1.0" encoding="utf-8" ?&gt;
            	&lt;configuration&gt;
            		&lt;appSettings&gt;
            		&lt;add key="Quiksoft.EasyMail.SMTP.EmailMessage.To" value="John Volp" /&gt;
            		&lt;add key="Quiksoft.EasyMail.SMTP.EmailMessage.ToAddr" value="<EMAIL>" /&gt;
            		&lt;/appSettings&gt;
            	&lt;/configuration&gt;
            </code>
            </example>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.Attachments">
            <summary>
            Gets or sets the <see cref="T:Quiksoft.EasyMail.SMTP.AttachmentCollection"/> object.
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.From">
            <summary>
            Gets or sets the <see cref="T:Quiksoft.EasyMail.SMTP.Address"/> object specifying the sender of the message.
            </summary>
            <value>An <see cref="T:Quiksoft.EasyMail.SMTP.Address"/> object.</value>
            <remarks>The value specified by this property is used in the FROM header of the message.
            The value specified by this property is also used with the SMTP "MAIL FROM" command during the SMTP conversation if the ReturnPath is not set. This command indicates to the SMTP host, the e-mail address of the message sender. Most mail servers will accept mail from anyone, however some mail servers will only accept mail from some senders.</remarks>
            <example>
            <code>
            The following example shows how to set this value using the appSettings section of a configuration file.
            &lt;?xml version="1.0" encoding="utf-8" ?&gt;
            	&lt;configuration&gt;
            		&lt;appSettings&gt;
            		&lt;add key="Quiksoft.EasyMail.SMTP.EmailMessage.From" value="John Volp" /&gt;
            		&lt;add key="Quiksoft.EasyMail.SMTP.EmailMessage.FromAddr" value="<EMAIL>" /&gt;
            		&lt;/appSettings&gt;
            	&lt;/configuration&gt;
            </code>
            </example>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.ReturnPath">
            <summary>
            Gets or sets the return path of the message.
            </summary>
            <value>An string.</value>
            <remarks>The value specified by this property is optional and only used with the SMTP "MAIL FROM" command during the SMTP conversation. 
            This command indicates to the SMTP host, the e-mail address of the message sender and will receive any returned failure message. 
            If this value is not set then the From address is used.</remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.OptionFlags">
            <summary>
            Gets or sets the <see cref="P:Quiksoft.EasyMail.SMTP.EmailMessage.OptionFlags"/>. 
            </summary>
            <value>Any combination of the <see cref="P:Quiksoft.EasyMail.SMTP.EmailMessage.OptionFlags"/> values.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.ReplyTo">
            <summary>
            Gets or sets the reply-to address of the e-mail message.
            </summary>
            <value>Sets the address for which replies to this message should be sent.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.ReturnReceiptTo">
            <summary>
            Gets or sets the return receipt address of the e-mail message.
            </summary>
            <value>Specifies an e-mail address to send receipts to.</value>
            <remarks>This method will add the Return-Receipt-To header field to the message.</remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.MessageID">
            <summary>
            Gets or sets the message id of the e-mail address.
            </summary>
            <value>Specifies the message id that will be sent with this message.  If not set a value will be automatically generated.</value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.EmailMessage.DispositionNotification">
            <summary>
            Gets or sets the <see cref="T:Quiksoft.EasyMail.SMTP.DispositionNotification"/> properties.
            </summary>
            <value>A <see cref="T:Quiksoft.EasyMail.SMTP.DispositionNotification"/> object.</value>
            <remarks>This value is optional and can be ignored.</remarks>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.EmailMessage.PrepareFlags">
            <summary>
            Internal Flags for preparing a message.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.SMTPStatus">
            <summary>
            Represents the method that will handle the <see cref="T:Quiksoft.EasyMail.SMTP.SMTP">SMTPStatus</see> event of the SMTP Object.
            </summary>
            <param name="TotalSent">Total bytes sent for this message.</param>
            <param name="TotalSize">Total size of this message in bytes.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.SMTPBulkStatus">
            <summary>
            Represents the method that will handle the <see cref="T:Quiksoft.EasyMail.SMTP.SMTP">SMTPBulkStatus</see> event of the SMTP Object.
            </summary>
            <param name="MessagesSent">Total number of messages sent in this mailing.</param>
            <param name="TotalMessage">Total number to message to send in this bulk mailing.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.SMTPBulkStatusDetails">
            <summary>
            Represents the method that will handle the <see cref="T:Quiksoft.EasyMail.SMTP.SMTP">SMTPBulkStatusDetails</see> event of the SMTP Object.
            </summary>
            <param name="CurrentRow">The last record in the mail merge that was attempted.</param>
            <param name="Success">This value is set to true if the message was sent successfully otherwise false.</param>
            <param name="FailException">If there was an error sending the current row this parameter will contain the exception that was thrown.
            This can be any of the exceptions thrown from the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Send(Quiksoft.EasyMail.SMTP.EmailMessage)"/> or <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.SubmitToExpress(Quiksoft.EasyMail.SMTP.EmailMessage,System.String)"/> methods.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.ISMTPSSL">
            <summary>
            Internal Interface that should be ignored.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.setoptions(System.Object,System.Object,System.Object)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="o"></param>
            <param name="b"></param>
            <param name="c"></param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.recv(System.String@,System.Int32)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.send(System.String,System.Boolean)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.send(System.String,System.Boolean,System.Text.Encoding)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <param name="c"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.send(System.Byte[],System.Int32)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.Flush">
            <summary>
            Internal Interface that should be ignored.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.Close">
            <summary>
            Internal Interface that should be ignored.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.connect(System.String,System.Int32)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.GetTimeoutValue">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.SetWriteToLog(System.Object)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.SetTimeoutValue(System.Int32)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.Shutdown(System.Net.Sockets.SocketShutdown)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.Receive(System.Byte[],System.Int32,System.Net.Sockets.SocketFlags)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <param name="c"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.IsConnected">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.StartedTLS">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.ActiveProtocol(System.Int32)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.ISMTPSSL.SetDomain(System.String)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.SMTPProtocolLog">
            <summary>
            Represents the method that will handle the <see cref="E:Quiksoft.EasyMail.SMTP.SMTP.ProtocolLog"/> event.
            </summary>
            <param name="Data">Protocol log data.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.SMTP">
            <summary>
            Provides properties and methods for sending messages using the SMTP protocol.
            </summary>
            <remarks>The SMTP object will send messages to an SMTP server or queue them for delivery by SMTPExpress.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.WriteToLog(System.String)">
            <summary>
            Writes to the debug log.  See <see cref="P:Quiksoft.EasyMail.SMTP.SMTP.LogFile"/> property for more information.
            </summary>
            <param name="Data">String to write to the log file.</param>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.SMTP.m_pStatus">
            <summary>
            Private Member
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.QuickSend(System.String,System.String,System.String,System.String,System.String,Quiksoft.EasyMail.SMTP.BodyPartFormat)">
            <summary>
            Sends an e-mail message without needing to create an object instance of the <see cref="T:Quiksoft.EasyMail.SMTP.SMTP">SMTP class</see>.
            </summary>
            <param name="SMTPServerName">SMTP server name or address</param>
            <param name="RecipientEmail">Recipient's e-mail address</param>
            <param name="FromEmail">Sender's e-mail address</param>
            <param name="Subject">Subject of the message</param>
            <param name="BodyText">The text of the message body</param>
            <param name="BodyFormat">One of the <see cref="T:Quiksoft.EasyMail.SMTP.BodyPartFormat">BodyPartFormat</see> values specifying the format of the body part</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when file cannot be read.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
            <example>
            <code>
            License.Key = "Your Company/12F3234AB23E23"
            SMTP.QuickSend("mail.domain.com", "webmaster@localhost","<EMAIL>", "My Subject", "Hello how are you?", BodyPartFormat.Plain);
            </code>
            </example>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.QuickSend(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Sends an plain text e-mail message without needing to create an object instance of the <see cref="T:Quiksoft.EasyMail.SMTP.SMTP">SMTP class</see>.
            </summary>
            <param name="SMTPServerName">SMTP server name or address</param>
            <param name="RecipientEmail">Recipient's e-mail address</param>
            <param name="FromEmail">Sender's e-mail address</param>
            <param name="Subject">Subject of the message</param>
            <param name="BodyText">The text of the message body</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when file cannot be read.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
            <example>
            <code>
            License.Key = "Your Company/12F3234AB23E23"
            SMTP.QuickSend("mail.domain.com", "webmaster@localhost","<EMAIL>", "My Subject", "Hello how are you?");
            </code>
            </example>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.QuickSend(System.String,System.String,System.String)">
            <summary>
            Sends an e-mail message without needing to create an object instance of the <see cref="T:Quiksoft.EasyMail.SMTP.SMTP">SMTP class</see>.
            </summary>
            <param name="RecipientEmail">Recipient's e-mail address</param>
            <param name="Subject">Subject of the message</param>
            <param name="BodyText">The text of the message body</param>
            <remarks>To use this method you must set the SMTPServer and From address in the
            appSettings configuration section. </remarks>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when file cannot be read.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.SMTP">SMTP class</see> with the SMTP mail server name or address.
            </summary>
            <param name="SMTPServerName">Name or address of the SMTP server</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.Reset">
            <summary>
            Clears all existing members and collections.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.Dispose">
            <summary>
            Releases all resources used by this <see cref="T:Quiksoft.EasyMail.SMTP.SMTP"/> object.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.SendSMTPCommand(System.String)">
            <summary>
            Sends a custom SMTP command directly to the SMTP server.  
            </summary>
            <remarks>This method should only be used by advanced users who understand the details of the SMTP protocol.  
            This method can only be used with command that return a single line response. Before this method can be used
            you must be connected to an SMTP server.</remarks>
            <example><code>
            
            string Response = SMTP.SendSMTPCommand("RSET");
            
            </code>
            </example>
            <param name="Command">Command to send to SMTP server.</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <returns>Returns the mail server response.</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.SMTP">SMTP class</see>.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.SubmitToExpress(Quiksoft.EasyMail.SMTP.EmailMessage,System.String)">
            <summary>
            The SubmitToExpress method queues an e-mail message for delivery by SMTPExpress.
            </summary>
            <param name="MessageObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage</see> object to queue</param>
            <param name="SMTPExpressPath">The path to the EasyMail SMTPExpress directory on the computer where EasyMailSMTP Express is installed.</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when a file cannot be written to or read from.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.SubmitTo3rdPartyQueue(Quiksoft.EasyMail.SMTP.EmailMessage,System.String,Quiksoft.EasyMail.SMTP.SMTPQueueType)">
            <summary>
            The SubmitTo3rdPartyQueue method queues an e-mail message for delivery by 3rd party queuing agents.
            </summary>
            <param name="MessageObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage</see> object to queue</param>
            <param name="QueuePath">The path to the queue directory.  This value is the location to where the queuing agent picks up messages to be processed.</param>
            <param name="QueueType">One or more of the <see cref="T:Quiksoft.EasyMail.SMTP.SMTPQueueType"/> values specifying the queuing software you are using.</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when a file cannot be written to or read from.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel">
            <summary>
            Cancels the transmission of the message currently being sent. 
            </summary>
            <remarks>This will result in a <see cref="T:Quiksoft.EasyMail.SMTP.CancelException"/> being thrown.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.RelayMessage(System.String,System.String,System.String)">
            <summary>
            Relays or forwards a saved message to one or more recipients.
            </summary>
            <remarks>If you do not call <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Connect(System.Object)"/> prior to <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.RelayMessage(System.String,System.String,System.String)"/>, <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.RelayMessage(System.String,System.String,System.String)"/> will call <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Connect(System.Object)"/> and <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Disconnect"/> automatically.
            If you can not determine the problem based on the exception thrown, you should turn on logging by using the LogFile property.
            The progress of the transmission can be monitored by the <see cref="E:Quiksoft.EasyMail.SMTP.SMTP.SMTPStatus"/> event. The transmission may be cancelled by calling the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method.
            This method leaves the original message completely intact.
            </remarks>
            <param name="Filename">File name and path of message that should be relayed.</param>
            <param name="From">Sender's e-mail address.</param>
            <param name="Recipients">Recipient's e-mail address.  Multiple recipients may be specified in a comma delimited list.</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when file cannot be read.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException">Thrown when a authentication error occurs.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.SubmitBulkMerge(Quiksoft.EasyMail.SMTP.EmailMessage,System.Data.DataTable,System.String)">
            <summary>
            Allows you to send a bulk mailing by passing in a Datatable and a EmailMessage object. 
            </summary>
            <param name="MessageObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage</see> object that will be used a as template.</param>
            <param name="MergeTable">DataTable that contains the information to be merged in the e-mail message.</param>
            <param name="SMTPExpressPath">The path to where SMTP Express is installed.</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when a file cannot be written to or read from.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.BeginSubmitBulkMerge(Quiksoft.EasyMail.SMTP.EmailMessage,System.Data.DataTable,System.String,System.String,System.AsyncCallback,System.Object)">
            <summary>
            Allows you to send a bulk mailing by passing in a Datatable and a EMailMessage object. 
            </summary>
            <param name="MessageObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage</see> object that will be used a as template.</param>
            <param name="MergeTable">DataTable that contains the information to be merged in the e-mail message.</param>
            <param name="SMTPExpressPath">The path to where SMTP Express is installed.</param>
            <param name="Token">The token is a special string consisting of 1 or more characters. If the token is found within the body of the message, in any of its member values or sub object member values it will be replaced with the corresponding value taken from the data table.  The text between the token must represent the name of the column in the DataTable.  For example if the token is set to ##, then ##FROM## would be replaced with the value in the DataColumn named FROM. The token should be a character that will not be anywhere within the message other the merge fields. Default ##</param>
            <param name="callBack">The AsyncCallback delegate. </param>
            <param name="state">An object containing state information for this request.</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when a file cannot be written to or read from.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
            <returns>An IAsyncResult that references the asynchronous send.</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.EndSubmitBulkMerge(System.IAsyncResult)">
            <summary>
            Ends a pending asynchronous submitBulkMerge.
            </summary>
            <param name="result">An IAsyncResult that stores state information for this asynchronous operation. </param>
            <remarks>If the asynchronous operation has not been completed, this function will block until the result is available.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.SubmitBulkMerge(Quiksoft.EasyMail.SMTP.EmailMessage,System.Data.DataTable,System.String,System.String)">
            <summary>
            Allows you to send a bulk mailing by passing in a Datatable and a EMailMessage object. 
            </summary>
            <param name="MessageObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage</see> object that will be used a as template.</param>
            <param name="MergeTable">DataTable that contains the information to be merged in the e-mail message.</param>
            <param name="SMTPExpressPath">The path to where SMTP Express is installed.</param>
            <param name="Token">The token is a special string consisting of 1 or more characters. If the token is found within the body of the message, in any of its member values or sub object member values it will be replaced with the corresponding value taken from the data table.  The text between the token must represent the name of the column in the DataTable.  For example if the token is set to ##, then ##FROM## would be replaced with the value in the DataColumn named FROM. The token should be a character that will not be anywhere within the message other the merge fields. Default ##</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when a file cannot be written to or read from.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.SubmitBulkMerge(Quiksoft.EasyMail.SMTP.EmailMessage,System.Data.DataSet,System.String,System.String)">
            <summary>
            Allows you to send a bulk mail merge using a DataSet.
            </summary>
            <remarks>This method takes the datatable at index 0 and uses that for the bulk merge.</remarks>
            <param name="MessageObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage</see> object that will be used a as template.</param>
            <param name="MergeDataSet">DataTable that contains the information to be merged in the e-mail message.</param>
            <param name="SMTPExpressPath">The path to where SMTP Express is installed.</param>
            <param name="Token">The token is a special string consisting of 1 or more characters. If the token is found within the body of the message, in any of its member values or sub object member values it will be replaced with the corresponding value taken from the data table.  The text between the token must represent the name of the column in the DataTable.  For example if the token is set to ##, then ##FROM## would be replaced with the value in the DataColumn named FROM. The token should be a character that will not be anywhere within the message other the merge fields. Default ##</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when a file cannot be read.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException">Thrown when a authentication error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.SubmitBulkMerge(Quiksoft.EasyMail.SMTP.EmailMessage,System.Data.IDataReader,System.String,System.String)">
            <summary>
            Allows you to send a bulk merge using a IDataReader interface.
            </summary>
            <remarks>The IDataReader is convert to a dataset before the message is sent.</remarks>
            <param name="MessageObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage</see> object that will be used a as template.</param>
            <param name="MergeDataReader"></param>
            <param name="SMTPExpressPath">The path to where SMTP Express is installed.</param>
            <param name="Token">The token is a special string consisting of 1 or more characters. If the token is found within the body of the message, in any of its member values or sub object member values it will be replaced with the corresponding value taken from the data table.  The text between the token must represent the name of the column in the DataTable.  For example if the token is set to ##, then ##FROM## would be replaced with the value in the DataColumn named FROM. The token should be a character that will not be anywhere within the message other the merge fields. Default ##</param>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.SMTP.m_pBulkStatus">
            <summary>
            Private Member
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.ConvertDataReaderToDataSet(System.Data.IDataReader)">
            <summary>
            Converts a <see cref="!:DataReader"/> object to a dataset.
            </summary>
            <param name="reader">The <see cref="T:System.Data.IDataReader"/> object to convert.</param>
            <returns>A dataset filled with data from the <see cref="!:DataReader"/></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.SendBulkMerge(Quiksoft.EasyMail.SMTP.EmailMessage,System.Data.DataTable,System.Int32,System.Int32)">
            <summary>
            Allows you to send a bulk mail merge using a DataTable.
            </summary>
            <param name="MessageObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage</see> object that will be used a as template.</param>
            <param name="MergeTable">DataTable that contains the information to be merged in the e-mail message.</param>
            <param name="MessagesPerConnection">The number of messages to send on a single connection.  Some mail server only allow X number of messages per connection this settings will allow you to work around this limitation by automatically closing and reopening the connection after the number of messages specified.  Setting this value to 0 will send all messages on a single connection.</param>
            <param name="MillisecondPause">Amount of time to pause between connections.  If MessagesPerConnection is set to 0 this value is ignored.</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when a file cannot be read.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException">Thrown when a authentication error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.SendBulkMerge(Quiksoft.EasyMail.SMTP.EmailMessage,System.Data.DataSet,System.Int32,System.Int32,System.String)">
            <summary>
            Allows you to send a bulk mail merge using a DataSet.
            </summary>
            <remarks>This method takes the datatable at index 0 and uses that for the bulk merge.</remarks>
            <param name="MessageObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage</see> object that will be used a as template.</param>
            <param name="MergeDataSet">DataTable that contains the information to be merged in the e-mail message.</param>
            <param name="MessagesPerConnection">The number of messages to send on a single connection.  Some mail server only allow X number of messages per connection this settings will allow you to work around this limitation by automatically closing and reopening the connection after the number of messages specified.  Setting this value to 0 will send all messages on a single connection.</param>
            <param name="MillisecondPause">Amount of time to pause between connections.  If MessagesPerConnection is set to 0 this value is ignored.</param>
            <param name="Token">The token is a special string consisting of 1 or more characters. If the token is found within the body of the message, in any of its member values or sub object member values it will be replaced with the corresponding value taken from the data table.  The text between the token must represent the name of the column in the DataTable.  For example if the token is set to ##, then ##FROM## would be replaced with the value in the DataColumn named FROM. The token should be a character that will not be anywhere within the message other the merge fields. Default ##</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when a file cannot be read.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException">Thrown when a authentication error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.SendBulkMerge(Quiksoft.EasyMail.SMTP.EmailMessage,System.Data.IDataReader,System.Int32,System.Int32,System.String)">
            <summary>
            Allows you to send a bulk merge using a IDataReader interface.
            </summary>
            <remarks>The IDataReader is convert to a dataset before the message is sent.</remarks>
            <param name="MessageObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage</see> object that will be used a as template.</param>
            <param name="MergeDataReader"></param>
            <param name="MessagesPerConnection">The number of messages to send on a single connection.  Some mail servers only allow X number of messages per connection this settings will allow you to work around this limitation by automatically closing and reopening the connection after the number of messages specified.  Setting this value to 0 will send all messages on a single connection.</param>
            <param name="MillisecondPause">Amount of time to pause between connections.  If MessagesPerConnection is set to 0 this value is ignored.</param>
            <param name="Token">The token is a special string consisting of 1 or more characters. If the token is found within the body of the message, in any of its member values or sub object member values it will be replaced with the corresponding value taken from the data table.  The text between the token must represent the name of the column in the DataTable.  For example if the token is set to ##, then ##FROM## would be replaced with the value in the DataColumn named FROM. The token should be a character that will not be anywhere within the message other the merge fields. Default ##</param>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Quiksoft.EasyMail.SMTP.SMTP.BeginSendBulkMerge(Quiksoft.EasyMail.SMTP.EmailMessage,System.Data.DataTable,System.Int32,System.Int32,System.String,System.String,Quiksoft.EasyMail.SMTP.DSNNotifyFlags,Quiksoft.EasyMail.SMTP.DSNReturnFlags,System.Boolean,System.String,System.AsyncCallback,System.Object)" -->
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.EndSendBulkMerge(System.IAsyncResult)">
            <summary>
            Ends a pending asynchronous bulk merge.
            </summary>
            <param name="result">An IAsyncResult that stores state information for this asynchronous operation. </param>
            <remarks>If the asynchronous operation has not been completed, this function will block until the result is available.</remarks>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when a file cannot be read.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException">Thrown when a authentication error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Quiksoft.EasyMail.SMTP.SMTP.SendBulkMerge(Quiksoft.EasyMail.SMTP.EmailMessage,System.Data.DataTable,System.Int32,System.Int32,System.String,System.String,Quiksoft.EasyMail.SMTP.DSNNotifyFlags,Quiksoft.EasyMail.SMTP.DSNReturnFlags,System.Boolean,System.String)" -->
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.SendBulkMerge(Quiksoft.EasyMail.SMTP.EmailMessage,System.Data.DataTable,System.Int32,System.Int32,System.String)">
            <summary>
            Allows you to send a bulk mail merge using a DataTable.
            </summary>
            <param name="MessageObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage</see> object that will be used a as template.</param>
            <param name="MergeTable">DataTable that contains the information to be merged in the e-mail message.</param>
            <param name="MessagesPerConnection">The number of messages to send on a single connection.  Some mail servers only allow X number of messages per connection this settings will allow you to work around this limitation by automatically closing and reopening the connection after the number of messages specified.  Setting this value to 0 will send all messages on a single connection.</param>
            <param name="MillisecondPause">Amount of time to pause between connections.  If MessagesPerConnection is set to 0 this value is ignored.</param>
            <param name="Token">The token is a special string consisting of 1 or more characters. If the token is found within the body of the message, in any of its member values or sub object member values it will be replaced with the corresponding value taken from the data table.  The text between the token must represent the name of the column in the DataTable.  For example if the token is set to ##, then ##FROM## would be replaced with the value in the DataColumn named FROM. The token should be a character that will not be anywhere within the message other the merge fields. Default ##</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when a file cannot be read.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException">Thrown when a authentication error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.Send(Quiksoft.EasyMail.SMTP.EmailMessage)">
            <summary>
            Sends an e-mail message to an SMTP server.
            </summary>
            <remarks>If you do not call <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Connect(System.Object)"/> prior to <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Send(Quiksoft.EasyMail.SMTP.EmailMessage)"/>, the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Send(Quiksoft.EasyMail.SMTP.EmailMessage)"/> method will call <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Connect(System.Object)"/> and <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Disconnect"/> automatically.
            If you can not determine the problem based on the exception thrown, you should turn on logging by using the LogFile property.
            The progress of the transmission can be monitored by the <see cref="E:Quiksoft.EasyMail.SMTP.SMTP.SMTPStatus"/> event. The transmission may be cancelled by calling the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method.</remarks>
            <param name="MessageObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage</see> object to send.</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when a file cannot be read.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException">Thrown when a authentication error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.BeginSend(Quiksoft.EasyMail.SMTP.EmailMessage,System.AsyncCallback,System.Object)">
            <summary>
            Sends an e-mail message asynchronously to an SMTP server.
            </summary>
            <remarks>If you do not call <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Connect(System.Object)"/> prior to <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Send(Quiksoft.EasyMail.SMTP.EmailMessage)"/>, the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Send(Quiksoft.EasyMail.SMTP.EmailMessage)"/> method will call <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Connect(System.Object)"/> and <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Disconnect"/> automatically.
            If you can not determine the problem based on the exception thrown, you should turn on logging by using the LogFile property.
            The progress of the transmission can be monitored by the <see cref="E:Quiksoft.EasyMail.SMTP.SMTP.SMTPStatus"/> event. The transmission may be cancelled by calling the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method.</remarks>
            <param name="MessageObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage</see> object to send.</param>
            <param name="callBack">The AsyncCallback delegate. </param>
            <param name="state">An object containing state information for this request.</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when a file cannot be read.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException">Thrown when a authentication error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
            <returns>An IAsyncResult that references the asynchronous send.</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.EndSend(System.IAsyncResult)">
            <summary>
            Ends a pending asynchronous send.
            </summary>
            <param name="result">An IAsyncResult that stores state information for this asynchronous operation. </param>
            <remarks>If the asynchronous operation has not been completed, this function will block until the result is available.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.Send(Quiksoft.EasyMail.SMTP.EmailMessage,System.String,Quiksoft.EasyMail.SMTP.DSNNotifyFlags,Quiksoft.EasyMail.SMTP.DSNReturnFlags,System.Boolean)">
            <summary>
            Sends an e-mail message to an SMTP server.
            </summary>
            <remarks>If you do not call <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Connect(System.Object)"/> prior to <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Send(Quiksoft.EasyMail.SMTP.EmailMessage)"/>, the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Send(Quiksoft.EasyMail.SMTP.EmailMessage)"/> method will call <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Connect(System.Object)"/> and <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Disconnect"/> automatically.
            If you can not determine the problem based on the exception thrown, you should turn on logging by using the LogFile property.
            The progress of the transmission can be monitored by the <see cref="E:Quiksoft.EasyMail.SMTP.SMTP.SMTPStatus"/> event. The transmission may be cancelled by calling the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method.</remarks>
            <param name="MessageObj">A reference to the <see cref="T:Quiksoft.EasyMail.SMTP.EmailMessage">EmailMessage</see> object to send.</param>
            <param name="DSNID">Specifies the ESMTP Delivery status notification envelope ID for this message.</param>
            <param name="DSNNotify">Any combination of the <see cref="T:Quiksoft.EasyMail.SMTP.DSNNotifyFlags">DSNNotifyFlags</see> values specifying the events that trigger ESMTP delivery status notifications.</param>
            <param name="DSNReturn">One of the <see cref="T:Quiksoft.EasyMail.SMTP.DSNReturnFlags">DSNReturnFlags</see> values specifying how much of the original message is returned with ESMTP delivery status notifications.</param>
            <param name="Pipelining">Enables or disables ESMTP pipelining.</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.FileIOException">Thrown when file cannot be read.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException">Thrown when a authentication error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
            <exception cref="!:Quiksoft.EasyMail.SSL.SSLConnectionException">Thrown if an SSL connection error occurs.</exception>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Quiksoft.EasyMail.SMTP.SMTP.Send(Quiksoft.EasyMail.SMTP.EmailMessage,System.String,Quiksoft.EasyMail.SMTP.DSNNotifyFlags,Quiksoft.EasyMail.SMTP.DSNReturnFlags,System.Boolean,System.String)" -->
        <!-- Badly formed XML comment ignored for member "M:Quiksoft.EasyMail.SMTP.SMTP.BeginSend(Quiksoft.EasyMail.SMTP.EmailMessage,System.String,Quiksoft.EasyMail.SMTP.DSNNotifyFlags,Quiksoft.EasyMail.SMTP.DSNReturnFlags,System.Boolean,System.String,System.AsyncCallback,System.Object)" -->
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.Connect(System.Object)">
            <summary>
            Attempts to connect to an SMTP mail server.
            </summary>
            <param name="SSLInterface">The SSL interface to use for this connection.  The return value of <see cref="!:Quiksoft.EasyMail.SSL.GetInterface"/></param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException">Thrown when an authentication error occurs.</exception>
            <exception cref="!:Quiksoft.EasyMail.SSL.SSLConnectionException">Thrown if an SSL connection error occurs.</exception>
            <remarks>The connect method will try each SMTPServer in the <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServerCollection"/> until a connection is made.  Once a connection is made it will initiate an SMTP session and perform the specified authentication.  Any connection opened with the Connect method must be terminated with the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Disconnect"/> method.
            If a connection already exists, it will be automatically terminated, before the new connection is made.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.Connect(System.Object,System.String)">
            <summary>
            Attempts to connect to an SMTP mail server.
            </summary>
            <param name="Domain">The domain used in the HELO command to initiate a conversation with a SMTP mail server.  Default: The DNS host name of the local computer.</param>
            <param name="SSLInterface">The SSL interface to use for this connection.  The return value of <see cref="!:Quiksoft.EasyMail.SSL.GetInterface"/></param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException">Thrown when an authentication error occurs.</exception>
            <exception cref="!:Quiksoft.EasyMail.SSL.SSLConnectionException">Thrown if an SSL connection error occurs.</exception>
            <remarks>The connect method will try each SMTPServer in the <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServerCollection"/> until a connection is made.  Once a connection is made it will initiate an SMTP session and perform the specified authentication.  Any connection opened with the Connect method must be terminated with the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Disconnect"/> method.
            If a connection already exists, it will be automatically terminated, before the new connection is made.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.Connect">
            <summary>
            Attempts to connect to an SMTP mail server.
            </summary>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException">Thrown when an authentication error occurs.</exception>
            <remarks>The connect method will try each SMTPServer in the <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServerCollection"/> until a connection is made.  Once a connection is made it will initiate an SMTP session and perform the specified authentication.  Any connection opened with the Connect method must be terminated with the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Disconnect"/> method.
            If a connection already exists, it will be automatically terminated, before the new connection is made.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.Connect(System.String)">
            <summary>
            Attempts to connect to an SMTP mail server.
            </summary>
            <param name="Domain">The domain used in the HELO command to initiate a conversation with a SMTP mail server.  Default: The DNS host name of the local computer.</param>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolException">Thrown when there is an SMTP protocol error.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPConnectionException">Thrown when a connection error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.CancelException">Thrown when the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Cancel"/> method is called.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.SMTPAuthenticationException">Thrown when an authentication error occurs.</exception>
            <exception cref="T:Quiksoft.EasyMail.SMTP.LicenseException">Thrown if the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.</exception>
            <remarks>The connect method will try each SMTPServer in the <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServerCollection"/> until a connection is made.  Once a connection is made it will initiate an SMTP session and perform the specified authentication.  Any connection opened with the Connect method must be terminated with the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Disconnect"/> method.
            If a connection already exists, it will be automatically terminated, before the new connection is made.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.ValidateEmailAddress(System.String,System.String,Quiksoft.EasyMail.SMTP.AddressValidationOptions)">
            <summary>
            Validates an e-mail address and returns a status code.
            </summary>
            <param name="EmailAddress">The EMail address you would like to verify.</param>
            <param name="Expression">The regular expression used to validate the email address.</param>
            <param name="Options">An <see cref="T:Quiksoft.EasyMail.SMTP.AddressValidationOptions"/> enumeration that specifies advanced options.</param>
            <remarks>This function only validates the syntax of the e-mail address.  This does not
            guarantee the message will be delivered.  There are many other factors that could cause the address to fail.</remarks>
            <returns>1 if address is invalid <br/> 0 if address is valid.</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.ValidateEmailAddress(System.String)">
            <summary>
            Validates an e-mail address and returns a status code.
            </summary>
            <param name="EmailAddress">The EMail address you would like to verify.</param>
            <remarks>This function only validates the syntax of the e-mail address.  This does not
            guarantee the message will be delivered.  There are many other factors that could cause the address to fail.</remarks>
            <returns>1 if address is invalid <br/> 0 if address is valid.</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.SMTP.Disconnect">
            <summary>
            Disconnects from the mail server.
            </summary>
            <remarks>The Disconnect() method terminates any active SMTP session and drops any existing connection.
            This method should be called for any connection opened with the <see cref="M:Quiksoft.EasyMail.SMTP.SMTP.Connect(System.Object)"/> method.
            </remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTP.Version">
            <summary>
            Gets the current version of this assembly.
            </summary>
        </member>
        <member name="E:Quiksoft.EasyMail.SMTP.SMTP.ProtocolLog">
            <summary>
             Adds or removes a <see cref="T:Quiksoft.EasyMail.SMTP.SMTPProtocolLog"/> delegate that is sent protocol data as the assembly communicates with the SMTP server.
            </summary>
        </member>
        <member name="E:Quiksoft.EasyMail.SMTP.SMTP.SMTPStatus">
            <summary>
             Adds or removes a <see cref="E:Quiksoft.EasyMail.SMTP.SMTP.SMTPStatus">SMTPStatus</see> delegate that is called with progress data as an e-mail message is being sent.
            </summary>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTP.SMTPServers">
            <summary>
            Gets the <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServerCollection">SMTPServerCollection</see> object.
            </summary>
            <value>The <see cref="T:Quiksoft.EasyMail.SMTP.SMTPServerCollection"/> object</value>
            <example>
            <code>
            The following example shows how to set a default value using the appSettings section of a configuration file.
            &lt;?xml version="1.0" encoding="utf-8" ?&gt;
            	&lt;configuration&gt;
            		&lt;appSettings&gt;
            		&lt;add key="Quiksoft.EasyMail.SMTP.SMTP.MailServers" value="mail.quiksoft.com,26,account,pass,AuthLogin" /&gt;
            		&lt;/appSettings&gt;
            	&lt;/configuration&gt;
            </code></example>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTP.DNSServers">
            <summary>
            Gets the <see cref="T:Quiksoft.EasyMail.SMTP.DnsServerCollection">DnsServerCollection</see> object.
            </summary>
            <value>The <see cref="T:Quiksoft.EasyMail.SMTP.DnsServerCollection"/> object</value>
            <remarks>This value can also be set using the machine, app, or web config files.</remarks>
            <example>
            <code>
            The following example shows how to set 2 default DNS servers using the appSettings section of a configuration file.
            &lt;?xml version="1.0" encoding="utf-8" ?&gt;
            	&lt;configuration&gt;
            		&lt;appSettings&gt;
            		&lt;add key="Quiksoft.EasyMail.SMTP.SMTP.DnsServer" value="127.0.0.1;10.10.10.1" /&gt;
            		&lt;/appSettings&gt;
            	&lt;/configuration&gt;
            </code></example>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.SMTP.LogFile">
            <summary>
            Get or sets the path and filename of log file to output all transactions with the server.
            </summary>
            <remarks>Path to log file must already exist. The file will be created if it does not already exist, otherwise it will be appended to.</remarks>
            <example>
            <code>
            The following example shows how to set this value using the appSettings section of a configuration file.
            &lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot; ?&gt;
            	&lt;configuration&gt;
            		&lt;appSettings&gt;
            		&lt;add key=&quot;Quiksoft.EasyMail.SMTP.SMTP.LogFile&quot; value=&quot;C:\imap4.log&quot; /&gt;
            		&lt;/appSettings&gt;
            	&lt;/configuration&gt;
            </code>
            </example>
        </member>
        <member name="E:Quiksoft.EasyMail.SMTP.SMTP.SMTPBulkStatus">
            <summary>
             Adds or removes a <see cref="E:Quiksoft.EasyMail.SMTP.SMTP.SMTPStatus">SMTPStatus</see> delegate that is called with progress data as an e-mail message is being sent.
            </summary>
        </member>
        <member name="E:Quiksoft.EasyMail.SMTP.SMTP.SMTPBulkDetails">
            <summary>
            Adds or removes a <see cref="T:Quiksoft.EasyMail.SMTP.SMTPBulkStatusDetails">SMTPBulkStatusDetails</see> delegate that is called with detailed information as a bulk merge is being sent.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.DSNNotifyFlags">
            <summary>
            Provides enumerated values that control what events trigger ESMTP delivery status notifications.
            </summary>
            <remarks>Values 2, 4 and 8 may be combined to customize the notification rules.
            The setting of this property applies to all recipients of the message.
            If ESMTP delivery status notifications are not supported by the server, this value is ignored.
            </remarks>
            
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DSNNotifyFlags.None">
            <summary>
            Do not send command to server.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DSNNotifyFlags.Default">
            <summary>
            Send the command but do what ever the server default is.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DSNNotifyFlags.Failure">
            <summary>
            Notify if message fails.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DSNNotifyFlags.Success">
            <summary>
            Notify if message is sent successfully.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DSNNotifyFlags.Delay">
            <summary>
            Notify if message is delayed.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DSNNotifyFlags.Never">
            <summary>
            Do not notify me.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.DSNReturnFlags">
            <summary>
            Provides enumerated values that control how much of the original message is returned with ESMTP delivery status notifications.
            </summary>
            <remarks>
            The setting of this property applies to all recipients of the message.
            If ESMTP delivery status notifications are not supported by the server, this value is ignored.
            </remarks>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DSNReturnFlags.None">
            <summary>
            Do not send command to server.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DSNReturnFlags.Default">
            <summary>
            Return what ever the server default is.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DSNReturnFlags.Header">
            <summary>
            Return only the header of the message.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DSNReturnFlags.Message">
            <summary>
            Return the full message.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.OptionFlags">
            <summary>
            Provides enumerated values that set various options.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.OptionFlags.None">
            <summary>
            No flags specified.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.OptionFlags.NoFrom">
            <summary>
            Message will not include a "From:" header.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.OptionFlags.NoDate">
            <summary>
            Message will not include a "Date:" header.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.OptionFlags.NoMessageID">
            <summary>
            A "Message-ID" header will not be automatically added to messages created by the object. You can add a custom "Message-ID" header by using the Custom Header collection.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.OptionFlags.NoRecipients">
            <summary>
            Message will not include a "To", "CC", or "BCC" header.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.OptionFlags.NoAuthInFrom">
            <summary>
            When this option is specified along with ESMTP authentication, the authentication account name will not be appended to the end of the MAIL FROM command. This option is to be used for servers which reject the MAIL FROM command when the authentication account name is appended.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.OptionFlags.AddBCC">
            <summary>
            When this option is specified a "BCC" header will be added to the message containing the blind carbon copy recipients.  
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.CancelException">
            <summary>
            Thrown when the cancel method is called.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.ParseException">
            <summary>
            Thrown when the cancel method is called.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.ProtocolException">
            <summary>
            Exception related to the Specific Protocol.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.emSocket">
            <summary>
            Quiksoft Socket Class.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.emSocket.BUFFER_SIZE">
            <summary>
            Size of buffers to use.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.emSocket.m_pWriteToLog">
            <summary>
            Allows class to write to a log file.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.emSocket.m_pNetStream">
            <summary>
            Holds the network stream.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.emSocket.ActiveProtocol(System.Int32)">
            <summary>
            1-Unkown, 2-SMTP, 3=POP3, 4=IMAP4
            </summary>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.emSocket.Flush">
            <summary>
            Clears out stream buffers.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.emSocket.SetTimeoutValue(System.Int32)">
            <summary>
            Sets the timeout value of the socket.
            </summary>
            <param name="m_nMilliseconds">Millisecond timeout value</param>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.emSocket.recv(System.String@,System.Int32)">
            <summary>
            Receives one SMTP line of data from the socket.  
            </summary>
            <param name="strData">Data received</param>
            <param name="nBufferSize">Max size to receive</param>
            <returns>0 on success</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.emSocket.send(System.String,System.Boolean,System.Text.Encoding)">
            <summary>
            
            </summary>
            <param name="strData"></param>
            <param name="bLog">Log the data.  Also means its a protocol send and should be send right away.
            This determines if we should buffer or not too.</param>
            <param name="CharsetEncoding"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.emSocket.Quiksoft#EasyMail#SMTP#ISMTPSSL#Close">
            <summary>
            Quiksoft Socket Class.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.emSocket.Quiksoft#EasyMail#SMTP#ISMTPSSL#Shutdown(System.Net.Sockets.SocketShutdown)">
            <summary>
            Quiksoft Socket Class.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.emSocket.Quiksoft#EasyMail#SMTP#ISMTPSSL#Receive(System.Byte[],System.Int32,System.Net.Sockets.SocketFlags)">
            <summary>
            Quiksoft Socket Class.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.DnsServerCollection">
            <summary>
            Represents a collection of <see cref="T:Quiksoft.EasyMail.SMTP.DnsServer">DnsServer</see> objects.
            </summary>
            <remarks>When the DNS server collection is populated with at least one valid DNS server the SMTP object
            will perform an MX record lookup on the recipients domain and attempt to connect directly to the recipients mail server.
            This method should be used with caution because not all mail servers will except mail from senders they can not validate
            as authentic mail servers.
            </remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.DnsServerCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.DnsServerCollection">DnsServerCollection class</see> 
            </summary>
            <remarks>Add multiple DnsServer objects to this collection for failover querys.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.DnsServerCollection.Add(System.String)">
            <summary>
            Creates and adds a <see cref="T:Quiksoft.EasyMail.SMTP.DnsServer">DnsServer object</see> to the collection.
            </summary>
            <param name="IPAddress">Specifies the IP address of the DNS server.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.DnsServerCollection.Add(System.String,System.Int32)">
            <summary>
            Creates and adds a <see cref="T:Quiksoft.EasyMail.SMTP.DnsServer"/> class to the collection.
            </summary>
            <param name="IPAddress">IP address of DNS server</param>
            <param name="Timeout">The timeout value in milliseconds.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.DnsServerCollection.LoadFromWin32API">
            <summary>
            Attempts to load the DNS server using the Windows IP Helper API.
            </summary>
            <returns>True on success or False on failure.</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.DnsServerCollection.LoadFromRegistry">
            <summary>
            Attempts to load the DNS server settings from the win32 system registry.
            </summary>
            <returns>True if the settings were successfully retrieved.</returns>
            <remarks>May not work with all OS's and network configurations.  Requires registry access.</remarks>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.DnsServerCollection.Add(Quiksoft.EasyMail.SMTP.DnsServer)">
            <summary>
            Adds a <see cref="T:Quiksoft.EasyMail.SMTP.DnsServer">DnsServer object</see> to the collection.
            </summary>
            <param name="DNSServerObj">A reference to an <see cref="T:Quiksoft.EasyMail.SMTP.DnsServer"/> object to add to the collection.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.DnsServerCollection.CacheTimeout">
            <summary>
            Gets or sets the DNS cache timout period. The default is 120 minutes.
            </summary>
            <value>The amount of time in minutes that MX records should be cached.  </value>
            <remarks></remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.DnsServerCollection.UseRandomServer">
            <summary>
            Gets or sets a boolean value that specifies if a random dns server should be used when multiple servers are provided.
            </summary>
            <value>
            When set to true a random DNS server is used from the collection.  When set to false the servers will be attempted in the order they were added
            to the collection.
            </value>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.DnsServerCollection.CacheDns">
            <summary>
            Gets or sets a boolean value that turns on or off DNS caching.  Default: true.
            </summary>
            <value>A boolean value that when set to true will turn on DNS caching and when set to false will turn off DNS caching.</value>
            <remarks>Using a DNS cache will greatly improve performance.</remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.DnsServerCollection.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:Quiksoft.EasyMail.SMTP.DnsServer"/> object at the specified position.
            </summary>
            <param name="index">An ordinal index value that specifies which <see cref="T:Quiksoft.EasyMail.SMTP.DnsServer"/> to return.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.DnsServer">
            <summary>
            Provides properties and methods for an SMTP mail server that will be used when sending e-mail messages.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.DnsServer.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.DnsServer">DnsServer class</see> with the name, port number and timeout value for the SMTP server.
            </summary>
            <param name="IPAddress">IP address of the DNS server</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.DnsServer.#ctor(System.String,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.DnsServer">DnsServer class</see> with the name, port number and timeout value for the SMTP server.
            </summary>
            <param name="IPAddress">IP address of the DNS server</param>
            <param name="Timeout">The timeout value in milliseconds.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.DnsServer.Timeout">
            <summary>
            Gets or sets the timeout value.
            </summary>
            <value>The connection time out value in milliseconds.  The default value is system dependant.  See remarks.</value>
            <remarks>Sets the maximum amount of time that the component will wait for a response from the DNS server, before returning an error. 
            Some devices running the Compact Framework may not support the setting of a timeout.  If you are targeting the Compact Framework
            You should check the default value of this property prior to setting it.  If the default value is less than zero,
            then setting the timeout is not supported and any attempts to do so will result in a SocketException being thrown.
            </remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.DnsServer.IPAddress">
            <summary>
            Gets or sets the IP address of the DNS server.
            </summary>
            <value>Name or address of the DNS server</value>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.ValidateL">
            <summary>
            Summary description for ValidateL.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.LicenseException">
            <summary>
            The exception that is thrown when the static class <see cref="T:Quiksoft.EasyMail.SMTP.License"/> is not set with a valid license key.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.LicenseException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Quiksoft.EasyMail.SMTP.LicenseException"/> class with a specified error message.
            </summary>
            <param name="Description">The error message string.</param>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.License">
            <summary>
            Provides a static property for setting the license key.
            </summary>
            <remarks>
            
            A license key is similar to a registration code, or serial number. It is a unique string that identifies the 
            licensee and the type of license. The <see cref="P:Quiksoft.EasyMail.SMTP.License.Key"/> property must be assigned a valid license key to unlock the assembly.
            Both permanent and trial (temporary) license keys can be obtained from Quiksoft.
            If a valid license key is not properly assigned to the <see cref="P:Quiksoft.EasyMail.SMTP.License.Key"/> property, or if there is a error in the format
            of the license key, the assembly will not be unlocked, and will throw a <see cref="T:Quiksoft.EasyMail.SMTP.LicenseException"/> from most method calls.
            When using a license key, please remember these important points:  
            The license key is case sensitive.  The license key is unique to each licensee and product.  
            The license key can not contain any line breaks.  Your license key must be assigned to the <see cref="P:Quiksoft.EasyMail.SMTP.License.Key"/> property
            exactly as you received it without modification of any sort.
            The <see cref="P:Quiksoft.EasyMail.SMTP.License.Key"/> property should be assigned before the assembly is used.
            The License object is static, so there is no need to create an instance of it prior to assigning the <see cref="P:Quiksoft.EasyMail.SMTP.License.Key"/> property.
            </remarks>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.License.Key">
            <summary>
            Gets and sets the license key.
            </summary>
            <value>This property holds the license key for the assembly. If this property is not assigned a valid license key, the object will throw a <see cref="T:Quiksoft.EasyMail.SMTP.LicenseException"/> from most methods.</value>		
            <remarks>
            In addition to setting this property in your code you can also set it in the appSettings section of the Machine.config, App.Config and Web.Config files.
            Just add a key value pair for Quiksoft.EasyMail.NetEdition3.License.Key. See example for more information.<BR/>
            The <see cref="T:Quiksoft.EasyMail.SMTP.License"/> object and the Key property are static.  You can not create an instance of the <see cref="T:Quiksoft.EasyMail.SMTP.License"/> object.  For more information on license keys, see the remarks on the <see cref="T:Quiksoft.EasyMail.SMTP.License"/> object.
            </remarks>
            <example>
            Setting the license key in your code:
            <code>
            Quiksoft.EasyMail.SMTP.License.Key = "Your Company/12F3234AB23E23"
            </code>
            Setting the license key in a config file:
            <code>
            
            &lt;configuration&gt;
            	&lt;appSettings&gt;
            		&lt;add key="Quiksoft.EasyMail.NetEdition3.License.Key" value="Your Company/12F3234AB23E23" /&gt;
            	&lt;/appSettings&gt;
            &lt;/configuration&gt;
            </code>
            </example>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.MemoryWrite">
            <summary>
            Used to write a message to memory.
            Uses StringBuilder for better performance
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.MemoryWrite.ActiveProtocol(System.Int32)">
            <summary>
            1-Unkown, 2-SMTP, 3=POP3, 4=IMAP4
            </summary>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.FileWrite.ActiveProtocol(System.Int32)">
            <summary>
            1-Unkown, 2-SMTP, 3=POP3, 4=IMAP4
            </summary>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.GenericStream.ActiveProtocol(System.Int32)">
            <summary>
            1-Unkown, 2-SMTP, 3=POP3, 4=IMAP4
            </summary>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.GenericStream.Receive(System.Byte[],System.Int32,System.Net.Sockets.SocketFlags)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <param name="c"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.GenericStream.IsConnected">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.GenericStream.connect(System.String,System.Int32)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.GenericStream.GetTimeoutValue">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.GenericStream.SetWriteToLog(System.Object)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.GenericStream.SetTimeoutValue(System.Int32)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.GenericStream.Shutdown(System.Net.Sockets.SocketShutdown)">
            <summary>
            Internal Interface that should be ignored.
            </summary>
            <param name="a"></param>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.HMAC_MD5">
            <summary>
            
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.HMAC_MD5.digest">
            Digest to be returned upon completion of the HMAC_MD5.
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.HMAC_MD5.kIpad">
            Inner Padding.
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.HMAC_MD5.kOpad">
            Outer Padding.
        </member>
        <member name="F:Quiksoft.EasyMail.Internal.HMAC_MD5.innerMD5">
            Inner MD5 object.
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.HMAC_MD5.#ctor(System.Byte[])">
            Constructor
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.HMAC_MD5.clear">
            Clear the HMAC_MD5 object.
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.HMAC_MD5.addData(System.Byte[])">
             HMAC_MD5 function.
            
             @param text Text to process
            
             @param key Key to use for HMAC hash.
            
             @return hash
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.HMAC_MD5.addData(System.Byte[],System.Int32,System.Int32)">
             HMAC_MD5 function.
            
             @param text Text to process
            
             @param textStart   Start position of text in text buffer.
             @param textLen Length of text to use from text buffer.
             @param key Key to use for HMAC hash.
            
             @return hash
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.HMAC_MD5.verify(System.Byte[])">
             Validate a signature against the current digest.
             Compares the hash against the signature.
            
             @param signature
            
             @return True if the signature matches the calculated hash.
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.HMAC_MD5.ToString">
              Return the digest as a HEX string.
            
             @return a hex representation of the MD5 digest.
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.MD5.#ctor">
             MD5 initialization. Begins an MD5 operation, writing a new context.
            
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.MD5.update(System.Byte[])">
             MD5 block update operation. Continues an MD5 message-digest
             operation, processing another message block, and updating the
             context.
            
             @param input byte array of data
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.MD5.update(System.Byte[],System.Int32,System.Int32)">
             MD5 block update operation.
            
             @param input byte array of data
             @param offset offset into the array to start the digest calculation
             @param inputLen byte count to use in the calculation
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.MD5.digest">
             MD5 finalization. Ends an MD5 message-digest operation, writing the
             the message digest and zeroizing the context.
            
             @return the digest
            
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.DnsException">
            <summary>
            The exception that is thrown when an error occurs performing a DNS lookup.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.DnsException.#ctor">
            <summary>
            Initializes a new instance of the DnsException class.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.DnsException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the DnsException class with a specified error message.
            </summary>
            <param name="description">The error message string.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.DnsException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the DnsException class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="description">The error message string.</param>
            <param name="inner">The inner exception reference.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.DnsException.#ctor(System.String,Quiksoft.EasyMail.SMTP.DnsErrorCode)">
            <summary>
            Initializes a new instance of the DnsException class with a specified error message 
            and error code.
            </summary>
            <param name="description">The error message string.</param>
            <param name="errorCode">One of the <see cref="T:Quiksoft.EasyMail.SMTP.DnsErrorCode"/> that describes why this exception was thrown.</param>
        </member>
        <member name="M:Quiksoft.EasyMail.SMTP.DnsException.#ctor(System.String,System.Exception,Quiksoft.EasyMail.SMTP.DnsErrorCode)">
            <summary>
            Initializes a new instance of the DnsException class with a specified error message 
            and error code.
            </summary>
            <param name="description">The error message string.</param>
            <param name="inner">The inner exception reference.</param>
            <param name="errorCode">One of the <see cref="T:Quiksoft.EasyMail.SMTP.DnsErrorCode"/> that describes why this exception was thrown.</param>
        </member>
        <member name="P:Quiksoft.EasyMail.SMTP.DnsException.ErrorCode">
            <summary>
            Retrieves error code.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.SMTP.DnsErrorCode">
            <summary>
            Enumerated values that specify the DNS error that occurred.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DnsErrorCode.Success">
            <summary>
            Success, no error.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DnsErrorCode.Format">
            <summary>
            The name server was unable to process this query due to a problem with the name server.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DnsErrorCode.ServerFail">
            <summary>
            The DNS server failed.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DnsErrorCode.NameError">
            <summary>
            Meaningful only for responses from an authoritative name server, this code signifies that the domain name referenced in the query does not exist.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DnsErrorCode.NotImplemented">
            <summary>
            The name server does not support the requested kind of query. 
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DnsErrorCode.Refused">
            <summary>
             The name server refuses to perform the specified operation for policy reasons. For example, a name server may not wish to provide the information to the particular requester, or a name server may not wish to perform a particular operation (for example, zoneMockapetris [Page 27] transfer) for particular data.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DnsErrorCode.ConnectionError">
            <summary>
            There was an error connecting to the DNS server.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DnsErrorCode.Unknown">
            <summary>
            There was an unknown error.
            </summary>
        </member>
        <member name="F:Quiksoft.EasyMail.SMTP.DnsErrorCode.NoNameServer">
            <summary>
            The name server could not be found or was not set.
            </summary>
        </member>
        <member name="T:Quiksoft.EasyMail.Internal.Encoder">
            <summary>
            Summary description for Encoder.
            </summary>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Encoder.ToBase64Wrap(System.String,Quiksoft.EasyMail.SMTP.ISMTPSSL,Quiksoft.EasyMail.Internal.SMTP.Status,System.Int64@,System.Int64,System.Text.Encoding)">
            <summary>
            Converts a unicode string to Base64.
            IT wraps the lines at 75 characters.
            </summary>
            <param name="strData">Data to encode</param>
            <param name="pIOControl">Stream object to write to.</param>
            <param name="pStatus"></param>
            <param name="nBytesSent"></param>
            <param name="nTotalBytes"></param>
            <param name="CharsetEncoding"></param>
            <returns></returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Encoder.QuotedCharacter(System.Int32)">
            <summary>
            Converts a character to its quoted-printable equivalent
            </summary>
            <param name="pChar">Character to convert</param>
            <returns>return equal sign plus character code with 2 decimal digits.</returns>
        </member>
        <member name="M:Quiksoft.EasyMail.Internal.Encoder.SendBase64File(System.String,Quiksoft.EasyMail.SMTP.ISMTPSSL,Quiksoft.EasyMail.Internal.SMTP.Status,System.Int64@,System.Int64)">
            <summary>
            Base 64 encodes a file and wraps at 75 characters.
            Sends output to io stream.
            </summary>
            <param name="strFile">File Name to Send</param>
            <param name="pIOControl">Stream Object</param>
            <param name="pStatus"></param>
            <param name="nBytesSent"></param>
            <param name="nTotalBytes"></param>
            <returns></returns>
        </member>
    </members>
</doc>
