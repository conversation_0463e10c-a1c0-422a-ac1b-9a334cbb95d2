﻿@using Kendo.Mvc.UI;

@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@model Selltis.MVC.Models.NewEditDesktop
@*<script src="https://kendo.cdn.telerik.com/2015.3.930/js/kendo.all.min.js"></script>

<script src="https://kendo.cdn.telerik.com/2015.3.930/js/kendo.web.min.js" type="text/javascript"></script>
<script src="https://kendo.cdn.telerik.com/2015.3.930/js/kendo.aspnetmvc.min.js" type="text/javascript"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/2.4.0/jszip.js"></script>*@
<style>
    .row {
        margin-top: 5px;
    }

    hr {
        margin: 15px 0px;
    }

    .k-button {
        color: #fff;
        background: #3498db;
        border-color: #3498db;
    }

        .k-button:hover {
            background-color: #2384c6;
            color: #fff;
            border-color: #3498db;
        }

    input[type="checkbox"] {
        vertical-align: sub;
    }    
</style>
<style>
    div.k-multiselect div.btn-group {
        display: none !important;
    }
    .k-button {
        background-color: #428BCA !important;
    }

    .k-multiselect .k-button {
        background-color: #428bca !important;
    }


    #divfrmtitle {
        line-height: 38px;
        font-weight: 600 !important;
        font-size: 15px !important;
    }

    .panel-heading.panelhead {
        background: #e9e9e9 !important;
        height: 38px;
    }

      .panel.panel-visible {
    margin-bottom: 5px !important;
    background-color: #e7e7e7;
    height: 38px;
}

       .headrow {
    margin-top: 0px !important;
}

        .panel-title {
    line-height: 34px !important;
}

        #content {
    padding-left: 0px;
    padding-top: 0px;
    
}
        /* specific to column1 and column2 button dropdowns..J */ 
        .actions > a > i, .actions > li > a > i {
        color: white;
        font-size: 18px;
    }

        /* specific to remove scroll in body..J */
        body {
        overflow: hidden !important;
    }
    button.multiselect.dropdown-toggle.btn.btn-default {
    display: none !important;
    }
</style>

<script>
    function CMB_WCBUTTON_SelectedIndexChanged(e) {
        //debugger
        //var CMBWCBUTTONSelectedValue = $("#Undernode").data("kendoMultiSelect");                       
    }

    //09102018 tckt #2490: Desktop order is changing when edit the the desktop from desktopmanager..J
    function CMB_WCBUTTON_onDeselect(e) {        
        ManageDesktopsUnderFolders(e, "DESELECT");
    }

    function CMB_WCBUTTON_onSelect(e) {
        ManageDesktopsUnderFolders(e, "SELECT");
    }

    function ManageDesktopsUnderFolders(e, ManageType){
        var dataItem = e.dataItem;
        var RemovedFolderName =  dataItem.Text;
        var EdtNameText = $("#Name").val();
        var CHKSHAREDChecked = $("#ChkSharedChecked").prop('checked');
        var DesktopId = $("#SelectedDesktopId").val();
        
        $.ajax({
            url: "/ManageDesktops/ManageDesktopUnderFolder",
            data: {
                EdtNameText: EdtNameText,
                CHKSHAREDChecked: CHKSHAREDChecked,
                FolderName: RemovedFolderName,
                DesktopId: DesktopId,
                ManageType: ManageType
            },
            cache: false,
            type: "post",
            success: function (data) {
              
            },
            error: function (data) {
                
            }
        })
    }

    function BTN_WCBUTTON_Click() {
        var model = $("#ModalMenuManager");
        model.modal('show');
    }
</script>

<div>
    <section id="content_wrapper">
        <section id="content" class="animated fadeIn">
            <div class="tray tray-center">

                


                <div class="row headrow">
                    <div class="col-md-12">
                        <div class="panel panel-visible" id="spy2" style="margin-bottom: 5px!important;margin-right:-10px;">
                            <div class="topbar-left">

                                <div class="panel-heading panelhead" style="background: #e9e9e9!important;height:38px;">
                                    <div class="panel-heading-btn" style="float:right;margin-top: 3px;">

                                        @if (Model.BtnOkEnabled)
                                        {
                                            <button id="btn_OK" type="button" class="btn btn-sm btn-success" title="Save" onclick="btn_OK_Click()"><i class="glyphicon glyphicon-floppy-disk"></i></button>
                                        }
                                        else
                                        {
                                            <button id="btn_OK" type="button" class="btn btn-sm btn-success" title="Save" onclick="btn_OK_Click()" disabled="disabled"><i class="glyphicon glyphicon-floppy-disk"></i></button>
                                        }

                                        @if (Model.BtnSaveAsEnabled)
                                        {
                                            <button class="btn btn-sm btn-primary" type="button" id="BTN_SAVEAS" style="display:none; float: right; margin-left: 10px;" title="Save as" onclick="BTN_SAVEAS_Click()"><i class="fa-hdd-o"></i></button>
                                        }
                                        else
                                        {
                                            <button class="btn btn-sm btn-primary" type="button" id="BTN_SAVEAS" style="float: right; margin-left: 10px; display: none;" title="Save as" onclick="BTN_SAVEAS_Click()" disabled="disabled"><i class="fa-hdd-o"></i></button>
                                        }
                                        <button id="CancelClick" type="button" class="btn btn-sm btn-danger" title="Cancel" onclick="onCancel()"><i class="fa fa-times"></i></button>
                                        @if (Model.BtnMetaVisible)
                                        {
                                            <button id="BTN_META" type="button" class="btn btn-sm btn-primary" title="View Metadata" onclick="BTN_META_Click()"><i class="fa fa-cube"></i></button>
                                            @*<img id="BTN_META" src="~/Content/Images/Cube.gif" style="margin-top: 2px;cursor:pointer;" title="View Metadata" onclick="BTN_META_Click()" />*@
                                        }

                                        @*<div style="float: right; margin-left: 10px; /*margin-right: 30px;*/ margin-top: 2px;cursor:pointer;" title="Help" onclick="window.open('http://help.selltis.com/sales/index.html#context/userguide/1012_WVIEWPRO_ViewProperties', '_blank'); return false;">
                    <i class="fa fa-2x fa-question-circle"></i>
                </div>*@
                                        <!--Changed from help.selltis.com/sale/index.html to below link. ref to tickt #2235: user manual link is  broken.  When click on Help and Index ..J-->
                                        <button id="btn_Help" type="button" class="btn btn-sm btn-primary" title="Help" onclick="window.open('http://selltis.com/wp-content/uploads/2017/05/Selltis-CRM-Users-Guide.pdf#page=34', '_blank'); return false;"><i class="fa fa-question"></i></button>

                                    </div>
                                    <h5 id="divfrmtitle" style="line-height:38px;font-weight:bold;" class="panel-title"><i class="glyphicon glyphicon-th-large"></i>&nbsp;&nbsp;@ViewBag.Title</h5>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>


                @*<div class="panel-heading" style="height:30px">
                    <span class="panel-title fw600">
                        @ViewBag.Title
                    </span>
                    <div class="pull-right hidden-xs">
                        <button type="button" class="btn btn-sm btn-primary" title="Cancel" onclick="onCancel()"><i class="fa fa-times"></i></button>
                    </div>
                </div>*@
                <div class="row" style="margin-left:17px;">
                    <div class="col-md-1"></div>
                    <div class="col-md-10" style="margin-top: -14px;">
                        @using (Html.BeginForm("", "", FormMethod.Post, new { id = "saveDesktopManageLayout" }))
                        {
                            //Reset to defaults messagebox Starts
                            <div class="row">
                                <div class="row" id="PNL_DeleteMessageBox" style="display: none;">
                                    <div class="col-md-2">

                                    </div>
                                    <div class="col-md-8" style="">

                                        <table class="table-curved" cellpadding="0" cellspacing="0" style="border-right: silver 0px; border-top: silver 0px; overflow: hidden; border-left: silver 0px; border-bottom: silver 0px; height: 8px; width: 100%; background: #FFFFB0;">
                                            <tr style="background-color: #5b97cb">
                                                <td style="height: 21px; width: 98%;">
                                                    <table cellpadding="0" cellspacing="0" style="width: 100%">
                                                        <tr>
                                                            <td style="width: 90%; height: 17px;">
                                                                <label id="LBL_MsgBoxTitle"
                                                                       style="background-color: #5B97CB; border-color: #5B97CB; border-style: Solid; border-width: 2px; margin: 0px; font-weight: bold; font-family: Open Sans, Helvetica, Arial, sans-serif; font-size: 13px; color: white; width: 100%;margin-top: -2px;margin-left: 8px;">
                                                                    LBL_MsgBoxTitle
                                                                </label>
                                                            </td>
                                                            <td align="right" style="width: 10%; height: 17px; vertical-align: top;" valign="top">
                                                                @*<img id="IMG_MsgBoxClose" src="~/Content/Images/CloseWhiteNoBorder.gif" style="cursor: pointer; border-width: 0px;" />*@
                                                                <label id="IMG_MsgBoxClose" style="cursor: pointer; border-width: 0px; margin-right: 10px;margin-top: -1px;color: white;"><b>X</b></label>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" valign="top" style="width: 98%; vertical-align: top; padding: 4px 4px 4px 4px;">
                                                    <label id="LBL_MsgBoxMessage"
                                                           style="font-family:Open Sans, Helvetica, Arial, sans-serif;font-size:12px;margin-top: -5px;margin-left: 5px;">
                                                    </label>
                                                </td>
                                            </tr>

                                            <tr>
                                                <td align="center" style="height: 30px; width: 98%; vertical-align: middle; padding: 4px 4px 4px 4px;" valign="middle">
                                                    <input type="button" class="btn btn-xs btn-primary" name="BTN_MsgBox1" id="BTN_MsgBox1" value="Yes" style="display:none" onclick="onBTN_MsgBox1Click()" />
                                                    <input type="button" class="btn btn-xs btn-primary" name="BTN_MsgBox2" id="BTN_MsgBox2" value="No" style="display:none" onclick="onBTN_MsgBox2Click()" />
                                                    <input type="button" class="btn btn-xs btn-primary" name="BTN_MsgBox3" id="BTN_MsgBox3" value="Cancel" style="display:none"onclick="onBTN_MsgBox3Click()" />
                                                </td>
                                            </tr>
                                        </table>

                                    </div>
                                    <div class="col-md-2">

                                    </div>
                                </div>
                            </div>
                            //Reset to defaults messagebox Ends

                            <div class="row" style="">
                                <div class="col-md-9">
                                    <div class="col-md-1">
                                        <label>Name</label>
                                    </div>
                                    <div class="col-md-5">

                                       @if (Model.EdtNameEnabled)
                                       {
                                        @(Html.Kendo().TextBox()
                                            .Name("Name")
                                            .Value(Model.EdtNameText)
                                                   .HtmlAttributes(new { @class = "form-control", @style = "width:100%;height: 28px;" })
                                        )
                                       }
                                       else
                                       {
                                        @(Html.Kendo().TextBox()
                                            .Name("Name")
                                            .Value(Model.EdtNameText)
                                                   .HtmlAttributes(new { @class = "form-control", @style = "width:100%;height: 28px;", @disabled = "disabled" })
                                        )
                                       }
                                    </div>

                                    <input type="hidden" id="SelectedDesktopId" value="@Model.PageID" />
                                    <div class="col-md-2">
                                        <label style="float:right">Desktop location</label>
                                    </div>
                                    <div class="col-md-3">
                                        @if (Model.CMBWCBUTTONEnabled)
                                            {                                             
                                                if (Model.CMBWCBUTTONSelectedIndex.Contains(","))
                                                {
                                                    @(Html.Kendo().MultiSelect()
                                                         .Name("Undernode")
                                                      .DataTextField("Text")
                                                      .DataValueField("Value")
                                                      .BindTo(Model.FillWebClientButtons)
                                                         .Value(Model.CMBWCBUTTONSelectedIndex.Split(','))
                                                         .Events(e => e.Change("CMB_WCBUTTON_SelectedIndexChanged").Select("CMB_WCBUTTON_onSelect").Deselect("CMB_WCBUTTON_onDeselect"))
                                                      .HtmlAttributes(new { style = "width: 100%" })
                                                    )
                                                }
                                                else
                                                {
                                                    @(Html.Kendo().MultiSelect()
                                                         .Name("Undernode")
                                                      .DataTextField("Text")
                                                      .DataValueField("Value")
                                                      .BindTo(Model.FillWebClientButtons)
                                                         .Value(Model.CMBWCBUTTONSelectedIndex)
                                                         .Events(e => e.Change("CMB_WCBUTTON_SelectedIndexChanged").Select("CMB_WCBUTTON_onSelect").Deselect("CMB_WCBUTTON_onDeselect"))
                                                      .HtmlAttributes(new { style = "width: 100%" })
                                                    )
                                                }                                                                                        
                                            }
                                        else
                                        {                                                                                          
                                                 @(Html.Kendo().MultiSelect()
                                                      .Name("Undernode")
                                                      .DataTextField("Text")
                                                      .DataValueField("Value")
                                                      .BindTo(Model.FillWebClientButtons)
                                                      .Value(Model.CMBWCBUTTONSelectedIndex)
                                                      .HtmlAttributes(new { style = "width: 100%" })
                                                    )
                                            }
                                    </div>
                                    <div class="col-md-1">
                                        <div class="row" style="float:right">
                                            @if (Model.ChkSharedEnabled)
                                            {
                                                @Html.CheckBoxFor(m => m.ChkSharedChecked, new { @title="Shared", @onChange = "changeShared(); return false;" })<i title="Shared" class="fa fa-1x fa-users"></i>
                                            }
                                            else
                                            {
                                                @Html.CheckBoxFor(m => m.ChkSharedChecked, new { @title = "Shared", @disabled = "disabled" }) <i title="Shared" class="fa fa-1x fa-users"></i>
                                            }
                                        </div>

                                    </div>

                                </div>
                            </div>
                          
                            <div class="row" style="display:none">
                                <div class="col-md-1">
                                    @if (Model.LBLProductEnabled)
                                    {
                                        if (Model.LBLProductVisible)
                                        {
                                            <label>Product</label>
                                        }
                                    }
                                </div>
                                <div class="col-md-3">
                                    @if (Model.CMBProductEnabled)
                                    {
                                        if (Model.CMBProductVisible)
                                        {
                                            @(Html.Kendo().DropDownList()
                                              .Name("product")
                                              .BindTo(new List<string>() {
                                                  "Sales"
                                              })
                                             .HtmlAttributes(new { style = "width: 100%" })
                                            )
                                        }
                                    }
                                </div>
                                <div class="col-md-9">
                                </div>
                            </div>
                           
                            <div id="loadTab">

                                @*<div class="row">
                                    <div class="col-md-9">
                                        <div class="col-md-5">
                                            <label> In the left menu bar make this desktop available under</label>
                                        </div>
                                        @*<div class="col-md-1">
                                            <label>Under Folder</label>
                                        </div>* @
                                        <div class="col-md-6">
                                            @if (Model.CMBWCBUTTONEnabled)
                                            {
                                                @* @(Html.Kendo().DropDownList()
                                                .Name("Undernode")
                                                .DataTextField("Text")
                                                .DataValueField("Value")
                                                .BindTo(Model.FillWebClientButtons)
                                                .Value(Model.CMBWCBUTTONSelectedIndex)
                                                .Events(e => e.Change("CMB_WCBUTTON_SelectedIndexChanged"))
                                                .HtmlAttributes(new { style = "width: 100%" }))* @
                                                
                                                 @(Html.Kendo().MultiSelect()
                                                         .Name("Undernode")
                                                      .DataTextField("Text")
                                                      .DataValueField("Value")
                                                      .BindTo(Model.FillWebClientButtons)
                                                         .Value(Model.CMBWCBUTTONSelectedIndex)
                                                         .Events(e => e.Change("CMB_WCBUTTON_SelectedIndexChanged"))
                                                      .HtmlAttributes(new { style = "width: 100%" })
                                                    )
                                            }
                                            else
                                            {
                                                @* @(Html.Kendo().DropDownList()
                                                .Name("Undernode")
                                                .DataTextField("Text")
                                                .DataValueField("Value")
                                                .BindTo(Model.FillWebClientButtons)
                                                .Value(Model.CMBWCBUTTONSelectedIndex)
                                                .HtmlAttributes(new { style = "width: 100%" }))* @
                                                
                                                 @(Html.Kendo().MultiSelect()
                                                      .Name("Undernode")
                                                      .DataTextField("Text")
                                                      .DataValueField("Value")
                                                      .BindTo(Model.FillWebClientButtons)
                                                      .Value(Model.CMBWCBUTTONSelectedIndex)
                                                      .HtmlAttributes(new { style = "width: 100%" })
                                                    )
                                            }
                                        </div>
                                        @*<div class="col-md-1">
                                            @if (Model.BTNWCBUTTONEnabled)
                                            {
                                                @(Html.Kendo().Button()
                                                .Name("Manage")
                                                .HtmlAttributes(new { type = "button", @class = "btn btn-sm btn-primary" })
                                                .Content("Manage...")
                                                .Events(e => e.Click("BTN_WCBUTTON_Click")))
                                            }
                                            else
                                            {
                                                @(Html.Kendo().Button()
                                                .Name("Manage")
                                                        .HtmlAttributes(new { type = "button", @class = "btn btn-sm btn-primary" })
                                                .Content("Manage..."))
                                            }
                                        </div>* @
                                    </div>
                                </div>*@
                                @*<div class="row">
                                    <div class="col-md-9">
                                        <div class="col-md-5">
                                            <label>Utilize a custom display in your desktop</label>
                                        </div>
                                        <div class="col-md-6">
                                            @(Html.Kendo().DropDownList()
                                            .Name("customdisplay")
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .Events(e => e.Change("onChangeCustomDisplay"))
                                            .BindTo(Model.GetFRFControlList)
                                            .Value(Model.CMBFRFCONTROLSelectedValue)
                                //.DataSource(source => source.Read(read => read.Action("GetFRFControlList", "ManageDesktops")))
                                //.AutoBind(false)
                                            .HtmlAttributes(new { style = "width: 100%" })
                                            )
                                        </div>
                                        @*<div class="col-md-5">
            <label>* Desktops that utilize custom displays do not support additional properties</label>
        </div>* @
                                    </div>
                                    
                                </div>

                                <div class="row">
                                    <div class="col-md-9">
                                        <div class="col-md-5">

                                        </div>
                                        <div class="col-md-6">
                                            <label>* Desktops that utilize custom displays do not support additional properties</label>
                                        </div>
                                    </div>                                    
                                </div>*@

                                @*<div class="row">
                                    <div class="col-md-4">
                                        <label>Desktop Type</label>
                                    </div>
                                    <div class="col-md-4">
                                        @(Html.Kendo().ComboBox()
                                        .HtmlAttributes(new { style = "width:100%" })
                                        .Name("DesktopType")
                                        .DataTextField("Text")
                                        .DataValueField("Value")
                                        .AutoBind(true)
                                        .Value(Model.DesktopTypeSelectedIndex)
                                        //.SelectedIndex(0)
                                        .BindTo(new List<SelectListItem>() {
                                        new SelectListItem() {
                                        Text = "Select", Value = "Select"
                                        },
                                        new SelectListItem() {
                                        Text = "View", Value = "View"
                                        },
                                        new SelectListItem() {
                                        Text = "Report", Value = "Report"
                                        },
                                        new SelectListItem() {
                                          Text = "Chart", Value = "Chart"
                                        },
                                        new SelectListItem() {
                                        Text = "Linked View", Value = "Linked View"
                                        },
                                        new SelectListItem() {
                                        Text = "Tabbed View", Value = "Tabbed View"
                                        },
                                        new SelectListItem() {
                                        Text = "Calendar", Value = "Calendar"
                                        }
                                        })
                                        )
                                    </div>
                                </div>*@
                                    <div class="col-md-9">
                                        <div class="row" id="PNLLayoutSubVisible">
                                            <div class="panel ">
                                                <div class="panel-heading panelhead" style="background: #e9e9e9!important;height:38px;">
                                                    @*<div class="panel-heading-btn pull-right " style="float:right;margin-top: 4px;">

                @if (Model.BTNINSERTVIEWEnabled)
                {
                    <button type="button" class="btn btn-sm btn-primary" title="Insert" id="BTN_INSERTVIEW"><i class="fa fa-plus"></i></button>
                }
                else
                {
                    <button type="button" class="btn btn-sm btn-primary" title="Insert" id="BTN_INSERTVIEW"><i class="fa fa-plus"></i></button>
                }
                @if (Model.BTNAPPENDVIEWEnabled)
                {
                    <button type="button" class="btn btn-sm btn-primary" title="Append" id="BTN_APPENDVIEW"><i class="fa fa-plus-circle"></i></button>
                }
                else
                {
                    <button type="button" class="btn btn-sm btn-primary" title="Append" id="BTN_APPENDVIEW"><i class="fa fa-plus-circle"></i></button>
                }
                @if (Model.BTNDUPLICATEVIEWEnabled)
                {
                    <button type="button" class="btn btn-sm btn-primary" style="display:none;" title="Duplicate" id="BTN_DUPLICATEVIEW"><i class="fa fa-file-code-o"></i></button>
                }
                else
                {
                    <button type="button" class="btn btn-sm btn-primary" style="display:none;" title="Duplicate" id="BTN_DUPLICATEVIEW"><i class="fa fa-file-code-o"></i></button>
                }

                @if (Model.BTNEDITVIEWEnabled)
                {
                    <button type="button" class="btn btn-sm btn-primary" title="Edit" id="BTN_EDITVIEW"><i class="fa fa-edit"></i></button>
                }
                else
                {
                    <button type="button" class="btn btn-sm btn-primary" title="Edit" id="BTN_EDITVIEW"><i class="fa fa-edit"></i></button>
                }
                @if (Model.BTNREMOVEVIEWEnabled)
                {
                    <button type="button" class="btn btn-sm btn-primary" title="Remove" id="BTN_REMOVEVIEW"><i class="fa fa-trash-o"></i></button>
                }
                else
                {
                    <button type="button" class="btn btn-sm btn-primary" title="Remove" id="BTN_REMOVEVIEW"><i class="fa fa-trash-o"></i></button>
                }

                @if (Model.BTNEQUALHEIGHTSEnabled)
                {
                    <button type="button" class="btn btn-sm btn-primary" title="Equal heights" id="BTN_EQUALHEIGHTS"><i class="fa fa-align-justify"></i></button>
                }
                else
                {
                    <button type="button" class="btn btn-sm btn-primary" title="Equal heights" id="BTN_EQUALHEIGHTS"><i class="fa fa-align-justify"></i></button>
                }
            </div>*@
                                                    <h5 class="panel-title">Layout Properties</h5>
                                                </div>
                                                <div class="panel-body">
                                                    
                                                    
                                                    <div class="row">
                                                        <div class="col-md-1"></div>

                                                        <div class="col-md-4">
                                                            <div class="col-md-6">
                                                                @Html.CheckBoxFor(m => m.ChkDrsEnabledChecked, new { id = "showSelector", onchange = "changeshowSelector(); return false;" }) Show date range selector
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div id="daterangeSelector" style="display:none">
                                                                    @(Html.Kendo().DropDownList()
                                                                             .Name("daterange")
                                                                             .DataTextField("Text")
                                                                             .DataValueField("Value")
                                                                             .Events(e => e.Change("onChangeDateRange"))
                                                                             .BindTo(Model.FillCMBDRS)
                                                                             .Value(Model.CMBDRSDefaultSelectedValue)
                                                                             .HtmlAttributes(new { style = "width: 100%" })
                                                                    )
                                                                </div>
                                                                <div id="hiddendaterangeSelector">
                                                                    @(Html.Kendo().DropDownList()
                                                                            .Name("daterangehide")
                                                                            .DataTextField("Text")
                                                                            .DataValueField("Value")
                                                                            .HtmlAttributes(new { style = "width: 100%" })
                                                                    )
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="col-md-2"></div>

                                                        <div class="col-md-4">
                                                            <div class="col-md-2">
                                                                <label>From</label>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div id="fromshow">
                                                                    @(Html.Kendo().DatePicker()
                                                                                 .Name("from")
                                                                                 .Format("yyyy-MM-dd")
                                                                                 .Value(Model.TXTDRSFromText)
                                                                                 .HtmlAttributes(new { @id = "from", style = "width:100% !important;" })
                                                                                 .Enable(false)
                                                                    )
                                                                </div>
                                                            </div>
                                                            <div class="col-md-2">
                                                                <label>To</label>
                                                            </div>
                                                            <div class="col-md-4">
                                                                @(Html.Kendo().DatePicker()
                                                                            .Name("to")
                                                                            .Format("yyyy-MM-dd")
                                                                            .Value(Model.TXTDRSToText)
                                                                            .HtmlAttributes(new { @id = "to", @style = "width:100% !important;" })
                                                                            .Enable(false)
                                                                )
                                                            </div>
                                                        </div>

                                                        <div class="col-md-1"></div>
                                                    </div>      
                                                    
                                                                                                  
                                                    <div class="row">
                                                        <div class="col-md-1"></div>

                                                        <div class="col-md-4">
                                                            <div class="col-md-6">
                                                                <label>Mini Views Area Height</label>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="col-md-7">
                                                                    <input type="text" value=@Model.EdtHeight1 style="width:100%;padding: 1px 0px 0px 5px;" id="EDT_HEIGHT1" onchange="CalcAreas()" />
                                                                </div>
                                                                <div class="col-md-2">
                                                                    <label>% </label>
                                                                </div>
                                                                @*<div class="row col-md-4">
                                                                    @if (Model.BTNRefresh2Visible)
                                                                    {
                                                                        <button class="btn btn-xs btn-primary" id="Reclac2" onclick="CalcAreas(); return false;">Recalc</button>
                                                                    }
                                                                </div>*@
                                                            </div>
                                                        </div>

                                                        <div class="col-md-2"></div>

                                                        <div class="col-md-4">
                                                            <div class="col-md-6">
                                                                <label>Tab Views Area Height</label>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="col-md-7">
                                                                    @if (Model.EDTHEIGHT2Enabled)
                                                                    {
                                                                        <input type="text" value=@Model.EdtHeight2 readonly="readonly" style="width:100%;padding: 1px 0px 0px 5px;" id="EDT_HEIGHT2" />
                                                                    }
                                                                    else
                                                                    {
                                                                        <input type="text" value=@Model.EdtHeight2 readonly="readonly" style="width:100%;padding: 1px 0px 0px 5px;" id="EDT_HEIGHT2" disabled="disabled" />
                                                                    }
                                                                </div>
                                                               
                                                                <div class="col-md-2">
                                                                    <label>% </label>
                                                                </div>
                                                            </div>

                                                        </div>

                                                        <div class="col-md-1"></div>
                                                    </div>


                                                   


                                                    <div class="row">
                                                        <div class="col-md-1"></div>

                                                        <div class="col-md-4">
                                                            <div class="col-md-6">
                                                                <label>Column 1 Width</label>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="col-md-7">
                                                                    <input type="text" value=@Model.EdtCol1Width style="width:100%;padding: 1px 0px 0px 5px;" id="EDT_COL1WIDTH" onchange="CalcAreas()" />
                                                                </div>
                                                                <div class="col-md-2">
                                                                    <label>% </label>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="col-md-2"></div>

                                                        <div class="col-md-4">
                                                            <div class="col-md-6">
                                                                <label>Column 2 Width</label>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="col-md-7">
                                                                    @if (Model.EDTCOL2WIDTHEnabled)
                                                                    {
                                                                        <input type="text" value=@Model.EdtCol2Width readonly="readonly" style="width:100%;padding: 1px 0px 0px 5px;"  id="EDT_COL2WIDTH" />
                                                                    }
                                                                    else
                                                                    {
                                                                        <input type="text" value=@Model.EdtCol2Width readonly="readonly" style="width:100%;padding: 1px 0px 0px 5px;" id="EDT_COL2WIDTH" disabled="disabled" />
                                                                    }
                                                                </div>
                                                                <div class="col-md-2">
                                                                    <label>% </label>
                                                                </div>
                                                            </div>

                                                        </div>

                                                        <div class="col-md-1"></div>
                                                    </div>

                                                    

                                                    <div class="row">
                                                        <div class="col-md-1"></div>

                                                        <div class="col-md-4">
                                                            @if (Model.BTNCOL1Visible)
                                                            {
                                                                <div>
                                                                    @*<input class="btn btn-sm btn-primary" type="button" id="Column1" style="width:100%;" value="Column 1">*@
                                                                    <div id="Column1" class="btn btn-sm btn-primary" style="background-color: #3498db;display:block;text-align: left;">
                                                                        Column 1
                                                                        <div class="pull-right">
                                                                            <div class="actions dropdown apps">
                                                                                <a id="Column1DropDown" href="" data-toggle="dropdown" aria-expanded="true">
                                                                                    <i class="fa fa-ellipsis-v"></i>
                                                                                </a>
                                                                                <ul class="dropdown-menu dropdown-menu-right">


                                                                                    @if (Model.BTNINSERTVIEWEnabled)
                                                                                    {
                                                                                        <li id="BTN_INSERTVIEWCol1" onclick="InsertView()">
                                                                                            <a href="#"><i class="fa fa-plus"></i>&nbsp;&nbsp;Insert</a>
                                                                                        </li>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        @*<li id="BTN_INSERTVIEWCol1" onclick="InsertView()">
                                                                                            <a href="#"><i class="fa fa-plus"></i>&nbsp;&nbsp;Insert</a>
                                                                                        </li>*@
                                                                                    }
                                                                                    @if (Model.BTNAPPENDVIEWEnabled)
                                                                                    {
                                                                                        <li id="BTN_APPENDVIEWCol1" onclick="AppendView()">
                                                                                            <a href="#"><i class="fa fa-plus-circle"></i>&nbsp;&nbsp;Append</a>
                                                                                        </li>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        @*<li id="BTN_APPENDVIEWCol1" onclick="AppendView()">
                                                                                            <a href="#"><i class="fa fa-plus-circle"></i>&nbsp;&nbsp;Append</a>
                                                                                        </li>*@
                                                                                    }
                                                                                    @if (Model.BTNDUPLICATEVIEWEnabled)
                                                                                    {
                                                                                        <li>
                                                                                            <a href="" onclick="" id="BTN_DUPLICATEVIEW" style="display:none;"><i class="fa fa-file-code-o"></i>&nbsp;&nbsp;Duplicate</a>
                                                                                        </li>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        @*<li>
                                                                                            <a href="" onclick="" id="BTN_DUPLICATEVIEW" style="display:none;"><i class="fa fa-file-code-o"></i>&nbsp;&nbsp;Duplicate</a>
                                                                                        </li>*@
                                                                                    }

                                                                                    @if (Model.BTNEDITVIEWEnabled)
                                                                                    {
                                                                                        <li id="BTN_EDITVIEWCol1" onclick="EditView()">
                                                                                            <a href="#"><i class="fa fa-edit"></i>&nbsp;&nbsp;Edit</a>
                                                                                        </li>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        @*<li id="BTN_EDITVIEWCol1" onclick="EditView()">
                                                                                            <a href="#"><i class="fa fa-edit"></i>&nbsp;&nbsp;Edit</a>
                                                                                        </li>*@
                                                                                    }
                                                                                    @if (Model.BTNREMOVEVIEWEnabled)
                                                                                    {
                                                                                        <li id="BTN_REMOVEVIEWCol1" onclick="DeleteView()">
                                                                                            <a href="#"><i class="fa fa-trash-o"></i>&nbsp;&nbsp;Delete</a>
                                                                                        </li>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        @*<li id="BTN_REMOVEVIEWCol1" onclick="DeleteView()">
                                                                                            <a href="#"><i class="fa fa-trash-o"></i>&nbsp;&nbsp;Delete</a>
                                                                                        </li>*@
                                                                                    }

                                                                                    @if (Model.BTNEQUALHEIGHTSEnabled)
                                                                                    {
                                                                                        <li id="BTN_EQUALHEIGHTSCol1" onclick="EqualHeights()">
                                                                                            <a href="#"><i class="fa fa-align-justify"></i>&nbsp;&nbsp;Equal heights</a>
                                                                                        </li>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        @*<li id="BTN_EQUALHEIGHTSCol1" onclick="EqualHeights()">
                                                                                            <a href="#"><i class="fa fa-align-justify"></i>&nbsp;&nbsp;Equal heights</a>
                                                                                        </li>*@
                                                                                    }

                                                                                </ul>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                            }
                                                        </div>

                                                        <div class="col-md-2"></div>

                                                        <div class="col-md-4">
                                                            @if (Model.BTNCOL2Visible)
                                                            {
                                                                @*<input class="btn btn-sm btn-primary" type="button" id="Column2" style="width:100%;" value="Column 2">*@

                                                                <div>
                                                                    @*<input class="btn btn-sm btn-primary" type="button" id="Column1" style="width:100%;" value="Column 1">*@
                                                                    <div id="Column2" class="btn btn-sm btn-primary" style="background-color: #3498db;display:block;text-align: left;">
                                                                        Column 2
                                                                        <div class="pull-right">
                                                                            <div class="actions dropdown apps">
                                                                                <a id="Column2DropDown" href="" data-toggle="dropdown" aria-expanded="true">
                                                                                    <i class="fa fa-ellipsis-v"></i>
                                                                                </a>
                                                                                <ul class="dropdown-menu dropdown-menu-right">


                                                                                    @if (Model.BTNINSERTVIEWEnabled)
                                                                                    {
                                                                                        <li id="BTN_INSERTVIEWCol2" onclick="InsertView()">
                                                                                            <a href="#"><i class="fa fa-plus"></i>&nbsp;&nbsp;Insert</a>
                                                                                        </li>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        @*<li id="BTN_INSERTVIEWCol2" onclick="InsertView()">
                                                                                            <a href="#"><i class="fa fa-plus"></i>&nbsp;&nbsp;Insert</a>
                                                                                        </li>*@
                                                                                    }
                                                                                    @if (Model.BTNAPPENDVIEWEnabled)
                                                                                    {
                                                                                        <li id="BTN_APPENDVIEWCol2" onclick="AppendView()">
                                                                                            <a href="#"><i class="fa fa-plus-circle"></i>&nbsp;&nbsp;Append</a>
                                                                                        </li>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        @*<li id="BTN_APPENDVIEWCol2" onclick="AppendView()">
                                                                                            <a href="#"><i class="fa fa-plus-circle"></i>&nbsp;&nbsp;Append</a>
                                                                                        </li>*@
                                                                                    }
                                                                                    @if (Model.BTNDUPLICATEVIEWEnabled)
                                                                                    {
                                                                                        <li>
                                                                                            <a href="#" onclick="" id="BTN_DUPLICATEVIEW" style="display:none;"><i class="fa fa-file-code-o"></i>&nbsp;&nbsp;Duplicate</a>
                                                                                        </li>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        @*<li>
                                                                                            <a href="#" onclick="" id="BTN_DUPLICATEVIEW" style="display:none;"><i class="fa fa-file-code-o"></i>&nbsp;&nbsp;Duplicate</a>
                                                                                        </li>*@
                                                                                    }

                                                                                    @if (Model.BTNEDITVIEWEnabled)
                                                                                    {
                                                                                        <li id="BTN_EDITVIEWCol2" onclick="EditView()">
                                                                                            <a href="#"><i class="fa fa-edit"></i>&nbsp;&nbsp;Edit</a>
                                                                                        </li>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        @*<li id="BTN_EDITVIEWCol2" onclick="EditView()">
                                                                                            <a href="#"><i class="fa fa-edit"></i>&nbsp;&nbsp;Edit</a>
                                                                                        </li>*@
                                                                                    }
                                                                                    @if (Model.BTNREMOVEVIEWEnabled)
                                                                                    {
                                                                                        <li id="BTN_REMOVEVIEWCol2" onclick="DeleteView()">
                                                                                            <a href="#"><i class="fa fa-trash-o"></i>&nbsp;&nbsp;Delete</a>
                                                                                        </li>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        @*<li id="BTN_REMOVEVIEWCol2" onclick="DeleteView()">
                                                                                            <a href="#"><i class="fa fa-trash-o"></i>&nbsp;&nbsp;Delete</a>
                                                                                        </li>*@
                                                                                    }

                                                                                    @if (Model.BTNEQUALHEIGHTSEnabled)
                                                                                    {
                                                                                        <li id="BTN_EQUALHEIGHTSCol2" onclick="EqualHeights()">
                                                                                            <a href="#"><i class="fa fa-align-justify"></i>&nbsp;&nbsp;Equal heights</a>
                                                                                        </li>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        @*<li id="BTN_EQUALHEIGHTSCol2" onclick="EqualHeights()">
                                                                                            <a href="#"><i class="fa fa-align-justify"></i>&nbsp;&nbsp;Equal heights</a>
                                                                                        </li>*@
                                                                                    }

                                                                                </ul>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            }
                                                        </div>

                                                        <div class="col-md-1"></div>
                                                    </div>

                                                   

                                                    
                                                    <div class="row" style="display:none">
                                                        <div class="col-md-6">
                                                            <div class="row">
                                                                <div class="col-md-2">

                                                                </div>
                                                                <div class="col-md-8">
                                                                    @Html.CheckBoxFor(m => m.ChkAdjustHeights1Checked, new { @onchange = "ChkAdjustHeights1Change();" }) <label>Adjust View Heights Manually</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="row">
                                                                <div class="col-md-2">

                                                                </div>
                                                                <div class="col-md-8">
                                                                    @Html.CheckBoxFor(m => m.ChkAdjustHeights2Checked, new { @onchange = "ChkAdjustHeights2Change();" }) <label>Adjust View Heights Manually</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>



                                                    <div class="row">
                                                        <div class="col-md-1">
                                                            <div class="row">
                                                                <div style="text-align: center; margin-top: 15px; cursor: pointer" id="BTN_LINEUP1">
                                                                    <i title="Move Up" class="fa fa-1x fa-arrow-up"></i>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div style="text-align: center; margin-top: 10px; cursor: pointer" id="BTN_LINEDOWN1">
                                                                    <i title="Move Down" class="fa fa-1x fa-arrow-down"></i>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="col-md-4">
                                                            @Html.ListBox("LST1", new SelectList(Model.LST1, "Value", "Text"), new { @class = "col-md-12", @style = "height: 80px;width:100%;" })
                                                        </div>

                                                        <div class="col-md-2">
                                                            <div class="row">
                                                                <div style="text-align: center; margin-top: 15px; cursor: pointer" id="BTN_1TO2">
                                                                    <i title="Move Right" class="fa fa-1x fa-mail-forward"></i>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div style="text-align: center; margin-top: 10px; cursor: pointer" id="BTN_2TO1">
                                                                    <i title="Move Left" class="fa fa-1x fa-mail-reply"></i>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="col-md-4">
                                                            @Html.ListBox("LST2", new SelectList(Model.LST2, "Value", "Text"), new { @class = "col-md-12", @style = "height: 80px;width:100%;" })
                                                        </div>

                                                        <div class="col-md-1">
                                                            <div class="row">
                                                                <div style="text-align: center; margin-top: 15px; cursor: pointer" id="BTN_LINEUP2">
                                                                    <i title="Move Up" class="fa fa-1x fa-arrow-up"></i>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div style="text-align: center; margin-top: 10px; cursor: pointer" id="BTN_LINEDOWN2">
                                                                    <i title="Move Down" class="fa fa-1x fa-arrow-down"></i>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>


                                                    

                                                    <div class="row">
                                                        <div class="col-md-1"></div>

                                                        <div class="col-md-4">
                                                            <div style="text-align: center;">
                                                                <i title="Move Down" id="BTN_MOVEDOWN1" style="width:30px; cursor:pointer" class="fa fa-1x fa-arrow-down"></i>  <i title="Move Up" id="BTN_MOVEUP1" class="fa fa-1x fa-arrow-up" style="cursor:pointer"></i>
                                                            </div>
                                                        </div>

                                                        <div class="col-md-2"></div>

                                                        <div class="col-md-4">
                                                            <div style="text-align: center;">
                                                                <i title="Move Down" id="BTN_MOVEDOWN2" style="width: 30px; cursor: pointer; " class="fa fa-1x fa-arrow-down"></i>  <i id="BTN_MOVEUP2" title="Move Up" class="fa fa-1x fa-arrow-up" style="cursor:pointer"></i>
                                                            </div>
                                                        </div>

                                                        <div class="col-md-1"></div>
                                                    </div>


                                                   
                                                    <div class="row">
                                                        <div class="col-md-2"></div>
                                                        
                                                        <div class="col-md-8">
                                                            @*<input class="btn btn-sm btn-primary" type="button" id="Column1" style="width:100%;" value="Column 1">*@
                                                            <div id="TabViews" class="btn btn-sm btn-primary" style="background-color: #3498db;display:block;text-align: left;">
                                                                Tab Views
                                                                <div class="pull-right">
                                                                    <div class="actions dropdown apps">
                                                                        <a id="TabDropDown" href="" data-toggle="dropdown" aria-expanded="true">
                                                                            <i class="fa fa-ellipsis-v"></i>
                                                                        </a>
                                                                        <ul class="dropdown-menu dropdown-menu-right">


                                                                            @if (Model.BTNINSERTVIEWEnabled)
                                                                            {
                                                                                <li id="BTN_INSERTVIEWTab" onclick="InsertView()">
                                                                                    <a href="#"><i class="fa fa-plus"></i>&nbsp;&nbsp;Insert</a>
                                                                                </li>
                                                                            }
                                                                            else
                                                                            {
                                                                                @*<li id="BTN_INSERTVIEWTab" onclick="InsertView()">
                                                                                    <a href="#"><i class="fa fa-plus"></i>&nbsp;&nbsp;Insert</a>
                                                                                </li>*@
                                                                            }
                                                                            @if (Model.BTNAPPENDVIEWEnabled)
                                                                            {
                                                                                <li id="BTN_APPENDVIEWTab" onclick="AppendView()">
                                                                                    <a href="#"><i class="fa fa-plus-circle"></i>&nbsp;&nbsp;Append</a>
                                                                                </li>
                                                                            }
                                                                            else
                                                                            {
                                                                                @*<li id="BTN_APPENDVIEWTab" onclick="AppendView()">
                                                                                    <a href="#"><i class="fa fa-plus-circle"></i>&nbsp;&nbsp;Append</a>
                                                                                </li>*@
                                                                            }
                                                                            @if (Model.BTNDUPLICATEVIEWEnabled)
                                                                            {
                                                                                <li>
                                                                                    <a href="" onclick="" id="BTN_DUPLICATEVIEW" style="display:none;"><i class="fa fa-file-code-o"></i>&nbsp;&nbsp;Duplicate</a>
                                                                                </li>
                                                                            }
                                                                            else
                                                                            {
                                                                                @*<li>
                                                                                    <a href="" onclick="" id="BTN_DUPLICATEVIEW" style="display:none;"><i class="fa fa-file-code-o"></i>&nbsp;&nbsp;Duplicate</a>
                                                                                </li>*@
                                                                            }

                                                                            @if (Model.BTNEDITVIEWEnabled)
                                                                            {
                                                                                <li id="BTN_EDITVIEWTab" onclick="EditView()">
                                                                                    <a href="#"><i class="fa fa-edit"></i>&nbsp;&nbsp;Edit</a>
                                                                                </li>
                                                                            }
                                                                            else
                                                                            {
                                                                                @*<li id="BTN_EDITVIEWTab" onclick="EditView()">
                                                                                    <a href="#"><i class="fa fa-edit"></i>&nbsp;&nbsp;Edit</a>
                                                                                </li>*@
                                                                            }
                                                                            @if (Model.BTNREMOVEVIEWEnabled)
                                                                            {
                                                                                <li id="BTN_REMOVEVIEWTab" onclick="DeleteView()">
                                                                                    <a href="#"><i class="fa fa-trash-o"></i>&nbsp;&nbsp;Delete</a>
                                                                                </li>
                                                                            }
                                                                            else
                                                                            {
                                                                                @*<li id="BTN_REMOVEVIEWTab" onclick="DeleteView()">
                                                                                    <a href="#"><i class="fa fa-trash-o"></i>&nbsp;&nbsp;Delete</a>
                                                                                </li>*@
                                                                            }

                                                                            @if (Model.BTNEQUALHEIGHTSEnabled)
                                                                            {
                                                                                <li id="BTN_EQUALHEIGHTSTab" onclick="EqualHeights()">
                                                                                    <a href="#"><i class="fa fa-align-justify"></i>&nbsp;&nbsp;Equal heights</a>
                                                                                </li>
                                                                            }
                                                                            else
                                                                            {
                                                                                @*<li id="BTN_EQUALHEIGHTSTab" onclick="EqualHeights()">
                                                                                    <a href="#"><i class="fa fa-align-justify"></i>&nbsp;&nbsp;Equal heights</a>
                                                                                </li>*@
                                                                            }

                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </div>

                                                        <div class="col-md-2"></div>
                                                    </div>
                                                   
                                                    
                                                    



                                                    <div class="row">                                                                                                               

                                                        <div class="col-md-2"></div>

                                                        <div class="col-md-8">
                                                            @Html.ListBox("LSTTabs", new SelectList(Model.LSTTabs, "Value", "Text"), new { @class = "col-md-12", @style = "height: 90px;width:100%;" })
                                                        </div>

                                                        <div class="col-md-2">
                                                            <div class="col-md-6" >
                                                                <div class="row">
                                                                    <div style="text-align: center; margin-top: 15px; cursor:pointer" id="BTN_LINEUP3">
                                                                        <i title="Move Up" class="fa fa-1x fa-arrow-up"></i>
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div style="text-align: center; margin-top: 10px; cursor: pointer" id="BTN_LINEDOWN3">
                                                                        <i title="Move Down" class="fa fa-1x fa-arrow-down"></i>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <input type="hidden" id="Areafocused" value=@Model.AreaInFocus />
                                                    </div>




                                                                                                      

                                                    <div class="row" style="">
                                                        @if (Model.LBLVIEWSPERMISSIONVisible)
                                                        {
                                                            <label>You don't have permission to add, edit, or remove views</label>
                                                        }
                                                    </div>

                            </div>
                            </div>
                            </div>
                            </div>


                            </div>
                        }
                        @*<hr />*@
                        <div class="row">
                            <div class="col-md-12">
                                <div class="col-md-4" style="color: #9E9D98;">
                                    <label>Created By:</label><label>@Model.Creator</label>
                                </div>                              
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">                                
                                <div class="col-md-4" style="color: #9E9D98;">
                                    @if (Model.LblModByVisible)
                                    {
                                        <label>Modified By:</label><label>@Model.ModifiedBy</label>
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="row">   
                            <div class="col-md-6" style="margin-top:-7px;">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label>Utilize a custom display in your desktop</label>
                                    </div>
                                    <div class="col-md-4">
                                        @(Html.Kendo().DropDownList()
                                            .Name("customdisplay")
                                            .DataTextField("Text")
                                            .DataValueField("Value")
                                            .Events(e => e.Change("onChangeCustomDisplay"))
                                            .BindTo(Model.GetFRFControlList)
                                            .Value(Model.CMBFRFCONTROLSelectedValue)
                                                //.DataSource(source => source.Read(read => read.Action("GetFRFControlList", "ManageDesktops")))
                                                //.AutoBind(false)
                                            .HtmlAttributes(new { style = "width: 100%" })
                                        )

                                    </div>
                                    <div class="col-md-4" style="visibility:hidden !important">
                                        @if (Model.Mode == "Creation")
                                        {
                                            <button class="btn btn-sm btn-primary" type="button" id="btn_OK" style="float: right; margin-left: 10px;" onclick="onAddProfilePage()">Add Profile Page</button>
                                        }
                                        else if (Model.Mode == "Modif")
                                        {
                                            <button class="btn btn-sm btn-primary" type="button" id="btn_EDIT" style="float: right; margin-left: 10px;" onclick="oneditProfilePage()">Edit Profile Page</button>
                                        }
                                    </div>
                                </div>
                                <div class="row">
                                    <label>* Desktops that utilize custom displays do not support additional properties</label>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <label>Workspace Id</label>
                                    </div>
                                    <div class="col-md-4">
                                        @(Html.Kendo().TextBox()
                                            .Name("WorkspaceId")
                                            .Value(Model.WorkspaceId)
                                                   .HtmlAttributes(new { @class = "form-control", @style = "width:100%;height: 28px;" })
                                        )
                                    </div>
                                    <div class="col-md-4">
                                        <label><b>* Required only for PowerBI Reports</b></label>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <label>Report Id</label>
                                    </div>
                                    <div class="col-md-4">
                                        @(Html.Kendo().TextBox()
                                            .Name("ReportId")
                                            .Value(Model.ReportId)
                                                   .HtmlAttributes(new { @class = "form-control", @style = "width:100%;height: 28px;" })
                                        )
                                    </div>
                                    <div class="col-md-4">
                                        <label><b>* Required only for PowerBI Reports</b></label>
                                    </div>
                                </div>

                                <div class="row">
                                    @*<button id="resettodefaults" onclick="BTN_RESETTODEFAULTS_Click(); return false;">Reset View to Defaults...</button>*@
                                    <div id="staMessage" style="display:none;">
                                        <img src="~/Content/Images/info.gif" />
                                        <label>Your changes to this shared desktop will affect all workgroup users.</label>
                                    </div>
                                    <div>
                                        @if (Model.CLKINFOVisible)
                                        {
                                            <img src="~/Content/Images/info.gif" />
                                        }
                                        <label>@Model.STAMESSAGEText</label>
                                    </div>
                                </div>
                            </div>
                                <div class="col-md-3">
                                    @*<button class="btn btn-sm btn-primary" style="float: right; margin-left: 10px;" onclick="window.open('http://help.selltis.com/sales/index.html#context/userguide/1012_WVIEWPRO_ViewProperties','_blank'); return false;"> ? </button>*@
                                    @*<div style="float: right; margin-left: 10px; /*margin-right: 30px;*/ margin-top: 2px;cursor:pointer;" title="Help" onclick="window.open('http://help.selltis.com/sales/index.html#context/userguide/1012_WVIEWPRO_ViewProperties', '_blank'); return false;">
                <i class="fa fa-2x fa-question-circle"></i>
            </div>
            <button class="btn btn-sm btn-primary" style="float: right; margin-left: 10px;" id="CancelClick">Cancel</button>
            @if (Model.BtnOkEnabled)
            {
                <button class="btn btn-sm btn-primary" type="button" id="btn_OK" style="float: right; margin-left: 10px;" onclick="btn_OK_Click()">Save</button>
            }
            else
            {
                <button class="btn btn-sm btn-primary" type="button" id="btn_OK" style="float: right; margin-left: 10px;" onclick="btn_OK_Click()" disabled="disabled">Save</button>
            }
            @if (Model.BtnSaveAsEnabled)
            {
                <button class="btn btn-sm btn-primary" type="button" id="BTN_SAVEAS" style="display:none; float: right; margin-left: 10px;" onclick="BTN_SAVEAS_Click()">Duplicate Desktop...</button>
            }
            else
            {
                <button class="btn btn-sm btn-primary" type="button" id="BTN_SAVEAS" style="float: right; margin-left: 10px; display: none;" onclick="BTN_SAVEAS_Click()" disabled="disabled">Duplicate Desktop...</button>
            }
            @if (Model.BtnMetaVisible)
            {
                <img id="BTN_META" src="~/Content/Images/Cube.gif" style="float: right;margin-top: 8px;cursor:pointer;" title="View Metadata" onclick="BTN_META_Click()" />
            }*@
                                </div>
                            </div>
                    </div>
                    <div class="col-md-1"></div>
                </div>
            </div>
        </section>
        <!-- Begin: popout -->
        <!-- Modal -->
        <div class="modal fade" id="myModal" role="dialog">
            <div class="modal-dialog" style="width:50%;">
                <!-- Modal content-->
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h4 class="modal-title" id="btnJournalVoucherEditGrid-label"></h4>
                    </div>
                    <div class="modal-body" style="overflow-x: scroll; overflow-y: scroll; height: 500px;">
                        <p>Some text in the modal.</p>
                    </div>
                    <div class="modal-footer">
                    </div>
                </div>
            </div>
        </div>
        <!-- End: popout -->
        <div class="modal fade" id="ModalMenuManager" role="dialog">
            <div class="modal-dialog" style="width:60%;">
                <!-- Modal content-->
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Menu Manager<button type="button" class="close" data-dismiss="modal">&times;</button></h3>                        
                    </div>
                    <div class="modal-body" style="overflow-x: scroll; overflow-y: scroll; height: 500px;">
                        @*@Html.Partial("~/Views/MenuManager/PartialMenuManager.cshtml")*@
                    </div>
                    <div class="modal-footer">
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<script>
    function onChange(e) {
        var value = e.sender._old;
        if (value == 2) {
            $('#divicon').show();
            $("#divrootnode").show();
            $("#divurl").hide();
            $("#divpopup").hide();
        }
        else if (value == 3) {
            $('#divicon').show();
            $("#divrootnode").show();
            $("#divurl").show();
            $("#divpopup").show();
        }
        else {
            $("#divrootnode").hide();
            $("#divurl").hide();
            $("#divpopup").hide();
            $('#divicon').hide();
        }
    }


    function onCancel() {
        showProgress();
        var desktopId = '@ViewBag.DesktopIdforCancel';
        window.location.href = "/ManageDesktops/OpenDesktopManager?DesktopId=" + desktopId;
    }
</script>


<script>
    //$("#BTN_INSERTVIEWCol1").click(function () {
    //    InsertView();
    //})
    //$("#BTN_INSERTVIEWCol2").click(function () {
    //    InsertView();
    //})
    //$("#BTN_INSERTVIEWTab").click(function () {
    //    InsertView();
    //})

    function InsertView() {
        //debugger
        showProgress();
        CalcAreas();
        var EdtNameText = $("#Name").val();
        var CHKSHAREDChecked = $("#ChkSharedChecked").prop('checked');
        var CMBFRFCONTROLSelectedValue = $("#customdisplay").data("kendoDropDownList").value();
        var CMBFRFCONTROLSelectedIndex, CMBWCBUTTONSelectedIndex;
        if (CMBFRFCONTROLSelectedValue == "") {
            CMBFRFCONTROLSelectedIndex = -1;
        }
        //var CMBWCBUTTONSelectedValue = $("#Undernode").data("kendoDropDownList").value();
        //if (CMBWCBUTTONSelectedValue == "") {
        //    CMBWCBUTTONSelectedIndex = -1;
        //}

        //var CMBWCBUTTONSelectedValue = $("#Undernode").data("kendoMultiSelect").dataItems();

        var CMB_WCBUTTONSelectedValue = $("#Undernode").data("kendoMultiSelect").dataItems();
        var CMB_WCBUTTONSelectedFolderValues = "";
        var CMB_WCBUTTONSelectedFolderNames = "";

        $.each(CMB_WCBUTTONSelectedValue, function (key, item) {
            if (CMB_WCBUTTONSelectedFolderValues == "") {
                CMB_WCBUTTONSelectedFolderValues = item.Value;
                CMB_WCBUTTONSelectedFolderNames = item.Text;
            }
            else {
                CMB_WCBUTTONSelectedFolderValues = CMB_WCBUTTONSelectedFolderValues + "," + item.Value;
                CMB_WCBUTTONSelectedFolderNames = CMB_WCBUTTONSelectedFolderNames + "," + item.Text;
            }
        });


        var chkshowSelector = $("#showSelector").prop('checked');
        var chkADJUSTHEIGHTS1 = $("#ChkAdjustHeights1Checked").prop('checked');
        var chkADJUSTHEIGHTS2 = $("#ChkAdjustHeights2Checked").prop('checked');
        var TXTDRSFromText = $("#from").val();
        var TXTDRSToText = $("#to").val();
        var EDTCOL1WIDTHText = $("#EDT_COL1WIDTH").val();
        var EDTHEIGHT1Text = $("#EDT_HEIGHT1").val();
        var EDTCOL2WIDTHText = $("#EDT_COL2WIDTH").val();
        var EDTHEIGHT2Text = $("#EDT_HEIGHT2").val();
        var CMBDRSDefaultSelectedValue = $("#daterange").data("kendoDropDownList").value();

        var LST1 = document.getElementById("LST1");
        var LST1SelectedIndex = LST1.selectedIndex;
        //var LST1SelectedText = LST1.options[LST1.selectedIndex].text;

        var LST2 = document.getElementById("LST2");
        var LST2SelectedIndex = LST2.selectedIndex;
        //var LST2SelectedText = LST2.options[LST2.selectedIndex].text;

        var LSTTab = document.getElementById("LSTTabs");
        var LSTTabsSelectedIndex = LSTTab.selectedIndex;
        //var LSTTabSelectedText = LSTTab.options[LSTTab.selectedIndex].text;

        var sAreaInFocus = $("#Areafocused").val();
        $.ajax({
            url: "/ManageDesktops/Insert",
            type: "POST",
            data: {
                EdtNameText: EdtNameText,
                CHKSHAREDChecked: CHKSHAREDChecked,
                chkshowSelector: chkshowSelector,
                chkADJUSTHEIGHTS1: chkADJUSTHEIGHTS1,
                chkADJUSTHEIGHTS2: chkADJUSTHEIGHTS2,
                LST1SelectedIndex: LST1SelectedIndex,
                LST2SelectedIndex: LST2SelectedIndex,
                LSTTabsSelectedIndex: LSTTabsSelectedIndex,
                sAreaInFocus: sAreaInFocus,
                CMBFRFCONTROLSelectedIndex: CMBFRFCONTROLSelectedIndex,

                CMB_WCBUTTONSelectedValue: CMB_WCBUTTONSelectedFolderNames,
                CMBFRFCONTROLSelectedValue: CMBFRFCONTROLSelectedValue,

                CMB_WCBUTTONSelectedValueRoot: CMB_WCBUTTONSelectedFolderValues,

                TXTDRSFromText: TXTDRSFromText,
                TXTDRSToText: TXTDRSToText,
                CMBDRSDefaultSelectedValue: CMBDRSDefaultSelectedValue,
                EDTCOL1WIDTHText: EDTCOL1WIDTHText,
                EDTCOL2WIDTHText: EDTCOL2WIDTHText,
                EDTHEIGHT1Text: EDTHEIGHT1Text,
                EDTHEIGHT2Text: EDTHEIGHT2Text
            },
            cache: false,
            success: function (data) {
                //debugger
                if (data.LBL_MsgBoxMessageText == "Select a column or tab views first..") {
                    DisplayMessageBox(data);
                    hideProgress();
                }
                else {
                    window.location.href = "/ManageDesktops/InsertEditView?type=insert";
                }
            },
            error: function (data) {
                hideProgress();
                return data;
            }
        })
    }

    function AppendView() {
        showProgress();
        CalcAreas();

        var EdtNameText = $("#Name").val();
        var CHKSHAREDChecked = $("#ChkSharedChecked").prop('checked');
        var CMBFRFCONTROLSelectedValue = $("#customdisplay").data("kendoDropDownList").value();
        var CMBFRFCONTROLSelectedIndex, CMBWCBUTTONSelectedIndex;
        if (CMBFRFCONTROLSelectedValue == "") {
            CMBFRFCONTROLSelectedIndex = -1;
        }
        //var CMBWCBUTTONSelectedValue = $("#Undernode").data("kendoDropDownList").value();
        //if (CMBWCBUTTONSelectedValue == "") {
        //    CMBWCBUTTONSelectedIndex = -1;
        //}
        var CMB_WCBUTTONSelectedValue = $("#Undernode").data("kendoMultiSelect").dataItems();
        var CMB_WCBUTTONSelectedFolderValues = "";
        var CMB_WCBUTTONSelectedFolderNames = "";

        $.each(CMB_WCBUTTONSelectedValue, function (key, item) {
            if (CMB_WCBUTTONSelectedFolderValues == "") {
                CMB_WCBUTTONSelectedFolderValues = item.Value;
                CMB_WCBUTTONSelectedFolderNames = item.Text;
            }
            else {
                CMB_WCBUTTONSelectedFolderValues = CMB_WCBUTTONSelectedFolderValues + "," + item.Value;
                CMB_WCBUTTONSelectedFolderNames = CMB_WCBUTTONSelectedFolderNames + "," + item.Text;
            }
        });

        var chkshowSelector = $("#showSelector").prop('checked');
        var chkADJUSTHEIGHTS1 = $("#ChkAdjustHeights1Checked").prop('checked');
        var chkADJUSTHEIGHTS2 = $("#ChkAdjustHeights2Checked").prop('checked');
        var TXTDRSFromText = $("#from").val();
        var TXTDRSToText = $("#to").val();
        var EDTCOL1WIDTHText = $("#EDT_COL1WIDTH").val();
        var EDTHEIGHT1Text = $("#EDT_HEIGHT1").val();
        var EDTCOL2WIDTHText = $("#EDT_COL2WIDTH").val();
        var EDTHEIGHT2Text = $("#EDT_HEIGHT2").val();
        var CMBDRSDefaultSelectedValue = $("#daterange").data("kendoDropDownList").value();

        var LST1 = document.getElementById("LST1");
        var LST1SelectedIndex = LST1.selectedIndex;
        //var LST1SelectedText = LST1.options[LST1.selectedIndex].text;

        var LST2 = document.getElementById("LST2");
        var LST2SelectedIndex = LST2.selectedIndex;
        //var LST2SelectedText = LST2.options[LST2.selectedIndex].text;

        var LSTTab = document.getElementById("LSTTabs");
        var LSTTabsSelectedIndex = LSTTab.selectedIndex;
        //var LSTTabSelectedText = LSTTab.options[LSTTab.selectedIndex].text;

        var sAreaInFocus = $("#Areafocused").val();

        $.ajax({
            url: "/ManageDesktops/Append",
            type: "POST",
            data: {
                EdtNameText: EdtNameText,
                CHKSHAREDChecked: CHKSHAREDChecked,
                chkshowSelector: chkshowSelector,
                chkADJUSTHEIGHTS1: chkADJUSTHEIGHTS1,
                chkADJUSTHEIGHTS2: chkADJUSTHEIGHTS2,
                LST1SelectedIndex: LST1SelectedIndex,
                LST2SelectedIndex: LST2SelectedIndex,
                LSTTabsSelectedIndex: LSTTabsSelectedIndex,
                sAreaInFocus: sAreaInFocus,
                CMBFRFCONTROLSelectedIndex: CMBFRFCONTROLSelectedIndex,

                CMB_WCBUTTONSelectedValue: CMB_WCBUTTONSelectedFolderNames,
                CMBFRFCONTROLSelectedValue: CMBFRFCONTROLSelectedValue,

                CMB_WCBUTTONSelectedValueRoot: CMB_WCBUTTONSelectedFolderValues,

                TXTDRSFromText: TXTDRSFromText,
                TXTDRSToText: TXTDRSToText,
                CMBDRSDefaultSelectedValue: CMBDRSDefaultSelectedValue,
                EDTCOL1WIDTHText: EDTCOL1WIDTHText,
                EDTCOL2WIDTHText: EDTCOL2WIDTHText,
                EDTHEIGHT1Text: EDTHEIGHT1Text,
                EDTHEIGHT2Text: EDTHEIGHT2Text
            },
            cache: false,
            success: function (data) {
                //debugger
                if (data.LBL_MsgBoxMessageText == "Select a column or tab views first..") {
                    DisplayMessageBox(data);
                    hideProgress();
                }
                else {
                    window.location.href = "/ManageDesktops/InsertEditView?type=append";
                }
            },
            error: function (data) {
                hideProgress();
                return data;
            }
        })
    }

    function DeleteView() {
        //debugger
        showProgress();
        CalcAreas();
        var CMBFRFCONTROLSelectedValue = $("#customdisplay").data("kendoDropDownList").value();
        var CMBFRFCONTROLSelectedIndex, CMBWCBUTTONSelectedIndex;
        if (CMBFRFCONTROLSelectedValue == "") {
            CMBFRFCONTROLSelectedIndex = -1;
        }
        //var CMBWCBUTTONSelectedValue = $("#Undernode").data("kendoDropDownList").value();
        //if (CMBWCBUTTONSelectedValue == "") {
        //    CMBWCBUTTONSelectedIndex = -1;
        //}
        //else
        //{

        //}
        var CMB_WCBUTTONSelectedValue = $("#Undernode").data("kendoMultiSelect").dataItems();
        var CMB_WCBUTTONSelectedFolderValues = "";
        var CMB_WCBUTTONSelectedFolderNames = "";

        $.each(CMB_WCBUTTONSelectedValue, function (key, item) {
            if (CMB_WCBUTTONSelectedFolderValues == "") {
                CMB_WCBUTTONSelectedFolderValues = item.Value;
                CMB_WCBUTTONSelectedFolderNames = item.Text;
            }
            else {
                CMB_WCBUTTONSelectedFolderValues = CMB_WCBUTTONSelectedFolderValues + "," + item.Value;
                CMB_WCBUTTONSelectedFolderNames = CMB_WCBUTTONSelectedFolderNames + "," + item.Text;
            }
        });
        var TXTDRSFromText = $("#from").val();
        var TXTDRSToText = $("#to").val();
        var EDTCOL2WIDTHText = $("#EDT_COL2WIDTH").val();
        var EDTHEIGHT2Text = $("#EDT_HEIGHT2").val();
        var CMBDRSDefaultSelectedValue = $("#daterange").data("kendoDropDownList").value();

        var LST1 = document.getElementById("LST1");
        var LST1SelectedIndex = LST1.selectedIndex;
        //var LST1SelectedText = LST1.options[LST1.selectedIndex].text;

        var LST2 = document.getElementById("LST2");
        var LST2SelectedIndex = LST2.selectedIndex;
        //var LST2SelectedText = LST2.options[LST2.selectedIndex].text;

        var LSTTab = document.getElementById("LSTTabs");
        var LSTTabsSelectedIndex = LSTTab.selectedIndex;
        //var LSTTabSelectedText = LSTTab.options[LSTTab.selectedIndex].text;

        var sAreaInFocus = $("#Areafocused").val();

        //Confirms before delete 
        var confirmMsg = confirm("Are you sure you want to delete the selected view ?");
        if(confirmMsg){

            $.ajax({
                url: "/ManageDesktops/Remove",
                type: "POST",
                data: {
                    LST1SelectedIndex: LST1SelectedIndex,
                    LST2SelectedIndex: LST2SelectedIndex,
                    LSTTabsSelectedIndex: LSTTabsSelectedIndex,
                    sAreaInFocus: sAreaInFocus,
                    CMBFRFCONTROLSelectedIndex: CMBFRFCONTROLSelectedIndex,

                    CMB_WCBUTTONSelectedValue: CMB_WCBUTTONSelectedFolderNames,
                    CMBFRFCONTROLSelectedValue: CMBFRFCONTROLSelectedValue,

                    CMB_WCBUTTONSelectedValueRoot: CMB_WCBUTTONSelectedFolderValues,

                    TXTDRSFromText: TXTDRSFromText,
                    TXTDRSToText: TXTDRSToText,
                    CMBDRSDefaultSelectedValue: CMBDRSDefaultSelectedValue,
                    EDTCOL2WIDTHText: EDTCOL2WIDTHText,
                    EDTHEIGHT2Text: EDTHEIGHT2Text
                },
                cache: false,
                success: function (data) {
                    //debugger
                    if (data.LBL_MsgBoxMessageText == "Click a button above a list first.") {
                        DisplayMessageBox(data);

                    }
                    else {
                        if (sAreaInFocus == "1") {
                            $('#LST1').find('option').remove();

                            $.each(data, function (i) {
                                var optionhtml = '<option value="' +
                                data[i].Value + '">' + data[i].Text + '</option>';
                                $("#LST1").append(optionhtml);
                            });

                            $('#LST1')[0].selectedIndex = LST1SelectedIndex;
                            if (data.length == LST1SelectedIndex) {
                                $('#LST1')[0].selectedIndex = LST1SelectedIndex - 1;
                            }

                            $('#LST1').focus();
                        }
                        else if (sAreaInFocus == "2") {
                            $('#LST2').find('option').remove();

                            $.each(data, function (i) {
                                var optionhtml = '<option value="' +
                                data[i].Value + '">' + data[i].Text + '</option>';
                                $("#LST2").append(optionhtml);
                            });

                            $('#LST2')[0].selectedIndex = LST2SelectedIndex;
                            if (data.length == LST2SelectedIndex) {
                                $('#LST2')[0].selectedIndex = LST2SelectedIndex - 1;
                            }

                            $('#LST2').focus();
                        }
                        else if (sAreaInFocus == "TABS") {
                            $('#LSTTabs').find('option').remove();

                            $.each(data, function (i) {
                                var optionhtml = '<option value="' +
                                data[i].Value + '">' + data[i].Text + '</option>';
                                $("#LSTTabs").append(optionhtml);
                            });

                            $('#LSTTabs')[0].selectedIndex = LSTTabsSelectedIndex;
                            if (data.length == LSTTabsSelectedIndex) {
                                $('#LSTTabs')[0].selectedIndex = LSTTabsSelectedIndex - 1;
                            }

                            $('#LSTTabs').focus();
                        }
                    }
                    hideProgress();
                },
                error: function (data) {
                    hideProgress();
                    return data;
                }
            })
        }
        else {
            hideProgress();
        }
    }

    //$("#BTN_APPENDVIEW").click(function () {
    //    //debugger
        
    //})

    $("#LST1").dblclick(function () {
        var str = "";
        $("select option:selected").each(function () {
            //alert($(this).text());
            GoToEditPageCommonCode();
        });

    })

    $("#LST2").dblclick(function () {
        var str = "";
        $("select option:selected").each(function () {
            //alert($(this).text());
            GoToEditPageCommonCode();
        });

    })

    $("#LSTTabs").dblclick(function () {
        var str = "";
        $("select option:selected").each(function () {
            //alert($(this).text());
            GoToEditPageCommonCode();
        });

    })


    function GoToEditPageCommonCode() {
        showProgress();
        CalcAreas();
        var CMBFRFCONTROLSelectedValue = $("#customdisplay").data("kendoDropDownList").value();
        var CMBFRFCONTROLSelectedIndex, CMBWCBUTTONSelectedIndex;
        if (CMBFRFCONTROLSelectedValue == "") {
            CMBFRFCONTROLSelectedIndex = -1;
        }


        var CMB_WCBUTTONSelectedValue = $("#Undernode").data("kendoMultiSelect").dataItems();
        var CMB_WCBUTTONSelectedFolderValues = "";
        var CMB_WCBUTTONSelectedFolderNames = "";

        $.each(CMB_WCBUTTONSelectedValue, function (key, item) {
            if (CMB_WCBUTTONSelectedFolderValues == "") {
                CMB_WCBUTTONSelectedFolderValues = item.Value;
                CMB_WCBUTTONSelectedFolderNames = item.Text;
            }
            else {
                CMB_WCBUTTONSelectedFolderValues = CMB_WCBUTTONSelectedFolderValues + "," + item.Value;
                CMB_WCBUTTONSelectedFolderNames = CMB_WCBUTTONSelectedFolderNames + "," + item.Text;
            }
        });

        var EdtNameText = $("#Name").val();
        var CHKSHAREDChecked = $("#ChkSharedChecked").prop('checked');
        var chkshowSelector = $("#showSelector").prop('checked');
        var chkADJUSTHEIGHTS1 = $("#ChkAdjustHeights1Checked").prop('checked');
        var chkADJUSTHEIGHTS2 = $("#ChkAdjustHeights2Checked").prop('checked');
        var TXTDRSFromText = $("#from").val();
        var TXTDRSToText = $("#to").val();
        var EDTCOL1WIDTHText = $("#EDT_COL1WIDTH").val();
        var EDTHEIGHT1Text = $("#EDT_HEIGHT1").val();
        var EDTCOL2WIDTHText = $("#EDT_COL2WIDTH").val();
        var EDTHEIGHT2Text = $("#EDT_HEIGHT2").val();
        var CMBDRSDefaultSelectedValue = $("#daterange").data("kendoDropDownList").value();

        var LST1 = document.getElementById("LST1");
        var LST1SelectedIndex = LST1.selectedIndex;
        //var LST1SelectedText = LST1.options[LST1.selectedIndex].text;

        var LST2 = document.getElementById("LST2");
        var LST2SelectedIndex = LST2.selectedIndex;
        //var LST2SelectedText = LST2.options[LST2.selectedIndex].text;

        var LSTTab = document.getElementById("LSTTabs");
        var LSTTabsSelectedIndex = LSTTab.selectedIndex;
        //var LSTTabSelectedText = LSTTab.options[LSTTab.selectedIndex].text;

        var sAreaInFocus = $("#Areafocused").val();
        $.ajax({
            url: "/ManageDesktops/Edit",
            type: "POST",
            data: {
                LST1SelectedIndex: LST1SelectedIndex,
                LST2SelectedIndex: LST2SelectedIndex,
                LSTTabsSelectedIndex: LSTTabsSelectedIndex,
                sAreaInFocus: sAreaInFocus,
                CMBFRFCONTROLSelectedIndex: CMBFRFCONTROLSelectedIndex,
                CMB_WCBUTTONSelectedValue: CMB_WCBUTTONSelectedFolderNames,
                CMBFRFCONTROLSelectedValue: CMBFRFCONTROLSelectedValue,
                CMB_WCBUTTONSelectedValueRoot: CMB_WCBUTTONSelectedFolderValues,
                TXTDRSFromText: TXTDRSFromText,
                TXTDRSToText: TXTDRSToText,
                CMBDRSDefaultSelectedValue: CMBDRSDefaultSelectedValue,
                EDTCOL2WIDTHText: EDTCOL2WIDTHText,
                EDTHEIGHT2Text: EDTHEIGHT2Text,
                EDTCOL1WIDTHText: EDTCOL1WIDTHText,
                EDTHEIGHT1Text: EDTHEIGHT1Text,
                EdtNameText: EdtNameText,
                CHKSHAREDChecked: CHKSHAREDChecked,
                chkshowSelector: chkshowSelector,
                chkADJUSTHEIGHTS1: chkADJUSTHEIGHTS1,
                chkADJUSTHEIGHTS2: chkADJUSTHEIGHTS2
            },
            cache: false,
            success: function (data) {
                //debugger
                if (data.LBL_MsgBoxMessageText == "No views to edit..") {
                    DisplayMessageBox(data);
                    hideProgress();
                }
                else if (data.LBL_MsgBoxMessageText == "Select a column or tab views first..") {
                    DisplayMessageBox(data);
                    hideProgress();
                }
                else {
                    var viewid = data[2];
                    //debugger
                    ////var LST1 = document.getElementById("LST1");
                    //if (parseInt(LST1SelectedIndex) >= 0) {
                    //    viewid = LST1.options[LST1.selectedIndex].value;// LST1.selectedValue;
                    //}
                    //if (parseInt(LST2SelectedIndex) >= 0) {
                    //    viewid = LST2.options[LST2.selectedIndex].value;
                    //}
                    //if (parseInt(LSTTabsSelectedIndex) >= 0) {
                    //    viewid = LSTTab.options[LSTTab.selectedIndex].value;
                    //}
                    ////viewid = LST1.options[LST1.selectedIndex].value;// LST1.selectedValue;
                    ////alert(viewid)

                    //window.location.href = "/LoadViewProperties/LoadViewProperties/" + viewid.replace(" ", "") + "/Property" + "/0" + "/True/HISTORYKEY";
                    window.location.href = "/LoadViewProperties/LoadViewProperties/" + viewid + "/Property" + "/0" + "/True/HISTORYKEY";
                }
            },
            error: function (data) {
                hideProgress();
                return data;
            }
        })
    }



    $("#BTN_DUPLICATEVIEW").click(function () {
        showProgress();
        $.ajax({
            url: "/ManageDesktops/Duplicate",
            type: "POST",
            cache: false,
            success: function (data) {
                //debugger
                DisplayMessageBox(data);
                hideProgress();
            },
            error: function (data) {
                hideProgress();
                return data;
            }
        })
    })

    $(function(){
        $("#LST1").click(function () {
            $("#Areafocused").val("1");
        });
        $("#LST2").click(function () {
            $("#Areafocused").val("2");
        });
        $("#LSTTabs").click(function () {
            $("#Areafocused").val("TABS");
        });

    })

    $("#BTN_EDITVIEW").click(function () {
        GoToEditPageCommonCode();        
    })

    function EditView() {
        GoToEditPageCommonCode();
    }

    //$("#BTN_REMOVEVIEW").click(function () {
    //    DeleteView();
    //})

    

    $("#BTN_EQUALHEIGHTS").click(function () {
        EqualHeights();
    })

    function EqualHeights() {
        var sAreaInFocus = $("#Areafocused").val();
        showProgress();
        $.ajax({
            url: "/ManageDesktops/EQUALHEIGHTS",
            type: "POST",
            data: {
                sAreaInFocus: sAreaInFocus
            },
            cache: false,
            success: function (data) {
                //debugger
                if (data.LBL_MsgBoxMessageText == "Click in a Column 1 or Column 2 table first.") {
                    DisplayMessageBox(data);
                }
                else {
                    if (sAreaInFocus == "1") {
                        $('#LST1').focus();
                    }
                    else if (sAreaInFocus == "2") {
                        $('#LST2').focus();
                    }
                }
                hideProgress();
            },
            error: function (data) {
                hideProgress();
                return data;
            }
        })
    }

    $("#CancelClick").click(function () {
        showProgress();
        var desktopId = '@ViewBag.DesktopIdforCancel';
        window.location.href = "/ManageDesktops/OpenDesktopManager?DesktopId=" + desktopId;
    });

    function changeShared() {
        var checked = $('#ChkSharedChecked').prop('checked');
        //debugger
        if (!checked) {
            $("#staMessage").hide();
        }
        else {
            //staMessage not coming to show
            $("#staMessage").show();
        }
    }

    function ChkAdjustHeights1Change() {
        var checked = $("#ChkAdjustHeights1Checked").prop('checked');
        if (checked) {
            $("#Column1").css("font-weight", "bold");
            $("#Column2").css("font-weight", "normal");
            $("#TabViews").css("font-weight", "normal");
            $("#LST1").focus();
        }
    }

    function ChkAdjustHeights2Change() {
        var checked = $("#ChkAdjustHeights2Checked").prop('checked');
        if (checked) {
            $("#Column1").css("font-weight", "normal");
            $("#Column2").css("font-weight", "bold");
            $("#TabViews").css("font-weight", "normal");
            $("#LST2").focus();
        }
    }

    function btn_OK_Click() {
        //debugger
        showProgress();
        CalcAreas();
        var EditName = $("#Name").val();

        var WorkspaceId = $("#WorkspaceId").val();
        var ReportId = $("#ReportId").val();

        //var DesktopType = $("#DesktopType").data("kendoComboBox").value();
        var DesktopType = "View";
        
        var CHK_SHAREDChecked = $("#ChkSharedChecked").prop('checked');

        //var CMB_WCBUTTONSelectedValue = $("#Undernode").data("kendoDropDownList").value();
        //if (CMB_WCBUTTONSelectedValue == "<None>") {
        //    CMB_WCBUTTONSelectedValue = "None";
        //}

        var CMB_WCBUTTONSelectedValue = $("#Undernode").data("kendoMultiSelect").dataItems();
        var CMB_WCBUTTONSelectedFolderValues = "";
        var CMB_WCBUTTONSelectedFolderNames = "";

        $.each(CMB_WCBUTTONSelectedValue, function (key, item) {
            if (CMB_WCBUTTONSelectedFolderValues == "") {
                CMB_WCBUTTONSelectedFolderValues = item.Value;
                CMB_WCBUTTONSelectedFolderNames = item.Text;
            }
            else {
                CMB_WCBUTTONSelectedFolderValues = CMB_WCBUTTONSelectedFolderValues + "," + item.Value;
                CMB_WCBUTTONSelectedFolderNames = CMB_WCBUTTONSelectedFolderNames + "," + item.Text;
            }
        });

       
        var CMB_FRFCONTROLSelectedValue = $("#customdisplay").data("kendoDropDownList").value();
        if (CMB_FRFCONTROLSelectedValue == "<None>") {
            CMB_FRFCONTROLSelectedValue = "";
        }

        var chk_showSelector = $("#showSelector").prop('checked');
        var CMB_DRSDefaultSelectedValue = $("#daterange").data("kendoDropDownList").value();

        var TXT_DRSFrom = $('#from').val();
        var TXT_DRSTo = $('#to').val();

        var EDT_COL2WIDTHText = $('#EDT_COL2WIDTH').val();
        var EDT_HEIGHT2Text = $('#EDT_HEIGHT2').val();

        var chk_ADJUSTHEIGHTS1 = $("#ChkAdjustHeights1Checked").prop('checked');
        var chk_ADJUSTHEIGHTS2 = $("#ChkAdjustHeights2Checked").prop('checked');

        $.ajax({
            url: "/ManageDesktops/OnSaveClick",
            type: 'POST',
            data: {
                EditName: EditName,
                DesktopType:DesktopType,
                CHK_SHAREDChecked: CHK_SHAREDChecked,

                CMB_WCBUTTONSelectedValue: CMB_WCBUTTONSelectedFolderNames,
                CMB_WCBUTTONSelectedValueRoot: CMB_WCBUTTONSelectedFolderValues,

                CMB_FRFCONTROLSelectedValue: CMB_FRFCONTROLSelectedValue,

                chk_showSelector: chk_showSelector,
                CMB_DRSDefaultSelectedValue: CMB_DRSDefaultSelectedValue,
                TXT_DRSFrom: TXT_DRSFrom,
                TXT_DRSTo: TXT_DRSTo,

                EDT_COL2WIDTHText: EDT_COL2WIDTHText,
                EDT_HEIGHT2Text: EDT_HEIGHT2Text,

                CHK_ADJUSTHEIGHTS1: chk_ADJUSTHEIGHTS1,
                CHK_ADJUSTHEIGHTS2: chk_ADJUSTHEIGHTS2,
                WorkspaceId: WorkspaceId,
                ReportId:ReportId


            },
            success: function (data) {
                //debugger                               
                if (data.PNL_MessageBoxVisible == true) {
                    DisplayMessageBox(data);
                    hideProgress();
                }
                else {
                    var desktopId = data;

                    //clear previous splitter changes from browser cookie..J
                    ClearDesktopSplitterCookie(desktopId);

                    window.location.href = "/ManageDesktops/OpenDesktopManager?DesktopId=" + desktopId;
                }

            },
            error: function (data) {
                //debugger
                hideProgress();
            }
        })
    }

    function ClearDesktopSplitterCookie(SelectedDesktopId) {        

        $.ajax({
            url: "/ManageDesktops/GetViewIdsInDesktop",
            type: "POST",
            data: { DesktopId: SelectedDesktopId },
            success: function (data) {

                //To clear views panes sizes..J 
                if (data.indexOf(',') > -1) {
                    var ViewIDs = data.split(',');
                    for (var i = 0; i < ViewIDs.length; i++) {
                        eraseCookie("DS_" + SelectedDesktopId + "_view" + ViewIDs[i].replace(' ', ''));
                    }
                }

                //To clear top and tab view panes sizes..J               
                eraseCookie("DS_" + SelectedDesktopId + "_splitterT");
                eraseCookie("DS_" + SelectedDesktopId + "_divheight");
            },
            error: function (data) {

            }
        })
    }

    function createCookie(cookieName, cookieVal, days) {
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            var expires = ";expires=" + date.toGMTString();
        }
        else var expires = "";
        document.cookie = cookieName + "=" + cookieVal + expires + "; path=/";
    }

    function eraseCookie(name) {
        createCookie(name, "", -1);
    }

    function BTN_META_Click() {
        showProgress();
        CalcAreas();
        var EdtNameText = $("#Name").val();
        var CHKSHAREDChecked = $("#ChkSharedChecked").prop('checked');
        var CMBFRFCONTROLSelectedValue = $("#customdisplay").data("kendoDropDownList").value();
        var CMBFRFCONTROLSelectedIndex, CMBWCBUTTONSelectedIndex;
        if (CMBFRFCONTROLSelectedValue == "") {
            CMBFRFCONTROLSelectedIndex = -1;
        }
        //var CMBWCBUTTONSelectedValue = $("#Undernode").data("kendoDropDownList").value();
        //if (CMBWCBUTTONSelectedValue == "") {
        //    CMBWCBUTTONSelectedIndex = -1;
        //}
        var CMB_WCBUTTONSelectedValue = $("#Undernode").data("kendoMultiSelect").dataItems();
        var CMB_WCBUTTONSelectedFolderValues = "";
        var CMB_WCBUTTONSelectedFolderNames = "";

        $.each(CMB_WCBUTTONSelectedValue, function (key, item) {
            if (CMB_WCBUTTONSelectedFolderValues == "") {
                CMB_WCBUTTONSelectedFolderValues = item.Value;
                CMB_WCBUTTONSelectedFolderNames = item.Text;
            }
            else {
                CMB_WCBUTTONSelectedFolderValues = CMB_WCBUTTONSelectedFolderValues + "," + item.Value;
                CMB_WCBUTTONSelectedFolderNames = CMB_WCBUTTONSelectedFolderNames + "," + item.Text;
            }
        });
        var chkshowSelector = $("#showSelector").prop('checked');
        var chkADJUSTHEIGHTS1 = $("#ChkAdjustHeights1Checked").prop('checked');
        var chkADJUSTHEIGHTS2 = $("#ChkAdjustHeights2Checked").prop('checked');
        var TXTDRSFromText = $("#from").val();
        var TXTDRSToText = $("#to").val();
        var EDTCOL1WIDTHText = $("#EDT_COL1WIDTH").val();
        var EDTHEIGHT1Text = $("#EDT_HEIGHT1").val();
        var EDTCOL2WIDTHText = $("#EDT_COL2WIDTH").val();
        var EDTHEIGHT2Text = $("#EDT_HEIGHT2").val();
        var CMBDRSDefaultSelectedValue = $("#daterange").data("kendoDropDownList").value();

        var LST1 = document.getElementById("LST1");
        var LST1SelectedIndex = LST1.selectedIndex;
        //var LST1SelectedText = LST1.options[LST1.selectedIndex].text;

        var LST2 = document.getElementById("LST2");
        var LST2SelectedIndex = LST2.selectedIndex;
        //var LST2SelectedText = LST2.options[LST2.selectedIndex].text;

        var LSTTab = document.getElementById("LSTTabs");
        var LSTTabsSelectedIndex = LSTTab.selectedIndex;
        //var LSTTabSelectedText = LSTTab.options[LSTTab.selectedIndex].text;

        var sAreaInFocus = $("#Areafocused").val();
        $.ajax({
            url: "/ManageDesktops/NewDesktopMeta",
            type: "POST",
            data: {
                EdtNameText: EdtNameText,
                CHKSHAREDChecked: CHKSHAREDChecked,
                chkshowSelector: chkshowSelector,
                chkADJUSTHEIGHTS1: chkADJUSTHEIGHTS1,
                chkADJUSTHEIGHTS2: chkADJUSTHEIGHTS2,
                LST1SelectedIndex: LST1SelectedIndex,
                LST2SelectedIndex: LST2SelectedIndex,
                LSTTabsSelectedIndex: LSTTabsSelectedIndex,
                sAreaInFocus: sAreaInFocus,
                CMBFRFCONTROLSelectedIndex: CMBFRFCONTROLSelectedIndex,

                CMB_WCBUTTONSelectedValue: CMB_WCBUTTONSelectedFolderNames,
                CMBFRFCONTROLSelectedValue: CMBFRFCONTROLSelectedValue,

                CMB_WCBUTTONSelectedValueRoot: CMB_WCBUTTONSelectedFolderValues,

                TXTDRSFromText: TXTDRSFromText,
                TXTDRSToText: TXTDRSToText,
                CMBDRSDefaultSelectedValue: CMBDRSDefaultSelectedValue,
                EDTCOL1WIDTHText: EDTCOL1WIDTHText,
                EDTCOL2WIDTHText: EDTCOL2WIDTHText,
                EDTHEIGHT1Text: EDTHEIGHT1Text,
                EDTHEIGHT2Text: EDTHEIGHT2Text
            },
            cache: false,
            success: function (data) {
                //debugger
                DisplayMessageBox(data);
                hideProgress();
            },
            error: function (data) {
                hideProgress();
                return data;
            }
        })
    }

    function DisplayMessageBox(data) {

        $("#pnlError").hide();

        if (data.PNL_MessageBoxVisible == true) {

            $("#PNL_DeleteMessageBox").show();
            $("#LBL_MsgBoxTitle").text(data.LBL_MsgBoxTitleText);
            var _LBL_MsgBoxMessage = data.LBL_MsgBoxMessageText.replace("/n/n", "</br></br>").replace("/n", "</br>");
            $("#LBL_MsgBoxMessage").html(_LBL_MsgBoxMessage);


            if (data.BTN_MsgBox1Visible == true) {
                $("#BTN_MsgBox1").show();

                $("#BTN_MsgBox1").attr('value', data.BTN_MsgBox1Text);
                $("#BTN_MsgBox1").prop('value', data.BTN_MsgBox1Text);
                $("#BTN_MsgBox1").html(data.BTN_MsgBox1Text);
            }
            else {
                $("#BTN_MsgBox1").hide();
            }

            if (data.BTN_MsgBox2Visible == true) {
                $("#BTN_MsgBox2").show();

                $("#BTN_MsgBox2").attr('value', data.BTN_MsgBox2Text);
                $("#BTN_MsgBox2").prop('value', data.BTN_MsgBox2Text);
                $("#BTN_MsgBox2").html(data.BTN_MsgBox2Text);
            }
            else {
                $("#BTN_MsgBox2").hide();
            }

            if (data.BTN_MsgBox3Visible == true) {
                $("#BTN_MsgBox3").show();

                $("#BTN_MsgBox3").attr('value', data.BTN_MsgBox3Text);
                $("#BTN_MsgBox3").prop('value', data.BTN_MsgBox3Text);
                $("#BTN_MsgBox3").html(data.BTN_MsgBox3Text);
            }
            else {
                $("#BTN_MsgBox3").hide();
            }

        }
    }

    function onBTN_MsgBox1Click() {
        //debugger
        var chk_ADJUSTHEIGHTS1 = $("#ChkAdjustHeights1Checked").prop('checked');
        var chk_ADJUSTHEIGHTS2 = $("#ChkAdjustHeights2Checked").prop('checked');
        showProgress();
        $.ajax({
            url: "/ManageDesktops/OnBTN_MsgBox1ForNewEditClick",
            type: "POST",
            data: { CHK_ADJUSTHEIGHTS1: chk_ADJUSTHEIGHTS1, CHK_ADJUSTHEIGHTS2: chk_ADJUSTHEIGHTS2 },
            success: function (data) {
                hideProgress();
                $("#PNL_DeleteMessageBox").hide();
            },
            error: function (data) {
                hideProgress();
            }
        })
    }
    function onBTN_MsgBox2Click() {
        showProgress();
        $.ajax({
            url: "/ManageDesktops/OnBTN_MsgBox2ForNewEditClick",
            type: "POST",
            success: function (data) {
                hideProgress();
                $("#PNL_DeleteMessageBox").hide();
            },
            error: function (data) {
                hideProgress();
            }
        })

        //$("#PNL_DeleteMessageBox").hide();
    }
    function onBTN_MsgBox3Click() {
        $("#pnlError").hide();
        $("#PNL_DeleteMessageBox").hide();
    }

	function onAddProfilePage() {
		var modetype = '@Model.Mode';
        showProgress();
        $.ajax({
            url: "/ManageProfilePage/OnNewClick",
			type: "POST",
			data: {
				modetype: modetype,
			},
            success: function (data) {
                window.location.href = data;
            },
            error: function (data) {

            }
        })
	}
	function oneditProfilePage() {
         var modetype = '@Model.Mode';
        showProgress();
        $.ajax({
            url: "/ManageProfilePage/OnNewClick",
			type: "POST",
			data: {
				modetype: modetype,
			},
            success: function (data) {
                window.location.href = data;
            },
            error: function (data) {

            }
        })
    }


</script>
<script>
    $("#Column1").click(function (e) {
        //debugger
        //$("#Column1").css("border-color", "black");
        $("#Column2").css("border-color", "");
        $("#Column1").css("font-weight", "bold");
        $("#Column2").css("font-weight", "normal");
        $("#TabViews").css("font-weight", "normal");
        //$("#LST1").focus();
        var LST1SelectedIndex = document.getElementById("LST1").selectedIndex;
        if (LST1SelectedIndex == -1) {
            $("#BTN_DUPLICATEVIEW").attr("disabled", "disabled"); 
            //$("#BTN_EDITVIEW").attr("disabled", "disabled");
            //$("#BTN_REMOVEVIEW").attr("disabled", "disabled");

            $("#BTN_EDITVIEWCol1").css("display", "none");    //specific to dropdown column1..J            
            $("#BTN_REMOVEVIEWCol1").css("display", "none");
        }
        else
        {
            $("#BTN_DUPLICATEVIEW").removeAttr("disabled");
            //$("#BTN_EDITVIEW").removeAttr("disabled");
            //$("#BTN_REMOVEVIEW").removeAttr("disabled");

            $("#BTN_EDITVIEWCol1").css("display", "");    //specific to dropdown column1..J
            $("#BTN_REMOVEVIEWCol1").css("display", "");
        }
        $("#Areafocused").val("1");
        
        //Show dropdown on Column 1 click also along with icon.. S1
        if (e.target.id != "Column1DropDown") {
            if (e.target.id != "") {
                setTimeout(function () {
                    $('#Column1DropDown').click();
                }, 100);
            }
        }
    })

    $("#Column2").click(function (e) {
        $("#Column1").css("border-color", "");
        //$("#Column2").css("border-color", "black");
        $("#Column1").css("font-weight", "normal");
        $("#Column2").css("font-weight", "bold");
        $("#TabViews").css("font-weight", "normal");
        //$("#LST2").focus();
        var LST2SelectedIndex = document.getElementById("LST2").selectedIndex;
        if (LST2SelectedIndex == -1) {
            $("#BTN_DUPLICATEVIEW").attr("disabled", "disabled");
            //$("#BTN_EDITVIEW").attr("disabled", "disabled");
            //$("#BTN_REMOVEVIEW").attr("disabled", "disabled");

            $("#BTN_EDITVIEWCol2").css("display", "none");   //specific to dropdown column2..J            
            $("#BTN_REMOVEVIEWCol2").css("display", "none");
        }
        else {
            $("#BTN_DUPLICATEVIEW").removeAttr("disabled");
            //$("#BTN_EDITVIEW").removeAttr("disabled");
            //$("#BTN_REMOVEVIEW").removeAttr("disabled");

            $("#BTN_EDITVIEWCol2").css("display", "");   //specific to dropdown column2..J
            $("#BTN_REMOVEVIEWCol2").css("display", "");
        }
        $("#Areafocused").val("2");

        //Show dropdown on Column 2 click also along with icon.. S1
        if (e.target.id != "Column2DropDown") {
            if (e.target.id != "") {
                setTimeout(function () {
                    $('#Column2DropDown').click();
                }, 100);
            }
        }
    })

    $("#TabViews").click(function (e) {
        $("#Column1").css("font-weight", "normal");
        $("#Column2").css("font-weight", "normal");
        $("#TabViews").css("font-weight", "bold");
        //$("#LSTTabs").focus();
        var LSTTabsSelectedIndex = document.getElementById("LSTTabs").selectedIndex;
        if (LSTTabsSelectedIndex == -1) {
            $("#BTN_DUPLICATEVIEW").attr("disabled", "disabled");
            //$("#BTN_EDITVIEW").attr("disabled", "disabled");
            //$("#BTN_REMOVEVIEW").attr("disabled", "disabled");

            $("#BTN_EDITVIEWTab").css("display", "none");   //specific to dropdown tabbed views..J            
            $("#BTN_REMOVEVIEWTab").css("display", "none");
        }
        else {
            $("#BTN_DUPLICATEVIEW").removeAttr("disabled");
            //$("#BTN_EDITVIEW").removeAttr("disabled");
            //$("#BTN_REMOVEVIEW").removeAttr("disabled");

            $("#BTN_EDITVIEWTab").css("display", "");   //specific to dropdown tabbed views..J            
            $("#BTN_REMOVEVIEWTab").css("display", "");
        }
        $("#Areafocused").val("TABS");

        //Show dropdown on Tab 1 click also along with icon.. S1
        if (e.target.id != "TabDropDown") {
            if (e.target.id != "") {
                setTimeout(function () {
                    $('#TabDropDown').click();
                }, 100);
            }
        }
    })

    function CalcAreas() {
        var i;
        //Column 1 width
        i = $("#EDT_COL1WIDTH").val();
        if (i < 0) {
            i = 0;
        }
        else if (i > 100) {
            i = 100;
        }
        $("#EDT_COL2WIDTH").val(100 - i);

        //Mini views/tab views heights
        i = $("#EDT_HEIGHT1").val();
        if (i < 0) {
            i = 0;
        }
        else if (i > 100) {
            i = 100;
        }
        $("#EDT_HEIGHT2").val(100 - i);
    }

    $("#BTN_1TO2").click(function () {
        //debugger
        var skillsSelect = document.getElementById("LST1");
        if (skillsSelect.selectedIndex == -1) {
            return false;
        }
        var selectedText = skillsSelect.options[skillsSelect.selectedIndex].text;
        var selectedIndex = skillsSelect.selectedIndex;

        //if (selectedIndex == 0) {
        //    return false;
        //}

        $.ajax({
            url: "/ManageDesktops/BTN1TO2",
            data: {
                listItem: selectedText, SelectedIndex: selectedIndex
            },
            type: "post",
            success: function (data) {
                $('#LST1').find('option').remove();
                $('#LST2').find('option').remove();

                $.each(data.FromLST, function (i) {
                    var optionhtml = '<option value="' +
                    data.FromLST[i].Value + '">' + data.FromLST[i].Text + '</option>';
                    $("#LST1").append(optionhtml);
                });

                $.each(data.ToLST, function (i) {
                    var optionhtml = '<option value="' +
                    data.ToLST[i].Value + '">' + data.ToLST[i].Text + '</option>';
                    $("#LST2").append(optionhtml);
                });

                $('#LST1')[0].selectedIndex = selectedIndex;
                if (data.FromLST.length == selectedIndex) {
                    $('#LST1')[0].selectedIndex = selectedIndex - 1;
                }

                $('#LST2 option').map(function () {
                    if ($(this).text() == selectedText) return this;
                }).attr('selected', 'selected');
                //debugger
                //var lst1count = $('#LST1 option').length;
                //var lst2count = $('#LST2 option').length;
                //var diff = Math.abs(lst1count - lst2count);
                //if (diff >= 1) {
                //    $("#BTN_EQUALHEIGHTS").removeAttr("disabled");
                //}
                //else
                //{
                //    $("#BTN_EQUALHEIGHTS").attr("disabled", "disabled");                    
                //}
            },
            error: function (data) {
                return data;
            }
        })
    })

    $("#BTN_2TO1").click(function () {
        //debugger
        var skillsSelect = document.getElementById("LST2");
        if (skillsSelect.selectedIndex == -1) {
            return false;
        }
        var selectedText = skillsSelect.options[skillsSelect.selectedIndex].text;
        var selectedIndex = skillsSelect.selectedIndex;

        //if (selectedIndex == 0) {
        //    return false;
        //}

        $.ajax({
            url: "/ManageDesktops/BTN2TO1",
            data: {
                listItem: selectedText, SelectedIndex: selectedIndex
            },
            type: "post",
            success: function (data) {
                //debugger
                $('#LST1').find('option').remove();
                $('#LST2').find('option').remove();

                $.each(data.FromLST, function (i) {
                    var optionhtml = '<option value="' +
                    data.FromLST[i].Value + '">' + data.FromLST[i].Text + '</option>';
                    $("#LST1").append(optionhtml);
                });

                $.each(data.ToLST, function (i) {
                    var optionhtml = '<option value="' +
                    data.ToLST[i].Value + '">' + data.ToLST[i].Text + '</option>';
                    $("#LST2").append(optionhtml);
                });

                $('#LST2')[0].selectedIndex = selectedIndex;
                if (data.ToLST.length == selectedIndex) {
                    $('#LST2')[0].selectedIndex = selectedIndex - 1;
                }

                $('#LST1 option').map(function () {
                    if ($(this).text() == selectedText) return this;
                }).attr('selected', 'selected');

                //debugger
                //var lst1count = $('#LST1 option').length;
                //var lst2count = $('#LST2 option').length;
                //var diff = Math.abs(lst1count - lst2count);
                //if (diff >= 1) {
                //    $("#BTN_EQUALHEIGHTS").removeAttr("disabled");
                //}
                //else {
                //    $("#BTN_EQUALHEIGHTS").attr("disabled", "disabled");
                //}
            },
            error: function (data) {
                return data;
            }
        })
    })

    $("#BTN_MOVEDOWN1").click(function () {
        //debugger
        var skillsSelect = document.getElementById("LST1");
        if (skillsSelect.selectedIndex == -1) {
            return false;
        }
        var selectedText = skillsSelect.options[skillsSelect.selectedIndex].text;
        var selectedIndex = skillsSelect.selectedIndex;

        //if (selectedIndex == 0) {
        //    return false;
        //}

        $.ajax({
            url: "/ManageDesktops/BTN_MOVEDOWN1",
            data: {
                listItem: selectedText, SelectedIndex: selectedIndex
            },
            type: "post",
            success: function (data) {
                //debugger
                $('#LST1').find('option').remove();
                $('#LSTTabs').find('option').remove();

                $.each(data.FromLST, function (i) {
                    var optionhtml = '<option value="' +
                    data.FromLST[i].Value + '">' + data.FromLST[i].Text + '</option>';
                    $("#LST1").append(optionhtml);
                });

                $.each(data.ToLST, function (i) {
                    var optionhtml = '<option value="' +
                    data.ToLST[i].Value + '">' + data.ToLST[i].Text + '</option>';
                    $("#LSTTabs").append(optionhtml);
                });

                $('#LST1')[0].selectedIndex = selectedIndex;
                if (data.FromLST.length == selectedIndex) {
                    $('#LST1')[0].selectedIndex = selectedIndex - 1;
                }

                $('#LSTTabs option').map(function () {
                    if ($(this).text() == selectedText) return this;
                }).attr('selected', 'selected');

                //debugger
                //var lst1count = $('#LST1 option').length;
                //var lst2count = $('#LST2 option').length;
                //var diff = Math.abs(lst1count - lst2count);
                //if (diff >= 1) {
                //    $("#BTN_EQUALHEIGHTS").removeAttr("disabled");
                //}
                //else {
                //    $("#BTN_EQUALHEIGHTS").attr("disabled", "disabled");
                //}
            },
            error: function (data) {
                return data;
            }
        })
    })

    $("#BTN_MOVEUP1").click(function () {
        //debugger
        var skillsSelect = document.getElementById("LSTTabs");
        if (skillsSelect.selectedIndex == -1) {
            return false;
        }
        var selectedText = skillsSelect.options[skillsSelect.selectedIndex].text;
        var selectedIndex = skillsSelect.selectedIndex;

        //if (selectedIndex == 0) {
        //    return false;
        //}

        $.ajax({
            url: "/ManageDesktops/BTN_MOVEUP1",
            data: {
                listItem: selectedText, SelectedIndex: selectedIndex
            },
            type: "post",
            success: function (data) {
                //debugger
                $('#LST1').find('option').remove();
                $('#LSTTabs').find('option').remove();

                $.each(data.FromLST, function (i) {
                    var optionhtml = '<option value="' +
                    data.FromLST[i].Value + '">' + data.FromLST[i].Text + '</option>';
                    $("#LST1").append(optionhtml);
                });

                $.each(data.ToLST, function (i) {
                    var optionhtml = '<option value="' +
                    data.ToLST[i].Value + '">' + data.ToLST[i].Text + '</option>';
                    $("#LSTTabs").append(optionhtml);
                });

                $('#LSTTabs')[0].selectedIndex = selectedIndex;
                if (data.ToLST.length == selectedIndex) {
                    $('#LSTTabs')[0].selectedIndex = selectedIndex - 1;
                }

                $('#LST1 option').map(function () {
                    if ($(this).text() == selectedText) return this;
                }).attr('selected', 'selected');

                //debugger
                //var lst1count = $('#LST1 option').length;
                //var lst2count = $('#LST2 option').length;
                //var diff = Math.abs(lst1count - lst2count);
                //if (diff >= 1) {
                //    $("#BTN_EQUALHEIGHTS").removeAttr("disabled");
                //}
                //else {
                //    $("#BTN_EQUALHEIGHTS").attr("disabled", "disabled");
                //}
            },
            error: function (data) {
                return data;
            }
        })
    })

    $("#BTN_MOVEDOWN2").click(function () {
        //debugger
        var skillsSelect = document.getElementById("LST2");
        if (skillsSelect.selectedIndex == -1) {
            return false;
        }
        var selectedText = skillsSelect.options[skillsSelect.selectedIndex].text;
        var selectedIndex = skillsSelect.selectedIndex;

        //if (selectedIndex == 0) {
        //    return false;
        //}

        $.ajax({
            url: "/ManageDesktops/BTN_MOVEDOWN2",
            data: {
                listItem: selectedText, SelectedIndex: selectedIndex
            },
            type: "post",
            success: function (data) {
                //debugger
                $('#LST2').find('option').remove();
                $('#LSTTabs').find('option').remove();

                $.each(data.FromLST, function (i) {
                    var optionhtml = '<option value="' +
                    data.FromLST[i].Value + '">' + data.FromLST[i].Text + '</option>';
                    $("#LST2").append(optionhtml);
                });

                $.each(data.ToLST, function (i) {
                    var optionhtml = '<option value="' +
                    data.ToLST[i].Value + '">' + data.ToLST[i].Text + '</option>';
                    $("#LSTTabs").append(optionhtml);
                });

                $('#LST2')[0].selectedIndex = selectedIndex;
                if (data.FromLST.length == selectedIndex) {
                    $('#LST2')[0].selectedIndex = selectedIndex - 1;
                }

                $('#LSTTabs option').map(function () {
                    if ($(this).text() == selectedText) return this;
                }).attr('selected', 'selected');

                //debugger
                //var lst1count = $('#LST1 option').length;
                //var lst2count = $('#LST2 option').length;
                //var diff = Math.abs(lst1count - lst2count);
                //if (diff >= 1) {
                //    $("#BTN_EQUALHEIGHTS").removeAttr("disabled");
                //}
                //else {
                //    $("#BTN_EQUALHEIGHTS").attr("disabled", "disabled");
                //}
            },
            error: function (data) {
                return data;
            }
        })
    })

    $("#BTN_MOVEUP2").click(function () {
        //debugger
        var skillsSelect = document.getElementById("LSTTabs");
        if (skillsSelect.selectedIndex == -1) {
            return false;
        }
        var selectedText = skillsSelect.options[skillsSelect.selectedIndex].text;
        var selectedIndex = skillsSelect.selectedIndex;

        //if (selectedIndex == 0) {
        //    return false;
        //}

        $.ajax({
            url: "/ManageDesktops/BTN_MOVEUP2",
            data: {
                listItem: selectedText, SelectedIndex: selectedIndex
            },
            type: "post",
            success: function (data) {
                //debugger
                $('#LST2').find('option').remove();
                $('#LSTTabs').find('option').remove();

                $.each(data.FromLST, function (i) {
                    var optionhtml = '<option value="' +
                    data.FromLST[i].Value + '">' + data.FromLST[i].Text + '</option>';
                    $("#LST2").append(optionhtml);
                });

                $.each(data.ToLST, function (i) {
                    var optionhtml = '<option value="' +
                    data.ToLST[i].Value + '">' + data.ToLST[i].Text + '</option>';
                    $("#LSTTabs").append(optionhtml);
                });

                $('#LSTTabs')[0].selectedIndex = selectedIndex;
                if (data.ToLST.length == selectedIndex) {
                    $('#LSTTabs')[0].selectedIndex = selectedIndex - 1;
                }

                $('#LST2 option').map(function () {
                    if ($(this).text() == selectedText) return this;
                }).attr('selected', 'selected');

                //debugger
                //var lst1count = $('#LST1 option').length;
                //var lst2count = $('#LST2 option').length;
                //var diff = Math.abs(lst1count - lst2count);
                //if (diff >= 1) {
                //    $("#BTN_EQUALHEIGHTS").removeAttr("disabled");
                //}
                //else {
                //    $("#BTN_EQUALHEIGHTS").attr("disabled", "disabled");
                //}
            },
            error: function (data) {
                return data;
            }
        })
    })

    $("#BTN_LINEUP3").click(function () {
        //debugger
        var skillsSelect = document.getElementById("LSTTabs");
        if (skillsSelect.selectedIndex == -1) {
            return false;
        }
        var selectedIndex = skillsSelect.selectedIndex;


        if (selectedIndex == 0) {
            return false;
        }

        $.ajax({
            url: "/ManageDesktops/BTN_LINEUP3",
            data: {
                index: selectedIndex
            },
            type: "post",
            success: function (data) {

                $('#LSTTabs').find('option').remove();

                $.each(data, function (i) {
                    var optionhtml = '<option value="' +
                    data[i].Value + '">' + data[i].Text + '</option>';
                    $("#LSTTabs").append(optionhtml);
                });

                $('#LSTTabs')[0].selectedIndex = selectedIndex - 1;
            },
            error: function (data) {
                return data;
            }
        })
    })

    $("#BTN_LINEDOWN3").click(function () {
        //debugger
        var skillsSelect = document.getElementById("LSTTabs");
        if (skillsSelect.selectedIndex == -1) {
            return false;
        }
        var selectedIndex = skillsSelect.selectedIndex;

        if (selectedIndex == $("#LSTTabs option").length - 1) {
            return false;
        }

        $.ajax({
            url: "/ManageDesktops/BTN_LINEDOWN3",
            data: {
                index: selectedIndex
            },
            type: "post",
            success: function (data) {

                $('#LSTTabs').find('option').remove();

                $.each(data, function (i) {
                    var optionhtml = '<option value="' +
                    data[i].Value + '">' + data[i].Text + '</option>';
                    $("#LSTTabs").append(optionhtml);
                });

                $('#LSTTabs')[0].selectedIndex = selectedIndex + 1;
            },
            error: function (data) {
                return data;
            }
        })
    })

    $("#BTN_LINEUP2").click(function () {
        //debugger
        var skillsSelect = document.getElementById("LST2");
        if (skillsSelect.selectedIndex == -1) {
            return false;
        }
        var selectedIndex = skillsSelect.selectedIndex;

        if (selectedIndex == 0) {
            return false;
        }

        $.ajax({
            url: "/ManageDesktops/BTN_LINEUP2",
            data: {
                index: selectedIndex
            },
            type: "post",
            success: function (data) {

                $('#LST2').find('option').remove();

                $.each(data, function (i) {
                    var optionhtml = '<option value="' +
                    data[i].Value + '">' + data[i].Text + '</option>';
                    $("#LST2").append(optionhtml);
                });

                $('#LST2')[0].selectedIndex = selectedIndex - 1;
            },
            error: function (data) {
                return data;
            }
        })
    })

    $("#BTN_LINEDOWN2").click(function () {
        //debugger
        var skillsSelect = document.getElementById("LST2");
        if (skillsSelect.selectedIndex == -1) {
            return false;
        }
        var selectedIndex = skillsSelect.selectedIndex;

        if (selectedIndex == $("#LST2 option").length - 1) {
            return false;
        }

        $.ajax({
            url: "/ManageDesktops/BTN_LINEDOWN2",
            data: {
                index: selectedIndex
            },
            type: "post",
            success: function (data) {

                $('#LST2').find('option').remove();

                $.each(data, function (i) {
                    var optionhtml = '<option value="' +
                    data[i].Value + '">' + data[i].Text + '</option>';
                    $("#LST2").append(optionhtml);
                });

                $('#LST2')[0].selectedIndex = selectedIndex + 1;
            },
            error: function (data) {
                return data;
            }
        })
    })

    $("#BTN_LINEUP1").click(function () {
        //debugger
        var skillsSelect = document.getElementById("LST1");
        if (skillsSelect.selectedIndex == -1) {
            return false;
        }
        var selectedIndex = skillsSelect.selectedIndex;

        if (selectedIndex == 0) {
            return false;
        }

        $.ajax({
            url: "/ManageDesktops/BTN_LINEUP1",
            data: {
                index: selectedIndex
            },
            type: "post",
            success: function (data) {

                $('#LST1').find('option').remove();

                $.each(data, function (i) {
                    var optionhtml = '<option value="' +
                    data[i].Value + '">' + data[i].Text + '</option>';
                    $("#LST1").append(optionhtml);
                });

                $('#LST1')[0].selectedIndex = selectedIndex - 1;
            },
            error: function (data) {
                return data;
            }
        })
    })

    $("#BTN_LINEDOWN1").click(function () {
        //debugger
        var skillsSelect = document.getElementById("LST1");
        if (skillsSelect.selectedIndex == -1) {
            return false;
        }
        var selectedIndex = skillsSelect.selectedIndex;

        if (selectedIndex == $("#LST1 option").length - 1) {
            return false;
        }

        $.ajax({
            url: "/ManageDesktops/BTN_LINEDOWN1",
            data: {
                index: selectedIndex
            },
            type: "post",
            success: function (data) {

                $('#LST1').find('option').remove();

                $.each(data, function (i) {
                    var optionhtml = '<option value="' +
                    data[i].Value + '">' + data[i].Text + '</option>';
                    $("#LST1").append(optionhtml);
                });

                $('#LST1')[0].selectedIndex = selectedIndex + 1;
            },
            error: function (data) {
                return data;
            }
        })
    })
</script>

<script>

    var datepickerfrom = $("#from").data("kendoDatePicker");
    var datepickerto = $("#to").data("kendoDatePicker");
    $(document).ready(function () {
		//btn_EDIT
		//debugger;
		@*$('#btn_EDIT').hide();
		var mode = '@Model.Mode';
		if (mode == 'Modif') {
			$('#btn_EDIT').show();
            $('#btn_OK').show();
		}*@
        if ($(window).height() < 900) {
            $('body').attr('style', 'overflow-y:visible !important');
        }
        else {
            $('body').attr('style', 'overflow:hidden !important');
        }
        @*debugger
        var CMBWCBUTTONSelectedValue = $("#Undernode").data("kendoMultiSelect");
        var SelectedNodeId = '@Model.CMBWCBUTTONSelectedIndex';
        if (SelectedNodeId.indexOf(',') > -1) {
            var SelectedNodeIds = SelectedNodeId.split(',');
            CMBWCBUTTONSelectedValue.value(SelectedNodeIds);
        }
        else {
            CMBWCBUTTONSelectedValue.value(SelectedNodeId);
        }*@
        
        @*CMBWCBUTTONSelectedValue.select(function (dataItem) {
            var SelectedNodeId = '@Model.CMBWCBUTTONSelectedIndex';
            CMBWCBUTTONSelectedValue.value(SelectedNodeId);
        });*@

        //debugger
        var LST1 = document.getElementById("LST1");
        LST1.selectedIndex = 0;

        var LST2 = document.getElementById("LST2");
        LST2.selectedIndex = 0;

        var LSTTab = document.getElementById("LSTTabs");
        LSTTab.selectedIndex = 0;

        var checked = $('#showSelector').prop('checked');
        if (checked) {
            $("#daterangeSelector").show();
            $("#hiddendaterangeSelector").hide();
        }

        var areafocus = '@Model.AreaInFocus';

        //To desable only when no views in all lists..J
        var LST1SelectedIndex = document.getElementById("LST1").selectedIndex;
        var LST2SelectedIndex = document.getElementById("LST2").selectedIndex;
        var LSTTabsSelectedIndex = document.getElementById("LSTTabs").selectedIndex;

        //if (LST1SelectedIndex == -1 && LST2SelectedIndex == -1 && LSTTabsSelectedIndex == -1) {
        //    $("#BTN_DUPLICATEVIEW").attr("disabled", "disabled");
        //    //$("#BTN_EDITVIEW").attr("disabled", "disabled");
        //    //$("#BTN_REMOVEVIEW").attr("disabled", "disabled");

        //    $("#BTN_EDITVIEW").attr("display", "none");    ////specific to dropdown column1 ..J  
        //    $("#BTN_REMOVEVIEW").attr("display", "none");
        //}
        //else {
        //    $("#BTN_DUPLICATEVIEW").removeAttr("disabled");
        //    //$("#BTN_EDITVIEW").removeAttr("disabled");
        //    //$("#BTN_REMOVEVIEW").removeAttr("disabled");

        //    $("#BTN_EDITVIEW").attr("display", "");    ////specific to dropdown column1 ..J  
        //    $("#BTN_REMOVEVIEW").attr("display", "");
        //}

        //debugger
        if (areafocus == "1") {
            $("#Column1").css("font-weight", "bold");
            $("#Column2").css("font-weight", "normal");
            $("#TabViews").css("font-weight", "normal");
            $("#LST1").focus();
            //var LST1SelectedIndex = document.getElementById("LST1").selectedIndex;
            //if (LST1SelectedIndex == -1) {
            //    $("#BTN_DUPLICATEVIEW").attr("disabled", "disabled");
            //    $("#BTN_EDITVIEW").attr("disabled", "disabled");
            //    $("#BTN_REMOVEVIEW").attr("disabled", "disabled");
            //}
            //else {
            //    $("#BTN_DUPLICATEVIEW").removeAttr("disabled");
            //    $("#BTN_EDITVIEW").removeAttr("disabled");
            //    $("#BTN_REMOVEVIEW").removeAttr("disabled");
            //}
        }
        else if (areafocus == "2") {
            $("#Column1").css("font-weight", "normal");
            $("#Column2").css("font-weight", "bold");
            $("#TabViews").css("font-weight", "normal");
            $("#LST2").focus();
            //var LST2SelectedIndex = document.getElementById("LST2").selectedIndex;
            //if (LST2SelectedIndex == -1) {
            //    $("#BTN_DUPLICATEVIEW").attr("disabled", "disabled");
            //    $("#BTN_EDITVIEW").attr("disabled", "disabled");
            //    $("#BTN_REMOVEVIEW").attr("disabled", "disabled");
            //}
            //else {
            //    $("#BTN_DUPLICATEVIEW").removeAttr("disabled");
            //    $("#BTN_EDITVIEW").removeAttr("disabled");
            //    $("#BTN_REMOVEVIEW").removeAttr("disabled");
            //}
        }
        else if (areafocus == "TABS") {
            $("#Column1").css("font-weight", "normal");
            $("#Column2").css("font-weight", "normal");
            $("#TabViews").css("font-weight", "bold");
            $("#LSTTabs").focus();
            //var LSTTabsSelectedIndex = document.getElementById("LSTTabs").selectedIndex;
            //if (LSTTabsSelectedIndex == -1) {
            //    $("#BTN_DUPLICATEVIEW").attr("disabled", "disabled");
            //    $("#BTN_EDITVIEW").attr("disabled", "disabled");
            //    $("#BTN_REMOVEVIEW").attr("disabled", "disabled");
            //}
            //else {
            //    $("#BTN_DUPLICATEVIEW").removeAttr("disabled");
            //    $("#BTN_EDITVIEW").removeAttr("disabled");
            //    $("#BTN_REMOVEVIEW").removeAttr("disabled");
            //}
        }
        //   debugger;
        var datepickerfrom = $("#from").data("kendoDatePicker");
        var datepickerto = $("#to").data("kendoDatePicker");
        var value = '@Model.CMBDRSDefaultSelectedValue';
        if (value == "CUSTOM") {
            //$("#fromshow").show();
            //$("#fromhide").hide();
            //$("#toshow").show();
            //$("#tohide").hide();
            datepickerfrom.enable(true);
            datepickerto.enable(true);
        }

        var frf = '@Model.CMBFRFCONTROLSelectedValue';
        if (frf == "") {
            $("#PNLLayoutSubVisible").show();
        }
        else {
            $("#PNLLayoutSubVisible").hide();
        }
    })
    function onChangeCustomDisplay(e) {
        var dataItem = this.dataItem(e.item);
        var value = dataItem.Value;
        if (value != "") {
            $("#PNLLayoutSubVisible").hide();
        }
        else {
            $("#PNLLayoutSubVisible").show();
        }
        var dropDownList = $('#customdisplay').data('kendoDropDownList');
        dropDownList.refresh();
    }

    function onChangeDateRange(e) {
        //  debugger
        var dataItem = this.dataItem(e.item);

        var datepickerfrom = $("#from").data("kendoDatePicker");
        var datepickerto = $("#to").data("kendoDatePicker");
        var value = dataItem.Value;
        if (value == "CUSTOM") {
            //$("#fromshow").show();
            //$("#fromhide").hide();
            //$("#toshow").show();
            //$("#tohide").hide();
            datepickerfrom.enable(true);
            datepickerto.enable(true);
        }
        else {
            datepickerfrom.enable(false);
            datepickerto.enable(false);
            //$("#fromshow").hide();
            //$("#fromhide").show();
            //$("#toshow").hide();
            //$("#tohide").show();
            $("#from").data("kendoDatePicker").value("");
            $("#to").data("kendoDatePicker").value("");
        }
    }

    function changeshowSelector() {
        var checked = $('#showSelector').prop('checked');
        var datepickerfrom = $("#from").data("kendoDatePicker");
        var datepickerto = $("#to").data("kendoDatePicker");
        if (!checked) {
            $("#hiddendaterangeSelector").show();
            $("#daterangeSelector").hide();
            //$("#fromshow").hide();
            //$("#fromhide").show();
            //$("#toshow").hide();
            //$("#tohide").show();
            datepickerfrom.enable(false);
            datepickerto.enable(false);
            $("#from").data("kendoDatePicker").value("");
            $("#to").data("kendoDatePicker").value("");
        }
        else {
            $("#hiddendaterangeSelector").hide();
            $("#daterangeSelector").show();
            var datepickerfrom = $("#from").data("kendoDatePicker");
            var datepickerto = $("#to").data("kendoDatePicker");
            var dataItem = $("#daterange").data("kendoDropDownList").value();
            if (dataItem == "CUSTOM") {
                //$("#fromshow").show();
                //$("#fromhide").hide();
                //$("#toshow").show();
                //$("#tohide").hide();
                datepickerfrom.enable(true);
                datepickerto.enable(true);
            }
        }
    }

    $("#IMG_MsgBoxClose").click(function () {
        $("#PNL_DeleteMessageBox").hide();
    });
</script>