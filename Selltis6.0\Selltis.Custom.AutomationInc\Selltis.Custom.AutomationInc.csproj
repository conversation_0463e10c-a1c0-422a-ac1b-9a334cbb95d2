﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{FDDD8171-563C-4AC2-B423-DD7DED4661E3}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Selltis.Custom.AutomationInc</RootNamespace>
    <AssemblyName>Selltis.Custom.AutomationInc</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\Selltis6.0\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DebugNew|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\DebugNew\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Ads.Common">
      <HintPath>..\Common\P21SDK_Lib\Ads.Common.dll</HintPath>
    </Reference>
    <Reference Include="Ads.Common.License">
      <HintPath>..\Common\P21SDK_Lib\Ads.Common.License.dll</HintPath>
    </Reference>
    <Reference Include="Ads.Common.Service">
      <HintPath>..\Common\P21SDK_Lib\Ads.Common.Service.dll</HintPath>
    </Reference>
    <Reference Include="Ads.Soa.Business">
      <HintPath>..\Common\P21SDK_Lib\Ads.Soa.Business.dll</HintPath>
    </Reference>
    <Reference Include="Ads.Soa.DomainObject">
      <HintPath>..\Common\P21SDK_Lib\Ads.Soa.DomainObject.dll</HintPath>
    </Reference>
    <Reference Include="Ads.Soa.Rest">
      <HintPath>..\Common\P21SDK_Lib\Ads.Soa.Rest.dll</HintPath>
    </Reference>
    <Reference Include="Ads.Soa.Soap">
      <HintPath>..\Common\P21SDK_Lib\Ads.Soa.Soap.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.WindowsAzure.Configuration, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\CommonDLLs\Microsoft.WindowsAzure.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage, Version=4.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\CommonDLLs\Microsoft.WindowsAzure.Storage.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.StorageClient">
      <HintPath>..\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Newtonsoft.Json.5.0.6\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="P21.Business">
      <HintPath>..\Common\P21SDK_Lib\P21.Business.dll</HintPath>
    </Reference>
    <Reference Include="P21.Common">
      <HintPath>..\Common\P21SDK_Lib\P21.Common.dll</HintPath>
    </Reference>
    <Reference Include="P21.DataAccess">
      <HintPath>..\Common\P21SDK_Lib\P21.DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="P21.DomainObject">
      <HintPath>..\Common\P21SDK_Lib\P21.DomainObject.dll</HintPath>
    </Reference>
    <Reference Include="P21.DomainObject.UDF">
      <HintPath>..\Common\P21SDK_Lib\P21.DomainObject.UDF.dll</HintPath>
    </Reference>
    <Reference Include="P21.FormsAndReporting.Model">
      <HintPath>..\Common\P21SDK_Lib\P21.FormsAndReporting.Model.dll</HintPath>
    </Reference>
    <Reference Include="P21.Soa.Service">
      <HintPath>..\Common\P21SDK_Lib\P21.Soa.Service.dll</HintPath>
    </Reference>
    <Reference Include="P21.UI">
      <HintPath>..\Common\P21SDK_Lib\P21.UI.dll</HintPath>
    </Reference>
    <Reference Include="P21.UI.BulkEditor">
      <HintPath>..\Common\P21SDK_Lib\P21.UI.BulkEditor.dll</HintPath>
    </Reference>
    <Reference Include="P21.UI.BulkEditor.Model">
      <HintPath>..\Common\P21SDK_Lib\P21.UI.BulkEditor.Model.dll</HintPath>
    </Reference>
    <Reference Include="P21.UI.BulkEditor.Service">
      <HintPath>..\Common\P21SDK_Lib\P21.UI.BulkEditor.Service.dll</HintPath>
    </Reference>
    <Reference Include="P21.UI.Service">
      <HintPath>..\Common\P21SDK_Lib\P21.UI.Service.dll</HintPath>
    </Reference>
    <Reference Include="PublicDomain, Version=0.2.50.0, Culture=neutral, PublicKeyToken=fd3f43b5776a962b, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\CommonDLLs\PublicDomain.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ScriptsCustom.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Selltis.BusinessLogic\Selltis.Data.csproj">
      <Project>{0718c809-a336-45b1-9029-253af6e1fc66}</Project>
      <Name>Selltis.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\Selltis.Core\Selltis.Core.csproj">
      <Project>{6f68b0de-b188-4aa1-aaae-5d90bc4a6942}</Project>
      <Name>Selltis.Core</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>