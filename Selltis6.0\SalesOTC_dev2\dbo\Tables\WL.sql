﻿CREATE TABLE [dbo].[WL] (
    [GID_ID]                        UNIQUEIDENTIFIER CONSTRAINT [DF_WL_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'WL',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                        BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                      NVARCHAR (80)    NULL,
    [DTT_CreationTime]              DATETIME         CONSTRAINT [DF_WL_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]                  TINYINT          NULL,
    [TXT_ModBy]                     VARCHAR (4)      NULL,
    [DTT_ModTime]                   DATETIME         CONSTRAINT [DF_WL_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_WebLeadName]               NVARCHAR (50)    NULL,
    [MMO_ImportData]                NTEXT            NULL,
    [SI__ShareState]                TINYINT          CONSTRAINT [DF_WL_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]              UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]                  VARCHAR (50)     NULL,
    [TXT_ExternalID]                NVARCHAR (1000)  NULL,
    [TXT_ExternalSource]            VARCHAR (10)     NULL,
    [TXT_ImpJobID]                  VARCHAR (20)     NULL,
    [MLS_STAGE]                     SMALLINT         NULL,
    [MLS_TYPEOFREQUEST]             SMALLINT         NULL,
    [MLS_PRODUCTTYPE]               SMALLINT         NULL,
    [TXT_FIRSTNAME]                 NVARCHAR (80)    NULL,
    [TXT_LASTNAME]                  NVARCHAR (80)    NULL,
    [TXT_COMPANYNAME]               NVARCHAR (100)   NULL,
    [EML_EMAIL]                     NVARCHAR (500)   NULL,
    [TEL_PHONENUMBER]               NVARCHAR (80)    NULL,
    [TXT_CITY]                      NVARCHAR (80)    NULL,
    [TXT_COUNTRY]                   NVARCHAR (80)    NULL,
    [MMO_COMMENTS]                  NTEXT            NULL,
    [GID_RELATED_IU]                UNIQUEIDENTIFIER NULL,
    [GID_ASSIGNEDTO_US]             UNIQUEIDENTIFIER NULL,
    [DTT_WLCreationTime]            DATETIME         NULL,
    [TXT_CreatedDateTime]           VARCHAR (50)     NULL,
    [DTT_CreationDateTimeInHubSpot] DATETIME         NULL,
    [GID_RELATED_CN]                UNIQUEIDENTIFIER NULL,
    [GID_RELATED_CO]                UNIQUEIDENTIFIER NULL,
    [TXT_INDUSTRY]                  NVARCHAR (250)   NULL,
    [TXT_WLNO]                      NVARCHAR (250)   NULL,
    [CHK_ContactValidated]          AS               (case when [Gid_Related_CN] IS NULL then (0) else (1) end),
    [GID_LINKED_PE]                 UNIQUEIDENTIFIER NULL,
    [MMO_DisqualifiedLead]          NTEXT            NULL,
    [DTT_RecordOpenDateTime]        DATETIME         NULL,
    [GID_COUNTRY_CY]                UNIQUEIDENTIFIER NULL,
    [GID_RELATED_SO]                UNIQUEIDENTIFIER NULL,
    [CHK_SyncedFromHubSpot]         TINYINT          NULL,
    CONSTRAINT [PK_WL] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_WL_WebLeadName]
    ON [dbo].[WL]([TXT_WebLeadName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_WL_CreatedBy_US]
    ON [dbo].[WL]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_WL_ModDateTime]
    ON [dbo].[WL]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_WL_Name]
    ON [dbo].[WL]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_WL_CreationTime]
    ON [dbo].[WL]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_WL_BI__ID]
    ON [dbo].[WL]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_WL_TXT_ImportID]
    ON [dbo].[WL]([TXT_ImportID] ASC);


GO




CREATE TRIGGER [dbo].[trWLUpdateTN]
ON [dbo].[WL]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in WL table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(1000)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'WL'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [WL]
			SET [WL].TXT_ExternalSource = '', [WL].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [WL].GID_ID = in1.GID_ID
				and ISNULL([WL].TXT_ExternalSource, '') <> ''
				and ISNULL([WL].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trWLUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trWLUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	 
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trWLUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!

GO

CREATE TRIGGER [dbo].[trWL_AfterInsert]
ON [dbo].[WL]
AFTER INSERT 
AS
BEGIN

    Declare @NewWLNo nvarchar(50)=''  

     Select @NewWLNo = 'SEWL-' + Isnull(try_Cast(Max(try_Cast(Replace(Isnull(TXT_WLNO,'SEWL-9999'),'SEWL-','') as int)) + 1 as nvarchar(20)),'10000') 
     from WL 
	 WHERE TXT_WLNO like 'SEWL-%'
        
   Update l 
   Set 
   l.TXT_WLNO=@NewWLNo
   , l.GID_RELATED_CN= c.GID_ID
   ,l.GID_RELATED_CO=c.GID_Related_CO 
   ,l.GID_LINKED_PE = p.GID_ID
   ,l.GID_RELATED_IU=b.GID_ID
   ,l.MLS_Stage = 1
   ,l.GID_COUNTRY_CY = y.GID_ID
   FROM inserted i
   JOIN WL l on l.Gid_id=i.GID_ID
   LEFT JOIN CN c on i.EML_EMAIL=Cast(c.EML_Email as nvarchar(500))
   Left Join IU b on b.TXT_IndustryName=i.TXT_INDUSTRY
   LEFT JOIN MD m on m.TXT_Property = cast(i.MLS_PRODUCTTYPE as nvarchar(100)) AND m.txt_Page='LST_WL:ProductType'
   LEFT JOIN PE p on p.TXT_ProductTypeName = m.TXT_Value
   LEFT JOIN CY y on y.TXT_CountryName = i.TXT_COUNTRY
    
END

   
