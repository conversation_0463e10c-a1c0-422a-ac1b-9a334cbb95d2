'OWNER: RH

Imports Microsoft.VisualBasic

Public Class clInfoMessage

    Protected cArrayCollection As New Collection

    Public Sub AddInfo(ByVal par_sString As String)

        cArrayCollection.Add(par_sString)


    End Sub

    Public Sub ClearArray()

        cArrayCollection = Nothing
        cArrayCollection = New Collection

    End Sub

    Public Function CopyAllIn(ByRef par_doIM As clInfoMessage) As Boolean

        Try

            Dim i As Long

            For i = 1 To cArrayCollection.Count
                par_doIM.AddInfo(cArrayCollection(i))
            Next

            Return True

        Catch ex As Exception

            Return False

        End Try




    End Function

    Public Function CopyAllInString() As String

        Dim i As Long
        Dim sString As String = ""

        For i = 1 To cArrayCollection.Count
            sString = sString & cArrayCollection(i) & vbCrLf
        Next

        Return sString


    End Function

    Public Function GetDimension() As Long

        Return cArrayCollection.Count


    End Function

    Public Function GetInfo(ByVal par_lItem As Long) As String

        If par_lItem < 1 Then
            Return ""
        End If
        If par_lItem > cArrayCollection.Count Then
            Return ""
        End If
        Return cArrayCollection.Item(par_lItem)


    End Function

    Public Function ModInfo(ByVal par_lItem As Integer, ByVal par_sNewValue As String) As Boolean
        'MI 4/20/10 Added Return True at the end
        'PURPOSE:
        '       Edit an element. This deletes the element and adds the 'edited' element at
        '       the end of the collection.
        If par_lItem < 1 Then
            Return False
        End If
        If par_lItem > cArrayCollection.Count Then
            Return False
        End If
        cArrayCollection.Remove(par_lItem)
        cArrayCollection.Add(par_sNewValue)
        Return True         'MI 4/20/10


    End Function

    Public Function DeleteInfo(ByVal par_lItem As Integer) As Boolean
        'MI 4/20/10 Added
        'Delete the line at position par_lItem
        If par_lItem < 1 Then
            Return False
        End If
        If par_lItem > cArrayCollection.Count Then
            Return False
        End If
        cArrayCollection.Remove(par_lItem)
        Return True

    End Function

    Public Function SeekInfo(ByVal par_sInfo As String, _
                      Optional ByVal par_bMode As Boolean = False, _
                      Optional ByVal par_bCaseSensitive As Boolean = True) As Long
        'MI 11/21/06    Added par_bCaseSensitive
        'MI 4/11/06 changed return type to Long, changed final Return from Return "" to Return 0

        'PURPOSE:
        '		Seek a string in the array and return the index if found
        'PARAMETERS:
        '		par_sInfo:	String to search in the array
        '		par_bMode:	if False, returns the index if the beginning of the string in the array=the string to test
        '					if true, returns the index only if both string are EXACTLY the same
        'RETURNS:
        '		the index in the array where the string was found (method depends of par_bMode), 0 if not found
        'HOW IT WORKS:
        '		loop on the array and compare
        'EXAMPLE:
        '		lIndex=goIM:SeeekInfo(sString)

        Dim i As Integer

        For i = 1 To cArrayCollection.Count
            If par_bMode Then
                If par_bCaseSensitive Then
                    If cArrayCollection.Item(i) = par_sInfo Then Return i
                Else
                    If UCase(cArrayCollection.Item(i)) = UCase(par_sInfo) Then Return i
                End If
            Else
                If par_bCaseSensitive Then
                    If Left(cArrayCollection.Item(i), Len(par_sInfo)) = par_sInfo Then Return i
                Else
                    If UCase(Left(cArrayCollection.Item(i), Len(par_sInfo))) = UCase(par_sInfo) Then Return i
                End If
            End If
        Next

        Return 0

    End Function







End Class
