Imports Microsoft.VisualBasic
Imports System.Collections.Specialized

Public Class clWebForm

    Public WorkgroupOptions As String = ""
    Public PageURL As String = ""
    Public HTML As String = ""
    'Public FieldValueList As New SortedList
    Public FieldValueList As New NameValueCollection
    Public EmailAlias As String = ""
    Public SelectHTMLStrings As New Collection
    Public LinkList As New SortedList

    Public _WFSUBMITTO As String = ""
    Public _REQDINCOMPLETE As String = ""
    Public _WFTYPE As String = ""
    Public _WFPURPOSE As String = ""

    Public Sub AddSelectHTMLString(ByVal sFile As String, ByVal sHTML As String)
        If SelectHTMLStrings.Contains(sFile) Then
            SelectHTMLStrings.Remove(sFile)
        End If
        SelectHTMLStrings.Add(sHTML, sFile)
    End Sub

    Public Function GetSelectHTMLString(ByVal sFile As String) As String
        If SelectHTMLStrings.Contains(sFile) Then
            Return SelectHTMLStrings.Item(sFile)
        Else
            Return ""
        End If
    End Function

End Class
