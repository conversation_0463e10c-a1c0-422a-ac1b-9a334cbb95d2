﻿CREATE TABLE [dbo].[RI_Involves_CO] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_RI_Involves_CO_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_RI] UNIQUEIDENTIFIER NOT NULL,
    [GID_CO] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_RI_Involves_CO] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CO_InvolvedWith_RI] FOREIGN KEY ([GID_RI]) REFERENCES [dbo].[RI] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_RI_Involves_CO] FOREIGN KEY ([GID_CO]) REFERENCES [dbo].[CO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[RI_Involves_CO] NOCHECK CONSTRAINT [LNK_CO_InvolvedWith_RI];


GO
ALTER TABLE [dbo].[RI_Involves_CO] NOCHECK CONSTRAINT [LNK_RI_Involves_CO];


GO
CREATE NONCLUSTERED INDEX [IX_CO_InvolvedWith_RI]
    ON [dbo].[RI_Involves_CO]([GID_CO] ASC, [GID_RI] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RI_Involves_CO]
    ON [dbo].[RI_Involves_CO]([GID_RI] ASC, [GID_CO] ASC);

