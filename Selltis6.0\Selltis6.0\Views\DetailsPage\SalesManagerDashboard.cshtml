﻿
@model Selltis.MVC.Models.OutsideSales
@{
    ViewBag.Title = "SalesManagerDashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>SalesManagerDashboard</h2>

<head>


    <link href="~/Content/assets/js/jquery-ui/css/no-theme/jquery-ui-1.10.3.custom.css" rel="stylesheet" />
    <link rel="stylesheet" href="~/Content/assets/css/font-icons/entypo/css/entypo.css">
    <link rel="stylesheet" href="~/Content/assets/js/select2/select2-bootstrap.css">
    <link rel="stylesheet" href="~/Content/assets/js/select2/select2.css">
    <link rel="stylesheet" href="~/Content/assets/js/datatables/datatables.css">
    <link rel="stylesheet" href="~/Content/assets/js/zurb-responsive-tables/responsive-tables.css">
    <link rel="stylesheet" href="~/Content/assets/js/vertical-timeline/css/component.css">

    <link rel="stylesheet" href="~/Content/assets/css/custom.css">

    <script src="~/Content/assets/js/gsap/TweenMax.min.js"></script>


</head>
<style>

    .mt-20 {
        color: black;
    }

    .table-bordered > thead > tr > th, .table-bordered > tbody > tr > th, .table-bordered > tfoot > tr > th, .table-bordered > thead > tr > td, .table-bordered > tbody > tr > td, .table-bordered > tfoot > tr > td {
        border: 0px solid #ebebeb !important
    }

    .table-bordered > thead > tr > th {
        font-weight: 400
    }

    .table-bordered > tfoot > tr > td, .table-bordered > tfoot > tr > th, .table-bordered > thead > tr > td, .table-bordered > thead > tr > th {
        background-color: #ecf1f7;
        color: #a6a7aa;
    }

    .page-content-container {
        padding: 5px 15px 15px 15px;
        border-radius: 3px;
        background-color: white
    }

    h4 {
        font-size: 16px;
        color: #00366c;
        font-family: inherit;
        font-weight: 500;
    }

    table {
        border-collapse: collapse;
        border-spacing: 0
    }

    a {
        color: #373e4a;
    }

    .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
        position: relative;
        min-height: 1px;
        padding-left: 15px;
        padding-right: 15px
    }

    .table thead th, .table tbody td {
        text-align: right;
    }

        .table thead th:first-child, .table tbody td:first-child {
            text-align: left;
        }

    .mb-10 {
        margin-bottom: 10px !important;
        margin-top: 10px !important;
    }

    .table > thead > tr > th {
        padding: 8px;
        line-height: 1.********;
    }

    .text-left-i-quotes div, .text-left-i-contacts div, .text-left-i-opps div, .text-left-i-Accounts div, .text-left-i-activities div, .text-left-i-activitiesSV div, .text-left-i-Tasks div {
        width: auto;
        overflow: hidden;
        white-space: normal;
        text-overflow: ellipsis;
    }

    .Main-name {
        font-size: 12px !important;
        color: #373E4A;
    }

    .Sub-name {
        font-family: 'Open Sans', Helvetica, Arial, sans-serif !important;
        font-size: 10px;
        color: #949494;
    }

    .hide-row {
        display: none;
    }

    .table-link {
        color: #a6a7aa;
        font-size: 10px !important;
        margin-right: 15px;
        display: inline-flex;
        align-items: center;
    }


        .table-link i {
            font-size: 8px;
            margin-left: 5px;
        }

    .icon {
        font-size: 60px;
        font-weight: bold;
        line-height: 100%;
        color: #ECF1F7;
        font-style: normal;
    }

    .heading {
        color: white;
    }



    /*Page content css*/
    .social-icons {
        font-size: 4rem;
    }

        .social-icons li {
            display: inline;
            float: left;
        }

            .social-icons li i:before {
                margin: 0px;
            }

    .profile-name aside.user-thumb img {
        max-width: 70px;
    }

    .contacts-all-btn-icon {
        font-size: 4rem;
    }




    .text-decoration-none {
        text-decoration: none !important;
    }

    .profile-name {
        color: #fff;
    }

    .tile-stats {
        position: relative;
        display: block;
        background: #303641;
        padding: 20px;
        margin-bottom: 12px;
        overflow: hidden;
        -webkit-border-radius: 5px;
        -webkit-background-clip: padding-box;
        -moz-border-radius: 5px;
        -moz-background-clip: padding;
        border-radius: 5px;
        background-clip: padding-box;
        -webkit-transition: all 300ms ease-in-out;
        -moz-transition: all 300ms ease-in-out;
        -o-transition: all 300ms ease-in-out;
        transition: all 300ms ease-in-out;
    }

        .tile-stats:hover {
            background: #252a32;
        }

        .tile-stats .icon {
            color: rgba(0, 0, 0, 0.1);
            position: absolute;
            right: 5px;
            bottom: 5px;
            z-index: 1;
        }

            .tile-stats .icon i {
                font-size: 100px;
                line-height: 0;
                margin: 0;
                padding: 0;
                vertical-align: bottom;
            }

                .tile-stats .icon i:before {
                    margin: 0;
                    padding: 0;
                    line-height: 0;
                }

        .tile-stats .num,
        .tile-stats h3,
        .tile-stats p {
            position: relative;
            color: #fff;
            z-index: 5;
            margin: 0;
            padding: 0;
        }

        .tile-stats .num {
            font-size: 38px;
            font-weight: bold;
        }

        .tile-stats h3 {
            font-size: 18px;
            margin-top: 5px;
        }

        .tile-stats p {
            font-size: 11px;
            margin-top: 5px;
        }

        .tile-stats.tile-red {
            background: #f56954;
        }

            .tile-stats.tile-red:hover {
                background: #f4543c;
            }

            .tile-stats.tile-red .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-red .num,
            .tile-stats.tile-red h3,
            .tile-stats.tile-red p {
                color: #fff;
            }

        .tile-stats.tile-green {
            background: #00a65a;
        }

            .tile-stats.tile-green:hover {
                background: #008d4c;
            }

            .tile-stats.tile-green .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-green .num,
            .tile-stats.tile-green h3,
            .tile-stats.tile-green p {
                color: #fff;
            }

        .tile-stats.tile-blue {
            background: #0073b7;
        }

            .tile-stats.tile-blue:hover {
                background: #00639e;
            }

            .tile-stats.tile-blue .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-blue .num,
            .tile-stats.tile-blue h3,
            .tile-stats.tile-blue p {
                color: #fff;
            }

        .tile-stats.tile-aqua {
            background: #00c0ef;
        }

            .tile-stats.tile-aqua:hover {
                background: #00acd6;
            }

            .tile-stats.tile-aqua .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-aqua .num,
            .tile-stats.tile-aqua h3,
            .tile-stats.tile-aqua p {
                color: #fff;
            }

        .tile-stats.tile-cyan {
            background: #00b29e;
        }

            .tile-stats.tile-cyan:hover {
                background: #009987;
            }

            .tile-stats.tile-cyan .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-cyan .num,
            .tile-stats.tile-cyan h3,
            .tile-stats.tile-cyan p {
                color: #fff;
            }

        .tile-stats.tile-purple {
            background: #ba79cb;
        }

            .tile-stats.tile-purple:hover {
                background: #b167c4;
            }

            .tile-stats.tile-purple .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-purple .num,
            .tile-stats.tile-purple h3,
            .tile-stats.tile-purple p {
                color: #fff;
            }

        .tile-stats.tile-pink {
            background: #ec3b83;
        }

            .tile-stats.tile-pink:hover {
                background: #ea2474;
            }

            .tile-stats.tile-pink .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-pink .num,
            .tile-stats.tile-pink h3,
            .tile-stats.tile-pink p {
                color: #fff;
            }

        .tile-stats.tile-orange {
            background: #ffa812;
        }

            .tile-stats.tile-orange:hover {
                background: #f89d00;
            }

            .tile-stats.tile-orange .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-orange .num,
            .tile-stats.tile-orange h3,
            .tile-stats.tile-orange p {
                color: #fff;
            }

        .tile-stats.tile-brown {
            background: #6c541e;
        }

            .tile-stats.tile-brown:hover {
                background: #584418;
            }

            .tile-stats.tile-brown .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-brown .num,
            .tile-stats.tile-brown h3,
            .tile-stats.tile-brown p {
                color: #fff;
            }

        .tile-stats.tile-plum {
            background: #701c1c;
        }

            .tile-stats.tile-plum:hover {
                background: #5c1717;
            }

            .tile-stats.tile-plum .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-plum .num,
            .tile-stats.tile-plum h3,
            .tile-stats.tile-plum p {
                color: #fff;
            }

        .tile-stats.tile-gray {
            background: #f5f5f5;
        }

            .tile-stats.tile-gray:hover {
                background: #e8e8e8;
            }

            .tile-stats.tile-gray .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-gray .num,
            .tile-stats.tile-gray h3,
            .tile-stats.tile-gray p {
                color: #8f8f8f;
            }

        .tile-stats.tile-white {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white .num,
            .tile-stats.tile-white h3,
            .tile-stats.tile-white p {
                color: #303641;
            }

            .tile-stats.tile-white:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-red {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-red:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-red .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-red .num,
            .tile-stats.tile-white-red h3,
            .tile-stats.tile-white-red p {
                color: #f56954;
            }

            .tile-stats.tile-white-red:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-green {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-green:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-green .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-green .num,
            .tile-stats.tile-white-green h3,
            .tile-stats.tile-white-green p {
                color: #00a65a;
            }

            .tile-stats.tile-white-green:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-blue {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-blue:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-blue .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-blue .num,
            .tile-stats.tile-white-blue h3,
            .tile-stats.tile-white-blue p {
                color: #0073b7;
            }

            .tile-stats.tile-white-blue:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-aqua {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-aqua:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-aqua .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-aqua .num,
            .tile-stats.tile-white-aqua h3,
            .tile-stats.tile-white-aqua p {
                color: #00c0ef;
            }

            .tile-stats.tile-white-aqua:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-cyan {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-cyan:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-cyan .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-cyan .num,
            .tile-stats.tile-white-cyan h3,
            .tile-stats.tile-white-cyan p {
                color: #00b29e;
            }

            .tile-stats.tile-white-cyan:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-purple {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-purple:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-purple .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-purple .num,
            .tile-stats.tile-white-purple h3,
            .tile-stats.tile-white-purple p {
                color: #ba79cb;
            }

            .tile-stats.tile-white-purple:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-pink {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-pink:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-pink .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-pink .num,
            .tile-stats.tile-white-pink h3,
            .tile-stats.tile-white-pink p {
                color: #ec3b83;
            }

            .tile-stats.tile-white-pink:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-orange {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-orange:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-orange .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-orange .num,
            .tile-stats.tile-white-orange h3,
            .tile-stats.tile-white-orange p {
                color: #ffa812;
            }

            .tile-stats.tile-white-orange:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-brown {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-brown:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-brown .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-brown .num,
            .tile-stats.tile-white-brown h3,
            .tile-stats.tile-white-brown p {
                color: #6c541e;
            }

            .tile-stats.tile-white-brown:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-plum {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-plum:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-plum .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-plum .num,
            .tile-stats.tile-white-plum h3,
            .tile-stats.tile-white-plum p {
                color: #701c1c;
            }

            .tile-stats.tile-white-plum:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-gray {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-gray:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-gray .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-gray .num,
            .tile-stats.tile-white-gray h3,
            .tile-stats.tile-white-gray p {
                color: #8f8f8f;
            }

            .tile-stats.tile-white-gray:hover {
                background-color: #fafafa;
            }

        .tile-stats.stat-tile {
            padding: 0px;
            height: 155px;
            border: none !important;
            box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
        }

        .tile-stats.tile-neon-red {
            background: #ff4e50;
        }

        .tile-stats.stat-tile h3 {
            padding: 20px 20px 0px 20px;
        }

        .tile-stats.stat-tile p {
            padding: 0px 20px 20px 20px;
            margin-bottom: 20px;
        }

    .tile-reg-users {
        background: #8a6edc !important;
    }

    .tile-stats {
        min-height: 149px;
    }

    .tile-oppstatus {
        background: #373E4A;
    }

    .tile-quicklinks {
        background: #ffffff;
        border: 1px solid #EBEBEB;
    }



    .company-details label, .company-details p {
        font-size: 12px;
    }

    .company-details label {
        color: #00366c;
        margin-bottom: 0px;
    }

    .tile-stats .icon i {
        position: relative;
        top: -20px
    }

    .tile-stats {
        min-height: 149px
    }

        .tile-stats .num {
            font-size: 60px;
            font-weight: bold;
            line-height: 100%
        }

        .tile-stats p {
            font-size: 14px
        }

            .tile-stats p a {
                text-decoration: underline
            }

    .tile-quicklinks {
        background: #ffffff;
        border: 1px solid #EBEBEB
    }

        .tile-quicklinks div.num {
            color: #373E4A
        }

        .tile-quicklinks h3 {
            color: #373E4A
        }

        .tile-quicklinks p {
            color: #373E4A
        }

        .tile-quicklinks:hover {
            background: #ffffff
        }

    .tile-oppstatus {
        background: #da58a4 !important
    }

    .tile-oppstatus1 {
        background: #40587C !important
    }

    .tile-oppstatus2 {
        background: #30D5C8 !important
    }

    .tile-aopotentials {
        background: #5bce29 !important;
    }

    .tile-aopotentials1 {
        background: #f17b00 !important;
    }

    .name-title-info-list {
        padding: 5px 15px
    }

        .name-title-info-list .name-title-info-item {
            padding-top: 5px !important;
            padding-bottom: 5px !important
        }

        .name-title-info-list .name-title {
            font-size: 14px;
            color: #373E4A
        }

        .name-title-info-list .sub-phone-email {
            font-size: 10px;
            color: #949494
        }

    .timeline {
        border: 1px solid #f5f5f6;
        border-radius: 3px;
        padding: 15px
    }

    ul.cbp_tmtimeline > li .cbp_tmlabel, ul.cbp_tmtimeline > li .cbp_tmlabel.empty {
        margin-bottom: 15px !important
    }

    .cbp_tmtimeline > li .cbp_tmlabel {
        background: #F5F5F7;
        padding: 1.2rem 1.6rem
    }

    .company-details label {
        color: #00366c;
        margin-bottom: 0px
    }

    .company-details label, .company-details p {
        font-size: 12px
    }

    .border-light-gray {
        border: 1px solid #f5f5f6;
    }

    .txt-light-gray {
        color: #949494;
    }

    .edit-btn {
        font-size: 12px;
    }

    .ml-10 {
        margin-left: 10px;
    }

    .company-details label, .company-details p {
        font-size: 12px;
    }

    .company-details label {
        color: #00366c;
        margin-bottom: 0px;
    }

    label {
        display: inline-block;
        max-width: 100%;
        margin-bottom: 5px;
        font-weight: bold;
    }

    .cbp_tmtimeline:before {
        background: #f5f5f6;
        width: 5px;
        margin-left: -6px;
    }


    #content.table-layout > div, #content.table-layout > section {
        vertical-align: top;
        padding: 0px 0px 0px !important;
    }

    .tile-stats {
        cursor: pointer;
    }

    th a:hover {
        color: #3498db !important;
    }

    .responsive-container {
        overflow-x: auto;
        display: block;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        margin: 0 auto;
    }

    .table {
        width: 100%;
        table-layout: auto;
        border-collapse: collapse;
    }

        .table th, .table td {
            white-space: nowrap;
            text-align: left;
            vertical-align: middle;
        }

            .table th a, .table td a {
                display: inline-block;
            }


    .responsive-container {
        padding: 0 10px;
    }

    .header-row .custom-header-buttons {
        display: flex;
        flex-direction: column;
    }

    .d-flex {
        display: flex;
    }

    .flex-wrap {
        flex-wrap: wrap;
    }

    .justify-content-between {
        justify-content: space-between;
    }

    .align-items-center {
        align-items: center;
    }

    .gap-1 a {
        margin-right: 10px;
        text-decoration: none;
    }

        .gap-1 a:last-child {
            margin-right: 0;
        }

    footer.main {
        display: none;
    }

    @@media (min-width: 768px) {
        .header-row .custom-header-buttons {
            flex-direction: row;
            align-items: center;
        }

        .gap-1 {
            margin-left: auto;
        }
    }

    .custom-header-buttons .d-flex a {
        text-decoration: none;
        color: inherit;
        font-weight: 500;
    }

        .custom-header-buttons .d-flex a:hover {
            text-decoration: underline;
            color: #007bff;
        }

    @@media (max-width: 768px) {
        .custom-header-buttons .d-flex {
            flex-direction: column;
        }
    }

    .table-container {
        display: block; /* Block display for both tables */
        margin-top: 20px;
    }

    /* Ensure both tables take up the full width */
    .table {
        width: 100%;
        border-collapse: collapse;
        table-layout: fixed; /* Ensures equal width for each column */
    }

        .table th, .table td {
            padding: 10px;
            text-align: left;
            word-wrap: break-word; /* Prevents overflow in case of long text */
        }

        /* Apply background to header for both tables */
        .table th {
            background-color: #f1f1f1;
        }
</style>

<!-- TOP FIXED NAVIGATION -->
<section id="content_wrapper" style="height: calc(100%); padding-top:0 !important">
    <!-- Begin: Content -->
    <section id="content" style="height:100%" class=" table-layout animated fadein">

        <div class="page-container">
            <div class="main-content" style="background-color:#003665;padding:12px;height:90%">
                <div class="d-flex justify-content-between align-items-center" style="position: relative; width: 100%; color: white;">
                    <!-- Left Section: Title -->
                    @*<div class="company-title ml-10 mb-10">
                            <span style="font-size: 18px;">Sales Person Segment Dashboard</span>
                        </div>*@
                    <div class="company-title ml-10 mb-10" style="display: flex; align-items: center;">
                        <span style="font-size: 18px; margin-right: 10px;">Sales Manager Dashboard</span>
                        <div id="dropdownTree" style="width: 300px; margin-right: 20px;">

                            @(Html.Kendo().DropDownTree()
                             .Name("SalesPersonHierarchy")
                             .DataTextField("Name")
                             .DataValueField("id")
                             .Height("400px")
                             .Filter("startswith")
                             .MinLength(3) // Wait for 3 characters
                             .HtmlAttributes(new { style = "width: 90%" })
                             .DataSource(dataSource => dataSource
                                 .Read(read => read.Action("GetSegmentLeaderHierarchy", "DetailsPage"))
                                 //.ServerFiltering(true)
                             )
                             .Events(e => e.Change("onChange"))
                         )
                            @*.*@

                        </div>
                        <div id="breadcrumb" style="font-size: 16px; display: flex; align-items: center;">
                            <ul class="breadcrumb">
                            </ul>
                        </div>
                    </div>

                </div>

                <div class="page-content-container">
                    <!--tile's-->
                    <div class="row my-4">
                        <div class="col-lg-4 col-md-6 col-xs-12">
                            <div class="tile-stats tile-oppstatus d-flex justify-content-start align-items-center" onclick="gotoDesktop('DSK_423F6AAB-3975-4488-5858-B27E014B859F')">
                                <div>
                                    <div class="icon"><i class="fa fa-hashtag"></i></div>
                                    <div class="num" id="overDueprimaryAccountsQtytile"></div>
                                    <h3>Overdue Primary Accounts</h3>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 col-xs-12">
                            <div class="tile-stats tile-aopotentials" onclick="gotoDesktop('DSK_40B08A04-DA97-4074-5858-B2880076F639')">
                                <div class="icon"><i class="fa fa-dollar"></i></div>
                                <div class="num" id="myQuotePipeline"></div>
                                <h3>Quote pipeline</h3>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 col-xs-12 ">
                            <div class="tile-stats tile-oppstatus1 d-flex justify-content-start align-items-center" onclick="gotoDesktop('DSK_A0DF8D41-E32E-4CE6-5858-B27D00C74B8A')">
                                <div>
                                    <div class="icon"><i class="fa fa-hashtag"></i></div>
                                    <div class="num" id="MyLeadsTile"></div>
                                    <h3>Overdue Leads</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row my-4">
                        <div class="col-lg-4 col-md-6 col-xs-12">
                            <div class="tile-stats tile-oppstatus2" onclick="gotoDesktop('DSK_B782CACD-C145-40B6-5858-B27D017E86F0')">
                                <div class="icon"><i class="fa fa-hashtag"></i></div>
                                <div class="num" id="openStrategicQuotesQtytile"></div>
                                <h3>Overdue Strategic Quotes</h3>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 col-xs-12">
                            <div class="tile-stats tile-reg-users" onclick="gotoDesktop('DSK_EB2BF4D5-0FED-4BE9-5858-B261016BF25D')">
                                <div class="icon"><i class="fa fa-hashtag"></i></div>
                                <div class="num" id="MyTaskstile"></div>
                                <h3>Overdue Tasks</h3>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 col-xs-12">
                            <div class="tile-stats tile-aopotentials1" onclick="gotoDesktop('DSK_B40EF2B0-2F73-40DD-5858-B27D01802B74')">
                                <div class="icon"><i class="fa fa-hashtag"></i></div>
                                <div class="num" id="MyOpenOppsqtytile"></div>
                                <h3>Overdue Opportunities</h3>
                            </div>
                        </div>

                    </div>
                    <!--Body-->
                    <div class="row mt-20">
                        <div class="col-lg-4 col-xs-12">
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0">Accounts</h4>
                                    <span class="d-flex gap-1">
                                        <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_48A5D0AE-6889-4886-5858-B292001ED134','','','')">&nbsp;&nbsp;&nbsp;&nbsp;<i class="entypo-eye"></i> View All</a>
                                        <a href="#" style="color: #a6a7aa; margin-right: 5px;" onclick="gotoDesktop('DSK_0B9E62DB-3FE2-4E50-5858-AC4F0085282F','','','')"><i class="entypo-eye"></i> View Recent</a>
                                        <a href="#" style="color: #a6a7aa;" onclick="PrepareFormLink('CO','CRU_CO')"><i class="entypo-plus"></i> Add</a>
                                    </span>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_9E166A86-0262-4C04-5858-B26101698EE6');" class="primary-account-link">Primary Accounts</a>
                                                    @*<span class="d-flex gap-1">
                                                            <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_2004063009585560154C_S 00016XX','','','')"><i class="entypo-eye"></i> View All</a>
                                                            <a href="#" style="color: #a6a7aa; margin-right: 5px;" onclick="gotoDesktop('DSK_0B9E62DB-3FE2-4E50-5858-AC4F0085282F','','','')"><i class="entypo-eye"></i> View Recent</a>
                                                            <a href="#" style="color: #a6a7aa;" onclick="PrepareFormLink('CO','CRU_CO')"><i class="entypo-plus"></i> Add</a>
                                                        </span>*@
                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_22D3897A-6265-47F3-5858-B261016A05B0')">New Accounts</a></td>
                                            <td>  <a href="#" onclick="gotoDesktop('DSK_22D3897A-6265-47F3-5858-B261016A05B0')"><p style="font-size:105%" id="accountQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_D8BD9FA3-F906-487D-5858-B261016AC211')">Team Sell Accounts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_D8BD9FA3-F906-487D-5858-B261016AC211')"><p style="font-size: 105% " id="teamSellAccountsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_C58F44F0-6241-4E22-5858-B261016B30F2')">Overserved Accounts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_C58F44F0-6241-4E22-5858-B261016B30F2')"><p style="font-size: 105% " id="overservedAccountsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_C58F44F0-6241-4E22-5858-B261016B30F2')">Underserved Accounts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_C58F44F0-6241-4E22-5858-B261016B30F2')"><p style="font-size: 105% " id="underservedAccountsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_423F6AAB-3975-4488-5858-B27E014B859F')">Overdue Primary Accounts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_423F6AAB-3975-4488-5858-B27E014B859F')"><p style="font-size: 105% " id="overDueprimaryAccountsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_76B47119-937A-4326-5858-B2790148AF97')">Primary Accounts Without Contacts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_76B47119-937A-4326-5858-B2790148AF97')"><p style="font-size: 105% " id="accountsWithoutContacts"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_CD45098F-0187-450D-5858-B27901491345')">Primary Accounts Without Activities</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_CD45098F-0187-450D-5858-B27901491345')"><p style="font-size: 105% " id="accountsWithoutActivities"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td class="text-left-i-Accounts" colspan="2" style="text-align: left">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0 ">Contacts</h4>
                                    <span class="d-flex gap-1">
                                        <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_2004051408135030253C_S 00016XX','','','')">&nbsp;&nbsp;&nbsp;&nbsp;<i class="entypo-eye"></i> View All</a>
                                        @*<a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_2004110817164953089MAR 00014XX','','','')"><i class="entypo-eye"></i> View All My</a>*@
                                        <a href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_F38C50BB-CB7D-4A45-5858-AC4F00A2F11A','','','')"><i class="entypo-eye"></i> View Recent</a>
                                    </span>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_A7DEDA4D-DAC7-4FEB-5858-B261016BA14A')" class="primary-account-link">Contacts</a>

                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_A7DEDA4D-DAC7-4FEB-5858-B261016BA14A')">New Contacts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_A7DEDA4D-DAC7-4FEB-5858-B261016BA14A')"><p style="font-size: 105% " id="newContactsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_A7DEDA4D-DAC7-4FEB-5858-B261016BA14A')">Key Contacts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_A7DEDA4D-DAC7-4FEB-5858-B261016BA14A')"><p style="font-size: 105% " id="keyContactsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_A7DEDA4D-DAC7-4FEB-5858-B261016BA14A')">Overdue Contacts</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_A7DEDA4D-DAC7-4FEB-5858-B261016BA14A')"><p style="font-size: 105% " id="overdueContactsQty"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td class="text-left-i-contacts" colspan="2" style="text-align: left">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0 ">Tasks</h4>
                                    <span class="d-flex gap-1">
                                        <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_8F5B6E8A-EB7D-4A9F-5858-B2920020BDD8','','','')">&nbsp;&nbsp;&nbsp;&nbsp;<i class="entypo-eye"></i> View All</a>
                                        <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_EB2BF4D5-0FED-4BE9-5858-B261016BF25D')"><i class="entypo-eye"></i> View Recent</a>
                                        @*<a href="#" style="color: #a6a7aa;" onclick=PrepareFormLink('TD','CRU_TD')><i class="entypo-plus"></i> Add</a>*@
                                    </span>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_EB2BF4D5-0FED-4BE9-5858-B261016BF25D')" class="primary-account-link">Tasks</a>

                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_EB2BF4D5-0FED-4BE9-5858-B261016BF25D')">Due Soon</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_EB2BF4D5-0FED-4BE9-5858-B261016BF25D')"><p style="font-size: 105% " id="overdueTasks"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_EB2BF4D5-0FED-4BE9-5858-B261016BF25D')">Overdue Tasks</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_EB2BF4D5-0FED-4BE9-5858-B261016BF25D')"><p style="font-size: 105% " id="dueNext10Days"></p></a></td>
                                        </tr>

                                        <tr>
                                            <td class="text-left-i-Tasks" colspan="2" style="text-align: left">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0 ">Activities</h4>
                                    <div class="d-flex flex-wrap justify-content-between align-items-center">
                                        <span class="d-flex gap-1">
                                            <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_2003100817330625007MAR 00014XX','','','')"> &nbsp;&nbsp;&nbsp;&nbsp;<i class="entypo-eye"></i> View All</a>
                                            @*<a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_23A55DFB-8818-45A2-5858-B261016CBD91')"><i class="entypo-eye"></i> View All My</a>*@

                                            <a href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_E7128441-F2B9-429B-2020-98CF0118E2B5','','','')"><i class="entypo-eye"></i> View Recent</a>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_23A55DFB-8818-45A2-5858-B261016CBD91')" class="primary-account-link">Activities</a>
                                                    @*<span class="d-flex gap-1">
                                                            <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_2003100817330625007MAR 00014XX','','','')"><i class="entypo-eye"></i> View All</a>
                                                            <a href="#" style="color: #a6a7aa; margin-right: 10px;" onclick="gotoDesktop('DSK_23A55DFB-8818-45A2-5858-B261016CBD91')"><i class="entypo-eye"></i> View All My</a>

                                                            <a href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_E7128441-F2B9-429B-2020-98CF0118E2B5','','','')"><i class="entypo-eye"></i> View Recent</a>
                                                        </span>*@
                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_8AE3B6A7-433D-40E1-5858-B261016D2C69')">Teamsell Activities</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_8AE3B6A7-433D-40E1-5858-B261016D2C69')"><p style="font-size: 105%" id="myAcTeamSell"></p></a></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_9F9AF030-26D5-4E41-5858-B261016D9E05')" class="primary-account-link">Sales Visits</a>
                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_9F9AF030-26D5-4E41-5858-B261016D9E05')">This Month</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_9F9AF030-26D5-4E41-5858-B261016D9E05')"><p style="font-size: 105%" id="mySalesThisMonth"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_FBFE103B-6BE4-4A99-5858-B2880069BD5A')">Last Month</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_FBFE103B-6BE4-4A99-5858-B2880069BD5A')"><p style="font-size: 105%" id="mySalesLastonth"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td class="text-left-i-activitiesSV" colspan="2" style="text-align: left">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr class="header-row">
                                            <th class="custom-header-buttons">
                                                <div class="d-flex flex-wrap justify-content-between align-items-center">
                                                    <a class="primary-account-link">Other's Activities</a>
                                                </div>
                                            </th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_8E9C4EA0-8429-489F-5858-B268005E8A6B')">This Month</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_8E9C4EA0-8429-489F-5858-B268005E8A6B')"><p style="font-size: 105%" id="othersAcThisMonth"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td><a href="#" onclick="gotoDesktop('DSK_BBE9A0C4-0D68-43E9-5858-B288006B5CEC')">Last Month</a></td>
                                            <td><a href="#" onclick="gotoDesktop('DSK_BBE9A0C4-0D68-43E9-5858-B288006B5CEC')"><p style="font-size: 105%" id="othersAcLastMonth"></p></a></td>
                                        </tr>
                                        <tr>
                                            <td class="text-left-i-activities" colspan="2" style="text-align: left">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>


                        <div class="col-lg-8 col-xs-12">
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0 ">Opportunities</h4>
                                    <div class="custom-header-buttons d-flex align-items-center ms-3">
                                        <span class="d-flex gap-1">
                                            <a class="align-items-center justify-content-center" href="#" style="color:#a6a7aa;" onclick="gotoDesktop('DSK_E8F3B145-4737-4D41-5858-B292002564E5','','','')">
                                                &nbsp;&nbsp;&nbsp;&nbsp;
                                                <i class="entypo-eye"></i>
                                                View All
                                            </a>
                                            @*<a class="align-items-center justify-content-center" href="#"style="color:#a6a7aa;" onclick="gotoDesktop('DSK_2004052710071079098C_S 00016XX','','','')">
                                                    <i class="entypo-eye"></i>
                                                    View All My
                                                </a>*@
                                            <a class="align-items-center justify-content-center" href="#" style="color:#a6a7aa;" onclick="gotoDesktop('DSK_2004052710071079098C_S 00016XX','','','')">
                                                <i class="entypo-eye"></i>
                                                View Recent
                                            </a>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <table class="table table-bordered responsive first-col-left">
                                <thead>
                                    <tr>
                                        <th width="20%" class="custom-header-buttons" style="color:#a6a7aa;">
                                            <a href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_51F1CA4B-5229-4BBD-5858-B2680060E038','','','')">Leads</a>

                                        </th>
                                        <th width="20%">Qty</th>
                                        <th width="30%">Expected Value</th>
                                        <th width="30%">Weighted Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_51F1CA4B-5229-4BBD-5858-B2680060E038')">Open Leads</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_51F1CA4B-5229-4BBD-5858-B2680060E038')"><p style="font-size: 105%" id="MyLeadQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_51F1CA4B-5229-4BBD-5858-B2680060E038')"><p style="font-size: 105%" id="MyLeadExpVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_51F1CA4B-5229-4BBD-5858-B2680060E038')"><p style="font-size: 105%" id="MyLeadWeightedVal"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_5E1F2C69-47D3-47FE-5858-B27D00C5AED7')">Team Sell Leads</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_5E1F2C69-47D3-47FE-5858-B27D00C5AED7')"><p style="font-size: 105%" id="teamSellLeadQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_5E1F2C69-47D3-47FE-5858-B27D00C5AED7')"><p style="font-size: 105%" id="teamSellLeadExpVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_5E1F2C69-47D3-47FE-5858-B27D00C5AED7')"><p style="font-size: 105%" id="teamSellLeadWeightedVal"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B9588B7D-2B4B-4674-5858-B27D00C603DF')">New Leads</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B9588B7D-2B4B-4674-5858-B27D00C603DF')"><p style="font-size: 105%" id="newLeadsQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B9588B7D-2B4B-4674-5858-B27D00C603DF')"><p style="font-size: 105%" id="newLeadsExpVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B9588B7D-2B4B-4674-5858-B27D00C603DF')"><p style="font-size: 105%" id="newLeadWeightedVal"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_A0DF8D41-E32E-4CE6-5858-B27D00C74B8A')">Overdue Leads</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_A0DF8D41-E32E-4CE6-5858-B27D00C74B8A')"><p style="font-size: 105%" id="overDueLeadsQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_A0DF8D41-E32E-4CE6-5858-B27D00C74B8A')"><p style="font-size: 105%" id="overDueLeadsExpVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_A0DF8D41-E32E-4CE6-5858-B27D00C74B8A')"><p style="font-size: 105%" id="overDueLeadsWeightedVal"></p></a></td>
                                    </tr>
                                    <tr style="background-color: #ecf1f7; height: 30px; ">
                                        <td class="custom-header-buttons">
                                            <a href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_DC6D1B4B-E7FD-4EA9-5858-B2610168D263','','','')">Open Opportunities</a>

                                            @*<span class="d-flex gap-1">
                                                    <a class="align-items-center justify-content-center" href="#" style="color: #a6a7aa;" onclick="gotoDesktop('DSK_2003012009491863136MAI 30119XX','','','')">
                                                        <i class="entypo-eye"></i>
                                                        &nbsp; View All
                                                    </a>
                                                    <a class="align-items-center justify-content-center" style="color:#a6a7aa;" href="#" onclick="gotoDesktop('DSK_8A77F884-81D3-4041-5858-AED10164277D','','','')">
                                                        <i class="entypo-eye"></i>
                                                        View All My
                                                    </a>
                                                    <a class="align-items-center justify-content-center" style="color:#a6a7aa;" href="#" onclick="gotoDesktop('DSK_8A77F884-81D3-4041-5858-AED10164277D','','','')">
                                                        <i class="entypo-eye"></i>
                                                        View Recent
                                                    </a>
                                                </span>*@
                                        </td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_DC6D1B4B-E7FD-4EA9-5858-B2610168D263')"><span id="openOppQty"></span> </a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_DC6D1B4B-E7FD-4EA9-5858-B2610168D263')"><span id="openOppExpVal"></span></a></td>
                                        <td>
                                            <a href="#" onclick="gotoDesktop('DSK_DC6D1B4B-E7FD-4EA9-5858-B2610168D263')"><span id="openOppWeightedVal"></span></a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_83F997EA-0F08-461C-5858-B27D0180B63D')">Team Sell</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_83F997EA-0F08-461C-5858-B27D0180B63D')"><p style="font-size: 105%" id="openOppTeamSellQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_83F997EA-0F08-461C-5858-B27D0180B63D')"><p style="font-size: 105%" id="openOppTeamSellExpVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_83F997EA-0F08-461C-5858-B27D0180B63D')"><p style="font-size: 105%" id="openOppTeamSellWeightedVal"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_71BEC2E8-3E55-4E45-5858-B27D018086A6')">Due Next 30 days</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_71BEC2E8-3E55-4E45-5858-B27D018086A6')"><p style="font-size: 105%" id="openOppDueNext30DaysQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_71BEC2E8-3E55-4E45-5858-B27D018086A6')"><p style="font-size: 105%" id="openOppDueNext30DaysExpVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_71BEC2E8-3E55-4E45-5858-B27D018086A6')"><p style="font-size: 105%" id="openOppDueNext30DaysWeightedVal"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B40EF2B0-2F73-40DD-5858-B27D01802B74')">Overdue Opportunities</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B40EF2B0-2F73-40DD-5858-B27D01802B74')"><p style="font-size: 105%" id="openOppOverDueQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B40EF2B0-2F73-40DD-5858-B27D01802B74')"><p style="font-size: 105%" id="openOppOverDueExpVal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B40EF2B0-2F73-40DD-5858-B27D01802B74')"><p style="font-size: 105%" id="openOppOverDueWeightedVal"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td class="text-left-i-opps" colspan="4">
                                            @*<td class="" colspan="2" style="text-align: left">*@
                                        </td>
                                    </tr>
                                    <tr style="background-color: #ecf1f7; height: 30px; ">
                                        <td style="color: #a6a7aa; text-align: left;">Closed Lost 90 Days</td>
                                        <td style=" color: #a6a7aa;">Qty</td>
                                        <td style=" color: #a6a7aa;">Total</td>
                                        <td style=" color: #a6a7aa;">Rate (%)</td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_64FD85FC-87F1-434A-5858-B261016E0F9F')">Won</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_64FD85FC-87F1-434A-5858-B261016E0F9F')"><p style="font-size: 105%" id="wonQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_64FD85FC-87F1-434A-5858-B261016E0F9F')"><p style="font-size: 105%" id="wonTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_64FD85FC-87F1-434A-5858-B261016E0F9F')"><p style="font-size: 105%" id="wonRate"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_64FD85FC-87F1-434A-5858-B261016E0F9F')">Lost</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_64FD85FC-87F1-434A-5858-B261016E0F9F')"><p style="font-size: 105%" id="lostQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_64FD85FC-87F1-434A-5858-B261016E0F9F')"><p style="font-size: 105%" id="lostTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_64FD85FC-87F1-434A-5858-B261016E0F9F')"><p style="font-size: 105%" id="lostRate"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_64FD85FC-87F1-434A-5858-B261016E0F9F')">Cancelled</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_64FD85FC-87F1-434A-5858-B261016E0F9F')"><p style="font-size: 105%" id="cancelledQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_64FD85FC-87F1-434A-5858-B261016E0F9F')"><p style="font-size: 105%" id="cancelledTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_64FD85FC-87F1-434A-5858-B261016E0F9F')"><p style="font-size: 105%" id="cancelledRate"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td colspan="4" style="height: 20px;"></td>
                                    </tr>
                                    <tr>
                                </tbody>
                            </table>

                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                    <h4 class="m-0 ">Quotes</h4>
                                    <div class="custom-header-buttons d-flex align-items-center ms-3">
                                        <span class="d-flex gap-1">
                                            <a class="align-items-center justify-content-center" style="color:#a6a7aa;" href="#" onclick="gotoDesktop('DSK_2004052610211387019C_S 00016XX','','','')">
                                                &nbsp;&nbsp;&nbsp;&nbsp;
                                                <i class="entypo-eye"></i>
                                                View All
                                            </a>
                                            @*<a class="align-items-center justify-content-center" style="color:#a6a7aa;" href="#" onclick="gotoDesktop('DSK_5D8505F5-A8E9-4EA7-5858-AECF0153D82A','','','')">
                                                    <i class="entypo-eye"></i>
                                                    View All My
                                                </a>*@
                                            <a class="align-items-center justify-content-center" style="color:#a6a7aa;" href="#" onclick="gotoDesktop('DSK_5D8505F5-A8E9-4EA7-5858-AECF0153D82A','','','')">
                                                <i class="entypo-eye"></i>
                                                View Recent
                                            </a>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <table class="table table-bordered responsive first-col-left">
                                <tbody>
                                    <tr style="background-color: #ecf1f7; height: 30px;">
                                        <th width="20%" class="custom-header-buttons">
                                        </th>
                                        <td style="width: 20%; color: #a6a7aa;">Qty</td>
                                        <td style="width: 30%; color: #a6a7aa;">Total</td>
                                        <td style="width: 30%; color: #a6a7aa;">Margin (%)</td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_F9E7EBFB-EBB3-429E-5858-B2610168A0B7')">Open RFQ's</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_F9E7EBFB-EBB3-429E-5858-B2610168A0B7')"><p style="font-size: 105%" id="openRFQsQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_F9E7EBFB-EBB3-429E-5858-B2610168A0B7')"><p style="font-size: 105%" id="openRFQstotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_F9E7EBFB-EBB3-429E-5858-B2610168A0B7')"><p style="font-size: 105%" id="openRFQsmargin"></p></a></td>
                                    </tr>
                                    @*<tr style="background-color: #ecf1f7; height: 30px; ">
                                            <td style="color: #a6a7aa; text-align: left;"><a href="#" onclick="gotoDesktop('DSK_1235E934-25E7-4B3B-5858-B261016807CD')">All Open Quotes</a></td>
                                            <td style="color: #a6a7aa;">Qty</td>
                                            <td style="color: #a6a7aa;">Total</td>
                                            <td style="color: #a6a7aa;">Rate (%)</td>
                                        </tr>*@
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_89CA5F4A-3E4B-4D8A-5858-B27E015451CA')">All Open Quotes</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_89CA5F4A-3E4B-4D8A-5858-B27E015451CA')"><p style="font-size: 105%" id="AllOpenQuotesQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_89CA5F4A-3E4B-4D8A-5858-B27E015451CA')"><p style="font-size: 105%" id="AllOpenQuotesTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_89CA5F4A-3E4B-4D8A-5858-B27E015451CA')"><p style="font-size: 105%" id="AllOpenQuotesMargin"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_1235E934-25E7-4B3B-5858-B261016807CD')">Open Strategic Quotes</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_1235E934-25E7-4B3B-5858-B261016807CD')"><p style="font-size: 105%" id="openStrategicQuotesQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_1235E934-25E7-4B3B-5858-B261016807CD')"><p style="font-size: 105%" id="openStrategicQuotesTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_1235E934-25E7-4B3B-5858-B261016807CD')"><p style="font-size: 105%" id="openStrategicQuotesMargin"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9EBE71D2-BF33-4CF3-5858-B27D01348127')">New Strategic Quotes</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9EBE71D2-BF33-4CF3-5858-B27D01348127')"><p style="font-size: 105%" id="newStrategicQuotesQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9EBE71D2-BF33-4CF3-5858-B27D01348127')"><p style="font-size: 105%" id="newStrategicQuotesTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_9EBE71D2-BF33-4CF3-5858-B27D01348127')"><p style="font-size: 105%" id="newStrategicQuotesMargin"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B782CACD-C145-40B6-5858-B27D017E86F0')">Overdue Strategic Quotes</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B782CACD-C145-40B6-5858-B27D017E86F0')"><p style="font-size: 105%" id="overdueStrategicQuotesQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B782CACD-C145-40B6-5858-B27D017E86F0')"><p style="font-size: 105%" id="overdueStrategicQuotesTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_B782CACD-C145-40B6-5858-B27D017E86F0')"><p style="font-size: 105%" id="overdueStrategicQuotesMargin"></p></a></td>

                                    </tr>
                                    <tr>
                                        <td class="text-left-i-quotes" colspan="4">
                                        </td>
                                    </tr>

                                    <tr style="background-color: #ecf1f7; height: 30px; ">
                                        <td style="color: #a6a7aa; text-align: left;">Closed Last 90 Days</td>
                                        <td style="color: #a6a7aa;">Qty</td>
                                        <td style="color: #a6a7aa;">Total</td>
                                        <td style="color: #a6a7aa;">Rate(%)</td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E83A1420-F8E7-4A75-5858-B26101683E9A')">Strategic Quotes - Won</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E83A1420-F8E7-4A75-5858-B26101683E9A')"><p style="font-size: 105%" id="strategicQuotesWonQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E83A1420-F8E7-4A75-5858-B26101683E9A')"><p style="font-size: 105%" id="strategicQuotesWonTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E83A1420-F8E7-4A75-5858-B26101683E9A')"><p style="font-size: 105%" id="strategicQuotesWonRate"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E83A1420-F8E7-4A75-5858-B26101683E9A')">Strategic Quotes - Lost</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E83A1420-F8E7-4A75-5858-B26101683E9A')"><p style="font-size: 105%" id="strategicQuotesLostQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E83A1420-F8E7-4A75-5858-B26101683E9A')"><p style="font-size: 105%" id="strategicQuotesLostTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E83A1420-F8E7-4A75-5858-B26101683E9A')"><p style="font-size: 105%" id="strategicQuotesLostRate"></p></a></td>
                                    </tr>
                                    <tr>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E83A1420-F8E7-4A75-5858-B26101683E9A')">Strategic Quotes - Cancelled</a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E83A1420-F8E7-4A75-5858-B26101683E9A')"><p style="font-size: 105%" id="strategicQuotesCancelledQty"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E83A1420-F8E7-4A75-5858-B26101683E9A')"><p style="font-size: 105%" id="strategicQuotesCancelledTotal"></p></a></td>
                                        <td><a href="#" onclick="gotoDesktop('DSK_E83A1420-F8E7-4A75-5858-B26101683E9A')"><p style="font-size: 105%" id="strategicQuotesCancelledRate"></p></a></td>
                                    </tr>
                                    <tr>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="row mt-20">
                        <div class="mb-20">
                            <div class="Contact-input-trend"></div>
                            <div class="SalesVisit-Input-Trends"></div>
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                </div>
                            </div>
                        </div>
                        <div class="mb-20">
                            <div class="d-flex justify-content-between align-items-center mb-10">
                                <div class="d-flex justify-content-start align-items-center">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-20">
                        <div class="Opportunity-Input-Trends"></div>
                        <div class="Strategic-Quote-Input-Trend"></div>
                        <div class="col-lg-6 col-xs-12">
                            <div class="mb-20">
                                <div class="d-flex justify-content-between align-items-center mb-10">
                                    <div class="d-flex justify-content-start align-items-center">

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-xs-12">
                            <div class="mb-20">
                                <div class="d-flex justify-content-between align-items-center mb-10">
                                    <div class="d-flex justify-content-start align-items-center">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Footer -->
                <footer class="main mt-0 pt-20 pb-20 pl-20">
                    &copy; 2020 <strong>Selltis</strong>
                </footer>
            </div>
        </div>
    </section>
</section>

<script type="text/javascript">
    $(document).ready(function () {

        showProgress();
        GetSegment_User_Dashboard_Data();

    });

    function onChange(e) {
    var selectedNode = this.treeview.dataItem(this.treeview.select());
    if (selectedNode) {
        var selectedNodeId = selectedNode.id;
        var selectedNodeName = selectedNode.Name;

        updateBreadcrumb(selectedNode);

        $.ajax({
            url: '@Url.Action("SetSegmentManagerUserID", "DetailsPage")',
            method: 'POST',
            data: { SelectedUser: selectedNodeId,SelectedNodeName : selectedNodeName},
            success: function (response) {
                if (response.success) {
                    showProgress();
                    GetSegment_User_Dashboard_Data();
                }
                else {
                    window.location.href = '@Url.Action("Segmentsalesdashboard", "DetailsPage")';
                }
            }
        });
    }
}
    function updateBreadcrumb(selectedNode) {

        var breadcrumb = $("#breadcrumb");
        breadcrumb.empty();

        if (!selectedNode) return;
        var nodePath = [];

        while (selectedNode) {
            nodePath.unshift(selectedNode);
            selectedNode = selectedNode.parentNode();
        }

        if (nodePath.length === 0) return;
        nodePath.forEach((node, index) => {
            if (index > 0) breadcrumb.append("<span>&nbsp;&nbsp;&gt;&nbsp;&nbsp;</span>");
            breadcrumb.append(
                $("<a>")
                    .text(node.Name || "Undefined")
                    .attr("href", `/details/${node.id}`)
                    .data("id", node.id)
                    .on("click", function (e) {
                        e.preventDefault();
                        ChangeNodeInDropdownTree(node.id,node.Name);
                        console.log(`Breadcrumb clicked: ${node.Name}`);
                    })
                    .css("color", "white")
            );
        });
    }
    function ChangeNodeInDropdownTree(selectedNodeId,SelectedNodeName) {
    const breadcrumb = $("#breadcrumb");
    let shouldRemove = false;

    breadcrumb.children().each(function () {
        const $child = $(this);

        if ($child.data("id") === selectedNodeId) {
            shouldRemove = true;
        } else if (shouldRemove) {
            $child.remove();
        }
    });

    const dropdown = $("#SalesPersonHierarchy").data("kendoDropDownTree");
    if (dropdown) {
        const selectedNode = dropdown.dataSource.get(selectedNodeId);
        if (selectedNode) {
            dropdown.value(selectedNodeId);
        }
    }

    $.ajax({
        url: '@Url.Action("SetSegmentManagerUserID", "DetailsPage")',
        method: 'POST',
        data: { SelectedUser: selectedNodeId, SelectedNodeName: SelectedNodeName },
        success: function (response) {
            //alert(response.success)
            if (response.success) {
                showProgress();
                GetSegment_User_Dashboard_Data();
            } else {
                window.location.href = '@Url.Action("Segmentsalesdashboard", "DetailsPage")';
            }
        },
        error: function (error) {
            console.error("Redirection failed:", error);
        }
    });
}
    function selectNodeInDropdownTree(nodeId) {
        var treeView = dropdownTree.treeview;
        var nodeToSelect = findNodeById(treeView.dataSource.view(), nodeId);

        if (nodeToSelect) {
            treeView.select(treeView.findByUid(nodeToSelect.uid));
            dropdownTree.value(nodeToSelect.id);
            updateBreadcrumb(nodeToSelect);
        }
    }
    function findNodeById(nodes, nodeId) {
        for (var i = 0; i < nodes.length; i++) {
            if (nodes[i].id == nodeId) {
                return nodes[i];
            } else if (nodes[i].hasChildren) {
                var childResult = findNodeById(nodes[i].children.view(), nodeId);
                if (childResult) return childResult;
            }
        }
        return null;
    }
    function GetSegment_User_Dashboard_Data() {

            $.ajax({
url: '@Url.Action("GetSalesManagerDashboard", "DetailsPage")',
                type: 'GET',
dataType: 'json',
success: function (data) {
    console.log(data[0]);

    if (data && data.length > 0) {
        var item = data[0];  // Access the first item
        // Update fields with data from the first item
        $('#accountQty').text(item.NewAccountsQty || '0');
        $('#teamSellAccountsQty').text(item.TeamSellAccountsQty || '0');
        $('#overservedAccountsQty').text(item.OverservedAccountsQty || '0');
        $('#underservedAccountsQty').text(item.UnderservedAccountsQty || '0');
        $('#overDueprimaryAccountsQtytile').text(item.OverDueprimaryAccountsQty || '0');
        $('#overDueprimaryAccountsQty').text(item.OverDueprimaryAccountsQty || '0');
        $('#newContactsQty').text(item.NewContactsQty || '0');
        $('#keyContactsQty').text(item.KeyContactsQty || '0');
        $('#overdueContactsQty').text(item.OverdueContactsQty || '0');

        $('#teamSellLeadQty').text(item.MyLeadTeamSellLeadQty || '0');
        $('#teamSellLeadExpVal').text((item.MyTeamSellLeadsExptedVal || '0'));
        $('#teamSellLeadWeightedVal').text('');
        $('#newLeadsQty').text(item.MyLeadNewLeadsQty || '0');
        $('#newLeadsExpVal').text((item.MyNewLeadsExptedVal || '0'));
        $('#newLeadWeightedVal').text('');
        $('#overDueLeadsQty').text(item.MyLeadOverDueLeadsQty || '0');
        $('#overDueLeadsExpVal').text((item.MyOverdueLeadsExptedVal || '0'));
        $('#overDueLeadsWeightedVal').text('');

        $('#MyLeadQty').text(item.Leads || '0');
        $('#MyLeadsTile').text(item.MyLeadOverDueLeadsQty || '0');
        $('#MyLeadExpVal').text((item.LeadsExpVal || '0'));
        $('#MyLeadWeightedVal').text('');

        $('#MyOpenOppsqtytile').text(item.OpenOppOverDueQty || '0');
        $('#openOppQty').text(item.MyOpenOppsqty || '0');
        $('#openOppExpVal').text((item.MyOpenOppsExptedVal || '0'));
        $('#openOppWeightedVal').text((item.MyOpenOppsWeightedVal || '0'));

        $('#openOppTeamSellQty').text(item.OpenOppTeamSellQty || '0');
        $('#openOppDueNext30DaysQty').text(item.OpenOppDueNext30DaysQty || '0');
        $('#openOppOverDueQty').text(item.OpenOppOverDueQty || '0');

        $('#openOppTeamSellExpVal').text((item.OpenOppTeamSellExpVal || '0'));
        $('#openOppDueNext30DaysExpVal').text((item.OpenOppDueNext30DaysExpVal || '0'));
        $('#openOppOverDueExpVal').text((item.OpenOppOverDueExpVal || '0'));

        $('#openOppTeamSellWeightedVal').text((item.OpenOppTeamSellWeightedVal || '0'));
        $('#openOppDueNext30DaysWeightedVal').text((item.OpenOppDueNext30DaysWeightedVal || '0'));
        $('#openOppOverDueWeightedVal').text((item.OpenOppOverDueWeightedVal || '0'));

        $('#wonQty').text(item.WonQty || '0');
        $('#lostQty').text(item.LostQty || '0');
        $('#cancelledQty').text(item.CancelledQty || '0');
        $('#wonTotal').text((item.WonTotal || '0'));
        $('#lostTotal').text((item.LostTotal || '0'));
        $('#cancelledTotal').text((item.CancelledTotal || '0'));

        $('#wonRate').text((item.WonRate || 0));
        $('#lostRate').text((item.LostRate || 0));
        $('#cancelledRate').text((item.CancelledRate || 0));


        $('#dueNext10Days').text(item.DueNext10Days || '0');
        $('#overdueTasks').text(item.OverdueTasks || '0');
        $('#openOrdersTotal').text((item.OpenOrdersTotal || 0));
        $('#openOrdersMargin').text((item.OpenOrdersMargin || 0));
        $('#ordersThisMonthQty').text(item.OrdersThisMonthQty || '0');
        $('#ordersThisMonthTotal').text((item.OrdersThisMonthTotal || 0));
        $('#ordersThisMonthMargin').text((item.OrdersThisMonthMargin || 0));
        $('#ordersLastMonthQty').text(item.OrdersLastMonthQty || '0');
        $('#ordersLastMonthTotal').text((item.OrdersLastMonthTotal || 0));
        $('#ordersLastMonthMargin').text((item.OrdersLastMonthMargin || 0));
        $('#openOrdersQty').text(item.OpenOrdersQty || '0');
        $('#myActivities').text(item.MyActivities || '0');
        $('#myAcTeamSell').text(item.MyAcTeamSell || '0');
        $('#mySalesThisMonth').text(item.MySalesThisMonth || '0');
        $('#mySalesLastonth').text(item.MySalesLastonth || '0');
        $('#othersAcThisMonth').text(item.OthersAcThisMonth || '0');
        $('#othersAcLastMonth').text(item.OthersAcLastMonth || '0');

        $('#openStrategicQuotesQty').text(item.OpenStrategicQuotesQty || '0');
        $('#AllOpenQuotesQty').text(item.AllOpenQuotesQty || '0');
        $('#openStrategicQuotesQtytile').text(item.OverdueStrategicQuotesQty || '0');
        $('#newStrategicQuotesQty').text(item.NewStrategicQuotesQty || '0');
        $('#overdueStrategicQuotesQty').text(item.OverdueStrategicQuotesQty || '0');
        $('#openStrategicQuotesTotal').text((item.OpenStrategicQuotesTotal || 0));
        $('#AllOpenQuotesTotal').text((item.AllOpenQuotesTotal || 0));
        $('#newStrategicQuotesTotal').text((item.NewStrategicQuotesTotal || 0));
        $('#overdueStrategicQuotesTotal').text((item.OverdueStrategicQuotesTotal || 0));
        $('#openStrategicQuotesMargin').text((item.OpenStrategicQuotesMargin || 0));
        $('#AllOpenQuotesMargin').text((item.AllOpenQuotesMargin || 0));
        $('#newStrategicQuotesMargin').text((item.NewStrategicQuotesMargin || 0));
        $('#overdueStrategicQuotesMargin').text((item.OverdueStrategicQuotesMargin || 0));

        $('#openRFQsQty').text(item.OpenRFQsQty || '0');
        $('#openRFQstotal').text((item.OpenRFQstotal||'0'));
        $('#openRFQsmargin').text((item.OpenRFQsmargin || 0));

        $('#strategicQuotesWonQty').text(item.StrategicQuotesWonQty || '0');
        $('#strategicQuotesLostQty').text(item.StrategicQuotesLostQty || '0');
        $('#strategicQuotesCancelledQty').text(item.StrategicQuotesCancelledQty || '0');
        $('#strategicQuotesWonTotal').text((item.StrategicQuotesWonTotal || 0));
        $('#strategicQuotesLostTotal').text((item.StrategicQuotesLostTotal || 0));
        $('#strategicQuotesCancelledTotal').text((item.StrategicQuotesCancelledTotal || 0));
        $('#strategicQuotesWonRate').text((item.StrategicQuotesWonRate || 0));
        $('#strategicQuotesLostRate').text((item.StrategicQuotesLostRate || 0));
        $('#strategicQuotesCancelledRate').text((item.StrategicQuotesCancelledRate || 0));

        $('#salesThisMonthQty').text(item.SalesThisMonthQty || '0');
        $('#salesLastMonthQty').text(item.SalesLastMonthQty || '0');
        $('#salesThisMonthTotal').text((item.SalesThisMonthTotal || 0));
        $('#salesThisMonthMargin').text((item.SalesThisMonthMargin || 0));
        $('#salesLastMonthTotal').text((item.SalesLastMonthTotal || 0));
        $('#salesLastMonthMargin').text((item.SalesLastMonthMargin || 0));
        $('#lastRefreshedDate').text((item.LastRefreshedDate || 0));
        $('#accountsWithoutContacts').text((item.AccountsWithoutContacts || 0));
        $('#accountsWithoutActivities').text((item.AccountsWithoutActivities || 0));

        $('#myQuotePipeline').text((item.MyQuotePipeline === "$0.00" ? "0" : item.MyQuotePipeline || 0));

        $('#MyTaskstile').text((item.DueNext10Days || 0));

        $('.Contact-input-trend').html(item.ContactInputTrend);
        $('.SalesVisit-Input-Trends').html(item.SalesVisitInputTrends);
        $('.Opportunity-Input-Trends').html(item.OpportunityInputTrends);
        $('.Strategic-Quote-Input-Trend').html(item.StrategicQuoteInputTrend);
        $('.Bookings-Input-Trend').hide().html(item.BookingsInputTrend);
        $('.Sales-Input-Trend').hide().html(item.SalesInputTrend);

        // Clear and populate overdue account list
        $('.text-left-i-Accounts').empty();
        var accountDiv = '';
        $.each(item.OverdueAccountList.slice(0, 3) || [], function (index, account) {
             accountDiv += `
         <div class="name-title-info-item" style= "padding-top: 6px !important;padding-bottom: 6px !important">
        <a href="#" onclick="GoDetailsPage('CO', '${account.GID_ID}')">
            <div class="Main-name">${account.TXT_CompanyName}</div>
            <div class="Sub-name">${account.Txt_AddrMailing}| ${account.Txt_CityMailing}| ${account.Txt_StateMailing}| ${account.txt_ZipMailing}</div>
        </a>
        </div>`;

        });
        $('.text-left-i-Accounts').html(accountDiv);
        // Clear and populate overdue contacts list
        $('.text-left-i-contacts').empty();
        var contactDiv = '';
        $.each(item.OverdueContactsList || [], function (index, contact) {
            // var contactDiv = `<div><a href="#" onclick="GoDetailsPage('CN', '${contact.GID_ID}')">${contact.SYS_NAME}</a></div>`;
             contactDiv += `
                     <div class="name-title-info-item" style= "padding-top: 6px !important;padding-bottom: 6px !important">
                     <a href="#" onclick="GoDetailsPage('CN', '${contact.GID_ID}')">
                     <div class="Main-name">${contact.Txt_NameFirst}|${contact.Txt_NameLast}|${contact.TXT_COMPANYNAMETEXT}</div>
                     <div class="Sub-name">${contact.Txt_AddrMailing}|${contact.Txt_MailingCity}|${contact.Txt_MailingState}|${contact.txt_MailingZip}</div>
                     </a>
                     </div>`;

        });
        $('.text-left-i-contacts').html(contactDiv);

        // Clear and populate overdue tasks list
        $('.text-left-i-Tasks').empty();
        var taskDiv = '';
        $.each(item.OverdueTasksList || [], function (index, task) {
             taskDiv += `
           <div class="name-title-info-item" style= "padding-top: 6px !important;padding-bottom: 6px !important">
            <a href="#" onclick="openRecord('TD', '${task.GID_ID}')">
           <div class="Main-name">${task.TXT_DESCRIPTION}</div>
           <div class="Sub-name">${task.TXT_FULLNAME}|${task.DTT_STARTDATE}|${task.DTT_DUETIME}|${task.MLS_TYPE}</div>
            </a>
            </div>`;

        });
        $('.text-left-i-Tasks').html(taskDiv);

        // Overdue Activities (Sales Visit)
        $('.text-left-i-activitiesSV').empty();
        var acsalesvisitDiv = '';
        $.each(item.OverdueActivitiesSalesVisitList || [], function (index, acsalesvisit) {
            //var acsalesvisitDiv = `<div><a href="#" onclick="openRecord('AC', '${acsalesvisit.GID_ID}')">${acsalesvisit.SYS_NAME}</a></div>`;
             acsalesvisitDiv += `
          <div class="name-title-info-item" style= "padding-top: 6px !important;padding-bottom: 6px !important">
           <a href="#" onclick="openRecord('AC', '${acsalesvisit.GID_ID}')">
            <div class="Main-name">${acsalesvisit.MLS_TYPE}|${acsalesvisit.DTT_STARTTIME}|${acsalesvisit.TXT_FULLNAME}</div>
             <div class="Sub-name">${acsalesvisit.MMO_History}</div>
          </a>
          </div>`;

        });
        $('.text-left-i-activitiesSV').html(acsalesvisitDiv);

        //// Overdue Activities
        $('.text-left-i-activities').empty();
        var activitiesDiv = '';
        $.each(item.OverdueActivitiesList || [], function (index, activities) {
            //var activitiesDiv = `<div><a href="#" onclick="openRecord('AC', '${activities.GID_ID}')">${activities.SYS_NAME}</a></div>`;
             activitiesDiv += `
          <div class="name-title-info-item" style= "padding-top: 6px !important;padding-bottom: 6px !important">
           <a href="#" onclick="openRecord('AC', '${activities.GID_ID}')">
            <div class="Main-name">${activities.MLS_TYPE}|${activities.DTT_STARTTIME}|${activities.TXT_FULLNAME}|${activities.TXT_COMPANYNAME}</div>
             <div class="Sub-name">${activities.MMO_History}</div>
           </a>
            </div>`;

        });
        $('.text-left-i-activities').html(activitiesDiv);

        // Overdue Opportunities
        $('.text-left-i-opps').empty();
        var oppsDiv = "";
        $.each(item.OverdueOppsList || [], function (index, opps) {
            //var oppsDiv = `<div><a href="#" onclick="GoDetailsPage('OP', '${opps.GID_ID}')">${opps.SYS_NAME}</a></div>`;
            oppsDiv += `
    <div class="name-title-info-item" style="padding-top: 6px !important; padding-bottom: 6px !important">
        <a href="#" onclick="GoDetailsPage('OP', '${opps.GID_ID}')">
            <div class="Main-name">${opps.TXT_FULLNAME} | ${opps.TXT_COMPANYNAME} | ${opps.TXT_OPPORTUNITYNAME}</div>
            <div class="Sub-name">
                ${opps.MLS_OPPORTUNITYTYPE} | ${opps.MLS_SALESPROCESSSTAGE} | ${opps.DTT_CREATIONTIME} | ${opps.DTT_EXPCLOSEDATE} |
                ${(parseFloat(opps.CUR_OPPLINEVALUE) || 0).toFixed(2)}
            </div>
        </a>
    </div>`;


        });
        $('.text-left-i-opps').html(oppsDiv);

        // Overdue Quotes
        $('.text-left-i-quotes').empty();
        var quoteDiv = '';
        $.each(item.OverdueQuotesList || [], function (index, quote) {
            //var quoteDiv = `<div><a href="#" onclick="openRecord('QT', '${quote.GID_ID}')">${quote.SYS_NAME}</a></div>`;
             quoteDiv += `
              <div class="name-title-info-item" style= "padding-top: 6px !important;padding-bottom: 6px !important">
           <a href="#" onclick="openRecord('QT', '${quote.GID_ID}')">
            <div class="Main-name">${quote.TXT_QUOTENO}|${quote.TXT_DESCRIPTION}|${quote.TXT_FULLNAME}|${quote.TXT_COMPANYNAME}</div>
           <div class="Sub-name">${quote.MLS_STATUS}| ${(parseFloat(quote.CUR_TOTALAMOUNT) ||0).toFixed(2)}| ${quote.DTT_EXPCLOSEDATE}| ${quote.DTT_VALIDUNTILDATE}</div>
         </a>
       </div>`;

        });
        $('.text-left-i-quotes').html(quoteDiv);
        $(window).on('load', function () {
            hideProgress();
        });

        $(window).on('pageshow', function (event) {
            if (event.originalEvent.persisted) {
                hideProgress();
            }
        });
    }
    else {
    }
    const dropdown = $("#SalesPersonHierarchy").data("kendoDropDownTree");


    if (dropdown) {
        dropdown.bind("dataBound", function () {
            const selectedUserID = item.SelectedUserID;

            const selectedNode = dropdown.dataSource.get(selectedUserID);

            if (selectedNode) {
                dropdown.value(selectedUserID);
                expandToNode(selectedNode, dropdown);
                updateBreadcrumb(selectedNode);

            }
        });
    }
    //const dropdown = $("#SalesPersonHierarchy").data("kendoDropDownTree");

    //if (dropdown) {
    //    dropdown.bind("dataBound", function () {
    //        const selectedUserID = item.SelectedUserID;
    //        //console.log(selectedUserID)
    //        const selectedNode = dropdown.dataSource.get(selectedUserID);

    //        if (selectedNode) {
    //            dropdown.value(selectedUserID);
    //            expandToNode(selectedNode, dropdown);
    //            updateBreadcrumb(selectedNode);
    //            //dropdown.open();

    //        }
    //    });
    //    dropdown.dataSource.fetch();
    //}
    hideProgress();
        },

error: function (xhr, status, error) {
    console.log("Error: ", error);
    $('#accountQty').text('Error');
    $('#teamSellAccountsQty').text('Error');
    $('#overservedAccountsQty').text('Error');
    $('#newContactsQty').text('Error');
}
    });
    }
    //function expandToNode(node, dropdown) {
    //    const treeview = dropdown.treeview;
    //    const path = [];
    //    let currentNode = node;
    //    while (currentNode) {
    //        path.unshift(currentNode);
    //        currentNode = dropdown.dataSource.parent(currentNode);
    //    }
    //    expandPathSequentially(path, treeview);
    //}
    //function expandPathSequentially(path, treeview) {
    //    if (!path.length) return;

    //    const currentNode = path.shift();
    //    const treeNode = treeview.findByUid(currentNode.uid);
    //    if (treeNode.length) {
    //        treeview.expand(treeNode);
    //        setTimeout(() => expandPathSequentially(path, treeview), 300);
    //    }
    //}
    function expandToNode(node, dropdown) {
        const treeview = dropdown.treeview;
        const path = [];
        let currentNode = node;

        while (currentNode) {
            path.unshift(currentNode.id);
            currentNode = dropdown.dataSource.parent(currentNode);
        }

        path.forEach((nodeId) => {
            const treeNode = treeview.findByUid(dropdown.dataSource.get(nodeId).uid);
            if (treeNode.length) {
                treeview.expand(treeNode);
            }
        });
        const selectedNode = treeview.findByUid(dropdown.dataSource.get(node.id).uid);
        if (selectedNode.length) {
            treeview.expand(selectedNode);
        }
    }
    function GoDetailsPage(file, gid) {
        if (file == 'CO') {
            // window.location = "/DetailsPage/CompanyDetails/?sCompanyId=" + gid;

            window.location = "/DetailsPage/PRF?sPRPId=PRF_CO&sRecId=" + gid
        }
        else if (file == 'CN') {
            //window.location = "/DetailsPage/ContactDetails/?sContactId=" + gid;
            window.location = "/DetailsPage/PRF?sPRPId=PRF_CN&sRecId=" + gid
        }
        else if (file == 'OP') {
            //window.location = "/DetailsPage/OPDetails/?sOPId=" + gid;
            window.location = "/DetailsPage/PRF?sPRPId=PRF_OP&sRecId=" + gid
        }
    }
    function openRecord(tablename, gid_id) {

        window.location = "/CreateForm/CreateForm/" + tablename + "/" + gid_id + "/TYPE/false/false/KEY/false/MASTERID/FORMKEY/FIELD/"

    }
    function RefreshData() {
        showProgress();
        $.ajax({
            url: '/DetailsPage/RefreshOutsideSalesDashboardData',
            type: 'POST',
            dataType: 'json',
            success: function (response) {
                if (response.success) {

                    window.location = "/DetailsPage/GetSalesManagerDashboard"
                    //gotoDesktop('DSK_3E971649-F608-49AD-5858-B20C00C8201E', '', '', '')
                } else {

                }
            },

        });

    }
    function PrepareFormLink(tableName, type) {
    ////debugger;

    if (navigator.onLine == false) {
        hideProgress();
        alert('Check your Internet Connection');
        return false;
    }

    showProgress();

    window.location = "/CreateForm/CreateForm/" + tableName + "/ID/" + type + "/false/false/KEY/false/MASTERID/FORMKEY/FIELD/";

    }
    @*function SetSelectedRecID(file,DSKID) {
        $.ajax({
            url: '@Url.Action("SetSelectedRecID", "DetailsPage")',
            method: 'POST',
            data: { sFile: file },
            success: function (response) {
                if (response.success) {
                    gotoDesktop(DSKID, '', '', '')
                }
            }
        });
    }*@

</script>
<script>
            var spinnerVisible = false;
        function showProgress() {
            if (!spinnerVisible) {
                $("div#spinnertool").fadeIn("fast");
                spinnerVisible = true;
            }
        };
        function hideProgress() {
            if (spinnerVisible) {
                var spinner = $("div#spinnertool");
                spinner.stop();
                spinner.fadeOut("fast");
                spinnerVisible = false;
            }
        };
</script>

