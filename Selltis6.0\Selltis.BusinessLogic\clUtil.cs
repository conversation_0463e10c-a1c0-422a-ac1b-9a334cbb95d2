﻿using System;

//OWNER: WT

using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using System.Reflection;
using System.Web;

namespace Selltis.BusinessLogic
{
	public class clUtil
	{

		private clProject goP;
		private clError goErr;
		private clData goData;
		private clTransform goTr;
		private clMetaData goMeta;

		private void Initialize()
		{
			string sProc = "clForm:Initialize";
			//Try
			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
				goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
				goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
				goTr = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTR"];
				goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public object CreateCustomClass(string baseClass, string derivedClass, object[] args)
		{

			object oReturn = null;
			bool bDerivedClass = false;

			Type t = typeof(clSelltisMobileBase);
			string s = t.Assembly.FullName.ToUpper();
			try
			{
				//first preference to custom class .i.e derivedClass
				oReturn = Assembly.Load(s).CreateInstance(derivedClass, true, BindingFlags.Default, null, args, null, null);
				bDerivedClass = true;
			}
			catch
			{
				//custom failed, create base now
				bDerivedClass = false;
			}

			if (oReturn == null)
			{
				//custom failed, create base now
				bDerivedClass = false;
			}

			if (bDerivedClass == false)
			{
				oReturn = Assembly.Load(s).CreateInstance(baseClass, true, BindingFlags.Default, null, args, null, null);
			}

			return oReturn;
		}


		public string ConvertToSellDate(DateTime dtDate, string sDorT)
		{
			string sProc = "clTransform::ConvertToSellDate";
			try
			{
				string sResult = "";
				sResult = Convert.ToDateTime(dtDate).ToString("yyyy-MM-dd HH:mm:ss");
				if (sDorT == "Date")
				{
					sResult = sResult.Substring(0, 10);
				}
				else if (sDorT == "Time")
				{
					sResult = sResult.Substring(sResult.Length - 8);
				}
				else
				{
					sResult = sResult.Substring(0, 10) + "|" + sResult.Substring(sResult.Length - 8);
				}
				return sResult;
			}
			catch (Exception ex)
			{
				//goLog.LogActivity(ex.Message & vbCrLf & ex.StackTrace, 1)
				return dtDate.ToString();
			}
		}

		public string BuildString(string sReturnString, string sString, string sDelimiter)
		{
			//PURPOSE:
			//		builds a delimited string
			//PARAMETERS:
			//		sReturnString:              the string to start with
			//		sString:                    the string to append
			//		sDelimiter:                 the delimiter between start string and appended string
			//RETURNS:
			//		new string
			//AUTHOR: WT
			string sProc = "clUtil::BuildString";
			try
			{
				switch (sReturnString)
				{
					case "":
						sReturnString = sString;
						break;
					default:
						sReturnString = sReturnString + sDelimiter + sString;
						break;
				}
				return sReturnString;
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return "";
			}
		}

		public string GetSysName(string sFile, string sID)
		{
			//MI 11/10/09 Added sFields parameter to clRowset instantiation.
			//PURPOSE:
			//		builds a toprec string for a specific record in a specific sort
			//PARAMETERS:
			//		sTable:             file that record is in
			//		sSort:              sort to build toprec on
			//		sGID:               gid_id of record
			//RETURNS:
			//		toprec string
			//AUTHOR: WT
			string sProc = "clUtil::GetTopRec";
			try
			{
				clRowSet oRS;
				oRS = new clRowSet(sFile, clC.SELL_READONLY, "GID_ID='" + sID + "'", "GID_ID", "SYS_Name"); //MI 11/10/09 added SYS_Name in list of fields
				if (oRS.GetFirst() == 1)
				{
					return Convert.ToString(oRS.GetFieldVal("SYS_NAME"));
				}
				else
				{
					return "";
				}
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return "";
			}
		}

		public string GetTopRec(string sTable, string sSort, string sGID)
		{
			//MI 7/12/10 Changed returning clC.SELL_BLANK_DATETIME in descending DTx cases to the format with | between date and time.
			//MI 11/12/09 Added calculating end of the range values for date grouping fields in desc order.
			//MI 11/10/09 Added If sSort = "" Then sSort = "SYS_Name" to ensure a field will be in sFields parameter of new clRowset.
			//PURPOSE:
			//		builds a toprec string for a specific record in a specific sort
			//PARAMETERS:
			//		sTable:             file that record is in
			//		sSort:              sort to build toprec on
			//		sGID:               gid_id of record
			//RETURNS:
			//		toprec string
			//AUTHOR: WT
			string sProc = "clUtil::GetTopRec";
			try
			{
				clRowSet oRS = null;
				string sField = null;
				string sTopRec = "";
				int i = 0;
				Microsoft.VisualBasic.Collection cSort = goData.GetFilterSortFields("SORT=" + sSort);
				string sFieldByItself = null;
				string sDirection = null;
				DateTime dtDate = default(DateTime);
				string sDate = null;
				sSort = "";
				for (i = 1; i <= cSort.Count; i++)
				{
					sField = goTr.ExtractString(cSort[i].ToString(), 1, "|");
					if (goData.IsFieldValid(sTable, sField))
					{
						sSort = BuildString(sSort, sField, ", ");
					}
				}
				//MI 11/10/09 added
				if (sSort == "")
				{
					sSort = "SYS_Name";
				}
				oRS = new clRowSet(sTable, clC.SELL_READONLY, "GID_ID='" + sGID + "'", "", sSort);
				if (oRS.GetFirst() == 1)
				{
					for (i = 1; i <= cSort.Count; i++)
					{
						sField = goTr.ExtractString(cSort[i].ToString(), 1, "|");
						if (goData.IsFieldValid(sTable, sField))
						{
							//MI 11/12/09 Added considering direction to calculate the end of the 
							//period value for DTx (date grouping) fields
							sDirection = goTr.ExtractString(cSort[i].ToString(), 2, "|"); //Returns ASC or DESC
							if (sDirection.ToUpper().Substring(0, 1) == "A")
							{
								//In ascending direction, DTx field values are the starting values and no calculation is needed
								goTr.StrWrite(ref sTopRec, "TOPREC_" + sField, goTr.PrepareForSQL(goTr.DelimitFilterConditionValue(sField, Convert.ToString(oRS.GetFieldVal(sField, clC.SELL_SQLSTRING)))));
							}
							else
							{
								//For descending date grouping fields, calculate the end of the date range value
								if (sField.IndexOf("%%") + 1 > 0)
								{
									sFieldByItself = goTr.GetFieldPartFromLinkName(sField);
								}
								else
								{
									sFieldByItself = sField;
								}
								//Calculate the end time of the year, quarter, month, day period
								switch (goTr.GetPrefix(sFieldByItself))
								{
									case "DTY_":
										dtDate = Convert.ToDateTime(oRS.GetFieldVal(sField, clC.SELL_SYSTEM));
										if (dtDate <= clC.SELL_BLANK_DTDATETIME)
										{
											sDate = clC.SELL_BLANK_SYSDATETIME; //MI 7/12/10 Changed from clC.SELL_BLANK_DATETIME
										}
										else
										{
											sDate = dtDate.Year.ToString() + "-12-31|23:59:59.996";
										}
										goTr.StrWrite(ref sTopRec, "TOPREC_" + sField, goTr.PrepareForSQL(goTr.DelimitFilterConditionValue(sField, sDate)));
										break;
									case "DTQ_":
										dtDate = Convert.ToDateTime(oRS.GetFieldVal(sField, clC.SELL_SYSTEM));
										if (dtDate <= clC.SELL_BLANK_DTDATETIME)
										{
											sDate = clC.SELL_BLANK_SYSDATETIME; //MI 7/12/10 Changed from clC.SELL_BLANK_DATETIME
										}
										else
										{
											dtDate = goTr.GetQuarterDate(dtDate);
											dtDate = dtDate.AddMonths(3);
											dtDate = dtDate.AddMilliseconds(-4);
											sDate = goTr.DateTimeToSysString(dtDate);
										}
										goTr.StrWrite(ref sTopRec, "TOPREC_" + sField, goTr.PrepareForSQL(goTr.DelimitFilterConditionValue(sField, sDate)));
										break;
									case "DTM_":
										dtDate = Convert.ToDateTime(oRS.GetFieldVal(sField, clC.SELL_SYSTEM));
										if (dtDate <= clC.SELL_BLANK_DTDATETIME)
										{
											sDate = clC.SELL_BLANK_SYSDATETIME; //MI 7/12/10 Changed from clC.SELL_BLANK_DATETIME
										}
										else
										{
											//Get first of next month
											sDate = goTr.Pad((dtDate.Month + 1).ToString(), 2, "0", "L");
											sDate = dtDate.Year.ToString() + "-" + sDate + "-01";
											dtDate = goTr.StringToDate(sDate, clC.SELL_SYSTEMFORMAT);
											//Get 4 milliseconds back into the previous day
											dtDate = dtDate.AddMilliseconds(-4);
											sDate = goTr.DateTimeToSysString(dtDate);
										}
										goTr.StrWrite(ref sTopRec, "TOPREC_" + sField, goTr.PrepareForSQL(goTr.DelimitFilterConditionValue(sField, sDate)));
										break;
									case "DTD_":
										dtDate = Convert.ToDateTime(oRS.GetFieldVal(sField, clC.SELL_SYSTEM));
										if (dtDate <= clC.SELL_BLANK_DTDATETIME)
										{
											sDate = clC.SELL_BLANK_SYSDATETIME; //MI 7/12/10 Changed from clC.SELL_BLANK_DATETIME
										}
										else
										{
											sDate = goTr.DateToString(dtDate, clC.SELL_SYSTEMFORMAT);
											sDate += "|23:59:59.996";
										}
										goTr.StrWrite(ref sTopRec, "TOPREC_" + sField, goTr.PrepareForSQL(goTr.DelimitFilterConditionValue(sField, sDate)));
										break;
									default:
										goTr.StrWrite(ref sTopRec, "TOPREC_" + sField, goTr.PrepareForSQL(goTr.DelimitFilterConditionValue(sField, Convert.ToString(oRS.GetFieldVal(sField, clC.SELL_SQLSTRING)))));
										break;
								}
							}
						}
					}
				}
				return sTopRec;
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return "";
			}
		}

		public void RemoveAlert(string sType, string sID)
		{
			//MI 3/17/09 clRowset.New: added true in par_bGetAllUsersUnsharedRecs, par_bSysFileFullPermissions params.
			//PURPOSE:
			//		removes an alert based on parameters by deleting the page in md.
			//PARAMETERS:
			//		sType:              type of alert (FORM OR DESKTOP)
			//		sID:                ID of record (for form), or id of desktop
			//RETURNS:
			//		Nothing
			//AUTHOR: WT
			string sProc = "clUtil::RemoveAlert";
			//Try
			var sCondition = "TXT_PAGE[='alt%' AND EXECUTE='" + sID + "' AND TYPE='" + sType + "' AND GID_SECTION = '" + goP.GetUserTID() + "'";
				//MI 3/17/09 Added true in par_bGetAllUsersUnsharedRecs, par_bSysFileFullPermissions params 
				//to make the deletion succeed under any login (normally MD can be modified only by authors and admins).
				clRowSet oRS = new clRowSet("MD", 1, sCondition, "TXT_PAGE", "GID_ID", -1, "", "", "", "", "", false, false, true, false, -1, "", false, true);
				if (oRS.GetFirst() != 0)
				{
					oRS.DeleteRecord();
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public string ReverseSort(string sTable, string sSort)
		{
			string sProc = "clUtil::ReverseSort";
			try
			{
				int i = 0;
				string sField = null;
				string sOrder = null;
				string sNewSort = null;
				clUtil oUtil = new clUtil();
				Microsoft.VisualBasic.Collection cSort = goData.GetFilterSortFields("SORT=" + sSort, false);
				sNewSort = "";
				for (i = 1; i <= cSort.Count; i++)
				{
					sField = goTr.ExtractString(cSort[i].ToString(), 1, "|");
					sOrder = goTr.ExtractString(cSort[i].ToString(), 2, "|");
					if (goData.IsFieldValid(sTable, sField))
					{
						switch (sOrder)
						{
							case "ASC":
								sOrder = "DESC";
								break;
							case "DESC":
								sOrder = "ASC";
								break;
						}
						sNewSort = oUtil.BuildString(sNewSort, sField + " " + sOrder, ", ");
					}
					else
					{
						return "";
					}
				}
				return sNewSort;

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return "";
			}
		}

		public clUtil()
		{
			Initialize();
		}

		public bool SessionRestartAgainAgain()
		{
			object a = new object();
			return false;
		}

		public bool IsMobile()
		{
			//Try

			string sAgent = HttpContext.Current.Request.UserAgent.ToLower();

				// Define some test strings.
				//Dim tests() As String = {"iphone", "blackberry", "midp", "j2me", "avant", "docomo", _
				//                            "novarra", "palmos", "palmsource", "240x320", _
				//                            "opwv", "chtml", "pda", "windows ce", _
				//                            "mmp/", "mib/", "symbian", _
				//                            "wireless", "nokia", "hand", "mobi", _
				//                            "phone", "cdm", "up.b", "audio", _
				//                            "SIE-", "SEC-", "samsung", "HTC", _
				//                            "mot-", "mitsu", "sagem", "sony", _
				//                            "alcatel", "lg", "eric", "vx", _
				//                            "NEC", "philips", "mmm", "xx", _
				//                            "panasonic", "sharp", "wap", "sch", _
				//                            "rover", "pocket", "benq", "java", _
				//                            "vox", "amoi", _
				//                            "bird", "compal", "kg", "voda", _
				//                            "sany", "kdd", "dbt", "sendo", _
				//                            "sgh", "gradi", "jb", "dddi", _
				//                            "moto"}

				string[] tests = {"ipad", "iphone", "blackberry", "midp", "j2me", "avant", "docomo", "novarra", "palmos", "palmsource", "240x320", "opwv", "chtml", "windows ce", "mmp/", "mib/", "symbian", "wireless", "nokia", "hand", "mobi", "phone", "cdm", "up.b", "audio", "SIE-", "SEC-", "samsung", "HTC", "mot-", "mitsu", "sagem", "sony", "alcatel", "eric", "NEC", "philips", "panasonic", "sharp", "rover", "pocket", "benq", "java", "moto"};

				//user had the following, picked up on "pt"
				//Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; GTB5; .NET CLR 2.0.50727; MSN Optimized;US; MSN Optimized;US)
				//user had the following, picked up on "pg"
				//"Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; xss--3- Mi_ATXLD5bBTFzUGAmyrl_8_IXcLakDRazRjHQ38UhApg8viTh_Uo1XcEvuKa3TLAiVykVSokyqcqg3jOgGdoA; SLCC1; .NET CLR 2.0.50727; .NET CLR 3.0.04506; .NET CLR 1.1.4322; InfoPath.2)"
				//"Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; xss--3- Mi_ATXLD5bBTFzUGAmyrl_8_IXcLakDRazRjHQ38UhApg8viTh_Uo1XcEvuKa3TLAiVykVSokyqcqg3jOgGdoA; SLCC1; .net clr 2.0.50727; .NET CLR 3.0.04506; .NET CLR 1.1.4322; InfoPath.2)"
				// Check each test string against the regular expression.
				foreach (string test in tests)
				{
					if (sAgent.IndexOf(test) + 1 > 0)
					{
						if (test == "ipad")
						{
							return false;
						}
						return true;
					}
					else
					{
						//do nothing, keep looping
					}
				}

				return false;

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then

			//    End If
			//End Try
		}

		//V_T 2/19/2015
		//Function - strips out all the HTML Tags and return the plaint text from HTML content
		//MMR Change
		public string StripHTML(string source)
		{
			try
			{

				//If source.Length > 20000 Then
				//    Return source
				//End If

				string result;

				// Remove HTML Development formatting
				// Replace line breaks with space
				// because browsers inserts space
				result = source.Replace("\r", " ");
				// Replace line breaks with space
				// because browsers inserts space
				result = result.Replace("\n", " ");
				// Remove step-formatting
				result = result.Replace("\t", string.Empty);
				// Remove repeating spaces because browsers ignore them
				result = System.Text.RegularExpressions.Regex.Replace(result, "( )+", " ");

				// Remove the header (prepare first by clearing attributes)
				result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*head([^>])*>", "<head>", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "(<( )*(/)( )*head( )*>)", "</head>", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "(<head>).*(</head>)", string.Empty, System.Text.RegularExpressions.RegexOptions.IgnoreCase);

				// remove all scripts (prepare first by clearing attributes)
				result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*script([^>])*>", "<script>", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "(<( )*(/)( )*script( )*>)", "</script>", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				//result = System.Text.RegularExpressions.Regex.Replace(result,

				// string.Empty,
				// System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "(<script>).*(</script>)", string.Empty, System.Text.RegularExpressions.RegexOptions.IgnoreCase);

				// remove all styles (prepare first by clearing attributes)
				result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*style([^>])*>", "<style>", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "(<( )*(/)( )*style( )*>)", "</style>", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "(<style>).*(</style>)", string.Empty, System.Text.RegularExpressions.RegexOptions.IgnoreCase);

				// insert tabs in spaces of <td> tags
				result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*td([^>])*>", "\t", System.Text.RegularExpressions.RegexOptions.IgnoreCase);

				// insert line breaks in places of <BR> and <LI> tags
				result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*br( )*>", "\r", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*li( )*>", "\r", System.Text.RegularExpressions.RegexOptions.IgnoreCase);

				// insert line paragraphs (double line breaks) in place
				// if <P>, <DIV> and <TR> tags
				result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*div([^>])*>", "\r" + "\r", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*tr([^>])*>", "\r" + "\r", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "<( )*p([^>])*>", "\r" + "\r", System.Text.RegularExpressions.RegexOptions.IgnoreCase);

				// Remove remaining tags like <a>, links, images,
				// comments etc - anything that's enclosed inside < >
				result = System.Text.RegularExpressions.Regex.Replace(result, "<[^>]*>", string.Empty, System.Text.RegularExpressions.RegexOptions.IgnoreCase);

				// replace special characters:
				result = System.Text.RegularExpressions.Regex.Replace(result, " ", " ", System.Text.RegularExpressions.RegexOptions.IgnoreCase);

				result = System.Text.RegularExpressions.Regex.Replace(result, "&bull;", " * ", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "&lsaquo;", "<", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "&rsaquo;", ">", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "&trade;", "(tm)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "&frasl;", "/", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "&lt;", "<", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "&gt;", ">", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "&copy;", "(c)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "&reg;", "(r)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				// Remove all others. More can be added, see
				// http://hotwired.lycos.com/webmonkey/reference/special_characters/
				result = System.Text.RegularExpressions.Regex.Replace(result, "&(.{2,6});", string.Empty, System.Text.RegularExpressions.RegexOptions.IgnoreCase);

				// for testing
				//System.Text.RegularExpressions.Regex.Replace(result,
				// this.txtRegex.Text,string.Empty,
				// System.Text.RegularExpressions.RegexOptions.IgnoreCase);

				// make line breaking consistent
				result = result.Replace("\n", "\r");

				// Remove extra line breaks and tabs:
				// replace over 2 breaks with 2 and over 4 tabs with 4.
				// Prepare first to remove any whitespaces in between
				// the escaped characters and remove redundant tabs in between line breaks
				result = System.Text.RegularExpressions.Regex.Replace(result, "(" + "\r" + ")( )+(" + "\r" + ")", "\r" + "\r", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "(" + "\t" + ")( )+(" + "\t" + ")", "\t" + "\t", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "(" + "\t" + ")( )+(" + "\r" + ")", "\t" + "\r", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				result = System.Text.RegularExpressions.Regex.Replace(result, "(" + "\r" + ")( )+(" + "\t" + ")", "\r" + "\t", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				// Remove redundant tabs
				result = System.Text.RegularExpressions.Regex.Replace(result, "(" + "\r" + ")(" + "\t" + ")+(" + "\r" + ")", "\r" + "\r", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				// Remove multiple tabs following a line break with just one tab
				result = System.Text.RegularExpressions.Regex.Replace(result, "(" + "\r" + ")(" + "\t" + ")+", "\r" + "\t", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
				// Initial replacement target string for line breaks
				string breaks = "\r" + "\r" + "\r";
				// Initial replacement target string for tabs
				string tabs = "\t" + "\t" + "\t" + "\t" + "\t";
				//For index As Integer = 0 To result.Length - 1
				//    result = result.Replace(breaks, vbCr & vbCr)
				//    result = result.Replace(tabs, vbTab & vbTab & vbTab & vbTab)
				//    breaks = breaks & Convert.ToString(vbCr)
				//    tabs = tabs & Convert.ToString(vbTab)
				//Next

				// That's it.
				return result;
			}
			catch
			{
				return source;
			}
		}

	}

}
