﻿Imports Microsoft.VisualBasic
Imports System.IO
Imports System.Web
Imports System.Runtime.Serialization

Public Class clSelltisMobileBase
    Protected m_RS As clRowSet
    Protected m_clData As cljsondata
    Protected m_clListFields As ListFields
    Protected m_goTR As clTransform
    Protected m_goErr As clError
    Protected m_goData As clData
    Protected m_goP As clProject
    Protected m_goLog As clLog
    Protected m_goMeta As clMetaData




    Public Sub New(data As cljsondata)
        m_clData = data
        m_clListFields = New ListFields(m_clData.lstData)

        m_goTR = HttpContext.Current.Session("goTr")
        m_goErr = HttpContext.Current.Session("goErr")
        m_goData = HttpContext.Current.Session("goData")
        m_goP = HttpContext.Current.Session("goP")
        m_goLog = HttpContext.Current.Session("goLog")
        m_goMeta = HttpContext.Current.Session("goMeta")

    End Sub

    Public Sub WriteLog(proc As String, data As String)
        m_goLog.Log(proc, data, 0, True)
    End Sub

    Public Overridable Sub SaveOpen()


        'get the new clrowset
        If m_clData.IsNew = False Then
            m_RS = New clRowSet(m_clData.filename, clC.SELL_EDIT, m_clData.filter, "", "*", 1, "", "") 'Update
        Else
            m_RS = New clRowSet(m_clData.filename, clC.SELL_ADD, , , , , , , m_clData.createType, m_clData.createFrom, , False) 'Insert
        End If

        m_RS.ToTable()

        'V_T create the rowset fields collection to manage the dirty fields collection
        ''m_RS.PrepareDirtyFieldsCollection()

    End Sub

    Public Overridable Sub UpdateRowSet()

        'WriteLog("Base", "UpdateRowset")

        'clear all links 
        If m_clData.IsNew = False Then
            For Each item In m_clData.lstData
                If item.FieldName.Contains("LNK") Then
                    m_RS.ClearLinkAll(item.FieldName)
                End If
            Next
        End If

        'set field values
        For Each item In m_clData.lstData
            If item.FieldName.StartsWith("CUR_") Then
                m_RS.SetFieldVal(item.FieldName, item.FieldValue.Replace("$", ""), 1)
            Else
                m_RS.SetFieldVal(item.FieldName, item.FieldValue, 1)
            End If
        Next
    End Sub

    Public Overridable Function OnPreSaveData(SectionsToDisable As String) As Boolean
        'CS passing a string variable from the cus file that lists all sections that should not run in the base

        'WriteLog("Base", "OnSavePreData")
        'WriteLog("OnPreSaveSectionsToDisable", SectionsToDisable)

        'CS 05192015 This runs before the record has been committed to the rowset and before all the fields have been set to the rowset.
        'To customize this function, add code to the top of the same named function in cus_ Mobile file
        'and then call this function from it.
        'If you do not want to call this function remove the call from the _OnPreSave function in the cus file.

        'AC core functions; pass variable from cus Pre script telling which scripting functionality to ignore in the base
        If ProcessAC_Pre(SectionsToDisable) = False Then
            Return False
        End If

        If ProcessOP_Pre(SectionsToDisable) = False Then
            Return False
        End If



        Return True


    End Function

    Public Overridable Function OnPostSaveData(SectionsToDisable As String) As Boolean

        'WriteLog("Base", "OnSavePostData")

        'CS 05192015 This runs after the record has been committed to the rowset.
        'To customize this function, add code to the top of the same named function in cus_ Mobile file
        'and then call this function from it.
        'If you do not want to call this function remove the call from the _OnPostSave function in the cus file.

        'AC core post functions; pass variable from cus Post script telling which scripting functionality to ignore in the base
        If ProcessAC_Post(SectionsToDisable) = False Then
            Return False
        End If

        'OP core post functions; pass variable from cus Post script telling which scripting functionality to ignore in the base
        If ProcessOP_Post(SectionsToDisable) = False Then
            Return False
        End If


        Return True

    End Function

    Public ReadOnly Property SaveType As String

        Get
            If m_clData.IsNew = False Then
                Return "Update"
            Else
                Return "Save"
            End If
        End Get

    End Property

    Public Function GetReturnErrorMessage() As clServiceOutput
        Dim objclServiceOutput As New clServiceOutput()

        Dim sMsg As String

        sMsg = m_goErr.GetLastError("MESSAGE")
        WriteLog("SaveMsg", sMsg)

        If m_goErr.GetLastError("NUMBER") = "E47260" Then 'validation error
            WriteLog("SaveField", m_goTR.ExtractString(m_goErr.GetLastError("PARAMS"), 1))
            Dim sField As String = m_goTR.ExtractString(m_goErr.GetLastError("PARAMS"), 1) 'DB field name
            Dim sFieldLabel = m_goData.GetFieldLabelFromName(m_clData.filename, sField) 'Field label

            sMsg = "The field " & sFieldLabel & " must contain a value."

            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = SaveType & " Failed - " & sMsg
        Else
            objclServiceOutput.Status = "Fail"
            objclServiceOutput.Data = SaveType & " Failed - " & sMsg

        End If

        Return objclServiceOutput
    End Function
    Public Overridable Function Save() As clServiceOutput

        Dim objclServiceOutput As New clServiceOutput()


        SaveOpen()

        'Custom or base function failed due to missing rquired field, etc
        Dim bReturn As Boolean
        bReturn = OnPreSaveData("")

        If bReturn = False Then
            'WriteLog("Save", "Returned False")
            Return GetReturnErrorMessage()
        End If

        UpdateRowSet()

        'commit transaction
        Dim retval As Integer = m_RS.Commit()

        If retval = 1 Then

            OnPostSaveData("") 'This runs after the record has been committed to the rowset.



            objclServiceOutput.Status = "Success"
            'objclServiceOutput.Data = sType & "d Successfully"

            'Get GID_ID & SYS_NAME from the rs
            'V_T 4/20/2015
            Dim sGID_ID As String = m_RS.oDataSet.Tables(0).Rows(0)("GID_ID").ToString()
            Dim sSYS_NAME As String = m_RS.SYS_NAME_LastCommit
            objclServiceOutput.Data = SaveType & "d Successfully" & "|" & sGID_ID & "|" & sSYS_NAME

        Else
            'MS 5/12 commented by Manmeet, added fail message details

            Return GetReturnErrorMessage()

        End If

        Return objclServiceOutput
    End Function
    Public Function ProcessAC_Pre(SectionsToDisable As String) As Boolean
        'WriteLog("Base", "ProcessAC")
        'WriteLog("ProcessACSectionsToDisable", SectionsToDisable)

        'NOTE: Every section of code should be pre-pended to check if it was disabled in the custom pre script

        If m_clData.filename.ToUpper() <> "AC" Then
            Return True
        End If

        If m_goTR.StrRead(SectionsToDisable, "FILLACENDDATEANDTIME") = "" Then 'Only call base function if custom pre script didn't disable it
            'WriteLog("BaseProcessAC", "Run")
            If m_clData.IsNew Then
                m_clListFields.CopyField("DTE_ENDTIME", "DTE_STARTTIME", "Today")
                m_clListFields.CopyField("TME_ENDTIME", "TME_STARTTIME", "Now")
            End If
        Else
            'disabled in cus script
            'WriteLog("Base", "Don't Fill End Date")
        End If

        'If m_goTR.StrRead(SectionsToDisable, "EnforceProductIfPurposeIsInquiry") = "" Then
        '    'WriteLog("ProcessAC:purpose", m_clListFields.GetField("MLS_PURPOSE"))
        '    If m_clListFields.GetField("MLS_PURPOSE") = "Inquiry" And m_clListFields.GetField("LNK_RELATED_PD") = "" Then
        '        'Dim sFieldLabel As String = m_goData.GetFieldLabelFromName(m_clData.filename, "LNK_RELATED_PD")
        '        m_goErr.SetWarning(47260, "ProcessAC_Pre", "", "LNK_RELATED_PD", "", "", "", "", "", "", "", "", "LNK_RELATED_PD")
        '        Return False
        '    End If
        'End If

        'CS don't think we can do this b/c Journal field is not available on creation of a new AC?
        'If m_goTR.StrRead(SectionsToDisable, "CopyNotesToJournalIfNewLead") = "" Then
        '    If m_clData.IsNew Then
        '        If m_clListFields.GetField("MLS_PURPOSE") = "Lead" And m_clListFields.GetField("MMO_NOTES") <> "" Then
        '            'If journal is blank
        '            If m_clListFields.GetField("MMO_JOURNAL") = "" Then
        '                'Dim sDateCode As String = m_goTR.NowLocal '& " " & m_goP.GetMe("CODE")
        '                Dim dtNow As DateTime = m_goTR.NowLocal()
        '                Dim sDateStamp As String = Left(m_goTR.DateTimeToSysString(dtNow, , " "), 16)
        '                WriteLog("DateandCode", sDateStamp)
        '                m_clListFields.SetField("MMO_JOURNAL", sDateStamp & " " & m_clListFields.GetField("MMO_NOTES"))
        '            Else
        '                'If journal is not blank        '               
        '                m_clListFields.SetField("MMO_JOURNAL", m_clListFields.GetField("MMO_JOURNAL") & " " & m_clListFields.GetField("MMO_NOTES"))
        '            End If
        '        End If
        '    End If
        'End If     

        'If editing an AC, capture value of Journal being added. This will be used in Post Save script to create the linked Journal AC
        If m_goTR.StrRead(SectionsToDisable, "CreateJournalActivity") = "" Then
            If m_clListFields.GetField("MMO_JOURNAL") <> "" Then 'MMO_JOUrnal only passed when adding a journal entry
                Dim sRecordID As String = m_RS.GetFieldVal("GID_ID")
                Dim sOriginalJournal As String = m_RS.GetFieldVal("MMO_JOURNAL")
                Dim sJournalAdded As String = m_clListFields.GetField("MMO_JOURNAL")
                sJournalAdded = m_goTR.Replace(sJournalAdded, sOriginalJournal, "")
                m_goP.SetVar("NewJournal", sJournalAdded)
            End If
        End If


        Return True
    End Function
    Public Function ProcessAC_Post(SectionsToDisable As String) As Boolean

        If m_clData.filename.ToUpper() <> "AC" Then
            Return True
        End If

        If m_goTR.StrRead(SectionsToDisable, "CreateJournalActivity") = "" Then
            'Only in edit mode
            If m_clData.IsNew = False Then
                'Check if have WOP enabled to create journal activity when journal field is updated.
                If m_goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "ONSAVEAC_CREATE_AC", "0") = "1" Then
                    'Check if there is a value in MMO_JOURNAl; when adding a journal to a record we only have MMO_JOURNAL field in m_clListFields
                    'WriteLog("ProcessAC_Post", "WOP on")
                    If m_goP.GetVar("NewJournal") <> "" Then
                        Dim sRecordID As String = m_RS.GetFieldVal("GID_ID")
                        CreateJournalAC(sRecordID, m_goP.GetVar("NewJournal"))
                        m_goP.SetVar("NewJournal", "")
                    End If

                End If
            End If
        End If

        Return True
    End Function
    Public Function CreateJournalAC(RecordID As String, JournalAdded As String) As Boolean

        Dim sRecordID As String = RecordID
        Dim sJournalAdded As String = JournalAdded

        If sRecordID <> "" And sJournalAdded <> "" Then

            'Dim sNotes As String = sJournalAdded & vbCrLf & vbCrLf & "== Created from Activity '" & m_RS.GetFieldVal("SYS_NAME") & "'"
            Dim sNotes As String = sJournalAdded & vbCrLf & vbCrLf & "== Created from " & m_clData.filename & " '" & m_RS.GetFieldVal("SYS_NAME") & "'"

            Dim doNew As New clRowSet("AC", 2, , , , , , , "CRL_AC", sRecordID, , True)
            doNew.SetFieldVal("MMO_NOTES", sNotes)
            doNew.SetFieldVal("MLS_TYPE", 31, 2)
            doNew.SetFieldVal("MLS_Status", 1, 2)     'Completed
            doNew.SetFieldVal("DTT_StartTime", "Today|Now")
            doNew.SetFieldVal("DTT_EndTime", "Today|Now")
            doNew.SetFieldVal("LNK_CreditedTo_US", m_goP.GetMe("ID"))
            doNew.SetFieldVal("MMO_HISTORY", m_goTR.WriteLogLine("", "Created."))
            doNew.Commit()

        End If


        Return True
    End Function

    Function ProcessOP_Pre(SectionsToDisable As String) As Boolean

        'NOTE: Every section of code should be pre-pended to check if it was disabled in the custom pre script

        If m_clData.filename.ToUpper() <> "OP" Then
            Return True
        End If

        'If m_goTR.StrRead(SectionsToDisable, "EnforceNextActionDate") = "" Then
        '    If (m_clListFields.GetField("MLS_STATUS") = "Open" Or m_clListFields.GetField("MLS_STATUS") = "On Hold") And m_clListFields.GetField("DTE_NEXTACTIONDATE") = "" Then
        '        m_goErr.SetWarning(47260, "ProcessOP_Pre", "", "DTE_NextActionDate", "", "", "", "", "", "", "", "", "DTE_NextActionDate")
        '        Return False
        '    End If
        'End If

        'If editing an OP, capture value of Journal being added. This will be used in Post Save script to create the linked Journal AC
        If m_goTR.StrRead(SectionsToDisable, "CreateJournalActivity") = "" Then
            If m_clListFields.GetField("MMO_JOURNAL") <> "" Then 'MMO_JOUrnal only passed when adding a journal entry
                Dim sRecordID As String = m_RS.GetFieldVal("GID_ID")
                Dim sOriginalJournal As String = m_RS.GetFieldVal("MMO_JOURNAL")
                Dim sJournalAdded As String = m_clListFields.GetField("MMO_JOURNAL")
                sJournalAdded = m_goTR.Replace(sJournalAdded, sOriginalJournal, "")
                m_goP.SetVar("NewJournal", sJournalAdded)
            End If
        End If


        Return True
    End Function

    Public Function ProcessOP_Post(SectionsToDisable As String) As Boolean

        If m_clData.filename.ToUpper() <> "OP" Then
            Return True
        End If

        If m_goTR.StrRead(SectionsToDisable, "CreateJournalActivity") = "" Then
            'Only in edit mode
            If m_clData.IsNew = False Then
                'Check if have WOP enabled to create journal activity when journal field is updated.
                If m_goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "ONSAVEOPP_CREATE_AC", "0") = "1" Then
                    'Check if there is a value in MMO_JOURNAl; when adding a journal to a record we only have MMO_JOURNAL field in m_clListFields
                    If m_goP.GetVar("NewJournal") <> "" Then
                        Dim sRecordID As String = m_RS.GetFieldVal("GID_ID")
                        CreateJournalAC(sRecordID, m_goP.GetVar("NewJournal"))
                        m_goP.SetVar("NewJournal", "")
                    End If
                End If
            End If
        End If

        Return True
    End Function

End Class

<Serializable()>
<DataContract()>
Public Class clServiceOutput
    <DataMember()>
    Public Status As String
    <DataMember()>
    Public Data As String
    <DataMember()>
    Public TurnAroundTime As String
    <DataMember()>
    Public FileData As Byte()
End Class
<Serializable()>
<DataContract()>
Public Class clversion
    <DataMember()>
    Public VersionCode As String
End Class

<Serializable()>
<DataContract()>
Public Class clServiceOutputForLogin
    <DataMember()>
    Public UserId As String
    <DataMember()>
    Public Name As String
    <DataMember()>
    Public Code As String
    <DataMember()>
    Public UserName As String
    <DataMember()>
    Public Curr_MD_Version As String
    <DataMember()>
    Public UserFCMTokenID As String
    <DataMember()>
    Public FCMTokenDeviceName As String
    <DataMember()>
    Public EnabledTwoFactorAuth As Boolean
    <DataMember()>
    Public TwoFactorAuthMessage As String
    <DataMember()>
    Public AutoCRLACSave As String
    <DataMember()>
    Public Status As String
    <DataMember()>
    Public Data As String
    <DataMember()>
    Public AppVersion As String
    <DataMember()>
    Public CustomerLogo As String
    <DataMember()>
    Public IsUserAdmin As Boolean

End Class

<Serializable()>
<DataContract()>
Public Class clSiteSettings
    <DataMember()>
    Public EnableSSO As String
    <DataMember()>
    Public ClientId As String
    <DataMember()>
    Public TenantId As String
End Class

<Serializable()>
<DataContract>
Public Class clFields
    <DataMember()>
    Public FieldName As String
    <DataMember()>
    Public FieldValue As String
End Class

<Serializable()>
<DataContract>
Public Class MobileProfilePage
    <DataMember()>
    Public Property PageId As String
    <DataMember()>
    Public Property Title As String
    <DataMember()>
    Public Property ViewCount As Integer
    <DataMember()>
    Public Property ProfileViews As List(Of MobileProfileView)
    <DataMember()>
    Public Property Status As String
    <DataMember()>
    Public Property ErrorMessage As String
End Class
<Serializable()>
<DataContract>
Public Class MobileProfileView
    <DataMember()>
    Public Property ViewTitle As String
    <DataMember()>
    Public Property ViewType As String
    <DataMember()>
    Public Property ViewId As String
    <DataMember()>
    Public Property BgColor As String
    <DataMember()>
    Public Property Icon As String
    <DataMember()>
    Public Property NavigateToPageId As String
    <DataMember()>
    Public Property AddNewLink As Integer
    <DataMember()>
    Public Property GroupName As String
    <DataMember()>
    Public Property GroupValue As String
    <DataMember()>
    Public Property FileName As String
    <DataMember()>
    Public Property Condition As String
    <DataMember()>
    Public Property Sort As String
    <DataMember()>
    Public Property RowsData As List(Of RowData)
End Class

<Serializable()>
<DataContract>
Public Class RowData
    <DataMember()>
    Public Property Row1Data As String
    <DataMember()>
    Public Property Row2Data As String
    <DataMember()>
    Public Property GidId As String
End Class

<Serializable()>
<DataContract>
Public Class CustomOutput
    <DataMember()>
    Public Property RequiredFields As String
    <DataMember()>
    Public Property ShowFields As String
    <DataMember()>
    Public Property HideFields As Integer
End Class

Public Class ListFields

    Dim lstData As List(Of clFields)

    Public Sub New(data As List(Of clFields))
        lstData = data
    End Sub


    Public Function FieldExists(fieldName As String) As Boolean

        For Each item In lstData
            If item.FieldName.ToUpper() = fieldName.ToUpper() Then
                Return True
            End If
        Next

        Return False
    End Function

    Public Function GetField(fieldName As String) As String

        For Each item In lstData
            If item.FieldName.ToUpper() = fieldName.ToUpper() Then
                Return item.FieldValue
            End If
        Next

        Return Nothing
    End Function

    Public Function CopyField(toField As String, fromField As String, defaultValue As String) As Boolean

        Dim toValue As String

        If String.IsNullOrEmpty(GetField(toField)) Then
            ' not exists
            Dim fromValue As String = GetField(fromField)

            If String.IsNullOrEmpty(fromValue) Then
                toValue = defaultValue
            Else
                toValue = fromValue
            End If

            AddField(toField, toValue, True)
        End If

        Return True
    End Function
    Public Function AddField(fieldName As String, fieldValue As String, bUpdateIfExists As Boolean) As Boolean

        If (FieldExists(fieldName)) Then
            If bUpdateIfExists = True Then
                SetField(fieldName, fieldValue)
                Return True
            End If
            Return False
        End If

        Dim c1 As New clFields()
        c1.FieldName = fieldName
        c1.FieldValue = fieldValue
        lstData.Add(c1)

        Return True

    End Function

    Public Sub SetField(fieldName As String, fieldValue As String)

        For Each item In lstData
            If item.FieldName.ToUpper() = fieldName.ToUpper() Then
                item.FieldValue = fieldValue
            End If
        Next

    End Sub
End Class

<Serializable()>
<DataContract()>
Public Class cljsondata

    <DataMember()>
    Public username As String
    <DataMember()>
    Public password As String
    <DataMember()>
    Public filename As String
    <DataMember()>
    Public filter As String
    <DataMember()>
    Public lstData As New List(Of clFields)
    <DataMember()>
    Public IsNew As Boolean
    <DataMember()>
    Public createType As String
    <DataMember()>
    Public createFrom As String
    <DataMember()>
    Public App_Version As String
    <DataMember()>
    Public App_DeviceName As String
    <DataMember()>
    Public MD_Version As String
    '<DataMember()>
    'Public FileData() As Byte

End Class

Public Class UserMailSettings
    Public UserName As String
    Public Password As String
    Public ServerName As String
    Public PortNumber As Integer
    Public SmtpServerName As String
    Public SmtpPortnumber As Integer
    Public SmtpUserName As String
    Public SmtpPassword As String
End Class

Public Class clFCMUpdate

    <DataMember()>
    Public UserName As String
    <DataMember()>
    Public Password As String
    <DataMember()>
    Public FCMTokenId As String
    <DataMember()>
    Public FCMTokenDevice As String
    <DataMember()>
    Public App_Version As String
    <DataMember()>
    Public App_DeviceName As String
    <DataMember()>
    Public MD_Version As String

End Class

<Serializable()>
<DataContract()>
Public Class WebmailFolders
    <DataMember()>
    Public FolderName As String
    <DataMember()>
    Public ChildFolder As IList(Of WebmailFolders)
End Class

<Serializable()>
<DataContract()>
Public Class clMP

    <DataMember()>
    Public Property GID_ID() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_GID_ID), "", m_GID_ID)
        End Get
        Set(value As String)
            m_GID_ID = value
        End Set
    End Property
    Private m_GID_ID As String

    <DataMember()>
    Public Property TXT_VIEW_NAME() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_VIEW_NAME), "", m_TXT_VIEW_NAME)
        End Get
        Set(value As String)
            m_TXT_VIEW_NAME = value
        End Set
    End Property
    Private m_TXT_VIEW_NAME As String

    <DataMember()>
    Public Property TXT_PAGE_TYPE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_PAGE_TYPE), "", m_TXT_PAGE_TYPE)
        End Get
        Set(value As String)
            m_TXT_PAGE_TYPE = value
        End Set
    End Property
    Private m_TXT_PAGE_TYPE As String

    <DataMember()>
    Public Property TXT_PAGE_TITLE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_PAGE_TITLE), "", m_TXT_PAGE_TITLE)
        End Get
        Set(value As String)
            m_TXT_PAGE_TITLE = value
        End Set
    End Property
    Private m_TXT_PAGE_TITLE As String

    <DataMember()>
    Public Property TXT_KEY() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_KEY), "", m_TXT_KEY)
        End Get
        Set(value As String)
            m_TXT_KEY = value
        End Set
    End Property
    Private m_TXT_KEY As String

    <DataMember()>
    Public Property SI__NAVBAR_HOME() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__NAVBAR_HOME), "", m_SI__NAVBAR_HOME)
        End Get
        Set(value As String)
            m_SI__NAVBAR_HOME = value
        End Set
    End Property
    Private m_SI__NAVBAR_HOME As String

    <DataMember()>
    Public Property SI__NAVBAR_SCAN() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__NAVBAR_SCAN), "", m_SI__NAVBAR_SCAN)
        End Get
        Set(value As String)
            m_SI__NAVBAR_SCAN = value
        End Set
    End Property
    Private m_SI__NAVBAR_SCAN As String

    <DataMember()>
    Public Property SI__SAVE_CREATE_ANOTHER() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__SAVE_CREATE_ANOTHER), "", m_SI__SAVE_CREATE_ANOTHER)
        End Get
        Set(value As String)
            m_SI__SAVE_CREATE_ANOTHER = value
        End Set
    End Property
    Private m_SI__SAVE_CREATE_ANOTHER As String

    <DataMember()>
    Public Property SI__NAVBAR_JOURNAL() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__NAVBAR_JOURNAL), "", m_SI__NAVBAR_JOURNAL)
        End Get
        Set(value As String)
            m_SI__NAVBAR_JOURNAL = value
        End Set
    End Property
    Private m_SI__NAVBAR_JOURNAL As String

    <DataMember()>
    Public Property SI__NAVBAR_CREATELINKED() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__NAVBAR_CREATELINKED), "", m_SI__NAVBAR_CREATELINKED)
        End Get
        Set(value As String)
            m_SI__NAVBAR_CREATELINKED = value
        End Set
    End Property
    Private m_SI__NAVBAR_CREATELINKED As String

    <DataMember()>
    Public Property TXT_CREATELINKED_TYPE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_CREATELINKED_TYPE), "", m_TXT_CREATELINKED_TYPE)
        End Get
        Set(value As String)
            m_TXT_CREATELINKED_TYPE = value
        End Set
    End Property
    Private m_TXT_CREATELINKED_TYPE As String

    <DataMember()>
    Public Property SI__NAVBAR_EDIT() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__NAVBAR_EDIT), "", m_SI__NAVBAR_EDIT)
        End Get
        Set(value As String)
            m_SI__NAVBAR_EDIT = value
        End Set
    End Property
    Private m_SI__NAVBAR_EDIT As String

    <DataMember()>
    Public Property TXT_EDIT_PAGE_ID() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_EDIT_PAGE_ID), "", m_TXT_EDIT_PAGE_ID)
        End Get
        Set(value As String)
            m_TXT_EDIT_PAGE_ID = value
        End Set
    End Property
    Private m_TXT_EDIT_PAGE_ID As String

    <DataMember()>
    Public Property SI__NAVBAR_MARKREVIEWED() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__NAVBAR_MARKREVIEWED), "", m_SI__NAVBAR_MARKREVIEWED)
        End Get
        Set(value As String)
            m_SI__NAVBAR_MARKREVIEWED = value
        End Set
    End Property
    Private m_SI__NAVBAR_MARKREVIEWED As String

    <DataMember()>
    Public Property DTT_CREATIONTIME() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_DTT_CREATIONTIME), "", m_DTT_CREATIONTIME)
        End Get
        Set(value As String)
            m_DTT_CREATIONTIME = value
        End Set
    End Property
    Private m_DTT_CREATIONTIME As String

    <DataMember()>
    Public Property SYS_Name() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SYS_Name), "", m_SYS_Name)
        End Get
        Set(value As String)
            m_SYS_Name = value
        End Set
    End Property
    Private m_SYS_Name As String

    <DataMember()>
    Public Property SI__ShareState() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__ShareState), "", m_SI__ShareState)
        End Get
        Set(value As String)
            m_SI__ShareState = value
        End Set
    End Property
    Private m_SI__ShareState As String

    <DataMember()>
    Public Property GID_CreatedBy_US() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_GID_CreatedBy_US), "", m_GID_CreatedBy_US)
        End Get
        Set(value As String)
            m_GID_CreatedBy_US = value
        End Set
    End Property
    Private m_GID_CreatedBy_US As String

    <DataMember()>
    Public Property TXT_DETAILS_PAGE_ID() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_DETAILS_PAGE_ID), "", m_TXT_DETAILS_PAGE_ID)
        End Get
        Set(value As String)
            m_TXT_DETAILS_PAGE_ID = value
        End Set
    End Property
    Private m_TXT_DETAILS_PAGE_ID As String

    <DataMember()>
    Public Property SI__NAVBAR_ADD() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__NAVBAR_ADD), "", m_SI__NAVBAR_ADD)
        End Get
        Set(value As String)
            m_SI__NAVBAR_ADD = value
        End Set
    End Property
    Private m_SI__NAVBAR_ADD As String

    <DataMember()>
    Public Property TXT_ADD_TYPE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_ADD_TYPE), "", m_TXT_ADD_TYPE)
        End Get
        Set(value As String)
            m_TXT_ADD_TYPE = value
        End Set
    End Property
    Private m_TXT_ADD_TYPE As String

    <DataMember()>
    Public Property TXT_SCANFIELD() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_SCANFIELD), "", m_TXT_SCANFIELD)
        End Get
        Set(value As String)
            m_TXT_SCANFIELD = value
        End Set
    End Property
    Private m_TXT_SCANFIELD As String

    <DataMember()>
    Public Property TXT_ICON_NAME() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_ICON_NAME), "", m_TXT_ICON_NAME)
        End Get
        Set(value As String)
            m_TXT_ICON_NAME = value
        End Set
    End Property
    Private m_TXT_ICON_NAME As String

    <DataMember()>
    Public Property SI__PAGE_SIZE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__PAGE_SIZE), "", m_SI__PAGE_SIZE)
        End Get
        Set(value As String)
            m_SI__PAGE_SIZE = value
        End Set
    End Property
    Private m_SI__PAGE_SIZE As String

    <DataMember()>
    Public Property SI__LINKS_TOP_COUNT() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__LINKS_TOP_COUNT), "", m_SI__LINKS_TOP_COUNT)
        End Get
        Set(value As String)
            m_SI__LINKS_TOP_COUNT = value
        End Set
    End Property
    Private m_SI__LINKS_TOP_COUNT As String

    <DataMember()>
    Public Property TXT_SWIPE1FIELD() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_SWIPE1FIELD), "", m_TXT_SWIPE1FIELD)
        End Get
        Set(value As String)
            m_TXT_SWIPE1FIELD = value
        End Set
    End Property
    Private m_TXT_SWIPE1FIELD As String

    <DataMember()>
    Public Property TXT_SWIPE2FIELD() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_SWIPE2FIELD), "", m_TXT_SWIPE2FIELD)
        End Get
        Set(value As String)
            m_TXT_SWIPE2FIELD = value
        End Set
    End Property
    Private m_TXT_SWIPE2FIELD As String

    <DataMember()>
    Public Property TXT_SWIPE1EDITPAGEID() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_SWIPE1EDITPAGEID), "", m_TXT_SWIPE1EDITPAGEID)
        End Get
        Set(value As String)
            m_TXT_SWIPE1EDITPAGEID = value
        End Set
    End Property
    Private m_TXT_SWIPE1EDITPAGEID As String

    <DataMember()>
    Public Property TXT_SWIPE2EDITPAGEID() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_SWIPE2EDITPAGEID), "", m_TXT_SWIPE2EDITPAGEID)
        End Get
        Set(value As String)
            m_TXT_SWIPE2EDITPAGEID = value
        End Set
    End Property
    Private m_TXT_SWIPE2EDITPAGEID As String

    <DataMember()>
    Public Property SI__SEARCH() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__SEARCH), "", m_SI__SEARCH)
        End Get
        Set(value As String)
            m_SI__SEARCH = value
        End Set
    End Property
    Private m_SI__SEARCH As String

    <DataMember()>
    Public Property TXT_SEARCH() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_SEARCH), "", m_TXT_SEARCH)
        End Get
        Set(value As String)
            m_TXT_SEARCH = value
        End Set
    End Property
    Private m_TXT_SEARCH As String

    <DataMember()>
    Public Property SI__ROLLODEX() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__ROLLODEX), "", m_SI__ROLLODEX)
        End Get
        Set(value As String)
            m_SI__ROLLODEX = value
        End Set
    End Property
    Private m_SI__ROLLODEX As String

    <DataMember()>
    Public Property TXT_ROLLODEX() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_ROLLODEX), "", m_TXT_ROLLODEX)
        End Get
        Set(value As String)
            m_TXT_ROLLODEX = value
        End Set
    End Property
    Private m_TXT_ROLLODEX As String

    <DataMember()>
    Public Property TXT_SWIPE3FIELD() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_SWIPE3FIELD), "", m_TXT_SWIPE3FIELD)
        End Get
        Set(value As String)
            m_TXT_SWIPE3FIELD = value
        End Set
    End Property
    Private m_TXT_SWIPE3FIELD As String

    <DataMember()>
    Public Property TXT_SWIPE3EDITPAGEID() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_SWIPE3EDITPAGEID), "", m_TXT_SWIPE3EDITPAGEID)
        End Get
        Set(value As String)
            m_TXT_SWIPE3EDITPAGEID = value
        End Set
    End Property
    Private m_TXT_SWIPE3EDITPAGEID As String

    <DataMember()>
    Public Property TXT_SWIPE1CLPAGEIDS() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_SWIPE1CLPAGEIDS), "", m_TXT_SWIPE1CLPAGEIDS)
        End Get
        Set(value As String)
            m_TXT_SWIPE1CLPAGEIDS = value
        End Set
    End Property
    Private m_TXT_SWIPE1CLPAGEIDS As String

    <DataMember()>
    Public Property TXT_SWIPE2CLPAGEIDS() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_SWIPE2CLPAGEIDS), "", m_TXT_SWIPE2CLPAGEIDS)
        End Get
        Set(value As String)
            m_TXT_SWIPE2CLPAGEIDS = value
        End Set
    End Property
    Private m_TXT_SWIPE2CLPAGEIDS As String

    <DataMember()>
    Public Property TXT_SWIPE3CLPAGEIDS() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_SWIPE3CLPAGEIDS), "", m_TXT_SWIPE3CLPAGEIDS)
        End Get
        Set(value As String)
            m_TXT_SWIPE3CLPAGEIDS = value
        End Set
    End Property
    Private m_TXT_SWIPE3CLPAGEIDS As String

    <DataMember()>
    Public Property SI__CACHEDATA() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__CACHEDATA), "", m_SI__CACHEDATA)
        End Get
        Set(value As String)
            m_SI__CACHEDATA = value
        End Set
    End Property
    Private m_SI__CACHEDATA As String
    <DataMember()>
    Public Property SI__SHOWASBTNINHOMEPAGE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__SHOWASBTNINHOMEPAGE), "", m_SI__SHOWASBTNINHOMEPAGE)
        End Get
        Set(value As String)
            m_SI__SHOWASBTNINHOMEPAGE = value
        End Set
    End Property
    Private m_SI__SHOWASBTNINHOMEPAGE As String

    <DataMember()>
    Public Property SI__NEARME() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__NEARME), "", m_SI__NEARME)
        End Get
        Set(value As String)
            m_SI__NEARME = value
        End Set
    End Property
    Private m_SI__NEARME As String

    <DataMember()>
    Public Property TXT_NEARME_PAGEID() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_NEARME_PAGEID), "", m_TXT_NEARME_PAGEID)
        End Get
        Set(value As String)
            m_TXT_NEARME_PAGEID = value
        End Set
    End Property
    Private m_TXT_NEARME_PAGEID As String

    <DataMember()>
    Public Property TXT_MAINTITLE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_MAINTITLE), "", m_TXT_MAINTITLE)
        End Get
        Set(value As String)
            m_TXT_MAINTITLE = value
        End Set
    End Property
    Private m_TXT_MAINTITLE As String

End Class

<Serializable()>
<DataContract()>
Public Class clMF

    <DataMember()>
    Public Property BI__ID() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_BI__ID), "", m_BI__ID)
        End Get
        Set(value As String)
            m_BI__ID = value
        End Set
    End Property
    Private m_BI__ID As String


    <DataMember()>
    Public Property GID_ID() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_GID_ID), "", m_GID_ID)
        End Get
        Set(value As String)
            m_GID_ID = value
        End Set
    End Property
    Private m_GID_ID As String


    <DataMember()>
    Public Property TXT_MP_GID_ID() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_MP_GID_ID), "", m_TXT_MP_GID_ID)
        End Get
        Set(value As String)
            m_TXT_MP_GID_ID = value
        End Set
    End Property
    Private m_TXT_MP_GID_ID As String


    <DataMember()>
    Public Property TXT_CATEGORY() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_CATEGORY), "", m_TXT_CATEGORY)
        End Get
        Set(value As String)
            m_TXT_CATEGORY = value
        End Set
    End Property
    Private m_TXT_CATEGORY As String


    <DataMember()>
    Public Property TXT_FIELDTYPE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_FIELDTYPE), "", m_TXT_FIELDTYPE)
        End Get
        Set(value As String)
            m_TXT_FIELDTYPE = value
        End Set
    End Property
    Private m_TXT_FIELDTYPE As String


    <DataMember()>
    Public Property TXT_FIELDNAMES() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_FIELDNAMES), "", m_TXT_FIELDNAMES)
        End Get
        Set(value As String)
            m_TXT_FIELDNAMES = value
        End Set
    End Property
    Private m_TXT_FIELDNAMES As String


    <DataMember()>
    Public Property TXT_LABELNAME() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_LABELNAME), "", m_TXT_LABELNAME)
        End Get
        Set(value As String)
            m_TXT_LABELNAME = value
        End Set
    End Property
    Private m_TXT_LABELNAME As String


    <DataMember()>
    Public Property TXT_GROUP_NAME() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_GROUP_NAME), "", m_TXT_GROUP_NAME)
        End Get
        Set(value As String)
            m_TXT_GROUP_NAME = value
        End Set
    End Property
    Private m_TXT_GROUP_NAME As String


    <DataMember()>
    Public Property TXT_TITLE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_TITLE), "", m_TXT_TITLE)
        End Get
        Set(value As String)
            m_TXT_TITLE = value
        End Set
    End Property
    Private m_TXT_TITLE As String


    <DataMember()>
    Public Property TXT_WIDGETTYPE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_WIDGETTYPE), "", m_TXT_WIDGETTYPE)
        End Get
        Set(value As String)
            m_TXT_WIDGETTYPE = value
        End Set
    End Property
    Private m_TXT_WIDGETTYPE As String


    <DataMember()>
    Public Property TXT_WIDGETVALUE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_WIDGETVALUE), "", m_TXT_WIDGETVALUE)
        End Get
        Set(value As String)
            m_TXT_WIDGETVALUE = value
        End Set
    End Property
    Private m_TXT_WIDGETVALUE As String


    <DataMember()>
    Public Property TXT_FIELDVALUE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_FIELDVALUE), "", m_TXT_FIELDVALUE)
        End Get
        Set(value As String)
            m_TXT_FIELDVALUE = value
        End Set
    End Property
    Private m_TXT_FIELDVALUE As String


    <DataMember()>
    Public Property SI__IS_REQUIRED() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__IS_REQUIRED), "", m_SI__IS_REQUIRED)
        End Get
        Set(value As String)
            m_SI__IS_REQUIRED = value
        End Set
    End Property
    Private m_SI__IS_REQUIRED As String


    <DataMember()>
    Public Property TXT_REQUIRED_MESSAGE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_REQUIRED_MESSAGE), "", m_TXT_REQUIRED_MESSAGE)
        End Get
        Set(value As String)
            m_TXT_REQUIRED_MESSAGE = value
        End Set
    End Property
    Private m_TXT_REQUIRED_MESSAGE As String


    <DataMember()>
    Public Property SR__SORTORDER() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SR__SORTORDER), "", m_SR__SORTORDER)
        End Get
        Set(value As String)
            m_SR__SORTORDER = value
        End Set
    End Property
    Private m_SR__SORTORDER As String


    <DataMember()>
    Public Property SYS_Name() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SYS_Name), "", m_SYS_Name)
        End Get
        Set(value As String)
            m_SYS_Name = value
        End Set
    End Property
    Private m_SYS_Name As String


    <DataMember()>
    Public Property SI__ShareState() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__ShareState), "", m_SI__ShareState)
        End Get
        Set(value As String)
            m_SI__ShareState = value
        End Set
    End Property
    Private m_SI__ShareState As String


    <DataMember()>
    Public Property GID_CreatedBy_US() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_GID_CreatedBy_US), "", m_GID_CreatedBy_US)
        End Get
        Set(value As String)
            m_GID_CreatedBy_US = value
        End Set
    End Property
    Private m_GID_CreatedBy_US As String


    <DataMember()>
    Public Property DTT_CreationTime() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_DTT_CreationTime), "", m_DTT_CreationTime)
        End Get
        Set(value As String)
            m_DTT_CreationTime = value
        End Set
    End Property
    Private m_DTT_CreationTime As String


    <DataMember()>
    Public Property TXT_ICON_NAME() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_ICON_NAME), "", m_TXT_ICON_NAME)
        End Get
        Set(value As String)
            m_TXT_ICON_NAME = value
        End Set
    End Property
    Private m_TXT_ICON_NAME As String


    <DataMember()>
    Public Property TXT_FIELD_LOCATION() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_FIELD_LOCATION), "", m_TXT_FIELD_LOCATION)
        End Get
        Set(value As String)
            m_TXT_FIELD_LOCATION = value
        End Set
    End Property
    Private m_TXT_FIELD_LOCATION As String


    <DataMember()>
    Public Property SI__FILTER_DEFAULT() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__FILTER_DEFAULT), "", m_SI__FILTER_DEFAULT)
        End Get
        Set(value As String)
            m_SI__FILTER_DEFAULT = value
        End Set
    End Property
    Private m_SI__FILTER_DEFAULT As String


    <DataMember()>
    Public Property TXT_FILTER_SORT() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_FILTER_SORT), "", m_TXT_FILTER_SORT)
        End Get
        Set(value As String)
            m_TXT_FILTER_SORT = value
        End Set
    End Property
    Private m_TXT_FILTER_SORT As String

    <DataMember()>
    Public Property DT_DataValue() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_DT_DataValue), "", m_DT_DataValue)
        End Get
        Set(value As String)
            m_DT_DataValue = value
        End Set
    End Property
    Private m_DT_DataValue As String

    <DataMember()>
    Public Property SI__FIELD_VISIBLE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__FIELD_VISIBLE), "", m_SI__FIELD_VISIBLE)
        End Get
        Set(value As String)
            m_SI__FIELD_VISIBLE = value
        End Set
    End Property
    Private m_SI__FIELD_VISIBLE As String

    <DataMember()>
    Public Property INT_ROWNUMBER() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_INT_ROWNUMBER), "", m_INT_ROWNUMBER)
        End Get
        Set(value As String)
            m_INT_ROWNUMBER = value
        End Set
    End Property
    Private m_INT_ROWNUMBER As String

    <DataMember()>
    Public Property TXT_CRL_TYPE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_CRL_TYPE), "", m_TXT_CRL_TYPE)
        End Get
        Set(value As String)
            m_TXT_CRL_TYPE = value
        End Set
    End Property
    Private m_TXT_CRL_TYPE As String

    <DataMember()>
    Public Property SI__CRL_VISIBLE() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__CRL_VISIBLE), "", m_SI__CRL_VISIBLE)
        End Get
        Set(value As String)
            m_SI__CRL_VISIBLE = value
        End Set
    End Property
    Private m_SI__CRL_VISIBLE As String

    <DataMember()>
    Public Property TXT_CRL_PAGEID() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_CRL_PAGEID), "", m_TXT_CRL_PAGEID)
        End Get
        Set(value As String)
            m_TXT_CRL_PAGEID = value
        End Set
    End Property
    Private m_TXT_CRL_PAGEID As String

    <DataMember()>
    Public Property TXT_CRL() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_CRL), "", m_TXT_CRL)
        End Get
        Set(value As String)
            m_TXT_CRL = value
        End Set
    End Property
    Private m_TXT_CRL As String

    <DataMember()>
    Public Property TXT_CRL_DEFAULT() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_CRL_DEFAULT), "", m_TXT_CRL_DEFAULT)
        End Get
        Set(value As String)
            m_TXT_CRL_DEFAULT = value
        End Set
    End Property
    Private m_TXT_CRL_DEFAULT As String

    <DataMember()>
    Public Property SR__SORTORDERNEW() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SR__SORTORDERNEW), "", m_SR__SORTORDERNEW)
        End Get
        Set(value As String)
            m_SR__SORTORDERNEW = value
        End Set
    End Property
    Private m_SR__SORTORDERNEW As String

    <DataMember()>
    Public Property TXT_MAP_LINKBOX() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_MAP_LINKBOX), "", m_TXT_MAP_LINKBOX)
        End Get
        Set(value As String)
            m_TXT_MAP_LINKBOX = value
        End Set
    End Property
    Private m_TXT_MAP_LINKBOX As String

    <DataMember()>
    Public Property SI__SHOWLABEL() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_SI__SHOWLABEL), "", m_SI__SHOWLABEL)
        End Get
        Set(value As String)
            m_SI__SHOWLABEL = value
        End Set
    End Property
    Private m_SI__SHOWLABEL As String

    <DataMember()>
    Public Property TXT_SEARCHFILTERKEY() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_SEARCHFILTERKEY), "", m_TXT_SEARCHFILTERKEY)
        End Get
        Set(value As String)
            m_TXT_SEARCHFILTERKEY = value
        End Set
    End Property
    Private m_TXT_SEARCHFILTERKEY As String

    <DataMember()>
    Public Property TXT_SEARCHHINTTEXT() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_SEARCHHINTTEXT), "", m_TXT_SEARCHHINTTEXT)
        End Get
        Set(value As String)
            m_TXT_SEARCHHINTTEXT = value
        End Set
    End Property
    Private m_TXT_SEARCHHINTTEXT As String

    <DataMember()>
    Public Property TXT_LNKSEARCHFILTERKEY() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_LNKSEARCHFILTERKEY), "", m_TXT_LNKSEARCHFILTERKEY)
        End Get
        Set(value As String)
            m_TXT_LNKSEARCHFILTERKEY = value
        End Set
    End Property
    Private m_TXT_LNKSEARCHFILTERKEY As String

    <DataMember()>
    Public Property TXT_LNKSEARCHHINTTEXT() As String
        Get
            Return IIf(String.IsNullOrEmpty(m_TXT_LNKSEARCHHINTTEXT), "", m_TXT_LNKSEARCHHINTTEXT)
        End Get
        Set(value As String)
            m_TXT_LNKSEARCHHINTTEXT = value
        End Set
    End Property
    Private m_TXT_LNKSEARCHHINTTEXT As String

End Class

<Serializable()>
<DataContract()>
Public Class clStatus

    <DataMember()>
    Public status As String

    <DataMember()>
    Public Errormessage As String

End Class

<Serializable()>
<DataContract()>
Public Class clOutput

    <DataMember()>
    Public lstMP As List(Of clMP)

    <DataMember()>
    Public lstMF As List(Of clMF)

    <DataMember()>
    Public Data As String

    <DataMember()>
    Public Datarows As New List(Of Dictionary(Of String, String))()

    <DataMember()>
    Public objStatus As New clStatus

    <DataMember()>
    Public CurrentMDVersion As String

    <DataMember()>
    Public ProfilePage As New MobileProfilePage()

    <DataMember()>
    Public CusOutput As New CustomOutput()

End Class
