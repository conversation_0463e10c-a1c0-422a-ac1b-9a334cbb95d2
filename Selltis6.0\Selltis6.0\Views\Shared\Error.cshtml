﻿@*@Response.StatusCode = 500*@
@using Selltis.Core;
@model System.Web.Mvc.HandleErrorInfo

@{
    =ViewBag.Title = "Error";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@*<h1 class="text-danger">Error.</h1>
<h2 class="text-danger">An error occurred while processing your request.</h2>

@if (true)
{
    <h4>Error Details:</h4>
    <p>Controller Name: @Model.ControllerName</p>
    <p>Action Name: @Model.ActionName</p>
    <p>Exception: @Model.Exception</p>
}*@

<section id="content_wrapper">
    <div id="content" class="animated fadeIn" style="padding-top: 0px;padding-bottom: 0px; padding-left: 0px !important;padding-right:0px !important">
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-visible" id="spy2" style="margin-top: 0px!important;margin-bottom: 5px!important;">


                    <div class="topbar-left">
                        <div class="panel-heading" style="background: #e9e9e9!important;height:38px;">
                            <div class="panel-title" style="height:38px;margin-top:4px;">
                                <i class="glyphicon glyphicon-th-large"></i>@Model.ActionName
                                @*<span>Controller Name: @Model.ControllerName</span>*@
                            </div>

                            <button type="button" class="btn btn-sm btn-primary" title="Cancel" onclick="history.back();"><i class="fa fa-times"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" id="ErrorHeadLineDiv" style="padding:1.5%;">
            <div class="row">
                <label id="LBL_ErrorMessage" style="color:red">We're sorry, an error occurred.  Please contact your Selltis administrator with the following information:</label>
            </div>
            <div class="row" style="margin-bottom:5px;">
                <label id="LBL_Controller1" style="font-size:12px;font-weight:bold;">Controller Name: @Model.ControllerName</label>
            </div>
            <div class="row" style="margin-bottom:5px;">
                <label id="LBL_Action2" style="font-size:12px;font-weight:bold;">Action Name: @Model.ActionName</label>
            </div>
            @*<div class="row">
                <label id="LBL_ErrorInfo">@Model.Exception</label>
            </div>*@
            <div class="row">
                <br />
                @if (Util.IsUserAdmin())
                {
                    <a href="#" onclick="ErrorDetailsClick()">Details</a>
                }
            </div>
        </div>

        <div class="row" id="ErrorDetailsDiv" style="display: none;padding:1.5%;">
            <div class="row col-md-12">
                <div class="col-md-8">
                    <div class="row" style="margin-bottom:5px;">
                        <label id="LBL_Title" style="color:red;font-size:16px;font-weight:bold;">Selltis Error</label>
                    </div>
                    <div class="row" style="margin-bottom:5px;">
                        <label id="LBL_Controller" style="font-size:12px;font-weight:bold;">Controller Name: @Model.ControllerName</label>
                    </div>
                    <div class="row" style="margin-bottom:5px;">
                        <label id="LBL_Action" style="font-size:12px;font-weight:bold;">Action Name: @Model.ActionName</label>
                    </div>
                    <div class="row" style="margin-bottom:5px;">
                        <label id="LBL_Details" style="font-weight:bold;">Details:</label>
                    </div>
                    <div class="row">
                        @Html.TextAreaFor(m => m.Exception, new { @class = "col-md-12", @style = "height:200px", @disabled = "disabled" })
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    body {
        overflow: hidden !important;
    }
</style>

<script>
    var desktopid = '@Util.GetSessionValue("DesktopId")';
    $('#CMB_DESKTOPLIST').val(desktopid);
    function btnBack() {
        window.history.back(-2);
    }
    function DesktopChange(desktopid) {
        var FolderId = '@Util.GetSessionValue("FolderID")';
        window.location.href = '/Desktop/LoadDesktop/' + desktopid + '/false/' + FolderId;
    }

    function ErrorDetailsClick()
    {
        $('#ErrorHeadLineDiv').hide();
        $('#ErrorDetailsDiv').show();
    }

    function onCancel() {
        var desktopId = '@Convert.ToString(Util.GetSessionValue("DesktopId"))';
        var FolderId = '@Convert.ToString(Util.GetSessionValue("FolderID"))';
        if (FolderId == null || FolderId == '' || FolderId == undefined) {
            FolderId = 'FOLDERID';
        }
        var HistoryKey = '@Convert.ToString(Util.GetSessionValue("LastDesktopHistoryKey"))';
        if (HistoryKey == null || HistoryKey == '' || HistoryKey == undefined) {
            HistoryKey = 'FOLDERID';
        }

        if (desktopId == null || desktopId != '') {
            window.location = '/Desktop/LoadDesktop?DesktopId=' + desktopId + '&FolderId=' + FolderId + '&Key=' + HistoryKey;
        }
        else {
            window.location = '/Login/LoginSubmit';
        }
    }
</script>
