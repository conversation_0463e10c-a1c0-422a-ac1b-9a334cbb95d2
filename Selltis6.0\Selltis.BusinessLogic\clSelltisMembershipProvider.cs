﻿using System;
using System.Web;
using System.Web.Security;
using System.Configuration;
using System.Data;

namespace Selltis.BusinessLogic
{
	public class SelltisMembershipProvider : MembershipProvider
	{
		//OWNER: RH
		//MI 10/24/13 Deprecated deactivation date notion, commented code that tests deactivation.
		//MI 8/13/09 Added Return Nothing to GetUser() (plus one override function). 
		//MI 4/27/09 Bug fix: ValidateUser: A login without a User record linked or for which there is no User record caused a lockup during login. 


		//---for database access use---
		private string connStr;
		private System.Data.OleDb.OleDbCommand comm = new System.Data.OleDb.OleDbCommand(); //Data.SqlClient.SqlCommand

		private bool _requiresQuestionAndAnswer;
		private int _minRequiredPasswordLength;

		public override void Initialize(string name, System.Collections.Specialized.NameValueCollection config)
		{

			//===retrives the attribute values set in 
			//web.config and assign to local variables===

			if (config["requiresQuestionAndAnswer"] == "true")
			{
				_requiresQuestionAndAnswer = true;
			}

			//Dim sConnectionString As String = ""
			//Dim sUrl As String
			//Dim sHostingEnvironment As String = ConfigurationManager.AppSettings("HostingEnvironment").ToString().ToLower()

			//'#If DEBUG Then
			//'        sConnectionString = "SelltisConnectionString"
			//'#End If

			//If sHostingEnvironment = "debugging" Then
			//    sConnectionString = "SelltisConnectionString"
			//End If

			//If String.IsNullOrEmpty(sConnectionString) Then

			//    If sHostingEnvironment = "staging" Then
			//        'site url is IP address , when hosted in local / test server
			//        sUrl = HttpContext.Current.Request.Url.Port.ToString()
			//    Else
			//        sUrl = HttpContext.Current.Request.Url.Host
			//        sUrl = sUrl.ToLower().Replace(".selltis.com", "")
			//    End If
			//    sConnectionString = sUrl & "_SelltisConnectionString"

			//End If

			//connStr = ConfigurationManager.ConnectionStrings(sConnectionString).ConnectionString 'config("connectionString")
			string sHostName = clSettings.GetHostName();
			if (HttpContext.Current.Session[sHostName + "_SiteSettings"] == null)
			{
				clSettings.LoadSiteSettings();
			}

			connStr = Convert.ToString(((DataTable)HttpContext.Current.Session[sHostName + "_SiteSettings"]).Rows[0]["ConnectionString"]);

			base.Initialize(name, config);
		}

		public override string ApplicationName
		{
			get
			{
				return this.Name.ToString();
			}
			set
			{
			}
		}

		public override bool ChangePassword(string username, string oldPassword, string newPassword)
		{

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public override bool ChangePasswordQuestionAndAnswer(string username, string password, string newPasswordQuestion, string newPasswordAnswer)
		{

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public override System.Web.Security.MembershipUser CreateUser(string username, string password, string email, string passwordQuestion, string passwordAnswer, bool isApproved, object providerUserKey, out System.Web.Security.MembershipCreateStatus status)
		{

			System.Data.SqlClient.SqlConnection conn = new System.Data.SqlClient.SqlConnection(connStr); //OleDb.OleDbConnection(connStr)

			//----perform checking all the relevant checks here
			// and set the status of the error accordingly, e.g.:
			//status = MembershipCreateStatus.InvalidPassword
			//status = MembershipCreateStatus.InvalidAnswer
			//status = MembershipCreateStatus.InvalidEmail

			//---add the user to the database
			try
			{
				conn.Open();
				string sql = "INSERT INTO XU VALUES (" + "@username, @password, @email, " + " @passwordQuestion, @passwordAnswer )";
				System.Data.SqlClient.SqlCommand comm = new System.Data.SqlClient.SqlCommand(sql, conn);
				comm.Parameters.AddWithValue("@username", username);
				comm.Parameters.AddWithValue("@password", password);
				comm.Parameters.AddWithValue("@email", email);
				comm.Parameters.AddWithValue("@passwordQuestion", passwordQuestion);
				comm.Parameters.AddWithValue("@passwordAnswer", passwordAnswer);
				int result = comm.ExecuteNonQuery();
				conn.Close();

				status = MembershipCreateStatus.Success;
				MembershipUser user = new MembershipUser("SelltisMembershipProvider", username, null, email, passwordQuestion, null, true, false, DateTime.Now, default(DateTime), default(DateTime), default(DateTime), default(DateTime));
				return user;
			}
			catch (Exception ex)
			{

//				System.Windows.MessageBox.Show(ex.ToString());
				//---failed; determine the reason why
				status = MembershipCreateStatus.UserRejected;
				return null;
			}
		}

		public override bool DeleteUser(string username, bool deleteAllRelatedData)
		{

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public override bool EnablePasswordReset
		{
			get
			{

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
				return false;
			}
		}

		public override bool EnablePasswordRetrieval
		{
			get
			{

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
				return false;
			}
		}

		public override System.Web.Security.MembershipUserCollection FindUsersByEmail(string emailToMatch, int pageIndex, int pageSize, out int totalRecords)
		{
			System.Web.Security.MembershipUserCollection ret = new System.Web.Security.MembershipUserCollection();
			totalRecords = 0; // The code is never there to set this value

            return ret;

		}

		public override System.Web.Security.MembershipUserCollection FindUsersByName(string usernameToMatch, int pageIndex, int pageSize, out int totalRecords)
		{
			System.Web.Security.MembershipUserCollection ret = new System.Web.Security.MembershipUserCollection();
            totalRecords = 0; // The code is never there to set this value
            return ret;

		}

		public override System.Web.Security.MembershipUserCollection GetAllUsers(int pageIndex, int pageSize, out int totalRecords)
		{
			System.Web.Security.MembershipUserCollection ret = new System.Web.Security.MembershipUserCollection();
            totalRecords = 0; // The code is never there to set this value
            return ret;
		}

		public override int GetNumberOfUsersOnline()
		{

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return 0;
		}

		public override string GetPassword(string username, string answer)
		{
			return "";
		}

		public override System.Web.Security.MembershipUser GetUser(string username, bool userIsOnline)
		{
			return null;
		}

		public override System.Web.Security.MembershipUser GetUser(object providerUserKey, bool userIsOnline)
		{
			return null;
		}

		public override string GetUserNameByEmail(string email)
		{
			return "";

		}

		public override int MaxInvalidPasswordAttempts
		{
			get
			{

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
				return 0;
			}
		}

		public override int MinRequiredNonAlphanumericCharacters
		{
			get
			{

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
				return 0;
			}
		}

		public override int MinRequiredPasswordLength
		{
			get
			{

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
				return 0;
			}
		}

		public override int PasswordAttemptWindow
		{
			get
			{

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
				return 0;
			}
		}

		public override System.Web.Security.MembershipPasswordFormat PasswordFormat
		{
			get
			{

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
				return 0;
			}
		}

		public override string PasswordStrengthRegularExpression
		{

			get
			{
				return "";

			}
		}

		public override bool RequiresQuestionAndAnswer
		{
			get
			{
				if (_requiresQuestionAndAnswer == true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
		}

		public override bool RequiresUniqueEmail
		{
			get
			{

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
				return false;
			}
		}

		public override string ResetPassword(string username, string answer)
		{
			return "";
		}

		public override bool UnlockUser(string userName)
		{

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public override void UpdateUser(System.Web.Security.MembershipUser user)
		{

		}

		public override bool ValidateUser(string username, string password)
		{
			//MI 11/2/11 Changed text of the login lockout message.
			//MI 10/9/09 Added login timeout on 5 failed login attempts.
			//MI 7/29/09 Added checking GLOBAL MOBILE perm if user doesn't have an explicit one. This fixes default Mobile permission not working.
			//MI 4/27/09 Bug fix: A login not linked to a User record caused a lockup during login. 
			//DVF 4/2/09 Added IP address to login logging.
			//MI 3/12/09 Added logging login success and failure.
			//MI 11/19/08 Added 'cus_' to 'Agreement.aspx'.
			//MI 5/16/08 Added brackets in SQL statement(s).
			//MI 5/4/07 Implemented redirecting to Agreement.aspx
			//MI 3/12/07 Modified to work with XU table

			//IMPORTANT: Don't just Return True, but GOTO LoginSucceeded!

			//Dim conn As New OleDb.OleDbConnection(connStr)
			string sProc = "clSelltisMembershipProvider::ValidateUser";
			string sID = HttpContext.Current.Session.SessionID;

			string sMessage = null;
			System.Data.SqlClient.SqlConnection conn = new System.Data.SqlClient.SqlConnection(connStr);
			string sClientIP = null;
			string sTemp = null;
			DateTime dtNow = DateTime.Now;
			string sNow = null;
			string sql = null;
			string sql2 = null;
			System.Data.SqlClient.SqlCommand comm = null;
			System.Data.SqlClient.SqlCommand comm2 = null;
			System.Data.SqlClient.SqlDataReader reader = null;
			System.Data.SqlClient.SqlDataReader reader2 = null;
			int iLogins = 0;
			int iFailureCount = 0;
			//Added to test case of password  RH 12/4/09
			string sPassword = password;
			bool bAgree = true; //End
			clMembershipData oMD = new clMembershipData();
			bool bAdminBypass = false;
			sClientIP = HttpContext.Current.Request.UserHostAddress;
			conn.Open();
			oMD.WriteAuthenticationMessage(username, password, "");

			try
			{

				if (HttpContext.Current.Session["sProduct"] == null)
				{
					HttpContext.Current.Session["sProduct"] = "SA";
				}
				else
				{
					if (HttpContext.Current.Session["sProduct"].ToString() == "")
					{
						HttpContext.Current.Session["sProduct"] = "SA";
					}
				}

				string tempVar = Convert.ToString(HttpContext.Current.Session["sProduct"]);
				SetSession(ref conn, ref tempVar);
					HttpContext.Current.Session["sProduct"] = tempVar;
				HttpContext.Current.Session["LASTLOGINFAILUREREASON"] = "";
				HttpContext.Current.Session["PASSWORD"] = password;


				//---------------- MI 10/14/09 login lockout changes ---------------
				//Determine number of logins (users who can log in)
				sql = "Select COUNT(*) From [XU] " + "WHERE [CHK_LoginGroup] IS null or [CHK_LoginGroup] = 0";
				comm = new System.Data.SqlClient.SqlCommand(sql, conn);
				iLogins = Convert.ToInt32(comm.ExecuteScalar());
				if (iLogins < 1)
				{
					iLogins = 1;
				}

				//Count total workgroup-wide number of failures within last 5 minutes. Disallow login if necessary.
				//This is done to slow down brute force attacks. We can add notifying the admin here.
				sql = "SELECT COUNT(*) FROM [XL] WHERE([SI__Purpose] = 2 And [DTT_CreationTime] > DateAdd(Minute, -" + clC.SELL_GROUPLOGINFAILURELOCKOUTMINUTES + ", GetUTCDate()))";
				comm = new System.Data.SqlClient.SqlCommand(sql, conn);
				iFailureCount = Convert.ToInt32(comm.ExecuteScalar());
				if (iFailureCount >= iLogins * clC.SELL_MAXFAILEDTOTALLOGINATTEMPTSMULTIPLIER)
				{
					//DEBUG
					//If iFailureCount > 8 Then
					sMessage = "Workgroup logins locked out: '" + username + "', IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
					Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE);
					//Communicate back to login.aspx the reason for login failure. We don't want to alarm users nor reveal too much about why.
					//MI 11/2/11 Changed the text from "Try logging in after a few minutes."
					HttpContext.Current.Session["LASTLOGINFAILUREREASON"] = "The system is temporarily locked out for security reasons. Try again in a few minutes.";
					return false;
				}

				//Count login failures of the user logging in. If > allowed number, 
				//lock the user out until enough time passes for the number to drop.
				sql = "SELECT COUNT(*) FROM [XL] WHERE([SI__Purpose] = 2) and [DTT_CreationTime] > DateAdd(minute, -" + clC.SELL_USERLOGINFAILURELOCKOUTMINUTES.ToString() + ", GetUTCDate()) and TXT_Message LIKE 'Login failed: ''" + username + "''%' ";
				comm = new System.Data.SqlClient.SqlCommand(sql, conn);
				iFailureCount = Convert.ToInt32(comm.ExecuteScalar());
				if (iFailureCount >= clC.SELL_MAXFAILEDUSERLOGINATTEMPTS)
				{
					//User login failed more than the allowed no of times within the allowed period
					sMessage = "User login locked out: '" + username + "', IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
					Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE);
					//Communicate back to login.aspx the reason for login failure.
					//MI 11/2/11 Changed the text from "Try logging in after a few minutes."
					HttpContext.Current.Session["LASTLOGINFAILUREREASON"] = "You are temporarily locked out for security reasons. Try again in a few minutes.";
					return false;
				}
				//------------------------------------------

				sNow = ("0000" + dtNow.Year.ToString()).Substring(("0000" + dtNow.Year.ToString()).Length - 4) + "-" + ("00" + dtNow.Month.ToString()).Substring(("00" + dtNow.Month.ToString()).Length - 2) + "-" + ("00" + dtNow.Day.ToString()).Substring(("00" + dtNow.Day.ToString()).Length - 2) + " " + ("00" + dtNow.Hour.ToString()).Substring(("00" + dtNow.Hour.ToString()).Length - 2) + ":" + ("00" + dtNow.Minute.ToString()).Substring(("00" + dtNow.Minute.ToString()).Length - 2) + ":" + ("00" + dtNow.Second.ToString()).Substring(("00" + dtNow.Second.ToString()).Length - 2) + "." + ("000" + dtNow.Millisecond.ToString()).Substring(("000" + dtNow.Millisecond.ToString()).Length - 3);
				//'Encrypt the connection string and set it in a session var
				//Dim oCrypt As New Crypto.SymmCrypto
				//oCrypt.SymmCrypto(Crypto.SymmCrypto.SymmProvEnum.DES)
				//Dim s As String = oCrypt.Encrypting(connStr, "SellKey")
				//HttpContext.Current.Session("ConnString") = s

				HttpContext.Current.Session["ConnString"] = connStr;
				username = username.ToUpper();

				//This code is SQL injection attack-proof
				sql = "Select * From [XU] WHERE " + "[TXT_logonname]=@username"; // AND [TXT_password]=@password"
				comm = new System.Data.SqlClient.SqlCommand(sql, conn);
				comm.Parameters.AddWithValue("@username", username);
				//comm.Parameters.AddWithValue("@password", password)

				//DO NOT ENABLE! The following block allows these SQL injection attacks to succeed:
				//   NOW PLUGGED:    ' or 1=1 --
				//   LETS ME IN:     ' or XU.TXT_LogonName = 'MARIOI' --
				//   SUCCEEDS:       ' or 1=1; drop table AA; --
				//   SHOULD SUCCEED: ; shutdown with nowait; --
				//Dim sql As String = "Select * From [XU] WHERE " & _testig of
				//       "[TXT_logonname]='" & username & "' AND [TXT_password]='" & password & "'"
				//Dim comm As New Data.SqlClient.SqlCommand(sql, conn)

				reader = comm.ExecuteReader();

				if (reader.HasRows)
				{
					//Log("RHTEST", "MESSAGE 4")
					DataTable dt = new DataTable();
					bool bReinit = false;

					dt.Load(reader);
					//Loading the datatable closes the reader

					//More than one row returned? Either there is a duplicate username/password or
					//somehow the query got compromised (SQL injection?)
					if (dt.Rows.Count != 1)
					{
						//No User referenced in the login
						sMessage = "Login failed because multiple Login (XU) records were returned for this login: '" + username + "', IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
						Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE, false, false, 0, 2);
						conn.Close();
						return false;
					}

					//Test case of password
					if (sPassword.Length < 3) //handles "" and Null cases.
					{
						return false;
					}
					if (sPassword != Convert.ToString(dt.Rows[0]["Txt_password"]))
					{
						//If password for user fails, test if Admin password
						if (oMD.TestIfPasswordIsAdmin(sPassword) == true)
						{
							//Password is Admin.. log and continue
							sMessage = "Admin logging in as: '" + username + "', IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
							Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE, false, false, 0, 2);
							bAdminBypass = true;
						}
						else
						{
							//Password is not Admin.. fail
							sMessage = "Login failed: '" + username + "', IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
							Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE, false, false, 0, 2);
							conn.Close();
							return false;
						}
					}
					//End

					//**************************************************************
					//                User Login and Password Tests

					if (bAdminBypass == false)
					{



						if (oMD.TestIfLoginIsEnabled(username) == false)
						{
							sMessage = "Login failed as not enabled: '" + username + "', IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
							Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE, false, false, 0, 2);
							oMD.WriteAuthenticationMessage(username, password, "3");
							return false;
						}

						//MI 10/24/13 Deprecated deactivation date notion
						//If oMD.TestIfLoginIsDeactivated(username) Then
						//    sMessage = "Login failed as past deactivation date: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'."
						//    Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2)
						//    oMD.WriteAuthenticationMessage(username, password, "4")
						//    Return False
						//End If

						if (oMD.TestIfPasswordHasExpired(username))
						{
							sMessage = "Login password has expired: '" + username + "', IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
							Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE, false, false, 0, 2);
							oMD.WriteAuthenticationMessage(username, password, "7");
							return false;
						}



						if (oMD.TestIfPasswordIsTemporary(username, password))
						{
							if (oMD.TestIfTemporaryPasswordHasExpired(username, password))
							{
								sMessage = "Login failed because password is temporary and has expired: '" + username + "', IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
								Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE, false, false, 0, 2);
								oMD.WriteAuthenticationMessage(username, password, "1");
								return false;
							}
							else
							{
								sMessage = "Login failed because password is temporary: '" + username + "', IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
								Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE, false, false, 0, 2);
								oMD.WriteAuthenticationMessage(username, password, "2");
								return false;
							}
						}

					}

					//**************************************************************


					if (HttpContext.Current.Session["USERID"] != "")
					{
						bReinit = true;
					}

					sTemp = dt.Rows[0]["GID_UserID"].ToString();

					HttpContext.Current.Session["USERID"] = sTemp;
					HttpContext.Current.Session["LOGINID"] = dt.Rows[0]["GID_ID"].ToString();
					HttpContext.Current.Session["LOGINNAME"] = dt.Rows[0]["TXT_LogonName"].ToString();

					//---------------------------------
					//SHOULD THIS BE REENABLED HERE?
					//MI 4/27/09 Moved the following block to after login validations take place.
					//With this code here the initialization was failing if the login isn't valid
					//causing the Catch to fail the login. To the user this looks like the page
					//returns without any indication of login failure.
					//If bReinit = True Then
					//    Dim oInit As New clInit
					//    oInit.ReInitialize()
					//End If
					//---------------------------------

					//Is there a User for this login?
					if (sTemp.Trim(' ') == "")
					{
						//No User referenced in the login
						sMessage = "Login failed because no User (US) record is associated with this login: '" + username + "', IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
						Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE, false, false, 0, 2);
						conn.Close();
						return false;
					}

					//Is there an actual User record referenced in this login?
					sql2 = "SELECT GID_ID FROM US WHERE GID_ID = '" + sTemp + "'";
					comm2 = new System.Data.SqlClient.SqlCommand(sql2, conn);
					reader2 = comm2.ExecuteReader();
					if (!reader2.HasRows)
					{
						//User isn't allowed to log onto MB
						sMessage = "Login failed because User record '" + sTemp + "' referenced in login: '" + username + "' cannot be found. IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
						Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE, false, false, 0, 2);
						reader2.Close();
						conn.Close();
						return false;
					}
					reader2.Close();
					if (bAdminBypass == false)
					{


						//Check product permission
						switch (HttpContext.Current.Session["sProduct"])
						{
							//--> Code additional product permissions as additional CASEs.
							case "MB":
								sql2 = "SELECT GID_ID FROM XP WHERE GID_Section = @loginid and TXT_Page = 'FEATURES' and TXT_Property = 'MOBILE' and TXT_Value = '1'";
								comm2 = new System.Data.SqlClient.SqlCommand(sql2, conn);
								comm2.Parameters.AddWithValue("@loginid", HttpContext.Current.Session["LOGINID"]);
								reader2 = comm2.ExecuteReader();
								if (!reader2.HasRows)
								{
									//MI 7/29/09 User doesn't have an explicit Mobile permission, is there explicit denied perm?
									reader2.Close();
									sql2 = "SELECT GID_ID FROM XP WHERE GID_Section = @loginid and TXT_Page = 'FEATURES' and TXT_Property = 'MOBILE' and TXT_Value = '0'";
									comm2 = new System.Data.SqlClient.SqlCommand(sql2, conn);
									comm2.Parameters.AddWithValue("@loginid", HttpContext.Current.Session["LOGINID"]);
									reader2 = comm2.ExecuteReader();
									if (reader2.HasRows)
									{
										//MI 7/29/09 Perm explicitly denied
										sMessage = "Login failed due to no product permission: '" + username + "', IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
										Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE, false, false, 0, 2);
										reader2.Close();
										conn.Close();
										return false;
									}
									else
									{
										//MI 7/29/09 Denied perm not found. Check default permission.
										reader2.Close();
										sql2 = "SELECT GID_ID FROM XP WHERE GID_Section is NULL and TXT_Page = 'FEATURES' and TXT_Property = 'MOBILE' and TXT_Value = '1'";
										comm2 = new System.Data.SqlClient.SqlCommand(sql2, conn);
										reader2 = comm2.ExecuteReader();
										if (!reader2.HasRows)
										{
											//User isn't allowed to log onto MB
											sMessage = "Login failed due to no product permission: '" + username + "', IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
											Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE, false, false, 0, 2);
											reader2.Close();
											conn.Close();
											return false;
										}
									}
								}
								reader2.Close();
								break;
							default: //SA
							break;
								//Product permission for SA is implied currently, but could be checked in the future
						}

						//Check User Agreement acceptance
						if (dt.Rows[0]["DTT_AgrAccepted"].ToString() == "")
						{
							//User hasn't accepted the User agreement - redirect to Agreement.aspx
							conn.Close();
							//MI 4/27/09 Reinit the project "just in case"
							//If bReinit = True Then
							//    Dim oInit As New clInit
							//    oInit.ReInitialize()
							//End If
							HttpContext.Current.Session["UserAgreementAccepted"] = null;
							HttpContext.Current.Session["PASSWORD"] = password.ToString();

							bAgree = false;

							//We've left this page at this point
						}
						else
						{
							//User accepted the user agreement
							HttpContext.Current.Session["UserAgreementAccepted"] = "1";
							sMessage = "Login successful: '" + username + "', IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
							Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE, false, false, 0, 1, "", "", 0, 0, username);
							//Log UserAgent if not webservice client
							if (HttpContext.Current.Request.UserAgent.IndexOf("MS Web Services Client") + 1 == 0)
							{
								Log(sProc, HttpContext.Current.Request.UserAgent, (short)clC.SELL_LOGLEVEL_NONE, false, false, 0, 5, HttpContext.Current.Request.Browser.Browser, HttpContext.Current.Request.Browser.Version, (short)HttpContext.Current.Request.Browser.MajorVersion, HttpContext.Current.Request.Browser.MinorVersion, username);
							}
							conn.Close();
							goto LoginSucceeded;
						}

						if (bAgree == false)
						{
							goto RedirectSection;
						}

					}
	LoginSucceeded:
					//MI 4/27/09 Moved here. See note above the commented block of code above.
					if (bReinit == true)
					{
						clInit oInit = new clInit();
						oInit.ReInitialize();
					}

					HttpContext.Current.Session["PASSWORD"] = "";

					return true;

				}
				else
				{
					sMessage = "Login failed: '" + username + "', IP '" + sClientIP + "', product '" + Convert.ToString(HttpContext.Current.Session["sProduct"]) + "'.";
					Log(sProc, sMessage, (short)clC.SELL_LOGLEVEL_NONE, false, false, 0, 2, "", "", 0, 0, username);
					conn.Close();
					return false;
				}


			}
			catch (Exception ex)
			{

				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    sMessage = "Login failed due to unknown error: '" & username & "', IP '" & sClientIP & "', product '" & HttpContext.Current.Session("sProduct") & "'. Error: " & ex.Message
				//    Log(sProc, sMessage, clC.SELL_LOGLEVEL_NONE, , , , 2, , , , , username)
				//    conn.Close()
				//End If
				return false;

			}

			//Should never get here
			conn.Close();
			return false;

	RedirectSection:

			//HttpContext.Current.Response.Redirect("cus_Agreement.aspx", False) 'MI 11/19/08 added 'cus_'   '*** MI 10/18/07 added True
			HttpContext.Current.Session[clSettings.GetHostName() + "_" + "bIsRedirectToAgreement"] = "True";
			return false;

		}

		private void SetSession(ref System.Data.SqlClient.SqlConnection conn, ref string sProduct)
		{

			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();

			cmd.CommandText = "pInitSession";
			cmd.CommandType = System.Data.CommandType.StoredProcedure;
			cmd.Connection = conn;

			System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uUserID", SqlDbType.UniqueIdentifier);
			uSection.Value = StringToGuid("F0272467-3EC5-48F7-5553-987900B57A11");
			cmd.Parameters.Add(uSection);

			System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sUserCode", SqlDbType.Char);
			strPage.Value = "SYST";
			cmd.Parameters.Add(strPage);

			System.Data.SqlClient.SqlParameter strProduct = new System.Data.SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar);
			strProduct.Value = sProduct;
			cmd.Parameters.Add(strProduct);
			cmd.ExecuteNonQuery();


		}




		public bool Log(string par_sProc, string par_sMessage, short par_siLevel = 1, bool par_bStack = false, bool par_bTrace = false, short par_siWarnErr = 0, short par_tiPurpose = 0, string par_sBrowser = "", string par_sVersion = "", short par_iMajorVersion = 0, double par_dMinorVersion = 0, string par_sUser = "")
		{

			//MI 3/12/09 Added par_tiPurpose.
			//MI 3/3/09 Changed sproc reference.
			//MI 3/5/07 Added clC.SELL_LOGLINE_MAXLENGTH as length of message.

			//PURPOSE:	Write in a text file a line to log current task/message
			//
			//PARAMETERS:
			//		par_sModule: 	Module Name (the name of the module where the call is made)
			//		par_sMessage: 	The message to log 
			//		par_siLevel:	Level of the log operation
			//		Levels available:
			//		0 - None: no logging except error messages
			//		1 - Always ON, only Entry and Exit of the main modules are logged (and errors, of course)
			//		2 - Details: main functionalities of each module are also logged
			//		3 - Debug: Everything is logged
			//       par_bStack: True if method stack log
			//       par_bTrace: Used by trace system to flag as a tracg log item
			//       par_siWarnErr: 0=not error or warning; 1=warning; 2=error
			//       par_tiPurpose: 0 (default)=unassigned; 1=login success; 2=login failure; 10=UI element start
			//RETURNS:	
			//		True if the operation was logged, False if not (level of the operation > current level)

			//EXAMPLE:	goLog.Log("clImpExp:ExecuteImport", "Going to process script after line", 3)



			string sProc = "clSelltisMembershipProvider::Log";

			bool bWebservice = false;
			if (HttpContext.Current.Request.UserAgent.IndexOf("MS Web Services Client") + 1 > 0)
			{
				bWebservice = true;
			}


			int iResult = 0;
			System.Data.SqlClient.SqlConnection oConnection = GetConnection();

			System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader oReader = null;

			oCommand.CommandText = "pWriteLog";
			oCommand.CommandType = CommandType.StoredProcedure;
			oCommand.Connection = oConnection;

			//parameter
			System.Data.SqlClient.SqlParameter strProc = new System.Data.SqlClient.SqlParameter("@par_sModule", SqlDbType.VarChar);
			strProc.Value = par_sProc.Substring(0, Math.Min(80, par_sProc.Length));
			oCommand.Parameters.Add(strProc);

			//parameter
			System.Data.SqlClient.SqlParameter strMessage = new System.Data.SqlClient.SqlParameter("@par_sMessage", SqlDbType.VarChar);
			strMessage.Value = par_sMessage.Substring(0, clC.SELL_LOGLINE_MAXLENGTH); //*** MI 3/5/07
			oCommand.Parameters.Add(strMessage);

			//parameter
			System.Data.SqlClient.SqlParameter strSessionID = new System.Data.SqlClient.SqlParameter("@par_sSessionID", SqlDbType.VarChar);
			strSessionID.Value = HttpContext.Current.Session.SessionID.Substring(0, 30);
			oCommand.Parameters.Add(strSessionID);

			//parameter
			System.Data.SqlClient.SqlParameter tiLevel = new System.Data.SqlClient.SqlParameter("@par_tiLevel", SqlDbType.TinyInt);
			tiLevel.Value = par_siLevel;
			oCommand.Parameters.Add(tiLevel);

			//parameter
			System.Data.SqlClient.SqlParameter tiWarnErr = new System.Data.SqlClient.SqlParameter("@par_tiWarnErr", SqlDbType.TinyInt);
			tiLevel.Value = par_siWarnErr;
			oCommand.Parameters.Add(tiWarnErr);

			//parameter
			System.Data.SqlClient.SqlParameter bStack = new System.Data.SqlClient.SqlParameter("@par_bStack", SqlDbType.Bit);
			bStack.Value = par_bStack;
			oCommand.Parameters.Add(bStack);

			//parameter
			System.Data.SqlClient.SqlParameter bTrace = new System.Data.SqlClient.SqlParameter("@par_bTrace", SqlDbType.Bit);
			bTrace.Value = par_bTrace;
			oCommand.Parameters.Add(bTrace);

			//parameter
			System.Data.SqlClient.SqlParameter tiPurpose = new System.Data.SqlClient.SqlParameter("@par_tiPurpose", SqlDbType.TinyInt);
			tiPurpose.Value = par_tiPurpose;
			oCommand.Parameters.Add(tiPurpose);

			//parameter
			System.Data.SqlClient.SqlParameter strBrowser = new System.Data.SqlClient.SqlParameter("@par_sBrowser", SqlDbType.VarChar);
			strBrowser.Value = par_sBrowser;
			oCommand.Parameters.Add(strBrowser);

			//parameter
			System.Data.SqlClient.SqlParameter strVersion = new System.Data.SqlClient.SqlParameter("@par_sVersion", SqlDbType.VarChar);
			strVersion.Value = par_sVersion;
			oCommand.Parameters.Add(strVersion);

			//parameter
			System.Data.SqlClient.SqlParameter iMajorVersion = new System.Data.SqlClient.SqlParameter("@par_iMajorVersion", SqlDbType.Int);
			iMajorVersion.Value = par_iMajorVersion;
			oCommand.Parameters.Add(iMajorVersion);

			//parameter
			System.Data.SqlClient.SqlParameter dMinorVersion = new System.Data.SqlClient.SqlParameter("@par_iMinorVersion", SqlDbType.Float);
			dMinorVersion.Value = par_dMinorVersion;
			oCommand.Parameters.Add(dMinorVersion);

			//parameter
			System.Data.SqlClient.SqlParameter strUser = new System.Data.SqlClient.SqlParameter("@par_sLoginName", SqlDbType.VarChar);
			strUser.Value = par_sUser;
			oCommand.Parameters.Add(strUser);

			//return parameter
			System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
			retValParam.Direction = ParameterDirection.ReturnValue;
			oCommand.Parameters.Add(retValParam);

			//parameter
			System.Data.SqlClient.SqlParameter bWS = new System.Data.SqlClient.SqlParameter("@par_bWebService", SqlDbType.Bit);
			bWS.Value = bWebservice;
			oCommand.Parameters.Add(bWS);

			//execute

			oReader = oCommand.ExecuteReader();

			//Now you can grab the output parameter's value...
			iResult = Convert.ToInt16(retValParam.Value);

			oReader.Close();
			oConnection.Close();




			return true;
		}

		public System.Data.SqlClient.SqlConnection GetConnection(int par_iTimeout = 60)
		{

			//MI 5/17/06 Replaced setting ID and usercode from ConfigurationManager.AppSettings
			//           with using goP methods.
			//Estabishes a connection to SQL server.  Calls pInitSession sp to provide user info.
			//IMPORTANT: goP.InitProject must run before this runs the first time, otherwise
			//           this method will set the wrong user information in the SQL Server 
			//           session (see pInitSession sproc in SS).

			string settings = connStr;
			// Dim iTimeout As Integer = InStr(LCase(settings), "timeout=")

			settings = settings.Replace("timeout=x", "timeout=" + par_iTimeout);

			System.Data.SqlClient.SqlConnection sqlConnection1 = new System.Data.SqlClient.SqlConnection(settings);


			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();

			//Try
			cmd.CommandText = "pInitSession";
				cmd.CommandType = System.Data.CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uUserID", SqlDbType.UniqueIdentifier);
				//We don't have the real user login at this point, setting to SYSTEM login until we log on the user
				uSection.Value = StringToGuid("F0272467-3EC5-48F7-5553-987900B57A11"); //MI 1/14/09 '*** MI 5/17/06
				//uSection.Value = StringToGuid(goP.GetUserTID())  '*** MI 5/17/06
				cmd.Parameters.Add(uSection);

				System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sUserCode", SqlDbType.Char);
				strPage.Value = "SYST"; //MI 1/14/09 changed from RAH to SYST '*** MI 5/17/06
				cmd.Parameters.Add(strPage);

				System.Data.SqlClient.SqlParameter strProduct = new System.Data.SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar);
				strProduct.Value = HttpContext.Current.Session["sProduct"];
				cmd.Parameters.Add(strProduct);

				sqlConnection1.Open();
				cmd.ExecuteNonQuery();

			//Catch ex As Exception
			//    '==> raise error

			//End Try

			return sqlConnection1;

		}

		private System.Guid StringToGuid(string sValue)
		{
			System.Guid guidValue = Guid.TryParse(sValue, out guidValue) ? guidValue : Guid.Empty;
			return guidValue;
		}

        

       
    }

}
