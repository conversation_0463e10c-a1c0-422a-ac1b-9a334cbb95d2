﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Selltis.WebApi.Models
{
    public class ParamModel
    {
        public string sFile;
        public int iType;
        public string sCondition;
        public string sSort;
        public string sFields;
        public int iTop;
        public string sINI;
        public string par_sGenFieldsDefs;
        public string hostName;
        public string userName;
        public string password;
        public string iTopRec;
    }

    public class ParamUpdateModel
    {
        public string par_sDataSet;
        public string strCompareField;
        public string strExtSource;
        public string sInstructions;
        public string hostName;
        public string userName;
    }

}