﻿CREATE TABLE [dbo].[EX_Related_CO] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Expense_Related_Company_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_EX] UNIQUEIDENTIFIER NOT NULL,
    [GID_CO] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_EX_Related_CO] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CO_Connected_EX] FOREIGN KEY ([GID_EX]) REFERENCES [dbo].[EX] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_EX_Related_CO] FOREIGN KEY ([GID_CO]) REFERENCES [dbo].[CO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[EX_Related_CO] NOCHECK CONSTRAINT [LNK_CO_Connected_EX];


GO
ALTER TABLE [dbo].[EX_Related_CO] NOCHECK CONSTRAINT [LNK_EX_Related_CO];


GO
CREATE CLUSTERED INDEX [IX_CO_Connected_EX]
    ON [dbo].[EX_Related_CO]([GID_EX] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_Related_CO]
    ON [dbo].[EX_Related_CO]([GID_CO] ASC);

