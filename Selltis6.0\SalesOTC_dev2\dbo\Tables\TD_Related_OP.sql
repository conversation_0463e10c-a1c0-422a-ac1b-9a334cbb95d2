﻿CREATE TABLE [dbo].[TD_Related_OP] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_ToDo_Related_Opp_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_Related_OP] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_OP_Related_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_Related_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_Related_OP] NOCHECK CONSTRAINT [LNK_OP_Related_TD];


GO
ALTER TABLE [dbo].[TD_Related_OP] NOCHECK CONSTRAINT [LNK_TD_Related_OP];


GO
CREATE CLUSTERED INDEX [IX_OP_Related_TD]
    ON [dbo].[TD_Related_OP]([GID_TD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_Related_OP]
    ON [dbo].[TD_Related_OP]([GID_OP] ASC);

