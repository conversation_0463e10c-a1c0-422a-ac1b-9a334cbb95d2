﻿using System;
using System.Net;
using System.IO;
using System.Xml.Serialization;
using System.Web.Script.Serialization;
using System.Text;
using System.Collections.Generic;

namespace Selltis.BusinessLogic
{
	public class EfcOnlineApi
	{
		public static string ApplicationId = "86D8057D-2032-4824-A70D-CBC045231507";
		// This ApplicationId is provided by eFileCabinet
		private static string BaseUrl = "https://api.efilecabinetonline.com/";


		public static string AuthToken
		{
			get
			{
				return m_AuthToken;
			}
			set
			{
				m_AuthToken = value;
			}
		}
		private static string m_AuthToken;

		public static string GenerateUserAuthToken(string username, string password)
		{

			string url = BaseUrl + "EfcPartner/GenerateUserAuthToken";

			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.Method = "POST";

			w.ContentLength = 0;

			w.Headers.Add("PartnerApplicationId", ApplicationId);

			w.Headers.Add("EfcUserName", username);

			w.Headers.Add("EfcUserPassword", password);

			AuthToken = GetResponseString(w);

			//GetResponseString(w)
			return AuthToken;

		}



#region EfcObject Services

		public static List<EfcObject> GetCabinets()
		{

			List<EfcObject> efcos = null;

			string url = BaseUrl + "EfcObject/GetCabinets";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectTypeFlag", Convert.ToInt64(EfcObject.EfcObjectTypeFlag.AllFoldersAndFiles).ToString());



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efcos = Deserializer.DeserializeJson<List<EfcObject>>(rs.ReadToEnd());
			}

			r.Close();

			return efcos;

		}

		public static List<EfcObject> GetChildren(int EfcObjectId)
		{

			List<EfcObject> efcos = null;

			string url = BaseUrl + "EfcObject/GetChildren";


			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;


			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Timeout = 600000;

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());

			w.Headers.Add("EfcObjectTypeFlag", Convert.ToInt64(EfcObject.EfcObjectTypeFlag.AllFoldersAndFiles).ToString());



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efcos = Deserializer.DeserializeJson<List<EfcObject>>(rs.ReadToEnd());
			}


			r.Close();
			return efcos;

		}

		public static List<EfcObject> GetDescendants(int EfcObjectId)
		{

			List<EfcObject> efcos = null;

			string url = BaseUrl + "EfcObject/GetDescendants";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.ContentLength = 0;

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efcos = Deserializer.DeserializeJson<List<EfcObject>>(rs.ReadToEnd());
			}


			r.Close();
			return efcos;

		}

		public static EfcObject GetEfcObjectById(int EfcObjectId)
		{

			EfcObject efco = null;

			string url = BaseUrl + "EfcObject/Get";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());





			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efco = Deserializer.Deserialize<EfcObject>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return efco;

		}

		public static List<EfcComment> GetComments(int EfcObjectId)
		{

			List<EfcComment> efcC = null;

			string url = BaseUrl + "EfcObject/GetComments";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());





			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efcC = Deserializer.Deserialize<List<EfcComment>>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return efcC;

		}

		public static List<EfcObject> GetObjectsVisibleToUser(int EfcUserId, EfcObject.EfcObjectTypeFlag typesToGet)
		{

			List<EfcObject> efcos = null;

			string url = BaseUrl + "EfcObject/GetObjectsVisibleToUser";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentLength = 0;

			w.ContentType = "application/json";

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcUserId", EfcUserId.ToString());

			w.Headers.Add("EfcObjectTypeFlag", Convert.ToInt64(typesToGet).ToString());



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efcos = Deserializer.DeserializeJson<List<EfcObject>>(rs.ReadToEnd());
			}

			r.Close();

			return efcos;

		}

		public static EfcObject CreateEfcObject(EfcObject o)
		{

			EfcObject efco = null;

			string url = BaseUrl + "EfcObject/Create";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.Headers.Add("EfcAuthToken", AuthToken);



			SetPostData(ref w, Serializer.Serialize<EfcObject>(o));



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efco = Deserializer.Deserialize<EfcObject>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return efco;

		}

		public static EfcComment CreateComment(EfcComment c)
		{

			EfcComment efcC = null;

			string url = BaseUrl + "EfcObject/CreateComment";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", c.EfcObjectId.ToString());


			SetPostData(ref w, Serializer.Serialize<EfcComment>(c));


			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efcC = Deserializer.Deserialize<EfcComment>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return efcC;

		}

		public static EfcObject UpdateEfcObject(EfcObject o)
		{

			EfcObject efco = null;

			string url = BaseUrl + "EfcObject/Update";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.Headers.Add("EfcAuthToken", AuthToken);



			SetPostData(ref w, Serializer.Serialize<EfcObject>(o));



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efco = Deserializer.Deserialize<EfcObject>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return efco;

		}

		public static EfcObject CopyEfcObjectById(int EfcObjectId, int toParentId)
		{

			EfcObject CopiedEfco = null;

			string url = BaseUrl + "EfcObject/Copy";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());

			w.Headers.Add("ParentId", toParentId.ToString());



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				CopiedEfco = Deserializer.Deserialize<EfcObject>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return CopiedEfco;

		}

		public static EfcObject DeleteEfcObject(EfcObject o)
		{

			EfcObject efco = null;

			string url = BaseUrl + "EfcObject/Delete";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.Headers.Add("EfcAuthToken", AuthToken);



			SetPostData(ref w, Serializer.Serialize<EfcObject>(o));



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efco = Deserializer.Deserialize<EfcObject>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return efco;

		}

		public static EfcObject PurgeEfcObject(EfcObject o)
		{

			EfcObject efco = null;

			string url = BaseUrl + "EfcObject/Purge";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.Headers.Add("EfcAuthToken", AuthToken);



			SetPostData(ref w, Serializer.Serialize<EfcObject>(o));



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efco = Deserializer.Deserialize<EfcObject>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return efco;

		}

		public static List<EfcObject> SimpleSearch(string SearchCriteria)
		{

			List<EfcObject> searchResults = null;

			string url = BaseUrl + "EfcSearch/SearchSimple";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectTypeFlag", Convert.ToInt64(EfcObject.EfcObjectTypeFlag.AllFoldersAndFiles).ToString());

			w.Headers.Add("SearchString", SearchCriteria);



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				searchResults = Deserializer.DeserializeJson<List<EfcObject>>(rs.ReadToEnd());
			}

			r.Close();

			return searchResults;

		}

		public static bool UploadEfcObjectData(int EfcObjectId, string filePath)
		{

			int fileLength = 0;

			List<byte[]> fileChunks = null;

			int i = 0;



			UploadInitialize(EfcObjectId);



			fileChunks = FileToArrayChunked(filePath, 4096);
			// = 0x1000
			//fileChunks = FileToArrayChunked(filePath, 65536); // = 0x10000

			foreach (byte[] nextChunk in fileChunks)
			{

				i += 1;

				//client.UploadEfcObjectDataChunk(AuthToken, f.EfcObjectId, nextChunk, i);

				UploadChunk(new FileContentsChunk()
				{
					EfcObjectId = EfcObjectId,
					ContentsOrderId = i,
					ContentsChunk = nextChunk
				});


				fileLength += nextChunk.Length;
			}



			return UploadFinalize(EfcObjectId, fileLength);

		}

		private static bool UploadChunk(FileContentsChunk o)
		{

			string url = BaseUrl + "EfcObject/UploadChunk";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.Headers.Add("EfcAuthToken", AuthToken);



			SetPostData(ref w, Serializer.Serialize<FileContentsChunk>(o));



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			r.Close();

			return r.StatusCode == HttpStatusCode.OK;

		}

		private static bool UploadInitialize(int EfcObjectId)
		{

			string url = BaseUrl + "EfcObject/UploadInitialize";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			r.Close();

			return r.StatusCode == HttpStatusCode.OK;

		}

		private static bool UploadFinalize(int EfcObjectId, int FileBytesLength)
		{

			string url = BaseUrl + "EfcObject/UploadFinalize";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());

			w.Headers.Add("FileBytesLength", FileBytesLength.ToString());



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			r.Close();

			return r.StatusCode == HttpStatusCode.OK;

		}

		public static bool UploadStream(int EfcObjectId, Stream sourceStream)
		{

			string url = BaseUrl + "EfcObject/UploadStream";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = sourceStream.Length;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());



			Stream requestStream = w.GetRequestStream();



			int bufferSize = 104856;
			// was 1024
			int bytesRead = 0;

			byte[] buffer = new byte[bufferSize];



			while ((InlineAssignHelper(ref bytesRead, sourceStream.Read(buffer, 0, bufferSize))) > 0)
			{
				requestStream.Write(buffer, 0, bytesRead);
			}



			requestStream.Close();



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();
			r.Close();
			return r.StatusCode == HttpStatusCode.OK;

		}

		public static bool UploadImages(int EfcObjectId, Stream sourceStream)
		{

			string url = BaseUrl + "EfcObject/UploadImages";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = sourceStream.Length;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());



			Stream requestStream = w.GetRequestStream();



			int bufferSize = 104856;
			// was 1024
			int bytesRead = 0;

			byte[] buffer = new byte[bufferSize];



			while ((InlineAssignHelper(ref bytesRead, sourceStream.Read(buffer, 0, bufferSize))) > 0)
			{
				requestStream.Write(buffer, 0, bytesRead);
			}



			requestStream.Close();



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();
			r.Close();
			return r.StatusCode == HttpStatusCode.OK;

		}

		public static bool AppendToPDF(int EfcObjectId, Stream sourceStream)
		{

			string url = BaseUrl + "EfcObject/AppendToPDF";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = sourceStream.Length;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());



			Stream requestStream = w.GetRequestStream();



			int bufferSize = 104856;
			// was 1024
			int bytesRead = 0;

			byte[] buffer = new byte[bufferSize];



			while ((InlineAssignHelper(ref bytesRead, sourceStream.Read(buffer, 0, bufferSize))) > 0)
			{
				requestStream.Write(buffer, 0, bytesRead);
			}



			requestStream.Close();



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();
			r.Close();
			return r.StatusCode == HttpStatusCode.OK;

		}



		public static byte[] DownloadEfcObject(int EfcObjectId)
		{

			EfcObjectFileContents efco = null;



			string url = BaseUrl + "EfcObject/GetFileContents";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();



			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efco = Deserializer.Deserialize<EfcObjectFileContents>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return efco.Contents;

		}

		public static MemoryStream DownloadFileStream(int EfcObjectId, ref long length)
		{

			string url = BaseUrl + "EfcObject/GetFileContentsStream";


			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			length = r.ContentLength;

			MemoryStream ms = new MemoryStream();

			r.GetResponseStream().CopyTo(ms);

			r.Close();

			return ms;

		}



		public static byte[] GetViewPage(int EfcObjectId, int pageNumber, ref int totalPages)
		{

			byte[] result = null;

			totalPages = -1;



			string url = BaseUrl + "EfcObject/GetViewPage";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "image/png";

			w.ContentType = "image/png";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());

			w.Headers.Add("PageNumber", pageNumber.ToString());



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();



			if (r.StatusCode == HttpStatusCode.OK)
			{

				totalPages = int.Parse(r.Headers["TotalPages"]);



				using (StreamReader rs = new StreamReader(r.GetResponseStream()))
				{

					long responseLength = r.ContentLength;

					if (responseLength > 0)
					{

						result = new byte[responseLength];



						int bytesRead = 0;

						int totalBytes = 0;

						while ((InlineAssignHelper(ref bytesRead, rs.BaseStream.Read(result, (int)totalBytes, (int)(responseLength - totalBytes)))) > 0)
						{


							totalBytes += bytesRead;

						}

					}

				}
			}

			r.Close();

			return result;

		}

		public static byte[] GetViewPageScaled(int EfcObjectId, int pageNumber, int maxWidth, int maxHeight, ref int totalPages)
		{

			byte[] result = null;

			totalPages = -1;



			string url = BaseUrl + "EfcObject/GetViewPageScaled";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "image/jpeg";

			w.ContentType = "image/jpeg";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());

			w.Headers.Add("PageNumber", pageNumber.ToString());

			w.Headers.Add("MaxWidth", maxWidth.ToString());

			w.Headers.Add("MaxHeight", maxHeight.ToString());

			w.Timeout = 3600000;



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();



			if (r.StatusCode == HttpStatusCode.OK)
			{

				totalPages = int.Parse(r.Headers["TotalPages"]);



				using (StreamReader rs = new StreamReader(r.GetResponseStream()))
				{

					long responseLength = r.ContentLength;

					if (responseLength > 0)
					{

						result = new byte[responseLength];



						int bytesRead = 0;

						int totalBytes = 0;

						while ((InlineAssignHelper(ref bytesRead, rs.BaseStream.Read(result, (int)totalBytes, (int)(responseLength - totalBytes)))) > 0)
						{


							totalBytes += bytesRead;

						}

					}

				}
			}

			r.Close();

			return result;

		}





		public static byte[] GetThumbnail(int EfcObjectId)
		{

			byte[] result = null;



			string url = BaseUrl + "EfcObject/GetThumbnail";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "image/png";

			w.ContentType = "image/png";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();



			if (r.StatusCode == HttpStatusCode.OK)
			{

				using (StreamReader rs = new StreamReader(r.GetResponseStream()))
				{

					long responseLength = r.ContentLength;

					result = new byte[responseLength];



					int bytesRead = 0;

					int totalBytes = 0;

					while ((InlineAssignHelper(ref bytesRead, rs.BaseStream.Read(result, (int)totalBytes, (int)(responseLength - totalBytes)))) > 0)
					{


						totalBytes += bytesRead;

					}

				}
			}

			r.Close();

			return result;

		}



		public static List<EfcObject> GetAncestry(int EfcObjectId)
		{

			List<EfcObject> efcos = null;

			string url = BaseUrl + "EfcObject/GetAncestry";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());





			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efcos = Deserializer.DeserializeJson<List<EfcObject>>(rs.ReadToEnd());
			}

			r.Close();

			return efcos;

		}



		public static string GenerateUploadToken()
		{

			string url = BaseUrl + "EfcObject/GetUploadToken";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.Method = "POST";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);



			return GetResponseString(w);

		}


#region Profile Calls

		public static EfcObjectProfile GetEfcObjectProfile(int EfcObjectId)
		{

			EfcObjectProfile efcop = null;

			string url = BaseUrl + "EfcProfile/Get";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.ContentLength = 0;

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcObjectId", EfcObjectId.ToString());





			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efcop = Deserializer.Deserialize<EfcObjectProfile>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return efcop;

		}

		public static EfcObjectProfile UpdateEfcProfile(EfcObjectProfile o)
		{

			EfcObjectProfile efcop = null;

			string url = BaseUrl + "EfcProfile/Update";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.Headers.Add("EfcAuthToken", AuthToken);



			SetPostData(ref w, Serializer.Serialize<EfcObjectProfile>(o));



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efcop = Deserializer.Deserialize<EfcObjectProfile>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return efcop;

		}

		public static EfcProfile CreateEfcProfile(string name, List<EfcProfileItem> ProfileItems)
		{

			EfcProfile efcp = null;

			string url = BaseUrl + "EfcProfile/CreateProfile";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcProfileName", name);


			SetPostData(ref w, Serializer.Serialize<List<EfcProfileItem>>(ProfileItems));



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efcp = Deserializer.Deserialize<EfcProfile>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return efcp;

		}

		public static EfcProfileItem CreateProfileItem(EfcProfileItem ProfileItem)
		{

			EfcProfileItem efcpi = null;

			string url = BaseUrl + "EfcProfile/CreateProfileItem";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.Headers.Add("EfcAuthToken", AuthToken);


			SetPostData(ref w, Serializer.Serialize<EfcProfileItem>(ProfileItem));



			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{


				efcpi = Deserializer.Deserialize<EfcProfileItem>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return efcpi;
		}

		public static List<EfcProfileItem> GetProfileItems(int ProfileId)
		{

			List<EfcProfileItem> efcpi = null;

			string url = BaseUrl + "EfcProfile/GetProfileItems";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.Headers.Add("EfcAuthToken", AuthToken);

			w.Headers.Add("EfcProfileId", ProfileId.ToString());


			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{
				efcpi = Deserializer.Deserialize<List<EfcProfileItem>>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return efcpi;
		}

		public static List<EfcProfile> GetProfiles()
		{

			List<EfcProfile> efcp = null;

			string url = BaseUrl + "EfcProfile/GetProfiles";



			HttpWebRequest w = WebRequest.Create(url) as HttpWebRequest;

			w.Method = "POST";

			w.Accept = "application/json";

			w.ContentType = "application/json";

			w.Headers.Add("EfcAuthToken", AuthToken);


			HttpWebResponse r = (HttpWebResponse)w.GetResponse();

			using (StreamReader rs = new StreamReader(r.GetResponseStream()))
			{
				efcp = Deserializer.Deserialize<List<EfcProfile>>(rs.ReadToEnd(), r.ContentType);
			}

			r.Close();

			return efcp;
		}

#endregion

#endregion



#region Request/Response Helpers



		private static List<byte[]> FileToArrayChunked(string filePath, int chunkSize)
		{


			Stream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read);

			List<byte[]> bytes = new List<byte[]>();

			int cursor = 0;

			int nextChunkSize = chunkSize;

			byte[] buffer = new byte[chunkSize];

			while (cursor < fs.Length)
			{

				if (cursor + chunkSize > fs.Length)
				{
					nextChunkSize = (int)(fs.Length - cursor);

					buffer = new byte[nextChunkSize];
				}
				else
				{



					buffer = new byte[nextChunkSize];
				}
				fs.Read(buffer, 0, nextChunkSize);

				bytes.Add(buffer);

				cursor += nextChunkSize;
			}

			fs.Close();

			return bytes;

		}


		private static void SetPostData(ref HttpWebRequest w, string PostData)
		{

			byte[] postBuffer = Encoding.UTF8.GetBytes(PostData);

			w.ContentLength = postBuffer.Length;

			Stream sPostData = w.GetRequestStream();

			sPostData.Write(postBuffer, 0, postBuffer.Length);

			sPostData.Close();

		}



		private static string GetResponseString(HttpWebRequest w)
		{

			return GetResponseRaw(w).Replace("\"", "");

		}


		private static string GetResponseRaw(HttpWebRequest w)
		{

			string responseText = "";

			//Try

			HttpWebResponse r = w.GetResponse() as HttpWebResponse;

				StreamReader rs = new StreamReader(r.GetResponseStream());

				responseText = rs.ReadToEnd();

				r.Close();


				rs.Close();

			//Catch ex As Exception


			//    responseText = ex.ToString()
			//End Try

			return responseText;

		}
		private static T InlineAssignHelper<T>(ref T target, T value)
		{
			target = value;
			return value;
		}

#endregion

	}



	public sealed class Deserializer
	{
		private Deserializer()
		{
		}
		public static T Deserialize<T>(string s, string MediaTypeFormatter = "none") where T: class, new()
		{
			if (MediaTypeFormatter.Contains("/xml"))
			{
				return DeserializeXml<T>(s);
			}
			else if (MediaTypeFormatter.Contains("/json"))
			{
				return DeserializeJson<T>(s);
			}
			else
			{
				T ret = default(T);
				if (object.Equals(ret, default(T)))
				{
					ret = DeserializeXml<T>(s);
				}
				if (object.Equals(ret, default(T)))
				{
					ret = DeserializeJson<T>(s);
				}
				return ret;
			}
		}

		public static T DeserializeXml<T>(string s) where T: class, new()
		{
			var serializer = new XmlSerializer(typeof(T));
			using (var reader = new StringReader(s))
			{
				try
				{
					return (T)serializer.Deserialize(reader);
				}
				catch
				{
					// Could not be deserialized to this type. 
					return default(T);
				}
			}
		}

		public static T DeserializeJson<T>(string s) where T: class, new()
		{
			try
			{
				JavaScriptSerializer ser = new JavaScriptSerializer();
				ser.MaxJsonLength = int.MaxValue;
				return ser.Deserialize<T>(s);
			}
			catch
			{
				return default(T);
			}
		}

	}

	public sealed class Serializer
	{
		private Serializer()
		{
		}
		public static string Serialize<T>(T e) where T: class, new()
		{
			JavaScriptSerializer ser = new JavaScriptSerializer();
			ser.MaxJsonLength = int.MaxValue;
			return ser.Serialize(e);
		}
	}


	internal class EfcLogin
	{

		public string username
		{
			get
			{
				return m_username;
			}
			set
			{
				m_username = value;
			}
		}
		private string m_username;

		public string password
		{
			get
			{
				return m_password;
			}
			set
			{
				m_password = value;
			}
		}
		private string m_password;

		public string AuthToken
		{
			get
			{
				return m_AuthToken;
			}
			set
			{
				m_AuthToken = value;
			}
		}
		private string m_AuthToken;

	}



	public class EfcObject
	{

		public int EfcObjectId
		{
			get
			{
				return m_EfcObjectId;
			}
			set
			{
				m_EfcObjectId = value;
			}
		}
		private int m_EfcObjectId;

		public int ParentEfcObjectId
		{
			get
			{
				return m_ParentEfcObjectId;
			}
			set
			{
				m_ParentEfcObjectId = value;
			}
		}
		private int m_ParentEfcObjectId;

		public string Name
		{
			get
			{
				return m_Name;
			}
			set
			{
				m_Name = value;
			}
		}
		private string m_Name;

		public string Extension
		{
			get
			{
				return m_Extension;
			}
			set
			{
				m_Extension = value;
			}
		}
		private string m_Extension;

		public string TypeString
		{
			get
			{
				return m_TypeString;
			}
			set
			{
				m_TypeString = value;
			}
		}
		private string m_TypeString;

		public int TypeEnum
		{
			get
			{
				return m_TypeEnum;
			}
			set
			{
				m_TypeEnum = value;
			}
		}
		private int m_TypeEnum;

		public long SizeInBytes
		{
			get
			{
				return m_SizeInBytes;
			}
			set
			{
				m_SizeInBytes = value;
			}
		}
		private long m_SizeInBytes;

		public EfcUser CreatedByUser
		{
			get
			{
				return m_CreatedByUser;
			}
			set
			{
				m_CreatedByUser = value;
			}
		}
		private EfcUser m_CreatedByUser;

		public DateTime DateCreated
		{
			get
			{
				return m_DateCreated;
			}
			set
			{
				m_DateCreated = value;
			}
		}
		private DateTime m_DateCreated;

		public DateTime DateModified
		{
			get
			{
				return m_DateModified;
			}
			set
			{
				m_DateModified = value;
			}
		}
		private DateTime m_DateModified;

		public string ApplicationData
		{
			get
			{
				return m_ApplicationData;
			}
			set
			{
				m_ApplicationData = value;
			}
		}
		private string m_ApplicationData;




		[Flags]
		public enum EfcObjectTypeFlag
		{

			//****************************************
			//
			//             * Flags
			//
			//             ****************************************




			// The flag for None is                 00000000

			None = 0x0,



			// The flag for Cabinet is              00000001

			Cabinet = 0x1,



			// The flag for Drawer is               00000010

			Drawer = 0x2,



			// The flag for Folder is               00000100

			Folder = 0x4,



			// The flag for File is                 00001000

			File = 0x8,

			// The flag for Search is

			Search = 0x9,


			AllFoldersAndFiles = Cabinet | Drawer | Folder | File

		}

	}



	public class EfcObjectFileContents
	{

		public int EfcObjectId
		{
			get
			{
				return m_EfcObjectId;
			}
			set
			{
				m_EfcObjectId = value;
			}
		}
		private int m_EfcObjectId;

		public DateTime LastWriteTimeUtc
		{
			get
			{
				return m_LastWriteTimeUtc;
			}
			set
			{
				m_LastWriteTimeUtc = value;
			}
		}
		private DateTime m_LastWriteTimeUtc;

		public double LastWriteTimeOA
		{
			get
			{
				return m_LastWriteTimeOA;
			}
			set
			{
				m_LastWriteTimeOA = value;
			}
		}
		private double m_LastWriteTimeOA;

		public byte[] Contents
		{
			get
			{
				return m_Contents;
			}
			set
			{
				m_Contents = value;
			}
		}
		private byte[] m_Contents;

	}



	public class FileContentsChunk
	{

		public int EfcObjectId
		{
			get
			{
				return m_EfcObjectId;
			}
			set
			{
				m_EfcObjectId = value;
			}
		}
		private int m_EfcObjectId;

		public byte[] ContentsChunk
		{
			get
			{
				return m_ContentsChunk;
			}
			set
			{
				m_ContentsChunk = value;
			}
		}
		private byte[] m_ContentsChunk;

		public int ContentsOrderId
		{
			get
			{
				return m_ContentsOrderId;
			}
			set
			{
				m_ContentsOrderId = value;
			}
		}
		private int m_ContentsOrderId;

	}



	public class EfcUser
	{

		public int EfcUserId
		{
			get
			{
				return m_EfcUserId;
			}
			set
			{
				m_EfcUserId = value;
			}
		}
		private int m_EfcUserId;

		public string UserName
		{
			get
			{
				return m_UserName;
			}
			set
			{
				m_UserName = value;
			}
		}
		private string m_UserName;

		public string Password
		{
			get
			{
				return m_Password;
			}
			set
			{
				m_Password = value;
			}
		}
		private string m_Password;

		public string NameFirst
		{
			get
			{
				return m_NameFirst;
			}
			set
			{
				m_NameFirst = value;
			}
		}
		private string m_NameFirst;

		public string NameLast
		{
			get
			{
				return m_NameLast;
			}
			set
			{
				m_NameLast = value;
			}
		}
		private string m_NameLast;

		public EfcUserRoleType Role
		{
			get
			{
				return m_Role;
			}
			set
			{
				m_Role = value;
			}
		}
		private EfcUserRoleType m_Role;



		public enum EfcUserRoleType
		{

			Standard = 0,

			Portal = 1,

			Admin = 2,

			MasterAccount = 4

		}

	}



	public class EfcAccount
	{

		public string EmailAddress
		{
			get
			{
				return m_EmailAddress;
			}
			set
			{
				m_EmailAddress = value;
			}
		}
		private string m_EmailAddress;

		public string AuthToken
		{
			get
			{
				return m_AuthToken;
			}
			set
			{
				m_AuthToken = value;
			}
		}
		private string m_AuthToken;

		public int EfcUserId
		{
			get
			{
				return m_EfcUserId;
			}
			set
			{
				m_EfcUserId = value;
			}
		}
		private int m_EfcUserId;

	}



	public class EfcUserPermission
	{

		public int EfcUserId
		{
			get
			{
				return m_EfcUserId;
			}
			set
			{
				m_EfcUserId = value;
			}
		}
		private int m_EfcUserId;

		public int EfcObjectId
		{
			get
			{
				return m_EfcObjectId;
			}
			set
			{
				m_EfcObjectId = value;
			}
		}
		private int m_EfcObjectId;

		public bool PushDown
		{
			get
			{
				return m_PushDown;
			}
			set
			{
				m_PushDown = value;
			}
		}
		private bool m_PushDown;

		public EfcPermissionType PermissionType
		{
			get
			{
				return m_PermissionType;
			}
			set
			{
				m_PermissionType = value;
			}
		}
		private EfcPermissionType m_PermissionType;

	}



	public class EfcGroup
	{

		public int EfcGroupId
		{
			get
			{
				return m_EfcGroupId;
			}
			set
			{
				m_EfcGroupId = value;
			}
		}
		private int m_EfcGroupId;

		public string Name
		{
			get
			{
				return m_Name;
			}
			set
			{
				m_Name = value;
			}
		}
		private string m_Name;

		public string Description
		{
			get
			{
				return m_Description;
			}
			set
			{
				m_Description = value;
			}
		}
		private string m_Description;

	}



	public class EfcGroupUserChange
	{

		public int EfcGroupId
		{
			get
			{
				return m_EfcGroupId;
			}
			set
			{
				m_EfcGroupId = value;
			}
		}
		private int m_EfcGroupId;

		public int EfcUserId
		{
			get
			{
				return m_EfcUserId;
			}
			set
			{
				m_EfcUserId = value;
			}
		}
		private int m_EfcUserId;

		public EfcAction Action
		{
			get
			{
				return m_Action;
			}
			set
			{
				m_Action = value;
			}
		}
		private EfcAction m_Action;

	}



	public class EfcGroupPermission
	{

		public int EfcGroupId
		{
			get
			{
				return m_EfcGroupId;
			}
			set
			{
				m_EfcGroupId = value;
			}
		}
		private int m_EfcGroupId;

		public int EfcObjectId
		{
			get
			{
				return m_EfcObjectId;
			}
			set
			{
				m_EfcObjectId = value;
			}
		}
		private int m_EfcObjectId;

		public bool PushDown
		{
			get
			{
				return m_PushDown;
			}
			set
			{
				m_PushDown = value;
			}
		}
		private bool m_PushDown;

		public EfcPermissionType PermissionType
		{
			get
			{
				return m_PermissionType;
			}
			set
			{
				m_PermissionType = value;
			}
		}
		private EfcPermissionType m_PermissionType;

	}



	public enum EfcPermissionType
	{
		View = 0,

		Edit = 1,

		Delete = 2,

		Admin = 3
	}



	public enum EfcAction
	{

		None = 0,

		Add = 1,

		Remove = 2

	}



	public class EfcObjectProfile
	{
		public int EfcObjectId
		{
			get
			{
				return m_EfcObjectId;
			}
			set
			{
				m_EfcObjectId = value;
			}
		}
		private int m_EfcObjectId;

		public EfcProfile EfcProfile
		{
			get
			{
				return m_EfcProfile;
			}
			set
			{
				m_EfcProfile = value;
			}
		}
		private EfcProfile m_EfcProfile;

		public List<EfcProfileItem> Items
		{
			get
			{
				return m_Items;
			}
			set
			{
				m_Items = value;
			}
		}
		private List<EfcProfileItem> m_Items;
	}



	public class EfcProfile
	{
		public int EfcProfileId
		{
			get
			{
				return m_EfcProfileId;
			}
			set
			{
				m_EfcProfileId = value;
			}
		}
		private int m_EfcProfileId;

		public string Name
		{
			get
			{
				return m_Name;
			}
			set
			{
				m_Name = value;
			}
		}
		private string m_Name;
	}



	public class EfcProfileItem
	{
		public int EfcProfileItemId
		{
			get
			{
				return m_EfcProfileItemId;
			}
			set
			{
				m_EfcProfileItemId = value;
			}
		}
		private int m_EfcProfileItemId;

		public string Name
		{
			get
			{
				return m_Name;
			}
			set
			{
				m_Name = value;
			}
		}
		private string m_Name;

		public string Value
		{
			get
			{
				return m_Value;
			}
			set
			{
				m_Value = value;
			}
		}
		private string m_Value;

		public string Type
		{
			get
			{
				return m_Type;
			}
			set
			{
				m_Type = value;
			}
		}
		private string m_Type;

		public EfcProfileItemType TypeEnum
		{
			get
			{
				return m_TypeEnum;
			}
			set
			{
				m_TypeEnum = value;
			}
		}
		private EfcProfileItemType m_TypeEnum;

		public EfcProfileItemOption[] ValueOptions
		{
			get
			{
				return m_ValueOptions;
			}
			set
			{
				m_ValueOptions = value;
			}
		}
		private EfcProfileItemOption[] m_ValueOptions;
	}



	public enum EfcProfileItemType
	{
		// Item types

		Text = 1,

		Number = 2,

		Date = 3,

		Currency = 4,

		PhoneNumber = 5,

		List = 6,

		EfcObjectChildren = 7
	}

	public class EfcProfileItemOption
	{
		public string Value
		{
			get
			{
				return m_Value;
			}
			set
			{
				m_Value = value;
			}
		}
		private string m_Value;
	}

	public class EfcComment
	{
		public int CommentId
		{
			get
			{
				return m_CommentId;
			}
			set
			{
				m_CommentId = value;
			}
		}
		private int m_CommentId;
		public string Text
		{
			get
			{
				return m_Text;
			}
			set
			{
				m_Text = value;
			}
		}
		private string m_Text;
		public EfcUser CreatedByUser
		{
			get
			{
				return m_CreatedByUser;
			}
			set
			{
				m_CreatedByUser = value;
			}
		}
		private EfcUser m_CreatedByUser;
		public DateTime DateCreated
		{
			get
			{
				return m_DateCreated;
			}
			set
			{
				m_DateCreated = value;
			}
		}
		private DateTime m_DateCreated;
		public DateTime? DateModified
		{
			get
			{
				return m_DateModified;
			}
			set
			{
				m_DateModified = value;
			}
		}
		private DateTime? m_DateModified;
		public int EfcObjectId
		{
			get
			{
				return m_EfcObjectId;
			}
			set
			{
				m_EfcObjectId = value;
			}
		}
		private int m_EfcObjectId;
	}

	public class AccountInfo
	{
		public AccountStatusType AccountStatus
		{
			get
			{
				return m_AccountStatus;
			}
			set
			{
				m_AccountStatus = value;
			}
		}
		private AccountStatusType m_AccountStatus;

		public bool NeedsSecurityQuestion
		{
			get
			{
				return m_NeedsSecurityQuestion;
			}
			set
			{
				m_NeedsSecurityQuestion = value;
			}
		}
		private bool m_NeedsSecurityQuestion;

		public DateTime? Expiration
		{
			get
			{
				return m_Expiration;
			}
			set
			{
				m_Expiration = value;
			}
		}
		private DateTime? m_Expiration;
	}

	public enum AccountStatusType
	{
		Alpha = 101,
		Beta = 102,
		Unchanged = 0,
		Trial = 1,
		Active = 2,
		ActivePastDue = 3,
		Inactive = 4,
		Purged = 5,
		TrialExpired = 6
	}

}
