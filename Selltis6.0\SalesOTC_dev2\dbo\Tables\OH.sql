﻿CREATE TABLE [dbo].[OH] (
    [GID_ID]               UNIQUEIDENTIFIER CONSTRAINT [DF_OH_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),(0))+CONVERT([binary](2),'OH',(0)))+CONVERT([binary](6),getutcdate(),(0)),(0))) ROWGUIDCOL NOT NULL,
    [BI__ID]               BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]             NVARCHAR (80)    NULL,
    [CHK_ActiveField]      TINYINT          CONSTRAINT [DF_OH_CHK_ActiveField] DEFAULT ((1)) NULL,
    [DTT_CreationTime]     DATETIME         CONSTRAINT [DF_OH_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]         TINYINT          NULL,
    [TXT_ModBy]            VARCHAR (4)      NULL,
    [DTT_ModTime]          DATETIME         CONSTRAINT [DF_OH_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_OrderHistoryName] NVARCHAR (50)    NULL,
    [MMO_ImportData]       NTEXT            NULL,
    [SI__ShareState]       TINYINT          CONSTRAINT [DF_OH_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]     UNIQUEIDENTIFIER NULL,
    [GID_CreditedTo_US]    UNIQUEIDENTIFIER NULL,
    [GID_For_CO]           UNIQUEIDENTIFIER NULL,
    [GID_For_MO]           UNIQUEIDENTIFIER NULL,
    [GID_For_PD]           UNIQUEIDENTIFIER NULL,
    [GID_Related_VE]       UNIQUEIDENTIFIER NULL,
    [GID_Related_LO]       UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]         VARCHAR (50)     NULL,
    [DTT_OrderTime]        DATETIME         NULL,
    [TXT_OrderNo]          VARCHAR (20)     NULL,
    [SR__LineNo]           REAL             NULL,
    [TXT_CreditedToUS]     VARCHAR (50)     NULL,
    [TXT_CompanyID]        VARCHAR (10)     NULL,
    [TXT_ShipToID]         VARCHAR (10)     NULL,
    [TXT_ModelID]          VARCHAR (50)     NULL,
    [TXT_ProductID]        VARCHAR (50)     NULL,
    [TXT_VendorID]         VARCHAR (50)     NULL,
    [SR__Qty]              REAL             NULL,
    [CUR_UnitPrice]        MONEY            NULL,
    [CUR_ExtPrice]         MONEY            NULL,
    [SR__DiscountPerc]     REAL             NULL,
    [MMO_Description]      TEXT             NULL,
    [TXT_BranchID]         NCHAR (20)       NULL,
    [TXT_ExternalID]       NVARCHAR (120)   NULL,
    [TXT_ExternalSource]   VARCHAR (10)     NULL,
    [TXT_ImpJobID]         VARCHAR (20)     NULL,
    CONSTRAINT [PK_OH] PRIMARY KEY CLUSTERED ([GID_ID] ASC) WITH (FILLFACTOR = 95)
);

