﻿@model Selltis.Core.Report
@using Selltis.Core;
@using Kendo.Mvc.UI;

<script id="photo-template" type="text/x-kendo-template">
    <div>Total: #: Value #</div>
</script>

<link href="~/Content/themes/Selltis/css/Custom/PartialPageReportStyles.css?v=1.1" rel="stylesheet" />

@(Html.Hidden("AggregateColumns" + Model.ViewId.Replace(" ", ""), Model.AggregateColumns))
@(Html.Hidden("DISPLAYSECTIONS" + Model.ViewId.Replace(" ", ""), Model.DISPLAYSECTIONS))
@(Html.Hidden("CURSymbols" + Model.ViewId.Replace(" ", ""), Model.CURSymbols))

<input type="hidden" id="<EMAIL>(" ", "")" value="@Model.DependencyViewIds" />
<input type="hidden" id="<EMAIL>(" ", "")" value="@Model.DependencyViewIdsOnFocus" />
<input type="hidden" id="<EMAIL>(" ", "")" value="@Model.ParentViewId" />
<input type="hidden" id="<EMAIL>(" ", "")" />
<input type="hidden" id="<EMAIL>(" ", "")" value="report" />

@(Html.Kendo().TreeList<dynamic>()
               .Name("grid" + Model.ViewId.Replace(" ", ""))

                .Columns(columns =>
                {                     
                    foreach (Selltis.Core.GridColumn column in Model.Columns)
                    {
                        var c = columns.Add().Field(column.Name.Replace(",", "").Replace("<%", "").Replace("%>", "").Replace(" ", "")).Title(column.Title).Width(column.Width);

                        c.HtmlAttributes(new { @style = "vertical-align: text-top;" })
                         .Encoded(false);

                        if (column.Alignment == "C" || column.Alignment.ToUpper() == "CENTER")
                            c.HtmlAttributes(new { @style = "text-align:center;vertical-align:text-top;" });
                        else if (column.Alignment == "R" || column.Alignment.ToUpper() == "RIGHT")
                            c.HtmlAttributes(new { @style = "text-align:right;vertical-align:text-top;" });

                        if (column.Name.Contains("CUR_") || column.Name.Contains("SI__"))
                        {
                            c.Template("#=FormatNegativeValue(data." + column.Name + ",\'" + column.Name.ToUpper() + "\',\'" + Model.ViewId.Replace(" ", "") + "\')#");
                        }

                        if (!column.Name.Equals("id") && !column.Name.Equals("parentId"))
                        {
                            if (column.Name.Equals("Value"))
                            {
                                c.Template("<div>Total: #: Value #</div>");
                            }
                            else if (column.Name.Equals("GID_ID"))
                            {
                                c.Hidden(true);
                            }

                            else if (column.IsIcon == true)
                            {
                                c.Template("#=TreeListTemplateData(data." + column.Name + ",\'" + column.Name.ToUpper() + "\',\'" + Model.ViewId.Replace(" ", "") + "\')#");
                            }

                            //To set bustton style for MLS columns..S1
                            if (column.IsButton == true)
                            {
                                c.Encoded(false);
                            }

                            else if (column.IsLink == true)
                            {
                                c.Sortable(false);
                                if (column.Name.ToUpper().Contains("EML_"))
                                {
                                    string colValue = "#:data." + column.Name + "#";
                                    c.Template("#if(data." + column.Name + "==null){}else{#<a href='mailto:" + colValue + "?Subject='>" + colValue + "</a>#}#");
                                }
                                else if (column.Name.ToUpper().Contains("LNK_"))
                                {
                                    c.Template("#=getLinks(data." + column.Name + ",\'" + column.Name.ToUpper() + "\')#");
                                }
                            }
                        }
                        if (column.Name.ToUpper().Contains("MLS_") && column.AllowEdit == true)
                        {

                            c.Template(string.Format("#=GetMLSControlR(\'" + Model.TableName + "\',\'" + column.Name.ToUpper() + "\',data." + column.Name + ",\'" + column.Width + "\',\'" + Model.ViewId + "\',\'" + Model.Key + "\'" + ",\'" + column.Title + "\')#"));
                        }

                        if (column.Name.ToUpper().Contains("DTE_") && column.AllowEdit == true)
                        {
                            string dateValue1 = "#:data." + column.Name + "#";
                            string _id = "DT_#:data.GID_ID#";
                            c.Template("<input style = \"height:auto !important;\" id=" + _id + "  onmouseover=\"DTClicked(this);\"  name=" + _id + "  value=" + dateValue1 + "  onchange=\"GetSelectedVal(this," + "'" + Model.ViewId.Replace(" ", "") + "'," + "'" + Model.TableName + "'," + "'" + column.Name.ToUpper() + "'," + "'" + Model.EnableBulkRecordsSaveOnView.ToLower() + "'," + "'" + column.Title + "'" + "," + "'" + Model.ViewType + "'" + ")\">");
                        }

                        if (column.IsSortable == false)
                        {
                            c.Sortable(false).HeaderAttributes(new { @title = "This column is not sortable" });
                        }
                        else
                        {
                            //tkt #2014 '\\#' replaced in column title.
                            //set sort icon for default sort field
                            if (column.Name == Model.DefaultSortField)
                            {
                                if (Model.DefaultSortDirection == "DESC")
                                {
                                    c.Sortable(false).HeaderTemplate("<span title = 'This column is sortable' name = 'SortableField' class='k-link' onclick=\"onGridColumnSortClick('" + column.Name + "', '" + Model.ViewId.Replace(" ", "") + "', '" + Model.TableName + "')\" > " + column.Title.Replace("#", "\\#") + "   <span id='" + Model.Key + "_" + Model.ViewId.Replace(" ", "") + "_" + column.Name.Replace(" ", "") + "_SortableFieldIcon' class='fa fa-long-arrow-down' >");
                                }
                                else
                                {
                                    c.Sortable(false).HeaderTemplate("<span title = 'This column is sortable' name = 'SortableField' class='k-link' onclick=\"onGridColumnSortClick('" + column.Name + "', '" + Model.ViewId.Replace(" ", "") + "', '" + Model.TableName + "')\" > " + column.Title.Replace("#", "\\#") + "   <span id='" + Model.Key + "_" + Model.ViewId.Replace(" ", "") + "_" + column.Name.Replace(" ", "") + "_SortableFieldIcon' class='fa fa-long-arrow-up' >");
                                }
                            }
                            else
                            {
                                c.Sortable(false).HeaderTemplate("<span title = 'This column is sortable' name = 'SortableField' class='k-link' onclick=\"onGridColumnSortClick('" + column.Name + "', '" + Model.ViewId.Replace(" ", "") + "', '" + Model.TableName + "')\" > " + column.Title.Replace("#", "\\#") + "   <span id='" + Model.Key + "_" + Model.ViewId.Replace(" ", "") + "_" + column.Name.Replace(" ", "") + "_SortableFieldIcon' class='fa fa-long-arrow-up' style='display: none;' >");
                            }
                        }
                    }
                })
                    .Filterable(false)
                    .Scrollable(Model.Scrollable)
                    .Sortable(false)
                    .Resizable(true)
                    .Selectable(true)
                    .HtmlAttributes(new { @style = "width:99.8%;height:100%!important" })
                    .DataSource(dataSource => dataSource
                    .Read(read => read.Action("ReadData1_V2", "Report", new { ViewKey = Model.ViewKey, firstload = Model.FirstLoad, Key = Model.Key }))
                    .ServerOperation(false)
                    )
)
@{
    SessionViewInfo _sessionViewInfo = new SessionViewInfo();
    var hdn_LastSelectedIndex = "";
    var hdn_LastSelectedValue = "";
    if (Selltis.Core.Util.GetSessionValue(Model.Key + "_" + Model.ViewId.Replace(" ", "")) != null)
    {
        _sessionViewInfo = (SessionViewInfo)Selltis.Core.Util.GetSessionValue(Model.Key + "_" + Model.ViewId.Replace(" ", ""));
        hdn_LastSelectedIndex = _sessionViewInfo.LastSelectedRowIndex.ToString();
        hdn_LastSelectedValue = _sessionViewInfo.LastSelectedRecord;
    }
    _sessionViewInfo = null;
}
<input type="hidden" id="<EMAIL>(" ","")" value="@hdn_LastSelectedIndex" />
<input type="hidden" id="<EMAIL>(" ","")" value="@hdn_LastSelectedValue" />

<!--Version for code minification starts here..J-->

@Html.Hidden("hidPartialGridViewId", Model.ViewId)
@Html.Hidden("hidPartialGridViewKey", Model.ViewId.Replace(" ", ""))
@Html.Hidden("hidKey_" + Model.ViewId.Replace(" ", ""), Model.Key)
@Html.Hidden("hidSiteId_" + Model.ViewId.Replace(" ", ""), Model.SiteId)

@Html.Hidden("hidDisplayNegativeValuesInRed_" + Model.ViewId.Replace(" ", ""), Model.DisplayNegativeValuesInRed)
@Html.Hidden("hidIsMasterView_" + Model.ViewId.Replace(" ", ""), Model.IsMasterView)
@Html.Hidden("hidIsMaster_And_Client_View_" + Model.ViewId.Replace(" ", ""), Model.IsMaster_And_Client_View)
@Html.Hidden("hidReportTooLong_" + Model.ViewId.Replace(" ", ""), Model.ReportTooLong)
@Html.Hidden("hidOneLinePerRecord_" + Model.ViewId.Replace(" ", ""), Model.OneLinePerRecord)
@Html.Hidden("hidAutoCount_" + Model.ViewId.Replace(" ", ""), Model.AutoCount)
@Html.Hidden("hidIsTabView_" + Model.ViewId.Replace(" ", ""), Model.IsTabView)
@Html.Hidden("hidIsActive_" + Model.ViewId.Replace(" ", ""), Model.IsActive)
@Html.Hidden("hidIndex_" + Model.ViewId.Replace(" ", ""), Model.Index)
@Html.Hidden("hidDependencyViewIds_" + Model.ViewId.Replace(" ", ""), Model.DependencyViewIds)
@Html.Hidden("hidDependencyViewIdsOnFocus_" + Model.ViewId.Replace(" ", ""), Model.DependencyViewIdsOnFocus)
@Html.Hidden("hidTableName_" + Model.ViewId.Replace(" ", ""), Model.TableName)
@Html.Hidden("hidViewRecordOpen_" + Model.ViewId.Replace(" ", ""), Model.ViewRecordOpen)
@Html.Hidden("hidOrderIndex_" + Model.ViewId.Replace(" ", ""), Model.OrderIndex)
@Html.Hidden("hidIsSelectSpecificRecord_" + Model.ViewId.Replace(" ", ""), Model.IsSelectSpecificRecord)
@Html.Hidden("hidSelectSpecificRecordId_" + Model.ViewId.Replace(" ", ""), Model.SelectSpecificRecordId)
@Html.Hidden("hidIsParent_" + Model.ViewId.Replace(" ", ""), Model.IsParent)
@Html.Hidden("hidAutoLoad_" + Model.ViewId.Replace(" ", ""), Model.AutoLoad)
@Html.Hidden("hidEnableBulkRecordsSaveOnView_" + Model.ViewId.Replace(" ", ""), Model.EnableBulkRecordsSaveOnView)
@Html.Hidden("hidUseHeadings_" + Model.ViewId.Replace(" ", ""), Model.UseHeadings)
@Html.Hidden("hidFirstTdPossibleWidth_" + Model.ViewId.Replace(" ", ""), Model.FirstTdPossibleWidth)
@Html.Hidden("hidViewMultiSelect_" + Model.ViewId.Replace(" ", ""), Model.ViewMultiSelect)

@Html.Hidden("hidDesktopId_" + Model.ViewId.Replace(" ", ""), Selltis.Core.Util.GetSessionValue("DesktopId"))

<!--Version for code minification ends here..J-->

<div id="@Model.ViewId.Replace("V", "divReportTooLong").Replace(" ", "")" style="display: none; background-color: #FFFFB0;">
    <label style="margin-left:3px;">cannot display the view because it has more than 10000 records. Modify the filter to return less data.</label>
</div>

<script src="~/Content/themes/Selltis/scripts/Custom/PartialPageReportScripts.js?v1=2.7" type="text/javascript"></script>

<script>
    $(document).ready(function () {
        ReportDocumentReady('@Model.ViewId', '@Model.ViewId.Replace(" ", "")');
    });

    $(function () {
        AvoidReportDragAndDropInMobileLAyout('@Model.ViewId.Replace(" ", "")')
    });

</script>