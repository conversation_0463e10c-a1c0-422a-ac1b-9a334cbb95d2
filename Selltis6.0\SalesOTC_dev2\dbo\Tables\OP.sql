﻿CREATE TABLE [dbo].[OP] (
    [GID_ID]                         UNIQUEIDENTIFIER CONSTRAINT [DF_OP_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'OP',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                         BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                       NVARCHAR (3000)  NULL,
    [FIL_Attachments]                NTEXT            NULL,
    [MLS_CompetingPosition]          SMALLINT         NULL,
    [MMO_CompetitionNotes]           NTEXT            NULL,
    [DTT_CreationTime]               DATETIME         CONSTRAINT [DF_OP_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [MLS_Currency]                   SMALLINT         NULL,
    [DTT_DateClosed]                 DATETIME         NULL,
    [CHK_DemoData]                   TINYINT          NULL,
    [SR__ExchRate]                   REAL             NULL,
    [DTT_ExpCloseDate]               DATETIME         NULL,
    [MMO_Influences]                 NTEXT            NULL,
    [MMO_Journal]                    NTEXT            NULL,
    [SI__Month]                      TINYINT          NULL,
    [SI__MonthClose]                 TINYINT          NULL,
    [MMO_NextAction]                 NTEXT            NULL,
    [DTT_NextActionDate]             DATETIME         NULL,
    [MMO_Notes]                      NTEXT            NULL,
    [TXT_Description]                NVARCHAR (1000)  NULL,
    [CHK_Open]                       TINYINT          NULL,
    [CHK_PrimaryOpp]                 TINYINT          NULL,
    [MLS_Priority]                   SMALLINT         NULL,
    [SI__Probability]                TINYINT          NULL,
    [CHK_Q01]                        TINYINT          NULL,
    [CHK_Q02]                        TINYINT          NULL,
    [CHK_Q03]                        TINYINT          NULL,
    [CHK_Q04]                        TINYINT          NULL,
    [CHK_Q05]                        TINYINT          NULL,
    [CHK_Q06]                        TINYINT          NULL,
    [CHK_Q07]                        TINYINT          NULL,
    [CHK_Q08]                        TINYINT          NULL,
    [CHK_Q09]                        TINYINT          NULL,
    [CHK_Q10]                        TINYINT          NULL,
    [CHK_Q11]                        TINYINT          NULL,
    [CHK_Q12]                        TINYINT          NULL,
    [CHK_Q13]                        TINYINT          NULL,
    [CHK_Q14]                        TINYINT          NULL,
    [CHK_Q15]                        TINYINT          NULL,
    [SR__Qty]                        REAL             NULL,
    [MMO_Questionnaire]              NTEXT            NULL,
    [MLS_ReasonWonLost]              SMALLINT         NULL,
    [MLS_Stage]                      SMALLINT         NULL,
    [MLS_Status]                     SMALLINT         NULL,
    [MMO_Strategy]                   NTEXT            NULL,
    [MMO_SWOT]                       NTEXT            NULL,
    [DTT_Time]                       DATETIME         NULL,
    [CUR_UnitValue]                  MONEY            NULL,
    [URL_URLs]                       NTEXT            NULL,
    [CUR_Value]                      MONEY            NULL,
    [CUR_ValueIndex]                 MONEY            NULL,
    [TXT_Year]                       CHAR (4)         NULL,
    [TXT_YearClose]                  CHAR (4)         NULL,
    [TXT_ModBy]                      VARCHAR (4)      NULL,
    [DTT_ModTime]                    DATETIME         CONSTRAINT [DF_OP_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [MMO_ImportData]                 NTEXT            NULL,
    [CHK_Report]                     TINYINT          CONSTRAINT [DF_OP_CHK_Report] DEFAULT ((1)) NULL,
    [SI__ShareState]                 TINYINT          CONSTRAINT [DF_OP_SI__ShareState] DEFAULT ((2)) NULL,
    [MLS_Custom1]                    SMALLINT         NULL,
    [GID_CreatedBy_US]               UNIQUEIDENTIFIER NULL,
    [GID_CreditedTo_US]              UNIQUEIDENTIFIER NULL,
    [GID_For_CO]                     UNIQUEIDENTIFIER NULL,
    [GID_For_PD]                     UNIQUEIDENTIFIER NULL,
    [GID_LostTo_VE]                  UNIQUEIDENTIFIER NULL,
    [GID_OriginatedBy_CN]            UNIQUEIDENTIFIER NULL,
    [GID_Peer_US]                    UNIQUEIDENTIFIER NULL,
    [GID_Related_DV]                 UNIQUEIDENTIFIER NULL,
    [GID_Related_PR]                 UNIQUEIDENTIFIER NULL,
    [GID_From_SO]                    UNIQUEIDENTIFIER NULL,
    [GID_Related_TE]                 UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]                   VARCHAR (50)     NULL,
    [SI__Day]                        TINYINT          NULL,
    [SI__DayClose]                   TINYINT          NULL,
    [TXT_ExternalID]                 NVARCHAR (120)   NULL,
    [TXT_ExternalSource]             VARCHAR (10)     NULL,
    [TXT_ImpJobID]                   VARCHAR (20)     NULL,
    [GID_Related_LO]                 UNIQUEIDENTIFIER NULL,
    [GID_Related_AL]                 UNIQUEIDENTIFIER NULL,
    [GID_Distributor_CO]             UNIQUEIDENTIFIER NULL,
    [TXT_QuoteNo]                    NVARCHAR (20)    NULL,
    [TXT_ProjectNo]                  NVARCHAR (20)    NULL,
    [DTT_BidDueDate]                 DATETIME         NULL,
    [MLS_BidType]                    SMALLINT         NULL,
    [MLS_Plant]                      SMALLINT         NULL,
    [SR__Go]                         REAL             NULL,
    [SR__Get]                        REAL             NULL,
    [GID_RELATED_VE]                 UNIQUEIDENTIFIER NULL,
    [ADR_Attachments]                NTEXT            NULL,
    [MLS_BRIEF]                      SMALLINT         NULL,
    [TXT_OPPORTUNITYNAME]            NVARCHAR (1500)  NULL,
    [TXT_BILLINGADDRESS]             NVARCHAR (1000)  NULL,
    [TXT_BILLINGADDITIONALCOMMENTS]  NVARCHAR (1000)  NULL,
    [TXT_BILLINGCONTACTNAME]         NVARCHAR (100)   NULL,
    [TXT_BILLINGPHONE]               NVARCHAR (1000)  NULL,
    [EML_BILLINGEMAIL]               TEXT             NULL,
    [MMO_LEADTIME]                   NTEXT            NULL,
    [TXT_SHIPPINGADDRESS]            NVARCHAR (1000)  NULL,
    [TXT_PONO]                       NVARCHAR (18)    NULL,
    [TXT_ORDERNUMBER]                NVARCHAR (80)    NULL,
    [CHK_CAPEXREQUIRED]              TINYINT          NULL,
    [CUR_AMOUNT]                     MONEY            NULL,
    [CUR_REALIZEDAMOUNT]             MONEY            NULL,
    [TXT_REVISION]                   NVARCHAR (80)    NULL,
    [TXT_SHIPPINGSPECIFIEDCARRIER]   NVARCHAR (1000)  NULL,
    [TXT_SHIPPINGADDITIONALCOMMENTS] NVARCHAR (400)   NULL,
    [TXT_SHIPPINGCONTACTNAME]        NVARCHAR (100)   NULL,
    [EML_SHIPPINGEMAIL]              TEXT             NULL,
    [TEL_SHIPPINGPHONE]              NVARCHAR (80)    NULL,
    [DTT_REQUESTEDSHIPDATE]          DATETIME         NULL,
    [GID_RELATED_SO]                 UNIQUEIDENTIFIER NULL,
    [CHK_ORDERED]                    TINYINT          NULL,
    [CHK_WAITINGFORACCOUNTNAVID]     TINYINT          NULL,
    [MLS_RISKLEVEL]                  SMALLINT         NULL,
    [MLS_TYPEOFBID]                  SMALLINT         NULL,
    [MLS_LOB]                        SMALLINT         NULL,
    [MLS_SHIPPINGTERMS]              SMALLINT         NULL,
    [MLS_PAYMENTTERMS]               SMALLINT         NULL,
    [MLS_FINALDESTINATION]           SMALLINT         NULL,
    [MLS_SCOPEFITCAPABILITIES]       SMALLINT         NULL,
    [MLS_URGENCY]                    SMALLINT         NULL,
    [MLS_DECISIONMAKERENGAGEMENT]    SMALLINT         NULL,
    [MLS_TIMELINE]                   SMALLINT         NULL,
    [MLS_PRICEBOOK]                  SMALLINT         NULL,
    [MLS_SHIPFROMLOCATION]           SMALLINT         NULL,
    [MLS_OPPORTUNITYTYPE]            SMALLINT         NULL,
    [GID_ESTIMATED1_PD]              UNIQUEIDENTIFIER NULL,
    [GID_ESTIMATED2_PD]              UNIQUEIDENTIFIER NULL,
    [GID_ESTIMATED3_PD]              UNIQUEIDENTIFIER NULL,
    [SR__EstimatedQuantity1]         REAL             NULL,
    [SR__EstimatedQuantity2]         REAL             NULL,
    [SR__EstimatedQuantity3]         REAL             NULL,
    [TXT_EstimatedRegion1]           VARCHAR (500)    NULL,
    [TXT_EstimatedRegion2]           VARCHAR (500)    NULL,
    [TXT_EstimatedRegion3]           VARCHAR (500)    NULL,
    [GID_BILLING_CO]                 UNIQUEIDENTIFIER NULL,
    [GID_SHIPPING_CO]                UNIQUEIDENTIFIER NULL,
    [GID_BILLING_CN]                 UNIQUEIDENTIFIER NULL,
    [GID_SHIPPING_CN]                UNIQUEIDENTIFIER NULL,
    [MLS_Region]                     SMALLINT         NULL,
    [MLS_UNITCONDITION]              SMALLINT         NULL,
    [MLS_Company]                    SMALLINT         NULL,
    [MLS_QuotingForRentalReturn]     SMALLINT         NULL,
    [MLS_RequestedServiceLocation]   SMALLINT         NULL,
    [MMO_Questions_Comments]         NTEXT            NULL,
    [TXT_NAVOPPID]                   VARCHAR (500)    NULL,
    [DTT_PRICEVALIDITY]              DATETIME         NULL,
    [LI__MinRentalDays]              INT              NULL,
    [MLS_TYPEOFBUSINESS]             SMALLINT         NULL,
    [MMO_RentalRates]                NTEXT            NULL,
    [MLS_SHIPPINGAGENT]              SMALLINT         NULL,
    [MLS_PLACEOFDELIVERY]            SMALLINT         NULL,
    [TXT_OPPNO]                      VARCHAR (250)    NULL,
    [CUR_RENTALAMOUNT]               MONEY            NULL,
    [CUR_TOTALAMOUNT]                MONEY            NULL,
    [CUR_TOTALAMOUNTUSD]             MONEY            NULL,
    [TXT_SFOPPID]                    NVARCHAR (50)    NULL,
    [TXT_SFOPPNumber]                NVARCHAR (15)    NULL,
    [TXT_PRIMARYQUOTE]               NVARCHAR (100)   NULL,
    [GID_CONNECTED_CN]               UNIQUEIDENTIFIER NULL,
    [MLS_TYPEOFBILLING]              SMALLINT         NULL,
    [MLS_UNITTYPE]                   SMALLINT         NULL,
    [GID_QUOTEPREPAREDFOR_CN]        UNIQUEIDENTIFIER NULL,
    [MLS_ProcurementPreference]      SMALLINT         NULL,
    [GID_CLONEDFROM_OP]              UNIQUEIDENTIFIER NULL,
    [MMO_HISTORY]                    NTEXT            NULL,
    [MMO_Introduction]               NTEXT            NULL,
    [SR__ConversionRate]             REAL             NULL,
    [MMO_NotesForQuote]              NTEXT            NULL,
    [TXT_STATEBILLING]               NVARCHAR (100)   NULL,
    [TXT_ZIPBILLING]                 NVARCHAR (50)    NULL,
    [TXT_STATESHIPPING]              NVARCHAR (100)   NULL,
    [TXT_ZIPSHIPPING]                NVARCHAR (50)    NULL,
    [GID_LINKED_WL]                  UNIQUEIDENTIFIER NULL,
    [MMO_FIELDSAUDITTRAIL]           NTEXT            NULL,
    [GID_PRIMARYREASONCODE_RC]       UNIQUEIDENTIFIER NULL,
    [GID_SECONDARYREASONCODE_RC]     UNIQUEIDENTIFIER NULL,
    [CHK_TARGETGROWTH]               TINYINT          NULL,
    [MLS_CustomerBuyingPhase]        SMALLINT         NULL,
    [CUR_OppLineValue]               MONEY            NULL,
    [CUR_ExpectedValue]              MONEY            NULL,
    [CUR_WeightedExpectedValue]      MONEY            NULL,
    [CUR_WeightedOppLineValue]       MONEY            NULL,
    [CHK_Stalled]                    TINYINT          NULL,
    [DTT_LASTACTIVITYDATE]           DATETIME         NULL,
    [MLS_JOURNALTYPE]                SMALLINT         NULL,
    [MLS_SALESPROCESSSTAGE]          SMALLINT         NULL,
    [GID_RELATED_PG]                 UNIQUEIDENTIFIER NULL,
    [GID_RELATED_PD]                 UNIQUEIDENTIFIER NULL,
    [GID_RELATED_MO]                 UNIQUEIDENTIFIER NULL,
    [SR__PROFIT]                     REAL             NULL,
    [CUR_LineUnitPrice]              MONEY            NULL,
    [SR__LINEQTY]                    REAL             NULL,
    [SR__LINEGET]                    REAL             NULL,
    [MMR_LeadNotes]                  NTEXT            NULL,
    [MMR_QONotes]                    NTEXT            NULL,
    [MMR_GANotes]                    NTEXT            NULL,
    [MMR_EINotes]                    NTEXT            NULL,
    [MMR_CDNotes]                    NTEXT            NULL,
    [MMR_PPNotes]                    NTEXT            NULL,
    [MMR_CLNotes]                    NTEXT            NULL,
    [DTT_LeadDoneDate]               DATETIME         NULL,
    [DTT_QODoneDate]                 DATETIME         NULL,
    [DTT_GADoneDate]                 DATETIME         NULL,
    [DTT_EIDoneDate]                 DATETIME         NULL,
    [DTT_CDDoneDate]                 DATETIME         NULL,
    [DTT_PPDoneDate]                 DATETIME         NULL,
    [DTT_CLDoneDate]                 DATETIME         NULL,
    [GID_LEADDONEBY_US]              UNIQUEIDENTIFIER NULL,
    [GID_QODONEBY_US]                UNIQUEIDENTIFIER NULL,
    [GID_GADONEBY_US]                UNIQUEIDENTIFIER NULL,
    [GID_EIDONEBY_US]                UNIQUEIDENTIFIER NULL,
    [GID_CDDONEBY_US]                UNIQUEIDENTIFIER NULL,
    [GID_PPDONEBY_US]                UNIQUEIDENTIFIER NULL,
    [GID_CLDONEBY_US]                UNIQUEIDENTIFIER NULL,
    [SR__LeadDuration]               REAL             NULL,
    [SR__QODuration]                 REAL             NULL,
    [SR__GADuration]                 REAL             NULL,
    [SR__EIDuration]                 REAL             NULL,
    [SR__CDDuration]                 REAL             NULL,
    [SR__PPDuration]                 REAL             NULL,
    [SR__CLDuration]                 REAL             NULL,
    [CUR_TotalPipeLineValue]         MONEY            NULL,
    [DTT_LeadStartDate]              DATETIME         NULL,
    [DTT_QOStartDate]                DATETIME         NULL,
    [DTT_GAStartDate]                DATETIME         NULL,
    [DTT_EIStartDate]                DATETIME         NULL,
    [DTT_CDStartDate]                DATETIME         NULL,
    [DTT_PPStartDate]                DATETIME         NULL,
    [DTT_CLStartDate]                DATETIME         NULL,
    [CHK_LineReport]                 TINYINT          NULL,
    [CUR_PROFIT]                     MONEY            NULL,
    [CUR_PROFITPROBINDEX]            MONEY            NULL,
    [CUR_LINECOST]                   MONEY            NULL,
    [GID_RELATED_PF]                 UNIQUEIDENTIFIER NULL,
    [CHK_DIRECTQUOTE]                TINYINT          NULL,
    [GID_MERGEDTO_OP]                UNIQUEIDENTIFIER NULL,
    [GID_JCIID]                      UNIQUEIDENTIFIER NULL,
    [GID_RELATED_BC]                 UNIQUEIDENTIFIER NULL,
    [GID_RELATED_BR]                 UNIQUEIDENTIFIER NULL,
    [TXT_Segment]                    NVARCHAR (250)   NULL,
    CONSTRAINT [PK_OP] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_OP_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_CreditedTo_US] FOREIGN KEY ([GID_CreditedTo_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_For_CO] FOREIGN KEY ([GID_For_CO]) REFERENCES [dbo].[CO] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_For_PD] FOREIGN KEY ([GID_For_PD]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_From_SO] FOREIGN KEY ([GID_From_SO]) REFERENCES [dbo].[SO] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_LostTo_VE] FOREIGN KEY ([GID_LostTo_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_OriginatedBy_CN] FOREIGN KEY ([GID_OriginatedBy_CN]) REFERENCES [dbo].[CN] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_Peer_US] FOREIGN KEY ([GID_Peer_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_Related_DV] FOREIGN KEY ([GID_Related_DV]) REFERENCES [dbo].[DV] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_Related_PR] FOREIGN KEY ([GID_Related_PR]) REFERENCES [dbo].[PR] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_Related_TE] FOREIGN KEY ([GID_Related_TE]) REFERENCES [dbo].[TE] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[OP] NOCHECK CONSTRAINT [LNK_OP_CreatedBy_US];


GO
ALTER TABLE [dbo].[OP] NOCHECK CONSTRAINT [LNK_OP_CreditedTo_US];


GO
ALTER TABLE [dbo].[OP] NOCHECK CONSTRAINT [LNK_OP_For_CO];


GO
ALTER TABLE [dbo].[OP] NOCHECK CONSTRAINT [LNK_OP_For_PD];


GO
ALTER TABLE [dbo].[OP] NOCHECK CONSTRAINT [LNK_OP_From_SO];


GO
ALTER TABLE [dbo].[OP] NOCHECK CONSTRAINT [LNK_OP_LostTo_VE];


GO
ALTER TABLE [dbo].[OP] NOCHECK CONSTRAINT [LNK_OP_OriginatedBy_CN];


GO
ALTER TABLE [dbo].[OP] NOCHECK CONSTRAINT [LNK_OP_Peer_US];


GO
ALTER TABLE [dbo].[OP] NOCHECK CONSTRAINT [LNK_OP_Related_DV];


GO
ALTER TABLE [dbo].[OP] NOCHECK CONSTRAINT [LNK_OP_Related_PR];


GO
ALTER TABLE [dbo].[OP] NOCHECK CONSTRAINT [LNK_OP_Related_TE];


GO
CREATE NONCLUSTERED INDEX [IX_OP_CreationTime]
    ON [dbo].[OP]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_Related_TE]
    ON [dbo].[OP]([GID_Related_TE] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_StatusDateTimeRev]
    ON [dbo].[OP]([MLS_Status] ASC, [DTT_Time] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_Name]
    ON [dbo].[OP]([SYS_NAME] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_OP_BI__ID]
    ON [dbo].[OP]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_StatusValueRev]
    ON [dbo].[OP]([MLS_Status] ASC, [CUR_Value] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_For_CO]
    ON [dbo].[OP]([GID_For_CO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_DateTime]
    ON [dbo].[OP]([DTT_Time] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_Peer_US]
    ON [dbo].[OP]([GID_Peer_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_StatusExpClDteValRev]
    ON [dbo].[OP]([MLS_Status] ASC, [DTT_ExpCloseDate] ASC, [CUR_Value] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_For_PD]
    ON [dbo].[OP]([GID_For_PD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_LostTo_VE]
    ON [dbo].[OP]([GID_LostTo_VE] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_Related_LO]
    ON [dbo].[OP]([GID_Related_LO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_StatusPriNADteValRev]
    ON [dbo].[OP]([MLS_Status] ASC, [MLS_Priority] ASC, [DTT_NextActionDate] ASC, [CUR_Value] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_CreditedTo_US]
    ON [dbo].[OP]([GID_CreditedTo_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_OriginatedBy_CN]
    ON [dbo].[OP]([GID_OriginatedBy_CN] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_StatusPriNADteVInRev]
    ON [dbo].[OP]([MLS_Status] ASC, [MLS_Priority] ASC, [DTT_NextActionDate] ASC, [CUR_ValueIndex] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_CreatedBy_US]
    ON [dbo].[OP]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_ExpCloseDteValueRev]
    ON [dbo].[OP]([DTT_ExpCloseDate] ASC, [CUR_Value] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_TXT_ImportID]
    ON [dbo].[OP]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_Related_AL]
    ON [dbo].[OP]([GID_Related_AL] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_StatusDateClosedRev]
    ON [dbo].[OP]([MLS_Status] ASC, [DTT_DateClosed] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_StatusNADateValueRev]
    ON [dbo].[OP]([MLS_Status] ASC, [DTT_NextActionDate] ASC, [CUR_Value] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_Related_DV]
    ON [dbo].[OP]([GID_Related_DV] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_From_SO]
    ON [dbo].[OP]([GID_From_SO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_StatusValueIndRev]
    ON [dbo].[OP]([MLS_Status] ASC, [CUR_ValueIndex] DESC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_ModDateTime]
    ON [dbo].[OP]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_Related_PR]
    ON [dbo].[OP]([GID_Related_PR] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_StatusDateTime]
    ON [dbo].[OP]([MLS_Status] ASC, [DTT_Time] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_DTT_CreationTime]
    ON [dbo].[OP]([DTT_CreationTime] ASC)
    INCLUDE([GID_CreditedTo_US], [GID_For_CO]);


GO
CREATE NONCLUSTERED INDEX [IX_OP_MLS_STATUS]
    ON [dbo].[OP]([MLS_Status] ASC)
    INCLUDE([DTT_ExpCloseDate], [GID_CreditedTo_US], [GID_For_CO]);


GO
CREATE NONCLUSTERED INDEX [OP_MLSStatus_DtClosed]
    ON [dbo].[OP]([MLS_Status] ASC, [DTT_DateClosed] ASC)
    INCLUDE([GID_CreditedTo_US], [CUR_OppLineValue], [MLS_SALESPROCESSSTAGE]);


GO
CREATE NONCLUSTERED INDEX [OP_MLS_Status_CTOUS_ForCO_]
    ON [dbo].[OP]([MLS_Status] ASC)
    INCLUDE([GID_CreditedTo_US], [GID_For_CO], [CUR_ExpectedValue], [CUR_WeightedExpectedValue]);


GO
CREATE TRIGGER trOPUpdateTN
ON [OP]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in OP table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(120)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'OP'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable
					and tn2.GID_InternalID = inserted.GID_ID)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [OP]
			SET [OP].TXT_ExternalSource = '', [OP].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [OP].GID_ID = in1.GID_ID
				and ISNULL([OP].TXT_ExternalSource, '') <> ''
				and ISNULL([OP].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trOPUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trOPUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trOPUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!
GO

--select * from sys.triggers where object_id=342160917
CREATE TRIGGER [dbo].[TR_Opps] on [dbo].[OP]   
FOR UPDATE,INSERT--,DELETE      
AS       
BEGIN
--DECLARE @Table varchar(20) = 'OP'
--	Insert into A0
--	Select @Table,i.GID_ID,i.GID_CreatedBy_US,
--	FROM Inserted i
--      INNER JOIN Deleted d ON i.ID = d.ID

Declare @Table Varchar(MAX)       
Declare @PrimaryKeyColumn varchar(1000)    
    
Declare @SQL Varchar(MAX)      
Declare @ColumnList Varchar(MAX)      
Declare @ColumnList2 Varchar(MAX)      
  
--declare @userid uniqueidentifier  
--select @userid = cast(context_info() as uniqueidentifier)   
      
--- Get Parent Table Name of Trigger      
SELECT @Table = b.name+'.'+a.name      
      FROM sys.objects a      
      JOIN sys.schemas b      
            ON a.schema_id = b.schema_id      
      JOIN sys.triggers t      
            ON t.parent_id = a.object_id        
      --WHERE t.object_id = @@procid  

SELECT @PrimaryKeyColumn = cu.COLUMN_NAME     
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc join INFORMATION_SCHEMA.KEY_COLUMN_USAGE Cu on tc.CONSTRAINT_NAME = cu.CONSTRAINT_NAME    
where tc.CONSTRAINT_TYPE = 'PRIMARY KEY' and tc.TABLE_SCHEMA+'.'+tc.TABLE_NAME =@Table    
group by cu.COLUMN_NAME
--select @PrimaryKeyColumn,@Table
SELECT 
GID_ID,BI__ID,SYS_NAME,MLS_CompetingPosition,DTT_CreationTime,MLS_Currency,DTT_DateClosed,CHK_DemoData,SR__ExchRate,DTT_ExpCloseDate
,SI__Month,SI__MonthClose,DTT_NextActionDate,TXT_Description,CHK_Open,CHK_PrimaryOpp,MLS_Priority,SI__Probability,CHK_Q01,CHK_Q02
,CHK_Q03,CHK_Q04,CHK_Q05,CHK_Q06,CHK_Q07,CHK_Q08,CHK_Q09,CHK_Q10,CHK_Q11,CHK_Q12,CHK_Q13,CHK_Q14,CHK_Q15,SR__Qty,MLS_ReasonWonLost
,MLS_Stage,MLS_Status,DTT_Time,CUR_UnitValue,CUR_Value,CUR_ValueIndex,TXT_Year,TXT_YearClose,TXT_ModBy,DTT_ModTime,CHK_Report
,SI__ShareState,MLS_Custom1,GID_CreatedBy_US,GID_CreditedTo_US,GID_For_CO,GID_For_PD,GID_LostTo_VE,GID_OriginatedBy_CN,GID_Peer_US,GID_Related_DV
,GID_Related_PR,GID_From_SO,GID_Related_TE,TXT_ImportID,SI__Day,SI__DayClose,TXT_ExternalID,TXT_ExternalSource,TXT_ImpJobID,GID_Related_LO
,GID_Related_AL,GID_Distributor_CO,TXT_QuoteNo,TXT_ProjectNo,DTT_BidDueDate,MLS_BidType,MLS_Plant,SR__Go,SR__Get,GID_RELATED_VE,MLS_BRIEF
,TXT_OPPORTUNITYNAME,TXT_BILLINGADDRESS,TXT_BILLINGADDITIONALCOMMENTS,TXT_BILLINGCONTACTNAME,TXT_BILLINGPHONE,TXT_SHIPPINGADDRESS
,TXT_PONO,TXT_ORDERNUMBER,CHK_CAPEXREQUIRED,CUR_AMOUNT,CUR_REALIZEDAMOUNT,TXT_REVISION,TXT_SHIPPINGSPECIFIEDCARRIER,TXT_SHIPPINGADDITIONALCOMMENTS
,TXT_SHIPPINGCONTACTNAME,TEL_SHIPPINGPHONE,DTT_REQUESTEDSHIPDATE,GID_RELATED_SO,CHK_ORDERED,CHK_WAITINGFORACCOUNTNAVID,MLS_RISKLEVEL
,MLS_TYPEOFBID,MLS_LOB,MLS_SHIPPINGTERMS,MLS_PAYMENTTERMS,MLS_FINALDESTINATION,MLS_SCOPEFITCAPABILITIES,MLS_URGENCY,MLS_DECISIONMAKERENGAGEMENT
,MLS_TIMELINE,MLS_PRICEBOOK,MLS_SHIPFROMLOCATION,MLS_OPPORTUNITYTYPE,GID_ESTIMATED1_PD,GID_ESTIMATED2_PD,GID_ESTIMATED3_PD
,SR__EstimatedQuantity1,SR__EstimatedQuantity2,SR__EstimatedQuantity3,TXT_EstimatedRegion1,TXT_EstimatedRegion2,TXT_EstimatedRegion3,GID_BILLING_CO,GID_SHIPPING_CO
,GID_BILLING_CN,GID_SHIPPING_CN,MLS_Region,MLS_UNITCONDITION,MLS_Company,MLS_QuotingForRentalReturn,MLS_RequestedServiceLocation
,TXT_NAVOPPID,DTT_PRICEVALIDITY,LI__MinRentalDays,MLS_TYPEOFBUSINESS,MLS_SHIPPINGAGENT,MLS_PLACEOFDELIVERY,TXT_OPPNO,CUR_RENTALAMOUNT
,CUR_TOTALAMOUNT,CUR_TOTALAMOUNTUSD,TXT_SFOPPID,TXT_SFOPPNumber,TXT_PRIMARYQUOTE,GID_CONNECTED_CN,MLS_TYPEOFBILLING,MLS_UNITTYPE
,GID_QUOTEPREPAREDFOR_CN,MLS_ProcurementPreference,GID_CLONEDFROM_OP,SR__ConversionRate,TXT_STATEBILLING,TXT_ZIPBILLING
,TXT_STATESHIPPING,TXT_ZIPSHIPPING,GID_LINKED_WL
,    row_number() over(order by @PrimaryKeyColumn) AS TrigID INTO #inserted FROM INSERTED 
select * from #inserted

SELECT 
GID_ID,BI__ID,SYS_NAME,MLS_CompetingPosition,DTT_CreationTime,MLS_Currency,DTT_DateClosed,CHK_DemoData,SR__ExchRate,DTT_ExpCloseDate
,SI__Month,SI__MonthClose,DTT_NextActionDate,TXT_Description,CHK_Open,CHK_PrimaryOpp,MLS_Priority,SI__Probability,CHK_Q01,CHK_Q02
,CHK_Q03,CHK_Q04,CHK_Q05,CHK_Q06,CHK_Q07,CHK_Q08,CHK_Q09,CHK_Q10,CHK_Q11,CHK_Q12,CHK_Q13,CHK_Q14,CHK_Q15,SR__Qty,MLS_ReasonWonLost
,MLS_Stage,MLS_Status,DTT_Time,CUR_UnitValue,CUR_Value,CUR_ValueIndex,TXT_Year,TXT_YearClose,TXT_ModBy,DTT_ModTime,CHK_Report
,SI__ShareState,MLS_Custom1,GID_CreatedBy_US,GID_CreditedTo_US,GID_For_CO,GID_For_PD,GID_LostTo_VE,GID_OriginatedBy_CN,GID_Peer_US,GID_Related_DV
,GID_Related_PR,GID_From_SO,GID_Related_TE,TXT_ImportID,SI__Day,SI__DayClose,TXT_ExternalID,TXT_ExternalSource,TXT_ImpJobID,GID_Related_LO
,GID_Related_AL,GID_Distributor_CO,TXT_QuoteNo,TXT_ProjectNo,DTT_BidDueDate,MLS_BidType,MLS_Plant,SR__Go,SR__Get,GID_RELATED_VE,MLS_BRIEF
,TXT_OPPORTUNITYNAME,TXT_BILLINGADDRESS,TXT_BILLINGADDITIONALCOMMENTS,TXT_BILLINGCONTACTNAME,TXT_BILLINGPHONE,TXT_SHIPPINGADDRESS
,TXT_PONO,TXT_ORDERNUMBER,CHK_CAPEXREQUIRED,CUR_AMOUNT,CUR_REALIZEDAMOUNT,TXT_REVISION,TXT_SHIPPINGSPECIFIEDCARRIER,TXT_SHIPPINGADDITIONALCOMMENTS
,TXT_SHIPPINGCONTACTNAME,TEL_SHIPPINGPHONE,DTT_REQUESTEDSHIPDATE,GID_RELATED_SO,CHK_ORDERED,CHK_WAITINGFORACCOUNTNAVID,MLS_RISKLEVEL
,MLS_TYPEOFBID,MLS_LOB,MLS_SHIPPINGTERMS,MLS_PAYMENTTERMS,MLS_FINALDESTINATION,MLS_SCOPEFITCAPABILITIES,MLS_URGENCY,MLS_DECISIONMAKERENGAGEMENT
,MLS_TIMELINE,MLS_PRICEBOOK,MLS_SHIPFROMLOCATION,MLS_OPPORTUNITYTYPE,GID_ESTIMATED1_PD,GID_ESTIMATED2_PD,GID_ESTIMATED3_PD
,SR__EstimatedQuantity1,SR__EstimatedQuantity2,SR__EstimatedQuantity3,TXT_EstimatedRegion1,TXT_EstimatedRegion2,TXT_EstimatedRegion3,GID_BILLING_CO,GID_SHIPPING_CO
,GID_BILLING_CN,GID_SHIPPING_CN,MLS_Region,MLS_UNITCONDITION,MLS_Company,MLS_QuotingForRentalReturn,MLS_RequestedServiceLocation
,TXT_NAVOPPID,DTT_PRICEVALIDITY,LI__MinRentalDays,MLS_TYPEOFBUSINESS,MLS_SHIPPINGAGENT,MLS_PLACEOFDELIVERY,TXT_OPPNO,CUR_RENTALAMOUNT
,CUR_TOTALAMOUNT,CUR_TOTALAMOUNTUSD,TXT_SFOPPID,TXT_SFOPPNumber,TXT_PRIMARYQUOTE,GID_CONNECTED_CN,MLS_TYPEOFBILLING,MLS_UNITTYPE
,GID_QUOTEPREPAREDFOR_CN,MLS_ProcurementPreference,GID_CLONEDFROM_OP,SR__ConversionRate,TXT_STATEBILLING,TXT_ZIPBILLING
,TXT_STATESHIPPING,TXT_ZIPSHIPPING,GID_LINKED_WL
,    row_number() over(order by @PrimaryKeyColumn) AS TrigID INTO #deleted  FROM deleted 
--select * from #deleted
Select @ColumnList = COALESCE(@ColumnList+',','') + 'ISNULL(Cast([' + COLUMN_NAME + '] AS Varchar(MAX)),''XXXX-------XXXX'') as [' + COLUMN_NAME + ']'      
            from INFORMATION_SCHEMA.COLUMNS where TABLE_SCHEMA+'.'+TABLE_NAME = @Table      
      
Select @ColumnList2 = COALESCE(@ColumnList2+',','') + '[' + COLUMN_NAME + ']'      
            from INFORMATION_SCHEMA.COLUMNS where TABLE_SCHEMA+'.'+TABLE_NAME = @Table   	  

Create Table #InsertedPiv (TrigID INT,EntityID varchar(1000), ColName Varchar(200),ColVal Varchar(MAX))      
Create Table #DeletedPiv  (TrigID INT,EntityID varchar(1000), ColName Varchar(200),ColVal Varchar(MAX))      
  
---- If Inserted          
IF exists (SELECT 1 FROM #INSERTED) and not exists (SELECT 1 FROM #deleted)      
BEGIN      
SET @SQL = 'SELECT      TrigID,EntityID,     ColName, NULLIF(ColVal , ''XXXX-------XXXX'' )      
      FROM (SELECT TrigID, '+@PrimaryKeyColumn+' as EntityID,'  + @ColumnList + '  
      FROM #inserted) as sourceT      
      UNPIVOT      
      (ColVal FOR ColName IN ( ['+ @PrimaryKeyColumn +'],GID_CreatedBy_US )) PT     '      
END  
  
ELSE  
BEGIN  
SET @SQL = 'SELECT      TrigID,EntityID,     ColName, NULLIF(ColVal , ''XXXX-------XXXX'' )      
      FROM (SELECT TrigID, '+@PrimaryKeyColumn+' as EntityID, '  + @ColumnList + '      
      FROM #inserted) as sourceT      
      UNPIVOT      
      (ColVal FOR ColName IN ( '+ @ColumnList2 +' )) PT     '     
END                  
  
INSERT into #InsertedPiv      
EXEC(@SQL)              
  
SET @SQL = 'SELECT      TrigID, EntityID, ColName, NULLIF(ColVal , ''XXXX-------XXXX'' )      
      FROM (SELECT TrigID, '+@PrimaryKeyColumn+' as EntityID,'  + @ColumnList + '       
      FROM #deleted) as sourceT      
      UNPIVOT      
      (ColVal FOR ColName IN ( '+ @ColumnList2 +' )) PT     '      
                  
INSERT into #DeletedPiv      
EXEC(@SQL)              
  
------------Updates      
      
IF exists (SELECT 1 FROM #INSERTED) and exists (SELECT 1 FROM #deleted)      
BEGIN      
      select * from #Update1
      SELECT a.ColName, a.[PrevVal], a.[NewVal], b.Colval [ModifiedBy],a.EntityID      
            INTO #Update1      
      FROM (      
            SELECT      a.ColName,  b.colval [PrevVal],     a.colval [NewVal], a.TrigID,a.EntityID     
            FROM #InsertedPiv a      
            JOIN #DeletedPiv b      
       ON a.ColName = b.ColName      
                  AND a.TrigID = b.TrigID      
                  and a.EntityID=b.EntityID    
            WHERE ISNULL(a.colval, 'NULL') <> ISNULL(b.colval, 'NULL') ) a      
      JOIN #InsertedPiv b      
            ON a.TrigID = b.TrigID      
            --and b.ColName = 'ModifiedBy'
			
INSERT INTO A0 (Txt_Filename,Gid_recordID,Gid_Changedone_US,Txt_fieldName,Txt_Oldvalue,Txt_Newvalue,DTT_Changeddate)
      SELECT      @Table,EntityID,GID_CreatedBy_US, ColName, [PrevVal], [NewVal], GETUTCDATE()
            FROM #Update1      
                  
END

-------------Inserts      
      
IF exists (SELECT 1 FROM #INSERTED) and not exists (SELECT 1 FROM #deleted)      
BEGIN      
      
      SELECT a.ColName, a.ColVal [NewVal],a.EntityID,b.Colval GID_CreatedBy_US  
            INTO #INSERT1      
      FROM #InsertedPiv a      
      JOIN #InsertedPiv b      
            ON a.TrigID = b.TrigID 
            and a.EntityID=b.EntityID    
            AND b.ColName = ',GID_CreatedBy_US'      
            AND a.ColName NOT IN (',GID_CreatedBy_US')--,'ModifiedBy')      
      --select * from #INSERT1
      INSERT INTO A0 (Txt_Filename,Gid_recordID,Gid_Changedone_US,Txt_fieldName,Txt_Oldvalue,Txt_Newvalue,DTT_Changeddate)
      SELECT      @Table,EntityID,GID_CreatedBy_US, ColName, Null, [NewVal], GETUTCDATE()     
            FROM #INSERT1      
                  
END
END


GO
DISABLE TRIGGER [dbo].[TR_Opps]
    ON [dbo].[OP];

