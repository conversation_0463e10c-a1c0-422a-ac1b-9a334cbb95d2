﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{0718C809-A336-45B1-9029-253AF6E1FC66}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Selltis.BusinessLogic</RootNamespace>
    <AssemblyName>Selltis.BusinessLogic</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineConstants>TRACE;DEBUG;Windows</DefineConstants>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>Selltis.BusinessLogic.xml</DocumentationFile>
    <UseVSHostingProcess>true</UseVSHostingProcess>
    <CheckForOverflowUnderflow>true</CheckForOverflowUnderflow>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineConstants>TRACE;Windows</DefineConstants>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>Selltis.BusinessLogic.xml</DocumentationFile>
    <CheckForOverflowUnderflow>true</CheckForOverflowUnderflow>
  </PropertyGroup>
  <PropertyGroup>
  </PropertyGroup>
  <PropertyGroup>
  </PropertyGroup>
  <PropertyGroup>
  </PropertyGroup>
  <PropertyGroup>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DebugNew|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <DefineConstants>TRACE;DEBUG;Windows</DefineConstants>
    <OutputPath>bin\DebugNew\</OutputPath>
    <DocumentationFile>Selltis.BusinessLogic.xml</DocumentationFile>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CheckForOverflowUnderflow>true</CheckForOverflowUnderflow>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="DocumentFormat.OpenXml">
      <HintPath>..\Common\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="ephtmltopdf, Version=6.2.0.0, Culture=neutral, PublicKeyToken=5b5f377bc08a4d32, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\CommonDLLs\ephtmltopdf.dll</HintPath>
    </Reference>
    <Reference Include="Google.GData.Client">
      <HintPath>..\packages\CommonDLLs\Google.GData.Client.dll</HintPath>
    </Reference>
    <Reference Include="IMAP4.Net">
      <HintPath>..\packages\CommonDLLs\IMAP4.Net.dll</HintPath>
    </Reference>
    <Reference Include="ImapUtility">
      <HintPath>..\packages\CommonDLLs\ImapUtility.dll</HintPath>
    </Reference>
    <Reference Include="MailBee.NET">
      <HintPath>..\packages\CommonDLLs\MailBee.NET.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Configuration, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\CommonDLLs\Microsoft.WindowsAzure.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage, Version=4.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\CommonDLLs\Microsoft.WindowsAzure.Storage.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.StorageClient, Version=1.7.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll</HintPath>
    </Reference>
    <Reference Include="POP3.Net">
      <HintPath>..\packages\CommonDLLs\POP3.Net.dll</HintPath>
    </Reference>
    <Reference Include="PublicDomain">
      <HintPath>..\packages\CommonDLLs\PublicDomain.dll</HintPath>
    </Reference>
    <Reference Include="Quickwebsoft.Web.UI.WebControls.EventCalendar">
      <HintPath>..\packages\CommonDLLs\Quickwebsoft.Web.UI.WebControls.EventCalendar.dll</HintPath>
    </Reference>
    <Reference Include="SautinSoft.Document">
      <HintPath>..\Common\SautinSoft.Document.dll</HintPath>
    </Reference>
    <Reference Include="SMTP.Net">
      <HintPath>..\packages\CommonDLLs\SMTP.Net.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Mvc">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Telerik.Windows.Documents.Core">
      <HintPath>..\Common\telerik DLLs\Telerik.Windows.Documents.Core.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Documents.Fixed">
      <HintPath>..\Common\telerik DLLs\Telerik.Windows.Documents.Fixed.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Documents.Flow">
      <HintPath>..\Common\telerik DLLs\Telerik.Windows.Documents.Flow.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Documents.Flow.FormatProviders.Pdf">
      <HintPath>..\Common\telerik DLLs\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Zip">
      <HintPath>..\Common\telerik DLLs\Telerik.Windows.Zip.dll</HintPath>
    </Reference>
    <Reference Include="UltimateAjax">
      <HintPath>..\packages\CommonDLLs\UltimateAjax.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Using Include="Microsoft.VisualBasic" />
    <Using Include="System" />
    <Using Include="System.Collections" />
    <Using Include="System.Collections.Generic" />
    <Using Include="System.Data" />
    <Using Include="System.Diagnostics" />
    <Using Include="System.Linq" />
    <Using Include="System.Xml.Linq" />
    <Using Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="TangibleNumericHelper.cs" />
    <Compile Include="TangibleDateHelper.cs" />
    <Compile Include="clArray.cs" />
    <Compile Include="clAttachments.cs" />
    <Compile Include="clAuto.cs" />
    <Compile Include="clAutomatorMenu.cs" />
    <Compile Include="clAzureFileStorage.cs" />
    <Compile Include="clC.cs" />
    <Compile Include="clData.cs" />
    <Compile Include="clDefaults.cs" />
    <Compile Include="clDiaAdmPerm.cs" />
    <Compile Include="clDiaFormPro.cs" />
    <Compile Include="clEmail.cs" />
    <Compile Include="clEmailAlerts.cs" />
    <Compile Include="clEncryption.cs" />
    <Compile Include="clError.cs" />
    <Compile Include="clFileBrowse.cs" />
    <Compile Include="clGoogleAuth.cs" />
    <Compile Include="clHistory.cs" />
    <Compile Include="clHistoryItem.cs" />
    <Compile Include="clImport.cs" />
    <Compile Include="clImportTasks.cs" />
    <Compile Include="clInfoMessage.cs" />
    <Compile Include="clInit.cs" />
    <Compile Include="clList.cs" />
    <Compile Include="clLog.cs" />
    <Compile Include="clLogic.cs" />
    <Compile Include="clMembershipData.cs" />
    <Compile Include="clMetaData.cs" />
    <Compile Include="clNumMask.cs" />
    <Compile Include="clPDF.cs" />
    <Compile Include="clPerm.cs" />
    <Compile Include="clProject.cs" />
    <Compile Include="clRowSet.cs" />
    <Compile Include="clSchema.cs" />
    <Compile Include="clScriptsRowSet.cs" />
    <Compile Include="clScrMngRowSet.cs" />
    <Compile Include="clSelltisMembershipProvider.cs" />
    <Compile Include="clSelltisMembershipProviderNew.cs" />
    <Compile Include="clSelltisMobileBase.cs" />
    <Compile Include="clSend.cs" />
    <Compile Include="clSettings.cs" />
    <Compile Include="clSQL.cs" />
    <Compile Include="clTable.cs" />
    <Compile Include="clTransform.cs" />
    <Compile Include="ClUI.cs" />
    <Compile Include="clUtil.cs" />
    <Compile Include="clVar.cs" />
    <Compile Include="clWebForm.cs" />
    <Compile Include="clWebMail.cs" />
    <Compile Include="clWorkareaMessage.cs" />
    <Compile Include="cus_clScriptsCustom.cs" />
    <Compile Include="cus_clScriptsRowSetCustom.cs" />
    <Compile Include="cus_clSelltisMobileCustom.cs" />
    <Compile Include="EfcOnlineApi.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SelltisCache.cs" />
    <Compile Include="WordToPDF.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>