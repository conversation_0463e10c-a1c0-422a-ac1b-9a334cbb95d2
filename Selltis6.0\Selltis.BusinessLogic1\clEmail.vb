Imports Microsoft.VisualBasic
Imports Quiksoft.EasyMail.SMTP
Imports System.Net.Mail
Imports System.Web
Imports System.IO
Imports System.Text.RegularExpressions
Imports System.Drawing

'OWNER: RH

Public Class clEmail


    Dim goP As clProject
    Dim goMeta As clMetaData
    Dim goTR As clTransform
    Dim goData As clData
    Dim goErr As clError
    Dim goLog As clLog
    Dim goScr As clScrMngRowSet
    Dim sSourceID As String



    Public Sub New()

        goP = HttpContext.Current.Session("goP")
        goTR = HttpContext.Current.Session("goTr")
        goMeta = HttpContext.Current.Session("goMeta")
        goData = HttpContext.Current.Session("goData")
        goErr = HttpContext.Current.Session("goErr")
        goLog = HttpContext.Current.Session("goLog")
        goScr = HttpContext.Current.Session("goScr")

    End Sub

    Function LogMessage(ByVal dt As DataTable) As String

        Dim sProc As String = "clEmail::LogMessage"

        LogMessage = ""

        '  Try

        Dim sComp As String
            Dim sCont As String
            Dim sLink As String
            Dim sAdd() As String
            Dim l As Integer
            Dim sComps As String = ""
            Dim sConts As String = ""
            Dim sMemo As String = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "", True)


            Dim iShare As Integer


            Dim objAddRowSet As New clRowSet("AC", 2)
            objAddRowSet.bBypassValidation = True

            objAddRowSet.SetFieldVal("MMO_HISTORY", PrepHistory() & " " & goP.GetUserCode & _
            " E-mail Logged" & vbCrLf & "To: " & GetVal(dt, "To") & vbCrLf & "cc: " & GetVal(dt, "CC") & _
             vbCrLf & "From: " & GetVal(dt, "From"))

            objAddRowSet.SetFieldVal("CHK_EXTDEVREVIEW", 1, clC.SELL_SYSTEM)

            objAddRowSet.SetFieldVal("LNK_From_SO", GetSourceVal())
            objAddRowSet.SetFieldVal("DTE_STARTTIME", GetVal(dt, "Date"))
            objAddRowSet.SetFieldVal("DTE_ENDTIME", GetVal(dt, "Date"))
            objAddRowSet.SetFieldVal("TME_STARTTIME", GetVal(dt, "Time"))
            objAddRowSet.SetFieldVal("TME_ENDTIME", GetVal(dt, "Time"))
            objAddRowSet.SetFieldVal("LNK_CreatedBy_US", goP.gsUserID)

            If dt.Columns.Contains("LinkRelatedField1") And dt.Columns.Contains("LinkRelatedValue1") Then
                Dim sColumn As String = GetVal(dt, "LinkRelatedField1")
                Dim sValue As String = GetVal(dt, "LinkRelatedValue1")

                If String.IsNullOrEmpty(sColumn) = False And String.IsNullOrEmpty(sValue) = False Then
                    objAddRowSet.SetFieldVal(sColumn, sValue)
                    'VS 06222016
                    AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"))
                End If
            End If

            If dt.Columns.Contains("LinkRelatedField2") And dt.Columns.Contains("LinkRelatedValue2") Then
                Dim sColumn As String = GetVal(dt, "LinkRelatedField2")
                Dim sValue As String = GetVal(dt, "LinkRelatedValue2")

                If String.IsNullOrEmpty(sColumn) = False And String.IsNullOrEmpty(sValue) = False Then
                    objAddRowSet.SetFieldVal(sColumn, sValue)
                    'VS 06222016
                    AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"))
                End If
            End If

            If dt.Columns.Contains("LinkRelatedField3") And dt.Columns.Contains("LinkRelatedValue3") Then
                Dim sColumn As String = GetVal(dt, "LinkRelatedField3")
                Dim sValue As String = GetVal(dt, "LinkRelatedValue3")

                If String.IsNullOrEmpty(sColumn) = False And String.IsNullOrEmpty(sValue) = False Then
                    objAddRowSet.SetFieldVal(sColumn, sValue)
                    'VS 06222016
                    AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"))
                End If
            End If

            If dt.Columns.Contains("LinkRelatedField4") And dt.Columns.Contains("LinkRelatedValue4") Then
                Dim sColumn As String = GetVal(dt, "LinkRelatedField4")
                Dim sValue As String = GetVal(dt, "LinkRelatedValue4")

                If String.IsNullOrEmpty(sColumn) = False And String.IsNullOrEmpty(sValue) = False Then
                    objAddRowSet.SetFieldVal(sColumn, sValue)
                    'VS 06222016
                    AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"))
                End If
            End If

            'changes from 5.5 24/3/17
            Dim sType As String = GetVal(dt, "Type")
            If sType = "Sent" Then
                objAddRowSet.SetFieldVal("MLS_TYPE", 6, clC.SELL_SYSTEM)
            ElseIf sType = "Received" Then
                objAddRowSet.SetFieldVal("MLS_TYPE", 5, clC.SELL_SYSTEM)
            Else
                objAddRowSet.SetFieldVal("MLS_TYPE", sType, clC.SELL_FRIENDLY)
            End If

            '08022018:Tckt#2357 Change - Email log type when activity log is selected in outlook..J
            'Change type in sas70.selltis.com when 'From' contains sas domain and 'To' does not contain.
            'Checking CC to differentiate multiple sites not sas70.selltis.com
            '29102018 tckt #2518: AC log type revert back change..J
            'If (GetVal(dt, "From").Contains("@stresshq.com") And GetVal(dt, "CC").Contains("@stresshq.com")) And (Not GetVal(dt, "To").Contains("@stresshq.com")) Then
            '    objAddRowSet.SetFieldVal("MLS_TYPE", 6, clC.SELL_SYSTEM) 'Sent
            'Else
            '    If sType = "Sent" Then
            '        objAddRowSet.SetFieldVal("MLS_TYPE", 6, clC.SELL_SYSTEM)  'Sent
            '    ElseIf sType = "Received" Then
            '        objAddRowSet.SetFieldVal("MLS_TYPE", 5, clC.SELL_SYSTEM)  'Received
            '    Else
            '        objAddRowSet.SetFieldVal("MLS_TYPE", sType, clC.SELL_FRIENDLY)
            '    End If
            'End If

            objAddRowSet.SetFieldVal("MMO_NOTES", GetVal(dt, "Subject"))
            objAddRowSet.SetFieldVal("FIL_ATTACHMENTS", GetVal(dt, "Attachments"))
            objAddRowSet.SetFieldVal("TXT_SUBJ", Left(GetVal(dt, "Subject"), 80))
            objAddRowSet.SetFieldVal("MMO_LETTER", "From: " & GetVal(dt, "From") & _
            vbCrLf & "Received: " & GetVal(dt, "Date") & " " & GetVal(dt, "Time") & _
            vbCrLf & "Subject: " & GetVal(dt, "Subject") & vbCrLf & vbCrLf & GetVal(dt, "Body"))

            If GetVal(dt, "Type") = "Sent" Then
                objAddRowSet.SetFieldVal("EML_EMAIL", GetVal(dt, "To"))
                objAddRowSet.SetFieldVal("CHK_SENT", 1, clC.SELL_SYSTEM)
                objAddRowSet.SetFieldVal("MLS_STATUS", 1, clC.SELL_SYSTEM)
            Else
                objAddRowSet.SetFieldVal("EML_EMAIL", GetVal(dt, "From"))
                'VS 02152017 : Getting value from Workgroup options
                Dim sStatus As String = goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "LOG_EMAIL_STATUS_DEFAULT", "0", False, "")
                objAddRowSet.SetFieldVal("MLS_STATUS", sStatus, clC.SELL_SYSTEM)
                'objAddRowSet.SetFieldVal("MLS_STATUS", 0, clC.SELL_SYSTEM)
            End If


            '


            'Email sent
            If GetVal(dt, "Type") = "Sent" Then

                iShare = CInt(goTR.StrRead(sMemo, "EMAILSHARESENT", "1"))
                objAddRowSet.SetFieldVal("SI__SHARESTATE", iShare)


                objAddRowSet.SetFieldVal("MMO_LETTER", "From: " & GetVal(dt, "From") & _
                        vbCrLf & "Sent: " & GetVal(dt, "Date") & " " & GetVal(dt, "Time") & _
                        vbCrLf & "Subject: " & GetVal(dt, "Subject") & vbCrLf & vbCrLf & GetVal(dt, "Body"))

                sLink = ""
                sComp = ""
                sCont = ""

                sAdd = Split(GetVal(dt, "To"), "; ")
                For l = 0 To UBound(sAdd)
                    GetEmailAliasLinks(sAdd(l), sLink, sComp, sCont)
                    If sComp <> "" Then objAddRowSet.SetLinkVal("LNK_Related_CO", sComp)
                    If sCont <> "" Then objAddRowSet.SetLinkVal("LNK_Related_CN", sCont)
                Next

                sLink = ""
                sComp = ""
                sCont = ""
                sComps = ""
                sConts = ""

                sAdd = Split(GetVal(dt, "CC"), "; ")
                For l = 0 To UBound(sAdd)
                    GetEmailAliasLinks(sAdd(l), sLink, sComp, sCont)
                    If sComp <> "" Then objAddRowSet.SetLinkVal("LNK_Related_CO", sComp)
                    If sCont <> "" Then objAddRowSet.SetLinkVal("LNK_cc_CN", sCont)
                Next

                sLink = ""
                sComp = ""
                sCont = ""
                sComps = ""
                sConts = ""

                sAdd = Split(GetVal(dt, "BCC"), "; ")
                For l = 0 To UBound(sAdd)
                    GetEmailAliasLinks(sAdd(l), sLink, sComp, sCont)
                    If sComp <> "" Then objAddRowSet.SetLinkVal("LNK_Related_CO", sComp)
                    If sCont <> "" Then objAddRowSet.SetLinkVal("LNK_bc_CN", sCont)
                Next

                'Email received
            Else

                iShare = CInt(goTR.StrRead(sMemo, "EMAILSHARERECEIVED", "1"))
                objAddRowSet.SetFieldVal("SI__SHARESTATE", iShare)

                objAddRowSet.SetFieldVal("MMO_LETTER", "From: " & GetVal(dt, "From") & _
                            vbCrLf & "Received: " & GetVal(dt, "Date") & " " & GetVal(dt, "Time") & _
                            vbCrLf & "Subject: " & GetVal(dt, "Subject") & vbCrLf & vbCrLf & GetVal(dt, "Body"))

                sLink = "" : sComp = "" : sCont = ""
                GetEmailAliasLinks(GetVal(dt, "From"), sLink, sComp, sCont)
                If sLink <> "" Then objAddRowSet.SetLinkVal("LNK_Related_EL", sLink)
                If sCont <> "" Then objAddRowSet.SetLinkVal("LNK_Related_CN", sCont)
                If sComp <> "" Then objAddRowSet.SetLinkVal("LNK_Related_CO", sComp)

                'tkt #1872 new feature:To add all cc individuals and allowing to reply to all when log mail..J
                sLink = ""
                sComp = ""
                sCont = ""
                sComps = ""
                sConts = ""
                sAdd = Split(GetVal(dt, "CC"), "; ")
                For l = 0 To UBound(sAdd)
                    GetEmailAliasLinks(sAdd(l), sLink, sComp, sCont)
                    If sComp <> "" Then objAddRowSet.SetLinkVal("LNK_Related_CO", sComp)
                    If sCont <> "" Then objAddRowSet.SetLinkVal("LNK_cc_CN", sCont)
                Next

            End If


            Return objAddRowSet.Commit().ToString

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try


    End Function

    'S_B Tkt#2216 New Log mail function for Attachments
    Function LogMessage_With_Attachments(ByVal dt As DataTable, ByVal dtAttachments As DataTable, Optional ByVal hostName As String = "") As String

        Dim sProc As String = "clEmail::LogMessage"

        LogMessage_With_Attachments = ""

        '  Try

        Dim sComp As String
        Dim sCont As String
        Dim sLink As String
        Dim sAdd() As String
        Dim l As Integer
        Dim sComps As String = ""
        Dim sConts As String = ""
        Dim sMemo As String = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "", True)


        Dim iShare As Integer


        Dim objAddRowSet As New clRowSet("AC", 2)
        objAddRowSet.bBypassValidation = True

        objAddRowSet.SetFieldVal("MMO_HISTORY", PrepHistory() & " " & goP.GetUserCode &
            " E-mail Logged" & vbCrLf & "To: " & GetVal(dt, "To") & vbCrLf & "cc: " & GetVal(dt, "CC") &
             vbCrLf & "From: " & GetVal(dt, "From"))

        objAddRowSet.SetFieldVal("CHK_EXTDEVREVIEW", 1, clC.SELL_SYSTEM)

        objAddRowSet.SetFieldVal("LNK_From_SO", GetSourceVal())
        objAddRowSet.SetFieldVal("DTE_STARTTIME", GetVal(dt, "Date"))
        objAddRowSet.SetFieldVal("DTE_ENDTIME", GetVal(dt, "Date"))
        objAddRowSet.SetFieldVal("TME_STARTTIME", GetVal(dt, "Time"))
        objAddRowSet.SetFieldVal("TME_ENDTIME", GetVal(dt, "Time"))
        objAddRowSet.SetFieldVal("LNK_CreatedBy_US", goP.gsUserID)

        If dt.Columns.Contains("LinkRelatedField1") And dt.Columns.Contains("LinkRelatedValue1") Then
            Dim sColumn As String = GetVal(dt, "LinkRelatedField1")
            Dim sValue As String = GetVal(dt, "LinkRelatedValue1")

            If String.IsNullOrEmpty(sColumn) = False And String.IsNullOrEmpty(sValue) = False Then
                objAddRowSet.SetFieldVal(sColumn, sValue)
                'VS 06222016
                AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"))
            End If
        End If

        If dt.Columns.Contains("LinkRelatedField2") And dt.Columns.Contains("LinkRelatedValue2") Then
            Dim sColumn As String = GetVal(dt, "LinkRelatedField2")
            Dim sValue As String = GetVal(dt, "LinkRelatedValue2")

            If String.IsNullOrEmpty(sColumn) = False And String.IsNullOrEmpty(sValue) = False Then
                objAddRowSet.SetFieldVal(sColumn, sValue)
                'VS 06222016
                AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"))
            End If
        End If

        If dt.Columns.Contains("LinkRelatedField3") And dt.Columns.Contains("LinkRelatedValue3") Then
            Dim sColumn As String = GetVal(dt, "LinkRelatedField3")
            Dim sValue As String = GetVal(dt, "LinkRelatedValue3")

            If String.IsNullOrEmpty(sColumn) = False And String.IsNullOrEmpty(sValue) = False Then
                objAddRowSet.SetFieldVal(sColumn, sValue)
                'VS 06222016
                AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"))
            End If
        End If

        If dt.Columns.Contains("LinkRelatedField4") And dt.Columns.Contains("LinkRelatedValue4") Then
            Dim sColumn As String = GetVal(dt, "LinkRelatedField4")
            Dim sValue As String = GetVal(dt, "LinkRelatedValue4")

            If String.IsNullOrEmpty(sColumn) = False And String.IsNullOrEmpty(sValue) = False Then
                objAddRowSet.SetFieldVal(sColumn, sValue)
                'VS 06222016
                AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"))
            End If
        End If

        'changes from 5.5 24/3/17
        Dim sType As String = GetVal(dt, "Type")
        If sType = "Sent" Then
            objAddRowSet.SetFieldVal("MLS_TYPE", 6, clC.SELL_SYSTEM)
        ElseIf sType = "Received" Then
            objAddRowSet.SetFieldVal("MLS_TYPE", 5, clC.SELL_SYSTEM)
        Else
            objAddRowSet.SetFieldVal("MLS_TYPE", sType, clC.SELL_FRIENDLY)
        End If

        '08022018:Tckt#2357 Change - Email log type when activity log is selected in outlook..J
        'Change type in sas70.selltis.com when 'From' contains sas domain and 'To' does not contain.
        'Checking CC to differentiate multiple sites not sas70.selltis.com
        '29102018 tckt #2518: AC log type revert back change..J
        'If (GetVal(dt, "From").Contains("@stresshq.com") And GetVal(dt, "CC").Contains("@stresshq.com")) And (Not GetVal(dt, "To").Contains("@stresshq.com")) Then
        '    objAddRowSet.SetFieldVal("MLS_TYPE", 6, clC.SELL_SYSTEM) 'Sent
        'Else
        'If sType = "Sent" Then
        '    objAddRowSet.SetFieldVal("MLS_TYPE", 6, clC.SELL_SYSTEM)  'Sent
        'ElseIf sType = "Received" Then
        '    objAddRowSet.SetFieldVal("MLS_TYPE", 5, clC.SELL_SYSTEM)  'Received
        'Else
        '    objAddRowSet.SetFieldVal("MLS_TYPE", sType, clC.SELL_FRIENDLY)
        'End If
        'End If


        objAddRowSet.SetFieldVal("MMO_NOTES", GetVal(dt, "Subject"))
        'objAddRowSet.SetFieldVal("FIL_ATTACHMENTS", GetVal(dt, "Attachments"))
        objAddRowSet.SetFieldVal("TXT_SUBJ", Left(GetVal(dt, "Subject"), 80))
        objAddRowSet.SetFieldVal("MMO_LETTER", "From: " & GetVal(dt, "From") &
            vbCrLf & "Received: " & GetVal(dt, "Date") & " " & GetVal(dt, "Time") &
            vbCrLf & "Subject: " & GetVal(dt, "Subject") & vbCrLf & vbCrLf & GetVal(dt, "Body"))

        If GetVal(dt, "Type") = "Sent" Then
            objAddRowSet.SetFieldVal("EML_EMAIL", GetVal(dt, "To"))
            objAddRowSet.SetFieldVal("CHK_SENT", 1, clC.SELL_SYSTEM)
            objAddRowSet.SetFieldVal("MLS_STATUS", 1, clC.SELL_SYSTEM)
        Else
            objAddRowSet.SetFieldVal("EML_EMAIL", GetVal(dt, "From"))
            'VS 02152017 : Getting value from Workgroup options
            Dim sStatus As String = goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "LOG_EMAIL_STATUS_DEFAULT", "0", False, "")
            objAddRowSet.SetFieldVal("MLS_STATUS", sStatus, clC.SELL_SYSTEM)
            'objAddRowSet.SetFieldVal("MLS_STATUS", 0, clC.SELL_SYSTEM)
        End If


        '


        'Email sent
        If GetVal(dt, "Type") = "Sent" Then

            iShare = CInt(goTR.StrRead(sMemo, "EMAILSHARESENT", "1"))
            objAddRowSet.SetFieldVal("SI__SHARESTATE", iShare)


            objAddRowSet.SetFieldVal("MMO_LETTER", "To: " & GetVal(dt, "To") &
                        vbCrLf & "CC: " & GetVal(dt, "CC") &
                        vbCrLf & "From: " & GetVal(dt, "From") &
                        vbCrLf & "Sent: " & GetVal(dt, "Date") & " " & GetVal(dt, "Time") &
                        vbCrLf & "Subject: " & GetVal(dt, "Subject") & vbCrLf & vbCrLf & GetVal(dt, "Body"))

            sLink = ""
            sComp = ""
            sCont = ""

            sAdd = Split(GetVal(dt, "To"), "; ")
            For l = 0 To UBound(sAdd)
                GetEmailAliasLinks(sAdd(l), sLink, sComp, sCont)
                If sComp <> "" Then objAddRowSet.SetLinkVal("LNK_Related_CO", sComp)
                If sCont <> "" Then objAddRowSet.SetLinkVal("LNK_Related_CN", sCont)
            Next

            sLink = ""
            sComp = ""
            sCont = ""
            sComps = ""
            sConts = ""

            sAdd = Split(GetVal(dt, "CC"), "; ")
            For l = 0 To UBound(sAdd)
                GetEmailAliasLinks(sAdd(l), sLink, sComp, sCont)
                If sComp <> "" Then objAddRowSet.SetLinkVal("LNK_Related_CO", sComp)
                If sCont <> "" Then objAddRowSet.SetLinkVal("LNK_cc_CN", sCont)
            Next

            sLink = ""
            sComp = ""
            sCont = ""
            sComps = ""
            sConts = ""

            sAdd = Split(GetVal(dt, "BCC"), "; ")
            For l = 0 To UBound(sAdd)
                GetEmailAliasLinks(sAdd(l), sLink, sComp, sCont)
                If sComp <> "" Then objAddRowSet.SetLinkVal("LNK_Related_CO", sComp)
                If sCont <> "" Then objAddRowSet.SetLinkVal("LNK_bc_CN", sCont)
            Next

            'Email received
        Else

            iShare = CInt(goTR.StrRead(sMemo, "EMAILSHARERECEIVED", "1"))
            objAddRowSet.SetFieldVal("SI__SHARESTATE", iShare)

            objAddRowSet.SetFieldVal("MMO_LETTER", "To: " & GetVal(dt, "To") &
                            vbCrLf & "CC: " & GetVal(dt, "CC") &
                            vbCrLf & "From: " & GetVal(dt, "From") &
                            vbCrLf & "Received: " & GetVal(dt, "Date") & " " & GetVal(dt, "Time") &
                            vbCrLf & "Subject: " & GetVal(dt, "Subject") & vbCrLf & vbCrLf & GetVal(dt, "Body"))

            sLink = "" : sComp = "" : sCont = ""
            GetEmailAliasLinks(GetVal(dt, "From"), sLink, sComp, sCont)
            If sLink <> "" Then objAddRowSet.SetLinkVal("LNK_Related_EL", sLink)
            If sCont <> "" Then objAddRowSet.SetLinkVal("LNK_Related_CN", sCont)
            If sComp <> "" Then objAddRowSet.SetLinkVal("LNK_Related_CO", sComp)

            'tkt #1872 new feature:To add all cc individuals and allowing to reply to all when log mail..J
            sLink = ""
            sComp = ""
            sCont = ""
            sComps = ""
            sConts = ""
            sAdd = Split(GetVal(dt, "CC"), "; ")
            For l = 0 To UBound(sAdd)
                GetEmailAliasLinks(sAdd(l), sLink, sComp, sCont)
                If sComp <> "" Then objAddRowSet.SetLinkVal("LNK_Related_CO", sComp)
                If sCont <> "" Then objAddRowSet.SetLinkVal("LNK_cc_CN", sCont)
            Next

        End If


        'Return objAddRowSet.Commit().ToString
        Dim retVal As String = objAddRowSet.Commit().ToString

        If retVal = "1" Then
            Dim sGID_ID As String = objAddRowSet.oDataSet.Tables(0).Rows(0)("GID_ID").ToString()
            Dim sViewName As String = "AC"
            Dim sFieldName As String = "ADR_ATTACHMENTS"

            Dim objclAttachments As New clAttachments()

            If dtAttachments.Rows.Count > 0 Then
                For Each row As DataRow In dtAttachments.Rows
                    Dim FileName As String = row("FileName").ToString
                    Dim sFileData As String = row("FileStream").ToString

                    Dim sUploadStatus As String = objclAttachments.UploadAttachment(sViewName, sGID_ID, sFieldName, FileName, sFileData, hostName)
                Next
            End If
            Return retVal
        End If

        Return retVal

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try


    End Function

    Function AddEmailtoLNKRecJournal(ByVal sLinkedRecordID As String, ByVal sSubject As String, ByVal sBody As String, ByVal sAttachments As String) As Boolean
        'VS 06222016
        'Purpose : Add Email as journal entry to the linked record with subject and First 250 chars of Body.
        'VS 07212016: Include Attachments also.

        If sLinkedRecordID = "" Then Return True

        'Check if Email Journaling has been disabled in WOP_WORKGROUP_OPTIONS 
        If goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "LOGANDLINKJOURNAL_ENABLED", "1") = 0 Then Return True

        Dim sFile As String = ""
        Dim sJournEntry As String = ""
        Dim sJournal As String = ""
        Dim sDateStamp As String = ""
        Dim doRS As clRowSet

        sFile = goTR.GetFileFromSUID(sLinkedRecordID)
        If sFile = "" Then Return True

        If Not goData.IsFieldValid(sFile, "MMO_JOURNAL") Then Return True

        doRS = New clRowSet(sFile, clC.SELL_EDIT, "GID_ID='" & sLinkedRecordID & "'", , , , , , , , , True, True)

        If doRS.GetFirst = 1 Then
            goScr.RunScript("GetDateTimeStampRecord", doRS, , "NEUTRAL", , "CODE", "USERNOOFFSETLABEL", , sDateStamp) 'returns var sDateStamp
            'sJournEntry = sDateStamp & " " & sSubject & vbCrLf & Left(sBody.Trim, 250)
            sJournEntry = sDateStamp & " " & sSubject & vbCrLf & sBody
            sJournal = doRS.GetFieldVal("MMO_JOURNAL")
            sJournal = sJournEntry & vbCrLf & sJournal
            doRS.SetFieldVal("MMO_JOURNAL", sJournal)
            If goData.IsFieldValid(sFile, "FIL_ATTACHMENTS") Then doRS.SetFieldVal("FIL_ATTACHMENTS", sAttachments)
            If doRS.Commit() = 0 Then
                Return False
            End If
        End If

        Return True
    End Function

    Function GetVal(ByVal par_dt As DataTable, ByVal par_sField As String) As String

        Dim sProc As String = "clEmail::GetVal"

        GetVal = ""

        'Try

        If par_dt.Rows(0).Item(par_sField).GetType.ToString = "System.DBNull" Then
                GetVal = ""
            Else
                GetVal = par_dt.Rows(0).Item(par_sField)
            End If

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try

    End Function

    Public Function PrepHistory() As String
        Dim sProc As String = "clEmail::PrepHistory"

        '2003-11-04 08:33

        PrepHistory = goTR.DateTimeToString(goTR.NowUTC)

    End Function

    Public Function GetSourceVal() As String

        Dim sProc As String = "clEmail::GetSourceVal"

        GetSourceVal = ""

        On Error GoTo ErrorHandler

        If sSourceID = "" Then
            Dim oRS As New clRowSet("SO", 3, "TXT_SOURCENAME='E-MAIL'", , "GID_ID")
            If oRS.Count = 1 Then
                GetSourceVal = oRS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY)
                sSourceID = GetSourceVal
            End If

        Else
            GetSourceVal = sSourceID
        End If

        Exit Function

ErrorHandler:

        GetSourceVal = ""

    End Function

    Public Sub GetEmailAliasLinks(ByVal strEmail As String, ByRef sLink As String, ByRef sCompany As String, ByRef sContact As String)

        Dim sProc As String = "clEmail::GetEmailAliasLinks"

        sCompany = ""
        sContact = ""
        sLink = ""

        If Not String.IsNullOrEmpty(Trim(strEmail)) Then

            strEmail = goTR.PrepareForSQL(strEmail)

            Dim objCNRowSet As New clRowSet("CN", 3, "EML_EMAIL='" & Left(strEmail, 100) & "' ", "DTT_CreationTime desc", "**", 1)

            If objCNRowSet.GetFirst() = 1 Then

                sLink = ""
                sCompany = objCNRowSet.GetFieldVal("LNK_Related_CO")
                sContact = objCNRowSet.GetFieldVal("GID_ID")
                'If objCNRowSet.GetNext() = 1 Then
                '    sLink = ""
                '    sCompany = ""
                '    sContact = ""
                'End If

                objCNRowSet = Nothing

                'Else

                '    Dim objRowSet As New clRowSet("EL", 3, "TXT_EmailAddress='" & Left(strEmail, 100) & "' and CHK_DONOTAUTOCONNECT=0", "", "**")

                '    If objRowSet.GetFirst() = 1 Then

                '        sLink = objRowSet.GetFieldVal("GID_ID")
                '        sCompany = objRowSet.GetFieldVal("LNK_Related_CO")
                '        sContact = objRowSet.GetFieldVal("LNK_Related_CN")
                '        If objRowSet.GetNext() = 1 Then
                '            sLink = ""
                '            sCompany = ""
                '            sContact = ""
                '        End If
                '    End If

                '    objRowSet = Nothing

            End If

        End If

        ' Try
        'Dim objRowSet As New clRowSet("EL", 3, "TXT_EmailAddress='" & Left(strEmail, 100) & "' and CHK_DONOTAUTOCONNECT=0", "", "**")

        '    If objRowSet.GetFirst() = 1 Then

        '        sLink = objRowSet.GetFieldVal("GID_ID")
        '        sCompany = objRowSet.GetFieldVal("LNK_Related_CO")
        '        sContact = objRowSet.GetFieldVal("LNK_Related_CN")
        '        If objRowSet.GetNext() = 1 Then
        '            sLink = ""
        '            sCompany = ""
        '            sContact = ""
        '        End If
        '    End If

        '    objRowSet = Nothing

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try


    End Sub

    Public Function SendSMTPEmail(ByVal par_sSubject As String, _
                                ByVal par_sBody As String, _
                                ByVal par_sTo As String, _
                                ByVal par_sCC As String, _
                                ByVal par_sBCC As String, _
                                ByVal par_sUnused As String, _
                                Optional ByVal par_ReplyName As String = "", _
                                Optional ByVal par_ReplyAddress As String = "", _
                                Optional ByVal sFiles As String = "", _
                                Optional ByVal bHTML As Boolean = False, _
                                Optional ByVal sHTMLURI As String = "", _
                                Optional ByVal sHTMLURIBase As String = "", _
                                Optional ByVal bDeleteFiles As Boolean = True, _
                                Optional ByVal bAppendTextFromWOP As Boolean = True) As Boolean
        'MI 2/1/12 Added bAppendTextFromWOP.
        'MI 4/12/11 Expanded message in Catch SMTPProtocolExcep As SMTPProtocolException.
        'MI 3/9/07 Added SetWarnings, streamlined returning result.
        'VS 05022017 hardCoding From <NAME_EMAIL> and ReplyTo to par_ReplyAddress

        Dim sProc As String = "clEmail::SendSMTPEmail"

        '21/06/2017 7.0 Changes..J
        Return SendSMTPEmailNew(par_sSubject, par_sBody, par_sTo, par_sCC, par_sBCC, par_sUnused, par_ReplyName, par_ReplyAddress, sFiles, bHTML, sHTMLURI, sHTMLURIBase, bDeleteFiles, bAppendTextFromWOP)

        'Dim msgObj As New Quiksoft.EasyMail.SMTP.EmailMessage
        'Dim smtpObj As New Quiksoft.EasyMail.SMTP.SMTP
        'Dim sAttach(0) As String
        'Dim sTo(0) As String
        'Dim sCC(0) As String
        'Dim sBC(0) As String
        'Dim f As Integer
        'Dim bResult As Boolean = True

        'Try

        '    Quiksoft.EasyMail.SMTP.License.Key = "Selltis, LLC (Single Developer)/6899051F129703729F3CD444C1B003"

        '    msgObj.Subject = par_sSubject


        '    'Add TOs
        '    par_sTo = goTR.Replace(par_sTo, ",", ";")
        '    If par_sTo <> "" Then
        '        sTo = Split(par_sTo, ";")
        '        For f = 0 To sTo.GetUpperBound(0)
        '            If Trim(sTo(f)) <> "" Then
        '                msgObj.Recipients.Add(Trim(sTo(f)), "", RecipientType.To)
        '            End If
        '        Next
        '    End If

        '    'Add CCs
        '    par_sCC = goTR.Replace(par_sCC, ",", ";")
        '    If par_sCC <> "" Then
        '        sCC = Split(par_sCC, ";")
        '        For f = 0 To sCC.GetUpperBound(0)
        '            If Trim(sCC(f)) <> "" Then
        '                msgObj.Recipients.Add(Trim(sCC(f)), "", RecipientType.CC)
        '            End If
        '        Next
        '    End If

        '    'Add BCCs
        '    par_sBCC = goTR.Replace(par_sBCC, ",", ";")
        '    If par_sBCC <> "" Then
        '        sBC = Split(par_sBCC, ";")
        '        For f = 0 To sBC.GetUpperBound(0)
        '            If Trim(sBC(f)) <> "" Then
        '                msgObj.Recipients.Add(Trim(sBC(f)), "", RecipientType.BCC)
        '            End If
        '        Next
        '    End If

        '    'Specify the sender
        '    'VS 05022017 : Set from <NAME_EMAIL>
        '    'msgObj.From.Email = par_ReplyAddress
        '    msgObj.From.Email = "<EMAIL>"
        '    msgObj.From.Name = par_ReplyName = System.Configuration.ConfigurationManager.AppSettings("smtpSender")

        '    'VS 05022017 : set reply email
        '    msgObj.ReplyTo = par_ReplyAddress

        '    'Set message body

        '    If bAppendTextFromWOP Then      '2/1/12 MI Added testing this to be able to send an email without the WOP text appended
        '        par_sBody = par_sBody & vbCrLf & vbCrLf & goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "EMAILAPPEND", "", False)
        '    End If

        '    If bHTML = False Then
        '        msgObj.BodyParts.Add(par_sBody)
        '    Else
        '        If sHTMLURI <> "" Then
        '            msgObj.ImportFromURI(sHTMLURI, sHTMLURIBase, ImportURIFlags.EmbedImages)
        '        Else
        '            msgObj.BodyParts.Add(par_sBody, BodyPartFormat.HTML)
        '        End If
        '    End If


        '    'Add attachment
        '    If sFiles <> "" Then
        '        sAttach = Split(sFiles, vbCrLf)

        '        For f = 0 To sAttach.GetUpperBound(0)
        '            If sAttach(f) <> "" Then
        '                msgObj.Attachments.Add(sAttach(f))
        '            End If
        '        Next
        '    End If

        '    'Set mail server
        '    Dim oServer As New Quiksoft.EasyMail.SMTP.SMTPServer

        '    'oServer.AuthMode = SMTPAuthMode.CramMD5
        '    'oServer.Name = "mail.selltis.com"
        '    'oServer.Account = "sellsmtp"
        '    'oServer.Password = "smtpking"
        '    ''oServer.Port = 12225
        '    'oServer.Port = 587

        '    Dim smtpServer As String = System.Configuration.ConfigurationManager.AppSettings("smtpServer")
        '    If String.IsNullOrEmpty(smtpServer) Then
        '        smtpServer = "mail.selltis.com"
        '    Else
        '        smtpServer = smtpServer.ToString()
        '    End If

        '    Dim smtpPort As String = System.Configuration.ConfigurationManager.AppSettings("smtpPort")
        '    If String.IsNullOrEmpty(smtpPort) Then
        '        smtpPort = 587
        '    Else
        '        smtpPort = Convert.ToInt32(smtpPort)
        '    End If

        '    Dim smtpUser As String = System.Configuration.ConfigurationManager.AppSettings("smtpUser")
        '    If String.IsNullOrEmpty(smtpUser) Then
        '        smtpUser = "sellsmtp"
        '    Else
        '        smtpUser = smtpUser.ToString()
        '    End If

        '    Dim smtpPassword As String = System.Configuration.ConfigurationManager.AppSettings("smtpPassword")
        '    If String.IsNullOrEmpty(smtpPassword) Then
        '        smtpPassword = "smtpking"
        '    Else
        '        smtpPassword = smtpPassword.ToString()
        '    End If

        '    oServer.AuthMode = SMTPAuthMode.CramMD5
        '    oServer.Name = smtpServer
        '    oServer.Account = smtpUser
        '    oServer.Password = smtpPassword
        '    'oServer.Port = 12225
        '    oServer.Port = smtpPort

        '    smtpObj.SMTPServers.Add(oServer)

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If

        'End Try


        'Try
        '    'Send the message
        '    smtpObj.Send(msgObj)
        '    bResult = True

        'Catch LicenseExcep As LicenseException
        '    'MsgBox("License key error: " + LicenseExcep.Message)
        '    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed: the license appears to be invalid.")
        '    bResult = False
        'Catch FileIOExcep As FileIOException
        '    'MsgBox("File IO error: " + FileIOExcep.Message)
        '    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed due to 'FileIOException'.")
        '    bResult = False
        'Catch SMTPAuthExcep As SMTPAuthenticationException
        '    'MsgBox("SMTP Authentication error: " + SMTPAuthExcep.Message)
        '    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed: the user can't be authenticated.")
        '    bResult = False
        'Catch SMTPConnectExcep As SMTPConnectionException
        '    'Debug.Print("Connection error: " + SMTPConnectExcep.Message & "  " & SMTPConnectExcep.ErrorCode.ToString)
        '    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed: connection can't be established.")
        '    bResult = False
        'Catch SMTPProtocolExcep As SMTPProtocolException
        '    'MsgBox("SMTP protocol error: " + SMTPProtocolExcep.Message)
        '    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed due to an unspecified SMTP Protocol Exception. The recipient's address may be invalid.")
        '    bResult = False
        'End Try

        ''Clean up temporary attachments unless there was an error above

        'If bDeleteFiles Then
        '    If bResult Then
        '        Try
        '            For f = 0 To sAttach.GetUpperBound(0)
        '                If sAttach(f) <> "" Then
        '                    My.Computer.FileSystem.DeleteFile(sAttach(f), FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
        '                End If
        '            Next

        '            If sHTMLURI <> "" Then
        '                My.Computer.FileSystem.DeleteFile(sHTMLURI, FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
        '            End If
        '        Catch ex As Exception
        '            If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '                goErr.SetError(ex, 45105, sProc)
        '            End If
        '        End Try
        '    End If
        'End If


        'Return bResult

    End Function


    Public Function SendSMTPEmailCustom(ByVal par_sSubject As String, _
                                ByVal par_sBody As String, _
                                ByVal par_sTo As String, _
                                ByVal par_sCC As String, _
                                ByVal par_sBCC As String, _
                                ByVal par_sUnused As String, _
                                Optional ByVal par_ReplyName As String = "", _
                                Optional ByVal par_ReplyAddress As String = "", _
                                Optional ByVal sFiles As String = "", _
                                Optional ByVal bHTML As Boolean = False, _
                                Optional ByVal sHTMLURI As String = "", _
                                Optional ByVal sHTMLURIBase As String = "", _
                                Optional ByVal bDeleteFiles As Boolean = True, _
                                Optional ByVal bAppendTextFromWOP As Boolean = True) As Boolean
        'MI 2/1/12 Added bAppendTextFromWOP.
        'MI 4/12/11 Expanded message in Catch SMTPProtocolExcep As SMTPProtocolException.
        'MI 3/9/07 Added SetWarnings, streamlined returning result.
        'VS 05022017 hardCoding From <NAME_EMAIL> and ReplyTo to par_ReplyAddress

        Dim sProc As String = "clEmail::SendSMTPEmail"

        '09/07/2018 Send email with sender from email..J
        Return SendSMTPEmailNewCustom(par_sSubject, par_sBody, par_sTo, par_sCC, par_sBCC, par_sUnused, par_ReplyName, par_ReplyAddress, sFiles, bHTML, sHTMLURI, sHTMLURIBase, bDeleteFiles, bAppendTextFromWOP)

    End Function

    Public Function SendSMTPEmailHTMLWithEmbeddedImages(ByVal par_sSubject As String, _
                                     ByVal par_sBody As String, _
                                    ByVal par_sTo As String, _
                                    ByVal par_sCC As String, _
                                    ByVal par_sBCC As String, _
                                    ByVal par_sAttachments As String, _
                                    Optional ByVal par_ReplyName As String = "", _
                                    Optional ByVal par_ReplyAddress As String = "", _
                                    Optional ByVal sFiles As String = "", _
                                    Optional ByVal bHTML As Boolean = False, _
                                    Optional ByVal sHTMLURI As String = "", _
                                    Optional ByVal sHTMLURIBase As String = "", _
                                    Optional ByVal bAppendTextFromWOP As Boolean = True) As Boolean

        'MI 2/1/12 Added bAppendTextFromWOP.


        Dim sProc As String = "clEmail::SendSMTPEmailHTMLWithEmbeddedImages"

        '21/06/2017 7.0 Changes..J
        Return SendSMTPEmailNew(par_sSubject, par_sBody, par_sTo, par_sCC, par_sBCC, par_sAttachments, par_ReplyName, par_ReplyAddress, sFiles, bHTML, sHTMLURI, sHTMLURIBase, True, bAppendTextFromWOP)

        'Dim msgObj As New Quiksoft.EasyMail.SMTP.EmailMessage
        'Dim smtpObj As New Quiksoft.EasyMail.SMTP.SMTP
        'Dim sAttach(0) As String
        'Dim sTo(0) As String
        'Dim sCC(0) As String
        'Dim sBC(0) As String
        'Dim f As Integer
        'Dim bResult As Boolean = True



        'Try

        '    Quiksoft.EasyMail.SMTP.License.Key = "Selltis, LLC (Single Developer)/6899051F129703729F3CD444C1B003"

        '    msgObj.Subject = par_sSubject


        '    'Add TOs
        '    par_sTo = goTR.Replace(par_sTo, ",", ";")
        '    If par_sTo <> "" Then
        '        sTo = Split(par_sTo, ";")
        '        For f = 0 To sTo.GetUpperBound(0)
        '            If Trim(sTo(f)) <> "" Then

        '                msgObj.Recipients.Add(Trim(sTo(f)), "", RecipientType.To)
        '            End If
        '        Next
        '    End If

        '    'Add CCs
        '    par_sCC = goTR.Replace(par_sCC, ",", ";")
        '    If par_sCC <> "" Then
        '        sCC = Split(par_sCC, ";")
        '        For f = 0 To sCC.GetUpperBound(0)
        '            If Trim(sCC(f)) <> "" Then
        '                msgObj.Recipients.Add(Trim(sCC(f)), "", RecipientType.CC)
        '            End If
        '        Next
        '    End If

        '    'Add BCCs
        '    par_sBCC = goTR.Replace(par_sBCC, ",", ";")
        '    If par_sBCC <> "" Then
        '        sBC = Split(par_sBCC, ";")
        '        For f = 0 To sBC.GetUpperBound(0)
        '            If Trim(sBC(f)) <> "" Then
        '                msgObj.Recipients.Add(Trim(sBC(f)), "", RecipientType.BCC)
        '            End If
        '        Next
        '    End If

        '    'Specify the sender
        '    msgObj.From.Email = par_ReplyAddress
        '    msgObj.From.Name = par_ReplyName = System.Configuration.ConfigurationManager.AppSettings("smtpSender")

        '    'Set message body

        '    If bAppendTextFromWOP Then      '2/1/12 MI Added testing this to be able to send an email without the WOP text appended
        '        par_sBody = par_sBody & vbCrLf & vbCrLf & goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "EMAILAPPEND", "", False)
        '    End If

        '    If bHTML = False Then
        '        msgObj.BodyParts.Add(par_sBody)
        '    Else
        '        msgObj.BodyParts.Add(par_sBody, BodyPartFormat.HTML)

        '    End If


        '    'Add attachment
        '    If sFiles <> "" Then
        '        sAttach = Split(sFiles, vbCrLf)

        '        For f = 0 To sAttach.GetUpperBound(0)
        '            If sAttach(f) <> "" Then
        '                msgObj.Attachments.Add(sAttach(f))
        '            End If
        '        Next
        '    End If

        '    'Set mail server
        '    Dim oServer As New Quiksoft.EasyMail.SMTP.SMTPServer

        '    'oServer.AuthMode = SMTPAuthMode.CramMD5
        '    'oServer.Name = "mail.selltis.com"
        '    'oServer.Account = "sellsmtp"
        '    'oServer.Password = "smtpking"
        '    ''oServer.Port = 12225
        '    'oServer.Port = 587

        '    Dim smtpServer As String = System.Configuration.ConfigurationManager.AppSettings("smtpServer")
        '    If String.IsNullOrEmpty(smtpServer) Then
        '        smtpServer = "mail.selltis.com"
        '    Else
        '        smtpServer = smtpServer.ToString()
        '    End If

        '    Dim smtpPort As String = System.Configuration.ConfigurationManager.AppSettings("smtpPort")
        '    If String.IsNullOrEmpty(smtpPort) Then
        '        smtpPort = 587
        '    Else
        '        smtpPort = Convert.ToInt32(smtpPort)
        '    End If

        '    Dim smtpUser As String = System.Configuration.ConfigurationManager.AppSettings("smtpUser")
        '    If String.IsNullOrEmpty(smtpUser) Then
        '        smtpUser = "sellsmtp"
        '    Else
        '        smtpUser = smtpUser.ToString()
        '    End If

        '    Dim smtpPassword As String = System.Configuration.ConfigurationManager.AppSettings("smtpPassword")
        '    If String.IsNullOrEmpty(smtpPassword) Then
        '        smtpPassword = "smtpking"
        '    Else
        '        smtpPassword = smtpPassword.ToString()
        '    End If

        '    oServer.AuthMode = SMTPAuthMode.CramMD5
        '    oServer.Name = smtpServer
        '    oServer.Account = smtpUser
        '    oServer.Password = smtpPassword
        '    'oServer.Port = 12225
        '    oServer.Port = smtpPort

        '    smtpObj.SMTPServers.Add(oServer)

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If

        'End Try


        'Try
        '    'Send the message
        '    smtpObj.Send(msgObj)

        'Catch LicenseExcep As LicenseException
        '    'MsgBox("License key error: " + LicenseExcep.Message)
        '    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed: the license appears to be invalid.")
        '    bResult = False
        'Catch FileIOExcep As FileIOException
        '    'MsgBox("File IO error: " + FileIOExcep.Message)
        '    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed due to 'FileIOException'.")
        '    bResult = False
        'Catch SMTPAuthExcep As SMTPAuthenticationException
        '    'MsgBox("SMTP Authentication error: " + SMTPAuthExcep.Message)
        '    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed: the user can't be authenticated.")
        '    bResult = False
        'Catch SMTPConnectExcep As SMTPConnectionException
        '    'Debug.Print("Connection error: " + SMTPConnectExcep.Message & "  " & SMTPConnectExcep.ErrorCode.ToString)
        '    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed: connection can't be established.")
        '    bResult = False
        'Catch SMTPProtocolExcep As SMTPProtocolException
        '    'MsgBox("SMTP protocol error: " + SMTPProtocolExcep.Message)
        '    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed due to 'SMTPProtocolException'.")
        '    bResult = False
        'End Try

        ''Clean up temporary attachments unless there was an error above
        'If bResult Then
        '    Try
        '        For f = 0 To sAttach.GetUpperBound(0)
        '            If sAttach(f) <> "" Then
        '                My.Computer.FileSystem.DeleteFile(sAttach(f), FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
        '            End If
        '        Next
        '    Catch ex As Exception
        '        If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '            goErr.SetError(ex, 45105, sProc)
        '        End If
        '    End Try
        'End If

        'Return bResult

    End Function

    Public Function SendSMTPEmailNew(ByVal par_sSubject As String,
                                    ByVal par_sBody As String,
                                    ByVal par_sTo As String,
                                    ByVal par_sCC As String,
                                    ByVal par_sBCC As String,
                                    ByVal par_sUnused As String,
                                    Optional ByVal par_ReplyName As String = "",
                                    Optional ByVal par_ReplyAddress As String = "",
                                    Optional ByVal sFiles As String = "",
                                    Optional ByVal bHTML As Boolean = False,
                                    Optional ByVal sHTMLURI As String = "",
                                    Optional ByVal sHTMLURIBase As String = "",
                                    Optional ByVal bDeleteFiles As Boolean = True,
                                    Optional ByVal bAppendTextFromWOP As Boolean = True) As Boolean

        Dim sProc As String = "clEmail::SendSMTPEmailNew"
        Dim client As New SmtpClient()
        Dim message As New MailMessage()
        Dim sAttach(0) As String
        Dim sTo(0) As String
        Dim sCC(0) As String
        Dim sBC(0) As String
        Dim f As Integer
        Dim bResult As Boolean = True

        'Try

        'Add TOs
        par_sTo = goTR.Replace(par_sTo, ",", ";")
        If par_sTo <> "" Then
            sTo = Split(par_sTo, ";")
            For f = 0 To sTo.GetUpperBound(0)
                If Trim(sTo(f)) <> "" Then
                    message.To.Add(Trim(sTo(f)))
                End If
            Next
        End If

        'Add CCs
        par_sCC = goTR.Replace(par_sCC, ",", ";")
        If par_sCC <> "" Then
            sCC = Split(par_sCC, ";")
            For f = 0 To sCC.GetUpperBound(0)
                If Trim(sCC(f)) <> "" Then
                    message.CC.Add(Trim(sCC(f)))
                End If
            Next
        End If

        'Add BCCs
        par_sBCC = goTR.Replace(par_sBCC, ",", ";")
        If par_sBCC <> "" Then
            sBC = Split(par_sBCC, ";")
            For f = 0 To sBC.GetUpperBound(0)
                If Trim(sBC(f)) <> "" Then
                    message.Bcc.Add(Trim(sBC(f)))
                End If
            Next
        End If

        'Specify the sender
        'msgObj.From.Email = par_ReplyAddress
        'msgObj.From.Name = par_ReplyName    

        '21092018 tickt #2463: Sending activty reports is not working..J
        'This is not wokrking on production due to display name format..J  
        Dim strDisplayName As String = par_ReplyName
        par_ReplyName = System.Configuration.ConfigurationManager.AppSettings("smtpSender")
        If Not String.IsNullOrEmpty(par_ReplyName) Then
            'message.From = New MailAddress(par_ReplyName, par_ReplyAddress)
            message.From = New MailAddress(par_ReplyName, strDisplayName)
        End If

        message.Priority = MailPriority.Normal

        message.Subject = par_sSubject

        'Set message body

        If bAppendTextFromWOP Then      '2/1/12 MI Added testing this to be able to send an email without the WOP text appended
            par_sBody = par_sBody & vbCrLf & vbCrLf & goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "EMAILAPPEND", "", False)
        End If

        If bHTML = False Then
            message.Body = par_sBody
            message.IsBodyHtml = False
        Else
            'If sHTMLURI <> "" Then
            '    msgObj.ImportFromURI(sHTMLURI, sHTMLURIBase, ImportURIFlags.EmbedImages)
            'Else
            '    msgObj.BodyParts.Add(par_sBody, BodyPartFormat.HTML)
            'End If
            message.Body = par_sBody
            message.IsBodyHtml = True
        End If

        'Add attachment
        If sFiles <> "" Then
            sAttach = Split(sFiles, vbCrLf)
            For f = 0 To sAttach.GetUpperBound(0)
                If sAttach(f) <> "" Then
                    'msgObj.Attachments.Add(sAttach(f))
                    Dim attachment As System.Net.Mail.Attachment = New System.Net.Mail.Attachment(sAttach(f), System.Net.Mime.MediaTypeNames.Application.Octet)
                    message.Attachments.Add(attachment)

                End If
            Next
        End If

        'Set mail server
        'client.Host = "mail.selltis.com"
        'client.Port = 587
        'client.Credentials = New System.Net.NetworkCredential("sellsmtp", "smtpking")

        Dim smtpServer As String = System.Configuration.ConfigurationManager.AppSettings("smtpServer")
        If String.IsNullOrEmpty(smtpServer) Then
            smtpServer = "mail.selltis.com"
        Else
            smtpServer = smtpServer.ToString()
        End If

        Dim smtpPort As String = System.Configuration.ConfigurationManager.AppSettings("smtpPort")
        If String.IsNullOrEmpty(smtpPort) Then
            smtpPort = 587
        Else
            smtpPort = Convert.ToInt32(smtpPort)
        End If

        Dim smtpUser As String = System.Configuration.ConfigurationManager.AppSettings("smtpUser")
        If String.IsNullOrEmpty(smtpUser) Then
            smtpUser = "sellsmtp"
        Else
            smtpUser = smtpUser.ToString()
        End If

        Dim smtpPassword As String = System.Configuration.ConfigurationManager.AppSettings("smtpPassword")
        If String.IsNullOrEmpty(smtpPassword) Then
            smtpPassword = "smtpking"
        Else
            smtpPassword = smtpPassword.ToString()
        End If

        'Set mail server
        client.Host = smtpServer
        client.Port = smtpPort
        client.Credentials = New System.Net.NetworkCredential(smtpUser, smtpPassword)
        client.EnableSsl = True

        client.Send(message)
        bResult = True

        'Catch ex As Exception
        '    If ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetWarning(45105, sProc, ex.ToString())
        '    End If
        'End Try

        'Clean up temporary attachments unless there was an error above
        If bDeleteFiles Then
            If bResult Then
                ' Try
                For f = 0 To sAttach.GetUpperBound(0)
                    If sAttach(f) <> "" Then
                        My.Computer.FileSystem.DeleteFile(sAttach(f), FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
                    End If
                Next

                If sHTMLURI <> "" Then
                    My.Computer.FileSystem.DeleteFile(sHTMLURI, FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
                End If
                'Catch ex As Exception
                '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
                '        goErr.SetError(ex, 45105, sProc)
                '    End If
                'End Try
            End If
        End If

        Return bResult

    End Function

    Public Function SendSMTPEmail_With_Attachment(ByVal par_sSubject As String,
                                    ByVal par_sBody As String,
                                    ByVal par_sTo As String,
                                    ByVal par_sCC As String,
                                    ByVal par_sBCC As String,
                                    ByVal par_sUnused As String,
                                    Optional ByVal par_ReplyName As String = "",
                                    Optional ByVal par_ReplyAddress As String = "",
                                    Optional ByVal sAttachments As String = "",
                                    Optional ByVal bHTML As Boolean = False,
                                    Optional ByVal sHTMLURI As String = "",
                                    Optional ByVal sHTMLURIBase As String = "",
                                    Optional ByVal bDeleteFiles As Boolean = True,
                                    Optional ByVal bAppendTextFromWOP As Boolean = True) As Boolean

        Dim sProc As String = "clEmail::SendSMTPEmailNew"
        Dim client As New SmtpClient()
        Dim message As New MailMessage()
        Dim sAttach(0) As String
        Dim sTo(0) As String
        Dim sCC(0) As String
        Dim sBC(0) As String
        Dim f As Integer
        Dim bResult As Boolean = True

        Try

            'Add TOs
            par_sTo = goTR.Replace(par_sTo, ",", ";")
            If par_sTo <> "" Then
                sTo = Split(par_sTo, ";")
                For f = 0 To sTo.GetUpperBound(0)
                    If Trim(sTo(f)) <> "" Then
                        message.To.Add(Trim(sTo(f)))
                    End If
                Next
            End If

            'Add CCs
            par_sCC = goTR.Replace(par_sCC, ",", ";")
            If par_sCC <> "" Then
                sCC = Split(par_sCC, ";")
                For f = 0 To sCC.GetUpperBound(0)
                    If Trim(sCC(f)) <> "" Then
                        message.CC.Add(Trim(sCC(f)))
                    End If
                Next
            End If

            'Add BCCs
            par_sBCC = goTR.Replace(par_sBCC, ",", ";")
            If par_sBCC <> "" Then
                sBC = Split(par_sBCC, ";")
                For f = 0 To sBC.GetUpperBound(0)
                    If Trim(sBC(f)) <> "" Then
                        message.Bcc.Add(Trim(sBC(f)))
                    End If
                Next
            End If

            'Specify the sender
            'msgObj.From.Email = par_ReplyAddress
            'msgObj.From.Name = par_ReplyName    

            '21092018 tickt #2463: Sending activty reports is not working..J
            'This is not wokrking on production due to display name format..J  
            Dim strDisplayName As String = par_ReplyName
            par_ReplyName = System.Configuration.ConfigurationManager.AppSettings("smtpSender")
            If Not String.IsNullOrEmpty(par_ReplyName) Then
                'message.From = New MailAddress(par_ReplyAddress, strDisplayName)
                message.From = New MailAddress(par_ReplyName, strDisplayName)
            End If

            If Not String.IsNullOrEmpty(par_ReplyAddress) Then
                message.ReplyToList.Add(par_ReplyAddress)
            End If

            message.Priority = MailPriority.Normal

            message.Subject = par_sSubject

            'Set message body

            If bAppendTextFromWOP Then      '2/1/12 MI Added testing this to be able to send an email without the WOP text appended
                par_sBody = par_sBody & vbCrLf & vbCrLf & goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "EMAILAPPEND", "", False)
            End If

            If bHTML = False Then
                message.Body = par_sBody
                message.IsBodyHtml = False
            Else
                'If sHTMLURI <> "" Then
                '    msgObj.ImportFromURI(sHTMLURI, sHTMLURIBase, ImportURIFlags.EmbedImages)
                'Else
                '    msgObj.BodyParts.Add(par_sBody, BodyPartFormat.HTML)
                'End If
                message.Body = par_sBody
                message.IsBodyHtml = True
            End If

            'Add attachment

            ''sViewName + "|" + sFieldName + "|" + sFileName + "|" + sGid_id
            If sAttachments <> "" Then
                Dim sFiles = Split(sAttachments, "|")
                Dim sViewName As String = sFiles.GetValue(0)
                Dim sFieldName As String = sFiles.GetValue(1)
                Dim sFileName As String = sFiles.GetValue(2)
                Dim sGid_id As String = sFiles.GetValue(3)

                Dim ct As System.Net.Mime.ContentType = New System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Application.Pdf)
                Dim _fileArray = clAzureFileStorage.GetFile(sViewName, sGid_id, sFieldName, sFileName)
                Dim attachment As System.Net.Mail.Attachment =
                    New System.Net.Mail.Attachment(New MemoryStream(_fileArray), ct)
                attachment.ContentDisposition.FileName = sFileName
                message.Attachments.Add(attachment)
            End If

            'If sAttachment IsNot Nothing Then
            '    Dim ct As System.Net.Mime.ContentType
            '    'If sAttachmentType.ToUpper = "PDF" Then
            '    ct = New System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Application.Pdf)
            '    'ElseIf sAttachmentType.ToUpper = "IMAGE" Then
            '    '    ct = New System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Image.Jpeg)
            '    'End If
            '    Dim attachment As System.Net.Mail.Attachment = New System.Net.Mail.Attachment(sAttachment, ct)
            '    attachment.ContentDisposition.FileName = par_sUnused
            '    message.Attachments.Add(attachment)
            'End If
            'If sFiles <> "" Then
            '    sAttach = Split(sFiles, vbCrLf)
            '    For f = 0 To sAttach.GetUpperBound(0)
            '        If sAttach(f) <> "" Then
            '            'msgObj.Attachments.Add(sAttach(f))
            '            Dim attachment As System.Net.Mail.Attachment = New System.Net.Mail.Attachment(sAttach(f), System.Net.Mime.MediaTypeNames.Application.Octet)
            '            message.Attachments.Add(attachment)

            '        End If
            '    Next
            'End If

            'Set mail server
            'client.Host = "mail.selltis.com"
            'client.Port = 587
            'client.Credentials = New System.Net.NetworkCredential("sellsmtp", "smtpking")

            Dim smtpServer As String = System.Configuration.ConfigurationManager.AppSettings("smtpServer")
            If String.IsNullOrEmpty(smtpServer) Then
                smtpServer = "mail.selltis.com"
            Else
                smtpServer = smtpServer.ToString()
            End If

            Dim smtpPort As String = System.Configuration.ConfigurationManager.AppSettings("smtpPort")
            If String.IsNullOrEmpty(smtpPort) Then
                smtpPort = 587
            Else
                smtpPort = Convert.ToInt32(smtpPort)
            End If

            Dim smtpUser As String = System.Configuration.ConfigurationManager.AppSettings("smtpUser")
            If String.IsNullOrEmpty(smtpUser) Then
                smtpUser = "sellsmtp"
            Else
                smtpUser = smtpUser.ToString()
            End If

            Dim smtpPassword As String = System.Configuration.ConfigurationManager.AppSettings("smtpPassword")
            If String.IsNullOrEmpty(smtpPassword) Then
                smtpPassword = "smtpking"
            Else
                smtpPassword = smtpPassword.ToString()
            End If

            'Set mail server
            client.Host = smtpServer
            client.Port = smtpPort
            client.Credentials = New System.Net.NetworkCredential(smtpUser, smtpPassword)
            client.EnableSsl = True

            client.Send(message)

            bResult = True

        Catch ex As Exception
            If ex.Message <> clC.EX_THREAD_ABORT_MESSAGE Then
                goErr.SetWarning(45105, sProc, ex.ToString())
            End If
        End Try

        'Clean up temporary attachments unless there was an error above
        'If bDeleteFiles Then
        '    If bResult Then
        '        ' Try
        '        For f = 0 To sAttach.GetUpperBound(0)
        '            If sAttach(f) <> "" Then
        '                My.Computer.FileSystem.DeleteFile(sAttach(f), FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
        '            End If
        '        Next

        '        If sHTMLURI <> "" Then
        '            My.Computer.FileSystem.DeleteFile(sHTMLURI, FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
        '        End If
        '        'Catch ex As Exception
        '        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        '        goErr.SetError(ex, 45105, sProc)
        '        '    End If
        '        'End Try
        '    End If
        'End If

        Return bResult

    End Function
    Public Function SendSMTPEmail_With_LocalFile_As_Attachment(ByVal par_sSubject As String,
                                    ByVal par_sBody As String,
                                    ByVal par_sTo As String,
                                    ByVal par_sCC As String,
                                    ByVal par_sBCC As String,
                                    ByVal par_sUnused As String,
                                    Optional ByVal par_ReplyName As String = "",
                                    Optional ByVal par_ReplyAddress As String = "",
                                    Optional ByVal sAttachments As String = "",
                                    Optional ByVal bHTML As Boolean = False,
                                    Optional ByVal sHTMLURI As String = "",
                                    Optional ByVal sHTMLURIBase As String = "",
                                    Optional ByVal bDeleteFiles As Boolean = True,
                                    Optional ByVal bAppendTextFromWOP As Boolean = True, Optional ByVal sFileName As String = "") As Boolean

        Dim sProc As String = "clEmail::SendSMTPEmailNew"
        Dim client As New SmtpClient()
        Dim message As New MailMessage()
        Dim sAttach(0) As String
        Dim sTo(0) As String
        Dim sCC(0) As String
        Dim sBC(0) As String
        Dim f As Integer
        Dim bResult As Boolean = True

        Try

            'Add TOs
            par_sTo = goTR.Replace(par_sTo, ",", ";")
            If par_sTo <> "" Then
                sTo = Split(par_sTo, ";")
                For f = 0 To sTo.GetUpperBound(0)
                    If Trim(sTo(f)) <> "" Then
                        message.To.Add(Trim(sTo(f)))
                    End If
                Next
            End If

            'Add CCs
            par_sCC = goTR.Replace(par_sCC, ",", ";")
            If par_sCC <> "" Then
                sCC = Split(par_sCC, ";")
                For f = 0 To sCC.GetUpperBound(0)
                    If Trim(sCC(f)) <> "" Then
                        message.CC.Add(Trim(sCC(f)))
                    End If
                Next
            End If

            'Add BCCs
            par_sBCC = goTR.Replace(par_sBCC, ",", ";")
            If par_sBCC <> "" Then
                sBC = Split(par_sBCC, ";")
                For f = 0 To sBC.GetUpperBound(0)
                    If Trim(sBC(f)) <> "" Then
                        message.Bcc.Add(Trim(sBC(f)))
                    End If
                Next
            End If

            'Specify the sender
            'msgObj.From.Email = par_ReplyAddress
            'msgObj.From.Name = par_ReplyName    

            '21092018 tickt #2463: Sending activty reports is not working..J
            'This is not wokrking on production due to display name format..J  
            Dim strDisplayName As String = par_ReplyName
            par_ReplyName = System.Configuration.ConfigurationManager.AppSettings("smtpSender")
            If Not String.IsNullOrEmpty(par_ReplyName) Then
                'message.From = New MailAddress(par_ReplyAddress, strDisplayName)
                message.From = New MailAddress(par_ReplyName, strDisplayName)
            End If

            If Not String.IsNullOrEmpty(par_ReplyAddress) Then
                message.ReplyToList.Add(par_ReplyAddress)
            End If

            message.Priority = MailPriority.Normal

            message.Subject = par_sSubject

            'Set message body

            If bAppendTextFromWOP Then      '2/1/12 MI Added testing this to be able to send an email without the WOP text appended
                par_sBody = par_sBody & vbCrLf & vbCrLf & goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "EMAILAPPEND", "", False)
            End If

            If bHTML = False Then
                message.Body = par_sBody
                message.IsBodyHtml = False
            Else
                'If sHTMLURI <> "" Then
                '    msgObj.ImportFromURI(sHTMLURI, sHTMLURIBase, ImportURIFlags.EmbedImages)
                'Else
                '    msgObj.BodyParts.Add(par_sBody, BodyPartFormat.HTML)
                'End If
                message.Body = par_sBody
                message.IsBodyHtml = True
            End If

            'Add attachment

            ''sViewName + "|" + sFieldName + "|" + sFileName + "|" + sGid_id
            If sAttachments <> "" Then
                'Dim sFiles = Split(sAttachments, "|")
                'Dim sViewName As String = sFiles.GetValue(0)
                'Dim sFieldName As String = sFiles.GetValue(1)
                'Dim sFileName As String = sFiles.GetValue(2)
                'Dim sGid_id As String = sFiles.GetValue(3)

                ' Dim ct As System.Net.Mime.ContentType = New System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Application.Pdf)
                ' Dim _fileArray = clAzureFileStorage.GetFile(sViewName, sGid_id, sFieldName, sFileName)
                Dim attachment As System.Net.Mail.Attachment =
                    New System.Net.Mail.Attachment(sAttachments)
                attachment.ContentDisposition.FileName = sFileName
                message.Attachments.Add(attachment)
            End If

            'If sAttachment IsNot Nothing Then
            '    Dim ct As System.Net.Mime.ContentType
            '    'If sAttachmentType.ToUpper = "PDF" Then
            '    ct = New System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Application.Pdf)
            '    'ElseIf sAttachmentType.ToUpper = "IMAGE" Then
            '    '    ct = New System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Image.Jpeg)
            '    'End If
            '    Dim attachment As System.Net.Mail.Attachment = New System.Net.Mail.Attachment(sAttachment, ct)
            '    attachment.ContentDisposition.FileName = par_sUnused
            '    message.Attachments.Add(attachment)
            'End If
            'If sFiles <> "" Then
            '    sAttach = Split(sFiles, vbCrLf)
            '    For f = 0 To sAttach.GetUpperBound(0)
            '        If sAttach(f) <> "" Then
            '            'msgObj.Attachments.Add(sAttach(f))
            '            Dim attachment As System.Net.Mail.Attachment = New System.Net.Mail.Attachment(sAttach(f), System.Net.Mime.MediaTypeNames.Application.Octet)
            '            message.Attachments.Add(attachment)

            '        End If
            '    Next
            'End If

            'Set mail server
            'client.Host = "mail.selltis.com"
            'client.Port = 587
            'client.Credentials = New System.Net.NetworkCredential("sellsmtp", "smtpking")

            Dim smtpServer As String = System.Configuration.ConfigurationManager.AppSettings("smtpServer")
            If String.IsNullOrEmpty(smtpServer) Then
                smtpServer = "mail.selltis.com"
            Else
                smtpServer = smtpServer.ToString()
            End If

            Dim smtpPort As String = System.Configuration.ConfigurationManager.AppSettings("smtpPort")
            If String.IsNullOrEmpty(smtpPort) Then
                smtpPort = 587
            Else
                smtpPort = Convert.ToInt32(smtpPort)
            End If

            Dim smtpUser As String = System.Configuration.ConfigurationManager.AppSettings("smtpUser")
            If String.IsNullOrEmpty(smtpUser) Then
                smtpUser = "sellsmtp"
            Else
                smtpUser = smtpUser.ToString()
            End If

            Dim smtpPassword As String = System.Configuration.ConfigurationManager.AppSettings("smtpPassword")
            If String.IsNullOrEmpty(smtpPassword) Then
                smtpPassword = "smtpking"
            Else
                smtpPassword = smtpPassword.ToString()
            End If

            'Set mail server
            client.Host = smtpServer
            client.Port = smtpPort
            client.Credentials = New System.Net.NetworkCredential(smtpUser, smtpPassword)
            client.EnableSsl = True

            client.Send(message)

            bResult = True

        Catch ex As Exception
            If ex.Message <> clC.EX_THREAD_ABORT_MESSAGE Then
                goErr.SetWarning(45105, sProc, ex.ToString())
            End If
        End Try

        'Clean up temporary attachments unless there was an error above
        'If bDeleteFiles Then
        '    If bResult Then
        '        ' Try
        '        For f = 0 To sAttach.GetUpperBound(0)
        '            If sAttach(f) <> "" Then
        '                My.Computer.FileSystem.DeleteFile(sAttach(f), FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
        '            End If
        '        Next

        '        If sHTMLURI <> "" Then
        '            My.Computer.FileSystem.DeleteFile(sHTMLURI, FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
        '        End If
        '        'Catch ex As Exception
        '        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        '        goErr.SetError(ex, 45105, sProc)
        '        '    End If
        '        'End Try
        '    End If
        'End If

        Return bResult

    End Function


    Public Function SendSMTPEmailNewCustom(ByVal par_sSubject As String, _
                                    ByVal par_sBody As String, _
                                    ByVal par_sTo As String, _
                                    ByVal par_sCC As String, _
                                    ByVal par_sBCC As String, _
                                    ByVal par_sUnused As String, _
                                    Optional ByVal par_ReplyName As String = "", _
                                    Optional ByVal par_ReplyAddress As String = "", _
                                    Optional ByVal sFiles As String = "", _
                                    Optional ByVal bHTML As Boolean = False, _
                                    Optional ByVal sHTMLURI As String = "", _
                                    Optional ByVal sHTMLURIBase As String = "", _
                                    Optional ByVal bDeleteFiles As Boolean = True, _
                                    Optional ByVal bAppendTextFromWOP As Boolean = True) As Boolean

        Dim sProc As String = "clEmail::SendSMTPEmailNew"
        Dim client As New SmtpClient()
        Dim message As New MailMessage()
        Dim sAttach(0) As String
        Dim sTo(0) As String
        Dim sCC(0) As String
        Dim sBC(0) As String
        Dim f As Integer
        Dim bResult As Boolean = True

        Try

            'Add TOs
            par_sTo = goTR.Replace(par_sTo, ",", ";")
            If par_sTo <> "" Then
                sTo = Split(par_sTo, ";")
                For f = 0 To sTo.GetUpperBound(0)
                    If Trim(sTo(f)) <> "" Then
                        message.To.Add(Trim(sTo(f)))
                    End If
                Next
            End If

            'Add CCs
            par_sCC = goTR.Replace(par_sCC, ",", ";")
            If par_sCC <> "" Then
                sCC = Split(par_sCC, ";")
                For f = 0 To sCC.GetUpperBound(0)
                    If Trim(sCC(f)) <> "" Then
                        message.CC.Add(Trim(sCC(f)))
                    End If
                Next
            End If

            'Add BCCs
            par_sBCC = goTR.Replace(par_sBCC, ",", ";")
            If par_sBCC <> "" Then
                sBC = Split(par_sBCC, ";")
                For f = 0 To sBC.GetUpperBound(0)
                    If Trim(sBC(f)) <> "" Then
                        message.Bcc.Add(Trim(sBC(f)))
                    End If
                Next
            End If

            'Specify the sender
            'msgObj.From.Email = par_ReplyAddress
            'msgObj.From.Name = par_ReplyName 

            '21092018 tickt #2463: Sending activty reports is not working..J
            'This is not wokrking on production due to display name format..J  
            Dim strDisplayName As String = par_ReplyName
            par_ReplyName = System.Configuration.ConfigurationManager.AppSettings("smtpSender")
            If Not String.IsNullOrEmpty(par_ReplyName) Then
                'message.From = New MailAddress(par_ReplyName, par_ReplyAddress)
                message.From = New MailAddress(par_ReplyName, strDisplayName)
                message.ReplyToList.Add(par_ReplyAddress)
            End If

            message.Priority = MailPriority.Normal

            message.Subject = par_sSubject

            'set image body
            ConvertBase64Images(par_sBody)

            'Set message body

            If bAppendTextFromWOP Then      '2/1/12 MI Added testing this to be able to send an email without the WOP text appended
                par_sBody = par_sBody & vbCrLf & vbCrLf & goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "EMAILAPPEND", "", False)
            End If

            '02082018 tckt #2381: Need to remove spaces when activity send reports showing in Outlook..J
            par_sBody = par_sBody.Replace("</p><br />", "</p>")

            If bHTML = False Then
                message.Body = par_sBody
                message.IsBodyHtml = False
            Else
                'If sHTMLURI <> "" Then
                '    msgObj.ImportFromURI(sHTMLURI, sHTMLURIBase, ImportURIFlags.EmbedImages)
                'Else
                '    msgObj.BodyParts.Add(par_sBody, BodyPartFormat.HTML)
                'End If
                message.Body = par_sBody
                message.IsBodyHtml = True
            End If

            'Add attachment
            If sFiles <> "" Then
                sAttach = Split(sFiles, vbCrLf)
                For f = 0 To sAttach.GetUpperBound(0)
                    If sAttach(f) <> "" Then
                        'msgObj.Attachments.Add(sAttach(f))
                        Dim attachment As System.Net.Mail.Attachment = New System.Net.Mail.Attachment(sAttach(f), System.Net.Mime.MediaTypeNames.Application.Octet)
                        message.Attachments.Add(attachment)

                    End If
                Next
            End If

            'Set mail server
            'client.Host = "mail.selltis.com"
            'client.Port = 587
            'client.Credentials = New System.Net.NetworkCredential("sellsmtp", "smtpking")

            Dim smtpServer As String = System.Configuration.ConfigurationManager.AppSettings("smtpServer")
            If String.IsNullOrEmpty(smtpServer) Then
                smtpServer = "mail.selltis.com"
            Else
                smtpServer = smtpServer.ToString()
            End If

            Dim smtpPort As String = System.Configuration.ConfigurationManager.AppSettings("smtpPort")
            If String.IsNullOrEmpty(smtpPort) Then
                smtpPort = 587
            Else
                smtpPort = Convert.ToInt32(smtpPort)
            End If

            Dim smtpUser As String = System.Configuration.ConfigurationManager.AppSettings("smtpUser")
            If String.IsNullOrEmpty(smtpUser) Then
                smtpUser = "sellsmtp"
            Else
                smtpUser = smtpUser.ToString()
            End If

            Dim smtpPassword As String = System.Configuration.ConfigurationManager.AppSettings("smtpPassword")
            If String.IsNullOrEmpty(smtpPassword) Then
                smtpPassword = "smtpking"
            Else
                smtpPassword = smtpPassword.ToString()
            End If

            'Set mail server
            client.Host = smtpServer
            client.Port = smtpPort
            client.Credentials = New System.Net.NetworkCredential(smtpUser, smtpPassword)
            client.EnableSsl = True

            client.Send(message)
            bResult = True
            ''goLog.Log("Send Activity Report", "Call report was sent successfully From " & message.From.ToString, 0, True)
            goLog.Log("Send Activity Report", par_sSubject & " has been sent successfully From " & message.From.ToString & ", To : " & par_sTo & ";" & par_sCC & ";" & par_sBCC, 0, True)

        Catch ex As Exception
            'If ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetWarning(45105, sProc, ex.ToString())
            'End If

            '19092018 tickt #2463:Customer is saying that SOMETIMES  when they use the Send AC Report button, it does not send. They have to go back into the record and do it again. Is there anything we can check here?..J
            bResult = False
            goLog.Log("Send Activity Report", par_sSubject & " sent failed due to the error - " & ex.Message(), 0, True)
        End Try

        'Clean up temporary attachments unless there was an error above
        If bDeleteFiles Then
            If bResult Then
                'Try
                For f = 0 To sAttach.GetUpperBound(0)
                        If sAttach(f) <> "" Then
                            My.Computer.FileSystem.DeleteFile(sAttach(f), FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
                        End If
                    Next

                    If sHTMLURI <> "" Then
                        My.Computer.FileSystem.DeleteFile(sHTMLURI, FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
                    End If
                'Catch ex As Exception
                '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
                '        goErr.SetError(ex, 45105, sProc)
                '    End If
                'End Try
            End If
        End If

        Return bResult

    End Function

    Public Function ConvertBase64Images(ByRef strData As String) As String

        Dim Ht As New ArrayList() 'keeps the physical image file names
        Dim Ht1 As New ArrayList() 'keeps the base64 Image string to check for the same images
        Dim DHT As Dictionary(Of Integer, String) = New Dictionary(Of Integer, String)
        'wt 11/11/14
        'added support for html in word templates

        'Clipboard.SetData(System.Windows.Forms.DataFormats.Html, "<html><body>" & strData & "</body></html>")
        'objWord.Selection.PasteAndFormat(16)
        'objWord.Selection.PasteSpecial(, , , , 10)

        'strData = strData.Replace("<p>", "").Replace("</p>", "<br/>")

        'V_T 2/26/2015
        'MMR Change -- Replace the Base64 images with local image path

        Dim regexImgSrc As String = "<img[^>]*?src\s*=\s*[""']?([^'"" >]+?)[ '""][^>]*?>"
        Dim matchesImgSrc As MatchCollection = Regex.Matches(strData, regexImgSrc, RegexOptions.IgnoreCase Or RegexOptions.Singleline)

        Dim indx As Integer = 0

        For indx = 0 To matchesImgSrc.Count - 1

            Dim imgstrs As String = matchesImgSrc.Item(indx).Value

            Dim _sPath As String = System.Web.HttpContext.Current.Server.MapPath("../Temp") 'Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)
            _sPath = _sPath & "\" & clSettings.GetHostName() & "\OutLookImages\"

            'Dim _sPath = "/PublicFiles/" + clSettings.GetSiteId() + "/OutLookImages/"

            Dim exists As Boolean = System.IO.Directory.Exists(_sPath)
            If Not exists Then
                System.IO.Directory.CreateDirectory(_sPath)
            End If

            Dim iwidth, iheight As Integer
            iwidth = 0
            iheight = 0

            'Get the Image size section start
            Dim reHeight As New Regex("height:\s\d*", RegexOptions.IgnoreCase Or RegexOptions.Singleline)
            Dim reWidth As New Regex("width:\s\d*", RegexOptions.IgnoreCase Or RegexOptions.Singleline)

            Dim mWidth As Match = reWidth.Match(imgstrs)
            If mWidth.Length > 8 And mWidth.Value.Contains(":") And mWidth.Value.Contains("width") Then
                Integer.TryParse(mWidth.Value.Split(":").GetValue(1).ToString().Trim(), iwidth)
            End If

            Dim mHeight As Match = reHeight.Match(imgstrs)
            If mHeight.Length > 8 And mHeight.Value.Contains(":") And mHeight.Value.Contains("height") Then
                Integer.TryParse(mHeight.Value.Split(":").GetValue(1).ToString().Trim(), iheight)
            End If

            'Get the images size section end
            Dim _imageName As String = "OutookNotesImage" & GetTimeString() & indx.ToString() & ".jpg"
            _sPath = _sPath & _imageName
            'Dim PBase64Imagestr As String = Regex.Match(imgstrs, "<img.+?src=[""'](.+?)[""'].+?>", RegexOptions.IgnoreCase).Groups(1).Value

            '29082018 tickt #2401: Images are not showed in outlook email for the fist time after AC logged from outlook..J
            Dim PBase64Imagestr As String = Regex.Match(imgstrs, "src\s*=\s*[""'](.+?)[""'].+?", RegexOptions.IgnoreCase).Groups(1).Value

            If PBase64Imagestr.Split(",").Length > 1 Then
                Dim Base64Imagestr As String = PBase64Imagestr.Split(",")(1)
                'Dim Base64ImagestrEncoded = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(Base64Imagestr)) 'Convert.FromBase64String(Base64Imagestr)
                'Dim bytes = Convert.FromBase64String(Base64ImagestrEncoded)
                Dim b64 As String = Base64Imagestr.Replace(" ", "+")
                Dim bytes = Convert.FromBase64String(b64)
                Using imageFile = New FileStream(_sPath, FileMode.Create)
                    imageFile.Write(bytes, 0, bytes.Length)
                    imageFile.Flush()
                End Using

                'Resize Image if style specified in the image tag
                If iwidth > 0 AndAlso iheight > 0 Then
                    Dim _Timg As Image = Image.FromFile(_sPath)
                    Dim myCallback As Image.GetThumbnailImageAbort = New Image.GetThumbnailImageAbort(AddressOf ThumbnailCallback)
                    Dim _ImgToSave As Image = _Timg.GetThumbnailImage(iwidth, iheight, myCallback, IntPtr.Zero)
                    _Timg.Dispose()
                    If File.Exists(_sPath) Then
                        File.Delete(_sPath)
                    End If
                    _ImgToSave.Save(_sPath, System.Drawing.Imaging.ImageFormat.Jpeg)
                End If
                'Resize done
                Dim objmmr_letterAttach As Dictionary(Of String, String) = New Dictionary(Of String, String) 'Attach mmr_Later Image
                objmmr_letterAttach.Add(_imageName, _sPath)

                'checking for the same image 
                If Ht1.Contains(imgstrs) Then
                    Dim pair As KeyValuePair(Of Integer, String)
                    Dim indval As Integer = 0
                    For Each pair In DHT
                        If pair.Value = imgstrs Then
                            indval = pair.Key
                            GoTo end_of_for
                        End If
                    Next
end_of_for:
                    Ht.Add(Ht(indval))
                Else
                    Ht.Add(_imageName)
                    Ht1.Add(imgstrs)
                    DHT.Add(indx, imgstrs)
                End If

                '_sPath = "~/Temp/" & clSettings.GetHostName() & "/OutLookImages/" & _imageName
                'If Directory.Exists("/PublicFiles/" + clSettings.GetSiteId() + "/OutLookImages/") Then
                '    Directory.CreateDirectory("/PublicFiles/" + clSettings.GetSiteId() + "/OutLookImages/")
                'End If
                Dim baseURL As String = HttpContext.Current.Request.Url.Scheme + "://" + HttpContext.Current.Request.Url.Authority
                _sPath = baseURL & "/Temp/" & clSettings.GetHostName() & "/OutLookImages/" & _imageName
                _imageName = "<img src='" & _sPath & "' />"

                strData = strData.ToString().Replace(imgstrs, _imageName)
            End If
        Next

        Return ""

    End Function



    Public Function GetTimeString() As String

        Dim sTime As DateTime = Now
        'GetLocalTime(sTime)

        GetTimeString = sTime.Year & PadLeadingZeros(sTime.Month, 2) & PadLeadingZeros(sTime.Day, 2) & PadLeadingZeros(sTime.Hour, 2) & PadLeadingZeros(sTime.Minute, 2) & PadLeadingZeros(sTime.Second, 2) & Left(PadLeadingZeros(sTime.Millisecond, 3), 2)

    End Function


    Public Function ThumbnailCallback() As Boolean
        Return False
    End Function

    Public Function PadLeadingZeros(ByVal strForm As String, ByVal intLen As Integer) As String

        If Len(strForm) < intLen Then
            Do While Len(strForm) <> intLen
                strForm = "0" & strForm
            Loop
            PadLeadingZeros = strForm
        Else
            PadLeadingZeros = strForm
        End If

    End Function


End Class
