﻿using System;
using System.Data;


//Owner: MI 
//MI 2/15/07 Created ImportUpdateSysNames, ImportUpdateSysNamesInTable.
//MI 2/13/07 Removed adding TXT_SI__ShareState, always setting it to 2.
//CREATEED: 11/9/06

//PURPOSE:
//       Manage schema maintenance and data-import-related schema modifications.
//       IMPORTANT: There is no global object for this class. You must create and
//       manage persisting this object as needed in each page.

using System.Web;

namespace Selltis.BusinessLogic
{
	public class clSchema
	{

		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clLog goLog;
		private clDefaults goDef;

		public void Initialize()
		{

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			goDef = (Selltis.BusinessLogic.clDefaults)HttpContext.Current.Session["goDef"];

		}

		private bool CreateAFFile()
		{
			//AUTHOR: MI 3/22/07
			//MI 3/2/09 Writing AF MD to XX product.
			//PURPOSE:
			//       Creates AF table, links, and related MD to enable mapping Cmc 'File' category
			//       to the AF table.
			//RETURNS:
			//       True when successful, False when file is unsupported. SQL server errors
			//       are not checked.

			string sproc = "clSchema::CreateAFFile";
			goLog.Log(sproc, "Start", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			bool bResult = true;
			string s = null;

			//Try

			string sSQL = "";

				sSQL = "if not exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AF]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)" + "\r\n" + "BEGIN" + "\r\n" + "CREATE TABLE [dbo].[AF] (" + "\r\n" + "	[GID_ID] [uniqueidentifier] NOT NULL ," + "\r\n" + "	[BI__ID] [bigint] IDENTITY (1, 1) NOT NULL ," + "\r\n" + "	[SYS_Name] [nvarchar] (80) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ," + "\r\n" + "	[DTT_CreationTime] [datetime] NULL ," + "\r\n" + "	[TXT_CreatedBy] [varchar] (4) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ," + "\r\n" + "	[DTT_Time] [datetime] NULL ," + "\r\n" + "	[TXT_AttachedFileName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ," + "\r\n" + "	[URL_FileOnServer] [ntext] COLLATE SQL_Latin1_General_CP1_CI_AS NULL ," + "\r\n" + "	[TXT_ModBy] [varchar] (4) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ," + "\r\n" + "	[DTT_ModTime] [datetime] NULL ," + "\r\n" + "	[SI__ShareState] [tinyint] NULL ," + "\r\n" + "	[GID_CreatedBy_US] [uniqueidentifier] NULL " + "\r\n" + ") ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]" + "\r\n" + "" + "\r\n" + "ALTER TABLE [dbo].[AF] WITH NOCHECK ADD " + "\r\n" + "	CONSTRAINT [PK_AF] PRIMARY KEY  CLUSTERED " + "\r\n" + "	(" + "\r\n" + "           [GID_ID]" + "\r\n" + "	)  ON [PRIMARY] " + "\r\n" + "" + "\r\n" + "ALTER TABLE [dbo].[AF] ADD " + "\r\n" + "	CONSTRAINT [DF_AF_GID_ID] DEFAULT (convert(uniqueidentifier,(convert(binary(8),newid()) + convert(binary(2),'AF') + convert(binary(6),GetUTCDate())))) FOR [GID_ID]," + "\r\n" + "	CONSTRAINT [DF_AF_DTT_CreationTime] DEFAULT (GetUTCDate()) FOR [DTT_CreationTime]," + "\r\n" + "	CONSTRAINT [DF_AF_DTT_Time] DEFAULT (GetUTCDate()) FOR [DTT_Time]," + "\r\n" + "	CONSTRAINT [DF_AF_DTT_ModTime] DEFAULT (GetUTCDate()) FOR [DTT_ModTime]," + "\r\n" + "	CONSTRAINT [DF_AF_SI__ShareState] DEFAULT (2) FOR [SI__ShareState]" + "\r\n" + "" + "\r\n" + " CREATE  INDEX [IX_AF_Name] ON [dbo].[AF]([SYS_Name]) ON [PRIMARY]" + "\r\n" + " CREATE  INDEX [IX_AF_CreatedBy_US] ON [dbo].[AF]([GID_CreatedBy_US]) ON [PRIMARY]" + "\r\n" + " CREATE  INDEX [IX_AF_BI__ID] ON [dbo].[AF]([BI__ID]) ON [PRIMARY]" + "\r\n" + "END";

				goLog.Log(sproc, "Running RunSQLQuery: '" + sSQL + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);
				if (!goData.RunSQLQuery(sSQL))
				{
					bResult = false;
				}

				//-----------------------
				//Create link tables
				sSQL = "EXEC dbo.pCreateLinkTable" + "\r\n" + "@sFile1=AF," + "\r\n" + "@sFile2=AC," + "\r\n" + "@sLinkName='Attached'," + "\r\n" + "@sLinkRevName='Attached'";
				goLog.Log(sproc, "Running RunSQLQuery: '" + sSQL + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);
				if (!goData.RunSQLQuery(sSQL))
				{
					bResult = false;
				}

				sSQL = "EXEC dbo.pCreateLinkTable" + "\r\n" + "@sFile1=AF," + "\r\n" + "@sFile2=PR," + "\r\n" + "@sLinkName='AttachedTo'," + "\r\n" + "@sLinkRevName='Attached'";
				goLog.Log(sproc, "Running RunSQLQuery: '" + sSQL + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);
				if (!goData.RunSQLQuery(sSQL))
				{
					bResult = false;
				}

				//----------------------------
				//Create metadata
				//File label
				System.Data.SqlClient.SqlConnection tempVar = null;
				goMeta.LineWrite("GLOBAL", "OTH_FILES", "US_AF", "Attached File", ref tempVar, "", "XX");
				System.Data.SqlClient.SqlConnection tempVar2 = null;
				goMeta.LineWrite("GLOBAL", "OTH_FILES", "US_SAF", "Attached Files", ref tempVar2, "", "XX");

				//Link definitions
				System.Data.SqlClient.SqlConnection tempVar3 = null;
				goMeta.LineWrite("GLOBAL", "OTH_LINKS", "AF,AC,Attached", "NN,Attached", ref tempVar3, "", "XX");
				System.Data.SqlClient.SqlConnection tempVar4 = null;
				goMeta.LineWrite("GLOBAL", "OTH_LINKS", "AF,PR,AttachedTo", "NN,Attached", ref tempVar4, "", "XX");
				System.Data.SqlClient.SqlConnection tempVar5 = null;
				goMeta.LineWrite("GLOBAL", "OTH_LINKS", "AF,US,CreatedBy", "N1,Created", ref tempVar5, "", "XX");

				//Field definition page for the new file 'AF'
				s = "";
				goTR.StrWrite(ref s, "US_BI__ID", "Sequence ID");
				goTR.StrWrite(ref s, "US_DTE_CREATIONTIME", "Creation Date");
				goTR.StrWrite(ref s, "US_DTE_MODTIME", "Last Modification Date");
				goTR.StrWrite(ref s, "US_DTE_TIME", "Date");
				goTR.StrWrite(ref s, "DTE_TIME_DEF", "Today");
				goTR.StrWrite(ref s, "US_DTT_CREATIONTIME", "Creation Date/Time");
				goTR.StrWrite(ref s, "US_DTT_MODTIME", "Last Modification Date/Time");
				goTR.StrWrite(ref s, "US_DTT_TIME", "Date/Time");
				goTR.StrWrite(ref s, "FIELDSWDEFS", "DTE_TIME|TME_TIME|");
				goTR.StrWrite(ref s, "US_GID_ID", "ID");
				goTR.StrWrite(ref s, "LINKLOCKED", "LNK_CreatedBy_US|");
				goTR.StrWrite(ref s, "LINKMELINKS", "LNK_CreatedBy_US|");
				goTR.StrWrite(ref s, "US_LNK_CREATEDBY_US", "Created By User");
				goTR.StrWrite(ref s, "US_NAME", "Fields for file ATTACHED FILE");
				goTR.StrWrite(ref s, "US_SI__SHARESTATE", "Sharing State");
				goTR.StrWrite(ref s, "US_SYS_NAME", "Name");
				goTR.StrWrite(ref s, "US_TME_CREATIONTIME", "Creation Time");
				goTR.StrWrite(ref s, "US_TME_MODTIME", "Last Modification Time");
				goTR.StrWrite(ref s, "US_TME_TIME", "Time");
				goTR.StrWrite(ref s, "TME_TIME_DEF", "Now");
				goTR.StrWrite(ref s, "US_TXT_CREATEDBY", "Created By");
				goTR.StrWrite(ref s, "US_TXT_MODBY", "Modified By");
				goTR.StrWrite(ref s, "US_URL_FILEONSERVER", "File On Server");
				goTR.StrWrite(ref s, "US_TXT_ATTACHEDFILENAME", "Attached File Name");
				goMeta.PageWrite("GLOBAL", "FLD_AF", s, "", "", "XX");

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sproc)
			//    End If
			//End Try

			return bResult;

		}

		public string GenerateLinkCreationSQL(string par_sFileName, string par_sTempLinkField, long par_lFromBIID = -1, long par_lToBIID = -1, bool par_bIgnoreDuplicateImportIDs = false, string par_sImpJobID = "", int par_iMode = clC.SELL_LinkProcessMode_ImportID, string par_sInstructionsIni = "", string par_sLinkKeyField = "TXT_ImportID")
		{

			//PJ 11/1/11 Changed functiom from Private to Public to be compat with new import utility
			//           Added Optional ByVal par_sLinkKeyField As String = "TXT_ImportID" to support 'Link Key Field' linking in import utility

			//MI 10/6/09 Changed methodology from looping on BI__ID to CURSOR.
			//MI 3/26/09 Enabled support for GID_ID and TN modes.
			//MI 3/9/09 Added par_iMode param. Enabling new processing modes: by GID_ID and through TN table.
			//MI 3/4/09 Added par_sImpJobID to support processing field for a single import job only.
			//MI 2/27/09 Made NN link parsing code parse any combination of Chr(13) and Chr(10),
			//       or either of those by itself.
			//MI 8/7/08 added par_bIgnoreDuplicateImportIDs to allow processing links when there
			//       are records in linked tables that have duplicate TXT_ImportID values.
			//       WARNING: this will cause one of the duplicates records (no way to control which one)
			//       to be linked to all records that contain that Import ID value in a temp
			//       import field.
			//MI 5/14/08 Added brackets in SQL statement(s).
			//MI 2/10/07 Fixed no BEGIN END issue causing creation of redundant links
			//MI 11/14/06
			//PURPOSE:
			//       Creates a portion of a dynamic SQL statement that creates a link by
			//       either adding a record to a link table (for NN links) or by UPDATEing
			//       an appropriate GID_<LinkTo>_<Table> field (for N1 links).
			//       Variable names used in the statement generated here are declared in
			//       ImportProcessFieldsInTable2. Keep them in sync!
			//       This is calleed from ImportProcessFieldsInTable2() for each LNK temporary
			//       import field. These fields are initially populated with Name values from
			//       Cmc or another app using DTS. Each statement generated by this method is appended to a
			//       string and all statements are executed as one large SQL statement.
			//       This makes performance several orders of magnitude better than a 
			//       rowset approach, but makes debugging difficult because SQL errors
			//       don't indicate the record on which an error occurred.
			//PARAMETERS:
			//       par_sFileName: name of the table to process
			//       par_sTempLinkField: name of the import temp field to process (ex: TXT_LNK_CreditedTo_US,
			//           MMO_LNK_Involves_US, TXT_DTE_StartTime, TXT_MLS_Type).
			//       par_lFromBIID: Optional: if defined, BI__ID value from which to process
			//           records. If not defined or -1 (default), records are processed from BI__ID = 1.
			//       par_lToBIID: Optional: if defined, BI__ID value to which to process 
			//           records. If not defined or -1 (default), records are processed to the last
			//           BI__ID value.
			//       par_bIgnoreDuplicateImportIDs: optional (False by default), pertains only to
			//           to ImportID par_iMode!
			//           If false (default) a SQL error 512 will be raised if there
			//           are any records in linked tables that have duplicate TXT_ImportID values.
			//           If True, one of the duplicates records (no way to control which one)
			//           will be linked to all records that contain that Import ID value in a
			//           temp import field in the table being processed. 
			//           WARNING: Set this parameter to True only when it is impossible to 
			//           resolve duplicate TXT_ImportIDs. This will lead to wrong records linked!
			//       par_sImpJobID: If not blank, fields will be processed only in records where
			//           TXT_ImpJobID = this parameter. The value of this parameter must not exceed
			//           20 characters. If it does, an error is generated and processing is canceled.
			//       par_iMode: Mode in which to process the link. Note that par_sInstructionsIni may
			//           need to contain additional information depending on the mode. Supported values are:
			//           1 or clc.SELL_LinkProcessMode_ImportID: existing method where temp import fields
			//               contain values that are stored in TXT_ImportID fields. par_sInstructionsIni
			//               is optional. 
			//           2 or clc.SELL_LinkProcessMode_GID_ID: links are established throughy explicit 
			//               GID_ID values. This is used when an ID of a Selltis record is part of the 
			//               imported data, for example the same user's GID_ID is to be linked to all
			//               imported records in a particular link. par_sInstructionsIni must contain 
			//               the following line forthe link being processed:
			//                   <linkname>=<GID_ID to link>
			//               It can contain a complete ini string as passed to ImportProcessFieldsInTable2.
			//           3 or clc.SELL_LinkProcessMode_TN: temp import fields contain values that are logged
			//               in the TN (Translation) table. Records are matched based on TN IDs. par_sInstructionsIni
			//               must contain ExternalSourceName=<name of the source> or the 
			//               complete ini string as passed to ImportProcessFieldsInTable2.
			//       par_sInstructionsIni: ini string passed to ImportProcessFieldsInTable2. This string
			//           depends on par_iMode. Here's what's expected in it when par_iMode is:
			//           1 or clc.SELL_LinkProcessMode_ImportID: Nothing: this parameter is ignored.
			//           2 or clc.SELL_LinkProcessMode_GID_ID: <linkname>=<GID_ID> definition for the link
			//               being processed (as defined in par_sTempLinkField parameter).
			//           3 or clc.SELL_LinkProcessMode_TN: ExternalSourceName= definition. Since par_iMode 
			//               unambiguously defines the link processing mode, DEFAULTMODE= or ProcessListedLinksOnly=
			//               are ignored. 
			//           For more information on the syntax of the ini string in this parameter, 
			//           see ImportProcessFieldsInTable2(). 

			string sproc = "clSchema::GenerateLinkCreationSQL";
			goLog.Log(sproc, "Start par_sFileName: '" + par_sFileName + "' par_sTempLinkField: '" + par_sTempLinkField + "' par_lFromBIID: '" + par_lFromBIID.ToString() + "' par_lToBIID: '" + par_lToBIID.ToString() + "' par_bIgnoreDuplicateImportIDs: '" + par_bIgnoreDuplicateImportIDs.ToString() + "' par_sImpJobID: '" + par_sImpJobID + "' par_iMode: '" + par_iMode + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			string sResult = "";
			string sFileName = par_sFileName.ToUpper();
			string sTempLinkFieldName = par_sTempLinkField.ToUpper();
			string sLinkName = goTR.FromTo(sTempLinkFieldName, 5, -1);
			string sLinkToFile = null;
			string sTableToWriteTo = null; //Table on either side of the link (N1) or the link table (NN)
			string sType = null; //NN or N1
			string sLinkGIDField = ""; //N1 links only: name of the field that holds the GID_ID of the linked table's records
			string sLinkGIDField1 = ""; //NN links: Name of the ID field referencing the GID_ID of the 'from'
			//                                   'linked table's records when direction is 1.
			string sLinkGIDField2 = ""; //NN links: Name of the ID field referencing the GID_ID of the 'to'
			//                                   'linked table's records when direction is 1.
			string sInverseName = null;
			int iDirection = 0;
			string sTemp = null;
			string sPreStatement = "";
			string sN1Statement = "";
			string sNNStatement = "";
			string sWhere = "";
			string sWhereAppend = "";
			int iMode = par_iMode;
			string sExternalSourceName = "";
			string sGID_ID = "";

			//Get the name of the table in which to write the ID(s) of the link record(s)
			sTableToWriteTo = goData.LK_GetFile(sFileName, sLinkName);
			if (sTableToWriteTo == "ROOT")
			{
				sTableToWriteTo = sFileName;
			}

			//Extract the 'to' file from the link name
			sLinkToFile = goTR.GetFileFromLinkName(sLinkName);

			//Skip links to our 'system' tables, which don't have TXT_ImportID field
			if (sLinkToFile == "TN" || sLinkToFile == "XL" || sLinkToFile == "MD")
			{
				return "";
			}

			iDirection = goData.LKGetDirection(sFileName, sLinkName);

			//In TN mode, get the external source name.
			switch (iMode)
			{
				case clC.SELL_LinkProcessMode_GID_ID:
					//Read the GID_ID value to set in this link
					sGID_ID = goTR.StrRead(par_sInstructionsIni, sLinkName, "", false);
					break;
				case clC.SELL_LinkProcessMode_TN:
					sExternalSourceName = goTR.StrRead(par_sInstructionsIni, "ExternalSourceName", "", false);
					break;
			}

			//------------- Write the ImpJobID portion for WHERE statements ------------
			if (par_sImpJobID.Length > 20)
			{
				goErr.SetError(35000, sproc, "The value of parameter 'par_sImpJobID' exceeds 20 characters: '" + par_sImpJobID + "'. Processing failed.");
			}
			if (par_sImpJobID != "")
			{
				//This counts on a WHERE statement being defined in each query. If 'all records' 
				//should be returned, put 'WHERE 1 = 1' in the query.
				sWhereAppend = "[TXT_ImpJobID] = '" + par_sImpJobID + "' ";
			}

			//-------------------- HEADER ----------------------
			//STATEMENT EXAMPLE:
			//DECLARE @biRec bigint
			//DECLARE @biPos bigint
			//DECLARE @biNewPos bigint
			//DECLARE @sVal varchar(80)
			//DECLARE @uGID uniqueidentifier
			//DECLARE @uLinkedRecGID uniqueidentifier
			//DECLARE @iResult int
			//
			//-- Loop through records sequentially by their BI__ID. If there are gaps in BI__ID
			//-- values, this will take longer to run, but will not raise errors.
			//If @biStartRec = -1
			//SET @biStartRec = (SELECT TOP 1 BI__ID FROM CO ORDER BY BI__ID ASC)
			//IF @biEndRec = -1
			//SET @biEndRec = (SELECT TOP 1 BI__ID FROM CO ORDER BY BI__ID DESC)
			//SET @biRec = @biStartRec	--245091
			//PRINT 'Start rec: ' + CAST(@biStartRec as varchar(20))
			//PRINT 'End rec: ' + CAST(@biEndRec as varchar(20))

			sPreStatement = "DECLARE @biRec bigint " + "\r\n" + "DECLARE @biPos bigint " + "\r\n" + "DECLARE @biNewPos bigint " + "\r\n" + "DECLARE @sVal varchar(80) " + "\r\n" + "DECLARE @uGID uniqueidentifier " + "\r\n" + "DECLARE @uLinkedRecGID uniqueidentifier " + "\r\n" + "DECLARE @iResult integer " + "\r\n" + "DECLARE @sCRString varchar(2)" + "\r\n" + "DECLARE @sCRChars varchar(40)" + "\r\n" + "DECLARE @iCRLength integer" + "\r\n" + "DECLARE @s" + sTempLinkFieldName + " nvarchar(max)" + "\r\n" + "\r\n";



			//Prepare WHERE part of the CURSOR's SELECT statement
			sWhere = "";
			if (par_lFromBIID != -1)
			{
				sWhere += "[BI__ID] >= " + par_lFromBIID.ToString() + " AND ";
			}
			if (par_lToBIID != -1)
			{
				sWhere += "[BI__ID] <= " + par_lToBIID.ToString() + " AND ";
			}
			sWhere += sWhereAppend;
			//Remove trailing AND
			if (sWhere.Substring(sWhere.Length - 5) == " AND ")
			{
				sWhere = goTR.FromTo(sWhere, 1, sWhere.Length - 5);
			}
			if (sWhere.Length > 0)
			{
				sWhere = "WHERE " + sWhere;
			}

			//-------------- CURSOR -----------------
			sN1Statement = sPreStatement + "DECLARE LinkUpdate CURSOR FOR" + "\r\n" + "SELECT [GID_ID], [BI__ID], [" + sTempLinkFieldName + "]" + "\r\n" + "FROM [" + sFileName + "]" + "\r\n" + sWhere + "\r\n" + "ORDER BY [BI__ID] ASC" + "\r\n" + "\r\n" + "OPEN LinkUpdate" + "\r\n" + "\r\n" + "FETCH NEXT FROM LinkUpdate" + "\r\n" + "INTO @uGID, @biRec, @s" + sTempLinkFieldName + "\r\n" + "\r\n" + "WHILE @@FETCH_STATUS = 0" + "\r\n" + "BEGIN" + "\r\n" + "\t" + "PRINT '------'" + "\r\n" + "\t" + "PRINT '@biRec: ''' + CAST(@biRec as varchar(30)) + ''''" + "\r\n";

			if (iMode == clC.SELL_LinkProcessMode_GID_ID)
			{
				//GID_ID mode
				sN1Statement += "\t" + "SET @uLinkedRecGID = CAST('" + sGID_ID + "' as uniqueidentifier)" + "\r\n" + "\t" + "PRINT '@uLinkedRecGID: ''' + CAST(@uLinkedRecGID as varchar(40)) + ''''" + "\r\n" + "\t" + "[1]" + "\r\n";
			}
			else
			{
				//TXT_ImportID and TN modes
				sN1Statement += "\t" + "SET @sVal = @s" + sTempLinkFieldName + "\r\n" + "\t" + "PRINT '@sVal: ''' + @sVal + ''''" + "\r\n" + "\t" + "IF not @sVal is null and not @sVal = ''" + "\r\n" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "PRINT '@sVal is not null and not '''''" + "\r\n";
				if (iMode == clC.SELL_LinkProcessMode_ImportID)
				{
					if (par_bIgnoreDuplicateImportIDs)
					{
						//This will link the first record returned with the specified TXT_ImportID. 
						//WARNING: If duplicates exist, this will cause wrong records to be linked!
						//PJ: TXT_ImportID
						sN1Statement += "\t" + "\t" + "SET @uLinkedRecGID = (SELECT TOP 1 [GID_ID] FROM [" + sLinkToFile + "] WHERE [" + par_sLinkKeyField + "] = @sVal)" + "\r\n";
					}
					else
					{
						//This will generate SQL error 512 if multiple records with the same TXT_ImportID are found
						//PJ: TXT_ImportID
						sN1Statement += "\t" + "\t" + "SET @uLinkedRecGID = (SELECT [GID_ID] FROM [" + sLinkToFile + "] WHERE [" + par_sLinkKeyField + "] = @sVal)" + "\r\n";
					}
				}
				else
				{
					//clC.SELL_LinkProcessMode_TN
					sN1Statement += "\t" + "\t" + "SET @uLinkedRecGID = (SELECT [GID_InternalID] FROM [TN] WHERE [TXT_File] = '" + sLinkToFile + "' and [TXT_ExternalSource] = '" + sExternalSourceName + "' and [TXT_ExternalID] = @sVal)" + "\r\n";
				}
				sN1Statement += "\t" + "\t" + "PRINT '@uLinkedRecGID: ''' + CAST(@uLinkedRecGID as varchar(40)) + ''''" + "\r\n" + "\t" + "\t" + "IF not @uLinkedRecGID is null" + "\r\n" + "\t" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "\t" + "PRINT '@uLinkedRecGID is not null'" + "\r\n" + "\t" + "\t" + "\t" + "[1]" + "\r\n" + "\t" + "\t" + "END" + "\r\n" + "\t" + "END" + "\r\n";
			}

			sN1Statement += "\t" + "-- Move to the next record" + "\r\n" + "\t" + "FETCH NEXT FROM LinkUpdate" + "\r\n" + "\t" + "INTO @uGID, @biRec, @s" + sTempLinkFieldName + "\r\n" + "END" + "\r\n" + "\r\n" + "CLOSE LinkUpdate" + "\r\n" + "DEALLOCATE LinkUpdate" + "\r\n" + "\r\n";


			//-------------- CURSOR -----------------
			sNNStatement = sPreStatement + "DECLARE LinkUpdate CURSOR FOR" + "\r\n" + "SELECT [GID_ID], [BI__ID], [" + sTempLinkFieldName + "]" + "\r\n" + "FROM [" + sFileName + "]" + "\r\n" + sWhere + "\r\n" + "ORDER BY [BI__ID] ASC" + "\r\n" + "\r\n" + "OPEN LinkUpdate" + "\r\n" + "\r\n" + "FETCH NEXT FROM LinkUpdate" + "\r\n" + "INTO @uGID, @biRec, @s" + sTempLinkFieldName + "\r\n" + "\r\n" + "WHILE @@FETCH_STATUS = 0" + "\r\n" + "BEGIN" + "\r\n" + "\t" + "PRINT '------'" + "\r\n" + "\t" + "PRINT '@biRec: ''' + CAST(@biRec as varchar(30)) + ''''" + "\r\n";

			if (iMode == clC.SELL_LinkProcessMode_GID_ID)
			{
				//GID_ID mode
				sNNStatement += "\t" + "SET @sVal = '" + sGID_ID + "'" + "\r\n" + "\t" + "PRINT '@sVal: ' + @sVal" + "\r\n" + "\t" + "IF not @sVal is null and not @sVal = ''" + "\r\n" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "SET @uLinkedRecGID = (CAST(@sVal as uniqueidentifier))" + "\r\n" + "\t" + "\t" + "PRINT '@uLinkedRecGID: ' + CAST(@uLinkedRecGID as varchar(40))" + "\r\n" + "\t" + "\t" + "IF not @uLinkedRecGID is null" + "\r\n" + "\t" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "\t" + "PRINT '@uLinkedRecGID is not null'" + "\r\n" + "\t" + "\t" + "\t" + "[1]" + "\r\n" + "\t" + "\t" + "END" + "\r\n" + "\t" + "END" + "\r\n";
				//[1] is replaced below with a variable statement

			}
			else
			{
				//TXT_ImportID and TN modes
				sNNStatement += "\t" + "-- Get the position of the first CR and determine what constitutes a CR." + "\r\n" + "\t" + "-- This is to process imported data that may contain a complete CR" + "\r\n" + "\t" + "-- (CHAR(13) + CHAR(10)) or just CHAR(13) or CHAR(10)." + "\r\n" + "\t" + "SET @sCRString = CHAR(13) + CHAR(10)" + "\r\n" + "\t" + "SET @sCRChars = 'CHAR(13) + CHAR(10)'" + "\r\n" + "\t" + "SET @iCRLength = 2" + "\r\n" + "\t" + "SET @biPos = charindex(@sCRString, @s" + sTempLinkFieldName + ")" + "\r\n" + "\t" + "IF @biPos < 1" + "\r\n" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "-- Look for CHAR(10) + CHAR(13)" + "\r\n" + "\t" + "\t" + "SET @sCRString = CHAR(10) + CHAR(13)" + "\r\n" + "\t" + "\t" + "SET @sCRChars = 'CHAR(10) + CHAR(13)'" + "\r\n" + "\t" + "\t" + "SET @iCRLength = 2" + "\r\n" + "\t" + "\t" + "SET @biPos = charindex(@sCRString, @s" + sTempLinkFieldName + ")" + "\r\n" + "\t" + "\t" + "IF @biPos < 1" + "\r\n" + "\t" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "\t" + "-- Look for just CHAR(13)" + "\r\n" + "\t" + "\t" + "\t" + "SET @sCRString = CHAR(13)" + "\r\n" + "\t" + "\t" + "\t" + "SET @sCRChars = 'CHAR(13)'" + "\r\n" + "\t" + "\t" + "\t" + "SET @iCRLength = 1" + "\r\n" + "\t" + "\t" + "\t" + "SET @biPos = charindex(@sCRString, @s" + sTempLinkFieldName + ")" + "\r\n" + "\t" + "\t" + "\t" + "IF @biPos < 1" + "\r\n" + "\t" + "\t" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "\t" + "\t" + "-- Look for just CHAR(10)" + "\r\n" + "\t" + "\t" + "\t" + "\t" + "SET @sCRString = CHAR(10)" + "\r\n" + "\t" + "\t" + "\t" + "\t" + "SET @sCRChars = 'CHAR(10)'" + "\r\n" + "\t" + "\t" + "\t" + "\t" + "SET @iCRLength = 1" + "\r\n" + "\t" + "\t" + "\t" + "\t" + "SET @biPos = charindex(@sCRString, @s" + sTempLinkFieldName + ")" + "\r\n" + "\t" + "\t" + "\t" + "END" + "\r\n" + "\t" + "\t" + "END" + "\r\n" + "\t" + "END" + "\r\n" + "\t" + "PRINT '@sCRChars: ' + @sCRChars" + "\r\n" + "\t" + "PRINT '@biPos: ' + CAST(@biPos as varchar(30))" + "\r\n" + "\r\n" + "\t" + "IF @biPos < 1 " + "\r\n" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "-- CR not found, limit what we get to first 80 characters, enough for a system name" + "\r\n" + "\t" + "\t" + "SET @sVal = Substring(@s" + sTempLinkFieldName + ", 1, 80)" + "\r\n" + "\t" + "END" + "\r\n" + "\t" + "ELSE" + "\r\n" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "SET @sVal = Substring(@s" + sTempLinkFieldName + ", 1, @biPos-1)" + "\r\n" + "\t" + "END" + "\r\n" + "\t" + "PRINT '@sVal: ' + @sVal" + "\r\n" + "\t" + "IF not @sVal is null and not @sVal = ''" + "\r\n" + "\t" + "BEGIN" + "\r\n";
				if (iMode == clC.SELL_LinkProcessMode_ImportID)
				{
					if (par_bIgnoreDuplicateImportIDs)
					{
						//This will link the first record returned with the specified TXT_ImportID. 
						//WARNING: If duplicates exist, this will cause wrong records to be linked!
						//PJ: TXT_ImportID
						sNNStatement += "\t" + "\t" + "SET @uLinkedRecGID = (SELECT TOP 1 [GID_ID] FROM [" + sLinkToFile + "] WHERE [" + par_sLinkKeyField + "] = @sVal)" + "\r\n";
					}
					else
					{
						//This will generate SQL error 512 if multiple records with the same TXT_ImportID are found
						//PJ: TXT_ImportID
						sNNStatement += "\t" + "\t" + "SET @uLinkedRecGID = (SELECT [GID_ID] FROM [" + sLinkToFile + "] WHERE [" + par_sLinkKeyField + "] = @sVal)" + "\r\n";
					}
				}
				else
				{
					//clC.SELL_LinkProcessMode_TN
					sNNStatement += "\t" + "\t" + "SET @uLinkedRecGID = (SELECT [GID_InternalID] FROM [TN] WHERE [TXT_File] = '" + sLinkToFile + "' and [TXT_ExternalSource] = '" + sExternalSourceName + "' and [TXT_ExternalID] = @sVal)" + "\r\n";
				}
				sNNStatement += "\t" + "\t" + "PRINT '@uLinkedRecGID: ' + CAST(@uLinkedRecGID as varchar(40))" + "\r\n" + "\t" + "\t" + "IF not @uLinkedRecGID is null" + "\r\n" + "\t" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "\t" + "PRINT '@uLinkedRecGID is not null'" + "\r\n" + "\t" + "\t" + "\t" + "[1]" + "\r\n" + "\t" + "\t" + "END" + "\r\n" + "\t" + "END" + "\r\n" + "\r\n" + "\t" + "-- Loop through the rest of the CR-delimited values" + "\r\n" + "\t" + "WHILE @biPos > 0" + "\r\n" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "-- Get the position of CR" + "\r\n" + "\t" + "\t" + "SET @biNewPos = charindex(@sCRString, @s" + sTempLinkFieldName + ", @biPos + @iCRLength)" + "\r\n" + "\t" + "\t" + "PRINT '@biNewPos: ' + CAST(@biNewPos as varchar(30))" + "\r\n" + "\t" + "\t" + "IF @biNewPos < 1" + "\r\n" + "\t" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "\t" + "-- Return the rest of the memo (a name is not expected to be > 80 characters)" + "\r\n" + "\t" + "\t" + "\t" + "SET @sVal = Substring(@s" + sTempLinkFieldName + ", @biPos + @iCRLength, @biPos + 80 + @iCRLength)" + "\r\n" + "\t" + "\t" + "END" + "\r\n" + "\t" + "\t" + "ELSE" + "\r\n" + "\t" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "\t" + "-- Return the next CR-delimited value" + "\r\n" + "\t" + "\t" + "\t" + "SET @sVal = Substring(@s" + sTempLinkFieldName + ", @biPos + @iCRLength, @biNewPos - @biPos - @iCRLength)" + "\r\n" + "\t" + "\t" + "END" + "\r\n" + "\t" + "\t" + "PRINT '@sVal: ' + @sVal" + "\r\n" + "\t" + "\t" + "IF not @sVal is null and not @sVal = ''" + "\r\n" + "\t" + "\t" + "BEGIN" + "\r\n";
				if (iMode == clC.SELL_LinkProcessMode_ImportID)
				{
					if (par_bIgnoreDuplicateImportIDs)
					{
						//This will link the first record returned with the specified TXT_ImportID. 
						//WARNING: If duplicates exist, this will cause wrong records to be linked!
						//PJ 11/1/11: Replaced TXT_ImportID with par_sLinkKeyField
						sNNStatement += "\t" + "\t" + "SET @uLinkedRecGID = (SELECT TOP 1 [GID_ID] FROM [" + sLinkToFile + "] WHERE [" + par_sLinkKeyField + "] = @sVal)" + "\r\n";
					}
					else
					{
						//This will generate SQL error 512 if multiple records with the same TXT_ImportID are found
						//PJ 11/1/11: Replaced TXT_ImportID with par_sLinkKeyField
						sNNStatement += "\t" + "\t" + "SET @uLinkedRecGID = (SELECT [GID_ID] FROM [" + sLinkToFile + "] WHERE [" + par_sLinkKeyField + "] = @sVal)" + "\r\n";
					}
				}
				else
				{
					//clC.SELL_LinkProcessMode_TN
					sNNStatement += "\t" + "\t" + "SET @uLinkedRecGID = (SELECT [GID_InternalID] FROM [TN] WHERE [TXT_File] = '" + sLinkToFile + "' and [TXT_ExternalSource] = '" + sExternalSourceName + "' and [TXT_ExternalID] = @sVal)" + "\r\n";
				}
				sNNStatement += "\t" + "\t" + "\t" + "PRINT '@uLinkedRecGID: ' + CAST(@uLinkedRecGID as varchar(40))" + "\r\n" + "\t" + "\t" + "\t" + "IF not @uLinkedRecGID is null" + "\r\n" + "\t" + "\t" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "\t" + "\t" + "PRINT '@uLinkedRecGID is not null'" + "\r\n" + "\t" + "\t" + "\t" + "\t" + "[1]" + "\r\n" + "\t" + "\t" + "\t" + "END" + "\r\n" + "\t" + "\t" + "END" + "\r\n" + "\t" + "\t" + "SET @biPos = @biNewPos" + "\r\n" + "\t" + "END" + "\r\n";

				//[1] is replaced with a variable statement below

				//'DEBUG
				//If UCase(par_sTempLinkField) = UCase("TXT_LNK_EMPLOYS_US") Then Stop
				//If UCase(par_sTempLinkField) = UCase("TXT_LNK_TEAMLEADER_US") Then Stop
				//'END DEBUG
			}

			sNNStatement += "\t" + "FETCH NEXT FROM LinkUpdate" + "\r\n" + "\t" + "INTO @uGID, @biRec, @s" + sTempLinkFieldName + "\r\n" + "END" + "\r\n" + "\r\n" + "CLOSE LinkUpdate" + "\r\n" + "DEALLOCATE LinkUpdate" + "\r\n" + "\r\n";



			//--------------- Substitute '[1]': Process N1 and NN links written in TXT_LNK_ and MMO_LNK temp fields ------------
			switch (iDirection)
			{

				case 0:
					//Unsupported direction
					goErr.SetError(35000, sproc, "Unsupported link direction returned by goData.LKGetDirection for '" + sFileName + "." + sLinkName + "'. Only 1 (primary) or 2 (reverse) are supported.");
					break;

				case 1:
					//Direction 1
					if (sTableToWriteTo.IndexOf("_") + 1 < 1)
					{

						//N1 primary direction link
						//The link has 1 on either side. Since 1N definitions are not supported, it can only be N1.
						sType = "N1";
						//Same for link to different file and same file
						if (goTR.ExtractString(sLinkName, 3, "_") != sFileName)
						{
							//Link to different file, primary direction (Ex: CO.LNK_TeamLeader_US -> CO.GID_TeamLeader_US)
							//*** CURRENTLY SAME CODE AS FOR SAME-FILE LINK ***
							sLinkGIDField = "GID_" + goTR.FromTo(sLinkName, 5, -1);
							sTemp = "-- Write the ID of the linked record in the GID_Link field" + "\r\n" + "\t" + "\t" + "UPDATE [" + sTableToWriteTo + "] SET [" + sLinkGIDField + "] = @uLinkedRecGID WHERE [BI__ID] = @biRec";
							sResult = goTR.Replace(sN1Statement, "[1]", sTemp);
						}
						else
						{
							//Link to same file, primary direction (Ex: CO.LNK_Parent_CO -> CO.GID_Parent_CO)
							//*** CURRENTLY SAME CODE AS FOR DIFFERENT-FILE LINK ***
							sLinkGIDField = "GID_" + goTR.FromTo(sLinkName, 5, -1);
							sTemp = "-- Write the ID of the linked record in the GID_Link field" + "\r\n" + "\t" + "\t" + "UPDATE [" + sTableToWriteTo + "] SET [" + sLinkGIDField + "] = @uLinkedRecGID WHERE [BI__ID] = @biRec";
							sResult = goTR.Replace(sN1Statement, "[1]", sTemp);
						}
					}
					else
					{
						//NN primary direction link - it involves a link table (XX_LinkName_YY)
						if (sLinkToFile != sFileName)
						{
							//Link to different file
							//Primary direction (Ex: CO.LNK_Involves_US -> CO_Involves_US.GID_CO, CO_Involves_US.GID_US)
							//==> TEST
							sLinkGIDField1 = "GID_" + sFileName;
							sLinkGIDField2 = "GID_" + sLinkToFile;
						}
						else
						{
							//Link to same file
							//==> TEST
							//Primary direction (Ex: CO.LNK_Referred_CO -> CO_Referred_CO.GID_CO, CO_Referred_CO.GID_CO2)
							sLinkGIDField1 = "GID_" + sFileName; //GID_CO
							sLinkGIDField2 = "GID_" + sFileName + "2"; //GID_CO2
						}
						sTemp = "-- Create a record in the link table" + "\r\n" + "\t" + "\t" + "\t" + "If not exists (SELECT [GID_ID] FROM [" + sTableToWriteTo + "] WHERE [" + sLinkGIDField1 + "] = @uGID and [" + sLinkGIDField2 + "] = @uLinkedRecGID)" + "\r\n" + "\t" + "\t" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "\t" + "\t" + "INSERT [" + sTableToWriteTo + "] ([" + sLinkGIDField1 + "], [" + sLinkGIDField2 + "]) VALUES (@uGID, @uLinkedRecGID)" + "\r\n" + "\t" + "\t" + "\t" + "\t" + "PRINT 'Record inserted in " + sTableToWriteTo + "'" + "\r\n" + "\t" + "\t" + "\t" + "END";
						sResult = goTR.Replace(sNNStatement, "[1]", sTemp);
					}
					break;

				default:
					//Direction 2

					if (sTableToWriteTo.IndexOf("_") + 1 < 1)
					{
						//N1 reverse direction link - it does NOT involve a link table (XX_LinkName_YY), but 
						//there are multiple values in a MMO_LNK_ field, so it's treated as an NN link
						if (sLinkToFile != sFileName)
						{
							//Link to different file
							//*** THIS IS THE SAME AS SAME-FILE LINK FOR NOW ***
							//Reverse direction (Ex: CO.LNK_Connected_CN -> CN.GID_Related_CO)
							//EXAMPLE:
							//-- Update GID_Link field in the linked record in the other table
							//SET @uGID = (SELECT GID_ID FROM CO WHERE BI__ID = @biRec)
							//UPDATE CN SET GID_Related_CO = @uGID WHERE GID_ID = @uLinkedRecGID
							sInverseName = goData.LKGetInverseName(sFileName, sLinkName);
							sLinkGIDField = "GID_" + goTR.FromTo(sInverseName, 5, -1);
							sTemp = "-- Update GID_Link field in the linked record in the other table" + "\r\n" + "\t" + "\t" + "\t" + "UPDATE [" + sTableToWriteTo + "] SET [" + sLinkGIDField + "] = @uGID WHERE [GID_ID] = @uLinkedRecGID";
							sResult = goTR.Replace(sNNStatement, "[1]", sTemp);
						}
						else
						{
							//Link to same file - no link table, but multiple values are in a MMO_LNK_ field,
							//so it's treated as an NN link
							//Reverse direction (Ex: LNK_Subsidiary_CO -> CO.GID_Parent_CO)
							//*** THIS IS THE SAME AS LINK TO DIFF FILE FOR NOW ***
							//EXAMPLE:
							//-- Update GID_Link field in the linked record in the same table
							//SET @uGID = (SELECT GID_ID FROM CO WHERE BI__ID = @biRec)
							//UPDATE CO SET GID_Parent_CO = @uGID WHERE GID_ID = @uLinkedRecGID
							sInverseName = goData.LKGetInverseName(sFileName, sLinkName);
							sLinkGIDField = "GID_" + goTR.FromTo(sInverseName, 5, -1);
							sTemp = "-- Update GID_Link field in the linked record in the same table" + "\r\n" + "\t" + "\t" + "\t" + "UPDATE [" + sTableToWriteTo + "] SET [" + sLinkGIDField + "] = @uGID WHERE [GID_ID] = @uLinkedRecGID";
							sResult = goTR.Replace(sNNStatement, "[1]", sTemp);
						}
					}
					else
					{
						//NN reverse direction link - it involves a link table (XX_LinkName_YY)
						if (sLinkToFile != sFileName)
						{
							//Link to different file
							//Reverse direction (Ex: CO.LNK_Involves_CN -> CN_InvolvedWith_CO.GID_CO, CN_InvolvedWith_CO.GID_CN)
							//==> TEST
							sLinkGIDField1 = "GID_" + sFileName;
							sLinkGIDField2 = "GID_" + sLinkToFile;
						}
						else
						{
							//Link to same file
							//==> TEST
							//Reverse direction (Ex: CO.LNK_ReferredBy_CO -> CO_Referred_CO.GID_CO2, CO_Referred_CO.GID_CO)
							sLinkGIDField1 = "GID_" + sFileName + "2";
							sLinkGIDField2 = "GID_" + sFileName;
						}
						sTemp = "-- Create a record in the link table" + "\r\n" + "\t" + "\t" + "\t" + "If not exists (SELECT [GID_ID] FROM [" + sTableToWriteTo + "] WHERE [" + sLinkGIDField1 + "] = @uGID and [" + sLinkGIDField2 + "] = @uLinkedRecGID)" + "\r\n" + "\t" + "\t" + "\t" + "BEGIN" + "\r\n" + "\t" + "\t" + "\t" + "\t" + "INSERT [" + sTableToWriteTo + "] ([" + sLinkGIDField1 + "], [" + sLinkGIDField2 + "]) VALUES (@uGID, @uLinkedRecGID)" + "\r\n" + "\t" + "\t" + "\t" + "\t" + "PRINT 'Record inserted in " + sTableToWriteTo + "'" + "\r\n" + "\t" + "\t" + "\t" + "END";
						sResult = goTR.Replace(sNNStatement, "[1]", sTemp);
					}
					break;

			}

			return sResult;

		}

		private string WrapWithColumnCheckingStatement(string par_sFileName, string par_sFieldName, string par_sStatement)
		{
			//MI 2/20/07 Created
			//PURPOSE:
			//       Prepend an UPDATE statement with code that checks whether the column
			//       in UPDATE exists.
			//       Called from summary field processing code in ImportProcessFieldsInTable2.
			//PARAMETERS:
			//       par_sFilename: Table name of the UPDATE.
			//       par_sFieldName: Column name being UPDATEd.
			//       par_sStatement: UPDATE statement itself.
			//RETURNS:
			//       Complete statement as string.

			string sProc = "clSchema::WrapWithColumnCheckingStatement";
			goLog.Log(sProc, "Start par_sFileName: '" + par_sFileName + "' par_sFieldName: '" + par_sFieldName + "' par_sStatement: '" + par_sStatement.Substring(0, 2000) + "'", (short)clC.SELL_LOGLEVEL_DEBUG, true);


			string sStatement = goTR.PrepareForSQL(par_sStatement);
			string sResult;

			sResult = "IF EXISTS (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS" + "\r\n" + "WHERE TABLE_NAME = '" + par_sFileName + "' and COLUMN_NAME = '" + par_sFieldName + "')" + "\r\n" + "BEGIN" + "\r\n" + "EXECUTE ('" + sStatement + "')" + "\r\n" + "END";

			return sResult;

		}

		public bool ConvertDateTimes(string par_sFileName, int par_iTimeZoneOffset)
		{
			//MI 10/31/07 Modified comment, added @par_uUserID parameter
			//MI 9/19/07
			//PURPOSE:
			//       Runs pAdjustDateTimes to convert local datetimes after import to UTC (GMT)
			//       values for all users. The conversion is designed to be run once for all records in all files. 
			//       If you run it multiple times, for example after reimporting and merging additional 
			//       data, only the unconverted records will be converted. Here's how pAdjustDateTimes
			//       works: After each file is processed, it writes the highest processed BI__ID to the 
			//       GLOBAL MD page 'OTH_DATETIMEADJUSTMENT'. It processes only records with BI__ID > 
			//       the pointer in OTH_DATETIMEADJUSTMENT. However, if the process is interrupted on 
			//       the server (closing the browser will not interrupt the process), records within 
			//       one file could be processed partially. If this happens, either reimport/reprocess 
			//       the data in the affected table (easier), or adjust the data manually with UPDATE 
			//       queries in QA. pAdjustDateTimes supports converting datetimes user by user. This
			//       method supports only all users.
			//       *** IMPORTANT: ***
			//       pAdjustDateTimes does not take into account daylight savings changes in the offset. 
			//       If it is critical that the offset be observed, run AdjustDSTOffset.sql (for US 
			//       time zones) or AdjustDSTOffsetEurope.sql (for all time zones that use the Europe 
			//       DST rules). This will affect all users. If it is important to process DST periods 
			//       correctly for users with different DST rules, you will have to write custom scripts.
			//       To adjust individual user's time zone offsets, run pAdjustDateTimes, but set the 
			//       minutes parameter to the difference between the offset of the main time zone for
			//       which you ran the adjustment above and the user's time zone offset. If the user 
			//       is east of the main time zone, make the number negative, else make it positive.
			//       For example, if you used 360 above (CST) and the user is in the US Eastern zone, 
			//       run this:
			//           DECLARE @iResult int 
			//           -- Adjust times 1 hour backward (for US Eastern time) for one user
			//           EXEC @iResult = pAdjustDateTimes -60, 0, 'PutUserGID_IDHere'
			//           Print 'Result: ''' + CAST(@iResult as varchar(40)) + '''' 
			//           GO
			//       Remember, here you are adjusting the already converted GMT values. You have to shift
			//       GMT values for the amount that the user is away from your main time zone, but in the
			//       opposite direction.
			//PARAMETERS:
			//       par_sFileName: Unsupported. All files are processed. Only the records with BI__ID
			//           greater than the pointers written in OTH_DATETIMEADJUSTMENT MD are processed.
			//           If you need to reprocess a range of records, edit the OTH_DATETIMEADJUSTMENT
			//           pointers manually.
			//       par_iTimeZoneOffset: Number of minutes by which to adjust datetimes. Use a positive
			//           number if the imported data was created west of the GMT time zone and negative
			//           number for zones east of GMT. Example: to adjust datetimes from the US Central 
			//           time with daylight savings, enter 300 (5 hours). For US CST (no daylight savings)
			//           enter 360 (6 hours). For most EU countries, enter -120 or -60. See 'Important'
			//           above for information about the daylight savings considerations.
			//RETURNS:
			//       True if successful, False otherwise.

			string sProc = "clSchema::ConvertDateTimes";
			goLog.Log(sProc, "Start par_sFileName: '" + par_sFileName + "' par_iTimeZoneOffset: '" + par_iTimeZoneOffset.ToString() + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			bool bResult = true;
			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			string sProgress = null;
			DateTime dtStarted = goTR.NowUTC();

			//Try

			HttpContext.Current.Session.Timeout = 40000; //almost 4 weeks

				//Clear existing import progress logs in metadata
				goMeta.PageDelete("GLOBAL", "OTH_IMPORT_PROGRESS");

				sProgress = "Datetime conversion to GMT started";
				goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "ConvertDateTimes", sProgress);

				cmd.CommandText = "pAdjustDateTimes";
				//Returns no data, just the result
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				//parameter
				System.Data.SqlClient.SqlParameter paramMinutes = new System.Data.SqlClient.SqlParameter("@par_iMinutes", System.Data.SqlDbType.Int);
				paramMinutes.Value = par_iTimeZoneOffset;
				cmd.Parameters.Add(paramMinutes);

				//parameter
				System.Data.SqlClient.SqlParameter paramForce = new System.Data.SqlClient.SqlParameter("@par_bForce", System.Data.SqlDbType.Bit);
				paramForce.Value = 0;
				cmd.Parameters.Add(paramForce);

				//parameter
				System.Data.SqlClient.SqlParameter paramUser = new System.Data.SqlClient.SqlParameter("@par_uUserID", System.Data.SqlDbType.UniqueIdentifier);
				paramUser.Value = System.DBNull.Value;
				cmd.Parameters.Add(paramUser);

				cmd.ExecuteNonQuery();

				sqlConnection1.Close();

				sProgress = "Datetime conversion to GMT ended (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtStarted, goTR.NowUTC()) + " mins)";
				goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "ConvertDateTimes_End", sProgress);

			//HttpContext.Current.Session.Timeout = 30

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If
			//End Try

			return bResult;

		}


		public bool ImportProcessFields(string par_sFileName = "", int par_iRecs = 20, long par_lFromBIID = -1, long par_lToBIID = -1, bool par_bLinksOnly = false, bool par_bIgnoreDuplicateImportIDs = false, string par_sImpJobID = "", int par_iLinkProcessingMode = clC.SELL_LinkProcessMode_ImportID, string par_sInstructionsIni = "")
		{
			//MI 3/30/09 Added par_iLinkProcessingMode and par_sInstructionsIni parameters.
			//MI 3/4/09 Added par_sImpJobID to support processing field for a single import job only. 
			//MI 8/7/08 added par_bIgnoreDuplicateImportIDs to allow processing links when there
			//       are records in linked tables that have duplicate TXT_ImportID values.
			//MI 2/19/08 added ability to process only links.
			//MI 12/10/07 Added support for from and to file.
			//MI 11/3/06

			//PURPOSE:
			//       Populate datetime and numeric fields and create links from data
			//       in temporary TXT and MMO fields that were created and 
			//       populated by data exported from an existing customer.
			//       For example, in the AC file, process the value in
			//       TXT_DTT_StartTime and populate the equivalent permanent
			//       field DTT_StartTime. Files "MD", "TN", "XL" are skipped
			//       as they don't hold data, but metadata, transfer log and
			//       message log, respectively.
			//       Before this is run, the schema needs to be expanded with
			//       temporary fields by running goData.SchemaAddFields(), and
			//       data must be imported using DTS (in SS's EM).
			//       Run ImportUpdateSysNames after this.
			//       You can remove temporary fields by running
			//       goData.SchemaRemoveFields().
			//PARAMETERS:
			//       par_sFileName: Name of the from and to file, pipe-delimited,
			//           example: CN|MS will cause files between and including
			//           CN and MS to be processed. If blank, the process runs 
			//           for all files. If the parameter is not blank and 
			//           from and to files are the same, par_lFromBIID and 
			//           par_lToBIID parameters can be used to limit the
			//           records that are processed.
			//       par_iRecs: *** Ignored since a SQL solution was implemented ***
			//           No. of fields to process as a batch. The higher
			//           the number the more memory the server will need to 
			//           allocate, adversely affecting the server performance.
			//           A small number slows down the process because a rowset
			//           has to be instantiated more times. The server, however,
			//           is offloaded more that way.
			//       par_lFromBIID: BI__ID value of the record to start from.
			//           If unspecified or -1, the processing starts at the first record
			//           in the table according to the BI__ID asc sort.
			//           Ignored if par_sFileName is blank.
			//       par_lToBIID: BI__ID value of the record to finish at.
			//           Ignored if par_sFileName is blank.
			//           If unspecified or -1, processing continues to the last record
			//           in the table according to the BI__ID asc sort.
			//       par_bLinksOnly: When true (default is False), only links get
			//           processed. 
			//       par_bIgnoreDuplicateImportIDs: If True (default is False), links will be established
			//           even if duplicate TXT_ImportID values in linked tables are found. This will
			//           cause mislinking so use this option only when you can't edit exported data.
			//       par_sImpJobID: If not blank, fields will be processed only in records where
			//           TXT_ImpJobID = this parameter. The value of this parameter must not exceed
			//           20 characters. If it does, an error is generated and processing is canceled.
			//       par_iLinkProcessingMode: Supported values are:
			//           1 or clc.SELL_LinkProcessMode_ImportID
			//           2 or clc.SELL_LinkProcessMode_GID_ID
			//           4 or clc.SELL_LinkProcessMode_Mixed
			//           See ImportProcessFieldsInTable2 for more information.
			//       par_sInstructionsIni: ini string that defines how to process each group of links.
			//           Optional in par_iMode 1. Required in par_iMode 2 and 3. For more information,
			//           see ImportProcessFieldsInTable2.
			//RETURNS:
			//       True if successful, False otherwise.

			string sProc = "clSchema::ImportProcessFields";
			goLog.Log(sProc, "Start par_sFileName: '" + par_sFileName + "' par_iRecs: '" + par_iRecs.ToString() + "' par_lFromBIID: '" + par_lFromBIID.ToString() + "' par_lToBIID: '" + par_lToBIID.ToString() + "' par_bLinksOnly: '" + par_bLinksOnly.ToString() + "' par_bIgnoreDuplicateImportIDs: '" + par_bIgnoreDuplicateImportIDs.ToString() + "' par_sImpJobID: '" + par_sImpJobID + "' par_iLinkProcessingMode: '" + par_iLinkProcessingMode.ToString() + "' par_sInstructionsIni: '" + par_sInstructionsIni + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			bool bResult = true;
			clArray aFiles = new clArray();
			string sFile = "";
			int i = 0;
			string sProgress = null;
			DateTime dtStarted = goTR.NowUTC();
			string sFromFile = "";
			string sToFile = "";
			bool bInclude = false;

			HttpContext.Current.Session.Timeout = 40000; //almost 4 weeks

			//Clear existing import progress logs in metadata
			goMeta.PageDelete("GLOBAL", "OTH_IMPORT_PROGRESS");

			sProgress = "Field and link processing started";
			goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Processing", sProgress);

			bInclude = false;

			if (par_sFileName == "")
			{
				//All files
				sFromFile = "AA";
				sToFile = "ZZ";
				bInclude = true;
			}
			else
			{
				//Validate file names
				sFromFile = (goTR.ExtractString(par_sFileName, 1, "|")).ToUpper();
				if (!goData.IsFileValid(sFromFile))
				{
					goErr.SetError(10100, sProc, "", sFromFile);
					//E10100: Invalid file name '[1]'. [2]
					return false;
				}
				sToFile = (goTR.ExtractString(par_sFileName, 2, "|")).ToUpper();
				if (!goData.IsFileValid(sToFile))
				{
					goErr.SetError(10100, sProc, "", sToFile);
					//E10100: Invalid file name '[1]'. [2]
					return false;
				}
			}

			//Process
			if (sFromFile == sToFile)
			{
				//One file
				bResult = ImportProcessFieldsInTable2(sFromFile, par_iRecs, par_lFromBIID, par_lToBIID, par_bLinksOnly, par_bIgnoreDuplicateImportIDs, par_sImpJobID, par_iLinkProcessingMode, par_sInstructionsIni);
			}
			else
			{
				//Multiple files
				aFiles = goData.GetFiles();
				for (i = 1; i <= aFiles.GetDimension(); i++)
				{
					sFile = aFiles.GetInfo(i).ToUpper();
					//If this the 'From' file, start processing
					if (sFile == sFromFile)
					{
						bInclude = true;
					}
					if (!bInclude)
					{
						goto ProcessNextFile;
					}
					switch (sFile)
					{
						case "MD":
						case "TN":
						case "XL":
						case "XP":
						case "XU":
							goto ProcessNextFile;
					}
					//Return True only if SchemaAddFieldsToTable returned true in all loops
					//Instructions parameter is not supported here because it allows defining modes and links for one file only. ImportID is the only
					//supported processing method in 'All files' case. ImportTasks.aspx page enforces that.
					if (!ImportProcessFieldsInTable2(sFile, par_iRecs, -1, -1, par_bLinksOnly, par_bIgnoreDuplicateImportIDs, par_sImpJobID, par_iLinkProcessingMode))
					{
						bResult = false;
					}
	ProcessNextFile:
					//If this the 'To' file, exit the loop
					if (sFile == sToFile)
					{
						break;
					}
				}
			}

			//HttpContext.Current.Session.Timeout = 30

			sProgress = "Field and link processing ended (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtStarted, goTR.NowUTC()) + " mins)";
			goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Processing_End", sProgress);

			return bResult;

		}


		private bool ImportProcessFieldsInTable(string par_sFileName, int par_iRecs = 20)
		{
			//MI 11/3/06
			//PURPOSE:
			//       *** DO NOT USE THIS. This is the slow rowset version (2 recs per second).
			//       See ImportProcessFieldsInTable2() for a dynamic UPDATE query version
			//       (1440 recs per second). ***
			//       Populate permanent datetime, numeric, and link fields from temporary
			//       import fields (for example DTT_StartTime from TXT_DTT_StartTime).
			//       See notes in ImportProcessFields(), which calls this method.
			//PARAMETERS:
			//       par_sFileName: Name of the table, i.e. 'AC', 'CN', or 'OP'.
			//       par_iRecs: number of records to process in a batch. Default is 20.
			//           A large number will cause the server to use more memory and 
			//           take longer to finish a batch.
			//RETURNS:
			//       True if successful, False if anything failed.

			//==> FINISH

			string sproc = "clSchema::ImportProcessFieldsInTable";
			goLog.Log(sproc, "Start: par_sFileName: '" + par_sFileName + "' par_iRecs: '" + par_iRecs.ToString() + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			bool bResult = true;
			clArray aFields = new clArray();
			int j = 0;
			string sField = null;
			string sPermField = null;
			long l = 0;
			string sPrefix = null;
			string sTemp = null;
			string sDay = null;
			string sMonth = null;
			string sYear = null;
			string sHour = null;
			string sMinute = null;
			string sAMPM = null;
			int iHour = 0;
			long lBI__ID = -1;
			int iIterationsDone = 0;
			string sProgress = null;
			DateTime dtStarted = goTR.NowUTC();
			clRowSet doRS = null;

			//Try

			if (!goData.IsFileValid(par_sFileName))
			{
					goErr.SetError(10100, sproc, "", par_sFileName);
					//E10100: Invalid file name '[1]'. [2]
					return false;
				}
				//----------------------- Process fields -------------------------
				goData.LoadFieldData();

				//doRS = New clRowSet(par_sFileName, clC.SELL_EDIT, , "SYS_Name ASC", "*")

				aFields = goData.GetFields(par_sFileName);
				iIterationsDone = 0;
				//==> DEBUG
				//lBI__ID = 196081
				//END DEBUG

				do
				{
					//==> DEBUG
					//If iIterationsDone > 2 Then Exit Do
					//END DEBUG
					//Get the records. This is forcing no links. gop.sRunMode MUST be set to "Import" prior to this
					//or Commit will fail due to RecordOnSave script attempting to evaluate links.
					//doRS = New clRowSet(par_sFileName, clC.SELL_EDIT, "TXT_ImportID = '' or TXT_ImportID <> ''", "TXT_ImportID ASC", "NOLINKS", 5000)
					doRS = new clRowSet(par_sFileName, clC.SELL_EDIT, "BI__ID > " + lBI__ID, "BI__ID ASC", "NOLINKS", par_iRecs, "", "", "", "", "", true, true);
					if (doRS.Count() < 1)
					{
						if (iIterationsDone == 0)
						{
							//No records found
							goErr.SetWarning(35000, sproc, "There are no records to process in file '" + par_sFileName + "'.");
							return false;
						}
						else
						{
							//No more records found
							break;
						}
					}
					goP.sRunMode = clC.SELL_MODE_IMPORT;
					//Loop through records
					do
					{
						//Loop through temp fields
						for (j = 1; j <= aFields.GetDimension(); j++)
						{
							sField = aFields.GetInfo(j);
							//Exclude permanent fields
							if (goTR.FromTo(sField, 4, 4) == "_" && goTR.FromTo(sField, 8, 8) == "_")
							{
								//Temp field found
							}
							else
							{
								//Not a temp field
								goto ProcessNextField;
							}
							//'Exclude links in this pass
							//If goTR.FromTo(sField, 5, 8) = "LNK_" Then GoTo ProcessNextField
							sPermField = goTR.FromTo(sField, 5, -1);
							sPrefix = goTR.GetPrefix(sPermField);

							//==> DEBUG
							//If sPrefix = "DTT_" Then Stop

							switch (sPrefix)
							{
								case "BI__":
									if (sPermField.ToUpper() != "BI__ID")
									{
										//BI__ID is maintained by SS
										//doRS.SetFieldVal(sPermField, goTR.StringToNum(doRS.GetFieldVal(sField)), clC.SELL_SYSTEM)
										doRS.SetFieldVal(sPermField, goTR.Replace(Convert.ToString(doRS.GetFieldVal(sField)), ",", ""), clC.SELL_SYSTEM);
									}
									break;
								case "CHK_":
									sTemp = Convert.ToString(doRS.GetFieldVal(sField)).ToUpper();
									if (sTemp == "TRUE") //Or sTemp = "YES" Or sTemp = "1" Or sTemp = "CHECKED" Then
									{
										doRS.SetFieldVal(sPermField, true, clC.SELL_SYSTEM);
									}
									else
									{
										doRS.SetFieldVal(sPermField, false, clC.SELL_SYSTEM);
									}
									break;
								case "CMB_":
								case "LST_":
								case "SEL_":
									doRS.SetFieldVal(sPermField, goTR.Replace(Convert.ToString(doRS.GetFieldVal(sField)), ",", ""), clC.SELL_SYSTEM);
									break;
								case "CUR_":
									doRS.SetFieldVal(sPermField, goTR.Replace(goTR.Replace(Convert.ToString(doRS.GetFieldVal(sField)), ",", ""), "$", ""), clC.SELL_SYSTEM);
									break;
								case "DR__":
									doRS.SetFieldVal(sPermField, goTR.Replace(Convert.ToString(doRS.GetFieldVal(sField)), ",", ""), clC.SELL_SYSTEM);
									break;
								case "DTE_":
									sTemp = Convert.ToString(doRS.GetFieldVal(sField));
									if (sTemp == "")
									{
										doRS.SetFieldVal(sPermField, clC.SELL_BLANK_DATETIME);
									}
									else
									{
										sMonth = goTR.Pad(goTR.ExtractString(sTemp, 1, "/"), 2, "0", "L", true);
										sDay = goTR.Pad(goTR.ExtractString(sTemp, 2, "/"), 2, "0", "L", true);
										sYear = goTR.Pad(goTR.ExtractString(sTemp, 3, "/"), 4, "0", "L", true);
										doRS.SetFieldVal(sPermField, sYear + "-" + sMonth + "-" + sDay);
									}
									break;
								case "DTT_":
									//Not supported - treat as just date
									sTemp = Convert.ToString(doRS.GetFieldVal(sField));
									if (sTemp == "")
									{
										doRS.SetFieldVal(sPermField, clC.SELL_BLANK_DATETIME);
									}
									else
									{
										sMonth = goTR.Pad(goTR.ExtractString(sTemp, 1, "/"), 2, "0", "L", true);
										sDay = goTR.Pad(goTR.ExtractString(sTemp, 2, "/"), 2, "0", "L", true);
										sYear = goTR.Pad(goTR.ExtractString(sTemp, 3, "/"), 4, "0", "L", true);
										doRS.SetFieldVal(sPermField, sYear + "-" + sMonth + "-" + sDay);
									}
									break;
								case "INT_":
									//doRS.SetFieldVal(sPermField, goTR.StringToNum(doRS.GetFieldVal(sField)), clC.SELL_SYSTEM)
									doRS.SetFieldVal(sPermField, goTR.Replace(Convert.ToString(doRS.GetFieldVal(sField)), ",", ""), clC.SELL_SYSTEM);
									break;
								case "LI__":
									//doRS.SetFieldVal(sPermField, goTR.StringToNum(doRS.GetFieldVal(sField)), clC.SELL_SYSTEM)
									doRS.SetFieldVal(sPermField, goTR.Replace(Convert.ToString(doRS.GetFieldVal(sField)), ",", ""), clC.SELL_SYSTEM);
									break;
								case "LNK_":
								break;
									//==> Implement
								case "MLS_":
									//sTemp = goData.goList.LReadSeek(par_sFileName & ":" & goTR.RemovePrefix(sPermField), "VALUE", doRS.GetFieldVal(sField))
									doRS.SetFieldVal(sPermField, doRS.GetFieldVal(sField), clC.SELL_FRIENDLY);
									break;
								case "SI__":
									//doRS.SetFieldVal(sPermField, goTR.StringToNum(doRS.GetFieldVal(sField)), clC.SELL_SYSTEM)
									doRS.SetFieldVal(sPermField, goTR.Replace(Convert.ToString(doRS.GetFieldVal(sField)), ",", ""), clC.SELL_SYSTEM);
									break;
								case "SR__":
									//doRS.SetFieldVal(sPermField, goTR.StringToNum(doRS.GetFieldVal(sField)), clC.SELL_SYSTEM)
									doRS.SetFieldVal(sPermField, goTR.Replace(Convert.ToString(doRS.GetFieldVal(sField)), ",", ""), clC.SELL_SYSTEM);
									break;
								case "TME_":
									sTemp = Convert.ToString(doRS.GetFieldVal(sField));
									if (sTemp == "")
									{
										//Leave the date in the permanent DTT field unchanged
									}
									else
									{
										//Write the time
										sTemp = Convert.ToString(doRS.GetFieldVal(sField));
										sHour = goTR.Pad(goTR.ExtractString(sTemp, 1, ":"), 2, "0", "L", true);
										sMinute = goTR.Pad(goTR.ExtractString(goTR.ExtractString(sTemp, 2, ":"), 1, " "), 2, "0", "L", true);
										sAMPM = goTR.ExtractString(sTemp, 2, " ").ToUpper();
										if (sAMPM == "PM")
										{
											iHour = Convert.ToInt32(goTR.StringToNum(sHour));
											iHour = iHour + 12;
											sHour = iHour.ToString();
										}
										doRS.SetFieldVal(sPermField, sHour + ":" + sMinute);
									}
									break;
								default:
									goErr.SetError(35000, sproc, "Unsupported field type (prefix) in field '" + sField + "'. l iteration: '" + l.ToString() + "'; j iteration: '" + j.ToString() + "'.");
									break;
							}
	ProcessNextField: ;
						}
						lBI__ID = Convert.ToInt64(doRS.GetFieldVal("BI__ID", clC.SELL_SYSTEM));
						if (doRS.Commit() != 1)
						{
							goP.sRunMode = clC.SELL_MODE_MAIN;
							goErr.SetError(35000, sproc, "Committing record " + l.ToString() + " (" + Convert.ToString(doRS.GetFieldVal("TXT_ImportID")) + " [" + Convert.ToString(doRS.GetFieldVal("GID_ID")) + "]) failed. Import fields processing stopped.");
							bResult = false;
							break;
						}
						if (doRS.GetNext() != 1)
						{
							break;
						}
					} while (true);
					iIterationsDone = iIterationsDone + 1;
					int tempVar = clC.SELL_TYPE_INVALID;
					string tempVar2 = " ";
					sProgress = iIterationsDone * par_iRecs + " recs done in " + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtStarted, goTR.NowUTC()) + " mins. " + "Current: '" + doRS.GetFieldVal("TXT_ImportID").ToString() + "' (" + doRS.GetFieldVal("BI__ID").ToString() + "). " + "Time (GMT): " + goTR.DateTimeToSysString(goTR.NowUTC(), ref tempVar, ref tempVar2).Substring(0, 19) + ", started " + goTR.DateTimeToSysString(dtStarted).Substring(0, 19) + ".";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Progress" + par_sFileName, sProgress);
				} while (true);


			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sproc)
			//    End If
			//End Try

			goP.sRunMode = clC.SELL_MODE_MAIN;

			return bResult;

		}


		public bool ImportProcessFieldsInTable2(string par_sFileName, int par_iRecs = 20, long par_lFromBIID = -1, long par_lToBIID = -1, bool par_bLinksOnly = false, bool par_bIgnoreDuplicateImportIDs = false, string par_sImpJobID = "", int par_iLinkProcessingMode = clC.SELL_LinkProcessMode_ImportID, string par_sInstructionsIni = "")
		{
			//MI 3/26/09 Enabled support for GID_ID and TN modes.
			//MI 3/9/09 Added par_iLinkProcessingMode and par_sInstructionsIni params.
			//MI 3/4/09 Added par_sImpJobID to support processing field for a single import job only.
			//MI 8/7/08 added par_bIgnoreDuplicateImportIDs to allow processing links when there
			//       are records in linked tables that have duplicate TXT_ImportID values.
			//MI 5/16/08 Added brackets in SQL statement(s).
			//MI 2/19/08 added ability to process only links.
			//MI 2/6/08 Added logging of field-level processing.
			//MI 5/14/07 Enforcing temp field prefix (must be TXT or MMO) to prevent processing an N1 link field like "GID_For_CO" as a temp field
			//MI 5/3/07 Added processing AP SI__StartTimeDay, SI__StartTimeMonth, TXT_StartTimeYear, TD SI__DueTimeDay, SI__DueTimeMonth, TXT_DueTimeYear
			//MI 3/5/07 Added support for TXT_CHK_Urgent -> MLS_Priority processing.
			//MI 2/27/07 Added renaming UI records.
			//MI 2/23/07 Fixed dealing with updating DTT_CreationTime
			//MI 2/19/07 Added: FI renaming, AP.DTT_AlarmTime processing, filling NULL GID_Createdby_US fields.
			//MI 11/9/06
			//PURPOSE:
			//       *** This is a dynamic UPDATE statement version of ImportProcessFieldsInTable. 
			//       For direct fields in ImportID mode, it runs 1440 recs per second vs ImportProcessFieldsInTable
			//       at 2 recs per second. ***
			//       Populates permanent datetime, numeric, and link fields from temporary
			//       import fields (for example DTT_StartTime from TXT_DTT_StartTime).
			//       See notes in ImportProcessFields(), which calls this method.
			//PARAMETERS:
			//       par_sFileName: Name of the table, i.e. 'AC', 'CN', or 'OP'.
			//       par_iRecs: *** Ignored ***
			//           Number of records to process in a batch. Default is 20.
			//           If the processing involved a rowset, a large number would cause 
			//           the server to use more memory and take longer to finish a batch.
			//       par_lFromBIID: BI__ID value of the record from which to perform the update.
			//           If unspecified or -1, records with ID that is greater than or
			//           equal to this value are processed.
			//       par_lToBIID: BI__ID value of the record to which to perform the update.
			//           If unspecified or -1, records with ID that is less than or
			//           equal to this value are processed.
			//       par_bLinksOnly: If true, only links get processed from TXT_LNK_ and MMO_LNK_
			//           fields. Direct fields, summary fields, Created By, etc are NOT processed.
			//       par_bIgnoreDuplicateImportIDs: Pertains only to ImportID par_iLinkProcessingMode. 
			//           Ignored in other modes. If True (default is False), links will be established
			//           even if duplicate TXT_ImportID values in linked tables are found. This will
			//           cause mislinking so use this option only when you can't edit exported data.
			//       par_sImpJobID: If not blank, fields will be processed only in records where
			//           TXT_ImpJobID = this parameter. The value of this parameter must not exceed
			//           20 characters. If it does, an error is generated and processing is canceled.
			//       par_iLinkProcessingMode: Supported values are:
			//           1 or clc.SELL_LinkProcessMode_ImportID: existing method where temp import fields
			//               contain values that are stored in TXT_ImportID fields. par_sInstructionsIni
			//               can be blank or Nothing, or, to process only certain links, it should contain:
			//                  ProcessListedLinksOnly=1
			//                  ImportIDModeLinkCount=<no of links>
			//                  ImportIDModeLink1=<linkname1>
			//                  ImportIDModeLink<n>=<linkname<n>>
			//           2 or clc.SELL_LinkProcessMode_GID_ID: ea link is established based on a GID_ID of
			//               the record in Selltis. This mode requires passing an instruction ini string 
			//               in par_sInstructionsIni parameter with the following lines:
			//                   GID_IDModeLinkCount=<no of links>
			//                   GID_IDModeLink1=<linkname1>
			//                   GID_IDModeLink<n>=<linkname<n>>
			//                   <linkname1>=<GID_ID1>
			//                   <linkname<n>>=<GID_ID<n>>
			//               The rest of the ini string is ignored. Only the defined links are processed. 
			//           4 or clc.SELL_LinkProcessMode_Mixed: this mode allows processing a batch of links
			//               using mode 1 or 'ImportID' (clC.SELL_LinkProcessMode_ImportID), another batch using 
			//               mode 2 or 'GID_ID' (clC.SELL_LinkProcessMode_GID_ID), and the third batch using 
			//               mode 3 or 'TN' (clc.SELL_LinkProcessMode_TN). This mode requires defining an 
			//               instruction ini string in the par_sInstructionsIni parameter. If
			//               it is not defined, all links will be processed using the default mode,
			//               clC.SELL_LinkProcessMode_ImportID.
			//       par_sInstructionsIni: ini string that defines how to process each group of links.
			//           Optional in par_iMode 1. Required in par_iMode 2 and 3. Sample:
			//               ProcessListedLinksOnly=1
			//               DefaultMode=TN
			//               ExternalSourceName=P21
			//               ImportIDModeLinkCount=2
			//               ImportIDModeLink1=LNK_RELATED_VN
			//               ImportIDModeLink2=LNK_CONNECTED_QT
			//               GID_IDModeLinkCount=1
			//               GID_IDModeLink1=LNK_WORKSWITH_CN
			//               LNK_WORKSWITH_CN=37658979-D27C-4E60-434E-960E048F7CB2
			//               LNK_INVOLVES_US=EE3FDA35-88F7-4B75-5553-9A1901F8141E
			//               TNModeLinkCount=1
			//               TNModeLink1=LNK_INVOLVES_CN
			//           Key:
			//               ProcessListedLinksOnly: 1 (default) or 0. If 1, only the links listed in this ini string will be processed and DEFAULTMODE will be ignored.
			//               DEFAULTMODE defines the mode in which all links that are not explicitly defined will be processed unless ProcessListedLinksOnly is 1. In that case, this parameter can be omitted. Supported are values '1' or 'IMPORTID' or '2' or 'GID_ID' or '3' or 'TN':
			//                   1 or IMPORTID   'clc.SELL_LinkProcessMode_ImportID
			//                   2 or GID_ID     'clc.SELL_LinkProcessMode_GID_ID
			//                   3 or TN         'clc.SELL_LinkProcessMode_TN
			//               ExternalSourceName applies to all links and is the name under which data from
			//                   an external data source is tracked in the TN table's TXT_ExternalSource column.
			//               <linkname>=<GID_ID> must be defined for each GID_IDModeLink<n>.
			//RETURNS:
			//       True if successful, False if anything failed.

			string sproc = "clSchema::ImportProcessFieldsInTable2";
			goLog.Log(sproc, "Start par_sFileName: '" + par_sFileName + "' par_lFromBIID: '" + par_lFromBIID.ToString() + "' par_lToBIID: '" + par_lToBIID.ToString() + "' par_bLinksOnly: '" + par_bLinksOnly.ToString() + "' par_bIgnoreDuplicateImportIDs: '" + par_bIgnoreDuplicateImportIDs.ToString() + "' par_sImpJobID: '" + par_sImpJobID + "' par_iLinkProcessingMode: '" + par_iLinkProcessingMode.ToString() + "' par_sInstructionsIni: '" + par_sInstructionsIni + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			bool bResult = true;
			bool bLinksResult = true;
			bool bSummaryResult = true;
			clArray aFields = new clArray();
			int j = 0;
			string sField = null;
			string sPermField = null;
			string sPrefix = null;
			long lBI__ID = -1;
			string sProgress = null;
			DateTime dtStarted = goTR.NowUTC();
			DateTime dtFIStarted = default(DateTime);
			DateTime dtLinksStarted = default(DateTime);
			DateTime dtLinkStarted = default(DateTime);
			DateTime dtSummaryStarted = default(DateTime);
			DateTime dtCreatedByStarted = default(DateTime);
			DateTime dtCreationTimeStarted = default(DateTime);
			DateTime dtModTimeStarted = default(DateTime);
			DateTime dtReportStarted = default(DateTime);
			DateTime dtIUStarted = default(DateTime);
			DateTime dtTDUrgentStarted = default(DateTime);
			string sSQL = null; //Dynamic SQL stamement for populating direct fields
			//Dim sSQLLinksHeader As String   'Declarations and initializations for the links SQL statement
			string sSQLLinks = ""; //Dynamic SQL statement for creating links
			long lFromBIID = par_lFromBIID;
			long lToBIID = par_lToBIID;
			clArray doLinkStatements = new clArray();
			clArray doLinkNames = new clArray(); //Names of links for which SQL statements are added to doLinkStatements
			clArray doSummaryStatements = new clArray();
			clArray doSummaryNames = new clArray();
			int i = 0;
			string sLinkCreationSQL = null;
			int lPos = 0;
			string sOtherField = null;
			string sWhereAppend = "";
			string sText = "";
			string sTempPrefix = "";
			string sImpJobID = null;
			int iLinkProcessingMode = 0;
			int iOneLinkProcessingMode = 0;
			int iDefaultLinkProcessingMode = 0;
			clArray aMode1Links = new clArray();
			clArray aMode2Links = new clArray();
			clArray aMode3Links = new clArray();
			bool bProcessListedLinksOnly = false;
			string sLink = null;

			//'==> DEBUG
			//lFromBIID = 246001
			//lToBIID = 246020
			//'==> END DEBUG

			//Try

			if (!goData.IsFileValid(par_sFileName))
			{
					goErr.SetError(10100, sproc, "", par_sFileName);
					//E10100: Invalid file name '[1]'. [2]
					return false;
				}

				//========================= DIRECT FIELDS =============================

				sProgress = "   -----------------------------------";
				goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Fields_" + par_sFileName + "_Start1", sProgress);

				//------------- Create the common appended portion of the WHERE statement -------------------
				if (lFromBIID == -1)
				{
					//Start from first record
					if (lToBIID == -1)
					{
						//No upper limit
						//No WHERE statement needed
						sWhereAppend = " ";
					}
					else
					{
						//Less than
						sWhereAppend = " AND ([BI__ID] <= " + lToBIID + ") ";
					}
				}
				else
				{
					//Start from a particular record
					if (lToBIID == -1)
					{
						//No upper limit
						sWhereAppend = " AND ([BI__ID] >= " + lFromBIID + ") ";
					}
					else
					{
						//Between
						sWhereAppend = " AND ([BI__ID] >= " + lFromBIID + " AND [BI__ID] <= " + lToBIID + ") ";
					}
				}

				//------------- Add ImpJobID ------------
				if (par_sImpJobID.Length > 20)
				{
					goErr.SetError(35000, sproc, "The value of parameter 'par_sImpJobID' exceeds 20 characters: '" + par_sImpJobID + "'. Processing failed.");
				}
				sImpJobID = par_sImpJobID;
				if (sImpJobID != "")
				{
					sWhereAppend += "AND TXT_ImpJobID = '" + sImpJobID + "' ";
				}

				//----------- Link processing definition ---------------
				iLinkProcessingMode = par_iLinkProcessingMode;
				//Validate the mode. Supported values here are:
				//   clC.SELL_LinkProcessMode_ImportID
				//   clC.SELL_LinkProcessMode_GID_ID
				//   clC.SELL_LinkProcessMode_Mixed
				switch (par_iLinkProcessingMode)
				{
					case clC.SELL_LinkProcessMode_ImportID:
					case clC.SELL_LinkProcessMode_GID_ID:
					case clC.SELL_LinkProcessMode_Mixed:
						//Mode is supported
						iLinkProcessingMode = par_iLinkProcessingMode;
						break;
					default:
						//Unsupported mode
						goErr.SetError(10103, sproc, "", "par_iLinkProcessingMode", "par_iLinkProcessingMode", par_iLinkProcessingMode.ToString() + "'. Supported values are '1', '2', or '4");
						break;
						//Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
				}

				//Enforce par_sInstructionIn in GID_ID and TN modes
				if (iLinkProcessingMode == clC.SELL_LinkProcessMode_GID_ID || iLinkProcessingMode == clC.SELL_LinkProcessMode_Mixed)
				{
					if (par_sInstructionsIni.Trim(' ') == "")
					{
						//Instructions not provided
						goErr.SetError(35000, sproc, "par_sInstructionsIni is blank. It must be defined in processing mode '" + par_sInstructionsIni + "'. Processing failed");
					}
				}

				//Parse instructions and prepare variables
				//Supported instructions:
				//In ImportID mode (1):
				//   ProcessListedLinksOnly=1
				//   ImportIDModeLinkCount=2
				//   ImportIDModeLink1=LNK_RELATED_VN
				//   ImportIDModeLink2=LNK_CONNECTED_QT
				//In GID_ID mode (2):
				//   ProcessListedLinksOnly=1
				//   GID_IDModeLinks=LNK_WORKSWITH_CN|LNK_INVOLVES_US
				//   LNK_WORKSWITH_CN=37658979-D27C-4E60-434E-960E048F7CB2
				//   LNK_INVOLVES_US=EE3FDA35-88F7-4B75-5553-9A1901F8141E
				//In Mixed mode (4):
				//   ProcessListedLinksOnly=1
				//   DEFAULTMODE=TN
				//   ExternalSourceName=P21
				//   ImportIDModeLinks=LNK_RELATED_VN|LNK_CONNECTED_QT|LNK_RELATED_CN
				//   GID_IDModeLinks=LNK_WORKSWITH_CN|LNK_INVOLVES_US
				//   LNK_WORKSWITH_CN=37658979-D27C-4E60-434E-960E048F7CB2
				//   LNK_INVOLVES_US=EE3FDA35-88F7-4B75-5553-9A1901F8141E
				//   TNModeLinks=LNK_INVOLVES_CN

				//Set default processing mode
				iDefaultLinkProcessingMode = iLinkProcessingMode;
				//Get default processing mode in mixed mode only
				if (iLinkProcessingMode == clC.SELL_LinkProcessMode_Mixed)
				{
					switch (goTR.StrRead(par_sInstructionsIni, "DEFAULTMODE", "IMPORTID", false).ToUpper())
					{
						case "1":
						case "IMPORTID":
							iDefaultLinkProcessingMode = clC.SELL_LinkProcessMode_ImportID;
							break;
						case "2":
						case "GID_ID":
							iDefaultLinkProcessingMode = clC.SELL_LinkProcessMode_GID_ID;
							break;
						case "3":
						case "TN":
							iDefaultLinkProcessingMode = clC.SELL_LinkProcessMode_TN;
							break;
						default:
							goErr.SetError(10103, sproc, "", "DEFAULTMODE", "par_sInstructionsIni", goTR.StrRead(par_sInstructionsIni, "DEFAULTMODE", "IMPORTID", false));
							break;
							//Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
					}
				}

				if (iLinkProcessingMode == clC.SELL_LinkProcessMode_ImportID)
				{
					//ImportID mode: default is False because instructions are optional
					bProcessListedLinksOnly = goTR.StringToCheckbox(goTR.StrRead(par_sInstructionsIni, "ProcessListedLinksOnly", "0", false));
				}
				else
				{
					//GID_ID, TN modes: default is True because instructions are mandatory
					bProcessListedLinksOnly = goTR.StringToCheckbox(goTR.StrRead(par_sInstructionsIni, "ProcessListedLinksOnly", "1", false));
				}

// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of Integer.Parse(goTR.StrRead(par_sInstructionsIni, "ImportIDModeLinkCount", "0", false)) for every iteration:
				int tempVar = int.Parse(goTR.StrRead(par_sInstructionsIni, "ImportIDModeLinkCount", "0", false));
				for (i = 1; i <= tempVar; i++)
				{
					aMode1Links.AddInfo(goTR.StrRead(par_sInstructionsIni, "ImportIDModeLink" + i.ToString(), null, false));
				}
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of Integer.Parse(goTR.StrRead(par_sInstructionsIni, "GID_IDModeLinkCount", "0", false)) for every iteration:
				int tempVar2 = int.Parse(goTR.StrRead(par_sInstructionsIni, "GID_IDModeLinkCount", "0", false));
				for (i = 1; i <= tempVar2; i++)
				{
					aMode2Links.AddInfo(goTR.StrRead(par_sInstructionsIni, "GID_IDModeLink" + i.ToString(), null, false));
				}
// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of Integer.Parse(goTR.StrRead(par_sInstructionsIni, "TNModeLinkCount", "0", false)) for every iteration:
				int tempVar3 = int.Parse(goTR.StrRead(par_sInstructionsIni, "TNModeLinkCount", "0", false));
				for (i = 1; i <= tempVar3; i++)
				{
					aMode3Links.AddInfo(goTR.StrRead(par_sInstructionsIni, "TNModeLink" + i.ToString(), null, false));
				}


				//--------------------------- Process ---------------------------------------
				if (!par_bLinksOnly) //MI 2/19/08 added ability to process only links.
				{

					//------------ Process DTT_CreationTime ---------------
					dtCreationTimeStarted = goTR.NowUTC();
					//Update the progress record
					sProgress = "   " + par_sFileName + ": Setting DTT_CreationTime pass 1: started";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "CreationTime1_" + par_sFileName, sProgress);

					//Fill DTT_CreationTime from temp fields only if they contain a value.
					sField = "DTT_CreationTime";
					sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[DTT_CreationTime] = dbo.fnConvertFieldValue('TXT_DTE_CreationTime', [TXT_DTE_CreationTime], '" + par_sFileName + "', [TXT_TME_CreationTime])" + "\r\n" + "WHERE [TXT_DTE_CreationTime] IS NOT NULL and [TXT_DTE_CreationTime] <> ''" + sWhereAppend);
					bResult = goData.RunSQLQuery(sSQL);
					if (bResult)
					{
						sProgress = "   " + par_sFileName + ": Setting DTT_CreationTime pass 1: SUCCESS (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtCreationTimeStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "CreationTime1_" + par_sFileName, sProgress);
					}
					else
					{
						sProgress = "   " + par_sFileName + ": Setting DTT_CreationTime pass 1: FAILED (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtCreationTimeStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "CreationTime1_" + par_sFileName, sProgress);
						return bResult;
					}

					//---------------- DTT_CreationTime pass 2 -------------------
					//If DTT_CreationTime is null or blank after the above UPDATE, put UTC 'now' in it. 
					//This ensures there will be a value in it.
					dtCreationTimeStarted = goTR.NowUTC();
					//Update the progress record
					sProgress = "   " + par_sFileName + ": Setting DTT_CreationTime pass 2: started";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "CreationTime2_" + par_sFileName, sProgress);
					sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[DTT_CreationTime] = GetUTCDate()" + "\r\n" + "WHERE ([DTT_CreationTime] IS NULL or [DTT_CreationTime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
					bResult = goData.RunSQLQuery(sSQL);
					if (bResult)
					{
						sProgress = "   " + par_sFileName + ": Setting DTT_CreationTime pass 2: SUCCESS (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtCreationTimeStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "CreationTime2_" + par_sFileName, sProgress);
					}
					else
					{
						sProgress = "   " + par_sFileName + ": Setting DTT_CreationTime pass 2: FAILED (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtCreationTimeStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "CreationTime2_" + par_sFileName, sProgress);
						return bResult;
					}


					//------------ Process DTT_ModTime ---------------
					dtModTimeStarted = goTR.NowUTC();
					//Update the progress record
					sProgress = "   " + par_sFileName + ": Setting DTT_ModTime: started";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "ModTime1_" + par_sFileName, sProgress);

					//Fill DTT_ModTime from temp fields only if null or blank.
					//This allows running fields processing multiple times and not altering ModTime during each run.
					//There is only one shot at this. If temp fields weren't mapped, ModTime will be set to
					//current time and this value will not be modified by future processing except if altered
					//through the rowset (as SYS_Name updating does), in which case the rowset will update the value
					//to current datetime.
					sField = "DTT_ModTime";
					sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[DTT_ModTime] = GetUTCDate()" + "\r\n" + "WHERE ([DTT_ModTime] IS NULL or [DTT_ModTime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
					bResult = goData.RunSQLQuery(sSQL);
					if (bResult)
					{
						sProgress = "   " + par_sFileName + ": Setting DTT_ModTime: SUCCESS (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtModTimeStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "ModTime1_" + par_sFileName, sProgress);
					}
					else
					{
						sProgress = "   " + par_sFileName + ": Setting DTT_ModTime: FAILED (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtModTimeStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "ModTime1_" + par_sFileName, sProgress);
						return bResult;
					}


					//----------------- Process CHK_Report pass 1 ---------------------
					dtReportStarted = goTR.NowUTC();
					//Update the progress record
					sProgress = "   " + par_sFileName + ": Setting CHK_Report pass 1: started";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "CHKReport_" + par_sFileName, sProgress);

					//Fill CHK_Report from temp field only if the temp field is not NULL.
					sField = "CHK_Report";
					sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[CHK_Report] = dbo.fnConvertFieldValue('TXT_CHK_Report', [TXT_CHK_Report], '" + par_sFileName + "', NULL)" + "\r\n" + "WHERE [TXT_CHK_Report] IS NOT NULL and [TXT_CHK_Report] <> ''" + sWhereAppend);
					bResult = goData.RunSQLQuery(sSQL);
					if (bResult)
					{
						sProgress = "   " + par_sFileName + ": Setting CHK_Report pass 1: SUCCESS (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtReportStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "CHKReport_" + par_sFileName, sProgress);
					}
					else
					{
						sProgress = "   " + par_sFileName + ": Setting CHK_Report pass 1: FAILED (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtReportStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "CHKReport_" + par_sFileName, sProgress);
						return bResult;
					}

					//----------------- Process CHK_Report pass 2 ---------------------
					dtReportStarted = goTR.NowUTC();
					//Update the progress record
					sProgress = "   " + par_sFileName + ": Setting CHK_Report pass 2: started";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "CHKReport2_" + par_sFileName, sProgress);

					//Fill CHK_Report from temp field only if the temp field is not NULL.
					sField = "CHK_Report";
					sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[CHK_Report] = 1" + "\r\n" + "WHERE ([TXT_CHK_Report] IS NULL or [TXT_CHK_Report] = '')" + sWhereAppend);
					bResult = goData.RunSQLQuery(sSQL);
					if (bResult)
					{
						sProgress = "   " + par_sFileName + ": Setting CHK_Report pass 2: SUCCESS (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtReportStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "CHKReport2_" + par_sFileName, sProgress);
					}
					else
					{
						sProgress = "   " + par_sFileName + ": Setting CHK_Report pass 2: FAILED (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtReportStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "CHKReport2_" + par_sFileName, sProgress);
						return bResult;
					}

				}

				//-------------------------------- Process fields ---------------------------------
				//Update the progress record
				sProgress = "   " + par_sFileName + ": Field processing: started";
				goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Fields_" + par_sFileName, sProgress);

				//------------ Start the big direct fields statement ---------------
				goData.LoadFieldData();
				aFields = goData.GetFields(par_sFileName);

				sSQL = "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n";

				//----- Loop through temporary import fields -----
				for (j = 1; j <= aFields.GetDimension(); j++)
				{
					sField = aFields.GetInfo(j);
					sProgress = "      " + sField + " (last field processed)";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Fields_" + par_sFileName + "_field", sProgress);
					if (goTR.FromTo(sField, 4, 4) == "_" && goTR.FromTo(sField, 8, 8) == "_")
					{
						//This may be a temp field (ex: TXT_DTT_StartTime or TXT_CUR_Amount)
						sTempPrefix = goTR.GetPrefix(sField);
						//*** MI 5/14/07 Added testing temp field prefix (must be TXT or MMO) to prevent
						//processing an N1 link field like "GID_For_CO" as a temp field
						//Temp fields can have only TXT or MMO prefix
						if (sTempPrefix != "TXT_" && sTempPrefix != "MMO_")
						{
							goto ProcessNextField;
						}
						sPermField = goTR.FromTo(sField, 5, -1);
						sPrefix = goTR.GetPrefix(sPermField);
						if (!par_bLinksOnly) //*** MI 2/19/08 added ability to process only links
						{
							//Process direct fields only if we are not in 'links only' mode
							switch (sPrefix)
							{
								case "DTE_":
									//Skip DTT_CreationTime, which we already processed above
									if (goTR.RemovePrefix(sPermField.ToUpper()) == "CREATIONTIME")
									{
										goto ProcessNextField;
									}
									if (goTR.RemovePrefix(sPermField.ToUpper()) == "MODTIME")
									{
										goto ProcessNextField;
									}
									lPos = aFields.SeekInfo("TXT_TME_" + goTR.RemovePrefix(sPermField), true, false);
									if (lPos > 0)
									{
										//Matching time field exists
										//Have we already added this DTT field?
										if (sSQL.IndexOf("[DTT_" + goTR.RemovePrefix(sPermField) + "] = ") + 1 < 1)
										{
											//Same DTT field not found, add it
											sOtherField = aFields.GetInfo(lPos);
											sSQL += "[DTT_" + goTR.RemovePrefix(sPermField) + "] = " + "dbo.fnConvertFieldValue('" + sField + "', [" + sField + "], '" + par_sFileName + "', [" + sOtherField + "])," + "\r\n";
										}
									}
									else
									{
										//Have we already added this DTT field?
										if (sSQL.IndexOf("[DTT_" + goTR.RemovePrefix(sPermField) + "] = ") + 1 < 1)
										{
											//Field not found, add it
											//There is no matching time field, time will be set to midnight automatically by SS
											sSQL += "[DTT_" + goTR.RemovePrefix(sPermField) + "] = " + "dbo.fnConvertFieldValue('" + sField + "', [" + sField + "], '" + par_sFileName + "', NULL)," + "\r\n";
										}
									}
									break;
								case "TME_":
									//Skip DTT_CreationTime, which we already processed above
									if (goTR.RemovePrefix(sPermField.ToUpper()) == "CREATIONTIME")
									{
										goto ProcessNextField;
									}
									if (goTR.RemovePrefix(sPermField.ToUpper()) == "MODTIME")
									{
										goto ProcessNextField;
									}
									lPos = aFields.SeekInfo("TXT_DTE_" + goTR.RemovePrefix(sPermField), true, false);
									if (lPos > 0)
									{
										//Matching date field exists
										//Have we already added this DTT field?
										if (sSQL.IndexOf("[DTT_" + goTR.RemovePrefix(sPermField) + "] = ") + 1 < 1)
										{
											//Field not found, add it
											sOtherField = aFields.GetInfo(lPos);
											sSQL += "[DTT_" + goTR.RemovePrefix(sPermField) + "] = " + "dbo.fnConvertFieldValue('" + sOtherField + "', [" + sOtherField + "], '" + par_sFileName + "', [" + sField + "])," + "\r\n";
										}
									}
									else
									{
										//Have we already added this DTT field?
										if (sSQL.IndexOf("[DTT_" + goTR.RemovePrefix(sPermField) + "] = ") + 1 < 1)
										{
											//Same DTT field not found, add it
											//No date provided, we set it to today. If not, SS would set it to '1900-01-01'.
											sSQL += "[DTT_" + goTR.RemovePrefix(sPermField) + "] = " + "dbo.fnConvertFieldValue('" + sField + "', [" + sField + "], '" + par_sFileName + "', '" + goTR.DateTimeToSysString(goTR.NowUTC()).Substring(0, 10) + "')," + "\r\n";
										}
									}
									break;
								case "DTT_":
									//TXT_DTT_ fields are not created for import processing and are not supported.
									//This is here just in case one is created by hand, for example. fnConvertFieldValue
									//ignores the time value, so only the date will survive!
									//Skip DTT_CreationTime, which we already processed above
									if (goTR.RemovePrefix(sPermField.ToUpper()) == "CREATIONTIME")
									{
										goto ProcessNextField;
									}
									if (goTR.RemovePrefix(sPermField.ToUpper()) == "MODTIME")
									{
										goto ProcessNextField;
									}
									//Have we already added this DTT field?
									if (sSQL.IndexOf(sPermField + " = ") + 1 < 1)
									{
										//Field not found, add it
										sSQL += "[" + sPermField + "] = " + "dbo.fnConvertFieldValue('" + sField + "', [" + sField + "], '" + par_sFileName + "', NULL)," + "\r\n";
									}
									break;
								case "CUR_":
									//Implicit conversion nvarchar -> money not allowed in SS, must convert (cast) explicitly
									sSQL += "[" + sPermField + "] = " + "CAST(dbo.fnConvertFieldValue('" + sField + "', [" + sField + "], '" + par_sFileName + "', NULL) as money)," + "\r\n";
									break;
								case "LNK_":
								break;
									//Links are processed in a separate Select Case statement below
								default:
									//------- Process overrides first ------
									//SI__ShareState is forced to 2 (shared) - should not come up because TXT_SI__ShareState temp field
									//is not being created anymore in SchemaAddFieldsToTable()
									if (sPermField.ToUpper() == "SI__SHARESTATE")
									{
										sSQL += "[" + sPermField + "] = 2," + "\r\n";
										goto ProcessNextField;
									}
									//TXT_CHK_Report, if unmapped, must be disregarded and 1 set in CHK_Report.
									//This was processed above.
									if (sPermField.ToUpper() == "CHK_REPORT")
									{
										goto ProcessNextField;
									}
									//TXT_MLS_Review is processed to CHK_Review, not MLS_Review, which is a temp field created
									//by SchemaAddFieldsToTable to allow mapping CN.Review combo from Cmc to a checkbox CHK_Review in SellSQL.
									if (sPermField.ToUpper() == "MLS_REVIEW" && par_sFileName.ToUpper() == "CN")
									{
										sSQL += "[CHK_Review] = COALESCE(Cast(Replace(Replace([TXT_MLS_REVIEW], 'Do Not Review', '0'), 'Review', '1') as tinyint), 0)," + "\r\n";
										goto ProcessNextField;
									}
									//In SellSQL TD file, CHK_Urgent is a selection in MLS_Priority field. in Cmc it is a checkbox.
									//In temp field creation code we create TXT_CHK_Urgent, but not the 'real' field CHK_Urgent.
									//We skip it here and process it to MLS_Priority in a separate statement below.
									if (sPermField.ToUpper() == "CHK_URGENT" && par_sFileName.ToUpper() == "TD")
									{
										//Skip
										goto ProcessNextField;
									}
									sSQL += "[" + sPermField + "] = " + "dbo.fnConvertFieldValue('" + sField + "', [" + sField + "], '" + par_sFileName + "', NULL)," + "\r\n";
									break;
							}
						}
						//Process a link
						switch (sPrefix)
						{
							case "LNK_":
								//DEBUG
								//If UCase(sField) = "MMO_LNK_CONNECTED_AC" Then Stop
								sLink = goTR.RemovePrefix(sField.ToUpper());

								//Considering bProcessListedLinksOnly. If 1, then skip the link if undefined.
								if (bProcessListedLinksOnly)
								{
									switch (iLinkProcessingMode)
									{
										case clC.SELL_LinkProcessMode_ImportID:
											if (aMode1Links.SeekInfo(sLink, true, false) < 1)
											{
												goto ProcessNextField;
											}
											break;
										case clC.SELL_LinkProcessMode_GID_ID:
											if (aMode2Links.SeekInfo(sLink, true, false) < 1)
											{
												goto ProcessNextField;
											}
											break;
										default:
											//clC.SELL_LinkProcessMode_Mixed
											if (aMode1Links.SeekInfo(sLink, true, false) < 1)
											{
												if (aMode2Links.SeekInfo(sLink, true, false) < 1)
												{
													if (aMode3Links.SeekInfo(sLink, true, false) < 1)
													{
														goto ProcessNextField;
													}
												}
											}
											break;
									}

								}

								//Mixed mode only: establish processing mode for the link
								if (iLinkProcessingMode == clC.SELL_LinkProcessMode_Mixed)
								{
									//Default processing mode for mixed mode 
									iOneLinkProcessingMode = iDefaultLinkProcessingMode;
									//Get the processing for this link, if defined
									lPos = aMode1Links.SeekInfo(sLink, true, false);
									if (lPos > 0)
									{
										iOneLinkProcessingMode = clC.SELL_LinkProcessMode_ImportID;
									}
									else
									{
										lPos = aMode2Links.SeekInfo(sLink, true, false);
										if (lPos > 0)
										{
											iOneLinkProcessingMode = clC.SELL_LinkProcessMode_GID_ID;
										}
										else
										{
											lPos = aMode3Links.SeekInfo(sLink, true, false);
											if (lPos > 0)
											{
												iOneLinkProcessingMode = clC.SELL_LinkProcessMode_TN;
											}
										}
									}
								}
								else
								{
									iOneLinkProcessingMode = iLinkProcessingMode;
								}

								//Process the link
								sLinkCreationSQL = GenerateLinkCreationSQL(par_sFileName, sField, lFromBIID, lToBIID, par_bIgnoreDuplicateImportIDs, sImpJobID, iOneLinkProcessingMode, par_sInstructionsIni);

								//Skip blank statements in case of links to our 'system' tables.
								if (sLinkCreationSQL != "")
								{
									doLinkStatements.AddInfo(sLinkCreationSQL);
									doLinkNames.AddInfo(sField);
								}
								break;
						}
					}
					else
					{
						//Don't process permanent fields here because they may not have been set yet
						//and because the UPDATE statement may end up with two Field = Value lines
						//for the same field
						//'Permanent field found
						//sPermField = sField
						//sPrefix = goTR.GetPrefix(sPermField)
						//Select Case UCase(sPermField)
						//    Case "SI__SHARESTATE"
						//        sSQL &= "[" & sPermField & "] = 2" & vbCrLf
						//End Select
					}
	ProcessNextField: ;
				}


				if (!par_bLinksOnly) //MI 2/19/08 added ability to process only links.
				{

					//----------- Did we set any direct fields? ------------
					if (sSQL == "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n")
					{
						//No direct fields were found or set. This table either doesn't have temp fields
						//or has no processable temp fields.
						sProgress = "   " + par_sFileName + ": Field processing: fields were not edited because there are no temporary import fields to process.";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Fields_" + par_sFileName, sProgress);
					}
					else
					{
						//----------- Finish the statement for direct fields and run it --------------
						//Remove the trailing comma and CR
						if (sSQL.Substring(sSQL.Length - 3) == "," + "\r\n")
						{
							sSQL = sSQL.Substring(0, sSQL.Length - 3);
						}
						sSQL += "\r\n";
						if (lFromBIID == -1)
						{
							//Start from first record
							if (lToBIID == -1)
							{
								//No upper limit
								//No WHERE statement needed
								sSQL += "WHERE 1 = 1";
							}
							else
							{
								//Less than
								sSQL += "WHERE [BI__ID] <= " + lToBIID;
							}
						}
						else
						{
							//Start from a particular record
							if (lToBIID == -1)
							{
								//No upper limit
								sSQL += "WHERE [BI__ID] >= " + lFromBIID;
							}
							else
							{
								//Between
								sSQL += "WHERE [BI__ID] >= " + lFromBIID + " AND [BI__ID] <= " + lToBIID;
							}
						}

						//Add sImpJobID to the WHERE statement
						if (sImpJobID != "")
						{
							sSQL += " AND TXT_ImpJobID = '" + sImpJobID + "' ";
						}

						//Run the UPDATE query
						//DEBUG
						//If par_sFileName = "CN" Then Stop 'And UCase(sField) = UCase("TXT_LNK_Related_SO") Then Stop
						bResult = goData.RunSQLQuery(sSQL);
					}

					if (bResult)
					{
						sProgress = "   " + par_sFileName + ": Field processing: SUCCESS (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Fields_" + par_sFileName, sProgress);
					}
					else
					{
						sProgress = "   " + par_sFileName + ": Field processing: FAILED (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Fields_" + par_sFileName, sProgress);
						return bResult;
					}


					//======================= PROCESS TD.CHK_URGENT TO MLS_PRIORITY ==========================
					//If TXT_CHK_Urgent is True, set MLS_Priority value to Urgent (0)
					//This must run after MLS_Priority was processed.
					if (par_sFileName.ToUpper() == "TD")
					{
						dtTDUrgentStarted = goTR.NowUTC();
						//Update the progress record
						sProgress = "   " + par_sFileName + ": Processing To Do Urgent checkbox: started";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "ToDoUrgent_" + par_sFileName, sProgress);

						sField = "MLS_Priority";
						sSQL = "IF EXISTS (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'TD' and COLUMN_NAME = 'MLS_Priority')" + "\r\n" + "AND EXISTS (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'TD' and COLUMN_NAME = 'TXT_CHK_Urgent')" + "\r\n" + "BEGIN" + "\r\n" + "EXECUTE ('UPDATE [" + par_sFileName + "]" + "\r\n" + "SET" + "\r\n" + "[" + sField + "] = 0" + "\r\n" + "WHERE [TXT_CHK_Urgent] = ''TRUE''" + goTR.PrepareForSQL(sWhereAppend) + "')" + "\r\n" + "END";

						bResult = goData.RunSQLQuery(sSQL);
						if (bResult)
						{
							sProgress = "   " + par_sFileName + ": Processing To Do Urgent checkbox: SUCCESS (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtTDUrgentStarted, goTR.NowUTC()) + " mins)";
							goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "ToDoUrgent_" + par_sFileName, sProgress);
						}
						else
						{
							sProgress = "   " + par_sFileName + ": Processing To Do Urgent checkbox: FAILED (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtTDUrgentStarted, goTR.NowUTC()) + " mins)";
							goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "ToDoUrgent_" + par_sFileName, sProgress);
							return bResult;
						}
					}


					//========================= RENAME FI (FILE) RECORDS ======================
					//File FI has one record for each data table. There records are used to filter Group records
					//in linkboxes so they appear only in forms of certain file(s). Since FI records are imported
					//from Commence with Commence category names, we rename them to SellSQL names here.
					//Note: this doesn't observe from and to BI__ID parameters because all file names must
					//be processed as a group.
					if (par_sFileName.ToUpper() == "FI")
					{
						dtFIStarted = goTR.NowUTC();
						//Update the progress record
						sProgress = "   " + par_sFileName + ": FI renaming: started";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "FIRenaming_" + par_sFileName, sProgress);

						sSQL = "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.COLUMNS" + "\r\n" + "WHERE TABLE_NAME = 'FI')" + "\r\n" + "BEGIN" + "\r\n" + "\t" + "UPDATE [FI]" + "\r\n" + "\t" + "SET" + "\r\n" + "\t" + "[TXT_FileName] = dbo.fnGetShortTableName([TXT_ImportID], 1)," + "\r\n" + "\t" + "[SYS_Name] = dbo.fnGetShortTableName([TXT_ImportID], 1)" + "\r\n" + "END";
						bResult = goData.RunSQLQuery(sSQL);
						if (bResult)
						{
							sProgress = "   " + par_sFileName + ": FI renaming: SUCCESS (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtFIStarted, goTR.NowUTC()) + " mins)";
							goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "FIRenaming_" + par_sFileName, sProgress);
						}
						else
						{
							sProgress = "   " + par_sFileName + ": FI renaming: FAILED (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtFIStarted, goTR.NowUTC()) + " mins)";
							goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "FIRenaming_" + par_sFileName, sProgress);
							return bResult;
						}
					}


					//========================= RENAME IU (Industry) RECORDS ========================
					//If TXT_IndustryName and SYS_Name were imported from Cmc, they contain numeric codes
					//Here we replace them with a readable and quickselectable text
					if (par_sFileName.ToUpper() == "IU")
					{
						dtIUStarted = goTR.NowUTC();
						//Update the progress record
						sProgress = "   " + par_sFileName + ": IU renaming: started";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "IURenaming_" + par_sFileName, sProgress);

						sSQL = "IF EXISTS (SELECT TABLE_NAME FROM INFORMATION_SCHEMA.COLUMNS" + "\r\n" + "WHERE TABLE_NAME = 'IU')" + "\r\n" + "BEGIN" + "\r\n" + "\t" + "UPDATE [IU]" + "\r\n" + "\t" + "SET" + "\r\n" + "\t" + "[TXT_IndustryName] = Left(Replace(Replace(Replace(Cast([MMO_Description] as nvarchar(100)), Char(10), ' '), Char(13), ' '), '  ', ' '), 80)," + "\r\n" + "\t" + "[SYS_Name] = Left(Replace(Replace(Replace(Cast([MMO_Description] as nvarchar(100)), Char(10), ' '), Char(13), ' '), '  ', ' '), 67) + ' (' + [TXT_IndustryCode] + ')'" + "\r\n" + "\t" + "--WHERE [TXT_IndustryCode] like '01%'" + "\r\n" + "END";
						bResult = goData.RunSQLQuery(sSQL);
						if (bResult)
						{
							sProgress = "   " + par_sFileName + ": IU renaming: SUCCESS (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtIUStarted, goTR.NowUTC()) + " mins)";
							goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "IURenaming_" + par_sFileName, sProgress);
						}
						else
						{
							sProgress = "   " + par_sFileName + ": IU renaming: FAILED (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtIUStarted, goTR.NowUTC()) + " mins)";
							goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "IURenaming_" + par_sFileName, sProgress);
							return bResult;
						}
					}



					//========================= SUMMARY FIELDS =============================
					//Update the progress record
					sProgress = "   " + par_sFileName + ": Summary field updating: started";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SummaryFields_" + par_sFileName, sProgress);


					//----------------------- Update summary and 'custom' fields ------------------------
					//Updatce fields that are maintained "behind the scenes" by RecordOnSave scripts.
					//IMPORTANT: DO NOT attempt to update SYS_Name fields here because they use links and
					//ALL links must be processed first. SYS_Name updating is initiated
					//by a separate button on ImportTasks.aspx pagem, which calls clSchema.ImportUpdateSysNames.
					dtSummaryStarted = goTR.NowUTC();

					//Concatenate UPDATE statements for each field
					switch (par_sFileName.ToUpper())
					{
						case "AC":
							//CHK_CORR/MLS_TYPE
							sField = "CHK_CORR";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 1" + "\r\n" + "WHERE [MLS_TYPE] >= 3 and [MLS_TYPE] <= 10" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 0" + "\r\n" + "WHERE not ([MLS_TYPE] >= 3 and [MLS_TYPE] <= 10)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//CHK_CORRRECEIVED
							sField = "CHK_CORRRECEIVED";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 1" + "\r\n" + "WHERE ([MLS_TYPE] = 3 or [MLS_TYPE] = 5 or [MLS_TYPE] = 7 or [MLS_TYPE] = 9)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 0" + "\r\n" + "WHERE not ([MLS_TYPE] = 3 or [MLS_TYPE] = 5 or [MLS_TYPE] = 7 or [MLS_TYPE] = 9)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//CHK_CORRSENT
							sField = "CHK_CORRSENT";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 1" + "\r\n" + "WHERE ([MLS_TYPE] = 4 or [MLS_TYPE] = 6 or [MLS_TYPE] = 8 or [MLS_TYPE] = 10)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 0" + "\r\n" + "WHERE not ([MLS_TYPE] = 4 or [MLS_TYPE] = 6 or [MLS_TYPE] = 8 or [MLS_TYPE] = 10)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//CHK_LEAD
							sField = "CHK_LEAD";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 1" + "\r\n" + "WHERE ([MLS_PURPOSE] = 8 or [MLS_PURPOSE] = 21 or [MLS_PURPOSE] = 22 or [MLS_PURPOSE] = 23 or [MLS_PURPOSE] = 24 or [MLS_PURPOSE] = 25)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 0" + "\r\n" + "WHERE not ([MLS_PURPOSE] = 8 or [MLS_PURPOSE] = 21 or [MLS_PURPOSE] = 22 or [MLS_PURPOSE] = 23 or [MLS_PURPOSE] = 24 or [MLS_PURPOSE] = 25)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//CHK_SERVICE
							sField = "CHK_SERVICE";
							//6=Service, 7=Support
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 1" + "\r\n" + "WHERE ([MLS_PURPOSE] = 6 or [MLS_PURPOSE] = 7)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 0" + "\r\n" + "WHERE not ([MLS_PURPOSE] = 6 or [MLS_PURPOSE] = 7)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//CHK_SERVICEEXT
							sField = "CHK_SERVICEEXT";
							//6=Service, 7=Support, 4=Order, 53=Training
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 1" + "\r\n" + "WHERE ([MLS_PURPOSE] = 4 or [MLS_PURPOSE] = 6 or [MLS_PURPOSE] = 7 or [MLS_PURPOSE] = 53)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 0" + "\r\n" + "WHERE not ([MLS_PURPOSE] = 4 or [MLS_PURPOSE] = 6 or [MLS_PURPOSE] = 7 or [MLS_PURPOSE] = 53)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//DTT_ENDTIME
							//If doRS.GetFieldVal("DTT_EndTime", clC.SELL_FRIENDLY) = "" Then
							//    doRS.SetFieldVal("DTT_EndTime", doRS.GetFieldVal("DTT_StartTime", clC.SELL_FRIENDLY), clC.SELL_FRIENDLY)
							//End If
							sField = "DTT_ENDTIME";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = DTT_STARTTIME" + "\r\n" + "WHERE ([DTT_ENDTIME] IS NULL or [DTT_ENDTIME] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//MI 5/29/08 Added
							//Generate TXT_StartTimeYear, SI__StartTimeMonth, SI__StartTimeDay	
							sField = "TXT_STARTTIMEYEAR";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Cast(Year([DTT_STARTTIME]) as varchar(4))" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//MI 5/29/08 Added
							sField = "SI__STARTTIMEMONTH";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Month([DTT_STARTTIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//MI 5/29/08 Added
							sField = "SI__STARTTIMEDAY";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Day([DTT_STARTTIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;


						case "AP":
							//DTT_ALARMTIME
							sField = "DTT_ALARMTIME";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = DateAdd(Minute, -20, DTT_STARTTIME)" + "\r\n" + "WHERE [CHK_Alarm] = 1 and ([DTT_ALARMTIME] IS NULL or [DTT_ALARMTIME] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//Generate TXT_StartTimeYear, SI__StartTimeMonth, SI__StartTimeDay	
							sField = "TXT_STARTTIMEYEAR";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Cast(Year([DTT_STARTTIME]) as varchar(4))" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "SI__STARTTIMEMONTH";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Month([DTT_STARTTIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "SI__STARTTIMEDAY";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Day([DTT_STARTTIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;


						case "CN":
							//dtt_origcreatedtime
							//If doRS.GetFieldVal("dtt_origcreatedtime", 1) = "" Then
							//    doRS.SetFieldVal("dtt_origcreatedtime", "Today|Now")
							//End If
							sField = "dtt_origcreatedtime";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = GetUTCDate()" + "\r\n" + "WHERE ([dtt_origcreatedtime] IS NULL or [dtt_origcreatedtime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//doRS.SetFieldVal("TXT_STATEBUSINESS", UCase(doRS.GetFieldVal("TXT_STATEBUSINESS")))
							sField = "TXT_STATEBUSINESS";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Upper([" + sField + "])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//doRS.SetFieldVal("TXT_STATEHOME", UCase(doRS.GetFieldVal("TXT_STATEHOME")))
							sField = "TXT_STATEHOME";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Upper([" + sField + "])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//doRS.SetFieldVal("TXT_STATEOTHER", UCase(doRS.GetFieldVal("TXT_STATEOTHER")))
							sField = "TXT_STATEOTHER";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Upper([" + sField + "])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "CO":
							//dtt_origcreatedtime
							sField = "dtt_origcreatedtime";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = GetUTCDate()" + "\r\n" + "WHERE ([dtt_origcreatedtime] IS NULL or [dtt_origcreatedtime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//TXT_STATEMAILING
							sField = "TXT_STATEMAILING";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Upper([" + sField + "])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//TXT_STATEBILLING
							sField = "TXT_STATEBILLING";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Upper([" + sField + "])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//TXT_STATESHIPPING
							sField = "TXT_STATESHIPPING";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Upper([" + sField + "])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//---- Process Company type checkboxes into LNK_Related_RL ----
							//In Selltis DB and maybe some early customer DBs there were several checkboxes
							//in CO file that indicated whether a Company is customer, prospect, vendor, etc.
							//In SellSQL there is a link to Relation file that allows associating a Company
							//with multiple 'relationship types'. In SchemaAddFieldToTable we add a temp field
							//MMO_LNK_Related_RL for the link LNK_Related_RL.
							//Here we populate MMO_LNK_Related_RL based on each checkbox, simulating a link
							//that could have been mapped to this field from Cmc. This field ends up with a 
							//CR_delimited string with values that are processed into the LNK_Related_RL link
							//together with the rest of the links in the code below.
							//Important: There must be an entry in the RL file
							//named exactly as each of the checkboxes processed here (minues 'CHK_TEMP' prefix).

							sField = "CHK_TEMPCOMPETITOR";
							sText = "Competitor";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[MMO_LNK_Related_RL] = '" + sText + "'" + "\r\n" + "WHERE " + "[" + sField + "] = 1 and ([MMO_LNK_Related_RL] IS NULL or [MMO_LNK_Related_RL] LIKE '')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[MMO_LNK_Related_RL] = Cast([MMO_LNK_Related_RL] as nvarchar(3900)) + Char(13) + Char(10) + '" + sText + "'" + "\r\n" + "WHERE " + "[" + sField + "] = 1 and [MMO_LNK_Related_RL] IS NOT NULL and [MMO_LNK_Related_RL] NOT LIKE ''" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "CHK_TEMPCUSTOMER";
							sText = "Customer";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[MMO_LNK_Related_RL] = '" + sText + "'" + "\r\n" + "WHERE " + "[" + sField + "] = 1 and ([MMO_LNK_Related_RL] IS NULL or [MMO_LNK_Related_RL] LIKE '')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[MMO_LNK_Related_RL] = Cast([MMO_LNK_Related_RL] as nvarchar(3900)) + Char(13) + Char(10) + '" + sText + "'" + "\r\n" + "WHERE " + "[" + sField + "] = 1 and [MMO_LNK_Related_RL] IS NOT NULL and [MMO_LNK_Related_RL] NOT LIKE ''" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "CHK_TEMPFORMERCUSTOMER";
							sText = "Former Customer";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[MMO_LNK_Related_RL] = '" + sText + "'" + "\r\n" + "WHERE " + "[" + sField + "] = 1 and ([MMO_LNK_Related_RL] IS NULL or [MMO_LNK_Related_RL] LIKE '')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[MMO_LNK_Related_RL] = Cast([MMO_LNK_Related_RL] as nvarchar(3900)) + Char(13) + Char(10) + '" + sText + "'" + "\r\n" + "WHERE " + "[" + sField + "] = 1 and [MMO_LNK_Related_RL] IS NOT NULL and [MMO_LNK_Related_RL] NOT LIKE ''" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "CHK_TEMPPROSPECT";
							sText = "Prospect";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[MMO_LNK_Related_RL] = '" + sText + "'" + "\r\n" + "WHERE " + "[" + sField + "] = 1 and ([MMO_LNK_Related_RL] IS NULL or [MMO_LNK_Related_RL] LIKE '')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[MMO_LNK_Related_RL] = Cast([MMO_LNK_Related_RL] as nvarchar(3900)) + Char(13) + Char(10) + '" + sText + "'" + "\r\n" + "WHERE " + "[" + sField + "] = 1 and [MMO_LNK_Related_RL] IS NOT NULL and [MMO_LNK_Related_RL] NOT LIKE ''" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "CHK_TEMPSERVICEPROVIDER";
							sText = "Service Provider";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[MMO_LNK_Related_RL] = '" + sText + "'" + "\r\n" + "WHERE " + "[" + sField + "] = 1 and ([MMO_LNK_Related_RL] IS NULL or [MMO_LNK_Related_RL] LIKE '')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[MMO_LNK_Related_RL] = Cast([MMO_LNK_Related_RL] as nvarchar(3900)) + Char(13) + Char(10) + '" + sText + "'" + "\r\n" + "WHERE " + "[" + sField + "] = 1 and [MMO_LNK_Related_RL] IS NOT NULL and [MMO_LNK_Related_RL] NOT LIKE ''" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "CHK_TEMPSUPPLIER";
							sText = "Supplier";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[MMO_LNK_Related_RL] = '" + sText + "'" + "\r\n" + "WHERE " + "[" + sField + "] = 1 and ([MMO_LNK_Related_RL] IS NULL or [MMO_LNK_Related_RL] LIKE '')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[MMO_LNK_Related_RL] = Cast([MMO_LNK_Related_RL] as nvarchar(3900)) + Char(13) + Char(10) + '" + sText + "'" + "\r\n" + "WHERE " + "[" + sField + "] = 1 and [MMO_LNK_Related_RL] IS NOT NULL and [MMO_LNK_Related_RL] NOT LIKE ''" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "EA":
							//dtt_origcreatedtime
							sField = "dtt_origcreatedtime";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = GetUTCDate()" + "\r\n" + "WHERE ([dtt_origcreatedtime] IS NULL or [dtt_origcreatedtime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "EC":
							//dtt_origcreatedtime
							sField = "dtt_origcreatedtime";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = GetUTCDate()" + "\r\n" + "WHERE ([dtt_origcreatedtime] IS NULL or [dtt_origcreatedtime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "EX":
							//Generate TXT_YEAR, SI__MONTH, SI__DAY
							sField = "TXT_YEAR";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Cast(Year([DTT_TIME]) as varchar(4))" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "SI__MONTH";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Month([DTT_TIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "SI__DAY";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Day([DTT_TIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "GR":
							//dtt_origcreatedtime
							sField = "dtt_origcreatedtime";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = GetUTCDate()" + "\r\n" + "WHERE ([dtt_origcreatedtime] IS NULL or [dtt_origcreatedtime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "LO":
							//dtt_origcreatedtime
							sField = "dtt_origcreatedtime";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = GetUTCDate()" + "\r\n" + "WHERE ([dtt_origcreatedtime] IS NULL or [dtt_origcreatedtime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "MO":
							//dtt_origcreatedtime
							sField = "dtt_origcreatedtime";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = GetUTCDate()" + "\r\n" + "WHERE ([dtt_origcreatedtime] IS NULL or [dtt_origcreatedtime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "MS":
							//Generate TXT_YEAR, SI__MONTH, SI__DAY
							//MI 5/29/08 Added
							sField = "TXT_YEAR";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Cast(Year([DTT_TIME]) as varchar(4))" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//MI 5/29/08 Added
							sField = "SI__MONTH";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Month([DTT_TIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//MI 5/29/08 Added
							sField = "SI__DAY";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Day([DTT_TIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "OP":
							//Fill CHK_Open when Status is Open, On Hold or Quoted
							sField = "CHK_Open";
							//0=Open, 1=On Hold, 6=Quoted
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 1" + "\r\n" + "WHERE ([MLS_STATUS] = 0 or [MLS_STATUS] = 1 or [MLS_STATUS] = 6)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 0" + "\r\n" + "WHERE not ([MLS_STATUS] = 0 or [MLS_STATUS] = 1 or [MLS_STATUS] = 6)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//TXT_YEAR, SI__MONTH
							//doRS.SetFieldVal("TXT_YEAR", goTR.GetYear(doRS.GetFieldVal("DTT_TIME", 2)))
							//doRS.SetFieldVal("SI__MONTH", goTR.StringToNum(goTR.GetMonth(doRS.GetFieldVal("DTT_TIME", 2))))
							sField = "TXT_YEAR";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Cast(Year([DTT_TIME]) as varchar(4))" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "SI__MONTH";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Month([DTT_TIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//MI 5/29/08 Added
							sField = "SI__DAY";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Day([DTT_TIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//TXT_YEARCLOSE, SI__MONTHCLOSE
							//doRS.SetFieldVal("TXT_YEARCLOSE", goTR.GetYear(doRS.GetFieldVal("DTT_EXPCLOSEDATE", 2)))
							//doRS.SetFieldVal("SI__MONTHCLOSE", goTR.StringToNum(goTR.GetMonth(doRS.GetFieldVal("DTT_EXPCLOSEDATE", 2))))
							sField = "TXT_YEARCLOSE";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Cast(Year([DTT_EXPCLOSEDATE]) as varchar(4))" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "SI__MONTHCLOSE";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Month([DTT_EXPCLOSEDATE])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//MI 5/29/08 Added
							sField = "SI__DAYCLOSE";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Day([DTT_EXPCLOSEDATE])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;


						case "PD":
							//dtt_origcreatedtime
							sField = "dtt_origcreatedtime";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = GetUTCDate()" + "\r\n" + "WHERE ([dtt_origcreatedtime] IS NULL or [dtt_origcreatedtime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "PR":
							//CHK_OPEN
							sField = "CHK_OPEN";
							//0=Open, 1=On Hold
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 1" + "\r\n" + "WHERE ([MLS_STATUS] = 0 or [MLS_STATUS] = 1)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 0" + "\r\n" + "WHERE not ([MLS_STATUS] = 0 or [MLS_STATUS] = 1)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//dtt_origcreatedtime
							sField = "dtt_origcreatedtime";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = GetUTCDate()" + "\r\n" + "WHERE ([dtt_origcreatedtime] IS NULL or [dtt_origcreatedtime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//MI 5/29/08 Added
							sField = "TXT_YEAR";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Cast(Year([DTT_TIME]) as varchar(4))" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//MI 5/29/08 Added
							sField = "SI__MONTH";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Month([DTT_TIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//MI 5/29/08 Added
							sField = "SI__DAY";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Day([DTT_TIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "QL":
							//Select Case doRS.GetFieldVal("MLS_STATUS", 2)
							//    Case 0, 20      'Open, On Hold
							//        doRS.SetFieldVal("CHK_OPEN", 1, 2)
							//    Case Else
							//        doRS.SetFieldVal("CHK_OPEN", 0, 2)
							//End Select
							//CHK_OPEN
							sField = "CHK_OPEN";
							//0=Open, 20=On Hold
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 1" + "\r\n" + "WHERE ([MLS_STATUS] = 0 or [MLS_STATUS] = 20)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 0" + "\r\n" + "WHERE not ([MLS_STATUS] = 0 or [MLS_STATUS] = 20)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//doRS.SetFieldVal("SI__MONTH", goTR.StringToNum(goTR.GetMonth(doRS.GetFieldVal("DTT_TIME", 2))))
							//doRS.SetFieldVal("TXT_YEAR", goTR.GetYear(doRS.GetFieldVal("DTT_TIME", 2)))
							sField = "TXT_YEAR";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Cast(Year([DTT_TIME]) as varchar(4))" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "SI__MONTH";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Month([DTT_TIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//MI 5/29/08 Added
							sField = "SI__DAY";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Day([DTT_TIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//Fill DTT_EXPCLOSEDATE with today when blank
							sField = "DTT_EXPCLOSEDATE";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = GetUTCDate()" + "\r\n" + "WHERE ([DTT_EXPCLOSEDATE] IS NULL or [DTT_EXPCLOSEDATE] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//doRS.SetFieldVal("SI__MONTHCLOSE", goTR.StringToNum(goTR.GetMonth(doRS.GetFieldVal("DTT_EXPCLOSEDATE", 2))))
							//doRS.SetFieldVal("TXT_YEARCLOSE", goTR.GetYear(doRS.GetFieldVal("DTT_EXPCLOSEDATE", 2)))
							sField = "TXT_YEARCLOSE";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Cast(Year([DTT_EXPCLOSEDATE]) as varchar(4))" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "SI__MONTHCLOSE";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Month([DTT_EXPCLOSEDATE])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//MI 5/29/08 Added
							sField = "SI__DAYCLOSE";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Day([DTT_EXPCLOSEDATE])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;


						case "QT":
							sField = "CHK_OPEN";
							//0=Open, 1=On Hold
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 1" + "\r\n" + "WHERE ([MLS_STATUS] = 0 or [MLS_STATUS] = 1)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 0" + "\r\n" + "WHERE not ([MLS_STATUS] = 0 or [MLS_STATUS] = 1)" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "TXT_YEAR";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Cast(Year([DTT_TIME]) as varchar(4))" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "SI__MONTH";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Month([DTT_TIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//MI 5/29/08 Added
							sField = "SI__DAY";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Day([DTT_TIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;



						case "RO":
							//dtt_origcreatedtime
							sField = "dtt_origcreatedtime";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = GetUTCDate()" + "\r\n" + "WHERE ([dtt_origcreatedtime] IS NULL or [dtt_origcreatedtime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "RS":
							//dtt_origcreatedtime
							sField = "dtt_origcreatedtime";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = GetUTCDate()" + "\r\n" + "WHERE ([dtt_origcreatedtime] IS NULL or [dtt_origcreatedtime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "ST":
							//drResult = doRS.GetFieldVal("SR__STATERATE", 2) + doRS.GetFieldVal("SR__LOCALRATE", 2)
							//doRS.SetFieldVal("SR__RATETOTAL", drResult, 2)
							sField = "SR__RATETOTAL";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = [SR__STATERATE] + [SR__LOCALRATE]" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "TD":
							//Set CHK_OPEN based on CHK_COMPLETED
							sField = "CHK_OPEN";
							//0=Open, 1=On Hold
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 0" + "\r\n" + "WHERE [CHK_COMPLETED] = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 1" + "\r\n" + "WHERE not [CHK_COMPLETED] = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//Set MLS_STATUS based on CHK_COMPLETED
							sField = "MLS_STATUS";
							//0=undefined, 4=Completed
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 4" + "\r\n" + "WHERE [CHK_COMPLETED] = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							//Disallow completed Status when Complete checkbox is checked.
							//We don't set the right status here depending on the Start and Due date
							//relative to current date. clScripts.TD_RecordOnSave does that.
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = 0" + "\r\n" + "WHERE [CHK_COMPLETED] = 0 and [MLS_STATUS] = 4" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							//Generate TXT_DueTimeYear, SI__DueTimeMonth, SI__DueTimeDay	
							sField = "TXT_DUETIMEYEAR";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Cast(Year([DTT_DUETIME]) as varchar(4))" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "SI__DUETIMEMONTH";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Month([DTT_DUETIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);

							sField = "SI__DUETIMEDAY";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = Day([DTT_DUETIME])" + "\r\n" + "WHERE 1 = 1" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "US":
							//dtt_origcreatedtime
							sField = "dtt_origcreatedtime";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = GetUTCDate()" + "\r\n" + "WHERE ([dtt_origcreatedtime] IS NULL or [dtt_origcreatedtime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

						case "VE":
							//dtt_origcreatedtime
							sField = "dtt_origcreatedtime";
							sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET " + "\r\n" + "[" + sField + "] = GetUTCDate()" + "\r\n" + "WHERE ([dtt_origcreatedtime] IS NULL or [dtt_origcreatedtime] = '1753-01-02 23:59:59.000')" + sWhereAppend);
							doSummaryStatements.AddInfo(sSQL);
							doSummaryNames.AddInfo(sField);
							break;

					}

					//----------- Did we set any summary fields? ------------
					if (doSummaryStatements.GetDimension() < 1)
					{
						//No summary fields found
						sProgress = "   " + par_sFileName + ": Summary field updating: fields not edited because they were not found.";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SummaryFields_" + par_sFileName + "_End", sProgress);
					}
					else
					{
						//'Update the progress record
						//sProgress = "   " & par_sFileName & ": Summary field updating: started"
						//goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SummaryFields_" & par_sFileName, sProgress)
						//Run the statement
						for (i = 1; i <= doSummaryStatements.GetDimension(); i++)
						{
							dtSummaryStarted = goTR.NowUTC();

							//DEBUG
							string sSummaryFieldName = doSummaryNames.GetInfo(i);
							string sSummaryStatement = doSummaryStatements.GetInfo(i);
							//END DEBUG

							bResult = goData.RunSQLQuery(doSummaryStatements.GetInfo(i));
							if (bResult)
							{
								sProgress = "      Success: " + par_sFileName + "." + doSummaryNames.GetInfo(i) + " (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtSummaryStarted, goTR.NowUTC()) + " mins)";
								goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SummaryField_" + par_sFileName + "_" + doSummaryNames.GetInfo(i), sProgress);
							}
							else
							{
								bSummaryResult = false;
								sProgress = "      Failed: " + par_sFileName + "." + doSummaryNames.GetInfo(i) + " (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtSummaryStarted, goTR.NowUTC()) + " mins)";
								goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SummaryField_" + par_sFileName + "_" + doSummaryNames.GetInfo(i), sProgress);
							}
						}
						if (bSummaryResult)
						{
							sProgress = "   " + par_sFileName + ": Summary field updating: SUCCESS (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtSummaryStarted, goTR.NowUTC()) + " mins)";
							goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SummaryFields_" + par_sFileName + "_End", sProgress);
						}
						else
						{
							sProgress = "   " + par_sFileName + ": Summary field updating: FAILED (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtSummaryStarted, goTR.NowUTC()) + " mins)";
							goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SummaryFields_" + par_sFileName + "_End", sProgress);
						}
					}



					//========================= SET GID_CREATEDBY_US WHERE NULL ======================
					//GID_Createdby_US fields that are not mapped will be NULL. This code sets the current user
					//as the creator of all records. This observes from and to BI__ID parameters.
					dtCreatedByStarted = goTR.NowUTC();
					//Update the progress record
					sProgress = "   " + par_sFileName + ": Setting GID_CreatedBy_US: started";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SetCreatedbyUS_" + par_sFileName, sProgress);

					sField = "GID_CreatedBy_US";
					sSQL = WrapWithColumnCheckingStatement(par_sFileName, sField, "UPDATE [" + par_sFileName + "]" + "\r\n" + "SET" + "\r\n" + "[" + sField + "] = '" + goP.GetUserTID() + "'" + "\r\n" + "WHERE [GID_CreatedBy_US] IS NULL" + sWhereAppend);

					bResult = goData.RunSQLQuery(sSQL);
					if (bResult)
					{
						sProgress = "   " + par_sFileName + ": Setting GID_CreatedBy_US: SUCCESS (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtCreatedByStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SetCreatedbyUS_" + par_sFileName, sProgress);
					}
					else
					{
						sProgress = "   " + par_sFileName + ": Setting GID_CreatedBy_US: FAILED (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtCreatedByStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SetCreatedbyUS_" + par_sFileName, sProgress);
						return bResult;
					}

				} //MI 2/19/08 added ability to process only links.


				//=================================== LINKS ======================================

				//------------------------- Finish the link creation SQL -------------------------
				dtLinksStarted = goTR.NowUTC();
				if (doLinkStatements.GetDimension() < 1)
				{
					//If sSQLLinks = "" Then
					//No temp link fields found
					sProgress = "   " + par_sFileName + ": Link creation: not created because no temp link fields were found";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Links_" + par_sFileName + "_End", sProgress);
				}
				else
				{
					//Concatenate the header and link generation statement
					//sSQLLinks = sSQLLinksHeader & sSQLLinks
					//Update the progress record
					sProgress = "   " + par_sFileName + ": Link creation: started";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Links_" + par_sFileName, sProgress);
					//Run the statement
					for (i = 1; i <= doLinkStatements.GetDimension(); i++)
					{
						dtLinkStarted = goTR.NowUTC();

						//'DEBUG
						string sLinkname = doLinkNames.GetInfo(i);
						//==> REMOVE!!!!!!!!!!
						//If UCase(sLinkname) = "TXT_LNK_FOR_PD" Then Stop
						//Dim sLinkstatement As String = doLinkStatements.GetInfo(i)
						//'END DEBUG

						bResult = goData.RunSQLQuery(doLinkStatements.GetInfo(i));
						//bResult = goData.RunSQLQuery(sSQLLinks)
						if (bResult)
						{
							sProgress = "      Success: " + par_sFileName + "." + doLinkNames.GetInfo(i) + " (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtLinkStarted, goTR.NowUTC()) + " mins)";
							goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Link_" + par_sFileName + "_" + doLinkNames.GetInfo(i), sProgress);
						}
						else
						{
							bLinksResult = false;
							sProgress = "      Failed: " + par_sFileName + "." + doLinkNames.GetInfo(i) + " (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtLinkStarted, goTR.NowUTC()) + " mins)";
							goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Link_" + par_sFileName + "_" + doLinkNames.GetInfo(i), sProgress);
						}
					}
					if (bLinksResult)
					{
						sProgress = "   " + par_sFileName + ": Link creation: SUCCESS (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtLinksStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Links_" + par_sFileName + "_End", sProgress);
					}
					else
					{
						sProgress = "   " + par_sFileName + ": Link creation: FAILED (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtLinksStarted, goTR.NowUTC()) + " mins)";
						goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "Links_" + par_sFileName + "_End", sProgress);
					}
				}


			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sproc)
			//    End If
			//End Try

			return bResult;

		}


		public bool ImportUpdateSysNames(string par_sFileName = "", long par_lFromBIID = -1, long par_lToBIID = -1, long par_lRecs = 50, string par_sReplaceChar = "", string par_sReplaceWithChar = "", bool par_bConvertASCIIToUnicode = false)
		{
			//MI 9/16/08 Added par_bConvertASCIItoUnicode.
			//MI 12/9/07 Added support for from and to files in par_sFileName
			//MI 5/15/07 Added par_sReplaceChar, par_sReplaceWithChar params
			//MI 2/15/07

			//PURPOSE:
			//       Populate SYS_Name fields.
			//       Before this is run, the schema needs to be expanded with
			//       temporary fields by running goData.SchemaAddFields(), and
			//       data must be imported using DTS (in SS's EM) and processed
			//       by running ImportProcessFields().
			//       After this method is run and data is checked, you can remove 
			//       temporary fields by running goData.SchemaRemoveFields().
			//PARAMETERS:
			//       par_sFileName: Name of the from and to file, pipe-delimited,
			//           example: CN|MS will cause files between and including
			//           CN and MS to be updated. If blank, the process runs 
			//           for all files. If the parameter is not blank and 
			//           from and to files are the same, par_lFromBIID and 
			//           par_lToBIID parameters can be used to limit the
			//           records that are processed.
			//       par_lFromBIID:
			//           BI__ID value of the record to start from.
			//           If unspecified or -1, the processing starts at the first record
			//           in the table according to the BI__ID asc sort.
			//           Ignored if par_sFileName is blank.
			//       par_lToBIID:
			//           BI__ID value of the record to finish at.
			//           Ignored if par_sFileName is blank.
			//           If unspecified or -1, processing continues to the last record
			//           in the table according to the BI__ID asc sort.
			//       par_lRecs: no of records to process per rowset at a time. Default is 20.
			//       par_sReplaceChar: 1 or more characters to replace. This is typically "^",
			//           and is replaced with the double-quote (Chr(34)). If "", this and 
			//           the next parameters are ignored - no characters are replaced.
			//       par_sReplaceWithChar: 1 or more characters with which to replace 
			//           par_sReplaceChar. Typically, this is ". If blank and par_sReplaceChar
			//           is not blank, the par_sReplaceChar character(s) are deleted.
			//       par_bConvertASCIIToUnicode: When true, ASCII high characters (used in Cmc)
			//           are converted to Unicode. Run this ONLY if you are certain that high
			//           characters (ASCII 128+) are consistently coded as ASCII or this will
			//           corrupt accented, umlauted and other international characters in data!
			//RETURNS:
			//       True if successful, False otherwise.

			string sProc = "clSchema::ImportUpdateSysNames";
			goLog.Log(sProc, "Start par_sFileName: '" + par_sFileName + "' par_lFromBIID: '" + par_lFromBIID.ToString() + "' par_lToBIID: '" + par_lToBIID.ToString() + "' par_iRecs: '" + par_lRecs.ToString() + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			bool bResult = true;
			clArray aFiles = new clArray();
			string sFile = "";
			int i = 0;
			string sProgress = null;
			DateTime dtStarted = goTR.NowUTC();
			string sFromFile = "";
			string sToFile = "";
			bool bInclude = false;

			HttpContext.Current.Session.Timeout = 40000; //almost 4 weeks

			//Clear existing import progress logs in metadata
			goMeta.PageDelete("GLOBAL", "OTH_IMPORT_PROGRESS");

			sProgress = "SYS_Name updating started";
			goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SysNames", sProgress);

			bInclude = false;

			if (par_sFileName == "")
			{
				//All files
				sFromFile = "AA";
				sToFile = "ZZ";
				bInclude = true;
			}
			else
			{
				//Validate file names
				sFromFile = (goTR.ExtractString(par_sFileName, 1, "|")).ToUpper();
				if (!goData.IsFileValid(sFromFile))
				{
					goErr.SetError(10100, sProc, "", sFromFile);
					//E10100: Invalid file name '[1]'. [2]
					return false;
				}
				sToFile = (goTR.ExtractString(par_sFileName, 2, "|")).ToUpper();
				if (!goData.IsFileValid(sToFile))
				{
					goErr.SetError(10100, sProc, "", sToFile);
					//E10100: Invalid file name '[1]'. [2]
					return false;
				}
			}

			//Process
			if (sFromFile == sToFile)
			{
				//One file
				bResult = ImportUpdateSysNamesInTable(sFromFile, par_lFromBIID, par_lToBIID, par_lRecs, par_sReplaceChar, par_sReplaceWithChar, par_bConvertASCIIToUnicode);
			}
			else
			{
				//Multiple files
				aFiles = goData.GetFiles();
				for (i = 1; i <= aFiles.GetDimension(); i++)
				{
					sFile = aFiles.GetInfo(i).ToUpper();
					//If this the 'From' file, start processing
					if (sFile == sFromFile)
					{
						bInclude = true;
					}
					if (!bInclude)
					{
						goto ProcessNextFile;
					}
					switch (sFile)
					{
						case "MD":
						case "TN":
						case "XL":
						case "XP":
						case "XU":
							goto ProcessNextFile;
					}
					//Return True only if SchemaAddFieldsToTable returned true in all loops
					if (!ImportUpdateSysNamesInTable(sFile, -1, -1, par_lRecs, par_sReplaceChar, par_sReplaceWithChar, par_bConvertASCIIToUnicode))
					{
						bResult = false;
					}
	ProcessNextFile:
					//If this the 'To' file, exit the loop
					if (sFile == sToFile)
					{
						break;
					}
				}
			}

			//HttpContext.Current.Session.Timeout = 30

			sProgress = "SYS_Name updating ended (" + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtStarted, goTR.NowUTC()) + " mins)";
			goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SysNames_End", sProgress);

			return bResult;

		}

		private bool ImportUpdateSysNamesInTable(string par_sFileName, long par_lFromBIID = -1, long par_lToBIID = -1, long par_lRecs = 50, string par_sReplaceChar = "", string par_sReplaceWithChar = "", bool par_bConvertASCIIToUnicode = false)
		{
			//MI 9/16/08 Added par_bConvertASCIItoUnicode.
			//MI 1/30/08 Added support for processing FIL_Attachments in QT.
			//MI 6/22/07 Changed mapping Attached File attachment references from URL_URLs to FIL_Attachments.
			//MI 5/15/07 Added par_sReplaceChar, par_sReplaceWithChar params
			//MI 5/15/07 Added replacing ^ with ".
			//MI 3/23/07 Added copying URLs from attached files (AF) to URL_URLs in AC and PR.
			//MI 3/1/07 Implemented bBypassRecordOnSave in clRowset
			//MI 2/15/07 Created
			//PURPOSE:
			//       *** This is the slow rowset version. There is no fast version currently. ***
			//       Update SYS_Name values by opening each record and commit it.
			//PARAMETERS:
			//       par_sFileName: Name of the table, i.e. 'AC', 'CN', or 'OP'.
			//       par_lFromBIID: BI__ID value of the record from which to perform the update.
			//           If unspecified or -1, records with ID that is greater than or
			//           equal to this value are processed.
			//       par_lToBIID: BI__ID value of the record to which to perform the update.
			//           If unspecified or -1, records with ID that is less than or
			//           equal to this value are processed.
			//       par_lRecs: number of records to process in a batch. Default is 20.
			//           A large number will cause the server to use more memory and 
			//           take longer to finish a batch.
			//       par_sReplaceChar: 1 or more characters to replace. This is typically "^",
			//           and is replaced with the double-quote (Chr(34)). If "", this and 
			//           the next parameters are ignored - no characters are replaced.
			//       par_sReplaceWithChar: 1 or more characters with which to replace 
			//           par_sReplaceChar. Typically, this is ". If blank and par_sReplaceChar
			//           is not blank, the par_sReplaceChar character(s) are deleted.
			//       par_bConvertASCIIToUnicode: When true, ASCII high characters (used in Cmc)
			//           are converted to Unicode. Run this ONLY if you are certain that high
			//           characters (ASCII 128+) are consistently coded as ASCII or this will
			//           corrupt accented, umlauted and other international characters in data!
			//RETURNS:
			//       True if successful, False if anything failed.

			string sproc = "clSchema::ImportUpdateSysNamesInTable";
			goLog.Log(sproc, "Start par_sFileName: '" + par_sFileName + "' par_lFromBIID: '" + par_lFromBIID.ToString() + "' par_lToBIID: '" + par_lToBIID.ToString() + "' par_lRecs: '" + par_lRecs.ToString() + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			bool bResult = true;
			clArray aFields = new clArray();
			long lBI__ID = -1;
			int iIterationsDone = 0;
			string sProgress = null;
			DateTime dtStarted = goTR.NowUTC();
			string sFilter = "";
			long lFromBIID = par_lFromBIID;
			long lToBIID = par_lToBIID;
			long lLowestBIID = 0;
			long lHighestBIID = 0;
			long lCurrentBIID = 0;
			long lRecs = par_lRecs; //process 20 records per rowset
			string sImportID = null;
			string sGID_ID = null;
			bool bTXTImportIDExists = false;
			clRowSet doRS = null;
			string sTemp = "";
			string sTemp2 = "";
			bool bACURLsExist = true;
			bool bPRURLsExist = true;
			bool bQTURLsExist = true;
			int j = 0;
			string sField = null;
			string sPrefix = null;

			//Try

			if (!goData.IsFileValid(par_sFileName))
			{
					goErr.SetError(10100, sproc, "", par_sFileName);
					//E10100: Invalid file name '[1]'. [2]
					return false;
				}

				//Refresh and load field data
				goData.LoadFieldData();
				aFields = goData.GetFields(par_sFileName);

				sProgress = "   -----------------------------------";
				goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SysNames_" + par_sFileName + "_Start1", sProgress);
				sProgress = "   " + par_sFileName + ": SYS_Name updating: started";
				goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SysNames_" + par_sFileName + "_Start2", sProgress);

				if (!goData.IsFieldValid(par_sFileName, "BI__ID"))
				{
					sProgress = "   " + par_sFileName + ": SYS_Name updating: FAILED (field BI__ID doesn't exist in this table)";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SysNames_" + par_sFileName + "_End", sProgress);
					goP.sRunMode = clC.SELL_MODE_MAIN;
					return false;
				}

				if (goData.IsFieldValid(par_sFileName, "TXT_ImportID"))
				{
					bTXTImportIDExists = true;
				}

				//----------------------- Open and save records -------------------------
				//Determine the lowest and highest BI__IDs in the whole file
				clRowSet doFirst = new clRowSet(par_sFileName, clC.SELL_READONLY, "", "BI__ID ASC", "BI__ID", 1);
				if (doFirst.Count() < 1)
				{
					//File is empty
					sProgress = "   " + par_sFileName + ": SYS_Name updating: EMPTY FILE";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SysNames_" + par_sFileName + "_End", sProgress);
					goP.sRunMode = clC.SELL_MODE_MAIN;
					return true;
				}
				else
				{
					lLowestBIID = Convert.ToInt64(doFirst.GetFieldVal("BI__ID", 2));
				}
				if (doFirst != null)
				{
					doFirst = null;
				}
				//Determine the highest BI__ID
				clRowSet doLast = new clRowSet(par_sFileName, clC.SELL_READONLY, "", "BI__ID DESC", "BI__ID", 1);
				if (doLast.Count() < 1)
				{
					//File is empty
					sProgress = "   " + par_sFileName + ": SYS_Name updating: EMPTY FILE";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SysNames_" + par_sFileName + "_End", sProgress);
					goP.sRunMode = clC.SELL_MODE_MAIN;
					return true;
				}
				else
				{
					lHighestBIID = Convert.ToInt64(doLast.GetFieldVal("BI__ID", 2));
				}
				if (doLast != null)
				{
					doLast = null;
				}

				//Adjust 'From' and 'To' BI__IDs if they are 'all' or outside of the range 
				//of BI__IDs that are in the DB
				if (lFromBIID < lLowestBIID || lFromBIID == -1)
				{
					lFromBIID = lLowestBIID;
				}
				if (lToBIID > lHighestBIID || lToBIID == -1)
				{
					lToBIID = lHighestBIID;
				}

				sProgress = "      " + "Processing BI__IDs from " + lFromBIID.ToString() + " to " + lToBIID.ToString();
				goLog.Log(sproc, sProgress, (short)clC.SELL_LOGLEVEL_DETAILS, true);

				goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SysNames_" + par_sFileName + "_Start3", sProgress);

				//Set the current record counter to the 'from' BI__ID
				lCurrentBIID = lFromBIID;

				//This turns off link processing in RecordOnSave scripts, improving performance
				goP.sRunMode = clC.SELL_MODE_IMPORT;

				//Test whether fields needed for copying attached file (AF) URLs exist
				if (!goData.IsFieldValid("AF", "URL_FileOnServer"))
				{
					bACURLsExist = false;
					bPRURLsExist = false;
					bQTURLsExist = false;
				}
				else
				{
					if (!goData.IsFieldValid("AC", "FIL_Attachments"))
					{
						bACURLsExist = false;
					}
					if (!goData.IsFieldValid("AC", "LNK_Attached_AF"))
					{
						bACURLsExist = false;
					}
					if (!goData.IsFieldValid("PR", "FIL_Attachments"))
					{
						bPRURLsExist = false;
					}
					if (!goData.IsFieldValid("PR", "LNK_Attached_AF"))
					{
						bPRURLsExist = false;
					}
					if (!goData.IsFieldValid("QT", "FIL_Attachments"))
					{
						bQTURLsExist = false;
					}
					if (!goData.IsFieldValid("QT", "LNK_Attached_AF"))
					{
						bQTURLsExist = false;
					}
				}

				//---------- Start rowset iteration loop ---------------
				//This iterates rowsets for lRecs records at a time.
				iIterationsDone = 0;
				do
				{
					//DEBUG
					//If iIterationsDone > 2 Then Exit Do
					//END DEBUG
					//Get the records. This is forcing no links. gop.sRunMode MUST be set to "Import" prior to this
					//or Commit will fail due to RecordOnSave script attempting to evaluate links.
					//doRS = New clRowSet(par_sFileName, clC.SELL_EDIT, "BI__ID > " & lBI__ID, "BI__ID ASC", "NOLINKS", par_iRecs)

					if (lCurrentBIID > lToBIID)
					{
						break;
					}

					//------------- Build the filter string --------------
					if ((lCurrentBIID + lRecs - 1) > lToBIID)
					{
						sFilter = "BI__ID >= " + lCurrentBIID + " AND BI__ID <= " + lToBIID;
					}
					else
					{
						sFilter = "BI__ID >= " + lCurrentBIID + " AND BI__ID <= " + (lCurrentBIID + lRecs - 1);
					}

					//------------ Go through the rowset and commit each record -----------
					//Get records with all links (**) or fetch links dynamically (*).
					//Informal benchmarking on a small database shows * as tiny bit slower. The advantage may be
					//in large files with a lot of links when processing list files with lots of links such as User,
					//Group, Product, Company, etc.
					//Last parameter turns off validation code in RecordOnSave scripts to prevent warnings from
					//being generated and to improve performance.
					doRS = new clRowSet(par_sFileName, clC.SELL_EDIT, sFilter, "BI__ID ASC", "**", -1, "", "", "", "", "", true, true);
					if (doRS.Count() < 1)
					{
						//No records found
						//Can get here if records were deleted after import and BI__ID values are not contiguous
					}
					else
					{
						//Loop through records
						do
						{
							lBI__ID = Convert.ToInt64(doRS.GetFieldVal("BI__ID", clC.SELL_SYSTEM));
							if (bTXTImportIDExists)
							{
								sImportID = Convert.ToString(doRS.GetFieldVal("TXT_ImportID"));
							}
							else
							{
								sImportID = "";
							}
							sGID_ID = Convert.ToString(doRS.GetFieldVal("GID_ID"));
							//----- Copy URL_FileOnServer from linked AF record(s) into the FIL_Attachments field
							switch (par_sFileName.ToUpper())
							{
								case "AC":
									if (bACURLsExist)
									{
										sTemp = goTR.RemoveCR(Convert.ToString(doRS.GetFieldVal("LNK_Attached_AF%%URL_FileOnServer", clC.SELL_FRIENDLY)));
										if (sTemp != "")
										{
											//There are attached AF URLs to copy
											sTemp2 = Convert.ToString(doRS.GetFieldVal("FIL_Attachments"));
											if (sTemp2 != "")
											{
												//FIL_Attachments field already has something in it - preserve it by appending the AF URLs
												sTemp = goTR.RemoveCR(sTemp2) + "\r\n" + sTemp;
											}
											//Copy the URLs
											doRS.SetFieldVal("FIL_Attachments", sTemp);
										}
									}
									break;
								case "PR":
									if (bPRURLsExist)
									{
										sTemp = goTR.RemoveCR(Convert.ToString(doRS.GetFieldVal("LNK_Attached_AF%%URL_FileOnServer", clC.SELL_FRIENDLY)));
										if (sTemp != "")
										{
											//There are attached AF URLs to copy
											sTemp2 = Convert.ToString(doRS.GetFieldVal("FIL_Attachments"));
											if (sTemp2 != "")
											{
												//FIL_Attachments field already has something in it - preserve it by appending the AF URLs
												sTemp = goTR.RemoveCR(sTemp2) + "\r\n" + sTemp;
											}
											//Copy the URLs
											doRS.SetFieldVal("FIL_Attachments", sTemp);
										}
									}
									break;
								case "QT":
									if (bQTURLsExist)
									{
										sTemp = goTR.RemoveCR(Convert.ToString(doRS.GetFieldVal("LNK_Attached_AF%%URL_FileOnServer", clC.SELL_FRIENDLY)));
										if (sTemp != "")
										{
											//There are attached AF URLs to copy
											sTemp2 = Convert.ToString(doRS.GetFieldVal("FIL_Attachments"));
											if (sTemp2 != "")
											{
												//FIL_Attachments field already has something in it - preserve it by appending the AF URLs
												sTemp = goTR.RemoveCR(sTemp2) + "\r\n" + sTemp;
											}
											//Copy the URLs
											doRS.SetFieldVal("FIL_Attachments", sTemp);
										}
									}
									break;
							}

							//--------------- REPLACE CHARACTERS IN TXT and MMO FIELDS ----------------
							if (par_sReplaceChar != "")
							{
								//Loop through all fields -----
								for (j = 1; j <= aFields.GetDimension(); j++)
								{
									sField = aFields.GetInfo(j);
									//Process only permanent TXT and MMO fields (temp fields have _ in position 8: MMO_LNK_xxx)
									if (goTR.FromTo(sField, 8, 8) == "_")
									{
										goto ProcessNextTextField;
									}
									//Skip TXT_ImportID field, which is used for linking the imported records
									if (sField.ToUpper() == "TXT_IMPORTID")
									{
										goto ProcessNextTextField;
									}
									sPrefix = goTR.GetPrefix(sField);
									switch (sPrefix)
									{
										case "TXT_":
										case "MMO_":
										case "MMR_":
										case "TEL_":
										case "EML_":
										case "URL_":
										case "FIL_":
											sTemp = Convert.ToString(doRS.GetFieldVal(sField));
											//Replace a character like ^ with double quote ". Double quotes are typically replaced in Cmc
											//to avoid delimiter issues in comma-delimited export files.
											sTemp = goTR.Replace(sTemp, par_sReplaceChar, par_sReplaceWithChar);
											if (par_bConvertASCIIToUnicode)
											{
												//Replace ASCII international characters with unicode (ANSI?) equivalents in SS.
												//Note: Commence exports int'l characters using the old ASCII mapping. In Windows XP
												//Character map, this is the character set 'DOS: United States'. We are converting special characters
												//to the XP Unicode character set mapping.
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(128).ToString(), Microsoft.VisualBasic.Strings.Chr(199).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(129).ToString(), Microsoft.VisualBasic.Strings.Chr(252).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(130).ToString(), Microsoft.VisualBasic.Strings.Chr(233).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(131).ToString(), Microsoft.VisualBasic.Strings.Chr(226).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(132).ToString(), Microsoft.VisualBasic.Strings.Chr(228).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(133).ToString(), Microsoft.VisualBasic.Strings.Chr(224).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(134).ToString(), Microsoft.VisualBasic.Strings.Chr(229).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(135).ToString(), Microsoft.VisualBasic.Strings.Chr(231).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(136).ToString(), Microsoft.VisualBasic.Strings.Chr(234).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(137).ToString(), Microsoft.VisualBasic.Strings.Chr(235).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(138).ToString(), Microsoft.VisualBasic.Strings.Chr(232).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(139).ToString(), Microsoft.VisualBasic.Strings.Chr(239).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(140).ToString(), Microsoft.VisualBasic.Strings.Chr(238).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(141).ToString(), Microsoft.VisualBasic.Strings.Chr(236).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(142).ToString(), Microsoft.VisualBasic.Strings.Chr(196).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(143).ToString(), Microsoft.VisualBasic.Strings.Chr(197).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(144).ToString(), Microsoft.VisualBasic.Strings.Chr(201).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(145).ToString(), Microsoft.VisualBasic.Strings.Chr(230).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(146).ToString(), Microsoft.VisualBasic.Strings.Chr(198).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(147).ToString(), Microsoft.VisualBasic.Strings.Chr(244).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(148).ToString(), Microsoft.VisualBasic.Strings.Chr(246).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(149).ToString(), Microsoft.VisualBasic.Strings.Chr(242).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(150).ToString(), Microsoft.VisualBasic.Strings.Chr(251).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(151).ToString(), Microsoft.VisualBasic.Strings.Chr(249).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(152).ToString(), Microsoft.VisualBasic.Strings.Chr(255).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(153).ToString(), Microsoft.VisualBasic.Strings.Chr(214).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(154).ToString(), Microsoft.VisualBasic.Strings.Chr(220).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(155).ToString(), Microsoft.VisualBasic.Strings.Chr(162).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(156).ToString(), Microsoft.VisualBasic.Strings.Chr(163).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(157).ToString(), Microsoft.VisualBasic.Strings.Chr(165).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(158).ToString(), ((char)8359).ToString()); //(Pts - Pesetas)
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(159).ToString(), Microsoft.VisualBasic.Strings.Chr(131).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(160).ToString(), Microsoft.VisualBasic.Strings.Chr(225).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(161).ToString(), Microsoft.VisualBasic.Strings.Chr(237).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(162).ToString(), Microsoft.VisualBasic.Strings.Chr(243).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(163).ToString(), Microsoft.VisualBasic.Strings.Chr(250).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(164).ToString(), Microsoft.VisualBasic.Strings.Chr(241).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(165).ToString(), Microsoft.VisualBasic.Strings.Chr(209).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(166).ToString(), Microsoft.VisualBasic.Strings.Chr(170).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(167).ToString(), Microsoft.VisualBasic.Strings.Chr(186).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(168).ToString(), Microsoft.VisualBasic.Strings.Chr(191).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(169).ToString(), ((char)8976).ToString()); //(left-closing bracket)
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(170).ToString(), Microsoft.VisualBasic.Strings.Chr(172).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(171).ToString(), Microsoft.VisualBasic.Strings.Chr(189).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(172).ToString(), Microsoft.VisualBasic.Strings.Chr(188).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(173).ToString(), Microsoft.VisualBasic.Strings.Chr(161).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(174).ToString(), Microsoft.VisualBasic.Strings.Chr(171).ToString()); //�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(175).ToString(), Microsoft.VisualBasic.Strings.Chr(187).ToString()); //�
												//The commented characters would be double-converted, do not uncomment them!
												//sTemp = goTR.Replace(sTemp, Chr(225), Chr(223))    '�
												//sTemp = goTR.Replace(sTemp, Chr(230), Chr(181))    '�
												//sTemp = goTR.Replace(sTemp, Chr(241), Chr(177))    '�
												//sTemp = goTR.Replace(sTemp, Chr(246), Chr(247))    '�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(248).ToString(), Microsoft.VisualBasic.Strings.Chr(176).ToString()); //�
												//sTemp = goTR.Replace(sTemp, Chr(250), Chr(183))    '�
												sTemp = goTR.Replace(sTemp, Microsoft.VisualBasic.Strings.Chr(253).ToString(), Microsoft.VisualBasic.Strings.Chr(178).ToString()); //�
											}

											//Set the converted string back into the field
											doRS.SetFieldVal(sField, sTemp);
											break;
									}
	ProcessNextTextField: ;
								}
							}

							//Commit the record
							goLog.Log(sproc, "Committing record BI__ID '" + lBI__ID.ToString() + "' sImportID: '" + sImportID + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);
							//DEBUG
							//If UCase(sGID_ID) = "1ACD4972-A163-4BDA-4143-975300E845ED" Then Stop
							if (doRS.Commit() != 1)
							{
								goP.sRunMode = clC.SELL_MODE_MAIN;
								goErr.SetError(35000, sproc, "Committing " + par_sFileName + " record BI__ID '" + lBI__ID.ToString() + "' (" + sImportID + " [" + sGID_ID + "]) failed. Import fields processing stopped.");
								bResult = false;
								break;
							}
							if (doRS.GetNext() != 1)
							{
								break;
							}
						} while (true);
					}
					lCurrentBIID = lCurrentBIID + lRecs;
					iIterationsDone = iIterationsDone + 1;
					sProgress = "   " + par_sFileName + ": SYS_Name updating: approx. " + (iIterationsDone * par_lRecs).ToString() + " recs processed. " + "Current BI__ID: " + lCurrentBIID + ".";
					goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SysNames_" + par_sFileName + "_End", sProgress);
				} while (true);

				if (doRS != null)
				{
					doRS = null;
				}

				sProgress = "   " + par_sFileName + ": SYS_Name updating: SUCCESS (" + (lToBIID - lFromBIID + 1).ToString() + " recs in " + DateHelper.DateDiff(DateHelper.DateInterval.Minute, dtStarted, goTR.NowUTC()) + " mins)";
				goMeta.LineWrite("GLOBAL", "OTH_IMPORT_PROGRESS", "SysNames_" + par_sFileName + "_End", sProgress);
				goLog.Log(sproc, sProgress, (short)clC.SELL_LOGLEVEL_DETAILS, true);


			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sproc)
			//    End If
			//End Try

			goP.sRunMode = clC.SELL_MODE_MAIN;

			return bResult;

		}


		public bool SchemaAddFields(string par_sFileName = "", string par_sPurpose = "IMPORT")
		{
			//MI 12/10/07 Added support for from and to files in par_sFileName
			//MI 11/1/06 Not finished.

			//AUTHOR: MI 10/31/06
			//PURPOSE:
			//       For each field and link in files with 2-character names (main files),
			//       or in a particular file, this creates a redundant varchar or nvarchar
			//       field named TXT_<FieldName>. These fields are for the initial data import
			//       by an external utility. Once the utility populates data in all TXT and MMO
			//       fields, a second stage import process can be executed that populates
			//       "real" fields with the temporary data.
			//PARAMETERS:
			//       par_sFileName: Name of the from and to file, pipe-delimited,
			//           example: CN|MS will cause redundant fields to be created in 
			//           files between and including CN and MS to be updated. 
			//           If blank, redundant fields are created in all files. 
			//       par_sPurpose: IMPORT - populate tables with redundant TXT fields needed
			//           for import of existing customers' data. After these fields are populated
			//           with textual values of numeric, datetime, and link fields, another
			//           process is called to take those values into the real, typed fields.

			string sProc = "clSchema::SchemaAddFields";
			goLog.Log(sProc, "Start par_sFileName: '" + par_sFileName + "' par_sPurpose: '" + par_sPurpose + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			clArray aFiles = new clArray();
			clArray aFields = new clArray();
			int i = 0;
			string sFile = null;
			bool bResult = true;
			string sFromFile = "";
			string sToFile = "";
			bool bInclude = false;

			HttpContext.Current.Session.Timeout = 40000;

			//------------- First add AF table, links, and MD ----------------
			//AF (Attached File) is added to map to the File category in Cmc.
			//Its content is processed later into FIL and URL fields in 
			//individual files (PR, AC).
			bResult = CreateAFFile();

			//Refresh schema data tables
			goData.LoadTableData();
			goData.LoadFieldData();
			goData.LoadLinkData();

			//--------------- Normal field adding ---------------
			bInclude = false;

			if (par_sFileName == "")
			{
				//All files
				sFromFile = "AA";
				sToFile = "ZZ";
				bInclude = true;
			}
			else
			{
				//Validate file names
				sFromFile = (goTR.ExtractString(par_sFileName, 1, "|")).ToUpper();
				if (!goData.IsFileValid(sFromFile))
				{
					goErr.SetError(10100, sProc, "", sFromFile);
					//E10100: Invalid file name '[1]'. [2]
					return false;
				}
				sToFile = (goTR.ExtractString(par_sFileName, 2, "|")).ToUpper();
				if (!goData.IsFileValid(sToFile))
				{
					goErr.SetError(10100, sProc, "", sToFile);
					//E10100: Invalid file name '[1]'. [2]
					return false;
				}
			}

			//Process
			if (sFromFile == sToFile)
			{
				//One file
				bResult = SchemaAddFieldsToTable(sFromFile, par_sPurpose);
			}
			else
			{
				//Multiple files
				aFiles = goData.GetFiles();
				for (i = 1; i <= aFiles.GetDimension(); i++)
				{
					sFile = aFiles.GetInfo(i).ToUpper();
					//If this the 'From' file, start processing
					if (sFile == sFromFile)
					{
						bInclude = true;
					}
					if (!bInclude)
					{
						goto ProcessNextFile;
					}
					switch (sFile)
					{
						case "MD":
						case "TN":
						case "XL":
						case "XP":
						case "XU":
							goto ProcessNextFile;
					}
					//Return True only if SchemaAddFieldsToTable returned true in all loops
					if (!SchemaAddFieldsToTable(sFile, par_sPurpose))
					{
						bResult = false;
					}
	ProcessNextFile:
					//If this the 'To' file, exit the loop
					if (sFile == sToFile)
					{
						break;
					}
				}
			}

			//HttpContext.Current.Session.Timeout = 30

			return bResult;

		}


		public bool SchemaRemoveFields(string par_sFileName = "", string par_sPurpose = "IMPORT")
		{
			//MI 12/10/07 Added support for from and to files to par_sFileName parameter.
			//MI 11/1/06 Not finished.

			//AUTHOR: MI 10/31/06
			//PURPOSE:
			//       Remove redundant fields created by SchemaAddFields.
			//PARAMETERS:
			//       par_sFileName: Name of the from and to file, pipe-delimited.
			//           Example: CN|MS will cause temp fields to be removed between 
			//           and including CN and MS files. If blank, the process runs 
			//           for all files.
			//       par_sPurpose: IMPORT - removes redundant TXT fields needed
			//           for import of existing customers' data.

			string sProc = "clSchema::SchemaRemoveFields";
			goLog.Log(sProc, "Start par_sFileName: '" + par_sFileName + "' par_sPurpose: '" + par_sPurpose + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			clArray aFiles = new clArray();
			clArray aFields = new clArray();
			int i = 0;
			string sFile = null;
			bool bResult = true;
			string sFromFile = "";
			string sToFile = "";
			bool bInclude = false;

			HttpContext.Current.Session.Timeout = 40000;

			bInclude = false;

			if (par_sFileName == "")
			{
				//All files
				sFromFile = "AA";
				sToFile = "ZZ";
				bInclude = true;
			}
			else
			{
				//Validate file names
				sFromFile = (goTR.ExtractString(par_sFileName, 1, "|")).ToUpper();
				if (!goData.IsFileValid(sFromFile))
				{
					goErr.SetError(10100, sProc, "", sFromFile);
					//E10100: Invalid file name '[1]'. [2]
					return false;
				}
				sToFile = (goTR.ExtractString(par_sFileName, 2, "|")).ToUpper();
				if (!goData.IsFileValid(sToFile))
				{
					goErr.SetError(10100, sProc, "", sToFile);
					//E10100: Invalid file name '[1]'. [2]
					return false;
				}
			}

			goData.LoadFieldData();

			//Process
			if (sFromFile == sToFile)
			{
				//One file
				bResult = SchemaRemoveFieldsFromTable(sFromFile, par_sPurpose);
			}
			else
			{
				//Multiple files
				aFiles = goData.GetFiles();
				for (i = 1; i <= aFiles.GetDimension(); i++)
				{
					sFile = aFiles.GetInfo(i).ToUpper();
					//If this the 'From' file, start processing
					if (sFile == sFromFile)
					{
						bInclude = true;
					}
					if (!bInclude)
					{
						goto ProcessNextFile;
					}
					switch (sFile)
					{
						case "MD":
						case "TN":
						case "XL":
						case "XP":
						case "XU":
							goto ProcessNextFile;
					}
					//Return True only if SchemaAddFieldsToTable returned true in all loops
					if (!SchemaRemoveFieldsFromTable(sFile, par_sPurpose))
					{
						bResult = false;
					}
	ProcessNextFile:
					//If this the 'To' file, exit the loop
					if (sFile == sToFile)
					{
						break;
					}
				}
			}

			//HttpContext.Current.Session.Timeout = 30

			return bResult;

		}

		public bool SchemaAddFieldsToTable(string par_sFileName, string par_sPurpose, clArray par_aLinks = null, bool par_bLinksOnly = false)
		{

			//PJ 5/3/11 Added Optional params: par_aFields, par_bLinksOnly. Changed function from Private to Public
			//MI 3/2/09 Writing LST MD to XX product.
			//MI 5/16/08 Added brackets in SQL statement(s).
			//MI 4/16/07 Changed ntext to text for temp NN link fields (MMO_LNK_...)
			//MI 3/5/07 Added adding TXT_CHK_Urgent.
			//MI 2/16/07 Added solution for importing CN.Review combo into CHK_Review.
			//MI 11/1/06.

			//AUTHOR: MI 11/1/06
			//PURPOSE:
			//       Create redundant varchar or nvarchar fields named TXT_<FieldName> in a single
			//       data table. This is called from SchemaAddFields.
			//PARAMETERS:
			//       par_sFileName: Name of the file in which to create redundant fields.
			//       par_sPurpose: IMPORT - populate tables with redundant TXT fields needed
			//           for import of existing customers' data.
			//       par_aLinks: Array of link names that need a temp field. For import utility process
			//       par_bLinksOnly: True if called from import utility. In this case, only the links passed in par_aLinks get temp fields
			//RETURNS:
			//       True when successful, False when file is unsupported. SQL server errors
			//       are not checked.

			string sProc = "clSchema::SchemaAddFieldsToTable";
			goLog.Log(sProc, "Start par_sFileName: '" + par_sFileName + "' par_sPurpose: '" + par_sPurpose + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			bool bResult = true;

			//Try

			clArray aFields = new clArray();
				int j = 0;
				string sField = null;
				string sPrefix = null;
				string sFieldToCreate = null;
				string sFieldType = null; //Example: varchar(20)
				string sSQL = "";
				string sImportIDField = "TXT_ImportID";
				string s = "";

				if (!goData.IsFileValid(par_sFileName))
				{
					goErr.SetError(10100, sProc, "", par_sFileName);
					//E10100: Invalid file name '[1]'. [2]
					return false;
				}
				//----------------------- Process fields -------------------------

				if (!par_bLinksOnly)
				{
					goData.LoadFieldData();
					aFields = goData.GetFields(par_sFileName);

					for (j = 1; j <= aFields.GetDimension(); j++)
					{
						sField = aFields.GetInfo(j);
						//Exclude particular fields
						sFieldToCreate = "TXT_" + sField;
						switch (sField.ToUpper())
						{
							case "BI__ID":
							case "SI__SHARESTATE": //, "DTT_CREATIONTIME", "DTT_MODTIME", "TXT_MODBY"
								//Skip read-only and system fields
								//MI 11/24/06: remove fields above that can be filled in SS
								goto ProcessNextField;
							default:
								//Exclude all fields that start with a particular prefix
								sPrefix = goTR.GetPrefix(sField);
								switch (sPrefix)
								{
									case "TXT_":
									case "MMO_":
									case "MMR_":
									case "GID_":
									case "EML_":
									case "FIL_":
									case "SYS_":
									case "TEL_":
									case "URL_":
									case "BIN_":
									case "DTE_":
									case "TME_":
										//Skipping these because they can be filled directly without conversion
										//DTE and TME fields are virtual fields. Only DTTs are real datetime fields.
										goto ProcessNextField;
									case "BI__": //Big integer (bigint in SQL Server)
										sFieldType = "varchar(30)";
										break;
									case "CHK_": //Checkbox (internally short int)
										if (sField.ToUpper() == "CHK_REVIEW" && par_sFileName.ToUpper() == "CN")
										{
											//In Cmc the field 'Review' is a combo. We are creating MLS_Review here and TXT_MLS_Review
											//so that this field can be mapped. We later process TXT_MLS_Review value into CHK_Review.
											//Create the substitute MLS field
											sFieldType = "smallint";
											sFieldToCreate = "MLS_" + goTR.RemovePrefix(sField);
											sSQL += "IF NOT EXISTS (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS" + "\r\n" + "WHERE TABLE_NAME = '" + par_sFileName + "' and COLUMN_NAME = '" + sFieldToCreate + "')" + "\r\n" + "ALTER TABLE [" + par_sFileName + "]" + "\r\n" + "ADD [" + sFieldToCreate + "] " + sFieldType + " NULL " + "\r\n";
											//Create the temp field for the MLS field
											sFieldType = "varchar(30)";
											sFieldToCreate = "TXT_MLS_" + goTR.RemovePrefix(sField);
											sSQL += "IF NOT EXISTS (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS" + "\r\n" + "WHERE TABLE_NAME = '" + par_sFileName + "' and COLUMN_NAME = '" + sFieldToCreate + "')" + "\r\n" + "ALTER TABLE [" + par_sFileName + "]" + "\r\n" + "ADD [" + sFieldToCreate + "] " + sFieldType + " NULL " + "\r\n";
											//Add MLS definition MD page
											s = "";
											goTR.StrWrite(ref s, "US_0", "Do Not Review");
											goTR.StrWrite(ref s, "US_1", "Review");
											goTR.StrWrite(ref s, "US_NAME", "CONTACT:REVIEW");
											goTR.StrWrite(ref s, "DEFAULT", "0");
											goTR.StrWrite(ref s, "SORT", "NUMERIC");
											goMeta.PageWrite("GLOBAL", "LST_CN:REVIEW", s, "", "", "XX");
											goto ProcessNextField;
										}
										sFieldType = "varchar(5)";
										break;
									case "CMB_": //Combo box
										sFieldType = "varchar(10)";
										break;
									case "CUR_": //Currency
										sFieldType = "varchar(30)";
										break;
									case "DR__": //Double Real
										sFieldType = "varchar(30)";
										break;
									case "DTT_":
										//Create TXT_DTE_ and TXT_TME_ fields instead of TXT_DTT_ field.
										sFieldType = "varchar(12)";
										sFieldToCreate = "TXT_DTE_" + goTR.RemovePrefix(sField);
										sSQL += "IF NOT EXISTS (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS" + "\r\n" + "WHERE TABLE_NAME = '" + par_sFileName + "' and COLUMN_NAME = '" + sFieldToCreate + "')" + "\r\n" + "ALTER TABLE [" + par_sFileName + "]" + "\r\n" + "ADD [" + sFieldToCreate + "] " + sFieldType + " NULL " + "\r\n";
										sFieldToCreate = "TXT_TME_" + goTR.RemovePrefix(sField);
										sSQL += "IF NOT EXISTS (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS" + "\r\n" + "WHERE TABLE_NAME = '" + par_sFileName + "' and COLUMN_NAME = '" + sFieldToCreate + "')" + "\r\n" + "ALTER TABLE [" + par_sFileName + "]" + "\r\n" + "ADD [" + sFieldToCreate + "] " + sFieldType + " NULL " + "\r\n";
										goto ProcessNextField;
									case "INT_": //Integer
										sFieldType = "varchar(13)";
										break;
									case "LI__": //Long integer
										sFieldType = "varchar(20)";
										break;
									case "LNK_": //Link
										sFieldToCreate = "MMO_" + sField;
										sFieldType = "text";
										break;
									case "LST_": //Listbox
										sFieldType = "varchar(10)";
										break;
									case "MLS_": //Metadata list (combobox- or listbox-type field automatically filled from a list in _META)
										sFieldType = "varchar(60)";
										break;
									case "SEL_": //Selector (radio buttons)
										sFieldType = "varchar(10)";
										break;
									case "SI__": //Short integer
										sFieldType = "varchar(8)";
										break;
									case "SR__": //Single Real
										sFieldType = "varchar(18)";
										break;
									default:
										//Invalid prefix
										goErr.SetWarning(35000, sProc, "Unsupported field type: '" + sField + "'.");
										goto ProcessNextField;
								}
								break;
						}
						//'----------------------
						//'OLD: Create the new field
						//'Example:
						//'ALTER TABLE AC
						//'ADD TXT_DTT_StartTime varchar(20) NULL
						//sSQL = "ALTER TABLE " & par_sFileName & vbCrLf & _
						//"ADD " & sFieldToCreate & " " & sFieldType & " NULL "
						//'"DEFAULT cast(GetUTCDate() as varchar(20)) WITH VALUES" & vbcrlf
						//goData.RunSQLQuery(sSQL)

						//----------------------
						//Create the new field
						//Example:
						//IF NOT EXISTS (SELECT COLUMN_NAME FROM(INFORMATION_SCHEMA.COLUMNS)
						//WHERE TABLE_NAME = 'AC' and COLUMN_NAME = 'TXT_DTT_StartTime')
						//ALTER TABLE [AC]
						//ADD [TXT_DTT_StartTime] varchar(20) NULL
						sSQL += "IF NOT EXISTS (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS" + "\r\n" + "WHERE TABLE_NAME = '" + par_sFileName + "' and COLUMN_NAME = '" + sFieldToCreate + "')" + "\r\n" + "ALTER TABLE [" + par_sFileName + "]" + "\r\n" + "ADD [" + sFieldToCreate + "] " + sFieldType + " NULL " + "\r\n";
						//"DEFAULT cast(GetUTCDate() as varchar(20)) WITH VALUES" & vbcrlf

	ProcessNextField: ;
					}
				}

				//--------------- Create TD.TXT_CHK_Urgent ---------------
				//This field is needed to map Urgent checkbox from Cmc and process it later
				//to MLS_Priority in SellSQL.
				if (par_sFileName.ToUpper() == "TD")
				{
					sFieldType = "varchar(5)";
					sFieldToCreate = "TXT_CHK_Urgent";
					sSQL += "IF NOT EXISTS (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS" + "\r\n" + "WHERE TABLE_NAME = '" + par_sFileName + "' and COLUMN_NAME = '" + sFieldToCreate + "')" + "\r\n" + "ALTER TABLE [" + par_sFileName + "]" + "\r\n" + "ADD [" + sFieldToCreate + "] " + sFieldType + " NULL " + "\r\n";
				}


				//------------------- Process links ----------------------

				//----- PJ: Get fields from par_aFields -----
				if (par_aLinks == null)
				{
					goData.LoadLinkData();
					aFields = goData.GetLinks(par_sFileName);
				}
				else
				{
					aFields = par_aLinks;
				}

				//aFields = New clArray
				//aFields = goData.GetLinks(par_sFileName)
				//-------------------------------------------

				for (j = 1; j <= aFields.GetDimension(); j++)
				{
					sField = aFields.GetInfo(j);
					//Exclude particular fields
					if (goTR.FromTo(goData.LKGetType(par_sFileName, sField), 2, 2) == "1")
					{
						//1 link
						sFieldToCreate = "TXT_" + sField;
						sFieldType = "varchar(50)";
					}
					else
					{
						//N link
						sFieldToCreate = "MMO_" + sField;
						sFieldType = "text"; //changed from ntext
					}
					//----------------------
					//Create the new field
					//Example:
					//IF NOT EXISTS (SELECT COLUMN_NAME FROM(INFORMATION_SCHEMA.COLUMNS)
					//WHERE TABLE_NAME = 'AC' and COLUMN_NAME = 'TXT_LNK_CreditedTo_US')
					//ALTER TABLE [AC]
					//ADD [TXT_LNK_CreditedTo_US] varchar(80) NULL
					sSQL += "IF NOT EXISTS (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS" + "\r\n" + "WHERE TABLE_NAME = '" + par_sFileName + "' and COLUMN_NAME = '" + sFieldToCreate + "')" + "\r\n" + "ALTER TABLE [" + par_sFileName + "]" + "\r\n" + "ADD [" + sFieldToCreate + "] " + sFieldType + " NULL " + "\r\n";
					//"DEFAULT cast(GetUTCDate() as varchar(20)) WITH VALUES" & vbcrlf
	ProcessNextLink: ;
				}


				//'-----------------------
				//'Create a new field for storing the imported ID/Name value
				//'ALTER TABLE AC 
				//'ADD TXT_ImportID varchar(50) NULL
				//sSQL = "ALTER TABLE " & par_sFileName & vbCrLf & _
				//"ADD " & sImportIDField & " " & "varchar(80)" & " NULL "

				goLog.Log(sProc, "Running RunSQLQuery: '" + sSQL + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);
				if (!goData.RunSQLQuery(sSQL))
				{
					bResult = false;
				}

				//-----------------------
				//Create a new field for storing the imported ID/Name value
				//IF NOT EXISTS (SELECT COLUMN_NAME FROM(INFORMATION_SCHEMA.COLUMNS)
				//WHERE TABLE_NAME = 'AC' and COLUMN_NAME = 'TXT_ImportID')
				//ALTER TABLE [AC] 
				//ADD [TXT_ImportID] varchar(50) NULL
				sSQL = "IF NOT EXISTS (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS" + "\r\n" + "WHERE TABLE_NAME = '" + par_sFileName + "' and COLUMN_NAME = '" + sImportIDField + "')" + "\r\n" + "ALTER TABLE [" + par_sFileName + "]" + "\r\n" + "ADD [" + sImportIDField + "] " + "varchar(50)" + " NULL ";

				goLog.Log(sProc, "Running RunSQLQuery: '" + sSQL + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);
				if (!goData.RunSQLQuery(sSQL))
				{
					bResult = false;
				}

				//-----------------------
				//Create an index for import ID/Name field
				//Example:
				//IF EXISTS (SELECT name FROM sysindexes 
				//WHERE name = 'IX_AC_TXT_ImportID')
				//DROP INDEX AC.[IX_AC_TXT_ImportID]
				//CREATE INDEX [IX_AC_TXT_ImportID] on AC ([TXT_ImportID])
				sSQL = "IF EXISTS (SELECT name FROM sysindexes " + "\r\n" + "WHERE name = '" + "IX_" + par_sFileName + "_" + sImportIDField + "')" + "\r\n" + "DROP INDEX [" + par_sFileName + "].[IX_" + par_sFileName + "_" + sImportIDField + "]" + "\r\n" + "CREATE INDEX [IX_" + par_sFileName + "_" + sImportIDField + "] on [" + par_sFileName + "] ([" + sImportIDField + "]) ";

				goLog.Log(sProc, "Running RunSQLQuery: '" + sSQL + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);
				if (!goData.RunSQLQuery(sSQL))
				{
					bResult = false;
				}
			//------------------------

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If
			//End Try

			return bResult;

		}


		private bool SchemaRemoveFieldsFromTable(string par_sFileName, string par_sPurpose)
		{
			//MI 2/1/07 Removed removing TXT_ImportID
			//MI 11/1/06.

			//AUTHOR: MI 11/1/06
			//PURPOSE:
			//       Remove redundant varchar or nvarchar fields named TXT_<FieldName> from a single
			//       data table. This is called from SchemaRemoveFields.
			//       EXCEPTIONS:
			//       - CN.MLS_Review is removed together with CN.TXT_MLS_Review. This field
			//           is auto-created in SchemaAddFieldsToTable to allow mapping a combo CN.Review from Cmc.
			//PARAMETERS:
			//       par_sFileName: Name of the file in which to remove redundant fields.
			//       par_sPurpose: IMPORT - remove redundant TXT fields needed
			//           for import of existing customers' data.
			//RETURNS:
			//       True when successful, False when file is unsupported. SQL server errors
			//       are not checked.

			string sProc = "clSchema::SchemaRemoveFieldsFromTable";
			goLog.Log(sProc, "Start par_sFileName: '" + par_sFileName + "' par_sPurpose: '" + par_sPurpose + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			bool bResult = true;

			//Try

			clArray aFields = new clArray();
				int j = 0;
				string sField = null;
				string sSQL = "";
				string sImportIDField = "TXT_ImportID";

				if (!goData.IsFileValid(par_sFileName))
				{
					goErr.SetError(10100, sProc, "", par_sFileName);
					//E10100: Invalid file name '[1]'. [2]
					return false;
				}
				aFields = goData.GetFields(par_sFileName);
				for (j = 1; j <= aFields.GetDimension(); j++)
				{
					sField = aFields.GetInfo(j);
					//Redundant fields have two prefixes. ex: TXT_DTT_StartTime
					if (goTR.FromTo(sField, 8, 8) == "_" && goTR.FromTo(sField, 4, 4) == "_")
					{
						//Redundant field found - remove it
						//Example:
						//IF EXISTS (SELECT COLUMN_NAME FROM(INFORMATION_SCHEMA.COLUMNS)
						//WHERE TABLE_NAME = 'AC' and COLUMN_NAME = 'TXT_ImportID')
						//ALTER TABLE [AC] DROP COLUMN [TXT_ImportID]
						//sSQL &= "ALTER TABLE [" & par_sFileName & "] DROP COLUMN [" & sField & "]" & vbCrLf
						sSQL += "IF EXISTS (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS" + "\r\n" + "WHERE TABLE_NAME = '" + par_sFileName + "' and COLUMN_NAME = '" + sField + "')" + "\r\n" + "ALTER TABLE [" + par_sFileName + "] DROP COLUMN [" + sField + "]" + "\r\n";
						//goData.RunSQLQuery(sSQL)
					}
				}

				//Remove MLS_Review field from CN file. This field is added in SchemaAddFieldsToTable
				//to allow importing data from a combo field CN.Review from Commence.
				sSQL += "IF EXISTS (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS" + "\r\n" + "WHERE TABLE_NAME = '" + "CN" + "' and COLUMN_NAME = '" + "MLS_REVIEW" + "')" + "\r\n" + "ALTER TABLE [" + "CN" + "] DROP COLUMN [" + "MLS_REVIEW" + "]" + "\r\n";
				//Remove MLS field definition for MLS_Review from MD
				goMeta.PageDelete("GLOBAL", "LST_CN:REVIEW");


				goLog.Log(sProc, "Running RunSQLQuery: '" + sSQL + "'", (short)clC.SELL_LOGLEVEL_DETAILS, true);
				if (!goData.RunSQLQuery(sSQL))
				{
					bResult = false;
				}

			//-----------------------------
			//Don't remove TXT_ImportID per PJ 2/1/07

			//'First remove the index on TXT_ImportID
			//'Example:
			//'IF EXISTS (SELECT name FROM sysindexes 
			//'WHERE name = 'IX_AC_TXT_ImportID')
			//'DROP INDEX AC.[IX_AC_TXT_ImportID]
			//sSQL = "IF EXISTS (SELECT name FROM sysindexes " & vbCrLf & _
			//"WHERE name = 'IX_" & par_sFileName & "_" & sImportIDField & "')" & vbCrLf & _
			//"DROP INDEX " & par_sFileName & ".[IX_" & par_sFileName & "_" & sImportIDField & "]"
			//If Not goData.RunSQLQuery(sSQL) Then bResult = False

			//'Remove TXT_ImportID field
			//'Example:
			//'IF EXISTS (SELECT COLUMN_NAME FROM(INFORMATION_SCHEMA.COLUMNS)
			//'WHERE TABLE_NAME = 'AA' and COLUMN_NAME = 'TXT_ImportID')
			//'ALTER TABLE [AA] DROP COLUMN [TXT_ImportID]
			//sSQL = "IF EXISTS (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS" & vbCrLf & _
			//"WHERE TABLE_NAME = '" & par_sFileName & "' and COLUMN_NAME = '" & sImportIDField & "')" & vbCrLf & _
			//"ALTER TABLE [" & par_sFileName & "] DROP COLUMN [" & sImportIDField & "]"
			//goLog.Log(sProc, "Running RunSQLQuery: '" & sSQL & "'", clC.SELL_LOGLEVEL_DETAILS, True)
			//If Not goData.RunSQLQuery(sSQL) Then bResult = False

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If
			//End Try

			return bResult;

		}

		~clSchema()
		{
// INSTANT C# NOTE: The base class Finalize method is automatically called from the destructor:
			//base.Finalize();
		}

		public clSchema()
		{
			Initialize();
		}
	}

}
