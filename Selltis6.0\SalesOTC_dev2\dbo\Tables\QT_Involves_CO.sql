﻿CREATE TABLE [dbo].[QT_Involves_CO] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Quote_Involves_Company_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_QT] UNIQUEIDENTIFIER NOT NULL,
    [GID_CO] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_QT_Involves_CO] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CO_InvolvedIn_QT] FOREIGN KEY ([GID_QT]) REFERENCES [dbo].[QT] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_Involves_CO] FOREIGN KEY ([GID_CO]) REFERENCES [dbo].[CO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[QT_Involves_CO] NOCHECK CONSTRAINT [LNK_CO_InvolvedIn_QT];


GO
ALTER TABLE [dbo].[QT_Involves_CO] NOCHECK CONSTRAINT [LNK_QT_Involves_CO];


GO
CREATE CLUSTERED INDEX [IX_CO_InvolvedIn_QT]
    ON [dbo].[QT_Involves_CO]([GID_QT] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_Involves_CO]
    ON [dbo].[QT_Involves_CO]([GID_CO] ASC);

