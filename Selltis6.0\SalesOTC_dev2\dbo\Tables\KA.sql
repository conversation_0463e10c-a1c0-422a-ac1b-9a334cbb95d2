﻿CREATE TABLE [dbo].[KA] (
    [GID_ID]              UNIQUEIDENTIFIER CONSTRAINT [DF_KA_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),(0))+CONVERT([binary](2),'KA',(0)))+CONVERT([binary](6),getutcdate(),(0)),(0))) ROWGUIDCOL NOT NULL,
    [BI__ID]              BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]            NVARCHAR (80)    NULL,
    [DTT_CreationTime]    DATETIME         CONSTRAINT [DF_KA_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]        TINYINT          NULL,
    [TXT_ModBy]           VARCHAR (4)      NULL,
    [DTT_ModTime]         DATETIME         CONSTRAINT [DF_KA_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_KPIReport01Name] NVARCHAR (50)    NULL,
    [MMO_ImportData]      NTEXT            NULL,
    [SI__ShareState]      TINYINT          CONSTRAINT [DF_KA_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]    UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]        VARCHAR (50)     NULL,
    [TXT_ExternalID]      NVARCHAR (80)    NULL,
    [TXT_ExternalSource]  VARCHAR (10)     NULL,
    [TXT_ImpJobID]        VARCHAR (20)     NULL,
    [TXT_KPIName]         VARCHAR (80)     NULL,
    [TXT_User]            VARCHAR (80)     NULL,
    [TXT_Vendor]          VARCHAR (80)     NULL,
    [CUR_Jan]             MONEY            NULL,
    [CUR_Feb]             MONEY            NULL,
    [CUR_Mar]             MONEY            NULL,
    [CUR_Apr]             MONEY            NULL,
    [CUR_May]             MONEY            NULL,
    [CUR_Jun]             MONEY            NULL,
    CONSTRAINT [PK_KA] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_KA_CreatedBy_US]
    ON [dbo].[KA]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_KA_CreationTime]
    ON [dbo].[KA]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_KA_KPIReport01Name]
    ON [dbo].[KA]([TXT_KPIReport01Name] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_KA_ModDateTime]
    ON [dbo].[KA]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_KA_Name]
    ON [dbo].[KA]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_KA_TXT_ImportID]
    ON [dbo].[KA]([TXT_ImportID] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_KA_BI__ID]
    ON [dbo].[KA]([BI__ID] ASC);

