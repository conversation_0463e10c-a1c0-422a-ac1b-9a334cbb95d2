﻿CREATE TABLE [dbo].[QO] (
    [GID_ID]                 UNIQUEIDENTIFIER CONSTRAINT [DF_QO_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),(0))+CONVERT([binary](2),'QO',(0)))+CONVERT([binary](6),getutcdate(),(0)),(0))) NOT NULL,
    [BI__ID]                 BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]               NVARCHAR (80)    NULL,
    [DTT_CreationTime]       DATETIME         CONSTRAINT [DF_QO_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]           TINYINT          NULL,
    [TXT_ModBy]              VARCHAR (4)      NULL,
    [DTT_ModTime]            DATETIME         CONSTRAINT [DF_QO_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_QuoteOrderName]     NVARCHAR (50)    NULL,
    [MMO_ImportData]         NTEXT            NULL,
    [SI__ShareState]         TINYINT          CONSTRAINT [DF_QO_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]       UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]           VARCHAR (50)     NULL,
    [TXT_ExternalID]         NVARCHAR (80)    NULL,
    [TXT_ExternalSource]     VARCHAR (10)     NULL,
    [TXT_ImpJobID]           VARCHAR (20)     NULL,
    [GID_FOR_CO]             UNIQUEIDENTIFIER NULL,
    [TXT_Description]        NVARCHAR (1000)  NULL,
    [TXT_AddressMailing]     NVARCHAR (300)   NULL,
    [EML_Email]              TEXT             NULL,
    [CUR_TOTALAMOUNTUSD]     MONEY            NULL,
    [SI__GROSSMARGIN]        TINYINT          NULL,
    [CUR_GROSSPROFIT]        MONEY            NULL,
    [TXT_ORDERNUMBER]        NVARCHAR (80)    NULL,
    [TXT_PONo]               NVARCHAR (50)    NULL,
    [MLS_Status]             SMALLINT         NULL,
    [MLS_ReasonWonLost]      SMALLINT         NULL,
    [GID_CREDITEDTO_US]      UNIQUEIDENTIFIER NULL,
    [GID_PEER_US]            UNIQUEIDENTIFIER NULL,
    [SI__WEEKS]              TINYINT          NULL,
    [TXT_ProposedShipping]   NVARCHAR (50)    NULL,
    [GID_RELATED_TR]         UNIQUEIDENTIFIER NULL,
    [TXT_FOB]                NVARCHAR (20)    NULL,
    [GID_RELATED_DM]         UNIQUEIDENTIFIER NULL,
    [TXT_ShippingTerms]      NVARCHAR (30)    NULL,
    [GID_RELATED_QT]         UNIQUEIDENTIFIER NULL,
    [DTT_Time]               DATETIME         NULL,
    [DTT_DateClosed]         DATETIME         NULL,
    [DTT_ExpCloseDate]       DATETIME         NULL,
    [DTT_ValidUntilDate]     DATETIME         NULL,
    [DTT_NextActionDate]     DATETIME         NULL,
    [GID_ORIGINATEDBY_CN]    UNIQUEIDENTIFIER NULL,
    [CUR_TOTALAMOUNT]        MONEY            NULL,
    [TXT_SXORDERNO]          NVARCHAR (250)   NULL,
    [TXT_SELLQTNO]           NVARCHAR (250)   NULL,
    [MLS_SXOrderType]        SMALLINT         NULL,
    [GID_RELATED_BC]         UNIQUEIDENTIFIER NULL,
    [DTT_SXEnteredDateTime]  DATETIME         NULL,
    [DTT_OrigCreationTime]   DATETIME         NULL,
    [MLS_SXSTAGE]            SMALLINT         NULL,
    [INT_SXSTAGE]            SMALLINT         NULL,
    [TXT_SXPACKAGEID]        NVARCHAR (250)   NULL,
    [DTT_SXPICKEDDATE]       DATETIME         NULL,
    [DTT_SXPAIDDATE]         DATETIME         NULL,
    [DTT_SXSHIPPEDDATE]      DATETIME         NULL,
    [DTT_SXCANCELLEDDATE]    DATETIME         NULL,
    [DTT_SXINVOICEDDATE]     DATETIME         NULL,
    [CUR_TOTALINVOICEAMOUNT] MONEY            NULL,
    [MMO_JOURNAL]            NTEXT            NULL,
    [CHK_MASTERORDER]        TINYINT          NULL,
    [ADR_ATTACHMENTS]        NTEXT            NULL,
    [TXT_APPROVTYP]          NVARCHAR (250)   NULL,
    [CHK_CREDITAPP]          AS               (case when [TXT_APPROVTYP]='Y' then (1) else (0) end),
    [TXT_ORIGINCOPYTYPE]     NVARCHAR (250)   NULL,
    [TXT_ORIGINORDERNO]      NVARCHAR (250)   NULL,
    [TXT_LOSTREASONCODE]     NVARCHAR (250)   NULL,
    [TXT_CSDSTATUS]          NVARCHAR (250)   NULL,
    [GID_RELATED_BU]         UNIQUEIDENTIFIER NULL,
    [TXT_ShipToName]         NVARCHAR (1000)  NULL,
    [TXT_SalesWarehouse]     NVARCHAR (500)   NULL,
    CONSTRAINT [PK_QO] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_QO_CREDITEDTO_US]
    ON [dbo].[QO]([DTT_Time] ASC)
    INCLUDE([SYS_NAME], [GID_CREDITEDTO_US]);


GO
CREATE NONCLUSTERED INDEX [IX_QO_MASTERORDER]
    ON [dbo].[QO]([CHK_MASTERORDER] ASC, [DTT_SXEnteredDateTime] ASC)
    INCLUDE([MLS_SXOrderType], [GID_RELATED_BC], [MLS_SXSTAGE]);


GO
CREATE NONCLUSTERED INDEX [IX_QO_SXENTEREDDATETIME]
    ON [dbo].[QO]([CHK_MASTERORDER] ASC, [DTT_SXEnteredDateTime] ASC)
    INCLUDE([GID_FOR_CO], [TXT_SXORDERNO], [MLS_SXOrderType], [MLS_SXSTAGE]);


GO
CREATE NONCLUSTERED INDEX [IX_QO_ERLATED_BC]
    ON [dbo].[QO]([GID_RELATED_BC] ASC, [CHK_MASTERORDER] ASC, [DTT_SXEnteredDateTime] ASC)
    INCLUDE([MLS_SXOrderType], [MLS_SXSTAGE]);


GO
CREATE NONCLUSTERED INDEX [QO_DTime_MLS_SXSStage]
    ON [dbo].[QO]([DTT_Time] ASC, [MLS_SXSTAGE] ASC)
    INCLUDE([GID_FOR_CO], [SI__GROSSMARGIN], [GID_CREDITEDTO_US], [GID_PEER_US], [CUR_TOTALAMOUNT]);


GO



CREATE TRIGGER [dbo].[trQOUpdateTN]
ON [dbo].[QO]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in QO table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(120)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'QO'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable
					and tn2.GID_InternalID = inserted.GID_ID)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [QO]
			SET [QO].TXT_ExternalSource = '', [QO].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [QO].GID_ID = in1.GID_ID
				and ISNULL([QO].TXT_ExternalSource, '') <> ''
				and ISNULL([QO].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trQTUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trQTUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trQTUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!
