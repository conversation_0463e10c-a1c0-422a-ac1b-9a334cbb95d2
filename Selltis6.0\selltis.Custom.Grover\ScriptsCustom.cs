﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using System.Xml;
using System.Diagnostics;
using System.Net;

namespace Selltis.Custom
{
    public class ScriptsCustom
    {
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clProject goP;
        private clLog goLog;
        private clError goErr;
        private clPerm goPerm;
        private ClUI goUI;
        ScriptManager scriptManager = new ScriptManager();
        public string sError;

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        object par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";

        System.Data.SqlClient.SqlConnection SqlConnection = null;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();
            Util.SetSessionValue("SkipQLSpecificLogic", "Y");
        }

        public ScriptsCustom()
        {
            Initialize();
        }

        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Utility_RunImportUtility(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            try
            {
                goUI.OpenURLExternal("../Pages/cus_diaImportMan.aspx", "Selltis", "height=840,width=1250,left=100,top=100,status=yes,location=no,toolbar=no,resizable=yes,titlebar=no,dependent=yes");
            }


            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool AC_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // VS 01252016 TKT#918 : When Type is Sales Visit or Purpose is Prospecting make Related Product mandatory
            // SKO 07012016 TKT#1149 : customization removed
            // Dim iPurpose As Integer = 0
            // Dim iType As Integer = 0
            // iPurpose = doForm.doRS.GetFieldVal("MLS_PURPOSE", 2)
            // iType = doForm.doRS.GetFieldVal("MLS_TYPE", 2)
            // Dim sColor As String = goP.GetVar("sMandatoryFieldColor")
            // If iType = 11 Or iPurpose = 3 Then    'Sales Visit or Prospecting
            // doForm.SetFieldProperty("LNK_RELATED_PD", "LABELCOLOR", sColor)
            // Else
            // doForm.SetFieldProperty("LNK_RELATED_PD", "LABELCOLOR", System.Drawing.Color.Black.ToString())
            // End If

            return true;
        }
        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 1/4/08 Changed declaration to Dim bMailList As Boolean = True per CS.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // TLD 11/12/2012 Added var for Purpose
            int iPurpose = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Purpose", 2));
            int iStatus = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Status", 2));

            // TLD 12/3/2012 Remove mandatory when purpose is prospecting
            // TLD 11/12/2012 Added Purpose to PD enforcement
            // TLD 9/20/2012----- Enforce Product if Type is Sales Visit or purpose is Prospecting
            // If doForm.doRS.GetFieldVal("MLS_Type", 2) = 11 And doForm.doRS.IsLinkEmpty("LNK_Related_PD") = True Then
            // If doForm.doRS.GetFieldVal("MLS_Type", 2) = 11 Or iPurpose = 3 Then
            // VS 01252016 TKT#918 : Incluse if Purpose is Prospecting 
            // SKO 07012016 TKT#1149 : customization removed
            // If doForm.doRS.GetFieldVal("MLS_Type", 2) = 11 Or iPurpose = 3 Then
            // If doForm.doRS.IsLinkEmpty("LNK_Related_PD") = True Then
            // doForm.MoveToTab(2)     'Notes?
            // doForm.MoveToField("LNK_Related_PD")
            // goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("LNK_RELATED_PD"), "", "", "", "", "", "", "", "", "LNK_RELATED_PD")
            // Return False
            // End If
            // End If
            // TLD 9/20/2012----- Enforce Product if Type is Sales Visit

            // TLD 12/3/2012 Added to enforce if Status is Open and purpose is prospecting
            // TLD 11/12/2012 Changed MLS_Purpose to var
            // TLD 10/31/2012 Enforce Next Action Date when purpose is inquiry
            // If doForm.doRS.GetFieldVal("MLS_Purpose", 2) = 1 And doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1) = "" Then
            // If iPurpose = 1 And doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1) = "" Then
            if ((iPurpose == 1 | (iPurpose == 3 & Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Status", 2)) == 0)) & Convert.ToString(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1)) == "")
            {
                doForm.MoveToTab(3);     // Journal
                doForm.MoveToField("DTE_NEXTACTIONDATE");
                goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE");
                par_doCallingObject = doForm;
                return false;
            }
            // TLD 10/31/2012 -------Enforce Next Action Date when purpose is inquiry

            // SKO ******** TKT 1145:Need to script DTE_NEXTACTIONDATE to be mandatory when status is open.
            if ((iStatus == 0 & Convert.ToString(doForm.doRS.GetFieldVal("DTE_NEXTACTIONDATE", 1)) == ""))
            {
                doForm.MoveToTab(3);     // Journal
                doForm.MoveToField("DTE_NEXTACTIONDATE");
                goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("DTE_NEXTACTIONDATE"), "", "", "", "", "", "", "", "", "DTE_NEXTACTIONDATE");
                par_doCallingObject = doForm;
                return false;
            }

            // VS 08122016 TKT#1198 : Disable Mandatory product for purpose 1 = Inquiry/Price & Availablility
            goTR.StrWrite(ref par_sSections, "EnforceProductIfPurposeIsInquiry", "0");

            par_doCallingObject = doForm;
            return true;
        }
        //public bool AC_FormControlOnChange_MLS_PURPOSE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        //{
        //    // par_doCallingObject: Form object calling this script. Do not delete in script!
        //    // par_doArray: Unused.
        //    // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
        //    // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
        //    // par_s3 to par_s5: Unused.
        //    // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
        //    // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
        //    // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

        //    string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

        //    Form doForm = (Form)par_doCallingObject;

        //    // VS 01252016 TKT#913 : Source is mandatory only when Purpose is Lead or Inquiry.
        //    // SKO 07012016 TKT#1149 : customization removed
        //    // Dim iPurpose As Integer = 0
        //    // Dim iType As Integer = 0
        //    // iPurpose = doForm.doRS.GetFieldVal("MLS_PURPOSE", 2)
        //    // iType = doForm.doRS.GetFieldVal("MLS_TYPE", 2)
        //    // Dim sColor As String = goP.GetVar("sMandatoryFieldColor")
        //    // If iType = 11 Or iPurpose = 3 Then    'Sales Visit or Prospecting
        //    // doForm.SetFieldProperty("LNK_RELATED_PD", "LABELCOLOR", sColor)
        //    // Else
        //    // doForm.SetFieldProperty("LNK_RELATED_PD", "LABELCOLOR", System.Drawing.Color.Black.ToString())
        //    // End If

        //    par_doCallingObject = doForm;
        //    return true;
        //}
        //public bool AC_FormControlOnChange_MLS_TYPE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        //{
        //    // par_doCallingObject: Form object calling this script. Do not delete in script!
        //    // par_doArray: Unused.
        //    // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
        //    // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
        //    // par_s3 to par_s5: Unused.
        //    // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
        //    // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
        //    // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

        //    string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
        //    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

        //    Form doForm = (Form)par_doCallingObject;

        //    // VS 01252016 TKT#913 : Source is mandatory only when Purpose is Lead or Inquiry.
        //    // SKO 07012016 TKT#1149 : customization removed
        //    // Dim iPurpose As Integer = 0
        //    // Dim iType As Integer = 0
        //    // iPurpose = doForm.doRS.GetFieldVal("MLS_PURPOSE", 2)
        //    // iType = doForm.doRS.GetFieldVal("MLS_TYPE", 2)
        //    // Dim sColor As String = goP.GetVar("sMandatoryFieldColor")
        //    // If iType = 11 Or iPurpose = 3 Then    'Sales Visit or Prospecting
        //    // doForm.SetFieldProperty("LNK_RELATED_PD", "LABELCOLOR", sColor)
        //    // Else
        //    // doForm.SetFieldProperty("LNK_RELATED_PD", "LABELCOLOR", System.Drawing.Color.Black.ToString())
        //    // End If

        //    par_doCallingObject = doForm;
        //    return true;
        //}
        public bool AC_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/27/07 Sectionalized the script.
            // par_doCallingObject: Rowset object containing the record to be saved.
            // par_doArray: Unused.
            // par_sMode: 'CREATION' or 'MODIF'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // goP.TraceLine("", "", sProc)

            clRowSet doRS = (clRowSet)par_doCallingObject; // object

            // TLD 11/11/2013 Set Purpose accordingly if logged from outlook
            if (doRS.iRSType == 2)
            {
                if (Convert.ToString(goP.GetVar("PROGID")) == "SELLOLL")
                {
                    switch (Convert.ToInt32(doRS.GetFieldVal("MLS_Type", 2)))
                    {
                        case 5: // E-mail Received
                            {
                                doRS.SetFieldVal("MLS_Purpose", 31, 2); // E-mail Received
                                break;
                            }

                        case 6: // E-mail Sent
                            {
                                doRS.SetFieldVal("MLS_Purpose", 32, 2); // E-mail Sent
                                break;
                            }
                    }
                }
            }

            // VS 02022016 TKT918 : Check CHK_HasAttachments if Record has any attachments
            if (Convert.ToString(doRS.GetFieldVal("ADR_ATTACHMENTS")) == "")
                doRS.SetFieldVal("CHK_HASATTACHMENTS", 0, 2);
            else
                doRS.SetFieldVal("CHK_HASATTACHMENTS", 1, 2);

            par_doCallingObject = doRS;
            return true;
        }
        public bool Activity_CreateActLog_pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // goP.TraceLine("In " & sProc, "", sProc)

            // PURPOSE:
            // PJ 5/30/02 Adds Act Log with new notes of Lead Activity.
            // Run from enforce.
            // RETURNS:
            // 1 if the Activity is created or didn't need to be created; 0 if not created or the user canceled.

            // 2004/12/08 10:03:25 MAR Considering changes to Journal instead of Notes.

            Form doForm = (Form)par_doCallingObject;

            // SKO 07012016 Ticket#1149
            par_bRunNext = false;

            string sNotes;
            string sWork;
            int lWork;

            // If answered Yes on MB to create a journal, then go directly to script that creates journal.
            // If UCase(par_s1) = "YES" Then
            // GoTo CREATEACTLOG
            // End If


            string sMessage;

            if (Convert.ToString(doForm.doRS.GetFieldVal("MMO_JOURNAL")).Length <= Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))
            {
                par_doCallingObject = doForm;
                return true;
            }
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Activity is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);
                doForm.oVar.SetVar("Ac_CreateActLog_Ran", "1");
                // Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                par_doCallingObject = doForm;
                return true;
            }
            // If doForm.GetMode() = "CREATION" Then Return True		'The record must be in edit mode so we have its ID for links to it.
            doForm.oVar.SetVar("Ac_CreateActLog_Ran", "1");

            // sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            // CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            // hard returns in the journal field
            sWork = Convert.ToString(doForm.oVar.GetVar("JournalWithHardReturns"));
            // CS 2/4/10
            if (sWork == "")
            {
                par_doCallingObject = doForm;
                return true; // We didn't hit MessageBoxEvent from entering a journal note.
            }

            clArray doLink = new clArray();
            // SKO 07012016 Ticket#1149
            clRowSet doNew = new clRowSet("AC", 2, "", "", "", -1, "", "", "", "", "", true);

            // ==> Use goData:CopyAllLinks() method instead of copying links one by one?

            // 'CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            // 'hard returns in the journal field
            // sWork = doForm.oVar.GetVar("JournalWithHardReturns")
            // 'sWork = doForm.doRS.GetFieldVal("MMO_JOURNAL")

            // goP.TraceLine("Length of sWork: '" & Len(sWork) & "'", "", sProc)
            // goP.TraceLine("Val of GetVar of lLenJournal: '" & Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")) & "'", "", sProc)
            // goP.TraceLine("Length - Val = '" & (Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"))) & "'", "", sProc)
            // goP.TraceLine("lWork (Left of sWork for Length - Val): '" & Left(sWork, (Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))) & "'", "", sProc)
            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, lWork);
            // goP.TraceLine("sNotes (Left(sWork, lWork)): '" & sNotes & "'", "", sProc)
            sNotes = sNotes + "== Created from Activity '" + doForm.doRS.GetFieldVal("SYS_Name") + "'";
            // goP.TraceLine("Full sNotes: '" & sNotes & "'", "", sProc)
            // goP.TraceLine("Setting field MMO_NOTES '" & sNotes & "'", "", sProc)
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            // CS goP.TraceLine("Setting field LNK_Involves_US '" & goP.GetMe("ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            // goP.TraceLine("Setting field MLS_Status '" & 1 & "'", "", sProc)
            doNew.SetFieldVal("MLS_Status", 1, 2);     // Completed
                                                       // goP.TraceLine("Setting field TME_STARTTIME '" & "Now" & "'", "", sProc)
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            // goP.TraceLine("Setting field TME_ENDTIME '" & "Now" & "'", "", sProc)
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            // goP.TraceLine("Setting field DTE_STARTTIME '" & "Today" & "'", "", sProc)
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            // goP.TraceLine("Setting field DTE_ENDTIME '" & "Today" & "'", "", sProc)
            doNew.SetFieldVal("DTE_ENDTIME", "Today");
            // goP.TraceLine("Setting field LNK_CreditedTo_US '" & goP.GetMe("ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_Related_CN", ref doLink, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_CN", doLink);
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_Related_PD", ref doLink, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_PD", doLink);
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_Related_GR", ref doLink, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_GR", doLink);
            // goP.TraceLine("Setting field EML_EMAIL '" & doForm.doRS.GetFieldVal("EML_EMAIL") & "'", "", sProc)
            doNew.SetFieldVal("EML_EMAIL", doForm.doRS.GetFieldVal("EML_EMAIL"));
            // goP.TraceLine("Setting field TEL_FAX '" & doForm.doRS.GetFieldVal("TEL_FAX") & "'", "", sProc)
            doNew.SetFieldVal("TEL_FAX", doForm.doRS.GetFieldVal("TEL_FAX"));
            oTable = null;
            doLink = doForm.doRS.GetLinkVal("LNK_Related_CO", ref doLink, true, 0, -1, "A_a", ref oTable);
            doNew.SetLinkVal("LNK_Related_CO", doLink);
            // goP.TraceLine("Setting field MLS_TYPE '" & 31 & "'", "", sProc)
            doNew.SetFieldVal("MLS_TYPE", 31, 2);      // Journal
                                                       // goP.TraceLine("Setting field MMO_HISTORY '" & goTR.WriteLogLine(doForm.GetFieldVal("MMO_HISTORY"), "Created.") & "'", "", sProc)
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine("", "Created."));

            // goP.TraceLine("Setting field LNK_Connected_AC '" & doForm.GetFieldVal("GID_ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_Related_AC", doForm.doRS.GetCurrentRecID());
            // goP.TraceLine("About to commit.", "", sProc)

            if (doNew.Commit() != 1)
            {
                // delete(doNew)
                // delete(doLink)
                doNew = null;
                doLink = null;

                string sError = goErr.GetLastError();
                // Dim sMessage As String
                sMessage = goErr.GetLastError("MESSAGE");
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);

                goLog.Log("Activity_CreateActLog", Convert.ToString(doForm.oVar.GetVar("ScriptMessages")), 1, false, true);

                par_doCallingObject = doForm;
                return false;
            }


            doNew = null;
            doLink = null;

            par_doCallingObject = doForm;
            return true;
        }
        public bool Activity_ManageControlState_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // PURPOSE:
            // Manage the state, position, and size of form controls for different 
            // Types and modes. Enablement of controls is hard-coded in each form
            // in ManageControlState procedure. This is currently used for 
            // positioning and sizing of controls.

            // CS: Added mandatory field color here

            try
            {
                Form doForm = (Form)par_doCallingObject;

                // TLD 12/3/2012 Open to Journal tab when Purpose is Prospecting
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Purpose", 2)) == 3)
                    doForm.MoveToTab(3);// Journal

                par_doCallingObject = doForm;
                return true;
            }
            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
                return false;
            }
        }
        public bool AutoAlertEveryDay_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter in all rowsets to get all users' private recs
            // MI 10/19/07 started converting the script to be aware of the start of the day depending on the user's time zone
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DETAILS, true);

            Form doForm = (Form)par_doCallingObject;
            // *** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
            // It is unlikely that there would be non-shared User records, but just in case...
            var rsUsers = new clRowSet("US", clC.SELL_READONLY, "CHK_ACTIVEFIELD=1", "SYS_Name", "GID_ID, SYS_NAME", -1, "", "", "", "", "", false, false, true);

            // TLD 11/16/2012 Prevent Overdue Quotes Alert from running
            goTR.StrWrite(ref par_sSections, "AlertOverdueQuotes", "0");

            // No active users
            if (rsUsers.Count() == 0)
            {
                par_doCallingObject = doForm;
                return true;
            }

            // PURPOSE:
            // Every day display alerts about things that require review or followup,
            // update Appt dates (reschedule)/To Do due dates.
            // The alerts are typically set up to open an appropriate desktop.
            // This replaces 'alarm' agents in Selltis 1.0. The name of the agent 
            // is on the top of each section of code.
            // MI 10/19/07: Making the script aware of ea user's time zone so that
            // the alerts, auto-updates, rescheduling, etc. occurs after midnight
            // user's time and not server's time. See additional notes in the code.
            // HOW THIS WORKS:
            // This script runs hourly by an automator AGE_2005072616363748192MAR 00002XX
            // that has the following definition:
            // A_01_EXECUTE = AutoAlertEveryDay
            // A_01_OBJSHARED = 1
            // A_01_TYPE = RUNSCRIPT
            // A_ORDER=1,
            // ACTIONS=1,
            // ACTIVE = 1
            // E_TM_HOUR = 0
            // E_TM_INTERVAL = 2
            // E_TM_MINUTE = 1
            // E_TM_MISSED = 1
            // EVENT=TIMER
            // US_NAME=AutoAlertEveryDay
            // US_PURPOSE=Add alerts every morning for followup of leads, contacts, to dos, etc.
            // SORTVALUE1=TIMER_ACTIVE
            // Make sure the timer automator engine is running on this site's web server.
            // WARNING:
            // Tread carefully - this is a very sensitive area.

            clRowSet doRS;
            string sResult;
            // Dim sStartDate As String
            // Dim sDueDate As String
            bool bMore = true;
            string sCurrentUser = "";
            // Dim dtDate As Date
            // Dim lDays As Long
            string sPointers;
            string sPointerDateTime;
            DateTime dtPointerDate;
            PublicDomain.TzTimeZone zone;
            DateTime dtUsersNow;       // User time zone 'now'
            DateTime dtUsersToday;     // User time zone Today (now.Date())
            string sDateTime;
            DateTime dtDateTime;
            // Dim sStartTime As String
            // Dim sEndTime As String

            // Get a page with date stamps of last dates when daily timer script was processed for ea user. Ex:
            // USER1GID_ID=2008-03-25
            // USER2GID_ID=2008-03-22
            // ...
            // where USERnGID_ID is a Selltis global ID (SUID).
            sPointers = goMeta.PageRead("GLOBAL", "OTH_DAILY_SCRIPT_PROCESSED");


            // --------------------- Non-user-dependent updates -----------------------

            // --------- Set 'today UTC' -------------
            // sDateTime replaces the Selltis keyword 'Today' with the datetime value that corresponds to midnight
            // in the currently processed user's time zone
            dtDateTime = goTR.NowUTC().Date;
            par_iValid = 3;
            string par_sDelim = "|";
            sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_sDelim);

            // ---------------------- Per-user processing loop ------------------
            while (bMore == true)
            {

                // The timer automator fires this script every hour.
                // Here we make sure the actual update is made only once in a day for each user.
                // This is very important because we are modifying data (changing Appointment 
                // date field values, for example) for the whole day. This should not be done 
                // piece meal or it will be confusing to users.

                // A 'day' is seen from the perspective of the currently processed user's
                // current time zone. If the user switches to a different time zone within 
                // the same day, the processing should not occur again until the next day 
                // starts in that time zone. As a consequence, for users traveling west 
                // this script may not fire for up to an extra day. Users traveling east 
                // will have the processing occur in less than 24 hours from the last time it ran.

                // Read user's 'last processed' date (recorded in user's time zone at the time)
                sCurrentUser = Convert.ToString(rsUsers.GetFieldVal("GID_ID", clC.SELL_FRIENDLY));

                // DEBUG 
                // goLog.Log(sProc, "  Processing user '" & goData.GetRecordNameByID(sCurrentUser) & "' [" & sCurrentUser & "]", clC.SELL_LOGLEVEL_DETAILS)
                // END DEBUG

                // Get user's time zone
                zone = goTR.UTC_GetUserTimeZone(sCurrentUser);
                // Get user's 'now' and 'today at midnight' in the user's current time zone
                dtUsersNow = zone.ToLocalTime(goTR.NowUTC());   // User's current datetime in his/her time zone
                dtUsersToday = dtUsersNow.Date;                  // Midnight on user's current date in his/her time zone
                                                                 // Get the 'last processed' date (no time) as a local datetime
                                                                 // Pointers are written as local datetimes in user's last login time zone.
                sPointerDateTime = Strings.Left(goTR.StrRead(sPointers, sCurrentUser, "", false), 10);
                par_iValid = 3;
                if (sPointerDateTime == "")
                    // Leaving one whole day ahead of 'blank' datetime: '1753-01-02 23:59:59.000'.
                    dtPointerDate = goTR.StringToDate("1753-01-04", "", ref par_iValid);
                else
                    dtPointerDate = goTR.StringToDate(sPointerDateTime, clC.SELL_FORMAT_DATEDEF, ref par_iValid).Date;
                dtPointerDate = DateTime.SpecifyKind(dtPointerDate, DateTimeKind.Local);
                dtPointerDate = goTR.AddDay(dtPointerDate, 1);

                // *** MI 10/23/07 Replaced >= with just = to avoid not processing for days or months in case 
                // the server clock was accidentally set ahead and the processing occurred... In that case, 
                // pointer datetimes set far in the future would preclude running this.
                // If dtPointerDate >= dtUsersToday Then GoTo ProcessNextUser

                // DEBUG
                // goLog.Log(sProc, "    Pointer date: '" & goTR.DateTimeToSysString(dtPointerDate) & "' User's date: '" & goTR.DateTimeToSysString(dtUsersToday) & "'", clC.SELL_LOGLEVEL_DETAILS)
                // END DEBUG

                if (dtPointerDate == dtUsersToday)
                    goto ProcessNextUser;

                // --------- Set 'today' -------------
                // sDateTime replaces the Selltis keyword 'Today' with the datetime value that corresponds to midnight
                // in the currently processed user's time zone
                dtDateTime = goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday));
                par_iValid = 3;
                par_sDelim = "|";
                sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_sDelim);

                // ---------- Set 'tomorrow' -----------
                // Set user's 'tomorrow at midnight' as local datetime for filtering
                dtDateTime = goTR.UTC_UTCToLocal(zone.ToUniversalTime(dtUsersToday));
                dtDateTime = goTR.AddDay(dtDateTime, 1);
                sDateTime = goTR.DateTimeToSysString(dtDateTime, ref par_iValid, ref par_sDelim);

                // TLD 11/16/2012 Customize to go to Created By User
                // ---------- Quotes to Review ----------
                // If goScr.IsSectionEnabled(sProc, par_sSections, "AlertOverdueQuotes", True) Then
                // Quote:DailyAlert
                // sDateTime: tomorrow
                // *** MI 4/10/08 added True for par_bGetAllUsersUnsharedRecs parameter to get all users' private recs
                // doRS = New clRowSet("QT", 3, "CHK_OPEN=1 AND DTT_NEXTACTIONDATE<'" & sDateTime & "' AND LNK_Peer_US='" & sCurrentUser & "'", _
                // , "GID_ID, SYS_NAME", 1, , , , , , , , True)
                doRS = new clRowSet("QT", 3, "CHK_OPEN=1 AND DTT_NEXTACTIONDATE<'" + sDateTime + "' AND LNK_CreatedBy_US='" + sCurrentUser + "'", "", "GID_ID, SYS_NAME", 1, "", "", "", "", "", false, false, true);
                // goLog.Log("autoalerteveryday:quotes", doRS.Count, , , True)
                if (doRS.GetFirst() == 1)
                {
                }
                doRS = null/* TODO Change to default(_) if this is not a reference type */;
                // End If
                // ---------- End Quotes to Review ----------

                par_iValid = 3;
                par_sDelim = "|";
                // Update the daily processing pointer with current datetime in the processed user's time zone
                goMeta.LineWrite("GLOBAL", "OTH_DAILY_SCRIPT_PROCESSED", sCurrentUser, goTR.DateTimeToSysString(dtUsersNow, ref par_iValid, ref par_sDelim), ref SqlConnection);

                ProcessNextUser:
                ;
                if (rsUsers.GetNext() == 0)
                    bMore = false;
            }

            rsUsers = null/* TODO Change to default(_) if this is not a reference type */;

            // DEBUG: 
            // goLog.Log(sProc, "End: Return True", clC.SELL_LOGLEVEL_DETAILS, True)
            // END DEBUG

            par_doCallingObject = doForm;
            return true;
        }
        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/19/2012 Disable CHK_Merged
            doForm.SetControlState("CHK_Merged", 4);

            par_doCallingObject = doForm;
            return true;
        }
        public bool CN_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sJobfunction;

            // TLD 9/19/2012 Merge Functionality - run at end of CN_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CN") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CN_Merge")) != "1")
                    {
                        // Don't allow merge of contact to itself
                        if (Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")) == Convert.ToString(doForm.doRS.GetFieldVal("LNK_Mergedto_CN%%GID_ID")))
                            doForm.MessageBox("You cannot merge a contact to itself.  Please select a different merge to contact.", clC.SELL_MB_OK, "", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CN", "MergeFail");
                        else
                            doForm.MessageBox("This contact will be merged to the target contact, '" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_MergedTo_CN%%SYS_Name")) + "'. Blank fields on the target contact will be filled from this contact record and all links will be copied to the target contact. Are you sure you want to merge this contact?", clC.SELL_MB_YESNOCANCEL, "", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CN", "Merge");
                    }
                }
            }

            // SKO 09022016 TKT#1230 : I added chk_primarycontact. On save, if related job function is Primary Contact, check this box.
            sJobfunction = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_JF%%TXT_JOBFUNCNAME"));
            sJobfunction = sJobfunction + Constants.vbCrLf;
            if (Strings.InStr(sJobfunction, "Primary Contact" + Constants.vbCrLf) > 0)
                doForm.doRS.SetFieldVal("chk_primarycontact", 1, 2);

            par_doCallingObject = doForm;
            return true;
        }
        public bool CN_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/19/2012 Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CN_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                par_doCallingObject = doForm;
                return false;
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // CS ******** Set LNK_IN_TE field label red.
            string sColor = Convert.ToString(goP.GetVar("sMandatoryFieldColor"));
            // SKO ******** TKT 1145:
            // doForm.SetFieldProperty("LNK_IN_TE", "LABELCOLOR", sColor)

            // TLD 9/12/2012 Disable fields for Target Account
            doForm.SetControlState("TXT_CURRANDPOT", 4);

            // TLD 9/19/2012 Disable CHK_Merged
            doForm.SetControlState("CHK_Merged", 4);

            // TLD 11/16/2012 On new, set Team Leader to
            // Routing Record's Company Team Leader user
            if (doForm.GetMode() == "CREATION")
            {
                // Check for routing record
                clRowSet doRORS = new clRowSet("RO", 3, "TXT_RoutingName='Company Team Leader'", "", "LNK_To_US");
                if (doRORS.GetFirst() == 1)
                {
                    doForm.doRS.SetFieldVal("LNK_TeamLeader_US", doRORS.GetFieldVal("LNK_To_US", 2, 1, true, 1), 2);
                    doRORS = null/* TODO Change to default(_) if this is not a reference type */;
                }
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // CS ********: Enforce LNK_IN_TE from script instead of in REQUIREDFIELDS MD because the field is not currently supported in mobile.
            // SKO ******** TKT 1145:Make LNK_IN_TE not Mandatory.
            // If doForm.doRS.GetLinkCount("LNK_IN_TE") < 1 Then
            // doForm.MoveToTab(0)
            // doForm.MoveToField("LNK_IN_TE")
            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("CO", "LNK_IN_TE"), "", "", "", "", "", "", "", "", "LNK_IN_TE")
            // Return False
            // End If

            // TLD 9/19/2012 Merge Functionality - run at end of CO_FormOnSave_Post
            if (doForm.doRS.GetLinkCount("LNK_MergedTo_CO") > 0)
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Merged", 2)) == 0)
                {
                    if (Convert.ToString(doForm.oVar.GetVar("CO_Merge")) != "1")
                    {
                        // Don't allow merge of company to itself
                        if (Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")) == Convert.ToString(doForm.doRS.GetFieldVal("LNK_Mergedto_CO%%GID_ID")))
                            doForm.MessageBox("You cannot merge a company to itself.  Please select a different merge to company.", clC.SELL_MB_OK, "", "", "", "", "", "MessageBoxEvent", "", "", doForm, null, "OK", "", "", "CO", "MergeFail");
                        else
                            doForm.MessageBox("This company will be merged to the target company, '" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_MergedTo_CO%%SYS_Name")) + "'. Blank fields on the target company will be filled from this company record and all links will be copied to the target company. Are you sure you want to merge this company?", clC.SELL_MB_YESNOCANCEL, "", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "MessageBoxEvent", doForm, null, "YES", "NO", "CANCEL", "CO", "Merge");
                    }
                }
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/19/2012 Click cancel on merge
            if (Convert.ToString(doForm.oVar.GetVar("CancelSave")) == "1")
            {
                doForm.oVar.SetVar("CO_Merge", "");
                doForm.oVar.SetVar("CancelSave", "");
                par_doCallingObject = doForm;
                return false;
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool CO_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;;

            // TLD 6/19/2012 ----------Target account calcs
            string sCurVol = "";
            string sPotVol = "";
            int iTargetAcct = 0; // CHK_TargetAcct

            // SKO ******** TKT#420: Don't fill from current to potential
            // Copy LNK_CURRENT_PD to LNK_POTENTIAL_PD
            // doRS.SetFieldVal("LNK_POTENTIAL_PD", doRS.GetFieldVal("LNK_CURRENT_PD"))

            // Fill Current & Potential Quadrant`
            // VS ******** TKT#420 : Use Space instead of Z for <Make Selection>
            sCurVol = Strings.Left(Convert.ToString(doRS.GetFieldVal("MLS_CURVOLUME")), 1);
            sPotVol = Strings.Left(Convert.ToString(doRS.GetFieldVal("MLS_POTVOLUME")), 1);
            if (sCurVol == "<")
                // sCurVol = "Z"  'Use Space instead of Z
                sCurVol = " ";
            if (sPotVol == "<")
                // sPotVol = "Z"  'Use Space instead of Z
                sPotVol = " ";

            // set field to cur & pot
            doRS.SetFieldVal("TXT_CURRANDPOT", sCurVol + sPotVol);
            // TLD 6/19/2012 ----------End Target account calcs

            par_doCallingObject = doRS;
            return true;
        }
        public bool FIND_FormControlOnChange_BTN_QTSearch_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/2/2009 Prevent main from running
            par_bRunNext = false;

            // Find dialog; Quote tab Search button
            string sQuoteNo;
            string sQuoteRefNo;
            string sCompany;
            string sCreditedTo;
            // TLD 9/2/2009 Added Description & Part Number field
            string sDescription;
            string sPart;

            string sView;
            int iCondCount = 0;
            string sViewCondition;
            string sNewCondition;
            int iOrigCondCount;
            int i;
            string sFilter = "";

            // Get values from form
            sQuoteNo = doForm.GetControlVal("NDB_TXT_QUOTENO");
            // TLD 3/8/2013 Update from main
            if (!string.IsNullOrEmpty(sQuoteNo))
                sQuoteNo = goTR.ConvertStringForQS(goTR.PrepareForSQL(sQuoteNo.Trim()), "TXT_QUOTENO", "QT", true);
            sQuoteRefNo = doForm.GetControlVal("NDB_TXT_QUOTEREFNO");
            // TLD 3/8/2013 Update from main
            if (!string.IsNullOrEmpty(sQuoteRefNo))
                sQuoteRefNo = goTR.ConvertStringForQS(goTR.PrepareForSQL(sQuoteRefNo.Trim()), "TXT_QUOTEREFNO", "QT", true);
            sCompany = doForm.GetControlVal("NDB_TXT_COMPANY");
            // TLD 3/8/2013 Update from main
            if (!string.IsNullOrEmpty(sCompany))
                sCompany = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCompany), "LNK_TO_CO%%SYS_NAME", "QT", true);
            sCreditedTo = doForm.GetControlVal("NDB_TXT_CREDITEDTO");
            // TLD 3/8/2013 Update from main
            if (!string.IsNullOrEmpty(sCreditedTo))
                sCreditedTo = goTR.ConvertStringForQS(goTR.PrepareForSQL(sCreditedTo), "LNK_CREDITEDTO_US%%SYS_NAME", "QT", true);
            // TLD 9/2/2009 Added Description & Part Number field
            sDescription = doForm.GetControlVal("NDB_TXT_DESCRIPTION");
            // TLD 3/8/2013 Added like main
            if (!string.IsNullOrEmpty(sDescription))
                sDescription = goTR.ConvertStringForQS(goTR.PrepareForSQL(sDescription), "TXT_DESCRIPTION", "QT", true);
            sPart = doForm.GetControlVal("NDB_TXT_PARTNUMBER");
            // TLD 3/8/2013 Added like main
            if (!string.IsNullOrEmpty(sPart))
                sPart = goTR.ConvertStringForQS(goTR.PrepareForSQL(sPart), "TXT_PARTNUMBER", "MO", true);

            //Use values to filter Quote - Search Results desktop if it exists
            string Key = Guid.NewGuid().ToString();
            Desktop oDesktop = new Desktop("GLOBAL", "DSK_D8E2511C-031F-45DD-5858-9AF500ECEC44", false, Key);
            //Edit views in DT

            //View 1:Quote - Search Results
            sView = oDesktop.GetViewMetadata("VIE_4D836010-021D-4AD9-5858-9AF500ECEC44");
            
            //iCondCount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sView, "CCOUNT"), "", ref par_iValid));


            //// If CCOUNT=1 need to check if C1FIELDNAME=<%ALL%> b/c in that case I need to overwrite
            //if (iCondCount == 1)
            //{
            //    if (goTR.StrRead(sView, "C1FIELDNAME") == "<%ALL%>")
            //        iCondCount = 0;// Will overwrite these values
            //}
            //// Original condition count
            iOrigCondCount = iCondCount;

            // Only want to filter if the NDB fields contained a value
            // Get the total # of conditions
            if (!string.IsNullOrEmpty(sQuoteNo))
                iCondCount = iCondCount + 1;
            if (!string.IsNullOrEmpty(sQuoteRefNo))
                iCondCount = iCondCount + 1;
            if (!string.IsNullOrEmpty(sCompany))
                iCondCount = iCondCount + 1;
            if (!string.IsNullOrEmpty(sCreditedTo))
                iCondCount = iCondCount + 1;
            // TLD 9/2/2009 Added Description & Part Number Field
            if (!string.IsNullOrEmpty(sDescription))
                iCondCount = iCondCount + 1;
            if (!string.IsNullOrEmpty(sPart))
                iCondCount = iCondCount + 1;

            // Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            if (!string.IsNullOrEmpty(sQuoteNo))
            {
                // Add 'Quote No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_QUOTENO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sQuoteNo);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_QUOTENO[" + sQuoteNo + "";
                else
                    sFilter = "TXT_QUOTENO[" + sQuoteNo + "";
            }
            if (!string.IsNullOrEmpty(sQuoteRefNo))
            {
                // Add 'Quote Ref No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_QUOTEREFNO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sQuoteRefNo);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_QUOTEREFNO[" + sQuoteRefNo + "";
                else
                    sFilter = "TXT_QUOTEREFNO[" + sQuoteRefNo + "";
            }
            if (!string.IsNullOrEmpty(sCompany))
            {
                // Add 'To Company' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_TO_CO%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCompany);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND LNK_TO_CO%%SYS_NAME[" + sCompany + "";
                else
                    sFilter = "LNK_TO_CO%%SYS_NAME[" + sCompany + "";
            }
            if (!string.IsNullOrEmpty(sCreditedTo))
            {
                // Add 'Credited To User' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_CREDITEDTO_US%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCreditedTo);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND LNK_CREDITEDTO_US%%SYS_NAME[" + sCreditedTo + "";
                else
                    sFilter = "LNK_CREDITEDTO_US%%SYS_NAME[" + sCreditedTo + "";
            }
            // TLD 9/2/2009 Added Description field
            if (!string.IsNullOrEmpty(sDescription))
            {
                // Add 'Quote Ref No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_DESCRIPTION%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sDescription);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_DESCRIPTION[" + sDescription + "";
                else
                    sFilter = "TXT_DESCRIPTION[" + sDescription + "";
            }

            // TLD 9/2/2009 Added Part Number
            if (!string.IsNullOrEmpty(sPart))
            {
                // Add 'Quote Ref No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_CONNECTED_QL%%LNK_FOR_MO%%TXT_PARTNUMBER%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sPart);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND LNK_CONNECTED_QL%%LNK_FOR_MO%%TXT_PARTNUMBER[" + sPart + "";
                else
                    sFilter = "LNK_CONNECTED_QL%%LNK_FOR_MO%%TXT_PARTNUMBER[" + sPart + "";
            }

            // Edit CONDITION= line in view MD
            sViewCondition = goTR.StrRead(sView, "CONDITION");
            if (sViewCondition == "")
                sNewCondition = sFilter; // No filter in view already
            else
                sNewCondition = sViewCondition + " AND " + sFilter;
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            oDesktop.SetViewMetadata("VIE_4D836010-021D-4AD9-5858-9AF500ECEC44", sView);

            //Load current view dataset with find filtertext..J
            SessionViewInfo _sessionViewInfo = new SessionViewInfo();
            _sessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + "VIE_4D836010-021D-4AD9-5858-9AF500ECEC44");
            _sessionViewInfo.ViewMetaData = sView;
            _sessionViewInfo.ViewCondition = sNewCondition;
            Util.LoadViewData("VIE_4D836010-021D-4AD9-5858-9AF500ECEC44", _sessionViewInfo.SortText, 1, "", false, Key);
            //update all the child views data sets
            Util.LoadViewDataSets(_sessionViewInfo.TopViewCount, _sessionViewInfo.TabViewCount, "VIE_4D836010-021D-4AD9-5858-9AF500ECEC44", true, Key);

            oDesktop = new Desktop("GLOBAL", "DSK_D8E2511C-031F-45DD-5858-9AF500ECEC44", true, Key);

            sView = "";
            sViewCondition = "";
            sNewCondition = "";
            iCondCount = 0;

            // Que QT Search Results desktop
            goUI.Queue("DESKTOP", oDesktop);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));

            par_doCallingObject = doForm;
            return true;
        }
        public bool GenerateSysName_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            // MI 3/12/10 Added CASE "DF", modified Case Else to generate Name based on TXT_<FileName>Name field if exists.
            // CS 6/2/09 Modified QL case
            // CS 8/25/08 Case US: Added Title text
            // CS 8/20/08 Case CN: added Title Text
            // MI 11/14/07 Case CN: added TXT_ContactCode.
            // MI 10/15/07 Appended ' UTC' to all datetimes
            // MI 6/22/07 Changes to QT, OP, PR, PD, MO
            // MI 4/17/07 Added Phone Co to CN; Phone to CO ; removed padding from and to to 4 in MS; Status to OP, PR, QT, TD(?)
            // MI 3/9/07 Removed ellipsis from Co name in AC name.
            // MI 3/1/07 Added Contact, Company to AC Name.
            // MI 2/1/07 Added Date, Originator to Project name
            // MI 9/15/06 Updated QL, WT SYS_Name formats.
            // MI 7/25/06 Added raising error when field is not in the rowset.
            // MI 7/24/06 Mods. 
            // MI 7/20/06 Created in clScripts.
            // MI 7/20/06 Finished, tested, moved from clData to clScripts and renamed from GetCurrentRecordName to GetSysName.
            // MI 7/17/06 Started making this work in SellSQL.

            // AUTHOR: MI
            // PURPOSE:
            // Send back via the par_oReturn parameter the 'User Friendly' Name of the current record in par_oRowset.
            // This is to be called from each RecordOnSave script, but can be called
            // from any other code to generate a SYS_Name value. Do NOT set the name in 
            // the par_doCallingObject rowset or the script won't be usable simply for evaluating the returned
            // string.
            // IMPORTANT: Keep this "in sync" with clScripts.GetDefaultSort(), which is called from
            // clData.GetDefaultSort.
            // PARAMETERS:
            // par_doCallingObject: rowset object (for example, 'doRS'). The rowset must contain
            // all links and fields being referenced in the code below or error 45163 will be
            // raised. This can be achieved by putting '**' in the FIELDS parameter of the rowset, but 
            // avoid this when possible for performance reasons. The object, declared ByRef to conserve
            // resources by avoiding duplicating the object in memory, should not be altered directly
            // by this method (the purpose of the method is to return the name, not set it), but check
            // the code below to be sure.
            // par_doArray: not used
            // par_s1 - 5: not used
            // par_oReturn: String containing the generated SysName.
            // RETURNS:
            // True as a result. Returns friendly name or an empty string if the
            // filename is invalid via par_oReturn parameter.
            // EXAMPLE:
            // 'From a RecordOnSave script (not tested):
            // Dim sName as string = goScr.RunScript("GenerateSysName", doRS)
            // NOTES:
            // When a "Name" that is built with this method
            // is displayed in a View or linkbox and the same Name field is used
            // to sort the View or linkbox, at least the first field should match
            // the first field defined in clData::LKGetSortValue(). Otherwise what's
            // displayed will appear to be sorted arbitrarily.
            // NOTE 2:  
            // Links will not be tested because they are loaded automatically, but 
            // currently there is a bug in clRowset. RH working on this.

            string sProc = "clScripts:GenerateSysName";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sTemp4 = "";
            string sFileName = doRS.GetFileName();
            string sResult = "";
            // Dim doLink As clRowSet
            // Dim iLen As Integer

            // We assume that sFileName is valid. If this is a problem, test it here and SetError.

            switch (Strings.UCase(sFileName))
            {
                case "TE":        // TERR		TXT_TerrName
                    {
                        if (!doRS.IsLoaded("MMO_Description"))
                            goErr.SetError(35103, sProc, "", sFileName + ".MMO_Description");
                        if (!doRS.IsLoaded("TXT_TerrName"))
                            goErr.SetError(35103, sProc, "", sFileName + ".TXT_TerrName");
                        sTemp = goTR.ExtractString(Convert.ToString(doRS.GetFieldVal("MMO_Description", 0, 30)), 1, Constants.vbCrLf);
                        sResult = Convert.ToString(doRS.GetFieldVal("TXT_TerrName", 0, 49));
                        if (sTemp != "")
                            sResult += " " + sTemp;

                        sResult = goTR.Replace(sResult, Constants.vbCrLf, " ");
                        sResult = goTR.Replace(sResult, Strings.Chr(10).ToString(), " ");
                        sResult = goTR.Replace(sResult, Strings.Chr(13).ToString(), " ");
                        sResult = goTR.Replace(sResult, Constants.vbTab, " ");
                        par_oReturn = sResult;
                        break;
                    }
            }

            par_doCallingObject = doRS;
            return true;
        }
        public bool GetDefaultSort(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here.
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }
        public bool MergeRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 3/18/2011 Added for merge
            par_bRunNext = false;

            clRowSet doRSMerge = (clRowSet)par_doCallingObject;     // Record being merged, will be deactivated
            clRowSet doRSMergeTo;                         // Good record, stays active

            clArray aFields;
            clArray aLinks;
            string sField;
            string sFieldType;
            clArray doLink = new clArray();
            string[] sLinkType;
            string sReturn = "";

            try
            {
                // Enumerate schema
                // aFields = goData.GetFields("CN")
                aFields = goData.GetFields(doRSMerge.GetFileName());
                // aLinks = goData.GetLinks("CN")
                aLinks = goData.GetLinks(doRSMerge.GetFileName());

                // Get mergeto record from rowset of merged record. User selects mergeto record on the form
                doRSMergeTo = new clRowSet(doRSMerge.GetFileName(), 1, "GID_ID = '" + doRSMerge.GetFieldVal("LNK_MergedTo_" + doRSMerge.GetFileName()) + "'", "", "**", -1, "", "", "", "", "", true, true, false, false, -1, "", true);
                if (doRSMergeTo.GetFirst() == 1)
                {
                    for (int i = 1; i <= aFields.GetDimension(); i++)
                    {
                        sField = aFields.GetItem(i);
                        sFieldType = Strings.Left(sField, 3);
                        switch (sFieldType)
                        {
                            case "TXT":
                            case "TEL":
                            case "EML":
                            case "URL":
                                {
                                    if (doRSMergeTo.GetFieldVal(sField).ToString() == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    break;
                                }

                            case "MMO":
                                {
                                    // Append
                                    if (Convert.ToString(doRSMergeTo.GetFieldVal(sField)) == "")
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField));
                                    else
                                        doRSMergeTo.SetFieldVal(sField, Convert.ToString(doRSMergeTo.GetFieldVal(sField)) + Constants.vbCrLf + Constants.vbCrLf + "== Merged from record " + Convert.ToString(doRSMerge.GetFieldVal("SYS_Name")) + " ==" + Constants.vbCrLf + Convert.ToString(doRSMerge.GetFieldVal(sField)));
                                    break;
                                }

                            case "CHK":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }

                            case "MLS":
                                {
                                    if (Convert.ToInt32(doRSMergeTo.GetFieldVal(sField, 2)) == 0)
                                        doRSMergeTo.SetFieldVal(sField, doRSMerge.GetFieldVal(sField, 2), 2);
                                    break;
                                }
                        }
                    }

                    for (int i = 1; i <= aLinks.GetDimension(); i++)
                    {
                        // If NN link, copy all. If N1, copy only if blank
                        sLinkType = Strings.Split(goData.LK_GetType(doRSMerge.GetFileName(), aLinks.GetItem(i)), Strings.Chr(9).ToString());
                        if (sLinkType[4] == "NN" | sLinkType[1] == "2")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                        else if (Convert.ToString(doRSMergeTo.GetFieldVal(aLinks.GetItem(i))) == "")
                        {
                            oTable = null;
                            doLink = doRSMerge.GetLinkVal(aLinks.GetItem(i), ref doLink, true, 0, -1, "A_a", ref oTable);
                            doRSMergeTo.SetLinkVal(aLinks.GetItem(i), doLink);
                        }
                    }

                    // Check Merged on merged record
                    doRSMerge.SetFieldVal("CHK_MERGED", 1, 2);
                    // Uncheck on mergeto record
                    doRSMergeTo.SetFieldVal("CHK_Merged", 0, 2);

                    doRSMerge.SetFieldVal("CHK_ACTIVEFIELD", 0, 2);
                    // Link Merged record to master
                    doRSMerge.SetFieldVal("LNK_MERGEDTO_" + doRSMerge.GetFileName(), doRSMergeTo.GetFieldVal("GID_ID"));
                    // Clear link on merge to record
                    doRSMergeTo.ClearLinkAll("LNK_MergedTo_" + doRSMerge.GetFileName());

                    // Commit both records
                    doRSMerge.Commit();
                    doRSMergeTo.Commit();
                }

                sReturn = "Success";
            }
            catch (Exception ex)
            {
                sReturn = "Failed";
            }

            par_oReturn = sReturn;

            par_doCallingObject = doRSMerge;
            return true;
        }
        public bool MessageBoxEvent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // Par_s5 will always be the name of the script that called doform.MessageBox
            // Par_s1 will be whatever button the user clicked.
            // Par_s2-Par_s4 can be whatever else you want to pass.
            // In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sJournal = "";
            string sWork = "";

            switch (Strings.UCase(par_s5))
            {
                case "MERGE":
                    {
                        //par_bRunNext = false;
                        doForm = (Form)par_doCallingObject;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":
                                {
                                    // run merge script, continue save
                                    //goScr.RunScript("MergeRecord", doForm.doRS);
                                    par_doCallingObject = doForm.doRS;
                                    bool runnext = true;
                                    scriptManager.RunScript("MergeRecord", ref par_doCallingObject, ref par_oReturn, ref runnext, ref par_sSections);
                                    doForm.doRS = (clRowSet)par_doCallingObject;                                  
                                    break;
                                }

                            case "NO":
                                {
                                    // Clear merged to co linkbox, continue save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    break;
                                }

                            case "CANCEL":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "MERGEFAIL":
                    {
                        par_bRunNext = false;
                        doForm.oVar.SetVar(par_s4 + "_Merge", "1");
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    // Clear merged to co linkbox, cancel save
                                    doForm.doRS.ClearLinkAll("LNK_MergedTo_" + par_s4);
                                    doForm.oVar.SetVar("CancelSave", "1");
                                    break;
                                }
                        }

                        break;
                    }

                case "QT_FORMCONTROLONCHANGE_BTN_INSERTLINE":
                    {
                        par_bRunNext = false;
                        switch (Strings.UCase(par_s1))
                        {
                            case "OK":
                                {
                                    sJournal = Convert.ToString(doForm.doRS.GetFieldVal("MMO_Journal"));
                                    // CS 1/26/10: Set par_s2 (typed entry) in a variable to use later for putting in the Journal AC.
                                    // Below we convert vbcrlf to spaces in some cases and we don't want to use that for the Journal AC.
                                    sWork = par_s2;
                                    doForm.oVar.SetVar("JournalWithHardReturns", sWork + Constants.vbCrLf + sJournal);
                                    if (sWork != "")
                                    {
                                        // CS 1/26/10: If WOP is on to replace hard returns with 3 spaces do that here
                                        // If not defined in WOP, default is 1
                                        if (Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%JOURNALWITHOUTHARDRETURNS")) != "0")
                                        {
                                            sWork = goTR.Replace(sWork, Constants.vbCrLf, (Strings.Chr(32) + Strings.Chr(32) + Strings.Chr(32)).ToString());
                                            doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                        }
                                        else
                                            doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork + Constants.vbCrLf + sJournal);
                                        doForm.MoveToField("MMO_JOURNAL");
                                        doForm.oVar.SetVar("QT_JournalType", Convert.ToString(doForm.doRS.GetFieldVal("MLS_JournalType", 1)));
                                        doForm.doRS.SetFieldVal("MLS_JournalType", 31, 2);
                                    }

                                    break;
                                }
                        }

                        break;
                    }
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool ModelsCreateRecordsFromDatatable_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 10/23/2012 Tasks: Model Import From Excel

            try
            {
                DataSet ds = (DataSet)par_doCallingObject;
                DataTable dt = ds.Tables[0];
                string sWork = "";
                int iCounter = 0;
                bool bModelCol = false;
                bool bStatusCol = false;
                bool bPDCol = false;
                bool bDescCol = false;

                // Check for records?
                if (dt.Rows.Count == 0 | dt.Columns.Count == 0)
                {
                    // No records return true to timeout?
                    // Return True
                    goMeta.LineWrite(goP.GetMe("ID"), "OTH_Model_Upload_ImportCompleteFlag", "ImportComplete", "True", ref SqlConnection);
                    par_doCallingObject = ds;
                    return false;
                }

                // Associate column headers in Excel to field names on MO form
                Hashtable oHashFieldName = new Hashtable();
                oHashFieldName.Add("TXT_ModelName", "TXT_ModelName");
                oHashFieldName.Add("MLS_Status", "CHK_ActiveField");
                oHashFieldName.Add("LNK_Of_PD", "LNK_Of_PD");
                oHashFieldName.Add("MMO_Description", "MMO_Description");

                // Check table for columns
                if (dt.Columns.Contains("TXT_ModelName"))
                    bModelCol = true;
                if (dt.Columns.Contains("MLS_Status"))
                    bStatusCol = true;
                if (dt.Columns.Contains("LNK_Of_PD"))
                    bPDCol = true;
                if (dt.Columns.Contains("MMO_Description"))
                    bDescCol = true;

                // If TXT_ModelName column does not exist, exit?
                if (bModelCol == false)
                {
                    goMeta.LineWrite(goP.GetMe("ID"), "OTH_Model_Upload_ImportCompleteFlag", "ImportComplete", "True", ref SqlConnection);
                    par_doCallingObject = ds;
                    return false;
                }

                // loop through import dt and create Model records
                clRowSet moRS;
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    iCounter += 1;

                    // Clear vars
                    sWork = "";

                    // Need to check for existing first
                    // TXT_ModelName
                    // sWork = goTR.PrepareForSQL(dr(0).ToString)
                    sWork = dr["TXT_ModelName"].ToString();

                    // Check for dups, update all dups?
                    moRS = new clRowSet("MO", 1, "TXT_ModelName='" + sWork + "'", "", "", -1, "", "", "", "", "", true);
                    if (moRS.GetFirst() == 1)
                    {
                        // Edit existing dups
                        do
                        {
                            // MLS_Status -- list on their end, but corresponds to
                            // our chk_activefield
                            if (bStatusCol)
                            {
                                sWork = dr["MLS_Status"].ToString();
                                if (Strings.UCase(sWork) == "CHECKED")
                                    // Checked
                                    moRS.SetFieldVal("CHK_ActiveField", 1, 2);
                                else
                                    // Unchecked?
                                    moRS.SetFieldVal("CHK_ActiveField", 0, 2);
                            }

                            if (bPDCol)
                            {
                                // Get Product #, try to connect to existing product
                                sWork = dr["LNK_Of_PD"].ToString();
                                if (sWork != "")
                                {
                                    // Try to get product by name?
                                    clRowSet doPDRS = new clRowSet("PD", 3, "TXT_ProductNo='" + sWork + "'", "", "GID_ID", 1);
                                    if (doPDRS.GetFirst() == 1)
                                    {
                                        moRS.SetFieldVal("LNK_Of_PD", doPDRS.GetCurrentRecID());
                                        doPDRS = null/* TODO Change to default(_) if this is not a reference type */;
                                    }
                                }
                            }

                            if (bDescCol)
                            {
                                // MMO_Description
                                moRS.SetFieldVal("MMO_Description", dr["MMO_Description"].ToString());
                                if (moRS.Commit() == 0)
                                {
                                    if (goErr.GetLastError("NUMBER") == "E47250")
                                        // failed due to permissions, write to log
                                        goLog.Log(sProc, "Model Update from Import failed due to permissions for " + Convert.ToString(moRS.GetFieldVal("TXT_ModelName")), 1, false, true);
                                    else
                                        // write message to log with error
                                        goLog.Log(sProc, "Model Update from Import failed for " + Convert.ToString(moRS.GetFieldVal("TXT_ModelName")), 1, false, true);
                                }
                            }

                            if (moRS.GetNext() == 0)
                                break;
                        }
                        while (true);
                        moRS = null/* TODO Change to default(_) if this is not a reference type */;
                    }
                    else
                    {
                        // Create Rowset
                        moRS = new clRowSet("MO", 2, "", "", "", 1, "", "", "", "", "", true);

                        // Fill fields

                        // TXT_ModelName -- need to check for dups first
                        moRS.SetFieldVal("TXT_ModelName", dr["TXT_ModelName"].ToString());

                        if (bStatusCol)
                        {
                            // MLS_Status -- list on their end, but corresponds to
                            // our chk_activefield
                            sWork = dr["MLS_Status"].ToString();
                            if (Strings.UCase(sWork) == "CHECKED")
                                // Checked
                                moRS.SetFieldVal("CHK_ActiveField", 1, 2);
                            else
                                // Unchecked?
                                moRS.SetFieldVal("CHK_ActiveField", 0, 2);
                        }

                        if (bPDCol)
                        {
                            // Get Product #, try to connect to existing product
                            sWork = dr["LNK_Of_PD"].ToString();
                            if (sWork != "")
                            {
                                // Try to get product by name?
                                clRowSet doPDRS = new clRowSet("PD", 3, "TXT_ProductNo='" + sWork + "'", "", "GID_ID", 1);
                                if (doPDRS.GetFirst() == 1)
                                {
                                    moRS.SetFieldVal("LNK_Of_PD", doPDRS.GetCurrentRecID());
                                    doPDRS = null/* TODO Change to default(_) if this is not a reference type */;
                                }
                            }
                        }

                        if (bDescCol)
                            // MMO_Description
                            moRS.SetFieldVal("MMO_Description", dr["MMO_Description"].ToString());

                        if (moRS.Commit() == 0)
                        {
                            if (goErr.GetLastError("NUMBER") == "E47250")
                                // failed due to permissions, write to log
                                goLog.Log(sProc, "Model Creation From Import failed due to permissions on import row " + dr.ItemArray.ToString(), 1, false, true);
                            else
                                // write message to log with error
                                goLog.Log(sProc, "Model Creation From Import failed on import row " + dr.ItemArray.ToString(), 1, false, true);
                            if (sError == "")
                                sError = "Could not create model " + iCounter;
                        }
                    }
                }

                if (sError != "")
                {
                    // Show user message?
                    goUI.NewWorkareaMessage(sError + " Check XL Log for more information.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
                    par_doCallingObject = ds;
                    return true;
                }
            }
            catch (Exception ex)
            {
                sError = goErr.GetLastError();
                goUI.NewWorkareaMessage("Model import failed.  Make sure the excel model file is open.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
            }

            // Successful?
            // Send alert that done?
            // Opens Models - My Created Today
            goMeta.LineWrite(goP.GetMe("ID"), "OTH_Model_Upload_ImportCompleteFlag", "ImportComplete", "True", ref SqlConnection);
            goUI.AddAlert("Model Import Done", clC.SELL_ALT_OPENDESKTOP, "DSK_72227496-16DD-48DB-5858-A0F30151D93B", goP.GetMe("ID"), "Model16.gif");

            return true;
        }
        public bool Models_OpenWOP_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 10/23/2012 For import models from open spreadsheet

            try
            {
                bool bPerms = true; // Assume perms

                // Check feature perms for import from excel?
                if (goPerm.GetUserPermission(goP.GetMe("ID"), "IMPORT") != "1")
                    bPerms = false;

                // Perm to create Models?
                if (goData.GetAddPermission("MO") == false)
                    bPerms = false;

                if (bPerms)
                    // open custom Expense page
                    goUI.OpenURLExternal("../Pages/cus_wopModels.aspx", "Selltis", "height=500,width=600,left=100,top=100,status=yes,location=no,toolbar=no,resizable=yes,titlebar=no,dependent=yes");
                else
                    // Message to user
                    goUI.NewWorkareaMessage("You do not have permission to (1) import excel data and/or (2) to create models.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
            }
            catch (Exception ex)
            {
                if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
                    goErr.SetError(ex, 45105, sProc);
            }

            return true;
        }
        public bool QL_FormControlOnChange_BTN_USEMODELPRICE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            // CS 11/2/07: Commenting this out b/c core scripts was restructured such that this function calls Quoteline_FillItem

            // Dim sSKUPrice As String
            // Dim cSKUPrice As Decimal
            // Dim sSKUCost As String
            // Dim cSKUCost As Decimal
            // Dim rQtyFld As Decimal
            // Dim cCostVal As Decimal
            // Dim sModel As String
            // Dim sLinkedModel As String
            // Dim sUnit As String
            // Dim strMOType As String = ""
            // Dim strMODescription As String = ""
            // Dim strMOPartNumber As String = ""

            // Dim doForm As Object = par_doCallingObject

            // 'If no model selected, display messagebox
            // If doForm.dors.getlinkcount("LNK_FOR_MO") = 0 Then
            // doForm.messagebox("Please select a Model.")
            // Return True
            // End If

            // 'Set Unit price to linked model's price
            // sSKUPrice = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_PRICE", 1)
            // If sSKUPrice <> "" Then
            // cSKUPrice = sSKUPrice
            // End If
            // 'goP.TraceLine("LNK_FOR_MO%%CUR_PRICE: " & Convert.ToString(cSKUPrice))

            // doForm.doRS.SetFieldVal("CUR_PRICEUNIT", cSKUPrice, 2)

            // 'Set Cost to linked model's cost
            // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST", 1)
            // If sSKUCost <> "" Then
            // cSKUCost = sSKUCost
            // rQtyFld = doForm.doRS.GetFieldVal("SR__QTY")
            // cCostVal = cSKUCost * rQtyFld
            // doForm.doRS.SetFieldVal("CUR_COST", cCostVal)
            // End If

            // 'If txt_model is blank set to linked model's description
            // sModel = doForm.doRS.GetFieldVal("TXT_Model")
            // If sModel = "" Then
            // strMOType = doForm.doRS.GetFieldVal("LNK_FOR_MO%%TXT_Type")
            // strMODescription = doForm.doRS.GetFieldVal("LNK_FOR_MO%%MMO_Description")
            // strMOPartNumber = "Grover PN: " & doForm.doRS.GetFieldVal("LNK_FOR_MO%%TXT_PartNumber")
            // doForm.doRS.SetFieldVal("TXT_MODEL", strMOType & vbCrLf & strMODescription & vbCrLf & strMOPartNumber)
            // End If

            // 'Fill unit with linked model's unit
            // sUnit = doForm.doRS.GetFieldVal("LNK_FOR_MO%%txt_unittext")
            // doForm.doRS.SetFieldVal("TXT_Unit", sUnit)

            // 'Calc quote line totals
            // goScr.RunScript("Quotline_CalcTotal", doForm)

            // par_bRunNext = False

            return true;
        }
        public bool QL_FormControlOnChange_LNK_FOR_MO_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // CS commenting this out b/c core scripts was restructured such that this function now calls Quoteline_FillItem
            // Dim strMOType As String = ""
            // Dim strMODescription As String = ""
            // Dim strMOPartNumber As String = ""
            // Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
            // goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

            // Dim doForm As Object = par_doCallingObject

            // Dim sSKUPrice As String
            // Dim cSKUPrice As Decimal
            // Dim sSKUCost As String
            // Dim cSKUCost As Decimal
            // Dim rQtyFld As Decimal
            // Dim cCostVal As Decimal
            // Dim sModel As String
            // Dim sLinkedModel As String
            // Dim sUnit As String


            // 'If no model selected, display messagebox
            // If doForm.dors.getlinkcount("LNK_FOR_MO") = 0 Then
            // Return True
            // End If

            // 'Set Unit price to linked model's price
            // sSKUPrice = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_PRICE", 1)
            // If sSKUPrice <> "" Then
            // cSKUPrice = sSKUPrice
            // End If

            // doForm.doRS.SetFieldVal("CUR_PRICEUNIT", cSKUPrice, 2)

            // 'Set Cost to linked model's cost
            // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST", 1)
            // If sSKUCost <> "" Then
            // cSKUCost = sSKUCost
            // rQtyFld = doForm.doRS.GetFieldVal("SR__QTY")
            // cCostVal = cSKUCost * rQtyFld
            // doForm.doRS.SetFieldVal("CUR_COST", cCostVal)
            // End If

            // 'If txt_model is blank set to linked model's description
            // sModel = doForm.doRS.GetFieldVal("TXT_Model")
            // If sModel = "" Then
            // strMOType = doForm.doRS.GetFieldVal("LNK_FOR_MO%%TXT_Type")
            // strMODescription = doForm.doRS.GetFieldVal("LNK_FOR_MO%%MMO_Description")
            // strMOPartNumber = "Grover PN: " & doForm.doRS.GetFieldVal("LNK_FOR_MO%%TXT_PartNumber")
            // doForm.doRS.SetFieldVal("TXT_MODEL", strMOType & vbCrLf & strMODescription & vbCrLf & strMOPartNumber)
            // End If

            // 'Fill unit with linked model's unit
            // sUnit = doForm.doRS.GetFieldVal("LNK_FOR_MO%%txt_unittext")
            // doForm.doRS.SetFieldVal("TXT_Unit", sUnit)

            // goScr.RunScript("Quotline_CheckTaxable", doForm)
            // goScr.RunScript("Quotline_ConnectVendors", doForm.doRS)
            // goScr.RunScript("Quotline_CalcTotal", doForm)        'runs CalcTotal
            // goScr.RunScript("Quotline_FillItem", doForm)


            // 'Set Product link to Model's Product
            // doForm.doRS.ClearLinkAll("LNK_FOR_PD")
            // doForm.doRS.SetFieldVal("LNK_FOR_PD", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 2), 2)
            // par_bRunNext = False

            return true;
        }
        public bool QT_FormControlOnChange_LNK_TO_CO_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 11/16/2012 Set Credited To User to Company's Team Leader
            doForm.doRS.SetFieldVal("LNK_CreditedTo_US", doForm.doRS.GetFieldVal("LNK_To_CO%%LNK_TeamLeader_US", 2), 2);

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 9/12/2012 Disable fields for Target Account
            doForm.SetControlState("TXT_CURRANDPOT", 4);

            // TLD 9/19/2012 Disable CHK_Merged
            doForm.SetControlState("CHK_Merged", 4);
            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);

            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", color);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }

            // TLD 7/22/2014 commented, now user created by user
            // 'TLD 11/16/2012 On new, set Credited to User
            // 'and Peer User to
            // 'Routing Record's Company Team Leader user
            // If doForm.GetMode() = "CREATION" Then
            // 'Set Credited To User to Company's Team Leader
            // doForm.doRS.SetFieldVal("LNK_CreditedTo_US", doForm.doRS.GetFieldVal("LNK_To_CO%%LNK_TeamLeader_US", 2), 2)
            // 'Check for routing record
            // Dim doRORS As New clRowSet("RO", 3, "TXT_RoutingName='Quote Peer User'", , "LNK_To_US")
            // If doRORS.GetFirst() Then
            // doForm.doRS.SetFieldVal("LNK_Peer_US", doRORS.GetFieldVal("LNK_To_US", 2, , , 1), 2)
            // doRORS = Nothing
            // End If
            // End If
            // SKO ******** Ticket#1006: when a quote is opened a second and or subsequent times, take the user to the journal tab, rather than the Lines tab.
            if (doForm.GetMode() == "CREATION")
                doForm.MoveToTab(1);
            else
                doForm.MoveToTab(2);

            // VS ******** TKT#1093 : Drive the user to the Lead/Opportunity tab when the mls_stage is lead or opportunity
            switch (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STAGE", 2)))
            {
                case 1:
                case 2:   // Lead & Opp
                    {
                        doForm.MoveToTab(16);    // Lead/Opportunity
                        break;
                    }
            }

            doForm.SetControlState("CHK_LEAD", 4);
            doForm.SetControlState("CHK_OPPORTUNITY", 4);
            doForm.SetControlState("CHK_LEADTOOPP", 4);
            doForm.SetControlState("CHK_LEADTOQUOTE", 4);
            doForm.SetControlState("CHK_OPPTOQUOTE", 4);


            // SKO 02082017 TKT#1423 :
            if (doForm.GetMode() == "CREATION")
            {
                string sID = doForm.GetCreateLinkedSourceRecSUID;
                // If the ID is not blank, find out if coming from an AC
                if (sID != "" & sID != null)
                {
                    if (goTR.GetFileFromSUID(sID) == "CO")
                    {
                        // Check if this AC is Lead
                        clRowSet doRSAC = new clRowSet("CO", 3, "GID_ID='" + sID + "'", "", "GID_ID,CHK_TargetAcct");
                        if (doRSAC.GetFirst() == 1)
                        {
                            if (Convert.ToInt32(doRSAC.GetFieldVal("CHK_TargetAcct", 2)) == 1)
                                doForm.doRS.SetFieldVal("mls_strategicaccountquote", 10, 2); // US_10=Strategic
                            else
                                doForm.doRS.SetFieldVal("mls_strategicaccountquote", 20, 2);// US_20=Custom Accounts
                        }
                        else
                            doForm.doRS.SetFieldVal("mls_strategicaccountquote", 20, 2);// US_20=Custom Accounts
                    }
                }
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool Quotline_FillItem_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // goP.TraceLine("", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            // PURPOSE:
            // Fill the TXT_MODEL field
            // RETURNS:
            // True.

            // CS 11/2/07: Comment out below b/c of restructuring of ql function in main scripts file

            // If Trim(doForm.doRS.GetFieldVal("TXT_Model")) = "" Then
            // If doForm.dors.GetLinkCount("LNK_FOR_MO") > 0 Then
            // strMOType = doForm.doRS.GetFieldVal("LNK_FOR_MO%%TXT_Type")
            // strMODescription = doForm.doRS.GetFieldVal("LNK_FOR_MO%%MMO_Description")
            // strMOPartNumber = "Grover PN: " & doForm.doRS.GetFieldVal("LNK_FOR_MO%%TXT_PartNumber")
            // doForm.doRS.SetFieldVal("TXT_MODEL", strMOType & vbCrLf & strMODescription & vbCrLf & strMOPartNumber)
            // End If
            // End If
            // par_bRunNext = False

            // TLD 8/26/2009 Moved to Top
            par_bRunNext = false;

            string sSKUPrice;
            decimal cSKUPrice = 0;
            string sSKUCost = "";
            decimal cSKUCost;
            decimal rQtyFld;
            decimal cCostVal;
            string sModel;
            // Dim sLinkedModel As String
            string sUnit;
            string strMOType = "";
            string strMODescription = "";
            string strMOPartNumber = "";
            string sResult = "";

            // If no model selected, return
            if (doForm.doRS.GetLinkCount("LNK_FOR_MO") == 0)
            {
                par_doCallingObject = doForm;
                return true;
            }

            // TLD 8/26/2009 Added for optimization
            // CS 6/3/09 Store QL Model info in var to use instead of creating rowsets throughout.
            string sWork = "";
            clRowSet doMO = new clRowSet("MO", 3, "GID_ID='" + Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO")) + "'", "", "CUR_PRICE,CUR_COST,TXT_TYPE,MMO_Description,TXT_PartNumber,TXT_UNITTEXT,LNK_OF_PD");
            if (doMO.GetFirst() == 1)
            {
                goTR.StrWrite(ref sWork, "MO_CUR_PRICE", doMO.GetFieldVal("CUR_PRICE"));
                goTR.StrWrite(ref sWork, "MO_CUR_COST", doMO.GetFieldVal("CUR_COST"));
                goTR.StrWrite(ref sWork, "MO_TXT_TYPE", doMO.GetFieldVal("TXT_TYPE"));
                goTR.StrWrite(ref sWork, "MO_MMO_DESCRIPTION", doMO.GetFieldVal("MMO_DESCRIPTION"));
                goTR.StrWrite(ref sWork, "MO_TXT_PARTNUMBER", doMO.GetFieldVal("TXT_PARTNUMBER"));
                goTR.StrWrite(ref sWork, "MO_TXT_UNITTEXT", doMO.GetFieldVal("TXT_UNITTEXT"));
                goTR.StrWrite(ref sWork, "MO_LNK_OF_PD", doMO.GetFieldVal("LNK_OF_PD"));
            }

            // TLD 8/26/2009 Updated to use var if not empty
            // Set Unit price to linked model's price
            if (sWork == "")
                sSKUPrice = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_PRICE", 2));
            else
                sSKUPrice = goTR.StrRead(sWork, "MO_CUR_PRICE");
            if (sSKUPrice != "")
            {
                sSKUPrice = sSKUPrice.Replace("$", "");
                cSKUPrice = decimal.Parse(sSKUPrice);
            }

            doForm.doRS.SetFieldVal("CUR_PRICEUNIT", cSKUPrice, 2);

            // TLD 8/26/2009 Updated to use var if not empty
            // Set Cost to linked model's cost
            if (sWork == "")
                sSKUCost = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST", 2));
            else
                sSKUCost = goTR.StrRead(sWork, "MO_CUR_COST");
            if (sSKUCost != "")
            {
                sSKUCost = sSKUCost.Replace("$", "");
                cSKUCost = decimal.Parse(sSKUCost);
                rQtyFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("SR__QTY"));
                cCostVal = cSKUCost * rQtyFld;
                doForm.doRS.SetFieldVal("CUR_COST", cCostVal);
            }

            // TLD 8/26/2009 Updated to use var if not empty
            // If txt_model is blank set to linked model's description
            sModel = Convert.ToString(doForm.doRS.GetFieldVal("TXT_Model"));
            if (sModel == "")
            {
                if (sWork == "")
                {
                    strMOType = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO%%TXT_Type"));
                    strMODescription = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO%%MMO_Description"));
                    strMOPartNumber = "Grover PN: " + doForm.doRS.GetFieldVal("LNK_FOR_MO%%TXT_PartNumber");
                }
                else
                {
                    strMOType = goTR.StrRead(sWork, "MO_TXT_TYPE");
                    strMODescription = goTR.StrRead(sWork, "MO_MMO_DESCRIPTION");
                    strMOPartNumber = "Grover PN: " + goTR.StrRead(sWork, "MO_TXT_PartNumber");
                }
                // SKO 10142015 TKT#759 
                if (strMOType != "")
                {
                    sResult = strMOType + Constants.vbCrLf;
                    if (strMODescription != "")
                        sResult = sResult + strMODescription + Constants.vbCrLf;
                }
                else if (strMOType == "")
                {
                    if (strMODescription != "")
                        sResult = strMODescription + Constants.vbCrLf;
                }
                if (strMOPartNumber != "")
                    sResult = sResult + strMOPartNumber;

                doForm.doRS.SetFieldVal("TXT_MODEL", Strings.Trim(sResult));
            }

            // TLD 8/26/2009 Updated to use var if not empty
            // Fill unit with linked model's unit
            if (sWork == "")
                sUnit = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO%%txt_unittext"));
            else
                sUnit = goTR.StrRead(sWork, "MO_txt_unittext");
            doForm.doRS.SetFieldVal("TXT_Unit", sUnit);

            //goScr.RunScript("Quotline_CheckTaxable", doForm);
            //goScr.RunScript("Quotline_ConnectVendors", doForm.doRS);
            //goScr.RunScript("Quotline_CalcTotal", doForm);        // runs CalcTotal

            par_doCallingObject = doForm;
            scriptManager.RunScript("Quotline_CheckTaxable", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            doForm = (Form)par_doCallingObject;

            par_doCallingObject = doForm.doRS;
            scriptManager.RunScript("Quotline_ConnectVendors", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            doForm.doRS = (clRowSet)par_doCallingObject;

            par_doCallingObject = doForm;
            scriptManager.RunScript("Quotline_CalcTotal", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections); // runs CalcTotal
            doForm = (Form)par_doCallingObject;

            // TLD 8/26/2009 Updated to use var if not empty
            // Set Product link to Model's Product
            doForm.doRS.ClearLinkAll("LNK_FOR_PD");
            if (sWork == "")
                doForm.doRS.SetFieldVal("LNK_FOR_PD", doForm.doRS.GetFieldVal("LNK_FOR_MO%%LNK_OF_PD", 2), 2);
            else
                doForm.doRS.SetFieldVal("LNK_FOR_PD", goTR.StrRead(sWork, "MO_LNK_OF_PD"), 2);

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Rowset object containing the record to be saved.
            // par_doArray: Unused.
            // par_sMode: 'CREATION' or 'MODIF'.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;;

            // VS 10082015 TKT#745 : Check CHK_HASATTACHMENTS if QT has attachments.
            if (Convert.ToString(doRS.GetFieldVal("ADR_ATTACHMENTS")) == "")
                doRS.SetFieldVal("CHK_HASATTACHMENTS", 0, 2);
            else
                doRS.SetFieldVal("CHK_HASATTACHMENTS", 1, 2);

            // VS ******** TKT#1093 : Set checkbox values based on Stage
            int iStageOld = 0;
            int iStageNew = 0;
            iStageNew = Convert.ToInt32(doRS.GetFieldVal("MLS_STAGE", 2));

            if (iStageNew == 1)
                doRS.SetFieldVal("CHK_LEAD", 1, 2);
            if (iStageNew == 2)
                doRS.SetFieldVal("CHK_OPPORTUNITY", 1, 2);

            if (doRS.iRSType != clC.SELL_ADD)
            {
                iStageOld = Convert.ToInt32(goData.GetFieldValueFromRec(Convert.ToString(doRS.GetCurrentRecID()), "MLS_STAGE", 2));

                if (iStageOld == 1)
                {
                    if (iStageNew == 2)
                        doRS.SetFieldVal("CHK_LEADTOOPP", 1, 2);
                    else if (iStageNew == 3)
                        doRS.SetFieldVal("CHK_LEADTOQUOTE", 1, 2);
                }
                else if (iStageOld == 2)
                {
                    if (iStageNew == 3)
                        doRS.SetFieldVal("CHK_OPPTOQUOTE", 1, 2);
                }
            }

            par_doCallingObject = doRS;
            return true;
        }
        public bool QT_FormControlOnChange_BTN_INSERTLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/8/07 Changed time stamp to local time, no label.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // Dim sWork As String
            string sParams = "";
            // SKO TKT:#1006 ********
            //par_bRunNext = false;

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("   " & par_sFormAlias & " " & par_sFieldName, "", sProc)

            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_JournalType", 2)) == 0)
            {
               par_bRunNext = false;
                doForm.MessageBox("Please select a Journal Type.");
                par_doCallingObject = doForm;
                return true;
            }
            else
            {
                //goScr.RunScript("GetDateTimeStamp", doForm, "", "NEUTRAL", "sDateStamp", "CODE", "USERNOOFFSETLABEL"); // returns var sDateStamp
                par_doCallingObject = doForm;
                scriptManager.RunScript("GetDateTimeStamp", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "NEUTRAL", "sDateStamp", "CODE", "USERNOOFFSETLABEL"); // returns var sDateStamp
                doForm = (Form)par_doCallingObject;


                doForm.MessageBox("Please enter your journal note.", clC.SELL_MB_INPUTBOX, "Add Journal Note", "OK", "", "", Convert.ToString(doForm.oVar.GetVar("sDateStamp")) + " ", "MessageBoxEvent", "", "", "", null, "OK", "", "", "", "QT_FormControlOnChange_BTN_INSERTLINE");
               par_bRunNext = false;
                par_doCallingObject = doForm;
                return true;
            }

            // goTR.StrWrite(sParams, "TITLE", "Add Note")

            // sWork = doForm.oVar.GetVar("sDateStamp")
            // If sWork <> "" Then
            // sWork &= vbCrLf & vbCrLf & doForm.doRS.GetFieldVal("MMO_JOURNAL")
            // doForm.doRS.SetFieldVal("MMO_JOURNAL", sWork)
            // doForm.MoveToField("MMO_JOURNAL")
            // 'doForm.oVar.SetVar("sJournalVal", sWork)
            // End If


            // CS: Inputbox must be replaced in web context.
            // sWork = InputBox(doForm.oVar.GetVar("sDateStamp") & " ", sParams, doForm)
            // If sWork <> "" Then
            // sWork &= vbCrLf & vbCrLf & doForm.GetFieldVal("MMO_JOURNAL")
            // doForm.SetFieldVal("MMO_JOURNAL", sWork)
            // doForm.MoveToField("MMO_JOURNAL")
            // doForm.oVar.SetVar("sJournalVal", sWork)
            // End If





            //return true;
        }
        public bool Quote_CreateActLog_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sWork;
            int lWork;
            string sNotes;
            string sMessage;

            par_bRunNext = false;

            // goP.TraceLine("", "", sProc)

            // PURPOSE:
            // Offer to create an Activity when the Journal in the Quote have been edited.
            // Originally by PJ 5/30/02 Adds Act Log with new Journal of Qte.
            // RETURNS:
            // True if Activity was created or creation wasn't needed; False if the user declined creating an Activity.


            // CS: per MI, no prompts
            // 'If answered Yes on MB to create a journal, then go directly to script that creates journal.
            // If UCase(par_s1) = "YES" Then
            // GoTo CREATEACTLOG
            // End If

            if (Convert.ToString(doForm.doRS.GetFieldVal("MMO_JOURNAL")).Length <= Convert.ToInt32(doForm.oVar.GetVar("lLenJournal")))
            {
                par_doCallingObject = doForm;
                return true;
            }

            if (Convert.ToInt32(doForm.doRS.GetFieldVal("SI__SHARESTATE", 2)) < 2)
            {
                sMessage = "A journal Activity cannot be created because this Quote is not shared.";
                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);
                doForm.oVar.SetVar("Quote_CreateActLog_Ran", "1");
                // Don't return out of formonsave. This message will be displayed at end of form save.
                doForm.oVar.SetVar("ContinueSave", "1");
                par_doCallingObject = doForm;
                return true;
            } // If doForm.GetMode() = "CREATION" Then Return True					'The record must be in edit mode so we have its ID for links to it.

            doForm.oVar.SetVar("Quote_CreateActLog_Ran", "1");

            // sWork = doForm.doRS.GetFieldVal("MMO_Journal")
            // CS 1/26/10: Instead of getting the journal field get the variable from MessageBoxEvent so that we have the
            // hard returns in the journal field
            sWork = Convert.ToString(doForm.oVar.GetVar("JournalWithHardReturns"));
            // CS 2/4/10
            if (sWork == "")
            {
                par_doCallingObject = doForm;
                return true; // We didn't hit MessageBoxEvent from entering a journal note.
            }

            // CREATEACTLOG:

            clArray doLink = new clArray();
            clRowSet doNew = new clRowSet("AC", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);



            lWork = Strings.Len(sWork) - Convert.ToInt32(doForm.oVar.GetVar("lLenJournal"));
            sNotes = Strings.Left(sWork, lWork);
            sNotes = sNotes + "== Created from Quote '" + Convert.ToString(doForm.doRS.GetFieldVal("SYS_Name")) + "'";
            doNew.SetFieldVal("MMO_NOTES", sNotes);
            // CS goP.TraceLine("Setting field LNK_Involves_US '" & goP.GetMe("ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_Involves_US", goP.GetMe("ID"));
            // goP.TraceLine("Setting field MLS_Status '" & 1 & "'", "", sProc)
            doNew.SetFieldVal("MLS_Status", 1, 2);     // Completed
            doNew.SetFieldVal("DTE_STARTTIME", "Today");
            doNew.SetFieldVal("DTE_ENDTIME", "Today");
            // goP.TraceLine("Setting field TME_STARTTIME '" & "<%Now%>" & "'", "", sProc)
            doNew.SetFieldVal("TME_STARTTIME", "Now");
            // goP.TraceLine("Setting field TME_ENDTIME '" & "<%Now%>" & "'", "", sProc)
            doNew.SetFieldVal("TME_ENDTIME", "Now");
            // goP.TraceLine("Setting field DTE_STARTTIME '" & "<%Today%>" & "'", "", sProc)
            // doNew.SetFieldVal("DTE_STARTTIME", "Today")
            // goP.TraceLine("Setting field DTE_ENDTIME '" & "<%Today%>" & "'", "", sProc)
            // CS doNew.SetFieldVal("DTE_ENDTIME", doForm.doRS.GetFieldVal("DTE_Time"))
            // doNew.SetFieldVal("DTE_ENDTIME", "Today")
            // goP.TraceLine("Setting field LNK_CreditedTo_US '" & goP.GetMe("ID") & "'", "", sProc)
            doNew.SetFieldVal("LNK_CreditedTo_US", goP.GetMe("ID"));
            doNew.SetFieldVal("LNK_RELATED_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN", 2), 2);
            doNew.SetFieldVal("LNK_RELATED_CO", doForm.doRS.GetFieldVal("LNK_TO_CO", 2), 2);
            // goP.TraceLine("Setting field MLS_TYPE '" & 31 & "'", "", sProc)
            // doNew.SetFieldVal("MLS_TYPE", 31, 2)      'Journal
            // SKO TKT:#1006 ********
            // Sets type to MLS_JOURNALTYPE if not 0
            string sJournalType = Convert.ToString(doForm.oVar.GetVar("QT_JournalType"));
            clList goACType = new clList();
            if (sJournalType != "<Make selection>")
            {
                if (int.Parse(goACType.LReadSeek("AC:TYPE", "VALUE", sJournalType)) != 0)
                    doNew.SetFieldVal("MLS_TYPE", sJournalType, 1);
                else
                    doNew.SetFieldVal("MLS_TYPE", 31, 2);// Journal
            }
            else
                doNew.SetFieldVal("MLS_TYPE", 31, 2);// Journal

            // SGR TKT:# 1164 13072016 
            string sPurpose = Convert.ToString(doForm.doRS.GetFieldVal("MLS_PURPOSE", 1));
            if (sPurpose != "<Make selection>")
                doNew.SetFieldVal("MLS_PURPOSE", sPurpose, 1);

            // doNew.SetFieldVal("MLS_TYPE", 31, 2)      'Journal
            // Reset vars here?
            doForm.oVar.SetVar("QT_JournalType", "");
            // goP.TraceLine("Setting field MMO_HISTORY '" & goTR.WriteLogLine(doForm.doRS.GetFieldVal("MMO_HISTORY"), "Created.") & "'", "", sProc)
            doNew.SetFieldVal("MMO_HISTORY", goTR.WriteLogLine(Convert.ToString(doNew.GetFieldVal("MMO_HISTORY")), "Created."));
            // goP.TraceLine("Setting field LNK_RELATED_QT '" & doForm.GetRecordID() & "'", "", sProc)
            doNew.SetFieldVal("LNK_RELATED_QT", doForm.GetRecordID());

            // goP.TraceLine("About to commit.", "", sProc)
            if (doNew.Commit() != 1)
            {
                doNew = null;
                doLink = null;

                string sError = goErr.GetLastError();
                sMessage = goErr.GetLastError("MESSAGE");

                doForm.oVar.SetVar("ScriptMessages", doForm.oVar.GetVar("ScriptMessages") + sMessage + Constants.vbCrLf);

                goLog.Log("Quote_CreateActLog", Convert.ToString(doForm.oVar.GetVar("ScriptMessages")), 1, false, true);
                par_doCallingObject = doForm;
                return false;
            }


            doNew = null;
            doLink = null;

            par_doCallingObject = doForm;
            return true;
        }

        public bool RHTESTSCRIPT(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            // rh test script for toolbar automater and alerts
            ClUI goui = new ClUI();
            //goui.Initialize();
            goui.AddAlert("Process billing reports", clC.SELL_ALT_OPENDESKTOP, "DSK_2005042614562492255MAR 00002XX", clC.SELL_USER_ALL);
            goui.AddAlert("New Message from 'Rick'", clC.SELL_ALT_OPENRECORD, "c0023d42-55b3-473d-4d53-9752008b6c04", clC.SELL_USER_ALL);

            return true;
        }

        // SGR ******** TKT:#1147 Filling state on change of LNK_RELATED_SE 
        public bool CO_FormControlOnChange_LNK_RELATED_SE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/8/07 Changed time stamp to local time, no label.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            doForm.doRS.SetFieldVal("TXT_STATEMAILING", doForm.doRS.GetFieldVal("LNK_RELATED_SE%%TXT_STATECODE"));


            par_doCallingObject = doForm;
            return true;
        }
        // SGR ******** TKT:#1147 Filling country on change of LNK_RELATED_CY
        public bool CO_FormControlOnChange_LNK_RELATED_CY_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 11/8/07 Changed time stamp to local time, no label.
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            doForm.doRS.SetFieldVal("TXT_COUNTRYMAILING", doForm.doRS.GetFieldVal("LNK_RELATED_CY%%TXT_COUNTRYNAME"));

            par_doCallingObject = doForm;
            return true;
        }
        public bool CN_FormControlOnChange_LNK_RELATED_JF_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sJobfunction;

            // SKO 09022016 TKT#1230 : I added chk_primarycontact. On save, if related job function is Primary Contact, check this box.
            sJobfunction = Convert.ToString(doForm.doRS.GetFieldVal("LNK_RELATED_JF%%TXT_JOBFUNCNAME"));
            sJobfunction = sJobfunction + Constants.vbCrLf;
            if (Strings.InStr(sJobfunction, "Primary Contact" + Constants.vbCrLf) > 0)
                doForm.doRS.SetFieldVal("chk_primarycontact", 1, 2);

            par_doCallingObject = doForm;
            return true;
        }

        public bool TD_FormControlOnChange_CHK_COMPLETED_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //SB 12182019 Tkt#3257 Whenever Compeleted id checked DateCompleted should fill with current date.
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_COMPLETED", 2)) == 1)
            {
                doForm.doRS.SetFieldVal("DTT_DATECOMPLETED", DateTime.Now, 2);
            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_Quote Template TnC Revised_Draft.docx";
                }
            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "cus_Quote Template TnC Revised.docx";
                }
            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }

            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);
                       
                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEInclude", 2));


            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            // rsQL.SetFieldVal("CUR_Cost", curCost);

            rsQL.SetFieldVal("SR__Qty", dQty);
            rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);

            //rsQL.SetFieldVal("TXT_Model", sModelText);      
            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }
            //}


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }
        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
            }

        }

        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }

        public bool QT_MoveQuoteLine_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            par_bRunNext = false;

            string sDirection = "";
            string sLtGt = "";
            string sFirstLast = "";
            string sMoveSortDir = "";

            string sRecID = "";
            string sQuoteID = "";
            string sSelLineNO = "";
            string sMoveLineNO = "";
            double dSelLineNO = 0;
            double dMoveLineNO = 0;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot edit Quote Lines.");
                return true;
            }

            if (par_s3.ToUpper() == "UP")
            {
                sDirection = "up";
                sLtGt = "<";
                sFirstLast = "first";
                sMoveSortDir = "desc";
            }
            else if (par_s3.ToUpper() == "DOWN")
            {
                sDirection = "down";
                sLtGt = ">";
                sFirstLast = "last";
                sMoveSortDir = "asc";
            }
            else
            {
                return false;
            }

            sQuoteID = doForm.doRS.GetFieldVal("GID_ID").ToString();
            sRecID = doForm.GetLinkSelection("LNK_Connected_QL");

            if (string.IsNullOrEmpty(sRecID))
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }

            clRowSet doQLSelRS = new clRowSet("QL", clC.SELL_EDIT, "GID_ID=" + sRecID, "", "*,**", 1, par_bBypassValidation: true);
            if (doQLSelRS.GetFirst() == 0)
            {
                doForm.MessageBox("Please select a Quote Line to move.");
                par_doCallingObject = doForm;
                return false;
            }
            sSelLineNO = doQLSelRS.GetFieldVal("SR__LineNo").ToString();
            dSelLineNO = Convert.ToDouble(doQLSelRS.GetFieldVal("SR__LineNo"));

            clRowSet doQLMoveRS = new clRowSet("QL", clC.SELL_EDIT, ("LNK_IN_QT=" + sQuoteID + " AND SR__LineNo" + sLtGt + sSelLineNO), "SR__LineNo " + sMoveSortDir, "*,**", 1, par_bBypassValidation: true);
            if (doQLMoveRS.GetFirst() == 0)
            {
                doForm.MessageBox("This is the " + sFirstLast + " Qoute Line. Please select another Qoute Line to move " + sDirection + ".");
                par_doCallingObject = doForm;
                return false;
            }
            sMoveLineNO = doQLMoveRS.GetFieldVal("SR__LineNo").ToString();
            dMoveLineNO = Convert.ToDouble(doQLMoveRS.GetFieldVal("SR__LineNo"));

            doQLSelRS.SetFieldVal("SR__LineNo", dMoveLineNO, 2);
            doQLMoveRS.SetFieldVal("SR__LineNo", dSelLineNO, 2);

            if (doQLSelRS.Commit() == 0 || doQLMoveRS.Commit() == 0)
            {
                doForm.MessageBox("Could not save the Quote Lines. Please reopen the quote and try again.");
                par_doCallingObject = doForm;
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");
            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;
        }

        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);

            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {

                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }

            par_doCallingObject = doForm;
            return true;
        }


        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_FOR_MO,LNK_RELATED_PD,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("LNK_FOR_MO", MO_Gid);
            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);



            //This will generate line no's in mobile.
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }

            par_doCallingObject = doRS;

            return true;
        }

        public bool OP_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;


            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                doRS.SetFieldVal("CUR_VALUE", curValue);
                doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

            }
            par_doCallingObject = doRS;
            return true;
        }
        public bool Opp_CalcProbability_post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sSalesProcess = "0", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: doForm.
            //par_doArray: Unused.
            //par_sSalesProcess: 1 or 0 (default): if 1, use checkboxes in the Sales Process tab
            //                           to calculate probability %, else just calculate value and value index.
            //                   2 to calc both
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = null;
            clRowSet doRS1 = null;

           
            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS1.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doRS1.SetFieldVal("CUR_VALUE", curValue);
                    doRS1.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doRS1;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                    doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doForm;
            }

            return true;



        }
        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
           

            //par_doCallingObject = doForm;
            return true;

        }

        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }



            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));

            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECLOSED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            //doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
                rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("Cur_UnitPrice", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }



    }
}