﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using Selltis.MVC.Models;
using System.Data;
using System.IO;
using Selltis.Core;

namespace Selltis.MVC.Controllers
{
    public class DownloadsController : Controller
    {
        //
        // GET: /Downloads/
        public PartialViewResult Download()
        {
            var dir = new System.IO.DirectoryInfo(Server.MapPath("~/Downloads/"));
             System.IO.FileInfo[] fileNames = dir.GetFiles("*.*");
            

            List<string> items = new List<string>();
            foreach (var file in fileNames)
            {
                items.Add(file.Name);
            }
            @ViewBag.download = "Downloads";
            @ViewBag.items = items;
            if (Util.GetSessionValue("DesktopId") != null)
            {
                if (Util.GetSessionValue("DesktopId").ToString() != string.Empty)
                {
                    ViewBag.SelectedDesktopID = Util.GetSessionValue("DesktopId");
                    ViewBag.FolderId = Util.GetSessionValue("FolderID");
                    ViewBag.HistoryKey = Util.GetSessionValue("LastDesktopHistoryKey");
                }
            }

            string prevUrl = System.Web.HttpContext.Current.Request.UrlReferrer.ToString();

            //Util.GetVar("LatestFormKey") 

            if (prevUrl.ToUpper().Contains("CREATEFORM/CREATEFORM"))
            {
                if (prevUrl.ToUpper().Contains("/ID/"))
                {
                    if (Util.GetSessionValue("LatestFormKey") != null)
                    {
                        if (!string.IsNullOrEmpty(Util.GetSessionValue("LatestFormKey").ToString()))
                        {
                            string[] aformKeyElements = Util.GetSessionValue("LatestFormKey").ToString().Split('|');
                            prevUrl = prevUrl.Replace("/ID/", "/" + aformKeyElements[1] + "/");
                            Util.SetSessionValue("CurrentLastFormKey", Util.GetSessionValue("LatestFormKey").ToString());
                        }
                    }
                }
            }           
            // when we came from createform
            ViewBag.PreviousUrl = prevUrl;

            return PartialView("~/Views/Downloads/Download.cshtml",LoadList());
        }

        public FileResult File(string Filename)
        {
            if (Filename.Substring(0, 4) != "http")
            {
                return File(Filename, System.Net.Mime.MediaTypeNames.Application.Octet, Filename.Substring(12));
            }
            else
            {
                using (WebClient wc = new WebClient())
                {
                    var byteArr = wc.DownloadData(Filename);
                    return File(byteArr, "Application/pdf", Filename.Substring(19));
                }
            }
        }

        public List<DownloadsList> LoadList()
        {
            List<DownloadsList> downloadslist = new List<DownloadsList>();
            DataSet ds = new DataSet();
            ds = GetDownloadDataSet();
            downloadslist = (from DataRow dr in ds.Tables[0].Rows
                             select new DownloadsList()
                             {
                                 filename = dr["filename"].ToString(),
                                 path = dr["path"].ToString(),
                                 icon = dr["icon"].ToString(),
                                 description = dr["description"].ToString()
                             }).ToList();

            return downloadslist;
        }
        public DataSet GetDownloadDataSet()
        {
            string myXMLfile = Server.MapPath("~/App_Data/download.xml");
            DataSet ds = new DataSet();
            var fsReadXml = new FileStream(myXMLfile, FileMode.Open, FileAccess.Read);
            ds.ReadXml(fsReadXml);
            fsReadXml.Close();
            fsReadXml.Dispose();
            return ds;
        }

    }
}