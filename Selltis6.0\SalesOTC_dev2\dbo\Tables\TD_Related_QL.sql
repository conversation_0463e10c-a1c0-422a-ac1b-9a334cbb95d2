﻿CREATE TABLE [dbo].[TD_Related_QL] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_ToDo_Related_QuotLine_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [G<PERSON>_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_QL] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_Related_QL] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_QL_Related_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_Related_QL] FOREIGN KEY ([GID_QL]) REFERENCES [dbo].[QL] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_Related_QL] NOCHECK CONSTRAINT [LNK_QL_Related_TD];


GO
ALTER TABLE [dbo].[TD_Related_QL] NOCHECK CONSTRAINT [LNK_TD_Related_QL];


GO
CREATE CLUSTERED INDEX [IX_TD_Related_QL]
    ON [dbo].[TD_Related_QL]([GID_QL] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QL_Related_TD]
    ON [dbo].[TD_Related_QL]([GID_TD] ASC);

