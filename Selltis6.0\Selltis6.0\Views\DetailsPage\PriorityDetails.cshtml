﻿@model Selltis.MVC.Models.PriorityDetails
@{
    ViewBag.Title = "PriorityDetails";

    Layout = "~/Views/Shared/_Layout.cshtml";
}


<link rel="stylesheet" href="~/Content/fonts/entypo/css/entypo.css">
<link rel="stylesheet" href="~/Content/component.css">

<style>
    element.style {
    }

    * {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
    }

    .num-col {
        min-width: 30px;
        width: 30px;
        text-align: right;
        display: inline-block;
    }

    user agent stylesheet i {
        font-style: italic;
    }

    .txt-light-gray {
        color: #949494;
    }

    .edit-btn {
        font-size: 12px;
    }

    a {
        color: #373e4a;
    }

    a {
        color: #373e4a;
        text-decoration: none;
    }

    user agent stylesheet a:-webkit-any-link {
        color: -webkit-link;
        cursor: pointer;
    }

    body {
        font-size: 14px;
    }

    body {
        font-family: "Helvetica Neue", Helvetica, "Noto Sans", sans-serif;
    }

    body {
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        font-size: 12px;
        line-height: 1.42857143;
        color: #949494;
        background-color: #fff;
    }

    html {
        font-size: 10px;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    html {
        font-family: sans-serif;
        -ms-text-size-adjust: 100%;
        -webkit-text-size-adjust: 100%;
    }



    .d-flex {
        display: flex !important;
    }

    .flex-column {
        flex-direction: column;
    }

    .flex-row {
        flex-direction: row;
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    .justify-content-center {
        justify-content: center !important;
    }

    .justify-content-end {
        justify-content: flex-end !important;
    }

    .justify-content-around {
        justify-content: space-around !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .align-items-start {
        align-items: flex-start !important;
    }

    .align-items-end {
        align-items: flex-end !important;
    }

    .align-items-stretch {
        align-items: stretch !important;
    }

    .flex-grow-1 {
        flex-grow: 1;
    }

    .flex-grow-2 {
        flex-grow: 2;
    }

    .flex-grow-3 {
        flex-grow: 3;
    }

    .flex-grow-4 {
        flex-grow: 4;
    }

    .flex-grow-5 {
        flex-grow: 5;
    }

    .flex-grow-6 {
        flex-grow: 6;
    }

    .flex-shrink-1 {
        flex-grow: 1;
    }

    .flex-shrink-2 {
        flex-grow: 2;
    }

    .flex-shrink-3 {
        flex-grow: 3;
    }

    .flex-shrink-4 {
        flex-grow: 4;
    }

    .flex-shrink-5 {
        flex-grow: 5;
    }

    .flex-shrink-6 {
        flex-grow: 6;
    }

    .ml-auto {
        margin-left: auto !important;
    }

    .mr-auto {
        margin-right: auto !important;
    }

    .mt-auto {
        margin-top: auto !important;
    }

    .mb-auto {
        margin-bottom: auto !important;
    }

    .my-auto {
        margin-top: auto !important;
        margin-bottom: auto !important;
        cursor: pointer;
    }

    .mx-auto {
        margin-left: auto !important;
        margin-right: auto !important;
    }

    .mw-200px {
        max-width: 200px;
        width: 100%;
    }

    .mw-400 {
        max-width: 400px;
        width: 100%;
    }

    .mw-70p {
        max-width: 70%;
        width: 100%;
    }

    .mw-80p {
        max-width: 80%;
        width: 100%;
    }

    .minw-300 {
        min-width: 300px;
        width: 100%;
    }

    .mr-5 {
        margin-right: 5px;
    }

    .mr-10 {
        margin-right: 10px;
    }

    .mr-15 {
        margin-right: 15px;
    }

    .ml-5 {
        margin-left: 5px;
    }

    .ml-10 {
        margin-left: 10px;
    }

    .ml-15 {
        margin-left: 15px;
    }

    .ml-20 {
        margin-left: 20px;
    }

    .mt-20 {
        margin-top: 20px;
    }

    .mt-25 {
        margin-top: 25px;
    }

    .mt-30 {
        margin-top: 30px;
    }

    .my-1 {
        margin-top: .25rem;
        margin-bottom: .25rem;
    }

    .my-2 {
        margin-top: .5rem;
        margin-bottom: .5rem;
    }

    .my-3 {
        margin-top: 1rem;
        margin-bottom: 1rem;
    }

    .my-4 {
        margin-top: 1.25rem;
        margin-bottom: 1.25rem;
    }

    .my-5 {
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    .btn-favorite {
        color: #949494;
        original gray color: #c3c3c3;
        width: 30px;
    }

    .page-content-container {
        padding: 5px 15px 15px 15px;
        border-radius: 3px;
        background-color: white;
    }

    .m-0 {
        margin: 0px !important;
    }

    .mt-0 {
        margin-top: 0px !important;
    }

    .mt-5 {
        margin-top: 5px !important;
    }

    .mt-10 {
        margin-top: 10px !important;
    }

    .mt-15 {
        margin-top: 15px !important;
    }

    .mt-20 {
        margin-top: 20px !important;
    }

    .mt-25 {
        margin-top: 25px !important;
    }

    .mt-30 {
        margin-top: 30px !important;
    }

    .mt-40 {
        margin-top: 40px !important;
    }

    .mt-44 {
        margin-top: 44px !important;
    }

    .mt-50 {
        margin-top: 50px !important;
    }

    .mt-60 {
        margin-top: 60px !important;
    }

    .mt-80 {
        margin-top: 80px !important;
    }

    .mb-0 {
        margin-bottom: 0px !important;
    }

    .mb-5 {
        margin-bottom: 5px !important;
    }

    .mb-10 {
        margin-bottom: 10px !important;
    }

    .mb-15 {
        margin-bottom: 15px !important;
    }

    .mb-20 {
        margin-bottom: 20px !important;
    }

    .mb-25 {
        margin-bottom: 25px !important;
    }

    .mb-30 {
        margin-bottom: 30px !important;
    }

    .mb-40 {
        margin-bottom: 40px !important;
    }

    .mb-50 {
        margin-bottom: 50px !important;
    }

    .mb-60 {
        margin-bottom: 60px !important;
    }

    .mb-80 {
        margin-bottom: 80px !important;
    }

    .mr-5 {
        margin-right: 5px !important;
    }

    .mr-10 {
        margin-right: 10px !important;
    }

    .mr-15 {
        margin-right: 15px !important;
    }

    .p-0 {
        padding: 0px !important;
    }

    .p-10 {
        padding: 10px;
    }

    .p-15 {
        padding: 15px;
    }

    .p-20 {
        padding: 20px;
    }

    .pt-0 {
        padding-top: 0px !important;
    }

    .pt-5 {
        padding-top: 5px !important;
    }

    .pt-10 {
        padding-top: 10px !important;
    }

    .pt-15 {
        padding-top: 15px !important;
    }

    .pt-20 {
        padding-top: 20px !important;
    }

    .pt-25 {
        padding-top: 25px !important;
    }

    .pt-30 {
        padding-top: 30px !important;
    }

    .pt-40 {
        padding-top: 40px !important;
    }

    .pt-50 {
        padding-top: 50px !important;
    }

    .pt-60 {
        padding-top: 60px !important;
    }

    .pt-80 {
        padding-top: 80px !important;
    }

    .pt-82 {
        padding-top: 116px !important;
    }


    .pb-0 {
        padding-bottom: 0px !important;
    }

    .pb-5 {
        padding-bottom: 5px !important;
    }

    .pb-10 {
        padding-bottom: 10px !important;
    }

    .pb-15 {
        padding-bottom: 15px !important;
    }

    .pb-20 {
        padding-bottom: 20px !important;
    }

    .pb-25 {
        padding-bottom: 25px !important;
    }

    .pb-30 {
        padding-bottom: 30px !important;
    }

    .pb-40 {
        padding-bottom: 40px !important;
    }

    .pb-50 {
        padding-bottom: 50px !important;
    }

    .pb-60 {
        padding-bottom: 60px !important;
    }

    .pb-80 {
        padding-bottom: 80px !important;
    }

    .pl-20 {
        padding-left: 20px !important;
    }

    .pr-20 {
        padding-right: 20px !important;
    }

    .pr-15 {
        padding-right: 15px !important;
    }

    .pr-10 {
        padding-right: 10px !important;
    }

    .pl-280 {
        padding-left: 240px !important;
    }

    .pl-65 {
        padding-left: 65px !important;
    }


    .pr-5 {
        padding-right: 5px !important;
    }

    .pr-10 {
        padding-right: 10px !important;
    }

    .pr-15 {
        padding-right: 15px !important;
    }

    .px-5 {
        padding-left: 5px !important;
        padding-right: 5px !important;
    }

    .px-10 {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }

    .px-15 {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    .px-50 {
        padding-left: 50px !important;
        padding-right: 50px !important;
    }

    .px-10p {
        padding-left: 10% !important;
        padding-right: 10% !important;
    }

    .py-5 {
        padding-top: 5px !important;
        padding-bottom: 5px !important;
    }

    .py-10 {
        padding-top: 10px !important;
        padding-bottom: 10px !important;
    }

    .py-15 {
        padding-top: 15px !important;
        padding-bottom: 15px !important;
    }

    .lh-100 {
        line-height: 100%;
    }

    .lh-120 {
        line-height: 120%;
    }

    .lh-140 {
        line-height: 140%;
    }

    .lh-160 {
        line-height: 160;
    }

    .z-index-10 {
        z-index: 10;
    }

    .z-index-20 {
        z-index: 20;
    }

    .z-index-30 {
        z-index: 30;
    }

    .z-index-40 {
        z-index: 40;
    }

    .z-index-50 {
        z-index: 50;
    }

    .border-right {
        border-right: 1px solid #ebebec;
    }

    .border-left {
        border-left: 1px solid #ebebec;
    }

    .border-top {
        border-top: 1px solid #ebebec;
    }

    .border-bottom {
        border-bottom: 1px solid #ebebec;
    }

    .d-inline {
        display: inline;
    }

    .d-block {
        display: block;
    }

    .d-inline-block {
        display: inline-block;
    }

    .scrollable {
        overflow-x: hidden;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        height: 100%;
    }

    .disable-button {
        pointer-events: none;
    }

    .full-width {
        width: 95%;
    }

    .z-index-1 {
        z-index: 1;
    }

    .rounded-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 50px;
        height: 50px;
        background-color: lightgreen;
        border-radius: 50px;
        background-color: #373e4a;
        color: white;
    }

    .color-danger {
        color: #cc2424 !important;
    }

    .no-border {
        border: 0 !important;
    }

    .table.no-borders th {
        border: 0;
    }

    .table.bold-header th {
        font-weight: bold;
        padding: 4px 8px;
        color: #00366c;
    }

    .font-10 {
        font-size: 10px !important;
    }

    .font-11 {
        font-size: 11px !important;
    }

    .font-12 {
        font-size: 12px !important;
    }

    .font-13 {
        font-size: 13px !important;
    }

    .font-14 {
        font-size: 14px !important;
    }

    .font-15 {
        font-size: 15px !important;
    }

    .font-16 {
        font-size: 16px !important;
    }

    .gray-text {
        color: #8d929a;
    }


    /*Page content css*/
    .social-icons {
        font-size: 4rem;
    }

        .social-icons li {
            display: inline;
            float: left;
        }

            .social-icons li i:before {
                margin: 0px;
            }

    .profile-name aside.user-thumb img {
        max-width: 70px;
    }

    .contacts-all-btn-icon {
        font-size: 4rem;
    }

    /*Theme Fixes*/
    .profile-env section.profile-feed .profile-stories article.story .user-thumb {
        width: 10%;
    }

    .profile-env section.profile-feed .profile-stories article.story .story-content {
        width: 90%;
    }

    .login-page .btn-login:focus, .login-page .btn-login:active, .login-page .btn-login:hover {
        color: #51555d !important;
    }

    .sui-hover {
        font-size: 12px;
    }

    .col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xl-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xl-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xl-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xl-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xl-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xl-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xl-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xl-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xl-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xl-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xl-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12, .col-xl-12 {
        position: relative;
        min-height: 1px;
        padding-left: 15px;
        padding-right: 15px;
    }

    .border-light-gray {
        border: 1px solid #f5f5f6;
    }

    .name-title-info-list .name-title-info-item, .border-bottom-light-gray {
        border-bottom: 1px solid #f5f5f6;
    }

        .name-title-info-list .name-title-info-item:last-child {
            border-bottom: 0;
        }

    .br-3 {
        border-radius: 3px;
    }

    .box-shadow {
        -webkit-box-shadow: 2px 2px 10px 5px rgba(0,0,0,0.07);
        -moz-box-shadow: 2px 2px 10px 5px rgba(0,0,0,0.07);
        box-shadow: 2px 2px 10px 5px rgba(0,0,0,0.07);
    }

    .name-title-info-list {
        padding: 5px 15px;
    }

    .name-title-info-item {
        padding-top: 5px !important;
        padding-bottom: 5px !important;
    }

    .name-title {
        font-size: 14px;
        color: #373E4A;
    }

    .sub-phone-email {
        font-size: 10px;
        color: #949494;
    }

    /*}*/

    h1, h2, h3, h4, h5, h6, button, footer.main {
        color: #00366c;
    }

    h4 {
        font-size: 16px;
    }

    h5 {
        font-size: 14px;
    }
    /*header css*/
    header.logo-env .logo a img {
        width: 150px;
    }

    header.logo-env {
        padding: 15px;
    }

    .timeline {
        border: 1px solid #f5f5f6;
        border-radius: 3px;
    }

    ul.cbp_tmtimeline > li .cbp_tmlabel, ul.cbp_tmtimeline > li .cbp_tmlabel.empty {
        margin-bottom: 0px !important;
    }

    .cbp_tmtimeline > li .cbp_tmlabel {
        background: #F5F5F7 !important;
        border-right-color: #F5F5F7 !important;
        /*padding: 1.2rem 1.6rem;*/
    }

    .cbp_tmtimeline > li .cbp_tmtime {
    }

    .cbp_tmtimeline.timeline-modified {
    }

        .cbp_tmtimeline.timeline-modified:before {
            display: none;
        }

        .cbp_tmtimeline.timeline-modified > li .cbp_tmlabel {
            /*margin: 0 0 15px 10px;*/
            margin: 0 0 0px 0px;
        }

    .company-details label {
        color: #00366c;
        margin-bottom: 0px;
    }

    /*.company-details {
        label, p

    {
        font-size: 12px;
    }

    }*/

    .text-decoration-none {
        text-decoration: none !important;
    }

    .profile-name {
        color: #fff;
    }

    .tile-stats {
        position: relative;
        display: block;
        background: #303641;
        padding: 20px;
        margin-bottom: 12px;
        overflow: hidden;
        -webkit-border-radius: 5px;
        -webkit-background-clip: padding-box;
        -moz-border-radius: 5px;
        -moz-background-clip: padding;
        border-radius: 5px;
        background-clip: padding-box;
        -webkit-transition: all 300ms ease-in-out;
        -moz-transition: all 300ms ease-in-out;
        -o-transition: all 300ms ease-in-out;
        transition: all 300ms ease-in-out;
    }

        .tile-stats:hover {
            background: #252a32;
        }

        .tile-stats .icon {
            color: rgba(0, 0, 0, 0.1);
            position: absolute;
            right: 5px;
            bottom: 5px;
            z-index: 1;
        }

            .tile-stats .icon i {
                font-size: 100px;
                line-height: 0;
                margin: 0;
                padding: 0;
                vertical-align: bottom;
            }

                .tile-stats .icon i:before {
                    margin: 0;
                    padding: 0;
                    line-height: 0;
                }

        .tile-stats .num,
        .tile-stats h3,
        .tile-stats p {
            position: relative;
            color: #fff;
            z-index: 5;
            margin: 0;
            padding: 0;
        }

        .tile-stats .num {
            font-size: 38px;
            font-weight: bold;
        }

        .tile-stats h3 {
            font-size: 18px;
            margin-top: 5px;
        }

        .tile-stats p {
            font-size: 11px;
            margin-top: 5px;
        }

        .tile-stats.tile-red {
            background: #f56954;
        }

            .tile-stats.tile-red:hover {
                background: #f4543c;
            }

            .tile-stats.tile-red .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-red .num,
            .tile-stats.tile-red h3,
            .tile-stats.tile-red p {
                color: #fff;
            }

        .tile-stats.tile-green {
            background: #00a65a;
        }

            .tile-stats.tile-green:hover {
                background: #008d4c;
            }

            .tile-stats.tile-green .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-green .num,
            .tile-stats.tile-green h3,
            .tile-stats.tile-green p {
                color: #fff;
            }

        .tile-stats.tile-blue {
            background: #0073b7;
        }

            .tile-stats.tile-blue:hover {
                background: #00639e;
            }

            .tile-stats.tile-blue .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-blue .num,
            .tile-stats.tile-blue h3,
            .tile-stats.tile-blue p {
                color: #fff;
            }

        .tile-stats.tile-aqua {
            background: #00c0ef;
        }

            .tile-stats.tile-aqua:hover {
                background: #00acd6;
            }

            .tile-stats.tile-aqua .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-aqua .num,
            .tile-stats.tile-aqua h3,
            .tile-stats.tile-aqua p {
                color: #fff;
            }

        .tile-stats.tile-cyan {
            background: #00b29e;
        }

            .tile-stats.tile-cyan:hover {
                background: #009987;
            }

            .tile-stats.tile-cyan .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-cyan .num,
            .tile-stats.tile-cyan h3,
            .tile-stats.tile-cyan p {
                color: #fff;
            }

        .tile-stats.tile-purple {
            background: #ba79cb;
        }

            .tile-stats.tile-purple:hover {
                background: #b167c4;
            }

            .tile-stats.tile-purple .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-purple .num,
            .tile-stats.tile-purple h3,
            .tile-stats.tile-purple p {
                color: #fff;
            }

        .tile-stats.tile-pink {
            background: #ec3b83;
        }

            .tile-stats.tile-pink:hover {
                background: #ea2474;
            }

            .tile-stats.tile-pink .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-pink .num,
            .tile-stats.tile-pink h3,
            .tile-stats.tile-pink p {
                color: #fff;
            }

        .tile-stats.tile-orange {
            background: #ffa812;
        }

            .tile-stats.tile-orange:hover {
                background: #f89d00;
            }

            .tile-stats.tile-orange .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-orange .num,
            .tile-stats.tile-orange h3,
            .tile-stats.tile-orange p {
                color: #fff;
            }

        .tile-stats.tile-brown {
            background: #6c541e;
        }

            .tile-stats.tile-brown:hover {
                background: #584418;
            }

            .tile-stats.tile-brown .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-brown .num,
            .tile-stats.tile-brown h3,
            .tile-stats.tile-brown p {
                color: #fff;
            }

        .tile-stats.tile-plum {
            background: #701c1c;
        }

            .tile-stats.tile-plum:hover {
                background: #5c1717;
            }

            .tile-stats.tile-plum .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-plum .num,
            .tile-stats.tile-plum h3,
            .tile-stats.tile-plum p {
                color: #fff;
            }

        .tile-stats.tile-gray {
            background: #f5f5f5;
        }

            .tile-stats.tile-gray:hover {
                background: #e8e8e8;
            }

            .tile-stats.tile-gray .icon {
                color: rgba(0, 0, 0, 0.1);
            }

            .tile-stats.tile-gray .num,
            .tile-stats.tile-gray h3,
            .tile-stats.tile-gray p {
                color: #8f8f8f;
            }

        .tile-stats.tile-white {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white .num,
            .tile-stats.tile-white h3,
            .tile-stats.tile-white p {
                color: #303641;
            }

            .tile-stats.tile-white:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-red {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-red:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-red .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-red .num,
            .tile-stats.tile-white-red h3,
            .tile-stats.tile-white-red p {
                color: #f56954;
            }

            .tile-stats.tile-white-red:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-green {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-green:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-green .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-green .num,
            .tile-stats.tile-white-green h3,
            .tile-stats.tile-white-green p {
                color: #00a65a;
            }

            .tile-stats.tile-white-green:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-blue {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-blue:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-blue .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-blue .num,
            .tile-stats.tile-white-blue h3,
            .tile-stats.tile-white-blue p {
                color: #0073b7;
            }

            .tile-stats.tile-white-blue:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-aqua {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-aqua:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-aqua .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-aqua .num,
            .tile-stats.tile-white-aqua h3,
            .tile-stats.tile-white-aqua p {
                color: #00c0ef;
            }

            .tile-stats.tile-white-aqua:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-cyan {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-cyan:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-cyan .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-cyan .num,
            .tile-stats.tile-white-cyan h3,
            .tile-stats.tile-white-cyan p {
                color: #00b29e;
            }

            .tile-stats.tile-white-cyan:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-purple {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-purple:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-purple .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-purple .num,
            .tile-stats.tile-white-purple h3,
            .tile-stats.tile-white-purple p {
                color: #ba79cb;
            }

            .tile-stats.tile-white-purple:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-pink {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-pink:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-pink .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-pink .num,
            .tile-stats.tile-white-pink h3,
            .tile-stats.tile-white-pink p {
                color: #ec3b83;
            }

            .tile-stats.tile-white-pink:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-orange {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-orange:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-orange .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-orange .num,
            .tile-stats.tile-white-orange h3,
            .tile-stats.tile-white-orange p {
                color: #ffa812;
            }

            .tile-stats.tile-white-orange:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-brown {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-brown:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-brown .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-brown .num,
            .tile-stats.tile-white-brown h3,
            .tile-stats.tile-white-brown p {
                color: #6c541e;
            }

            .tile-stats.tile-white-brown:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-plum {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-plum:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-plum .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-plum .num,
            .tile-stats.tile-white-plum h3,
            .tile-stats.tile-white-plum p {
                color: #701c1c;
            }

            .tile-stats.tile-white-plum:hover {
                background-color: #fafafa;
            }

        .tile-stats.tile-white-gray {
            background: #fff;
            border: 1px solid #ebebeb;
        }

            .tile-stats.tile-white-gray:hover {
                background: #f2f2f2;
            }

            .tile-stats.tile-white-gray .icon {
                color: #f2f2f2;
            }

            .tile-stats.tile-white-gray .num,
            .tile-stats.tile-white-gray h3,
            .tile-stats.tile-white-gray p {
                color: #8f8f8f;
            }

            .tile-stats.tile-white-gray:hover {
                background-color: #fafafa;
            }

        .tile-stats.stat-tile {
            padding: 0px;
            height: 155px;
            border: none !important;
            box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
        }

        .tile-stats.tile-neon-red {
            background: #ff4e50;
        }

        .tile-stats.stat-tile h3 {
            padding: 20px 20px 0px 20px;
        }

        .tile-stats.stat-tile p {
            padding: 0px 20px 20px 20px;
            margin-bottom: 20px;
        }

    .tile-reg-users {
        background: #123568;
        cursor: pointer;
    }

    .tile-stats {
        min-height: 149px;
    }

    .tile-aopotentials {
        background: #ECF1F7;
        cursor: pointer;
    }

    .tile-oppstatus {
        background: #373E4A;
        cursor: pointer;
    }

    .tile-quicklinks {
        background: #ffffff;
        border: 1px solid #EBEBEB;
    }



    .company-details label, .company-details p {
        font-size: 12px;
    }

    .company-details label {
        color: #00366c;
        margin-bottom: 0px;
    }

    .tile-stats .icon i {
        position: relative;
        top: -20px
    }

    .tile-stats {
        min-height: 149px
    }

        .tile-stats .num {
            font-size: 60px;
            font-weight: bold;
            line-height: 100%
        }

        .tile-stats p {
            font-size: 14px
        }

            .tile-stats p a {
                text-decoration: underline
            }

    .tile-quicklinks {
        background: #ffffff;
        border: 1px solid #EBEBEB
    }

        .tile-quicklinks div.num {
            color: #373E4A
        }

        .tile-quicklinks h3 {
            color: #373E4A
        }

        .tile-quicklinks p {
            color: #373E4A
        }

        .tile-quicklinks:hover {
            background: #ffffff
        }

    .tile-oppstatus {
        background: #373E4A
    }

        .tile-oppstatus div.num {
            color: #ECF1F7
        }

        .tile-oppstatus h3 {
            color: #ECF1F7
        }

        .tile-oppstatus p {
            color: #ECF1F7
        }

        .tile-oppstatus i {
            color: #A3A8B0
        }

        .tile-oppstatus:hover {
            background: #373E4A
        }

    .tile-aopotentials {
        background: #ECF1F7
    }

        .tile-aopotentials div.num {
            color: #123568
        }

        .tile-aopotentials h3 {
            color: #123568
        }

        .tile-aopotentials p {
            color: #123568
        }

        .tile-aopotentials i {
            color: #D3DAE3
        }

        .tile-aopotentials:hover {
            background: #ECF1F7
        }

    .tile-reg-users {
        background: #123568
    }

        .tile-reg-users div.num {
            color: #ffffff
        }

        .tile-reg-users h3 {
            color: #ffffff
        }

        .tile-reg-users p {
            color: #ffffff
        }

        .tile-reg-users i {
            color: #20467E
        }

        .tile-reg-users:hover {
            background: #123568
        }

    .name-title-info-list {
        padding: 5px 15px
    }

        .name-title-info-list .name-title-info-item {
            padding-top: 5px !important;
            padding-bottom: 5px !important
        }

        .name-title-info-list .name-title {
            font-size: 14px;
            color: #373E4A
        }

        .name-title-info-list .sub-phone-email {
            font-size: 10px;
            color: #949494
        }

    .timeline {
        border: 1px solid #f5f5f6;
        border-radius: 3px;
        padding: 15px
    }

    ul.cbp_tmtimeline > li .cbp_tmlabel, ul.cbp_tmtimeline > li .cbp_tmlabel.empty {
        margin-bottom: 15px !important
    }

    .cbp_tmtimeline > li .cbp_tmlabel {
        background: #F5F5F7;
        padding: 1.2rem 1.6rem
    }

    .company-details label {
        color: #00366c;
        margin-bottom: 0px
    }

    .company-details label, .company-details p {
        font-size: 12px
    }

    .border-light-gray {
        border: 1px solid #f5f5f6;
    }

    .txt-light-gray {
        color: #949494;
    }

    .edit-btn {
        font-size: 12px;
    }

    .ml-10 {
        margin-left: 10px;
    }

    .company-details label, .company-details p {
        font-size: 12px;
    }

    .company-details label {
        color: #00366c;
        margin-bottom: 0px;
    }

    label {
        display: inline-block;
        max-width: 100%;
        margin-bottom: 5px;
        font-weight: bold;
    }

    .cbp_tmtimeline:before {
        background: #f5f5f6;
        width: 5px;
        margin-left: -6px;
    }

    .timeline {
        border: 1px solid #f5f5f6;
        border-radius: 3px;
        padding: 15px;
    }

    .cbp_tmtimeline:before {
        background: #f5f5f6;
        width: 5px;
        margin-left: -6px;
    }

    .cbp_tmtimeline > li .cbp_tmtime {
        display: block;
        width: 25%;
        padding-right: 100px;
        position: absolute;
    }

        .cbp_tmtimeline > li .cbp_tmtime > span {
            color: #111;
            font-size: 15px;
        }

            .cbp_tmtimeline > li .cbp_tmtime > span:first-child {
                font-weight: bold;
                margin-bottom: 2px;
            }

            .cbp_tmtimeline > li .cbp_tmtime > span:last-child {
                color: #303641;
                zoom: 1;
                -webkit-opacity: 0.8;
                -moz-opacity: 0.8;
                opacity: 0.8;
                filter: alpha(opacity=80);
                font-size: 12px;
            }

            .cbp_tmtimeline > li .cbp_tmtime > span.large {
                font-size: 17px;
            }

            .cbp_tmtimeline > li .cbp_tmtime > span.hidden + span {
                margin-top: 8px;
            }

    .cbp_tmtimeline > li .cbp_tmicon {
        background: #ffffff;
        color: #d2d2d2;
        -moz-box-shadow: 0px 0px 0px 5px #f5f5f6;
        -webkit-box-shadow: 0px 0px 0px 5px #f5f5f6;
        box-shadow: 0px 0px 0px 5px #f5f5f6;
    }

        .cbp_tmtimeline > li .cbp_tmicon.bg-primary {
            background-color: #303641;
            color: #fff;
        }

        .cbp_tmtimeline > li .cbp_tmicon.bg-secondary {
            background-color: #ee4749;
            color: #fff;
        }

        .cbp_tmtimeline > li .cbp_tmicon.bg-success {
            background-color: #00a651;
            color: #fff;
        }

        .cbp_tmtimeline > li .cbp_tmicon.bg-info {
            background-color: #21a9e1;
            color: #fff;
        }

        .cbp_tmtimeline > li .cbp_tmicon.bg-warning {
            background-color: #fad839;
            color: #fff;
        }

        .cbp_tmtimeline > li .cbp_tmicon.bg-danger {
            background-color: #cc2424;
            color: #fff;
        }

    .cbp_tmtimeline > li .cbp_tmlabel {
        background: #f5f5f6;
        color: #737881;
        margin-bottom: 70px;
        padding: 1.7em;
        -webkit-border-radius: 3px;
        -webkit-background-clip: padding-box;
        -moz-border-radius: 3px;
        -moz-background-clip: padding;
        border-radius: 3px;
        background-clip: padding-box;
    }

        .cbp_tmtimeline > li .cbp_tmlabel h2,
        .cbp_tmtimeline > li .cbp_tmlabel p {
            color: #737881;
            font-family: "Helvetica Neue", Helvetica, "Noto Sans", sans-serif;
            font-size: 12px;
            margin: 0;
            line-height: 1.42857143;
        }

            .cbp_tmtimeline > li .cbp_tmlabel p + p {
                margin-top: 15px;
            }

        .cbp_tmtimeline > li .cbp_tmlabel h2 {
            font-size: 16px;
        }

            .cbp_tmtimeline > li .cbp_tmlabel h2 a {
                color: #303641;
            }

            .cbp_tmtimeline > li .cbp_tmlabel h2 span {
                zoom: 1;
                -webkit-opacity: 0.6;
                -moz-opacity: 0.6;
                opacity: 0.6;
                filter: alpha(opacity=60);
            }

        .cbp_tmtimeline > li .cbp_tmlabel:after {
            border-right-color: #f5f5f6;
        }

        .cbp_tmtimeline > li .cbp_tmlabel.empty {
            background: 0;
            padding: 9px 0;
            margin-bottom: 70px;
        }

            .cbp_tmtimeline > li .cbp_tmlabel.empty:after {
                visibility: hidden;
            }



    .cbp_tmtimeline > li .cbp_tmicon {
        width: 40px;
        height: 40px;
        speak: none;
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        font-size: 1.4em;
        line-height: 40px;
        -webkit-font-smoothing: antialiased;
        position: absolute;
        color: #fff;
        background: #46a4da;
        border-radius: 50%;
        box-shadow: 0 0 0 8px #afdcf8;
        text-align: center;
        left: 20%;
        top: 0;
        margin: 0 0 0 -25px;
    }
</style>

<script type="text/javascript">
     function PrepareFormLink(tableName, type) {
        ////debugger;

        if (navigator.onLine == false) {
            hideProgress();
            alert('Check your Internet Connection');
            return false;
        }

        showProgress();

        window.location = "/CreateForm/CreateForm/" + tableName + "/ID/" + type + "/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + '@Model.Key';

    }

    function GoDetailsPage(file, gid) {
        if (file == 'CO') {
            window.location = "/DetailsPage/CompanyDetails/?sCompanyId=" + gid;
        }
        else if (file == 'CN') {
            window.location = "/DetailsPage/ContactDetails/?sContactId=" + gid;
        }
        else if (file == 'CNv2') {
            window.location = "/DetailsPage/ContactV2Details/?sContactId=" + gid;
        }
        else if (file == 'OP') {
            window.location = "/DetailsPage/OPDetails/?sOPId=" + gid;
		}
         else if (file == 'WL') {
            window.location = "/DetailsPage/WLDetails/?sWLId=" + gid;
        }
    }

     function openRecord(sfile,id) {
        window.location = "/CreateForm/CreateForm/"+ sfile +"/" + id + "/TYPE/false/false/KEY/false/MASTERID/FORMKEY/FIELD/"+ '@Model.Key';
    }

</script>


<!-- Start: Content-Wrapper -->
<section id="content_wrapper" style="height:calc(100%)">
    <!-- Begin: Content -->
    <section id="content" style="height:100%" class=" table-layout animated fadein">
        <div class="main-content" style="background-color:#003665;padding:12px;height:90%">
            <div class="">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="company-title ml-10 mb-10">
                        <h3 class="mt-10" style="color:white !important">My Priorities</h3>
                    </div>
                </div>
            </div>

            <div class="page-container">
                <div class="main-content">
                    <div class="page-content-container">
                        <div class="row my-4">
                            <div class="col-lg-4 col-md-6 col-xs-12">
                                <!--<div class="tile-stats tile-reg-users" onclick="gotoDsk('DSK_9CAF8C7F-E749-4F34-5858-AC5C00B00AA5')">
                                <div class="icon"><i class="entypo-star-empty"></i></div>
                                <div class="num">@Model.OPActiveCount</div>
                                <h3>Active Opportunities</h3>-->
                                @*<p>Last 3 Months</p>*@
                                <!--</div>-->
                                <div class="tile-stats tile-oppstatus d-flex justify-content-start align-items-center" onclick="gotoDsk('DSK_CEDDB884-A6F3-45D0-5858-AC5C00B1D352')">
                                    <div>
                                        <div class="icon"><i class="entypo-chart-bar"></i></div>
                                        <div class="num">@Model.OPCount_Total</div>
                                        <h3>Opportunity Status</h3>
                                    </div>
                                    <div class="d-block ml-20">
                                        <ul class="list-unstyled my-auto" style="font-size:11px;">
                                            <li><div class="num-col">@Model.OPCount_Creating</div> | Won</li>
                                            <li><div class="num-col">@Model.OPCount_Hold</div>  | Hot Prospect</li>
                                            <li><div class="num-col">@Model.OPCount_Won</div>  | Interested Prospect</li>
                                            <li><div class="num-col">@Model.OPCount_QualifiedProspect</div>  | Qualified Prospect</li>
                                            <li><div class="num-col">@Model.OPCount_PotentialProspect</div>  | Potential Prospect</li>
                                            <li><div class="num-col">@Model.OPCount_Lost</div>  | Lost</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 col-xs-12">
                                <div class="tile-stats tile-aopotentials" onclick="gotoDsk('DSK_9CAF8C7F-E749-4F34-5858-AC5C00B00AA5')">
                                    <div class="icon"><i class="fa fa-dollar"></i></div>
                                    <div class="num">@Model.OPTotalAmount.ToString("C0")</div>
                                    <h3>Active Opportunities Potential</h3>
                                    @*<p>Last 3 Months</p>*@
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 col-xs-12 ">
                                <div class="tile-stats tile-oppstatus d-flex justify-content-start align-items-center" onclick="gotoDsk('DSK_E91E11E9-90E1-4389-5858-ACFC00A85521')">
                                    <div>
                                        <div class="icon"><i class="entypo-chart-bar"></i></div>
                                        <div class="num">@Model.WLCount_Total</div>
                                        <h3>My Leads Status</h3>
                                    </div>
                                    <div class="d-block ml-20">
                                        <ul class="list-unstyled my-auto" style="font-size:11px;">
                                            <li><div class="num-col">@Model.WLCount_Assigned</div> | Assigned</li>
                                            <li><div class="num-col">@Model.WLCount_Qualified</div>  | Qualified</li>
                                            <li><div class="num-col">@Model.WLCount_EmailSentToCustomer</div> | Email Sent To Customer</li>
                                            <li><div class="num-col">@Model.WLCount_CallPlacedToCustomer</div>  | Call Placed To Customer</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 col-xs-12">
                                <div class="tile-stats tile-oppstatus" onclick="gotoDsk('DSK_F3CA8A52-64BC-4856-5858-AC5C011FF460')">
                                    <div class="icon"><i class="entypo-chat"></i></div>
                                    <div class="num">@Model.OPFollowUpCount</div>
                                    <h3>My Opportunties to Follow Up</h3>
                                    @*<p>Last 3 Months</p>*@
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6 col-xs-12">
                                <div class="tile-stats tile-reg-users" onclick="gotoDsk('DSK_2004060109460200235C_S 00016XX')">
                                    <div class="icon"><i class="entypo-phone"></i></div>
                                    <div class="num">@Model.QTTotalCount</div>
                                    <h3>My Quotes to Follow Up</h3>
                                    @*<p>Last 3 Months</p>*@
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6 col-xs-12">
                                <!--<div class="tile-stats tile-aopotentials" onclick="gotoDsk('DSK_2006070710355090045ME_ 00004XX')">
                                    <div class="icon"><i class="entypo-calendar"></i></div>
                                    <div class="num">@Model.APTotalCount</div>
                                    <h3>My Appointments</h3>-->
                                <!--</div>-->
                                <div class="tile-stats tile-aopotentials" onclick="gotoDsk('DSK_74FA4B19-152C-447B-5858-AEEF006BEE54')">
                                    <div class="icon"><i class="fa fa-warning"></i></div>
                                    <div class="num">@Model.APTotalCount</div>
                                    <h3>My Open CIRs</h3>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-xl-6 col-xs-12">

                                @*Web Leads*@
                                <div class="d-flex justify-content-between align-items-center mt-20">
                                    <div class="d-flex justify-content-start align-items-center">
                                        <h4 class="m-0 ">My Leads</h4>
                                        <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_61C971ED-D8D9-41C6-5858-ACBD009A4C38')">
                                            <i class="entypo-eye"></i>
                                            View All
                                        </a>
                                        <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_E91E11E9-90E1-4389-5858-ACFC00A85521')">
                                            <i class="entypo-eye"></i>
                                            View My Leads
                                        </a>
                                    </div>

                                    @*<a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_78AFAA51-632F-418F-5858-AD1E00ED0067')">
                                            <i class="entypo-eye"></i>
                                            View My Group
                                        </a>
                                        <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_87D9A3EE-424A-4886-5858-AD40007683D9')">
                                            <i class="entypo-eye"></i>
                                            View Disqualified - My Group
                                        </a>*@

                                    <div class="d-flex justify-content-start">
                                        <a class="ml-10 txt-light-gray edit-btn" href="#" onclick=PrepareFormLink('WL','CRU_WL')>
                                            <i class="entypo-plus"></i>
                                            Add Leads
                                        </a>
                                    </div>

                                </div>
                                <div class="name-title-info-list border-light-gray br-3 mt-10">

                                    @if (Model.WLCount > 0)
                                    {
                                        for (int i = 0; i < Model.WLCount; i++)
                                        {
                                            <div class="name-title-info-item">
                                                <a href="#" onclick="GoDetailsPage('WL','@Model.LinkedWLs[i].Gid_id')">
                                                    <div class="d-flex justify-content-start align-items-start">
                                                        <i class="entypo-vcard mr-5"></i>
                                                        <div>
                                                            <div class="name-title">
                                                                @Model.LinkedWLs[i].Name | @Model.LinkedWLs[i].WLNO |@Model.LinkedWLs[i].Stage | @Model.LinkedWLs[i].TypeOfReq | @Model.LinkedWLs[i].ProductType
                                                            </div>
                                                            <div class="sub-phone-email mb-5">
                                                                @Model.LinkedWLs[i].Email | @Model.LinkedWLs[i].CompanyName | @Model.LinkedWLs[i].City| @Model.LinkedWLs[i].County
                                                            </div>
                                                        </div>
                                                    </div>
                                                </a>
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="name-title-info-item">
                                            <a href="#">
                                                <div class="d-flex justify-content-start align-items-start">
                                                    @*<img class="mr-10 mt-5" src="~/Content/images/icon-v-card.svg" alt="Opportunity">*@
                                                    <div>
                                                        <div class="name-title">
                                                            No leads found.
                                                        </div>
                                                        <div class="sub-phone-email mb-5">

                                                        </div>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    }


                                </div>

                                <div class="d-flex justify-content-between align-items-center mt-20">
                                    <div class="d-flex justify-content-start align-items-center">
                                        <h4 class="m-0 ">My Opportunities</h4>
                                        <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_2003012009491863136MAI 30119XX')">
                                            <i class="entypo-eye"></i>
                                            View All
                                        </a>
                                        <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_CEDDB884-A6F3-45D0-5858-AC5C00B1D352')">
                                            <i class="entypo-eye"></i>
                                            View All - My Opps
                                        </a>
                                        <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_47A2A180-CE1A-4593-5858-AD5E00E63AE6')">
                                            <i class="entypo-eye"></i>
                                            View - My Open Opps
                                        </a>
                                    </div>
                                    <div class="d-flex justify-content-start">
                                        <a class="ml-20 txt-light-gray edit-btn" href="#" onclick=PrepareFormLink('OP','CRU_OP')>
                                            <i class="entypo-plus"></i>
                                            Add Oppurtunity
                                        </a>
                                    </div>

                                </div>
                                <div class="name-title-info-list border-light-gray br-3 mt-10">

                                    @if (Model.OPCount > 0)
                                    {
                                        for (int i = 0; i < Model.OPCount; i++)
                                        {

                                            <div class="name-title-info-item">
                                                <a href="#" onclick="GoDetailsPage('OP','@Model.LinkedOPs[i].Gid_id')">
                                                    <div class="d-flex justify-content-start align-items-start">
                                                        @*<img class="mr-10 mt-5" src="~/Content/images/icon-v-card.svg" alt="Opportunity">*@
                                                        <i class="entypo-star mr-5"></i>
                                                        <div>
                                                            <div class="name-title">
                                                                @Model.LinkedOPs[i].OPName | @Model.LinkedOPs[i].OppNumber | @Model.LinkedOPs[i].Company | @Model.LinkedOPs[i].Contact |@Model.LinkedOPs[i].Probability
                                                            </div>
                                                            <div class="sub-phone-email mb-5">
                                                                @Model.LinkedOPs[i].OPType | @Model.LinkedOPs[i].LOB | @Model.LinkedOPs[i].Stage | @Model.LinkedOPs[i].ExpCloseDate.ToString("MM/dd/yyyy") | @Model.LinkedOPs[i].TotalAmount.ToString("C0").Replace("$", @Model.LinkedOPs[i].Currency + " ")
                                                            </div>


                                                        </div>
                                                    </div>
                                                </a>
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="name-title-info-item">
                                            <a href="#">
                                                <div class="d-flex justify-content-start align-items-start">
                                                    @*<img class="mr-10 mt-5" src="~/Content/images/icon-v-card.svg" alt="Opportunity">*@
                                                    <div>
                                                        <div class="name-title">
                                                            No opportunities found.
                                                        </div>
                                                        <div class="sub-phone-email mb-5">

                                                        </div>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    }


                                </div>

                                <div class="d-flex justify-content-between align-items-center mt-20">
                                    <div class="d-flex justify-content-start align-items-center">
                                        <h4 class="m-0 ">Recent Contacts</h4>
                                        <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_2004051408135030253C_S 00016XX')">
                                            <i class="entypo-eye"></i>
                                            View All
                                        </a>
                                        <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_2004110817164953089MAR 00014XX')">
                                            <i class="entypo-eye"></i>
                                            View My Contacts
                                        </a>
                                    </div>
                                    <div class="d-flex justify-content-start">
                                        <a class="ml-10 txt-light-gray edit-btn" href="#" onclick=PrepareFormLink('CN','CRU_CN')>
                                            <i class="entypo-plus"></i>
                                            Add Contact
                                        </a>
                                    </div>
                                </div>
                                <div class="name-title-info-list border-light-gray br-3 mt-10">

                                    @if (Model.CNCount > 0)
                                    {
                                        for (int i = 0; i < Model.CNCount; i++)
                                        {
                                            <div class="name-title-info-item">
                                                <a href="#" onclick="GoDetailsPage('CN','@Model.LinkedCNs[i].Gid_id')">
                                                    <div class="d-flex justify-content-start align-items-start">
                                                        <i class="entypo-vcard mr-5"></i>
                                                        <div>
                                                            <div class="name-title">
                                                                @Model.LinkedCNs[i].CNName | @Model.LinkedCNs[i].AccountName | @Model.LinkedCNs[i].Title
                                                            </div>
                                                            <div class="sub-phone-email mb-5">
                                                                @Model.LinkedCNs[i].Phone | @Model.LinkedCNs[i].Email | @Model.LinkedCNs[i].ContactofUser
                                                            </div>
                                                        </div>
                                                    </div>
                                                </a>
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="name-title-info-item">
                                            <a href="#">
                                                <div class="d-flex justify-content-start align-items-start">
                                                    @*<img class="mr-10 mt-5" src="~/Content/images/icon-v-card.svg" alt="Opportunity">*@
                                                    <div>
                                                        <div class="name-title">
                                                            No contacts found.
                                                        </div>
                                                        <div class="sub-phone-email mb-5">

                                                        </div>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    }


                                </div>

                                @*Recent Accounts*@
                                <div class="d-flex justify-content-between align-items-center mt-20">
                                    <div class="d-flex justify-content-start align-items-center">
                                        <h4 class="m-0 ">Recent Accounts</h4>
                                        <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_0B9E62DB-3FE2-4E50-5858-AC4F0085282F')">
                                            <i class="entypo-eye"></i>
                                            View All
                                        </a>
                                        <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_2004063009585560154C_S 00016XX')">
                                            <i class="entypo-eye"></i>
                                            View My Accounts
                                        </a>
                                    </div>
                                    <div class="d-flex justify-content-start">
                                        <a class="ml-10 txt-light-gray edit-btn" href="#" onclick=PrepareFormLink('CO','CRU_CO')>
                                            <i class="entypo-plus"></i>
                                            Add Account
                                        </a>
                                    </div>
                                </div>
                                <div class="name-title-info-list border-light-gray br-3 mt-10">

                                    @if (Model.COCount > 0)
                                    {
                                        for (int i = 0; i < Model.COCount; i++)
                                        {
                                            <div class="name-title-info-item">
                                                <a href="#" onclick="GoDetailsPage('CO','@Model.LinkedCOs[i].Gid_id')">
                                                    <div class="d-flex justify-content-start align-items-start">
                                                        <i class="entypo-briefcase mr-5"></i>
                                                        <div>
                                                            <div class="name-title">
                                                                @Model.LinkedCOs[i].CompanyName | @Model.LinkedCOs[i].Code | @Model.LinkedCOs[i].AccountType | @Model.LinkedCOs[i].NAVID | | @Model.LinkedCOs[i].Industry
                                                            </div>
                                                            <div class="sub-phone-email mb-5">
                                                                @Model.LinkedCOs[i].Address | @Model.LinkedCOs[i].City | @Model.LinkedCOs[i].State | @Model.LinkedCOs[i].Zip
                                                            </div>
                                                        </div>
                                                    </div>
                                                </a>
                                            </div>
                                        }
                                    }
                                    else
                                    {
                                        <div class="name-title-info-item">
                                            <a href="#">
                                                <div class="d-flex justify-content-start align-items-start">
                                                    @*<img class="mr-10 mt-5" src="~/Content/images/icon-v-card.svg" alt="Opportunity">*@
                                                    <div>
                                                        <div class="name-title">
                                                            No account found.
                                                        </div>
                                                        <div class="sub-phone-email mb-5">

                                                        </div>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    }


                                </div>

                            </div>
                            <div class="col-xl-6 col-xs-12">
                                <div class="timeline">
                                    <div class="timeline-header">
                                        <div class="d-flex justify-content-between align-items-center">
                                            @*<div class="d-flex justify-content-between align-items-center mt-20">*@
                                                <div class="d-flex justify-content-start align-items-center">
                                                    <h4 class="m-0 ">My Upcoming Activities</h4>

                                                    <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_2003100817330625007MAR 00014XX')">
                                                        <i class="entypo-eye"></i>
                                                        View All
                                                    </a>
                                                    <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_2004052710225915073C_S%2000016XX')">
                                                        <i class="entypo-eye"></i>
                                                        View My Activities
                                                    </a>
                                                </div>
                                                <div class="d-flex justify-content-start">

                                                    <a class="ml-10 txt-light-gray edit-btn" href="#" onclick=PrepareFormLink('AC','CRU_AC')>
                                                        <i class="entypo-plus"></i>
                                                        Add Activities
                                                    </a>
                                                </div>
                                                @*<a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_81FBCB4C-6C3F-4329-5858-AC6300E7C95E')">
            <i class="entypo-eye"></i>
              View Past Activities
             </a>*@
                                            </div>
                                        </div>
                                    <div class="name-title-info-list border-light-gray br-3 mt-10">

                                        @if (Model.ACCount > 0)
                                        {
                                            for (int i = 0; i < Model.ACCount; i++)
                                            {
                                                <div class="name-title-info-item">
                                                    <a href="#" onclick="openRecord('AC','@Model.LinkedACs[i].Gid_id')">
                                                        <div class="d-flex justify-content-start align-items-start">
                                                            <i class="entypo-clock mr-5"></i>
                                                            <div>
                                                                <div class="name-title">
                                                                    @Model.LinkedACs[i].Notes.Replace("<br />", "").Replace("<p>", " ").Replace("</p>", " ")
                                                                </div>
                                                                <div class="sub-phone-email mb-5">
                                                                    @Model.LinkedACs[i].Type | @Model.LinkedACs[i].Date.ToString("MM/dd/yyyy") | @Model.LinkedACs[i].Contact
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </a>
                                                </div>
                                            }
                                        }
                                        else
                                        {
                                            <div class="name-title-info-item">
                                                <a href="#">
                                                    <div class="d-flex justify-content-start align-items-start">

                                                        <div>
                                                            <div class="name-title">
                                                                No Activities found.
                                                            </div>
                                                            <div class="sub-phone-email mb-5">

                                                            </div>
                                                        </div>
                                                    </div>
                                                </a>
                                            </div>
                                        }
                                    </div>

                                    @*</div>*@

                                </div>

                            </div>

                            @*My Appointments*@
                        <div class="col-xl-6 col-xs-12">
                            <div class="timeline">
                                <div class="timeline-header">
                                    @*<div class="d-flex justify-content-between align-items-center">*@
                                        <div class="d-flex justify-content-between align-items-center mt-20">
                                            <div class="d-flex justify-content-start align-items-center">
                                                <h4 class="m-0 ">My Upcoming Appointments</h4>
                                                <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_2004120313075878074MAR%2000002XX')">
                                                    <i class="entypo-eye"></i>
                                                    View All
                                                </a>
                                                <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_2002041814292423238MAI%2002895XX')">
                                                    <i class="entypo-eye"></i>
                                                    View My Appointments
                                                </a>

                                            </div>
                                            <div class="d-flex justify-content-start">
                                                <a class="ml-10 txt-light-gray edit-btn" href="#" onclick=PrepareFormLink('AP','CRU_AP')>
                                                    <i class="entypo-plus"></i>
                                                    Add Appointments
                                                </a>
                                            </div>
                                        </div>
                                        <div class="name-title-info-list border-light-gray br-3 mt-10">

                                            @if (Model.APCount > 0)
                                            {
                                                for (int i = 0; i < Model.APCount; i++)
                                                {
                                                    <div class="name-title-info-item">
                                                        <a href="#" onclick="openRecord('AP','@Model.LinkedAPs[i].Gid_id')">
                                                            <div class="d-flex justify-content-start align-items-start">
                                                                <i class="entypo-bell mr-5"></i>
                                                                <div>
                                                                    <div class="name-title">
                                                                        @Model.LinkedAPs[i].Description |    @Model.LinkedAPs[i].Type
                                                                    </div>
                                                                    <div class="sub-phone-email mb-5">
                                                                        @Model.LinkedAPs[i].StartDate | @Model.LinkedAPs[i].EndDate
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </a>
                                                    </div>
                                                }
                                            }
                                            else
                                            {
                                                <div class="name-title-info-item">
                                                    <a href="#">
                                                        <div class="d-flex justify-content-start align-items-start">

                                                            <div>
                                                                <div class="name-title">
                                                                    No Appointments found.
                                                                </div>
                                                                <div class="sub-phone-email mb-5">

                                                                </div>
                                                            </div>
                                                        </div>
                                                    </a>
                                                </div>
                                            }

                                            </div>
                                        </div>

                                    @*</div>*@

                            </div>
                        </div>

                            @*My Customer Incidents Reports*@
                             <div class="col-xl-6 col-xs-12">
                                <div class="timeline">
                                    <div class="timeline-header">
                                        @*<div class="d-flex justify-content-between align-items-center">*@
                                            <div class="d-flex justify-content-between align-items-center mt-20">
                                                <div class="d-flex justify-content-start align-items-center">
                                                    <h4 class="m-0 ">My Customer Incident Reports</h4>
                                                    <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_3D6D7FF8-DD42-4D0A-5858-AEB700488199')">
                                                        <i class="entypo-eye"></i>
                                                        View All
                                                    </a>
                                                    <a class="ml-10 txt-light-gray edit-btn" href="#" onclick="gotoDsk('DSK_74FA4B19-152C-447B-5858-AEEF006BEE54')">
                                                        <i class="entypo-eye"></i>
                                                        View My Open CIRs
                                                    </a>

                                                </div>
                                                <div class="d-flex justify-content-start">
                                                    <a class="ml-10 txt-light-gray edit-btn" href="#" onclick=PrepareFormLink('CI','CRU_CI')>
                                                        <i class="entypo-plus"></i>
                                                        Create CIR


                                                    </a>
                                                </div>
                                            </div>
                                            <div class="name-title-info-list border-light-gray br-3 mt-10">

                                                @if (Model.CIRCount > 0)
                                                {
                                                    for (int i = 0; i < Model.CIRCount; i++)
                                                    {
                                                        <div class="name-title-info-item">
                                                            <a href="#" onclick="openRecord('CI','@Model.LinkedCIRs[i].Gid_id')">
                                                                <div class="d-flex justify-content-start align-items-start">
                                                                    <i class="entypo-bell mr-5"></i>
                                                                    <div>
                                                                        <div class="name-title">
                                                                            @Model.LinkedCIRs[i].CIR_No |    @Model.LinkedCIRs[i].CustomerReportIncidentContact | @Model.LinkedCIRs[i].Account | @Model.LinkedCIRs[i].HooverCSOrederNo
                                                                        </div>
                                                                        <div class="sub-phone-email mb-5">
                                                                            @Model.LinkedCIRs[i].DateReported | @Model.LinkedCIRs[i].DateOfIncident | @Model.LinkedCIRs[i].AssignedSalesPerson | @Model.LinkedCIRs[i].Urgency | @Model.LinkedCIRs[i].Status | @Model.LinkedCIRs[i].CIRCategory
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </a>
                                                        </div>
                                                    }
                                                }
                                                else
                                                {
                                                    <div class="name-title-info-item">
                                                        <a href="#">
                                                            <div class="d-flex justify-content-start align-items-start">

                                                                <div>
                                                                    <div class="name-title">
                                                                        No CIRs found.
                                                                    </div>
                                                                    <div class="sub-phone-email mb-5">

                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </a>
                                                    </div>
                                                }
                                            </div>

                                        @*</div>*@

                                    </div>

                                </div>
                            </div>
                        </div>

                    </div>

                </div>

            </div>

        </div>





    </section>
    <!-- End: Content -->
</section>
<!-- End: Content-Wrapper -->