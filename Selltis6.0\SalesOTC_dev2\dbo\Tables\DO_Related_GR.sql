﻿CREATE TABLE [dbo].[DO_Related_GR] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_DO_Related_GR_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_DO] UNIQUEIDENTIFIER NOT NULL,
    [GID_GR] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_DO_Related_GR] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_DO_Related_GR] FOREIGN KEY ([GID_GR]) REFERENCES [dbo].[GR] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_GR_Connected_DO] FOREIGN KEY ([GID_DO]) REFERENCES [dbo].[DO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[DO_Related_GR] NOCHECK CONSTRAINT [LNK_DO_Related_GR];


GO
ALTER TABLE [dbo].[DO_Related_GR] NOCHECK CONSTRAINT [LNK_GR_Connected_DO];


GO
CREATE CLUSTERED INDEX [IX_GR_Connected_DO]
    ON [dbo].[DO_Related_GR]([GID_DO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_DO_Related_GR]
    ON [dbo].[DO_Related_GR]([GID_GR] ASC);

