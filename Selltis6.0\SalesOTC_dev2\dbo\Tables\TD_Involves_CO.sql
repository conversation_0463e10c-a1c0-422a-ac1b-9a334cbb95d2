﻿CREATE TABLE [dbo].[TD_Involves_CO] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_ToDo_Involves_Company_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_CO] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_Involves_CO] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CO_InvolvedIn_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_Involves_CO] FOREIGN KEY ([GID_CO]) REFERENCES [dbo].[CO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_Involves_CO] NOCHECK CONSTRAINT [LNK_CO_InvolvedIn_TD];


GO
ALTER TABLE [dbo].[TD_Involves_CO] NOCHECK CONSTRAINT [LNK_TD_Involves_CO];


GO
CREATE CLUSTERED INDEX [IX_CO_InvolvedIn_TD]
    ON [dbo].[TD_Involves_CO]([GID_TD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_Involves_CO]
    ON [dbo].[TD_Involves_CO]([GID_CO] ASC);

