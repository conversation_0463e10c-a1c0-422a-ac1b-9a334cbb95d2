   .winmd.dll.exe $   OE:\GitHub Selltis\Selltis6.0\Selltis.BusinessLogic\My Project\Application.myappOE:\GitHub Selltis\Selltis6.0\Selltis.BusinessLogic\My Project\Settings.settings>E:\GitHub Selltis\Selltis6.0\Common\DocumentFormat.OpenXml.dll@E:\GitHub Selltis\Selltis6.0\packages\CommonDLLs\ephtmltopdf.dllHE:\GitHub Selltis\Selltis6.0\packages\CommonDLLs\Google.GData.Client.dll>E:\GitHub Selltis\Selltis6.0\packages\CommonDLLs\IMAP4.Net.dll@E:\GitHub Selltis\Selltis6.0\packages\CommonDLLs\ImapUtility.dll@E:\GitHub Selltis\Selltis6.0\packages\CommonDLLs\MailBee.NET.dllYE:\GitHub Selltis\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.Configuration.dllSE:\GitHub Selltis\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.Storage.dllYE:\GitHub Selltis\Selltis6.0\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll=E:\GitHub Selltis\Selltis6.0\packages\CommonDLLs\POP3.Net.dllAE:\GitHub Selltis\Selltis6.0\packages\CommonDLLs\PublicDomain.dllbE:\GitHub Selltis\Selltis6.0\packages\CommonDLLs\Quickwebsoft.Web.UI.WebControls.EventCalendar.dll;E:\GitHub Selltis\Selltis6.0\Common\SautinSoft.Document.dll=E:\GitHub Selltis\Selltis6.0\packages\CommonDLLs\SMTP.Net.dllmC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.Configuration.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.Core.dllvC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.Data.DataSetExtensions.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.Data.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.dllgC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.Drawing.dlluC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.Runtime.Serialization.dllwC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.Web.ApplicationServices.dllcC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.Web.dllnC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.Web.Extensions.dll]E:\GitHub Selltis\Selltis6.0\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dllcC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.Xml.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.Xml.Linq.dllSE:\GitHub Selltis\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Documents.Core.dllTE:\GitHub Selltis\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Documents.Fixed.dllSE:\GitHub Selltis\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Documents.Flow.dllgE:\GitHub Selltis\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.dllHE:\GitHub Selltis\Selltis6.0\Common\telerik DLLs\Telerik.Windows.Zip.dllAE:\GitHub Selltis\Selltis6.0\packages\CommonDLLs\UltimateAjax.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\WindowsBase.dll       UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}D{Registry:Software\Microsoft\.NETFramework,v4.6.1,AssemblyFoldersEx}
{RawFileName}=E:\GitHub Selltis\Selltis6.0\Selltis.BusinessLogic\bin\Debug\     D{Registry:Software\Microsoft\.NETFramework,v4.6.1,AssemblyFoldersEx}fE:\GitHub Selltis\Selltis6.0\Selltis.BusinessLogic\obj\Debug\DesignTimeResolveAssemblyReferences.cache   UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\Facades\.NETFramework,Version=v4.6.1.NET Framework 4.6.1v4.6.1msil
v4.0.30319         