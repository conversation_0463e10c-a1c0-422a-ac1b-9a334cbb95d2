﻿CREATE TABLE [dbo].[EX_Related_CN] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Expense_Related_Contact_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_EX] UNIQUEIDENTIFIER NOT NULL,
    [GID_CN] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_EX_Related_CN] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CN_Connected_EX] FOREIGN KEY ([GID_EX]) REFERENCES [dbo].[EX] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_EX_Related_CN] FOREIGN KEY ([GID_CN]) REFERENCES [dbo].[CN] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[EX_Related_CN] NOCHECK CONSTRAINT [LNK_CN_Connected_EX];


GO
ALTER TABLE [dbo].[EX_Related_CN] NOCHECK CONSTRAINT [LNK_EX_Related_CN];


GO
CREATE CLUSTERED INDEX [IX_CN_Connected_EX]
    ON [dbo].[EX_Related_CN]([GID_EX] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_Related_CN]
    ON [dbo].[EX_Related_CN]([GID_CN] ASC);

