﻿CREATE TABLE [dbo].[GR_Related_FI] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Group_Related_FileList_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_GR] UNIQUEIDENTIFIER NOT NULL,
    [GID_FI] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_GR_Related_FI] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_FL_Connected_GR] FOREIGN KEY ([GID_GR]) REFERENCES [dbo].[GR] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_GR_Related_FL] FOREIGN KEY ([GID_FI]) REFERENCES [dbo].[FI] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[GR_Related_FI] NOCHECK CONSTRAINT [LNK_FL_Connected_GR];


GO
ALTER TABLE [dbo].[GR_Related_FI] NOCHECK CONSTRAINT [LNK_GR_Related_FL];


GO
CREATE CLUSTERED INDEX [IX_FI_Connected_GR]
    ON [dbo].[GR_Related_FI]([GID_GR] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_GR_Related_FI]
    ON [dbo].[GR_Related_FI]([GID_FI] ASC);

