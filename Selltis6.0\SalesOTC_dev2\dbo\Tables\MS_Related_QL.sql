﻿CREATE TABLE [dbo].[MS_Related_QL] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Message_Related_QuotLine_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_MS] UNIQUEIDENTIFIER NOT NULL,
    [GID_QL] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MS_Related_QL] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_MS_Related_QL] FOREIGN KEY ([GID_QL]) REFERENCES [dbo].[QL] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QL_Connected_MS] FOREIGN KEY ([GID_MS]) REFERENCES [dbo].[MS] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MS_Related_QL] NOCHECK CONSTRAINT [LNK_MS_Related_QL];


GO
ALTER TABLE [dbo].[MS_Related_QL] NOCHECK CONSTRAINT [LNK_QL_Connected_MS];


GO
CREATE CLUSTERED INDEX [IX_QL_Connected_MS]
    ON [dbo].[MS_Related_QL]([GID_MS] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MS_Related_QL]
    ON [dbo].[MS_Related_QL]([GID_QL] ASC);

