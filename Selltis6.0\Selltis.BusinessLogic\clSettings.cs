﻿using System;
using System.Web;
using System.IO;
using System.Configuration;
using System.Text.RegularExpressions;
using System.Drawing;
using System.Data;
using System.Collections;
using System.Collections.Generic;

namespace Selltis.BusinessLogic
{
	public sealed class clSettings
	{

		public static string LoadSiteSettings()
		{

			string sHostName = GetHostName();

			//Try

			//Dim myXMLfile As String = HttpContext.Current.Server.MapPath((Convert.ToString("~/App_Data/SiteSettings/") & sHostName) + "_SiteSettings.xml")
			string myXMLfile = System.Configuration.ConfigurationManager.AppSettings["CustomFilesPath"].ToString() + sHostName + "/SiteSettings.xml";

			//To differentiate local and azure..J
			string substring = myXMLfile.Substring(1, 2);
			if (substring == ":\\")
			{
				myXMLfile = System.Configuration.ConfigurationManager.AppSettings["CustomFilesPath"].ToString() + sHostName + "/SiteSettings.xml";
			}
			else
			{
				myXMLfile = HttpContext.Current.Server.MapPath(System.Configuration.ConfigurationManager.AppSettings["CustomFilesPath"].ToString()) + sHostName + "\\SiteSettings.xml";
			}


			HttpContext.Current.Session[sHostName + "_SiteSettings"] = null;

			//If Not String.IsNullOrEmpty(myXMLfile) Then

			if (string.IsNullOrEmpty(myXMLfile))
			{
				return myXMLfile + "Site settings file for the site " + System.Web.HttpContext.Current.Request.Url.Host + " is not found";
			}

			if (File.Exists(myXMLfile) == false)
			{

				return myXMLfile + "Site settings file for the site " + sHostName + " is not found";
			}

			DataSet ds = new DataSet();
			var fsReadXml = new FileStream(myXMLfile, FileMode.Open, FileAccess.Read);
			ds.ReadXml(fsReadXml);
			fsReadXml.Close();
			fsReadXml.Dispose();

			if (ds != null && ds.Tables[0] != null)
			{

				string sError = string.Empty;

				sError = ValidateConfiguration("SiteId", ds);
				if (sError != string.Empty)
				{
					return sError;
				}

				sError = ValidateConfiguration("ConnectionString", ds);
				if (sError != string.Empty)
				{
					return sError;
				}

				//sError = ValidateConfiguration("Logo", ds)
				//If sError <> String.Empty Then
				//    Return sError
				//End If

				sError = ValidateConfiguration("AttachmentsPath", ds);
				if (sError != string.Empty)
				{
					return sError;
				}

				sError = ValidateConfiguration("AttachmentsTempPath", ds);
				if (sError != string.Empty)
				{
					return sError;
				}

				sError = ValidateConfiguration("AttachmentsMaxFolderSize", ds);
				if (sError != string.Empty)
				{
					return sError;
				}

				sError = ValidateConfiguration("AttachmentMaxFileSize", ds);
				if (sError != string.Empty)
				{
					return sError;
				}

				sError = ValidateConfiguration("UseCache", ds);
				if (sError != string.Empty)
				{
					return sError;
				}

				sError = ValidateConfiguration("CustomDLLName", ds);
				if (sError != string.Empty)
				{
					//Return sError
					HttpContext.Current.Session["CUSTOMDLLEXISTED"] = false;
				}

				HttpContext.Current.Session[sHostName + "_SiteSettings"] = ds.Tables[0];

				return string.Empty;

			}
			else
			{
				return myXMLfile + "Site settings for the site " + System.Web.HttpContext.Current.Request.Url.Host + " is not found";
			}
			//Catch ex As Exception
			//    HttpContext.Current.Session(sHostName + "_SiteSettings") = Nothing
			//    Return ex.Message
			//End Try
			//Else
			//Return False
			//End If
		}

		private static string ValidateConfiguration(string sProperty, DataSet ds)
		{

			if (ds.Tables[0].Columns.Contains(sProperty) == false)
			{
				return "Site settings for the site '" + System.Web.HttpContext.Current.Request.Url.Host + "' is not configured correctly; Error:'" + sProperty + "' is missed in the configuration";
			}

			if (ds.Tables[0].Rows[0][sProperty] == null)
			{
				return "Site settings for the site '" + System.Web.HttpContext.Current.Request.Url.Host + "' is not configured correctly; Error:'" + sProperty + "' is missed in the configuration";
			}

			if (string.IsNullOrEmpty(ds.Tables[0].Rows[0][sProperty].ToString()))
			{
				return "Site settings for the site '" + System.Web.HttpContext.Current.Request.Url.Host + "' is not configured correctly; Error:'" + sProperty + "' is missed in the configuration";
			}

			return string.Empty;

		}

		public static string GetHostName()
		{
			string sHostingEnvironment = ConfigurationManager.AppSettings["HostingEnvironment"].ToString().ToLower();
			string sHostName = "";
			if (sHostingEnvironment == "debugging")
			{
				sHostName = "default";
			}
			else if (sHostingEnvironment == "staging")
			{
				sHostName = HttpContext.Current.Request.Url.Host.ToString().ToLower() + "_" + HttpContext.Current.Request.Url.Port.ToString();
			}
			else
			{

				object host = null;
				if (HttpContext.Current.Session["HostName"] == null)
				{
					host = HttpContext.Current.Request.Url.Host.ToString().ToLower();
				}
				else
				{
					host = HttpContext.Current.Session["HostName"].ToString();
				}


				return Convert.ToString(host);
			}
			return sHostName;
		}

		public static string GetSiteId()
		{
			string sHostingEnvironment = ConfigurationManager.AppSettings["HostingEnvironment"].ToString().ToLower();
			var sSiteId = ((DataTable)GetSessionValue("SiteSettings")).Rows[0]["SiteId"].ToString(); //((DataTable)Util.GetSessionValue("SiteSettings")).Rows[0]["SiteId"].ToString()

			return sSiteId;
		}

		public static string GetUseSSO()
		{
			string sHostingEnvironment = ConfigurationManager.AppSettings["HostingEnvironment"].ToString().ToLower();
			string sUseSSO = "";
			try
			{
				sUseSSO = Convert.ToString(((DataTable)GetSessionValue("SiteSettings")).Rows[0]["UseSSO"]);
			}
			catch (Exception ex)
			{
				sUseSSO = "false";
			}
			return sUseSSO;
		}

		private static string GetHostPrefix()
		{
			string HostPrefix = "";

			string HostName = "";
			HostName = HttpContext.Current.Request.Url.Host;

			HostPrefix = HostName.Replace(".selltis.com", "");
			return HostPrefix;
		}

		public static string GetCustomDLL_FilePath()
		{
			try
			{
				string sCustomDLLFilePath = "";
				string sCustomFilesPath = ConfigurationManager.AppSettings["CustomFilesPath"].ToString();
				string sHostName = GetHostName();

				//To differentiate local and azure..J
				string substring = sCustomFilesPath.Substring(1, 2);
				if (substring == ":\\")
				{
					sCustomFilesPath = System.Configuration.ConfigurationManager.AppSettings["CustomFilesPath"].ToString();
				}
				else
				{
					sCustomFilesPath = HttpContext.Current.Server.MapPath(System.Configuration.ConfigurationManager.AppSettings["CustomFilesPath"].ToString());
				}

				string sCusDLLName = ((DataTable)GetSessionValue("SiteSettings")).Rows[0]["CustomDLLName"].ToString();

				string sHostingEnvironment = ConfigurationManager.AppSettings["HostingEnvironment"].ToString().ToLower();
				if (sHostingEnvironment == "debugging")
				{
					var path = System.IO.Path.Combine(System.AppDomain.CurrentDomain.BaseDirectory, "bin");
					sCustomDLLFilePath = System.IO.Path.Combine(path, sCusDLLName);
				}
				else
				{
					sCustomDLLFilePath = Convert.ToString((sCustomFilesPath + sHostName) + "\\") + sCusDLLName;
				}

				return sCustomDLLFilePath;
			}
			catch (Exception generatedExceptionName)
			{
				return "";
			}

		}


		public static object GetSessionValue(string sKeyName)
		{
			sKeyName = MakeSessionKey(sKeyName);
			return HttpContext.Current.Session[sKeyName];
		}

		public static string MakeSessionKey(string sKey)
		{
			return Convert.ToString(GetHostName() + "_") + sKey;
		}

		public static string ConvertBase64Images(ref string strData, string UserId = "")
		{

			ArrayList Ht = new ArrayList(); //keeps the physical image file names
			ArrayList Ht1 = new ArrayList(); //keeps the base64 Image string to check for the same images
			Dictionary<int, string> DHT = new Dictionary<int, string>();

			//MMR Change -- Replace the Base64 images with local image path
			string regexImgSrc = "<img[^>]*?src\\s*=\\s*[\"']?([^'\" >]+?)[ '\"][^>]*?>";
			MatchCollection matchesImgSrc = Regex.Matches(strData, regexImgSrc, RegexOptions.IgnoreCase | RegexOptions.Singleline);

			int indx = 0;

// INSTANT C# NOTE: There is no C# equivalent to VB's implicit 'once only' variable initialization within loops, so the following variable declaration has been placed prior to the loop:
			int iwidth = 0;
		int iheight = 0;
			for (indx = 0; indx < matchesImgSrc.Count; indx++)
			{

				string imgstrs = matchesImgSrc[indx].Value;

				string _sPath = System.Web.HttpContext.Current.Server.MapPath("../Temp"); //Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)
				_sPath = _sPath + "\\" + clSettings.GetHostName() + "\\SignatureImages\\" + UserId + "\\";

				//Dim _sPath = "/PublicFiles/" + clSettings.GetSiteId() + "/OutLookImages/"

				bool exists = System.IO.Directory.Exists(_sPath);
				if (!exists)
				{
					System.IO.Directory.CreateDirectory(_sPath);
				}

	//			Dim iwidth, iheight As Integer
				iwidth = 0;
				iheight = 0;

				//Get the Image size section start
				Regex reHeight = new Regex("height:\\s\\d*", RegexOptions.IgnoreCase | RegexOptions.Singleline);
				Regex reWidth = new Regex("width:\\s\\d*", RegexOptions.IgnoreCase | RegexOptions.Singleline);

				Match mWidth = reWidth.Match(imgstrs);
				if (mWidth.Length > 8 && mWidth.Value.Contains(":") & mWidth.Value.Contains("width"))
				{
					int.TryParse(mWidth.Value.Split(':').GetValue(1).ToString().Trim(), out iwidth);
				}

				Match mHeight = reHeight.Match(imgstrs);
				if (mHeight.Length > 8 && mHeight.Value.Contains(":") & mHeight.Value.Contains("height"))
				{
					int.TryParse(mHeight.Value.Split(':').GetValue(1).ToString().Trim(), out iheight);
				}

				//Get the images size section end
				string _imageName = "SignatureImage" + GetTimeString() + indx.ToString() + ".jpg";
				_sPath = _sPath + _imageName;
				//Dim PBase64Imagestr As String = Regex.Match(imgstrs, "<img.+?src=[""'](.+?)[""'].+?>", RegexOptions.IgnoreCase).Groups(1).Value

				//29082018 tickt #2401: Images are not showed in outlook email for the fist time after AC logged from outlook..J
				string PBase64Imagestr = Regex.Match(imgstrs, "src\\s*=\\s*[\"'](.+?)[\"'].+?", RegexOptions.IgnoreCase).Groups[1].Value;

				if (PBase64Imagestr.Split(',').Length > 1)
				{
					string Base64Imagestr = PBase64Imagestr.Split(',')[1];
					//Dim Base64ImagestrEncoded = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(Base64Imagestr)) 'Convert.FromBase64String(Base64Imagestr)
					//Dim bytes = Convert.FromBase64String(Base64ImagestrEncoded)
					string b64 = Base64Imagestr.Replace(" ", "+");
					var bytes = Convert.FromBase64String(b64);
					using (var imageFile = new FileStream(_sPath, FileMode.Create))
					{
						imageFile.Write(bytes, 0, bytes.Length);
						imageFile.Flush();
					}

					//Resize Image if style specified in the image tag
					if (iwidth > 0 && iheight > 0)
					{
						Image _Timg = Image.FromFile(_sPath);
						Image.GetThumbnailImageAbort myCallback = new Image.GetThumbnailImageAbort(ThumbnailCallback);
						Image _ImgToSave = _Timg.GetThumbnailImage(iwidth, iheight, myCallback, IntPtr.Zero);
						_Timg.Dispose();
						if (File.Exists(_sPath))
						{
							File.Delete(_sPath);
						}
						_ImgToSave.Save(_sPath, System.Drawing.Imaging.ImageFormat.Jpeg);
					}
					//Resize done
					Dictionary<string, string> objmmr_letterAttach = new Dictionary<string, string>(); //Attach mmr_Later Image
					objmmr_letterAttach.Add(_imageName, _sPath);

					//checking for the same image 
					if (Ht1.Contains(imgstrs))
					{
// INSTANT C# NOTE: Commented this declaration since looping variables in 'foreach' loops are declared in the 'foreach' header in C#:
//						Dim pair As KeyValuePair(Of Integer, String)
						int indval = 0;
						foreach (KeyValuePair<int, string> pair in DHT)
						{
							if (pair.Value == imgstrs)
							{
								indval = pair.Key;
								goto end_of_for;
							}
						}
	end_of_for:
						Ht.Add(Ht[indval]);
					}
					else
					{
						Ht.Add(_imageName);
						Ht1.Add(imgstrs);
						DHT.Add(indx, imgstrs);
					}

					string baseURL = HttpContext.Current.Request.Url.Scheme + "://" + HttpContext.Current.Request.Url.Authority;
					_sPath = baseURL + "/Temp/" + clSettings.GetHostName() + "/SignatureImages/" + UserId + "/" + _imageName;
					_imageName = "<img src='" + _sPath + "' />";

					strData = strData.ToString().Replace(imgstrs, _imageName);
				}
			}

			return "";

		}

		public static string GetTimeString()
		{

			DateTime sTime = DateTime.Now;
			//GetLocalTime(sTime)

			return sTime.Year + PadLeadingZeros(sTime.Month.ToString(), 2) + PadLeadingZeros(sTime.Day.ToString(), 2) + PadLeadingZeros(sTime.Hour.ToString(), 2) + PadLeadingZeros(sTime.Minute.ToString(), 2) + PadLeadingZeros(sTime.Second.ToString(), 2) + PadLeadingZeros(sTime.Millisecond.ToString(), 3).Substring(0, 2);

		}

		public static bool ThumbnailCallback()
		{
			return false;
		}

		public static string PadLeadingZeros(string strForm, int intLen)
		{
				string tempPadLeadingZeros = null;

			if (strForm.Length < intLen)
			{
				while (strForm.Length != intLen)
				{
					strForm = "0" + strForm;
				}
				tempPadLeadingZeros = strForm;
			}
			else
			{
				tempPadLeadingZeros = strForm;
			}

			return tempPadLeadingZeros;
		}
	}

}
