﻿CREATE TABLE [dbo].[OP_Related_VE] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Opp_Related_Vendor_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    [GID_VE] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_OP_Related_VE] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_OP_Related_VE] FOREIGN KEY ([GID_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_VE_Connected_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[OP_Related_VE] NOCHECK CONSTRAINT [LNK_OP_Related_VE];


GO
ALTER TABLE [dbo].[OP_Related_VE] NOCHECK CONSTRAINT [LNK_VE_Connected_OP];


GO
CREATE CLUSTERED INDEX [IX_VE_Connected_OP]
    ON [dbo].[OP_Related_VE]([GID_OP] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_Related_VE]
    ON [dbo].[OP_Related_VE]([GID_VE] ASC);

