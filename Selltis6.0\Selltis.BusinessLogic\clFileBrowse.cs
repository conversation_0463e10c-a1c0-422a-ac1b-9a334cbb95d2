﻿using System;
using System.Data;
using System.Web;


namespace Selltis.BusinessLogic
{
	public class clFileBrowse
	{

		//owner wt
		//returns a datatable...
		//...with the following fields: sys_name, gid_id, along with any additional fields requested by the caller
		//...with the file's default sort unless defined by caller
		//...with a filter of "all records" unless defined by the caller

		//reference project session information
		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;

		public string TopRecIDForPrevious = "";
		public string TopRecIDForNext = "";
		public bool PreviousEnabled = false;
		public bool NextEnabled = false;
		public long RecordCount = 0;

		public clFileBrowse()
		{
			Initialize();
		}

		private void Initialize()
		{
			string sProc = "clView:Initialize";
			// Try
			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
				goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
				goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
				goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
				goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public DataTable QuickSelect(string par_sFile, string par_sQuickSelectValue = "", int par_iTop = -1, string par_sFilter = "", string par_sSort = "", string par_sTopRecordGID = "", string par_sAdditionalFields = "")
		{
			try
			{


				if (par_sQuickSelectValue != "")
				{

					//filter
					string sFilter = ""; //all records
					if (par_sFilter != "")
					{
						sFilter = par_sFilter;
					}

					string sOper = "";
					string sSortOn = "";
					bool bDescending = false;

					if (par_sSort == "")
					{
						par_sSort = goData.GetDefaultSort(par_sFile);
					}
					sSortOn = GetFirstSortField(par_sSort, ref sOper);
					if (sOper == "<=")
					{
						bDescending = true;
					}

					if (sFilter == "")
					{
						sFilter = sSortOn + sOper + goTR.ConvertStringForQS(goTR.PrepareForSQL(par_sQuickSelectValue), sSortOn.Replace(">=", "").Replace("<=", ""), par_sFile, true, bDescending);
					}
					else
					{
						sFilter = "(" + sFilter + ") AND (" + sSortOn + sOper + goTR.ConvertStringForQS(goTR.PrepareForSQL(par_sQuickSelectValue), sSortOn.Replace(">=", "").Replace("<=", ""), par_sFile, true, bDescending) + ")";
					}

					DataTable oTable = GetData(par_sFile, par_iTop + 1, sFilter, par_sSort, par_sTopRecordGID, par_sAdditionalFields);
					FinalizeData(ref oTable, par_iTop, "QUICKSELECT");
					return oTable;

				}
				else
				{

					DataTable oTable = GetFirst(par_sFile, par_iTop, par_sFilter, par_sSort, par_sTopRecordGID, par_sAdditionalFields);
					return oTable;

				}


			}
			catch (Exception ex)
			{

				return new DataTable();

			}

		}

		public DataTable GetFirst(string par_sFile, int par_iTop = -1, string par_sFilter = "", string par_sSort = "", string par_sTopRecordGID = "", string par_sAdditionalFields = "")
		{

			try
			{


				if (goData.IsFileValid(par_sFile))
				{

					DataTable oTable = GetData(par_sFile, par_iTop + 1, par_sFilter, par_sSort, par_sTopRecordGID, par_sAdditionalFields);
					FinalizeData(ref oTable, par_iTop, "FIRST");
					return oTable;

				}
				else
				{

					return new DataTable();

				}

			}
			catch (Exception ex)
			{

				return new DataTable();

			}

		}

		public DataTable GetNext(string par_sFile, int par_iTop = -1, string par_sFilter = "", string par_sSort = "", string par_sTopRecordGID = "", string par_sAdditionalFields = "")
		{

			try
			{


				if (goData.IsFileValid(par_sFile))
				{

					string sTopRec = TopRecIDForNext;
					if (par_sTopRecordGID != "")
					{
						sTopRec = par_sTopRecordGID;
					}

					DataTable oTable = GetData(par_sFile, par_iTop + 1, par_sFilter, par_sSort, sTopRec, par_sAdditionalFields);
					FinalizeData(ref oTable, par_iTop, "NEXT");

					return oTable;

				}
				else
				{

					return new DataTable();

				}

			}
			catch (Exception ex)
			{

				return new DataTable();

			}

		}

		public DataTable GetPrevious(string par_sFile, int par_iTop = -1, string par_sFilter = "", string par_sSort = "", string par_sTopRecordGID = "", string par_sAdditionalFields = "")
		{

			try
			{

				if (goData.IsFileValid(par_sFile))
				{

					if (par_sSort == "")
					{
						par_sSort = goData.GetDefaultSort(par_sFile);
					}

					clUtil oUtil = new clUtil();
					string sSort = oUtil.ReverseSort(par_sFile, par_sSort);

					string sTopRec = TopRecIDForPrevious;
					if (par_sTopRecordGID != "")
					{
						sTopRec = par_sTopRecordGID;
					}

					DataTable oTable = GetData(par_sFile, par_iTop + 2, par_sFilter, sSort, sTopRec, par_sAdditionalFields);
					FinalizeData(ref oTable, par_iTop, "PREVIOUS", par_sFile, par_sFilter, par_sSort, par_sAdditionalFields);

					return oTable;

				}
				else
				{

					return new DataTable();

				}

			}
			catch (Exception ex)
			{

				return new DataTable();

			}

		}

		public DataTable GetLast(string par_sFile, int par_iTop = -1, string par_sFilter = "", string par_sSort = "", string par_sTopRecordGID = "", string par_sAdditionalFields = "")
		{

			try
			{


				if (goData.IsFileValid(par_sFile))
				{

					if (par_sSort == "")
					{
						par_sSort = goData.GetDefaultSort(par_sFile);
					}

					clUtil oUtil = new clUtil();
					string sSort = oUtil.ReverseSort(par_sFile, par_sSort);

					DataTable oTable = GetData(par_sFile, par_iTop + 1, par_sFilter, sSort, "", par_sAdditionalFields);
					FinalizeData(ref oTable, par_iTop, "LAST");

					return oTable;

				}
				else
				{

					return new DataTable();

				}

			}
			catch (Exception ex)
			{

				return new DataTable();

			}

		}

		private DataTable GetData(string par_sFile, int par_iTop = -1, string par_sFilter = "", string par_sSort = "", string par_sTopRecordGID = "", string par_sAdditionalFields = "")
		{
			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			try
			{

				//field list
				string sFieldList = "SYS_NAME,GID_ID"; //,BI__ID"
				if (par_sAdditionalFields != "")
				{
					sFieldList = sFieldList + "," + par_sAdditionalFields;
				}

				//filter
				string sFilter = ""; //all records
				if (par_sFilter != "")
				{
					sFilter = par_sFilter;
				}

				//sort
				string sSort = goData.GetDefaultSort(par_sFile); //default sort for given file
				if (par_sSort != "")
				{
					sSort = par_sSort;
				}

				//top rec
				string sTopRec = "";
				if (par_sTopRecordGID != "")
				{
					if (goTR.IsTID(par_sTopRecordGID))
					{
						clUtil oUtil = new clUtil();
						sTopRec = oUtil.GetTopRec(par_sFile, sSort, par_sTopRecordGID);
					}
				}
				clRowSet oRS = new clRowSet(par_sFile, clC.SELL_READONLY, sFilter, sSort, sFieldList, par_iTop, "", "", "", "", sTopRec);
				oRS.ToTable();
				DataTable oTable = oRS.dtTransTable;

				RecordCount = goData.GetCount(par_sFile, sFieldList, sFilter);

				return oTable;

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return new DataTable();
			}

		}

		private void FinalizeData(ref DataTable par_oTable, int par_iTop, string sBrowseInstruct, string par_sFile = "", string par_sFilter = "", string par_sSort = "", string par_sAdditionalFields = "")
		{
			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			// Try
// INSTANT C# NOTE: The following VB 'Select Case' included either a non-ordinal switch expression or non-ordinal, range-type, or non-constant 'Case' expressions and was converted to C# 'if-else' logic:
//			Select Case par_oTable.Rows.Count
// ORIGINAL LINE: Case Is <= par_iTop
			if (par_oTable.Rows.Count <= par_iTop)
			{
					switch (sBrowseInstruct)
					{
						case "FIRST":
							PreviousEnabled = false;
							NextEnabled = false;
							TopRecIDForNext = "";
							TopRecIDForPrevious = "";
							break;
						case "PREVIOUS":
							par_oTable = GetFirst(par_sFile, par_iTop, par_sFilter, par_sSort, "", par_sAdditionalFields);
							break;
						case "NEXT":
							PreviousEnabled = (RecordCount > par_iTop);
							NextEnabled = false;
							TopRecIDForNext = "";
							TopRecIDForPrevious = par_oTable.Rows[0]["GID_ID"].ToString();
							break;
						case "LAST":
							PreviousEnabled = (RecordCount > par_iTop);
							NextEnabled = false;
							par_oTable = ReverseData(par_oTable);
							TopRecIDForNext = "";
							TopRecIDForPrevious = "";
							break;
						case "QUICKSELECT":
							PreviousEnabled = true;
							NextEnabled = false;
							TopRecIDForNext = "";
							TopRecIDForPrevious = par_oTable.Rows[0]["GID_ID"].ToString();
							break;
						default:
							PreviousEnabled = false;
							NextEnabled = false;
							TopRecIDForNext = "";
							TopRecIDForPrevious = "";
							break;
					}
			}
// ORIGINAL LINE: Case Is > par_iTop
			else if (par_oTable.Rows.Count > par_iTop)
			{
					switch (sBrowseInstruct)
					{
						case "FIRST":
							PreviousEnabled = false;
							NextEnabled = true;
							TopRecIDForNext = par_oTable.Rows[par_oTable.Rows.Count - 1]["GID_ID"].ToString();
							TopRecIDForPrevious = "";
							par_oTable.Rows.RemoveAt(par_oTable.Rows.Count - 1);
							break;
						case "PREVIOUS":
							if (par_oTable.Rows.Count == par_iTop + 2)
							{
								PreviousEnabled = true;
								NextEnabled = true;
								TopRecIDForNext = par_oTable.Rows[0]["GID_ID"].ToString();
								TopRecIDForPrevious = par_oTable.Rows[par_oTable.Rows.Count - 2]["GID_ID"].ToString();
								par_oTable.Rows.RemoveAt(par_oTable.Rows.Count - 1);
								par_oTable.Rows.RemoveAt(0);
								par_oTable = ReverseData(par_oTable);
							}
							else
							{
								//PreviousEnabled = False
								par_oTable = GetFirst(par_sFile, par_iTop, par_sFilter, par_sSort, "", par_sAdditionalFields);
							}
							break;
						case "NEXT":
							PreviousEnabled = true;
							NextEnabled = true;
							TopRecIDForNext = par_oTable.Rows[par_oTable.Rows.Count - 1]["GID_ID"].ToString();
							TopRecIDForPrevious = par_oTable.Rows[0]["GID_ID"].ToString();
							par_oTable.Rows.RemoveAt(par_oTable.Rows.Count - 1);
							break;
						case "LAST":
							PreviousEnabled = true;
							NextEnabled = false;
							TopRecIDForNext = "";
							TopRecIDForPrevious = par_oTable.Rows[par_oTable.Rows.Count - 2]["GID_ID"].ToString();
							par_oTable.Rows.RemoveAt(par_oTable.Rows.Count - 1);
							par_oTable = ReverseData(par_oTable);
							break;
						case "QUICKSELECT":
							PreviousEnabled = true;
							NextEnabled = true;
							TopRecIDForNext = par_oTable.Rows[par_oTable.Rows.Count - 1]["GID_ID"].ToString();
							TopRecIDForPrevious = par_oTable.Rows[0]["GID_ID"].ToString();
							par_oTable.Rows.RemoveAt(par_oTable.Rows.Count - 1);
							break;
						default:
							PreviousEnabled = false;
							NextEnabled = false;
							TopRecIDForNext = "";
							TopRecIDForPrevious = "";
							break;
					}
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		private DataTable ReverseData(DataTable par_oTable)
		{
			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			try
			{
				DataTable oNewDT = par_oTable.Clone();
				int i = 0;
				for (i = par_oTable.Rows.Count - 1; i >= 0; i--)
				{
					DataRow oRow;
					oRow = par_oTable.Rows[i];
					oNewDT.ImportRow(oRow);
				}
				return oNewDT;
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return par_oTable;
			}
		}

        public string GetFirstSortField(string sOrder, ref string sOper)
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            try
            {
                string[] aOrder = sOrder.Split(new string[] { ", " }, StringSplitOptions.None);
                sOrder = aOrder[0];
                sOper = ">=";
                int iPos = sOrder.ToUpper().IndexOf(" A") + 1;
                if (iPos > 0)
                {
                    sOrder = goTR.FromTo(sOrder, 1, iPos - 1);
                    sOper = ">=";
                }
                iPos = sOrder.ToUpper().IndexOf(" D") + 1;
                if (iPos > 0)
                {
                    sOrder = goTR.FromTo(sOrder, 1, iPos - 1);
                    sOper = "<=";
                }
                return sOrder;
            }
            catch (Exception ex)
            {
                // If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
                //     goErr.SetError(ex, 45105, sProc)
                // End If
                return "";
            }
        }
    }

}
