﻿CREATE TABLE [dbo].[QT_Involves_US] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Quote_Involves_User_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_QT] UNIQUEIDENTIFIER NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_QT_Involves_US] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_QT_Involves_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_InvolvedIn_QT] FOREIGN KEY ([GID_QT]) REFERENCES [dbo].[QT] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[QT_Involves_US] NOCHECK CONSTRAINT [LNK_QT_Involves_US];


GO
ALTER TABLE [dbo].[QT_Involves_US] NOCHECK CONSTRAINT [LNK_US_InvolvedIn_QT];


GO
CREATE CLUSTERED INDEX [IX_US_InvolvedIn_QT]
    ON [dbo].[QT_Involves_US]([GID_QT] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QT_Involves_US]
    ON [dbo].[QT_Involves_US]([GID_US] ASC);

