﻿@model Selltis.Core.Grid
@using Kendo.Mvc.UI;

<style>
    /*To set bustton style for MLS columns..S1*/
    .IconButton {
        /*display: inline;*/
        padding: 0.0em 0.6em 0.2em;
        /*font-size: 84%;
    font-weight: 600;*/
        color: #ffffff;
        /*text-align: center;*/
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 5px;
        width: 80% !important;
        /*max-width: 150px;*/
        display: inline-block;
        height: 15px;
    }


    .k-grid-content{
        min-height:200px;
        height:auto !important;
        overflow-x:auto !important;
    }

    .k-grid-content {
    position: relative;
    /*width: 100%;*/
     /*overflow: inherit !important; 
     overflow-x: inherit !important; 
     overflow-y: inherit !important;*/
    zoom: 1;
}

    .k-widget.k-grid.k-secondary.k-reorderable{
        /*min-width:100% !important;
        width:auto !important;*/
        /*overflow-x:auto !important;*/
    }
</style>
@*<script src="~/Scripts/jquery-2.1.4.min.js" type="text/javascript"></script>
    <script src="~/Scripts/jquery-ui-1.11.4.min.js" type="text/javascript"></script>*@

@*<script src="https://kendo.cdn.telerik.com/2014.3.1029/js/kendo.all.min.js"></script>*@
@*<script src="https://kendo.cdn.telerik.com/2015.3.930/js/kendo.all.min.js"></script>

    <script src="https://kendo.cdn.telerik.com/2015.3.930/js/kendo.web.min.js" type="text/javascript"></script>
    <script src="https://kendo.cdn.telerik.com/2015.3.930/js/kendo.aspnetmvc.min.js" type="text/javascript"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/2.4.0/jszip.js"></script>*@

<!--Kendo UI version Upgraded to 2017-->
@*<script src="~/Content/themes/Selltis/scripts/2017.1.223/kendo.all.min.js" type="text/javascript"></script>
<script src="~/Content/themes/Selltis/scripts/2017.1.223/kendo.aspnetmvc.min.js" type="text/javascript"></script>*@

<script src="~/Content/themes/Selltis/scripts/2017.3.1026/kendo.all.min.js" type="text/javascript"></script>
<script src="~/Content/themes/Selltis/scripts/2017.3.1026/kendo.aspnetmvc.min.js" type="text/javascript"></script>

<script src="~/Content/themes/Selltis/scripts/kendo.web.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/2.4.0/jszip.js"></script>
@*for kendo*@
@*<link href="~/Content/themes/Selltis/css/kendo.common-bootstrap.min.css" rel="stylesheet" />
    <link href="~/Content/themes/Selltis/css/kendo.bootstrap.min.css" rel="stylesheet" />*@

<link href="~/Content/themes/Selltis/css/Custom/Grid.css" rel="stylesheet" type="text/css" /> @*Grid Specific*@

@(Html.Kendo().Grid<dynamic>()
            .Name("kendogrid" + Model.ViewId.Replace(" ", ""))
        .Columns(columns =>
        {
        if (Model != null)
        {
            // Open anchor column
                @*if (Model.ViewRecordOpen != "1")
                {
                    columns.Template(@<text></text>).ClientTemplate("<a style=\"cursor: pointer;\" class=\"AnchorOpen\" onclick=\"OpenGridClick(this,'"+Model.File+"','"+Model.ViewId.Replace(" ","")+"')\">Open</a>").Width(40);
                }*@
                foreach (var Column in Model.Columns)
                {
                    if (Column.Title != "GID_ID")
                    {
                        var c = columns.Bound(Column.Name.Replace(",", "").Replace("<%", "").Replace("%>", "").Replace(" ", "")).Title(Column.Title);
                        //.Width(Column.Width);

                        if (Column.Name.ToUpper().Contains("MMO") || Column.Name.ToUpper().Contains("MMR") || Column.Name.ToUpper().Contains("JOURNAL") || Column.Name.ToUpper().Contains("NOTES"))
                        {
                            c.Width(500);
                        }

                        //if (Column.Alignment == "C" || Column.Alignment.ToUpper() == "CENTER")
                        //    c.HtmlAttributes(new { @style = "text-align:center;" });
                        //else if (Column.Alignment == "R" || Column.Alignment.ToUpper() == "RIGHT")
                        //    c.HtmlAttributes(new { @style = "text-align:right;" });

                        bool _IsLicon = false;
                        // ADR_

                        if (Column.Name.Contains("CHK_") && Column.Name.Split('_').Length > 0)
                        {
                            if (Column.Name.Split('_')[0].Equals("CHK"))
                            {
                                _IsLicon = true;
                            }
                        }

                        if (Column.Name.Contains("CUR_") || Column.Name.Contains("SI__"))
                        {
                            c.ClientTemplate("#=FormatNegativeValue(data." + Column.Name + ",\'" + Column.Name.ToUpper() + "\')#");
                        }

                        if (Column.IsIcon == true || _IsLicon)
                        {
                            c.ClientTemplate("#=GridTemplateData(data." + Column.Name + ",\'" + Column.Name.ToUpper() + "\')#");
                        }

                        //To set bustton style for MLS columns..J
                        if (Column.IsButton == true)
                        {
                            c.Encoded(false);
                            //c.HtmlAttributes(new { @style = "text-align:center;" });
                        }

                        //To set bustton style for MLS columns..S1
                        if (Column.IsIconButton == true)
                        {
                            c.Encoded(false);
                            //c.HtmlAttributes(new { @style = "text-align:center;" });
                        }

                        if (Column.IsLink == true)
                        {

                            if (Column.Name.ToUpper().Contains("EML_"))
                            {
                                string colValue = "#:data." + Column.Name + "#";
                                c.ClientTemplate("<a href='mailto:'" + colValue + "?Subject=''>" + colValue + "</a>");
                            }
                            else if (Column.Name.ToUpper().Contains("LNK_"))
                            {
                                c.ClientTemplate("#=getLinks(data." + Column.Name + ",\'" + Column.Name.ToUpper() + "\')#");
                            }
                        }
                        if (Column.Name.Contains("ADV_"))
                        {
                            c.ClientTemplate("#= data." + Column.Name + "#");
                        }
                        if (Column.IsSortable == false)
                        {
                            c.Sortable(false).HeaderHtmlAttributes(new { @title = "This column is not sortable" });
                        }
                        else
                        {
                            //set sort icon for default sort field
                            if (Column.Name == Model.DefaultSortField)
                            {
                                if (Model.DefaultSortDirection == "DESC")
                                {
                                    c.Sortable(false).HeaderTemplate("<span title = 'This column is sortable' name = 'SortableField' class='k-link' onclick=\"onGridColumnSortClick('" + Column.Name + "', '" + Model.ViewId.Replace(" ", "") + "', '" + Model.TableName + "')\" > " + Column.Title + "   <span id='" + Model.Key + "_" + Model.ViewId.Replace(" ", "") + "_" + Column.Name.Replace(" ", "") + "_SortableFieldIcon' class='fa fa-long-arrow-down' >");
                                }
                                else
                                {
                                    c.Sortable(false).HeaderTemplate("<span title = 'This column is sortable' name = 'SortableField' class='k-link' onclick=\"onGridColumnSortClick('" + Column.Name + "', '" + Model.ViewId.Replace(" ", "") + "', '" + Model.TableName + "')\" > " + Column.Title + "   <span id='" + Model.Key + "_" + Model.ViewId.Replace(" ", "") + "_" + Column.Name.Replace(" ", "") + "_SortableFieldIcon' class='fa fa-long-arrow-up' >");
                                }
                            }
                            else
                            {
                                c.Sortable(false).HeaderTemplate("<span title = 'This column is sortable' name = 'SortableField' class='k-link' onclick=\"onGridColumnSortClick('" + Column.Name + "', '" + Model.ViewId.Replace(" ", "") + "', '" + Model.TableName + "')\" > " + Column.Title + "   <span id='" + Model.Key + "_" + Model.ViewId.Replace(" ", "") + "_" + Column.Name.Replace(" ", "") + "_SortableFieldIcon' class='fa fa-long-arrow-up' style='display: none;' >");
                            }
                            //c.Sortable(false).HeaderTemplate("<span title = 'This column is sortable' name = 'SortableField' class='k-link' onclick=\"onGridColumnSortClick('" + Column.Name + "', '" + Model.ViewId.Replace(" ", "") + "', '" + Model.TableName + "')\" > " + Column.Title + "   <span id='" + Model.ViewId.Replace(" ", "") + "_" + Column.Name.Replace(" ", "") + "_SortableFieldIcon' class='fa fa-long-arrow-up' style='display: none;' >");
                        }
                    }
                }

            }
        })
        .Selectable(s => { s.Mode(GridSelectionMode.Single); })
        .Navigatable()
        .Events(e => e.DataBound("GridDataBound"))
        //.HtmlAttributes(new { @style = "width:99.8%;" })
        .Scrollable()
        .Sortable(s => { s.AllowUnsort(false); })
        .Reorderable(reorderable => reorderable.Columns(true))
        .Resizable(resize => resize.Columns(true))
        .DataSource(dataSource => dataSource
         .Ajax()
         .PageSize(Model.PageSize)
                     //.Read(read => read.Action("ReadData", "ViewPrint", new { ViewKey = Model.ViewKey, MasterSelID = Model.MasterSelID, Key = Model.Key }))
                     //.Read(read => read.Action("ReadData_V2", "LoadView", new { ViewKey = Model.ViewKey, firstload = Model.FirstLoad, Key = Model.Key }))
                     .Read(read => read.Action("ReadData_SendJob", "Report", new { ViewKey = Model.ViewKey, firstload = Model.FirstLoad, _ViewType = "GRID", Key = Model.Key }))

        )
)

<script src="~/Content/themes/Selltis/scripts/Custom/Grid.js" type="text/javascript"></script>
<script>


    $(document).ready(function () {

    })

    function GridDataBound(e) {
        //debugger;
        var Target = sessionStorage.getItem("Target");
        var PrintActionMode = sessionStorage.getItem('PrintActionMode');
        if (PrintActionMode.toLowerCase() == "view") {
            //RunGridSpecifiedAction('view', 0);
            setTimeout(RunGridSpecifiedAction, 2000, 'view', 0);
        }
        else if (PrintActionMode.toLowerCase() == "desktop") {
            var viewCount = sessionStorage.getItem('desktopViewCount');
            viewCount = (viewCount == null || viewCount == undefined) ? 1 : viewCount;
            if (viewCount == '@Selltis.Core.Util.GetSessionValue("_desktopViewCount")') {
                //RunGridSpecifiedAction('desktop', viewCount);
                setTimeout(RunGridSpecifiedAction, 2000, 'desktop', viewCount);
                sessionStorage.setItem('desktopViewCount', 0);
            }
            else {
                viewCount = (viewCount == null || viewCount == undefined) ? 0 : viewCount;
                sessionStorage.setItem('desktopViewCount', parseInt(viewCount) + 1);
            }
        }
    }

    @*function RunGridSpecifiedAction(_Mode, viewCount) {
                    //debugger;
        var grid = $("#kendogrid" + '@Model.ViewId.Replace(" ", "")').data("kendoGrid");

        var TXT_ToText = sessionStorage.getItem("TXT_To");
        var TXT_CcText = sessionStorage.getItem("TXT_Cc");
        var TXT_SubjectText = sessionStorage.getItem("TXT_Subject");
        var Target = sessionStorage.getItem("Target");
        var ViewId = sessionStorage.getItem("ViewId");
        var EDT_FIRSTNRECORDSText = sessionStorage.getItem("EDT_FIRSTNRECORDSText");
        var SEL_VIEWOPTIONSSelectedIndex = sessionStorage.getItem("SEL_VIEWOPTIONSSelectedIndex");

        if (SEL_VIEWOPTIONSSelectedIndex == null || SEL_VIEWOPTIONSSelectedIndex == "")
        {
            SEL_VIEWOPTIONSSelectedIndex = 0;
        }

        var siteN = location.protocol + '//' + location.host;

        if (Target == "EXCEL") {
            //debugger;
            var EmailGridHtml = grid.table.context.innerHTML.toString();
            var EmailGridHtml = encodeURI(EmailGridHtml);
            $.ajax({
                url: '/ViewPrint/btn_OK_Click',
                dataType: 'json',
                type: 'POST',
                async: false,
                data: { EmailGridHtml: EmailGridHtml, Target: Target, ViewKey: '@Model.ViewKey', EDT_FIRSTNRECORDSText: EDT_FIRSTNRECORDSText, SEL_VIEWOPTIONSSelectedIndex: SEL_VIEWOPTIONSSelectedIndex, TXT_ToText: TXT_ToText, TXT_CcText: TXT_CcText, TXT_SubjectText: TXT_SubjectText, _viewCount: viewCount, siteName: siteN, Key: '@Model.Key', View_Mode: _Mode },
                success: function (data) {
                    //debugger;
                    if (data == "success") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }
                    else if (data == "desktop") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }

                },
                error: function (data) {
                    //debugger;
                }
            })
        }


        else if (Target == "HTMLEMAIL") {
            //debugger;
            var EmailGridHtml = grid.table.context.innerHTML.toString();
            EmailGridHtml = encodeURI(EmailGridHtml);

            $.ajax({
                url: '/ViewPrint/btn_OK_Click',
                dataType: 'json',
                type: 'POST',
                async: false,
                data: { EmailGridHtml: EmailGridHtml, Target: Target, ViewKey: '@Model.ViewKey', EDT_FIRSTNRECORDSText: EDT_FIRSTNRECORDSText, SEL_VIEWOPTIONSSelectedIndex: SEL_VIEWOPTIONSSelectedIndex, TXT_ToText: TXT_ToText, TXT_CcText: TXT_CcText, TXT_SubjectText: TXT_SubjectText, _viewCount: viewCount, siteName: siteN, Key: '@Model.Key', View_Mode: _Mode },
                success: function (data) {
                    debugger;
                    if (data.PNL_MessageBoxVisible == true) {
                        DisplayMessageBox(data);
                        $('#AlertModel').show();
                        $("#myModalPrint").modal('show');
            }
                    else if (_Mode == "view") {
                        window.close();
                    }
                    else if (data == "desktop") {
                        window.close();
                    }

                },
                error: function (data) {
                    //debugger;
                }
            })
        }

        else if (Target == "PDF") {
            var EmailGridHtml = grid.table.context.innerHTML.toString();
            var EmailGridHtml = encodeURI(EmailGridHtml);
            //debugger;
            $.ajax({
                url: '/ViewPrint/btn_OK_Click',
                dataType: 'json',
                type: 'POST',
                async: false,
                data: { EmailGridHtml: EmailGridHtml, Target: Target, ViewKey: '@Model.ViewKey', EDT_FIRSTNRECORDSText: EDT_FIRSTNRECORDSText, SEL_VIEWOPTIONSSelectedIndex: SEL_VIEWOPTIONSSelectedIndex, TXT_ToText: TXT_ToText, TXT_CcText: TXT_CcText, TXT_SubjectText: TXT_SubjectText, _viewCount: viewCount, siteName: siteN, Key: '@Model.Key', View_Mode: _Mode },
                success: function (data) {
                    debugger;
                    if (data == "success") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }
                    else if (data == "desktop") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }

                },
                error: function (data) {
                    //debugger;
                }
    })
        }
        else if (Target == "print") {
            //window.print();
        }
    }*@

    function DisplayMessageBox(data) {
        $("#PNL_MessageBox").show();
        $("#LBL_MsgBoxTitle").text(data.LBL_MsgBoxTitleText);
        var _LBL_MsgBoxMessage = data.LBL_MsgBoxMessageText.replace("/n/n", "</br></br>").replace("/n", "</br>");
        $("#LBL_MsgBoxMessage").html(_LBL_MsgBoxMessage);

        if (data.BTN_MsgBox1Visible == true) {
            $("#BTN_MsgBox1").show();

            $("#BTN_MsgBox1").attr('value', data.BTN_MsgBox1Text);
            $("#BTN_MsgBox1").prop('value', data.BTN_MsgBox1Text);
            $("#BTN_MsgBox1").html(data.BTN_MsgBox1Text);
        }
        else {
            $("#BTN_MsgBox1").hide();
        }

        if (data.BTN_MsgBox2Visible == true) {
            $("#BTN_MsgBox2").show();

            $("#BTN_MsgBox2").attr('value', data.BTN_MsgBox2Text);
            $("#BTN_MsgBox2").prop('value', data.BTN_MsgBox2Text);
            $("#BTN_MsgBox2").html(data.BTN_MsgBox2Text);
        }
        else {
            $("#BTN_MsgBox2").hide();
        }

        if (data.BTN_MsgBox3Visible == true) {
            $("#BTN_MsgBox3").show();

            $("#BTN_MsgBox3").attr('value', data.BTN_MsgBox3Text);
            $("#BTN_MsgBox3").prop('value', data.BTN_MsgBox3Text);
            $("#BTN_MsgBox3").html(data.BTN_MsgBox3Text);
        }
        else {
            $("#BTN_MsgBox3").hide();
        }
    }

    function onBTN_MsgBox1Click() {
        window.close();
    }
    function onBTN_MsgBox2Click() {
        window.close();
    }
    function onBTN_MsgBox3Click() {
        window.close();
    }

    function getLinks(entireValue, linkName) {
        if (entireValue != null) {
            var values = entireValue.split("#$%^&*");
            var names = values[0].split("\n");
            var gid_ids = values[1].split("\n");
            var htmlContent = "";
            if (names.length > 0) {
                for (var i = 0; i < names.length; i++) {
                    if (htmlContent) {
                        htmlContent = htmlContent + "&nbsp;" + names[i];
                    }
                    else {
                        htmlContent = names[i];
                    }
                }
            }
        }
        // console.log($.parseHTML(htmlContent));
        return htmlContent;
    }
    function GridTemplateData(data, ColumnName) {

        var GridContent = "";
        if (data.indexOf(".") >= 0) {
            var imgUrl = location.protocol + '//' + location.host;          //Added to load images in email and other devices(http(s))
            if (data.toLowerCase().indexOf("cus_") >= 0) {
                GridContent = "<img src='" + imgUrl + "/PublicFiles/" + '@Model.SiteId' + "/Images/" + data + "' alt='" + data + "' />";
            }
            else {
                GridContent = "<img src='" + imgUrl + "/Content/Images/" + data + "' alt='" + data + "' />";
            }
        }
        else {
            GridContent = "<span>" + data + "</span>";
        }
        return GridContent;
    }
    function FormatNegativeValue(data, ColumnName) {

        var GridContent = data;

        if (data != null && data.length > 0) {
            if (data.indexOf("-") >= 0) {
                GridContent = "<b style='color:red'>" + data + "</b>";
            }
            else {
                GridContent = data;
            }
        }
        return GridContent;
    }


    function RunGridSpecifiedAction(_Mode, viewCount) {
        //debugger;
        sessionStorage.setItem("RunSpecifiedRun", "true");
        var grid = $("#kendogrid" + '@Model.ViewId.Replace(" ", "")').data("kendoGrid");

        var TXT_ToText = sessionStorage.getItem("TXT_To");
        var TXT_CcText = sessionStorage.getItem("TXT_Cc");
        var TXT_SubjectText = sessionStorage.getItem("TXT_Subject");
        var Target = sessionStorage.getItem("Target");
        var ViewId = sessionStorage.getItem("ViewId");
        var EDT_FIRSTNRECORDSText = sessionStorage.getItem("EDT_FIRSTNRECORDSText");
        var SEL_VIEWOPTIONSSelectedIndex = sessionStorage.getItem("SEL_VIEWOPTIONSSelectedIndex");

        if (SEL_VIEWOPTIONSSelectedIndex == null || SEL_VIEWOPTIONSSelectedIndex == "") {
            SEL_VIEWOPTIONSSelectedIndex = 0;
        }

        var siteN = location.protocol + '//' + location.host;

        if (Target == "EXCEL") {
            //debugger;
            var _Page = encodeURI(document.documentElement.innerHTML);
            var siteN = location.protocol + '//' + location.host;
            $.ajax({
                url: '/ViewPrint/SendJob',
                dataType: 'json',
                type: 'POST',
                async: false,
                data: { Page: _Page, Target: Target, ViewKey: '@Model.ViewKey', EDT_FIRSTNRECORDSText: EDT_FIRSTNRECORDSText, SEL_VIEWOPTIONSSelectedIndex: SEL_VIEWOPTIONSSelectedIndex, TXT_ToText: TXT_ToText, TXT_CcText: TXT_CcText, TXT_SubjectText: TXT_SubjectText, _viewCount: viewCount, siteName: siteN, Key: '@Model.Key', View_Mode: _Mode, SiteName: siteN },
                success: function (data) {
                    //debugger;
                    if (data == "success") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }
                    else if (data == "desktop") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }

                },
                error: function (data) {
                    //debugger;
                }
            })
        }


        else if (Target == "HTMLEMAIL") {
            //debugger;
            var _Page = encodeURI(document.documentElement.innerHTML);
            var siteN = location.protocol + '//' + location.host;

            $.ajax({
                url: '/ViewPrint/SendJob',
                dataType: 'json',
                type: 'POST',
                async: false,
                data: { Page: _Page, Target: Target, ViewKey: '@Model.ViewKey', EDT_FIRSTNRECORDSText: EDT_FIRSTNRECORDSText, SEL_VIEWOPTIONSSelectedIndex: SEL_VIEWOPTIONSSelectedIndex, TXT_ToText: TXT_ToText, TXT_CcText: TXT_CcText, TXT_SubjectText: TXT_SubjectText, _viewCount: viewCount, siteName: siteN, Key: '@Model.Key', View_Mode: _Mode, SiteName: siteN },
                success: function (data) {
                    debugger;
                    //if (data.PNL_MessageBoxVisible == true) {
                    //    DisplayMessageBox(data);
                    //    $("#AlertModel").modal('show');
                    //    $('#AlertModel').html(modelPrint);
                    //    $("#myModalPrint").modal('show');

                    //    DisplayMessageBox(data);
                    //}
                    //else if (_Mode == "view") {
                    //    window.close();
                    //}
                    //else if (data == "desktop") {
                    //    window.close();
                    //}
                    window.close();

                },
                error: function (data) {
                    //debugger;
                }
            })
        }

        else if (Target == "PDF") {
            var _Page = encodeURI(document.documentElement.innerHTML);
            var siteN = location.protocol + '//' + location.host;
            //debugger;
            $.ajax({
                url: '/ViewPrint/SendJob',
                dataType: 'json',
                type: 'POST',
                async: false,
                data: { Page: _Page, Target: Target, ViewKey: '@Model.ViewKey', EDT_FIRSTNRECORDSText: EDT_FIRSTNRECORDSText, SEL_VIEWOPTIONSSelectedIndex: SEL_VIEWOPTIONSSelectedIndex, TXT_ToText: TXT_ToText, TXT_CcText: TXT_CcText, TXT_SubjectText: TXT_SubjectText, _viewCount: viewCount, siteName: siteN, Key: '@Model.Key', View_Mode: _Mode, SiteName: siteN },
                success: function (data) {
                    debugger;
                    if (data == "success") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }
                    else if (data == "desktop") {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                        window.close();
                    }

                },
                error: function (data) {
                    //debugger;
                }
            })
        }
        else if (Target == "print") {
            //window.print();
        }
    }

</script>