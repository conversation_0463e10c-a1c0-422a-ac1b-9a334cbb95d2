﻿CREATE TABLE [dbo].[OP_Related_RI] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_OP_Related_RI_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    [GID_RI] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_OP_Related_RI] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_OP_Related_RI] FOREIGN KEY ([GID_RI]) REFERENCES [dbo].[RI] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_RI_Connected_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[OP_Related_RI] NOCHECK CONSTRAINT [LNK_OP_Related_RI];


GO
ALTER TABLE [dbo].[OP_Related_RI] NOCHECK CONSTRAINT [LNK_RI_Connected_OP];


GO
CREATE NONCLUSTERED INDEX [IX_RI_Connected_OP]
    ON [dbo].[OP_Related_RI]([GID_RI] ASC, [GID_OP] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_Related_RI]
    ON [dbo].[OP_Related_RI]([GID_OP] ASC, [GID_RI] ASC);

