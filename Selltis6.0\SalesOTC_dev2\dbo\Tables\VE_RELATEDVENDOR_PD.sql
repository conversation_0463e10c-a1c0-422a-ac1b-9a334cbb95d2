﻿CREATE TABLE [dbo].[VE_RELATEDVENDOR_PD] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_VE_RELATEDVENDOR_PD_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_VE] UNIQUEIDENTIFIER NOT NULL,
    [GID_PD] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_VE_RELATEDVENDOR_PD] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PD_CONNECTEDPRODUCT_VE] FOREIGN KEY ([GID_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_VE_RELATEDVENDOR_PD] FOREIGN KEY ([GID_PD]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[VE_RELATEDVENDOR_PD] NOCHECK CONSTRAINT [LNK_PD_CONNECTEDPRODUCT_VE];


GO
ALTER TABLE [dbo].[VE_RELATEDVENDOR_PD] NOCHECK CONSTRAINT [LNK_VE_RELATEDVENDOR_PD];


GO
CREATE NONCLUSTERED INDEX [IX_PD_CONNECTEDPRODUCT_VE]
    ON [dbo].[VE_RELATEDVENDOR_PD]([GID_PD] ASC, [GID_VE] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_VE_RELATEDVENDOR_PD]
    ON [dbo].[VE_RELATEDVENDOR_PD]([GID_VE] ASC, [GID_PD] ASC);

