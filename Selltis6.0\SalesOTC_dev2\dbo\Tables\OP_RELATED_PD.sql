﻿CREATE TABLE [dbo].[OP_RELATED_PD] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_OP_RELATED_PD_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    [GID_PD] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_OP_RELATED_PD] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_OP_RELATED_PD] FOREIGN KEY ([GID_PD]) REFERENCES [dbo].[PD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PD_LINKED_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[OP_RELATED_PD] NOCHECK CONSTRAINT [LNK_OP_RELATED_PD];


GO
ALTER TABLE [dbo].[OP_RELATED_PD] NOCHECK CONSTRAINT [LNK_PD_LINKED_OP];


GO
CREATE NONCLUSTERED INDEX [IX_OP_RELATED_PD]
    ON [dbo].[OP_RELATED_PD]([GID_OP] ASC, [GID_PD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PD_LINKED_OP]
    ON [dbo].[OP_RELATED_PD]([GID_PD] ASC, [GID_OP] ASC);

