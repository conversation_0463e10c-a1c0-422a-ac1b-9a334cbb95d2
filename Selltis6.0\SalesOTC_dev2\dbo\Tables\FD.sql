﻿CREATE TABLE [dbo].[FD] (
    [GID_ID]               UNIQUEIDENTIFIER CONSTRAINT [DF_FD_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'FD',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]               BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]             NVARCHAR (80)    NULL,
    [DTT_CreationTime]     DATETIME         CONSTRAINT [DF_FD_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]         TINYINT          NULL,
    [TXT_ModBy]            VARCHAR (4)      NULL,
    [DTT_ModTime]          DATETIME         CONSTRAINT [DF_FD_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_FileDownloadName] NVARCHAR (50)    NULL,
    [MMO_ImportData]       NTEXT            NULL,
    [SI__ShareState]       TINYINT          CONSTRAINT [DF_FD_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]     UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]         VARCHAR (50)     NULL,
    [TXT_ExternalID]       NVARCHAR (80)    NULL,
    [TXT_ExternalSource]   VARCHAR (10)     NULL,
    [TXT_ImpJobID]         VARCHAR (20)     NULL,
    [TXT_TITLE]            NVARCHAR (1000)  NULL,
    [TXT_FILEURL]          NVARCHAR (2000)  NULL,
    [GID_TO_US]            UNIQUEIDENTIFIER NULL,
    [MMR_MESSAGE]          NTEXT            NULL,
    [URL_DownloadLink]     NTEXT            NULL,
    CONSTRAINT [PK_FD] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_FD_FileDownloadName]
    ON [dbo].[FD]([TXT_FileDownloadName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_FD_CreatedBy_US]
    ON [dbo].[FD]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_FD_ModDateTime]
    ON [dbo].[FD]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_FD_Name]
    ON [dbo].[FD]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_FD_CreationTime]
    ON [dbo].[FD]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_FD_BI__ID]
    ON [dbo].[FD]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_FD_TXT_ImportID]
    ON [dbo].[FD]([TXT_ImportID] ASC);

