﻿using Elmah;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Selltis.Core;
using Selltis.BusinessLogic;
namespace Selltis.MVC.Elmah
{
    public class ElmahHandledErrorLoggerFilter : IExceptionFilter
    {
        public void OnException(ExceptionContext filterContext)
        {
            // Log only handled exceptions, because all other will be caught by ELMAH anyway.
            //if (context.ExceptionHandled || context.RequestContext.HttpContext.Request.IsAjaxRequest())
            //    ErrorSignal.FromCurrentContext().Raise(context.Exception);

            if (filterContext.HttpContext.IsCustomErrorEnabled)
            {

                Exception ex = filterContext.Exception;// new Exception(filterContext.Exception);
                clError goErr = (clError)Util.GetInstance("err");
                goErr.SetError(ex, 45105, "ElmahHandeledException");

                //trigger elmah
                ErrorSignal.FromCurrentContext().Raise(filterContext.Exception);

                //get the last elmah error
                //  var errorList = new List<ErrorLogEntry>();
                // ErrorLog.GetDefault(filterContext.HttpContext.ApplicationInstance.Context).GetErrors(0, 1, errorList);
                //var error = filterContext.Exception;

                //var controllerName = (string)filterContext.RouteData.Values["controller"];
                //var actionName = (string)filterContext.RouteData.Values["action"];
                //var model = new HandleErrorInfo(filterContext.Exception, controllerName, actionName);
                // return the custom error page
                //filterContext.Result = new ViewResult
                //{
                //    ViewName = "~/Views/Shared/Error.cshtml",
                //   // MasterName = Master,
                //    ViewData = new ViewDataDictionary<HandleErrorInfo>(model),
                //    TempData = filterContext.Controller.TempData
                //};


                //stop further error processing
                filterContext.ExceptionHandled = true;
                
            }
            else
            {
              //  base.OnException(filterContext);
            }

        }
    }
}