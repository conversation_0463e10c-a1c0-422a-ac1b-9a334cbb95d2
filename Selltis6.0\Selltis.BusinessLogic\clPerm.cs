﻿using System;
using System.Data;
using System.Web;

//Owner: MI

namespace Selltis.BusinessLogic
{
	public class clPerm
	{
		//MI 10/24/13 LoginAdd, LoginEdit, LoginEditIgnorePassword: Deprecated par_dtBillingStartTime, par_dtDeactivationTime; added par_bBill.
		//MI 5/10/13 LoginAdd: Changed the duplicate login message to: 'The login record can't be added because a user login or login group with the same name exists. Please assign a unique name.'
		//MI 4/24/12 LoginAdd: removed test connection-closing code that was inadvertently left in.
		//MI 12/16/11 Added GenerateTemporaryLoginPassword, commented redundant sProc lines.
		//MI 3/13/07 Created for managing permissions access to/from XP table.
		//PURPOSE:
		//       Manage user permissions. Permissions are stored in the XP table,
		//       which is similar to the MD (metadata) table. Permissions data
		//       is organized in sections (GLOBAL for workgroup-level permissions,
		//       user ID for user-specific permissions) and pages (groups of property/
		//       value pairs that can be read with a single method). Individual properties
		//       (Admin=, AUTHOR=, AC_READ_FILTER=, etc.) can be assigned values.
		//       Pages can be read as ini strings using PageRead, and each property 
		//       can be read from the resulting string with goTr.StrRead.
		//       If a local (user's) permission isn't defined, a global value for the
		//       same property is sought and returned if found. If neither value
		//       is found, the default value is returned. If a default is not defined
		//       in the reading method, "" is returned.
		//       Values are stored as strings. Remember to convert them to the proper
		//       type, for example by using goTr.StringToCheckbox() when you need to
		//       convert a "1" or "0" into True/False.
		//       Unlike metadata, permissions do not support the language notion.
		//       Do not prefix properties with langauge codes (ex: US_NAME)

		private clProject goP;
		//Dim goMeta As clMetaData
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clLog goLog;
		//Dim goDef As clDefaults
		private clPerm goPerm;
		public System.Data.SqlClient.SqlConnection oMetaConnection = null;

		public string GenerateTemporaryLoginPassword()
		{
			//MI 12/15/11 Added. Used in diaAdmPerm.aspx.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			return goData.GenGuid().Substring(0, 12);

			//Alternative method with more control over how the password looks. From RH:

			//Dim charset As String = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890-+=\|/()[]{}*&^%$#@!"
			//Dim pass As String = ""
			//Dim r As New Random

			//For i As Integer = 0 To lenPass - 1
			//    pass += charset(r.Next(0, charset.Length))
			//Next

			//Return pass

		}

		public DateTime GetTemporaryPasswordExpirationTime(string par_sTZPerspective = "LOCAL")
		{
			//MI 3/6/12 Changed from 60 to 7.
			//MI 2/8/12 Made the parameter optional.
			//MI 1/9/12 Returning the time in local time zone.
			//MI 12/16/11 Added
			//PURPOSE:
			//       This is when the temporary password expires.
			//       Generate the temp password expiration time in local, server, or UTC time. 
			//       Can be tweaked here to be based on metadata, customized, etc. For now, it is 60 days.
			//RETURNS:
			//       Datetime: expiration datetime.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			switch (par_sTZPerspective)
			{
				case "LOCAL":
					return goTR.UTC_ServerToLocal(DateTime.Now.AddDays(7));
				case "SERVER":
					return DateTime.Now.AddDays(7);
				default:
					//UTC
					return goTR.UTC_ServerToUTC(DateTime.Now.AddDays(7));
			}

		}

		//public bool ResetAndSendPassword(int par_sCase, string par_sLoginName, string par_sSendToAddress, string par_sSendMethod, ref string par_sTempPassword, ref DateTime par_dtTempPasswordExpirationTime, ref string par_sMessage, ref string par_sErrorCode)
		//{
		//	string tempVar = "";
		//	return ResetAndSendPassword(par_sCase, par_sLoginName, par_sSendToAddress, par_sSendMethod, ref par_sTempPassword, ref par_dtTempPasswordExpirationTime, ref par_sMessage, ref par_sErrorCode, ref tempVar);
		//}

		//public bool ResetAndSendPassword(int par_sCase, string par_sLoginName, string par_sSendToAddress, string par_sSendMethod, ref string par_sTempPassword, ref DateTime par_dtTempPasswordExpirationTime, ref string par_sMessage)
		//{
		//	string tempVar = "";
		//	string tempVar2 = "";
		//	return ResetAndSendPassword(par_sCase, par_sLoginName, par_sSendToAddress, par_sSendMethod, ref par_sTempPassword, ref par_dtTempPasswordExpirationTime, ref par_sMessage, ref tempVar, ref tempVar2);
		//}

		//public bool ResetAndSendPassword(int par_sCase, string par_sLoginName, string par_sSendToAddress, string par_sSendMethod, ref string par_sTempPassword, ref DateTime par_dtTempPasswordExpirationTime)
		//{
		//	string tempVar = "";
		//	string tempVar2 = "";
		//	string tempVar3 = "";
		//	return ResetAndSendPassword(par_sCase, par_sLoginName, par_sSendToAddress, par_sSendMethod, ref par_sTempPassword, ref par_dtTempPasswordExpirationTime, ref tempVar, ref tempVar2, ref tempVar3);
		//}

		//public bool ResetAndSendPassword(int par_sCase, string par_sLoginName, string par_sSendToAddress, string par_sSendMethod, ref string par_sTempPassword)
		//{
		//	DateTime tempVar = clC.SELL_BLANK_DTDATETIME;
		//	string tempVar2 = "";
		//	string tempVar3 = "";
		//	string tempVar4 = "";
		//	return ResetAndSendPassword(par_sCase, par_sLoginName, par_sSendToAddress, par_sSendMethod, ref par_sTempPassword, ref tempVar, ref tempVar2, ref tempVar3, ref tempVar4);
		//}

		//public bool ResetAndSendPassword(int par_sCase, string par_sLoginName, string par_sSendToAddress, string par_sSendMethod)
		//{
		//	string tempVar = "";
		//	DateTime tempVar2 = clC.SELL_BLANK_DTDATETIME;
		//	string tempVar3 = "";
		//	string tempVar4 = "";
		//	string tempVar5 = "";
		//	return ResetAndSendPassword(par_sCase, par_sLoginName, par_sSendToAddress, par_sSendMethod, ref tempVar, ref tempVar2, ref tempVar3, ref tempVar4, ref tempVar5);
		//}

		//public bool ResetAndSendPassword(int par_sCase, string par_sLoginName, string par_sSendToAddress)
		//{
		//	string tempVar = "";
		//	DateTime tempVar2 = clC.SELL_BLANK_DTDATETIME;
		//	string tempVar3 = "";
		//	string tempVar4 = "";
		//	string tempVar5 = "";
		//	return ResetAndSendPassword(par_sCase, par_sLoginName, par_sSendToAddress, "EMAIL", ref tempVar, ref tempVar2, ref tempVar3, ref tempVar4, ref tempVar5);
		//}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Public Function ResetAndSendPassword(ByVal par_sCase As Integer, ByVal par_sLoginName As String, ByVal par_sSendToAddress As String, Optional ByVal par_sSendMethod As String = "EMAIL", Optional ByRef par_sTempPassword As String = "", Optional ByRef par_dtTempPasswordExpirationTime As DateTime = clC.SELL_BLANK_DTDATETIME, Optional ByRef par_sMessage As String = "", Optional ByRef par_sErrorCode As String = "", Optional ByRef par_sErrorMessage As String = "") As Boolean
		public bool ResetAndSendPassword(string par_sCase, string par_sLoginName, string par_sSendToAddress, string par_sSendMethod, ref string par_sTempPassword, ref DateTime par_dtTempPasswordExpirationTime, ref string par_sMessage, ref string par_sErrorCode, ref string par_sErrorMessage)
		{
			//MI 2/2/12 Created.
			//PURPOSE:
			//       Generate a new password and send an email to the user. If par_sCase='SELFSIGNUP',
			//       also write the new password and other data to the XU record.
			//       To generate the password only, run clPerm.GenerateTemporaryLoginPassword().
			//PARAMETERS:
			//       par_sCase: the following values are currently supported: 
			//           ADMINRESET      Admin resetting the password in diaAdmPerm; XU record not edited
			//           ADMINWELCOME    Admin welcoming a new user in diaAdmPerm; XU record not edited
			//           SELFSIGNUP      Self-registered eval user signing up; XU record edited
			//       par_sLoginName: The value of XU.TXT_LogonName. There is a unique constraint on that column in 
			//           the database. Currently only used in SELFSIGNUP case. Pass "" otherwise.
			//       par_sSendToAddress: e-mail address, e.g. '<EMAIL>'.
			//       par_sSendMethod: Optional: 'EMAIL'. May support other delivery method we may support in the future. 
			//           Keep the supported values the same as in TEMPPASSWORDDELIVERYMETHOD (see diaAdmPerm).
			//           Anything other than 'EMAIL' (e.g. 'VERBAL') will raise an error.
			//       par_sTempPassword: optional ByRef, returns the generated password, if of interest in the calling code.
			//       par_dtTempPasswordExpirationTime: optional ByRef, returns the datetime when the temporary password expires.
			//       par_sMessage: optional ByRef, returns the message to display in a messagebox to the user (admin).
			//       par_sErrorCode: optional ByRef, returns the error code if an error occurred.
			//       par_sErrorMessage: optional ByRef, returns the error message if an error occurred.
			//RETURNS:
			//       True or false: success or failure. In case of failure, error code and
			//       message can be retrieved from par_sErrorCode and par_sErrorMessage params.
			//       
			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sPassword = null;
			string sBody = null;
			clRowSet doRS = null;
			DateTime dtTempPasswordGeneratedTime = default(DateTime);

			//Try
			//Add cases for all supported methods
			switch (par_sSendMethod)
			{
					case "EMAIL":
					break;
						//Supported
					default:
						goErr.SetError(10103, sProc, "", "par_sSendMethod", sProc, par_sSendMethod);
						break;
						//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
				}

				switch (par_sCase)
				{
					case "SELFSIGNUP":
						//We edit the XU record below
						doRS = new clRowSet("XU", clC.SELL_EDIT, "TXT_LogonName='" + par_sLoginName + "'", "", "CHK_PasswordIsTemporary, DTT_TempPasswordExpirationTime, DTT_TempPasswordGeneratedTime, TXT_TempPasswordDeliveryMethod, TXT_TempPasswordGeneratedBy, TXT_TempPasswordSentTo, DTT_PasswordChangedTime");
						if (doRS.Count() < 1)
						{
							par_sErrorCode = "";
							par_sErrorMessage = "Login '" + par_sLoginName + "' not found.";
							par_sMessage = par_sErrorMessage + " The login may have been deleted since you opened it. The password was not reset.";
							return false;
						}
						else
						{
							//The record was found, we'll edit it below
						}
						break;
					default:
					break;
						//XU record won't be edited here, be sure the calling code edits it!
				}

				//Generate the temporary password
				sPassword = goPerm.GenerateTemporaryLoginPassword();
				dtTempPasswordGeneratedTime = goTR.UTC_ServerToUTC(DateTime.Now);
				par_dtTempPasswordExpirationTime = GetTemporaryPasswordExpirationTime("UTC");

				sBody = "Your Selltis password has been reset. You have been assigned the following temporary password:" + "\r\n" + "\r\n";
				sBody += sPassword + "\r\n" + "\r\n";
				sBody += "It will expire on " + goTR.DateToString(par_dtTempPasswordExpirationTime) + " UTC. " + "\r\n" + "\r\n";
				sBody += "After logging in with your temporary password, you will be prompted to enter a new password." + "\r\n" + "\r\n";
				sBody += "If you need assistance, e-mail our support <NAME_EMAIL> or call ************ Monday - Friday, 8am - 5pm CST." + "\r\n" + "\r\n";
				sBody += "Thank you for using Selltis." + "\r\n" + "\r\n";
				sBody += "The Selltis Support Team" + "\r\n" + "\r\n" + "Selltis, LLC" + "\r\n" + "http://www.selltis.com" + "\r\n";
				sBody += "Tel: (************* (support)" + "\r\n" + "Tel: (************* (main)" + "\r\n" + "3500 Hwy 190, Ste 200" + "\r\n" + "Mandeville, LA 70471" + "\r\n" + "USA";

				//--> Add other send method Cases if appropriate
				switch (par_sSendMethod.ToUpper())
				{
					case "EMAIL":
						clEmail oSend = new clEmail();
						if (!oSend.SendSMTPEmailNew("Password reset", sBody, par_sSendToAddress, "", "", "", "Selltis Support", "<EMAIL>", "", false, "", "", true, false))
						{
							//Undo gw.tLogins.SetVals set above?
							par_sErrorCode = goErr.GetLastError("NUMBER");
							par_sErrorMessage = goErr.GetLastError("MESSAGE");
							par_sMessage = goErr.GetLastError("MESSAGE") + " (" + goErr.GetLastError() + "). View, copy, and e-mail the temporary password manually.";
							return false;
						}
						else
						{
							par_sMessage = "Password change notification sent to '" + par_sSendToAddress + "'.";
							//Edit the XU record
							switch (par_sCase)
							{
								case "SELFSIGNUP":
									doRS.SetFieldVal("CHK_PasswordIsTemporary", "1");
									doRS.SetFieldVal("DTT_TempPasswordExpirationTime", par_dtTempPasswordExpirationTime, clC.SELL_SYSTEM);
									doRS.SetFieldVal("DTT_TempPasswordGeneratedTime", dtTempPasswordGeneratedTime, clC.SELL_SYSTEM);
									doRS.SetFieldVal("TXT_TempPasswordDeliveryMethod", "");
									doRS.SetFieldVal("TXT_TempPasswordGeneratedBy", "");
									doRS.SetFieldVal("TXT_TempPasswordSentTo", "");
									doRS.SetFieldVal("DTT_PasswordChangedTime", "");
									break;
							}
						}
						break;
					default:
						goErr.SetError(10103, sProc, "", "par_sSendMethod", sProc, par_sSendMethod);
						break;
						//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public string GetDataAccessPermLetter(string par_sPermType, int par_iPermLevel, bool par_bDefault = false)
		{
			//MI 3/27/07 Created.
			//PUPROSE:
			//       Return a letter to display in the caption for each file's selective access permissions
			//       for each permission type (Read, add, edit, delete). Note that the permissions level of
			//       -1 (default) is not allowed in this context, only 0-2 (none-full)
			//PARAMETERS:
			//       par_sPermType: A, E, D, or R.
			//       par_iPermLevel: 0 (none), 1 (selective), 2 (full). -1 will raise an error.
			//       par_bDefault: when true [default: False], the letter is returned enclosed in parens, ex: (A) or (r)
			//RETURNS:
			//       String up to 3 characters (parens around the letter when level is default.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			//goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

			string sResult = "";

			switch (par_sPermType.ToUpper())
			{
				case "R": //Read
					switch (par_iPermLevel)
					{
						case 0: //None
							if (par_bDefault)
							{
								sResult = " "; //"( )"
							}
							else
							{
								sResult = " *";
							}
							break;
						case 1: //Selective
							if (par_bDefault)
							{
								sResult = "r";
							}
							else
							{
								sResult = "r*";
							}
							break;
						case 2: //Full
							if (par_bDefault)
							{
								sResult = "R";
							}
							else
							{
								sResult = "R*";
							}
							break;
						default:
							goErr.SetError(10103, sProc, "", "par_sPermType", sProc, par_sPermType);
							//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
							sResult = "";
							break;
					}
					break;
				case "A": //Add
					switch (par_iPermLevel)
					{
						case 0: //None
							if (par_bDefault)
							{
								sResult = " ";
							}
							else
							{
								sResult = " *";
							}
							break;
						case 1: //Selective--NOT SUPPORTED, but not removing to avoid introducing issues (MI 10/30/13)
							if (par_bDefault)
							{
								sResult = "a";
							}
							else
							{
								sResult = "a*";
							}
							break;
						case 2: //Full
							if (par_bDefault)
							{
								sResult = "A";
							}
							else
							{
								sResult = "A*";
							}
							break;
						default:
							goErr.SetError(10103, sProc, "", "par_sPermType", sProc, par_sPermType);
							//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
							sResult = "";
							break;
					}
					break;
				case "E": //Edit
					switch (par_iPermLevel)
					{
						case 0: //None
							if (par_bDefault)
							{
								sResult = " ";
							}
							else
							{
								sResult = " *";
							}
							break;
						case 1: //Selective
							if (par_bDefault)
							{
								sResult = "e";
							}
							else
							{
								sResult = "e*";
							}
							break;
						case 2: //Full
							if (par_bDefault)
							{
								sResult = "E";
							}
							else
							{
								sResult = "E*";
							}
							break;
						default:
							goErr.SetError(10103, sProc, "", "par_sPermType", sProc, par_sPermType);
							//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
							sResult = "";
							break;
					}
					break;
				case "D": //Delete
					switch (par_iPermLevel)
					{
						case 0: //None
							if (par_bDefault)
							{
								sResult = " ";
							}
							else
							{
								sResult = " *";
							}
							break;
						case 1: //Selective
							if (par_bDefault)
							{
								sResult = "d";
							}
							else
							{
								sResult = "d*";
							}
							break;
						case 2: //Full
							if (par_bDefault)
							{
								sResult = "D";
							}
							else
							{
								sResult = "D*";
							}
							break;
						default:
							goErr.SetError(10103, sProc, "", "par_sPermType", sProc, par_sPermType);
							//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
							sResult = "";
							break;
					}
					break;
				default:
					goErr.SetError(10103, sProc, "", "par_sPermType", sProc, par_sPermType);
					//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
					sResult = "";
					break;
			}

			return sResult;
		}


		public string GetCreatorID(string par_sSection, string par_sPageID)
		{
			//MI 12/5/06 Created
			//PURPOSE:
			//       Return the ID of the user who created this permissions page.
			//PARAMETERS:
			//       par_sSection: Section ID of the page such as user's GID_ID
			//           or "GLOBAL"
			//       par_sPageID: page ID of the page such as "DSK_Xxxxxx" or
			//           'WOP_WORKGROUP_OPTIONS"
			//RETURNS:
			//       GID_CreatedBy_US value as string.

			//Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
			//goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

			string sSection = par_sSection.ToString();
			string sText = "";

			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader reader = null;
			Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();

			cmd.CommandText = "pPermGetCreatorID";
			//Returns column GID_CreatedBy_US with one record
			cmd.CommandType = CommandType.StoredProcedure;
			cmd.Connection = sqlConnection1;

			//parameter
			System.Data.SqlClient.SqlParameter uSec = new System.Data.SqlClient.SqlParameter("@par_uSection", System.Data.SqlDbType.UniqueIdentifier);
			if (sSection.ToUpper() == "GLOBAL" || sSection == "")
			{
				uSec.Value = System.DBNull.Value;
			}
			else
			{
				uSec.Value = goTR.StringToGuid(sSection);
			}
			cmd.Parameters.Add(uSec);

			//parameter
			System.Data.SqlClient.SqlParameter sPageID = new System.Data.SqlClient.SqlParameter("@par_sPageID", System.Data.SqlDbType.VarChar);
			sPageID.Value = par_sPageID;
			cmd.Parameters.Add(sPageID);

			reader = cmd.ExecuteReader();
			if (reader.Read())
			{
				sText = reader[0].ToString();
			}

			reader.Close();
			sqlConnection1.Close();

			return sText;

		}


		public string GetDefaultFeaturePermissions()
		{
			//MI 2/20/14 Added GOOGLECAL, GOOGLECONTACT.
			//MI 12/6/13 Added LINK_OUTLOOKTODO.
			//MI 10/29/13 Removed customer admin permission, which are now under Roles. Reordered, commented lines.
			//MI 5/18/11 Added DOCLIBxxx, CUSTADMINxxx.
			//MI 5/13/11 Added CUSTADMINREPORTS
			//MI 5/14/09 added FORMS.
			//MI 3/27/09 Added VIEWS.
			//MI 3/5/09 Added MOBILEREMEMBERME, MOBILEEXPIRELOGININDAYS, MOBILEREMEMBERUSERNAME permissions.
			//MI 2/5/09 Added MOBILE.
			//MI 5/6/08 Added SHAREDDESKTOPS.
			//MI 5/5/08 Added LINK_OUTLOOKCAL and LINK_OUTLOOKCONTACT
			//PURPOSE:
			//       Returns an ini string with feature permission defaults
			//       Permissions that are not explicitly defined here will
			//       have a default of 0.
			//       The only other method where individual permissions are hard-coded
			//       is clProject.GetMe(). Maintain these two methods in sync.

			//Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
			//goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

			string s = "";

			goTR.StrWrite(ref s, "PRINT", "1");
			goTR.StrWrite(ref s, "TOEXCEL", "0");
			goTR.StrWrite(ref s, "IMPORT", "0");
			goTR.StrWrite(ref s, "EXPORT", "0");
			goTR.StrWrite(ref s, "TRANSFERIN", "0");
			goTR.StrWrite(ref s, "TRANSFEROUT", "0");
			goTR.StrWrite(ref s, "LINK_OUTLOOKCAL", "1");
			goTR.StrWrite(ref s, "LINK_OUTLOOKCONTACT", "1");
			goTR.StrWrite(ref s, "LINK_OUTLOOKTODO", "1");
			goTR.StrWrite(ref s, "LINK_GOOGLECAL", "1");
			goTR.StrWrite(ref s, "LINK_GOOGLECONTACT", "1");
			goTR.StrWrite(ref s, "MOBILE", "0");
			goTR.StrWrite(ref s, "MOBILEREMEMBERME", "1");
			goTR.StrWrite(ref s, "MOBILEEXPIRELOGININDAYS", clC.SELL_MOBILEEXPIRELOGININDAYS.ToString());
			goTR.StrWrite(ref s, "MOBILEREMEMBERUSERNAME", "1");
			goTR.StrWrite(ref s, "PERIODICBILLING", "0");
			goTR.StrWrite(ref s, "APPROVEREIMBURSE", "0");
			goTR.StrWrite(ref s, "REIMBURSE", "0");
			goTR.StrWrite(ref s, "SHAREDDESKTOPS", "0");
			goTR.StrWrite(ref s, "VIEWS", "1");
			goTR.StrWrite(ref s, "FORMS", "0");
			//MI 10/29/13 Removed customer admin permissions, which are now written under 'roles'
			//goTR.StrWrite(s, "CUSTADMINMANAGELOGINS", "0")
			//goTR.StrWrite(s, "CUSTADMINMANAGEFEATURES", "0")
			//goTR.StrWrite(s, "CUSTADMINMANAGEDATAACCESS", "0")
			//goTR.StrWrite(s, "WORKGROUPOPTIONS", "0")
			//goTR.StrWrite(s, "CUSTADMINREPORTS", "0")
			//goTR.StrWrite(s, "CUSTADMINREASSIGNRECORDS", "0")

			//MI 5/18/11
			goTR.StrWrite(ref s, "DOCLIBPUBLICCREATEFOLDERS", "0");
			goTR.StrWrite(ref s, "DOCLIBPUBLICDELETEFOLDERS", "0");
			goTR.StrWrite(ref s, "DOCLIBPUBLICCREATEFILES", "0");
			goTR.StrWrite(ref s, "DOCLIBPUBLICDELETEFILES", "0");
			goTR.StrWrite(ref s, "DOCLIBPERSONALCREATEFOLDERS", "1");
			goTR.StrWrite(ref s, "DOCLIBPERSONALDELETEFOLDERS", "1");
			goTR.StrWrite(ref s, "DOCLIBPERSONALCREATEFILES", "1");
			goTR.StrWrite(ref s, "DOCLIBPERSONALDELETEFILES", "1");
			goTR.StrWrite(ref s, "DOCRECORDLEVELCREATEFILES", "1");
			goTR.StrWrite(ref s, "DOCRECORDLEVELDELETEFILES", "1");


			return s;

		}

		public string GetDataAccessDefaultFilter(string par_sFile, string par_sResultType = "FILTER")
		{
			//MI 9/6/12 Removed CONDSUMMARY. The summary can be fetched dynamically with goTr.GetFilterConditionSummary.
			//PURPOSE:
			//   Get the default filter (Created by me) [default] or condition used in most data access permission.
			//PARAMETERS:
			//   par_sFile: File name, e.g. 'CO'.
			//   par_sResultType: optional: 'FILTER' [default] or 'CONDITION' (value of CONDITION= line in filter ini-style string).

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sIniString = "";

			// Try

			switch (par_sResultType)
			{
					case "FILTER":
						goTR.StrWrite(ref sIniString, "FILE", par_sFile);
						goTR.StrWrite(ref sIniString, "CONDITION", "LNK_CreatedBy_US='<%MEID%>'");
						goTR.StrWrite(ref sIniString, "CCOUNT", "1");
						goTR.StrWrite(ref sIniString, "C1FIELDNAME", "<%LNK_CREATEDBY_US%>");
						goTR.StrWrite(ref sIniString, "C1CONDITION", "0");
						goTR.StrWrite(ref sIniString, "C1VALUE1", "<%MEID%>");
						break;
						//MI 9/6/12 Removed CONDSUMMARY. The summary can be fetched dynamically with goTr.GetFilterConditionSummary.
						//goTR.StrWrite(sIniString, "CONDSUMMARY", "Created by User Is '<Me>'")
					case "CONDITION":
						sIniString = "LNK_CREATEDBY_US='<%MEID%>'";
						break;
					default:
						goErr.SetError(10103, sProc, "", "par_sResultType", sProc, par_sResultType);
						break;
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sIniString;

		}


		public string GetSelectiveFilter(string par_sFile, string par_sType = "R")
		{
			//MI 9/19/08 Edited comment.
			//MI 8/8/08 Changed case "R" to "LNK_CreatedBy_US='<%MEID%>'" from "".
			//MI 3/26/07 Changed MD format.
			//MI 3/20/07 Implemented goData.IsFileSystem.
			//MI 3/15/07 Returning "" for system files.
			//PURPOSE:
			//       Return current user's selective permission filter condition for Read, Edit, or Delete
			//       access to a particular file.
			//PARAMETERS:
			//       par_sFile: System file name
			//       par_sType: optional: type of permission. Supported values: 'R', 'E', 'D'.
			//           note that filter conditions are not supported for 'A' (add) access.
			//RETURNS:
			//       String: filter condition to use as CONDITION= property in a
			//           standard SellSQL filter/sort statement. If the condition is not defined
			//           either for the user or in GLOBAL permissions, this method
			//           returns a "default" string, which is:
			//               LNK_CreatedBy_US='<%MEID%>'

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sResult = null;
			string sDefault = "";

			if (goData.IsFileSystem(par_sFile))
			{
				//Selective permissions are not allowed for these 'system' files.
				return "";
			}

			//Validate parameters
			switch (par_sType.ToUpper())
			{
				case "R":
					sDefault = "LNK_CreatedBy_US='<%MEID%>'"; //MI 8/8/08 Changed from ""
					break;
				case "E":
				case "D":
					sDefault = "LNK_CreatedBy_US='<%MEID%>'";
					break;
				default:
					goErr.SetError(10103, sProc, "", "par_sType", sProc, par_sType);
					break;
					//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
			}

			sResult = goPerm.LineRead(goP.GetUserLogonID(), "ACCESS", par_sFile + "_" + par_sType + "COND", sDefault);

			return sResult;

		}

		public int GetSelectivePermission(string par_sFile, string par_sType = "R")
		{
			//MI 3/16/09 Changed perm for system files (MD, TN, XL) from 2 for all users
			//       (which was a security threat) to 2 only for admins and full authors
			//       and R:0, A:2, E:0, D:0 (no read, full add, no edit, no delete) for
			//       other users. These permissions are hard-coded.
			//MI 3/26/07 Changed MD format.
			//MI 3/20/07 Implemented goData.IsFileSystem.
			//MI 3/15/07 Added allowing full edit and delete to system files.

			//PURPOSE:
			//       Return current user's permission level for Read, Add, Edit, or Delete access to a particular file.
			//PARAMETERS:
			//       par_sFile: System file name
			//       par_sType: optional: type of permission. Supported values: 'R', 'A', 'E', 'D'.
			//RETURNS:
			//       Integer: permission level: 0 (none), 1 (selective), 2 (full).
			//           For system files, 2 is returned for admins and full authors;
			//           other users get R:0, A:0, E:0, D:0. For all other files,
			//           if a permission is not defined for the user, the GLOBAL default
			//           permission is returned. If the GLOBAL default isn't defined or is -1,
			//           then 2 or 1 is returned depending on the access type:
			//               Read, Add: 2 (full)
			//               Edit, Delete: 1 (selective)
			//NOTE MI 3/16/09 
			//       System files like MD, TN and XL are off limit to regular users.
			//       Admins and full authors have full access to them.
			//       IMPORTANT: scripts that access these files under a non-admin/author user login
			//       will fail. You will have to code around this limitation. You can use one of
			//       these techniques:
			//          1. Set par_bSysFileFullPermission parameter in clRowset.New to True.
			//              The rowset will read, add, edit, and delete data with full permissions.
			//          2. Pass all parameters to the rowset in the ini parameter with
			//              ACCESSTYPE= property set to the letter that represents the 
			//              permission which the rowset should use. For example, assuming that the
			//              user has a full add permission, but no read, edit, or delete permission
			//              ( - A - - ), set ACCESSTYPE=A and the rowset's reading, writing and deleting
			//              will run under the 'add' permission for the logged on user.
			//          3. Put your code in clScripts.AutoAlertEveryDay or AutoAlertEveryNSecs, which
			//              run hourly and every minute, respectively. This is not feasible when an 
			//              immediate UI response is required, but will work well for batch updates
			//              and other delayed updates that should be performed under SYSTEM login.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sResult = null;
			int iResult = 0;
			string sDefault = "2";

			if (goData.IsFileSystem(par_sFile))
			{
				if (goP.IsUserAdmin() || goP.IsUserAuthor() == 2)
				{
					//Admins and full authors have no restriction on system files
					return 2; //full in all cases, R, A, E, and D
				}
				else
				{
					switch (par_sType.ToUpper())
					{
						case "R":
							return 0;
						case "A":
							return 0;
						case "E":
							return 0;
						case "D":
							return 0;
						default:
							goErr.SetError(10103, sProc, "", "par_sType", sProc, par_sType);
							break;
							//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
					}
				}
			}

			//Validate parameters
			switch (par_sType.ToUpper())
			{
				case "R":
				case "A":
					sDefault = "2"; //full
					break;
				case "E":
				case "D":
					sDefault = "1"; //selective
					break;
				default:
					goErr.SetError(10103, sProc, "", "par_sType", sProc, par_sType);
					break;
					//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
			}

			sResult = goPerm.LineRead(goP.GetUserLogonID(), "ACCESS", par_sFile + "_" + par_sType, sDefault);
			//Replace 'default' (-1) with an actual permission
			if (sResult == "-1")
			{
				sResult = sDefault;
			}
			iResult = Convert.ToInt32(goTR.StringToNum(sResult));
			if (iResult > 2 || iResult < 0)
			{
				goErr.SetError(35000, sProc, "Unsupported value returned by goPerm.LineRead for login '" + goP.GetUserLogon() + "', login ID '" + goP.GetUserLogonID() + "', user: '" + goP.GetUserTID() + " (" + goP.GetMe("NAME") + "), par_sFile: '" + par_sFile + "', par_sType: '" + par_sType + "'. Value: '" + sResult + "'.");
			}

			return iResult;

		}

		public string GetUserPermission(string par_sUserID, string par_sType)
		{
			//MI 9/23/08 Added support for @par_sType parameter in the call to pGetUserLogon.
			//MI 4/23/07 Created.
			//PURPOSE:
			//       Returns roles and feature permissions for any user. For data access permissions,
			//       use goPerm.GetSelectivePermission().
			//PARAMETERS:
			//       par_sUserID: User record GID_ID.
			//       par_sType: Permission type to return:
			//           LOGONNAME: name with which the user logs into the system
			//           LOGONID: ID of the XU (user login) record by which permissions are referenced in XP file.
			//           AUTHOR: Author permissions, can be 0: none, 1: partial; 2: full
			//           ADMIN: Admin permission, can be 0: none, 1: true
			//           <any feature permission such as IMPORT, EXPORT, TRANSFERIN, PERIODICBILLING, REIMBURSE, etc.>
			//               "0" is returned if the feature is not defined
			//RETURNS:
			//       String: requested permission.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sType = par_sType.ToUpper();
			string sRoles = null;
			string sPermissions = null;
			string sLogonID = null; //GID_ID of the XU record (User logon)
			string sLogonName = null;

			sRoles = goPerm.PageRead(goP.GetUserLogonID(), "ROLES");
			sPermissions = goPerm.PageRead(goP.GetUserLogonID(), "FEATURES");

			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader reader = null;
			Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();

			cmd.CommandText = "pGetUserLogon";
			//Returns column GID_CreatedBy_US with one record
			cmd.CommandType = CommandType.StoredProcedure;
			cmd.Connection = sqlConnection1;

			//parameter
			System.Data.SqlClient.SqlParameter uSec = new System.Data.SqlClient.SqlParameter("@par_uGID", System.Data.SqlDbType.UniqueIdentifier);
			uSec.Value = goTR.StringToGuid(par_sUserID);
			cmd.Parameters.Add(uSec);

			//parameter
			System.Data.SqlClient.SqlParameter sMode = new System.Data.SqlClient.SqlParameter("@par_sType", System.Data.SqlDbType.VarChar);
			sMode.Value = "LOGIN";
			cmd.Parameters.Add(sMode);

			reader = cmd.ExecuteReader();
			if (reader.Read())
			{
				sLogonID = reader[0].ToString();
				sLogonName = reader[1].ToString();
			}
			else
			{
				goErr.SetWarning(35000, sProc, "Permissions cannot be evaluated because user login was not found for user '" + par_sUserID + "'.");
				return "";
			}

			reader.Close();
			sqlConnection1.Close();

			sRoles = PageRead(sLogonID, "ROLES");
			sPermissions = PageRead(sLogonID, "FEATURES");

			switch (sType)
			{
				case "LOGONNAME":
					return sLogonName;
				case "LOGONID":
					return sLogonID;
				case "AUTHOR":
					return goTR.StrRead(sRoles, "AUTHOR", "0");
				case "ADMIN":
					return goTR.StrRead(sRoles, "ADMIN", "0");
				default:
					return goTR.StrRead(sPermissions, par_sType, "0", false);
			}

		}

		public string GetUserLogin(string par_sUserID, string par_sType = "ID")
		{
			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);
			//MI 3/17/14 Created.
			//PURPOSE:
			//       Return A Login ID or Login Name for the provided User's GID_ID.
			//PARAMETERS:
			//       par_sUserID: User record GID_ID
			//       par_sType: What to return: 
			//           ID: Login ID (GID_ID of the record in the XU file) [default]
			//           NAME: TXT_LogonName of the login
			//RETURNS:
			//       String: Login GID_ID or TXT_LogonName depending on par_sType for
			//           User with GID_ID = par_sUserID

			string sReturn = "";

			//Try
			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader reader = null;
				Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();

				cmd.CommandText = "pGetUserLogon";
				//Returns column GID_CreatedBy_US with one record
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				//parameter
				System.Data.SqlClient.SqlParameter uSec = new System.Data.SqlClient.SqlParameter("@par_uGID", System.Data.SqlDbType.UniqueIdentifier);
				uSec.Value = goTR.StringToGuid(par_sUserID);
				cmd.Parameters.Add(uSec);

				//parameter
				System.Data.SqlClient.SqlParameter sMode = new System.Data.SqlClient.SqlParameter("@par_sType", System.Data.SqlDbType.VarChar);
				sMode.Value = "LOGIN";
				cmd.Parameters.Add(sMode);

				reader = cmd.ExecuteReader();
				if (reader.Read())
				{
					if (par_sType.ToUpper() == "ID")
					{
						sReturn = reader[0].ToString();
					}
					else
					{
						sReturn = reader[1].ToString();
					}
				}
				else
				{
					goErr.SetError(35000, sProc, "User Login cannot be returned because there is no User Login for User '" + par_sUserID + "'.");
					sReturn = "";
				}

				reader.Close();
				sqlConnection1.Close();

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sReturn;

		}

		public bool IsObjectShared(string par_sPageID)
		{

			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//       Determines whether a permissions page is shared ("GLOBAL" section)
			//PARAMETERS:
			//		par_sPageID: ID of the permission page
			//RESULT:
			//		Boolean

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
			System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
			System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader oReader = null;
			bool bReturn = false;
			string sPage = par_sPageID;

			//  Try

			//sql command
			oCommand.CommandText = "fnPermIsPageShared";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter 
				System.Data.SqlClient.SqlParameter sPageParam = new System.Data.SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar);
				sPageParam.Value = sPage;
				oCommand.Parameters.Add(sPageParam);

				//return parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Bit);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				//execute sproc

				oReader = oCommand.ExecuteReader();

				//Now you can grab the output parameter's value...
				bReturn = Convert.ToBoolean(retValParam.Value);

				oReader.Close();
				oConnection.Close();

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return bReturn;

		}

		public bool IsGroup(string par_sLoginID)
		{
			//MI 9/8/09 Created.
			//PURPOSE:
			//       Test whether a Login is a Login or Login Group
			//PARAMETERS:
			//       par_sLoginID: GID_ID of the Login (XU) record to test.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//Try
			//Find out whether the Login to copy is a Login ot Login Group
			System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;
				oCommand.CommandText = "SELECT TOP 1 [CHK_LoginGroup] FROM [XU] WHERE [GID_ID] = '" + par_sLoginID + "'";
				oCommand.CommandType = System.Data.CommandType.Text;
				oCommand.Connection = oConnection;
				//execute
				oReader = oCommand.ExecuteReader();
				//read returned value
				if (oReader.HasRows)
				{
					//iCount = 1
					while (oReader.Read())
					{
						if (oReader["CHK_LoginGroup"].ToString() == "True")
						{
							return true;
						}
					}
				}
				oReader.Close();
				oConnection.Close();
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
			return false;
		}


		public bool LineDelete(string par_sSection, string par_sPage, string par_sLine)
		{
			//MI 8/17/06 Added treating blank par_sSection treated as global.
			//7/14/06 MI Returning False if sProc returns <> 0.

			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Delete a line in a permission page
			//PARAMETERS:
			//		par_sSection	=	Section in which the line must be deleted
			//		par_sPage		=	Page in which the line must be deleted
			//		par_sLine		=	Line/property to delete
			//RETURNS:
			//		True if found and Deleted, false if not found (also OK, just nothing to delete)
			//HOW IT WORKS:
			//		Calls sProc pPermLineDelete and gets return value
			//EXAMPLE:
			//		goPerm.LineDelete(goP.GetUserTID(), "FEATURES", "PRINT")

			//==> Add error checking in sproc

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			//goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

			string sSection = par_sSection.Trim(' ').ToUpper();
			string sPage = par_sPage.Trim(' ').ToUpper();
			string sLine = par_sLine.Trim(' ').ToUpper();
			int iResult = 0;

			try
			{

				//*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
				System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;

				oCommand.CommandText = "pPermLineDelete";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter
				System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier);
				if (sSection == "GLOBAL" || sSection == "")
				{
					uSection.Value = System.DBNull.Value;
				}
				else
				{
					uSection.Value = goTR.StringToGuid(sSection);
				}
				oCommand.Parameters.Add(uSection);

				//parameter
				System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar);
				strPage.Value = sPage;
				oCommand.Parameters.Add(strPage);

				//parameter
				System.Data.SqlClient.SqlParameter strProperty = new System.Data.SqlClient.SqlParameter("@par_sProperty", SqlDbType.VarChar);
				strProperty.Value = sLine;
				oCommand.Parameters.Add(strProperty);

				//return parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				//execute

				oReader = oCommand.ExecuteReader();

				//Now you can grab the output parameter's value...
				iResult = Convert.ToInt16(retValParam.Value);

				oReader.Close();
				oConnection.Close();

				if (iResult == 0)
				{
					return true;
				}
				else
				{
					return false; //but no error checking in sproc yet, so this should be never returned
				}

			}
			catch (Exception ex)
			{

				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If

				return false;

			}

		}

		public string LineRead(string par_sSection, string par_sPage, string par_sLine, string par_sDefaultValue = "", bool par_bUserOnly = false)
		{
			//MI 1/19/07 Added replacing Chr(1) & Chr(1) with vbcrlf
			//MI 8/17/06 Added treating blank par_sSection treated as global.
			//MI 5/26/06 Modified comments.
			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Read a permission line. If par_bUserOnly is False (default), if the user's permission
			//       is not defined (left on default), the Login Group's value is returned. If not defined,
			//       the master default (User Login '<%DEFAULT%>') value is returned. If not defined,
			//       par_sDefaultValue is returned.
			//PARAMETERS:
			//		par_sSection		=	Section of the permission page from which to read a property's value.
			//                               Can be 'GLOBAL' or User's GID as string. If not 'GLOBAL' and 
			//                               par_bUserOnly is False, the Login Group's or GLOBAL value is returned 
			//                               if the property (par_sLine) is not found.
			//		par_sPage			=	Permission 'Page' such as "ACCESS", "FEATURES", or "ROLES".
			//		par_sLine			=	TXT_Property for which to read TXT_Value. Ex: AC_R for Activity read
			//                               permission or TRANSFERIN for a transfer in feature permission.
			//		par_sDefaultValue	= 	Default value to be returned if Section, Login Group or GLOBAL line was found.
			//		par_bUserOnly		=	if TRUE, does not read the Login Group or GLOBAL values. Default is False.
			//RETURNS:
			//		The content found or "".
			//HOW IT WORKS:
			//		calls udf fnPermLineRead with adequate parameters (already doing the merging) and returns the result.
			//EXAMPLE:
			//       Dim bAdmin as boolean
			//		bAdmin = goTr.StringToCheckbox(goPerm.LineRead(goP.GetUserTID(), "ROLES ", "Admin", "0"))

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			//goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

			string sSection = par_sSection.Trim(' ').ToUpper();
			string sPage = par_sPage.Trim(' ').ToUpper();
			string sLine = par_sLine.Trim(' ').ToUpper();
			string sDefaultValue = par_sDefaultValue;
			bool bUserOnly = par_bUserOnly;
			string sLang = "";

			try
			{

				if (oMetaConnection == null)
				{
					oMetaConnection = goData.GetConnection();
				}
				System.Data.SqlClient.SqlConnection oConnection = oMetaConnection;
				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;
				string sResult = "";

				oCommand.CommandText = "fnPermLineRead";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter
				System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier);
				if (sSection == "GLOBAL" || sSection == "")
				{
					uSection.Value = System.DBNull.Value;
				}
				else
				{
					uSection.Value = goTR.StringToGuid(sSection);
				}
				oCommand.Parameters.Add(uSection);

				//parameter
				System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar);
				strPage.Value = sPage;
				oCommand.Parameters.Add(strPage);

				//parameter
				System.Data.SqlClient.SqlParameter strProperty = new System.Data.SqlClient.SqlParameter("@par_sProperty", SqlDbType.VarChar);
				strProperty.Value = sLine;
				oCommand.Parameters.Add(strProperty);

				//parameter
				System.Data.SqlClient.SqlParameter strDefaultValue = new System.Data.SqlClient.SqlParameter("@par_sDefaultValue", SqlDbType.NVarChar);
				strDefaultValue.Value = sDefaultValue;
				oCommand.Parameters.Add(strDefaultValue);

				//parameter
				System.Data.SqlClient.SqlParameter blnUserOnly = new System.Data.SqlClient.SqlParameter("@par_bUserOnly", SqlDbType.Bit);
				blnUserOnly.Value = bUserOnly;
				oCommand.Parameters.Add(blnUserOnly);

				//return parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.NVarChar);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				//execute

				oReader = oCommand.ExecuteReader();

				//Now you can grab the output parameter's value...
				sResult = Convert.ToString(retValParam.Value);
				sResult = goTR.Replace(sResult, ((char)1).ToString() + ((char)1).ToString(), "\r\n");

				oReader.Close();
				//oConnection.Close()

				return sResult;

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If

				return "";

			}

		}

		public bool LineWrite(string par_sSection, string par_sPage, string par_sLine, object par_vValue)
		{
			System.Data.SqlClient.SqlConnection tempVar = null;
			return LineWrite(par_sSection, par_sPage, par_sLine, par_vValue, ref tempVar);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Public Function LineWrite(ByVal par_sSection As String, ByVal par_sPage As String, ByVal par_sLine As String, ByVal par_vValue As Object, Optional ByRef par_oConnection As SqlClient.SqlConnection = Nothing) As Boolean
		public bool LineWrite(string par_sSection, string par_sPage, string par_sLine, object par_vValue, ref System.Data.SqlClient.SqlConnection par_oConnection)
		{
			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Writes a line in a permission page
			//PARAMETERS:
			//		par_sSection	=	Section in which the line must be deleted
			//		par_sPage		=	Page in which the line must be deleted
			//		par_sLine		=	Line/property to delete
			//       par_sValue      =   Value to write for this line
			//       par_oConnection =   Optional SQL connection passed by PageWrite
			//RETURNS:
			//		True if successful write, false if not
			//HOW IT WORKS:
			//		Calls sProc pPermLineWrite and gets return value
			//EXAMPLE:
			//		goPerm.LineWrite("GLOBAL", "ACCESS", "AUTHOR", "2")

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			//goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

			string sSection = par_sSection.ToUpper().Trim(' ');
			string sPage = par_sPage.Trim(' ');
			string sLine = par_sLine.Trim(' ');
			string sValue = par_vValue.ToString(); //Trim(par_vValue.ToString)
			string sLang = "";
			int iResult = 0;

			//Replace CRs with two characters 1
			sValue = goTR.Replace(sValue, "\r\n", ((char)1).ToString() + ((char)1).ToString());

			//Try

			System.Data.SqlClient.SqlConnection oConnection = null;
				if (par_oConnection == null)
				{
					oConnection = goData.GetConnection();
				}
				else
				{
					oConnection = par_oConnection;
				}

				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;

				oCommand.CommandText = "pPermLineWrite";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter
				System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier);
				if (sSection == "GLOBAL" || sSection == "")
				{
					uSection.Value = System.DBNull.Value;
				}
				else
				{
					uSection.Value = goTR.StringToGuid(sSection);
				}
				oCommand.Parameters.Add(uSection);

				//parameter
				System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar);
				strPage.Value = sPage;
				oCommand.Parameters.Add(strPage);

				//parameter
				System.Data.SqlClient.SqlParameter strProperty = new System.Data.SqlClient.SqlParameter("@par_sProperty", SqlDbType.VarChar);
				strProperty.Value = sLine;
				oCommand.Parameters.Add(strProperty);

				//parameter
				System.Data.SqlClient.SqlParameter strValue = new System.Data.SqlClient.SqlParameter("@par_sValue", SqlDbType.NVarChar);
				strValue.Value = sValue;
				oCommand.Parameters.Add(strValue);

				//return parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				//execute

				oReader = oCommand.ExecuteReader();

				//Now you can grab the output parameter's value...
				iResult = Convert.ToInt16(retValParam.Value);

				oReader.Close();
				if (par_oConnection == null)
				{
					oConnection.Close();
				}


				if (iResult == 0)
				{
					return true;
				}
				else
				{
					switch (iResult)
					{
						case -1: //when fnGetMe returns '' User ID
							goErr.SetWarning(35503, sProc);
							break;
							//35503: Metadata or permission line couldn't be written because fnGetMe returned an invalid user ID.
						case -2: //when @par_sUserCode is ''
							goErr.SetWarning(35504, sProc);
							break;
							//35504: Metadata or permission line couldn't be written because fnGetMe returned an invalid user code.
					}
					return false;
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult, bool par_bThisIsLoginGroup, string par_sGroupGIDID, string par_sBillingType, bool par_bEnabled, bool par_bPasswordIsTemporary, DateTime par_dtTempPasswordExpirationTime, DateTime par_dtTempPasswordGeneratedTime, string par_sTempPasswordDeliveryMethod, string par_sTempPasswordGeneratedBy, string par_sTempPasswordSentTo, bool par_bPasswordDoesntExpire, string par_sPasswordChangedBy, DateTime par_dtPasswordChangedTime)
		{
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref par_sResult, par_bThisIsLoginGroup, par_sGroupGIDID, par_sBillingType, par_bEnabled, par_bPasswordIsTemporary, par_dtTempPasswordExpirationTime, par_dtTempPasswordGeneratedTime, par_sTempPasswordDeliveryMethod, par_sTempPasswordGeneratedBy, par_sTempPasswordSentTo, par_bPasswordDoesntExpire, par_sPasswordChangedBy, par_dtPasswordChangedTime, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult, bool par_bThisIsLoginGroup, string par_sGroupGIDID, string par_sBillingType, bool par_bEnabled, bool par_bPasswordIsTemporary, DateTime par_dtTempPasswordExpirationTime, DateTime par_dtTempPasswordGeneratedTime, string par_sTempPasswordDeliveryMethod, string par_sTempPasswordGeneratedBy, string par_sTempPasswordSentTo, bool par_bPasswordDoesntExpire, string par_sPasswordChangedBy)
		{
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref par_sResult, par_bThisIsLoginGroup, par_sGroupGIDID, par_sBillingType, par_bEnabled, par_bPasswordIsTemporary, par_dtTempPasswordExpirationTime, par_dtTempPasswordGeneratedTime, par_sTempPasswordDeliveryMethod, par_sTempPasswordGeneratedBy, par_sTempPasswordSentTo, par_bPasswordDoesntExpire, par_sPasswordChangedBy, clC.SELL_BLANK_DTDATETIME, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult, bool par_bThisIsLoginGroup, string par_sGroupGIDID, string par_sBillingType, bool par_bEnabled, bool par_bPasswordIsTemporary, DateTime par_dtTempPasswordExpirationTime, DateTime par_dtTempPasswordGeneratedTime, string par_sTempPasswordDeliveryMethod, string par_sTempPasswordGeneratedBy, string par_sTempPasswordSentTo, bool par_bPasswordDoesntExpire)
		{
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref par_sResult, par_bThisIsLoginGroup, par_sGroupGIDID, par_sBillingType, par_bEnabled, par_bPasswordIsTemporary, par_dtTempPasswordExpirationTime, par_dtTempPasswordGeneratedTime, par_sTempPasswordDeliveryMethod, par_sTempPasswordGeneratedBy, par_sTempPasswordSentTo, par_bPasswordDoesntExpire, "", clC.SELL_BLANK_DTDATETIME, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult, bool par_bThisIsLoginGroup, string par_sGroupGIDID, string par_sBillingType, bool par_bEnabled, bool par_bPasswordIsTemporary, DateTime par_dtTempPasswordExpirationTime, DateTime par_dtTempPasswordGeneratedTime, string par_sTempPasswordDeliveryMethod, string par_sTempPasswordGeneratedBy, string par_sTempPasswordSentTo)
		{
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref par_sResult, par_bThisIsLoginGroup, par_sGroupGIDID, par_sBillingType, par_bEnabled, par_bPasswordIsTemporary, par_dtTempPasswordExpirationTime, par_dtTempPasswordGeneratedTime, par_sTempPasswordDeliveryMethod, par_sTempPasswordGeneratedBy, par_sTempPasswordSentTo, false, "", clC.SELL_BLANK_DTDATETIME, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult, bool par_bThisIsLoginGroup, string par_sGroupGIDID, string par_sBillingType, bool par_bEnabled, bool par_bPasswordIsTemporary, DateTime par_dtTempPasswordExpirationTime, DateTime par_dtTempPasswordGeneratedTime, string par_sTempPasswordDeliveryMethod, string par_sTempPasswordGeneratedBy)
		{
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref par_sResult, par_bThisIsLoginGroup, par_sGroupGIDID, par_sBillingType, par_bEnabled, par_bPasswordIsTemporary, par_dtTempPasswordExpirationTime, par_dtTempPasswordGeneratedTime, par_sTempPasswordDeliveryMethod, par_sTempPasswordGeneratedBy, "", false, "", clC.SELL_BLANK_DTDATETIME, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult, bool par_bThisIsLoginGroup, string par_sGroupGIDID, string par_sBillingType, bool par_bEnabled, bool par_bPasswordIsTemporary, DateTime par_dtTempPasswordExpirationTime, DateTime par_dtTempPasswordGeneratedTime, string par_sTempPasswordDeliveryMethod)
		{
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref par_sResult, par_bThisIsLoginGroup, par_sGroupGIDID, par_sBillingType, par_bEnabled, par_bPasswordIsTemporary, par_dtTempPasswordExpirationTime, par_dtTempPasswordGeneratedTime, par_sTempPasswordDeliveryMethod, "", "", false, "", clC.SELL_BLANK_DTDATETIME, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult, bool par_bThisIsLoginGroup, string par_sGroupGIDID, string par_sBillingType, bool par_bEnabled, bool par_bPasswordIsTemporary, DateTime par_dtTempPasswordExpirationTime, DateTime par_dtTempPasswordGeneratedTime)
		{
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref par_sResult, par_bThisIsLoginGroup, par_sGroupGIDID, par_sBillingType, par_bEnabled, par_bPasswordIsTemporary, par_dtTempPasswordExpirationTime, par_dtTempPasswordGeneratedTime, "", "", "", false, "", clC.SELL_BLANK_DTDATETIME, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult, bool par_bThisIsLoginGroup, string par_sGroupGIDID, string par_sBillingType, bool par_bEnabled, bool par_bPasswordIsTemporary, DateTime par_dtTempPasswordExpirationTime)
		{
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref par_sResult, par_bThisIsLoginGroup, par_sGroupGIDID, par_sBillingType, par_bEnabled, par_bPasswordIsTemporary, par_dtTempPasswordExpirationTime, clC.SELL_BLANK_DTDATETIME, "", "", "", false, "", clC.SELL_BLANK_DTDATETIME, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult, bool par_bThisIsLoginGroup, string par_sGroupGIDID, string par_sBillingType, bool par_bEnabled, bool par_bPasswordIsTemporary)
		{
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref par_sResult, par_bThisIsLoginGroup, par_sGroupGIDID, par_sBillingType, par_bEnabled, par_bPasswordIsTemporary, clC.SELL_BLANK_DTDATETIME, clC.SELL_BLANK_DTDATETIME, "", "", "", false, "", clC.SELL_BLANK_DTDATETIME, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult, bool par_bThisIsLoginGroup, string par_sGroupGIDID, string par_sBillingType, bool par_bEnabled)
		{
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref par_sResult, par_bThisIsLoginGroup, par_sGroupGIDID, par_sBillingType, par_bEnabled, false, clC.SELL_BLANK_DTDATETIME, clC.SELL_BLANK_DTDATETIME, "", "", "", false, "", clC.SELL_BLANK_DTDATETIME, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult, bool par_bThisIsLoginGroup, string par_sGroupGIDID, string par_sBillingType)
		{
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref par_sResult, par_bThisIsLoginGroup, par_sGroupGIDID, par_sBillingType, false, false, clC.SELL_BLANK_DTDATETIME, clC.SELL_BLANK_DTDATETIME, "", "", "", false, "", clC.SELL_BLANK_DTDATETIME, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult, bool par_bThisIsLoginGroup, string par_sGroupGIDID)
		{
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref par_sResult, par_bThisIsLoginGroup, par_sGroupGIDID, "0", false, false, clC.SELL_BLANK_DTDATETIME, clC.SELL_BLANK_DTDATETIME, "", "", "", false, "", clC.SELL_BLANK_DTDATETIME, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult, bool par_bThisIsLoginGroup)
		{
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref par_sResult, par_bThisIsLoginGroup, "", "0", false, false, clC.SELL_BLANK_DTDATETIME, clC.SELL_BLANK_DTDATETIME, "", "", "", false, "", clC.SELL_BLANK_DTDATETIME, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult)
		{
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref par_sResult, false, "", "0", false, false, clC.SELL_BLANK_DTDATETIME, clC.SELL_BLANK_DTDATETIME, "", "", "", false, "", clC.SELL_BLANK_DTDATETIME, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID)
		{
			string tempVar = "";
			return LoginAdd(par_sLogonName, par_sPassword, par_sUserGIDID, ref tempVar, false, "", "0", false, false, clC.SELL_BLANK_DTDATETIME, clC.SELL_BLANK_DTDATETIME, "", "", "", false, "", clC.SELL_BLANK_DTDATETIME, false);
		}

		public bool LoginAdd(string par_sLogonName, string par_sPassword)
		{
			string tempVar = "";
			return LoginAdd(par_sLogonName, par_sPassword, "", ref tempVar, false, "", "0", false, false, clC.SELL_BLANK_DTDATETIME, clC.SELL_BLANK_DTDATETIME, "", "", "", false, "", clC.SELL_BLANK_DTDATETIME, false);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Public Function LoginAdd(ByVal par_sLogonName As String, ByVal par_sPassword As String, Optional ByVal par_sUserGIDID As String = "", Optional ByRef par_sResult As String = "", Optional ByVal par_bThisIsLoginGroup As Boolean = False, Optional ByVal par_sGroupGIDID As String = "", Optional ByVal par_sBillingType As String = "0", Optional ByVal par_bEnabled As Boolean = False, Optional ByVal par_bPasswordIsTemporary As Boolean = False, Optional ByVal par_dtTempPasswordExpirationTime As DateTime = clC.SELL_BLANK_DTDATETIME, Optional ByVal par_dtTempPasswordGeneratedTime As DateTime = clC.SELL_BLANK_DTDATETIME, Optional ByVal par_sTempPasswordDeliveryMethod As String = "", Optional ByVal par_sTempPasswordGeneratedBy As String = "", Optional ByVal par_sTempPasswordSentTo As String = "", Optional ByVal par_bPasswordDoesntExpire As Boolean = False, Optional ByVal par_sPasswordChangedBy As String = "", Optional ByVal par_dtPasswordChangedTime As DateTime = clC.SELL_BLANK_DTDATETIME, Optional ByVal par_bBill As Boolean = False) As Boolean
		public bool LoginAdd(string par_sLogonName, string par_sPassword, string par_sUserGIDID, ref string par_sResult, bool par_bThisIsLoginGroup, string par_sGroupGIDID, string par_sBillingType, bool par_bEnabled, bool par_bPasswordIsTemporary, DateTime par_dtTempPasswordExpirationTime, DateTime par_dtTempPasswordGeneratedTime, string par_sTempPasswordDeliveryMethod, string par_sTempPasswordGeneratedBy, string par_sTempPasswordSentTo, bool par_bPasswordDoesntExpire, string par_sPasswordChangedBy, DateTime par_dtPasswordChangedTime, bool par_bBill)
		{

			//Public Function LoginAdd(ByVal par_sLogonName As String, _
			//                ByVal par_sPassword As String, _
			//                Optional ByVal par_sUserGIDID As String = "", _
			//                Optional ByRef par_sResult As String = "", _
			//                Optional ByVal par_bThisIsLoginGroup As Boolean = False, _
			//                Optional ByVal par_sGroupGIDID As String = "", _
			//                Optional ByVal par_sBillingType As String = "0", _
			//                Optional ByVal par_bEnabled As Boolean = False, _
			//                Optional ByVal par_dtBillingStartTime As DateTime = clC.SELL_BLANK_DTDATETIME, _           --> DEPRECATED
			//                Optional ByVal par_dtDeactivationTime As DateTime = clC.SELL_BLANK_DTDATETIME, _           --> DEPRECATED
			//                Optional ByVal par_bPasswordIsTemporary As Boolean = False, _
			//                Optional ByVal par_dtTempPasswordExpirationTime As DateTime = clC.SELL_BLANK_DTDATETIME, _
			//                Optional ByVal par_dtTempPasswordGeneratedTime As DateTime = clC.SELL_BLANK_DTDATETIME, _
			//                Optional ByVal par_sTempPasswordDeliveryMethod As String = "", _
			//                Optional ByVal par_sTempPasswordGeneratedBy As String = "", _
			//                Optional ByVal par_sTempPasswordSentTo As String = "", _
			//                Optional ByVal par_dtPasswordUserChangedTime As DateTime = clC.SELL_BLANK_DTDATETIME, _    --> DEPRECATED
			//                Optional ByVal par_bPasswordDoesntExpire As Boolean = False, _
			//                Optional ByVal par_sPasswordChangedBy As String = "", _
			//                Optional ByVal par_dtPasswordChangedTime As DateTime = clC.SELL_BLANK_DTDATETIME) As Boolean

			//MI 10/24/13 LoginAdd: Added par_bBill.
			//MI 10/23/13 LoginAdd: Deprecating par_dtBillingStartTime, par_dtDeactivationTime.
			//MI 10/4/13 LoginAdd: Added par_sPasswordChangedBy, par_dtPasswordChangedTime parameters.
			//MI 9/24/13 LoginAdd: Added par_bPasswordDoesntExpire parameter.
			//MI 5/10/13 LoginAdd: Changed the duplicate login message to: 'The login record can't be added because a user login or login group with the same name exists. Please assign a unique name.'
			//MI 4/24/12 LoginAdd: removed test connection-closing code that was inadvertently left in.
			//MI 1/3/12 Added support for enablement, billing, and temp password parameters.
			//MI 10/20/09 Added par_sBillingType parameter.
			//MI 8/26/09 Added par_sGroupGIDID parameter.
			//MI 9/23/08 Added par_bThisIsLoginGroup parameter.
			//PURPOSE:
			//       Create a new XU (User login) record. If an error occurs, sets a warning.
			//       Default values in optional parameters assume that a user login is being
			//       created. 
			//PARAMETERS:
			//       par_sLogonName: name under which the user will log on.
			//       par_sPassword: Logon password. If par_bThisIsLoginGroup=True, send "".
			//       par_sUserGIDID: GID_ID of the US record associated. If 
			//           par_bThisIsLoginGroup = True, omit or send "".
			//       par_sResult: Return parameter: GID_ID of the XU record created.
			//       par_bThisIsLoginGroup: When True, create the 'login' as a login group.
			//           Default is False.
			//       par_sGroupGIDID: GID_ID of the Login Group record from XU table.
			//           Ignored if par_bThisIsLoginGroup is True: omit or send ''
			//           if this is a login group.
			//       	--------- PARAMETERS BELOW APPLICABLE ONLY TO LOGINS, NOT GROUPS ----------
			//       par_siBillingType: 0=Sales (default); 1=Lite; 2=Partner; 255=Do not bill
			//       par_bEnabled bit: when 1 (default), the login is enabled, otherwise
			//       	the user can't log in
			//       par_dtBillingStartTime: DEPRECATEAD start billing on this date. The default value
			//       	in SS is GETUTCDATE()
			//       par_dtDeactivationTime: DEPRECATED deactivate the login on this datetime. 
			//       	NULL means infinity, never deactivate.
			//       par_bPasswordIsTemporary: when 1 (default), the password is temporary
			//       	and expires on datetime in @par_dtTempPasswordExpirationTime. If NULL,
			//           it(doesn) 't expire.
			//       par_dtTempPasswordExpirationTime: if the password is temporary, it expires
			//       	on this datetime.
			//       par_dtTempPasswordGeneratedTime: datetime the temp password was generated
			//       	by an admin in diaAdmPerm.
			//       par_sTempPasswordDeliveryMethod: VERBAL or EMAIL (other modes may be supported
			//       	in the future).
			//       par_sTempPasswordGeneratedBy: 4-char user code of the admin who generated
			//           the temp password.
			//       par_sTempPasswordSentTo: E-mail or other address indicating how the temp
			//           password was sent to the user.
			//       par_dtPasswordUserChangedTime: DEPRECATED datetime the user changed the password.
			//       par_bPasswordDoesntExpire: When True, the password is not subject to the 
			//           WOP property EXPIREPASSWORDSINDAYS= and never 'expires'. 
			//       par_sPasswordChangedBy: 4-char user code of the user or admin who last
			//           changed the password (temporary or permanent)
			//       par_dtPasswordChangedTime: datetime the user or admin changed the password
			//       par_bBill: when 1 (default), the login will be billed.
			//RETURNS:
			//       True on success, false otherwise. Get the error via goErr.GetLastError.

			//@par_sLogonName nvarchar(20),
			//@par_sPassword nvarchar(20),
			//@par_uUserGIDID uniqueidentifier = NULL,
			//@par_uResult uniqueidentifier OUTPUT,
			//@par_bThisIsLoginGroup bit = 0,
			//@par_uGroupGIDID uniqueidentifier = NULL

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			bool bResult = true;
			int iResult = 0;

			//Try

			//------------ Validate ------------
			if (par_sLogonName.Trim(' ') == "")
			{
					goErr.SetWarning(35000, sProc, "The login record can't be added because the Login Name is blank.");
					//MessTranslate
				}
				if (!par_bThisIsLoginGroup)
				{
					if (par_sPassword.Trim(' ') == "")
					{
						goErr.SetWarning(35000, sProc, "The login record can't be added because the Password is blank.");
						//MessTranslate
					}
				}

				//------------- Add ------------
				System.Data.SqlClient.SqlConnection oConnection = new System.Data.SqlClient.SqlConnection();
				oConnection = goData.GetConnection();

				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;

				oCommand.CommandText = "pLoginAdd";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter
				System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sLogonName", SqlDbType.VarChar);
				strPage.Value = par_sLogonName;
				oCommand.Parameters.Add(strPage);

				//parameter
				System.Data.SqlClient.SqlParameter strProperty = new System.Data.SqlClient.SqlParameter("@par_sPassword", SqlDbType.VarChar);
				strProperty.Value = par_sPassword;
				oCommand.Parameters.Add(strProperty);

				//parameter
				System.Data.SqlClient.SqlParameter uUserGIDID = new System.Data.SqlClient.SqlParameter("@par_uUserGIDID", SqlDbType.UniqueIdentifier);
				if (par_sUserGIDID == "" || par_sUserGIDID == "<%NONE%>" || par_sUserGIDID == null)
				{
					uUserGIDID.Value = System.DBNull.Value;
				}
				else
				{
					uUserGIDID.Value = goTR.StringToGuid(par_sUserGIDID);
				}
				oCommand.Parameters.Add(uUserGIDID);

				//Return parameter
				System.Data.SqlClient.SqlParameter uResult = new System.Data.SqlClient.SqlParameter("@par_uResult", SqlDbType.UniqueIdentifier);
				//uResult.Value = System.DBNull.Value
				uResult.Direction = ParameterDirection.Output;
				oCommand.Parameters.Add(uResult);

				//parameter
				System.Data.SqlClient.SqlParameter bThisIsLoginGroup = new System.Data.SqlClient.SqlParameter("@par_bThisIsLoginGroup", SqlDbType.Bit);
				bThisIsLoginGroup.Value = Convert.ToInt16(par_bThisIsLoginGroup);
				oCommand.Parameters.Add(bThisIsLoginGroup);

				//parameter
				System.Data.SqlClient.SqlParameter uGroupGIDID = new System.Data.SqlClient.SqlParameter("@par_uGroupGIDID", SqlDbType.UniqueIdentifier);
				if (!par_bThisIsLoginGroup)
				{
					//Regular Login
					if (par_sGroupGIDID == "" || par_sGroupGIDID == "<%NONE%>" || par_sGroupGIDID == null)
					{
						uGroupGIDID.Value = System.DBNull.Value;
					}
					else
					{
						uGroupGIDID.Value = goTR.StringToGuid(par_sGroupGIDID);
					}
				}
				else
				{
					//Login Group - ignore the Login Group GID_ID parameter
					uGroupGIDID.Value = System.DBNull.Value;
				}
				oCommand.Parameters.Add(uGroupGIDID);

				//parameter
				System.Data.SqlClient.SqlParameter parBillingType = new System.Data.SqlClient.SqlParameter("@par_siBillingType", SqlDbType.TinyInt);
				parBillingType.Value = goTR.StringToNum(par_sBillingType, "0");
				oCommand.Parameters.Add(parBillingType);

				//MI 1/3/12 -------- Added the following 10 parameters ------------

				//parameter
				System.Data.SqlClient.SqlParameter bEnabled = new System.Data.SqlClient.SqlParameter("@par_bEnabled", SqlDbType.Bit);
				bEnabled.Value = Convert.ToInt16(par_bEnabled);
				oCommand.Parameters.Add(bEnabled);

				//'parameter
				//Dim dtBillingStartTime As New SqlClient.SqlParameter("@par_dtBillingStartTime", SqlDbType.DateTime)
				//If par_dtBillingStartTime = clC.SELL_BLANK_DTDATETIME Then
				//    dtBillingStartTime.Value = System.DBNull.Value
				//Else
				//    dtBillingStartTime.Value = par_dtBillingStartTime
				//End If
				//oCommand.Parameters.Add(dtBillingStartTime)

				//'parameter
				//Dim dtDeactivationTime As New SqlClient.SqlParameter("@par_dtDeactivationTime", SqlDbType.DateTime)
				//If par_dtDeactivationTime = clC.SELL_BLANK_DTDATETIME Then
				//    dtDeactivationTime.Value = System.DBNull.Value
				//Else
				//    dtDeactivationTime.Value = par_dtDeactivationTime
				//End If
				//oCommand.Parameters.Add(dtDeactivationTime)

				//parameter
				System.Data.SqlClient.SqlParameter bPasswordIsTemporary = new System.Data.SqlClient.SqlParameter("@par_bPasswordIsTemporary", SqlDbType.Bit);
				bPasswordIsTemporary.Value = Convert.ToInt16(par_bPasswordIsTemporary);
				oCommand.Parameters.Add(bPasswordIsTemporary);

				//parameter
				System.Data.SqlClient.SqlParameter dtTempPasswordExpirationTime = new System.Data.SqlClient.SqlParameter("@par_dtTempPasswordExpirationTime", SqlDbType.DateTime);
				if (par_dtTempPasswordExpirationTime == clC.SELL_BLANK_DTDATETIME)
				{
					dtTempPasswordExpirationTime.Value = System.DBNull.Value;
				}
				else
				{
					dtTempPasswordExpirationTime.Value = par_dtTempPasswordExpirationTime;
				}
				oCommand.Parameters.Add(dtTempPasswordExpirationTime);

				//parameter
				System.Data.SqlClient.SqlParameter dtTempPasswordGeneratedTime = new System.Data.SqlClient.SqlParameter("@par_dtTempPasswordGeneratedTime", SqlDbType.DateTime);
				if (par_dtTempPasswordGeneratedTime == clC.SELL_BLANK_DTDATETIME)
				{
					dtTempPasswordGeneratedTime.Value = System.DBNull.Value;
				}
				else
				{
					dtTempPasswordGeneratedTime.Value = par_dtTempPasswordGeneratedTime;
				}
				oCommand.Parameters.Add(dtTempPasswordGeneratedTime);

				//parameter
				System.Data.SqlClient.SqlParameter sTempPasswordDeliveryMethod = new System.Data.SqlClient.SqlParameter("@par_sTempPasswordDeliveryMethod", SqlDbType.VarChar, 8);
				if (par_sTempPasswordDeliveryMethod == "")
				{
					sTempPasswordDeliveryMethod.Value = System.DBNull.Value;
				}
				else
				{
					sTempPasswordDeliveryMethod.Value = par_sTempPasswordDeliveryMethod;
				}
				oCommand.Parameters.Add(sTempPasswordDeliveryMethod);

				//parameter
				System.Data.SqlClient.SqlParameter sTempPasswordGeneratedBy = new System.Data.SqlClient.SqlParameter("@par_sTempPasswordGeneratedBy", SqlDbType.VarChar, 4);
				if (par_sTempPasswordGeneratedBy == "")
				{
					sTempPasswordGeneratedBy.Value = System.DBNull.Value;
				}
				else
				{
					sTempPasswordGeneratedBy.Value = par_sTempPasswordGeneratedBy;
				}
				oCommand.Parameters.Add(sTempPasswordGeneratedBy);

				//parameter
				System.Data.SqlClient.SqlParameter sTempPasswordSentTo = new System.Data.SqlClient.SqlParameter("@par_sTempPasswordSentTo", SqlDbType.NVarChar, 80);
				if (par_sTempPasswordSentTo == "")
				{
					sTempPasswordSentTo.Value = System.DBNull.Value;
				}
				else
				{
					sTempPasswordSentTo.Value = par_sTempPasswordSentTo;
				}
				oCommand.Parameters.Add(sTempPasswordSentTo);

				//'parameter
				//Dim dtPasswordUserChangedTime As New SqlClient.SqlParameter("@par_dtPasswordUserChangedTime", SqlDbType.DateTime)
				//If par_dtPasswordUserChangedTime = clC.SELL_BLANK_DTDATETIME Then
				//    dtPasswordUserChangedTime.Value = System.DBNull.Value
				//Else
				//    dtPasswordUserChangedTime.Value = par_dtPasswordUserChangedTime
				//End If
				//oCommand.Parameters.Add(dtPasswordUserChangedTime)

				//parameter MI 9/24/13
				System.Data.SqlClient.SqlParameter bPasswordDoesntExpire = new System.Data.SqlClient.SqlParameter("@par_bPasswordDoesntExpire", SqlDbType.Bit);
				bPasswordDoesntExpire.Value = Convert.ToInt16(par_bPasswordDoesntExpire);
				oCommand.Parameters.Add(bPasswordDoesntExpire);

				//parameter MI 10/4/13
				System.Data.SqlClient.SqlParameter sPasswordChangedBy = new System.Data.SqlClient.SqlParameter("@par_sPasswordChangedBy", SqlDbType.VarChar, 4);
				if (par_sPasswordChangedBy == "")
				{
					sPasswordChangedBy.Value = System.DBNull.Value;
				}
				else
				{
					sPasswordChangedBy.Value = par_sPasswordChangedBy;
				}
				oCommand.Parameters.Add(sPasswordChangedBy);

				//parameter MI 10/4/13
				System.Data.SqlClient.SqlParameter dtPasswordChangedTime = new System.Data.SqlClient.SqlParameter("@par_dtPasswordChangedTime", SqlDbType.DateTime);
				if (par_dtPasswordChangedTime == clC.SELL_BLANK_DTDATETIME)
				{
					dtPasswordChangedTime.Value = System.DBNull.Value;
				}
				else
				{
					dtPasswordChangedTime.Value = par_dtPasswordChangedTime;
				}
				oCommand.Parameters.Add(dtPasswordChangedTime);

				//parameter
				System.Data.SqlClient.SqlParameter bBill = new System.Data.SqlClient.SqlParameter("@par_bBill", SqlDbType.Bit);
				bBill.Value = Convert.ToInt16(par_bBill);
				oCommand.Parameters.Add(bBill);


				//Result parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				try
				{
					//execute
					oReader = oCommand.ExecuteReader();
					//Now you can grab the output parameter's value...
					iResult = Convert.ToInt16(retValParam.Value);
					par_sResult = uResult.Value.ToString(); //GID_ID of the XU record

					oReader.Close();
					oConnection.Close();

				}
				catch (System.Data.SqlClient.SqlException sqlex)
				{
					if (sqlex.ErrorCode == -2146232060)
					{
						goErr.SetWarning(35000, sProc, "The login record can't be added because a user login or login group with the same name exists. Please assign a unique name.");
						//MessTranslate
					}
					else
					{
						goErr.SetWarning(35000, sProc, "Adding login record failed due to database error '" + sqlex.ErrorCode + "': '" + sqlex.Message + "'");
						//MessTranslate
					}
					return false;
				}

				if (iResult == 0)
				{
					return true;
				}
				else
				{
					switch (iResult)
					{
						case -1: //when fnGetMe returns '' User ID
							goErr.SetWarning(35503, sProc);
							break;
							//35503: Metadata or permission line couldn't be written because fnGetMe returned an invalid user ID.
						case -2: //when @par_sUserCode is ''
							goErr.SetWarning(35504, sProc);
							break;
							//35504: Metadata or permission line couldn't be written because fnGetMe returned an invalid user code.
						case -3: //LogonName duplicate error (SS error 2601)
							goErr.SetWarning(35000, sProc, "The login record can't be added because a user login or login group with the same name exists. Please assign a unique name.");
							break;
							//MessTranslate
						default:
							goErr.SetWarning(35000, sProc, "Adding login record failed: database error is '" + iResult + "'.");
							break;
							//MessTranslate
					}
					return false;
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return bResult;

		}

		public bool LoginEdit(string par_sGIDID, string par_sLogonName, string par_sPassword, string par_sUserGIDID, string par_sGroupGIDID, string par_sBillingType, bool par_bEnabled, bool par_bPasswordIsTemporary, DateTime par_dtTempPasswordExpirationTime, DateTime par_dtTempPasswordGeneratedTime, string par_sTempPasswordDeliveryMethod, string par_sTempPasswordGeneratedBy, string par_sTempPasswordSentTo, bool par_bPasswordDoesntExpire, string par_sPasswordChangedBy, DateTime par_dtPasswordChangedTime, bool par_bBill)
		{

			//Public Function LoginEdit(ByVal par_sGIDID As String, _
			//                ByVal par_sLogonName As String, _
			//                ByVal par_sPassword As String, _
			//                ByVal par_sUserGIDID As String, _
			//                ByVal par_sGroupGIDID As String, _
			//                ByVal par_sBillingType As String, _
			//                ByVal par_bEnabled As Boolean, _
			//                ByVal par_dtBillingStartTime As DateTime, _            --> DEPRECATED
			//                ByVal par_dtDeactivationTime As DateTime, _            --> DEPRECATED
			//                ByVal par_bPasswordIsTemporary As Boolean, _
			//                ByVal par_dtTempPasswordExpirationTime As DateTime, _
			//                ByVal par_dtTempPasswordGeneratedTime As DateTime, _
			//                ByVal par_sTempPasswordDeliveryMethod As String, _
			//                ByVal par_sTempPasswordGeneratedBy As String, _
			//                ByVal par_sTempPasswordSentTo As String, _
			//                ByVal par_dtPasswordUserChangedTime As DateTime, _     --> DEPRECATED
			//                ByVal par_bPasswordDoesntExpire As Boolean, _
			//                ByVal par_sPasswordChangedBy As String, _
			//                ByVal par_dtPasswordChangedTime As DateTime) As Boolean

			//MI 10/24/13 LoginEdit: Added par_bBill.
			//MI 10/23/13 LoginEdit: Deprecating par_dtBillingStartTime, par_dtDeactivationTime.
			//MI 10/11/13 Undoing skipping the password.
			//MI 10/10/13 Enabling skipping the password by sending Nothing in that parameter.
			//MI 10/4/13 LoginEdit: Added par_sPasswordChangedBy, par_dtPasswordChangedTime parameters.
			//MI 9/24/13 Added support for par_bPasswordDoesntExpire. 
			//MI 12/29/11 Added support for enablement, billing, and temp password parameters.
			//MI 8/26/09 Added support for Group GID_ID.
			//MI 9/23/08 Added support for login groups.
			//PURPOSE:
			//       Edit an XU (User login or User Login Group) record. 
			//       If an error occurs, sets a warning. Since none of the parameters is optional,
			//       all field values must be read and set explicitly even in the case of login
			//       Group to which most of the parameters don't pertain.
			//       To skip sending the password and modifying other password-related fields, 
			//       use LoginEditIgnorePassword instead.
			//WARNING:
			//       Since all fields are written explicitly, the XU writing is record-level, 
			//       not field-level. If user 1 opens the XU record (by selecting a Login in
			//       diaAdmPerm.aspx), user 2 edits and saves the same XU record, and
			//       user 1 then saves, the changes made by user 2 will be lost.
			//PARAMETERS:
			//       Note: Except for password, none of the parameters is optional to prevent inadvertently 
			//       overwriting values. Since pLoginEdit performs a single UPDATE of the XU record, none of
			//       the fields can be skipped. If pLoginEdit were rewritten to write only the
			//       non-null fields using dynamic SQL, then the parameters in this method could be
			//       made optional. Note that boolean and datetime types can't be set to Nothing.
			//       To do so, parameters would need to be of object type or mandatory parameters
			//       could be made nullable using this syntax (from WT 1/4/12):
			//          Optional ByVal par_bEnabled As Nullable(Of Boolean), _
			//
			//       par_sGIDID: GID_ID of the XU record to edit.
			//       par_sLogonName: name under which the user will log on or the 
			//           name of the login group.
			//       par_sPassword: Logon password. If this is a login group, send ''.
			//           To skip sending the password and modifying other password-
			//           related fields, use LoginEditIgnorePassword instead.
			//       par_sUserGIDID: GID_ID of the US record associated. If
			//           this is a login group, omit or send ''. 
			//       par_sGroupGIDID: GID_ID of the Login Group record from XU table.
			//           Only applicable to Logins. If this is a login group, omit or
			//           send ''. This WILL write the Group GID_ID in Login Group
			//           records (which is nonsensical), but this will be ignored by the UI.
			//       	--------- PARAMETERS BELOW APPLICABLE ONLY TO LOGINS, NOT GROUPS ----------
			//       par_siBillingType: 0=Sales (default); 1=Lite; 2=Partner; 255=Do not bill.
			//       par_bEnabled bit: when 1 (default), the login is enabled, otherwise
			//       	the user can't log in. 
			//       par_dtBillingStartTime: DEPRECATED start billing on this date. The default value
			//       	in SS is GETUTCDATE().
			//       par_dtDeactivationTime: DEPRECATED deactivate the login on this datetime. 
			//       	NULL means infinity, never deactivate. 
			//       par_bPasswordIsTemporary: when 1 (default), the password is temporary
			//       	and expires on datetime in @par_dtTempPasswordExpirationTime. If NULL,
			//           it doesn't expire. 
			//       par_dtTempPasswordExpirationTime: if the password is temporary, it expires
			//       	on this datetime.  
			//       par_dtTempPasswordGeneratedTime: datetime the temp password was generated
			//       	by an admin in diaAdmPerm.  
			//       par_sTempPasswordDeliveryMethod: VERBAL or EMAIL (other modes may be supported
			//       	in the future).  
			//       par_sTempPasswordGeneratedBy: 4-char user code of the admin who generated
			//           the temp password.  
			//       par_sTempPasswordSentTo: E-mail or other address indicating how the temp
			//           password was sent to the user.  
			//       par_dtPasswordUserChangedTime: DEPRECATED datetime the user changed the password.
			//       par_bPasswordDoesntExpire: 0=password is subject to EXPIREPASSWORDSINDAYS= in WOP
			//           (default); 1=password never expires. 
			//       par_sPasswordChangedBy: 4-char user code of the user or admin who last
			//           changed the password (temporary or permanent)
			//       par_dtPasswordChangedTime: datetime the user or admin changed the password
			//       par_bBill: when 1 (default), the login will be billed.

			//RETURNS:
			//       True on success, False otherwise. Retrieve the error via goErr.GetLastError
			//CREATE PROCEDURE pLoginEdit 
			//	@par_uGIDID uniqueidentifier,
			//	@par_sLogonName nvarchar(20),
			//	@par_sPassword nvarchar(20),
			//	@par_uUserGIDID uniqueidentifier = NULL,
			//   @par_uGroupGIDID uniqueidentifier = NULL

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			if (par_dtTempPasswordExpirationTime == Convert.ToDateTime("12:00:00 AM"))
			{

				par_dtTempPasswordExpirationTime = clC.SELL_BLANK_DTDATETIME;

			}
			if (par_dtTempPasswordGeneratedTime == Convert.ToDateTime("12:00:00 AM"))
			{

				par_dtTempPasswordGeneratedTime = clC.SELL_BLANK_DTDATETIME;

			}
			if (par_dtPasswordChangedTime == Convert.ToDateTime("12:00:00 AM"))
			{

				par_dtPasswordChangedTime = clC.SELL_BLANK_DTDATETIME;

			}
			bool bResult = true;
			int iResult = 0;

			//Try

			//------------ Validate ------------
			if (par_sGIDID.Trim(' ') == "")
			{
					goErr.SetError(10103, sProc, "", "par_sGIDID", sProc, par_sGIDID);
					//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
				}
				if (par_sLogonName.Trim(' ') == "")
				{
					goErr.SetWarning(35000, sProc, "The login record can't be edited because the Login Name is blank.");
					//MessTranslate
				}
				//If Trim(par_sPassword) = "" Then
				//    goErr.SetWarning(35000, sProc, "The login record can't be edited because the Password is blank.")
				//    'MessTranslate
				//End If

				//------------- Edit ---------------
				System.Data.SqlClient.SqlConnection oConnection = new System.Data.SqlClient.SqlConnection();
				oConnection = goData.GetConnection();
				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;

				oCommand.CommandText = "pLoginEdit";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter
				System.Data.SqlClient.SqlParameter uGIDID = new System.Data.SqlClient.SqlParameter("@par_uGIDID", SqlDbType.UniqueIdentifier);
				if (par_sGIDID == "")
				{
					uGIDID.Value = System.DBNull.Value;
				}
				else
				{
					uGIDID.Value = goTR.StringToGuid(par_sGIDID);
				}
				oCommand.Parameters.Add(uGIDID);

				//parameter
				System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sLogonName", SqlDbType.VarChar);
				strPage.Value = par_sLogonName;
				oCommand.Parameters.Add(strPage);

				//parameter
				System.Data.SqlClient.SqlParameter strProperty = new System.Data.SqlClient.SqlParameter("@par_sPassword", SqlDbType.VarChar);
				strProperty.Value = par_sPassword;
				oCommand.Parameters.Add(strProperty);

				//parameter
				System.Data.SqlClient.SqlParameter uUserGIDID = new System.Data.SqlClient.SqlParameter("@par_uUserGIDID", SqlDbType.UniqueIdentifier);
				if (par_sUserGIDID == "" || par_sUserGIDID == "<%NONE%>")
				{
					uUserGIDID.Value = System.DBNull.Value;
				}
				else
				{
					uUserGIDID.Value = goTR.StringToGuid(par_sUserGIDID);
				}
				oCommand.Parameters.Add(uUserGIDID);

				//parameter
				System.Data.SqlClient.SqlParameter uGroupGIDID = new System.Data.SqlClient.SqlParameter("@par_uGroupGIDID", SqlDbType.UniqueIdentifier);
				if (par_sGroupGIDID == "" || par_sGroupGIDID == "<%NONE%>")
				{
					uGroupGIDID.Value = System.DBNull.Value;
				}
				else
				{
					uGroupGIDID.Value = goTR.StringToGuid(par_sGroupGIDID);
				}
				oCommand.Parameters.Add(uGroupGIDID);

				//parameter
				System.Data.SqlClient.SqlParameter parBillingType = new System.Data.SqlClient.SqlParameter("@par_siBillingType", SqlDbType.TinyInt);
				parBillingType.Value = goTR.StringToNum(par_sBillingType, "0");
				oCommand.Parameters.Add(parBillingType);

				//MI 1/3/12 -------- Added the following 10 parameters ------------

				//parameter
				System.Data.SqlClient.SqlParameter bEnabled = new System.Data.SqlClient.SqlParameter("@par_bEnabled", SqlDbType.Bit);
				bEnabled.Value = Convert.ToInt16(par_bEnabled);
				oCommand.Parameters.Add(bEnabled);

				//'parameter
				//Dim dtBillingStartTime As New SqlClient.SqlParameter("@par_dtBillingStartTime", SqlDbType.DateTime)
				//If par_dtBillingStartTime = clC.SELL_BLANK_DTDATETIME Then
				//    dtBillingStartTime.Value = System.DBNull.Value
				//Else
				//    dtBillingStartTime.Value = par_dtBillingStartTime
				//End If
				//oCommand.Parameters.Add(dtBillingStartTime)

				//'parameter
				//Dim dtDeactivationTime As New SqlClient.SqlParameter("@par_dtDeactivationTime", SqlDbType.DateTime)
				//If par_dtDeactivationTime = clC.SELL_BLANK_DTDATETIME Then
				//    dtDeactivationTime.Value = System.DBNull.Value
				//Else
				//    dtDeactivationTime.Value = par_dtDeactivationTime
				//End If
				//oCommand.Parameters.Add(dtDeactivationTime)

				//parameter
				System.Data.SqlClient.SqlParameter bPasswordIsTemporary = new System.Data.SqlClient.SqlParameter("@par_bPasswordIsTemporary", SqlDbType.Bit);
				bPasswordIsTemporary.Value = Convert.ToInt16(par_bPasswordIsTemporary);
				oCommand.Parameters.Add(bPasswordIsTemporary);

				//parameter
				System.Data.SqlClient.SqlParameter dtTempPasswordExpirationTime = new System.Data.SqlClient.SqlParameter("@par_dtTempPasswordExpirationTime", SqlDbType.DateTime);
				if (par_dtTempPasswordExpirationTime == clC.SELL_BLANK_DTDATETIME)
				{
					dtTempPasswordExpirationTime.Value = System.DBNull.Value;
				}
				else
				{
					dtTempPasswordExpirationTime.Value = par_dtTempPasswordExpirationTime;
				}
				oCommand.Parameters.Add(dtTempPasswordExpirationTime);

				//parameter
				System.Data.SqlClient.SqlParameter dtTempPasswordGeneratedTime = new System.Data.SqlClient.SqlParameter("@par_dtTempPasswordGeneratedTime", SqlDbType.DateTime);
				if (par_dtTempPasswordGeneratedTime == clC.SELL_BLANK_DTDATETIME)
				{
					dtTempPasswordGeneratedTime.Value = System.DBNull.Value;
				}
				else
				{
					dtTempPasswordGeneratedTime.Value = par_dtTempPasswordGeneratedTime;
				}
				oCommand.Parameters.Add(dtTempPasswordGeneratedTime);

				//parameter
				System.Data.SqlClient.SqlParameter sTempPasswordDeliveryMethod = new System.Data.SqlClient.SqlParameter("@par_sTempPasswordDeliveryMethod", SqlDbType.VarChar, 8);
				if (par_sTempPasswordDeliveryMethod == "")
				{
					sTempPasswordDeliveryMethod.Value = System.DBNull.Value;
				}
				else
				{
					sTempPasswordDeliveryMethod.Value = par_sTempPasswordDeliveryMethod;
				}
				oCommand.Parameters.Add(sTempPasswordDeliveryMethod);

				//parameter
				System.Data.SqlClient.SqlParameter sTempPasswordGeneratedBy = new System.Data.SqlClient.SqlParameter("@par_sTempPasswordGeneratedBy", SqlDbType.VarChar, 4);
				if (par_sTempPasswordGeneratedBy == "")
				{
					sTempPasswordGeneratedBy.Value = System.DBNull.Value;
				}
				else
				{
					sTempPasswordGeneratedBy.Value = par_sTempPasswordGeneratedBy;
				}
				oCommand.Parameters.Add(sTempPasswordGeneratedBy);

				//parameter
				System.Data.SqlClient.SqlParameter sTempPasswordSentTo = new System.Data.SqlClient.SqlParameter("@par_sTempPasswordSentTo", SqlDbType.NVarChar, 80);
				if (par_sTempPasswordSentTo == "")
				{
					sTempPasswordSentTo.Value = System.DBNull.Value;
				}
				else
				{
					sTempPasswordSentTo.Value = par_sTempPasswordSentTo;
				}
				oCommand.Parameters.Add(sTempPasswordSentTo);

				//'parameter
				//Dim dtPasswordUserChangedTime As New SqlClient.SqlParameter("@par_dtPasswordUserChangedTime", SqlDbType.DateTime)
				//If par_dtPasswordUserChangedTime = clC.SELL_BLANK_DTDATETIME Then
				//    dtPasswordUserChangedTime.Value = System.DBNull.Value
				//Else
				//    dtPasswordUserChangedTime.Value = par_dtPasswordUserChangedTime
				//End If
				//oCommand.Parameters.Add(dtPasswordUserChangedTime)

				//parameter MI 9/24/13
				System.Data.SqlClient.SqlParameter bPasswordDoesntExpire = new System.Data.SqlClient.SqlParameter("@par_bPasswordDoesntExpire", SqlDbType.Bit);
				bPasswordDoesntExpire.Value = Convert.ToInt16(par_bPasswordDoesntExpire);
				oCommand.Parameters.Add(bPasswordDoesntExpire);

				//parameter MI 10/4/13
				System.Data.SqlClient.SqlParameter sPasswordChangedBy = new System.Data.SqlClient.SqlParameter("@par_sPasswordChangedBy", SqlDbType.VarChar, 4);
				if (par_sPasswordChangedBy == "")
				{
					sPasswordChangedBy.Value = System.DBNull.Value;
				}
				else
				{
					sPasswordChangedBy.Value = par_sPasswordChangedBy;
				}
				oCommand.Parameters.Add(sPasswordChangedBy);

				//parameter MI 10/4/13
				System.Data.SqlClient.SqlParameter dtPasswordChangedTime = new System.Data.SqlClient.SqlParameter("@par_dtPasswordChangedTime", SqlDbType.DateTime);
				if (par_dtPasswordChangedTime == clC.SELL_BLANK_DTDATETIME)
				{
					dtPasswordChangedTime.Value = System.DBNull.Value;
				}
				else
				{
					dtPasswordChangedTime.Value = par_dtPasswordChangedTime;
				}
				oCommand.Parameters.Add(dtPasswordChangedTime);

				//parameter
				System.Data.SqlClient.SqlParameter bBill = new System.Data.SqlClient.SqlParameter("@par_bBill", SqlDbType.Bit);
				bBill.Value = Convert.ToInt16(par_bBill);
				oCommand.Parameters.Add(bBill);


				//Result parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				try
				{
					//execute
					oReader = oCommand.ExecuteReader();
					//Now you can grab the output parameter's value...
					iResult = Convert.ToInt16(retValParam.Value);

					oReader.Close();
					oConnection.Close();

				}
				catch (System.Data.SqlClient.SqlException sqlex)
				{
					if (sqlex.ErrorCode == -2146232060)
					{
						goErr.SetWarning(35000, sProc, "The login/login group record can't be edited because a duplicate exists.");
						//MessTranslate
					}
					else
					{
						goErr.SetWarning(35000, sProc, "Editing login or login group record failed due to database error '" + sqlex.ErrorCode + "': '" + sqlex.Message + "'");
						//MessTranslate
					}
					return false;
				}

				if (iResult == 0)
				{
					return true;
				}
				else
				{
					switch (iResult)
					{
						case -1: //when fnGetMe returns '' User ID
							goErr.SetWarning(35503, sProc);
							break;
							//35503: Metadata or permission line couldn't be written because fnGetMe returned an invalid user ID.
						case -2: //when @par_sUserCode is ''
							goErr.SetWarning(35504, sProc);
							break;
							//35504: Metadata or permission line couldn't be written because fnGetMe returned an invalid user code.
						case -3: //LogonName duplicate error (SS error 2601)
							goErr.SetWarning(35000, sProc, "The login or login group record can't be edited because a duplicate exists.");
							break;
							//MessTranslate
						case -4:
							goErr.SetWarning(35000, sProc, "The login or login group record can't be edited because it doesn't exist.");
							break;
							//MessTranslate
						default:
							goErr.SetWarning(35000, sProc, "Editing login or login group record failed: database error is '" + iResult + "'.");
							break;
							//MessTranslate
					}
					return false;
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return bResult;

		}

		public bool LoginEditIgnorePassword(string par_sGIDID, string par_sLogonName, string par_sUserGIDID, string par_sGroupGIDID, string par_sBillingType, bool par_bEnabled, bool par_bBill)
		{

			//Public Function LoginEditIgnorePassword(ByVal par_sGIDID As String, _
			//            ByVal par_sLogonName As String, _
			//            ByVal par_sUserGIDID As String, _
			//            ByVal par_sGroupGIDID As String, _
			//            ByVal par_sBillingType As String, _
			//            ByVal par_bEnabled As Boolean, _
			//            ByVal par_dtBillingStartTime As DateTime, _
			//            ByVal par_dtDeactivationTime As DateTime) As Boolean

			//MI 10/24/13 LoginEditIgnorePassword: Added par_bBill.
			//MI 10/23/13 LoginEditIgnorePassword: Deprecating par_dtBillingStartTime, par_dtDeactivationTime.
			//MI 10/11/13 Created from LoginEdit.
			//PURPOSE:
			//       Edit an XU (User login or User Login Group) record without changing any of
			//       the password-related fields. This is intended to be called from Admin>
			//       User/Group Permissions dialog (diaAdmPerm) when saving changes to the
			//       login (when selecting another login or clicking OK). Password information
			//       is written, together with other login properties by Generate Password, 
			//       Reset Password and Set Permanent Password buttons in this dialog. As 
			//       the user of the login could change the password before the admin selects
			//       anothear login or exits the dialog, we don't want the information about 
			//       the temporary password written when saving the login information the second time. 
			//       If an error occurs, sets a warning. Since none of the parameters is optional,
			//       all field values must be read and set explicitly even in the case of login
			//       Group to which most of the parameters don't pertain.
			//       To set the password and other password-related fields, use 
			//       LoginEdit instead.
			//WARNING:
			//       Since all fields are written explicitly, the XU writing is record-level, 
			//       not field-level. If user 1 opens the XU record (by selecting a Login in
			//       diaAdmPerm.aspx), user 2 edits and saves the same XU record, and
			//       user 1 then saves, the changes made by user 2 will be lost.
			//PARAMETERS:
			//       Note: Except for password, none of the parameters is optional to prevent inadvertently 
			//       overwriting values. Since pLoginEditIgnorePassword performs a single 
			//       UPDATE of the XU record, none of the fields can be skipped. 
			//       For more, see notes under pLoginEdit.
			//
			//       par_sGIDID: GID_ID of the XU record to edit.
			//       par_sLogonName: name under which the user will log on or the 
			//           name of the login group.
			//       par_sUserGIDID: GID_ID of the US record associated. If
			//           this is a login group, omit or send ''. 
			//       par_sGroupGIDID: GID_ID of the Login Group record from XU table.
			//           Only applicable to Logins. If this is a login group, omit or
			//           send ''. This WILL write the Group GID_ID in Login Group
			//           records (which is nonsensical), but this will be ignored by the UI.
			//       	--------- PARAMETERS BELOW APPLICABLE ONLY TO LOGINS, NOT GROUPS ----------
			//       par_siBillingType: 0=Sales (default); 1=Lite; 2=Partner; 255=Do not bill.
			//       par_bEnabled bit: when 1 (default), the login is enabled, otherwise
			//       	the user can't log in. 
			//       par_dtBillingStartTime: DEPRECATED start billing on this date. The default value
			//       	in SS is GETUTCDATE().
			//       par_dtDeactivationTime: DEPRECATED deactivate the login on this datetime. 
			//       	NULL means infinity, never deactivate. 
			//       par_bBill: when 1 (default), the login will be billed.

			//RETURNS:
			//       True on success, False otherwise. Retrieve the error via goErr.GetLastError
			//CREATE PROCEDURE pLoginEdit 
			//	@par_uGIDID uniqueidentifier,
			//	@par_sLogonName nvarchar(20),
			//	@par_sPassword nvarchar(20),
			//	@par_uUserGIDID uniqueidentifier = NULL,
			//   @par_uGroupGIDID uniqueidentifier = NULL

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DETAILS, true);

			bool bResult = true;
			int iResult = 0;

			//Try

			//------------ Validate ------------
			if (par_sGIDID.Trim(' ') == "")
			{
					goErr.SetError(10103, sProc, "", "par_sGIDID", sProc, par_sGIDID);
					//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
				}
				if (par_sLogonName.Trim(' ') == "")
				{
					goErr.SetWarning(35000, sProc, "The login record can't be edited because the Login Name is blank.");
					//MessTranslate
				}
				//If Trim(par_sPassword) = "" Then
				//    goErr.SetWarning(35000, sProc, "The login record can't be edited because the Password is blank.")
				//    'MessTranslate
				//End If

				//------------- Edit ---------------
				System.Data.SqlClient.SqlConnection oConnection = new System.Data.SqlClient.SqlConnection();
				oConnection = goData.GetConnection();
				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;

				oCommand.CommandText = "pLoginEditIgnorePassword";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter
				System.Data.SqlClient.SqlParameter uGIDID = new System.Data.SqlClient.SqlParameter("@par_uGIDID", SqlDbType.UniqueIdentifier);
				if (par_sGIDID == "")
				{
					uGIDID.Value = System.DBNull.Value;
				}
				else
				{
					uGIDID.Value = goTR.StringToGuid(par_sGIDID);
				}
				oCommand.Parameters.Add(uGIDID);

				//parameter
				System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sLogonName", SqlDbType.VarChar);
				strPage.Value = par_sLogonName;
				oCommand.Parameters.Add(strPage);

				//parameter
				System.Data.SqlClient.SqlParameter uUserGIDID = new System.Data.SqlClient.SqlParameter("@par_uUserGIDID", SqlDbType.UniqueIdentifier);
				if (par_sUserGIDID == "" || par_sUserGIDID == "<%NONE%>")
				{
					uUserGIDID.Value = System.DBNull.Value;
				}
				else
				{
					uUserGIDID.Value = goTR.StringToGuid(par_sUserGIDID);
				}
				oCommand.Parameters.Add(uUserGIDID);

				//parameter
				System.Data.SqlClient.SqlParameter uGroupGIDID = new System.Data.SqlClient.SqlParameter("@par_uGroupGIDID", SqlDbType.UniqueIdentifier);
				if (par_sGroupGIDID == "" || par_sGroupGIDID == "<%NONE%>")
				{
					uGroupGIDID.Value = System.DBNull.Value;
				}
				else
				{
					uGroupGIDID.Value = goTR.StringToGuid(par_sGroupGIDID);
				}
				oCommand.Parameters.Add(uGroupGIDID);

				//parameter
				System.Data.SqlClient.SqlParameter parBillingType = new System.Data.SqlClient.SqlParameter("@par_siBillingType", SqlDbType.TinyInt);
				parBillingType.Value = goTR.StringToNum(par_sBillingType, "0");
				oCommand.Parameters.Add(parBillingType);

				//parameter
				System.Data.SqlClient.SqlParameter bEnabled = new System.Data.SqlClient.SqlParameter("@par_bEnabled", SqlDbType.Bit);
				bEnabled.Value = Convert.ToInt16(par_bEnabled);
				oCommand.Parameters.Add(bEnabled);

				//'parameter
				//Dim dtBillingStartTime As New SqlClient.SqlParameter("@par_dtBillingStartTime", SqlDbType.DateTime)
				//If par_dtBillingStartTime = clC.SELL_BLANK_DTDATETIME Then
				//    dtBillingStartTime.Value = System.DBNull.Value
				//Else
				//    dtBillingStartTime.Value = par_dtBillingStartTime
				//End If
				//oCommand.Parameters.Add(dtBillingStartTime)

				//'parameter
				//Dim dtDeactivationTime As New SqlClient.SqlParameter("@par_dtDeactivationTime", SqlDbType.DateTime)
				//If par_dtDeactivationTime = clC.SELL_BLANK_DTDATETIME Then
				//    dtDeactivationTime.Value = System.DBNull.Value
				//Else
				//    dtDeactivationTime.Value = par_dtDeactivationTime
				//End If
				//oCommand.Parameters.Add(dtDeactivationTime)

				//parameter
				System.Data.SqlClient.SqlParameter bBill = new System.Data.SqlClient.SqlParameter("@par_bBill", SqlDbType.Bit);
				bBill.Value = Convert.ToInt16(par_bBill);
				oCommand.Parameters.Add(bBill);


				//Result parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				try
				{
					//execute
					oReader = oCommand.ExecuteReader();
					//Now you can grab the output parameter's value...
					iResult = Convert.ToInt16(retValParam.Value);

					oReader.Close();
					oConnection.Close();

				}
				catch (System.Data.SqlClient.SqlException sqlex)
				{
					if (sqlex.ErrorCode == -2146232060)
					{
						goErr.SetWarning(35000, sProc, "The login/login group record can't be edited because a duplicate exists.");
						//MessTranslate
					}
					else
					{
						goErr.SetWarning(35000, sProc, "Editing login or login group record failed due to database error '" + sqlex.ErrorCode + "': '" + sqlex.Message + "'");
						//MessTranslate
					}
					return false;
				}

				if (iResult == 0)
				{
					return true;
				}
				else
				{
					switch (iResult)
					{
						case -1: //when fnGetMe returns '' User ID
							goErr.SetWarning(35503, sProc);
							break;
							//35503: Metadata or permission line couldn't be written because fnGetMe returned an invalid user ID.
						case -2: //when @par_sUserCode is ''
							goErr.SetWarning(35504, sProc);
							break;
							//35504: Metadata or permission line couldn't be written because fnGetMe returned an invalid user code.
						case -3: //LogonName duplicate error (SS error 2601)
							goErr.SetWarning(35000, sProc, "The login or login group record can't be edited because a duplicate exists.");
							break;
							//MessTranslate
						case -4:
							goErr.SetWarning(35000, sProc, "The login or login group record can't be edited because it doesn't exist.");
							break;
							//MessTranslate
						default:
							goErr.SetWarning(35000, sProc, "Editing login or login group record failed: database error is '" + iResult + "'.");
							break;
							//MessTranslate
					}
					return false;
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return bResult;

		}

		public bool LoginDelete(string par_sGIDID, string par_sLogonName = "")
		{
			//MI 9/23/08 Added support for login groups.
			//PURPOSE:
			//       Delete a record in XU (User login) by its GID_ID or LogonName value. XU records
			//       can be true logins or login groups when CHK_LoginGroup is set to 1.
			//PARAMETERS:
			//       par_sGIDID: GID_ID of the XU record to delete. If "", par_sLogonName is used to find the record.
			//       par_sLogonName: If par_sGIDID is "", LogonName of the XU record to delete.
			//RETURNS:
			//       True on success, false otherwise. Retrieve the error via goErr.GetLastError

			//--	@par_uGIDID uniqueidentifier
			//--	@par_sLogonName nvarchar(20)

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_NONE, true);

			bool bResult = true;
			int iResult = 0;

			//Try

			System.Data.SqlClient.SqlConnection oConnection = new System.Data.SqlClient.SqlConnection();
				oConnection = goData.GetConnection();
				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;

				oCommand.CommandText = "pLoginDelete";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter
				System.Data.SqlClient.SqlParameter uUserGIDID = new System.Data.SqlClient.SqlParameter("@par_uGIDID", SqlDbType.UniqueIdentifier);
				if (par_sGIDID == "")
				{
					uUserGIDID.Value = System.DBNull.Value;
				}
				else
				{
					uUserGIDID.Value = goTR.StringToGuid(par_sGIDID);
				}
				oCommand.Parameters.Add(uUserGIDID);

				//parameter
				System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sLogonName", SqlDbType.VarChar);
				strPage.Value = par_sLogonName;
				oCommand.Parameters.Add(strPage);

				//Result parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				try
				{
					//execute
					oReader = oCommand.ExecuteReader();
					//Now you can grab the output parameter's value...
					iResult = Convert.ToInt16(retValParam.Value);

					oReader.Close();
					oConnection.Close();

				}
				catch (System.Data.SqlClient.SqlException sqlex)
				{
					//If sqlex.ErrorCode = -2146232060 Then
					goErr.SetWarning(35000, sProc, "Deleting login/login group record failed due to database error '" + sqlex.ErrorCode + "': '" + sqlex.Message + "'");
					//MessTranslate
					return false;
				}

				if (iResult == 0)
				{
					return true;
				}
				else
				{
					switch (iResult)
					{
						case -3: //Neither par_uGIDID nor par_sLogonName provided.
							goErr.SetError(35000, sProc, "The login or login group record can't be deleted because neither par_uGIDID not par_uLogonName were provided to pLoginDelete.");
							break;
							//MessTranslate
						case -4: //Record not found.
							goErr.SetWarning(35000, sProc, "The login or login group record doesn't exist.");
							break;
							//MessTranslate
						default:
							goErr.SetWarning(35000, sProc, "Adding login or login group record failed: database error is '" + iResult + "'.");
							break;
							//MessTranslate
					}
					return false;
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return bResult;

		}



		public bool LoginResetAgreement(string par_sGIDID)
		{
			//MI 5/17/07 Created.
			//PURPOSE:
			//       Delete the value of DTT_AgrAccepted field in the XU (User login) record.
			//PARAMETERS:
			//       par_sGIDID: GID_ID of the XU record.
			//RETURNS:
			//       True on success, False otherwise. Retrieve the error via goErr.GetLastError
			//CREATE PROCEDURE pLoginResetAgreement 
			//	@par_uGIDID uniqueidentifier

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			bool bResult = true;
			int iResult = 0;

			//Try

			//------------ Validate ------------
			if (par_sGIDID.Trim(' ') == "")
			{
					goErr.SetError(10103, sProc, "", "par_sGIDID", sProc, par_sGIDID);
					//10103: Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
				}

				//------------- Edit ---------------
				System.Data.SqlClient.SqlConnection oConnection = new System.Data.SqlClient.SqlConnection();
				oConnection = goData.GetConnection();
				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;

				oCommand.CommandText = "pLoginResetAgreement";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter
				System.Data.SqlClient.SqlParameter uGIDID = new System.Data.SqlClient.SqlParameter("@par_uGIDID", SqlDbType.UniqueIdentifier);
				if (par_sGIDID == "")
				{
					uGIDID.Value = System.DBNull.Value;
				}
				else
				{
					uGIDID.Value = goTR.StringToGuid(par_sGIDID);
				}
				oCommand.Parameters.Add(uGIDID);

				//Result parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				try
				{
					//execute
					oReader = oCommand.ExecuteReader();
					//Now you can grab the output parameter's value...
					iResult = Convert.ToInt16(retValParam.Value);

					oReader.Close();
					oConnection.Close();

				}
				catch (System.Data.SqlClient.SqlException sqlex)
				{
					goErr.SetWarning(35000, sProc, "Editing login record failed due to database error '" + sqlex.ErrorCode + "': '" + sqlex.Message + "'");
					//MessTranslate
					return false;
				}

				if (iResult == 0)
				{
					return true;
				}
				else
				{
					switch (iResult)
					{
						case -1: //when fnGetMe returns '' User ID
							goErr.SetWarning(35503, sProc);
							break;
							//35503: Metadata or permission line couldn't be written because fnGetMe returned an invalid user ID.
						case -2: //when @par_sUserCode is ''
							goErr.SetWarning(35504, sProc);
							break;
							//35504: Metadata or permission line couldn't be written because fnGetMe returned an invalid user code.
						case -4:
							goErr.SetWarning(35000, sProc, "The login record can't be edited because it doesn't exist.");
							break;
							//MessTranslate
						default:
							goErr.SetWarning(35000, sProc, "Editing login record failed: database error is '" + iResult + "'.");
							break;
							//MessTranslate
					}
					return false;
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return bResult;

		}

		public bool PageDelete(string par_sSection, string par_sPageID)
		{
			//MI 8/17/06 Added treating blank par_sSection as global.
			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Delete the permission record (page). If multiple pages exist with the same
			//		section and page ID, this method deletes all such pages.
			//PARAMETERS:
			//		par_sSection: "GLOBAL" or User's TID
			//		par_sPageID: page ID of the page, typically in the <PREFIX>_<TID> format,
			//						for example: DSK_xxxxxxxx (desktop) or SCR_xxxxxxx (script)
			//RETURNS:
			//		*** Always true. *** In NGP it was True/False:
			//			False: if the page couldn't be found or deletion failed.
			//			True: page was found and the deletion succeeded.
			//EXAMPLE:
			//		sSection = goP.GetUserTID()
			//		sPageID = "ACCESS"
			//       IF not goPerm.PageDelete(sSection,sPageID) THEN
			//			goLog.Log(sProc, "Deletion failed.")
			//		ELSE
			//			goLog.Log(sProc, Deletion succeeded.")
			//		END

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sSection = par_sSection.ToUpper().Trim(' ');
			string sPage = par_sPageID.ToUpper().Trim(' ');
			int iResult = 0;

			try
			{

				System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;

				oCommand.CommandText = "pPermPageDelete";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter
				System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier);
				if (sSection == "GLOBAL" || sSection == "")
				{
					uSection.Value = System.DBNull.Value;
				}
				else
				{
					uSection.Value = goTR.StringToGuid(sSection);
				}
				oCommand.Parameters.Add(uSection);

				//parameter
				System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar);
				strPage.Value = sPage;
				oCommand.Parameters.Add(strPage);

				//return parameter
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
				retValParam.Direction = ParameterDirection.ReturnValue;
				oCommand.Parameters.Add(retValParam);

				//execute

				oReader = oCommand.ExecuteReader();

				//Now you can grab the output parameter's value...
				iResult = Convert.ToInt16(retValParam.Value);

				oReader.Close();
				oConnection.Close();

				if (iResult == 0)
				{
					return true;
				}
				else
				{
					return false; //no error checking in sproc yet
				}

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return false;
			}

		}

		public string PageRead(string par_sSection, string par_sPage, string par_sDefaultPage = "", bool par_bUserOnly = false)
		{

			//MI 3/27/07 Added default Page evaluation.
			//MI 5/5/06  Added allowing a blank par_sSection (treated as GLOBAL)

			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Read a PAGE of permissions, merging automatically DEFAULT, GLOBAL and USER information (if asked for)
			//PARAMETERS:
			//		par_sSection	= 	Section value (Permissions are organized in Sections, Pages and Lines like metadata)
			//		par_sPage		= 	Page name (generally composed by a prefix (DSK_) and a SUID
			//		par_sDefaultPage=	Default page returned if nothing found
			//		par_bUserOnly	=	If TRUE, read only the par_sSection, and not the global one
			//RETURNS:
			//		A string containing the PAGE of permission lines.
			//HOW IT WORKS:
			//		Reads in uSer and global section and merge the information together (and with the default)
			//		If you don't want to merge with Global, set par_bUserOnly to TRUE
			//		if you don't want to merge with default, set par_sDefaultPage to ""
			//EXAMPLE:
			//		goPerm("GLOBAL","ACCESS","") will return global ACCESS permissions

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sSection = par_sSection.Trim(' ').ToUpper(); //Transform the possibly numerical value into a string
			string sPage = par_sPage.Trim(' ').ToUpper(); //Transform the possibly numerical value into a string
			long lRec = 0;
			string sDefaultPage = par_sDefaultPage;
			bool bUserOnly = par_bUserOnly;

			//A blank section is 'global'
			if (sSection == "")
			{
				sSection = "GLOBAL";
			}

			//Blank page - return empty string
			if (sPage == "")
			{
				return "";
			}

			//setup sql connection
			if (oMetaConnection == null)
			{
				oMetaConnection = goData.GetConnection();
			}
			System.Data.SqlClient.SqlConnection oConnection = oMetaConnection;
			System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader oReader = null;
			string sReturn = "";

			try
			{

				oCommand.CommandText = "pPermPageRead";
				oCommand.CommandType = CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter 
				System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier);
				if (sSection == "GLOBAL")
				{
					uSection.Value = System.DBNull.Value;
				}
				else
				{
					uSection.Value = goTR.StringToGuid(sSection);
				}
				oCommand.Parameters.Add(uSection);

				//parameter 
				System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sPage", SqlDbType.VarChar);
				strPage.Value = sPage;
				oCommand.Parameters.Add(strPage);

				//parameter 
				System.Data.SqlClient.SqlParameter iUserOnly = new System.Data.SqlClient.SqlParameter("@par_bUserOnly", SqlDbType.Bit);
				iUserOnly.Value = Convert.ToInt16(bUserOnly);
				oCommand.Parameters.Add(iUserOnly);

				//execute sproc
				oReader = oCommand.ExecuteReader();

				//read returned value
				if (oReader.HasRows)
				{
					while (oReader.Read())
					{
						string sKey = oReader[0].ToString().Trim(' ');
						string sVal = oReader[1].ToString().Trim(' ');
						sReturn = sReturn + sKey + "=" + sVal + "\r\n";
					}
				}
				else
				{
					sReturn = "";
				}
				sReturn = goTR.MergeIniStrings(sReturn, par_sDefaultPage);

				oReader.Close();
				//oConnection.Close()

			}
			catch (Exception ex)
			{

				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If

				sReturn = "";

			}

			return sReturn;

		}

		public bool PageWrite(string par_sSection, string par_sPage, string par_sVal, string par_sFriendlyName = "", bool par_bLocal = false, string par_sTextValue = "")
		{

			//==> par_bLocal is currently ignored until syncing is supported

			//AUTHOR: 
			//       WGT 3/15/2006
			//PURPOSE:
			//		Write a permissions Page identified by a section and Page
			//PARAMETERS:
			//		par_sSection		= First part of the identifier, "GLOBAL" for global permissions
			//		par_sPage			= Second part of the identifier... The identifier is supposed UNIQUE because the
			//							  Page should contain XXX_GID (GID is a ''UNIQUE'' generated ID)
			//		par_sVal			= String to write in the memo field
			//		par_sFriendlyName	= Friendly Name of the page that is stored in the NAME property
			//							  under the default (US) language.
			//		par_bLocal			= *** Ignored in SellSQL ***
			//                           If true, the page is saved as local instead of Protected for user section and shared for GLOBAL
			//		par_sTextValue		= *** Ignored in SellSQL ***
			//                           in NGP was the value to store in TXT_TextValue
			//RETURNS:
			//		TRUE or FALSE depending of the result of the writing
			//HOW IT WORKS:
			//		First try to read the record identified by Section+Page, then make a hAdd or 
			//		hModify depending if it has been previously found or not
			//EXAMPLE:
			//       Dim sPerms as string
			//       goTr.StrWrite(sPerms, "THISPERMISSION", "1")
			//       goTr.StrWrite(sPerms, "THATPERMISSION", "0")
			//		goPerm.PageWrite("GLOBAL", "ACCESS", sPerms)

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			bool bLocal = par_bLocal;
			string sSection = par_sSection.Trim(' ').ToUpper();
			string sPage = par_sPage.Trim(' ').ToUpper();
			string sTextValue = par_sTextValue;
			string sVal = par_sVal;
			string sFriendlyName = par_sFriendlyName;
			int iCRPos = 0;
			string sLine = null;
			string sProp = null;
			string sValue = null;
			int iCount = 1;
			int iEqualPos = 0;
			bool bResult = true;

			if (sPage == "")
			{
				goErr.SetError(35506, sProc, "", par_sSection, par_sPage, par_sFriendlyName, par_bLocal.ToString(), par_sVal);
				//35506: PageWrite was called with a blank page parameter. Metadata or permission page can't be written.
				// 
				//Section:    '[1]'
				//Page:       '[2]'
				//Friendly(Name) '[3]'
				//Local:      '[4]'
				//Value:      '[5]'
				//[6]
				return false;
			}

			//Add the final CR if missing
			if (sVal.Substring(sVal.Length - 2) != "\r\n")
			{
				sVal += "\r\n"; //*** MI 5/5/06 Changed += to &=
			}

			//---------- TEST WRITING TO SS ---------
			//Test whether we can write a test line without errors.
			//This is needed because the whole page will be deleted next, before lines are
			//written. If the writing of the lines fails, the page will be lost or incomplete.
			if (!LineWrite("GLOBAL", "OTH_TEST", "clPerm.PageWrite", "Test"))
			{
				goErr.SetError(35505, sProc, "", sSection, sPage);
				//35505: Metadata or permission page couldn't be written. Make sure that the server is running and that the ASP user has adequate permissions on it.
				// 
				//Section:    '[1]'
				//Page:       '[2]'
				return false;
			}

			//------------ DELETE PAGE -----------
			//Run pPermPageDelete to start with a clean slate, otherwise we
			//have to go PageRead first and delete the properties one by one
			if (!PageDelete(sSection, sPage))
			{
				goErr.SetError(35500, sProc, "", sSection, sPage);
				//35500: Deleting a metadata or permission page failed. Section: '[1]', page: '[2]'. [3]
				return false;
			}

			//==> Replace this later with reading the whole page and comparing lines, then
			//updating the changed lines and deleting the deleted lines.
			//This will be faster and will preserve the ModTime of unchanged lines, which
			//are all being wiped out now. OR MAYBE THEY SHOULD ALL BE UPDATED?

			try
			{

				System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
				iCount = 1;
				iEqualPos = 0;
				do
				{
					//Get the line
					sLine = goTR.ExtractString(sVal, iCount, "\r\n");
					//Skip blank line
					if (sLine == "")
					{
						goto BottomOfLoop;
					}
					//clC.EOT indicates end of loop
					if (sLine[0] == clC.EOT)
					{
						break;
					}

					iEqualPos = sLine.IndexOf("=") + 1;
					if (iEqualPos < 1)
					{
						//Line doesn't have an =. It may be a comment?
						goErr.SetWarning(35501, sProc, "", sLine);
						//35501: Invalid metadata line (no '='): '[1]'. [2]
						goto BottomOfLoop;
					}

					sProp = goTR.FromTo(sLine, 1, iEqualPos - 1);
					sValue = goTR.FromTo(sLine, iEqualPos + 1, -1);

					//Skip blank property
					if (sProp.Trim(' ') == "")
					{
						goto BottomOfLoop;
					}

					if (!LineWrite(sSection, sPage, sProp, sValue, ref oConnection))
					{
						bResult = false;
						goErr.SetWarning(35502, sProc, "", sSection, sPage, sProp, sValue);
						//35502: Metadata or permission line failed to be written.
						//Section:            '[1]'
						//Page:               '[2]'
						//Property: '[3]'
						//Value: '[4]'
						//[5]
					}

	BottomOfLoop:
					iCount = iCount + 1;
				} while (true);

				//write the friendly value...
				if (sFriendlyName != "")
				{
					if (!LineWrite(sSection, sPage, "NAME", sFriendlyName, ref oConnection))
					{
						bResult = false;
						goErr.SetWarning(35502, sProc, "", sSection, sPage, "NAME", sFriendlyName);
						//35502: Metadata or permission line failed to be written.
						//Section:            '[1]'
						//Page:               '[2]'
						//Property: '[3]'
						//Value: '[4]'
						//[5]
					}
				}

				oConnection.Close();

				if (bResult)
				{
					return true;
				}
				else
				{
					return false;
				}

			}
			catch (Exception ex)
			{

				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If

				return false;

			}

		}


		//    Public Function GetDefaultAccessPermissions(Optional ByVal par_sFile As String = "") As String
		//        'MI 10/31/13 This is coded in diaAdmPerm.FillAccess() now. Deprecating this method.
		//        'MI 9/17/09 This method currently isn't called from anywhere.
		//        'MI 3/26/07 Created.
		//        'PURPOSE:
		//        '       Retrieves an ini string with selective access permissions either for one file
		//        '       or all of them. This returns all '(none)' permissions to prevent users from gaining
		//        '       greater permissions than intended if some permission metadata is deleted inadvertently.
		//        '       Default permissions for each file should be defined as GLOBAL metadata in the XP
		//        '       table when creating a file. If permissions are not defined, they should be none.
		//        'PARAMETERS: 
		//        '       par_sFile: File for which to retrieve access permissions. If blank, permissions
		//        '           are returned for all files.
		//        'RETURNS:
		//        '       Ini format string to be read with goTr.StrRead().

		//        'Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
		//        'goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

		//        Dim aFiles As New clArray
		//        Dim i As Integer
		//        Dim sFile As String = UCase(par_sFile)
		//        Dim s As String = ""

		//        If sFile <> "" Then
		//            goTR.StrWrite(s, sFile & "_R", "2")     'originally was "0"
		//            goTR.StrWrite(s, sFile & "_A", "2")     'originally was "0"
		//            goTR.StrWrite(s, sFile & "_E", "1")     'originally was "0"
		//            goTR.StrWrite(s, sFile & "_D", "1")     'originally was "0"
		//            goTR.StrWrite(s, sFile & "_RCOND", GetDataAccessDefaultFilter(sFile, "CONDITION"))
		//            goTR.StrWrite(s, sFile & "_ECOND", GetDataAccessDefaultFilter(sFile, "CONDITION"))
		//            goTR.StrWrite(s, sFile & "_DCOND", GetDataAccessDefaultFilter(sFile, "CONDITION"))
		//            goTR.StrWrite(s, sFile & "_RFILTER", GetDataAccessDefaultFilter(sFile))
		//            goTR.StrWrite(s, sFile & "_EFILTER", GetDataAccessDefaultFilter(sFile))
		//            goTR.StrWrite(s, sFile & "_DFILTER", GetDataAccessDefaultFilter(sFile))

		//        Else
		//            aFiles = goData.GetFiles()
		//            For i = 0 To aFiles.GetDimension() - 1
		//                sFile = UCase(aFiles.GetInfo(i))
		//                'If Not goData.IsFileSystem(sFile) Then GoTo ProcessNextFile
		//                '                Select Case sFile
		//                '                    Case "MS"
		//                '                        'Message
		//                '                        goTR.StrWrite(s, sFile & "_R", "1")
		//                '                        goTR.StrWrite(s, sFile & "_A", "2")
		//                '                        goTR.StrWrite(s, sFile & "_E", "1")
		//                '                        goTR.StrWrite(s, sFile & "_D", "1")
		//                '                        goTR.StrWrite(s, sFile & "_R_COND", "LNK_To_US='<%MEID%>' or LNK_Createdby_US='<%MEID%>'")
		//                '                        goTR.StrWrite(s, sFile & "_E_COND", "LNK_To_US='<%MEID%>' or LNK_Createdby_US='<%MEID%>'")
		//                '                        goTR.StrWrite(s, sFile & "_D_COND", "LNK_CreatedBy_US='<%MEID%>'")
		//                '                    Case Else
		//                '                        goTR.StrWrite(s, sFile & "_R", "2")
		//                '                        goTR.StrWrite(s, sFile & "_A", "2")
		//                '                        goTR.StrWrite(s, sFile & "_E", "1")
		//                '                        goTR.StrWrite(s, sFile & "_D", "1")
		//                '                        goTR.StrWrite(s, sFile & "_R_COND", "LNK_CreatedBy_US='<%MEID%>'")
		//                '                        goTR.StrWrite(s, sFile & "_E_COND", "LNK_CreatedBy_US='<%MEID%>'")
		//                '                        goTR.StrWrite(s, sFile & "_D_COND", "LNK_CreatedBy_US='<%MEID%>'")
		//                '                End Select
		//                goTR.StrWrite(s, sFile & "_R", "2")     'originally was "0"
		//                goTR.StrWrite(s, sFile & "_A", "2")     'originally was "0"
		//                goTR.StrWrite(s, sFile & "_E", "1")     'originally was "0"
		//                goTR.StrWrite(s, sFile & "_D", "1")     'originally was "0"
		//                goTR.StrWrite(s, sFile & "_R_COND", GetDataAccessDefaultFilter(sFile, "CONDITION"))
		//                goTR.StrWrite(s, sFile & "_E_COND", GetDataAccessDefaultFilter(sFile, "CONDITION"))
		//                goTR.StrWrite(s, sFile & "_D_COND", GetDataAccessDefaultFilter(sFile, "CONDITION"))
		//                goTR.StrWrite(s, sFile & "_RFILTER", GetDataAccessDefaultFilter(sFile))
		//                goTR.StrWrite(s, sFile & "_EFILTER", GetDataAccessDefaultFilter(sFile))
		//                goTR.StrWrite(s, sFile & "_DFILTER", GetDataAccessDefaultFilter(sFile))
		//ProcessNextFile:
		//            Next

		//        End If

		//        Return s

		//    End Function



		//Public Function GetDataAccssPermLbl(ByVal par_sPermission As String) As String
		//    'MI 3/27/07 Moved to clPerm from clDefaults::GetDataAccssPermLbl.
		//    'PURPOSE:
		//    '		Retrieve the label for a data access permission in the current language.
		//    'PARAMETERS:
		//    '		par_sPermission: String or number: a permission constant
		//    '		defined in Selltis.wl:
		//    '			SELL_DEFAULT			=  -1
		//    '			SELL_NO					=	0
		//    '			SELL_SELECTIVE			=	1
		//    '			SELL_FULL				=	2
		//    'RETURNS:
		//    '		Label as string.

		//    'Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
		//    'goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

		//    Dim sResult As String = ""

		//    Select Case par_sPermission
		//        Case clC.SELL_DEFAULT       '-1
		//            sResult = "(Default)" 'messtranslate MessTraduit(5237)   '5237:<Default>
		//        Case clC.SELL_NO            '0
		//            sResult = "None" 'messtranslate MessTraduit(5238)   '5238:None
		//        Case clC.SELL_SELECTIVE     '1
		//            sResult = "Selective" 'messtranslate MessTraduit(5239)   '5239:Selective
		//        Case clC.SELL_FULL          '2
		//            sResult = "Full" 'messtranslate MessTraduit(5240)   '5240:Full
		//    End Select

		//    Return sResult
		//End Function



		public void Initialize()
		{
			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			//goMeta = HttpContext.Current.Session("goMeta")
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			//goDef = HttpContext.Current.Session("goDef")
			goPerm = (Selltis.BusinessLogic.clPerm)HttpContext.Current.Session["goPerm"];
		}

		public clPerm()
		{

		}

	}
}
