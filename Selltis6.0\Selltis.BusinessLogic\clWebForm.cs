﻿using System;
using System.Collections;
using System.Collections.Specialized;

namespace Selltis.BusinessLogic
{
	public class clWebForm
	{

		public string WorkgroupOptions = "";
		public string PageURL = "";
		public string HTML = "";
		//Public FieldValueList As New SortedList
		public NameValueCollection FieldValueList = new NameValueCollection();
		public string EmailAlias = "";
		public Microsoft.VisualBasic.Collection SelectHTMLStrings = new Microsoft.VisualBasic.Collection();
		public SortedList LinkList = new SortedList();

		public string _WFSUBMITTO = "";
		public string _REQDINCOMPLETE = "";
		public string _WFTYPE = "";
		public string _WFPURPOSE = "";

		public void AddSelectHTMLString(string sFile, string sHTML)
		{
			if (SelectHTMLStrings.ToString().Contains(sFile))
			{
				SelectHTMLStrings.Remove(sFile);
			}
			SelectHTMLStrings.Add(sHTML, sFile);
		}

		public string GetSelectHTMLString(string sFile)
		{
			if (SelectHTMLStrings.ToString().Contains(sFile))
			{
				return SelectHTMLStrings[sFile].ToString();
			}
			else
			{
				return "";
			}
		}

	}

}
