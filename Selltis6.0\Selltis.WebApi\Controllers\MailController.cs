﻿using Selltis.BusinessLogic;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web;
using System.Web.Http;
using Newtonsoft.Json;
using System.IO;
using System.Data.SqlClient;
using Newtonsoft.Json.Linq;
using System.Web.Http.Cors;
using Selltis.Core;
using System.Collections;

namespace Selltis.WebApi.Controllers
{
    [Authorize]
    [EnableCors(origins: "*", headers: "*", methods: "*")]
    public class MailController : ApiController
    {
        [HttpPost]
        public HttpResponseMessage LogMail([FromBody] Models.ParamUpdateModel parameters)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;
            if (Logon(parameters.userName, parameters.hostName) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }

            HttpContext.Current.Session["HostName"] = parameters.hostName;
            clInit _clint = new clInit();
            DataTable par_oDataSet = JsonConvert.DeserializeObject<DataTable>(parameters.par_sDataSet);

            clEmail clEmail = new clEmail();

            if (string.IsNullOrEmpty(parameters.strCompareField))
            {
                string result = clEmail.LogMessage(par_oDataSet);
                response = Request.CreateResponse(HttpStatusCode.OK, result);
            }
            else
            {
                DataTable par_attachments = JsonConvert.DeserializeObject<DataTable>(parameters.strCompareField);
                string result = clEmail.LogMessage_With_Attachments(par_oDataSet, par_attachments, parameters.hostName);
                response = Request.CreateResponse(HttpStatusCode.OK, result);
            }
            return response;

        }


        [HttpPost]
        public HttpResponseMessage ValidateUser([FromBody] Models.ParamModel parameters)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;
            if (Logon(parameters.userName, parameters.hostName, parameters.password) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }
            else
                return Request.CreateResponse(HttpStatusCode.OK);
        }


        [HttpPost]
        public HttpResponseMessage NewRS([FromBody] Models.ParamModel parameters)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;
            if (Logon(parameters.userName, parameters.hostName) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }
            HttpContext.Current.Session["HostName"] = parameters.hostName;
            clInit _clint = new clInit();
            if (parameters.par_sGenFieldsDefs == "NAV")
            {
                clRowSet rs = new clRowSet(parameters.sFile, parameters.iType, parameters.sCondition, parameters.sSort, parameters.sFields, parameters.iTop, "", "");
                rs.ToTable();

                rs.SetFieldVal("TXT_ExternalID", parameters.sINI, 2);
                rs.SetFieldVal("TXT_ExternalSource", parameters.par_sGenFieldsDefs, 2);
                rs.Commit();
                response = Request.CreateResponse(HttpStatusCode.OK, rs.dtTransTable);
            }
            else
            {
                parameters.iTop = 20000;
               
                clRowSet rs = new clRowSet(parameters.sFile, parameters.iType, parameters.sCondition, parameters.sSort, parameters.sFields, parameters.iTop, parameters.sINI, parameters.par_sGenFieldsDefs);
               
                DataTable dtData = new DataTable();
                if (rs.oDataSet?.Tables.Count > 0)
                {
                    dtData = rs.oDataSet?.Tables[0];
                    MergeNNColumns(rs, dtData);
                }

                TransformDataTable(ref dtData, parameters.sFile);

                UpdateColumnLabels(ref dtData, parameters.sFile);

                response = Request.CreateResponse(HttpStatusCode.OK, dtData);
            }

            return response;

        }

        private static void MergeNNColumns(clRowSet rs, DataTable dtData)
        {
            //this code is for merge multi link (NN) columns to the 1st data table
            if (rs.oDataSet.Tables.Count > 1)
            {
                DataTable dt1 = rs.oDataSet.Tables[1];
                ArrayList LNK_NNColumns_list = new ArrayList();
                foreach (DataRow _row in dt1.Rows)
                {
                    LNK_NNColumns_list.Add(_row["LinkName"]);
                    dtData.Columns.Add(_row["LinkName"].ToString(), typeof(string));
                }

                foreach (DataRow _row in dtData.Rows)
                {
                    int iTemp = 2;
                    string sTempval = "";
                    foreach (string _newcol in LNK_NNColumns_list)
                    {
                        foreach (DataRow _row2 in rs.oDataSet.Tables[iTemp].Rows)
                        {
                            if (_row2["BASE%%GID_ID"].ToString() == _row["GID_ID"].ToString())
                            {
                                if (string.IsNullOrEmpty(sTempval))
                                    sTempval = Convert.ToString(_row2[_newcol]);
                                else
                                    sTempval = sTempval + "," + Convert.ToString(_row2[_newcol]);

                            }
                        }

                        _row[_newcol] = sTempval;
                        sTempval = "";
                        iTemp = iTemp + 1;
                    }
                }


                //var query = from sourceRow in dt.AsEnumerable()
                //            from targetTableIndex in Enumerable.Range(2, LNK_Columns_list.Count)
                //            let targetTable = rs.oDataSet.Tables[targetTableIndex]
                //            from targetRow in targetTable.AsEnumerable()
                //            where targetRow.Field<Guid>("BASE%%GID_ID") == sourceRow.Field<Guid>("GID_ID")
                //            from targetColumn in LNK_Columns_list.ToArray()
                //            group targetRow.Field<string>(targetColumn.ToString() ) by sourceRow into g
                //            select new
                //            {
                //                SourceRow = g.Key,
                //                ConcatenatedValues = string.Join(",", g)
                //            };

                //foreach (var result in query)
                //{
                //    DataRow sourceRow = result.SourceRow;
                //    string concatenatedValues = result.ConcatenatedValues;

                //    foreach (string targetColumn in LNK_Columns_list)
                //    {
                //        sourceRow[targetColumn] = concatenatedValues;
                //    }
                //}

            }
        }

        private void UpdateColumnLabels(ref DataTable dt, string sFileName)
        {
            clData _godata = (clData)Util.GetInstance("data");
            int iValid = 0;
            foreach (DataColumn _col in dt.Columns)
            {
                _col.ColumnName = _godata.GetFieldFullLabelFromName(sFileName, _col.ColumnName, ref iValid);
            }
        }

        private void TransformDataTable(ref DataTable dt, string sFileName)
        {
            try
            {
                DataTable dt1 = dt.Copy();
                ArrayList _col_remove_list = new ArrayList();
                foreach (DataColumn _col in dt.Columns)
                {
                    if (_col.ColumnName.StartsWith("MLS_") || _col.ColumnName.Contains("%%MLS_") || _col.ColumnName.StartsWith("DTT_") || _col.ColumnName.StartsWith("CUR_") || _col.ColumnName.StartsWith("CHK_"))
                    {
                        string sActualColName = _col.ColumnName;
                        int iOrdinal = _col.Ordinal;
                        dt1.Columns[_col.ColumnName].ColumnName = _col.ColumnName + "_|tmp";
                        dt1.Columns.Add(sActualColName, typeof(string));
                        dt1.Columns[sActualColName].SetOrdinal(iOrdinal);
                        _col_remove_list.Add(_col.ColumnName + "_|tmp");
                    }
                    else if (_col.ColumnName == "BI__ID")
                    {
                        _col_remove_list.Add(_col.ColumnName);
                    }


                }
                dt = null;


                foreach (DataColumn _col in dt1.Columns)
                {
                    if (_col.ColumnName.Contains("MLS_") && _col.ColumnName.EndsWith("_|tmp"))
                    {
                        TransformMLSValues(ref dt1, _col.ColumnName, sFileName);
                    }
                    else if (_col.ColumnName.StartsWith("DTT_") && _col.ColumnName.EndsWith("_|tmp"))
                    {
                        //IEnumerable<DataRow> rows = dt1.Rows.Cast<DataRow>().Where(r => r[_col.ColumnName].ToString() == "02-01-1753 23:59:59" 
                        //                                                                || r[_col.ColumnName].ToString() == "1/2/1753 11:59:59 PM");

                        //rows.ToList().ForEach(r => r.SetField(_col.ColumnName.Replace("_|tmp", ""), ""));

                        //IEnumerable<DataRow> rows1 = dt1.Rows.Cast<DataRow>().Where(r => r[_col.ColumnName].ToString() != "02-01-1753 23:59:59");

                        //rows1.ToList().ForEach(r => r.SetField(_col.ColumnName.Replace("_|tmp", ""), r[_col.ColumnName]));

                        foreach (DataRow _row in dt1.Rows)
                        {
                            string sval = Convert.ToString(_row[_col.ColumnName]);
                            if(sval == "1/2/1753 11:59:59 PM" || sval == "02-01-1753 23:59:59" || sval.Contains("1753"))
                            {
                                _row[_col.ColumnName.Replace("_|tmp", "")] = "";
                            }
                            else
                            {
                                _row[_col.ColumnName.Replace("_|tmp", "")] = sval;
                            }
                        }

                    }
                    else if (_col.ColumnName.StartsWith("CUR_") && _col.ColumnName.EndsWith("_|tmp"))
                    {


                        IEnumerable<DataRow> rows1 = dt1.Rows.Cast<DataRow>();

                        rows1.ToList().ForEach(r => r.SetField(_col.ColumnName.Replace("_|tmp", ""), String.Format("{0:C}", r[_col.ColumnName])));

                    }
                    else if (_col.ColumnName.StartsWith("CHK_") && _col.ColumnName.EndsWith("_|tmp"))
                    {

                        IEnumerable<DataRow> rows = dt1.Rows.Cast<DataRow>().Where(r => r[_col.ColumnName].ToString() == "1");

                        rows.ToList().ForEach(r => r.SetField(_col.ColumnName.Replace("_|tmp", ""), "Yes"));

                        IEnumerable<DataRow> rows1 = dt1.Rows.Cast<DataRow>().Where(r => r[_col.ColumnName].ToString() == "0");

                        rows1.ToList().ForEach(r => r.SetField(_col.ColumnName.Replace("_|tmp", ""), "No"));

                    }
                }
                

                //dt1.AcceptChanges();
                foreach (string _colname in _col_remove_list)
                {
                    dt1.Columns.Remove(_colname);
                }

                //dt1.AcceptChanges();
                dt = dt1.Copy();
            }
            catch (Exception ex)
            {

            }

        }
        public void TransformMLSValues(ref DataTable dt1, string ColName, string FileName)
        {

            DataView dvMlsdata = new DataView(dt1, "", "", DataViewRowState.CurrentRows);
            DataTable distinctValues = dvMlsdata.ToTable(true, ColName);
            string sMLSName = "";

            if (ColName.Contains("%%MLS_"))
            {
                FileName = ColName.Substring(ColName.IndexOf("%%") - 2, 2);
                sMLSName = ColName.Substring(ColName.IndexOf("%%") + 6).Replace("_|tmp", "");
            }
            else
            {
                sMLSName = ColName.Replace("MLS_", "").Replace("_|tmp", "");
            }

            if (distinctValues != null && distinctValues.Rows.Count > 0)
            {
                foreach (DataRow _row in distinctValues.Rows)
                {
                    string sMLSValue = Convert.ToString(_row[ColName]);
                    int itemp = 0;
                    if (Int32.TryParse(sMLSValue, out itemp))
                    {
                        clList _clList = new clList();

                        string sMLSText = _clList.LReadSeek(FileName + ":" + sMLSName, "KEY", sMLSValue);

                        IEnumerable<DataRow> rows = dt1.Rows.Cast<DataRow>().Where(r => r[ColName].ToString() == sMLSValue);

                        rows.ToList().ForEach(r => r.SetField(ColName.Replace("_|tmp", ""), sMLSText));
                    }
                }
            }

        }

        private bool Logon(string userName, string hostName, string pwd = "")
        {
            string sHostName = (hostName == "localhost" || string.IsNullOrEmpty(hostName)) ? "default" : hostName;
            if (string.IsNullOrEmpty(userName))
                userName = "system";
            //Load Settings
            DataSet ds = new DataSet();
            string myXMLfile = System.Configuration.ConfigurationManager.AppSettings["CustomFilesPath"].ToString() + sHostName + "/SiteSettings.xml";
            FileStream fsReadXml = new FileStream(myXMLfile, FileMode.Open, FileAccess.Read);
            ds.ReadXml(fsReadXml);
            fsReadXml.Close();
            fsReadXml.Dispose();
            HttpContext.Current.Session[hostName + "_SiteSettings"] = ds.Tables[0];

            //Login Process 
            string connStr = ds.Tables[0].Rows[0]["ConnectionString"].ToString();
            HttpContext.Current.Session[sHostName + "_" + "ConnString"] = connStr;
            SqlConnection sqlConnection = new SqlConnection(connStr);
            if (sqlConnection.State == ConnectionState.Closed)
                sqlConnection.Open();
            string sql = string.Empty;

            string IsSSoEnabled = "false";
            try
            {
                if (ds.Tables[0].Columns.Contains("UseSSO"))
                {
                    IsSSoEnabled = ds.Tables[0].Rows[0]["UseSSO"].ToString();
                }
                else
                {
                    IsSSoEnabled = "false";
                }               
            }
            catch
            {
            }           

            if (string.IsNullOrEmpty(pwd))
            {
                if (IsSSoEnabled == "true")
                {
                    sql = "Select b.* from US a Join XU b on b.GID_UserID = a.GID_ID WHERE a.CHK_ActiveField=1 AND cast(a.EML_Email as nvarchar(250))='" + userName + "'";
                }
                else if (IsSSoEnabled == "false")
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "'";
                }
                else
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='" + userName + "'";
                }
                    
            }               
            else
            {
                if(IsSSoEnabled=="true")
                {
                    sql = "Select b.* from US a Join XU b on b.GID_UserID = a.GID_ID WHERE a.CHK_ActiveField=1 AND cast(a.EML_Email as nvarchar(250))='"+ pwd + "'";
                }
                else if (IsSSoEnabled == "false")
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='"+ userName + "'  and [TXT_Password]='" + pwd + "'";
                }
                else
                {
                    sql = "Select * From [XU] WHERE [TXT_logonname]='"+ userName + "' and [TXT_Password]='" + pwd + "'";
                }
            }

            SqlCommand comm = new SqlCommand(sql, sqlConnection);
            //comm.Parameters.AddWithValue("@username", userName);
            //comm.Parameters.AddWithValue("@pwd", pwd);

            SqlDataReader reader = comm.ExecuteReader();
            if (reader.HasRows)
            {
                DataTable dt = new DataTable();
                dt.Load(reader);
                if (dt.Rows.Count == 0)
                {
                    return false;
                }
                else
                {

                    HttpContext.Current.Session["USERID"] = dt.Rows[0]["GID_UserID"].ToString();
                    HttpContext.Current.Session["LOGINID"] = dt.Rows[0]["GID_ID"].ToString();
                    HttpContext.Current.Session[sHostName + "_" + "LOGINNAME"] = dt.Rows[0]["TXT_LogonName"].ToString();
                    return true;
                }
            }
            else
                return false;


        }

        private const string Par_sVarName = "UpdateKey";

        [HttpPost]
        public HttpResponseMessage UpsertRecordsByDataset([FromBody] Models.ParamUpdateModel param)
        {
            HttpContext.Current.Session.Clear();
            clCache.ClearCache();
            HttpResponseMessage response;
            if (Logon(param.userName, param.hostName) == false)
            {
                response = Request.CreateResponse(HttpStatusCode.Unauthorized, "");
                return response;
            }
            HttpContext.Current.Session["HostName"] = param.hostName;
            clInit _clint = new clInit();


            clProject goP = HttpContext.Current.Session["goP"] as clProject;
            clTransform goTr = HttpContext.Current.Session["goTr"] as clTransform;

            if (!string.IsNullOrEmpty(param.sInstructions))
            {

                int i = Convert.ToInt32(goTr.StrRead(param.sInstructions, "TranslateIdCount", "0"));
                List<string> list = new List<string>();

                for (int k1 = 1; k1 < i; k1++)
                {
                    list.Add(goTr.StrRead(param.sInstructions, "TranslateField" + k1, " "));
                }
                string[] strFields = list.ToArray();
                goP.SetVar("TranslateFields", strFields);
            }
            goP.SetVar("ExternalSource", param.strExtSource);

            DataSet par_oDataSet = JsonConvert.DeserializeObject<DataSet>(param.par_sDataSet);



            goP.SetVar("bBypassValidation", true);
            goP.SetVar("bNoRecordOnSave", false);


            DataSet dsReturn = new DataSet();


            clError goErr = HttpContext.Current.Session["goErr"] as clError;
            clData goData = HttpContext.Current.Session["goData"] as clData;
            clScrMngRowSet goScr = new clScrMngRowSet(); // As clScrMng = HttpContext.Current.Session("goScr")
            goScr.Initialize();

            clLog goLog = HttpContext.Current.Session["goLog"] as clLog;
            int j;
            int k;
            string sCurrentTableName = "";
            clRowSet oURS = null;
            string sCurrentGID = "";


            DataTable dt2 = new DataTable();
            DataColumn dc2;
            DataRow dr2;
            bool bEditError = false;
            string sLogLine = "";
            string sTransType = "";
            DataTable dtStore;
            bool bAdd = false;
            sCurrentTableName = par_oDataSet.Tables[0].TableName;


            dc2 = new DataColumn("Adds", typeof(string));
            dt2.Columns.Add(dc2);
            dc2 = new DataColumn("Edits", typeof(string));
            dt2.Columns.Add(dc2);
            dc2 = new DataColumn("Errors", typeof(string));
            dt2.Columns.Add(dc2);

            int iAdds = 0;
            int iEdits = 0;
            int iErrors = 0;

            // *****************************************
            object oStore;
            StringBuilder sb = new StringBuilder();
            DataTable dtCount;
            string strInternalId = "";
            // ****************************************
            // New UpdateKey feature 
            bool bCommit = true;
            // ****************************************

            dr2 = dt2.NewRow();
            for (j = 0; j <= par_oDataSet.Tables[0].Rows.Count - 1; j++)
            {
                // ****************************************
                // New UpdateKey feature 
                bCommit = true;
                // ****************************************
                dtCount = goData.GetTNID(param.strExtSource, sCurrentTableName, "", par_oDataSet.Tables[0].Rows[j][param.strCompareField].ToString());
                // -- PJ: Old way - only checking first TN record
                // If dtCount.Rows.Count > 0 Then
                // strInternalId = dtCount.Rows(0).Item(0).ToString
                // End If

                // PJ 8/4/10: New way - checking multiple TNs for valid record in file
                if (dtCount.Rows.Count > 0)
                {
                    foreach (DataRow dr in dtCount.Rows)
                    {
                        strInternalId = dr[0].ToString();
                        sb = new StringBuilder();
                        sb.Append("GID_ID");
                        sb.Append("='");
                        sb.Append(strInternalId);
                        sb.Append("'");
                        oURS = new clRowSet(sCurrentTableName, clC.SELL_EDIT, sb.ToString());
                        if (oURS.Count() == 0)
                            // delete TN record because no matching record
                            // currently no method for this!!
                            strInternalId = "";
                        else
                            // This internal id is valid
                            break;
                    }
                }


                bEditError = false;
                bAdd = false;
                if (strInternalId == "")
                    bAdd = true;
                strInternalId = "";


                if (bAdd == false)
                {

                    // Added for LimitTo system
                    if (goP.GetVar("LimitTo").ToString().ToUpper() == "ADDS")
                        goto MoveOn;
                    // ****************************************
                    // New UpdateKey feature                    
                    if (goP.GetVar(Par_sVarName).ToString() != "")
                        bCommit = false;
                    // ****************************************
                    // Set field values for record being edited                        
                    for (k = 0; k <= par_oDataSet.Tables[0].Columns.Count - 1; k++)
                    {
                        if (par_oDataSet.Tables[0].Columns[k].ColumnName != "EXT_ID")
                        {
                            if ((par_oDataSet.Tables[0].Rows[j][k]) != System.DBNull.Value)
                            {

                                // ****************************************
                                // New UpdateKey feature
                                // Get value of current field
                                oStore = par_oDataSet.Tables[0].Rows[j][k];
                                // If current column name = key column then
                                string sColumnName = par_oDataSet.Tables[0].Columns[k].ColumnName.ToUpper();
                                string sKeyColumn = goP.GetVar("UpdateKey").ToString().ToUpper();
                                string sSellKeyValue = "";
                                if (sColumnName == sKeyColumn)
                                {
                                    // If value of current field = value of key column in rowset
                                    if (oURS != null)
                                        sSellKeyValue = oURS.GetFieldVal(par_oDataSet.Tables[0].Columns[k].ColumnName, clC.SELL_FRIENDLY).ToString();
                                    if (oStore.ToString() == sSellKeyValue)
                                        bCommit = true;
                                }
                                // ****************************************

                                oStore = par_oDataSet.Tables[0].Rows[j][k];
                                if (oStore.ToString() == "null;" | oStore.ToString() == "null")
                                    oStore = "";

                                if (testTranslate(par_oDataSet.Tables[0].Columns[k].ColumnName))
                                {
                                    int colnameLength = par_oDataSet.Tables[0].Columns[k].ColumnName.Length;
                                    dtStore = goData.GetTNID(goP.GetVar("ExternalSource").ToString(), par_oDataSet.Tables[0].Columns[k].ColumnName.Substring(colnameLength - 2, 2), "", oStore.ToString());
                                    if (dtStore.Rows.Count > 0)
                                        oStore = dtStore.Rows[0][0].ToString();
                                    else
                                        oStore = "";
                                }

                                if (oStore.ToString() != "")
                                {
                                    if (oURS.SetFieldVal(par_oDataSet.Tables[0].Columns[k].ColumnName, oStore, clC.SELL_FRIENDLY) == 0)
                                    {
                                        if (par_oDataSet.Tables[0].Columns[k].ColumnName.Substring(0, 3) != "LNK")
                                        {
                                            // log error
                                            sLogLine = "Field: " + par_oDataSet.Tables[0].Columns[k].ColumnName + "  Value: " + par_oDataSet.Tables[0].Rows[j][k];
                                            goLog.Log("ws_RowSet::UpsertRecordsByDataset", sLogLine, 1, false/* Conversion error: Set to default value for this argument */, true);

                                            // dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                                            // dr("STATUS") = "Error"
                                            // dr("DETAILS") = "Error on SetFieldVal: " & goErr.GetLastError("MESSAGE")

                                            bEditError = true;
                                            iErrors = iErrors + 1;
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        sTransType = "Edited";
                    }
                }
                else
                {
                    // Added for LimitTo system
                    if (goP.GetVar("LimitTo").ToString().ToUpper() == "EDITS")
                        goto MoveOn;

                    oURS = new clRowSet(sCurrentTableName, clC.SELL_ADD, sb.ToString());
                    // Set field values for record being added      
                    if (par_oDataSet.Tables[0].Rows[j][param.strCompareField].ToString() != "" | par_oDataSet.Tables[0].Rows[j][param.strCompareField] != System.DBNull.Value)
                        oURS.SetFieldVal("TXT_ExternalID", par_oDataSet.Tables[0].Rows[j][param.strCompareField], clC.SELL_FRIENDLY);

                    if (param.strExtSource != "")
                        oURS.SetFieldVal("TXT_ExternalSource", param.strExtSource, clC.SELL_FRIENDLY);

                    for (k = 0; k <= par_oDataSet.Tables[0].Columns.Count - 1; k++)
                    {
                        if (par_oDataSet.Tables[0].Columns[k].ColumnName != "EXT_ID")
                        {
                            if (par_oDataSet.Tables[0].Rows[j][k] != System.DBNull.Value)
                            {
                                oStore = par_oDataSet.Tables[0].Rows[j][k];
                                if (oStore.ToString() == "null;" | oStore.ToString() == "null")
                                    oStore = "";

                                if (testTranslate(par_oDataSet.Tables[0].Columns[k].ColumnName))
                                {
                                    int colnameLength = par_oDataSet.Tables[0].Columns[k].ColumnName.Length;
                                    dtStore = goData.GetTNID(goP.GetVar("ExternalSource").ToString(), par_oDataSet.Tables[0].Columns[k].ColumnName.Substring(colnameLength - 2, 2), "", oStore.ToString());
                                    if (dtStore.Rows.Count > 0)
                                        oStore = dtStore.Rows[0][0].ToString();
                                    else
                                        oStore = "";
                                }

                                if (oStore.ToString() != "")
                                {
                                    if (oURS.SetFieldVal(par_oDataSet.Tables[0].Columns[k].ColumnName, oStore, clC.SELL_FRIENDLY) == 0)
                                    {
                                        if (par_oDataSet.Tables[0].Columns[k].ColumnName.Substring(0, 3) != "LNK")
                                        {
                                            // log error
                                            sLogLine = "Field: " + par_oDataSet.Tables[0].Columns[k].ColumnName + "  Value: " + par_oDataSet.Tables[0].Rows[j][k];
                                            goLog.Log("ws_RowSet::UpsertRecordsByDataset", sLogLine, 1, false/* Conversion error: Set to default value for this argument */, true);
                                            iErrors = iErrors + 1;
                                            break;
                                        }
                                    }
                                }
                            }
                        }

                        sTransType = "Added";
                    }
                }

                if (oURS.Count() == 1)
                {
                    // Commit record    
                    sCurrentGID = oURS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY).ToString();
                    if (bEditError == false)
                    {
                        // oERS.bBypassValidation = True
                        SetRowsetBypass(oURS);

                        // ****************************************
                        // New UpdateKey feature 
                        if (bCommit == true)
                        {
                            // ****************************************
                            if (oURS.Commit() == 0)
                            {
                                // log error
                                // dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                                // dr("STATUS") = "Error"
                                // dr("DETAILS") = "Error on Commit: " & goErr.GetLastError("MESSAGE")
                                bEditError = true;
                                iErrors = iErrors + 1;
                            }
                            else
                                // log edit
                                // dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                                // dr("STATUS") = sTransType
                                // dr("GID_ID") = sCurrentGID

                                if (sTransType == "Edited")
                                iEdits = iEdits + 1;
                            else if (sTransType == "Added")
                                iAdds = iAdds + 1;
                        }
                    }
                }
                oURS = null/* TODO Change to default(_) if this is not a reference type */;
            MoveOn:
                ;
            }
            dr2["Adds"] = iAdds.ToString();
            dr2["Edits"] = iEdits.ToString();
            dr2["Errors"] = iErrors.ToString();
            dt2.Rows.Add(dr2);


            // dsReturn.Tables.Add(dt)
            dsReturn.Tables.Add(dt2);

            // Catch ex As Exception

            // If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            // goErr.SetError(ex, 45105, "ws_RowSet::UpsertRecordsByDataset")
            // End If

            // End Try
            response = Request.CreateResponse(HttpStatusCode.OK, dsReturn);
            return response;

        }


        private string GetFields(string sFileName)
        {
            string sFields = string.Empty;
            if (sFileName == "QT")
            {
                sFields = "LNK_CREDITEDTO_US%%TXT_FULLNAME,DTT_CreationTime,DTT_MODTIME,DTT_TIME,DTT_EXPCLOSEDATE,MLS_STATUS,TXT_OPPORTUNITYTYPE,CUR_TOTALAMOUNTUSD,LNK_FOR_CO%%SYS_NAME,TXT_DESCRIPTION,TXT_QUOTENO,TXT_LinkedOppNo,CHK_PRIMARYQUOTE,MLS_LOB,GID_ID";
            }
            else if (sFileName == "OP")
            {
                sFields = "TXT_OPPORTUNITYNAME,TXT_OPPNO,MLS_OPPORTUNITYTYPE,MLS_REGION,MLS_LOB,LNK_CREDITEDTO_US%%TXT_FULLNAME,LNK_FOR_CO%%TXT_COMPANYNAME,LNK_ORIGINATEDBY_CN%%TXT_FULLNAME,LNK_ORIGINATEDBY_CN%%TXT_CONTACTCODE,MLS_Stage,MLS_Currency,CUR_AMOUNT,CUR_RENTALAMOUNT,CUR_TOTALAMOUNT,CUR_TOTALAMOUNTUSD,DTT_ExpCloseDate,DTT_CREATIONTIME,DTT_MODTIME,MLS_TYPEOFBID,LNK_LINKED_WL%%TXT_WLNO,GID_ID";
            }


            return sFields;
        }

        private bool testTranslate(string strField)
        {
            string[] strTest;
            clProject goP = HttpContext.Current.Session["goP"] as clProject;
            try
            {
                strTest = (string[])goP.GetVar("TranslateFields");
            }
            catch (Exception ex)
            {
                return false;
            }

            int i = 0;

            for (i = 0; i < strTest.Count(); i++)
            {
                if (strTest[i].ToUpper() == strField.ToUpper())
                    return true;
            }

            return false;
        }

        private void SetRowsetBypass(clRowSet oRS)
        {
            // Dim goP As clProject = HttpContext.Current.Session("goP")
            clProject goP = HttpContext.Current.Session["goP"] as clProject;

            bool oVar1 = System.Convert.ToBoolean(goP.GetVar("bBypassValidation"));
            switch (oVar1)
            {
                case true:
                    {
                        oRS.bBypassValidation = true;
                        break;
                    }

                case false:
                    {
                        oRS.bBypassValidation = false;
                        break;
                    }
            }

            bool oVar2 = System.Convert.ToBoolean(goP.GetVar("bNoRecordOnSave"));
            switch (oVar2)
            {
                case true:
                    {
                        oRS.bNoRecordOnSave = true;
                        break;
                    }

                case false:
                    {
                        oRS.bNoRecordOnSave = false;
                        break;
                    }
            }
        }
    }
}
