﻿CREATE TABLE [dbo].[NM] (
    [GID_ID]             UNIQUEIDENTIFIER CONSTRAINT [DF_NM_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'NM',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]             BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]           NVARCHAR (80)    NULL,
    [DTT_CreationTime]   DATETIME         CONSTRAINT [DF_NM_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]       TINYINT          NULL,
    [TXT_ModBy]          VARCHAR (4)      NULL,
    [DTT_ModTime]        DATETIME         CONSTRAINT [DF_NM_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_NinjaModelName] NVARCHAR (50)    NULL,
    [MMO_ImportData]     NTEXT            NULL,
    [SI__ShareState]     TINYINT          CONSTRAINT [DF_NM_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]   UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]       VARCHAR (50)     NULL,
    [TXT_ExternalID]     NVARCHAR (80)    NULL,
    [TXT_ExternalSource] VARCHAR (10)     NULL,
    [TXT_ImpJobID]       VARCHAR (20)     NULL,
    [TXT_MODELID]        NVARCHAR (20)    NULL,
    [TXT_MODELNAME]      NVARCHAR (50)    NULL,
    [GID_RELATED_NP]     UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_NM] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_NM_NinjaModelName]
    ON [dbo].[NM]([TXT_NinjaModelName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NM_CreatedBy_US]
    ON [dbo].[NM]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NM_ModDateTime]
    ON [dbo].[NM]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NM_Name]
    ON [dbo].[NM]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NM_CreationTime]
    ON [dbo].[NM]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_NM_BI__ID]
    ON [dbo].[NM]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NM_TXT_ImportID]
    ON [dbo].[NM]([TXT_ImportID] ASC);

