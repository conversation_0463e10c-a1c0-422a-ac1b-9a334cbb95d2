﻿CREATE TABLE [dbo].[PR_Related_IU] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Project_Related_Industry_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PR] UNIQUEIDENTIFIER NOT NULL,
    [GID_IU] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PR_Related_IU] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_IU_Connected_PR] FOREIGN KEY ([GID_PR]) REFERENCES [dbo].[PR] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PR_Related_IU] FOREIGN KEY ([GID_IU]) REFERENCES [dbo].[IU] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PR_Related_IU] NOCHECK CONSTRAINT [LNK_IU_Connected_PR];


GO
ALTER TABLE [dbo].[PR_Related_IU] NOCHECK CONSTRAINT [LNK_PR_Related_IU];


GO
CREATE CLUSTERED INDEX [IX_IU_Connected_PR]
    ON [dbo].[PR_Related_IU]([GID_PR] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PR_Related_IU]
    ON [dbo].[PR_Related_IU]([GID_IU] ASC);

