﻿using System;
using System.Web.Caching;
using System.IO;
using System.Web;
using System.Data;

namespace Selltis.BusinessLogic
{
	public sealed class clCache
	{

		public static void AddtoCache(string Key, object Data)
		{
			//Try
			Key = PrepareKey(Key);
				object cacheddata;
				cacheddata = GetItemFromCache(Key);
				if (cacheddata == null)
				{
					HttpRuntime.Cache.Insert(Key, Data);
					//WriteLog("Insert into cache with key =" & Key & " & value = " & Data)
				}
			//Catch ex As Exception

			//End Try
		}

		public static object GetItemFromCache(string Key)
		{
			//Try
			Key = PrepareKey(Key);
				object cacheddata;
				cacheddata = HttpRuntime.Cache[Key]; //CStr(HttpRuntime.Cache(Key))
				if (cacheddata != null)
				{
					//WriteLog("read data from cache with key =" & Key)
					return cacheddata;
				}
				else
				{
					return null;
				}
			//Catch ex As Exception
			//    Return Nothing
			//End Try
		}

		public static void RemoveItemFromCache(string Key)
		{
			//Try
			Key = PrepareKey(Key);
				object cacheddata;
				cacheddata = GetItemFromCache(Key);
				if (cacheddata != null)
				{
					HttpRuntime.Cache.Remove(Key);
				}
			//Catch ex As Exception

			//End Try
		}

		public static string MakeCacheKey(string section, string page)
		{

			//'Dim sHostingEnvironment As String = System.Configuration.ConfigurationManager.AppSettings("HostingEnvironment").ToString().ToLower()
			//'Dim sHostName As String = ""
			//If sHostingEnvironment = "debugging" Then
			//    sHostName = "default"
			//ElseIf sHostingEnvironment = "staging" Then
			//    sHostName = HttpContext.Current.Request.Url.Host.ToString().ToLower() + "_" + HttpContext.Current.Request.Url.Port.ToString()
			//Else
			//    sHostName = HttpContext.Current.Request.Url.Host.ToString().ToLower()
			//End If
			string sHostName = GetHostName();
			return sHostName + "|" + section + "|" + page;

			//'Return section & "|" & page
		}

		public static bool IsCachable(string section, string page)
		{
			//WriteLog(section & "|" & page)

			bool UseCache = false;
			string sHostName = clSettings.GetHostName();
			//Boolean.TryParse(System.Configuration.ConfigurationManager.AppSettings("UseCache"), UseCache)
			if (!(((DataTable)HttpContext.Current.Session[sHostName + "_SiteSettings"]).Rows[0]["UseCache"] == null))
			{
				bool.TryParse(Convert.ToString(((DataTable)HttpContext.Current.Session[sHostName + "_SiteSettings"]).Rows[0]["UseCache"]), out UseCache);
			}
			else
			{
				UseCache = false;
			}

			if (UseCache == false)
			{
				return false;
			}

			string pageType;
			pageType = page.Substring(0, 3).ToLower();

			if (pageType == "alt" || pageType == "pta" || pageType == "snd" || pageType == "snq" || pageType == "age" || pageType == "oth")
			{
				return false;
			}

			if (section == "GLOBAL" | string.IsNullOrEmpty(section))
			{
				return true;
			}
			else
			{
				return false;
			}

		}

		public static bool IsCachable(string sSQLString, string TableName = "", string Var1 = "")
		{
			//WriteLog(section & "|" & page)
			//Var1 -- is a dummy parameter to make the signature different from the other overloaded function
			bool UseCache = false;
			//Boolean.TryParse(System.Configuration.ConfigurationManager.AppSettings("UseCache"), UseCache)
			if (!(((DataTable)HttpContext.Current.Session[clSettings.GetHostName() + "_SiteSettings"]).Rows[0]["UseCache"] == null))
			{
				bool.TryParse(Convert.ToString(((DataTable)HttpContext.Current.Session[clSettings.GetHostName() + "_SiteSettings"]).Rows[0]["UseCache"]), out UseCache);
			}
			else
			{
				UseCache = false;
			}



			if (UseCache == false)
			{
				return false;
			}

			if (sSQLString.ToLower().Contains("select") && (sSQLString.ToLower().Contains("from [xm]") | sSQLString.ToLower().Contains("from [xf]")) && (string.IsNullOrEmpty(TableName) == false && (TableName.ToLower() == "xm" || TableName.ToLower() == "xf")))
			{
				return true;
			}
			else if (sSQLString.ToLower().Contains("pgetcurrentmdversion"))
			{
				return true;
			}
			else if (sSQLString.ToLower().Contains("pgettables"))
			{
				return true;
			}
			else if (sSQLString.ToLower().Contains("pgetfields"))
			{
				return true;
			}
			else if (sSQLString.ToLower().Contains("pgetlinks"))
			{
				return true;
			}
			else if (sSQLString.ToLower().Contains("pgetlists"))
			{
				return true;
			}
			else if (sSQLString.ToLower().Contains("pgetactivemenuautomators"))
			{
				return true;
			}
			else if (sSQLString.ToLower().Contains("pgetactivebtnautomators"))
			{
				return true;
			}
			else
			{
				return false;
			}

		}

		public static void ClearCache()
		{
			//Try
			string sHostName = GetHostName();
				var cacheitems = (System.Collections.IDictionaryEnumerator)HttpRuntime.Cache.GetEnumerator();

				while (cacheitems.MoveNext())
				{
					if (cacheitems.Key.ToString().ToLower().Contains(sHostName.ToLower()))
					{
						HttpContext.Current.Cache.Remove(Convert.ToString(cacheitems.Key));
					}
				}
			//' WriteLog("Clears the cache")
			//Catch ex As Exception

			//End Try
		}

		public static void WriteLog(string data)
		{
			//Return
			//Try
			string sHostName = GetHostName();

				string sLogFilePath = System.Configuration.ConfigurationManager.AppSettings["LogFilePath"].ToString().ToLower();

				if (!System.IO.Directory.Exists(sLogFilePath + "LogFiles"))
				{
					System.IO.Directory.CreateDirectory(sLogFilePath + "LogFiles");
				}

				if (!System.IO.Directory.Exists(sLogFilePath + "LogFiles\\" + sHostName))
				{
					System.IO.Directory.CreateDirectory(sLogFilePath + "LogFiles\\" + sHostName);
				}

				string _DestinationPath = sLogFilePath + "LogFiles\\" + sHostName + "\\CacheLog_" + DateTime.Now.ToString("dd-MM-yyyy") + ".txt";

				if (File.Exists(_DestinationPath))
				{
					using (StreamWriter sw = File.AppendText(_DestinationPath))
					{
						sw.WriteLine("\r\n" + DateTime.Now + " :" + "\r\n" + data);
						sw.Flush();
						sw.Close();
					}
				}
				else
				{
					using (StreamWriter sw = File.CreateText(_DestinationPath))
					{
						sw.WriteLine("\r\n" + DateTime.Now + " :" + "\r\n" + data);
						sw.Flush();
						sw.Close();
					}
				}
			//Catch ex As Exception

			//End Try
		}

		private static string GetHostName()
		{
			string sHostingEnvironment = System.Configuration.ConfigurationManager.AppSettings["HostingEnvironment"].ToString().ToLower();
			string sHostName = "";
			if (sHostingEnvironment == "debugging")
			{
				sHostName = "default";
			}
			else if (sHostingEnvironment == "staging")
			{
				sHostName = HttpContext.Current.Request.Url.Host.ToString().ToLower() + "_" + HttpContext.Current.Request.Url.Port.ToString();
			}
			else
			{
				sHostName = HttpContext.Current.Request.Url.Host.ToString().ToLower();
			}
			return sHostName;
		}

		private static string PrepareKey(string key)
		{
			return GetHostName() + "_" + key;
		}

	}

}
