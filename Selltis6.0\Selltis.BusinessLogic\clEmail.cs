﻿using System;
using Quiksoft.EasyMail.SMTP;
using System.Net.Mail;
using System.Web;
using System.IO;
using System.Text.RegularExpressions;
using System.Drawing;
using System.Data;
using System.Collections.Generic;
using System.Collections;

//OWNER: RH

namespace Selltis.BusinessLogic
{
	public class clEmail
	{


		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clLog goLog;
		private clScrMngRowSet goScr;
		private string sSourceID;



		public clEmail()
		{

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			goScr = (Selltis.BusinessLogic.clScrMngRowSet)HttpContext.Current.Session["goScr"];

		}

		public string LogMessage(DataTable dt)
		{
				string tempLogMessage = null;

			string sProc = "clEmail::LogMessage";

			tempLogMessage = "";

			//  Try

			string sComp = null;
				string sCont = null;
				string sLink = null;
				string[] sAdd = null;
				int l = 0;
				string sComps = "";
				string sConts = "";
				string sMemo = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "", true);


				int iShare = 0;


				clRowSet objAddRowSet = new clRowSet("AC", 2);
				objAddRowSet.bBypassValidation = true;

				objAddRowSet.SetFieldVal("MMO_HISTORY", PrepHistory() + " " + goP.GetUserCode() + " E-mail Logged" + "\r\n" + "To: " + GetVal(dt, "To") + "\r\n" + "cc: " + GetVal(dt, "CC") + "\r\n" + "From: " + GetVal(dt, "From"));

				objAddRowSet.SetFieldVal("CHK_EXTDEVREVIEW", 1, clC.SELL_SYSTEM);

				objAddRowSet.SetFieldVal("LNK_From_SO", GetSourceVal());
				objAddRowSet.SetFieldVal("DTE_STARTTIME", GetVal(dt, "Date"));
				objAddRowSet.SetFieldVal("DTE_ENDTIME", GetVal(dt, "Date"));
				objAddRowSet.SetFieldVal("TME_STARTTIME", GetVal(dt, "Time"));
				objAddRowSet.SetFieldVal("TME_ENDTIME", GetVal(dt, "Time"));
				objAddRowSet.SetFieldVal("LNK_CreatedBy_US", goP.gsUserID);

				if (dt.Columns.Contains("LinkRelatedField1") & dt.Columns.Contains("LinkRelatedValue1"))
				{
					string sColumn = GetVal(dt, "LinkRelatedField1");
					string sValue = GetVal(dt, "LinkRelatedValue1");

					if (string.IsNullOrEmpty(sColumn) == false && string.IsNullOrEmpty(sValue) == false)
					{
						objAddRowSet.SetFieldVal(sColumn, sValue);
						//VS 06222016
						AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"));
					}
				}

				if (dt.Columns.Contains("LinkRelatedField2") & dt.Columns.Contains("LinkRelatedValue2"))
				{
					string sColumn = GetVal(dt, "LinkRelatedField2");
					string sValue = GetVal(dt, "LinkRelatedValue2");

					if (string.IsNullOrEmpty(sColumn) == false && string.IsNullOrEmpty(sValue) == false)
					{
						objAddRowSet.SetFieldVal(sColumn, sValue);
						//VS 06222016
						AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"));
					}
				}

				if (dt.Columns.Contains("LinkRelatedField3") & dt.Columns.Contains("LinkRelatedValue3"))
				{
					string sColumn = GetVal(dt, "LinkRelatedField3");
					string sValue = GetVal(dt, "LinkRelatedValue3");

					if (string.IsNullOrEmpty(sColumn) == false && string.IsNullOrEmpty(sValue) == false)
					{
						objAddRowSet.SetFieldVal(sColumn, sValue);
						//VS 06222016
						AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"));
					}
				}

				if (dt.Columns.Contains("LinkRelatedField4") & dt.Columns.Contains("LinkRelatedValue4"))
				{
					string sColumn = GetVal(dt, "LinkRelatedField4");
					string sValue = GetVal(dt, "LinkRelatedValue4");

					if (string.IsNullOrEmpty(sColumn) == false && string.IsNullOrEmpty(sValue) == false)
					{
						objAddRowSet.SetFieldVal(sColumn, sValue);
						//VS 06222016
						AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"));
					}
				}

				//changes from 5.5 24/3/17
				string sType = GetVal(dt, "Type");
				if (sType == "Sent")
				{
					objAddRowSet.SetFieldVal("MLS_TYPE", 6, clC.SELL_SYSTEM);
				}
				else if (sType == "Received")
				{
					objAddRowSet.SetFieldVal("MLS_TYPE", 5, clC.SELL_SYSTEM);
				}
				else
				{
					objAddRowSet.SetFieldVal("MLS_TYPE", sType, clC.SELL_FRIENDLY);
				}

				//08022018:Tckt#2357 Change - Email log type when activity log is selected in outlook..J
				//Change type in sas70.selltis.com when 'From' contains sas domain and 'To' does not contain.
				//Checking CC to differentiate multiple sites not sas70.selltis.com
				//29102018 tckt #2518: AC log type revert back change..J
				//If (GetVal(dt, "From").Contains("@stresshq.com") And GetVal(dt, "CC").Contains("@stresshq.com")) And (Not GetVal(dt, "To").Contains("@stresshq.com")) Then
				//    objAddRowSet.SetFieldVal("MLS_TYPE", 6, clC.SELL_SYSTEM) 'Sent
				//Else
				//    If sType = "Sent" Then
				//        objAddRowSet.SetFieldVal("MLS_TYPE", 6, clC.SELL_SYSTEM)  'Sent
				//    ElseIf sType = "Received" Then
				//        objAddRowSet.SetFieldVal("MLS_TYPE", 5, clC.SELL_SYSTEM)  'Received
				//    Else
				//        objAddRowSet.SetFieldVal("MLS_TYPE", sType, clC.SELL_FRIENDLY)
				//    End If
				//End If

				objAddRowSet.SetFieldVal("MMO_NOTES", GetVal(dt, "Subject"));
				objAddRowSet.SetFieldVal("FIL_ATTACHMENTS", GetVal(dt, "Attachments"));
				objAddRowSet.SetFieldVal("TXT_SUBJ", GetVal(dt, "Subject").Substring(0, 80));
				objAddRowSet.SetFieldVal("MMO_LETTER", "From: " + GetVal(dt, "From") + "\r\n" + "Received: " + GetVal(dt, "Date") + " " + GetVal(dt, "Time") + "\r\n" + "Subject: " + GetVal(dt, "Subject") + "\r\n" + "\r\n" + GetVal(dt, "Body"));

				if (GetVal(dt, "Type") == "Sent")
				{
					objAddRowSet.SetFieldVal("EML_EMAIL", GetVal(dt, "To"));
					objAddRowSet.SetFieldVal("CHK_SENT", 1, clC.SELL_SYSTEM);
					objAddRowSet.SetFieldVal("MLS_STATUS", 1, clC.SELL_SYSTEM);
				}
				else
				{
					objAddRowSet.SetFieldVal("EML_EMAIL", GetVal(dt, "From"));
					//VS 02152017 : Getting value from Workgroup options
					string sStatus = goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "LOG_EMAIL_STATUS_DEFAULT", "0", false, "");
					objAddRowSet.SetFieldVal("MLS_STATUS", sStatus, clC.SELL_SYSTEM);
					//objAddRowSet.SetFieldVal("MLS_STATUS", 0, clC.SELL_SYSTEM)
				}


				//


				//Email sent
				if (GetVal(dt, "Type") == "Sent")
				{

					iShare = int.Parse(goTR.StrRead(sMemo, "EMAILSHARESENT", "1"));
					objAddRowSet.SetFieldVal("SI__SHARESTATE", iShare);


					objAddRowSet.SetFieldVal("MMO_LETTER", "From: " + GetVal(dt, "From") + "\r\n" + "Sent: " + GetVal(dt, "Date") + " " + GetVal(dt, "Time") + "\r\n" + "Subject: " + GetVal(dt, "Subject") + "\r\n" + "\r\n" + GetVal(dt, "Body"));

					sLink = "";
					sComp = "";
					sCont = "";

					sAdd = Microsoft.VisualBasic.Strings.Split(GetVal(dt, "To"), "; ");
					for (l = 0; l <= sAdd.GetUpperBound(0); l++)
					{
						GetEmailAliasLinks(sAdd[l], ref sLink, ref sComp, ref sCont);
						if (sComp != "")
						{
							objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);
						}
						if (sCont != "")
						{
							objAddRowSet.SetLinkVal("LNK_Related_CN", sCont);
						}
					}

					sLink = "";
					sComp = "";
					sCont = "";
					sComps = "";
					sConts = "";

					sAdd = Microsoft.VisualBasic.Strings.Split(GetVal(dt, "CC"), "; ");
					for (l = 0; l <= sAdd.GetUpperBound(0); l++)
					{
						GetEmailAliasLinks(sAdd[l], ref sLink, ref sComp, ref sCont);
						if (sComp != "")
						{
							objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);
						}
						if (sCont != "")
						{
							objAddRowSet.SetLinkVal("LNK_cc_CN", sCont);
						}
					}

					sLink = "";
					sComp = "";
					sCont = "";
					sComps = "";
					sConts = "";

					sAdd = Microsoft.VisualBasic.Strings.Split(GetVal(dt, "BCC"), "; ");
					for (l = 0; l <= sAdd.GetUpperBound(0); l++)
					{
						GetEmailAliasLinks(sAdd[l], ref sLink, ref sComp, ref sCont);
						if (sComp != "")
						{
							objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);
						}
						if (sCont != "")
						{
							objAddRowSet.SetLinkVal("LNK_bc_CN", sCont);
						}
					}

					//Email received
				}
				else
				{

					iShare = int.Parse(goTR.StrRead(sMemo, "EMAILSHARERECEIVED", "1"));
					objAddRowSet.SetFieldVal("SI__SHARESTATE", iShare);

					objAddRowSet.SetFieldVal("MMO_LETTER", "From: " + GetVal(dt, "From") + "\r\n" + "Received: " + GetVal(dt, "Date") + " " + GetVal(dt, "Time") + "\r\n" + "Subject: " + GetVal(dt, "Subject") + "\r\n" + "\r\n" + GetVal(dt, "Body"));

					sLink = "";
					sComp = "";
					sCont = "";
					GetEmailAliasLinks(GetVal(dt, "From"), ref sLink, ref sComp, ref sCont);
					if (sLink != "")
					{
						objAddRowSet.SetLinkVal("LNK_Related_EL", sLink);
					}
					if (sCont != "")
					{
						objAddRowSet.SetLinkVal("LNK_Related_CN", sCont);
					}
					if (sComp != "")
					{
						objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);
					}

					//tkt #1872 new feature:To add all cc individuals and allowing to reply to all when log mail..J
					sLink = "";
					sComp = "";
					sCont = "";
					sComps = "";
					sConts = "";
					sAdd = Microsoft.VisualBasic.Strings.Split(GetVal(dt, "CC"), "; ");
					for (l = 0; l <= sAdd.GetUpperBound(0); l++)
					{
						GetEmailAliasLinks(sAdd[l], ref sLink, ref sComp, ref sCont);
						if (sComp != "")
						{
							objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);
						}
						if (sCont != "")
						{
							objAddRowSet.SetLinkVal("LNK_cc_CN", sCont);
						}
					}

				}


				return objAddRowSet.Commit().ToString();

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try


		}

		//S_B Tkt#2216 New Log mail function for Attachments
		public string LogMessage_With_Attachments(DataTable dt, DataTable dtAttachments, string hostName = "")
		{
				string tempLogMessage_With_Attachments = null;

			string sProc = "clEmail::LogMessage";

			tempLogMessage_With_Attachments = "";

			//  Try

			string sComp = null;
			string sCont = null;
			string sLink = null;
			string[] sAdd = null;
			int l = 0;
			string sComps = "";
			string sConts = "";
			string sMemo = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "", true);


			int iShare = 0;


			clRowSet objAddRowSet = new clRowSet("AC", 2);
			objAddRowSet.bBypassValidation = true;

			objAddRowSet.SetFieldVal("MMO_HISTORY", PrepHistory() + " " + goP.GetUserCode() + " E-mail Logged" + "\r\n" + "To: " + GetVal(dt, "To") + "\r\n" + "cc: " + GetVal(dt, "CC") + "\r\n" + "From: " + GetVal(dt, "From"));

			objAddRowSet.SetFieldVal("CHK_EXTDEVREVIEW", 1, clC.SELL_SYSTEM);

			objAddRowSet.SetFieldVal("LNK_From_SO", GetSourceVal());
			objAddRowSet.SetFieldVal("DTE_STARTTIME", GetVal(dt, "Date"));
			objAddRowSet.SetFieldVal("DTE_ENDTIME", GetVal(dt, "Date"));
			objAddRowSet.SetFieldVal("TME_STARTTIME", GetVal(dt, "Time"));
			objAddRowSet.SetFieldVal("TME_ENDTIME", GetVal(dt, "Time"));
			objAddRowSet.SetFieldVal("LNK_CreatedBy_US", goP.gsUserID);

			if (dt.Columns.Contains("LinkRelatedField1") & dt.Columns.Contains("LinkRelatedValue1"))
			{
				string sColumn = GetVal(dt, "LinkRelatedField1");
				string sValue = GetVal(dt, "LinkRelatedValue1");

				if (string.IsNullOrEmpty(sColumn) == false && string.IsNullOrEmpty(sValue) == false)
				{
					objAddRowSet.SetFieldVal(sColumn, sValue);
					//VS 06222016
					AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"));
				}
			}

			if (dt.Columns.Contains("LinkRelatedField2") & dt.Columns.Contains("LinkRelatedValue2"))
			{
				string sColumn = GetVal(dt, "LinkRelatedField2");
				string sValue = GetVal(dt, "LinkRelatedValue2");

				if (string.IsNullOrEmpty(sColumn) == false && string.IsNullOrEmpty(sValue) == false)
				{
					objAddRowSet.SetFieldVal(sColumn, sValue);
					//VS 06222016
					AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"));
				}
			}

			if (dt.Columns.Contains("LinkRelatedField3") & dt.Columns.Contains("LinkRelatedValue3"))
			{
				string sColumn = GetVal(dt, "LinkRelatedField3");
				string sValue = GetVal(dt, "LinkRelatedValue3");

				if (string.IsNullOrEmpty(sColumn) == false && string.IsNullOrEmpty(sValue) == false)
				{
					objAddRowSet.SetFieldVal(sColumn, sValue);
					//VS 06222016
					AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"));
				}
			}

			if (dt.Columns.Contains("LinkRelatedField4") & dt.Columns.Contains("LinkRelatedValue4"))
			{
				string sColumn = GetVal(dt, "LinkRelatedField4");
				string sValue = GetVal(dt, "LinkRelatedValue4");

				if (string.IsNullOrEmpty(sColumn) == false && string.IsNullOrEmpty(sValue) == false)
				{
					objAddRowSet.SetFieldVal(sColumn, sValue);
					//VS 06222016
					AddEmailtoLNKRecJournal(sValue, GetVal(dt, "Subject"), GetVal(dt, "Body"), GetVal(dt, "Attachments"));
				}
			}

			//changes from 5.5 24/3/17
			string sType = GetVal(dt, "Type");
			if (sType == "Sent")
			{
				objAddRowSet.SetFieldVal("MLS_TYPE", 6, clC.SELL_SYSTEM);
			}
			else if (sType == "Received")
			{
				objAddRowSet.SetFieldVal("MLS_TYPE", 5, clC.SELL_SYSTEM);
			}
			else
			{
				objAddRowSet.SetFieldVal("MLS_TYPE", sType, clC.SELL_FRIENDLY);
			}

			//08022018:Tckt#2357 Change - Email log type when activity log is selected in outlook..J
			//Change type in sas70.selltis.com when 'From' contains sas domain and 'To' does not contain.
			//Checking CC to differentiate multiple sites not sas70.selltis.com
			//29102018 tckt #2518: AC log type revert back change..J
			//If (GetVal(dt, "From").Contains("@stresshq.com") And GetVal(dt, "CC").Contains("@stresshq.com")) And (Not GetVal(dt, "To").Contains("@stresshq.com")) Then
			//    objAddRowSet.SetFieldVal("MLS_TYPE", 6, clC.SELL_SYSTEM) 'Sent
			//Else
			//If sType = "Sent" Then
			//    objAddRowSet.SetFieldVal("MLS_TYPE", 6, clC.SELL_SYSTEM)  'Sent
			//ElseIf sType = "Received" Then
			//    objAddRowSet.SetFieldVal("MLS_TYPE", 5, clC.SELL_SYSTEM)  'Received
			//Else
			//    objAddRowSet.SetFieldVal("MLS_TYPE", sType, clC.SELL_FRIENDLY)
			//End If
			//End If


			objAddRowSet.SetFieldVal("MMO_NOTES", GetVal(dt, "Subject"));
			//objAddRowSet.SetFieldVal("FIL_ATTACHMENTS", GetVal(dt, "Attachments"))
			objAddRowSet.SetFieldVal("TXT_SUBJ", GetVal(dt, "Subject").Substring(0, 80));
			objAddRowSet.SetFieldVal("MMO_LETTER", "From: " + GetVal(dt, "From") + "\r\n" + "Received: " + GetVal(dt, "Date") + " " + GetVal(dt, "Time") + "\r\n" + "Subject: " + GetVal(dt, "Subject") + "\r\n" + "\r\n" + GetVal(dt, "Body"));

			if (GetVal(dt, "Type") == "Sent")
			{
				objAddRowSet.SetFieldVal("EML_EMAIL", GetVal(dt, "To"));
				objAddRowSet.SetFieldVal("CHK_SENT", 1, clC.SELL_SYSTEM);
				objAddRowSet.SetFieldVal("MLS_STATUS", 1, clC.SELL_SYSTEM);
			}
			else
			{
				objAddRowSet.SetFieldVal("EML_EMAIL", GetVal(dt, "From"));
				//VS 02152017 : Getting value from Workgroup options
				string sStatus = goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "LOG_EMAIL_STATUS_DEFAULT", "0", false, "");
				objAddRowSet.SetFieldVal("MLS_STATUS", sStatus, clC.SELL_SYSTEM);
				//objAddRowSet.SetFieldVal("MLS_STATUS", 0, clC.SELL_SYSTEM)
			}


			//


			//Email sent
			if (GetVal(dt, "Type") == "Sent")
			{

				iShare = int.Parse(goTR.StrRead(sMemo, "EMAILSHARESENT", "1"));
				objAddRowSet.SetFieldVal("SI__SHARESTATE", iShare);


				objAddRowSet.SetFieldVal("MMO_LETTER", "To: " + GetVal(dt, "To") + "\r\n" + "CC: " + GetVal(dt, "CC") + "\r\n" + "From: " + GetVal(dt, "From") + "\r\n" + "Sent: " + GetVal(dt, "Date") + " " + GetVal(dt, "Time") + "\r\n" + "Subject: " + GetVal(dt, "Subject") + "\r\n" + "\r\n" + GetVal(dt, "Body"));

				sLink = "";
				sComp = "";
				sCont = "";

				sAdd = Microsoft.VisualBasic.Strings.Split(GetVal(dt, "To"), "; ");
				for (l = 0; l <= sAdd.GetUpperBound(0); l++)
				{
					GetEmailAliasLinks(sAdd[l], ref sLink, ref sComp, ref sCont);
					if (sComp != "")
					{
						objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);
					}
					if (sCont != "")
					{
						objAddRowSet.SetLinkVal("LNK_Related_CN", sCont);
					}
				}

				sLink = "";
				sComp = "";
				sCont = "";
				sComps = "";
				sConts = "";

				sAdd = Microsoft.VisualBasic.Strings.Split(GetVal(dt, "CC"), "; ");
				for (l = 0; l <= sAdd.GetUpperBound(0); l++)
				{
					GetEmailAliasLinks(sAdd[l], ref sLink, ref sComp, ref sCont);
					if (sComp != "")
					{
						objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);
					}
					if (sCont != "")
					{
						objAddRowSet.SetLinkVal("LNK_cc_CN", sCont);
					}
				}

				sLink = "";
				sComp = "";
				sCont = "";
				sComps = "";
				sConts = "";

				sAdd = Microsoft.VisualBasic.Strings.Split(GetVal(dt, "BCC"), "; ");
				for (l = 0; l <= sAdd.GetUpperBound(0); l++)
				{
					GetEmailAliasLinks(sAdd[l], ref sLink, ref sComp, ref sCont);
					if (sComp != "")
					{
						objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);
					}
					if (sCont != "")
					{
						objAddRowSet.SetLinkVal("LNK_bc_CN", sCont);
					}
				}

				//Email received
			}
			else
			{

				iShare = int.Parse(goTR.StrRead(sMemo, "EMAILSHARERECEIVED", "1"));
				objAddRowSet.SetFieldVal("SI__SHARESTATE", iShare);

				objAddRowSet.SetFieldVal("MMO_LETTER", "To: " + GetVal(dt, "To") + "\r\n" + "CC: " + GetVal(dt, "CC") + "\r\n" + "From: " + GetVal(dt, "From") + "\r\n" + "Received: " + GetVal(dt, "Date") + " " + GetVal(dt, "Time") + "\r\n" + "Subject: " + GetVal(dt, "Subject") + "\r\n" + "\r\n" + GetVal(dt, "Body"));

				sLink = "";
				sComp = "";
				sCont = "";
				GetEmailAliasLinks(GetVal(dt, "From"), ref sLink, ref sComp, ref sCont);
				if (sLink != "")
				{
					objAddRowSet.SetLinkVal("LNK_Related_EL", sLink);
				}
				if (sCont != "")
				{
					objAddRowSet.SetLinkVal("LNK_Related_CN", sCont);
				}
				if (sComp != "")
				{
					objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);
				}

				//tkt #1872 new feature:To add all cc individuals and allowing to reply to all when log mail..J
				sLink = "";
				sComp = "";
				sCont = "";
				sComps = "";
				sConts = "";
				sAdd = Microsoft.VisualBasic.Strings.Split(GetVal(dt, "CC"), "; ");
				for (l = 0; l <= sAdd.GetUpperBound(0); l++)
				{
					GetEmailAliasLinks(sAdd[l], ref sLink, ref sComp, ref sCont);
					if (sComp != "")
					{
						objAddRowSet.SetLinkVal("LNK_Related_CO", sComp);
					}
					if (sCont != "")
					{
						objAddRowSet.SetLinkVal("LNK_cc_CN", sCont);
					}
				}

			}


			//Return objAddRowSet.Commit().ToString
			string retVal = objAddRowSet.Commit().ToString();

			if (retVal == "1")
			{
				string sGID_ID = objAddRowSet.oDataSet.Tables[0].Rows[0]["GID_ID"].ToString();
				string sViewName = "AC";
				string sFieldName = "ADR_ATTACHMENTS";

				clAttachments objclAttachments = new clAttachments();

				if (dtAttachments.Rows.Count > 0)
				{
					foreach (DataRow row in dtAttachments.Rows)
					{
						string FileName = row["FileName"].ToString();
						string sFileData = row["FileStream"].ToString();

						string sUploadStatus = objclAttachments.UploadAttachment(sViewName, sGID_ID, sFieldName, FileName, sFileData, hostName).ToString();
					}
				}
				return retVal;
			}

			return retVal;

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try


		}

		public bool AddEmailtoLNKRecJournal(string sLinkedRecordID, string sSubject, string sBody, string sAttachments)
		{
			//VS 06222016
			//Purpose : Add Email as journal entry to the linked record with subject and First 250 chars of Body.
			//VS 07212016: Include Attachments also.

			if (sLinkedRecordID == "")
			{
				return true;
			}

			//Check if Email Journaling has been disabled in WOP_WORKGROUP_OPTIONS 
			if (goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "LOGANDLINKJOURNAL_ENABLED", "1") == "0")
			{
				return true;
			}

			string sFile = "";
			string sJournEntry = "";
			string sJournal = "";
			string sDateStamp = "";
			clRowSet doRS = null;

			sFile = goTR.GetFileFromSUID(sLinkedRecordID);
			if (sFile == "")
			{
				return true;
			}

			if (!goData.IsFieldValid(sFile, "MMO_JOURNAL"))
			{
				return true;
			}

			doRS = new clRowSet(sFile, clC.SELL_EDIT, "GID_ID='" + sLinkedRecordID + "'", "", "", -1, "", "", "", "", "", true, true);

			if (doRS.GetFirst() == 1)
			{
				object temp_doRS = doRS;
				object temp_sDateStamp = sDateStamp;
				goScr.RunScript("GetDateTimeStampRecord", ref temp_doRS, null, "NEUTRAL", "", "CODE", "USERNOOFFSETLABEL", "", ref temp_sDateStamp); //returns var sDateStamp
					sDateStamp = Convert.ToString(temp_sDateStamp);
					doRS = (Selltis.BusinessLogic.clRowSet)temp_doRS;
				//sJournEntry = sDateStamp & " " & sSubject & vbCrLf & Left(sBody.Trim, 250)
				sJournEntry = sDateStamp + " " + sSubject + "\r\n" + sBody;
				sJournal = Convert.ToString(doRS.GetFieldVal("MMO_JOURNAL"));
				sJournal = sJournEntry + "\r\n" + sJournal;
				doRS.SetFieldVal("MMO_JOURNAL", sJournal);
				if (goData.IsFieldValid(sFile, "FIL_ATTACHMENTS"))
				{
					doRS.SetFieldVal("FIL_ATTACHMENTS", sAttachments);
				}
				if (doRS.Commit() == 0)
				{
					return false;
				}
			}

			return true;
		}

		public string GetVal(DataTable par_dt, string par_sField)
		{
				string tempGetVal = null;

			string sProc = "clEmail::GetVal";

			tempGetVal = "";

			//Try

			if (par_dt.Rows[0][par_sField].GetType().ToString() == "System.DBNull")
			{
					tempGetVal = "";
				}
				else
				{
					tempGetVal = Convert.ToString(par_dt.Rows[0][par_sField]);
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return tempGetVal;
		}

		public string PrepHistory()
		{
			string sProc = "clEmail::PrepHistory";

			//2003-11-04 08:33

			return goTR.DateTimeToString(goTR.NowUTC());

		}

		public string GetSourceVal()
		{
					string tempGetSourceVal = null;
				try
				{
				string sProc = "clEmail::GetSourceVal";

				tempGetSourceVal = "";


				if (sSourceID == "")
				{
					clRowSet oRS = new clRowSet("SO", 3, "TXT_SOURCENAME='E-MAIL'", "", "GID_ID");
					if (oRS.Count() == 1)
					{
						tempGetSourceVal = Convert.ToString(oRS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY));
						sSourceID = tempGetSourceVal;
					}

				}
				else
				{
					tempGetSourceVal = sSourceID;
				}

				return tempGetSourceVal;

			}
			catch
			{
				goto ErrorHandler;
			}
	ErrorHandler:

			return "";

		}

		public void GetEmailAliasLinks(string strEmail, ref string sLink, ref string sCompany, ref string sContact)
		{

			string sProc = "clEmail::GetEmailAliasLinks";

			sCompany = "";
			sContact = "";
			sLink = "";

			if (!string.IsNullOrEmpty(strEmail.Trim(' ')))
			{

				strEmail = goTR.PrepareForSQL(strEmail);

				clRowSet objCNRowSet = new clRowSet("CN", 3, "EML_EMAIL='" + strEmail.Substring(0, 100) + "' ", "DTT_CreationTime desc", "**", 1);

				if (objCNRowSet.GetFirst() == 1)
				{

					sLink = "";
					sCompany = Convert.ToString(objCNRowSet.GetFieldVal("LNK_Related_CO"));
					sContact = Convert.ToString(objCNRowSet.GetFieldVal("GID_ID"));
					//If objCNRowSet.GetNext() = 1 Then
					//    sLink = ""
					//    sCompany = ""
					//    sContact = ""
					//End If

					objCNRowSet = null;

					//Else

					//    Dim objRowSet As New clRowSet("EL", 3, "TXT_EmailAddress='" & Left(strEmail, 100) & "' and CHK_DONOTAUTOCONNECT=0", "", "**")

					//    If objRowSet.GetFirst() = 1 Then

					//        sLink = objRowSet.GetFieldVal("GID_ID")
					//        sCompany = objRowSet.GetFieldVal("LNK_Related_CO")
					//        sContact = objRowSet.GetFieldVal("LNK_Related_CN")
					//        If objRowSet.GetNext() = 1 Then
					//            sLink = ""
					//            sCompany = ""
					//            sContact = ""
					//        End If
					//    End If

					//    objRowSet = Nothing

				}

			}

			// Try
			//Dim objRowSet As New clRowSet("EL", 3, "TXT_EmailAddress='" & Left(strEmail, 100) & "' and CHK_DONOTAUTOCONNECT=0", "", "**")

			//    If objRowSet.GetFirst() = 1 Then

			//        sLink = objRowSet.GetFieldVal("GID_ID")
			//        sCompany = objRowSet.GetFieldVal("LNK_Related_CO")
			//        sContact = objRowSet.GetFieldVal("LNK_Related_CN")
			//        If objRowSet.GetNext() = 1 Then
			//            sLink = ""
			//            sCompany = ""
			//            sContact = ""
			//        End If
			//    End If

			//    objRowSet = Nothing

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try


		}

		public bool SendSMTPEmail(string par_sSubject, string par_sBody, string par_sTo, string par_sCC, string par_sBCC, string par_sUnused, string par_ReplyName = "", string par_ReplyAddress = "", string sFiles = "", bool bHTML = false, string sHTMLURI = "", string sHTMLURIBase = "", bool bDeleteFiles = true, bool bAppendTextFromWOP = true)
		{
			//MI 2/1/12 Added bAppendTextFromWOP.
			//MI 4/12/11 Expanded message in Catch SMTPProtocolExcep As SMTPProtocolException.
			//MI 3/9/07 Added SetWarnings, streamlined returning result.
			//VS 05022017 hardCoding From <NAME_EMAIL> and ReplyTo to par_ReplyAddress

			string sProc = "clEmail::SendSMTPEmail";

			//21/06/2017 7.0 Changes..J
			return SendSMTPEmailNew(par_sSubject, par_sBody, par_sTo, par_sCC, par_sBCC, par_sUnused, par_ReplyName, par_ReplyAddress, sFiles, bHTML, sHTMLURI, sHTMLURIBase, bDeleteFiles, bAppendTextFromWOP);

			//Dim msgObj As New Quiksoft.EasyMail.SMTP.EmailMessage
			//Dim smtpObj As New Quiksoft.EasyMail.SMTP.SMTP
			//Dim sAttach(0) As String
			//Dim sTo(0) As String
			//Dim sCC(0) As String
			//Dim sBC(0) As String
			//Dim f As Integer
			//Dim bResult As Boolean = True

			//Try

			//    Quiksoft.EasyMail.SMTP.License.Key = "Selltis, LLC (Single Developer)/6899051F129703729F3CD444C1B003"

			//    msgObj.Subject = par_sSubject


			//    'Add TOs
			//    par_sTo = goTR.Replace(par_sTo, ",", ";")
			//    If par_sTo <> "" Then
			//        sTo = Split(par_sTo, ";")
			//        For f = 0 To sTo.GetUpperBound(0)
			//            If Trim(sTo(f)) <> "" Then
			//                msgObj.Recipients.Add(Trim(sTo(f)), "", RecipientType.To)
			//            End If
			//        Next
			//    End If

			//    'Add CCs
			//    par_sCC = goTR.Replace(par_sCC, ",", ";")
			//    If par_sCC <> "" Then
			//        sCC = Split(par_sCC, ";")
			//        For f = 0 To sCC.GetUpperBound(0)
			//            If Trim(sCC(f)) <> "" Then
			//                msgObj.Recipients.Add(Trim(sCC(f)), "", RecipientType.CC)
			//            End If
			//        Next
			//    End If

			//    'Add BCCs
			//    par_sBCC = goTR.Replace(par_sBCC, ",", ";")
			//    If par_sBCC <> "" Then
			//        sBC = Split(par_sBCC, ";")
			//        For f = 0 To sBC.GetUpperBound(0)
			//            If Trim(sBC(f)) <> "" Then
			//                msgObj.Recipients.Add(Trim(sBC(f)), "", RecipientType.BCC)
			//            End If
			//        Next
			//    End If

			//    'Specify the sender
			//    'VS 05022017 : Set from <NAME_EMAIL>
			//    'msgObj.From.Email = par_ReplyAddress
			//    msgObj.From.Email = "<EMAIL>"
			//    msgObj.From.Name = par_ReplyName = System.Configuration.ConfigurationManager.AppSettings("smtpSender")

			//    'VS 05022017 : set reply email
			//    msgObj.ReplyTo = par_ReplyAddress

			//    'Set message body

			//    If bAppendTextFromWOP Then      '2/1/12 MI Added testing this to be able to send an email without the WOP text appended
			//        par_sBody = par_sBody & vbCrLf & vbCrLf & goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "EMAILAPPEND", "", False)
			//    End If

			//    If bHTML = False Then
			//        msgObj.BodyParts.Add(par_sBody)
			//    Else
			//        If sHTMLURI <> "" Then
			//            msgObj.ImportFromURI(sHTMLURI, sHTMLURIBase, ImportURIFlags.EmbedImages)
			//        Else
			//            msgObj.BodyParts.Add(par_sBody, BodyPartFormat.HTML)
			//        End If
			//    End If


			//    'Add attachment
			//    If sFiles <> "" Then
			//        sAttach = Split(sFiles, vbCrLf)

			//        For f = 0 To sAttach.GetUpperBound(0)
			//            If sAttach(f) <> "" Then
			//                msgObj.Attachments.Add(sAttach(f))
			//            End If
			//        Next
			//    End If

			//    'Set mail server
			//    Dim oServer As New Quiksoft.EasyMail.SMTP.SMTPServer

			//    'oServer.AuthMode = SMTPAuthMode.CramMD5
			//    'oServer.Name = "mail.selltis.com"
			//    'oServer.Account = "sellsmtp"
			//    'oServer.Password = "smtpking"
			//    ''oServer.Port = 12225
			//    'oServer.Port = 587

			//    Dim smtpServer As String = System.Configuration.ConfigurationManager.AppSettings("smtpServer")
			//    If String.IsNullOrEmpty(smtpServer) Then
			//        smtpServer = "mail.selltis.com"
			//    Else
			//        smtpServer = smtpServer.ToString()
			//    End If

			//    Dim smtpPort As String = System.Configuration.ConfigurationManager.AppSettings("smtpPort")
			//    If String.IsNullOrEmpty(smtpPort) Then
			//        smtpPort = 587
			//    Else
			//        smtpPort = Convert.ToInt32(smtpPort)
			//    End If

			//    Dim smtpUser As String = System.Configuration.ConfigurationManager.AppSettings("smtpUser")
			//    If String.IsNullOrEmpty(smtpUser) Then
			//        smtpUser = "sellsmtp"
			//    Else
			//        smtpUser = smtpUser.ToString()
			//    End If

			//    Dim smtpPassword As String = System.Configuration.ConfigurationManager.AppSettings("smtpPassword")
			//    If String.IsNullOrEmpty(smtpPassword) Then
			//        smtpPassword = "smtpking"
			//    Else
			//        smtpPassword = smtpPassword.ToString()
			//    End If

			//    oServer.AuthMode = SMTPAuthMode.CramMD5
			//    oServer.Name = smtpServer
			//    oServer.Account = smtpUser
			//    oServer.Password = smtpPassword
			//    'oServer.Port = 12225
			//    oServer.Port = smtpPort

			//    smtpObj.SMTPServers.Add(oServer)

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If

			//End Try


			//Try
			//    'Send the message
			//    smtpObj.Send(msgObj)
			//    bResult = True

			//Catch LicenseExcep As LicenseException
			//    'MsgBox("License key error: " + LicenseExcep.Message)
			//    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed: the license appears to be invalid.")
			//    bResult = False
			//Catch FileIOExcep As FileIOException
			//    'MsgBox("File IO error: " + FileIOExcep.Message)
			//    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed due to 'FileIOException'.")
			//    bResult = False
			//Catch SMTPAuthExcep As SMTPAuthenticationException
			//    'MsgBox("SMTP Authentication error: " + SMTPAuthExcep.Message)
			//    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed: the user can't be authenticated.")
			//    bResult = False
			//Catch SMTPConnectExcep As SMTPConnectionException
			//    'Debug.Print("Connection error: " + SMTPConnectExcep.Message & "  " & SMTPConnectExcep.ErrorCode.ToString)
			//    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed: connection can't be established.")
			//    bResult = False
			//Catch SMTPProtocolExcep As SMTPProtocolException
			//    'MsgBox("SMTP protocol error: " + SMTPProtocolExcep.Message)
			//    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed due to an unspecified SMTP Protocol Exception. The recipient's address may be invalid.")
			//    bResult = False
			//End Try

			//'Clean up temporary attachments unless there was an error above

			//If bDeleteFiles Then
			//    If bResult Then
			//        Try
			//            For f = 0 To sAttach.GetUpperBound(0)
			//                If sAttach(f) <> "" Then
			//                    My.Computer.FileSystem.DeleteFile(sAttach(f), FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
			//                End If
			//            Next

			//            If sHTMLURI <> "" Then
			//                My.Computer.FileSystem.DeleteFile(sHTMLURI, FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
			//            End If
			//        Catch ex As Exception
			//            If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//                goErr.SetError(ex, 45105, sProc)
			//            End If
			//        End Try
			//    End If
			//End If


			//Return bResult

		}


		public bool SendSMTPEmailCustom(string par_sSubject, string par_sBody, string par_sTo, string par_sCC, string par_sBCC, string par_sUnused, string par_ReplyName = "", string par_ReplyAddress = "", string sFiles = "", bool bHTML = false, string sHTMLURI = "", string sHTMLURIBase = "", bool bDeleteFiles = true, bool bAppendTextFromWOP = true)
		{
			//MI 2/1/12 Added bAppendTextFromWOP.
			//MI 4/12/11 Expanded message in Catch SMTPProtocolExcep As SMTPProtocolException.
			//MI 3/9/07 Added SetWarnings, streamlined returning result.
			//VS 05022017 hardCoding From <NAME_EMAIL> and ReplyTo to par_ReplyAddress

			string sProc = "clEmail::SendSMTPEmail";

			//09/07/2018 Send email with sender from email..J
			return SendSMTPEmailNewCustom(par_sSubject, par_sBody, par_sTo, par_sCC, par_sBCC, par_sUnused, par_ReplyName, par_ReplyAddress, sFiles, bHTML, sHTMLURI, sHTMLURIBase, bDeleteFiles, bAppendTextFromWOP);

		}

		public bool SendSMTPEmailHTMLWithEmbeddedImages(string par_sSubject, string par_sBody, string par_sTo, string par_sCC, string par_sBCC, string par_sAttachments, string par_ReplyName = "", string par_ReplyAddress = "", string sFiles = "", bool bHTML = false, string sHTMLURI = "", string sHTMLURIBase = "", bool bAppendTextFromWOP = true)
		{

			//MI 2/1/12 Added bAppendTextFromWOP.


			string sProc = "clEmail::SendSMTPEmailHTMLWithEmbeddedImages";

			//21/06/2017 7.0 Changes..J
			return SendSMTPEmailNew(par_sSubject, par_sBody, par_sTo, par_sCC, par_sBCC, par_sAttachments, par_ReplyName, par_ReplyAddress, sFiles, bHTML, sHTMLURI, sHTMLURIBase, true, bAppendTextFromWOP);

			//Dim msgObj As New Quiksoft.EasyMail.SMTP.EmailMessage
			//Dim smtpObj As New Quiksoft.EasyMail.SMTP.SMTP
			//Dim sAttach(0) As String
			//Dim sTo(0) As String
			//Dim sCC(0) As String
			//Dim sBC(0) As String
			//Dim f As Integer
			//Dim bResult As Boolean = True



			//Try

			//    Quiksoft.EasyMail.SMTP.License.Key = "Selltis, LLC (Single Developer)/6899051F129703729F3CD444C1B003"

			//    msgObj.Subject = par_sSubject


			//    'Add TOs
			//    par_sTo = goTR.Replace(par_sTo, ",", ";")
			//    If par_sTo <> "" Then
			//        sTo = Split(par_sTo, ";")
			//        For f = 0 To sTo.GetUpperBound(0)
			//            If Trim(sTo(f)) <> "" Then

			//                msgObj.Recipients.Add(Trim(sTo(f)), "", RecipientType.To)
			//            End If
			//        Next
			//    End If

			//    'Add CCs
			//    par_sCC = goTR.Replace(par_sCC, ",", ";")
			//    If par_sCC <> "" Then
			//        sCC = Split(par_sCC, ";")
			//        For f = 0 To sCC.GetUpperBound(0)
			//            If Trim(sCC(f)) <> "" Then
			//                msgObj.Recipients.Add(Trim(sCC(f)), "", RecipientType.CC)
			//            End If
			//        Next
			//    End If

			//    'Add BCCs
			//    par_sBCC = goTR.Replace(par_sBCC, ",", ";")
			//    If par_sBCC <> "" Then
			//        sBC = Split(par_sBCC, ";")
			//        For f = 0 To sBC.GetUpperBound(0)
			//            If Trim(sBC(f)) <> "" Then
			//                msgObj.Recipients.Add(Trim(sBC(f)), "", RecipientType.BCC)
			//            End If
			//        Next
			//    End If

			//    'Specify the sender
			//    msgObj.From.Email = par_ReplyAddress
			//    msgObj.From.Name = par_ReplyName = System.Configuration.ConfigurationManager.AppSettings("smtpSender")

			//    'Set message body

			//    If bAppendTextFromWOP Then      '2/1/12 MI Added testing this to be able to send an email without the WOP text appended
			//        par_sBody = par_sBody & vbCrLf & vbCrLf & goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "EMAILAPPEND", "", False)
			//    End If

			//    If bHTML = False Then
			//        msgObj.BodyParts.Add(par_sBody)
			//    Else
			//        msgObj.BodyParts.Add(par_sBody, BodyPartFormat.HTML)

			//    End If


			//    'Add attachment
			//    If sFiles <> "" Then
			//        sAttach = Split(sFiles, vbCrLf)

			//        For f = 0 To sAttach.GetUpperBound(0)
			//            If sAttach(f) <> "" Then
			//                msgObj.Attachments.Add(sAttach(f))
			//            End If
			//        Next
			//    End If

			//    'Set mail server
			//    Dim oServer As New Quiksoft.EasyMail.SMTP.SMTPServer

			//    'oServer.AuthMode = SMTPAuthMode.CramMD5
			//    'oServer.Name = "mail.selltis.com"
			//    'oServer.Account = "sellsmtp"
			//    'oServer.Password = "smtpking"
			//    ''oServer.Port = 12225
			//    'oServer.Port = 587

			//    Dim smtpServer As String = System.Configuration.ConfigurationManager.AppSettings("smtpServer")
			//    If String.IsNullOrEmpty(smtpServer) Then
			//        smtpServer = "mail.selltis.com"
			//    Else
			//        smtpServer = smtpServer.ToString()
			//    End If

			//    Dim smtpPort As String = System.Configuration.ConfigurationManager.AppSettings("smtpPort")
			//    If String.IsNullOrEmpty(smtpPort) Then
			//        smtpPort = 587
			//    Else
			//        smtpPort = Convert.ToInt32(smtpPort)
			//    End If

			//    Dim smtpUser As String = System.Configuration.ConfigurationManager.AppSettings("smtpUser")
			//    If String.IsNullOrEmpty(smtpUser) Then
			//        smtpUser = "sellsmtp"
			//    Else
			//        smtpUser = smtpUser.ToString()
			//    End If

			//    Dim smtpPassword As String = System.Configuration.ConfigurationManager.AppSettings("smtpPassword")
			//    If String.IsNullOrEmpty(smtpPassword) Then
			//        smtpPassword = "smtpking"
			//    Else
			//        smtpPassword = smtpPassword.ToString()
			//    End If

			//    oServer.AuthMode = SMTPAuthMode.CramMD5
			//    oServer.Name = smtpServer
			//    oServer.Account = smtpUser
			//    oServer.Password = smtpPassword
			//    'oServer.Port = 12225
			//    oServer.Port = smtpPort

			//    smtpObj.SMTPServers.Add(oServer)

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If

			//End Try


			//Try
			//    'Send the message
			//    smtpObj.Send(msgObj)

			//Catch LicenseExcep As LicenseException
			//    'MsgBox("License key error: " + LicenseExcep.Message)
			//    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed: the license appears to be invalid.")
			//    bResult = False
			//Catch FileIOExcep As FileIOException
			//    'MsgBox("File IO error: " + FileIOExcep.Message)
			//    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed due to 'FileIOException'.")
			//    bResult = False
			//Catch SMTPAuthExcep As SMTPAuthenticationException
			//    'MsgBox("SMTP Authentication error: " + SMTPAuthExcep.Message)
			//    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed: the user can't be authenticated.")
			//    bResult = False
			//Catch SMTPConnectExcep As SMTPConnectionException
			//    'Debug.Print("Connection error: " + SMTPConnectExcep.Message & "  " & SMTPConnectExcep.ErrorCode.ToString)
			//    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed: connection can't be established.")
			//    bResult = False
			//Catch SMTPProtocolExcep As SMTPProtocolException
			//    'MsgBox("SMTP protocol error: " + SMTPProtocolExcep.Message)
			//    goErr.SetWarning(35000, sProc, "Sending e-mail via SMTP server failed due to 'SMTPProtocolException'.")
			//    bResult = False
			//End Try

			//'Clean up temporary attachments unless there was an error above
			//If bResult Then
			//    Try
			//        For f = 0 To sAttach.GetUpperBound(0)
			//            If sAttach(f) <> "" Then
			//                My.Computer.FileSystem.DeleteFile(sAttach(f), FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
			//            End If
			//        Next
			//    Catch ex As Exception
			//        If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//            goErr.SetError(ex, 45105, sProc)
			//        End If
			//    End Try
			//End If

			//Return bResult

		}

		public bool SendSMTPEmailNew(string par_sSubject, string par_sBody, string par_sTo, string par_sCC, string par_sBCC, string par_sUnused, string par_ReplyName = "", string par_ReplyAddress = "", string sFiles = "", bool bHTML = false, string sHTMLURI = "", string sHTMLURIBase = "", bool bDeleteFiles = true, bool bAppendTextFromWOP = true)
		{

			string sProc = "clEmail::SendSMTPEmailNew";
			SmtpClient client = new SmtpClient();
			MailMessage message = new MailMessage();
			string[] sAttach = new string[1];
			string[] sTo = new string[1];
			string[] sCC = new string[1];
			string[] sBC = new string[1];
			int f = 0;
			bool bResult = true;

			//Try

			//Add TOs
			par_sTo = goTR.Replace(par_sTo, ",", ";");
			if (par_sTo != "")
			{
				sTo = par_sTo.Split(';');
				for (f = 0; f <= sTo.GetUpperBound(0); f++)
				{
					if (sTo[f].Trim(' ') != "")
					{
						message.To.Add(sTo[f].Trim(' '));
					}
				}
			}

			//Add CCs
			par_sCC = goTR.Replace(par_sCC, ",", ";");
			if (par_sCC != "")
			{
				sCC = par_sCC.Split(';');
				for (f = 0; f <= sCC.GetUpperBound(0); f++)
				{
					if (sCC[f].Trim(' ') != "")
					{
						message.CC.Add(sCC[f].Trim(' '));
					}
				}
			}

			//Add BCCs
			par_sBCC = goTR.Replace(par_sBCC, ",", ";");
			if (par_sBCC != "")
			{
				sBC = par_sBCC.Split(';');
				for (f = 0; f <= sBC.GetUpperBound(0); f++)
				{
					if (sBC[f].Trim(' ') != "")
					{
						message.Bcc.Add(sBC[f].Trim(' '));
					}
				}
			}

			//Specify the sender
			//msgObj.From.Email = par_ReplyAddress
			//msgObj.From.Name = par_ReplyName    

			//21092018 tickt #2463: Sending activty reports is not working..J
			//This is not wokrking on production due to display name format..J  
			string strDisplayName = par_ReplyName;
			par_ReplyName = System.Configuration.ConfigurationManager.AppSettings["smtpSender"];
			if (!string.IsNullOrEmpty(par_ReplyName))
			{
				//message.From = New MailAddress(par_ReplyName, par_ReplyAddress)
				message.From = new MailAddress(par_ReplyName, strDisplayName);
			}

			message.Priority = MailPriority.Normal;

			message.Subject = par_sSubject;

			//Set message body

			if (bAppendTextFromWOP) //2/1/12 MI Added testing this to be able to send an email without the WOP text appended
			{
				par_sBody = par_sBody + "\r\n" + "\r\n" + goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "EMAILAPPEND", "", false);
			}

			if (bHTML == false)
			{
				message.Body = par_sBody;
				message.IsBodyHtml = false;
			}
			else
			{
				//If sHTMLURI <> "" Then
				//    msgObj.ImportFromURI(sHTMLURI, sHTMLURIBase, ImportURIFlags.EmbedImages)
				//Else
				//    msgObj.BodyParts.Add(par_sBody, BodyPartFormat.HTML)
				//End If
				message.Body = par_sBody;
				message.IsBodyHtml = true;
			}

			//Add attachment
			if (sFiles != "")
			{
				sAttach = Microsoft.VisualBasic.Strings.Split(sFiles, "\r\n");
				for (f = 0; f <= sAttach.GetUpperBound(0); f++)
				{
					if (sAttach[f] != "")
					{
						//msgObj.Attachments.Add(sAttach(f))
						System.Net.Mail.Attachment attachment = new System.Net.Mail.Attachment(sAttach[f], System.Net.Mime.MediaTypeNames.Application.Octet);
						message.Attachments.Add(attachment);

					}
				}
			}

			//Set mail server
			//client.Host = "mail.selltis.com"
			//client.Port = 587
			//client.Credentials = New System.Net.NetworkCredential("sellsmtp", "smtpking")

			string smtpServer = System.Configuration.ConfigurationManager.AppSettings["smtpServer"];
			if (string.IsNullOrEmpty(smtpServer))
			{
				smtpServer = "mail.selltis.com";
			}
			else
			{
				smtpServer = smtpServer.ToString();
			}

			string smtpPort = System.Configuration.ConfigurationManager.AppSettings["smtpPort"];
			if (string.IsNullOrEmpty(smtpPort))
			{
				smtpPort = "587";
			}
			else
			{
				smtpPort = Convert.ToInt32(smtpPort).ToString();
			}

			string smtpUser = System.Configuration.ConfigurationManager.AppSettings["smtpUser"];
			if (string.IsNullOrEmpty(smtpUser))
			{
				smtpUser = "sellsmtp";
			}
			else
			{
				smtpUser = smtpUser.ToString();
			}

			string smtpPassword = System.Configuration.ConfigurationManager.AppSettings["smtpPassword"];
			if (string.IsNullOrEmpty(smtpPassword))
			{
				smtpPassword = "smtpking";
			}
			else
			{
				smtpPassword = smtpPassword.ToString();
			}

			//Set mail server
			client.Host = smtpServer;
			client.Port = Convert.ToInt32(smtpPort);
			client.Credentials = new System.Net.NetworkCredential(smtpUser, smtpPassword);
			client.EnableSsl = true;

			client.Send(message);
			bResult = true;

			//Catch ex As Exception
			//    If ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetWarning(45105, sProc, ex.ToString())
			//    End If
			//End Try

			//Clean up temporary attachments unless there was an error above
			if (bDeleteFiles)
			{
				if (bResult)
				{
					// Try
					for (f = 0; f <= sAttach.GetUpperBound(0); f++)
					{
						if (sAttach[f] != "")
						{
							Microsoft.VisualBasic.FileIO.FileSystem.DeleteFile(sAttach[f], Microsoft.VisualBasic.FileIO.UIOption.OnlyErrorDialogs, Microsoft.VisualBasic.FileIO.RecycleOption.DeletePermanently);
						}
					}

					if (sHTMLURI != "")
					{
						Microsoft.VisualBasic.FileIO.FileSystem.DeleteFile(sHTMLURI, Microsoft.VisualBasic.FileIO.UIOption.OnlyErrorDialogs, Microsoft.VisualBasic.FileIO.RecycleOption.DeletePermanently);
					}
					//Catch ex As Exception
					//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
					//        goErr.SetError(ex, 45105, sProc)
					//    End If
					//End Try
				}
			}

			return bResult;

		}

		public bool SendSMTPEmail_With_Attachment(string par_sSubject, string par_sBody, string par_sTo, string par_sCC, string par_sBCC, string par_sUnused, string par_ReplyName = "", string par_ReplyAddress = "", string sAttachments = "", bool bHTML = false, string sHTMLURI = "", string sHTMLURIBase = "", bool bDeleteFiles = true, bool bAppendTextFromWOP = true)
		{

			string sProc = "clEmail::SendSMTPEmailNew";
			SmtpClient client = new SmtpClient();
			MailMessage message = new MailMessage();
			string[] sAttach = new string[1];
			string[] sTo = new string[1];
			string[] sCC = new string[1];
			string[] sBC = new string[1];
			int f = 0;
			bool bResult = true;

			try
			{

				//Add TOs
				par_sTo = goTR.Replace(par_sTo, ",", ";");
				if (par_sTo != "")
				{
					sTo = par_sTo.Split(';');
					for (f = 0; f <= sTo.GetUpperBound(0); f++)
					{
						if (sTo[f].Trim(' ') != "")
						{
							message.To.Add(sTo[f].Trim(' '));
						}
					}
				}

				//Add CCs
				par_sCC = goTR.Replace(par_sCC, ",", ";");
				if (par_sCC != "")
				{
					sCC = par_sCC.Split(';');
					for (f = 0; f <= sCC.GetUpperBound(0); f++)
					{
						if (sCC[f].Trim(' ') != "")
						{
							message.CC.Add(sCC[f].Trim(' '));
						}
					}
				}

				//Add BCCs
				par_sBCC = goTR.Replace(par_sBCC, ",", ";");
				if (par_sBCC != "")
				{
					sBC = par_sBCC.Split(';');
					for (f = 0; f <= sBC.GetUpperBound(0); f++)
					{
						if (sBC[f].Trim(' ') != "")
						{
							message.Bcc.Add(sBC[f].Trim(' '));
						}
					}
				}

				//Specify the sender
				//msgObj.From.Email = par_ReplyAddress
				//msgObj.From.Name = par_ReplyName    

				//21092018 tickt #2463: Sending activty reports is not working..J
				//This is not wokrking on production due to display name format..J  
				string strDisplayName = par_ReplyName;
				par_ReplyName = System.Configuration.ConfigurationManager.AppSettings["smtpSender"];
				if (!string.IsNullOrEmpty(par_ReplyName))
				{
					//message.From = New MailAddress(par_ReplyAddress, strDisplayName)
					message.From = new MailAddress(par_ReplyName, strDisplayName);
				}

				if (!string.IsNullOrEmpty(par_ReplyAddress))
				{
					message.ReplyToList.Add(par_ReplyAddress);
				}

				message.Priority = MailPriority.Normal;

				message.Subject = par_sSubject;

				//Set message body

				if (bAppendTextFromWOP) //2/1/12 MI Added testing this to be able to send an email without the WOP text appended
				{
					par_sBody = par_sBody + "\r\n" + "\r\n" + goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "EMAILAPPEND", "", false);
				}

				if (bHTML == false)
				{
					message.Body = par_sBody;
					message.IsBodyHtml = false;
				}
				else
				{
					//If sHTMLURI <> "" Then
					//    msgObj.ImportFromURI(sHTMLURI, sHTMLURIBase, ImportURIFlags.EmbedImages)
					//Else
					//    msgObj.BodyParts.Add(par_sBody, BodyPartFormat.HTML)
					//End If
					message.Body = par_sBody;
					message.IsBodyHtml = true;
				}

				//Add attachment

				//'sViewName + "|" + sFieldName + "|" + sFileName + "|" + sGid_id
				if (sAttachments != "")
				{
					string[] sFiles = sAttachments.Split('|');
					string sViewName = sFiles[0];
					string sFieldName = sFiles[1];
					string sFileName = sFiles[2];
					string sGid_id = sFiles[3];

					System.Net.Mime.ContentType ct = new System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Application.Pdf);
					var _fileArray = clAzureFileStorage.GetFile(sViewName, sGid_id, sFieldName, sFileName);
					System.Net.Mail.Attachment attachment = new System.Net.Mail.Attachment(new MemoryStream(_fileArray), ct);
					attachment.ContentDisposition.FileName = sFileName;
					message.Attachments.Add(attachment);
				}

				//If sAttachment IsNot Nothing Then
				//    Dim ct As System.Net.Mime.ContentType
				//    'If sAttachmentType.ToUpper = "PDF" Then
				//    ct = New System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Application.Pdf)
				//    'ElseIf sAttachmentType.ToUpper = "IMAGE" Then
				//    '    ct = New System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Image.Jpeg)
				//    'End If
				//    Dim attachment As System.Net.Mail.Attachment = New System.Net.Mail.Attachment(sAttachment, ct)
				//    attachment.ContentDisposition.FileName = par_sUnused
				//    message.Attachments.Add(attachment)
				//End If
				//If sFiles <> "" Then
				//    sAttach = Split(sFiles, vbCrLf)
				//    For f = 0 To sAttach.GetUpperBound(0)
				//        If sAttach(f) <> "" Then
				//            'msgObj.Attachments.Add(sAttach(f))
				//            Dim attachment As System.Net.Mail.Attachment = New System.Net.Mail.Attachment(sAttach(f), System.Net.Mime.MediaTypeNames.Application.Octet)
				//            message.Attachments.Add(attachment)

				//        End If
				//    Next
				//End If

				//Set mail server
				//client.Host = "mail.selltis.com"
				//client.Port = 587
				//client.Credentials = New System.Net.NetworkCredential("sellsmtp", "smtpking")

				string smtpServer = System.Configuration.ConfigurationManager.AppSettings["smtpServer"];
				if (string.IsNullOrEmpty(smtpServer))
				{
					smtpServer = "mail.selltis.com";
				}
				else
				{
					smtpServer = smtpServer.ToString();
				}

				string smtpPort = System.Configuration.ConfigurationManager.AppSettings["smtpPort"];
				if (string.IsNullOrEmpty(smtpPort))
				{
					smtpPort = "587";
				}
				else
				{
					smtpPort = Convert.ToInt32(smtpPort).ToString();
				}

				string smtpUser = System.Configuration.ConfigurationManager.AppSettings["smtpUser"];
				if (string.IsNullOrEmpty(smtpUser))
				{
					smtpUser = "sellsmtp";
				}
				else
				{
					smtpUser = smtpUser.ToString();
				}

				string smtpPassword = System.Configuration.ConfigurationManager.AppSettings["smtpPassword"];
				if (string.IsNullOrEmpty(smtpPassword))
				{
					smtpPassword = "smtpking";
				}
				else
				{
					smtpPassword = smtpPassword.ToString();
				}

				//Set mail server
				client.Host = smtpServer;
				client.Port = Convert.ToInt32(smtpPort);
				client.Credentials = new System.Net.NetworkCredential(smtpUser, smtpPassword);
				client.EnableSsl = true;

				client.Send(message);

				bResult = true;

			}
			catch (Exception ex)
			{
				if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
				{
					goErr.SetWarning(45105, sProc, ex.ToString());
				}
			}

			//Clean up temporary attachments unless there was an error above
			//If bDeleteFiles Then
			//    If bResult Then
			//        ' Try
			//        For f = 0 To sAttach.GetUpperBound(0)
			//            If sAttach(f) <> "" Then
			//                My.Computer.FileSystem.DeleteFile(sAttach(f), FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
			//            End If
			//        Next

			//        If sHTMLURI <> "" Then
			//            My.Computer.FileSystem.DeleteFile(sHTMLURI, FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
			//        End If
			//        'Catch ex As Exception
			//        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        '        goErr.SetError(ex, 45105, sProc)
			//        '    End If
			//        'End Try
			//    End If
			//End If

			return bResult;

		}
		public bool SendSMTPEmail_With_LocalFile_As_Attachment(string par_sSubject, string par_sBody, string par_sTo, string par_sCC, string par_sBCC, string par_sUnused, string par_ReplyName = "", string par_ReplyAddress = "", string sAttachments = "", bool bHTML = false, string sHTMLURI = "", string sHTMLURIBase = "", bool bDeleteFiles = true, bool bAppendTextFromWOP = true, string sFileName = "")
		{

			string sProc = "clEmail::SendSMTPEmailNew";
			SmtpClient client = new SmtpClient();
			MailMessage message = new MailMessage();
			string[] sAttach = new string[1];
			string[] sTo = new string[1];
			string[] sCC = new string[1];
			string[] sBC = new string[1];
			int f = 0;
			bool bResult = true;

			try
			{

				//Add TOs
				par_sTo = goTR.Replace(par_sTo, ",", ";");
				if (par_sTo != "")
				{
					sTo = par_sTo.Split(';');
					for (f = 0; f <= sTo.GetUpperBound(0); f++)
					{
						if (sTo[f].Trim(' ') != "")
						{
							message.To.Add(sTo[f].Trim(' '));
						}
					}
				}

				//Add CCs
				par_sCC = goTR.Replace(par_sCC, ",", ";");
				if (par_sCC != "")
				{
					sCC = par_sCC.Split(';');
					for (f = 0; f <= sCC.GetUpperBound(0); f++)
					{
						if (sCC[f].Trim(' ') != "")
						{
							message.CC.Add(sCC[f].Trim(' '));
						}
					}
				}

				//Add BCCs
				par_sBCC = goTR.Replace(par_sBCC, ",", ";");
				if (par_sBCC != "")
				{
					sBC = par_sBCC.Split(';');
					for (f = 0; f <= sBC.GetUpperBound(0); f++)
					{
						if (sBC[f].Trim(' ') != "")
						{
							message.Bcc.Add(sBC[f].Trim(' '));
						}
					}
				}

				//Specify the sender
				//msgObj.From.Email = par_ReplyAddress
				//msgObj.From.Name = par_ReplyName    

				//21092018 tickt #2463: Sending activty reports is not working..J
				//This is not wokrking on production due to display name format..J  
				string strDisplayName = par_ReplyName;
				par_ReplyName = System.Configuration.ConfigurationManager.AppSettings["smtpSender"];
				if (!string.IsNullOrEmpty(par_ReplyName))
				{
					//message.From = New MailAddress(par_ReplyAddress, strDisplayName)
					message.From = new MailAddress(par_ReplyName, strDisplayName);
				}

				if (!string.IsNullOrEmpty(par_ReplyAddress))
				{
					message.ReplyToList.Add(par_ReplyAddress);
				}

				message.Priority = MailPriority.Normal;

				message.Subject = par_sSubject;

				//Set message body

				if (bAppendTextFromWOP) //2/1/12 MI Added testing this to be able to send an email without the WOP text appended
				{
					par_sBody = par_sBody + "\r\n" + "\r\n" + goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "EMAILAPPEND", "", false);
				}

				if (bHTML == false)
				{
					message.Body = par_sBody;
					message.IsBodyHtml = false;
				}
				else
				{
					//If sHTMLURI <> "" Then
					//    msgObj.ImportFromURI(sHTMLURI, sHTMLURIBase, ImportURIFlags.EmbedImages)
					//Else
					//    msgObj.BodyParts.Add(par_sBody, BodyPartFormat.HTML)
					//End If
					message.Body = par_sBody;
					message.IsBodyHtml = true;
				}

				//Add attachment

				//'sViewName + "|" + sFieldName + "|" + sFileName + "|" + sGid_id
				if (sAttachments != "")
				{
					//Dim sFiles = Split(sAttachments, "|")
					//Dim sViewName As String = sFiles.GetValue(0)
					//Dim sFieldName As String = sFiles.GetValue(1)
					//Dim sFileName As String = sFiles.GetValue(2)
					//Dim sGid_id As String = sFiles.GetValue(3)

					// Dim ct As System.Net.Mime.ContentType = New System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Application.Pdf)
					// Dim _fileArray = clAzureFileStorage.GetFile(sViewName, sGid_id, sFieldName, sFileName)
					System.Net.Mail.Attachment attachment = new System.Net.Mail.Attachment(sAttachments);
					attachment.ContentDisposition.FileName = sFileName;
					message.Attachments.Add(attachment);
				}

				//If sAttachment IsNot Nothing Then
				//    Dim ct As System.Net.Mime.ContentType
				//    'If sAttachmentType.ToUpper = "PDF" Then
				//    ct = New System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Application.Pdf)
				//    'ElseIf sAttachmentType.ToUpper = "IMAGE" Then
				//    '    ct = New System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Image.Jpeg)
				//    'End If
				//    Dim attachment As System.Net.Mail.Attachment = New System.Net.Mail.Attachment(sAttachment, ct)
				//    attachment.ContentDisposition.FileName = par_sUnused
				//    message.Attachments.Add(attachment)
				//End If
				//If sFiles <> "" Then
				//    sAttach = Split(sFiles, vbCrLf)
				//    For f = 0 To sAttach.GetUpperBound(0)
				//        If sAttach(f) <> "" Then
				//            'msgObj.Attachments.Add(sAttach(f))
				//            Dim attachment As System.Net.Mail.Attachment = New System.Net.Mail.Attachment(sAttach(f), System.Net.Mime.MediaTypeNames.Application.Octet)
				//            message.Attachments.Add(attachment)

				//        End If
				//    Next
				//End If

				//Set mail server
				//client.Host = "mail.selltis.com"
				//client.Port = 587
				//client.Credentials = New System.Net.NetworkCredential("sellsmtp", "smtpking")

				string smtpServer = System.Configuration.ConfigurationManager.AppSettings["smtpServer"];
				if (string.IsNullOrEmpty(smtpServer))
				{
					smtpServer = "mail.selltis.com";
				}
				else
				{
					smtpServer = smtpServer.ToString();
				}

				string smtpPort = System.Configuration.ConfigurationManager.AppSettings["smtpPort"];
				if (string.IsNullOrEmpty(smtpPort))
				{
					smtpPort = "587";
				}
				else
				{
					smtpPort = Convert.ToInt32(smtpPort).ToString();
				}

				string smtpUser = System.Configuration.ConfigurationManager.AppSettings["smtpUser"];
				if (string.IsNullOrEmpty(smtpUser))
				{
					smtpUser = "sellsmtp";
				}
				else
				{
					smtpUser = smtpUser.ToString();
				}

				string smtpPassword = System.Configuration.ConfigurationManager.AppSettings["smtpPassword"];
				if (string.IsNullOrEmpty(smtpPassword))
				{
					smtpPassword = "smtpking";
				}
				else
				{
					smtpPassword = smtpPassword.ToString();
				}

				//Set mail server
				client.Host = smtpServer;
				client.Port = Convert.ToInt32(smtpPort);
				client.Credentials = new System.Net.NetworkCredential(smtpUser, smtpPassword);
				client.EnableSsl = true;

				client.Send(message);

				bResult = true;

			}
			catch (Exception ex)
			{
				if (ex.Message != clC.EX_THREAD_ABORT_MESSAGE)
				{
					goErr.SetWarning(45105, sProc, ex.ToString());
				}
			}

			//Clean up temporary attachments unless there was an error above
			//If bDeleteFiles Then
			//    If bResult Then
			//        ' Try
			//        For f = 0 To sAttach.GetUpperBound(0)
			//            If sAttach(f) <> "" Then
			//                My.Computer.FileSystem.DeleteFile(sAttach(f), FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
			//            End If
			//        Next

			//        If sHTMLURI <> "" Then
			//            My.Computer.FileSystem.DeleteFile(sHTMLURI, FileIO.UIOption.OnlyErrorDialogs, FileIO.RecycleOption.DeletePermanently)
			//        End If
			//        'Catch ex As Exception
			//        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        '        goErr.SetError(ex, 45105, sProc)
			//        '    End If
			//        'End Try
			//    End If
			//End If

			return bResult;

		}


		public bool SendSMTPEmailNewCustom(string par_sSubject, string par_sBody, string par_sTo, string par_sCC, string par_sBCC, string par_sUnused, string par_ReplyName = "", string par_ReplyAddress = "", string sFiles = "", bool bHTML = false, string sHTMLURI = "", string sHTMLURIBase = "", bool bDeleteFiles = true, bool bAppendTextFromWOP = true)
		{

			string sProc = "clEmail::SendSMTPEmailNew";
			SmtpClient client = new SmtpClient();
			MailMessage message = new MailMessage();
			string[] sAttach = new string[1];
			string[] sTo = new string[1];
			string[] sCC = new string[1];
			string[] sBC = new string[1];
			int f = 0;
			bool bResult = true;

			try
			{

				//Add TOs
				par_sTo = goTR.Replace(par_sTo, ",", ";");
				if (par_sTo != "")
				{
					sTo = par_sTo.Split(';');
					for (f = 0; f <= sTo.GetUpperBound(0); f++)
					{
						if (sTo[f].Trim(' ') != "")
						{
							message.To.Add(sTo[f].Trim(' '));
						}
					}
				}

				//Add CCs
				par_sCC = goTR.Replace(par_sCC, ",", ";");
				if (par_sCC != "")
				{
					sCC = par_sCC.Split(';');
					for (f = 0; f <= sCC.GetUpperBound(0); f++)
					{
						if (sCC[f].Trim(' ') != "")
						{
							message.CC.Add(sCC[f].Trim(' '));
						}
					}
				}

				//Add BCCs
				par_sBCC = goTR.Replace(par_sBCC, ",", ";");
				if (par_sBCC != "")
				{
					sBC = par_sBCC.Split(';');
					for (f = 0; f <= sBC.GetUpperBound(0); f++)
					{
						if (sBC[f].Trim(' ') != "")
						{
							message.Bcc.Add(sBC[f].Trim(' '));
						}
					}
				}

				//Specify the sender
				//msgObj.From.Email = par_ReplyAddress
				//msgObj.From.Name = par_ReplyName 

				//21092018 tickt #2463: Sending activty reports is not working..J
				//This is not wokrking on production due to display name format..J  
				string strDisplayName = par_ReplyName;
				par_ReplyName = System.Configuration.ConfigurationManager.AppSettings["smtpSender"];
				if (!string.IsNullOrEmpty(par_ReplyName))
				{
					//message.From = New MailAddress(par_ReplyName, par_ReplyAddress)
					message.From = new MailAddress(par_ReplyName, strDisplayName);
					message.ReplyToList.Add(par_ReplyAddress);
				}

				message.Priority = MailPriority.Normal;

				message.Subject = par_sSubject;

				//set image body
				ConvertBase64Images(ref par_sBody);

				//Set message body

				if (bAppendTextFromWOP) //2/1/12 MI Added testing this to be able to send an email without the WOP text appended
				{
					par_sBody = par_sBody + "\r\n" + "\r\n" + goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "EMAILAPPEND", "", false);
				}

				//02082018 tckt #2381: Need to remove spaces when activity send reports showing in Outlook..J
				par_sBody = par_sBody.Replace("</p><br />", "</p>");

				if (bHTML == false)
				{
					message.Body = par_sBody;
					message.IsBodyHtml = false;
				}
				else
				{
					//If sHTMLURI <> "" Then
					//    msgObj.ImportFromURI(sHTMLURI, sHTMLURIBase, ImportURIFlags.EmbedImages)
					//Else
					//    msgObj.BodyParts.Add(par_sBody, BodyPartFormat.HTML)
					//End If
					message.Body = par_sBody;
					message.IsBodyHtml = true;
				}

				//Add attachment
				if (sFiles != "")
				{
					sAttach = Microsoft.VisualBasic.Strings.Split(sFiles, "\r\n");
					for (f = 0; f <= sAttach.GetUpperBound(0); f++)
					{
						if (sAttach[f] != "")
						{
							//msgObj.Attachments.Add(sAttach(f))
							System.Net.Mail.Attachment attachment = new System.Net.Mail.Attachment(sAttach[f], System.Net.Mime.MediaTypeNames.Application.Octet);
							message.Attachments.Add(attachment);

						}
					}
				}

				//Set mail server
				//client.Host = "mail.selltis.com"
				//client.Port = 587
				//client.Credentials = New System.Net.NetworkCredential("sellsmtp", "smtpking")

				string smtpServer = System.Configuration.ConfigurationManager.AppSettings["smtpServer"];
				if (string.IsNullOrEmpty(smtpServer))
				{
					smtpServer = "mail.selltis.com";
				}
				else
				{
					smtpServer = smtpServer.ToString();
				}

				string smtpPort = System.Configuration.ConfigurationManager.AppSettings["smtpPort"];
				if (string.IsNullOrEmpty(smtpPort))
				{
					smtpPort = "587";
				}
				else
				{
					smtpPort = Convert.ToInt32(smtpPort).ToString();
				}

				string smtpUser = System.Configuration.ConfigurationManager.AppSettings["smtpUser"];
				if (string.IsNullOrEmpty(smtpUser))
				{
					smtpUser = "sellsmtp";
				}
				else
				{
					smtpUser = smtpUser.ToString();
				}

				string smtpPassword = System.Configuration.ConfigurationManager.AppSettings["smtpPassword"];
				if (string.IsNullOrEmpty(smtpPassword))
				{
					smtpPassword = "smtpking";
				}
				else
				{
					smtpPassword = smtpPassword.ToString();
				}

				//Set mail server
				client.Host = smtpServer;
				client.Port = Convert.ToInt32(smtpPort);
				client.Credentials = new System.Net.NetworkCredential(smtpUser, smtpPassword);
				client.EnableSsl = true;

				client.Send(message);
				bResult = true;
				//'goLog.Log("Send Activity Report", "Call report was sent successfully From " & message.From.ToString, 0, True)
				goLog.Log("Send Activity Report", par_sSubject + " has been sent successfully From " + message.From.ToString() + ", To : " + par_sTo + ";" + par_sCC + ";" + par_sBCC, 0, true);

			}
			catch (Exception ex)
			{
				//If ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetWarning(45105, sProc, ex.ToString())
				//End If

				//19092018 tickt #2463:Customer is saying that SOMETIMES  when they use the Send AC Report button, it does not send. They have to go back into the record and do it again. Is there anything we can check here?..J
				bResult = false;
				goLog.Log("Send Activity Report", par_sSubject + " sent failed due to the error - " + ex.Message, 0, true);
			}

			//Clean up temporary attachments unless there was an error above
			if (bDeleteFiles)
			{
				if (bResult)
				{
					//Try
					for (f = 0; f <= sAttach.GetUpperBound(0); f++)
					{
							if (sAttach[f] != "")
							{
								Microsoft.VisualBasic.FileIO.FileSystem.DeleteFile(sAttach[f], Microsoft.VisualBasic.FileIO.UIOption.OnlyErrorDialogs, Microsoft.VisualBasic.FileIO.RecycleOption.DeletePermanently);
							}
						}

						if (sHTMLURI != "")
						{
							Microsoft.VisualBasic.FileIO.FileSystem.DeleteFile(sHTMLURI, Microsoft.VisualBasic.FileIO.UIOption.OnlyErrorDialogs, Microsoft.VisualBasic.FileIO.RecycleOption.DeletePermanently);
						}
					//Catch ex As Exception
					//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
					//        goErr.SetError(ex, 45105, sProc)
					//    End If
					//End Try
				}
			}

			return bResult;

		}

		public string ConvertBase64Images(ref string strData)
		{

			ArrayList Ht = new ArrayList(); //keeps the physical image file names
			ArrayList Ht1 = new ArrayList(); //keeps the base64 Image string to check for the same images
			Dictionary<int, string> DHT = new Dictionary<int, string>();
			//wt 11/11/14
			//added support for html in word templates

			//Clipboard.SetData(System.Windows.Forms.DataFormats.Html, "<html><body>" & strData & "</body></html>")
			//objWord.Selection.PasteAndFormat(16)
			//objWord.Selection.PasteSpecial(, , , , 10)

			//strData = strData.Replace("<p>", "").Replace("</p>", "<br/>")

			//V_T 2/26/2015
			//MMR Change -- Replace the Base64 images with local image path

			string regexImgSrc = "<img[^>]*?src\\s*=\\s*[\"']?([^'\" >]+?)[ '\"][^>]*?>";
			MatchCollection matchesImgSrc = Regex.Matches(strData, regexImgSrc, RegexOptions.IgnoreCase | RegexOptions.Singleline);

			int indx = 0;

// INSTANT C# NOTE: There is no C# equivalent to VB's implicit 'once only' variable initialization within loops, so the following variable declaration has been placed prior to the loop:
			int iwidth = 0;
		int iheight = 0;
			for (indx = 0; indx < matchesImgSrc.Count; indx++)
			{

				string imgstrs = matchesImgSrc[indx].Value;

				string _sPath = System.Web.HttpContext.Current.Server.MapPath("../Temp"); //Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)
				_sPath = _sPath + "\\" + clSettings.GetHostName() + "\\OutLookImages\\";

				//Dim _sPath = "/PublicFiles/" + clSettings.GetSiteId() + "/OutLookImages/"

				bool exists = System.IO.Directory.Exists(_sPath);
				if (!exists)
				{
					System.IO.Directory.CreateDirectory(_sPath);
				}

	//			Dim iwidth, iheight As Integer
				iwidth = 0;
				iheight = 0;

				//Get the Image size section start
				Regex reHeight = new Regex("height:\\s\\d*", RegexOptions.IgnoreCase | RegexOptions.Singleline);
				Regex reWidth = new Regex("width:\\s\\d*", RegexOptions.IgnoreCase | RegexOptions.Singleline);

				Match mWidth = reWidth.Match(imgstrs);
				if (mWidth.Length > 8 && mWidth.Value.Contains(":") & mWidth.Value.Contains("width"))
				{
					int.TryParse(mWidth.Value.Split(':').GetValue(1).ToString().Trim(), out iwidth);
				}

				Match mHeight = reHeight.Match(imgstrs);
				if (mHeight.Length > 8 && mHeight.Value.Contains(":") & mHeight.Value.Contains("height"))
				{
					int.TryParse(mHeight.Value.Split(':').GetValue(1).ToString().Trim(), out iheight);
				}

				//Get the images size section end
				string _imageName = "OutookNotesImage" + GetTimeString() + indx.ToString() + ".jpg";
				_sPath = _sPath + _imageName;
				//Dim PBase64Imagestr As String = Regex.Match(imgstrs, "<img.+?src=[""'](.+?)[""'].+?>", RegexOptions.IgnoreCase).Groups(1).Value

				//29082018 tickt #2401: Images are not showed in outlook email for the fist time after AC logged from outlook..J
				string PBase64Imagestr = Regex.Match(imgstrs, "src\\s*=\\s*[\"'](.+?)[\"'].+?", RegexOptions.IgnoreCase).Groups[1].Value;

				if (PBase64Imagestr.Split(',').Length > 1)
				{
					string Base64Imagestr = PBase64Imagestr.Split(',')[1];
					//Dim Base64ImagestrEncoded = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(Base64Imagestr)) 'Convert.FromBase64String(Base64Imagestr)
					//Dim bytes = Convert.FromBase64String(Base64ImagestrEncoded)
					string b64 = Base64Imagestr.Replace(" ", "+");
					var bytes = Convert.FromBase64String(b64);
					using (var imageFile = new FileStream(_sPath, FileMode.Create))
					{
						imageFile.Write(bytes, 0, bytes.Length);
						imageFile.Flush();
					}

					//Resize Image if style specified in the image tag
					if (iwidth > 0 && iheight > 0)
					{
						Image _Timg = Image.FromFile(_sPath);
						Image.GetThumbnailImageAbort myCallback = new Image.GetThumbnailImageAbort(ThumbnailCallback);
						Image _ImgToSave = _Timg.GetThumbnailImage(iwidth, iheight, myCallback, IntPtr.Zero);
						_Timg.Dispose();
						if (File.Exists(_sPath))
						{
							File.Delete(_sPath);
						}
						_ImgToSave.Save(_sPath, System.Drawing.Imaging.ImageFormat.Jpeg);
					}
					//Resize done
					Dictionary<string, string> objmmr_letterAttach = new Dictionary<string, string>(); //Attach mmr_Later Image
					objmmr_letterAttach.Add(_imageName, _sPath);

					//checking for the same image 
					if (Ht1.Contains(imgstrs))
					{
// INSTANT C# NOTE: Commented this declaration since looping variables in 'foreach' loops are declared in the 'foreach' header in C#:
//						Dim pair As KeyValuePair(Of Integer, String)
						int indval = 0;
						foreach (KeyValuePair<int, string> pair in DHT)
						{
							if (pair.Value == imgstrs)
							{
								indval = pair.Key;
								goto end_of_for;
							}
						}
	end_of_for:
						Ht.Add(Ht[indval]);
					}
					else
					{
						Ht.Add(_imageName);
						Ht1.Add(imgstrs);
						DHT.Add(indx, imgstrs);
					}

					//_sPath = "~/Temp/" & clSettings.GetHostName() & "/OutLookImages/" & _imageName
					//If Directory.Exists("/PublicFiles/" + clSettings.GetSiteId() + "/OutLookImages/") Then
					//    Directory.CreateDirectory("/PublicFiles/" + clSettings.GetSiteId() + "/OutLookImages/")
					//End If
					string baseURL = HttpContext.Current.Request.Url.Scheme + "://" + HttpContext.Current.Request.Url.Authority;
					_sPath = baseURL + "/Temp/" + clSettings.GetHostName() + "/OutLookImages/" + _imageName;
					_imageName = "<img src='" + _sPath + "' />";

					strData = strData.ToString().Replace(imgstrs, _imageName);
				}
			}

			return "";

		}



		public string GetTimeString()
		{

			DateTime sTime = DateTime.Now;
			//GetLocalTime(sTime)

			return sTime.Year + PadLeadingZeros(sTime.Month.ToString(), 2) + PadLeadingZeros(sTime.Day.ToString(), 2) + PadLeadingZeros(sTime.Hour.ToString(), 2) + PadLeadingZeros(sTime.Minute.ToString(), 2) + PadLeadingZeros(sTime.Second.ToString(), 2) + PadLeadingZeros(sTime.Millisecond.ToString(), 3).Substring(0, 2);

		}


		public bool ThumbnailCallback()
		{
			return false;
		}

		public string PadLeadingZeros(string strForm, int intLen)
		{
				string tempPadLeadingZeros = null;

			if (strForm.Length < intLen)
			{
				while (strForm.Length != intLen)
				{
					strForm = "0" + strForm;
				}
				tempPadLeadingZeros = strForm;
			}
			else
			{
				tempPadLeadingZeros = strForm;
			}

			return tempPadLeadingZeros;
		}


	}

}
