﻿CREATE TABLE [dbo].[TD_RELATED_PF] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_TD_RELATED_PF_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [G<PERSON>_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_PF] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_RELATED_PF] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PF_CONNECTED_TD] FOREIGN KEY ([G<PERSON>_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_RELATED_PF] FOREIGN KEY ([GID_PF]) REFERENCES [dbo].[PF] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_RELATED_PF] NOCHECK CONSTRAINT [LNK_PF_CONNECTED_TD];


GO
ALTER TABLE [dbo].[TD_RELATED_PF] NOCHECK CONSTRAINT [LNK_TD_RELATED_PF];


GO
CREATE NONCLUSTERED INDEX [IX_TD_RELATED_PF]
    ON [dbo].[TD_RELATED_PF]([GID_TD] ASC, [GID_PF] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PF_CONNECTED_TD]
    ON [dbo].[TD_RELATED_PF]([GID_PF] ASC, [GID_TD] ASC);

