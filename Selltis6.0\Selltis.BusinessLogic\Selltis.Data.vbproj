﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{0718C809-A336-45B1-9029-253AF6E1FC66}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>Selltis.BusinessLogic</RootNamespace>
    <AssemblyName>Selltis.BusinessLogic</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>Selltis.BusinessLogic.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <UseVSHostingProcess>true</UseVSHostingProcess>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>Selltis.BusinessLogic.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DebugNew|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\DebugNew\</OutputPath>
    <DocumentationFile>Selltis.BusinessLogic.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DocumentFormat.OpenXml">
      <HintPath>..\Common\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="ephtmltopdf, Version=6.2.0.0, Culture=neutral, PublicKeyToken=5b5f377bc08a4d32, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\CommonDLLs\ephtmltopdf.dll</HintPath>
    </Reference>
    <Reference Include="Google.GData.Client">
      <HintPath>..\packages\CommonDLLs\Google.GData.Client.dll</HintPath>
    </Reference>
    <Reference Include="IMAP4.Net">
      <HintPath>..\packages\CommonDLLs\IMAP4.Net.dll</HintPath>
    </Reference>
    <Reference Include="ImapUtility">
      <HintPath>..\packages\CommonDLLs\ImapUtility.dll</HintPath>
    </Reference>
    <Reference Include="MailBee.NET">
      <HintPath>..\packages\CommonDLLs\MailBee.NET.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Configuration, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\CommonDLLs\Microsoft.WindowsAzure.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage, Version=4.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\CommonDLLs\Microsoft.WindowsAzure.Storage.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.StorageClient, Version=1.7.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\CommonDLLs\Microsoft.WindowsAzure.StorageClient.dll</HintPath>
    </Reference>
    <Reference Include="POP3.Net">
      <HintPath>..\packages\CommonDLLs\POP3.Net.dll</HintPath>
    </Reference>
    <Reference Include="PublicDomain">
      <HintPath>..\packages\CommonDLLs\PublicDomain.dll</HintPath>
    </Reference>
    <Reference Include="Quickwebsoft.Web.UI.WebControls.EventCalendar">
      <HintPath>..\packages\CommonDLLs\Quickwebsoft.Web.UI.WebControls.EventCalendar.dll</HintPath>
    </Reference>
    <Reference Include="SautinSoft.Document">
      <HintPath>..\Common\SautinSoft.Document.dll</HintPath>
    </Reference>
    <Reference Include="SMTP.Net">
      <HintPath>..\packages\CommonDLLs\SMTP.Net.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Mvc">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Telerik.Windows.Documents.Core">
      <HintPath>..\Common\telerik DLLs\Telerik.Windows.Documents.Core.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Documents.Fixed">
      <HintPath>..\Common\telerik DLLs\Telerik.Windows.Documents.Fixed.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Documents.Flow">
      <HintPath>..\Common\telerik DLLs\Telerik.Windows.Documents.Flow.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Documents.Flow.FormatProviders.Pdf">
      <HintPath>..\Common\telerik DLLs\Telerik.Windows.Documents.Flow.FormatProviders.Pdf.dll</HintPath>
    </Reference>
    <Reference Include="Telerik.Windows.Zip">
      <HintPath>..\Common\telerik DLLs\Telerik.Windows.Zip.dll</HintPath>
    </Reference>
    <Reference Include="UltimateAjax">
      <HintPath>..\packages\CommonDLLs\UltimateAjax.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="clArray.vb" />
    <Compile Include="clAttachments.vb" />
    <Compile Include="clAuto.vb" />
    <Compile Include="clAutomatorMenu.vb" />
    <Compile Include="clAzureFileStorage.vb" />
    <Compile Include="clC.vb" />
    <Compile Include="clData.vb" />
    <Compile Include="clDefaults.vb" />
    <Compile Include="clDiaAdmPerm.vb" />
    <Compile Include="clDiaFormPro.vb" />
    <Compile Include="clEmail.vb" />
    <Compile Include="clEmailAlerts.vb" />
    <Compile Include="clEncryption.vb" />
    <Compile Include="clError.vb" />
    <Compile Include="clFileBrowse.vb" />
    <Compile Include="clGoogleAuth.vb" />
    <Compile Include="clHistory.vb" />
    <Compile Include="clHistoryItem.vb" />
    <Compile Include="clImport.vb" />
    <Compile Include="clImportTasks.vb" />
    <Compile Include="clInfoMessage.vb" />
    <Compile Include="clInit.vb" />
    <Compile Include="clList.vb" />
    <Compile Include="clLog.vb" />
    <Compile Include="clLogic.vb" />
    <Compile Include="clMembershipData.vb" />
    <Compile Include="clMetaData.vb" />
    <Compile Include="clNumMask.vb" />
    <Compile Include="clPDF.vb" />
    <Compile Include="clPerm.vb" />
    <Compile Include="clProject.vb" />
    <Compile Include="clRowSet.vb" />
    <Compile Include="clSchema.vb" />
    <Compile Include="clScriptsRowSet.vb" />
    <Compile Include="clScrMngRowSet.vb" />
    <Compile Include="clSelltisMembershipProvider.vb" />
    <Compile Include="clSelltisMembershipProviderNew.vb" />
    <Compile Include="clSelltisMobileBase.vb" />
    <Compile Include="clSend.vb" />
    <Compile Include="clSettings.vb" />
    <Compile Include="clSQL.vb" />
    <Compile Include="clTable.vb" />
    <Compile Include="clTransform.vb" />
    <Compile Include="ClUI.vb" />
    <Compile Include="clUtil.vb" />
    <Compile Include="clVar.vb" />
    <Compile Include="clWebForm.vb" />
    <Compile Include="clWebMail.vb" />
    <Compile Include="clWorkareaMessage.vb" />
    <Compile Include="cus_clScriptsCustom.vb" />
    <Compile Include="cus_clScriptsRowSetCustom.vb" />
    <Compile Include="cus_clSelltisMobileCustom.vb" />
    <Compile Include="EfcOnlineApi.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="SelltisCache.vb" />
    <Compile Include="WordToPDF.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>