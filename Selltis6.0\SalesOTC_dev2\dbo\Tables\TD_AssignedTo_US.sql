﻿CREATE TABLE [dbo].[TD_AssignedTo_US] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_ToDo_AssignedTo_User_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [G<PERSON>_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_AssignedTo_US] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_TD_AssignedTo_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_Assigned_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_AssignedTo_US] NOCHECK CONSTRAINT [LNK_TD_AssignedTo_US];


GO
ALTER TABLE [dbo].[TD_AssignedTo_US] NOCHECK CONSTRAINT [LNK_US_Assigned_TD];


GO
CREATE CLUSTERED INDEX [IX_US_Assigned_TD]
    ON [dbo].[TD_AssignedTo_US]([GID_TD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_AssignedTo_US]
    ON [dbo].[TD_AssignedTo_US]([GID_US] ASC);

