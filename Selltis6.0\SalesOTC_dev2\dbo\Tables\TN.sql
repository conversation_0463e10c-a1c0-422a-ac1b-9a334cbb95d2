﻿CREATE TABLE [dbo].[TN] (
    [GID_ID]             UNIQUEIDENTIFIER CONSTRAINT [DF_TN_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'TN',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]             BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]           NVARCHAR (1000)  NULL,
    [DTT_CreationTime]   DATETIME         CONSTRAINT [DF_TN_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]       TINYINT          NULL,
    [GID_InternalID]     UNIQUEIDENTIFIER NOT NULL,
    [TXT_File]           VARCHAR (2)      NOT NULL,
    [TXT_ExternalID]     NVARCHAR (1000)  NULL,
    [TXT_ExternalSource] VARCHAR (10)     NULL,
    [TXT_ModBy]          VARCHAR (4)      NULL,
    [DTT_ModTime]        DATETIME         CONSTRAINT [DF_TN_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [SI__ShareState]     TINYINT          CONSTRAINT [DF_TN_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]   UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_TN] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_TN_CreationTime]
    ON [dbo].[TN]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_TN_BI__ID]
    ON [dbo].[TN]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TN_CreatedBy_US]
    ON [dbo].[TN]([GID_CreatedBy_US] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_TN_ExtIDSourceFile]
    ON [dbo].[TN]([TXT_ExternalID] ASC, [TXT_ExternalSource] ASC, [TXT_File] ASC, [GID_InternalID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TN_IntIDSource]
    ON [dbo].[TN]([GID_InternalID] ASC, [TXT_ExternalSource] ASC);

