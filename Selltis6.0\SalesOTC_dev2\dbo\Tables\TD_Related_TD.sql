﻿CREATE TABLE [dbo].[TD_Related_TD] (
    [GID_ID]  UNIQUEIDENTIFIER CONSTRAINT [DF_ToDo_Related_ToDo_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_TD]  UNIQUEIDENTIFIER NOT NULL,
    [GID_TD2] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_Related_TD] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_TD_Connected_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_Related_TD] FOREIGN KEY ([GID_TD2]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_Related_TD] NOCHECK CONSTRAINT [LNK_TD_Connected_TD];


GO
ALTER TABLE [dbo].[TD_Related_TD] NOCHECK CONSTRAINT [LNK_TD_Related_TD];


GO
CREATE CLUSTERED INDEX [IX_TD_Connected_TD]
    ON [dbo].[TD_Related_TD]([GID_TD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_Related_TD]
    ON [dbo].[TD_Related_TD]([GID_TD2] ASC);

