﻿using System;

//OWNER: MI

using System.Web;

namespace Selltis.BusinessLogic
{
	public class clNumMask
	{
		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clLog goLog;
		private clNumMask goNumMask;


		public string sEvents; //Liste des num�ros d'�v�nements
		//------------- Numeric -------------
		public string sDECIMALDIGIT;
		public string sDECIMALSYMBOL;
		public string sTHOUSANDSEP;
		public string sNEGATIVEBEFORE;
		public string sNEGATIVEAFTER;
		public bool bDISPLAYZERO;
		//------------ Currency -------------
		public int iCURRDECIMALDIGITS;
		public string sCURRENCYSYMBOL;
		public string sCURRDECIMALSYMBOL;
		public string sCURRTHOUSANDSEP;
		public string sCURRNEGATIVEBEFORE;
		public string sCURRNEGATIVEAFTER;
		public bool bCURRDISPLAYZERO;
		public int iCURRSYMBOLPLACEMENT;

		public clNumMask()
		{

		}
		public string GetCurrFormat(string par_sFormat = "")
		{
			//MI 3/20/08 Added support for | as delimiter in format.
			//MI 7/12/06 Enabled.
			//CS Need to test

			//Return 8 values, separated by TAB

			//** LOG ****
			string sMess = "Entrance";
			string sProc = "clNumMask::GetCurrFormat()";
			//if gbWriteLog then oLog is clLogObj(sProc,sMess,SELL_LOGLEVEL_DETAILS)
			//** LOG ****

			//JL
			// *********** NEW CODE *******************
			//Dim iJ As Integer
			string sResult = null;
			string sVal = null;
			string sFormat = par_sFormat;

			sFormat = goTR.Replace(sFormat, "|", "\t");

			sResult = goTR.ExtractString(sFormat, 1);
			if (NumericHelper.Val(sResult) == 0)
			{
				sResult = iCURRDECIMALDIGITS.ToString();
			}

			sVal = goTR.ExtractString(sFormat, 2);
			if (sVal.ToUpper() == "DEF" || sVal[0] == clC.EOT)
			{
				sVal = sCURRDECIMALSYMBOL;
			}
			sResult += "\t" + sVal;

			sVal = goTR.ExtractString(sFormat, 3);
			if (sVal.ToUpper() == "DEF" || sVal[0] == clC.EOT)
			{
				sVal = sCURRTHOUSANDSEP;
			}
			sResult += "\t" + sVal;

			sVal = goTR.ExtractString(sFormat, 4);
			if (sVal.ToUpper() == "DEF" || sVal[0] == clC.EOT)
			{
				sVal = sCURRNEGATIVEBEFORE;
			}
			sResult += "\t" + sVal;

			sVal = goTR.ExtractString(sFormat, 5);
			if (sVal.ToUpper() == "DEF" || sVal[0] == clC.EOT)
			{
				sVal = sCURRNEGATIVEAFTER;
			}
			sResult += "\t" + sVal;

			sVal = goTR.ExtractString(sFormat, 6);
			if (sVal.ToUpper() == "DEF" || sVal[0] == clC.EOT)
			{
				sVal = Convert.ToInt16(bCURRDISPLAYZERO).ToString();
			}
			sResult += "\t" + sVal;

			sVal = goTR.ExtractString(sFormat, 7);
			if (sVal.ToUpper() == "DEF" || sVal[0] == clC.EOT)
			{
				sVal = sCURRENCYSYMBOL;
			}
			sResult += "\t" + sVal;

			sVal = goTR.ExtractString(sFormat, 8);
			if (sVal.ToUpper() == "DEF" || sVal[0] == clC.EOT)
			{
				sVal = iCURRSYMBOLPLACEMENT.ToString();
			}
			sResult += "\t" + sVal;

			return sResult;

			// *********** OLD CODE *******************
			//result		:iCURRDECIMALDIGITS & tab & ...
			//			:sCURRENCYSYMBOL & tab & ...
			//			:sCURRDECIMALSYMBOL & tab & ...
			//			:sCURRTHOUSANDSEP & tab & ...
			//			:sCURRNEGATIVEBEFORE & tab & ...
			//			:sCURRNEGATIVEAFTER & tab & ...
			//			:bCURRDISPLAYZERO & tab & ...
			//			:iCURRSYMBOLPLACEMENT
			// ****************************************
		}
		public string GetNumFormat(string par_sFormat = "")
		{
			//MI 3/20/08 Added support for | as delimiter in format.
			//MI 7/12/06 Testing. Done.

			//Return 6 values, separated by TAB

			//** LOG ****
			string sMess = "Entrance";
			string sProc = "clNumMask::GetNumFormat()";
			//if gbWriteLog then oLog is clLogObj(sProc,sMess,SELL_LOGLEVEL_DETAILS)
			//** LOG ****

			//JL
			// *********** NEW CODE *******************
			//Dim iJ As Integer
			string sResult = null;
			string sVal = null;
			string sFormat = par_sFormat;

			sFormat = goTR.Replace(sFormat, "|", "\t");

			sResult = goTR.ExtractString(sFormat, 1);
			if (sResult != "0" && NumericHelper.Val(sResult) == 0) //JL modified
			{
				sResult = sDECIMALDIGIT;
			}

			sVal = goTR.ExtractString(sFormat, 2);
			if (sVal.ToUpper() == "DEF" || sVal[0] == clC.EOT)
			{
				sVal = sDECIMALSYMBOL;
			}
			sResult += "\t" + sVal;

			sVal = goTR.ExtractString(sFormat, 3);
			if (sVal.ToUpper() == "DEF" || sVal[0] == clC.EOT)
			{
				sVal = sTHOUSANDSEP;
			}
			sResult += "\t" + sVal;

			sVal = goTR.ExtractString(sFormat, 4);
			if (sVal.ToUpper() == "DEF" || sVal[0] == clC.EOT)
			{
				sVal = sNEGATIVEBEFORE;
			}
			sResult += "\t" + sVal;

			sVal = goTR.ExtractString(sFormat, 5);
			if (sVal.ToUpper() == "DEF" || sVal[0] == clC.EOT)
			{
				sVal = sNEGATIVEAFTER;
			}
			sResult += "\t" + sVal;

			sVal = goTR.ExtractString(sFormat, 6);
			if (sVal.ToUpper() == "DEF" || sVal[0] == clC.EOT)
			{
				sVal = Convert.ToInt16(bDISPLAYZERO).ToString();
			}
			sResult += "\t" + sVal;

			return sResult;

			// *********** OLD CODE *******************
			//result		:sDECIMALDIGIT + tab + ...
			//			:sDECIMALSYMBOL + tab + ...
			//			:sTHOUSANDSEP + tab + ...
			//			:sNEGATIVEBEFORE + tab + ...
			//			:sNEGATIVEAFTER + tab + ...
			//			:bDISPLAYZERO
			// ****************************************
		}
		public void InitClass()
		{
			//MI 6/8/08 Changed currency default format negative symbols to dash before the number.
			//MI 7/12/06 Uncommented code, tested.

			//PURPOSE:
			//       Initialize numeric and currency format variables

			string sMemo = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "", true);

			//----------- Numeric -------------
			sDECIMALDIGIT = goTR.StrRead(sMemo, "EDT_DECIMALDIGITS", "-1"); //Doesn't exist in POP? 'MI 6/8/08
			sDECIMALSYMBOL = goTR.StrRead(sMemo, "EDT_DECIMALSYMBOL", ".");
			sTHOUSANDSEP = goTR.StrRead(sMemo, "EDT_THOUSANDSEP", ",");
			sNEGATIVEBEFORE = goTR.StrRead(sMemo, "EDT_NEGATIVEBEFORE", "-");
			sNEGATIVEAFTER = goTR.StrRead(sMemo, "EDT_NEGATIVEAFTER", "");
			bDISPLAYZERO = bool.TryParse(goTR?.StrRead(sMemo, "CHK_DISPLAYZERO", 0), out bool result) && result; 

			//------------ Currency -------------
			iCURRDECIMALDIGITS = Convert.ToInt32(goTR.StrRead(sMemo, "EDT_CURRDECIMALDIGITS", 2));
			sCURRDECIMALSYMBOL = goTR.StrRead(sMemo, "EDT_CURRDECIMALSYMBOL", ".");
			sCURRTHOUSANDSEP = goTR.StrRead(sMemo, "EDT_CURRTHOUSANDSEP", ",");
			sCURRNEGATIVEBEFORE = goTR.StrRead(sMemo, "EDT_CURRNEGATIVEBEFORE", "-"); //MI 6/8/08
			sCURRNEGATIVEAFTER = goTR.StrRead(sMemo, "EDT_CURRNEGATIVEAFTER", ""); //MI 6/8/08
			bCURRDISPLAYZERO =  goTR?.StrRead(sMemo, "CHK_CURRDISPLAYZERO", 0) == "1";
            sCURRENCYSYMBOL = goTR.StrRead(sMemo, "EDT_CURRENCYSYMBOL", "$");
			iCURRSYMBOLPLACEMENT = Convert.ToInt32(goTR.StrRead(sMemo, "SEL_CURRSYMBOLPLACEMENT", 1));


		}
		public void Initialize()
		{

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			goNumMask = (Selltis.BusinessLogic.clNumMask)HttpContext.Current.Session["goNumMask"];

		}
		public void OnExit()
		{
			//CS PORTED as placeholder only. This sets a field directly, will have to be redone
			//in that respect.

			//PURPOSE:
			//		The Numeric fields, Currency Dates or Time, will post values correctly formated,
			//		according to the personal international parameters, defined in:
			//		Tools>Personal options>Regional options > Change ...
			//		and this, whatever the seizure user format
			//		(F)Les champs Numeric, Currency Date ou Time, afficheront des valeurs correctement format�es,
			//		selon les param�tres internationaux personnels, d�finis dans :
			//		Tools>Personal options>Regional options > Change ...
			//		et ce, quel que soit le format de saisie de l'utilisateur

			//PARAMETERS:
			//		N/A

			//RETURNS:
			//		Values correctly formated to the edit field (or '?')
			//		Valeur correctement format�e dans le champ de saisie (ou '?')

			//HOW IT WORKS:
			//		This code is called automatically to the go out of the Numeric fields, Currency Dates or Time...
			//		Of all the windows of the project.  
			//		(F)Ce code est ex�cut� automatiquement � la sortie des champs Numeric, Currency Date ou Time ...
			//		de toutes les fen�tres du projet.

			//EXAMPLE:
			//		Input code of window (En entr�e de fen�tre):
			//			Event("clNumMask::OpenWindow",".*",WM_KILLFOCUS)	'0x0008
			//		If the writed user 20021026 in a type field 'Dates',...
			//		This one will post 10/26/2002 as soon as it will have lost the focus.  
			//		Si l'utilisateur ecrit 20021026 dans un champ de type 'Date', ...
			//		celui-ci affichera 10/26/2002 d�s qu'il aura perdu le focus.

			//** LOG ****
			string sMess = "Entrance";
			string sProc = "clNumMask::OnExit()";
			//if gbWriteLog then oLog is clLogObj(sProc,sMess,SELL_LOGLEVEL_DETAILS)
			//** LOG ****

			//CS: Commented out from here to end.

			//iJ			is int
			//iK			is int
			//bNegative	is boolean
			//sEnt		is string
			//sDec		is string
			//bFalse		is boolean
			//sPrfx		is string
			//sField		is string=CurrentWin()+"."+_Eve.Name
			//sText		is string
			//sResult		is string

			//if CurrentWin()~="" then return
			//            sMess = ""
			//            If occurrence(sField) Then
			//	if {CurrentWin()+"."+_Eve.Name}..Type <> typTEXTE then
			//                    SMess = sField + " is not text type"
			//                    End
			//                Else
			//                    SMess = sField + " not exist"
			//                    End

			//                    If sMess <> "" Then
			//                        '	goMsgBox:Error(sMess)
			//goLog:                  Log(sProc, sMess, SELL_LOGLEVEL_STANDARD)
			//                        Return
			//                        End
			//sText=NoSpace({sField})
			//if sText~="?" or sText="" then return
			//                            ':bNumeric=false

			//sPrfx = Upper(_Eve.Name[[to 3]])
			//                            Switch(sPrfx)
			//	case "SI_", "INT", "LI_", "SR_", "DR_"
			//		if goTr:IsNum(sText) then
			//                                If sPrfx = "SR_" Or sPrfx = "DR_" Then
			//				{sField}=goTr:NumToString(goTr:StringToNum(sText))
			//                                Else
			//				{sField}=goTr:NumToString(arrondi(goTr:StringToNum(sText)))
			//                                    End
			//		else
			//                                    sMess = sText + ": it's not a numeric"
			//goLog:                              Log(sProc, sMess, SELL_LOGLEVEL_STANDARD)
			//                                    bFalse = True
			//                                    End
			//	case "CUR"
			//		if goTr:IsCurr(sText) then
			//			{sField}=goTr:CurrToString(goTr:StringToCurr(sText))
			//                                    Else
			//                                        bFalse = True
			//                                        End
			//	case "DTE"
			//		if goTr:IsDate(sText) then
			//			{sField}=goTr:DateToString(goTr:Stringtodate(sText))
			//                                        Else
			//                                            bFalse = True
			//                                            End
			//	case "TME"
			//		if goTr:IsTime(sText) then
			//			{sField}=goTr:timeToString(goTr:StringToTime(sText))
			//                                            Else
			//                                                bFalse = True
			//                                                End
			//	other case : return
			//                                                End
			//                                                If bFalse Then
			//                                                    '	beep
			//	{sField}="?"
			//                                                    End
			//                                                    Exit Sub
		}
		public void OpenWindow()
		{
			//CS PORTED as placeholder

			//PURPOSE:
			//		Declare events 'On Exit' for the fields of the new window.

			//PARAMETERS:
			//		N/A

			//RETURNS:
			//		N/A

			//HOW IT WORKS:
			//		Has each window opening, the :sEvents string is completed (1 text line by open window)
			//		The :sEvents string is a body of text lines, separated by CR.
			//		Each line contains: <Window's name>+TAB+NEve1+TAB+NEve2+TAB+...+TAB+NEven+CR
			//		Neve1... are numbers of events: as much as of concerned fields, in the window.
			//		When the window will be closed, the :CloseWindow() method will withdraw this line of the :sEvents string
			//		(F)A chaque ouverture de fen�tre, la chaine :sEvents est compl�t�e (1 ligne texte par fen�tre ouverte)
			//		La chaine :sEvents est un ensemble de lignes texte, s�par�es par des RC.
			//		Chaque ligne contient : <Nom de la fen�tre>+TAB+NEve1+TAB+NEve2+TAB+...+TAB+NEven+RC
			//		Neve1 ... sont des num�ros d'�v�nements : autant que de champs concern�s, dans la fen�tre.
			//		Lorsque la fen�tre sera ferm�e, la m�thode :CloseWindow() retirera cette ligne de la chaine :sEvents

			//EXAMPLE:
			//		N/A This Procedure is called by global event to the project.  
			//		N/A (F)Cette Procedure est appel�e par �v�nement global au projet.

			//** LOG ****
			string sMess = "Entrance";
			string sProc = "clNumMask::OpenWindow()";
			//if gbWriteLog then oLog is clLogObj(sProc,sMess,SELL_LOGLEVEL_DETAILS)
			//** LOG ****

			//CS: Commented
			//goNumMask: OnExit()
		}
	}

}
