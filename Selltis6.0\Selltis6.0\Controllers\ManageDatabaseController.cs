﻿using Selltis.BusinessLogic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Selltis.Core;
using Selltis.MVC.Models;
using System.Data;
using Microsoft.VisualBasic;

namespace Selltis.MVC.Controllers
{
    public class ManageDatabaseController : Controller
    {
        private clData goData;
        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        public ActionResult ManageDatabase(string SearchValue = "")
        {
            if (Util.GetSessionValue("DesktopId") != null)
            {
                if (Util.GetSessionValue("DesktopId").ToString() != string.Empty)
                    ViewBag.SelectedDesktopID = Util.GetSessionValue("DesktopId");

                ViewBag.FolderId = Util.GetSessionValue("FolderID");
                ViewBag.HistoryKey = Util.GetSessionValue("LastDesktopHistoryKey");
            }
            ManageDatabaseModel _ManageDatabaseModel = new ManageDatabaseModel();
            _ManageDatabaseModel.TotalTables = LoadSelltisTables();

            if (string.IsNullOrEmpty(SearchValue) == false)
            {
                var list = new List<string>();
                if (SearchValue.Length == 1)
                {
                    list = _ManageDatabaseModel.TotalTables.FindAll(m => m.Substring(0, 1).Contains(SearchValue.ToUpper()));
                }
                else
                {
                    list = _ManageDatabaseModel.TotalTables.FindAll(m => m.Substring(0, 2).Contains(SearchValue.ToUpper()));
                }
                _ManageDatabaseModel.TotalTables = list;
            }
            return View(_ManageDatabaseModel);
        }
        private List<string> LoadSelltisTables(bool OnlyTables = false)
        {
            goData = (clData)Util.GetInstance("data");
            List<string> Tables = new List<string>();
            clArray oFiles = goData.GetFiles(false);

            for (int i = 1; i <= oFiles.GetDimension(); i++)
            {
                if (OnlyTables)
                    Tables.Add(oFiles.GetItem(i));
                else
                    Tables.Add(oFiles.GetItem(i) + "|" + goData.GetFileLabelFromName(oFiles.GetItem(i)));
            }
            return Tables;
        }

        public ActionResult AddOrEditFields(string TableName)
        {
            //try
            //{
                goP = (clProject)Util.GetInstance("p");
                goData = (clData)Util.GetInstance("data");
                TableFieldsModel _model = new TableFieldsModel();
                _model.FileName = TableName;
                _model.LabelName = goData.GetFileLabelFromName(TableName);
                _model._TableFields = LoadSelltisFields(TableName);
                _model._TableFields = _model._TableFields.OrderBy(m => m.Label).ToList();
                return View(_model);
            //}
            //catch (Exception ex)
            //{
            //    ErrorLog ErrorModel = new ErrorLog();
            //    ErrorModel.sErrorLog = ex.ToString() + Environment.NewLine + ex.StackTrace;

            //    if (goP == null)
            //        goP = (clProject)Util.GetInstance("p");

            //    if (goTR == null)
            //        goTR = (clTransform)Util.GetInstance("tr");

            //    string sFilter = "TXT_SessionID='<%sessionid%>'";
            //    string sText = "";
            //    int par_iValid = clC.SELL_TYPE_INVALID;
            //    string par_sDelim = "|";

            //    clRowSet rs = new clRowSet("XL", 3, sFilter, "BI__ID desc", "BI__ID, DTT_CreationTime", 1, "", "", "", "", "", false, false, true, false, -1, "", false, true);
            //    if (rs.Count() <= 0)
            //    {
            //        sText = goP.GetMe("LogonName") + ": log data not found " + goTR.DateTimeToSysString(DateTime.Now, ref par_iValid, ref par_sDelim);
            //    }
            //    else
            //    {
            //        sText = goP.GetMe("LogonName") + ": " + rs.GetFieldVal("BI__ID") + " " + goTR.DateTimeToSysString(Convert.ToDateTime(rs.GetFieldVal("DTT_CreationTime", 2)), ref par_iValid, ref par_sDelim);
            //    }
            //    ErrorModel.LBL_ErrorInfo = sText;

            //    return View("~/Views/ErrorLog/LogError.cshtml", ErrorModel);
            //}
            
        }

        private List<TableFields> LoadSelltisFields(string sFile)
        {
            //try
            //{
                //PURPOSE: Load Selltis fields into gvMap
                //CALLED FROM: cmbMap_SelectedIndexChanged, cmbFile_SelectedIndexChanged
                
                clArray aFields = goData.GetFields(sFile);
                clArray aLinks = goData.GetLinks(sFile);

                string sLabel = goData.GetFieldLabel(sFile, aLinks.GetItem(1));

                DataTable dtMap = new DataTable();
                string sColumns = "Label,Expression,SSType,Length,FieldName";
                string[] aColumns = Strings.Split(sColumns, ",");
                DataRow dr = default(DataRow);
                DataColumn dc = default(DataColumn);
                string sFieldName = null;
                string sPrefix = null;
                string sLength = null;

                for (int i = 0; i <= aColumns.GetUpperBound(0); i++)
                {
                    dc = new DataColumn();
                    dc.ColumnName = aColumns[i];
                    dtMap.Columns.Add(dc);
                }
                int par_iValid = 4;
                //Add fields to dt. strip out DTx parts not wanted
                for (int i = 1; i <= aFields.GetDimension(); i++)
                {
                    dr = dtMap.NewRow();
                    sFieldName = aFields.GetItem(i);
                    sPrefix = Strings.Left(sFieldName, 3);

                    switch (sPrefix)
                    {
                        case "DTD":
                        case "DTM":
                        case "DTY":
                        case "DTQ":
                        case "DTT":
                        case "ADV":
                            break;
                        //Do not add to list
                        default:
                            dr["FieldName"] = sFieldName;
                            dr["Label"] = goData.GetFieldLabelFromName(sFile, sFieldName, ref par_iValid);
                            dr["SSType"] = goData.GetFieldType(sFile, sFieldName);
                            sLength = goData.GetFieldSize(sFile, sFieldName);
                            if (sLength != "1073741823")
                            {
                                dr["Length"] = sLength;
                            }
                            dtMap.Rows.Add(dr);
                            dtMap.TableName = "Map";
                            break;
                    }
                }                
                //Add links to dt
                for (int i = 1; i <= aLinks.GetDimension(); i++)
                {
                    dr = dtMap.NewRow();
                    sFieldName = aLinks.GetItem(i);
                    dr["FieldName"] = sFieldName;
                    dr["Label"] = goData.GetFieldLabelFromName(sFile, sFieldName, ref par_iValid);
                    dr["SSType"] = "Link";
                    dr["Length"] = Strings.Split(goData.LK_GetType(sFile, sFieldName).ToString(), Constants.vbTab)[4];
                    dtMap.Rows.Add(dr);
                    dtMap.TableName = "Map";
                }           
                //Sort dt
                dtMap.DefaultView.Sort = "Label";

                //testing: set values for link drop-downs
                DataTable dtLinkField = new DataTable();
                DataColumn dcField = new DataColumn();
                dcField.ColumnName = "Field";
                dtLinkField.Columns.Add(dcField);
                for (int i = 0; i <= 5; i++)
                {
                    DataRow drField = dtLinkField.NewRow();
                    drField["Field"] = i;
                    dtLinkField.Rows.Add(drField);
                }

                //Scheminator: bind fields to listbox:
                //sort by label
                dtMap.DefaultView.Sort = "Label";

                List<TableFields> _TableFields = new List<TableFields>();

                for (int i = 0; i <= dtMap.DefaultView.Count - 1; i++)
                {
                    string Text = "";
                    if (!string.IsNullOrEmpty((dtMap.DefaultView[i]["Label"]).ToString()))
                    {
                        Text = dtMap.DefaultView[i]["Label"].ToString();
                    }
                    else
                    {
                        Text = dtMap.DefaultView[i]["FieldName"].ToString();
                    }
                    _TableFields.Add(new TableFields
                    {
                        Label = Text,
                        FieldName = dtMap.DefaultView[i]["FieldName"].ToString(),
                        FieldType = dtMap.DefaultView[i]["SSType"].ToString(),
                        Length = dtMap.DefaultView[i]["Length"].ToString()
                    });
                }              
                goP.SetVar("IMP_dtMap", dtMap);
                return _TableFields;
            //}
            //catch (Exception)
            //{
            //    throw;
            //}
            
        }
        public ActionResult AddFile()
        {
            return View();
        }
        public ActionResult AddOrEditSelectedField(string FileName, string FieldName = "Field")
        {
            goP = (clProject)Util.GetInstance("p");
            goData = (clData)Util.GetInstance("data");
            string FieldValue = "";
            FieldProperties _model = new FieldProperties();
            _model.FileName = FileName;
            if (FieldName != "Field")
            {
                _model.IsEdit = true;
                FieldValue = GetFieldProperties(FileName, FieldName);
                string[] aProperties = Strings.Split(FieldValue, "|");
                string Type = aProperties[2];
                _model.FieldType = Type.ToLower();
                int par_iValid = 4;
                switch (Type)
                {
                    case "Link":
                        _model.FieldName = FieldName;
                        _model.LinkType = goData.LKGetType(FileName, aProperties[4]);

                        _model.sLinkPri = GetLinkName(FieldName).ToString().ToUpper();
                        _model.sLinkRev = GetLinkName(goData.LKGetInverseName(FileName, aProperties[4])).ToString().ToUpper();
                        _model.sLinkLabel = goData.LK_GetLabel(FileName, aProperties[4], ref par_iValid).ToString().ToUpper();
                        _model.sLinkLabelRev = goData.LK_GetLabel(Strings.Right(FieldName, 2), goData.LKGetInverseName(FileName, aProperties[4]), ref par_iValid).ToString().ToUpper();
                        _model.sLinkDir = goData.LKGetDirection(FileName, aProperties[4]).ToString().ToString().ToUpper();
                        _model.sLinkToFile = Strings.Right(FieldName, 2);

                        if (_model.sLinkDir == "1")
                        {
                            _model.lblLinkDir = "This is a primary link";
                            _model.btnEditEnabled = true;
                        }
                        else
                        {
                            _model.lblLinkDir = "This is a reverse link";
                            _model.btnEditEnabled = false;
                        }

                        break;
                    default:
                        _model.FieldName = aProperties[4];
                        _model.LabelName = aProperties[0];
                        _model.Length = aProperties[3];
                        break;
                }
            }
            else
            {
                _model.FieldName = "";
                _model.LabelName = "";
                _model.Length = "";
                _model.FieldType = "";
            }
            return View(_model);
        }

        private string GetFieldProperties(string sFileName, string sFieldName)
        {

            string sReturn = "";

            //Get dtMap
            DataTable dtMap = (DataTable)goP.GetVar("IMP_dtMap");
            if ((dtMap == null))
                return "";

            //Filter for row with FieldName = xx
            dtMap.DefaultView.RowFilter = "FieldName='" + sFieldName + "'";

            //Return delimited string of properties
            if (dtMap.DefaultView.Count == 0)
                return "";

            foreach (DataColumn dc in dtMap.Columns)
            {
                sReturn += dtMap.DefaultView[0][dc.ColumnName] + "|";
            }
            if (Strings.Right(sReturn, 1) == "|")
                sReturn = Strings.Left(sReturn, Strings.Len(sReturn) - 1);

            return sReturn;

        }

        public JsonResult LoadSelltisTablesCombo()
        {
            List<string> _list = LoadSelltisTables(true);
            List<SelectListItem> oList = new List<SelectListItem>();
            foreach (string table in _list)
            {
                oList.Add(new SelectListItem
                {
                    Text = table,
                    Value = table
                });
            }
            return Json(oList, JsonRequestBehavior.AllowGet);
        }

        public string GetLinkName(string sLink)
        {
            sLink = Strings.Left(sLink, Strings.Len(sLink) - 3);
            sLink = Strings.Right(sLink, Strings.Len(sLink) - 4);

            return sLink;
        }

        public string AddFileIntoDB(string TableName, string TableLabel, string TableLabelPlural, bool CreateFileLabels, bool CreateLinks, bool CreateFieldLabels, bool CreateForm, bool CreateDesktop, bool CreatePermissions)
        {
            goData = (clData)Util.GetInstance("data");
            int iCreateFileLabels = 0, iCreateLinks = 0, iCreateFieldLabels = 0, iCreateForm = 0, iCreateDesktop = 0, iCreatePermissions = 0;
            List<string> _tables = LoadSelltisTables(true);
            List<string> _tablesWithLabels = LoadSelltisTables();
            var item = _tables.FindAll(m => m == TableName);
            var LabelItem = _tablesWithLabels.FindAll(m => m.ToUpper().Contains(TableLabel.ToUpper()));
           
            if (item.Count >= 1)
            {
                return "Table already exists";
            }
            else if (LabelItem.Count >= 1)
            {
                return "Table label already exists";
            }

            if (CreateFileLabels)
                iCreateFileLabels = 1;
            if (CreateLinks)
                iCreateLinks = 1;
            if (CreateFieldLabels)
                iCreateFieldLabels = 1;
            if (CreateForm)
                iCreateForm = 1;
            if (CreateDesktop)
                iCreateDesktop = 1;
            if (CreatePermissions)
                iCreatePermissions = 1;

            string SqlQuery = "exec pCreateTable @par_sTableName='" + TableName + "',@par_sTableLabel='" + TableLabel + "',@par_sTableLabelPlural='" + TableLabelPlural + "',@par_bCreateFileLabels=" + iCreateFileLabels.ToString() + ",@par_bCreateLinks=" + iCreateLinks.ToString() + ",@par_bCreateFieldLabels=" + iCreateFieldLabels.ToString() + ",@par_bCreateForm=" + iCreateForm.ToString() + ",@par_bCreateDesktop=" + iCreateDesktop.ToString() + ",@par_bCreatePermissions=" + iCreatePermissions.ToString() + ",@par_bVerbose=0";

            bool result = goData.RunSQLQuery(SqlQuery);

            if (result)
                return "success";
            else
                return "Table Creation Failed";
        }

        public string AddOrEditFieldSave(bool IsEdit, string OldFieldName, string NewFieldName, string FileName, string FieldType, string Length, string Label, string LinkType, string LinkToFile, string PrimaryLinkName, string PrimaryLinkLabel, string RevLinkName, string RevLinkLabel)
        {

            goMeta = (clMetaData)Util.GetInstance("meta");
            goData = (clData)Util.GetInstance("data");

            FieldProperties FieldInfo = new FieldProperties();
            FieldInfo.OldFieldName = OldFieldName;
            FieldInfo.NewFieldName = NewFieldName;
            FieldInfo.FileName = FileName;
            FieldInfo.FieldType = FieldType;
            FieldInfo.Length = Length;
            FieldInfo.LabelName = Label;
            FieldInfo.LinkType = LinkType;
            FieldInfo.sLinkToFile = LinkToFile;

            PrimaryLinkName = PrimaryLinkName.ToUpper().Replace("LNK_", "");
            RevLinkName = RevLinkName.ToUpper().Replace("LNK_", "");

            FieldInfo.sLinkPri = PrimaryLinkName;
            FieldInfo.sLinkLabel = PrimaryLinkLabel;
            FieldInfo.sLinkRev = RevLinkName;
            FieldInfo.sLinkLabelRev = RevLinkLabel;

            string result = "";
            
            if (IsEdit)
            {
                if (FieldInfo.NewFieldName != FieldInfo.OldFieldName)
                {
                    string DuplicatesResult = CheckForDuplicates(FieldInfo.FieldType, FieldInfo.FileName, FieldInfo.sLinkToFile, FieldInfo.NewFieldName, FieldInfo.sLinkPri, FieldInfo.sLinkRev);
                    
                    if(string.IsNullOrEmpty(DuplicatesResult))
                    {
                        result = EditFieldSave(FieldInfo);
                    }
                    else
                        return DuplicatesResult;
                }
                else
                    result = EditFieldSave(FieldInfo);
            }
            else
            {
                string DuplicatesResult = CheckForDuplicates(FieldInfo.FieldType, FieldInfo.FileName, FieldInfo.sLinkToFile, FieldInfo.NewFieldName, FieldInfo.sLinkPri, FieldInfo.sLinkRev);

                if (string.IsNullOrEmpty(DuplicatesResult))
                    result = AddFieldSave(FieldInfo);
                else
                    return DuplicatesResult;
            }
            return result;
        }

        private string CheckForDuplicates(string FieldType, string FileName, string ToFile, string NewFieldName, string PrimaryLinkName, string RevLinkName)
        {
            clArray _clArray;

            if (FieldType == "link")
            {
                //Primary link checking
                _clArray = new clArray();
                _clArray = goData.GetLinks(FileName);//TX

                for (int i = 1; i <= _clArray.GetDimension(); i++)
                {
                    string sListItemText = _clArray.GetItem(i);
                    if (sListItemText.ToUpper().Equals("LNK_" + PrimaryLinkName.ToUpper() + "_" + ToFile.ToUpper())) //Connected_AC
                    {
                        return "Duplicates|Link Name \"" + PrimaryLinkName.ToUpper() + "\" is already existed in the file \"" + FileName.ToUpper() + "\"."; ;
                    }
                }

                //Rev link checking
                _clArray = new clArray();
                _clArray = goData.GetLinks(ToFile);//AC

                for (int i = 1; i <= _clArray.GetDimension(); i++)
                {
                    string sListItemText = _clArray.GetItem(i);
                    if (sListItemText.ToUpper().Equals("LNK_" + RevLinkName.ToUpper() + "_" + FileName.ToUpper())) //Connected_TX
                    {
                        return "Duplicates|Reverse Link Name \"" + RevLinkName.ToUpper() + "\" is already existed in the file \"" + ToFile.ToUpper() + "\".";
                    }
                }
                return "";
            }
            else
            {
                _clArray = new clArray();
                _clArray = goData.GetFields(FileName);

                for (int i = 1; i <= _clArray.GetDimension(); i++)
                {
                    string sListItemText = _clArray.GetItem(i);
                    if (sListItemText.ToUpper().Equals(NewFieldName.ToUpper())) //Connected_TX
                    {
                        return "Duplicates|Field Name \"" + NewFieldName.ToUpper() + "\" is already existed in the file \"" + FileName.ToUpper() + "\".";
                    }
                }
                return "";
            }

        }
        private string EditFieldSave(FieldProperties FieldInfo)
        {
            //SQL
            //sp_RENAME 'TableName.[OldColumnName]' , '[NewColumnName]', 'COLUMN'
            string sSQL = "";
            if (FieldInfo.OldFieldName != FieldInfo.NewFieldName)
            {
                sSQL = "sp_RENAME '" + FieldInfo.FileName + "." + FieldInfo.OldFieldName + "' , '" + FieldInfo.NewFieldName + "', 'COLUMN'";
                goData.RunSQLQuery(sSQL);
            }
            //Testing - change length of varchar
            if (FieldInfo.FieldType == "nvarchar" | FieldInfo.FieldType == "varchar" || FieldInfo.FieldType == "char")
            {
                sSQL = "ALTER TABLE " + FieldInfo.FileName + " ALTER COLUMN " + FieldInfo.NewFieldName + " " + FieldInfo.FieldType + "(" + FieldInfo.Length + ")";
            }
            else
            {
                sSQL = "ALTER TABLE " + FieldInfo.FileName + " ALTER COLUMN " + FieldInfo.NewFieldName + " " + FieldInfo.FieldType;
            }

            bool bResult = goData.RunSQLQuery(sSQL);

            //FLD_MD
            //Delete line  

            if (bResult)
            {
                goMeta.LineDelete("GLOBAL", "FLD_" + FieldInfo.FileName, "US_" + FieldInfo.OldFieldName, "XX");
                //Add line
                System.Data.SqlClient.SqlConnection con = goData.GetConnection();
                goMeta.LineWrite("GLOBAL", "FLD_" + FieldInfo.FileName, "US_" + FieldInfo.NewFieldName, FieldInfo.LabelName, ref con, "", "XX");

                clCache.ClearCache();

                ReloadSchema();                

                return "success";
            }
            else
                return "fail";
        }
        public string AddFieldSave(FieldProperties FieldInfo)
        {

            string sFieldName = "";
            string sFldType = FieldInfo.FieldType;
            string sProperty = null;
            string sValue = null;
            string sLinkProperty = FieldInfo.LinkType;
            string sDateFld = null;

            System.Data.SqlClient.SqlConnection con = goData.GetConnection();
            bool bResult = false;
            //Add specific-case MD
            switch (sFldType)
            {
                case "link":
                    switch (sLinkProperty)
                    {
                        case "N1":
                            bResult = AddSQLField_Primary(FieldInfo);
                            break;
                        case "NN":
                            int Result = CreateLinkTable(FieldInfo.FileName, FieldInfo.sLinkToFile, FieldInfo.sLinkPri, FieldInfo.sLinkRev);
                            if (Result == 1)
                            {
                                bResult = true;
                            }
                            break;
                        default:

                            break;
                    }

                    //oth_links, one entry
                    //if (bResult)
                    //{
                        sProperty = FieldInfo.FileName + "," + FieldInfo.sLinkToFile + "," + FieldInfo.sLinkPri.ToUpper();
                        sValue = sLinkProperty + "," + FieldInfo.sLinkRev;

                        goMeta.LineWrite("GLOBAL", "OTH_LINKS", sProperty, sValue, ref con, "", "XX");

                        //fld_primary
                        sProperty = "US_LNK_" + FieldInfo.sLinkPri.ToUpper() + "_" + FieldInfo.sLinkToFile;
                        sValue = FieldInfo.sLinkLabel;

                        goMeta.LineWrite("GLOBAL", "FLD_" + FieldInfo.FileName, sProperty, sValue, ref con, "", "XX");

                        //fld_reverse
                        sProperty = "US_LNK_" + FieldInfo.sLinkRev + "_" + FieldInfo.FileName;
                        sValue = FieldInfo.sLinkLabelRev;

                        goMeta.LineWrite("GLOBAL", "FLD_" + FieldInfo.sLinkToFile, sProperty, sValue, ref con, "", "XX");
                    //}

                    break;
                case "datetime":
                    bResult = AddSQLField_Primary(FieldInfo);

                    if (bResult)
                    {
                        sDateFld = FieldInfo.NewFieldName;
                        //Date
                        sProperty = "US_" + sDateFld;
                        sValue = FieldInfo.LabelName;

                        if (!string.IsNullOrEmpty(sProperty))
                        {
                            goMeta.LineWrite("GLOBAL", "FLD_" + FieldInfo.FileName, sProperty, sValue, ref con, "", "XX");
                        }

                        //Time
                        sProperty = "US_" + Strings.Replace(sDateFld, "DTE_", "TME_");
                        sValue = Strings.Replace(FieldInfo.LabelName, "Date", "Time");

                        if (!string.IsNullOrEmpty(sProperty))
                        {
                            goMeta.LineWrite("GLOBAL", "FLD_" + FieldInfo.FileName, sProperty, sValue, ref con, "", "XX");
                        }

                        //Date/Time
                        sProperty = "US_" + Strings.Replace(sDateFld, "DTE_", "DTT_");
                        sValue = Strings.Replace(FieldInfo.LabelName, "Date", "Date/Time");

                        if (!string.IsNullOrEmpty(sProperty))
                        {
                            goMeta.LineWrite("GLOBAL", "FLD_" + FieldInfo.FileName, sProperty, sValue, ref con, "", "XX");
                        }
                    }
                    break;

                //V_T
                case "attachment":

                    bResult = AddSQLField_Primary(FieldInfo);

                    if (bResult)
                    {
                        sProperty = "US_" + FieldInfo.NewFieldName;
                        sValue = FieldInfo.LabelName;

                        if (!string.IsNullOrEmpty(sProperty))
                        {
                            goMeta.LineWrite("GLOBAL", "FLD_" + FieldInfo.FileName, sProperty, sValue, ref con, "", "XX");
                        }

                        //ADV
                        sProperty = "US_" + Strings.Replace(FieldInfo.NewFieldName, "ADR_", "ADV_");
                        sValue = FieldInfo.LabelName;

                        if (!string.IsNullOrEmpty(sProperty))
                        {
                            goMeta.LineWrite("GLOBAL", "FLD_" + FieldInfo.FileName, sProperty, sValue, ref con, "", "XX");
                        }
                    }
                    break;

                default:

                    bResult = AddSQLField_Primary(FieldInfo);

                    if (bResult)
                    {
                        sProperty = "US_" + FieldInfo.NewFieldName;
                        sValue = FieldInfo.LabelName;

                        if (!string.IsNullOrEmpty(sProperty))
                        {
                            goMeta.LineWrite("GLOBAL", "FLD_" + FieldInfo.FileName, sProperty, sValue, ref con, "", "XX");
                        }
                    }
                    break;

            }

            clCache.ClearCache();

            ReloadSchema();

            //if (bResult)
                return "success";
            //else
            //    return "fail";

        }
        public void ReloadSchema()
        {
            //Refresh schema
            goData.LoadFieldData();
            goData.LoadLinkData();
        }
        public bool AddSQLField_Primary(FieldProperties FieldInfo)
        {
            return AddSqlField(FieldInfo.FileName, FieldInfo.NewFieldName, FieldInfo.FieldType, FieldInfo.Length, FieldInfo.LinkType, FieldInfo.sLinkPri, FieldInfo.sLinkToFile, FieldInfo.sLinkRev);
        }
        public bool AddSqlField(string FileName, string FieldName, string FieldType, string length, string LinkType, string LinkPrimary, string LinkToFile, string LinkRev)
        {
            string sSQL = "";
            switch (FieldType)
            {
                case "datetime":
                    FieldName = "DTT" + Strings.Mid(FieldName, 4, Strings.Len(FieldName) - 3);
                    break;
                default:
                    break;
            }

            //Add column to sql table, or link table for NN links
            switch (FieldType)
            {
                case "varchar":
                case "nvarchar":
                    sSQL = "ALTER TABLE " + FileName + " ADD " + FieldName + " " + FieldType + "(" + length + ")";
                    break;
                case "link":
                    if (LinkType == "N1")
                    {
                        sSQL = "ALTER TABLE " + FileName + " ADD " + "GID_" + LinkPrimary + "_" + LinkToFile + " uniqueidentifier";
                    }
                    else if (LinkType == "NN")
                    {
                        CreateLinkTable(FileName, LinkToFile, LinkPrimary, LinkRev);
                    }
                    break;
                //V_T
                case "attachment":
                    sSQL = "ALTER TABLE " + FileName + " ADD " + FieldName + " ntext";
                    //" nvarchar(" & sLength & ")"
                    break;
                default:
                    sSQL = "ALTER TABLE " + FileName + " ADD " + FieldName + " " + FieldType;
                    break;

            }
            bool bResult = false;
            if (!string.IsNullOrEmpty(sSQL))
            {
                bResult = goData.RunSQLQuery(sSQL);
            }
            return bResult;
        }
        private int CreateLinkTable(string FileName, string LinkToFile, string LinkPrimary, string LinkRev)
        {
            System.Data.SqlClient.SqlConnection con = goData.GetConnection();
            System.Data.SqlClient.SqlCommand myCommand = new System.Data.SqlClient.SqlCommand("pCreateLinkTable", con);
            myCommand.CommandType = System.Data.CommandType.StoredProcedure;

            System.Data.SqlClient.SqlParameter file1Param = new System.Data.SqlClient.SqlParameter("@sFile1", SqlDbType.NVarChar);
            System.Data.SqlClient.SqlParameter file2Param = new System.Data.SqlClient.SqlParameter("@sFile2", SqlDbType.NVarChar);
            System.Data.SqlClient.SqlParameter linknameParam = new System.Data.SqlClient.SqlParameter("@sLinkName", SqlDbType.NVarChar);
            System.Data.SqlClient.SqlParameter linknamerevParam = new System.Data.SqlClient.SqlParameter("@sLinkRevName", SqlDbType.NVarChar);

            file1Param.Value = FileName;
            file2Param.Value = LinkToFile;
            linknameParam.Value = LinkPrimary;
            linknamerevParam.Value = LinkRev;

            myCommand.Parameters.Add(file1Param);
            myCommand.Parameters.Add(file2Param);
            myCommand.Parameters.Add(linknameParam);
            myCommand.Parameters.Add(linknamerevParam);

            //Call the sproc...
            System.Data.SqlClient.SqlDataReader reader = myCommand.ExecuteReader();

            //return parameter
            System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
            retValParam.Direction = ParameterDirection.ReturnValue;
            myCommand.Parameters.Add(retValParam);

            int iResult = Convert.ToInt16(retValParam.Value);

            reader.Close();
            con.Close();
            return iResult;
        }
        public string DeleteField(string FileName, string FieldName)
        {
            goP = (clProject)Util.GetInstance("p");
            goData = (clData)Util.GetInstance("data");
            goMeta = (clMetaData)Util.GetInstance("meta");

            string FieldType = "";
            string LinkType = "";
            string PrimaryLinkName = "";
            string LinkToFile = "";
            string RevLinkName = "";
            string sLinkDir = "";

            string FieldValue = GetFieldProperties(FileName, FieldName);
            string[] aProperties = Strings.Split(FieldValue, "|");
            FieldType = aProperties[2];

            if (FieldType == "Link")
            {
                LinkType = goData.LKGetType(FileName, aProperties[4]);
                PrimaryLinkName = GetLinkName(FieldName).ToString().ToUpper();
                LinkToFile = Strings.Right(FieldName, 2);
                RevLinkName = GetLinkName(goData.LKGetInverseName(FileName, aProperties[4])).ToString().ToUpper();
                sLinkDir = goData.LKGetDirection(FileName, aProperties[4]).ToString().ToUpper();
                if (sLinkDir != "1")
                {
                    return "delete disabled";
                }
            }

            bool bResult = DeleteFieldSQL(FileName, FieldName, FieldType, LinkType, PrimaryLinkName, LinkToFile);

            if (bResult)
            {
                DeleteFieldMD(FileName, FieldName, FieldType, LinkToFile, PrimaryLinkName, RevLinkName);

                clCache.ClearCache();

                goData.LoadFieldData();
                goData.LoadLinkData();
                return "success";
            }
            else if (Util.GetSessionValue("ErrorMessage") != null)
            {
                return "selltis error";
            }
            else
                return "fail";
        }
        private bool DeleteFieldSQL(string FileName, string FieldName, string FieldType, string LinkType, string PrimaryLinkName, string LinkToFile)
        {
            string sSQL = "";

            if (FieldType.ToLower() != "link")
            {
                sSQL = "ALTER TABLE " + FileName + " DROP COLUMN " + FieldName;
            }
            else
            {
                if (LinkType == "N1")
                {
                    //Drop GID_LinkName_XX
                    sSQL = "ALTER TABLE " + FileName + " DROP COLUMN " + "GID_" + PrimaryLinkName + "_" + LinkToFile;
                }
                else
                {
                    //Drop link table
                    sSQL = "DROP TABLE " + FileName + "_" + PrimaryLinkName + "_" + LinkToFile;
                }
            }
            return goData.RunSQLQuery(sSQL);
        }
        private void DeleteFieldMD(string FileName, string FieldName, string FieldType, string LinkToFile, string PrimaryLinkName, string RevLinkName)
        {
            switch (FieldType.ToLower())
            {
                case "link":
                    string sLinkFile = LinkToFile;
                    string sLinkName = PrimaryLinkName;
                    string sLinkNameRev = RevLinkName;
                    //remove OTH_LINKS
                    goMeta.LineDelete("GLOBAL", "OTH_LINKS", FileName + "," + sLinkFile + "," + sLinkName, "XX");
                    //remove FLD_ primary
                    goMeta.LineDelete("GLOBAL", "FLD_" + FileName, "US_LNK_" + sLinkName + "_" + sLinkFile, "XX");
                    //remove FLD_ reverse
                    goMeta.LineDelete("GLOBAL", "FLD_" + sLinkFile, "US_LNK_" + sLinkNameRev + "_" + FileName, "XX");
                    break;

                case "datetime":
                    //remove FLD_ primary
                    goMeta.LineDelete("GLOBAL", "FLD_" + FileName, "US_" + FieldName, "XX");
                    //DTE
                    goMeta.LineDelete("GLOBAL", "FLD_" + FileName, "US_" + Strings.Replace(FieldName, "DTT", "DTE"), "XX");
                    //TME
                    goMeta.LineDelete("GLOBAL", "FLD_" + FileName, "US_" + Strings.Replace(FieldName, "DTE", "TME"), "XX");
                    break;
                default:
                    //remove FLD_ primary
                    goMeta.LineDelete("GLOBAL", "FLD_" + FileName, "US_" + FieldName, "XX");
                    break;

            }
        }
    }
}