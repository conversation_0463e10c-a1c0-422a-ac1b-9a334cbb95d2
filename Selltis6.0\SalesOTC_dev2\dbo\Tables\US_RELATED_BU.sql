﻿CREATE TABLE [dbo].[US_RELATED_BU] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_US_RELATED_BU_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_US] UNIQUEIDENTIFIER NOT NULL,
    [GID_BU] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_US_RELATED_BU] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_BU_CONNECTED_US] FOREIGN KEY ([GID_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_US_RELATED_BU] FOREIGN KEY ([GID_BU]) REFERENCES [dbo].[BU] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[US_RELATED_BU] NOCHECK CONSTRAINT [LNK_BU_CONNECTED_US];


GO
ALTER TABLE [dbo].[US_RELATED_BU] NOCHECK CONSTRAINT [LNK_US_RELATED_BU];


GO
CREATE NONCLUSTERED INDEX [IX_US_RELATED_BU]
    ON [dbo].[US_RELATED_BU]([GID_US] ASC, [GID_BU] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_BU_CONNECTED_US]
    ON [dbo].[US_RELATED_BU]([GID_BU] ASC, [GID_US] ASC);

