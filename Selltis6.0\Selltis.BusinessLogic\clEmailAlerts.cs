﻿using System;
using System.Web;
using System.Text.RegularExpressions;
using System.Configuration;
using System.Data;
using System.Collections;
using System.Collections.Generic;

namespace Selltis.BusinessLogic
{
	public class clEmailAlerts
	{

		private dynamic goMeta = HttpContext.Current.Session["goMeta"];
		private dynamic goP = HttpContext.Current.Session["goP"];
		private dynamic goTR = HttpContext.Current.Session["goTR"];
		private object goDef = HttpContext.Current.Session["goDef"];
		private dynamic goData = HttpContext.Current.Session["goData"];

		public void SendEmailAlerts(clRowSet doRS, string sMode)
		{
			try
			{

				string sPerOptions = "";
				string sPer_EmailAlertsEnabled = "";
				string sPer_EmailAlertsFiles = "";
				string sTemplateName = "";
				string sLocalTemplateFileName = "";
				string sMessageBody = "";
				string sCurrFile = null;
				string sWGOption = "";
				string sWG_EMAILALERTS_ENABLE = "";
				string sReply_To = "";

				//Read the WG Options
				sWGOption = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", true);
				sWG_EMAILALERTS_ENABLE = goTR.StrRead(sWGOption, "EMAILALERTS_ENABLE", "No");
				sReply_To = goTR.StrRead(sWGOption, "EMAILALERTS_REPLY_TO", "");

				//Return if email alerts disabled in WG options
				if (sWG_EMAILALERTS_ENABLE == "No")
				{
					return;
				}

				//Read the email alerts settings from personal options   
				//sPerOptions = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", goDef.GetPersonalOptionDefs(), False)
				//sPer_EmailAlertsEnabled = goTR.StrRead(sPerOptions, "EMAILALERTS_ENABLE", sWG_EMAILALERTS_ENABLE, False)
				//sPer_EmailAlertsFiles = goTR.StrRead(sPerOptions, "EMAILALERTS_FILES ", "", False)

				//'Return if email alerts disabled in personal options
				//If sPer_EmailAlertsEnabled = "No" Then
				//    Exit Sub
				//End If

				//get the current file name
				sCurrFile = doRS.GetFileName();

				//get the template file name form work group options if email alerts are enabled for the both user and the current file
				//if no template file is available for the current file then gets the default template file


				sTemplateName = GetTemplateFileName(sWGOption, sCurrFile);

				//process the template file and prepare the message body
				if (string.IsNullOrEmpty(sTemplateName) == false)
				{
					sMessageBody = ProcessTemplateFile(sTemplateName, doRS, sMode);
				}

				//call the ClEmail.SendSMTPEmail() function to send the email
				if (string.IsNullOrEmpty(sMessageBody) == false)
				{
					SendEmail(sMessageBody, sReply_To, sCurrFile);
				}



			}
			catch (Exception ex)
			{

			}
		}

		private void SendEmail(string sMessageBody, string sReply_To, string sCurrFile)
		{

			string sSubject = "";
			string sEmailIds = "";
			string sFrom = "";

			sEmailIds = GetBetweenString(sMessageBody, "<span id=\"sEmailids\">", "</span>");
			sSubject = GetBetweenString(sMessageBody, "<span id=\"sSubject\">", "</span>");
			sFrom = GetBetweenString(sMessageBody, "<span id=\"sFrom\">", "</span>");

			sEmailIds = ValidateEmailIds(sEmailIds, sCurrFile);

			if (!string.IsNullOrEmpty(sEmailIds))
			{
				clEmail _clEmail = new clEmail();
				//Dim bSendStatus As Boolean = _clEmail.SendSMTPEmail(sSubject, sMessageBody, sEmailIds, "", "", "", sReply_To, "", "", True, "", "", True, True)
				bool bSendStatus = _clEmail.SendSMTPEmailNew(sSubject, sMessageBody, sEmailIds, "", "", "", sReply_To, "", "", true, "", "", true, true);
			}

		}

		private string ValidateEmailIds(string _sEmailIds, string sCurrFile)
		{

			_sEmailIds = _sEmailIds.Replace("<BR>", ";");
			string[] sEmails = _sEmailIds.Split(';');
			string sRetVal = "";

			foreach (string _emailId in sEmails)
			{

				if (isEmail(_emailId) && sRetVal.Contains(_emailId) == false)
				{

					//verify the email alerts are enabled for the user or not
					clRowSet rsUS = new clRowSet("US", clC.SELL_READONLY, "EML_EMAIL='" + _emailId + "'", "", "GID_ID", -1, "", "", "", "", "", true, true);
					rsUS.ToTable();

					DataTable dt = rsUS.dtTransTable;

					if (dt != null && dt.Rows.Count > 0)
					{

						string sUserId = dt.Rows[0]["GID_ID"].ToString();

						if (goTR.IsEmailAlertsEnabled(sUserId, sCurrFile))
						{
							sRetVal = sRetVal + ";" + _emailId;
						}

					}

					rsUS = null;

				}

			}

			if (sRetVal.Length > 1)
			{
				sRetVal = sRetVal.Remove(0, 1);
			}

			return sRetVal;

		}

		private bool isEmail(string inputEmail)
		{
			string strRegex = "^([a-zA-Z0-9_\\-\\.]+)@((\\[[0-9]{1,3}" + "\\.[0-9]{1,3}\\.[0-9]{1,3}\\.)|(([a-zA-Z0-9\\-]+\\" + ".)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\\]?)$";
			Regex re = new Regex(strRegex);
			if (re.IsMatch(inputEmail))
			{
				return (true);
			}
			else
			{
				return (false);
			}
		}

		private string GetBetweenString(string src, string findfrom, string findto)
		{
			int start = src.IndexOf(findfrom);
			int to = src.IndexOf(findto, start + findfrom.Length);
			if (start < 0 || to < 0)
			{
				return "";
			}
			string s = src.Substring(start + findfrom.Length, to - start - findfrom.Length);
			return s;
		}

		private string GetTemplateFileName(string sWGOption, string sCurrFile)
		{

			string sTemplateName = null;
			//  Dim sWGOption As String = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", True)
			string sEMAILALERTS_FILES = goTR.StrRead(sWGOption, "EMAILALERTS_FILES", "");
			string sEMAILALERTS_TEMPLATES = goTR.StrRead(sWGOption, "EMAILALERTS_TEMPLATES", "");

			Hashtable HtFiles = new Hashtable();
			var sTemplates = sEMAILALERTS_TEMPLATES.Split(',');
			foreach (string sfile in sTemplates)
			{
				string[] stemps = sfile.Split('~');
				HtFiles.Add(stemps.GetValue(0).ToString(), stemps.GetValue(1).ToString());
			}

			if (HtFiles.Contains(sCurrFile))
			{
				sTemplateName = HtFiles[sCurrFile].ToString();
				if (sTemplateName == "All (default)") //get the default template
				{
					sTemplateName = HtFiles["All"].ToString();
				}
			}
			else
			{
				//get the default template 
				sTemplateName = HtFiles["All"].ToString();
			}

			return sTemplateName;

		}

		private string GetTemplatesPath()
		{
			string sCusFilesPath = ConfigurationManager.AppSettings["CustomFilesPath"].ToString();
			string sHostName = HttpContext.Current.Request.Url.Host.ToString().ToLower();

			//To differentiate local and azure..J
			string substring = sCusFilesPath.Substring(1, 2);
			if (substring == ":\\")
			{
				sCusFilesPath = System.Configuration.ConfigurationManager.AppSettings["CustomFilesPath"].ToString();
			}
			else
			{
				sCusFilesPath = HttpContext.Current.Server.MapPath(System.Configuration.ConfigurationManager.AppSettings["CustomFilesPath"].ToString());
			}

			string sHostingEnvironment = ConfigurationManager.AppSettings["HostingEnvironment"].ToString().ToLower();
			if (sHostingEnvironment == "debugging")
			{
				sHostName = "default";
			}
			else if (sHostingEnvironment == "staging")
			{
				sHostName = sHostName + "_" + HttpContext.Current.Request.Url.Port.ToString();
			}
			return sCusFilesPath + sHostName + "\\\\Templates\\\\";
		}

		private string ProcessTemplateFile(string sTemplateFileName, clRowSet doRS, string sMode)
		{

			try
			{

				int intStartPos;
				int intEndPos;
				string strCode = null;
				string strValue = null;
				string strField = null;
				string sTemplate;

				//sTemplate = System.IO.File.ReadAllText(goP.sPath & "\Templates\EmailAlertsTemplates\" & sTemplateFileName)
				sTemplate = System.IO.File.ReadAllText(GetTemplatesPath() + "\\EmailAlertsTemplates\\" + sTemplateFileName);

				while (!(sTemplate.IndexOf("(%") + 1 == 0))
				{

					intStartPos = sTemplate.IndexOf("(%") + 1;
					intEndPos = sTemplate.IndexOf("%)", intStartPos - 1) + 1;

					if (Convert.ToInt32(intStartPos) == 0)
					{
						return null;
					}
					if (Convert.ToInt32(intEndPos) == 0)
					{
						return null;
					}

					if (Convert.ToInt32(intStartPos) != 0 && Convert.ToInt32(intEndPos) != 0)
					{

						strCode = sTemplate.Substring(Convert.ToInt32(intStartPos - 1), Convert.ToInt32(intEndPos - intStartPos + 2));

						string strTempCode = strCode.ToString().Substring(2, (strCode.Length - 4));

						if (strTempCode == "USER_NAME")
						{
							strValue = goP.GetMe("NAME");
						}
						else if (strTempCode == "RECORD_ACTION")
						{
							if (doRS.iRSType == clC.SELL_ADD)
							{
								strValue = "Created";
							}
							else if (doRS.iRSType == clC.SELL_EDIT)
							{
								strValue = "Updated";
							}
							else
							{
								strValue = "";
							}
						}
						else if (strTempCode == "FILE_NAME")
						{
							strValue = goData.GetFileLabel(doRS.GetFileName());
						}
						else if (strTempCode == "SITE_URL")
						{
							strValue = GetBaseURL();
						}
						else if (strTempCode == "DATA_MODIFIED")
						{
							strValue = GetModifiedFields(doRS);
						}
						else
						{

							//*******
							string[] aVal = Microsoft.VisualBasic.Strings.Split(strTempCode, "%%");

							if (aVal.Length > 2)
							{

								//LNK_RELATED_CO%%LNK_INVOLVES_US%%EML_EMAIL

								string sID = aVal[0] + "%%GID_ID";
								strValue = doRS.GetFieldVal(sID).ToString();

								string[] aGid = Microsoft.VisualBasic.Strings.Split(strValue, "\r\n");
								string sField = aVal[1] + "%%" + aVal[2];

								strValue = "";

								foreach (string s in aGid)
								{

									string sFile = goTR.GetFileFromSUID(s).ToString();

									if (!goData.IsFieldValid(sFile, sField))
									{
										continue;
									}

									clRowSet oRS = new clRowSet(sFile, clC.SELL_READONLY, "GID_ID=" + s, "GID_ID", sField, 1);

									if (oRS.GetFirst() != 0)
									{

										if (Convert.ToString(strValue) == "")
										{
											strValue = oRS.GetFieldVal(sField, clC.SELL_FRIENDLY).ToString();
										}
										else
										{
											strValue = strValue + "\r\n" + oRS.GetFieldVal(sField, clC.SELL_FRIENDLY);
										}
									}
									oRS = null;

								}

							}
							else
							{
								if (goData.IsFieldValid(doRS.GetFileName(), strTempCode))
								{
									strValue = doRS.GetFieldVal(strTempCode).ToString();
								}
								else
								{
									strValue = "***Invalid Code***";
								}
							}

							//********

							//If goData.IsFieldValid(doRS.GetFileName(), strTempCode) Then
							//    strValue = doRS.GetFieldVal(strTempCode)
							//Else
							//    strValue = "***Invalid Code***"
							//End If
						}

						sTemplate = sTemplate.Replace(strCode, ReplaceHRTs(Convert.ToString(strValue)).ToString());

					}

				}

				return sTemplate;

			}
			catch (Exception ex)
			{

				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    Return ex.ToString()
				//End If
				return "";
			}


		}

		public string GetBaseURL()
		{
			string baseUrl = HttpContext.Current.Request.Url.Scheme + "://" + HttpContext.Current.Request.Url.Authority + HttpContext.Current.Request.ApplicationPath.TrimEnd('/') + "/";
			return baseUrl;
		}

		public object ReplaceHRTs(string RawText, bool bAlways = false)
		{
				object tempReplaceHRTs = null;
			//V_T 4/23/2015
			//If bAlways = False Then
			//    If INI.TemplType = "Text" Then
			//        ReplaceHRTs = RawText
			//        Exit Function
			//    End If
			//End If

			//HTMLText = ""
			//Replace Hard Returns
			int j;
			int k;
			int l;
			char TabChar;
			j = 1;
			while (Convert.ToInt32(j) != 0)
			{
				j = RawText.IndexOf("\r\n", j - 1) + 1;
				if (Convert.ToInt32(j) != 0)
				{
					RawText = RawText.Substring(0, Convert.ToInt32(j - 1)) + "<BR>" + RawText.Substring(Convert.ToInt32(j + 1));
				}
			}
			j = 1;
			while (Convert.ToInt32(j) != 0)
			{
				j = RawText.IndexOf("\n", j - 1) + 1;
				if (Convert.ToInt32(j) != 0)
				{
					RawText = RawText.Substring(0, Convert.ToInt32(j - 1)) + "<BR>" + RawText.Substring(Convert.ToInt32(j));
				}
			}

			//Replace tabs with multiple non-breaking spaces
			TabChar = '\t';
			l = 1;
			while (Convert.ToInt32(l) != 0)
			{
				l = RawText.IndexOf(TabChar, l - 1) + 1; //vbTab
				if (Convert.ToInt32(l) != 0)
				{
					RawText = RawText.Substring(0, Convert.ToInt32(l - 1)) + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + RawText.Substring(Convert.ToInt32(l));
				}
			}
			//Replace multi spaces with non-breaking spaces
			k = 1;
			while (Convert.ToInt32(k) != 0)
			{
				k = RawText.IndexOf("  ", k - 1) + 1;
				if (Convert.ToInt32(k) != 0)
				{
					RawText = RawText.Substring(0, Convert.ToInt32(k - 1)) + "&nbsp;&nbsp;" + RawText.Substring(Convert.ToInt32(k + 1));
				}
			}

			//HTMLText = RawText
			tempReplaceHRTs = RawText;
			RawText = "";
			//DEBUG
			//MsgBox "HTMLText is " & HTMLText
			//Now capture the HTMLText variable in the procedure from which you ran this function
			return tempReplaceHRTs;
		}

		public string GetModifiedFields(clRowSet doRS)
		{

			// Try
			if (doRS.gsDirtyFields != null && doRS.gsDirtyFields.Count > 0)
			{

					string sModifiedFields = "";

// INSTANT C# NOTE: Commented this declaration since looping variables in 'foreach' loops are declared in the 'foreach' header in C#:
//					Dim pair As KeyValuePair(Of String, Tuple(Of Object, Object))

					foreach (KeyValuePair<string, Tuple<object, object>> pair in doRS.gsDirtyFields)
					{

						string sField = pair.Key;

						if (sField == "MMO_HISTORY")
						{
							continue;
						}

						object sOriginalValue = pair.Value.Item1;
						object sNewValue = pair.Value.Item2;

						if (sOriginalValue == null || sNewValue == null)
						{
							continue;
						}

						if (sField.ToLower().Contains("cur_") | sField.ToLower().Contains("chk_"))
						{
							if (sOriginalValue.ToString() == "")
							{
								sOriginalValue = "0";
							}
							if (sNewValue.ToString() == "")
							{
								sNewValue = "0";
							}
							if (sField.ToLower().Contains("chk_"))
							{
								sOriginalValue = (Convert.ToInt32(sOriginalValue) == 0) ? "Unchecked" : "Checked";
								sNewValue = (Convert.ToInt32(sNewValue) == 0) ? "Unchecked" : "Checked";
							}
							else if (sField.ToLower().Contains("cur_"))
							{
								sOriginalValue = goTR.FormatCurrField(sOriginalValue);
								sNewValue = goTR.FormatCurrField(sNewValue);
							}
						}

						if (sOriginalValue.ToString().Trim().Equals(sNewValue.ToString().Trim()))
						{
							continue;
						}

						string srow = "";

						if (sField.ToLower().Contains("mls_"))
						{
							sOriginalValue = goTR.MLSToString(doRS.GetFileName(), sField, sOriginalValue);
							sNewValue = goTR.MLSToString(doRS.GetFileName(), sField, sNewValue);
						}

						srow = "<strong>" + goData.GetFieldLabelFromName(doRS.GetFileName(), sField) + "</strong>";
						srow = srow + "\t" + "<strike>" + sOriginalValue.ToString() + "</strike>";
						srow = srow + "\t" + Convert.ToString(sNewValue);

						sModifiedFields = sModifiedFields + Environment.NewLine + srow;

					}

					return sModifiedFields;

				}

				return "";
			//Catch ex As Exception
			//    Throw ex
			//End Try

		}

	}

}
