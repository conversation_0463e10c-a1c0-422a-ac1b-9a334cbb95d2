﻿CREATE TABLE [dbo].[MS_Related_CN] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Message_Related_Contact_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_MS] UNIQUEIDENTIFIER NOT NULL,
    [GID_CN] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MS_Related_CS] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CN_Connected_MS] FOREIGN KEY ([GID_MS]) REFERENCES [dbo].[MS] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_MS_Related_CN] FOREIGN KEY ([GID_CN]) REFERENCES [dbo].[CN] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MS_Related_CN] NOCHECK CONSTRAINT [LNK_CN_Connected_MS];


GO
ALTER TABLE [dbo].[MS_Related_CN] NOCHECK CONSTRAINT [LNK_MS_Related_CN];


GO
CREATE CLUSTERED INDEX [IX_CN_Connected_MS]
    ON [dbo].[MS_Related_CN]([GID_MS] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MS_Related_CN]
    ON [dbo].[MS_Related_CN]([GID_CN] ASC);

