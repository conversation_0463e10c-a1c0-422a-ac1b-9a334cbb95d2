﻿<appSettings>
  <add key="webpages:Version" value="*******" />
  <add key="webpages:Enabled" value="false" />
  <add key="ClientValidationEnabled" value="true" />
  <add key="UnobtrusiveJavaScriptEnabled" value="true" />
  <add key="HostingEnvironment" value="debugging" />
  <add key="AppVersion" value="v6.0.0" />
  <add key="CustomFilesPath" value="C:\Selltis\CustomFiles\" />
  <add key="LogFilePath" value="C:\LogFiles\"/>

  <add key="AttachmentsStorageType" value="Azure" />
  <!-- Azure-->

  <add key="StorageConnectionString" value="DefaultEndpointsProtocol=http;AccountName=selltisfilestore;AccountKey=****************************************************************************************" />

  <!--<add key="smtpServer" value="mail.selltis.com" />  
  <add key="smtpUser" value="sellsmtp" />-->

  <add key="smtpServer" value="smtp.office365.com" />
  <add key="smtpPort" value="587" />
  <add key="smtpUser" value="<EMAIL>" />
  <add key="smtpSender" value="<EMAIL>" />
  <add key="smtpPassword" value="uF9sv#fo8!" />

  <add key="ida:TenantId" value="b52bb686-d6be-4b2c-8aa6-7437749f2714"/>
  <add key="ida:Audience" value="https://graph.microsoft.com"/>

  <add key="ida:AADInstance" value="https://login.microsoftonline.com/{0}/v2.0/"/>
  <add key="ida:STSInstance" value="https://sts.windows.net/{0}/"/>

  <add key="authenticationType" value="serviceprincipal" />
  <!--serviceprincipal -->
  
  <add key="workspaceId" value="f5486ebd-f336-4122-9f4a-52cea0b63b5a" />
  <add key="authorityUrl" value="https://login.microsoftonline.com/organizations" />
  <add key="scope" value="https://analysis.windows.net/powerbi/api/.default" />
  <add key="urlPowerBiServiceApiRoot" value="https://api.powerbi.com" />
  <add key="applicationSecret" value="****************************************" />
  <add key="applicationId" value="5f0481dc-bfcd-46a2-a9b3-3d250b57f2c9" />
  <add key="applicationServicePrincipalId" value="db490f55-b07c-4062-9268-fdfd6969593c" />
  <add key="GoogleAPIkey" value="AIzaSyBeOYq42sBcMy8huOaV6-aaifKoXrhjRvs" />
  
  <!--Outlookextension-->
  <add key="autoHostName" value="https://outlookextension.azurewebsites.net/api/Selltis_Excel_HttpRequest_OutBound?code=M0p1PTxm9G4JqX0HgosJ7cj_rxxjoIjd_jGR6emQtOpoAzFuNKzL4g==" />

  <!--OutlookextensionProd--><!--
  <add key="autoHostName" value="https://outlookextensionprod.azurewebsites.net/api/Selltis_Excel_HttpRequest_OutBound?code=dIcSqSTBta-UPYv5vza3-WxrJUiFYY2j0GPtdN6joqGoAzFuLX5-Kg==" />-->
  
   <!--Infodat-->
   <add key="ClientId" value="cf31ad99-1cc2-4c4d-b4a8-131bfd8a1671" /> 
  <add key="Tenant" value="b52bb686-d6be-4b2c-8aa6-7437749f2714" />
  
  <!--Selltis-->
  <!--<add key="ClientId" value="b8a527e7-db29-490e-afa9-189b61ca52dc" />
  <add key="Tenant" value="5f051dd9-2747-42ed-88a2-567df7968bab" />-->

  <!--OTC Dev-->

  <!--<add key="ClientId" value="8e784e2d-ade0-4ba8-afbc-1c10e89e5f4f" />
  <add key="Tenant" value="5f051dd9-2747-42ed-88a2-567df7968bab" />-->
  
  <add key="Authority" value="https://login.microsoftonline.com/{0}/v2.0" />
  <add key="redirectUri" value="https://localhost:44349/Login/Login/" />
  <add key="redirectUriLogOut" value="https://localhost:44349/Login/Login/" />
  
</appSettings>  