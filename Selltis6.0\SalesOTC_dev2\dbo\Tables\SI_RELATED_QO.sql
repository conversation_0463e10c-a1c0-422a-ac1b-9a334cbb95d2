﻿CREATE TABLE [dbo].[SI_RELATED_QO] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_SI_RELATED_QO_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_SI] UNIQUEIDENTIFIER NOT NULL,
    [GID_QO] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_SI_RELATED_QO] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_QO_LINKED_SI] FOREIGN KEY ([GID_SI]) REFERENCES [dbo].[SI] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_SI_RELATED_QO] FOREIGN KEY ([GID_QO]) REFERENCES [dbo].[QO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[SI_RELATED_QO] NOCHECK CONSTRAINT [LNK_QO_LINKED_SI];


GO
ALTER TABLE [dbo].[SI_RELATED_QO] NOCHECK CONSTRAINT [LNK_SI_RELATED_QO];


GO
CREATE NONCLUSTERED INDEX [IX_SI_RELATED_QO]
    ON [dbo].[SI_RELATED_QO]([GID_SI] ASC, [GID_QO] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QO_LINKED_SI]
    ON [dbo].[SI_RELATED_QO]([GID_QO] ASC, [GID_SI] ASC);

