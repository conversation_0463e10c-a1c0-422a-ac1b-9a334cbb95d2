Imports System.Web

Public Class clHistory

    Private gcHistory As New Collection
    Private gcHistoryStack As New Collection

    Private gsCurrentGUID As String
    Private gsCurrentType As String
    Private gsCurrentSeqNo As String
    Private gsLastVisitedSeqNo As String

    Private giHistoryMax As Integer
    Private glSeqNo As Long

    Private goP As clProject
    Private goMeta As clMetaData
    Private goTR As clTransform
    Private goData As clData
    Private goErr As clError
    'Private goUI As clUI

    Public ReadOnly Property Stack() As Collection
        Get
            Return gcHistoryStack
        End Get
    End Property

    Public Property CurrentObjGUID() As String
        Get
            Return gsCurrentGUID
        End Get
        Set(ByVal value As String)
            gsCurrentGUID = value
        End Set
    End Property

    Public Property CurrentObjType() As String
        Get
            Return gsCurrentType
        End Get
        Set(ByVal value As String)
            gsCurrentType = value
        End Set
    End Property

    Public Property CurrentObjSeqNo() As String
        Get
            Return gsLastVisitedSeqNo
        End Get
        Set(ByVal value As String)
            gsLastVisitedSeqNo = value
        End Set
    End Property

    Public Property CurrentSeqNo() As String
        Get
            Return gsCurrentSeqNo
        End Get
        Set(ByVal value As String)
            gsCurrentSeqNo = value
        End Set
    End Property

    Public Sub Initialize()
        Dim sProc As String = "clHistory::Initialize"
        ' Try
        goP = HttpContext.Current.Session("goP")
            goTR = HttpContext.Current.Session("goTr")
            goMeta = HttpContext.Current.Session("goMeta")
            goData = HttpContext.Current.Session("goData")
            goErr = HttpContext.Current.Session("goErr")
        'goUI = HttpContext.Current.Session("goUI")
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Public Sub New()
        Dim sProc As String = "clHistory::New"
        ' Try
        Initialize()
            glSeqNo = 0
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Public Function GetNextSeqNo() As Long
        Dim sProc As String = "clHistory::GetNextSeqNo"
        ' Try
        glSeqNo = glSeqNo + 1
            Return glSeqNo
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Function

    Public Function Add(ByVal oItem As clHistoryItem) As Boolean
        Dim sProc As String = "clHistory::Add"
        Try

            'first, check if single reference requirement
            'if so, look for object already in history
            'if found, grab it, make it current, open it
            'if not found, add as per usual
            If oItem.SingleReferenceKey <> "" Then
                'this is a single reference item, should only appear once in history
                'lets check each one, see if it is already there
                For Each oHistoryItem As clHistoryItem In gcHistoryStack
                    If oHistoryItem.SingleReferenceKey = oItem.SingleReferenceKey Then
                        'found it, select it exit
                        gsCurrentGUID = oItem.GUID
                        gsCurrentType = oItem.Type
                        gsCurrentSeqNo = oItem.SeqNo
                        gsLastVisitedSeqNo = oItem.SeqNo
                        Return True
                    End If
                Next
            End If

            'if i'm here, the single reference item is not already in history OR this is not a single reference item....

            Dim i As Integer
            Dim oHI As clHistoryItem
            Dim sGUIDs As String = ""
            Dim iObjCount As Integer = 0
            Dim bFoundDesktop As Boolean = False
            Dim iDeletedCounter As Integer = 0

            oItem.SeqNo = DuplicateAndInvertForwardStack(oItem)

            gcHistoryStack.Add(oItem)

            gcHistory.Add(oItem)
            gsCurrentGUID = oItem.GUID
            gsCurrentType = oItem.Type
            gsCurrentSeqNo = oItem.SeqNo
            gsLastVisitedSeqNo = oItem.SeqNo

            'only if desktop, attempt removal
            If gsCurrentType = "DESKTOP" Then

                'first check to see if max limit is met, if so, remove oldest which is in the first position
                iObjCount = CountUniqueObjects()
                giHistoryMax = goMeta.LineRead(goP.GetUserTID, "POP_PERSONAL_OPTIONS", "MAXHISTORYITEMS", "20")

                If iObjCount > giHistoryMax Then

                    Dim iNumberToDelete As Integer = iObjCount - giHistoryMax
                    Dim j As Integer = 0

                    For i = 1 To gcHistory.Count
                        oHI = gcHistory.Item(i)
                        'remove only the first open desktop, if all are forms then treat like the form max was exceeded
                        If oHI.Type.ToUpper = "DESKTOP" Then
                            Remove(oHI.SeqNo, True)
                            bFoundDesktop = True
                            j = j + 1
                            If j = iNumberToDelete Then Exit For
                        End If
                    Next

                    If bFoundDesktop = False Then
                        'treat this like the form max was exceeded (all items in history are forms)
                        OfferFormManagement()
                    End If

                End If

            End If

            Return True
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return False
        End Try
    End Function

    Public Function AddTemp(Optional ByVal par_sSpecialType As String = "", Optional ByVal par_sSpecialGUID As String = "") As Boolean
        Dim sProc As String = "clHistory::AddTemp"
        Try
            RemoveTemp()
            Dim oItem As New clHistoryItem("TEMP", "TEMP", "", Me.GetNextSeqNo, par_sSpecialType, par_sSpecialGUID)
            gcHistory.Add(oItem)
            gsCurrentGUID = "TEMP"
            gsCurrentType = "TEMP"
            gsCurrentSeqNo = oItem.SeqNo
            Return True
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return False
        End Try
    End Function

    Private Function CountUniqueObjects() As Integer
        Dim sProc As String = "clHistory::CountUniqueObjects"
        Try
            'for now, just count desktop objects
            Dim i As Integer
            Dim oHI As clHistoryItem
            Dim iCount As Integer = 0
            For i = 1 To gcHistoryStack.Count
                oHI = gcHistory.Item(i)
                If oHI.Type.ToUpper = "DESKTOP" Then
                    iCount = iCount + 1
                End If
            Next
            Return iCount
            'Return gcHistoryStack.Count
            'Return gcHistory.Count
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return 0
        End Try
    End Function

    Public Function Remove(ByVal sObjSeqNo As String, Optional ByVal bKeepSelector As Boolean = False) As Boolean
        Dim sProc As String = "clHistory::Remove"
        Try

            'goLog.Log(sProc, "Start", 0, True, True)

            Dim i As Integer
            Dim oHI As clHistoryItem
            Dim iCount As Integer = gcHistory.Count
            'Dim iIndex As Integer = 0
            Dim sGUIDToRemove As String = ""

            For i = 1 To iCount
                oHI = gcHistory.Item(i)
                If oHI.SeqNo = sObjSeqNo Then
                    sGUIDToRemove = oHI.GUID
                    gcHistory.Remove(i)
                    If Not bKeepSelector Then
                        If iCount > 1 Then
                            Select Case i
                                Case 1
                                    Dim oNew As clHistoryItem = CType(gcHistory.Item(1), clHistoryItem)
                                    gsCurrentGUID = oNew.GUID
                                    gsCurrentType = oNew.Type
                                    gsCurrentSeqNo = oNew.SeqNo
                                    gsLastVisitedSeqNo = oNew.SeqNo
                                Case Is > 1
                                    Select Case i
                                        Case Is > gcHistory.Count
                                            Dim oNew As clHistoryItem = CType(gcHistory.Item(gcHistory.Count), clHistoryItem)
                                            gsCurrentGUID = oNew.GUID
                                            gsCurrentType = oNew.Type
                                            gsCurrentSeqNo = oNew.SeqNo
                                            gsLastVisitedSeqNo = oNew.SeqNo
                                        Case Else
                                            Dim oNew As clHistoryItem = CType(gcHistory.Item(i - 1), clHistoryItem)
                                            gsCurrentGUID = oNew.GUID
                                            gsCurrentType = oNew.Type
                                            gsCurrentSeqNo = oNew.SeqNo
                                            gsLastVisitedSeqNo = oNew.SeqNo
                                    End Select
                            End Select
                        Else
                            gsCurrentGUID = ""
                            gsCurrentType = ""
                            gsCurrentSeqNo = ""
                            gsLastVisitedSeqNo = ""
                        End If
                    End If
                    Exit For
                End If
            Next

            'remove all pointers with this guid
            If sGUIDToRemove <> "" Then
                Dim bFound As Boolean = False

                Do While True
                    bFound = False
                    iCount = gcHistory.Count
                    For i = 1 To iCount
                        oHI = gcHistory.Item(i)
                        If oHI.GUID = sGUIDToRemove Then
                            gcHistory.Remove(i)
                            bFound = True

                            If Not bKeepSelector Then

                                If iCount > 1 Then
                                    Select Case i
                                        Case 1
                                            gsCurrentGUID = CType(gcHistory.Item(1), clHistoryItem).GUID
                                            gsCurrentType = CType(gcHistory.Item(1), clHistoryItem).Type
                                            gsCurrentSeqNo = CType(gcHistory.Item(1), clHistoryItem).SeqNo
                                            gsLastVisitedSeqNo = CType(gcHistory.Item(1), clHistoryItem).SeqNo
                                        Case Is > 1
                                            Select Case i
                                                Case Is > gcHistory.Count
                                                    gsCurrentGUID = CType(gcHistory.Item(gcHistory.Count), clHistoryItem).GUID
                                                    gsCurrentType = CType(gcHistory.Item(gcHistory.Count), clHistoryItem).Type
                                                    gsCurrentSeqNo = CType(gcHistory.Item(gcHistory.Count), clHistoryItem).SeqNo
                                                    gsLastVisitedSeqNo = CType(gcHistory.Item(gcHistory.Count), clHistoryItem).SeqNo
                                                Case Else
                                                    gsCurrentGUID = CType(gcHistory.Item(i - 1), clHistoryItem).GUID
                                                    gsCurrentType = CType(gcHistory.Item(i - 1), clHistoryItem).Type
                                                    gsCurrentSeqNo = CType(gcHistory.Item(i - 1), clHistoryItem).SeqNo
                                                    gsLastVisitedSeqNo = CType(gcHistory.Item(i - 1), clHistoryItem).SeqNo
                                            End Select
                                    End Select
                                Else
                                    gsCurrentGUID = ""
                                    gsCurrentType = ""
                                    gsCurrentSeqNo = ""
                                    gsLastVisitedSeqNo = ""
                                End If

                            End If

                            Exit For
                        End If
                    Next

                    If Not bFound Then

                        'goLog.Log(sProc, "Exit", 0, True, True)
                        Exit Do

                    End If

                Loop

                bFound = False
                For i = 1 To gcHistoryStack.Count
                    oHI = gcHistoryStack.Item(i)
                    If oHI.GUID = sGUIDToRemove Then
                        gcHistoryStack.Remove(i)
                        bFound = True
                        Exit For
                    End If
                Next

            End If

            'remove the actual ui object
            If sGUIDToRemove <> "" Then
                ' goUI.RemoveUIObject(sGUIDToRemove)
            End If

        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return False
        End Try
    End Function

    Public Sub RemoveAll()
        Dim sProc As String = "clHistory::RemoveAll"
        ' Try
        gcHistory.Clear()
            gcHistoryStack.Clear()
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Public Sub RemoveTemp()
        Dim sProc As String = "clHistory::RemoveTemp"
        ' Try
        Dim i As Integer
            Dim oHI As clHistoryItem
            For i = 1 To gcHistory.Count
                oHI = gcHistory.Item(i)
                If oHI.GUID = "TEMP" Then
                    gcHistory.Remove(i)
                    Exit For
                End If
            Next
            For i = 1 To gcHistoryStack.Count
                oHI = gcHistoryStack.Item(i)
                If oHI.GUID = "TEMP" Then
                    gcHistoryStack.Remove(i)
                    Exit For
                End If
            Next
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Public Function MoveBack() As Boolean
        Dim sProc As String = "clHistory::MoveBack"
        Try
            Dim i As Integer
            Dim oHI As clHistoryItem
            Select Case gsCurrentType
                Case "TEMP"
                    For i = 1 To gcHistory.Count
                        oHI = gcHistory.Item(i)
                        If oHI.SeqNo = Me.gsLastVisitedSeqNo Then
                            gsCurrentGUID = CType(gcHistory.Item(i), clHistoryItem).GUID
                            gsCurrentType = CType(gcHistory.Item(i), clHistoryItem).Type
                            gsCurrentSeqNo = CType(gcHistory.Item(i), clHistoryItem).SeqNo
                            gsLastVisitedSeqNo = CType(gcHistory.Item(i), clHistoryItem).SeqNo
                            Exit For
                        End If
                    Next
                Case Else
                    For i = 1 To gcHistory.Count
                        oHI = gcHistory.Item(i)
                        'If oHI.SeqNo = Me.gsCurrentSeqNo Then
                        If oHI.SeqNo = Me.gsLastVisitedSeqNo Then
                            If i <> 1 Then
                                gsCurrentGUID = CType(gcHistory.Item(i - 1), clHistoryItem).GUID
                                gsCurrentType = CType(gcHistory.Item(i - 1), clHistoryItem).Type
                                gsCurrentSeqNo = CType(gcHistory.Item(i - 1), clHistoryItem).SeqNo
                                gsLastVisitedSeqNo = CType(gcHistory.Item(i - 1), clHistoryItem).SeqNo
                            Else
                                'don't change current information. on the first one
                            End If
                            Exit For
                        End If
                    Next
            End Select
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return False
        End Try
    End Function

    Public Function MoveForward() As Boolean
        Dim sProc As String = "clHistory::MoveForward"
        Try
            Dim i As Integer
            Dim oHI As clHistoryItem
            Select Case gsCurrentType
                Case "TEMP"
                    For i = 1 To gcHistory.Count
                        oHI = gcHistory.Item(i)
                        If oHI.SeqNo = Me.gsLastVisitedSeqNo Then
                            gsCurrentGUID = CType(gcHistory.Item(i + 1), clHistoryItem).GUID
                            gsCurrentType = CType(gcHistory.Item(i + 1), clHistoryItem).Type
                            gsCurrentSeqNo = CType(gcHistory.Item(i + 1), clHistoryItem).SeqNo
                            gsLastVisitedSeqNo = CType(gcHistory.Item(i + 1), clHistoryItem).SeqNo
                            Exit For
                        End If
                    Next
                Case Else
                    For i = 1 To gcHistory.Count
                        oHI = gcHistory.Item(i)
                        'If oHI.SeqNo = Me.gsCurrentSeqNo Then
                        If oHI.SeqNo = Me.gsLastVisitedSeqNo Then
                            If i <> gcHistory.Count Then
                                gsCurrentGUID = CType(gcHistory.Item(i + 1), clHistoryItem).GUID
                                gsCurrentType = CType(gcHistory.Item(i + 1), clHistoryItem).Type
                                gsCurrentSeqNo = CType(gcHistory.Item(i + 1), clHistoryItem).SeqNo
                                gsLastVisitedSeqNo = CType(gcHistory.Item(i + 1), clHistoryItem).SeqNo
                            Else
                                'don't change the current information, on the last one
                            End If
                            Exit For
                        End If
                    Next
            End Select
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return False
        End Try
    End Function

    Public Function GetLastOpenObjectGUID() As String
        Dim sProc As String = "clHistory::GetLastOpenObjectGUID"
        Try
            Dim i As Integer
            Dim oHI As clHistoryItem
            Dim sGUID As String = ""
            For i = gcHistory.Count To 1 Step -1
                oHI = gcHistory.Item(i)
                If oHI.Type <> "TEMP" Then
                    If gsLastVisitedSeqNo = oHI.SeqNo Then Return oHI.GUID
                End If
            Next
            Return ""
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return ""
        End Try
    End Function

    Public Function GetLastOpenDesktopGUID() As String
        Dim sProc As String = "clHistory::GetLastOpenDesktopGUID"
        Try
            Dim i As Integer
            Dim oHI As clHistoryItem
            Dim sGUID As String = ""
            Dim bStartLooking As Boolean = False
            For i = gcHistory.Count To 1 Step -1
                oHI = gcHistory.Item(i)
                If gsLastVisitedSeqNo = oHI.SeqNo Then bStartLooking = True
                If oHI.Type.ToUpper = "DESKTOP" And bStartLooking Then Return oHI.GUID
            Next
            Return ""
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return ""
        End Try
    End Function

    Public Function GetLastOpenFormGUID() As String
        Dim sProc As String = "clHistory::GetLastOpenFormGUID"
        Try
            Dim i As Integer
            Dim oHI As clHistoryItem
            Dim sGUID As String = ""
            Dim bStartLooking As Boolean = False
            For i = gcHistory.Count To 1 Step -1
                oHI = gcHistory.Item(i)

                'for now, this used for linkbox selector and NDB forms...
                If oHI.NDBType.ToUpper = "FORM" Then Return oHI.NDBGUID

                If gsLastVisitedSeqNo = oHI.SeqNo Then bStartLooking = True
                If oHI.Type.ToUpper = "FORM" And bStartLooking Then Return oHI.GUID
                'If gsLastVisitedSeqNo = oHI.SeqNo Then bStartLooking = True

            Next
            Return ""
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return ""
        End Try
    End Function

    Public Function GetParentFormGUID() As String
        Dim sProc As String = "clHistory::GetParentFormGUID"
        Try
            Dim i As Integer
            Dim oHI As clHistoryItem
            Dim sGUID As String = ""
            Dim bStartLooking As Boolean = False
            For i = gcHistory.Count To 1 Step -1
                oHI = gcHistory.Item(i)

                'for now, this used for linkbox selector and NDB forms...
                If oHI.NDBType.ToUpper = "FORM" Then Return oHI.NDBGUID

                'If gsLastVisitedSeqNo = oHI.SeqNo Then bStartLooking = True
                If oHI.Type.ToUpper = "FORM" And bStartLooking Then Return oHI.GUID
                If gsLastVisitedSeqNo = oHI.SeqNo Then bStartLooking = True

            Next
            Return ""
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return ""
        End Try
    End Function

    Private Function DuplicateAndInvertForwardStack(ByVal oItem As clHistoryItem) As String
        Dim sProc As String = "clHistory::DuplicateAndInvertForwardStack"

        Dim i As Integer
        Dim iForwardStackStart As Integer
        Dim oHI As clHistoryItem
        Dim oHICopy As clHistoryItem
        Dim iCurrentSeqNo = CInt(oItem.SeqNo)

        Try

            'if there is nothing in the stack, no need to do anything
            If gcHistory.Count = 0 Then Return iCurrentSeqNo.ToString()

            For i = 1 To gcHistory.Count
                oHI = gcHistory.Item(i)
                If oHI.SeqNo = Me.gsLastVisitedSeqNo Then
                    'this is the current item, start dupl/inv after this record
                    iForwardStackStart = i
                    Exit For
                End If
            Next

            Dim iCount As Integer = gcHistory.Count
            Dim iNo As Integer
            Dim bDebug As Boolean = False

            For i = iCount - 1 To iForwardStackStart Step -1

                'added 2009-05-07
                If i = 0 Then Exit For

                bDebug = True
                If i = iCount - 1 Then
                    iNo = iCurrentSeqNo
                Else
                    iNo = GetNextSeqNo.ToString
                End If
                oHI = gcHistory(i)
                oHICopy = New clHistoryItem(oHI.Type, oHI.GUID, oHI.Title, iNo)
                gcHistory.Add(oHICopy)
            Next

            If bDebug Then iCurrentSeqNo = GetNextSeqNo()
            'iCurrentSeqNo = GetNextSeqNo()

            Return iCurrentSeqNo.ToString

        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return iCurrentSeqNo
        End Try
    End Function

    Private Sub OfferFormManagement()

    End Sub

    Protected Overrides Sub Finalize()
        MyBase.Finalize()
    End Sub

    Public Sub RenameCurrentObj(ByVal sName As String)
        Dim sProc As String = "clHistory::RenameCurrentObj"
        ' Try
        Dim i As Integer
            Dim oHI As clHistoryItem
            Dim sGUID As String = ""
            For i = gcHistory.Count To 1 Step -1
                oHI = gcHistory.Item(i)
                If oHI.Type <> "TEMP" Then
                    If gsLastVisitedSeqNo = oHI.SeqNo Then
                        sGUID = oHI.GUID
                        oHI.Title = sName
                        Exit For
                    End If
                End If
            Next
            If sGUID <> "" Then
                'let's modify the visible list now
                For i = 1 To gcHistoryStack.Count
                    oHI = gcHistoryStack.Item(i)
                    If oHI.GUID = sGUID Then
                        oHI.Title = sName
                        Exit For
                    End If
                Next
            End If
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Public Function ContainsObject(ByVal par_sGUID As String) As Boolean
        Dim sProc As String = "clHistory::ContainsObject"
        Try
            Dim i As Integer
            Dim oHI As clHistoryItem
            Dim sGUID As String = ""
            For i = gcHistory.Count To 1 Step -1
                oHI = gcHistory.Item(i)
                If par_sGUID = oHI.SeqNo Then Return True
            Next
            Return False
        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return False
        End Try
    End Function

End Class