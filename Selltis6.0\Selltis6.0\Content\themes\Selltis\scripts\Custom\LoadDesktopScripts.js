﻿
function LoadDesktopDocumentReady1() {
    DesktopId = $("#hidSessionDesktopId").val();
    FolderId = $("#hidSessionFolderID").val();
    Key = $("#hidKey").val();
    
    $("#Fromdate").kendoDatePicker({
        change: onChange1,
        format: "yyyy-MM-dd"
    });

    $("#Todate").kendoDatePicker({
        change: onChange2,
        format: "yyyy-MM-dd"
    });


    var topHeaderHeight = 60;
    var DesktopHeaderHeight = 38;
    var SingleRowQuickFilterHeight = 38;
    var SingleRow_DRSEnabledHeight = 76;
    var DoubleRow_DRSEnabledHeight = 129;
    var DoubleRow_DRSDisabledHeight = 74;

    var totalWindowHeight = $(window).height();

    if ($("#tabheight").length > 0) {
        totalWindowHeight = totalWindowHeight - 2;
    }

    var QuickCount = $("#hidQuickCount").val();

    var FinalHeight = 0;
    var onePercentValue = Math.round((totalWindowHeight) / 100);
    var totalSubtractValue = (onePercentValue) + 15;

    if ($("#customToolBar").length > 0) {

        sessionStorage.setItem("IsCustomToolbarAvailable_InFirstLoad", true);
        sessionStorage.setItem("CustomToolbarAvailable", true);
    }
    else {

        sessionStorage.setItem("IsCustomToolbarAvailable_InFirstLoad", false);
        sessionStorage.setItem("CustomToolbarAvailable", false);
    }
    var QuickSelectNotAvailable = $("#hidQuickSelectNotAvailable").val();

    if ($("#QuickToolbarDiv").length > 0) {
        FinalHeight = totalWindowHeight - $("#QuickToolbarDiv").outerHeight();
    }
    else {
        FinalHeight = totalWindowHeight;
    }

    $(".heightchanged").css('height', (FinalHeight - topHeaderHeight - DesktopHeaderHeight));
    $(".heightchanged").css('max-height', (FinalHeight - topHeaderHeight - DesktopHeaderHeight));
    $(".heightchanged").css('min-height', (FinalHeight - topHeaderHeight - DesktopHeaderHeight));


    function onChange1() {

        var _Fromdate = $("#Fromdate").data("kendoDatePicker");

        if (_Fromdate.value() != null) {
            $('#dateRange').val("CUSTOM");
        }
        else {
            $('#dateRange').val("MAKESELECTION");
        }

        $("#btnPrevious").hide();
        $("#btnNext").hide();

        $("#DRSGoID").show();

        //Load DRS Enabled views again in change event..J
        //OnDateRangeChanged();
    }
    function onChange2() {

        var _Todate = $("#Todate").data("kendoDatePicker");

        if (_Todate.value() != null) {
            $('#dateRange').val("CUSTOM");
        }
        else {
            $('#dateRange').val("MAKESELECTION");
        }

        $("#btnPrevious").hide();
        $("#btnNext").hide();

        $("#DRSGoID").show();

        //Load DRS Enabled views again in change event..J
        //OnDateRangeChanged();
    }

    //SB Setting current view id from default view id on loading..
    var ViewId = $("#DefaultViewId").val().replace(" ", "");
    if (sessionStorage.getItem(Key + "_" + "CurrentViewId") == null || sessionStorage.getItem(Key + "_" + "CurrentViewId") == undefined) {
        sessionStorage.setItem(Key + "_" + "CurrentViewId", ViewId);
    }
}



//RN #1873 Multiselect functionality added.
function ManageMultipleSelectedRecords(gid_id, IsChecked, ViewId, Key) {
    var multipleRecords = sessionStorage.getItem("SelectMultipleRecords_" + ViewId + Key);
    var selectedMultipleGID_IDs = null;

    if (multipleRecords != null && multipleRecords != undefined) {
        selectedMultipleGID_IDs = multipleRecords;

        if (IsChecked) {
            if (selectedMultipleGID_IDs.indexOf(gid_id) <= -1)
                selectedMultipleGID_IDs = selectedMultipleGID_IDs + gid_id + ",";
        }
        else {
            selectedMultipleGID_IDs = selectedMultipleGID_IDs.replace(gid_id + ",", "");
        }
    }
    else {
        if (IsChecked)
            selectedMultipleGID_IDs = gid_id + ",";
    }
    if (selectedMultipleGID_IDs.trim() == "") {
        selectedMultipleGID_IDs = null;
    }
    sessionStorage.setItem("SelectMultipleRecords_" + ViewId + Key, selectedMultipleGID_IDs);
}

function ManageMultiSelectPersistancy(pageData, ViewId, Key) {
    var selectedMultipleGID_IDs = sessionStorage.getItem("SelectMultipleRecords_" + ViewId + Key);

    if (selectedMultipleGID_IDs != null && selectedMultipleGID_IDs != undefined) {
        selectedMultipleGID_IDs = selectedMultipleGID_IDs.replace(/(^,)|(,$)/g, "");

        for (var i = 0; i < pageData.length; i++) {

            if (selectedMultipleGID_IDs.indexOf(pageData[i].GID_ID) > -1 && $('#chk' + pageData[i].GID_ID).length > 0) {

                $('#chk' + pageData[i].GID_ID).attr('checked', true);
            }
        }
    }
}
//Show workarea messagebox when its comming from createform..J
function ShowWorkAreaMessageBox() {

    $.ajax({
        url: '/Desktop/ShowWorkAreaMessageBox',
        cache: false,
        success: function (result) {
            if (result != "") {
                if (result.MessageBox.MessageBoxDisplay == true) {
                    DisplayMessageBox(result.MessageBox);
                }
            }
        },
        error: function (result) {

        }
    })
}


//2
function reLoadViewsV2(viewId, firstload, pageno) {
    var _grid = $("#grid" + viewId.replace(" ", "")).data("kendoGrid");
    //debugger
    var surl = _grid.dataSource.transport.options.read.url;

    if (surl.indexOf("firstload") > 0) {
        var res = surl.split("&");
        for (var r = 0; r < res.length; r++) {
            if (res[r].indexOf("firstload") > -1) {
                surl = surl.replace(res[r], "firstload=" + firstload);
                break;
            }
        }
        _grid.dataSource.transport.options.read.url = surl;
    }
    else {
        _grid.dataSource.transport.options.read.url = _grid.dataSource.transport.options.read.url + "&firstload=" + firstload;
    }

    //Make grid pageno to 1 When the request comes from GoClick function need to clear previous selected record page values,So that it selects the first record that matches to filter text.Thats why added pageno parameter ..J
    if (pageno != undefined || pageno != null) {
        var requestedPage = pageno;
        _grid.dataSource.page(requestedPage);
    }
    else {
        _grid.dataSource.fetch();
    }
}

function refreshviewV2(viewkey, viewid, viewtype, firstLoad) {
    $.ajax({
        url: '/Desktop/ReloadViewV2',
        cache: false,
        dataType: "json",
        type: "GET",
        async: false,
        contentType: 'application/json; charset=utf-8',
        data: { ViewId: viewid, ViewKey: viewkey, FirstLoad: firstLoad, ViewType: viewtype, Key: Key },
        success: function (data) {
            var _viewid = data[1];
            var _html = data[0];
            if (_viewid != null && _html != null) {
                _viewid = _viewid.replace(" ", "");
                $("#viewbody" + viewid).html(_html);
            }
            $("#page-loader").hide();
        },
        error: function (xhr) {
            $("#page-loader").hide();
        }
    });


}

function refreshCalendarV2(viewId) {

    var scheduler = $("#calender" + viewId.replace(" ", "")).data("kendoScheduler");
    var surl = scheduler.dataSource.transport.options.read.url;

    if (surl.indexOf("firstload") > 0) {
        var res = surl.split("&");
        for (var r = 0; r < res.length; r++) {
            if (res[r].indexOf("firstload") > -1) {
                surl = surl.replace(res[r], "firstload=false");
                break;
            }
        }
        scheduler.dataSource.transport.options.read.url = surl;
    }
    else {
        scheduler.dataSource.transport.options.read.url = scheduler.dataSource.transport.options.read.url + "&firstload=false";
    }

    scheduler.dataSource.fetch();
}


function refreshGrid(viewId, _selrecid, _rowclick, pageno) {
    var _grid = $("#grid" + viewId.replace(" ", "")).data("kendoGrid");

    var surl = _grid.dataSource.transport.options.read.url;

    //check for IsActive
    if (surl.indexOf("IsActive") > 0) {
        var res = surl.split("&");
        for (var r = 0; r < res.length; r++) {
            if (res[r].indexOf("IsActive") > -1) {
                surl = surl.replace(res[r], "IsActive=True");
                break;
            }
        }
        _grid.dataSource.transport.options.read.url = surl;
    }
    surl = _grid.dataSource.transport.options.read.url;
    if (surl.indexOf("MasterSelID") > 0) {
        var res = surl.split("&");
        for (var r = 0; r < res.length; r++) {
            if (res[r].indexOf("MasterSelID") > -1) {
                surl = surl.replace(res[r], "MasterSelID=" + _selrecid);
                break;
            }
        }

        _grid.dataSource.transport.options.read.url = surl;
    }
    else {
        _grid.dataSource.transport.options.read.url = _grid.dataSource.transport.options.read.url + "&MasterSelID=" + _selrecid;
    }
    surl = _grid.dataSource.transport.options.read.url;
    if (surl.indexOf("RowClick") > 0) {
        var res = surl.split("&");
        for (var r = 0; r < res.length; r++) {
            if (res[r].indexOf("RowClick") > -1) {
                surl = surl.replace(res[r], "RowClick=" + _rowclick);
                break;
            }
        }
        _grid.dataSource.transport.options.read.url = surl;
    }
    else {
        _grid.dataSource.transport.options.read.url = _grid.dataSource.transport.options.read.url + "&RowClick=" + _rowclick + "";
    }

    //Make grid pageno to 1 When the request comes from GoClick function need to clear previous selected record page values,So that it selects the first record that matches to filter text.Thats why added pageno parameter ..J
    if (pageno != undefined || pageno != null) {
        var requestedPage = pageno;
        _grid.dataSource.page(requestedPage);
    }
    else {
        _grid.dataSource.fetch();
    }
}

function refreshCalendar(viewId, _selrecid, _rowclick) {
    var scheduler = $("#calender" + viewId.replace(" ", "")).data("kendoScheduler");

    var surl = scheduler.dataSource.transport.options.read.url;

    if (surl.indexOf("IsActive") > 0) {

        var res = surl.split("&");
        for (var r = 0; r < res.length; r++) {
            if (res[r].indexOf("IsActive") > -1) {
                surl = surl.replace(res[r], "IsActive=True");
                break;
            }
        }
        scheduler.dataSource.transport.options.read.url = surl;
    }
    surl = scheduler.dataSource.transport.options.read.url;
    if (surl.indexOf("MasterSelID") > 0) {

        var res = surl.split("&");
        for (var r = 0; r < res.length; r++) {
            if (res[r].indexOf("MasterSelID") > -1) {
                surl = surl.replace(res[r], "MasterSelID=" + _selrecid);
                break;
            }
        }
        scheduler.dataSource.transport.options.read.url = surl;
    }

    else {
        scheduler.dataSource.transport.options.read.url = scheduler.dataSource.transport.options.read.url + "&MasterSelID=" + _selrecid;
    }
    surl = scheduler.dataSource.transport.options.read.url;
    if (surl.indexOf("RowClick") > 0) {
        var res = surl.split("&");
        for (var r = 0; r < res.length; r++) {
            if (res[r].indexOf("RowClick") > -1) {
                surl = surl.replace(res[r], "RowClick=" + _rowclick);
                break;
            }
        }
        scheduler.dataSource.transport.options.read.url = surl;
    }
    else {
        scheduler.dataSource.transport.options.read.url = scheduler.dataSource.transport.options.read.url + "&RowClick=" + _rowclick + "";
    }
    scheduler.dataSource.fetch();
}

function reloadReport(viewId, _selrecid, _rowclick) {
    //viewkey, viewid, viewtype,masterselId,isactive,_rowClick
    refreshview(viewId, viewId.replace(" ", ""), "report", _selrecid, true, _rowclick);
}

function reloadMap(viewId, _selrecid, _rowclick) {
    //viewkey, viewid, viewtype,masterselId,isactive,_rowClick
    refreshview(viewId, viewId.replace(" ", ""), "map", _selrecid, true, _rowclick);
}

function reloadGrid(viewId, _selrecid, _rowclick) {
    //viewkey, viewid, viewtype,masterselId,isactive,_rowClick
    refreshview(viewId, viewId.replace(" ", ""), "list", _selrecid, true, _rowclick);
}

function reloadCalendar(viewId, _selrecid, _rowclick) {
    //viewkey, viewid, viewtype,masterselId,isactive,_rowClick
    refreshview(viewId, viewId.replace(" ", ""), "calendar", _selrecid, true, _rowclick);
}

//Get CreateLinked Menu when click on create linked button..J
function GetCreateLinkedHtml() {

    $.ajax({
        url: '/Common/GetCreateLinkedHtml',
        cache: false,
        success: function (result) {
            $("#createlinkedmenu").html(result);
        },
        error: function (result) {

        }
    })
}

function LoadDesktopDocumentReady2() {
    $('#dskmetaid').hide();
    $('#DesktopMetatext').hide();
    $('#ErrorWarning').hide();

    //hide or show meta data
    var isMetaVisible = $("#hidMetaVisible").val();
    if (isMetaVisible == "true") {
        $('.metaHide').show();
    }
    else {
        $('.metaHide').hide();
    }
}

function ShowMetaData() {

    var lastViewID = sessionStorage.getItem(Key + "_" + "CurrentViewId");

    //var ViewId = sessionStorage.getItem("ViewId");
    var ViewId = lastViewID;

    //ViewId = $("#DefaultViewId").val();
    var DesktopId = DesktopId;
    //$("#pageId").val(ViewId);
    $("#DesktopId").val(DesktopId);
    $("#PNL_MessageBox_ForMeta").hide();
    onCloseMsgBox('PNL_MessageBox_ForMeta');
    $('#ErrorWarning').hide();

    //To Show ViewMeta Data in Active State
    $("#ModelTabView li:first").addClass("active");
    $("#ModelTabView li").eq(1).removeClass("active");
    $('#dskmetaid').hide();
    $('#DesktopMetatext').hide();
    $('#viewmetaid').show();
    $('#ViewMetatext').show();

    ///end

    $.ajax({
        url: '/Desktop/ShowMetaData',
        cache: false,
        data: { ViewKey: ViewId.replace(" ", ""), DesktopId: DesktopId, Key: Key },
        success: function (data) {

            var model = $("#ModalMetaData");
            $('#txtDesktopMetaData').val(data.DesktopMetaData);
            $('#txtViewMeta').val(data.ViewMetaData);
            $('#Section').val(data.Section);
            $("#pageId").val(data.PageId);
            model.modal('show');
        }
    })
}

$("#IMG_MsgBoxCloseDelete").click(function () {
    $("#PNL_MessageBoxForDelete").hide();    
    onCloseMsgBox('PNL_MessageBoxForDelete');
});

function onBTN_MsgBox2ForDeleteClick() {
    debugger;
    var ViewId = sessionStorage.getItem(Key + "_" + "CurrentViewId");
    if (sessionStorage.getItem('Delete_RecurringAP') != undefined) {
        var gid_and_recurid = sessionStorage.getItem('Delete_RecurringAP');
        //sessionStorage.removeItem('Delete_RecurringAP');
        $.ajax({
            url: '/Desktop/Delete_RecurringSeries',
            cache: false,
            data: {
                UserResponse: 'NO',
                GID_AND_RECURID: gid_and_recurid
            },
            success: function (data) {
                var scheduler = $("#calender" + ViewId.replace(" ", "")).data("kendoScheduler");
                scheduler.options.dataSource.read();

                hideProgress();
                return true;
            },
            error: function (data) {
                alert(data.responsiveText);
                return false;
            }

        });
    }
    $("#PNL_MessageBoxForDelete").hide();  
    onCloseMsgBox('PNL_MessageBoxForDelete');
}

function DeleteRecord(sArg) {
    //debugger;
    $("#hdnMsgeBox1Purpose").val(sArg);

    var ViewId = sessionStorage.getItem(Key + "_ViewId");
    ViewId = sessionStorage.getItem(Key + "_" + "CurrentViewId");//$("#DefaultViewId").val().replace(" ", "");

    var viewtype = $("#hidViewType_" + ViewId.replace(" ", "")).val();

    if (viewtype == "list") {
        var grid = $("#grid" + ViewId).data("kendoGrid");
    }
    else if (viewtype == "report") {
        var grid = $("#grid" + ViewId).data("kendoTreeList");
    }
    else if (viewtype == "calendar") {
        var grid = $("#calender" + ViewId).data("kendoScheduler");
    }

    if (grid != null && grid != undefined) {
        if (sArg == "DELETERECORD") {
            if (viewtype == "calendar") {
                var gid_id = sessionStorage.getItem(Key + "_" + "LastSelectedRow");
                var SelectedRowIndex = 0;
            }
            else {
                var selectedItem = grid.dataItem(grid.select());
                if (selectedItem != null && selectedItem != undefined) {
                    var gid_id = selectedItem.GID_ID;
                    var dataRows = grid.items();
                    var SelectedRowIndex = dataRows.index(grid.select());
                }
            }
        }
        else {
            gid_id = "";
            SelectedRowIndex = 0;
        }
    }
    else {
        return;
    }


    $.ajax({
        type: 'POST',
        url: "/Desktop/DeleteRecord",
        cache: false,
        async: false,
        data: { ViewKey: ViewId.replace(" ", ""), SelectedRecordId: gid_id, SelectedRowIndex: SelectedRowIndex, Arg: sArg, Key: Key },
        dataType: 'json',
        success: function (data) {

            if (data.PNL_MessageBoxVisible == true) {
                $("#PNL_MessageBoxForDelete").show();
                $("#LBL_MsgBoxTitleForDelete").text(data.LBL_MsgBoxTitleText);
                var _LBL_MsgBoxMessage = data.LBL_MsgBoxMessageText.replace("/n/n", "</br></br>").replace("/n", "</br>");
                $("#LBL_MsgBoxMessageForDelete").html(_LBL_MsgBoxMessage);

                if (data.BTN_MsgBox1Visible == true) {
                    $("#BTN_MsgBox1ForDelete").show();

                    $("#BTN_MsgBox1ForDelete").attr('value', data.BTN_MsgBox1Text);
                    $("#BTN_MsgBox1ForDelete").prop('value', data.BTN_MsgBox1Text);
                    $("#BTN_MsgBox1ForDelete").html(data.BTN_MsgBox1Text);
                }
                else {
                    $("#BTN_MsgBox1ForDelete").hide();
                }

                if (data.BTN_MsgBox2Visible == true) {
                    $("#BTN_MsgBox2ForDelete").show();

                    $("#BTN_MsgBox2ForDelete").attr('value', data.BTN_MsgBox2Text);
                    $("#BTN_MsgBox2ForDelete").prop('value', data.BTN_MsgBox2Text);
                    $("#BTN_MsgBox2ForDelete").html(data.BTN_MsgBox2Text);
                }
                else {
                    $("#BTN_MsgBox2ForDelete").hide();
                }

                if (data.BTN_MsgBox3Visible == true) {
                    $("#BTN_MsgBox3ForDelete").show();

                    $("#BTN_MsgBox3ForDelete").attr('value', data.BTN_MsgBox3Text);
                    $("#BTN_MsgBox3ForDelete").prop('value', data.BTN_MsgBox3Text);
                    $("#BTN_MsgBox3ForDelete").html(data.BTN_MsgBox3Text);
                }
                else {
                    $("#BTN_MsgBox3ForDelete").hide();
                }
                onOpenMsgBox('PNL_MessageBoxForDelete');
            }

        },
        error: function (data) {

        }
    });

}

function ViewDataCount() {

    var ViewId = sessionStorage.getItem(Key + "_" + "CurrentViewId");

    $("#viewDataCountSpn" + ViewId).show();
}

function onBTN_MsgBox1ForDeleteClick() {
    //$("#PNL_MessageBoxForDelete").hide();
    showProgress();
    var MsgeBox1Purpose = $("#hdnMsgeBox1Purpose").val();

    var ViewId = sessionStorage.getItem(Key + "_" + "CurrentViewId");//sessionStorage.getItem(Key + "_ViewId");
    //ViewId = $("#DefaultViewId").val().replace(" ", "");

    debugger;
    if (sessionStorage.getItem('Delete_RecurringAP') != undefined) {
        var gid_and_recurid = sessionStorage.getItem('Delete_RecurringAP');
        //sessionStorage.removeItem('Delete_RecurringAP');
        $.ajax({
            url: '/Desktop/Delete_RecurringSeries',
            cache: false,
            data: {
                UserResponse: 'YES',
                GID_AND_RECURID: gid_and_recurid
            },
            success: function (data) {
                var scheduler = $("#calender" + ViewId.replace(" ", "")).data("kendoScheduler");
                scheduler.options.dataSource.read();

                hideProgress();
                return true;
            },
            error: function (data) {
                alert(data.responsiveText);
                return false;
            }

        });
    }

    $.ajax({
        type: 'POST',
        url: "/Desktop/DeleteRecordYesClick",
        cache: false,
        //async: false,
        data: { ViewKey: ViewId.replace(" ", ""), MsgeBox1Purpose: MsgeBox1Purpose, Key: Key },
        dataType: 'json',
        success: function (data) {

            if (data.PNL_MessageBoxVisible == true) {
                $("#PNL_MessageBoxForDelete").show();
                $("#LBL_MsgBoxTitleForDelete").text(data.LBL_MsgBoxTitleText);
                var _LBL_MsgBoxMessage = data.LBL_MsgBoxMessageText.replace("/n/n", "</br></br>").replace("/n", "</br>");
                $("#LBL_MsgBoxMessageForDelete").html(_LBL_MsgBoxMessage);

                if (data.BTN_MsgBox1Visible == true) {
                    $("#BTN_MsgBox1ForDelete").show();

                    $("#BTN_MsgBox1ForDelete").attr('value', data.BTN_MsgBox1Text);
                    $("#BTN_MsgBox1ForDelete").prop('value', data.BTN_MsgBox1Text);
                    $("#BTN_MsgBox1ForDelete").html(data.BTN_MsgBox1Text);
                }
                else {
                    $("#BTN_MsgBox1ForDelete").hide();
                }

                if (data.BTN_MsgBox2Visible == true) {
                    $("#BTN_MsgBox2ForDelete").show();

                    $("#BTN_MsgBox2ForDelete").attr('value', data.BTN_MsgBox2Text);
                    $("#BTN_MsgBox2ForDelete").prop('value', data.BTN_MsgBox2Text);
                    $("#BTN_MsgBox2ForDelete").html(data.BTN_MsgBox2Text);
                }
                else {
                    $("#BTN_MsgBox2ForDelete").hide();
                }

                if (data.BTN_MsgBox3Visible == true) {
                    $("#BTN_MsgBox3ForDelete").show();

                    $("#BTN_MsgBox3ForDelete").attr('value', data.BTN_MsgBox3Text);
                    $("#BTN_MsgBox3ForDelete").prop('value', data.BTN_MsgBox3Text);
                    $("#BTN_MsgBox3ForDelete").html(data.BTN_MsgBox3Text);
                }
                else {
                    $("#BTN_MsgBox3ForDelete").hide();
                }
                onOpenMsgBox('PNL_MessageBoxForDelete');
            }
            else {
                $("#PNL_MessageBoxForDelete").hide();
                //if ($('#grid' + ViewId.replace(" ", "")).data('kendoGrid')) {
                //    //refreshGrid(ViewId, "", "no");
                //    reLoadViewsV2(ViewId.replace(" ", ""), false);
                //}
                //else if ($('#grid' + ViewId.replace(" ", "")).data('kendoTreeList')) {
                //    refreshviewV2(ViewId, ViewId.replace(" ", ""), "report", false);
                //}
                onCloseMsgBox('PNL_MessageBoxForDelete');

                //Load client views..J
                var DepencyViewIds = data;
                if (DepencyViewIds != null && DepencyViewIds != "") {

                    var dependenceviewids = DepencyViewIds + "," + ViewId.replace(" ", "");
                    var arr = dependenceviewids.split(',');

                    for (var i = 0; i < arr.length; i++) {

                        if (arr[i] == "") {
                            continue;
                        }

                        if ($("#grid" + arr[i].replace(" ", "")).data("kendoGrid")) {
                            reLoadViewsV2(arr[i].replace(" ", ""), true, 1);
                        }
                        else if ($("#grid" + arr[i].replace(" ", "")).data("kendoTreeList")) {
                            refreshviewV2(arr[i], arr[i].replace(" ", ""), "report", true);
                        }
                        else if ($("#calender" + arr[i].replace(" ", "")).data("kendoScheduler")) {
                            refreshviewV2(arr[i], arr[i].replace(" ", ""), "calendar", true);
                        }
                        else if ($("#map_" + arr[i].replace(" ", "")).length > 0) {
                            refreshviewV2(arr[i], arr[i].replace(" ", ""), "map", true);
                        }
                        else if ($("#Chart" + arr[i].replace(" ", "")).data("kendoChart")) {

                            var desktopid = DesktopId;
                            var section = "GLOBAL";
                            var viewId = arr[i].replace(" ", "");

                            if (desktopid != null) {
                                $.ajax({
                                    url: '/Desktop/LoadLinkedChart',
                                    cache: false,
                                    dataType: "json",
                                    type: "GET",
                                    async: false,
                                    contentType: 'application/json; charset=utf-8',
                                    data: { ViewKey: arr[i].replace(" ", ""), DesktopId: desktopid, Section: section, MasterViewSelRecord: "", IsTabView: false, Key: Key },
                                    success: function (data) {

                                        if (data != 'failure') {
                                            $('#viewbody' + viewId.replace('/ /g', '')).html(data);

                                            ResizeChartInsplitter(viewId.replace(" ", ""));
                                        }
                                        hideProgress();
                                    },
                                    error: function (data) {
                                    }
                                })
                            }
                        }
                    }
                }
                else {
                    if ($("#grid" + ViewId.replace(" ", "")).data("kendoGrid")) {
                        reLoadViewsV2(ViewId.replace(" ", ""), true, 1);
                    }
                    else if ($("#grid" + ViewId.replace(" ", "")).data("kendoTreeList")) {
                        refreshviewV2(ViewId, ViewId.replace(" ", ""), "report", true);
                    }
                    else if ($("#calender" + ViewId.replace(" ", "")).data("kendoScheduler")) {
                        $("#calender" + ViewId.replace(" ", "")).data("kendoScheduler").options.dataSource.read();
                    }
                    else if ($("#map_" + ViewId.replace(" ", "")).length > 0) {
                        refreshviewV2(ViewId, ViewId.replace(" ", ""), "map", true);
                    }
                }
            }

            hideProgress();

        },
        error: function (data) {

        }
    });

}

function OnDSKMetaDataClick() {
    // //debugger;
    $("#PNL_MessageBox_ForMeta").hide();
    onCloseMsgBox('PNL_MessageBox_ForMeta');
    $('#dskmetaid').show();
    $('#DesktopMetatext').show();
    $('#viewmetaid').hide();
    $('#ViewMetatext').hide();

    var ViewId = sessionStorage.getItem(Key + "_ViewId");
    //ViewId = $("#DefaultViewId").val();
    var DesktopId = $("#hidSessionDesktopId").val();
    $('#pageId').val(DesktopId);
    $('#DesktopId').val(DesktopId);

    $.ajax({
        url: '/Desktop/ShowMetaData',
        cache: false,
        data: { DesktopId: DesktopId },
        success: function (data) {
            var model = $("#ModalMetaData");
            $('#txtDesktopMetaData').val(data.DesktopMetaData);
            $('#Section').val(data.Section);
            model.modal('show');
        }
    })
}

function OnViewMetaDataClick() {

    $("#PNL_MessageBox_ForMeta").hide();
    onCloseMsgBox('PNL_MessageBox_ForMeta');
    $('#dskmetaid').hide();
    $('#DesktopMetatext').hide();
    $('#viewmetaid').show();
    $('#ViewMetatext').show();

    // //debugger;
    var lastViewID = sessionStorage.getItem(Key + "_" + "CurrentViewId");

    //var ViewId = sessionStorage.getItem("ViewId");
    var ViewId = lastViewID;
    //ViewId = $("#DefaultViewId").val();
    var DesktopId = $("#hidSessionDesktopId").val();
    //$('#pageId').val(ViewId);
    $('#DesktopId').val(DesktopId);

    $.ajax({
        url: '/Desktop/ShowMetaData',
        cache: false,
        data: { ViewKey: ViewId.replace(" ", ""), Key: Key },
        success: function (data) {
            var model = $("#ModalMetaData");

            $('#txtViewMeta').val(data.ViewMetaData);
            $('#Section').val(data.Section);
            $("#pageId").val(data.PageId);
            model.modal('show');
        }
    })
}

function OnRefreshWarningClick() {
    var Product_val = $("#CMB_Product").val();
    var PageId = $("#pageId").val();

    $.ajax({
        url: '/Desktop/OnRefreshWarning',
        cache: false,
        data: { Product_val: Product_val, PageId: PageId },
        success: function (data) {
            ////debugger
            if (data != "") {
                $('#lblWarning').text(data);
                $('#ErrorWarning').show();
            }
            else
                $('#ErrorWarning').hide();
        }
    });
}

$("#btn_metadata_Save").click(function () {
    var Section = $("#Section").val();
    var Product_val = $("#CMB_Product").val();
    var PageId;
    var MetaData;
    var ActiveTab_Id = $('#ModelTabView li.active').attr('id')
    if (ActiveTab_Id == "viewmetadata") {
        PageId = $("#pageId").val();
        MetaData = encodeURIComponent($("#txtViewMeta").val());
    }
    else {
        PageId = $("#DesktopId").val();
        MetaData = encodeURIComponent($("#txtDesktopMetaData").val());
    }

    var PassData = { Section: Section, PageId: PageId, Product_val: Product_val, MetaData: MetaData }
    MetaData = JSON.stringify(MetaData)
    $.ajax({
        url: '/Desktop/SaveMetaData',
        cache: false,
        data: { PassData: JSON.stringify(PassData), Key: Key },
        type: 'POST',
        dataType: "json",
        success: function (data) {
            //  //debugger
            //DisplayMessageBox(data);
            if (data == "True") {
                window.location.href = "/Desktop/LoadDesktop/" + $("#hidSessionDesktopId").val() + "/true/FOLDERID/" + Key;
            }
            if (data.PNL_MessageBoxVisible == true) {
                $("#PNL_MessageBox_ForMeta").show();
                $("#LBL_MsgBoxTitle_ForMeta").text(data.LBL_MsgBoxTitleText);
                var _LBL_MsgBoxMessage = data.LBL_MsgBoxMessageText.replace("/n/n", "</br></br>").replace("/n", "</br>");
                $("#LBL_MsgBoxMessage_ForMeta").html(_LBL_MsgBoxMessage);

                if (data.BTN_MsgBox1Visible == true) {
                    $("#BTN_MsgBox1_ForMeta").show();

                    $("#BTN_MsgBox1_ForMeta").attr('value', data.BTN_MsgBox1Text);
                    $("#BTN_MsgBox1_ForMeta").prop('value', data.BTN_MsgBox1Text);
                    $("#BTN_MsgBox1_ForMeta").html(data.BTN_MsgBox1Text);
                }
                else {
                    $("#BTN_MsgBox1_ForMeta").hide();
                }

                if (data.BTN_MsgBox2Visible == true) {
                    $("#BTN_MsgBox2_ForMeta").show();

                    $("#BTN_MsgBox2_ForMeta").attr('value', data.BTN_MsgBox2Text);
                    $("#BTN_MsgBox2_ForMeta").prop('value', data.BTN_MsgBox2Text);
                    $("#BTN_MsgBox2_ForMeta").html(data.BTN_MsgBox2Text);
                }
                else {
                    $("#BTN_MsgBox2").hide();
                }

                if (data.BTN_MsgBox3Visible == true) {
                    $("#BTN_MsgBox3_ForMeta").show();

                    $("#BTN_MsgBox3_ForMeta").attr('value', data.BTN_MsgBox3Text);
                    $("#BTN_MsgBox3_ForMeta").prop('value', data.BTN_MsgBox3Text);
                    $("#BTN_MsgBox3_ForMeta").html(data.BTN_MsgBox3Text);
                }
                else {
                    $("#BTN_MsgBox3_ForMeta").hide();
                }
                if (data.oControlFocus != "") {
                    // $('#' + data.oControlFocus).focus();
                    var ActiveTab_Id = $('#ModelTabView li.active').attr('id')
                    if (ActiveTab_Id == "viewmetadata") {
                        $('#' + data.oControlFocus).focus();
                    }
                    else {
                        $('#DesktopId').focus();
                    }
                }
                onOpenMsgBox('PNL_MessageBox_ForMeta');
            }
            //if (data != "") {
            //    $('#lblWarning').text(data);
            //    $('#ErrorWarning').show();
            //}
            //else
            //    $('#ErrorWarning').hide();
        }
    });

})

function BTN_MsgBox1_ForMeta_Click() {
    $("#PNL_MessageBox_ForMeta").hide();
    onCloseMsgBox('PNL_MessageBox_ForMeta');
}

function BTN_MsgBox2_ForMeta_Click() {
    $("#PNL_MessageBox_ForMeta").hide();
    onCloseMsgBox('PNL_MessageBox_ForMeta');
}

function BTN_MsgBox3_ForMeta_Click() {
    $("#PNL_MessageBox_ForMeta").hide();
    onCloseMsgBox('PNL_MessageBox_ForMeta');
}

//3
function LoadDesktopDocumentReady3() {
    hideProgress();

    //To fix issue with improper loading divs(shrinking)..J
    if ($("#splitterT").length > 0) {
        $("#splitterT").show();
    }

    if ($("#divheight").length > 0) {
        $("#divheight").show();
    }

    if ($("#tabheight").length > 0) {
        $("#tabheight").show();
    }

    if ($("#hidIsFavourite").val() == 'True') {
        $("#iconFavorites").removeClass();
        $("#iconFavorites").addClass("fa fa-heart");
        $('#btnFavorites').prop('title', 'Remove from favorites');
    }
    else {
        $("#iconFavorites").removeClass();
        $("#iconFavorites").addClass("fa fa-heart-o");
        $('#btnFavorites').prop('title', 'Add to Favorites');
    }

    //****** Splitter Control ***************
    LoadSplitterSizes();

    ShowWorkAreaMessageBox();
}

var firsLoadWindowWidth = $(window).width();

$(window).on("resize", function () {

    //To clear cookie sizes when window is resized..J
    var DesktopId = $("#hidSessionDesktopId").val();
    ClearDesktopSplitterCookie(DesktopId);

    if (sessionStorage.getItem("CurrentPopOutViewId") != null && sessionStorage.getItem("CurrentPopOutViewId") != undefined) {
        var viewId = sessionStorage.getItem("CurrentPopOutViewId");

        if ($("#btnpopoutin" + viewId).length > 0) {
            $("#btnpopoutin" + viewId).click();
        }
    }


    if ($(window).width() < 900) {

        //$('body').attr('style', 'overflow:visible !important');
        CssForSplitterWindowResize()
    }
    else {

        $('body').scrollTop(0);
        $('body').attr('style', 'overflow:hidden !important');
        //debugger
        //specific to height..
        var diff = firsLoadWindowWidth - $(window).width();

        var resizedHeight = $(window).height() - $('header').outerHeight() - $(".panel-heading").outerHeight() - $("#QuickToolbarDiv").outerHeight();
        var resizedWidth = $(window).width() - $("#sidebar_left").outerWidth();
        var resizedCol1Width = $("#splittercol1").width() - resizedWidth;

        $(".fulldivpscroll").css('height', resizedHeight + 'px !important');
        //set column1 height when no tabs..J
        if ($("#tabheight").length == 0) {
            $("#divheight").height(resizedHeight);
            $("#splittercol1").height(resizedHeight);
            $("#splittercol2").height(resizedHeight);
        }

        //check for top views..J
        if (topViewCount != 0) {
            $("#divheight").data("kendoSplitter").options.panes[0].size = resizedCol1Width;
            if ($("#divheight").data("kendoSplitter") != null) {
                $("#divheight").data("kendoSplitter").trigger("resize");
            }

            if ($("#splittercol1").data("kendoSplitter") != null) {
                $("#splittercol1").data("kendoSplitter").trigger("resize");
            }

            if ($("#splittercol2").data("kendoSplitter") != null) {
                $("#splittercol2").data("kendoSplitter").trigger("resize");
            }
        }

        //remove top space when there are no top views..J
        if (topViewCount == 0) {
            if ($("#tabheight").length > 0) {
                $("#divheight").css('height', '0px');
                $("#tabheight").css('top', '0px');
            }
        }

        if ($("#tabheight").length > 0) {
            //$("#splitterT").data("kendoSplitter").options.panes[0].size = $("#divheight").height()+"px";
            //$("#splitterT").data("kendoSplitter").trigger("resize");

            //Setting tab height on window resize..S1
            var topHeaderHeight = 60;
            var DesktopHeaderHeight = 38;

            var totalWindowHeight = $(window).height();

            totalWindowHeight = totalWindowHeight - 2;

            var QuickCount = $("#hidQuickCount").val();

            var FinalHeight = 0;

            if ($("#QuickToolbarDiv").length > 0) {
                FinalHeight = totalWindowHeight - $("#QuickToolbarDiv").outerHeight();
            }
            else {
                FinalHeight = totalWindowHeight;
            }

            $(".heightchanged").css('height', (FinalHeight - topHeaderHeight - DesktopHeaderHeight));
            $(".heightchanged").css('max-height', (FinalHeight - topHeaderHeight - DesktopHeaderHeight));
            $(".heightchanged").css('min-height', (FinalHeight - topHeaderHeight - DesktopHeaderHeight));
        }

        //ClearDesktopSplitterCookie(DesktopId);
        //location.reload();       
    }

});

function LoadSplitterSizes() {
    $("#splitterT").css('height', '100%');
    $("[id*='splittercol']").css('height', '80%');
    $("#divheight").css('margin-top', '2px');
    //$("#divheight").css('margin-bottom', '-5px');

    //check for top views..J
    if (topViewCount != 0) {

        //debugger
        //$("#splittercol2").css('margin-left', '-3px');

        //
        // ********** splitter in between top views and tabbed views..J *********
        //get TopViews div height and set it to splitter pane..J
        var divHeight = ($("#divheight").height() + 20) + "px";
        //get pane size from cookie..J
        var existingval = readCookie("DS_" + $("#hidSessionDesktopId").val() + "_splitterT");
        if (existingval != null) {
            divHeight = existingval;
        }
        $("#splitterT").kendoSplitter({
            panes: [{ scrollable: false, size: "" + divHeight + "" }, {}],
            resize: splitterTResizeHandler,
            orientation: "vertical"
        });

        //Specific to IE Tabbed view issue..S1
        $(".k-splitbar.k-state-default.k-splitbar-vertical.k-splitbar-draggable-vertical").css("top", divHeight);
        var dvheight = divHeight.replace("px", "");
        $("#divheight").css('height', '');
        $("#divheight").height(parseInt(dvheight));
        var tabheight = $("#splitterT").height() - parseInt(dvheight);
        $("#tabheight").css('height', '');
        $("#tabheight").css({ 'height': (tabheight - 6) + 'px' });
        $("#tabheight").css({ 'top': (parseInt(dvheight) + 6) + 'px' });

        // ********** Splitter in between top views views(horizontal splitter between columns)..J *********
        //get column1 width and set it to splitter pane..J
        var col1Width = $("#splittercol1").width() + "px";
        //get pane size from cookie..J
        var existingval = readCookie("DS_" + $("#hidSessionDesktopId").val() + "_divheight");
        if (existingval != null) {
            col1Width = existingval;
        }


        $("#divheight").kendoSplitter({
            panes: [{ scrollable: false, size: "" + col1Width + "" }, {}],
            resize: divheightResizeHandler
        });

        //********** Splitter in between top Column1 views..J *********
        $("#splittercol1").kendoSplitter({
            panes: [{ scrollable: false }, {}],
            layoutChange: splittercol1ResizeHandler,
            orientation: "vertical"
        });

        //set column1 height when no tabs..J
        if ($("#tabheight").length == 0) {
            var splitterCol1Height = $("#splittercol1").height() - 5;
            $("#splittercol1").css('height', splitterCol1Height + 'px');
            $("#splittercol1").css('border-bottom', '1px solid rgb(204, 204, 204)');
        }
        $("#splittercol1").css('margin-left', '5px');

        //get panes sizes of Column1 splitter from cookie based in their view ids..J
        var splitterCol1InnerPanes = $("#splittercol1").data("kendoSplitter");
        //check column2 is there or not..J(single views have only column1)
        if (splitterCol1InnerPanes != null) {
            if (splitterCol1InnerPanes.element.length > 0) {
                for (var i = 0; i < splitterCol1InnerPanes.element[0].children.length; i++) {
                    if ((splitterCol1InnerPanes.element[0].children[i].id).indexOf('view') > -1) {
                        var divId = splitterCol1InnerPanes.element[0].children[i].id;
                        var divHeight = $("#" + divId).height();

                        //get pane size from cookie and set..J
                        var existingval = readCookie("DS_" + $("#hidSessionDesktopId").val() + "_" + divId);
                        if (existingval != null) {
                            divHeight = Number(existingval);
                        }
                        Col1PaneDivId = divId;
                        splitterCol1InnerPanes.size("#" + divId, divHeight + "px");
                        //debugger
                        if ($("#tabheight").length == 0) {
                            if ($("#splittercol2").length == 0) {
                                $("#" + divId).height($("#" + divId).height() - 5);
                            }
                            else {
                                $("#" + divId).height($("#" + divId).height() - 5);
                            }
                        }

                        if ($("#tabheight").length > 0) {
                            if (i == splitterCol1InnerPanes.element[0].children.length - 1) {
                                $("#" + divId).height($("#" + divId).height() - 8);
                            }
                        }

                        //set chart height after splitter is added..J
                        var viewId = divId.substring(4, divId.length);
                        ResizeChartInsplitter(viewId);
                    }
                }
            }
        }

        // ********** Splitter in between top Column2 views..J *********
        $("#splittercol2").kendoSplitter({
            panes: [{ scrollable: false }, {}],
            layoutChange: splittercol2ResizeHandler,
            orientation: "vertical"
        });

        //set column2 height when no tabs..J
        if ($("#tabheight").length == 0) {
            var splitterCol2Height = $("#splittercol2").height() - 5;
            $("#splittercol2").css('height', splitterCol2Height + 'px');
            $("#splittercol2").css('border-bottom', '1px solid rgb(204, 204, 204)');

            var horizntalSplitterHght = $(".k-splitbar.k-state-default.k-splitbar-horizontal.k-splitbar-draggable-horizontal").height() - 6;
            $(".k-splitbar.k-state-default.k-splitbar-horizontal.k-splitbar-draggable-horizontal").height(horizntalSplitterHght);
        }


        //get panes sizes of Column2 splitter from cookie based in their view ids..J
        var splitterCol2InnerPanes = $("#splittercol2").data("kendoSplitter");
        //check column2 is there or not..J(single views have only column1)
        if (splitterCol2InnerPanes != null) {
            if (splitterCol2InnerPanes.element.length > 0) {
                for (var i = 0; i < splitterCol2InnerPanes.element[0].children.length; i++) {
                    if ((splitterCol2InnerPanes.element[0].children[i].id).indexOf('view') > -1) {
                        var divId = splitterCol2InnerPanes.element[0].children[i].id;
                        var divHeight = $("#" + divId).height();

                        //get pane size from cookie and set..J
                        var existingval = readCookie("DS_" + $("#hidSessionDesktopId").val() + "_" + divId);
                        if (existingval != null) {
                            divHeight = Number(existingval);
                        }
                        Col2PaneDivId = divId;
                        splitterCol2InnerPanes.size("#" + divId, divHeight + "px");

                        //set inner divs height in column2..
                        if ($("#tabheight").length == 0) {
                            $("#" + divId).height($("#" + divId).height() - 5);
                        }

                        if ($("#tabheight").length > 0) {
                            if (i == splitterCol2InnerPanes.element[0].children.length - 1) {
                                $("#" + divId).height($("#" + divId).height() - 8);
                            }
                        }

                        //set chart height after splitter is added..J
                        var viewId = divId.substring(4, divId.length);
                        ResizeChartInsplitter(viewId);

                    }
                }
            }
        }
        //debugger
        //resize charts in tabbed div..J
        resizeChartsInTabbedDiv();

        //to remove scroll for second column..J
        $("#splittercol2").removeClass("k-pane k-scrollable k-widget k-splitter");
        $("#splittercol2").addClass("k-pane k-widget k-splitter");


        //set column1 widths when there is no column2..J
        if ($("#splittercol2").length == 0) {
            var splitterCol1Width = $("#splittercol1").width() - 5;
            $("#splittercol1").css('width', splitterCol1Width + 'px');
            setCol1InnerDivsWidth();
        }

        setCol2InnerDivsWidth();

        //set css for view there is tabbed views..J
        if ($("#tabheight").length > 0) {
            $("#splitterT").css('margin-left', '5px');
            $("#splittercol1").css('margin-left', '0px');
            $(".k-splitbar.k-state-default.k-splitbar-horizontal.k-splitbar-draggable-horizontal").css('margin-left', '-3px');
            $("#splittercol2").css('margin-left', '-3px');
            $("#tabheight").css('margin-left', '0px');
            //$("#tabheight").height($("#tabheight").height() - 15);
        }

        //when there is no column1 set margin left to column2..J
        if ($("#splittercol1").length == 0) {
            $("#splittercol2").css('margin-left', '5px');
        }
    }

    //remove top space when there are no top views..J
    if (topViewCount == 0) {
        if ($("#tabheight").length > 0) {
            $("#tabheight").css('top', '0px');
            $("#tabheight").css('width', '100%');
            $("#tabheight").css('height', '99.9%');
        }
    }

    //setting tab view height with its header tab list content accordingly.. SB
    if ($('#tabheight').length > 0) {
        var tabHeight = $('#tabheight').height();
        var tabListHeight = $('#myTab').height();
        var tabViewHeight = tabHeight - tabListHeight;
        $('.tab-content.fulldivp').attr('style', 'height:' + tabViewHeight + 'px !important; min-height:' + tabViewHeight + 'px !important;');
    }
}

function ClearDesktopSplitterCookie(SelectedDesktopId) {

    $.ajax({
        url: "/ManageDesktops/GetViewIdsInDesktop",
        type: "POST",
        cache: false,
        data: { DesktopId: SelectedDesktopId },
        success: function (data) {

            //To clear views panes sizes..J
            if (data.indexOf(',') > -1) {
                var ViewIDs = data.split(',');
                for (var i = 0; i < ViewIDs.length; i++) {
                    eraseCookie("DS_" + SelectedDesktopId + "_view" + ViewIDs[i].replace(' ', ''));
                }
            }

            //To clear top and tab view panes sizes..J
            eraseCookie("DS_" + SelectedDesktopId + "_splitterT");
            eraseCookie("DS_" + SelectedDesktopId + "_divheight");
        },
        error: function (data) {

        }
    })
}

function resizeChartsInTabbedDiv() {
    //debugger
    var chartsInTabbedDiv = $("#tabheight");
    if (chartsInTabbedDiv != null) {
        if (chartsInTabbedDiv.length > 0) {
            if (chartsInTabbedDiv[0].childNodes['1'] != null && chartsInTabbedDiv[0].childNodes['1'] != undefined) {
                if (chartsInTabbedDiv[0].childNodes['1'].children.length > 0) {
                    for (var i = 0; i < chartsInTabbedDiv[0].childNodes['1'].children.length; i++) {
                        if ((chartsInTabbedDiv[0].childNodes['1'].children[i].id).indexOf('view') > -1) {

                            var divId = chartsInTabbedDiv[0].childNodes['1'].children[i].id;

                            //set chart height after splitter is added..J
                            var viewId = divId.substring(4, divId.length);
                            ResizeChartInsplitter(viewId);

                        }
                    }
                }
            }

        }
    }
}

function setCol1InnerDivsWidth() {
    var splitterCol1InnerPanesWidth = $("#splittercol1");
    if (splitterCol1InnerPanesWidth != null) {
        if (splitterCol1InnerPanesWidth.length > 0) {
            for (var i = 0; i < splitterCol1InnerPanesWidth[0].children.length; i++) {
                if ((splitterCol1InnerPanesWidth[0].children[i].id).indexOf('view') > -1) {
                    //debugger
                    var divId = splitterCol1InnerPanesWidth[0].children[i].id;
                    var divWidth = $("#splittercol1").width();
                    $("#" + divId).css('width', divWidth + 'px');
                    //$("#" + divId).css('height', ($("#" + divId).height() - 10) + 'px');


                }
            }
        }
    }
}

function setCol2InnerDivsWidth() {
    var splitterCol2InnerPanesWidth = $("#splittercol2");
    if (splitterCol2InnerPanesWidth != null) {
        if (splitterCol2InnerPanesWidth.length > 0) {
            for (var i = 0; i < splitterCol2InnerPanesWidth[0].children.length; i++) {
                if ((splitterCol2InnerPanesWidth[0].children[i].id).indexOf('view') > -1) {

                    var divId = splitterCol2InnerPanesWidth[0].children[i].id;
                    var divWidth = $("#splittercol2").width();
                    $("#" + divId).css('width', divWidth + 'px');

                    if ($("#tabheight").length > 0) {
                        $("#" + divId).width($("#" + divId).width() - 6);
                    }

                }
            }
        }
    }
}

function setCol1andCol2InnerDivsHeight(IsFromTab) {

    var splitterCol1InnerPanesWidth = $("#splittercol1");
    if (splitterCol1InnerPanesWidth != null) {
        if (splitterCol1InnerPanesWidth.length > 0) {
            for (var i = 0; i < splitterCol1InnerPanesWidth[0].children.length; i++) {
                if ((splitterCol1InnerPanesWidth[0].children[i].id).indexOf('view') > -1) {

                    var divId = splitterCol1InnerPanesWidth[0].children[i].id;
                    var splitterCol1InnerPanes = $("#splittercol1").data("kendoSplitter");
                    var divHeight = $("#" + divId).height();
                    splitterCol1InnerPanes.size("#" + divId, divHeight + "px");

                    if (IsFromTab == true) {
                        $("#" + divId).height($("#" + divId).height() - 0);
                    }
                    else {
                        $("#" + divId).height($("#" + divId).height() - 5);
                    }
                }
            }
        }
    }


    var splitterCol2InnerPanesWidth = $("#splittercol2");
    if (splitterCol2InnerPanesWidth != null) {
        if (splitterCol2InnerPanesWidth.length > 0) {
            for (var i = 0; i < splitterCol2InnerPanesWidth[0].children.length; i++) {
                if ((splitterCol2InnerPanesWidth[0].children[i].id).indexOf('view') > -1) {

                    var divId = splitterCol2InnerPanesWidth[0].children[i].id;
                    var splitterCol2InnerPanes = $("#splittercol2").data("kendoSplitter");
                    var divHeight = $("#" + divId).height();
                    splitterCol2InnerPanes.size("#" + divId, divHeight + "px");

                    if (IsFromTab == true) {
                        $("#" + divId).height($("#" + divId).height() - 0);
                    }
                    else {
                        $("#" + divId).height($("#" + divId).height() - 5);
                    }
                }
            }
        }
    }
}

var splittercol1ResizeHandler = function () {
    splittercol1Resized(this, Col1PaneDivId);
}

function splittercol1Resized(sender, PaneId) {

    //prevent first loading..J
    if (sender.resizing != null && sender.resizing != undefined) {
        if (sender.element.length > 0) {
            for (var i = 0; i < sender.element[0].children.length; i++) {
                if ((sender.element[0].children[i].id).indexOf('view') > -1) {
                    var divId = sender.element[0].children[i].id;

                    //panel id will be empty only when it comes from resizing..J
                    if (PaneId == "") {
                        var divHeight = $("#" + divId).height();
                        var cookieName = "DS_" + $("#hidSessionDesktopId").val() + "_" + divId;
                        var cookieVal = divHeight;
                        createCookie(cookieName, cookieVal, 7);

                        if ($("#tabheight").length > 0) {
                            if (i == sender.element[0].children.length - 1) {
                                $("#" + divId).height($("#" + divId).height() - 8);
                            }
                        }

                        //set chart height after splitter is added..J
                        var viewId = divId.substring(4, divId.length);
                        ResizeChartInsplitter(viewId);
                    }
                    Col1PaneDivId = "";
                }
            }

            //when window has resized..J
            CssForSplitterWindowResize();
        }
    }
}

function CssForSplitterWindowResize() {

    //check for width width..J
    if ($(window).width() < 900) {
        $('body').attr('style', 'overflow:visible !important');
        //set column1 css..J
        var ResizedWidth = $("#divheight").width();
        $("#splittercol1").css('width', ResizedWidth + 'px');

        //set column2 css..J
        if ($("#splittercol2").length > 0) {
            $("#splittercol2").css('top', ($("#splittercol1").height() + 5) + 'px');
            $("#splittercol2").css('left', '5px');
            $("#splittercol2").css('width', ResizedWidth + 'px');
            $(".k-splitbar.k-state-default.k-splitbar-horizontal.k-splitbar-draggable-horizontal").css('display', 'none');
        }

        //set tabbed view css..J
        if ($("#tabheight").length > 0) {
            if ($("#splittercol2").length > 0) {
                $("#splittercol2").css('top', ($("#splittercol1").height() + 5) + 'px');
                $("#splittercol2").css('left', '5px');
                $("#splittercol2").css('width', ResizedWidth + 'px');
                $(".k-splitter.k-pane").css('overflow', 'visible');
            }
            $("#tabheight").css('top', (($("#splittercol1").height() + $("#splittercol2").height()) + 10) + 'px');
        }


    }
    else {
        $(".k-splitbar.k-state-default.k-splitbar-horizontal.k-splitbar-draggable-horizontal").css('display', '');
    }


}

var splittercol2ResizeHandler = function () {
    splittercol2Resized(this, Col2PaneDivId);
}

function splittercol2Resized(sender, PaneId) {

    //prevent first loading..J
    if (sender.resizing != null && sender.resizing != undefined) {
        if (sender.element.length > 0) {
            for (var i = 0; i < sender.element[0].children.length; i++) {
                if ((sender.element[0].children[i].id).indexOf('view') > -1) {
                    var divId = sender.element[0].children[i].id;

                    if (PaneId == "") {
                        var divHeight = $("#" + divId).height();
                        var cookieName = "DS_" + $("#hidSessionDesktopId").val() + "_" + divId;
                        var cookieVal = divHeight;
                        createCookie(cookieName, cookieVal, 7);

                        if ($("#tabheight").length > 0) {
                            $("#" + divId).width($("#" + divId).width() - 6);
                            if (i == sender.element[0].children.length - 1) {
                                $("#" + divId).height($("#" + divId).height() - 8);
                            }
                        }

                        //set chart height after splitter is added..J
                        var viewId = divId.substring(4, divId.length);
                        ResizeChartInsplitter(viewId);
                    }
                    Col2PaneDivId = "";
                }
            }

            //when window has resized..J
            CssForSplitterWindowResize();

        }
    }
}

var divheightResizeHandler = function () {
    //debugger
    divheightResized(this);
}

function divheightResized(sender) {

    var cookieName = "DS_" + $("#hidSessionDesktopId").val() + "_" + sender.element[0].id;
    var cookieVal = sender.options.panes[0].size;
    createCookie(cookieName, cookieVal, 7);

    //prevent first loading..J
    if (sender.resizing != null && sender.resizing != undefined) {
        //var splitterCol2Width = $("#splittercol2").width() - 10;
        //$("#splittercol2").css('width', splitterCol2Width + 'px');
        setCol2InnerDivsWidth();

        if ($("#tabheight").length == 0) {
            //set column1 height when no tabs..J
            var splitterCol1Height = $("#splittercol1").height() - 5;
            $("#splittercol1").css('height', splitterCol1Height + 'px');
            $("#splittercol1").css('border-bottom', '1px solid rgb(204, 204, 204)');

            //set column2 height when no tabs..J
            var splitterCol2Height = $("#splittercol2").height() - 5;
            $("#splittercol2").css('height', splitterCol2Height + 'px');
            $("#splittercol2").css('border-bottom', '1px solid rgb(204, 204, 204)');

            var horizntalSplitterHght = $(".k-splitbar.k-state-default.k-splitbar-horizontal.k-splitbar-draggable-horizontal").height() - 6;
            $(".k-splitbar.k-state-default.k-splitbar-horizontal.k-splitbar-draggable-horizontal").height(horizntalSplitterHght);

            setCol1andCol2InnerDivsHeight();
        }

        resizeChartsInTabbedDiv();

    }
}

var splitterTResizeHandler = function () {
    //debugger
    splitterTResized(this);
}

function splitterTResized(sender) {

    var cookieName = "DS_" + $("#hidSessionDesktopId").val() + "_" + sender.element[0].id;
    var cookieVal = sender.options.panes[0].size;
    createCookie(cookieName, cookieVal, 7);

    //prevent first loading..J
    if (sender.resizing != null && sender.resizing != undefined) {

        setTabbedSplitterFromCookie(sender.options.panes[0].size);
        setCol1andCol2InnerDivsHeight(true);

        //setting tab view height with its header tab list content accordingly.. SB
        if ($('#tabheight').length > 0) {
            var tabHeight = $('#tabheight').height();
            var tabListHeight = $('#myTab').height();
            var tabViewHeight = tabHeight - tabListHeight;
            $('.tab-content.fulldivp').attr('style', 'height:' + tabViewHeight + 'px !important; min-height:' + tabViewHeight + 'px !important;');
        }
    }

    //when window has resized..J
    CssForSplitterWindowResize();

}

function setTabbedSplitterFromCookie(senderPaneSize) {

    $(".k-splitbar.k-state-default.k-splitbar-vertical.k-splitbar-draggable-vertical").css("top", senderPaneSize);

    var dvheight = senderPaneSize.replace("px", "");
    //check for top views..J
    if (topViewCount != 0) {
        $("#divheight").height(parseInt(dvheight));
        $("#divheight").data("kendoSplitter")._size.height = parseInt(dvheight);

        $("#divheight").css('height', '');
        $("#divheight").css({ 'height': senderPaneSize });
    }
    var tabheight = $("#splitterT").height() - parseInt(dvheight);
    $("#tabheight").css('height', '');
    $("#tabheight").css({ 'height': (tabheight - 6) + 'px' });
    $("#tabheight").css({ 'top': (parseInt(dvheight) + 6) + 'px' });
}

function createCookie(cookieName, cookieVal, days) {
    if (days) {
        var date = new Date();
        date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
        var expires = ";expires=" + date.toGMTString();
    }
    else var expires = "";

    document.cookie = cookieName + "=" + cookieVal + expires + "; path=/";
}

function readCookie(name) {
    //debugger
    var nameEQ = name + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
}

function eraseCookie(name) {
    createCookie(name, "", -1);
}

function ResizeChartInsplitter(_viewId) {
    //debugger
    var isInview = localStorage.getItem(Key + 'isInview' + _viewId);


    if ($('#Chart' + _viewId).data("kendoChart")) {

        var divHeight = $('#view' + _viewId).height() - $('#divChartBrdCm' + _viewId).height() - 28;
        $('#viewbody' + _viewId).height(divHeight);

        var width = $('#viewbody' + _viewId).width();
        var height = $('#viewbody' + _viewId).height();
        width = width - 5;

        var reducableHeight = 5 * height / 100;
        //debugger
        //if (height < 100 || width < 450) {
        //    //height = $('#view' + _viewId).height();
        //    //width = $('#view' + _viewId).width();

        //    height = 300;       //set fixed height and width when scroll appears..J
        //    width = 500;

        //    $('#viewbody' + _viewId).css({ 'overflow': "auto" });  //when min height reached add scroll..J
        //    //$('#viewbody' + _viewId).css({ 'overflow-x': "hidden" });
        //}
        //else {
        //    //height = height - reducableHeight;
        //    $('#viewbody' + _viewId).css({ 'overflow': "hidden" });
        //    //$('#viewbody' + _viewId).css({ 'overflow-x': "hidden" });
        //}


        if (height < 100) {
            height = 200;
            //width = 500;
            //width = $('#viewbody' + _viewId).width();
            $('#viewbody' + _viewId).css({ 'overflow-y': "auto" });  //when min height reached add scroll..J
        }
        else {
            $('#viewbody' + _viewId).css({ 'overflow-y': "hidden" });
            $('#view' + _viewId).css({ 'overflow-y': "hidden" });
        }
        if (width < 450) {
            //height = 300;
            height = height - 10;
            width = 500;
            $('#viewbody' + _viewId).css({ 'overflow-x': "auto" });
        }
        else {
            $('#viewbody' + _viewId).css({ 'overflow-x': "hidden" });
            $('#view' + _viewId).css({ 'overflow-x': "hidden" });
        }

        $('#Chart' + _viewId).css({ 'margin-top': -3 });

        //debugger
        var customePanelDivHeight = $('#divOptionsPanel_' + _viewId).height();
        if (customePanelDivHeight != null && customePanelDivHeight != undefined) {
            height = height - customePanelDivHeight;
        }

        $('#Chart' + _viewId).data("kendoChart").options.chartArea['width'] = width - 15;
        $('#Chart' + _viewId).data("kendoChart").options.chartArea['height'] = height;
        $('#Chart' + _viewId).data("kendoChart").refresh();
    }
    else if ($('#Chart' + _viewId).data("kendoRadialGauge")) {

        var divHeight = $('#view' + _viewId).height() - $('#divChartBrdCm' + _viewId).height() - 28;
        $('#viewbody' + _viewId).height(divHeight);

        var width = $('#viewbody' + _viewId).width();
        var height = $('#viewbody' + _viewId).height();
        width = width - 5;

        var reducableHeight = 5 * height / 100;

        if (height < 105) {

            $('#view' + _viewId).css({ 'overflow-y': "auto" });  //when min height reached add scroll..J
            $('#view' + _viewId).css({ 'overflow-x': "hidden" });
        }
        else {
            //height = height - reducableHeight;
            $('#view' + _viewId).css({ 'overflow-y': "hidden" });
            $('#view' + _viewId).css({ 'overflow-x': "hidden" });
        }

        $('#Chart' + _viewId).css({ 'margin-top': -3 });

        var customePanelDivHeight = $('#divOptionsPanel_' + _viewId).height();
        if (customePanelDivHeight != null && customePanelDivHeight != undefined) {
            height = height - customePanelDivHeight;

        }
        $('#Chart' + _viewId).width(width);
        $('#Chart' + _viewId).height(height - 5);
        $('#Chart' + _viewId).data("kendoRadialGauge").redraw();
    }

    ////chart height and widths storing in session to reuse same style ..J
    //if ($('#Chart' + _viewId).data("kendoChart")) {
    //    sessionStorage.setItem('Chart' + _viewId + 'height', $('#Chart' + _viewId).data("kendoChart").options.chartArea['height']);
    //    sessionStorage.setItem('Chart' + _viewId + 'width', $('#Chart' + _viewId).data("kendoChart").options.chartArea['width']);
    //}
    //else if ($('#Chart' + _viewId).data("kendoRadialGauge")) {

    //    sessionStorage.setItem('Chart' + _viewId + 'height', $('#Chart' + _viewId).height());
    //    sessionStorage.setItem('Chart' + _viewId + 'width', $('#Chart' + _viewId).width());
    //}

}

function popout(el, viewtype) {
   //debugger;
    $("#PNL_MessageBoxForDelete").hide();
    //var attr = $(el).parent().parent().attr('style');
    // var attr = $(el).parent().parent().parent().attr('style');
    var attr1 = $(el).parent().parent().parent().attr('name');
    var vivid = $(el).parent().parent().parent()[0].id;
    var _viewId = vivid.substring(4);
    //alert(attr);

        // attr = attr.replace(/ /g, '');
        //if (attr != "border:1pxsolidrgb(204,204,204)!important;border-image:none!important;margin-right:0px;margin-bottom:5px;" && attr != "margin-bottom:5px;margin-right:0px;border:1pxsolid#cccccc!important;" && attr != "padding-bottom:5px;")
    if (attr1 != "inview") {
        var _class = $(el).parent().parent().parent().attr('attr');

        if (_class != undefined) {
            sessionStorage.setItem("HeightClass", attr);
        }

        if ($(".sb-l-disable-animation")[0]) {

        }
        else {
            //   $("#toggle_sidemenu_l").click();
        }
        var _getHeightClass = sessionStorage.getItem("HeightClass");
        $(el).parent().parent().parent().addClass(_getHeightClass);
        $("#popoutpopin" + _viewId).attr('class');
        $("#popoutpopin" + _viewId).removeClass('fa-compress');
        $("#popoutpopin" + _viewId).addClass('fa-expand');
        $("#btnpopoutin" + _viewId).attr('title', "Maximize");
        $(el).parent().parent().parent().removeAttr("style");
        // $(el).parent().parent().parent().css(attr);
        $(el).parent().parent().parent().attr('name', "inview");

        //$(el).parent().parent().parent().css({ 'border': "1px solid #cccccc !important", 'margin-right': 0, 'margin-bottom': 5 });//zIndex: "2147483647"
        //$(el).parent().parent().parent().css({ background: "white", "margin-top": 1, "margin-bottom": 5, position: "absolute" });//zIndex: "2147483647" // written in below else condition

        //popout - popin issue fixing - Ranganadh
        var RequiredStyles = sessionStorage.getItem("ViewStyles" + vivid);
        var isTabView = false;
        if (viewsWith_IsTabView.indexOf(',') > -1) {
            var viewid = vivid.replace("view", "");
            var arr = viewsWith_IsTabView.split(',');
            if (arr.length > 1) {
                for (var i = 0; i < arr.length; i++) {
                    if (arr[i].indexOf(viewid) > -1) {
                        var viewInf = arr[i].split("|");
                        var TabView = viewInf[1];
                        if (TabView == "True") {
                            isTabView = true;
                            break;
                        }
                        break;
                    }
                }
            }
        }
        if (isTabView) {
            $(el).parent().parent().parent().css({
                background: "white", "margin-right": 0, "margin-bottom": 5, "border-color": "#CCCCCC",
                "border-width": "1px",
                "border-style": "solid"
            });
        }
        else if (RequiredStyles.indexOf("|") > -1) {
            var aStyles = RequiredStyles.split('|');
            if (aStyles.length == 3) {
                var height = Number(aStyles[0]);
                var width = Number(aStyles[1]);
                var top = Number(aStyles[2]);

                $(el).parent().parent().parent().css({
                    background: "white", "margin-top": 1, "margin-bottom": 5, position: "absolute", top: top, "border-color": "#CCCCCC",
                    "border-width": "1px",
                    "border-style": "solid"
                });
                $(el).parent().parent().parent().height(height).width(width);
            }
        }
        else {
            $(el).parent().parent().parent().css({
                background: "white", "margin-top": 1, "margin-bottom": 5, position: "absolute", "border-color": "#CCCCCC",
                "border-width": "1px",
                "border-style": "solid"
            });
        }

        ////injecting previous styles after popout ..S1
        //$(el).parent().parent().parent().css('cssText', 'border: 1px solid #cccccc !important');
        //$(el).parent().parent().parent().css({ 'margin-bottom': 5, 'margin-right': 0, 'background-color': 'white' });//zIndex: "2147483647"

        ////Splitter specific..J
        //var styles = sessionStorage.getItem("inviewStyle" + vivid);
        //if (styles != undefined && styles != null) {
        //    $(el).parent().parent().parent().css(styles);
        //}

        $('.navbar').show();
        attr1 = "inview";

        if (viewtype == "CALENDAR") {
            var viewid = vivid.replace("view", "");
            var scheduler = $("#calender" + viewid.replace(" ", "")).data("kendoScheduler");
            scheduler.dataSource.fetch();
        }

        //hide pagination dropdown when view is in popiin..J
        if (viewtype == "GRID") {
            //$("#divPagination" + _viewId).hide();
        }

        sessionStorage.removeItem("CurrentPopOutViewId");

        ResizeChartInsplitter(_viewId);
        //resetChartAfterPopOut(_viewId);
        //resetChart(_viewId);
    }
    else {

        var _class = $(el).parent().parent().parent().attr('class');

        if (_class != undefined) {
            sessionStorage.setItem("HeightClass", _class);
        }
        if ($(".sb-l-disable-animation")[0]) {
            // $("#toggle_sidemenu_l").click();
        }
        else {
            //  $("#toggle_sidemenu_l").click();
        }

        //popout - popin issue fixing - Ranganadh
        var height = $(el).parent().parent().parent().height();
        var width = $(el).parent().parent().parent().width();
        var top = $(el).parent().parent().parent().css("top");

        if (top != undefined && top != null) {
            top = top.replace("px", "");
        }
        sessionStorage.setItem("ViewStyles" + vivid, height + "|" + width + "|" + top);


        $(el).parent().parent().parent().removeAttr("class");
        $("#popoutpopin" + _viewId).attr('class');
        $("#popoutpopin" + _viewId).removeClass('fa-expand');
        $("#popoutpopin" + _viewId).addClass('fa-compress');
        $("#btnpopoutin" + _viewId).attr('title', "Minimize");
        $(el).parent().parent().parent().attr('name', "outview");
        //$(el).parent().parent().parent().css({ position: "fixed", zIndex: "2147483647", left: 0, top: 0, bottom: 0, right: 0, height: "100% !important", background: "white", boxSizing: "border-box", MozBoxSizing: "border-box", WebkitBoxSizing: "border-box" });//zIndex: "2147483647"
        $(el).parent().parent().parent().css({ position: "fixed", zIndex: "999", left: 200, top: 0, bottom: 0, right: 0, height: "100% !important", background: "white", boxSizing: "border-box", MozBoxSizing: "border-box", WebkitBoxSizing: "border-box", 'overflow-y': 'hidden' });//zIndex: "2147483647"

        //Splitter specific..J
        var styles = $(el).parent().parent().parent().attr("style");


        //sessionStorage.setItem("inviewStyle" + vivid, styles);

        var FinalHeight = 0;

        if ($("#QuickToolbarDiv").length > 0) {
            FinalHeight = 98 + $("#QuickToolbarDiv").outerHeight();
        }
        else {
            FinalHeight = 98;
        }

        var leftMenuWidth = $("#sidebar_left").width();


        $(el).parent().parent().parent().removeAttr("style");
        //$(el).parent().parent().parent().css({ border: "1px solid rgb(204, 204, 204) !important", position: "fixed", top: 0, zIndex: "999", left: 0, bottom: 0, right: 0, background: "white", WebkitBoxSizing: "border-box" });//zIndex: "2147483647"
        $(el).parent().parent().parent().css({ border: "1px solid rgb(204, 204, 204) !important", position: "fixed", top: FinalHeight, zIndex: "999", left: leftMenuWidth, bottom: 0, right: 0, background: "white", WebkitBoxSizing: "border-box" });//zIndex: "2147483647"

        //$('.navbar').hide();
        attr1 = "outview";

        if (viewtype == "CALENDAR") {
            var viewid = vivid.replace("view", "");
            var scheduler = $("#calender" + viewid.replace(" ", "")).data("kendoScheduler");
            scheduler.dataSource.fetch();
        }
        //resetChart(_viewId);

        //set pagination dropdown when view is in popout..J
        if (viewtype == "GRID") {
            var _grid = $("#grid" + _viewId).data("kendoGrid");
            var pageData = _grid.dataSource.view();
            $('#pagination').val(pageData.length);
            if ($('#' + _viewId + '_spnLoadView').text() == "Click here to not load view.") {
                $("#divPagination" + _viewId).show();
            }
        }

        ResizeChartInsplitter(_viewId);

        sessionStorage.setItem("CurrentPopOutViewId", _viewId);
    }

    //chart load
    localStorage.setItem(Key + 'isInview' + _viewId, attr1);
    //resetChart(_viewId);
    //resetChartAfterPopOut(_viewId);
}

function resetChartAfterPopOut(_viewId) {
    if ($('#Chart' + _viewId).data("kendoChart")) {
        var width = $('#viewbody' + _viewId).width();
        var height = $('#viewbody' + _viewId).height();

        //get previous heights and widths(popin) from sessions..J
        var ChartHeight = sessionStorage.getItem('Chart' + _viewId + 'height');
        var ChartWidth = sessionStorage.getItem('Chart' + _viewId + 'width');

        //set previous styles as we cleared everything in popout so add again..J
        if (height < 105) {

            $('#view' + _viewId).css({ 'overflow-y': "auto" });
            $('#view' + _viewId).css({ 'overflow-x': "hidden" });
        }

        $('#Chart' + _viewId).data("kendoChart").options.chartArea['width'] = parseInt(ChartWidth);
        $('#Chart' + _viewId).data("kendoChart").options.chartArea['height'] = parseInt(ChartHeight);
        $('#Chart' + _viewId).data("kendoChart").refresh();
    }
    else if ($('#Chart' + _viewId).data("kendoRadialGauge")) {   //for Guage chart..J
        var width = $('#viewbody' + _viewId).width();
        var height = $('#viewbody' + _viewId).height();

        var ChartHeight = sessionStorage.getItem('Chart' + _viewId + 'height');
        var ChartWidth = sessionStorage.getItem('Chart' + _viewId + 'width');

        if (height < 105) {

            $('#view' + _viewId).css({ 'overflow-y': "auto" });
            $('#view' + _viewId).css({ 'overflow-x': "hidden" });
        }
        $('#Chart' + _viewId).css({ 'margin-top': 0 });
        $('#Chart' + _viewId).width(parseInt(ChartWidth));
        $('#Chart' + _viewId).height(parseInt(ChartHeight));
        $('#Chart' + _viewId).data("kendoRadialGauge").redraw();
    }
}

function resetChart(_viewId) {
    //debugger
    var isInview = localStorage.getItem(Key + 'isInview' + _viewId);
    if ($('#Chart' + _viewId).data("kendoChart")) {
        var width = $('#viewbody' + _viewId).width();
        var height = $('#viewbody' + _viewId).height();
        width = width - 35;

        if (height < 105) {
            //height = height - 20;
            //$('#Chart' + _viewId).css({ 'margin-top': -15 });

            $('#view' + _viewId).css({ 'overflow-y': "auto" });  //when min height reached add scroll..J
            $('#view' + _viewId).css({ 'overflow-x': "hidden" });
        }
        else if (height < 200) {
            $('#view' + _viewId).css({ 'overflow-y': "hidden" });
            height = height - 10;
            $('#Chart' + _viewId).css({ 'margin-top': -5 });
        }
        else {
            $('#view' + _viewId).css({ 'overflow-y': "hidden" });
            height = height - 38;
        }

        //debugger
        var customePanelDivHeight = $('#divOptionsPanel_' + _viewId).height();


        //height = 200;
        //$('#Chart' + _viewId).width(width);
        $('#Chart' + _viewId).data("kendoChart").options.chartArea['width'] = width;
        $('#Chart' + _viewId).data("kendoChart").options.chartArea['height'] = height;
        //$('#Chart' + _viewId).css({ 'margin-top': -10 });

        //if (isInview == "outview") {
        //    $('#Chart' + _viewId).data("kendoChart").options.chartArea['height'] = height;
        //}
        //else {
        //    $('#Chart' + _viewId).data("kendoChart").options.chartArea['height'] = height - 35;
        //}
        $('#Chart' + _viewId).data("kendoChart").refresh();
    }
    else if ($('#Chart' + _viewId).data("kendoRadialGauge")) {
        var width = $('#viewbody' + _viewId).width();
        var height = $('#viewbody' + _viewId).height();

        if (height < 105) {
            height = height - 20;
            $('#Chart' + _viewId).css({ 'margin-top': -15 });
        }
        else if (height < 200) {
            height = height - 10;
            $('#Chart' + _viewId).css({ 'margin-top': -15 });
        }
        else {
            height = height - 35;
            $('#Chart' + _viewId).css({ 'margin-top': -5 });
        }

        width = width - 35;
        //height = height - 35;
        $('#Chart' + _viewId).width(width);
        $('#Chart' + _viewId).height(height);
        //$('#Chart' + _viewId).css({ 'margin-top': -15 });


        //        if (isInview == "outview") {
        //            $('#Chart' + _viewId).height(height - 200);
        //            $('#divXAxisLabl').css({ "margin-top": 0 });
        //}
        //        else {
        //            $('#Chart' + _viewId).height(height - 35);
        //            $('#divXAxisLabl').css({ "margin-top": 0 });
        //            //$('#Chart' + _viewId).children().width(width).height(height - 85);
        //            //$('#Chart' + _viewId).children().children().width(width).height(height - 85);
        //        }
        $('#Chart' + _viewId).data("kendoRadialGauge").redraw();
    }
}

function ResetSort() {
    ////debugger;
    showProgress();
    var myval = sessionStorage.getItem(Key + "_" + "CurrentViewId");//$("#DefaultViewId").val().replace(" ", "");
    $.ajax({
        url: '/Desktop/ResetSort',
        async: true,
        cache: false,
        type: "GET",
        data: { ViewKey: myval.replace(" ", ""), Key: Key },
        success: function (data) {

            //if ($('#grid' + myval).data('kendoGrid')) {
            //    $('#grid' + myval).data("kendoGrid").dataSource.sort({});

            //    refreshGrid(myval, "", "no");

            //    // $('#grid' + myval).data('kendoGrid').dataSource.read();
            //    // $('#grid' + myval).data('kendoGrid').refresh();
            //}
            //else if ($("#calender" + myval.replace(" ", "")).data("kendoScheduler")) {

            //    $('#calender' + myval).data('kendoScheduler').dataSource.sort({});
            //    $('#calender' + myval).data('kendoScheduler').dataSource.read();

            //}
            //else if ($("#grid" + myval).data("kendoTreeList")) {
            //    $('#grid' + myval).data('kendoTreeList').dataSource.sort({});
            //    reloadReport(myval, "", "no");
            //    // reloadview($("#grid" + myval).data("kendoTreeList").dataSource.transport.options.read.url, myval, "report", true,"no");
            //}

            if ($("#grid" + myval.replace(" ", "")).data("kendoGrid")) {
                refreshviewV2(myval, myval.replace(" ", ""), "list", true);
            }
            else if ($("#grid" + myval.replace(" ", "")).data("kendoTreeList")) {
                refreshviewV2(myval, myval.replace(" ", ""), "report", true);
            }
            else if ($("#calender" + myval.replace(" ", "")).data("kendoScheduler")) {
                refreshviewV2(myval, myval.replace(" ", ""), "calendar", true);
            }
            else if ($("#map_" + myval.replace(" ", "")).length > 0) {
                refreshviewV2(myval, myval.replace(" ", ""), "map", true);
            }

            var viewtype = $("#hidViewType_" + myval.replace(" ", "")).val();

            if (viewtype == null || viewtype == undefined || viewtype == "undefined") {
                var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();
                var ids = viewIds_and_viewtypes.split("|");

                for (var j = 0; j < ids.length; j++) {
                    var types = ids[j].split(",");
                    if (types[0].replace(" ", "") == myval.replace(" ", "")) {
                        viewtype = types[1];
                        break;
                    }
                }
            }

            if (viewtype == "chart" || viewtype == "CHART") {
                var desktopid = DesktopId;
                var section = "GLOBAL";
                //var index = i + 1;
                //var masterViewSelRecord = gid_id;

                if (desktopid != null) {
                    $.ajax({
                        url: '/Desktop/LoadLinkedChart',
                        cache: false,
                        dataType: "json",
                        type: "GET",
                        async: false,
                        contentType: 'application/json; charset=utf-8',
                        data: { ViewKey: myval, DesktopId: desktopid, Section: section, MasterViewSelRecord: "", IsTabView: false, Key: Key },
                        success: function (data) {
                            ////debugger
                            if (data != 'failure') {
                                $('#viewbody' + myval.replace('/ /g', '')).html(data);

                                ResizeChartInsplitter(myval.replace(" ", ""));
                            }
                            hideProgress();
                        },
                        error: function (data) {
                        }
                    })
                }
            }

            LoadDependencyViewsV2(data, myval, true);
            MakeChildDependencyViewsOnFocus_AsEmpty(myval);

            hideProgress();
        },
        error: function (data) {
            hideProgress();
        }
    })
}

function ResetFilter() {
    //  //debugger;
    var myval = sessionStorage.getItem(Key + "_" + "CurrentViewId");  //$("#DefaultViewId").val().replace(" ", "");
    showProgress();
    $.ajax({
        url: '/Desktop/ResetFilter',
        // dataType: "json",
        type: "GET",
        cache: false,
        // contentType: 'application/json',
        data: { ViewKey: myval.replace(" ", ""), Key: Key },
        success: function (data) {

            if ($("#grid" + myval.replace(" ", "")).data("kendoGrid")) {
                refreshviewV2(myval, myval.replace(" ", ""), "list", true);
            }
            else if ($("#grid" + myval.replace(" ", "")).data("kendoTreeList")) {
                refreshviewV2(myval, myval.replace(" ", ""), "report", true);
            }
            else if ($("#calender" + myval.replace(" ", "")).data("kendoScheduler")) {
                refreshviewV2(myval, myval.replace(" ", ""), "calendar", true);
            }
            else if ($("#map_" + myval.replace(" ", "")).length > 0) {
                refreshviewV2(myval, myval.replace(" ", ""), "map", true);
            }

            var viewtype = $("#hidViewType_" + myval.replace(" ", "")).val();

            if (viewtype == null || viewtype == undefined || viewtype == "undefined") {
                var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();
                var ids = viewIds_and_viewtypes.split("|");

                for (var j = 0; j < ids.length; j++) {
                    var types = ids[j].split(",");
                    if (types[0].replace(" ", "") == myval.replace(" ", "")) {
                        viewtype = types[1];
                        break;
                    }
                }
            }

            if (viewtype == "chart" || viewtype == "CHART") {
                var desktopid = DesktopId;
                var section = "GLOBAL";
                //var index = i + 1;
                //var masterViewSelRecord = gid_id;

                if (desktopid != null) {
                    $.ajax({
                        url: '/Desktop/LoadLinkedChart',
                        cache: false,
                        dataType: "json",
                        type: "GET",
                        async: false,
                        contentType: 'application/json; charset=utf-8',
                        data: { ViewKey: myval, DesktopId: desktopid, Section: section, MasterViewSelRecord: "", IsTabView: false, Key: Key },
                        success: function (data) {
                            ////debugger
                            if (data != 'failure') {
                                $('#viewbody' + myval.replace('/ /g', '')).html(data);

                                ResizeChartInsplitter(myval.replace(" ", ""));
                            }
                            hideProgress();
                        },
                        error: function (data) {
                        }
                    })
                }
            }

            var _tooltip = data.ToolTip;

            $("#" + data.TableName + myval).attr("title", _tooltip);

            LoadDependencyViewsV2(data.DepencyViewIds, myval, true);
            MakeChildDependencyViewsOnFocus_AsEmpty(myval);

            hideProgress();
            //window.location.reload(true);
        },
        error: function (data) {
            //hideProgress();
            //window.location.reload(true);
        }
    })
}

function DRSGoFunction() {
    OnDateRangeChanged();
}

function OnDateRangeChanged(isFromBtns) {
    showProgress();

    var _dateRange = document.getElementById("dateRange").value;
    var _Fromdate = $("#Fromdate").data("kendoDatePicker");
    var _Todate = $("#Todate").data("kendoDatePicker");

    var _frmdate = _Fromdate._oldText;
    var _todate = _Todate._oldText;

    var _frmdate = $('#Fromdate').val();
    var _todate = $('#Todate').val();

    k = 0;


    //show GO button only when datarange chaged to 'custom'..J
    //otherwise show next and previous buttons..J
    if (_dateRange != "CUSTOM") {
        $("#DRSGoID").hide();
        $("#btnPrevious").show();
        $("#btnNext").show();
    }
    else {
        $("#DRSGoID").show();
        $("#btnPrevious").hide();
        $("#btnNext").hide();
    }

    //Return if selection is cutom and there is no date values(Last previed data should be shown)..J
    if (_dateRange == "CUSTOM" && (_frmdate == "" || _frmdate == null || _frmdate == undefined) && (_todate == "" || _todate == null || _todate == undefined)) {
        hideProgress();
        return;
    }

    //To load datasets..J
    var topViewsCount = $("#hidViewCount").val();
    var tabViewsCount = $("#hidTabViewCount").val();

    var myval = sessionStorage.getItem(Key + "_" + "CurrentViewId"); //$("#DefaultViewId").val().replace(" ", "");
    var DRSEnabledViewIds = $("#hidDRSEnabledViewIds").val();
    
    $.ajax({
        url: '/Desktop/SetDateRange',
        dataType: "json",
        cache: false,
        async: true,
        type: "GET",
        contentType: 'application/json',
        data: { sRange: _dateRange, par_sStart: _frmdate, par_sEnd: _todate, period: 0, TopViewsCount: topViewsCount, TabViewsCount: tabViewsCount, DRSEnabledViewIds: DRSEnabledViewIds, Key: Key },
        success: function (data) {

            data = data.split(",");

            if (data[0] != '') {
                $('#Fromdate').val(data[0]);
            }
            $('#Todate').val(data[1]);
            $("#btnPrevious").attr('title', data[2]);
            $("#btnNext").attr('title', data[3]);
            $("#btnPrevious").show();
            $("#btnNext").show();

            if (_dateRange == "CUSTOM" && isFromBtns == 'true') {
                $("#btnPrevious").show();
                $("#btnNext").show();
            }
            else {
                if (_dateRange == "CUSTOM") {
                    $("#btnPrevious").hide();
                    $("#btnNext").hide();
                }
            }

            if (_dateRange == "MAKESELECTION") {
                $("#btnPrevious").hide();
                $("#btnNext").hide();
                var calendar = $("#Fromdate").data("kendoDatePicker");
                calendar.value(new Date());
                var calendar1 = $("#Todate").data("kendoDatePicker");
                calendar1.value(new Date());
                $("#Fromdate").data("kendoDatePicker").value("");
                $("#Todate").data("kendoDatePicker").value("");
            }

            if (_dateRange == "ALL") {
                $("#btnPrevious").hide();
                $("#btnNext").hide();

                var calendar = $("#Fromdate").data("kendoDatePicker");
                calendar.value(new Date());
                var calendar1 = $("#Todate").data("kendoDatePicker");
                calendar1.value(new Date());
                $("#Fromdate").data("kendoDatePicker").value("");
                $("#Todate").data("kendoDatePicker").value("");
            }

            if (DRSEnabledViewIds != null && DRSEnabledViewIds != undefined && DRSEnabledViewIds != "") {
                //Desktop Refactoring V2 starts..J
                //showProgress();
                //Refresh DRS enabled views..J
                //var arr = DRSEnabledViewIds.split(',');
                views = $("#hidViewIDs").val();
                var arr = views.split(',');
                for (var i = 0; i < arr.length; i++) {


                    var viewtype = $("#hidViewType_" + arr[i].replace(" ", "")).val();

                    if (viewtype == null || viewtype == undefined || viewtype == "undefined") {
                        var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();
                        var ids = viewIds_and_viewtypes.split("|");

                        for (var j = 0; j < ids.length; j++) {
                            var types = ids[j].split(",");
                            if (types[0].replace(" ", "") == arr[i].replace(" ", "")) {
                                viewtype = types[1];
                                break;
                            }
                        }
                    }


                    if (viewtype == "list") {
                        reLoadViewsV2(arr[i].replace(" ", ""), true);
                    }
                    else if (viewtype == "report") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "report", true);
                    }
                    else if (viewtype == "calendar") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "calendar", true);
                    }
                    else if (viewtype == "map") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "map", true);
                    }
                    else if (viewtype.toLowerCase() == "chart") {
                        var desktopid = DesktopId;
                        var section = "GLOBAL";
                        //var masterViewSelRecord = gid_id;

                        if (desktopid != null) {
                            $.ajax({
                                url: '/Desktop/LoadLinkedChart',
                                cache: false,
                                dataType: "json",
                                type: "GET",
                                async: false,
                                contentType: 'application/json; charset=utf-8',
                                data: { ViewKey: arr[i].replace(" ", ""), DesktopId: desktopid, Section: section, MasterViewSelRecord: "", IsTabView: false, Key: Key },
                                success: function (data) {

                                    if (data != 'failure') {
                                        $('#viewbody' + arr[i].replace(/ /g, '')).html(data);

                                        ResizeChartInsplitter(arr[i].replace(" ", ""));
                                    }
                                    hideProgress();
                                },
                                error: function (data) {
                                }
                            })
                        }
                    }
                }
            }
            hideProgress();
            //Desktop Refactoring V2 ends..J

        },
        error: function (data) {

        }
    });
}

var k = 0;
$("#btnPrevious").click(function () {
    showProgress();
    var _dateRange = document.getElementById("dateRange").value;

    var _Fromdate = $("#Fromdate").data("kendoDatePicker");
    var _Todate = $("#Todate").data("kendoDatePicker");

    var _frmdate = _Fromdate._oldText;
    var _todate = _Todate._oldText;

    var _frmdate = $('#Fromdate').val();
    var _todate = $('#Todate').val();

    if (k != 0) {
        k = k - 1;
    }
    else {
        if (k < 0) {
            k = k - 1;
        }
        else if (k == 0) {
            k = k - 1;
        }
    }

    //To load datasets..J
    var topViewsCount = $("#hidViewCount").val();
    var tabViewsCount = $("#hidTabViewCount").val();

    var myval = sessionStorage.getItem(Key + "_" + "CurrentViewId"); //$("#DefaultViewId").val().replace(" ", "");
    var DRSEnabledViewIds = $("#hidDRSEnabledViewIds").val();
    
    $.ajax({
        url: '/Desktop/SetDateRange',
        dataType: "json",
        cache: false,
        type: "GET",
        contentType: 'application/json',
        data: { sRange: "PREVIOUSPERIOD", par_sStart: _frmdate, par_sEnd: _todate, period: k, TopViewsCount: topViewsCount, TabViewsCount: tabViewsCount, DRSEnabledViewIds: DRSEnabledViewIds, Key: Key, DateRangeSelectedValue: _dateRange },
        success: function (data) {

            data = data.split(",");

            $('#Fromdate').val(data[0]);
            $('#Todate').val(data[1]);

            $('#dateRange').val("CUSTOM");

            //Desktop Refactoring V2 starts..J
            showProgress();
            //Refresh DRS enabled views..J
            //var arr = DRSEnabledViewIds.split(',');

            if (DRSEnabledViewIds != null && DRSEnabledViewIds != undefined && DRSEnabledViewIds != "") {
                views = $("#hidViewIDs").val();
                var arr = views.split(',');
                for (var i = 0; i < arr.length; i++) {


                    var viewtype = $("#hidViewType_" + arr[i].replace(" ", "")).val();

                    if (viewtype == null || viewtype == undefined || viewtype == "undefined") {
                        var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();
                        var ids = viewIds_and_viewtypes.split("|");

                        for (var j = 0; j < ids.length; j++) {
                            var types = ids[j].split(",");
                            if (types[0].replace(" ", "") == arr[i].replace(" ", "")) {
                                viewtype = types[1];
                                break;
                            }
                        }
                    }


                    if (viewtype == "list") {
                        reLoadViewsV2(arr[i].replace(" ", ""), true);
                    }
                    else if (viewtype == "report") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "report", true);
                    }
                    else if (viewtype == "calendar") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "calendar", true);
                    }
                    else if (viewtype == "map") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "map", true);
                    }
                    else if (viewtype.toLowerCase() == "chart") {
                        var desktopid = DesktopId;
                        var section = "GLOBAL";
                        //var masterViewSelRecord = gid_id;

                        if (desktopid != null) {
                            $.ajax({
                                url: '/Desktop/LoadLinkedChart',
                                cache: false,
                                dataType: "json",
                                type: "GET",
                                async: false,
                                contentType: 'application/json; charset=utf-8',
                                data: { ViewKey: arr[i].replace(" ", ""), DesktopId: desktopid, Section: section, MasterViewSelRecord: "", IsTabView: false, Key: Key },
                                success: function (data) {

                                    if (data != 'failure') {
                                        $('#viewbody' + arr[i].replace(/ /g, '')).html(data);

                                        ResizeChartInsplitter(arr[i].replace(" ", ""));
                                    }
                                    hideProgress();
                                },
                                error: function (data) {
                                }
                            })
                        }
                    }

                }
            }
            hideProgress();
            //Desktop Refactoring V2 ends..J
        },
        error: function (data) {

        }
    });
})

$("#btnNext").click(function () {

    showProgress();
    if (k == 0) {
        k = k + 1;
    }
    else {
        k = k + 1;
    }

    var _dateRange = document.getElementById("dateRange").value;

    var _Fromdate = $("#Fromdate").data("kendoDatePicker");
    var _Todate = $("#Todate").data("kendoDatePicker");

    var _frmdate = _Fromdate._oldText;
    var _todate = _Todate._oldText;

    var _frmdate = $('#Fromdate').val();
    var _todate = $('#Todate').val();

    //To load datasets..J
    var topViewsCount = $("#hidViewCount").val();
    var tabViewsCount = $("#hidTabViewCount").val();

    var myval = sessionStorage.getItem(Key + "_" + "CurrentViewId"); //$("#DefaultViewId").val().replace(" ", "");
    var DRSEnabledViewIds = $("#hidDRSEnabledViewIds").val();
       
    $.ajax({
        url: '/Desktop/SetDateRange',
        dataType: "json",
        cache: false,
        type: "GET",
        contentType: 'application/json',
        data: { sRange: "NEXTPERIOD", par_sStart: _frmdate, par_sEnd: _todate, period: k, TopViewsCount: topViewsCount, TabViewsCount: tabViewsCount, DRSEnabledViewIds: DRSEnabledViewIds, Key: Key, DateRangeSelectedValue: _dateRange },
        success: function (data) {

            data = data.split(",");

            $('#Fromdate').val(data[0]);
            $('#Todate').val(data[1]);

            $('#dateRange').val("CUSTOM");

            //Desktop Refactoring V2 starts..J
            showProgress();
            //Refresh DRS enabled views..J
            //var arr = DRSEnabledViewIds.split(',');

            if (DRSEnabledViewIds != null && DRSEnabledViewIds != undefined && DRSEnabledViewIds != "") {
                var arr = views.split(',');
                for (var i = 0; i < arr.length; i++) {


                    var viewtype = $("#hidViewType_" + arr[i].replace(" ", "")).val();

                    if (viewtype == null || viewtype == undefined || viewtype == "undefined") {
                        var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();
                        var ids = viewIds_and_viewtypes.split("|");

                        for (var j = 0; j < ids.length; j++) {
                            var types = ids[j].split(",");
                            if (types[0].replace(" ", "") == arr[i].replace(" ", "")) {
                                viewtype = types[1];
                                break;
                            }
                        }
                    }


                    if (viewtype == "list") {
                        reLoadViewsV2(arr[i].replace(" ", ""), true);
                    }
                    else if (viewtype == "report") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "report", true);
                    }
                    else if (viewtype == "calendar") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "calendar", true);
                    }
                    else if (viewtype == "map") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "map", true);
                    }
                    else if (viewtype.toLowerCase() == "chart") {
                        var desktopid = DesktopId;
                        var section = "GLOBAL";
                        //var masterViewSelRecord = gid_id;

                        if (desktopid != null) {
                            $.ajax({
                                url: '/Desktop/LoadLinkedChart',
                                cache: false,
                                dataType: "json",
                                type: "GET",
                                async: false,
                                contentType: 'application/json; charset=utf-8',
                                data: { ViewKey: arr[i].replace(" ", ""), DesktopId: desktopid, Section: section, MasterViewSelRecord: "", IsTabView: false, Key: Key },
                                success: function (data) {

                                    if (data != 'failure') {
                                        $('#viewbody' + arr[i].replace(/ /g, '')).html(data);

                                        ResizeChartInsplitter(arr[i].replace(" ", ""));
                                    }
                                    hideProgress();
                                },
                                error: function (data) {
                                }
                            })
                        }
                    }
                }
            }
            hideProgress();
            //Desktop Refactoring V2 ends..J
        },
        error: function (data) {

        }
    });
})

function OnPaginationClicked() {

    _paginationClicked = "true";
}

function OnPaginationChanged() {

   

    if (Check_View_IsDirty($("#DefaultViewId").val()))
    {
        return false;
    }

    if (Check_View_IsDirty_Report($("#DefaultViewId").val()))
    {
        return false;
    }

    
    showProgress();
     var myval = $("#DefaultViewId").val().replace(" ", "");
    var _pagevalue = document.getElementById("pagination" + myval).value;

    $.ajax({
        url: '/Desktop/SetGridPagination',
        dataType: "json",
        type: "GET",
        cache: false,
        contentType: 'application/json',
        data: { ViewKey: myval, PageSize: _pagevalue, Key: Key },
        success: function (data) {
            //debugger;

            var _currPageNo = data;
            var grid = $('#grid' + myval).data("kendoGrid");
            //grid.dataSource.query({ page: parseInt(_currPageNo), pageSize: parseInt(_pagevalue) });

            //Desktop Refactoring V2 starts..J
            //Refresh all views..J
            views = $("#hidViewIDs").val();
            var arr = views.split(',');
            for (var i = 0; i < arr.length; i++) {

                if ($("#grid" + arr[i].replace(" ", "")).data("kendoGrid")) {

                    //Load current grid with current Page..J
                    if (arr[i].replace(" ", "") == myval.replace(" ", "")) {
                        reLoadViewsV2(arr[i].replace(" ", ""), true, _currPageNo);
                    }
                    else {
                        reLoadViewsV2(arr[i].replace(" ", ""), true);
                    }
                }
                else if ($("#grid" + arr[i].replace(" ", "")).data("kendoTreeList")) {
                    refreshviewV2(arr[i], arr[i].replace(" ", ""), "report", true);
                }
                else if ($("#calender" + arr[i].replace(" ", "")).data("kendoScheduler")) {
                    refreshviewV2(arr[i], arr[i].replace(" ", ""), "calendar", true);
                }
                else if ($("#map_" + arr[i].replace(" ", "")).length > 0) {
                    refreshviewV2(arr[i], arr[i].replace(" ", ""), "map", true);
                }
                else if ($("#Chart" + arr[i].replace(" ", "")).data("kendoChart")) {

                    var desktopid = DesktopId;
                    var section = "GLOBAL";
                    //var masterViewSelRecord = gid_id;

                    if (desktopid != null) {
                        $.ajax({
                            url: '/Desktop/LoadLinkedChart',
                            cache: false,
                            dataType: "json",
                            type: "GET",
                            async: false,
                            contentType: 'application/json; charset=utf-8',
                            data: { ViewKey: arr[i].replace(" ", ""), DesktopId: desktopid, Section: section, MasterViewSelRecord: "", IsTabView: false, Key: Key },
                            success: function (data) {
                                //debugger
                                if (data != 'failure') {
                                    $('#viewbody' + arr[i].replace(/ /g, '')).html(data);

                                    ResizeChartInsplitter(arr[i].replace(" ", ""));
                                }
                                hideProgress();
                            },
                            error: function (data) {
                            }
                        })
                    }
                }
            }
            //Desktop Refactoring V2 ends..J

            hideProgress();
            grid.table.focus();
        },
        error: function (data) {

        }
    })
    if ($("#QuickSelectCONDITION").length >= 1) {
        document.getElementById("QuickSelectCONDITION").focus();
    }
}

function ResetFunction() {

    var myval = sessionStorage.getItem(Key + "_" + "CurrentViewId"); //$("#DefaultViewId").val().replace(" ", "");

    var _Filtercond = $("#hidFilterTrack").val();
    var FilArray = _Filtercond.split(',');
    var fieldlist = "";

    var Val1 = "";
    for (index = 0; index < FilArray.length; index++) {

        var name = FilArray[index];

        if (name.indexOf("SYS") > -1) {
            $("input[name=" + name + "]").val('');
        }
        if (name.indexOf("TXT") > -1) {
            $("input[name=" + name + "]").val('');
        }
        if (name.indexOf("CHK") > -1) {
            $('select[name="' + name + '"]').val('-9999');
        }
        if (name.indexOf("MLS") > -1) {
            $('select[name="' + name + '"]').val('-9999');
        }
        if (name.indexOf("LNK") > -1) {
            $('select[name="' + name + '"]').val('-9999');
        }
    }

    var QS_FieldName = $("#QuickSelectCONDITION").attr("name");
    if (QS_FieldName != undefined) {

        $("#QuickSelectCONDITION").val('');
    }
    GoFunction();    
}

function GoFunction() {

    if (Check_View_IsDirty(sessionStorage.getItem(Key + "_" + "CurrentViewId"))) {
        return false;
    }

     if (Check_View_IsDirty_Report(sessionStorage.getItem(Key + "_" + "CurrentViewId"))) {
        return false;
    }

    
    showProgress();

    var _Filtercond = $("#hidFilterTrack").val();
    var FilArray = _Filtercond.split(',');
    var fieldlist = "";

    var Val1 = "";
    for (index = 0; index < FilArray.length; index++) {

        var name = FilArray[index];

        if (name.indexOf("SYS") > -1) {
            Val1 = $("input[name=" + name + "]").val();
        }
        if (name.indexOf("TXT") > -1) {
            Val1 = $("input[name=" + name + "]").val();
        }
        if (name.indexOf("CHK") > -1) {
            Val1 = $('select[name="' + name + '"] option:selected').val(); //$('select[name="' + name + '"]');
        }
        if (name.indexOf("MLS") > -1) {
            Val1 = $('select[name="' + name + '"] option:selected').val();
        }
        if (name.indexOf("DTT") > -1) {
            Val1 = $('select[name="' + name + '"] option:selected').val();
        }
        if (name.indexOf("LNK") > -1) {
            if ($('#LNKQuickFilterDropDown' + name).data("kendoComboBox")) {                
                Val1 = $("#LNKQuickFilterDropDown" + name).data("kendoComboBox").value();
            }
            else{
             Val1 = $('select[name="' + name + '"] option:selected').val();
            }           
        }

        var filtertext = "";
        filtertext = name + '*^$#' + Val1;

        if (fieldlist == "") {
            fieldlist = filtertext;
        }
        else {
            fieldlist = fieldlist + "," + filtertext;
        }
    }

    var _DrsControls = $("#hidDRSENABLEDStatus").val();
    var _DrsFromDate = "";
    var _DrsToDate = "";
    var _DRSSelectedVal = "";
    if (_DrsControls == "True") {
        _DrsFromDate = $("input[name=" + "fromdate" + "]").val();
        _DrsToDate = $("input[name=" + "todate" + "]").val();
        _DRSSelectedVal = $("#dateRange").val();
    }

    var QS_FieldName = $("#QuickSelectCONDITION").attr("name");
    var QS_FieldValue = "";
    var QS_Condition = "";

    if (QS_FieldName != undefined) {
        QS_FieldValue = $("#QuickSelectCONDITION").val();
        //QS_Condition = QS_FieldName + "," + QS_FieldValue;
        QS_Condition = QS_FieldValue;
    }

    GoClicked = true;
    var myval = sessionStorage.getItem(Key + "_" + "CurrentViewId"); //$("#DefaultViewId").val().replace(" ", "");

    $.ajax({
        url: '/Desktop/SetViewConditionFromQuickFilters',
        cache: false,
        type: "GET",
        data: { ViewKey: myval, FieldValues: fieldlist, DrsFromDate: _DrsFromDate, DrsToDate: _DrsToDate, QS_Condition: QS_Condition, QS_FieldName: QS_FieldName, DRSSelectedVal: _DRSSelectedVal, Key: Key },
        success: function (data) {

            if ($("#QuickSelectCONDITION").length >= 0) {
                $("#QuickSelectCONDITION").val(data.QS_Condition);
            }
            var _tooltip = data.ToolTip;

            $("#" + data.TableName + myval).attr("title", _tooltip);

            showProgress();

            //Gofunction is there only when desktop has single view so its enough to load current view..J
            if ($('#grid' + myval).data('kendoGrid')) {
                reLoadViewsV2(myval, true, 1);
                $('#grid' + myval).data("kendoGrid").table.focus();
            }
            else if ($("#grid" + myval).data("kendoTreeList")) {
                refreshviewV2(myval, myval.replace(" ", ""), "report", true);
            }
            else if ($("#calender" + myval.replace(" ", "")).data("kendoScheduler")) {
                refreshviewV2(myval, myval.replace(" ", ""), "calendar", true);
            }
            else if ($("#map_" + myval.replace(" ", "")).length > 0) {
                refreshviewV2(myval, myval.replace(" ", ""), "map", true);
            }
            else if ($("#Chart" + myval.replace(" ", "")).data("kendoChart")) {
                reloadChartView(myval);
            }

            hideProgress();
        },
        error: function (data) {
            if (data.responseText.length > 0 || data.responseText != null || data.responseText != undefined) {

            }
        }
    })
}

function GoMultiFilterFunction() {
    if (Check_View_IsDirty(sessionStorage.getItem(Key + "_" + "CurrentViewId"))) {
        return false;
    }

     if (Check_View_IsDirty_Report(sessionStorage.getItem(Key + "_" + "CurrentViewId"))) {
        return false;
    }


    showProgress();
    var _DrsControls = $("#hidDRSENABLEDStatus").val();
    var _DrsFromDate = "";
    var _DrsToDate = "";
    var _DRSSelectedVal = "";
    if (_DrsControls == "True") {
        _DrsFromDate = $("input[name=" + "fromdate" + "]").val();
        _DrsToDate = $("input[name=" + "todate" + "]").val();
        _DRSSelectedVal = $("#dateRange").val();
    }

    var QS_FieldName = $("#QuickSelectCONDITION").attr("name");
    var QS_FieldValue = "";
    var QS_Condition = "";

    if (QS_FieldName != undefined) {
        QS_FieldValue = $("#QuickSelectCONDITION").val();

        QS_Condition = QS_FieldValue;
    }

    var myval = sessionStorage.getItem(Key + "_" + "CurrentViewId");  //$("#DefaultViewId").val().replace(" ", "");

    var grid2 = $("#grid" + myval).data("kendoGrid");
    var _report = $("#grid" + myval).data("kendoTreeList");
    var scheduler = $("#calender" + myval).data("kendoScheduler");
    var map = $("#map_" + myval).length;

    GoClicked = true;

    if (grid2 != null || _report != null || scheduler != null || map > 0) {
        GoFilterDataWithQuickSelect(myval, _DrsFromDate, _DrsToDate, QS_Condition, QS_FieldName, _DRSSelectedVal);
    }
}

function GoFilterDataWithQuickSelect(myval, _DrsFromDate, _DrsToDate, QS_Condition, QS_FieldName, _DRSSelectedVal) {

    $.ajax({
        url: '/Desktop/FilterDataWithQuickSelect',
        cache: false,
        //async: false,
        type: "GET",
        data: { ViewKey: myval, DrsFromDate: _DrsFromDate, DrsToDate: _DrsToDate, QS_Condition: QS_Condition, QS_FieldName: QS_FieldName, DRSSelectedVal: _DRSSelectedVal, Key: Key },
        success: function (data) {

            if ($("#QuickSelectCONDITION").length >= 0) {
                $("#QuickSelectCONDITION").val(data.QS_Condition);
            }
            var _tooltip = data.ToolTip;

            $("#" + data.TableName + myval).attr("title", _tooltip);

            showProgress();
            //Load client views..J
            if (data.DepencyViewIds != null && data.DepencyViewIds != "") {
                
                var dependenceviewids = data.DepencyViewIds + "," + myval;
                var arr = dependenceviewids.split(',');

                //WriteLogToGoClick("If statement", myval, dependenceviewids)

                for (var i = 0; i < arr.length; i++) {

                    if (arr[i] == "") {
                        continue;
                    }

                    var viewtype = $("#hidViewType_" + arr[i].replace(" ", "")).val();

                    if (viewtype == null || viewtype == undefined || viewtype == "undefined") {
                        var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();
                        var ids = viewIds_and_viewtypes.split("|");

                        for (var j = 0; j < ids.length; j++) {
                            var types = ids[j].split(",");
                            if (types[0].replace(" ", "") == arr[i].replace(" ", "")) {
                                viewtype = types[1];
                                break;
                            }
                        }
                    }

                    if (viewtype == "list") {
                        reLoadViewsV2(arr[i].replace(" ", ""), true, 1);
                    }
                    else if (viewtype == "report") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "report", true);
                    }
                    else if (viewtype == "calendar") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "calendar", true);
                    }
                    else if (viewtype == "map") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "map", true);
                    }
                    else if (viewtype.toLowerCase() == "chart") {
                        var desktopid = DesktopId;
                        var section = "GLOBAL";
                        var viewId = arr[i].replace(" ", "");

                        if (desktopid != null) {
                            $.ajax({
                                url: '/Desktop/LoadLinkedChart',
                                cache: false,
                                dataType: "json",
                                type: "GET",
                                async: false,
                                contentType: 'application/json; charset=utf-8',
                                data: { ViewKey: arr[i].replace(" ", ""), DesktopId: desktopid, Section: section, MasterViewSelRecord: "", IsTabView: false, Key: Key },
                                success: function (data) {

                                    if (data != 'failure') {
                                        $('#viewbody' + viewId.replace('/ /g', '')).html(data);

                                        ResizeChartInsplitter(viewId.replace(" ", ""));
                                    }
                                    hideProgress();
                                },
                                error: function (data) {
                                }
                            })
                        }
                    }
                }
            }
            else {
                //var dependenceviewids = data.DepencyViewIds + "," + myval;
                //WriteLogToGoClick("Else Statement", myval, dependenceviewids);
                if ($("#grid" + myval.replace(" ", "")).data("kendoGrid")) {
                    reLoadViewsV2(myval.replace(" ", ""), true);
                }
                else if ($("#grid" + myval.replace(" ", "")).data("kendoTreeList")) {
                    refreshviewV2(myval, myval.replace(" ", ""), "report", true);
                }
                else if ($("#map_" + myval.replace(" ", "")).length > 0) {
                    refreshviewV2(myval, myval.replace(" ", ""), "map", true);
                }
            }

            hideProgress();
        },
        error: function (data) {

        }
    })
}

function ResetMultiFilterFunction() {

    showProgress();
    var myval = sessionStorage.getItem(Key + "_" + "CurrentViewId"); //$("#DefaultViewId").val().replace(" ", "");

    var QS_FieldName = $("#QuickSelectCONDITION").attr("name");
    if (QS_FieldName != undefined) {

        $("#QuickSelectCONDITION").val('');
    }

    GoMultiFilterFunction();

    hideProgress();
}

function ResetGoMultiFilter(myval) {

    $.ajax({
        url: '/Desktop/ResetMultiViewFilter',
        cache: false,
        async: false,
        type: "GET",
        data: { ViewKey: myval, Key: Key },
        success: function (data) {

            //need to reset tooltip of the view..J
            var _tooltip = data.ToolTip;
            $("#" + data.TableName + myval).attr("title", _tooltip);

            showProgress();

            if (data.DepencyViewIds != null && data.DepencyViewIds != "") {

                var dependenceviewids = data.DepencyViewIds + "," + myval;
                var arr = dependenceviewids.split(',');

                for (var i = 0; i < arr.length; i++) {

                    if (arr[i] == "") {
                        continue;
                    }

                    if ($("#grid" + arr[i].replace(" ", "")).data("kendoGrid")) {
                        reLoadViewsV2(arr[i].replace(" ", ""), true, 1);
                    }
                    else if ($("#grid" + arr[i].replace(" ", "")).data("kendoTreeList")) {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "report", true);
                    }
                    else if ($("#map_" + arr[i].replace(" ", "")).length > 0) {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "map", true);
                    }
                    else if ($("#calender" + arr[i].replace(" ", "")).data("kendoScheduler")) {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "calendar", true);
                    }
                    else if ($("#Chart" + arr[i].replace(" ", "")).data("kendoChart")) {

                        var desktopid = DesktopId;
                        var section = "GLOBAL";
                        var viewId = arr[i].replace(" ", "");

                        if (desktopid != null) {
                            $.ajax({
                                url: '/Desktop/LoadLinkedChart',
                                cache: false,
                                dataType: "json",
                                type: "GET",
                                async: false,
                                contentType: 'application/json; charset=utf-8',
                                data: { ViewKey: arr[i].replace(" ", ""), DesktopId: desktopid, Section: section, MasterViewSelRecord: "", IsTabView: false, Key: Key },
                                success: function (data) {

                                    if (data != 'failure') {
                                        $('#viewbody' + viewId.replace('/ /g', '')).html(data);

                                        ResizeChartInsplitter(viewId.replace(" ", ""));
                                    }
                                    hideProgress();
                                },
                                error: function (data) {
                                }
                            })
                        }
                    }
                }
            }
            else {
                if ($("#grid" + myval.replace(" ", "")).data("kendoGrid")) {
                    reLoadViewsV2(myval.replace(" ", ""), true);
                }
                else if ($("#grid" + myval.replace(" ", "")).data("kendoTreeList")) {
                    refreshviewV2(myval, myval.replace(" ", ""), "report", true);
                }
            }

            hideProgress();
        },
        error: function (data) {
            hideProgress();
        }
    })
}

function exportExcel(el) {
    $(el).parent().parent().next().find(".k-grid-excel").click();
}

function exportPdf(el) {
    $(el).parent().parent().next().find(".k-grid-pdf").click();
}

function GetMasterViewSelectedIds(viewid, viewids_with_parentids, CurrSelRecId, CurrSelViewId) {
    //debugger
    viewids_with_parentids = $("#hidViewId_and_ParentIds").val();
    
    var ViewId = viewid;
    var ParentViewIds = "";
    var Parent_selectedIds = "";
    var rowclick = "no";


    if ((CurrSelViewId != "" && CurrSelViewId != null && CurrSelViewId != undefined) && (CurrSelRecId != "" && CurrSelRecId != null && CurrSelRecId != undefined)) {
        Parent_selectedIds = CurrSelViewId + "," + CurrSelRecId;
        rowclick = "yes";
    }

    if (viewids_with_parentids != undefined && viewids_with_parentids != "" && viewids_with_parentids != null) {
        var all_Views = viewids_with_parentids.split("|");
        for (var i = 0; i < all_Views.length; i++) {
            if (all_Views[i].indexOf(viewid) >= 0) {
                var aParent_Info = all_Views[i].split("~");
                if (aParent_Info.length > 1) {
                    ParentViewIds = aParent_Info[1];
                    break;
                }
            }
        }
        if (ParentViewIds != "" && ParentViewIds != null && ParentViewIds != undefined) {

            var all_ParentIds = ParentViewIds.split(",");

            for (var i = 0; i < all_ParentIds.length; i++) {

                if (rowclick == "yes" && all_ParentIds[i] == CurrSelViewId) {
                    continue;
                }

                var gid_id = $("#hidSelRecId_" + all_ParentIds[i]).val();

                if (gid_id == "" || gid_id == undefined || gid_id == null)
                    gid_id = "";

                if (Parent_selectedIds == "")
                    Parent_selectedIds = all_ParentIds[i] + "," + gid_id;
                else
                    Parent_selectedIds = Parent_selectedIds + "|" + all_ParentIds[i] + "," + gid_id;
            }
        }
    }

    return Parent_selectedIds;
}

function selectedDiv(el, viewid) {
    var msg = el.target.id;
    //RN #1873 Multiselect functionality added.
    if (msg.length == 0 || msg.indexOf("chk") > -1) {
        msg = "view" + viewid;
    }

    msg = msg.replace("grid", "view").replace("_active_cell", "");
    msg = $("#" + msg).find(".panel-heading.active");
    //$(".panel-heading.active").css({ "background": "", "color": "" });
    //$(".panel-heading.active").css({ "background": "", "color": "", "border-bottom": "" });
    $(".panel-heading.active").removeClass('ViewHeaderOn');
    $(".panel-heading.active").addClass('ViewHeaderOff');


    //msg.css({ "background": "#428bca", "color": "white" });//old
    // msg.css({ "background": "#CDCDCD", "color": "#666666", "border-bottom": "2px solid #3498DB" });
    msg.removeClass('ViewHeaderOff');
    msg.addClass('ViewHeaderOn');
    sessionStorage.setItem(Key + "_" + "lastViewid", viewid);
    sessionStorage.setItem(Key + "_" + "LastGridViewId", viewid);
    sessionStorage.setItem(Key + "_" + "CurrentViewId", viewid);

}

function LoadFilterAndSortProperties() {

    showProgress();

    var val = sessionStorage.getItem(Key + "_" + "CurrentViewId");

    $.ajax({
        url: "/CreateForm/SetLastSelectedViewKey",
        cache: false,
        data: { ViewKey: val, Key: Key },
        success: function (data) {
            window.location.href = "/LoadViewProperties/LoadViewProperties/" + val + "/Filter" + "/5" + "/False/" + Key;
        },
        error: function (data) {
            hideProgress();
            alert(data.responseText);
        }
    })
    //window.location.href = "/LoadViewProperties/LoadViewProperties/" + val + "/Filter" + "/5" + "/False";
}

function LoadSortProperties() {

    showProgress();

    var val = sessionStorage.getItem(Key + "_" + "CurrentViewId");

    $.ajax({
        url: "/CreateForm/SetLastSelectedViewKey",
        cache: false,
        data: { ViewKey: val, Key: Key },
        success: function (data) {
            window.location.href = "/FilterSort/Index?ViewKey=" + val;
        },
        error: function (data) {
            hideProgress();
            alert(data.responseText);
        }
    })
    //window.location.href = "/FilterSort/Index?ViewKey=" + val;
    //  window.location.href = "/LoadViewFilter/Index?ViewKey=" + val + "&SelectedStatus=0"
}

function RefreshDesktop() {
    //location.reload();

    //To clear cookie sizes when window is resized..J
    var DesktopId = $("#hidSessionDesktopId").val();

    $.ajax({
        url: "/ManageDesktops/GetViewIdsInDesktop",
        type: "POST",
        cache: false,
        async: true,
        data: { DesktopId: DesktopId },
        success: function (data) {

            //To clear views panes sizes..J
            if (data.indexOf(',') > -1) {
                var ViewIDs = data.split(',');
                for (var i = 0; i < ViewIDs.length; i++) {
                    eraseCookie("DS_" + DesktopId + "_view" + ViewIDs[i].replace(' ', ''));
                }
            }

            //To clear top and tab view panes sizes..J
            eraseCookie("DS_" + DesktopId + "_splitterT");
            eraseCookie("DS_" + DesktopId + "_divheight");

            location.reload();
        },
        error: function (data) {

        }
    })
}

function LoadViewProperties() {
    //debugger
    showProgress();

    var val = sessionStorage.getItem(Key + "_" + "CurrentViewId");

    $.ajax({
        url: "/CreateForm/SetLastSelectedViewKey",
        cache: false,
        data: { ViewKey: val, Key: Key },
        success: function (data) {
            window.location.href = "/LoadViewProperties/LoadViewProperties/" + val + "/Property" + "/0" + "/False/" + Key;
        },
        error: function (data) {
            hideProgress();
            alert(data.responseText);
        }
    })
    //window.location.href = "/LoadViewProperties/LoadViewProperties/" + val + "/Property" + "/0" + "/False";
}

function reloadview(readurl, viewid, type, isactive, _rowClick) {

    var _kTreeListurl = readurl;
    var _kTreeListurlarr = _kTreeListurl.split('?');
    var _kTreeListparms = _kTreeListurlarr[1];
    var _kTreeListparmsarr = _kTreeListparms.split('&');
    var _kTreeListViewId = viewid.replace(" ", "");
    var _kTreeListViewKey = "";
    var _kTreeListMasterSelID = "";

    if (_rowClick == undefined) {
        _rowClick = 'yes';
    }

    for (var i = 0; i < _kTreeListparmsarr.length; i++) {

        var _item = _kTreeListparmsarr[i];
        var _key = ""; var _value = "";
        _key = _item.split('=')[0];
        _value = _item.split('=')[1];
        if (_key == "ViewKey") {
            _kTreeListViewKey = _value;
        }
        else if (_key == "MasterSelID") {
            _kTreeListMasterSelID = _value;
        }

    }

    $.ajax({
        url: '/Desktop/ReloadView',
        cache: false,
        dataType: "json",
        type: "GET",
        async: true,
        contentType: 'application/json; charset=utf-8',
        data: { ViewId: _kTreeListViewId, ViewKey: _kTreeListViewKey, MasterSelID: _kTreeListMasterSelID, ViewType: type, IsActive: isactive, RowClick: _rowClick, Key: Key },
        success: function (data) {

            var _viewid = data[1];
            var _html = data[0];
            if (_viewid != null && _html != null) {
                _viewid = _viewid.replace(" ", "");
                $("#viewbody" + _kTreeListViewId).html(_html);

            }
        },
        error: function (xhr) {

        }
    });


}

function refreshview(viewkey, viewid, viewtype, masterselId, isactive, _rowClick) {

    if (_rowClick == undefined) {
        _rowClick = 'yes';
    }

    $.ajax({
        url: '/Desktop/ReloadView',
        cache: false,
        dataType: "json",
        type: "GET",
        async: true,
        contentType: 'application/json; charset=utf-8',
        data: { ViewId: viewid, ViewKey: viewkey, MasterSelID: masterselId, ViewType: viewtype, IsActive: isactive, RowClick: _rowClick, Key: Key },
        success: function (data) {
            var _viewid = data[1];
            var _html = data[0];
            if (_viewid != null && _html != null) {
                _viewid = _viewid.replace(" ", "");
                $("#viewbody" + viewid).html(_html);

            }
        },
        error: function (xhr) {

        }
    });


}

function reloadChartView(viewid, isactive) {
    var type = "CHART";
    $.ajax({
        url: '/Desktop/ReloadChartView',
        cache: false,
        dataType: "json",
        type: "GET",
        async: true,
        contentType: 'application/json; charset=utf-8',
        data: { ViewId: viewid, IsActive: isactive },
        success: function (data) {
            $("#viewbody" + viewid).html(data);
            $('.panel-body').parent().removeClass('z-depthstyle');

            ResizeChartInsplitter(viewid.replace(" ", ""));
            //$("#page-loader").hide();
        },
        error: function (data) {
            ////debugger;
            //  alert(xhr);
        }
    });
}

//Toolbar start
function ViewHeaderPanelOnClick(e, ViewId, TableName) {
    
    var type = TableName; //$(this).attr('id');

    var divItem = $('#' + type + ViewId);

    if (divItem.hasClass('ViewHeaderOn')) {
        //RN tkt #2198 - pagination drop down disappears
        if (_paginationClicked == "true") {
            PageSizeChanged = "true";
        }

        _paginationClicked = "false";
    }
    else {
        //var filetype = type.toString().split("VIE");
        //type = filetype[0];
        sessionStorage.setItem("type", type);
        var viewId = ViewId; //"VIE" + filetype[1];

        $(".panel-heading.active").removeClass('ViewHeaderOn');
        $(".panel-heading.active").addClass('ViewHeaderOff');

        $('.panel-heading.active').parent().removeClass('z-depthstyle');
        $('#view' + viewId).addClass('z-depthstyle');
        if ($('#view' + viewId)[0].childNodes['0']) {

            $("#" + $('#view' + viewId)[0].childNodes['0'].id).removeClass('ViewHeaderOff');
            $("#" + $('#view' + viewId)[0].childNodes['0'].id).addClass('ViewHeaderOn');
            sessionStorage.setItem(Key + "_lastViewid", viewId);

            //sessionStorage.setItem("LastGridViewId", viewId.replace(" ", ""));
            sessionStorage.setItem(Key + "_" + "CurrentViewId", viewId.replace(" ", ""));
        }

        if (_paginationClicked == "false") {
            $.ajax({
                url: '/Desktop/GetSortedFieldControl',
                type: "GET",
                cache: false,
                data: { datafield: "", ViewKey: viewId, Key: Key, ViewMultiSelect: 'NOTFOUND' },
                success: function (data) {

                    if (data.FilterText != null) {

                        $("#QuickFilterBar").html("");
                        $("#QuickFilterBar").html(data.FilterText);
                        if ($("#QuickSelectCONDITION").length >= 1) {
                            //document.getElementById("QuickSelectCONDITION").focus();
                        }
                        SetDocumentHeight();
                        var myval = viewId.replace(" ", "");
                        var _tooltip = data.ToolTip;
                        $("#" + data.TableName + myval).attr("title", _tooltip)

                    }
                    if (data == "") {
                        $("#QuickFilterBar").html("");
                    }
                    //SetDesktopViewsDivHeightInQucikcFilterChange();
                }
            })

        }
        else if (_paginationClicked == "true") {
            PageSizeChanged = "true";
        }

        _paginationClicked = "false";

        //to get the type of the view i.e.,Grid or TreeView or Chart

        if (viewId != undefined && viewId != null) {

            var _grid = $("#grid" + viewId).data("kendoGrid");
            var gid_id = '';
            if (_grid == null || _grid == undefined) {
                _grid = $("#grid" + viewId).data("kendoTreeList");
                if (_grid != null) {
                    gid_id = _grid.element.find('.k-selected').find('td:last').html();
                }
            }
            else {
                gid_id = _grid.element.find('tbody tr:first td:last').html();
            }
            if (gid_id == undefined || gid_id == null) {
                gid_id = '';
            }
            if (gid_id != "" && gid_id == undefined && gid_id == null) {
                sessionStorage.setItem(Key + "_" + "CRL_RecordId", gid_id);
            }
        }

        RowColorPersistance();


        if (viewId != undefined && viewId != null) {
            var report = $("#grid" + viewId).data("kendoTreeList");
            var grid = null;
            if (!report) {
                grid = $('#grid' + viewId).data("kendoGrid");
            }
            if (report) {
                var isExist = false;
                var obj = null;

                $("#grid" + viewId).find('tbody').find('tr').filter(function () {
                    isExist = $(e).css('background-color') === 'rgb(226, 226, 226)';
                    if (isExist)
                        obj = this;
                });
                var gid_id = "";
                if (obj) {
                    $(obj).removeClass("k-alt");
                    $(obj).removeAttr("style");
                    $(obj).removeClass('defaultrowselectionGrey');
                    $(obj).addClass('k-selected');

                    gid_id = $(obj).find('td:last').html();
                }
                else {
                    var currentDataItem = $("#grid" + viewId).data("kendoTreeList").dataItem($("#grid" + viewId).data("kendoTreeList").select());
                    if (currentDataItem != undefined) {
                        gid_id = currentDataItem.GID_ID;
                    }
                }
                sessionStorage.setItem(Key + "_" + "CRL_RecordId", gid_id);
                sessionStorage.setItem(Key + "_" + "LastSelectedRow", gid_id);
                sessionStorage.setItem(Key + "_LastGridViewId", viewId);

                if ($("#grid" + viewId).data("kendoTreeList").select().index() == -1) {
                    if ($("#grid" + viewId).data("kendoTreeList").dataSource.total() != 0) {
                        MakeChildDependencyViewsOnFocus_AsEmpty(viewId);
                    }
                }
                else {
                    if (NavigationClicked == false) {
                        $("#grid" + viewId).find("tbody > tr").eq($("#grid" + viewId).data("kendoTreeList").select().index()).trigger('click');
                    }
                    else {
                        NavigationClicked = false;
                    }
                }
            }

            else if (grid) {
                if (grid.tbody.find('tr').hasClass('defaultrowselectionGrey')) {
                    var obj = grid.tbody.find('tr.defaultrowselectionGrey');
                    grid.tbody.find('tr').removeClass('defaultrowselectionGrey');

                    if (grid.tbody.find('tr').hasClass('k-selected') == false) {
                        obj.addClass('k-selected');
                        //grid.tbody.find('tr:first').addClass('k-selected');
                    }
                }
                var gridView = grid.dataSource.view();

                if (gridView.length > 0) {
                    var selection = grid.select();
                    var dataSource = grid.dataSource;
                    var gid_id = grid.dataItem(selection[0]).GID_ID;
                    sessionStorage.setItem(Key + "_" + "CRL_RecordId", gid_id);
                    sessionStorage.setItem(Key + "_" + "LastSelectedRow", gid_id);
                    sessionStorage.setItem(Key + "_LastGridViewId", viewId);
                }
                if (grid.select().index() == -1) {

                    if (grid.dataSource.total() != 0) {
                        MakeChildDependencyViewsOnFocus_AsEmpty(viewId);
                    }
                }
                else {
                    if (NavigationClicked == false) {
                        $("#grid" + viewId).find("tbody > tr").eq(grid.select().index()).trigger('click');
                    }
                    else {
                        NavigationClicked = false;
                    }
                }
            }

            //tckt #2136 Set view properties list buttons display hide/show when load view data enabled or not..J
            if ($("#divAutoLoadView" + viewId).length > 0) {
                if ($("#divAutoLoadView" + viewId)[0].innerText.indexOf("Click here to load view.") > -1) {
                    $("#btnCreateLinkedList").prop("disabled", true);
                    $("#liVwPrPrint").addClass('disabledLiElements');
                    $("#liVwPrSendByEmail").addClass('disabledLiElements');
                    $("#liVwPrSendToExcel").addClass('disabledLiElements');
                    $("#liVwPrSendToPdf").addClass('disabledLiElements');
                    $("#liVwPrDeleteRecord").addClass('disabledLiElements');
                    $("#liVwPrDeleteAllRecords").addClass('disabledLiElements');
                }
                else {
                    $("#btnCreateLinkedList").prop("disabled", false);
                    $("#liVwPrPrint").removeClass('disabledLiElements');
                    $("#liVwPrSendByEmail").removeClass('disabledLiElements');
                    $("#liVwPrSendToExcel").removeClass('disabledLiElements');
                    $("#liVwPrSendToPdf").removeClass('disabledLiElements');
                    $("#liVwPrDeleteRecord").removeClass('disabledLiElements');
                    $("#liVwPrDeleteAllRecords").removeClass('disabledLiElements');
                }
            }
            else {
                $("#btnCreateLinkedList").prop("disabled", false);
                $("#liVwPrPrint").removeClass('disabledLiElements');
                $("#liVwPrSendByEmail").removeClass('disabledLiElements');
                $("#liVwPrSendToExcel").removeClass('disabledLiElements');
                $("#liVwPrSendToPdf").removeClass('disabledLiElements');
                $("#liVwPrDeleteRecord").removeClass('disabledLiElements');
                $("#liVwPrDeleteAllRecords").removeClass('disabledLiElements');
            }
        }
    }
}

function SetDesktopViewsDivHeightInQucikcFilterChange() {
    var resizedHeight = $(window).height() - $('header').outerHeight() - $(".panel-heading").outerHeight() - $("#QuickToolbarDiv").outerHeight();

    $(".fulldivpscroll").css('height', resizedHeight + 'px !important');
    //set column1 height when no tabs..J
    if ($("#tabheight").length == 0) {
        $("#divheight").height(resizedHeight);
        $("#splittercol1").height(resizedHeight);
        $("#splittercol2").height(resizedHeight);
        $(".k-splitbar.k-state-default.k-splitbar-horizontal.k-splitbar-draggable-horizontal").height(resizedHeight);
    }

    //check for top views..J
    if (topViewCount != 0) {

        if ($("#divheight").data("kendoSplitter") != null) {
            $("#divheight").data("kendoSplitter").trigger("resize");
        }

        if ($("#splittercol1").data("kendoSplitter") != null) {
            $("#splittercol1").data("kendoSplitter").trigger("resize");
        }

        if ($("#splittercol2").data("kendoSplitter") != null) {
            $("#splittercol2").data("kendoSplitter").trigger("resize");
        }
    }

    //remove top space when there are no top views..J
    if (topViewCount == 0) {
        if ($("#tabheight").length > 0) {
            $("#divheight").css('height', '0px');
            $("#tabheight").css('top', '0px');
        }
    }
}

function addNew() {
    var type = sessionStorage.getItem("type");
    if (type == "" || type == null) {
        var type = $('.panel-heading.active:first').attr('id');
        var filetype = type.toString().split("VIE");
        type = filetype[0];
        sessionStorage.setItem("type", type)
    }

    $.ajax({
        url: "/CreateForm/ClearExistingNewSession",
        async: false,
        cache: false,
        data: { File: sessionStorage.getItem("type"), Type: "CRU_" },
        success: function (data) {
            if (data == "success") {
            }
        },
        error: function (data) {
        }
    })
    showProgress();
    //var ViewId = $("#DefaultViewId").val().replace(" ", "");
    var ViewId = sessionStorage.getItem(Key + "_" + "CurrentViewId");
    $.ajax({
        url: '/CreateForm/ClearSelectedFieldsData',
        async: true,
        cache: false,
        data: { ViewKey: ViewId, Key: Key },
        success: function (data) {
            if (data == "success") {
                type = sessionStorage.getItem("type");

                if (type != "" && type != null) {
                    window.location = "/CreateForm/CreateForm/" + sessionStorage.getItem("type") + "/ID/CRU_" + type + "/false/false/" + ViewId + "/false/MASTERID/FORMKEY/FIELD/" + Key;
                }
                else {
                    //var type = $('.panel-heading.active:first').attr('id');
                    var type = $('.panel-heading.active.gridoverflow.ViewHeaderOn').attr('id');   //gridoverflow ViewHeaderOn
                    var filetype = type.toString().split("VIE");
                    type = filetype[0];
                    sessionStorage.setItem("type", type)
                    window.location = "/CreateForm/CreateForm/" + sessionStorage.getItem("type") + "/ID/CRU_" + type + "/false/false/" + ViewId + "/false/MASTERID/FORMKEY/FIELD/" + Key;
                }
                sessionStorage.setItem("type", "");
            }
        },
        error: function (request, status, error) {
            if (request.responseText.length > 0 || request.responseText != null || request.responseText != undefined) {
                //alert(request.responseText);
            }
        }
    })

}

$("body").on('click', ".k-i-collapse", function () {
    $(this).removeClass("k-icon k-i-collapse");
    $(this).addClass("k-icon k-i-expand");
});
$("body").on('click', ".k-i-expand", function () {
    $(this).removeClass("k-icon k-i-expand");
    $(this).addClass("k-icon k-i-collapse");
});
function format1(n, currency) {
    if (n == null)
        return "";
    return currency + "" + n.toFixed(2).replace(/./g, function (c, i, a) {
        return i > 0 && c !== "." && (a.length - i) % 3 === 0 ? "," + c : c;
    });
}

function CreateLink(tableName, type) {
    
    showProgress();

    var ViewId = sessionStorage.getItem(Key + "_" + "CurrentViewId");

    if (sessionStorage.getItem(Key + "_" + "CRL_RecordId") != "" && sessionStorage.getItem(Key + "_" + "CRL_RecordId") != null) {
        $.ajax({
            url: "/CreateForm/SetLastSelectedViewKey",
            cache: false,
            data: { ViewKey: ViewId, Key: Key },
            success: function (data) {
                window.location = "/CreateForm/CreateForm/" + tableName + "/" + sessionStorage.getItem(Key + "_" + "CRL_RecordId") + "/" + type + "/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + Key;
            },
            error: function (data) {
                hideProgress();
                alert(data.responseText);
            }
        })
        //window.location = "/CreateForm/CreateForm/" + tableName + "/" + sessionStorage.getItem("CRL_RecordId") + "/" + type + "/false/false/KEY/false/MASTERID/FORMKEY/FIELD";
    }
    else {
        hideProgress();
    }
}

window.onload = function () {
    if ($("#QuickSelectCONDITION").length >= 1) {
        //document.getElementById("QuickSelectCONDITION").focus();
    }
};

$('body').on('keydown', '#QuickSelectCONDITION', function (event) {
    if (event.keyCode == 13) {
        //debugger;
        var _validID = $("#GoID").text();
        var _validMID = $("#GoMID").text();

        if (_validID.trim() == "Go") {
            GoFunction();
        }
        else if (_validMID.trim() == "Go") {
            GoMultiFilterFunction();
        }
    }
});


//4
function LoadDesktopDocumentReady4(){

//VT -- set foucus to master grid in desktop load    
    var _mastergrid = $("#grid" + mastergridid).data("kendoGrid");
    var _masterreport = $("#grid" + mastergridid).data("kendoTreeList");
    if (_mastergrid != null) {
        _mastergrid.table.focus();
        var _viewCount = $("#hidViewCount").val();
        $('#view' + mastergridid).addClass("z-depthstyle");
    }   

    if ($("#hidViewCount").val() > 1) {
        $('.multiviews').show();
    }
    else {
        //$('.multiviews').hide();      //SB showing desktop operations always
    }

    $('#CMB_DESKTOPLIST').val($("#hidSessionDesktopId").val());

    if ($("#hidIsValid").val() == 'True') {
        $('#pnlNoViews').hide();
        $('.fulldiv30').show();
        $('.hidWhenInValid').show();
    }
    else {
        if ($("#cusDesktop").val() != null && $("#cusDesktop").val() != '')
            $('#pnlNoViews').hide();
        else
            $('#pnlNoViews').show();

        $('.fulldiv30').hide();
        $('.hidWhenInValid').hide();
    }

    if ($("#hidLastOpenFolderId").val() != "") {
        var folderid = $("#hidLastOpenFolderId").val();
        //Encrypt & symbol in foldername..J
        if (folderid.indexOf("&amp;") > -1) {
            folderid = folderid.replace("&amp;", "\\&");
        }
        else if (folderid.indexOf("&") > -1) {
            folderid = folderid.replace("&", "\\&");
        }

        if ($('a#' + folderid)) {
            $('a#' + folderid).addClass("menu-open");
        }

        if ($("#hidModelDesktopId").val() != "") {
            var desktopid = $("#hidModelDesktopId").val();
            if ($('a#' + desktopid)) {
                $('a#' + desktopid).addClass("selectedDesktopClass");
            }
        }
    }
    
    if ($("#hidisLoadFromSession").val() == 'True') {
        if (sessionStorage.getItem("CurrentPopOutViewId") != null && sessionStorage.getItem("CurrentPopOutViewId") != undefined) {
            var viewId = sessionStorage.getItem("CurrentPopOutViewId");

            if ($("#btnpopoutin" + viewId).length > 0) {
                $("#btnpopoutin" + viewId).click();
            }
        }
    }
}

$('input[name=ViewOption]').change(function () {
    var selectedval = $('input[name=ViewOption]:checked').val()
    if (selectedval == "RecordsFrom1toN") {
        $("#TXT_FIRSTNRECORDS").removeAttr("disabled");
        $("#TXT_FIRSTNRECORDS").focus();
    }
    else {
        $("#TXT_FIRSTNRECORDS").attr("disabled", "disabled");
    }
});

function Printall(mode, target) {
    showProgress();
    var ViewId = sessionStorage.getItem(Key + "_" + "CurrentViewId");//$("#DefaultViewId").val().replace(" ", "");
    //ViewId = sessionStorage.getItem(Key + "_" + "CurrentViewId");

    var index = sessionStorage.getItem("Index");
    var masterViewSelId = sessionStorage.getItem($("#hidKey").val() + "_" + "RecordId");
    ViewId = ViewId;
    var selectedval;
    if (index === null || index === undefined)
        index = 1;
    if (masterViewSelId === null || masterViewSelId === undefined)
        masterViewSelId = "";
    if (selectedval === undefined || selectedval === null)
        selectedval = "";
    $.ajax({
        url: '/ViewPrint/SendToExcel',
        cache: false,
        dataType: 'json',
        data: { Mode: mode, Target: target, ViewKey: ViewId, Index: index, MasterViewSelId: masterViewSelId, Key: Key },
        success: function (data) {           
            if (data == "DOESNOTSUPPORTED") {                             
                alert("'Send To Excel' for this view is not supported.");
            }
            else if (data == "EMAILSENT")
            {
                alert("The 'Desktop' has been sent to Email.")
            }
            else if (data == "EMAILSENTFAILED")
            {
                alert("The user does not have a valid Email address");

            }
            else {
                alert("The 'Send To Excel' request has been submitted successfully.");
            }
        
            hideProgress();
           // RefreshDesktop();
            //location.reload();
            var btnGroup = document.getElementById("btnGroup");
            btnGroup.classList.remove("open");
            btnGroup.setAttribute("aria-expanded", "false");

        },
        error: function (data) {
            hideProgress();
        }
    })



}


function Print(mode, target) {

    showProgress();
    if (target == 'excel') {
        $('#myModalPrint').children().attr('style', 'margin-left: 30%; width: 50%; margin-top: 8%;');
    }
    else {
        $('#myModalPrint').children().attr('style', 'margin-left: 30%; width: 50%; margin-top: 15%;');
    }
    var ViewId = sessionStorage.getItem(Key + "_" + "CurrentViewId");//$("#DefaultViewId").val().replace(" ", "");
    //ViewId = sessionStorage.getItem(Key + "_" + "CurrentViewId");

    var index = sessionStorage.getItem("Index");
    var masterViewSelId = sessionStorage.getItem($("#hidKey").val() + "_" + "RecordId");
    ViewId = ViewId;

    $('#TXT_FIRSTNRECORDS').val("");
    if (target == "email") {
        target = "HTMLEMAIL";
        $("#divExcel").hide();
        $("#divEmail").show();
    }
    else if (target == "excel") {
        target = "EXCEL";
        $("#divEmail").hide();
        $("#TXT_SplitAddressLines").val("3");
        $("#divExcel").show();
    }
    else if (target == "pdf") {
        target = "PDF";
        $("#divEmail").hide();
        $("#divExcel").hide();

    }
    else if (target == "print") {
        $("#divEmail").hide();
        $("#divExcel").hide();
    }
    var selectedval = $('input[name=ViewOption]:checked').val()
    if (selectedval == "RecordsFrom1toN") {
        $("#TXT_FIRSTNRECORDS").removeAttr("disabled");
    }
    else {
        $("#TXT_FIRSTNRECORDS").attr("disabled", "disabled");
    }

    $("#hdnButtonClickEventMode").val(target);
    sessionStorage.setItem('PrintActionMode', mode);


    if (index == null || index == undefined)
        index = 1;
    if (masterViewSelId == null || masterViewSelId == undefined)
        masterViewSelId = "";
    if (selectedval == undefined || selectedval == null)
        selectedval = "";
    //debugger;

    $.ajax({
        url: '/ViewPrint/Print',
        cache: false,
        data: { Mode: mode, Target: target, ViewKey: ViewId, Index: index, MasterViewSelId: masterViewSelId, Key: Key },
        success: function (data) {
            //debugger
            if (data.ViewOption1_Enabled == false)
                $('input[name=ViewOption][value=AllRecords]').prop('disabled', true);
            else
                $('input[name=ViewOption][value=AllRecords]').prop('disabled', false);

            if (data.ViewOption2_Enabled == false)
                $('input[name=ViewOption][value=LoadedPage]').prop('disabled', true);
            else
                $('input[name=ViewOption][value=LoadedPage]').prop('disabled', false);

            if (data.ViewOption3_Enabled == false)
                $('input[name=ViewOption][value=RecordsFrom1toN]').prop('disabled', true);
            else
                $('input[name=ViewOption][value=RecordsFrom1toN]').prop('disabled', false);

            $('#LBL_Title').text(data.Title);
            $('#LBL_PrintTitle').text(data.LabelTitle);
            $('input[name=ViewOption][value="' + data.ViewOption + '"]').prop('checked', true);
            $('#Index').val(data.Index);
            $('#Mode').val(data.Mode);
            if (data.Mode == "VIEW") {
                $('#LBL_ViewOptions').hide();
                $('#LBL_NonListView').hide();
            }
            else {
                $('#LBL_ViewOptions').text(data.ViewOptionsLabel).show();
                $('#LBL_NonListView').text(data.NonListViewLabel).show();
            }

            if (data.Target == "HTMLEMAIL") {
                $("#TXT_Subject").val(data.TXT_SubjectText);
            }
            if (data.LBL_WarningVisible == true) {
                $("#divWarningLabel").show();
            }
            else {
                $("#divWarningLabel").hide();
            }
            if (data.LBL_NoPCLinkWarningVisible == true) {
                $("#divPCLinkWarningLabel").show();
            }
            else {
                $("#divPCLinkWarningLabel").hide();
            }

            $("#myModalPrint").modal('show');

            if (data.divOptionsVisible == false) {
                $("#divOptions").hide();
            }
            else {
                $("#divOptions").show();
            }

            if (data.Target == "HTMLEMAIL") {
                $("#BTN_OK").attr('title', 'Email');
            }
            else if (data.Target == "PDF") {
                $("#BTN_OK").attr('title', 'Pdf');
            }
            else if (data.Target == "EXCEL") {
                $("#BTN_OK").attr('title', 'Excel');
            }
            else {
                $("#BTN_OK").attr('title', 'Print');
            }

            if (data.ViewType == null || data.ViewType == undefined)
                data.ViewType = '';
            if ((data.ViewType.toLowerCase() == "calweek" || data.ViewType.toLowerCase() == "calday" || data.ViewType.toLowerCase() == "calmonth") && (target.toLowerCase() == "print" || target.toLowerCase() == "pdf")) {
                $("#BTN_OK").trigger("click");
            }
            hideProgress();
        },
        error: function (data) {
            hideProgress();
        }
    })
}

function OnMetaDataClick() {
    showProgress();
    var ViewId = sessionStorage.getItem(Key + "_ViewId");
    $.ajax({
        url: '/ViewPrint/ShowMetaData',
        cache: false,
        data: { ViewKey: ViewId, Key: Key },
        success: function (data) {
            var model = $("#ModalMetaData");
            data = data.replace(/\r\n/g, "<br/>");
            model.find('.modal-body').html(data);
            model.modal('show');
            hideProgress();
        },
        error: function (data) {
            hideProgress();
        }
    })
}

function onOkClick() {
    // var ViewId = sessionStorage.getItem("ViewId");
    //debugger;
    showProgress();
    var ViewId = $("#DefaultViewId").val().replace(" ", "");
    var maxrecords = $('#TXT_FIRSTNRECORDS').val();
    var selectedval = $('input[name=ViewOption]:checked').val()
    var index = $('#Index').val();
    var mode = $('#Mode').val();
    maxrecords = parseInt(maxrecords);
    if (isNaN(maxrecords))
        maxrecords = 0;
    if (selectedval == 'RecordsFrom1toN') {
        if ((maxrecords < 1) || (maxrecords > 10000)) {
            //alert("Please enter a number between 1 and 10000 for the number of records to print or select a different option.");
            $("#PNL_MessageBox").show();
            $("#LBL_MsgBoxTitle").text("Selltis");
            $("#LBL_MsgBoxMessage").text("Please enter a number between 1 and 10000 for the number of records to print or select a different option.");
            $("#BTN_MsgBox1").show();
            $("#BTN_MsgBox1").val("OK");
            $("#BTN_MsgBox2").hide();
            $("#BTN_MsgBox3").hide();
            hideProgress();
            return false;
        }
    }
    var Target = $("#hdnButtonClickEventMode").val();

    var TXT_ToText = $("#TXT_To").val();
    var TXT_CcText = $("#TXT_Cc").val();
    var TXT_SubjectText = $("#TXT_Subject").val();
    var EDT_FIRSTNRECORDSText = $("#TXT_FIRSTNRECORDS").val();
    var rdb_AllRecords = $("#AllRecords").prop('checked');
    var rdb_LoadedPage = $("#LoadedPage").prop('checked');
    var rdb_RecordsFrom1toN = $("#RecordsFrom1toN").prop('checked');
    var SEL_VIEWOPTIONSSelectedIndex;
    if (rdb_AllRecords) {
        SEL_VIEWOPTIONSSelectedIndex = 0;
    }
    else if (rdb_LoadedPage) {
        SEL_VIEWOPTIONSSelectedIndex = 1;
    }
    else if (rdb_RecordsFrom1toN) {
        SEL_VIEWOPTIONSSelectedIndex = 2;
    }

    sessionStorage.setItem("TXT_To", TXT_ToText);
    sessionStorage.setItem("TXT_Cc", TXT_CcText);
    sessionStorage.setItem("TXT_Subject", TXT_SubjectText);
    sessionStorage.setItem("Target", Target);
    sessionStorage.setItem("EDT_FIRSTNRECORDSText", EDT_FIRSTNRECORDSText);
    sessionStorage.setItem("SEL_VIEWOPTIONSSelectedIndex", SEL_VIEWOPTIONSSelectedIndex);

    if (Target == "HTMLEMAIL") {
        if (TXT_ToText.trim() == "" && TXT_CcText.trim() == "") {
            $("#PNL_MessageBox").show();
            $("#LBL_MsgBoxTitle").text("Selltis");
            $("#LBL_MsgBoxMessage").text("Please enter at least one To or Cc e-mail address.");
            $("#BTN_MsgBox1").show();
            $("#BTN_MsgBox1").val("OK");
            $("#BTN_MsgBox2").hide();
            $("#BTN_MsgBox3").hide();
            hideProgress();
            return false;
        }
    }

    sessionStorage.setItem("msgBoxPurpose", "ShowExcel");
    $.ajax({
        type: 'GET',
        cache: false,
        url: '/Desktop/GetExcelData_Ack',
        data: { Target: Target, EDT_FIRSTNRECORDS: EDT_FIRSTNRECORDSText, SEL_VIEWOPTIONS_SelectedIndex: SEL_VIEWOPTIONSSelectedIndex },
        success: function (strResult) {
            if (strResult != "") {
                $("#PNL_MessageBox").show();
                $("#LBL_MsgBoxTitle").text("");
                $("#LBL_MsgBoxMessage").text(strResult);
                $("#BTN_MsgBox1").show();
                $("#BTN_MsgBox1").val("OK");
                $("#BTN_MsgBox2").val("Don't show again");
                $("#BTN_MsgBox3").hide();
            }
            else {
                ShowExcel();
            }
            hideProgress();
        },
        error: function (data) {
            hideProgress();
        }
    });

}

function DisplayMessageBox(data) {

    if (data.PNL_MessageBoxVisible == true) {

        $("#PNL_MessageBox").show();
        $("#LBL_MsgBoxTitle").text(data.LBL_MsgBoxTitleText);
        var _LBL_MsgBoxMessage = data.LBL_MsgBoxMessageText.replace("/n/n", "</br></br>").replace("/n", "</br>");
        $("#LBL_MsgBoxMessage").html(_LBL_MsgBoxMessage);

        if (data.BTN_MsgBox1Visible == true) {
            $("#BTN_MsgBox1").show();

            $("#BTN_MsgBox1").attr('value', data.BTN_MsgBox1Text);
            $("#BTN_MsgBox1").prop('value', data.BTN_MsgBox1Text);
            $("#BTN_MsgBox1").html(data.BTN_MsgBox1Text);
        }
        else {
            $("#BTN_MsgBox1").hide();
        }

        if (data.BTN_MsgBox2Visible == true) {
            $("#BTN_MsgBox2").show();

            $("#BTN_MsgBox2").attr('value', data.BTN_MsgBox2Text);
            $("#BTN_MsgBox2").prop('value', data.BTN_MsgBox2Text);
            $("#BTN_MsgBox2").html(data.BTN_MsgBox2Text);
        }
        else {
            $("#BTN_MsgBox2").hide();
        }

        if (data.BTN_MsgBox3Visible == true) {
            $("#BTN_MsgBox3").show();

            $("#BTN_MsgBox3").attr('value', data.BTN_MsgBox3Text);
            $("#BTN_MsgBox3").prop('value', data.BTN_MsgBox3Text);
            $("#BTN_MsgBox3").html(data.BTN_MsgBox3Text);
        }
        else {
            $("#BTN_MsgBox3").hide();
        }

    }
}

function onBTN_MsgBox1Click() {

    //debugger
    if ($("#BTN_MsgBox1").val() == "OK") {
        $("#PNL_MessageBox").hide();
        return;
    }

    //debugger;
    ShowExcel();
}

function onBTN_MsgBox2Click() {

    var ShowExcel = sessionStorage.getItem("msgBoxPurpose");
    if (ShowExcel == "ShowExcel") {
        $.ajax({
            url: '/Desktop/DontShowAgain_ExcelOpt',
            cache: false,
            data: { '_msgPurpose': 'FinalOkMessage' },
            success: function () {
                ShowExcel();
            }
        });
    }
    $("#PNL_MessageBox").hide();
}

function onBTN_MsgBox3Click() {
    $("#PNL_MessageBox").hide();
}

function onBTN_MsgBox3Click_Desktop() {
    $("#PNL_MessageBox_Desktop").hide();
    onCloseMsgBox('PNL_MessageBox_Desktop');
}

function ShowExcel() {
    var ShowExcel = sessionStorage.getItem("msgBoxPurpose");
    var selectedval = $('input[name=ViewOption]:checked').val();

    if (selectedval == '' || selectedval == null || selectedval == undefined)
        selectedval = '';

    var maxrecords = $('#TXT_FIRSTNRECORDS').val();
    if (maxrecords == '' || maxrecords == null || maxrecords == undefined)
        maxrecords = 0;

    var ViewId = sessionStorage.getItem(Key + "_" + "CurrentViewId");//$("#DefaultViewId").val().replace(" ", "");
    if (ViewId == '' || ViewId == null || ViewId == undefined)
        ViewId = $("#hidDefaultViewId").val();
    var index = $('#Index').val();
    if (index == '' || index == null || index == undefined)
        index = 0;
    var mode = $('#Mode').val();
    if (mode == '' || mode == null || mode == undefined)
        mode = 'view';
    if (Key == '' || Key == null || Key == undefined)
        Key = $("#hidKey").val();

    if (ShowExcel == "ShowExcel") {
        $('#TXT_FIRSTNRECORDS').val("");
        $('#TXT_To').val("");
        $('#TXT_Cc').val("");
        $('#TXT_Subject').val("");
        $("#myModalPrint").modal('hide');
        window.open("/ViewPrint/DisplayViewPrint?ViewOption=" + selectedval + "&MaxRecords=" + maxrecords + "&Index=" + index + "&ViewKey=" + ViewId + "&Mode=" + mode + "&Key=" + Key, "_blank", "top=100,left=100,width=1200, height=550,resizable=1,scrollbars=1");
    }
}

function Navigation(viewid, navigateval, isFromKeyDown) {
    //debugger;
    if (Check_View_IsDirty(viewid)) {
        return false;
    }

     if (Check_View_IsDirty_Report(viewid)) {
        return false;
    }
    

    showProgress();

    NavigationClicked = true;

    var _grid = $("#grid" + viewid).data("kendoGrid");
    var _currentPage = _grid.dataSource._page;
    var requestedPage = 1;
    var lastpageno;

    $.ajax({
        url: '/Common/GetLastPageNumber',
        //async: false,
        cache: false,
        data: { ViewKey: viewid, NavigationValue: navigateval, CurrentPageNumber: _currentPage, Key: Key },
        success: function (data) {
            //debugger
            if (isFromKeyDown != undefined && isFromKeyDown != null && isFromKeyDown != '') {
                NavigateValue = navigateval;
                IsNavigateSuccess = true;
            }

            var result = data.split('|');
            lastpageno = Number(result[0]);
            var lastselectedindex = "";
            if (Number(result[1]) != '' && Number(result[1]) > 1) {

                _currentPage = Number(result[1]);
            }

            switch (navigateval) {
                case 'first':
                    requestedPage = 1;
                    break;
                case 'previous':
                    requestedPage = _currentPage - 1;
                    break;
                case 'next':
                    requestedPage = _currentPage + 1;
                    break;
                case 'last':
                    requestedPage = lastpageno;
                    break;
            }


            //V2 start
            showProgress();

            //Refresh dependency views..J

            if (result[2] != null & result[2] != "") {

                var dependenceviewids = result[2] + "," + viewid;

                var arr = dependenceviewids.split(',');

                for (var i = 0; i < arr.length; i++) {

                    if (arr[i] == "") {
                        continue;
                    }

                    var viewtype = $("#hidViewType_" + arr[i].replace(" ", "")).val();

                    if (viewtype == null || viewtype == undefined || viewtype == "undefined") {
                        var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();
                        var ids = viewIds_and_viewtypes.split("|");

                        for (var j = 0; j < ids.length; j++) {
                            var types = ids[j].split(",");
                            if (types[0].replace(" ", "") == arr[i].replace(" ", "")) {
                                viewtype = types[1];
                                break;
                            }
                        }
                    }

                    if (viewtype == "list") {

                        //Load current grid with current page number..J
                        if (arr[i].replace(" ", "") == viewid.replace(" ", "")) {
                            reLoadViewsV2(arr[i].replace(" ", ""), true, requestedPage);
                        }
                        else {
                            reLoadViewsV2(arr[i].replace(" ", ""), true);
                        }
                    }
                    else if (viewtype == "report") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "report", true);
                    }
                    else if (viewtype == "calendar") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "calendar", true);
                    }
                    else if (viewtype == "map") {
                        refreshviewV2(arr[i], arr[i].replace(" ", ""), "map", true);
                    }
                    else if (viewtype.toLowerCase() == "chart") {

                        if (arr[i].replace(" ", "") == viewid.replace(" ", "")) {
                            continue;
                        }
                        else {
                            var desktopid = DesktopId;
                            var section = "GLOBAL";
                            //var masterViewSelRecord = gid_id;

                            if (desktopid != null) {
                                $.ajax({
                                    url: '/Desktop/LoadLinkedChart',
                                    cache: false,
                                    dataType: "json",
                                    type: "GET",
                                    async: false,
                                    contentType: 'application/json; charset=utf-8',
                                    data: { ViewKey: arr[i].replace(" ", ""), DesktopId: desktopid, Section: section, MasterViewSelRecord: "", IsTabView: false, Key: Key },
                                    success: function (data) {

                                        if (data != 'failure') {
                                            $('#viewbody' + arr[i].replace(" ", "").replace('/ /g', '')).html(data);

                                            ResizeChartInsplitter(arr[i].replace(" ", "").replace(" ", ""));
                                        }
                                        hideProgress();
                                    },
                                    error: function (data) {
                                    }
                                })
                            }
                        }
                    }
                }

            }
            else {
                if ($("#grid" + viewid.replace(" ", "")).data("kendoGrid")) {
                    reLoadViewsV2(viewid.replace(" ", ""), true, requestedPage);
                }
            }

            var IsArrowClicked = sessionStorage.getItem("ArrowClicked");
            if (IsArrowClicked == null || IsArrowClicked == false) {
                $("#grid" + viewid).find('tbody tr:first').addClass('k-selected');
                $("#grid" + viewid).find('tbody tr:first td:first').addClass('k-focus');
            }
            else {
                sessionStorage.removeItem("ArrowClicked");
            }

            _grid.table.focus();

            hideProgress();

        },
        error: function (request, status, error) {

        }
    })
}

function DesktopChange(desktopid) {
    sessionStorage.setItem(Key + "_" + "CalRecordId", "");
    sessionStorage.setItem(Key + "_" + "CRL_RecordId", "");
    sessionStorage.setItem(Key + "_" + "LastSelectedRow", "");
    showProgress();
    $.ajax({
        url: '/Desktop/GetSection',
        async: false,
        cache: false,
        data: { FolderId: $("#hidWebClientButtonId").val(), DesktopId: desktopid },
        success: function (data) {
            if (data != '') {
                window.location.href = '/Desktop/LoadDesktop/' + desktopid + '/false/' + $("#hidWebClientButtonId").val() + '/HISTORYKEY/' + data;
            }
            else {
                window.location.href = '/Desktop/LoadDesktop/' + desktopid + '/false/' + $("#hidWebClientButtonId").val() + '/HISTORYKEY';
            }
        },
        error: function (data) {
            //alert(data.responseText);
        }
    })
}

function btnFavorites() {
    var labelName = $("#hidTitle").val();

    //Encrypt single quote(')..J
    if (labelName.indexOf("&#39;") > -1) {
        labelName = labelName.replace("&#39;", "'");
    }

    var id = DesktopId;
    var section = $("#hidSection").val();
    showProgress();
    $.ajax({
        url: '/Common/AddToFavorites',
        cache: false,
        async: true,
        data: { DesktopName: labelName, DesktopId: id, Section: section },
        success: function (data) {
            if (data == "Deleted") {
                $("#iconFavorites").removeClass();
                $("#iconFavorites").addClass("fa fa-heart-o");
                $('#btnFavorites').prop('title', 'Add to Favorites');
            }
            else {
                $("#iconFavorites").removeClass();
                $("#iconFavorites").addClass("fa fa-heart");
                $('#btnFavorites').prop('title', 'Remove from favorites');
            }

            location.reload();
        },
        error: function (data) {
            //alert(data.responseText);
        }
    })
}

function SelectGridOnTabSelection(e, tablename, viewId, viewtype, tab_indx, autoload) {    

    ViewHeaderPanelOnClick($("#" + tablename + viewId), viewId, tablename);  //Apply view header style and save session details for selected record and view id..J

    sessionStorage.setItem("type", tablename);

    var _selrecid_in_parent_view = GetMasterViewSelectedIds(viewId, viewids_With_ParentIds);   

    //$("#DefaultViewId").val(viewId.replace(" ", ""));
    $("#DefaultViewId").val(viewId);

    if ($('#nodatamsg' + viewId.replace(" ", "")).length > 0) {
        $('#nodatamsg' + viewId.replace(" ", "")).text(' Loading...');
    }

    sessionStorage.setItem(Key + "_" + "CurrentViewId", viewId);
    var hdnActiveTabs = $('#hdnActiveTab').val();
    $('#hdnActive').val(viewId.replace(" ", ""));
    $('#hdnActiveTab').val(hdnActiveTabs + "\n" + viewId.replace(" ", ""));

    //$(".panel-heading.active").removeClass('ViewHeaderOn');
    //$(".panel-heading.active").addClass('ViewHeaderOff');

    //$('.panel-heading.active').parent().removeClass('z-depthstyle');

    IsMasterViewRowSelected = undefined;

    //if (_paginationClicked == "false") {
    //    $.ajax({
    //        url: '/Desktop/GetSortedFieldControl',
    //        type: "GET",
    //        cache: false,
    //        data: { datafield: "", ViewKey: viewId.replace(" ", ""), Key: Key, ViewMultiSelect: 'NOTFOUND' },
    //        success: function (data) {
    //            if (data.FilterText != null) {

    //                $("#QuickFilterBar").html("");
    //                $("#QuickFilterBar").html(data.FilterText);
    //                //if ($("#QuickSelectCONDITION").length >= 1) {
    //                //    //document.getElementById("QuickSelectCONDITION").focus();
    //                //}

    //                SetDocumentHeight();
    //                var myval = viewId.replace(" ", "");
    //                var _tooltip = data.ToolTip;
    //                $("#" + data.TableName + myval).attr("title", _tooltip);
    //            }
    //        }
    //    })

    //}

    _paginationClicked = "false";

    $.ajax({
        url: '/Desktop/SaveSelectedTabInfo',
        async: false,
        cache: false,
        data: { TabIndex: tab_indx, LastSelectedViewId: viewId.replace(" ", ""), Key: Key },
        success: function (data) {

        },
        error: function (data) {
            //alert(data.responseText);
        }
    })

    //if ($(e)[0]) {
    //    //debugger
    //    var id = $(e)[0].id.replace("aview", "");
    //    var id = tablename + id;
    //    //$("#" + id).css({ "background": "#428bca", "color": "white" });//old
    //    //$("#" + id).css({ "background": "#CDCDCD", "color": "#666666", "border-bottom": "2px solid #3498DB" });

    //    $("#" + id).removeClass('ViewHeaderOff');
    //    $("#" + id).addClass('ViewHeaderOn');
    //    sessionStorage.setItem(Key + "_lastViewid", viewId.replace(" ", ""));
    //    $("#" + id).parent().addClass('z-depthstyle');
    //}

    sessionStorage.setItem(Key + "_" + "TabClicked", true);

    if (autoload == "0") {
        return;
    }


    switch (viewtype) {
        case "LIST":

            var viewtype = $("#hidViewType_" + viewId.replace(" ", "")).val();

            if (viewtype == null || viewtype == undefined || viewtype == "undefined") {
                var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();
                var ids = viewIds_and_viewtypes.split("|");

                for (i = 0; i <= ids.length; i++) {
                    var types = ids[i].split(",");
                    if (types[0].replace(" ", "") == viewId.replace(" ", "")) {
                        viewtype = types[1];
                        break;
                    }
                }
            }
            var grid_report_object = null;

            if (viewtype == "list") {

                grid_report_object = $("#grid" + viewId.replace(" ", "")).data("kendoGrid");

                //SB 08-23-2017 Ticket #1717
                //var gridState = kendo.stringify(grid_report_object.getOptions());   //Getting Grid options to reuse

                if (grid_report_object) {
                    //view already rendered so call the read action
                    //refreshGrid(viewId, _selrecid_in_parent_view, "yes");

                    //reLoadViewsV2(viewId.replace(" ", ""), false);
                    refreshviewV2(viewId, viewId.replace(" ", ""), "list", false);
                    $('#grid' + viewId.replace(" ", "")).data("kendoGrid").table.focus();
                }
                else {
                    reloadGrid(viewId, _selrecid_in_parent_view, "yes");
                    $('#grid' + viewId.replace(" ", "")).data("kendoGrid").table.focus();
                }

                //SB 08-23-2017 Ticket #1717
                //grid_report_object.setOptions(JSON.parse(gridState));   //Setting Grid options after fetching data
            }

            break;
        case "REPORT":

            var viewtype = $("#hidViewType_" + viewId.replace(" ", "")).val();

            if (viewtype == null || viewtype == undefined || viewtype == "undefined") {
                var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();
                var ids = viewIds_and_viewtypes.split("|");

                for (i = 0; i <= ids.length; i++) {
                    var types = ids[i].split(",");
                    if (types[0].replace(" ", "") == viewId.replace(" ", "")) {
                        viewtype = types[1];
                        break;
                    }
                }
            }
            var grid_report_object = null;

            if (viewtype == "report") {
                grid_report_object = $("#grid" + viewId.replace(" ", "")).data("kendoTreeList");

                if (grid_report_object) {
                    //hide 'No records to display' in report when click on tabed view..J
                    $(grid_report_object.wrapper).find('.k-status').css("display", "none");
                    //reloadReport(viewId, _selrecid_in_parent_view, "yes");

                    refreshviewV2(viewId, viewId.replace(" ", ""), "report", false);
                    //$('#grid' + viewId.replace(" ", "")).data("kendoTreeList").table.focus();
                }
                else {
                    reloadReport(viewId, _selrecid_in_parent_view, "yes");
                    //$('#grid' + viewId.replace(" ", "")).data("kendoTreeList").table.focus();
                }
            }

            break;
        case "CHART":
            ////debugger;
            var desktopid = DesktopId;
            var section = "GLOBAL";
            var masterViewSelRecord = _selrecid_in_parent_view;

            if (desktopid != null) {
                $.ajax({
                    url: '/Desktop/LoadLinkedChart',
                    cache: false,
                    dataType: "json",
                    type: "GET",
                    //async: false,
                    contentType: 'application/json; charset=utf-8',
                    data: { ViewKey: viewId, DesktopId: desktopid, Section: section, MasterViewSelRecord: masterViewSelRecord, IsTabView: true, Key: Key },
                    success: function (data) {
                        ////debugger
                        if (data != 'failure') {

                            $('#viewbody' + viewId.replace(/ /g, '')).html(data);
                            ResizeChartInsplitter(viewId);
                        }
                    },
                    error: function (data) {

                    }
                })
            }
            break;
        case "CALWEEK":
        case "CALDAY":
        case "CALMONTH":

            var viewtype = $("#hidViewType_" + viewId.replace(" ", "")).val();

            if (viewtype == null || viewtype == undefined || viewtype == "undefined") {
                var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();
                var ids = viewIds_and_viewtypes.split("|");

                for (i = 0; i <= ids.length; i++) {
                    var types = ids[i].split(",");
                    if (types[0].replace(" ", "") == viewId.replace(" ", "")) {
                        viewtype = types[1];
                        break;
                    }
                }
            }
            var grid_report_object = null;

            if (viewtype == "calendar") {
                grid_report_object = $("#grid" + viewId.replace(" ", "")).data("kendoTreeList");

                //tckt #2164: calendar is not loading in tabbed views..J
                refreshviewV2(viewId, viewId.replace(" ", ""), "calendar", false);
            }
            break;
        default:
            break;
    }
}

function ShowReport(obj, viewkey, index, section) {
    //debugger
    var isMap = false;
    if ($('#ShowChart' + viewkey.replace(/ /g, '')).length > 0) {
        $('#ShowChart' + viewkey.replace(/ /g, '')).show();
    }
    else if ($('#ShowMap' + viewkey.replace(/ /g, '')).length > 0) {
        isMap = true;
        $('#ShowMap' + viewkey.replace(/ /g, '')).show();
    }
    //$('#ShowChart' + viewkey.replace(/ /g, '')).show();
    $('#ShowReport' + viewkey.replace(/ /g, '')).hide();
    var desktopid = DesktopId;
    if (desktopid != null) {
        $.ajax({
            url: '/Desktop/ShowReport',
            async: false,
            cache: false,
            data: { ViewKey: viewkey, DesktopId: desktopid, Index: index, Section: section, Key: Key },
            success: function (data) {
                if (data != 'failure') {
                    if (isMap) {
                        $('#viewbody' + viewkey.replace(/ /g, '')).css({ overflow: 'auto' });
                        $('#viewbody' + viewkey.replace(/ /g, '')).html(data).css({ height: '100%' });
                    }
                    else {
                        $('#viewbody' + viewkey.replace(/ /g, '')).html(data).css({ height: '90%' });
                    }
                    $('#view' + viewkey.replace(/ /g, '')).css({ 'overflow-y': 'auto' });
                }
            },
            error: function (data) {
                //alert(responseText);
            }
        })
    }

    //viewbodyVIE_BCF8B7A9-0A0C-41CF-5858-9D2C0117891E
}

function ShowChart(obj, viewkey, index, section) {
    //debugger;
    $('#ShowChart' + viewkey.replace(/ /g, '')).hide();
    $('#ShowReport' + viewkey.replace(/ /g, '')).show();
    var attr1 = $(obj).parent().parent().parent().attr('name');
    var vivid = $(obj).parent().parent().parent()[0].id;
    var _viewId = vivid.substring(4);
    var desktopid = DesktopId;
    if (desktopid != null) {
        $.ajax({
            url: '/Desktop/ShowChart',
            async: false,
            cache: false,
            data: { ViewKey: viewkey, DesktopId: desktopid, Index: index, Section: section, Key: Key },
            success: function (data) {
                if (data != 'failure') {

                    $('#viewbody' + viewkey.replace(/ /g, '')).html(data).css({ 'overflow-y': 'hidden' });
                    ResizeChartInsplitter(_viewId);
                }
            },
            error: function (data) {

            }
        })
    }
}

function ShowMap(obj, viewkey, index, section) {
    //debugger;
    $('#ShowMap' + viewkey.replace(/ /g, '')).hide();
    $('#ShowReport' + viewkey.replace(/ /g, '')).show();
    var attr1 = $(obj).parent().parent().parent().attr('name');
    var vivid = $(obj).parent().parent().parent()[0].id;
    var _viewId = vivid.substring(4);
    var desktopid = DesktopId;
    if (desktopid != null) {
        $.ajax({
            url: '/Desktop/ShowMap',
            async: false,
            cache: false,
            data: { ViewKey: viewkey, DesktopId: desktopid, Index: index, Section: section, Key: Key },
            success: function (data) {
                if (data != 'failure') {

                    $('#viewbody' + viewkey.replace(/ /g, '')).html(data).css({ 'overflow-y': 'hidden' });
                }
            },
            error: function (data) {
                //alert(responseText);
            }
        })
    }
}

function RowColorPersistance(viewid) {
    var count = parseInt($("#hidViewCount").val()) + parseInt($("#hidTabViewCount").val());
    if (count > 1) {

        var LastSelectedRow = sessionStorage.getItem(Key + "_" + "LastSelectedRow");
        var LastGridViewId = sessionStorage.getItem(Key + "_LastGridViewId");
        if (LastSelectedRow != null) {
            var LastSelectedGrid = $("#grid" + LastGridViewId).data("kendoGrid");
            if (LastSelectedGrid) {
                if (LastSelectedGrid.tbody.find('tr').hasClass('defaultrowselectionGrey')) {
                    LastSelectedGrid.tbody.find('tr').removeClass('defaultrowselectionGrey');
                }
                var pageData = LastSelectedGrid.dataSource.view();
                var newSelection = [];
                for (var i = 0; i < pageData.length; i++) {
                    if (LastSelectedRow == pageData[i].GID_ID) {
                        //LastSelectedGrid.tbody.find(">tr[data-uid='" + pageData[i].uid + "']").removeClass('k-selected');// RN
                        if (viewid != undefined) {
                            if (viewid != LastGridViewId) {
                                LastSelectedGrid.tbody.find(">tr[data-uid='" + pageData[i].uid + "']").addClass('defaultrowselectionGrey');
                            }
                        }
                        else {
                            LastSelectedGrid.tbody.find(">tr[data-uid='" + pageData[i].uid + "']").addClass('defaultrowselectionGrey');
                        }
                        break;
                    }
                }
            }
            else {
                LastSelectedReport = $("#grid" + LastGridViewId).data("kendoTreeList");
                if (LastSelectedReport) {
                    var pageData = LastSelectedReport.dataSource.view();
                    for (var i = 0; i < pageData.length; i++) {
                        if (LastSelectedRow == pageData[i].GID_ID) {
                            LastSelectedReport.tbody.find(">tr[data-uid='" + pageData[i].uid + "']").removeClass("k-alt");// RN
                            LastSelectedReport.tbody.find(">tr[data-uid='" + pageData[i].uid + "']").removeClass('k-selected');// RN
                            LastSelectedReport.tbody.find(">tr[data-uid='" + pageData[i].uid + "']").addClass('defaultrowselectionGrey');
                            break;
                        }
                    }
                }
            }
        }
    }
}

$(window).unload(function () {
    //localStorage.clear();
});

function RunScript(runscript, event, arg, isfromautomatortoolbar) {
    if (isfromautomatortoolbar == undefined) {
        isfromautomatortoolbar = false;
    }
    showProgress();
    $.ajax({
        url: "/Desktop/DoEvent",
        cache: false,
        data: { sRunscript: runscript, sEvent: event, par_s1: arg, IsFromAutomatorToolbar: isfromautomatortoolbar, HistoryKey: Key },
        type: 'POST',
        success: function (data) {

            var _Key = $("#hidKey").val();
            var _ViewId = sessionStorage.getItem(Key + "_LastGridViewId");

            if (data != null && data != undefined && data != "") {
                hideProgress();
                if (data.ErrorMessage != "") {
                    var errormessage = "ScriptsError";
                    window.location.href = "/ErrorLog/LogError?sErrorLogs=" + errormessage;
                }
                else if (data.IsNavigate == true) {

                    //RN #1873 Multiselect functionality added.
                    if (event.indexOf("_ViewControlOnChange_") > -1) {
                        sessionStorage.removeItem("SelectMultipleRecords_" + _ViewId + _Key);
                    }

                    var _navigatetype = data.NavigateType;
                    if (_navigatetype == "FORM") {
                        window.location.href = "/CreateForm/CreateForm/" + data.FileName + "/" + data.RecordId + "/" + data.Type + "/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + Key;
                    }
                    else if (_navigatetype == "DESKTOP") {
                        var HistoryKey = data.HistoryKey;
                        if (HistoryKey == null || HistoryKey == "") {
                            HistoryKey = "HISTORYKEY";
                        }
                        window.location.href = "/Desktop/LoadDesktop/" + data.RecordId + "/false/FOLDERID/" + HistoryKey;
                    }
                    else if (_navigatetype == "DIALOG") {
                        var previousUrl = data.PreviousUrlForSendOption;
                        window.location.href = "/CreateForm/LoadSendOptions?PreviousUrl=" + previousUrl;
                    }
                    else if (_navigatetype == "OPENURLEXTERNAL") {
                        if (data.ExternalUrlItems != null && data.ExternalUrlItems != undefined) {
                            var sURL = data.ExternalUrlItems.sFilePath;
                            var sTitle = data.ExternalUrlItems.sTitle;
                            var sParams = data.ExternalUrlItems.sParams;

                            if (sURL.indexOf("mailto:") == 0) {
                                window.location = sURL;
                            }
                            else {
                                var myWin = window.open(sURL, sTitle, sParams);
                                if (window.focus) {
                                    //if EML will error on focus
                                    try { myWin.focus() }
                                    catch (Error) { }
                                }
                            }
                        }
                    }
                }
                else {
                    //RN #1873 Multiselect functionality added.
                    if (event.indexOf("_ViewControlOnChange_") > -1) {
                        sessionStorage.removeItem("SelectMultipleRecords_" + _ViewId + _Key);
                    }

                    if (data.MessageBox.MessageBoxDisplay == true) {
                        hideProgress();
                        DisplayMessageBox(data.MessageBox);
                    }
                    else {
                        ////Load current view and it's dependencies if any..J
                        //var LastSelectedViewId = sessionStorage.getItem("lastViewid");
                        //var TableName = sessionStorage.getItem("type");
                        //if ((LastSelectedViewId != null && LastSelectedViewId != "" && LastSelectedViewId != undefined) && (TableName != null && TableName != "" && TableName != undefined)) {

                        //    LastSelectedViewId = LastSelectedViewId.replace(" ", "");
                        //    var dependenceviewids = null;
                        //    dependenceviewids = $("#hidDepViews_" + LastSelectedViewId).val() + "," + $("#hidDepViewsOnFocus_" + LastSelectedViewId).val();

                        //    var dependenceviewids = RecurrenceFunctionForDependencies(dependenceviewids) + "," + LastSelectedViewId;

                        //    var arr = dependenceviewids.split(',');

                        //    for (var i = 0; i < arr.length; i++) {

                        //        if (arr[i] == "") {
                        //            continue;
                        //        }

                        //        if ($("#grid" + arr[i].replace(" ", "")).data("kendoGrid")) {
                        //            if (arr[i].replace(" ", "") == LastSelectedViewId) {
                        //                reLoadViewsV2(arr[i].replace(" ", ""), false);

                        //                $('#' + TableName + LastSelectedViewId).trigger('click');
                        //            }
                        //            else {
                        //                reLoadViewsV2(arr[i].replace(" ", ""), true);
                        //            }
                        //        }
                        //        else if ($("#grid" + arr[i].replace(" ", "")).data("kendoTreeList")) {
                        //            refreshviewV2(arr[i], arr[i].replace(" ", ""), "report", true);
                        //        }
                        //        else if ($("#calender" + arr[i].replace(" ", "")).data("kendoScheduler")) {
                        //            refreshviewV2(arr[i], arr[i].replace(" ", ""), "calendar", true);
                        //        }
                        //        else if ($("#Chart" + arr[i].replace(" ", "")).data("kendoChart")) {

                        //            var desktopid = DesktopId;
                        //            var section = "GLOBAL";
                        //            //var index = i + 1;
                        //            //var masterViewSelRecord = gid_id;
                        //            var viewId = arr[i].replace(" ", "");
                        //            if (desktopid != null) {
                        //                $.ajax({
                        //                    url: '/Desktop/LoadLinkedChart',
                        //                    cache: false,
                        //                    dataType: "json",
                        //                    type: "GET",
                        //                    async: false,
                        //                    contentType: 'application/json; charset=utf-8',
                        //                    data: { ViewKey: arr[i].replace(" ", ""), DesktopId: desktopid, Section: section, MasterViewSelRecord: "", IsTabView: false },
                        //                    success: function (data) {

                        //                        if (data != 'failure') {
                        //                            $('#viewbody' + viewId.replace('/ /g', '')).html(data);

                        //                            ResizeChartInsplitter(viewId.replace(" ", ""));
                        //                        }

                        //                    },
                        //                    error: function (data) {
                        //                    }
                        //                })
                        //            }

                        //        }

                        //    }

                        //}
                        //else {
                        var desktopid = DesktopId;
                        var folderid = FolderId;
                        if (folderid == null || folderid == undefined || folderid == "") {
                            folderid = "FOLDERID";
                        }
                        var HistoryKey = Key;
                        if (desktopid != null && desktopid != undefined && desktopid != "") {
                            window.location.href = "/Desktop/LoadDesktop/" + desktopid + "/true/" + folderid + "/" + HistoryKey;       //once do event has been called the isloadviefwfrom should be true to show the result..j
                        }
                        else {
                            window.location.href = '/Login/LoginSubmit';
                        }
                        //}

                        hideProgress();

                        $("#PNL_MessageBox_Desktop").hide();
                        onCloseMsgBox('PNL_MessageBox_Desktop');
                    }

                }
            }
        },
        error: function (data) {
            //alert(data.responseText);
        }
    })
}

function DisplayMessageBox(data) {
    ////debugger;
    if (data.MessageBoxDisplay == true) {

        //To toggle the Menu drop down..SB
        $('body').click();

        $("#PNL_MessageBox_Desktop").show();
        $("#LBL_MsgBoxTitle_Desktop").text(data.LBL_MsgBoxTitle);
        var _LBL_MsgBoxMessage = data.LBL_MsgBoxMessage.replace("/n/n", "</br></br>").replace("/n", "</br>");
        $("#LBL_MsgBoxMessage_Desktop").html(_LBL_MsgBoxMessage);

        if (data.TXT_MsgBoxInputVisible == true) {
            $("#TXT_MsgBoxInput_Desktop").show();
            $("#TXT_MsgBoxInput_Desktop").val(data.TXT_MsgBoxInputText);
            $("#TXT_MsgBoxInput_Desktop").focus();
        }
        else {
            $("#TXT_MsgBoxInput_Desktop").hide();
            $("#TXT_MsgBoxInput_Desktop").val("");
        }

        if (data.BTN_MsgBox1Visible == true) {
            $("#BTN_MsgBox1_Desktop").show();

            $("#BTN_MsgBox1_Desktop").attr('value', data.BTN_MsgBox1Text); //versions older than 1.6
            $("#BTN_MsgBox1_Desktop").prop('value', data.BTN_MsgBox1Text); //versions newer than 1.6
            $("#BTN_MsgBox1_Desktop").html(data.BTN_MsgBox1Text);
            $("#BTN_MsgBox1_Desktop").attr("title", data.BTN_MsgBox1ToolTip);
            //debugger;
            $("#BTN_MsgBox1_Desktop").attr('onclick', data.BTN_MsgBox1_onClick);

            //BTN_MsgBox1_onClick
        }
        else {
            $("#BTN_MsgBox1_Desktop").hide();
        }

        if (data.BTN_MsgBox2Visible == true) {
            $("#BTN_MsgBox2_Desktop").show();

            $("#BTN_MsgBox2_Desktop").attr('value', data.BTN_MsgBox2Text); //versions older than 1.6
            $("#BTN_MsgBox2_Desktop").prop('value', data.BTN_MsgBox2Text); //versions newer than 1.6
            $("#BTN_MsgBox2_Desktop").html(data.BTN_MsgBox2Text);
            $("#BTN_MsgBox2_Desktop").attr("title", data.BTN_MsgBox2ToolTip);
            $("#BTN_MsgBox2_Desktop").attr('onclick', data.BTN_MsgBox2_onClick);
            //BTN_MsgBox1_onClick
        }
        else {
            $("#BTN_MsgBox2_Desktop").hide();
        }

        if (data.BTN_MsgBox3Visible == true) {
            $("#BTN_MsgBox3_Desktop").show();

            $("#BTN_MsgBox3_Desktop").attr('value', data.BTN_MsgBox3Text); //versions older than 1.6
            $("#BTN_MsgBox3_Desktop").prop('value', data.BTN_MsgBox3Text); //versions newer than 1.6
            $("#BTN_MsgBox3_Desktop").html(data.BTN_MsgBox3Text);
            $("#BTN_MsgBox3_Desktop").attr("title", data.BTN_MsgBox3ToolTip);
            $("#BTN_MsgBox3_Desktop").attr('onclick', data.BTN_MsgBox3_onClick);

            //BTN_MsgBox1_onClick
        }
        else {
            $("#BTN_MsgBox3_Desktop").hide();
        }

        if (data.BTN_MsgBox1_Focus == true)
            $("#BTN_MsgBox1_Desktop").focus();
        else if (data.BTN_MsgBox2_Focus == true)
            $("#BTN_MsgBox2_Desktop").focus();
        else if (data.BTN_MsgBox3_Focus == true)
            $("#BTN_MsgBox3_Desktop").focus();
        else if (data.TXT_MsgBoxInput_Focus == true)
            $("#TXT_MsgBoxInput_Desktop").focus();

        onOpenMsgBox('PNL_MessageBox_Desktop');
    }
    else {
        $("#PNL_MessageBox_Desktop").hide();
        onCloseMsgBox('PNL_MessageBox_Desktop');
    }
}

function MessageBoxClick(sEvent, aArg, sVal) {
    RunScript(sEvent, aArg, sVal);
}

function SetDocumentHeight() {
    var topHeaderHeight = 60;
    var DesktopHeaderHeight = 38;
    var SingleRowQuickFilterHeight = 38;
    var SingleRow_DRSEnabledHeight = 76;
    var DoubleRow_DRSEnabledHeight = 129;
    var DoubleRow_DRSDisabledHeight = 74;

    var totalWindowHeight = $(window).height();
    var QuickCount = $("#hidQuickCount").val();

    //CheckHeight_InCustomToolbarCase();

    var FinalHeight = 0;
    var onePercentValue = Math.round((totalWindowHeight) / 100);
    var totalSubtractValue = (onePercentValue) + 15;

    if ($("#customToolBar").length > 0) {
        totalWindowHeight = totalWindowHeight - $("#customToolBar").height() - 8;
    }

    var QuickSelectNotAvailable = $("#hidQuickSelectNotAvailable").val();

    if ($("#hidDrsEnabled").val() == 'True') {
        if (parseInt(QuickCount) < 3) {
            if (QuickSelectNotAvailable == "True" && parseInt(QuickCount) == 0) {
                FinalHeight = totalWindowHeight - SingleRow_DRSEnabledHeight - onePercentValue - 5 + 30; // 28 - if the desktop does not have quick select div
            }
            else {
                FinalHeight = totalWindowHeight - SingleRow_DRSEnabledHeight - onePercentValue - 15;
            }
        }
        else {
            if (QuickSelectNotAvailable == "True" && parseInt(QuickCount) == 3) {
                FinalHeight = totalWindowHeight - SingleRow_DRSEnabledHeight - onePercentValue - 5;
            }
            else {
                FinalHeight = totalWindowHeight - DoubleRow_DRSEnabledHeight - totalSubtractValue + 15;
            }
        }
    }
    else {
        if (parseInt(QuickCount) < 3) {
            if (QuickSelectNotAvailable == "True" && parseInt(QuickCount) == 0) {
                FinalHeight = totalWindowHeight - SingleRowQuickFilterHeight - onePercentValue - 5 + 43; // 28 - if the desktop does not have quick select div
            }
            else {
                FinalHeight = totalWindowHeight - SingleRowQuickFilterHeight - onePercentValue - 10;
            }
        }
        else {
            if (QuickSelectNotAvailable == "True" && parseInt(QuickCount) == 3) {
                FinalHeight = totalWindowHeight - SingleRowQuickFilterHeight - onePercentValue - 5;
            }
            else {
                FinalHeight = totalWindowHeight - DoubleRow_DRSDisabledHeight - onePercentValue - 11;
            }
        }
    }

    //$(".heightchanged").css('height', (FinalHeight - topHeaderHeight - DesktopHeaderHeight));
    //$(".heightchanged").css('max-height', (FinalHeight - topHeaderHeight - DesktopHeaderHeight));
    //$(".heightchanged").css('min-height', (FinalHeight - topHeaderHeight - DesktopHeaderHeight));

}

function CheckHeight_InCustomToolbarCase() {

    var val = sessionStorage.getItem("IsCustomToolbarAvailable_InFirstLoad");
    if (val != undefined && val != null && val != false) {
        //custom toolbar appears in firstload
        if ($("#customToolBar").length > 0) {

            if (tabViewCount > 0) {
                if (sessionStorage.getItem("CustomToolbarAvailable") != undefined && sessionStorage.getItem("CustomToolbarAvailable") != null && sessionStorage.getItem("CustomToolbarAvailable") == "false") {
                    $("#tabheight").height($("#tabheight").height() - 30);
                }
            }
            else {
                if (sessionStorage.getItem("CustomToolbarAvailable") != undefined && sessionStorage.getItem("CustomToolbarAvailable") != null && sessionStorage.getItem("CustomToolbarAvailable") == "false") {
                    if ($("#splittercol1").length > 0) {
                        $("#splittercol1").height($("#splittercol1").height() - 30);
                    }
                    if ($("#splittercol2").length > 0) {
                        $("#splittercol2").height($("#splittercol2").height() - 30);
                    }
                }
            }
            sessionStorage.setItem("CustomToolbarAvailable", true);
        }
        else {
            if (tabViewCount > 0) {
                if (sessionStorage.getItem("CustomToolbarAvailable") != undefined && sessionStorage.getItem("CustomToolbarAvailable") != null && sessionStorage.getItem("CustomToolbarAvailable") == "true") {
                    $("#tabheight").height($("#tabheight").height() + 30);
                }
            }
            else {
                if (sessionStorage.getItem("CustomToolbarAvailable") != undefined && sessionStorage.getItem("CustomToolbarAvailable") != null && sessionStorage.getItem("CustomToolbarAvailable") == "true") {
                    if ($("#splittercol1").length > 0) {
                        $("#splittercol1").height($("#splittercol1").height() + 30);
                    }
                    if ($("#splittercol2").length > 0) {
                        $("#splittercol2").height($("#splittercol2").height() + 30);
                    }
                }
            }
            sessionStorage.setItem("CustomToolbarAvailable", false);
        }
    }
    else {
        //custom toolbar does not appears in firstload
        if ($("#customToolBar").length > 0) {
            if (tabViewCount > 0) {
                if (sessionStorage.getItem("CustomToolbarAvailable") != undefined && sessionStorage.getItem("CustomToolbarAvailable") != null && sessionStorage.getItem("CustomToolbarAvailable") == "false") {
                    $("#tabheight").height($("#tabheight").height() - 30);
                }
            }
            else {
                if (sessionStorage.getItem("CustomToolbarAvailable") != undefined && sessionStorage.getItem("CustomToolbarAvailable") != null && sessionStorage.getItem("CustomToolbarAvailable") == "false") {
                    if ($("#splittercol1").length > 0) {
                        $("#splittercol1").height($("#splittercol1").height() - 25);
                    }
                    if ($("#splittercol2").length > 0) {
                        $("#splittercol2").height($("#splittercol2").height() - 25);
                    }
                }
            }
            sessionStorage.setItem("CustomToolbarAvailable", true);
        }
        else {
            if (tabViewCount > 0) {
                if (sessionStorage.getItem("CustomToolbarAvailable") != undefined && sessionStorage.getItem("CustomToolbarAvailable") != null && sessionStorage.getItem("CustomToolbarAvailable") == "true") {
                    $("#tabheight").height($("#tabheight").height() + 30);
                }
            }
            else {
                if (sessionStorage.getItem("CustomToolbarAvailable") != undefined && sessionStorage.getItem("CustomToolbarAvailable") != null && sessionStorage.getItem("CustomToolbarAvailable") == "true") {
                    if ($("#splittercol1").length > 0) {
                        $("#splittercol1").height($("#splittercol1").height() + 25);
                    }
                    if ($("#splittercol2").length > 0) {
                        $("#splittercol2").height($("#splittercol2").height() + 25);
                    }
                }
            }
            sessionStorage.setItem("CustomToolbarAvailable", false);
        }
    }
}

function LoadDependencyViewsV2(gid_id, senderViewId, _rowclick, RunAllDependencies, RunOnlyDependenciesOnFocus) {
    
    TabViewIds = $("#hidTabViewIds").val();

    var lsViewIds = gid_id.split(",");

    for (var i = 0; i < lsViewIds.length; i++) {

        if (lsViewIds[i] == null || lsViewIds[i] == "" || lsViewIds[i] == senderViewId) {
            continue;
        }

        var isValidView = true;

        if (TabViewIds.indexOf(lsViewIds[i]) >= 0) {
            var arr = TabViewIds.split(',');
            for (var k = 0; k < arr.length; k++) {
                if (arr[k] == lsViewIds[i]) {
                    var activeTabId = $('#hdnActive').val();
                    if (lsViewIds[i] != activeTabId) {
                        isValidView = false;
                        break;
                    }
                }
            }
        }

        var viewId = lsViewIds[i].replace(" ", "");
        if (isValidView) {

            var viewtype = $("#hidViewType_" + viewId.replace(" ", "")).val();

            if (viewtype == null || viewtype == undefined || viewtype == "undefined") {
                var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();
                var ids = viewIds_and_viewtypes.split("|");

                for (var j = 0; j < ids.length; j++) {
                    var types = ids[j].split(",");
                    if (types[0].replace(" ", "") == viewId.replace(" ", "")) {
                        viewtype = types[1];
                        break;
                    }
                }

            }

            var grid_report_object = null;

            if (viewtype == "list") {
                grid_report_object = $("#grid" + viewId.replace(" ", "")).data("kendoGrid");
            }
            else if (viewtype == "report") {
                grid_report_object = $("#grid" + viewId.replace(" ", "")).data("kendoTreeList");
            }
            else if (viewtype == "calendar") {
                grid_report_object = $("#calender" + viewId.replace(" ", "")).data("kendoScheduler");
            }

            if (grid_report_object) {
                //view already rendered so call the read action
                if (viewtype == "list") {
                    //reLoadViewsV2(viewId, true, 1);
                    refreshviewV2(viewId, viewId.replace(" ", ""), "list", true);
                    //Disable first and previous navigation buttons in the current grid after sort is performed..J
                    $('#first' + viewId).prop("disabled", true);
                    $('#previous' + viewId).prop("disabled", true);
                    continue;
                }
                else if (viewtype == "report") {
                    //reloadReport(viewId, _selrecid, _rowclick);
                    refreshviewV2(viewId, viewId.replace(" ", ""), "report", true);
                    continue;
                }
                else if (viewtype == "calendar") {
                    refreshviewV2(viewId, viewId.replace(" ", ""), "calendar", true);
                    continue;
                }
            }
            else {
                //view is not loaded yet so call the 'reloadview'
                if (viewtype == "list") {
                    //reLoadViewsV2(viewId, true, 1);
                    refreshviewV2(viewId, viewId.replace(" ", ""), "list", true);
                    //Disable first and previous navigation buttons in the current grid after sort is performed..J
                    $('#first' + viewId).prop("disabled", true);
                    $('#previous' + viewId).prop("disabled", true);
                    continue;
                }
                else if (viewtype == "report") {
                    refreshviewV2(viewId, viewId.replace(" ", ""), "report", true);
                    continue;
                }
                else if (viewtype == "calendar") {
                    refreshviewV2(viewId, viewId.replace(" ", ""), "calendar", true);
                    continue;
                }
                else if (viewtype == "map") {
                    refreshviewV2(viewId, viewId.replace(" ", ""), "map", true);
                    continue;
                }
            }

            var chart = $("#Chart" + viewId.replace(" ", "")).data("kendoChart");

            if (viewtype == "chart" || viewtype == "CHART") {
                var desktopid = DesktopId;
                var section = "GLOBAL";
                //var index = i + 1;
                //var masterViewSelRecord = gid_id;

                if (desktopid != null) {
                    $.ajax({
                        url: '/Desktop/LoadLinkedChart',
                        cache: false,
                        dataType: "json",
                        type: "GET",
                        async: false,
                        contentType: 'application/json; charset=utf-8',
                        data: { ViewKey: viewId, DesktopId: desktopid, Section: section, MasterViewSelRecord: "", IsTabView: false, Key: Key },
                        success: function (data) {
                            ////debugger
                            if (data != 'failure') {
                                $('#viewbody' + viewId.replace('/ /g', '')).html(data);

                                ResizeChartInsplitter(viewId.replace(" ", ""));
                            }
                            hideProgress();
                        },
                        error: function (data) {
                        }
                    })
                }
            }
        }
    }

}

function RefreshAllChildViews(_ViewID, gid_id, TableName) {
    OnLoadDependecies(_ViewID, gid_id, TableName, true);
}

function EmptyDependencyViewsOnFocus(viewid) {
    var dependenceviewids = $("#hidDepViewsOnFocus_" + viewid).val();

    if (dependenceviewids != "" && dependenceviewids != null && dependenceviewids != undefined) {
        var lsViewIds = dependenceviewids.split(",");

        for (var i = 0; i < lsViewIds.length; i++) {

            var viewId = lsViewIds[i].replace(" ", "");

            if (viewId != "") {

                var viewtype = $("#hidViewType_" + viewId.replace(" ", "")).val();

                if (viewtype == null || viewtype == undefined || viewtype == "undefined") {
                    var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();
                    var ids = viewIds_and_viewtypes.split("|");

                    for (var j = 0; j < ids.length; j++) {
                        var types = ids[j].split(",");
                        if (types[0].replace(" ", "") == viewId.replace(" ", "")) {
                            viewtype = types[1];
                            break;
                        }
                    }
                }

                var grid_report_object = null;

                if (viewtype == "list") {
                    grid_report_object = $("#grid" + viewId.replace(" ", "")).data("kendoGrid");
                }
                else if (viewtype == "report") {
                    grid_report_object = $("#grid" + viewId.replace(" ", "")).data("kendoTreeList");
                }
                else if (viewtype == "calendar") {
                    grid_report_object = $("#calender" + viewId.replace(" ", "")).data("kendoScheduler");
                }

                if (grid_report_object != null) {
                    grid_report_object.dataSource.data([]);
                }
            }
        }

    }


}

function OnLoadDependecies(_ViewID, gid_id, TableName, rowclick, runDependenciesOnFocus) {
    if (runDependenciesOnFocus == undefined) {
        runDependenciesOnFocus = false;
    }

    showProgress();
    $.ajax({
        url: '/Desktop/SetSelectedRecordIdAndLoadDataSets',
        cache: false,
        type: "GET",
        data: { ViewKey: _ViewID, SelectedRecordId: gid_id, FileName: TableName, Key: Key },
        success: function (data) {

            $("#hidSelRecId_" + _ViewID).val(gid_id);

            if (data != null && data != "" && data != '') {

                LoadDependencyViewsV2(data, _ViewID, "", rowclick, runDependenciesOnFocus);
                MakeChildDependencyViewsOnFocus_AsEmpty(_ViewID);

                hideProgress();
            }
            hideProgress();
        },
        error: function (data) {
            hideProgress();
        }
    })
}

function MakeChildDependencyViewsOnFocus_AsEmpty(ViewID) {

    //debugger
    var allClientViewIds = $("#hidClientViewIds").val();

    var aChildIds = allClientViewIds.split(',');

    for (var i = 0; i < aChildIds.length; i++) {
        if (aChildIds[i] != ViewID) {
            EmptyDependencyViewsOnFocus(aChildIds[i]);
        }
    }

    var allIndependentViewIds = $("#hidIndependentViewIds").val();

    var aIndependentIds = allIndependentViewIds.split(',');

    for (var i = 0; i < aIndependentIds.length; i++) {
        if (aIndependentIds[i] != ViewID) {
            EmptyDependencyViewsOnFocus(aIndependentIds[i]);
        }
    }
}

function DependencyViewsLoad(gid_id, ViewId, TableName) {
    showProgress();
    $.ajax({
        url: '/Desktop/SetSelectedRecordIdAndLoadDataSets',
        cache: false,
        type: "GET",
        data: { ViewKey: ViewId, SelectedRecordId: gid_id, FileName: TableName, Key: Key },
        success: function (data) {
           
        },
        error: function (data) {

        }
    })
}

function RecurrenceFunctionForDependencies(dependenceviewids) {

    var sViewIds = dependenceviewids.split(',');

    for (var i = 0; i < sViewIds.length; i++) {

        var _viewids = $("#hidDepViews_" + sViewIds[i]).val();

        if (_viewids != null && _viewids != "") {
            dependenceviewids = dependenceviewids + "," + RecurrenceFunctionForDependencies(_viewids);
        }
    }

    return dependenceviewids;
}

function onGridColumnSortClick(FieldName, ViewKey, TableName) {

    showProgress();

    //Set view header styles if it is not selected..J
    if (!$("#" + TableName + ViewKey).hasClass('ViewHeaderOn')) {
        $('#' + TableName + ViewKey).trigger('click');
    }

    //Hide default sort icon..J
    $("#defualtSortFieldIcon").hide();

    //Get current sortfield..J
    var datafield = FieldName;

    $.ajax({
        url: '/Desktop/GetSortedFieldControl',
        type: "GET",
        cache: false,
        data: { datafield: datafield, ViewKey: ViewKey, Key: Key, ViewMultiSelect: 'NOTFOUND' },
        success: function (data) {

            if (data.FilterText != null) {

                $("#QuickFilterBar").html("");
                $("#QuickFilterBar").html(data.FilterText);

                SetDocumentHeight();

                if ($("#QuickSelectCONDITION").length >= 1) {
                    document.getElementById("QuickSelectCONDITION").focus();
                }
                var myval = ViewKey;
                var _tooltip = data.ToolTip;
                $("#" + data.TableName + myval).attr("title", _tooltip);

                //Refresh all views..J
                if (data.DepencyViewIds != null && data.DepencyViewIds != "") {

                    var dependenceviewids = data.DepencyViewIds + "," + ViewKey;
                    var arr = dependenceviewids.split(',');

                    for (var i = 0; i < arr.length; i++) {

                        if (arr[i] == "") {
                            continue;
                        }

                        if ($("#grid" + arr[i].replace(" ", "")).data("kendoGrid")) {
                            reLoadViewsV2(arr[i].replace(" ", ""), true);
                        }
                        else if ($("#grid" + arr[i].replace(" ", "")).data("kendoTreeList")) {
                            refreshviewV2(arr[i], arr[i].replace(" ", ""), "report", true);
                        }
                        else if ($("#calender" + arr[i].replace(" ", "")).data("kendoScheduler")) {
                            refreshviewV2(arr[i], arr[i].replace(" ", ""), "calendar", true);
                        }
                        else if ($("#map_" + arr[i].replace(" ", "")).length > 0) {
                            refreshviewV2(arr[i], arr[i].replace(" ", ""), "map", true);
                        }
                        else if ($("#Chart" + arr[i].replace(" ", "")).data("kendoChart")) {

                            var desktopid = DesktopId;
                            var section = "GLOBAL";
                            var viewId = arr[i].replace(" ", "");

                            if (desktopid != null) {
                                $.ajax({
                                    url: '/Desktop/LoadLinkedChart',
                                    cache: false,
                                    dataType: "json",
                                    type: "GET",
                                    async: false,
                                    contentType: 'application/json; charset=utf-8',
                                    data: { ViewKey: arr[i].replace(" ", ""), DesktopId: desktopid, Section: section, MasterViewSelRecord: "", IsTabView: false, Key: Key },
                                    success: function (data) {

                                        if (data != 'failure') {
                                            $('#viewbody' + viewId.replace('/ /g', '')).html(data);

                                            ResizeChartInsplitter(viewId.replace(" ", ""));
                                        }
                                        hideProgress();
                                    },
                                    error: function (data) {
                                    }
                                })
                            }
                        }
                    }
                }
                else {
                    if ($("#grid" + ViewKey.replace(" ", "")).data("kendoGrid")) {
                        reLoadViewsV2(ViewKey.replace(" ", ""), true);
                    }
                    else if ($("#grid" + ViewKey.replace(" ", "")).data("kendoTreeList")) {
                        refreshviewV2(ViewKey, ViewKey.replace(" ", ""), "report", true);
                    }
                }

                //Get previous Sorted column to remove its icon..J
                var previousSortedColumn = sessionStorage.getItem(Key + "_" + "PreviousSortedColumn" + ViewKey)
                if (previousSortedColumn != null) {
                    //Remove if icon is already there in previous sorted column..J
                    if ($("#" + Key + "_" + ViewKey + "_" + previousSortedColumn + "_SortableFieldIcon").hasClass('fa fa-long-arrow-down')) {
                        $("#" + Key + "_" + ViewKey + "_" + previousSortedColumn + "_SortableFieldIcon").removeClass('fa fa-long-arrow-down');
                    }
                    else if ($("#" + Key + "_" + ViewKey + "_" + previousSortedColumn + "_SortableFieldIcon").hasClass('fa fa-long-arrow-up')) {
                        $("#" + Key + "_" + ViewKey + "_" + previousSortedColumn + "_SortableFieldIcon").removeClass('fa fa-long-arrow-up');
                    }
                }

                //Set icon to sroted column..J
                //Remove if icon is already there..J
                if ($("#" + Key + "_" + ViewKey + "_" + datafield + "_SortableFieldIcon").hasClass('fa fa-long-arrow-down')) {
                    $("#" + Key + "_" + ViewKey + "_" + datafield + "_SortableFieldIcon").removeClass('fa fa-long-arrow-down');
                }
                else if ($("#" + Key + "_" + ViewKey + "_" + datafield + "_SortableFieldIcon").hasClass('fa fa-long-arrow-up')) {
                    $("#" + Key + "_" + ViewKey + "_" + datafield + "_SortableFieldIcon").removeClass('fa fa-long-arrow-up');
                }

                //Set icon as per current sortorder..J
                if (_tooltip.indexOf('asc') > -1) {
                    $("#" + Key + "_" + ViewKey + "_" + datafield + "_SortableFieldIcon").addClass('fa fa-long-arrow-up');
                }
                else if (_tooltip.indexOf('desc') > -1) {
                    $("#" + Key + "_" + ViewKey + "_" + datafield + "_SortableFieldIcon").addClass('fa fa-long-arrow-down');
                }

                //Make it visible..J
                $("#" + Key + "_" + ViewKey + "_" + datafield + "_SortableFieldIcon").show();

                sessionStorage.setItem(Key + "_" + "PreviousSortedColumn" + ViewKey, datafield);

                //Disable first and previous navigation buttons in the current grid after sort is performed..J
                $('#first' + ViewKey).prop("disabled", true);
                $('#previous' + ViewKey).prop("disabled", true);
            }

            hideProgress();
        },
        error: function (result) {
            //debugger
        }
    })
}

function lnkLoadView(e, viewId, viewtype, tableName) {

    var ViewKey = viewId;
    var FileName = tableName;
    
    var id = FileName + viewId.replace(" ", "");
    if (!$("#" + id).hasClass('ViewHeaderOn')) {

        $(".panel-heading.active").removeClass('ViewHeaderOn');
        $(".panel-heading.active").addClass('ViewHeaderOff');
        $('.panel-heading.active').parent().removeClass('z-depthstyle');

        $("#" + id).removeClass('ViewHeaderOff');
        $("#" + id).addClass('ViewHeaderOn');
        sessionStorage.setItem(Key + "_" + "lastViewid", viewId.replace(" ", ""));
        sessionStorage.setItem(Key + "_" + "CurrentViewId", viewId.replace(" ", ""));
        $("#" + id).parent().addClass('z-depthstyle');
        RowColorPersistance(viewId);
    }

    showProgress();

    $.ajax({
        cache: false,
        //async: false,
        url: '/Desktop/ChangeAutoLoadView',
        type: "GET",
        data: { ViewId: viewId, AutoLoad: "1", Key: Key },
        success: function (result) {
            //debugger
            $("#divAutoLoadView" + ViewKey).html("<span id=\"" + ViewKey + "_spnLoadView\" style=\"font-size:12px; cursor:pointer;color:#428BCA;\" onclick=\"lnkNotLoadView('" + e + "','" + ViewKey + "','" + viewtype + "','" + FileName + "');\"  ><u>Click here to not load view.</u>");
            
            if ($("#grid" + ViewKey.replace(" ", "")).data("kendoGrid")) {
                refreshviewV2(ViewKey, ViewKey.replace(" ", ""), "list", true);
            }
            else if ($("#grid" + ViewKey.replace(" ", "")).data("kendoTreeList")) {
                refreshviewV2(ViewKey, ViewKey.replace(" ", ""), "report", true);
            }
            else if ($("#calender" + ViewKey.replace(" ", "")).data("kendoScheduler")) {
                refreshviewV2(ViewKey, ViewKey.replace(" ", ""), "calendar", true);
            }
            else if ($("#map_" + ViewKey.replace(" ", "")).length > 0) {
                refreshviewV2(ViewKey, ViewKey.replace(" ", ""), "map", true);
            }
            else if ($("#Chart" + ViewKey.replace(" ", "")).length > 0) {   ////tckt #2136 Set view properties list buttons display hide/show when load view data enabled or not..J
                $("#viewbody" + ViewKey).show();
                $("#btnCreateLinkedList").prop("disabled", false);
                $("#liVwPrPrint").removeClass('disabledLiElements');
                $("#liVwPrSendByEmail").removeClass('disabledLiElements');
                $("#liVwPrSendToExcel").removeClass('disabledLiElements');
                $("#liVwPrSendToPdf").removeClass('disabledLiElements');
                $("#liVwPrDeleteRecord").addClass('disabledLiElements');
                $("#liVwPrDeleteAllRecords").addClass('disabledLiElements');
            }

            $("#divAutoLoadView" + ViewKey).show();
            $("#btnCreateLinkedList").prop("disabled", false);
            $("#divAutoLoadView" + ViewKey).css("border", "");
            $('#divPagination' + ViewKey).show();
            $("#grid" + ViewKey).css("border-top", "1px solid #ccc");

            //To load child views..J            
            LoadDependencyViewsV2(result, ViewKey, true);
            MakeChildDependencyViewsOnFocus_AsEmpty(ViewKey);

            hideProgress();
        },
        error: function (data) {

        }
    });
}

function lnkNotLoadView(e, viewId, viewtype, tableName) {
    //debugger
    var ViewKey = viewId;
    var FileName = tableName;

    var id = FileName + viewId.replace(" ", "");
    if (!$("#" + id).hasClass('ViewHeaderOn')) {

        $(".panel-heading.active").removeClass('ViewHeaderOn');
        $(".panel-heading.active").addClass('ViewHeaderOff');
        $('.panel-heading.active').parent().removeClass('z-depthstyle');


        $("#" + id).removeClass('ViewHeaderOff');
        $("#" + id).addClass('ViewHeaderOn');
        sessionStorage.setItem(Key + "_" + "lastViewid", viewId.replace(" ", ""));
        sessionStorage.setItem(Key + "_" + "CurrentViewId", viewId.replace(" ", ""));
        $("#" + id).parent().addClass('z-depthstyle');
        RowColorPersistance(viewId);
    }

    showProgress();
    $.ajax({
        cache: false,
        //async: false,
        url: '/Desktop/ChangeAutoLoadView',
        type: "GET",
        data: { ViewId: viewId, AutoLoad: "0", Key: Key },
        success: function (result) {

            if ($("#grid" + ViewKey.replace(" ", "")).data("kendoGrid")) {
                refreshviewV2(ViewKey, ViewKey.replace(" ", ""), "list", true);
            }
            else if ($("#grid" + ViewKey.replace(" ", "")).data("kendoTreeList")) {
                refreshviewV2(ViewKey, ViewKey.replace(" ", ""), "report", true);
            }
            else if ($("#calender" + ViewKey.replace(" ", "")).data("kendoScheduler")) {
                refreshviewV2(ViewKey, ViewKey.replace(" ", ""), "calendar", true);
            }
            else if ($("#map_" + ViewKey.replace(" ", "")).length > 0) {
                refreshviewV2(ViewKey, ViewKey.replace(" ", ""), "map", true);
            }
            else if ($("#Chart" + ViewKey.replace(" ", "")).length > 0) { ////tckt #2136 Set view properties list buttons display hide/show when load view data enabled or not..J
                $("#viewbody" + ViewKey).hide();
                $("#btnCreateLinkedList").prop("disabled", true);
                $("#liVwPrPrint").addClass('disabledLiElements');
                $("#liVwPrSendByEmail").addClass('disabledLiElements');
                $("#liVwPrSendToExcel").addClass('disabledLiElements');
                $("#liVwPrSendToPdf").addClass('disabledLiElements');
                $("#liVwPrDeleteRecord").addClass('disabledLiElements');
                $("#liVwPrDeleteAllRecords").addClass('disabledLiElements');
            }
            
            $("#divAutoLoadView" + ViewKey).show();
            $("#btnCreateLinkedList").prop("disabled", true);
            $("#divAutoLoadView" + ViewKey).html("<span id=\"" + ViewKey + "_spnLoadView\"  style=\"font-size:12px; cursor:pointer;color:#428BCA\" onclick=\"lnkLoadView('" + e + "','" + ViewKey + "','" + viewtype + "','" + FileName + "');\"  ><u>Click here to load view.</u>");
            $('#divPagination' + ViewKey).hide();
           
            LoadDependencyViewsV2(result, ViewKey, true);
            MakeChildDependencyViewsOnFocus_AsEmpty(ViewKey);

            $('#last' + viewId.replace(" ", "")).prop("disabled", true);
            $('#next' + viewId.replace(" ", "")).prop("disabled", true);
            $('#previous' + viewId.replace(" ", "")).prop("disabled", true);
            $('#first' + viewId.replace(" ", "")).prop("disabled", true);

            hideProgress();
        },
        error: function (data) {

        }
    });
}

$(document).keydown(function (e) {
    if ($(this)[0].activeElement.nodeName != 'TEXTAREA' && $(this)[0].activeElement.nodeName != 'INPUT') {
        if ($('#QuickSelectCONDITION').length > 0) {
            document.getElementById("QuickSelectCONDITION").focus();
        }
    }
});

//SB 12-01-2017 Tckt#1988 Message pop up behind the view
function onCloseMsgBox(msgBoxId) {
    //debugger;
    var ViewId = sessionStorage.getItem(Key + "_ViewId");
    ViewId = sessionStorage.getItem(Key + "_" + "CurrentViewId");
    var isInView = localStorage.getItem(Key + 'isInview' + ViewId);
    if (isInView == 'outview') {
        var ViewHeight = $('#view' + ViewId).height();
        var QuickToolbarDivHeight = 0;
        if ($('#QuickToolbarDiv').length > 0) {
            QuickToolbarDivHeight = $('#QuickToolbarDiv').height();
        }
        var topbarleftHeight = $('.topbar-left').height();
        ViewHeight = $(window).height();

        $('#view' + ViewId.replace(" ", "")).css({ 'height': ViewHeight - 60 + QuickToolbarDivHeight + topbarleftHeight, 'top': 60 + QuickToolbarDivHeight + topbarleftHeight });
    }
}

//SB 12-01-2017 Tckt#1988 Message pop up behind the view
function onOpenMsgBox(msgBoxId) {
    //debugger;
    var ViewId = sessionStorage.getItem(Key + "_ViewId");
    ViewId = sessionStorage.getItem(Key + "_" + "CurrentViewId");
    var isInView = localStorage.getItem(Key + 'isInview' + ViewId);
    if (isInView == 'outview') {
        var ViewHeight = $('#view' + ViewId).height();
        var QuickToolbarDivHeight = 0;
        if ($('#QuickToolbarDiv').length > 0) {
            QuickToolbarDivHeight = $('#QuickToolbarDiv').height();
        }
        var topbarleftHeight = $('.topbar-left').height();
        var MsgBoxHeight = $("#" + msgBoxId).height();

        $('#view' + ViewId.replace(" ", "")).css({ 'height': ViewHeight - MsgBoxHeight, 'top': 60 + QuickToolbarDivHeight + topbarleftHeight + MsgBoxHeight });

    }
}

function SetGridNavigationButtonCSS(viewId) {
    $.ajax({
        url: '/Desktop/SetGridNavigationButtonCSS',
        type: "GET",
        cache: false,
        data: { ViewId: viewId, Key: Key },
        success: function (result) {
            $('#last' + viewId.replace(" ", "")).prop("disabled", !result.HasLast);
            $('#next' + viewId.replace(" ", "")).prop("disabled", !result.HasNext);
            $('#first' + viewId.replace(" ", "")).prop("disabled", !result.HasFirst);
            $('#previous' + viewId.replace(" ", "")).prop("disabled", !result.HasPrevious);
        },
        error: function (result) {

        }
    });
}

function LogScriptError(ErrorStack,FunctionName, HistoryKey, ViewId, RecordId) {
         $.ajax({                    
                    url: '/Desktop/LogScriptError',
                    cache: false,
                    data: { ErrorDescription: ErrorStack, CurrentFunction: FunctionName, Key: HistoryKey, ViewKey: ViewId, RecordId: RecordId },
                    success: function (result) {
                    },
                    error: function (result) { }
                });
    }

function WriteLog(LastSelectedRowIndex, pageDatalength, _gridid) {
         $.ajax({                    
                    url: '/Desktop/WriteLogToText',
                    cache: false,
                    data: { LastSelectedRowIndex: LastSelectedRowIndex, pageDatalength: pageDatalength, _gridid: _gridid },
                    success: function (result) {
                    },
                    error: function (result) { }
                });
}

function WriteLogToGoClick(StatementLevel, ViewId, DependentViewIds) {
    $.ajax({
        url: '/Desktop/WriteLogToGoClick',
        cache: false,
        data: { StatementLevel: StatementLevel, ViewId: ViewId, DependentIDs: DependentViewIds },
        success: function (result) {
        },
        error: function (result) { }
    });
}

function OnMergeClick() {

    var myval = sessionStorage.getItem(Key + "_" + "CurrentViewId");
    var model = $("#myModalMergeRecords");
    showProgress();
    $.ajax({
        url: '/Common/MergeRecords',
        cache: false,
        data: { ViewKey: myval.replace(" ", ""), Key: Key },
        success: function (response) {
            if (response == "Please select only two records to merge.") {
                alert("Please select only two records to merge.");
                hideProgress();
            }
            else if (response == "Please select atleast two records to merge."){
                alert("Please select atleast two records to merge.");
                hideProgress();
            }
            else{
                model.find('.modal-body').html("");
                model.find('.modal-body').append(response);
                model.modal('show');
                hideProgress();
            }
        },
        error: function (data) {
             hideProgress();
        }
    })
}

function RecordMergeClick()
{
    var selectedvalues="";
        //debugger
        //$('table [type="radio"]').each(function (i, chk) {
        //    //s
        //    if (chk.checked) {
        //        //debugger
        //        var fieldName = chk.attributes[2].value;
        //        var radioValue = $("input[name='" + fieldName + "']:checked").val();
        //        selectedvalues = selectedvalues + "$" + fieldName + "-" + radioValue;
        //    }
        //});
    
        $('.MergeRecords').each(function (i, chk) {
            if (chk.checked) {
                //debugger
                var fieldName = chk.attributes[2].value;
                var radioValue = $("input[name='" + fieldName + "']:checked").val();
                selectedvalues = selectedvalues + "|" + fieldName + "<=>" + radioValue;
            }
        });
        //alert(selectedvalues);
        $.ajax({
            type: 'POST',
            data: { selectedvalues: selectedvalues, Key: Key },
            url: '/Common/Submit_MergeRecord',
            cache: false,
            success: function (data) {
                RecordMergeCANCELClick();
                alert(data);
            },
            error: function (xhr) {
            }
        });

}


function MasterRecordChanged(opt) {
    $('.MergeOption' + opt).click();
}

function RecordMergeCANCELClick() {
    $('.close').click();
}



function Check_View_IsDirty_Report(viewId) {
    if (localStorage.getItem('IsDirty_' + viewId) == true || localStorage.getItem('IsDirty_' + viewId) == 'true' || localStorage.getItem('IsDirty_' + viewId) == "true") {
        if (confirm('You have unsaved changes that will be lost if you leave this page.\nAre you sure you want to leave this page?')) {


            $.ajax({
                type: 'POST',
                url: '/Desktop/ClearChangedFieldValues_Session',
                data: { ViewId: viewId },
                datatype: 'json',
                success: function (result) {
                    localStorage.setItem('IsDirty_' + viewId.replace(" ", ""), false);
                    localStorage.setItem('IsDirtyReportEdit', false);
                    return false;
                },
                error: function (result) {
                    localStorage.setItem('IsDirty_' + viewId.replace(" ", ""), false);
                    localStorage.setItem('IsDirtyReportEdit', false);
                    return false;
                }
            });
            localStorage.setItem('IsDirty_' + viewId.replace(" ", ""), false);
            localStorage.setItem('IsDirtyReportEdit', false);
            return false;

        } else {
            return true;
        }
    }
    else {
        return false;
    }
}

function Check_View_IsDirty(viewId)
{
    if (localStorage.getItem('IsDirty_' + viewId) == true || localStorage.getItem('IsDirty_' + viewId) == 'true' || localStorage.getItem('IsDirty_' + viewId) == "true")
    {
        if (confirm('You have unsaved changes that will be lost if you leave this page.\nAre you sure you want to leave this page?')) {
            

            $.ajax({
                type: 'POST',
                url: '/Desktop/ClearChangedFieldValues_Session',
                data: { ViewId: viewId },
                datatype: 'json',
                success: function (result) {
                    localStorage.setItem('IsDirty_' + viewId.replace(" ", ""), false);
                    localStorage.setItem('IsDirtyGridEdit', false);
                    return false;
                },
                error: function (result) {
                    localStorage.setItem('IsDirty_' + viewId.replace(" ", ""), false);
                    localStorage.setItem('IsDirtyGridEdit', false);
                    return false;
                }
            });
            localStorage.setItem('IsDirty_' + viewId.replace(" ", ""), false);
            localStorage.setItem('IsDirtyGridEdit', false);
            return false;

        } else {
            return true;
        }
    }
    else {
        return false;
    }
}



function GetMLS_Control_As_String(_tablename, _fieldname, _defselval, _width, _ViewId, _key, _title, cookieName) {
    $.ajax({
        url: '/Common/GetMLS_Control_As_String',
        type: "GET",
        async: false,
        data: { TableName: _tablename, FieldName: _fieldname, selectedValue: _defselval, width: _width, ViewId: _ViewId, Key: _key, Title: _title },
        success: function (data) {
            _data = data;
            localStorage.setItem(cookieName, _data);
        },
        error: function (request, status, error) {
            return "";
        }
    });

    return _data;
}

function GetSelectedVal(a, viewId, tablename, fieldname, enablebulksave, title, viewType) {
    //debugger;
    var x = (a.value || a.options[a.selectedIndex].value);

    if (fieldname.startsWith('CHK_')) {
        x = a.checked;       
    }
   
    //crossbrowser solution =)
    var id = $(a).parent().parent().find('td:last').html();
    var rowIndex = $(a).parent().parent().index();
    $(a).parent().parent().parent().parent().find("tbody tr.k-selected").removeClass("k-selected")
    $(a).parent().parent().addClass('k-selected');
    $(a).parent().prepend("<span id=" + fieldname + " class='k-dirty'></span>");

    var ModelEnableBulkRecordsSaveOnView = $("#hidEnableBulkRecordsSaveOnView_" + viewId.replace(" ", "")).val();
    if (ModelEnableBulkRecordsSaveOnView == "1") {
        enablebulksave = 'true';
    }
    else {
        enablebulksave = 'false';
    }

    if (enablebulksave == 'true' || enablebulksave == "true") {
        localStorage.setItem('IsDirty_' + viewId.replace(" ", ""), true);

        var _grid = $("#grid" + viewId.replace(" ", "")).data("kendoGrid");
        if (viewType.toLowerCase() == "list") {
            localStorage.setItem('IsDirtyGridEdit', true);
            _grid = $("#grid" + viewId.replace(" ", "")).data("kendoGrid");
        }
        else{
            localStorage.setItem('IsDirtyReportEdit', true);
            _grid = $("#grid" + viewId.replace(" ", "")).data("kendoTreeList");
        }        

        gid_id = $(a).parent().parent().find('td:last').html();

        if (gid_id == undefined || gid_id == "undefined") {           
            gid_id = a.id.toString().replace('DT_', '');
            rowIndex = $(a).parent().parent().parent().parent().index();
        }

        var divItem = $('#' + tablename + viewId.replace(" ", ""));
        divItem.removeClass('ViewHeaderOn');
        ViewHeaderPanelOnClick($("#" + tablename + viewId.replace(" ", "")), viewId.replace(" ", ""), tablename);  //Apply view header style and save session details for selected record and view id..J

        $.ajax({
            type: 'POST',
            url: '/Desktop/Push_Changed_Field_Value_Into_Collection',
            data: { GIDID: gid_id, TableName: tablename, FieldName: fieldname, ViewId: viewId.replace(" ", ""), FieldValue: x, SelectedRowIndex: rowIndex },
            datatype: 'json',
            success: function (result) {

            },
            error: function (result) {

            }
        });
    }
    else if (enablebulksave == 'false' || enablebulksave == "false") {
        if (confirm('Are you sure you want to change the ' + title + '?')) {

            var _grid = $("#grid" + viewId.replace(" ", "")).data("kendoGrid");

            gid_id = $(a).parent().parent().find('td:last').html();

            if (gid_id == undefined || gid_id == "undefined") {           
                gid_id = a.id.toString().replace('DT_', '');
                rowIndex = $(a).parent().parent().parent().parent().index();
            }

            ViewHeaderPanelOnClick($("#" + tablename + viewId.replace(" ", "")), viewId.replace(" ", ""), tablename);  //Apply view header style and save session details for selected record and view id..J

            $.ajax({
                type: 'POST',
                url: '/Desktop/Push_Changed_Field_Value_Into_Collection',
                data: { GIDID: gid_id, TableName: tablename, FieldName: fieldname, ViewId: viewId.replace(" ", ""), FieldValue: x, SelectedRowIndex: rowIndex },
                datatype: 'json',
                success: function (result) {
                    SaveRows(viewId.replace(" ", ""), "", viewType);
                },
                error: function (result) {

                }
            });
        }
        else {
            if (viewType.toLowerCase() == "list") {
                var _grid = $("#grid" + viewId.replace(" ", "")).data("kendoGrid");
                var _currentPage = _grid.dataSource._page;
                reLoadViewsV2(viewId.replace(" ", ""), true, _currentPage);
            }
            else {
               refreshviewV2(viewId, viewId.replace(" ", ""), "report", false);
            }
            
        }
    }
}

function SaveRows(id, filename, viewType) {    
    showProgress();
    $.ajax({
        type: 'POST',
        url: '/Desktop/SaveChangedFieldValues',
        async: true,
        cache: false,
        data: { ViewId: id.replace(" ", ""), FileName: filename },
        //datatype: 'json',
        success: function (response) {
            
            alert(response.ResultMessage);
            localStorage.setItem('IsDirty_' + id.replace(" ", ""), false);
            if (viewType.toLowerCase() == "list") {
                localStorage.setItem('IsDirtyGridEdit', false);  
                var _grid = $("#grid" + id.replace(" ", "")).data("kendoGrid");
            }   
            else{
                localStorage.setItem('IsDirtyReportEdit', false);
                var _grid = $("#grid" + id.replace(" ", "")).data("kendoTreeList");
            }           
            
            var pageData = _grid.dataSource.view();

            if (response.ListOfTotalRecords != null) {
                for (var i = 0; i < response.ListOfTotalRecords.length; i++) {
                    if (response.ListOfTotalRecords[i].GIDID == pageData[response.ListOfTotalRecords[i].RowIndex].GID_ID) {
                        if (response.ListOfTotalRecords[i].FieldName.indexOf("DT") > -1) {
                            _grid.tbody.find(">tr[data-uid='" + pageData[response.ListOfTotalRecords[i].RowIndex].uid + "'] >td> span> span> span#" + response.ListOfTotalRecords[i].FieldName).removeClass("k-dirty");
                        }
                        else {
                            _grid.tbody.find(">tr[data-uid='" + pageData[response.ListOfTotalRecords[i].RowIndex].uid + "'] >td> span#" + response.ListOfTotalRecords[i].FieldName).removeClass("k-dirty");
                        }                        
                    }
                }
            }

            if (response.ListOfUnSavedRecords != null) {
                for (var i = 0; i < response.ListOfUnSavedRecords.length; i++) {
                    if (response.ListOfUnSavedRecords[i].GIDID == pageData[response.ListOfUnSavedRecords[i].RowIndex].GID_ID) {
                        if (response.ListOfUnSavedRecords[i].FieldName.indexOf("DT") > -1) {
                            _grid.tbody.find(">tr[data-uid='" + pageData[response.ListOfUnSavedRecords[i].RowIndex].uid + "'] >td> span> span> span#" + response.ListOfUnSavedRecords[i].FieldName).addClass("k-dirty");
                        }
                        else {
                            _grid.tbody.find(">tr[data-uid='" + pageData[response.ListOfUnSavedRecords[i].RowIndex].uid + "'] >td> span#" + response.ListOfUnSavedRecords[i].FieldName).addClass("k-dirty");
                        }
                    }
                }
            }

            hideProgress();
        },
        error: function (response) {

        }
    });
}

 function DTClicked(id)
 {
     var datepicker = $(id).data("kendoDatePicker");        
     if (datepicker == null) {            
         $(id).kendoDatePicker();
         PageSizeChanged = "true";
     }
 }