﻿using System;
using System.Web;
using System.IO;

namespace Selltis.BusinessLogic
{
	public class clBaseScripts
	{

		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		private clData goData;
		private clError goErr;
		private clLog goLog;
		//Dim goDef As clDefaults
		private clScrMngRowSet goScr;
		//Dim goUI As clUI
		private clPerm goPerm;
		//Dim goHist As clHistory
		public string sError;


		public void Initialize()
		{

			goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"];
			goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"];
			goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"];
			goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"];
			goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"];
			goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"];
			//goDef = HttpContext.Current.Session("goDef")
			goScr = (Selltis.BusinessLogic.clScrMngRowSet)HttpContext.Current.Session["goScr"];
			//goUI = HttpContext.Current.Session("goUI")
			goPerm = (Selltis.BusinessLogic.clPerm)HttpContext.Current.Session["goPerm"];
			//goHist = HttpContext.Current.Session("goHist")

		}

		//Function AC_RecordAfterSave(Optional ByRef par_doCallingObject As Object = vbNull, Optional ByRef par_oReturn As Object = vbNull, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "", Optional ByVal par_doArray As clArray = Nothing, Optional ByVal par_sMode As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "") As Boolean
		//    'MI 11/27/07 Sectionalized the script.
		//    'par_doCallingObject: Rowset object containing the record to be saved.
		//    'par_doArray: Unused.
		//    'par_sMode: 'CREATION' or 'MODIF'.
		//    'par_s2 to par_s5: Unused.
		//    'par_oReturn: Object passed to script ByRef.  Used to return values to calling process
		//    'par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
		//    'par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

		//    Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
		//    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

		//    Dim doRS As clRowSet = par_doCallingObject

		//    Dim doRSContacts As clRowSet
		//    Dim doRSCompanies As clRowSet

		//    'CS 3/1/13
		//    'Moving this code from AC_FormOnSave to RecAfterSave so that I can create only 1 CN rowset
		//    'and have this run quicker. Also disabled running validation and RecOnSave in the CN rowset.
		//    If goScr.IsSectionEnabled(sProc, par_sSections, "UpdateContacts") Then

		//        'Only try to update linked CNs next and last CN dates if workgroup option is set.
		//        If doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONSAVEAC_UPDATE_CN_DATES") = "1" Then
		//            If doRS.GetLinkCount("LNK_RELATED_CN") > 0 And doRS.oVar.GetVar("AC_FormONSave_CN_NADate") <> "1" Then
		//                doRS.oVar.SetVar("AC_FormOnSave_CN_NADate", "1")
		//                Dim oProducts As clArray = doRS.GetFieldVal("LNK_RELATED_PD", 2)
		//                doRSContacts = New clRowSet("CN", 1, "LNK_CONNECTED_AC='" & doRS.GetFieldVal("GID_ID") & "'", , "*,LNK_INTERESTEDIN_PD", , , , , , , True, True)
		//                If doRSContacts.Count() <> 0 Then
		//                    Do
		//                        Dim sInterval As String = goTR.NumToString(doRSContacts.GetFieldVal("INT_REVIEWINTERVAL", 2)) & " days from today"
		//                        doRSContacts.SetFieldVal("LNK_INTERESTEDIN_PD", oProducts, 2)
		//                        doRSContacts.SetFieldVal("DTE_LASTCONTACTDATE", "Today", 1)
		//                        doRSContacts.SetFieldVal("TXT_LASTCONTACTEDBY", goP.GetMe("CODE"))
		//                        doRSContacts.SetFieldVal("DTE_NEXTCONTACTDATE", sInterval, 1)

		//                        If doRSContacts.Commit() <> 1 Then
		//                            'update of CN failed
		//                            goLog.Log(sProc, "Updating Contact fields failed for record " & doRS.GetFieldVal("SYS_NAME") & ".", , , , , )
		//                        End If
		//                        If doRSContacts.GetNext() = 0 Then Exit Do
		//                    Loop
		//                End If
		//            End If
		//        End If
		//    End If

		//    'CS 3/1/13: Moved from FormOnSave to here. This is to create only 1 CO rowset and speed
		//    'up the save. Also removed validation and calling RecOnSave from the CO rowset.
		//    'CS 1/1/09: Update linked Companies
		//    'Check if Review field is in database
		//    If goData.IsFieldValid("CO", "CHK_REVIEW") = True Then
		//        If goScr.IsSectionEnabled(sProc, par_sSections, "UpdateCompanies") Then
		//            'Only try to update linked COs next and last review dates if workgroup option is set.
		//            If doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONSAVEAC_UPDATE_CO_DATES") = "1" Then
		//                If doRS.GetLinkCount("LNK_RELATED_CO") > 0 And doRS.oVar.GetVar("AC_FormONSave_CO_NRDate") <> "1" Then
		//                    doRS.oVar.SetVar("AC_FormOnSave_CO_NRDate", "1")
		//                    'automatically update next and last CN dates of linked CNs
		//                    doRSCompanies = New clRowSet("CO", 1, "LNK_CONNECTED_AC='" & doRS.GetFieldVal("GID_ID") & "'", , "*", , , , , , , True, True)
		//                    If doRSCompanies.Count <> 0 Then
		//                        Do
		//                            doRSCompanies.SetFieldVal("DTE_LASTREVIEWDATE", "Today", 1)
		//                            doRSCompanies.SetFieldVal("TXT_LASTREVIEWEDBY", goP.GetMe("CODE"))
		//                            doRSCompanies.SetFieldVal("DTE_NEXTREVIEWDATE", goTR.NumToString(doRSCompanies.GetFieldVal("INT_REVIEWINTERVAL", 2)) & " days from today", 1)
		//                            If doRSCompanies.Commit() <> 1 Then
		//                                'update of CO failed
		//                                goLog.Log(sProc, "Updating Company fields failed for AC record " & doRS.GetFieldVal("SYS_NAME") & ".", , , , , )
		//                            End If
		//                            If doRSCompanies.GetNext() = 0 Then Exit Do
		//                        Loop
		//                    End If
		//                End If
		//            End If
		//        End If
		//    End If

		//    Return True
		//End Function
		//Function AC_RecordOnSave(Optional ByRef par_doCallingObject As Object = vbNull, Optional ByRef par_oReturn As Object = vbNull, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "", Optional ByVal par_doArray As clArray = Nothing, Optional ByVal par_sMode As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "") As Boolean
		//    'MI 11/27/07 Sectionalized the script.
		//    'par_doCallingObject: Rowset object containing the record to be saved.
		//    'par_doArray: Unused.
		//    'par_sMode: 'CREATION' or 'MODIF'.
		//    'par_s2 to par_s5: Unused.
		//    'par_oReturn: Object passed to script ByRef.  Used to return values to calling process
		//    'par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
		//    'par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

		//    Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
		//    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)
		//    'goP.TraceLine("", "", sProc)

		//    Dim doRS As clRowSet 'object
		//    Dim doLink As clArray 'object
		//    Dim sFileName As String = "AC"
		//    Dim sSysName As String = ""
		//    Dim bResult As Boolean
		//    Dim sJournal As String
		//    Dim sDateStamp As String = ""


		//    doRS = par_doCallingObject

		//    'CS 5/17/10: Will be used in import utility context. When creating leads with import util lead notes are 
		//    'copied to lead journal. If user has option set in WOP, this will create a journal activity.
		//    doRS.oVar.SetVar("lLenJournal", Len(doRS.GetFieldVal("MMO_JOURNAL")))



		//    '-------------- ENFORCE ----------------
		//    'If goP.GetRunMode() <> "Import" Then
		//    If doRS.bBypassValidation <> True Then
		//    End If


		//    'CS 5/18/10
		//    'If we are in the 'import utility' context of creating a lead, copy notes to journal. 
		//    'When leads are created via a Selltis form, notes are already copied in FormOnSave.
		//    'I could not move all code here because journal creation is heavily dependent on interacting with the 
		//    'journal messagebox. 
		//    '---Copy Notes to Journal on creation of a lead from the import utility
		//    If goP.GetVar("PROGID") = "SELLIMP" Then
		//        If goScr.IsSectionEnabled(sProc, par_sSections, "CopyNotesToJournalIfNewLead") Then
		//            If doRS.GetFieldVal("MLS_PURPOSE", 2) = 8 And doRS.GetInfo("TYPE") = 2 Then
		//                'Purpose 8 = Lead
		//                'Check if there is a value in the Journal field already
		//                'Make sure Notes field has a value
		//                If doRS.GetFieldVal("MMO_NOTES") <> "" Then
		//                    sJournal = doRS.GetFieldVal("MMO_JOURNAL")
		//                    If sJournal <> "" Then
		//                        doRS.SetFieldVal("MMO_JOURNAL", sJournal & " " & doRS.GetFieldVal("MMO_NOTES"))
		//                    Else
		//                        goScr.RunScript("GetDateTimeStampRecord", doRS, , "NEUTRAL", , "CODE", "USERNOOFFSETLABEL", , sDateStamp) 'returns var sDateStamp
		//                        doRS.SetFieldVal("MMO_JOURNAL", sJournal & sDateStamp & " " & doRS.GetFieldVal("MMO_NOTES"))
		//                    End If
		//                End If
		//            End If
		//        End If
		//    End If

		//    'Fill End datetime with Start datetime if blank
		//    If goScr.IsSectionEnabled(sProc, par_sSections, "FillEndTimeIfBlank") Then
		//        If doRS.GetFieldVal("DTT_EndTime", clC.SELL_FRIENDLY) = "" Then
		//            '	gop.traceline("Filling end time with start time.","",sProc)
		//            doRS.SetFieldVal("DTT_EndTime", doRS.GetFieldVal("DTT_StartTime", 2), 2)
		//        End If
		//    End If

		//    If goScr.IsSectionEnabled(sProc, par_sSections, "CalcDuration") Then
		//        'Can't call Activity_CalcDuration because it requires the form object
		//        doRS.SetFieldVal("LI__DURATION", DateDiff("n", doRS.GetFieldVal("DTT_StartTime", 2), doRS.GetFieldVal("DTT_EndTime", 2)))
		//    End If

		//    'CS 5/17/10 In Excel import utility context create a journal Activity b/c the lead notes were copied to the lead journal.
		//    If goP.GetVar("PROGID") = "SELLIMP" Then
		//        If goScr.IsSectionEnabled(sProc, par_sSections, "CreateJournalActivityWithLeadNotes") Then
		//            'Check WOP               
		//            If doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%ONIMPORTLEADWNOTES_CREATE_AC") = "1" Then
		//                bResult = goScr.RunScript("Activity_CreateActLogRecord", doRS)
		//            End If
		//        End If
		//    End If


		//    'CS 6/28/11: For Web Submission activities, create an alert for the assigned user on add.
		//    'CS 8/11/11: Added sectionalization
		//    If goScr.IsSectionEnabled(sProc, par_sSections, "CreateWebSubmissionAlert") Then
		//        If doRS.GetInfo("TYPE") = "2" Then 'Then
		//            'look for web submission type and see if it is selected; mls list may have been customized
		//            Dim goList As New clList
		//            Dim sType As String = goList.LReadSeek("AC:TYPE", "VALUE", "WEB SUBMISSION") 'Find index number of web submission status in Ac type list
		//            'CS 8/11/11: If type looking for does not exist, the default MLS value is returned so need to 
		//            'check if returned that error and if so, skip this.
		//            'If sType <> "" Then 'Found web sub type
		//            If sType <> "" And goErr.GetLastError("NUMBER") <> "E30035" Then
		//                If doRS.GetFieldVal("MLS_TYPE", 2) = goTR.StringToNum(sType) Then
		//                    If doRS.GetLinkCount("LNK_CREDITEDTO_US") > 0 Then
		//                        'goUI.AddAlert(doRS.GetFieldVal("LNK_CREATEDBY_US%%TXT_NAMELAST") & ", " & doRS.GetFieldVal("LNK_CREATEDBY_US%%TXT_NAMEFIRST") & ": " & sSubject & "", clC.SELL_ALT_OPENRECORD, doRS.GetFieldVal("GID_ID"), doRS.GetFieldVal("LNK_CREDITEDTO_US"), "MessageInUrgent16.gif")
		//                        'goUI.AddAlert("New Web Submission", clC.SELL_ALT_OPENRECORD, doRS.GetFieldVal("GID_ID"), doRS.GetFieldVal("LNK_CREDITEDTO_US"), "WebForms16.gif")
		//                    End If
		//                End If
		//            End If
		//        End If
		//    End If



		//    '---------- AUTO-FILLED VALUES ----------
		//    'Fill CHK_CORR
		//    If goScr.IsSectionEnabled(sProc, par_sSections, "AutoFillCorrespondenceCheckbox") Then
		//        Select Case doRS.GetFieldVal("MLS_TYPE", clC.SELL_SYSTEM) '2 = system
		//            Case 3, 4, 5, 6, 7, 8 'CS 1/19/09 Remove WP types, 9, 10       'Correspondence
		//                doRS.SetFieldVal("CHK_CORR", 1, clC.SELL_SYSTEM)
		//            Case Else
		//                doRS.SetFieldVal("CHK_CORR", 0, clC.SELL_SYSTEM)
		//        End Select
		//    End If
		//    'CHK_CORRRECEIVED
		//    If goScr.IsSectionEnabled(sProc, par_sSections, "AutoFillCorrespondenceReceivedCheckbox") Then
		//        Select Case doRS.GetFieldVal("MLS_TYPE", clC.SELL_SYSTEM)
		//            Case 3, 5, 7 'CS 1/19/09 Remove WP types, 9
		//                doRS.SetFieldVal("CHK_CORRRECEIVED", 1, clC.SELL_SYSTEM)
		//            Case Else
		//                doRS.SetFieldVal("CHK_CORRRECEIVED", 0, clC.SELL_SYSTEM)
		//        End Select
		//    End If
		//    'CHK_CORRSENT
		//    If goScr.IsSectionEnabled(sProc, par_sSections, "AutoFillCorrespondenceSentCheckbox") Then
		//        Select Case doRS.GetFieldVal("MLS_TYPE", clC.SELL_SYSTEM)
		//            Case 4, 6, 8 'CS 1/19/09 Remove WP code, 10
		//                doRS.SetFieldVal("CHK_CORRSENT", 1, clC.SELL_SYSTEM)
		//            Case Else
		//                doRS.SetFieldVal("CHK_CORRSENT", 0, clC.SELL_SYSTEM)
		//        End Select
		//    End If
		//    'CHK_LEAD
		//    If goScr.IsSectionEnabled(sProc, par_sSections, "AutoFillLeadCheckbox") Then
		//        Select Case doRS.GetFieldVal("MLS_PURPOSE", clC.SELL_SYSTEM)
		//            'Lead, Req Lit, Req Web Demo, Req Call, Req Qte, Submit Promoter Lead
		//            Case 8, 21, 22, 23, 24, 25
		//                doRS.SetFieldVal("CHK_LEAD", 1, clC.SELL_SYSTEM)
		//            Case Else
		//                doRS.SetFieldVal("CHK_LEAD", 0, clC.SELL_SYSTEM)
		//        End Select
		//    End If
		//    'CHK_SERVICE, CHK_SERVICEEXT
		//    If goScr.IsSectionEnabled(sProc, par_sSections, "AutoFillServiceCheckboxes") Then
		//        Select Case doRS.GetFieldVal("MLS_PURPOSE", clC.SELL_SYSTEM)
		//            Case 6, 7       '6=Service, 7=Support
		//                doRS.SetFieldVal("CHK_SERVICE", 1, clC.SELL_SYSTEM)
		//                doRS.SetFieldVal("CHK_SERVICEEXT", 1, clC.SELL_SYSTEM)
		//            Case 4, 53      '4=Order, 53=Training
		//                doRS.SetFieldVal("CHK_SERVICE", 0, clC.SELL_SYSTEM)
		//                doRS.SetFieldVal("CHK_SERVICEEXT", 1, clC.SELL_SYSTEM)
		//            Case Else
		//                doRS.SetFieldVal("CHK_SERVICE", 0, clC.SELL_SYSTEM)
		//                doRS.SetFieldVal("CHK_SERVICEEXT", 0, clC.SELL_SYSTEM)
		//        End Select
		//    End If

		//    'CS 1/19/09: Remove WP code
		//    'If goScr.IsSectionEnabled(sProc, par_sSections, "AutoFillSentCheckbox") Then
		//    '    Select Case doRS.GetFieldVal("MLS_TYPE", clC.SELL_SYSTEM)
		//    '        Case 9 'WP Submission Sent
		//    '            'always set chk_sent to 1: Per MI 5/22/07
		//    '            doRS.SetFieldVal("CHK_SENT", 1, clC.SELL_SYSTEM)
		//    '    End Select
		//    'End If

		//    If goScr.IsSectionEnabled(sProc, par_sSections, "AutoFillYearMonthDay") Then
		//        Dim dtDateTime As DateTime
		//        dtDateTime = goTR.UTC_LocalToUTC(doRS.GetFieldVal("DTT_STARTTIME", 2))
		//        If goData.IsFieldValid("AC", "TXT_StartTimeYear") Then doRS.SetFieldVal("TXT_StartTimeYear", goTR.GetYear(dtDateTime))
		//        If goData.IsFieldValid("AC", "SI__StartTimeMonth") Then doRS.SetFieldVal("SI__StartTimeMonth", goTR.StringToNum(goTR.GetMonth(dtDateTime)))
		//        If goData.IsFieldValid("AC", "SI__StartTimeDay") Then doRS.SetFieldVal("SI__StartTimeDay", goTR.StringToNum(goTR.GetDay(dtDateTime)))
		//    End If


		//    'goP.TraceLine("GetRunMode(): '" & goP.GetRunMode() & "'", "", sProc)
		//    If goP.GetRunMode() <> "Import" Then

		//        'CS 2/4/09: Removing Email Alias code. This is handled in 
		//        'clEmail.LogMessage which calls GetEmailAliasLinks
		//        If goScr.IsSectionEnabled(sProc, par_sSections, "FillEmailAliasLink") Then
		//            bResult = goScr.RunScript("Activity_EmailAlias", doRS)
		//            If doRS.oVar.GetVar("EL_ID") <> "" Then
		//                doRS.SetFieldVal("LNK_Related_EL", doRS.oVar.GetVar("EL_ID"))
		//            End If
		//        End If

		//        'CS 1/16/11: Phone Alias for Call Logger
		//        If goScr.IsSectionEnabled(sProc, par_sSections, "FillPhoneAliasLink") Then
		//            bResult = goScr.RunScript("Activity_PhoneAlias", doRS)
		//        End If



		//        '*** MI 11/27/07 This is done further down
		//        'If goScr.IsSectionEnabled(sProc, par_sSections, "FillUserLinks") Then
		//        '    goScr.RunScript("Activity_FillEmplConns", doRS)
		//        'End If

		//        '----- Set the Related Territory connection from Related Company's territory
		//        If goScr.IsSectionEnabled(sProc, par_sSections, "FillTerritoryFromCompanyTerritory") Then
		//            doRS.ClearLinkAll("LNK_Related_TE")
		//            doLink = New clArray()
		//            doLink = doRS.GetLinkVal("LNK_Related_CO%%LNK_In_TE", doLink)
		//            doRS.SetLinkVal("LNK_Related_TE", doLink)
		//            doLink = Nothing
		//        End If


		//        ''LNK_INVOLVES_COMPANY
		//        'doLink = new clArray()
		//        'doLink = doRS.GetLinkVal("LNK_RELATED_CO",doLink,False)
		//        'doLink = doRS.GetLinkVal("LNK_BILL_CO",doLink,False)
		//        'doLink = doRS.GetLinkVal("LNK_ORIGINAL_CO",doLink,False)
		//        '	doRS.SetLinkVal("LNK_INVOLVES_CO",doLink)

		//        'LNK_INVOLVES_CONTACT
		//        If goScr.IsSectionEnabled(sProc, par_sSections, "FillInvolvesContactLink") Then
		//            doLink = New clArray()
		//            'doLink = doRS.GetLinkVal("LNK_RELATED_CN",doLink,False)
		//            'doLink = doRS.GetLinkVal("LNK_ORIGINAL_CN",doLink,False)
		//            doLink = doRS.GetLinkVal("LNK_CC_CN", doLink, False)
		//            doLink = doRS.GetLinkVal("LNK_BC_CN", doLink, False)
		//            doRS.SetLinkVal("LNK_INVOLVES_CN", doLink)
		//        End If

		//        'LNK_INVOLVES_USER
		//        If goScr.IsSectionEnabled(sProc, par_sSections, "FillInvolvesUserLink") Then
		//            doLink = New clArray()
		//            doLink = doRS.GetLinkVal("LNK_CREATEDBY_US", doLink, False)
		//            doLink = doRS.GetLinkVal("LNK_CREDITEDTO_US", doLink, False)
		//            doRS.SetLinkVal("LNK_INVOLVES_US", doLink)
		//        End If
		//    End If

		//    SendEmailAlerts(doRS, par_sMode)

		//    Return True


		//End Function
		//Function AP_RecordBeforeDelete(Optional ByRef par_doCallingObject As Object = vbNull, Optional ByVal par_doArray As clArray = Nothing, Optional ByVal par_s1 As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "", Optional ByRef par_oReturn As Object = vbNull, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "") As Boolean
		//    'par_doCallingObject: Form object calling this script. Do not delete in script!
		//    'par_doArray: Unused.
		//    'par_s1: Unused.
		//    'par_s2 to par_s5: Unused.
		//    'par_oReturn: Object passed to script ByRef.  Used to return values to calling process
		//    'par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
		//    'par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

		//    Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
		//    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

		//    Dim doRS As clRowSet = par_doCallingObject
		//    Dim doCopy As clRowSet

		//    'CS 9/27/11 If the appointment being deleted has a LNK_SHAREDCLONE_AP or a LNK_CLONEOF_AP, delete it as well.
		//    If doRS.GetLinkCount("LNK_SHAREDCLONE_AP") > 0 And doRS.oVar.GetVar("AP_DeletePrivate") <> "1" Then
		//        'Delete the shared copy of this private appt
		//        doCopy = New clRowSet("AP", 1, "GID_ID = '" & doRS.GetFieldVal("LNK_SHAREDCLONE_AP") & "'", , , , , , , , , doRS.bBypassValidation)
		//        doCopy.oVar.SetVar("AP_DeleteShared", "1")
		//        doCopy.DeleteRecord()
		//        doCopy.oVar.SetVar("AP_DeleteShared", "0")
		//        doCopy = Nothing
		//    ElseIf doRS.GetLinkCount("LNK_CLONEOF_AP") > 0 And doRS.oVar.GetVar("AP_DeleteShared") <> "1" Then
		//        'Delete the private appt for this 'blank' shared appt
		//        doCopy = New clRowSet("AP", 1, "GID_ID = '" & doRS.GetFieldVal("LNK_CLONEOF_AP") & "'", , , , , , , , , doRS.bBypassValidation)
		//        doCopy.oVar.SetVar("AP_DeletePrivate", "1")
		//        doCopy.DeleteRecord()
		//        doCopy.oVar.SetVar("AP_DeletePrivate", "0")
		//        doCopy = Nothing
		//    End If

		//    Return True

		//End Function
		public bool AP_RecordOnSave(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray, string par_sMode, string par_s2, string par_s3, string par_s4)
		{
			return AP_RecordOnSave(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray, par_sMode, par_s2, par_s3, par_s4, "");
		}

		public bool AP_RecordOnSave(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray, string par_sMode, string par_s2, string par_s3)
		{
			return AP_RecordOnSave(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray, par_sMode, par_s2, par_s3, "", "");
		}

		public bool AP_RecordOnSave(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray, string par_sMode, string par_s2)
		{
			return AP_RecordOnSave(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray, par_sMode, par_s2, "", "", "");
		}

		public bool AP_RecordOnSave(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray, string par_sMode)
		{
			return AP_RecordOnSave(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray, par_sMode, "", "", "", "");
		}

		public bool AP_RecordOnSave(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray)
		{
			return AP_RecordOnSave(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray, "", "", "", "", "");
		}

		public bool AP_RecordOnSave(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections)
		{
			return AP_RecordOnSave(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
		}

		public bool AP_RecordOnSave(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext)
		{
			string tempVar = "";
			return AP_RecordOnSave(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref tempVar, null, "", "", "", "", "");
		}

		public bool AP_RecordOnSave(ref object par_doCallingObject, ref object par_oReturn)
		{
			bool tempVar = true;
			string tempVar2 = "";
			return AP_RecordOnSave(ref par_doCallingObject, ref par_oReturn, ref tempVar, ref tempVar2, null, "", "", "", "", "");
		}

		public bool AP_RecordOnSave(ref object par_doCallingObject)
		{
			object tempVar = null;
			bool tempVar2 = true;
			string tempVar3 = "";
			return AP_RecordOnSave(ref par_doCallingObject, ref tempVar, ref tempVar2, ref tempVar3, null, "", "", "", "", "");
		}

		public bool AP_RecordOnSave()
		{
			object tempVar = null;
			object tempVar2 = null;
			bool tempVar3 = true;
			string tempVar4 = "";
			return AP_RecordOnSave(ref tempVar, ref tempVar2, ref tempVar3, ref tempVar4, null, "", "", "", "", "");
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Function AP_RecordOnSave(Optional ByRef par_doCallingObject As Object = vbNull, Optional ByRef par_oReturn As Object = vbNull, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "", Optional ByVal par_doArray As clArray = Nothing, Optional ByVal par_sMode As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "") As Boolean
		public bool AP_RecordOnSave(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray, string par_sMode, string par_s2, string par_s3, string par_s4, string par_s5)
		{
			//par_doCallingObject: Rowset object containing the record to be saved.
			//par_doArray: Unused.
			//par_sMode: 'CREATION' or 'MODIF'.
			//par_s2 to par_s5: Unused.
			//par_oReturn: Object passed to script ByRef.  Used to return values to calling process
			//par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
			//par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

			string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//goP.TraceLine("", "", sProc)

			clRowSet doRS = null;
			clArray doLink = null;
			string sFileName = "AP";
			string sDate = null;
			string sTime = null;
			long lInterval = 0;
			long lMultiplier = 0;
			long lAhead = 0;
			string sSysName = "";
			DateTime dtDateTime = default(DateTime);
			bool bUpdate = false;

			//CS: Per MI, Commenting out IsObjAssigned
			//If Not IsObjectAssigned(par_doCallingObject) Then
			//    goLog.SetError(30012, sProc, "", "par_doCallingObject")
			//    ' An internal error has occurred. Object '[1]' is not assigned.
			//    '
			//    'Please contact Selltis support.
			//    Return False
			//Else
			doRS = (Selltis.BusinessLogic.clRowSet)par_doCallingObject;
			//End If       

			//Filling Alarm Date and Time
			//goP.TraceLine("Calculating Alarm date and time", "", sProc)
			//goP.TraceLine("CHK_ALARM: '" & doRS.GetFieldVal("CHK_ALARM", 2) & "'", "", sProc)
			if (Convert.ToInt32(doRS.GetFieldVal("CHK_ALARM", 2)) == 1)
			{
				//goP.TraceLine("CHK_ALARM is checked, calc and fill alarm date and time", "", sProc)
				sDate = Convert.ToString(doRS.GetFieldVal("DTE_STARTTIME", 2));
				sTime = Convert.ToString(doRS.GetFieldVal("TME_STARTTIME", 1));
				//goP.TraceLine("sDate: '" & sDate & "'", "", sProc)
				//goP.TraceLine("sTime: '" & sTime & "'", "", sProc)
				if (sDate == "" || Convert.ToDateTime(sDate) == (Convert.ToDateTime(clC.SELL_BLANK_DATETIME))) //CS added BLANK test
				{
					//Date is blank, can't set alarm date, treat like Alarm is not checked.
					//If a value already exists in Alarm Date/Time fields, leave them as is
					//Perhaps they were written by import.
					//goP.TraceLine("sDate is blank", "", sProc)
				}
				else
				{
					//goP.TraceLine("sDate is not blank", "", sProc)
					//CS: Commented out this block because TME will always be set to midnight if the user
					//did not explicitly set the time. 
					//If sTime = "" Then
					//    'Time is blank, treat as midnight
					//    goP.TraceLine("sTime is blank, setting it to 00000000", "", sProc)
					//    sTime = "00000000"
					//End If
					//Set time multiplier based on Alarm Units. This field is currently
					//not exposed on the form. Minutes are selected by default.
					//This field is currently used to map Palm Pilot's alarm units
					//so they wouldn't get lost during Palm syncing
					//goP.TraceLine("MLS_ALARMUNITS: '" & doRS.GetFieldVal("MLS_ALARMUNITS", 2) & "'", "", sProc)
					switch (doRS.GetFieldVal("MLS_ALARMUNITS", 2))
					{
						case "2": //Hours
							lMultiplier = 60;
							break;
						case "3": //Days
							lMultiplier = 1440; //60*24
							break;
						default: //Minutes
							lMultiplier = 1;
							break;
					}
					//Calculate time ahead in minutes
					//goP.TraceLine("Calcing lAhead", "", sProc)
					lInterval = Convert.ToInt64(doRS.GetFieldVal("LI__ALARMINTERVAL", 2));
					//goP.TraceLine("LI__ALARMINTERVAL (lInterval): '" & lInterval & "'", "", sProc)
					//goP.TraceLine("Multiplier: '" & lMultiplier & "'", "", sProc)
					lAhead = lInterval * lMultiplier;
					//Invert the negative symbol - a positive number means minutes before, not after
					if (lAhead < 0)
					{
						lAhead = Math.Abs(lAhead);
					}
					else
					{
						lAhead -= lAhead * 2;
					}
					//goP.TraceLine("lAhead (minutes ahead) after inverting the negative symbol: '" & lAhead & "'", "", sProc)
					//goP.TraceLine("Running AddMinutes", "", sProc)
					string sDateTime = sDate + " " + sTime;
					//'CS added
					//Dim sDateTime As String
					//'sDate has just the date; sTime has the date and time
					//If sDate <> sTime Then
					//    sDateTime = sTime
					//Else
					//    sDateTime = sDate
					//End If
					DateTime dDateTime = Convert.ToDateTime(sDateTime);
					if (!goTR.AddMinutes(ref dDateTime, lAhead))
					{
						goErr.SetWarning(30200, sProc, "", "Error calculating alarm date/time: goTr.AddMinutes returned False.");
						// [1]
						//goP.TraceLine("AddMinutes returned false, setting Return False", "", sProc)
						return false;
					}
					//goP.TraceLine("Setting Alarm date: '" & sDate & "', time: '" & sTime & "'", "", sProc)
					//CS commented below line and added setting DTT
					//doRS.SetFieldVal("DTE_ALARMTIME", sDate, 2)
					doRS.SetFieldVal("DTT_ALARMTIME", dDateTime, 2);
					//CS commented
					//doRS.SetFieldVal("TME_ALARMTIME", sTime, 2)
				}
			}

			//If goP.GetRunMode() <> "Import" Then
			if (doRS.bBypassValidation != true)
			{
				//Start Date cannot be left empty or invalid
				//goP.TraceLine("Start Date: '" & doRS.GetFieldVal("DTE_StartTime", 1) & "'", "", sProc)
				//CS: 7/6/07 In MD
				//If doRS.GetFieldVal("DTT_StartTime", 1) = "" Then
				//    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel(sFileName, "DTT_StartTime"), "", "", "", "", "", "", "", "", "DTT_StartTime")
				//    ' Please fill out the '[1]' field.
				//    'goP.TraceLine("Set error 30029, exiting.", "", sProc)
				//    Return False
				//End If

				//'CS: For now setting the end date/time as the start date/time if it is blank...until issues below are solved.
				//Dim sDate2 As String
				//sDate2 = doRS.GetFieldVal("DTT_StartTime", 1)
				//If doRS.GetFieldVal("DTT_EndTime", 1) = "" Then
				//    doRS.SetFieldVal("DTT_EndTime", sDate2, 1)
				//End If

				//CS: 7/6/07 In MD
				//'Coordinator link empty
				//If doRS.GetLinkCount("LNK_COORDINATEDBY_US") = 0 Then
				//    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel(sFileName, "LNK_Coordinatedby_US"), "", "", "", "", "", "", "", "", "LNK_Coordinatedby_Us")
				//    Return False
				//End If
				//'Description blank
				//If doRS.GetFieldVal("TXT_DESCRIPTION") = "" Then
				//    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel(sFileName, "TXT_Description"), "", "", "", "", "", "", "", "", "TXT_Description")
				//    Return False
				//End If

				//CS: Per MI, Commented out this code because not sure how to deal with filling times based on blank.
				//Times are now midnight not blank. Will figure out later.
				//'Fill Start Time with End Time if blank
				//goP.TraceLine("Start Time: '" & doRS.GetFieldVal("TME_StartTime", 1) & "'", "", sProc)
				//If doRS.GetFieldVal("TME_StartTime", 1) = "" Then
				//    goP.TraceLine("Filling start time with end time.", "", sProc)
				//    doRS.SetFieldVal("TME_StartTime", doRS.GetFieldVal("TME_EndTime", 1), 1)
				//End If

				//CS: Same comment as above.
				//'Fill End Time with Start Time if blank
				//goP.TraceLine("End Time: '" & doRS.GetFieldVal("TME_EndTime", 1) & "'", "", sProc)
				//If doRS.GetFieldVal("TME_EndTime", 1) = "" Then
				//    goP.TraceLine("Filling end time with start time.", "", sProc)
				//    doRS.SetFieldVal("TME_EndTime", doRS.GetFieldVal("TME_StartTime", 1), 1)
				//End If

				//CS: Same comment as above.
				//'Fill End Date based on Start Date
				//'If End time is before start time, the End Date is one day forward
				//'We must support this due to time zone differences.
				//'We don't support multi-day appointments. Appts should not exceed 24 hours.
				//'Daily recurring Appts longer than 24hrs would cause overlapping appts.
				//If doRS.GetFieldVal("TME_EndTime", 2) < doRS.GetFieldVal("TME_StartTime", 2) Then
				//    'Set End Date to Start Date + 1
				//    doRS.SetFieldVal("DTE_EndTime", "1 day after " & doRS.GetFieldVal("DTE_STARTTIME", 1), 1)
				//Else
				//    doRS.SetFieldVal("DTE_EndTime", doRS.GetFieldVal("DTE_STARTTIME", 1), 1)
				//End If

				//Enforce Max value in Alarm interval: 182 days from Date/Time
				//Currently enforced only in FormOnSave script.
				//One day is 8640000 hundredths of a second
				//2147483648 (long int max) allows storing hundredths for 248 days
				//This is needed for calculating alarm date/time.

			}

			if (goScr.IsSectionEnabled(sProc, par_sSections, "App_FillPhone"))
			{
				object temp_doRS = doRS;
				goScr.RunScript("App_FillPhone", ref temp_doRS);
					doRS = (Selltis.BusinessLogic.clRowSet)temp_doRS;
			}

			if (goScr.IsSectionEnabled(sProc, par_sSections, "App_FillContactInfo"))
			{
				object temp_doRS = doRS;
				goScr.RunScript("App_FillContactInfo", ref temp_doRS);
					doRS = (Selltis.BusinessLogic.clRowSet)temp_doRS;
			}

			if (goScr.IsSectionEnabled(sProc, par_sSections, "App_ConnectVendors"))
			{
				object temp_doRS = doRS;
				goScr.RunScript("App_ConnectVendors", ref temp_doRS);
					doRS = (Selltis.BusinessLogic.clRowSet)temp_doRS;
			}

			if (goScr.IsSectionEnabled(sProc, par_sSections, "AutoFillYearMonthDay"))
			{
				DateTime tempVar5 = Convert.ToDateTime(doRS.GetFieldVal("DTT_STARTTIME", 2));
				dtDateTime = goTR.UTC_LocalToUTC(ref tempVar5);
				doRS.SetFieldVal("TXT_StartTimeYear", goTR.GetYear(dtDateTime));
				doRS.SetFieldVal("SI__StartTimeMonth", goTR.StringToNum(goTR.GetMonth(dtDateTime)));
				doRS.SetFieldVal("SI__StartTimeDay", goTR.StringToNum(goTR.GetDay(dtDateTime)));
			}

			//CS 9/26/11: In edit mode, update matching fields in the linked shared or private appt
			//only run this if we are not running reconsave as a result of updating the linked
			//appt already.
			if (doRS.GetInfo("TYPE") == "1" && Convert.ToString(doRS.oVar.GetVar("AP_UpdatingPrivate")) != "1" && Convert.ToString(doRS.oVar.GetVar("AP_UpdatingShared")) != "1")
			{
				//is this the private appt and we have a shared duplicate? Also, don't run this
				//if reconsave is running b/c we are updating the linked private appt
				if (doRS.GetLinkCount("LNK_SHAREDCLONE_AP") > 0)
				{

					//Compare fields and see if need to update linked shared appt
					clRowSet doRSAppt = new clRowSet("AP", 3, "GID_ID=" + Convert.ToString(doRS.GetFieldVal("LNK_SHAREDCLONE_AP")) + "", "", "*,LNK_COORDINATEDBY_US,LNK_INVOLVES_US");
					if (doRSAppt.GetFirst() == 1)
					{
						if (doRSAppt.GetFieldVal("DTT_STARTTIME", 2) != doRS.GetFieldVal("DTT_STARTTIME", 2))
						{
							bUpdate = true;
						}
						if (doRSAppt.GetFieldVal("DTT_ENDTIME", 2) != doRS.GetFieldVal("DTT_ENDTIME", 2))
						{
							bUpdate = true;
						}
						if (doRSAppt.GetFieldVal("CHK_COMPLETED", 2) != doRS.GetFieldVal("CHK_COMPLETED", 2))
						{
							bUpdate = true;
						}
						if (doRSAppt.GetFieldVal("CHK_REPORT", 2) != doRS.GetFieldVal("CHK_REPORT", 2))
						{
							bUpdate = true;
						}
						if (doRSAppt.GetFieldVal("LNK_COORDINATEDBY_US") != doRS.GetFieldVal("LNK_COORDINATEDBY_US"))
						{
							bUpdate = true;
						}
						if (doRSAppt.GetFieldVal("LNK_INVOLVES_US") != doRS.GetFieldVal("LNK_INVOLVES_US"))
						{
							bUpdate = true;
						}
						if (doRSAppt.GetFieldVal("CHK_DEMODATA", 2) != doRS.GetFieldVal("CHK_DEMODATA", 2))
						{
							bUpdate = true;
						}
						if (goData.IsFieldValid("AP", "MLS_TIMEZONE"))
						{
							if (doRSAppt.GetFieldVal("MLS_TIMEZONE", 2) != doRS.GetFieldVal("MLS_TIMEZONE", 2))
							{
								bUpdate = true;
							}
						}

						//If doRSAppt.GetFieldVal("MLS_TYPE", 2) <> doRS.GetFieldVal("MLS_TYPE", 2) Then bUpdate = True
						doRSAppt = null;

						if (bUpdate == true) //need to update the linked shared appt
						{
							doRSAppt = new clRowSet("AP", 1, "GID_ID=" + Convert.ToString(doRS.GetFieldVal("LNK_SHAREDCLONE_AP")) + "", "", "*,LNK_COORDINATEDBY_US");
							if (doRSAppt.GetFirst() == 1)
							{
								doRSAppt.oVar.SetVar("AP_UpdatingShared", "1");
								doRSAppt.SetFieldVal("DTT_STARTTIME", doRS.GetFieldVal("DTT_STARTTIME", 2), 2);
								doRSAppt.SetFieldVal("DTT_ENDTIME", doRS.GetFieldVal("DTT_ENDTIME", 2), 2);
								doRSAppt.SetFieldVal("CHK_COMPLETED", doRS.GetFieldVal("CHK_COMPLETED", 2), 2);
								doRSAppt.SetFieldVal("CHK_REPORT", doRS.GetFieldVal("CHK_REPORT", 2), 2);
								doRSAppt.SetFieldVal("LNK_COORDINATEDBY_US", doRS.GetFieldVal("LNK_COORDINATEDBY_US", 2), 2);
								doRSAppt.SetFieldVal("CHK_DEMODATA", doRS.GetFieldVal("CHK_DEMODATA", 2), 2);
								doRSAppt.SetFieldVal("LNK_INVOLVES_US", doRS.GetFieldVal("LNK_INVOLVES_US", 2), 2);
								if (goData.IsFieldValid("AP", "MLS_TIMEZONE"))
								{
									doRSAppt.SetFieldVal("MLS_TIMEZONE", doRS.GetFieldVal("MLS_TIMEZONE", 2), 2);
								}

								//doRSAppt.SetFieldVal("MLS_TYPE", doRS.GetFieldVal("MLS_TYPE", 2), 2)
								if (doRSAppt.Commit() != 1)
								{
									goLog.Log(sProc, "Could not update the linked shared appointment for appointment '" + Convert.ToString(doRS.GetFieldVal("SYS_NAME")) + "'", 1, false, true);
								}
								doRSAppt.oVar.SetVar("AP_UpdatingShared", "0");
							}
						}
					}
				}
				//Is there a linked private appt for this shared appt?
				//Also be sure we are not running RecOnSave as a result of updating the linked shared
				//appt, otherwise we will end up in a loop
				if (doRS.GetLinkCount("LNK_CLONEOF_AP") > 0)
				{
					//Compare fields and see if need to update linked appt
					clRowSet doRSAppt = new clRowSet("AP", 3, "GID_ID=" + Convert.ToString(doRS.GetFieldVal("LNK_CLONEOF_AP")) + "", "", "*,LNK_COORDINATEDBY_US");
					if (doRSAppt.GetFirst() == 1)
					{
						if (doRSAppt.GetFieldVal("DTT_STARTTIME", 2) != doRS.GetFieldVal("DTT_STARTTIME", 2))
						{
							bUpdate = true;
						}
						if (doRSAppt.GetFieldVal("DTT_ENDTIME", 2) != doRS.GetFieldVal("DTT_ENDTIME", 2))
						{
							bUpdate = true;
						}
						if (doRSAppt.GetFieldVal("CHK_COMPLETED", 2) != doRS.GetFieldVal("CHK_COMPLETED", 2))
						{
							bUpdate = true;
						}
						if (doRSAppt.GetFieldVal("CHK_REPORT", 2) != doRS.GetFieldVal("CHK_REPORT", 2))
						{
							bUpdate = true;
						}
						if (doRSAppt.GetFieldVal("LNK_COORDINATEDBY_US") != doRS.GetFieldVal("LNK_COORDINATEDBY_US"))
						{
							bUpdate = true;
						}
						//If doRSAppt.GetFieldVal("MLS_TYPE", 2) <> doRS.GetFieldVal("MLS_TYPE", 2) Then bUpdate = True 'if change type on shared copy need to update private, but not vice versa
						if (doRSAppt.GetFieldVal("LNK_INVOLVES_US") != doRS.GetFieldVal("LNK_INVOLVES_US"))
						{
							bUpdate = true;
						}
						if (doRSAppt.GetFieldVal("CHK_DEMODATA", 2) != doRS.GetFieldVal("CHK_DEMODATA", 2))
						{
							bUpdate = true;
						}
						if (goData.IsFieldValid("AP", "MLS_TIMEZONE"))
						{
							if (doRSAppt.GetFieldVal("MLS_TIMEZONE", 2) != doRS.GetFieldVal("MLS_TIMEZONE", 2))
							{
								bUpdate = true;
							}
						}
						doRSAppt = null;

						if (bUpdate == true) //need to update the linked private appt
						{
							doRSAppt = new clRowSet("AP", 1, "GID_ID=" + Convert.ToString(doRS.GetFieldVal("LNK_CLONEOF_AP")) + "", "", "*,LNK_COORDINATEDBY_US");
							if (doRSAppt.GetFirst() == 1)
							{
								doRSAppt.oVar.SetVar("AP_UpdatingPrivate", "1");
								doRSAppt.SetFieldVal("DTT_STARTTIME", doRS.GetFieldVal("DTT_STARTTIME", 2), 2);
								doRSAppt.SetFieldVal("DTT_ENDTIME", doRS.GetFieldVal("DTT_ENDTIME", 2), 2);
								doRSAppt.SetFieldVal("CHK_COMPLETED", doRS.GetFieldVal("CHK_COMPLETED", 2), 2);
								doRSAppt.SetFieldVal("CHK_REPORT", doRS.GetFieldVal("CHK_REPORT", 2), 2);
								doRSAppt.SetFieldVal("LNK_COORDINATEDBY_US", doRS.GetFieldVal("LNK_COORDINATEDBY_US", 2), 2);
								//doRSAppt.SetFieldVal("MLS_TYPE", doRS.GetFieldVal("MLS_TYPE", 2), 2)
								doRSAppt.SetFieldVal("CHK_DEMODATA", doRS.GetFieldVal("CHK_DEMODATA", 2), 2);
								doRSAppt.SetFieldVal("LNK_INVOLVES_US", doRS.GetFieldVal("LNK_INVOLVES_US", 2), 2);
								if (goData.IsFieldValid("AP", "MLS_TIMEZONE"))
								{
									doRSAppt.SetFieldVal("MLS_TIMEZONE", doRS.GetFieldVal("MLS_TIMEZONE", 2), 2);
								}
								if (doRSAppt.Commit() != 1)
								{
									goLog.Log(sProc, "Could not update the linked private appointment for appointment '" + Convert.ToString(doRS.GetFieldVal("SYS_NAME")) + "'", 1, false, true);
								}
								doRSAppt.oVar.SetVar("AP_UpdatingPrivate", "0");
							}
						}
					}
				}
			}

			if (goP.GetRunMode() != "Import")
			{
				//LNK_INVOLVES_USER
				if (goScr.IsSectionEnabled(sProc, par_sSections, "FillInvolvesUser"))
				{
					doLink = new clArray();
					doLink = doRS.GetLinkVal("LNK_COORDINATEDBY_US", ref doLink, false);
					//doLink = doRS.GetLinkVal("LNK_CREATEDBY_US",doLink,False)
					doRS.SetLinkVal("LNK_INVOLVES_US", doLink);
					doLink = null;
				}
			}

			return true;

		}
		//Function CN_RecordOnSave(Optional ByRef par_doCallingObject As Object = vbNull, Optional ByRef par_oReturn As Object = vbNull, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "", Optional ByVal par_doArray As clArray = Nothing, Optional ByVal par_sMode As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "") As Boolean
		//    'par_doCallingObject: Rowset object containing the record to be saved.
		//    'par_doArray: Unused.
		//    'par_sMode: 'CREATION' or 'MODIF'.
		//    'par_s2 to par_s5: Unused.
		//    'par_oReturn: Object passed to script ByRef.  Used to return values to calling process
		//    'par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
		//    'par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

		//    Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
		//    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

		//    'goP.TraceLine("", "", sProc)

		//    Dim doRS As clRowSet
		//    Dim doLink As clArray
		//    Dim sFileName As String = "CN"
		//    Dim sSysName As String = ""
		//    Dim sEmail As String


		//    doRS = par_doCallingObject

		//    If doRS.bBypassValidation <> True Then
		//        'CS: 7/6/07 In MD
		//        ''Enforce Last Name
		//        'If doRS.GetFieldVal("txt_namelast", 1) = "" Then
		//        '    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel(sFileName, "TXT_NameLast"), "", "", "", "", "", "", "", "", "TXT_Namelast")
		//        '    ' Please fill out the '[1]' field.
		//        '    'goP.TraceLine("Set error 30029, exiting.", "", sProc)
		//        '    Return False
		//        'End If

		//        'If doRS.GetLinkCount("LNK_RELATED_US") = 0 Then
		//        '    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel(sFileName, "LNK_RELATED_US"), "", "", "", "", "", "", "", "", "LNK_Related_US")
		//        '    Return False
		//        'End If

		//    End If

		//    'Fill Original Creation Date/Time if blank
		//    If doRS.GetInfo(clC.SELL_ROWSET_TYPE) = "2" Then
		//        If doRS.GetFieldVal("dtt_origcreatedtime", 1) = "" Then
		//            doRS.SetFieldVal("dtt_origcreatedtime", "Today|Now")
		//        End If
		//    End If

		//    'CS 6/19/09 Get company info
		//    Dim doRSCO As New clRowSet("CO", 3, "GID_ID='" & doRS.GetFieldVal("LNK_RELATED_CO") & "'", , "TXT_COMPANYNAME,LNK_RELATED_IU")


		//    'Fill the Company Name Text if empty
		//    If doRS.GetFieldVal("TXT_COMPANYNAMETEXT") = "" Then
		//        ' CS 6/19/09 doRS.SetFieldVal("TXT_COMPANYNAMETEXT", doRS.GetFieldVal("LNK_Related_CO%%TXT_COMPANYNAME"))
		//        If doRSCO.Count > 0 Then
		//            doRS.SetFieldVal("TXT_COMPANYNAMETEXT", doRSCO.GetFieldVal("TXT_COMPANYNAME"))
		//        Else
		//            doRS.SetFieldVal("TXT_COMPANYNAMETEXT", "")
		//        End If
		//    End If

		//    'Uppercase the State fields
		//    doRS.SetFieldVal("TXT_STATEBUSINESS", UCase(doRS.GetFieldVal("TXT_STATEBUSINESS")))
		//    doRS.SetFieldVal("TXT_STATEHOME", UCase(doRS.GetFieldVal("TXT_STATEHOME")))
		//    doRS.SetFieldVal("TXT_STATEOTHER", UCase(doRS.GetFieldVal("TXT_STATEOTHER")))

		//    'CS 8/26/08
		//    'Create an email alias record if the CN has an email address and it is not in EL already
		//    'First check if the user has permissions to create ELs. If no permission, skip this
		//    sEmail = Trim(doRS.GetFieldVal("EML_EMAIL"))
		//    If goData.GetAddPermission("EL") = True Then
		//        If sEmail <> "" Then
		//            If goScr.RunScript("Contact_EmailAlias", doRS, , sEmail) = False Then
		//                'Email Alias could not be created; Contact_EmailAlias sets error
		//            End If
		//        End If
		//    End If

		//    'CS 1/13/11
		//    'Create or edit Phone Alias records for the following fields if they contain values: 
		//    'TEL_BusPhone
		//    'TEL_CellPhone
		//    'TEL_HomePhone
		//    'TEL_MainPhone
		//    'TEL_OtherPhone
		//    If goScr.RunScript("Contact_PhoneAlias", doRS, , "CN") = False Then
		//        'warning raised in function
		//    End If



		//    '---------- AUTO-FILLED FIELDS
		//    If goP.GetRunMode() <> "Import" Then

		//        goScr.RunScript("Contact_ConnectVendors", doRS)
		//        goScr.RunScript("Contact_ConnectSource", doRS)
		//        goScr.RunScript("Contact_CalcReviewOverdue", doRS)

		//        If doRS.GetLinkCount("LNK_RELATED_IU") = 0 Then
		//            'Connect Related Industry Code from Related Company
		//            'doRS.SetFieldVal("LNK_RELATED_IU", doRS.GetFieldVal("LNK_RELATED_CO%%LNK_RELATED_IU", 2), 2)
		//            'CS 6/19/09
		//            If doRSCO.Count > 0 Then
		//                doRS.SetFieldVal("LNK_RELATED_IU", doRSCO.GetFieldVal("LNK_RELATED_IU"))
		//            Else
		//                doRS.SetFieldVal("LNK_RELATED_IU", "")
		//            End If
		//        End If

		//        If doRS.GetFieldVal("TXT_TITLETEXT") = "" Then
		//            If doRS.GetLinkCount("LNK_RELATED_JF") = 1 Then
		//                Dim oArray As New clArray
		//                oArray = doRS.GetLinkVal("LNK_RELATED_JF", oArray)
		//                doRS.SetFieldVal("TXT_TITLETEXT", goData.GetFieldValueFromRec(oArray.GetItem(1), "TXT_JOBFUNCNAME"))
		//            End If
		//        End If

		//        'LNK_INVOLVEDIN_ACTIVITY
		//        doLink = New clArray()
		//        'doLink = doRS.GetLinkVal("LNK_CONNECTED_ACTIVITY",doLink,False)
		//        'doLink = doRS.GetLinkVal("LNK_ORIGINALIN_ACTIVITY",doLink,False)
		//        doLink = doRS.GetLinkVal("LNK_CCIN_AC", doLink, False)
		//        doLink = doRS.GetLinkVal("LNK_BCIN_AC", doLink, False)
		//        doRS.SetLinkVal("LNK_INVOLVEDIN_AC", doLink)
		//        doLink = Nothing

		//        'LNK_INVOLVEDIN_OPP
		//        doLink = New clArray()
		//        doLink = doRS.GetLinkVal("LNK_ORIGINATED_OP", doLink, False)
		//        doRS.SetLinkVal("LNK_INVOLVEDIN_OP", doLink)
		//        doLink = Nothing

		//        'LNK_INVOLVEDIN_QUOTE
		//        doLink = New clArray()
		//        doLink = doRS.GetLinkVal("LNK_TOIN_QT", doLink, False)
		//        doLink = doRS.GetLinkVal("LNK_CCIN_QT", doLink, False)
		//        doLink = doRS.GetLinkVal("LNK_BCIN_QT", doLink, False)
		//        doRS.SetLinkVal("LNK_INVOLVEDIN_QT", doLink)
		//        doLink = Nothing
		//    End If

		//    'V_T Email Alerts 9/23/15
		//    SendEmailAlerts(doRS, par_sMode)

		//    Return True

		//End Function
		//Function CN_RecordAfterSave(Optional ByRef par_doCallingObject As Object = vbNull, Optional ByRef par_oReturn As Object = vbNull, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "", Optional ByVal par_doArray As clArray = Nothing, Optional ByVal par_sMode As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "") As Boolean
		//    'V_T 9/14/2015
		//    'Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
		//    'goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)
		//    'If goP.GetRunMode() <> "Import" Then
		//    '    Dim doRS As clRowSet = par_doCallingObject
		//    '    ''Email Alerts
		//    '    Dim _clEmailAlerts As New clEmailAlerts()
		//    '    _clEmailAlerts.SendEmailAlerts(doRS, par_sMode)
		//    'End If

		//    Return True
		//End Function
		//Function CO_RecordAfterSave(Optional ByRef par_doCallingObject As Object = vbNull, Optional ByRef par_oReturn As Object = vbNull, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "", Optional ByVal par_doArray As clArray = Nothing, Optional ByVal par_sMode As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "") As Boolean
		//    'V_T 9/14/2015
		//    'Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
		//    'goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)
		//    'If goP.GetRunMode() <> "Import" Then
		//    '    Dim doRS As clRowSet = par_doCallingObject
		//    '    ''Email Alerts
		//    '    Dim _clEmailAlerts As New clEmailAlerts()
		//    '    _clEmailAlerts.SendEmailAlerts(doRS, par_sMode)
		//    'End If

		//    Return True
		//End Function
		//Function CO_RecordOnSave(Optional ByRef par_doCallingObject As Object = vbNull, Optional ByRef par_oReturn As Object = vbNull, Optional ByRef par_bRunNext As Boolean = True, Optional ByRef par_sSections As String = "", Optional ByVal par_doArray As clArray = Nothing, Optional ByVal par_sMode As String = "", Optional ByVal par_s2 As String = "", Optional ByVal par_s3 As String = "", Optional ByVal par_s4 As String = "", Optional ByVal par_s5 As String = "") As Boolean
		//    'MI 11/8/07 Filling SIC Code from Industry, if linked.
		//    'par_doCallingObject: Rowset object containing the record to be saved.
		//    'par_doArray: Unused.
		//    'par_sMode: 'CREATION' or 'MODIF'.
		//    'par_s2 to par_s5: Unused.
		//    'par_oReturn: Object passed to script ByRef.  Used to return values to calling process
		//    'par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
		//    'par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

		//    Dim sProc As String = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
		//    goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

		//    'goP.TraceLine("", "", sProc)

		//    '--- HISTORY ---
		//    '2005/08/25 09:54:22 MAR Removed filling Involved User with creator. Allows the person who creates
		//    '							a Company not to have that Company in 'Companies - My' view.

		//    Dim doRS As clRowSet
		//    Dim doLink As clArray
		//    Dim sFileName As String = "CO"
		//    Dim sWork As String
		//    Dim sSysName As String = ""
		//    'Dim sNum As String

		//    doRS = par_doCallingObject

		//    'CS added this.
		//    'If Cust code is blank try to generate it.
		//    If goScr.IsSectionEnabled(sProc, par_sSections, "GenerateCustomerCodeIfBlank") Then
		//        If Trim(doRS.GetFieldVal("TXT_CUSTCODE", 1)) = "" Then
		//            goScr.RunScript("Company_FillCustCode", doRS)
		//        End If
		//        'Uppercase the code
		//        doRS.SetFieldVal("TXT_CUSTCODE", UCase(doRS.GetFieldVal("TXT_CUSTCODE")))
		//    End If

		//    'CS 1/13/11
		//    'Create or edit Phone Alias records for the following fields if they contain values: 
		//    'TEL_PhoneNo
		//    'TEL_Phone2       
		//    If goScr.RunScript("Company_PhoneAlias", doRS) = False Then
		//        'warning raised in function
		//    End If

		//    'If goP.GetRunMode() <> "Import" Then
		//    If doRS.bBypassValidation <> True Then

		//        'CS Added
		//        'CS: 7/6/07 In MD
		//        'If doRS.GetFieldVal("txt_companyname", 1) = "" Then
		//        '    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel(sFileName, "TXT_CompanyName"), "", "", "", "", "", "", "", "", "TXT_CompanyName")
		//        '    ' Please fill out the '[1]' field.
		//        '    'goP.TraceLine("Set error 30029, exiting.", "", sProc)
		//        '    Return False
		//        'End If

		//        'If doRS.GetLinkCount("LNK_TEAMLEADER_US") = 0 Then
		//        '    goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel(sFileName, "LNK_TeamLeader_US"), "", "", "", "", "", "", "", "", "LNK_TeamLeader_US")
		//        '    Return False
		//        'End If

		//        'If doRS.GetFieldVal("txt_custcode") = "" Then
		//        '    goErr.SetWarning(30200, sProc, "", "The Customer Code could not be generated automatically. Please enter it manually.", _
		//        '                     "", "", "", "", "", "", "", "", "TXT_CUSTCODE")
		//        '    Return False
		//        'End If

		//        'CS 1/2/09
		//        'Check if review fields exist in db and if so, calculate
		//        If goData.IsFieldValid("CO", "CHK_REVIEW") = True Then
		//            goScr.RunScript("Company_CalcReviewOverdue", doRS)
		//        End If

		//        'Duplicate Company Code
		//        If goScr.IsSectionEnabled(sProc, par_sSections, "ValidateCompanyCode") Then
		//            'goP.TraceLine("Cust Code: '" & doRS.GetFieldVal("TXT_CUSTCODE") & "'", "", sProc)
		//            'goP.TraceLine("Before Company_ValidateName, sDuplCustCodeCount: '" & goP.GetVar("sDuplCustCodeCount") & "'", "", sProc)
		//            goScr.RunScript("Company_ValidateName", , , doRS.GetFieldVal("TXT_CUSTCODE"), doRS.GetFieldVal("GID_ID"))
		//            'goP.TraceLine("After Company_ValidateName, sDuplCustCodeCount: '" & goP.GetVar("sDuplCustCodeCount") & "'", "", sProc)
		//            If goP.GetVar("sDuplCustCodeCount") <> "0" Then
		//                'goP.TraceLine("Setting duplicate code 'error' 30200, exiting.", "", sProc)
		//                sWork = doRS.GetFieldVal("TXT_CUSTCODE")
		//                'Don't give duplicate Cust Code message if the code is blank.
		//                goErr.SetWarning(30200, sProc, "", "Company Code '" & sWork & "' is already in use. Please edit the code to make it unique.", _
		//                             "", "", "", "", "", "", "", "", "TXT_CUSTCODE")
		//                ' [1]
		//                Return False
		//            End If
		//        End If
		//    End If

		//    '------------ AUTO-FILLED FIELDS ------------
		//    If goP.GetRunMode() <> "Import" Then

		//        'Fill Original Creation Date/Time if blank
		//        If goScr.IsSectionEnabled(sProc, par_sSections, "FillOrigCreationDateTime") Then
		//            If doRS.GetInfo(clC.SELL_ROWSET_TYPE) = "2" Then
		//                If doRS.GetFieldVal("dtt_origcreatedtime", 1) = "" Then
		//                    doRS.SetFieldVal("dtt_origcreatedtime", "Today|Now")
		//                End If
		//            End If
		//        End If

		//        '--------- FROM FILL NAME FUNCTION ----------------
		//        If goScr.IsSectionEnabled(sProc, par_sSections, "UppercaseStateFields") Then
		//            sWork = doRS.GetFieldVal("TXT_STATEMAILING")
		//            If sWork <> UCase(sWork) Then doRS.SetFieldVal("TXT_STATEMAILING", UCase(sWork))

		//            sWork = doRS.GetFieldVal("TXT_STATEBILLING")
		//            If sWork <> UCase(sWork) Then doRS.SetFieldVal("TXT_STATEBILLING", UCase(sWork))

		//            sWork = doRS.GetFieldVal("TXT_STATESHIPPING")
		//            If sWork <> UCase(sWork) Then doRS.SetFieldVal("TXT_STATESHIPPING", UCase(sWork))
		//        End If

		//        If goScr.IsSectionEnabled(sProc, par_sSections, "FillSICCodeFromRelatedIndustry") Then
		//            'MI 11/8/07 Modified per PJ for DAF? Filling SIC Code from Industry, if linked.
		//            sWork = doRS.GetFieldVal("LNK_RELATED_IU%%TXT_INDUSTRYCODE")
		//            If sWork <> "" Then
		//                'If doRS.GetFieldVal("TXT_SICCODE") = "" Then
		//                doRS.SetFieldVal("TXT_SICCODE", sWork)
		//            End If
		//        End If

		//        ''Update TXT_CompanyName in all connected Contact records
		//        ''*** Can't do it here because GenerateSysName for the Contact we are updating
		//        ''uses the old Company's Name, not the one that we are saving here. This is coded
		//        ''in FormAfterSave. It should be coded in RecordAfterSave, but that event is not
		//        ''yet available. ***
		//        'If goScr.IsSectionEnabled(sProc, par_sSections, "UpdateCompanyNameInConnContacts") Then
		//        '    Dim doContacts As New clRowSet("CN", clC.SELL_EDIT, "LNK_RELATED_CO='" & doRS.GetFieldVal("GID_ID") & "'", , "TXT_CompanyNameText", , , , , , , True, True)
		//        '    If doContacts.Count() > 0 Then
		//        '        Do
		//        '            sWork = doRS.GetFieldVal("TXT_CompanyName")
		//        '            If doContacts.GetFieldVal("TXT_CompanyNameText") <> sWork Then
		//        '                doContacts.SetFieldVal("TXT_CompanyNameText", sWork)
		//        '                If doContacts.Commit() <> 1 Then
		//        '                    'The user doesn't have Contact edit permission, nothing we can do
		//        '                End If
		//        '            End If
		//        '            If doContacts.GetNext() <> 1 Then Exit Do
		//        '        Loop
		//        '    End If
		//        '    doContacts = Nothing
		//        'End If

		//        'If goScr.IsSectionEnabled(sProc, par_sSections, "FillInvolvesContact") Then
		//        ''LNK_INVOLVES_CONTACT
		//        'doLink = new clArray()
		//        'doLink = doRS.GetLinkVal("LNK_CONNECTED_CN",doLink,False)
		//        'doLink = doRS.GetLinkVal("LNK_ORIGINAL_CN",doLink,False)
		//        'If doLink <> NULL Then
		//        '	doRS.SetLinkVal("LNK_INVOLVES_CN",doLink)
		//        '	delete doLink
		//        'End If
		//        'End If

		//        'LNK_INVOLVES_USER
		//        If goScr.IsSectionEnabled(sProc, par_sSections, "FillInvolvesUser") Then
		//            doLink = New clArray()
		//            doLink = doRS.GetLinkVal("LNK_TEAMLEADER_US", doLink, False)
		//            doRS.SetLinkVal("LNK_INVOLVES_US", doLink)
		//            doLink = Nothing
		//        End If

		//        ''LNK_INVOLVEDIN_ACTIVITY
		//        'If goScr.IsSectionEnabled(sProc, par_sSections, "FillInvolvedInActivity") Then
		//        'doLink = new clArray()
		//        'doLink = doRS.GetLinkVal("LNK_BILLEDFOR_AC",doLink,False)
		//        'doLink = doRS.GetLinkVal("LNK_CONNECTED_AC",doLink,False)
		//        'doLink = doRS.GetLinkVal("LNK_ORIGINALIN_AC",doLink,False)
		//        '	doRS.SetLinkVal("LNK_INVOLVEDIN_AC",doLink)
		//        'End If

		//        'LNK_INVOLVEDIN_QUOTE
		//        If goScr.IsSectionEnabled(sProc, par_sSections, "FillInvolvedInQuote") Then
		//            doLink = New clArray()
		//            doLink = doRS.GetLinkVal("LNK_RECEIVED_QT", doLink, False)
		//            doRS.SetLinkVal("LNK_INVOLVEDIN_QT", doLink)
		//        End If

		//        If goScr.IsSectionEnabled(sProc, par_sSections, "ConnectSource") Then
		//            goScr.RunScript("Company_ConnectSource", doRS)
		//        End If

		//    End If

		//    'V_T Email Alerts 9/23/15
		//    SendEmailAlerts(doRS, par_sMode)

		//    Return True

		//End Function

		public void SendEmailAlerts(clRowSet doRS, string par_sMode)
		{
			if (goP.GetRunMode() != "Import")
			{
				//'Email Alerts
				var sEmailAlerts = goP.GetVar("EmailAlertsSave");
				if (sEmailAlerts != null && sEmailAlerts.ToString() == "ON")
				{
					clEmailAlerts _clEmailAlerts = new clEmailAlerts();
					_clEmailAlerts.SendEmailAlerts(doRS, par_sMode);
					goP.SetVar("EmailAlertsSave ", "OFF");
				}
			}
		}


		public clBaseScripts()
		{

		}
	}

}
