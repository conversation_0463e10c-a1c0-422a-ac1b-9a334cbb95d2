﻿CREATE TABLE [dbo].[NO] (
    [GID_ID]                   UNIQUEIDENTIFIER CONSTRAINT [DF_NO_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'NO',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                   BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                 NVARCHAR (80)    NULL,
    [DTT_CreationTime]         DATETIME         CONSTRAINT [DF_NO_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]             TINYINT          NULL,
    [TXT_ModBy]                VARCHAR (4)      NULL,
    [DTT_ModTime]              DATETIME         CONSTRAINT [DF_NO_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_NinjaOpportunityName] NVARCHAR (50)    NULL,
    [MMO_ImportData]           NTEXT            NULL,
    [SI__ShareState]           TINYINT          CONSTRAINT [DF_NO_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]         UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]             VARCHAR (50)     NULL,
    [TXT_ExternalID]           NVARCHAR (80)    NULL,
    [TXT_ExternalSource]       VARCHAR (10)     NULL,
    [TXT_ImpJobID]             VARCHAR (20)     NULL,
    [TXT_OPPNUMBER]            NVARCHAR (20)    NULL,
    [MLS_STAGE]                SMALLINT         NULL,
    [CUR_VALUE]                MONEY            NULL,
    [DTT_NEXTACTIONDATE]       DATETIME         NULL,
    [MMO_NEXTACTION]           NTEXT            NULL,
    [DTT_CLOSEDATE]            DATETIME         NULL,
    [GID_RELATED_CO]           UNIQUEIDENTIFIER NULL,
    [GID_RELATED_CN]           UNIQUEIDENTIFIER NULL,
    [GID_RELATED_NM]           UNIQUEIDENTIFIER NULL,
    [GID_RELATED_NP]           UNIQUEIDENTIFIER NULL,
    [MMO_WLData]               NTEXT            NULL,
    CONSTRAINT [PK_NO] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_NO_NinjaOpportunityName]
    ON [dbo].[NO]([TXT_NinjaOpportunityName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NO_CreatedBy_US]
    ON [dbo].[NO]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NO_ModDateTime]
    ON [dbo].[NO]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NO_Name]
    ON [dbo].[NO]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NO_CreationTime]
    ON [dbo].[NO]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_NO_BI__ID]
    ON [dbo].[NO]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_NO_TXT_ImportID]
    ON [dbo].[NO]([TXT_ImportID] ASC);

