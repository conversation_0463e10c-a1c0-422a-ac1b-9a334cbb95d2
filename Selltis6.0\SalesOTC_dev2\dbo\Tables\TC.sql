﻿CREATE TABLE [dbo].[TC] (
    [GID_ID]               UNIQUEIDENTIFIER CONSTRAINT [DF_TC_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'TC',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]               BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]             NVARCHAR (80)    NULL,
    [DTT_CreationTime]     DATETIME         CONSTRAINT [DF_TC_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]         TINYINT          NULL,
    [TXT_ModBy]            VARCHAR (4)      NULL,
    [DTT_ModTime]          DATETIME         CONSTRAINT [DF_TC_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_TaskCategoryName] NVARCHAR (50)    NULL,
    [MMO_ImportData]       NTEXT            NULL,
    [SI__ShareState]       TINYINT          CONSTRAINT [DF_TC_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]     UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]         VARCHAR (50)     NULL,
    [TXT_ExternalID]       NVARCHAR (80)    NULL,
    [TXT_ExternalSource]   VARCHAR (10)     NULL,
    [TXT_ImpJobID]         VARCHAR (20)     NULL,
    CONSTRAINT [PK_TC] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_TC_TaskCategoryName]
    ON [dbo].[TC]([TXT_TaskCategoryName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TC_CreatedBy_US]
    ON [dbo].[TC]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TC_ModDateTime]
    ON [dbo].[TC]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TC_Name]
    ON [dbo].[TC]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TC_CreationTime]
    ON [dbo].[TC]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_TC_BI__ID]
    ON [dbo].[TC]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TC_TXT_ImportID]
    ON [dbo].[TC]([TXT_ImportID] ASC);

