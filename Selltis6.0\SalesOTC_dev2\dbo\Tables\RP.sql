﻿CREATE TABLE [dbo].[RP] (
    [GID_ID]                  UNIQUEIDENTIFIER CONSTRAINT [DF_RP_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),(0))+CONVERT([binary](2),'RP',(0)))+CONVERT([binary](6),getutcdate(),(0)),(0))) ROWGUIDCOL NOT NULL,
    [BI__ID]                  BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                NVARCHAR (80)    NULL,
    [DTT_CreationTime]        DATETIME         CONSTRAINT [DF_RP_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]            TINYINT          NULL,
    [TXT_ModBy]               VARCHAR (4)      NULL,
    [DTT_ModTime]             DATETIME         CONSTRAINT [DF_RP_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_RetentionPolicyName] NVARCHAR (50)    NULL,
    [MMO_ImportData]          NTEXT            NULL,
    [SI__ShareState]          TINYINT          CONSTRAINT [DF_RP_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]        UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]            VARCHAR (50)     NULL,
    [TXT_ExternalID]          NVARCHAR (80)    NULL,
    [TXT_ExternalSource]      VARCHAR (10)     NULL,
    [TXT_ImpJobID]            VARCHAR (20)     NULL,
    CONSTRAINT [PK_RP] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_RP_Name]
    ON [dbo].[RP]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RP_RetentionPolicyName]
    ON [dbo].[RP]([TXT_RetentionPolicyName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RP_TXT_ImportID]
    ON [dbo].[RP]([TXT_ImportID] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_RP_BI__ID]
    ON [dbo].[RP]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RP_CreatedBy_US]
    ON [dbo].[RP]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RP_CreationTime]
    ON [dbo].[RP]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_RP_ModDateTime]
    ON [dbo].[RP]([DTT_ModTime] ASC);

