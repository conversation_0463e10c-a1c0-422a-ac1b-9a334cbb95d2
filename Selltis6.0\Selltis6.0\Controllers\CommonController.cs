﻿using Kendo.Mvc.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Selltis.Core;
using System.Data;
using Kendo.Mvc.Extensions;
using Selltis.BusinessLogic;
using System.Text;
using System.Reflection;
using System.Data.SqlClient;
using Microsoft.VisualBasic;
using Selltis.MVC.Models;
using System.Text.RegularExpressions;
using System.IO;
using Telerik.Web.UI.ExportInfrastructure;

namespace Selltis.MVC.Controllers
{
    public class CommonController : Controller
    {
        //
        // GET: /Common/
        private clTransform goTr;
        private clMetaData goMeta;
        private clData goData;
        private clProject goP;
        private clError goErr;
        public JsonResult ReadData([DataSourceRequest] DataSourceRequest request, string ViewKey, bool IsActive, string MasterSelID = "", string RowClick = "no", string Key = "")//string FilterText,,string _ResetValue 
        {
            string TopRecordId = string.Empty;
            SessionViewInfo _SessionViewInfo = new SessionViewInfo();
            string FilterText = string.Empty;

            if (Util.GetSessionValue(Key + "_" + ViewKey) != null)
            {
                _SessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + ViewKey);
            }

            DataTable dt = new DataTable();
            if (_SessionViewInfo.IsActive || IsActive)
            {
                if (!_SessionViewInfo.IsInvalid)
                {
                    string TableName = _SessionViewInfo.TableName;
                    string Top = _SessionViewInfo.PageSize.ToString();

                    //Sort
                    string SortText = _SessionViewInfo.SortText;

                    //  if (_ResetValue != "yes")
                    //  {
                    if (request.Sorts != null && request.Sorts.Count > 0)
                    {
                        string sSortDir = request.Sorts[0].SortDirection.ToString().ToLower() == "ascending" ? "ASC" : "DESC";
                        SortText = request.Sorts[0].Member.Replace("___", ", ").Replace("__", "%%") + " " + sSortDir;
                    }
                    // Session["SortText"] = SortText;
                    //}
                    //else
                    //{
                    //    request.Sorts = null;
                    //    SortText = Util.GetPropertyValueFromMetaData(_SessionViewInfo.ViewMetaData, "SORT");
                    //}

                    //Get Data

                    //Session["CurrentPageNo"] = request.Page;
                    Util.SetSessionValue("CurrentPageNo", request.Page);

                    dt = Util.ReadData(ViewKey, MasterSelID, SortText, request.Page, RowClick);

                    if (goTr == null)
                    {
                        goTr = (clTransform)Util.GetInstance("tr");
                    }

                    string _Onelineperrecord = goTr.StrRead(_SessionViewInfo.ViewMetaData, "LIST_ONELINEPERRECORD");  //Util.GetPropertyValueFromMetaData(_SessionViewInfo.ViewMetaData, "LIST_ONELINEPERRECORD");

                    if (_Onelineperrecord == "1")
                    {
                        FormatGridRows(dt);
                    }

                    request.Page = 1;
                    request.PageSize = _SessionViewInfo.PageSize;
                    _SessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + ViewKey);
                }
            }
            var result = dt.ToDataSourceResult(request);
            result.Total = int.Parse(_SessionViewInfo.ViewDataCount.ToString());
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        public void FormatGridRows(DataTable dt)
        {
            string[] columnNames = dt.Columns.Cast<DataColumn>()
                                 .Select(x => x.ColumnName)
                                 .ToArray();
            int j = 0;
            foreach (string ColName in columnNames)
            {
                if (ColName.Contains("ADV_"))
                {
                    for (int i = 0; i <= dt.Rows.Count - 1; i++)
                    {

                        dt.Rows[i][j] = dt.Rows[i][j].ToString().Replace("<BR>", "; ");
                    }
                }

                j++;
            }
        }


        public string GetMLS_Control_As_String(string TableName, string FieldName, string selectedValue, int width, string ViewId, string Key, string Title)
        {
            clList oMLSList = new clList();
            clArray aList = default(clArray);
            aList = oMLSList.GetList(TableName, FieldName);
            StringBuilder sboptions = new StringBuilder();
            if (width > 30)
            {
                width = width - 10;
            }

            SessionViewInfo _SessionViewInfo = new SessionViewInfo();
            if (Util.GetSessionValue(Key + "_" + ViewId.Replace(" ", "")) != null)
            {
                _SessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + ViewId.Replace(" ", ""));
            }

            if (goTr == null)
            {
                goTr = (clTransform)Util.GetInstance("tr");
            }

            string _EnableBulkSave = goTr.StrRead(_SessionViewInfo.ViewMetaData, "ENABLEBULKSAVE", "false");
            if (_EnableBulkSave == "1")
            {
                _EnableBulkSave = "true";
            }
            else
            {
                _EnableBulkSave = "false";
            }
            long x = 0;
            for (x = 1; x <= aList.GetDimension(); x++)
            {
                string sItemText = aList.GetItem(x);
                string sItemTextToCompare = sItemText.Replace("<", "").Replace(">", "");
                string ReplacesField = FieldName.Replace("MLS_", "");
                string sItemValue = oMLSList.LReadSeek(TableName + ":" + ReplacesField, "VALUE", sItemText);

                if (sItemTextToCompare == selectedValue)
                {
                    if (sItemText.ToLower() == "<make selection>")
                    {
                        sItemText = sItemText.Replace("<", "-- ").Replace(">", " --");
                    }
                    sboptions.AppendLine("<option id='" + sItemValue + "' selected value='" + sItemValue + "'>" + sItemText + "</option>");
                }
                else
                {
                    if (sItemText.ToLower() == "<make selection>")
                    {
                        sItemText = sItemText.Replace("<", "-- ").Replace(">", " --");
                    }
                    sboptions.AppendLine("<option id='" + sItemValue + "' value='" + sItemValue + "'>" + sItemText + "</option>");
                }

            }

            string sMLSctrl =
            "<select id='" + FieldName + "' onchange=\"GetSelectedVal(this," + "'" + ViewId.Replace(" ", "") + "'," + "'" + TableName + "'," + "'" + FieldName + "'," + "'" + _EnableBulkSave.ToLower() + "'," + "'" + Title + "'" + "," + "'" + _SessionViewInfo.ViewType + "'" + ")\" style=\"Width:90%;font-size:11px !important;box-sizing:border-box;height: 23px;\">" +
            sboptions.ToString() +
            "</select>";

            return sMLSctrl;

        }

        public string GetMLS_Control_As_StringR(string TableName, string FieldName, string selectedValue, int width, string ViewId, string Key, string Title)
        {
            clList oMLSList = new clList();
            clArray aList = default(clArray);
            aList = oMLSList.GetList(TableName, FieldName);
            StringBuilder sboptions = new StringBuilder();
            if (width > 30)
            {
                width = width - 10;
            }
            SessionViewInfo _SessionViewInfo = new SessionViewInfo();
            if (Util.GetSessionValue(Key + "_" + ViewId.Replace(" ", "")) != null)
            {
                _SessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + ViewId.Replace(" ", ""));
            }

            if (goTr == null)
            {
                goTr = (clTransform)Util.GetInstance("tr");
            }

            string _EnableBulkSave = goTr.StrRead(_SessionViewInfo.ViewMetaData, "ENABLEBULKSAVE", "false");

            if (_EnableBulkSave == "1")
            {
                _EnableBulkSave = "true";
            }
            else
            {
                _EnableBulkSave = "false";
            }

            long x = 0;
            for (x = 1; x <= aList.GetDimension(); x++)
            {
                string sItemText = aList.GetItem(x);
                string sItemTextToCompare = sItemText.Replace("<", "").Replace(">", "");
                string ReplacesField = FieldName.Replace("MLS_", "");
                string sItemValue = oMLSList.LReadSeek(TableName + ":" + ReplacesField, "VALUE", sItemText);

                if (sItemTextToCompare == selectedValue)
                {
                    if (sItemText.ToLower() == "<make selection>")
                    {
                        sItemText = sItemText.Replace("<", "-- ").Replace(">", " --");
                    }
                    sboptions.AppendLine("<option id='" + sItemValue + "' selected value='" + sItemValue + "'>" + sItemText + "</option>");
                }
                else
                {
                    if (sItemText.ToLower() == "<make selection>")
                    {
                        sItemText = sItemText.Replace("<", "-- ").Replace(">", " --");
                    }
                    sboptions.AppendLine("<option id='" + sItemValue + "' value='" + sItemValue + "'>" + sItemText + "</option>");
                }

            }

            string sMLSctrl =
            "<select id='" + FieldName + "' onchange=\"GetSelectedValR(this," + "'" + ViewId + "'," + "'" + TableName + "'," + "'" + FieldName + "'," + "'" + _EnableBulkSave.ToLower() + "'," + "'" + Title + "'" + ")\" style=\"Width: " + width.ToString() + "px;font-size:11px !important;box-sizing:border-box;height: 23px;\">" +
            sboptions.ToString() +
            "</select>";

            return sMLSctrl;

        }

        public JsonResult GetSessionData(string ViewKey, string Key)
        {
            SessionViewInfo _SessionViewInfo = Util.SessionViewInfo(Key + "_" + ViewKey);
            GetViewSessionDataDetails _getViewSessionDataDetails = new GetViewSessionDataDetails()
            {
                IsSorted = Convert.ToString(_SessionViewInfo.IsSorted),
                LastSelectedRowIndex = Convert.ToString(_SessionViewInfo.LastSelectedRowIndex),
                LastSelectedRowGID_ID = _SessionViewInfo.LastSelectedRecord,
                LastPageNumber = _SessionViewInfo.LastPageNumber,
                VisibleRecordCount = _SessionViewInfo.ViewDataCount,
                ViewId = ViewKey,
                GridNavigationButtonCssHasFirst = _SessionViewInfo.HasFirst,
                GridNavigationButtonCssHasLast = _SessionViewInfo.HasLast,
                GridNavigationButtonCssHasNext = _SessionViewInfo.HasNext,
                GridNavigationButtonCssHasPrevious = _SessionViewInfo.HasPrevious
            };


            //bool IsSorted = _SessionViewInfo.IsSorted;
            //int LastSelectedRowIndex = _SessionViewInfo.LastSelectedRowIndex;
            //string LastSelectedRowGID_ID = _SessionViewInfo.LastSelectedRecord;
            //string sReturnVal = IsSorted.ToString() + "|" + LastSelectedRowIndex.ToString() + "|" + LastSelectedRowGID_ID + "|" + _SessionViewInfo.LastPageNumber;
            return Json(_getViewSessionDataDetails, JsonRequestBehavior.AllowGet);
        }

        public JsonResult GetColumnWidthFromCookie(string cookieName, string Key)
        {
            goTr = (clTransform)Util.GetInstance("tr");
            string sCookie;
            if (System.Web.HttpContext.Current.Request.Cookies[cookieName] != null)
            {
                sCookie = System.Web.HttpContext.Current.Request.Cookies[cookieName].Value.ToString();

                if (!string.IsNullOrEmpty(sCookie))
                {
                    var aCookie = sCookie.Split(',');
                    foreach (var myCookie in aCookie)
                    {
                        string sColDef = myCookie;
                        if (!string.IsNullOrEmpty(sColDef))
                        {
                            string[] aCol = sColDef.Split('|');
                            string sCol = aCol[0];
                            string sWidth = aCol[1];

                            string ViewKey = cookieName.Replace("_col", "");
                            SessionViewInfo _SessionViewInfo = new SessionViewInfo();
                            if (Util.GetSessionValue(Key + "_" + ViewKey) != null)
                            {
                                _SessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + ViewKey);
                            }
                            string[] fields = _SessionViewInfo.Fields.Replace("%%", "__").Split(',');
                            int i = 1;
                            foreach (var item in fields)
                            {
                                if (item.Trim() == sCol)
                                {
                                    sCol = i.ToString();
                                    break;
                                }
                                i++;
                            }

                            int iCol = Convert.ToInt32(sCol);
                            int iWidth = Convert.ToInt32(Convert.ToInt32(sWidth) / 6);

                            //let's determine if this is a chart view, and if so, if this is a y axis total or a regular column
                            string sMD = _SessionViewInfo.ViewMetaData;
                            string sType = goTr.StrRead(sMD, "TYPE", "NONE", false);
                            //written only for list, refer legacy
                            goTr.StrWrite(ref sMD, "COL" + iCol + "WIDTH", iWidth);
                            //oDT.SetViewMetadata(sView, _view.ViewType);
                            _SessionViewInfo.ViewMetaData = sMD;
                        }
                    }

                    HttpCookie c1 = System.Web.HttpContext.Current.Request.Cookies[cookieName];
                    System.Web.HttpContext.Current.Request.Cookies.Remove(cookieName);
                    c1.Expires = DateTime.Now.AddDays(-10);
                    c1.Value = null;

                    System.Web.HttpContext.Current.Response.SetCookie(c1);
                }
            }
            return Json(true, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public ActionResult Export_Save(string contentType, string base64, string fileName)
        {
            var fileContents = Convert.FromBase64String(base64);
            return File(fileContents, contentType, fileName);
        }

        public ContentResult RunScript(string Args, string FormKey)
        {
            ScriptManager _scriptManager = new ScriptManager();
            Models.clCreateForm Model = new Models.clCreateForm();

            if (Util.GetSessionValue(FormKey) != null)
            {
                Model = (Models.clCreateForm)Util.GetSessionValue(FormKey);
            }

            //if (Session[FormKey] != null)
            //{
            //    Model = (Models.clCreateForm)Session[FormKey];
            //}
            ////var Model = (Selltis.MVC.Models.clCreateForm)Session["clCreateForm"];

            object par_doCallingObject = Model.gsForm;
            object par_oReturn = null;
            bool par_bRunNext = true;
            string par_sSections = "";
            Model.gsForm.MBMessage = "";
            var retval = _scriptManager.RunScript(Args, ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
            Model.gsForm = (Form)par_doCallingObject;
            if (Model.gsForm.MessageBoxDisplay == true)
            {
                return Content(Model.gsForm.MessageBoxMessage);
            }
            return Content(Args);
        }

        // CRL Start
        public HtmlString GetCreateLinkData()
        {
            //if (Session["sCLMenu"] != null)
            //{
            //    return new HtmlString(Session["sCLMenu"].ToString());
            //}

            if (Util.GetSessionValue("sCLMenu") != null)
            {
                return new HtmlString(Util.GetSessionValue("sCLMenu").ToString());
            }

            goTr = (clTransform)Util.GetInstance("tr");
            goMeta = (clMetaData)Util.GetInstance("meta");
            goData = (clData)Util.GetInstance("data");
            StringBuilder menu = new StringBuilder();
            string gsType = "CRL";
            string[] aCRLExclude;
            string[] aCRUExclude;
            string sFile = "";
            string sLabel = "";
            string sPage = goMeta.PageRead("GLOBAL", "OTH_CREATEMENUS", "", true);
            aCRLExclude = goTr.StrRead(sPage, "CRLEXCLUDE", "", false).Split(',');
            aCRUExclude = goTr.StrRead(sPage, "CRUEXCLUDE", "", false).Split(',');
            int n = 4;

            for (int i = 1; i <= goTr.StringToNum(goTr.StrRead(sPage, "MCOUNT", "0", false), "0", ref n, ""); i++)
            {
                string sPos = "M" + goTr.Pad(i.ToString(), 3, "0", "L");
                string sTarget = goTr.StrRead(sPage, sPos, "", false);
                if (gsType.ToUpper() == "CRU")
                {
                    if (Array.IndexOf(aCRUExclude, sPos) >= 0)
                        continue;
                }
                else
                {
                    if (Array.IndexOf(aCRLExclude, sPos) >= 0)
                        continue;
                }
                if (string.IsNullOrEmpty(sTarget))
                    continue;
                if (sTarget.ToUpper().Contains("SM_"))
                {
                    menu.Append("<li><a class=\"accordion-toggle\"><span class=\"sidebar-title\">" + goTr.StrRead(sPage, sPos + "LABEL") + "</span><span class=\"caret\"></span></a><ul>");
                    menu.Append(GetCSSSubMenu(sPage, i, gsType));
                    menu.Append("</ul></li>");
                }
                else
                {
                    sFile = sTarget.ToUpper().Substring(0, 2);
                    sLabel = goTr.StrRead(sPage, sPos + "LABEL");
                    if (string.IsNullOrEmpty(sLabel))
                        sLabel = goData.GetFileLabelFromName(sFile);
                    menu.Append("<li title=\'" + sLabel + "\'><a onclick=\"return CreateLink(\'" + sFile + "\',\'" + (gsType.ToUpper()) + "_" + sTarget.Replace(":", "||") + "\')\" href=\"#\">" + sLabel + "</a></li>");
                }

            }
            HtmlString menubar = new HtmlString(menu.ToString());

            //if (Session["sCLMenu"] == null)
            //{
            //    Session["sCLMenu"] = menu.ToString();
            //}
            if (Util.GetSessionValue("sCLMenu") == null)
            {
                Util.SetSessionValue("sCLMenu", menu.ToString());
            }
            return menubar;
        }
        private string GetCSSSubMenu(string sPage, int iMenuPos, string gsType)
        {
            int refint = 4;
            int i = 0;
            string sMainMenuPos = null;
            string sPos = null;
            string sTarget = null;
            string sFile = null;
            string[] aCRLExclude = null;
            string[] aCRUExclude = null;
            string sLabel = null;
            sMainMenuPos = "SM" + goTr.Pad(iMenuPos.ToString(), 3, "0", "L");
            aCRLExclude = goTr.StrRead(sPage, "CRLEXCLUDE", "", false).Split(',');
            aCRUExclude = goTr.StrRead(sPage, "CRUEXCLUDE", "", false).Split(',');
            StringBuilder menu = new StringBuilder();
            for (i = 1; i <= goTr.StringToNum(goTr.StrRead(sPage, sMainMenuPos + "COUNT", "0", false), "0", ref refint, ""); i++)
            {
                sPos = goTr.Pad(i.ToString(), 3, "0", "L");
                sTarget = goTr.StrRead(sPage, sMainMenuPos + "_" + sPos, "", false);
                if (gsType.ToUpper() == "CRL")
                {
                    if (Array.IndexOf(aCRLExclude, sMainMenuPos + "_" + sPos) >= 0)
                        continue;
                }
                else
                {
                    if (Array.IndexOf(aCRUExclude, sMainMenuPos + "_" + sPos) >= 0)
                        continue;
                }
                if (string.IsNullOrEmpty(sTarget))
                    continue;

                sFile = sTarget.ToUpper().Substring(0, 2);
                sLabel = goTr.StrRead(sPage, sMainMenuPos + "_" + sPos + "LABEL");
                if (string.IsNullOrEmpty(sLabel))
                    sLabel = goData.GetFileLabelFromName(sFile);
                if (gsType == "CRU")
                {
                    menu.Append("<li title=" + sLabel + "><a href=\"/CreateForm/CreateForm/" + sFile + "/ID/TYPE/false/false/KEY/false/MASTERID/FORMKEY/FIELD\">" + sLabel + "</a></li>");
                }
                else
                {
                    menu.Append("<li title=" + sLabel + "><a onclick=\"return CreateLink(\'" + sFile + "\',\'" + (gsType.ToUpper()) + "_" + sTarget.Replace(":", "||") + "\')\" href=\"#\">" + sLabel + "</a></li>");
                }
            }
            return menu.ToString();
        }
        // CRL End
        public string GetFileFromLinkName(string LinkName)
        {
            goTr = (clTransform)Util.GetInstance("tr");
            string File = goTr.GetFileFromLinkName(LinkName);
            return ClearExistingSession((File));
        }
        public string ClearExistingSession(string File)
        {
            //Session[File + "-1111-1111-1111-CRU_"] = null;
            Util.ClearSessionValue(File + "-1111-1111-1111-CRU_");
            return File;
        }
        public string GetLastPageNumber(string ViewKey, string NavigationValue, string CurrentPageNumber, string Key)
        {
            Util.SetSessionValue(Key + "_" + "IsFromCreateForm", false);

            SessionViewInfo _SessionViewInfo = new SessionViewInfo();
            string FilterText = string.Empty;

            var strClientViewIds = "";

            var PreviousPageNumber = "";

            if (Util.GetSessionValue(Key + "_" + ViewKey) != null)
            {
                _SessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + ViewKey);
                _SessionViewInfo.BrowseInstruct = NavigationValue;
                _SessionViewInfo.LastSelectedRowIndex = 0;
                PreviousPageNumber = _SessionViewInfo.PreviousPageno.ToString();
                if (NavigationValue == "next")
                {
                    //_SessionViewInfo.CurrentPageNumber = Convert.ToInt32(CurrentPageNumber) + 1;
                    _SessionViewInfo.CurrentPageNumber = Convert.ToInt32(PreviousPageNumber) + 1;
                }
                else if (NavigationValue == "previous")
                {
                    //_SessionViewInfo.CurrentPageNumber = Convert.ToInt32(CurrentPageNumber) - 1;
                    _SessionViewInfo.CurrentPageNumber = Convert.ToInt32(PreviousPageNumber) - 1;
                }
                else if (NavigationValue == "last")
                {
                    _SessionViewInfo.CurrentPageNumber = _SessionViewInfo.LastPageNumber;
                }
                else if (NavigationValue == "first")
                {
                    _SessionViewInfo.CurrentPageNumber = 1;
                }

                if (_SessionViewInfo.CurrentPageNumber == 0)
                {
                    _SessionViewInfo.CurrentPageNumber = 1;
                }

                //Make current row details to first after performing sort/navigation need to clear lst selected rocord details and make it to first record..J
                _SessionViewInfo.LastSelectedRowIndex = 0;
                _SessionViewInfo.LastSelectedRecord = null;

                //tckt #2134 Set navigation buttons CSS..J
                bool Disable_QuickFilter_On_MV = Util.Get_WG_Options_Reset_QuickFilter_Persitence(goTr);
                if (Disable_QuickFilter_On_MV)
                {
                    goTr = (clTransform)Util.GetInstance("tr");
                    string viewMetaDataForNav = _SessionViewInfo.ViewMetaData;
                    goTr.StrWrite(ref viewMetaDataForNav, "BROWSED", "1");
                    goTr.StrWrite(ref viewMetaDataForNav, "BROWSEFIRSTON", _SessionViewInfo.HasFirst);
                    goTr.StrWrite(ref viewMetaDataForNav, "BROWSELASTON", _SessionViewInfo.HasLast);
                    _SessionViewInfo.ViewMetaData = viewMetaDataForNav;
                    Util.LoadViewData(ViewKey, _SessionViewInfo.SortText, _SessionViewInfo.CurrentPageNumber, "", false, Key);
                }
                else
                {
                    string SortText = _SessionViewInfo.SortText;
                    string Fields = _SessionViewInfo.Fields;
                    string ViewsTop = _SessionViewInfo.PageSize.ToString();
                    string TopRec = _SessionViewInfo.Toprec;
                    string BottomRec = _SessionViewInfo.Bottomrec;
                    int Linkstop = _SessionViewInfo.LinksTop;
                    string File = _SessionViewInfo.TableName;

                    Util.LoadViewDataForNavigation(_SessionViewInfo, File, FilterText, ViewKey, SortText, Fields, ViewsTop, "", "", "", NavigationValue, TopRec, BottomRec, false, Linkstop, Key);
                }


                //if (_SessionViewInfo.IsParent)
                //{
                //    //update all the child views data sets
                //    Util.LoadViewDataSets(_SessionViewInfo.TopViewCount, _SessionViewInfo.TabViewCount, ViewKey, false, Key);
                //}

                //JK 02-05-2018 Tkt#2092 Navigation doouble click loading issue
                var clientViewIds = Util.SetSelectedRecord_GetClientViewIds(ViewKey, "", _SessionViewInfo.TableName, Key);


                //Load views which are loaded with fake ids irrespective of evaluation order..J
                Util.LoadViewDataSetsWithFakeIds(Key);

                //var clientViewIds = Util.GetClientViewIds(Key);
                if (clientViewIds != null)
                {
                    strClientViewIds = clientViewIds.ToString();
                }
            }

            //var PreviousPageNumber = Util.GetSessionValue(Key + "_" + "PreviousPageNumber");
            Util.SetSessionValue(Key + "_" + "PreviousPageTopRec", _SessionViewInfo.Toprec);
            Util.SetSessionValue(Key + "_" + "PreviousPageBottomrec", _SessionViewInfo.Bottomrec);
            Util.SetSessionValue(Key + "_" + "LastSelectedRowIndex", _SessionViewInfo.LastSelectedRowIndex);

            return _SessionViewInfo.LastPageNumber.ToString() + "|" + PreviousPageNumber.ToString() + "|" + strClientViewIds;
        }

        public ActionResult GetAlertsCount()
        {
            goData = (clData)Util.GetInstance("data");
            goTr = (clTransform)Util.GetInstance("tr");
            goP = (clProject)Util.GetInstance("p");
            goMeta = (clMetaData)Util.GetInstance("meta");
            IList<AlertsModel> _AlertsModel = new List<AlertsModel>();
            Collection UniqueAlerts = new Collection();

            if (goP == null)
            {
                return Json("Logout", JsonRequestBehavior.AllowGet);
            }
            Collection DuplicateAlerts2 = new Collection();
            SqlConnection oConnection = goData.GetConnection();
            SqlCommand oCommand = new SqlCommand();
            SqlDataReader oReader = default(SqlDataReader);

            oCommand.CommandText = "pMetaGetAlerts";
            oCommand.CommandType = System.Data.CommandType.StoredProcedure;
            oCommand.Connection = oConnection;

            //parameter 
            SqlParameter strSection = new SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier);
            strSection.Value = goTr.StringToGuid(goP.GetUserTID());
            oCommand.Parameters.Add(strSection);

            //return parameter
            SqlParameter retValParam = new SqlParameter("@RETURN_VALUE", SqlDbType.Int);
            retValParam.Direction = ParameterDirection.ReturnValue;
            oCommand.Parameters.Add(retValParam);

            //execute            
            oReader = oCommand.ExecuteReader();
            if (oReader.HasRows)
            {
                while (oReader.Read())
                {
                    string sExecute = Strings.Trim(oReader["EXECUTE"].ToString());
                    string sMessage = Strings.Trim(oReader["MESSAGE"].ToString());
                    string sName = Strings.Trim(oReader["NAME"].ToString());
                    string sType = Strings.Trim(oReader["TYPE"].ToString());
                    string sIcon = Strings.Trim(oReader["ICON"].ToString());
                    string sSection = Strings.Trim(oReader["SECTION"].ToString());
                    string sPage = Strings.Trim(oReader["PAGE"].ToString());
                    string sTooltip = Strings.Trim(oReader["TOOLTIP"].ToString());

                    string sKey = sType + "#" + sMessage + "#" + sExecute;
                    if (string.IsNullOrEmpty(sIcon) || string.IsNullOrEmpty(sExecute) || sMessage.Contains("Service Unavailable") || sMessage.Contains("SoapHttpClientProtocol") || sMessage.Contains("Google Sync Error") || sMessage.Contains("HTTP status 503"))
                    {

                    }
                    else if (!UniqueAlerts.Contains(sKey))
                    {


                        _AlertsModel.Add(new AlertsModel
                        {
                            Execute = sExecute,
                            Message = sMessage,
                            Name = sName,
                            Type = sType,
                            Icon = sIcon,
                            Section = sSection,
                            Page = sPage,
                            Tooltip = sTooltip
                        });
                    }
                    else
                    {

                    }
                }
            }

            AlertsData _model = new AlertsData();

            _model.Count = _AlertsModel.Count;

            return Json(_model, JsonRequestBehavior.AllowGet);

        }

        public ActionResult CheckAlerts(string Event = "", string Args = "", string Refresh = "NO")
        {

            //Load site settings from XML..J

            //if (!Util.LoadSiteSettings())
            //{
            //    ViewBag.Error = "Site settings for the site " + System.Web.HttpContext.Current.Request.Url.Host + " is not found";
            //    return View("Login");
            //} 

            //clInit Init = new clInit();        
            goData = (clData)Util.GetInstance("data");
            goTr = (clTransform)Util.GetInstance("tr");
            goP = (clProject)Util.GetInstance("p");
            goMeta = (clMetaData)Util.GetInstance("meta");
            IList<AlertsModel> _AlertsModel = new List<AlertsModel>();
            Collection UniqueAlerts = new Collection();
            Collection DuplicateAlerts = new Collection();
            Collection LastDownload = new Collection();
            bool bSomethingNew = false;

            if (Refresh == "YES")
            {
                bSomethingNew = true;
                //do nothing, we don't want to compare to last downloaded values, this is a refresh
            }
            else
            {
                if (goP == null)
                {
                    //clInit Init = new clInit();
                    //goData = (clData)Util.GetInstance("data");
                    //goTr = (clTransform)Util.GetInstance("tr");
                    //goP = (clProject)Util.GetInstance("p");
                    //goMeta = (clMetaData)Util.GetInstance("meta");

                    //AlertsData _emptyModel = new AlertsData();
                    //_emptyModel.AlertsHTML = "";
                    //return Json(_emptyModel, JsonRequestBehavior.AllowGet);

                    //return RedirectToAction("Logout", "Login");

                    return Json("Logout", JsonRequestBehavior.AllowGet);
                }
                object oTest = goP.GetVar("ALT_cLastDownload");
                if (oTest.GetType().ToString() == "Microsoft.VisualBasic.Collection")
                    LastDownload = (Collection)goP.GetVar("ALT_cLastDownload");
            }
            int i = 0;
            int j = 0;
            string sMetaInfo = null;
            string[] aMetaInfo = null;
            switch (Event.ToUpper())
            {
                case "DELETE":
                    DuplicateAlerts = (Collection)goP.GetVar("ALT_cDuplicateAlerts");
                    string sKey = Args;
                    try
                    {
                        sMetaInfo = DuplicateAlerts[sKey].ToString();
                        aMetaInfo = Strings.Split(sMetaInfo, Constants.vbCrLf);
                        for (i = 0; i <= aMetaInfo.Length - 1; i++)
                        {
                            Args = aMetaInfo[i];
                            string sSection = goTr.ExtractString(Args, 1, "|");
                            string sPage = goTr.ExtractString(Args, 2, "|");
                            bool bReturn = goMeta.PageDelete(sSection, sPage);
                            bReturn = goMeta.PageDelete(sSection, sPage, "XX");
                        }
                        bSomethingNew = true;
                    }
                    catch (Exception ex)
                    {
                        int n = 0;
                        for (n = 1; n <= DuplicateAlerts.Count; n++)
                        {
                            sMetaInfo = DuplicateAlerts[n].ToString();
                            if (Strings.InStr(sMetaInfo, sKey) > 0)
                            {
                                aMetaInfo = Strings.Split(sMetaInfo, Constants.vbCrLf);
                                for (i = 0; i <= aMetaInfo.Length - 1; i++)
                                {
                                    Args = aMetaInfo[i];
                                    string sSection = goTr.ExtractString(Args, 1, "|");
                                    string sPage = goTr.ExtractString(Args, 2, "|");
                                    bool bReturn = goMeta.PageDelete(sSection, sPage);
                                    bReturn = goMeta.PageDelete(sSection, sPage, "XX");
                                }
                                bSomethingNew = true;
                                break;
                            }
                        }
                    }

                    break;
                case "DELETEALL":
                    DuplicateAlerts = (Collection)goP.GetVar("ALT_cDuplicateAlerts");

                    goP.DeleteVar("ALT_cDuplicateAlerts");
                    for (i = 1; i <= DuplicateAlerts.Count; i++)
                    {
                        sMetaInfo = DuplicateAlerts[i].ToString();
                        aMetaInfo = Strings.Split(sMetaInfo, Constants.vbCrLf);
                        for (j = 0; j <= aMetaInfo.Length - 1; j++)
                        {
                            Args = aMetaInfo[j];
                            string sSection = goTr.ExtractString(Args, 1, "|");
                            string sPage = goTr.ExtractString(Args, 2, "|");
                            bool bReturn = goMeta.PageDelete(sSection, sPage);
                            bReturn = goMeta.PageDelete(sSection, sPage, "XX");
                        }
                    }
                    bSomethingNew = true;

                    break;
                default:
                    goP.DeleteVar("ALT_cDuplicateAlerts");
                    break;
            }
            Collection DuplicateAlerts2 = new Collection();
            SqlConnection oConnection = goData.GetConnection();
            SqlCommand oCommand = new SqlCommand();
            SqlDataReader oReader = default(SqlDataReader);

            oCommand.CommandText = "pMetaGetAlerts";
            oCommand.CommandType = System.Data.CommandType.StoredProcedure;
            oCommand.Connection = oConnection;

            //parameter 
            SqlParameter strSection = new SqlParameter("@par_uSection", SqlDbType.UniqueIdentifier);
            strSection.Value = goTr.StringToGuid(goP.GetUserTID());
            oCommand.Parameters.Add(strSection);

            //return parameter
            SqlParameter retValParam = new SqlParameter("@RETURN_VALUE", SqlDbType.Int);
            retValParam.Direction = ParameterDirection.ReturnValue;
            oCommand.Parameters.Add(retValParam);

            //execute            
            oReader = oCommand.ExecuteReader();
            if (oReader.HasRows)
            {
                while (oReader.Read())
                {
                    string sExecute = Strings.Trim(oReader["EXECUTE"].ToString());
                    string sMessage = Strings.Trim(oReader["MESSAGE"].ToString());
                    string sName = Strings.Trim(oReader["NAME"].ToString());
                    string sType = Strings.Trim(oReader["TYPE"].ToString());
                    string sIcon = Strings.Trim(oReader["ICON"].ToString());
                    string sSection = Strings.Trim(oReader["SECTION"].ToString());
                    string sPage = Strings.Trim(oReader["PAGE"].ToString());
                    string sTooltip = Strings.Trim(oReader["TOOLTIP"].ToString());

                    string sKey = sType + "#" + sMessage + "#" + sExecute;
                    //SB 04-18-2019 If Icon empty from alert adding default icon to show alerts
                    sIcon = string.IsNullOrEmpty(sIcon) ? "PermDef.gif" : sIcon;
                    if (string.IsNullOrEmpty(sIcon) || string.IsNullOrEmpty(sExecute) || sMessage.Contains("Service Unavailable") || sMessage.Contains("SoapHttpClientProtocol") || sMessage.Contains("Google Sync Error") || sMessage.Contains("HTTP status 503"))
                    {

                    }
                    else if (!UniqueAlerts.Contains(sKey))
                    {
                        if (!LastDownload.Contains(sKey))
                            bSomethingNew = true;

                        //add to alerts collection
                        string sValue = sSection + "|" + sPage;
                        UniqueAlerts.Add(sPage, sKey);
                        DuplicateAlerts2.Add(sValue, sPage);

                        _AlertsModel.Add(new AlertsModel
                        {
                            Execute = sExecute,
                            Message = sMessage,
                            Name = sName,
                            Type = sType,
                            Icon = sIcon,
                            Section = sSection,
                            Page = sPage,
                            Tooltip = sTooltip
                        });
                    }
                    else
                    {
                        string PageKey = UniqueAlerts[sKey].ToString();
                        string Value = DuplicateAlerts2[PageKey].ToString();
                        Value = Value + Constants.vbCrLf + sSection + "|" + sPage;
                        DuplicateAlerts2.Remove(PageKey);
                        DuplicateAlerts2.Add(Value, PageKey);
                    }
                }
            }
            if (!bSomethingNew)
            {
                if (LastDownload.Count != UniqueAlerts.Count)
                    bSomethingNew = true;
            }
            goP.SetVar("ALT_cLastDownload", UniqueAlerts);
            goP.SetVar("ALT_cDuplicateAlerts", DuplicateAlerts2);
            oReader.Close();
            oConnection.Close();

            StringBuilder AlertsHTML = new StringBuilder();
            int index = 0;
            string marginTopOnTextLength = "";
            int AlertCountVal = 2;
            List<string> _lstAlerts = new List<string>();
            List<string> _lstTitles = new List<string>();
            foreach (AlertsModel _alert in _AlertsModel)
            {

                string ButtonIconsForAlert = "<button class=\"btnShowAlwaysNotif\" title=\"Clear\" style=\"top:0;margin-left: 270px;\" onclick=\"ClearNotification(this, 'Alert');\"><i class=\"fa fa-trash\" aria-hidden=\"true\"></i></button>" +
                   "<button class=\"btnShowAlwaysNotif\" title=\"Open\" style=\"top:0;margin-left: 250px;\" onclick=\"OpenNotification(this, 'Alert');\"><i class=\"fa fa-folder-open-o\" aria-hidden=\"true\"></i></button>";

                marginTopOnTextLength = (_alert.Message.Length >= 42) ? "margin-top: -4px;" : "margin-top: 3px;";
                if (_alert.Type.ToUpper() == "OPENRECORD")
                {
                    string _file = goTr.GetFileFromSUID(_alert.Execute);

                    //Alert Box messsage list
                    AlertsHTML.Append("<li title='" + _alert.Tooltip + "' class=\"timeline-item\"><div>" + ButtonIconsForAlert + "</div><div class=\"timeline-icon bg-info\"><span><image src=\"/Content/Images/" + _alert.Icon + "\"/></span></div><div class=\"timeline-desc\"><p id='Notif" + index + "AlertPAGE" + _alert.Page + "' style=\"cursor:pointer;margin-bottom:0px;\" onclick=\"alertClick('" + _alert.Execute + "','" + _alert.Section + "','" + _alert.Type + "','" + _alert.Page + "','" + _file + "')\">" + _alert.Message + "</p></div></li>");

                    //Notification list
                    _lstAlerts.Add("<li id='Notif" + index + "PAGE" + _alert.Page + "' title='" + _alert.Tooltip + "' style=\"cursor:pointer\" onclick=\"alertClick('" + _alert.Execute + "','" + _alert.Section + "','" + _alert.Type + "','" + _alert.Page + "','" + _file + "')\" class=\"timeline-item\"><div class=\"timeline-icon bg-info\"><span><image src=\"/Content/Images/" + _alert.Icon + "\"/></span></div><div class=\"timeline-desc\"><label style='cursor:pointer;" + marginTopOnTextLength + ";color: #FFFFFF;'>" + _alert.Message + "</label></div></li>");
                    _lstTitles.Add(_alert.Name);
                }
                else
                {
                    //Alert Box messsage list
                    AlertsHTML.Append("<li title='" + _alert.Tooltip + "' class=\"timeline-item\"><div>" + ButtonIconsForAlert + "</div><div class=\"timeline-icon bg-info\"><span><image src=\"/Content/Images/" + _alert.Icon + "\"/></span></div><div class=\"timeline-desc\"><p id='Notif" + index + "AlertPAGE" + _alert.Page + "' style=\"cursor:pointer;margin-bottom:0px;\" onclick=\"alertClick('" + _alert.Execute + "','" + _alert.Section + "','" + _alert.Type + "','" + _alert.Page + "')\">" + _alert.Message + "</p></div></li>");

                    //Notification list
                    _lstAlerts.Add("<li id='Notif" + index + "PAGE" + _alert.Page + "' title='" + _alert.Tooltip + "' style=\"cursor:pointer\" onclick=\"alertClick('" + _alert.Execute + "','" + _alert.Section + "','" + _alert.Type + "','" + _alert.Page + "')\" class=\"timeline-item\"><div class=\"timeline-icon bg-info\"><span><image src=\"/Content/Images/" + _alert.Icon + "\"/></span></div><div class=\"timeline-desc\"><label style='cursor:pointer;" + marginTopOnTextLength + ";color: #FFFFFF;'>" + _alert.Message + "</label></div></li>");
                    _lstTitles.Add(_alert.Name);
                }
                index++;
            }
            AlertCountVal = Convert.ToInt32(Util.GetSessionValue("AlertCountVal"));
            AlertsData _model = new AlertsData();
            if (AlertCountVal < _AlertsModel.Count && AlertCountVal != 0)
            {
                int LIndex = AlertCountVal;
                int Mindex = 0;
                _lstAlerts.Reverse();
                _model.AlertsLastRowHTML = new List<string>();
                _model.AlertsTitle = new List<string>();
                foreach (var li in _lstAlerts)
                {
                    _model.AlertsLastRowHTML.Add(_lstAlerts[LIndex].ToString());
                    _model.AlertsTitle.Add(_lstTitles[LIndex].ToString());
                    LIndex++; Mindex++;
                    if (LIndex == _AlertsModel.Count)
                        goto ContinueFromHere;
                }
            }
            else if (AlertCountVal == 0)
            {
                _model.AlertsLastRowHTML = _lstAlerts;
                _model.AlertsTitle = _lstTitles;
            }
            else
            {
                //_model.AlertsTitle = "New E-mail";
            }
            ContinueFromHere:

            _model.AlertsHTML = AlertsHTML.ToString();
            _model.Count = _AlertsModel.Count;
            //_model.AlertsLastRowHTML = LastRowHTML;
            Util.SetSessionValue("AlertCountVal", _AlertsModel.Count);
            return Json(_model, JsonRequestBehavior.AllowGet);
        }
        //public HtmlString GetTasksMenu()
        //{
        //   clAutomatorMenu _clautomatorMenu = new clAutomatorMenu();
        //   return new HtmlString(_clautomatorMenu.GetCSSMenu());
        //}

        public string GetFavoritesMetaData()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            return goMeta.PageRead(goP.GetUserTID(), "POP_MENU_OPTIONS", "");
        }
        public string AddToFavorites(string DesktopName, string DesktopId, string Section)
        {
            goTr = (clTransform)Util.GetInstance("tr");
            goP = (clProject)Util.GetInstance("p");
            goMeta = (clMetaData)Util.GetInstance("meta");
            goData = (clData)Util.GetInstance("data");

            DataSet dsMenuData = Util.GetMenuDataSet();
            int rowsCount = dsMenuData.Tables[0].Rows.Count;

            string FilterQuery = "MenuType = 'USERFAVORITES' and DesktopId='" + DesktopId + "'";
            DataView dvFavDesktops = new DataView(dsMenuData.Tables[0], FilterQuery, "", DataViewRowState.CurrentRows);
            string returnVal = "";

            //delete if existed
            if (dvFavDesktops != null && dvFavDesktops.ToTable() != null && dvFavDesktops.ToTable().Rows.Count > 0)
            {
                string rowId = dvFavDesktops.ToTable().Rows[0]["MenuId"].ToString();
                dsMenuData.Tables[0].Rows.Find(rowId).Delete();
                dsMenuData.Tables[0].AcceptChanges();

                goP.SetVar("MenuData", dsMenuData);

                string sFavoritesMeta = Util.GetFavoritesMenuMetaData_FromDS(dsMenuData);
                //goMeta.PageWrite(goP.GetUserTID(), "POP_MENU_OPTIONS", sFavoritesMeta, "User Menu Favorites", "", "");
                System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
                goMeta.LineWrite(goP.GetUserTID(), "POP_MENU_OPTIONS", "USERFAVORITES", sFavoritesMeta, ref oConnection);
                returnVal = "Deleted";

            }
            //add if not existed
            else
            {
                FilterQuery = "ItemName = 'USERFAVORITES' and ItemType='ROOT'";
                dvFavDesktops = new DataView(dsMenuData.Tables[0], FilterQuery, "", DataViewRowState.CurrentRows);
                string parentNodeId = "";
                int DisplayOrder = 1;

                if (dvFavDesktops != null && dvFavDesktops.ToTable() != null && dvFavDesktops.ToTable().Rows.Count > 0)
                {
                    parentNodeId = dvFavDesktops.ToTable().Rows[0]["MenuId"].ToString();
                }
                else
                {
                    //add the favourites rootnode to dataset

                    parentNodeId = Guid.NewGuid().ToString();

                    DataRow drFnew = dsMenuData.Tables[0].NewRow();
                    drFnew[0] = rowsCount + 1;
                    drFnew[1] = parentNodeId;
                    drFnew[2] = "USERFAVORITES";
                    drFnew[3] = "ROOT";
                    drFnew[4] = "";
                    drFnew[5] = DBNull.Value;
                    drFnew[6] = 999;
                    drFnew[7] = "";
                    drFnew[8] = "";
                    drFnew[9] = "";
                    drFnew[10] = "";
                    drFnew[11] = "";
                    drFnew[12] = 0;
                    drFnew[13] = "USERFAVORITES";
                    drFnew["IsShared"] = "True";
                    drFnew["IsDeleted"] = "False";
                    drFnew["Section"] = "GLOBAL";

                    dsMenuData.Tables[0].Rows.Add(drFnew);

                }

                DataRow drnew = dsMenuData.Tables[0].NewRow();
                drnew[0] = rowsCount + 1;
                drnew[1] = Guid.NewGuid().ToString();
                drnew[2] = DesktopName;
                drnew[3] = "DESKTOP";
                drnew[4] = "USERFAVORITES";
                drnew[5] = parentNodeId;
                drnew[6] = DisplayOrder;
                drnew[7] = "";
                drnew[8] = "";
                drnew[9] = DesktopName;
                drnew[10] = DesktopId;
                drnew[11] = "";
                drnew[12] = 0;
                drnew[13] = "USERFAVORITES";
                if (Section.ToUpper() == "GLOBAL")
                {
                    drnew["IsShared"] = "True";
                }
                else
                {
                    drnew["IsShared"] = "False";
                }

                drnew["IsDeleted"] = "False";
                drnew["Section"] = Section;

                dsMenuData.Tables[0].Rows.Add(drnew);

                goP.SetVar("MenuData", dsMenuData);

                string sFavoritesMeta = Util.GetFavoritesMenuMetaData_FromDS(dsMenuData);
                //  goMeta.PageWrite(goP.GetUserTID(), "POP_MENU_OPTIONS", sFavoritesMeta, "User Menu Favorites", "", "");
                System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
                goMeta.LineWrite(goP.GetUserTID(), "POP_MENU_OPTIONS", "USERFAVORITES", sFavoritesMeta, ref oConnection);
                returnVal = "Added";
            }

            //DataTable dtFavDesktops = dvFavDesktops.ToTable();
            //int foldersCount = dtFavDesktops.Rows.Count;


            //string sFavoritesMeta = GetFavoritesMetaData();

            //if (string.IsNullOrEmpty(sFavoritesMeta)) //no page found
            //{
            //    //create page in meta data
            //    sFavoritesMeta = "UserFavorites=" + DesktopName + "~" + DesktopId;
            //    goMeta.PageWrite(goP.GetUserTID(), "POP_MENU_OPTIONS", sFavoritesMeta, "User Menu Favorites", "", "");
            //}
            //else 
            //{
            //    string sUserFavorites = goTr.StrRead(sFavoritesMeta, "UserFavorites");

            //    if (string.IsNullOrEmpty(sUserFavorites) == false)
            //    {
            //        StringBuilder _FavoritesBuilder = new StringBuilder();
            //        List<string> sAllData = sUserFavorites.Split(',').ToList<string>();

            //        int index = sAllData.IndexOf(DesktopName + "~" + DesktopId);

            //        if (index > -1)
            //        {
            //            sAllData.RemoveAt(index);
            //            returnVal = "Deleted";
            //        }

            //        else
            //            sAllData.Add(DesktopName + "~" + DesktopId);

            //        if (sAllData.Count > 0)
            //        {
            //            //sAllData.ForEach(x => _FavoritesBuilder.Append(x + ","));

            //            foreach (var item in sAllData)
            //            {
            //                _FavoritesBuilder.Append(item + ",");
            //            }
            //            sUserFavorites = _FavoritesBuilder.ToString().Substring(0, _FavoritesBuilder.ToString().LastIndexOf(","));
            //        }
            //        else 
            //            sUserFavorites = "";
            //        sAllData = null;
            //        if (goTr.StrWrite(ref sFavoritesMeta, "UserFavorites", sUserFavorites))
            //        {
            //            goMeta.PageWrite(goP.GetUserTID(), "POP_MENU_OPTIONS", sFavoritesMeta, "User Menu Favorites", "", "");
            //        }

            //    }
            //    else //rootnode line not found
            //    {
            //        if (goTr.StrWrite(ref sFavoritesMeta, "UserFavorites", DesktopName+"~"+DesktopId))
            //        {
            //            goMeta.PageWrite(goP.GetUserTID(), "POP_MENU_OPTIONS", sFavoritesMeta, "User Menu Favorites", "", "");
            //        }
            //    }
            //}

            //Need to update session popmetadata, else we must logout and login..J
            string sFavoritesMenuAfterUpdate = goMeta.PageRead(goP.GetUserTID(), "POP_MENU_OPTIONS");
            goP.SetVar("POPMenuData", sFavoritesMenuAfterUpdate);

            Util.ClearSessionValue("LeftMenuHtmlString");
            return returnVal;
        }

        [HttpPost]
        public ActionResult Excel_Export_Save(string contentType, string base64, string fileName)
        {
            var fileContents = Convert.FromBase64String(base64);

            return File(fileContents, contentType, fileName);
        }

        public HtmlString LoadLeftMenu()
        {
            SidebarDesktopsMenu desktopsmenu = new SidebarDesktopsMenu();

            HtmlString LeftMenuHtmlString = desktopsmenu.GetMenu1();

            return LeftMenuHtmlString;
        }

        public int GetSelectedRowIndexFromSessionViewInfo(string ViewKey, string Key)
        {
            SessionViewInfo _SessionViewInfo = new SessionViewInfo();
            if (Util.GetSessionValue(Key + "_" + ViewKey) != null)
            {
                _SessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + ViewKey);
            }

            int LastSelectedRowIndex = _SessionViewInfo.LastSelectedRowIndex;

            return LastSelectedRowIndex;
        }


        public JsonResult SiteRunScript(string Script)
        {
            string gsFlag = "";

            if (Script == "STARTOUTLOOKLINK")
            {
                if (!StartOutlookLink())
                {
                    //goUI.NewWorkareaMessage(goErr.GetLastError());
                }
                else
                {
                    gsFlag = "OPENFILE";
                }
            }
            else if (Script == "STARTEMAILLOGGING")
            {
                if (!StartEmailLogging())
                {
                    //goUI.NewWorkareaMessage(goErr.GetLastError());
                }
                else
                {
                    gsFlag = "LOGEMAIL";
                }
            }
            else if (Script == "STARTIMPORTUTILITY")
            {
                if (!StartImportUtility())
                {
                    //goUI.NewWorkareaMessage(goErr.GetLastError());
                }
                else
                {
                    gsFlag = "OPENFILE";
                }
            }


            return Json(gsFlag, JsonRequestBehavior.AllowGet);
        }

        private bool StartOutlookLink()
        {
            //PURPOSE:
            //       Create a send job (SNQ_ MD page) to start outlook link
            //       This is for initiating outlook link from the browser.
            //RETURNS:
            //       True or False if unsuccessful.
            string sProc = "RunScript.aspx::StartOutlookLink";
            string sPageID = null;
            string sIni = "";

            goData = (clData)Util.GetInstance("data");
            goTr = (clTransform)Util.GetInstance("tr");
            goP = (clProject)Util.GetInstance("p");
            goMeta = (clMetaData)Util.GetInstance("meta");
            goErr = (clError)Util.GetInstance("err");

            try
            {
                goTr.StrWrite(ref sIni, "MODE", "OLLINK");
                goTr.StrWrite(ref sIni, "US_NAME", "OLLINK");
                goTr.StrWrite(ref sIni, "SENDNAME", "Start Syncing Outlook");
                goTr.StrWrite(ref sIni, "OBJECTTOSEND", "None");

                sPageID = "SNQ_" + goData.GenSUID("XX");
                if (!goMeta.PageWrite(goP.GetUserTID(), sPageID, sIni))
                {
                    goErr.SetError(35000, sProc, "Error writing SNQ_ metadata to initiate logging e-mail.");
                    return false;
                }
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    goErr.SetError(ex, 45105, sProc);
                //    return false;
                //}
                return false;
            }

            return true;

        }

        private bool StartEmailLogging()
        {
            //AUTHOR: MI 3/6/07
            //PURPOSE:
            //       Create a send job (SNQ_ MD page) to start logging e-mails
            //       This is for initiating logging emails from the browser.
            //RETURNS:
            //       True or False if unsuccessful.
            string sProc = "RunScript.aspx::StartEmailLogging";
            string sPageID = null;
            string sIni = "";

            goData = (clData)Util.GetInstance("data");
            goTr = (clTransform)Util.GetInstance("tr");
            goP = (clProject)Util.GetInstance("p");
            goMeta = (clMetaData)Util.GetInstance("meta");
            goErr = (clError)Util.GetInstance("err");

            try
            {
                goTr.StrWrite(ref sIni, "MODE", "LOGEMAIL");
                goTr.StrWrite(ref sIni, "US_NAME", "LOGEMAIL");
                goTr.StrWrite(ref sIni, "SENDNAME", "Start logging e-mail");
                goTr.StrWrite(ref sIni, "OBJECTTOSEND", "None");

                sPageID = "SNQ_" + goData.GenSUID("XX");
                if (!goMeta.PageWrite(goP.GetUserTID(), sPageID, sIni))
                {
                    goErr.SetError(35000, sProc, "Error writing SNQ_ metadata to initiate logging e-mail.");
                    return false;
                }
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    goErr.SetError(ex, 45105, sProc);
                //    return false;
                //}
                return false;
            }

            return true;
        }

        private bool StartImportUtility()
        {
            //PURPOSE:
            //       Create a send job (SNQ_ MD page) to start outlook link
            //       This is for initiating outlook link from the browser.
            //RETURNS:
            //       True or False if unsuccessful.
            string sProc = "RunScript.aspx::StartImportUtility";
            string sPageID = null;
            string sIni = "";

            goData = (clData)Util.GetInstance("data");
            goTr = (clTransform)Util.GetInstance("tr");
            goP = (clProject)Util.GetInstance("p");
            goMeta = (clMetaData)Util.GetInstance("meta");
            goErr = (clError)Util.GetInstance("err");

            try
            {
                goTr.StrWrite(ref sIni, "MODE", "SELLIMP");
                goTr.StrWrite(ref sIni, "US_NAME", "SELLIMP");
                goTr.StrWrite(ref sIni, "SENDNAME", "Launch Excel Import");
                goTr.StrWrite(ref sIni, "OBJECTTOSEND", "None");

                sPageID = "SNQ_" + goData.GenSUID("XX");
                if (!goMeta.PageWrite(goP.GetUserTID(), sPageID, sIni))
                {
                    goErr.SetError(35000, sProc, "Error writing SNQ_ metadata to initiate logging e-mail.");
                    return false;
                }
            }
            catch (Exception ex)
            {
                //if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                //{
                //    goErr.SetError(ex, 45105, sProc);
                //    return false;
                //}
                return false;
            }

            return true;
        }

        public ActionResult ImportTasks()
        {
            if (Util.GetSessionValue("DesktopId") != null)
            {
                if (Util.GetSessionValue("DesktopId").ToString() != string.Empty)
                {
                    ViewBag.SelectedDesktopID = Util.GetSessionValue("DesktopId");
                    ViewBag.FolderId = Util.GetSessionValue("FolderID");
                    ViewBag.HistoryKey = Util.GetSessionValue("LastDesktopHistoryKey");
                }
            }

            return View();
        }

        public ActionResult ImportDataUtility()
        {
            if (Util.GetSessionValue("DesktopId") != null)
            {
                if (Util.GetSessionValue("DesktopId").ToString() != string.Empty)
                {
                    ViewBag.SelectedDesktopID = Util.GetSessionValue("DesktopId");
                    ViewBag.FolderId = Util.GetSessionValue("FolderID");
                    ViewBag.HistoryKey = Util.GetSessionValue("LastDesktopHistoryKey");
                }
            }

            return View();
        }


        public HtmlString GetAddNewMenuHtml()
        {
            goTr = (clTransform)Util.GetInstance("tr");
            goMeta = (clMetaData)Util.GetInstance("meta");
            goData = (clData)Util.GetInstance("data");

            SidebarDesktopsMenu _sidebarDesktopMenu = new SidebarDesktopsMenu();
            return _sidebarDesktopMenu.GetAddNewMenu();
        }

        public HtmlString GetTaskMenuHtml()
        {
            TasksMenu _tasksmenu = new TasksMenu();
            return _tasksmenu.GetTasksMenu();
        }

        public HtmlString GetHelpMenuHtml()
        {
            HelpMenu _helpmenu = new HelpMenu();
            return _helpmenu.GetHelpMenu();
        }

        public HtmlString GetToolsMenuHtml()
        {
            SidebarDesktopsMenu _sidebarDesktopMenu = new SidebarDesktopsMenu();
            return _sidebarDesktopMenu.GetToolsMenu();
        }

        public HtmlString GetCreateLinkedHtml()
        {
            return Util.GetCreateLinkData();
        }

        public HtmlString GetQuickAddCreateLinkedHtml()
        {
            return Util.GetQuickAddCreateLinkedHtml();
        }

        public HtmlString GetAutomatorToolbarHtml()
        {
            HtmlString automatorToolbarHtml = new HtmlString(Util.BuildAutomatorBar().ToString());
            return automatorToolbarHtml;
        }

        public HtmlString GetHistoryHtml()
        {
            HtmlString HistoryHtml = new HtmlString(GetHistoryListHtml().ToString());
            return HistoryHtml;
        }


        public HtmlString GetHistoryListHtml()
        {
            StringBuilder menu = new StringBuilder();

            List<HistoryItems> _desktopHistoryObjects = new List<HistoryItems>();

            //Get maximum history items count from personal options..J
            string sMaxHistoryItems = Util.GetPersonalOptionsValue("MAXHISTORYITEMS");

            if (Util.GetSessionValue("DesktopHistoryObjects") != null && ((List<HistoryItems>)Util.GetSessionValue("DesktopHistoryObjects")).Count > 0)
            {
                _desktopHistoryObjects = (List<HistoryItems>)Util.GetSessionValue("DesktopHistoryObjects");

                //Take another temp list to reverse the queue..J
                //Get maximum history items as per the count..J
                //var lastHistroyItems = _desktopHistoryObjects.Take(Convert.ToInt32(sMaxHistoryItems)).ToList();

                //No concern with max limit..J
                var lastHistroyItems = _desktopHistoryObjects.ToList();
                lastHistroyItems.Reverse();

                foreach (var item in lastHistroyItems)
                {
                    //Apply CSS style for selected item(note: By defualt the first item should be selected)..J                 
                    //Add delete option for items except selected one..J
                    if (Util.GetSessionValue("SelectedHistoryItem") != null && Util.GetSessionValue("SelectedHistoryItem").ToString() == item.Key)
                    {
                        if (item.FormMode != "NDB")
                        {
                            menu.Append("<li title=\'" + item.Title + "\' style = \'background-color: #EEEEEE;padding: 5px;color: #888888 !important; \'><a style = \"white-space: initial !important; padding:5px 12px !important;color: #888888 !important;text-decoration: none !important;\" onclick=\"return OnHistoryListItemClick(\'" + item.Url + "\',\'" + item.Id + "\',\'" + item.Key + "\',\'" + item.IsDesktop_or_Form + "\',\'" + item.FormKey + "\')\" href=\"#\">" + ((item.Title.Length >= 40) ? item.Title.Substring(0, 40) + "..." : item.Title) + "</a></li>");
                        }
                    }
                    else
                    {
                        if (item.FormMode != "NDB")
                        {
                            if (item.Url.Contains("QT"))
                            {
                                menu.Append("<li style=\"padding: 5px; color: #888888 !important;\" title=\'" + item.Title + "\'><a style = \"white-space: initial !important; padding:5px 12px !important;text-decoration: none !important;color: #888888 !important;\" onclick=\"return OnHistoryListItemClick(\'" + item.Url + "\',\'" + item.Id + "\',\'" + item.Key + "\',\'" + item.IsDesktop_or_Form + "\',\'" + item.FormKey + "\')\" href=\"#\">" + ((item.Title.Length >= 40) ? item.Title.Substring(0, 40) + "..." : item.Title) + "</a></li>");
                            }
                            else
                            {
                                menu.Append("<li style=\"padding: 5px; color: #888888 !important;\" title=\'" + item.Title + "\'><a style = \"white-space: initial !important; padding:5px 12px !important;text-decoration: none !important;color: #888888 !important;\" onclick=\"return OnHistoryListItemClick(\'" + item.Url + "\',\'" + item.Id + "\',\'" + item.Key + "\',\'" + item.IsDesktop_or_Form + "\',\'" + item.FormKey + "\')\" href=\"#\">" + ((item.Title.Length >= 40) ? item.Title.Substring(0, 40) + "..." : item.Title) + "</a><span onclick='DeleteHistoryItem(\"" + item.Key + "\");' class='pull-right' style='width: 40px;'><i class='fa fa-trash' style='margin-left:10px;cursor:pointer;'></i></span></li>");
                            }
                            //menu.Append("<li style=\"padding: 5px; color: #888888 !important;\" title=\'" + item.Title + "\'><a style = \"white-space: initial !important; padding:5px 12px !important;text-decoration: none !important;color: #888888 !important;\" onclick=\"return OnHistoryListItemClick(\'" + item.Url + "\',\'" + item.Id + "\',\'" + item.Key + "\',\'" + item.IsDesktop_or_Form + "\',\'" + item.FormKey + "\')\" href=\"#\">" + ((item.Title.Length >= 40) ? item.Title.Substring(0, 40) + "..." : item.Title) + "</a><span onclick='DeleteHistoryItem(\"" + item.Key + "\");' class='pull-right' style='width: 40px;'><i class='fa fa-trash' style='margin-left:10px;cursor:pointer;'></i></span></li>");                        
                        }
                    }
                }
            }

            HtmlString menubar = new HtmlString(menu.ToString());

            return menubar;
        }

        public void CheckUrl(string Key = "")
        {
            Util.SetSessionValue("IsFromHistory", "true");
            Util.SetSessionValue(Key + "_" + "IsFromCreateForm", true);
            Util.SetSessionValue("SelectedHistoryItem", Key);
        }

        public string DeleteHistoryItem(string FormHistoryKey)
        {
            if (Util.GetSessionValue("DesktopHistoryObjects") != null && ((List<HistoryItems>)Util.GetSessionValue("DesktopHistoryObjects")).Count > 1)
            {
                //Get history items..J
                List<HistoryItems> _desktopHistoryObjects = new List<HistoryItems>();
                _desktopHistoryObjects = (List<HistoryItems>)Util.GetSessionValue("DesktopHistoryObjects");

                //Remove item from history list..J
                _desktopHistoryObjects.Remove(_desktopHistoryObjects.Find(e => e.Key == FormHistoryKey));
            }

            return "success";
        }

        public string DeleteAllHistoryItems()
        {
            if (Util.GetSessionValue("DesktopHistoryObjects") != null && ((List<HistoryItems>)Util.GetSessionValue("DesktopHistoryObjects")).Count > 1)
            {
                //Get history items..J
                List<HistoryItems> _desktopHistoryObjects = new List<HistoryItems>();
                _desktopHistoryObjects = (List<HistoryItems>)Util.GetSessionValue("DesktopHistoryObjects");

                HistoryItems selectedItem = _desktopHistoryObjects.First(item => item.Key == Util.GetSessionValue("SelectedHistoryItem").ToString());
                _desktopHistoryObjects.Clear();
                _desktopHistoryObjects.Add(selectedItem);
            }

            return "success";
        }


        public int GetHistoryItemsCount()
        {
            int count = 0;
            if (Util.GetSessionValue("DesktopHistoryObjects") != null)
            {
                return ((List<HistoryItems>)Util.GetSessionValue("DesktopHistoryObjects")).Count;
            }

            return count;
        }

        //RN #1873 Multiselect functionality added.
        public void UpdateMultiSelectValues(string RecordId, bool IsChecked, string ViewKey, string Key)
        {
            Util.UpdateMultiSelectValues(RecordId, IsChecked, ViewKey, Key, false, "1");
        }

        public string MergeRecords(string ViewKey, string Key)
        {
            if (Util.GetSessionValue(Key + "_" + "MultiSelectedRecords") != null)
            {
                string values = Util.GetSessionValue(Key + "_" + "MultiSelectedRecords").ToString();
                clRowSet doRS1 = default(clRowSet);
                clRowSet doRS2 = default(clRowSet);
                //Util.ClearSessionValue(Key + "_" + "MultiSelectedRecords");
                if (goTr == null)
                {
                    goTr = (clTransform)Util.GetInstance("tr");
                }
                if (goMeta == null)
                {
                    goMeta = (clMetaData)Util.GetInstance("meta");
                }
                if (goData == null)
                {
                    goData = (clData)Util.GetInstance("data");
                }

                string[] RecordIds = values.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                if (RecordIds.Count() > 2 || RecordIds.Count() < 2)
                {
                    return "Please select only two records to merge.";
                }
                DataTable dt1 = new DataTable();
                DataTable dt2 = new DataTable();

                string Record1 = RecordIds[0].ToString();
                string sFileName1 = goTr.GetFileFromSUID(Record1);
                string Record2 = RecordIds[1].ToString();
                string sFileName2 = goTr.GetFileFromSUID(Record2);

                doRS1 = new clRowSet(sFileName1, 1, "GID_ID='" + Record1 + "'", "GID_ID A", "*");
                doRS2 = new clRowSet(sFileName2, 1, "GID_ID='" + Record2 + "'", "GID_ID A", "*");



                if (doRS1.GetFirst() == 1)
                {
                    doRS1.ToTable();
                    dt1 = doRS1.oDataSet.Tables[0];
                    //doRS1 = null;
                }

                if (doRS2.GetFirst() == 1)
                {
                    doRS2.ToTable();
                    dt2 = doRS2.oDataSet.Tables[0];
                    //doRS2 = null;
                }


                string sMDPage = goMeta.PageRead("", "FLD_" + sFileName1);

                StringBuilder sb = new StringBuilder();
                sb.AppendLine("<div>");
                sb.AppendLine("<table id=\"tblMergeRecords\" class=\"table\" style=\"width: 600px; \">");
                sb.AppendLine(" <tbody>");

                sb.AppendLine("<div class=\"row\" style=\"font-size: 15px;\">");
                //sb.AppendLine("<div class=\"col-md-1\"></div>");
                sb.AppendLine("<div class=\"col-md-4\"><b>" + "Master Record :" + "</b></div>");
                sb.AppendLine("<div class=\"col-md-4\">");
                sb.AppendLine("<input type=\"radio\" id=\"MasterRecord1\" name=\"" + "MasterRecord" + "\" class=\"MergeRecords\" value=\"" + Convert.ToString(dt1.Rows[0]["GID_ID"]) + "\" onclick=\"MasterRecordChanged('1')\" checked=\"checked\" />&nbsp;<label for=\"MasterRecord1\"><b>" + Convert.ToString(dt1.Rows[0]["SYS_NAME"]) + "</b></label>");
                sb.AppendLine("</div>");
                sb.AppendLine("<div class=\"col-md-4\">");
                sb.AppendLine("<input type=\"radio\" id=\"MasterRecord2\" name=\"" + "MasterRecord" + "\" class=\"MergeRecords\" value=\"" + Convert.ToString(dt2.Rows[0]["GID_ID"]) + "\" onclick=\"MasterRecordChanged('2')\" />&nbsp;<label for=\"MasterRecord2\"><b>" + Convert.ToString(dt2.Rows[0]["SYS_NAME"]) + "</b></label>");
                sb.AppendLine("</div>");
                sb.AppendLine("</div>");
                sb.AppendLine("<hr>");

                string AllFields = "";

                clArray clAFields = goData.GetFields(sFileName1);
                clArray clALinks = goData.GetLinks(sFileName1);

                Form doForm = new Form(sFileName1, Record1);

                AllFields = doForm.AllFormFields;
                string[] _FormFieldsList = Strings.Split(AllFields, Constants.vbCrLf);


                foreach (var ColumnName in _FormFieldsList)
                {

                    string Prefix = goTr.GetPrefix(ColumnName);
                    switch (Prefix)
                    {
                        case "DTE_":
                        case "TME_":
                        case "TML_":
                        case "DTY_":
                        case "DTQ_":
                        case "DTM_":
                        case "DTD_":
                        case "NDB_":
                            //case "DTT_":
                            goto SkipThisField;
                    }

                    try
                    {
                        Bind_MergePopUp(doRS1, doRS2, dt1, dt2, sFileName1, sb, doForm, ColumnName);
                    }
                    catch (Exception ex)
                    {
                        goto SkipThisField;
                    }

                    SkipThisField: { }
                }

                //goData.GetFields 

                for (int i = 1; i <= clAFields.GetDimension(); i++)
                {
                    string ColumnName = (clAFields.GetItem(i)).ToUpper();
                    if (AllFields.Contains(ColumnName))
                    {
                        goto SkipThisField;
                    }

                    string Prefix = goTr.GetPrefix(ColumnName);
                    switch (Prefix)
                    {
                        case "DTE_":
                        case "TME_":
                        case "TML_":
                        case "DTY_":
                        case "DTQ_":
                        case "DTM_":
                        case "DTD_":
                        case "NDB_":
                            //case "DTT_":
                            goto SkipThisField;
                    }

                    AllFields = AllFields + Constants.vbCrLf + ColumnName;

                    try
                    {
                        Bind_MergePopUp(doRS1, doRS2, dt1, dt2, sFileName1, sb, doForm, ColumnName);
                    }
                    catch (Exception ex)
                    {
                        goto SkipThisField;
                    }

                    SkipThisField: { }
                }


                for (int i = 1; i <= clALinks.GetDimension(); i++)
                {
                    string ColumnName = (clALinks.GetItem(i)).ToUpper();
                    if (AllFields.Contains(ColumnName))
                    {
                        goto SkipThisField;
                    }

                    AllFields = AllFields + Constants.vbCrLf + ColumnName;

                    try
                    {
                        Bind_MergePopUp(doRS1, doRS2, dt1, dt2, sFileName1, sb, doForm, ColumnName);
                    }
                    catch (Exception ex)
                    {
                        goto SkipThisField;
                    }

                    SkipThisField: { }
                }


                sb.AppendLine("<tr>");
                sb.AppendLine("<td>");
                //sb.AppendLine("<button type=\"button\" id=\"btnRecordMerge\" onclick=\"RecordMergeClick()\">OK</button>");
                sb.AppendLine("</td>");
                sb.AppendLine("</tr>");

                sb.AppendLine("</tbody>");
                sb.AppendLine("</table>");
                sb.AppendLine("</div>");

                // return "Records has been merged successfully.";
                return sb.ToString();


                /*******************************************************/


                //string FieldName = "";
                //foreach(var Field in FieldList)
                //{
                //    //for(int FieldIndx=0; FieldIndx < clAFields.GetDimension(); FieldIndx++)
                //    //{
                //    //    FieldName = clAFields.GetItem(FieldIndx).ToUpper();

                //    //    string Prefix = goTr.GetPrefix(FieldName);
                //    //    switch (Prefix)
                //    //    {
                //    //        case "DTE_":
                //    //        case "TME_":
                //    //        case "TML_":
                //    //        case "DTY_":
                //    //        case "DTQ_":
                //    //        case "DTM_":
                //    //        case "DTD_":
                //    //        //case "DTT_":
                //    //            goto SkipThisField;
                //    //    }



                //    //    SkipThisField: { }
                //    //}



                //}

                //*********************************************************************

                //foreach (DataRow row1 in dt1.Rows)
                //{
                //    foreach (DataColumn column in dt1.Columns)
                //    {
                //        string ColumnData2 = "";
                //        //string ColumnName2 = "";
                //        string ColumnName1 = column.ColumnName;
                //        string ColumnData1 = row1[Convert.ToInt32(column.Ordinal)].ToString();
                //        foreach (DataRow row2 in dt2.Rows)
                //        {
                //            ColumnData2 = row2[Convert.ToInt32(column.Ordinal)].ToString();
                //            //ColumnName2 = row2[column.ColumnName].ToString();
                //            //goto RunFromHere;
                //            //}
                //            //RunFromHere:
                //            if ((ColumnData1 != "" || ColumnData2 != "") && (ColumnData1 != ColumnData2))
                //            {
                //                string sColumnNameVal = goTr.StrRead(sMDPage, ColumnName1);

                //                if (ColumnName1 != "ID" && !ColumnName1.Contains("GID_ID"))
                //                {
                //                    sColumnNameVal = string.IsNullOrEmpty(sColumnNameVal) ? ColumnName1 : sColumnNameVal;

                //                    sb.AppendLine("<div class=\"row\">");
                //                    //sb.AppendLine("<div class=\"col-md-1\"></div>");


                //                    if (ColumnName1.Contains("LNK"))
                //                    {
                //                        int iColumnName_Len = ColumnName1.IndexOf("%");
                //                        sColumnNameVal = ColumnName1.Substring(0, iColumnName_Len);

                //                        string LnkFileName = sColumnNameVal.Substring(iColumnName_Len - 2);
                //                        LnkFileName = goMeta.PageRead("", "FRM_" + LnkFileName);
                //                        LnkFileName = goTr.StrRead(LnkFileName, "NAME");
                //                        LnkFileName = LnkFileName.Replace("Form", "");

                //                        sb.AppendLine("<div class=\"col-md-4\"><b>" + LnkFileName + " :</b></div>");
                //                    }
                //                    else
                //                    {
                //                        sb.AppendLine("<div class=\"col-md-4\"><b>" + sColumnNameVal + " :</b></div>");
                //                    }
                //                    sb.AppendLine("<div class=\"col-md-4\">");

                //                    if (ColumnName1.Contains("%"))
                //                    {
                //                        string ColumnData1_GID_ID = "";
                //                        string ColumnData2_GID_ID = "";

                //                        ColumnData1_GID_ID = row1[sColumnNameVal + "%%GID_ID"].ToString();
                //                        ColumnData2_GID_ID = row2[sColumnNameVal + "%%GID_ID"].ToString();


                //                        sb.AppendLine("<input type=\"radio\" id=\"" + sColumnNameVal.Trim() + "1\" name=\"" + sColumnNameVal + "\" class=\"MergeRecords MergeOption1\" value=\"1\" checked=\"checked\" />&nbsp;<label for=\"" + sColumnNameVal.Trim() + "1\">" + ColumnData1 + "</label>");
                //                        sb.AppendLine("</div>");
                //                        sb.AppendLine("<div class=\"col-md-4\">");
                //                        sb.AppendLine("<input type=\"radio\" id=\"" + sColumnNameVal.Trim() + "2\" name=\"" + sColumnNameVal + "\" class=\"MergeRecords MergeOption2\" value=\"2\" />&nbsp;<label for=\"" + sColumnNameVal.Trim() + "2\">" + ColumnData2 + "</label>");
                //                    }
                //                    else if (ColumnName1.Contains("MLS"))
                //                    {
                //                        string ColumnData1_Index = ColumnData1;
                //                        string ColumnData2_Index = ColumnData2;

                //                        ColumnData1 = Convert.ToString(doRS1.GetFieldVal(ColumnName1, 1));
                //                        ColumnData2 = Convert.ToString(doRS2.GetFieldVal(ColumnName1, 1));

                //                        sb.AppendLine("<input type=\"radio\" id=\"" + ColumnName1.Trim() + "1\" name=\"" + ColumnName1 + "\" class=\"MergeRecords MergeOption1\" value=\"1\" checked=\"checked\" />&nbsp;<label for=\"" + ColumnName1.Trim() + "1\">" + ColumnData1 + "</label>");
                //                        sb.AppendLine("</div>");
                //                        sb.AppendLine("<div class=\"col-md-4\">");
                //                        sb.AppendLine("<input type=\"radio\" id=\"" + ColumnName1.Trim() + "2\" name=\"" + ColumnName1 + "\" class=\"MergeRecords MergeOption2\" value=\"2\" />&nbsp;<label for=\"" + ColumnName1.Trim() + "2\">" + ColumnData2 + "</label>");
                //                    }
                //                    else if (ColumnName1.Contains("CHK"))
                //                    {
                //                        string ColumnData1_Index = ColumnData1;
                //                        string ColumnData2_Index = ColumnData2;

                //                        ColumnData1 = ColumnData1 == "1" ? "true" : "false";
                //                        ColumnData2 = ColumnData2 == "1" ? "true" : "false";

                //                        sb.AppendLine("<input type=\"radio\" id=\"" + ColumnName1.Trim() + "1\" name=\"" + ColumnName1 + "\" class=\"MergeRecords MergeOption1\" value=\"1\" checked=\"checked\" />&nbsp;<label for=\"" + ColumnName1.Trim() + "1\">" + ColumnData1 + "</label>");
                //                        sb.AppendLine("</div>");
                //                        sb.AppendLine("<div class=\"col-md-4\">");
                //                        sb.AppendLine("<input type=\"radio\" id=\"" + ColumnName1.Trim() + "2\" name=\"" + ColumnName1 + "\" class=\"MergeRecords MergeOption2\" value=\"2\" />&nbsp;<label for=\"" + ColumnName1.Trim() + "2\">" + ColumnData2 + "</label>");
                //                    }
                //                    else
                //                    {
                //                        sb.AppendLine("<input type=\"radio\" id=\"" + ColumnName1.Trim() + "1\" name=\"" + ColumnName1 + "\" class=\"MergeRecords MergeOption1\" value=\"1\" checked=\"checked\" />&nbsp;<label for=\"" + ColumnName1.Trim() + "1\">" + ColumnData1 + "</label>");
                //                        sb.AppendLine("</div>");
                //                        sb.AppendLine("<div class=\"col-md-4\">");
                //                        sb.AppendLine("<input type=\"radio\" id=\"" + ColumnName1.Trim() + "2\" name=\"" + ColumnName1 + "\" class=\"MergeRecords MergeOption2\" value=\"2\" />&nbsp;<label for=\"" + ColumnName1.Trim() + "2\">" + ColumnData2 + "</label>");
                //                    }
                //                    sb.AppendLine("</div>");
                //                    sb.AppendLine("</div>");
                //                    sb.AppendLine("<hr>");
                //                }
                //            }
                //        }

                //    }
                //}

                //sb.AppendLine("<tr>");
                //sb.AppendLine("<td>");
                ////sb.AppendLine("<button type=\"button\" id=\"btnRecordMerge\" onclick=\"RecordMergeClick()\">OK</button>");
                //sb.AppendLine("</td>");
                //sb.AppendLine("</tr>");

                //sb.AppendLine("</tbody>");
                //sb.AppendLine("</table>");
                //sb.AppendLine("</div>");

                ////return "Records has been merged successfully.";
                //return sb.ToString();
            }
            else
            {
                return "Please select only two records to <b>Merge</b>.";
            }
        }

        private void Bind_MergePopUp(clRowSet doRS1, clRowSet doRS2, DataTable dt1, DataTable dt2, string sFileName1, StringBuilder sb, Form doForm, string ColumnName)
        {
            string ColumnData1 = dt1.Rows[0][ColumnName].ToString();
            string ColumnData2 = dt2.Rows[0][ColumnName].ToString();

            if ((ColumnData1 != "" || ColumnData2 != "") && (ColumnData1 != ColumnData2))
            {
                //string sColumnNameVal = goTr.StrRead(sMDPage, ColumnName);
                string sColumnNameVal = doForm.GetFieldLabel(ColumnName) != "" ? doForm.GetFieldLabel(ColumnName) : (goData.GetFieldLabel(sFileName1, ColumnName) == "" ? ColumnName : goData.GetFieldLabel(sFileName1, ColumnName));

                if (ColumnName != "ID" && !ColumnName.Contains("GID_ID"))
                {
                    sColumnNameVal = string.IsNullOrEmpty(sColumnNameVal) ? ColumnName : sColumnNameVal;

                    sb.AppendLine("<div class=\"row\">");
                    //sb.AppendLine("<div class=\"col-md-1\"></div>");


                    if (ColumnName.Contains("LNK"))
                    {
                        int iColumnName_Len = ColumnName.IndexOf("%");
                        sColumnNameVal = ColumnName.Substring(0, iColumnName_Len);

                        string LnkFileName = sColumnNameVal.Substring(iColumnName_Len - 2);
                        LnkFileName = goMeta.PageRead("", "FRM_" + LnkFileName);
                        LnkFileName = goTr.StrRead(LnkFileName, "NAME");
                        LnkFileName = LnkFileName.Replace("Form", "");

                        sb.AppendLine("<div class=\"col-md-4\"><b>" + LnkFileName + " :</b></div>");
                    }
                    else
                    {
                        sb.AppendLine("<div class=\"col-md-4\"><b>" + sColumnNameVal + " :</b></div>");
                    }
                    sb.AppendLine("<div class=\"col-md-4\">");

                    if (ColumnName.Contains("%"))
                    {
                        string ColumnData1_GID_ID = "";
                        string ColumnData2_GID_ID = "";

                        ColumnData1_GID_ID = dt1.Rows[0][sColumnNameVal + "%%GID_ID"].ToString();
                        ColumnData2_GID_ID = dt1.Rows[0][sColumnNameVal + "%%GID_ID"].ToString();


                        sb.AppendLine("<input type=\"radio\" id=\"" + sColumnNameVal.Trim() + "1\" name=\"" + sColumnNameVal + "\" class=\"MergeRecords MergeOption1\" value=\"1\" checked=\"checked\" />&nbsp;<label for=\"" + sColumnNameVal.Trim() + "1\">" + ColumnData1 + "</label>");
                        sb.AppendLine("</div>");
                        sb.AppendLine("<div class=\"col-md-4\">");
                        sb.AppendLine("<input type=\"radio\" id=\"" + sColumnNameVal.Trim() + "2\" name=\"" + sColumnNameVal + "\" class=\"MergeRecords MergeOption2\" value=\"2\" />&nbsp;<label for=\"" + sColumnNameVal.Trim() + "2\">" + ColumnData2 + "</label>");
                    }
                    else if (ColumnName.Contains("MLS"))
                    {
                        string ColumnData1_Index = ColumnData1;
                        string ColumnData2_Index = ColumnData2;

                        ColumnData1 = Convert.ToString(doRS1.GetFieldVal(ColumnName, 1));
                        ColumnData2 = Convert.ToString(doRS2.GetFieldVal(ColumnName, 1));

                        sb.AppendLine("<input type=\"radio\" id=\"" + ColumnName.Trim() + "1\" name=\"" + ColumnName + "\" class=\"MergeRecords MergeOption1\" value=\"1\" checked=\"checked\" />&nbsp;<label for=\"" + ColumnName.Trim() + "1\">" + ColumnData1 + "</label>");
                        sb.AppendLine("</div>");
                        sb.AppendLine("<div class=\"col-md-4\">");
                        sb.AppendLine("<input type=\"radio\" id=\"" + ColumnName.Trim() + "2\" name=\"" + ColumnName + "\" class=\"MergeRecords MergeOption2\" value=\"2\" />&nbsp;<label for=\"" + ColumnName.Trim() + "2\">" + ColumnData2 + "</label>");
                    }
                    else if (ColumnName.Contains("CHK"))
                    {
                        string ColumnData1_Index = ColumnData1;
                        string ColumnData2_Index = ColumnData2;

                        ColumnData1 = ColumnData1 == "1" ? "true" : "false";
                        ColumnData2 = ColumnData2 == "1" ? "true" : "false";

                        sb.AppendLine("<input type=\"radio\" id=\"" + ColumnName.Trim() + "1\" name=\"" + ColumnName + "\" class=\"MergeRecords MergeOption1\" value=\"1\" checked=\"checked\" />&nbsp;<label for=\"" + ColumnName.Trim() + "1\">" + ColumnData1 + "</label>");
                        sb.AppendLine("</div>");
                        sb.AppendLine("<div class=\"col-md-4\">");
                        sb.AppendLine("<input type=\"radio\" id=\"" + ColumnName.Trim() + "2\" name=\"" + ColumnName + "\" class=\"MergeRecords MergeOption2\" value=\"2\" />&nbsp;<label for=\"" + ColumnName.Trim() + "2\">" + ColumnData2 + "</label>");
                    }
                    else
                    {
                        sb.AppendLine("<input type=\"radio\" id=\"" + ColumnName.Trim() + "1\" name=\"" + ColumnName + "\" class=\"MergeRecords MergeOption1\" value=\"1\" checked=\"checked\" />&nbsp;<label for=\"" + ColumnName.Trim() + "1\">" + ColumnData1 + "</label>");
                        sb.AppendLine("</div>");
                        sb.AppendLine("<div class=\"col-md-4\">");
                        sb.AppendLine("<input type=\"radio\" id=\"" + ColumnName.Trim() + "2\" name=\"" + ColumnName + "\" class=\"MergeRecords MergeOption2\" value=\"2\" />&nbsp;<label for=\"" + ColumnName.Trim() + "2\">" + ColumnData2 + "</label>");
                    }
                    sb.AppendLine("</div>");
                    sb.AppendLine("</div>");
                    sb.AppendLine("<hr>");
                }
            }
        }

        public JsonResult Submit_MergeRecord(string selectedvalues, string Key)
        {
            if (goTr == null)
            {
                goTr = (clTransform)Util.GetInstance("tr");
            }
            if (goMeta == null)
            {
                goMeta = (clMetaData)Util.GetInstance("meta");
            }
            if (goData == null)
            {
                goData = (clData)Util.GetInstance("data");
            }
            if (goErr == null)
            {
                goErr = (clError)Util.GetInstance("err");
            }

            string values = Util.GetSessionValue(Key + "_" + "MultiSelectedRecords").ToString();
            string[] RecordIds = values.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);





            selectedvalues = selectedvalues.Substring(1);
            string[] ColumnVals = selectedvalues.Split('|');

            string[] MergeRecord = Regex.Split(ColumnVals[0], "<=>");

            string MergeToRecord = "";
            string MergingRecord = "";

            string IsFirstRecord = "1";
            if (RecordIds[0].Trim() == MergeRecord[1].Trim())
            {
                MergeToRecord = RecordIds[0];
                MergingRecord = RecordIds[1];
                IsFirstRecord = "1";
            }
            else
            {
                MergeToRecord = RecordIds[1];
                MergingRecord = RecordIds[0];
                IsFirstRecord = "2";
            }

            string sFileName = goTr.GetFileFromSUID(MergeRecord[1]);

            string rStatus = "";
            try
            {
                clRowSet doRS_MergeTo = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + MergeToRecord + "'", "GID_ID A", "*");
                clRowSet doRS_Merging = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + MergingRecord + "'", "GID_ID A", "*");

                clArray oLinks = new clArray();

                if (doRS_MergeTo.GetFirst() == 1)
                {
                    int Col_Index = 0;
                    foreach (var EachColumn in ColumnVals)
                    {
                        if (Col_Index > 0)
                        {
                            string[] MergeColumn = Regex.Split(EachColumn, "<=>");
                            string sPrefix = Strings.Left(goTr.GetPrefix(MergeColumn[0]), 3);

                            if (IsFirstRecord == "1" && MergeColumn[1] == "1" || IsFirstRecord == "2" && MergeColumn[1] == "2")
                            {
                                //doRS_MergeTo.SetFieldVal(MergeColumn[0], doRS_Merging.GetFieldVal(MergeColumn[0]));
                            }
                            else if (IsFirstRecord == "1" && MergeColumn[1] == "2" || IsFirstRecord == "2" && MergeColumn[1] == "1")
                            {
                                if (sPrefix == "LNK")
                                {
                                    if (goData.LKGetType(sFileName, MergeColumn[0]) == "N1")
                                    {
                                        if (!string.IsNullOrEmpty(Convert.ToString(doRS_Merging.GetFieldVal(MergeColumn[0]))))
                                        {
                                            doRS_MergeTo.SetFieldVal(MergeColumn[0], doRS_Merging.GetFieldVal(MergeColumn[0]));
                                        }
                                    }
                                    else
                                    {
                                        //NN link; add to any existing links
                                        DataTable oTable = new DataTable();
                                        oLinks = doRS_Merging.GetLinkVal(MergeColumn[0], ref oLinks, true, 0, -1, "A_a", ref oTable);
                                        oLinks = doRS_MergeTo.GetLinkVal(MergeColumn[0], ref oLinks, false, 0, -1, "A_a", ref oTable);
                                        doRS_MergeTo.SetFieldVal(MergeColumn[0], oLinks, clC.SELL_FORMAT_SYSTEM);
                                    }
                                }
                                else
                                {
                                    doRS_MergeTo.SetFieldVal(MergeColumn[0], doRS_Merging.GetFieldVal(MergeColumn[0]));
                                }
                            }
                        }

                        Col_Index++;
                    }



                    //Commit changes, delete old record and refresh?
                    if (doRS_MergeTo.Commit() != 1)
                    {
                        //goErr.SetError(35000, "Submit_MergeRecord", "Committing record " + MergeToRecord + " failed. Records were not merged.");
                        //rStatus = "Committing record " + MergeToRecord + " failed. Records were not merged.";
                        rStatus = "Committing record " + MergeToRecord + " failed. " + goErr.GetLastError("Message");
                    }
                    else
                    {
                        //Delete original record (selected record)
                        //If doRsMerging.DeleteRecord() <> 1 Then
                        //    goErr.SetError(35000, sProc, "Deleting record " & sRecordMerging & " failed. Records were merged but the original record was not deleted.")
                        //    Return False
                        //End If
                        doRS_Merging.SetFieldVal("CHK_ACTIVEFIELD", 0); //Making Source merge record inactive.
                        if (doRS_Merging.Commit() == 1)
                            rStatus = "Records were merged and original record moved to Inactive.";
                        else
                            rStatus = "Records were merged and original record is active.";
                    }
                }
            }
            catch (Exception ex)
            {
                rStatus = ex.Message;
            }
            return Json(rStatus, JsonRequestBehavior.AllowGet);
        }

        public ActionResult DoGlobalSearch(string doSearchText, string doSearchType)
        {
            string txt = doSearchText;
            Util.SetSessionValue("globalSearchKey", doSearchText);
            goMeta = (clMetaData)Util.GetInstance("meta");
            clTransform goTR = (clTransform)Util.GetInstance("tr");
            //goData = (clData)Util.GetInstance("data");
            string sViewMD = goMeta.PageRead("Global", "OTH_GlobalSearchDesktops", "", true);
            string skey = goTR.StrRead(sViewMD, doSearchType, "");
            return Json(skey, JsonRequestBehavior.AllowGet);
        }

        //public JsonResult GroupedChart_ReadData([DataSourceRequest] DataSourceRequest request, string ViewKey, string Key = "")
        //{
        //    DataTable dt = new DataTable();

        //    if (Util.GetSessionValue(Key + "_" + ViewKey + "_dt") != null)
        //    {
        //        dt =(DataTable)Util.GetSessionValue(Key + "_" + ViewKey + "_dt");
        //    }           

        //    var result = dt.ToDataSourceResult(request);           
        //    return Json(result, JsonRequestBehavior.AllowGet);
        //}
        [HttpPost]
        public ActionResult Upload_image_to_blob(string Base64Imagestr)
        {
            string Imagestr = Base64Imagestr.Split(',')[1];          
            var bytes = Convert.FromBase64String(Imagestr);
            MemoryStream memoryStream = new MemoryStream(bytes);

            //string _imageName = Convert.ToBase64String(Guid.NewGuid().ToByteArray()).Replace ("==","");
            //_imageName = _imageName + ".png";
            //DateTime currentTime = DateTime.UtcNow;
            //long unixTime = ((DateTimeOffset)currentTime).ToUnixTimeMilliseconds();
            //string sSiteName = Util.GetHostName().ToLower().Replace(".selltis.com",""); //"otctest";
            //string sUsername = goP.GetMe("code");
            //string _imageName = sSiteName + "_" + sUsername + "_" + unixTime.ToString() + ".png";  

            string _imageName = Guid.NewGuid().ToString() + ".png";
            string sImageURL = Util.UploadFileToBlob(_imageName, memoryStream, "image/png", "editorimages");
            memoryStream.Dispose();

            return Json(sImageURL, JsonRequestBehavior.AllowGet);
        }
        public string Get_MLS_Control_For_QuickEdit(string TableName, string FieldName,string HeaderFieldName, string selectedValue,string gidid)
        {
            clList oMLSList = new clList();
            clArray aList = default(clArray);
            aList = oMLSList.GetList(TableName, FieldName);
            StringBuilder sboptions = new StringBuilder();

            //if (width > 30)
            //{
            //    width = width - 10;
            //}

            //SessionViewInfo _SessionViewInfo = new SessionViewInfo();
            //if (Util.GetSessionValue(Key + "_" + ViewId.Replace(" ", "")) != null)
            //{
            //    _SessionViewInfo = (SessionViewInfo)Util.GetSessionValue(Key + "_" + ViewId.Replace(" ", ""));
            //}

            if (goTr == null)
            {
                goTr = (clTransform)Util.GetInstance("tr");
            }

            //string _EnableBulkSave = goTr.StrRead(_SessionViewInfo.ViewMetaData, "ENABLEBULKSAVE", "false");
            //if (_EnableBulkSave == "1")
            //{
            //    _EnableBulkSave = "true";
            //}
            //else
            //{
            //    _EnableBulkSave = "false";
            //}
            long x = 0;
            for (x = 1; x <= aList.GetDimension(); x++)
            {
                string sItemText = aList.GetItem(x);
                string sItemTextToCompare = sItemText.Replace("<", "").Replace(">", "");
                string ReplacesField = FieldName.Replace("MLS_", "");
                string sItemValue = oMLSList.LReadSeek(TableName + ":" + ReplacesField, "VALUE", sItemText);

                if (sItemTextToCompare == selectedValue)
                {
                    if (sItemText.ToLower() == "<make selection>")
                    {
                        sItemText = sItemText.Replace("<", "-- ").Replace(">", " --");
                    }
                    sboptions.AppendLine("<option id='" + sItemValue + "' selected value='" + sItemValue + "'>" + sItemText + "</option>");
                }
                else
                {
                    if (sItemText.ToLower() == "<make selection>")
                    {
                        sItemText = sItemText.Replace("<", "-- ").Replace(">", " --");
                    }
                    sboptions.AppendLine("<option id='" + sItemValue + "' value='" + sItemValue + "'>" + sItemText + "</option>");
                }

            }

            string _id = FieldName + "_" + gidid;


            string sMLSctrl =
            "<select id='" + _id + "' onchange=\"OnValueChange('" + FieldName.ToUpper() + "','"+ gidid + "','"+ selectedValue + "','"+ HeaderFieldName + "')\" style=\"Width:90%;font-size:11px !important;box-sizing:border-box;height: 23px;\">" +
            sboptions.ToString() +
            "</select>";

            return sMLSctrl;

        }

        public HtmlString GetQuickSearchMenu()
        {
            goTr = (clTransform)Util.GetInstance("tr");
            goMeta = (clMetaData)Util.GetInstance("meta");
            StringBuilder menu = new StringBuilder();            
            string sPage = goMeta.PageRead("GLOBAL", "OTH_QUICKSEARCHMENU", "", true);
            int n = 5;
            for (int i = 1; i <= goTr.StringToNum(goTr.StrRead(sPage, "US_COUNT", "0", false), "0", ref n, ""); i++)
            {
                string sPos = "US_" + goTr.Pad(i.ToString(), 3, "", " ");
                string sTarget = goTr.StrRead(sPage, sPos, "", false);                

                if (!string.IsNullOrEmpty(sTarget))
                {
                    menu.Append("<option>" + sTarget + "</option>");                   
                }
            }
            HtmlString menubar = new HtmlString(menu.ToString());
            return menubar;
        }
    }
}