﻿CREATE TABLE [dbo].[XA] (
    [GID_ID]                 UNIQUEIDENTIFIER CONSTRAINT [DF_XA_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'XA',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                 BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]               NVARCHAR (80)    NULL,
    [DTT_CreationTime]       DATETIME         CONSTRAINT [DF_XA_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]           TINYINT          NULL,
    [TXT_ModBy]              VARCHAR (4)      NULL,
    [DTT_ModTime]            DATETIME         CONSTRAINT [DF_XA_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_RecentName]         NVARCHAR (50)    NULL,
    [MMO_ImportData]         NTEXT            NULL,
    [SI__ShareState]         TINYINT          CONSTRAINT [DF_XA_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]       UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]           VARCHAR (50)     NULL,
    [TXT_ExternalID]         NVARCHAR (80)    NULL,
    [TXT_ExternalSource]     VARCHAR (10)     NULL,
    [TXT_ImpJobID]           VARCHAR (20)     NULL,
    [TXT_File]               NVARCHAR (2)     NULL,
    [TXT_EntityType]         NVARCHAR (20)    NULL,
    [TXT_EntityID]           NVARCHAR (50)    NULL,
    [DTT_LastOpenedDateTime] DATETIME         NULL,
    [GID_RELATED_US]         UNIQUEIDENTIFIER NULL,
    [GID_RELATED_CO]         UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_XA] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE NONCLUSTERED INDEX [IX_XA_RecentName]
    ON [dbo].[XA]([TXT_RecentName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XA_CreatedBy_US]
    ON [dbo].[XA]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XA_ModDateTime]
    ON [dbo].[XA]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XA_Name]
    ON [dbo].[XA]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XA_CreationTime]
    ON [dbo].[XA]([DTT_CreationTime] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_XA_BI__ID]
    ON [dbo].[XA]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XA_TXT_ImportID]
    ON [dbo].[XA]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_XA_US_File_Entity]
    ON [dbo].[XA]([GID_RELATED_US] ASC, [TXT_EntityID] ASC)
    INCLUDE([DTT_LastOpenedDateTime]);

