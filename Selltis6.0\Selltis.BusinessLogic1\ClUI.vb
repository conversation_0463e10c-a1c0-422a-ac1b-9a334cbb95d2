﻿Imports Microsoft.VisualBasic
Imports System
Imports System.Web
Imports System.Data
Imports System.Math
Imports System.Text.RegularExpressions

Public Class ClUI

    Dim goP As clProject
    Dim goMeta As clMetaData
    Dim goTR As clTransform
    Dim goData As clData
    Dim goErr As clError
    Dim goLog As clLog
    Dim goDef As clDefaults
    Dim goScr As clScrMngRowSet
    Dim goNumMask As clNumMask
    ' Dim goUI As clUI
    Dim goHist As clHistory
    Dim goPerm As clPerm
    Dim goWAMess As clWorkareaMessage

    Private gcUIObjects As New Collection
    Private gcNavigationQueue As New Collection
    Private gcQueuedObjectsToLoad As New Collection
    Shared gbExecuteSendPopup As Boolean = False
    Shared QueueList As New List(Of QueueItems)()

    Function AddAlert(ByVal par_sMessage As String,
                           ByVal par_sType As String,
                           ByVal par_sExecute As String,
                           Optional ByVal par_sUser As String = clC.SELL_USER_ALL,
                           Optional ByVal par_sIcon As String = "",
                           Optional ByVal par_sProduct As String = "XX",
                           Optional ByVal par_sTooltip As String = "") As Boolean
        'MI 4/24/12 Started adding cuplicate checking code, but abandoned the approach. Would require a query for each user
        '       in the loop for the following 'line' records in MD, which constitute a duplicate:
        '       TYPE=
        '       MESSAGE=
        '       EXECUTE=
        '       INNER JOINS would be needed for those 3 MD records.
        '       This was originally requested by the dev team (RH) to prevent writing duplicate alerts in a high alert 
        '       creation environment, all in response to a customer request for Tena to write an alert creation routine, 
        '       which would create 2400 alerts/day.
        'MI 4/20/12 Writing MESSAGE and TOOLTIP only when not "".
        'WT 10/27/10 added par_sTooltip
        'MI 2/6/09 Added par_sProduct.
        'MI 10/26/07 Added logging.

        'PURPOSE:
        '  Create one or more new alert(s)(ALT_) record(s) in metadata.  Metadata is written using an add rowset. See notes within
        '  the code below. BY default alerts are written for XX product (which makes it apply to all products).
        'PARAMETERS:
        '  par_sMessage: Text to display in the ALERTS window.
        '  par_sType: Alert type. Supported values:
        '   OPENRECORD  'Record TID
        '   OPENDESKTOP  'Page ID of the desktop to open
        '   OPENNEWFORM     'type of form to open (CRU_CN, CRL_CN)
        '   RUNSCRIPT   'script name
        '   *** NOT SUPPORTED IN SELLSQL  *** PROGRAM   'Full path of the program to run (<ProgramDir> and <DBDir>
        '       'keywords supported
        '   DOCUMENT     URL
        '  par_sExecute: ID of the record, page ID of desktop, type of new form to open, script to run
        '   or the URL to run/open. See par_sType for supported alert types.
        '  par_sIcon: Optional: Icon file name without path.
        '   Evaluated from images.
        '  par_sProduct: Product code like SA, MB, or XX for 'all products. If not specified,
        '   'XX' will be used. To get the current session's product, send goP.GetProduct() in this param.
        '  par_sTooltip: tooltip shown when hovering over alert in alerts panel. if blank, actual tooltip is MESSAGE
        '  *** NOT SUPPORTED *** par_bMsgBox: Display the alert as an 'above all' message box instead of in the
        '   ALERTS window. The Alerts button in SellSQL doesn't change if this property is True.
        '  *** NOT SUPPORTED *** par_sMsgType: Message type (ignored if par_bMsgBox <> 1). Supported values:
        '   OK    'OK button only
        '   YESNO   'Yes and No buttons (default)
        '   'LIST   'Currently not supported because LIST messages allow specifying
        '       'automator actions. For now we don't want to deal with
        '       'running automator actions outside of the automator context
        'RETURNS:
        '  String: True on success or errors
        'EXAMPLE:

        goP = HttpContext.Current.Session("goP")
        goTR = HttpContext.Current.Session("goTr")
        goMeta = HttpContext.Current.Session("goMeta")
        goData = HttpContext.Current.Session("goData")
        goErr = HttpContext.Current.Session("goErr")
        goLog = HttpContext.Current.Session("goLog")
        goDef = HttpContext.Current.Session("goDef")
        goScr = HttpContext.Current.Session("goScr")
        goNumMask = HttpContext.Current.Session("goNumMask")
        'goUI = HttpContext.Current.Session("goUI")
        goHist = HttpContext.Current.Session("goHist")
        goPerm = HttpContext.Current.Session("goPerm")

        Dim sProc As String = "clUI::AddAlert"
        goLog.Log(sProc, "Start: '" & par_sMessage & "' Type: '" & par_sType & "' Execute: '" & par_sExecute & "' User: '" & par_sUser & "'", clC.SELL_LOGLEVEL_DETAILS, True)

        Dim sString As String = ""
        Dim rs As clRowSet
        Dim i As Integer
        Dim sStr As String
        Dim sUserGID As String

        'Try

        'Write parameters for the memo
        goTR.StrWrite(sString, "US_NAME", "Alert")
            If par_sMessage <> "" Then
                goTR.StrWrite(sString, "MESSAGE", par_sMessage)
            End If
            goTR.StrWrite(sString, "TYPE", par_sType)
            goTR.StrWrite(sString, "EXECUTE", par_sExecute)
            If par_sTooltip <> "" Then
                goTR.StrWrite(sString, "TOOLTIP", par_sTooltip)
            End If
            If par_sIcon <> "" Then
                goTR.StrWrite(sString, "ICON", par_sIcon)
            End If

            'Get rs of GID_IDs for users to add alerts for
            If par_sUser = clC.SELL_USER_ALL Then
                rs = New clRowSet("US", clC.SELL_READONLY, "CHK_ActiveField='1'", , "GID_ID")
            Else
                rs = New clRowSet("US", clC.SELL_READONLY, "GID_ID='" & par_sUser & "' AND CHK_ActiveField='1'", , "GID_ID")
            End If

            'Add an alert for each active user
            For i = 1 To rs.Count
                sUserGID = rs.GetFieldVal("GID_ID", clC.SELL_FRIENDLY)
                sStr = "ALT_" & goData.GenGuid
                'MI 4/24/12 ==> Evaluate duplicates for sUserGID here. 
                'A duplicate is an MD 'page' that has the same TYPE= MESSAGE= EXECUTE=.
                goMeta.PageWrite(sUserGID, sStr, sString, "Alert", , par_sProduct)

                rs.GetNext()
            Next

        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45101, sProc)
        '    End If
        'End Try

        Return True
    End Function

    Public Sub Queue(ByVal sType As String, ByVal NavigationItem As Object)

        Dim lQueueItems As New QueueItems()

        lQueueItems.IsNavigate = True
        lQueueItems.NavigationItem = NavigationItem
        lQueueItems.NavigationType = sType

        QueueList.Add(lQueueItems)

        'gcNavigationQueue.Add(lQueueItems)

        'HttpContext.Current.Session(clSettings.GetHostName() + "_IsNavigate") = True
        'HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationItem") = NavigationItem
        'HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationType") = sType 'FORM / DESKTOP
    End Sub

    Public Sub ReleaseQueue(Optional i As Integer = 0)

        Dim sProc As String = "clUI::ReleaseQueue"
        'Try

        If QueueList.Count > 0 Then
            QueueList.RemoveAt(i) '//25042019 tckt #2793:None of the checkboxes on the top of the forms to create another record when you save an leave open are working.  On JCI the from ac the create qt is not working...J
        End If

        'Dim sNav As String = ""
        'Dim oQI As QueueItems
        ''Dim oHI As clHistoryItem
        'Dim i As Integer
        ''add to ui object collection
        ''add to history
        'For i = Me.gcNavigationQueue.Count To 1 Step -1
        '    oQI = Me.gcNavigationQueue.Item(i)
        '    Dim sGuid As String = oQI.NavigationItem.GUID
        '    AddUIObject(sGuid, oQI.NavigationItem)
        '    Dim sTitle = oQI.NavigationItem.Title
        '    'If oQI.NavigationItem.GetType.ToString.ToUpper = "CLFORM" Then sTitle = oQI.NavigationItem.TitleForHistory
        '    'oHI = New clHistoryItem(oQI.Type, sGuid, sTitle, goHist.GetNextSeqNo.ToString)
        '    'goHist.Add(oHI)
        '    RequiresLoad(sGuid)
        'Next
        'Me.gcNavigationQueue.Clear()
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45101, sProc)
        '    End If
        'End Try

        'HttpContext.Current.Session(clSettings.GetHostName() + "_IsNavigate") = False
        'HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationItem") = Nothing
        'HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationType") = Nothing
    End Sub

    Public Function AddUIObject(ByVal sGUID As String, ByVal oUIObject As Object) As Boolean
        Dim sProc As String = "clUI::AddUIObject"
        'Try
        If gcUIObjects.Contains(sGUID) Then
                Return False
            Else
                gcUIObjects.Add(oUIObject, sGUID)
                Return True
            End If
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        '    Return False
        'End Try
    End Function

    Private Sub RequiresLoad(ByVal sGuid As String)
        If gcQueuedObjectsToLoad.Contains(sGuid) Then
        Else
            gcQueuedObjectsToLoad.Add(sGuid, sGuid)
        End If
    End Sub

    Public Function IsQueueLoaded() As Boolean
        Return QueueList.Count > 0
        'If HttpContext.Current.Session(clSettings.GetHostName() + "_IsNavigate") <> False AndAlso Not String.IsNullOrEmpty(HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationType")) Then
        '    Return True
        'End If
        'Return False

    End Function

    Public Function QueueCount() As Integer
        Return QueueList.Count
    End Function

    Public Function NavigateType(Optional i As Integer = 0) As String
        If QueueList.Count > 0 Then
            Return QueueList(i).NavigationType
        End If

        Return ""


        'If Not String.IsNullOrEmpty(HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationType")) Then
        '    Return HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationType").ToString()
        'End If
        'Return ""
    End Function

    Public Sub ReleaseAllFromQueue()
        If QueueList.Count > 0 Then
            QueueList.Clear()
        End If
    End Sub

    Public Function NavigateItem(Optional i As Integer = 0) As Object

        If QueueList.Count > 0 Then
            Return QueueList(i).NavigationItem
        End If

        Return Nothing

        'If HttpContext.Current.Session("NavigationItem") <> Nothing Then
        '    Return HttpContext.Current.Session("NavigationItem")
        'End If
        'Return Nothing
        'Return HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationItem")
    End Function

    Public Function NavigateSelectedItem(i As Integer) As Object

        If QueueList.Count > 0 Then
            Return QueueList(i).NavigationItem
        End If

        Return Nothing

        'If HttpContext.Current.Session("NavigationItem") <> Nothing Then
        '    Return HttpContext.Current.Session("NavigationItem")
        'End If
        'Return Nothing
        'Return HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationItem")
    End Function

    Public Sub OpenURLExternal(ByVal sUrl As String, Optional ByVal sWindowTitle As String = "Selltis", Optional ByVal sParams As String = "")

        If sUrl.ToLower().Contains("cus_") And HttpContext.Current.Session(clSettings.GetHostName() + "_SiteSettings") IsNot Nothing Then

            'get the public files folder and set the path

            Dim dt As System.Data.DataTable = New DataTable()

            dt = CType(HttpContext.Current.Session(clSettings.GetHostName() + "_SiteSettings"), System.Data.DataTable)

            Dim sSiteId As String = CType(dt.Rows(0)("SiteId"), String)

            Dim FileName As String = sUrl.Substring(sUrl.ToLower().IndexOf("cus_"))

            HttpContext.Current.Session(clSettings.GetHostName() + "_ExternalURLPath") = "../../PublicFiles/" & sSiteId & "/WebForms/" & FileName
        Else
            HttpContext.Current.Session(clSettings.GetHostName() + "_ExternalURLPath") = sUrl
        End If

        HttpContext.Current.Session(clSettings.GetHostName() + "_IsNavigate") = True
        HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationItem") = sUrl
        HttpContext.Current.Session(clSettings.GetHostName() + "_ExtUrlTitle") = sWindowTitle
        HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationType") = "OPENURLEXTERNAL" 'FORM / DESKTOP
        HttpContext.Current.Session(clSettings.GetHostName() + "_ExtUrlParms") = sParams
    End Sub

    Public Sub AddNDBFormToQueue(ByVal sType As String, ByVal NavigationItem As Object, ByVal NavigationValue As Object)

        HttpContext.Current.Session(clSettings.GetHostName() + "_IsNavigate") = True
        HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationItem") = NavigationItem
        HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationType") = sType 'FORM / DESKTOP
        HttpContext.Current.Session(clSettings.GetHostName() + "_NavigationValue") = NavigationValue
    End Sub


    Public Sub NewWorkareaMessage(ByVal par_sMessage As String,
              Optional ByVal par_iStyle As Integer = clC.SELL_MB_OK,
              Optional ByVal par_sTitle As String = "Selltis",
              Optional ByVal par_sButton1Label As String = "",
              Optional ByVal par_sButton2Label As String = "",
              Optional ByVal par_sButton3Label As String = "",
              Optional ByVal par_sInputDefaultValue As String = "",
              Optional ByVal par_sButton1Script As String = "",
              Optional ByVal par_sButton2Script As String = "",
              Optional ByVal par_sButton3Script As String = "",
              Optional ByRef par_doCallingObject As Object = Nothing,
              Optional ByVal par_doArray As clArray = Nothing,
              Optional ByVal par_s1 As String = "",
              Optional ByVal par_s2 As String = "",
              Optional ByVal par_s3 As String = "",
              Optional ByVal par_s4 As String = "",
              Optional ByVal par_s5 As String = "")
        goWAMess = New clWorkareaMessage(par_sMessage,
                                            par_iStyle,
                                            par_sTitle,
                                            par_sButton1Label,
                                            par_sButton2Label,
                                            par_sButton3Label,
                                            par_sInputDefaultValue,
                                            par_sButton1Script,
                                            par_sButton2Script,
                                            par_sButton3Script,
                                            par_doCallingObject,
                                            par_doArray,
                                            par_s1,
                                            par_s2,
                                            par_s3,
                                            par_s4,
                                            par_s5)

        HttpContext.Current.Session(clSettings.GetHostName() + "_NewWorkareaMessage") = goWAMess
    End Sub

    Public Function GetLastSelected(sWhat As String) As String
        Select Case sWhat
            Case "SELECTEDRECORDID"
                If HttpContext.Current.Session(clSettings.GetHostName() + "_SelectedRecordID") <> Nothing Then
                    Return HttpContext.Current.Session(clSettings.GetHostName() + "_SelectedRecordID").ToString()
                End If
            Case "LASTOPENDESKTOPGUID"
                Return ""
            Case Else
                Return ""
        End Select

        Return ""
    End Function

    Public Property ExecuteSendPopup() As Boolean   'S_B On Calling it will set true/false based on passing value
        Get
            Return gbExecuteSendPopup
        End Get
        Set(ByVal value As Boolean)
            gbExecuteSendPopup = value  'S_B true for open a popup in Form page.
        End Set
    End Property

End Class

Public Class QueueItems
    Public IsNavigate As Boolean
    Public NavigationItem As Object
    Public NavigationType As String
End Class
