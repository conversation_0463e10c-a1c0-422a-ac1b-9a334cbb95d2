﻿using Selltis.BusinessLogic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Selltis.MVC.Models
{
    public class ManageDesktops
    {
        public int DesktopListCount { get; set; }
        public bool IsForProductVisible { get; set; }
        public bool IsProductLabelVisible { get; set; }
        public bool IsOpenButtonEnable { get; set; }
        public bool IsOpenTipLabelVisible { get; set; }
        public string OpenLabelText { get; set; }
        public bool IsEditButtonEnable { get; set; }
        public bool IsDeleteButtonEnable { get; set; }
        public bool IsDuplicateButtonEnable { get; set; }
        public bool IsTransferInButtonEnable { get; set; }
        public bool IsTransferOutButtonEnable { get; set; }
        
        public string StaCreatedBy { get; set; }
        public string StaModeBy { get; set; }
        public string StaDesktopNode { get; set; }
        public bool IsShared { get; set; }

        public string NewlyCreatedOrEditedDesktopId { get; set; }
        public string SelectedDesktopId { get; set; }
        public string DuplicateCreatedDesktopId { get; set; }
        public string MessageBoxPurpose { get; set; }



        //hiden div properties
        public bool IsTextDetailsVisible { get; set; }
        public string TextDetails { get; set; }
        public string LBL_DetailsTipText { get; set; }
        public string TXT_DetailsBackColor { get; set; }
        public bool TXT_DetailsReadOnly { get; set; }


        public string DetailsTipText { get; set; }
        public bool TXT_TransferInNameVisible { get; set; }
        public string TransferInNameText { get; set; }
        public bool IsTransferInNameLabelVisible { get; set; }
        public bool IsDetailsTipImageVisible { get; set; }
        public bool IsDetailsTipLabelVisible { get; set; }
        public bool IsPermissionsWarningImageVisible { get; set; }
        public bool IsPermissionsWarningLabelVisible { get; set; }

        public bool IsTransferInProductLabelVisible { get; set; }
        public bool IsTransferInProductCmboVisible { get; set; }


        public bool BTN_TransferInImportVisible { get; set; }
        public bool LBL_TransferInImportVisible { get; set; }
        public bool CHK_TransferInSharedVisible { get; set; }
        public bool IsTransferInSharedEnable { get; set; }
        public string CHKTransferInSharedText { get; set; }

        public bool IsTransferInSharedChecked { get; set; }
        public bool LBL_TransferInModeVisible { get; set; }
        public bool IsTransferInModeLabelEnable { get; set; }
        public bool SEL_TransferInModeVisible { get; set; }
        public string SELTransferInImportRadiobuttons { get; set; }


        // Delete message box controlls

        public bool PNL_MessageBoxVisible { get; set; }
        public bool MessageBoxDisplay { get; set; }
        public bool BTN_MsgBox1Visible { get; set; }
        public bool BTN_MsgBox2Visible { get; set; }
        public bool BTN_MsgBox3Visible { get; set; }

        public string BTN_MsgBox1Text { get; set; }
        public string BTN_MsgBox2Text { get; set; }
        public string BTN_MsgBox3Text { get; set; }


        public string LBL_MsgBoxMessageText { get; set; }
        public string LBL_MsgBoxTitleText { get; set; }

        //Duplicate message box controls
        public string LBL_DuplText { get; set; }
        public string TXT_DuplName { get; set; }
        public bool CHK_DuplSharedChecked { get; set; }
        public bool CHK_DuplSharedEnabled { get; set; }
        public bool LBL_DuplProductVisible { get; set; }
        public bool CMB_DuplProductVisible { get; set; }
        public bool PNL_DuplicateVisible { get; set; }

        public bool CHK_DuplOverwriteExistingVisible { get; set; }
        public bool CHK_DuplOverwriteExistingChecked { get; set; }
        public bool LBL_DuplOverwriteExistingVisible { get; set; }       

        public IList<ListItems> CMB_DuplProductList { get; set; }

        public List<System.Web.Mvc.SelectListItem> FillUnderNodes { get; set; }
        public string CMB_DuplNodeSelectedValue { get; set; }

        public IList<ListItems> ListDesktops { get; set; }
        public IList<DesktopListItems> DesktopListItems { get; set; }
    //    public IList<DesktopNames> DesktopNames { get; set; }
    }

    public class ListItems
    {
        public string Value { get; set; }
        public string Text { get; set; }
        public string Index { get; set; }
        public string IsShared { get; set; }
        public string Type { get; set; }
        public string InvolvedFiles { get; set; }

    }

    //public class DesktopNames
    //{
    //    public string Text { get; set; }
    //}


    public class DesktopListItems
    {
        public string Value { get; set; }
        public string Text { get; set; }
        public string Type { get; set; }
        public int Index { get; set; }
        public string Description { get; set; }
        public string CreatedBy { get; set; }
        public string ModifiedBy { get; set; }
        public string IsShared { get; set; }
        public string DesktopNode { get; set; }
        public string DesktopNames { get; set; }

    }

    public class NewEditDesktop
    {
        //public HtmlString HtmlRaw { get; set; }
        //public NewEditDesktopGeneralInfo ListGeneralInfo { get; set; }
        //public NewEditDesktopLayoutInfo ListLayoutInfo { get; set; }
        //use this only for PowerBI reports
        public string WorkspaceId { get; set; }
        //use this only for PowerBI reports
        public string ReportId { get; set; }
        public string Product { get; set; }
        public bool BtnMetaVisible { get; set; }
        public bool BtnSaveAsEnabled { get; set; }
        public bool EdtNameEnabled { get; set; }
        public bool BtnOkEnabled { get; set; }
        public bool ChkSharedEnabled { get; set; }
        public string Vals { get; set; }
        public string[] aViewVals { get; set; }
        public string Mode { get; set; }
        public string PageID { get; set; }
        public string NameTrans { get; set; }
        public System.Data.DataTable dt1 { get; set; }
        public System.Data.DataTable dt2 { get; set; }
        public System.Data.DataTable dtTabs { get; set; }
        public clTable t1 { get; set; }
        public clTable t2 { get; set; }
        public clTable tTabs { get; set; }
        public string ParamsToDiaDeskVie { get; set; }
        public string sResultFromSubdialog { get; set; }
        public bool bBackFromSubdialog { get; set; }
        //
        public string EdtNameText { get; set; }
        public string DesktopType { get; set; }
        public string DesktopTypeSelectedIndex { get; set; }
        public bool ChkSharedChecked { get; set; }
        public string Creator { get; set; }
        public string CreatorID { get; set; }
        public bool LblModByVisible { get; set; }
        public bool StaModByVisible { get; set; }
        public string ModifiedBy { get; set; }
        public List<System.Web.Mvc.SelectListItem> FillWebClientButtons { get; set; }
        public List<System.Web.Mvc.SelectListItem> FillCMBDRS { get; set; }
        public List<System.Web.Mvc.SelectListItem> GetFRFControlList { get; set; }        
        public string CLKINFOImageUrl { get; set; }
        public bool CLKINFOVisible { get; set; }
        public string STAMESSAGEText { get; set; }
        public bool LBLProductEnabled { get; set; }
        public bool CMBProductEnabled { get; set; }
        public bool LBLProductVisible { get; set; }
        public bool CMBProductVisible { get; set; }
        public bool STAWCBUTTONEnabled { get; set; }
        public bool LBLWCBUTTONEnabled { get; set; }
        public bool CMBWCBUTTONEnabled { get; set; }
        public bool BTNWCBUTTONEnabled { get; set; }
        public string CMBWCBUTTONSelectedIndex { get; set; }
        public bool bSharedOnOpen { get; set; }
        public bool bShared { get; set; }
        public string sNameTrans { get; set; }

        //
        public bool ChkDrsEnabledChecked { get; set; }
        public string EdtCol2Width { get; set; }
        public string EdtCol1Width { get; set; }
        public string EdtHeight2 { get; set; }
        public string EdtHeight1 { get; set; }
        public bool ChkAdjustHeights1Checked { get; set; }
        public bool ChkAdjustHeights2Checked { get; set; }
        public List<System.Web.Mvc.SelectListItem> LST1 { get; set; }
        public List<System.Web.Mvc.SelectListItem> LST2 { get; set; }
        public List<System.Web.Mvc.SelectListItem> LSTTabs { get; set; }

        //managestate

        public bool PNLLayoutSubVisible { get; set; }
        public bool CMBDRSDefaultEnabled { get; set; }
        public string CMBDRSDefaultSelectedValue { get; set; }
        public bool LBLDRSDefaultEnabled { get; set; }
        public bool LBLDRSFromEnabled { get; set; }
        public bool TXTDRSFromEnabled { get; set; }
        public bool LBLDRSToEnabled { get; set; }
        public bool TXTDRSToEnabled { get; set; }
        public bool BTNLINEUP1Enabled { get; set; }
        public bool BTNLINEDOWN1Enabled { get; set; }
        public bool BTNLINEUP2Enabled { get; set; }
        public bool BTNLINEDOWN2Enabled { get; set; }
        public bool BTNLINEUP3Enabled { get; set; }
        public bool BTNLINEDOWN3Enabled { get; set; }
        public bool BTNEQUALHEIGHTSEnabled { get; set; }
        public bool BTNEDITVIEWEnabled { get; set; }
        public bool BTNREMOVEVIEWEnabled { get; set; }
        public bool BTNDUPLICATEVIEWEnabled { get; set; }
        public string STAHEIGHT1Text { get; set; }
        public int LST1Width { get; set; }
        public int LST1Height { get; set; }
        public bool LBLWCBUTTONVisible { get; set; }
        public bool CMBWCBUTTONVisible { get; set; }
        public bool BTNWCBUTTONVisible { get; set; }
        public bool IMGMINIVIEWSVisible { get; set; }
        public bool LBLViewsVisible { get; set; }
        public bool BTNCOL1Visible { get; set; }
        public bool EDTCOL1WIDTHVisible { get; set; }
        public bool STACOL1WIDTHVisible { get; set; }
        public bool BTNRefreshVisible { get; set; }
        public bool CHKADJUSTHEIGHTS1Visible { get; set; }
        public bool EDTHEIGHT1Visible { get; set; }
        public bool STAPERCENT1Visible { get; set; }
        public bool BTNRefresh2Visible { get; set; }
        public bool BTN1TO2Visible { get; set; }
        public bool BTN2TO1Visible { get; set; }
        public bool BTNCOL2Visible { get; set; }
        public bool EDTCOL2WIDTHVisible { get; set; }
        public bool STACOL2WIDTHVisible { get; set; }
        public bool CHKADJUSTHEIGHTS2Visible { get; set; }
        public bool LST2Visible { get; set; }
        public bool BTNLINEUP2Visible { get; set; }
        public bool BTNLINEDOWN2Visible { get; set; }
        public bool BTNMOVEDOWN1Visible { get; set; }
        public bool BTNMOVEUP1Visible { get; set; }
        public bool BTNMOVEDOWN2Visible { get; set; }
        public bool BTNMOVEUP2Visible { get; set; }
        public bool IMGTABVIEWSVisible { get; set; }
        public bool BTNTABSVisible { get; set; }
        public bool STAHEIGHT2Visible { get; set; }
        public bool EDTHEIGHT2Visible { get; set; }
        public bool STAPERCENT2Visible { get; set; }
        public bool LSTTabsVisible { get; set; }
        public bool BTNLINEUP3Visible { get; set; }
        public bool BTNLINEDOWN3Visible { get; set; }
        public bool BTNEQUALHEIGHTSVisible { get; set; }
        public bool LBLVIEWSPERMISSIONVisible { get; set; }
        public bool EDTCOL1WIDTHEnabled { get; set; }
        public bool STACOL1WIDTHEnabled { get; set; }
        public bool CHKADJUSTHEIGHTS1Enabled { get; set; }
        public bool EDTCOL2WIDTHEnabled { get; set; }
        public bool STACOL2WIDTHEnabled { get; set; }
        public bool CHKADJUSTHEIGHTS2Enabled { get; set; }
        public bool STAHEIGHT1Enabled { get; set; }
        public bool EDTHEIGHT1Enabled { get; set; }
        public bool STAPERCENT1Enabled { get; set; }
        public bool STAHEIGHT2Enabled { get; set; }
        public bool EDTHEIGHT2Enabled { get; set; }
        public bool STAPERCENT2Enabled { get; set; }
        public bool BTN1TO2Enabled { get; set; }
        public bool BTN2TO1Enabled { get; set; }
        public bool BTNMOVEDOWN1Enabled { get; set; }
        public bool BTNMOVEUP1Enabled { get; set; }
        public bool BTNMOVEDOWN2Enabled { get; set; }
        public bool BTNMOVEUP2Enabled { get; set; }
        public bool BTNINSERTVIEWEnabled { get; set; }
        public bool BTNAPPENDVIEWEnabled { get; set; }
        public string CMBFRFCONTROLSelectedValue { get; set; }
        public string AreaInFocus { get; set; }
        public string CMBProductSelectedValue { get; set; }
        public int EDTCOL1WIDTHText { get; set; }
        public int EDTCOL2WIDTHText { get; set; }
        public int EDTHEIGHT1Text { get; set; }
        public int EDTHEIGHT2Text { get; set; }
        public string COLLeftWidth { get; set; }
        public string COLUpDown1Width { get; set; }
        public string COLColumn1Width { get; set; }
        public string COLCol1Col2Width { get; set; }
        public string COLColumn2Width { get; set; }
        public string COLupDown2Width { get; set; }
        public bool STAHEIGHT1Visible { get; set; }
        public string TXTDRSFromText { get; set; }
        public string TXTDRSToText { get; set; }

        public MessageBoxForNewEdit messageBox { get; set; }

        //MessageBoxForNewEdit buttons 
        //public bool PNL_MessageBoxVisible { get; set; }
        //public bool MessageBoxDisplay { get; set; }
        //public bool BTN_MsgBox1Visible { get; set; }
        //public bool BTN_MsgBox2Visible { get; set; }
        //public bool BTN_MsgBox3Visible { get; set; }

        //public string BTN_MsgBox1Text { get; set; }
        //public string BTN_MsgBox2Text { get; set; }
        //public string BTN_MsgBox3Text { get; set; }

        public bool PNL_GeneralVisible { get; set; }
        public bool PNL_LayoutVisible { get; set; }

        //public string LBL_MsgBoxMessageText { get; set; }
        //public string LBL_MsgBoxTitleText { get; set; }
    }
    public class MessageBoxForNewEdit
    {
        public bool PNL_MessageBoxVisible { get; set; }
        public bool MessageBoxDisplay { get; set; }
        public bool BTN_MsgBox1Visible { get; set; }
        public bool BTN_MsgBox2Visible { get; set; }
        public bool BTN_MsgBox3Visible { get; set; }

        public string BTN_MsgBox1Text { get; set; }
        public string BTN_MsgBox2Text { get; set; }
        public string BTN_MsgBox3Text { get; set; }

        public bool PNL_GeneralVisible { get; set; }
        public bool PNL_LayoutVisible { get; set; }

        public string LBL_MsgBoxMessageText { get; set; }
        public string LBL_MsgBoxTitleText { get; set; }
    }

    public class MoveSelectListItem
    {
        public List<System.Web.Mvc.SelectListItem> FromLST { get; set; }
        public List<System.Web.Mvc.SelectListItem> ToLST { get; set; }
    }

    public class List1SelectedIndexChanged
    {
        public List<System.Web.Mvc.SelectListItem> LSTCOPY { get; set; }
        public string EDTVIEWNAMEText { get; set; }
    }

    //public class NewEditDesktopGeneralInfo
    //{
    //    public string EdtNameText { get; set; }
    //    public bool ChkSharedChecked { get; set; }
    //    public string Creator { get; set; }
    //    public string CreatorID { get; set; }
    //    public bool LblModByVisible { get; set; }
    //    public bool StaModByVisible { get; set; }
    //    public string ModifiedBy { get; set; }
    //    public List<System.Web.Mvc.SelectListItem> FillWebClientButtons { get; set; }
    //    public string CLKINFOImageUrl { get; set; }
    //    public bool CLKINFOVisible { get; set; }
    //    public string STAMESSAGEText { get; set; }
    //    public bool LBLProductEnabled { get; set; }
    //    public bool CMBProductEnabled { get; set; }
    //    public bool LBLProductVisible { get; set; }
    //    public bool CMBProductVisible { get; set; }
    //    public bool STAWCBUTTONEnabled { get; set; }
    //    public bool LBLWCBUTTONEnabled { get; set; }
    //    public bool CMBWCBUTTONEnabled { get; set; }
    //    public bool BTNWCBUTTONEnabled { get; set; }
    //    public string CMBWCBUTTONSelectedIndex { get; set; }
    //    public bool bSharedOnOpen { get; set; }
    //}

    //public class NewEditDesktopLayoutInfo
    //{
    //    public bool ChkDrsEnabledChecked { get; set; }
    //    public string EdtCol2Width { get; set; }
    //    public string EdtCol1Width { get; set; }
    //    public string EdtHeight2 { get; set; }
    //    public string EdtHeight1 { get; set; }
    //    public bool ChkAdjustHeights1Checked { get; set; }
    //    public bool ChkAdjustHeights2Checked { get; set; }

    //    //managestate
        
    //    public bool PNLLayoutSubVisible { get; set; }
    //    public bool CMBDRSDefaultEnabled { get; set; }
    //    public string CMBDRSDefaultSelectedValue { get; set; }        
    //    public bool LBLDRSDefaultEnabled { get; set; }
    //    public bool LBLDRSFromEnabled { get; set; }
    //    public bool TXTDRSFromEnabled { get; set; }
    //    public bool LBLDRSToEnabled { get; set; }
    //    public bool TXTDRSToEnabled { get; set; }
    //    public bool BTNLINEUP1Enabled { get; set; }
    //    public bool BTNLINEDOWN1Enabled { get; set; }
    //    public bool BTNLINEUP2Enabled { get; set; }
    //    public bool BTNLINEDOWN2Enabled { get; set; }
    //    public bool BTNLINEUP3Enabled { get; set; }
    //    public bool BTNLINEDOWN3Enabled { get; set; }        
    //    public bool BTNEQUALHEIGHTSEnabled { get; set; }
    //    public bool BTNEDITVIEWEnabled { get; set; }
    //    public bool BTNREMOVEVIEWEnabled { get; set; }
    //    public bool BTNDUPLICATEVIEWEnabled { get; set; }
    //    public string STAHEIGHT1Text { get; set; }
    //    public int LST1Width { get; set; }
    //    public int LST1Height { get; set; }
    //    public bool LBLWCBUTTONVisible { get; set; }
    //    public bool CMBWCBUTTONVisible { get; set; }
    //    public bool BTNWCBUTTONVisible { get; set; }
    //    public bool IMGMINIVIEWSVisible { get; set; }
    //    public bool LBLViewsVisible { get; set; }
    //    public bool BTNCOL1Visible { get; set; }
    //    public bool EDTCOL1WIDTHVisible { get; set; }
    //    public bool STACOL1WIDTHVisible { get; set; }
    //    public bool BTNRefreshVisible { get; set; }
    //    public bool CHKADJUSTHEIGHTS1Visible { get; set; }
    //    public bool EDTHEIGHT1Visible { get; set; }
    //    public bool STAPERCENT1Visible { get; set; }
    //    public bool BTNRefresh2Visible { get; set; }
    //    public bool BTN1TO2Visible { get; set; }
    //    public bool BTN2TO1Visible { get; set; }
    //    public bool BTNCOL2Visible { get; set; }
    //    public bool EDTCOL2WIDTHVisible { get; set; }
    //    public bool STACOL2WIDTHVisible { get; set; }
    //    public bool CHKADJUSTHEIGHTS2Visible { get; set; }
    //    public bool LST2Visible { get; set; }
    //    public bool BTNLINEUP2Visible { get; set; }
    //    public bool BTNLINEDOWN2Visible { get; set; }
    //    public bool BTNMOVEDOWN1Visible { get; set; }
    //    public bool BTNMOVEUP1Visible { get; set; }
    //    public bool BTNMOVEDOWN2Visible { get; set; }
    //    public bool BTNMOVEUP2Visible { get; set; }
    //    public bool IMGTABVIEWSVisible  { get; set; }
    //    public bool BTNTABSVisible { get; set; }
    //    public bool STAHEIGHT2Visible { get; set; }
    //    public bool EDTHEIGHT2Visible { get; set; }
    //    public bool STAPERCENT2Visible  { get; set; }
    //    public bool LSTTabsVisible { get; set; }
    //    public bool BTNLINEUP3Visible { get; set; }
    //    public bool BTNLINEDOWN3Visible { get; set; }
    //    public bool BTNEQUALHEIGHTSVisible { get; set; }
    //    public bool LBLVIEWSPERMISSIONVisible { get; set; }
    //    public bool EDTCOL1WIDTHEnabled { get; set; }
    //    public bool STACOL1WIDTHEnabled { get; set; }
    //    public bool CHKADJUSTHEIGHTS1Enabled { get; set; }
    //    public bool EDTCOL2WIDTHEnabled { get; set; }
    //    public bool STACOL2WIDTHEnabled { get; set; }
    //    public bool CHKADJUSTHEIGHTS2Enabled { get; set; }
    //    public bool STAHEIGHT1Enabled { get; set; }
    //    public bool EDTHEIGHT1Enabled { get; set; }
    //    public bool STAPERCENT1Enabled { get; set; }
    //    public bool STAHEIGHT2Enabled { get; set; }
    //    public bool EDTHEIGHT2Enabled { get; set; }
    //    public bool STAPERCENT2Enabled { get; set; }
    //    public bool BTN1TO2Enabled { get; set; }
    //    public bool BTN2TO1Enabled { get; set; }
    //    public bool BTNMOVEDOWN1Enabled { get; set; }
    //    public bool BTNMOVEUP1Enabled { get; set; }
    //    public bool BTNMOVEDOWN2Enabled { get; set; }
    //    public bool BTNMOVEUP2Enabled { get; set; }
    //    public bool BTNINSERTVIEWEnabled { get; set; }
    //    public bool BTNAPPENDVIEWEnabled { get; set; }
    //    public string CMBFRFCONTROLSelectedValue { get; set; }
    //    public bool bSharedOnOpen { get; set; }        
    //    public string AreaInFocus { get; set; }
    //    public string CMBProductSelectedValue { get; set; }
    //    public int EDTCOL1WIDTHText { get; set; }
    //    public int EDTHEIGHT1Text { get; set; }
    //    public string COLLeftWidth { get; set; }
    //    public string COLUpDown1Width { get; set; }
    //    public string COLColumn1Width { get; set; }
    //    public string COLCol1Col2Width { get; set; }
    //    public string COLColumn2Width { get; set; }
    //    public string COLupDown2Width { get; set; }
    //    public bool STAHEIGHT1Visible { get; set; }
    //    public string TXTDRSFromText { get; set; }
    //    public string TXTDRSToText { get; set; }
    //}

    public class InsertEditView
    {
        public string sParams {get;set;}
        public string sProduct {get;set;}
        public string sSection {get;set;}
        public string sDesktopID {get;set;}
        public string sFileName {get;set;}
        public string sDefaults {get;set;}
        public string sViewID { get; set; }
        public string EDTVIEWNAMEText {get;set;}
        public string sViewVals {get;set;}
        public string SELTypeSelectedValue {get;set;}
        public string LSTCOPYSelectedValue {get;set;}
        public string LST1SelectedValue {get;set;}
        public string SELSORTSelectedValue {get;set;}
        public string LBLCOPYText { get; set; }
        public string par_sMode { get; set; }
        public string sCOLUMN { get; set; }
        public string sROWNO { get; set; }
        public string sTitle { get; set; }
        public string sPERCHEIGHT { get; set; }
        public string sFileNameOrig { get; set; }
        public string sTABLABEL { get; set; }
        public bool SELVIEWTYPEEnabled {get;set;} 
        public bool SELVIEWTYPELISTEnabled {get;set;} 
        public bool SELVIEWTYPECALDAYEnabled {get;set;} 
        public bool SELVIEWTYPECALWEEKEnabled {get;set;} 
        public bool SELVIEWTYPECALMONTHEnabled {get;set;} 
        public bool SELVIEWTYPECHARTEnabled {get;set;} 
        public bool LBLProductVisible {get;set;} 
        public bool CMBProductVisible {get;set;} 
        public bool LBLCOPYVisible {get;set;} 
        public bool LSTCOPYVisible {get;set;} 
        public bool LBL_SORTVisible {get;set;} 
        public bool SEL_SORTVisible {get;set;} 
        public bool LBLSORTVisible {get;set;} 
        public bool SELSORTVisible {get;set;}
        public int iTotalViews { get; set; }
        public List<System.Web.Mvc.SelectListItem> List1 { get; set; }
        public List<System.Web.Mvc.SelectListItem> LSTCOPY { get; set; }
    }
}