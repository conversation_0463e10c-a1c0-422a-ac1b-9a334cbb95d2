﻿using System;
using System.Data;


//Owner: MI was RH
//MI 6/21/13 Renamed clProject_Nation to giNation.
//MI 6/21/13 added default value to iNation: 3. 
//MI 8/13/09 Took ownership from RH. Fixed VS2008 warnings.
//MI 4/11/11 IsPcLinkSupported() created.

using System.Web;


namespace Selltis.BusinessLogic
{
	public class clProject : clVar
	{
		//MI 1/14/09 added Public sProduct

		private clProject goP;
		private clMetaData goMeta;
		private clTransform goTR;
		public clData goData;
		private clError goErr;
		private clLog goLog;
		//Dim goDef As clDefaults
		//Dim goScr As clScrMng
		//Dim goNumMask As clNumMask
		//Dim goUI As clUI        '*** MI 8/22/06
		private clPerm goPerm; //*** MI 3/14/07

		//Public oData As New clData
		//Public aSelectedItems As New clArray

		//From clLog declarations 
		public bool clLog_bLogAvailable = true;

		public int giNation = 3; //MI 6/21/13 added default value: 3.

		//From clProject Declarations
		public int ctStringLimit = 65000; //Maximum size for any type of string (not windev
		//maximum, but onstring functions limitations)
		public long glID;
		public string gsVersion;
		public string gsProgDir; //Program directory
		public string gsIniFile; //Ini file path
		public string gsFilesDir; //Directory containing data files
		public string gsDB; //Database 'directory only (ex: 'SellDB')
		public string gsDatabaseName; //Name of the current database
		public string gsLang; //Language
		public string gsUserID; //Current user's 4-character code (ex: 'MAR')
		public string sUserName;
		public string gsAnaPath;
		public int giMultitaskRun; //counter for goP:Multitask() procedure
		//For the directory selector window [JL]
		public string gsFolderByDefault; //Name of the folder when opening select-dir.
		public int giNumEventFolder; //Used on opening of select-dir to point to the called folder
		public bool bMetaAvailable; //Used to know if we can call the meta object in clData
		//Global from clProject
		public string sRunMode; //can be SELL_MODE_MAIN, SELL_MODE_API, ...
		public string sDataBase;
		public string sUser;
		public string sPassWord;
		public string sAction;
		public string sAComp;
		public long lNGPHandle;
		public string sPath = HttpContext.Current.Server.MapPath("~");
		public string sProduct;
		public DataTable dtProducts = new DataTable();
		//Local
		private long lMainWindowHandle; //Used to store thge main window handle for Temproay records management
		private long lLastSSync; //JL Used to memorize last SellSync call (TimeToInteger)
		private string sUserTID; //User Text ID (T30) used to read/write user settings/metadata
		private string sUserSort; //User sort order (used to optimize the creation of
		//all links on user: it's not necessary this way to read 
		//the user record each and everytime)
		private string sUserLogon; //Saved in rder to be able to logon automatically later
		private string sUserLogonID; //ID of the login record in XU table
		private string sUserPass; //Saved in rder to be able to logon automatically later
		private string sUserVBPassword; //Saved under VB encryption in order to allow logon by sellsend
		private int iUserAuthorState; //User status: Author
		private bool bUserIsAdmin; //User status: Admin
		private bool bUserIsCustomerAdmin; //User status: Customer admin
		private long uCurrentUAID; //Used by FH in UAID methods
		private int iNbHourglass; //Nb of activated Hourglass (used by JL)
		private long lTraceMode; //Trace mode (SELLTRACE_OFF,SELLTRACE_USER,SELLTRACE_ALL)
		private bool bCancelEvent; //Global flag to authorize or not any process
		private int iLangPosition; //Used by MI in :LangFirst(), :LangNext()
		private string sUserLocation; //Current user location
		//	gsWindow is string						'Used by JL, added 3/20/2002
		private long lHNDHourglass;
		private string sUserPermissions; //Ini string with user permissions


		public string GetVersion(string par_sName = "SelltisSales")
		{
			//PURPOSE:
			//		Gets version from version.txt located in root directory.
			//PARAMETERS:
			//		Optional par_sName product name. E.g.:
			//            SelltisSales, PCLink, SelltisMobile, SelltisIntegrationSuite
			//RETURNS:
			//		Version string or 'Unknown'


			string sProc = "clProject::GetVersion";
			string sResult = "Unknown";

			try
			{

				string sVersion = System.IO.File.ReadAllText(sPath + "\\version.txt");
				sResult = goTR.StrRead(sVersion, par_sName, "Unknown");

			}
			catch (Exception ex)
			{
				sResult = "Unknown";
			}

			return sResult;

		}

		public long GetVersionAsLong(string par_sName = "SelltisSales")
		{
			//MI 9/14/09 created.
			//PURPOSE:
			//		Gets version as long integer from version.txt located in root directory.
			//PARAMETERS:
			//		Optional par_sName product name
			//            SalesSelltis, PCLink, OutlookAddin
			//RETURNS:
			//		Version as long with 0s padded like this:
			//       mmmsssbbbbbb
			//       where m=main version number, s=sub-version number and b=build
			//       5.5.151 becomes 005005000151
			string sProc = "clProject::GetVersionAsLong";
			string sVersion = null;
			long lResult = 0;
			string s1 = null; //main version
			string s2 = null; //sub-version
			string s3 = null; //build
			string sTemp = null;

			try
			{
				sVersion = GetVersion(par_sName);
				s1 = goTR.ExtractString(sVersion, 1, ".");
				if (s1 == "" || s1[0] == clC.EOT)
				{
					lResult = 0;
					return lResult;
				}
				s2 = goTR.ExtractString(sVersion, 2, ".");
				if (s2 == "" || s2[0] == clC.EOT)
				{
					s2 = "0";
				}
				s3 = goTR.ExtractString(sVersion, 3, ".");
				if (s3 == "" || s3[0] == clC.EOT)
				{
					s3 = "0";
				}
				//Derive the number
				s1 = goTR.Pad(s1, 3, "0", "L", true, "L");
				s2 = goTR.Pad(s2, 3, "0", "L", true, "L");
				s3 = goTR.Pad(s3, 6, "0", "L", true, "L");
				sTemp = s1 + s2 + s3;
				lResult = long.Parse(sTemp);

			}
			catch (Exception ex)
			{
				return lResult;
			}

			return lResult;

		}

		public string TestVersion(string par_sVersion1, string par_sVersion2)
		{

			//PURPOSE:
			//		Tests whether second version string provided is greater, less, or the same as the first.
			//PARAMETERS:
			//		par_sVersion1: First version (baseline) xx.xx.xxx format
			//		par_sVersion2: Second version  xx.xx.xxx format

			//RETURNS:
			//		String: +, -, or =, or - on error

			try
			{

				int ia1 = 0;
				int ia2 = 0;
				int ia3 = 0;
				int ib1 = 0;
				int ib2 = 0;
				int ib3 = 0;

				ia1 = int.Parse(goTR.ExtractString(par_sVersion1, 1, "."));
				ia2 = int.Parse(goTR.ExtractString(par_sVersion1, 2, "."));
				ia3 = int.Parse(goTR.ExtractString(par_sVersion1, 3, "."));

				ib1 = int.Parse(goTR.ExtractString(par_sVersion2, 1, "."));
				ib2 = int.Parse(goTR.ExtractString(par_sVersion2, 2, "."));
				ib3 = int.Parse(goTR.ExtractString(par_sVersion2, 3, "."));

				if (ia1 > ib1)
				{
					return "-";
				}
				if (ia1 < ib1)
				{
					return "+";
				}

				if (ia2 > ib2)
				{
					return "-";
				}
				if (ia2 < ib2)
				{
					return "+";
				}

				if (ia3 > ib3)
				{
					return "-";
				}
				if (ia3 < ib3)
				{
					return "+";
				}

				return "=";
			}
			catch (Exception ex)
			{

				return "-";

			}


		}


		public string AddAlert(string par_sMessage, string par_sType, string par_sExecute, string par_sIcon = "", bool par_bMsgBox = false, string par_sMsgType = "YESNO")
		{
			//DEPRECATES
			//---------
			//SET ERROR
			//---------
			//CS PORTED as placeholder (from w_proc)

			//PURPOSE:
			//		Create a new alert (ALT_) record in metadata. Alerts are processed by SellAlrt.exe
			//		and its WALERTS window. Metadata is written using an add rowset. See notes within
			//		the code below.
			//PARAMETERS:
			//		par_sMessage: Text to display in the WALERTS window or a messagebox.
			//		par_sType: Alert type. Supported values:
			//			OPENRECORD		'Record TID
			//			OPENDESKTOP		'Page ID of the desktop to open
			//			PROGRAM			'Full path of the program to run (<ProgramDir> and <DBDir>
			//							'keywords supported
			//			DOCUMENT		'Full path of the document (<ProgramDir and <DBDir>
			//							'supported) or URL
			//		par_sExecute: ID of the record, page ID of desktop, path of the program or document,
			//			or the URL to run/open. See par_sType for supported alert types.
			//		par_sIcon: Optional: Icon file name without path.
			//			Evaluated first from <DBDir>\Icons. If not there, from \<ProgramDir>\Icons. 
			//		par_bMsgBox: Display the alert as an 'above all' message box instead of in the
			//			WALERTS window. The Alerts button in SellNGP doesn't change if this property is True.
			//		par_sMsgType: Message type (ignored if par_bMsgBox <> 1). Supported values:
			//			OK				'OK button only
			//			YESNO			'Yes and No buttons (default)
			//			'LIST			'Currently not supported because LIST messages allow specifying
			//							'automator actions. For now we don't want to deal with
			//							'running automator actions outside of the automator context
			//RETURNS:
			//		String: Page ID of the ALT_ record created or "" if the record couldn't be created (with SetError).
			//EXAMPLE:
			return ""; //cs remove

			//sProc is string = FenEnExecution()+".AddAlert"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//sString is string
			//sType is string = par_sType
			//sExecute is string = par_sExecute
			//sID is string
			//sCleUnik is string
			//sWork is string

			//goLog:  SetError()

			//        'Validate Type, Execute parameters
			//        SWITCH(Upper(sType))
			//	CASE "OPENRECORD"
			//		If not goTr:IsTID(sExecute) Then
			//goLog:      SetError(10129, sProc, MessTraduit(2001), "", sExecute)
			//            ' Invalid ID. Please contact your system administrator.
			//            '
			//            'ID expected: '[1]'
			//            'ID passed: '[2]'
			//            '2001: An alert can't be created because the record ID is not valid: '[1]'.
			//            '
			//            'Please contact your Selltis administrator.
			//            RESULT("")
			//            End
			//	CASE "OPENDESKTOP"
			//		If not goTr:IsPageID(sExecute) THEN
			//			goLog:SetError(30002, sProc, goTr:MessComplete(MessTraduit(2000),sExecute), sExecute, sProc)
			//                ' Metadata object ID is not valid: '[1]'. Please contact Selltis support.
			//                '
			//                '([2])
			//                '2000:An alert can't be created because the desktop ID is not valid: '[1]'.
			//                '
			//                'Please contact your Selltis administrator.
			//                RESULT("")
			//                End
			//	CASE "PROGRAM", "DOCUMENT"
			//                'Supported
			//	OTHER CASE
			//goLog:          SetError(30033, sProc, "", sType, sProc)
			//                ' Unsupported alert type: '[1]'
			//                '
			//                'Please contact your Selltis administrator.
			//                RESULT("")
			//                End

			//                'Write parameters for the memo
			//goTr:           StrWrite(sString, "US_NAME", "Alert")
			//goTr:           StrWrite(sString, "MESSAGE", par_sMessage)
			//goTr:           StrWrite(sString, "TYPE", sType)
			//goTr:           StrWrite(sString, "EXECUTE", sExecute)
			//                If par_sIcon <> "" Then
			//goTr:               StrWrite(sString, "ICON", par_sIcon)
			//                    End
			//                    If par_bMsgBox Then
			//goTr:                   StrWrite(sString, "MSGBOX", "1")
			//goTr:                   StrWrite(sString, "MSGTYPE", par_sMsgType)
			//                        End

			//                        'Use an add rowset to write the _META record instead of
			//                        'goMeta:PageWrite() so that we can get the CLEUNIK value
			//                        'and generate a Page value from it. Add rowsets create
			//                        'a temporary record, which we need to be able to access
			//                        'the cleunik value.
			//                        'Using CLEUNIK value in Page ensures that the order of records
			//                        'on the Section+Page key is 100% accurate and not clock-dependent
			//                        '(standard page IDs are clock-dependent). 

			//doNew is object dynamic = new clRowSet("_META",2)
			//doNew:                  SetFieldVal("MMO_MemoValue", sString)
			//doNew:SetFieldVal("TXT_Section",goP:GetUserTID())
			//sCleUnik = NumToString(doNew:GetFieldVal("MDCLEUNIK",SELL_SYSTEM))
			//                        sWork = "ALT_" + Repete("0", 30 - Length(sCleUnik)) + sCleUnik
			//doNew:                  SetFieldVal("TXT_Page", sWork)
			//doNew:                  SetFieldVal("TXT_MetaType", "ALT")
			//doNew:                  SetFieldVal("TXT_TextValue", "Alert")
			//doNew:                  SetFieldVal("SI__ShareState", SELL_LOCAL, 2)

			//                        sID = doNew : Commit()
			//                        If sID = "" Then
			//                            'Pass the error to the calling code
			//                            'Exit the script
			//                            delete(doNew)
			//                            Result("")
			//                        Else
			//                            delete(doNew)
			//                            RESULT(sID)
			//                            End
		}


		public bool CreateIconsDir()
		{
			//CS PORTED as placeholder

			//---------
			//SET ERROR
			//---------
			//AUTHOR: MI 1/26/04	1/26/04
			//PURPOSE:
			//		Create <DBDir>Icons\ directory if necessary.
			//PARAMETERS:
			//		None
			//RETURNS:
			//		True/False. If False, an error must be reset by the calling code.

			//For debugging the order of calls.
			string sProc = "clProject::CreateIconsDir";
			//IF gbWriteLog THEN oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//CS: commented from here to end.
			//        Dim sDefaultDir As String

			//goLog:  SetError()

			//        sDefaultDir = goData : GetDBPath(+"Icons\")
			//        fDir(infoexe("Nom"))    'To make sure the parent dir is unlocked
			//        fMakeDir(sDefaultDir)       'If the dir is already there, fMakeDir will not do anything
			//        If FDir(sDefaultDir) = "" Then
			//	goLog:SetError(30000, sProc, goTr:MessComplete(MessTranslate(5104),sDefaultDir), sDefaultDir, sProc)
			//            '5104:Can't create the icons directory '[1]'.
			//            '
			//            'Ask your network administrator to grant you adequate write permissions.
			//            fDir(infoexe("Nom"))    'To make sure the parent dir is unlocked
			//            RESULT(False)
			//            End

			return true;
		}
		public void Debug(string par_sMessage, string par_sUserCode = "", string par_sProc = "")
		{
			//CS PORTED as placeholder (from w_proc)
			//DEPRECATED

			//PURPOSE:
			//		Display a debug info message box. The syntax is the same as for TraceLine().
			//		This fonction will display a message box only if the property
			//		'DEBUGSCRIPTS' in POP_PERSONAL_OPTIONS is set to '1'.
			//		Use Debug() when you want to interrupt the execution of the script between
			//		discrete sections of code. To trace the execution of scripts without interrupting
			//		them, use TraceLine() instead.
			//PARAMETERS:
			//		par_sMessage: Text to display.
			//		par_sUserCode: User code. If specified, the message box will display only for
			//			the user who has the specified user code if the DEBUGSCRIPTS flag is on (see
			//			above). For example, if you are logging on as JOHNDOE and your User Code
			//			is JHD, this parameter should be 'JHD'.
			//		par_sProc: Name of the procedure in which this function is called.
			//			In scripts, this name is in the variable sProc. 
			//EXAMPLE:
			//		Debug("My diagnostic message","JHD",sProc)
			//RETURNS:
			//		Nothing.

			//sProc is string=FenEnExecution()+".Debug"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//goMsgBox: Debug(par_sMessage, par_sUserCode, par_sProc)
		}
		public void Definitions()
		{
			//CS PORTED (actually just copy and change comment character)

			//==================================================================
			//STANDARD CODE MODULE HEADER:
			//==================================================================

			//OWNER: [Owner's initials]  [Date of last update]
			//[Other ownership comments]
			//
			//PURPOSE:
			//		[Purpose of the class, procedure, method, window]

			//==================================================================
			//STANDARD PROCEDURE HEADER:
			//==================================================================

			//PURPOSE:
			//		[Purpose of the class, procedure, method, window]
			//PARAMETERS:
			//		par_[Parameter1]: [Description of how and why the parameter is used]
			//		par_[Parametern]: ...
			//RETURNS:
			//		[Data type and description of returned data]
			//HOW IT WORKS:
			//		[Short explanation of how to use the code module]
			//EXAMPLE:
			//		[At least one example of how to call the procedure and what
			//		the procedure returns]                    



			//===========================================
			//GENERAL CODING STANDARDS
			//		Document all changes and their purpose in each window and
			//		procedure. For details, see project initialization code.
			//To do�s:
			//		Precede your to do�s in code with '==>. 
			//Debug:
			//		Precede all debug code with a line 'DEBUG. End all
			//		debug code sections with 'END DEBUG.
			//Internationalization:
			//		Use MessTranslate() for all strings. Start writing
			//		a message by pressing Ctrl-T. For messages that combine
			//		strings and variables, use clTransform:MessComplete()
			//Initializing controls:
			//		Initialize controls in window opening code instead
			//		of the control�s own opening code whenever possible.
			//Message Boxes:
			//		Use clMsgBox() class, Display method for dialog boxes
			//		when needed (e.g. OK/Cancel/Help dialog with a �?� icon).
			//Help files:
			//		To invoke a help file, do not use WHelp() function
			//		directly; instead, use goP:WHelp() method (in the
			//		clProject class). This method works exactly like
			//		WinDev's WHelp() for now, but allows us to support
			//		alternative help systems such as HTMLHelp in the future.
			//Browsing a HyperFile rowset:
			//		'Read the first record (of the file or of a filter)
			//		IF goData:HReadFirst("_META","_IDKey") THEN
			//			'Record was read correctly, we can process it
			//			LOOP
			//				'Process current record (here, just a trace)
			//				Trace(_META.TXT_TextValue)
			//				'Then read the next record, and exit the loop if not found
			//				IF not goData:hReadNext("_META","_IDKey") THEN BREAK
			//			END
			//		ELSE
			//			'No record was found, write a special processes (message...)
			//			'Here just a trace
			//			Trace("No record")
			//		END
			//
			//Finding records in a HyperFile rowset:
			//		'After HReadSeek(), do not test IF h.Found, but test:
			//		if goData:HReadSeek(...) then
			//			'Process...
			//		else
			//			'Process...
			//		end

			//===========================================
			//CONSTANTS
			//Do not use constant declarations in project init code or global
			//procs, only in class methods or SELLTIS.WL file.
			//Add all constants to SELLTIS.WL file and 'mit your additions
			//and changes to the project leader.


			//===========================================
			//NAMING OF VARIABLES, CLASSES, OBJECTS
			//
			//Syntax:	<scope><Data type><Name> in proper case
			//			<scope> and <Data type>: see table below
			//
			//Example:
			//		giCounter (global int named �Counter�)
			//
			//<scope>:		
			//		g		Global to project or public in classes
			//		gw		Global to window (variables only)
			//		<none>	Local or private, protected in classes
			//
			//<Data type>:		
			//		-------	-----------	-----------------------------
			//		Prefix	VB equiv.	Type
			//		-------	-----------	-----------------------------
			//		b		bln			Boolean
			//		i		int			Int
			//		u					Unsigned Int
			//		l		lng			Long Int
			//		si					Short Int
			//		r		sgl, f		Real
			//		t					Turbo real
			//		d		dbl, p		Double real
			//		c		curr, c		Currency
			//		chr		byt, t		Character
			//		fs					Fixed string on
			//		as					Ascii string on
			//		ps					Pascal string on
			//		s		str			String
			//		v		var			Variant (only in parameters)
			//		a		arr			Array
			//		da					Dynamic array
			//		st					Structure
			//		con					Constant
			//		cl		cls			Class
			//		o		obj			Object
			//		do					Dynamic object
			//
			//Examples:
			//		gsMyString (global string �MyString�)
			//		gwiAmount (window-global integer �Amount�)
			//		gdoOutlook (global dynamic object �Outlook�)


			//===========================================
			//OOP NAMING CONVENTIONS
			//
			//Prepend lowercase �cl� to all class names.
			//When naming objects, use the following syntax:
			//		<scope>o<Class name minus �cl� prefix>[_<context.]
			//
			//Examples:
			//		oView_Contacts5		(local clView object for 5th Contacts View)
			//		goDesktop			(clDesktop)
			//		goproject			(clProject)
			//
			//Name inherited classes so the name of the parent class is clear,
			//for example cParentClass_DerivedClass.
			//No prefix/suffix for class methods or members (properties).
			//Prepend lowercase o to local object names, go to global object names.
			//Examples: goHF, oRep. 


			//============================================
			//NAMING OF FUNCTIONS AND PROCEDURES
			//
			//Syntax:
			//		<scope><Name>(<Paras>) in proper case
			//		<scope>: g for global, gw for global to window, <none> for local
			//		<Name>: should start with a verb; can be a question (proper case)
			//		<Paras> inside procedures: par_<Data Type><Name> in proper case.
			//		For a list of <DataType>�s, see the table above.
			//
			//Examples:
			//		PROCEDURE gwCalcDate(par_sDate1, ... par_sDate2)
			//			'(window-global procedure);
			//		PROCEDURE gIsBackedUp(par_sFileName, ... par_lRecNum)
			//			'(project-global procedure)
			//
			//Because parameters are referenced by reference, not by value,
			//declare a local variable at the opening of the procedure and assign
			//it the value of the parameter. For example:
			//
			//		PROCEDURE gIsBackedUp(par_sFileName, ... par_lRecNum)
			//			'(project-global procedure)
			//  	sFile is string = par_sFileName
			//		lRec is long int = par_lRecNum

			//If you want to explicitly change the value of the variable passed
			//to the called procedure as a parameter, you can make an assignment
			//directly on the par_ parameter. Be careful to avoid introducing bugs
			//this way.


			//===========================================
			//WINDOW NAMING
			//
			//Syntax:
			//		W<NAME> in uppercase only, under 8 characters
			//		<NAME>: Typically, name of the file, e.g. �CONTACT� or �CN�,
			//		appended with the description of the purpose of the window

			//Examples:
			//		WCONTACT (Contact form window)
			//		WCNSEL (Selection dialog called from Contact form)


			//===========================================
			//NAMING CONTROLS 
			//
			//Syntax:
			//		<TYPE>_<NAME> in uppercase only
			//Table column syntax:
			//		<TYPE>_<COLNAME>_<TBLNAME> in uppercase only
			//
			//		<TYPE>: Type of control - 3 letters plus underscore (see below)
			//		<NAME>: Descriptive name of the control
			//		<COLNAME>: Table column name
			//		<TBLNAME>: Table name without the �TBL_� prefix
			//
			//Examples:
			//		EDT_NAMELAST (�NameLast� edit control)
			//		TBL_01 (�01� table control)
			//		COL_FAX_01 (�Fax� column of �01� table)
			//
			//Supported <TYPE>s:		
			//      -------	-----------	--------------------------------
			//		Prefix	VB equiv.	Type
			//		-------	-----------	--------------------------------
			//		STA_	lbl			Static
			//		EDT_	txt, msk	Edit
			//		DTE_				Date
			//		TME_				Regular time
			//		SEL_	opt			Radio button
			//		CHK_	chk			Check box
			//		LST_	lst			Listbox
			//		CMB_	cbo			Combo box
			//		BTN_	cmd			Button
			//		IMG_				Image
			//		CLK_				Click area
			//		SCR_				Scroll bar
			//		TBL_				Table
			//		COL_xx_zz			Table column
			//							(xx: column name,
			//							zz: table name without �TBL_� prefix)
			//		TAB_				Tab
			//		OLE_				OLE
			//		VBX_				VBX
			//		ACX_				ActiveX
			//		TRE_				Tree view
			//		SPN_				Spin
			//		SLI_				Slider
			//		SHA_				Soft shade


			//===========================================
			//ANALYSIS NAMING
			//
			//File names:
			//		limit to 8 characters, uppercase only
			//		Files to be hidden from user start with underscore
			//Short File names:
			//		limited to 2 characters, uppercase only
			//		Examples:	CONTACT (CN); OPP (OP); ACTIVITY (AC);
			//					Hidden files: _META (MD); _LINK (LI)
			//Sequential ID key:
			//		<xx>CLEUNIK (<xx>: short File name)
			//		Example: CNCLEUNIK (CONTACT table seq. ID)
			//Workgroup-unique ID (WUID) key _IDKey consists of five fields:
			//		IDDate
			//		IDTime
			//		IDCounter
			//		IDCreatedBy
			//		IDUAID
			//Fields:
			//		Use 3-character prefixes (uppercase only) plus underscore for
			//		all fields. Use same prefixes as you would use for
			//		controls on forms. Examples below. 
			//		The VIRTUAL files are the ones that are NOT PHYSICALLY PRESENT IN THE FILE/ANALYSIS.
			// ------- 	-------	--------------------------------
			// Virtual 	Prefix	Type
			// -------	-------	--------------------------------
			//           BI_     Big integer (8 bytes - in SQL Server 10 bytes)
			//			BIN		Binary memos
			//			CHK		Checkbox (internally short int)
			//			_CHR	Checkbox Reverse	Reversed checkbox
			//			CMB		Combo box
			//			CUR		Currency
			//			DR_		Double Real
			//			DTE		Date (Long int)
			//			_DTR	Date Reverse	Date field reversed
			//			FIL		Memo field containing a list of files separated by ';'
			//			INT		Integer (2 bytes)
			//			LI_		Long integer (4 bytes)
			//	X		LNK		Field reprensentation of a link that is physically in the _LINK file
			//			LST		Listbox
			//			MLS		Metadata list (combobox- or listbox-type field automatically filled from a list in _META)
			//			MMO		Text memo (up to SELL_STRINGLIMIT=?32000? chars)
			//			_MUP	Memo Upper	Same thing as TUP, but for original field of type MMO (up to 40 chars)
			//			_MUR	Memo Upper Reverse	Same as MUP, but in reverse order
			//			SEL		Selector (radio buttons)
			//			SI_		Short integer (1 byte)
			//			SR_		Single Real
			//	X		SYS		System field (not physically present in the file, can be SYS_NAME or SYS_ID)
			//			_TEO	Telephone Opposite Direction	Byte-level opposite-direction telephone number field without any non-numeric characters and without an extension number (string after the first �x� or �ext� is ignored)
			//			TME		Time (Long int)
			//			TML		Time long (stored the same way, but for display with second and hundreth)
			//			_TMR	Time Reverse	Time field reversed
			//			TXT		Text (non-memo)
			//			_TUP	Text Upper	Uppercase without spaces, punctuation, and non-alphanumerical characters (up to 40 chars)
			//			_TUR	Text Upper Reverse	Same as TUP, but in reverse order
			//			URL		Memo field containing a list of URLs separated by ';'
			//
			//		-------	--------------------------------
			//		Example: ACTIVITY.DTE_Date; APP.TME_StartTime; TODO.CHK_Completed
			//
			//		Prepend fields you want to hide from the user with underscores
			//		Example: ACTIVITY._INT_ShareState; CONTACT._CHK_PubOverride
			//
			//		To avoid problems, do not exceed 21 characters per field in Analysis.
			//
			//		Non-data-bound controls on forms shall have standard prefixes by their type.
			//
			//		Data-bound controls on forms shall be named the same as fields in the Analysis,
			//		including the prefix. This will allow our version of ScreenToFile() and
			//		FileToScreen() to match file fields with form controls.
			//
			//		Customer-modifiable lists such as ACTIVITY Purpose or Type shall be
			//		stored in the _META file. Such lists shall be modifiable only by designated
			//		users (admin only, admin and users). Such lists shall be used to fill
			//		Combobox or Selection controls. The selected item in a listbox or combobox
			//		shall be stored in a Combobox or Selection type field. To have our version
			//		of FileToScreen() automatically fill a combobox or listbox type field with
			//		values from a corresponding list in _META, the field must be prefixes with MLS_
			//		(Metadata List) in the analaysis.
			//
			//		Deleting selections in _META lists shall not be allowed, only renaming them. 
			//
			//		Lists of data for combos, listboxes, and radio buttons stored in _META shall
			//		have the following format in the Memo field:
			//
			//			Type=
			//			Sort=[Alpha]|[Numeric]|[Fixed]
			//			Order=1,3,6,2,4,5
			//			Val1=Call Received
			//			Val2=Call Placed
			//			Val3=Fax Received
			//			Val4=Fax Sent
			//			Val5=E-mail Received
			//			Val6=E-mail Placed
			//
			//		Note: The Order= line is irrelevant if Alpha or Numeric order is selected.
			//		We still keep the Order= line if the list has ever been set to the Fixed sort order.
			//
			//		Every list in _META shall be named according to its purpose (Activity Type,
			//		Activity Purpose, To-Do Type, etc.)
			//
			//		In release 1, there shall be no lists for custom fields for end-users to modify.



			//===========================================
			//_META (Metadata) prefixes for Page field
			//These definitions are accessible programmatically from goDef:GetMetaObjects()
			//AGE_			Agent
			//CND_			Conduit (application link)
			//DBB_			Dock Bar Button
			//DBI_			Dock Bar Icon (contains references to Dock Bar Buttons under which it displays)
			//DEV_			External Device (page is DEV_DeviceName, memo contains device ID, used as source information)
			//DSK_			Desktop
			//EXP_			Export definition
			//FLD_			Files' fields labels
			//FLT_			Filter
			//FRM_			Form customizations (Tabs hidden/displayed,
			//				custom fields enabled/disabled, field labels,
			//				scripts)
			//IDS_			Import Data Source Group
			//IMP_			Import definition
			//LST_			List
			//OTH_			Other
			//PGS_			Page setup
			//POP_			Personal options
			//PTA_			Automator pointer
			//PTR_			Sync pointer
			//PRN_			Print/send Templates
			//RPT_			Report
			//SP__			ScratchPad document
			//SCR_			Script (dynamically compiled code)
			//TMP_			Temporary backup of filters for file browser window
			//VIE_			View
			//VTM_			View Template
			//WIN_			State of main window
			//WOP_			Workgroup options




			//============================================
			//METADATA NAMES:
			//All metadata shall now use the same methodology to store their name:
			//
			//xx_NAME=yyyy (stored in the memo field with other parameters)
			// - with xx=Language prefix (result of the new goP:GetLangCode() method)
			// - and with yyyy =name of the metadata (displayable name)
			//
			//this solves the following problems:
			//- internationalisation of the metadata
			//- Displayability of a list of metadata in a table (for tranfer In by example) with all metadata names coded the same way
			//
			//Therefore, thank you to:
			//- change the content of all existing metadata
			//- change the way the name of each type of metadata is used/displayed/written

			//===========================================
			//FILE hierarchy for links (most dependent on bottom) upd. 1/29/2003:
			//
			//USER			[was EMPLOYEE in Commence]
			//FILELIST
			//GROUP
			//VENDOR
			//RESOURCE
			//TERR
			//PUBTERR
			//DIVISION
			//SLSTAX
			//LOCATION
			//DELIVERY
			//TERMS
			//EXPACCT
			//EXPCAT
			//INDUSTRY
			//JOBFUNC		[was TITLE in Commence]
			//CONFROOM
			//PRODUCT		[was ITEM in Commence]
			//MODEL			[was SKU in Commence]
			//EMPLOYEE
			//COMPANY
			//CONTACT
			//DOCUMENT
			//PROJECT
			//OPP
			//QUOTE
			//QUOTLINE		[was QUOTE ITEM in Commence]
			//EMAILALI
			//ACTIVITY
			//APP
			//TODO
			//EXPENSE
			//MESSAGE
			//
			//Independent files:
			//
			//REPORT
			//_TRANS
			//_META
			//_IMPLOG
			//_SYNCLOG
			//_LINK



			//===========================================
			//Alphabetical listing of files (upd. 10/7/2003):
			//_IMPLOG		IL
			//_LINK			LI
			//_META			MD
			//_SESSION		SE
			//_SYNCLOG		SL
			//_TRANS		TN
			//ACTIVITY		AC
			//APP			AP
			//COMPANY		CO
			//CONFROOM		CR
			//CONTACT		CN
			//DELIVERY		DM
			//DIVISION		DV
			//DOCUMENT		D0		
			//EMAILALI		EL
			//EMPLOYEE		EM
			//EXPACCT		EA
			//EXPCAT		EC
			//EXPENSE		EX
			//FILELIST		FI
			//GROUP			GR
			//INDUSTRY		IU
			//JOBFUNC		JF
			//LOCATION		LO
			//MESSAGE		MS
			//MODEL			ML
			//OPP			OP
			//PRODUCT		PD
			//PROJECT		PO
			//PUBTERR		PE
			//QUOTE			QU
			//QUOTLINE		QL
			//RELATION		RL
			//REPORT		RE
			//RESOURCE		RC
			//ROUTING		RO
			//SLSTAX		ST
			//TERMS			TR
			//TERR			TE
			//TODO			TD
			//USER			US
			//VENDOR		VE



			//===========================================
			//Fields used in most files:
			//
			//xxxxxName			where xxxxx is the name of the file
			//DeleteRec
			//DemoData
			//Description		memo
			//Notes				memo
			//SecureNotes		memo
			//Report
			//IDCreatedBy
			//IDDate
			//IDTime
			//IDCounter
			//IDUAID
			//ModBy
			//ModDate
			//ModTime
			//ImportData
			//CustomData
			//_ShareState
			//_ShareOverride
			//_PubOverride
			//_IDKey
			//SourceCreatedBy
			//SourceDate
			//SourceTime
			//SourceCounter
			//SourceUAID
			//LI__SourceSession	'ID of the import or sync session.
			//SyncedFromCreatedBy
			//SyncedFromDate
			//SyncedFromTime
			//SyncedFromCounter
			//SyncedFromUAID
			//-[other keys]

			//=============================================
			//_LINK file LinkTypes in most files:
			//
			//Related - 1
			//Involves - 2
			//Peer - 5
			//Referred By - 10
			//Referred - 11
			//Competing - 20
			//To - 30
			//cc - 31
			//Bill - 40
			//Billed In - 41
			//Share to USER - 101
			//Editable by USER - 102  (later will become EditShare, 103 will be used for AddShare)
			//Deletable by USER - 104
			//Publish to USER - 201
			//	NOT USED CURRENTLY WritePub to USER - 202  (later will become EditPub, 103 will be used for AddPub)
			//	NOT USED CURRENTLY DeletePub to USER - 204


			//==============================================
			//_LINK file keys and their structures:
			//
			//LICLEUNIK: automatic counter-based identifier
			//_IDKey: standard key: IDDate+IDTime+IDCounter+IDCreatedBy+IDUAID
			//_IDKey1: file1 to file2 link (not typed - will pull in all link types):
			//		LinkFile1+IDDate1+IDTime1+IDCounter1+IDCreatedBy1+IDUAD1+LinkFile2+...
			//			IDDate2+IDTime2+IDCounter2+IDCreatedBy2+IDUAID2
			//_IDKey2: file2 to file1 link (not typed - will pull in all link types):
			//		LinkFile2+IDDate2+IDTime2+IDCounter2+IDCreatedBy2+IDUAD2+LinkFile1+...
			//			IDDate1+IDTime1+IDCounter1+IDCreatedBy1+IDUAID1
			//_IDKey1Typed: file1 to file2 link (typed - will pull in only link recs of a specified type):
			//		LinkType+LinkFile1+IDDate1+IDTime1+IDCounter1+IDCreatedBy1+IDUAD1+LinkFile2+...
			//			IDDate2+IDTime2+IDCounter2+IDCreatedBy2+IDUAID2
			//_IDKey2Typed: file1 to file2 link (typed - will pull in only link recs of a specified type):
			//		LinkType+LinkFile2+IDDate2+IDTime2+IDCounter2+IDCreatedBy2+IDUAD2+LinkFile1+...
			//			IDDate1+IDTime1+IDCounter1+IDCreatedBy1+IDUAID1


			//==============================================
			//[FH] _TRANS (Translation) table Structure/Use :
			//- _IDKey of the record
			//- Source (device) :     T4
			//(Palm/Accounting software/Selltis in commence db/...)
			//- File:                    T8
			//(NGP file name)
			//- Internal ID Fields (IDDate, IDTime,...) (with composite key)
			//- External ID Field: T50
			//This translation table is supposed to be able to support unik ID of ANY external device or application... As we don't know what will be the 'size  of these keys in our futur links, I propose the following mechanism:
			//-- Each time we'll have to find a record in the translation table searching by the external ID, we'll search on the Device+external ID field, ' which is a non unik Key... then we'll compare to the FULL EXTERNAL ID that will be stored in a memo field (outlook has a T150 unik key, and  'windev support only keys on 100 characters)...
			//-- If the ID is not the good one, we'll loop on hreadnext till we find the good one or the External ID field does not match nay more
			//- FULL External ID Field: Memo text
			//- Composite key on device+file name+Internal ID (unik)
			//- Composite key on device+External ID (not unik)
			//
			//Depending on the data source, our code will extract the portion of the ID (from right to left or left to right) that provides uniqueness  '("uniquefying" the ID according to Rick).
			//
			//Based on whether the data source provides unique ID's < 50 long, we put the ID into the memo field or not.
			//
			//When integrating the changed data from an external source, compare all field values and log into SYNCLOG only the changed fields. This is to  'reduce the amount of data that syncs.


			//================================================
			//Selection lists (most managed in _META file):
			//
			//BUYING ROLE	Contact
			//BUYING STYLE	Contact
			//DELMETHOD	Delivery method in Quote
			//DIVISION	Activity, Product, ...
			//INFLUENCE ROLE	Contact
			//LOCATION	Company, Activity, ...
			//PURPOSE		Activity
			//QTELINESTATUS	Qte Line
			//QTESTATUS	Quote
			//RELATIONSHIP	Company (for WebPARTNER)
			//SOURCE		Activity, Opp, Quote, Contact
			//'MISSIONTYPE	WebForms 'mission types
			//TERMS		Quote terms and conditions, Company
			//TERRITORY	Activity, Company, Project, Opp, Quote Line
			//TYPE		Contact, Company, Activity, Appointment, Employee, User, ToDo, 


			//=========================== VIEW TYPES =============================
			// 	Supported view types to be used in 
			//	goWin:giLastViewType and goWin:giCurrWinType values:
			// 	Supported view types to be used in 
			//	goWin:giLastViewType and goWin:giCurrWinType values:
			//	SELL_NOT_VIEW		= 0	'Window is not a view
			//	SELL_FORM		= 1	'Form window (not a view)
			// 	SELL_VIEW_LIST		= 10	'List view
			// 	SELL_VIEW_DAYCAL	= 21	'Day Calendar view
			// 	SELL_VIEW_MDAYCAL	= 22	'Multi-day Calendar view
			// 	SELL_VIEW_WEEKCAL	= 23	'Week Calendar view
			// 	SELL_VIEW_MONTHCAL	= 24	'Month Calendar view
			// 	SELL_VIEW_YEARCAL	= 25	'Year Calendar view
			// 	SELL_VIEW_MCOLCAL	= 26	'Multi-column Calendar view
			// 	SELL_VIEW_GRAPHCAL	= 27	'Graphical Calendar view
			//	SELL_VIEW_WEEK2CAL	= 28	'Week Small Calendar view
			//					'	(user thinks this is a small-font
			//					'	version of regular Week Calendar view)
			// 	SELL_VIEW_MFIELD	= 30	'Multi-field view
			// 	SELL_VIEW_SFIELD	= 31	'Single-field view
			// 	SELL_VIEW_WEB		= 40	'Web browser view
			// 	SELL_VIEW_FORM		= 50	'Form view
			// 	SELL_VIEW_CHART		= 60	'Chart view
			// 	SELL_VIEW_GANTT		= 70	'Gantt (scheduling) view


			//====================== "NAME" VALUES ======================
			//Every record has a virtual "Name" value displayed in results of Global Search,
			//in linkboxes, and in linkbox selection lists.
			//
			//Characters between field codes are meant to be preserved literally except for the
			//"<>" characters, which contain instructions on conditional processing or link specs.
			//
			//Remove the delimiting characters that precede blank keywords. For example, If
			//a Contact "John Doe" has a BusPhone of "************", then the "Name" would
			//appear as "Doe, John - ************", if not, the "Name" would appear as "Doe,
			//John" (no space, hyphen, space after "John").
			//
			//This list may be exposed for editing in global option, editable only by the admin.
			//This way what displays is customizeable by us and by our customers.
			//
			//Prepending and appending currency symbols to CUR_ fields will be done after we
			//support regional settings in Tools>Options.
			//
			//_LINK		
			//_META		
			//_TRANS		
			//ACTIVITY	DTE_Date <short, HH:MMam representation of TME_StartTime> ...
			//			<Related - Contact - TXT_NameLast>, <Related - Contact - TXT_NameFirst> ...
			//			<Related - Contact - TXT_ContactCode> <First 30 characters of MMO_Notes field>
			//APP		DTE_Date TME_StartTime <First 50 characters of MMO_Description field>
			//COMPANY	TXT_CompanyName - TXT_CityMailing TXT_StateMailing	
			//CONFROOM	TXT_ConfRoomName	
			//CONTACT	TXT_NameLast, TXT_NameFirst - TXT_BusPhone	
			//DELIVERY	TXT_DelMethName	
			//DIVISION	TXT_DivisionName
			//DOCUMENT	MMO_Description (first 50 characters)	
			//EMAILALI	TXT_EmailAddress	
			//EMPLOYEE	TXT_NameLast, TXT_NameFirst - TXT_TitleText
			//EXPACCT	TXT_ExpAcctName	
			//EXPCAT	TXT_ExpCatName
			//EXPENSE	<For - User - TXT_Code> CUR_Amount <To - Vendor - TXT_VendorName>
			//GROUP		TXT_GroupName
			//INDUSTRY	TXT_IndustryName	
			//LOCATION	TXT_LocationName	
			//MESSAGE	<From - User - TXT_Code> to <To - User - TXT_Code> DTE_Date <short TME_Time> TXT_'ject
			//MODEL		TXT_ModelName
			//OPP		<For - Company - TXT_CompanyName> <For - Product - TXT_ProductName> ...
			//			CUR_ValueIndex (MLS_Status)  
			//PRNTEMPL	TXT_PrnTemplName
			//PRODUCT	TXT_ProductName 	
			//PROJECT	TXT_ProjectName
			//PUBTERR	TXT_PubTerrName
			//QUOTE		<To - Company - TXT_CompanyName> CUR_Total <first 50 chars of MMO_Description> 
			//QUOTLINE	<To - Company - TXT_CompanyName>	<In - Quote - DTE_Date> TXT_Model CUR_'total
			//REPORT	TXT_ReportName
			//RESOURCE	TXT_ResourceName	
			//SLSTAX	TXT_SalesTaxName
			//TERMS		TXT_TermsName
			//TERR		TXT_TerrName
			//TODO		<Assigned To - User - TXT_Code>: <First 50 characters of MMO_Description>
			//USER		TXT_NameLast, TXT_Name First - TXT_Code
			//VENDOR	TXT_VendorName


			//============ STORING INITIAL STATES OF WINDOWS =================
			//When you want to persist the state of a window or controls within it so
			//the window is opened in the same state in which it was last closed, for
			//example, use a _META page "OTH_INITIAL_STATES". For example:
			//
			//goP:LineRead(goP:GetUserTID(), "OTH_INITIAL_STATES", "WDESKPRO_TAB", "1", True)
			//
			//'True' passed as the last parameter ensures that the property
			//'WDESKPRO_TAB' (tab to open in the WDESKPRO window) will be read only
			//from user-specific metadata, not first from GLOBAL, then user's page.



			//===================== EVENT LOGGING ========================== 
			//Use clLog class to log events. Events are logged in the \Log\Log.txt file
			//of the program directory. Log levels allow the user to start the database
			//with a command line switch that makes the product log more or fewer events.
			//
			//Logging levels available:
			//1 - SELL_LOGLEVEL_STANDARD 	- Always ON, only Entry and Exit of the main
			//								  modules are logged (and errors, of course)
			//2 - SELL_LOGLEVEL_DETAILS     - Details: main functionalities of each module
			//								  are also logged
			//3 - SELL_LOGLEVEL_DEBUG       - Debug: Everything is logged
			//
			//Always test whether the CURRENT LOG LEVEL is greater of equal to the LOG operation:
			//This is for logging debug messages that you can leave in the code after you have
			//finished debugging. The line will be logged ONLY if the current log level is DEBUG.
			//
			//You can set the current log level for the entire session or 'by hand' in your code
			//with goLog:SetLogLevel method. If you use this for debugging during development, remove
			//before shipping the product! We want our customers to see logs that are meaningful.
			//
			//You can also trace messages on screen during the execution by activating the SCREEN mode:
			//		goLog:SetTraceMode(True)
			//
			//Available command line parameters:
			//		Screen=ON	to display everything being logged in a trace window
			//					(same as goLog:SetTraceMode(True))
			//		Trace=X		where X can be 0, 1, or 2 
			//
			//Displaying the log messages in a window:
			//		Select Help>Message Log in W_MAIN.
			//
			//Error details are logged in a separate file, error.txt in the same \Log\ folder.
			//Each error is also logged in one line under priority 0 in Log file...


			//============== FIELD CODES IN METADATA ================
			//Combined fields:
			//		<%Separator0<%Field1%>separator1<%Field2%>separator2<%Field3%>separator3<%Field4%>Separator4%>
			//	Example:
			//		On <%DTEDate:%> <%Start Time%> - <%CoordinatedBy%%User%%TXT_Code%>: <%Description%>
			//
			//Size in direct fields:
			//		<%TXT_FieldCode SIZE=n DOTS=x%>
			//	Example:
			//		<%MMO_Notes SIZE=10 DOTS=1%>
			//
			//Link fields:
			//		<%LNK_LinkName_LinkFile%>
			//		<%LNK_LinkName_LinkFile%%TXT_FieldName%>
			//		<%LNK_LinkName_LinkFile%%TXT_FieldName RECS=n DOTS=1|0 DELIM=CR%>
			//	Examples:
			//		<%LNK_CreatedBy_USER%> returns the Name of the User
			//		<%LNK_CreatedBy_USER%%SYS_Name%> is the same as above
			//		<%LNK_CreatedBy_USER%%LNK_Peer_USER%> returns the Name of the Peer of the User
			//		<%LNK_CreatedBy_USER%%TXT_NameLast%> returns the Last Name of the User
			//
			//Name field:
			//		<%SYS_Name%>
			//
			//ID field:
			//		<%SYS_ID%>
			//
			//Calculated fields:
			//		<%Calc VALUE="string [Numerical calcs] string...] FORMAT=n"%>
			//	Example:
			//		<%TXT_NameLast%>, <%TXT_NameFirst%>'s adjusted unit price: [<%CUR_PriceUnit%>*8-3 FORMAT=2]

			//==================== INSTALLING A NEW DB ============================
			//These are old notes that may be obsolete.
			//The user can create a new database locally.
			//When creating a DB, the user names it and selects a directory.
			//If the directory is already in a list of registered DB's, the user
			//can't do it. If the directory doesn't exist, the user is asked
			//whether to create a new dir and DB in it. If answered Yes, we create 
			//the dir, add the DB to the list of DB's and to the REP file. If the
			//dir exists and is not in a list, is the
			//directory empty? If so, the user is asked whether to create a new DB.
			//The DB is added to the list of DB's and to the REP file.
			//If not empty, we check whether there are .fic, .mmo, and .ndx files
			//for each of our data files in the analysis. If all files exist, the user
			//is asked whether to add this database to the list of DB's. If user clicks
			//Yes, the database is added to the list of DB's and to the REP file. 

			//If the user changes the DB location, we have to modify the REP file!





			//Initialize global variables
		}
		public void EndProgram(string sString = "")
		{
			//CS PORTED as placeholder
			//DEPRECATED
			string sProc = "clProject::EndProgram";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

		}
		public void FileTrace(string par_sMess, string sProc)
		{

			//Dim sProc As String = "clProject::FileTrace"

			goLog.Log(sProc, par_sMess, 1, false, true);

			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)
			//CS: Commented from here to end.
			//fecritligne(:glID,Timetostring(timesys())+" - "+par_sMess)
			//        'multitache(-1)
		}
		public long GetAlertPointer(string par_sFile, string par_sProcName)
		{
			//CS PORTED as placeholder (from w_proc)

			//PURPOSE:
			//		Returns the record CLEUNIK value for a given file and script procedure
			//		that wrote it with SetAlertPointer(). 
			//PARAMETERS:
			//		par_sFile: System filename
			//		par_sProcName: sProc name of the script procedure that uses the pointer
			//RETURNS:
			//		Long int: CLEUNIK value (pointer)

			return 0; //cs remove"

			//sProc is string=FenEnExecution()+".GetAlertPointer"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//sMemo is string = goMeta:PageRead(goP:GetUserTID(),"OTH_ALERT_POINTERS", "", True)
			//sFile is string = Upper(par_sFile)
			//sProcName is string = Upper(par_sProcName)
			//lPointer is long int

			//lPointer = Val(goTr:StrRead(sMemo, sFile+"|"+sProcName, "0", False))

			//        RESULT(lPointer)
		}
		public int GetCurrentUAID(bool par_bUnsigned = false)
		{
			//CS OK, WT Review
			//Review: Translation of Transfer and this function says it can return as integer
			//or an unsigned integer, but uCurrentUAID is actually declared as a 
			//long. What does this function need to return?

			//Send back the current UAID, as an integer (the way it's declared in the DB)
			string sProc = "clProject::GetCurrentUAID";
			//No log here, or infinite loop and too much level in debug mode (used in the log method)
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//Depending on the flag, result can be returned as an int (for file storing) or
			//as an unsiged int, for display purpose (otherwise sometimes < 0)

			//----------------------------------------------------------------------------------------------------------------------
			//file storing means that it's a value that you want to store in a corresponding field in a file (by example 
			//in "ACTIVITY.INT_CreatorIDUAIDUS". In that case, it needs to be an INT, as the field in the file is of INT type.

			//Display purpose means that you want display the UAID value somewhere... In that case, if it's returned as an INT, and 
			//the value is bigger than 32768, then the returned value will be negative (-534 for 65001 by example), which is 
			//certainly not what you want to display... In that case, you want to receive the unsigned int value 65001 and 
			//display it.
			//----------------------------------------------------------------------------------------------------------------------


			int iUAID = 0;
			if (par_bUnsigned)
			{
				return (int)uCurrentUAID;
			}
			else
			{
				//CS: Check the way I translated Transfer (look in Windev help)  
				iUAID = Convert.ToInt32(uCurrentUAID.ToString().Substring(0, 2));
				//transfer(&iUAID,&uCurrentUAID,2)
				return iUAID;
			}
		}
		public string GetCurrentUserAbbrev()
		{
			//CS OK

			//Send back the current User abbrev (4 letters)
			string sProc = "clProject::GetCurrentUserAbbrev";

			//No log here, or infinite loop and toomuch level in debug mode (used in the log method)
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			return goTR.Complete(goP.GetUserCode(), 4);
		}

		public string GetDefaultLang()
		{
			//1/10/07 MI changed return type to string
			//MI 12/10/06 Added
			string sproc = "clProject::GetDefaultLang";
			string s;

			s = goMeta.LineRead("GLOBAL", "GOP_GLOBAL_OPTIONS", "DEFAULT_LANGUAGE", "US");

			if (s != "")
			{
				return s;
			}
			else
			{
				return "US";
			}

		}
		public string GetLangCode(int par_iLang = 0)
		{
			//MI 6/21/13 When par_iLang = 0, replaced explicitly setting language to 3 with a call to GetNation().
			//MI 3/16/06 Temporarily made this return 'US' if par_iLang = 0.

			//CS OK
			//---------
			//SET ERROR
			//---------
			//AUTHOR: MI 4/13/04	8/27/2002
			//PURPOSE:
			//		Send back the lang code ('US', 'FR' ...) for
			//			the language number (WinDev's Nation() number) or
			//			for the current language if par_iLang is empty.
			//PARAMETERS:
			//		par_iLang:	language number (3, 5...) for which we want the code.
			//					If empty, current language is used. 
			//RETURNS:
			//		String containing the language code (US, FR...) or "" with error set.
			//HOW IT WORKS:
			//		Use it when you want to write a parameter in a specific language in metadata
			//			(or read a parameter in a language that is different from the current one)
			//EXAMPLE:
			//		goP:GetLangCode()

			//---------------- HISTORY ------------------
			//8/27/2002	MI	clProject::GetLangCode()			Edited comment.

			string sProc = "clProject::GetLangCode";
			//Not logging for speed optimisation
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_iLang,SELL_LOGLEVEL_DEBUG)

			//goLog.SetError() 'CS: Commented out until ready

			int iLang = 0;
			//CS Note: This function accepted a parameter of Nation() [MI 6/21/13: now GetNation()],
			//but I don't think
			//this is supported in .net. I set a default value of 0 for the nation and if
			//it is 0 I will get the global variable that holds the nation. This
			//will only work if 0 is not a value that the variable could hold to
			//represent a particular language.
			if (par_iLang == 0) //No language was specified, return the current nation
			{
				//*** Temporarily forcing 'US' until Nation() is debugged ***
				iLang = GetNation(); //MI 6/21/13 commented value '3' 'Nation()
			}
			else
			{
				iLang = par_iLang;
			}

			switch (iLang)
			{
				case 3:
					return "US";
				case 5:
					return "FR";
				case 1:
					return "GR";
				case 7:
					return "SP";
				default:
					//goLog.SetError(10103, sProc, "", "par_iLang", sProc, par_iLang) 'CS: Commented out until ready
					// Internal error: unsupported parameter value. The parameter '[1]' received in '[2]' is equal to '[3]'. This value is not supported.
					//
					//Please contact your system administrator.
					//Return GetDefaultLang()
					//CS: Commented out above line b/c MI did not want that function ported.
					return "US";
			}
		}



		public string GetLangName(string par_sLangToFind = "", string par_sReturnLang = "")
		{
			//CS OK

			//---------
			//SET ERROR
			//---------
			//AUTHOR: MI 4/19/04	7/24/2002                                     
			//
			//PURPOSE:
			//		Get friendly name of the language. If no parameters, get name of the
			//		current language in the current language.
			//PARAMETERS:
			//		par_sLangToFind: Text code of the language whose friendly name to return
			//			If blank, current language is used.
			//		par_sReturnLang: Text code of the language in which to return the name
			//			If blank, current language is used.
			//RETURNS:
			//		String: name of the language or "". If "", an error is set.

			string sProc = "clProject::GetLangName()";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//goLog.SetError() 'CS: Commented out until ready

			string sLangToFind = par_sLangToFind;
			string sReturnLang = par_sReturnLang;

			if (sLangToFind == "")
			{
				sLangToFind = GetLangCode(); //Current language
			}

			if (sReturnLang == "")
			{
				sReturnLang = GetLangCode(); //Current language
			}

			switch (sLangToFind)
			{
				case "US":
					switch (sReturnLang)
					{
						case "US":
							return "US English";
						case "FR":
							return "Anglais/Am�ricain";
						case "SP":
							return "Ingles Americano";
						case "GR":
							return "Englisch (USA)";
					}
					break;
				case "FR":
					switch (sReturnLang)
					{
						case "US":
							return "French";
						case "FR":
							return "Fran�ais";
						case "SP":
							return "Frances";
						case "GR":
							return "Franz�sisch";
					}
					break;
				case "GR":
					switch (sReturnLang)
					{
						case "US":
							return "German";
						case "FR":
							return "Allemand";
						case "SP":
							return "Alleman";
						case "GR":
							return "Deutsch";
					}
					break;
				case "SP":
					switch (sReturnLang)
					{
						case "US":
							return "Spanish";
						case "FR":
							return "Espagnol";
						case "SP":
							return "Espa�ol";
						case "GR":
							return "Spanisch";
					}
					break;
				default:
					//goLog.SetError(10103, sProc, "", "sLangToFind", sProc, sLangToFind) 'CS: Commented till ready
					// Internal error: incorrect parameter value. The parameter '[1]' received in '[2]' is equal to '[3]'. This value is not supported.
					//
					//Please contact your system administrator.
					return "";

			}

			return "";
		}
		public string GetMapURL(string par_sAddress, string par_sCity, string par_sState, string par_sZip)
		{
			//CS PORTED as placeholder (from w_proc)
			//For now return "".

			//PURPOSE:
			//		Returns a MapQuest URL, which can be used internally to be stored or shown in a web view.
			//		The MapQuest service supports addresses in the United States. Other countries may not be
			//		supported.
			//		See also: ShowMap().
			//PARAMETERS:
			//		par_sAddress: Street address. This parameter should not contain hard returns and must be
			//			resolvable by MapQuest. 
			//		par_sCity: City
			//		par_sState: 2-character state abbreviation
			//		par_sZip: zip code (with or without extension).
			//RETURNS:
			//		MapQuest URL that will display a map for the given address.
			//SEE ALSO:
			//		ShowMap()

			//sProc is string=FenEnExecution()+".GetMapURL"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			return ""; //cs remove

			//autSellMisc is automation "SellUtil.Misc"

			//        RESULT(autSellMisc >> GetMapURL(par_sAddress, par_sCity, par_sState, par_sZip))
		}

		public string GetMe(string par_sType)
		{
			//WT 12/5/14 added case TZOFFSET
			//MI 12/10/13 Added case PERMLINK_OUTLOOKTODO.
			//MI 5/13/11 Added case CUSTADMINREPORTS.
			//MI 3/3/11 Added case USERADMINCUSTOMER.
			//MI 2/5/08 Added case PERMMOBILE.
			//MI 5/6/08 Added case PERMSHAREDDESKTOPS.
			//MI 5/5/08 Added cases PERMLINK_OUTLOOKCONTACT and PERMLINK_OUTLOOKCAL.
			//MI 11/26/07 Added PERMWORKGROUPOPTIONS.
			//MI 8/10/07 Fixed case ID. Added Case LocationName. Added Case Else to process custom PERMxxxx permissions.
			//MI 5/15/07
			//MI 4/13/07 Added LogonID
			//MI 2/7/07 Implemented permissions
			//MI 5/15/06 Started enabling
			//PURPOSE:
			//		Get information about the currently logged on user. User information is taken
			//		from the USER file and/or from the security file that is automatically managed
			//		when saving a User record.
			//       The only other method where individual permissions are hard-coded
			//       is clPerm.GetDefaultFeaturePermissions(). Maintain these two methods in sync.
			//SYNTAX IN SCRIPTING API:
			//		sResult = GetMe(par_sType)
			//PARAMETERS:
			//		par_sType	String	Type of information to return. Either pass
			//           one of the supported values below (see Select Case statement
			//           in code below), or for custom permissions, pass the name of the 
			//           permission prepended with 'PERM'. Example:
			//               PERMMULTIPLESESSIONS
			//           Default feature permissions are defined in clPerm.GetDefaultFeaturePermissions().
			//           Custom permissions, if not defined, will return "", NOT a "0"! If your code
			//           expects an integer, use goTr.StringToNum(gop.GetMe("PERMCUSTOM"));
			//           for a boolean use goTr.StringToCheckbox(goP.GetMe("PERMCUSTOM")).
			//           Those methods interpret "" as 0 or Flase, respectively.
			//           Supported values are:
			//			Name		'SYS_Name of the user.
			//			ID			'30-character text ID of the user.
			//			Code		'4-character user code from the TXT_Code field in the User file. 
			//                       'Trailing spaces are truncated, therefore fewer characters may be returned.
			//			Location	'GID_ID of the Location linked to the User.
			//           LocationName    'SYS_Name of the Location linked to the User.
			//			LogonName	'User name as entered in the log in dialog.
			//           LogonID     'GID_ID of User's logon record in XU table
			//			PermAuthor	'Author permission. Values: 0=not author; 1=limited; 2=Full author
			//			PermAdmin	'Admin permission. Values: 0=not an admin; 1=admin
			//           ...         'See Select Case below
			//           NGP constants for permission types are abandoned. A few examples:
			//   			clc.SELL_AUTHOR_NONE		=	0
			//	    		clc.SELL_AUTHOR_PARTIAL		=	1
			//	    		clc.SELL_AUTHOR_FULL		=	2
			//		    	clc.SELL_ADMIN				=	3
			//			    clc.SELL_CREATENEWDB		=	4
			//RETURNS:
			//		String: The requested information or "" if the requested type is unsupported.
			//           Checkbox (boolean) values are returned as "1" or "0".
			//EXAMPLE:
			//		IF doRowset.GetFieldVal("LNK_CreatedBy_User") = goTr.GetMe("ID") THEN
			//			'I created this record
			//		ELSE
			//			'Someone else created this record
			//		END
			//       If goP.GetMe("SHAREDDESKTOPS") = "1" Then
			//           'The user is authorized to add/edit/delete shared desktops
			//       End If

			string sProc = "clProject::GetMe";

			string sType = par_sType.ToUpper();
			string sPerms = GetUserPermissions();

			switch (sType)
			{
				case "NAMEUS":
					return GetUserName().Replace(",", "");
				case "NAME":
					return GetUserName();
				case "ID":
				case "GID_ID":
					return GetUserTID();
				case "CODE":
					return GetUserCode();
				case "LOCATION":
					return GetUserLocation();
				case "LOCATIONNAME":
					return goData.GetRecordNameByID(GetUserLocation());
				case "LOGONNAME":
					return GetUserLogon();
				case "LOGONID":
					return GetUserLogonID();
				case "TZOFFSET":
					string sTZLocale = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "TZLOCALE", "", false);
					PublicDomain.TzTimeZone zone = PublicDomain.TzTimeZone.GetTimeZone(sTZLocale);
					if (zone == null)
					{
						return "";
					}
					else
					{
						//Time zone is defined
						//------------- Current time, GMT time, and TZ offset ------------------------
						int iHours = goTR.UTC_GetUtcOffset(zone.ToLocalTime(goTR.NowUTC()), zone).Hours;
						string sHours = iHours.ToString();
						string sNegative = "+";
						if (sHours.Substring(0, 1) == "-")
						{
							sNegative = "-";
							sHours = goTR.FromTo(sHours, 2, -1);
						}
						sHours = goTR.Pad(sHours, 2, "0", "L");
						return sNegative + sHours + ":" + goTR.Pad((goTR.UTC_GetUtcOffset(zone.ToLocalTime(goTR.NowUTC()), zone).Minutes).ToString(), 2, "0", "L");
					}
					break;
				case "PERMAUTHOR":
					return IsUserAuthor().ToString();
				case "PERMADMIN":
					return IsUserAdmin().ToString();
					//Customer admin soon to be discontinued. See individual perms on the bottom.
				case "PERMADMINCUSTOMER":
					return IsUserCustomerAdmin().ToString();
				case "PERMIMPORT":
					return goTR.StrRead(sPerms, "IMPORT", "0", false);
				case "PERMEXPORT":
					return goTR.StrRead(sPerms, "EXPORT", "0", false);
				case "PERMTRANSFERIN":
					return goTR.StrRead(sPerms, "TRANSFERIN", "0", false);
				case "PERMTRANSFEROUT":
					return goTR.StrRead(sPerms, "TRANSFEROUT", "0", false);
				case "PERMPRINT":
					return goTR.StrRead(sPerms, "PRINT", "0", false);
				case "PERMSEND":
					return goTR.StrRead(sPerms, "SEND", "0", false);
				case "PERMTOEXCEL":
					return goTR.StrRead(sPerms, "TOEXCEL", "0", false);
				case "PERMLINK_OUTLOOKCAL":
					return goTR.StrRead(sPerms, "LINK_OUTLOOKCAL", "1", false);
				case "PERMLINK_OUTLOOKCONTACT":
					return goTR.StrRead(sPerms, "LINK_OUTLOOKCONTACT", "1", false);
				case "PERMLINK_OUTLOOKTODO":
					return goTR.StrRead(sPerms, "LINK_OUTLOOKTODO", "1", false);
					//Mobile
				case "PERMMOBILE":
					return goTR.StrRead(sPerms, "MOBILE", "0", false);
				case "PERMMOBILEREMEMBERME":
					return goTR.StrRead(sPerms, "MOBILEREMEMBERME", "1", false);
				case "PERMMOBILEEXPIRELOGININDAYS":
					return goTR.StrRead(sPerms, "MOBILEEXPIRELOGININDAYS", goTR.NumToString(clC.SELL_MOBILEEXPIRELOGININDAYS), false);
				case "PERMMOBILEREMEMBERUSERNAME":
					return goTR.StrRead(sPerms, "MOBILEREMEMBERUSERNAME", "1", false);
					//Desktop sharing
				case "PERMSHAREDDESKTOPS":
					return goTR.StrRead(sPerms, "SHAREDDESKTOPS", "0", false);
					//Billing, reimbursement
				case "PERMPERIODICBILLING":
					return goTR.StrRead(sPerms, "PERIODICBILLING", "0", false);
				case "PERMREIMBURSE":
					return goTR.StrRead(sPerms, "REIMBURSE", "0", false);
				case "PERMAPPROVEREIMBURSE":
					return goTR.StrRead(sPerms, "APPROVEREIMBURSE", "0", false);
					//Customer admin permissions
				case "PERMCUSTADMINMANAGELOGINS":
					return goTR.StrRead(sPerms, "CUSTADMINMANAGELOGINS", "0", false);
				case "PERMCUSTADMINMANAGEFEATURES":
					return goTR.StrRead(sPerms, "CUSTADMINMANAGEFEATURES", "0", false);
				case "PERMCUSTADMINMANAGEDATAACCESS":
					return goTR.StrRead(sPerms, "CUSTADMINMANAGEDATAACCESS", "0", false);
				case "PERMWORKGROUPOPTIONS":
					return goTR.StrRead(sPerms, "WORKGROUPOPTIONS", "0", false);
				case "PERMCUSTADMINREPORTS":
					return goTR.StrRead(sPerms, "CUSTADMINREPORTS", "0", false);
				case "PERMCUSTADMINREASSIGNRECORDS":
					return goTR.StrRead(sPerms, "CUSTADMINREASSIGNRECORDS", "0", false);
					//Document library (S3) permissions
				case "PERMDOCLIBPUBLICCREATEFOLDERS":
					return goTR.StrRead(sPerms, "DOCLIBPUBLICCREATEFOLDERS", "0", false);
				case "PERMDOCLIBPUBLICDELETEFOLDERS":
					return goTR.StrRead(sPerms, "DOCLIBPUBLICDELETEFOLDERS", "0", false);
				case "PERMDOCLIBPUBLICCREATEFILES":
					return goTR.StrRead(sPerms, "DOCLIBPUBLICCREATEFILES", "0", false);
				case "PERMDOCLIBPUBLICDELETEFILES":
					return goTR.StrRead(sPerms, "DOCLIBPUBLICDELETEFILES", "0", false);
				case "PERMDOCLIBPERSONALCREATEFOLDERS":
					return goTR.StrRead(sPerms, "DOCLIBPERSONALCREATEFOLDERS", "1", false);
				case "PERMDOCLIBPERSONALDELETEFOLDERS":
					return goTR.StrRead(sPerms, "DOCLIBPERSONALDELETEFOLDERS", "1", false);
				case "PERMDOCLIBPERSONALCREATEFILES":
					return goTR.StrRead(sPerms, "DOCLIBPERSONALCREATEFILES", "1", false);
				case "PERMDOCLIBPERSONALDELETEFILES":
					return goTR.StrRead(sPerms, "DOCLIBPERSONALDELETEFILES", "1", false);
				case "PERMDOCRECORDLEVELCREATEFILES":
					return goTR.StrRead(sPerms, "DOCRECORDLEVELCREATEFILES", "1", false);
				case "PERMDOCRECORDLEVELDELETEFILES":
					return goTR.StrRead(sPerms, "DOCRECORDLEVELDELETEFILES", "1", false);
				default:
					//Read any other "PERM..." value
					return goTR.StrRead(sPerms, goTR.FromTo(sType, 5, -1), "", false);
			}

			return "";

		}

		public string GetModuleList()
		{
			//CS OK

			//AUTHOR: MI 5/21/03
			//PURPOSE:
			//		Return tab- and vbCrLf-delimited list of supported modules
			//FORMAT OF EACH LINE:
			//		<n>+TAB+<Module name>+TAB+<MODULETYPE>+vbCrLf
			//			Don't add vbCrLf to the last line!
			//
			//PARAMETERS:
			//		<none>

			//For debugging the order of calls.
			string sProc = "clProject::GetModuleList";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string s = "";

			s += 11.ToString() + "\t" + "Standard - sync client" + "\t" + "DATABASE" + "\r\n";
			s += 12.ToString() + "\t" + "Standard - network client" + "\t" + "DATABASE" + "\r\n";
			s += 13.ToString() + "\t" + "Standard - web client" + "\t" + "DATABASE" + "\r\n";
			s += 21.ToString() + "\t" + "Sales - sync client" + "\t" + "DATABASE" + "\r\n";
			s += 22.ToString() + "\t" + "Sales - network client" + "\t" + "DATABASE" + "\r\n";
			s += 23.ToString() + "\t" + "Sales - web client" + "\t" + "DATABASE" + "\r\n";
			s += 31.ToString() + "\t" + "Quoting - sync client" + "\t" + "DATABASE" + "\r\n";
			s += 32.ToString() + "\t" + "Quoting - network client" + "\t" + "DATABASE" + "\r\n";
			s += 61.ToString() + "\t" + "WebPARTNER client" + "\t" + "ACCESS" + "\r\n";
			s += 71.ToString() + "\t" + "Outlook Link" + "\t" + "LINK" + "\r\n";
			s += 72.ToString() + "\t" + "Palm Link" + "\t" + "LINK" + "\r\n";
			s += 73.ToString() + "\t" + "CardScan Link" + "\t" + "LINK";

			return s;
		}
		public string GetModuleNameFromID(int par_iModuleID)
		{
			//CS OK

			//OWNER: MI 7/23/2002

			//PURPOSE:
			//		Retrieve friendly name of a module based on the provided module ID.
			//
			//PARAMETERS:
			//		par_iModuleID: ID of the Module for which to retrieve the name.

			string sProc = "clProject::GetModuleNameFromID";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//goLog.SetError() 'CS: Commented out until ready

			switch (par_iModuleID)
			{
				case 11:
					return ("Standard - sync client"); //MessTranslate
					//				----- Anglais/English (3) -----
					//				Standard - sync client
					//				----- Francais/French (5) -----
					//				Standard - Client r�pliqu�
				case 12:
					return ("Standard - network client"); //MessTranslate
					//				----- Anglais/English (3) -----
					//				Standard - network client
					//				----- Francais/French (5) -----
					//				Standard - Client r�seau
				case 13:
					return ("Standard - web client"); //MessTranslate
					//				----- Anglais/English (3) -----
					//				Standard - web client
					//				----- Francais/French (5) -----
					//				Standard - Client web
				case 21:
					return ("Sales - sync client"); //MessTranslate
					//				----- Anglais/English (3) -----
					//				Sales - sync client
					//				----- Francais/French (5) -----
					//				Ventes - Client r�pliqu�
				case 22:
					return ("Sales - network client"); //MessTranslate
					//				----- Anglais/English (3) -----
					//				Sales - network client
					//				----- Francais/French (5) -----
					//				Ventes - Client r�seau
				case 23:
					return ("Sales - web client"); //MessTranslate
					//				----- Anglais/English (3) -----
					//				Sales - web client
					//				----- Francais/French (5) -----
					//				Ventes - Client web
				case 31:
					return ("Quoting - sync client"); //MessTranslate
					//				----- Anglais/English (3) -----
					//				Quoting - sync client
					//				----- Francais/French (5) -----
					//				Devis - Client r�pliqu�
				case 32:
					return ("Quoting - network client"); //MessTranslate
					//				----- Anglais/English (3) -----
					//				Quoting - network client
					//				----- Francais/French (5) -----
					//				Devis - Client r�seau
				case 61:
					return ("WebPARTNER client"); //MessTranslate
					//				----- Anglais/English (3) -----
					//				WebPARTNER client
					//				----- Francais/French (5) -----
					//				Client WebPartner
				case 71:
					return ("Outlook Link"); //MessTranslate
					//				----- Anglais/English (3) -----
					//				Outlook Link
					//				----- Francais/French (5) -----
					//				Lien Outlook
				case 72:
					return ("Palm Link"); //MessTranslate
					//				----- Anglais/English (3) -----
					//				Palm Link
					//				----- Francais/French (5) -----
					//				Lien Palm
				case 73:
					return ("CardScan Link"); //MessTranslate
					//				----- Anglais/English (3) -----
					//				CardScan Link
					//				----- Francais/French (5) -----
					//				Lien CardScan
				default:
					goLog.SetError(10103, sProc, "", "par_iModuleID", sProc, par_iModuleID);
					// Internal error: incorrect parameter value. The parameter '[1]' received in '[2]' is equal to '[3]'. This value is not supported.
					//
					//Please contact your system administrator.
					//goLog.DisplayLastError()
					return "";
			}


		}
		public int GetNumberOfSupportedLangs()
		{
			//CS OK

			//PURPOSE:
			//		Returns the number of supported lang, base on the stgring returned by GetSyupportedLang
			//PARAMETERS:
			//		N/A
			//RETURNS:
			//		The number of supported langs
			//HOW IT WORKS:
			//		Count the langs inthe string returned by GetSupported lang
			//EXAMPLE:
			//		iLang=goP:GetNumberOfSupportedLangs()

			string sProc = "clProject::GetNumberOfSupportedLangs";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start" , SELL_LOGLEVEL_DEBUG)

			string sWork = GetSupportedLangs();
			int iI = 1;
			do
			{
				if (goTR.ExtractString(sWork, iI) == clC.EOT.ToString())
				{
					return iI - 1;
				}
				iI = iI + 1;
			} while (true);
// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return 0;
		}

		public string GetProduct()
		{
			//MI 3/3/09 Removed goLog.Log.
			//MI 1/14/09 Created
			//PURPOSE:
			//       Return 2-character code of the current login's product

			//Dim sProc As String = "clProject::GetProduct"
			//Must not run goLog(sProc, ...)!
			//Log to avoid a loop: goLog.Log calls goData.GetConnection, which calls goP.GetProduct, which must not call golog.Log!

			return sProduct;
		}

		public string GetProductLabel(string par_sProduct)
		{
			//MI 3/4/09 Removed goLog.Log.
			//MI 1/28/09 Created

			//Dim sProc As String = "clProject::GetProductLabel"
			//goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

			DataRow [] aRowArray = null;
			DataRow dRow = null;
			string sReturn = "";

			//Try
			aRowArray = goP.dtProducts.Select("TXT_ProductCode='" + par_sProduct + "'");
				dRow = aRowArray[0];

				sReturn = dRow["TXT_ProductLabel"].ToString();

			//Catch ex As Exception
			//    sReturn = ""
			//End Try

			return sReturn;

		}

		public DataTable GetProductList()
		{
			//MI 3/4/09 Removed goLog.Log.
			//MI 1/14/09 Created.

			//Dim sProc As String = "clMetaData::GetProductList"
			//goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

			return dtProducts;
		}

		public void LoadProductList()
		{
			//MI 3/4/09 Removed goLog.Log.
			//MI 3/3/09 Added comment on pGetProducts
			//MI 1/14/09 Created.

			string sProc = "clMetaData::LoadProductList";
			//goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader reader = null;
			Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();

			dtProducts.Rows.Clear();

			//MI 3/4/09 Commented Try Catch not to cause issues if this is called during early clProject initialization
			//Try
			cmd.CommandText = "pGetProducts";
			cmd.CommandType = CommandType.StoredProcedure;
			cmd.Connection = sqlConnection1;

			reader = cmd.ExecuteReader();

			//pGetProducts returns:
			//--		SI__Order			Number used for sorting the products. This value may change 
			//--							if additional products are defined later.
			//--		TXT_ProductCode		Product code like 'SA', 'MB', 'WP' (varchar(2))
			//--		TXT_ProductLabel	Descriptive name like 'Sales', 'Mobile', etc. (nvarchar(50))

			if (reader.HasRows)
			{
				dtProducts.Load(reader);
				//dtProducts.PrimaryKey = dtProducts.Columns("TXT_ProductCode")
				//==> set key
			}
			else
			{
				//==> raise error
			}

			reader.Close();
			sqlConnection1.Close();
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

		}

		public string GetRunMode()
		{
			//MI 3/4/09 Removed goLog.Log.
			//CS OK

			//Dim sProc As String = "clProject::GetRunMode"
			//goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

			return sRunMode;
		}

		public bool GetSaveState(string par_sCreatorID, bool par_bShared, string par_sSection = "", string par_sPage = "")
		{
			//MI 5/7/08 Added par_sPage parameter to support special managed shared desktops/view permission, which 
			//       overrides the author consideration
			//MI 5/23/07 Added parameter par_sSection
			//MI 5/16/07 Edited header.
			//MI 4/1/07 Added checking creator for unshared objects

			//AUTHOR: MI 12/6/06
			//PURPOSE:
			//       Return whether the user should be able to save a page of metadata.
			//       This is called from dialogs to determine whether the Save button
			//       should be Grayed or not.
			//PARAMETERS:
			//       par_sCreatorID: GID_ID of the creator's US (User) record.
			//       par_bShared: Sharing status of a metadata page. Pass True if the page is
			//           'GLOBAL', else pass False.
			//       par_sSection: Optional: section of the MD page. When this parameter
			//           is not blank and par_bShared is not True, the permission is
			//           based on the section, not on the Creator. This is how our permissions
			//           work currently. This allows reassigning local MD pages to other users
			//           by changing only the section. The creator ID remains unchanged, yet
			//           the new user can edit the page. If par_bShared is True, permissions
			//           are based only on the creator because the section doesn't convey
			//           the user (it is always "GLOBAL" or NULL as stored in SS).
			//       par_sPage: Page ID or prefix of the MD page. If the object is a desktop, you should
			//           pass its page ID to this parameter in order for the special 
			//           'SHAREDDESKTOPS' permission to be taken into consideration. Otherwise,
			//           only the author level is used.
			//RETURNS:
			//       Boolean: True if the user has permission to save.

			string sProc = "clProject::GetSaveState";
			bool bResult = false;

			if (par_bShared) //shared
			{
				switch (goTR.GetPrefix(par_sPage))
				{
					case "DSK_":
					case "VIE_":
						//Desktops and views observe a special SHAREDDESKTOPS permission, which overrides the author consideration
						//and allows any user to add, edit and delete shared desktops and their views.
						if (goP.GetMe("PERMSHAREDDESKTOPS") == "1")
						{
							bResult = true;
							goto FinishAndReturnResult;
						}
						break;
				}

				switch (goP.IsUserAuthor())
				{
					case 1: //Limited author
						//Allowed to save a shared object that (s)he created
						if (par_sCreatorID.ToUpper() != goP.GetUserTID().ToUpper())
						{
							bResult = false;
						}
						else
						{
							bResult = true;
						}
						break;
					case 2: //Full author
						//Always allowed to save
						bResult = true;
						break;
					default: //User not an author
						//Not allowed to save a shared object
						bResult = false;
						break;
				}
			}
			else
			{
				//Local desktops, views, etc are invisible to users who didn't create the object, but in case someone
				//hacks the system and opens another user's object, (s)he won't be able to save it unless full author.
				//If the section is passed, a non-author user will have save permission if the section of the MD page
				//is the same as the user's GID_ID.
				switch (goP.IsUserAuthor())
				{
					case 2:
						bResult = true;
						break;
					default:
						if (par_sSection == "")
						{
							//Section not passed, base the permission on the creator
							if (par_sCreatorID.ToUpper() != goP.GetUserTID().ToUpper())
							{
								bResult = false;
							}
							else
							{
								bResult = true;
							}
						}
						else
						{
							//Section passed, base the permission on the section
							if (par_sSection.ToUpper() != goP.GetUserTID().ToUpper())
							{
								bResult = false;
							}
							else
							{
								bResult = true;
							}
						}
						break;
				}
			}

	FinishAndReturnResult:
			return bResult;

		}


		public string GetSelectedRecordID()
		{
			//CS PORTED as placeholder (from w_proc)
			//DEPRECATED
			//PURPOSE:
			//		Retrieves the ID of the record that is currently selected in the work area.
			//       Use goUI.GetLastSelected instead.
			//		The record can be displayed on a form or in a Desktop view. If the work area
			//		is empty, the selected view displays no records, the form is in creation
			//		mode or there is no selected record for any other reason, the method will return
			//		a blank string.
			//PARAMETERS:
			//		None.
			//RETURNS:
			//		String: 30-character ID of the selected record or "" if there is no selected record.
			//EXAMPLE:
			//		'Link a selected Contact in the Related Contact linkbox
			//		doForm:SetFieldVal("LNK_Related_Contact",GetSelectedRecordID())

			return "";
		}
		public string GetSupportedLangs()
		{
			//CS OK

			//AUTHOR: MI 4/13/04	7/30/2002
			//PURPOSE:
			//		Return a TAB delimited list of supported text language codes ("US", "FR", etc.)
			//		To get the default language, use :GetDefaultLang().

			//--------- LOG --------------------------
			//7/30/2002	MI	clProject:GetSupportedLangs()		Edited.
			//7/24/2002	MI	clProject:GetSupportedLangs()		Created method to manage supported languages.
			string sProc = "clProject::GetSupportedLangs";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string s;

			s = "US" + "\t" + "FR" + "\t" + "GR" + "\t" + "SP";

			return s;
		}
		public string GetUserCode()
		{
			//MI 5/15/06

			string sProc = "clProject::GetUserCode";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			return gsUserID;
		}
		public string GetUserLocation()
		{

			string sProc = "clProject::GetUserLocation";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			return sUserLocation;
		}
		public string GetUserLogon()
		{

			string sProc = "clProject::GetUserLogon";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			return sUserLogon;
		}
		public DateTime GetUserLastLogonDate(string par_sUserID = null)
		{
			//MI 5/16/08 Added [ ] delimiters.
			//MI 11/13/07 Created per WT.
			//PURPOSE:
			//       Retrieves the last datetime when the user has logged on.
			//       If the user never logged on, returns clc.SELL_BLANK_DATETIME .
			//PARAMETERS:
			//       par_sUserID: Optional: GID_ID value of the US record. If Nothing,
			//       the last login date is returned for the currently logged on user
			//       ("me").
			//RETURNS:
			//       Datetime: user's last login datetime.

			string sProc = "clProject::GetUserLastLogonDate";
			string sUserID = null;
			int tempVar = clC.SELL_TYPE_INVALID;
			DateTime dtResult = goTR.StringToDateTime(clC.SELL_BLANK_DATETIME, "", "", ref tempVar, " ");

			//Try
			if (par_sUserID == null)
			{
					//Me
					sUserID = goP.GetUserTID();
				}
				else
				{
					sUserID = par_sUserID;
				}

				//Read logon information and fill listbox
				System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
				System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader oReader = null;
				oCommand.CommandText = "SELECT [DTT_LastLogin] FROM [XU] WHERE [GID_UserID] = '" + sUserID + "'";
				oCommand.CommandType = System.Data.CommandType.Text;
				oCommand.Connection = oConnection;
				//execute
				oReader = oCommand.ExecuteReader();
				//read returned value
				if (oReader.HasRows)
				{
					//iCount = 1
					while (oReader.Read())
					{
						if (oReader["DTT_LastLogin"] == System.DBNull.Value)
						{
							//Never logged in
							dtResult = DateTime.Parse(clC.SELL_BLANK_DATETIME);
						}
						else
						{
							//Login datetime found
							dtResult = goTR.UTC_UTCToLocal(Convert.ToDateTime(oReader["DTT_LastLogin"]));
						}
					}
				}
				else
				{
					//XU record not found - a Login for the User has not been created
					dtResult = DateTime.Parse(clC.SELL_BLANK_DATETIME);
				}
				oReader.Close();
				oConnection.Close();
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return dtResult;
		}
		public string GetUserLogonID()
		{

			string sProc = "clProject::GetUserLogonID";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			return sUserLogonID;
		}
		public string GetUserName()
		{

			//Send back the user 'Name'                                       
			string sProc = "clProject::GetUserName";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			return sUserName;
		}
		public string GetUserPermissions()
		{
			//MI 2/7/07 Created.
			string sproc = "clproject::GetUserPermissions";
			return sUserPermissions;
		}

		public string GetUserPassWord()
		{
			//CS OK

			//PURPOSE:
			//		Returns the user password
			//PARAMETERS:
			//		N/A
			//RETURNS:
			//		a string
			//EXAMPLE:
			//		goP:GetuserPassword()

			string sProc = "clProject::GetUserPassWord";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)
			return sUserPass;
		}
		public string GetUserTID()
		{
			//CS oK

			//Send back the user Text ID                                       
			string sProc = "clProject::GetUserTID";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			return sUserTID;
		}
		public string GetUserVBPassword()
		{
			//CS OK

			string sProc = "clProject::GetUserVBPassword";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)
			return sUserVBPassword;
		}
		public void InitClass()
		{
			//CS In progress, WT review/finish

			//PURPOSE:
			//		Init the class
			//PARAMETERS:
			//		-
			//RETURNS:
			//		-
			//HOW IT WORKS:
			//		-
			//EXAMPLE:
			//		-

			string sProc = "clProject::InitClass";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			InitProject();

			//*** Comment these 2 lines
			//CS: Commented these 2 lines; WT translate/review.
			//glID = fopen(goTR.AddFinalSlash(gsProgDir) & "Trace.txt", FOCreateIfNotExist & FOAdd)
			//SellTraceMode(Iniread("LOG" & sRunMode, "SELLTRACE", numtostring(SELLTRACE_OFF), gsIniFile))

			SetLanguage();
		}
		public bool Initialize()
		{
				bool tempInitialize = false;

			string sProc = "clProject::Initialize";

			try
			{

				goP = (Selltis.BusinessLogic.clProject)HttpContext.Current.Session["goP"]; //*** MI 5/16/06 ***
				goTR = (Selltis.BusinessLogic.clTransform)HttpContext.Current.Session["goTr"]; //*** MI 5/16/06 ***
				goMeta = (Selltis.BusinessLogic.clMetaData)HttpContext.Current.Session["goMeta"]; //*** MI 5/16/06 ***
				goData = (Selltis.BusinessLogic.clData)HttpContext.Current.Session["goData"]; //*** MI 5/16/06 ***
				goErr = (Selltis.BusinessLogic.clError)HttpContext.Current.Session["goErr"]; //*** MI 5/16/06 ***
				goLog = (Selltis.BusinessLogic.clLog)HttpContext.Current.Session["goLog"]; //*** MI 5/16/06 ***
				//goDef = HttpContext.Current.Session("goDef")    '*** MI 5/16/06 ***
				//goScr = HttpContext.Current.Session("goScr")    '*** MI 5/16/06 ***
				//goNumMask = HttpContext.Current.Session("goNumMask")    '*** MI 7/12/06 ***
				//goUI = HttpContext.Current.Session("goUI")    '*** MI 8/22/06 ***
				goPerm = (Selltis.BusinessLogic.clPerm)HttpContext.Current.Session["goPerm"]; //*** MI 3/14/07 ***

				tempInitialize = true;

			}
			catch (Exception ex)
			{
				//Error initializing the project.
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If

				tempInitialize = false;
			}

			return tempInitialize;
		}
		public void InitProject()
		{
			//MI 6/22/11 Deprecated Customer Admin notion.
			//MI 1/14/09 Added initializing goP.sProduct
			//MI 5/18/07 Added loging last login datetime to the XU table
			//MI 3/14/07 Added reading permissions from goPerm, XP table instead of Members.mdb.
			//MI 3/12/07 Changed oledb connection to sqlclient.
			//MI 2/7/07 Removed old NGP code
			//MI 2/7/07 Added calling SetUserPermissions to set new permissions in this string
			//MI 12/6/06 Added reading Author and Admin state from sec database
			//MI 8/28/06 Removed unused variable
			//MI 5/17/06 Adding support for reading real User info
			//MI 5/15/06 Added fake user data, rearranged code
			//MI 4/12/06 added framework for pulling user info
			//MI 3/30/06 Enabled portion of LOGON section

			string sProc = "clProject::InitProject";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sPermissions = null;
			string sSQL = null;

			//========================= VERSION ==================================
			//Version is now in version.txt
			goP.gsVersion = "5.0 20070518";

			//======================== RUN MODE ==================================
			//If HttpContext.Current.Session("LOGINRUNMODE") = "MAIN" Then
			if (HttpContext.Current.Session[clSettings.GetHostName() + "_" + "LOGINRUNMODE"] == "MAIN")
			{
				goP.sRunMode = clC.SELL_MODE_MAIN;
			}
			HttpContext.Current.Session[clSettings.GetHostName() + "_" + "LOGINRUNMODE"] = null;

			//====================== PRODUCT =====================================
			goP.sProduct = Convert.ToString(HttpContext.Current.Session["sProduct"]);
			//Set 'SA' (Sales) as the default product if the product session var hasn't been set
			if (goP.sProduct == null || goP.sProduct == "")
			{
				goP.sProduct = "SA";
			}

			//========================= LOGON ====================================
			//User ID is established in clAccessMembershipProvider.ValidateUser
			try
			{
				if (HttpContext.Current.Session["USERID"].ToString() == "")
				{
					goErr.SetWarning(35000, sProc, "User was redirected to logon because session 'USERID' is blank.");
					HttpContext.Current.Response.Redirect("~/pages/redirect.aspx", true);
				}
				else
				{
					goP.SetUserTID(Convert.ToString(HttpContext.Current.Session["USERID"])); //2002020715000000001FH__00001US")
				}
			}
			catch (Exception ex)
			{
				HttpContext.Current.Response.Redirect("~/pages/redirect.aspx", true);
			}

			goP.SetUserLogon(Convert.ToString(HttpContext.Current.Session[clSettings.GetHostName() + "_" + "LOGINNAME"]));
			goP.SetUserLogonID(Convert.ToString(HttpContext.Current.Session["LOGINID"]));

			//'---------- Read security data ---------------
			//'Dim oCrypt As New Crypto.SymmCrypto
			//'oCrypt.SymmCrypto(Crypto.SymmCrypto.SymmProvEnum.DES)
			//'sConnStr = oCrypt.Decrypting(HttpContext.Current.Session("ConnString"), "SelltisKey1")
			//Available login info:
			//HttpContext.Current.Session("USERID")
			//HttpContext.Current.Session("LOGINID")
			//HttpContext.Current.Session("LOGINNAME")

			//--------------- Log last login datetime in XU table ---------------
			//Log only user logins, not logins by utilities
			if (goP.sRunMode == clC.SELL_MODE_MAIN)
			{
				goP.sRunMode = clC.SELL_MODE_MAIN;
				sSQL = "UPDATE [XU] SET [DTT_LastLogin]=GetUTCDate() WHERE [GID_ID]='" + Convert.ToString(HttpContext.Current.Session["LOGINID"]) + "'";
				goData.RunSQLQuery(sSQL);
			}

			//------------- Set roles and feature permissions --------------
			goP.SetUserPass(""); //Avoid loading the password here for security reasons (unless really necessary)
			sPermissions = goPerm.PageRead(goP.GetUserLogonID(), "ROLES");
			goP.SetUserAuthor(Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sPermissions, "AUTHOR", "0"))));
			if (goTR.StrRead(sPermissions, "ADMIN", "0") == "1")
			{
				goP.SetUserAdmin(true);
			}
			else
			{
				goP.SetUserAdmin(false);
			}

			//MI 6/22/11 Deprecating the notion of customer admin. Now the user is a 'customer admin' if (s)he
			//has at least one administrative permission.
			//If goTR.StrRead(sPermissions, "ADMINCUSTOMER", "0") = "1" Then
			//    goP.SetUserCustomerAdmin(True)
			//Else
			//    goP.SetUserCustomerAdmin(False)
			//End If

			//Roles and feature permissions
			//   CUSTADMINMANAGELOGINS
			//   CUSTADMINMANAGEFEATURES
			//   CUSTADMINMANAGEDATAACCESS
			//   WORKGROUPOPTIONS
			//   CUSTADMINREPORTS
			//   CUSTADMINREASSIGNRECORDS
			sPermissions = goPerm.PageRead(goP.GetUserLogonID(), "FEATURES", goPerm.PageRead(goP.GetUserLogonID(), "ROLES", goPerm.GetDefaultFeaturePermissions()));

			goP.SetUserPermissions(sPermissions);


			//------------ Set various defaults -----------------
			goP.SetVar("sMandatoryFieldColor", "#BE0000");
			//goP.SetVar("sMandatoryFieldColor", System.Drawing.Color.DarkRed)   'Color of labels for fields that are mandatory

			goP.SetVar("sHttpString", "https:"); //or "http"

			//'goP.SetUserCode(ConfigurationManager.AppSettings("usercode"))   '"MAI"
			//'goP.SetUserTID(ConfigurationManager.AppSettings("id"))     '2002020715000000001FH__00001US")
			//The following doesn't set the variable in the proper session object
			//gsUserID = ConfigurationManager.AppSettings("usercode") '"MAI"
			//DEBUG
			//s = goP.GetUserTID()
			//s = goP.GetUserCode()
			int iCount = 0;

			//--------------- Fill user (US record) info ---------------
			//*** MI 5/17/06 trying to have goP accessible in clData.GetConnection
			//*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
			System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
			System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader oReader = null;
			int iResult = 0;
			string sConnStr = "";

			//Try
			oCommand.CommandText = "pGetUserInfo";
				oCommand.CommandType = System.Data.CommandType.StoredProcedure;
				oCommand.Connection = oConnection;

				//parameter
				System.Data.SqlClient.SqlParameter sParCode = new System.Data.SqlClient.SqlParameter("@par_sCode", System.Data.SqlDbType.VarChar);
				sParCode.Value = System.DBNull.Value;
				oCommand.Parameters.Add(sParCode);

				//parameter
				System.Data.SqlClient.SqlParameter uParGID = new System.Data.SqlClient.SqlParameter("@par_uGID", System.Data.SqlDbType.UniqueIdentifier);
				System.Guid guidValue = Guid.TryParse(goP.GetUserTID(), out guidValue) ? guidValue : Guid.Empty;
				uParGID.Value = guidValue;
				oCommand.Parameters.Add(uParGID);

				//'return parameter - not used
				//Dim retValParam As New System.Data.SqlClient.SqlParameter("@RETURN_VALUE", System.Data.SqlDbType.Int)
				//retValParam.Direction = System.Data.ParameterDirection.ReturnValue
				//oCommand.Parameters.Add(retValParam)

				//execute
				//oConnection.Open()
				oReader = oCommand.ExecuteReader();

				//'Now you can grab the output parameter's value - not used here
				//iResult = Convert.ToInt16(retValParam.Value)

				//read returned value
				if (oReader.HasRows)
				{
					iCount = 1;
					while (oReader.Read())
					{
						if (iCount > 1)
						{
							goErr.SetError(35400, sProc, "", goP.GetUserCode());
							//35400: More than one user found with the same User Code '[1]'.
							break;
						}
						goP.SetUserTID(oReader["GID_ID"].ToString());
						goP.SetUserName(oReader["SYS_Name"].ToString());
						goP.SetUserLocation(oReader["LNK_At_LO%%GID_ID"].ToString());
						goP.SetUserCode(oReader["TXT_Code"].ToString()); //"MAI"

						iCount = iCount + 1;
					}
				}

				oReader.Close();
				oConnection.Close();


			//Catch ex As Exception
			//    'Error reading user information
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc, , "Error reading user information")
			//    End If
			//End Try

			//DEBUG
			//Uncomment to force using particular user's info
			//goP.SetUserName("Igrec, Mario")
			//goP.SetUserPass("")
			//goP.SetUserLocation("23C34E3A-5DDE-445C-4C4F-9752007D8F86") '23C34E3A-5DDE-445C-4C4F-9752007D8F86='Selltis'
			//goP.SetUserAdmin(False)
			//goP.SetUserAuthor(False)
			//goP.SetUserLogon("MARIOI")


		}

		public void InitProject2()
		{
			string sProc = "clProject::InitProject2";
			//MI 9/10/09 Changed goTR.oUserTimeZone.GetUtcOffset to goTR.UTC_GetUtcOffset.
			//PURPOSE:
			//       Stage 2 of project initialization that allows for classes to be
			//       fully initialized before this is run.
			//       Run this from clInit.Initialize() AFTER goP.InitClass(), goTR.UTC_InitTimeZones(),
			//       and all other additional class initializations.

			string sTZOffset = null;
			string sTZLocale = null;
			string sTZGlobalLocale = null;
			string sServerTZ = null;
			clTable t = null;
			int i = 0;
			string sURL = null;
			string sTemp = null;

			//Try

			//-------- Set server's time zone -----------
			sServerTZ = TimeZone.CurrentTimeZone.DaylightName;
				t = goTR.UTC_GetTimeZonesTable();
				i = (int)t.Seek("WindowsName", sServerTZ);
				if (i < 1)
				{
					//Olson entry not found for the Windows Daylight Name of the time zone on the server
					goErr.SetError(35000, sProc, "The server's Windows time zone '" + sServerTZ + "' wasn't found in goTR.UTC_GetTimeZonesTable(), 'WindowsName' column. Select a different time zone on the server.");
					//goTR.tsServerOffset = TimeZone.CurrentTimeZone.GetUtcOffset(Now)
				}
				else
				{
					sTemp = t.GetVal("Value", i).ToString();
					if (sTemp == "")
					{
						goTR.oServerTimeZone = null;
					}
					else
					{
						goTR.oServerTimeZone = PublicDomain.TzTimeZone.GetTimeZone(sTemp);
					}
					if (goTR.oServerTimeZone == null)
					{
						//Invalid locale
						goErr.SetError(35000, sProc, "Olson time zone '" + t.GetVal("Value", i).ToString() + "' can't be found and the server's time zone can't be set. Windows time zone daylight name is '" + sServerTZ + "'.");
						//goTR.tsServerOffset = TimeZone.CurrentTimeZone.GetUtcOffset(Now)
					}
				}

				//---------- Set UTC time zone -----------
				//PublicDomain.TzTimeZone.TreatUnspecifiedKindAsLocal = False
				goTR.oUTCTimeZone = PublicDomain.TzTimeZone.GetTimeZone("GMT");
				if (goTR.oUTCTimeZone == null)
				{
					//Invalid locale
					goErr.SetError(35000, sProc, "Olson time zone 'GMT' can't be found.");
					//goTR.tsServerOffset = TimeZone.CurrentTimeZone.GetUtcOffset(Now)
				}

				//---------- Check user's time zone locale -------------
				sTZOffset = Convert.ToString(HttpContext.Current.Session[clSettings.GetHostName() + "_TimeZoneOffset"]);
				sTZLocale = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "TZLOCALE", "", true);
				if (sTZLocale == "")
				{
					//No POP definition found
					//Redirect the user to the TZ selection page and pre-select the WOP default TZ
					sTZGlobalLocale = goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "TZLOCALE", "", true);
					if (HttpContext.Current.Session[clSettings.GetHostName() + "_LoginIsFromUI"] == "1")
					{
						//UI login
						sURL = "";
						goTR.URLWrite(ref sURL, "|URLAddress|", "~/TimeZone.aspx");
						goTR.URLWrite(ref sURL, "locale", sTZGlobalLocale);
						goTR.URLWrite(ref sURL, "offset", sTZOffset);
						HttpContext.Current.Session[clSettings.GetHostName() + "_RedirectToPage"] = sURL;
						//HttpContext.Current.Response.Redirect(sURL, True)
					}
					else
					{
						//Webservice login
						goErr.SetWarning(35000, sProc, "Time zone locale definition TZLOCALE not found in POP_PERSONAL_OPTIONS for non-UI (webservice) login '" + goP.GetUserLogon() + "', user '" + goP.GetUserCode() + "' (" + goP.GetUserTID() + "). Attempting to initialize WOP_WORKGROUP_OPTIONS zone '" + sTZGlobalLocale + "' or zone 'GMT'.");
						if (sTZGlobalLocale == "")
						{
							goErr.SetWarning(35000, sProc, "Time zone is not defined in TZLOCALE of WOP_WORKGROUP_OPTIONS page. Zone 'GMT' is used.");
							//No POP or WOP definition found: assume a locale 'GMT' for webservices
							goTR.oUserTimeZone = PublicDomain.TzTimeZone.GetTimeZone("GMT");
							if (goTR.oUserTimeZone == null)
							{
								goErr.SetError(35000, sProc, "Error initializing time zone PublicDomain.TzTimeZone.GetTimeZone('GMT').");
							}
						}
						else
						{
							//WOP definition found, use it
							goTR.oUserTimeZone = PublicDomain.TzTimeZone.GetTimeZone(sTZGlobalLocale);
							if (goTR.oUserTimeZone == null)
							{
								goErr.SetWarning(35000, sProc, "PublicDomain.TzTimeZone.GetTimeZone error: zone '" + sTZGlobalLocale + "' defined in TZLOCALE of WOP_WORKGROUP_OPTIONS can't be initialized. Attempting to initialize zone 'GMT'.");
								//WOP definition not valid, use GMT
								goTR.oUserTimeZone = PublicDomain.TzTimeZone.GetTimeZone("GMT");
								if (goTR.oUserTimeZone == null)
								{
									goErr.SetError(35000, sProc, "Error initializing time zone PublicDomain.TzTimeZone.GetTimeZone('GMT').");
								}
							}
						}
					}
				}
				else
				{
					//POP definition is found
					goTR.oUserTimeZone = PublicDomain.TzTimeZone.GetTimeZone(sTZLocale);
					if (goTR.oUserTimeZone == null)
					{
						//Invalid POP locale
						sTZGlobalLocale = goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "TZLOCALE", "", true);
						if (HttpContext.Current.Session[clSettings.GetHostName() + "_LoginIsFromUI"] == "1")
						{
							//UI login
							sURL = "";
							goTR.URLWrite(ref sURL, "|URLAddress|", "~/TimeZone.aspx");
							goTR.URLWrite(ref sURL, "locale", sTZGlobalLocale);
							goTR.URLWrite(ref sURL, "offset", sTZOffset);
							HttpContext.Current.Session[clSettings.GetHostName() + "_RedirectToPage"] = sURL;
							//We are redirecting to the TimeZone page from Default.aspx, not from here,  
							//that's what the session variable "RedirectToPage" is for.
							//HttpContext.Current.Response.Redirect(sURL, True)
						}
						else
						{
							//Webservice login
							if (sTZGlobalLocale == "")
							{
								//No POP or WOP definition found: assume a locale 'GMT' for webservices
								goErr.SetWarning(35000, sProc, "Time zone locale '" + sTZLocale + "' defined in TZLOCALE in POP_PERSONAL_OPTIONS for non-UI (webservice) login '" + goP.GetUserLogon() + "', user '" + goP.GetUserCode() + "' (" + goP.GetUserTID() + ") is invalid and can't be initialized. Attempting to initialize zone 'GMT'.");
								goTR.oUserTimeZone = PublicDomain.TzTimeZone.GetTimeZone("GMT");
								if (goTR.oUserTimeZone == null)
								{
									goErr.SetError(35000, sProc, "Error initializing time zone PublicDomain.TzTimeZone.GetTimeZone('GMT').");
								}
							}
							else
							{
								//WOP definition found, use it
								goTR.oUserTimeZone = PublicDomain.TzTimeZone.GetTimeZone(sTZGlobalLocale);
								if (goTR.oUserTimeZone == null)
								{
									//WOP definition not valid, use GMT
									goErr.SetWarning(35000, sProc, "PublicDomain.TzTimeZone.GetTimeZone error: TZLOCALE zone in WOP_WORKGROUP_OPTIONS can't be initialized: '" + sTZGlobalLocale + "'. Attempting to initialize zone 'GMT'.");
									goTR.oUserTimeZone = PublicDomain.TzTimeZone.GetTimeZone("GMT");
									if (goTR.oUserTimeZone == null)
									{
										goErr.SetError(35000, sProc, "Error initializing time zone PublicDomain.TzTimeZone.GetTimeZone('GMT').");
									}
								}
							}
						}
					}
					else
					{
						//Valid POP locale
						//Compare the user's curent client-side time zone offset to TZLOCALE setting in POP MD
						if (goTR.UTC_GetUtcOffset(DateTime.Now, goTR.oUserTimeZone).TotalMinutes.ToString() != sTZOffset)
						{
							if (HttpContext.Current.Session[clSettings.GetHostName() + "_LoginIsFromUI"] == "1")
							{
								//UI login
								sURL = "";
								goTR.URLWrite(ref sURL, "|URLAddress|", "~/TimeZone.aspx");
								goTR.URLWrite(ref sURL, "locale", sTZLocale);
								goTR.URLWrite(ref sURL, "offset", sTZOffset);
								HttpContext.Current.Session[clSettings.GetHostName() + "_RedirectToPage"] = sURL;
								//We are redirecting to the TimeZone page from Default.aspx, not from here,  
								//that's what the session variable "RedirectToPage" is for.
								//HttpContext.Current.Response.Redirect(sURL, True)
							}
							else
							{
								//Non-UI login such as a webservice, let it pass
							}
						}
					}
				}

			//HttpContext.Current.Session("LoginIsFromUI") = Nothing

			//Catch ex As Exception
			//    'Error reading user information
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc, , "Error reading user information")
			//    End If
			//End Try


		}

		public bool InitRunMode(string par_sRunMode = clC.SELL_MODE_MAIN)
		{
			//CS PORTED as placeholder.

			//PURPOSE:
			//		Read the command line and depending of the parameters, sets the current run mode
			//PARAMETERS:
			//		par_sRunMode:	default mode: if not determined by code, uses this one
			//RETURNS:
			//		Nothing
			//HOW IT WORKS:
			//		--
			//EXAMPLE:
			//		clProject::InitRunMode()

			string sProc = "clProject::InitRunMode";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)


			//CS: Commented here to end.
			//oCL is clCommandLine()
			//sNewInstance is String

			//        sNewInstance = oCL : Command_ReadParam("NI")

			//sWork is string
			//iI is int
			//if sNewInstance~="" THEN
			//            ' -> pas de chaine New instance. Lancement standard ou deuxi�me lancement manuel.
			//            'No new instance string. Standard mode.
			//            If nospace(par_sRunMode) <> "" Then
			//                : sRunMode = par_sRunMode
			//                result(True)
			//            Else
			//                : sRunMode = SELL_MODE_MAIN
			//                Result(False)    'NI without the proper action code
			//                End
			//ELSE
			//                ' -> Ici tu as la chaine au format MetaData 
			//                ' -> Exemple de chaine
			//                ' DB=1			-> DB number to use in ini file.
			//                ' US=User Name		-> User Name
			//                ' PW=Password		-> Password
			//                ' A=Action			-> Action Code 
			//                ' AA=Action complement	-> Action Complement 
			//                iI = 1

			//	loop
			//            sWork = Extractstring(sNewInstance, iI, rc)
			//            If sWork = eot Then break()
			//            Switch(extractstring(sWork, 1, "="))
			//		case "DB"
			//			::sDataBase=sWork[[4 to]]
			//		case "US"
			//			::sUser=sWork[[4 to]]
			//		case "PW"
			//			::sPassWord=sWork[[4 to]]
			//		case "A"
			//			::sAction=sWork[[3 to]]
			//		case "AA"
			//			::sAComp=sWork[[4 to]]
			//		case "H"
			//			::lNGPHandle=sWork[[3 to]]
			//		other case
			//            'Nothing to do yet not supported
			//            End
			//		iI++
			//            End

			//	switch val(::sAction)
			//	case SELL_NI_OPENFORM,SELL_NI_OPENFEATURE,SELL_NI_OPENDESKTOP  'Opening form, Feature or desktop in new instance
			//		if not clProject::TestNGPHandle(::lNGPHandle) then result false
			//                : sRunMode = SELL_MODE_NEWINSTANCE
			//	case SELL_NI_SYNCENGINE		'Opening Sync Engine
			//                : sRunMode = SELL_MODE_SYNC
			//	case SELL_NI_AUTOMATOR			' Opening SellAuto
			//                : sRunMode = SELL_MODE_AUTO
			//	case SELL_NI_ALERT				'JL Opening SellAlrt
			//                : sRunMode = SELL_MODE_ALERT  'JL
			//	case SELL_NI_NGP				'JL Opening Selltis.exe
			//                : sRunMode = SELL_MODE_MAIN   'JL
			//	Other case
			//                If nospace(par_sRunMode) <> "" Then
			//                    : sRunMode = par_sRunMode
			//                    result(True)
			//                Else
			//                    Result(False)    'NI without the proper action code
			//                    End
			//                    End
			//                    End

			//                    result(True)
			//                    'Proc�dure global clProject::InitRunMode(par_sRunMode=SELL_MODE_MAIN)  
			//                    ''PURPOSE:
			//                    ''		Read the command line and depending of the parameters, sets the current run mode
			//                    ''PARAMETERS:
			//                    ''		par_sRunMode:	default mode: if not determined by code, uses this one
			//                    ''RETURNS:
			//                    ''		Nothing
			//                    ''HOW IT WORKS:
			//                    ''		--
			//                    ''EXAMPLE:
			//                    ''		clProject::InitRunMode()
			//                    '
			//                    'sProc is string="clProject::InitRunMode"
			//                    'if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)
			//                    '
			//                    'oCL is clCommandLine()
			//                    'sNewInstance is String
			//                    ''oTr is clTransform()
			//                    '
			//                    'sNewInstance=oCL:Command_ReadParam("NI")
			//                    '
			//                    'sWork is string
			//                    'iI is int
			//                    'if sNewInstance~="" THEN
			//                    '    ' -> pas de chaine New instance. Lancement standard ou deuxi�me lancement manuel.
			//                    '	'No new instance string. Standard mode.
			//                    '	if nospace(par_sRunMode)<>"" then
			//                    '		::sRunMode=par_sRunMode
			//                    '		result true
			//                    '	else
			//                    '		::sRunMode=SELL_MODE_MAIN
			//                    '		Result false	'NI without the proper action code
			//                    '	end
			//                    'ELSE
			//                    '    ' -> Ici tu as la chaine au format MetaData 
			//                    '    ' -> Exemple de chaine
			//                    '    ' DB=1			-> DB number to use in ini file.
			//                    '    ' US=User Name		-> User Name
			//                    '    ' PW=Password		-> Password
			//                    '    ' A=Action			-> Action Code 
			//                    '    ' AA=Action complement	-> Action Complement 
			//                    '	iI=1
			//                    ''info(sNewInstance)
			//                    '	loop
			//                    '		sWork=Extractstring(sNewInstance,iI,rc)
			//                    '		if sWork=eot then break
			//                    '		switch extractstring(sWork, 1, "=")
			//                    '		case "DB"
			//                    '			::sDataBase=sWork[[4 to]]
			//                    '		case "US"
			//                    '			::sUser=sWork[[4 to]]
			//                    '		case "PW"
			//                    '			::sPassWord=sWork[[4 to]]
			//                    '		case "A"
			//                    '			::sAction=sWork[[3 to]]
			//                    '		case "AA"
			//                    '			::sAComp=sWork[[4 to]]
			//                    '		case "H"
			//                    '			::lNGPHandle=sWork[[3 to]]
			//                    '		other case
			//                    '			'Nothing to do yet not supported
			//                    '		end
			//                    '		iI++
			//                    '	END
			//                    '	
			//                    ''info(::sDataBase, ::sUser, ::sPassWord, ::sAction, ::sAComp, ::lNGPHandle)
			//                    '
			//                    '	switch val(::sAction)
			//                    '	case SELL_NI_OPENFORM,SELL_NI_OPENFEATURE,SELL_NI_OPENDESKTOP  'Opening form, Feature or desktop in new instance
			//                    '		if not clProject::TestNGPHandle(::lNGPHandle) then result false
			//                    '		::sRunMode=SELL_MODE_NEWINSTANCE
			//                    '	case SELL_NI_SYNCENGINE		'Opening Sync Engine
			//                    '		::sRunMode=SELL_MODE_SYNC
			//                    '	case SELL_NI_AUTOMATOR			'JL Opening SellAuto
			//                    '		::sRunMode=SELL_MODE_AUTO	'JL
			//                    '	Other case
			//                    '		if nospace(par_sRunMode)<>"" then
			//                    '			::sRunMode=par_sRunMode
			//                    '			result true
			//                    '		else
			//                    '			Result false	'NI without the proper action code
			//                    '		end
			//                    '	end
			//                    'END
			//                    '
			//                    'result true

			//CS: Remove this
			return true;
		}

		public void VerifyDatabaseID()
		{
			//PURPOSE:
			//		Tests existance of DATABASEID in WOP and creates it if it does not exist
			//PARAMETERS:
			//		None
			//RETURNS:
			//		Nothing
			//AUTHOR:
			//		RH

			if (goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "DATABASEID", "") == "")
			{
				System.Data.SqlClient.SqlConnection tempVar = null;
				goMeta.LineWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", "DATABASEID", goData.GenGuid(), ref tempVar, "", "XX");
			}

		}
		public string GetDatabaseID()
		{
			//PURPOSE:
			//		Tests existance of DATABASEID in WOP, creates it if it does not exist, and returns it.
			//PARAMETERS:
			//		None
			//RETURNS:
			//		DATABASEID
			//AUTHOR:
			//		RH

			string sDatabaseID = "";

			sDatabaseID = goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "DATABASEID", "", false);

			if (sDatabaseID == "")
			{
				System.Data.SqlClient.SqlConnection tempVar = null;
				goMeta.LineWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", "DATABASEID", goData.GenGuid(), ref tempVar, "", "XX");
			}

			return goMeta.LineRead(goP.GetUserTID(), "WOP_WORKGROUP_OPTIONS", "DATABASEID", "", false);


		}
		public bool IsObjectAssigned(object par_doObject)
		{
			//CS PORTED as placeholder. Always return True.

			//PURPOSE:
			//		Test if the parameter sent is a valid object. Now we write "If oObject is nothing then..."
			//PARAMETERS:
			//		par_doObject:	A supposed pointer on an object
			//RETURNS:
			//		True if the object is correctly assigned, false if not or if not an object at all (string, etc..)
			//HOW IT WORKS:
			//		Intercept some specific exception occurring when setting the object received in a real object pointer
			//EXAMPLE:
			//		if not goP:IsObjectAssigned(doForm) then

			//-----------------------------------------------
			//SEE ALSO: goP:AssignObject(doObject, lAddress)
			//-----------------------------------------------

			//Not logginf for speed optimisation
			//sProc is string="clProject::IsObjectAssigned"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//CS: Commented from here to end. Always true
			return true;
		}
		public bool IsPCLinkSupported()
		{
			//MI 4/11/11 Created.
			//PURPOSE:
			//       Test whether the current environment supports PC Link functionality
			//       such as printing and sending (any kind of job processing).
			//RETURNS:
			//       Boolean

			string sProc = "clProject::IsPCLinkSupported";

			if (HttpContext.Current.Session["SA_ENVIRONMENT"] == "APPLE")
			{
				return false;
			}
			else
			{
				return true;
			}
		}
		public bool IsUserAdmin()
		{
			//CS OK

			//PURPOSE:
			//		Return whether the user has admin permissions
			//PARAMETERS:
			//		None                                                                 
			//RETURNS:
			//		boolean True/False
			//HOW IT WORKS:
			//		Call whenever you need to find out whether the user has admin permissions
			//		Admin permissions determine whether the user is allowed to
			//		create, modify, and delete data in all data files without restrictions
			//		that regular users have
			//		import templates, etc.
			//EXAMPLE:
			//		IF goP.IsUserAdmin() THEN <process>

			string sProc = "clProject::IsUserAdmin";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			return bUserIsAdmin;

		}

		public bool IsUserCustomerAdmin()
		{
			//MI 6/22/11 Changed 6/22/11 to returning whether the user has at least one permission that qualifies him/her as 'customer admin'.
			//PURPOSE:
			//       Returns whether the user has the ability to get into Admin>User/Group Permissions.
			//       Previously, Customer Admin was a monolithic notion like sysadmin, and was set
			//       in diaAdmPerm via a checkbox.
			//PARAMETERS:
			//  None                                                                 
			//RETURNS:
			//  boolean True/False
			//EXAMPLE:
			//  IF goP.IsUserCustomerAdmin() THEN <process>

			string sProc = "clProject::IsUserCustomerAdmin";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//If goP.GetMe("PERMCUSTADMINMANAGELOGINS") = "1" Or goP.GetMe("PERMCUSTADMINMANAGEFEATURES") = "1" Or goP.GetMe("PERMCUSTADMINMANAGEDATAACCESS") = "1" Or goP.GetMe("PERMWORKGROUPOPTIONS") = "1" Or goP.GetMe("PERMCUSTADMINREPORTS") = "1" Or goP.GetMe("PERMCUSTADMINREASSIGNRECORDS") = "1" Then
			//Only the permissions that allow the user to manage permissions for others qualify the user as 'cust admin'.
			if (goP.GetMe("PERMCUSTADMINMANAGELOGINS") == "1" || goP.GetMe("PERMCUSTADMINMANAGEFEATURES") == "1" || goP.GetMe("PERMCUSTADMINMANAGEDATAACCESS") == "1" || goP.GetMe("PERMWORKGROUPOPTIONS") == "1")
			{
				return true;
			}
			else
			{
				return false;
			}
			//Return bUserIsCustomerAdmin
		}
		public int IsUserAuthor()
		{
			//CS OK

			//PURPOSE:
			//		Return whether the user has author permissions
			//PARAMETERS:
			//		None
			//RETURNS:
			//		integer: 0=not author; 1=limited author; 2=full author
			//HOW IT WORKS:
			//		Call whenever you need to find out whether the user has author permissions
			//		Author permissions determine whether the user is allowed to
			//		create, modify, and delete shared metadata objects such as desktops, views,
			//		import templates, etc.
			//EXAMPLE:
			//		IF goP:IsUserAuthor THEN <process>

			string sProc = "clProject::IsUserAuthor";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			return iUserAuthorState;
		}
		public string LangFirst()
		{
			//CS OK

			//AUTHOR: MI 7/30/2002

			//PURPOSE:
			//		Return the code of the first supported language.
			//RETURNS:
			//		2-chracter language code or "" if out of range.
			string sProc = "clProject::LangFirst";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string s = null;

			iLangPosition = 1;
			s = goTR.ExtractString(GetSupportedLangs(), iLangPosition);
			if (s[0] == clC.EOT)
			{
				return "";
			}
			else
			{
				return s;
			}
		}
		public string LangNext()
		{
			//CS OK

			//AUTHOR: MI 7/30/2002

			//PURPOSE:
			//		Return the code of the "next" supported language.
			//		Which language is "next" is determined by the
			//		iLangPosition variable
			//RETURNS:
			//		2-chracter language code or "" if out of range.
			string sProc = "clProject::LangNext";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string s = null;

			iLangPosition = iLangPosition + 1;
			s = goTR.ExtractString(GetSupportedLangs(), iLangPosition);
			if (s[0] == clC.EOT)
			{
				return "";
			}
			else
			{
				return s;
			}
		}
		public void Multitask(long par_lValue = 0, string par_sProcedure = "")
		{
			//PORT empty just to avoid having to remove calls to this in the calling code.

		}
		public int GetNation()
		{

			//PURPOSE:
			//       Retrieve the value of the variable that stores the current locale.
			//       Use GetlangCode to retrieve the language code as string from the locale.
			//PARAMETERS:
			//       None.
			//RETURNS:
			//       Nation session variable. 3=US, and is the default.

			//goLog.SetError()

			return giNation;

		}

		public void SetNation(int par_iNation)
		{
			//MI 6/21/13 Added. Initially one method, Nation() was reading and setting the locale code.
			//Localization in Selltis is not complete. Currently, 3 (US) is the default, but
			//can be changed with this method.

			if (par_iNation == 0)
			{
				//paramter was not passed so we are getting the nation/language
				giNation = 3; //Default=US
			}
			else
			{
				giNation = par_iNation;
			}

		}

		public string NotEOT(string par_sTextValue)
		{
			//CS OK

			//Return the original value or an empty string if the orignal value is EOT
			string sProc = "clProject::NotEOT";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			if (par_sTextValue[0] == clC.EOT)
			{
				return "";
			}
			else
			{
				return par_sTextValue;
			}
		}
		public void OpenUtilities()
		{
			//CS PORTED as placeholder
			//DEPRECATED
			//PURPOSE:
			//		Launch auxiliary programs needed to NGP.

			//PARAMETERS:
			//		N/A

			//RETURNS:
			//		N/A

			//HOW IT WORKS:
			//		For each program, launching is conditional.

			//EXAMPLE:
			//		goP:OpenUtilities()

			string sProc = "clProject::OpenUtilities";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start",SELL_LOGLEVEL_DEBUG)

		}
		public string RunAction(string par_sActionName, string par_sActionModeOrID = "", object par_doObject = null, object par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
		{
			//CS PORTEED as placeholder (from w_proc)

			//---------
			//SET ERROR
			//---------
			//AUTHOR: FH		MOD BY MI 3/1/05
			//PURPOSE:
			//                Create an action by calling the clAction::AddAction method.
			//                The action is stacked and will be exeuted next time the action engine is active
			//				  (normally NOT when you are adding one from a script), in the order of the actions in the stack.
			//PARAMETERS:
			//
			//		ALL TEXT PARAMS ARE CASE INSENSITIVE 
			//
			//                par_sActionName:         Name of the action to execute... Can be:
			//                                                        "OpenForm", "RunAutomator", "OpenDesktop"
			//                par_sActionModeOrID:     Action mode or ID of the desktop, automator, or script
			//											When an action can have different modes, the mode information is there:
			//														OpenDesktop: Desktop Page ID or its metadata.
			//                                                        OpenForm supports: 
			//                                                                - Edit (open an existing record in edit mode), 
			//                                                                - CreateLinkedByID (Open a form in Creation mode, linked to an existing record)
			//                                                                - CreateUnlinked (Open a form in creation mode, unlinked)
			//                                                                - CreateBlank (Open a form in creation mode, without proceedng even to the fields
			//                                                                        init done in create unlinked)
			//														RunAutomator: Automator ID or its metadata.
			//                par_doObject:                Depending of the action, can be any type of object (calling object, or anything else)
			//                par_doArray:                Doarray object allowing to pass to the action an unlimitted number of parameters
			//                par_s1:                                1st main param of the action (allows to pass it without declaring an array)
			//                                                        - For OpenForm: file name
			//                                                        - For RunAutomator: ""
			//														  - For OpenDesktop: ""
			//                par_s2:                                2nd main param of the action (allows to pass it without declaring an array)
			//                                                        - For OpenForm:        par_s2 is the TID of the record to open (mode EDIT) or to link to 
			//                                                                                                        (Mode CreateLinkedByID)
			//                par_s3:                                3rd main param of the action (allows to pass it without declaring an array)
			//                par_s4:                                4rth main param of the action (allows to pass it without declaring an array)
			//                par_s5:                                5th main param of the action (allows to pass it without declaring an array)
			//RETURNS:
			//                The number of the created action (like in a filter) or 0 if an error occurred, with a seterror.
			//HOW IT WORKS:
			//                Relay the calls to clAction::AddAction and return it's result
			//EXAMPLE:
			//                RunAction("OpenForm", "Edit", NULL, NULL, sTID)

			//3/1/05	MI	W_PROC.RunAction				For RunAutomator, OpenDesktop, and RunScript, made ID passable via
			//												the par_sActionModeOrID: parameter, not par_s1.

			//sProc is string="w_Proc::RunAction"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_sActionName+" "+par_sActionModeOrID, SELL_LOGLEVEL_DEBUG)

			return ""; //cs remove

			//'goAction:StopTimer("DEBUG", sproc)
			//'stop
			//'First verify if parameter values are supported
			//sActionName is string=upper(nospace(par_sActionName))
			//sActionModeOrID is string=upper(nospace(par_sActionModeOrID))
			//s1 is string=par_s1
			//s2 is string=par_s2
			//s3 is string=par_s3
			//s4 is string=par_s4
			//s5 is string=par_s5
			//doObject is object dynamic=par_doObject
			//doArray is object dynamic=par_doArray

			//goLog:SetError()

			//switch sActionName
			//case SELL_A_OPENDESKTOP
			//        'The sActionModeOrID parameter is supposed to contain either the pageID of a desktop meta record, or it's content
			//        'but we need to pass to addAction one or theother, with a flag.
			//		s1=sActionModeOrID
			//        if goTr:IsPageID(sActionModeOrID) then
			//			's1 doesn't change
			//			s2=""			'There is no meta content, we are working on the page ID
			//			s3="0"			'False: NOT from string
			//		else
			//			s1=""			'There isno PageID
			//			s3="1"			'TRUE: coming from the meta string
			//        end
			//		sActionModeOrID=""
			//CASE SELL_A_DESKTOPREFRESH		'"DESKTOPREFRESH"
			//	if goP:IsObjectAssigned(doMain) then
			//		doArray=doMain:GetContext()
			//		'Refresh the desktop if there is an active desktop in the context
			//		if val(doArray:Get(1))<>0 then
			//			s1=""
			//			s2=""
			//			s3=""
			//			doObject=NULL
			//			sActionModeOrID=""
			//			s4=""
			//			s5=""
			//			'goAction:AddAction(SELL_A_DESKTOPREFRESH, "", "", "", Null, "", doCtx)
			//		else
			//			'No desktop to refresh
			//			goLog:SetError(30009, sProc, "")
			//			' There is no open desktop.
			//			Result 0
			//		end
			//	end	
			//case SELL_A_RUNAUTOMATOR
			//        'The sActionModeOrID parameter is supposed to conatin either the pageID of an automator meta record,
			//		'or its content, but we need to pass to addAction the content only.
			//		s1=sActionModeOrID
			//        if goTr:IsPageID(s1) then
			//            s1=goMeta:PageRead(goP:GetUserTID(), s1)
			//			'Else s1 already contains the meta page
			//        end
			//		sActionModeOrID=""
			//case SELL_A_RUNSCRIPT
			//        'The sActionModeOrID parameter is supposed to conatin either the pageID of a script meta record,
			//		'or its content, but we need to pass to add a script the content only.
			//		s1=sActionModeOrID
			//        if goTr:IsPageID(s1) then
			//            s1=goMeta:PageRead(goP:GetUserTID(), s1)
			//			'Else s1 already contains the meta page
			//        end
			//		sActionModeOrID=""
			//case SELL_A_FORMCREATE
			//        'Test the subparam action mode
			//        switch sActionModeOrID
			//        case SELL_AMODE_EDITFORM                '        =        "Edit"
			//                'We must have a valid filename in par_s1
			//                if not goData:isFileValid(s1) then
			//                        goLog:SetError(10103, sProc, "", "par_s1", sProc, par_s1)
			//                        ' Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
			//                        '
			//                        'Please contact Selltis support.
			//                        result 0
			//                END
			//                'We must have a valid TID in par_s2 here
			//                if not goTr:IsTID(s2) then
			//                        goLog:SetError(10103, sProc, "", "par_s2", sProc, par_s2)
			//                        ' Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
			//                        '
			//                        'Please contact Selltis support.
			//                        result 0
			//                END

			//        case SELL_AMODE_CREATELINKED        '        =        "CreateLinkedByID"
			//                'We must have a valid filename in par_s1
			//                if not goData:isFileValid(s1) then
			//                        goLog:SetError(10103, sProc, "", "par_s1", sProc, par_s1)
			//                        ' Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
			//                        '
			//                        'Please contact Selltis support.
			//                        result 0
			//                END
			//                'We must have a valid TID in par_s2 here
			//                if not goTr:IsTID(s2) then
			//                        goLog:SetError(10103, sProc, "", "par_s2", sProc, par_s2)
			//                        ' Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
			//                        '
			//                        'Please contact Selltis support.
			//                        result 0
			//                END

			//        case SELL_AMODE_CREATEUNLINKED        '=        "CreateUnlinked"

			//        case SELL_AMODE_CREATEBLANK                '        =        "CreateBlank"

			//        other case
			//                goLog:SetError(10103, sProc, "", "par_sActionModeOrID", sProc, par_sActionModeOrID)
			//                ' Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
			//                '
			//                'Please contact Selltis support.
			//                result 0
			//        END
			//other case
			//        goLog:SetError(10103, sProc, "", "par_sActionName", sProc, par_sActionName)
			//        ' Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
			//        '
			//        'Please contact Selltis support.
			//        result 0
			//end

			//result goAction:AddAction(sActionName, s1, s2, s3, doObject, sActionModeOrID, doArray, s4, s5)
		}
		public bool RunUtility(string par_sDBName, string par_sUserLogon, string par_UserVBPassWord, int par_iUtility, string par_sComplement = "")
		{
			//DEPRECATED
			//--------------------
			//SETERROR
			//--------------------
			//CS PORTED as placeholder

			//PURPOSE:
			//		Grouper en un seul endroit (ici) le code pour lancer les utilitaires Sellsync et SellAuto
			//PARAMETERS:
			//		par_sDBName: DB name
			//		par_sUserLogon: User logon
			//		par_UserVBPassWord: User password
			//		par_iUtility: Constant that defines the program -> SELL_NI_SYNCENGINE, SELL_NI_AUTOMATOR
			//		par_sComplement: Action complement (optional)
			//RETURNS:
			//		True if succeeded, false otherwise.
			//HOW IT WORKS:
			//		1) Build the command line, in metadata format.
			//		2) Use DDEStart function.
			//EXAMPLE:
			//		goP:RunUtility(goData:GetDBName(), gop:GetUserLogon(), gop:GetUserVBPassWord(), SELL_NI_SYNCENGINE, sRestore)

			string sProc = "clProject::RunUtility";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_iUtility, SELL_LOGLEVEL_DEBUG)

			return true;
		}
		public void SellTrace(string par_sUserID, string par_sMessage, string par_sProc = "")
		{

			//PURPOSE:                                                                                  
			//		Display trace depending of Mode. (See SellTraceMode)
			//PARAMETERS:
			//		Par_sUsedID : ID of user (DD, FH, MI etc..)
			//		Par_sMessage : Message to display
			//		Par_sProc (optional) : Procedure calling SellTrace
			//RETURNS:
			//		Nothing
			//HOW IT WORKS:
			//EXAMPLE:
			//		gop:SellTrace("DD","My message","clView:Event_0001")

			string sUserID = par_sUserID;
			string sMessage = par_sMessage;
			string sProc = par_sProc;
			string sTraceLine = null;

			switch (lTraceMode)
			{
				case clC.SELLTRACE_USER_AND_EMPTY:
					//    trace("SELLTRACE_USER_AND_EMPTY", sproc)
					if (sUserID.ToUpper() == GetUserCode().ToUpper() || sUserID == "")
					{
						sTraceLine = sUserID.Trim(' ');
						if (sTraceLine != "")
						{
							sTraceLine += " - ";
						}
						sTraceLine += sMessage.Trim(' ');
						if (sProc != "")
						{
							sTraceLine += " (" + sProc + ")";
						}
						FileTrace(sTraceLine, par_sProc);
					}
					break;
				case clC.SELLTRACE_USER:
					//    trace("SELLTRACE_USER", sproc)
					if (sUserID.ToUpper() == GetUserCode().ToUpper()) //or sUserID="" then
					{
						sTraceLine = sMessage.Trim(' ');
						if (sProc != "")
						{
							sTraceLine += " (" + sProc + ")";
						}
						FileTrace(sTraceLine, par_sProc);
					}
					break;
				case clC.SELLTRACE_ALL:
					//    trace("SELLTRACE_ALL", sproc)
					sTraceLine = sUserID + " - " + sMessage;
					if (sProc != "")
					{
						sTraceLine += " (" + sProc + ")";
					}
					FileTrace(sTraceLine, par_sProc);
					break;
				default:
				break;
					//    trace("OTHER", sproc)
					//	trace(:lTraceMode, "OTHER")
			}
		}
		public void SellTraceMode(long Par_lMode = clC.SELLTRACE_USER)
		{

			//PURPOSE:
			//		Set the mode of trace.
			//PARAMETERS:
			//		Par_lMode : clc.SELLTRACE_USER (Default) ' Trace if user code is current user
			//					clc.SELLTRACE_ALL			 ' All Trace
			//					clc.SELLTRACE_OFF			 ' No trace
			//                   clC.SELLTRACE_USER_AND_EMPTY ' Trace if user code is current user or blank
			//RETURNS:                                                                                   
			//		Nothing
			//HOW IT WORKS:
			//EXAMPLE:
			//		goP.SellTraceMode(clc.SELLTRACE_OFF)

			string sProc = "clProject::SellTraceMode";

			switch (Par_lMode)
			{
				case clC.SELLTRACE_OFF:
				case clC.SELLTRACE_USER:
				case clC.SELLTRACE_ALL:
				case clC.SELLTRACE_USER_AND_EMPTY:
					lTraceMode = Par_lMode;
					break;
			}
		}
		public bool SetAlertPointer(string par_sFile, string par_sProcName, long par_lValue)
		{
			//CS Need to test (from w_proc)

			//PURPOSE:
			//		Set a pointer to the CLEUNIK of the most recent record processed
			//		by a particular alert-creating script. This is needed for timer-triggered
			//		alerts that look for new records, for example, for incoming correspondence
			//		or Messages. Pointers are written in OTH_ALERT_POINTERS metadata in the
			//		format of FILENAME_PROCEDURENAME=VALUE where PROCEDURENAME is the sProcName
			//		name of the script that uses the pointers.
			//		The corresponding procedure is GetAlertPointer().
			//PARAMETERS:
			//		par_sFile: System file name: ACTIVITY, CONTACT, MESSAGE, OPP. Validity of the
			//			file is not checked. Case insensitive.
			//		par_sProcName: Name of the procedure that uses the pointer. Case insensitive.
			//		par_lValue: CLEUNIK value of the most recent record in the file at the time
			//			Obtain this value with GetFirst in a rowset sorted by XXCLEUNIK
			//			in descending order.
			//RETURNS:
			//		True.
			//EXAMPLE:

			//sProc is string=FenEnExecution()+".SetAlertPointer"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//*** Skip the following line in SellSQL.
			//sMemo is string = goMeta:PageRead(goP:GetUserTID(),"OTH_ALERT_POINTERS", "", True)
			string sFile = par_sFile.ToUpper();
			string sProcName = par_sProcName.ToUpper();

			//*** Instead of the next two lines do:
			goMeta.LineWrite(goP.GetUserTID(), "OTH_ALERT_POINTERS", sFile + "|" + sProcName, par_lValue.ToString());
			//goTr:   StrWrite(sMemo, sFile + "|" + sProcName, NumToString(par_lValue))

			//goMeta:PageWrite(goP:GetUserTID(),"OTH_ALERT_POINTERS",sMemo)
// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}
		public void SetCurrentUAID(int par_iUAID)
		{
			//CS OK

			string sProc = "clProject::SetCurrentUAID";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			long lUAID; //Declared as long int to accept all values
			lUAID = par_iUAID;
			if (lUAID < 0 || lUAID > 65535)
			{
				uCurrentUAID = 0;
			}
			else
			{
				uCurrentUAID = lUAID;
			}
		}
		public void SetLanguage()
		{
			//CS In progress, WT finish
			//Need to replace IniRead, LoadError, GetProgramDir, MessTraduit

			//PORT. You should have a To Do about creating clProject::Nation(). [MI 6/21/13: now GetNation()]
			//Call this new method instead of WD's Nation().

			//CS: Commented here to end until have all functions.
			//Dim iResult As Integer

			//'============================ LANGUAGE =====================================
			//gsLang = IniRead("Common", "Language", "US English", gsIniFile)
			//Select Case gsLang
			//    Case "US English"
			//        Nation(3)           'US English
			//        'No loaderror necessary, as both french and english mesages are included in the original dll
			//    Case "French"
			//        Nation(5)           'French
			//        'No loaderror necessary, as both french and english mesages are included in the original dll
			//    Case "German"
			//        Nation(1)           'German
			//        iResult = LoadError(goTR.AddFinalSlash(GetProgramDir()) & "SELL_GR.WDM")
			//        If iResult <> 0 Then
			//            MsgBox(goTR.MessComplete(MessTraduit(5010), gsLang, iResult), 6, "Selltis")
			//            '----- Anglais/English (3) -----
			//            'Can't load error messages in [1] (error '[2]').
			//            '
			//            'Please contact Selltis support.
			//            '----- Francais/French (5) -----
			//            'Impossible de charger les messages en [1] (erreur '[2]').
			//            '
			//            'Veuillez contacter le support Selltis.
			//            '----- Allemand/German (1) -----
			//            'Can't load error messages in [1] (error '[2]').
			//            '
			//            'Please contact Selltis support.
			//            '----- Espagnol/Spanish (7) -----
			//            'Can't load error messages in [1] (error '[2]').
			//            '
			//            'Please contact Selltis support.		
			//        End If
			//    Case "Spanish"
			//        Nation(7)           'Spanish
			//        iResult = LoadError(goTR.AddFinalSlash(GetProgramDir()) & "SELL_SP.WDM")
			//        If iResult <> 0 Then
			//            MsgBox(goTR.MessComplete(MessTraduit(5010), gsLang, iResult), 6, "Selltis")
			//            '----- Anglais/English (3) -----
			//            'Can't load error messages in [1] (error '[2]').
			//            '
			//            'Please contact Selltis support.
			//            '----- Francais/French (5) -----
			//            'Impossible de charger les messages en [1] (erreur '[2]').
			//            '
			//            'Veuillez contacter le support Selltis.
			//            '----- Allemand/German (1) -----
			//            'Can't load error messages in [1] (error '[2]').
			//            '
			//            'Please contact Selltis support.
			//            '----- Espagnol/Spanish (7) -----
			//            'Can't load error messages in [1] (error '[2]').
			//            '
			//            'Please contact Selltis support.
			//        End If
			//    Case Else
			//        'Wrong parameter or can't read ini file
			//        Nation(3)           'US English - default
			//End Select
		}
		public void SetUserAdmin(bool par_bStatus)
		{
			//PURPOSE:
			//		Give user admin permissions
			//PARAMETERS:
			//		par_bStatus = status to set (True/False)
			//RETURNS:
			//		Nothing
			//HOW IT WORKS:                                                                                 
			//		Sets user status to admin.
			//		Admin permissions determine whether the user is allowed to
			//		create, modify, and delete data in all files without restriction.
			//EXAMPLE:
			//		goP:SetUserAdmin(True)
			string sProc = "clProject::SetUserAdmin";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)


			bUserIsAdmin = par_bStatus;
		}

		public void SetUserAuthor(int par_iStatus)
		{
			//PURPOSE:
			//		Give user author permissions
			//PARAMETERS:                                                                                  
			//		par_bStatus = status to set
			//			Supported: 0=not author; 1=limited author; 2=full author
			//RETURNS:
			//		Nothing
			//HOW IT WORKS:
			//		Sets user status to author.
			//		Author permissions determine whether the user is allowed to
			//		create, modify, and delete shared metadata objects such as desktops, views,
			//		import templates, etc.
			//		- Limited and full authors can created new shared objects.
			//		- Limited author can edit and delete objects (s)he created.
			//		- Full author can edit and delete all shared objects.
			//EXAMPLE:
			//		goP:SetUserAuthor(1)	'give User limited authors permissions
			//		goP:SetUserAuthor(2)	'give User full author privileges
			string sProc = "clProject::SetUserAuthor";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)


			if (par_iStatus > 2 || par_iStatus < 0)
			{
				iUserAuthorState = 0;
			}
			else
			{
				iUserAuthorState = par_iStatus;
			}
		}
		public void SetUserCode(string par_sCode)
		{
			//MI 5/15/06
			//CS OK

			//Set the current user's 4-characters code (ex: 'MAR')
			//Should be called only by the logon management
			string sProc = "clProject::SetUserCode";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			gsUserID = par_sCode;
		}

		public void SetUserLocation(string par_sLocation)
		{
			string sProc = "clProject::SetUserLocation";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)
			sUserLocation = par_sLocation;
		}
		public void SetUserLogon(string par_sLogon)
		{
			string sProc = "clProject::SetUserLogon";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)
			sUserLogon = par_sLogon;
		}
		public void SetUserLogonID(string par_sLogonID)
		{
			string sProc = "clProject::SetUserLogonID";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)
			sUserLogonID = par_sLogonID;
		}
		public void SetUserName(string par_sName)
		{
			string sProc = "clProject::SetUserName";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)
			sUserName = par_sName;
		}
		public void SetUserPass(string par_sPass)
		{
			string sProc = "clProject::SetUserPass";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)
			sUserPass = par_sPass;
		}
		public void SetUserPermissions(string par_sString)
		{
			//MI 2/7/07 Created.
			string sproc = "clproject::SetUserPermissions";
			sUserPermissions = par_sString;
		}

		public void SetUserTID(string par_sID)
		{
			//Set the current user Text ID
			//Should be called only by the logon management
			string sProc = "clProject::SetUserTID";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			sUserTID = par_sID;
		}
		public void SetUserVBPassword(string par_sPass)
		{
			string sProc = "clProject::SetUserVBPassword";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)
			sUserVBPassword = par_sPass;
		}

		public void ShowMap(string par_sAddress, string par_sCity, string par_sState, string par_sZip, string par_sCountry = "")
		{
			//CS PORTED as placeholder (from w_proc)

			//PURPOSE:
			//		Displays the map of the address in the default web browser.
			//		The MapQuest service supports addresses in the United States. Other countries may not be
			//		supported.
			//		See also: GetMapURL().
			//PARAMETERS:
			//		par_sAddress: Street address. This parameter should not contain hard returns and must be
			//			resolvable by MapQuest. 
			//		par_sCity: City
			//		par_sState: 2-character state abbreviation
			//		par_sZip: zip code (with or without extension).
			//		par_sCountry: name of the country (optional)
			//RETURNS:
			//		Nothing.
			//SEE ALSO:
			//		GetMapURL()

			//sProc is string=FenEnExecution()+".ShowMap"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//autSellMisc is automation "SellUtil.Misc"

			//sURL is string
			//sCountry is string = Upper(par_sCountry)
			//sCountryCode is string

			//'==> Finish
			//'Switch sCountry
			//'	CASE "Afghanistan"
			//'		sCountryCode = "AF"
			//'	CASE ""
			//'		AL	Albania	
			//'	CASE ""
			//'		DZ	Algeria
			//'	CASE ""
			//'		AS	American Samoa
			//'	CASE ""
			//'		AD	Andorra
			//'	CASE ""
			//'		AO	Angola
			//'	CASE ""
			//'		AI	Anguilla
			//'	CASE ""
			//'		AG	Antigua and Barbuda
			//'	CASE ""
			//'		AR	Argentina
			//'	CASE ""
			//'		AM	Armenia
			//'	CASE ""
			//'		AW	Aruba
			//'	CASE ""
			//'		AU	Australia
			//'	CASE ""
			//'		AT	Austria
			//'	CASE ""
			//'		AZ	Azerbaijan
			//'	CASE ""
			//'		BS	Bahamas
			//'	CASE ""
			//'		BH	Bahrain
			//'	CASE ""
			//'		BD	Bangladesh
			//'	CASE ""
			//'		BB	Barbados
			//'	CASE ""
			//'		BY	Belarus
			//'	CASE ""
			//'		BE	Belgium
			//'	CASE ""
			//'		BZ	Belize
			//'	CASE ""
			//'		BJ	Benin
			//'	CASE ""
			//'		BM	Bermuda
			//'	CASE ""
			//'		BT	Bhutan
			//'	CASE ""
			//'		BO	Bolivia
			//'	CASE ""
			//'		BA	Bosnia and Herzegovina
			//'	CASE ""
			//'		BW	Botswana
			//'	CASE ""
			//'		BV	Bouvet Island
			//'	CASE ""
			//'		BR	Brazil
			//'	CASE ""
			//'		IO	British Indian Ocean Territory
			//'	CASE ""
			//'		VG	British Virgin Islands
			//'	CASE ""
			//'		BN	Brunei
			//'	CASE ""
			//'		BG	Bulgaria
			//'	CASE ""
			//'		BF	Burkina Faso
			//'	CASE ""
			//'		BI	Burundi
			//'	CASE ""
			//'		KH	Cambodia
			//'	CASE ""
			//'		CM	Cameroon
			//'	CASE ""
			//'		CA	Canada
			//'	CASE ""
			//'		CV	Cape Verde
			//'	CASE ""
			//'		KY	Cayman Islands
			//'	CASE ""
			//'		CF	Central African Republic
			//'	CASE ""
			//'		TD	Chad
			//'	CASE ""
			//'		CL	Chile
			//'	CASE ""
			//'		CN	China
			//'	CASE ""
			//'		CX	Christmas Island
			//'	CASE ""
			//'		CC	Cocos (Keeling) Islands
			//'	CASE ""
			//'		CO	Colombia
			//'	CASE ""
			//'		KM	Comoros
			//'	CASE ""
			//'		CG	Congo
			//'	CASE ""
			//'		CD	Congo - Democratic Republic of
			//'	CASE ""
			//'		CK	Cook Islands
			//'	CASE ""
			//'		CR	Costa Rica
			//'	CASE ""
			//'		CI	Cote d'Ivoire
			//'	CASE ""
			//'		HR	Croatia
			//'	CASE ""
			//'		CU	Cuba
			//'	CASE ""
			//'		CY	Cyprus
			//'	CASE ""
			//'		CZ	Czech Republic
			//'	CASE ""
			//'		DK	Denmark
			//'	CASE ""
			//'		DJ	Djibouti
			//'	CASE ""
			//'		DM	Dominica
			//'	CASE ""
			//'		DO	Dominican Republic
			//'	CASE ""
			//'		TP	East Timor
			//'	CASE ""
			//'		EC	Ecuador
			//'	CASE ""
			//'		EG	Egypt
			//'	CASE ""
			//'		SV	El Salvador
			//'	CASE ""
			//'		GQ	Equitorial Guinea
			//'	CASE ""
			//'		ER	Eritrea
			//'	CASE ""
			//'		EE	Estonia
			//'	CASE ""
			//'		ET	Ethiopia
			//'	CASE ""
			//'		FK	Falkland Islands (Islas Malvinas)
			//'	CASE ""
			//'		FO	Faroe Islands
			//'	CASE ""
			//'		FJ	Fiji
			//'	CASE ""
			//'		FI	Finland
			//'	CASE ""
			//'		FR	France
			//'	CASE ""
			//'		GF	French Guyana
			//'	CASE ""
			//'		PF	French Polynesia
			//'	CASE ""
			//'		TF	French Southern and Antarctic Lands
			//'	CASE ""
			//'		GA	Gabon
			//'	CASE ""
			//'		GM	Gambia
			//'	CASE ""
			//'		GZ	Gaza Strip
			//'	CASE ""
			//'		GE	Georgia
			//'	CASE ""
			//'		DE	Germany
			//'	CASE ""
			//'		GH	Ghana
			//'	CASE ""
			//'		GI	Gibraltar
			//'	CASE ""
			//'		GR	Greece
			//'	CASE ""
			//'		GL	Greenland
			//'	CASE ""
			//'		GD	Grenada
			//'	CASE ""
			//'		GP	Guadeloupe
			//'	CASE ""
			//'		GU	Guam
			//'	CASE ""
			//'		GT	Guatemala
			//'	CASE ""
			//'		GN	Guinea
			//'	CASE ""
			//'		GW	Guinea-Bissau
			//'	CASE ""
			//'		GY	Guyana
			//'	CASE ""
			//'		HT	Haiti
			//'	CASE ""
			//'		HM	Heard Island and McDonald Islands
			//'	CASE ""
			//'		VA	Holy See (Vatican City)
			//'	CASE ""
			//'		HN	Honduras
			//'	CASE ""
			//'		HK	Hong Kong
			//'	CASE ""
			//'		HU	Hungary
			//'	CASE ""
			//'		IS	Iceland
			//'	CASE ""
			//'		IN	India
			//'	CASE ""
			//'		ID	Indonesia
			//'	CASE ""
			//'		IR	Iran
			//'	CASE ""
			//'		IQ	Iraq
			//'	CASE ""
			//'		IE	Ireland
			//'	CASE ""
			//'		IL	Israel
			//'	CASE ""
			//'		IT	Italy
			//'	CASE ""
			//'		JM	Jamaica
			//'	CASE ""
			//'		JP	Japan
			//'	CASE ""
			//'		JO	Jordan
			//'	CASE ""
			//'		KZ	Kazakhstan
			//'	CASE ""
			//'		KE	Kenya
			//'	CASE ""
			//'		KI	Kiribati
			//'	CASE ""
			//'		KW	Kuwait
			//'	CASE ""
			//'		KG	Kyrgyzstan
			//'	CASE ""
			//'		LA	Laos
			//'	CASE ""
			//'		LV	Latvia
			//'	CASE ""
			//'		LB	Lebanon
			//'	CASE ""
			//'		LS	Lesotho
			//'	CASE ""
			//'		LR	Liberia
			//'	CASE ""
			//'		LY	Libya
			//'	CASE ""
			//'		LI	Liechtenstein
			//'	CASE ""
			//'		LT	Lithuania
			//'	CASE ""
			//'		LU	Luxembourg
			//'	CASE ""
			//'		MO	Macau
			//'	CASE ""
			//'		MK	Macedonia - The Former Yugoslav Republic of
			//'	CASE ""
			//'		MG	Madagascar
			//'	CASE ""
			//'		MW	Malawi
			//'	CASE ""
			//'		MY	Malaysia
			//'	CASE ""
			//'		MV	Maldives
			//'	CASE ""
			//'		ML	Mali
			//'	CASE ""
			//'		MT	Malta
			//'	CASE ""
			//'		MH	Marshall Islands
			//'	CASE ""
			//'		MQ	Martinique
			//'	CASE ""
			//'		MR	Mauritania
			//'	CASE ""
			//'		MU	Mauritius
			//'	CASE ""
			//'		YT	Mayotte
			//'	CASE ""
			//'		MX	Mexico
			//'	CASE ""
			//'		FM	Micronesia - Federated States of
			//'	CASE ""
			//'		MD	Moldova
			//'	CASE ""
			//'		MC	Monaco
			//'	CASE ""
			//'		MN	Mongolia
			//'	CASE ""
			//'		MS	Montserrat
			//'	CASE ""
			//'		MA	Morocco
			//'	CASE ""
			//'		MZ	Mozambique
			//'	CASE ""
			//'		MM	Myanmar
			//'	CASE ""
			//'		NA	Namibia
			//'	CASE ""
			//'		NR	Naura
			//'	CASE ""
			//'		NP	Nepal
			//'	CASE ""
			//'		NL	Netherlands
			//'	CASE ""
			//'		AN	Netherlands Antilles
			//'	CASE ""
			//'		NC	New Caledonia
			//'	CASE ""
			//'		NZ	New Zealand
			//'	CASE ""
			//'		NI	Nicaragua
			//'	CASE ""
			//'		NE	Niger
			//'	CASE ""
			//'		NG	Nigeria
			//'	CASE ""
			//'		NU	Niue
			//'	CASE ""
			//'		NF	Norfolk Island
			//'	CASE ""
			//'		KP	North Korea
			//'	CASE ""
			//'		MP	Northern Mariana Islands
			//'	CASE ""
			//'		NO	Norway
			//'	CASE ""
			//'		OM	Oman
			//'	CASE ""
			//'		PK	Pakistan
			//'	CASE ""
			//'		PW	Palau
			//'	CASE ""
			//'		PA	Panama
			//'	CASE ""
			//'		PG	Papua New Guinea
			//'	CASE ""
			//'		PY	Paraguay
			//'	CASE ""
			//'		PE	Peru
			//'	CASE ""
			//'		PH	Philippines
			//'	CASE ""
			//'		PN	Pitcairn Islands
			//'	CASE ""
			//'		PL	Poland
			//'	CASE ""
			//'		PT	Portugal
			//'	CASE ""
			//'		PR	Puerto Rico
			//'	CASE ""
			//'		QA	Qatar
			//'	CASE ""
			//'		RE	Reunion
			//'	CASE ""
			//'		RO	Romania
			//'	CASE ""
			//'		RU	Russia
			//'	CASE ""
			//'		RW	Rwanda
			//'	CASE ""
			//'		KN	Saint Kitts and Nevis
			//'	CASE ""
			//'		LC	Saint Lucia
			//'	CASE ""
			//'		VC	Saint Vincent and the Grenadines
			//'	CASE ""
			//'		WS	Samoa
			//'	CASE ""
			//'		SM	San Marino
			//'	CASE ""
			//'		ST	Sao Tome and Principe
			//'	CASE ""
			//'		SA	Saudi Arabia
			//'	CASE ""
			//'		SN	Senegal
			//'	CASE ""
			//'		CS	Serbia and Montenegro
			//'	CASE ""
			//'		SC	Seychelles
			//'	CASE ""
			//'		SL	Sierra Leone
			//'	CASE ""
			//'		SG	Singapore
			//'	CASE ""
			//'		SK	Slovakia
			//'	CASE ""
			//'		SI	Slovenia
			//'	CASE ""
			//'		SB	Solomon Islands
			//'	CASE ""
			//'		SO	Somalia
			//'	CASE ""
			//'		ZA	South Africa
			//'	CASE ""
			//'		GS	South Georgia and the South Sandwich Islands
			//'	CASE ""
			//'		KR	South Korea
			//'	CASE ""
			//'		ES	Spain
			//'	CASE ""
			//'		LK	Sri Lanka
			//'	CASE ""
			//'		SH	St. Helena
			//'	CASE ""
			//'		PM	St. Pierre and Miquelon
			//'	CASE ""
			//'		SD	Sudan
			//'	CASE ""
			//'		SR	Suriname
			//'	CASE ""
			//'		SJ	Svalbard
			//'	CASE ""
			//'		SZ	Swaziland
			//'	CASE ""
			//'		SE	Sweden
			//'	CASE ""
			//'		CH	Switzerland
			//'	CASE ""
			//'		SY	Syria
			//'	CASE ""
			//'		TW	Taiwan
			//'	CASE ""
			//'		TJ	Tajikistan
			//'	CASE ""
			//'		TZ	Tanzania
			//'	CASE ""
			//'		TH	Thailand
			//'	CASE ""
			//'		TG	Togo
			//'	CASE ""
			//'		TK	Tokelau
			//'	CASE ""
			//'		TO	Tonga
			//'	CASE ""
			//'		TT	Trinidad and Tobago
			//'	CASE ""
			//'		TN	Tunisia
			//'	CASE ""
			//'		TR	Turkey
			//'	CASE ""
			//'		TM	Turkmenistan
			//'	CASE ""
			//'		TC	Turks and Caicos Islands
			//'	CASE ""
			//'		TV	Tuvalu
			//'	CASE ""
			//'		UG	Uganda
			//'	CASE ""
			//'		UA	Ukraine
			//'	CASE ""
			//'		AE	United Arab Emirates
			//'	CASE ""
			//'		GB	United Kingdom
			//'	CASE ""
			//'		US	United States
			//'	CASE ""
			//'		VI	United States Virgin Islands
			//'	CASE ""
			//'		UY	Uruguay
			//'	CASE "Uzbekistan"
			//'		UZ	
			//'	CASE "Vanuatu"
			//'		VU	
			//'	CASE "Venezuela"
			//'		VE	
			//'	CASE "Vietnam"
			//'		VN	
			//'	CASE "Wallis and Futuna"
			//'		WF	
			//'	CASE "West Bank"
			//'		PS	
			//'	CASE "Western Sahara"
			//'		EH	
			//'	CASE "Yemen"
			//'		YE	
			//'	CASE "Zambia"
			//'		ZM	
			//'	CASE "Zimbabwe"
			//'		ZW	
			//'END

			//If sCountryCode = "" Then
			//	sURL = autSellMisc>>GetMapURL(par_sAddress,par_sCity,par_sState,par_sZip)
			//ELSE
			//	sURL = autSellMisc>>GetMapURL(par_sAddress,par_sCity,par_sState,par_sZip,sCountryCode)
			//END


			//If not goP:ShellExecute(sURL,"htm") Then
			//	goLog:SetError(30027, sProc, "", sURL)
			//	' An error occurred opening web page '[1]'.
			//	goLog:DisplayLastErrorFriendly()
			//	goLog:SetError()
			//END	
		}

		public void TraceLine(string par_sMessage, string par_sUserCode = "", string par_sProc = "")
		{
			//DEPRECATED
			//PURPOSE:
			//		Display a trace line using SellTrac.exe. Use this for debugging scripts
			//		when calls to MsgBox() are too disruptive or when they can't be used
			//		due to a conflict with various program events. To see the line displayed,
			//		run SellTrac.exe first and check the Trace checkbox. If you don't
			//		use the par_sUserCode parameter, you must modify the settings in 
			//		SellTrace: select File>Settings and select the Trace All Users
			//		radio button. Click Start to start the Trace.
			//PARAMETERS:
			//		par_sMessage: Text to display
			//		par_sUserCode: User code. If SellTrace is set to display messages
			//			only for the logged on user, this code must correspond to the
			//			User Code of the logged on user. For example, if you are logging
			//			on as JOHNDOE and your User Code is JHD, this parameter should be
			//			JHD.
			//		par_sProc: Name of the procedure in which this function is called.
			//			In scripts, this name is in the variable sProc. 
			//EXAMPLE:
			//		TraceLine("My diagnostic message","JHD",sProc)

			//sProc is string = FenEnExecution()+".TraceLine"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			goP.SellTrace(par_sUserCode, par_sMessage, par_sProc);
		}
		public string ViewTypeToText(string par_sViewType)
		{
			//OWNER: MI		8/28/2002
			//Convert view type constant to friendly text for display to user
			//PARAMETERS:
			//		par_sViewType: constant for type of the view (string).
			//			Constants used to be integers such as 10=LIST, 21=CALDAY, etc.
			//			Constants are written in the file Selltis.wl. If you make changes
			//			there, recompile the project.
			//
			//RETURNS:
			//		String, friendly name of the view type

			//---------------- HISTORY ---------------------
			//8/28/2002	MI	clProject::ViewTypeToText()		Edited to support string constants
			//3/22/2002	MI	clProject::ViewTypeToText()		Created method for converting type code to "friendly" label.

			string sProc = "clProject::ViewTypeToText";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			switch (par_sViewType)
			{
				case clC.SELL_FORM:
				
					return "Form"; //MessTranslate(5001)) '5001:Form

				case clC.SELL_VIEW_LIST:
				
					return "List"; //MessTranslate(5002)) '5002:List

				case clC.SELL_VIEW_DAYCAL:
				
					return "Day Calendar"; //MessTranslate(5003)) '5003:Day Calendar

				case clC.SELL_VIEW_WEEKCAL:
					return "Week Calendar"; //MessTranslate(5005)) '5005:Week calendar

				case clC.SELL_VIEW_MONTHCAL:
					return "Month Calendar"; //MessTranslate(5006)) '5006:Month Calendar

				case clC.SELL_VIEW_YEARCAL:
					return "Year Calendar"; //MessTranslate(5007)) '5007:Year Calendar

				case clC.SELL_VIEW_MFIELD:
					return "Multi-field"; //MessTranslate(5011)) '5011:Multi-field

				case clC.SELL_VIEW_SFIELD:
					return "Single-field"; //MessTranslate(5012)) '5012:Single-field

				default:
					return "Unsupported view type passed to clProject::ViewTypeToText(): '" + par_sViewType + " '.";
					//MessTranslate(5017) + par_sViewType + " '.")   5017:Unsupported view type passed to clProject::ViewTypeToText(): '

			}
		}

		public clProject()
		{

		}

		~clProject()
		{
// INSTANT C# NOTE: The base class Finalize method is automatically called from the destructor:
			//base.Finalize();
		}
	}

}
