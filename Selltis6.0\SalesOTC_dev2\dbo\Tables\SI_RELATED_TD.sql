﻿CREATE TABLE [dbo].[SI_RELATED_TD] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_SI_RELATED_TD_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_SI] UNIQUEIDENTIFIER NOT NULL,
    [G<PERSON>_TD] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_SI_RELATED_TD] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_SI_RELATED_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_LINKED_SI] FOREIGN KEY ([GID_SI]) REFERENCES [dbo].[SI] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[SI_RELATED_TD] NOCHECK CONSTRAINT [LNK_SI_RELATED_TD];


GO
ALTER TABLE [dbo].[SI_RELATED_TD] NOCHECK CONSTRAINT [LNK_TD_LINKED_SI];


GO
CREATE NONCLUSTERED INDEX [IX_SI_RELATED_TD]
    ON [dbo].[SI_RELATED_TD]([GID_SI] ASC, [GID_TD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_LINKED_SI]
    ON [dbo].[SI_RELATED_TD]([GID_TD] ASC, [GID_SI] ASC);

