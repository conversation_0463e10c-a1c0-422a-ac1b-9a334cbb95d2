﻿CREATE TABLE [dbo].[QO_RELATED_QO] (
    [GID_ID]  UNIQUEIDENTIFIER CONSTRAINT [DF_QO_RELATED_QO_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_QO]  UNIQUEIDENTIFIER NOT NULL,
    [GID_QO2] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_QO_RELATED_QO] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_QO_CONNECTED_QO] FOREIGN KEY ([GID_QO]) REFERENCES [dbo].[QO] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QO_RELATED_QO] FOREIGN KEY ([GID_QO2]) REFERENCES [dbo].[QO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[QO_RELATED_QO] NOCHECK CONSTRAINT [LNK_QO_CONNECTED_QO];


GO
ALTER TABLE [dbo].[QO_RELATED_QO] NOCHECK CONSTRAINT [LNK_QO_RELATED_QO];


GO
CREATE NONCLUSTERED INDEX [IX_QO_RELATED_QO]
    ON [dbo].[QO_RELATED_QO]([GID_QO] ASC, [GID_QO2] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_QO_CONNECTED_QO]
    ON [dbo].[QO_RELATED_QO]([GID_QO2] ASC, [GID_QO] ASC);

