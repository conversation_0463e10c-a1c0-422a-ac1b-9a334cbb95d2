﻿CREATE TABLE [dbo].[SI_CONNECTED_BU] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_SI_CONNECTED_BU_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_SI] UNIQUEIDENTIFIER NOT NULL,
    [GID_BU] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_SI_CONNECTED_BU] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_BU_LINKED_SI] FOREIGN KEY ([GID_SI]) REFERENCES [dbo].[SI] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_SI_CONNECTED_BU] FOREIGN KEY ([GID_BU]) REFERENCES [dbo].[BU] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[SI_CONNECTED_BU] NOCHECK CONSTRAINT [LNK_BU_LINKED_SI];


GO
ALTER TABLE [dbo].[SI_CONNECTED_BU] NOCHECK CONSTRAINT [LNK_SI_CONNECTED_BU];


GO
CREATE NONCLUSTERED INDEX [IX_SI_CONNECTED_BU]
    ON [dbo].[SI_CONNECTED_BU]([GID_SI] ASC, [GID_BU] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_BU_LINKED_SI]
    ON [dbo].[SI_CONNECTED_BU]([GID_BU] ASC, [GID_SI] ASC);

