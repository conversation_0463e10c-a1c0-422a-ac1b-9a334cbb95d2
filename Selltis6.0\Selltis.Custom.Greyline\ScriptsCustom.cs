﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Text;
using System.Drawing;
using System.Data;
using Microsoft.VisualBasic;
using System.Collections;
using System.Text.RegularExpressions;
using System.IO;
using System.Xml;
using System.Diagnostics;
using System.Net;


namespace Selltis.Custom
{
    public class ScriptsCustom
    {

        private clProject goP;
        private clMetaData goMeta;
        private clTransform goTR;
        private clData goData;
        private clError goErr;
        private clLog goLog;
        private clDefaults goDef;
        //private clScrMng goScr;
        ScriptManager scriptManager = new ScriptManager();
        private ClUI goUI;
        private clPerm goPerm;
        private clHistory goHist;
        public string sError;

        int par_iValid = 4;
        DataTable oTable = new DataTable();
        string sDelim = "";

        string par_oReturn = null;
        bool par_bRunNext = false;
        string par_sSections = "";

        System.Data.SqlClient.SqlConnection par_oConnection = null;

        public void Initialize()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goData = (clData)Util.GetInstance("data");
            goP = (clProject)Util.GetInstance("p");
            goErr = (clError)Util.GetInstance("err");
            goLog = (clLog)Util.GetInstance("log");
            goUI = new ClUI();

        }
        public ScriptsCustom()
        {
            Initialize();
        }


        public bool _TemplateScript(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // *** For notes on how to create a custom script, see clScripts.vb ***

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            //try
            //{
            //}


            //catch (Exception ex)
            //{
            //    if (!ex.Message == clC.EX_THREAD_ABORT_MESSAGE)
            //        goErr.SetError(ex, 45105, sProc);
            //}

            return true;
        }

        public bool AC_FormControlOnChange_BTN_CORRLEAVEOPENLETTER_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // Dim doNewForm As Object

            // TLD 2/27/09 Added button to send letter, but leave form open
            // This stuff makes that work correctly with other
            // save and save leave open buttons
            goP.SetVar("OpenSendDialog", "1");

            // CS 6/28/07:Changing to doForm.save(5) so that the _post script will be called if there is any. When
            // set to doform.save(1) the code never returned here after save so the function never
            // returned true and the post script would never be called. So now the post script can be called and I
            // just set a boolean that tells the form to close so that the Send dialog displays.
            if (doForm.Save(5, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
            {
                return false;
            }

            return true;
        }

        public bool AC_FormControlOnChange_MLS_TYPE_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 2/27/2009 Hides/Shows BTN_CORRLEAVEOPENLETTER based on type
            // Letter Sent
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_TYPE", 2)) == 4)
            {
                doForm.SetControlState("BTN_CORRLEAVEOPENLETTER", 0); // Enabled
            }
            else
            {
                doForm.SetControlState("BTN_CORRLEAVEOPENLETTER", 2);// Hidden
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool AC_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sURL = "";
            string sName = "";
            string sID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

            // TLD 2/27/2009 Hides/Shows BTN_CORRLEAVEOPEN based on type
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_TYPE", 2)) == 4)
            {
                doForm.SetControlState("BTN_CORRLEAVEOPENLETTER", 0); // Enabled
            }
            else
            {
                doForm.SetControlState("BTN_CORRLEAVEOPENLETTER", 2);// Hidden
            }

            // TLD 2/27/2009 Added tooltip for BTN_CORRLEAVEOPEN
            doForm.SetFieldProperties("BTN_CORRLEAVEOPENLETTER", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Send and Leave Open");

            // TLD 2/27/2009 For processing Send and Leave Open
            // when type = "Letter Sent" -- Var set when
            // user clicks button BTN_CORRLEAVEOPENLETTER
            // --------------------BEGIN PROCESS SEND AND LEAVE OPEN
            if (goP.GetVar("OpenSendDialog") == "1")
            {
                sName = Convert.ToString(doForm.doRS.GetFieldVal("SYS_Name"));
                // CS: Always update sys name in case one of the affected fields changed.
                // If sName = "" Then
                scriptManager.RunScript("GenerateSysName", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, par_doArray, "", "", "", "", "");
                sURL = "";
                sURL = "../Pages/diaSend.aspx";
                // objectotsend: VIEW, RECORD, CORR
                goTR.URLWrite(ref sURL, "objecttosend", "CORR");

                // Send type: LETTER
                goTR.URLWrite(ref sURL, "sendtype", "LETTER");

                goTR.URLWrite(ref sURL, "objectid", sID);

                goTR.URLWrite(ref sURL, "objectname", sName);
                // goTR.URLWrite(sURL, "returnto", "MI_Test.aspx")

                // 'CS 6/28/07:Changing to doForm.save(5) so that the _post script will be called if there is any. When
                // 'set to doform.save(1) the code never returned here after save so the function never
                // 'returned true and the post script would never be called. So now the post script can be called and I
                // 'just set a boolean that tells the form to close so that the Send dialog displays.
                // If doForm.Save(5, , System.Reflection.MethodInfo.GetCurrentMethod().Name) = 0 Then
                // Return False
                // End If

                // TLD 9/18/2008 Commented following 2 lines per CS
                // Update fixed some stuff, broke this
                // Added third line below as a fix
                // goUI.SetNext("DIALOG", sURL) 'CS: Moved to after save call. Was before
                // doForm.CloseOnReturn = True 'flag 

                goP.DeleteVar("OpenSendDialog");
                //HttpContext.Current.Response.Redirect(goUI.Navigate("DIALOG",sURL));
                goUI.Queue("DIALOG", sURL);
            }
            // --------------- End processing Send and Leave Open
            par_doCallingObject = doForm;
            return true;
        }

        public bool AC_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 10/14/2009 Prevents main from running
            // They don't want Next Action Date enforce for leads
            goTR.StrWrite(ref par_sSections, "EnforceNextActionDateIfOpenLead", "0");

            // TLD 10/14/2009 Prevents main from running
            // Customize here -- their Source field is on different tab
            // And their purposes don't match
            goTR.StrWrite(ref par_sSections, "EnforceSourceIfPurposeIsLead", "0");
            // ----- Enforce Source if Purpose = "Lead" OR Purpose contains "Request" or "Submission"
            // If goScr.IsSectionEnabled(sProc, par_sSections, "EnforceSourceIfPurposeIsLead") Then
            switch (doForm.doRS.GetFieldVal("MLS_PURPOSE", 2))
            {
                case 8:
                case 21:
                case 22:
                case 23:
                case 24   // 8=Lead, 21-24=Request
               :
                    {
                        if (doForm.doRS.GetLinkCount("LNK_FROM_SO") < 1)
                        {
                            // doForm.MoveToTab(4)     'Details
                            doForm.MoveToTab(2);     // Notes
                                                     // goScr.RunScript("Activity_ManageExtraFields", doForm)
                            doForm.MoveToField("LNK_FROM_SO");
                            // goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("AC", "LNK_from_so"), "", "", "", "", "", "", "", "", "LNK_from_so")
                            goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("LNK_from_so"), "", "", "", "", "", "", "", "", "LNK_from_so");
                            return false;
                        }

                        break;
                    }
            }
            // End If
            par_doCallingObject = doForm;
            return true;
        }

        public bool Activity_FillAddress_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            par_bRunNext = false;

            // TLD 8/29/2008 Prevented Main from running, modified here

            // MI 1/28/07 Removed SetControlState calls. They are in Activity_ManageControlState.
            // PURPOSE:
            // Fill TXT_Address field with values from the Related Contact
            // RETURNS:
            // True if the user doesn't need to check the address (the address wasn't filled)
            // or False if the address was filled and the user needs to check it.		

            // goP.TraceLine("In " & sProc, "", sProc)
            try
            {
                Form doForm = (Form)par_doCallingObject;

                string sWork;
                string sAddress;
                int lType = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_Type", 2));

                // Fill Address field it Type = Letter Sent (4) and there is only 1 recipient
                // goP.TraceLine("Type is (should be '4' - Letter Sent): '" & Convert.ToString(lType) & "'", "", sProc)

                // CS 6/13/07: Only fill address if the address is blank. Before this change if there was already an address
                // it was being overwritten in formonload of AC.
                if (doForm.doRS.GetFieldVal("TXT_Address") != "")
                {
                    return true;
                }
                switch (lType)
                {
                    case 4:
                    case 8       // Letter Sent, Fax Sent
                   :
                        {
                            // goP.TraceLine("TXT_Address is (should be blank): '" & Trim(doForm.doRS.doGetFieldVal("TXT_ADDRESS") & "'"))
                            // If Trim(doForm.doRS.GetFieldVal("TXT_ADDRESS")) = "" Then
                            // goP.TraceLine("GetLinkCountEx of Related Contact is (should be < 2): '" & doForm.GetLinkCountEx("LNK_Related_CN") & "'")
                            // If doForm.doRS.GetLinkCount("LNK_Related_CN") = 1 Then        'originally was '< 2'
                            // goP.TraceLine("Concatenating the address", "", sProc)
                            // doForm.MoveToField("TXT_Address")
                            // doForm.setcontrolstate("TXT_Address", 0)
                            // doForm.setcontrolstate("BTN_FillFromCN", 0)
                            sWork = "";
                            // TLD 10/6/2010: Get CN info in 1 rowset instead of multiple double hops
                            clRowSet doRSCN = new clRowSet("CN", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_RELATED_CN") + "'", null, "TXT_NAMELAST,TXT_NAMEFIRST,TXT_ADDRBUSINESS,TXT_CITYBUSINESS,TXT_STATEBUSINESS,TXT_ZIPBUSINESS,TXT_COUNTRYBUSINESS");
                            // TLD 8/29/2008 Set sAddress to "" -- customer does NOT want ATTN: in Address field
                            // sAddress = "Attn:"
                            if (doRSCN.GetFirst() == 1)
                            {
                                sAddress = "";
                                // TLD 10/6/2010 Mod to use RS instead of double hops
                                // sWork = doForm.doRS.GetFieldVal("LNK_Related_CN%%TXT_NAMEFIRST")
                                sWork = Convert.ToString(doRSCN.GetFieldVal("TXT_NAMEFIRST"));
                                if (sWork != "")
                                {
                                    sAddress = sAddress + sWork;
                                }
                                // sWork = doForm.doRS.GetFieldVal("LNK_Related_CN%%TXT_NAMELAST")
                                sWork = Convert.ToString(doRSCN.GetFieldVal("TXT_NAMELAST"));
                                if (sWork != "")
                                {
                                    sAddress = sAddress + " " + sWork;
                                }
                                // sWork = doForm.doRS.GetFieldVal("LNK_Related_CN%%TXT_ADDRBUSINESS")
                                sWork = Convert.ToString(doRSCN.GetFieldVal("TXT_ADDRBUSINESS"));
                                if (sWork != "")
                                {
                                    sAddress = sAddress + Constants.vbCrLf + sWork;
                                }
                                // sWork = doForm.doRS.GetFieldVal("LNK_Related_CN%%TXT_CITYBUSINESS")
                                sWork = Convert.ToString(doRSCN.GetFieldVal("TXT_CITYBUSINESS"));
                                if (sWork != "")
                                {
                                    sAddress = sAddress + Constants.vbCrLf + sWork;
                                }
                                // sWork = doForm.doRS.GetFieldVal("LNK_Related_CN%%TXT_STATEBUSINESS")
                                sWork = Convert.ToString(doRSCN.GetFieldVal("TXT_STATEBUSINESS"));
                                if (sWork != "")
                                {
                                    sAddress = sAddress + ", " + sWork;
                                }
                                // sWork = doForm.doRS.GetFieldVal("LNK_Related_CN%%TXT_ZIPBUSINESS")
                                sWork = Convert.ToString(doRSCN.GetFieldVal("TXT_ZIPBUSINESS"));
                                if (sWork != "")
                                {
                                    sAddress = sAddress + " " + sWork;
                                }
                                // sWork = doForm.doRS.GetFieldVal("LNK_Related_CN%%TXT_COUNTRYBUSINESS")
                                sWork = Convert.ToString(doRSCN.GetFieldVal("TXT_COUNTRYBUSINESS"));
                                if (sWork != "")
                                {
                                    sAddress = sAddress + Constants.vbCrLf + sWork;
                                }
                                doForm.doRS.SetFieldVal("TXT_Address", sAddress);
                                // goP.TraceLine("Activity_FillAddress about to exit with False result")
                                doRSCN = null/* TODO Change to default(_) if this is not a reference type */;
                                return false;
                            }
                            else
                            // doForm.ovar.setvar("AC_FillAddressFalse", "1")
                            {
                                doForm.doRS.SetFieldVal("TXT_Address", "");
                                break;
                            }
                        }
                }


            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                    goErr.SetError(ex, 45105, sProc);
                return false;
            }
            return true;
        }

        public bool AutoPPDuplicate(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 11/11/2009 Duplicates selected Product Profile
            // MI 4/25/07 CREATED BY MI 4/22/07
            // PURPOSE:
            // Duplicate an existing Product Profile

            string sID = goUI.GetLastSelected("SELECTEDRECORDID");
            clRowSet doRowset;
            string sFileName;

            string sOrigPPName;
            string sOrigPPID;

            // goP.TraceLine("sID: " & sID & "'", "", sProc)
            sFileName = Strings.UCase(goTR.GetFileFromSUID(sID));
            if (sFileName != "PP")
            {
                goUI.NewWorkareaMessage("Please select a Product Profile first.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, par_doArray, "", "", "", "", "");
                return true;
            }

            // Check if have permissions
            if (goData.GetAddPermission("PP") == false)
            {
                goUI.NewWorkareaMessage("You cannot duplicate the selected Product Profile because you don't have permissions to create Product Profiles.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, par_doArray, "", "", "", "", "");
                return true;
            }

            // Copy the selected record
            // Get doRowset of current record
            doRowset = new clRowSet(sFileName, clC.SELL_EDIT, "GID_ID='" + sID + "'", null/* Conversion error: Set to default value for this argument */, "**", 1);
            if (doRowset.Count() < 1)
            {
                goUI.NewWorkareaMessage("The selected record can't be found in the database. It may have been deleted by another user, or may be a new model that has not yet been saved. Select a different record and start again or save the new model before duplicating.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject, par_doArray, "", "", "", "", "");
                return true;
            }
            else
            {
                sOrigPPName = Convert.ToString(doRowset.GetFieldVal("SYS_Name"));
                sOrigPPID = Convert.ToString(doRowset.GetFieldVal("GID_ID"));
            }

            // Create the new Product Profile form

            Form doForm = new Form(sFileName, "", "CRU_" + sFileName);
            clRowSet doRsForm = doForm.doRS;
            // Copy this product profile to the new form's rowset
            if (!goData.CopyRecord(ref doRowset, ref doRsForm))
            {
                goErr.SetError(35000, sProc, "Copying the selected Product Profile '" + sID + "' failed.");
                return false;
            }

            doForm.MessagePanel("This is a duplicate of the Product Profile '" + sOrigPPName + "'." + Constants.vbCrLf + "Fill out the form and click Save.", null/* Conversion error: Set to default value for this argument */, null/* Conversion error: Set to default value for this argument */, "Info.gif");

            goUI.Queue("FORM", doForm); // from menu

            // Clean up objects
            doRowset = null/* TODO Change to default(_) if this is not a reference type */;

            return true;
        }

        public bool CalcQuoteTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 11/11/2009 Moved to top
            par_bRunNext = false;

            // CS 6/26/08: Overriding core b/c of GST Tax %
            // PURPOSE:
            // Calculate totals of a Quote from Quote Lines independent of the form context.
            // RETURNS:
            // True if successful, False if not with SetError. Returns calculation results
            // via gop:SetVar() in the following variables:
            // CUR_SUBTOTAL
            // CUR_SUBTOTALT
            // CUR_SALESTAX
            // CUR_TOTAL	

            clRowSet doLines = null;
            int lI = 0;
            decimal cWork = default(decimal);
            //Non-taxable subtotals
            decimal cWorkT = default(decimal);       // Taxable subtotals only
            double rTaxPercent;
            decimal cTaxAmount;
            decimal cOtherCharge;
            decimal cTotalAmount;
            decimal cShipAmount = default(decimal);
            // Dim s1 As String = par_s1   'Quote GID_ID
            // Dim s2 As String = par_s2   'Quote Line GID_ID 
            // Dim s3 As String = par_s3
            // Dim s4 As String = par_s4
            // Dim s5 As String = par_s5
            clRowSet doRS = (clRowSet)par_doCallingObject;      // Quote rowset
            bool bCommit = false;
            bool bQLTaxable = false;
            decimal cQLSubtotal = default(decimal);
            bool bQLFound = false;

            decimal cGSTTaxAmount = default(decimal);

            // Vars are:
            // s1 = QT GID_ID
            // s2 = QL GID_ID
            // s3 = QL CHK_TAXABLE
            // s4 = QL CUR_Subtotal

            // Vars used to be:
            // s1 = goP.GetVar("QUOTE_GID_ID")
            // s2 = goP.GetVar("QUOTE_CHK_INCLUDETAXCHARGES")
            // s3 = goP.GetVar("QUOTE_SR__SALESTAXPERCENT")
            // s4 = goP.GetVar("QUOTE_CUR_OTHERCHARGE")
            // s5 = goP.GetVar("QUOTE_CUR_SHIPPING")


            // ------------- Validate parameters ----------------
            if (!goData.IsFileValid(goTR.GetFileFromSUID(par_s1.ToString())))
            {
                goErr.SetError(10100, sProc, null/* Conversion error: Set to default value for this argument */, goTR.GetFileFromSUID(par_s1), "File extracted from SUID in par_s1: '" + par_s1 + "'. Be sure to pass the GID_ID value of the Quote to recalculate.");
                // 10100: Invalid file name '[1]'. [2]
                return false;
            }
            if (par_s2.ToString() == "")
            {
                // Override QL ID not provided - ignore the rest of QL parameters
                par_s3 = "";
                par_s4 = "";
            }
            else
            {
                // Quote Line GID_ID was passed
                // QL's CHK_Taxable value
                if (par_s3.ToString() == "")
                {
                    goErr.SetError(35000, sProc, "par_s3 is blank. QL's CHK_Taxable value must be passed through this parameter when QL's GID_ID is passed via par_s2.");
                    return false;
                }
                else
                    bQLTaxable = goTR.StringToCheckbox(par_s3, false, ref par_iValid);
                // QL's CUR_Subtotal value
                if (par_s4.ToString() == "")
                {
                    goErr.SetError(35000, sProc, "par_s4 is blank. QL's CUR_Subtotal value must be passed through this parameter when QL's GID_ID is passed via par_s2.");
                    return false;
                }
                else
                    cQLSubtotal = Convert.ToDecimal(par_s4.ToString());
            }

            // -------------- Read Lines and calculate their amounts ------------
            doLines = new clRowSet("QL", 3, "LNK_IN_QT='" + par_s1.ToString() + "'", goData.GetDefaultSort("QL"), "GID_ID, CHK_TAXABLE, CUR_SUBTOTAL");
            // Browse through the rowset
            lI = 1;
            if (doLines.GetFirst() == 1)
            {
                do
                {
                    // Add up Quote Lines. Skip the one for which GID_ID is passed via par_s2
                    if (string.IsNullOrEmpty(par_s2.ToString()) | Strings.UCase(par_s2) != Strings.UCase(doLines.GetFieldVal("GID_ID", 1).ToString()))
                    {
                        if (Convert.ToInt32(doLines.GetFieldVal("CHK_TAXABLE", 2)) == 1)
                        {
                            cWorkT += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));
                        }
                        else
                        {
                            cWork += Convert.ToDecimal(doLines.GetFieldVal("CUR_SUBTOTAL", 2));
                        }
                    }

                    if (doLines.GetNext() == 0)
                        break;
                    lI += 1;
                }
                while (true);
            }
            // delete(doLines)
            doLines = null;

            // Add the Quote Line passed via parameters
            if (par_s2 != "")
            {
                if (bQLTaxable == true)
                {
                    cWorkT += cQLSubtotal;
                }
                else
                {
                    cWork += cQLSubtotal;
                }
            }

            // Subtotal = cWork + cWorkT
            // SubtotalT = cWorkT

            // ---------- Pull up the Quote -----------
            if (doRS == null)
            {
                // Get the quote from disk
                bCommit = true;
                // doRS = New clRowSet("QT", 1, _
                // "GID_ID='" & par_s1 & "'", _
                // "DTT_TIME ASC", _
                // "CUR_SUBTOTAL, CUR_SUBTOTALT, CUR_SALESTAX, CUR_TOTAL")
                // CS 7/26/07: Currently if you open a QT that has a required field missing
                // such as NA date, edit a QL and save the QL you get an error. Trying to avoid
                // that by bypassing validation.
                // TLD 10/6/2010 Mod RS to optimize
                // doRS = New clRowSet("QT", 1, _
                // "GID_ID='" & par_s1 & "'", _
                // "DTT_TIME ASC", _
                // "CUR_SUBTOTAL, CUR_SUBTOTALT, CUR_SALESTAX, CUR_TOTAL", , , , , , , True)
                doRS = new clRowSet("QT", 1, "GID_ID='" + par_s1 + "'", "DTT_TIME ASC", "*", -1, null, null, null, null, null, true);
            }
            else
                // Validate the passed Rowset object
                if (Strings.UCase(doRS.GetFileName()) != "QT")
            {
                goErr.SetError(35000, sProc, "The file of the rowset in par_doCallingObject parameter is not QT (Quote). Either pass a Quote rowset or only pass the Quote GID_ID in par_s1 parameter.");
                return false;
            }

            // ----------- Read Quote data and calculate -------------
            if (doRS.GetFirst() == 1)
            {
                rTaxPercent = Convert.ToDouble(doRS.GetFieldVal("SR__SALESTAXPERCENT", clC.SELL_SYSTEM));    // s3
                cTaxAmount = cWorkT * Convert.ToDecimal(rTaxPercent) / 100;     // cTaxAmount goes into CUR_SALESTAX
                                                                                // If the 'Include Tax/Charges' check-box is not checked, do not add tax,
                                                                                // other charge and shipping to Total. 
                if (Convert.ToInt32(doRS.GetFieldVal("CHK_INCLUDETAXCHARGES", clC.SELL_SYSTEM)) == 1)
                {
                    cOtherCharge = Convert.ToDecimal(doRS.GetFieldVal("CUR_OTHERCHARGE", clC.SELL_SYSTEM));   // s4
                    cShipAmount = Convert.ToDecimal(doRS.GetFieldVal("CUR_SHIPPING", clC.SELL_SYSTEM)); // s5
                                                                                                        // CS 6/26/08 CUSTOM
                    if (Convert.ToInt32(doRS.GetFieldVal("DR__GSTTAXPERCENT", 2)) != 0)
                        cGSTTaxAmount = (Convert.ToDecimal(cWorkT + cShipAmount) * (Convert.ToDecimal(doRS.GetFieldVal("DR__GSTTAXPERCENT", 2)) / 100));
                    // cTotalAmount goes to CUR_TOTAL
                    cTotalAmount = cWork + cWorkT + cTaxAmount + cOtherCharge + cShipAmount + cGSTTaxAmount;
                }
                else
                    // cTotalAmount goes to CUR_TOTAL
                    cTotalAmount = cWork + cWorkT;
            }
            else
            {
                // goP.TraceLine("doRS GetFirst not found", "", sProc)
                doRS = null/* TODO Change to default(_) if this is not a reference type */;
                goErr.SetError(30032, sProc, "", "Quote");
                // The linked [1] can't be updated because it can't be found. 
                // goP.TraceLine("Return False", "", sProc)
                return false;
            }

            // --------------- Update calculated fields ----------------
            doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cWork + cWorkT), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_SUBTOTALT", goTR.RoundCurr(cWorkT), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_SALESTAX", goTR.RoundCurr(cTaxAmount), clC.SELL_SYSTEM);
            doRS.SetFieldVal("CUR_GSTSALESTAX", goTR.RoundCurr(cGSTTaxAmount, 2));
            doRS.SetFieldVal("CUR_TOTAL", goTR.RoundCurr(cTotalAmount), clC.SELL_SYSTEM);
            // VS 03032014 TKT#383 : Calculate CUR_TOTALBEFORETAX as Subtotal + Shipping
            doRS.SetFieldVal("CUR_TOTALBEFORETAX", goTR.RoundCurr(cWork + cWorkT + cShipAmount), clC.SELL_SYSTEM);

            // --------------- Save the Quote ---------------
            if (bCommit)
            {
                goP.SetVar("bDoNotUpdateQuoteLines", "1");
                // Save to disk
                if (doRS.Commit() != 1)
                {
                    // goP.TraceLine("Commit failed, raising error", "", sProc)
                    goP.DeleteVar("bDoNotUpdateQuoteLines");
                    doRS = null/* TODO Change to default(_) if this is not a reference type */;
                    goErr.SetError(35000, sProc, "Error updating the Quote '" + par_s1 + "'."); // CS
                                                                                                // goP.TraceLine("Return False", "", sProc)
                    return false;
                }
                else
                {
                    goP.DeleteVar("bDoNotUpdateQuoteLines");
                    if (!(doRS == null))
                        doRS = null;
                }
            }
            par_doCallingObject = doRS;
            return true;
        }


        public bool CN_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 9/10/2008 Sets Email field label to green
            Form doform = (Form)par_doCallingObject;
            string sColor = "#0033CC";
            doform.SetFieldProperty("CHK_MAILINGLIST", "LABELCOLOR", sColor);

            // TLD 10/21/2008 Customer wants company name in TXT_ADDRBUSINESS field by default when creating new record
            string sWork = null;
            string sCustNameL = null;
            string sStreet = null;
            long lWork;
            clArray oArray = new clArray();

            if (doform.GetMode() == "CREATION")
            {
                if (doform.doRS.GetLinkCount("LNK_RELATED_CO") != 0)
                {
                    oArray = doform.doRS.GetLinkVal("LNK_RELATED_CO", ref oArray, true, 0, -1, "A_a", ref oTable);
                    oTable = null;
                    sCustNameL = goData.GetFieldValueFromRec(oArray.GetItem(1), "TXT_COMPANYNAME").ToString();
                    sStreet = goData.GetFieldValueFromRec(oArray.GetItem(1), "TXT_ADDRMAILING").ToString();
                    // Remove the Company name from Company's address, if on first line
                    if (sStreet != "")
                    {
                        lWork = Strings.InStr(sStreet, Constants.vbCrLf);
                        if (lWork > 0)
                        {
                            sWork = Strings.Mid(sStreet, 1, Convert.ToInt32(lWork - 1));
                            if (sWork == sCustNameL)
                            {
                                sStreet = goTR.FromTo(sStreet, lWork + 2);
                            }
                        }
                        doform.doRS.SetFieldVal("TXT_ADDRBUSINESS", sCustNameL + Constants.vbCrLf + sStreet);
                    }
                }
            }
            par_doCallingObject = doform;
            return true;
        }

        public bool CN_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 11/11/2009 Moved to top
            par_bRunNext = false;

            // clRowSet doRS;
            clArray doLink;
            string sFileName = "CN";
            string sSysName = "";

            clRowSet doRS = (clRowSet)par_doCallingObject;

            if (doRS.bBypassValidation != true)
            {
            }

            // Fill Original Creation Date/Time if blank
            if (doRS.GetInfo(clC.SELL_ROWSET_TYPE) == "2")
            {
                if (doRS.GetFieldVal("dtt_origcreatedtime", 1) == "")
                {
                    doRS.SetFieldVal("dtt_origcreatedtime", "Today|Now");
                }
            }


            // Fill the Company Name Text if empty
            if (doRS.GetFieldVal("TXT_COMPANYNAMETEXT") == "")
            {
                doRS.SetFieldVal("TXT_COMPANYNAMETEXT", doRS.GetFieldVal("LNK_Related_CO%%TXT_COMPANYNAME"));
            }

            // Uppercase the State fields
            // doRS.SetFieldVal("TXT_STATEBUSINESS", UCase(doRS.GetFieldVal("TXT_STATEBUSINESS")))
            // doRS.SetFieldVal("TXT_STATEHOME", UCase(doRS.GetFieldVal("TXT_STATEHOME")))
            // doRS.SetFieldVal("TXT_STATEOTHER", UCase(doRS.GetFieldVal("TXT_STATEOTHER")))


            // ---------- AUTO-FILLED FIELDS
            if (goP.GetRunMode() != "Import")
            {
                scriptManager.RunScript("Contact_ConnectVendors", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                scriptManager.RunScript("Contact_ConnectSource", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);
                scriptManager.RunScript("Contact_CalcReviewOverdue", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections);

                if (doRS.GetLinkCount("LNK_RELATED_IU") == 0)
                // Connect Related Industry Code from Related Company
                {
                    doRS.SetFieldVal("LNK_RELATED_IU", doRS.GetFieldVal("LNK_RELATED_CO%%LNK_RELATED_IU", 2), 2);
                }

                if (doRS.GetFieldVal("TXT_TITLETEXT") == "")
                {
                    if (doRS.GetLinkCount("LNK_RELATED_JF") == 1)
                    {
                        clArray oArray = new clArray();
                        oTable = null;
                        oArray = doRS.GetLinkVal("LNK_RELATED_JF", ref oArray, true, 0, -1, "A_a", ref oTable);
                        doRS.SetFieldVal("TXT_TITLETEXT", goData.GetFieldValueFromRec(oArray.GetItem(1), "TXT_JOBFUNCNAME"));
                    }
                }

                // LNK_INVOLVEDIN_ACTIVITY
                doLink = new clArray();
                oTable = null;
                // doLink = doRS.GetLinkVal("LNK_CONNECTED_ACTIVITY",doLink,False)
                // doLink = doRS.GetLinkVal("LNK_ORIGINALIN_ACTIVITY",doLink,False)
                doLink = doRS.GetLinkVal("LNK_CCIN_AC", ref doLink, true, 0, -1, "A_a", ref oTable);
                doLink = doRS.GetLinkVal("LNK_BCIN_AC", ref doLink, true, 0, -1, "A_a", ref oTable);
                doRS.SetLinkVal("LNK_INVOLVEDIN_AC", doLink);
                doLink = null/* TODO Change to default(_) if this is not a reference type */;

                // LNK_INVOLVEDIN_OPP
                doLink = new clArray();
                doLink = doRS.GetLinkVal("LNK_ORIGINATED_OP", ref doLink, true, 0, -1, "A_a", ref oTable);
                doRS.SetLinkVal("LNK_INVOLVEDIN_OP", doLink);
                doLink = null/* TODO Change to default(_) if this is not a reference type */;

                // LNK_INVOLVEDIN_QUOTE
                doLink = new clArray();
                doLink = doRS.GetLinkVal("LNK_TOIN_QT", ref doLink, true, 0, -1, "A_a", ref oTable);
                doLink = doRS.GetLinkVal("LNK_CCIN_QT", ref doLink, true, 0, -1, "A_a", ref oTable);
                doLink = doRS.GetLinkVal("LNK_BCIN_QT", ref doLink, true, 0, -1, "A_a", ref oTable);
                doRS.SetLinkVal("LNK_INVOLVEDIN_QT", doLink);
                doLink = null/* TODO Change to default(_) if this is not a reference type */;
            }
            par_doCallingObject = doRS;
            return true;
        }

        public bool FIND_FormControlOnChange_BTN_COSearch_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            // CS 8/17/09: Do NOT consider already saved filter of the desktop.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 11/11/2009 Added for Custom Project Search
            par_bRunNext = false;

            Form oForm = (Form)par_doCallingObject;

            // Find dialog; Company tab Search button
            string sName;
            string sCode;
            string sZip;
            string sCity;
            string sState;
            string sCountry;
            string sPhone;
            string sFilterPhone = "";
            string sCustNo;
            int n;
            // TLD 11/11/2009 Added for Territor
            string sTerr;

            string sView = null; ;
            int iCondCount = 0;
            string sViewCondition;
            string sNewCondition;
            int iOrigCondCount = 0;
            int i;
            string sFilter = "";
            Desktop oDesktop = null;

            // Get values from form
            sName = Strings.Trim(oForm.GetControlVal("NDB_TXT_COMPANYNAME"));
            sCode = Strings.Trim(oForm.GetControlVal("NDB_TXT_CODE"));
            sZip = oForm.GetControlVal("NDB_TXT_ZIPBUSINESS");
            sCity = oForm.GetControlVal("NDB_TXT_CITYBUSINESS");
            sState = oForm.GetControlVal("NDB_TXT_STATEBUSINESS");
            sCountry = oForm.GetControlVal("NDB_TXT_COUNTRYBUSINESS");
            sPhone = oForm.GetControlVal("NDB_TXT_PHONE");
            sCustNo = oForm.GetControlVal("NDB_TXT_CUSTNO");
            // TLD 11/11/2009 Added for Territor
            sTerr = Strings.Trim(oForm.GetControlVal("NDB_TXT_TERRITORY"));

            // Use values to filter Company - Search Results desktop if it exists
            oDesktop = new Desktop("GLOBAL", "DSK_CDC43A16-6CC5-4D73-5858-9AF1013E4F05", true, "");
            // Edit views in DT

            // View 1:Companies - Search Results
            sView = oDesktop.GetViewMetadata("VIE_14D622C2-C038-4D39-5858-9AF1013E4F05");
            // iCondCount = goTR.StringToNum(goTR.StrRead(sView, "CCOUNT"))

            // 'If CCOUNT=1 need to check if C1FIELDNAME=<%ALL%> b/c in that case I need to overwrite
            // If iCondCount = 1 Then
            // If goTR.StrRead(sView, "C1FIELDNAME") = "<%ALL%>" Then
            // iCondCount = 0 'Will overwrite these values
            // End If
            // End If
            // Original condition count
            iOrigCondCount = iCondCount;

            // Only want to filter if the NDB fields contained a value
            // Get the total # of conditions
            if (sName != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sCode != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sZip != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sCity != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sPhone != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sCustNo != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sState != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sCountry != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sTerr != "")
            {
                iCondCount = iCondCount + 1;
            }

            // Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            if (sName != "")
            {
                // Add 'Company Name' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_COMPANYNAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sName);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_COMPANYNAME['" + sName + "'";
                }
                else
                {
                    sFilter = "TXT_COMPANYNAME['" + sName + "'";
                }
            }
            if (sCode != "")
            {
                // Add 'Company Code' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_CUSTCODE%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCode);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_CUSTCODE['" + sCode + "'";
                }
                else
                {
                    sFilter = "TXT_CUSTCODE['" + sCode + "'";
                }
            }
            if (sZip != "")
            {
                // Add 'Zip Mailing' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_ZIPMAILING%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sZip);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_ZIPMAILING['" + sZip + "'";
                }
                else
                {
                    sFilter = "TXT_ZIPMAILING['" + sZip + "'";
                }
            }
            if (sCity != "")
            {
                // Add 'City Mailing' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_CITYMAILING%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCity);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_CITYMAILING['" + sCity + "'";
                }
                else
                {
                    sFilter = "TXT_CITYMAILING['" + sCity + "'";
                }
            }
            // CS 12/1/08
            if (sState != "")
            {
                // Add 'State Mailing' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_STATEMAILING%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sState);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND TXT_STATEMAILING['" + sState + "'";
                else
                    sFilter = "TXT_STATEMAILING['" + sState + "'";
            }
            // CS 12/1/08
            if (sCountry != "")
            {
                // Add 'Country Mailing' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_COUNTRYMAILING%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCountry);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_COUNTRYMAILING['" + sCountry + "'";
                }
                else
                {
                    sFilter = "TXT_COUNTRYMAILING['" + sCountry + "'";
                }
            }
            if (sPhone != "")
            {
                // Add 'Phone' condition
                // Replace all non-numeric characters (not 0-9) with %. This will make sure we pick up all possible
                // phone number formats. Loop thru all characters in Phone
                for (n = 1; n <= Strings.Len(sPhone); n++)
                {
                    if (!goTR.IsNumeric(Strings.Mid(sPhone, n, 1)))
                    // Replace this character with '%'
                    {
                        sFilterPhone = sFilterPhone + "%";
                    }
                    else
                    {
                        sFilterPhone = sFilterPhone + Strings.Mid(sPhone, n, 1);
                    }
                }

                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TEL_PHONENO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sFilterPhone);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TEL_PHONENO['" + sFilterPhone + "'";
                }
                else
                {
                    sFilter = "TEL_PHONENO['" + sFilterPhone + "'";
                }
            }
            if (sCustNo != "")
            {
                // Add 'Cust No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_CUSTNO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCustNo);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_CUSTNO['" + sCustNo + "'";
                }
                else
                {
                    sFilter = "TXT_CUSTNO['" + sCustNo + "'";
                }
            }
            if (sTerr != "")
            {
                // Add 'Territory' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_TERRITORY%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sTerr);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_TERRITORY['" + sTerr + "'";
                }
                else
                {
                    sFilter = "TXT_TERRITORY['" + sTerr + "'";
                }
            }

            // Edit CONDITION= line in view MD
            // sViewCondition = goTR.StrRead(sView, "CONDITION")
            // If sViewCondition = "" Then
            sNewCondition = sFilter; // No filter in view already
                                     // Else
                                     // sNewCondition = sViewCondition & " AND " & sFilter
                                     // End If
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            oDesktop.SetViewMetadata("VIE_14D622C2-C038-4D39-5858-9AF1013E4F05", sView);
            sView = "";
            sViewCondition = "";
            sNewCondition = "";
            iCondCount = 0;

            // Que Company Search Results desktop
            goUI.Queue("DESKTOP", oDesktop);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
            goUI.Queue("", "");
            par_doCallingObject = oForm;
            return true;
        }

        public bool FIND_FormControlOnChange_BTN_PPCancel(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form oForm = (Form)par_doCallingObject;

            // TLD 12/9/2008 Added for custom Product Profile Find
            // Find dialog; appt tab Cancel button
            // If click Cancel, close the form
            oForm.CloseOnReturn = true;
            par_doCallingObject = oForm;
            return true;
        }

        public bool FIND_FormControlOnChange_BTN_PPSearch(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form oForm = (Form)par_doCallingObject;

            // TLD 12/10/2008 Added for custom Product Profile Search

            // Find dialog; Appt tab Search button
            string sSensorModel;
            string sForSKU = null;
            string sNotes = null;
            // TLD 11/11/2009 Added 2 new fields
            string sSerial;
            string sSensorSerial;


            string sView;
            int iCondCount = 0; ;
            string sViewCondition;
            string sNewCondition;
            int iOrigCondCount;
            int i;
            string sFilter = "";
            Desktop oDesktop = null;

            // Get values from form
            sNotes = Strings.Trim(oForm.GetControlVal("NDB_TXT_NOTES"));
            sForSKU = Strings.Trim(oForm.GetControlVal("NDB_TXT_SKU"));
            sSensorModel = Strings.Trim(oForm.GetControlVal("NDB_TXT_SENSORMODEL"));
            // TLD 11/11/2009 Added 2 new fields
            sSerial = Strings.Trim(oForm.GetControlVal("NDB_TXT_SERIALNO"));
            sSensorSerial = Strings.Trim(oForm.GetControlVal("NDB_TXT_SENSORSERIAL"));

            // TLD 11/16/2009 Changed to Search Results: Product Profiles desktop
            // Use values to filter Product Profile - Search Results desktop if it exists
            // Dim oDesktop As New clDesktop("GLOBAL", "DSK_914CE7D2-5B4F-4421-5858-9AB701389535")
            oDesktop = new Desktop("GLOBAL", "DSK_54DE5361-897E-46F4-5858-9CC301349474");
            // Edit views in DT

            // TLD 11/16/2009 Changed to Search Results Product Profile view
            // View 1:Product Pfile - Search Results
            // sView = oDesktop.GetViewMetadata("VIE_E95E7426-43EB-4934-5858-9AB701389535")
            sView = oDesktop.GetViewMetadata("VIE_C62612B6-A4F5-4EEF-5858-9CC301349474");
            iCondCount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead("", "CCOUNT"), "", ref par_iValid, ""));
            //goTR.StrWrite(ref sView, "CCOUNT", iCondCount);

            // If CCOUNT=1 need to check if C1FIELDNAME=<%ALL%> b/c in that case I need to overwrite
            if (iCondCount == 1)
            {
                if (goTR.StrRead(sView, "C1FIELDNAME") == "<%ALL%>")
                {
                    iCondCount = 0;
                }// Will overwrite these values
            }
            // Original condition count
            iOrigCondCount = iCondCount;

            // Only want to filter if the NDB fields contained a value
            // Get the total # of conditions
            if (sNotes != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sForSKU != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sSensorModel != "")
            {
                iCondCount = iCondCount + 1;
            }
            // TLD 11/11/2009 Added 2 new fields
            if (sSerial != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sSensorSerial != "")
            {
                iCondCount = iCondCount + 1;
            }

            // Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            if (sNotes != "")
            {
                // Add 'Notes' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%MMO_NOTES%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sNotes);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND MMO_NOTES[" + sNotes + "";
                }
                else
                {
                    sFilter = "MMO_NOTES['" + sNotes + "'";
                }
            }
            if (sForSKU != "")
            {
                // Add Coordinated by user Sys Name condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_FOR_MO%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sForSKU);
                i = i + 1;
                if (sFilter != "")
                    sFilter = sFilter + " AND LNK_FOR_MO%%SYS_NAME[" + sForSKU + "";
                else
                    sFilter = "LNK_FOR_MO%%SYS_NAME['" + sForSKU + "'";
            }
            if (sSensorModel != "")
            {
                // Add 'Description' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_SENSORMODEL%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sSensorModel);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_SENSORMODEL['" + sSensorModel + "'";
                }
                else
                {
                    sFilter = "TXT_SENSORMODEL['" + sSensorModel + "'";
                }
            }
            // TLD 11/11/2009 Added for Serial Number
            if (sSerial != "")
            {
                // Add 'Serial Number' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_SERIALNO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sSerial);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_SERIALNO['" + sSerial + "'";
                }
                else
                {
                    sFilter = "TXT_SERIALNO['" + sSerial + "'";
                }
            }
            // TLD 11/11/2009 Added for Sensor Serial Number
            if (sSensorSerial != "")
            {
                // Add 'Sensor Serial Number' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_SENSORSERIALNO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sSensorSerial);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_SENSORSERIALNO['" + sSensorSerial + "'";
                }
                else
                {
                    sFilter = "TXT_SENSORSERIALNO['" + sSensorSerial + "'";
                }
            }

            // Edit CONDITION= line in view MD
            sViewCondition = goTR.StrRead(sView, "CONDITION");
            if (sViewCondition == "")
            {
                sNewCondition = sFilter; // No filter in view already
            }
            else
            {
                sNewCondition = sViewCondition + " AND " + sFilter;
            }
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            // TLD 11/16/2009 Changed to view on Search Results desktop
            // oDesktop.SetViewMetadata("VIE_E95E7426-43EB-4934-5858-9AB701389535", sView)
            oDesktop.SetViewMetadata("VIE_C62612B6-A4F5-4EEF-5858-9CC301349474", sView);
            sView = "";
            sViewCondition = "";
            sNewCondition = "";
            iCondCount = 0;

            // Que Appointment Search Results desktop
            goUI.Queue("DESKTOP", oDesktop);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
            goUI.Queue("", "");
            return true;
        }

        public bool FIND_FormControlOnChange_BTN_QTSearch_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process

            // CS 8/17/09: Do NOT consider already saved filter of the desktop.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 11/11/2009 Added for Custom Quote Search
            par_bRunNext = false;

            Form oForm = (Form)par_doCallingObject;

            // Find dialog; Quote tab Search button
            string sQuoteNo;
            string sQuoteRefNo;
            string sCompany;
            string sCreditedTo;
            // TDL 11/11/2009 Added for PO #
            string sPONO;


            string sView;
            int iCondCount = 0;
            string sViewCondition;
            string sNewCondition;
            int iOrigCondCount = 0;
            int i;
            string sFilter = "";
            Desktop oDesktop = null;

            // Get values from form
            sQuoteNo = Strings.Trim(oForm.GetControlVal("NDB_TXT_QUOTENO"));
            sQuoteRefNo = Strings.Trim(oForm.GetControlVal("NDB_TXT_QUOTEREFNO"));
            sCompany = oForm.GetControlVal("NDB_TXT_COMPANY");
            sCreditedTo = oForm.GetControlVal("NDB_TXT_CREDITEDTO");
            // TDL 11/11/2009 Added for PO #
            sPONO = Strings.Trim(oForm.GetControlVal("NDB_TXT_PONO"));

            // Use values to filter Quote - Search Results desktop if it exists
            oDesktop = new Desktop("GLOBAL", "DSK_D8E2511C-031F-45DD-5858-9AF500ECEC44");
            // Edit views in DT

            // View 1:Quote - Search Results
            sView = oDesktop.GetViewMetadata("VIE_4D836010-021D-4AD9-5858-9AF500ECEC44");
            // iCondCount = goTR.StringToNum(goTR.StrRead(sView, "CCOUNT"))

            // 'If CCOUNT=1 need to check if C1FIELDNAME=<%ALL%> b/c in that case I need to overwrite
            // If iCondCount = 1 Then
            // If goTR.StrRead(sView, "C1FIELDNAME") = "<%ALL%>" Then
            // iCondCount = 0 'Will overwrite these values
            // End If
            // End If
            // Original condition count
            iOrigCondCount = iCondCount;

            // Only want to filter if the NDB fields contained a value
            // Get the total # of conditions
            if (sQuoteNo != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sQuoteRefNo != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sCompany != "")
            {
                iCondCount = iCondCount + 1;
            }
            if (sCreditedTo != "")
            {
                iCondCount = iCondCount + 1;
            }
            // TLD 11/11/2009 Added for PO No
            if (sPONO != "")
                iCondCount = iCondCount + 1;

            // Edit view properties dialog lines
            goTR.StrWrite(ref sView, "CCOUNT", iCondCount);
            i = iOrigCondCount + 1;
            if (sQuoteNo != "")
            {
                // Add 'Quote No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_QUOTENO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sQuoteNo);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_QUOTENO['" + sQuoteNo + "'";
                }
                else
                {
                    sFilter = "TXT_QUOTENO['" + sQuoteNo + "'";
                }
            }
            if (sQuoteRefNo != "")
            {
                // Add 'Quote Ref No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_QUOTEREFNO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sQuoteRefNo);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_QUOTEREFNO['" + sQuoteRefNo + "'";
                }
                else
                {
                    sFilter = "TXT_QUOTEREFNO['" + sQuoteRefNo + "'";
                }
            }
            if (sCompany != "")
            {
                // Add 'To Company' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_TO_CO%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCompany);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND LNK_TO_CO%%SYS_NAME['" + sCompany + "'";
                }
                else
                {
                    sFilter = "LNK_TO_CO%%SYS_NAME['" + sCompany + "'";
                }
            }
            if (sCreditedTo != "")
            {
                // Add 'Credited To User' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%LNK_CREDITEDTO_US%><%SYS_NAME%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sCreditedTo);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND LNK_CREDITEDTO_US%%SYS_NAME['" + sCreditedTo + "'";
                }
                else
                {
                    sFilter = "LNK_CREDITEDTO_US%%SYS_NAME['" + sCreditedTo + "'";
                }
            }
            // TLD 11/11/2009 Added for PO NO
            if (sPONO != "")
            {
                // Add 'PO No' condition
                goTR.StrWrite(ref sView, "C" + i + "FIELDNAME", "<%TXT_PONO%>");
                goTR.StrWrite(ref sView, "C" + i + "CONDITION", "["); // contains
                goTR.StrWrite(ref sView, "C" + i + "VALUE1", sPONO);
                i = i + 1;
                if (sFilter != "")
                {
                    sFilter = sFilter + " AND TXT_PONO['" + sPONO + "'";
                }
                else
                {
                    sFilter = "TXT_PONO['" + sPONO + "'";
                }
            }

            // Edit CONDITION= line in view MD
            // sViewCondition = goTR.StrRead(sView, "CONDITION")
            // If sViewCondition = "" Then
            sNewCondition = sFilter; // No filter in view already
                                     // Else
                                     // sNewCondition = sViewCondition & " AND " & sFilter
                                     // End If
            goTR.StrWrite(ref sView, "CONDITION", sNewCondition);

            oDesktop.SetViewMetadata("VIE_4D836010-021D-4AD9-5858-9AF500ECEC44", sView);
            sView = "";
            sViewCondition = "";
            sNewCondition = "";
            iCondCount = 0;

            // Que OP Search Results desktop
            goUI.Queue("DESKTOP", oDesktop);
            //HttpContext.Current.Response.Redirect(goUI.Navigate("", ""));
            goUI.Queue("", "");
            return true;
        }

        public bool GenerateSysName_Post(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;
            string sTemp = "";
            string sTemp2 = "";
            string sTemp3 = "";
            string sTemp4 = "";
            string sFileName = doRS.GetFileName();
            string sResult = "";

            // We assume that sFileName is valid. If this is a problem, test it here and SetError.

            switch (Strings.UCase(sFileName))
            {
                case "PP":
                    {
                        if (!doRS.IsLoaded("TXT_SERIALNO"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".TXT_SERIALNO");
                            sTemp = Convert.ToString(doRS.GetFieldVal("TXT_SERIALNO"));
                        }


                        if (!doRS.IsLoaded("LNK_RELATED_CO%%SYS_NAME"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_RELATED_CO%%SYS_NAME");
                            sTemp2 = Convert.ToString(doRS.GetFieldVal("LNK_RELATED_CO%%SYS_NAME"));
                        }

                        if (!doRS.IsLoaded("DTE_DATEOFSALE"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".DTE_DATEOFSALE");
                            sTemp3 = Convert.ToString(doRS.GetFieldVal("DTE_DATEOFSALE"));
                        }

                        if (!doRS.IsLoaded("LNK_FOR_MO%%SYS_NAME"))
                        {
                            goErr.SetError(35103, sProc, null/* Conversion error: Set to default value for this argument */, sFileName + ".LNK_FOR_MO%%SYS_NAME");
                            sTemp4 = Convert.ToString(doRS.GetFieldVal("LNK_FOR_MO%%SYS_NAME"));
                        }

                        sResult = sTemp + " " + Strings.Left(sTemp2, 20) + " " + sTemp3 + " " + Strings.Left(sTemp4, 14);
                        par_oReturn = sResult;
                        break;
                    }
            }
            par_doCallingObject = doRS;
            return true;
        }

        public bool GetDefaultSort(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_sFileName: file for which to return the sort.
            // par_sReverseDirection: "1" causes the direction to be reversed from the 'normal' order, "0" is the default.
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            // PURPOSE:
            // Override goData.getDefaultSort, if necessary, by setting a default sort for any file(s).
            // By default the sort is SYS_Name ASC. If you create new files that require a custom sort,
            // add CASEs for them here. To not override the default sort, par_oReturn must be "".
            // IMPORTANT: Keep this "in sync" with GenerateSysName. For example, if the SYS_Name starts 
            // with a date, you may want the sort to be DESC whereas if it starts with a Company Name,
            // the sort likely should be ASC.
            // RETURNS:
            // Always True. The sort string is returned via par_oReturn parameter.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            string sResult = "";

            // Select Case (par_sFileName)
            // Case "AA"
            // 'This is a reverse sort, typically used for datetime fields
            // If par_sReverseDirection = "1" Then
            // sResult = "SYS_NAME ASC"
            // Else
            // sResult = "SYS_NAME DESC"
            // End If
            // Case "BB"
            // 'Reverse sort on Creation datetime
            // If par_sReverseDirection = "1" Then
            // sResult = "DTT_CREATIONTIME ASC"
            // Else
            // sResult = "DTT_CREATIONTIME DESC"
            // End If
            // 'Case Else
            // '    'Standard ascending sort for selection files like CO, CN, PD is coded in clScripts
            // '    'it is not needed here
            // End Select

            par_oReturn = sResult;

            return true;
        }

        public bool QL_FormOnLoadRecord_Post(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // SKO 06302015 TKT:604 
            clRowSet doRSQT = new clRowSet("QT", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_IN_QT") + "'", null/* Conversion error: Set to default value for this argument */, "CHK_Invoiced");
            if (doRSQT.GetFirst() == 1)
            {
                if (Convert.ToInt32(doRSQT.GetFieldVal("CHK_Invoiced", 2)) == 1)
                {
                    clRowSet doRSUS = new clRowSet("US", 3, "GID_ID='" + goP.GetMe("ID") + "'", null/* Conversion error: Set to default value for this argument */, "CHK_QuoteInvoiced");
                    if (doRSUS.GetFirst() == 1)
                    {
                        if (Convert.ToInt32(doRSUS.GetFieldVal("CHK_QuoteInvoiced", 2)) != 1)
                        {
                            doForm.SetControlState("BTN_SAVE", 4);
                            doForm.SetControlState("BTN_SAVEANDLEAVEOPEN", 4);
                            doForm.MessagePanel("This Quote Line Quote has been invoiced and is not Editable.");
                            return false;
                        }
                    }
                }
            }
            par_doCallingObject = doForm;
            return true;
        }

        public bool QL_RecordOnSave_Post(ref object par_doCallingObject, ref string par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sMode = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // TLD 10/24/2011 Fill Group from QT on Save
            if (doRS.GetLinkCount("LNK_In_QT") == 1)
            {
                doRS.SetFieldVal("LNK_Related_GR", doRS.GetFieldVal("LNK_In_QT%%LNK_Related_GR", 2), 2);
            }
            par_doCallingObject = doRS;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_CORRLEAVEOPEN(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            // Dim doNewForm As Object

            // TLD 8/11/08 Added button to send quote, but leave form open

            string sURL;
            string sName;
            string sID;

            sURL = "";
            sURL = "../Pages/diaSend.aspx";
            // objectotsend: VIEW, RECORD, CORR
            goTR.URLWrite(ref sURL, "objecttosend", "CORR");

            // We don't know how the user wants to send a Quote 
            // Send type: EMAIL, FAX, LETTER, or WPRESPONSE
            // goTR.URLWrite(sURL, "sendtype", "EMAIL")

            goTR.URLWrite(ref sURL, "objectid", Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            sName = Convert.ToString(doForm.doRS.GetFieldVal("SYS_Name"));
            // CS: Always update sys name in case one of the affected fields changed.
            // If sName = "" Then
            scriptManager.RunScript("GenerateSysName", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            // End If
            goTR.URLWrite(ref sURL, "objectname", sName);
            sID = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));
            // goTR.URLWrite(sURL, "returnto", "MI_Test.aspx")

            // If doForm.Save(1, , System.Reflection.MethodInfo.GetCurrentMethod().Name) = 0 Then
            // Return False
            // End If

            // CS 6/28/07:Changing to doForm.save(2) so that the _post script will be called if there is any. When
            // set to doform.save(1) the code never returned here after save so the function never
            // returned true and the post script would never be called. So now the post script can be called and I
            // just set a boolean that tells the form to close so that the Send dialog displays.
            if (doForm.Save(3, true, System.Reflection.MethodInfo.GetCurrentMethod().Name) == 0)
            {
                return false;
            }

            // TLD 9/18/2008 Commented following 2 lines per CS
            // Update fixed some stuff, broke this
            // Added third line below as a fix
            // goUI.SetNext("DIALOG", sURL) 'CS: Moved to after save call. Was before
            // doForm.CloseOnReturn = True 'flag 
            //HttpContext.Current.Response.Redirect(goUI.Navigate("DIALOG", sURL));
            goUI.Queue("DIALOG", sURL);
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_LNK_TO_CO_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            // TLD 7/16/2008 -- Fills LNK_RELATED_TR with company terms if LNK_TO_CO changes and is new quote
            if (doForm.GetMode() == "CREATION")
            {
                if (doForm.doRS.GetLinkCount("LNK_TO_CO") > 0)
                {
                    doForm.doRS.ClearLinkAll("LNK_RELATED_TR");
                    clArray doLink = new clArray();
                    doLink = new clArray();
                    oTable = null;
                    doLink = doForm.doRS.GetLinkVal("LNK_TO_CO%%LNK_HAS_TR", ref doLink, true, 0, -1, "A_a", ref oTable);
                    doForm.doRS.SetLinkVal("LNK_RELATED_TR", doLink);
                    doLink = null/* TODO Change to default(_) if this is not a reference type */;
                }
            }

            // S_B Tkt#2433 Link CO Sales Order Notes to QT Sales Order Notes.
            doForm.doRS.SetFieldVal("MMO_SALESORDER_NOTES", doForm.doRS.GetFieldVal("LNK_To_CO%%MMO_SALESORDER_NOTES"));

            doForm.doRS.ClearLinkAll("LNK_In_TE");
            // S_B Tkt#2433 Link CO Territory to QT Territory.
            doForm.doRS.SetFieldVal("LNK_In_TE", doForm.doRS.GetFieldVal("LNK_To_CO%%LNK_In_TE%%GID_ID"));

            doForm.doRS.ClearLinkAll("LNK_RELATED_CT");
            // S_B 05242019 Tkt#2844 Link CO's Country to QT Country.
            doForm.doRS.SetFieldVal("LNK_RELATED_CT", doForm.doRS.GetFieldVal("LNK_To_CO%%LNK_RELATED_CT%%GID_ID"));

            // S_B 05242019 Tkt#2844 Link CO's Market Category to QT Market Category.
            doForm.doRS.SetFieldVal("MLS_MARKETCATEGORY", doForm.doRS.GetFieldVal("LNK_To_CO%%MLS_MARKETCATEGORY"));

            //Tkt#4360 Link CO's Territory to QT Territory.
            doForm.doRS.SetFieldVal("LNK_IN_TE", doForm.doRS.GetFieldVal("LNK_To_CO%%LNK_IN_TE"));

            // If doForm.doRS.GetLinkCount("LNK_To_CO%%LNK_In_TE") > 0 Then
            // Else
            // doForm.doRS.ClearLinkAll("LNK_In_TE")
            // End If

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_LNK_ORIGINATEDBY_CN_POST(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            // par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            // par_s3 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // goP.TraceLine("", "", sProc)
            // goP.TraceLine("par_sFieldName is: '" & par_sFieldName & "'", "", sProc)

            Form doForm = (Form)par_doCallingObject;

            scriptManager.RunScript("QT_FormControlOnChange_LNK_TO_CO", ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, "", "", "", "", "");
            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sColor = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", sColor);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", sColor);
            doForm.doRS.SetFieldVal("CHK_LINEInclude", 1, 2);
            doForm.doRS.SetFieldVal("CHK_LINEREPORT", 1, 2);
            // SKO 06302015 TKT:604 
            if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_Invoiced", 2)) == 1)
            {
                clRowSet doRSUS = new clRowSet("US", 3, "GID_ID='" + goP.GetMe("ID") + "'", null/* Conversion error: Set to default value for this argument */, "CHK_QuoteInvoiced");
                if (doRSUS.GetFirst() == 1)
                {
                    if (Convert.ToInt32(doRSUS.GetFieldVal("CHK_QuoteInvoiced", 2)) != 1)
                    {
                        doForm.SetControlState("BTN_SAVE", 4);
                        doForm.SetControlState("BTN_SAVEANDLEAVEOPEN", 4);
                        doForm.MessagePanel("This Quote has been invoiced and is not Editable.");
                        return false;
                    }
                }
            }


            if (doForm.GetMode() == "CREATION")
            {
                string sID = doForm.GetCreateLinkedSourceRecSUID;

                if (!string.IsNullOrEmpty(sID))
                {
                    // S_B Tkt#2433 Co's Sales Order Notes into Linked QT Sales Order Notes and Territory fields.
                    if ((goTR.GetFileFromSUID(sID) == "CO"))
                    {
                        clRowSet doRS_CO = new clRowSet("CO", clC.SELL_READONLY, "GID_ID='" + sID + "'", null, "**");
                        if (doRS_CO.GetFirst() == 1)
                        {
                            doForm.doRS.SetFieldVal("MMO_SALESORDER_NOTES", doRS_CO.GetFieldVal("MMO_SALESORDER_NOTES"));
                            if (doRS_CO.GetLinkCount("LNK_IN_TE") > 0)
                                doForm.doRS.SetFieldVal("LNK_IN_TE", doRS_CO.GetFieldVal("LNK_IN_TE"));

                            // S_B 05242019 Tkt#2844 Link CO's Country to QT Country.
                            doForm.doRS.SetFieldVal("LNK_RELATED_CT", doRS_CO.GetFieldVal("LNK_RELATED_CT%%GID_ID"));
                            // S_B 05242019 Tkt#2844 Link CO's Market Category to QT Market Category.
                            doForm.doRS.SetFieldVal("MLS_MARKETCATEGORY", doRS_CO.GetFieldVal("MLS_MARKETCATEGORY"));
                        }
                    }
                    else if ((goTR.GetFileFromSUID(sID) == "CN"))
                    {
                        clRowSet doRS_CN = new clRowSet("CN", clC.SELL_READONLY, "GID_ID='" + sID + "'", null, "**");
                        if (doRS_CN.GetFirst() == 1)
                        {
                            doForm.doRS.SetFieldVal("MMO_SALESORDER_NOTES", doRS_CN.GetFieldVal("LNK_RELATED_CO%%MMO_SALESORDER_NOTES"));
                            // If doRS_CN.GetLinkCount("LNK_RELATED_CO%%LNK_IN_TE") > 0 Then
                            doForm.doRS.SetFieldVal("LNK_IN_TE", doRS_CN.GetFieldVal("LNK_RELATED_CO%%LNK_IN_TE%%GID_ID"));
                            // End If

                            // S_B 05242019 Tkt#2844 Link CO's Country to QT Country.
                            doForm.doRS.SetFieldVal("LNK_RELATED_CT", doRS_CN.GetFieldVal("LNK_RELATED_CO%%LNK_RELATED_CT%%GID_ID"));
                            // S_B 05242019 Tkt#2844 Link CO's Market Category to QT Market Category.
                            doForm.doRS.SetFieldVal("MLS_MARKETCATEGORY", doRS_CN.GetFieldVal("LNK_RELATED_CO%%MLS_MARKETCATEGORY"));
                        }
                    }
                    else if ((goTR.GetFileFromSUID(sID) == "AC"))
                    {
                        clRowSet doRS_AC = new clRowSet("AC", clC.SELL_READONLY, "GID_ID='" + sID + "'", null, "**");
                        if (doRS_AC.GetFirst() == 1)
                        {
                            doForm.doRS.SetFieldVal("MMO_SALESORDER_NOTES", doRS_AC.GetFieldVal("LNK_RELATED_CO%%MMO_SALESORDER_NOTES"));
                            // If doRS_AC.GetLinkCount("LNK_RELATED_CO%%LNK_IN_TE") > 0 Then
                            doForm.doRS.SetFieldVal("LNK_IN_TE", doRS_AC.GetFieldVal("LNK_RELATED_CO%%LNK_IN_TE%%GID_ID"));
                            // End If

                            // S_B 05242019 Tkt#2844 Link CO's Country to QT Country.
                            doForm.doRS.SetFieldVal("LNK_RELATED_CT", doRS_AC.GetFieldVal("LNK_RELATED_CO%%LNK_RELATED_CT%%GID_ID"));
                            // S_B 05242019 Tkt#2844 Link CO's Market Category to QT Market Category.
                            doForm.doRS.SetFieldVal("MLS_MARKETCATEGORY", doRS_AC.GetFieldVal("LNK_RELATED_CO%%MLS_MARKETCATEGORY"));
                        }
                    }
                }
            }

            // S_B Tkt#2433 Customer asked for Revert back to TXT_CURRENCY
            // 'S_B Tkt#2433 Putting Old field TXT_CURRENCY to newly created MLS_NEWCURRENCY on client request.
            // If doForm.doRS.GetFieldVal("MLS_NEWCURRENCY", 2) = 0 Then
            // Dim sCurrency As String = doForm.doRS.GetFieldVal("TXT_CURRENCY")
            // If sCurrency = "CAD$" Then
            // doForm.doRS.SetFieldVal("MLS_NEWCURRENCY", 2)
            // ElseIf (sCurrency = "US$") Then
            // doForm.doRS.SetFieldVal("MLS_NEWCURRENCY", 1)
            // Else
            // doForm.doRS.SetFieldVal("MLS_NEWCURRENCY", 0)
            // End If
            // End If

            // TLD 7/16/2008 -- Fills LNK_RELATED_TR with company terms
            if (doForm.GetMode() == "CREATION")
            {
                if (doForm.doRS.GetLinkCount("LNK_TO_CO") > 0)
                {
                    clArray doLink = new clArray();
                    oTable = null;
                    doLink = new clArray();
                    doLink = doForm.doRS.GetLinkVal("LNK_TO_CO%%LNK_HAS_TR", ref doLink, true, 0, -1, "A_a", ref oTable);
                    doForm.doRS.SetLinkVal("LNK_RELATED_TR", doLink);
                    doLink = null;
                }
            }
            else
            {
                doForm.doRS.oVar.SetVar("PO#DuplicatedNumber", Convert.ToString(doForm.doRS.GetFieldVal("TXT_PONO", 2)));
            }

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormOnLoadRecord_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            Form doForm = (Form)par_doCallingObject;

            string sColor = "#000000";
            doForm.MoveToTab(0);

            //Lines specific code
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("LNK_FORLINE_MO", "LABELCOLOR", color);
            doForm.SetFieldProperty("SR__LINEQTY", "LABELCOLOR", color);
            doForm.SetFieldProperty("CUR_LINEPRICEUNIT", "LABELCOLOR", color);


            if (doForm.doRS.iRSType == clC.SELL_EDIT)
            {
                doForm.SetControlState("BTN_PRINT", 0);
                doForm.SetControlState("BTN_CREATEREVISION", 0);
                doForm.SetControlState("BTN_PRINTSEND", 0);
            }
            else
            {
                doForm.SetControlState("BTN_PRINT", 2);
                //doForm.SetControlState("BTN_CREATEREVISION", 2);
                doForm.SetControlState("BTN_PRINTSEND", 2);
            }

            doForm.SetFieldProperty("MLS_QTTEMPLATE", "LABELCOLOR", sColor);

            ClearLineFields(doForm);

            if (doForm.GetMode() == "CREATION")
            {
                if (((doForm.oVar.GetVar("QuoteOpeningMode") == null) ? "" : doForm.oVar.GetVar("QuoteOpeningMode").ToString()) == "Revision")
                {
                    doForm.doRS.SetFieldVal("TXT_Signature", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRSIGNATURE"));
                    doForm.doRS.SetFieldVal("MMO_UNDERSIGNATURE", doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%CORRBELOWSIGNATURE"));
                    par_doCallingObject = doForm;
                }
            }

            // Gray out GST Sales TAx calculated field
            doForm.SetControlState("CUR_GSTSALESTAX", 4);

            // TLD 7/16/2008 -- Fills LNK_RELATED_TR with company terms
            //if (doForm.GetMode() == "CREATION")
            //{
            //    if (doForm.doRS.GetLinkCount("LNK_TO_CO") > 0)
            //    {
            //        clArray doLink = new clArray();
            //        oTable = null;
            //        doLink = new clArray();
            //        doLink = doForm.doRS.GetLinkVal("LNK_TO_CO%%LNK_HAS_TR", ref doLink, true, 0, -1, "A_a", ref oTable);
            //        doForm.doRS.SetLinkVal("LNK_RELATED_TR", doLink);
            //        doLink = null/* TODO Change to default(_) if this is not a reference type */;
            //    }
            //}

            // TLD 8/11/08 Added tooltip for BTN_CORRLEAVEOPEN
            doForm.SetFieldProperties("BTN_CORRLEAVEOPEN", -1, -1, -1, -1, -1, "^^^^^", -1, -1, "Send and Leave Open");

            // VS 03032014 TKT#383 : CUR_TOTALBEFORETAX  Gray Out
            doForm.SetControlState("CUR_TOTALBEFORETAX", 4); // Gray Out
            par_doCallingObject = doForm;
            return true;
        }
        public bool Quote_FillAddress_PRE(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //goP.TraceLine("", "", sProc)

            //PURPOSE:
            //		Fill the address field, first checking whether it is empty.
            //RETURNS:
            //		True

            string sContactName = "";
            string sMailingAddr = null;
            string sFirstName = null;
            string sLastName = null;
            //Dim sCompName as string
            string sAddrMail = null;
            string sCityMail = null;
            string sStateMail = null;
            string sZipMail = null;
            string sCountryMail = null;
            string scompany = null;

            //VS 03262018 TKT#2151 : Refill data whenever Contact has changed even if not empty.
            //if (!string.IsNullOrEmpty(doForm.doRS.GetFieldVal("TXT_ADDRESSMAILING").ToString()))
            //    return true;
            if (doForm.doRS.GetLinkCount("LNK_ORIGINATEDBY_CN") < 1)
                return true;

            //CS 6/22/09: Create CN rowset to get CN fields
            clRowSet doRSContact = new clRowSet("CN", 3, "GID_ID='" + doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN") + "'", "", "LNK_RELATED_CO,TXT_NAMEFIRST,TXT_NAMELAST,TXT_ADDRBUSINESS,TXT_CITYBUSINESS,TXT_STATEBUSINESS,TXT_ZIPBUSINESS,TXT_COUNTRYBUSINESS");
            if (doRSContact.GetFirst() == 1)
            {
                scompany = Convert.ToString(doRSContact.GetFieldVal("LNK_RELATED_CO%%TXT_COMPANYNAME"));
                sFirstName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMEFIRST"));
                sLastName = Convert.ToString(doRSContact.GetFieldVal("TXT_NAMELAST"));


                if (!string.IsNullOrEmpty(sFirstName))
                {
                    sContactName = sFirstName + " ";
                }
                sContactName += sLastName;

                sAddrMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ADDRBUSINESS"));
                sCityMail = Convert.ToString(doRSContact.GetFieldVal("TXT_CITYBUSINESS"));
                sStateMail = Convert.ToString(doRSContact.GetFieldVal("TXT_STATEBUSINESS"));
                sZipMail = Convert.ToString(doRSContact.GetFieldVal("TXT_ZIPBUSINESS"));
                sCountryMail = Convert.ToString(doRSContact.GetFieldVal("TXT_COUNTRYBUSINESS"));

                //Start building the mailing address
                //sMailingAddr = scompany;
                //if (!string.IsNullOrEmpty(scompany))
                //{
                //    sMailingAddr = sMailingAddr + Environment.NewLine + scompany;
                //}
                //sMailingAddr = scompany;
                sMailingAddr = sContactName;
                if (!sAddrMail.Contains(scompany))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + scompany;
                }
                if (!string.IsNullOrEmpty(sAddrMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sAddrMail;
                }
                if (!string.IsNullOrEmpty(sCityMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sCityMail;
                }
                if (!string.IsNullOrEmpty(sStateMail))
                {
                    sMailingAddr = sMailingAddr + ", " + sStateMail;
                }
                if (!string.IsNullOrEmpty(sZipMail))
                {
                    sMailingAddr = sMailingAddr + " " + sZipMail;
                }
                if (!string.IsNullOrEmpty(sCountryMail))
                {
                    sMailingAddr = sMailingAddr + Environment.NewLine + sCountryMail;
                }
                doForm.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailingAddr);
                doForm.doRS.SetFieldVal("MMO_ADDRMAILING", sMailingAddr);
            }


            par_bRunNext = false;
            par_doCallingObject = doForm;
            return true;

        }
        public bool QT_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            clRowSet doRS = (clRowSet)par_doCallingObject;

            // Fill Ship to Company with To Company if no Ship To company linked
            // and override not checked
            if (doRS.GetLinkCount("LNK_SHIPTO_CO") == 0 & doRS.GetLinkCount("LNK_TO_CO") > 0 & Convert.ToInt32(doRS.GetFieldVal("CHK_OVERRIDE", 2)) == 0)
            {
                doRS.SetFieldVal("LNK_SHIPTO_CO", doRS.GetFieldVal("LNK_TO_CO", 2), 2);

                // Fill Involves Co with Ship To Company 
                doRS.SetFieldVal("LNK_INVOLVES_CO", doRS.GetFieldVal("LNK_SHIPTO_CO", 2), 2);
            }

            // TLD 10/27/2011 Fill Territory from C on Save
            //if (doRS.GetLinkCount("LNK_TO_CO") == 1)
            //{
            //    doRS.SetFieldVal("LNK_In_TE", doRS.GetFieldVal("LNK_To_CO%%LNK_In_TE", 2), 2);
            //}
            par_doCallingObject = doRS;
            return true;
        }

        public bool Quote_GenerateQuoteNo_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // MI 10/1/07 Changed now to goTR.NowUTC().
            // par_doCallingObject: Unused.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // PURPOSE:
            // Generate the old Selltis-style Quote number (10 digits plus user code)
            // On 10/1/07, MI changed 'now' to the UTC value, goTR.UTC_ServerToUTC(Now).
            // This may cause the duplication of some ID numbers in existing databases.
            // RETURNS:
            // Code number via return parameter.

            string s = "";
            DateTime dtBase;
            long lDiff;

            par_bRunNext = false;

            try
            {
                // DateSerial gives us a serial number of the starting point in time
                dtBase = Microsoft.VisualBasic.DateAndTime.DateSerial(1997, 1, 1);         // DO NOT change the year number after the DB is in use!
                lDiff = Microsoft.VisualBasic.DateAndTime.DateDiff("s", dtBase, goTR.NowUTC());      // difference between dtBase and Now
                s = goTR.Pad(lDiff.ToString(), 10, "0", "L"); // ' DVF  - Commented out per customer request not to use Emp Code & goP.GetUserCode()      'US.TXT_Code
            }
            catch (Exception ex)
            {
                if (!(ex.Message == clC.EX_THREAD_ABORT_MESSAGE))
                {
                    goErr.SetError(ex, 45105, sProc);
                    return false;
                }
            }

            par_oReturn = s;

            return true;
        }

        public bool Quotline_CalcTotal_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: doForm.
            // par_doArray: Unused.
            // par_s1: 
            // par_s2: 
            // par_s3: 
            // par_s4: 
            // par_s5: 
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // VS 04132018 TKT#2180 : Rounding decimal places to while calculating discounted price, otherwise this is causing a difference of penny or two.
            par_bRunNext = false;

            Form doForm = null;
            clRowSet doRS1 = null;
            // If gbWriteLog Then Dim oLog As Object = New clLogObj(sProc, "Start", 3)
            if (par_s1 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
            }

            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("Begin-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


            decimal cPriceUnit = default(decimal);
            double rQtyFld = 0;
            double rDiscPerc = 0;
            decimal cDiscAddAmt = default(decimal);
            decimal cCostVal = default(decimal);
            decimal cWork = default(decimal);
            decimal cSubtotal = default(decimal);
            double rSalesTaxPerc = 0;
            decimal cSKUCost = default(decimal);
            string sSKUCost = null;



            // PURPOSE:
            // Calc Subtotal if Include is checked, otherwise enter 0 as subtotal
            // Field 'Price Unit No Disc' = Unit Price before discount
            // Field 'Price Unit' = Unit Price after discount
            // RETURNS:
            // True.

            // goP.TraceLine("", "", sProc)

            // CS Need to check if coming from RecOnSave b/c in that case we are working with a rowset and 
            // otherwise we are on a form.
            if (par_s1 != "doRS")
            {
                if (Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_INCLUDE", 2)) != 1)
                {
                    // Set calculated fields to 0
                    doForm.doRS.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_COST", 0, 2);
                    doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                    doForm.doRS.SetFieldVal("CUR_SALESTAX", 0, 2);
                }
                else
                {
                    cPriceUnit = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_PRICEUNIT", 2));
                    rQtyFld = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY", 2));
                    rDiscPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__DISCPERCENT", 2));
                    cDiscAddAmt = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_DISCADDLAMT", 2));
                    // CS 5/21/09: Always calculate total cost based on linked model's cost * qty
                    // cCostVal = doForm.doRS.GetFieldVal("CUR_COST", 2)
                    sSKUCost = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST"));
                    if (sSKUCost != "")
                    {
                        cSKUCost = Convert.ToDecimal(goTR.StringToCurr(sSKUCost, "", ref par_iValid, ""));
                    }
                    else
                    {
                        cSKUCost = 0;
                    }
                    cCostVal = Convert.ToDecimal(cSKUCost) * Convert.ToDecimal(rQtyFld);
                    doForm.doRS.SetFieldVal("CUR_COST", cCostVal);



                    // Copy Cost from SKU and multiply it by Qty if the user edited Qty or if Qty is 0
                    // (we set it to 0 above if it is blank)
                    // bUpdCostVal is set to 1 in ControlOnLeave script for 'For Model'.

                    // Check if form variable has been set otherwise get an error comparing srQtyEnterVal as 
                    // a string ("") to a double (rQtyFld)
                    // Per MI, we can get rid of the form var dealing with changing the qty. We will always
                    // recalc regardless of it.
                    // If doForm.oVar.GetVar("srQtyEnterVal") <> "" Then
                    // If (rQtyFld <> doForm.oVar.GetVar("srQtyEnterVal")) Or (doForm.oVar.GetVar("bUpdCostVal")) = 1 Then
                    // If doForm.oVar.Getvar("bUpdCostVal") <> "" Then
                    // If (doForm.oVar.GetVar("bUpdCostVal")) = 1 Then
                    // CS: Moving this to QL_FormControlOnChange_USEMODELPRICE. When the user clicks this button the linked model's cost
                    // will be used.
                    // sSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")
                    // If sSKUCost <> "" Then 'CS added this If b/c if coming from QL_FormOnLoad the value
                    // 'is blank for cost and get error
                    // cSKUCost = doForm.doRS.GetFieldVal("LNK_FOR_MO%%CUR_COST")

                    // cCostVal = cSKUCost * rQtyFld
                    // doForm.doRS.SetFieldVal("CUR_COST", cCostVal)
                    // End If
                    // End If

                    // Calculate unit price after discount
                    cWork = cPriceUnit - (cPriceUnit * Convert.ToDecimal(rDiscPerc / 100));
                    doForm.doRS.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork));


                    // 'Calculate Subtotal
                    // cSubtotal = (cPriceUnit * rQtyFld) - (cPriceUnit * rQtyFld * rDiscPerc / 100) + cDiscAddAmt
                    // VS 04132018 TKT#2180 : Rounding decimal places to while calculating discounted price, otherwise this is causing a difference of penny or two.
                    cSubtotal = (cPriceUnit * Convert.ToDecimal(rQtyFld)) - (cPriceUnit * Convert.ToDecimal(rQtyFld) * Convert.ToDecimal(rDiscPerc / 100)) + cDiscAddAmt;
                    doForm.doRS.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cSubtotal));

                    // Calc Gross Profit
                    // CS 5/13/09 Remove qty b/c cur_cost already considers qty
                    // cWork = cSubtotal - (cCostVal * rQtyFld) 'CS 6/13/07: Added rQtyField per DF
                    cWork = cSubtotal - cCostVal;
                    doForm.doRS.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));

                    // Sales tax
                    if (Convert.ToBoolean(doForm.doRS.GetFieldVal("CHK_TAXABLE", 2)))
                    {
                        // CS 6/2/09: Get value from variable if set
                        if (string.IsNullOrEmpty(goP.GetVar("QuoteInfo").ToString()))
                        {
                            rSalesTaxPerc = Convert.ToDouble(doForm.doRS.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"));
                        }
                        else
                        {
                            rSalesTaxPerc = Convert.ToDouble(goTR.StrRead(goP.GetVar("QuoteInfo").ToString(), "QT_SALESTAXPERCENT", "", false));
                        }


                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", rSalesTaxPerc);
                        doForm.doRS.SetFieldVal("CUR_SALESTAX", cSubtotal * Convert.ToDecimal(rSalesTaxPerc / 100));
                    }
                    else
                    {
                        doForm.doRS.SetFieldVal("SR__SALESTAXPERCENT", 0);
                        doForm.doRS.SetFieldVal("CUR_SALESTAX", 0);
                    }
                }
            }
            else if (Convert.ToInt32(doRS1.GetFieldVal("CHK_INCLUDE", 2)) != 1)
            {
                // Set calculated fields to 0
                doRS1.SetFieldVal("CUR_SUBTOTAL", 0, 2);
                doRS1.SetFieldVal("CUR_GROSSPROFIT", 0, 2);
                doRS1.SetFieldVal("CUR_COST", 0, 2);
                doRS1.SetFieldVal("SR__SALESTAXPERCENT", 0, 2);
                doRS1.SetFieldVal("CUR_SALESTAX", 0, 2);
            }
            else
            {
                cPriceUnit = Convert.ToDecimal(doRS1.GetFieldVal("CUR_PRICEUNIT", 2));
                rQtyFld = Convert.ToDouble(doRS1.GetFieldVal("SR__QTY", 2));
                rDiscPerc = Convert.ToDouble(doRS1.GetFieldVal("SR__DISCPERCENT", 2));
                cDiscAddAmt = Convert.ToDecimal(doRS1.GetFieldVal("CUR_DISCADDLAMT", 2));
                // cCostVal = doRS1.GetFieldVal("CUR_COST", 2)
                // CS 5/21/09: Always calculate total cost based on linked model's cost * qty
                string sModel = goP.GetVar("QuoteLineInfo_" + doRS1.GetFieldVal("GID_ID").ToString()).ToString();
                if (sModel == "")
                {
                    sSKUCost = doRS1.GetFieldVal("LNK_FOR_MO%%CUR_COST").ToString();
                }
                else
                {
                    sSKUCost = goTR.StrRead(sModel, "MO_CUR_COST", "", false);
                }
                if (sSKUCost != "")
                {
                    int par_iValid = 4;
                    cSKUCost = Convert.ToDecimal(goTR.StringToCurr(sSKUCost, "", ref par_iValid));
                }
                else
                {
                    cSKUCost = 0;
                }
                cCostVal = Convert.ToDecimal(Convert.ToDecimal(cSKUCost) * Convert.ToDecimal(rQtyFld));
                doRS1.SetFieldVal("CUR_COST", cCostVal);

                // 'CS: only set the cost if it is 0
                // If cCostVal = 0 Then
                // cSKUCost = goTR.StringToCurr(doRS1.GetFieldVal("LNK_FOR_MO%%CUR_COST"))
                // cCostVal = cSKUCost * rQtyFld
                // doRS1.SetFieldVal("CUR_COST", goTR.RoundCurr(cCostVal))
                // End If

                // Calculate unit price after discount
                cWork = cPriceUnit - (Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rDiscPerc / 100));
                doRS1.SetFieldVal("CUR_PRICEUNITAFTERDISC", goTR.RoundCurr(cWork));

                // 'Calculate Subtotal
                // cSubtotal = (cPriceUnit * rQtyFld) - (cPriceUnit * rQtyFld * rDiscPerc / 100) + cDiscAddAmt
                // VS 04132018 TKT#2180 : Rounding decimal places to while calculating discounted price, otherwise this is causing a difference of penny or two.
                cSubtotal = (Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rQtyFld)) - (Convert.ToDecimal(cPriceUnit) * Convert.ToDecimal(rQtyFld * rDiscPerc / 100)) + cDiscAddAmt;
                doRS1.SetFieldVal("CUR_SUBTOTAL", goTR.RoundCurr(cSubtotal));

                // Calc Gross Profit
                cWork = cSubtotal - cCostVal;
                // CS 5/13/09 Remove qty b/c cur_cost already considers qty
                // cWork = cSubtotal - (cCostVal * rQtyFld) 'CS Added rQtyField 
                doRS1.SetFieldVal("CUR_GROSSPROFIT", goTR.RoundCurr(cWork));

                // Sales tax
                if (Convert.ToBoolean(doRS1.GetFieldVal("CHK_TAXABLE", 2)))
                {
                    // CS 6/2/09:
                    if (goP.GetVar("QuoteInfo") == "")
                    {
                        // CS 10/19/10: Check if have a linked QT. Should always, but causes error if not.
                        if (doRS1.GetLinkCount("LNK_IN_QT") > 0)
                        {
                            rSalesTaxPerc = Convert.ToDouble(doRS1.GetFieldVal("LNK_IN_QT%%SR__SALESTAXPERCENT"));
                        }
                        else
                        {
                            rSalesTaxPerc = 0;
                        }
                    }
                    else
                    {
                        rSalesTaxPerc = Convert.ToDouble(goTR.StrRead(goP.GetVar("QuoteInfo").ToString(), "QT_SALESTAXPERCENT", "", false));
                    }

                    doRS1.SetFieldVal("SR__SALESTAXPERCENT", rSalesTaxPerc);
                    doRS1.SetFieldVal("CUR_SALESTAX", Convert.ToDecimal(cSubtotal) * Convert.ToDecimal(rSalesTaxPerc / 100));
                }
                else
                {
                    doRS1.SetFieldVal("SR__SALESTAXPERCENT", 0);
                    doRS1.SetFieldVal("CUR_SALESTAX", 0);
                }
            }
            // CS Debug
            // CType(HttpContext.Current.Session("sb"), StringBuilder).AppendLine("End-" & sProc & ": " & CType(HttpContext.Current.Session("sw"), System.Diagnostics.Stopwatch).ElapsedMilliseconds)


            if (par_s1 == "doRS")
            {
                par_doCallingObject = doRS1;
            }
            else
            {
                par_doCallingObject = doForm;
            }

            return true;

        }
        public bool QT_FormControlOnChange_MLS_STATUS_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string PoNO = "";
            string color = goP.GetVar("sMandatoryFieldColor").ToString();

            int _status = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2));
            if (_status == 5)
            {
                doForm.SetFieldProperty("TXT_PONO", "LABELCOLOR", color);
            }
            else
            {
                doForm.SetFieldProperty("TXT_PONO", "LABELCOLOR", "#666666");
            }
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            Form doForm = (Form)par_doCallingObject;


            string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("Gid_id"));

            clRowSet rsQL = new clRowSet("QL", clC.SELL_READONLY, "LNK_IN_QT='" + sGidId + "' And LNK_FOR_MO%%BI__ID<1 ", "LNK_IN_QT", "LNK_FOR_MO");

            if (rsQL.GetFirst() == 1)
            {
                goErr.SetWarning(35000, sProc, "Please fill Model before saving the Quote  ");
                doForm.FieldInFocus = "LNK_FOR_MO";
                par_doCallingObject = doForm;
                return false;
            }
            clRowSet rsQL1 = new clRowSet("QL", clC.SELL_READONLY, "LNK_IN_QT='" + sGidId + "'", "LNK_IN_QT", "**");

            StringBuilder sb = new StringBuilder();
            StringBuilder sb1 = new StringBuilder();
            StringBuilder sb2 = new StringBuilder();
            StringBuilder sb3 = new StringBuilder();

            int rowCount = Convert.ToInt32(rsQL1.Count()); // Get the total number of rows

            for (int i = 0; i < rowCount; i++)
            {
                sb.Append(Convert.ToString(rsQL1.GetFieldVal("SR__QTY")) + "  " + Convert.ToString(rsQL1.GetFieldVal("TXT_MODEL")) + Environment.NewLine + "<br />");
                sb1.Append(Convert.ToString(rsQL1.GetFieldVal("TXT_MODEL")) + Environment.NewLine + "<br />");
                sb2.Append(Convert.ToString(rsQL1.GetFieldVal("MMO_NOTES")) + Environment.NewLine + "<br />");
                sb3.Append(Convert.ToString(rsQL1.GetFieldVal("LNK_RELATED_PP%%TXT_SERIALNO")) + Environment.NewLine + "<br />");
                rsQL1.GetNext();

            }

            doForm.doRS.SetFieldVal("MMO_QUOTELINEQTYANDMODEL", sb.ToString().Trim());
            doForm.doRS.SetFieldVal("MMO_QUOTELINEMODEL", sb1.ToString().Trim());
            doForm.doRS.SetFieldVal("MMO_QUOTELINENOTES", sb2.ToString().Trim());
            doForm.doRS.SetFieldVal("MMO_QUOTELINESERIALNO", sb3.ToString().Trim());

            return true;
        }
        public bool QT_FormOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string PoNO = "";

            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            int _status = Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2));


            if (_status == 5)
            {
                doForm.SetFieldProperty("TXT_PONO", "LABELCOLOR", color);
                string sPONo = Convert.ToString(doForm.doRS.GetFieldVal("TXT_PONO"));
                if (string.IsNullOrEmpty(sPONo))
                {
                    doForm.MoveToField("TXT_PONO");
                    goErr.SetWarning(35000, sProc, "Please enter the field Po#.");
                    par_doCallingObject = doForm;
                    return false;
                }
                PoNO = doForm.doRS.GetFieldVal("TXT_PONO").ToString();
                clRowSet _clrowset = new clRowSet("QT", 3, "TXT_PONO='" + goTR.PrepareForSQL(PoNO) + "' ", "", "GID_ID,TXT_QuoteNo", 1);
                //if (Convert.ToString(doForm.doRS.oVar.GetVar("PO#DuplicatedNumber")) != Convert.ToString(doForm.doRS.GetFieldVal("TXT_PONO")))
                //{
                    if (_clrowset.Count() > 0)
                    {
                        if (Convert.ToString(doForm.oVar.GetVar("CheckDuplicatePO#")) != "1")
                        {
                            doForm.MessageBox("The PO# already linked to Quote# ," + _clrowset.GetFieldVal("TXT_QuoteNo") + Environment.NewLine + "Are you sure to save this Quote with same PO#?", clC.SELL_MB_YESNO + clC.SELL_MB_DEFBUTTON1, "Selltis", "", "", "", "", "MessageBoxEvent", "MessageBoxEvent", "", null, null, "Yes", "No", "", "", "MERGEFAIL");
                            par_doCallingObject = doForm;
                            return false;
                        }
                        else
                        {
                            doForm.doRS.oVar.DeleteVar("CheckDuplicatePO#");

                        }
                    }
                }
                else
                {
                    doForm.SetFieldProperty("TXT_PONO", "LABELCOLOR", "#666666");
                }
            //}           

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            // TLD 8/29/2008 Prevent from running
            goTR.StrWrite(ref par_sSections, "FillRFQNo", "0"); // do not run FillRFQNo

            return true;
        }
        public bool CalcQuoteTotal_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool MessageBoxEvent_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // Every time doForm.MessageBox is called it should call this script. This script determines what will happen based on
            // the user's response.
            // Par_s5 will always be the name of the script that called doform.MessageBox
            // Par_s1 will be whatever button the user clicked.
            // Par_s2-Par_s4 can be whatever else you want to pass.
            // In the case of an input type messagebox, par_s2 will contain the text the user typed in the input box.

            // After this script is run and whatever code is called, goForm.Save is called if this started by clicking Save button.

            string sProc;
            sProc = "Script::MessageBoxEvent_Post";
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sJournal = "";
            string sWork = "";
            //DateTime dtNow = goTR.NowLocal();
            par_bRunNext = false;
            switch (Strings.UCase(par_s5))
            {

                case "MERGEFAIL":
                    {

                        switch (Strings.UCase(par_s1))
                        {
                            case "YES":

                                doForm = (Form)par_doCallingObject;
                                doForm.oVar.SetVar("CheckDuplicatePO#", "1");
                                doForm.Save(5);
                                break;
                            case "NO":

                                return false;

                        }

                        break;
                    }
                //case "QT_FORMONSAVE_QLSTATUS":
                //    switch (par_s1.ToUpper())
                //    {
                //        case "YES":
                //            //User clicked Yes to update Quote line status with Quote status
                //            goP.SetVar("UseQTStatus", "1");
                //            doForm.oVar.SetVar("QT_CheckStatus_Ran", "1");
                //            break;
                //        case "NO":
                //            //Uncheck checkbox that keeps QL status same as QT
                //            doForm.doRS.SetFieldVal("CHK_UPDQLSTATUS", 0, 2);
                //            goP.SetVar("USEQTStatus", "");
                //            doForm.oVar.SetVar("QT_CheckStatus_Ran", "1");
                //            break;
                //        case "CANCEL":
                //            goP.SetVar("USEQtStatus", "");
                //            doForm.oVar.SetVar("CancelSave", "1");
                //            break;
                //    }
                //    break;

            }
            par_bRunNext = false;
            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_PREVIEW_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate, true);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }


            Generate_Quote(doForm, sTemplateName, 0, 1, 0);

            par_doCallingObject = doForm;
            return true;
        }

        private string GetQuoteTemplate(string sQTTemplate, bool isDraft = false)
        {
            if (isDraft)
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "GreylineStandardQuote_draft.docx";
                }
                else if (sQTTemplate == "Change Order")
                {
                    return "cus_change_order_draft.docx";
                }
                else if (sQTTemplate == "Commercial Invoice")
                {
                    return "cus_commercialinvoice_Draft.docx";
                }
                else if (sQTTemplate == "Commercial Invoice and Packing Slip")
                {
                    return "cus_commercialinvoice_packinglist_Draft.docx";
                }
                else if (sQTTemplate == "Confirmation")
                {
                    return "cus_order_confirmation_Quote_Draft.docx";
                }
                else if (sQTTemplate == "Invoice")
                {
                    return "cus_invoice_only_Quote_Draft.docx";
                }
                else if (sQTTemplate == "Invoice & Packing Slip")
                {
                    return "cus_invoice_packing_Quote_Draft.docx";
                }
                else if (sQTTemplate == "Packing Slip")
                {
                    return "cus_packinglist_Draft.docx";
                }
                else if (sQTTemplate == "Quote - Word - No Cover Letter")
                {
                    return "cus_Quote Word - No Cover Letter_Draft.docx";
                }
                else if (sQTTemplate == "Quote Word - With Cover Letter")
                {
                    return "cus_Quote_with_cover_Draft.docx";
                }
                else if (sQTTemplate == "Rep Quote")
                {
                    return "cus_rep_quote_advice_Draft.docx";
                }
                else if (sQTTemplate == "Rush Delivery")
                {
                    return "cus_rush_delivery_request_Draft.docx";
                }
                else if (sQTTemplate == "Sales Order")
                {
                    return "cus_sales_order_Quote_Draft.docx";
                }
                else if (sQTTemplate == "Sales Requisition")
                {
                    return "cus_sales_requisition_quote_Draft.docx";
                }
                else if (sQTTemplate == "Service Requisition")
                {
                    return "cus_service_order_Quote_Draft.docx";
                }
                else if (sQTTemplate == "ZTest Sales Order")
                {
                    return "cus_sales_order_Quote_VSTest_Draft.docx";
                }

            }
            else
            {
                if (sQTTemplate == "Standard Quote")
                {
                    return "GreylineStandardQuote.docx";
                }
                else if (sQTTemplate == "Change Order")
                {
                    return "cus_change_order.docx";
                }
                else if (sQTTemplate == "Commercial Invoice")
                {
                    return "cus_commercialinvoice.docx";
                }
                else if (sQTTemplate == "Commercial Invoice and Packing Slip")
                {
                    return "cus_commercialinvoice_packinglist.docx";
                }
                else if (sQTTemplate == "Confirmation")
                {
                    return "cus_order_confirmation_Quote.docx";
                }
                else if (sQTTemplate == "Invoice")
                {
                    return "cus_invoice_only_Quote.docx";
                }
                else if (sQTTemplate == "Invoice & Packing Slip")
                {
                    return "cus_invoice_packing_Quote.docx";
                }
                else if (sQTTemplate == "Packing Slip")
                {
                    return "cus_packinglist.docx";
                }
                else if (sQTTemplate == "Quote - Word - No Cover Letter")
                {
                    return "cus_Quote Word - No Cover Letter.docx";
                }
                else if (sQTTemplate == "Quote Word - With Cover Letter")
                {
                    return "cus_Quote_with_cover.docx";
                }
                else if (sQTTemplate == "Rep Quote")
                {
                    return "cus_rep_quote_advice.docx";
                }
                else if (sQTTemplate == "Rush Delivery")
                {
                    return "cus_rush_delivery_request.docx";
                }
                else if (sQTTemplate == "Sales Order")
                {
                    return "cus_sales_order_Quote.docx";
                }
                else if (sQTTemplate == "Sales Requisition")
                {
                    return "cus_sales_requisition_quote.docx";
                }
                else if (sQTTemplate == "Service Requisition")
                {
                    return "cus_service_order_Quote.docx";

                }
                else if (sQTTemplate == "ZTest Sales Order")
                {
                    return "cus_sales_order_Quote_VSTest.docx";
                }

            }


            return "";
        }

        private void Generate_Quote(Form doForm, string sTemplateName, int iFlag = 0, int iPreview = 0, int iSend = 0)
        {
            WordDocumentHelper _doctopdf = new WordDocumentHelper();

            int iShowHtml = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%SHOW_HTML_IN_WORD"));
            int iHideZeroCurValues = Convert.ToInt32(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%DONT_SHOW_ZERO_CUR_VALUES"));

            string sfileextension = ".pdf";
            int idoctype = 1;
            string sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%SERVERSIDE_QT_DOCTYPE"));
            if (string.IsNullOrEmpty(sdocType))
            {
                sdocType = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTION%%SERVERSIDE_QT_DOCTYPE"));
            }
            if (string.IsNullOrEmpty(sdocType) || iPreview == 1)
            {
                sfileextension = ".pdf";
                idoctype = 1;
            }
            else
            {
                sfileextension = sdocType.ToLower();
                idoctype = sdocType.ToLower() == ".pdf" ? 1 : 2;
            }

            string templatePath = Util.GetTemplatesPath() + sTemplateName;
            string temppath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\SendTemp\";

            if (!Directory.Exists(temppath))
            {
                Directory.CreateDirectory(temppath);
            }

            string tempfileName = temppath + Guid.NewGuid().ToString() + ".docx";
            File.Copy(templatePath, tempfileName);

            //save to cloud and add the .pdf as an attachment to the quote form
            string sFileName = "";
            string sExistingAttachments = Convert.ToString(doForm.doRS.GetFieldVal("ADR_ATTACHMENTS", 2));

            if (string.IsNullOrEmpty(sExistingAttachments))
            {
                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + sfileextension;
            }
            else
            {
                string[] source = sExistingAttachments.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);

                var matchQuery = from word in source
                                 where word.ToLowerInvariant().Contains(sFileName.ToLowerInvariant())
                                 select word;

                int wordCount = matchQuery != null ? matchQuery.Count() : 0;

                wordCount = wordCount + 1;

                //sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + ".pdf";
                sFileName = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + "_v" + wordCount.ToString() + sfileextension;
            }

            Stream _stream = _doctopdf.ProcessDocument(tempfileName, doForm.doRS, iFlag, iShowHtml, iHideZeroCurValues, idoctype, sFileName);

            //bool _status = SaveToCloud(doForm, sFileName, _stream);
            bool _status = Util.SaveToCloud(doForm, sFileName, _stream, "QT", "ADR_ATTACHMENTS");

            if (File.Exists(tempfileName))
            {
                File.Delete(tempfileName);
            }

            if (_status)
            {
                string sGID = doForm.GetRecordID();

                if (iPreview == 0)
                {
                    sExistingAttachments = sExistingAttachments + "|" + sFileName;

                    doForm.doRS.SetFieldVal("ADR_ATTACHMENTS", sExistingAttachments);

                    string _soldHistory = doForm.doRS.GetFieldVal("MMO_HISTORY").ToString();
                    string par_sDelim = " ";
                    string sPrint_Sent = "Printed";
                    string _sresult = Microsoft.VisualBasic.Strings.Left(goTR.DateTimeToSysString(DateTime.UtcNow, ref par_iValid, ref par_sDelim), 16) + " GMT " + goP.GetMe("CODE") + " " + sPrint_Sent;
                    doForm.doRS.SetFieldVal("MMO_HISTORY", _sresult + "<br>" + doForm.doRS.GetFieldVal("MMO_HISTORY").ToString());


                    //string sGID = doForm.GetRecordID();

                    if (doForm.Save(3) != 1)
                    {
                        goLog.SetErrorMsg("Save failed for QT PDF Generation " + sGID);
                        //return false;
                    }
                    else
                    {
                        //save the attachment record into database
                        clAttachments _clattachment = new clAttachments();
                        string sFileFullpath = "QT/" + sGID + "/ADR_ATTACHMENTS/" + sFileName;
                        _clattachment.SaveAttachment("QT", sGID, sfileextension, "10", sFileName, sFileFullpath, "ADR_ATTACHMENTS", "Selltis");
                    }
                    if (iSend == 1)
                    {
                        //Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                        //get coverletter,to,subject and attach it to session
                        string sFrom = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sCc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL")); //+";"+ Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL"));
                        string sCoverletter = Convert.ToString(doForm.doRS.GetFieldVal("MTA_GLOBAL%%WOP_WORKGROUP_OPTIONS%%QUOTE_COVERLETTER"));
                        sCoverletter = sCoverletter + "<br/><br/>For further communication, please reply back to " + Convert.ToString(doForm.doRS.GetFieldVal("LNK_PEER_US%%EML_EMAIL")) + "," + Convert.ToString(doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US%%EML_EMAIL"));
                        string sTo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));
                        string sSubject = Convert.ToString(doForm.doRS.GetFieldVal("TXT_QUOTENO")) + ", " + Convert.ToString(doForm.doRS.GetFieldVal("TXT_DESCRIPTION"));
                        Util.SetSessionValue("SendFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID + "|" + sTo + "|" + sSubject + "|" + sCoverletter + "|" + sFrom + "|" + sCc);
                        
                    }
                    else
                    {
                        Util.SetSessionValue("DownloadFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                    }
                }
                else
                {
                    Util.SetSessionValue("PreviewFileData", sFileName + "|QT|ADR_ATTACHMENTS|" + sGID);
                }

            }
        }

        public bool QT_FormControlOnChange_BTN_PRINTSEND_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";
            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0, 1);

            par_doCallingObject = doForm;
            return true;
        }

        public bool QT_FormControlOnChange_BTN_Print_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            string sTemplateName = "";

            string sQTTemplate = Convert.ToString(doForm.doRS.GetFieldVal("MLS_QTTEMPLATE"));
            if (string.IsNullOrEmpty(sQTTemplate))
            {
                doForm.MessageBox("Please select quote template under the 'Preview' Tab.");
                doForm.FieldInFocus = "MLS_QTTEMPLATE";
                par_doCallingObject = doForm;
                return false;
            }
            else
            {
                sTemplateName = GetQuoteTemplate(sQTTemplate);
            }


            if (string.IsNullOrEmpty(sTemplateName))
            {
                doForm.MessageBox("The quote template is not available. Please contact selltis administrator.");
                par_doCallingObject = doForm;
                return false;
            }

            Generate_Quote(doForm, sTemplateName, 0, 0);

            par_doCallingObject = doForm;
            return true;
        }
        public bool QT_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("QT", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FORLINE_MO"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Model");
                doForm.FieldInFocus = "LNK_FORLINE_MO";
                par_doCallingObject = doForm;
                return false;
            }


            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEPRICEUNIT", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEQTY"));
            double dDiscper = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__LINEDISCPERCENT"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));
            string sUnit = Convert.ToString(doForm.doRS.GetFieldVal("TXT_LINEUNIT"));
            int iReport = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEREPORT", 2));
            int iinclude = Convert.ToInt32(doForm.doRS.GetFieldVal("CHK_LINEINCLUDE", 2));
            string sModelText = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_DESCRIPTION"));
            //string sModelDesc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_DESCRIPTION"));
            double sku = Convert.ToDouble(doForm.doRS.GetFieldVal("LI__LINESKU"));

            //string SerialNo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%LNK_CONNECTED_PP%%TXT_SERIALNO"));
            //string SensorModel = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%LNK_CONNECTED_PP%%TXT_SENSORMODEL"));
            //string SensorNo = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%LNK_CONNECTED_PP%%TXT_SENSORSERIALNO"));

            //string sProdNo = null;
            //SerialNo = SerialNo.Replace("\r\n", ", ");
            //SerialNo = SerialNo.TrimStart(',');
            //SerialNo = SerialNo.TrimEnd(',');

            //SensorModel = SensorModel.TrimStart('\r', '\n');
            //SensorNo = SensorNo.TrimStart('\r', '\n');


            //if (SensorModel.Contains("\r\n") & SensorNo.Contains("\r\n"))
            //{
            //    string[] models = SensorModel.Split(new[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries);
            //    string[] serilnos = SensorNo.Split(new[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries);

            //    // Combine the corresponding elements from the two arrays with a space in between
            //    sProdNo = string.Join(",", models.Select((n, index) => $"{n} {serilnos[index]}"));
            //}

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEPRICEUNIT";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__LINEQTY";
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_QL");
            iLineCount = iLineCount + 1;

            clRowSet rsQL = new clRowSet("QL", clC.SELL_ADD, "", "", "LNK_TO_CO,TXT_SERIALNO,TXT_Productno,TXT_Model,LNK_IN_QT,LNK_INVOLVES_US,LNK_FOR_MO,SR__QTY,TXT_UNIT,CUR_PRICEUNIT,SR__DISCPERCENT,CHK_REPORT,CHK_INCLUDE,SR__LINENO,CUR_COST,CUR_SUBTOTAL,CUR_GROSSPROFIT,CUR_PRICEUNITAFTERDISC,LI__SKU", -1, "", "", "", "", "", true);

            rsQL.SetFieldVal("LNK_In_QT", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsQL.SetFieldVal("LNK_TO_CO", doForm.doRS.GetFieldVal("LNK_TO_CO%%GID_ID"));
            rsQL.SetFieldVal("LNK_FOR_MO", MO_Gid);

            rsQL.SetFieldVal("LNK_ORIGINATEDBY_CN", doForm.doRS.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            rsQL.SetFieldVal("LNK_CREDITEDTO_US", doForm.doRS.GetFieldVal("LNK_CREDITEDTO_US"));
            rsQL.SetFieldVal("LNK_PEER_US", doForm.doRS.GetFieldVal("LNK_PEER_US"));
            rsQL.SetFieldVal("DTE_EXPCLOSEDATE", doForm.doRS.GetFieldVal("DTE_EXPCLOSEDATE"));
            rsQL.SetFieldVal("LNK_INVOLVES_US", doForm.doRS.GetFieldVal("LNK_INVOLVES_US"));
            rsQL.SetFieldVal("TXT_UNIT", sUnit);
            rsQL.SetFieldVal("LI__SKU", sku);
            //rsQL.SetFieldVal("TXT_MODEL", skufieldname);
            rsQL.SetFieldVal("CUR_PRICEUNIT", curUnitPrice);
            //rsQL.SetFieldVal("TXT_SERIALNO", SerialNo);
            //SensorModel = SensorModel + " " + SensorNo;
            //rsQL.SetFieldVal("TXT_Productno", sProdNo);

            rsQL.SetFieldVal("TXT_MODEL", sModelText);
            //rsQL.SetFieldVal("MMO_DETAILS", sModelDesc);

            rsQL.SetFieldVal("SR__Qty", dQty);
            rsQL.SetFieldVal("SR__DISCPERCENT", dDiscper);

            rsQL.SetFieldVal("CHK_Include", iinclude, 2);
            rsQL.SetFieldVal("CHK_REPORT", iReport, 2);

            rsQL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsQL.Commit() != 1)
            {
                return false;
            }

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            ClearLineFields(doForm);

            doForm.FieldInFocus = "LNK_FOR_MO";

            par_doCallingObject = doForm;
            return true;

        }
        private void ClearLineFields(Form doForm)
        {
            if (doForm.TableName.ToUpper() == "OP")
            {
                doForm.doRS.ClearLinkAll("LNK_FOR_PD");
                doForm.doRS.SetFieldVal("CUR_LineUnitPrice", 0);
                doForm.doRS.SetFieldVal("SR__QTY", 0);
                doForm.doRS.SetFieldVal("LNK_FORLINE_MO", 0);
            }
            else if (doForm.TableName.ToUpper() == "QT")
            {
                doForm.doRS.ClearLinkAll("LNK_FORLINE_MO");
                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", 0);
                doForm.doRS.SetFieldVal("SR__LINEQTY", 0);
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", "");
                doForm.doRS.SetFieldVal("SR__LINEDISCPERCENT", 0);
                doForm.doRS.SetFieldVal("CHK_LINEINCLUDE", 1, 2);
                doForm.doRS.SetFieldVal("CHK_LineReport", 1, 2);
                doForm.doRS.SetFieldVal("LI__LINESKU", 0);
                doForm.doRS.SetFieldVal("TXT_LINEMODEL", "");
            }

        }
        public bool OP_FormControlOnChange_BTN_ConvertToQT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.Commit() == 1)
            {
                string sGidId = Convert.ToString(doForm.doRS.GetFieldVal("GID_ID"));

                //clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY, "LNK_CONNECTED_OP = '" + sGidId + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1  OR CUR_COST<=0)", "");

                //if (rsOLs.GetFirst() == 1)
                //{
                //    doForm.MessageBox("Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
                //    doForm.FieldInFocus = "LNK_RELATED_VE";
                //    par_doCallingObject = doForm;
                //    return false;
                //}

                return Convert_OP_To_QT_Pre(ref par_doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections, null, Convert.ToString(doForm.doRS.GetFieldVal("GID_ID")));
            }

            return false;
        }

        public bool Convert_OP_To_QT_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string Gid_OP = par_s1;

            clRowSet rsOP = new clRowSet("OP", clC.SELL_EDIT, "GID_ID='" + Gid_OP + "'", "", "**");
            //int Status = Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS"));
            if (Convert.ToInt32(rsOP.GetFieldVal("MLS_STATUS", 2)) != 0)
            {
                Desktop _desktop = (Desktop)par_doCallingObject;
                _desktop.MessageBox(ref par_doCallingObject, "This Opportunity has already converted to Quote.");
                par_doCallingObject = _desktop;
                return false;
            }



            //if (par_doCallingObject == null || (par_doCallingObject != null
            //    && par_doCallingObject.GetType().Name.ToLower().ToString() == "desktopmodel"))
            //{
            //    //came from OP details page
            //    clRowSet rsOLs = new clRowSet("OL", clC.SELL_READONLY,
            //        "LNK_CONNECTED_OP = '" + Gid_OP + "' AND (LNK_RELATED_PG%%BI__ID<1 OR LNK_RELATED_PD%%BI__ID<1 OR CUR_COST<=0)", "");

            //    if (rsOLs.GetFirst() == 1)
            //    {
            //        if (par_doCallingObject != null)
            //        {
            //            Desktop _desktop = (Desktop)par_doCallingObject;
            //            _desktop.MessageBox(ref par_doCallingObject, "Please fill 'Product Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //            par_doCallingObject = _desktop;
            //        }
            //        //else
            //        //{                        
            //        //    Desktop _desktop = new Desktop("Global", "");//"DSK_3678E041-D280-4B2A-A253-6C8EFDABE845"
            //        //    _desktop.MessageBox(ref par_doCallingObject, "Please fill 'PCAT Group','PCAT' & 'Cost' of all the lines before converting the Opportunity to Quote.");
            //        //    par_doCallingObject = _desktop;
            //        //}
            //        return false;
            //    }
            //}

            //string sOPNo = Convert.ToString(rsOP.GetFieldVal("Txt_OPPNo"));

            //string sNewQTNo = sOPNo.Substring(0, sOPNo.Length - 1);
            //sNewQTNo = sNewQTNo + "Q";

            Form doFormQT = new Form("QT", Gid_OP, "CRU_QT");

            //doFormQT.doRS.SetFieldVal("TXT_QuoteNo", sNewQTNo);

            doFormQT.doRS.SetFieldVal("LNK_RELATED_OP", rsOP.GetFieldVal("GID_ID"));

            doFormQT.doRS.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
            doFormQT.doRS.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_ENDUSER_CO", rsOP.GetFieldVal("LNK_ENGINEERING_CO"));
            //doFormQT.doRS.SetFieldVal("LNK_DISTREP_CO", rsOP.GetFieldVal("LNK_DISTRIBUTOR_CO"));
            doFormQT.doRS.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
            doFormQT.doRS.SetFieldVal("EML_EMAIL", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%EML_EMAIL"));//primary contatc email

            string sMailAdd = String.Concat(Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_ADDRMAILING"))
                , "\r\n", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGCITY"))
                , ", ", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGSTATE"))
                , "-", Convert.ToString(rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN%%TXT_MAILINGZIP")));
            doFormQT.doRS.SetFieldVal("TXT_ADDRESSMAILING", sMailAdd);//primary contact address 

            doFormQT.doRS.SetFieldVal("DTT_EXPCLOSEDATE", rsOP.GetFieldVal("DTT_EXPCLOSEDATE"));
            doFormQT.doRS.SetFieldVal("DTE_NEXTACTIONDATE", rsOP.GetFieldVal("DTE_NEXTACTIONDATE"));
            doFormQT.doRS.SetFieldVal("DTE_DATECOMPLETED", rsOP.GetFieldVal("DTE_DATECLOSED"));
            //doFormQT.doRS.SetFieldVal("TXT_LinkedOppNo", sOPNo);
            doFormQT.doRS.SetFieldVal("TXT_DESCRIPTION", Convert.ToString(rsOP.GetFieldVal("TXT_DESCRIPTION")));
            doFormQT.doRS.SetFieldVal("MLS_STATUS", 0, 2);//open
            doFormQT.doRS.SetFieldVal("LNK_Peer_US", doFormQT.doRS.GetFieldVal("MTA_MEID%%POP_PERSONAL_OPTIONS%%QUOTE_PEER_USER"));
            doFormQT.doRS.SetFieldVal("LNK_RELATED_TR", rsOP.GetFieldVal("LNK_FOR_CO%%LNK_HAS_TR"));

            doFormQT.doRS.SetFieldVal("CHK_COMMIT", 0, 2);

            doFormQT.doRS.SetFieldVal("MMO_NEXTACTION", rsOP.GetFieldVal("MMO_NEXTACTION"));
            doFormQT.doRS.SetFieldVal("MMO_JOURNAL", rsOP.GetFieldVal("MMO_JOURNAL"));
            //doFormQT.doRS.SetFieldVal("CUR_TotalAmount", rsOP.GetFieldVal("CUR_OPPLINEVALUE", 2), 2);


            doFormQT.doRS.bBypassValidation = true;

            par_oReturn = doFormQT.doRS.GetFieldVal("GID_ID");

            if (doFormQT.doRS.Commit() == 1)
            {
                //update the quote No# as revision in the connected OP
                rsOP.SetFieldVal("TXT_REVISION", doFormQT.doRS.GetFieldVal("TXT_QUOTENO"));
                rsOP.SetFieldVal("MLS_SALESPROCESSSTAGE", 6, 2);//Present / Propose
                rsOP.SetFieldVal("MLS_STATUS", 6, 2);//Converted To Quote
                rsOP.Commit();

                clRowSet rsOL = new clRowSet("OL", clC.SELL_EDIT, "LNK_IN_OP='" + Gid_OP + "'", "", "*");
                for (int i = 1; i <= rsOL.Count(); i++)
                {
                    clRowSet doNewQL = new clRowSet("QL" +
                        "", clC.SELL_ADD, "", "", "", -1, "", "", "CRL_QL", doFormQT.doRS.GetFieldVal("GID_ID").ToString(), "", true);


                    doNewQL.SetFieldVal("LNK_FOR_MO", rsOL.GetFieldVal("LNK_FOR_MO", 2), 2);
                    doNewQL.SetFieldVal("LNK_TO_CO", rsOP.GetFieldVal("LNK_FOR_CO"));
                    doNewQL.SetFieldVal("LNK_ORIGINATEDBY_CN", rsOP.GetFieldVal("LNK_ORIGINATEDBY_CN"));
                    doNewQL.SetFieldVal("LNK_CREDITEDTO_US", rsOP.GetFieldVal("LNK_CREDITEDTO_US"));
                    doNewQL.SetFieldVal("LNK_INVOLVES_US", rsOP.GetFieldVal("LNK_INVOLVES_US"));

                    if (!rsOL.IsLinkEmpty("LNK_RELATED_PD"))
                    {
                        doNewQL.SetFieldVal("MMO_DETAILS", rsOL.GetFieldVal("LNK_RELATED_PD%%MMO_SPECIFICATIONS"));
                    }

                    if (doNewQL.IsLinkEmpty("LNK_PEER_US"))
                    {
                        doNewQL.SetFieldVal("LNK_PEER_US", goP.GetMe("ID"));
                    }

                    doNewQL.SetFieldVal("SR__LineNo", rsOL.GetFieldVal("SR__LineNo", 2), 2);
                    doNewQL.SetFieldVal("SR__Qty", rsOL.GetFieldVal("SR__Qty", 2), 2);

                    doNewQL.SetFieldVal("Cur_UnitPrice", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CUR_PriceUnit", rsOL.GetFieldVal("Cur_UnitPrice", 2), 2);
                    doNewQL.SetFieldVal("CHK_Include", "1", 2);

                    if (doNewQL.Commit() != 1)
                    {
                        //MI 3/31/09 added 35000 and sproc, was coded with string in first parameter
                        goErr.SetError(35000, "Convert Op To QT", "Error committing an add rowset for the new Quote Line.");
                        return false;
                    }
                    if (rsOL.GetNext() != 1)
                        break; // Exit For
                }
            }

            if (doFormQT.doRS.Commit() == 1)
            {
                doFormQT.doRS.UpdateLinkState("LNK_CONNECTED_QL");
                doFormQT.RefreshLinkNames("LNK_CONNECTED_QL");
            }

            goUI.Queue("FORM", doFormQT);

            return true;

        }
        public bool OP_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string color = goP.GetVar("sMandatoryFieldColor").ToString();
            doForm.SetFieldProperty("CUR_LINEUNITPRICE", "LABELCOLOR", color);

            if (doForm.GetMode() == "CREATION")
            {
                doForm.SetControlState("BTN_CONVERTTOQT", 2);
            }
            else
            {

                if (Convert.ToInt32(doForm.doRS.GetFieldVal("MLS_STATUS", 2)) == 0)
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 0);
                }
                else
                {
                    doForm.SetControlState("BTN_CONVERTTOQT", 2);
                }

            }

            par_doCallingObject = doForm;
            return true;
        }
        public bool OP_FormControlOnChange_NDB_BTN_ADDLINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (doForm.doRS.ValidateRecord() == false)
            {
                if (goErr.GetLastError("NUMBER") == "E47260")
                {
                    string sField = goTR.ExtractString(goErr.GetLastError("PARAMS"), 1);
                    if (!string.IsNullOrEmpty(sField))
                    {
                        doForm.MoveToField(sField);
                        goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", sField), "", "", "", "", "", "", "", "", sField);
                    }
                }
                else
                {
                    goErr.SetWarning(35000, sProc, "Please fill all the required fields.");
                }
                par_doCallingObject = doForm;
                return false;
            }

            if (doForm.doRS.IsLinkEmpty("LNK_FOR_PD"))
            {
                goErr.SetWarning(35000, sProc, "Please select a Product");
                doForm.FieldInFocus = "LNK_FOR_PD";
                par_doCallingObject = doForm;
                return false;
            }


            //clRowSet doRowset1 = new clRowSet("QL", clC.SELL_COUNT, "LNK_In_QT='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "' ", "Gid_ID", "BI__COUNT");
            //if (doRowset1.GetFirst() == 1)
            //{
            //    double dMaxLineno = Convert.ToDouble(doRowset1.GetFieldVal("BI__COUNT"));
            //    dNextLineno = dMaxLineno + 1;
            //}
            //else
            //{
            //    dNextLineno = 1.0;
            //}
            //doRowset1 = null;

            double curUnitPrice = Convert.ToDouble(doForm.doRS.GetFieldVal("CUR_LINEUNITPRICE", 2));
            double dQty = Convert.ToDouble(doForm.doRS.GetFieldVal("SR__QTY"));
            double dProb = Convert.ToDouble(doForm.doRS.GetFieldVal("SI__PROBABILITY"));
            string PD_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FOR_PD%%GID_ID"));
            string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));

            if (curUnitPrice <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Unit Price");
                doForm.FieldInFocus = "CUR_LINEUNITPRICE";
                par_doCallingObject = doForm;
                return false;
            }

            if (dQty <= 0)
            {
                goErr.SetWarning(35000, sProc, "Please enter valid Quantity");
                doForm.FieldInFocus = "SR__QTY";
                par_doCallingObject = doForm;
                return false;
            }


            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            //doForm.doRS.bBypassValidation = true;

            //get next line no
            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            long iLineCount = doForm.doRS.GetLinkCount("LNK_CONNECTED_OL");
            iLineCount = iLineCount + 1;

            clRowSet rsOL = new clRowSet("OL", clC.SELL_ADD, "", "", "LNK_IN_OP,LNK_FOR_MO,LNK_RELATED_PD,SR__QTY,CUR_UnitPrice,SR__LINENO,CUR_VALUE,CUR_VALUEINDEX", -1, "", "", "", "", "", true);

            rsOL.SetFieldVal("LNK_IN_OP", doForm.doRS.GetFieldVal("Gid_ID").ToString());
            rsOL.SetFieldVal("LNK_RELATED_PD", PD_Gid);
            rsOL.SetFieldVal("LNK_FOR_MO", MO_Gid);
            rsOL.SetFieldVal("CUR_UnitPrice", curUnitPrice);
            rsOL.SetFieldVal("SR__Qty", dQty);
            rsOL.SetFieldVal("SR__PROB", dProb);
            rsOL.SetFieldVal("SR__LineNo", iLineCount);

            if (rsOL.Commit() != 1)
            {
                return false;
            }


            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            ClearLineFields(doForm);

            //calculate the line total rollups at header level
            clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
            if ((rsOL1.GetFirst() == 1))
            {
                double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                double curTotalUnitPrice = Convert.ToDouble(rsOL1.GetFieldVal("CUR_UnitPrice|SUM", 2));

                doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);
                doForm.doRS.SetFieldVal("CUR_UnitValue", curTotalUnitPrice);
            }

            // doForm.doRS.bBypassValidation = false;

            if (doForm.doRS.Commit() != 1)
            {
                par_doCallingObject = doForm;
                return false;
            }

            doForm.FieldInFocus = "LNK_RELATED_PD";

            par_doCallingObject = doForm;
            return true;

        }

        public bool OL_RecordOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);


            clRowSet doRS = (clRowSet)par_doCallingObject;
            double curUnitPrice = Convert.ToDouble(doRS.GetFieldVal("Cur_UnitPrice", 2));
            double iQty = Convert.ToDouble(doRS.GetFieldVal("SR__Qty"));
            double rprob = Convert.ToDouble(doRS.GetFieldVal("SR__PROB"));


            double cur_Value = curUnitPrice * iQty;
            double cur_ValueIndex = cur_Value * rprob / 100;

            doRS.SetFieldVal("CUR_Value", cur_Value);
            doRS.SetFieldVal("cur_ValueIndex", cur_ValueIndex);

            //This will generate line no# when add lines from mobile app
            //double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            //if (rLineNo <= 0)
            //{
            //    long iLineCount = doRS.GetLinkCount("LNK_CONNECTED_OP%%LNK_RELATED_OL");
            //    iLineCount = iLineCount + 1;
            //    doRS.SetFieldVal("SR__LINENO", iLineCount);
            //}
            //mobile Ol line number
            double rLineNo = Convert.ToDouble(doRS.GetFieldVal("SR__LINENO"));
            goLog.Log("OL_RecordOnSave ", " Line count " + rLineNo.ToString(), 1, false, true);
            if (rLineNo <= 0)
            {
                clRowSet doOPLines = default(clRowSet);
                string sID = Convert.ToString(doRS.GetFieldVal("LNK_IN_OP%%GID_ID"));
                doOPLines = new clRowSet("OL", clC.SELL_READONLY, "LNK_IN_OP = '" + sID + "'", "SR__LineNo ASC", "SR__LineNo", -1, "", "", "", "", "", true, true);

                if (doOPLines.GetFirst() == 1)
                {
                    long iLineCount = doOPLines.Count();
                    iLineCount = iLineCount + 1;
                    doRS.SetFieldVal("SR__LINENO", iLineCount);
                    iLineCount = doOPLines.Count();
                    goLog.Log("OL_RecordOnSave ", "new LineNO# " + iLineCount, 1, false, true);
                    doOPLines = null;
                }
                else
                {
                    doRS.SetFieldVal("SR__LINENO", 1);
                    goLog.Log("OL_RecordOnSave ", "new LineNO# 1 ", 1, false, true);
                }
            }
            par_doCallingObject = doRS;

            return true;
        }

        public bool Opp_EnforceValue_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Unused.
            //par_doArray: Unused.
            //par_s1: 
            //par_s2: 
            //par_s3: 
            //par_s4: 
            //par_s5: 
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            ////goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            par_bRunNext = false;
            //Form doForm = (Form)par_doCallingObject;

            ////REVIEW:
            ////This is a separate proc since it is called multiple times in OnSave Proc

            //decimal cValueFld = default(decimal);

            //// Make sure the Value field has been filled out
            //cValueFld = Convert.ToDecimal(doForm.doRS.GetFieldVal("CUR_UNITVALUE", 2).ToString());
            ////Get system value

            //// First checking whether the field value is numeric, then checking for
            //// two conditions, 0 if numeric, empty if not numeric
            ////No need, but check if blank with friendly value
            //if (goTR.IsNumeric(cValueFld) == true)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //cValueFld = ""
            //        doForm.doRS.SetFieldVal("CUR_UNITVALUE", cValueFld, 2);
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}
            //if (goTR.IsNumeric(cValueFld) == false)
            //{
            //    if (cValueFld == 0)
            //    {
            //        doForm.MoveToField("CUR_UNITVALUE");
            //        //goErr.SetWarning(30029, sProc, "", goData.GetFieldLabel("OP", "CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE")
            //        goErr.SetWarning(30029, sProc, "", doForm.GetFieldLabel("CUR_UNITVALUE"), "", "", "", "", "", "", "", "", "CUR_UNITVALUE");
            //        return false;
            //    }
            //}

            //par_doCallingObject = doForm;
            return true;

        }
        public bool Opp_CalcProbability_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            // par_doCallingObject: Form object calling this script. Do not delete in script!
            // par_doArray: Unused.
            // par_s1: Unused.
            // par_s2 to par_s5: Unused.
            // par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            // par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            // par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            return true;
        }
        public bool Opp_CalcProbability_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_s1: Unused.
            //par_s2 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = null;
            clRowSet doRS1 = null;


            if (par_s2 == "doRS")
            {
                doRS1 = (clRowSet)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doRS1.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doRS1.SetFieldVal("CUR_VALUE", curValue);
                    doRS1.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doRS1;
            }
            else
            {
                doForm = (Form)par_doCallingObject;
                clRowSet rsOL1 = new clRowSet("OL", clC.SELL_GROUPBY, "LNK_IN_OP='" + doForm.doRS.GetFieldVal("Gid_ID").ToString() + "'", "TXT_OpportunityLineName", "CUR_Value|SUM,CUR_ValueIndex|SUM,CUR_UnitPrice|SUM");
                if ((rsOL1.GetFirst() == 1))
                {
                    double curValue = Convert.ToDouble(rsOL1.GetFieldVal("CUR_Value|SUM", 2));
                    double curValueIndex = Convert.ToDouble(rsOL1.GetFieldVal("CUR_ValueIndex|SUM", 2));
                    doForm.doRS.SetFieldVal("CUR_VALUE", curValue);
                    doForm.doRS.SetFieldVal("CUR_VALUEIndex", curValueIndex);

                }
                par_doCallingObject = doForm;
            }

            return true;

        }
        public bool OP_FormControlOnChange_BTN_CALCPROBABILITY_1_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //Refresh_QouteTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_OL");
            doForm.RefreshLinkNames("LNK_CONNECTED_OL");

            par_doCallingObject = doForm;
            return true;

        }
        public bool QT_FormControlOnChange_BTN_RECALC_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            //Refresh_QouteTotal(doForm.doRS);

            doForm.doRS.UpdateLinkState("LNK_CONNECTED_QL");
            doForm.RefreshLinkNames("LNK_CONNECTED_QL");

            par_doCallingObject = doForm;
            return true;

        }
        public bool QT_FormControlOnChange_BTN_DUPLICATELINE_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_sFormAlias = "", string par_sFieldName = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            //par_doCallingObject: Form object calling this script. Do not delete in script!
            //par_doArray: Unused.
            //par_sFormAlias: Alias name of the form, e.g. 'WFRMACWA_433'.
            //par_sFieldName: Name of the control, e.g. 'MMO_Notes'.
            //par_s3 to par_s5: Unused.
            //par_oReturn: Object passed to script ByRef.  Used to return values to calling process
            //par_bRunNext: only observed in clScriptsCustom '_Pre' scripts: set this to False to prevent the main and _Post script from running.
            //par_sSections: Ini-format list of code sections to exclude (SectionName=0) or include (SectionName=1). See _RunScriptManager for more info.

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            //goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sID = null;
            clRowSet doRowset = default(clRowSet);
            clRowSet doQuoteLines = default(clRowSet);
            double lHighestLine = 0;
            double lLine = 0;
            string sWork = null;
            string sNewID = null;

            //Check if have permissions to edit this QT
            if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
            {
                doForm.MessageBox("You do not have permission to edit this Quote so you cannot duplicate a Quote Line.");
                return true;
            }


            //Get doRowset of currently selected Quote Line record
            sID = doForm.GetLinkSelection("LNK_Connected_QL");
            if (string.IsNullOrEmpty(sID) | sID == null)
            {
                //goUI.NewWorkareaMessage("Please select a Quote Line to duplicate.")
                doForm.MessageBox("Please select a Quote Line to duplicate.");
                return true;
            }
            doRowset = new clRowSet("QL", 1, "GID_ID='" + sID + "'", "", "**", 1);
            if (doRowset.Count() < 1)
            {
                //goUI.NewWorkareaMessage("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.")
                doForm.MessageBox("The selected Quote Line can't be found in the database. It may have been deleted by another user. Select a different record and start again.");
                return true;
            }
            else
            {
                //Check if have add perm on QL
                if (goData.GetAddPermission("QL") == false)
                {
                    doForm.MessageBox("You do not have permission to add Quote Lines.");
                    return true;
                }
                //Check if have QT edit perm
                if (goData.GetRecordPermission(doForm.doRS.GetFieldVal("GID_ID").ToString(), "E") == false)
                {
                    doForm.MessageBox("You do not have permission to edit this Quote.");
                    return true;
                }
                clRowSet doNewQL = new clRowSet("QL", 2, "", "", "", -1, "", "", "", "", "", doForm.doRS.bBypassValidation);
                if (goData.CopyRecord(ref doRowset, ref doNewQL) == true)
                {
                    doNewQL.SetFieldVal("DTT_TIME", "Today|Now");
                    doNewQL.SetFieldVal("DTE_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("TME_TIMECOMPLETED", "");
                    doNewQL.SetFieldVal("MMO_IMPORTDATA", "");
                    doNewQL.SetFieldVal("GID_ID", goData.GenerateID("QL"));

                    //Set the line no to the highest line no of this quote + 1
                    //doQuoteLines = New clRowSet("QL", 1, "LNK_IN_QT='" & doForm.doRS.GetFieldVal("GID_ID") & "'", "DTT_QTETIME D, SR__LINENO A", , , , , , , , doForm.doRS.bBypassValidation)
                    //*** MI 11/21/07 Optimization: read only rowset

                    string sWhere = "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'";



                    //doQuoteLines = new clRowSet("QL", 3, "LNK_IN_QT='" + doForm.doRS.GetFieldVal("GID_ID") + "'", "", "SR__LINENO");
                    doQuoteLines = new clRowSet("QL", 3, sWhere, "", "SR__LINENO");
                    if (doQuoteLines.GetFirst() == 1)
                    {
                        do
                        {
                            lLine = Convert.ToDouble(doQuoteLines.GetFieldVal("SR__LINENO", 2));
                            if (lLine > lHighestLine)
                                lHighestLine = lLine;
                            if (doQuoteLines.GetNext() == 0)
                                break; // TODO: might not be correct. Was : Exit Do
                        } while (true);
                    }
                    else
                    {
                        //goP.TraceLine("No Quote Lines found.", "", sProc)
                    }
                    doQuoteLines = null;

                    lHighestLine = lHighestLine + 1;

                    doNewQL.SetFieldVal("SR__LINENO", lHighestLine, 2);
                    sNewID = doNewQL.GetFieldVal("GID_ID").ToString();

                    if (doNewQL.Commit() == 0)
                    {
                        goErr.SetWarning(30200, sProc, "", "An error occurred while duplicating the Quote Line." + Environment.NewLine + "Open the Quote Line you are trying to duplicate and make sure all required fields are filled.", "", "", "", "", "", "", "", "", "");
                        return false;
                    }
                }
                else
                {
                    goErr.SetError(35000, sProc, "Duplicating Quote Line failed.");
                    return false;
                }
            }

            doForm.doRS.UpdateLinkState("LNK_Connected_QL");
            //doForm.RefreshLinkNames("LNK_Connected_QL")
            //Select the new QL
            doForm.SetLinkSelection("LNK_Connected_QL", sNewID);
            par_doCallingObject = doForm;
            //Calc_QuoteTotal(doForm.doRS);            
            return true;
        }

        public bool QT_FormControlOnChange_LNK_FORLINE_MO_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;

            if (Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO")) != "")
            {
                string MO_Gid = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%GID_ID"));

                clRowSet doRSModel = default(clRowSet);

                doRSModel = new clRowSet("MO", 3, "GID_ID='" + MO_Gid + "'", "", "cur_price,TXT_UNITTEXT,LI__SKUNO,TXT_DESCRIPTION");
                string sModelDesc = Convert.ToString(doForm.doRS.GetFieldVal("LNK_FORLINE_MO%%TXT_DESCRIPTION"));

                doForm.doRS.SetFieldVal("CUR_LINEPRICEUNIT", doRSModel.GetFieldVal("CUR_PRICE", 1).ToString());
                doForm.doRS.SetFieldVal("TXT_LINEUNIT", doRSModel.GetFieldVal("TXT_UNITTEXT", 1).ToString());
                doForm.doRS.SetFieldVal("LI__LINESKU", doRSModel.GetFieldVal("LI__SKUNO", 1).ToString());
                doForm.doRS.SetFieldVal("TXT_LINEMODEL", sModelDesc);
            }

            par_doCallingObject = doForm;
            //par_bRunNext = false;
            return true;

        }
        public bool XW_FormOnLoadRecord_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", false, "XX");

            //TLD 5/30/2012 Added for AC New User Prompts
            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                return false;
            }

            string sFiscalCalander = goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "FISCALCALENDER", "");

            string[] sFiscalValues = sFiscalCalander.Split('|');
            StringBuilder sFical = new StringBuilder();
            for (int i = 0; i < sFiscalValues.Length; i++)
            {
                sFical.Append(sFiscalValues[i]);

                if (i < sFiscalValues.Length - 1)
                {
                    sFical.Append(System.Environment.NewLine);
                }
            }

            doForm.SetControlVal("NDB_MMO_FISCALCALANDER", sFical.ToString());

            par_doCallingObject = doForm;
            return true;

        }

        public bool XW_FormOnSave_Pre(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {

            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);

            Form doForm = (Form)par_doCallingObject;
            string sWOP = goMeta.PageRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "", false, "XX");

            //TLD 5/30/2012 Added for AC New User Prompts
            if (goP.GetMe("PERMWORKGROUPOPTIONS") != "1")
            {
                doForm.MessageBox("You do not have permissions to edit Workgroup options.");
                return false;
            }

            string sFiscal = Convert.ToString(doForm.GetControlVal("NDB_MMO_FISCALCALANDER"));
            sFiscal = sFiscal.Replace("<br />", "|").Replace("</p><p>", "|").Replace("</p>", "").Replace("<p>", "");

            goTR.StrWrite(ref sWOP, "FISCALCALENDER", sFiscal);

            goMeta.PageWrite("GLOBAL", "WOP_WORKGROUP_OPTIONS", sWOP, "", "", "XX");

            doForm.CloseOnReturn = true;
            doForm.CancelSave();

            return true;

        }
        public bool FD_RecordOnSave_Post(ref object par_doCallingObject, ref object par_oReturn, ref bool par_bRunNext, ref string par_sSections, clArray par_doArray = null, string par_s1 = "", string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "")
        {
            string sProc = System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name + "::" + System.Reflection.MethodInfo.GetCurrentMethod().Name;
            goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, true);
            clRowSet doRS = (clRowSet)par_doCallingObject;

            string sTitle = "";
            string sUserGid = "";

            sTitle = Convert.ToString(doRS.GetFieldVal("TXT_TITLE"));
            sUserGid = Convert.ToString(doRS.GetFieldVal("LNK_TO_US"));

            goUI.AddAlert(sTitle + "  file is ready to download", "OPENDESKTOP", "DSK_57D90904-E840-4155-5858-B13400A198D9", sUserGid, "").ToString();

            par_doCallingObject = doRS;
            return true;
        }

    }
}
