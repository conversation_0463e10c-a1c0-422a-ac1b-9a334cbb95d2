<%@ WebService Language="VB" Debug="true" Class="WebService_RowSet" %>

Imports System.Web
Imports System.Xml
Imports System.Xml.XPath
Imports System.Xml.Schema
Imports System.Web.Services
Imports System.Web.Services.Protocols
Imports System.IO
Imports Selltis.BusinessLogic
Imports System.Data


<WebService(Namespace:="http://tempuri.org/")> _
<WebServiceBinding(ConformsTo:=WsiProfiles.BasicProfile1_1)> _
Public Class WebService_RowSet


    Inherits System.Web.Services.WebService
    '*****************************************************************************

    Public m_sErrorResult As String
    Public m_sProcessInstructions As String


    <WebMethod(EnableSession:=True)> _
    Public Function Logon(ByVal sUser As String, ByVal sPassword As String) As Boolean

        'Dim sb As Boolean
        'sb = Membership.ValidateUser(sUser, sPassword)

        Dim _memberShip As New clSelltisMembershipProviderNew()
        Dim sb As Boolean = _memberShip.ValidateUser(sUser, sPassword)

        If sb Then
            Dim Init As New clInit
        End If

        Dim goP As clProject = HttpContext.Current.Session("goP")
        goP.SetVar("bBypassValidation", True)
        goP.SetVar("bNoRecordOnSave", False)

        Return sb

    End Function

    <WebMethod(EnableSession:=True)>
    Public Function SetVar(ByVal sProperty As String, ByVal sValue As String) As Boolean

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim goP As clProject = HttpContext.Current.Session("goP")
        goP.SetVar(sProperty, sValue)

        Return True

    End Function

    <WebMethod(EnableSession:=True)>
    Public Function NewRS(ByVal sFile As String,
                        ByVal iType As Integer,
                        ByVal sCondition As String,
                        ByVal sSort As String,
                        ByVal sFields As String,
                        ByVal iTop As Integer,
                        ByVal sINI As String,
                        ByVal par_sGenFieldsDefs As String) As Boolean


        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim Init As New clInit

        HttpContext.Current.Session("goP").sRunMode = "Webservice"


        Dim rs As New clRowSet(sFile, iType, sCondition, sSort, sFields, iTop, sINI, par_sGenFieldsDefs)
        Session("WS_RS_" & Session.SessionID) = rs
        Return True


    End Function


    <WebMethod(EnableSession:=True)>
    Public Function Bypass(ByVal bBypassValidation As Boolean, ByVal bNoRecordOnSave As Boolean) As Boolean

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        'Try
        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        rs.bBypassValidation = bBypassValidation
        rs.bNoRecordOnSave = bNoRecordOnSave

        Dim goP As clProject = HttpContext.Current.Session("goP")
        goP.SetVar("bBypassValidation", bBypassValidation)
        goP.SetVar("bNoRecordOnSave", bNoRecordOnSave)

        Return True

        'Catch ex As Exception

        'End Try

        Return False

    End Function

    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function SetFieldVal(ByVal sFieldName As String, ByVal oValue As Object, ByVal iFormat As Integer) As Integer

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        Dim goTr As clTransform = HttpContext.Current.Session("goTr")

        If goTr.GetPrefix(sFieldName) = "MMO_" Or goTr.GetPrefix(sFieldName) = "LNK_" Or goTr.GetPrefix(sFieldName) = "URL_" Then
            If oValue <> "" Then
                oValue = Replace(oValue, vbLf, "!@#!@#!!!!!||")
                oValue = Replace(oValue, "!@#!@#!!!!!||", vbCrLf)
                oValue = Replace(oValue, "<%MEID%>", "<%ME%>")
            End If
        End If

        Return rs.SetFieldVal(sFieldName, oValue, iFormat)

    End Function

    <WebMethod(EnableSession:=True)>
    Public Function SetFieldVals(ByVal sIni As String) As Integer

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)



        'Return rs.SetFieldVal(sFieldName, oValue, iFormat)

    End Function


    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function GetFieldVal(ByVal sFieldName As String, ByVal iFormat As Integer) As Object

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Try

            '*********************************************
            'Get session rowset
            Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
            Dim oReturn As Object

            '*********************************************
            'Execute call
            oReturn = Replace(rs.GetFieldVal(sFieldName, iFormat), Chr(1), vbCrLf)


            Return oReturn

        Catch ex As Exception

            '*********************************************
            'Get session err obj
            'Dim goErr As clError
            'goErr = HttpContext.Current.Session("goErr")


            'Err.Raise(goErr.GetLastError(), , goErr.GetLastError("MESSAGE"))
            Return ("")


        End Try

    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function Log(ByVal par_sMethod As String, ByVal sErrorMessage As String, ByVal iLevel As Integer) As Boolean

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        '*********************************************
        'Get session err obj
        Dim goLog As clLog
        goLog = HttpContext.Current.Session("goLog")
        Return goLog.Log(par_sMethod, sErrorMessage, iLevel)

    End Function

    <WebMethod(EnableSession:=True)>
    Public Function GetLinkVal(ByVal sFieldName As String, ByVal sDelim As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        '*********************************************
        'Get session rowset
        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        Dim oReturn As Object

        '*********************************************
        'Execute call
        oReturn = rs.GetFieldVal(sFieldName, clC.SELL_FRIENDLY, , , , sDelim)

        Return oReturn

    End Function


    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function GetFieldVals(ByVal sFields As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        'Return rs.GetFieldVal(sFieldName, iFormat)
        Return ""

    End Function

    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function ProcessingInstructions(ByVal sInstructions As String) As Boolean



        sInstructions = Replace(sInstructions, "|||^|||", vbCrLf)

        If InStr(sInstructions, vbCrLf) = 0 Then
            sInstructions = Replace(sInstructions, vbLf, vbCrLf)
        End If

        Dim goP As clProject = HttpContext.Current.Session("goP")
        Dim goTr As clTransform = HttpContext.Current.Session("goTr")
        Dim i As Integer = CInt(goTr.StrRead(sInstructions, "TranslateIdCount", "0"))
        Dim j As Integer
        Dim strFields(i) As String

        'Try

        For j = 1 To i
            strFields(j) = goTr.StrRead(sInstructions, "TranslateField" & j, " ")
        Next

        goP.SetVar("TranslateFields", strFields)
        goP.SetVar("ExternalSource", goTr.StrRead(sInstructions, "ExternalSource", ""))
        goP.SetVar("bBypassValidation", CBool(goTr.StrRead(sInstructions, "bBypassValidation", "true")))
        goP.SetVar("bNoRecordOnSave", CBool(goTr.StrRead(sInstructions, "bNoRecordOnSave", "false")))
        goP.SetVar("bUpsert", CBool(goTr.StrRead(sInstructions, "bUpsert", "false")))
        goP.SetVar("PreCommitScript", goTr.StrRead(sInstructions, "PreCommitScript", ""))
        'Added for LimitTo system
        goP.SetVar("LimitTo", goTr.StrRead(sInstructions, "LimitTo", "Both"))
        goP.SetVar("UpdateKey", goTr.StrRead(sInstructions, "UpdateKey", ""))

        'Catch ex As Exception


        'End Try

        Return True

    End Function

    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function ClearLink(ByVal sLinkName As String, ByVal oLinks As Object) As Integer

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        Return rs.ClearLink(sLinkName, oLinks)

    End Function
    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function ClearLinkAll(ByVal sLinkName As String) As Integer

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        Return rs.ClearLinkAll(sLinkName)

    End Function
    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function Commit() As Integer

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        Return rs.Commit

    End Function
    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function Count() As Long

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        Return rs.Count()
    End Function
    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function DeleteRecord() As Integer

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        Return rs.DeleteRecord()

    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function DeleteAll() As Integer

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        Return rs.DeleteAll()

    End Function
    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function ToTable() As Boolean

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        Return rs.ToTable()

    End Function
    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function GetTransTable() As DataSet

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        rs.ToTable()

        Dim ds As New DataSet
        ds.Tables.Add(rs.dtTransTable)

        Return ds

    End Function

    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function GetTransTableAsXML() As XmlDocument

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        'Wrapper function for GetTransTable to accept and return data as xml
        Dim goData As clData = HttpContext.Current.Session("goData")
        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        rs.ToTable()
        Dim ds As New DataSet
        ds.Tables.Add(rs.dtTransTable)

        Return goData.DataSetToXML(ds, True, "SelltisData")

    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function GetTransTableAsXMLWithCount(ByVal Start As Integer, ByVal RecordCount As Integer) As XmlDocument

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        'Wrapper function for GetTransTable to accept and return data as xml
        Dim goData As clData = HttpContext.Current.Session("goData")
        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        rs.ToTable()
        Dim dt As DataTable = rs.dtTransTable.Clone
        Dim totalreccount As Integer
        totalreccount = rs.dtTransTable.Rows.Count

        For i As Integer = Start To (Start + RecordCount) - 1
            'If i > rs.dtTransTable.Rows.Count Then Exit For
            'dt.ImportRow(rs.dtTransTable.Rows(i))
            If i < totalreccount Then
                dt.ImportRow(rs.dtTransTable.Rows(i))
            Else
                Exit For
            End If
        Next

        Dim ds As New DataSet
        ds.Tables.Add(dt)

        Return goData.DataSetToXML(ds, True, "SelltisData")

    End Function

    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function GetTransTableAsXMLString() As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        'Wrapper function for GetTransTable to accept and return data as xml
        Dim goData As clData = HttpContext.Current.Session("goData")
        Dim rs As clRowSet = Session("WS_RS_" & Session.SessionID)
        rs.ToTable()
        Dim ds As New DataSet
        ds.Tables.Add(rs.dtTransTable)

        Return goData.DataSetToXML(ds, True, "SelltisData").InnerXml.ToString

    End Function

    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function GetLastError(ByVal par_sParam As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        '*********************************************
        'Get session err obj
        Dim goErr As clError
        goErr = HttpContext.Current.Session("goErr")


        GetLastError = goErr.GetLastError(par_sParam)


    End Function

    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function Logoff() As Boolean

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Session("WS_RS_" & Session.SessionID) = Nothing
        Session.Clear()
        Session.Abandon()
        GC.Collect()


        Return True


    End Function

    '*****************************************************************************
    <WebMethod(EnableSession:=True)>
    Public Function GetMetaPage(ByVal par_sSection As String, ByVal par_sPage As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim oMeta As clMetaData = HttpContext.Current.Session("goMeta")
        Return Replace(oMeta.PageRead(par_sSection, par_sPage), Chr(1), vbCrLf)

    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function ResetConduit(ByVal par_sConduit As String, ByVal par_sValue As String) As Boolean

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")
        Dim goP As clProject = HttpContext.Current.Session("goP")
        Dim oMeta As clMetaData = HttpContext.Current.Session("goMeta")
        Return oMeta.LineWrite(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", par_sConduit & "_RESET","","", par_sValue,"XX")

    End Function

    '*****************************************************************************   



    <WebMethod(EnableSession:=True)>
    Public Function GetMe(ByVal par_sVal As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim goP As clProject = HttpContext.Current.Session("goP")

        Return goP.GetMe(par_sVal)

    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function GetLoginByUserID(ByVal par_sUserID As String, ByVal par_sType As String) As String

        'PURPOSE:
        '       Return A Login ID or Login Name for the provided User's GID_ID.
        'PARAMETERS:
        '       par_sUserID: User record GID_ID
        '       par_sType: What to return: 
        '           ID: Login ID (GID_ID of the record in the XU file) [default]
        '           NAME: TXT_LogonName of the login
        'RETURNS:
        '       String: Login GID_ID or TXT_LogonName depending on par_sType for
        '           User with GID_ID = par_sUserID


        Dim oPerm As clPerm = HttpContext.Current.Session("goPerm")
        Return oPerm.GetUserLogin(par_sUserID, par_sType)


    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function GetQuoteSubject() As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim goScr As clScrMngRowSet = New clScrMngRowSet
        goScr.Initialize()
        Dim sReturn As String = ""


        goScr.RunScript("Quote_EmailSubject",sReturn)
        Return sReturn


    End Function

    '*****************************************************************************
    'MS 20150903
    <WebMethod(EnableSession:=True)>
    Public Function GetFieldLabelFromName(ByVal par_sFileName As String,
                  ByVal par_sFieldName As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")
        Dim goData As clData = HttpContext.Current.Session("goData")
        Dim goTr As clTransform = HttpContext.Current.Session("goTr")
        Dim sReturn As String = ""
        Dim sLinkFile As String
        Dim sLinkField As String

        If goTr.GetPrefix(par_sFieldName) = "LNK_" And InStr(par_sFieldName, "%%") > 0 Then
            'Link field defined
            sLinkFile = goTr.GetFileFromLinkName(par_sFieldName)
            sLinkField = goTr.ExtractString(par_sFieldName, 2, "%%")
            sReturn = goData.GetFieldLabelFromName(sLinkFile, sLinkField)
        Else

            sReturn = goData.GetFieldLabelFromName(par_sFileName, par_sFieldName)
        End If
        Return sReturn
    End Function
    '*****************************************************************************
    'MS 20150903
    <WebMethod(EnableSession:=True)>
    Public Function GetFileLabel(ByVal par_sFile As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")
        Dim goData As clData = HttpContext.Current.Session("goData")
        Dim sReturn As String = ""


        sReturn = goData.GetFileLabel(par_sFile)

        Return sReturn
    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function TestIDsForDeletions(ByVal sIDS As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        sIDS = Replace(sIDS, "||", vbCrLf)

        Dim goP As clProject = HttpContext.Current.Session("goP")
        Dim goData As clData = HttpContext.Current.Session("goData")
        Dim sReturn As String = ""

        Dim oArr As New clArray

        oArr.StringToArray(sIDS)

        Dim i As Integer = 0

        For i = 1 To oArr.GetDimension
            If Not goData.IsRecordValid(oArr.GetItem(i)) Then
                sReturn = sReturn & oArr.GetItem(i) & "||"
            End If
        Next

        Return sReturn

    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function GetFieldList(ByVal par_sFile As String) As String


        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim goData As clData
        goData = HttpContext.Current.Session("goData")
        Dim oArr As clArray

        oArr = goData.GetFields(par_sFile)
        Return oArr.CopyAllInString



    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function GetLinksList(ByVal par_sFile As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim goData As clData
        goData = HttpContext.Current.Session("goData")
        Dim oArr As clArray

        oArr = goData.GetLinks(par_sFile)
        Return oArr.CopyAllInString


    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function GetFileList() As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim goData As clData
        goData = HttpContext.Current.Session("goData")
        Dim oArr As clArray

        oArr = goData.GetFiles()
        Return oArr.CopyAllInString


    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function GetPermission(ByVal par_sFile As String, ByVal par_sType As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim goPerm As clPerm
        goPerm = HttpContext.Current.Session("goPerm")
        Dim sPerm As String = ""

        sPerm = goPerm.GetSelectivePermission(par_sFile, par_sType)
        Return sPerm

    End Function
    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function ValidateSession() As Boolean


        Try
            Dim oP As clProject = HttpContext.Current.Session("goP")
            If oP.gsUserID = "" Then

                HttpContext.Current.Session.Abandon()

                Return False
            Else
                Return True
            End If
        Catch ex As Exception
            HttpContext.Current.Session.Abandon()
            Return False
        End Try

    End Function


    <WebMethod(EnableSession:=True)>
    Public Function AddRecordsByXML(ByVal xmlNode As System.Xml.XmlDocument, ByVal par_bDupCheck As Boolean, ByVal par_sDupReturnFields As String) As XmlDocument

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim ds As New DataSet
        Dim test As String = m_sProcessInstructions
        Dim goData As clData = HttpContext.Current.Session("goData")

        Dim golog As clLog = HttpContext.Current.Session("goLog")
        golog.Log("Rowset.asmx::AddRecordsByXML", xmlNode.InnerXml)

        If ValidateIncomingXml(xmlNode) = False Then
            Return ErrorXML()
        End If
        ds = AddRecordsByDataset(goData.XMLToDataSet(xmlNode), par_bDupCheck, par_sDupReturnFields)
        Dim xmlReturn As System.Xml.XmlDocument = New XmlDocument()
        xmlReturn.LoadXml(ds.GetXml())
        Return goData.DataSetToXML(ds, True, "SelltisData")

    End Function

    <WebMethod(EnableSession:=True)>
    Public Function AddRecordsByXMLString(ByVal xmlString As String, ByVal par_bDupCheck As Boolean, ByVal par_sDupReturnFields As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim ds As New DataSet

        Dim test = m_sProcessInstructions

        Dim goData As clData = HttpContext.Current.Session("goData")

        Dim golog As clLog = HttpContext.Current.Session("goLog")
        golog.Log("Rowset.asmx::AddRecordsByXML", xmlString)

        Dim xDoc As New System.Xml.XmlDocument

        xDoc.LoadXml(xmlString)

        If ValidateIncomingXml(xDoc) = False Then
            Return ErrorXML().InnerXml.ToString
        End If
        ds = AddRecordsByDataset(goData.XMLToDataSet(xDoc), par_bDupCheck, par_sDupReturnFields)
        Dim xmlReturn As System.Xml.XmlDocument = New XmlDocument()
        xmlReturn.LoadXml(ds.GetXml())

        Return goData.DataSetToXML(ds, True, "SelltisData").InnerXml.ToString

    End Function

    Public Function testTranslate(ByVal strField As String) As Boolean

        Dim strTest() As String
        Dim goP As clProject = HttpContext.Current.Session("goP")
        Try

            strTest = goP.GetVar("TranslateFields")

        Catch ex As Exception

            Return False

        End Try

        Dim i As Integer = 0

        For i = strTest.GetLowerBound(0) To strTest.GetUpperBound(0)

            If UCase(strTest(i)) = UCase(strField) Then

                Return True

            End If

        Next

        Return False

    End Function


    '*****************************************************************************    
    <WebMethod(EnableSession:=True)>
    Public Function AddRecordsByDataset(ByVal par_oDataSet As DataSet, ByVal par_bDupCheck As Boolean, ByVal par_sDupReturnFields As String) As DataSet

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim dsReturn As New DataSet
        Dim goP As clProject = HttpContext.Current.Session("goP")
        Dim goErr As clError = HttpContext.Current.Session("goErr")
        Dim goData As clData = HttpContext.Current.Session("goData")
        'Try

        Dim goScr As clScrMngRowSet = New clScrMngRowSet 'clScrMng = HttpContext.Current.Session("goScr")
        goScr.Initialize()
        Dim goTr As clTransform = HttpContext.Current.Session("goTr")
        Dim goLog As clLog = HttpContext.Current.Session("goLog")

        Dim i As Integer
        Dim j As Integer
        Dim k As Integer
        Dim l As Integer
        Dim sCurrentTableName As String = ""
        Dim oARS As clRowSet
        Dim sCurrentGID As String = ""
        Dim bHasDuplicates As Boolean = False
        Dim sRecordData As String = ""
        Dim oDupRS As clRowSet = Nothing
        Dim dt As New DataTable
        Dim dc As DataColumn
        Dim dr As DataRow
        Dim bAddError As Boolean = False
        Dim sLogLine As String = ""
        Dim sFields As String = ""
        Dim sNewCol As String = ""
        Dim dtStore As DataTable
        If par_sDupReturnFields = "" Or par_sDupReturnFields = String.Empty Then
            par_sDupReturnFields = "GID_ID|SYS_NAME"
        End If
        sCurrentTableName = par_oDataSet.Tables(0).TableName

        '*****************************************
        'Build return table            
        i = 1

        dc = New DataColumn("EXT_ID", GetType(String))
        dt.Columns.Add(dc)
        dc = New DataColumn("STATUS", GetType(String))
        dt.Columns.Add(dc)
        dc = New DataColumn("DETAILS", GetType(String))
        dt.Columns.Add(dc)
        dc = New DataColumn("GID_ID", GetType(String))
        dt.Columns.Add(dc)


        Do While goTr.ExtractString(par_sDupReturnFields, i, "|") <> clC.EOT
            sNewCol = goTr.ExtractString(par_sDupReturnFields, i, "|")
            If sNewCol = "EXT_ID" Or sNewCol = "STATUS" Or sNewCol = "DETAILS" Or sNewCol = "GID_ID" Then
                'Move to next
            Else

                dc = New DataColumn(sNewCol, GetType(String))
                dt.Columns.Add(dc)

            End If
            i = i + 1
        Loop
        '*****************************************

        For j = 0 To par_oDataSet.Tables(0).Rows.Count - 1

            bHasDuplicates = False

            'Get duplicates        
            If par_bDupCheck Then


                sFields = "*," & par_sDupReturnFields
                sFields = Replace(sFields, "|", ",")


                For iCol As Integer = 0 To par_oDataSet.Tables(0).Columns.Count - 1

                    Try
                        goTr.StrWrite(sRecordData, par_oDataSet.Tables(0).Columns.Item(iCol).ColumnName, par_oDataSet.Tables(0).Rows(j).Item(iCol))
                    Catch ex0 As Exception
                        goTr.StrWrite(sRecordData, par_oDataSet.Tables(0).Columns.Item(iCol).ColumnName, "")
                    End Try


                Next iCol

                goScr.RunScript("GetDuplicates", Me, sCurrentTableName, sRecordData, sFields, , oDupRS)

                If oDupRS.Count <> 0 Then
                    bHasDuplicates = True
                End If

            End If

            sRecordData = ""

            'Add new record as there are either no dups or par_bDupCheck is False
            Dim oStore As Object
            If bHasDuplicates = False Then

                dr = dt.NewRow()
                bAddError = False

                oARS = New clRowSet(sCurrentTableName, clC.SELL_ADD)

                'Set field values for record being added                        
                For k = 0 To par_oDataSet.Tables(0).Columns.Count - 1
                    If par_oDataSet.Tables(0).Columns(k).ColumnName <> "EXT_ID" Then

                        If IsDBNull(par_oDataSet.Tables(0).Rows(j).Item(k)) = False Then
                            oStore = par_oDataSet.Tables(0).Rows(j).Item(k)
                            If oStore = "null;" Or oStore = "null" Then

                                oStore = ""

                            End If


                            If testTranslate(par_oDataSet.Tables(0).Columns(k).ColumnName) Then

                                dtStore = goData.GetTNID(goP.GetVar("ExternalSource"), Right(par_oDataSet.Tables(0).Columns(k).ColumnName, 2), "", oStore)
                                If dtStore.Rows.Count > 0 Then
                                    oStore = dtStore.Rows(0).Item(0).ToString
                                Else
                                    oStore = ""
                                End If

                            End If

                            If oARS.SetFieldVal(par_oDataSet.Tables(0).Columns(k).ColumnName, oStore, clC.SELL_FRIENDLY) = 0 Then

                                If Left(par_oDataSet.Tables(0).Columns(k).ColumnName, 3) <> "LNK" Then
                                    'log error
                                    sLogLine = "Field: " & par_oDataSet.Tables(0).Columns(k).ColumnName & "  Value: " & par_oDataSet.Tables(0).Rows(j).Item(k)
                                    goLog.Log("ws_RowSet::AddRecordsByDataset", sLogLine, 1, , True)

                                    dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                                    dr("STATUS") = "Error"
                                    dr("DETAILS") = "Error on SetFieldVal: " & goErr.GetLastError("MESSAGE")
                                    bAddError = True
                                    Exit For

                                End If

                            End If

                        End If

                    End If

                Next

                'Commit record    
                sCurrentGID = oARS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY)
                If bAddError = False Then
                    'oARS.bBypassValidation = True
                    SetRowsetBypass(oARS)
                    If oARS.Commit = 0 Then
                        'log error
                        dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                        dr("STATUS") = "Error"
                        dr("DETAILS") = "Error on Commit: " & goErr.GetLastError("MESSAGE")
                        bAddError = True
                    Else
                        'log add
                        dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                        dr("STATUS") = "Added"
                        dr("GID_ID") = sCurrentGID
                    End If
                End If
                oARS = Nothing
                dt.Rows.Add(dr)

            Else
                'Add duplicates to return table

                For l = 1 To oDupRS.Count
                    i = 1
                    dr = dt.NewRow()
                    If l <> 1 Then
                        oDupRS.GetNext()
                    End If

                    dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                    dr("STATUS") = "Dup"
                    dr("GID_ID") = oDupRS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY)

                    Do While goTr.ExtractString(par_sDupReturnFields, i, "|") <> clC.EOT
                        dr(goTr.ExtractString(par_sDupReturnFields, i, "|")) = oDupRS.GetFieldVal(goTr.ExtractString(par_sDupReturnFields, i, "|"), clC.SELL_FRIENDLY)
                        i = i + 1
                    Loop
                    dt.Rows.Add(dr)
                Next

            End If

        Next

        dsReturn.Tables.Add(dt)

        'Catch ex As Exception

        'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '    goErr.SetError(ex, 45105, "ws_RowSet::AddRecordsByDataset")
        'End If

        'End Try

        Return dsReturn

    End Function


    '*****************************************************************************   
    <WebMethod(EnableSession:=True)>
    Public Function EditRecordsByXML(ByVal xmlNode As System.Xml.XmlDocument) As XmlDocument

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        'Wrapper function for EditRecordsByDataset to accept and return data as xml
        Dim ds As DataSet
        Dim goData As clData = HttpContext.Current.Session("goData")

        Dim golog As clLog = HttpContext.Current.Session("goLog")
        golog.Log("Rowset.asmx::AddRecordsByXML", xmlNode.InnerXml)

        If ValidateIncomingXml(xmlNode) = False Then
            Return ErrorXML()
        End If
        ds = EditRecordsByDataset(goData.XMLToDataSet(xmlNode))
        Dim xmlReturn As System.Xml.XmlDocument = New XmlDocument()
        xmlReturn.LoadXml(ds.GetXml())

        Return goData.DataSetToXML(ds, True, "SelltisData")

    End Function

    '*****************************************************************************   
    <WebMethod(EnableSession:=True)>
    Public Function EditRecordsByXMLString(ByVal xmlString As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        'Wrapper function for EditRecordsByDataset to accept and return data as xml
        Dim ds As DataSet
        Dim goData As clData = HttpContext.Current.Session("goData")

        Dim golog As clLog = HttpContext.Current.Session("goLog")
        golog.Log("Rowset.asmx::AddRecordsByXML", xmlString)

        Dim xDoc As New System.Xml.XmlDocument

        xDoc.LoadXml(xmlString)

        If ValidateIncomingXml(xDoc) = False Then
            Return ErrorXML().InnerXml.ToString
        End If
        ds = EditRecordsByDataset(goData.XMLToDataSet(xDoc))
        Dim xmlReturn As System.Xml.XmlDocument = New XmlDocument()
        xmlReturn.LoadXml(ds.GetXml())

        Return goData.DataSetToXML(ds, True, "SelltisData").InnerXml.ToString

    End Function

    <WebMethod(EnableSession:=True)>
    Public Function EditRecordsByDataset(ByVal par_oDataSet As DataSet) As DataSet

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim dsReturn As New DataSet
        Dim goP As clProject = HttpContext.Current.Session("goP")
        Dim goErr As clError = HttpContext.Current.Session("goErr")
        Dim goData As clData = HttpContext.Current.Session("goData")
        'Try

        Dim goScr As clScrMngRowSet = New clScrMngRowSet 'HttpContext.Current.Session("goScr")
        goScr.Initialize()
        Dim goTr As clTransform = HttpContext.Current.Session("goTr")
        Dim goLog As clLog = HttpContext.Current.Session("goLog")

        Dim i As Integer
        Dim j As Integer
        Dim k As Integer
        Dim sCurrentTableName As String = ""
        Dim oERS As clRowSet
        Dim sCurrentGID As String = ""
        Dim bHasDuplicates As Boolean = False
        Dim sRecordData As String = ""
        'Dim oDupRS As clRowSet
        Dim dt As New DataTable
        Dim dc As DataColumn
        Dim dr As DataRow
        Dim bEditError As Boolean = False
        Dim sLogLine As String = ""
        Dim sFields As String = ""
        Dim dtStore As DataTable

        sCurrentTableName = par_oDataSet.Tables(0).TableName

        '*****************************************
        'Build return table            
        i = 1

        dc = New DataColumn("EXT_ID", GetType(String))
        dt.Columns.Add(dc)
        dc = New DataColumn("STATUS", GetType(String))
        dt.Columns.Add(dc)
        dc = New DataColumn("DETAILS", GetType(String))
        dt.Columns.Add(dc)
        dc = New DataColumn("GID_ID", GetType(String))
        dt.Columns.Add(dc)

        '*****************************************
        Dim oStore As Object
        For j = 0 To par_oDataSet.Tables(0).Rows.Count - 1


            dr = dt.NewRow()
            bEditError = False

            oERS = New clRowSet(sCurrentTableName, clC.SELL_EDIT, "GID_ID='" & par_oDataSet.Tables(0).Rows(j).Item("GID_ID") & "'")
            If oERS.Count = 1 Then

                'Set field values for record being edited                        
                For k = 0 To par_oDataSet.Tables(0).Columns.Count - 1

                    If par_oDataSet.Tables(0).Columns(k).ColumnName <> "EXT_ID" Then

                        If IsDBNull(par_oDataSet.Tables(0).Rows(j).Item(k)) = False Then

                            oStore = par_oDataSet.Tables(0).Rows(j).Item(k)
                            If oStore = "null;" Or oStore = "null" Then

                                oStore = ""

                            End If

                            If testTranslate(par_oDataSet.Tables(0).Columns(k).ColumnName) Then

                                dtStore = goData.GetTNID(goP.GetVar("ExternalSource"), Right(par_oDataSet.Tables(0).Columns(k).ColumnName, 2), "", oStore)
                                If dtStore.Rows.Count > 0 Then
                                    oStore = dtStore.Rows(0).Item(0).ToString
                                Else
                                    oStore = ""
                                End If

                            End If

                            If oERS.SetFieldVal(par_oDataSet.Tables(0).Columns(k).ColumnName, oStore, clC.SELL_FRIENDLY) = 0 Then

                                If Left(par_oDataSet.Tables(0).Columns(k).ColumnName, 3) <> "LNK" Then
                                    'log error
                                    sLogLine = "Field: " & par_oDataSet.Tables(0).Columns(k).ColumnName & "  Value: " & par_oDataSet.Tables(0).Rows(j).Item(k)
                                    goLog.Log("ws_RowSet::EditRecordsByDataset", sLogLine, 1, , True)

                                    dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                                    dr("STATUS") = "Error"
                                    dr("DETAILS") = "Error on SetFieldVal: " & goErr.GetLastError("MESSAGE")
                                    bEditError = True
                                    Exit For
                                End If

                            End If

                        End If

                    End If

                Next

            Else
                dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                dr("STATUS") = "Error"
                dr("DETAILS") = "Record does not exist"
                bEditError = True

            End If

            If oERS.Count = 1 Then
                'Commit record    
                sCurrentGID = oERS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY)
                If bEditError = False Then
                    'oERS.bBypassValidation = True
                    SetRowsetBypass(oERS)

                    If oERS.Commit = 0 Then
                        'log error
                        dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                        dr("STATUS") = "Error"
                        dr("DETAILS") = "Error on Commit: " & goErr.GetLastError("MESSAGE")
                        bEditError = True
                    Else
                        'log edit
                        dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                        dr("STATUS") = "Edited"
                        dr("GID_ID") = sCurrentGID
                    End If
                End If

            End If
            oERS = Nothing
            dt.Rows.Add(dr)


        Next

        dsReturn.Tables.Add(dt)

        'Catch ex As Exception

        'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '    goErr.SetError(ex, 45105, "ws_RowSet::EditRecordsByDataset")
        'End If

        'End Try

        Return dsReturn

    End Function


    '*****************************************************************************   
    <WebMethod(EnableSession:=True)>
    Public Function UpsertRecordsByXML(ByVal xmlNode As System.Xml.XmlDocument, ByVal strCompareField As String, ByVal strExtSource As String) As XmlDocument

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        'Wrapper function for EditRecordsByDataset to accept and return data as xml
        Dim ds As DataSet
        Dim goData As clData = HttpContext.Current.Session("goData")

        Dim golog As clLog = HttpContext.Current.Session("goLog")
        golog.Log("Rowset.asmx::AddRecordsByXML", xmlNode.InnerXml)

        If ValidateIncomingXml(xmlNode) = False Then
            Return ErrorXML()
        End If
        ds = UpsertRecordsByDataset(goData.XMLToDataSet(xmlNode), strCompareField, strExtSource)
        Dim xmlReturn As System.Xml.XmlDocument = New XmlDocument()
        xmlReturn.LoadXml(ds.GetXml())

        Return goData.DataSetToXML(ds, True, "SelltisData")

    End Function

    '*****************************************************************************   
    <WebMethod(EnableSession:=True)>
    Public Function UpsertRecordsByXMLString(ByVal xmlString As String, ByVal strCompareField As String, ByVal strExtSource As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        'Wrapper function for EditRecordsByDataset to accept and return data as xml
        Dim ds As DataSet
        Dim goData As clData = HttpContext.Current.Session("goData")

        Dim golog As clLog = HttpContext.Current.Session("goLog")
        golog.Log("Rowset.asmx::UpsertRecordsByXML", xmlString)

        Dim xDoc As New System.Xml.XmlDocument

        xDoc.LoadXml(xmlString)

        If ValidateIncomingXml(xDoc) = False Then
            Return ErrorXML().InnerXml.ToString
        End If
        ds = UpsertRecordsByDataset(goData.XMLToDataSet(xDoc), strCompareField, strExtSource)
        Dim xmlReturn As System.Xml.XmlDocument = New XmlDocument()
        xmlReturn.LoadXml(ds.GetXml())

        Return goData.DataSetToXML(ds, True, "SelltisData").InnerXml.ToString

    End Function

    '*****************************************************************************   

    'Revised PJ 8/4/10 to handle multiple TN's for single external ID

    <WebMethod(EnableSession:=True)>
    Public Function UpsertRecordsByDataset(ByVal par_oDataSet As DataSet, ByVal strCompareField As String, ByVal strExtSource As String) As DataSet

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim dsReturn As New DataSet
        Dim goErr As clError = HttpContext.Current.Session("goErr")
        Dim goData As clData = HttpContext.Current.Session("goData")
        Dim goP As clProject = HttpContext.Current.Session("goP")

        'Try

        Dim goScr As clScrMngRowSet = New clScrMngRowSet 'As clScrMng = HttpContext.Current.Session("goScr")
        goScr.Initialize()
        Dim goTr As clTransform = HttpContext.Current.Session("goTr")
        Dim goLog As clLog = HttpContext.Current.Session("goLog")

        Dim i As Integer
        Dim j As Integer
        Dim k As Integer
        Dim sCurrentTableName As String = ""
        Dim oURS As clRowSet
        Dim sCurrentGID As String = ""
        Dim bHasDuplicates As Boolean = False
        Dim sRecordData As String = ""
        'Dim oDupRS As clRowSet
        Dim bAddError As Boolean = False
        ''Dim dt As New DataTable
        'Dim dc As DataColumn
        'Dim dr As DataRow
        Dim dt2 As New DataTable
        Dim dc2 As DataColumn
        Dim dr2 As DataRow
        Dim bEditError As Boolean = False
        Dim sLogLine As String = ""
        Dim sFields As String = ""
        Dim sTransType As String = ""
        Dim dtStore As DataTable
        Dim bAdd As Boolean = False
        sCurrentTableName = par_oDataSet.Tables(0).TableName

        '*****************************************
        'Build return table            
        i = 1

        'dc = New DataColumn("EXT_ID", GetType(String))
        'dt.Columns.Add(dc)
        'dc = New DataColumn("STATUS", GetType(String))
        'dt.Columns.Add(dc)
        'dc = New DataColumn("DETAILS", GetType(String))
        'dt.Columns.Add(dc)
        'dc = New DataColumn("GID_ID", GetType(String))
        'dt.Columns.Add(dc)

        dc2 = New DataColumn("Adds", GetType(String))
        dt2.Columns.Add(dc2)
        dc2 = New DataColumn("Edits", GetType(String))
        dt2.Columns.Add(dc2)
        dc2 = New DataColumn("Errors", GetType(String))
        dt2.Columns.Add(dc2)

        Dim iAdds As Integer = 0
        Dim iEdits As Integer = 0
        Dim iErrors As Integer = 0

        '*****************************************
        Dim oStore As Object
        Dim sb As New StringBuilder
        Dim dtCount As DataTable
        Dim strInternalId As String = ""
        '****************************************
        'New UpdateKey feature 
        Dim bCommit As Boolean = True
        '****************************************

        dr2 = dt2.NewRow()
        For j = 0 To par_oDataSet.Tables(0).Rows.Count - 1
            '****************************************
            'New UpdateKey feature 
            bCommit = True
            '****************************************
            dtCount = goData.GetTNID(strExtSource, sCurrentTableName, "", par_oDataSet.Tables(0).Rows(j).Item(strCompareField))
            '-- PJ: Old way - only checking first TN record
            'If dtCount.Rows.Count > 0 Then
            '    strInternalId = dtCount.Rows(0).Item(0).ToString
            'End If

            'PJ 8/4/10: New way - checking multiple TNs for valid record in file
            If dtCount.Rows.Count > 0 Then
                For Each dr As DataRow In dtCount.Rows
                    strInternalId = dr.Item(0).ToString
                    sb = New StringBuilder
                    sb.Append("GID_ID")
                    sb.Append("='")
                    sb.Append(strInternalId)
                    sb.Append("'")
                    oURS = New clRowSet(sCurrentTableName, clC.SELL_EDIT, sb.ToString)
                    If oURS.Count = 0 Then
                        'delete TN record because no matching record
                        'currently no method for this!!
                        strInternalId = ""
                    Else
                        'This internal id is valid
                        Exit For
                    End If
                Next
            End If

            'sb = New StringBuilder
            'sb.Append("GID_ID")
            'sb.Append("='")
            'sb.Append(strInternalId)
            'sb.Append("'")
            ''dr = dt.NewRow()

            bEditError = False
            bAdd = False
            If strInternalId = "" Then bAdd = True
            strInternalId = ""

            'PJ 8/4/10 - replaced this section with above
            'If bAdd = False Then
            '    oURS = New clRowSet(sCurrentTableName, clC.SELL_EDIT, sb.ToString)
            '    '--- PJ 7/7/10: Replaced this If..Then ---
            '    '   If the rowset is empty then the record was deleted, so add as new
            '    If oURS.Count = 0 Then
            '        bAdd = True
            '    End If
            '    'If oURS.Count = 1 Then
            '    '    bAdd = False
            '    'End If
            '    '-----------------------------------------
            'End If

            If bAdd = False Then    'THIS IS EDIT

                'Added for LimitTo system
                If UCase(goP.GetVar("LimitTo")) = "ADDS" Then GoTo MoveOn
                '****************************************
                'New UpdateKey feature                    
                If goP.GetVar("UpdateKey") <> "" Then bCommit = False
                '****************************************

                'Set field values for record being edited                        
                For k = 0 To par_oDataSet.Tables(0).Columns.Count - 1

                    If par_oDataSet.Tables(0).Columns(k).ColumnName <> "EXT_ID" Then

                        If IsDBNull(par_oDataSet.Tables(0).Rows(j).Item(k)) = False Then

                            '****************************************
                            'New UpdateKey feature
                            'Get value of current field
                            oStore = par_oDataSet.Tables(0).Rows(j).Item(k)
                            'If current column name = key column then
                            Dim sColumnName As String = UCase(par_oDataSet.Tables(0).Columns(k).ColumnName)
                            Dim sKeyColumn As String = UCase(goP.GetVar("UpdateKey"))
                            Dim sSellKeyValue As String = ""
                            If sColumnName = sKeyColumn Then
                                'If value of current field = value of key column in rowset
                                sSellKeyValue = oURS.GetFieldVal(par_oDataSet.Tables(0).Columns(k).ColumnName, clC.SELL_FRIENDLY)
                                If oStore = sSellKeyValue Then
                                    bCommit = True
                                End If
                            End If
                            '****************************************

                            oStore = par_oDataSet.Tables(0).Rows(j).Item(k)
                            If oStore = "null;" Or oStore = "null" Then

                                oStore = ""

                            End If

                            If testTranslate(par_oDataSet.Tables(0).Columns(k).ColumnName) Then

                                dtStore = goData.GetTNID(goP.GetVar("ExternalSource"), Right(par_oDataSet.Tables(0).Columns(k).ColumnName, 2), "", oStore)
                                If dtStore.Rows.Count > 0 Then
                                    oStore = dtStore.Rows(0).Item(0).ToString
                                Else
                                    oStore = ""
                                End If

                            End If

                            If oStore <> "" Then
                                If oURS.SetFieldVal(par_oDataSet.Tables(0).Columns(k).ColumnName, oStore, clC.SELL_FRIENDLY) = 0 Then

                                    If Left(par_oDataSet.Tables(0).Columns(k).ColumnName, 3) <> "LNK" Then
                                        'log error
                                        sLogLine = "Field: " & par_oDataSet.Tables(0).Columns(k).ColumnName & "  Value: " & par_oDataSet.Tables(0).Rows(j).Item(k)
                                        goLog.Log("ws_RowSet::UpsertRecordsByDataset", sLogLine, 1, , True)

                                        'dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                                        'dr("STATUS") = "Error"
                                        'dr("DETAILS") = "Error on SetFieldVal: " & goErr.GetLastError("MESSAGE")

                                        bEditError = True
                                        iErrors = iErrors + 1
                                        Exit For
                                    End If
                                End If
                            End If

                        End If

                    End If
                    sTransType = "Edited"
                Next

            Else    'THIS IS ADD
                'Added for LimitTo system
                If UCase(goP.GetVar("LimitTo")) = "EDITS" Then GoTo MoveOn

                oURS = New clRowSet(sCurrentTableName, clC.SELL_ADD, sb.ToString)
                'Set field values for record being added      
                If par_oDataSet.Tables(0).Rows(j).Item(strCompareField) <> "" Or IsDBNull(par_oDataSet.Tables(0).Rows(j).Item(strCompareField)) Then
                    oURS.SetFieldVal("TXT_ExternalID", par_oDataSet.Tables(0).Rows(j).Item(strCompareField), clC.SELL_FRIENDLY)
                End If

                If strExtSource <> "" Then
                    oURS.SetFieldVal("TXT_ExternalSource", strExtSource, clC.SELL_FRIENDLY)
                End If

                For k = 0 To par_oDataSet.Tables(0).Columns.Count - 1
                    If par_oDataSet.Tables(0).Columns(k).ColumnName <> "EXT_ID" Then

                        If IsDBNull(par_oDataSet.Tables(0).Rows(j).Item(k)) = False Then

                            oStore = par_oDataSet.Tables(0).Rows(j).Item(k)
                            If oStore = "null;" Or oStore = "null" Then

                                oStore = ""

                            End If

                            If testTranslate(par_oDataSet.Tables(0).Columns(k).ColumnName) Then

                                dtStore = goData.GetTNID(goP.GetVar("ExternalSource"), Right(par_oDataSet.Tables(0).Columns(k).ColumnName, 2), "", oStore)
                                If dtStore.Rows.Count > 0 Then
                                    oStore = dtStore.Rows(0).Item(0).ToString
                                Else
                                    oStore = ""
                                End If

                            End If

                            If oStore <> "" Then
                                If oURS.SetFieldVal(par_oDataSet.Tables(0).Columns(k).ColumnName, oStore, clC.SELL_FRIENDLY) = 0 Then

                                    If Left(par_oDataSet.Tables(0).Columns(k).ColumnName, 3) <> "LNK" Then
                                        'log error
                                        sLogLine = "Field: " & par_oDataSet.Tables(0).Columns(k).ColumnName & "  Value: " & par_oDataSet.Tables(0).Rows(j).Item(k)
                                        goLog.Log("ws_RowSet::UpsertRecordsByDataset", sLogLine, 1, , True)

                                        'dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                                        'dr("STATUS") = "Error"
                                        'dr("DETAILS") = "Error on SetFieldVal: " & goErr.GetLastError("MESSAGE")
                                        bAddError = True
                                        iErrors = iErrors + 1
                                        Exit For
                                    End If
                                End If
                            End If


                        End If

                    End If

                    sTransType = "Added"

                Next

            End If

            If oURS.Count = 1 Then
                'Commit record    
                sCurrentGID = oURS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY)
                If bEditError = False Then
                    'oERS.bBypassValidation = True
                    SetRowsetBypass(oURS)

                    '****************************************
                    'New UpdateKey feature 
                    If bCommit = True Then
                        '****************************************
                        If oURS.Commit = 0 Then
                            'log error
                            'dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                            'dr("STATUS") = "Error"
                            'dr("DETAILS") = "Error on Commit: " & goErr.GetLastError("MESSAGE")
                            bEditError = True
                            iErrors = iErrors + 1
                        Else
                            'log edit
                            'dr("EXT_ID") = par_oDataSet.Tables(0).Rows(j).Item("EXT_ID")
                            'dr("STATUS") = sTransType
                            'dr("GID_ID") = sCurrentGID

                            If sTransType = "Edited" Then
                                iEdits = iEdits + 1
                            ElseIf sTransType = "Added" Then
                                iAdds = iAdds + 1
                            End If
                        End If
                    End If

                End If

            End If
            oURS = Nothing
MoveOn:         'Added for LimitTo system

            'dt.Rows.Add(dr)
        Next
        dr2("Adds") = iAdds.ToString
        dr2("Edits") = iEdits.ToString
        dr2("Errors") = iErrors.ToString
        dt2.Rows.Add(dr2)


        'dsReturn.Tables.Add(dt)
        dsReturn.Tables.Add(dt2)

        'Catch ex As Exception

        'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '    goErr.SetError(ex, 45105, "ws_RowSet::UpsertRecordsByDataset")
        'End If

        'End Try

        Return dsReturn

    End Function







    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function GetList(ByVal sFile As String, ByVal sField As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim oList As New clList
        oList.Initialize()
        Dim sRet As String = ""

        'Try
        sRet = Replace(oList.GetList(sFile, sField, "VALUE").CopyAllInString, vbCrLf, "|**|")
        sRet = sRet & "|##||##|" & Replace(oList.GetList(sFile, sField).CopyAllInString, vbCrLf, "|**|")
        Return sRet

        'Catch ex As Exception

        'Return sRet
        'End Try

        Return sRet


    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function DatasetToTextFile(ByVal par_oDataset As DataSet, ByVal par_iMaxCharsPerCell As Integer) As Boolean

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim goData As clData
        goData = HttpContext.Current.Session("goData")

        Return goData.DatasetToTextFile(par_oDataset, par_iMaxCharsPerCell)

    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function GetSchemaAsXML() As System.Xml.XmlDocument

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim goData As clData
        goData = HttpContext.Current.Session("goData")
        goData.TestSchema()
        Dim xmlReturn As New XmlDocument()

        Dim ds As New DataSet
        Dim dt As New DataTable

        Dim sortDirection As String = "DESC"
        Dim sortFormat As String = "{0} {1}"

        Dim dest As DataTable = goData.dtLists.Clone

        'Try

        dt = goData.dtTables
        dt.DefaultView.Sort = String.Format(sortFormat, "Name", sortDirection)
        ds.Tables.Add(dt.DefaultView.Table)

        dt = goData.dtFields
        ds.Tables.Add(dt)

        dt = goData.dtLinks
        dt = ChangeRowVal(ChangeColName(dt, "LinkName", "FQLinkName"), "LinkName")
        dt.DefaultView.Sort = String.Format(sortFormat, "LinkName", sortDirection)
        ds.Tables.Add(dt.DefaultView.Table)

        dt = goData.dtLists
        For Each dr As DataRow In dt.Rows
            dest.ImportRow(dr)

        Next
        dest.TableName = "Table4"
        ds.Tables.Add(ChangeRowVal(ChangeColName(dest, "FileName", "TableName"), "ListName"))

        xmlReturn = goData.DataSetToXML(ds, True, "Selltis_Schema")

        'Catch ex As Exception

        'ex.Message.ToString()

        'End Try

        Return xmlReturn

    End Function

    '*****************************************************************************

    '<WebMethod(EnableSession:=True)> _
    'Public Function GetNextSetSchemaAsXML(ByVal SkipRecords As Integer) As System.Xml.XmlDocument

    '    If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

    '    Dim goData As clData
    '    goData = HttpContext.Current.Session("goData")
    '    goData.TestSchema()
    '    Dim xmlReturn As New XmlDocument()

    '    Dim ds As New DataSet
    '    Dim dt As New DataTable
    '    Dim dt1 As New DataTable
    '    Dim dt2 As New DataTable
    '    Dim dt3 As New DataTable

    '    Dim sortDirection As String = "DESC"
    '    Dim sortFormat As String = "{0} {1}"

    '    Dim dest As DataTable = goData.dtLists.Clone

    '    Try

    '        dt = goData.dtTables.Clone
    '        dt.TableName = goData.dtTables.TableName
    '        For i As Integer = SkipRecords To SkipRecords + 1000
    '            If goData.dtTables.Rows.Count < i Then Exit For
    '            dt.ImportRow(goData.dtTables.Rows(i))
    '        Next
    '        dt.DefaultView.Sort = String.Format(sortFormat, "Name", sortDirection)
    '        ds.Tables.Add(dt.DefaultView.Table)

    '        dt1 = goData.dtFields.Clone
    '        dt1.TableName = goData.dtFields.TableName
    '        For i As Integer = SkipRecords To SkipRecords + 1000
    '            If goData.dtFields.Rows.Count < i Then Exit For
    '            dt1.ImportRow(goData.dtFields.Rows(i))
    '        Next
    '        ds.Tables.Add(dt1)

    '        dt2 = goData.dtLinks.Clone
    '        dt2.TableName = goData.dtLinks.TableName
    '        For i As Integer = SkipRecords To SkipRecords + 1000
    '            If goData.dtLinks.Rows.Count < i Then Exit For
    '            dt2.ImportRow(goData.dtLinks.Rows(i))
    '        Next

    '        dt2 = ChangeRowVal(ChangeColName(dt2, "LinkName", "FQLinkName"), "LinkName")
    '        dt2.DefaultView.Sort = String.Format(sortFormat, "LinkName", sortDirection)
    '        ds.Tables.Add(dt2.DefaultView.Table)

    '        dt3 = goData.dtLists

    '        'For Each dr As DataRow In dt.Rows
    '        'dest.ImportRow(dr)
    '        'Next

    '        For i As Integer = SkipRecords To SkipRecords + 1000
    '            If dt3.Rows.Count < i Then Exit For
    '            dest.ImportRow(dt3.Rows(i))
    '        Next
    '        dest.TableName = "Table4"
    '        ds.Tables.Add(ChangeRowVal(ChangeColName(dest, "FileName", "TableName"), "ListName"))

    '        xmlReturn = goData.DataSetToXML(ds, True, "Selltis_Schema")

    '    Catch ex As Exception

    '        ex.Message.ToString()

    '    End Try

    '    Return xmlReturn

    'End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Public Function GetSchemaAsXMLString() As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim goData As clData
        goData = HttpContext.Current.Session("goData")
        goData.TestSchema()
        Dim xmlReturn As New XmlDocument()

        Dim ds As New DataSet
        Dim dt As New DataTable

        Dim sortDirection As String = "DESC"
        Dim sortFormat As String = "{0} {1}"

        Dim dest As DataTable = goData.dtLists.Clone

        'Try

        dt = goData.dtTables
        dt.DefaultView.Sort = String.Format(sortFormat, "Name", sortDirection)
        ds.Tables.Add(dt.DefaultView.Table)

        dt = goData.dtFields
        ds.Tables.Add(dt)

        dt = goData.dtLinks
        dt = ChangeRowVal(ChangeColName(dt, "LinkName", "FQLinkName"), "LinkName")
        dt.DefaultView.Sort = String.Format(sortFormat, "LinkName", sortDirection)
        ds.Tables.Add(dt.DefaultView.Table)

        dt = goData.dtLists
        For Each dr As DataRow In dt.Rows
            dest.ImportRow(dr)

        Next
        dest.TableName = "Table4"
        ds.Tables.Add(ChangeRowVal(ChangeColName(dest, "FileName", "TableName"), "ListName"))

        xmlReturn = goData.DataSetToXML(ds, True, "Selltis_Schema")

        'Catch ex As Exception

        'ex.Message.ToString()

        'End Try

        Return xmlReturn.InnerXml.ToString

    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Function DeleteRecordsByXMLString(ByVal xmlString As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim goData As clData
        Dim selRootNode As XmlNode
        Dim fileNodeName As String
        Dim curFilenode As XmlNode
        Dim gIDNode As XmlNode
        Dim sb As StringBuilder
        Dim goErr As clError

        Dim xNode As New System.Xml.XmlDocument

        xNode.LoadXml(xmlString)

        If ValidateIncomingXml(xNode) = False Then
            Return ErrorXML().InnerXml.ToString
        End If

        goErr = HttpContext.Current.Session("goErr")

        goData = HttpContext.Current.Session("goData")

        selRootNode = xNode.SelectSingleNode("Selltis")

        fileNodeName = selRootNode.FirstChild.Name.ToString

        If goData.IsFileValid(fileNodeName) Then

            For Each curFilenode In selRootNode.ChildNodes

                For Each gIDNode In curFilenode.ChildNodes

                    If DeleteRecordByID(fileNodeName, gIDNode.InnerText) = True Then

                        sb = New StringBuilder
                        sb.Append("Deleted ")
                        sb.Append(gIDNode.InnerText)
                        gIDNode.InnerText = sb.ToString

                    Else

                        Dim GetLastError As String = goErr.GetLastError("MESSAGE")

                        sb = New StringBuilder
                        sb.Append("GID_ID ")
                        sb.Append(gIDNode.InnerText)
                        sb.Append(" ")
                        sb.Append(GetLastError)
                        gIDNode.InnerText = sb.ToString

                    End If

                Next

            Next

        End If

        Return xNode.InnerXml.ToString

    End Function

    '*****************************************************************************

    <WebMethod(EnableSession:=True)>
    Function DeleteRecordsByXML(ByVal xNode As System.Xml.XmlDocument) As System.Xml.XmlDocument

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim goData As clData
        Dim selRootNode As XmlNode
        Dim fileNodeName As String
        Dim curFilenode As XmlNode
        Dim gIDNode As XmlNode
        Dim sb As StringBuilder
        Dim goErr As clError

        If ValidateIncomingXml(xNode) = False Then
            Return ErrorXML()
        End If

        goErr = HttpContext.Current.Session("goErr")

        goData = HttpContext.Current.Session("goData")

        If xNode.FirstChild().Name.ToString = "Selltis" Then

            selRootNode = xNode.FirstChild()

            fileNodeName = selRootNode.FirstChild.Name.ToString

            If goData.IsFileValid(fileNodeName) Then

                For Each curFilenode In selRootNode.ChildNodes

                    For Each gIDNode In curFilenode.ChildNodes

                        If DeleteRecordByID(fileNodeName, gIDNode.InnerText) = True Then

                            sb = New StringBuilder
                            sb.Append("Deleted ")
                            sb.Append(gIDNode.InnerText)
                            gIDNode.InnerText = sb.ToString

                        Else

                            Dim GetLastError As String = goErr.GetLastError("MESSAGE")

                            sb = New StringBuilder
                            sb.Append("GID_ID ")
                            sb.Append(gIDNode.InnerText)
                            sb.Append(" ")
                            sb.Append(GetLastError)
                            gIDNode.InnerText = sb.ToString

                        End If

                    Next

                Next

            End If

        End If

        Return xNode

    End Function


    Function DeleteRecordByID(ByVal sFile As String, ByVal sID As String) As Boolean

        Dim goLog As clLog = HttpContext.Current.Session("goLog")

        Try
            Dim oRS As New clRowSet(sFile, 1, "GID_ID='" & sID & "'")

            If oRS.Count = 1 Then
                If oRS.DeleteRecord() = 0 Then
                    Return False
                Else
                    Return True
                End If
            Else
                goLog.SetWarning(35000, "WS_Rowset::DeleteRecordByID", "Record does not exist")
                Return False
            End If
        Catch ex As Exception
            Return False
        End Try
    End Function


    Sub SetRowsetBypass(ByRef oRS As clRowSet)

        Dim goP As clProject = HttpContext.Current.Session("goP")

        Dim oVar1 As Boolean = CBool(goP.GetVar("bBypassValidation"))
        Select Case oVar1
            Case True
                oRS.bBypassValidation = True
            Case False
                oRS.bBypassValidation = False
        End Select

        Dim oVar2 As Boolean = CBool(goP.GetVar("bNoRecordOnSave"))
        Select Case oVar2
            Case True
                oRS.bNoRecordOnSave = True
            Case False
                oRS.bNoRecordOnSave = False
        End Select

    End Sub

    Private Function ChangeColName(ByVal table As DataTable, ByVal sNewName As String, ByVal sOldName As String) As DataTable

        Dim column As DataColumn

        For Each column In table.Columns

            If column.ColumnName = sOldName Then

                column.ReadOnly = False
                column.ColumnName = sNewName

            End If

        Next

        Return table

    End Function

    Private Function ChangeRowVal(ByVal table As DataTable, ByVal sRowName As String) As DataTable

        Dim row As DataRow

        For Each row In table.Rows

            Select Case sRowName

                Case "LinkName"
                    Dim sCurVal As String = row.Item(sRowName)
                    row.Item(sRowName) = sCurVal.Remove(0, 3)

                Case "ListName"
                    Dim sCurVal As String = row.Item(sRowName)
                    row.Item(sRowName) = "MLS_" + sCurVal

            End Select

        Next

        Return table

    End Function

#Region "XmlValidate Functions"


    Public Function ValidateIncomingXml(ByVal xDoc As System.Xml.XmlDocument) As Boolean

        Dim goData As clData
        Dim fileNode As XmlNode
        Dim fileNodeName As String
        Dim curFilenode As XmlNode
        Dim childNode As XmlNode
        Dim sb As StringBuilder

        goData = HttpContext.Current.Session("goData")

        If xDoc.FirstChild().Name.ToString = "Selltis" Then

            fileNode = xDoc.FirstChild()

            fileNodeName = fileNode.FirstChild.Name.ToString

            If goData.IsFileValid(fileNodeName) Then

                For Each curFilenode In fileNode.ChildNodes

                    For Each childNode In curFilenode.ChildNodes

                        If childNode.Name.ToString <> fileNodeName And childNode.Name.ToString <> "EXT_ID" And childNode.Name.ToString <> "GID_ID" Then

                            If goData.IsFieldValid(fileNodeName, childNode.Name.ToString) = False Then

                                sb = New StringBuilder
                                sb.Append("Field ")
                                sb.Append(childNode.Name)
                                sb.Append(" ")
                                sb.Append(" is not valid")
                                m_sErrorResult = sb.ToString
                                Return False

                            End If

                        End If

                    Next

                Next

            Else

                sb = New StringBuilder
                sb.Append("Table ")
                sb.Append(fileNodeName)
                sb.Append(" ")
                sb.Append(" is not valid")
                m_sErrorResult = sb.ToString
                Return False

            End If

        End If

        Return True

    End Function

    Private schemaValidation As New ValidationEventHandler(AddressOf ValidationHandler)

    Private Function ValidateXmlSchema(ByVal xDoc As System.Xml.XmlDocument, ByVal xmlSchemaName As String) As String

        xDoc.Schemas.Add(GetSchema(xmlSchemaName))
        'Try
        xDoc.Validate(schemaValidation)
        Return "Valid"
        'Catch ex As XmlSchemaValidationException

        'Return ex.Message
        'Catch ex As XmlSchemaException

        'Return ex.Message
        'Catch ex As Exception

        'Return ex.Message
        'End Try

    End Function


    Private Function GetSchema(ByVal filePath As String) As XmlSchema

        Dim schema As XmlSchema

        Using s As New System.IO.FileStream(filePath, FileMode.Open)

            Using reader As New StreamReader(s)

                schema = XmlSchema.Read(reader, Nothing)

            End Using

        End Using

        Return schema

    End Function


    Private Sub ValidationHandler(ByVal sender As Object, ByVal e As System.Xml.Schema.ValidationEventArgs)

        Throw e.Exception

    End Sub


    Public Function ErrorXML() As XmlDocument

        Dim xmlMsg As New StringBuilder()
        xmlMsg.Append("<?xml version=")
        xmlMsg.Append(Convert.ToChar(34))
        xmlMsg.Append("1.0")
        xmlMsg.Append(Convert.ToChar(34))
        xmlMsg.Append(" encoding=")
        xmlMsg.Append(Convert.ToChar(34))
        xmlMsg.Append("utf-8")
        xmlMsg.Append(Convert.ToChar(34))
        xmlMsg.Append("?>")
        xmlMsg.Append("<Selltis>")
        xmlMsg.Append("<Error>")
        xmlMsg.Append(m_sErrorResult)
        xmlMsg.Append("</Error>")
        xmlMsg.Append("</Selltis>")
        Dim xDoc As New System.Xml.XmlDocument

        xDoc.LoadXml(xmlMsg.ToString)

        Return xDoc

    End Function


#End Region

    '*****************************************************************************    
    <WebMethod(EnableSession:=True)>
    Public Function UploadAndProcessDataset_GID(ByVal par_oDataSet As DataSet, ByVal par_sScriptName As String, ByVal par_sReportPage As String, ByRef par_GIDID As String) As Boolean

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim goScr As clScrMngRowSet = New clScrMngRowSet 'As clScrMng = HttpContext.Current.Session("goScr")
        goScr.Initialize()

        Try
            Dim oMeta As clMetaData = HttpContext.Current.Session("goMeta")
            Dim goP As clProject = HttpContext.Current.Session("goP")
            oMeta.LineWrite(goP.GetMe("ID"), par_sReportPage, "STATUS", "PROCESSING")

            Return goScr.RunScript("ProcessDataset", par_oDataSet, "", par_sReportPage, par_sScriptName, "","" , "", par_GIDID)

        Catch ex As Exception
            Return False
        End Try

    End Function



    <WebMethod(EnableSession:=True)> _
    Public Function UploadAndProcessDataset(ByVal par_oDataSet As DataSet, ByVal par_sScriptName As String, ByVal par_sReportPage As String) As Boolean

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Dim goScr As clScrMngRowSet = New clScrMngRowSet 'As clScrMng = HttpContext.Current.Session("goScr")
        goScr.Initialize()

        Try
            Dim oMeta As clMetaData = HttpContext.Current.Session("goMeta")
            Dim goP As clProject = HttpContext.Current.Session("goP")
            oMeta.LineWrite(goP.GetMe("ID"), par_sReportPage, "STATUS", "PROCESSING")

            'Return goScr.RunScript("ProcessDataset", par_oDataSet, , par_sReportPage)
            Return goScr.RunScript("ProcessDataset", par_oDataSet, "", par_sReportPage, par_sScriptName)

        Catch ex As Exception
            Return False
        End Try


    End Function


    <WebMethod(EnableSession:=True)> _
    Public Function UpdateStatus(ByVal par_sReportPage As String, ByVal sStatus As String) As Boolean

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Try
            Dim oMeta As clMetaData = HttpContext.Current.Session("goMeta")
            Dim goP As clProject = HttpContext.Current.Session("goP")

            oMeta.LineWrite(goP.GetMe("ID"), par_sReportPage, "STATUS", sStatus)
            Return True

        Catch ex As Exception
            Return False
        End Try



    End Function

    <WebMethod(EnableSession:=True)> _
    Public Function UpdateS3Status(ByVal par_sReportPage As String, ByVal sStatus As String, ByVal sErrorMessage As String, ByVal sProgress As String, ByVal lBytesTransferred As Long, ByVal lTotalBytes As Long, ByVal sType As String, ByVal sFileLastMod As String) As Boolean

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Try
            Dim oMeta As clMetaData = HttpContext.Current.Session("goMeta")
            Dim goP As clProject = HttpContext.Current.Session("goP")
            Dim goTr As clTransform = HttpContext.Current.Session("goTr")

            Dim sMD As String = oMeta.PageRead(goP.GetMe("ID"), par_sReportPage)
            goTr.StrWrite(sMD, "STATUS", sStatus)
            goTr.StrWrite(sMD, "ERRORMESSAGE", sErrorMessage)
            goTr.StrWrite(sMD, "PROGRESS", sProgress)
            goTr.StrWrite(sMD, "BYTESTRANSFERRED", lBytesTransferred)
            goTr.StrWrite(sMD, "TOTALBYTES", lTotalBytes)
            goTr.StrWrite(sMD, "FILETYPE", sType)
            goTr.StrWrite(sMD, "FILELASTMOD", sFileLastMod)

            oMeta.PageWrite("GLOBAL", par_sReportPage, sMD)

            If sStatus = "COMPLETED" Then
                Dim sID As String = goTr.StrRead(sMD, "DATAFILEID")
                Dim oRS As New clRowSet("DF", clC.SELL_EDIT, "GID_ID='" & sID & "'")


                'Try
                If oRS.GetFieldVal("MLS_STATUS", clC.SELL_SYSTEM) = 4 Then   'Uploading
                    oRS.SetFieldVal("MLS_STATUS", 3, clC.SELL_SYSTEM)
                    oRS.SetFieldVal("DTT_FileModTime", sFileLastMod, clC.SELL_SYSTEM)
                    oRS.SetFieldVal("TXT_FILETYPE", sType, clC.SELL_SYSTEM)
                    oRS.SetFieldVal("BI__FILESIZE", lTotalBytes, clC.SELL_SYSTEM)

                    oRS.Commit()
                End If
                'xxx deal with accounting..

                oMeta.PageDelete("GLOBAL", par_sReportPage)

            End If

            Return True

        Catch ex As Exception

            Try
                Dim goLog As clLog
                goLog = HttpContext.Current.Session("goLog")
                Return goLog.Log("WebService_Rowset::UpdateS3Status", ex.Message)
            Catch ex2 As Exception
                Return False
            End Try

        End Try


    End Function

    <WebMethod(EnableSession:=True)> _
    Public Function GetSystemCodes() As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Try

            Dim goTr As clTransform = HttpContext.Current.Session("goTr")
            Return goTr.GetSystemCodes

        Catch ex As Exception
            Return False
        End Try


    End Function

    <WebMethod(EnableSession:=True)> _
    Public Function GetStandardizedPhoneNumber(ByVal sNumber As String) As String

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Try

            Dim goTr As clTransform = HttpContext.Current.Session("goTr")
            Return goTr.GetStandardizedPhoneNumber(sNumber, "GENERIC")

        Catch ex As Exception
            Return False
        End Try


    End Function

    <WebMethod(EnableSession:=True)> _
    Public Function LogError(ByVal par_sMessage As String, ByVal par_sProcedure As String) As Boolean

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Try

            Dim goLog As clLog = HttpContext.Current.Session("goLog")
            goLog.LogError(par_sMessage, par_sProcedure, False)
            Return True

        Catch ex As Exception
            Return False
        End Try


    End Function

    <WebMethod(EnableSession:=True)> _
    Public Function AddAlert(ByVal par_sMessage As String, _
                            ByVal par_sType As String, _
                            ByVal par_sExecute As String, _
                            ByVal par_sUser As String, _
                            ByVal par_sIcon As String, _
                            ByVal par_sProduct As String, _
                            ByVal par_sTooltip As String) As Boolean

        If Not ValidateSession() Then Err.Raise(35000, "rowset.asmx", "Session is invalid")

        Try

            Dim goUI As clUI = HttpContext.Current.Session("goUI")
            Return goUI.AddAlert(par_sMessage, par_sType, par_sExecute, par_sUser, par_sIcon, par_sProduct, par_sTooltip)

        Catch ex As Exception
            Return False
        End Try


    End Function




End Class

