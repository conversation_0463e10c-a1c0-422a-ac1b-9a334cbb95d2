﻿CREATE TABLE [dbo].[GL_For_FI] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_GL_For_FI_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_GL] UNIQUEIDENTIFIER NOT NULL,
    [GID_FI] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_GL_For_FI] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_FI_In_GL] FOREIGN KEY ([GID_GL]) REFERENCES [dbo].[GL] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_GL_For_FI] FOREIGN KEY ([GID_FI]) REFERENCES [dbo].[FI] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[GL_For_FI] NOCHECK CONSTRAINT [LNK_FI_In_GL];


GO
ALTER TABLE [dbo].[GL_For_FI] NOCHECK CONSTRAINT [LNK_GL_For_FI];


GO
CREATE NONCLUSTERED INDEX [IX_FI_In_GL]
    ON [dbo].[GL_For_FI]([GID_FI] ASC, [GID_GL] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_GL_For_FI]
    ON [dbo].[GL_For_FI]([GID_GL] ASC, [GID_FI] ASC);

