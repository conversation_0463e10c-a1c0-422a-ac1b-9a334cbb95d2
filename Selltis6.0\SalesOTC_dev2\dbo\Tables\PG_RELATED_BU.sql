﻿CREATE TABLE [dbo].[PG_RELATED_BU] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_PG_RELATED_BU_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_PG] UNIQUEIDENTIFIER NOT NULL,
    [GID_BU] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_PG_RELATED_BU] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_BU_CONNECTED_PG] FOREIGN KEY ([GID_PG]) REFERENCES [dbo].[PG] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PG_RELATED_BU] FOREIGN KEY ([GID_BU]) REFERENCES [dbo].[BU] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PG_RELATED_BU] NOCHECK CONSTRAINT [LNK_BU_CONNECTED_PG];


GO
ALTER TABLE [dbo].[PG_RELATED_BU] NOCHECK CONSTRAINT [LNK_PG_RELATED_BU];


GO
CREATE NONCLUSTERED INDEX [IX_PG_RELATED_BU]
    ON [dbo].[PG_RELATED_BU]([GID_PG] ASC, [GID_BU] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_BU_CONNECTED_PG]
    ON [dbo].[PG_RELATED_BU]([GID_BU] ASC, [GID_PG] ASC);

