﻿@model Selltis.MVC.Models.CLAttachments
@using Kendo.Mvc.UI;

@{
    var listItems = new List<SelectListItem>();
    listItems.Add(new SelectListItem { Text = "From PC drive", Value = "1" });
    listItems.Add(new SelectListItem { Text = "From EFileCabinet", Value = "0" });
}

<script>

    var Cancelzilla = document.getElementById('@Model.sField' + "_F");

    //Cancelzilla.onclick = charge

    function charge() {
        //  debugger;
        document.body.onfocus = roar
    }

    function roar() {
        //  debugger;
        if (Cancelzilla.value.length) {
            var dropdownlist = $("#" + 'ADR_IMAGES' + "drpDataType").data("kendoDropDownList");
            var dropdownlist1 = $("#" + 'ADR_DOCUMENTS' + "drpDataType").data("kendoDropDownList");
            dropdownlist.select(0);
            dropdownlist1.select(0);
        }
        else {
            var dropdownlist = $("#" + 'ADR_IMAGES' + "drpDataType").data("kendoDropDownList");
            var dropdownlist1 = $("#" + 'ADR_DOCUMENTS' + "drpDataType").data("kendoDropDownList");
            dropdownlist.select(0);
            dropdownlist1.select(0);
            document.body.onfocus = null
        }
        document.body.onfocus = null

    }

    function onImageUpload(e) {
        showProgress();
        //To return if file is Invalid..J
        var dropdownlist = $("#" + '@Model.sField' + "drpDataType").data("kendoDropDownList");
        dropdownlist.select(0);

        var filename = e.files[0].name;

        if (filename.length > 150) {
            alert('File name length should be less than or equal to 150 characters');
            dropdownlist.select(0);
            hideProgress();
            return;
        }
        //var iChars = "!#$%^&*+=-[]\';,/{}|:<>?~";
        //var iChars = '\";/:*?<>|$%^@@' + "'";
        var iChars = '\";/:*?<>|%' + "'";

        if (filename.indexOf("&amp;") > -1) {
            filename = filename.replace("&amp;", "&");
        }

        for (var i = 0; i < filename.length; i++) {
            if (iChars.indexOf(filename.charAt(i)) != -1) {
                alert("Invalid File name");
                dropdownlist.select(0);
                hideProgress();
                return;
            }
        }
        showProgress();
        $.ajax({
            url: '/CreateForm/CheckFileValidOrNot',
            cache: false,
            data: { FileName: filename, FileSize: e.files[0].size },
            type: 'GET',
            success: function(data)
            {
                if (data == "Invalid file extension")
                {
                    alert("Invalid file extension.");
                    hideProgress();
                    return;
                }
                else if (data == "Invalid file size")
                {
                    alert("Max File size limit (10 MB) exceeded");
                    hideProgress();
                    return;
                }
            },
            error: function(data)
            {
                hideProgress();
                return;
            }
        });
    }



    function onSuccess(e) {
        // debugger;


        if (e.operation == "upload") {
            if (e.response.status == "OK,Image") {
                // alert(" image has been uploaded successfully");
                var sFieldName = $("#sField").val();

                @*$("#Grid_" + '@Model.sField').data('kendoGrid').dataSource.read();
                $("#Grid_" + '@Model.sField').data('kendoGrid').refresh();*@

                $("#Grid_" + '@Model.sField').data('kendoGrid').dataSource.fetch();


                var dropdownlist = $("#" + '@Model.sField' + "drpDataType").data("kendoDropDownList");
                dropdownlist.select(0);
                // return;
            }
            else if (e.response.status == "File with same name is already uploaded") {
                alert("File with same name is already uploaded");
                var dropdownlist = $("#" + '@Model.sField' + "drpDataType").data("kendoDropDownList");
                dropdownlist.select(0);
                hideProgress();
                return;

            }
            else if (e.response.status == "This file is deleted recently so please save the record first and then upload the same file again") {
                alert("This file is deleted recently so please save the record first and then upload the same file again");
                var dropdownlist = $("#" + '@Model.sField' + "drpDataType").data("kendoDropDownList");
                dropdownlist.select(0);
                hideProgress();
                return;

            }
            else if (e.response.status == "Max File size limit (10 MB) exceeded") {
                alert("Max File size limit (10 MB) exceeded");
                var dropdownlist = $("#" + '@Model.sField' + "drpDataType").data("kendoDropDownList");
                dropdownlist.select(0);
                hideProgress();
                return;

            }

            else if (e.response.status == "Max attachments folder size is reached") {
                alert("Max attachments folder size is reached");
                var dropdownlist = $("#" + '@Model.sField' + "drpDataType").data("kendoDropDownList");
                dropdownlist.select(0);
                hideProgress();
                return;
            }
            else if (e.response.status == "ERROR") {
                var dropdownlist = $("#" + '@Model.sField' + "drpDataType").data("kendoDropDownList");
                dropdownlist.select(0);
                hideProgress();
                return;
            }
            else if (e.response.status == "Invalid File name") {
                alert("Invalid File name");
                var dropdownlist = $("#" + '@Model.sField' + "drpDataType").data("kendoDropDownList");
                dropdownlist.select(0);
                hideProgress();
                return;
            }
            else {
                // alert("Please select image");
                @*$("#Grid_" + '@Model.sField').data('kendoGrid').dataSource.read();
                $("#Grid_" + '@Model.sField').data('kendoGrid').refresh();*@
                $("#Grid_" + '@Model.sField').data('kendoGrid').dataSource.fetch();

                var dropdownlist = $("#" + '@Model.sField' + "drpDataType").data("kendoDropDownList");
                dropdownlist.select(0);
                hideProgress();
                return;
            }
        }
        hideProgress();
    }
    var openWindow;
    function OnDataTypeChange(e) {
        //  debugger
        var value = this.value();
        var _FieldName = $("#sField").val();
        if (value == "From PC drive") {
            var fiu = document.getElementById('@Model.sField' + "_F");  //"files"

            //Reset drop down list value to empty(i.e., selected value to 0)..J
            var dropdownlist = $("#" + '@Model.sField' + "drpDataType").data("kendoDropDownList");
            dropdownlist.select(0);

            fiu.click();
        }
        else if (value == "From EFileCabinet") {
            window.open("/CreateForm/LaunchManualProposalWindow?fldname=" + '@Model.sField' + "&FormKey=" + '@Model.FormKey' + "&FormHistoryKey=" + '@Model.FormHistoryKey', "EFile", "width=700,height=526,top=150,left=200,location=no,menubar=no,scrollbars=no,resizable=no,fullscreen=no,overflow:hidden");
        }
        else {
            return;
        }
    }

    function window_close1() {
        //debugger;
        //  $(".k-pager-refresh").trigger('click');
        //$('.k-i-refresh').click();   // After closing click child Efile Window btn click is fired to refresh grids

        //var dropdownlist = $("#" + 'ADR_IMAGES' + "drpDataType").data("kendoDropDownList");
        //var dropdownlist1 = $("#" + 'ADR_DOCUMENTS' + "drpDataType").data("kendoDropDownList");

        var kGrid = $('#Grid_' + '@Model.sField').data("kendoGrid");
        kGrid.dataSource.read();

        var dropdownlist1 = $("#" + '@Model.sField' + "drpDataType").data("kendoDropDownList");
        //dropdownlist.select(0);
        dropdownlist1.select(0);

    }

</script>
<div id="target">
    <table style="border: none; width: auto; border-collapse: collapse; border-spacing: 0">
        <tr style="background-color: #5B97CB; height: 16px !important; padding: 0px; color: white;">
            <td style="vertical-align: middle; font-weight: bold; height: 16px !important;">
                @Html.Label(" Files", new { @id = "lbltitle", @style = "Height=16px;width: 50px;padding-left: 2px;color:#FFFFFF;" })
            </td>

            <td class="ADRControlCss" style="vertical-align: middle; padding: 0px; height: 16px !important;float: right; padding-top: 0px;">
                @(Html.Kendo().DropDownList()
								.Name(Model.sField + "drpDataType")
								.HtmlAttributes(new { style = "width:175px" })
								.SelectedIndex(0)
								 .Value("Add New")
								.BindTo(new List<string>() {
								  " ",
								  "From PC drive"
                                  //P_R Remove "From EFileCabinet" option from Dropdown in  ADR_Attachments
                                  //,"From EFileCabinet"
                                  }).Events(events => events.Change("OnDataTypeChange"))
                )
            </td>
            <td style="width: 1px; height: 1px; overflow: hidden;">
                @*<input id="<EMAIL>" type="file" style="visibility:hidden;width: 1px; height: 1px; overflow: hidden;" onchange="javascript:StoreSelection(this,<EMAIL>);" />*@

                @Html.HiddenFor(model => model.GID_ID, new { id = "GID_ID" })
                @Html.HiddenFor(model => model.sField, new { id = "sField" })
                @Html.HiddenFor(model => model.sValue, new { id = "sValue" })
                @Html.HiddenFor(model => model.sAttachmentSource, new { id = "sAttachmentSource" })
                @Html.HiddenFor(model => model.FileName, new { id = "FileName" })

                @Html.Hidden("hidAttachments")
                @Html.Hidden("hidTempId")
                @Html.Hidden("hidIsSel")
            </td>

        </tr>

        <tr style="background-color:#ffff99; height: 16px !important; padding: 0px; color: #666666;">
            <td colspan="3">
                <div style="width:auto; height:auto" id="dropZoneElement" class="<EMAIL>">
                    <p class="dropImageHereText"><b>Drop files here to upload</b></p>
                </div>
            </td>
        </tr>
        <tr style="overflow:auto">
            <td colspan="3">

                @(Html.Kendo().Grid<Selltis.MVC.Models.ADRGrid>()
               .Name("Grid_" + @Model.sField)
               .Columns(columns =>
               {
                   columns.Template(@<text></text>).ClientTemplate(" #=FileIcon# ").HtmlAttributes(new { title = " #=FileExt# " }).Width(30).Hidden();
                   columns.Template(@<text></text>).ClientTemplate(" #=FileName# ");
                   columns.Template(@<text></text>).ClientTemplate("<i class=\"k-i-refresh\"></i> ").Hidden();
                   columns.Template(@<text></text>).ClientTemplate(" #=DownloadFile# ").HtmlAttributes(new { title = "Download" }).Width(30).Hidden();
                   columns.Template(@<text></text>).ClientTemplate("<a id=" + @Model.sField + " onclick=\"onRemoveClick(NameofFile='#=NameofFile#',sViewName ='#=sViewName#',GID='#=GID#',FieldValue='#=FieldValue#');\" id=\"imgdelete\" style=\"height:16px;width:16px;cursor: pointer;\"><span class=\"fas fa-trash\"></span></a>").HtmlAttributes(new { title = "Delete" }).Width(30);
               })
                        .Scrollable(scr => scr.Height("100px"))
                        .AutoBind(true)
                        .DataSource(dataSource => dataSource
                        .Ajax()
                        .PageSize(50)
                        .ServerOperation(true)
                        .Read(read => read.Action("GetAttachmentsData", "CreateForm", new { Attachment = "", sFieldName = Model.sField, GID_ID = Model.GID_ID, FileName = Model.FileName, FormKey = Model.FormKey, FormHistoryKey = Model.FormHistoryKey })))
                )

                <button style="visibility:hidden;display:none" id="btn2" onclick="window_close1();"></button>

            </td>
            <td id="AssetTypehide">
                @*style="visibility:hidden;display:none"*@
                <div style="display:none">
                    @(Html.Kendo().Upload()
                   .Name(@Model.sField + "_F")  //"files"
                   .HtmlAttributes(new { name = "files" })
                   .Async(a => a.Save("SaveImages", "CreateForm", new { FieldName = Model.sField, FormKey = Model.FormKey, FormHistoryKey = Model.FormHistoryKey }))
                        //.Async(a => a.Save("SaveFilesToAzureCloudTemp", "CreateForm", new { FieldName = Model.sField, FormKey = Model.FormKey, FormHistoryKey = Model.FormHistoryKey }))
                   .Events(events => events.Success("onSuccess").Upload("onImageUpload"))  //.Select("onSelect").Cancel("onCancel").Upload("onImageUpload")
                   .Multiple(false)
                   .DropZone(".dropZoneElement_" + @Model.sField)
                )
                </div>
            </td>
        </tr>


    </table>
</div>
<style>
    .k-grid-pager {
        height: 0px;
    }

    .k-calendar .k-alt, .k-calendar th, .k-dropzone-hovered, .k-footer-template td, .k-grid-footer, .k-group-footer td, .k-grouping-header, .k-pager-wrap, .k-toolbar, .k-widget .k-status, .k-widget {
        background-color: #ffff99 !important;
        color: #666666 !important;
    }

    .dropImageHereText {
        height: 40px;
        line-height: 14px;
        margin: 0px;
        vertical-align: middle;
        padding: 10px;
        border-right: 1px solid #CBCBCB;
        border-left: 1px solid #CBCBCB;
    }
</style>

<script type="text/javascript">


    $(document).ready(function () {
        //debugger
        $("#AssetTypehide").hide();

        $(".k-grid .k-grid-header").hide();
        $(".k-grid .k-grid-footer").hide();
        $("#Grid_" + '@Model.sField').removeAttr("style");


        var File_Gid_id = $("#GID_ID").val();
        var FldName = $("#sField").val();
        var FileName = $("#FileName").val();
        var sAttachmentSource = $("#sAttachmentSource").val();


    });

    function onRemoveClick(NameofFile, sViewName, sGID, FieldValue) {
        //debugger;
        if (confirm("Are you sure you want to delete the selected attachment ?")) {
            showProgress();
            $.ajax({
                url: "/CreateForm/DeleteAttachments",
                //url: "/CreateForm/DeleteAttachmentsAzure",
                type: "GET",
                //  url: '@Url.Action("CreateForm", "DeleteAttachments")',
                data: { NameofFile: NameofFile, sViewName: sViewName, sGID: sGID, FieldValue: FieldValue },
                success: function (data) {
                    //debugger;
                    // alert("success");

                    //$("#Grid_" + FieldValue).data('kendoGrid').dataSource.read();
                    //$("#Grid_" + FieldValue).data('kendoGrid').refresh();

                    $("#Grid_" + FieldValue).data('kendoGrid').dataSource.fetch();

                    hideProgress();
                },
                error: function (data) {
                    //debugger;
                    alert("Fail");

                }
            })
        }
        else {
            return;
        }
    }


</script>