﻿CREATE TABLE [dbo].[MO_Competing_VE] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Model_Competing_Vendor_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_MO] UNIQUEIDENTIFIER NOT NULL,
    [GID_VE] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MO_Competing_VE] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_MO_Competing_VE] FOREIGN KEY ([GID_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_VE_CompetitorTo_MO] FOREIGN KEY ([GID_MO]) REFERENCES [dbo].[MO] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MO_Competing_VE] NOCHECK CONSTRAINT [LNK_MO_Competing_VE];


GO
ALTER TABLE [dbo].[MO_Competing_VE] NOCHECK CONSTRAINT [LNK_VE_CompetitorTo_MO];


GO
CREATE CLUSTERED INDEX [IX_MO_Competing_VE]
    ON [dbo].[MO_Competing_VE]([GID_VE] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_VE_CompetitorTo_MO]
    ON [dbo].[MO_Competing_VE]([GID_MO] ASC);

