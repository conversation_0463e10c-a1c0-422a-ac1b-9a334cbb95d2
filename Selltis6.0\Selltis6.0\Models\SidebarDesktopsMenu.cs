﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Text;
using System.IO;
using Microsoft.VisualBasic;
using Selltis.BusinessLogic;
using Selltis.Core;
using System.Web.Mvc;
using Kendo.Mvc.UI;
namespace Selltis.MVC.Models
{
    public class SidebarDesktopsMenu
    {
        private clTransform goTR;
        private clMetaData goMeta;
        private clData goData;
        private clProject goP;
        string gsType = string.Empty;
        public SidebarDesktopsMenu()
        {
            goTR = (clTransform)Util.GetInstance("tr");
            goMeta = (clMetaData)Util.GetInstance("meta");
            goData = (clData)Util.GetInstance("data");
        }
        public HtmlString GetMenu(string DesktopId)
        {
            //try
            //{
                DataSet ds = new DataSet();
                //ds = GetMenuDataSet();

                DataTable dt = new DataTable();
                //dt = GetMainMenuFromDB();
                dt = GetSidebarDT();

                ds.Tables.Add(dt);

                StringBuilder menu = new StringBuilder();
                List<string> MenuNodes = new List<string>();

                for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
                {
                    if (ds.Tables[0].Rows[i]["Parent"].ToString() == "NAV")
                    {
                        MenuNodes.Add(ds.Tables[0].Rows[i]["name"].ToString());
                    }
                }

                menu.Append("<ul class=\"nav sidebar-menu\">");
                foreach (var node in MenuNodes)
                {
                    menu.Append("<li class=\"sidebar-label pt10\">" + node + "</li>");
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        string parent = row["Parent"].ToString().ToLower();
                        string iconname = row["iconname"].ToString();
                        string name = row["name"].ToString();
                        //string wcbid = row["wcbid"].ToString();
                        string wcdid = row["wcdid"].ToString();
                        string desktopid = row["desktopid"].ToString();
                        string menutype = row["menutype"].ToString();
                        desktopid = HttpUtility.UrlDecode(desktopid);
                        wcdid = HttpUtility.UrlDecode(wcdid);
                        if (parent == node.ToLower())
                        {
                            if (menutype.ToLower() == "desktoplink")
                            {
                                menu.Append("<li><a style=\"cursor:pointer\" onclick=\"gotoWCB('" + desktopid + "','" + wcdid + "')\"><span class=\"fa fa-" + iconname + "\"></span> <span class=\"sidebar-title\">" + name + "</span></a></li>");
                            }
                            else if (menutype.ToLower() == "desktop")
                            {
                                menu.Append("<li><a style=\"cursor:pointer\" onclick=\"gotoDsk('" + desktopid + "')\"><span class=\"fa fa fa-building-o" + iconname + "\"></span> <span class=\"sidebar-title\">" + name + "</span></a></li>");
                            }
                            else
                            {
                                menu.Append("<li><a style=\"cursor:pointer\" onclick=\"onOpenLinkDesktop('" + desktopid + "')\"><span class=\"fa fa-external-link" + iconname + "\"></span> <span class=\"sidebar-title\">" + name + "</span></a></li>");
                            }
                        }

                    }
                }
                menu.Append("</ul>");

                HtmlString menubar = new HtmlString(menu.ToString());

                return menubar;
            //}
            //catch (Exception e)
            //{
            //    return new HtmlString(e.Message);
            //}
        }

        public HtmlString GetMenu1()
        {
            //try
            //{
                goTR = (clTransform)Util.GetInstance("tr");
                goMeta = (clMetaData)Util.GetInstance("meta");
                goP = (clProject)Util.GetInstance("p");
                StringBuilder sMenu = new StringBuilder();
                //string sMenuMeta = GetMenuManagerMeataData();

                if(Util.GetSessionValue("LeftMenuHtmlString")!=null)
                {
                    return (HtmlString)Util.GetSessionValue("LeftMenuHtmlString");
                }
                else
                {
                    //GetMenuMetaData_FromDS()  
                    DataSet MenuDS = new DataSet();
                    MenuDS = Util.GetMenuDataSet();
                    string sMenuMeta = Util.GetGlobalMenuMetaData_FromDS(MenuDS);
                    //string sssMenuMeta = Util.GetFavoritesMenuMetaData_FromDS(MenuDS);
                    //string ssMenuMeta = Util.GetPOP_MENU_OPTIONS_Meta(MenuDS);
                    string menu = goTR.StrRead(sMenuMeta, "RootNode");
                    string[] AllRoots = menu.Split(',');
                    if (AllRoots.Length > 0)
                    {
                        string sPOP_MenuMeta = Util.GetFavoritesMenuMetaData_FromDS(MenuDS);
                        sMenuMeta = sMenuMeta + "\r\nDNODE_" + AllRoots[0] + "_Favorites" + "=fa fa-heart,Menu|" + sPOP_MenuMeta.Replace("USERFAVORITES=", "");

                        string sNodeMeta = goTR.StrRead(sMenuMeta, "Root_" + AllRoots[0]);

                        if (!string.IsNullOrEmpty(sNodeMeta))
                        {
                            //goTR.StrWrite(ref sMenuMeta, "Root_" + AllRoots[0], sNodeMeta + ",Favorites");
                            goTR.StrWrite(ref sMenuMeta, "Root_" + AllRoots[0], "Favorites," + sNodeMeta);      //SB Favourites folder should be top of all Nodes
                        }
                        else
                            goTR.StrWrite(ref sMenuMeta, "Root_" + AllRoots[0], "Favorites");
                    }

                    string sRootNodeMeta = goTR.StrRead(sMenuMeta, "RootNode");

                    if (string.IsNullOrEmpty(sRootNodeMeta) == false)
                    {
                        string[] rootNodes = sRootNodeMeta.Split(',');

                        sMenu.Append("<ul id=\"menubar\" class=\"nav sidebar-menu\">");
                        //sMenu.Append("<ul id=\"menubar\" class=\"nav sidebar-menu\">");

                        foreach (string _rootnode in rootNodes)
                        {
                            sMenu.Append("<li class=\"sidebar-label pt10\">" + _rootnode + "</li>");

                            string sDNodeMeta = goTR.StrRead(sMenuMeta, "Root_" + _rootnode);

                            if (string.IsNullOrEmpty(sDNodeMeta) == false) //desktop folder
                            {
                                string[] folderNodes = sDNodeMeta.Split(',');

                                foreach (string _foldernode in folderNodes)
                                {
                                    string sFolderMetaData = goTR.StrRead(sMenuMeta, "DNode_" + _rootnode + "_" + _foldernode);

                                    //string[] afolders = sFolderMetaData.Split('|');
                                    //string sfolderIcon = afolders.GetValue(0).ToString();
                                    //if (sfolderIcon.Contains(","))
                                    //{                                    
                                    //    sfolderIcon = sfolderIcon.Split(',')[0].ToString();
                                    //}

                                    string IsVisible = GetFolderIsVisibleInfo(MenuDS, _foldernode, _rootnode);
                                    if (IsVisible == "False")
                                    {
                                        continue;
                                    }
                                    //Hiding menus folders when they are selected to hide.. from Personal Options Nodes..J/S1
                                    string HidenFoldersData = Util.GetPersonalOptionsValue("MENUFOLDERSHIDDEN");
                                    if (HidenFoldersData.Contains(_foldernode))
                                    {
                                        continue;
                                    }

                                    if (!string.IsNullOrEmpty(sFolderMetaData))
                                    {
                                        string[] folders = sFolderMetaData.Split('|');

                                        string folderLevel = "";
                                        string folderIcon = folders.GetValue(0).ToString();

                                        if (folderIcon.Contains(","))
                                        {
                                            folderLevel = folderIcon.Split(',')[1].ToString();
                                            if (folderLevel.ToUpper() == "MENU")
                                            {
                                                //sFolderMetaData = Util.GetFolderMetaInvolvesPOP(_rootnode, _foldernode, sFolderMetaData);
                                            }
                                        }
                                    }
                                    //Get Icon
                                    if (!string.IsNullOrEmpty(sFolderMetaData))
                                    {
                                        string[] folders = sFolderMetaData.Split('|');

                                        string folderLevel = "";
                                        string folderIcon = folders.GetValue(0).ToString();

                                        if (folderIcon.Contains(","))
                                        {
                                            folderLevel = folderIcon.Split(',')[1].ToString();
                                            folderIcon = folderIcon.Split(',')[0].ToString();
                                        }

                                        if (folders.Length > 1)
                                        {
                                            if (folderLevel == "Menu")
                                            {
                                                string sDesktops = folders.GetValue(1).ToString();
                                                sMenu.Append("<li>");
                                                //sMenu.Append("<a class=\"accordion-toggle\" style=\"cursor:pointer\" ><span class=\"" + folderIcon + "\"></span> <span class=\"sidebar-title\">" + _foldernode + "</span>  <span class=\"caret\"></span> </a> ");
                                                sMenu.Append("<a id=\"" + _foldernode.Replace(" ", "") + "\" class=\"accordion-toggle\" style=\"cursor:pointer\" ><span class=\"" + folderIcon + "\"></span> <span class=\"sidebar-title\">" + _foldernode + "</span>  <span class=\"caret\"></span> </a> ");
                                                sMenu.Append("<ul class=\"nav sub-nav\">");
                                                List<string> GlobalDesktopIds = new List<string>();
                                                if (string.IsNullOrEmpty(sDesktops) == false)
                                                {
                                                    string[] ArrayDesktops = sDesktops.Split(',');

                                                    foreach (string _desktop in ArrayDesktops)
                                                    {
                                                        string[] ArraydesktopInfo = _desktop.Split('~');
                                                        string desktopName = ArraydesktopInfo.GetValue(0).ToString();
                                                        string desktopId = ArraydesktopInfo.GetValue(1).ToString();
                                                        GlobalDesktopIds.Add(desktopId);
                                                        string section = "";
                                                        if (ArraydesktopInfo.Length > 2)
                                                        {
                                                            section = ArraydesktopInfo.GetValue(2).ToString();
                                                        }
                                                        if (_foldernode.ToUpper() == "TEST DESKTOPS")
                                                            sMenu.Append("<li><a style=\"cursor:pointer\" id=\"" + desktopId.Replace(" ", "") + "\"  onclick=\"gotoDesktop('" + desktopId + "','" + _rootnode + "','" + _foldernode + "','" + section + "'+'true')\"><span >" + desktopName + "</span></a></li>");
                                                        else
                                                            sMenu.Append("<li><a style=\"cursor:pointer\" id=\"" + desktopId.Replace(" ", "") + "\"  onclick=\"gotoDesktop('" + desktopId + "','" + _rootnode + "','" + _foldernode + "','" + section + "')\"><span >" + desktopName + "</span></a></li>");
                                                    }
                                                }
                                                string POP_MetaData = Util.GetPOP_MENU_OPTIONS_Meta_ByFolderId(MenuDS, _foldernode);

                                                if (string.IsNullOrEmpty(POP_MetaData) == false)
                                                {
                                                    string[] ArrayDesktops = POP_MetaData.Split(',');
                                                    foreach (string _desktop in ArrayDesktops)
                                                    {
                                                        string[] ArraydesktopInfo = _desktop.Split('~');
                                                        string desktopName = ArraydesktopInfo.GetValue(0).ToString();
                                                        string desktopId = ArraydesktopInfo.GetValue(1).ToString();
                                                        string section = ArraydesktopInfo.GetValue(2).ToString();

                                                        string desktop = GlobalDesktopIds.Find(m => m == desktopId);
                                                        if (string.IsNullOrEmpty(desktop))
                                                        {
                                                            if (_foldernode.ToUpper() == "TEST DESKTOPS")
                                                                sMenu.Append("<li><a style=\"cursor:pointer\" id=\"" + desktopId.Replace(" ", "") + "\"  onclick=\"gotoDesktop('" + desktopId + "','" + _rootnode + "','" + _foldernode + "','" + section + "'+'true')\"><span >" + desktopName + "</span></a></li>");
                                                            else
                                                                sMenu.Append("<li><a style=\"cursor:pointer\" id=\"" + desktopId.Replace(" ", "") + "\"  onclick=\"gotoDesktop('" + desktopId + "','" + _rootnode + "','" + _foldernode + "','" + section + "')\"><span >" + desktopName + "</span></a></li>");
                                                        }
                                                    }
                                                }
                                                GlobalDesktopIds = null;
                                                sMenu.Append("</ul>");
                                                
                                                sMenu.Append("</li>");

                                            }
                                            else
                                            {
                                                string sDesktops = folders.GetValue(1).ToString();
                                                sMenu.Append("<li><a style=\"cursor:pointer\" onclick=\"gotoDesktop('" + sDesktops + "','" + _rootnode + "','" + _foldernode + "')\"><span class=\"" + folderIcon + "\"></span> <span class=\"sidebar-title\">" + _foldernode + "</span></a></li>");
                                            }

                                        }
                                        else if (folders.Length == 1)
                                        {
                                            if (folderLevel == "Menu")
                                            {
                                                sMenu.Append("<li>");
                                                sMenu.Append("<a class=\"accordion-toggle\" style=\"cursor:pointer\" ><span class=\"" + folderIcon + "\"></span> <span class=\"sidebar-title\">" + _foldernode + "</span>  <span class=\"caret\"></span> </a> ");
                                                sMenu.Append("<ul class=\"nav sub-nav\">");
                                                string POP_MetaData = Util.GetPOP_MENU_OPTIONS_Meta_ByFolderId(MenuDS, _foldernode);

                                                if (string.IsNullOrEmpty(POP_MetaData) == false)
                                                {
                                                    string[] ArrayDesktops = POP_MetaData.Split(',');
                                                    foreach (string _desktop in ArrayDesktops)
                                                    {
                                                        string[] ArraydesktopInfo = _desktop.Split('~');
                                                        string desktopName = ArraydesktopInfo.GetValue(0).ToString();
                                                        string desktopId = ArraydesktopInfo.GetValue(1).ToString();
                                                        string section = ArraydesktopInfo.GetValue(2).ToString();

                                                        if (_foldernode.ToUpper() == "TEST DESKTOPS")
                                                            sMenu.Append("<li><a style=\"cursor:pointer\" id=\"" + desktopId.Replace(" ", "") + "\"  onclick=\"gotoDesktop('" + desktopId + "','" + _rootnode + "','" + _foldernode + "','" + section + "','true')\"><span class=\"sidebar-title\">" + desktopName + "</span></a></li>");
                                                        else
                                                            sMenu.Append("<li><a style=\"cursor:pointer\" id=\"" + desktopId.Replace(" ", "") + "\"  onclick=\"gotoDesktop('" + desktopId + "','" + _rootnode + "','" + _foldernode + "','" + section + "')\"><span class=\"sidebar-title\">" + desktopName + "</span></a></li>");

                                                    }
                                                }

                                                sMenu.Append("</ul>");
                                                sMenu.Append("</li>");
                                            }
                                            else
                                            {
                                                string POP_MetaData = Util.GetPOP_MENU_OPTIONS_Meta_ByFolderId(MenuDS, _foldernode);

                                                if (string.IsNullOrEmpty(POP_MetaData) == false)
                                                {
                                                    sMenu.Append("<li><a style=\"cursor:pointer\" onclick=\"gotoDesktop('" + POP_MetaData + "','" + _rootnode + "','" + _foldernode + "')\"><span class=\"" + folderIcon + "\"></span> <span class=\"sidebar-title\">" + _foldernode + "</span></a></li>");
                                                }
                                                else
                                                {
                                                    sMenu.Append("<li><a style=\"cursor:pointer\"><span class=\"" + folderIcon + "\"></span> <span class=\"sidebar-title\">" + _foldernode + "</span></a></li>");
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            string sLNodeMeta = goTR.StrRead(sMenuMeta, "LNode_" + _rootnode);

                            if (string.IsNullOrEmpty(sLNodeMeta) == false)
                            {
                                string[] ArrayLNodes = sLNodeMeta.Split('|');

                                foreach (string _linknode in ArrayLNodes)
                                {
                                    string[] sLinkNodeInfo = _linknode.Split(',');

                                    string sLinkName = sLinkNodeInfo.GetValue(0).ToString();
                                    string sIcon = sLinkNodeInfo.GetValue(1).ToString();
                                    string sPopup = sLinkNodeInfo.GetValue(2).ToString();
                                    string sURL = sLinkNodeInfo.GetValue(3).ToString();

                                    sMenu.Append("<li><a style=\"cursor:pointer\" onclick=\"gotoLink('" + sURL + "','" + sPopup + "')\"><span class=\"" + sIcon + "\"></span> <span class=\"sidebar-title\">" + sLinkName + "</span></a></li>");

                                }
                            }

                        }

                        sMenu.Append("</ul>");
                    }

                    HtmlString menubar = new HtmlString(sMenu.ToString());

                    Util.SetSessionValue("LeftMenuHtmlString", menubar);

                    return menubar;
                }

                
            //}
            //catch (Exception e)
            //{
            //    return new HtmlString(e.Message);
            //}
        }

        private static string GetFolderIsVisibleInfo(DataSet _MenuDataSet, string FolderNodeName, string RootNodeName)
        {
            DataView _DvDesktopView = new DataView(_MenuDataSet.Tables[0], "ItemType='FOLDER' and ItemName='" + FolderNodeName + "'and ParentItemName='" + RootNodeName + "' and MenuType='GLOBAL' and IsDeleted='False'", "DisplayOrder", DataViewRowState.CurrentRows);
            DataTable _DtDesktop = _DvDesktopView.ToTable();

            string LoginGroupInfo = "";

            if (_DtDesktop.Rows.Count > 0)
            {
                foreach (DataRow _row in _DtDesktop.Rows)
                {
                    LoginGroupInfo = _row["IsVisible"].ToString();
                }
            }

            return LoginGroupInfo;
        }


        public System.Data.SqlClient.SqlDataReader CommonToGetFoldersList()
        {
            goData = (clData)Util.GetInstance("data");
            System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
            System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
            System.Data.SqlClient.SqlDataReader oReader = null;

            oCommand.CommandText = "[pGetWebClientFolders]";
            oCommand.CommandType = System.Data.CommandType.StoredProcedure;
            oCommand.Connection = oConnection;

            System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
            retValParam.Direction = ParameterDirection.ReturnValue;
            oCommand.Parameters.Add(retValParam);

            oReader = oCommand.ExecuteReader();

            return oReader;
        }
        public System.Data.SqlClient.SqlDataReader CommonToGetDnodesList(string FolderId)
        {
            goData = (clData)Util.GetInstance("data");
            System.Data.SqlClient.SqlConnection oConnection = goData.GetConnection();
            System.Data.SqlClient.SqlCommand oCommand = new System.Data.SqlClient.SqlCommand();
            System.Data.SqlClient.SqlDataReader oReader = null;

            oCommand.CommandText = "pGetWebClientDNode";
            oCommand.CommandType = System.Data.CommandType.StoredProcedure;
            oCommand.Connection = oConnection;

            System.Data.SqlClient.SqlParameter strWCFID = new System.Data.SqlClient.SqlParameter("@par_sWCFID", SqlDbType.VarChar);
            strWCFID.Value = FolderId;
            oCommand.Parameters.Add(strWCFID);

            System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", SqlDbType.Int);
            retValParam.Direction = ParameterDirection.ReturnValue;
            oCommand.Parameters.Add(retValParam);

            oReader = oCommand.ExecuteReader();

            return oReader;
        }

        public DataTable GetMainMenuFromDB()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("name");
            dt.Columns.Add("Parent");
            dt.Columns.Add("IconName");
            dt.Columns.Add("desktopid");
            dt.Columns.Add("menutype");
            dt.Columns.Add("wcfid");
            dt.Columns.Add("wcdid");
            List<SelectListItem> FoldersNodeList = new List<SelectListItem>();
            System.Data.SqlClient.SqlDataReader oReader = CommonToGetFoldersList();
            while (oReader.Read())
            {
                if (!string.IsNullOrEmpty(oReader["value"].ToString()))
                {
                    FoldersNodeList.Add(new SelectListItem
                    {
                        Text = oReader["value"].ToString(),
                        Value = oReader["page"].ToString()
                    });
                    dt.Rows.Add(oReader["value"].ToString(), "NAV", "", "", "", oReader["page"].ToString(), "");
                }
            }

            foreach (SelectListItem item in FoldersNodeList)
            {
                System.Data.SqlClient.SqlDataReader DnodeReader = CommonToGetDnodesList(item.Value);
                while (DnodeReader.Read())
                {
                    if (!string.IsNullOrEmpty(DnodeReader["value"].ToString()))
                    {
                        if (DnodeReader["page"].ToString().IndexOf("DSK_") > -1)
                        {
                            string type = IsDesktopLink(DnodeReader["page"].ToString());

                            if (type == "link")
                            {
                                dt.Rows.Add(DnodeReader["value"].ToString(), item.Text, DnodeReader["icon"].ToString(), DnodeReader["page"].ToString(), "link", "", "");
                            }
                            else
                            {
                                dt.Rows.Add(DnodeReader["value"].ToString(), item.Text, DnodeReader["icon"].ToString(), DnodeReader["page"].ToString(), "desktop", "", "");
                            }

                        }
                        else
                        {
                            dt.Rows.Add(DnodeReader["value"].ToString(), item.Text, DnodeReader["icon"].ToString(), "", "desktoplink", "", DnodeReader["page"].ToString());
                        }
                    }
                }
            }


            return dt;
        }

        public DataTable GetSidebarDT()
        {
            List<TreeViewItemModel> _model = new List<TreeViewItemModel>();
            //_model = GetMenu_TreeView();

            DataTable dt = new DataTable();
            dt.Columns.Add("name");
            dt.Columns.Add("Parent");
            dt.Columns.Add("IconName");
            dt.Columns.Add("desktopid");
            dt.Columns.Add("type");
            GetMenu_TreeView(ref dt);


            return new DataTable();
        }

        //side bar begin
        private string GetMenuManagerMeataData()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            return goMeta.PageRead("GLOBAL", "WOP_MENU_OPTIONS", "");
        }
        private List<TreeViewItemModel> GetMenu_TreeView(ref DataTable dt)
        {
            try
            {
                goTR = (clTransform)Util.GetInstance("tr");
                goMeta = (clMetaData)Util.GetInstance("meta");

                string sMenuMeta = GetMenuManagerMeataData();
                //Get All root nodes
                string sRootNodeMeta = goTR.StrRead(sMenuMeta, "RootNode");

                List<TreeViewItemModel> lstMenuItems = new List<TreeViewItemModel>();

                if (string.IsNullOrEmpty(sRootNodeMeta) == false)
                {
                    string[] rootNodes = sRootNodeMeta.Split(',');
                    //Loop root nodes
                    foreach (string _rootnode in rootNodes)
                    {

                        dt.Rows.Add(_rootnode, "NAV", "", "", "", "");

                        string foldersData = goTR.StrRead(sMenuMeta, "ROOT_" + _rootnode);

                        if (string.IsNullOrEmpty(foldersData) == false)
                        {
                            string[] folderNodes = foldersData.Split(',');

                            foreach (string _folder in folderNodes)
                            {
                                string DesktopData = goTR.StrRead(sMenuMeta, "DNODE_" + _rootnode + "_" + _folder);

                                string[] desktopNodes = DesktopData.Split('|');

                                if (desktopNodes.Length <= 1)
                                    dt.Rows.Add(_folder, _rootnode, desktopNodes[0], "", "desktop", "");
                                else
                                    dt.Rows.Add(_folder, _rootnode, desktopNodes[0], desktopNodes[1], "desktop", "");
                            }
                        }

                        else
                        {
                            string linksData = goTR.StrRead(sMenuMeta, "LNODE_" + _rootnode);
                            string[] _LinksData = linksData.Split('|');

                            foreach (string _link in _LinksData)
                            {
                                string[] _linkData = _link.Split(',');
                                dt.Rows.Add(_linkData[0], _rootnode, _linkData[1], "", "link", _linkData[3]);
                            }
                        }
                    }

                    return lstMenuItems;

                }
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        private List<TreeViewItemModel> GetFolders(string sRootNode)
        {
            //Root_MENU
            goTR = (clTransform)Util.GetInstance("tr");
            goMeta = (clMetaData)Util.GetInstance("meta");

            string sMenuMeta = GetMenuManagerMeataData();
            //Get All root node folders
            string sRootNodeMeta = goTR.StrRead(sMenuMeta, "Root_" + sRootNode);

            List<TreeViewItemModel> lstRootFolderItems = new List<TreeViewItemModel>();

            if (string.IsNullOrEmpty(sRootNodeMeta) == false) //desktop folder
            {
                string[] folderNodes = sRootNodeMeta.Split(',');
                //Loop root nodes
                foreach (string _foldernode in folderNodes)
                {
                    TreeViewItemModel _folderMenuItem = new TreeViewItemModel();
                    _folderMenuItem.Text = _foldernode;
                    _folderMenuItem.Id = "WCD_" + Guid.NewGuid();
                    _folderMenuItem.Expanded = true;

                    //DNode_DATA_Companies
                    string sFolderMetaData = goTR.StrRead(sMenuMeta, "DNode_" + sRootNode + "_" + _foldernode);

                    //Get Icon
                    string[] folders = sFolderMetaData.Split('|');
                    string folderIcon = folders.GetValue(0).ToString();
                    _folderMenuItem.SpriteCssClass = (folderIcon);

                    if (folders.Length > 1)
                    {
                        string sDesktops = folders.GetValue(1).ToString();
                        string[] ArrayDesktops = sDesktops.Split(',');

                        foreach (string _desktop in ArrayDesktops)
                        {
                            TreeViewItemModel _desktopMenuItem = new TreeViewItemModel();
                            string[] ArraydesktopInfo = _desktop.Split('~');
                            _desktopMenuItem.Text = ArraydesktopInfo.GetValue(0).ToString();
                            _desktopMenuItem.Id = ArraydesktopInfo.GetValue(1).ToString();
                            _folderMenuItem.Items.Add(_desktopMenuItem);
                        }
                    }

                    lstRootFolderItems.Add(_folderMenuItem);

                }

            }
            else //Link
            {
                //LNode_CONFIGURATION = Downloads,fa-Icon,0,/Pages/Downloads|Manage Desktop,fa-Icon,0,ManageDesktops/OpenManageDesktops
                string sLNodeMeta = goTR.StrRead(sMenuMeta, "LNode_" + sRootNode);
                if (string.IsNullOrEmpty(sLNodeMeta) == false)
                {
                    string[] ArrayLNodes = sLNodeMeta.Split('|');

                    foreach (string _linknode in ArrayLNodes)
                    {
                        string[] sLinkNodeInfo = _linknode.Split(',');
                        TreeViewItemModel _linkMenuItem = new TreeViewItemModel();

                        _linkMenuItem.Text = sLinkNodeInfo.GetValue(0).ToString();
                        _linkMenuItem.Id = "LNK_" + Guid.NewGuid().ToString();
                        lstRootFolderItems.Add(_linkMenuItem);
                    }
                }

            }

            return lstRootFolderItems;


        }
        //side bar end

        public string IsDesktopLink(string DesktopId)
        {
            goTR = (clTransform)Util.GetInstance("tr");
            //Desktop _desktop = new Desktop("GLOBAL", DesktopId, false);
            string DesktopMetaData = Util.GeDesktopMetaData("GLOBAL", DesktopId);
            string name = goTR.StrRead(DesktopMetaData, "US_NAME", "", false);
            string url = goTR.StrRead(DesktopMetaData, "US_URL", "", false);
            string showpopup = goTR.StrRead(DesktopMetaData, "US_SHOWPOPUP", "", false);
            string result = "";

            if (string.IsNullOrEmpty(url) && string.IsNullOrEmpty(showpopup))
            {
                result = "desktop";
            }
            else
            {
                result = "link";
            }

            return result;
        }

        public static DataSet GetMenuDataSet()
        {
            string myXMLfile = HttpContext.Current.Server.MapPath("~/App_Data/SidebarDesktopsMenu.xml");
            DataSet ds = new DataSet();
            var fsReadXml = new FileStream(myXMLfile, FileMode.Open, FileAccess.Read);
            ds.ReadXml(fsReadXml);
            fsReadXml.Close();
            fsReadXml.Dispose();
            return ds;
        }

        public HtmlString GetAddNewMenu()
        {
            goP = (clProject)Util.GetInstance("p");

            if (Util.GetItemFromCache(goP.GetUserTID()+"_sAddNewMenu") != null)
            {
                return new HtmlString(Util.GetItemFromCache(goP.GetUserTID() + "_sAddNewMenu").ToString());
            }

            gsType = "CRU";
            StringBuilder menu = new StringBuilder();
            string[] aCRLExclude;
            string[] aCRUExclude;
            string sFile = "";
            string sLabel = "";
            string sPage = goMeta.PageRead("GLOBAL", "OTH_CREATEMENUS", "", true);
            aCRLExclude = goTR.StrRead(sPage, "CRLEXCLUDE", "", false).Split(',');
            aCRUExclude = goTR.StrRead(sPage, "CRUEXCLUDE", "", false).Split(',');
            int n = 4;
            menu.Append("<ul>");

            for (int i = 1; i <= goTR.StringToNum(goTR.StrRead(sPage, "MCOUNT", "0", false), "0", ref n, ""); i++)
            {
                string sPos = "M" + goTR.Pad(i.ToString(), 3, "0", "L");
                string sTarget = goTR.StrRead(sPage, sPos, "", false);
                if (gsType.ToUpper() == "CRL")
                {
                    if (Array.IndexOf(aCRLExclude, sPos) >= 0)
                        continue;
                }
                else
                {
                    if (Array.IndexOf(aCRUExclude, sPos) >= 0)
                        continue;
                }
                if (string.IsNullOrEmpty(sTarget))
                    continue;
                if (Strings.Left(sTarget.ToUpper(), 3) == "SM_")
                {
                    menu.Append("<li><a class=\"accordion-toggle\"><span class=\"sidebar-title\">" + goTR.StrRead(sPage, sPos + "LABEL") + "</span><span class=\"caret\"></span></a><ul>");
                    menu.Append(GetCSSSubMenu(sPage, i));
                    menu.Append("</ul></li>");
                }
                else
                {
                    sFile = Strings.Left(sTarget, 2);
                    sLabel = goTR.StrRead(sPage, sPos + "LABEL");
                    if (string.IsNullOrEmpty(sLabel))
                        sLabel = goData.GetFileLabelFromName(sFile);
                    sTarget = sTarget.Replace(":", "||");
                    sTarget = sTarget.Replace("/", "~");
                    menu.Append("<li title=\'" + sLabel + "\'><a onclick=\"MainAddNew('" + sFile + "','" + sTarget + "')\" href=\"#\">" + sLabel + "</a></li>");
                    //menu.Append("<li title=\'" + sLabel + "\'><a href=\"/CreateForm/CreateForm/" + sFile + "/ID/CRU_/false/false/KEY/false/MASTERID\">" + sLabel + "</a></li>");
                }

            }
            menu.Append("</ul>");

            HtmlString menubar = new HtmlString(menu.ToString());

            Util.AddtoCache(goP.GetUserTID() + "_sAddNewMenu", menubar);

            return menubar;
        }
        private string GetCSSSubMenu(string sPage, int iMenuPos)
        {
            int refint = 4;
            int i = 0;
            string sMainMenuPos = null;
            string sPos = null;
            string sTarget = null;
            string sFile = null;
            string[] aCRLExclude = null;
            string[] aCRUExclude = null;
            string sLabel = null;
            sMainMenuPos = "SM" + goTR.Pad(iMenuPos.ToString(), 3, "0", "L");
            aCRLExclude = Strings.Split(goTR.StrRead(sPage, "CRLEXCLUDE", "", false), ",");
            aCRUExclude = Strings.Split(goTR.StrRead(sPage, "CRUEXCLUDE", "", false), ",");
            StringBuilder menu = new StringBuilder();
            for (i = 1; i <= goTR.StringToNum(goTR.StrRead(sPage, sMainMenuPos + "COUNT", "0", false), "0", ref refint, ""); i++)
            {
                sPos = goTR.Pad(i.ToString(), 3, "0", "L");
                sTarget = goTR.StrRead(sPage, sMainMenuPos + "_" + sPos, "", false);
                if (gsType.ToUpper() == "CRL")
                {
                    if (Array.IndexOf(aCRLExclude, sMainMenuPos + "_" + sPos) >= 0)
                        continue;
                }
                else
                {
                    if (Array.IndexOf(aCRUExclude, sMainMenuPos + "_" + sPos) >= 0)
                        continue;
                }
                if (string.IsNullOrEmpty(sTarget))
                    continue;

                sFile = Strings.Left(sTarget, 2);
                sLabel = goTR.StrRead(sPage, sMainMenuPos + "_" + sPos + "LABEL");
                if (string.IsNullOrEmpty(sLabel))
                    sLabel = goData.GetFileLabelFromName(sFile);
                if (gsType == "CRU")
                {
                    sTarget = sTarget.Replace(":", "||");
                    menu.Append("<li title=" + sLabel + "><a onclick=\"MainAddNew('" + sFile + "','" + sTarget + "','TRUE')\"  href=\"#\">" + sLabel + "</a></li>");
                    //menu.Append("<li title=" + sLabel + "><a href=\"/CreateForm/CreateForm/" + sFile + "/ID/CRU_" + sTarget + "/false/false/KEY/false/MASTERID\">" + sLabel + "</a></li>");
                }
                //else
                //{
                //    menu.Append("<li><a onclick=\"return CreateLink(\'" + sFile + "\')\" href=\"#\">" + sLabel + "</a></li>");
                //}
            }
            return menu.ToString();
        }

        public HtmlString GetToolsMenu()
        {
            goP = (clProject)Util.GetInstance("p");
            //Get from cache if available..J
            if (Util.GetItemFromCache(goP.GetUserTID() + "_sToolsMenu") != null)
            {
                return new HtmlString(Util.GetItemFromCache(goP.GetUserTID() + "_sToolsMenu").ToString());
            }

            //Else load from here..J
            goP = (clProject)Util.GetInstance("p");
            StringBuilder toolsMenu = new StringBuilder();

            toolsMenu.Append("<ul>");

            //toolsMenu.Append("<li><a href=\"#\">Webmail</a><li>");    //Hidden by manmeet word.. 07/11/2017.. S1
            //toolsMenu.Append("<li title=\"Log E-mail\"><a href=\"#\" onclick=\"SiteRunScript('STARTEMAILLOGGING')\">Log E-mail</a><li>");
            //toolsMenu.Append("<li title=\"Sync with Outlook\"><a href=\"#\" onclick=\"SiteRunScript('STARTOUTLOOKLINK')\">Sync with Outlook</a><li>");

            //if (goP.GetMe("PERMIMPORT") == "1")
            //{
            //    toolsMenu.Append("<li title=\"Import from Excel\"><a href=\"#\" onclick=\"SiteRunScript('STARTIMPORTUTILITY')\">Import from Excel</a></li>");
            //}
           // toolsMenu.Append("<li class=\"divider\"></li>");
            toolsMenu.Append("<li title=\"Manage Desktops/Views\"><a onclick=\"ManageDesktopsClick()\" href=\"#\">Manage Desktops/Views</a></li>");

            if (goP.IsUserAdmin() || goP.IsUserCustomerAdmin())
            {
                toolsMenu.Append("<li title=\"Manage Menu\"><a onclick=\"ManageMenuClick()\" href=\"#\">Manage Menu</a></li>");
                toolsMenu.Append("<li title=\"Manage Database\"><a onclick=\"ManageDatabaseClick()\" href=\"#\">Manage Database</a></li>");
                //toolsMenu.Append("<li><a href=\"/MobileFields/LoadMobileFields\">Mobile Fields</a></li>");
                toolsMenu.Append("<li title=\"Mobile Fields\"><a href=\"/MobileFields/LoadLagacyMobileFields\">Mobile Fields</a></li>");
            }

            if (goP.IsUserAdmin() || goP.IsUserCustomerAdmin())
            {
                if (goP.IsUserAdmin() || goP.GetMe("PERMCUSTADMINMANAGELOGINS") == "1" || goP.GetMe("PERMCUSTADMINMANAGEFEATURES") == "1" || goP.GetMe("PERMCUSTADMINMANAGEDATAACCESS") == "1")
                {
                    if (Util.Show("ADMIN_USERPERMS"))
                    {
                        toolsMenu.Append("<li class=\"divider\"></li>");
                        toolsMenu.Append("<li title=\"User/Group Permissions\"><a onclick=\"UserGroupPermissionsClick()\" href=\"#\">User/Group Permissions</a></li>");
                    }
                }
                else
                {
                    if (Util.Show("ADMIN_USERPERMS"))
                    {
                        toolsMenu.Append("<li class=\"divider\"></li>");
                        toolsMenu.Append("<li title=\"User/Group Permissions\"><a href=\"#\">User/Group Permissions</a></li>");
                    }
                }
                if (goP.IsUserAdmin() || goP.GetMe("PERMWORKGROUPOPTIONS") == "1")
                {
                    if (Util.Show("ADMIN_WOP"))
                    {
                        toolsMenu.Append("<li title=\"Workgroup Options\"><a onclick=\"WorkGroupOptionsClick()\" href=\"#\">Workgroup Options</a></li>");
                    }
                }
                else
                {
                    if (Util.Show("ADMIN_WOP"))
                    {
                        toolsMenu.Append("<li title=\"Workgroup Options\"><a href=\"#\">Workgroup Options</a></li>");
                    }
                }
                if (goP.IsUserAdmin() || goP.GetMe("PERMWORKGROUPOPTIONS") == "1")
                {
                    if (Util.Show("ADMIN_CUSTOMWOP"))
                    {
                        toolsMenu.Append("<li title=\"Custom Workgroup Options\"><a href=\"/CreateForm/CreateForm/XW/ID/CRU_XW/false/false/KEY/false/MASTERID/FORMKEY/FIELD\">Custom Workgroup Options</a></li>");
                    }
                }
                else
                {
                    if (Util.Show("ADMIN_CUSTOMWOP"))
                    {
                        toolsMenu.Append("<li title=\"Custom Workgroup Options\"><a href=\"#\">Custom Workgroup Options</a></li>");
                    }
                }

                //toolsMenu.Append("<li title=\"Import\"><a href=\"/Common/ImportTasks\">Import</a></li>");
                //toolsMenu.Append("<li title=\"Import Jobs\"><a href=\"/Common/ImportDataUtility\">Simpul 2.0</a></li>");
            }


            if (goP.IsUserAdmin() || goP.IsUserAuthor() == 2 || goP.GetMe("PERMFORMS") == "1")
            {
                if (goP.IsUserAdmin() || goP.IsUserAuthor() == 2 || goP.GetMe("PERMFORMS") == "1")
                {
                    if (Util.Show("AUTHOR_MANAGEFORMS"))
                    {
                        toolsMenu.Append("<li class=\"divider\"></li>");
                        toolsMenu.Append("<li title=\"Manage Forms\"><a onclick=\"ManageFormsClick()\" href=\"#\">Manage Forms</a></li>");
                    }
                }
                else
                {
                    if (Util.Show("AUTHOR_MANAGEFORMS"))
                    {
                        toolsMenu.Append("<li class=\"divider\"></li>");
                        toolsMenu.Append("<li title=\"Manage Forms\"><a href=\"#\">Manage Forms</a></li>");
                    }
                }
                if (goP.IsUserAdmin() || goP.IsUserAuthor() == 2)
                {
                    if (Util.Show("AUTHOR_BROWSEMETA"))
                    {
                        toolsMenu.Append("<li title=\"Browse Metadata\"><a onclick=\"BrowseMetadataClick()\" href=\"#\">Browse Metadata</a></li>");
                    }
                }
                else
                {
                    if (Util.Show("AUTHOR_BROWSEMETA"))
                    {
                        toolsMenu.Append("<li title=\"Browse Metadata\"><a href=\"#\">Browse Metadata</a></li>");
                    }
                }
                if (goP.IsUserAuthor() == 2)
                {
                    if (Util.Show("AUTHOR_ERRORMSGS"))
                    {
                        toolsMenu.Append("<li title=\"Error Messages\"><a href=\"/WhatsNew/WhatsNew\">Error Messages</a></li>");
                    }
                }
                else
                {
                    if (Util.Show("AUTHOR_ERRORMSGS"))
                    {
                        toolsMenu.Append("<li title=\"Error Messages\"><a href=\"/WhatsNew/WhatsNew\">Error Messages</a></li>");
                    }
                }
            }


            toolsMenu.Append("</ul>");

            HtmlString toolsMenuHtml = new HtmlString(toolsMenu.ToString());

            Util.AddtoCache(goP.GetUserTID() + "_sToolsMenu", toolsMenuHtml);

            return toolsMenuHtml;
        }
        
    }
}