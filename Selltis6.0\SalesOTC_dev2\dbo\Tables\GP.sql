﻿CREATE TABLE [dbo].[GP] (
    [GID_ID]                                     UNIQUEIDENTIFIER CONSTRAINT [DF_GP_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),(0))+CONVERT([binary](2),'GP',(0)))+CONVERT([binary](6),getutcdate(),(0)),(0))) ROWGUIDCOL NOT NULL,
    [BI__ID]                                     BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                                   NVARCHAR (80)    NULL,
    [DTT_CreationTime]                           DATETIME         CONSTRAINT [DF_GP_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]                               TINYINT          NULL,
    [TXT_ModBy]                                  VARCHAR (4)      NULL,
    [DTT_ModTime]                                DATETIME         CONSTRAINT [DF_GP_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [TXT_DemandName]                             NVARCHAR (50)    NULL,
    [MMO_ImportData]                             NTEXT            NULL,
    [SI__ShareState]                             TINYINT          CONSTRAINT [DF_GP_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]                           UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]                               VARCHAR (50)     NULL,
    [TXT_ExternalID]                             NVARCHAR (80)    NULL,
    [TXT_ExternalSource]                         VARCHAR (10)     NULL,
    [TXT_ImpJobID]                               VARCHAR (20)     NULL,
    [GID_RELATED_CO]                             UNIQUEIDENTIFIER NULL,
    [GID_RELATED_PG]                             UNIQUEIDENTIFIER NULL,
    [GID_RELATED_PC]                             UNIQUEIDENTIFIER NULL,
    [CHK_TargetMD]                               TINYINT          NULL,
    [CUR_Demand]                                 MONEY            NULL,
    [CUR_Potential]                              MONEY            NULL,
    [CUR_GapPotential]                           MONEY            NULL,
    [CUR_Actual]                                 MONEY            NULL,
    [CUR_Gap]                                    MONEY            NULL,
    [MLS_CompetitionList1]                       SMALLINT         NULL,
    [CHK_AVL1]                                   TINYINT          NULL,
    [SR__CompetitionInstalledBaseNo1]            REAL             NULL,
    [SR__CompetitionInstalledBasePercent1]       REAL             NULL,
    [SR__Annualpurchase1]                        REAL             NULL,
    [SR__OurProductInstalledBase1]               REAL             NULL,
    [SR__OurProductInstalledBasePercent1]        REAL             NULL,
    [SR__OurproductoverallinstalledbasePercent1] REAL             NULL,
    [MLS_CompetitionList2]                       SMALLINT         NULL,
    [SR__CompetitionInstalledBaseNo2]            REAL             NULL,
    [SR__CompetitionInstalledBasePercent2]       REAL             NULL,
    [MLS_CompetitionList3]                       SMALLINT         NULL,
    [SR__CompetitionInstalledBaseNo3]            REAL             NULL,
    [SR__CompetitionInstalledBasePercent3]       REAL             NULL,
    [MLS_CompetitionList4]                       SMALLINT         NULL,
    [SR__CompetitionInstalledBaseNo4]            REAL             NULL,
    [SR__CompetitionInstalledBasePercent4]       REAL             NULL,
    [MLS_CompetitionList5]                       SMALLINT         NULL,
    [SR__CompetitionInstalledBaseNo5]            REAL             NULL,
    [SR__CompetitionInstalledBasePercent5]       REAL             NULL,
    [MLS_CompetitionList6]                       SMALLINT         NULL,
    [SR__CompetitionInstalledBaseNo6]            REAL             NULL,
    [SR__CompetitionInstalledBasePercent6]       REAL             NULL,
    [sr__TotalInstallations]                     REAL             NULL,
    CONSTRAINT [PK_GP] PRIMARY KEY CLUSTERED ([GID_ID] ASC)
);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_GP_BI__ID]
    ON [dbo].[GP]([BI__ID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_GP_CreationTime]
    ON [dbo].[GP]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_GP_ModDateTime]
    ON [dbo].[GP]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_GP_TXT_ImportID]
    ON [dbo].[GP]([TXT_ImportID] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_GP_CreatedBy_US]
    ON [dbo].[GP]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_GP_DemandName]
    ON [dbo].[GP]([TXT_DemandName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_GP_Name]
    ON [dbo].[GP]([SYS_NAME] ASC);

