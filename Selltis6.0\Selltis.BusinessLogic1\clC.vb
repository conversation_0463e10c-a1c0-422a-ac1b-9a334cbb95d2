'Owner MI as of 4/4/06 was CS previously
Imports Microsoft.VisualBasic

Public Class clC
    'MI 10/1/13 Added SELL_EXPIRE_PASSWORD_MAX_DAYS.
    'MI 7/12/10 Added SELL_BLANK_SYSDATETIME.
    'MI 3/25/10 Added SELL_SUIDxxxxxx.
    'MI 12/10/09 Added SELL_GROUPBY, SELL_COUNT, SELL_VIEW_CHART.
    'MI 10/9/09 added SELL_MAX_LOGINATTEMPTS and SELL_FAILEDLOGINTIMEOUTMINS.
    'MI 3/9/09 Added SELL_LinkProcessModes.
    'MI 2/11/09 Added SELL_SYSTEMFORMAT.
    'MI 11/4/08 Added SELL_LINKSTOP
    'MI 8/28/08 Added SELL_EXTRACT_BETWEENSINGLEQUOTES
    'MI 7/17/07 Added SELL_FORM_COLUMNWIDTHDEF, SELL_FORM_COLUMNLBLWIDTHDEF
    'MI 3/5/07 Added SELL_LOGLINE_MAXLENGTH. Changed SELL_METAVALUELIMIT from 4000 to 3800.
    'MI 2/28/07 Added Control state section.
    'MI 9/20/06 Added SELL_FIELDTYPE_GID
    'MI 9/12/06 Added SELL_SQLSTRING.
    'MI 9/11/06 Added HISTORY constants for WT.
    'MI 8/28/06 Added MessageBox constants.
    'MI 7/25/06 Added SELL_FIELDTYPE constants.
    '7/17/06    MI  Entered new constants for RH. Changed SELL_EARLIEST_DATE to SELL_BLANK_DATETIME.
    '4/27/06    MI  Modified SELL_BLANK_DATETIME to 1/2/1753 to leave 1/1/1753 for recording date-less times.

    'Public constants from selltis.wl    

    Public Const SELL_FORMAT_DATETIME As String = "yyyy-MM-dd HH:mm:ss.fff"

    'SELL_BLANK_DATETIME represents a date and time with no value. A blank datetime
    'could be expressed as NULL in SS, but .net doesn't
    'support NULLs in datetime variables. The value is set to 1 second before
    'midnight of 1/3/1753. This indicates blank date and blank time.
    'A value of 1753-01-03 00:00:00.000 is 1/3/1753 at midnight, a supported
    'date and time.
    '
    'The reason for 1/2/1753 is to leave the whole day of 1/1/1753 open
    'for recording date-less times. Dateless times raise many issues are this notion
    'is not supported as of 7/17/06.
    '
    'You can't express a date with a blank time. All-day appts
    'for example have a time span from 00:00:00.000 to 23:59:59.996. SellSQL
    'doesn't support the notion of untimed appointments, as NGP did. This greatly
    'facilitates filtering and sorting since time is always part of the datetime
    'date type.
    '
    'The reason for the blank datetime value being 1 second before midnight and not
    '1 millisecond before is that datetimes passed as strings lose milliseconds,
    'so testing If dtDate = clC.SELL_BLANK_DATETIME wouldn't catch such values.
    Public Const SELL_BLANK_DATETIME As String = "1753-01-02 23:59:59.000" 'not "1753-01-01 00:00:00.000"
    Public Const SELL_BLANK_SYSDATETIME As String = "1753-01-02|23:59:59.000"
    Public Const SELL_BLANK_DTDATETIME As DateTime = #1/2/1753 11:59:59 PM#

    ' FORMATS
    'Use this in the par_sFormat parameter of goTr.StringToXxx or XxxToString methods
    'when you are parsing a number, date, time, currency value as text and you need 
    'the format to stay consistent and predictable.
    'The 'system' format is the way SQL displays and accepts non-string values in queries,
    'for example numbers as '-24553456.42775', datetimes as '2010-04-23 23:54:21.698', etc.
    Public Const SELL_SYSTEMFORMAT As String = "SYSTEM"
    ' DEFAULT FORMATS
    Public Const SELL_FORMAT_TIMEDEF As String = "HH:mm:ss.fff"
    Public Const SELL_FORMAT_DATEDEF As String = "yyyy-MM-dd"
    ' FORMATS FOR RETURNED DATA TYPES
    Public Const SELL_FORMAT_STRING As Integer = 0
    Public Const SELL_FORMAT_SYSTEM As Integer = 1
    Public Const SELL_FORMAT_SECOND As Integer = 2
    Public Const SELL_FORMAT_MINUTE As Integer = 3
    Public Const SELL_FORMAT_TIME As Integer = 4
    Public Const SELL_FORMAT_DATE As Integer = 5
    Public Const SELL_FORMAT_LONGDATE As Integer = 6
    Public Const SELL_FORMAT_NUM As Integer = 7
    Public Const SELL_FORMAT_CURR As Integer = 8
    Public Const SELL_FORMAT_HUNDREDTH As Integer = 9

    'FIELD TYPES
    Public Const SELL_FIELDTYPE_OTHER As Integer = 0
    Public Const SELL_FIELDTYPE_MEMO As Integer = 1
    Public Const SELL_FIELDTYPE_NUMERIC As Integer = 2
    Public Const SELL_FIELDTYPE_DATETIME As Integer = 3
    Public Const SELL_FIELDTYPE_GID As Integer = 4

    'To be removed after WT reads MAXHISTORYITEMS from POP_PERSONAL_OPTIONS
    Public Const SELL_HISTORY_MAXITEMS As Integer = 20
    Public Const SELL_HISTORY_MAXFORMS As Integer = 5

    'LOGIN
    'Public Const SELL_MAX_LOGINATTEMPTS As Integer = 5          'MI 10/9/09
    'Public Const SELL_FAILEDLOGINTIMEOUTMINS As Integer = 5          'MI 10/9/09
    'XU count is multiplied by this number to determine max allowed number of failed login attempts workgroup-wide per period set with SELL_GROUPLOGINFAILURETIMEOUTMINUTES
    Public Const SELL_MAXFAILEDTOTALLOGINATTEMPTSMULTIPLIER As Integer = 3
    'Maximum number of failed user login attempts per period set with SELL_USERLOGINFAILURETIMEOUTMINUTES
    Public Const SELL_MAXFAILEDUSERLOGINATTEMPTS As Integer = 5          'MI 10/9/09
    'Number of minutes during which failed user login attempts that exceed SELL_MAXFAILEDUSERLOGINATTEMPTS will cause a lockout
    Public Const SELL_USERLOGINFAILURELOCKOUTMINUTES As Integer = 5    'MI 10/13/09
    'Number of minutes during which workgroup-wide failed attempts that exceed SELL_MAXFAILEDTOTALLOGINATTEMPTSMULTIPLIER will cause a lockout
    Public Const SELL_GROUPLOGINFAILURELOCKOUTMINUTES As Integer = 5    'MI 10/13/09

    ' VARIOUS
    Public Const SELL_EXPIRE_PASSWORD_MAX_DAYS As Integer = 3700        'MI 10/1/13
    Public Const SELL_ALT_RUNSCRIPT As String = "RUNSCRIPT"
    Public Const SELL_AGE_OPENNDBFORM As String = "OPENNDBFORM"
    Public Const SELL_MESS_SHOWALERT As Integer = 10000
    Public Const SELL_ALERT_SHAREMEM As String = "SELLTIS_ALERT_SHAREMEM"
    Public Const SELL_SELLTIS_DIALOG As String = "SELLTIS_DIALOG"
    Public Const SELL_SELLTIS_ALERT As String = "SELLTIS_ALERT"
    Public Const SELL_DEFAULTFONT_NAME As String = "Tahoma"
    Public Const SELL_DEFAULTFONT_SIZE As Integer = 8.5
    Public Const SELL_MAX_INDEX As Integer = 999999999
    Public Const SELL_ROWSET_TYPE As String = "TYPE"
    Public Const SELL_ROWSET_FIELDS As String = "FIELDS"
    Public Const SELL_ROWSET_CONDITION As String = "CONDITION"
    Public Const SELL_ROWSET_SORT As String = "SORT"
    Public Const SELL_ROWSET_TOP As String = "TOP"
    Public Const SELL_ROWSET_TABLE As String = "TABLE"
    Public Const SELL_DUPLICATEVALUE_PARAM As String = "PARAM"
    Public Const SELL_DUPLICATEVALUE_FILE As String = "FILE"
    Public Const SELL_DUPLICATEVALUE_KEYNAME As String = "KEY"
    Public Const SELL_AMODE_EDITFORM As String = "EDIT"
    Public Const SELL_AMODE_CREATELINKED As String = "CREATELINKEDBYID"
    Public Const SELL_AMODE_CREATEUNLINKED As String = "CREATEUNLINKED"
    Public Const SELL_AMODE_CREATEBLANK As String = "CREATEBLANK"
    Public Const SELL_DELETE_SILENT As String = "DeleteSilent"
    Public Const SELL_CANCEL_SILENT As String = "CancelSilent"
    Public Const SELL_SAVEANDCLOSE_SILENT As String = "SaveAndCloseSilent"
    Public Const SELL_SAVENOCLOSE_SILENT As String = "SaveNoCloseSilent"
    Public Const SELL_ARRAY_LINKS As Integer = 1
    Public Const SELL_ARRAY_FIELDS As Integer = 2
    Public Const SELL_ARRAY_FILES As Integer = 3
    Public Const SELL_FILTER_CONDITION As Integer = 1
    Public Const SELL_FILTER_SORTORDER As Integer = 2
    Public Const SELL_COL_ADDED As String = "A"
    Public Const SELL_COL_DELETED As String = "D"
    Public Const SELL_FORM_DISPLAY As Integer = 0
    Public Const SELL_FORM_REDISPLAY As Integer = 1
    Public Const SELL_FORM_COLUMNWIDTHDEF As Integer = 200
    Public Const SELL_FORM_COLUMNLBLWIDTHDEF As Integer = 100
    Public Const SELL_FRIENDLY As Integer = 1
    Public Const SELL_SYSTEM As Integer = 2
    Public Const SELL_DISPLAYED As Integer = 3
    Public Const SELL_SQLSTRING As Integer = 4      '*** MI 9/12/06

    'WT 12/16/13
    Public Const SELL_AGE_OPENCVS As String = "OPENCVS"
    Public Const SELL_AGE_OPENFRF As String = "OPENFRF"
    Public Const SELL_FORM_TYPE_NDB As Integer = 1
    Public Const SELL_FORM_TYPE_CVS As Integer = 2
    Public Const SELL_FORM_TYPE_FRF As Integer = 3

    Public Const SELL_EDIT As Integer = 1 'Edit type rowset
    Public Const SELL_ADD As Integer = 2 'Add type rowset
    Public Const SELL_READONLY As Integer = 3 'Add type rowset
    Public Const SELL_GROUPBY As Integer = 4    'MI 12/10/09 added
    Public Const SELL_GROUPBYWITHROLLUP As Integer = 5    'MI 1/8/10 added
    Public Const SELL_COUNT As Integer = 6      'MI 12/10/09 added
    Public Const SELL_READONLY_ALL As Integer = 7 'VT 5/22/23 Add type rowset

    Public Const SELL_RESULT_ERROR As Integer = 0
    Public Const SELL_RESULT_OK As Integer = 1
    Public Const SELL_RESULT_ABORT As Integer = 2
    Public Const SELL_RESULT_CANCEL As Integer = -1
    Public Const SELL_USER_ALL As String = "ALL"
    'ACTION TO TAKE
    Public Const SELL_FILE_ADD As Integer = 1
    Public Const SELL_FILE_DELETE As Integer = 2
    'Alerts
    Public Const SELL_ALT_OPENRECORD As String = "OPENRECORD"
    Public Const SELL_ALT_OPENDESKTOP As String = "OPENDESKTOP"
    Public Const SELL_ALT_OPENURL As String = "OPENURL"
    Public Const SELL_ALT_OPENURLEXTERNAL As String = "OPENURLEXTERNAL"
    Public Const SELL_ALT_OPENNEWFORM As String = "OPENNEWFORM"
    Public Const SELL_ALT_OPENNEWNDBFORM As String = "OPENNEWNDBFORM"
    'Licences
    Public Const SELL_LICENCE_LAN As Integer = 1
    Public Const SELL_LICENCE_SYNC As Integer = 2
    Public Const SELL_LICENCE_CARDLINK As Integer = 3
    Public Const SELL_LICENCE_OUTLOOKPERS As Integer = 4
    Public Const SELL_LICENCE_OUTLOOKWRK As Integer = 5
    'Lists
    Public Const SELL_LISTNAME As Integer = 1
    Public Const SELL_ITEMKEY As Integer = 2
    Public Const SELL_ITEMVALUE As Integer = 3
    ' SELLTRAN MESSAGES
    Public Const SELL_TRAN_ERROR As Integer = 0
    Public Const SELL_TRAN_TOOLTIP As Integer = 1
    Public Const SELL_TRAN_PROCESS As Integer = 2
    ' ALERT FIRE TIMER EVERY
    Public Const SELL_ALERT_EVERY_NMINUTES As Integer = 1
    Public Const SELL_ALERT_EVERY_HOUR As Integer = 2
    Public Const SELL_ALERT_EVERY_DAY As Integer = 3
    Public Const SELL_ALERT_EVERY_WEEK As Integer = 4
    Public Const SELL_ALERT_EVERY_WEEKDAY As Integer = 5
    Public Const SELL_ALERT_EVERY_MONTH As Integer = 6
    ' DB ACCESS MODE
    Public Const SELL_DBMODE_NODB As String = "" 'no DB opened
    Public Const SELL_DBMODE_LAN As String = "LAN" 'Access to the main DB Via the network
    Public Const SELL_DBMODE_SYNC As String = "SYNC" 'Sync client (local DB part of a workgroup)
    Public Const SELL_DBMODE_STANDALONE As String = "STDALONE" 'Local DB not part of a workgroup
    Public Const SELL_DBMODE_SERVER As String = "SERVER" 'used when accessing locally the server DB (mainly sync engine)
    ' TYPEs OF USER/PERMISSIONS
    Public Const SELL_AUTHOR_NONE As Integer = 0
    Public Const SELL_AUTHOR_PARTIAL As Integer = 1
    Public Const SELL_AUTHOR_FULL As Integer = 2
    Public Const SELL_ADMIN As Integer = 3
    Public Const SELL_CREATENEWDB As Integer = 4
    Public Const SELL_BACKUP As Integer = 6
    Public Const SELL_IMPORT As Integer = 7
    Public Const SELL_EXPORT As Integer = 8
    Public Const SELL_TRANSFERIN As Integer = 9
    Public Const SELL_TRANSFEROUT As Integer = 10
    Public Const SELL_PERIODICBILLING As Integer = 11
    Public Const SELL_REIMBURSE As Integer = 12
    Public Const SELL_APPROVEREIMBURSE As Integer = 13
    Public Const SELL_PRINT As Integer = 14
    Public Const SELL_SEND As Integer = 18
    Public Const SELL_DEFAULT As Integer = 9
    Public Const SELL_NO As Integer = 0
    Public Const SELL_SELECTIVE As Integer = 1
    Public Const SELL_FULL As Integer = 2
    Public Const SELL_READ As Integer = 1
    Public Const SELL_WRITE As Integer = 2
    Public Const SELL_DELETE As Integer = 3
    ' FIELD ICONS
    Public Const SELL_MAX_FIELD_ICONS As Integer = 1000
    ' MLS LIST
    Public Const SELL_MAX_LIST_ITEMS As Integer = 100
    Public Const SELL_MAX_AUTOMATOR_ACTIONS As Integer = 99
    Public Const SELL_SHARED_CHAR As String = "*"
    ' FILTER SETTINGS
    Public Const SELL_CUSTOMORDER As String = "Custom"
    Public Const SELL_REVERSEORDER As String = "1"
    Public Const SELL_NORMALORDER As String = "0"
    Public Const SELL_ASCENDING As String = "1"
    Public Const SELL_DESCENDING As String = "2"
    ' DATE/TIME MANAGEMENT
    Public Const SELL_TYPE_FIXE As Integer = 0
    Public Const SELL_TYPE_KEYWORD As Integer = 1
    Public Const SELL_TYPE_ABBREV As Integer = 2
    Public Const SELL_TYPE_INVALID As Integer = 3
    Public Const SELL_TYPE_VALID As Integer = 4
    ' Passwordmanagement
    Public Const SELL_PASSWORD_INCLEAR As Integer = 0
    Public Const SELL_PASSWORD_CRYPTED As Integer = 1
    ' SHARING STATE OF A RECORD
    ' do NOT replace the value 0/1/2 by strings
    ' it's stored as a SI in the file
    Public Const SELL_LOCAL As Integer = 0
    Public Const SELL_PROTECTED As Integer = 1
    Public Const SELL_SHARED As Integer = 2
    ' TYPE OF RECORD TO CREATE
    Public Const SELL_NOTSPECIFIED As String = "NOT SPECIFIED"
    Public Const SELL_LEAD As String = "LEAD"
    Public Const SELL_NOTE As String = "NOTE"
    Public Const SELL_CORRESPONDANCE As String = "CORR"
    Public Const SELL_EMAIL As String = "EMAIL"
    Public Const SELL_LETTER As String = "LETTER"
    Public Const SELL_FAX As String = "FAX"
    ' CALLING MODES FOR McreateFormLinked
    ' and MCreateFormUnLinked
    Public Const SELL_FROMMAIN As Integer = 0
    Public Const SELL_FROMFORM As Integer = 1
    ' LINKBOX
    'OPENING MODES
    'ON THE FORM
    Public Const SELL_LBOX_COMBOLIKE As Integer = 1
    Public Const SELL_LBOX_LISTBOX As Integer = 2
    Public Const SELL_LBOX_TABLE As Integer = 3
    'IN THE SELECTOR
    Public Const SELL_LBOX_ONEPANE As Integer = 4
    Public Const SELL_LBOX_TWOPANES As Integer = 5
    Public Const SELL_LBOX_TWOPANESALL As Integer = 6
    'PARAMETERS REQUESTS 
    Public Const SELL_LBOX_FILTER As Integer = 10
    Public Const SELL_LBOX_COLFORM As Integer = 11
    Public Const SELL_LBOX_COLSEL As Integer = 12
    'MISC 
    Public Const SELL_LBOX_FORM As Integer = 1
    Public Const SELL_LBOX_SELECTOR As Integer = 2
    Public Const SELL_FROMMEMORY As Integer = 1
    ' GETLASTERROR
    Public Const SELL_ERROR_NUMBER As String = "NUMBER"
    Public Const SELL_ERROR_MESSAGE As String = "MESSAGE"
    Public Const SELL_ERROR_PROC As String = "PROCEDURE"
    Public Const SELL_ERROR_PARAMS As String = "PARAMS"
    ' GETLINEVALUE, GETLINEELEMENT, GETLINEPART:
    ' String Extraction
    Public Const SELL_EXTRACT_OUT As Integer = 0
    Public Const SELL_EXTRACT_PERHAPSENTERING As Integer = 1
    Public Const SELL_EXTRACT_ENTERING As Integer = 2
    Public Const SELL_EXTRACT_IN As Integer = 3
    Public Const SELL_EXTRACT_PERHAPSQUITTING As Integer = 4
    Public Const SELL_EXTRACT_QUITTING As Integer = 5
    Public Const SELL_EXTRACT_BETWEENQUOTES As Integer = 6
    Public Const SELL_EXTRACT_ONASPACE As Integer = 7
    Public Const SELL_EXTRACT_BETWEENSINGLEQUOTES As Integer = 8
    'These are for GetLineElement:
    Public Const SELL_EXTRACT_SEP As String = "S"
    Public Const SELL_EXTRACT_FIELD As String = "F"
    ' LINKS
    Public Const SELL_LINK_1TON As String = "1N"
    Public Const SELL_LINK_NTO1 As String = "N1"
    Public Const SELL_LINK_NTON As String = "NN"
    ' FUNCTIONING MODES
    Public Const SELL_MODE_MAIN As String = "SellMain"
    Public Const SELL_MODE_API As String = "API"
    Public Const SELL_MODE_APIDLL As String = "APIDLL"
    Public Const SELL_MODE_NEWINSTANCE As String = "SellInstance"
    Public Const SELL_MODE_SEARCH As String = "Search"
    Public Const SELL_MODE_SYNC As String = "Sync"
    Public Const SELL_MODE_AUTO As String = "Automator"
    Public Const SELL_MODE_ADMINCONS As String = "AdminCons"
    Public Const SELL_MODE_MODFILE As String = "ModFile"
    Public Const SELL_MODE_DDESERV As String = "DDEServ"
    Public Const SELL_MODE_TOEXCEL As String = "ToExcel"
    Public Const SELL_MODE_SELLSEND As String = "SellSend"
    Public Const SELL_MODE_WEB As String = "SellWeb"
    Public Const SELL_MODE_CARDSCAN As String = "CardScanLink"
    Public Const SELL_MODE_OUTLOOK As String = "OutlookLink"
    Public Const SELL_MODE_PALM As String = "PalmLink"
    Public Const SELL_MODE_SELLTRAN As String = "Tran"
    Public Const SELL_MODE_FORM As String = "Form"
    Public Const SELL_MODE_IMPORT As String = "Import"
    Public Const SELL_MODE_ROWSET As String = "Rowset"
    Public Const SELL_MODE_SPLA As String = "SellSpla"
    Public Const SELL_MODE_ALERT As String = "Alert"
    ' CONDUITS
    Public Const SELL_CONDUIT_OUTLOOKCAL As String = "OUTLOOKCAL"
    Public Const SELL_CONDUIT_OUTLOOKCONTACT As String = "OUTLOOKCONTACT"
    Public Const SELL_CONDUIT_PALMADDRESS As String = "PALMADDRESSBOOK"
    Public Const SELL_CONDUIT_PALMDATE As String = "PALMDATEBOOK"
    Public Const SELL_CONDUIT_PALMMEMO As String = "PALMMEMO"
    Public Const SELL_CONDUIT_PALMTODO As String = "PALMTODO"
    ' File types (in PRNTEMPL)
    Public Const SELL_TEMPLATE_LIST As Integer = 1 'Template HTML for lists
    Public Const SELL_TEMPLATE_ONERECORD As Integer = 2 'Template HTML used for Forms, multi-fields or single-field
    Public Const SELL_TEMPLATE_WEEKCAL As Integer = 3 'Template HTML for week calendars
    Public Const SELL_TEMPLATE_MONTHCAL As Integer = 4 'Template HTML for month calendars
    Public Const SELL_TEMPLATE_DAYCAL As Integer = 5 'Template HTML for day calendars
    ' Miscellaneous
    Public Const SELL_HISTORY_MAXCHAR As Integer = 4000 'How many characters to keep in MMO_History fields.
    Public Const SELL_MEMOLIMIT As Integer = 32000 'Maximum size for memos
    Public Const SELL_METALIMIT As Integer = 1000000000 'Maximum size for meta's memo (not direct edition) was 64000
    Public Const SELL_METAVALUELIMIT As Integer = 3800 'Maximum size for meta line's value (nvarchar(4000) in SS)
    Public Const SELL_COMBOLIMIT As Integer = 6500 'Max no of elements in combos and listboxes
    Public Const SELL_MINIMUMPACKET As Integer = 50 'Minimum sync packet size is 50 Ko
    ' SellTrace
    Public Const SELLTRACE_USER As Integer = 0
    Public Const SELLTRACE_ALL As Integer = 1
    Public Const SELLTRACE_OFF As Integer = 2
    Public Const SELLTRACE_USER_AND_EMPTY As Integer = 3
    ' Calendar
    Public Const CAL_TIMEBAR_YES As Integer = 1
    Public Const CAL_TIMEBAR_NO As Integer = 2
    Public Const CAL_TIMEBAR_IFNEEDED As Integer = 3
    Public Const CAL_SELECTFIRSTDATE As Integer = 1
    Public Const CAL_SELECTLASTDATE As Integer = 2
    Public Const CAL_SELECTNOCHANGE As Integer = 3
    ' View types
    ' Supported view types (Typically used in gwsVals, etc.)
    Public Const SELL_FORM As String = "FORM" 'Form window
    Public Const SELL_RECORD As String = "RECORD" 'One record from a list view
    Public Const SELL_VIEW_LIST As String = "LIST" 'List view
    Public Const SELL_VIEW_LISTFILTER As String = "LISTFILTER" 'List Filter view
    Public Const SELL_VIEW_LISTREPORT As String = "LISTREPORT" 'List report view
    Public Const SELL_VIEW_DAYCAL As String = "CALDAY" 'Day Calendar view
    Public Const SELL_VIEW_WEEKCAL As String = "CALWEEK" 'Week Calendar view
    Public Const SELL_VIEW_MONTHCAL As String = "CALMONTH" 'Month Calendar view
    Public Const SELL_VIEW_YEARCAL As String = "CALYEAR" 'Year Calendar view
    Public Const SELL_VIEW_MFIELD As String = "MFIELD" 'Multi-field view
    Public Const SELL_VIEW_SFIELD As String = "SFIELD" 'Single-field view
    Public Const SELL_VIEW_CHART As String = "CHART"    'Chart view     'MI 12/10/09 added
    Public Const SELL_VIEW_DEFAULTTOP As Integer = 10 'The 'First' no of lines in views
    Public Const SELL_LINKBOX_CONTROL As String = "LINKBOX_CONTROL" 'Linkbox control on a form
    Public Const SELL_LISTVIEW_SHOWTOPMAX As Integer = 1000     'Max number of recs supported in list view
    Public Const SELL_REPORTVIEW_SHOWTOPMAX As Integer = 10000     'Max number of recs supported in report view
    Public Const SELL_CHARTVIEW_SHOWTOPMAX As Integer = 10000     'Max number of recs supported in chart view
    Public Const SELL_VIEWPRINT_MAXRECORDS As Integer = 10000     'Max number of recs supported in list and report view printing and sending to Excel
    ' Event logging
    Public Const SELL_LOGLEVEL_NONE As Integer = 0
    Public Const SELL_LOGLEVEL_STANDARD As Integer = 1
    Public Const SELL_LOGLEVEL_DETAILS As Integer = 2
    Public Const SELL_LOGLEVEL_DEBUG As Integer = 3
    Public Const SELL_LOGLINE_MAXLENGTH As Integer = 7600       'Size of XL.TXT_Message field
    ' TimeZone management [FH]
    Public Const TIME_ZONE_ID_DAYLIGHT As Integer = 2
    Public Const TIME_ZONE_ID_STANDARD As Integer = 1
    Public Const TIME_ZONE_ID_UNKNOWN As Integer = 0
    'Return values:
    Public Const IDABORT As Integer = 3
    Public Const IDCANCEL As Integer = 2
    Public Const IDIGNORE As Integer = 5
    Public Const IDNO As Integer = 7
    Public Const IDOK As Integer = 1
    Public Const IDRETRY As Integer = 4
    Public Const IDYES As Integer = 6

    'This is a native constant in Windev
    Public Const EOT As Char = Chr(4)

    'From clTransform 
    Public Const SELL_YEAR_END As Integer = 49  'used in CompleteYear()

    'Error management
    Public Const EX_THREAD_ABORT_MESSAGE As String = "Thread was being aborted."

    'MessageBox
    'Type:
    Public Const SELL_MB_ABORTRETRYIGNORE As Integer = &H2
    Public Const SELL_MB_OK As Integer = &H0
    Public Const SELL_MB_OKCANCEL As Integer = &H1
    Public Const SELL_MB_RETRYCANCEL As Integer = &H5
    Public Const SELL_MB_YESNO As Integer = &H4
    Public Const SELL_MB_YESNOCANCEL As Integer = &H3
    Public Const SELL_MB_INPUTBOX As Integer = &H8
    'Icons:
    Public Const SELL_MB_ICONBLANK As Integer = &H0
    Public Const SELL_MB_ICONEXCLAMATION As Integer = &H30
    Public Const SELL_MB_ICONWARNING As Integer = &H30
    Public Const SELL_MB_ICONINFORMATION As Integer = &H40
    Public Const SELL_MB_ICONASTERISK As Integer = &H40
    Public Const SELL_MB_ICONQUESTION As Integer = &H20
    Public Const SELL_MB_ICONSTOP As Integer = &H10
    Public Const SELL_MB_ICONERROR As Integer = &H10
    Public Const SELL_MB_ICONHAND As Integer = &H10
    'Default buttons:
    Public Const SELL_MB_DEFBUTTON1 As Integer = &H0
    Public Const SELL_MB_DEFBUTTON2 As Integer = &H100
    Public Const SELL_MB_DEFBUTTON3 As Integer = &H200
    Public Const SELL_MB_DEFBUTTON4 As Integer = &H300
    'Modality:
    Public Const SELL_MB_APPLMODAL As Integer = &H0
    Public Const SELL_MB_SYSTEMMODAL As Integer = &H1000
    Public Const SELL_MB_TASKMODAL As Integer = &H2000
    'Other values:
    Public Const SELL_MB_TOPMOST As Integer = &H40000

    ' Automators
    Public Const SELL_AGE_RUNSCRIPT As String = "RUNSCRIPT"
    Public Const SELL_AGE_OPENRECORD As String = "OPENRECORD"
    Public Const SELL_AGE_OPENDESKTOP As String = "OPENDESKTOP"
    Public Const SELL_AGE_OPENURL As String = "OPENURL"
    Public Const SELL_AGE_OPENURLEXTERNAL As String = "OPENURLEXTERNAL"

    'Control state (note different meanings for controls and linkboxes)
    Public Const SELL_STATE_UNCHANGED As Integer = -1          'Control: unchanged. Linkbox: unchanged.
    Public Const SELL_STATE_ACTIVE As Integer = 0              'Control: active. Linkbox: visible, click-through working, button active.
    Public Const SELL_STATE_INACTIVE As Integer = 1            'Control: locked out. Linkbox: visible, click-through working, button active, but linkbox selector controls grayed out.
    Public Const SELL_STATE_INVISIBLE As Integer = 2           'Control: invisible. Linkbox: invisible, button invisible.
    Public Const SELL_STATE_GRAYED As Integer = 4              'Control: grayed out. Linkbox: grayed out, click-through not working, button grayed out.
    Public Const SELL_STATE_LINKBUTTONGRAYED As Integer = 5     'Control: unchanged. Linkbox: visible, click-through working, button grayed out.
    Public Const SELL_STATE_LINKBUTTONINVISIBLE As Integer = 6  'Control: unchanged. Linkbox: visible, click-through working, button invisible.

    'Links top default
    Public Const SELL_LINKSTOP As Integer = 5                  'no of linked recs returned by default in a view    'MI 11/4/08

    Public Const SELL_DATERANGE_STARTTIME As Integer = 1
    Public Const SELL_DATERANGE_ENDTIME As Integer = 2
    Public Const SELL_DATERANGE_LABEL As Integer = 3
    Public Const SELL_DATERANGE_PERIOD As Integer = 4          'word usable in date phrases that denotes the range: day, week, month, quarter, year.

    'Mobile
    Public Const SELL_MOBILEEXPIRELOGININDAYS As Integer = 365 'Default no of days in which to expire a mobile login cookie
    Public Const SELL_MOBILEEXPIRELOGININDAYSMAX As Integer = 3650 'Max no of days for mobile login expiration.

    'Post-import processing modes
    Public Const SELL_LinkProcessMode_ImportID As Integer = 1  '(existing mode)
    Public Const SELL_LinkProcessMode_GID_ID As Integer = 2    '(by explicit GID_ID value)
    Public Const SELL_LinkProcessMode_TN As Integer = 3        '(by Translation [TN] record)
    Public Const SELL_LinkProcessMode_Mixed As Integer = 4     '(mix of the above 3 modes)

    Public Const SELL_SUIDFakePreFile As String = "11111111-1111-1111-" 'Left portion of 'fake' Selltis unique ID (before the 4 chars that identify the file)
    Public Const SELL_SUIDFakePostFile As String = "-111111111111"      'Right portion of 'fake' Selltis unique ID (after the 4 chars that identify the file)
    Public Const SELL_SUIDAnyPostFile As String = "-111111111112"      'Right portion of '(Any)' Selltis unique ID (after the 4 chars that identify the file)





    Public Sub New()

    End Sub
End Class
