﻿CREATE TABLE [dbo].[PD] (
    [GID_ID]                      UNIQUEIDENTIFIER CONSTRAINT [DF_PD_GID_ID] DEFAULT (CONVERT([uniqueidentifier],(CONVERT([binary](8),newid(),0)+CONVERT([binary](2),'PD',0))+CONVERT([binary](6),getutcdate(),0),0)) ROWGUIDCOL NOT NULL,
    [BI__ID]                      BIGINT           IDENTITY (1, 1) NOT NULL,
    [SYS_NAME]                    NVARCHAR (2000)  NULL,
    [CHK_ActiveField]             TINYINT          NULL,
    [FIL_Attachments]             NTEXT            NULL,
    [MMO_CatalogDescr]            NTEXT            NULL,
    [TXT_CatalogName]             NVARCHAR (50)    NULL,
    [MMO_CompetitionNotes]        NTEXT            NULL,
    [DTT_CreationTime]            DATETIME         CONSTRAINT [DF_PD_DTT_CreationTime] DEFAULT (getutcdate()) NULL,
    [CHK_DemoData]                TINYINT          NULL,
    [MMO_Notes]                   NTEXT            NULL,
    [TXT_OrigCreatedBy]           VARCHAR (4)      NULL,
    [DTT_OrigCreatedTime]         DATETIME         NULL,
    [FIL_Picture]                 NTEXT            NULL,
    [TXT_ProductName]             NVARCHAR (500)   NULL,
    [CHK_Report]                  TINYINT          CONSTRAINT [DF_PD_CHK_Report] DEFAULT ((1)) NULL,
    [MMO_SpecQuestions]           NTEXT            NULL,
    [URL_URLs]                    NTEXT            NULL,
    [URL_WebPage]                 NTEXT            NULL,
    [MMO_WhyBuy]                  NTEXT            NULL,
    [TXT_ModBy]                   VARCHAR (4)      NULL,
    [DTT_ModTime]                 DATETIME         CONSTRAINT [DF_PD_DTT_ModTime] DEFAULT (getutcdate()) NULL,
    [MMO_ImportData]              NTEXT            NULL,
    [SI__ShareState]              TINYINT          CONSTRAINT [DF_PD_SI__ShareState] DEFAULT ((2)) NULL,
    [GID_CreatedBy_US]            UNIQUEIDENTIFIER NULL,
    [GID_Related_GR]              UNIQUEIDENTIFIER NULL,
    [GID_Related_DV]              UNIQUEIDENTIFIER NULL,
    [GID_Related_VE]              UNIQUEIDENTIFIER NULL,
    [TXT_ImportID]                VARCHAR (50)     NULL,
    [TXT_ExternalID]              NVARCHAR (120)   NULL,
    [TXT_ExternalSource]          VARCHAR (10)     NULL,
    [TXT_ImpJobID]                VARCHAR (20)     NULL,
    [CHK_TargetProduct]           TINYINT          NULL,
    [CHK_Key]                     TINYINT          NULL,
    [ADR_Attachments]             NTEXT            NULL,
    [GID_CONNECTED_GR]            UNIQUEIDENTIFIER NULL,
    [TXT_ITEMNUMBER]              NVARCHAR (80)    NULL,
    [MLS_PRODUCTCURRANCY]         SMALLINT         NULL,
    [TXT_HSCODE]                  NVARCHAR (80)    NULL,
    [CUR_RESELLERPRICE]           MONEY            NULL,
    [SR__TAREWEIGHTLBS]           REAL             NULL,
    [SR__MAXIMUMGROSSWEIGHT]      REAL             NULL,
    [SR__PAYLOAD]                 REAL             NULL,
    [TXT_PREVIOUSPARTNUMBER]      NVARCHAR (100)   NULL,
    [TXT_REFERENCEDRAWINGNUMBER]  NVARCHAR (100)   NULL,
    [SR__PAYLOADKG]               REAL             NULL,
    [SR__TAREWEIGHTKG]            REAL             NULL,
    [SR__MAXIMUMGROSSWEIGHTKG]    REAL             NULL,
    [SR__LENGTHINCH]              REAL             NULL,
    [SR__WIDTHINCH]               REAL             NULL,
    [SR__HEIGHTMM]                REAL             NULL,
    [SR__HEIGHTM]                 REAL             NULL,
    [SR__LENGTHMM]                REAL             NULL,
    [SR__LENGTHM]                 REAL             NULL,
    [SR__WIDTHM]                  REAL             NULL,
    [SR__WIDTHMM]                 REAL             NULL,
    [SR__INTERNALLENGTHMM]        REAL             NULL,
    [SR__INTERNALWIDTHMM]         REAL             NULL,
    [SR__INTERNALHEIGHTMM]        REAL             NULL,
    [CUR_1YEARRENTALRATE]         MONEY            NULL,
    [CUR_3MONTHRENTALRATE]        MONEY            NULL,
    [CUR_6MONTHRENTALRATE]        MONEY            NULL,
    [TXT_DESCRIPTION]             NVARCHAR (4000)  NULL,
    [CHK_RENTAL]                  TINYINT          NULL,
    [TXT_EXTENDEDDESCRIPTION]     NVARCHAR (100)   NULL,
    [ADR_CERTIFICATIONS]          NTEXT            NULL,
    [MMO_SPECIFICATIONS]          NTEXT            NULL,
    [CHK_PRICEEDITABLE]           TINYINT          NULL,
    [CHK_NONDISCOUNTABLE]         TINYINT          NULL,
    [CHK_TAXABLE]                 TINYINT          NULL,
    [CHK_QUANTITYEDITABLE]        TINYINT          NULL,
    [MLS_LOB]                     SMALLINT         NULL,
    [GID_RELATED_PG]              UNIQUEIDENTIFIER NULL,
    [CUR_Amount]                  MONEY            NULL,
    [MLS_Currency]                SMALLINT         NULL,
    [TXT_SFProductID]             NVARCHAR (50)    NULL,
    [TXT_LookupName]              NVARCHAR (2000)  NULL,
    [MMO_DESCRIPTION]             NTEXT            NULL,
    [CHK_OTC]                     TINYINT          NULL,
    [MLS_LEGACYDATASOURCE]        SMALLINT         NULL,
    [CHK_JCIPCAT]                 TINYINT          NULL,
    [Gid_JCIID]                   UNIQUEIDENTIFIER NULL,
    [DTT_LastOpenedDateTimeforME] AS               ([dbo].[Cus_GetLastOpenedDateTimeforME]([GID_ID])),
    CONSTRAINT [PK_PD] PRIMARY KEY CLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_PD_CreatedBy_US] FOREIGN KEY ([GID_CreatedBy_US]) REFERENCES [dbo].[US] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PD_Related_DV] FOREIGN KEY ([GID_Related_DV]) REFERENCES [dbo].[DV] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PD_Related_GR] FOREIGN KEY ([GID_Related_GR]) REFERENCES [dbo].[GR] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PD_Related_VE] FOREIGN KEY ([GID_Related_VE]) REFERENCES [dbo].[VE] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[PD] NOCHECK CONSTRAINT [LNK_PD_CreatedBy_US];


GO
ALTER TABLE [dbo].[PD] NOCHECK CONSTRAINT [LNK_PD_Related_DV];


GO
ALTER TABLE [dbo].[PD] NOCHECK CONSTRAINT [LNK_PD_Related_GR];


GO
ALTER TABLE [dbo].[PD] NOCHECK CONSTRAINT [LNK_PD_Related_VE];


GO
CREATE NONCLUSTERED INDEX [IX_PD_Name]
    ON [dbo].[PD]([SYS_NAME] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PD_ProductName]
    ON [dbo].[PD]([TXT_ProductName] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PD_Related_GR]
    ON [dbo].[PD]([GID_Related_GR] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PD_CreationTime]
    ON [dbo].[PD]([DTT_CreationTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PD_ModDateTime]
    ON [dbo].[PD]([DTT_ModTime] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PD_Related_DV]
    ON [dbo].[PD]([GID_Related_DV] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PD_Related_VE]
    ON [dbo].[PD]([GID_Related_VE] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PD_CreatedBy_US]
    ON [dbo].[PD]([GID_CreatedBy_US] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_PD_TXT_ImportID]
    ON [dbo].[PD]([TXT_ImportID] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_PD_BI__ID]
    ON [dbo].[PD]([BI__ID] ASC);


GO
CREATE TRIGGER trPDUpdateTN
ON [PD]
FOR INSERT, UPDATE 
AS 
-- MI 5/1/09 Added ISNULL tests, setting ExternalID and ExternalSource to '' instead of NULL.
--		This is what the rowset does to all NULL fields anyway.
-- MI 1/13/09 Not running UPDATE of the original record unless
-- both ExternalSource and ExternalID are not null.
-- PURPOSE: This trigger creates a TN record for all INSERTed or UPDATEed
-- records in PD table.
-- -- IMPORTANT: keep this code in sync with pCreateTNRecord sproc!
-- We can't call that sproc from here because the creation of TN records
-- must be set-based for performance when large INSERTs or UPDATEs are made.
	DECLARE @sExternalSource varchar(10)
	DECLARE @sExternalID nvarchar(120)
	DECLARE @uGIDID uniqueidentifier
	DECLARE @sGIDID varchar(36)
	DECLARE @sTable varchar(2)
	DECLARE @uID uniqueidentifier
	DECLARE @sUserID varchar(36)
	DECLARE @sUserCode varchar(4)
	DECLARE @iError int
	DECLARE @sError varchar(20)

	SET @sTable = 'PD'
	SET @iError = 0

	-- Validate user login
	SET @sUserID = [dbo].fnGetMe('ID')
	IF @sUserID is null or LTrim(RTrim(@sUserID)) = ''
	BEGIN
		-- User login not in SQL session, set it to SYSTEM.
		SET @sUserID = '********-3EC5-48F7-5553-987900B57A11'	--SYSTEM
		SET @sUserCode = 'SYST'
	END
	ELSE
	BEGIN
		-- User ID found in SQL session
		-- Validate user code
		SET @sUserCode = [dbo].fnGetMe('CODE')
		IF @sUserCode is null or LTrim(RTrim(@sUserCode)) = ''
		BEGIN
			-- User code is blank or NULL.
			SET @iError = -3
			GOTO ErrHandler
		END
	END

	INSERT INTO TN
		(SYS_Name,
		CHK_DemoData,
		GID_InternalID,
		TXT_File,
		TXT_ExternalID,
		TXT_ExternalSource,
		TXT_ModBy,
		DTT_ModTime,
		SI__ShareState,
		GID_CreatedBy_US)
	SELECT '',
		0, 
		inserted.GID_ID,
		@sTable,
		inserted.TXT_ExternalID,
		inserted.TXT_ExternalSource,
		@sUserCode,
		GetUTCDate(),
		2,
		@sUserID
		FROM inserted
		WHERE ISNULL(inserted.TXT_ExternalSource, '') <> '' and ISNULL(inserted.TXT_ExternalID, '') <> '' 
			and not exists 
				(SELECT *
				FROM TN tn2
				WHERE tn2.TXT_ExternalID = inserted.TXT_ExternalID
					and tn2.TXT_ExternalSource = inserted.TXT_ExternalSource
					and tn2.TXT_File = @sTable
					and tn2.GID_InternalID = inserted.GID_ID)

	SET @iError = @@ERROR
	IF @iError = 0 
	BEGIN
		-- No error
		-- Remove values from ExtSource and ExtID
		UPDATE [PD]
			SET [PD].TXT_ExternalSource = '', [PD].TXT_ExternalID = '' 
			FROM inserted in1
			WHERE [PD].GID_ID = in1.GID_ID
				and ISNULL([PD].TXT_ExternalSource, '') <> ''
				and ISNULL([PD].TXT_ExternalID, '') <> ''
		GOTO WeAreDone
	END
	ELSE
	BEGIN
		-- Real error
		-- Error 2601 is PK duplicate. Should not get it since we are testing whether dupl rec exists.
		GOTO ErrHandler
	END

ErrHandler:
	-- No error, skip the rest of error handling
	IF @iError = 0 GOTO WeAreDone
	SET @sError = CAST(@iError as varchar(20))
	IF @iError = -2
	BEGIN
		RAISERROR ('Error %d: session has no user ID. [trPDUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	IF @iError = -3
	BEGIN
		RAISERROR ('Error %d: session has no user code. [trPDUpdateTN] failed to create TN records. Insert or update of %d records failed.',
			  16, 1, @sError, @sTable); 
		GOTO RollBackTransaction
	END
	-- Unexpected error occurred (@iError is not 0, -2, or -3)
	RAISERROR ('Error %d occurred during INSERT INTO TN in [trPDUpdateTN]. Insert or update of %d records failed.',
		  16, 1, @sError, @sTable); 
	GOTO RollBackTransaction

RollBackTransaction:
	ROLLBACK TRANSACTION

WeAreDone:
	-- Don't add code below this line!