﻿CREATE TABLE [dbo].[XF] (
    [BI__ID]                  BIGINT           IDENTITY (10000, 1) NOT NULL,
    [GID_ID]                  UNIQUEIDENTIFIER NOT NULL,
    [TXT_MP_GID_ID]           VARCHAR (50)     NULL,
    [TXT_CATEGORY]            VARCHAR (50)     NULL,
    [TXT_FIELDTYPE]           VARCHAR (200)    NULL,
    [TXT_FIELDNAMES]          VARCHAR (1000)   NULL,
    [TXT_LABELNAME]           VARCHAR (50)     NULL,
    [TXT_GROUP_NAME]          VARCHAR (50)     NULL,
    [TXT_TITLE]               VARCHAR (50)     NULL,
    [TXT_WIDGETTYPE]          VARCHAR (50)     NULL,
    [TXT_WIDGETVALUE]         VARCHAR (50)     NULL,
    [TXT_FIELDVALUE]          VARCHAR (50)     NULL,
    [SI__IS_REQUIRED]         TINYINT          NULL,
    [TXT_REQUIRED_MESSAGE]    VARCHAR (100)    NULL,
    [SR__SORTORDER]           FLOAT (53)       NULL,
    [SYS_Name]                NVARCHAR (320)   NULL,
    [SI__ShareState]          TINYINT          NOT NULL,
    [GID_CreatedBy_US]        UNIQUEIDENTIFIER NULL,
    [DTT_CreationTime]        DATETIME         NULL,
    [TXT_ICON_NAME]           NVARCHAR (50)    NULL,
    [TXT_FIELD_LOCATION]      NVARCHAR (50)    NULL,
    [SI__FILTER_DEFAULT]      SMALLINT         NULL,
    [TXT_FILTER_SORT]         NVARCHAR (250)   NULL,
    [SI__FIELD_VISIBLE]       SMALLINT         NULL,
    [INT_ROWNUMBER]           INT              NULL,
    [TXT_CRL_TYPE]            VARCHAR (100)    NULL,
    [SI__CRL_VISIBLE]         SMALLINT         NULL,
    [TXT_CRL_PAGEID]          VARCHAR (100)    NULL,
    [TXT_CRL]                 VARCHAR (100)    NULL,
    [TXT_CRL_DEFAULT]         VARCHAR (100)    NULL,
    [SR__SORTORDERNEW]        FLOAT (53)       NULL,
    [TXT_MAP_LINKBOX]         VARCHAR (100)    NULL,
    [DTT_ModTime]             DATETIME         NULL,
    [SI__SHOWLABEL]           TINYINT          NULL,
    [TXT_SEARCHFILTERKEY]     NVARCHAR (1)     NULL,
    [TXT_SEARCHHINTTEXT]      NVARCHAR (1)     NULL,
    [TXT_LNKSEARCHFILTERKEY]  NVARCHAR (1)     NULL,
    [TXT_LNKSEARCHHINTTEXT]   NVARCHAR (1)     NULL,
    [CHK_PAGEISFORQUICKFORMS] TINYINT          NULL,
    CONSTRAINT [PK_XF] PRIMARY KEY CLUSTERED ([BI__ID] ASC) WITH (FILLFACTOR = 95)
);

