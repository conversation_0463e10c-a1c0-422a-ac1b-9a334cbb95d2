'OWNER: MI

Public Class cldiaFormPro
    'Inherits clVar

    'MI 5/22/07 Created.

    'Enable global objects only if needed
    'Private goP As clProject
    'Private goMeta As clMetaData
    'Private goTR As clTransform
    'Private goData As clData
    'Private goErr As clError
    'Private goLog As clLog
    'Private goUI As clUI
    'Private goHist As clHistory

    Public par_sMode As String     'Page opening mode
    Public sReturnURL As String = ""
    Public bBackFromSubdialog As Boolean
    Public sMessageBoxPurpose As String

    'Parent dialog object
    Public sTitle As String = "Form Properties"
    Public fenenexecution As String = "diaFormPro.aspx"

    Public sVals As String = ""         'Main work string
    Public iCurrentPlane As Integer     'number of the current plane (Multiview view)
    Public sMode As String = ""         'Window opening mode in uppercase: ModifDisplayedForm, EditForm
    Public sFileName As String = ""     'Main file
    Public sSection As String
    Public sPage As String
    Public sTab As String
    Public sSelectedListbox As String
    Public sDefaultLabelText As String = "(default)"
    Public sAreaInFocus As String
    Public sSelectedTab As String

    Public sRowSpanLabel As String = Chr(160) & Chr(160) & Chr(160) & Chr(160) & Chr(160) & "|"
    Public sColSpanLabel As String = "---------------"
    Public sBlankLabel As String = "[blank]"
    Public sNoLabelLabel As String = "[no label]"
    Public sDefaultLblWidth As String = "100"
    Public sDefaultWidth As String = "200"
    Public sNewTabLabel As String = "new tab"
    Public sFormDisplayGridPref As String   'POP setting "FORMDISPLAYGRID" 
    Public sMessage As String               'Variable for passing 
    Public sName As String
    Public sToolbarControlName As String
    Public sToolbarControlLabel As String
    Public sToolbarControlWidth As String
    Public sToolbarControlHeight As String
    Public sToolbarControlImage As String
    Public TabLabel As String


    'Column widths
    Public sColLblWidth001 As String
    Public sColWidth001 As String
    Public sColLblWidth002 As String
    Public sColWidth002 As String
    Public sColLblWidth003 As String
    Public sColWidth003 As String
    Public sColLblWidth004 As String
    Public sColWidth004 As String
    Public sColLblWidth005 As String
    Public sColWidth005 As String
    Public sColLblWidth006 As String
    Public sColWidth006 As String
    Public sColLblWidth011 As String
    Public sColWidth011 As String
    Public sColLblWidth012 As String
    Public sColWidth012 As String
    Public sColLblWidth013 As String
    Public sColWidth013 As String
    Public sColLblWidth014 As String
    Public sColWidth014 As String
    Public sColLblWidth015 As String
    Public sColWidth015 As String
    Public sColLblWidth016 As String
    Public sColWidth016 As String

    'Table declarations
    Public dt001 As New DataTable
    Public t001 As New clTable
    Public dt002 As New DataTable
    Public t002 As New clTable
    Public dt003 As New DataTable
    Public t003 As New clTable
    Public dt004 As New DataTable
    Public t004 As New clTable
    Public dt005 As New DataTable
    Public t005 As New clTable
    Public dt006 As New DataTable
    Public t006 As New clTable
    Public dt011 As New DataTable
    Public t011 As New clTable
    Public dt012 As New DataTable
    Public t012 As New clTable
    Public dt013 As New DataTable
    Public t013 As New clTable
    Public dt014 As New DataTable
    Public t014 As New clTable
    Public dt015 As New DataTable
    Public t015 As New clTable
    Public dt016 As New DataTable
    Public t016 As New clTable
    Public dtClip As New DataTable
    Public tClip As New clTable
    Public dtTabs As New DataTable
    Public tTabs As New clTable
    Public dtToolbar As New DataTable
    Public tToolbar As New clTable
    Public dtHeader As New DataTable
    Public tHeader As New clTable

    'Record Selector Properties
    Public RecordSelectorDisplay As Boolean = False
    Public RecordSelectorField1 As String = ""
    Public RecordSelectorField2 As String = ""
    Public RecordSelectorField3 As String = ""
    Public RecordSelectorField4 As String = ""
    Public RecordSelectorField5 As String = ""

    Public RecordSelectorField1Label As Boolean = False
    Public RecordSelectorField2Label As Boolean = False
    Public RecordSelectorField3Label As Boolean = False
    Public RecordSelectorField4Label As Boolean = False
    Public RecordSelectorField5Label As Boolean = False



    Public Sub Initialize()
        Dim sProc As String = "clDiaFormPro::Initialize"
        'Try
        '   'Enable only if needed
        '   goP = HttpContext.Current.Session("goP")
        '   goTR = HttpContext.Current.Session("goTr")
        '   goMeta = HttpContext.Current.Session("goMeta")
        '   goData = HttpContext.Current.Session("goData")
        '   goErr = HttpContext.Current.Session("goErr")
        '   goLog = HttpContext.Current.Session("goLog")
        '   goUI = HttpContext.Current.Session("goUI")
        '   goHist = HttpContext.Current.Session("goHist")
        'Catch ex As Exception
        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, sProc)
        '    End If
        'End Try
    End Sub

    Public Sub New()

    End Sub

    Protected Overrides Sub Finalize()
        MyBase.Finalize()
    End Sub
End Class
