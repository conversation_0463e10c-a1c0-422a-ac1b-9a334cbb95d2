﻿@model Selltis.MVC.Models.PersonalOptionsModel
@using Kendo.Mvc.UI

<div class="row clearfix" id="FilterSortP">
    <div class="col-lg-12" style="background-color:white !important;">
        <section id="profile" class="well" style="margin-left:-10px; margin-top:-6px;">
            <div id="div_MsgBox" class="xs_Msg" style="display:none">
                @{Html.RenderPartial("~/Views/PersonalOptions/MsgBoxPartial.cshtml");}
            </div>
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-2">
                        <span id="Label9" style="display:inline-block; width:125px;">Send Email Alerts</span>
                    </div>
                    @*  @Html.HiddenFor(m => m.EmailAlertOptions_Info.sWGEmailAlertsEnabled, new { id = "sWGEmailAlertsEnabled" })*@
                    @Html.HiddenFor(m => m.EmailAlertOptions_Info.CMB_EmailAlertsOptEnabled, new { id = "CMB_EmailAlertsOptEnabled" })
                    @*<div class="col-md-1">
                            @(Html.Kendo().DropDownListFor(m => Model.EmailAlertOptions_Info.SelVal)
                                 .Name("CMB_EmailAlertsOpt")
                                 .DataTextField("Text")
                                 .DataValueField("Value")
                                 .HtmlAttributes(new { style = "width: 100%" })
                                 .BindTo(new List<SelectListItem>()
                                   {
                                      new SelectListItem() {
                                           Text = "Yes",
                                           Value ="Yes"
                                         },
                                     new SelectListItem() {
                                          Text = "No",
                                          Value ="No"
                                        }
                                   }).Value(Model.EmailAlertOptions_Info.sWGEmailAlertsEnabled)
                                    //.Events(e => e.Change("OnSearchTypeChange"))
                            )
                        </div>*@
                    <div class="col-md-1">
                        @{
                            List<SelectListItem> EmailAlerts = new List<SelectListItem>();
                            EmailAlerts.Add(new SelectListItem
                                 {
                                     Text = "Yes",
                                     Value = "Yes",
                                     Selected = false
                                 });
                            EmailAlerts.Add(new SelectListItem
                                 {
                                     Text = "No",
                                     Value = "No",
                                     Selected = false
                                 });

                        }
                        @Html.DropDownListFor(m => m.EmailAlertOptions_Info.SelVal, EmailAlerts, new { id = "CMB_EmailAlertsOpt", style = "height: 13px;" })
                    </div>
                </div>
            </div>
            @Html.HiddenFor(m => m.EmailAlertOptions_Info.ChkLBFilesEnabled, new { id = "ChkLBFilesEnabled" })
            <div id="EmailAlerts_div">
                <div class="row">
                    <span id="lblEmailAlerts" style="display:inline-block;width:200px;">Select files to send email alerts:</span>
                </div>
                @*<div>
                        @if (Model.EmailAlertOptions_Info.ChkLBFiles != null)
                        {

                           if (Model.EmailAlertOptions_Info.ChkLBFiles.Count > 0 )
                           {
                               int j = 0;
                               for (int i = 0; i < Model.EmailAlertOptions_Info.ChkLBFiles.Count; i += 4)
                               {
                                   j = i;
                                   <div class="row" style="padding-top:5px;">

                                       @while (j < i + 4 && j < Model.EmailAlertOptions_Info.ChkLBFiles.Count)
                                       {
                                           @(Html.Kendo().CheckBox().Name('"' + Model.EmailAlertOptions_Info.ChkLBFiles[j].Value + '"')
                                            .Checked(Model.EmailAlertOptions_Info.ChkLBFiles[j].Selected)
                                               .Label(Model.EmailAlertOptions_Info.ChkLBFiles[j].Text).HtmlAttributes(new { style = "padding-right:10px;" }));
                                       j++;
                                       }
                                   </div>

                               }
                           }
                        }
                    </div>*@
                <div>
                    @if (Model.EmailAlertOptions_Info.ChkLBFiles != null)
                    {
                        if (Model.EmailAlertOptions_Info.ChkLBFiles.Count > 0)
                        {
                            int j = 0;
                            for (int i = 0; i < Model.EmailAlertOptions_Info.ChkLBFiles.Count; i += 4)
                            {
                                j = i;
                                <div class="row" style="padding-top:5px;">

                                    @while (j < i + 4 && j < Model.EmailAlertOptions_Info.ChkLBFiles.Count)
                                    {
                                        @Html.HiddenFor(m => Model.EmailAlertOptions_Info.ChkLBFiles[j].Value, new { style = "padding-right:10px;margin-right: 5px;" })
                                        @Html.CheckBoxFor(m => Model.EmailAlertOptions_Info.ChkLBFiles[j].Selected, new { style = "padding-right:10px;margin-right: 5px;" })

                                        @Html.DisplayFor(m => Model.EmailAlertOptions_Info.ChkLBFiles[j].Text, new { style = "padding-right:10px;margin-right: 5px!important;" })

                                        @:&nbsp;&nbsp;
                                        j++;
                                    }
                                </div>
                            }
                        }
                    }
                </div>
            </div>
            <div class="row"></div>
            @{Html.RenderPartial("~/Views/PersonalOptions/SaveFunc_Partial.cshtml");}
            @*<div style="min-height:420px !important;">

                </div>*@
        </section>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function () {
        //debugger
        var Sel_EmailAlertsOpt = $("#CMB_EmailAlertsOpt").data("kendoDropDownList");
        if (Sel_EmailAlertsOpt == "No") {
            $("#EmailAlerts_div").hide();
        }

        if ($('#ChkLBFilesEnabled').val() == 'True') {
            $("#EmailAlerts_div").show();
        }
        else {
            $("#EmailAlerts_div").hide();
        }

        if ($('#CMB_EmailAlertsOptEnabled').val() == 'True') {
            //Sel_EmailAlertsOpt.enable();
            $("#CMB_EmailAlertsOpt").attr("disabled", false);
        }
        else {
            $("#CMB_EmailAlertsOpt").attr("disabled", true);
            //Sel_EmailAlertsOpt.enable(false);
        }
        //debugger;
        //var ss = $("#sWGEmailAlertsEnabled").val();
        //if (ss == "Yes") {
        //    $("#CMB_EmailAlertsOpt").val("Yes");
        //    // $("#CMB_EmailAlertsOpt option[value='Yes']").prop("selected", "selected");
        //}
        //else {
        //    $("#CMB_EmailAlertsOpt").val("No");
        //    // $("#CMB_EmailAlertsOpt option[value='No']").prop("selected", "selected");

        //}
    });

</script>

