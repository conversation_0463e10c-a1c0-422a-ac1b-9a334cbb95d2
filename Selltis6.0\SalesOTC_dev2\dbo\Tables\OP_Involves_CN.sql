﻿CREATE TABLE [dbo].[OP_Involves_CN] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Opp_Involves_Contact_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_OP] UNIQUEIDENTIFIER NOT NULL,
    [GID_CN] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_OP_Involves_CN] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_CN_InvolvedIn_OP] FOREIGN KEY ([GID_OP]) REFERENCES [dbo].[OP] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_OP_Involves_CN] FOREIGN KEY ([GID_CN]) REFERENCES [dbo].[CN] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[OP_Involves_CN] NOCHECK CONSTRAINT [LNK_CN_InvolvedIn_OP];


GO
ALTER TABLE [dbo].[OP_Involves_CN] NOCHECK CONSTRAINT [LNK_OP_Involves_CN];


GO
CREATE CLUSTERED INDEX [IX_CN_InvolvedIn_OP]
    ON [dbo].[OP_Involves_CN]([GID_OP] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_OP_Involves_CN]
    ON [dbo].[OP_Involves_CN]([GID_CN] ASC);

