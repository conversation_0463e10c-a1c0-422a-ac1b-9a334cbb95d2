﻿CREATE TABLE [dbo].[MS_Related_PR] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Message_Related_Project_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_MS] UNIQUEIDENTIFIER NOT NULL,
    [GID_PR] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_MS_Related_PR] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_MS_Related_PR] FOREIGN KEY ([GID_PR]) REFERENCES [dbo].[PR] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_PR_Connected_MS] FOREIGN KEY ([GID_MS]) REFERENCES [dbo].[MS] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[MS_Related_PR] NOCHECK CONSTRAINT [LNK_MS_Related_PR];


GO
ALTER TABLE [dbo].[MS_Related_PR] NOCHECK CONSTRAINT [LNK_PR_Connected_MS];


GO
CREATE CLUSTERED INDEX [IX_PR_Connected_MS]
    ON [dbo].[MS_Related_PR]([GID_MS] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_MS_Related_PR]
    ON [dbo].[MS_Related_PR]([GID_PR] ASC);

