﻿Imports Microsoft.VisualBasic
Imports System.Web
Imports ExpertPdf.HtmlToPdf

Public Class clPDF

    Dim goErr As clError
    Dim goLog As clLog
    'Dim goUI As clUI
    Dim goP As clProject
    Dim goData As clData
    Dim goMeta As clMetaData
    Dim goTR As clTransform

    Public Sub New()
        goErr = HttpContext.Current.Session("goErr")
        goLog = HttpContext.Current.Session("goLog")
        'goUI = HttpContext.Current.Session("goUI")

        goP = HttpContext.Current.Session("goP")
        goData = HttpContext.Current.Session("goData")
        goMeta = HttpContext.Current.Session("goMeta")
        goTR = HttpContext.Current.Session("goTR")
    End Sub

    Public Function HTMLToPDF(ByVal par_sFilename As String, ByVal par_sHTML As String,
                              Optional ByVal par_sPageSize As String = "LETTER",
                              Optional ByVal par_sOrientation As String = "LANDSCAPE",
                              Optional ByVal par_bShowHeader As Boolean = False,
                              Optional ByVal par_sHeaderText As String = "",
                              Optional ByVal par_sHeaderTextAlignment As String = "RIGHT",
                              Optional ByVal par_iLeftMargin As Integer = 35,
                              Optional ByVal par_iRightMargin As Integer = 35,
                              Optional ByVal par_iTopMargin As Integer = 35,
                              Optional ByVal par_iBottomMargin As Integer = 35,
                              Optional ByVal par_iHeaderHeight As Integer = 30,
                              Optional ByVal par_iFooterHeight As Integer = 35,
                              Optional ByVal par_sHeaderFontName As String = "Verdana",
                              Optional ByVal par_sFooterFontName As String = "Verdana",
                              Optional ByVal par_iHeaderFontSize As Integer = 9,
                              Optional ByVal par_iFooterFontSize As Integer = 9,
                              Optional ByVal par_iPageWidth As Integer = 0) As Boolean
        'AUTHOR: WT
        'MI 9/23/09 Added page sizes, made other mods.
        'PARAMETERS:
        '       par_sPageSize = page size. Supported: A4, LEGAL, LETTER. Unsupported sizes are processed as LETTER.
        '       par_sFilename = name of file, including .pdf extension
        '       par_sHTML = complete HTML to convert
        '       par_sOrientation = can be LANDSCAPE or PORTRAIT
        '       par_bShowHeader = header on, True.  header off, False.
        '       par_sHeaderText = header text
        '       par_sHeaderAlignment = header text alignment, possible values are CENTER, LEFT, RIGHT
        '       par_iLeftMargin = 50
        '       par_iRightMargin = 50
        '       par_iTopMargin = 50
        '       par_iBottomMargin = 50

        Dim sProc As String = "clPDF::HTMLToPDF"
        goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

        Try
            Dim PdfConverter As New ExpertPdf.HtmlToPdf.PdfConverter

            'goLog.Log(sProc, "62", clC.SELL_LOGLEVEL_DEBUG, True, True)

            Select Case UCase(par_sPageSize)
                Case "A4"
                    PdfConverter.PdfDocumentOptions.PdfPageSize = PdfPageSize.A4
                Case "LEGAL"
                    PdfConverter.PdfDocumentOptions.PdfPageSize = PdfPageSize.Legal
                Case Else
                    'LETTER and other unsupported sizes
                    PdfConverter.PdfDocumentOptions.PdfPageSize = PdfPageSize.Letter
            End Select

            'goLog.Log(sProc, "74", clC.SELL_LOGLEVEL_DEBUG, True, True)

            Select Case par_sOrientation
                Case "LANDSCAPE"
                    PdfConverter.PdfDocumentOptions.PdfPageOrientation = PDFPageOrientation.Landscape
                Case Else
                    'PORTRAIT
                    PdfConverter.PdfDocumentOptions.PdfPageOrientation = PDFPageOrientation.Portrait
            End Select

            'goLog.Log(sProc, "84", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfDocumentOptions.PdfCompressionLevel = PdfCompressionLevel.Normal

            'goLog.Log(sProc, "88", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfDocumentOptions.LeftMargin = par_iLeftMargin

            'goLog.Log(sProc, "92", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfDocumentOptions.RightMargin = par_iRightMargin

            'goLog.Log(sProc, "96", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfDocumentOptions.TopMargin = par_iTopMargin

            'goLog.Log(sProc, "100", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfDocumentOptions.BottomMargin = par_iBottomMargin

            'goLog.Log(sProc, "104", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfDocumentOptions.GenerateSelectablePdf = True

            'goLog.Log(sProc, "108", clC.SELL_LOGLEVEL_DEBUG, True, True)

            'MI 9/24/09 Per WT: FitWidth won't work until you know the width of the desktops largest table.
            'then you set pdfconverter.PageWidth  to that number, then the html would fit.
            PdfConverter.PdfDocumentOptions.FitWidth = True

            PdfConverter.PageWidth = par_iPageWidth

            'goLog.Log(sProc, "114", clC.SELL_LOGLEVEL_DEBUG, True, True)

            'PdfConverter.PdfDocumentOptions.FitHeight = True

            PdfConverter.AvoidImageBreak = True

            'goLog.Log(sProc, "120", clC.SELL_LOGLEVEL_DEBUG, True, True)

            'PdfConverter.AvoidTextBreak = True

            'header
            PdfConverter.PdfDocumentOptions.ShowHeader = par_bShowHeader

            'goLog.Log(sProc, "127", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfHeaderOptions.DrawHeaderLine = False

            'goLog.Log(sProc, "131", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfHeaderOptions.HeaderText = par_sHeaderText

            'goLog.Log(sProc, "135", clC.SELL_LOGLEVEL_DEBUG, True, True)

            Select Case par_sHeaderTextAlignment
                Case "LEFT"
                    PdfConverter.PdfHeaderOptions.HeaderTextAlign = HorizontalTextAlign.Left
                Case "RIGHT"
                    PdfConverter.PdfHeaderOptions.HeaderTextAlign = HorizontalTextAlign.Right
                Case "CENTER"
                    PdfConverter.PdfHeaderOptions.HeaderTextAlign = HorizontalTextAlign.Center
                Case Else
                    PdfConverter.PdfHeaderOptions.HeaderTextAlign = HorizontalTextAlign.Left
            End Select

            'goLog.Log(sProc, "148", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfHeaderOptions.HeaderHeight = par_iHeaderHeight

            'goLog.Log(sProc, "152", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfHeaderOptions.HeaderTextFontName = par_sHeaderFontName

            'goLog.Log(sProc, "156", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfHeaderOptions.HeaderTextFontSize = par_iHeaderFontSize

            'goLog.Log(sProc, "160", clC.SELL_LOGLEVEL_DEBUG, True, True)


            'footer
            PdfConverter.PdfDocumentOptions.ShowFooter = True

            'goLog.Log(sProc, "166", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfFooterOptions.DrawFooterLine = False

            'goLog.Log(sProc, "170", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfFooterOptions.PageNumberingFormatString = "Page &p; of &P;"

            'goLog.Log(sProc, "174", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfFooterOptions.ShowPageNumber = True

            'goLog.Log(sProc, "178", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfFooterOptions.FooterHeight = par_iFooterHeight

            'goLog.Log(sProc, "182", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfFooterOptions.FooterTextFontName = par_sFooterFontName

            'goLog.Log(sProc, "186", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfFooterOptions.FooterTextFontSize = par_iFooterFontSize

            'goLog.Log(sProc, "190", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfFooterOptions.PageNumberTextFontName = par_sFooterFontName

            'goLog.Log(sProc, "194", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.PdfFooterOptions.PageNumberTextFontSize = par_iFooterFontSize

            'goLog.Log(sProc, "198", clC.SELL_LOGLEVEL_DEBUG, True, True)

            Dim sPath As String = HttpContext.Current.Server.MapPath("~/Temp/")

            'goLog.Log(sProc, "202", clC.SELL_LOGLEVEL_DEBUG, True, True)

            PdfConverter.LicenseKey = "CSI7KTEpODo/KT8nOSk6OCc4OycwMDAw" '"3fbs/eX97+Xs/ejz7f3u7PPs7/Pk5OTk"

            'goLog.Log(sProc, "206", clC.SELL_LOGLEVEL_DEBUG, True, True)

            Dim downloadBytes() As Byte = PdfConverter.GetPdfBytesFromHtmlString(par_sHTML)

            'goLog.Log(sProc, "210", clC.SELL_LOGLEVEL_DEBUG, True, True)

            'let's test for directory
            If System.IO.Directory.Exists(HttpContext.Current.Server.MapPath("~\Temp\")) Then
                'do nothing
            Else
                Try
                    'try to create directory
                    System.IO.Directory.CreateDirectory(HttpContext.Current.Server.MapPath("..") & "\Temp")
                Catch ex As Exception
                    'goErr.SetError(ex, 45105, sProc)
                    Return ""
                End Try
            End If

            'goLog.Log(sProc, "225", clC.SELL_LOGLEVEL_DEBUG, True, True)

            ' Delete the file if it exists.
            If System.IO.File.Exists(sPath & par_sFilename) Then
                System.IO.File.Delete(sPath & par_sFilename)
            End If

            'goLog.Log(sProc, "232", clC.SELL_LOGLEVEL_DEBUG, True, True)

            'create the new file
            Dim fs As System.IO.FileStream = System.IO.File.Create(sPath & par_sFilename)

            'goLog.Log(sProc, "237", clC.SELL_LOGLEVEL_DEBUG, True, True)

            fs.Write(downloadBytes, 0, downloadBytes.Length)

            'goLog.Log(sProc, "241", clC.SELL_LOGLEVEL_DEBUG, True, True)

            fs.Close()

            'goLog.Log(sProc, "245", clC.SELL_LOGLEVEL_DEBUG, True, True)

            Return True

        Catch ex As Exception
            'If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
            '    goErr.SetError(ex, 45105, sProc)
            'End If
            Return False

        End Try
    End Function

End Class
