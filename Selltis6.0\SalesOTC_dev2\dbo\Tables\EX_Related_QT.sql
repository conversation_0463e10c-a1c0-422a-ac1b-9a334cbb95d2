﻿CREATE TABLE [dbo].[EX_Related_QT] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Expense_Related_Quote_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_EX] UNIQUEIDENTIFIER NOT NULL,
    [GID_QT] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_EX_Related_QT] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_EX_Related_QT] FOREIGN KEY ([GID_QT]) REFERENCES [dbo].[QT] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_QT_Connected_EX] FOREIGN KEY ([GID_EX]) REFERENCES [dbo].[EX] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[EX_Related_QT] NOCHECK CONSTRAINT [LNK_EX_Related_QT];


GO
ALTER TABLE [dbo].[EX_Related_QT] NOCHECK CONSTRAINT [LNK_QT_Connected_EX];


GO
CREATE CLUSTERED INDEX [IX_QT_Connected_EX]
    ON [dbo].[EX_Related_QT]([GID_EX] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_Related_QT]
    ON [dbo].[EX_Related_QT]([GID_QT] ASC);

