﻿CREATE TABLE [dbo].[EX_Related_TD] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Expense_Related_ToDo_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_EX] UNIQUEIDENTIFIER NOT NULL,
    [GID_TD] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_EX_Related_TD] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_EX_Related_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_Connected_EX] FOREIGN KEY ([GID_EX]) REFERENCES [dbo].[EX] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[EX_Related_TD] NOCHECK CONSTRAINT [LNK_EX_Related_TD];


GO
ALTER TABLE [dbo].[EX_Related_TD] NOCHECK CONSTRAINT [LNK_TD_Connected_EX];


GO
CREATE CLUSTERED INDEX [IX_TD_Connected_EX]
    ON [dbo].[EX_Related_TD]([GID_EX] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_EX_Related_TD]
    ON [dbo].[EX_Related_TD]([GID_TD] ASC);

