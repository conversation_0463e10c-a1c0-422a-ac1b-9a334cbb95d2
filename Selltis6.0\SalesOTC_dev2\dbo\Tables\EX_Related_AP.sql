﻿CREATE TABLE [dbo].[EX_Related_AP] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_Expense_Related_App_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_EX] UNIQUEIDENTIFIER NOT NULL,
    [GID_AP] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_EX_Related_AP] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_AP_Connected_EX] FOREIGN KEY ([GID_EX]) REFERENCES [dbo].[EX] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_EX_Related_AP] FOREIGN KEY ([GID_AP]) REFERENCES [dbo].[AP] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[EX_Related_AP] NOCHECK CONSTRAINT [LNK_AP_Connected_EX];


GO
ALTER TABLE [dbo].[EX_Related_AP] NOCHECK CONSTRAINT [LNK_EX_Related_AP];


GO
CREATE CLUSTERED INDEX [IX_EX_Related_AP]
    ON [dbo].[EX_Related_AP]([GID_AP] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_AP_Connected_EX]
    ON [dbo].[EX_Related_AP]([GID_EX] ASC);

