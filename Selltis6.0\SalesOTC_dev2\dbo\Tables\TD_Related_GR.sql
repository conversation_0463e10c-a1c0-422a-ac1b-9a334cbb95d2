﻿CREATE TABLE [dbo].[TD_Related_GR] (
    [GID_ID] UNIQUEIDENTIFIER CONSTRAINT [DF_ToDo_Related_Group_GID_ID] DEFAULT (newid()) ROWGUIDCOL NOT NULL,
    [GID_TD] UNIQUEIDENTIFIER NOT NULL,
    [GID_GR] UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_TD_Related_GR] PRIMARY KEY NONCLUSTERED ([GID_ID] ASC),
    CONSTRAINT [LNK_GR_Connected_TD] FOREIGN KEY ([GID_TD]) REFERENCES [dbo].[TD] ([GID_ID]) NOT FOR REPLICATION,
    CONSTRAINT [LNK_TD_Related_GR] FOREIGN KEY ([GID_GR]) REFERENCES [dbo].[GR] ([GID_ID]) NOT FOR REPLICATION
);


GO
ALTER TABLE [dbo].[TD_Related_GR] NOCHECK CONSTRAINT [LNK_GR_Connected_TD];


GO
ALTER TABLE [dbo].[TD_Related_GR] NOCHECK CONSTRAINT [LNK_TD_Related_GR];


GO
CREATE CLUSTERED INDEX [IX_GR_Connected_TD]
    ON [dbo].[TD_Related_GR]([GID_TD] ASC);


GO
CREATE NONCLUSTERED INDEX [IX_TD_Related_GR]
    ON [dbo].[TD_Related_GR]([GID_GR] ASC);

