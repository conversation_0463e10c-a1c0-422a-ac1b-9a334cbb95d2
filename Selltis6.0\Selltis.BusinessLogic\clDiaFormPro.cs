﻿using System;
using System.Data;

//OWNER: MI

namespace Selltis.BusinessLogic
{
	public class cldiaFormPro
	{
		//Inherits clVar

		//MI 5/22/07 Created.

		//Enable global objects only if needed
		//Private goP As clProject
		//Private goMeta As clMetaData
		//Private goTR As clTransform
		//Private goData As clData
		//Private goErr As clError
		//Private goLog As clLog
		//Private goUI As clUI
		//Private goHist As clHistory

		public string par_sMode; //Page opening mode
		public string sReturnURL = "";
		public bool bBackFromSubdialog;
		public string sMessageBoxPurpose;

		//Parent dialog object
		public string sTitle = "Form Properties";
		public string fenenexecution = "diaFormPro.aspx";

		public string sVals = ""; //Main work string
		public int iCurrentPlane; //number of the current plane (Multiview view)
		public string sMode = ""; //Window opening mode in uppercase: ModifDisplayedForm, EditForm
		public string sFileName = ""; //Main file
		public string sSection;
		public string sPage;
		public string sTab;
		public string sSelectedListbox;
		public string sDefaultLabelText = "(default)";
		public string sAreaInFocus;
		public string sSelectedTab;

		public string sRowSpanLabel = Microsoft.VisualBasic.Strings.Chr(160).ToString() + Microsoft.VisualBasic.Strings.Chr(160).ToString().ToString() + Microsoft.VisualBasic.Strings.Chr(160).ToString().ToString() + Microsoft.VisualBasic.Strings.Chr(160).ToString().ToString() + Microsoft.VisualBasic.Strings.Chr(160).ToString() + "|";
		public string sColSpanLabel = "---------------";
		public string sBlankLabel = "[blank]";
		public string sNoLabelLabel = "[no label]";
		public string sDefaultLblWidth = "100";
		public string sDefaultWidth = "200";
		public string sNewTabLabel = "new tab";
		public string sFormDisplayGridPref; //POP setting "FORMDISPLAYGRID"
		public string sMessage; //Variable for passing
		public string sName;
		public string sToolbarControlName;
		public string sToolbarControlLabel;
		public string sToolbarControlWidth;
		public string sToolbarControlHeight;
		public string sToolbarControlImage;
		public string TabLabel;


		//Column widths
		public string sColLblWidth001;
		public string sColWidth001;
		public string sColLblWidth002;
		public string sColWidth002;
		public string sColLblWidth003;
		public string sColWidth003;
		public string sColLblWidth004;
		public string sColWidth004;
		public string sColLblWidth005;
		public string sColWidth005;
		public string sColLblWidth006;
		public string sColWidth006;
		public string sColLblWidth011;
		public string sColWidth011;
		public string sColLblWidth012;
		public string sColWidth012;
		public string sColLblWidth013;
		public string sColWidth013;
		public string sColLblWidth014;
		public string sColWidth014;
		public string sColLblWidth015;
		public string sColWidth015;
		public string sColLblWidth016;
		public string sColWidth016;

		//Table declarations
		public DataTable dt001 = new DataTable();
		public clTable t001 = new clTable();
		public DataTable dt002 = new DataTable();
		public clTable t002 = new clTable();
		public DataTable dt003 = new DataTable();
		public clTable t003 = new clTable();
		public DataTable dt004 = new DataTable();
		public clTable t004 = new clTable();
		public DataTable dt005 = new DataTable();
		public clTable t005 = new clTable();
		public DataTable dt006 = new DataTable();
		public clTable t006 = new clTable();
		public DataTable dt011 = new DataTable();
		public clTable t011 = new clTable();
		public DataTable dt012 = new DataTable();
		public clTable t012 = new clTable();
		public DataTable dt013 = new DataTable();
		public clTable t013 = new clTable();
		public DataTable dt014 = new DataTable();
		public clTable t014 = new clTable();
		public DataTable dt015 = new DataTable();
		public clTable t015 = new clTable();
		public DataTable dt016 = new DataTable();
		public clTable t016 = new clTable();
		public DataTable dtClip = new DataTable();
		public clTable tClip = new clTable();
		public DataTable dtTabs = new DataTable();
		public clTable tTabs = new clTable();
		public DataTable dtToolbar = new DataTable();
		public clTable tToolbar = new clTable();
		public DataTable dtHeader = new DataTable();
		public clTable tHeader = new clTable();

		//Record Selector Properties
		public bool RecordSelectorDisplay = false;
		public string RecordSelectorField1 = "";
		public string RecordSelectorField2 = "";
		public string RecordSelectorField3 = "";
		public string RecordSelectorField4 = "";
		public string RecordSelectorField5 = "";

		public bool RecordSelectorField1Label = false;
		public bool RecordSelectorField2Label = false;
		public bool RecordSelectorField3Label = false;
		public bool RecordSelectorField4Label = false;
		public bool RecordSelectorField5Label = false;



		public void Initialize()
		{
			string sProc = "clDiaFormPro::Initialize";
			//Try
			//   'Enable only if needed
			//   goP = HttpContext.Current.Session("goP")
			//   goTR = HttpContext.Current.Session("goTr")
			//   goMeta = HttpContext.Current.Session("goMeta")
			//   goData = HttpContext.Current.Session("goData")
			//   goErr = HttpContext.Current.Session("goErr")
			//   goLog = HttpContext.Current.Session("goLog")
			//   goUI = HttpContext.Current.Session("goUI")
			//   goHist = HttpContext.Current.Session("goHist")
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
		}

		public cldiaFormPro()
		{

		}

		~cldiaFormPro()
		{
// INSTANT C# NOTE: The base class Finalize method is automatically called from the destructor:
			//base.Finalize();
		}
	}

}
