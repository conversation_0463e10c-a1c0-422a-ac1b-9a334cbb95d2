Imports System.Web

'OWNER: RH

Public Class clAuto
    Dim goP As clProject
    Dim goTR As clTransform
    Dim goMeta As clMetaData
    Dim goData As clData
    Dim goErr As clError
    Dim goLog As clLog
    Dim goDef As clDefaults
    Dim goScr As clScrMngRowSet

    Private Function ACT_DisplayMessage() As Boolean


    End Function

    Private Function ACT_OpenURL() As Boolean


    End Function

    Private Function ACT_RunAutomator(ByVal par_s As String) As Boolean




    End Function

    Private Function ACT_RunScript() As Boolean


    End Function

    Public Function RunActions(ByVal par_sAutomator As String) As Boolean

        Dim sScript As String = goTR.StrRead(par_sAutomator, "A_01_EXECUTE")

        If goScr.RunScript(sScript) = False Then
            Return False
        Else
            Return True
        End If

    End Function

    Public Function TestTimerTrigger(ByVal par_sID As String, ByVal sAutomator As String, ByVal dtNow As DateTime) As Boolean

        'PURPOSE:
        '		Test whether timer automator should be launched now.
        '       If 

        'PARAMETERS:
        '		par_sID: Automator ID.

        'RETURNS:
        '		'true' if automator should be launched.


        Dim dtLast As DateTime
        Dim dtNext As DateTime

        Dim sUser As String = goP.GetUserTID()
        Dim sPTAPage As String = goMeta.PageRead("GLOBAL", "PTA_" & goTR.FromTo(par_sID, 5))

        'create pta if it does not exist
        If sPTAPage = "" Then
            goTR.StrWrite(sPTAPage, "LastDate", clC.SELL_BLANK_DATETIME)
            goTR.StrWrite(sPTAPage, "INPROGRESS", "0")
            goTR.StrWrite(sPTAPage, "Name", "Pointer for timer automator " & goTR.StrRead(sAutomator, "US_NAME"))
            goMeta.PageWrite("", "PTA_" & goTR.FromTo(par_sID, 5), sPTAPage)
        End If

        Dim bInProgress As String = goTR.StrRead(sPTAPage, "INPROGRESS", "0")
        If bInProgress = "1" Then
            'RH Added test of LASTSTARTTIME            '
            Dim dtLastStart As DateTime = goTR.StringToDateTime(goTR.StrRead(sPTAPage, "LASTSTARTTIME"))
            If dtLastStart.AddHours(12) < (Now) Then
                Return True
            Else
                Return False
            End If
        End If

        dtLast = goTR.StringToDateTime(goTR.StrRead(sPTAPage, "LASTDATE"))

        'if lastdate is blank then set as now without running and exit
        If dtLast = clC.SELL_BLANK_DATETIME Then
            goMeta.LineWrite("GLOBAL", "PTA_" & goTR.FromTo(par_sID, 5), "LASTDATE", goTR.DateTimeToString(dtNow))
            Return False
        End If

        'Dim bIfMissed As Boolean = Val(goTR.StrRead(sAutomator, "E_TM_MISSED"))    'Not supported

        Dim sName As String = goTR.StrRead(sAutomator, "NAME")
        Dim lInterval As Long = Val(goTR.StrRead(sAutomator, "E_TM_INTERVAL"))
        Dim iMinute As Integer = Val(goTR.StrRead(sAutomator, "E_TM_MINUTE"))
        Dim iHour As Integer = Right("00" & goTR.StrRead(sAutomator, "E_TM_HOUR"), 2)
        Dim iDay As Integer = Val(goTR.StrRead(sAutomator, "E_TM_DAY"))
        Dim iDate As Integer = Val(goTR.StrRead(sAutomator, "E_TM_DATE"))
        ' Dim bBetween As Boolean = Val(goTR.StrRead(sAutomator, "E_TM_ONLYBETWEEN"))    'Not supported
        Dim lPeriod As Long

        'Strip seconds and milliseconds
        dtLast = dtLast.AddMilliseconds(-dtLast.Millisecond)
        dtLast = dtLast.AddSeconds(-dtLast.Second)
        dtNext = dtNext.AddMilliseconds(-dtNext.Millisecond)
        dtNext = dtNext.AddSeconds(-dtNext.Second)
        dtNow = dtNow.AddMilliseconds(-dtNow.Millisecond)
        dtNow = dtNow.AddSeconds(-dtNow.Second)



        Select Case lInterval

            Case clC.SELL_ALERT_EVERY_NMINUTES  ' ? minutes

                'fire if next date is greater than or equal to now

                If dtLast.AddMinutes(iMinute) <= dtNow Then
                    Return True
                Else
                    Return False
                End If

            Case clC.SELL_ALERT_EVERY_HOUR  ' Hour

                dtNext = dtLast

                'derive next valid date after last date
                dtNext = dtNext.AddMinutes(1)

                Do While dtNext.Minute <> iMinute
                    dtNext = dtNext.AddMinutes(1)
                    lPeriod = lPeriod + 1
                    If lPeriod > 1000000 Then
                        '==> raise error
                    End If
                Loop

                'fire if now date is greater than or equal to next date

                If dtNow >= dtNext Then
                    Return True
                Else
                    Return False
                End If


            Case clC.SELL_ALERT_EVERY_DAY

                dtNext = dtLast

                'derive next valid date after last date
                dtNext = dtNext.AddMinutes(1)

                Do While dtNext.Hour <> iHour Or dtNext.Minute <> iMinute
                    dtNext = dtNext.AddMinutes(1)
                    lPeriod = lPeriod + 1
                    If lPeriod > 1000000 Then
                        '==> raise error
                    End If
                Loop

                'fire if now date is greater than or equal to next date

                If dtNow >= dtNext Then
                    Return True
                Else
                    Return False
                End If


            Case clC.SELL_ALERT_EVERY_WEEK  ' Week

                dtNext = dtLast
                iDay = iDay + 1

                'derive next valid date after last date
                dtNext = dtNext.AddMinutes(1)

                Do While dtNext.Hour <> iHour Or dtNext.Minute <> iMinute Or dtNext.DayOfWeek <> iDay
                    dtNext = dtNext.AddMinutes(1)
                    lPeriod = lPeriod + 1
                    If lPeriod > 1000000 Then
                        '==> raise error
                    End If
                Loop

                'fire if if now date is greater than or equal to next date

                If dtNow >= dtNext Then
                    Return True
                Else
                    Return False
                End If

            Case clC.SELL_ALERT_EVERY_WEEKDAY   ' WeekDay

                dtNext = dtLast

                'derive next valid date after last date
                dtNext = dtNext.AddMinutes(1)

                Do While dtNext.Hour <> iHour Or dtNext.Minute <> iMinute
                    dtNext = dtNext.AddMinutes(1)
                    lPeriod = lPeriod + 1
                    If lPeriod > 1000000 Then
                        '==> raise error
                    End If
                Loop

                'fire if  now date is greater than or equal to next date

                If dtNow >= dtNext Then
                    If dtNow.DayOfWeek <> DayOfWeek.Saturday And dtNow.DayOfWeek <> DayOfWeek.Sunday Then
                        Return True
                    End If
                    Return False
                Else
                    Return False
                End If

            Case clC.SELL_ALERT_EVERY_MONTH ' Month

                dtNext = dtLast

                'derive next valid date after last date
                dtNext = dtNext.AddMinutes(1)

                Do While dtNext.Hour <> iHour Or dtNext.Minute <> iMinute Or dtNext.Day <> iDate
                    dtNext = dtNext.AddMinutes(1)
                    lPeriod = lPeriod + 1
                    If lPeriod > 1000000 Then
                        '==> raise error
                    End If
                Loop

                'fire if now date is greater than or equal to next date

                If dtNow >= dtNext Then
                    Return True
                Else
                    Return False
                End If

        End Select

    End Function


    Sub Initialize()

        goP = HttpContext.Current.Session("goP")
        goTR = HttpContext.Current.Session("goTr")
        goMeta = HttpContext.Current.Session("goMeta")
        goData = HttpContext.Current.Session("goData")
        goErr = HttpContext.Current.Session("goErr")
        goLog = HttpContext.Current.Session("goLog")
        goDef = HttpContext.Current.Session("goDef")
        goScr = HttpContext.Current.Session("goScr")

    End Sub

    Public Function TestAndRunTimers() As String
        'MI 3/4/09 Removed .net warning by moving Return below End Try.

        ' Try

        Dim sSelect As String = "Select DISTINCT" & vbCrLf & _
                                    "GID_Section as 'Section'," & vbCrLf & _
                                    "TXT_Page as 'Page'," & vbCrLf & _
                                    "TXT_Property as 'Property'," & vbCrLf & _
                                    "TXT_Language as 'Language'," & vbCrLf & _
                                    "TXT_Value as 'Value'" & vbCrLf & _
                                    "FROM MD" & vbCrLf & _
                                    "WHERE" & vbCrLf & _
                                    "TXT_Page LIKE 'AGE_%'" & vbCrLf & _
                                    "and TXT_Property = 'Event'" & vbCrLf & _
                                    "and TXT_Value = 'Timer'" & vbCrLf & _
                                    "ORDER BY GID_Section, TXT_Page, TXT_Property, TXT_Language, TXT_Value"

            Dim dtAge As New DataTable
            Dim i As Integer
            Dim sAgePage As String = ""
            Dim sLog As String = ""



            Dim sqlConnection1 As SqlClient.SqlConnection = goData.GetConnection

            Dim cmd As New SqlClient.SqlCommand
            Dim reader As SqlClient.SqlDataReader
            Dim mColl As New Collection
            Dim dtNow As DateTime = goTR.NowServer  '*** MI 10/1/07
            Dim bFired As Boolean

            dtNow = dtNow.AddMilliseconds(-dtNow.Millisecond)
            dtNow = dtNow.AddSeconds(-dtNow.Second)


        '   Try
        cmd.CommandText = sSelect
                cmd.CommandType = CommandType.Text
                cmd.Connection = sqlConnection1

                reader = cmd.ExecuteReader()

                If reader.HasRows Then
                    dtAge.Load(reader)
                Else
                    '==> raise error
                End If

                reader.Close()
                sqlConnection1.Close()

        'Catch ex As Exception

        '    '==> error handle
        'End Try

        If dtAge.Rows.Count > 0 Then


                sLog = "Server time: " & dtNow.ToString & "[cr]"
                For i = 0 To dtAge.Rows.Count - 1
                    sAgePage = goMeta.PageRead("GLOBAL", dtAge.Rows(i).Item("Page").ToString)

                    If goTR.StrRead(sAgePage, "ACTIVE") = "1" Then
                        If TestTimerTrigger(dtAge.Rows(i).Item("Page").ToString, sAgePage, dtNow) = True Then
                            '==> Execute automator
                            goMeta.LineWrite("GLOBAL", "PTA_" & goTR.FromTo(dtAge.Rows(i).Item("Page").ToString, 5), "INPROGRESS", 1)
                            goMeta.LineWrite("GLOBAL", "PTA_" & goTR.FromTo(dtAge.Rows(i).Item("Page").ToString, 5), "LASTSTARTTIME", goTR.DateTimeToString(dtNow))
                            If goScr.RunScript(goTR.StrRead(sAgePage, "A_01_EXECUTE"), , , goTR.StrRead(sAgePage, "PARAMS", "")) = False Then
                                sLog = sLog & "[cr]" & goTR.StrRead(sAgePage, "US_NAME") & "  [False]"
                                goLog.Log("clAuto::TestAndRunTimers", "Timer Automator script returned FALSE: " & goTR.StrRead(sAgePage, "A_01_EXECUTE"), 0, , , , 20)
                            Else
                                goMeta.LineWrite("GLOBAL", "PTA_" & goTR.FromTo(dtAge.Rows(i).Item("Page").ToString, 5), "LASTDATE", goTR.DateTimeToString(dtNow))
                                '==> Compile and write log
                                sLog = sLog & "[cr]" & goTR.StrRead(sAgePage, "US_NAME") & "  [True]"
                            End If
                            'goMeta.LineWrite("GLOBAL", "PTA_" & goTR.FromTo(dtAge.Rows(i).Item("Page").ToString, 5), "LASTSTARTTIME", "")
                            goMeta.LineWrite("GLOBAL", "PTA_" & goTR.FromTo(dtAge.Rows(i).Item("Page").ToString, 5), "INPROGRESS", 0)
                            bFired = True
                        End If
                    End If
                Next
                If bFired = True Then
                    Return sLog
                Else
                    Return "None triggered"
                End If

            End If

        'Catch ex As Exception

        '    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
        '        goErr.SetError(ex, 45105, "clAuto::TestAndRunTimers")
        '    End If

        'End Try

        Return "None triggered"


    End Function




    Public Sub New()
        Initialize()
    End Sub
End Class
