﻿using System;
using Google.GData.Client;

namespace Selltis.BusinessLogic
{
	public class clGoogleAuth
	{

		private string clientId = "316819080531.apps.googleusercontent.com";
		private string clientSecret = "72TLUOczva90D1nagN7p19Bi";
		private string applicationName = "Selltis Link™ for Google®";
		private string redirectUri = "urn:ietf:wg:oauth:2.0:oob";
		// Requesting access to Contacts, Calendat, and Tasks APIs
		private string scopes = "https://www.google.com/m8/feeds/ https://www.googleapis.com/auth/calendar https://www.googleapis.com/auth/tasks";


		public OAuth2Parameters GetParametersFromRefreshToken(string sRefreshToken)
		{

			OAuth2Parameters parameters = new OAuth2Parameters();
			try
			{

				parameters.ClientId = clientId;
				parameters.ClientSecret = clientSecret;
				parameters.RedirectUri = redirectUri;
				parameters.Scope = scopes;
				parameters.ApprovalPrompt = "force";

				parameters.RefreshToken = sRefreshToken;

				OAuthUtil.RefreshAccessToken(parameters);
				return parameters;


			}
			catch (Exception ex)
			{
				//xxx log error
				return parameters;
			}


		}

		public string GetRefreshTokenFromAuthCode(string sAuthCode)
		{


			try
			{

				OAuth2Parameters parameters = new OAuth2Parameters();
				parameters.ClientId = clientId;
				parameters.ClientSecret = clientSecret;
				parameters.RedirectUri = redirectUri;
				parameters.Scope = scopes;
				parameters.ApprovalPrompt = "force";

				parameters.AccessCode = sAuthCode;
				OAuthUtil.GetAccessToken(parameters);

				OAuthUtil.RefreshAccessToken(parameters);
				return parameters.RefreshToken;

			}
			catch (Exception ex)
			{

				//xxx log error
				return "";

			}



		}

		public string GetAuthenticationURL()
		{


			OAuth2Parameters parameters = new OAuth2Parameters();
			parameters.ClientId = clientId;
			parameters.ClientSecret = clientSecret;
			parameters.RedirectUri = redirectUri;
			parameters.Scope = scopes;
			parameters.ApprovalPrompt = "force";

			string url = OAuthUtil.CreateOAuth2AuthorizationUrl(parameters);

			return url;


		}

	}

}
